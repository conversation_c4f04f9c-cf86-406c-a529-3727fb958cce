=pod

=head1 NAME

BIO_printf, BIO_vprintf, BIO_snprintf, BIO_vsnprintf
- formatted output to a BIO

=head1 SYNOPSIS

 #include <openssl/bio.h>

 int BIO_printf(BIO *bio, const char *format, ...);
 int BIO_vprintf(BIO *bio, const char *format, va_list args);

 int BIO_snprintf(char *buf, size_t n, const char *format, ...);
 int BIO_vsnprintf(char *buf, size_t n, const char *format, va_list args);

=head1 DESCRIPTION

BIO_printf() is similar to the standard C printf() function, except that
the output is sent to the specified BIO, I<bio>, rather than standard
output.  All common format specifiers are supported.

BIO_vprintf() is similar to the vprintf() function found on many platforms,
the output is sent to the specified BIO, I<bio>, rather than standard
output.  All common format specifiers are supported. The argument
list I<args> is a stdarg argument list.

BIO_snprintf() is for platforms that do not have the common snprintf()
function. It is like sprintf() except that the size parameter, I<n>,
specifies the size of the output buffer.

BIO_vsnprintf() is to BIO_snprintf() as BIO_vprintf() is to BIO_printf().

=head1 RETURN VALUES

All functions return the number of bytes written, or -1 on error.
For BIO_snprintf() and BIO_vsnprintf() this includes when the output
buffer is too small.

=head1 NOTES

Except when I<n> is 0, both BIO_snprintf() and BIO_vsnprintf() always
terminate their output with C<'\0'>.  This includes cases where -1 is
returned, such as when there is insufficient space to output the whole
string.

=head1 COPYRIGHT

Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
