Here's a brief summary of the syntax of the tcl versions of the
kadm5 functions:

string		Can be a string or "null" which will turn into a null pointer
principal_ent	A 12-field list in the order of the principal_ent
		structure: {string number number number number string
			    number mask number number string mask}
		It can also be "null", like a string, to indicate that
		a null structure pointer should be used.
mask		Either a number, representing the actual value of the
		mask, or a sequence of symbols in a list.  Example:
		{PRINCIPAL ATTRIBUTES} is a valid principal mask.
boolean		"1", "0", "true", "false", etc.
varname		The name of a Tcl variable, or "null" to not assign.
policy_ent	Similar to principal_ent, but with seven fields,
		instead of 12.  The first is a string, and the rest
		are numbers.

init
		client_name:string pass:string service_name:string
		realm:string struct_version:int api_version:int
		server_handle_ret:varname
destroy
		server_handle:string
create_principal
		server_handle:string principal:principal_ent
		mask:principal_mask password:string
delete_principal
		server_handle:string name:string
modify_principal
		server_handle:string principal_principal_ent
		mask:principal_mask
rename_principal
		server_handle:string source:string target:string
chpass_principal
		server_handle:string name:string password:string
chpass_principal_util
		server_handle:string name:string password:string
		pw_ret:varname msg_ret:varname
randkey_principal
		server_handle:string name:string keyblock_var:varname
get_principal [-struct]
		server_handle:string name:string princ_var:varname
create_policy
		server_handle:string policy:policy_ent mask:policy_mask
delete_policy
		server_handle:string name:string
modify_policy
		server_handle:string policy:policy_ent mask:policy_mask
get_policy [-struct]
		server_handle:string name:string policy_var:varname
free_principal_ent
		server_handle:string handle:string
free_policy_ent
		server_handle:string handle:string
get_privs
		server_handle:string privs:priv_var
