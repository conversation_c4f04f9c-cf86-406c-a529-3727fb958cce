=pod

=head1 NAME

SSL_CTX_set_cert_verify_callback - set peer certificate verification procedure

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_CTX_set_cert_verify_callback(SSL_CTX *ctx,
                                       int (*callback)(X509_STORE_CTX *, void *),
                                       void *arg);

=head1 DESCRIPTION

SSL_CTX_set_cert_verify_callback() sets the verification callback function for
I<ctx>. SSL objects that are created from I<ctx> inherit the setting valid at
the time when L<SSL_new(3)> is called.

=head1 NOTES

Whenever a certificate is verified during a SSL/TLS handshake, a verification
function is called. If the application does not explicitly specify a
verification callback function, the built-in verification function is used.
If a verification callback I<callback> is specified via
SSL_CTX_set_cert_verify_callback(), the supplied callback function is called
instead. By setting I<callback> to NULL, the default behaviour is restored.

When the verification must be performed, I<callback> will be called with
the arguments callback(X509_STORE_CTX *x509_store_ctx, void *arg). The
argument I<arg> is specified by the application when setting I<callback>.

I<callback> should return 1 to indicate verification success and 0 to
indicate verification failure. If SSL_VERIFY_PEER is set and I<callback>
returns 0, the handshake will fail. As the verification procedure may
allow the connection to continue in the case of failure (by always
returning 1) the verification result must be set in any case using the
B<error> member of I<x509_store_ctx> so that the calling application
will be informed about the detailed result of the verification procedure!

Within I<x509_store_ctx>, I<callback> has access to the I<verify_callback>
function set using L<SSL_CTX_set_verify(3)>.

=head1 RETURN VALUES

SSL_CTX_set_cert_verify_callback() does not return a value.

=head1 WARNINGS

Do not mix the verification callback described in this function with the
B<verify_callback> function called during the verification process. The
latter is set using the L<SSL_CTX_set_verify(3)>
family of functions.

Providing a complete verification procedure including certificate purpose
settings etc is a complex task. The built-in procedure is quite powerful
and in most cases it should be sufficient to modify its behaviour using
the B<verify_callback> function.

=head1 BUGS

SSL_CTX_set_cert_verify_callback() does not provide diagnostic information.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set_verify(3)>,
L<SSL_get_verify_result(3)>,
L<SSL_CTX_load_verify_locations(3)>

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
