{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, der, PrivateKeyInfo),
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, pem, PrivateKeyInfo),
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, der, EncryptedPrivateKeyInfo),
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, pem, EncryptedPrivateKeyInfo),
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, der, SubjectPublicKeyInfo),
ENCODER_w_structure("{{ variant['name'] }}", {{ variant['name'] }}, pem, SubjectPublicKeyInfo),
     {%- for classical_alg in variant['mix_with'] -%}
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, der, PrivateKeyInfo),
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, pem, PrivateKeyInfo),
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, der, EncryptedPrivateKeyInfo),
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, pem, EncryptedPrivateKeyInfo),
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, der, SubjectPublicKeyInfo),
ENCODER_w_structure("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, pem, SubjectPublicKeyInfo),
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}

