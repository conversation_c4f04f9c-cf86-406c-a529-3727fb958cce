=pod

=head1 NAME

SSL_CTX_set_session_id_context, SSL_set_session_id_context - set context within which session can be reused (server side only)

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_set_session_id_context(SSL_CTX *ctx, const unsigned char *sid_ctx,
                                    unsigned int sid_ctx_len);
 int SSL_set_session_id_context(SSL *ssl, const unsigned char *sid_ctx,
                                unsigned int sid_ctx_len);

=head1 DESCRIPTION

SSL_CTX_set_session_id_context() sets the context B<sid_ctx> of length
B<sid_ctx_len> within which a session can be reused for the B<ctx> object.

SSL_set_session_id_context() sets the context B<sid_ctx> of length
B<sid_ctx_len> within which a session can be reused for the B<ssl> object.

=head1 NOTES

Sessions are generated within a certain context. When exporting/importing
sessions with B<i2d_SSL_SESSION>/B<d2i_SSL_SESSION> it would be possible,
to re-import a session generated from another context (e.g. another
application), which might lead to malfunctions. Therefore, each application
must set its own session id context B<sid_ctx> which is used to distinguish
the contexts and is stored in exported sessions. The B<sid_ctx> can be
any kind of binary data with a given length, it is therefore possible
to use e.g. the name of the application and/or the hostname and/or service
name ...

The session id context becomes part of the session. The session id context
is set by the SSL/TLS server. The SSL_CTX_set_session_id_context() and
SSL_set_session_id_context() functions are therefore only useful on the
server side.

OpenSSL clients will check the session id context returned by the server
when reusing a session.

The maximum length of the B<sid_ctx> is limited to
B<SSL_MAX_SID_CTX_LENGTH>.

=head1 WARNINGS

If the session id context is not set on an SSL/TLS server and client
certificates are used, stored sessions
will not be reused but a fatal error will be flagged and the handshake
will fail.

If a server returns a different session id context to an OpenSSL client
when reusing a session, an error will be flagged and the handshake will
fail. OpenSSL servers will always return the correct session id context,
as an OpenSSL server checks the session id context itself before reusing
a session as described above.

=head1 RETURN VALUES

SSL_CTX_set_session_id_context() and SSL_set_session_id_context()
return the following values:

=over 4

=item Z<>0

The length B<sid_ctx_len> of the session id context B<sid_ctx> exceeded
the maximum allowed length of B<SSL_MAX_SID_CTX_LENGTH>. The error
is logged to the error stack.

=item Z<>1

The operation succeeded.

=back

=head1 SEE ALSO

L<ssl(7)>

=head1 COPYRIGHT

Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
