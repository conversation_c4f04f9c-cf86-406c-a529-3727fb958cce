# AES 128 OCB vectors from RFC 7253
COUNT = 0
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221100
AAD =
Plaintext =
Ciphertext = 785407BFFFC8AD9EDCC5520AC9111EE6

COUNT = 1
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221101
AAD = 0001020304050607
Plaintext = 0001020304050607
Ciphertext = 6820B3657B6F615A5725BDA0D3B4EB3A257C9AF1F8F03009

COUNT = 2
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221102
AAD = 0001020304050607
Plaintext =
Ciphertext = 81017F8203F081277152FADE694A0A00

COUNT = 3
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221103
AAD =
Plaintext = 0001020304050607
Ciphertext = 45DD69F8F5AAE72414054CD1F35D82760B2CD00D2F99BFA9

COUNT = 4
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221104
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 571D535B60B277188BE5147170A9A22C3AD7A4FF3835B8C5701C1CCEC8FC3358

COUNT = 5
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221105
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext =
Ciphertext = 8CF761B6902EF764462AD86498CA6B97

COUNT = 6
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221106
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 5CE88EC2E0692706A915C00AEB8B2396F40E1C743F52436BDF06D8FA1ECA343D

COUNT = 7
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221107
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = 1CA2207308C87C010756104D8840CE1952F09673A448A122C92C62241051F57356D7F3C90BB0E07F

COUNT = 8
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221108
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext =
Ciphertext = 6DC225A071FC1B9F7C69F93B0F1E10DE

COUNT = 9
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA99887766554433221109
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = 221BD0DE7FA6FE993ECCD769460A0AF2D6CDED0C395B1C3CE725F32494B9F914D85C0B1EB38357FF

COUNT = 10
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110A
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = BD6F6C496201C69296C11EFD138A467ABD3C707924B964DEAFFC40319AF5A48540FBBA186C5553C68AD9F592A79A4240

COUNT = 11
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext =
Ciphertext = FE80690BEE8A485D11F32965BC9D2A32

COUNT = 12
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110C
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 2942BFC773BDA23CABC6ACFD9BFD5835BD300F0973792EF46040C53F1432BCDFB5E1DDE3BC18A5F840B52E653444D5DF

COUNT = 13
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110D
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = D5CA91748410C1751FF8A2F618255B68A0A12E093FF454606E59F9C1D0DDC54B65E8628E568BAD7AED07BA06A4A69483A7035490C5769E60

COUNT = 14
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110E
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext =
Ciphertext = C5CD9D1850C141E358649994EE701B68

COUNT = 15
Key = 000102030405060708090A0B0C0D0E0F
Nonce = BBAA9988776655443322110F
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 4412923493C57D5DE0D700F753CCE0D1D2D95060122E9F15A5DDBFC5787E50B5CC55EE507BCB084E479AD363AC366B95A98CA5F3000B1479
