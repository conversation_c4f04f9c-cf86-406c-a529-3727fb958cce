quic.dll
========================

WinDbg debugger extension for the MsQuic library.

Publishing Instructions
------------------------

- Increment the version number in GalleryManifest.xml.
- Create a folder with name equal to the version number.
- Copy GalleryManifest.xml into the folder.
- To the version folder, add subfolders for x86 and amd64.
- Copy the corresponding versions of quic.dll in each of those subfolders.
- Zip it all up.
- Share it out.
- Send <NAME_EMAIL> with the path to the shared folder.
