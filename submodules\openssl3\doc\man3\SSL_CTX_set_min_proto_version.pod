=pod

=head1 NAME

SSL_CTX_set_min_proto_version, SSL_CTX_set_max_proto_version,
SSL_CTX_get_min_proto_version, SSL_CTX_get_max_proto_version,
SSL_set_min_proto_version, SSL_set_max_proto_version,
SSL_get_min_proto_version, SSL_get_max_proto_version - Get and set minimum
and maximum supported protocol version

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_set_min_proto_version(SSL_CTX *ctx, int version);
 int SSL_CTX_set_max_proto_version(SSL_CTX *ctx, int version);
 int SSL_CTX_get_min_proto_version(SSL_CTX *ctx);
 int SSL_CTX_get_max_proto_version(SSL_CTX *ctx);

 int SSL_set_min_proto_version(SSL *ssl, int version);
 int SSL_set_max_proto_version(SSL *ssl, int version);
 int SSL_get_min_proto_version(SSL *ssl);
 int SSL_get_max_proto_version(SSL *ssl);

=head1 DESCRIPTION

The functions get or set the minimum and maximum supported protocol versions
for the B<ctx> or B<ssl>.
This works in combination with the options set via
L<SSL_CTX_set_options(3)> that also make it possible to disable
specific protocol versions.
Use these functions instead of disabling specific protocol versions.

Setting the minimum or maximum version to 0, will enable protocol
versions down to the lowest version, or up to the highest version
supported by the library, respectively.

Getters return 0 in case B<ctx> or B<ssl> have been configured to
automatically use the lowest or highest version supported by the library.

Currently supported versions are B<SSL3_VERSION>, B<TLS1_VERSION>,
B<TLS1_1_VERSION>, B<TLS1_2_VERSION>, B<TLS1_3_VERSION> for TLS and
B<DTLS1_VERSION>, B<DTLS1_2_VERSION> for DTLS.

=head1 RETURN VALUES

These setter functions return 1 on success and 0 on failure. The getter
functions return the configured version or 0 for auto-configuration of
lowest or highest protocol, respectively.

=head1 NOTES

All these functions are implemented using macros.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_options(3)>, L<SSL_CONF_cmd(3)>

=head1 HISTORY

The setter functions were added in OpenSSL 1.1.0. The getter functions
were added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2016-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
