{"type": "object", "definitions": {"HkdfTestGroup": {"type": "object", "properties": {"type": {"enum": ["HkdfTest"]}, "keySize": {"type": "integer", "description": "the size of the ikm in bits"}, "tests": {"type": "array", "items": {"$ref": "#/definitions/HkdfTestVector"}}}}, "HkdfTestVector": {"type": "object", "properties": {"tcId": {"type": "integer", "description": "Identifier of the test case"}, "comment": {"type": "string", "description": "A brief description of the test case"}, "ikm": {"type": "string", "format": "HexBytes", "description": "the key (input key material)"}, "salt": {"type": "string", "format": "HexBytes", "description": "the salt for the key derivation"}, "info": {"type": "string", "format": "HexBytes", "description": "additional information used in the key derivation"}, "size": {"type": "integer", "description": "the size of the output in bytes"}, "okm": {"type": "string", "format": "HexBytes", "description": "the generated bytes (output key material)"}, "result": {"type": "string", "description": "Test result", "enum": ["valid", "invalid", "acceptable"]}, "flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags"}}}}, "properties": {"algorithm": {"type": "string", "description": "the primitive tested in the test file"}, "generatorVersion": {"type": "string", "description": "the version of the test vectors."}, "header": {"type": "array", "items": {"type": "string"}, "description": "additional documentation"}, "notes": {"type": "object", "description": "a description of the labels used in the test vectors"}, "numberOfTests": {"type": "integer", "description": "the number of test vectors in this test"}, "schema": {"enum": ["hkdf_test_schema.json"]}, "testGroups": {"type": "array", "items": {"$ref": "#/definitions/HkdfTestGroup"}}}}