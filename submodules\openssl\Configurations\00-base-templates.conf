# -*- Mode: perl -*-
my %targets=(
    DEFAULTS => {
	template	=> 1,

	cflags		=> "",
	cppflags	=> "",
	lflags		=> "",
	defines		=> [],
	includes	=> [],
	lib_cflags	=> "",
	lib_cppflags	=> "",
	lib_defines	=> [],
	thread_scheme	=> "(unknown)", # Assume we don't know
	thread_defines	=> [],

	apps_aux_src	=> "",
	apps_init_src	=> "",
	cpuid_asm_src	=> "mem_clr.c",
	uplink_aux_src	=> "",
	bn_asm_src	=> "bn_asm.c",
	ec_asm_src	=> "",
	des_asm_src	=> "des_enc.c fcrypt_b.c",
	aes_asm_src	=> "aes_core.c aes_cbc.c",
	bf_asm_src	=> "bf_enc.c",
	md5_asm_src	=> "",
	cast_asm_src	=> "c_enc.c",
	rc4_asm_src	=> "rc4_enc.c rc4_skey.c",
	rmd160_asm_src	=> "",
	rc5_asm_src	=> "rc5_enc.c",
	wp_asm_src	=> "wp_block.c",
	cmll_asm_src	=> "camellia.c cmll_misc.c cmll_cbc.c",
	modes_asm_src	=> "",
	padlock_asm_src	=> "",
	chacha_asm_src	=> "chacha_enc.c",
	poly1305_asm_src	=> "",
	keccak1600_asm_src	=> "keccak1600.c",

	unistd		=> "<unistd.h>",
	shared_target	=> "",
	shared_cflag	=> "",
	shared_defines	=> [],
	shared_ldflag	=> "",
	shared_rcflag	=> "",
	shared_extension	=> "",

	#### Defaults for the benefit of the config targets who don't inherit
	#### a BASE and assume Unix defaults
	#### THESE WILL DISAPPEAR IN OpenSSL 1.2
	build_scheme	=> [ "unified", "unix" ],
	build_file	=> "Makefile",

	AR		=> "ar",
	ARFLAGS		=> "r",
	CC		=> "cc",
	HASHBANGPERL	=> "/usr/bin/env perl",
	RANLIB		=> sub { which("$config{cross_compile_prefix}ranlib")
                                     ? "ranlib" : "" },
	RC		=> "windres",

	#### THESE WILL BE ENABLED IN OpenSSL 1.2
	#HASHBANGPERL	=> "PERL", # Only Unix actually cares
    },

    BASE_common => {
	template	=> 1,

	enable		=> [],
	disable		=> [],

	defines		=>
	    sub {
                my @defs = ();
                push @defs, "ZLIB" unless $disabled{zlib};
                push @defs, "ZLIB_SHARED" unless $disabled{"zlib-dynamic"};
                return [ @defs ];
            },
        includes        =>
            sub {
                my @incs = ();
                push @incs, $withargs{zlib_include}
                    if !$disabled{zlib} && $withargs{zlib_include};
                return [ @incs ];
            },
    },

    BASE_unix => {
        inherit_from    => [ "BASE_common" ],
        template        => 1,

        AR              => "ar",
        ARFLAGS         => "r",
        CC              => "cc",
        lflags          =>
            sub { $withargs{zlib_lib} ? "-L".$withargs{zlib_lib} : () },
        ex_libs         =>
            sub { !defined($disabled{zlib})
                  && defined($disabled{"zlib-dynamic"})
                  ? "-lz" : () },
        HASHBANGPERL    => "/usr/bin/env perl", # Only Unix actually cares
        RANLIB          => sub { which("$config{cross_compile_prefix}ranlib")
                                     ? "ranlib" : "" },
        RC              => "windres",

        shared_extension => ".so",

        build_scheme    => [ "unified", "unix" ],
        build_file      => "Makefile",
    },

    BASE_Windows => {
        inherit_from    => [ "BASE_common" ],
        template        => 1,

        lib_defines      =>
            sub {
                my @defs = ();
                unless ($disabled{"zlib-dynamic"}) {
                    my $zlib = $withargs{zlib_lib} // "ZLIB1";
                    push @defs, 'LIBZ=' . (quotify("perl", $zlib))[0];
                }
                return [ @defs ];
            },
        ex_libs         =>
            sub {
                unless ($disabled{zlib}) {
                    if (defined($disabled{"zlib-dynamic"})) {
                        return $withargs{zlib_lib} // "ZLIB1";
                    }
                }
                return ();
            },

        LD              => "link",
        LDFLAGS         => "/nologo",
        ldoutflag       => "/out:",
        AR              => "lib",
        ARFLAGS         => "/nologo",
        aroutflag       => "/out:",
        RC               => "rc",
        rcoutflag        => "/fo",
        MT              => "mt",
        MTFLAGS         => "-nologo",
        mtinflag        => "-manifest ",
        mtoutflag       => "-outputresource:",

        shared_extension => ".dll",

        build_file      => "makefile",
        build_scheme    => [ "unified", "windows" ],
    },

    BASE_VMS => {
        inherit_from    => [ "BASE_common" ],
        template        => 1,

        includes        =>
            add(sub {
                    my @incs = ();
                    # GNV$ZLIB_INCLUDE is the standard logical name for later
                    # zlib incarnations.
                    push @incs, 'GNV$ZLIB_INCLUDE:'
                        if !$disabled{zlib} && !$withargs{zlib_include};
                    return [ @incs ];
                }),

        shared_extension => ".exe",

        build_file       => "descrip.mms",
        build_scheme     => [ "unified", "VMS" ],
    },

    uplink_common => {
	template	=> 1,
	apps_init_src	=> add("../ms/applink.c"),
	uplink_aux_src	=> add("../ms/uplink.c"),
	defines		=> add("OPENSSL_USE_APPLINK"),
    },
    x86_uplink => {
	inherit_from	=> [ "uplink_common" ],
	template	=> 1,
	uplink_aux_src	=> add("uplink-x86.s"),
    },
    x86_64_uplink => {
	inherit_from	=> [ "uplink_common" ],
	template	=> 1,
	uplink_aux_src	=> add("uplink-x86_64.s"),
    },
    ia64_uplink => {
	inherit_from	=> [ "uplink_common" ],
	template	=> 1,
	uplink_aux_src	=> add("uplink-ia64.s"),
    },

    x86_asm => {
	template	=> 1,
	cpuid_asm_src	=> "x86cpuid.s",
	bn_asm_src	=> "bn-586.s co-586.s x86-mont.s x86-gf2m.s",
	ec_asm_src	=> "ecp_nistz256.c ecp_nistz256-x86.s",
	des_asm_src	=> "des-586.s crypt586.s",
	aes_asm_src	=> "aes_core.c aes_cbc.c vpaes-x86.s aesni-x86.s",
	bf_asm_src	=> "bf-586.s",
	md5_asm_src	=> "md5-586.s",
	cast_asm_src	=> "cast-586.s",
	sha1_asm_src	=> "sha1-586.s sha256-586.s sha512-586.s",
	rc4_asm_src	=> "rc4-586.s",
	rmd160_asm_src	=> "rmd-586.s",
	rc5_asm_src	=> "rc5-586.s",
	wp_asm_src	=> "wp_block.c wp-mmx.s",
	cmll_asm_src	=> "cmll-x86.s",
	modes_asm_src	=> "ghash-x86.s",
	padlock_asm_src	=> "e_padlock-x86.s",
	chacha_asm_src	=> "chacha-x86.s",
	poly1305_asm_src=> "poly1305-x86.s",
    },
    x86_elf_asm => {
	template	=> 1,
	inherit_from	=> [ "x86_asm" ],
	perlasm_scheme	=> "elf"
    },
    x86_64_asm => {
	template	=> 1,
	cpuid_asm_src   => "x86_64cpuid.s",
	bn_asm_src      => "asm/x86_64-gcc.c x86_64-mont.s x86_64-mont5.s x86_64-gf2m.s rsaz_exp.c rsaz-x86_64.s rsaz-avx2.s",
	ec_asm_src      => "ecp_nistz256.c ecp_nistz256-x86_64.s x25519-x86_64.s",
	aes_asm_src     => "aes_core.c aes_cbc.c vpaes-x86_64.s aesni-x86_64.s aesni-sha1-x86_64.s aesni-sha256-x86_64.s aesni-mb-x86_64.s",
	md5_asm_src     => "md5-x86_64.s",
	sha1_asm_src    => "sha1-x86_64.s sha256-x86_64.s sha512-x86_64.s sha1-mb-x86_64.s sha256-mb-x86_64.s",
	rc4_asm_src     => "rc4-x86_64.s rc4-md5-x86_64.s",
	wp_asm_src      => "wp-x86_64.s",
	cmll_asm_src    => "cmll-x86_64.s cmll_misc.c",
	modes_asm_src   => "ghash-x86_64.s aesni-gcm-x86_64.s",
	padlock_asm_src => "e_padlock-x86_64.s",
	chacha_asm_src	=> "chacha-x86_64.s",
	poly1305_asm_src=> "poly1305-x86_64.s",
	keccak1600_asm_src	=> "keccak1600-x86_64.s",
    },
    ia64_asm => {
	template	=> 1,
	cpuid_asm_src   => "ia64cpuid.s",
	bn_asm_src      => "bn-ia64.s ia64-mont.s",
	aes_asm_src     => "aes_core.c aes_cbc.c aes-ia64.s",
	sha1_asm_src    => "sha1-ia64.s sha256-ia64.s sha512-ia64.s",
	modes_asm_src   => "ghash-ia64.s",
	perlasm_scheme	=> "void"
    },
    sparcv9_asm => {
	template	=> 1,
	cpuid_asm_src   => "sparcv9cap.c sparccpuid.S",
	bn_asm_src      => "asm/sparcv8plus.S sparcv9-mont.S sparcv9a-mont.S vis3-mont.S sparct4-mont.S sparcv9-gf2m.S",
	ec_asm_src      => "ecp_nistz256.c ecp_nistz256-sparcv9.S",
	des_asm_src     => "des_enc-sparc.S fcrypt_b.c dest4-sparcv9.S",
	aes_asm_src     => "aes_core.c aes_cbc.c aes-sparcv9.S aest4-sparcv9.S aesfx-sparcv9.S",
	md5_asm_src     => "md5-sparcv9.S",
	sha1_asm_src    => "sha1-sparcv9.S sha256-sparcv9.S sha512-sparcv9.S",
	cmll_asm_src    => "camellia.c cmll_misc.c cmll_cbc.c cmllt4-sparcv9.S",
	modes_asm_src   => "ghash-sparcv9.S",
	poly1305_asm_src=> "poly1305-sparcv9.S",
	perlasm_scheme	=> "void"
    },
    sparcv8_asm => {
	template	=> 1,
	cpuid_asm_src   => "",
	bn_asm_src      => "asm/sparcv8.S",
	des_asm_src     => "des_enc-sparc.S fcrypt_b.c",
	perlasm_scheme	=> "void"
    },
    alpha_asm => {
	template	=> 1,
	cpuid_asm_src   => "alphacpuid.s",
	bn_asm_src      => "bn_asm.c alpha-mont.S",
	sha1_asm_src    => "sha1-alpha.S",
	modes_asm_src   => "ghash-alpha.S",
	perlasm_scheme	=> "void"
    },
    mips32_asm => {
	template	=> 1,
	bn_asm_src      => "bn-mips.S mips-mont.S",
	aes_asm_src     => "aes_cbc.c aes-mips.S",
	sha1_asm_src    => "sha1-mips.S sha256-mips.S",
    },
    mips64_asm => {
	inherit_from	=> [ "mips32_asm" ],
	template	=> 1,
	sha1_asm_src    => add("sha512-mips.S"),
	poly1305_asm_src=> "poly1305-mips.S",
    },
    s390x_asm => {
	template	=> 1,
	cpuid_asm_src   => "s390xcap.c s390xcpuid.S",
	bn_asm_src      => "asm/s390x.S s390x-mont.S s390x-gf2m.s",
	aes_asm_src     => "aes-s390x.S aes-ctr.fake aes-xts.fake",
	sha1_asm_src    => "sha1-s390x.S sha256-s390x.S sha512-s390x.S",
	rc4_asm_src     => "rc4-s390x.s",
	modes_asm_src   => "ghash-s390x.S",
	chacha_asm_src  => "chacha-s390x.S",
	poly1305_asm_src=> "poly1305-s390x.S",
	keccak1600_asm_src	=> "keccak1600-s390x.S",
    },
    armv4_asm => {
	template	=> 1,
	cpuid_asm_src   => "armcap.c armv4cpuid.S",
	bn_asm_src      => "bn_asm.c armv4-mont.S armv4-gf2m.S",
	ec_asm_src      => "ecp_nistz256.c ecp_nistz256-armv4.S",
	aes_asm_src     => "aes_cbc.c aes-armv4.S bsaes-armv7.S aesv8-armx.S",
	sha1_asm_src    => "sha1-armv4-large.S sha256-armv4.S sha512-armv4.S",
	modes_asm_src   => "ghash-armv4.S ghashv8-armx.S",
	chacha_asm_src  => "chacha-armv4.S",
	poly1305_asm_src=> "poly1305-armv4.S",
	keccak1600_asm_src	=> "keccak1600-armv4.S",
	perlasm_scheme	=> "void"
    },
    aarch64_asm => {
	template	=> 1,
	cpuid_asm_src   => "armcap.c arm64cpuid.S",
	ec_asm_src      => "ecp_nistz256.c ecp_nistz256-armv8.S",
	bn_asm_src      => "bn_asm.c armv8-mont.S",
	aes_asm_src     => "aes_core.c aes_cbc.c aesv8-armx.S vpaes-armv8.S",
	sha1_asm_src    => "sha1-armv8.S sha256-armv8.S sha512-armv8.S",
	modes_asm_src   => "ghashv8-armx.S",
	chacha_asm_src  => "chacha-armv8.S",
	poly1305_asm_src=> "poly1305-armv8.S",
	keccak1600_asm_src	=> "keccak1600-armv8.S",
    },
    parisc11_asm => {
	template	=> 1,
	cpuid_asm_src   => "pariscid.s",
	bn_asm_src      => "bn_asm.c parisc-mont.s",
	aes_asm_src     => "aes_core.c aes_cbc.c aes-parisc.s",
	sha1_asm_src    => "sha1-parisc.s sha256-parisc.s sha512-parisc.s",
	rc4_asm_src     => "rc4-parisc.s",
	modes_asm_src   => "ghash-parisc.s",
	perlasm_scheme	=> "32"
    },
    parisc20_64_asm => {
	template	=> 1,
	inherit_from	=> [ "parisc11_asm" ],
	perlasm_scheme	=> "64",
    },
    ppc32_asm => {
	template	=> 1,
	cpuid_asm_src   => "ppccpuid.s ppccap.c",
	bn_asm_src      => "bn-ppc.s ppc-mont.s",
	aes_asm_src     => "aes_core.c aes_cbc.c aes-ppc.s vpaes-ppc.s aesp8-ppc.s",
	sha1_asm_src    => "sha1-ppc.s sha256-ppc.s sha512-ppc.s sha256p8-ppc.s sha512p8-ppc.s",
	modes_asm_src   => "ghashp8-ppc.s",
	chacha_asm_src	=> "chacha-ppc.s",
	poly1305_asm_src=> "poly1305-ppc.s poly1305-ppcfp.s",
    },
    ppc64_asm => {
	inherit_from	=> [ "ppc32_asm" ],
	template	=> 1,
	ec_asm_src	=> "ecp_nistz256.c ecp_nistz256-ppc64.s x25519-ppc64.s",
	keccak1600_asm_src	=> "keccak1600-ppc64.s",
    },
);
