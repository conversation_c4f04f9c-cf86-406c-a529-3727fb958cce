=pod

=head1 NAME

DSA_generate_key - generate DSA key pair

=head1 SYNOPSIS

 #include <openssl/dsa.h>

 int DSA_generate_key(DSA *a);

=head1 DESCRIPTION

DSA_generate_key() expects B<a> to contain DSA parameters. It generates
a new key pair and stores it in B<a-E<gt>pub_key> and B<a-E<gt>priv_key>.

The random generator must be seeded prior to calling DSA_generate_key().
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see L<RAND(7)>), the operation will fail.

=head1 RETURN VALUES

DSA_generate_key() returns 1 on success, 0 otherwise.
The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<DSA_new(3)>, L<ERR_get_error(3)>, L<RAND_bytes(3)>,
L<DSA_generate_parameters_ex(3)>

=head1 COPYRIGHT

Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
