.. _rcache_definition:

replay cache
============

A replay cache (or "rcache") keeps track of all authenticators
recently presented to a service.  If a duplicate authentication
request is detected in the replay cache, an error message is sent to
the application program.

The replay cache interface, like the credential cache and
:ref:`keytab_definition` interfaces, uses `type:value` strings to
indicate the type of replay cache and any associated cache naming
data to use.

Background information
----------------------

Some Kerberos or GSSAPI services use a simple authentication mechanism
where a message is sent containing an authenticator, which establishes
the encryption key that the client will use for talking to the
service.  But nothing about that prevents an eavesdropper from
recording the messages sent by the client, establishing a new
connection, and re-sending or "replaying" the same messages; the
replayed authenticator will establish the same encryption key for the
new session, and the following messages will be decrypted and
processed.  The attacker may not know what the messages say, and can't
generate new messages under the same encryption key, but in some
instances it may be harmful to the user (or helpful to the attacker)
to cause the server to see the same messages again a second time.  For
example, if the legitimate client sends "delete first message in
mailbox", a replay from an attacker may delete another, different
"first" message.  (Protocol design to guard against such problems has
been discussed in :rfc:`4120#section-10`.)

Even if one protocol uses further protection to verify that the client
side of the connection actually knows the encryption keys (and thus is
presumably a legitimate user), if another service uses the same
service principal name, it may be possible to record an authenticator
used with the first protocol and "replay" it against the second.

The replay cache mitigates these attacks somewhat, by keeping track of
authenticators that have been seen until their five-minute window
expires.  Different authenticators generated by multiple connections
from the same legitimate client will generally have different
timestamps, and thus will not be considered the same.

This mechanism isn't perfect.  If a message is sent to one application
server but a man-in-the-middle attacker can prevent it from actually
arriving at that server, the attacker could then use the authenticator
(once!) against a different service on the same host.  This could be a
problem if the message from the client included something more than
authentication in the first message that could be useful to the
attacker (which is uncommon; in most protocols the server has to
indicate a successful authentication before the client sends
additional messages), or if the simple act of presenting the
authenticator triggers some interesting action in the service being
attacked.

Default rcache type
-------------------

There is currently only one implemented kind of replay cache, called
**dfl**.  It stores replay data in one file, occasionally rewriting it
to purge old, expired entries.

The default type can be overridden by the **KRB5RCACHETYPE**
environment variable.

The placement of the replay cache file is determined by the following:

#. The **KRB5RCACHEDIR** environment variable;

#. If KRB5RCACHEDIR is unspecified, on UNIX, the library
   will fall back to the environment variable **TMPDIR**, and then to
   a temporary directory determined at configuration time such as
   */tmp* or */var/tmp*; on Windows, it will check the environment
   variables *TEMP* and *TMP*, and fall back to the directory C:\\.

Performance issues
------------------

Several known minor performance issues that may occur when replay
cache is enabled on the Kerberos system include: delays due to writing
the authenticator data to disk slowing down response time for very
heavily loaded servers, and delays during the rewrite that may be
unacceptable to high-performance services.

For use cases where replays are adequately defended against for all
protocols using a given service principal name, or where performance
or other considerations outweigh the risk of replays, the special
replay cache type "none" can be specified::

    KRB5RCACHETYPE=none

It doesn't record any information about authenticators, and reports
that any authenticator seen is not a replay.
