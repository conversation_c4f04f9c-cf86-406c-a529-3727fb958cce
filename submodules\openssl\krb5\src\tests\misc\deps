#
# Generated makefile dependencies follow.
#
$(OUTPRE)test_getpw.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  test_getpw.c
$(OUTPRE)test_chpw_message.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h \
  test_chpw_message.c
$(OUTPRE)test_getsockname.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  test_getsockname.c
$(OUTPRE)test_cxx_krb5.$(OBJEXT): $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/locate_plugin.h test_cxx_krb5.cpp
$(OUTPRE)test_cxx_k5int.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-ipc_stream.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/k5-utf8.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h test_cxx_k5int.cpp
$(OUTPRE)test_cxx_gss.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  test_cxx_gss.cpp
$(OUTPRE)test_cxx_rpc.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssrpc/types.h $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h test_cxx_rpc.cpp
$(OUTPRE)test_cxx_kadm5.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssrpc/types.h $(BUILDTOP)/include/kadm5/admin.h \
  $(BUILDTOP)/include/kadm5/chpass_util_strings.h $(BUILDTOP)/include/kadm5/kadm_err.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/kdb.h \
  $(top_srcdir)/include/krb5.h test_cxx_kadm5.cpp
