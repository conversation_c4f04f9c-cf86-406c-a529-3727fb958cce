#!/bin/bash
echo "Building P2PSocket library with integrated MsQuic..."
mkdir -p build_p2psocket_android
cd build_p2psocket_android
export ANDROID_NDK_ROOT=/home/<USER>/android-ndk-r23c
ANDROID_TOOLCHAIN_FILE="$ANDROID_NDK_ROOT/build/cmake/android.toolchain.cmake"

# Build MsQuic as a static library to integrate into p2psocket.so
cmake .. -G"Unix Makefiles" -DCMAKE_TOOLCHAIN_FILE="$ANDROID_TOOLCHAIN_FILE" -DANDROID_ABI=arm64-v8a -DANDROID_PLATFORM=android-29 -DANDROID_NDK="$ANDROID_NDK_ROOT" -DQUIC_BUILD_PLATFORM=android -DQUIC_BUILD_SHARED=OFF -DQUIC_ENABLE_LOGGING=OFF -DQUIC_TLS=openssl3 -DANDROID=ON
cmake --build . --config Release
echo ""
echo "Build completed."
echo ""
echo "The integrated P2PSocket library can be found in:"
echo "$(pwd)/bin"
cd ..
