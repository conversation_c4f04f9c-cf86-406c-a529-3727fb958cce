---
name: Question
labels: 'issue: question'
about: Ask a question about OpenSSL
---

<!--
Thank you for your interest in OpenSSL. If this is your first question,
please take the time to read the following lines before posting it.

For general questions about *using* OpenSSL:

    If you have questions about how to use OpenSSL for specific tasks
    or how to solve certain problems you have when using it, you might
    want to ask them <NAME_EMAIL> mailing list.
    There you can get help from a great community of OpenSSL users,
    not only (but including) the OpenSSL developers. For more information
    about our mailing lists, see
    https://www.openssl.org/community/mailinglists.html.

For questions related to build issues:

    Please use the 'Bug report' template.

For other questions:

    Please describe your problem as concisely as possible while giving
    us enough information to understand your problem. Example code
    or example commands are highly appreciated if they help us to
    better understand what you are trying to achieve.

    Also, please remember to tell us which OpenSSL version you are
    using and whether it is system provided or you built it yourself.
    In the latter case, please also send us your build configuration.
    With OpenSSL before 1.1.1, the configuration output comes from the
    configuration command.  With OpenSSL 1.1.1 and on, you can obtain
    the information by running the command `perl configdata.pm --dump`
    in the root directory of the source tree.

Please remember to put ``` lines before and after any commands plus
output and code, like this:

    ```
    $ echo output output output
    output output output
    ```

    ```
    #include <stdio.h>

    int main() {
        int foo = 1;
        printf("%d\n", foo);
    }
    ```
-->
