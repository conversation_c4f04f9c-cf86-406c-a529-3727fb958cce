{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_PrivateKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_PrivateKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_EncryptedPrivateKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_EncryptedPrivateKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_SubjectPublicKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_to_SubjectPublicKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_PrivateKeyInfo_der_to_{{ variant['name'] }}_decoder_functions[];
extern const OSSL_DISPATCH oqs_SubjectPublicKeyInfo_der_to_{{ variant['name'] }}_decoder_functions[];
     {%- for classical_alg in variant['mix_with'] -%}
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_PrivateKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_PrivateKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_EncryptedPrivateKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_EncryptedPrivateKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_SubjectPublicKeyInfo_der_encoder_functions[];
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_to_SubjectPublicKeyInfo_pem_encoder_functions[];
extern const OSSL_DISPATCH oqs_PrivateKeyInfo_der_to_{{ classical_alg['name'] }}_{{ variant['name'] }}_decoder_functions[];
extern const OSSL_DISPATCH oqs_SubjectPublicKeyInfo_der_to_{{ classical_alg['name'] }}_{{ variant['name'] }}_decoder_functions[];
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}

