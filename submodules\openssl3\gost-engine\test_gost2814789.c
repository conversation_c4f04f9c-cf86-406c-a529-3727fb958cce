/* vim: set fileencoding=ascii : Charset: ASCII */
/* test/gostr2814789t.c */
/* ====================================================================
 * Copyright (c) 2012 Crypto-Pro, Ltd., Serguei E. Leontiev,
 *                    <EMAIL>
 *
 * This file is distributed under the same license as OpenSSL
 * ====================================================================
 */
#ifdef _MSC_VER
# pragma warning(push, 3)
# include <openssl/applink.c>
# pragma warning(pop)
#endif
#include <stdio.h>

#include <stdlib.h>
#include <string.h>
#include <openssl/conf.h>
#include <openssl/crypto.h>
#include <openssl/engine.h>
#include <openssl/evp.h>
#include <openssl/hmac.h>
#include <openssl/obj_mac.h>

#define CCGOST_ID "gost"
#define CCGOST_DIR "."

#include "gost89.h"

#define G89_MAX_TC_LEN	(2048)
#define G89_BLOCK_LEN (8)

typedef enum g89_mode_ {
    G89_ECB,
    G89_CFB,
    G89_CNT,
    G89_IMIT
}   g89_mode;

typedef struct g89_tc_ {
    unsigned long long ullLen;		// ullLen > G89_MAX_TC_LEN
    					//     Clear text ullLen
					//     of zero bytes
    const byte bIn[G89_MAX_TC_LEN];	// Clear text, when
					//     ullLen <= G89_MAX_TC_LEN
    const char *szParamSet;		// S-Box ID
    const char *szDerive;		// String for derive bRawKey
    const byte bRawKey[EVP_MAX_KEY_LENGTH];
    g89_mode gMode;			// Mode of encryption or MAC
    const byte bIV[EVP_MAX_IV_LENGTH];	// IV for CFB or CNT mode
    const byte bOut[G89_MAX_TC_LEN];	// Cipher text for ECB/CFB/CNT
    					// mode, when ullLen <= G89_MAX_TC_LEN;
					// Last 16 byte of cipher text for
					// ECB/CFB/CNT, when ullLen >
					// G89_MAX_TC_LEN;
					// 4 byte MAC for imitovstavka
}   g89_tc;

const g89_tc tcs[] = {
  /*
   * GOST R 34.11-94 Test cases
   */
    { /* see p. A.3.1 [GOSTR341194], p. 7.3.1 [ENG-GOSTR341194] */
      /* <http://tools.ietf.org/html/rfc5831#section-7.3.1> */
      /* Iteration 1, K[1], see Errata for RFC 5831 */
	8,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	"id-GostR3410-94-TestParamSet",
	NULL,
	{
	    0x54, 0x6d, 0x20, 0x33, 0x68, 0x65, 0x6c, 0x32,
	    0x69, 0x73, 0x65, 0x20, 0x73, 0x73, 0x6e, 0x62,
	    0x20, 0x61, 0x67, 0x79, 0x69, 0x67, 0x74, 0x74,
	    0x73, 0x65, 0x68, 0x65, 0x20, 0x2c, 0x3d, 0x73
	},
	G89_ECB,
	{ 0 },
	{
	    0x1b, 0x0b, 0xbc, 0x32, 0xce, 0xbc, 0xab, 0x42
	}
    },
    { /* see p. A.3.1 [GOSTR341194], p. 7.3.1 [ENG-GOSTR341194] */
      /* <http://tools.ietf.org/html/rfc5831#section-7.3.1> */
      /* Iteration 1, K[4] */
	8,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	"id-GostR3410-94-TestParamSet",
	NULL,
	{
	    0xec, 0x0a, 0x8b, 0xa1, 0x5e, 0xc0, 0x04, 0xa8,
	    0xba, 0xc5, 0x0c, 0xac, 0x0c, 0x62, 0x1d, 0xee,
	    0xe1, 0xc7, 0xb8, 0xe7, 0x00, 0x7a, 0xe2, 0xec,
	    0xf2, 0x73, 0x1b, 0xff, 0x4e, 0x80, 0xe2, 0xa0
	},
	G89_ECB,
	{ 0 },
	{
	    0x2d, 0x56, 0x2a, 0x0d, 0x19, 0x04, 0x86, 0xe7
	}
    },
    { /* see p. A.3.1 [GOSTR341194], p. 7.3.1 [ENG-GOSTR341194] */
      /* <http://tools.ietf.org/html/rfc5831#section-7.3.1> */
      /* Iteration 2, K[1] */
	8,
	{
	    0x34, 0xc0, 0x15, 0x33, 0xe3, 0x7d, 0x1c, 0x56
	},
	"id-GostR3410-94-TestParamSet",
	NULL,
	{
	    0x34, 0x87, 0x24, 0xa4, 0xc1, 0xa6, 0x76, 0x67,
	    0x15, 0x3d, 0xde, 0x59, 0x33, 0x88, 0x42, 0x50,
	    0xe3, 0x24, 0x8c, 0x65, 0x7d, 0x41, 0x3b, 0x8c,
	    0x1c, 0x9c, 0xa0, 0x9a, 0x56, 0xd9, 0x68, 0xcf
	},
	G89_ECB,
	{ 0 },
	{
	    0x86, 0x3e, 0x78, 0xdd, 0x2d, 0x60, 0xd1, 0x3c
	}
    },
  /*
   * id-Gost28147-89-CryptoPro-A-ParamSet (1.2.643.2.2.31.1)
   * Test cases
   */
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	16,
	{
	    0x74, 0x3d, 0x76, 0xf9, 0x1b, 0xee, 0x35, 0x3c,
	    0xa2, 0x5c, 0x3b, 0x10, 0xeb, 0x64, 0xcf, 0xf5
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testecb1",
	{
	    0xbb, 0xf1, 0xed, 0xd3, 0x20, 0xaf, 0x8a, 0x62,
	    0x8e, 0x11, 0xc8, 0xa9, 0x51, 0xcc, 0xbe, 0x81,
	    0x47, 0x7b, 0x41, 0xa1, 0x6a, 0xf6, 0x7f, 0x05,
	    0xe8, 0x51, 0x2f, 0x9e, 0x01, 0xf8, 0xcf, 0x49
	},
	G89_ECB,
	{ 0 },
	{
	    0xc3, 0x73, 0x90, 0x95, 0x35, 0x58, 0x08, 0x63,
	    0xcb, 0x68, 0x85, 0x96, 0x77, 0xe8, 0xfb, 0xa9
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	16,
	{
	    0xd2, 0xfd, 0xf8, 0x3a, 0xc1, 0xb4, 0x39, 0x23,
	    0x2e, 0xaa, 0xcc, 0x98, 0x0a, 0x02, 0xda, 0x33
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testcfb1",
	{
	    0x8d, 0x5a, 0x2c, 0x83, 0xa7, 0xc7, 0x0a, 0x61,
	    0xd6, 0x1b, 0x34, 0xb5, 0x1f, 0xdf, 0x42, 0x68,
	    0x66, 0x71, 0xa3, 0x5d, 0x87, 0x4c, 0xfd, 0x84,
	    0x99, 0x36, 0x63, 0xb6, 0x1e, 0xd6, 0x0d, 0xad
	},
	G89_CFB,
	{
	    0x46, 0x60, 0x6f, 0x0d, 0x88, 0x34, 0x23, 0x5a
	},
	{
	    0x88, 0xb7, 0x75, 0x16, 0x74, 0xa5, 0xee, 0x2d,
	    0x14, 0xfe, 0x91, 0x67, 0xd0, 0x5c, 0xcc, 0x40
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	16,
	{
	    0x90, 0xa2, 0x39, 0x66, 0xae, 0x01, 0xb9, 0xa3,
	    0x52, 0x4e, 0xc8, 0xed, 0x6c, 0xdd, 0x88, 0x30
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testcnt1",
	{
	    0x59, 0x9f, 0x84, 0xba, 0xc3, 0xf3, 0xd2, 0xf1,
	    0x60, 0xe1, 0xe3, 0xf2, 0x6a, 0x96, 0x1a, 0xf9,
	    0x9c, 0x48, 0xb2, 0x4e, 0xbc, 0xbb, 0xbf, 0x7c,
	    0xd8, 0xf3, 0xac, 0xcd, 0x96, 0x8d, 0x28, 0x6a
	},
	G89_CNT,
	{
	    0x8d, 0xaf, 0xa8, 0xd1, 0x58, 0xed, 0x05, 0x8d
	},
	{
	    0x6e, 0x72, 0x62, 0xcc, 0xe3, 0x59, 0x36, 0x90,
	    0x83, 0x3a, 0xfe, 0xa9, 0x1b, 0xc9, 0xbe, 0xce
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	16,
	{
	    0xb5, 0xa1, 0xf0, 0xe3, 0xce, 0x2f, 0x02, 0x1d,
	    0x67, 0x61, 0x94, 0x34, 0x5c, 0x41, 0xe3, 0x6e
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit1",
	{
	    0x9d, 0x05, 0xb7, 0x9e, 0x90, 0xca, 0xd0, 0x0a,
	    0x2c, 0xda, 0xd2, 0x2e, 0xf4, 0xe8, 0x6f, 0x5c,
	    0xf5, 0xdc, 0x37, 0x68, 0x19, 0x85, 0xb3, 0xbf,
	    0xaa, 0x18, 0xc1, 0xc3, 0x05, 0x0a, 0x91, 0xa2
	},
	G89_IMIT,
	{ 0 },
	{
	    0xf8, 0x1f, 0x08, 0xa3
	}
    },
  /*
   * Other paramsets and key meshing test cases.
   */
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	1039,
	{
	    0x8c, 0x9c, 0x44, 0x35, 0xfb, 0xe9, 0xa5, 0xa3,
	    0xa0, 0xae, 0x28, 0x56, 0x91, 0x10, 0x8e, 0x1e ,
	    0xd2, 0xbb, 0x18, 0x53, 0x81, 0x27, 0x0d, 0xa6,
	    0x68, 0x59, 0x36, 0xc5, 0x81, 0x62, 0x9a, 0x8e,
	    0x7d, 0x50, 0xf1, 0x6f, 0x97, 0x62, 0x29, 0xec,
	    0x80, 0x51, 0xe3, 0x7d, 0x6c, 0xc4, 0x07, 0x95,
	    0x28, 0x63, 0xdc, 0xb4, 0xb9, 0x2d, 0xb8, 0x13,
	    0xb1, 0x05, 0xb5, 0xf9, 0xeb, 0x75, 0x37, 0x4e,
	    0xf7, 0xbf, 0x51, 0xf1, 0x98, 0x86, 0x43, 0xc4,
	    0xe4, 0x3d, 0x3e, 0xa7, 0x62, 0xec, 0x41, 0x59,
	    0xe0, 0xbd, 0xfb, 0xb6, 0xfd, 0xec, 0xe0, 0x77,
	    0x13, 0xd2, 0x59, 0x90, 0xa1, 0xb8, 0x97, 0x6b,
	    0x3d, 0x8b, 0x7d, 0xfc, 0x9d, 0xca, 0x82, 0x73,
	    0x32, 0x70, 0x0a, 0x74, 0x03, 0xc6, 0x0c, 0x26,
	    0x7f, 0x56, 0xf0, 0x9d, 0xb2, 0xeb, 0x71, 0x40,
	    0xd7, 0xc3, 0xb1, 0xa7, 0xc5, 0x1e, 0x20, 0x17,
	    0xb3, 0x50, 0x1d, 0x8a, 0x6e, 0x19, 0xcb, 0xbe,
	    0x20, 0x86, 0x2b, 0xd6, 0x1c, 0xfd, 0xb4, 0xb7,
	    0x5d, 0x9a, 0xb3, 0xe3, 0x7d, 0x15, 0x7a, 0x35,
	    0x01, 0x9f, 0x5d, 0x65, 0x89, 0x4b, 0x34, 0xc6,
	    0xf4, 0x81, 0x3f, 0x78, 0x30, 0xcf, 0xe9, 0x15,
	    0x90, 0x9a, 0xf9, 0xde, 0xba, 0x63, 0xd0, 0x19,
	    0x14, 0x66, 0x3c, 0xb9, 0xa4, 0xb2, 0x84, 0x94,
	    0x02, 0xcf, 0xce, 0x20, 0xcf, 0x76, 0xe7, 0xc5,
	    0x48, 0xf7, 0x69, 0x3a, 0x5d, 0xec, 0xaf, 0x41,
	    0xa7, 0x12, 0x64, 0x83, 0xf5, 0x99, 0x1e, 0x9e,
	    0xb2, 0xab, 0x86, 0x16, 0x00, 0x23, 0x8e, 0xe6,
	    0xd9, 0x80, 0x0b, 0x6d, 0xc5, 0x93, 0xe2, 0x5c,
	    0x8c, 0xd8, 0x5e, 0x5a, 0xae, 0x4a, 0x85, 0xfd,
	    0x76, 0x01, 0xea, 0x30, 0xf3, 0x78, 0x34, 0x10,
	    0x72, 0x51, 0xbc, 0x9f, 0x76, 0xce, 0x1f, 0xd4,
	    0x8f, 0x33, 0x50, 0x34, 0xc7, 0x4d, 0x7b, 0xcf,
	    0x91, 0x63, 0x7d, 0x82, 0x9e, 0xa1, 0x23, 0x45,
	    0xf5, 0x45, 0xac, 0x98, 0x7a, 0x48, 0xff, 0x64,
	    0xd5, 0x59, 0x47, 0xde, 0x2b, 0x3f, 0xfa, 0xec,
	    0x50, 0xe0, 0x81, 0x60, 0x8b, 0xc3, 0xfc, 0x80,
	    0x98, 0x17, 0xc7, 0xa3, 0xc2, 0x57, 0x3d, 0xab,
	    0x91, 0x67, 0xf5, 0xc4, 0xab, 0x92, 0xc8, 0xd6,
	    0x3b, 0x6b, 0x3f, 0xff, 0x15, 0x6b, 0xcf, 0x53,
	    0x65, 0x02, 0xf1, 0x74, 0xca, 0xa9, 0xbe, 0x24,
	    0xd2, 0xf0, 0xb7, 0x26, 0xa8, 0xd7, 0x6d, 0xed,
	    0x90, 0x36, 0x7b, 0x3e, 0x41, 0xa9, 0x7f, 0xa3,
	    0x1b, 0xf4, 0x43, 0xc5, 0x51, 0xbe, 0x28, 0x59,
	    0xe9, 0x45, 0x26, 0x49, 0x38, 0x32, 0xf8, 0xf3,
	    0x92, 0x6e, 0x30, 0xcc, 0xb0, 0xa0, 0xf9, 0x01,
	    0x14, 0xc8, 0xba, 0xd9, 0xf0, 0x2a, 0x29, 0xe2,
	    0x52, 0x9a, 0x76, 0x95, 0x3a, 0x16, 0x32, 0xec,
	    0xf4, 0x10, 0xec, 0xee, 0x47, 0x00, 0x70, 0x19,
	    0xe4, 0x72, 0x35, 0x66, 0x44, 0x53, 0x2d, 0xa2,
	    0xf3, 0xaa, 0x7e, 0x8a, 0x33, 0x13, 0xcd, 0xc8,
	    0xbf, 0x0e, 0x40, 0x90, 0x00, 0xe4, 0x42, 0xc3,
	    0x09, 0x84, 0xe1, 0x66, 0x17, 0xa2, 0xaf, 0x03,
	    0xab, 0x6b, 0xa1, 0xec, 0xfb, 0x17, 0x72, 0x81,
	    0xfe, 0x9a, 0x9f, 0xf4, 0xb2, 0x33, 0x1f, 0xae,
	    0x0c, 0xd1, 0x6a, 0xae, 0x19, 0xb8, 0xaf, 0xec,
	    0xe3, 0xea, 0x00, 0xf8, 0xac, 0x87, 0x07, 0x5f,
	    0x6d, 0xb0, 0xac, 0x6b, 0x22, 0x48, 0x36, 0xbf,
	    0x22, 0x18, 0xb0, 0x03, 0x9f, 0x6c, 0x70, 0x45,
	    0x36, 0xf0, 0x6b, 0xc6, 0xc2, 0xa5, 0x72, 0x2c,
	    0xd8, 0xe0, 0x27, 0x3d, 0xec, 0x56, 0x07, 0x05,
	    0x7d, 0x83, 0xa1, 0x65, 0x7d, 0x41, 0x5b, 0xcd,
	    0x77, 0x24, 0xe5, 0xaa, 0x76, 0x47, 0xd0, 0x50,
	    0xf6, 0xe7, 0xb5, 0x59, 0x75, 0x31, 0x27, 0xef,
	    0xd8, 0xa6, 0x4e, 0x7f, 0xb8, 0x40, 0xb1, 0xdf,
	    0x53, 0x14, 0xed, 0xf1, 0x68, 0x5f, 0xfc, 0x3f,
	    0x02, 0xdb, 0x05, 0xeb, 0x31, 0xe4, 0x2c, 0x7f,
	    0x32, 0xb5, 0x70, 0x8e, 0x75, 0x85, 0xa4, 0x5c,
	    0x16, 0x23, 0x37, 0xf2, 0x10, 0x79, 0xcb, 0xdc,
	    0xf8, 0x1c, 0x25, 0xc2, 0xa1, 0x3d, 0x9c, 0x33,
	    0x6c, 0xed, 0xc3, 0xe7, 0xf3, 0x02, 0x87, 0x82,
	    0x4e, 0xfb, 0xac, 0xb3, 0x2d, 0xfc, 0xf8, 0x0d,
	    0x1d, 0x4a, 0x39, 0xd4, 0xb3, 0x09, 0xbb, 0xe9,
	    0x25, 0xc7, 0xec, 0x6a, 0x87, 0x72, 0x84, 0xed,
	    0x12, 0x60, 0x19, 0x64, 0xeb, 0x16, 0x2a, 0x5b,
	    0x10, 0x76, 0x27, 0xff, 0x7b, 0xe4, 0xae, 0xe5,
	    0xa4, 0x04, 0x02, 0x7f, 0xbb, 0x0a, 0xb5, 0xf4,
	    0x05, 0xa5, 0x56, 0x1c, 0x53, 0x31, 0x7a, 0x93,
	    0xba, 0x16, 0x15, 0xab, 0x62, 0x60, 0xfc, 0xde,
	    0x72, 0x36, 0x6e, 0x28, 0xaf, 0x98, 0x0d, 0xe6,
	    0xf4, 0xde, 0x60, 0xa7, 0x7e, 0x06, 0x07, 0x86,
	    0xf3, 0x94, 0xb6, 0x6d, 0x0d, 0x93, 0xa6, 0xbc,
	    0x60, 0x70, 0x33, 0xac, 0x3f, 0xa1, 0xa8, 0x4a,
	    0x20, 0x61, 0xb6, 0xb5, 0x43, 0xa3, 0x15, 0x5a,
	    0x00, 0xbe, 0x76, 0x98, 0x57, 0x72, 0xab, 0x7a,
	    0x0e, 0x18, 0x93, 0x82, 0x3a, 0x18, 0x78, 0x6e,
	    0x71, 0x7b, 0x78, 0x4f, 0x7e, 0x8c, 0xde, 0x7a,
	    0x62, 0xb5, 0x0a, 0x7c, 0x45, 0x1d, 0x16, 0xd5,
	    0xc3, 0x8c, 0x9b, 0x25, 0xb4, 0x50, 0x90, 0xcd,
	    0x96, 0x93, 0xad, 0x0f, 0xd4, 0x43, 0xcb, 0x49,
	    0x0f, 0xfc, 0x5a, 0x31, 0xf4, 0x19, 0xb7, 0xd4,
	    0xeb, 0x4d, 0x40, 0x58, 0xd0, 0x3b, 0xc8, 0xe0,
	    0x4a, 0x54, 0x2f, 0xdb, 0x22, 0xc3, 0x29, 0x7b,
	    0x40, 0x90, 0x61, 0x43, 0xd3, 0x7e, 0xe2, 0x30,
	    0x2b, 0x48, 0x3c, 0xce, 0x90, 0x93, 0xb1, 0x8b,
	    0x31, 0x96, 0x65, 0x6d, 0x57, 0x8b, 0x9d, 0x4d,
	    0x53, 0xf0, 0x83, 0x1c, 0xe5, 0xa1, 0x9d, 0x55,
	    0xe3, 0xbf, 0x7e, 0xca, 0x1a, 0x74, 0x66, 0x14,
	    0xcc, 0x47, 0x43, 0xd9, 0xbb, 0xef, 0x97, 0x7d,
	    0xb7, 0x6e, 0xff, 0xf1, 0x22, 0xf8, 0x10, 0x2d,
	    0x3f, 0xcd, 0x49, 0x96, 0xd9, 0x09, 0x11, 0xb8,
	    0x33, 0xd0, 0x23, 0x9a, 0xfa, 0x16, 0xcb, 0x50,
	    0x26, 0x57, 0x24, 0x5c, 0x0e, 0xba, 0xf0, 0x3f,
	    0x37, 0x2f, 0xa3, 0xf7, 0x18, 0x57, 0x48, 0x48,
	    0x95, 0xcf, 0xef, 0x87, 0x67, 0x2a, 0xe9, 0xb6,
	    0x8a, 0x21, 0x36, 0x7f, 0xff, 0x48, 0x6c, 0x46,
	    0x35, 0x57, 0xf2, 0xbc, 0x48, 0x67, 0x8f, 0x63,
	    0x23, 0x78, 0x11, 0x2b, 0xc2, 0x08, 0xde, 0x51,
	    0xe8, 0x8b, 0x92, 0x29, 0xf9, 0x9a, 0x9e, 0xad,
	    0xed, 0x0f, 0xeb, 0xa2, 0xd2, 0x40, 0x92, 0xd4,
	    0xde, 0x62, 0x95, 0x76, 0xfd, 0x6e, 0x3c, 0xbf,
	    0xc0, 0xd7, 0x0d, 0xe5, 0x1b, 0xa4, 0xc7, 0x18,
	    0xe1, 0x58, 0xa4, 0x56, 0xef, 0x2e, 0x17, 0x1b,
	    0x75, 0xcb, 0xbc, 0xf9, 0x2a, 0x95, 0x71, 0xa7,
	    0x1d, 0x7f, 0xe7, 0x73, 0x63, 0x05, 0x6b, 0x19,
	    0x4c, 0xf4, 0x22, 0x14, 0xc4, 0x59, 0x88, 0x66,
	    0x92, 0x86, 0x61, 0x5c, 0x6a, 0xae, 0xec, 0x58,
	    0xff, 0xc9, 0xf2, 0x44, 0xd4, 0xa2, 0xf5, 0x98,
	    0xeb, 0x5f, 0x09, 0xbc, 0x8a, 0xbf, 0x3c, 0xb4,
	    0x3e, 0xb1, 0x20, 0x05, 0x44, 0x96, 0x79, 0x0a,
	    0x40, 0x92, 0x7f, 0x9d, 0xd1, 0xaf, 0xbc, 0x90,
	    0x95, 0x0a, 0x81, 0xd4, 0xa7, 0xc6, 0xb8, 0xe0,
	    0xe4, 0x39, 0x30, 0x1d, 0x79, 0xc0, 0xe5, 0xfa,
	    0xb4, 0xe9, 0x63, 0xb4, 0x09, 0x72, 0x3b, 0x3e,
	    0xd9, 0xf6, 0xd9, 0x10, 0x21, 0x18, 0x7e, 0xe5,
	    0xad, 0x81, 0xd7, 0xd5, 0x82, 0xd0, 0x8c, 0x3b,
	    0x38, 0x95, 0xf8, 0x92, 0x01, 0xa9, 0x92, 0x00,
	    0x70, 0xd1, 0xa7, 0x88, 0x77, 0x1f, 0x3a, 0xeb,
	    0xb5, 0xe4, 0xf5, 0x9d, 0xc7, 0x37, 0x86, 0xb2,
	    0x12, 0x46, 0x34, 0x19, 0x72, 0x8c, 0xf5, 0x8c,
	    0xf6, 0x78, 0x98, 0xe0, 0x7c, 0xd3, 0xf4
	},
	"id-Gost28147-89-CryptoPro-B-ParamSet",
	"testcfb2",
	{
	    0x48, 0x0c, 0x74, 0x1b, 0x02, 0x6b, 0x55, 0xd5,
	    0xb6, 0x6d, 0xd7, 0x1d, 0x40, 0x48, 0x05, 0x6b,
	    0x6d, 0xeb, 0x3c, 0x29, 0x0f, 0x84, 0x80, 0x23,
	    0xee, 0x0d, 0x47, 0x77, 0xe3, 0xfe, 0x61, 0xc9
	},
	G89_CFB,
	{
	    0x1f, 0x3f, 0x82, 0x1e, 0x0d, 0xd8, 0x1e, 0x22
	},
	{
	    0x23, 0xc6, 0x7f, 0x20, 0xa1, 0x23, 0x58, 0xbc,
	    0x7b, 0x05, 0xdb, 0x21, 0x15, 0xcf, 0x96, 0x41,
	    0xc7, 0x88, 0xef, 0x76, 0x5c, 0x49, 0xdb, 0x42,
	    0xbf, 0xf3, 0xc0, 0xf5, 0xbd, 0x5d, 0xd9, 0x8e,
	    0xaf, 0x3d, 0xf4, 0xe4, 0xda, 0x88, 0xbd, 0xbc,
	    0x47, 0x5d, 0x76, 0x07, 0xc9, 0x5f, 0x54, 0x1d,
	    0x1d, 0x6a, 0xa1, 0x2e, 0x18, 0xd6, 0x60, 0x84,
	    0x02, 0x18, 0x37, 0x92, 0x92, 0x15, 0xab, 0x21,
	    0xee, 0x21, 0xcc, 0x71, 0x6e, 0x51, 0xd9, 0x2b,
	    0xcc, 0x81, 0x97, 0x3f, 0xeb, 0x45, 0x99, 0xb8,
	    0x1b, 0xda, 0xff, 0x90, 0xd3, 0x41, 0x06, 0x9c,
	    0x3f, 0xfb, 0xe4, 0xb2, 0xdc, 0xc9, 0x03, 0x0d,
	    0xa7, 0xae, 0xd7, 0x7d, 0x02, 0xb8, 0x32, 0xab,
	    0xf3, 0x65, 0xa3, 0x65, 0x6c, 0x4e, 0xe4, 0xa2,
	    0x5e, 0x9e, 0xee, 0xcd, 0xde, 0x79, 0x36, 0x6b,
	    0x1b, 0xe1, 0x3c, 0xdf, 0x10, 0xad, 0x4f, 0x02,
	    0xe1, 0x14, 0xaa, 0x09, 0xb4, 0x0b, 0x76, 0xeb,
	    0x69, 0x38, 0x20, 0x02, 0xcb, 0x8e, 0xc0, 0xdf,
	    0xca, 0x48, 0x74, 0xc3, 0x31, 0xad, 0x42, 0x2c,
	    0x51, 0x9b, 0xd0, 0x6a, 0xc1, 0x36, 0xd7, 0x21,
	    0xdf, 0xb0, 0x45, 0xba, 0xca, 0x7f, 0x35, 0x20,
	    0x28, 0xbb, 0xc1, 0x76, 0xfd, 0x43, 0x5d, 0x23,
	    0x7d, 0x31, 0x84, 0x1a, 0x97, 0x4d, 0x83, 0xaa,
	    0x7e, 0xf1, 0xc4, 0xe6, 0x83, 0xac, 0x0d, 0xef,
	    0xef, 0x3c, 0xa4, 0x7c, 0x48, 0xe4, 0xc8, 0xca,
	    0x0d, 0x7d, 0xea, 0x7c, 0x45, 0xd7, 0x73, 0x50,
	    0x25, 0x1d, 0x01, 0xc4, 0x02, 0x1a, 0xcd, 0xe0,
	    0x38, 0x5b, 0xa8, 0x5a, 0x16, 0x9a, 0x10, 0x59,
	    0x74, 0xd7, 0x19, 0xc6, 0xf3, 0xb5, 0x17, 0xf6,
	    0x59, 0x8d, 0x62, 0xaf, 0x44, 0xe8, 0xdc, 0xe9,
	    0xc1, 0x76, 0xf1, 0xd0, 0xbd, 0x29, 0xd7, 0xec,
	    0x1d, 0xac, 0x57, 0xdb, 0x1a, 0x3f, 0xd8, 0xf6,
	    0x6e, 0xb6, 0xe6, 0xdf, 0x36, 0xe7, 0x89, 0xce,
	    0x56, 0x35, 0x43, 0x1c, 0x7d, 0x57, 0x79, 0x0e,
	    0xd8, 0xf4, 0xd7, 0xa7, 0x0d, 0xc6, 0x8f, 0x91,
	    0x66, 0x67, 0x82, 0x0f, 0x49, 0xc9, 0xc5, 0x65,
	    0x81, 0xa1, 0x39, 0x5a, 0x53, 0x9f, 0x02, 0xa5,
	    0xd5, 0x36, 0x22, 0xa8, 0xa8, 0x1c, 0x37, 0x0e,
	    0x76, 0x46, 0xdf, 0xbd, 0x6a, 0xdb, 0xfc, 0x1b,
	    0xbd, 0x10, 0xb8, 0xb1, 0xbc, 0x72, 0x4c, 0x58,
	    0x4a, 0xda, 0x6d, 0x66, 0x00, 0xda, 0x7a, 0x66,
	    0xa0, 0xe7, 0x3b, 0x39, 0xa3, 0xf7, 0x05, 0x07,
	    0xfa, 0x21, 0x4b, 0xc7, 0x94, 0xc0, 0xd3, 0x7b,
	    0x19, 0x02, 0x5d, 0x4a, 0x10, 0xf1, 0xc2, 0x0f,
	    0x19, 0x68, 0x27, 0xc7, 0x7d, 0xbf, 0x55, 0x03,
	    0x57, 0x7d, 0xaf, 0x77, 0xae, 0x80, 0x2f, 0x7a,
	    0xe6, 0x1f, 0x4b, 0xdc, 0x15, 0x18, 0xc0, 0x62,
	    0xa1, 0xe8, 0xd9, 0x1c, 0x9e, 0x8c, 0x96, 0x39,
	    0xc1, 0xc4, 0x88, 0xf7, 0x0c, 0xe1, 0x04, 0x84,
	    0x68, 0x51, 0xce, 0xf1, 0x90, 0xda, 0x7f, 0x76,
	    0xc8, 0xc0, 0x88, 0xef, 0x8e, 0x15, 0x25, 0x3e,
	    0x7b, 0xe4, 0x79, 0xb5, 0x66, 0x2d, 0x9c, 0xd1,
	    0x13, 0xda, 0xd0, 0xd5, 0x46, 0xd5, 0x8d, 0x46,
	    0x18, 0x07, 0xee, 0xd8, 0xc9, 0x64, 0xe3, 0xbe,
	    0x0e, 0x68, 0x27, 0x09, 0x96, 0x26, 0xf6, 0xe2,
	    0x19, 0x61, 0x3f, 0xf4, 0x58, 0x27, 0x0a, 0xeb,
	    0xce, 0x7c, 0xb6, 0x68, 0x92, 0xe7, 0x12, 0x3b,
	    0x31, 0xd4, 0x48, 0xdf, 0x35, 0x8d, 0xf4, 0x86,
	    0x42, 0x2a, 0x15, 0x4b, 0xe8, 0x19, 0x1f, 0x26,
	    0x65, 0x9b, 0xa8, 0xda, 0x4b, 0x79, 0x1f, 0x8e,
	    0xe6, 0x13, 0x7e, 0x49, 0x8f, 0xc1, 0xce, 0xdc,
	    0x5e, 0x64, 0x74, 0xce, 0x02, 0x78, 0xe0, 0xcf,
	    0xa0, 0xed, 0x5e, 0x31, 0x74, 0xd1, 0xd0, 0xb4,
	    0xee, 0x70, 0x19, 0x14, 0x3c, 0x8f, 0x16, 0xa6,
	    0xcf, 0x12, 0x93, 0x15, 0x88, 0xeb, 0x91, 0x65,
	    0x76, 0x98, 0xfd, 0xa1, 0x94, 0x30, 0xba, 0x43,
	    0x62, 0x65, 0x40, 0x04, 0x77, 0x9e, 0xd6, 0xab,
	    0x8b, 0x0d, 0x93, 0x80, 0x50, 0x5f, 0xa2, 0x76,
	    0x20, 0xa7, 0xd6, 0x9c, 0x27, 0x15, 0x27, 0xbc,
	    0xa5, 0x5a, 0xbf, 0xe9, 0x92, 0x82, 0x05, 0xa8,
	    0x41, 0xe9, 0xb5, 0x60, 0xd5, 0xc0, 0xd7, 0x4b,
	    0xad, 0x38, 0xb2, 0xe9, 0xd1, 0xe5, 0x51, 0x5f,
	    0x24, 0x78, 0x24, 0x9a, 0x23, 0xd2, 0xc2, 0x48,
	    0xbd, 0x0e, 0xf1, 0x37, 0x72, 0x91, 0x87, 0xb0,
	    0x4e, 0xbd, 0x99, 0x6b, 0x2c, 0x01, 0xb6, 0x79,
	    0x69, 0xec, 0x0c, 0xed, 0xe5, 0x3f, 0x50, 0x64,
	    0x7c, 0xb9, 0xdd, 0xe1, 0x92, 0x81, 0xb5, 0xd0,
	    0xcb, 0x17, 0x83, 0x86, 0x8b, 0xea, 0x4f, 0x93,
	    0x08, 0xbc, 0x22, 0x0c, 0xef, 0xe8, 0x0d, 0xf5,
	    0x9e, 0x23, 0xe1, 0xf9, 0xb7, 0x6b, 0x45, 0x0b,
	    0xcb, 0xa9, 0xb6, 0x4d, 0x28, 0x25, 0xba, 0x3e,
	    0x86, 0xf2, 0x75, 0x47, 0x5d, 0x9d, 0x6b, 0xf6,
	    0x8a, 0x05, 0x58, 0x73, 0x3d, 0x00, 0xde, 0xfd,
	    0x69, 0xb1, 0x61, 0x16, 0xf5, 0x2e, 0xb0, 0x9f,
	    0x31, 0x6a, 0x00, 0xb9, 0xef, 0x71, 0x63, 0x47,
	    0xa3, 0xca, 0xe0, 0x40, 0xa8, 0x7e, 0x02, 0x04,
	    0xfe, 0xe5, 0xce, 0x48, 0x73, 0xe3, 0x94, 0xcf,
	    0xe2, 0xff, 0x29, 0x7e, 0xf6, 0x32, 0xbb, 0xb7,
	    0x55, 0x12, 0x21, 0x7a, 0x9c, 0x75, 0x04, 0x0c,
	    0xb4, 0x7c, 0xb0, 0x3d, 0x40, 0xb3, 0x11, 0x9a,
	    0x7a, 0x9a, 0x13, 0xfb, 0x77, 0xa7, 0x51, 0x68,
	    0xf7, 0x05, 0x47, 0x3b, 0x0f, 0x52, 0x5c, 0xe6,
	    0xc2, 0x99, 0x3a, 0x37, 0x54, 0x5c, 0x4f, 0x2b,
	    0xa7, 0x01, 0x08, 0x74, 0xbc, 0x91, 0xe3, 0xe2,
	    0xfe, 0x65, 0x94, 0xfd, 0x3d, 0x18, 0xe0, 0xf0,
	    0x62, 0xed, 0xc2, 0x10, 0x82, 0x9c, 0x58, 0x7f,
	    0xb2, 0xa3, 0x87, 0x8a, 0x74, 0xd9, 0xc1, 0xfb,
	    0x84, 0x28, 0x17, 0xc7, 0x2b, 0xcb, 0x53, 0x1f,
	    0x4e, 0x8a, 0x82, 0xfc, 0xb4, 0x3f, 0xc1, 0x47,
	    0x25, 0xf3, 0x21, 0xdc, 0x4c, 0x2d, 0x08, 0xfa,
	    0xe7, 0x0f, 0x03, 0xa9, 0x68, 0xde, 0x6b, 0x41,
	    0xa0, 0xf9, 0x41, 0x6c, 0x57, 0x4d, 0x3a, 0x0e,
	    0xea, 0x51, 0xca, 0x9f, 0x97, 0x11, 0x7d, 0xf6,
	    0x8e, 0x88, 0x63, 0x67, 0xc9, 0x65, 0x13, 0xca,
	    0x38, 0xed, 0x35, 0xbe, 0xf4, 0x27, 0xa9, 0xfc,
	    0xa9, 0xe6, 0xc3, 0x40, 0x86, 0x08, 0x39, 0x72,
	    0x37, 0xee, 0xb2, 0x87, 0x09, 0x96, 0xb7, 0x40,
	    0x87, 0x36, 0x92, 0xc1, 0x5d, 0x6a, 0x2c, 0x43,
	    0xca, 0x25, 0xc8, 0x35, 0x37, 0x2d, 0xb5, 0xa9,
	    0x27, 0x44, 0x50, 0xf2, 0x6d, 0x22, 0x75, 0x41,
	    0x77, 0x2a, 0xdb, 0xb1, 0x8c, 0x6d, 0x05, 0xe8,
	    0xc9, 0x99, 0xc7, 0x08, 0xf9, 0x14, 0x8f, 0x78,
	    0xa9, 0x8f, 0xc2, 0x5a, 0x7a, 0x65, 0xc5, 0xd8,
	    0x86, 0xbb, 0x72, 0x69, 0x6b, 0x6b, 0x45, 0x83,
	    0x5b, 0xb1, 0xf7, 0xcd, 0x16, 0x73, 0xee, 0xe9,
	    0x80, 0x85, 0xfe, 0x8e, 0xe1, 0xae, 0x53, 0x8f,
	    0xde, 0xbe, 0x48, 0x8b, 0x59, 0xef, 0xf6, 0x7e,
	    0xd8, 0xb5, 0xa8, 0x47, 0xc0, 0x4e, 0x15, 0x58,
	    0xca, 0xd3, 0x2f, 0xf8, 0x6c, 0xa6, 0x3d, 0x78,
	    0x4d, 0x7a, 0x54, 0xd6, 0x10, 0xe5, 0xcc, 0x05,
	    0xe2, 0x29, 0xb5, 0x86, 0x07, 0x39, 0x7d, 0x78,
	    0x8e, 0x5a, 0x8f, 0x83, 0x4c, 0xe7, 0x3d, 0x68,
	    0x3e, 0xe5, 0x02, 0xe6, 0x64, 0x4f, 0x5e, 0xb4,
	    0x49, 0x77, 0xf0, 0xc0, 0xfa, 0x6f, 0xc8, 0xfb,
	    0x9f, 0x84, 0x6f, 0x55, 0xfb, 0x30, 0x5e, 0x89,
	    0x93, 0xa9, 0xf3, 0xa6, 0xa3, 0xd7, 0x26, 0xbb,
	    0xd8, 0xa8, 0xd9, 0x95, 0x1d, 0xfe, 0xfc, 0xd7,
	    0xa8, 0x93, 0x66, 0x2f, 0x04, 0x53, 0x06, 0x64,
	    0x7f, 0x31, 0x29, 0xae, 0xb7, 0x9f, 0xba, 0xc4,
	    0x6d, 0x68, 0xd1, 0x24, 0x32, 0xf4, 0x11
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4,
	{
	    0x07, 0x9c, 0x91, 0xbe
	},
	"id-Gost28147-89-CryptoPro-C-ParamSet",
	"testcfb3",
	{
	    0x77, 0xc3, 0x45, 0x8e, 0xf6, 0x42, 0xe7, 0x04,
	    0x8e, 0xfc, 0x08, 0xe4, 0x70, 0x96, 0xd6, 0x05,
	    0x93, 0x59, 0x02, 0x6d, 0x6f, 0x97, 0xca, 0xe9,
	    0xcf, 0x89, 0x44, 0x4b, 0xde, 0x6c, 0x22, 0x1d
	},
	G89_CFB,
	{
	    0x43, 0x7c, 0x3e, 0x8e, 0x2f, 0x2a, 0x00, 0x98
	},
	{
	    0x19, 0x35, 0x81, 0x34
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	9,
	{
	    0x2f, 0x31, 0xd8, 0x83, 0xb4, 0x20, 0xe8, 0x6e,
	    0xda
	},
	"id-Gost28147-89-CryptoPro-D-ParamSet",
	"testcfb4",
	{
	    0x38, 0x9f, 0xe8, 0x37, 0xff, 0x9c, 0x5d, 0x29,
	    0xfc, 0x48, 0x55, 0xa0, 0x87, 0xea, 0xe8, 0x40,
	    0x20, 0x87, 0x5b, 0xb2, 0x01, 0x15, 0x55, 0xa7,
	    0xe3, 0x2d, 0xcb, 0x3d, 0xd6, 0x59, 0x04, 0x73
	},
	G89_CFB,
	{
	    0xc5, 0xa2, 0xd2, 0x1f, 0x2f, 0xdf, 0xb8, 0xeb
	},
	{
	    0x6d, 0xa4, 0xed, 0x40, 0x08, 0x88, 0x71, 0xad,
	    0x16
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	5242880+8,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test5Mcfb",
	{
	    0x61, 0x58, 0x44, 0x5a, 0x41, 0xf6, 0xc7, 0x0f,
	    0x6b, 0xdb, 0x51, 0x91, 0x6a, 0xf6, 0x81, 0x30,
	    0x8c, 0xa7, 0x98, 0xdd, 0x38, 0x35, 0x8a, 0x60,
	    0x85, 0xb4, 0xf0, 0xf9, 0x43, 0xa2, 0x7d, 0x9a
	},
	G89_CFB,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	{
	    0x1c, 0x16, 0xa0, 0xe9, 0x63, 0x94, 0xfe, 0x38,
	    0x37, 0xa7, 0x9b, 0x70, 0x25, 0x2e, 0xd6, 0x00
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4294967296ULL+16,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test4Gcfb",
	{
	    0xae, 0x57, 0xa2, 0xdd, 0xa4, 0xef, 0x4f, 0x96,
	    0xb8, 0x94, 0xa5, 0xd1, 0x1b, 0xc8, 0x9b, 0x42,
	    0xa5, 0x24, 0xcc, 0x89, 0x5c, 0xb8, 0x92, 0x52,
	    0xc1, 0x12, 0x6a, 0xb0, 0x9a, 0x26, 0xe8, 0x06
	},
	G89_CFB,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	{
	    0x2e, 0x62, 0xb0, 0x2e, 0xc7, 0x87, 0x4b, 0x29,
	    0x33, 0x16, 0x6b, 0xb4, 0xd6, 0x61, 0x66, 0xd9
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	1037,
	{
	    0x3d, 0x0b, 0x69, 0xf7, 0xa8, 0xe4, 0xfc, 0x99,
	    0x22, 0x2e, 0xee, 0xd1, 0x63, 0x12, 0xfe, 0xa8,
	    0x9d, 0xcb, 0x6c, 0x4d, 0x48, 0x8c, 0xe8, 0xbd,
	    0x8b, 0x60, 0xf1, 0xbf, 0x7b, 0xe3, 0x79, 0xd5,
	    0x2b, 0x25, 0x97, 0x13, 0xef, 0x35, 0xda, 0xf4,
	    0xbc, 0x77, 0xce, 0xea, 0xe9, 0x3f, 0xa4, 0xb6,
	    0x01, 0xd5, 0x73, 0x29, 0x58, 0xda, 0xd7, 0x67,
	    0x17, 0xac, 0xe4, 0x75, 0x2f, 0x57, 0x23, 0xac,
	    0x96, 0x21, 0xc7, 0x62, 0x2d, 0xf7, 0x32, 0xb5,
	    0x44, 0x5f, 0x72, 0xb1, 0x5f, 0xba, 0x1b, 0x1e,
	    0xdb, 0x4a, 0x09, 0x8c, 0x92, 0x61, 0xa2, 0xb0,
	    0x49, 0x68, 0xe5, 0xb3, 0xa2, 0x8f, 0x13, 0x4b,
	    0xf5, 0x4d, 0x84, 0xda, 0xab, 0xa0, 0xb6, 0xd1,
	    0x5a, 0x63, 0x19, 0xe8, 0xa2, 0x09, 0xf6, 0x76,
	    0x6f, 0x9b, 0x48, 0x0a, 0x15, 0x5d, 0xb7, 0x20,
	    0x21, 0x9a, 0x2e, 0xb9, 0x6d, 0xfa, 0x1e, 0xc2,
	    0x0e, 0xef, 0x15, 0xab, 0x59, 0x01, 0xfe, 0x43,
	    0x90, 0xf2, 0x62, 0xca, 0x4a, 0x9a, 0x48, 0x38,
	    0xab, 0x6f, 0x9d, 0x21, 0xb3, 0xad, 0xa7, 0x60,
	    0x46, 0xe3, 0xef, 0xd0, 0xe3, 0x1d, 0xc5, 0xe1,
	    0xb8, 0xa1, 0xe2, 0x99, 0x20, 0xc5, 0x76, 0xcc,
	    0xaa, 0x8a, 0xa9, 0x45, 0x55, 0xa0, 0x78, 0x00,
	    0x64, 0xde, 0xcf, 0x5b, 0xdf, 0x26, 0x48, 0xcd,
	    0xba, 0x8a, 0xb5, 0xfb, 0xfd, 0x4a, 0xd5, 0xc4,
	    0xe0, 0x43, 0xa6, 0x71, 0x90, 0xa4, 0x8b, 0xca,
	    0x2e, 0x88, 0x7b, 0xac, 0xb2, 0xdc, 0xf2, 0x01,
	    0xcb, 0xda, 0x6e, 0x91, 0x27, 0x28, 0x44, 0x88,
	    0x9a, 0xd2, 0x12, 0xf1, 0xa6, 0xf5, 0xb7, 0x61,
	    0xce, 0x79, 0x62, 0x52, 0x3c, 0xe6, 0x14, 0x73,
	    0xd1, 0x41, 0x92, 0x50, 0xbd, 0xdc, 0x3b, 0xd0,
	    0xa7, 0x11, 0x8c, 0x3a, 0xe4, 0x2d, 0xf2, 0x52,
	    0xd3, 0x2f, 0x7c, 0x8e, 0x54, 0x90, 0x4e, 0x23,
	    0xae, 0xb3, 0xa0, 0xf3, 0x25, 0x7e, 0x66, 0xaa,
	    0x0f, 0x6f, 0x81, 0x72, 0x77, 0xbb, 0xd3, 0x47,
	    0xe8, 0x05, 0xff, 0xe1, 0x5b, 0xc9, 0x37, 0x50,
	    0x33, 0x49, 0x17, 0xaf, 0xab, 0x1d, 0xe1, 0x15,
	    0xf2, 0xe5, 0x98, 0x5e, 0x2d, 0x05, 0x1f, 0x0d,
	    0x55, 0x97, 0xed, 0xff, 0x5e, 0xe0, 0x0f, 0xc3,
	    0x9c, 0xbd, 0x82, 0xc2, 0x06, 0xbe, 0x45, 0x66,
	    0xae, 0x33, 0xbe, 0x28, 0x48, 0xe9, 0x2d, 0x1a,
	    0xe6, 0x65, 0x8e, 0xdf, 0x76, 0x03, 0x73, 0x4b,
	    0xc0, 0x80, 0x71, 0xf9, 0xac, 0xba, 0xa0, 0xb0,
	    0x19, 0x1a, 0x0a, 0xd4, 0x35, 0x12, 0x88, 0x76,
	    0x05, 0x75, 0x8f, 0x7c, 0xb5, 0xf0, 0x19, 0x75,
	    0x6d, 0x05, 0xcb, 0x0d, 0xbc, 0x8d, 0xe9, 0xf0,
	    0xd4, 0xdb, 0x3c, 0x3c, 0x29, 0x8e, 0x2c, 0x32,
	    0x1d, 0xf7, 0xb6, 0x49, 0xcf, 0xdb, 0x63, 0xee,
	    0x3c, 0xfa, 0x33, 0x73, 0x6f, 0xe4, 0x97, 0x4e,
	    0x2f, 0xc9, 0x4c, 0x5c, 0x65, 0xfe, 0xea, 0xfb,
	    0xc6, 0xdd, 0xc1, 0x1c, 0x47, 0x3f, 0xf4, 0x50,
	    0x2f, 0xde, 0x1b, 0x5b, 0x0b, 0x16, 0xca, 0xb6,
	    0x46, 0x44, 0xf2, 0xc1, 0x0d, 0xa1, 0x1d, 0xa6,
	    0xdb, 0xf0, 0x3d, 0xb1, 0x6c, 0x05, 0x31, 0x85,
	    0x8e, 0x74, 0xae, 0xf2, 0x39, 0x26, 0xf7, 0xc1,
	    0xe7, 0x4c, 0xdd, 0x9d, 0x40, 0xb8, 0xf3, 0xc5,
	    0xc2, 0x16, 0x64, 0x6b, 0xaa, 0xdb, 0x4b, 0x82,
	    0x5c, 0xd3, 0x02, 0xd3, 0x8f, 0x26, 0x79, 0x8d,
	    0xb0, 0x78, 0x70, 0x19, 0x58, 0x0c, 0xb4, 0x31,
	    0x88, 0x44, 0x1c, 0x91, 0x6f, 0xf4, 0x52, 0x39,
	    0xa8, 0xf5, 0xc0, 0x1b, 0xfe, 0xf2, 0x0e, 0x4b,
	    0xac, 0x0a, 0xc2, 0x7e, 0x9c, 0x9b, 0xeb, 0x5d,
	    0x4e, 0x4f, 0x42, 0xd8, 0x71, 0x0a, 0x97, 0x27,
	    0x03, 0x14, 0x96, 0xa6, 0x3d, 0x04, 0xea, 0x9f,
	    0x14, 0x14, 0x27, 0x4c, 0xd9, 0xa2, 0x89, 0x5f,
	    0x65, 0x4a, 0xe1, 0x9d, 0x2c, 0xb8, 0xf8, 0xd4,
	    0x8f, 0x2a, 0x57, 0x36, 0xcc, 0x06, 0x9c, 0x2c,
	    0xc5, 0x13, 0x16, 0xdf, 0xfc, 0xae, 0x22, 0x16,
	    0xa8, 0x2b, 0x71, 0x6f, 0x1d, 0xb3, 0x47, 0x54,
	    0x3f, 0x2d, 0x0a, 0x68, 0x9f, 0x2e, 0xf6, 0x90,
	    0xd8, 0xa1, 0x21, 0x09, 0xd4, 0x97, 0xb9, 0x7b,
	    0x7f, 0x9b, 0x6a, 0xed, 0xd1, 0xf0, 0xe3, 0xb6,
	    0x28, 0xc7, 0x62, 0x82, 0x00, 0xc9, 0x38, 0xa1,
	    0x82, 0x78, 0xce, 0x87, 0xc8, 0x53, 0xac, 0x4f,
	    0x2e, 0x31, 0xb9, 0x50, 0x7f, 0x36, 0x00, 0x4a,
	    0x32, 0xe6, 0xd8, 0xbb, 0x59, 0x45, 0x0e, 0x91,
	    0x1b, 0x38, 0xa9, 0xbc, 0xb9, 0x5e, 0x6c, 0x6a,
	    0x9c, 0x03, 0x01, 0x1c, 0xde, 0xe8, 0x1f, 0x1e,
	    0xe3, 0xde, 0x25, 0xa2, 0x56, 0x79, 0xe1, 0xbd,
	    0x58, 0xc4, 0x93, 0xe6, 0xd0, 0x8a, 0x4d, 0x08,
	    0xab, 0xf7, 0xaa, 0xc3, 0x7d, 0xc1, 0xee, 0x68,
	    0x37, 0xbc, 0x78, 0x0b, 0x19, 0x68, 0x2b, 0x2b,
	    0x2e, 0x6d, 0xc4, 0x6f, 0xaa, 0x3b, 0xc6, 0x19,
	    0xcb, 0xf1, 0x58, 0xb9, 0x60, 0x85, 0x45, 0xae,
	    0x52, 0x97, 0xba, 0x24, 0x32, 0x13, 0x72, 0x16,
	    0x6e, 0x7b, 0xc1, 0x98, 0xac, 0xb1, 0xed, 0xb4,
	    0xcc, 0x6c, 0xcf, 0x45, 0xfc, 0x50, 0x89, 0x80,
	    0x8e, 0x7a, 0xa4, 0xd3, 0x64, 0x50, 0x63, 0x37,
	    0xc9, 0x6c, 0xf1, 0xc4, 0x3d, 0xfb, 0xde, 0x5a,
	    0x5c, 0xa8, 0x21, 0x35, 0xe6, 0x2e, 0x8c, 0x2a,
	    0x3c, 0x12, 0x17, 0x79, 0x9a, 0x0d, 0x2e, 0x79,
	    0xeb, 0x67, 0x1f, 0x2b, 0xf8, 0x6e, 0xca, 0xc1,
	    0xfa, 0x45, 0x18, 0x9e, 0xdf, 0x6a, 0xe6, 0xcb,
	    0xe9, 0x5c, 0xc3, 0x09, 0xaf, 0x93, 0x58, 0x13,
	    0xbf, 0x90, 0x84, 0x87, 0x75, 0xd6, 0x82, 0x28,
	    0x8d, 0xe7, 0x2f, 0xa3, 0xfb, 0x97, 0x74, 0x2a,
	    0x73, 0x04, 0x82, 0x06, 0x76, 0x69, 0xb1, 0x0b,
	    0x19, 0xfc, 0xae, 0xb3, 0xdd, 0x2a, 0xe5, 0xc1,
	    0x05, 0xd8, 0x80, 0x95, 0x22, 0x90, 0x71, 0xfc,
	    0xc2, 0x92, 0x42, 0xfd, 0xf1, 0x70, 0xb4, 0x68,
	    0x88, 0xa4, 0x9e, 0x0a, 0x24, 0x40, 0x13, 0xc8,
	    0xa2, 0x56, 0x4f, 0x39, 0xe6, 0x06, 0xf1, 0xdc,
	    0xf5, 0x13, 0x0e, 0xad, 0x9c, 0x8b, 0xaf, 0xe9,
	    0xe3, 0x88, 0x72, 0xff, 0xa0, 0x6d, 0xda, 0x08,
	    0x70, 0xb9, 0x2e, 0x83, 0xc5, 0xbb, 0x32, 0xa5,
	    0x74, 0xc7, 0xfb, 0x7b, 0x76, 0xaf, 0x02, 0xbb,
	    0x2b, 0xb8, 0x5e, 0x65, 0x02, 0xfe, 0x0e, 0xa0,
	    0x99, 0xce, 0x01, 0x3b, 0x35, 0xe1, 0xb0, 0x22,
	    0xe5, 0x94, 0xbd, 0xdd, 0x8e, 0xbb, 0xf6, 0x75,
	    0xbf, 0xbf, 0xee, 0x7a, 0xb1, 0x58, 0xb4, 0x81,
	    0xb8, 0x39, 0x3e, 0xb6, 0x1e, 0xde, 0xda, 0x1b,
	    0xd5, 0xf7, 0xdd, 0x7d, 0x65, 0x9c, 0xaa, 0x56,
	    0x93, 0xb8, 0xaf, 0x48, 0x53, 0xc7, 0x22, 0xe4,
	    0x1c, 0xdf, 0xe9, 0x79, 0xb4, 0x20, 0x89, 0xcc,
	    0x2a, 0x79, 0x2c, 0x09, 0xbe, 0x78, 0xcf, 0xcc,
	    0xf2, 0x90, 0xd6, 0x65, 0xc5, 0x29, 0xfc, 0xda,
	    0x69, 0xfc, 0xc0, 0xd6, 0x70, 0x99, 0x61, 0x3f,
	    0x60, 0x02, 0xd8, 0x12, 0x22, 0xc8, 0x34, 0xc6,
	    0x3b, 0xb3, 0xc2, 0x33, 0xa1, 0x5c, 0x8f, 0x4c,
	    0xd1, 0x52, 0x72, 0xf2, 0x42, 0x05, 0x8e, 0x18,
	    0x1f, 0x16, 0xda, 0xb8, 0x53, 0xa1, 0x5f, 0x01,
	    0x32, 0x1b, 0x90, 0xb3, 0x53, 0x9b, 0xd0, 0x85,
	    0x61, 0x2d, 0x17, 0xed, 0x0a, 0xa4, 0xa5, 0x27,
	    0x09, 0x75, 0x7c, 0xbc, 0x30, 0xf7, 0x5e, 0x59,
	    0x9a, 0x07, 0x96, 0x84, 0x28, 0x86, 0x4b, 0xa7,
	    0x22, 0x35, 0x28, 0xc7, 0xed, 0x0d, 0xc3, 0xce,
	    0x98, 0xcc, 0x2d, 0xec, 0xd4, 0x98, 0x09, 0x8e,
	    0x52, 0x5f, 0x2b, 0x9a, 0x13, 0xbe, 0x99, 0x16,
	    0x73, 0xd1, 0x1f, 0x81, 0xe5, 0xa2, 0x08, 0x78,
	    0xcb, 0x0c, 0x20, 0xd4, 0xa5, 0xea, 0x4b, 0x5b,
	    0x95, 0x5a, 0x92, 0x9a, 0x52
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testcnt2",
	{
	    0x1b, 0x5d, 0xdb, 0x77, 0xcf, 0xf9, 0xec, 0x95,
	    0x5e, 0xcc, 0x67, 0x9f, 0x5d, 0x28, 0xad, 0x4a,
	    0x27, 0xf4, 0x32, 0xc6, 0xb2, 0xcb, 0xb1, 0x45,
	    0x6a, 0x88, 0x14, 0x0c, 0x9b, 0x9b, 0x5f, 0x48
	},
	G89_CNT,
	{
	    0x71, 0x58, 0x8c, 0xe1, 0x55, 0xf4, 0xf6, 0xb3
	},
	{
	    0x8e, 0xcd, 0x8f, 0xc8, 0xac, 0xe1, 0x15, 0x48,
	    0x2d, 0xae, 0x24, 0x8a, 0xc7, 0xfb, 0xba, 0x0f,
	    0x1d, 0x8a, 0x95, 0xa2, 0x43, 0xef, 0xcb, 0xdc,
	    0x59, 0x57, 0xa7, 0xc7, 0x0e, 0xe3, 0xe2, 0xb9,
	    0x0d, 0x86, 0x29, 0x62, 0xcb, 0x83, 0x4d, 0x07,
	    0x0c, 0x40, 0xd4, 0x7b, 0x2e, 0xca, 0xba, 0xbf,
	    0x4a, 0x60, 0x3b, 0x31, 0x98, 0xc8, 0x88, 0x47,
	    0xd9, 0x82, 0xab, 0xfc, 0x8f, 0x48, 0xe2, 0x46,
	    0xab, 0xd3, 0xa1, 0xab, 0x8a, 0x05, 0x22, 0x8c,
	    0xf4, 0xec, 0x9a, 0x1e, 0x76, 0xab, 0x1a, 0x60,
	    0xd9, 0x25, 0x6b, 0xb8, 0x56, 0xe5, 0xb2, 0xea,
	    0x10, 0xf3, 0x62, 0x04, 0x32, 0x5e, 0xaa, 0x3b,
	    0x7b, 0x57, 0xbc, 0x3b, 0x8b, 0x43, 0x47, 0xf2,
	    0xd5, 0x03, 0x7e, 0x51, 0x01, 0xff, 0x77, 0x28,
	    0xca, 0x90, 0xa3, 0xfe, 0x7e, 0x2e, 0x70, 0x16,
	    0x75, 0x18, 0x44, 0xf0, 0x1b, 0x85, 0x05, 0xea,
	    0xe3, 0x21, 0xf7, 0x26, 0x86, 0x76, 0x3c, 0x67,
	    0x9d, 0xfc, 0xbc, 0x10, 0x7f, 0x77, 0xe4, 0xed,
	    0xd3, 0x12, 0xf8, 0x83, 0x00, 0x1f, 0x4b, 0x92,
	    0x95, 0x92, 0x5c, 0xf3, 0x5a, 0xf3, 0xb7, 0xd0,
	    0xa9, 0x5f, 0xf2, 0x18, 0xc4, 0x66, 0x62, 0xc1,
	    0x84, 0x0e, 0x66, 0xe8, 0x80, 0x7d, 0x1f, 0xf0,
	    0xba, 0x01, 0x9b, 0x71, 0xae, 0x93, 0xcc, 0x27,
	    0x54, 0x34, 0x9a, 0xbd, 0xca, 0xee, 0x52, 0x09,
	    0x92, 0x9d, 0xb0, 0xd5, 0xd9, 0xba, 0x2f, 0xb9,
	    0x96, 0xdc, 0xfa, 0xbd, 0xce, 0xea, 0x1a, 0x7b,
	    0x9a, 0x1d, 0x13, 0xa7, 0x11, 0xe2, 0x9a, 0x64,
	    0xf6, 0xd3, 0xee, 0xc6, 0x33, 0xb7, 0x6e, 0xef,
	    0x25, 0x9e, 0x1e, 0x7c, 0xe3, 0x1f, 0x2c, 0x6e,
	    0xa9, 0xc0, 0xf8, 0xc1, 0xbf, 0x3b, 0xf8, 0x34,
	    0x03, 0x9b, 0xa1, 0x40, 0x5b, 0x0c, 0x3c, 0x09,
	    0x66, 0x9d, 0x63, 0xe2, 0xe2, 0x04, 0x8f, 0x06,
	    0x84, 0x74, 0x68, 0xb2, 0x5c, 0x3b, 0x4c, 0xad,
	    0x0b, 0x3f, 0x03, 0xb3, 0x07, 0x8a, 0x64, 0xa7,
	    0x36, 0x56, 0x26, 0x39, 0x66, 0xda, 0xe9, 0x6d,
	    0x1b, 0xd5, 0x88, 0xe8, 0x5c, 0xaf, 0x5a, 0x4c,
	    0x49, 0xf7, 0xf5, 0xb7, 0x78, 0xf0, 0xde, 0xec,
	    0xcd, 0x16, 0x23, 0x9e, 0x8c, 0x13, 0xbe, 0x6b,
	    0x6f, 0x9b, 0x07, 0xe5, 0xbb, 0xcc, 0x3a, 0x1b,
	    0x6f, 0x43, 0xdf, 0xff, 0x46, 0x2a, 0xae, 0x47,
	    0x19, 0x18, 0x9a, 0x25, 0x09, 0xc9, 0x24, 0x40,
	    0x0c, 0x4b, 0xa7, 0xda, 0x5e, 0x0d, 0xee, 0xfa,
	    0x62, 0x45, 0x8e, 0xcc, 0x2f, 0x23, 0x08, 0x1d,
	    0x92, 0xf0, 0xfe, 0x82, 0x0f, 0xd7, 0x11, 0x60,
	    0x7e, 0x0b, 0x0b, 0x75, 0xf4, 0xf5, 0x3b, 0xc0,
	    0xa4, 0xe8, 0x72, 0xa5, 0xb6, 0xfa, 0x5a, 0xad,
	    0x5a, 0x4f, 0x39, 0xb5, 0xa2, 0x12, 0x96, 0x0a,
	    0x32, 0x84, 0xb2, 0xa1, 0x06, 0x68, 0x56, 0x57,
	    0x97, 0xa3, 0x7b, 0x22, 0x61, 0x76, 0x5d, 0x30,
	    0x1a, 0x31, 0xab, 0x99, 0x06, 0xc5, 0x1a, 0x96,
	    0xcf, 0xcf, 0x14, 0xff, 0xb2, 0xc4, 0xcc, 0x2b,
	    0xbf, 0x0c, 0x9d, 0x91, 0x8f, 0x79, 0x5b, 0xbc,
	    0xa9, 0x6b, 0x91, 0x6a, 0xb4, 0x93, 0x5c, 0x7b,
	    0x5d, 0xc2, 0x8a, 0x75, 0xc0, 0xc1, 0x08, 0xfa,
	    0x99, 0xf9, 0x4d, 0x5e, 0x0c, 0x06, 0x64, 0x60,
	    0xa9, 0x01, 0x4a, 0x34, 0x0f, 0x33, 0x84, 0x95,
	    0x69, 0x30, 0xc1, 0x1c, 0x36, 0xf8, 0xfc, 0x30,
	    0x23, 0xb2, 0x71, 0xe5, 0x52, 0x4d, 0x12, 0x1a,
	    0xc9, 0xbe, 0xee, 0xc9, 0xcb, 0x01, 0x85, 0xf3,
	    0xdb, 0x30, 0xf9, 0x41, 0xa9, 0x40, 0xb0, 0x06,
	    0x29, 0x77, 0xcd, 0xc5, 0xec, 0x58, 0x02, 0x48,
	    0x83, 0x53, 0x44, 0x6a, 0xd2, 0xca, 0x05, 0xd8,
	    0x5a, 0x08, 0xeb, 0xa9, 0xf4, 0xe6, 0xc7, 0x9d,
	    0xd5, 0x7b, 0x74, 0x0b, 0x31, 0xb7, 0xa5, 0x57,
	    0x7c, 0x7a, 0xfd, 0x1a, 0x0e, 0xd7, 0x97, 0x41,
	    0xbf, 0xdd, 0xc6, 0x19, 0x6c, 0x77, 0x8c, 0x18,
	    0x52, 0x57, 0x83, 0xba, 0x71, 0x25, 0xee, 0x39,
	    0xbb, 0xe2, 0x43, 0xa0, 0x14, 0xdc, 0x0e, 0x84,
	    0xb4, 0x2b, 0xde, 0x3e, 0xe5, 0x36, 0xb7, 0xa2,
	    0x92, 0x98, 0x05, 0xb8, 0x96, 0xe5, 0xd0, 0x8c,
	    0x08, 0x93, 0x35, 0xc2, 0x81, 0xe0, 0xfc, 0x59,
	    0x71, 0xe2, 0x44, 0x49, 0x5d, 0xda, 0xfb, 0x9c,
	    0xaa, 0x70, 0x9f, 0x43, 0xa8, 0xa5, 0xd9, 0x67,
	    0xd9, 0x8f, 0xa3, 0x1e, 0xbe, 0x0e, 0xec, 0xdf,
	    0x12, 0x2b, 0x6a, 0xe7, 0x1c, 0x12, 0x17, 0xe7,
	    0xc4, 0x6d, 0x50, 0xc9, 0x52, 0x7a, 0xd5, 0xe8,
	    0x7f, 0xbc, 0x07, 0x15, 0xac, 0xdb, 0x93, 0x66,
	    0xb1, 0xf0, 0xa7, 0x7b, 0x2f, 0xe9, 0xec, 0xd0,
	    0x47, 0x69, 0x59, 0x87, 0xf1, 0x4c, 0x3e, 0x4b,
	    0x9b, 0x11, 0x79, 0x13, 0xe4, 0x96, 0xf6, 0x56,
	    0x04, 0x6e, 0x0b, 0x33, 0xfc, 0x40, 0xf6, 0xc7,
	    0xc1, 0x43, 0xb1, 0xbf, 0x0e, 0xb3, 0x87, 0xfd,
	    0x0b, 0x1c, 0x63, 0x46, 0x3a, 0xd3, 0xa0, 0x17,
	    0x59, 0x25, 0x94, 0x6c, 0x9c, 0x3d, 0x0c, 0x81,
	    0xce, 0x82, 0x72, 0x42, 0x28, 0xf9, 0x37, 0x6a,
	    0x6d, 0xe4, 0x12, 0xf4, 0x21, 0xaa, 0xf7, 0xfe,
	    0x27, 0x55, 0x40, 0x1a, 0x14, 0xc3, 0x39, 0x5b,
	    0xbf, 0x63, 0xc2, 0x5f, 0x10, 0x1f, 0x14, 0x25,
	    0xd0, 0xce, 0xf3, 0x14, 0x48, 0x13, 0xa5, 0x0b,
	    0x4d, 0x38, 0xcf, 0x0d, 0x34, 0xc0, 0x0a, 0x11,
	    0xb4, 0xb5, 0x72, 0xc8, 0x4b, 0xc2, 0x6f, 0xe7,
	    0x9d, 0x93, 0xf7, 0xdf, 0xb8, 0x43, 0x72, 0x7e,
	    0xda, 0x3e, 0x20, 0x1f, 0xbc, 0x21, 0x2a, 0xce,
	    0x00, 0xfa, 0x96, 0x9f, 0x3d, 0xe5, 0x88, 0x96,
	    0xef, 0x29, 0x84, 0xdf, 0x6c, 0x1c, 0x96, 0xd8,
	    0x58, 0x47, 0xaa, 0x92, 0xf3, 0x07, 0xe5, 0xfb,
	    0xaf, 0xea, 0x95, 0x7e, 0x0b, 0x71, 0xcd, 0x81,
	    0x0f, 0xb7, 0x0a, 0x59, 0x8f, 0x31, 0x4d, 0xd1,
	    0xc3, 0xf3, 0x2f, 0x70, 0x5c, 0x59, 0x18, 0x97,
	    0xaf, 0x77, 0x95, 0x5e, 0xaf, 0x40, 0x06, 0x12,
	    0x81, 0x61, 0x86, 0x08, 0x4e, 0xbc, 0x89, 0x46,
	    0x07, 0x2e, 0x5b, 0x10, 0xaa, 0x12, 0xf0, 0xa7,
	    0x84, 0xe2, 0x9a, 0x08, 0xf1, 0xde, 0x59, 0xe3,
	    0x0e, 0x47, 0x4b, 0xff, 0xc3, 0xc9, 0x18, 0xaf,
	    0x95, 0x9c, 0x67, 0x2a, 0xde, 0x8a, 0x7a, 0x99,
	    0x04, 0xc4, 0xb8, 0x97, 0x4c, 0x04, 0x29, 0x71,
	    0x05, 0xda, 0xb3, 0xd6, 0xdb, 0x6c, 0x71, 0xe6,
	    0xe8, 0x03, 0xbf, 0x94, 0x7d, 0xde, 0x3d, 0xc8,
	    0x44, 0xfa, 0x7d, 0x62, 0xb4, 0x36, 0x03, 0xee,
	    0x36, 0x52, 0x64, 0xb4, 0x85, 0x6d, 0xd5, 0x78,
	    0xf0, 0x6f, 0x67, 0x2d, 0x0e, 0xe0, 0x2c, 0x88,
	    0x9b, 0x55, 0x19, 0x29, 0x40, 0xf6, 0x8c, 0x12,
	    0xbb, 0x2c, 0x83, 0x96, 0x40, 0xc0, 0x36, 0xf5,
	    0x77, 0xff, 0x70, 0x8c, 0x75, 0x92, 0x0b, 0xad,
	    0x05, 0x9b, 0x7e, 0xa2, 0xfc, 0xa9, 0xd1, 0x64,
	    0x76, 0x82, 0x13, 0xba, 0x22, 0x5e, 0x33, 0x0e,
	    0x26, 0x70, 0xa9, 0xbe, 0x74, 0x28, 0xf5, 0xe2,
	    0xc4, 0x96, 0xee, 0x3a, 0xbc, 0x97, 0xa6, 0x2c,
	    0x2a, 0xe0, 0x64, 0x8d, 0x35, 0xc6, 0x1a, 0xca,
	    0xf4, 0x92, 0xfa, 0xc3, 0xf1, 0x1f, 0x98, 0xe4,
	    0x43, 0x88, 0x69, 0x3a, 0x09, 0xbf, 0x63, 0xe5,
	    0x96, 0x29, 0x0b, 0x9b, 0x62, 0x23, 0x14, 0x8a,
	    0x95, 0xe4, 0x1c, 0x5c, 0x0a, 0xa9, 0xc5, 0xb9,
	    0x6f, 0x4f, 0x2b, 0x25, 0x6f, 0x74, 0x1e, 0x18,
	    0xd5, 0xfe, 0x27, 0x7d, 0x3f, 0x6e, 0x55, 0x2c,
	    0x67, 0xe6, 0xde, 0xb5, 0xcc, 0xc0, 0x2d, 0xff,
	    0xc4, 0xe4, 0x06, 0x21, 0xa5, 0xc8, 0xd3, 0xd6,
	    0x6c, 0xa1, 0xc3, 0xfb, 0x88, 0x92, 0xb1, 0x1d,
	    0x90, 0xe1, 0x35, 0x05, 0x9b, 0x29, 0x6d, 0xba,
	    0xf1, 0xf4, 0x1e, 0x23, 0x2e
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	5242880+8,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test5Mcnt",
	{
	    0x07, 0x52, 0x65, 0xe7, 0xca, 0xa3, 0xca, 0x45,
	    0xcf, 0x3a, 0x05, 0x1d, 0x38, 0x03, 0x53, 0x0c,
	    0x22, 0x31, 0xba, 0x99, 0x4f, 0x9b, 0x6a, 0x1b,
	    0x7e, 0x09, 0x9d, 0x4e, 0xb5, 0xc9, 0x84, 0x2e
	},
	G89_CNT,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	{
	    0x3d, 0x05, 0x07, 0x57, 0xc0, 0x75, 0x89, 0x97,
	    0xd6, 0x94, 0x49, 0x11, 0x1d, 0xd0, 0x91, 0xee
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4294967296ULL+16,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test4Gcnt",
	{
	    0x75, 0xa3, 0x3c, 0xae, 0x03, 0x6b, 0x10, 0xdb,
	    0xc1, 0x56, 0x50, 0x89, 0x03, 0xd2, 0x9f, 0x91,
	    0xee, 0xe8, 0x64, 0x1d, 0x43, 0xf2, 0x4e, 0xf8,
	    0xf2, 0x6c, 0xed, 0xda, 0x8f, 0xe4, 0x88, 0xe9
	},
	G89_CNT,
	{
	    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
	},
	{
	    0xfa, 0x6c, 0x96, 0x78, 0xe2, 0xf8, 0xdd, 0xaa,
	    0x67, 0x5a, 0xc9, 0x5d, 0x57, 0xf1, 0xbd, 0x99
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	1035,
	{
	    0xd6, 0xcf, 0x31, 0x96, 0x9c, 0xa1, 0xfb, 0xd6,
	    0x8d, 0xa3, 0xdd, 0x01, 0xd9, 0x88, 0xc0, 0x2f,
	    0xbc, 0x46, 0xc7, 0x3a, 0xe4, 0x21, 0x86, 0x96,
	    0x8d, 0xe2, 0xca, 0xb6, 0x37, 0xa2, 0xe1, 0xa8,
	    0x7e, 0xa7, 0x79, 0x2e, 0xa4, 0x56, 0x75, 0x7f,
	    0x3e, 0x55, 0x8b, 0x43, 0xae, 0x65, 0xdf, 0xaa,
	    0x42, 0xb6, 0x00, 0xa6, 0x61, 0x03, 0x0d, 0xd3,
	    0x41, 0x02, 0x27, 0x23, 0x95, 0x79, 0x9b, 0x34,
	    0x81, 0xa9, 0x86, 0xb5, 0xa7, 0x90, 0xe2, 0xae,
	    0xc4, 0x2f, 0xc3, 0x8e, 0x32, 0x56, 0x13, 0xfa,
	    0x4d, 0x4e, 0x9f, 0x15, 0x75, 0x7e, 0x74, 0xdc,
	    0x32, 0x2d, 0xee, 0x4d, 0x67, 0x70, 0x9f, 0x62,
	    0xb9, 0xc4, 0xdb, 0x24, 0x84, 0xcc, 0x16, 0x7b,
	    0xda, 0x22, 0xf7, 0xc5, 0xf3, 0x93, 0x35, 0x73,
	    0xc6, 0x03, 0x1c, 0x77, 0xa5, 0xf2, 0x76, 0x56,
	    0xb4, 0x95, 0xd4, 0x7e, 0x0d, 0x20, 0xc6, 0x6e,
	    0xee, 0x8f, 0x25, 0x48, 0xff, 0x7e, 0x01, 0x3a,
	    0xb4, 0x1f, 0xaa, 0x35, 0xc0, 0x33, 0x58, 0x9c,
	    0xb5, 0xba, 0x65, 0x4b, 0xd3, 0x51, 0x14, 0xec,
	    0x61, 0xce, 0xe4, 0xba, 0x49, 0xba, 0x39, 0x32,
	    0xab, 0xce, 0x81, 0x72, 0xce, 0xab, 0xed, 0xd4,
	    0xd2, 0x19, 0x87, 0x85, 0x92, 0xfa, 0x64, 0x34,
	    0xd8, 0x86, 0xf4, 0x8a, 0x08, 0x3c, 0xde, 0xee,
	    0x97, 0x92, 0x92, 0x69, 0xba, 0x9b, 0x5f, 0x7a,
	    0x03, 0xc1, 0x5d, 0x43, 0x02, 0x8c, 0xbe, 0xd2,
	    0x46, 0x72, 0x81, 0x40, 0x7d, 0x68, 0x98, 0x45,
	    0x0b, 0x54, 0x27, 0x1c, 0xaf, 0x80, 0x42, 0xe4,
	    0xd5, 0xd4, 0xe4, 0xa2, 0x98, 0x07, 0x8f, 0x03,
	    0xf5, 0x2c, 0x8c, 0x88, 0xca, 0x5a, 0xde, 0xe4,
	    0x9f, 0xb1, 0x5f, 0x82, 0xff, 0x20, 0x67, 0x52,
	    0x85, 0x84, 0x4f, 0xc8, 0xfe, 0xa7, 0x9e, 0xae,
	    0x1c, 0xfa, 0xb8, 0x75, 0xd3, 0xf7, 0x9f, 0x0d,
	    0xda, 0x2d, 0xe6, 0xcc, 0x86, 0x6b, 0xa4, 0x14,
	    0x65, 0xc3, 0xf9, 0x15, 0xbc, 0x87, 0xf5, 0xae,
	    0x8c, 0x10, 0xd4, 0xce, 0x5b, 0x9c, 0xe2, 0xdd,
	    0x42, 0x03, 0x09, 0x87, 0x47, 0xed, 0x5d, 0xd0,
	    0x7a, 0x69, 0x4c, 0xfa, 0x43, 0x7d, 0xbf, 0x07,
	    0x85, 0x6a, 0xee, 0x68, 0xe6, 0x7a, 0x57, 0xb2,
	    0x20, 0x8d, 0x80, 0xf2, 0x91, 0x6f, 0x5c, 0x07,
	    0x8c, 0xe4, 0x6a, 0x49, 0x90, 0x85, 0x8b, 0x77,
	    0x29, 0x56, 0x1c, 0x5e, 0xa9, 0x3f, 0xab, 0x8b,
	    0x79, 0xa3, 0x6f, 0x6b, 0x34, 0xcb, 0x61, 0xf6,
	    0xe6, 0x92, 0xd1, 0x48, 0x9e, 0x11, 0xa2, 0x82,
	    0xc0, 0x4e, 0x23, 0xd2, 0x15, 0x0d, 0x8d, 0xff,
	    0xfa, 0x17, 0x9d, 0x81, 0xb8, 0xbc, 0xd7, 0x5b,
	    0x08, 0x81, 0x20, 0x40, 0xc0, 0x3c, 0x06, 0x8b,
	    0x1a, 0x88, 0x0b, 0x4b, 0x7b, 0x31, 0xf5, 0xd4,
	    0x4e, 0x09, 0xd1, 0x4d, 0x0d, 0x7f, 0x45, 0xd1,
	    0x09, 0x35, 0xba, 0xce, 0x65, 0xdd, 0xf2, 0xb8,
	    0xfb, 0x7a, 0xbc, 0xc4, 0x4b, 0xc8, 0x75, 0xda,
	    0x6b, 0xce, 0x3d, 0xe8, 0x94, 0xcc, 0x23, 0x6f,
	    0xb0, 0x3b, 0x4f, 0x7d, 0x07, 0xb9, 0x0f, 0x62,
	    0x92, 0x7e, 0xda, 0x70, 0x50, 0xce, 0xd3, 0x28,
	    0x12, 0x11, 0x00, 0xeb, 0x8d, 0x63, 0x70, 0x78,
	    0xa8, 0x7b, 0x76, 0xab, 0xc6, 0x40, 0xc0, 0x4e,
	    0x80, 0xdd, 0xf0, 0xfe, 0x83, 0x72, 0x56, 0x4c,
	    0x09, 0x4c, 0xf1, 0x72, 0x72, 0x86, 0x26, 0x31,
	    0xc3, 0xc2, 0xdc, 0x8e, 0xc7, 0xf4, 0x35, 0xec,
	    0x17, 0x06, 0x63, 0x47, 0x49, 0x88, 0x47, 0xaf,
	    0xb3, 0x38, 0x4f, 0x7e, 0x44, 0x95, 0xb5, 0xbb,
	    0x1d, 0xbd, 0x5a, 0x91, 0x5b, 0xd0, 0x1a, 0xdf,
	    0x0d, 0x0b, 0x50, 0xd8, 0xe2, 0x0e, 0xc5, 0x00,
	    0x2d, 0x5b, 0x29, 0x19, 0xaa, 0x2b, 0x64, 0xc5,
	    0x40, 0x31, 0x48, 0x11, 0xbc, 0x04, 0xd1, 0xcf,
	    0x6d, 0xf9, 0xa5, 0x2f, 0x4a, 0xc9, 0x82, 0xfa,
	    0x59, 0xe1, 0xfc, 0xab, 0x1c, 0x33, 0x26, 0x0a,
	    0x5f, 0xef, 0xf2, 0x06, 0xd8, 0xd3, 0x7e, 0x16,
	    0x58, 0x16, 0x78, 0x73, 0xae, 0xba, 0xeb, 0xe5,
	    0x3d, 0xb2, 0x0a, 0xb3, 0x32, 0x2d, 0x14, 0xa4,
	    0xfa, 0x3f, 0x1f, 0x43, 0xf9, 0x7b, 0xa9, 0x43,
	    0x98, 0x18, 0x94, 0x07, 0x07, 0xe5, 0x19, 0x34,
	    0xa8, 0x16, 0x5f, 0x71, 0x67, 0xaa, 0x29, 0xe5,
	    0xfa, 0xf0, 0x83, 0x06, 0x1d, 0x9d, 0xfc, 0xfe,
	    0xfe, 0x8c, 0xb5, 0xb2, 0xa9, 0xe7, 0xa0, 0x40,
	    0x60, 0xb6, 0x71, 0x9e, 0xab, 0x5b, 0x83, 0xb9,
	    0x0c, 0x2b, 0x58, 0x23, 0x80, 0x09, 0x9e, 0x5d,
	    0x94, 0x7d, 0x40, 0x76, 0xa9, 0x16, 0x96, 0x9e,
	    0x83, 0xe0, 0x0d, 0xec, 0xa0, 0xec, 0x76, 0x2a,
	    0xb7, 0xa0, 0xff, 0xb8, 0x50, 0x4c, 0x5b, 0xc6,
	    0x8b, 0x0a, 0x65, 0x2e, 0xfe, 0xb4, 0x40, 0x9a,
	    0x01, 0xd8, 0xc6, 0xa3, 0xab, 0x99, 0xa2, 0xc5,
	    0x0c, 0x08, 0xc4, 0xb7, 0xee, 0x4d, 0x1d, 0xc4,
	    0x08, 0x15, 0xd0, 0xdb, 0xaa, 0x63, 0x4f, 0x31,
	    0xeb, 0x14, 0x97, 0x43, 0xbd, 0xc1, 0x94, 0x08,
	    0xe6, 0xde, 0x43, 0x9f, 0x95, 0x0b, 0x96, 0x7e,
	    0x7f, 0x3c, 0x68, 0xba, 0x6f, 0xc4, 0xc9, 0x35,
	    0x2b, 0xc4, 0x0e, 0xda, 0x1f, 0x91, 0x68, 0x64,
	    0x63, 0x34, 0x73, 0xbe, 0x57, 0x75, 0xb9, 0xed,
	    0xf7, 0x2d, 0x3b, 0x05, 0x21, 0x93, 0x28, 0x48,
	    0x96, 0x95, 0x97, 0xa0, 0xd2, 0x7d, 0x78, 0xbb,
	    0x6a, 0x49, 0x8f, 0x76, 0x55, 0x74, 0x63, 0xb9,
	    0xc5, 0x36, 0x12, 0x25, 0xbf, 0x03, 0x82, 0x8f,
	    0xf0, 0xf6, 0x80, 0xbb, 0x33, 0xb4, 0xf4, 0x17,
	    0x27, 0x1c, 0xf3, 0x4c, 0x10, 0xa3, 0xe4, 0xd1,
	    0x55, 0xd9, 0x68, 0x21, 0x4e, 0x5a, 0x83, 0x67,
	    0xbf, 0xf8, 0x3c, 0x7d, 0x4e, 0x62, 0xd3, 0x28,
	    0xa7, 0x26, 0x6f, 0xe9, 0xee, 0xc2, 0x0b, 0x2d,
	    0x03, 0x84, 0xb1, 0xff, 0xd6, 0x68, 0x1f, 0xb6,
	    0xf2, 0xe4, 0x0f, 0xda, 0x2d, 0xee, 0x5f, 0x6e,
	    0x21, 0xc8, 0xe1, 0xfc, 0xad, 0x6b, 0x0e, 0x04,
	    0x7d, 0xaf, 0xc2, 0x3b, 0xa5, 0x68, 0x9b, 0x0c,
	    0xf3, 0x56, 0xf3, 0xda, 0x8d, 0xc8, 0x7d, 0x39,
	    0xdc, 0xd5, 0x99, 0xc6, 0x01, 0x10, 0xce, 0x42,
	    0x1b, 0xac, 0x48, 0xdc, 0x97, 0x78, 0x0a, 0xec,
	    0xb3, 0x8f, 0x47, 0x35, 0xa3, 0x6a, 0x64, 0xb2,
	    0x8e, 0x63, 0x69, 0x22, 0x66, 0xae, 0x2e, 0xe0,
	    0x88, 0xf9, 0x40, 0x3c, 0xc9, 0xa2, 0x57, 0x61,
	    0xf6, 0xad, 0xf0, 0xdc, 0x90, 0x56, 0x3f, 0x06,
	    0x9b, 0x7d, 0xbd, 0xc2, 0x81, 0x02, 0xab, 0xb8,
	    0x15, 0x09, 0x88, 0x4a, 0xff, 0x2f, 0x31, 0xbf,
	    0x5e, 0xfa, 0x6a, 0x7e, 0xf6, 0xc5, 0xa7, 0xf7,
	    0xd5, 0xab, 0x55, 0xac, 0xae, 0x0d, 0x8c, 0x8d,
	    0x7f, 0x4b, 0x25, 0xbb, 0x32, 0xff, 0x11, 0x33,
	    0x2e, 0x37, 0x37, 0x69, 0x96, 0x15, 0x17, 0xb1,
	    0x17, 0x49, 0xe0, 0x9a, 0x9c, 0xd9, 0x5b, 0x8d,
	    0x58, 0xa3, 0x1d, 0x92, 0x87, 0xf8, 0x80, 0xb9,
	    0xbd, 0x5a, 0xec, 0x40, 0xe1, 0x00, 0x33, 0x60,
	    0xe4, 0x86, 0x16, 0x6d, 0x61, 0x81, 0xf2, 0x28,
	    0x6a, 0xa7, 0xce, 0x3f, 0x95, 0xae, 0x43, 0xca,
	    0xe1, 0x3f, 0x81, 0x74, 0x7e, 0x1c, 0x47, 0x17,
	    0x95, 0xc6, 0x60, 0xda, 0x74, 0x77, 0xd9, 0x9f,
	    0xfa, 0x92, 0xb4, 0xbe, 0xe1, 0x23, 0x98, 0x18,
	    0x95, 0x63, 0x03, 0x13, 0x4c, 0x1a, 0x2d, 0x41,
	    0xcd, 0xe4, 0x84, 0xf7, 0xe6, 0x38, 0xef, 0xff,
	    0x95, 0xb2, 0xe8, 0x7c, 0x8f, 0x58, 0xb5, 0xb5,
	    0xed, 0x27, 0x7f, 0x3c, 0x18, 0xab, 0xbe, 0x7f,
	    0x4f, 0xe2, 0x35, 0x15, 0x71, 0xb7, 0x6f, 0x85,
	    0x38, 0x9b, 0x88, 0xf6, 0x9c, 0x8d, 0x43, 0xb5,
	    0x58, 0x9e, 0xf2, 0xd1, 0x96, 0xbe, 0xb7, 0xad,
	    0x1a, 0xa0, 0x98
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit2",
	{
	    0x80, 0xd9, 0xa0, 0xdc, 0x21, 0xf9, 0x30, 0x40,
	    0x75, 0xfe, 0x49, 0x1b, 0x9e, 0x71, 0x90, 0x91,
	    0x78, 0x88, 0x21, 0x60, 0x39, 0xe7, 0xc9, 0x2b,
	    0xfb, 0x55, 0x1d, 0xf4, 0xdd, 0x2b, 0x0a, 0x01
	},
	G89_IMIT,
	{ 0 },
	{
	    0x90, 0xf2, 0x11, 0x9a
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	10,
	{
	    0x1d, 0xeb, 0xe6, 0x79, 0x0a, 0x59, 0x00, 0xe6,
	    0x8e, 0x5c
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit3",
	{
	    0xa9, 0xb6, 0x37, 0xcc, 0x6d, 0x9b, 0x2f, 0x25,
	    0xb0, 0xdf, 0x47, 0x04, 0x50, 0x68, 0xb0, 0x27,
	    0x41, 0x27, 0x58, 0x6a, 0xbd, 0x0a, 0x6e, 0x50,
	    0x2f, 0xc6, 0xfc, 0xc0, 0x3e, 0x29, 0x42, 0xa5
	},
	G89_IMIT,
	{ 0 },
	{
	    0x31, 0x7c, 0x16, 0xe4
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	6,
	{
	    0xef, 0x06, 0x8f, 0x14, 0xc9, 0x04
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit4",
	{
	    0xb0, 0x6c, 0x48, 0x23, 0x0a, 0x6e, 0xf4, 0xec,
	    0x27, 0x98, 0x01, 0x23, 0xa7, 0xd8, 0xbf, 0x60,
	    0x89, 0xef, 0xad, 0xe8, 0x8f, 0x79, 0x14, 0x8c,
	    0x18, 0x5c, 0x9a, 0xda, 0xef, 0x0b, 0xdd, 0xa0
	},
	G89_IMIT,
	{ 0 },
	{
	    0xe9, 0x72, 0xae, 0xbf
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	16,
	{
	    0x02, 0xf8, 0xec, 0x2b, 0x4d, 0x1f, 0xbc, 0x7c,
	    0x6e, 0x47, 0xe3, 0x87, 0x22, 0x75, 0x41, 0xa7
	},
	"id-Gost28147-89-CryptoPro-B-ParamSet",
	"testimit5",
	{
	    0x33, 0xd3, 0xef, 0x01, 0x19, 0x95, 0x0e, 0x15,
	    0xa1, 0x69, 0x75, 0xae, 0x56, 0x27, 0x17, 0x79,
	    0x63, 0x47, 0xab, 0x62, 0x9d, 0x4a, 0xf0, 0x34,
	    0xd3, 0x1e, 0x69, 0x74, 0xec, 0x31, 0x48, 0xfc
	},
	G89_IMIT,
	{ 0 },
	{
	    0xf5, 0x55, 0x1f, 0x28
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	8,
	{
	    0xf3, 0xb2, 0x29, 0xd2, 0x7a, 0x37, 0x03, 0x12
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit6",
	{
	    0x42, 0x35, 0x81, 0x91, 0x0b, 0xa9, 0x99, 0xff,
	    0xd9, 0x43, 0xf8, 0xc6, 0x19, 0x55, 0x1f, 0x2f,
	    0x2d, 0x45, 0x40, 0x20, 0x1e, 0x1d, 0x32, 0x7a,
	    0xb1, 0x07, 0x6b, 0x4f, 0x45, 0x90, 0xd9, 0x80
	},
	G89_IMIT,
	{ 0 },
	{
	    0x6e, 0x15, 0xfa, 0xe8
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	0,
	{
	    0
	},
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"testimit7",
	{
	    0x26, 0xcb, 0xb9, 0xf0, 0x0c, 0x62, 0x9f, 0xaa,
	    0x4a, 0x1d, 0xb6, 0x30, 0x09, 0x01, 0x56, 0x89,
	    0x66, 0xd4, 0xe4, 0x0e, 0xfe, 0xf6, 0x10, 0x6b,
	    0x6c, 0xe8, 0x04, 0x3a, 0xe3, 0x61, 0x4b, 0x19
	},
	G89_IMIT,
	{ 0 },
	{
	    0x00, 0x00, 0x00, 0x00
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	5242880,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test5Mimit",
	{
	    0xaa, 0x85, 0x84, 0xcd, 0x65, 0x28, 0xe1, 0xdb,
	    0xb8, 0x20, 0x19, 0x43, 0xe0, 0x36, 0x35, 0x10,
	    0x19, 0xc3, 0x70, 0x5b, 0x27, 0xc1, 0x9d, 0x84,
	    0x75, 0xa3, 0xc6, 0x49, 0x46, 0x8f, 0x7c, 0x4e
	},
	G89_IMIT,
	{ 0 },
	{
	    0x2a, 0xe6, 0x23, 0xc6
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	3221225472ULL + 16,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test3Gimit1",
	{
	    0xd5, 0xda, 0xfe, 0x06, 0x60, 0xdc, 0xf0, 0xb3,
	    0x49, 0x5a, 0x02, 0x59, 0xc8, 0x2e, 0x4a, 0x2b,
	    0xcc, 0x9b, 0x98, 0x04, 0xb7, 0xf2, 0x78, 0xb7,
	    0xce, 0xa3, 0xf2, 0xdb, 0x9e, 0xa8, 0x49, 0x1d
	},
	G89_IMIT,
	{ 0 },
	{
	    0xcc, 0x46, 0x67, 0xe4
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4ULL*1024*1024*1024ULL,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test4Gimit3",
	{
	    0x0d, 0xf1, 0xa8, 0x7f, 0x57, 0x03, 0x44, 0xcc,
	    0xdb, 0x20, 0xde, 0xed, 0x85, 0x50, 0x38, 0xda,
	    0xc9, 0x44, 0xec, 0x2c, 0x0d, 0x66, 0xb7, 0xdc,
	    0x17, 0x14, 0x55, 0x95, 0x33, 0x6e, 0x43, 0x3e
	},
	G89_IMIT,
	{ 0 },
	{
	    0xb7, 0x21, 0x2e, 0x48
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4ULL*1024*1024*1024+4ULL,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test4Gimit1",
	{
	    0x0c, 0xf3, 0xe9, 0xb0, 0x28, 0x3b, 0x9f, 0x8b,
	    0xe3, 0x82, 0xb9, 0xa2, 0xa6, 0xbd, 0x80, 0xd2,
	    0xcd, 0xfa, 0x3f, 0xf7, 0x90, 0xa7, 0x55, 0x06,
	    0x9b, 0x7a, 0x58, 0xee, 0xe7, 0xf1, 0x9d, 0xbe
	},
	G89_IMIT,
	{ 0 },
	{
	    0xda, 0x15, 0x10, 0x73
	}
    },
    { /* Calculated by libcapi10, CryptoPro CSP 3.6R2, Mac OSX */
	4ULL*1024*1024*1024+10ULL,
	{ 0 },
	"id-Gost28147-89-CryptoPro-A-ParamSet",
	"test4Gimit2",
	{
	    0x97, 0x1a, 0x42, 0x22, 0xfa, 0x07, 0xb2, 0xca,
	    0xf9, 0xd2, 0x34, 0x5a, 0x92, 0xb1, 0x1f, 0x6b,
	    0x53, 0xf8, 0xaf, 0xed, 0x9a, 0x73, 0xc4, 0x38,
	    0xd7, 0x7d, 0x25, 0x81, 0x00, 0x0d, 0xd4, 0x29
	},
	G89_IMIT,
	{ 0 },
	{
	    0x52, 0xaa, 0x22, 0xb4
	}
    }
};

int main(int argc, char *argv[])
{
    int t;
    unsigned long long ullMaxLen = 6*1000*1000;
    int ignore = 0;
    ENGINE *impl = NULL;
    EVP_MD_CTX *mctx = NULL;
    EVP_CIPHER_CTX *ectx = NULL;
    EVP_PKEY *mac_key;
    byte bDerive[EVP_MAX_KEY_LENGTH];
    byte bTest[G89_MAX_TC_LEN];
    byte bTest1[G89_MAX_TC_LEN];
    unsigned long long ullLeft;
    static const byte bZB[40*1024*1024] = { 0 };
    static byte bTS[40*1024*1024] = { 0 };
    unsigned int mdl = 0;
    int enlu = 0;
    int enlf = 0;
    size_t siglen;
    size_t l = 0;

    const EVP_MD *md_gost94 = NULL;
    const EVP_CIPHER *cp_g89cfb = NULL;
    const EVP_CIPHER *cp_g89cnt = NULL;
    const EVP_CIPHER *ctype = NULL;
    const EVP_MD *md_g89imit = NULL;

    long lErrLine;
    CONF *pConfig = NCONF_new(NULL);
    BIO *bpConf;
    char sConf[] =
	    "openssl_conf = openssl_def\n"
	    "\n"
	    "[openssl_def]\n"
	    "engines = engine_section\n"
	    "\n"
	    "[engine_section]\n"
	    "gost = gost_section\n"
	    "\n"
	    "[gost_section]\n"
	    "default_algorithms = ALL\n"
	    "\n"
	    ;

    printf("Testing GOST 28147-89 ");

    if(1 < argc) {
       if(1 != sscanf(argv[1], "%llu", &ullMaxLen) ||
          ( 2 < argc ?
            1 != sscanf(argv[2], "%d", &ignore) : 0)) {
           fflush(NULL);
           fprintf(stderr, "Usage: %s [maxlen [ignore-error]]\n",
                               argv[0]);
           return 1;
       }
    }

    /*
     * Internal function test on GostR3411_94_TestParamSet
     */
    #ifdef OPENSSL_NO_DYNAMIC_ENGINE
    {
	gost_ctx ctx;

	for(t = 0; t < sizeof(tcs)/sizeof(tcs[0]); t++) {
	    const gost_subst_block *pSubst = NULL;

	    if(1024 < tcs[t].ullLen) {
		/* Key meshing check by engine tests */
		continue;
	    }
	    memset(bTest, 0xc3, sizeof(bTest));
	    if(0 == strcmp(tcs[t].szParamSet,
			"id-GostR3410-94-TestParamSet")) {
		pSubst = &GostR3411_94_TestParamSet;
	    } else if(0 == strcmp(tcs[t].szParamSet,
			"id-Gost28147-89-CryptoPro-A-ParamSet")) {
		pSubst = &Gost28147_CryptoProParamSetA;
	    } else if(0 == strcmp(tcs[t].szParamSet,
			"id-Gost28147-89-CryptoPro-B-ParamSet")) {
		pSubst = &Gost28147_CryptoProParamSetB;
	    } else if(0 == strcmp(tcs[t].szParamSet,
			"id-Gost28147-89-CryptoPro-C-ParamSet")) {
		pSubst = &Gost28147_CryptoProParamSetC;
	    } else if(0 == strcmp(tcs[t].szParamSet,
			"id-Gost28147-89-CryptoPro-D-ParamSet")) {
		pSubst = &Gost28147_CryptoProParamSetD;
	    }
	    gost_init(&ctx, pSubst);
	    gost_key(&ctx, tcs[t].bRawKey);
	    switch(tcs[t].gMode) {
	    case G89_ECB:
		gost_enc(&ctx, tcs[t].bIn, bTest,
			    (int)((tcs[t].ullLen + G89_BLOCK_LEN - 1)/
			          G89_BLOCK_LEN));
		l = (size_t)tcs[t].ullLen;
		break;
	    case G89_CFB:
		gost_enc_cfb(&ctx, tcs[t].bIV, tcs[t].bIn,
			    bTest,
			    (int)((tcs[t].ullLen + G89_BLOCK_LEN - 1)/
			          G89_BLOCK_LEN));
		l = (size_t)tcs[t].ullLen;
		break;
	    case G89_CNT:
	    	/* GOST 28147-89 cipher CNT mode check by engine
		 * tests
		 */
		continue;
	    case G89_IMIT:
	        gost_mac(&ctx, 32, tcs[t].bIn,
				    (unsigned int)tcs[t].ullLen,
				    bTest);
	        gost_mac_iv(&ctx, 32, tcs[t].bIV, tcs[t].bIn,
				    (unsigned int)tcs[t].ullLen,
				    bTest1);
		if(0 != memcmp(bTest, bTest1, 4)) {
		    fflush(NULL);
		    fprintf(stderr, "\nInternal test t=%d len=%llu "
				    "failed (gost_mac_iv).\n", t,
				    tcs[t].ullLen);
		    if(!ignore) {
			return 2;
		    }
		}
		l = 4;
		break;
	    }
	    gost_destroy(&ctx);

	    if(0 != memcmp(tcs[t].bOut, bTest, l)) {
		fflush(NULL);
		fprintf(stderr, "\nInternal test t=%d len=%llu "
				"failed.\n", t,
				tcs[t].ullLen);
		if(!ignore) {
		    return 3;
		}
	    } else {
		printf(",");
		fflush(NULL);
	    }
	}
    }
    #endif

    /*
     * ccgost engine test on GostR3411_94_CryptoProParamSet
     */
    #ifndef OPENSSL_NO_DYNAMIC_ENGINE
	setenv("OPENSSL_ENGINES", CCGOST_DIR, 1);
    #endif
    ERR_load_crypto_strings();
    ENGINE_load_builtin_engines();
    OPENSSL_load_builtin_modules();

    bpConf = BIO_new_mem_buf(sConf, -1);
    if(!NCONF_load_bio(pConfig, bpConf, &lErrLine)) {
	fflush(NULL);
	fprintf(stderr, "NCONF_load_bio: ErrLine=%ld: %s\n",
		lErrLine,
		ERR_error_string(ERR_get_error(), NULL));
	return 4;
    }
    BIO_free(bpConf);

    if(!CONF_modules_load(pConfig, NULL, 0)) {
	fflush(NULL);
	fprintf(stderr, "CONF_modules_load: %s\n",
		ERR_error_string(ERR_get_error(), NULL));
	return 5;
    }

	/* Test load engine */
    if(NULL == (impl = ENGINE_by_id(CCGOST_ID))) {
	fflush(NULL);
	fprintf(stderr, "Can't load engine id \"" CCGOST_ID "\"\n");
	if(!ignore) {
	    return 6;
	}
    }
    if(NULL == (md_gost94 = EVP_get_digestbyname(SN_id_GostR3411_94))) {
	fflush(NULL);
	fprintf(stderr, "\"" SN_id_GostR3411_94 "\" - not found\n");
	if(!ignore) {
	    return 7;
	}
    }
    if(NULL == (cp_g89cfb = EVP_get_cipherbyname(SN_id_Gost28147_89))) {
	fflush(NULL);
	fprintf(stderr, "\"" SN_id_Gost28147_89 "\" - not found\n");
	if(!ignore) {
	    return 8;
	}
    }
    if(NULL == (cp_g89cnt = EVP_get_cipherbyname(SN_gost89_cnt))) {
	fflush(NULL);
	fprintf(stderr, "\"" SN_gost89_cnt "\" - not found\n");
	if(!ignore) {
	    return 9;
	}
    }
    if(NULL == (md_g89imit = EVP_get_digestbyname(SN_id_Gost28147_89_MAC))) {
	fflush(NULL);
	fprintf(stderr, "\"" SN_id_Gost28147_89_MAC "\" - not found\n");
	if(!ignore) {
	    return 10;
	}
    }

	/* Test cases */
    for(t = 0; t < sizeof(tcs)/sizeof(tcs[0]); t++) {
	if(NULL == tcs[t].szDerive) {
	    continue;
	}
	memset(bDerive, 0x3c, sizeof(bDerive));
	mdl = sizeof(bDerive);
	EVP_Digest(tcs[t].szDerive, strlen(tcs[t].szDerive),
			bDerive, &mdl,
			md_gost94, impl);
	if(0 != memcmp(tcs[t].bRawKey, bDerive, mdl)) {
	    fflush(NULL);
	    fprintf(stderr, "Engine test t=%d "
	    		"derive key error.\n", t);
	    if(!ignore) {
		return 12;
	    }
	}
	if(ullMaxLen < tcs[t].ullLen) {
	    continue;
	}
	memset(bTest, 0xa5, sizeof(bTest));
	memset(bTest1, 0x5a, sizeof(bTest1));
	if(!ENGINE_ctrl_cmd_string(impl,
		    "CRYPT_PARAMS",
		    tcs[t].szParamSet, 0)) {
	    fflush(NULL);
	    fprintf(stderr, "ENGINE_ctrl_cmd_string: %s\n",
		    ERR_error_string(ERR_get_error(), NULL));
	    return 11;
	}
	switch(tcs[t].gMode) {
	case G89_ECB:
	    /* OpenSSL/ccgost not implemented GOST 28147-89 ECB */
	    continue;
	case G89_CFB:
	    ctype = cp_g89cfb;
	    goto engine_cipher_check;
	case G89_CNT:
	    if(0 != strcmp("id-Gost28147-89-CryptoPro-A-ParamSet",
					    tcs[t].szParamSet)) {
		/*
		 * ccgost engine can't change S-Box for gost-cnt
		 */
		continue;
	    }
	    ctype = cp_g89cnt;
engine_cipher_check:
	    EVP_CIPHER_CTX_init(ectx);
	    EVP_EncryptInit_ex(ectx, ctype, impl,
				    bDerive, tcs[t].bIV);
	    if(G89_MAX_TC_LEN >= tcs[t].ullLen) {
		enlu = sizeof(bTest);
		EVP_EncryptUpdate(ectx, bTest, &enlu,
				    tcs[t].bIn, (int)tcs[t].ullLen);
		l = (size_t)tcs[t].ullLen;
	    } else {
		for(ullLeft = tcs[t].ullLen;
			    ullLeft >= sizeof(bZB);
				    ullLeft -= sizeof(bZB)) {
		    printf("B");
		    fflush(NULL);
		    enlu = sizeof(bTS);
		    EVP_EncryptUpdate(ectx, bTS, &enlu,
					    bZB, sizeof(bZB));
		}
		printf("b%llu/%llu", ullLeft, tcs[t].ullLen);
		fflush(NULL);
		EVP_EncryptUpdate(ectx, bTS, &enlu,
					bZB, (int)ullLeft);
		memcpy(bTest, &bTS[enlu-16], 16);
		enlu = (int)tcs[t].ullLen;
		l = 16;
	    }
	    enlf = sizeof(bTest1);
	    EVP_EncryptFinal_ex(ectx, bTest1, &enlf);
	    EVP_CIPHER_CTX_cleanup(ectx);
	    break;
	case G89_IMIT:
	    if(0 != strcmp("id-Gost28147-89-CryptoPro-A-ParamSet",
					    tcs[t].szParamSet)) {
		/*
		 * ccgost engine can't change S-Box for gost-mac
		 */
		continue;
	    }
	    EVP_MD_CTX_init(mctx);
	    mac_key = EVP_PKEY_new_mac_key(
				NID_id_Gost28147_89_MAC, NULL,
				bDerive, mdl);
            EVP_DigestSignInit(mctx, NULL,
				    md_g89imit, impl, mac_key);
	    if(G89_MAX_TC_LEN >= tcs[t].ullLen) {
		EVP_DigestSignUpdate(mctx, tcs[t].bIn,
				    (unsigned int)tcs[t].ullLen);
	    } else {
		for(ullLeft = tcs[t].ullLen;
			    ullLeft >= sizeof(bZB);
				    ullLeft -= sizeof(bZB)) {
		    printf("B");
		    fflush(NULL);
		    EVP_DigestSignUpdate(mctx, bZB, sizeof(bZB));
		}
		printf("b%llu/%llu", ullLeft, tcs[t].ullLen);
		fflush(NULL);
		EVP_DigestSignUpdate(mctx, bZB,
					(unsigned int)ullLeft);
	    }
	    siglen = 4;
	    EVP_DigestSignFinal(mctx, bTest, &siglen);
	    EVP_MD_CTX_free(mctx);
	    enlu = (int)tcs[t].ullLen;
	    enlf = 0;
	    l = siglen;
	    break;
	}
	if((int)tcs[t].ullLen != enlu || 0 != enlf ||
	   0 != memcmp(tcs[t].bOut, bTest, l)) {
	    fflush(NULL);
	    fprintf(stderr, "\nEngine test t=%d len=%llu "
			    "failed.\n", t, tcs[t].ullLen);
	    if(!ignore) {
	    	return 13;
	    }
	} else {
	    printf(".");
	    fflush(NULL);
	}
    }

    printf(" passed\n");
    fflush(NULL);

    return EXIT_SUCCESS;
}
