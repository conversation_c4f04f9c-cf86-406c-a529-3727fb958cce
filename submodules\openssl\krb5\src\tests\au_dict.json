{"event_name": "", "event_success": 0, "evidence_tkt_id": "", "fromport": 0, "fromaddr": {"type": 0, "length": 0, "ip": []}, "kdc_status": "", "rep_etype": 0, "rep.ticket": {"authtime": 0, "cname": {"components": [], "realm": "", "length": 0, "type": 0}, "end": 0, "flags": 0, "sess_etype": 0, "srv_etype": 0, "sname": {"components": [], "realm": "", "length": 0, "type": 0}}, "req.avail_etypes": [], "req.client": {"components": [], "realm": "", "length": 0, "type": 0}, "req_id": "", "req.kdc_options": 0, "req.pa_type": [], "req.server": {"components": [], "realm": "", "length": 0, "type": 0}, "req.tkt_end": 0, "s4u2proxy_user": {"components": [], "realm": "", "length": 0, "type": 0}, "s4u2self_user": {"components": [], "realm": "", "length": 0, "type": 0}, "stage": 1, "tkt_in_id": "", "tkt_renewed": 0, "tkt_out_id": "", "tkt_validated": 0, "u2u_user": {"components": [], "realm": "", "length": 0, "type": 0}, "violation": 0}