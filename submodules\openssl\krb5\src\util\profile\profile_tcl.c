/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 1.3.40
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

#define SWIGTCL

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#  ifndef GCC_HASCLASSVISIBILITY
#    define GCC_HASCLASSVISIBILITY
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif



#include <stdio.h>
#include <tcl.h>
#include <errno.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctype.h>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return and integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows to return the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
#define SWIG_ERROR                 (-1)
#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporal objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  int equiv = 0;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (!equiv && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = (SWIG_TypeNameComp(nb, ne, tb, te) == 0) ? 1 : 0;
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCompare(const char *nb, const char *tb) {
  int equiv = 0;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (!equiv && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = (SWIG_TypeNameComp(nb, ne, tb, te) == 0) ? 1 : 0;
    if (*ne) ++ne;
  }
  return equiv;
}


/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  We choose
     to print the last name, as it is often (?) the most
     specific. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = ((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = ((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/*  Errors in SWIG */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13



/* -----------------------------------------------------------------------------
 * error manipulation
 * ----------------------------------------------------------------------------- */

SWIGINTERN const char*
SWIG_Tcl_ErrorType(int code) {
  const char* type = 0;
  switch(code) {
  case SWIG_MemoryError:
    type = "MemoryError";
    break;
  case SWIG_IOError:
    type = "IOError";
    break;
  case SWIG_RuntimeError:
    type = "RuntimeError";
    break;
  case SWIG_IndexError:
    type = "IndexError";
    break;
  case SWIG_TypeError:
    type = "TypeError";
    break;
  case SWIG_DivisionByZero:
    type = "ZeroDivisionError";
    break;
  case SWIG_OverflowError:
    type = "OverflowError";
    break;
  case SWIG_SyntaxError:
    type = "SyntaxError";
    break;
  case SWIG_ValueError:
    type = "ValueError";
    break;
  case SWIG_SystemError:
    type = "SystemError";
    break;
  case SWIG_AttributeError:
    type = "AttributeError";
    break;
  default:
    type = "RuntimeError";
  }
  return type;
}


SWIGINTERN void
SWIG_Tcl_SetErrorObj(Tcl_Interp *interp, const char *ctype, Tcl_Obj *obj)
{
  Tcl_ResetResult(interp);
  Tcl_SetObjResult(interp, obj);
  Tcl_SetErrorCode(interp, "SWIG", ctype, NULL);
}

SWIGINTERN void
SWIG_Tcl_SetErrorMsg(Tcl_Interp *interp, const char *ctype, const char *mesg)
{
  Tcl_ResetResult(interp);
  Tcl_SetErrorCode(interp, "SWIG", ctype, NULL);
  Tcl_AppendResult(interp, ctype, " ", mesg, NULL);
  /*
  Tcl_AddErrorInfo(interp, ctype);
  Tcl_AddErrorInfo(interp, " ");
  Tcl_AddErrorInfo(interp, mesg);
  */
}

SWIGINTERNINLINE void
SWIG_Tcl_AddErrorMsg(Tcl_Interp *interp, const char* mesg)
{
  Tcl_AddErrorInfo(interp, mesg);
}



/* -----------------------------------------------------------------------------
 * SWIG API. Portion that goes into the runtime
 * ----------------------------------------------------------------------------- */
#ifdef __cplusplus
extern "C" {
#endif

/* -----------------------------------------------------------------------------
 * Constant declarations
 * ----------------------------------------------------------------------------- */

/* Constant Types */
#define SWIG_TCL_POINTER 4
#define SWIG_TCL_BINARY  5

/* Constant information structure */
typedef struct swig_const_info {
    int type;
    char *name;
    long lvalue;
    double dvalue;
    void   *pvalue;
    swig_type_info **ptype;
} swig_const_info;

typedef int   (*swig_wrapper)(ClientData, Tcl_Interp *, int, Tcl_Obj *CONST []);
typedef int   (*swig_wrapper_func)(ClientData, Tcl_Interp *, int, Tcl_Obj *CONST []);
typedef char *(*swig_variable_func)(ClientData, Tcl_Interp *, char *, char *, int);
typedef void  (*swig_delete_func)(ClientData);

typedef struct swig_method {
  const char     *name;
  swig_wrapper   method;
} swig_method;

typedef struct swig_attribute {
  const char     *name;
  swig_wrapper   getmethod;
  swig_wrapper   setmethod;
} swig_attribute;

typedef struct swig_class {
  const char         *name;
  swig_type_info   **type;
  swig_wrapper       constructor;
  void              (*destructor)(void *);
  swig_method        *methods;
  swig_attribute     *attributes;
  struct swig_class **bases;
  const char              **base_names;
  swig_module_info   *module;
} swig_class;

typedef struct swig_instance {
  Tcl_Obj       *thisptr;
  void          *thisvalue;
  swig_class   *classptr;
  int            destroy;
  Tcl_Command    cmdtok;
} swig_instance;

/* Structure for command table */
typedef struct {
  const char *name;
  int       (*wrapper)(ClientData, Tcl_Interp *, int, Tcl_Obj *CONST []);
  ClientData  clientdata;
} swig_command_info;

/* Structure for variable linking table */
typedef struct {
  const char *name;
  void *addr;
  char * (*get)(ClientData, Tcl_Interp *, char *, char *, int);
  char * (*set)(ClientData, Tcl_Interp *, char *, char *, int);
} swig_var_info;


/* -----------------------------------------------------------------------------*
 *  Install a constant object
 * -----------------------------------------------------------------------------*/

static Tcl_HashTable   swigconstTable;
static int             swigconstTableinit = 0;

SWIGINTERN void
SWIG_Tcl_SetConstantObj(Tcl_Interp *interp, const char* name, Tcl_Obj *obj) {
  int newobj;
  Tcl_ObjSetVar2(interp,Tcl_NewStringObj(name,-1), NULL, obj, TCL_GLOBAL_ONLY);
  Tcl_SetHashValue(Tcl_CreateHashEntry(&swigconstTable, name, &newobj), (ClientData) obj);
}

SWIGINTERN Tcl_Obj *
SWIG_Tcl_GetConstantObj(const char *key) {
  Tcl_HashEntry *entryPtr;
  if (!swigconstTableinit) return 0;
  entryPtr = Tcl_FindHashEntry(&swigconstTable, key);
  if (entryPtr) {
    return (Tcl_Obj *) Tcl_GetHashValue(entryPtr);
  }
  return 0;
}

#ifdef __cplusplus
}
#endif



/* -----------------------------------------------------------------------------
 * See the LICENSE file for information on copyright, usage and redistribution
 * of SWIG, and the README file for authors - http://www.swig.org/release.html.
 *
 * tclrun.swg
 *
 * This file contains the runtime support for Tcl modules and includes
 * code for managing global variables and pointer type checking.
 * ----------------------------------------------------------------------------- */

/* Common SWIG API */

/* for raw pointers */
#define SWIG_ConvertPtr(oc, ptr, ty, flags)             SWIG_Tcl_ConvertPtr(interp, oc, ptr, ty, flags)
#define SWIG_NewPointerObj(ptr, type, flags)            SWIG_Tcl_NewPointerObj(ptr, type, flags)

/* for raw packed data */
#define SWIG_ConvertPacked(obj, ptr, sz, ty)            SWIG_Tcl_ConvertPacked(interp, obj, ptr, sz, ty)
#define SWIG_NewPackedObj(ptr, sz, type)                SWIG_Tcl_NewPackedObj(ptr, sz, type)

/* for class or struct pointers */
#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_Tcl_ConvertPtr(interp, obj, pptr, type, flags)
#define SWIG_NewInstanceObj(thisvalue, type, flags)     SWIG_Tcl_NewInstanceObj(interp, thisvalue, type, flags)

/* for C or C++ function pointers */
#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_Tcl_ConvertPtr(interp, obj, pptr, type, 0)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_Tcl_NewPointerObj(ptr, type, 0)

/* for C++ member pointers, ie, member methods */
#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_Tcl_ConvertPacked(interp,obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_Tcl_NewPackedObj(ptr, sz, type)


/* Runtime API */

#define SWIG_GetModule(clientdata)                      SWIG_Tcl_GetModule((Tcl_Interp *) (clientdata))
#define SWIG_SetModule(clientdata, pointer)          	SWIG_Tcl_SetModule((Tcl_Interp *) (clientdata), pointer)


/* Error manipulation */

#define SWIG_ErrorType(code)                            SWIG_Tcl_ErrorType(code)
#define SWIG_Error(code, msg)            		SWIG_Tcl_SetErrorMsg(interp, SWIG_Tcl_ErrorType(code), msg)
#define SWIG_fail                        		goto fail


/* Tcl-specific SWIG API */

#define SWIG_Acquire(ptr)                               SWIG_Tcl_Acquire(ptr)
#define SWIG_MethodCommand                           	SWIG_Tcl_MethodCommand
#define SWIG_Disown(ptr)                             	SWIG_Tcl_Disown(ptr)
#define SWIG_ConvertPtrFromString(c, ptr, ty, flags) 	SWIG_Tcl_ConvertPtrFromString(interp, c, ptr, ty, flags)
#define SWIG_MakePtr(c, ptr, ty, flags)              	SWIG_Tcl_MakePtr(c, ptr, ty, flags)
#define SWIG_PointerTypeFromString(c)                	SWIG_Tcl_PointerTypeFromString(c)
#define SWIG_GetArgs                                 	SWIG_Tcl_GetArgs
#define SWIG_GetConstantObj(key)                     	SWIG_Tcl_GetConstantObj(key)
#define SWIG_ObjectConstructor                       	SWIG_Tcl_ObjectConstructor
#define SWIG_Thisown(ptr)                            	SWIG_Tcl_Thisown(ptr)
#define SWIG_ObjectDelete                            	SWIG_Tcl_ObjectDelete


#define SWIG_TCL_DECL_ARGS_2(arg1, arg2)                (Tcl_Interp *interp SWIGUNUSED, arg1, arg2)
#define SWIG_TCL_CALL_ARGS_2(arg1, arg2)                (interp, arg1, arg2)
/* -----------------------------------------------------------------------------
 * pointers/data manipulation
 * ----------------------------------------------------------------------------- */

/* For backward compatibility only */
#define SWIG_POINTER_EXCEPTION  0
#define SWIG_GetConstant        SWIG_GetConstantObj
#define SWIG_Tcl_GetConstant    SWIG_Tcl_GetConstantObj

#include "assert.h"

#ifdef __cplusplus
extern "C" {
#if 0
} /* cc-mode */
#endif
#endif

/* Object support */

SWIGRUNTIME Tcl_HashTable*
SWIG_Tcl_ObjectTable(void) {
  static Tcl_HashTable  swigobjectTable;
  static int            swigobjectTableinit = 0;
  if (!swigobjectTableinit) {
    Tcl_InitHashTable(&swigobjectTable, TCL_ONE_WORD_KEYS);
    swigobjectTableinit = 1;
  }
  return &swigobjectTable;
}

/* Acquire ownership of a pointer */
SWIGRUNTIME void
SWIG_Tcl_Acquire(void *ptr) {
  int newobj;
  Tcl_CreateHashEntry(SWIG_Tcl_ObjectTable(), (char *) ptr, &newobj);
}

SWIGRUNTIME int
SWIG_Tcl_Thisown(void *ptr) {
  if (Tcl_FindHashEntry(SWIG_Tcl_ObjectTable(), (char *) ptr)) {
    return 1;
  }
  return 0;
}

/* Disown a pointer.  Returns 1 if we owned it to begin with */
SWIGRUNTIME int
SWIG_Tcl_Disown(void *ptr) {
  Tcl_HashEntry *entryPtr = Tcl_FindHashEntry(SWIG_Tcl_ObjectTable(), (char *) ptr);
  if (entryPtr) {
    Tcl_DeleteHashEntry(entryPtr);
    return 1;
  }
  return 0;
}

/* Convert a pointer value */
SWIGRUNTIME int
SWIG_Tcl_ConvertPtrFromString(Tcl_Interp *interp, const char *c, void **ptr, swig_type_info *ty, int flags) {
  swig_cast_info *tc;
  /* Pointer values must start with leading underscore */
  while (*c != '_') {
    *ptr = (void *) 0;
    if (strcmp(c,"NULL") == 0) return SWIG_OK;

    /* Empty string: not a pointer */
    if (*c == 0) return SWIG_ERROR;

    /* Hmmm. It could be an object name. */

    /* Check if this is a command at all. Prevents <c> cget -this         */
    /* from being called when c is not a command, firing the unknown proc */
    if (Tcl_VarEval(interp,"info commands ", c, (char *) NULL) == TCL_OK) {
      Tcl_Obj *result = Tcl_GetObjResult(interp);
      if (*(Tcl_GetStringFromObj(result, NULL)) == 0) {
        /* It's not a command, so it can't be a pointer */
        Tcl_ResetResult(interp);
        return SWIG_ERROR;
      }
    } else {
      /* This will only fail if the argument is multiple words. */
      /* Multiple words are also not commands.                  */
      Tcl_ResetResult(interp);
      return SWIG_ERROR;
    }

    /* Check if this is really a SWIG pointer */
    if (Tcl_VarEval(interp,c," cget -this", (char *) NULL) != TCL_OK) {
      Tcl_ResetResult(interp);
      return SWIG_ERROR;
    }

    c = Tcl_GetStringFromObj(Tcl_GetObjResult(interp), NULL);
  }

  c++;
  c = SWIG_UnpackData(c,ptr,sizeof(void *));
  if (ty) {
    tc = c ? SWIG_TypeCheck(c,ty) : 0;
    if (!tc) {
      return SWIG_ERROR;
    }
    if (flags & SWIG_POINTER_DISOWN) {
      SWIG_Disown((void *) *ptr);
    }
    {
      int newmemory = 0;
      *ptr = SWIG_TypeCast(tc,(void *) *ptr,&newmemory);
      assert(!newmemory); /* newmemory handling not yet implemented */
    }
  }
  return SWIG_OK;
}

/* Convert a pointer value */
SWIGRUNTIMEINLINE int
SWIG_Tcl_ConvertPtr(Tcl_Interp *interp, Tcl_Obj *oc, void **ptr, swig_type_info *ty, int flags) {
  return SWIG_Tcl_ConvertPtrFromString(interp, Tcl_GetStringFromObj(oc,NULL), ptr, ty, flags);
}

/* Convert a pointer value */
SWIGRUNTIME char *
SWIG_Tcl_PointerTypeFromString(char *c) {
  char d;
  /* Pointer values must start with leading underscore. NULL has no type */
  if (*c != '_') {
    return 0;
  }
  c++;
  /* Extract hex value from pointer */
  while ((d = *c)) {
    if (!(((d >= '0') && (d <= '9')) || ((d >= 'a') && (d <= 'f')))) break;
    c++;
  }
  return c;
}

/* Convert a packed value value */
SWIGRUNTIME int
SWIG_Tcl_ConvertPacked(Tcl_Interp *SWIGUNUSEDPARM(interp) , Tcl_Obj *obj, void *ptr, int sz, swig_type_info *ty) {
  swig_cast_info *tc;
  const char  *c;

  if (!obj) goto type_error;
  c = Tcl_GetStringFromObj(obj,NULL);
  /* Pointer values must start with leading underscore */
  if (*c != '_') goto type_error;
  c++;
  c = SWIG_UnpackData(c,ptr,sz);
  if (ty) {
    tc = SWIG_TypeCheck(c,ty);
    if (!tc) goto type_error;
  }
  return SWIG_OK;

 type_error:

  return SWIG_ERROR;
}


/* Take a pointer and convert it to a string */
SWIGRUNTIME void
SWIG_Tcl_MakePtr(char *c, void *ptr, swig_type_info *ty, int flags) {
  if (ptr) {
    *(c++) = '_';
    c = SWIG_PackData(c,&ptr,sizeof(void *));
    strcpy(c,ty->name);
  } else {
    strcpy(c,(char *)"NULL");
  }
  flags = 0;
}

/* Create a new pointer object */
SWIGRUNTIMEINLINE Tcl_Obj *
SWIG_Tcl_NewPointerObj(void *ptr, swig_type_info *type, int flags) {
  Tcl_Obj *robj;
  char result[SWIG_BUFFER_SIZE];
  SWIG_MakePtr(result,ptr,type,flags);
  robj = Tcl_NewStringObj(result,-1);
  return robj;
}

SWIGRUNTIME Tcl_Obj *
SWIG_Tcl_NewPackedObj(void *ptr, int sz, swig_type_info *type) {
  char result[1024];
  char *r = result;
  if ((2*sz + 1 + strlen(type->name)) > 1000) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  strcpy(r,type->name);
  return Tcl_NewStringObj(result,-1);
}

/* -----------------------------------------------------------------------------*
 *  Get type list
 * -----------------------------------------------------------------------------*/

SWIGRUNTIME swig_module_info *
SWIG_Tcl_GetModule(Tcl_Interp *interp) {
  const char *data;
  swig_module_info *ret = 0;

  /* first check if pointer already created */
  data = Tcl_GetVar(interp, (char *)"swig_runtime_data_type_pointer" SWIG_RUNTIME_VERSION SWIG_TYPE_TABLE_NAME, TCL_GLOBAL_ONLY);
  if (data) {
    SWIG_UnpackData(data, &ret, sizeof(swig_type_info **));
  }

  return ret;
}

SWIGRUNTIME void
SWIG_Tcl_SetModule(Tcl_Interp *interp, swig_module_info *module) {
  char buf[SWIG_BUFFER_SIZE];
  char *data;

  /* create a new pointer */
  data = SWIG_PackData(buf, &module, sizeof(swig_type_info **));
  *data = 0;
  Tcl_SetVar(interp, (char *)"swig_runtime_data_type_pointer" SWIG_RUNTIME_VERSION SWIG_TYPE_TABLE_NAME, buf, 0);
}

/* -----------------------------------------------------------------------------*
 *  Object auxiliars
 * -----------------------------------------------------------------------------*/


SWIGRUNTIME void
SWIG_Tcl_ObjectDelete(ClientData clientData) {
  swig_instance *si = (swig_instance *) clientData;
  if ((si) && (si->destroy) && (SWIG_Disown(si->thisvalue))) {
    if (si->classptr->destructor) {
      (si->classptr->destructor)(si->thisvalue);
    }
  }
  Tcl_DecrRefCount(si->thisptr);
  free(si);
}

/* Function to invoke object methods given an instance */
SWIGRUNTIME int
SWIG_Tcl_MethodCommand(ClientData clientData, Tcl_Interp *interp, int objc, Tcl_Obj *CONST _objv[]) {
  char *method,   *attrname;
  swig_instance   *inst = (swig_instance *) clientData;
  swig_method     *meth;
  swig_attribute  *attr;
  Tcl_Obj         *oldarg;
  Tcl_Obj         **objv;
  int              rcode;
  swig_class      *cls;
  swig_class      *cls_stack[64];
  int              cls_stack_bi[64];
  int              cls_stack_top = 0;
  int              numconf = 2;
  int              bi;

  objv = (Tcl_Obj **) _objv;
  if (objc < 2) {
    Tcl_SetResult(interp, (char *) "wrong # args.", TCL_STATIC);
    return TCL_ERROR;
  }
  method = Tcl_GetStringFromObj(objv[1],NULL);
  if (strcmp(method,"-acquire") == 0) {
    inst->destroy = 1;
    SWIG_Acquire(inst->thisvalue);
    return TCL_OK;
  }
  if (strcmp(method,"-disown") == 0) {
    if (inst->destroy) {
      SWIG_Disown(inst->thisvalue);
    }
    inst->destroy = 0;
    return TCL_OK;
  }
  if (strcmp(method,"-delete") == 0) {
    Tcl_DeleteCommandFromToken(interp,inst->cmdtok);
    return TCL_OK;
  }
  cls_stack[cls_stack_top] = inst->classptr;
  cls_stack_bi[cls_stack_top] = -1;
  cls = inst->classptr;
  while (1) {
    bi = cls_stack_bi[cls_stack_top];
    cls = cls_stack[cls_stack_top];
    if (bi != -1) {
      if (!cls->bases[bi] && cls->base_names[bi]) {
        /* lookup and cache the base class */
	swig_type_info *info = SWIG_TypeQueryModule(cls->module, cls->module, cls->base_names[bi]);
	if (info) cls->bases[bi] = (swig_class *) info->clientdata;
      }
      cls = cls->bases[bi];
      if (cls) {
        cls_stack_bi[cls_stack_top]++;
        cls_stack_top++;
        cls_stack[cls_stack_top] = cls;
        cls_stack_bi[cls_stack_top] = -1;
        continue;
      }
    }
    if (!cls) {
      cls_stack_top--;
      if (cls_stack_top < 0) break;
      else continue;
    }
    cls_stack_bi[cls_stack_top]++;

    meth = cls->methods;
    /* Check for methods */
    while (meth && meth->name) {
      if (strcmp(meth->name,method) == 0) {
        oldarg = objv[1];
        objv[1] = inst->thisptr;
        Tcl_IncrRefCount(inst->thisptr);
        rcode = (*meth->method)(clientData,interp,objc,objv);
        objv[1] = oldarg;
        Tcl_DecrRefCount(inst->thisptr);
        return rcode;
      }
      meth++;
    }
    /* Check class methods for a match */
    if (strcmp(method,"cget") == 0) {
      if (objc < 3) {
        Tcl_SetResult(interp, (char *) "wrong # args.", TCL_STATIC);
        return TCL_ERROR;
      }
      attrname = Tcl_GetStringFromObj(objv[2],NULL);
      attr = cls->attributes;
      while (attr && attr->name) {
        if ((strcmp(attr->name, attrname) == 0) && (attr->getmethod)) {
          oldarg = objv[1];
          objv[1] = inst->thisptr;
          Tcl_IncrRefCount(inst->thisptr);
          rcode = (*attr->getmethod)(clientData,interp,2, objv);
          objv[1] = oldarg;
          Tcl_DecrRefCount(inst->thisptr);
          return rcode;
        }
        attr++;
      }
      if (strcmp(attrname, "-this") == 0) {
        Tcl_SetObjResult(interp, Tcl_DuplicateObj(inst->thisptr));
        return TCL_OK;
      }
      if (strcmp(attrname, "-thisown") == 0) {
        if (SWIG_Thisown(inst->thisvalue)) {
          Tcl_SetResult(interp,(char*)"1",TCL_STATIC);
        } else {
          Tcl_SetResult(interp,(char*)"0",TCL_STATIC);
        }
        return TCL_OK;
      }
    } else if (strcmp(method, "configure") == 0) {
      int i;
      if (objc < 4) {
        Tcl_SetResult(interp, (char *) "wrong # args.", TCL_STATIC);
        return TCL_ERROR;
      }
      i = 2;
      while (i < objc) {
        attrname = Tcl_GetStringFromObj(objv[i],NULL);
        attr = cls->attributes;
        while (attr && attr->name) {
          if ((strcmp(attr->name, attrname) == 0) && (attr->setmethod)) {
            oldarg = objv[i];
            objv[i] = inst->thisptr;
            Tcl_IncrRefCount(inst->thisptr);
            rcode = (*attr->setmethod)(clientData,interp,3, &objv[i-1]);
            objv[i] = oldarg;
            Tcl_DecrRefCount(inst->thisptr);
            if (rcode != TCL_OK) return rcode;
            numconf += 2;
          }
          attr++;
        }
        i+=2;
      }
    }
  }
  if (strcmp(method,"configure") == 0) {
    if (numconf >= objc) {
      return TCL_OK;
    } else {
      Tcl_SetResult(interp,(char *) "Invalid attribute name.", TCL_STATIC);
      return TCL_ERROR;
    }
  }
  if (strcmp(method,"cget") == 0) {
    Tcl_SetResult(interp,(char *) "Invalid attribute name.", TCL_STATIC);
    return TCL_ERROR;
  }
  Tcl_SetResult(interp, (char *) "Invalid method. Must be one of: configure cget -acquire -disown -delete", TCL_STATIC);
  cls = inst->classptr;
  bi = 0;
  while (cls) {
    meth = cls->methods;
    while (meth && meth->name) {
      char *cr = (char *) Tcl_GetStringResult(interp);
      size_t meth_len = strlen(meth->name);
      char* where = strchr(cr,':');
      while(where) {
        where = strstr(where, meth->name);
        if(where) {
          if(where[-1] == ' ' && (where[meth_len] == ' ' || where[meth_len]==0)) {
            break;
          } else {
            where++;
          }
        }
      }

      if (!where)
        Tcl_AppendElement(interp, (char *) meth->name);
      meth++;
    }
    cls = inst->classptr->bases[bi++];
  }
  return TCL_ERROR;
}

/* This function takes the current result and turns it into an object command */
SWIGRUNTIME Tcl_Obj *
SWIG_Tcl_NewInstanceObj(Tcl_Interp *interp, void *thisvalue, swig_type_info *type, int flags) {
  Tcl_Obj *robj = SWIG_NewPointerObj(thisvalue, type,0);
  /* Check to see if this pointer belongs to a class or not */
  if ((type->clientdata) && (interp)) {
    Tcl_CmdInfo    ci;
    char          *name;
    name = Tcl_GetStringFromObj(robj,NULL);
    if (!Tcl_GetCommandInfo(interp,name, &ci) || (flags)) {
      swig_instance *newinst = (swig_instance *) malloc(sizeof(swig_instance));
      newinst->thisptr = Tcl_DuplicateObj(robj);
      Tcl_IncrRefCount(newinst->thisptr);
      newinst->thisvalue = thisvalue;
      newinst->classptr = (swig_class *) type->clientdata;
      newinst->destroy = flags;
      newinst->cmdtok = Tcl_CreateObjCommand(interp, Tcl_GetStringFromObj(robj,NULL), (swig_wrapper_func) SWIG_MethodCommand, (ClientData) newinst, (swig_delete_func) SWIG_ObjectDelete);
      if (flags) {
        SWIG_Acquire(thisvalue);
      }
    }
  }
  return robj;
}

/* Function to create objects */
SWIGRUNTIME int
SWIG_Tcl_ObjectConstructor(ClientData clientData, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  Tcl_Obj          *newObj = 0;
  void             *thisvalue = 0;
  swig_instance   *newinst = 0;
  swig_class      *classptr = (swig_class *) clientData;
  swig_wrapper     cons = 0;
  char             *name = 0;
  int               firstarg = 0;
  int               thisarg = 0;
  int               destroy = 1;

  if (!classptr) {
    Tcl_SetResult(interp, (char *) "swig: internal runtime error. No class object defined.", TCL_STATIC);
    return TCL_ERROR;
  }
  cons = classptr->constructor;
  if (objc > 1) {
    char *s = Tcl_GetStringFromObj(objv[1],NULL);
    if (strcmp(s,"-this") == 0) {
      thisarg = 2;
      cons = 0;
    } else if (strcmp(s,"-args") == 0) {
      firstarg = 1;
    } else if (objc == 2) {
      firstarg = 1;
      name = s;
    } else if (objc >= 3) {
      char *s1;
      name = s;
      s1 = Tcl_GetStringFromObj(objv[2],NULL);
      if (strcmp(s1,"-this") == 0) {
	thisarg = 3;
	cons = 0;
      } else {
	firstarg = 1;
      }
    }
  }
  if (cons) {
    int result;
    result = (*cons)(0, interp, objc-firstarg, &objv[firstarg]);
    if (result != TCL_OK) {
      return result;
    }
    newObj = Tcl_DuplicateObj(Tcl_GetObjResult(interp));
    if (!name) name = Tcl_GetStringFromObj(newObj,NULL);
  } else if (thisarg > 0) {
    if (thisarg < objc) {
      destroy = 0;
      newObj = Tcl_DuplicateObj(objv[thisarg]);
      if (!name) name = Tcl_GetStringFromObj(newObj,NULL);
    } else {
      Tcl_SetResult(interp, (char *) "wrong # args.", TCL_STATIC);
      return TCL_ERROR;
    }
  } else {
    Tcl_SetResult(interp, (char *) "No constructor available.", TCL_STATIC);
    return TCL_ERROR;
  }
  if (SWIG_Tcl_ConvertPtr(interp,newObj, (void **) &thisvalue, *(classptr->type), 0) != SWIG_OK) {
    Tcl_DecrRefCount(newObj);
    return TCL_ERROR;
  }
  newinst = (swig_instance *) malloc(sizeof(swig_instance));
  newinst->thisptr = newObj;
  Tcl_IncrRefCount(newObj);
  newinst->thisvalue = thisvalue;
  newinst->classptr = classptr;
  newinst->destroy = destroy;
  if (destroy) {
    SWIG_Acquire(thisvalue);
  }
  newinst->cmdtok = Tcl_CreateObjCommand(interp,name, (swig_wrapper) SWIG_MethodCommand, (ClientData) newinst, (swig_delete_func) SWIG_ObjectDelete);
  return TCL_OK;
}

/* -----------------------------------------------------------------------------*
 *   Get arguments
 * -----------------------------------------------------------------------------*/
SWIGRUNTIME int
SWIG_Tcl_GetArgs(Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[], const char *fmt, ...) {
  int        argno = 0, opt = 0;
  long       tempi;
  double     tempd;
  const char *c;
  va_list    ap;
  void      *vptr;
  Tcl_Obj   *obj = 0;
  swig_type_info *ty;

  va_start(ap,fmt);
  for (c = fmt; (*c && (*c != ':') && (*c != ';')); c++,argno++) {
    if (*c == '|') {
      opt = 1;
      c++;
    }
    if (argno >= (objc-1)) {
      if (!opt) {
        Tcl_SetResult(interp, (char *) "Wrong number of arguments ", TCL_STATIC);
        goto argerror;
      } else {
        va_end(ap);
        return TCL_OK;
      }
    }

    vptr = va_arg(ap,void *);
    if (vptr) {
      if (isupper(*c)) {
        obj = SWIG_Tcl_GetConstantObj(Tcl_GetStringFromObj(objv[argno+1],0));
        if (!obj) obj = objv[argno+1];
      } else {
        obj = objv[argno+1];
      }
      switch(*c) {
      case 'i': case 'I':
      case 'l': case 'L':
      case 'h': case 'H':
      case 'b': case 'B':
        if (Tcl_GetLongFromObj(interp,obj,&tempi) != TCL_OK) goto argerror;
        if ((*c == 'i') || (*c == 'I')) *((int *)vptr) = (int)tempi;
        else if ((*c == 'l') || (*c == 'L')) *((long *)vptr) = (long)tempi;
        else if ((*c == 'h') || (*c == 'H')) *((short*)vptr) = (short)tempi;
        else if ((*c == 'b') || (*c == 'B')) *((unsigned char *)vptr) = (unsigned char)tempi;
        break;
      case 'f': case 'F':
      case 'd': case 'D':
        if (Tcl_GetDoubleFromObj(interp,obj,&tempd) != TCL_OK) goto argerror;
        if ((*c == 'f') || (*c == 'F')) *((float *) vptr) = (float)tempd;
        else if ((*c == 'd') || (*c == 'D')) *((double*) vptr) = tempd;
        break;
      case 's': case 'S':
        if (*(c+1) == '#') {
          int *vlptr = (int *) va_arg(ap, void *);
          *((char **) vptr) = Tcl_GetStringFromObj(obj, vlptr);
          c++;
        } else {
          *((char **)vptr) = Tcl_GetStringFromObj(obj,NULL);
        }
        break;
      case 'c': case 'C':
        *((char *)vptr) = *(Tcl_GetStringFromObj(obj,NULL));
        break;
      case 'p': case 'P':
        ty = (swig_type_info *) va_arg(ap, void *);
        if (SWIG_Tcl_ConvertPtr(interp, obj, (void **) vptr, ty, 0) != SWIG_OK) goto argerror;
        break;
      case 'o': case 'O':
        *((Tcl_Obj **)vptr) = objv[argno+1];
        break;
      default:
        break;
      }
    }
  }

  if ((*c != ';') && ((objc-1) > argno)) {
    Tcl_SetResult(interp, (char *) "Wrong # args.", TCL_STATIC);
    goto argerror;
  }
  va_end(ap);
  return TCL_OK;

 argerror:
  {
    char temp[32];
    sprintf(temp,"%d", argno+1);
    c = strchr(fmt,':');
    if (!c) c = strchr(fmt,';');
    if (!c) c = (char *)"";
    Tcl_AppendResult(interp,c," argument ", temp, NULL);
    va_end(ap);
    return TCL_ERROR;
  }
}

#ifdef __cplusplus
#if 0
{ /* cc-mode */
#endif
}
#endif



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0)

#define SWIG_contract_assert(expr, msg) if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } else



/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p__profile_t swig_types[0]
#define SWIGTYPE_p_char swig_types[1]
#define SWIGTYPE_p_int swig_types[2]
#define SWIGTYPE_p_long swig_types[3]
#define SWIGTYPE_p_p__profile_t swig_types[4]
#define SWIGTYPE_p_p_char swig_types[5]
#define SWIGTYPE_p_p_p_char swig_types[6]
#define SWIGTYPE_p_p_p_void swig_types[7]
#define SWIGTYPE_p_p_void swig_types[8]
static swig_type_info *swig_types[10];
static swig_module_info swig_module = {swig_types, 9, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */

#define SWIG_init    Profile_Init
#define SWIG_name    "profile"
#define SWIG_prefix  ""
#define SWIG_version "0.0"

#define SWIGVERSION 0x010340
#define SWIG_VERSION SWIGVERSION


#define SWIG_as_voidptr(a) (void *)((const void *)(a))
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),(void**)(a))



#ifdef __cplusplus
extern "C" {
#endif
#ifdef MAC_TCL
#pragma export on
#endif
SWIGEXPORT int SWIG_init(Tcl_Interp *);
#ifdef MAC_TCL
#pragma export off
#endif
#ifdef __cplusplus
}
#endif

/* Compatibility version for TCL stubs */
#ifndef SWIG_TCL_STUBS_VERSION
#define SWIG_TCL_STUBS_VERSION "8.1"
#endif



/*
 * Copyright 2004, 2008 by the Massachusetts Institute of Technology.
 * All Rights Reserved.
 *
 * Export of this software from the United States of America may
 *   require a specific license from the United States Government.
 *   It is the responsibility of any person or organization contemplating
 *   export to obtain such a license before exporting.
 *
 * WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 * distribute this software and its documentation for any purpose and
 * without fee is hereby granted, provided that the above copyright
 * notice appear in all copies and that both that copyright notice and
 * this permission notice appear in supporting documentation, and that
 * the name of M.I.T. not be used in advertising or publicity pertaining
 * to distribution of the software without specific, written prior
 * permission.  Furthermore if you modify this software you must label
 * your software as modified software and not distribute it in such a
 * fashion that it might be confused with the original M.I.T. software.
 * M.I.T. makes no representations about the suitability of
 * this software for any purpose.  It is provided "as is" without express
 * or implied warranty.
 *
 *
 * Input for wrapper generator program SWIG for profile routines.
 */
#include <errno.h>
#include "com_err.h"
#include "profile.h"

#ifdef SWIGTCL
/* Reduce warnings about cast discarding const to just this one, from
   every SWIG-generated call to Tcl_SetResult.  */
static void my_tcl_setresult(Tcl_Interp *i, const char *str, Tcl_FreeProc *f)
{
    Tcl_SetResult(i, (char *) str, f);
}
#undef Tcl_SetResult
#define Tcl_SetResult my_tcl_setresult
#endif


typedef struct {
    void *handle;
    char **args;
} *iter_t;


SWIGINTERN int
SWIG_AsCharPtrAndSize(Tcl_Obj *obj, char** cptr, size_t* psize, int *alloc)
{
  int len = 0;
  char *cstr = Tcl_GetStringFromObj(obj, &len);
  if (cstr) {
    if (cptr)  *cptr = cstr;
    if (psize) *psize = len + 1;
    if (alloc) *alloc = SWIG_OLDOBJ;
    return SWIG_OK;
  }
  return SWIG_TypeError;
}





#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


SWIGINTERN int
SWIG_AsVal_long SWIG_TCL_DECL_ARGS_2(Tcl_Obj *obj, long* val)
{
  long v;
  if (Tcl_GetLongFromObj(0,obj, &v) == TCL_OK) {
    if (val) *val = (long) v;
    return SWIG_OK;
  }
  return SWIG_TypeError;
}


SWIGINTERN int
SWIG_AsVal_int SWIG_TCL_DECL_ARGS_2(Tcl_Obj * obj, int *val)
{
  long v;
  int res = SWIG_AsVal_long SWIG_TCL_CALL_ARGS_2(obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v < INT_MIN || v > INT_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = (int)(v);
    }
  }
  return res;
}


SWIGINTERNINLINE Tcl_Obj*
SWIG_From_long  (long value)
{
  if (((long) INT_MIN <= value) && (value <= (long) INT_MAX)) {
    return Tcl_NewIntObj((int)(value));
  } else {
    return Tcl_NewLongObj(value);
  }
}


SWIGINTERNINLINE Tcl_Obj *
SWIG_From_int  (int value)
{
  return SWIG_From_long  (value);
}


static void iter_free(iter_t it)
{
    int i;

    for (i = 0; it->args != NULL && it->args[i] != NULL; i++)
	free(it->args[i]);
    free(it->args);
    profile_iterator_free(&it->handle);
    free(it);
}

static errcode_t iter_create(profile_t p, const char **nullterm,
			     int flags, iter_t *OUTPUT)
{
    iter_t it;
    errcode_t err;

    it = malloc(sizeof(*it));
    if (it == NULL)
	return ENOMEM;
    {
	/* The current implementation requires that the names be kept around
	 * for the lifetime of the iterator. */
	int count, j;
	for (count = 0; nullterm[count]; count++) ;
	it->args = calloc(count + 1, sizeof(*it->args));
	if (it->args == NULL)
	    return ENOMEM;
	for (j = 0; j < count; j++) {
	    it->args[j] = strdup(nullterm[j]);
	    if (it->args[j] == NULL)
		return ENOMEM;
	}
	it->args[j] = NULL;
    }
    err = profile_iterator_create(p, (const char **)it->args, flags,
				  &it->handle);
    if (err)
	iter_free(it);
    else
	*OUTPUT = it;
    return err;
}


/* A TCL_AppInit() function that lets you build a new copy
 * of tclsh.
 *
 * The macro SWIG_init contains the name of the initialization
 * function in the wrapper file.
 */

#ifndef SWIG_RcFileName
char *SWIG_RcFileName = "~/.myapprc";
#endif


#ifdef MAC_TCL
extern int		MacintoshInit _ANSI_ARGS_((void));
#endif

int Tcl_AppInit(Tcl_Interp *interp){

  if (Tcl_Init(interp) == TCL_ERROR)
    return TCL_ERROR;

  /* Now initialize our functions */

  if (SWIG_init(interp) == TCL_ERROR)
    return TCL_ERROR;
#if TCL_MAJOR_VERSION > 7 || TCL_MAJOR_VERSION == 7 && TCL_MINOR_VERSION >= 5
   Tcl_SetVar(interp, (char *) "tcl_rcFileName",SWIG_RcFileName,TCL_GLOBAL_ONLY);
#else
   tcl_RcFileName = SWIG_RcFileName;
#endif
#ifdef SWIG_RcRsrcName
  Tcl_SetVar(interp, (char *) "tcl_rcRsrcName",SWIG_RcRsrcName,TCL_GLOBAL);
#endif

  return TCL_OK;
}

#if TCL_MAJOR_VERSION > 7 || TCL_MAJOR_VERSION == 7 && TCL_MINOR_VERSION >= 4
int main(int argc, char **argv) {
#ifdef MAC_TCL
    char *newArgv[2];

    if (MacintoshInit()  != TCL_OK) {
	Tcl_Exit(1);
    }

    argc = 1;
    newArgv[0] = "tclsh";
    newArgv[1] = NULL;
    argv = newArgv;
#endif

  Tcl_Main(argc, argv, Tcl_AppInit);
  return(0);

}
#else
extern int main();
#endif


#ifdef __cplusplus
extern "C" {
#endif
SWIGINTERN int
_wrap_profile_init_path(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  char *arg1 = (char *) NULL ;
  profile_t *arg2 = (profile_t *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  profile_t tmp2 ;
  errcode_t result;

  {
    /*generic swigtype hack*/ arg2 = &tmp2;
  }
  if (SWIG_GetArgs(interp, objc, objv,"|o:profile_init_path ?path? ",(void *)0) == TCL_ERROR) SWIG_fail;
  if (objc > 1) {
    res1 = SWIG_AsCharPtrAndSize(objv[1], &buf1, NULL, &alloc1);
    if (!SWIG_IsOK(res1)) {
      SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_init_path" "', argument " "1"" of type '" "char const *""'");
    }
    arg1 = (char *)(buf1);
  }
  result = (errcode_t)profile_init_path((char const *)arg1,arg2);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  /*generic swigtype hack*/ Tcl_SetObjResult(interp,SWIG_NewInstanceObj((void *) *arg2, SWIGTYPE_p__profile_t,0));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return TCL_OK;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_init(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  char **arg1 = (char **) NULL ;
  profile_t *arg2 = (profile_t *) 0 ;
  profile_t tmp2 ;
  errcode_t result;

  {
    /*generic swigtype hack*/ arg2 = &tmp2;
  }
  if (SWIG_GetArgs(interp, objc, objv,"|o:profile_init ?nullterm? ",(void *)0) == TCL_ERROR) SWIG_fail;
  if (objc > 1) {
    {
      /* in char **nullterm */
      int n;
      if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[1],NULL), &n, (const char ***) &arg1) == TCL_ERROR) SWIG_fail;
    }
  }
  result = (errcode_t)profile_init((char const **)arg1,arg2);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  /*generic swigtype hack*/ Tcl_SetObjResult(interp,SWIG_NewInstanceObj((void *) *arg2, SWIGTYPE_p__profile_t,0));
  {
    /* freearg char **nullterm */
    if (arg1) {
      Tcl_Free((char *)arg1); arg1 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg1) {
      Tcl_Free((char *)arg1); arg1 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_flush(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"o:profile_flush profile_t ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_flush" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  result = (errcode_t)profile_flush(arg1);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  return TCL_OK;
fail:
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_flush_to_file(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"oo:profile_flush_to_file profile_t path ",(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_flush_to_file" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  res2 = SWIG_AsCharPtrAndSize(objv[2], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "profile_flush_to_file" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = (char *)(buf2);
  result = (errcode_t)profile_flush_to_file(arg1,(char const *)arg2);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return TCL_OK;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_abandon(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;

  if (SWIG_GetArgs(interp, objc, objv,"o:profile_abandon profile_t ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_abandon" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  profile_abandon(arg1);

  return TCL_OK;
fail:
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_release(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;

  if (SWIG_GetArgs(interp, objc, objv,"o:profile_release profile_t ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_release" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  profile_release(arg1);

  return TCL_OK;
fail:
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_values(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char ***arg3 = (char ***) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char **tmp3 ;
  errcode_t result;

  {
    /* in char ***OUTPUT */
    tmp3 = NULL;
    arg3 = &tmp3;
  }
  if (SWIG_GetArgs(interp, objc, objv,"oo:profile_get_values p nullterm ",(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_values" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  result = (errcode_t)profile_get_values(arg1,(char const **)arg2,arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char ***OUTPUT */
    int i;
    for (i = 0; (*arg3)[i]; i++)
    Tcl_AppendElement(interp, (*arg3)[i]);
  }
  profile_free_list(*arg3);
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_string(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char *arg2 = (char *) 0 ;
  char *arg3 = (char *) 0 ;
  char *arg4 = (char *) NULL ;
  char *arg5 = (char *) NULL ;
  char **arg6 = (char **) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  int res4 ;
  char *buf4 = 0 ;
  int alloc4 = 0 ;
  int res5 ;
  char *buf5 = 0 ;
  int alloc5 = 0 ;
  char *tmp6 ;
  errcode_t result;

  {
    /* in char **OUTPUT */
    tmp6 = NULL;
    arg6 = &tmp6;
  }
  if (SWIG_GetArgs(interp, objc, objv,"ooo|oo:profile_get_string p name subname ?subsubname? ?defval? ",(void *)0,(void *)0,(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_string" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  res2 = SWIG_AsCharPtrAndSize(objv[2], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "profile_get_string" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = (char *)(buf2);
  res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_get_string" "', argument " "3"" of type '" "char const *""'");
  }
  arg3 = (char *)(buf3);
  if (objc > 4) {
    res4 = SWIG_AsCharPtrAndSize(objv[4], &buf4, NULL, &alloc4);
    if (!SWIG_IsOK(res4)) {
      SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "profile_get_string" "', argument " "4"" of type '" "char const *""'");
    }
    arg4 = (char *)(buf4);
  }
  if (objc > 5) {
    res5 = SWIG_AsCharPtrAndSize(objv[5], &buf5, NULL, &alloc5);
    if (!SWIG_IsOK(res5)) {
      SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "profile_get_string" "', argument " "5"" of type '" "char const *""'");
    }
    arg5 = (char *)(buf5);
  }
  result = (errcode_t)profile_get_string(arg1,(char const *)arg2,(char const *)arg3,(char const *)arg4,(char const *)arg5,arg6);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char **OUTPUT */
    /*    Tcl_SetResult(interp, *arg6, TCL_DYNAMIC); */
    char *s = (arg6 && *arg6) ? *arg6 : "";
    Tcl_ListObjAppendElement(interp, Tcl_GetObjResult(interp),
      Tcl_NewStringObj(s, strlen(s)));
    profile_release_string(s);
  }
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  if (alloc5 == SWIG_NEWOBJ) free((char*)buf5);
  return TCL_OK;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  if (alloc5 == SWIG_NEWOBJ) free((char*)buf5);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_integer(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char *arg2 = (char *) 0 ;
  char *arg3 = (char *) 0 ;
  char *arg4 = (char *) NULL ;
  int arg5 = (int) 0 ;
  int *arg6 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  int res4 ;
  char *buf4 = 0 ;
  int alloc4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  int temp6 ;
  int res6 = SWIG_TMPOBJ ;
  errcode_t result;

  arg6 = &temp6;
  if (SWIG_GetArgs(interp, objc, objv,"ooo|oo:profile_get_integer p name subname ?subsubname? ?defval? ",(void *)0,(void *)0,(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_integer" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  res2 = SWIG_AsCharPtrAndSize(objv[2], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "profile_get_integer" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = (char *)(buf2);
  res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_get_integer" "', argument " "3"" of type '" "char const *""'");
  }
  arg3 = (char *)(buf3);
  if (objc > 4) {
    res4 = SWIG_AsCharPtrAndSize(objv[4], &buf4, NULL, &alloc4);
    if (!SWIG_IsOK(res4)) {
      SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "profile_get_integer" "', argument " "4"" of type '" "char const *""'");
    }
    arg4 = (char *)(buf4);
  }
  if (objc > 5) {
    ecode5 = SWIG_AsVal_int SWIG_TCL_CALL_ARGS_2(objv[5], &val5);
    if (!SWIG_IsOK(ecode5)) {
      SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "profile_get_integer" "', argument " "5"" of type '" "int""'");
    }
    arg5 = (int)(val5);
  }
  result = (errcode_t)profile_get_integer(arg1,(char const *)arg2,(char const *)arg3,(char const *)arg4,arg5,arg6);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  if (SWIG_IsTmpObj(res6)) {
    Tcl_ListObjAppendElement(interp,Tcl_GetObjResult(interp),SWIG_From_int((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    Tcl_ListObjAppendElement(interp,Tcl_GetObjResult(interp),SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_int, new_flags));
  }
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_OK;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_boolean(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char *arg2 = (char *) 0 ;
  char *arg3 = (char *) 0 ;
  char *arg4 = (char *) NULL ;
  int arg5 = (int) 0 ;
  int *arg6 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  int res4 ;
  char *buf4 = 0 ;
  int alloc4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  int temp6 ;
  int res6 = SWIG_TMPOBJ ;
  errcode_t result;

  arg6 = &temp6;
  if (SWIG_GetArgs(interp, objc, objv,"ooo|oo:profile_get_boolean p name subname ?subsubname? ?defval? ",(void *)0,(void *)0,(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_boolean" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  res2 = SWIG_AsCharPtrAndSize(objv[2], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "profile_get_boolean" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = (char *)(buf2);
  res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_get_boolean" "', argument " "3"" of type '" "char const *""'");
  }
  arg3 = (char *)(buf3);
  if (objc > 4) {
    res4 = SWIG_AsCharPtrAndSize(objv[4], &buf4, NULL, &alloc4);
    if (!SWIG_IsOK(res4)) {
      SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "profile_get_boolean" "', argument " "4"" of type '" "char const *""'");
    }
    arg4 = (char *)(buf4);
  }
  if (objc > 5) {
    ecode5 = SWIG_AsVal_int SWIG_TCL_CALL_ARGS_2(objv[5], &val5);
    if (!SWIG_IsOK(ecode5)) {
      SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "profile_get_boolean" "', argument " "5"" of type '" "int""'");
    }
    arg5 = (int)(val5);
  }
  result = (errcode_t)profile_get_boolean(arg1,(char const *)arg2,(char const *)arg3,(char const *)arg4,arg5,arg6);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  if (SWIG_IsTmpObj(res6)) {
    Tcl_ListObjAppendElement(interp,Tcl_GetObjResult(interp),SWIG_From_int((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    Tcl_ListObjAppendElement(interp,Tcl_GetObjResult(interp),SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_int, new_flags));
  }
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_OK;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_relation_names(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char ***arg3 = (char ***) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char **tmp3 ;
  errcode_t result;

  {
    /* in char ***OUTPUT */
    tmp3 = NULL;
    arg3 = &tmp3;
  }
  if (SWIG_GetArgs(interp, objc, objv,"oo:profile_get_relation_names p nullterm ",(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_relation_names" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  result = (errcode_t)profile_get_relation_names(arg1,(char const **)arg2,arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char ***OUTPUT */
    int i;
    for (i = 0; (*arg3)[i]; i++)
    Tcl_AppendElement(interp, (*arg3)[i]);
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_get_subsection_names(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char ***arg3 = (char ***) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char **tmp3 ;
  errcode_t result;

  {
    /* in char ***OUTPUT */
    tmp3 = NULL;
    arg3 = &tmp3;
  }
  if (SWIG_GetArgs(interp, objc, objv,"oo:profile_get_subsection_names p nullterm ",(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_get_subsection_names" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  result = (errcode_t)profile_get_subsection_names(arg1,(char const **)arg2,arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char ***OUTPUT */
    int i;
    for (i = 0; (*arg3)[i]; i++)
    Tcl_AppendElement(interp, (*arg3)[i]);
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_iterator_create(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  int arg3 ;
  iter_t *arg4 = (iter_t *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  iter_t tmp4 ;
  errcode_t result;

  {
    /*generic swigtype hack*/ arg4 = &tmp4;
  }
  if (SWIG_GetArgs(interp, objc, objv,"ooo:profile_iterator_create p nullterm flags ",(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_iterator_create" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  ecode3 = SWIG_AsVal_int SWIG_TCL_CALL_ARGS_2(objv[3], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "profile_iterator_create" "', argument " "3"" of type '" "int""'");
  }
  arg3 = (int)(val3);
  result = (errcode_t)iter_create(arg1,(char const **)arg2,arg3,arg4);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  /*generic swigtype hack*/ Tcl_SetObjResult(interp,SWIG_NewInstanceObj((void *) *arg4, SWIGTYPE_p_p_void,0));
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_iterator_free(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  iter_t arg1 = (iter_t) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;

  if (SWIG_GetArgs(interp, objc, objv,"o:profile_iterator_free i ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p_p_void, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_iterator_free" "', argument " "1"" of type '" "iter_t""'");
  }
  arg1 = (iter_t)(argp1);
  iter_free(arg1);

  return TCL_OK;
fail:
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_iterator(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  iter_t arg1 = (iter_t) 0 ;
  char **arg2 = (char **) 0 ;
  char **arg3 = (char **) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char *tmp2 ;
  char *tmp3 ;
  errcode_t result;

  {
    /* in char **OUTPUT */
    tmp2 = NULL;
    arg2 = &tmp2;
  }
  {
    /* in char **OUTPUT */
    tmp3 = NULL;
    arg3 = &tmp3;
  }
  if (SWIG_GetArgs(interp, objc, objv,"o:profile_iterator iter_t ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p_p_void, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_iterator" "', argument " "1"" of type '" "iter_t""'");
  }
  arg1 = (iter_t)(argp1);
  result = (errcode_t)profile_iterator(&arg1->handle,arg2,arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char **OUTPUT */
    /*    Tcl_SetResult(interp, *arg2, TCL_DYNAMIC); */
    char *s = (arg2 && *arg2) ? *arg2 : "";
    Tcl_ListObjAppendElement(interp, Tcl_GetObjResult(interp),
      Tcl_NewStringObj(s, strlen(s)));
  }
  profile_release_string(*arg2);
  {
    /* argout char **OUTPUT */
    /*    Tcl_SetResult(interp, *arg3, TCL_DYNAMIC); */
    char *s = (arg3 && *arg3) ? *arg3 : "";
    Tcl_ListObjAppendElement(interp, Tcl_GetObjResult(interp),
      Tcl_NewStringObj(s, strlen(s)));
  }
  profile_release_string(*arg3);
  return TCL_OK;
fail:
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_update_relation(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char *arg3 = (char *) 0 ;
  char *arg4 = (char *) NULL ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  int res4 ;
  char *buf4 = 0 ;
  int alloc4 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"ooo|o:profile_update_relation p nullterm oldval ?newval? ",(void *)0,(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_update_relation" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_update_relation" "', argument " "3"" of type '" "char const *""'");
  }
  arg3 = (char *)(buf3);
  if (objc > 4) {
    res4 = SWIG_AsCharPtrAndSize(objv[4], &buf4, NULL, &alloc4);
    if (!SWIG_IsOK(res4)) {
      SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "profile_update_relation" "', argument " "4"" of type '" "char const *""'");
    }
    arg4 = (char *)(buf4);
  }
  result = (errcode_t)profile_update_relation(arg1,(char const **)arg2,(char const *)arg3,(char const *)arg4);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  if (alloc4 == SWIG_NEWOBJ) free((char*)buf4);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_clear_relation(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"oo:profile_clear_relation p nullterm ",(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_clear_relation" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  result = (errcode_t)profile_clear_relation(arg1,(char const **)arg2);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_rename_section(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char *arg3 = (char *) NULL ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"oo|o:profile_rename_section p nullterm ?new_name? ",(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_rename_section" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  if (objc > 3) {
    res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
    if (!SWIG_IsOK(res3)) {
      SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_rename_section" "', argument " "3"" of type '" "char const *""'");
    }
    arg3 = (char *)(buf3);
  }
  result = (errcode_t)profile_rename_section(arg1,(char const **)arg2,(char const *)arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_add_relation(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  char *arg3 = (char *) NULL ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  errcode_t result;

  if (SWIG_GetArgs(interp, objc, objv,"oo|o:profile_add_relation p nullterm ?new_val? ",(void *)0,(void *)0,(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_add_relation" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  {
    /* in char **nullterm */
    int n;
    if (Tcl_SplitList(interp, Tcl_GetStringFromObj(objv[2],NULL), &n, (const char ***) &arg2) == TCL_ERROR) SWIG_fail;
  }
  if (objc > 3) {
    res3 = SWIG_AsCharPtrAndSize(objv[3], &buf3, NULL, &alloc3);
    if (!SWIG_IsOK(res3)) {
      SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "profile_add_relation" "', argument " "3"" of type '" "char const *""'");
    }
    arg3 = (char *)(buf3);
  }
  result = (errcode_t)profile_add_relation(arg1,(char const **)arg2,(char const *)arg3);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  return TCL_OK;
fail:
  {
    /* freearg char **nullterm */
    if (arg2) {
      Tcl_Free((char *)arg2); arg2 = (char **) NULL;
    }
  }
  if (alloc3 == SWIG_NEWOBJ) free((char*)buf3);
  return TCL_ERROR;
}


SWIGINTERN int
_wrap_profile_flush_to_buffer(ClientData clientData SWIGUNUSED, Tcl_Interp *interp, int objc, Tcl_Obj *CONST objv[]) {
  profile_t arg1 = (profile_t) 0 ;
  char **arg2 = (char **) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char *tmp2 ;
  errcode_t result;

  {
    /* in char **OUTPUT */
    tmp2 = NULL;
    arg2 = &tmp2;
  }
  if (SWIG_GetArgs(interp, objc, objv,"o:profile_flush_to_buffer p ",(void *)0) == TCL_ERROR) SWIG_fail;
  res1 = SWIG_ConvertPtr(objv[1], &argp1,SWIGTYPE_p__profile_t, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "profile_flush_to_buffer" "', argument " "1"" of type '" "profile_t""'");
  }
  arg1 = (profile_t)(argp1);
  result = (errcode_t)profile_flush_to_buffer(arg1,arg2);
  {
    /* out errcode_t result */
    if (result) {
      /* There could be a memory leak here in the SWIG-Tcl layer,
      	   I'm not sure.  Not going to worry about it though.  */
      Tcl_SetResult(interp, (char *) error_message(result), TCL_STATIC);
      SWIG_fail;
    }
  }
  {
    /* argout char **OUTPUT */
    /*    Tcl_SetResult(interp, *arg2, TCL_DYNAMIC); */
    char *s = (arg2 && *arg2) ? *arg2 : "";
    Tcl_ListObjAppendElement(interp, Tcl_GetObjResult(interp),
      Tcl_NewStringObj(s, strlen(s)));
  }
  return TCL_OK;
fail:
  return TCL_ERROR;
}



static swig_command_info swig_commands[] = {
    { SWIG_prefix "profile_init_path", (swig_wrapper_func) _wrap_profile_init_path, NULL},
    { SWIG_prefix "profile_init", (swig_wrapper_func) _wrap_profile_init, NULL},
    { SWIG_prefix "profile_flush", (swig_wrapper_func) _wrap_profile_flush, NULL},
    { SWIG_prefix "profile_flush_to_file", (swig_wrapper_func) _wrap_profile_flush_to_file, NULL},
    { SWIG_prefix "profile_abandon", (swig_wrapper_func) _wrap_profile_abandon, NULL},
    { SWIG_prefix "profile_release", (swig_wrapper_func) _wrap_profile_release, NULL},
    { SWIG_prefix "profile_get_values", (swig_wrapper_func) _wrap_profile_get_values, NULL},
    { SWIG_prefix "profile_get_string", (swig_wrapper_func) _wrap_profile_get_string, NULL},
    { SWIG_prefix "profile_get_integer", (swig_wrapper_func) _wrap_profile_get_integer, NULL},
    { SWIG_prefix "profile_get_boolean", (swig_wrapper_func) _wrap_profile_get_boolean, NULL},
    { SWIG_prefix "profile_get_relation_names", (swig_wrapper_func) _wrap_profile_get_relation_names, NULL},
    { SWIG_prefix "profile_get_subsection_names", (swig_wrapper_func) _wrap_profile_get_subsection_names, NULL},
    { SWIG_prefix "profile_iterator_create", (swig_wrapper_func) _wrap_profile_iterator_create, NULL},
    { SWIG_prefix "profile_iterator_free", (swig_wrapper_func) _wrap_profile_iterator_free, NULL},
    { SWIG_prefix "profile_iterator", (swig_wrapper_func) _wrap_profile_iterator, NULL},
    { SWIG_prefix "profile_update_relation", (swig_wrapper_func) _wrap_profile_update_relation, NULL},
    { SWIG_prefix "profile_clear_relation", (swig_wrapper_func) _wrap_profile_clear_relation, NULL},
    { SWIG_prefix "profile_rename_section", (swig_wrapper_func) _wrap_profile_rename_section, NULL},
    { SWIG_prefix "profile_add_relation", (swig_wrapper_func) _wrap_profile_add_relation, NULL},
    { SWIG_prefix "profile_flush_to_buffer", (swig_wrapper_func) _wrap_profile_flush_to_buffer, NULL},
    {0, 0, 0}
};

static swig_var_info swig_variables[] = {
    {0,0,0,0}
};

static swig_const_info swig_constants[] = {
    {0,0,0,0,0,0}
};

/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static swig_type_info _swigt__p__profile_t = {"_p__profile_t", "struct _profile_t *|profile_t", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_int = {"_p_int", "int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long = {"_p_long", "long *|errcode_t *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p__profile_t = {"_p_p__profile_t", "profile_t *|struct _profile_t **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_char = {"_p_p_char", "char **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_p_char = {"_p_p_p_char", "char ***", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_p_void = {"_p_p_p_void", "void ***|iter_t *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_void = {"_p_p_void", "void **|iter_t", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p__profile_t,
  &_swigt__p_char,
  &_swigt__p_int,
  &_swigt__p_long,
  &_swigt__p_p__profile_t,
  &_swigt__p_p_char,
  &_swigt__p_p_p_char,
  &_swigt__p_p_p_void,
  &_swigt__p_p_void,
};

static swig_cast_info _swigc__p__profile_t[] = {  {&_swigt__p__profile_t, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_int[] = {  {&_swigt__p_int, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long[] = {  {&_swigt__p_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p__profile_t[] = {  {&_swigt__p_p__profile_t, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_char[] = {  {&_swigt__p_p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_p_char[] = {  {&_swigt__p_p_p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_p_void[] = {  {&_swigt__p_p_p_void, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_void[] = {  {&_swigt__p_p_void, 0, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p__profile_t,
  _swigc__p_char,
  _swigc__p_int,
  _swigc__p_long,
  _swigc__p_p__profile_t,
  _swigc__p_p_char,
  _swigc__p_p_p_char,
  _swigc__p_p_p_void,
  _swigc__p_p_void,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */

#ifdef __cplusplus
}
#endif
/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned staticly to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif


SWIGRUNTIME void
SWIG_InitializeModule(void *clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int found, init;

  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }

  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
    module_head = &swig_module;
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    found=0;
    iter=module_head;
    do {
      if (iter==&swig_module) {
        found=1;
        break;
      }
      iter=iter->next;
    } while (iter!= module_head);

    /* if the is found in the list, then all is done and we may leave */
    if (found) return;
    /* otherwise we must add out module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }

  /* When multiple interpeters are used, a module could have already been initialized in
       a different interpreter, but not yet have a pointer in this interpreter.
       In this case, we do not want to continue adding types... everything should be
       set up already */
  if (init == 0) return;

  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %d\n", swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;

#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %d %s\n", i, swig_module.type_initial[i]->name);
#endif

    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
        type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
        printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }

    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {
      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
        if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
        if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
          printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
          cast->type = ret;
          ret = 0;
        } else {
          /* Check for casting already in the list */
          swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
          if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
          if (!ocast) ret = 0;
        }
      }

      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
        printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;

#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %d %s\n", i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
    printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;

  if (init_run) return;
  init_run = 1;

  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
          SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{
  /* c-mode */
#endif
}
#endif


#ifdef __cplusplus
extern "C" {
#endif

  /* -----------------------------------------------------------------------------
   * constants/methods manipulation
   * ----------------------------------------------------------------------------- */

  /* Install Constants */

  SWIGINTERN void
  SWIG_Tcl_InstallConstants(Tcl_Interp *interp, swig_const_info constants[]) {
    int i;
    Tcl_Obj *obj;

    if (!swigconstTableinit) {
      Tcl_InitHashTable(&swigconstTable, TCL_STRING_KEYS);
      swigconstTableinit = 1;
    }
    for (i = 0; constants[i].type; i++) {
      switch(constants[i].type) {
      case SWIG_TCL_POINTER:
        obj = SWIG_NewPointerObj(constants[i].pvalue, *(constants[i]).ptype,0);
        break;
      case SWIG_TCL_BINARY:
        obj = SWIG_NewPackedObj(constants[i].pvalue, constants[i].lvalue, *(constants[i].ptype));
        break;
      default:
        obj = 0;
        break;
      }
      if (obj) {
        SWIG_Tcl_SetConstantObj(interp, constants[i].name, obj);
      }
    }
  }

#ifdef __cplusplus
}
#endif

/* -----------------------------------------------------------------------------*
 *  Partial Init method
 * -----------------------------------------------------------------------------*/

SWIGEXPORT int SWIG_init(Tcl_Interp *interp) {
  int i;
  if (interp == 0) return TCL_ERROR;
#ifdef USE_TCL_STUBS
  /* (char*) cast is required to avoid compiler warning/error for Tcl < 8.4. */
  if (Tcl_InitStubs(interp, (char*)SWIG_TCL_STUBS_VERSION, 0) == NULL) {
    return TCL_ERROR;
  }
#endif
#ifdef USE_TK_STUBS
  /* (char*) cast is required to avoid compiler warning/error. */
  if (Tk_InitStubs(interp, (char*)SWIG_TCL_STUBS_VERSION, 0) == NULL) {
    return TCL_ERROR;
  }
#endif

  Tcl_PkgProvide(interp, (char*)SWIG_name, (char*)SWIG_version);

#ifdef SWIG_namespace
  Tcl_Eval(interp, "namespace eval " SWIG_namespace " { }");
#endif

  SWIG_InitializeModule((void *) interp);
  SWIG_PropagateClientData();

  for (i = 0; swig_commands[i].name; i++) {
    Tcl_CreateObjCommand(interp, (char *) swig_commands[i].name, (swig_wrapper_func) swig_commands[i].wrapper,
      swig_commands[i].clientdata, NULL);
  }
  for (i = 0; swig_variables[i].name; i++) {
    Tcl_SetVar(interp, (char *) swig_variables[i].name, (char *) "", TCL_GLOBAL_ONLY);
    Tcl_TraceVar(interp, (char *) swig_variables[i].name, TCL_TRACE_READS | TCL_GLOBAL_ONLY,
      (Tcl_VarTraceProc *) swig_variables[i].get, (ClientData) swig_variables[i].addr);
    Tcl_TraceVar(interp, (char *) swig_variables[i].name, TCL_TRACE_WRITES | TCL_GLOBAL_ONLY,
      (Tcl_VarTraceProc *) swig_variables[i].set, (ClientData) swig_variables[i].addr);
  }

  SWIG_Tcl_InstallConstants(interp, swig_constants);


  return TCL_OK;
}
