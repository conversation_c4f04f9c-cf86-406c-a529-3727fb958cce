=pod

=head1 NAME

EVP_idea_cbc,
<PERSON><PERSON>_idea_cfb,
<PERSON><PERSON>_idea_cfb64,
<PERSON><PERSON>_idea_ecb,
EVP_idea_ofb
- EVP IDEA cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_idea_cbc(void)
 const EVP_CIPHER *EVP_idea_cfb(void)
 const EVP_CIPHER *EVP_idea_cfb64(void)
 const EVP_CIPHER *EVP_idea_ecb(void)
 const EVP_CIPHER *EVP_idea_ofb(void)

=head1 DESCRIPTION

The IDEA encryption algorithm for EVP.

=over 4

=item EVP_idea_cbc(),
EVP_idea_cfb(),
EVP_idea_cfb64(),
EVP_idea_ecb(),
EVP_idea_ofb()

The IDEA encryption algorithm in CBC, CFB, ECB and OFB modes respectively.

=back

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

