# P2P Socket Interface Unit Tests

## Overview

`testp2psocket.cpp` is a comprehensive unit test suite for the basic p2psocket.h interface. It focuses on testing the core socket operations with real server-client connections, excluding Stream API functionality.

## Features Tested

### Basic Socket Operations
- Socket creation and destruction (`P2pCreate`, `P2pClose`)
- Socket binding and listening (`P2pBind`, `P2pL<PERSON>en`, `P2pAccept`)
- Connection establishment (`P2pConnect`)
- Data transmission (`P2pWrite`, `P2pRead`, `P2pWritev`)
- Socket configuration (`P2pSetConnTimeout`, `P2pSetSendMode`, `P2pSetReadMode`)
- Port operations (`P2pGetLocalPort`)
- Polling operations (`P2pPoll`)

### Real Connection Testing
- Server-client connection establishment
- Data transmission with echo server
- Vectored I/O operations with real connections
- Connection timeout handling

### Error Handling & Edge Cases
- Invalid parameter handling (null pointers, invalid values)
- Multiple socket types (MU<PERSON>ITCP, SSL, QUIC)
- Boundary conditions and edge cases
- Invalid socket options and parameters

## Building and Running

### Prerequisites
- C++ compiler with C++11 support
- p2psocket library and headers
- cert_wrapper library for certificate handling

### Compilation
```bash
# Using the project's build system
cd /path/to/QuicChannel
# Add testp2psocket.cpp to your build configuration

# Or compile manually (example)
g++ -std=c++11 -I./src/p2psocket -L./lib \
    src/p2psocket/testp2psocket.cpp \
    -lp2psocket -lcert_wrapper -o testp2psocket
```

### Running Tests
```bash
# Run with default settings (QUIC socket type)
./testp2psocket

# Run with specific socket type
./testp2psocket -type 3  # QUIC
./testp2psocket -type 2  # SSL
./testp2psocket -type 1  # MULTITCP

# Run with custom configuration
./testp2psocket -type 3 -port 5000 -ip ************* -workers 8

# Show help
./testp2psocket -h
```

### Command Line Options
- `-type <n>`: Socket type (1=MULTITCP, 2=SSL, 3=QUIC)
- `-port <n>`: Test port number (default: 4433)
- `-ip <addr>`: Test IP address (default: 127.0.0.1)
- `-workers <n>`: Number of worker threads (default: 4)
- `-h, --help`: Show help message

## Test Categories

### 1. Basic Socket Operations
Tests fundamental socket functionality including creation, binding, and basic configuration.

### 2. Invalid Parameters
Verifies that the API handles invalid inputs gracefully without crashing.

### 3. Socket Binding
Tests port binding, listening, and local port retrieval functionality.

### 4. Socket Settings
Tests timeout and mode configuration functions.

### 5. Polling Operations
Tests event polling functionality for sockets.

### 6. Server-Client Connection
Tests real connection establishment with echo server for data transmission validation.

### 7. Vectored I/O
Tests scatter-gather I/O operations with real connections.

### 8. Multiple Socket Types
Tests all supported socket types (MULTITCP, SSL, QUIC).

### 9. Edge Cases
Tests boundary conditions and unusual parameter combinations.

## Expected Results

The test suite is designed to work with real network connections where:
- Sockets can be created and configured
- Server-client connections can be established
- Data can be transmitted and received
- Invalid operations fail gracefully
- API functions return appropriate error codes
- Memory is managed correctly

The tests use real server-client connections with echo functionality to validate data transmission APIs.

## Test Output

The test suite provides detailed output including:
- Individual test results (PASS/FAIL)
- Error messages for failed tests
- Summary statistics
- Configuration information

Example output:
```
[TEST] Starting P2P Socket Interface Unit Tests (Basic API Only)
[TEST] Configuration:
[TEST]   Socket type: 3
[TEST]   Test port: 4433
[TEST]   Test IP: 127.0.0.1
[TEST]   Workers: 4

[INFO] PASS: Certificate creation
[INFO] PASS: Socket creation
[INFO] PASS: Socket close
[TEST] Server thread starting...
[TEST] Server listening on port 4443
[TEST] Client connected successfully
[TEST] Server received 18 bytes
[TEST] Server echoed 18 bytes
[INFO] PASS: Client write data
[INFO] PASS: Client read echoed data
[INFO] PASS: Echoed data matches sent data
[TEST] Data echo test successful
...
[TEST] === Test Summary ===
[TEST] Total tests: 35
[TEST] Passed: 35
[TEST] Failed: 0
[TEST] All tests passed! ✓
```

## Integration with Build System

To integrate with your build system, add `testp2psocket.cpp` to your test targets and ensure it links with the p2psocket and cert_wrapper libraries.

## Notes

- Tests are designed to be non-destructive and safe to run repeatedly
- Real server-client connections are established for data transmission testing
- Some tests may have different results depending on the socket type
- The test suite focuses on API correctness rather than performance
- Certificate generation is handled automatically by the cert_wrapper library
- Stream API functionality is not tested in this suite (focused on basic socket operations only)
- Tests use different ports to avoid conflicts when running multiple test instances
