# P2P Socket Interface Unit Tests

## Overview

`testp2psocket.cpp` is a comprehensive unit test suite for the p2psocket.h interface. It tests all major functionality including basic socket operations, Stream API, error handling, and edge cases.

## Features Tested

### Basic Socket Operations
- Socket creation and destruction (`P2pCreate`, `P2pClose`)
- Socket binding and listening (`P2pBind`, `P2pListen`, `P2pAccept`)
- Connection establishment (`P2pConnect`)
- Data transmission (`P2pWrite`, `P2pRead`, `P2pWritev`)
- Socket configuration (`P2pSetConnTimeout`, `P2pSetSendMode`, `P2pSetReadMode`)
- Port operations (`P2pGetLocalPort`)
- Polling operations (`P2pPoll`)

### Stream API Operations
- Stream creation and destruction (`P2pStreamCreate`, `P2pStreamClose`)
- Stream data transmission (`P2pStreamWrite`, `P2pStreamRead`, `P2pStreamWritev`)
- Stream state management (`P2pStreamGetState`, `P2pStreamGetId`)
- Stream information queries (`P2pStreamGetBufferedBytes`, `P2pStreamGetSocket`)
- Stream polling (`P2pStreamPoll`)
- Stream callbacks (`P2pStreamSetCallback`)

### Error Handling & Edge Cases
- Invalid parameter handling (null pointers, invalid values)
- Operations on unconnected sockets
- Multiple socket types (MULTITCP, SSL, QUIC)
- Boundary conditions and edge cases

## Building and Running

### Prerequisites
- C++ compiler with C++11 support
- p2psocket library and headers
- cert_wrapper library for certificate handling

### Compilation
```bash
# Using the project's build system
cd /path/to/QuicChannel
# Add testp2psocket.cpp to your build configuration

# Or compile manually (example)
g++ -std=c++11 -I./src/p2psocket -L./lib \
    src/p2psocket/testp2psocket.cpp \
    -lp2psocket -lcert_wrapper -o testp2psocket
```

### Running Tests
```bash
# Run with default settings (QUIC socket type)
./testp2psocket

# Run with specific socket type
./testp2psocket -type 3  # QUIC
./testp2psocket -type 2  # SSL
./testp2psocket -type 1  # MULTITCP

# Run with custom configuration
./testp2psocket -type 3 -port 5000 -ip ************* -workers 8

# Show help
./testp2psocket -h
```

### Command Line Options
- `-type <n>`: Socket type (1=MULTITCP, 2=SSL, 3=QUIC)
- `-port <n>`: Test port number (default: 4433)
- `-ip <addr>`: Test IP address (default: 127.0.0.1)
- `-workers <n>`: Number of worker threads (default: 4)
- `-h, --help`: Show help message

## Test Categories

### 1. Basic Socket Operations
Tests fundamental socket functionality including creation, binding, and basic configuration.

### 2. Invalid Parameters
Verifies that the API handles invalid inputs gracefully without crashing.

### 3. Socket Binding
Tests port binding and local port retrieval functionality.

### 4. Socket Settings
Tests timeout and mode configuration functions.

### 5. Stream API Basics
Tests stream creation, information queries, and basic operations.

### 6. Stream API Invalid Parameters
Verifies stream API error handling with invalid inputs.

### 7. Vectored I/O
Tests scatter-gather I/O operations for both sockets and streams.

### 8. Polling Operations
Tests event polling functionality for sockets and streams.

### 9. Stream Callbacks
Tests stream event callback registration and handling.

### 10. Server-Client Connection
Tests basic connection establishment (simplified for unit testing).

### 11. Data Transmission (Unconnected)
Tests that data operations fail gracefully on unconnected sockets.

### 12. Multiple Socket Types
Tests all supported socket types (MULTITCP, SSL, QUIC).

### 13. Edge Cases
Tests boundary conditions and unusual parameter combinations.

## Expected Results

The test suite is designed to work in a unit testing environment where:
- Sockets can be created and configured
- Invalid operations fail gracefully
- API functions return appropriate error codes
- Memory is managed correctly

Note: Some tests (like actual network connections) are expected to fail in a unit test environment, but the API should handle these failures gracefully.

## Test Output

The test suite provides detailed output including:
- Individual test results (PASS/FAIL)
- Error messages for failed tests
- Summary statistics
- Configuration information

Example output:
```
[TEST] Starting P2P Socket Interface Unit Tests
[TEST] Configuration:
[TEST]   Socket type: 3
[TEST]   Test port: 4433
[TEST]   Test IP: 127.0.0.1
[TEST]   Workers: 4

[INFO] PASS: Certificate creation
[INFO] PASS: Socket creation
[INFO] PASS: Socket close
...
[TEST] === Test Summary ===
[TEST] Total tests: 45
[TEST] Passed: 43
[TEST] Failed: 2
[TEST] All tests passed! ✓
```

## Integration with Build System

To integrate with your build system, add `testp2psocket.cpp` to your test targets and ensure it links with the p2psocket and cert_wrapper libraries.

## Notes

- Tests are designed to be non-destructive and safe to run repeatedly
- Some tests may have different results depending on the socket type
- The test suite focuses on API correctness rather than performance
- Certificate generation is handled automatically by the cert_wrapper library
