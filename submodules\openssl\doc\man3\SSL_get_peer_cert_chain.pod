=pod

=head1 NAME

SSL_get_peer_cert_chain, SSL_get0_verified_chain - get the X509 certificate
chain of the peer

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 STACK_OF(X509) *SSL_get_peer_cert_chain(const SSL *ssl);
 STACK_OF(X509) *SSL_get0_verified_chain(const SSL *ssl);

=head1 DESCRIPTION

SSL_get_peer_cert_chain() returns a pointer to STACK_OF(X509) certificates
forming the certificate chain sent by the peer. If called on the client side,
the stack also contains the peer's certificate; if called on the server
side, the peer's certificate must be obtained separately using
L<SSL_get_peer_certificate(3)>.
If the peer did not present a certificate, NULL is returned.

NB: SSL_get_peer_cert_chain() returns the peer chain as sent by the peer: it
only consists of certificates the peer has sent (in the order the peer
has sent them) it is B<not> a verified chain.

SSL_get0_verified_chain() returns the B<verified> certificate chain
of the peer including the peer's end entity certificate. It must be called
after a session has been successfully established. If peer verification was
not successful (as indicated by SSL_get_verify_result() not returning
X509_V_OK) the chain may be incomplete or invalid.

=head1 NOTES

If the session is resumed peers do not send certificates so a NULL pointer
is returned by these functions. Applications can call SSL_session_reused()
to determine whether a session is resumed.

The reference count of each certificate in the returned STACK_OF(X509) object
is not incremented and the returned stack may be invalidated by renegotiation.
If applications wish to use any certificates in the returned chain
indefinitely they must increase the reference counts using X509_up_ref() or
obtain a copy of the whole chain with X509_chain_up_ref().

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item NULL

No certificate was presented by the peer or no connection was established
or the certificate chain is no longer available when a session is reused.

=item Pointer to a STACK_OF(X509)

The return value points to the certificate chain presented by the peer.

=back

=head1 SEE ALSO

L<ssl(7)>, L<SSL_get_peer_certificate(3)>, L<X509_up_ref(3)>,
L<X509_chain_up_ref(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
