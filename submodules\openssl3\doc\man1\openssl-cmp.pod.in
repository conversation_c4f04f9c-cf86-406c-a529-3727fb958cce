=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-cmp - Certificate Management Protocol (CMP, RFC 4210) application

=head1 SYNOPSIS

B<openssl> B<cmp>
[B<-help>]
[B<-config> I<filename>]
[B<-section> I<names>]
[B<-verbosity> I<level>]

Generic message options:

[B<-cmd> I<ir|cr|kur|p10cr|rr|genm>]
[B<-infotype> I<name>]
[B<-geninfo> I<OID:int:N>]

Certificate enrollment options:

[B<-newkey> I<filename>|I<uri>]
[B<-newkeypass> I<arg>]
[B<-subject> I<name>]
[B<-issuer> I<name>]
[B<-days> I<number>]
[B<-reqexts> I<name>]
[B<-sans> I<spec>]
[B<-san_nodefault>]
[B<-policies> I<name>]
[B<-policy_oids> I<names>]
[B<-policy_oids_critical>]
[B<-popo> I<number>]
[B<-csr> I<filename>]
[B<-out_trusted> I<filenames>|I<uris>]
[B<-implicit_confirm>]
[B<-disable_confirm>]
[B<-certout> I<filename>]
[B<-chainout> I<filename>]

Certificate enrollment and revocation options:

[B<-oldcert> I<filename>|I<uri>]
[B<-revreason> I<number>]

Message transfer options:

[B<-server> I<[http[s]://][userinfo@]host[:port][/path][?query][#fragment]>]
[B<-proxy> I<[http[s]://][userinfo@]host[:port][/path][?query][#fragment]>]
[B<-no_proxy> I<addresses>]
[B<-recipient> I<name>]
[B<-path> I<remote_path>]
[B<-keep_alive> I<value>]
[B<-msg_timeout> I<seconds>]
[B<-total_timeout> I<seconds>]

Server authentication options:

[B<-trusted> I<filenames>|I<uris>]
[B<-untrusted> I<filenames>|I<uris>]
[B<-srvcert> I<filename>|I<uri>]
[B<-expect_sender> I<name>]
[B<-ignore_keyusage>]
[B<-unprotected_errors>]
[B<-extracertsout> I<filename>]
[B<-cacertsout> I<filename>]

Client authentication and protection options:

[B<-ref> I<value>]
[B<-secret> I<arg>]
[B<-cert> I<filename>|I<uri>]
[B<-own_trusted> I<filenames>|I<uris>]
[B<-key> I<filename>|I<uri>]
[B<-keypass> I<arg>]
[B<-digest> I<name>]
[B<-mac> I<name>]
[B<-extracerts> I<filenames>|I<uris>]
[B<-unprotected_requests>]

Credentials format options:

[B<-certform> I<PEM|DER>]
[B<-keyform> I<PEM|DER|P12|ENGINE>]
[B<-otherpass> I<arg>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

Random state options:

{- $OpenSSL::safe::opt_r_synopsis -}

TLS connection options:

[B<-tls_used>]
[B<-tls_cert> I<filename>|I<uri>]
[B<-tls_key> I<filename>|I<uri>]
[B<-tls_keypass> I<arg>]
[B<-tls_extra> I<filenames>|I<uris>]
[B<-tls_trusted> I<filenames>|I<uris>]
[B<-tls_host> I<name>]

Client-side debugging options:

[B<-batch>]
[B<-repeat> I<number>]
[B<-reqin> I<filenames>]
[B<-reqin_new_tid>]
[B<-reqout> I<filenames>]
[B<-rspin> I<filenames>]
[B<-rspout> I<filenames>]
[B<-use_mock_srv>]

Mock server options:

[B<-port> I<number>]
[B<-max_msgs> I<number>]
[B<-srv_ref> I<value>]
[B<-srv_secret> I<arg>]
[B<-srv_cert> I<filename>|I<uri>]
[B<-srv_key> I<filename>|I<uri>]
[B<-srv_keypass> I<arg>]
[B<-srv_trusted> I<filenames>|I<uris>]
[B<-srv_untrusted> I<filenames>|I<uris>]
[B<-rsp_cert> I<filename>|I<uri>]
[B<-rsp_extracerts> I<filenames>|I<uris>]
[B<-rsp_capubs> I<filenames>|I<uris>]
[B<-poll_count> I<number>]
[B<-check_after> I<number>]
[B<-grant_implicitconf>]
[B<-pkistatus> I<number>]
[B<-failure> I<number>]
[B<-failurebits> I<number>]
[B<-statusstring> I<arg>]
[B<-send_error>]
[B<-send_unprotected>]
[B<-send_unprot_err>]
[B<-accept_unprotected>]
[B<-accept_unprot_err>]
[B<-accept_raverified>]

Certificate verification options, for both CMP and TLS:

{- $OpenSSL::safe::opt_v_synopsis -}

=head1 DESCRIPTION

The B<cmp> command is a client implementation for the Certificate
Management Protocol (CMP) as defined in RFC4210.
It can be used to request certificates from a CA server,
update their certificates,
request certificates to be revoked, and perform other types of CMP requests.

=head1 OPTIONS

=over 4

=item B<-help>

Display a summary of all options

=item B<-config> I<filename>

Configuration file to use.
An empty string C<""> means none.
Default filename is from the environment variable C<OPENSSL_CONF>.

=item B<-section> I<names>

Section(s) to use within config file defining CMP options.
An empty string C<""> means no specific section.
Default is C<cmp>.

Multiple section names may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Contents of sections named later may override contents of sections named before.
In any case, as usual, the C<[default]> section and finally the unnamed
section (as far as present) can provide per-option fallback values.

=item B<-verbosity> I<level>

Level of verbosity for logging, error output, etc.
0 = EMERG, 1 = ALERT, 2 = CRIT, 3 = ERR, 4 = WARN, 5 = NOTE,
6 = INFO, 7 = DEBUG, 8 = TRACE.
Defaults to 6 = INFO.

=back

=head2 Generic message options

=over 4

=item B<-cmd> I<ir|cr|kur|p10cr|rr|genm>

CMP command to execute.
Currently implemented commands are:

=over 8

=item  ir E<nbsp>  - Initialization Request

=item  cr E<nbsp>  - Certificate Request

=item  p10cr - PKCS#10 Certification Request (for legacy support)

=item  kur E<nbsp>E<nbsp>- Key Update Request

=item  rr E<nbsp>  - Revocation Request

=item  genm  - General Message

=back

B<ir> requests initialization of an end entity into a PKI hierarchy
by issuing a first certificate.

B<cr> requests issuing an additional certificate for an end entity already
initialized to the PKI hierarchy.

B<p10cr> requests issuing an additional certificate similarly to B<cr>
but using legacy PKCS#10 CSR format.

B<kur> requests a (key) update for an existing certificate.

B<rr> requests revocation of an existing certificate.

B<genm> requests information using a General Message, where optionally
included B<InfoTypeAndValue>s may be used to state which info is of interest.
Upon receipt of the General Response, information about all received
ITAV B<infoType>s is printed to stdout.

=item B<-infotype> I<name>

Set InfoType name to use for requesting specific info in B<genm>,
e.g., C<signKeyPairTypes>.

=item B<-geninfo> I<OID:int:N>

generalInfo integer values to place in request PKIHeader with given OID,
e.g., C<*******:int:56789>.

=back

=head2 Certificate enrollment options

=over 4

=item B<-newkey> I<filename>|I<uri>

The source of the private or public key for the certificate being requested.
Defaults to the public key in the PKCS#10 CSR given with the B<-csr> option,
the public key of the reference certificate, or the current client key.

The public portion of the key is placed in the certification request.

Unless B<-cmd> I<p10cr>, B<-popo> I<-1>, or B<-popo> I<0> is given, the
private key will be needed as well to provide the proof of possession (POPO),
where the B<-key> option may provide a fallback.

=item B<-newkeypass> I<arg>

Pass phrase source for the key given with the B<-newkey> option.
If not given here, the password will be prompted for if needed.

For more information about the format of I<arg> see
L<openssl-passphrase-options(1)>.

=item B<-subject> I<name>

X509 Distinguished Name (DN) of subject to use in the requested certificate
template.
If the NULL-DN (C<"/">) is given then no subject is placed in the template.
Default is the subject DN of any PKCS#10 CSR given with the B<-csr> option.
For KUR, a further fallback is the subject DN
of the reference certificate (see B<-oldcert>) if provided.
This fallback is used for IR and CR only if no SANs are set.

If provided and neither B<-cert> nor B<-oldcert> is given,
the subject DN is used as fallback sender of outgoing CMP messages.

The argument must be formatted as I</type0=value0/type1=value1/type2=...>.
Special characters may be escaped by C<\> (backslash); whitespace is retained.
Empty values are permitted, but the corresponding type will not be included.
Giving a single C</> will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a C<+> character instead of a C</>
between the AttributeValueAssertions (AVAs) that specify the members of the set.
Example:

C</DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe>

=item B<-issuer> I<name>

X509 issuer Distinguished Name (DN) of the CA server
to place in the requested certificate template in IR/CR/KUR.
If the NULL-DN (C<"/">) is given then no issuer is placed in the template.

If provided and neither B<-recipient> nor B<-srvcert> is given,
the issuer DN is used as fallback recipient of outgoing CMP messages.

The argument must be formatted as I</type0=value0/type1=value1/type2=...>.
For details see the description of the B<-subject> option.

=item B<-days> I<number>

Number of days the new certificate is requested to be valid for, counting from
the current time of the host.
Also triggers the explicit request that the
validity period starts from the current time (as seen by the host).

=item B<-reqexts> I<name>

Name of section in OpenSSL config file defining certificate request extensions.
If the B<-csr> option is present, these extensions augment the extensions
contained the given PKCS#10 CSR, overriding any extensions with same OIDs.

=item B<-sans> I<spec>

One or more IP addresses, DNS names, or URIs separated by commas or whitespace
(where in the latter case the whole argument must be enclosed in "...")
to add as Subject Alternative Name(s) (SAN) certificate request extension.
If the special element "critical" is given the SANs are flagged as critical.
Cannot be used if any Subject Alternative Name extension is set via B<-reqexts>.

=item B<-san_nodefault>

When Subject Alternative Names are not given via B<-sans>
nor defined via B<-reqexts>,
they are copied by default from the reference certificate (see B<-oldcert>).
This can be disabled by giving the B<-san_nodefault> option.

=item B<-policies> I<name>

Name of section in OpenSSL config file defining policies to be set
as certificate request extension.
This option cannot be used together with B<-policy_oids>.

=item B<-policy_oids> I<names>

One or more OID(s), separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...")
to add as certificate policies request extension.
This option cannot be used together with B<-policies>.

=item B<-policy_oids_critical>

Flag the policies given with B<-policy_oids> as critical.

=item B<-popo> I<number>

Proof-of-possession (POPO) method to use for IR/CR/KUR; values: C<-1>..<2> where
C<-1> = NONE, C<0> = RAVERIFIED, C<1> = SIGNATURE (default), C<2> = KEYENC.

Note that a signature-based POPO can only be produced if a private key
is provided via the B<-newkey> or B<-key> options.

=item B<-csr> I<filename>

PKCS#10 CSR in PEM or DER format containing a certificate request.
With B<-cmd> I<p10cr> it is used directly in a legacy P10CR message.

When used with B<-cmd> I<ir>, I<cr>, or I<kur>,
it is transformed into the respective regular CMP request.
In this case, a private key must be provided (with B<-newkey> or B<-key>)
for the proof of possession (unless B<-popo> I<-1> or B<-popo> I<0> is used)
and the respective public key is placed in the certification request
(rather than taking over the public key contained in the PKCS#10 CSR).

PKCS#10 CSR input may also be used with B<-cmd> I<rr>
to specify the certificate to be revoked
via the included subject name and public key.

=item B<-out_trusted> I<filenames>|I<uris>

Trusted certificate(s) to use for validating the newly enrolled certificate.
During this verification, any certificate status checking is disabled.

Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
only affect the certificate verification enabled via this option.

=item B<-implicit_confirm>

Request implicit confirmation of newly enrolled certificates.

=item B<-disable_confirm>

Do not send certificate confirmation message for newly enrolled certificate
without requesting implicit confirmation
to cope with broken servers not supporting implicit confirmation correctly.
B<WARNING:> This leads to behavior violating RFC 4210.

=item B<-certout> I<filename>

The file where the newly enrolled certificate should be saved.

=item B<-chainout> I<filename>

The file where the chain of the newly enrolled certificate should be saved.

=back

=head2 Certificate enrollment and revocation options

=over 4

=item B<-oldcert> I<filename>|I<uri>

The certificate to be updated (i.e., renewed or re-keyed) in Key Update Request
(KUR) messages or to be revoked in Revocation Request (RR) messages.
For KUR the certificate to be updated defaults to B<-cert>,
and the resulting certificate is called I<reference certificate>.
For RR the certificate to be revoked can also be specified using B<-csr>.

The reference certificate, if any, is also used for
deriving default subject DN and Subject Alternative Names and the
default issuer entry in the requested certificate template of an IR/CR/KUR.
Its public key is used as a fallback in the template of certification requests.
Its subject is used as sender of outgoing messages if B<-cert> is not given.
Its issuer is used as default recipient in CMP message headers
if neither B<-recipient>, B<-srvcert>, nor B<-issuer> is given.

=item B<-revreason> I<number>

Set CRLReason to be included in revocation request (RR); values: C<0>..C<10>
or C<-1> for none (which is the default).

Reason numbers defined in RFC 5280 are:

   CRLReason ::= ENUMERATED {
        unspecified             (0),
        keyCompromise           (1),
        cACompromise            (2),
        affiliationChanged      (3),
        superseded              (4),
        cessationOfOperation    (5),
        certificateHold         (6),
        -- value 7 is not used
        removeFromCRL           (8),
        privilegeWithdrawn      (9),
        aACompromise           (10)
    }

=back

=head2 Message transfer options

=over 4

=item B<-server> I<[http[s]://][userinfo@]host[:port][/path][?query][#fragment]>

The DNS hostname or IP address and optionally port
of the CMP server to connect to using HTTP(S).
This option excludes I<-port> and I<-use_mock_srv>.
It is ignored if I<-rspin> is given with enough filename arguments.

The scheme C<https> may be given only if the B<-tls_used> option is used.
In this case the default port is 443, else 80.
The optional userinfo and fragment components are ignored.
Any given query component is handled as part of the path component.
If a path is included it provides the default value for the B<-path> option.

=item B<-proxy> I<[http[s]://][userinfo@]host[:port][/path][?query][#fragment]>

The HTTP(S) proxy server to use for reaching the CMP server unless B<-no_proxy>
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is C<https>; apart from that
the optional C<http://> or C<https://> prefix is ignored (note that TLS may be
selected by B<-tls_used>), as well as any path, userinfo, and query, and fragment
components.
Defaults to the environment variable C<http_proxy> if set, else C<HTTP_PROXY>
in case no TLS is used, otherwise C<https_proxy> if set, else C<HTTPS_PROXY>.
This option is ignored if I<-server> is not given.

=item B<-no_proxy> I<addresses>

List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable C<no_proxy> if set, else C<NO_PROXY>.
This option is ignored if I<-server> is not given.

=item B<-recipient> I<name>

Distinguished Name (DN) to use in the recipient field of CMP request message
headers, i.e., the CMP server (usually the addressed CA).

The recipient field in the header of a CMP message is mandatory.
If not given explicitly the recipient is determined in the following order:
the subject of the CMP server certificate given with the B<-srvcert> option,
the B<-issuer> option,
the issuer of the certificate given with the B<-oldcert> option,
the issuer of the CMP client certificate (B<-cert> option),
as far as any of those is present, else the NULL-DN as last resort.

The argument must be formatted as I</type0=value0/type1=value1/type2=...>.
For details see the description of the B<-subject> option.

=item B<-path> I<remote_path>

HTTP path at the CMP server (aka CMP alias) to use for POST requests.
Defaults to any path given with B<-server>, else C<"/">.

=item B<-keep_alive> I<value>

If the given value is 0 then HTTP connections are not kept open
after receiving a response, which is the default behavior for HTTP 1.0.
If the value is 1 or 2 then persistent connections are requested.
If the value is 2 then persistent connections are required,
i.e., in case the server does not grant them an error occurs.
The default value is 1, which means preferring to keep the connection open.

=item B<-msg_timeout> I<seconds>

Number of seconds a CMP request-response message round trip
is allowed to take before a timeout error is returned.
A value <= 0 means no limitation (waiting indefinitely).
Default is to use the B<-total_timeout> setting.

=item B<-total_timeout> I<seconds>

Maximum total number of seconds a transaction may take,
including polling etc.
A value <= 0 means no limitation (waiting indefinitely).
Default is 0.

=back

=head2 Server authentication options

=over 4

=item B<-trusted> I<filenames>|I<uris>

The certificate(s), typically of root CAs, the client shall use as trust anchors
when validating signature-based protection of CMP response messages.
This option is ignored if the B<-srvcert> option is given as well.
It provides more flexibility than B<-srvcert> because the CMP protection
certificate of the server is not pinned but may be any certificate
from which a chain to one of the given trust anchors can be constructed.

If none of B<-trusted>, B<-srvcert>, and B<-secret> is given, message validation
errors will be thrown unless B<-unprotected_errors> permits an exception.

Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
have no effect on the certificate verification enabled via this option.

=item B<-untrusted> I<filenames>|I<uris>

Non-trusted intermediate CA certificate(s).
Any extra certificates given with the B<-cert> option are appended to it.
All these certificates may be useful for cert path construction
for the own CMP signer certificate (to include in the extraCerts field of
request messages) and for the TLS client certificate (if TLS is enabled)
as well as for chain building
when validating server certificates (checking signature-based
CMP message protection) and when validating newly enrolled certificates.

Multiple filenames or URLs may be given, separated by commas and/or whitespace.
Each source may contain multiple certificates.

=item B<-srvcert> I<filename>|I<uri>

The specific CMP server certificate to expect and directly trust (even if it is
expired) when verifying signature-based protection of CMP response messages.
This pins the accepted server and results in ignoring the B<-trusted> option.

If set, the subject of the certificate is also used
as default value for the recipient of CMP requests
and as default value for the expected sender of CMP responses.

=item B<-expect_sender> I<name>

Distinguished Name (DN) expected in the sender field of incoming CMP messages.
Defaults to the subject DN of the pinned B<-srvcert>, if any.

This can be used to make sure that only a particular entity is accepted as
CMP message signer, and attackers are not able to use arbitrary certificates
of a trusted PKI hierarchy to fraudulently pose as a CMP server.
Note that this option gives slightly more freedom than setting the B<-srvcert>,
which pins the server to the holder of a particular certificate, while the
expected sender name will continue to match after updates of the server cert.

The argument must be formatted as I</type0=value0/type1=value1/type2=...>.
For details see the description of the B<-subject> option.

=item B<-ignore_keyusage>

Ignore key usage restrictions in CMP signer certificates when validating
signature-based protection of incoming CMP messages.
By default, C<digitalSignature> must be allowed by CMP signer certificates.

=item B<-unprotected_errors>

Accept missing or invalid protection of negative responses from the server.
This applies to the following message types and contents:

=over 4

=item * error messages

=item * negative certificate responses (IP/CP/KUP)

=item * negative revocation responses (RP)

=item * negative PKIConf messages

=back

B<WARNING:> This setting leads to unspecified behavior and it is meant
exclusively to allow interoperability with server implementations violating
RFC 4210, e.g.:

=over 4

=item * section ******* allows exceptions from protecting only for special
cases:
"There MAY be cases in which the PKIProtection BIT STRING is deliberately not
used to protect a message [...] because other protection, external to PKIX, will
be applied instead."

=item * section 5.3.21 is clear on ErrMsgContent: "The CA MUST always sign it
with a signature key."

=item * appendix D.4 shows PKIConf message having protection

=back

=item B<-extracertsout> I<filename>

The file where to save all certificates contained in the extraCerts field
of the last received response message (except for pollRep and PKIConf).

=item B<-cacertsout> I<filename>

The file where to save any CA certificates contained in the caPubs field of
the last received certificate response (i.e., IP, CP, or KUP) message.

=back

=head2 Client authentication options

=over 4

=item B<-ref> I<value>

Reference number/string/value to use as fallback senderKID; this is required
if no sender name can be determined from the B<-cert> or <-subject> options and
is typically used when authenticating with pre-shared key (password-based MAC).

=item B<-secret> I<arg>

Provides the source of a secret value to use with MAC-based message protection.
This takes precedence over the B<-cert> and B<-key> options.
The secret is used for creating MAC-based protection of outgoing messages
and for validating incoming messages that have MAC-based protection.
The algorithm used by default is Password-Based Message Authentication Code (PBM)
as defined in RFC 4210 section *******.

For more information about the format of I<arg> see
L<openssl-passphrase-options(1)>.

=item B<-cert> I<filename>|I<uri>

The client's current CMP signer certificate.
Requires the corresponding key to be given with B<-key>.

The subject and the public key contained in this certificate
serve as fallback values in the certificate template of IR/CR/KUR messages.

The subject of this certificate will be used as sender of outgoing CMP messages,
while the subject of B<-oldcert> or B<-subjectName> may provide fallback values.

The issuer of this certificate is used as one of the recipient fallback values
and as fallback issuer entry in the certificate template of IR/CR/KUR messages.

When performing signature-based message protection,
this "protection certificate", also called "signer certificate",
will be included first in the extraCerts field of outgoing messages
and the signature is done with the corresponding key.
In Initialization Request (IR) messages this can be used for authenticating
using an external entity certificate as defined in appendix E.7 of RFC 4210.

For Key Update Request (KUR) messages this is also used as
the certificate to be updated if the B<-oldcert> option is not given.

If the file includes further certs, they are appended to the untrusted certs
because they typically constitute the chain of the client certificate, which
is included in the extraCerts field in signature-protected request messages.

=item B<-own_trusted> I<filenames>|I<uris>

If this list of certificates is provided then the chain built for
the client-side CMP signer certificate given with the B<-cert> option
is verified using the given certificates as trust anchors.

Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
have no effect on the certificate verification enabled via this option.

=item B<-key> I<filename>|I<uri>

The corresponding private key file for the client's current certificate given in
the B<-cert> option.
This will be used for signature-based message protection unless the B<-secret>
option indicating MAC-based protection or B<-unprotected_requests> is given.

It is also used as a fallback for the B<-newkey> option with IR/CR/KUR messages.

=item B<-keypass> I<arg>

Pass phrase source for the private key given with the B<-key> option.
Also used for B<-cert> and B<-oldcert> in case it is an encrypted PKCS#12 file.
If not given here, the password will be prompted for if needed.

For more information about the format of I<arg> see
L<openssl-passphrase-options(1)>.

=item B<-digest> I<name>

Specifies name of supported digest to use in RFC 4210's MSG_SIG_ALG
and as the one-way function (OWF) in C<MSG_MAC_ALG>.
If applicable, this is used for message protection and
proof-of-possession (POPO) signatures.
To see the list of supported digests, use C<openssl list -digest-commands>.
Defaults to C<sha256>.

=item B<-mac> I<name>

Specifies the name of the MAC algorithm in C<MSG_MAC_ALG>.
To get the names of supported MAC algorithms use C<openssl list -mac-algorithms>
and possibly combine such a name with the name of a supported digest algorithm,
e.g., hmacWithSHA256.
Defaults to C<hmac-sha1> as per RFC 4210.

=item B<-extracerts> I<filenames>|I<uris>

Certificates to append in the extraCerts field when sending messages.
They can be used as the default CMP signer certificate chain to include.

Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.

=item B<-unprotected_requests>

Send request messages without CMP-level protection.

=back

=head2 Credentials format options

=over 4

=item B<-certform> I<PEM|DER>

File format to use when saving a certificate to a file.
Default value is PEM.

=item B<-keyform> I<PEM|DER|P12|ENGINE>

The format of the key input; unspecified by default.
See L<openssl(1)/Format Options> for details.

=item B<-otherpass> I<arg>

Pass phrase source for certificate given with the B<-trusted>, B<-untrusted>,
B<-own_trusted>, B<-srvcert>, B<-out_trusted>, B<-extracerts>,
B<-srv_trusted>, B<-srv_untrusted>, B<-rsp_extracerts>, B<-rsp_capubs>,
B<-tls_extra>, and B<-tls_trusted> options.
If not given here, the password will be prompted for if needed.

For more information about the format of I<arg> see
L<openssl-passphrase-options(1)>.

{- $OpenSSL::safe::opt_engine_item -}

{- output_off() if $disabled{"deprecated-3.0"}; "" -}
As an alternative to using this combination:

    -engine {engineid} -key {keyid} -keyform ENGINE

... it's also possible to just give the key ID in URI form to B<-key>,
like this:

    -key org.openssl.engine:{engineid}:{keyid}

This applies to all options specifying keys: B<-key>, B<-newkey>, and
B<-tls_key>.
{- output_on() if $disabled{"deprecated-3.0"}; "" -}

=back

=head2 Provider options

=over 4

{- $OpenSSL::safe::opt_provider_item -}

=back

=head2 Random state options

=over 4

{- $OpenSSL::safe::opt_r_item -}

=back

=head2 TLS connection options

=over 4

=item B<-tls_used>

Enable using TLS (even when other TLS-related options are not set)
for message exchange with CMP server via HTTP.
This option is not supported with the I<-port> option.
It is ignored if the I<-server> option is not given or I<-use_mock_srv> is given
or I<-rspin> is given with enough filename arguments.

The following TLS-related options are ignored
if B<-tls_used> is not given or does not take effect.

=item B<-tls_cert> I<filename>|I<uri>

Client's TLS certificate.
If the source includes further certs they are used (along with B<-untrusted>
certs) for constructing the client cert chain provided to the TLS server.

=item B<-tls_key> I<filename>|I<uri>

Private key for the client's TLS certificate.

=item B<-tls_keypass> I<arg>

Pass phrase source for client's private TLS key B<-tls_key>.
Also used for B<-tls_cert> in case it is an encrypted PKCS#12 file.
If not given here, the password will be prompted for if needed.

For more information about the format of I<arg> see
L<openssl-passphrase-options(1)>.

=item B<-tls_extra> I<filenames>|I<uris>

Extra certificates to provide to TLS server during TLS handshake

=item B<-tls_trusted> I<filenames>|I<uris>

Trusted certificate(s) to use for validating the TLS server certificate.
This implies hostname validation.

Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
have no effect on the certificate verification enabled via this option.

=item B<-tls_host> I<name>

Address to be checked during hostname validation.
This may be a DNS name or an IP address.
If not given it defaults to the B<-server> address.

=back

=head2 Client-side debugging options

=over 4

=item B<-batch>

Do not interactively prompt for input, for instance when a password is needed.
This can be useful for batch processing and testing.

=item B<-repeat> I<number>

Invoke the command the given positive number of times with the same parameters.
Default is one invocation.

=item B<-reqin> I<filenames>

Take the sequence of CMP requests to send to the server from the given file(s)
rather than from the sequence of requests produced internally.

This option is ignored if the B<-rspin> option is given
because in the latter case no requests are actually sent.

Multiple filenames may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").

The files are read as far as needed to complete the transaction
and filenames have been provided.  If more requests are needed,
the remaining ones are taken from the items at the respective position
in the sequence of requests produced internally.

The client needs to update the recipNonce field in the given requests (except
for the first one) in order to satisfy the checks to be performed by the server.
This causes re-protection (if protecting requests is required).

=item B<-reqin_new_tid>

Use a fresh transactionID for CMP request messages read using B<-reqin>,
which causes their reprotection (if protecting requests is required).
This may be needed in case the sequence of requests is reused
and the CMP server complains that the transaction ID has already been used.

=item B<-reqout> I<filenames>

Save the sequence of CMP requests created by the client to the given file(s).
These requests are not sent to the server if the B<-reqin> option is used, too.

Multiple filenames may be given, separated by commas and/or whitespace.

Files are written as far as needed to save the transaction
and filenames have been provided.
If the transaction contains more requests, the remaining ones are not saved.

=item B<-rspin> I<filenames>

Process the sequence of CMP responses provided in the given file(s),
not contacting any given server,
as long as enough filenames are provided to complete the transaction.

Multiple filenames may be given, separated by commas and/or whitespace.

Any server specified via the I<-server> or I<-use_mock_srv> options is contacted
only if more responses are needed to complete the transaction.
In this case the transaction will fail
unless the server has been prepared to continue the already started transaction.

=item B<-rspout> I<filenames>

Save the sequence of actually used CMP responses to the given file(s).
These have been received from the server unless B<-rspin> takes effect.

Multiple filenames may be given, separated by commas and/or whitespace.

Files are written as far as needed to save the responses
contained in the transaction and filenames have been provided.
If the transaction contains more responses, the remaining ones are not saved.

=item B<-use_mock_srv>

Test the client using the internal CMP server mock-up at API level,
bypassing socket-based transfer via HTTP.
This excludes the B<-server> and B<-port> options.

=back

=head2 Mock server options

=over 4

=item B<-port> I<number>

Act as HTTP-based CMP server mock-up listening on the given port.
This excludes the B<-server> and B<-use_mock_srv> options.
The B<-rspin>, B<-rspout>, B<-reqin>, and B<-reqout> options
so far are not supported in this mode.

=item B<-max_msgs> I<number>

Maximum number of CMP (request) messages the CMP HTTP server mock-up
should handle, which must be nonnegative.
The default value is 0, which means that no limit is imposed.
In any case the server terminates on internal errors, but not when it
detects a CMP-level error that it can successfully answer with an error message.

=item B<-srv_ref> I<value>

Reference value to use as senderKID of server in case no B<-srv_cert> is given.

=item B<-srv_secret> I<arg>

Password source for server authentication with a pre-shared key (secret).

=item B<-srv_cert> I<filename>|I<uri>

Certificate of the server.

=item B<-srv_key> I<filename>|I<uri>

Private key used by the server for signing messages.

=item B<-srv_keypass> I<arg>

Server private key (and cert) file pass phrase source.

=item B<-srv_trusted> I<filenames>|I<uris>

Trusted certificates for client authentication.

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
have no effect on the certificate verification enabled via this option.

=item B<-srv_untrusted> I<filenames>|I<uris>

Intermediate CA certs that may be useful when validating client certificates.

=item B<-rsp_cert> I<filename>|I<uri>

Certificate to be returned as mock enrollment result.

=item B<-rsp_extracerts> I<filenames>|I<uris>

Extra certificates to be included in mock certification responses.

=item B<-rsp_capubs> I<filenames>|I<uris>

CA certificates to be included in mock Initialization Response (IP) message.

=item B<-poll_count> I<number>

Number of times the client must poll before receiving a certificate.

=item B<-check_after> I<number>

The checkAfter value (number of seconds to wait) to include in poll response.

=item B<-grant_implicitconf>

Grant implicit confirmation of newly enrolled certificate.

=item B<-pkistatus> I<number>

PKIStatus to be included in server response.
Valid range is 0 (accepted) .. 6 (keyUpdateWarning).

=item B<-failure> I<number>

A single failure info bit number to be included in server response.
Valid range is 0 (badAlg) .. 26 (duplicateCertReq).

=item B<-failurebits> I<number>
Number representing failure bits to be included in server response.
Valid range is 0 .. 2^27 - 1.

=item B<-statusstring> I<arg>

Text to be included as status string in server response.

=item B<-send_error>

Force server to reply with error message.

=item B<-send_unprotected>

Send response messages without CMP-level protection.

=item B<-send_unprot_err>

In case of negative responses, server shall send unprotected error messages,
certificate responses (IP/CP/KUP), and revocation responses (RP).
WARNING: This setting leads to behavior violating RFC 4210.

=item B<-accept_unprotected>

Accept missing or invalid protection of requests.

=item B<-accept_unprot_err>

Accept unprotected error messages from client.
So far this has no effect because the server does not accept any error messages.

=item B<-accept_raverified>

Accept RAVERIFED as proof of possession (POPO).

=back

=head2 Certificate verification options, for both CMP and TLS

=over 4

{- $OpenSSL::safe::opt_v_item -}

The certificate verification options
B<-verify_hostname>, B<-verify_ip>, and B<-verify_email>
only affect the certificate verification enabled via the B<-out_trusted> option.

=back

=head1 NOTES

When a client obtains from a CMP server CA certificates that it is going to
trust, for instance via the C<caPubs> field of a certificate response,
authentication of the CMP server is particularly critical.
So special care must be taken setting up server authentication
using B<-trusted> and related options for certificate-based authentication
or B<-secret> for MAC-based protection.

When setting up CMP configurations and experimenting with enrollment options
typically various errors occur until the configuration is correct and complete.
When the CMP server reports an error the client will by default
check the protection of the CMP response message.
Yet some CMP services tend not to protect negative responses.
In this case the client will reject them, and thus their contents are not shown
although they usually contain hints that would be helpful for diagnostics.
For assisting in such cases the CMP client offers a workaround via the
B<-unprotected_errors> option, which allows accepting such negative messages.

If OpenSSL was built with trace support enabled
and the environment variable B<OPENSSL_TRACE> includes B<HTTP>,
the request and response headers of HTTP transfers are printed.

=head1 EXAMPLES

=head2 Simple examples using the default OpenSSL configuration file

This CMP client implementation comes with demonstrative CMP sections
in the example configuration file F<openssl/apps/openssl.cnf>,
which can be used to interact conveniently with the Insta Demo CA.

In order to enroll an initial certificate from that CA it is sufficient
to issue the following shell commands.

  export OPENSSL_CONF=/path/to/openssl/apps/openssl.cnf

=begin comment

  wget 'http://pki.certificate.fi:8081/install-ca-cert.html/ca-certificate.crt\
        ?ca-id=632&download-certificate=1' -O insta.ca.crt

=end comment

  openssl genrsa -out insta.priv.pem
  openssl cmp -section insta

This should produce the file F<insta.cert.pem> containing a new certificate
for the private key held in F<insta.priv.pem>.
It can be viewed using, e.g.,

  openssl x509 -noout -text -in insta.cert.pem

In case the network setup requires using an HTTP proxy it may be given as usual
via the environment variable B<http_proxy> or via the B<-proxy> option in the
configuration file or the CMP command-line argument B<-proxy>, for example

  -proxy http://192.168.1.1:8080

In the Insta Demo CA scenario both clients and the server may use the pre-shared
secret I<insta> and the reference value I<3078> to authenticate to each other.

Alternatively, CMP messages may be protected in signature-based manner,
where the trust anchor in this case is F<insta.ca.crt>
and the client may use any certificate already obtained from that CA,
as specified in the B<[signature]> section of the example configuration.
This can be used in combination with the B<[insta]> section simply by

  openssl cmp -section insta,signature

By default the CMP IR message type is used, yet CR works equally here.
This may be specified directly at the command line:

  openssl cmp -section insta -cmd cr

or by referencing in addition the B<[cr]> section of the example configuration:

  openssl cmp -section insta,cr

In order to update the enrolled certificate one may call

  openssl cmp -section insta,kur

using MAC-based protection with PBM or

  openssl cmp -section insta,kur,signature

using signature-based protection.

In a similar way any previously enrolled certificate may be revoked by

  openssl cmp -section insta,rr -trusted insta.ca.crt

or

  openssl cmp -section insta,rr,signature

Many more options can be given in the configuration file
and/or on the command line.
For instance, the B<-reqexts> CLI option may refer to a section in the
configuration file defining X.509 extensions to use in certificate requests,
such as C<v3_req> in F<openssl/apps/openssl.cnf>:

  openssl cmp -section insta,cr -reqexts v3_req

=head2 Certificate enrollment

The following examples do not make use of a configuration file at first.
They assume that a CMP server can be contacted on the local TCP port 80
and accepts requests under the alias I</pkix/>.

For enrolling its very first certificate the client generates a client key
and sends an initial request message to the local CMP server
using a pre-shared secret key for mutual authentication.
In this example the client does not have the CA certificate yet,
so we specify the name of the CA with the B<-recipient> option
and save any CA certificates that we may receive in the C<capubs.pem> file.

In below command line usage examples the C<\> at line ends is used just
for formatting; each of the command invocations should be on a single line.

  openssl genrsa -out cl_key.pem
  openssl cmp -cmd ir -server 127.0.0.1:80/pkix/ -recipient "/CN=CMPserver" \
    -ref 1234 -secret pass:1234-5678 \
    -newkey cl_key.pem -subject "/CN=MyName" \
    -cacertsout capubs.pem -certout cl_cert.pem

=head2 Certificate update

Then, when the client certificate and its related key pair needs to be updated,
the client can send a key update request taking the certs in C<capubs.pem>
as trusted for authenticating the server and using the previous cert and key
for its own authentication.
Then it can start using the new cert and key.

  openssl genrsa -out cl_key_new.pem
  openssl cmp -cmd kur -server 127.0.0.1:80/pkix/ \
    -trusted capubs.pem \
    -cert cl_cert.pem -key cl_key.pem \
    -newkey cl_key_new.pem -certout cl_cert.pem
  cp cl_key_new.pem cl_key.pem

This command sequence can be repeated as often as needed.

=head2 Requesting information from CMP server

Requesting "all relevant information" with an empty General Message.
This prints information about all received ITAV B<infoType>s to stdout.

  openssl cmp -cmd genm -server 127.0.0.1/pkix/ -recipient "/CN=CMPserver" \
    -ref 1234 -secret pass:1234-5678

=head2 Using a custom configuration file

For CMP client invocations, in particular for certificate enrollment,
usually many parameters need to be set, which is tedious and error-prone to do
on the command line.
Therefore, the client offers the possibility to read
options from sections of the OpenSSL config file, usually called F<openssl.cnf>.
The values found there can still be extended and even overridden by any
subsequently loaded sections and on the command line.

After including in the configuration file the following sections:

  [cmp]
  server = 127.0.0.1
  path = pkix/
  trusted = capubs.pem
  cert = cl_cert.pem
  key = cl_key.pem
  newkey = cl_key.pem
  certout = cl_cert.pem

  [init]
  recipient = "/CN=CMPserver"
  trusted =
  cert =
  key =
  ref = 1234
  secret = pass:1234-5678-1234-567
  subject = "/CN=MyName"
  cacertsout = capubs.pem

the above enrollment transactions reduce to

  openssl cmp -section cmp,init
  openssl cmp -cmd kur -newkey cl_key_new.pem

and the above transaction using a general message reduces to

  openssl cmp -section cmp,init -cmd genm

=head1 SEE ALSO

L<openssl-genrsa(1)>, L<openssl-ecparam(1)>, L<openssl-list(1)>,
L<openssl-req(1)>, L<openssl-x509(1)>, L<x509v3_config(5)>

=head1 HISTORY

The B<cmp> application was added in OpenSSL 3.0.

The B<-engine option> was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
