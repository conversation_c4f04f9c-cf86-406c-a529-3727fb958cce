=pod

=head1 NAME

ASN1_STRING_new, ASN1_STRING_type_new, ASN1_STRING_free -
ASN1_STRING allocation functions

=head1 SYNOPSIS

 #include <openssl/asn1.h>

 ASN1_STRING * ASN1_STRING_new(void);
 ASN1_STRING * ASN1_STRING_type_new(int type);
 void ASN1_STRING_free(ASN1_STRING *a);

=head1 DESCRIPTION

ASN1_STRING_new() returns an allocated B<ASN1_STRING> structure. Its type
is undefined.

ASN1_STRING_type_new() returns an allocated B<ASN1_STRING> structure of
type B<type>.

ASN1_STRING_free() frees up B<a>.
If B<a> is NULL nothing is done.

=head1 NOTES

Other string types call the B<ASN1_STRING> functions. For example
ASN1_OCTET_STRING_new() calls ASN1_STRING_type(V_ASN1_OCTET_STRING).

=head1 RETURN VALUES

ASN1_STRING_new() and ASN1_STRING_type_new() return a valid
ASN1_STRING structure or B<NULL> if an error occurred.

ASN1_STRING_free() does not return a value.

=head1 SEE ALSO

L<ERR_get_error(3)>

=head1 COPYRIGHT

Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
