=pod

=head1 NAME

openssl-storeutl,
storeutl - STORE utility

=head1 SYNOPSIS

B<openssl> B<storeutl>
[B<-help>]
[B<-out file>]
[B<-noout>]
[B<-passin arg>]
[B<-text arg>]
[B<-engine id>]
[B<-r>]
[B<-certs>]
[B<-keys>]
[B<-crls>]
[B<-subject arg>]
[B<-issuer arg>]
[B<-serial arg>]
[B<-alias arg>]
[B<-fingerprint arg>]
[B<-I<digest>>]
B<uri> ...

=head1 DESCRIPTION

The B<storeutl> command can be used to display the contents (after decryption
as the case may be) fetched from the given URIs.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out filename>

specifies the output filename to write to or standard output by
default.

=item B<-noout>

this option prevents output of the PEM data.

=item B<-passin arg>

the key password source. For more information about the format of B<arg>
see L<openssl(1)/Pass Phrase Options>.

=item B<-text>

Prints out the objects in text form, similarly to the B<-text> output from
B<openssl x509>, B<openssl pkey>, etc.

=item B<-engine id>

specifying an engine (by its unique B<id> string) will cause B<storeutl>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed.
The engine will then be set as the default for all available algorithms.

=item B<-r>

Fetch objects recursively when possible.

=item B<-certs>

=item B<-keys>

=item B<-crls>

Only select the certificates, keys or CRLs from the given URI.
However, if this URI would return a set of names (URIs), those are always
returned.

=item B<-subject arg>

Search for an object having the subject name B<arg>.
The arg must be formatted as I</type0=value0/type1=value1/type2=...>.
Keyword characters may be escaped by \ (backslash), and whitespace is retained.
Empty values are permitted but are ignored for the search.  That is,
a search with an empty value will have the same effect as not specifying
the type at all.

=item B<-issuer arg>

=item B<-serial arg>

Search for an object having the given issuer name and serial number.
These two options I<must> be used together.
The issuer arg must be formatted as I</type0=value0/type1=value1/type2=...>,
characters may be escaped by \ (backslash), no spaces are skipped.
The serial arg may be specified as a decimal value or a hex value if preceded
by B<0x>.

=item B<-alias arg>

Search for an object having the given alias.

=item B<-fingerprint arg>

Search for an object having the given fingerprint.

=item B<-I<digest>>

The digest that was used to compute the fingerprint given with B<-fingerprint>.

=back

=head1 SEE ALSO

L<openssl(1)>

=head1 HISTORY

The B<openssl> B<storeutl> app was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
