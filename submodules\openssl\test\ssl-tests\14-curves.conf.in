# -*- mode: perl; -*-

## SSL test configurations

package ssltests;

use strict;
use warnings;

use OpenSSL::Test;
use OpenSSL::Test::Utils qw(anydisabled);

my @curves = ("sect163k1", "sect163r1", "sect163r2", "sect193r1",
              "sect193r2", "sect233k1", "sect233r1", "sect239k1",
              "sect283k1", "sect283r1", "sect409k1", "sect409r1",
              "sect571k1", "sect571r1", "secp160k1", "secp160r1",
              "secp160r2", "secp192k1", "prime192v1", "secp224k1",
              "secp224r1", "secp256k1", "prime256v1", "secp384r1",
              "secp521r1", "brainpoolP256r1", "brainpoolP384r1",
              "brainpoolP512r1", "X25519", "X448");

our @tests = ();

sub generate_tests() {
    foreach (0..$#curves) {
        my $curve = $curves[$_];
        push @tests, {
            name => "curve-${curve}",
            server => {
                "Curves" => $curve,
                # TODO(TLS1.3): Can we get this to work for TLSv1.3?
                "MaxProtocol" => "TLSv1.2"
            },
            client => {
                "CipherString" => "ECDHE",
                "MaxProtocol" => "TLSv1.2",
                "Curves" => $curve
            },
            test   => {
                "ExpectedTmpKeyType" => $curve,
                "ExpectedResult" => "Success"
            },
        };
    }
}

generate_tests();
