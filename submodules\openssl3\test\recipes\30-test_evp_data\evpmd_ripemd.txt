#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = RIPEMD160 tests

Availablein = legacy default
Digest = RIPEMD160
Input = ""
Output = 9c1185a5c5e9fc54612808977ee8f548b2258d31

Availablein = legacy default
Digest = RIPEMD160
Input = "a"
Output = 0bdc9d2d256b3ee9daae347be6f4dc835a467ffe

Availablein = legacy default
Digest = RIPEMD160
Input = "abc"
Output = 8eb208f7e05d987a9b044a8e98c6b087f15a0bfc

Availablein = legacy default
Digest = RIPEMD160
Input = "message digest"
Output = 5d0689ef49d2fae572b881b123a85ffa21595f36

Availablein = legacy default
Digest = RIPEMD160
Input = "abcdefghijklmnopqrstuvwxyz"
Output = f71c27109c692c1b56bbdceb5b9d2865b3708dbc

Availablein = legacy default
Digest = RIPEMD160
Input = "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
Output = 12a053384a9c0c88e405a06c27dcf49ada62eb2b

Availablein = legacy default
Digest = RIPEMD160
Input = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
Output = b0e20b6e3116640286ed3a87a5713079b21f5189

Availablein = legacy default
Digest = RIPEMD160
Input = "12345678901234567890123456789012345678901234567890123456789012345678901234567890"
Output = 9b752e45573d4b39f4dbd3323cab82bf63326bfb
