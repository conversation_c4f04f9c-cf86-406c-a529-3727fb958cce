*.py[cod]
MANIFEST
htmlcov

# C extensions
*.so
*.dylib

# Packages
*.egg
*.egg-info
dist
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64
__pycache__

# Installer logs
pip-log.txt

# Other logs
*.log

# Unit test / coverage reports
.coverage
coverage-html
.tox
nosetests.xml
t/
.hypothesis/

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

#vscode
.vscode

# Backup files
*.swp
*~
.idea
.cache
