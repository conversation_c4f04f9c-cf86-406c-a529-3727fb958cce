<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
<HEAD>
<meta name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1">
<link rel="stylesheet" type="text/css" href="Leash.css" />
<Title>Change Password</Title>
</HEAD>
<BODY>
<H1>Change Password </H1>

<p>

To change your password:</p>
<ol>
<li> Create or choose a strong new password. It's best to start with a phrase instead of a word. <br>
 <a href="HTML/Password_Tips.htm">About: Password Tips and Examples</a></li>

<li>If you have multiple <a href="JavaScript:popup.TextPopup(popupPrincipal, popfont,9,9,-1,-1)">principals</a>, you can select the principal you want to manage by clicking it in the main window. Or you can wait and  enter your principal in the Change Password window. </li>

<li>Click the Change Password button in the ribbon menu at the of the main window.</li>

<li> Verify your principal (your Kerberos identity) or enter a different one.  </li>

<li>Enter your current password.</li>

<li> Enter your new password twice. You must enter it the second time to make sure you have typed it correctly, since you cannot visually confirm what you have typed.</li>

<li>Click Okay.</li>

</ol>


<H2>Related Help</H2>
<ul id="helpul">
<li><a href="HTML/Passwords.htm">About passwords</a></li>
<li><a href="HTML/Password_Tips.htm">Password tips and examples</a></li>
<li><a href="HTML/Forget_Password.htm">If you forget your password</a></li>


</ul>

<SCRIPT Language=JavaScript>
popfont="Arial,.625,"
popupPrincipal="Your principal is your Kerberos identity. It is your user name combined with the Kerberos realm you are using. For example: '<EMAIL>' "
</SCRIPT>

<OBJECT id=popup type="application/x-oleobject"
classid="clsid:adb880a6-d8ff-11cf-9377-00aa003b7a11">
</OBJECT>
</BODY>
</HTML>
