﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ack_tracker.c" />
    <ClCompile Include="api.c" />
    <ClCompile Include="bbr.c" />
    <ClCompile Include="binding.c" />
    <ClCompile Include="configuration.c" />
    <ClCompile Include="congestion_control.c" />
    <ClCompile Include="connection.c" />
    <ClCompile Include="crypto.c" />
    <ClCompile Include="crypto_tls.c" />
    <ClCompile Include="cubic.c" />
    <ClCompile Include="datagram.c" />
    <ClCompile Include="frame.c" />
    <ClCompile Include="injection.c" />
    <ClCompile Include="library.c" />
    <ClCompile Include="listener.c" />
    <ClCompile Include="lookup.c" />
    <ClCompile Include="loss_detection.c" />
    <ClCompile Include="mtu_discovery.c" />
    <ClCompile Include="operation.c" />
    <ClCompile Include="packet.c" />
    <ClCompile Include="packet_builder.c" />
    <ClCompile Include="packet_space.c" />
    <ClCompile Include="path.c" />
    <ClCompile Include="range.c" />
    <ClCompile Include="recv_buffer.c" />
    <ClCompile Include="registration.c" />
    <ClCompile Include="send.c" />
    <ClCompile Include="send_buffer.c" />
    <ClCompile Include="sent_packet_metadata.c" />
    <ClCompile Include="settings.c" />
    <ClCompile Include="sliding_window_extremum.c" />
    <ClCompile Include="stream.c" />
    <ClCompile Include="stream_recv.c" />
    <ClCompile Include="stream_send.c" />
    <ClCompile Include="stream_set.c" />
    <ClCompile Include="timer_wheel.c" />
    <ClCompile Include="version_neg.c" />
    <ClCompile Include="worker.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ack_tracker.h" />
    <ClInclude Include="api.h" />
    <ClInclude Include="bbr.h" />
    <ClInclude Include="binding.h" />
    <ClInclude Include="cid.h" />
    <ClInclude Include="configuration.h" />
    <ClInclude Include="congestion_control.h" />
    <ClInclude Include="connection.h" />
    <ClInclude Include="crypto.h" />
    <ClInclude Include="cubic.h" />
    <ClInclude Include="datagram.h" />
    <ClInclude Include="frame.h" />
    <ClInclude Include="library.h" />
    <ClInclude Include="listener.h" />
    <ClInclude Include="lookup.h" />
    <ClInclude Include="loss_detection.h" />
    <ClInclude Include="mtu_discovery.h" />
    <ClInclude Include="operation.h" />
    <ClInclude Include="packet.h" />
    <ClInclude Include="packet_builder.h" />
    <ClInclude Include="packet_space.h" />
    <ClInclude Include="path.h" />
    <ClInclude Include="precomp.h" />
    <ClInclude Include="quicdef.h" />
    <ClInclude Include="range.h" />
    <ClInclude Include="recv_buffer.h" />
    <ClInclude Include="registration.h" />
    <ClInclude Include="send.h" />
    <ClInclude Include="send_buffer.h" />
    <ClInclude Include="sent_packet_metadata.h" />
    <ClInclude Include="settings.h" />
    <ClInclude Include="sliding_window_extremum.h" />
    <ClInclude Include="stream.h" />
    <ClInclude Include="stream_set.h" />
    <ClInclude Include="timer_wheel.h" />
    <ClInclude Include="transport_params.h" />
    <ClInclude Include="version_neg.h" />
    <ClInclude Include="worker.h" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E862BE76-298B-4021-8105-A3E115FA2900}</ProjectGuid>
    <TemplateGuid>{0a049372-4c4d-4ea0-a64e-dc6ad88ceca1}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <DriverType>KMDF</DriverType>
    <UseInternalMSUniCrtPackage>true</UseInternalMSUniCrtPackage>
    <UndockedKernelModeBuild>true</UndockedKernelModeBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <_NT_TARGET_VERSION>0x0A00000A</_NT_TARGET_VERSION>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <QUIC_VER_BUILD_ID Condition="'$(QUIC_VER_BUILD_ID)' == ''">0</QUIC_VER_BUILD_ID>
    <QUIC_VER_SUFFIX Condition="'$(QUIC_VER_SUFFIX)' == ''">-private</QUIC_VER_SUFFIX>
    <QUIC_VER_GIT_HASH Condition="'$(QUIC_VER_GIT_HASH)' == ''">0</QUIC_VER_GIT_HASH>
  </PropertyGroup>
  <PropertyGroup>
    <RunCodeAnalysis>true</RunCodeAnalysis>
    <CodeAnalysisTreatWarningsAsErrors>true</CodeAnalysisTreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <TargetName>core</TargetName>
    <OutDir>$(SolutionDir)build\winkernel\$(Platform)_$(Configuration)_schannel\bin\</OutDir>
    <IntDir>$(SolutionDir)build\winkernel\$(Platform)_$(Configuration)_schannel\obj\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup>
    <ExternalIncludePath />
  </PropertyGroup>
  <PropertyGroup Condition="'$(ONEBRANCH_BUILD)' != ''">
    <ApiValidator_Enable>false</ApiValidator_Enable>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>..\inc;$(SolutionDir)build\winkernel\$(Platform)_$(Configuration)_schannel\inc;$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <AdditionalOptions Condition="'$(Platform)'!='x64'">/Gw /kernel /ZH:SHA_256</AdditionalOptions>
      <AdditionalOptions Condition="'$(Platform)'=='x64'">/Gw /kernel /ZH:SHA_256 -d2jumptablerdata -d2epilogunwindrequirev2</AdditionalOptions>
    </ClCompile>
    <Lib>
      <LinkTimeCodeGeneration>true</LinkTimeCodeGeneration>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PreprocessorDefinitions>VER_BUILD_ID=$(QUIC_VER_BUILD_ID);VER_SUFFIX=$(QUIC_VER_SUFFIX);VER_GIT_HASH=$(QUIC_VER_GIT_HASH);QUIC_EVENTS_MANIFEST_ETW;QUIC_LOGS_MANIFEST_ETW;QUIC_TELEMETRY_ASSERTS=1;SECURITY_KERNEL;SECURITY_WIN32;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>VER_BUILD_ID=$(QUIC_VER_BUILD_ID);VER_SUFFIX=$(QUIC_VER_SUFFIX);VER_GIT_HASH=$(QUIC_VER_GIT_HASH);QUIC_EVENTS_MANIFEST_ETW;QUIC_LOGS_MANIFEST_ETW;QUIC_TELEMETRY_ASSERTS=1;SECURITY_KERNEL;SECURITY_WIN32;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
