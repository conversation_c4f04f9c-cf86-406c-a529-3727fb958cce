<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
<HEAD>
<meta name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1">
<link rel="stylesheet" type="text/css" href="Leash.css" />
<Title>Password Tips</Title>

</HEAD>
<BODY>
<H1>Password Tips and Examples</H1>
<p>
As computing power has gotten faster and cheaper, password cracking generators have gotten better as well. A password cracking program can rapidly try all English words, all combinations of seven or fewer characters, and common passwords such as <tt>12345678</tt> and <tt>password123</tt>. In addition, anyone with access to your personal information can try names and dates from that information.   </p>

<H3>Good Password Requirements</H3>
<p>
A strong password:  </p>
<ul>
<li>Is at least 8 characters long (preferably longer)</li>
<li>Doesn't include your name or other easily obtained personal information</li>
<li>Uses a mix of lower case letters, uppercase letters, numbers, and symbols </li>
<li>Is only used for one program </li>
</ul>
<H3>Password Advice and Examples</H3>
<p>
To create a strong password that is still easy to remember, try starting with a phrase or sentence. Then play around with symbols, shorthands, and misspellings to make it more secure.  Remember that you can have spaces in your password. Some examples:</p>
<ul>
<li> "Beans and rice are my favorite foods" can become <tt>Beans&ricearemyFavoriteFoods!</tt>, <tt>Rbeans&ricemyfavoritefoods?</tt> or <tt>BeansNRiceRFavz!</tt></li>
<li>"I can't wait 2 go to Spain" can become <tt>Ican'twait2go2Spain!</tt></li>
<li>"Meet me at the store" can become <tt>mEEtme@zeStore</tt></li>
<li>"Cat or dog?" can become <tt>?KatsRd0gs</tt> or you can leave it as is. </li>
</ul>
<H3>What Makes a Bad Password</H3>
<p>
Do <b>not</b> base your password on any of the following. They are far too easy to guess (even if you spell them backwards).</p>
<ul>
<li>Any names, including yours or that of your parents, children, pets, friends, characters from popular media, etc.</li>
<li> Your phone number, address, birthday, etc.</li>
<li>Your social security, drivers license, or license plate numbers. </li>
<li> One or two words found in a dictionary.</li>
<li> The phrases "Let me in," "open up," or something similar.</li>
<li> Simple patterns like "lolololololo" or "12345678." </li>
<li> Any password used as an example in a manual or in help (including the examples given here).</li>
<li> A password that you use elsewhere, especially an insecure program or website.</li>
</ul>
<H3>Related Help</H3>
<ul id="helpul">
<li><a href="HTML/Change_Password.htm">Change Password</a></li>
<li><a href="HTML/Forget_Password.htm">If you forget your password</a></li>
</ul>
</BODY>
</HTML>
