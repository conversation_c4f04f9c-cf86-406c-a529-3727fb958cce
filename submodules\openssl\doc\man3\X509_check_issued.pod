=pod

=head1 NAME

X509_check_issued - checks if certificate is apparently issued by another
certificate

=head1 SYNOPSIS

 #include <openssl/x509v3.h>

 int X509_check_issued(X509 *issuer, X509 *subject);


=head1 DESCRIPTION

X509_check_issued() checks if certificate I<subject> was apparently issued
using (CA) certificate I<issuer>. This function takes into account not only
matching of the issuer field of I<subject> with the subject field of I<issuer>,
but also compares all sub-fields of the B<authorityKeyIdentifier> extension of
I<subject>, as far as present, with the respective B<subjectKeyIdentifier>,
serial number, and issuer fields of I<issuer>, as far as present. It also checks
if the B<keyUsage> field (if present) of I<issuer> allows certificate signing.
It does not check the certificate signature.

=head1 RETURN VALUES

Function return B<X509_V_OK> if certificate I<subject> is issued by
I<issuer> or some B<X509_V_ERR*> constant to indicate an error.

=head1 SEE ALSO

L<X509_verify_cert(3)>,
L<X509_check_ca(3)>,
L<verify(1)>

=head1 COPYRIGHT

Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
