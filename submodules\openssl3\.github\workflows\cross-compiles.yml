# Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: Cross Compile

on: [pull_request, push]

permissions:
  contents: read

jobs:
  cross-compilation:
    strategy:
      fail-fast: false
      matrix:
        # The platform matrix specifies:
        #   arch: the architecture to build for, this defines the tool-chain
        #         prefix {arch}- and the Debian compiler package gcc-{arch}
        #         name.
        #   libs: the Debian package for the necessary link/runtime libraries.
        #   target: the OpenSSL configuration target to use, this is passed
        #           directly to the config command line.
        #   fips:   set to "no" to disable building FIPS, leave unset to
        #           build the FIPS provider.
        #   tests: omit this to run all the tests using QEMU, set it to "none"
        #          to never run the tests, otherwise its value is passed to
        #          the "make test" command to allow selective disabling of
        #          tests.
        #   qemucpu: optional; string that describes CPU properties.
        #            The string will be used to set the QEMU_CPU variable.
        #   opensslcapsname: optional; string that describes the postfix of the
        #                    OpenSSL environment variable that defines CPU
        #                    capabilities. E.g. "foo" will result in an
        #                    environment variable with the name OPENSSL_foo.
        #   opensslcaps: optional; if opensslcapsname (see above) is set, then
        #                this string will be used as content for the OpenSSL
        #                capabilities variable.
        #   ppa:   Launchpad PPA repository to download packages from.
        platform: [
          {
            arch: i386-pc-msdosdjgpp,
            libs: libc-djgpp-dev libwatt-djgpp-dev djgpp-utils,
            target: no-threads 386 DJGPP,
            tests: none,
            ppa: jwt27/djgpp-toolchain
          }, {
            arch: aarch64-linux-gnu,
            libs: libc6-dev-arm64-cross,
            target: linux-aarch64
          }, {
            arch: alpha-linux-gnu,
            libs: libc6.1-dev-alpha-cross,
            target: linux-alpha-gcc
          }, {
            arch: arm-linux-gnueabi,
            libs: libc6-dev-armel-cross,
            target: linux-armv4,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: arm-linux-gnueabihf,
            libs: libc6-dev-armhf-cross,
            target: linux-armv4,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: hppa-linux-gnu,
            libs: libc6-dev-hppa-cross,
            target: -static linux-generic32,
            fips: no,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: m68k-linux-gnu,
            libs: libc6-dev-m68k-cross,
            target: -static -m68040 linux-latomic -Wno-stringop-overflow,
            fips: no,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: mips-linux-gnu,
            libs: libc6-dev-mips-cross,
            target: -static linux-mips32,
            fips: no,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: mips64-linux-gnuabi64,
            libs: libc6-dev-mips64-cross,
            target: -static linux64-mips64,
            fips: no
          }, {
            arch: mipsel-linux-gnu,
            libs: libc6-dev-mipsel-cross,
            target: linux-mips32,
            tests: -test_includes -test_store -test_x509_store
          }, {
            arch: powerpc64le-linux-gnu,
            libs: libc6-dev-ppc64el-cross,
            # The default compiler for this platform on Ubuntu 20.04 seems
            # buggy and causes test failures. Dropping the optimisation level
            # resolves it.
            target: -O2 linux-ppc64le
          }, {
            arch: riscv64-linux-gnu,
            libs: libc6-dev-riscv64-cross,
            target: linux64-riscv64
          }, {
            arch: s390x-linux-gnu,
            libs: libc6-dev-s390x-cross,
            target: linux64-s390x -Wno-stringop-overflow
          }, {
            arch: sh4-linux-gnu,
            libs: libc6-dev-sh4-cross,
            target: no-async linux-latomic,
            tests: -test_includes -test_store -test_x509_store
          },

          # These build with shared libraries but they crash when run
          # They mirror static builds above in order to cover more of the
          # code base.
          {
            arch: hppa-linux-gnu,
            libs: libc6-dev-hppa-cross,
            target: linux-generic32,
            tests: none
          }, {
            arch: m68k-linux-gnu,
            libs: libc6-dev-m68k-cross,
            target: -mcfv4e linux-latomic -Wno-stringop-overflow,
            tests: none
          }, {
            arch: mips-linux-gnu,
            libs: libc6-dev-mips-cross,
            target: linux-mips32,
            tests: none
          }, {
            arch: mips64-linux-gnuabi64,
            libs: libc6-dev-mips64-cross,
            target: linux64-mips64,
            tests: none
          },

          # This build doesn't execute either with or without shared libraries.
          {
            arch: sparc64-linux-gnu,
            libs: libc6-dev-sparc64-cross,
            target: linux64-sparcv9,
            tests: none
          }
        ]
    runs-on: ubuntu-latest
    steps:
    - name: install package repository
      if: matrix.platform.ppa != ''
      run: |
        sudo add-apt-repository ppa:${{ matrix.platform.ppa }}
    - name: install packages
      run: |
        sudo apt-get update
        sudo apt-get -yq --force-yes install \
            gcc-${{ matrix.platform.arch }} \
            ${{ matrix.platform.libs }}
    - uses: actions/checkout@v4

    - name: config with FIPS
      if: matrix.platform.fips != 'no'
      run: |
        ./config --banner=Configured --strict-warnings enable-fips \
                 --cross-compile-prefix=${{ matrix.platform.arch }}- \
                 ${{ matrix.platform.target }}
    - name: config without FIPS
      if: matrix.platform.fips == 'no'
      run: |
        ./config --banner=Configured --strict-warnings \
                 --cross-compile-prefix=${{ matrix.platform.arch }}- \
                 ${{ matrix.platform.target }}
    - name: config dump
      run: ./configdata.pm --dump

    - name: make
      run: make -s -j4

    - name: install qemu
      if: github.event_name == 'push' && matrix.platform.tests != 'none'
      run: sudo apt-get -yq --force-yes install qemu-user

    - name: Set QEMU environment
      if: github.event_name == 'push' && matrix.platform.qemucpu != ''
      run: echo "QEMU_CPU=${{ matrix.platform.qemucpu }}" >> $GITHUB_ENV

    - name: Set OpenSSL caps environment
      if: github.event_name == 'push' && matrix.platform.opensslcapsname != ''
      run: echo "OPENSSL_${{ matrix.platform.opensslcapsname }}=\
                 ${{ matrix.platform.opensslcaps }}" >> $GITHUB_ENV

    - name: make all tests
      if: github.event_name == 'push' && matrix.platform.tests == ''
      run: |
        make test HARNESS_JOBS=${HARNESS_JOBS:-4} \
                  TESTS="-test_afalg" \
                  QEMU_LD_PREFIX=/usr/${{ matrix.platform.arch }}
    - name: make some tests
      if: github.event_name == 'push' && matrix.platform.tests != 'none' && matrix.platform.tests != ''
      run: |
        make test HARNESS_JOBS=${HARNESS_JOBS:-4} \
                  TESTS="${{ matrix.platform.tests }} -test_afalg" \
                  QEMU_LD_PREFIX=/usr/${{ matrix.platform.arch }}
