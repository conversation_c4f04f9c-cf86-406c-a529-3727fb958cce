// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#ifndef FSSOCKET_COMMON_H
#define FSSOCKET_COMMON_H

#ifdef _WIN32
#ifndef NOMINMAX
#define NOMINMAX
#endif
#include <winSock2.h>
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>
#define FS_SOCKET SOCKET
//#define socklen_t int
#define CLOSE_SOCKET(s) closesocket(s)
#define GET_LAST_ERROR() WSAGetLastError()

#define ERR_WOULDBLOCK WSAEWOULDBLOCK
#define ERR_INTERRUPTED WSAEINTR
#define ERR_AGAIN WSAEWOULDBLOCK
#define ERR_TIMEDOUT WSAETIMEDOUT
#define ERR_INPROGRESS WSAEINPROGRESS

#else
#include <arpa/inet.h>
#include <errno.h>
#include <netdb.h>
#include <netinet/in.h>
#include <poll.h>
#include <pthread.h>
#include <signal.h>
#include <sys/param.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/uio.h>
#include <unistd.h>
#define FS_SOCKET int
#define SOCKET int
//#define INVALID_SOCKET -1
//#define SOCKET_ERROR -1
#define CLOSE_SOCKET(s) close(s)
#define GET_LAST_ERROR() errno
#define ERR_WOULDBLOCK EWOULDBLOCK
#define ERR_INTERRUPTED EINTR
#define ERR_AGAIN EAGAIN
#define ERR_TIMEDOUT ETIMEDOUT
#define ERR_INPROGRESS EINPROGRESS
#endif  // end of _WIN32
#define E_UNSUPPORT -1
#endif  // end of FSSOCKET_COMMON_H
