# Extend the list of the allowed enctypes and salts as needed
Types:
    supported_enctypes:
        '(aes256-cts-hmac-sha1-96|aes256-cts|aes128-cts-hmac-sha1-96|aes128-cts|des3-hmac-sha1|des3-cbc-raw|des3-cbc-sha1|des3-hmac-sha1|rc4-hmac|arcfour-hmac-md5)(:(normal|v4))?$'
    default_tgs_enctypes:
        '(aes256-cts-hmac-sha1-96|aes256-cts|aes128-cts-hmac-sha1-96|aes128-cts|des3-hmac-sha1|des3-cbc-raw|des3-cbc-sha1|des3-hmac-sha1|rc4-hmac|arcfour-hmac-md5)'
    default_tkt_enctypes:
        '(aes256-cts-hmac-sha1-96|aes256-cts|aes128-cts-hmac-sha1-96|aes128-cts|des3-hmac-sha1|des3-cbc-raw|des3-cbc-sha1|des3-hmac-sha1|rc4-hmac|arcfour-hmac-md5)'

# Add all valid profile attributes that are not listed in k5-int.h 
Attributes:
    - logging
    - dbmodules
