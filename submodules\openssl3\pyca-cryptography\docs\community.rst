Community
=========

You can find ``cryptography`` all over the web:

* `Mailing list`_
* `Source code`_
* `Issue tracker`_
* `Documentation`_
* IRC: ``#pyca`` on ``irc.libera.chat``

Wherever we interact, we adhere to the `Python Community Code of Conduct`_.


.. _`Mailing list`: https://mail.python.org/mailman/listinfo/cryptography-dev
.. _`Source code`: https://github.com/pyca/cryptography
.. _`Issue tracker`: https://github.com/pyca/cryptography/issues
.. _`Documentation`: https://cryptography.io/
.. _`Python Community Code of Conduct`: https://www.python.org/psf/codeofconduct/
