Resources
=========

Mailing lists
-------------

* <EMAIL> is a community resource for discussion and
  questions about MIT krb5 and other Kerberos implementations.  To
  subscribe to the list, please follow the instructions at
  https://mailman.mit.edu/mailman/listinfo/kerberos.
* <EMAIL> is the primary list for developers of MIT Kerberos.
  To subscribe to the list, please follow the instructions at
  https://mailman.mit.edu/mailman/listinfo/krbdev.
* <EMAIL> is notified when a ticket is created or updated.
  This list helps track bugs and feature requests.
  In addition, this list is used to track documentation criticism
  and recommendations for improvements.
* <EMAIL> is a private list for the MIT krb5 core team.  Send
  mail to this list if you need to contact the core team.
* <EMAIL> is the point of contact for security problems
  with MIT Kerberos.  Please use PGP-encrypted mail to report possible
  vulnerabilities to this list.


IRC channels
------------

The IRC channel `#kerberos` on irc.freenode.net is a community
resource for general Kerberos discussion and support.

The main IRC channel for MIT Kerberos development is `#krbdev` on
freenode.

For more information about freenode, see https://freenode.net/.


Archives
--------

* The archive https://mailman.mit.edu/pipermail/kerberos/ contains
  past postings from the `<EMAIL>` list.

* The https://mailman.mit.edu/pipermail/krbdev/ contains past postings
  from the `<EMAIL>` list.


Wiki
----

The wiki at https://k5wiki.kerberos.org/ contains useful information
for developers working on the MIT Kerberos source code.  Some of the
information on the wiki may be useful for advanced users or system
administrators.

Web pages
---------

* https://web.mit.edu/kerberos/ is the MIT Kerberos software web page.

* https://kerberos.org/ is the MIT Kerberos Consortium web page.
