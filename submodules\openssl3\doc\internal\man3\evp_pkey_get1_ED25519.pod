=pod

=head1 NAME

evp_pkey_get1_ED25519, evp_pkey_get1_ED448,
evp_pkey_get1_X25519, evp_pkey_get1_X448
- internal ECX from EVP_PKEY getter functions

=head1 SYNOPSIS

 #include "internal/evp.h"

 ECX_KEY *evp_pkey_get1_ED25519(EVP_PKEY *pkey);
 ECX_KEY *evp_pkey_get1_ED448(EVP_PKEY *pkey);
 ECX_KEY *evp_pkey_get1_X25519(EVP_PKEY *pkey);
 ECX_KEY *evp_pkey_get1_X448(EVP_PKEY *pkey);

=head1 DESCRIPTION

evp_pkey_get1_ED25519(), evp_pkey_get1_ED448(), evp_pkey_get1_X25519() and
evp_pkey_get1_X448() return the referenced key in I<pkey> or NULL if the key
is not of the correct type.  The returned key must be freed after use.

=head1 RETURN VALUES

evp_pkey_get1_ED25519(), evp_pkey_get1_ED448(), evp_pkey_get1_X25519() and
evp_pkey_get1_X448() return the referenced key or NULL if an error
occurred.

=head1 HISTORY

This functionality was added to OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use this
file except in compliance with the License.  You can obtain a copy in the file
LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
