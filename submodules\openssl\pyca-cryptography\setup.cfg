[metadata]
name = cryptography
version = attr: cryptography.__version__
description = cryptography is a package which provides cryptographic recipes and primitives to Python developers.
long_description = file: README.rst
long_description_content_type = text/x-rst
license = BSD or Apache License, Version 2.0
url = https://github.com/pyca/cryptography
author = The Python Cryptographic Authority and individual contributors
author_email = <EMAIL>
project_urls =
    Documentation=https://cryptography.io/
    Source=https://github.com/pyca/cryptography/
    Issues=https://github.com/pyca/cryptography/issues
    Changelog=https://cryptography.io/en/latest/changelog/
classifiers =
    Development Status :: 5 - Production/Stable
    Intended Audience :: Developers
    License :: OSI Approved :: Apache Software License
    License :: OSI Approved :: BSD License
    Natural Language :: English
    Operating System :: MacOS :: MacOS X
    Operating System :: POSIX
    Operating System :: POSIX :: BSD
    Operating System :: POSIX :: Linux
    Operating System :: Microsoft :: Windows
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: Implementation :: CPython
    Programming Language :: Python :: Implementation :: PyPy
    Topic :: Security :: Cryptography

[options]
python_requires = >=3.6
include_package_data = True
zip_safe = False
package_dir =
    =src
packages = find:
ext_package = cryptography.hazmat.bindings
# `install_requires` and `setup_requires` must be kept in sync with
# `pyproject.toml`
install_requires =
    cffi >=1.12
setup_requires =
    cffi >=1.12
    setuptools_rust >= 0.11.4

[options.packages.find]
where = src
exclude =
    _cffi_src
    _cffi_src.*

[options.extras_require]
test =
    pytest>=6.2.0
    pytest-cov
    pytest-subtests
    pytest-xdist
    pretend
    iso8601
    pytz
    hypothesis>=1.11.4,!=3.79.2
docs =
    sphinx >= 1.6.5,!=1.8.0,!=3.1.0,!=3.1.1
    sphinx_rtd_theme
docstest =
    doc8
    pyenchant >= 1.6.11
    twine >= 1.12.0
    sphinxcontrib-spelling >= 4.0.1
sdist =
    setuptools_rust >= 0.11.4
pep8test =
    black
    flake8
    flake8-import-order
    pep8-naming
# This extra is for OpenSSH private keys that use bcrypt KDF
# Versions: v3.1.3 - ignore_few_rounds, v3.1.5 - abi3
ssh =
    bcrypt >= 3.1.5
