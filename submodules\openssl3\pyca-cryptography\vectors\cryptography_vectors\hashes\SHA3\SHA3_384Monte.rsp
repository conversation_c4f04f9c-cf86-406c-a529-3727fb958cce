#  CAVS 19.0
#  "SHA3-384 Monte" information for "SHA3AllBytes1-28-16"
#  SHA3-384 tests are configured for BYTE oriented implementations
#  Length values represented in bits
#  Generated on Thu Jan 28 13:32:46 2016

[L = 384]

Seed = 7a00791f6f65c21f1c97c58fa3c0520cfc85cd7e3d398cf01950819fa717195065a363e77d07753647cb0c130e9972ad

COUNT = 0
MD = b2d4e10214bd7991e3a3e4772f5c7b390178e20c3ff882648a891e44b9d309d91bf5fab74c0bc155a7fac972a9b128a2

COUNT = 1
MD = 608db3176176effb7b7cceb8962bffc67584cc9e9860752f6644c7810cc83f4fdefa108bcf308d4137265fbb1ecf10fb

COUNT = 2
MD = c6c0d9c39fbd7cab9b96085f65420d53c0e1ca4f8b79edda3177a20d7a605ee4bc4f1e2589979867507736c39280901f

COUNT = 3
MD = 223cb4fb8c18b420df2ce25ed1b60c0d60d324234a0c311717058d0285ed14e02077d56a861e0e2a6474d39ed0bd8147

COUNT = 4
MD = a9ed13848adb2015465a26503d8c3b922400fc2e4dadb579da5115f96eff65151866bca58899297fee787de9305865a2

COUNT = 5
MD = 250160585ebbad4ada427e52b462af268ba791b03ea5e2ead06e51e1763eaade36addcdd7f2d2ca905584afee580787f

COUNT = 6
MD = 9ca20cfb96101503380d7356630af3f8c6c183c5c7c73f861144e8f000194560a0ca4913e61262599b88d0ddebfaf297

COUNT = 7
MD = a641d3958798f05269c10999c496ea6324e178c1cea3df8fcf6fb7b8570b30d08b3a724617036e3d7e493c185bd8dd59

COUNT = 8
MD = 361d41e02a7d9c4a2743e0e0ddfede8b917f216a375b44993c4da662f503d7fe5e3ea940102aff4676dbfbbab83ba0d9

COUNT = 9
MD = f2072c4e0efe91519abf3efaf73d9f7dfed66b55c82cf49e46f12695885e3107b0b91321b0ad984104f8530c0f11bb12

COUNT = 10
MD = dc204edcd9b381cf7362c5292a2dcbdc8234b46cfa0c38ea010a0eb4a1b135d6ae9f68724efb9f160b0bb3c4e5656e35

COUNT = 11
MD = 1c04cb6989b8070156bdaf607e2e53537df571558cfdb021ef8eb241186cb31be44b42279f42ecc1f8740f5678ff9bc5

COUNT = 12
MD = 328fffcd1f4a9ad876a1b9ae71289e8dc4305138c343ea94cec62225fa4c20bb8f831da333517bfcbe3fe742920abc69

COUNT = 13
MD = 85dbf0106bf464b2996f70a44d153f835d4a1ed775f71609bbbe51fe40a8f97785bdd65256d54faed5e05282e365a5cf

COUNT = 14
MD = fb9eee3197db0d6f0e771827c0f9846b7b989e6c035f3e89abaa2e2dcdd3e8a2651b3e2518fb5d287e2bc0449c8f5c3f

COUNT = 15
MD = 249a8b718ed3818a0873f92df3a20b2ac3e0f796c9b6f7204933e01167a9c3f82b82cc2fb2d8073ad864474b6dcbe611

COUNT = 16
MD = 9fe3eeb06de2263bbbf08f0e20e9b9ff8ac0f6ba96316235d6aba199e30d60d8bd8a878f674c7d0ad7cb05b2a5cda927

COUNT = 17
MD = d6298b466c189403382a8eb98dd9a54d0d39d59ae8f5e5d620ef418c55031de5124cb9bf6fa931570292dfb618c4dff4

COUNT = 18
MD = 6a423009ae0859052030f636fca51159a2d3414a6e025c12c811b3f2d8f880af2741c4e059f571339c8016de8b073608

COUNT = 19
MD = 4f31053aa710a9376fa2a410e3458c1b4d9005c66ae01f41c093996f9a8b5c6885467acd9ad4b4bbbe1fa32d24ace547

COUNT = 20
MD = 77a296b5cb27d6d33365d65c6d49e6e27b50888adf2bd8581eac8266a7a182ca05498c65276e5eecbf297ab61c834881

COUNT = 21
MD = ed319f492a16cca4d5ba30381e1ec08c0b66f34bf306b21a651e4693e171598ac9cefaa1f2c3891d5c688bedbeb3dfd1

COUNT = 22
MD = 400577d24b8eeae14eacd5df544b2567bcbca4884678925acb3722ad56cea947989665de03fd86f9550af0b272c36c95

COUNT = 23
MD = 774ec2c381923a8898e798942b6ab7b9689f316aa83b8cf36af9192c7750b239ac72c8466e6c43997c7d0b124fbb86a4

COUNT = 24
MD = b64d7cd2cca30cad4f9a6f7ca3b39d913479a4fabf3b02eb1c0636f91bbece91a9855e3999fa9358836c88d253191494

COUNT = 25
MD = 58e0a41eff4b33bbcb1f8be2406345532be643dc7e0dc1319b576f7d9eeb645fbe1aba0ab8f42ef49ae11e37dceca507

COUNT = 26
MD = e891ad66e4da1ffa63793cff8b36f5c28ca19c51bfe77709fe678c415f4a5895fa7c778ebf915258adff2777adffa2cf

COUNT = 27
MD = 4ce67891990de0607bba2b3f7f8c09f036523308bc54d4bb404af382667a933ce2d86bec0b315be538e2d6003e4ebf6a

COUNT = 28
MD = 4793cb36391bfb59a5d65cc1eefa421903631a558a544f6276d9d6bad0e826aad41ab892b133267ee2215a6e3520583d

COUNT = 29
MD = 963eb0c68937a5b282cb86125b9515cc78c4182a04daba0df72a5095d906b149024318b06c6639fafdccc8c4ca1c8b9d

COUNT = 30
MD = 36a9cd9b49a33b3115da7b0b862988a0275b2cf3241b4d4a135dfbc8c1983454ac122822e7556818edc5ae4267d0f13d

COUNT = 31
MD = f658aca80477b04e85ac6470c685acee6e8ad6563a6d4a903865b07a99648a2e9749d82bb187a8ce01636244ad30a3d2

COUNT = 32
MD = aefc4b9fd0d99fd6eef4a0f5099cebd9f409a7fce251bb675016d830e7787e812ae90452a33d9ba8d41f9ec40716ddc1

COUNT = 33
MD = b31d8342fe8c5adaecca08fee1d97adcf74131cb67505c155957f740dd2ee3ec7099bc271143e9785f2d7e99f15d1013

COUNT = 34
MD = ee8ffcf5e1455283c1cedabb355489a49de5207a138e29ab9b0b8af5eab6938d1dee47f6315a5e2697b58a39499a2574

COUNT = 35
MD = 05db22799cba19e8a88cc8b07c96725a71f518081cc7c71970f9a132f95f2e2dbbe020a5a227c1eb6927fe53dfd5ec63

COUNT = 36
MD = 6ca724f20838d1a485d698d07d967bda095dd79f56372986eff7fc7a23e9f99e7c3b44a46604979806687c9c1a4c8fd2

COUNT = 37
MD = 05340677c8620a2fd171caf84f344cd83f837ed5d544d1d3c9d116615e9b5624081709e8db92d3fb0a8f771b521f11b9

COUNT = 38
MD = a0e2be50601a3fbc16b49305f5e695e978b971b2c068596fa9bd13f7e7ed3e8c1205313f8a23839625336dbc5d2d6f83

COUNT = 39
MD = 6a997c32ae605df471e7922e7870ec4712beb7d5bf47104462a00e2a1832166ebdecef3455c1af0bc132ca4ca7624db0

COUNT = 40
MD = 67641cc95f9eac54b088c15658c84a6af7303b489c3566f0937b93666fe13a0f520cb7b56265315c928dc1b8361a8af8

COUNT = 41
MD = b0889a08457fcd166a84303029d55eaf531a308de12fbcc6f876ed6d6d377408e9cd3deaee59a41c22ac06813dcc88bc

COUNT = 42
MD = b3c444ce312b350d0187ced7ea7b8351d454e4d34ecab213fed966aa630a817f7c70717fa0714750802bf38978e52b75

COUNT = 43
MD = 20d6902f2df906d2c7b9c404cde23a62307790250724e7fd8ac9abb93a44e715f7afe2ff8ed889a9f355dfdd95934ff5

COUNT = 44
MD = f8bd9e71525d0d94def6a774894f0417ac961062b77420570cd6499fed9c20decec64ed95b1f79825efad5afc92d5f9b

COUNT = 45
MD = 19dfb0afff19f2cc3faf3874727f6dfc5f16133f5a5a29e54549eeb8450c8e86f64f35b614f645fb0e9ee36cfe0741b6

COUNT = 46
MD = 58f3fd66aa8e6704ba659560272c3af9725af8c60137acd86b2b8b8c334aa9a04065ca7aeac84d6bffda6fe420018d26

COUNT = 47
MD = fd7f8e39744b8fae34d626843046690db47d7225e3ec22ae41963aaffeca634dc607c48c80d69b084749ae6004d50532

COUNT = 48
MD = c31b336be783063bb594fd2aac1794a457246b53084e57e24e254dc46eccaca666ac5ed49cd2a0e8284d89106b023c26

COUNT = 49
MD = fadf05bf17572ca80e771afc8599c903a483e86c53080999d022e1feeb0a102ed60fd98e51d6286c2114b4876316a4cf

COUNT = 50
MD = 67e8e0996eafeb0457ca23f8996245bbb4e4a9f69a0bbe150e3b072f1aa3e6feda0bf8d63d126adcc1ac7fbcf26027a3

COUNT = 51
MD = c0f02377d0eef098c89768493203c6ad5384b6968a7bef8edde2ee19a5e50ec9a9c8c583d7adaacb9e5be384d84717de

COUNT = 52
MD = bcce2df94092d8b10561a17499d232bfd40a591b9a9284306c12dce48a2040c15ab48f957a93554d3f71569336065ea7

COUNT = 53
MD = 23440d45311673e2a16cc9ff56f7fd63984825b60050ac02819359d4e6cd3ac7c97026dcaaf187136bdc870e997b95e9

COUNT = 54
MD = fb8245292587c40de63b12d41a0d4d88ae52327fffcef876ae2447cbb502e25debe8e7e4f6165cdfa42e17b7a3432e4b

COUNT = 55
MD = b4d125497c90fce16368116bdfa11060a4e6e4c41112e6dbb521a2081c463bda8d1246bf4ad5c7ba47a930dcfbd8c925

COUNT = 56
MD = 8c26a31291004c8fada17d135ff35bd58878e8b6b5fb77bc305a81512704ddd92e26dc9560ffc62fba4b31754ad75fa5

COUNT = 57
MD = 2855078eec4ed7ef8d258b7689687b9d4d6963275b5c4a1ab8682d4e9c4e978645207717917ca16ef9e785e2b61ba166

COUNT = 58
MD = fd36c351aa1e105eec035f1971c0602f34601b63710ddf7557787d84ed4022e75a787027cfb40effef0f12a0f2ead56d

COUNT = 59
MD = bab50ad626ec56d8dbe9a9318a2fcb2359d0accd9499bd76a8db33922503f40f3b0e9a43af68d537bfac341b343d21d8

COUNT = 60
MD = 937d6cada88fbf1490322c52f9e2a463f3d0d17954d19d07e0b0d4b8d172f0d56a852030b80985328cdd038fb3eb04d6

COUNT = 61
MD = 2ac82984b61b7fbb810f13b7d6d1cba2ab22f381e988d016432a60daaa205e5ee841cce614869164e0ca3e9b5ab7695b

COUNT = 62
MD = b1b08cc5d7cd4fd3698e764c938f0d24b24b8f9966f734f9c86df1067a6e3addbe7050181eb52e51565fc5a0fd39d492

COUNT = 63
MD = bfdb13378e6a975eb947d419224b813264bac407ee754ea468aade2e9ff267c5742b4a72fd7a757d392a310f74d21c1e

COUNT = 64
MD = bb8d3f70cfd2a243512275860e93b3191fa38caa92ec3309dc50c1ee26819e622119260ed529bd11c5a03cf7151b72b0

COUNT = 65
MD = b5b6bcbadf784e212212a296d600218bd1f6f576a70587337faac9332b637cb38c6bbafe788d023146501ebfff72ffe9

COUNT = 66
MD = d50ff2a926a49d4183070c70c0a95a8ff56ea45928eeac3542492c66b3093fb6ed03d355dc4117ec351a6446579c606f

COUNT = 67
MD = a2dcbf96b45206fab7769a26977c71735d8f4543aeb81587b2238636bf84517f81b89394c4c1e7ae935869fd7aa3035f

COUNT = 68
MD = 8c250da3ce14a9e08c38a5e8c8a31c45b99ae7f1f61c90d589956ea27c7942d33f5eb737796f7ca35b6fccaec13da764

COUNT = 69
MD = f1746cf5c0b7d045979f1e185a35db13ae22a77ea437cc8f7aa6514e8b6e6a82c615bc96bfb6def8c43d31c6dda35d0b

COUNT = 70
MD = 812ffe86f90be9a977de260c6fcfc0682092f8cc1404c25e52972a3a2d953c021a531c6db7502bef75743fb65a79052e

COUNT = 71
MD = d3d6f385abc45f1ac8a72f049204701cb37a28c9ee593596b5f38189ffdbac4bab6dbf28c374a0216b7d56875bb9ec12

COUNT = 72
MD = e1fd5e62f418aaf6f245838e419bdf092b7ec3ad02efddf9145063791e352ed3b1c21ff7cb68223e1cb288299ae09e77

COUNT = 73
MD = bec64b051c19b035adb906ffe37112855a787082334286b631fae0b5183341097b37192c96d1ddf566f6909cd0b8b264

COUNT = 74
MD = e82761ac7a5da23236265fd7f8cad06744870c2b5362a8adeed3428ea324066448c98482e1c5b15f0199ee92d4aa0e69

COUNT = 75
MD = df495bcc82cc006dce81997c2799c27e3908032cb059cefbcc687e0cbf561cc05c6a0cd91ac9ecb074392aff7e5e3bb5

COUNT = 76
MD = 404a4cb927ed4bb7d97934dc232d9866dcf1a7036bbf3e4afea357752fe69cbacbf0240c5b7b23a83520daaf22fa2f63

COUNT = 77
MD = 52eb7c7ed4ef722016783f16f5d89a77b0250438953d5372bfba2a1108b698c391e361c4866c188de3112972c69eef80

COUNT = 78
MD = 4cb8e58fe96b142bf063c443cd4462d47397428abec69fc3bc94591b35abfa8a0baec90526b27f3ab1e97a5c19c22b60

COUNT = 79
MD = 93c22a7d0bd3b7d806ee50abedd0f26a80654f0b7187ed48965ab0845b113b9b9093d1969762f268ddec1a7d3c30b11d

COUNT = 80
MD = e4df31ddb119ae861c3f2d4d7e2473664fb79532de6ecaf635f235da1629406d7110c776de2838360a4b3fb0fb54e1d8

COUNT = 81
MD = 3741ecfca60572118076e19a9d71a6c092a31c5a523c7458397c934d2eeac17daf03c0ac16b3852955ed9bd0067865db

COUNT = 82
MD = 88c6709e3efc7bb8f5e4ae15a83ee8d0c0ec14676d81fe263fb28dd293b65eaf5963ef6ff226628650200f939a8a7deb

COUNT = 83
MD = bf514a6252783e67e0a6e9d31d7bdeca857b83b09bcecf020f17471b397991effb31d350a0679f8d080cd63a43bed7b1

COUNT = 84
MD = 70ceeedbd57969e63dcc035af4737ec406b007bb8f1e3d26e63ac8664a809f0803e39bc3581fd229ba6262b0794c3b2d

COUNT = 85
MD = 80d09f64b95a411e7282ca8f70d6ae21cbff6ce3a0b84b65c4c137d7bc13b8d1d773dbeea7e5d4bb11565c4fafa0faee

COUNT = 86
MD = a17f543a1d5a61585409abd20a3c8fbf0dd756a703b6f0c39fad04bc266b7cb69462196425f5d259ee31f53f3d6fda3a

COUNT = 87
MD = be19ea7c1d0c009a81dca1e9a7c6762f9a8191eb82fd72efb28b42b901be4e2385a5d1572d2c7c0995851ff87a649b78

COUNT = 88
MD = 21ed3cdc4c043d8312b30cb972d2766cb0bc6792aa1661b207e505ad8ac5a0a7cd03375862ec8cdc769de60ccec0864f

COUNT = 89
MD = 3ba7ef694fbc93667f92bfd36898f65f93addb39328c7ba099f6da2bddd8d9d44d8baee0edf8f151d80f365df0943c60

COUNT = 90
MD = 00d66ff05a5b45a3f4125b0e8363899c96f6788aef4c3c0f1335a8b50785d28cca4ec4c892e3ca23bd392c936171aa44

COUNT = 91
MD = 5c975bcdc34f090299362adb1264dd2a028d6ed74576ef47a3c320bf71cd5ef6a230f4d2ade779c927891238f7eae32c

COUNT = 92
MD = 27392d2b874e926ee64374f831a41c89027a87778a88ac81dc6cded5a195935c27a495d5e7411b4472742696c8ce786e

COUNT = 93
MD = e1d4b3b786d55611ed685873c6fd80a4fe4a3fa1e958f656c73f1e750967f5c059122a13a8bd50d36713b463149b7685

COUNT = 94
MD = ac779f59d238eee1a1046a7487826b7e53f7171bf985a8a61526caeb0af13610227ff25ea340a9b7e53c646508dd546a

COUNT = 95
MD = bae8ec05d6de979f7c614e2d496b3186e27c6c856fcbe9c853b8ceb1476cedb2734f5bfb46e26ee8a0cfa763f5927710

COUNT = 96
MD = 9f3e7209fd277887c19e6b35b03f00ac6c65efc17b38163c5e001b7b2ef6325d89cf6553f8dd5cb3fedb4f6815500053

COUNT = 97
MD = eed180d3d0b1c308a9465fb9f101bd826b17484b9f59e7dc22a2b0ff5d10c0e689169eccd85038ffa043789f65024a8a

COUNT = 98
MD = 96cdb425388488374307551dce0ce0e624005499ac861202961f3b00edaf42e45c8905101a62f2d9a5d32a01fd778f02

COUNT = 99
MD = 02c9babd4add11a5f23c1808f72e3dc8325cedc31d28213a04d999dac8f46b866f84ba3dbfbcf1a863cc54d808ffadca

