=pod

=head1 NAME

SSL_get_verify_result - get result of peer certificate verification

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_get_verify_result(const SSL *ssl);

=head1 DESCRIPTION

SSL_get_verify_result() returns the result of the verification of the
X509 certificate presented by the peer, if any.

=head1 NOTES

SSL_get_verify_result() can only return one error code while the verification
of a certificate can fail because of many reasons at the same time. Only
the last verification error that occurred during the processing is available
from SSL_get_verify_result().

The verification result is part of the established session and is restored
when a session is reused.

=head1 BUGS

If no peer certificate was presented, the returned result code is
X509_V_OK. This is because no verification error occurred, it does however
not indicate success. SSL_get_verify_result() is only useful in connection
with L<SSL_get_peer_certificate(3)>.

=head1 RETURN VALUES

The following return values can currently occur:

=over 4

=item X509_V_OK

The verification succeeded or no peer certificate was presented.

=item Any other value

Documented in L<verify(1)>.

=back

=head1 SEE ALSO

L<ssl(7)>, L<SSL_set_verify_result(3)>,
L<SSL_get_peer_certificate(3)>,
L<verify(1)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
