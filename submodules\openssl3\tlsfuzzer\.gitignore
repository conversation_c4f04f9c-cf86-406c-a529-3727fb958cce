# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# python virtual environments
*venv*

# hypothesis cache files
.hypothesis

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# When running with embedded tlslite/ecdsa lib these should be hidden
ecdsa
tlslite

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.cache
nosetests.xml
coverage.xml
pylint_report.txt

# Translations
*.mo
*.pot

# Sphinx documentation
docs/_build/
epydoc/

# PyBuilder
target/
