#
# Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.


# Public / Private keys from other tests used for keypair testing.

PrivateKey=Alice-25519
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VuBCIEIHcHbQpzGKV9PBbBclGyZkXfTC+H68CZKrF3+6UduSwq
-----END PRIVATE KEY-----

PrivateKey=Alice-448
-----BEGIN PRIVATE KEY-----
MEYCAQAwBQYDK2VvBDoEOJqPSSXRUZ9Xdc9GsEtYANTunui66LxVZdSYwo3Zybr1
dKlBl0SJc5EAY4Km8SerHZrC2MClmHJr
-----END PRIVATE KEY-----

PublicKey=P-256-PUBLIC
-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAELBUPQpznDyFsJSz14GLOH2Oc1dFl
x/iUJAcsJxl9eLM7kg6VzbZk6ZDc8M/qDZTiqOavnQ5YBW5lMQSSW5/myQ==
-----END PUBLIC KEY-----

PublicKey=KAS-ECC-CDH_K-163_C0-PUBLIC
-----BEGIN PUBLIC KEY-----
MEAwEAYHKoZIzj0CAQYFK4EEAAEDLAAEBx+LKHfWAn2cGt5CRPLeoSaS7yPVBcFe
53YiHHK4SzR844PzgGe4nD6a
-----END PUBLIC KEY-----

PrivateKey = RSA-2048
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Title = Test keypair mismatches

PrivPubKeyPair = Alice-25519:P-256-PUBLIC
Result = KEYPAIR_TYPE_MISMATCH

PrivPubKeyPair = Alice-448:P-256-PUBLIC
Result = KEYPAIR_TYPE_MISMATCH

PrivPubKeyPair = RSA-2048:P-256-PUBLIC
Result = KEYPAIR_TYPE_MISMATCH

PrivPubKeyPair = RSA-2048:KAS-ECC-CDH_K-163_C0-PUBLIC
Result = KEYPAIR_TYPE_MISMATCH

PrivPubKeyPair = Alice-25519:KAS-ECC-CDH_K-163_C0-PUBLIC
Result = KEYPAIR_TYPE_MISMATCH
