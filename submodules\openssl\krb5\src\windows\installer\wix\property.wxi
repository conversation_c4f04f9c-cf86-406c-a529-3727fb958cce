<?xml version="1.0" encoding="utf-8"?>
<!--

  Copyright (C) 2004, 2005, 2006 by the Massachusetts Institute of Technology.
  Copyright (C) 2007 Secure Endpoints Inc.
  All rights reserved.
 
  Export of this software from the United States of America may
    require a specific license from the United States Government.
    It is the responsibility of any person or organization contemplating
    export to obtain such a license before exporting.
 
  WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
  distribute this software and its documentation for any purpose and
  without fee is hereby granted, provided that the above copyright
  notice appear in all copies and that both that copyright notice and
  this permission notice appear in supporting documentation, and that
  the name of M.I.T. not be used in advertising or publicity pertaining
  to distribution of the software without specific, written prior
  permission.  Furthermore if you modify this software you must label
  your software as modified software and not distribute it in such a
  fashion that it might be confused with the original M.I.T. software.
  M.I.T. makes no representations about the suitability of
  this software for any purpose.  It is provided "as is" without express
  or implied warranty.
  
  -->
<Include xmlns="http://schemas.microsoft.com/wix/2003/01/wi">

    <!-- Important:  This product should only be installed in all-user mode -->
    <Property Id="ALLUSERS">1</Property>

    <Property Id="LEASHAUTOINIT" Admin="yes" Secure="yes">-autoinit</Property>
    <Property Id="LEASHAUTOSTART" Admin="yes" Secure="yes">1</Property>
    
    <Property Id="ARPCOMMENTS">$(var.ARPComments)</Property>
    <Property Id="ARPCONTACT"><EMAIL></Property>
    <Property Id="ARPURLINFOABOUT">http://web.mit.edu/kerberos</Property>
    <Property Id="ARPHELPLINK">http://web.mit.edu/kerberos</Property>
    <Property Id="INSTALLLEVEL">50</Property>
    <Property Id="ComponentDownload">http://web.mit.edu/kerberos</Property>

    <Property Id="UPGRADENSIS">
        <RegistrySearch Win64="no" Id="regsrch_NSIS" Root="HKLM" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\Kerberos for Windows" Name="UninstallString" Type="raw"/>
    </Property>
    
    <Property Id="NSISVERSION">
        <RegistrySearch Win64="no" Id="regsrch_NSISV" Root="HKLM" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\Kerberos for Windows" Name="DisplayVersion" Type="raw" />
    </Property>

    <Property Id="CantRemoveNSISError">!(loc.CantRemoveNSIS)</Property>
    <Property Id="NoIE501Error">!(loc.IE501Required)</Property>

    <!--  Additional properties relating to the UI are in the appropriate UI.wxi file -->
    
    <!-- Configuration properties.  If these properties are defined, then
         the corresponding component will be enabled. If the corresponding
         variable value is the empty string (""), then the properties will
         not be defined. -->
<?ifdef UseDefaultProperties?>
    <Property Id="LEASHCREATEMISSINGCONFIG" Admin="yes" Secure="yes">$(var.LeashCreateMissingConfig)</Property>
    <Property Id="LEASHAUTORENEWTICKETS" Admin="yes" Secure="yes">$(var.LeashAutoRenewTickets)</Property>
    <Property Id="LEASHMSLSAIMPORT" Admin="yes" Secure="yes">$(var.LeashMsLsaImport)</Property>
    <Property Id="LEASHLIFETIME" Admin="yes" Secure="yes">$(var.LeashLifetime)</Property>
    <Property Id="LEASHRENEWTILL" Admin="yes" Secure="yes">$(var.LeashRenewTill)</Property>
    <Property Id="LEASHRENEWABLE" Admin="yes" Secure="yes">$(var.LeashRenewable)</Property>
    <Property Id="LEASHFORWARDABLE" Admin="yes" Secure="yes">$(var.LeashForwardable)</Property>
    <Property Id="LEASHNOADDRESSES" Admin="yes" Secure="yes">$(var.LeashNoAddresses)</Property>
    <Property Id="LEASHPROXIABLE" Admin="yes" Secure="yes">$(var.LeashProxiable)</Property>
    <Property Id="LEASHPUBLICIP" Admin="yes" Secure="yes">$(var.LeashPublicIp)</Property>
    <Property Id="LEASHHIDEKINITOPTIONS" Admin="yes" Secure="yes">$(var.LeashHideKinitOptions)</Property>
    <Property Id="LEASHLIFEMIN" Admin="yes" Secure="yes">$(var.LeashLifeMin)</Property>
    <Property Id="LEASHLIFEMAX" Admin="yes" Secure="yes">$(var.LeashLifeMax)</Property>
    <Property Id="LEASHRENEWMIN" Admin="yes" Secure="yes">$(var.LeashRenewMin)</Property>
    <Property Id="LEASHRENEWMAX" Admin="yes" Secure="yes">$(var.LeashRenewMax)</Property>
    <Property Id="LEASHUPPERCASEREALM" Admin="yes" Secure="yes">$(var.LeashUppercaseRealm)</Property>
    <Property Id="LEASHTIMEHOST" Admin="yes" Secure="yes">$(var.LeashTimeHost)</Property>
    <Property Id="LEASHPRESERVEKINITOPTIONS" Admin="yes" Secure="yes">$(var.LeashPreserveKinitOptions)</Property>
    <Property Id="KRB5CONFIG" Admin="yes" Secure="yes">$(var.Krb5Config)</Property>
    <Property Id="KRB5CCNAME" Admin="yes" Secure="yes">$(var.Krb5CcName)</Property>
    <Property Id="KRB5PRESERVEIDENTITY" Admin="yes" Secure="yes">$(var.Krb5PreserveIdentity)</Property>
<?endif?>
<?ifdef UseLeash?>
    <Property Id="USELEASH" Admin="yes" Secure="yes">$(var.UseLeash)</Property>
<?endif?>
<?ifdef UseNetIDMgr?>
    <Property Id="USENETIDMGR" Admin="yes" Secure="yes">$(var.UseNetIDMgr)</Property>
<?endif?>
</Include>
