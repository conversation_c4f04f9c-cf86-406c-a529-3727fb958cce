@echo off
REM Selective test runner for p2psocket interface
REM This script demonstrates how to run individual tests

echo ========================================
echo P2P Socket Selective Test Runner
echo ========================================

REM Check if we're in the correct directory
if not exist "testp2psocket.cpp" (
    echo Error: testp2psocket.cpp not found in current directory
    echo Please run this script from the src/p2psocket directory
    pause
    exit /b 1
)

REM Build the project first
echo Building p2psocket library and tests...
cd ..\..
call build_p2psocket.bat
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

REM Go to the bin directory where executables are built
cd bin

REM Check if testp2psocket.exe exists
if not exist "testp2psocket.exe" (
    echo Error: testp2psocket.exe not found in bin directory
    echo Make sure the build completed successfully
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running Individual Tests
echo ========================================

echo.
echo 1. Testing Basic Operations...
testp2psocket.exe -test basic
set BASIC_RESULT=%errorlevel%
if %BASIC_RESULT% equ 0 (
    echo [OK] Basic operations test PASSED
) else (
    echo [FAIL] Basic operations test FAILED
    echo Stopping here - fix basic operations first
    pause
    exit /b %BASIC_RESULT%
)

echo.
echo 2. Testing Invalid Parameters...
testp2psocket.exe -test invalid
set INVALID_RESULT=%errorlevel%
if %INVALID_RESULT% equ 0 (
    echo [OK] Invalid parameters test PASSED
) else (
    echo [FAIL] Invalid parameters test FAILED
)

echo.
echo 3. Testing Socket Binding...
testp2psocket.exe -test binding
set BINDING_RESULT=%errorlevel%
if %BINDING_RESULT% equ 0 (
    echo [OK] Socket binding test PASSED
) else (
    echo [FAIL] Socket binding test FAILED
)

echo.
echo 4. Testing Socket Settings...
testp2psocket.exe -test settings
set SETTINGS_RESULT=%errorlevel%
if %SETTINGS_RESULT% equ 0 (
    echo [OK] Socket settings test PASSED
) else (
    echo [FAIL] Socket settings test FAILED
)

echo.
echo 5. Testing Polling Operations...
testp2psocket.exe -test polling
set POLLING_RESULT=%errorlevel%
if %POLLING_RESULT% equ 0 (
    echo [OK] Polling operations test PASSED
) else (
    echo [FAIL] Polling operations test FAILED
)

echo.
echo ========================================
echo Basic Tests Summary
echo ========================================
echo Basic Operations: %BASIC_RESULT%
echo Invalid Parameters: %INVALID_RESULT%
echo Socket Binding: %BINDING_RESULT%
echo Socket Settings: %SETTINGS_RESULT%
echo Polling Operations: %POLLING_RESULT%

REM Calculate basic tests result
set /a BASIC_FAILURES=%BASIC_RESULT%+%INVALID_RESULT%+%BINDING_RESULT%+%SETTINGS_RESULT%+%POLLING_RESULT%

if %BASIC_FAILURES% equ 0 (
    echo.
    echo *** ALL BASIC TESTS PASSED! ***
    echo You can now proceed to connection tests:
    echo   testp2psocket.exe -test connection
    echo   testp2psocket.exe -test vectored
    echo   testp2psocket.exe -test multiclient
) else (
    echo.
    echo *** SOME BASIC TESTS FAILED ***
    echo Please fix the basic functionality before proceeding to advanced tests.
)

echo.
echo To run a specific test manually:
echo   testp2psocket.exe -test ^<testname^>
echo.
echo Available tests: basic, invalid, binding, settings, polling,
echo                  connection, vectored, multiclient, types, edge, all
echo.
echo Press any key to exit...
pause >nul
exit /b %BASIC_FAILURES%
