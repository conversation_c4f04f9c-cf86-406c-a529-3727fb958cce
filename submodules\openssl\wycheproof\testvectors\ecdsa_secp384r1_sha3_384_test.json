{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 418, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "wx": "2da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa", "wy": "4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMH<PERSON>wEAYHKoZIzj0CAQYFK4EEACIDYgAELaV92hCJJ2pUP5/9rAv/DZdsrXHrcoDn\n2b/Z/uS9svIPR/+IgnQ4l3LZjMV1ITiqS20FTWnc8+JexJ34cHFeNIg7GDYZfXb4\nrZYuePZXG7x0B7DWCR+eTYjwFCdEBhdP\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c023100c0bb0ee1bffc6c7b74609ec20c460ec47f4d068f33d601870778e5e474860d77834d744db219e6abae9c32912907efd2", "result": "valid", "flags": []}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "308164023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "30820064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3063023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "30850100000064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "3089010000000000000064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3064028034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02803f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30660000023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "30694981773064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "306825003064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "30663064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30692235498177023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "306822342500023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "306c2232023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0004deadbeef02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3069023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c223549817702303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c2234250002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "306c023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c223202303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "306caa00bb00cd003064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "306aaa02aabb3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c2238aa00bb00cd00023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "306a2236aa02aabb023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c2238aa00bb00cd0002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "306a023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c2236aa02aabb02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30803064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30682280023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c000002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c228002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30803164023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30682280033034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c000002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c228003303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e64023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f64023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3164023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3264023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff64023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "306830010230633034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "3063023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "30633034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": ["BER"]}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a100", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a105000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30663000023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a13000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3067023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "30663064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "3032023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "308196023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a102303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "306502813034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0281303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30660282003034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c028200303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3064023134a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3064022f34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02313f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c022f3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30690285010000003034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3069023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c028501000000303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306d028901000000000000003034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306d023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02890100000000000000303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "306802847fffffff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02847fffffff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30680284ffffffff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0284ffffffff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30690285ffffffffff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3069023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0285ffffffffff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306c0288ffffffffffffffff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306c023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0288ffffffffffffffff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "306402ff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02ff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "303202303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "30330202303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3033023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3066023234a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c000002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02323f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30660232000034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c023200003f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c000002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3066023234a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c050002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3066023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02323f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a10500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3034028102303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3034023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3034050002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3034023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064003034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064013034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064033034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064043034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064ff3034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c00303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c01303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c03303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c04303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44cff303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3034020002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3034023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "30682234020134022fa42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3068023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c223402013f022f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3064023036a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303d44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db4cc02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd3921", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "3063022f34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db402303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "3063022fa42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "3063023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c022f3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "3063023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c022f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30650231ff34a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0231ff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "303509018002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3035023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "303502010002303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3035023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502310134a42eda6d8a881c6a3369fd89db629c9b61904c86019726b4f0fdd1ba4eeb869ab3182345a88754178a3e92aa12ddbf02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650231ff34a42eda6d8a881c6a3369fd89db629c9b61904c86019727262a62cdd1e08fc7ea7efcbeb447385e3db20bbd10888ad902303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30640230cb5bd125927577e395cc960276249d63649e6fb379fe68d912724fb039e84258bd66f58f03082026d561dad822b24bb402303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023100cb5bd125927577e395cc960276249d63649e6fb379fe68d8d9d59d322e1f7038158103414bb8c7a1c24df442ef77752702303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650231fecb5bd125927577e395cc960276249d63649e6fb379fe68d94b0f022e45b11479654ce7dcba5778abe875c16d55ed224102303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502310134a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c02303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023100cb5bd125927577e395cc960276249d63649e6fb379fe68d912724fb039e84258bd66f58f03082026d561dad822b24bb402303f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0231013f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78874db51f73e84e472ce6a716df47684a2b3c004470826314", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0231ff3f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78f8871a1b8b79f2887cb28bb24de619545163cd6ed6f8102e", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3064023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0230c0bb0ee1bffc6c7b74609ec20c460ec47f4d068f33d6018740159862804edf982b33669b69693f30c1b019265c42c65f", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0231fec0bb0ee1bffc6c7b74609ec20c460ec47f4d068f33d6018778b24ae08c17b1b8d31958e920b897b5d4c3ffbb8f7d9cec", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c0231013f44f11e400393848b9f613df3b9f13b80b2f970cc29fe78bfea679d7fb12067d4cc99649696c0cf3e4fe6d9a3bd39a1", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023034a42eda6d8a881c6a3369fd89db629c9b61904c86019726ed8db04fc617bda742990a70fcf7dfd92a9e2527dd4db44c023100c0bb0ee1bffc6c7b74609ec20c460ec47f4d068f33d6018740159862804edf982b33669b69693f30c1b019265c42c65f", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529730201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529720201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529740201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000001000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3337333130", "sig": "3066023100ac042e13ab83394692019170707bc21dd3d7b8d233d11b651757085bdd5767eabbb85322984f14437335de0cdf565684023100c1045ed26ae9e8aabf5307db317f60e8c2842f67df81da26633d831ae5e061a5ef850d7d49f085d566d92cfd9f152d46", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "3131353534393035383139", "sig": "306402300c0b82be4c36d506063fc963133c14d5014d65c9eb796ee8a8387120119ccc16b57302b6ccb19a846b7762375b3c97180230285919259f684f56f89cbaa789ef13e185fd24d09dcd46ce794aedc4e5b4a3820535213abb7c4e605b02200fbeb3227c", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "32363831393031303832", "sig": "306502307da99d7e8bb505cc5f12706d5eb7669336a61a726a5b376ff96d678a621f38681bc78592cd06717cb87753daf0d39b77023100ca91cdb78f21950877b69db1418a3e9b5799b3464f1fa223c7ac8d6fa9f647f2a08109935ad67477c96bbf1a2a127a1d", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "38333336353438363931", "sig": "30640230204d322af7178ac20b39a42723fb1f8329b105993e09dbdcabf3e0eaa0a08d54719e06ba704691295a56be7765b5fd7402303b526de3e47e69518d4fbc0833a5785074c3f4eef27b9f0fc48481514931e43235b81e51d2b577b1739964ef25d8faad", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "33363235303538313232", "sig": "30650231009d4adb54f52349cc73322ffc946bf44a1a1bb954bd4b58f912be068ce05272a12479bbb0f778a9faf8f9f2e9324bd5e902301eee2f98406c30728da3b2b533c387108cc67fc24abdb6bdab686f207f0a75cc9c3b4d4ea9427d881c47d419ed7a1b95", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "33323739333237313834", "sig": "3066023100ae50b1aaad54efbe007f1da7d50ec00cf1100f904fd8f4940ef48f364031dc1284ab984e018105e6d368bb5a47c25022023100a803fb0156a10e42d4294a764a1da9c3e0c8320bd1a83544ff46751a777bbce23985669e43ff63fcdbac34d68f42de56", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "383836363432313439", "sig": "3066023100bc65644acb7dcf72bbf937e781d6de7bca052adcad474e3a2b06795a18db7b89d246a485d696b2b8d07c07d2ba2e2929023100af811cb9772b4b3f1eed358b722a5b28a21617aea7eb6f9371b68a8d1eb7232def267ba56a6220f66a03c3ed7cd322e1", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "32333737393036313539", "sig": "3066023100f6205c154a9cd38a1fc9a18c7bf6350699c95144268ba4ca182a5c8d50b780d468aa9beb8115f8ec489558891ecd6d65023100863f41412ab418fe037fd688a9f6c509bc5535b2c6b5ad7bf9486fb0e5b02136219aca2cdd9d5d63f9140e6d1d054201", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "38303833323436363234", "sig": "3066023100aedf7382965359c9abff67f0fad2be6b84d760ac95da1c656989f19938b046371e101e8bab9a0ae9b9ad2bc242a982010231009175511515a01096b4d77cc505c60facfceb1841948442448e5c9f24204f817eb20d12479305e82ee5a34bd73ebb04ad", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "31343832353634393539", "sig": "3066023100bcc696d8d3445960e00c9f76f277e5fa3267224d0187ad120f9c074597eeafcb6c7f22f51900351848855b20072afdae023100935dfc4f7b48ac01116e5cf194fd2feed3cb28e72cba8485f1d94e5d20f5f4147a1ca3d6496bbe915913d21c4f5afbaf", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "33303635303732353033", "sig": "3066023100c029e49048921647659a042eb533004ea3487f22a7f2c471c43a5a2326dd03ac24386242c05698194b5c6171f08bb7cc023100a92ed5f2c736e27711384a131c464f73852a7dd167b27c63020040d8de991a390ad76627d597ccfebed809f2f7f57b26", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "31343932313138323033", "sig": "306402300f5e1771ba1957fe8794c23776832ea40ec4fda999186f6c365f4749f07893cb55e972658c2d3b39a7b485193ff1d71902303967983d1da9dcf0105ddc383f599539d4b32b1bb8dae1a6fe0afbc9bff1e0952a32f08d161b3979a60bb6e49b6c7d7a", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "353535333230313336", "sig": "306502300939874c2f67090a900ad7399df78c6005fc4673e23b155df7471b31debd2174fea94e00180ddc1a86609eda8830b449023100c9d71934a7222e415a01692c7274e5097d580dfe74175dfc0055feddfb414c1ae857051ce12c0ff25d5372751456622a", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "333331373032303036", "sig": "3066023100c35b9eaa9a03a36ba52b7ab207ff48925a0188d288b1ed25d7de8bc203e8ef912a01891eab8f8e3a7d0a948a26d35ab1023100cf04105208f10af61240da6073cc39278fdadc0578bf40bbd0b0f601ed791e041a90a09d7c423a83f6cd047d745c4f24", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "373531313832333938", "sig": "306502306c1fffcc270c9bf108289b42514e57e3f29ea0f1b3fbfc10ea283b3e6d2a4438d591fb7274c9ffee15009cd9e340f106023100de38043b47c7d5ab21d8ec5a35758f1a69ee59ea6df525884a04210172e7421f2a49f5921a4eac40b278f6e7c49474f4", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "3737303439353436343831", "sig": "3065023100ecc637f3d32bc9a1ec20f025af72eb03df49f27901fef6b58d226b6eaa9faa6374f87c2aaaecd69946f3777fb9d4581e023048f6a06b296a17d84dd26ffded0c5dccf177e6df9a7710b0406fedfd269b2c220f11c1e02cea42c18ccac768c64ba7eb", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "34323539303733303333", "sig": "306502307dcf9ded94532d50e9a2ac971642da0de95a1ca95500621174113c1d554f21bb2d175b5beacdd73443043c6cc8eaf105023100d4da518de6b8c05c640a3e7a1540482d935c4dfdca7544daf94ac8135804127b93665e1191b66bdb0089c49802c33fb1", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "31343139373133353036", "sig": "30660231008209054bb408eed6ab65f4bb76223d509ea24d02cbbc5273145bcb40189052540e565fbf50474f83db3da054a793c863023100b8169b12568ffa03c0e37d4a19911e9f4af7cd256343a36e41cd7b41395524235e86d55c647f288fe5cef2b5401e4413", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3231393238333534363838", "sig": "30660231009fe969770d630bb938ca2282536f71f3dc461071186216d940eca10fc53c4e7ef067bca237bd6a82eafef0fb8d38050e023100b23a042178fdea5da86229c08a51227f23e31b1e2345defa12ed7041bec31f87837ba4764721823ea9f1e652d536c5ed", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "36363235393934383739", "sig": "30640230459be510bca760f75aca10e41efb7ff64b78fb9711e72f224373b9af14f2c042b68b15bb189b3d7ccaed9318936543c90230579c07e99fc9891498ef3109360017052cb20bafb290ca2ffa64a72cf01e38e12770ba0ad5e190d2ef10c2d294e099a2", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "393538343937303836", "sig": "306502302bc3bb18191a5bfe6d13c735104d78dd947854cf1d93017695119c8f04ebb44d7a7fffe71d15b78e0c2c28765bbdfc38023100a9051dd102b20e3c69a01a36b85a1ccea670da784038989145e3cd9108b064d6d54f7df21164adb91b3850cd005ff68d", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32353939333434393638", "sig": "3065023100fe2c0567483ecbc6086a24160200a9ce91e6cf52df6d25b2ab08fedcc4ca95cbb6be68b58870c10169264f3b3e8d552e023034b7ef7c580506d29b1ef8223e2131602dad9fbcbce6f846de42519faecfa612a82e999cbfed45f377b77ae5ef0b4835", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "36323332343733393531", "sig": "3064023009296917f12fb3bbe2c69c9bbbccf8a400d7e0b31c453ff7e928a7e4347a185435490790f56a5a819271192d64d612da0230163860e1f6390c0ada261d2d0346b49f18ec3b17e0389e4c3b2296382bc23d6576bb968120cfd24ce735a14d3167f203", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "3737363033313033323137", "sig": "30650231009bf980d1d91fa0daf73e3bcc02c7773503f291b3378c96700ecd71aed81fb8ff47d4baa8b6782842f227a9314f343e4402304342d335dd870f4a1b817b519ab184710c2c79b6329ae3f87b735e48874b6e47950db7c8f0fba59a349112bd2b3d9eba", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "38393534383338363735", "sig": "306502303f9b09855b47d180d60fe6ac427458a452ad72678d13818d1a28a376b31fd7d1c67e70ec234c40fab7d17719f7caa27c023100dc1d5765bc5c266a39e1a94085983ccc63cb41556e3733330c98934c329eb7e724e12cadd082da23952b831bcc197f18", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "32343533383530303035", "sig": "30650231008c6910c012fb1a5b45032b09f2cfdbc7c3b22b0e18a0cc0ec4adc296cbfca66832379456b867ad1a0184ab1a80af59ee02303d87fec6feb833d01e4f77a306441fd27f328d01f6c20eef9b185ad4723c46f5d15e7be0db1c496018b4fa1987ac6b78", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "32363934383137303236", "sig": "30650231008cb0ad263557318156ffde6b45cb6ca8633c3b50b51454605dd01242dda44c9cc5b59b327e919629a9f73720e53a5e6302304f2a0cd11c7ac03425e25d84bb44149117903cc4638e2f64450e2a915b14c6d9c74f70c4f85d6036bc604a92f9b97166", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "343331323036323937", "sig": "3065023017d2c9d32253234b36a02e59f99163913a7c11a723f7122c521dba2cdec36bdcd1837c8b60a916aa64ed32b2c400d23a023100821fb503cb89385bf9a6288ce6559cb69652e8bf940ccd0fa88aae2e72d31ac7d7cf51433ee45889094f51a4cc17272d", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "33323031313936373336", "sig": "3065023100b2e6fbb2a70af41654fb5d632fcbf9dc8a8a362394e42d13e086e7da261aa980b49c4a610a367973f798b9aa9df6d0d102306d237b3161ec602529eecb5c7c706020f82b8040ccf7082576e3caef5e8d6cd87c46a8f3ea9947b18d1a35c83494d849", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "32383330303038353932", "sig": "3065023100a6927125459e31afc31810117677c6ec2ba27c3ee5cc5fafbbd74153d3d2b2f7c7411e564c09d582dd2d5a201ec2b0fa02306e14a3955d24c4ac4f8c035f5edaf74e45ebd95a27954bb1c11fdb00fbc7156e96318d33725c0666006ae0573f7df785", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "383834323632353236", "sig": "3065023100d0f8e8a570a0462ea8ccb980789acbf243cbe946522ae4e9e6fa7e5e8e1bc006c8b84915355f00f39a61dbe77d2b4b9a02300f1ed97929bd7cd633f835086d2241a7b7d8f857b94f71e30b3e9bd19863a401834d01d29d32399006e8f84e0852e3d3", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "3132333434393638393037", "sig": "3064023019e5a38c81ae167c70ef4a1879b7dba0dfaf5dc8a841269e82b106c6ea3f9e3f7e162b8c561d8f1b7b4a2cfba4c8a925023008c41e654d262ea6e1d2f74cd99ef479cb36476b2dac5bf0f250d87f7115bdcb59ddda54abf3b3b77471348facc0c8de", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "353439323433353935", "sig": "3066023100e47a0dd0507717c29d0e482037d7fd001eff3b80013688085ae430d46edb23cab8df8716d078c6503e38a1cf6a9e74f2023100edaf65e609db0925dff88d252791db4a008d9b46e5e6da98e23a766a8a35b8df79ec189d272429dd64ca60983462daef", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "33303832383235333337", "sig": "3065023035d47a723521553ea0440e6dea660439c51b80e896877b03b0c02ffabcecd86e6cfed2e4fcd80d76c97ef945b626b025023100dd61311a4d0eb024288fae55abef6f0fdaf71a55cd3ccb2f8ba8d43ef36dd5562c07d2b4ef60e04ec4c696fcd052185e", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "38303337353732353536", "sig": "306502305319f4a01c4e88261146de213d65e55c2532d9a535bc8c47cd940fd2b7b5bb363e1932bdacc9a196cde39368d86a14f50231008afea330d833a1f3310aafef6bc27b684838ef3e57ac7e36c02e0dbf9e33b934dc7afa7418aabc3e6b0841eff09bc470", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "373734343739383034", "sig": "306402305c51106927cb275b54a7c90f6ba69902f1b1a19e2ac4b76b8d1e41b86f14ff32bbc66f07d4be610ccde84af4e14011810230551d9901408a4d9a1a85fa17de0c7bc49b15bccfae095247fc256a048582610b6ba87bd89dc98859dba2df76d77aff2e", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "3133333732303536333833", "sig": "3066023100e931ac049c0b7bd9060a58b0f78d8d0b60f57caf647fe6476802c9baae7e06062fe3d1a1f0c6345dc7c530db32cad843023100b83867f656b9fea099ca0678bd62f2013238bbd6969a2384e0cb2488dad615a4d91dbdf7908426c9ea9ecf17b872a25e", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "35313832373036353336", "sig": "3065023100d4ccc6e89e85ffcca4b9e32fd45c5be1585d20c35ec83253f3080b0705746f0f5e7e92043b5ae8fd95963e45b4199213023048448f45ad0fc8d20fd1dbd088bdf6d51577f79a1e5e55432ea79d84eefe0b9b55ba145d637be5a686477fe00e1fb481", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34303337363939303936", "sig": "306402306d3ea919365f9f39fe1f9b8c17415766f4c2b77c8393dc8cef321af5b4aa955646643ac32b2220b7590deadec15b88af02304d64a4fb9e26aaeec0d92270becbb5e2f04d812b2bb8b86cb1744437e62e58dc72f98ecafeadae69aef3328953143490", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "3131343230303039363832", "sig": "306402307774080a80e32087c8e923c65522c76648205d9804805bdf05977c4559eeacc518560920e55f626748ae12034745f7bc02301bfbb5bcaff2b70298456fd8145bbcc6d150d9f2c3d91d6ed0f3d7eacc16456f698138ab34a546195941a68d7e92f3be", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "33323037353534303132", "sig": "3065023100b8232417c371ecc56ef6342abecfa42afe479ad1cfcb18f8945ab0e2076621c185c2821a8028c36f1f2a8d3be7fc3442023017a0f7c15403a3fba3d8f095cd7eea597df761dc46e5c8122a3fffabb9fe37c52232e7f49af7e7cbaad8ed62dee8a371", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "32383536323335373531", "sig": "30650231009a5e7ac2a195f5859a0753087da0a2ac20a8bacc551d4c19b10fffe6b7acdd3ca6543957c9f7be8bedd33e89df7ba5940230106cb9821f8aadaf7a7c411df6ca3bde9b6d4a267e4a43ffa9d5d29cc973f3ca4d776351b82586be7d6e2c251726b3ec", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32363736343535323539", "sig": "306502301cdc96cc7892322075399aac7e0a86d4ffdb6e45153c0afa98bfd912941c22d05f360fba6f8734542eb55375b26d38aa0231008ec452f8acbbef3ebbff11e6bf349032b610e87946a6221cccb5055c18d1f1188b6254a60113ed8adc6d0b09fb2f3fd4", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "32313832323433303137", "sig": "3065023100937d4df90d09299bd32bf354e1121a311a77ba0274e7b847804a40d5b72ecb8e9e441afc5289e0337ca1195a4951c1e902307e442371b9991905f417e4e67ead31621bc068964097a46d5bda507a804f5b3bb142ff66d07012549fc42cec38754d11", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "353232393434373333", "sig": "3064023065210ed179af3b99c09b9e95dc81f77007a32002ee7d53eed567086a68a62f1c08543c85f7d1e1f081bae477ff3613fa0230025ce6efa2fe24732fe11f5b1f1232d48fa5dbcfbd62f96776302b1ac52f0d0d40549f2b2f67299569cd14fb7ead4c45", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "343535383431313936", "sig": "3065023100e6a4518771467967e264a9b736aa1f8bc6f421de607fec7e93fc62d91082c979930e6a3ffdcc54d5f0f4b4a2f0665d4902304c6c625b60ab3230e6d190f37a6f14e574f8dc7595467fe89ce62d6d1f2fd198368769fc84b556a3847be26841351408", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "3132393134353430363938", "sig": "306502306388afc6cae9421ba6c52a640a0ebcb9c547505f500307194c8c1eb41cac959686ffa7b3a2adda65136030cba17d1695023100cb1e148645580dea5a87c60db7c818942d55f169fc59eda9a2177a001ecc1bcbf2d519d67d79fba44daa2945bd380c52", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "32323730323132343635", "sig": "306402302d7f29f767ba2f21619347bf29494a318eee949e91181ed7d2cf61162b92f0747c308885891b1734e9b6d5d3705475a902301c34c2ce61e3dca2bb3202b6c4320155f764fc58d318ba44df9a7c06a0a453ee43b633353dbcfe129a54ddc8b6a27e13", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "35313732353737303635", "sig": "3064023068a8758fb66c0ee50309490852f214f6bd09dd888f35390163defa70647202983ebabff3791287d016164c945494edf90230099a2c1073815916cebd4a41448e1b8dc9bb150465adf99c8a965b5fb327bb879e1b34f8d7c509aa1b018f98c9e13e40", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "31333835393530373037", "sig": "306402307ff134c055bda5bba91fa53da5ff90c501a6264abd8db5ced03e9eb88ee63325f267a8fe483b0f7f129434d2e2114705023011649294f067d415681ca6cf6245b0beadcb4095b8e9c9d18bf11ebae41ecafde7529796286ec2efa9073de2f9025e3d", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3634333837383033373037", "sig": "30660231009dfc836f6a993e1aeba9fe4b4e09901b83a5439a0ede150ab583c217fc22154050eb9c4a2f1f0f75c06139549d3013ee023100ed83ee554777a5086ac90b9654f724507a54e5651b4d38153ac7576cf8dc9487be7d3efca544ff4b4804981efbda10d7", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "34383637313133303632", "sig": "3065023100fd614924d6325daf270efbff4db11d645ec9b1f903fd36e1543bbd536ee010d07dd154fdc945a57f52f239439279f42f0230079edf2f7ab361f7542bfd9175dd41ec137bc00d997943720e164e7187585a487a1893cde536b1dc52cdc0baa1fc2183", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "3133343239363139303533", "sig": "3066023100eb55101d2d489c0151d991b0e486016222997b917363f8c48386683091297819662ccc34381d5e5ec1c0c43d137232e0023100d8bd992c2e0ab4fe46a4b43dc3b5103c123ca38e88e3c555385a6fc8ece7d9c957776667f389a950bca4b2ad6503c48b", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "35303634343932363338", "sig": "3065023100f29aea476f19eacc44749f6057d39c6da903ba5c5b5667694145a6fe053ee08abed1d6869d3830036a29b063b295e67f02302decfc3e7d8cf0391f8e21714eeef04fa4f660a404294bcab6cdf23e4fa9e44997694781c49f4539a8d5b0dfa55603f1", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "3232373339393133", "sig": "306402304b55c6c5f0264ddd31b88a92072d3a8f33b28306716d5430c0ff8fbc37d9ddf1e4a60e4e496b355f77ed005b51e352be023054d6da5a6385fa10e97c21b5bdb732a9a9c0685883da74f1f8dea0ae497b7609b3aa4ee92f448144ea2c5529ec2fc016", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "32333932353736393433", "sig": "306402306024ed7ee8ef3edc593a9d07856b9aa78972ff33b82608c93e7068bcac05e0c5048889c8d520351047fa80f050abf83a02300d221dba3ef2d3c14923a651bd2b803603fbc94634033d52a66d80ea6120976c8fadc7274d05ccd47e1d06a63310b6c6", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "32343435323032373632", "sig": "3066023100fab3f2cf338bd7bf46dada597a4f66cbeb336393e4a289e21f8a02a6428bcd5fe66e87bdd3b5072997f94b76f04d9aa6023100ad0c0f1d9c4f8a4b5100e183dee6d5d6825296784cb8205d448204237f5d3435f4c8f0a4fef81890c5a5a028405330da", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "37353934393639363532", "sig": "3065023015cd4339b568212b20856d8397e5c5aebf3b4e4eafd8c90adc2dfe93f928e8a8bf17ec307064ba866491d4b44440d116023100ba9357237d9d6b22be6761f63d91a265d1dc08cc693ae14576200d6aa7322eca439eea414634f5666c22ab29c67fbcdb", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "31303134363536343631", "sig": "30660231009d2deb753b8e16e6f24e1b718000daa0d4617242225f1b91192b1ea8acdca607b05f1c0da8e3cdbdc52f448a376f13b10231008654d2738725423c0934c20b28327f7a5ac53a61f296a5ce562c8684d2f3090d19811fe70dbce71f106c4060740981ec", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "39373034363738313139", "sig": "306402301c7c8d1c493bdb1f1290f04aed3c4a7cb0a83c36330a4fab50e68f235777579dd06a073a3857f226dae511a2569e928d023014e5058d70b7cfb04cfb0c3c1d3d6fe500328340860e4b7cc2b5f11cab09cba0c7b887274453ab30d9164c73fc1f6f36", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "343131303338333134", "sig": "3066023100cade486e6a8e78141b15dbe60095e42d8196fafd843c722c8c686a60063e701f30d8a488c2a18a63635a13bc8ff0a787023100ed7aa0208952d2d452432ffa1bbf7146080911cf7e87aa848ee90314b2afe427a80cd70187b3ac3572a360d4db6b17e5", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "353437383438353336", "sig": "306502302787240e7fd6d895098d1773733727ee5792fe644b0774b8530ddd758b347143d1939bb7c9a3312774cf7126e499f5ab023100ad215cb6681f287ffb96a6e7c41331a2e773e68791391c658f2e5c95cf82e3871e49c9fff08f7b540848c1a7cee2ab85", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "33333738363732383631", "sig": "3066023100aa92d0b7d6046210024b962fd79d1a27ee69c25936e5895cd92224b3f560829c11de20e7f52320bba91b87c4c7ef4962023100816c95ee54c677c4be1ba70317a90aaf1c1d2f233fd480d22cab453d9539657ce695e21952e6157ce3460680dc2fdbf2", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "313636353734353639", "sig": "306502304eda9fc1e0df8ef24f3148f8a737a76eceddfa6057441c877816ac402349f32571c8074611179968e6fe7cfc1f41a80b023100e0549e78e774377dffb9e742f05f5b1a1a2198571d0f2243fd25703029e0effac2808fad1c82efbdf0063d6032df33dc", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "363431343037363435", "sig": "3065023018a83b96dbd10de3a62fdab7142f201f9f480447bf000f6ee314da64d2351bbc7bb94cd1c551dee4828a603e6a853fca0231008fbf2a1a7ad4ed76a08748f41f5b3468a9a7cda57503aa71c455292bde2dc88a2580a65a6859d20f924aa7a5cea3743d", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "32303833363036303631", "sig": "306502302fb5726226521d1105cdd22e84ff46a36768ee4d71e6f5cfe720ddbd36ad645c05a7207c9f7cae2d8236d965ff64f943023100ac3f8b7841b31c95f27e99a86413b6aa9086fcdbd176f7de65a696d76edcb0775f2e257db75fa5aa716946f3d80b1cea", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "32383235303436303231", "sig": "306502302a38f4cc1da426f15d8c8dbed58608eec86862554f4d6d503dc6e162e72754b1298ad4508ae2a93d493c836b19548c4c0231009b51610514136d5dcfda3c4736a839288bc1f043ea362cf6e56dce3f4337204d5bdf92160a034f459b30410872dbeb0d", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "313432323938323834", "sig": "306502303407844641a75ba72ed05f9b7289ea2c8f9015c97e8d7aacec4a88b374a255371b19e7a2e7949f4b78b63334b4984434023100cee117c6fb8f8e47ce33357d7ed1a82b1ed912be3778eda9de303b2ee910c014eee3cf03e27f16fd94d7ed5a8e8c7b05", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "32313636343232303939", "sig": "3066023100b98e1313e62ff0155158059e422cb6e8ce70d103f1a95a77e1795ef2ae38a42596732405602299ee730b81e948083adf0231008a34134e86354d26f343343c05cdb46350b610ad16883f234e847fad97047ee4b8dfecd0bf77479b65643f9c35b74441", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "31343830313930313632", "sig": "306502300ae0a9cbd0de42e6590835374548708df9671208ab72e23bf7aa4329bbd0d4a696e99d04d36534e895737b468cff08ea0231008c8b6bb101ee844bc75cd2b3b32ea9c3b6c2ac5408c26f6a444335d730af2dce6f4bf1bf4585428e902f901eed10da62", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "35323138393734303738", "sig": "3066023100cf0310487690de93d344bba957a1ba380f72c2ae44975f69716b2aa2a866787dfc46629825ef19e5528395d872ff9367023100ff60a995865b6f5e6ffc15884e5901d55f384ffc62982e54a9c2dccaf7543246673c5bfe710f2a29daca77de766ee9ee", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "3130343538383435393733", "sig": "306502304a0f3d91ef6b9b6e512cd9c996f8e896717ea8c6d685834e4c31bcaf592a93d0f0b169efeb0ea52a5bea6be361d7a7b3023100c3d429d0daf1ee7c2bf4a0bc8f10cd7ce453b8e2a762b31885d36f5e03cdae3adb693bc2efe8a64d6e7bbc17f23b5500", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "343839323235333334", "sig": "3065023040f82935328e34e31f1966bd0bc1dfc2adf1d71d86fc6dd0f9e9e1930dfc357e30fa67722c562dd84cdb73fb715b622d023100cf40658591f34527587b0969a45ca5a30f87dbcf0b058f75c158ac883d52119030881c0aeb1f8e12682d06d072705550", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "35303433393832313335", "sig": "3065023100a3434df3d065f4b32957077f429bccdaa8875981006ce880585c160fca1f552dc6334583d7698226e650e95d86a896b7023054e2eb28c70796e3bea9f2fdc3799f7d6dde5b3cc84de7448296d65fd8a44260b0666cefa416adda5046f45a5b8a9ae7", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "3132393833353030363939", "sig": "3066023100b54b004489e12ec91e875f3062dff1f1bd0407e216162b4913a34f19943c8f967c1f7250ff0ce5f43a0b250bb9fae16b02310095c13a702ca6269ed8cac69291e01767c0f862648b0961238ef0b6be88cd316973a290bae4f50147816a49ab014a7d69", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "3131333236373331343032", "sig": "3066023100ea28a6b9158328d0711bfd10019643648e695c1fa9df2a7c2e1a6d3b03b6703bc763f8f0c701d7b925d35075da783f38023100b4bb6b034288af213ecabdcc2d55610181ba77b26673b1490e7e08a43f6e57fe20618a5adc7fbfcbe255fa79655aaeb1", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "33353733323839303232", "sig": "3065023100d973f5fa26a7069dac82790097db0d93dfc52a490ac2a84960c6dc52c2e84d2df1917c8d194789fe8981be40fbefb00602301dc1ab55752add3952ee3f5d86bb167ed1fdf20e19d5c893c1a6031c1a2b70701ba03cf7d78b89331d524c5dcf38462a", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "3131373339353634333738", "sig": "306402303d4ed5e71127a0da4aa63cc1d7ce517a450370dff65ef95b4185a44199181ec5ff70f80f6d7435e6bec4d6e58e73591b023027b2d65bf08ab8e745544225181638af5df08b85c9f7a9057e1605f145b3a1389661d9c990d0f4d82636dc6332b6941d", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "3733383230373935303035", "sig": "3065023100e36ffc2ca7e62c2fe35c7761a78ae2839d1503b437cc7a89eee28ec74d75d2948c7a3148070ad715f7ce8260c160611d02300c18edef913d63ac220cd4b28aef1cd43aa9acf7b0fe889c4a28ac22934e46aa2a99a5b803a61471bd5bfeef8c86b17b", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "34353233333738383430", "sig": "30640230148906bcfc686aa3f608321d17a425373bd9ce2f47a35a6a01124992cba56e744daef2b00dececff63ed96d5d7c2e15802304303a5c7049766956679f204e655301dc16fe9cd85f6ebb1997410e0d2029240181c946d86800cc6ba882f276603db29", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "39303239363535323738", "sig": "306502305264c26ceb0481b74472f26ecca4459785a2d63c9494d8744e42e9eea5799bfb0fa95ff3c8a5de2868098a025110bbe9023100e1858d96c814dbd39ca5dbde824be0894b4e418fe51306784a8fd0680850a5b32958714ae9124e9ad6372412212df1be", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "36363030353238363330", "sig": "30650230273e229dddfaa7ba5763c8563f3a05c7d2d2471331225e8f26a20e0ae656115c62ddfac3895f10012253ba7bb79a65ca02310089a6ab6fd5bca31659278ac3f3e79ded9a47a6fd166fc746b79fc3bd9d21e5f332bb1e89a14efcd3647f94aff9715aba", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "38333639343536343136", "sig": "3066023100f447dcc8ce6573a2da5fd58a3974f46a8d76608e477742b68c2e93245f359567a953cd18dc1d95fa7e3c5d02210cfc0e023100b273a9ce5a021a66f6a44f2ae94f2f5fab6e3b5016648c9df38756a5b7e71d07aa453240d39bef0d22afab1e19095694", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "3130303139393230373030", "sig": "30660231009378874a38f221b27d8b7ab06d827130d0db2e9c43f443e9cdd254ef77a4f7aae589a6c1499970dd5acf516802688aa6023100f94a6319379598119bddf9f787e74b135ad193b692e44a848ac6d1d0443d49adcdcf1a9f530686e76080840e1b647be2", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "31373032333133323830", "sig": "3065023100a48cc74a1d39a0b8cfcd12768277535389790c9ad2638aca42401a44e80ff0ceb40e193cd9e27e39443a1d2665de485c02301569ca82e563df78feb1d704953b8c35b7eda09259fc16ab262304d0c09383f550cfdc97ce549874212e3fc7b83f6d4b", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "31383038393439383431", "sig": "3066023100e6049a43aa5761ad4193a739da84c734f39f2f79f8d97241982082185fe9cef7747b68c59ef3909f18af6c5df48ee559023100bb7800436791bae910fbfc6b69c0b7e6972dea1bd5ad82aaf97ebb85d920a15f9f5f280fd813281f36b2ae3c53fd6e41", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "31373438323839333135", "sig": "30640230148d734104a52c9d58ca1ad7ba56fd35147e8d324a0923ebc9c5d8b393f492bce1da6c9d1fa68d4faeebf0868e03f17102304629809043f228f0f3adfc0696c2e9d800791ee82034c5fac37fc521e40f9bf2250c53036b8286e032959ed5f3a58483", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "36363232313031303338", "sig": "3065023016762ba4645c905590e6a3dd4b1e2af693cc4e64153812f93b80ed4d1c07e664e5b22880f24a120d4b48e1400fcd3afb023100d481c2f9b255bba2ac29fe055536c3c7fa92e4f34cfdc5b5f5227f582736c87c1350bcb760069c4004ac33fbe2ed3549", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "3132313833363339", "sig": "3066023100830c8c92465fc7f1a654d22eaeadf62b5fa29bebc8e184ca104913eb8bea234d287961900f308d88f9bb7387c8de58b2023100960eb635db967cd69f80123e0a43501c6161cbd9e8058f5bb7506cc24fba3a3694688b5b0e066bf2ccaecbb5a9eb0c9d", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "393535323437333036", "sig": "306402301377906f42629010e79bc60142234a44c78e8887f6dc4086bdc7e9bf94c92c84aaf48efb0269205b8bd6e324224df17802306f430a1937fc0463143c80a0e132074a64acc825c2f4ed8b0de03204a681bf171e9e002a88431fd388c7a906511171a4", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "31393531393636303731", "sig": "3066023100d1d335dca83a7ef4770e68ff82d2bb75341abf72a362c88d8a176020db37bfd5874e14c0cb011cb316bc6e6d1322a893023100c61fc7dd9f66b8cf2f8c9a780089fe31a20608b458ea12a246a1cba34566c2d833a71bbe09482ad3c26bf9bb6088fd5a", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "32383830303734303635", "sig": "30650230536183374fa37f210723fe3aabde18997be247be006e20c5d17d8e4c79790ddfe4e6f17f8823d36aceeea22c9e44ba9d023100b6a0f63b27876d1686b9e601c273c20530c765e506605cea39d9accba9a7007bb10d64333e5e22125f34d1dfc8e60461", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "3136373937333631323130", "sig": "306402302fa6073fd290a699ff0a4bd425a69d4e151a3ec3faa65c504d5e41b45c2a738d343a99865690bcc22c03230c3949ce3f02303989dd2d632007c498ed830d277cc1193590f23fe5e778deeffdbb2c135258327b121a81313a0bcc9f77db206afddd8f", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "323235353833343239", "sig": "3065023100cf60fb9789b449ac9b9f022dc83481777675e55f09b4cba5d8349c0e16907f8929e3b538cce4d71c01b010a633807997023067654a0bebf3a63fa93cf9906c846cf5edbb03968c86eef5e7555a14d606009006f9f9e4569be3375a9a8aa04aa20c45", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "3236393238343433343334", "sig": "306402306ab23c76784d003ec508233f7f5e6461d6806c66af62c4769d45ec8751d276bdb68b2efc4fcf83f675a3101941f9adec02306f306bd6f782aba3c7d0c0d6c0e0e8897f967f0de2a84db1d67e477378ea425dcc6fc6113e5a5f67ac34eca2c69d0bdf", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "36323734383032323238", "sig": "30650230526365e36472883664eb011bdf9a1503397f0e3509066665f9c276e367cf2730774d4525125cadccfef0c0cf28949a2b023100948cbaf1c0e7f0ccca5f5d2a7e4a94f4a7ec43d2cf69ae5ebecb41521daa9e618615208cb62b35809fc40401670ae3b5", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "31313439323431363433", "sig": "3066023100b1cf39b023502a1aa3daca372c295c1eb3c5fee2a841ef2cfd4087ffdd4e35e8804b8d879a939216d24fae1bd1e7f19a0231008b8bea55a9625efb9733d1dcfad8d426b81c9e71fb53b246ae54c3196972d284172e6b1911bafe6b631e5e48344c4409", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d7dea8ac1e4b9aea2d3d1ad7d6a877e116a8bcdb87c8463c69ad78f8074f33b2c179ac0580af901d21851cf15b3a5e342a088198c090b9e367695a1c7fa110b66828d8f07bafe6eb2521dd20e517cebd295cc9cce52e0c0081b4cf7fe5ea884e", "wx": "00d7dea8ac1e4b9aea2d3d1ad7d6a877e116a8bcdb87c8463c69ad78f8074f33b2c179ac0580af901d21851cf15b3a5e34", "wy": "2a088198c090b9e367695a1c7fa110b66828d8f07bafe6eb2521dd20e517cebd295cc9cce52e0c0081b4cf7fe5ea884e"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d7dea8ac1e4b9aea2d3d1ad7d6a877e116a8bcdb87c8463c69ad78f8074f33b2c179ac0580af901d21851cf15b3a5e342a088198c090b9e367695a1c7fa110b66828d8f07bafe6eb2521dd20e517cebd295cc9cce52e0c0081b4cf7fe5ea884e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE196orB5LmuotPRrX1qh34RaovNuHyEY8\naa14+AdPM7LBeawFgK+QHSGFHPFbOl40KgiBmMCQueNnaVocf6EQtmgo2PB7r+br\nJSHdIOUXzr0pXMnM5S4MAIG0z3/l6ohO\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "304d0218389cb27e0bc8d21fa7e5f24cb74f58851313e696333ad68b023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "valid", "flags": []}, {"tcId": 328, "comment": "r too large", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffe023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04cba0cc097c795cd467d835977764b7740fa480c3cad83a726d68bbfe8dbb752934eb4fb6c767dc09bdda6d0d2d057ae8e277c7ad56d6f21099d998e7bfded8c8d2d100c8ebd9f57681a633b91ad0890c020e724689c6b1b4b8f35b49679a4fa3", "wx": "00cba0cc097c795cd467d835977764b7740fa480c3cad83a726d68bbfe8dbb752934eb4fb6c767dc09bdda6d0d2d057ae8", "wy": "00e277c7ad56d6f21099d998e7bfded8c8d2d100c8ebd9f57681a633b91ad0890c020e724689c6b1b4b8f35b49679a4fa3"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004cba0cc097c795cd467d835977764b7740fa480c3cad83a726d68bbfe8dbb752934eb4fb6c767dc09bdda6d0d2d057ae8e277c7ad56d6f21099d998e7bfded8c8d2d100c8ebd9f57681a633b91ad0890c020e724689c6b1b4b8f35b49679a4fa3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEy6DMCXx5XNRn2DWXd2S3dA+kgMPK2Dpy\nbWi7/o27dSk060+2x2fcCb3abQ0tBXro4nfHrVbW8hCZ2Zjnv97YyNLRAMjr2fV2\ngaYzuRrQiQwCDnJGicaxtLjzW0lnmk+j\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "r,s are large", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52971", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ffc271e311cefc1c133202448e2ee74457bb68951b0e575747cc6ee9c0691720bcf9eba23c18f96e845cda05e06d4f7bdc7c5d17e91f12abf3638fc8e87866f0373f0ffa90c2c759712d3fb163730a184e4707ef424ef833079c0ed5e1498344", "wx": "00ffc271e311cefc1c133202448e2ee74457bb68951b0e575747cc6ee9c0691720bcf9eba23c18f96e845cda05e06d4f7b", "wy": "00dc7c5d17e91f12abf3638fc8e87866f0373f0ffa90c2c759712d3fb163730a184e4707ef424ef833079c0ed5e1498344"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ffc271e311cefc1c133202448e2ee74457bb68951b0e575747cc6ee9c0691720bcf9eba23c18f96e845cda05e06d4f7bdc7c5d17e91f12abf3638fc8e87866f0373f0ffa90c2c759712d3fb163730a184e4707ef424ef833079c0ed5e1498344", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE/8Jx4xHO/BwTMgJEji7nRFe7aJUbDldX\nR8xu6cBpFyC8+euiPBj5boRc2gXgbU973HxdF+kfEqvzY4/I6Hhm8Dc/D/qQwsdZ\ncS0/sWNzChhORwfvQk74MwecDtXhSYNE\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d1aee55fdc2a716ba2fabcb57020b72e539bf05c7902f98e105bf83d4cc10c2a159a3cf7e01d749d2205f4da6bd8fcf1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0474ae987af3a0ebd9f9a4f57be2d7d1c079c9ec7928a1da8c38ff0c2b9bd9822fa7603decc1becabd3f6ceebb353cb798e0c9ac6f4f575fa1ed2daf36224d09aa569f8b1d25b62fbaeddf766a34b9309000cce2447017a5cd8a3ce76dd5428ff1", "wx": "74ae987af3a0ebd9f9a4f57be2d7d1c079c9ec7928a1da8c38ff0c2b9bd9822fa7603decc1becabd3f6ceebb353cb798", "wy": "00e0c9ac6f4f575fa1ed2daf36224d09aa569f8b1d25b62fbaeddf766a34b9309000cce2447017a5cd8a3ce76dd5428ff1"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000474ae987af3a0ebd9f9a4f57be2d7d1c079c9ec7928a1da8c38ff0c2b9bd9822fa7603decc1becabd3f6ceebb353cb798e0c9ac6f4f575fa1ed2daf36224d09aa569f8b1d25b62fbaeddf766a34b9309000cce2447017a5cd8a3ce76dd5428ff1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEdK6YevOg69n5pPV74tfRwHnJ7HkoodqM\nOP8MK5vZgi+nYD3swb7KvT9s7rs1PLeY4Mmsb09XX6HtLa82Ik0Jqlafix0lti+6\n7d92ajS5MJAAzOJEcBelzYo8523VQo/x\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b6b681dc484f4f020fd3f7e626d88edc6ded1b382ef3e143d60887b51394260832d4d8f2ef70458f9fa90e38c2e19e4f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04dc23280ae627109b86d60be0e70cec0582a5b318fa8254dfcb97045eefdf1aa272937de99c6b3972c4cd108b4fc681cc0ec5438a5d44908c479da428e5b2e4f5ae93bf82b427d8dca996e23d930700082828112faac7f710928daa670b7576cb", "wx": "00dc23280ae627109b86d60be0e70cec0582a5b318fa8254dfcb97045eefdf1aa272937de99c6b3972c4cd108b4fc681cc", "wy": "0ec5438a5d44908c479da428e5b2e4f5ae93bf82b427d8dca996e23d930700082828112faac7f710928daa670b7576cb"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004dc23280ae627109b86d60be0e70cec0582a5b318fa8254dfcb97045eefdf1aa272937de99c6b3972c4cd108b4fc681cc0ec5438a5d44908c479da428e5b2e4f5ae93bf82b427d8dca996e23d930700082828112faac7f710928daa670b7576cb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE3CMoCuYnEJuG1gvg5wzsBYKlsxj6glTf\ny5cEXu/fGqJyk33pnGs5csTNEItPxoHMDsVDil1EkIxHnaQo5bLk9a6Tv4K0J9jc\nqZbiPZMHAAgoKBEvqsf3EJKNqmcLdXbL\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049bdf0a7793d0375a896a7f3084d3c45f8dfcd7f73d045484e71128713cab49b4c218af17e048fa6dbe32f2e289ee83950be28a090c2f6769f85e5ff1cfb300bd0ae907b5d5367ede98dfd3e6a81c4b4903289973285a4ef91b790ad12761321c", "wx": "009bdf0a7793d0375a896a7f3084d3c45f8dfcd7f73d045484e71128713cab49b4c218af17e048fa6dbe32f2e289ee8395", "wy": "0be28a090c2f6769f85e5ff1cfb300bd0ae907b5d5367ede98dfd3e6a81c4b4903289973285a4ef91b790ad12761321c"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049bdf0a7793d0375a896a7f3084d3c45f8dfcd7f73d045484e71128713cab49b4c218af17e048fa6dbe32f2e289ee83950be28a090c2f6769f85e5ff1cfb300bd0ae907b5d5367ede98dfd3e6a81c4b4903289973285a4ef91b790ad12761321c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEm98Kd5PQN1qJan8whNPEX4381/c9BFSE\n5xEocTyrSbTCGK8X4Ej6bb4y8uKJ7oOVC+KKCQwvZ2n4Xl/xz7MAvQrpB7XVNn7e\nmN/T5qgcS0kDKJlzKFpO+Rt5CtEnYTIc\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0490770515f27351111e56d3bf14fe392d42186cb870374a8d40870830057bf52da8c2e27691236a0de2876893f9b77ab2fb1cb5dcfd30e3a2a0056a5dbbc1c5d626ba669cbbfe8bdb121de7cc394a61721d5c3c73a3f5dea9388cad7fbca72649", "wx": "0090770515f27351111e56d3bf14fe392d42186cb870374a8d40870830057bf52da8c2e27691236a0de2876893f9b77ab2", "wy": "00fb1cb5dcfd30e3a2a0056a5dbbc1c5d626ba669cbbfe8bdb121de7cc394a61721d5c3c73a3f5dea9388cad7fbca72649"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000490770515f27351111e56d3bf14fe392d42186cb870374a8d40870830057bf52da8c2e27691236a0de2876893f9b77ab2fb1cb5dcfd30e3a2a0056a5dbbc1c5d626ba669cbbfe8bdb121de7cc394a61721d5c3c73a3f5dea9388cad7fbca72649", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEkHcFFfJzUREeVtO/FP45LUIYbLhwN0qN\nQIcIMAV79S2owuJ2kSNqDeKHaJP5t3qy+xy13P0w46KgBWpdu8HF1ia6Zpy7/ovb\nEh3nzDlKYXIdXDxzo/XeqTiMrX+8pyZJ\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020103", "result": "valid", "flags": []}, {"tcId": 335, "comment": "r is larger than n", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52975020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f7f5f9382da5dd3d41cc2d4e62570b581b67dc2ad456de3af75ad1ce7be27af8a77771e67a08f2dc87ac91c5a744886cf7194e819162862cb7c39e39445da63adfe10704ef7407f1fcef062c8f86729c700da4f9e747c5c77e32dd25e7f867af", "wx": "00f7f5f9382da5dd3d41cc2d4e62570b581b67dc2ad456de3af75ad1ce7be27af8a77771e67a08f2dc87ac91c5a744886c", "wy": "00f7194e819162862cb7c39e39445da63adfe10704ef7407f1fcef062c8f86729c700da4f9e747c5c77e32dd25e7f867af"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f7f5f9382da5dd3d41cc2d4e62570b581b67dc2ad456de3af75ad1ce7be27af8a77771e67a08f2dc87ac91c5a744886cf7194e819162862cb7c39e39445da63adfe10704ef7407f1fcef062c8f86729c700da4f9e747c5c77e32dd25e7f867af", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE9/X5OC2l3T1BzC1OYlcLWBtn3CrUVt46\n91rRznvievind3Hmegjy3IeskcWnRIhs9xlOgZFihiy3w545RF2mOt/hBwTvdAfx\n/O8GLI+GcpxwDaT550fFx34y3SXn+Gev\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "s is larger than n", "msg": "313233343030", "sig": "3036020102023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accd7fffa", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0424f0d59e6bab85cce63823e4b075c91520e0f7090c58dbae24774ef25917cf9fab1030513f4a10b84c59df529bc1d3b12469f23a674bf49a0383d239ca15676704eab86bd3149ea041a274643866643b786bb17c5d0f10dbf2bfc775c7087cc1", "wx": "24f0d59e6bab85cce63823e4b075c91520e0f7090c58dbae24774ef25917cf9fab1030513f4a10b84c59df529bc1d3b1", "wy": "2469f23a674bf49a0383d239ca15676704eab86bd3149ea041a274643866643b786bb17c5d0f10dbf2bfc775c7087cc1"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000424f0d59e6bab85cce63823e4b075c91520e0f7090c58dbae24774ef25917cf9fab1030513f4a10b84c59df529bc1d3b12469f23a674bf49a0383d239ca15676704eab86bd3149ea041a274643866643b786bb17c5d0f10dbf2bfc775c7087cc1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEJPDVnmurhczmOCPksHXJFSDg9wkMWNuu\nJHdO8lkXz5+rEDBRP0oQuExZ31KbwdOxJGnyOmdL9JoDg9I5yhVnZwTquGvTFJ6g\nQaJ0ZDhmZDt4a7F8XQ8Q2/K/x3XHCHzB\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "small r and s^-1", "msg": "313233343030", "sig": "3036020201000230489122448912244891224489122448912244891224489122347ce79bc437f4d071aaa92c7d6c882ae8734dc18cb0d553", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0439833aec7515dacd9546bab8dc740417f14d200bd26041bbf43266a8644628da82dbf53097fe43dca1c92b09832466ec67f862c02c8911343a146fddc8246c168376e4166e32bad39db5be2b74e58410b4e9cc4701dd0b97ba544142e66d7715", "wx": "39833aec7515dacd9546bab8dc740417f14d200bd26041bbf43266a8644628da82dbf53097fe43dca1c92b09832466ec", "wy": "67f862c02c8911343a146fddc8246c168376e4166e32bad39db5be2b74e58410b4e9cc4701dd0b97ba544142e66d7715"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000439833aec7515dacd9546bab8dc740417f14d200bd26041bbf43266a8644628da82dbf53097fe43dca1c92b09832466ec67f862c02c8911343a146fddc8246c168376e4166e32bad39db5be2b74e58410b4e9cc4701dd0b97ba544142e66d7715", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMH<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAEOYM67HUV2s2VRrq43HQEF/FNIAvSYEG7\n9DJmqGRGKNqC2/Uwl/5D3KHJKwmDJGbsZ/hiwCyJETQ6FG/dyCRsFoN25BZuMrrT\nnbW+K3TlhBC06cxHAd0Ll7pUQULmbXcV\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "303c02072d9b4d347952cd023100ce751512561b6f57c75342848a3ff98ccf9c3f0219b6b68d00449e6c971a85d2e2ce73554b59219d54d2083b46327351", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046cc5f5640d396de25e6b81331c1d4feba418319f984d8a1e179da59739d0d40971585e7c02d68c9a62d426ca59128e0ffeab57963b965302cffe9645cf3ee449846381d82d5814e8ca77167ccf4c20ec54278e874f834725d22e82b910c24c2a", "wx": "6cc5f5640d396de25e6b81331c1d4feba418319f984d8a1e179da59739d0d40971585e7c02d68c9a62d426ca59128e0f", "wy": "00feab57963b965302cffe9645cf3ee449846381d82d5814e8ca77167ccf4c20ec54278e874f834725d22e82b910c24c2a"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046cc5f5640d396de25e6b81331c1d4feba418319f984d8a1e179da59739d0d40971585e7c02d68c9a62d426ca59128e0ffeab57963b965302cffe9645cf3ee449846381d82d5814e8ca77167ccf4c20ec54278e874f834725d22e82b910c24c2a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEbMX1ZA05beJea4EzHB1P66QYMZ+YTYoe\nF52llznQ1AlxWF58AtaMmmLUJspZEo4P/qtXljuWUwLP/pZFzz7kSYRjgdgtWBTo\nyncWfM9MIOxUJ46HT4NHJdIugrkQwkwq\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3041020d1033e67e37b32b445580bf4efb02302ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad5215c51b320e460542f9cc38968ccdf4263684004eb79a452", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04567e0986a89e4a51ff44efdf924e9970cbdaf5796dea617f93e6e513f73cb529e7a666bd4338465c90ddd3f61823d6185b252f20921f66a72dfcd4d1e323aa05487abb16c797820f349daa04724f6a0e81423ddf74fdb17f0801d635d7af213d", "wx": "567e0986a89e4a51ff44efdf924e9970cbdaf5796dea617f93e6e513f73cb529e7a666bd4338465c90ddd3f61823d618", "wy": "5b252f20921f66a72dfcd4d1e323aa05487abb16c797820f349daa04724f6a0e81423ddf74fdb17f0801d635d7af213d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004567e0986a89e4a51ff44efdf924e9970cbdaf5796dea617f93e6e513f73cb529e7a666bd4338465c90ddd3f61823d6185b252f20921f66a72dfcd4d1e323aa05487abb16c797820f349daa04724f6a0e81423ddf74fdb17f0801d635d7af213d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMH<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAEVn4JhqieSlH/RO/fkk6ZcMva9Xlt6mF/\nk+blE/c8tSnnpma9QzhGXJDd0/YYI9YYWyUvIJIfZqct/NTR4yOqBUh6uxbHl4IP\nNJ2qBHJPag6BQj3fdP2xfwgB1jXXryE9\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "303602020100023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0495512f92e55b5d18003397b822c1173f4e25a2640a4a68bb880a6ca8605cbfb83c75dbddc4937ed822e56acde8f47c7348e4ff027a1b0a2d5790f68c69923f3231ac61074caad2a022f6eabf8c258bdb8142be43ffa16a6f2c52f33cba006400", "wx": "0095512f92e55b5d18003397b822c1173f4e25a2640a4a68bb880a6ca8605cbfb83c75dbddc4937ed822e56acde8f47c73", "wy": "48e4ff027a1b0a2d5790f68c69923f3231ac61074caad2a022f6eabf8c258bdb8142be43ffa16a6f2c52f33cba006400"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000495512f92e55b5d18003397b822c1173f4e25a2640a4a68bb880a6ca8605cbfb83c75dbddc4937ed822e56acde8f47c7348e4ff027a1b0a2d5790f68c69923f3231ac61074caad2a022f6eabf8c258bdb8142be43ffa16a6f2c52f33cba006400", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAElVEvkuVbXRgAM5e4IsEXP04lomQKSmi7\niApsqGBcv7g8ddvdxJN+2CLlas3o9HxzSOT/AnobCi1XkPaMaZI/MjGsYQdMqtKg\nIvbqv4wli9uBQr5D/6FqbyxS8zy6AGQA\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3041020d062522bbd3ecbe7c39e93e7c24023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0474d5679e10edc41eb06ba54a1de2c9c71820bbac14f3758bb7fb593dddbb2e573e0d7a785344961399da18c8f615ae1df71e1c0ea892931571da09432ac46f6cbf53129e1e3e74c567180c037df59da84c8374b295b5a0ec6100ce9d800cd05e", "wx": "74d5679e10edc41eb06ba54a1de2c9c71820bbac14f3758bb7fb593dddbb2e573e0d7a785344961399da18c8f615ae1d", "wy": "00f71e1c0ea892931571da09432ac46f6cbf53129e1e3e74c567180c037df59da84c8374b295b5a0ec6100ce9d800cd05e"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000474d5679e10edc41eb06ba54a1de2c9c71820bbac14f3758bb7fb593dddbb2e573e0d7a785344961399da18c8f615ae1df71e1c0ea892931571da09432ac46f6cbf53129e1e3e74c567180c037df59da84c8374b295b5a0ec6100ce9d800cd05e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEdNVnnhDtxB6wa6VKHeLJxxggu6wU83WL\nt/tZPd27Llc+DXp4U0SWE5naGMj2Fa4d9x4cDqiSkxVx2glDKsRvbL9TEp4ePnTF\nZxgMA331nahMg3SylbWg7GEAzp2ADNBe\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3065023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc528f3023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041764c83ff4c28f7b690ca1c4b05832d78394f0aa48de452eb7b470526f4099d45de563b506c1570eb9b0f899a5f03f5aff89e562385d77b2c5d48dbb54501960997566bca5dcdee15848b907ee7457f8e46a221f64091c36f8d3053147c1a628", "wx": "1764c83ff4c28f7b690ca1c4b05832d78394f0aa48de452eb7b470526f4099d45de563b506c1570eb9b0f899a5f03f5a", "wy": "00ff89e562385d77b2c5d48dbb54501960997566bca5dcdee15848b907ee7457f8e46a221f64091c36f8d3053147c1a628"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041764c83ff4c28f7b690ca1c4b05832d78394f0aa48de452eb7b470526f4099d45de563b506c1570eb9b0f899a5f03f5aff89e562385d77b2c5d48dbb54501960997566bca5dcdee15848b907ee7457f8e46a221f64091c36f8d3053147c1a628", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEF2TIP/TCj3tpDKHEsFgy14OU8KpI3kUu\nt7RwUm9AmdRd5WO1BsFXDrmw+Jml8D9a/4nlYjhdd7LF1I27VFAZYJl1Zryl3N7h\nWEi5B+50V/jkaiIfZAkcNvjTBTFHwaYo\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "s == 1", "msg": "313233343030", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020101", "result": "valid", "flags": []}, {"tcId": 344, "comment": "s == 0", "msg": "313233343030", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04100fd7ac4ae442ab989f94a10a1f310f799d76980d00a14418db067b144bf45fa7639446fad508b76fd3ad9c9fe55810693598529b8349a28dd1d0632039ff0897523fed9af2356c0e36612135ed629369448b97d165ae5b2fe5c5ad396d2b06", "wx": "100fd7ac4ae442ab989f94a10a1f310f799d76980d00a14418db067b144bf45fa7639446fad508b76fd3ad9c9fe55810", "wy": "693598529b8349a28dd1d0632039ff0897523fed9af2356c0e36612135ed629369448b97d165ae5b2fe5c5ad396d2b06"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004100fd7ac4ae442ab989f94a10a1f310f799d76980d00a14418db067b144bf45fa7639446fad508b76fd3ad9c9fe55810693598529b8349a28dd1d0632039ff0897523fed9af2356c0e36612135ed629369448b97d165ae5b2fe5c5ad396d2b06", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEA/XrErkQquYn5ShCh8xD3mddpgNAKFE\nGNsGexRL9F+nY5RG+tUIt2/TrZyf5VgQaTWYUpuDSaKN0dBjIDn/CJdSP+2a8jVs\nDjZhITXtYpNpRIuX0WWuWy/lxa05bSsG\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d9a08d4f9f8708471e2e6b04ce08750c395b80f4a14169123e2fd97556c1171d82e87165a77b2dfd089ad25382ef4251517d0c26bebfce8483bfb089243d82eb0e712a7d2e7f71f9abb82ddf16c2e525146c7dc5686fb7ad334022ad092d32a4", "wx": "00d9a08d4f9f8708471e2e6b04ce08750c395b80f4a14169123e2fd97556c1171d82e87165a77b2dfd089ad25382ef4251", "wy": "517d0c26bebfce8483bfb089243d82eb0e712a7d2e7f71f9abb82ddf16c2e525146c7dc5686fb7ad334022ad092d32a4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d9a08d4f9f8708471e2e6b04ce08750c395b80f4a14169123e2fd97556c1171d82e87165a77b2dfd089ad25382ef4251517d0c26bebfce8483bfb089243d82eb0e712a7d2e7f71f9abb82ddf16c2e525146c7dc5686fb7ad334022ad092d32a4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE2aCNT5+HCEceLmsEzgh1DDlbgPShQWkS\nPi/ZdVbBFx2C6HFlp3st/Qia0lOC70JRUX0MJr6/zoSDv7CJJD2C6w5xKn0uf3H5\nq7gt3xbC5SUUbH3FaG+3rTNAIq0JLTKk\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0468133653284ee7e9a8cccf584a9e4f06bc31a3eb031999f0229db7c2b10630424c7ee7513e40319e3972c1a5152d5d28a547a17df730d86278de44cc099643ebe1f07e48618de255bc672dff63c58f86b2db29c89f109147d8d6be1f03c466e5", "wx": "68133653284ee7e9a8cccf584a9e4f06bc31a3eb031999f0229db7c2b10630424c7ee7513e40319e3972c1a5152d5d28", "wy": "00a547a17df730d86278de44cc099643ebe1f07e48618de255bc672dff63c58f86b2db29c89f109147d8d6be1f03c466e5"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000468133653284ee7e9a8cccf584a9e4f06bc31a3eb031999f0229db7c2b10630424c7ee7513e40319e3972c1a5152d5d28a547a17df730d86278de44cc099643ebe1f07e48618de255bc672dff63c58f86b2db29c89f109147d8d6be1f03c466e5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEaBM2UyhO5+mozM9YSp5PBrwxo+sDGZnw\nIp23wrEGMEJMfudRPkAxnjlywaUVLV0opUehffcw2GJ43kTMCZZD6+HwfkhhjeJV\nvGct/2PFj4ay2ynInxCRR9jWvh8DxGbl\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294ba", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04233c399596e090c132c8e8b33c2ed443d73ab9abafaece5e47c6a6dc82d3fcc006ebf8b5b1c5fd028c97097909d5be38035f777dffaac0ef909bbe6be4e01ecfae3b36b2ea2095e352c179737980f96124d45b76677274d975eda57436f453de", "wx": "233c399596e090c132c8e8b33c2ed443d73ab9abafaece5e47c6a6dc82d3fcc006ebf8b5b1c5fd028c97097909d5be38", "wy": "035f777dffaac0ef909bbe6be4e01ecfae3b36b2ea2095e352c179737980f96124d45b76677274d975eda57436f453de"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004233c399596e090c132c8e8b33c2ed443d73ab9abafaece5e47c6a6dc82d3fcc006ebf8b5b1c5fd028c97097909d5be38035f777dffaac0ef909bbe6be4e01ecfae3b36b2ea2095e352c179737980f96124d45b76677274d975eda57436f453de", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEIzw5lZbgkMEyyOizPC7UQ9c6uauvrs5e\nR8am3ILT/MAG6/i1scX9AoyXCXkJ1b44A193ff+qwO+Qm75r5OAez647NrLqIJXj\nUsF5c3mA+WEk1Ft2Z3J02XXtpXQ29FPe\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "u1 == 1", "msg": "313233343030", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100f8723083bde48fae6e2f3ba5d836c2e954aec113030836fb978c08ab1b5a3dfe54aa2fab2423747e3b4fa70ec744894c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04357931297f6a369df1e1835ae66a97229c2ab63a75a55a9db2cdbf2e8582c7c0d8aa79f2c337e4e01980d7d84fd7991706b1de385965ae26fc38ab2b18a8ea60e52faea5c2e27666913858917cb1cf5b5c0bdc9c1498389c1db155e54d3198e2", "wx": "357931297f6a369df1e1835ae66a97229c2ab63a75a55a9db2cdbf2e8582c7c0d8aa79f2c337e4e01980d7d84fd79917", "wy": "06b1de385965ae26fc38ab2b18a8ea60e52faea5c2e27666913858917cb1cf5b5c0bdc9c1498389c1db155e54d3198e2"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004357931297f6a369df1e1835ae66a97229c2ab63a75a55a9db2cdbf2e8582c7c0d8aa79f2c337e4e01980d7d84fd7991706b1de385965ae26fc38ab2b18a8ea60e52faea5c2e27666913858917cb1cf5b5c0bdc9c1498389c1db155e54d3198e2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENXkxKX9qNp3x4YNa5mqXIpwqtjp1pVqd\nss2/LoWCx8DYqnnywzfk4BmA19hP15kXBrHeOFllrib8OKsrGKjqYOUvrqXC4nZm\nkThYkXyxz1tcC9ycFJg4nB2xVeVNMZji\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec63260230078dcf7c421b705191d0c45a27c93d16ab513eecfcf7c9042fd744d6d8dcefe1036fde07248d32fcb19c725c0580a027", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b760dcee338ca73c8cc69f0360d87253ef3632d302bdbf743a65f8762ecea207a5c5aff3add177378e133378d2c83a40abcba73c686f35e13d1cb44197bd763b5221d3b17ca7d888bbbc52eb2c33462036dd7a3b569290cb586d9e6514d69b92", "wx": "00b760dcee338ca73c8cc69f0360d87253ef3632d302bdbf743a65f8762ecea207a5c5aff3add177378e133378d2c83a40", "wy": "00abcba73c686f35e13d1cb44197bd763b5221d3b17ca7d888bbbc52eb2c33462036dd7a3b569290cb586d9e6514d69b92"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b760dcee338ca73c8cc69f0360d87253ef3632d302bdbf743a65f8762ecea207a5c5aff3add177378e133378d2c83a40abcba73c686f35e13d1cb44197bd763b5221d3b17ca7d888bbbc52eb2c33462036dd7a3b569290cb586d9e6514d69b92", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEt2Dc7jOMpzyMxp8DYNhyU+82MtMCvb90\nOmX4di7Oogelxa/zrdF3N44TM3jSyDpAq8unPGhvNeE9HLRBl712O1Ih07F8p9iI\nu7xS6ywzRiA23Xo7VpKQy1htnmUU1puS\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "u2 == 1", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fa8f96db8c7c9350d90ab49baaa941a79ebe62f017d54b6f83854f430408926e4a46335e44e1d67f0f18c7db2d70ca93b65df386caa193875fe91740214526a2ed17393d8bb62bdcee9f887802bc2d76ca9a304b94e795032956c8608c0e7f46", "wx": "00fa8f96db8c7c9350d90ab49baaa941a79ebe62f017d54b6f83854f430408926e4a46335e44e1d67f0f18c7db2d70ca93", "wy": "00b65df386caa193875fe91740214526a2ed17393d8bb62bdcee9f887802bc2d76ca9a304b94e795032956c8608c0e7f46"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fa8f96db8c7c9350d90ab49baaa941a79ebe62f017d54b6f83854f430408926e4a46335e44e1d67f0f18c7db2d70ca93b65df386caa193875fe91740214526a2ed17393d8bb62bdcee9f887802bc2d76ca9a304b94e795032956c8608c0e7f46", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+o+W24x8k1DZCrSbqqlBp56+YvAX1Utv\ng4VPQwQIkm5KRjNeROHWfw8Yx9stcMqTtl3zhsqhk4df6RdAIUUmou0XOT2Ltivc\n7p+IeAK8LXbKmjBLlOeVAylWyGCMDn9G\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa84ecde56a2cf73ea3abc092185cb1a51f34810f1ddd8c64d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0469bf123fb0d38b6a1c3f01a811e16ac78f40301332a0a18454fb4bd9b7c9516520f5ace9eddad328b8d283162eed1c759fa36f89c13419404c11c2ac982777cd30aea7e621351d96ba39676c26b36ccd109035d708da63ab9aefee3c82f6d405", "wx": "69bf123fb0d38b6a1c3f01a811e16ac78f40301332a0a18454fb4bd9b7c9516520f5ace9eddad328b8d283162eed1c75", "wy": "009fa36f89c13419404c11c2ac982777cd30aea7e621351d96ba39676c26b36ccd109035d708da63ab9aefee3c82f6d405"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000469bf123fb0d38b6a1c3f01a811e16ac78f40301332a0a18454fb4bd9b7c9516520f5ace9eddad328b8d283162eed1c759fa36f89c13419404c11c2ac982777cd30aea7e621351d96ba39676c26b36ccd109035d708da63ab9aefee3c82f6d405", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEab8SP7DTi2ocPwGoEeFqx49AMBMyoKGE\nVPtL2bfJUWUg9azp7drTKLjSgxYu7Rx1n6NvicE0GUBMEcKsmCd3zTCup+YhNR2W\nujlnbCazbM0QkDXXCNpjq5rv7jyC9tQF\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023052d0bad694a1853a24ba6937481240f8718f95b10102bcfe87d95839091e14aa1c38ba8e616126d4be6fe25a426c2dc4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041e5863f2dafa6a4d1a10a4cb18ebe792c0154aced0c5c2abe33f727335c720693e92749795539350d8503f209da1bea56f0889c25cd0aee834431262177f43b7ddb01a75532dd55086c44c1931cdd3e0312eea51d300050130f6e754aa9f92f8", "wx": "1e5863f2dafa6a4d1a10a4cb18ebe792c0154aced0c5c2abe33f727335c720693e92749795539350d8503f209da1bea5", "wy": "6f0889c25cd0aee834431262177f43b7ddb01a75532dd55086c44c1931cdd3e0312eea51d300050130f6e754aa9f92f8"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041e5863f2dafa6a4d1a10a4cb18ebe792c0154aced0c5c2abe33f727335c720693e92749795539350d8503f209da1bea56f0889c25cd0aee834431262177f43b7ddb01a75532dd55086c44c1931cdd3e0312eea51d300050130f6e754aa9f92f8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEHlhj8tr6ak0aEKTLGOvnksAVSs7QxcKr\n4z9yczXHIGk+knSXlVOTUNhQPyCdob6lbwiJwlzQrug0QxJiF39Dt92wGnVTLdVQ\nhsRMGTHN0+AxLupR0wAFATD251Sqn5L4\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02302412cc835da0a4357d1b7a986a76fe42b79542258c02dd7af927b27a9f9352ed3eedb6520a422e876949cb5fd0724090", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0487fc8357860b94528775f3787406b79d7a7d65d23d1d5707e66978be71aabae87bc539c24addf6c55468cea11cfb85bf3f881573285dd3742ecf062d5321c3d5f86212ba88ae75dd3945ebb3b44c37a178d440bfd72ca8f2e7c99cf6367da248", "wx": "0087fc8357860b94528775f3787406b79d7a7d65d23d1d5707e66978be71aabae87bc539c24addf6c55468cea11cfb85bf", "wy": "3f881573285dd3742ecf062d5321c3d5f86212ba88ae75dd3945ebb3b44c37a178d440bfd72ca8f2e7c99cf6367da248"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000487fc8357860b94528775f3787406b79d7a7d65d23d1d5707e66978be71aabae87bc539c24addf6c55468cea11cfb85bf3f881573285dd3742ecf062d5321c3d5f86212ba88ae75dd3945ebb3b44c37a178d440bfd72ca8f2e7c99cf6367da248", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEh/yDV4YLlFKHdfN4dAa3nXp9ZdI9HVcH\n5ml4vnGquuh7xTnCSt32xVRozqEc+4W/P4gVcyhd03QuzwYtUyHD1fhiErqIrnXd\nOUXrs7RMN6F41EC/1yyo8ufJnPY2faJI\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02301a2303bd73ab20717627366a498a23f4afe23f30b93b0f3be65b74e5eb19f2abef049411ba50146a305c5bb98169c597", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04eebe3763025894619173c8b4397deb7febbfe10fe2d283fd303d48691ebc8ba3ab1209278e763199a18f398b9c14840598640539d7ec66d3c43bfed723292c85856f02e020deff4e468bf3bf3c7fd08391d9525a2cb4f85fbebbb7945a5853ad", "wx": "00eebe3763025894619173c8b4397deb7febbfe10fe2d283fd303d48691ebc8ba3ab1209278e763199a18f398b9c148405", "wy": "0098640539d7ec66d3c43bfed723292c85856f02e020deff4e468bf3bf3c7fd08391d9525a2cb4f85fbebbb7945a5853ad"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004eebe3763025894619173c8b4397deb7febbfe10fe2d283fd303d48691ebc8ba3ab1209278e763199a18f398b9c14840598640539d7ec66d3c43bfed723292c85856f02e020deff4e468bf3bf3c7fd08391d9525a2cb4f85fbebbb7945a5853ad", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE7r43YwJYlGGRc8i0OX3rf+u/4Q/i0oP9\nMD1IaR68i6OrEgknjnYxmaGPOYucFIQFmGQFOdfsZtPEO/7XIykshYVvAuAg3v9O\nRovzvzx/0IOR2VJaLLT4X767t5RaWFOt\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02302805e063d315ae83078dcf7c421b705191d0c45a27c93d16a277765a9f34e9a4b2e3bac6291d3ba508e5769fdbc4920b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0441a0504da3995adb7665654b6ef7d0d00f77fcc536fc1cad41b0daca5a60dd88c99c20e698c99d5663eb532a57db08b34d2b0acb79b95b70cb0a5e2eba110061ef87f0d34b5bbfdeaf5184b67103f8a2bdcd20a7b9f09ad11811776659becb75", "wx": "41a0504da3995adb7665654b6ef7d0d00f77fcc536fc1cad41b0daca5a60dd88c99c20e698c99d5663eb532a57db08b3", "wy": "4d2b0acb79b95b70cb0a5e2eba110061ef87f0d34b5bbfdeaf5184b67103f8a2bdcd20a7b9f09ad11811776659becb75"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000441a0504da3995adb7665654b6ef7d0d00f77fcc536fc1cad41b0daca5a60dd88c99c20e698c99d5663eb532a57db08b34d2b0acb79b95b70cb0a5e2eba110061ef87f0d34b5bbfdeaf5184b67103f8a2bdcd20a7b9f09ad11811776659becb75", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEQaBQTaOZWtt2ZWVLbvfQ0A93/MU2/Byt\nQbDaylpg3YjJnCDmmMmdVmPrUypX2wizTSsKy3m5W3DLCl4uuhEAYe+H8NNLW7/e\nr1GEtnED+KK9zSCnufCa0RgRd2ZZvst1\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02305e063d315ae83078dcf7c421b705191d0c45a27c93d16ab4ff23e510c4a79cd1fa8a24dc1a179d3e092a72bc5c391080", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0445c152c4e642f156b50ef6252f2b0cdd36f20cfacbe389fd79e2fbf19f0810cfbfe5d157d2fcc9b2a649e9675fd86c074eeaab3bec18eff3b702e0e0f5c40ce928ae48161e06833ef3d76fa743c51b2711ca7c06cfc3a20ab804066251d2a115", "wx": "45c152c4e642f156b50ef6252f2b0cdd36f20cfacbe389fd79e2fbf19f0810cfbfe5d157d2fcc9b2a649e9675fd86c07", "wy": "4eeaab3bec18eff3b702e0e0f5c40ce928ae48161e06833ef3d76fa743c51b2711ca7c06cfc3a20ab804066251d2a115"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000445c152c4e642f156b50ef6252f2b0cdd36f20cfacbe389fd79e2fbf19f0810cfbfe5d157d2fcc9b2a649e9675fd86c074eeaab3bec18eff3b702e0e0f5c40ce928ae48161e06833ef3d76fa743c51b2711ca7c06cfc3a20ab804066251d2a115", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERcFSxOZC8Va1DvYlLysM3TbyDPrL44n9\neeL78Z8IEM+/5dFX0vzJsqZJ6Wdf2GwHTuqrO+wY7/O3AuDg9cQM6SiuSBYeBoM+\n89dvp0PFGycRynwGz8OiCrgEBmJR0qEV\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100bc0c7a62b5d060f1b9ef88436e0a323a188b44f927a2d569fe47ca21894f39a3f51449b8342f3a7c1254e578b8722100", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045f6849efa9aafd6a4030018579e39d241df4c192e5ba78c6e9b441aabdac8eb8f4b353865c1c9127ecccca468c41a561ec501582456fe6396643c368d2b9735c47384dbdcf2cc16927ab9b327c36350fe7e1f949e7ce14e60b1c1dbec8dff5f0", "wx": "5f6849efa9aafd6a4030018579e39d241df4c192e5ba78c6e9b441aabdac8eb8f4b353865c1c9127ecccca468c41a561", "wy": "00ec501582456fe6396643c368d2b9735c47384dbdcf2cc16927ab9b327c36350fe7e1f949e7ce14e60b1c1dbec8dff5f0"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045f6849efa9aafd6a4030018579e39d241df4c192e5ba78c6e9b441aabdac8eb8f4b353865c1c9127ecccca468c41a561ec501582456fe6396643c368d2b9735c47384dbdcf2cc16927ab9b327c36350fe7e1f949e7ce14e60b1c1dbec8dff5f0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEX2hJ76mq/WpAMAGFeeOdJB30wZLlunjG\n6bRBqr2sjrj0s1OGXByRJ+zMykaMQaVh7FAVgkVv5jlmQ8No0rlzXEc4Tb3PLMFp\nJ6ubMnw2NQ/n4flJ584U5gscHb7I3/Xw\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d315ae83078dcf7c421b705191d0c45a27c93d16ab513eecce49d6742e48c5aa2b36ea79df9d9e3277247c3d843d5887", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04c3ecd74e383f55b7ec8cf0579e6fedb9863ee0a82cc84cf13854dc1017aecb2a5969f15194a9ccb09e823559fcd7b6f11faa3cd553119de6efd237b9a84dfe520694ba373c8b60d5b2e741b35bbdd9cfa635353a1f0cf47042881684a96fe516", "wx": "00c3ecd74e383f55b7ec8cf0579e6fedb9863ee0a82cc84cf13854dc1017aecb2a5969f15194a9ccb09e823559fcd7b6f1", "wy": "1faa3cd553119de6efd237b9a84dfe520694ba373c8b60d5b2e741b35bbdd9cfa635353a1f0cf47042881684a96fe516"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004c3ecd74e383f55b7ec8cf0579e6fedb9863ee0a82cc84cf13854dc1017aecb2a5969f15194a9ccb09e823559fcd7b6f11faa3cd553119de6efd237b9a84dfe520694ba373c8b60d5b2e741b35bbdd9cfa635353a1f0cf47042881684a96fe516", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEw+zXTjg/VbfsjPBXnm/tuYY+4KgsyEzx\nOFTcEBeuyypZafFRlKnMsJ6CNVn817bxH6o81VMRnebv0je5qE3+UgaUujc8i2DV\nsudBs1u92c+mNTU6Hwz0cEKIFoSpb+UW\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100e2c087faeed9abb45e3942a10187bd6d2df94757e2584ca7599b3385119bc57f7573f71dfcc9161dd86a91096695d236", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e4efcd7525aa87a1390ca91cd3f0ad38613384377278c4e29b14264ca550e6e57e6c6559df830065caf902a2f8df41adff1121276e4228ac454d62994ca1a3cd24d500a90ddaaee2e5203da658504292bd81b62c4024a8fd4d0725e6a07c254a", "wx": "00e4efcd7525aa87a1390ca91cd3f0ad38613384377278c4e29b14264ca550e6e57e6c6559df830065caf902a2f8df41ad", "wy": "00ff1121276e4228ac454d62994ca1a3cd24d500a90ddaaee2e5203da658504292bd81b62c4024a8fd4d0725e6a07c254a"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e4efcd7525aa87a1390ca91cd3f0ad38613384377278c4e29b14264ca550e6e57e6c6559df830065caf902a2f8df41adff1121276e4228ac454d62994ca1a3cd24d500a90ddaaee2e5203da658504292bd81b62c4024a8fd4d0725e6a07c254a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE5O/NdSWqh6E5DKkc0/CtOGEzhDdyeMTi\nmxQmTKVQ5uV+bGVZ34MAZcr5AqL430Gt/xEhJ25CKKxFTWKZTKGjzSTVAKkN2q7i\n5SA9plhQQpK9gbYsQCSo/U0HJeagfCVK\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100e727c7f2d36924d4f4fb46d5d0c752e8aabb5317b2e59419d1d54ca40b148e12b60908edf846b56e4d64224fb8d7e885", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0454a018053bf8dff69ce32e1f8c0c9ba658dffcfc1200cbd89c16996aece05b84ba945164b4bcdb4d8b6dac967ac78c47edaafea84b25520478e67b328def37e5bdb94f18f3bce507cc24161aa4297477fff23968ae367cf0c3f2f70ed2bc205d", "wx": "54a018053bf8dff69ce32e1f8c0c9ba658dffcfc1200cbd89c16996aece05b84ba945164b4bcdb4d8b6dac967ac78c47", "wy": "00edaafea84b25520478e67b328def37e5bdb94f18f3bce507cc24161aa4297477fff23968ae367cf0c3f2f70ed2bc205d"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000454a018053bf8dff69ce32e1f8c0c9ba658dffcfc1200cbd89c16996aece05b84ba945164b4bcdb4d8b6dac967ac78c47edaafea84b25520478e67b328def37e5bdb94f18f3bce507cc24161aa4297477fff23968ae367cf0c3f2f70ed2bc205d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEVKAYBTv43/ac4y4fjAybpljf/PwSAMvY\nnBaZauzgW4S6lFFktLzbTYttrJZ6x4xH7ar+qEslUgR45nsyje835b25TxjzvOUH\nzCQWGqQpdHf/8jlorjZ88MPy9w7SvCBd\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100ad2f45296b5e7ac5db4596c8b7edbf078e706a4efefd43013f89f548eb1919353be15323e74f80a62e7c37108a58fbaf", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0468828912c312ed14280c954102f2d4ab06d58bd9e7abd0afcafa0c349d0f09100bc5c91156cefeb9d3e33721f5d1d5f469cc3a91967d5b964963044ea966e4a3e2488f3be4232f1a8723d2956c687240fb2f92d456bea0b087b1007b444141a9", "wx": "68828912c312ed14280c954102f2d4ab06d58bd9e7abd0afcafa0c349d0f09100bc5c91156cefeb9d3e33721f5d1d5f4", "wy": "69cc3a91967d5b964963044ea966e4a3e2488f3be4232f1a8723d2956c687240fb2f92d456bea0b087b1007b444141a9"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000468828912c312ed14280c954102f2d4ab06d58bd9e7abd0afcafa0c349d0f09100bc5c91156cefeb9d3e33721f5d1d5f469cc3a91967d5b964963044ea966e4a3e2488f3be4232f1a8723d2956c687240fb2f92d456bea0b087b1007b444141a9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEaIKJEsMS7RQoDJVBAvLUqwbVi9nnq9Cv\nyvoMNJ0PCRALxckRVs7+udPjNyH10dX0acw6kZZ9W5ZJYwROqWbko+JIjzvkIy8a\nhyPSlWxockD7L5LUVr6gsIexAHtEQUGp\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02310083c6e7be210db828c8e8622d13e49e8b55a89f767e7be481fb9d492c668a0ee02dc4f5dcb69eed3bcf4445e36922e4cd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041dc3d0da27139b88b61893d1bdee2e5fce3dcd8c4b65e1861ad0886068d32d905d343c4567ab20903f43beb1f5e3059a3cb44b0793c790e3f65bf78799755a8f40107cae627b57fbc03181f65b12416ba5f5fed566a95dc4b1b93a1a63550811", "wx": "1dc3d0da27139b88b61893d1bdee2e5fce3dcd8c4b65e1861ad0886068d32d905d343c4567ab20903f43beb1f5e3059a", "wy": "3cb44b0793c790e3f65bf78799755a8f40107cae627b57fbc03181f65b12416ba5f5fed566a95dc4b1b93a1a63550811"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041dc3d0da27139b88b61893d1bdee2e5fce3dcd8c4b65e1861ad0886068d32d905d343c4567ab20903f43beb1f5e3059a3cb44b0793c790e3f65bf78799755a8f40107cae627b57fbc03181f65b12416ba5f5fed566a95dc4b1b93a1a63550811", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEHcPQ2icTm4i2GJPRve4uX849zYxLZeGG\nGtCIYGjTLZBdNDxFZ6sgkD9DvrH14wWaPLRLB5PHkOP2W/eHmXVaj0AQfK5ie1f7\nwDGB9lsSQWul9f7VZqldxLG5OhpjVQgR\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0231008d1181deb9d59038bb139b3524c511fa57f11f985c9d879dd6df6133efa89045a38f50e201805df28ea43a9227177785", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0485d1e93894969ef05e85263e3751285abf14ce1fb1a947d99ab869e61249ab515224ab3b0f322be36c90a3a1522f83ab88fcdd8457e34a9e8105d361fb3711b544e4684aac178a3217505bb894e851181033d7c756d572abcea1aa7bb1e10c6e", "wx": "0085d1e93894969ef05e85263e3751285abf14ce1fb1a947d99ab869e61249ab515224ab3b0f322be36c90a3a1522f83ab", "wy": "0088fcdd8457e34a9e8105d361fb3711b544e4684aac178a3217505bb894e851181033d7c756d572abcea1aa7bb1e10c6e"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000485d1e93894969ef05e85263e3751285abf14ce1fb1a947d99ab869e61249ab515224ab3b0f322be36c90a3a1522f83ab88fcdd8457e34a9e8105d361fb3711b544e4684aac178a3217505bb894e851181033d7c756d572abcea1aa7bb1e10c6e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEhdHpOJSWnvBehSY+N1EoWr8Uzh+xqUfZ\nmrhp5hJJq1FSJKs7DzIr42yQo6FSL4OriPzdhFfjSp6BBdNh+zcRtUTkaEqsF4oy\nF1BbuJToURgQM9fHVtVyq86hqnux4Qxu\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffed2119d5fc12649fc808af3b6d9037d3a44eb32399970dd0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04609b7164d1e196a5596bef71b34a8fb4eacbbea10fd41d126c16ea3578d893e898c413805230c7fd7e33ee832be72120c2e379c857c01d95b53daf382fa5c196705c7f927ab3dcd8e6aa6bd4fe6767c56c178dcc1bbde32ea00afdc1a4f59fa6", "wx": "609b7164d1e196a5596bef71b34a8fb4eacbbea10fd41d126c16ea3578d893e898c413805230c7fd7e33ee832be72120", "wy": "00c2e379c857c01d95b53daf382fa5c196705c7f927ab3dcd8e6aa6bd4fe6767c56c178dcc1bbde32ea00afdc1a4f59fa6"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004609b7164d1e196a5596bef71b34a8fb4eacbbea10fd41d126c16ea3578d893e898c413805230c7fd7e33ee832be72120c2e379c857c01d95b53daf382fa5c196705c7f927ab3dcd8e6aa6bd4fe6767c56c178dcc1bbde32ea00afdc1a4f59fa6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEYJtxZNHhlqVZa+9xs0qPtOrLvqEP1B0S\nbBbqNXjYk+iYxBOAUjDH/X4z7oMr5yEgwuN5yFfAHZW1Pa84L6XBlnBcf5J6s9zY\n5qpr1P5nZ8VsF43MG73jLqAK/cGk9Z+m\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023079b95c013b0472de04d8faeec3b779c39fe729ea84fb554cd091c7178c2f054eabbc62c3e1cfbac2c2e69d7aa45d9072", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046f557ef52d480ea476d4e6bb8eb4c5a959eacf2ee66613dc784fff1660f246a1765e916d20ac0dbc4543303294772d12daba49f78c8a65d8946aab0a806140136516cff6725267865e9f93e4052e072ae984f3e975e7792b67b5b1807160d429", "wx": "6f557ef52d480ea476d4e6bb8eb4c5a959eacf2ee66613dc784fff1660f246a1765e916d20ac0dbc4543303294772d12", "wy": "00daba49f78c8a65d8946aab0a806140136516cff6725267865e9f93e4052e072ae984f3e975e7792b67b5b1807160d429"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046f557ef52d480ea476d4e6bb8eb4c5a959eacf2ee66613dc784fff1660f246a1765e916d20ac0dbc4543303294772d12daba49f78c8a65d8946aab0a806140136516cff6725267865e9f93e4052e072ae984f3e975e7792b67b5b1807160d429", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEb1V+9S1IDqR21Oa7jrTFqVnqzy7mZhPc\neE//FmDyRqF2XpFtIKwNvEVDMDKUdy0S2rpJ94yKZdiUaqsKgGFAE2UWz/ZyUmeG\nXp+T5AUuByrphPPpded5K2e1sYBxYNQp\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100bfd40d0caa4d9d42381f3d72a25683f52b03a1ed96fb72d03f08dcb9a8bc8f23c1a459deab03bcd39396c0d1e9053c81", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f07a94c4b1dd878c2b4507549ad7557cf70f7286b95d7b7b48a0491a635379c0032d21d3fbb289bb5b7214e2372d88ee38934125ec56253ef4b841373aea5451b6e55b7e8e999922980c0508dc4ffd5df70627c30a2026afbf99ef318e445c78", "wx": "00f07a94c4b1dd878c2b4507549ad7557cf70f7286b95d7b7b48a0491a635379c0032d21d3fbb289bb5b7214e2372d88ee", "wy": "38934125ec56253ef4b841373aea5451b6e55b7e8e999922980c0508dc4ffd5df70627c30a2026afbf99ef318e445c78"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f07a94c4b1dd878c2b4507549ad7557cf70f7286b95d7b7b48a0491a635379c0032d21d3fbb289bb5b7214e2372d88ee38934125ec56253ef4b841373aea5451b6e55b7e8e999922980c0508dc4ffd5df70627c30a2026afbf99ef318e445c78", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE8HqUxLHdh4wrRQdUmtdVfPcPcoa5XXt7\nSKBJGmNTecADLSHT+7KJu1tyFOI3LYjuOJNBJexWJT70uEE3OupUUbblW36OmZki\nmAwFCNxP/V33BifDCiAmr7+Z7zGORFx4\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02304c7d219db9af94ce7fffffffffffffffffffffffffffffffef15cf1058c8d8ba1e634c4122db95ec1facd4bb13ebf09a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0461f352564999c69ce86c0336c9a9a9baddcf4555b675183ea27f682a7b0661250ff7d2d00672880e7d3fd5329b4d19a31f28c529832d0b336633e3ef2b0bf97007a61b7e427c9d2ca1fc2910b0cc685d409ec423bf2f5211742b8d3b33d2f04a", "wx": "61f352564999c69ce86c0336c9a9a9baddcf4555b675183ea27f682a7b0661250ff7d2d00672880e7d3fd5329b4d19a3", "wy": "1f28c529832d0b336633e3ef2b0bf97007a61b7e427c9d2ca1fc2910b0cc685d409ec423bf2f5211742b8d3b33d2f04a"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000461f352564999c69ce86c0336c9a9a9baddcf4555b675183ea27f682a7b0661250ff7d2d00672880e7d3fd5329b4d19a31f28c529832d0b336633e3ef2b0bf97007a61b7e427c9d2ca1fc2910b0cc685d409ec423bf2f5211742b8d3b33d2f04a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEYfNSVkmZxpzobAM2yamput3PRVW2dRg+\non9oKnsGYSUP99LQBnKIDn0/1TKbTRmjHyjFKYMtCzNmM+PvKwv5cAemG35CfJ0s\nofwpELDMaF1AnsQjvy9SEXQrjTsz0vBK\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d219db9af94ce7ffffffffffffffffffffffffffffffffffd189bdb6d9ef7be8504ca374756ea5b8f15e44067d209b9b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d23d62e8f8c286da7a8e2aaaad9b759c6852da31639ebddf7b4e4fd1ebe26806caef21c9fdccced05cbe1332bce4bd4d899480daf03c5918b474d9dac0742ed97aa622d18b747c4446191b5639abc708c02ff97147b5092cc1395da611476001", "wx": "00d23d62e8f8c286da7a8e2aaaad9b759c6852da31639ebddf7b4e4fd1ebe26806caef21c9fdccced05cbe1332bce4bd4d", "wy": "00899480daf03c5918b474d9dac0742ed97aa622d18b747c4446191b5639abc708c02ff97147b5092cc1395da611476001"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d23d62e8f8c286da7a8e2aaaad9b759c6852da31639ebddf7b4e4fd1ebe26806caef21c9fdccced05cbe1332bce4bd4d899480daf03c5918b474d9dac0742ed97aa622d18b747c4446191b5639abc708c02ff97147b5092cc1395da611476001", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0j1i6PjChtp6jiqqrZt1nGhS2jFjnr3f\ne05P0eviaAbK7yHJ/czO0Fy+EzK85L1NiZSA2vA8WRi0dNnawHQu2XqmItGLdHxE\nRhkbVjmrxwjAL/lxR7UJLME5XaYRR2AB\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a433b735f299cfffffffffffffffffffffffffffffffffffdbb02debbfa7c9f1487f3936a22ca3f6f5d06ea22d7c0dc3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1c1d5980bb20f6622b35b87f020a53d73fe7d148178df52a91964b541311bd88e00b35834238a0bc1401f9c3ea0c3e3a50b861b701099048e0b36ec57b724b781f5c9e9d38eb345dd77eab0cb58b4fdea44e358bc6a6ae4d17476eb444bc61c", "wx": "00d1c1d5980bb20f6622b35b87f020a53d73fe7d148178df52a91964b541311bd88e00b35834238a0bc1401f9c3ea0c3e3", "wy": "00a50b861b701099048e0b36ec57b724b781f5c9e9d38eb345dd77eab0cb58b4fdea44e358bc6a6ae4d17476eb444bc61c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1c1d5980bb20f6622b35b87f020a53d73fe7d148178df52a91964b541311bd88e00b35834238a0bc1401f9c3ea0c3e3a50b861b701099048e0b36ec57b724b781f5c9e9d38eb345dd77eab0cb58b4fdea44e358bc6a6ae4d17476eb444bc61c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0cHVmAuyD2Yis1uH8CClPXP+fRSBeN9S\nqRlktUExG9iOALNYNCOKC8FAH5w+oMPjpQuGG3AQmQSOCzbsV7ckt4H1yenTjrNF\n3XfqsMtYtP3qRONYvGpq5NF0dutES8Yc\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b9af94ce7fffffffffffffffffffffffffffffffffffffffd6efeefc876c9f23217b443c80637ef939e911219f96c179", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046626339de05be6e5b2e15c47253ad621ae13fd4d5de4e4a038eb2127fe33fd5b898cd059a43ec09d186fbf24ed8c00d19251db17bc71d07b53e8d094c61b8e3049e040da95a885e4e476a445f7bfc3705f8c66a7f7d95f0697b9bf2eff9e4cc0", "wx": "6626339de05be6e5b2e15c47253ad621ae13fd4d5de4e4a038eb2127fe33fd5b898cd059a43ec09d186fbf24ed8c00d1", "wy": "009251db17bc71d07b53e8d094c61b8e3049e040da95a885e4e476a445f7bfc3705f8c66a7f7d95f0697b9bf2eff9e4cc0"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046626339de05be6e5b2e15c47253ad621ae13fd4d5de4e4a038eb2127fe33fd5b898cd059a43ec09d186fbf24ed8c00d19251db17bc71d07b53e8d094c61b8e3049e040da95a885e4e476a445f7bfc3705f8c66a7f7d95f0697b9bf2eff9e4cc0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEZiYzneBb5uWy4VxHJTrWIa4T/U1d5OSg\nOOshJ/4z/VuJjNBZpD7AnRhvvyTtjADRklHbF7xx0HtT6NCUxhuOMEngQNqVqIXk\n5HakRfe/w3BfjGan99lfBpe5vy7/nkzA\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a276276276276276276276276276276276276276276276273d7228d4f84b769be0fd57b97e4c1ebcae9a5f635e80e9df", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046288739deb45130ee9d84c5d7a74a64d4e1a829a657c8f06a178438b8657169c486fe7c2610ea1a01b90731edf8e2dd81f2d7a092ecf4a08e381473f70519befd79e3b1484076fb837a9ef8065d05f62df4753a26f72162f8be10d5bdf52a9e7", "wx": "6288739deb45130ee9d84c5d7a74a64d4e1a829a657c8f06a178438b8657169c486fe7c2610ea1a01b90731edf8e2dd8", "wy": "1f2d7a092ecf4a08e381473f70519befd79e3b1484076fb837a9ef8065d05f62df4753a26f72162f8be10d5bdf52a9e7"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046288739deb45130ee9d84c5d7a74a64d4e1a829a657c8f06a178438b8657169c486fe7c2610ea1a01b90731edf8e2dd81f2d7a092ecf4a08e381473f70519befd79e3b1484076fb837a9ef8065d05f62df4753a26f72162f8be10d5bdf52a9e7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEYohznetFEw7p2ExdenSmTU4agpplfI8G\noXhDi4ZXFpxIb+fCYQ6hoBuQcx7fji3YHy16CS7PSgjjgUc/cFGb79eeOxSEB2+4\nN6nvgGXQX2LfR1Oib3IWL4vhDVvfUqnn\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023073333333333333333333333333333333333333333333333316e4d9f42d4eca22df403a0c578b86f0a9a93fe89995c7ed", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042bdc91e87927364f316799ffabbfcda6fd15572255b08deb46090cd2ea351c911366b3c55383892cc6b8dd500a2cbaef9ffd06e925b733f3f017c92136a6cd096ad6d512866c52fecafc3b2d43a0d62ef1f8709d9bb5d29f595f6dbe3599ad3e", "wx": "2bdc91e87927364f316799ffabbfcda6fd15572255b08deb46090cd2ea351c911366b3c55383892cc6b8dd500a2cbaef", "wy": "009ffd06e925b733f3f017c92136a6cd096ad6d512866c52fecafc3b2d43a0d62ef1f8709d9bb5d29f595f6dbe3599ad3e"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042bdc91e87927364f316799ffabbfcda6fd15572255b08deb46090cd2ea351c911366b3c55383892cc6b8dd500a2cbaef9ffd06e925b733f3f017c92136a6cd096ad6d512866c52fecafc3b2d43a0d62ef1f8709d9bb5d29f595f6dbe3599ad3e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEK9yR6HknNk8xZ5n/q7/Npv0VVyJVsI3r\nRgkM0uo1HJETZrPFU4OJLMa43VAKLLrvn/0G6SW3M/PwF8khNqbNCWrW1RKGbFL+\nyvw7LUOg1i7x+HCdm7XSn1lfbb41ma0+\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffda4233abf824c93f90115e76db206fa7489d6647332e1ba3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049aaa6c4c26e55fdece622d4e1b8454a7e4be9470e2e9ecd67479f2b7bb79ac9e28ba363b206ce7af5932a154980c1612cb930ccefbd759befafdb234f72e4f58e0ce770991dac7c25bc3e4c7c0765fcf1dacbc55f4430520db7bf7da401080e1", "wx": "009aaa6c4c26e55fdece622d4e1b8454a7e4be9470e2e9ecd67479f2b7bb79ac9e28ba363b206ce7af5932a154980c1612", "wy": "00cb930ccefbd759befafdb234f72e4f58e0ce770991dac7c25bc3e4c7c0765fcf1dacbc55f4430520db7bf7da401080e1"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049aaa6c4c26e55fdece622d4e1b8454a7e4be9470e2e9ecd67479f2b7bb79ac9e28ba363b206ce7af5932a154980c1612cb930ccefbd759befafdb234f72e4f58e0ce770991dac7c25bc3e4c7c0765fcf1dacbc55f4430520db7bf7da401080e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEmqpsTCblX97OYi1OG4RUp+S+lHDi6ezW\ndHnyt7t5rJ4oujY7IGznr1kyoVSYDBYSy5MMzvvXWb76/bI09y5PWODOdwmR2sfC\nW8Pkx8B2X88drLxV9EMFINt799pAEIDh\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02303fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049004b1043628506e37308dd0107ba02d809b1504f89948161ab7a580b9e2b6c111688f9a7db9ec1e52c987cbe06f1173f20b953d46c6172a883fb614c788bf860c456b1b08db110b09447ef0176f7222be4120128f8a198f37264efe6256af93", "wx": "009004b1043628506e37308dd0107ba02d809b1504f89948161ab7a580b9e2b6c111688f9a7db9ec1e52c987cbe06f1173", "wy": "00f20b953d46c6172a883fb614c788bf860c456b1b08db110b09447ef0176f7222be4120128f8a198f37264efe6256af93"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049004b1043628506e37308dd0107ba02d809b1504f89948161ab7a580b9e2b6c111688f9a7db9ec1e52c987cbe06f1173f20b953d46c6172a883fb614c788bf860c456b1b08db110b09447ef0176f7222be4120128f8a198f37264efe6256af93", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEkASxBDYoUG43MI3QEHugLYCbFQT4mUgW\nGrelgLnitsERaI+afbnsHlLJh8vgbxFz8guVPUbGFyqIP7YUx4i/hgxFaxsI2xEL\nCUR+8BdvciK+QSASj4oZjzcmTv5iVq+T\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100dfea06865526cea11c0f9eb9512b41fa9581d0f6cb7db9680336151dce79de818cdf33c879da322740416d1e5ae532fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0423c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d11f4e0f85a87d3b08a699d6e83d0c8309e7e1646625f7caa73bed83e78b2e28d8384f2c0555bd1023701c10a2c1726a9dc", "wx": "23c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d11", "wy": "00f4e0f85a87d3b08a699d6e83d0c8309e7e1646625f7caa73bed83e78b2e28d8384f2c0555bd1023701c10a2c1726a9dc"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000423c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d11f4e0f85a87d3b08a699d6e83d0c8309e7e1646625f7caa73bed83e78b2e28d8384f2c0555bd1023701c10a2c1726a9dc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEI8VpTshVY0Pq+OcHbeDIEM4mqpb86doy\nWoE8SwRiVT1nnHCj2dYm3qwxYDc78F0R9OD4WofTsIppnW6D0Mgwnn4WRmJffKpz\nvtg+eLLijYOE8sBVW9ECNwHBCiwXJqnc\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "point duplication during verification", "msg": "313233343030", "sig": "3065023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce6002307cd374eebe35c25ce67aa38baafef7f6e470c9ec311a0bc81636f71b31b09a1c3860f70b53e285eab64133570bd7574f", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0423c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d110b1f07a5782c4f759662917c2f37cf6181e9b99da083558c4127c1874d1d727b7b0d3fa9a42efdc8fe3ef5d4e8d95623", "wx": "23c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d11", "wy": "0b1f07a5782c4f759662917c2f37cf6181e9b99da083558c4127c1874d1d727b7b0d3fa9a42efdc8fe3ef5d4e8d95623"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000423c5694ec8556343eaf8e7076de0c810ce26aa96fce9da325a813c4b0462553d679c70a3d9d626deac3160373bf05d110b1f07a5782c4f759662917c2f37cf6181e9b99da083558c4127c1874d1d727b7b0d3fa9a42efdc8fe3ef5d4e8d95623", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEI8VpTshVY0Pq+OcHbeDIEM4mqpb86doy\nWoE8SwRiVT1nnHCj2dYm3qwxYDc78F0RCx8HpXgsT3WWYpF8LzfPYYHpuZ2gg1WM\nQSfBh00dcnt7DT+ppC79yP4+9dTo2VYj\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "duplication bug", "msg": "313233343030", "sig": "3065023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce6002307cd374eebe35c25ce67aa38baafef7f6e470c9ec311a0bc81636f71b31b09a1c3860f70b53e285eab64133570bd7574f", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04916e5351bd3efecf755786fa77f6acfecf3b00cd496fbcdecd8d255120dfcf27b70e7fc9de74be9b15f72650b3eedfdd5bb6bcbdf478e15f77221d01d6086eae7dae44a16bdeb4afe178eb444600452789889310ad61014a3957436a59a3239a", "wx": "00916e5351bd3efecf755786fa77f6acfecf3b00cd496fbcdecd8d255120dfcf27b70e7fc9de74be9b15f72650b3eedfdd", "wy": "5bb6bcbdf478e15f77221d01d6086eae7dae44a16bdeb4afe178eb444600452789889310ad61014a3957436a59a3239a"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004916e5351bd3efecf755786fa77f6acfecf3b00cd496fbcdecd8d255120dfcf27b70e7fc9de74be9b15f72650b3eedfdd5bb6bcbdf478e15f77221d01d6086eae7dae44a16bdeb4afe178eb444600452789889310ad61014a3957436a59a3239a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEkW5TUb0+/s91V4b6d/as/s87AM1Jb7ze\nzY0lUSDfzye3Dn/J3nS+mxX3JlCz7t/dW7a8vfR44V93Ih0B1ghurn2uRKFr3rSv\n4XjrREYARSeJiJMQrWEBSjlXQ2pZoyOa\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3035020101023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e79f9ee594e711ae1439a237a0db174abd0b0138c4da3db1a6bc0180280b83020104580528d1030544ee4e7a17341e5c393de20f319b72e523b0b9ff9cd10cdc4a5b6b35850be57079e1afd30dbd6d4651139cfe0b16b32b074f81563009f7d9", "wx": "00e79f9ee594e711ae1439a237a0db174abd0b0138c4da3db1a6bc0180280b83020104580528d1030544ee4e7a17341e5c", "wy": "393de20f319b72e523b0b9ff9cd10cdc4a5b6b35850be57079e1afd30dbd6d4651139cfe0b16b32b074f81563009f7d9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e79f9ee594e711ae1439a237a0db174abd0b0138c4da3db1a6bc0180280b83020104580528d1030544ee4e7a17341e5c393de20f319b72e523b0b9ff9cd10cdc4a5b6b35850be57079e1afd30dbd6d4651139cfe0b16b32b074f81563009f7d9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE55+e5ZTnEa4UOaI3oNsXSr0LATjE2j2x\nprwBgCgLgwIBBFgFKNEDBUTuTnoXNB5cOT3iDzGbcuUjsLn/nNEM3EpbazWFC+Vw\neeGv0w29bUZRE5z+CxazKwdPgVYwCffZ\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3065023101000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049d91680bd5ac912ddecc5b609094a8d5fd12b5d5af7c5bbff8f129d9bcedd5dea45df2d09513ec7aead188885fd278bcd968fbaba2bd7d866f6853a6d79661fd53f252ea936573f6bc7a32426c6a379d3d8c1a6b1e1a1aa7faa7ffdf5c4b0fbd", "wx": "009d91680bd5ac912ddecc5b609094a8d5fd12b5d5af7c5bbff8f129d9bcedd5dea45df2d09513ec7aead188885fd278bc", "wy": "00d968fbaba2bd7d866f6853a6d79661fd53f252ea936573f6bc7a32426c6a379d3d8c1a6b1e1a1aa7faa7ffdf5c4b0fbd"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049d91680bd5ac912ddecc5b609094a8d5fd12b5d5af7c5bbff8f129d9bcedd5dea45df2d09513ec7aead188885fd278bcd968fbaba2bd7d866f6853a6d79661fd53f252ea936573f6bc7a32426c6a379d3d8c1a6b1e1a1aa7faa7ffdf5c4b0fbd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEnZFoC9WskS3ezFtgkJSo1f0StdWvfFu/\n+PEp2bzt1d6kXfLQlRPseurRiIhf0ni82Wj7q6K9fYZvaFOm15Zh/VPyUuqTZXP2\nvHoyQmxqN509jBprHhoap/qn/99cSw+9\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0466c48ea217602f3e0e77f402dfd386450c3a33f3b9a266d01cfa4d8cb9d58f19e7cc56315a5717ae27f931a8b6401aed0f47cc979e0edb9b7970ac66bc66315d3d38594dc933dfb963ccd5676efb57b14be806c0879b3cd28fe6ddeaaaf4ad92", "wx": "66c48ea217602f3e0e77f402dfd386450c3a33f3b9a266d01cfa4d8cb9d58f19e7cc56315a5717ae27f931a8b6401aed", "wy": "0f47cc979e0edb9b7970ac66bc66315d3d38594dc933dfb963ccd5676efb57b14be806c0879b3cd28fe6ddeaaaf4ad92"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000466c48ea217602f3e0e77f402dfd386450c3a33f3b9a266d01cfa4d8cb9d58f19e7cc56315a5717ae27f931a8b6401aed0f47cc979e0edb9b7970ac66bc66315d3d38594dc933dfb963ccd5676efb57b14be806c0879b3cd28fe6ddeaaaf4ad92", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEZsSOohdgLz4Od/QC39OGRQw6M/O5ombQ\nHPpNjLnVjxnnzFYxWlcXrif5Mai2QBrtD0fMl54O25t5cKxmvGYxXT04WU3JM9+5\nY8zVZ277V7FL6AbAh5s80o/m3eqq9K2S\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a03d026431e0f75a9ce6cd459eb268c44d59a00bb6facd5b816a2823845e7f65c48c69cfb4841bc0ab8c981e6c491db2488eb2d9321b30ebf3f1f99da618d3311b01928ae9b23764b530e2ad41dd121b6812b7a8a80f669934dd8efb0445a962", "wx": "00a03d026431e0f75a9ce6cd459eb268c44d59a00bb6facd5b816a2823845e7f65c48c69cfb4841bc0ab8c981e6c491db2", "wy": "488eb2d9321b30ebf3f1f99da618d3311b01928ae9b23764b530e2ad41dd121b6812b7a8a80f669934dd8efb0445a962"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a03d026431e0f75a9ce6cd459eb268c44d59a00bb6facd5b816a2823845e7f65c48c69cfb4841bc0ab8c981e6c491db2488eb2d9321b30ebf3f1f99da618d3311b01928ae9b23764b530e2ad41dd121b6812b7a8a80f669934dd8efb0445a962", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEoD0CZDHg91qc5s1FnrJoxE1ZoAu2+s1b\ngWooI4Ref2XEjGnPtIQbwKuMmB5sSR2ySI6y2TIbMOvz8fmdphjTMRsBkorpsjdk\ntTDirUHdEhtoEreoqA9mmTTdjvsERali\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04db12e7908092c195819ea7652a2f923f678f00aa8181f3c2cb0021e268a176737d48a48ea25a48ea2b0cce3c31f1406c9c46a9b415ca03d1b309c5f4735b6ce48da4d32a0eab51772dc6bb7e63d835ea7612c92a629c058af638a5bb5354110e", "wx": "00db12e7908092c195819ea7652a2f923f678f00aa8181f3c2cb0021e268a176737d48a48ea25a48ea2b0cce3c31f1406c", "wy": "009c46a9b415ca03d1b309c5f4735b6ce48da4d32a0eab51772dc6bb7e63d835ea7612c92a629c058af638a5bb5354110e"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004db12e7908092c195819ea7652a2f923f678f00aa8181f3c2cb0021e268a176737d48a48ea25a48ea2b0cce3c31f1406c9c46a9b415ca03d1b309c5f4735b6ce48da4d32a0eab51772dc6bb7e63d835ea7612c92a629c058af638a5bb5354110e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE2xLnkICSwZWBnqdlKi+SP2ePAKqBgfPC\nywAh4mihdnN9SKSOolpI6isMzjwx8UBsnEaptBXKA9GzCcX0c1ts5I2k0yoOq1F3\nLca7fmPYNep2EskqYpwFivY4pbtTVBEO\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04532b95507ca92950613dcffe7740715af07953e881d133b75989426f9aea6ed1bd22a9eb899441b29882a8e4f53f1db265dda7154f92c561b2b6c9f154af3a589871f5290114a457896fd1e9af235de9f1eb7cfe0911e27cecaa30f90bec73b4", "wx": "532b95507ca92950613dcffe7740715af07953e881d133b75989426f9aea6ed1bd22a9eb899441b29882a8e4f53f1db2", "wy": "65dda7154f92c561b2b6c9f154af3a589871f5290114a457896fd1e9af235de9f1eb7cfe0911e27cecaa30f90bec73b4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004532b95507ca92950613dcffe7740715af07953e881d133b75989426f9aea6ed1bd22a9eb899441b29882a8e4f53f1db265dda7154f92c561b2b6c9f154af3a589871f5290114a457896fd1e9af235de9f1eb7cfe0911e27cecaa30f90bec73b4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEUyuVUHypKVBhPc/+d0BxWvB5U+iB0TO3\nWYlCb5rqbtG9IqnriZRBspiCqOT1Px2yZd2nFU+SxWGytsnxVK86WJhx9SkBFKRX\niW/R6a8jXenx63z+CRHifOyqMPkL7HO0\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041dd1d7b6b2f677d7e10fa14bb35a74bcf83d6ea0bb308ffeb7d73634f6911e4213752173fa76b2c5be12d752b8176659888325cc90b23ae34fac03a5b9a30cbcb9d24e02923d6d68e8e54066eabbf8a87272827fb2f26392dc45664bb2399e90", "wx": "1dd1d7b6b2f677d7e10fa14bb35a74bcf83d6ea0bb308ffeb7d73634f6911e4213752173fa76b2c5be12d752b8176659", "wy": "00888325cc90b23ae34fac03a5b9a30cbcb9d24e02923d6d68e8e54066eabbf8a87272827fb2f26392dc45664bb2399e90"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041dd1d7b6b2f677d7e10fa14bb35a74bcf83d6ea0bb308ffeb7d73634f6911e4213752173fa76b2c5be12d752b8176659888325cc90b23ae34fac03a5b9a30cbcb9d24e02923d6d68e8e54066eabbf8a87272827fb2f26392dc45664bb2399e90", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEHdHXtrL2d9fhD6FLs1p0vPg9bqC7MI/+\nt9c2NPaRHkITdSFz+nayxb4S11K4F2ZZiIMlzJCyOuNPrAOluaMMvLnSTgKSPW1o\n6OVAZuq7+KhycoJ/svJjktxFZkuyOZ6Q\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04edc6ddb4a76167f8f7db96dbbbd87b241a2477e60ef21f22d0fb235fdd987adb15a13a9c9f05228ec7e33e39b56baf178397074f1f3b7e1d97a35d135760ff5175da027f521ee1d705b2f03e083536acfef9a9c57efe7655095631c611700542", "wx": "00edc6ddb4a76167f8f7db96dbbbd87b241a2477e60ef21f22d0fb235fdd987adb15a13a9c9f05228ec7e33e39b56baf17", "wy": "008397074f1f3b7e1d97a35d135760ff5175da027f521ee1d705b2f03e083536acfef9a9c57efe7655095631c611700542"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004edc6ddb4a76167f8f7db96dbbbd87b241a2477e60ef21f22d0fb235fdd987adb15a13a9c9f05228ec7e33e39b56baf178397074f1f3b7e1d97a35d135760ff5175da027f521ee1d705b2f03e083536acfef9a9c57efe7655095631c611700542", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE7cbdtKdhZ/j325bbu9h7JBokd+YO8h8i\n0PsjX92YetsVoTqcnwUijsfjPjm1a68Xg5cHTx87fh2Xo10TV2D/UXXaAn9SHuHX\nBbLwPgg1Nqz++anFfv52VQlWMcYRcAVC\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "extreme value for k", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04febf3b365df31548a5295cda6d7cff00f8ce15b4aa7dc8affe9c573decea9f7b75b64234e2d5da599bf2d1e416a7500769205229d1898c7db1d53a6bd11079458cc40da83c16f070e5772b1d2059fef19f0f36d4471ad85ec86cf1cd4e7d90c4", "wx": "00febf3b365df31548a5295cda6d7cff00f8ce15b4aa7dc8affe9c573decea9f7b75b64234e2d5da599bf2d1e416a75007", "wy": "69205229d1898c7db1d53a6bd11079458cc40da83c16f070e5772b1d2059fef19f0f36d4471ad85ec86cf1cd4e7d90c4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004febf3b365df31548a5295cda6d7cff00f8ce15b4aa7dc8affe9c573decea9f7b75b64234e2d5da599bf2d1e416a7500769205229d1898c7db1d53a6bd11079458cc40da83c16f070e5772b1d2059fef19f0f36d4471ad85ec86cf1cd4e7d90c4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE/r87Nl3zFUilKVzabXz/APjOFbSqfciv\n/pxXPezqn3t1tkI04tXaWZvy0eQWp1AHaSBSKdGJjH2x1Tpr0RB5RYzEDag8FvBw\n5XcrHSBZ/vGfDzbURxrYXshs8c1OfZDE\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048373e65ac625a5a4110e350e7f08a0392f8261581c06a88b125a145681687fc5a6c796f16ca48977bbfc7729bba8006301d966a2d30fdf2b6dbcc8c9ac3b6b2150431f95fdf49e8ea5fff99f185cbcd2f9631ee3f074d680700fe693b0398583", "wx": "008373e65ac625a5a4110e350e7f08a0392f8261581c06a88b125a145681687fc5a6c796f16ca48977bbfc7729bba80063", "wy": "01d966a2d30fdf2b6dbcc8c9ac3b6b2150431f95fdf49e8ea5fff99f185cbcd2f9631ee3f074d680700fe693b0398583"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048373e65ac625a5a4110e350e7f08a0392f8261581c06a88b125a145681687fc5a6c796f16ca48977bbfc7729bba8006301d966a2d30fdf2b6dbcc8c9ac3b6b2150431f95fdf49e8ea5fff99f185cbcd2f9631ee3f074d680700fe693b0398583", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEg3PmWsYlpaQRDjUOfwigOS+CYVgcBqiL\nEloUVoFof8Wmx5bxbKSJd7v8dym7qABjAdlmotMP3yttvMjJrDtrIVBDH5X99J6O\npf/5nxhcvNL5Yx7j8HTWgHAP5pOwOYWD\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d8b5b751bef246a3769682966232b714b05d99a37199223e55cbc4df6941b2529e57965c94f60d88837cfd952d151abf9eb51727dc4665f8e74e8f5c79d34ffd11c9eab8b5b773950d1f2c446d84c158aef8bbf93b986d9b374f722d94f59f1b", "wx": "00d8b5b751bef246a3769682966232b714b05d99a37199223e55cbc4df6941b2529e57965c94f60d88837cfd952d151abf", "wy": "009eb51727dc4665f8e74e8f5c79d34ffd11c9eab8b5b773950d1f2c446d84c158aef8bbf93b986d9b374f722d94f59f1b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d8b5b751bef246a3769682966232b714b05d99a37199223e55cbc4df6941b2529e57965c94f60d88837cfd952d151abf9eb51727dc4665f8e74e8f5c79d34ffd11c9eab8b5b773950d1f2c446d84c158aef8bbf93b986d9b374f722d94f59f1b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE2LW3Ub7yRqN2loKWYjK3FLBdmaNxmSI+\nVcvE32lBslKeV5ZclPYNiIN8/ZUtFRq/nrUXJ9xGZfjnTo9cedNP/RHJ6ri1t3OV\nDR8sRG2EwViu+Lv5O5htmzdPci2U9Z8b\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045f2098bc0eda6a7748fb7d95d5838a66d3f33ae4138767a7d3e221269d5b359b6456043b7a0973cf635e7424aaf1907db1e767233b18988d95e00bbb2dafbb69f92dcc01e5cb8da0c262cb52924af7976d9ded1d5fe60394035cc5509f45865c", "wx": "5f2098bc0eda6a7748fb7d95d5838a66d3f33ae4138767a7d3e221269d5b359b6456043b7a0973cf635e7424aaf1907d", "wy": "00b1e767233b18988d95e00bbb2dafbb69f92dcc01e5cb8da0c262cb52924af7976d9ded1d5fe60394035cc5509f45865c"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045f2098bc0eda6a7748fb7d95d5838a66d3f33ae4138767a7d3e221269d5b359b6456043b7a0973cf635e7424aaf1907db1e767233b18988d95e00bbb2dafbb69f92dcc01e5cb8da0c262cb52924af7976d9ded1d5fe60394035cc5509f45865c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEXyCYvA7aandI+32V1YOKZtPzOuQTh2en\n0+IhJp1bNZtkVgQ7eglzz2NedCSq8ZB9sednIzsYmI2V4Au7La+7afktzAHly42g\nwmLLUpJK95dtne0dX+YDlANcxVCfRYZc\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04018cb64da6154801677d34be71e75883f912274036029bb3cf2d5679bca22c9ff10d717e4d9c370d058ddd3f6d38beb25bc92d39b9be3fce5ebc38956044af21220aac3150bd899256e30344cf7caa6820666005ed965d8dc3e678412f39adda", "wx": "018cb64da6154801677d34be71e75883f912274036029bb3cf2d5679bca22c9ff10d717e4d9c370d058ddd3f6d38beb2", "wy": "5bc92d39b9be3fce5ebc38956044af21220aac3150bd899256e30344cf7caa6820666005ed965d8dc3e678412f39adda"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004018cb64da6154801677d34be71e75883f912274036029bb3cf2d5679bca22c9ff10d717e4d9c370d058ddd3f6d38beb25bc92d39b9be3fce5ebc38956044af21220aac3150bd899256e30344cf7caa6820666005ed965d8dc3e678412f39adda", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAYy2TaYVSAFnfTS+cedYg/kSJ0A2Apuz\nzy1WebyiLJ/xDXF+TZw3DQWN3T9tOL6yW8ktObm+P85evDiVYESvISIKrDFQvYmS\nVuMDRM98qmggZmAF7ZZdjcPmeEEvOa3a\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aedfc5ce97b01b6201936777b3d01fe19ecee98bfade49ec5936accac3b02ee90bd5af667a233c60c14dac619f110a7ad9b99c30856ef47a57800ea6935e63c0c2dd7ac01dd5c0224231c68ff4b7918ef23f26195467e1d6e1a2767d73817f69", "wx": "00aedfc5ce97b01b6201936777b3d01fe19ecee98bfade49ec5936accac3b02ee90bd5af667a233c60c14dac619f110a7a", "wy": "00d9b99c30856ef47a57800ea6935e63c0c2dd7ac01dd5c0224231c68ff4b7918ef23f26195467e1d6e1a2767d73817f69"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aedfc5ce97b01b6201936777b3d01fe19ecee98bfade49ec5936accac3b02ee90bd5af667a233c60c14dac619f110a7ad9b99c30856ef47a57800ea6935e63c0c2dd7ac01dd5c0224231c68ff4b7918ef23f26195467e1d6e1a2767d73817f69", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErt/FzpewG2IBk2d3s9Af4Z7O6Yv63kns\nWTasysOwLukL1a9meiM8YMFNrGGfEQp62bmcMIVu9HpXgA6mk15jwMLdesAd1cAi\nQjHGj/S3kY7yPyYZVGfh1uGidn1zgX9p\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "extreme value for k", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3NhfeSpYmLG9dnpi/kpLcKfj0Hb0omhR8\n6doxE7XwuMAKYLHOHX6BnXpDHXyQ6g5f\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100f8723083bde48fae6e2f3ba5d836c2e954aec113030836fb978c08ab1b5a3dfe54aa2fab2423747e3b4fa70ec744894c02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 394, "comment": "testing point duplication", "msg": "313233343030", "sig": "30640230078dcf7c421b705191d0c45a27c93d16ab513eecfcf7c9042fd744d6d8dcefe1036fde07248d32fcb19c725c0580a02702302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "00c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3yeghtWnZ05CiYWdAbW0j1gcL4kLXZeuD\nFiXO7EoPRz71n04w4oF+YoW84oRvFfGg\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100f8723083bde48fae6e2f3ba5d836c2e954aec113030836fb978c08ab1b5a3dfe54aa2fab2423747e3b4fa70ec744894c02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 396, "comment": "testing point duplication", "msg": "313233343030", "sig": "30640230078dcf7c421b705191d0c45a27c93d16ab513eecfcf7c9042fd744d6d8dcefe1036fde07248d32fcb19c725c0580a02702302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "wx": "29bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc", "wy": "009a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKb23bV+nQb/XAjPLOmbMfUS+s7BmPZKo\nE2ZQR4vO+2HvGC4VWlQ0Wl6OXojwZOW8mlJat/dk2tPa4UaMK0GfO2K5upF9XoxP\nsexHQEo/x2R0snEwgb6dtMAOBDran8Sj\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "pseudorandom signature", "msg": "", "sig": "30660231009da5c054a9eddabf4753559edd5a862cdf57adc0c2717a6949a43d80cfccd02b14ec06113ccf08081be43552391cfb1602310088bb307e9a04f923c70013db3ca716d21b313dde0cd6849435bf3b192d5266589a00b34e9c4c626b1055e7a38ef10853", "result": "valid", "flags": []}, {"tcId": 398, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3065023022b0ee0e8ce866c48a4400dd8522dd91bd7a13cc8a55f2814123564d039b1d1e3a7df010688dab94878f88a1e34a905e023100f7668925262da6aad96712f817a9397b79f0fb893aedcd7221f454a60a18abb3b165aae979f29d22cfab18fb61945f87", "result": "valid", "flags": []}, {"tcId": 399, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "306402303db6c4d7d482fdb0a13470845f5ad2438198776c2a5954b233e24230889f3023ff64e4cbc793c4e3e94318b4e65f8cdb023003c22aa010ea7247ae7cc6c7d0f6af76f76ef91ce33a028de49979bdc2cc17d7df4c19c0e4c61c49275bc408697e7846", "result": "valid", "flags": []}, {"tcId": 400, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "306502307a36e2c2ebf9bc0165ff75f5906a4806c2a668cb48477f7f105169c9b5a756abcc06b05b4d5ac42ecfd12cdd0f8fc65e02310096aff9db7873cd2f6aa85c2693e1129b7896340287762854062df8104162a4572bdcbaf673af28a92314ec597f7acfe3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "wx": "00ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aac", "wy": "00acbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE/////6pj8aI5rHAZfG6/zqV1bcASEj+C\nxR+odNZgKL4A6XahCAYGc3zHXEC9/kqsrL2FOJCIpipjmDhMIrUtSS8j9G5KJ6Ry\nStVVUdpcSDQ4CVokfLDDN48fUsNCX/nx\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306502310082b41176571a051a82c18e1ffbf4f3ef7146e0634755ba30fc965efec684d12830ed366acf4759fcce146e867b9108ea023052eaa43df5a95a92aee5f0002f4b4a1c870cdec040c966280be579a15e865bebc1269b084e17e727bae14b8ad6e6c73d", "result": "valid", "flags": []}, {"tcId": 402, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3066023100b4c1897895e4c214af7ac546230ab6ea733a6353fa11bd5d9892dc89e113dffb50a3581e58d5cac31efee0d56601bc84023100b1494f4cc17f4baa96aa2c3da9db004f64256c1f28aefd299085e29fe5399517a35ae8e049ec436e7fe1b2743f2a90a0", "result": "valid", "flags": []}, {"tcId": 403, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3066023100c9b58945eed8b9949bd3e78c8920e0210289c1029cdb22df780b66aee80dca40e0e9142fc6db2269adbc4cb89a425f09023100d672273cc979c16b3336428a60a3627bf752f9d7f1ba03c5e155cec8fcf523376feab08fe0e768f174828adcd17da0b2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "wx": "00d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422", "wy": "00c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0YJ/xvbxLyGZLFpAmgZTsSHS7wKysKsB\nqRYc6VYoB0Cx41ayVXAbCm3cnsLKipQixu1dLO2NirdWD6W7iMc450VBiD2KKxwO\nK6fjbQMPxNm/uLIvJNuJfrrEndQAAAAA\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30660231009ad0ec81fe78e7433ccfe8d429ffd8cc3792a7ed239104ade9b7c828332a5be57493346c9a4e944eec914acac1ab5a45023100cab9be172e51ff52c70176648c6c6285630594330d8ffa5d28a47a1b8e58ec5c32c70769ed28bc553330c9a7e674da8a", "result": "valid", "flags": []}, {"tcId": 405, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306502310084ba925242eaedb53cc529e4763d8995aa7315e68a47ef89f291dd29ef138e4810bc1c58a6bcbada3ac83541dc139c7902304579278b73adadb63599028b873bf5f7cee2ff01eaf0faf2d529b01211a63e78433011da37fab174607fe90a4c3d81bf", "result": "valid", "flags": []}, {"tcId": 406, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3064023056a69cb5b4026268e11631f7fc35830e8a612ed79278f280e7d7e409558c43ef226ab25cf639aae7f435545cc4d8e8e502305066494754680d61c23419273ba030df0f0b8b0a486cb0dd498298a34db478a6c133b4f5e071b6696cdbec63a74d84c2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "wx": "1099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000", "wy": "00e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEJm7RRAPVfWoXMo94rO9XiUPT2+tZjGj\nFWwuUqM9fWFd0nn3n4tLr/fHE6wAAAAA5sm3NqiSny7Xvgx1OlTLtIuEaeBBHq+T\npKgkWboLaBu6j1+zg7SQbUkBozA+LxVX\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402306328e30a8e218904631d6e8858e1e3841a2f6c0959af1b53ad3515bee16cbb600b5abaa5123c8eeb8cdc9b2da1a8ef39023040e708de5a00178926cdb263afcb12710ae8c03b298eeadbc40522c0479a94e98dfbdce493fcf0cf7f4afb6949d9f95d", "result": "valid", "flags": []}, {"tcId": 408, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3064023034b9ce48ad0aac78ff138881f3b13badae7e1cf5da7ff060c5642b22c5ec4c76fd4cd46d564676d4631bd567a7ea9284023061dae7993b4500005f45f55924c502f8803455e21a62499db2cbbc80a582c1107c8014afb4619f5d4d37fddbdf2d7bb9", "result": "valid", "flags": []}, {"tcId": 409, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3066023100e337217405a8457b0e31ae4e909eabe79343331c4dd0623c2b13d0981012e28d1fbf88f0101c1abae8cace1c801dfe16023100948603710e13fe5b87e96ca87fb17bddb5762b9e4f2fc6e1c4acf4ee20b641518158b32bbd42884bffad25e0171a3462", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "wx": "2b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69", "wy": "00d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAAAAACsInt11QWkBAUXyY/M0/BZ8wZ2u\ngiWXCuGcyMt+xzWT1qRlw3D1R4sOU51p0ZUdWXtWpnNFrLJYCVgfB80Ot42VOKP4\npl8wDmih63hQffdt5lDo+O5jpfDFaHyY\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3066023100b2f22aeb025c40f695850ca8d9243d671557ecdb28ba78ad2f3389e78fe685251a29dfbc2ebc1d7e5e1098b4b286db18023100d2ac24a65d1463405bd4bb117e4d1ed7f7d9b457d51dcb1fd8704ad27de5cbc11bea45f8e3cd1ecdb51981962feaa4b6", "result": "valid", "flags": []}, {"tcId": 411, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3066023100f3b374deaa912309be3a08722fcd0fa17fbad8a0d674a96b1140efe2f9451e373029546b84a565dd88b6816b03c69912023100f44fcc8e2513a2574e9c88de1960e8d7f6c607fb0aa6400362ccacf86e56cc44bfa6e233a993800fe1385e747312393b", "result": "valid", "flags": []}, {"tcId": 412, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3065023100de778636b0c8775a48e8f7c2da3ce056ea18c0f7b61a6ceebccdc1db0462a739a9f623b342d82b5cdba9329fd32d487002305f843dc49e8c8642d0ade1fbd635ee1ea6f6da8f980ec1d839de2b37ba7082668179cb80e7c97775e77c7afe8dfb9791", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2AAAAACCLP1rTs5N6zJ1gbMXs7KtKcB91\n7UKVfqTXhY0z9cJsauIKnMzaVplnANa0\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30650231008f6f35102ebc10571603d65d14d45e2658e36a961d790348df0ed3ee615d55919e1c31d02e48b4c29b724e75094e88e102301674424d64d3a780b031e928ee3b246a3703868aef1afcc6b50dd217ae6bdcb5fc7f59d2b14dc4dd08f22853abef621b", "result": "valid", "flags": []}, {"tcId": 414, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306602310081fdae0b7e18cca48e0bae1a4e2c96f3973b0f661ccae269c1f0535265954e76473f51710fd2eca0b014e0386bdb387e023100b4fd60411ae7ad836c8b1768bf44fe126d753781628a2b34f21fe1fbc961d21a153d3838e0200ddf8b7c16819230c0e2", "result": "valid", "flags": []}, {"tcId": 415, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3065023100f6b94cdc2083d5c6b4908063033dbe1817f5187a80fbf21e0155ebc16c3b14b06282171a63d8c6ad173bad8aa40b84060230569db82936c0d284c752149034a28e2415b57247c723077d8a5a7c9725ebca7603de5b7a41c53fed2bed8143a9bb8beb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "00ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2/////990wKUsTGyFM2KfkzoTE1S1j+CK\nEr1qgVsoenHMCj2SlR31YzMlqWeY/ylL\n-----END PUBLIC KEY-----", "sha": "SHA3-384", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306602310089d3d1a5c2ce6b637cc9e30a734ea63d34a7a72630400ee82916b79fa9a9a83b4e2faf765ddcf1fa596a4c026293ea060231009013c5c51bde3c114ae0ce19141c6c72bbf0a8f75885257f202240af212064f0fa9b1409d8c5e195a8db9d996eb1cd67", "result": "valid", "flags": []}, {"tcId": 417, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306502304bb0ddb7af2d58e75b17f7ea81c618ca191efaa374026901fc1914b97b44ed64873404b40c249ee652e9685c67347881023100af0bc80678b411ce0ea78c57f50bbb9b11678e001d92f2f49ad17af4759c7a013d27668ed17b13bc01e13eb9ee68040f", "result": "valid", "flags": []}, {"tcId": 418, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30650230024deac92bccdf77a3fe019fb5d35063c9ad9374bf1e7508218b25776815eb95f51c8c253f88991c3073c67ca8bbd5770231008da6b6f9fde42f24536413f8c2d3506171c742b6a0883de116b314d559388b41630aa24c485e090fee5f340c79486164", "result": "valid", "flags": []}]}]}