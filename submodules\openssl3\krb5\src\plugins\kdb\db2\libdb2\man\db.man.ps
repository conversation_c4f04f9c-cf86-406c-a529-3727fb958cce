%!PS-Adobe-3.0
%%Creator: groff version 1.08
%%DocumentNeededResources: font Times-Roman
%%+ font Times-Bold
%%+ font Times-Italic
%%DocumentSuppliedResources: procset grops 1.08 0
%%Pages: 28
%%PageOrder: Ascend
%%Orientation: Portrait
%%EndComments
%%BeginProlog
%%BeginResource: procset grops 1.08 0
/setpacking where{
pop
currentpacking
true setpacking
}if
/grops 120 dict dup begin
/SC 32 def
/A/show load def
/B{0 SC 3 -1 roll widthshow}bind def
/C{0 exch ashow}bind def
/D{0 exch 0 SC 5 2 roll awidthshow}bind def
/E{0 rmoveto show}bind def
/F{0 rmoveto 0 SC 3 -1 roll widthshow}bind def
/G{0 rmoveto 0 exch ashow}bind def
/H{0 rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/I{0 exch rmoveto show}bind def
/J{0 exch rmoveto 0 SC 3 -1 roll widthshow}bind def
/K{0 exch rmoveto 0 exch ashow}bind def
/L{0 exch rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/M{rmoveto show}bind def
/N{rmoveto 0 SC 3 -1 roll widthshow}bind def
/O{rmoveto 0 exch ashow}bind def
/P{rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/Q{moveto show}bind def
/R{moveto 0 SC 3 -1 roll widthshow}bind def
/S{moveto 0 exch ashow}bind def
/T{moveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/SF{
findfont exch
[exch dup 0 exch 0 exch neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/MF{
findfont
[5 2 roll
0 3 1 roll 
neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/level0 0 def
/RES 0 def
/PL 0 def
/LS 0 def
/PLG{
gsave newpath clippath pathbbox grestore
exch pop add exch pop
}bind def
/BP{
/level0 save def
1 setlinecap
1 setlinejoin
72 RES div dup scale
LS{
90 rotate
}{
0 PL translate
}ifelse
1 -1 scale
}bind def
/EP{
level0 restore
showpage
}bind def
/DA{
newpath arcn stroke
}bind def
/SN{
transform
.25 sub exch .25 sub exch
round .25 add exch round .25 add exch
itransform
}bind def
/DL{
SN
moveto
SN
lineto stroke
}bind def
/DC{
newpath 0 360 arc closepath
}bind def
/TM matrix def
/DE{
TM currentmatrix pop
translate scale newpath 0 0 .5 0 360 arc closepath
TM setmatrix
}bind def
/RC/rcurveto load def
/RL/rlineto load def
/ST/stroke load def
/MT/moveto load def
/CL/closepath load def
/FL{
currentgray exch setgray fill setgray
}bind def
/BL/fill load def
/LW/setlinewidth load def
/RE{
findfont
dup maxlength 1 index/FontName known not{1 add}if dict begin
{
1 index/FID ne{def}{pop pop}ifelse
}forall
/Encoding exch def
dup/FontName exch def
currentdict end definefont pop
}bind def
/DEFS 0 def
/EBEGIN{
moveto
DEFS begin
}bind def
/EEND/end load def
/CNT 0 def
/level1 0 def
/PBEGIN{
/level1 save def
translate
div 3 1 roll div exch scale
neg exch neg exch translate
0 setgray
0 setlinecap
1 setlinewidth
0 setlinejoin
10 setmiterlimit
[]0 setdash
/setstrokeadjust where{
pop
false setstrokeadjust
}if
/setoverprint where{
pop
false setoverprint
}if
newpath
/CNT countdictstack def
userdict begin
/showpage{}def
}bind def
/PEND{
clear
countdictstack CNT sub{end}repeat
level1 restore
}bind def
end def
/setpacking where{
pop
setpacking
}if
%%EndResource
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-Italic
grops begin/DEFS 1 dict def DEFS begin/u{.001 mul}bind def end/RES 72 def/PL
792 def/LS false def/ENC0[/asciicircum/asciitilde/Scaron/Zcaron/scaron/zcaron
/Ydieresis/trademark/quotesingle/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/space
/exclam/quotedbl/numbersign/dollar/percent/ampersand/quoteright/parenleft
/parenright/asterisk/plus/comma/hyphen/period/slash/zero/one/two/three/four
/five/six/seven/eight/nine/colon/semicolon/less/equal/greater/question/at/A/B/C
/D/E/F/G/H/I/J/K/L/M/N/O/P/Q/R/S/T/U/V/W/X/Y/Z/bracketleft/backslash
/bracketright/circumflex/underscore/quoteleft/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q
/r/s/t/u/v/w/x/y/z/braceleft/bar/braceright/tilde/.notdef/quotesinglbase
/guillemotleft/guillemotright/bullet/florin/fraction/perthousand/dagger
/daggerdbl/endash/emdash/ff/fi/fl/ffi/ffl/dotlessi/dotlessj/grave/hungarumlaut
/dotaccent/breve/caron/ring/ogonek/quotedblleft/quotedblright/oe/lslash
/quotedblbase/OE/Lslash/.notdef/exclamdown/cent/sterling/currency/yen/brokenbar
/section/dieresis/copyright/ordfeminine/guilsinglleft/logicalnot/minus
/registered/macron/degree/plusminus/twosuperior/threesuperior/acute/mu
/paragraph/periodcentered/cedilla/onesuperior/ordmasculine/guilsinglright
/onequarter/onehalf/threequarters/questiondown/Agrave/Aacute/Acircumflex/Atilde
/Adieresis/Aring/AE/Ccedilla/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute
/Icircumflex/Idieresis/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis
/multiply/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla/egrave/eacute
/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis/eth/ntilde/ograve
/oacute/ocircumflex/otilde/odieresis/divide/oslash/ugrave/uacute/ucircumflex
/udieresis/yacute/thorn/ydieresis]def/Times-Italic@0 ENC0/Times-Italic RE
/Times-Bold@0 ENC0/Times-Bold RE/Times-Roman@0 ENC0/Times-Roman RE
%%EndProlog
%%Page: 1 1
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 113.45(DB_BTREE\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 113.45(anual DB_BTREE\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)
72 84 S(ME).18 E F0(db_btree \255 btree database access method)108 96 Q F1
(DESCRIPTION)72 112.8 Q F0 .486(The DB library is a f)108 124.8 R .485
(amily of groups of functions that pro)-.1 F .485
(vides a modular programming interf)-.15 F .485(ace to trans-)-.1 F .822
(actions and record-oriented \214le access.)108 136.8 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 148.8 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 148.8 S .258(ell as v)237.082 148.8 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 148.8 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 148.8 S .258
(he functional groups \(e.g.)436.746 148.8 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 160.8 R .306(tional groups are e)108 172.8 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 172.8 Q .245(description of transactions, see)108 184.8 R/F2 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F2(db_open)2.745 E
F0(\(3\)).24 E .308(and then the indi)108 196.8 R .308
(vidual access method manual pages:)-.25 F F2(db_btr)2.807 E(ee)-.37 E F0
(\(3\),).18 E F2(db_hash)2.807 E F0(\(3\),).28 E F2(db_lo)2.807 E(g)-.1 E F0
.307(\(3\) and).22 F F2(db_r)2.807 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
208.8 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
208.8 R 3.635(,s)-.4 G(ee)307.32 208.8 Q F2(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 220.8 Q 2.5(,s)-.4 G(ee)171.2 220.8 Q F2(db_mpool)2.5 E F0
(\(3\).).51 E
(This manual page describes speci\214c details of the btree access method.)108
237.6 Q 1.518(The btree data structure is a sorted, balanced tree structure st\
oring associated k)108 254.4 R -.15(ey)-.1 G 1.517(/data pairs.).15 F
(Searches,)6.517 E .598(insertions, and deletions in the btree will all comple\
te in O lg base N where base is the a)108 266.4 R -.15(ve)-.2 G .598
(rage \214ll f).15 F(actor)-.1 E(.)-.55 E .306
(Often, inserting ordered data into btrees results in a lo)108 278.4 R 2.806
<778c>-.25 G .305(ll f)341.61 278.4 R(actor)-.1 E 5.305(.T)-.55 G .305
(his implementation has been modi\214ed)386.56 278.4 R(to mak)108 290.4 Q 2.5
(eo)-.1 G(rdered insertion the best case, resulting in a much better than norm\
al page \214ll f)147.34 290.4 Q(actor)-.1 E(.)-.55 E F1 -.495(AC)72 307.2 S
(CESS METHOD SPECIFIC INFORMA).495 E(TION)-.855 E F0 .175
(The btree access method speci\214c data structure pro)108 319.2 R .176
(vided to)-.15 F F2(db_open)2.676 E F0 .176(is typedef)2.676 F 1.176 -.5('d a)
.55 H .176(nd named BTREEINFO.).5 F 2.638(AB)108 331.2 S .138
(TREEINFO structure has at least the follo)124.528 331.2 R .137
(wing \214elds, which may be initialized before calling)-.25 F F2(db_open)2.637
E F0(:).24 E(u_int cachesize;)108 348 Q 3.743(As)133 360 S 1.243
(uggested maximum size \(in bytes\) of the memory cache.)147.853 360 R 1.243
(This v)6.243 F 1.243(alue is)-.25 F/F3 10/Times-Bold@0 SF(only)3.743 E F0
(advisory)3.743 E 3.744(,a)-.65 G 1.244(nd the)514.036 360 R .017
(access method will allocate more memory rather than f)133 372 R 2.517
(ail. Since)-.1 F -2.15 -.25(ev e)2.517 H .016(ry search e).25 F .016
(xamines the root page)-.15 F 1.319
(of the tree, caching the most recently used pages substantially impro)133 384
R -.15(ve)-.15 G 3.82(sa).15 G 1.32(ccess time.)441.05 384 R 1.32(In addition,)
6.32 F(ph)133 396 Q .911(ysical writes are delayed as long as possible, so a m\
oderate cache can reduce the number of I/O)-.05 F 1.497
(operations signi\214cantly)133 408 R 6.497(.O)-.65 G -.15(bv)243.674 408 S
(iously).15 E 3.997(,u)-.65 G 1.497(sing a cache increases \(b)288.821 408 R
1.498(ut only increases\) the lik)-.2 F 1.498(elihood of)-.1 F .336(corruption\
 or lost data if the system crashes while a tree is being modi\214ed.)133 420 R
(If)5.336 E F2(cac)2.836 E(hesize)-.15 E F0 .335(is 0 \(no size)2.835 F
(is speci\214ed\) a def)133 432 Q(ault cache is used.)-.1 E
(int \(*compare\)\(const DBT *, const DBT *\);)108 448.8 Q .194
(Compare is the k)133 460.8 R .494 -.15(ey c)-.1 H .194(omparison function.).15
F .194(It must return an inte)5.194 F .194
(ger less than, equal to, or greater than)-.15 F .656(zero if the \214rst k)133
472.8 R .956 -.15(ey a)-.1 H -.18(rg).15 G .656
(ument is considered to be respecti).18 F -.15(ve)-.25 G .655
(ly less than, equal to, or greater than the).15 F .798(second k)133 484.8 R
1.098 -.15(ey a)-.1 H -.18(rg).15 G 3.298(ument. The).18 F .798
(same comparison function must be used on a gi)3.298 F -.15(ve)-.25 G 3.298(nt)
.15 G .799(ree e)462.774 484.8 R -.15(ve)-.25 G .799(ry time it is).15 F 2.79
(opened. If)133 496.8 R F2(compar)2.79 E(e)-.37 E F0 .29
(is NULL \(no comparison function is speci\214ed\), the k)2.79 F -.15(ey)-.1 G
2.79(sa).15 G .29(re compared le)451.08 496.8 R(xically)-.15 E(,)-.65 E
(with shorter k)133 508.8 Q -.15(ey)-.1 G 2.5(sc).15 G
(onsidered less than longer k)208.57 508.8 Q -.15(ey)-.1 G(s.).15 E
(u_long \215ags;)108 525.6 Q(The \215ag v)133 537.6 Q(alue is speci\214ed by)
-.25 E F2(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)302.2 537.6 S
(he follo)310.81 537.6 Q(wing v)-.25 E(alues:)-.25 E(R_DUP)133 554.4 Q .354
(Permit duplicate k)158 566.4 R -.15(ey)-.1 G 2.854(si).15 G 2.854(nt)250.752
566.4 S .355(he tree, i.e. permit insertion if the k)261.386 566.4 R .655 -.15
(ey t)-.1 H 2.855(ob).15 G 2.855(ei)432.64 566.4 S .355(nserted already e)
442.715 566.4 R .355(xists in)-.15 F 1.65(the tree.)158 578.4 R 1.65(The def)
6.65 F 1.65(ault beha)-.1 F(vior)-.2 E 4.149(,a)-.4 G 4.149(sd)295.509 578.4 S
1.649(escribed in)308.548 578.4 R F2(db_open)4.149 E F0 1.649(\(3\), is to o)
.24 F -.15(ve)-.15 G 1.649(rwrite a matching k).15 F -.15(ey)-.1 G .783
(when inserting a ne)158 590.4 R 3.283(wk)-.25 G 1.083 -.15(ey o)253.542 590.4
T 3.283(rt).15 G 3.283(of)280.508 590.4 S .783(ail if the R_NOO)292.021 590.4 R
(VER)-.5 E .784(WRITE \215ag is speci\214ed.)-.55 F .784(The R_DUP)5.784 F .129
(\215ag is o)158 602.4 R -.15(ve)-.15 G .129(rridden by the R_NOO).15 F(VER)-.5
E .128(WRITE \215ag, and if the R_NOO)-.55 F(VER)-.5 E .128
(WRITE \215ag is spec-)-.55 F(i\214ed, attempts to insert duplicate k)158 614.4
Q -.15(ey)-.1 G 2.5(si).15 G(nto the tree will f)314.69 614.4 Q(ail.)-.1 E .835
(If the database contains duplicate k)158 631.2 R -.15(ey)-.1 G .835
(s, the order of retrie).15 F -.25(va)-.25 G 3.335(lo).25 G 3.336(fk)414.7
631.2 S -.15(ey)426.266 631.2 S .836(/data pairs is unde\214ned if).15 F(the)
158 643.2 Q F2 -.1(ge)3.003 G(t).1 E F0 .503(function is used, ho)3.003 F(we)
-.25 E -.15(ve)-.25 G -.4(r,).15 G F2(seq)3.403 E F0 .502
(function calls with the R_CURSOR \215ag set will al)3.003 F -.1(wa)-.1 G(ys).1
E(return the logical `)158 655.2 Q(`\214rst')-.74 E 2.5('o)-.74 G 2.5(fa)263.72
655.2 S .3 -.15(ny g)273.99 655.2 T(roup of duplicate k).15 E -.15(ey)-.1 G(s.)
.15 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 2
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 113.45(DB_BTREE\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 113.45(anual DB_BTREE\(3\))340.17 48 R(int lorder;)108 84 Q .65
(The byte order for inte)133 96 R .65(gers in the stored database metadata.)
-.15 F .65(The number should represent the order)5.65 F .749(as an inte)133 108
R .749(ger; for e)-.15 F .749(xample, big endian order w)-.15 F .749
(ould be the number 4,321.)-.1 F(If)5.749 E/F1 10/Times-Italic@0 SF(lor)3.249 E
(der)-.37 E F0 .749(is 0 \(no order is)3.249 F
(speci\214ed\) the current host order is used.)133 120 Q(int maxk)108 136.8 Q
-.15(ey)-.1 G(page;).15 E .073(The maximum number of k)133 148.8 R -.15(ey)-.1
G 2.573(sw).15 G .073(hich will be stored on an)266.155 148.8 R 2.574(ys)-.15 G
.074(ingle page.)376.436 148.8 R .074(This functionality is not cur)5.074 F(-)
-.2 E(rently implemented.)133 160.8 Q(int mink)108 177.6 Q -.15(ey)-.1 G(page;)
.15 E .532(The minimum number of k)133 189.6 R -.15(ey)-.1 G 3.031(sw).15 G
.531(hich will be stored on an)266.787 189.6 R 3.031(ys)-.15 G .531
(ingle page.)379.813 189.6 R .531(This v)5.531 F .531(alue is used to deter)
-.25 F(-)-.2 E .558(mine which k)133 201.6 R -.15(ey)-.1 G 3.058(sw).15 G .558
(ill be stored on o)211.914 201.6 R -.15(ve)-.15 G(r\215o).15 E 3.058(wp)-.25 G
.558(ages, i.e. if a k)319.424 201.6 R .859 -.15(ey o)-.1 H 3.059(rd).15 G .559
(ata item is longer than the page-)408.336 201.6 R .063(size di)133 213.6 R
.063(vided by the mink)-.25 F -.15(ey)-.1 G .063(page v).15 F .063
(alue, it will be stored on o)-.25 F -.15(ve)-.15 G(r\215o).15 E 2.563(wp)-.25
G .062(ages instead of in the page itself.)408.816 213.6 R(If)133 225.6 Q F1
(mink)2.5 E -.3(ey)-.1 G(pa).3 E -.1(ge)-.1 G F0(is 0 \(no minimum number of k)
2.6 E -.15(ey)-.1 G 2.5(si).15 G 2.5(ss)332.96 225.6 S(peci\214ed\) a v)343.24
225.6 Q(alue of 2 is used.)-.25 E
(size_t \(*pre\214x\)\(const DBT *, const DBT *\);)108 242.4 Q .691
(Pre\214x is the pre\214x comparison function.)133 254.4 R .692
(If speci\214ed, this function must return the number of bytes)5.691 F .195
(of the second k)133 266.4 R .495 -.15(ey a)-.1 H -.18(rg).15 G .195
(ument which are necessary to determine that it is greater than the \214rst k)
.18 F .495 -.15(ey a)-.1 H -.18(rg).15 G(u-).18 E 2.994(ment. If)133 278.4 R
.494(the k)2.994 F -.15(ey)-.1 G 2.994(sa).15 G .494(re equal, the k)211.376
278.4 R .794 -.15(ey l)-.1 H .494(ength should be returned.).15 F .494
(Note, the usefulness of this function)5.494 F .327(is v)133 290.4 R .327
(ery data dependent, b)-.15 F .326(ut, in some data sets can produce signi\214\
cantly reduced tree sizes and search)-.2 F 2.789(times. If)133 302.4 R F1(pr)
2.789 E(e\214x)-.37 E F0 .289(is NULL \(no pre\214x function is speci\214ed\),)
2.789 F/F2 10/Times-Bold@0 SF(and)2.789 E F0 .29
(no comparison function is speci\214ed, a)2.79 F(def)133 314.4 Q .902(ault le)
-.1 F .902(xical comparison function is used.)-.15 F(If)5.901 E F1(pr)3.401 E
(e\214x)-.37 E F0 .901(is NULL and a comparison function is speci-)3.401 F
(\214ed, no pre\214x comparison is done.)133 326.4 Q(u_int psize;)108 343.2 Q
-.15(Pa)133 355.2 S .118
(ge size is the size \(in bytes\) of the pages used for nodes in the tree.).15
F .119(The minimum page size is 512)5.119 F .377
(bytes and the maximum page size is 64K.)133 367.2 R(If)5.376 E F1(psize)2.876
E F0 .376(is 0 \(no page size is speci\214ed\) a page size is cho-)2.876 F
(sen based on the underlying \214le system I/O block size.)133 379.2 Q .79
(If the \214le already e)108 396 R .79(xists \(and the O_TR)-.15 F .79
(UNC \215ag is not speci\214ed\), the v)-.4 F .79
(alues speci\214ed for the parameters)-.25 F
(\215ags, lorder and psize are ignored in f)108 408 Q -.2(avo)-.1 G 2.5(ro).2 G
2.5(ft)284.4 408 S(he v)293.01 408 Q(alues used when the tree w)-.25 E
(as created.)-.1 E/F3 9/Times-Bold@0 SF(DB OPERA)72 424.8 Q(TIONS)-.855 E F0
1.037(The functions returned by)108 436.8 R F1(db_open)3.537 E F0 1.036
(for the btree access method are as described in)3.536 F F1(db_open)3.536 E F0
1.036(\(3\), with the).24 F(follo)108 448.8 Q(wing e)-.25 E
(xceptions and additions:)-.15 E 5.28(type The)108 465.6 R(type is DB_BTREE.)
2.5 E 10.28(del Space)108 482.4 R 1.681(freed up by deleting k)4.181 F -.15(ey)
-.1 G 1.681(/data pairs from the tree is ne).15 F -.15(ve)-.25 G 4.181(rr).15 G
1.682(eclaimed, although it is reused)411.342 482.4 R .734(where possible.)133
494.4 R .734(This means that the btree storage structure is gro)5.734 F(w-only)
-.25 E 5.734(.T)-.65 G .734(he only solutions are to)443.734 494.4 R -.2(avo)
133 506.4 S(id e).2 E(xcessi)-.15 E .3 -.15(ve d)-.25 H
(eletions, or to create a fresh tree periodically from a scan of an e).15 E
(xisting one.)-.15 E 9.72(put The)108 523.2 R F1(put)2.5 E F0(function tak)2.5
E(es the follo)-.1 E(wing additional \215ags:)-.25 E(R_SETCURSOR)133 540 Q
(Store the k)158 552 Q -.15(ey)-.1 G(/data pair).15 E 2.5(,s)-.4 G
(etting or initializing the position of the cursor to reference it.)256.5 552 Q
9.17(seq F)108 568.8 R(orw)-.15 E
(ard sequential scans of a tree are from the least k)-.1 E .3 -.15(ey t)-.1 H
2.5(ot).15 G(he greatest.)373.55 568.8 Q .892(The returned k)133 585.6 R 1.192
-.15(ey f)-.1 H .892(or the).15 F F1(seq)3.393 E F0 .893
(function is not necessarily an e)3.393 F .893
(xact match for the speci\214ed k)-.15 F 1.193 -.15(ey i)-.1 H 3.393(nt).15 G
(he)530.56 585.6 Q .5(btree access method.)133 597.6 R .5(The returned k)5.5 F
.8 -.15(ey i)-.1 H 3(st).15 G .499(he smallest k)307.04 597.6 R .799 -.15(ey g)
-.1 H .499(reater than or equal to the speci\214ed k).15 F -.15(ey)-.1 G(,)-.5
E(permitting partial k)133 609.6 Q .3 -.15(ey m)-.1 H
(atches and range searches.).15 E(The)133 626.4 Q F1(seq)2.5 E F0(function tak)
2.5 E(es the follo)-.1 E(wing additional \215ags:)-.25 E(R_LAST)133 643.2 Q .04
(The last k)158 655.2 R -.15(ey)-.1 G .04(/data pair of the database is return\
ed, and the cursor is set or initialized to reference).15 F(it.)158 667.2 Q
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(2)535 732 Q EP
%%Page: 3 3
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 113.45(DB_BTREE\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 113.45(anual DB_BTREE\(3\))340.17 48 R(R_PREV)133 84 Q(Retrie)158 96
Q .59 -.15(ve t)-.25 H .29(he k).15 F -.15(ey)-.1 G .29
(/data pair immediately before the cursor).15 F 5.29(.I)-.55 G 2.79(ft)395.73
96 S .29(he cursor is not yet set, this is the)404.63 96 R
(same as the R_LAST \215ag.)158 108 Q/F1 9/Times-Bold@0 SF(ERR)72 124.8 Q(ORS)
-.27 E F0(The)108 136.8 Q/F2 10/Times-Italic@0 SF(btr)2.541 E(ee)-.37 E F0 .041
(access method functions may f)2.541 F .041(ail and set)-.1 F F2(errno)2.541 E
F0 .041(for an)2.541 F 2.541(yo)-.15 G 2.541(ft)376.152 136.8 S .041
(he errors speci\214ed for the library func-)384.803 136.8 R(tion)108 148.8 Q
F2(db_open)2.5 E F0(\(3\).).24 E F1(SEE ALSO)72 165.6 Q F2(db_hash)108 177.6 Q
F0(\(3\),).28 E F2(db_loc)2.5 E(k)-.2 E F0(\(3\),).67 E F2(db_lo)2.5 E(g)-.1 E
F0(\(3\),).22 E F2(db_mpool)2.5 E F0(\(3\),).51 E F2(db_open)2.5 E F0(\(3\),)
.24 E F2(db_r)2.5 E(ecno)-.37 E F0(\(3\),).18 E F2(db_txn)2.5 E F0(\(3\)).24 E
F2(The Ubiquitous B-tr)108 201.6 Q(ee)-.37 E F0 2.5(,D).18 G(ouglas Comer)
209.47 201.6 Q 2.5(,A)-.4 G(CM Comput. Surv)276.72 201.6 Q 2.5(.1)-.65 G
(1, 2 \(June 1979\), 121-138.)360.25 201.6 Q F2(Pr)108 225.6 Q 1.588
(e\214x B-tr)-.37 F(ees)-.37 E F0 4.088(,B).27 G 1.587(ayer and Unterauer)
177.636 225.6 R 4.087(,A)-.4 G 1.587(CM T)270.447 225.6 R 1.587
(ransactions on Database Systems, V)-.35 F 1.587(ol. 2, 1 \(March 1977\),)-1.29
F(11-26.)108 237.6 Q F2(The Art of Computer Pr)108 261.6 Q -.1(og)-.45 G -.15
(ra).1 G(mming V).15 E(ol. 3: Sorting and Sear)-1.11 E -.15(ch)-.37 G(ing).15 E
F0 2.5(,D).22 G(.E. Knuth, 1968, pp 471-480.)382 261.6 Q(4.4 Berk)72 732 Q(ele)
-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E
(3)535 732 Q EP
%%Page: 1 4
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_HASH\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_HASH\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72
84 S(ME).18 E F0(db_hash \255 hash database access method)108 96 Q F1
(DESCRIPTION)72 112.8 Q F0 .485(The DB library is a f)108 124.8 R .485
(amily of groups of functions that pro)-.1 F .486
(vides a modular programming interf)-.15 F .486(ace to trans-)-.1 F .823
(actions and record-oriented \214le access.)108 136.8 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 148.8 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 148.8 S .258(ell as v)237.082 148.8 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 148.8 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 148.8 S .258
(he functional groups \(e.g.)436.746 148.8 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 160.8 R .306(tional groups are e)108 172.8 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 172.8 Q .245(description of transactions, see)108 184.8 R/F2 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F2(db_open)2.745 E
F0(\(3\)).24 E .307(and then the indi)108 196.8 R .307
(vidual access method manual pages:)-.25 F F2(db_btr)2.808 E(ee)-.37 E F0
(\(3\),).18 E F2(db_hash)2.808 E F0(\(3\),).28 E F2(db_lo)2.808 E(g)-.1 E F0
.308(\(3\) and).22 F F2(db_r)2.808 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
208.8 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
208.8 R 3.635(,s)-.4 G(ee)307.32 208.8 Q F2(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 220.8 Q 2.5(,s)-.4 G(ee)171.2 220.8 Q F2(db_mpool)2.5 E F0
(\(3\).).51 E
(This manual page describes speci\214c details of the hashing access method.)
108 237.6 Q .59(The hash data structure is an e)108 254.4 R .591
(xtensible, dynamic hashing scheme.)-.15 F(Backw)5.591 E .591
(ard compatible interf)-.1 F .591(aces to the)-.1 F .209
(functions described in)108 266.4 R F2(dbm)2.709 E F0 .209(\(3\), and).32 F F2
(ndbm)2.709 E F0 .209(\(3\) are pro).32 F .209(vided, ho)-.15 F(we)-.25 E -.15
(ve)-.25 G 2.708(rt).15 G .208(hese interf)382.71 266.4 R .208
(aces are not compatible with)-.1 F(pre)108 278.4 Q(vious \214le formats.)-.25
E F1 -.495(AC)72 295.2 S(CESS METHOD SPECIFIC INFORMA).495 E(TION)-.855 E F0
.612(The hash access method speci\214c data structure pro)108 307.2 R .612
(vided to)-.15 F F2(db_open)3.112 E F0 .612(is typedef)3.112 F 1.612 -.5('d a)
.55 H .613(nd named HASHINFO.).5 F 2.5(AH)108 319.2 S
(ASHINFO structure has at least the follo)124.94 319.2 Q
(wing \214elds, which may be initialized before calling)-.25 E F2(db_open)2.5 E
F0(:).24 E(u_int bsize;)108 336 Q F2(Bsize)133 348 Q F0 2.041
(de\214nes the hash table b)4.541 F(uck)-.2 E 2.041(et size, and is, by def)-.1
F 2.04(ault, 256 bytes.)-.1 F 2.04(It may be preferable to)7.04 F
(increase the page size for disk-resident tables and tables with lar)133 360 Q
(ge data items.)-.18 E(u_int cachesize;)108 376.8 Q 3.846(As)133 388.8 S 1.347
(uggested maximum size, in bytes, of the memory cache.)147.956 388.8 R 1.347
(This v)6.347 F 1.347(alue is)-.25 F/F3 10/Times-Bold@0 SF(only)3.847 E F0
(advisory)3.847 E 3.847(,a)-.65 G 1.347(nd the)513.933 388.8 R
(access method will allocate more memory rather than f)133 400.8 Q(ail.)-.1 E
(u_int f)108 417.6 Q -.1(fa)-.25 G(ctor;).1 E F2(Ffactor)133 429.6 Q F0 1.17
(indicates a desired density within the hash table.)3.67 F 1.169
(It is an approximation of the number of)6.169 F -.1(ke)133 441.6 S 1.162
(ys allo)-.05 F 1.162(wed to accumulate in an)-.25 F 3.662(yo)-.15 G 1.162
(ne b)284.852 441.6 R(uck)-.2 E 1.162(et, determining when the hash table gro)
-.1 F 1.162(ws or shrinks.)-.25 F(The def)133 453.6 Q(ault v)-.1 E(alue is 8.)
-.25 E(u_int32_t \(*hash\)\(const v)108 470.4 Q(oid *, size_t\);)-.2 E F2(Hash)
133 482.4 Q F0 .788(is a user de\214ned hash function.)3.288 F .787
(Since no hash function performs equally well on all possible)5.788 F .017
(data, the user may \214nd that the b)133 494.4 R .018
(uilt-in hash function does poorly on a particular data set.)-.2 F .018
(User speci-)5.018 F 1.154(\214ed hash functions must tak)133 506.4 R 3.654(et)
-.1 G 1.354 -.1(wo a)260.61 506.4 T -.18(rg).1 G 1.154
(uments \(a pointer to a byte string and a length\) and return a).18 F
(32-bit quantity to be used as the hash v)133 518.4 Q(alue.)-.25 E .665
(If a hash function is speci\214ed,)133 535.2 R F2(hash_open)3.165 E F0 .666
(will attempt to determine if the hash function speci\214ed is)3.166 F
(the same as the one with which the database w)133 547.2 Q
(as created, and will f)-.1 E(ail if it is not.)-.1 E(int lorder;)108 564 Q .65
(The byte order for inte)133 576 R .65(gers in the stored database metadata.)
-.15 F .65(The number should represent the order)5.65 F .748(as an inte)133 588
R .749(ger; for e)-.15 F .749(xample, big endian order w)-.15 F .749
(ould be the number 4,321.)-.1 F(If)5.749 E F2(lor)3.249 E(der)-.37 E F0 .749
(is 0 \(no order is)3.249 F .456(speci\214ed\) the current host order is used.)
133 600 R .456(If the)5.456 F .456(\214le already e)5.456 F .456
(xists, the speci\214ed v)-.15 F .455(alue is ignored and)-.25 F(the v)133 612
Q(alue speci\214ed when the tree w)-.25 E(as created is used.)-.1 E
(u_int nelem;)108 628.8 Q F2(Nelem)133 640.8 Q F0 1.225
(is an estimate of the \214nal size of the hash table.)3.724 F 1.225
(If not set or set too lo)6.225 F 2.525 -.65(w, h)-.25 H 1.225(ash tables will)
.65 F -.15(ex)133 652.8 S 1.294(pand gracefully as k).15 F -.15(ey)-.1 G 3.794
(sa).15 G 1.294(re entered, although a slight performance de)248.296 652.8 R
1.293(gradation may be noticed.)-.15 F(The def)133 664.8 Q(ault v)-.1 E
(alue is 1.)-.25 E .79(If the \214le already e)108 681.6 R .79
(xists \(and the O_TR)-.15 F .79(UNC \215ag is not speci\214ed\), the v)-.4 F
.79(alues speci\214ed for the parameters)-.25 F(bsize, f)108 693.6 Q -.1(fa)
-.25 G(ctor).1 E 2.5(,l)-.4 G(order and nelem are ignored and the v)167.23
693.6 Q(alues speci\214ed when the tree w)-.25 E(as created are used.)-.1 E
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 5
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_HASH\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_HASH\(3\))340.17 48 R/F1 9/Times-Bold@0 SF(DB OPERA)
72 84 Q(TIONS)-.855 E F0(The functions returned by)108 96 Q/F2 10
/Times-Italic@0 SF(db_open)2.5 E F0
(for the hash access method are as described in)2.5 E F2(db_open)2.5 E F0
(\(3\).).24 E F1(ERR)72 112.8 Q(ORS)-.27 E F0(The)108 124.8 Q F2(hash)2.609 E
F0 .109(access method functions may f)2.609 F .109(ail and set)-.1 F F2(errno)
2.609 E F0 .109(for an)2.609 F 2.609(yo)-.15 G 2.609(ft)375.678 124.8 S .109
(he errors speci\214ed for the library func-)384.397 124.8 R(tion)108 136.8 Q
F2(db_open)2.5 E F0(\(3\).).24 E F1(SEE ALSO)72 153.6 Q F2(db_btr)108 165.6 Q
(ee)-.37 E F0(\(3\),).18 E F2(db_loc)2.5 E(k)-.2 E F0(\(3\),).67 E F2(db_lo)2.5
E(g)-.1 E F0(\(3\),).22 E F2(db_mpool)2.5 E F0(\(3\),).51 E F2(db_open)2.5 E F0
(\(3\),).24 E F2(db_r)2.5 E(ecno)-.37 E F0(\(3\),).18 E F2(db_txn)2.5 E F0
(\(3\)).24 E F2(Dynamic Hash T)108 189.6 Q(ables)-.92 E F0 2.5(,P).27 G(er)
206.79 189.6 Q(-Ak)-.2 E 2.5(eL)-.1 G(arson, Communications of the A)242.86
189.6 Q(CM, April 1988.)-.4 E F2 2.5(AN)108 213.6 S .3 -.15(ew H)123.28 213.6 T
(ash P).15 E(ac)-.8 E(ka)-.2 E .2 -.1(ge f)-.1 H(or UNIX).1 E F0 2.5(,M).94 G
(ar)248.41 213.6 Q(go Seltzer)-.18 E 2.5(,U)-.4 G(SENIX Proceedings, W)308.09
213.6 Q(inter 1991.)-.4 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)
132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(2)535 732 Q EP
%%Page: 1 6
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_LOCK\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_LOCK\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72
84 S(ME).18 E F0(db_lock \255 general purpose lock manager)108 96 Q F1
(SYNOPSIS)72 112.8 Q/F2 10/Times-Bold@0 SF(#include <db_lock.h>)108 124.8 Q
(int)108 148.8 Q(lock_cr)108 160.8 Q(eate\(const char *path, mode_t mode,)-.18
E(int lock_modes, const int8_t con\215icts[][], u_int maxlocks\);)158 172.8 Q
(LOCK_T)108 196.8 Q(ABLE_T *)-.9 E(lock_open\(const char *path\);)108 208.8 Q
(int)108 232.8 Q(lock_v)108 244.8 Q(ec\(LOCK_T)-.1 E(ABLE_T *lt, DBT *lock)-.9
E(er)-.1 E 2.5(,s)-.92 G(truct timespec *timeout,)308.21 244.8 Q
(LOCK_REQ_T list[], int nlist, LOCK_REQ_T **elistp, DBT *con\215ict\);)158
256.8 Q(int)108 280.8 Q(lock_get\(LOCK_T)108 292.8 Q
(ABLE_T *lt, const DBT *lock)-.9 E(er)-.1 E(,)-.92 E
(const DBT *obj, const lock_mode_t lock_mode, LOCK_T **lockp\);)158 304.8 Q
(int)108 328.8 Q(lock_put\(LOCK_T *lockp\);)108 340.8 Q(int)108 364.8 Q
(lock_close\(LOCK_T)108 376.8 Q(ABLE_T *lt\);)-.9 E(int)108 400.8 Q
(lock_unlink\(const char *path, int f)108 412.8 Q(or)-.25 E(ce\);)-.18 E F1
(DESCRIPTION)72 429.6 Q F0 .485(The DB library is a f)108 441.6 R .485
(amily of groups of functions that pro)-.1 F .486
(vides a modular programming interf)-.15 F .486(ace to trans-)-.1 F .823
(actions and record-oriented \214le access.)108 453.6 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 465.6 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 465.6 S .258(ell as v)237.082 465.6 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 465.6 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 465.6 S .258
(he functional groups \(e.g.)436.746 465.6 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 477.6 R .306(tional groups are e)108 489.6 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 489.6 Q .245(description of transactions, see)108 501.6 R/F3 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F3(db_open)2.745 E
F0(\(3\)).24 E .307(and then the indi)108 513.6 R .307
(vidual access method manual pages:)-.25 F F3(db_btr)2.808 E(ee)-.37 E F0
(\(3\),).18 E F3(db_hash)2.808 E F0(\(3\),).28 E F3(db_lo)2.808 E(g)-.1 E F0
.308(\(3\) and).22 F F3(db_r)2.808 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
525.6 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
525.6 R 3.635(,s)-.4 G(ee)307.32 525.6 Q F3(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 537.6 Q 2.5(,s)-.4 G(ee)171.2 537.6 Q F3(db_mpool)2.5 E F0
(\(3\).).51 E
(This manual page describes speci\214c details of the locking interf)108 554.4
Q(ace.)-.1 E F3(Db_loc)108 571.2 Q(k)-.2 E F0 .346(is the library interf)2.846
F .346(ace intended to pro)-.1 F .346(vide general-purpose locking.)-.15 F .347
(While designed to w)5.347 F .347(ork with)-.1 F .946(the other DB functions, \
these functions are also useful for more general locking purposes.)108 583.2 R
.946(Locks can be)5.946 F(shared between processes.)108 595.2 Q .682
(The function)108 612 R F3(loc)3.182 E(k_cr)-.2 E(eate)-.37 E F0 .683
(creates and initializes the lock table identi\214ed by the)3.182 F F3(path)
3.183 E F0(directory)3.183 E 5.683(.T)-.65 G .683(his direc-)501.827 612 R .565
(tory must already e)108 624 R .565(xist when)-.15 F F3(loc)3.065 E(k_cr)-.2 E
(eate)-.37 E F0 .565(is called.)3.065 F .565(If the lock table identi\214ed by)
5.565 F F3(path)3.064 E F0 .564(already e)3.064 F .564(xists, then)-.15 F F3
(loc)108 636 Q(k_cr)-.2 E(eate)-.37 E F0 .974
(returns success without further action.)3.474 F .974
(The \214les associated with the lock table are created in)5.974 F 2.017
(the directory speci\214ed by)108 648 R F3(path)4.517 E F0 7.017(.\().28 G
2.017(The group of the created \214les is based on the system and directory)
250.846 648 R(def)108 660 Q .076(aults, and is not further speci\214ed by)-.1 F
F3(loc)2.576 E(k_cr)-.2 E(eate)-.37 E F0 2.576(.\) All).18 F .076
(\214les created by)2.576 F F3(loc)2.576 E(k_cr)-.2 E(eate)-.37 E F0 .077
(are created with mode)2.577 F F3(mode)108 672 Q F0(\(as described in)2.5 E F3
-.15(ch)2.5 G(mod).15 E F0(\(2\)\) and modi\214ed by the process' umask v).77 E
(alue \(see)-.25 E F3(umask)2.5 E F0(\(2\)\).).67 E .739(The parameter)108
688.8 R F3(loc)3.239 E(k_modes)-.2 E F0 .739(is the number of lock modes to be\
 recognized by the lock table \(including the)3.239 F(4.4 Berk)72 732 Q(ele)-.1
E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(1)
535 732 Q EP
%%Page: 2 7
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_LOCK\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_LOCK\(3\))340.17 48 R -.74(``)108 84 S(not-granted')
.74 E 2.5('m)-.74 G 2.5(ode\). The)176.22 84 R(parameter)2.5 E/F1 10
/Times-Italic@0 SF(con\215icts)2.5 E F0(is an)2.5 E F1(loc)2.5 E(k_modes)-.2 E
F0(by)2.5 E F1(loc)2.5 E(k_modes)-.2 E F0(array)2.5 E 5(.A)-.65 G(non-0 v)
467.59 84 Q(alue for:)-.25 E(con\215icts[requested_mode][held_mode])158 108 Q
.174(indicates that)108 132 R F1 -.37(re)2.674 G(quested_mode).37 E F0(and)
2.674 E F1(held_mode)2.674 E F0 2.675(con\215ict. The)2.674 F -.74(``)2.675 G
(not-granted').74 E 2.675('m)-.74 G .175(ode must be represented by 0.)419.705
132 R(The include \214le <db_lock.h> declares tw)108 148.8 Q 2.5(oc)-.1 G
(ommonly used con\215ict arrays:)283.87 148.8 Q(int lock_sx_n;)108 165.6 Q
(const int8_t lock_sx_c[lock_sx_n][lock_sx_n];)108 177.6 Q(These v)133 189.6 Q
(ariables specify a con\215ict array for a simple scheme using shared and e)
-.25 E(xclusi)-.15 E .3 -.15(ve l)-.25 H(ock modes.).15 E(int lock_g_n;)108
206.4 Q(const int8_t lock_g_c[lock_g_n][lock_g_n];)108 218.4 Q 1.071(These v)
133 230.4 R 1.071(ariables specify a con\215ict array that in)-.25 F -.2(vo)-.4
G(lv).2 E 1.071(es v)-.15 F 1.07
(arious intent lock modes \(e.g. intent shared\))-.25 F
(that are used for multigranularity locking.)133 242.4 Q 1.53
(In addition, <db_lock.h> de\214nes the follo)108 259.2 R 1.531
(wing macros that name lock modes for use with the standard)-.25 F(tables abo)
108 271.2 Q -.15(ve)-.15 G(:).15 E(LOCK_IS)144 288 Q(intent shared)169 300 Q
(LOCK_IX)144 312 Q(intent e)169 324 Q(xclusi)-.15 E -.15(ve)-.25 G(LOCK_NG)144
336 Q(not granted \(al)169 348 Q -.1(wa)-.1 G(ys 0\)).1 E(LOCK_S)144 360 Q
(shared)169 372 Q(LOCK_SIX)144 384 Q(shared/intent e)169 396 Q(xclusi)-.15 E
-.15(ve)-.25 G(LOCK_X)144 408 Q -.15(ex)169 420 S(clusi).15 E -.15(ve)-.25 G F1
(Maxloc)108 436.8 Q(ks)-.2 E F0 .442(is the maximum number of locks to be held\
 or requested in the table, and is used by)2.942 F F1(loc)2.941 E(k_cr)-.2 E
(eate)-.37 E F0(to estimate ho)108 448.8 Q 2.5(wm)-.25 G
(uch space to allocate for v)181.36 448.8 Q(arious lock-table data structures.)
-.25 E(The function)108 465.6 Q F1(loc)2.5 E(k_cr)-.2 E(eate)-.37 E F0
(returns -1 on f)2.5 E(ailure, setting)-.1 E F1(errno)2.5 E F0 2.5(,a).18 G
(nd 0 on success.)356.07 465.6 Q .202(The function)108 482.4 R F1(loc)2.703 E
(k_open)-.2 E F0 .203(returns a pointer to the lock table identi\214ed by)2.703
F F1(path)2.703 E F0 2.703(,w).28 G .203(hich must ha)425.678 482.4 R .503 -.15
(ve a)-.2 H .203(lready been).15 F 1.162(created by a call to)108 494.4 R F1
(loc)3.661 E(k_cr)-.2 E(eate)-.37 E F0 6.161(.T).18 G 1.161(he process must ha)
252.869 494.4 R 1.461 -.15(ve p)-.2 H 1.161
(ermission to read and write \214les with o).15 F(wners,)-.25 E .06
(groups and permissions as described for)108 506.4 R F1(loc)2.56 E(k_cr)-.2 E
(eate)-.37 E F0 5.06(.T).18 G(he)331.04 506.4 Q F1(loc)2.56 E(k_open)-.2 E F0
.06(function returns NULL on f)2.56 F .06(ailure, set-)-.1 F(ting)108 518.4 Q
F1(errno)2.5 E F0(.).18 E .986(The function)108 535.2 R F1(loc)3.486 E(k_vec)
-.2 E F0 .986
(atomically obtains and releases one or more locks from the designated table.)
3.486 F(The)5.986 E(function)108 547.2 Q F1(loc)4.52 E(k_vec)-.2 E F0 2.02(is \
intended to support acquisition or trading of multiple locks under one lock ta\
ble)4.52 F(semaphore, as is needed for lock coupling or in multigranularity lo\
cking for lock escalation.)108 559.2 Q .746(If an)108 576 R 3.246(yo)-.15 G
3.246(ft)140.442 576 S .746(he requested locks cannot be acquired or an)149.798
576 R 3.246(yo)-.15 G 3.246(ft)342.786 576 S .746
(he locks to be released cannot be released, no)352.142 576 R .117
(locks are acquired and no locks are released, and)108 588 R F1(loc)2.617 E
(k_vec)-.2 E F0 .117(returns an error)2.617 F 5.117(.T)-.55 G .117(he function)
419.211 588 R F1(loc)2.617 E(k_vec)-.2 E F0 .118(returns 0)2.617 F 1.143
(on success.)108 600 R 1.143(If an error occurs,)6.143 F F1(loc)3.642 E(k_vec)
-.2 E F0 1.142(returns one of the follo)3.642 F 1.142(wing v)-.25 F 3.642
(alues. In)-.25 F 1.142(addition, if)3.642 F F1(elistp)3.642 E F0 1.142(is not)
3.642 F(NULL, it is set to point to the LOCK_REQ_T entry which w)108 612 Q
(as being processed when the error occurred.)-.1 E(LOCK_GET_DEADLOCK)108 628.8
Q .431(The speci\214ed)133 640.8 R F1(loc)2.931 E -.1(ke)-.2 G(r).1 E F0 -.1
(wa)2.931 G 2.931(ss).1 G .431(elected as a victim in order to resolv)239.854
640.8 R 2.932(ead)-.15 G 2.932(eadlock. In)407.718 640.8 R .432
(this case, if the)2.932 F F1(con-)2.932 E(\215ict)133 652.8 Q F0(ar)2.901 E
.401(gument is non-NULL, it is set to reference the identity of a lock)-.18 F
.4(er holding the lock referenced)-.1 F(by)133 664.8 Q F1(elistp)2.585 E F0
.085(at the time the request w)2.585 F .085(as denied.)-.1 F .086
(\(This identity resides in static memory and may be o)5.086 F -.15(ve)-.15 G
-.2(r-).15 G(written by subsequent calls to)133 676.8 Q F1(loc)2.5 E(k_vec)-.2
E F0(\).).31 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q
99.315(ution August)-.2 F(1, 1995)2.5 E(2)535 732 Q EP
%%Page: 3 8
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_LOCK\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_LOCK\(3\))340.17 48 R(LOCK_GET_ERR)108 84 Q(OR)-.4 E
(An error occurred and the e)133 96 Q(xternal v)-.15 E(ariable)-.25 E/F1 10
/Times-Italic@0 SF(errno)2.5 E F0(has been set to indicate the error)2.5 E(.)
-.55 E(LOCK_GET_NO)108 112.8 Q(THELD)-.4 E
(The lock cannot be released, as it w)133 124.8 Q(as not held by the)-.1 E F1
(loc)2.5 E -.1(ke)-.2 G(r).1 E F0(.).73 E(LOCK_GET_RESOURCE)108 141.6 Q 2.311(\
The lock manager is unable to grant the requested locks because of limited int\
ernal resources.)133 153.6 R(\(Releasing locks may allo)133 165.6 Q 2.5(wf)-.25
G(uture calls to)249.4 165.6 Q F1(loc)2.5 E(k_vec)-.2 E F0(to succeed.\))2.5 E
(LOCK_GET_TIMEOUT)108 182.4 Q 3.204(At)133 194.4 S .704(imeout ar)146.204 194.4
R .704(gument w)-.18 F .705(as speci\214ed, and the requested locks were not a)
-.1 F -.25(va)-.2 G .705(ilable soon enough.).25 F .705(In this)5.705 F .625
(case, if the)133 206.4 R F1(con\215ict)3.125 E F0(ar)3.125 E .624
(gument is non-NULL, it is set to reference the identity of a lock)-.18 F .624
(er holding the)-.1 F .551(lock referenced by)133 218.4 R F1(elistp)3.052 E F0
.552(at the time the request w)3.052 F .552(as denied.)-.1 F .552
(\(This identity resides in static memory)5.552 F(and may be o)133 230.4 Q -.15
(ve)-.15 G(rwritten by subsequent calls to).15 E F1(loc)2.5 E(k_vec)-.2 E F0
(\).).31 E(The)108 247.2 Q F1(loc)3.005 E -.1(ke)-.2 G(r).1 E F0(ar)3.005 E
.504(gument speci\214ed to)-.18 F F1(loc)3.004 E(k_vec)-.2 E F0 .504
(is a pointer to an untyped byte string which identi\214es the entity)3.004 F
(requesting or releasing the lock.)108 259.2 Q(If)5 E F1(loc)2.5 E -.1(ke)-.2 G
(r).1 E F0(is NULL, the calling process' pid is used instead.)2.5 E(The)108 276
Q F1(timeout)4.628 E F0(ar)4.628 E 2.128(gument pro)-.18 F 2.128(vided to)-.15
F F1(loc)4.628 E(k_vec)-.2 E F0 2.128(speci\214es a maximum interv)4.628 F
2.128(al to w)-.25 F 2.128(ait for the locks to be)-.1 F 2.642(granted. If)108
288 R F1(timeout)2.642 E F0 .142(is NULL, it is ignored, and)2.642 F F1(loc)
2.642 E(k_vec)-.2 E F0 .141
(will not return until all of the locks are acquired or)2.642 F
(an error has occurred.)108 300 Q(The)108 316.8 Q F1(list)4.263 E F0 1.764
(array pro)4.263 F 1.764(vided to)-.15 F F1(loc)4.264 E(k_vec)-.2 E F0 1.764
(is typedef)4.264 F 2.764 -.5('d i).55 H 4.264(n<).5 G 1.764
(db_lock.h> as LOCK_REQ_T)331.114 316.8 R 6.764(.A)-.74 G(LOCK_REQ_T)476.67
316.8 Q(structure has at least the follo)108 328.8 Q
(wing \214elds, which must be initialized before calling)-.25 E F1(loc)2.5 E
(k_vec)-.2 E F0(:).31 E(enum lock)108 345.6 Q(op op;)-.1 E
(The operation to be performed, which must be set to one of the follo)133 357.6
Q(wing v)-.25 E(alues:)-.25 E(LOCK_GET)133 374.4 Q .201
(Get a lock, as de\214ned by the v)158 386.4 R .201(alues of)-.25 F F1(loc)
2.701 E -.1(ke)-.2 G(r).1 E F0(,).73 E F1(obj)2.701 E F0(and)2.7 E F1(loc)2.7 E
(k_mode)-.2 E F0 5.2(.U).18 G .2(pon return from)435.99 386.4 R F1(loc)2.7 E
(k_vec)-.2 E F0(,).31 E .161(if the)158 398.4 R F1(loc)2.661 E(kp)-.2 E F0 .162
(\214eld is non-NULL, a reference to the acquired lock is stored there.)2.662 F
.162(\(This reference)5.162 F(is in)158 410.4 Q -.25(va)-.4 G(lidated by an).25
E 2.5(yc)-.15 G(all to)247.19 410.4 Q F1(loc)2.5 E(k_vec)-.2 E F0(or)2.5 E F1
(loc)2.5 E(k_put)-.2 E F0(which releases the lock.\))2.5 E(LOCK_PUT)133 427.2 Q
(The lock referenced by the contents of the)158 439.2 Q F1(loc)2.5 E(kp)-.2 E
F0(\214eld is released.)2.5 E(LOCK_PUT_ALL)133 456 Q .759
(All locks held by the)158 468 R F1(loc)3.259 E -.1(ke)-.2 G(r).1 E F0 .759
(are released.)3.259 F(\(An)5.759 E 3.259(yl)-.15 G .759
(ocks acquired as a part of the current call to)358.501 468 R F1(loc)158 480 Q
(k_vec)-.2 E F0(are not considered for this operation\).)2.5 E(LOCK_PUT_OBJ)133
496.8 Q 1.409(All locks held by the)158 508.8 R F1(loc)3.909 E -.1(ke)-.2 G(r)
.1 E F0 3.909(,o).73 G 3.909(nt)287.704 508.8 S 1.409(he object)299.393 508.8 R
F1(obj)3.909 E F0 3.909(,w).48 G 1.41(ith the mode speci\214ed by)367.98 508.8
R F1(loc)3.91 E(k_mode)-.2 E F0 3.91(,a).18 G(re)532.23 508.8 Q 2.802
(released. A)158 520.8 R F1(loc)2.802 E(k_mode)-.2 E F0 .301
(of LOCK_NG indicates that all locks on the object should be released.)2.802 F
(\(An)158 532.8 Q 3.053(yl)-.15 G .553
(ocks acquired as a part of the current call to)184.233 532.8 R F1(loc)3.054 E
(k_vec)-.2 E F0 .554(are not considered for this opera-)3.054 F(tion\).)158
544.8 Q(const DBT obj;)108 561.6 Q
(An untyped byte string which speci\214es the object to be lock)133 573.6 Q
(ed or released.)-.1 E(const lock_mode_t lock_mode;)108 590.4 Q
(The lock mode, used as an inde)133 602.4 Q 2.5(xi)-.15 G(nto)268.94 602.4 Q F1
(lt)2.5 E F0 1.1 -.55('s c).68 H(on\215ict array).55 E(.)-.65 E
(LOCK_T **lockp;)108 619.2 Q 2.5(Ap)133 631.2 S
(ointer to a pointer to a lock reference.)147.72 631.2 Q(The)108 648 Q F1
(nlist)2.5 E F0(ar)2.5 E(gument speci\214es the number of elements in the)-.18
E F1(list)2.5 E F0(array)2.5 E(.)-.65 E 1.229(The function)108 664.8 R F1(loc)
3.729 E(k_g)-.2 E(et)-.1 E F0 1.228(is a simple interf)3.728 F 1.228
(ace to the)-.1 F F1(loc)3.728 E(k_vec)-.2 E F0(functionality)3.728 E 3.728(,a)
-.65 G 1.228(nd is equi)416.31 664.8 R -.25(va)-.25 G 1.228
(lent to calling the).25 F F1(loc)108 676.8 Q(k_vec)-.2 E F0 .123
(function with the)2.623 F F1(lt)2.623 E F0(and)2.623 E F1(loc)2.623 E -.1(ke)
-.2 G(r).1 E F0(ar)2.623 E .123(guments, NULL)-.18 F F1(timeout)2.623 E F0(,)
.68 E F1(elistp)2.623 E F0(and)2.623 E F1(con\215ict)2.623 E F0(ar)2.623 E .124
(guments, and a sin-)-.18 F .944(gle element)108 688.8 R F1(list)3.444 E F0
(array)3.444 E 3.444(,f)-.65 G .944(or which the)203.606 688.8 R F1(op)3.444 E
F0 .944(\214eld is LOCK_GET)3.444 F 3.444(,a)-.74 G .944(nd the)365.014 688.8 R
F1(obj)3.444 E F0(,).48 E F1(loc)3.444 E(k_mode)-.2 E F0(and)3.444 E F1(loc)
3.444 E(kp)-.2 E F0 .943(\214elds are)3.443 F(4.4 Berk)72 732 Q(ele)-.1 E 2.5
(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(3)535
732 Q EP
%%Page: 4 9
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 117.9(DB_LOCK\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 117.9(anual DB_LOCK\(3\))340.17 48 R .509(represented by the ar)108
84 R .509(guments of the same name.)-.18 F .51(Note that the type of the)5.509
F/F1 10/Times-Italic@0 SF(obj)3.01 E F0(ar)3.01 E .51(gument to)-.18 F F1(loc)
3.01 E(k_g)-.2 E(et)-.1 E F0 .51(is dif-)3.01 F .765(ferent from the)108 96 R
F1(obj)3.265 E F0 .765(element found in the LOCK_REQ_T structure.)3.265 F(The)
5.765 E F1(loc)3.265 E(k_g)-.2 E(et)-.1 E F0 .765(function returns success)
3.265 F(and f)108 108 Q(ailure as described for the)-.1 E F1(loc)2.5 E(k_vec)
-.2 E F0(function.)2.5 E 1.186(The function)108 124.8 R F1(loc)3.686 E(k_put)
-.2 E F0 1.187(is a simple interf)3.687 F 1.187(ace to the)-.1 F F1(loc)3.687 E
(k_vec)-.2 E F0(functionality)3.687 E 3.687(,a)-.65 G 1.187(nd is equi)416.515
124.8 R -.25(va)-.25 G 1.187(lent to calling the).25 F F1(loc)108 136.8 Q
(k_vec)-.2 E F0 .374(function with a single element)2.874 F F1(list)2.874 E F0
(array)2.873 E 2.873(,f)-.65 G .373(or which the)314.82 136.8 R F1(op)2.873 E
F0 .373(\214eld is LOCK_PUT and the)2.873 F F1(loc)2.873 E(kp)-.2 E F0(\214eld)
2.873 E .631(is represented by the ar)108 148.8 R .631
(gument of the same name.)-.18 F .632(Note that the type of the)5.632 F F1(loc)
3.132 E(kp)-.2 E F0(ar)3.132 E .632(gument to)-.18 F F1(loc)3.132 E(k_put)-.2 E
F0(is)3.132 E(dif)108 160.8 Q .275(ferent from the)-.25 F F1(loc)2.775 E(kp)-.2
E F0 .274(element found in the LOCK_REQ_T structure.)2.775 F(The)5.274 E F1
(loc)2.774 E(k_put)-.2 E F0 .274(function returns suc-)2.774 F(cess and f)108
172.8 Q(ailure as described for the)-.1 E F1(loc)2.5 E(k_vec)-.2 E F0
(function.)2.5 E .013(The function)108 189.6 R F1(loc)2.513 E(k_close)-.2 E F0
.013(disassociates the calling process from the lock table)2.513 F F1(lt)2.513
E F0 2.513(,a).68 G .013(fter releasing all locks held)431.636 189.6 R .228
(or requested by that process.)108 201.6 R .228(The function)5.228 F F1(loc)
2.728 E(k_close)-.2 E F0 .228(returns -1 on f)2.728 F .227(ailure, setting)-.1
F F1(errno)2.727 E F0 2.727(,a).18 G .227(nd 0 on success.)474.329 201.6 R .433
(The function)108 218.4 R F1(loc)2.933 E(k_unlink)-.2 E F0(destro)2.933 E .433
(ys the lock table identi\214ed by the directory)-.1 F F1(path)2.933 E F0 2.933
(,r).28 G(emo)440.636 218.4 Q .433(ving all \214les used to)-.15 F 1.005
(implement the lock table.)108 230.4 R 1.005(\(The directory)6.005 F F1(path)
3.505 E F0 1.005(is not remo)3.505 F -.15(ve)-.15 G 3.505(d.\) If).15 F 1.005
(there are processes which ha)3.505 F 1.305 -.15(ve c)-.2 H(alled).15 E F1(loc)
108 242.4 Q(k_open)-.2 E F0 .869(without calling)3.369 F F1(loc)3.369 E
(k_close)-.2 E F0 .869
(\(i.e., there are processes currently using the lock table\),)3.369 F F1(loc)
3.37 E(k_unlink)-.2 E F0 .409(will f)108 254.4 R .408
(ail without further action, unless the force \215ag is set, in which case)-.1
F F1(loc)2.908 E(k_unlink)-.2 E F0 .408(will attempt to delete)2.908 F .807
(the lock table \214les re)108 266.4 R -.05(ga)-.15 G .808(rdless of an).05 F
3.308(yp)-.15 G .808(rocesses still using the lock table.)264.662 266.4 R(An)
5.808 E 3.308(ya)-.15 G .808(ccesses to a remo)433.208 266.4 R -.15(ve)-.15 G
3.308(dl).15 G(ock)525.56 266.4 Q .046(table will lik)108 278.4 R .046
(ely result in une)-.1 F .045(xpected beha)-.15 F(vior)-.2 E 5.045(.T)-.55 G
.045(he function)304.24 278.4 R F1(loc)2.545 E(k_unlink)-.2 E F0 .045
(returns -1 on f)2.545 F .045(ailure, setting)-.1 F F1(errno)2.545 E F0(,).18 E
(and 0 on success.)108 290.4 Q .798(In the case of catastrophic or system f)108
307.2 R .798(ailure, it is possible to clean up a lock table by remo)-.1 F .799
(ving all of the)-.15 F .38(\214les in the directory speci\214ed to the)108
319.2 R F1(loc)2.88 E(k_cr)-.2 E(eate)-.37 E F0 .379
(function, as lock table \214les are ne)2.88 F -.15(ve)-.25 G 2.879(rc).15 G
.379(reated in an)461.543 319.2 R 2.879(yd)-.15 G(irec-)521.68 319.2 Q
(tory other than the one speci\214ed to)108 331.2 Q F1(loc)2.5 E(k_cr)-.2 E
(eate)-.37 E F0(.).18 E/F2 9/Times-Bold@0 SF(ERR)72 348 Q(ORS)-.27 E F0(The)108
360 Q F1(loc)4.158 E(k_cr)-.2 E(eate)-.37 E F0 1.658(function may f)4.158 F
1.658(ail and set)-.1 F F1(errno)4.158 E F0 1.658(for an)4.158 F 4.158(yo)-.15
G 4.158(ft)353.71 360 S 1.659(he errors speci\214ed for the library routines)
363.978 360 R F1(mmap)108 372 Q F0(\(2\),).19 E F1(open)2.5 E F0(\(2\) and).24
E F1(malloc)2.5 E F0(\(3\).).31 E(The)108 388.8 Q F1(loc)4.692 E(k_open)-.2 E
F0 2.192(function may f)4.692 F 2.192(ail and set)-.1 F F1(errno)4.692 E F0
2.192(for an)4.692 F 4.692(yo)-.15 G 4.692(ft)353.87 388.8 S 2.191
(he errors speci\214ed for the library routine)364.672 388.8 R F1(mmap)108
400.8 Q F0(\(2\) and).19 E F1(open)2.5 E F0(\(2\).).24 E(The)108 417.6 Q F1
(loc)2.57 E(k_close)-.2 E F0 .07(function may f)2.57 F .07(ail and set)-.1 F F1
(errno)2.57 E F0 .07(for an)2.57 F 2.57(yo)-.15 G 2.57(ft)333.76 417.6 S .07
(he errors speci\214ed for the library routine)342.44 417.6 R F1(close)2.57 E
F0(\(2\)).18 E(and)108 429.6 Q F1(munmap)2.5 E F0(\(2\).).19 E(The)108 446.4 Q
F1(loc)4.071 E(k_unlink)-.2 E F0 1.571(function may f)4.071 F 1.571
(ail and set)-.1 F F1(errno)4.071 E F0 1.571(for an)4.071 F 4.071(yo)-.15 G
4.07(ft)353.22 446.4 S 1.57(he errors speci\214ed for the library function)
363.4 446.4 R F1(unlink)108 458.4 Q F0(\(2\) or the follo).67 E(wing:)-.25 E
([EB)108 475.2 Q(USY])-.1 E(The lock table w)133 487.2 Q
(as in use and the force \215ag w)-.1 E(as not set.)-.1 E F2(SEE ALSO)72 504 Q
F1(db_btr)108 516 Q(ee)-.37 E F0(\(3\),).18 E F1(db_hash)2.5 E F0(\(3\),).28 E
F1(db_lo)2.5 E(g)-.1 E F0(\(3\),).22 E F1(db_mpool)2.5 E F0(\(3\),).51 E F1
(db_open)2.5 E F0(\(3\),).24 E F1(db_r)2.5 E(ecno)-.37 E F0(\(3\),).18 E F1
(db_txn)2.5 E F0(\(3\)).24 E F2 -.09(BU)72 532.8 S(GS).09 E F0(The)108 544.8 Q
F1(maxloc)2.656 E(ks)-.2 E F0 .156
(parameter is a kluge, and should be deleted in f)2.656 F -.2(avo)-.1 G 2.657
(ro).2 G 2.657(fd)381.055 544.8 S .157(ynamically e)392.042 544.8 R .157
(xpanding the lock table.)-.15 F(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G
(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(4)535 732 Q EP
%%Page: 1 10
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_LOG\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_LOG\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72
84 S(ME).18 E F0(db_log \255 log-manager access method)108 96 Q F1(DESCRIPTION)
72 112.8 Q F0 .486(The DB library is a f)108 124.8 R .485
(amily of groups of functions that pro)-.1 F .485
(vides a modular programming interf)-.15 F .485(ace to trans-)-.1 F .822
(actions and record-oriented \214le access.)108 136.8 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 148.8 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 148.8 S .258(ell as v)237.082 148.8 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 148.8 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 148.8 S .258
(he functional groups \(e.g.)436.746 148.8 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 160.8 R .306(tional groups are e)108 172.8 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 172.8 Q .245(description of transactions, see)108 184.8 R/F2 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F2(db_open)2.745 E
F0(\(3\)).24 E .308(and then the indi)108 196.8 R .308
(vidual access method manual pages:)-.25 F F2(db_btr)2.807 E(ee)-.37 E F0
(\(3\),).18 E F2(db_hash)2.807 E F0(\(3\),).28 E F2(db_lo)2.807 E(g)-.1 E F0
.307(\(3\) and).22 F F2(db_r)2.807 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
208.8 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
208.8 R 3.635(,s)-.4 G(ee)307.32 208.8 Q F2(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 220.8 Q 2.5(,s)-.4 G(ee)171.2 220.8 Q F2(db_mpool)2.5 E F0
(\(3\).).51 E
(This manual page describes speci\214c details of the logging access method.)
108 237.6 Q .03(These functions pro)108 254.4 R .03
(vide a general-purpose logging f)-.15 F .03(acility suf)-.1 F .03
(\214cient for transaction management.)-.25 F .03(Logs can)5.03 F
(be shared by multiple processes.)108 266.4 Q 3.717(Al)108 283.2 S 1.217
(og is represented by the directory)121.717 283.2 R(,)-.65 E F2 1.217
(not the \214le)3.717 F F0 3.717(,n).18 G 1.217(amed by the \214rst ar)323
283.2 R 1.218(gument to)-.18 F F2(db_open)3.718 E F0 3.718(\(3\). The).24 F
(\214rst)3.718 E(ar)108 295.2 Q .26
(gument must be non-NULL, and the directory must already e)-.18 F(xist)-.15 E
F2(db_open)2.76 E F0 .26(is called.)2.76 F .26(In that directory)5.26 F 2.76
(,t)-.65 G(he)530.56 295.2 Q 3.448
(log is stored in one or more \214les named in the format `)108 307.2 R
(`log.YYYY)-.74 E(.MM.DD.HH.MM.SS')-1.29 E 3.448(', where)-.74 F -.74(``)108
319.2 S(YYYY).74 E(.MM.DD.HH.SS')-1.29 E 2.507('i)-.74 G 2.507(st)220.497 319.2
S .007(he approximate creation time of the log \214le, and is guaranteed to be\
 unique in)229.674 319.2 R(the directory)108 331.2 Q(.)-.65 E .465
(The group of the created \214les is based on the system and directory def)108
348 R .466(aults, and is not further speci\214ed by)-.1 F .073
(the log access method.)108 360 R .072(All \214les are created with the)5.073 F
F2(mode)2.572 E F0 .072(speci\214ed to)2.572 F F2(db_open)2.572 E F0 2.572(,\()
.24 G .072(as described in)435.584 360 R F2 -.15(ch)2.572 G(mod).15 E F0
(\(2\)\)).77 E(and modi\214ed by the process' umask v)108 372 Q(alue \(see)-.25
E F2(umask)2.5 E F0(\(2\)\).).67 E(The)108 388.8 Q F2<8d61>2.5 E(gs)-.1 E F0
(ar)2.5 E(gument to)-.18 E F2(db_open)2.5 E F0(must be 0 for the)2.5 E F2
(db_lo)2.5 E(g)-.1 E F0(access method.)2.5 E F1 -.495(AC)72 405.6 S
(CESS METHOD SPECIFIC INFORMA).495 E(TION)-.855 E F0 .571
(The log access method speci\214c data structure pro)108 417.6 R .571(vided to)
-.15 F F2(db_open)3.071 E F0 .572(is typedef)3.071 F 1.572 -.5('d a).55 H .572
(nd named LOGINFO.).5 F(A)5.572 E(LOGINFO structure has at least the follo)108
429.6 Q(wing \214elds, which may be initialized before calling)-.25 E F2
(db_open)2.5 E F0(:).24 E(of)108 446.4 Q(f_t max_\214le_size;)-.25 E 1.585
(The maximum size of a single \214le in the log.)133 458.4 R 1.584
(If not speci\214ed, the maximum size def)6.584 F 1.584(aults to an)-.1 F
(implementation-speci\214c v)133 470.4 Q(alue.)-.25 E(int lorder;)108 487.2 Q
.65(The byte order for inte)133 499.2 R .65
(gers in the stored database metadata.)-.15 F .65
(The number should represent the order)5.65 F .749(as an inte)133 511.2 R .749
(ger; for e)-.15 F .749(xample, big endian order w)-.15 F .749
(ould be the number 4,321.)-.1 F(If)5.749 E F2(lor)3.249 E(der)-.37 E F0 .749
(is 0 \(no order is)3.249 F(speci\214ed\) the current host order is used.)133
523.2 Q 1.284(If the log already e)108 540 R 1.284(xists, the v)-.15 F 1.285(a\
lues speci\214ed for the parameters max_\214le_size and lorder are ignored in)
-.25 F -.1(fa)108 552 S -.2(vo)-.1 G 2.5(ro).2 G 2.5(ft)136.1 552 S(he v)144.71
552 Q(alues used when the log w)-.25 E(as created.)-.1 E F1(DB OPERA)72 568.8 Q
(TIONS)-.855 E F0 .687(The data part of the k)108 580.8 R -.15(ey)-.1 G .686(/\
data pair used by the log access method is the same as for other access method\
s.).15 F .837(The k)108 592.8 R 1.137 -.15(ey i)-.1 H 3.337(sd).15 G(if)159.421
592.8 Q 3.337(ferent. Each)-.25 F .837(log record is identi\214ed by a log seq\
uence number \(LSN\), which is stored in a)3.337 F(DBT)108 604.8 Q 2.702(,a)
-.74 G .202(nd which is used as the)136.902 604.8 R F2 -.1(ke)2.702 G(y)-.2 E
F0 .202(for all log functions that tak)2.702 F(e)-.1 E F2 -.1(ke)2.701 G(y)-.2
E F0(ar)2.701 E 2.701(guments. Applications)-.18 F .201(cannot create)2.701 F
(LSN')108 616.8 Q .539(s, and all LSN')-.55 F 3.039(sp)-.55 G(ro)203.216 616.8
Q .539(vided to functions as ar)-.15 F .539(guments must \214rst be retrie)-.18
F -.15(ve)-.25 G 3.04(du).15 G .54(sing the)440.37 616.8 R F2(put)3.04 E F0(or)
3.04 E F2(seq)3.04 E F0(func-)3.04 E 2.783(tions. T)108 628.8 R 2.783(op)-.8 G
(ro)153.326 628.8 Q .283(vide a distinguished v)-.15 F .282
(alue for applications, it is guaranteed that no v)-.25 F .282(alid LSN will e)
-.25 F -.15(ve)-.25 G 2.782(rh).15 G -2.25 -.2(av e)519.248 628.8 T(a)2.982 E
(size of 0.)108 640.8 Q(Applications can compare LSN')108 657.6 Q 2.5(su)-.55 G
(sing the)247.98 657.6 Q F2(lo)2.5 E(g_lsn_compar)-.1 E(e)-.37 E F0
(function \(see belo)2.5 E(w\).)-.25 E .429(Applications can associate LSN')108
674.4 R 2.929(sw)-.55 G .429(ith speci\214c log \214les.)253.586 674.4 R .429
(The function)5.429 F F2(lo)2.929 E(g_lsn_\214le)-.1 E F0 .43(\(see belo)2.93 F
.43(w\), returns the)-.25 F .214
(name of the log \214le containing the record with a speci\214ed LSN.)108 686.4
R .214(\(The mapping of LSN to \214le is needed for)5.214 F(4.4 Berk)72 732 Q
(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(3, 1995)
2.5 E(1)535 732 Q EP
%%Page: 2 11
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_LOG\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_LOG\(3\))340.17 48 R .397(database administration.)
108 84 R -.15(Fo)5.397 G 2.897(re).15 G .398
(xample, a transaction manager typically records the earliest LSN needed for)
231.931 84 R .519(restart, and the database administrator may w)108 96 R .519
(ant to archi)-.1 F .819 -.15(ve l)-.25 H .519(og \214les to tape when the).15
F 3.018(yc)-.15 G .518(ontain only LSN')465.624 96 R(s)-.55 E
(before the earliest one needed for restart.\))108 108 Q
(Applications can truncate the log \214le up to a speci\214c LSN using the)108
124.8 Q/F1 10/Times-Italic@0 SF(lo)2.5 E(g_trunc)-.1 E F0(function \(see belo)
2.5 E(w\).)-.25 E .221(The functions returned by)108 141.6 R F1(db_open)2.721 E
F0 .221(for the log access method are as described in)2.721 F F1(db_open)2.721
E F0 2.722(,w).24 G .222(ith the follo)482.586 141.6 R(w-)-.25 E(ing e)108
153.6 Q(xceptions and additions:)-.15 E 5.28(type The)108 170.4 R
(type is DB_LOG.)2.5 E 10.28(del The)108 187.2 R F1(del)3.505 E F0 1.005
(function al)3.505 F -.1(wa)-.1 G 1.005
(ys returns an error for the log-manager access method, setting).1 F F1(errno)
3.504 E F0 1.004(to EIN-)3.504 F -1.35(VA)133 199.2 S(L.)1.35 E
(int \(*log_\215ush\)\(const DB *db, const DBT *lsn\);)108 216 Q(The)133 228 Q
F1(lo)2.866 E(g_\215ush)-.1 E F0 .367
(function \215ushes the log up to and including the log record)2.866 F F1(lsn)
2.867 E F0 5.367(.T).24 G .367(he function)454.926 228 R F1(lo)2.867 E
(g_\215ush)-.1 E F0(returns -1 on f)133 240 Q(ailure, setting)-.1 E F1(errno)
2.5 E F0 2.5(,a).18 G(nd 0 on success.)278.61 240 Q
(int \(*log_lsn_compare\)\(const DB *,)108 256.8 Q .255
(const DBT *lsn1, const DBT *lsn2\); A pointer to a function which is pro)183
268.8 R .255(vided to permit)-.15 F .312(applications to compare LSN')133 280.8
R 2.812(s. The)-.55 F F1(lo)2.812 E(g_lsn_compar)-.1 E(e)-.37 E F0 .312
(function returns an inte)2.812 F .313(ger less than, equal to,)-.15 F .058
(or greater than zero if the \214rst LSN is considered to be respecti)133 292.8
R -.15(ve)-.25 G .058(ly less than, equal to, or greater than).15 F
(the second LSN.)133 304.8 Q(int \(*log_lsn_\214le\)\(const DB *db,)108 321.6 Q
(const DBT *lsn, char *name\);)183 333.6 Q(The)133 345.6 Q F1(lo)3.21 E
(g_lsn_\214le)-.1 E F0 .71
(function stores a pointer to the name of the \214le containing)3.21 F F1(lsn)
3.211 E F0 .711(in the address refer)3.211 F(-)-.2 E .293(enced by)133 357.6 R
F1(name)2.793 E(.)-.15 E F0 .293(This pointer is to an internal static object,\
 and subsequent calls to the same function)5.293 F
(will modify the same object.)133 369.6 Q(The function)133 386.4 Q F1(lo)2.5 E
(g_lsn_\214le)-.1 E F0(returns -1 on f)2.5 E(ailure, setting)-.1 E F1(errno)2.5
E F0 2.5(,a).18 G(nd 0 on success.)381.56 386.4 Q
(int \(*log_unlink\)\(const char *path, int force\);)108 403.2 Q(The)133 415.2
Q F1(lo)3.275 E(g_unlink)-.1 E F0 .775(function destro)3.275 F .775
(ys the log represented by)-.1 F F1(path)3.275 E F0 5.775(.I).28 G 3.275(ft)
394.745 415.2 S(he)404.13 415.2 Q F1(for)3.275 E(ce)-.37 E F0 .776
(parameter is not set to 1)3.275 F .725
(and there are other processes using the log, then)133 427.2 R F1(lo)3.224 E
(g_unlink)-.1 E F0 .724(will return -1, setting)3.224 F F1(errno)3.224 E F0
.724(to EB)3.224 F(USY)-.1 E(.)-1.29 E(If)133 439.2 Q F1(for)2.831 E .331
(ce is not set or ther)-.37 F 2.831(ea)-.37 G 1.071 -.37(re n)244.287 439.2 T
2.831(op).37 G -.45(ro)272.909 439.2 S .331(cesses using the lo).45 F .532 -.1
(g, t)-.1 H .332(hen all \214les).1 F F0 .332(used by the log are destro)2.832
F(yed.)-.1 E F1(lo)133 451.2 Q(g_unlink)-.1 E F0(will return -1 on f)2.5 E
(ailure, setting)-.1 E F1(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)337.96
451.2 Q(int \(*log_trunc\)\(const DB *db, const DBT *lsn\);)108 468 Q(The)133
480 Q F1(lo)2.601 E(g_trunc)-.1 E F0 .101
(function truncates the log up to an LSN which is less than)2.601 F F1(lsn)2.6
E F0 5.1(.T).24 G .1(he function)453.24 480 R F1(lo)2.6 E(g_trunc)-.1 E F0
(returns -1 on f)133 492 Q(ailure, setting)-.1 E F1(errno)2.5 E F0 2.5(,a).18 G
(nd 0 on success.)278.61 492 Q 9.72(put A)108 508.8 R .339
(log record containing)2.839 F F1(data)2.839 E F0 .339(is appended to the log.)
2.839 F(Unlik)5.339 E 2.84(et)-.1 G(he)382.44 508.8 Q F1(put)2.84 E F0 .34
(functions for other access meth-)2.84 F .789(ods, the k)133 520.8 R 1.089 -.15
(ey p)-.1 H .788(arameter is not initialized by the application, instead, the \
LSN assigned to the data is).15 F(returned in the)133 532.8 Q F1 -.1(ke)2.5 G
(y)-.2 E F0(parameter)2.5 E(.)-.55 E 1.157(The caller is responsible for pro)
133 549.6 R 1.157(viding an)-.15 F 3.657(yn)-.15 G 1.157(ecessary structure to)
318.267 549.6 R F1 1.157(data .)3.657 F F0(\(F)6.157 E 1.157(or e)-.15 F 1.157
(xample, in a write-)-.15 F .267
(ahead logging protocol, the application must understand what part of)133 561.6
R F1(data)2.767 E F0 .266(is an operation code, what)2.766 F .622
(part is redo information, and what part is undo information.)133 573.6 R .622
(In addition, most transaction managers)5.622 F .985(will store in)133 585.6 R
F1(data)3.485 E F0 .985(the LSN of the pre)3.485 F .984
(vious log record for the same transaction, to support chaining)-.25 F
(back through the transaction')133 597.6 Q 2.5(sl)-.55 G
(og records during undo.\))258.54 597.6 Q(The parameter)133 614.4 Q F1<8d61>2.5
E(g)-.1 E F0(must be set to 0 or e)2.5 E(xactly one of the follo)-.15 E(wing v)
-.25 E(alues:)-.25 E(R_CHECKPOINT)133 631.2 Q .5(Specify the k)158 643.2 R -.15
(ey)-.1 G .5(/data pair of the current call as the one to be returned when the)
.15 F F1(seq)3 E F0 .5(function is)3 F(ne)158 655.2 Q
(xt called with the R_CHECKPOINT \215ag.)-.15 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5
(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(3, 1995)2.5 E(2)535
732 Q EP
%%Page: 3 12
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_LOG\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_LOG\(3\))340.17 48 R(R_FLUSH)133 84 Q
(Flush immediately \(ignoring an)158 96 Q 2.5(yp)-.15 G
(ossibility for group commit\).)296.74 96 Q 9.17(seq The)108 112.8 R/F1 10
/Times-Italic@0 SF(seq)2.5 E F0(function tak)2.5 E(es the follo)-.1 E
(wing additional \215ag:)-.25 E(R_CHECKPOINT)133 129.6 Q .184(The last k)158
141.6 R -.15(ey)-.1 G .184(/data pair stored by the).15 F F1(put)2.684 E F0
.183(function \(using the R_CHECKPOINT \215ag\) is returned,)2.684 F .216
(and the cursor is set or initialized to reference it.)158 153.6 R .216(The e)
5.216 F .216(xpected use of this \215ag is during restart)-.15 F .801
(and to determine what part of the log must be a)158 165.6 R -.25(va)-.2 G .801
(ilable for restart.).25 F .801(Therefore, the log record)5.801 F(retrie)158
177.6 Q -.15(ve)-.25 G 3.352(dw).15 G .853
(ith R_CHECKPOINT should contain all the information that the transaction man-)
203.712 177.6 R(ager will need for this purpose.)158 189.6 Q 4.17(sync The)108
206.4 R F1(sync)3.135 E F0 .635(function al)3.135 F -.1(wa)-.1 G .635
(ys returns an error for the log-manager access method, setting).1 F F1(errno)
3.134 E F0 .634(to EIN-)3.134 F -1.35(VA)133 218.4 S(L.)1.35 E/F2 9
/Times-Bold@0 SF(SEE ALSO)72 235.2 Q F1(db_btr)108 247.2 Q(ee)-.37 E F0(\(3\),)
.18 E F1(db_hash)2.5 E F0(\(3\),).28 E F1(db_loc)2.5 E(k)-.2 E F0(\(3\),).67 E
F1(db_mpool)2.5 E F0(\(3\),).51 E F1(db_open)2.5 E F0(\(3\),).24 E F1(db_r)2.5
E(ecno)-.37 E F0(\(3\),).18 E F1(db_txn)2.5 E F0(\(3\)).24 E(4.4 Berk)72 732 Q
(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(3, 1995)
2.5 E(3)535 732 Q EP
%%Page: 1 13
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 110.12(DB_MPOOL\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 110.12(anual DB_MPOOL\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)
72 84 S(ME).18 E F0(db_mpool \255 general purpose shared memory b)108 96 Q(uf)
-.2 E(fer pool)-.25 E F1(SYNOPSIS)72 112.8 Q/F2 10/Times-Bold@0 SF
(#include <db)108 124.8 Q(.h>)-.4 E(#include <mpool.h>)108 136.8 Q(int)108
160.8 Q(mpool_cr)108 172.8 Q
(eate\(char *path, mode_t mode, size_t cachesize, u_long \215ags\);)-.18 E
(MPOOL *)108 196.8 Q(mpool_open\(char *path\);)108 208.8 Q(int)108 232.8 Q
(mpool_close\(MPOOL *mp\);)108 244.8 Q(MPOOLFILE *)108 268.8 Q(mpool_f)108
280.8 Q(open\(MPOOL *mp, char *path, size_t pagesize, v)-.25 E(oid *pgcookie,)
-.1 E(int \(*pgin\)\(MPOOLFILE *mpf)158 292.8 Q(,)-.15 E(pgno_t pgno, v)188
304.8 Q(oid *pgaddr)-.1 E 2.5(,v)-.92 G(oid *pgcookie\),)311.91 304.8 Q
(int \(*pgout\)\(MPOOLFILE *mpf)158 316.8 Q(,)-.15 E(pgno_t pgno, v)188 328.8 Q
(oid *pgaddr)-.1 E 2.5(,v)-.92 G(oid *pgcookie\);)311.91 328.8 Q(int)108 352.8
Q(mpool_fclose\(MPOOLFILE *mpf\);)108 364.8 Q -.1(vo)108 388.8 S(id *).1 E
(mpool_get\(MPOOLFILE *mpf)108 400.8 Q 2.5(,p)-.15 G(gno_t *pgnoaddr)252.02
400.8 Q 2.5(,u)-.92 G(_long \215ags,)334.73 400.8 Q
(int \(*callback\)\(MPOOLFILE *mpf)158 412.8 Q 2.5(,p)-.15 G(gno_t pgno\)\);)
318.97 412.8 Q(int)108 436.8 Q(mpool_put\(MPOOLFILE *mpf)108 448.8 Q 2.5(,v)
-.15 G(oid *pgaddr)253.04 448.8 Q 2.5(,u)-.92 G(_long \215ags\);)314.64 448.8 Q
(int)108 472.8 Q(mpool_sync\(MPOOLFILE *mpf\);)108 484.8 Q(int)108 508.8 Q
(mpool_unlink\(const char *path, int f)108 520.8 Q(or)-.25 E(ce\);)-.18 E -.1
(vo)108 544.8 S(id).1 E(mpool_stat\(MPOOL *mp, FILE *fp\);)108 556.8 Q F1
(DESCRIPTION)72 573.6 Q F0 .485(The DB library is a f)108 585.6 R .485
(amily of groups of functions that pro)-.1 F .486
(vides a modular programming interf)-.15 F .486(ace to trans-)-.1 F .823
(actions and record-oriented \214le access.)108 597.6 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 609.6 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 609.6 S .258(ell as v)237.082 609.6 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 609.6 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 609.6 S .258
(he functional groups \(e.g.)436.746 609.6 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 621.6 R .306(tional groups are e)108 633.6 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 633.6 Q .245(description of transactions, see)108 645.6 R/F3 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F3(db_open)2.745 E
F0(\(3\)).24 E .307(and then the indi)108 657.6 R .307
(vidual access method manual pages:)-.25 F F3(db_btr)2.808 E(ee)-.37 E F0
(\(3\),).18 E F3(db_hash)2.808 E F0(\(3\),).28 E F3(db_lo)2.808 E(g)-.1 E F0
.308(\(3\) and).22 F F3(db_r)2.808 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
669.6 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
669.6 R 3.635(,s)-.4 G(ee)307.32 669.6 Q F3(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 681.6 Q 2.5(,s)-.4 G(ee)171.2 681.6 Q F3(db_mpool)2.5 E F0
(\(3\).).51 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q
99.315(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 14
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 110.12(DB_MPOOL\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 110.12(anual DB_MPOOL\(3\))340.17 48 R
(This manual page describes speci\214c details of the memory pool interf)108 84
Q(ace.)-.1 E(The)108 100.8 Q/F1 10/Times-Italic@0 SF(db_mpool)3.682 E F0 1.182
(function is the library interf)3.682 F 1.183(ace intended to pro)-.1 F 1.183
(vide general-purpose, page-oriented b)-.15 F(uf)-.2 E(fer)-.25 E .16
(management of one or more \214les.)108 112.8 R .16(While designed to w)5.16 F
.16(ork with the other DB functions, these functions are)-.1 F .604
(also useful for more general purposes.)108 124.8 R .604
(The memory pools \(MPOOL)5.604 F -.55('s)-.92 G 3.104(\)a).55 G .605
(re referred to in this document as)404.18 124.8 R .985(simply `)108 136.8 R
(`pools')-.74 E 3.485('. Pools)-.74 F .985(may be shared between processes.)
3.485 F .985(Pools are usually \214lled by pages from one or)5.985 F .673
(more \214les \(MPOOLFILE')108 148.8 R 3.173(s\). P)-.55 F .674
(ages in the pool are replaced in LR)-.15 F 3.174(U\()-.4 G .674
(least-recently-used\) order)392.318 148.8 R 3.174(,w)-.4 G .674(ith each)
507.946 148.8 R(ne)108 160.8 Q 4.243(wp)-.25 G 1.743
(age replacing the page which has been unused the longest.)133.653 160.8 R -.15
(Pa)6.742 G 1.742(ges retrie).15 F -.15(ve)-.25 G 4.242(df).15 G 1.742
(rom the pool using)459.494 160.8 R F1(mpool_g)108 172.8 Q(et)-.1 E F0 1.255
(are `)3.755 F(`pinned')-.74 E 3.755('i)-.74 G 3.755(nm)215.435 172.8 S(emory)
231.97 172.8 Q 3.755(,b)-.65 G 3.755(yd)268.125 172.8 S(ef)281.88 172.8 Q 1.256
(ault, until the)-.1 F 3.756(ya)-.15 G 1.256(re returned to the pool using the)
358.168 172.8 R F1(mpool_put)3.756 E F0(function.)108 184.8 Q .934
(The function)108 201.6 R F1(mpool_cr)3.434 E(eate)-.37 E F0 .934
(creates and initializes the memory pool identi\214ed by the)3.434 F F1(path)
3.433 E F0(directory)3.433 E 5.933(.T)-.65 G(his)528.33 201.6 Q .931
(directory must already e)108 213.6 R .931(xist when)-.15 F F1(mpool_cr)3.431 E
(eate)-.37 E F0 .931(is called.)3.431 F .932
(If the memory pool identi\214ed by)5.931 F F1(path)3.432 E F0(already)3.432 E
-.15(ex)108 225.6 S .045(ists, then).15 F F1(mpool_cr)2.545 E(eate)-.37 E F0
.045(returns success without further action.)2.545 F .045
(The \214les associated with the memory pool)5.045 F .87
(are created in the directory speci\214ed by)108 237.6 R F1(path)3.37 E F0 5.87
(.\().28 G .87(The group of the created \214les is based on the system and)
304.08 237.6 R .258(directory def)108 249.6 R .258
(aults, and is not further speci\214ed by)-.1 F F1(mpool_cr)2.758 E(eate)-.37 E
F0 2.758(.\) All).18 F .258(\214les created by)2.758 F F1(mpool_cr)2.758 E
(eate)-.37 E F0 .258(are cre-)2.758 F .048(ated with mode)108 261.6 R F1(mode)
2.548 E F0 .049(\(as described in)2.548 F F1 -.15(ch)2.549 G(mod).15 E F0 .049
(\(2\)\) and modi\214ed by the process' umask v).77 F .049(alue \(see)-.25 F F1
(umask)2.549 E F0(\(2\)\).).67 E(The)108 278.4 Q F1(cac)2.544 E(hesize)-.15 E
F0(ar)2.544 E .044(gument speci\214es the size of the pool in bytes, and shoul\
d be the size of the normal w)-.18 F(orking)-.1 E .509(set of the application \
with some small amount of additional memory for unusual situations.)108 290.4 R
.509(If the number)5.509 F .362(of bytes currently `)108 302.4 R(`pinned')-.74
E 2.862('i)-.74 G 2.862(nm)226.828 302.4 S .362(emory e)242.47 302.4 R(xceeds)
-.15 E F1(cac)2.861 E(hesize)-.15 E F0 2.861(,t).18 G(he)351.734 302.4 Q F1
(db_mpool)2.861 E F0 .361(functions will attempt to allocate)2.861 F
(more memory and do not necessarily f)108 314.4 Q(ail, although the)-.1 E 2.5
(ym)-.15 G(ay suf)341.61 314.4 Q(fer performance de)-.25 E(gradation.)-.15 E
(The)108 331.2 Q F1<8d61>2.5 E(gs)-.1 E F0(ar)2.5 E(gument is set by)-.18 E F1
(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)272.73 331.2 S(he follo)281.34
331.2 Q(wing v)-.25 E(alues:)-.25 E(MPOOL_PRIV)108 348 Q -1.11(AT)-1.35 G(E)
1.11 E(The pool is not shared by other processes or threads, so no locking of \
pool resources is required.)144 360 Q .115(The function)108 376.8 R F1
(mpool_open)2.615 E F0 .115
(returns a pointer to the memory pool identi\214ed by)2.615 F F1(path)2.615 E
F0 2.615(,w).28 G .115(hich must ha)447.525 376.8 R .415 -.15(ve a)-.2 H
(lready).15 E .036(been created by a call to)108 388.8 R F1(mpool_cr)2.536 E
(eate)-.37 E F0 5.036(.T).18 G .036(he process must ha)276.074 388.8 R .336
-.15(ve p)-.2 H .036(ermission to read and write \214les with o).15 F(wn-)-.25
E 1.157(ers, groups and permissions as described for)108 400.8 R F1(mpool_cr)
3.657 E(eate)-.37 E F0 6.157(.T).18 G(he)365.075 400.8 Q F1(mpool_open)3.657 E
F0 1.157(function returns NULL on)3.657 F -.1(fa)108 412.8 S(ilure, setting).1
E F1(errno)2.5 E F0(.).18 E(The)108 429.6 Q F1(mpool_close)6.383 E F0 3.883
(function closes the pool indicated by the MPOOL pointer)6.383 F F1(mp)6.383 E
F0 6.383(,a).19 G 6.382(sr)480.026 429.6 S 3.882(eturned by)493.628 429.6 R F1
(mpool_open)108 441.6 Q F0 5.047(.T).24 G .047(his function does)171.337 441.6
R/F2 10/Times-Bold@0 SF(not)2.547 E F0 .047(imply a call to)2.547 F F1
(mpool_sync)2.547 E F0 .047(\(or to)2.547 F F1(mpool_fclose)2.547 E F0 2.547
(\)i).18 G .047(.e. no pages are writ-)455.951 441.6 R .404
(ten to the source \214le as as a result of calling)108 453.6 R F1(mpool_close)
2.904 E F0 5.404(.T).18 G .404(he function)354.658 453.6 R F1(mpool_close)2.904
E F0 .403(returns -1 on f)2.904 F(ailure,)-.1 E(setting)108 465.6 Q F1(errno)
2.5 E F0 2.5(,a).18 G(nd 0 on success.)169.01 465.6 Q .827(The function)108
482.4 R F1(mpool_fopen)3.327 E F0 .827(opens a \214le for b)3.327 F(uf)-.2 E
.828(fering in the pool speci\214ed by the MPOOL ar)-.25 F 3.328(gument. The)
-.18 F F1(path)108 494.4 Q F0(ar)2.85 E .349
(gument is the name of the \214le to be opened.)-.18 F(The)5.349 E F1(pa)2.849
E -.1(ge)-.1 G(size).1 E F0(ar)2.849 E .349
(gument is the size, in bytes, of the unit)-.18 F .738(of transfer between the\
 application and the pool, although not necessarily the unit of transfer betwe\
en the)108 506.4 R .12(pool and the source \214le.)108 518.4 R .12
(Applications not kno)5.12 F .12
(wing the page size of the source \214le should retrie)-.25 F .42 -.15(ve t)
-.25 H .12(he meta-).15 F .234(data from the \214le using a page size that is \
correct for the metadata, then close and reopen the \214le, or)108 530.4 R
2.735(,o)-.4 G(ther)521.32 530.4 Q(-)-.2 E
(wise determine the page size before calling)108 542.4 Q F1(mpool_fopen)2.5 E
F0(.).24 E .416(If the)108 559.2 R F1(pgin)2.916 E F0 .416(function is speci\
\214ed, it is called each time a page is read into the memory pool from the so\
urce)2.916 F 2.835(\214le. If)108 571.2 R(the)2.835 E F1(pgout)2.835 E F0 .336
(function is speci\214ed, it is called each time a page is written to the sour\
ce \214le.)2.835 F .336(Both func-)5.336 F .834
(tions are called with the MPOOLFILE pointer returned from)108 583.2 R F1
(mpool_fopen)3.333 E F0 3.333(,t).24 G .833(he page number)421.815 583.2 R
3.333(,ap)-.4 G .833(ointer to)505.557 583.2 R .014
(the page being read or written, and the ar)108 595.2 R(gument)-.18 E F1
(pgcookie)2.515 E F0 5.015(.I).18 G 2.515(fe)351.695 595.2 S .015
(ither function f)361.98 595.2 R .015(ails, it should return non-zero)-.1 F
(and set)108 607.2 Q F1(errno)2.5 E F0 2.5(,i).18 G 2.5(nw)168.73 607.2 S
(hich case the)183.45 607.2 Q F1(db_mpool)2.5 E F0
(function calling it will also f)2.5 E(ail, lea)-.1 E(ving)-.2 E F1(errno)2.5 E
F0(intact.)2.5 E(The)108 624 Q F1(mpool_fclose)2.705 E F0 .204
(function closes the source \214le indicated by the MPOOLFILE pointer)2.705 F
F1(mpf)2.704 E F0 5.204(.T)1.96 G .204(his function)492.296 624 R(does)108 636
Q F2(not)3.615 E F0 1.115(imply a call to)3.615 F F1(mpool_sync)3.615 E F0
3.615(,i).31 G 1.115
(.e. no pages are written to the source \214le as as a result of calling)
268.885 636 R F1(mpool_fclose)108 648 Q F0 5(.T).18 G(he function)175.12 648 Q
F1(mpool_fclose)2.5 E F0(returns -1 on f)2.5 E(ailure, setting)-.1 E F1(errno)
2.5 E F0 2.5(,a).18 G(nd 0 on success.)424.33 648 Q .019(The function)108 664.8
R F1(mpool_g)2.519 E(et)-.1 E F0 .019
(returns a pointer to the page with the page number speci\214ed by)2.519 F F1
(pgnoaddr)2.518 E F0 2.518(,f).73 G .018(rom the)509.152 664.8 R .986
(source \214le speci\214ed by the MPOOLFILE pointer)108 676.8 R F1(mpf)3.486 E
F0 5.986(.I)1.96 G 3.486(ft)342.268 676.8 S .987(he page does not e)351.864
676.8 R .987(xist or cannot be retrie)-.15 F -.15(ve)-.25 G(d,).15 E F1
(mpool_g)108 688.8 Q(et)-.1 E F0(returns NULL and sets errno.)2.5 E(4.4 Berk)72
732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F
(1, 1995)2.5 E(2)535 732 Q EP
%%Page: 3 15
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 110.12(DB_MPOOL\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 110.12(anual DB_MPOOL\(3\))340.17 48 R(The)108 84 Q/F1 10
/Times-Italic@0 SF<8d61>2.5 E(gs)-.1 E F0(ar)2.5 E(gument is set by)-.18 E F1
(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)272.73 84 S(he follo)281.34 84
Q(wing v)-.25 E(alues:)-.25 E(MPOOL_CALLB)108 100.8 Q -.4(AC)-.35 G(K).4 E 1.04
(After the page number has been determined, b)133 112.8 R 1.04(ut before an)-.2
F 3.54(yo)-.15 G 1.04(ther process or thread can access the)388.26 112.8 R .471
(page, the function speci\214ed by the)133 124.8 R F1(callbac)2.971 E(k)-.2 E
F0(ar)2.971 E .471(gument is called.)-.18 F .471(If the function f)5.471 F .472
(ails, it should return)-.1 F 1.11(non-zero and set)133 136.8 R F1(errno)3.61 E
F0 3.61(,i).18 G 3.61(nw)236.21 136.8 S 1.11(hich case)252.04 136.8 R F1
(mpool_g)3.61 E(et)-.1 E F0 1.11(will also f)3.61 F 1.11(ail, lea)-.1 F(ving)
-.2 E F1(errno)3.61 E F0 3.61(intact. The)3.61 F F1(callbac)3.61 E(k)-.2 E F0
1.012(function is called with the MPOOLFILE pointer returned from)133 148.8 R
F1(mpool_fopen)3.512 E F0 1.013(and the page number)3.513 F(.)-.55 E .228
(This functionality is commonly used when page locking is required, b)133 160.8
R .227(ut the page number of the page)-.2 F(being retrie)133 172.8 Q -.15(ve)
-.25 G 2.5(di).15 G 2.5(sn)198.14 172.8 S(ot kno)209.53 172.8 Q(wn.)-.25 E
(MPOOL_CREA)108 189.6 Q(TE)-1.11 E(If the speci\214ed page does not e)133 201.6
Q(xist, create it.)-.15 E(MPOOL_LAST)108 218.4 Q 2.105
(Return the last page of the source \214le and cop)133 230.4 R 4.605(yi)-.1 G
2.106(ts page number to the location referenced by)347.25 230.4 R F1(pgnoaddr)
133 242.4 Q F0(.).73 E(MPOOL_NEW)108 259.2 Q(Create a ne)133 271.2 Q 2.5(wp)
-.25 G(age in the \214le and cop)192.45 271.2 Q 2.5(yi)-.1 G
(ts page number to the location referenced by)290.67 271.2 Q F1(pgnoaddr)2.5 E
F0(.).73 E(MPOOL_NOPIN)108 288 Q(Don')133 300 Q 2.918(tp)-.18 G .418
(in the page into memory)164.068 300 R 5.418(.\()-.65 G .417
(This \215ag is intended for deb)274.108 300 R .417(ugging purposes, when it')
-.2 F 2.917(so)-.55 G .417(ften use-)504.873 300 R .972(ful to e)133 312 R .972
(xamine pages which are currently held by other parts of the application.)-.15
F -.15(Pa)5.973 G .973(ges retrie).15 F -.15(ve)-.25 G 3.473(di).15 G(n)535 312
Q .529(this manner don')133 324 R 3.029(tn)-.18 G .528
(eed to be returned to the memory pool, i.e. the)212.457 324 R 3.028(ys)-.15 G
(hould)413.95 324 Q/F2 10/Times-Bold@0 SF(not)3.028 E F0 .528
(be speci\214ed as ar)3.028 F(gu-)-.18 E(ments to the)133 336 Q F1(mpool_put)
2.5 E F0(routine.\))2.5 E(Created pages ha)108 352.8 Q .3 -.15(ve a)-.2 H
(ll their bytes set to 0.).15 E 2.078(All pages returned by)108 369.6 R F1
(mpool_g)4.578 E(et)-.1 E F0 2.079
(\(unless the MPOOL_NOPIN \215ag is speci\214ed\), will be retained \(i.e.)
4.578 F -.74(``)108 381.6 S(pinned').74 E
('\) in the pool until a subsequent call to)-.74 E F1(mpool_put)2.5 E F0(.).68
E .077(The function)108 398.4 R F1(mpool_put)2.577 E F0 .076
(indicates that the page referenced by)2.577 F F1(pgaddr)2.576 E F0 .076
(can be e)2.576 F .076(victed from the pool.)-.25 F F1(Pgaddr)5.076 E F0
(must be an address pre)108 410.4 Q(viously returned by)-.25 E F1(mpool_g)2.5 E
(et)-.1 E F0(.).68 E(The \215ag v)108 427.2 Q(alue is speci\214ed by)-.25 E F1
(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)277.2 427.2 S(he follo)285.81
427.2 Q(wing v)-.25 E(alues:)-.25 E(MPOOL_DIR)108 444 Q(TY)-.6 E .052(The page\
 has been modi\214ed and must be written to the source \214le before being e)
133 456 R .052(victed from the pool.)-.25 F(MPOOL_DISCARD)108 472.8 Q .145
(The page is unlik)133 484.8 R .144(ely to be useful in the near future, and s\
hould be discarded before other pages in the)-.1 F(pool.)133 496.8 Q
(The function)108 513.6 Q F1(mpool_put)2.5 E F0(returns -1 on f)2.5 E
(ailure, setting)-.1 E F1(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)352.77
513.6 Q .027(The function)108 530.4 R F1(mpool_sync)2.527 E F0 .028
(writes all pages associated with the MPOOLFILE pointer)2.528 F F1(mpf)2.528 E
F0 2.528(,w)1.96 G .028(hich were speci-)474.414 530.4 R .431(\214ed as ar)108
542.4 R .431(guments to the)-.18 F F1(mpool_put)2.931 E F0 .431
(function with an associated \215ag of MPOOL_DIR)2.931 F(TY)-.6 E 2.93(,t)-1.29
G 2.93(ot)472.61 542.4 S .43(he source \214le.)483.32 542.4 R(The function)108
554.4 Q F1(mpool_sync)2.5 E F0(returns -1 on f)2.5 E(ailure, setting)-.1 E F1
(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)357.76 554.4 Q 1.075
(The function)108 571.2 R F1(mpool_unlink)3.575 E F0(destro)3.575 E 1.075
(ys the memory pool identi\214ed by the directory)-.1 F F1(path)3.575 E F0
3.575(,r).28 G(emo)471.33 571.2 Q 1.075(ving all \214les)-.15 F 1.121
(used to implement the memory pool.)108 583.2 R 1.121(\(The directory)6.121 F
F1(path)3.621 E F0 1.121(is not remo)3.621 F -.15(ve)-.15 G 3.62(d.\) If).15 F
1.12(there are processes which)3.62 F(ha)108 595.2 Q .871 -.15(ve c)-.2 H
(alled).15 E F1(mpool_open)3.071 E F0 .571(without calling)3.071 F F1
(mpool_close)3.071 E F0 .572
(\(i.e., there are processes currently using the memory)3.071 F(pool\),)108
607.2 Q F1(mpool_unlink)2.652 E F0 .152(will f)2.652 F .151
(ail without further action, unless the force \215ag is set, in which case)-.1
F F1(mpool_unlink)2.651 E F0 .524
(will attempt to delete the memory pool \214les re)108 619.2 R -.05(ga)-.15 G
.525(rdless of an).05 F 3.025(yp)-.15 G .525
(rocesses still using the memory pool.)366.45 619.2 R(An)5.525 E(y)-.15 E .598
(accesses to a remo)108 631.2 R -.15(ve)-.15 G 3.097(dm).15 G .597
(emory pool will lik)208.95 631.2 R .597(ely result in une)-.1 F .597
(xpected beha)-.15 F(vior)-.2 E 5.597(.T)-.55 G .597(he function)436.036 631.2
R F1(mpool_unlink)3.097 E F0(returns -1 on f)108 643.2 Q(ailure, setting)-.1 E
F1(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)253.61 643.2 Q .11
(In the case of catastrophic or system f)108 660 R .11
(ailure, it is possible to clean up a memory pool by remo)-.1 F .11
(ving all of the)-.15 F .569(\214les in the directory speci\214ed to the)108
672 R F1(mpool_cr)3.068 E(eate)-.37 E F0 .568
(function, as memory pool \214les are ne)3.068 F -.15(ve)-.25 G 3.068(rc).15 G
.568(reated in an)487.364 672 R(y)-.15 E
(directory other than the one speci\214ed to)108 684 Q F1(mpool_cr)2.5 E(eate)
-.37 E F0(.).18 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q
99.315(ution August)-.2 F(1, 1995)2.5 E(3)535 732 Q EP
%%Page: 4 16
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 110.12(DB_MPOOL\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 110.12(anual DB_MPOOL\(3\))340.17 48 R .025(The function)108 84 R/F1
10/Times-Italic@0 SF(mpool_stat)2.525 E F0 .026
(writes statistics for the memory pool)2.526 F F1(mp)2.526 E F0 .026
(to the \214le speci\214ed by)2.526 F F1(fp)2.526 E F0 5.026(.T).19 G .026
(hese statistics)485.254 84 R .829
(include the number of \214les participating in the pool, the acti)108 96 R
1.129 -.15(ve p)-.25 H .829(ages in the pool, and numbers as to ho).15 F(w)-.25
E(ef)108 108 Q(fecti)-.25 E .3 -.15(ve t)-.25 H(he cache has been.).15 E/F2 9
/Times-Bold@0 SF(ERR)72 124.8 Q(ORS)-.27 E F0(The)108 136.8 Q F1(mpool_cr)3.852
E(eate)-.37 E F0(,).18 E F1(mpool_open)3.852 E F0(and)3.852 E F1(mpool_fopen)
3.852 E F0 1.353(functions may f)3.852 F 1.353(ail and set)-.1 F F1(errno)3.853
E F0 1.353(for an)3.853 F 3.853(yo)-.15 G 3.853(ft)493.424 136.8 S 1.353
(he errors)503.387 136.8 R(speci\214ed for the library functions)108 148.8 Q F1
(open)2.5 E F0(\(2\),).24 E F1 -.37(re)2.5 G(ad).37 E F0(\(2\), and).77 E F1
(malloc)2.5 E F0(\(3\).).31 E(The)108 165.6 Q F1(mpool_close)3.144 E F0(and)
3.144 E F1(mpool_fclose)3.144 E F0 .644(functions may f)3.144 F .644
(ail and set)-.1 F F1(errno)3.144 E F0 .643(for an)3.143 F 3.143(yo)-.15 G
3.143(ft)425.985 165.6 S .643(he errors speci\214ed for the)435.238 165.6 R
(library functions)108 177.6 Q F1(close)2.5 E F0(\(2\) and).18 E F1(fr)2.5 E
(ee)-.37 E F0(\(3\).).18 E(The)108 194.4 Q F1(mpool_g)4.097 E(et)-.1 E F0 1.597
(function may f)4.097 F 1.597(ail and set)-.1 F F1(errno)4.097 E F0 1.597
(for an)4.097 F 4.097(yo)-.15 G 4.097(ft)349.14 194.4 S 1.597
(he errors speci\214ed for the library functions)359.347 194.4 R F1 -.37(re)108
206.4 S(ad).37 E F0(\(2\),).77 E F1(write)2.5 E F0(\(2\), and).18 E F1(malloc)
2.5 E F0(\(3\) or the follo).31 E(wing:)-.25 E([EINV)108 223.2 Q(AL])-1.35 E
(The requested page does not e)133 235.2 Q(xist and MPOOL_CREA)-.15 E(TE w)
-1.11 E(as not set.)-.1 E(The)108 252 Q F1(mpool_put)4.288 E F0 1.787
(function may f)4.287 F 1.787(ail and set)-.1 F F1(errno)4.287 E F0 1.787
(for an)4.287 F 4.287(yo)-.15 G 4.287(ft)351.701 252 S 1.787
(he errors speci\214ed for the library function)362.098 252 R F1(write)108 264
Q F0(\(2\) or the follo).18 E(wing:)-.25 E([EA)108 280.8 Q(CCES])-.4 E
(The source \214le w)133 292.8 Q(as not opened for writing.)-.1 E(The)108 309.6
Q F1(mpool_sync)3.993 E F0 1.493(function may f)3.993 F 1.493(ail and set)-.1 F
F1(errno)3.993 E F0 1.494(for an)3.993 F 3.994(yo)-.15 G 3.994(ft)353.752 309.6
S 1.494(he errors speci\214ed for the library function)363.856 309.6 R F1
(write)108 321.6 Q F0(\(2\).).18 E(The)108 338.4 Q F1(mpool_unlink)3.569 E F0
1.069(function may f)3.569 F 1.068(ail and set)-.1 F F1(errno)3.568 E F0 1.068
(for an)3.568 F 3.568(yo)-.15 G 3.568(ft)356.734 338.4 S 1.068
(he errors speci\214ed for the library function)366.412 338.4 R F1(unlink)108
350.4 Q F0(\(2\) or the follo).67 E(wing:)-.25 E([EB)108 367.2 Q(USY])-.1 E
(The memory pool w)133 379.2 Q(as in use and the force \215ag w)-.1 E
(as not set.)-.1 E F2(SEE ALSO)72 396 Q F1(db_btr)108 408 Q(ee)-.37 E F0
(\(3\),).18 E F1(db_hash)2.5 E F0(\(3\),).28 E F1(db_loc)2.5 E(k)-.2 E F0
(\(3\),).67 E F1(db_lo)2.5 E(g)-.1 E F0(\(3\),).22 E F1(db_open)2.5 E F0
(\(3\),).24 E F1(db_r)2.5 E(ecno)-.37 E F0(\(3\),).18 E F1(db_txn)2.5 E F0
(\(3\)).24 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q
99.315(ution August)-.2 F(1, 1995)2.5 E(4)535 732 Q EP
%%Page: 1 17
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 119.01(DB_OPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 119.01(anual DB_OPEN\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)
72 84 S(ME).18 E F0(db_open \255 database access methods)108 96 Q F1(SYNOPSIS)
72 112.8 Q/F2 10/Times-Bold@0 SF(#include <db)108 124.8 Q(.h>)-.4 E(DB *)108
148.8 Q(db_open\(const char *\214le, int \215ags, int mode,)108 160.8 Q
(DBTYPE type, DBINFO *dbinf)158 172.8 Q(o, const v)-.25 E(oid *openinf)-.1 E
(o\);)-.25 E F1(DESCRIPTION)72 189.6 Q F0 .485(The DB library is a f)108 201.6
R .485(amily of groups of functions that pro)-.1 F .486
(vides a modular programming interf)-.15 F .486(ace to trans-)-.1 F .823
(actions and record-oriented \214le access.)108 213.6 R .822
(The library includes support for transaction, locking, logging and)5.822 F
.258(\214le b)108 225.6 R(uf)-.2 E .258(fering functionality)-.25 F 2.758(,a)
-.65 G 2.758(sw)223.214 225.6 S .258(ell as v)237.082 225.6 R .258(arious inde)
-.25 F -.15(xe)-.15 G 2.758(da).15 G .258(ccess methods.)331.434 225.6 R(Man)
5.258 E 2.758(yo)-.15 G 2.758(ft)427.878 225.6 S .258
(he functional groups \(e.g.)436.746 225.6 R .528(the memory pool functions\) \
are useful independently of the rest of the DB functions, although some func-)
108 237.6 R .306(tional groups are e)108 249.6 R .306
(xplicitly based on other functional groups \(e.g.)-.15 F .306
(transactions and logging\).)5.306 F -.15(Fo)5.306 G 2.806(rag).15 G(eneral)
515.57 249.6 Q .245(description of transactions, see)108 261.6 R/F3 10
/Times-Italic@0 SF(db_txn)2.745 E F0 2.745(\(3\). F).24 F .245
(or a general description of the access methods, see)-.15 F F3(db_open)2.745 E
F0(\(3\)).24 E .307(and then the indi)108 273.6 R .307
(vidual access method manual pages:)-.25 F F3(db_btr)2.808 E(ee)-.37 E F0
(\(3\),).18 E F3(db_hash)2.808 E F0(\(3\),).28 E F3(db_lo)2.808 E(g)-.1 E F0
.308(\(3\) and).22 F F3(db_r)2.808 E(ecno)-.37 E F0(\(3\).).18 E -.15(Fo)108
285.6 S 3.635(rag).15 G 1.135(eneral description of the lock manager)138.45
285.6 R 3.635(,s)-.4 G(ee)307.32 285.6 Q F3(db_loc)3.635 E(k)-.2 E F0 3.635
(\(3\). F).67 F 1.135(or a general description of the memory)-.15 F
(pool manager)108 297.6 Q 2.5(,s)-.4 G(ee)171.2 297.6 Q F3(db_mpool)2.5 E F0
(\(3\).).51 E(This manual page describes the o)108 314.4 Q -.15(ve)-.15 G
(rall structure of the a).15 E -.25(va)-.2 G(ilable access methods.).25 E .457
(The currently supported \214le formats are btree, hashed, log and recno \(i.e\
. \215at-\214le oriented\).)108 331.2 R .457(The btree for)5.457 F(-)-.2 E .974
(mat is a representation of a sorted, balanced tree structure.)108 343.2 R .973
(The hashed format is an e)5.974 F .973(xtensible, dynamic)-.15 F .801
(hashing scheme.)108 355.2 R .802
(The log format is a general-purpose logging f)5.801 F(acility)-.1 E 5.802(.T)
-.65 G .802(he recno format is a byte stream)406.888 355.2 R .415
(\214le with \214x)108 367.2 R .415(ed or v)-.15 F .415
(ariable length records.)-.25 F .415(The formats and other)5.415 F 2.914(,f)-.4
G .414(ormat speci\214c information are described)376.714 367.2 R
(in detail in their respecti)108 379.2 Q .3 -.15(ve m)-.25 H(anual pages:).15 E
F3(db_btr)2.5 E(ee)-.37 E F0(\(3\),).18 E F3(db_hash)2.5 E F0(\(3\),).28 E F3
(db_lo)2.5 E(g)-.1 E F0(\(3\), and).22 E F3(db_r)2.5 E(ecno)-.37 E F0(\(3\).)
.18 E .138(Db_open opens)108 396 R F3(\214le)2.638 E F0 .139
(for reading and/or writing.)2.638 F .139(Files ne)5.139 F -.15(ve)-.25 G 2.639
(ri).15 G .139(ntended to be preserv)349.088 396 R .139
(ed on disk may be created)-.15 F .423
(by setting the \214le parameter to NULL.)108 408 R .423
(\(Note, while most of the access methods use)5.423 F F3(\214le)2.923 E F0 .423
(as the name of an)2.923 F .429
(underlying \214le on disk, this is not guaranteed.)108 420 R .43
(See the manual pages for the indi)5.429 F .43(vidual access methods for)-.25 F
(more information.\))108 432 Q(The)108 448.8 Q F3<8d61>4.328 E(gs)-.1 E F0(and)
4.328 E F3 1.828(mode ar)4.328 F(guments)-.37 E F0 1.828
(are as speci\214ed to the)4.328 F F3(open)4.328 E F0 1.828(\(2\) function, ho)
.24 F(we)-.25 E -.15(ve)-.25 G 2.628 -.4(r, o).15 H 1.828(nly the O_CREA).4 F
-.74(T,)-1.11 G .127(O_EXCL, O_EXLOCK, O_NONBLOCK, O_RDONL)108 460.8 R 2.708
-1.29(Y, O)-1 H(_RD)1.29 E .128(WR, O_SHLOCK and O_TR)-.3 F .128
(UNC \215ags are)-.4 F 2.5(meaningful. \(Note,)108 472.8 R
(opening a database \214le O_WR)2.5 E(ONL)-.4 E 2.5(Yi)-1 G 2.5(sn)342.67 472.8
S(ot possible.\))354.06 472.8 Q(The)108 489.6 Q F3(type)5.338 E F0(ar)5.338 E
2.837(gument is of type DBTYPE \(as de\214ned in the <db)-.18 F 2.837
(.h> include \214le\) and may be set to)-.4 F
(DB_BTREE, DB_HASH, DB_LOG or DB_RECNO.)108 501.6 Q(The)108 518.4 Q F3(dbinfo)
3.279 E F0(ar)3.279 E .779(gument is a pointer to a structure containing refer\
ences to locking, logging, transaction, and)-.18 F 1.242(shared-memory b)108
530.4 R(uf)-.2 E 1.242(fer pool information.)-.25 F(If)6.242 E F3(dbinfo)3.742
E F0 1.241(is NULL, then the access method may still use these)3.741 F .667
(subsystems, b)108 542.4 R .667(ut the usage will be pri)-.2 F -.25(va)-.25 G
.668(te to the application and managed by DB.).25 F(If)5.668 E F3(dbinfo)3.168
E F0 .668(is non-NULL,)3.168 F .481(then the module referenced by each of the \
non-NULL \214elds is used by DB as necessary)108 554.4 R 5.48(.T)-.65 G .48
(he \214elds of the)479.4 554.4 R(DBINFO structure are de\214ned as follo)108
566.4 Q(ws:)-.25 E(const char *errpfx;)108 583.2 Q 2.5(Ap)133 595.2 S
(re\214x to prepend to error messages; used only if)147.72 595.2 Q F3
(err\214le)2.5 E F0(is non-NULL.)2.5 E(FILE *err\214le;)108 612 Q(The)133 624 Q
F3(stdio)2.5 E F0(\(3\) \214le stream to which error messages are logged.).18 E
.147(When an)133 648 R 2.647(ye)-.15 G .147(rror occurs in the)180.904 648 R F3
(db_open)2.648 E F0 .148(function, or in an)2.648 F 2.648(yf)-.15 G .148
(unction called using a \214eld of the returned)369.824 648 R .234
(DB structure, an error v)133 660 R .234
(alue is returned by the function, and the global v)-.25 F(ariable)-.25 E F3
(errno)2.733 E F0 .233(is set appropri-)2.733 F(ately)133 672 Q 5.415(.I)-.65 G
2.915(ns)163.035 672 S .416(ome cases, ho)174.84 672 R(we)-.25 E -.15(ve)-.25 G
1.216 -.4(r, t).15 H(he).4 E F3(errno)2.916 E F0 -.25(va)2.916 G .416
(lue may be insuf).25 F .416(\214cient to describe the cause of the error)-.25
F(.)-.55 E .137(In these cases, if)133 684 R F3(err\214le)2.637 E F0 .137(is n\
on-NULL, additional error information will be written to the \214le stream it)
2.637 F(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 18
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 119.01(DB_OPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 119.01(anual DB_OPEN\(3\))340.17 48 R .643
(represents, preceded by the string, if an)133 84 R 1.943 -.65(y, s)-.15 H .643
(peci\214ed by).65 F/F1 10/Times-Italic@0 SF(errpfx)3.143 E F0 5.643(.T).53 G
.644(his error logging f)394.94 84 R .644(acility should not)-.1 F
(be required for normal operation, b)133 96 Q(ut may be useful in deb)-.2 E
(ugging applications.)-.2 E(char *errb)108 112.8 Q(uf;)-.2 E .03(The b)133
124.8 R(uf)-.2 E .03(fer to which error messages are copied.)-.25 F .029
(If non-NULL,)5.029 F F1(errb)2.529 E(uf)-.2 E F0(beha)2.529 E -.15(ve)-.2 G
2.529(sa).15 G 2.529(sd)451.423 124.8 S .029(escribed for)462.842 124.8 R F1
(err\214le)2.529 E F0(,).18 E -.15(ex)133 136.8 S .173(cept that the).15 F F1
(errpfx)2.673 E F0 .174
(\214eld is ignored and the error message is copied into the speci\214ed b)
2.673 F(uf)-.2 E .174(fer instead)-.25 F 1.014
(of being written to the FILE stream.)133 148.8 R 1.013
(The DB routines assume that the associated b)6.014 F(uf)-.2 E 1.013
(fer is at least)-.25 F(1024 bytes in length.)133 160.8 Q(LOCK_T)108 177.6 Q
(ABLE_T *lockinfo;)-.93 E .265
(If locking is required for the \214le being opened \(as in the case of b)133
189.6 R(uf)-.2 E .266(fers being maintained in a shared)-.25 F 1.794(memory b)
133 201.6 R(uf)-.2 E 1.794(fer pool\), the)-.25 F F1(loc)4.294 E(kinfo)-.2 E F0
1.794(\214eld contains a return v)4.294 F 1.793(alue from the function)-.25 F
F1(loc)4.293 E(k_open)-.2 E F0(that)4.293 E(should be used \(see)133 213.6 Q F1
(db_loc)2.5 E(k)-.2 E F0 2.5(\(3\)\). If).67 F F1(loc)2.5 E(kinfo)-.2 E F0
(is NULL, no locking is done.)2.5 E(DB *loginfo;)108 230.4 Q .93
(If modi\214cations to the \214le being opened should be logged, the)133 242.4
R F1(lo)3.43 E(ginfo)-.1 E F0 .93(\214eld contains a return v)3.43 F(alue)-.25
E .063(from the function)133 254.4 R F1(dbopen)2.563 E F0 2.563(,w).24 G .062
(hen opening a DB \214le of type DB_LOG.)247.642 254.4 R(If)5.062 E F1(lo)2.562
E(ginfo)-.1 E F0 .062(is NULL, no logging)2.562 F(is done.)133 266.4 Q
(MPOOL *mpoolinfo;)108 283.2 Q 1.129
(If the cache for the \214le being opened should be maintained in a shared b)
133 295.2 R(uf)-.2 E 1.129(fer pool, the)-.25 F F1(mpoolinfo)3.629 E F0 .102
(\214eld contains a return v)133 307.2 R .102(alue from the function)-.25 F F1
(mpool_open)2.602 E F0 .102(that should be used \(see)2.602 F F1(db_mpool)2.602
E F0 2.602(\(3\)\). If).51 F F1(mpoolinfo)133 319.2 Q F0 .429
(is NULL, a memory pool may still be created, b)2.929 F .43(ut it will be pri)
-.2 F -.25(va)-.25 G .43(te to the application and).25 F(managed by DB.)133
331.2 Q(TXNMGR *txninfo;)108 348 Q 1.161
(If the accesses to the \214le being opened should tak)133 360 R 3.661(ep)-.1 G
1.161(lace in the conte)354.474 360 R 1.161(xt of transactions \(pro)-.15 F
(viding)-.15 E 1.239(atomicity and complete error reco)133 372 R -.15(ve)-.15 G
1.239(ry\), the).15 F F1(txninfo)3.739 E F0 1.239(\214eld contains a return v)
3.739 F 1.24(alue from the function)-.25 F F1(txn_open)133 384 Q F0(\(see)2.599
E F1(db_txn)2.599 E F0 2.599(\(3\)\). If).24 F .098
(transactions are speci\214ed, the application is responsible for making suit-)
2.599 F 1.27(able calls to)133 396 R F1(txn_be)3.77 E(gin)-.4 E F0(,).24 E F1
(txn_abort)3.77 E F0 3.77(,a).68 G(nd)282.91 396 Q F1(txn_commit)3.77 E F0 6.27
(.I).68 G(f)356.12 396 Q F1(txninfo)3.77 E F0 1.27
(is NULL, no transaction support is)3.77 F(done.)133 408 Q(The)108 424.8 Q F1
(openinfo)2.85 E F0(ar)2.85 E .349(gument is a pointer to an access method spe\
ci\214c structure described in the access method')-.18 F(s)-.55 E .03
(manual page.)108 436.8 R(If)5.03 E F1(openinfo)2.53 E F0 .031
(is NULL, each access method will use def)2.53 F .031
(aults appropriate for the system and the)-.1 F(access method.)108 448.8 Q/F2 9
/Times-Bold@0 SF(KEY/D)72 465.6 Q -1.35 -.855(AT A)-.315 H -.666(PA)3.105 G
(IRS).666 E F0 .313(Access to all access methods is based on k)108 477.6 R -.15
(ey)-.1 G .312(/data pairs.).15 F .312(Both k)5.312 F -.15(ey)-.1 G 2.812(sa)
.15 G .312(nd data are represented by the follo)386.758 477.6 R(w-)-.25 E
(ing data structure:)108 489.6 Q(typedef struct {)108 506.4 Q -.2(vo)144 518.4
S(id *data;).2 E(size_t size;)144 530.4 Q 2.5(}D)108 542.4 S(BT)122.52 542.4 Q
(;)-.55 E(The elements of the DBT structure are de\214ned as follo)108 559.2 Q
(ws:)-.25 E 5.84(data A)108 576 R(pointer to a byte string.)2.5 E 6.95
(size The)108 592.8 R(length of)2.5 E F1(data)2.5 E F0 2.5(,i).26 G 2.5(nb)
215.2 592.8 S(ytes.)227.7 592.8 Q -2.15 -.25(Ke y)108 609.6 T .672(and data by\
te strings may reference strings of essentially unlimited length, although an)
3.422 F 3.173(yt)-.15 G .873 -.1(wo o)493.204 609.6 T 3.173(ft).1 G(hem)522.78
609.6 Q(must \214t into a)108 621.6 Q -.25(va)-.2 G
(ilable memory at the same time.).25 E .14(The access methods pro)108 638.4 R
.139(vide no guarantees about byte string alignment, and applications are resp\
onsible for)-.15 F(maintaining an)108 650.4 Q 2.5(yn)-.15 G
(ecessary alignment.)180.07 650.4 Q F2(DB OPERA)72 667.2 Q(TIONS)-.855 E F1
(Db_open)108 679.2 Q F0 .56
(returns a pointer to a DB structure \(as de\214ned in the <db)3.06 F .56
(.h> include \214le\) on success, and NULL)-.4 F 1.02(on error)108 691.2 R 6.02
(.T)-.55 G 1.02(he DB structure describes a database type, and includes a set \
of functions to perform v)155.03 691.2 R(arious)-.25 E(4.4 Berk)72 732 Q(ele)
-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E
(2)535 732 Q EP
%%Page: 3 19
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 119.01(DB_OPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 119.01(anual DB_OPEN\(3\))340.17 48 R .241
(actions, as described belo)108 84 R 4.041 -.65(w. E)-.25 H .241
(ach of these functions tak).65 F .241
(es a pointer to a DB structure, and may tak)-.1 F 2.741(eo)-.1 G .242(ne or)
519.488 84 R .889(more DBT *')108 96 R 3.389(sa)-.55 G .889(nd a \215ag v)
174.827 96 R .889(alue as well.)-.25 F(Indi)5.889 E .888
(vidual access methods specify additional functions and \215ags)-.25 F
(which are speci\214c to the method.)108 108 Q
(The \214elds of the DB structure are as follo)5 E(ws:)-.25 E(DBTYPE type;)108
124.8 Q(The type of the underlying access method \(and \214le format\).)133
136.8 Q(int \(*close\)\(const DB *db\);)108 153.6 Q 3.863(Ap)133 165.6 S 1.363
(ointer to a function to \215ush an)149.083 165.6 R 3.864(yc)-.15 G 1.364
(ached information to disk, free an)290.968 165.6 R 3.864(ya)-.15 G 1.364
(llocated resources, and)445.912 165.6 R .878(close an)133 177.6 R 3.378(yu)
-.15 G .878(nderlying \214les.)179.596 177.6 R .878(Since k)5.878 F -.15(ey)-.1
G .878(/data pairs are cached in memory).15 F 3.377(,f)-.65 G .877
(ailing to sync the \214le with)431.445 177.6 R(the)133 189.6 Q/F1 10
/Times-Italic@0 SF(close)2.5 E F0(or)2.5 E F1(sync)2.5 E F0
(function may result in inconsistent or lost information.)2.5 E(The)133 206.4 Q
F1(close)2.5 E F0(functions return -1 on f)2.5 E(ailure, setting)-.1 E F1
(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)355.54 206.4 Q
(int \(*del\)\(const DB *db, TXN *txnid,)108 223.2 Q(const DBT *k)183 235.2 Q
-.15(ey)-.1 G 2.5(,u)-.5 G(_int \215ags\);)257.65 235.2 Q 2.541(Ap)133 247.2 S
.041(ointer to a function to remo)147.761 247.2 R .341 -.15(ve k)-.15 H -.15
(ey).05 G .041(/data pairs from the database.).15 F .042(The k)5.041 F -.15(ey)
-.1 G .042(/data pair associated with).15 F(the speci\214ed)133 259.2 Q F1 -.1
(ke)2.5 G(y)-.2 E F0(are discarded from the database.)2.5 E(The)133 276 Q F1
(txnid)3.317 E F0 .817(parameter contains a transaction ID returned from)3.317
F F1(txn_be)3.317 E(gin)-.4 E F0 3.317(,i).24 G 3.316(ft)431.22 276 S .816
(he \214le is being accessed)440.646 276 R
(under transaction protection, or NULL if transactions are not in ef)133 288 Q
(fect.)-.25 E(The parameter)133 304.8 Q F1<8d61>2.5 E(g)-.1 E F0
(must be set to 0 or e)2.5 E(xactly one of the follo)-.15 E(wing v)-.25 E
(alues:)-.25 E(R_CURSOR)133 321.6 Q(Delete the record referenced by the cursor)
158 333.6 Q 5(.T)-.55 G(he cursor must ha)339.32 333.6 Q .3 -.15(ve p)-.2 H(re)
.15 E(viously been initialized.)-.25 E(The)133 350.4 Q F1(delete)2.934 E F0
.434(functions return -1 on error)2.934 F 2.934(,s)-.4 G(etting)297.818 350.4 Q
F1(errno)2.934 E F0 2.934(,0o).18 G 2.934(ns)364.3 350.4 S .434
(uccess, and 1 if the speci\214ed)376.124 350.4 R F1 -.1(ke)2.935 G(y)-.2 E F0
.435(did not)2.935 F -.15(ex)133 362.4 S(ist in the \214le.).15 E
(int \(*fd\)\(const DB *db\);)108 379.2 Q 3.351(Ap)133 391.2 S .851
(ointer to a function which returns a \214le descriptor representati)148.571
391.2 R 1.15 -.15(ve o)-.25 H 3.35(ft).15 G .85(he underlying database.)430.53
391.2 R(A)5.85 E .338(\214le descriptor referencing the same \214le will be re\
turned to all processes which call)133 403.2 R F1(db_open)2.838 E F0 .339
(with the)2.839 F(same)133 415.2 Q F1(\214le)3.376 E F0 3.376(name. This)3.376
F .876(\214le descriptor may be safely used as an ar)3.376 F .876
(gument to the)-.18 F F1(fcntl)3.376 E F0 .875(\(2\) and).51 F F1(\215oc)3.375
E(k)-.2 E F0(\(2\)).67 E .99(locking functions.)133 427.2 R .99
(The \214le descriptor is not necessarily associated with an)5.99 F 3.49(yo)
-.15 G 3.49(ft)453.98 427.2 S .99(he underlying \214les)463.58 427.2 R
(used by the access method.)133 439.2 Q(No \214le descriptor is a)5 E -.25(va)
-.2 G(ilable for in memory databases.).25 E(The)133 456 Q F1(fd)2.5 E F0
(functions return -1 on error)2.5 E 2.5(,s)-.4 G(etting)278.68 456 Q F1(errno)
2.5 E F0 2.5(,a).18 G(nd the \214le descriptor on success.)335.8 456 Q
(int \(*get\)\(const DB *db, TXN *txnid,)108 472.8 Q(const DBT *k)183 484.8 Q
-.15(ey)-.1 G 2.5(,D)-.5 G(BT *data, u_int \215ags\);)259.87 484.8 Q 2.854(Ap)
133 496.8 S .354(ointer to a function which is the interf)148.074 496.8 R .354
(ace for k)-.1 F -.15(ey)-.1 G .353(ed retrie).15 F -.25(va)-.25 G 2.853(lf).25
G .353(rom the database.)397.995 496.8 R .353(The address and)5.353 F
(length of the data associated with the speci\214ed)133 508.8 Q F1 -.1(ke)2.5 G
(y)-.2 E F0(are returned in the structure referenced by)2.5 E F1(data)2.5 E F0
(.).26 E(The)133 525.6 Q F1(txnid)3.316 E F0 .816
(parameter contains a transaction ID returned from)3.316 F F1(txn_be)3.317 E
(gin)-.4 E F0 3.317(,i).24 G 3.317(ft)431.215 525.6 S .817
(he \214le is being accessed)440.642 525.6 R
(under transaction protection, or NULL if transactions are not in ef)133 537.6
Q(fect.)-.25 E(The)133 554.4 Q F1 -.1(ge)2.5 G(t).1 E F0
(functions return -1 on error)2.5 E 2.5(,s)-.4 G(etting)283.02 554.4 Q F1
(errno)2.5 E F0 2.5(,0o).18 G 2.5(ns)348.2 554.4 S(uccess, and 1 if the)359.59
554.4 Q F1 -.1(ke)2.5 G(y)-.2 E F0 -.1(wa)2.5 G 2.5(sn).1 G(ot found.)476.83
554.4 Q(int \(*put\)\(const DB *db, TXN *txnid,)108 571.2 Q(DBT *k)183 583.2 Q
-.15(ey)-.1 G 2.5(,c)-.5 G(onst DBT *data, u_int \215ags\);)233.48 583.2 Q 2.5
(Ap)133 595.2 S(ointer to a function to store k)147.72 595.2 Q -.15(ey)-.1 G
(/data pairs in the database.).15 E(The)133 612 Q F1(txnid)3.317 E F0 .817
(parameter contains a transaction ID returned from)3.317 F F1(txn_be)3.317 E
(gin)-.4 E F0 3.317(,i).24 G 3.316(ft)431.22 612 S .816
(he \214le is being accessed)440.646 612 R
(under transaction protection, or NULL if transactions are not in ef)133 624 Q
(fect.)-.25 E(The parameter)133 640.8 Q F1<8d61>2.5 E(g)-.1 E F0
(must be set to 0 or e)2.5 E(xactly one of the follo)-.15 E(wing v)-.25 E
(alues:)-.25 E(R_CURSOR)133 657.6 Q .448(Replace the k)158 669.6 R -.15(ey)-.1
G .448(/data pair referenced by the cursor).15 F 5.449(.T)-.55 G .449
(he cursor must ha)375.156 669.6 R .749 -.15(ve p)-.2 H(re).15 E .449
(viously been ini-)-.25 F(tialized.)158 681.6 Q(4.4 Berk)72 732 Q(ele)-.1 E 2.5
(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(3)535
732 Q EP
%%Page: 4 20
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 119.01(DB_OPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 119.01(anual DB_OPEN\(3\))340.17 48 R(R_NOO)133 84 Q(VER)-.5 E
(WRITE)-.55 E(Enter the ne)158 96 Q 2.5(wk)-.25 G -.15(ey)220.69 96 S
(/data pair only if the k).15 E .3 -.15(ey d)-.1 H(oes not pre).15 E(viously e)
-.25 E(xist.)-.15 E .664(The def)133 112.8 R .664(ault beha)-.1 F .664
(vior of the)-.2 F/F1 10/Times-Italic@0 SF(put)3.164 E F0 .664
(functions is to enter the ne)3.164 F 3.164(wk)-.25 G -.15(ey)387.498 112.8 S
.663(/data pair).15 F 3.163(,r)-.4 G .663(eplacing an)443.534 112.8 R 3.163(yp)
-.15 G(re)503.03 112.8 Q(viously)-.25 E -.15(ex)133 124.8 S(isting k).15 E -.15
(ey)-.1 G(.)-.5 E(The)133 141.6 Q F1(put)3.558 E F0 1.058
(functions return -1 on error)3.558 F 3.559(,s)-.4 G(etting)291.089 141.6 Q F1
(errno)3.559 E F0 3.559(,0o).18 G 3.559(ns)359.446 141.6 S 1.059
(uccess, and 1 if the R_NOO)371.895 141.6 R(VER)-.5 E(WRITE)-.55 E F1<8d61>133
153.6 Q(g)-.1 E F0 -.1(wa)2.5 G 2.5(ss).1 G(et and the k)172.24 153.6 Q .3 -.15
(ey a)-.1 H(lready e).15 E(xists in the \214le.)-.15 E
(int \(*seq\)\(const DB *db, TXN *txnid,)108 170.4 Q(DBT *k)183 182.4 Q -.15
(ey)-.1 G 2.5(,D)-.5 G(BT *data, u_int \215ags\);)236.26 182.4 Q 2.877(Ap)133
194.4 S .377(ointer to a function which is the interf)148.097 194.4 R .377
(ace for sequential retrie)-.1 F -.25(va)-.25 G 2.877(lf).25 G .377
(rom the database.)415.194 194.4 R .376(The address)5.376 F .012
(and length of the k)133 206.4 R .312 -.15(ey a)-.1 H .012
(re returned in the structure referenced by).15 F F1 -.1(ke)2.512 G(y)-.2 E F0
2.512(,a).32 G .012(nd the address and length of the)412.726 206.4 R
(data are returned in the structure referenced by)133 218.4 Q F1(data)2.5 E F0
(.).26 E(The)133 235.2 Q F1(txnid)3.317 E F0 .817
(parameter contains a transaction ID returned from)3.317 F F1(txn_be)3.317 E
(gin)-.4 E F0 3.317(,i).24 G 3.316(ft)431.22 235.2 S .816
(he \214le is being accessed)440.646 235.2 R
(under transaction protection, or NULL if transactions are not in ef)133 247.2
Q(fect.)-.25 E .721(Sequential k)133 264 R -.15(ey)-.1 G .721
(/data pair retrie).15 F -.25(va)-.25 G 3.221(lm).25 G .721(ay be)277.884 264 R
.721(gin at an)-.15 F 3.221(yt)-.15 G .721
(ime, and the logical position of the `)346.568 264 R(`cursor')-.74 E 3.222('i)
-.74 G(s)536.11 264 Q .947(not af)133 276 R .947(fected by calls to the)-.25 F
F1(del)3.447 E F0(,).51 E F1 -.1(ge)3.447 G(t).1 E F0(,).68 E F1(put)3.447 E F0
3.446(,o).68 G(r)308.572 276 Q F1(sync)3.446 E F0 3.446
(functions. Modi\214cations)3.446 F .946(to the database during a)3.446 F 2.091
(sequential scan will be re\215ected in the scan, i.e. records inserted behind\
 the cursor will not be)133 288 R
(returned while records inserted in front of the cursor will be returned.)133
300 Q(The parameter)133 316.8 Q F1<8d61>2.5 E(g)-.1 E F0(must be set to 0 or e)
2.5 E(xactly one of the follo)-.15 E(wing v)-.25 E(alues:)-.25 E(R_CURSOR)133
333.6 Q .937(The data associated with the speci\214ed k)158 345.6 R 1.237 -.15
(ey i)-.1 H 3.437(sr).15 G 3.437(eturned. This)348.546 345.6 R(dif)3.437 E .936
(fers from the)-.25 F F1 -.1(ge)3.436 G(t).1 E F0 .936(functions in)3.436 F
(that it sets or initializes the cursor to the location of the k)158 357.6 Q .3
-.15(ey a)-.1 H 2.5(sw).15 G(ell.)415.5 357.6 Q(R_FIRST)133 374.4 Q .835
(The \214rst k)158 386.4 R -.15(ey)-.1 G .835(/data pair of the database is re\
turned, and the cursor is set or initialized to refer).15 F(-)-.2 E(ence it.)
158 398.4 Q(R_NEXT)133 415.2 Q(Retrie)158 427.2 Q 1.015 -.15(ve t)-.25 H .715
(he k).15 F -.15(ey)-.1 G .715(/data pair immediately after the cursor).15 F
5.715(.I)-.55 G 3.215(ft)391.91 427.2 S .714
(he cursor is not yet set, this is the)401.235 427.2 R
(same as the R_FIRST \215ag.)158 439.2 Q(The)133 456 Q F1(seq)3.014 E F0 .514
(functions return -1 on error)3.014 F 3.015(,s)-.4 G(etting)287.83 456 Q F1
(errno)3.015 E F0 3.015(,0o).18 G 3.015(ns)354.555 456 S .515
(uccess, and 1 if there are no k)366.46 456 R -.15(ey)-.1 G .515(/data pairs)
.15 F(less than or greater than the speci\214ed or current k)133 468 Q -.15(ey)
-.1 G(.)-.5 E(int \(*sync\)\(const DB *db, u_int \215ags\);)108 484.8 Q 3.291
(Ap)133 496.8 S .791(ointer to a function to \215ush an)148.511 496.8 R 3.291
(yc)-.15 G .791(ached information to disk.)286.388 496.8 R .79
(If the database is in memory only)5.79 F(,)-.65 E(the)133 508.8 Q F1(sync)2.5
E F0(function has no ef)2.5 E(fect and will al)-.25 E -.1(wa)-.1 G(ys succeed.)
.1 E(The parameter)133 525.6 Q F1<8d61>2.5 E(g)-.1 E F0
(must be set to 0 or a v)2.5 E
(alue speci\214ed by an access method speci\214c manual page.)-.25 E(The)133
542.4 Q F1(sync)2.5 E F0(functions return -1 on f)2.5 E(ailure, setting)-.1 E
F1(errno)2.5 E F0 2.5(,a).18 G(nd 0 on success.)352.76 542.4 Q/F2 9
/Times-Bold@0 SF(ERR)72 559.2 Q(ORS)-.27 E F0(The)108 571.2 Q F1(db_open)4.548
E F0 2.048(function may f)4.548 F 2.049(ail and set)-.1 F F1(errno)4.549 E F0
2.049(for an)4.549 F 4.549(yo)-.15 G 4.549(ft)345.977 571.2 S 2.049
(he errors speci\214ed for the library functions)356.636 571.2 R F1(open)108
583.2 Q F0(\(2\),).24 E F1(malloc)2.5 E F0(\(3\) or the follo).31 E(wing:)-.25
E([EFTYPE])108 600 Q 2.5<418c>133 612 S(le is incorrectly formatted.)148.28 612
Q([EINV)108 628.8 Q(AL])-1.35 E 2.557(Ap)133 640.8 S .056
(arameter has been speci\214ed \(hash function, recno pad byte etc.\))147.777
640.8 R .056(that is incompatible with the cur)5.056 F(-)-.2 E .725
(rent \214le speci\214cation or)133 652.8 R 3.225(,a\215)-.4 G .725
(ag to a function which is not meaningful for the function \(for e)248.435
652.8 R(xample,)-.15 E .763(use of the cursor without prior initialization\) o\
r there is a mismatch between the v)133 664.8 R .763(ersion number of)-.15 F
(\214le and the softw)133 676.8 Q(are.)-.1 E(The)108 693.6 Q F1(close)2.913 E
F0 .413(functions may f)2.913 F .413(ail and set)-.1 F F1(errno)2.913 E F0 .413
(for an)2.913 F 2.913(yo)-.15 G 2.913(ft)319.62 693.6 S .414
(he errors speci\214ed for the library functions)328.643 693.6 R F1(close)2.914
E F0(\(2\),).18 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q
99.315(ution August)-.2 F(1, 1995)2.5 E(4)535 732 Q EP
%%Page: 5 21
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 119.01(DB_OPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 119.01(anual DB_OPEN\(3\))340.17 48 R/F1 10/Times-Italic@0 SF -.37
(re)108 84 S(ad).37 E F0(\(2\),).77 E F1(write)2.5 E F0(\(2\),).18 E F1(fr)2.5
E(ee)-.37 E F0(\(3\), or).18 E F1(fsync)2.5 E F0(\(2\).).31 E(The)108 100.8 Q
F1(del)2.52 E F0(,).51 E F1 -.1(ge)2.52 G(t).1 E F0(,).68 E F1(put)2.52 E F0
(and)2.52 E F1(seq)2.52 E F0 .02(functions may f)2.52 F .02(ail and set)-.1 F
F1(errno)2.52 E F0 .02(for an)2.52 F 2.52(yo)-.15 G 2.52(ft)376.3 100.8 S .02
(he errors speci\214ed for the library func-)384.93 100.8 R(tions)108 112.8 Q
F1 -.37(re)2.5 G(ad).37 E F0(\(2\),).77 E F1(write)2.5 E F0(\(2\),).18 E F1(fr)
2.5 E(ee)-.37 E F0(\(3\) or).18 E F1(malloc)2.5 E F0(\(3\).).31 E(The)108 129.6
Q F1(fd)2.5 E F0(functions will f)2.5 E(ail and set)-.1 E F1(errno)2.5 E F0
(to ENOENT for in memory databases.)2.5 E(The)108 146.4 Q F1(sync)2.5 E F0
(functions may f)2.5 E(ail and set)-.1 E F1(errno)2.5 E F0(for an)2.5 E 2.5(yo)
-.15 G 2.5(ft)312.71 146.4 S(he errors speci\214ed for the library function)
321.32 146.4 Q F1(fsync)2.5 E F0(\(2\).).31 E/F2 9/Times-Bold@0 SF(SEE ALSO)72
163.2 Q F1(db_btr)108 175.2 Q(ee)-.37 E F0(\(3\),).18 E F1(db_hash)2.5 E F0
(\(3\),).28 E F1(db_loc)2.5 E(k)-.2 E F0(\(3\),).67 E F1(db_lo)2.5 E(g)-.1 E F0
(\(3\),).22 E F1(db_mpool)2.5 E F0(\(3\),).51 E F1(db_r)2.5 E(ecno)-.37 E F0
(\(3\),).18 E F1(db_txn)2.5 E F0(\(3\)).24 E F2 -.09(BU)72 192 S(GS).09 E F0
.106(The name DBT is a mnemonic for `)108 204 R .106(`data base thang')-.74 F
.106(', and w)-.74 F .107(as used because noone could think of a reason-)-.1 F
(able name that w)108 216 Q(asn')-.1 E 2.5(ta)-.18 G(lready in use some)202.14
216 Q(where else.)-.25 E(The)108 232.8 Q F1(fd)2.5 E F0(function interf)2.5 E
(ace is a kluge, and will be deleted in a future v)-.1 E(ersion of the interf)
-.15 E(ace.)-.1 E(Only big and little endian byte order is supported.)108 249.6
Q(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(5)535 732 Q EP
%%Page: 1 22
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 111.23(DB_RECNO\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 111.23(anual DB_RECNO\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)
72 84 S(ME).18 E F0(db_recno \255 record number database access method)108 96 Q
F1(DESCRIPTION)72 112.8 Q F0(speci\214c details of the recno access method.)108
124.8 Q F1 -.495(AC)72 141.6 S(CESS METHOD SPECIFIC INFORMA).495 E(TION)-.855 E
F0 2.497(The recno access method speci\214c data structure pro)108 153.6 R
2.497(vided to)-.15 F/F2 10/Times-Italic@0 SF(db_open)4.997 E F0 2.497
(is typedef)4.997 F 3.497 -.5('d a).55 H 2.497(nd named REC-).5 F 2.765
(NOINFO. A)108 165.6 R .265(RECNOINFO structure has at least the follo)2.765 F
.266(wing \214elds, which may be initialized before call-)-.25 F(ing)108 177.6
Q F2(db_open)2.5 E F0(:).24 E(u_int8_t b)108 194.4 Q -.25(va)-.15 G(l;).25 E
.793(The delimiting byte to be used to mark the end of a record for v)133 206.4
R .793(ariable-length records, and the pad)-.25 F .386(character for \214x)133
218.4 R .387(ed-length records.)-.15 F .387(If no v)5.387 F .387
(alue is speci\214ed, ne)-.25 F .387(wlines \(`)-.25 F(`\\n')-.74 E .387
('\) are used to mark the end)-.74 F(of v)133 230.4 Q
(ariable-length records and \214x)-.25 E
(ed-length records are padded with spaces.)-.15 E(char *bfname;)108 247.2 Q
1.152(The recno access method stores the in-memory copies of its records in a \
btree.)133 259.2 R 1.152(If bfname is non-)6.152 F .35(NULL, it speci\214es th\
e name of the btree \214le, as if speci\214ed as the \214le name for a)133
271.2 R F2(db_open)2.851 E F0 .351(of a btree)2.851 F(\214le.)133 283.2 Q
(u_int cachesize;)108 300 Q 3.847(As)133 312 S 1.347
(uggested maximum size, in bytes, of the memory cache.)147.957 312 R 1.347
(This v)6.347 F 1.347(alue is)-.25 F/F3 10/Times-Bold@0 SF(only)3.847 E F0
(advisory)3.847 E 3.846(,a)-.65 G 1.346(nd the)513.934 312 R .693
(access method will allocate more memory rather than f)133 324 R 3.193(ail. If)
-.1 F F2(cac)3.193 E(hesize)-.15 E F0 3.193(is 0)3.193 F .693
(\(no size is speci\214ed\) a)3.193 F(def)133 336 Q(ault size is used.)-.1 E
(u_long \215ags;)108 352.8 Q(The \215ag v)133 364.8 Q(alue is speci\214ed by)
-.25 E F2(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)302.2 364.8 S
(he follo)310.81 364.8 Q(wing v)-.25 E(alues:)-.25 E(R_FIXEDLEN)133 381.6 Q
1.49(The records are \214x)158 393.6 R 1.489(ed-length, not byte delimited.)
-.15 F 1.489(The structure element)6.489 F F2 -.37(re)3.989 G(clen).37 E F0
1.489(speci\214es the)3.989 F .487
(length of the record, and the structure element)158 405.6 R F2(bval)2.987 E F0
.488(is used as the pad character)2.988 F 5.488(.A)-.55 G .788 -.15(ny r)
495.232 405.6 T(ecords,).15 E(inserted into the database, that are less than)
158 417.6 Q F2 -.37(re)2.5 G(clen).37 E F0
(bytes long are automatically padded.)2.5 E(R_NOKEY)133 434.4 Q 1.146
(In the interf)158 446.4 R 1.146(ace speci\214ed by)-.1 F F2(db_open)3.646 E F0
3.646(,t).24 G 1.146(he sequential record retrie)320.816 446.4 R -.25(va)-.25 G
3.646<6c8c>.25 G 1.145(lls in both the caller')449.31 446.4 R(s)-.55 E -.1(ke)
158 458.4 S 4.795(ya)-.05 G 2.295(nd data structures.)181.425 458.4 R 2.295
(If the R_NOKEY \215ag is speci\214ed, the)7.295 F F2(cur)4.795 E(sor)-.1 E F0
2.295(functions are not)4.795 F .621(required to \214ll in the k)158 470.4 R
.921 -.15(ey s)-.1 H 3.121(tructure. This).15 F .621
(permits applications to retrie)3.121 F .92 -.15(ve r)-.25 H .62
(ecords at the end of).15 F(\214les without reading all of the interv)158 482.4
Q(ening records.)-.15 E(R_SN)133 499.2 Q(APSHO)-.35 E(T)-.4 E .029
(This \215ag requires that a snapshot of the \214le be tak)158 511.2 R .029
(en when)-.1 F F2(db_open)2.529 E F0 .029(is called, instead of permit-)2.529 F
(ting an)158 523.2 Q 2.5(yu)-.15 G
(nmodi\214ed records to be read from the original \214le.)197.85 523.2 Q
(int lorder;)108 540 Q .65(The byte order for inte)133 552 R .65
(gers in the stored database metadata.)-.15 F .65
(The number should represent the order)5.65 F .748(as an inte)133 564 R .749
(ger; for e)-.15 F .749(xample, big endian order w)-.15 F .749
(ould be the number 4,321.)-.1 F(If)5.749 E F2(lor)3.249 E(der)-.37 E F0 .749
(is 0 \(no order is)3.249 F(speci\214ed\) the current host order is used.)133
576 Q(u_int psize;)108 592.8 Q .284(The recno access method stores the in-memo\
ry copies of its records in a btree.)133 604.8 R .284(This v)5.284 F .283
(alue is the size)-.25 F .297
(\(in bytes\) of the pages used for nodes in that tree.)133 616.8 R(If)5.297 E
F2(psize)2.797 E F0 .297(is 0 \(no page size is speci\214ed\) a page size)2.797
F(is chosen based on the underlying \214le system I/O block size.)133 628.8 Q
(See)5 E F2(btr)2.5 E(ee)-.37 E F0(\(3\) for more information.).18 E
(size_t reclen;)108 645.6 Q(The length of a \214x)133 657.6 Q
(ed-length record.)-.15 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)
132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 23
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 111.23(DB_RECNO\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 111.23(anual DB_RECNO\(3\))340.17 48 R/F1 9/Times-Bold@0 SF
(DB OPERA)72 84 Q(TIONS)-.855 E F0 .972(The data part of the k)108 96 R -.15
(ey)-.1 G .971(/data pair used by the recno access method is the same as other\
 access methods.).15 F .198(The k)108 108 R .498 -.15(ey i)-.1 H 2.698(sd).15 G
(if)157.504 108 Q 2.698(ferent. The)-.25 F/F2 10/Times-Italic@0 SF(data)2.698 E
F0 .198(\214eld of the k)2.698 F .499 -.15(ey s)-.1 H .199
(hould be a pointer to a memory location of type).15 F F2 -.37(re)2.699 G
(cno_t).37 E F0 2.699(,a).68 G(s)536.11 108 Q .506(de\214ned in the <db)108 120
R .506(.h> include \214le.)-.4 F .506(This type is normally the lar)5.506 F
.506(gest unsigned inte)-.18 F .506(gral type a)-.15 F -.25(va)-.2 G .505
(ilable to the).25 F 2.5(implementation. The)108 132 R F2(size)2.5 E F0
(\214eld of the k)2.5 E .3 -.15(ey s)-.1 H(hould be the size of that type.).15
E 1.944(The record number data structure is either v)108 148.8 R 1.944
(ariable or \214x)-.25 F 1.944
(ed-length records stored in a \215at-\214le format,)-.15 F 1.856
(accessed by the logical record number)108 160.8 R 6.856(.T)-.55 G 1.856(he e)
285.206 160.8 R 1.856(xistence of record number \214v)-.15 F 4.356(er)-.15 G
1.856(equires the e)440.442 160.8 R 1.856(xistence of)-.15 F .875
(records one through four)108 172.8 R 3.375(,a)-.4 G .875
(nd the deletion of record number one causes record number \214v)219.68 172.8 R
3.376(et)-.15 G 3.376(ob)489.928 172.8 S 3.376(er)503.304 172.8 S(enum-)514.45
172.8 Q .283(bered to record number four)108 184.8 R 2.783(,a)-.4 G 2.783(sw)
231.195 184.8 S .283(ell as the cursor)245.088 184.8 R 2.783(,i)-.4 G 2.783(fp)
316.64 184.8 S .282(ositioned after record number one, to shift do)327.753
184.8 R .282(wn one)-.25 F 3.18(record. The)108 196.8 R .68
(creation of record number \214v)3.18 F 3.18(ew)-.15 G .681
(hen records one through four do not e)295.05 196.8 R .681
(xist causes the logical)-.15 F(creation of them with zero-length data.)108
208.8 Q .372(Because there is no meta-data associated with the underlying recn\
o access method \214les, an)108 225.6 R 2.872(yc)-.15 G .372(hanges made)
487.698 225.6 R .191(to the def)108 237.6 R .191(ault v)-.1 F .191
(alues \(e.g. \214x)-.25 F .192(ed record length or byte separator v)-.15 F
.192(alue\) must be e)-.25 F .192(xplicitly speci\214ed each time)-.15 F
(the \214le is opened.)108 249.6 Q 1.037(The functions returned by)108 266.4 R
F2(db_open)3.537 E F0 1.036(for the btree access method are as described in)
3.536 F F2(db_open)3.536 E F0 1.036(\(3\), with the).24 F(follo)108 278.4 Q
(wing e)-.25 E(xceptions and additions:)-.15 E 5.28(type The)108 295.2 R
(type is DB_RECNO.)2.5 E 9.72(put Using)108 312 R(the)2.558 E F2(put)2.558 E F0
(interf)2.559 E .059(ace to create a ne)-.1 F 2.559(wr)-.25 G .059
(ecord will cause the creation of multiple, empty records if the)293.07 312 R
(record number is more than one greater than the lar)133 324 Q
(gest record currently in the database.)-.18 E(The)133 340.8 Q F2(put)2.5 E F0
(function tak)2.5 E(es the follo)-.1 E(wing additional \215ags:)-.25 E
(R_IAFTER)133 357.6 Q 1.225
(Append the data immediately after the data referenced by)158 369.6 R F2 -.1
(ke)3.724 G(y)-.2 E F0 3.724(,c).32 G 1.224(reating a ne)425.354 369.6 R 3.724
(wk)-.25 G -.15(ey)490.046 369.6 S 1.224(/data pair).15 F(.)-.55 E
(The record number of the appended k)158 381.6 Q -.15(ey)-.1 G
(/data pair is returned in the).15 E F2 -.1(ke)2.5 G(y)-.2 E F0(structure.)2.5
E(R_IBEFORE)133 398.4 Q 1.343
(Insert the data immediately before the data referenced by)158 410.4 R F2 -.1
(ke)3.844 G(y)-.2 E F0 3.844(,c).32 G 1.344(reating a ne)424.874 410.4 R 3.844
(wk)-.25 G -.15(ey)489.926 410.4 S 1.344(/data pair).15 F(.)-.55 E
(The record number of the inserted k)158 422.4 Q -.15(ey)-.1 G
(/data pair is returned in the).15 E F2 -.1(ke)2.5 G(y)-.2 E F0(structure.)2.5
E(R_SETCURSOR)133 439.2 Q(Store the k)158 451.2 Q -.15(ey)-.1 G(/data pair).15
E 2.5(,s)-.4 G
(etting or initializing the position of the cursor to reference it.)256.5 451.2
Q 9.17(seq The)108 468 R F2(seq)2.5 E F0(function tak)2.5 E(es the follo)-.1 E
(wing additional \215ags:)-.25 E(R_LAST)133 484.8 Q .04(The last k)158 496.8 R
-.15(ey)-.1 G .04(/data pair of the database is returned, and the cursor is se\
t or initialized to reference).15 F(it.)158 508.8 Q(R_PREV)133 525.6 Q(Retrie)
158 537.6 Q .59 -.15(ve t)-.25 H .29(he k).15 F -.15(ey)-.1 G .29
(/data pair immediately before the cursor).15 F 5.29(.I)-.55 G 2.79(ft)395.73
537.6 S .29(he cursor is not yet set, this is the)404.63 537.6 R
(same as the R_LAST \215ag.)158 549.6 Q .749
(If the database \214le is a character special \214le and no complete k)133
566.4 R -.15(ey)-.1 G .748(/data pairs are currently a).15 F -.25(va)-.2 G
(ilable,).25 E(the)133 578.4 Q F2(seq)2.5 E F0(function returns 2.)2.5 E 4.17
(sync The)108 595.2 R F2(sync)2.5 E F0(function tak)2.5 E(es the follo)-.1 E
(wing additional \215ag:)-.25 E(R_RECNOSYNC)133 612 Q .643
(This \215ag causes the)158 624 R F2(sync)3.143 E F0 .644
(function to apply to the btree \214le which underlies the recno \214le, not)
3.143 F .09(the recno \214le itself.)158 636 R .09(\(See the)5.09 F F2(bfname)
2.59 E F0 .09(\214eld of RECNOINFO structure, abo)2.59 F -.15(ve)-.15 G 2.59
(,f).15 G .09(or more informa-)470.95 636 R(tion.\))158 648 Q F1(ERR)72 664.8 Q
(ORS)-.27 E F0(The)108 676.8 Q F2 -.37(re)3.731 G(cno).37 E F0 1.231
(access method functions may f)3.731 F 1.231(ail and set)-.1 F F2(errno)3.731 E
F0 1.231(for an)3.731 F 3.731(yo)-.15 G 3.731(ft)392.652 676.8 S 1.231
(he errors speci\214ed for the library)402.493 676.8 R(function)108 688.8 Q F2
(db_open)2.5 E F0(\(3\) or the follo).24 E(wing:)-.25 E(4.4 Berk)72 732 Q(ele)
-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E
(2)535 732 Q EP
%%Page: 3 24
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 111.23(DB_RECNO\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 111.23(anual DB_RECNO\(3\))340.17 48 R([EINV)108 84 Q(AL])-1.35 E
(An attempt w)133 96 Q(as made to add a record to a \214x)-.1 E
(ed-length database that w)-.15 E(as too lar)-.1 E(ge to \214t.)-.18 E/F1 9
/Times-Bold@0 SF(SEE ALSO)72 112.8 Q/F2 10/Times-Italic@0 SF(db_btr)108 124.8 Q
(ee)-.37 E F0(\(3\),).18 E F2(db_hash)2.5 E F0(\(3\),).28 E F2(db_loc)2.5 E(k)
-.2 E F0(\(3\),).67 E F2(db_lo)2.5 E(g)-.1 E F0(\(3\),).22 E F2(db_mpool)2.5 E
F0(\(3\),).51 E F2(db_open)2.5 E F0(\(3\),).24 E F2(db_txn)2.5 E F0(\(3\)).24 E
F2 2.755(Document Pr)108 148.8 R 2.755
(ocessing in a Relational Database System)-.45 F F0 5.254(,M).32 G 2.754
(ichael Stonebrak)362.134 148.8 R(er)-.1 E 5.254(,H)-.4 G 2.754(eidi Stettner)
454.062 148.8 R 5.254(,J)-.4 G(oseph)516.67 148.8 Q
(Kalash, Antonin Guttman, Nadene L)108 160.8 Q
(ynn, Memorandum No. UCB/ERL M82/32, May 1982.)-.55 E F1 -.09(BU)72 177.6 S(GS)
.09 E F0(The)108 189.6 Q F2(sync)3.616 E F0(function')3.616 E 3.616(sR)-.55 G
1.116(_RECNOSYNC interf)198.838 189.6 R 1.117
(ace is a kluge, and will be deleted in a future v)-.1 F 1.117(ersion of the)
-.15 F(interf)108 201.6 Q(ace.)-.1 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G
(istrib)132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(3)535 732 Q EP
%%Page: 1 25
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_TXN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_TXN\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72
84 S(ME).18 E F0(db_txn \255 transaction management functions)108 96 Q F1
(SYNOPSIS)72 112.8 Q/F2 10/Times-Bold@0 SF(#include <db)108 124.8 Q(.h>)-.4 E
(#include <db_lock.h>)108 136.8 Q(int)108 160.8 Q(txn_cr)108 172.8 Q
(eate\(const char *path, mode_t mode, u_int maxtxns, u_int \215ags\);)-.18 E
(TXNMGR *)108 196.8 Q(txn_open\(const char *path, DBT *logp, LOCK_T)108 208.8 Q
(ABLE_T *lockp,)-.9 E(int \(*r)158 220.8 Q(eco)-.18 E -.1(ve)-.1 G
(r\)\(DBT *lsn, DBT *log_entry).1 E 2.5(,i)-.55 G(nt isundo\)\);)340.11 220.8 Q
(TXN *)108 244.8 Q(txn_begin\(TXNMGR *txnp\);)108 256.8 Q(int)108 280.8 Q
(txn_commit\(TXN *tid\);)108 292.8 Q(int)108 316.8 Q(txn_pr)108 328.8 Q(epar)
-.18 E(e\(TXN *tid\);)-.18 E(int)108 352.8 Q(txn_abort\(TXN *tid\);)108 364.8 Q
(int)108 388.8 Q(txn_close\(TXNMGR *txnp\);)108 400.8 Q(int)108 424.8 Q
(txn_unlink\(const char *path, int f)108 436.8 Q(or)-.25 E(ce\);)-.18 E F1
(DESCRIPTION)72 453.6 Q F0(speci\214c details of the transaction support.)108
465.6 Q/F3 10/Times-Italic@0 SF(Db_txn)108 482.4 Q F0 .034
(is the library interf)2.534 F .034(ace that pro)-.1 F .034
(vides transaction semantics.)-.15 F .034(Full transaction support is pro)5.034
F .034(vided by a)-.15 F .722(collection of modules that pro)108 494.4 R .723
(vide well de\214ned interf)-.15 F .723
(aces to the services required for transaction process-)-.1 F 3.488(ing. These)
108 506.4 R .988(services are reco)3.488 F -.15(ve)-.15 G .988(ry \(see).15 F
F3(db_lo)3.488 E(g)-.1 E F0 .988(\(3\)\), concurrenc).22 F 3.488(yc)-.15 G .988
(ontrol \(see)371.864 506.4 R F3(db_loc)3.487 E(k)-.2 E F0 .987
(\(3\)\), and the manage-).67 F 2.201(ment of shared data \(see)108 518.4 R F3
(db_mpool)4.701 E F0 4.701(\(3\)\). T).51 F 2.202
(ransaction semantics can be applied to the access methods)-.35 F(described in)
108 530.4 Q F3(db)2.5 E F0(\(3\) through function call parameters.).23 E .629(\
The model intended for transactional use \(and that is used by the access meth\
ods\) is that write-ahead log-)108 547.2 R .047(ging is pro)108 559.2 R .047
(vided by)-.15 F F3(db_lo)2.547 E(g)-.1 E F0 .047
(\(3\) to record both before- and after).22 F .048(-image logging.)-.2 F .048
(Locking follo)5.048 F .048(ws a tw)-.25 F(o-phase)-.1 E
(protocol and is implemented by)108 571.2 Q F3(db_loc)2.5 E(k)-.2 E F0(\(3\).)
.67 E .549(The function)108 588 R F3(txn_cr)3.049 E(eate)-.37 E F0 .549
(creates and initializes the transaction re)3.049 F .548
(gion identi\214ed by the)-.15 F F3(path)3.048 E F0(directory)3.048 E 5.548(.T)
-.65 G(his)528.33 588 Q .572(directory must already e)108 600 R .572(xist when)
-.15 F F3(txn_cr)3.072 E(eate)-.37 E F0 .572(is called.)3.072 F .572
(If the transaction re)5.572 F .572(gion identi\214ed by)-.15 F F3(path)3.072 E
F0(already)3.072 E -.15(ex)108 612 S 1.78(ists, then).15 F F3(txn_cr)4.28 E
(eate)-.37 E F0 1.78(returns success without further action.)4.28 F 1.78
(The \214les associated with the transaction)6.78 F(re)108 624 Q .293
(gion are created in the directory speci\214ed by)-.15 F F3(path)2.793 E F0
5.293(.\().28 G .293(The group of the created \214les is based on the system)
327.657 624 R .781(and directory def)108 636 R .781
(aults, and is not further speci\214ed by)-.1 F F3(txn_cr)3.281 E(eate)-.37 E
F0 3.281(.\) All).18 F .78(\214les created by)3.28 F F3(txn_cr)3.28 E(eate)-.37
E F0 .78(are cre-)3.28 F .048(ated with mode)108 648 R F3(mode)2.548 E F0 .049
(\(as described in)2.548 F F3 -.15(ch)2.549 G(mod).15 E F0 .049
(\(2\)\) and modi\214ed by the process' umask v).77 F .049(alue \(see)-.25 F F3
(umask)2.549 E F0(\(2\)\).).67 E(An)108 660 Q 2.5(yn)-.15 G(ecessary)132.57 660
Q 2.5(,a)-.65 G(ssociated log and lock re)175.23 660 Q
(gions are created as well \(see)-.15 E F3(db_lo)2.5 E(g)-.1 E F0(\(3\) and).22
E F3(db_loc)2.5 E(k)-.2 E F0(\(3\)\).).67 E(The)108 676.8 Q F3(maxtxns)4.191 E
F0(ar)4.191 E 1.691(gument speci\214es the maximum number of simultaneous tran\
sactions that are supported.)-.18 F .229
(This bounds the size of backing \214les and is used to deri)108 688.8 R .529
-.15(ve l)-.25 H .229(imits for the size of the lock re).15 F .229
(gion and log\214les.)-.15 F(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)
132.57 732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(1)535 732 Q EP
%%Page: 2 26
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_TXN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_TXN\(3\))340.17 48 R(When there are more than)108 84
Q/F1 10/Times-Italic@0 SF(maxtxns)2.5 E F0(concurrent transactions, calls to)
2.5 E F1(txn_be)2.5 E(gin)-.4 E F0(may f)2.5 E(ail.)-.1 E(Def)108 100.8 Q .847
(ault locking and logging protocols are pro)-.1 F .846
(vided only if the backing \214les e)-.15 F 3.346(xist. If)-.15 F .846
(the backing \214les do)3.346 F 1.433(not e)108 112.8 R 1.433(xist, the)-.15 F
F1<8d61>3.933 E(gs)-.1 E F0 1.434
(parameter must indicate both a logging mode and locking mode speci\214ed by)
3.933 F F1(or)3.934 E F0('ing).73 E(together at most one \215ag from each of t\
he TXN_LOCK and TXN_LOG classes as follo)108 124.8 Q(ws:)-.25 E(TXN_LOCK_2PL)
108 141.6 Q(Use tw)133 153.6 Q(o-phase locking.)-.1 E(TXN_LOCK_OPTIMISTIC)108
170.4 Q(Use optimistic locking \(not currently implemented\).)133 182.4 Q
(TXN_LOG_REDO)108 199.2 Q(Pro)133 211.2 Q
(vide redo-only logging \(not currently implemented\).)-.15 E(TXN_LOG_UNDO)108
228 Q(Pro)133 240 Q(vide undo-only logging \(not currently implemented\).)-.15
E(TXN_LOG_UNDOREDO)108 256.8 Q(Pro)133 268.8 Q
(vide undo/redo write-ahead logging.)-.15 E(The function)108 285.6 Q F1(txn_cr)
2.5 E(eate)-.37 E F0(returns -1 on f)2.5 E(ailure, setting)-.1 E F1(errno)2.5 E
F0 2.5(,a).18 G(nd 0 on success.)351.83 285.6 Q 1.892(The function)108 302.4 R
F1(txn_open)4.392 E F0 1.892(returns a pointer to the transaction re)4.392 F
1.892(gion identi\214ed by)-.15 F F1(path)4.392 E F0 4.392(,w).28 G 1.892
(hich must ha)476.016 302.4 R -.15(ve)-.2 G .239
(already been created by a call to)108 314.4 R F1(txn_cr)2.739 E(eate)-.37 E F0
5.239(.T).18 G .239(he process must ha)296.88 314.4 R .539 -.15(ve p)-.2 H .239
(ermission to read and write \214les with).15 F -.25(ow)108 326.4 S .327
(ners, groups and permissions as described for).25 F F1(txn_cr)2.826 E(eate)
-.37 E F0 5.326(.T).18 G(he)362.624 326.4 Q F1(txn_open)2.826 E F0 .326
(function returns NULL on f)2.826 F(ail-)-.1 E(ure, setting)108 338.4 Q F1
(errno)2.5 E F0(.).18 E(The)108 355.2 Q F1 -.37(re)3.181 G(co).37 E(ver)-.1 E
F0(ar)3.181 E .681(gument speci\214es a function that is called by)-.18 F F1
(txn_abort)3.181 E F0 .682(during transaction abort.)3.182 F .682(This func-)
5.682 F(tion tak)108 367.2 Q(es three ar)-.1 E(guments:)-.18 E 10.83(lsn A)108
384 R(log sequence number \(LSN\).)2.5 E(log_entry)108 400.8 Q 2.5(Al)133 412.8
S(og record.)145.5 412.8 Q(isundo)108 429.6 Q(An undo \215ag set to 0 if the o\
peration is a redo and set to 1 if the operation an undo.)133 441.6 Q 1.498
(As discussed in the)108 458.4 R F1(db_lo)3.998 E 3.998(g\()-.1 G(3\))228.44
458.4 Q F0 1.497(manual page, the application is responsible for pro)3.997 F
1.497(viding an)-.15 F 3.997(yn)-.15 G(ecessary)506.13 458.4 Q .486
(structure to the log record.)108 470.4 R -.15(Fo)5.486 G 2.986(re).15 G .487
(xample, the application must understand what part of the log record is an)
242.256 470.4 R(operation code, what part is redo information, and what part i\
s undo information.)108 482.4 Q(The)108 499.2 Q F1(txn_be)2.785 E(gin)-.4 E F0
.285(function creates a ne)2.785 F 2.784(wt)-.25 G .284
(ransaction in the designated transaction manager)264.018 499.2 R 2.784(,r)-.4
G .284(eturning a pointer)468.332 499.2 R
(to a TXN that uniquely identi\214es it.)108 511.2 Q(The)108 528 Q F1
(txn_commit)2.615 E F0 .115(function ends the transaction speci\214ed by the)
2.615 F F1(tid)2.615 E F0(ar)2.615 E 2.615(gument. An)-.18 F 2.615(yl)-.15 G
.115(ocks held by the transac-)440.12 528 R(tion are released.)108 540 Q
(If logging is enabled, a commit log record is written and \215ushed to disk.)5
E(The)108 556.8 Q F1(txn_abort)2.889 E F0 .389
(function causes an abnormal termination of the transaction.)2.889 F .388
(If logging is enabled, the log is)5.389 F 2.312(played backw)108 568.8 R 2.312
(ards and an)-.1 F 4.812(yr)-.15 G(eco)228.628 568.8 Q -.15(ve)-.15 G 2.312
(ry operations are initiated through the).15 F F1 -.37(re)4.813 G(co).37 E(ver)
-.1 E F0 2.313(function speci\214ed to)4.813 F F1(txn_open)108 580.8 Q F0 5(.A)
.24 G(fter reco)159.62 580.8 Q -.15(ve)-.15 G
(ry is completed, all locks held by the transaction are released.).15 E(The)108
597.6 Q F1(txn_close)3.824 E F0 1.323
(function detaches a process from the transaction en)3.823 F 1.323
(vironment speci\214ed by the TXNMGR)-.4 F(pointer)108 609.6 Q 5.261(.A)-.55 G
.261(ll mapped re)150.761 609.6 R .261(gions are unmapped and an)-.15 F 2.761
(ya)-.15 G .262(llocated resources are freed.)323.638 609.6 R(An)5.262 E 2.762
(yu)-.15 G .262(ncommitted trans-)466.688 609.6 R(actions are aborted.)108
621.6 Q .69(The function)108 638.4 R F1(txn_unlink)3.19 E F0(destro)3.19 E .69
(ys the transaction re)-.1 F .69(gion identi\214ed by the directory)-.15 F F1
(path)3.19 E F0 3.19(,r).28 G(emo)472.1 638.4 Q .69(ving all \214les)-.15 F
1.78(used to implement the transaction re)108 650.4 R 4.28(gion. \(The)-.15 F
(directory)4.28 E F1(path)4.28 E F0 1.78(is not remo)4.28 F -.15(ve)-.15 G 4.28
(d.\) If).15 F 1.78(there are processes)4.28 F .553(which ha)108 662.4 R .853
-.15(ve c)-.2 H(alled).15 E F1(txn_open)3.052 E F0 .552(without calling)3.052 F
F1(txn_close)3.052 E F0 .552
(\(i.e., there are processes currently using the transac-)3.052 F .135(tion re)
108 674.4 R(gion\),)-.15 E F1(txn_unlink)2.635 E F0 .135(will f)2.635 F .135
(ail without further action, unless the force \215ag is set, in which case)-.1
F F1(txn_unlink)2.636 E F0 1.67(will attempt to delete the transaction re)108
686.4 R 1.67(gion \214les re)-.15 F -.05(ga)-.15 G 1.67(rdless of an).05 F 4.17
(yp)-.15 G 1.67(rocesses still using the transaction)397.22 686.4 R(4.4 Berk)72
732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315(ution August)-.2 F
(1, 1995)2.5 E(2)535 732 Q EP
%%Page: 3 27
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_TXN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_TXN\(3\))340.17 48 R(re)108 84 Q 2.996(gion. An)-.15
F 2.996(ya)-.15 G .496(ccesses to a remo)165.902 84 R -.15(ve)-.15 G 2.997(dt)
.15 G .497(ransaction re)257.007 84 R .497(gion will lik)-.15 F .497
(ely result in une)-.1 F .497(xpected beha)-.15 F(vior)-.2 E 5.497(.T)-.55 G
.497(he func-)506.463 84 R(tion)108 96 Q/F1 10/Times-Italic@0 SF(txn_unlink)2.5
E F0(returns -1 on f)2.5 E(ailure, setting)-.1 E F1(errno)2.5 E F0 2.5(,a).18 G
(nd 0 on success.)316.39 96 Q .51(In the case of catastrophic or system f)108
112.8 R .51(ailure, it is possible to clean up a transaction re)-.1 F .51
(gion by remo)-.15 F .51(ving all)-.15 F .34
(of the \214les in the directory speci\214ed to the)108 124.8 R F1(txn_cr)2.841
E(eate)-.37 E F0 .341(function, as transaction re)2.841 F .341
(gion \214les are ne)-.15 F -.15(ve)-.25 G 2.841(rc).15 G(reated)515.57 124.8 Q
(in an)108 136.8 Q 2.5(yd)-.15 G(irectory other than the one speci\214ed to)
140.07 136.8 Q F1(txn_cr)2.5 E(eate)-.37 E F0(.).18 E(The)108 153.6 Q F1
(txn_pr)3.42 E(epar)-.37 E(e)-.37 E F0 .92(function initiates the be)3.42 F
.919(ginning of a tw)-.15 F 3.419(op)-.1 G .919(hase commit.)352.206 153.6 R
.919(In a distrib)5.919 F .919(uted transaction, the)-.2 F .185
(prepare directi)108 165.6 R .485 -.15(ve s)-.25 H .185
(hould be issued to all participating transaction managers.).15 F .185
(Each manager must tak)5.185 F 2.685(ew)-.1 G(hat-)524.45 165.6 Q -2.15 -.25
(ev e)108 177.6 T 2.5(ra).25 G
(ction is necessary to guarantee that a future call to)131.75 177.6 Q F1
(txn_commit)2.5 E F0(on the speci\214ed)2.5 E F1(tid)2.5 E F0(will succeed.)2.5
E/F2 9/Times-Bold@0 SF(SYSTEM INTEGRA)72 194.4 Q(TION)-.855 E F0 .28
(This model can be applied to data bases other than the pro)108 206.4 R .279
(vided access methods.)-.15 F -.15(Fo)5.279 G 2.779(re).15 G .279
(xample, consider an)459.182 206.4 R .15(application that pro)108 218.4 R .15
(vides transaction semantics to data stored in re)-.15 F .15
(gular \214les accessed using the)-.15 F F1 -.37(re)2.65 G(ad).37 E F0 .15
(\(2\) and).77 F F1(write)108 230.4 Q F0 .708(\(2\) system calls.).18 F .707
(The operations for which transaction protection is desired are brack)5.708 F
.707(eted by calls to)-.1 F F1(txn_be)108 242.4 Q(gin)-.4 E F0(and)2.5 E F1
(txn_commit)2.5 E F0(.).68 E .606
(Before data are referenced, a call is made to the lock manager)108 259.2 R(,)
-.4 E F1(db_loc)3.106 E(k)-.2 E F0 3.106(,f).67 G .606
(or a lock of the appropriate type)408.064 259.2 R .719
(\(e.g. read\) on the object being lock)108 271.2 R 3.218(ed. The)-.1 F .718
(object might be a page in the \214le, a byte, a range of bytes, or)3.218 F
.572(some k)108 283.2 R -.15(ey)-.1 G 5.572(.B)-.5 G .573
(efore a write is performed, the application mak)160.464 283.2 R .573
(es a call to the log manager)-.1 F(,)-.4 E F1(db_lo)3.073 E(g)-.1 E F0 3.073
(,t).22 G 3.073(or)506.387 283.2 S(ecord)517.79 283.2 Q .522
(enough information to redo the operation in case of f)108 295.2 R .522
(ailure after commit and to undo the operation in case)-.1 F .609(of abort.)108
307.2 R .609
(After the log message is written, the write system calls are issued.)5.609 F
.61(After all requests are issued,)5.61 F .518(the application calls)108 319.2
R F1(txn_commit)3.017 E F0 5.517(.W).68 G(hen)256.84 319.2 Q F1(txn_commit)
3.017 E F0 .517(returns, the caller is guaranteed that all necessary log)3.017
F(writes ha)108 331.2 Q .3 -.15(ve b)-.2 H(een written to disk.).15 E 1.081
(At an)108 348 R 3.581(yt)-.15 G 1.081(ime, the application may call)142.232
348 R F1(txn_abort)3.581 E F0 3.581(,w).68 G 1.081
(hich will result in the appropriate calls to the)318.828 348 R F1 -.37(re)
3.582 G(co).37 E(ver)-.1 E F0 .278(routine to restore the `)108 360 R
(`database')-.74 E 2.778('t)-.74 G 2.778(oac)246.48 360 S .278
(onsistent pre-transaction state.)265.916 360 R .277(\(The reco)5.277 F -.15
(ve)-.15 G 2.777(rr).15 G .277(outine must be able to)450.562 360 R
(either reapply or undo the update depending on the conte)108 372 Q
(xt, for each dif)-.15 E(ferent type of log record.\))-.25 E .746
(If the application should crash, the reco)108 388.8 R -.15(ve)-.15 G .746
(ry process uses the).15 F F1(db_lo)3.246 E(g)-.1 E F0(interf)3.246 E .746
(ace to read the log and call the)-.1 F F1 -.37(re)108 400.8 S(co).37 E(ver)-.1
E F0(routine to restore the database to a consistent state.)2.5 E(The)108 417.6
Q F1(txn_pr)3.098 E(epar)-.37 E(e)-.37 E F0 .598(function pro)3.098 F .598
(vides the core functionality to implement distrib)-.15 F .597
(uted transactions, b)-.2 F .597(ut it does)-.2 F .36
(not actually manage the noti\214cation of distrib)108 429.6 R .36
(uted transaction managers.)-.2 F .36(The caller is responsible for issu-)5.36
F(ing)108 441.6 Q F1(txn_pr)2.82 E(epar)-.37 E(e)-.37 E F0 .32
(calls to all sites participating in the transaction.)2.82 F .319
(If all responses are positi)5.319 F -.15(ve)-.25 G 2.819(,t).15 G .319
(he caller can)488.832 441.6 R .822(issue a)108 453.6 R F1(txn_commit)3.322 E
F0 5.822(.I).68 G 3.322(fa)198.076 453.6 S 1.122 -.15(ny o)209.168 453.6 T
3.322(ft).15 G .822(he responses are ne)236.772 453.6 R -.05(ga)-.15 G(ti).05 E
-.15(ve)-.25 G 3.322(,t).15 G .823(he caller should issue a)349.15 453.6 R F1
(txn_abort)3.323 E F0 5.823(.I).68 G 3.323(ng)499.747 453.6 S(eneral,)513.07
453.6 Q(the)108 465.6 Q F1(txn_pr)2.5 E(epar)-.37 E(e)-.37 E F0
(call requires that the transaction log be \215ushed to disk.)2.5 E .821
(The structure of the transaction support allo)108 482.4 R .821
(ws application designers to trade of)-.25 F 3.32(fp)-.25 G .82
(erformance and protec-)445.07 482.4 R 3.948(tion. Since)108 494.4 R 1.448
(DB manages man)3.948 F 3.948(ys)-.15 G 1.448(tructures in shared memory)245.36
494.4 R 3.948(,i)-.65 G 1.448(ts information is subject to corruption by)
367.982 494.4 R 1.306(applications when the library is link)108 506.4 R 1.306
(ed directly with the application.)-.1 F -.15(Fo)6.306 G 3.805(rt).15 G 1.305
(his reason, DB is designed to)416.815 506.4 R(allo)108 518.4 Q 3.367(wc)-.25 G
.867(ompilation into a separate serv)137.777 518.4 R .868
(er process that may be accessed via a sock)-.15 F .868(et interf)-.1 F 3.368
(ace. In)-.1 F .868(this w)3.368 F(ay)-.1 E(DB')108 530.4 Q 2.828(sd)-.55 G
.328(ata structures are protected from application code, b)136.388 530.4 R .328
(ut communication o)-.2 F -.15(ve)-.15 G .327(rhead is increased.).15 F(When)
5.327 E(applications are trusted, DB may be compiled directly into the applica\
tion for increased performance.)108 542.4 Q F2(ERR)72 559.2 Q(ORS)-.27 E F0
(The)108 571.2 Q F1(txn_cr)4.113 E(eate)-.37 E F0 1.613(function may f)4.113 F
1.613(ail and set)-.1 F F1(errno)4.113 E F0 1.614(for an)4.113 F 4.114(yo)-.15
G 4.114(ft)349.022 571.2 S 1.614
(he errors speci\214ed for the library functions)359.246 571.2 R F1(open)108
583.2 Q F0(\(2\),).24 E F1(write)2.5 E F0(\(2\),).18 E F1(malloc)2.5 E F0
(\(3\),).31 E F1(loc)2.5 E(k_cr)-.2 E(eate)-.37 E F0(\(3\), and).18 E F1(lo)2.5
E(g_cr)-.1 E(eate)-.37 E F0(\(3\).).18 E(The)108 600 Q F1(txn_open)2.509 E F0
.009(function may f)2.509 F .009(ail and set)-.1 F F1(errno)2.508 E F0 .008
(to an)2.508 F 2.508(yo)-.15 G 2.508(ft)323.916 600 S .008
(he errors speci\214ed for the library functions)332.534 600 R F1(open)2.508 E
F0(\(2\),).24 E F1(write)108 612 Q F0(\(2\),).18 E F1(malloc)2.5 E F0(\(3\),)
.31 E F1(loc)2.5 E(k_open)-.2 E F0(\(3\), and).24 E F1(lo)2.5 E(g_open)-.1 E F0
(\(3\).).24 E(The)108 628.8 Q F1(txn_be)2.671 E(gin)-.4 E F0 .171
(function may f)2.671 F .171(ail and set)-.1 F F1(errno)2.671 E F0 .171
(to ENOSPC indicating that the maximum number of concur)2.671 F(-)-.2 E
(rent transactions has been reached.)108 640.8 Q(The)108 657.6 Q F1(txn_commit)
2.5 E F0(function may f)2.5 E(ail and set)-.1 E F1(errno)2.5 E F0(to EINV)2.5 E
(AL indicating that the transaction w)-1.35 E(as aborted.)-.1 E(The)108 674.4 Q
F1(txn_close)4.582 E F0 2.082(function may f)4.582 F 2.081(ail and set)-.1 F F1
(errno)4.581 E F0 2.081(to an)4.581 F 4.581(yo)-.15 G 4.581(ft)345.753 674.4 S
2.081(he errors speci\214ed for the library functions)356.444 674.4 R F1(close)
108 686.4 Q F0(\(2\),).18 E F1 -.37(re)2.5 G(ad).37 E F0(\(2\),).77 E F1(write)
2.5 E F0(\(2\),).18 E F1(fr)2.5 E(ee)-.37 E F0(\(3\),).18 E F1(fsync)2.5 E F0
(\(2\),).31 E F1(loc)2.5 E(k_close)-.2 E F0(\(3\) or).18 E F1(lo)2.5 E(g_close)
-.1 E F0(\(3\).).18 E(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57
732 Q 99.315(ution August)-.2 F(1, 1995)2.5 E(3)535 732 Q EP
%%Page: 4 28
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.57(DB_TXN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.57(anual DB_TXN\(3\))340.17 48 R(The)108 84 Q/F1 10
/Times-Italic@0 SF(txn_unlink)4.319 E F0 1.819(function may f)4.319 F 1.819
(ail and set)-.1 F F1(errno)4.319 E F0 1.819(to an)4.319 F 4.319(yo)-.15 G 4.32
(ft)347.58 84 S 1.82(he errors speci\214ed for the library functions)358.01 84
R F1(unlink)108 96 Q F0(\(2\),).67 E F1(loc)2.5 E(k_unlink)-.2 E F0(\(3\), and)
.67 E F1(lo)2.5 E(g_unlink)-.1 E F0(\(3\), or the follo).67 E(wing:)-.25 E([EB)
108 112.8 Q(USY])-.1 E(The transaction re)133 124.8 Q(gion w)-.15 E
(as in use and the force \215ag w)-.1 E(as not set.)-.1 E/F2 9/Times-Bold@0 SF
(SEE ALSO)72 141.6 Q F1(db_btr)108 153.6 Q(ee)-.37 E F0(\(3\),).18 E F1
(db_hash)2.5 E F0(\(3\),).28 E F1(db_loc)2.5 E(k)-.2 E F0(\(3\),).67 E F1
(db_lo)2.5 E(g)-.1 E F0(\(3\),).22 E F1(db_mpool)2.5 E F0(\(3\),).51 E F1
(db_open)2.5 E F0(\(3\),).24 E F1(db_r)2.5 E(ecno)-.37 E F0(\(3\)).18 E F1 .904
(LIBTP: P)108 177.6 R(ortable)-.8 E 3.404(,M)-.1 G .904(odular T)189.738 177.6
R -.15(ra)-.55 G .904(nsactions for UNIX).15 F F0 3.404(,M).94 G(ar)328.884
177.6 Q .904(go Seltzer)-.18 F 3.403(,M)-.4 G .903
(ichael Olson, USENIX proceedings,)392.041 177.6 R -.4(Wi)108 189.6 S
(nter 1992.).4 E F2 -.09(BU)72 206.4 S(GS).09 E F0(The)108 218.4 Q F1(maxtxns)
2.792 E F0 .292(parameter is a kluge, and should be deleted in f)2.792 F -.2
(avo)-.1 G 2.793(ro).2 G 2.793(fd)378.448 218.4 S .293(ynamically e)389.571
218.4 R .293(xpanding the transaction)-.15 F(re)108 230.4 Q(gion.)-.15 E
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 99.315
(ution August)-.2 F(1, 1995)2.5 E(4)535 732 Q EP
%%Trailer
end
%%EOF
