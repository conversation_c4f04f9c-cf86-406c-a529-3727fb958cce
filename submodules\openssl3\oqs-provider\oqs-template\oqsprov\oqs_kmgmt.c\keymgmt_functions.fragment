{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
MAKE_SIG_KEYMGMT_FUNCTIONS({{variant['name']}})
     {%- for classical_alg in variant['mix_with'] %}
MAKE_SIG_KEYMGMT_FUNCTIONS({{ classical_alg['name'] }}_{{variant['name']}})
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}
{% for kem in config['kems'] %}
MAKE_KEM_KEYMGMT_FUNCTIONS({{kem['name_group']}}, {{kem['oqs_alg']}}, {{kem['bit_security']}})
{%- endfor %}

