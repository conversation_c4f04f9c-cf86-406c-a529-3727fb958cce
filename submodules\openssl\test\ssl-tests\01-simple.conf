# Generated with generate_ssl_tests.pl

num_tests = 3

test-0 = 0-default
test-1 = 1-Server signature algorithms bug
test-2 = 2-verify-cert
# ===========================================================

[0-default]
ssl_conf = 0-default-ssl

[0-default-ssl]
server = 0-default-server
client = 0-default-client

[0-default-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-default-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-Server signature algorithms bug]
ssl_conf = 1-Server signature algorithms bug-ssl

[1-Server signature algorithms bug-ssl]
server = 1-Server signature algorithms bug-server
client = 1-Server signature algorithms bug-client

[1-Server signature algorithms bug-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = PSS+SHA512:RSA+SHA512
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-Server signature algorithms bug-client]
CipherString = DEFAULT
SignatureAlgorithms = PSS+SHA256:RSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


# ===========================================================

[2-verify-cert]
ssl_conf = 2-verify-cert-ssl

[2-verify-cert-ssl]
server = 2-verify-cert-server
client = 2-verify-cert-client

[2-verify-cert-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-verify-cert-client]
CipherString = DEFAULT
VerifyMode = Peer

[test-2]
ExpectedClientAlert = UnknownCA
ExpectedResult = ClientFail


