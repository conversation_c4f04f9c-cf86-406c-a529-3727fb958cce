<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_f Struct Reference</h1><!-- doxytag: class="cc_credentials_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_credentials_t. For more information see <a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__f.html#6cc7338e31fd5f2436fb15b23506f57d">release</a> )(<a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> io_credentials)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gab5cad8ca82847950956b0f493132c14">cc_credentials_release()</a></b>: Release memory associated with a cc_credentials_t object.  <a href="#6cc7338e31fd5f2436fb15b23506f57d"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__f.html#8511e9a8220b2003a1c66b314ca6bc9f">compare</a> )(<a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_credentials, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_compare_to_credentials, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g39ae30e49dba65b87c6b9794f20fb784">cc_credentials_compare()</a></b>: Compare two credentials objects.  <a href="#8511e9a8220b2003a1c66b314ca6bc9f"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="6cc7338e31fd5f2436fb15b23506f57d"></a><!-- doxytag: member="cc_credentials_f::release" ref="6cc7338e31fd5f2436fb15b23506f57d" args=")(cc_credentials_t io_credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__f.html#6cc7338e31fd5f2436fb15b23506f57d">release</a>)(<a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> io_credentials)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gab5cad8ca82847950956b0f493132c14">cc_credentials_release()</a></b>: Release memory associated with a cc_credentials_t object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_credentials</em>&nbsp;</td><td>the credentials object to release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="8511e9a8220b2003a1c66b314ca6bc9f"></a><!-- doxytag: member="cc_credentials_f::compare" ref="8511e9a8220b2003a1c66b314ca6bc9f" args=")(cc_credentials_t in_credentials, cc_credentials_t in_compare_to_credentials, cc_uint32 *out_equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__f.html#8511e9a8220b2003a1c66b314ca6bc9f">compare</a>)(<a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_credentials, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_compare_to_credentials, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g39ae30e49dba65b87c6b9794f20fb784">cc_credentials_compare()</a></b>: Compare two credentials objects. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_credentials</em>&nbsp;</td><td>a credentials object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_compare_to_credentials</em>&nbsp;</td><td>a credentials object to compare with <em>in_credentials</em>. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_equal</em>&nbsp;</td><td>on exit, whether or not the two credentials objects refer to the same credentials in the cache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
