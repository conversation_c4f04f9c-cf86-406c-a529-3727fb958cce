#! /usr/bin/env perl
# Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


use strict;
use warnings;

use OpenSSL::Test::Utils;
use OpenSSL::Test qw/:DEFAULT srctop_file/;

setup("test_req");

plan tests => 14;

require_ok(srctop_file('test','recipes','tconversion.pl'));

open RND, ">>", ".rnd";
print RND "string to make the random number generator think it has randomness";
close RND;

# What type of key to generate?
my @req_new;
if (disabled("rsa")) {
    @req_new = ("-newkey", "dsa:".srctop_file("apps", "dsa512.pem"));
} else {
    @req_new = ("-new");
    note("There should be a 2 sequences of .'s and some +'s.");
    note("There should not be more that at most 80 per line");
}

# Check for duplicate -addext parameters, and one "working" case.
my @addext_args = ( "openssl", "req", "-new", "-out", "testreq.pem",
    "-config", srctop_file("test", "test.cnf"), @req_new );
my $val = "subjectAltName=DNS:example.com";
my $val2 = " " . $val;
my $val3 = $val;
$val3 =~ s/=/    =/;
ok( run(app([@addext_args, "-addext", $val])));
ok(!run(app([@addext_args, "-addext", $val, "-addext", $val])));
ok(!run(app([@addext_args, "-addext", $val, "-addext", $val2])));
ok(!run(app([@addext_args, "-addext", $val, "-addext", $val3])));
ok(!run(app([@addext_args, "-addext", $val2, "-addext", $val3])));

subtest "generating certificate requests with RSA" => sub {
    plan tests => 6;

    SKIP: {
        skip "RSA is not supported by this OpenSSL build", 2
            if disabled("rsa");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq.pem", "-utf8",
                    "-key", srctop_file("test", "testrsa.pem")])),
           "Generating request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq.pem", "-noout"])),
           "Verifying signature on request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq_withattrs_pem.pem", "-utf8",
                    "-key", srctop_file("test", "testrsa_withattrs.pem")])),
           "Generating request from a key with extra attributes - PEM");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq_withattrs_pem.pem", "-noout"])),
           "Verifying signature on request from a key with extra attributes - PEM");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq_withattrs_der.pem", "-utf8",
                    "-key", srctop_file("test", "testrsa_withattrs.der"),
	            "-keyform", "DER"])),
           "Generating request from a key with extra attributes - PEM");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq_withattrs_der.pem", "-noout"])),
           "Verifying signature on request from a key with extra attributes - PEM");
    }
};

subtest "generating certificate requests with DSA" => sub {
    plan tests => 2;

    SKIP: {
        skip "DSA is not supported by this OpenSSL build", 2
            if disabled("dsa");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq.pem", "-utf8",
                    "-key", srctop_file("test", "testdsa.pem")])),
           "Generating request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq.pem", "-noout"])),
           "Verifying signature on request");
    }
};

subtest "generating certificate requests with ECDSA" => sub {
    plan tests => 2;

    SKIP: {
        skip "ECDSA is not supported by this OpenSSL build", 2
            if disabled("ec");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq.pem", "-utf8",
                    "-key", srctop_file("test", "testec-p256.pem")])),
           "Generating request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq.pem", "-noout"])),
           "Verifying signature on request");
    }
};

subtest "generating certificate requests with Ed25519" => sub {
    plan tests => 2;

    SKIP: {
        skip "Ed25519 is not supported by this OpenSSL build", 2
            if disabled("ec");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq.pem", "-utf8",
                    "-key", srctop_file("test", "tested25519.pem")])),
           "Generating request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq.pem", "-noout"])),
           "Verifying signature on request");
    }
};

subtest "generating certificate requests with Ed448" => sub {
    plan tests => 2;

    SKIP: {
        skip "Ed448 is not supported by this OpenSSL build", 2
            if disabled("ec");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-new", "-out", "testreq.pem", "-utf8",
                    "-key", srctop_file("test", "tested448.pem")])),
           "Generating request");

        ok(run(app(["openssl", "req",
                    "-config", srctop_file("test", "test.cnf"),
                    "-verify", "-in", "testreq.pem", "-noout"])),
           "Verifying signature on request");
    }
};

subtest "generating certificate requests" => sub {
    plan tests => 2;

    ok(run(app(["openssl", "req", "-config", srctop_file("test", "test.cnf"),
                @req_new, "-out", "testreq.pem"])),
       "Generating request");

    ok(run(app(["openssl", "req", "-config", srctop_file("test", "test.cnf"),
                "-verify", "-in", "testreq.pem", "-noout"])),
       "Verifying signature on request");
};

my @openssl_args = ("req", "-config", srctop_file("apps", "openssl.cnf"));

run_conversion('req conversions',
               "testreq.pem");
run_conversion('req conversions -- testreq2',
               srctop_file("test", "testreq2.pem"));

unlink "testkey.pem", "testreq.pem", "testreq_withattrs_pem.pem", "testreq_withattrs_der.pem";

sub run_conversion {
    my $title = shift;
    my $reqfile = shift;

    subtest $title => sub {
        run(app(["openssl", @openssl_args,
                 "-in", $reqfile, "-inform", "p",
                 "-noout", "-text"],
                stderr => "req-check.err", stdout => undef));
        open DATA, "req-check.err";
        SKIP: {
            plan skip_all => "skipping req conversion test for $reqfile"
                if grep /Unknown Public Key/, map { s/\R//; } <DATA>;

            tconversion("req", $reqfile, @openssl_args);
        }
        close DATA;
        unlink "req-check.err";

        done_testing();
    };
}
