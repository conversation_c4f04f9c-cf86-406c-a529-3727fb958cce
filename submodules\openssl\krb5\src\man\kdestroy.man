.\" Man page generated from reStructuredText.
.
.TH "KDESTROY" "1" " " "1.17.1" "MIT Kerberos"
.SH NAME
kdestroy \- destroy Kerberos tickets
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkdestroy\fP
[\fB\-A\fP]
[\fB\-q\fP]
[\fB\-c\fP \fIcache_name\fP]
.SH DESCRIPTION
.sp
The kdestroy utility destroys the user\(aqs active Kerberos authorization
tickets by overwriting and deleting the credentials cache that
contains them.  If the credentials cache is not specified, the default
credentials cache is destroyed.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-A\fP
Destroys all caches in the collection, if a cache collection is
available.  May be used with the \fB\-c\fP option to specify the
collection to be destroyed.
.TP
\fB\-q\fP
Run quietly.  Normally kdestroy beeps if it fails to destroy the
user\(aqs tickets.  The \fB\-q\fP flag suppresses this behavior.
.TP
\fB\-c\fP \fIcache_name\fP
Use \fIcache_name\fP as the credentials (ticket) cache name and
location; if this option is not used, the default cache name and
location are used.
.sp
The default credentials cache may vary between systems.  If the
\fBKRB5CCNAME\fP environment variable is set, its value is used to
name the default ticket cache.
.TP
\fB\-p\fP \fIprinc_name\fP
If a cache collection is available, destroy the cache for
\fIprinc_name\fP instead of the primary cache.  May be used with the
\fB\-c\fP option to specify the collection to be searched.
.UNINDENT
.SH NOTE
.sp
Most installations recommend that you place the kdestroy command in
your .logout file, so that your tickets are destroyed automatically
when you log out.
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH FILES
.INDENT 0.0
.TP
.B \fB@CCNAME@\fP
Default location of Kerberos 5 credentials cache
.UNINDENT
.SH SEE ALSO
.sp
kinit(1), klist(1), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2019, MIT
.\" Generated by docutils manpage writer.
.
