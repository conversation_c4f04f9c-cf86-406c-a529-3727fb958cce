mydir=tests$(S)misc
BUILDTOP=$(REL)..$(S)..

OBJS=\
	test_getpw.o \
	test_chpw_message.o

SRCS=\
	$(srcdir)/test_getpw.c \
	$(srcdir)/test_chpw_message.c \
	$(srcdir)/test_getsockname.c \
	$(srcdir)/test_cxx_krb5.cpp \
	$(srcdir)/test_cxx_k5int.cpp \
	$(srcdir)/test_cxx_gss.cpp \
	$(srcdir)/test_cxx_rpc.cpp \
	$(srcdir)/test_cxx_kadm5.cpp

all: test_getpw test_chpw_message

check: test_getpw test_chpw_message test_cxx_krb5 test_cxx_gss test_cxx_rpc test_cxx_k5int test_cxx_kadm5
	$(RUN_TEST) ./test_getpw
	$(RUN_TEST) ./test_chpw_message
	$(RUN_TEST) ./test_cxx_krb5
	$(RUN_TEST) ./test_cxx_k5int
	$(RUN_TEST) ./test_cxx_gss
	$(RUN_TEST) ./test_cxx_rpc
	$(RUN_TEST) ./test_cxx_kadm5

test_getpw: $(OUTPRE)test_getpw.$(OBJEXT) $(SUPPORT_DEPLIB)
	$(CC_LINK) $(ALL_CFLAGS) -o test_getpw $(OUTPRE)test_getpw.$(OBJEXT) $(SUPPORT_LIB)

test_chpw_message: $(OUTPRE)test_chpw_message.$(OBJEXT) $(SUPPORT_DEPLIB)
	$(CC_LINK) $(ALL_CFLAGS) -o test_chpw_message $(OUTPRE)test_chpw_message.$(OBJEXT) $(KRB5_BASE_LIBS) $(LIBS)

test_getsockname: $(OUTPRE)test_getsockname.$(OBJEXT)
	$(CC_LINK) $(ALL_CFLAGS) -o test_getsockname $(OUTPRE)test_getsockname.$(OBJEXT) $(LIBS)

test_cxx_krb5: $(OUTPRE)test_cxx_krb5.$(OBJEXT) $(KRB5_DEPLIB)
	$(CXX_LINK) $(ALL_CXXFLAGS) -o test_cxx_krb5 $(OUTPRE)test_cxx_krb5.$(OBJEXT) $(KRB5_BASE_LIBS) $(LIBS)
test_cxx_k5int: $(OUTPRE)test_cxx_k5int.$(OBJEXT) $(KRB5_DEPLIB)
	$(CXX_LINK) $(ALL_CXXFLAGS) -o test_cxx_k5int $(OUTPRE)test_cxx_k5int.$(OBJEXT) $(KRB5_BASE_LIBS) $(LIBS)
test_cxx_gss: $(OUTPRE)test_cxx_gss.$(OBJEXT)
	$(CXX_LINK) $(ALL_CXXFLAGS) -o test_cxx_gss $(OUTPRE)test_cxx_gss.$(OBJEXT) $(LIBS)
test_cxx_rpc: $(OUTPRE)test_cxx_rpc.$(OBJEXT) $(GSSRPC_DEPLIBS)
	$(CXX_LINK) $(ALL_CXXFLAGS) -o test_cxx_rpc $(OUTPRE)test_cxx_rpc.$(OBJEXT) $(GSSRPC_LIBS) $(KRB5_BASE_LIBS) $(LIBS)
test_cxx_kadm5: $(OUTPRE)test_cxx_kadm5.$(OBJEXT) $(KADMCLNT_DEPLIBS)
	$(CXX_LINK) $(ALL_CXXFLAGS) -o test_cxx_kadm5 $(OUTPRE)test_cxx_kadm5.$(OBJEXT) $(KADMCLNT_LIBS) $(KRB5_BASE_LIBS) $(LIBS)

test_cxx_krb5.$(OBJEXT): test_cxx_krb5.cpp
test_cxx_gss.$(OBJEXT): test_cxx_gss.cpp
test_cxx_rpc.$(OBJEXT): test_cxx_rpc.cpp
test_cxx_kadm5.$(OBJEXT): test_cxx_kadm5.cpp

install:

clean:
	$(RM) test_getpw test_chpw_message test_cxx_krb5 test_cxx_gss test_cxx_k5int test_cxx_rpc test_cxx_kadm5 *.o

