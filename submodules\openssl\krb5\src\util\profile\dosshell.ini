EGA.INI/VGA.INI
****************  WARNING  ********************
This file may contain lines with more than 256
characters. Some editors will truncate or split
these lines. If you are not sure whether your
editor can handle long lines, exit now without
saving the file.

Note: The editor which is invoked by the
	  MS-DOS 5.0 EDIT command can be used
	  to edit this file.
****************  NOTE  ***********************
Everything up to the first left square bracket
character is considered a comment.
***********************************************
[savestate]
screenmode = text
resolution = low
startup = filemanager
filemanagermode = shared
sortkey = name
pause = disabled
explicitselection = disabled
swapmouse = disabled
tasklist = disabled
switching = disabled
[programstarter]
currentcolor = Ocean
filemanager = enabled
command = enabled
group = 
{
	program = 
	{
	command = COMMAND
	title = Command Prompt
	help = Starts the MS-DOS command prompt where you can type any MS-DOS command.^m^mTo return to MS-DOS Shell from the command line:^m^m1. Type exit^m2. Press ENTER.^m^mRelated Topic^m   " More on Command Prompt "~$129~
	pause = disabled
	}
	program = 
	{
	command = EDIT %1
	title = Editor
	help = Starts MS-DOS Editor, a text editor you can use to create and modify text files. After you choose Editor, you can specify the file you want to work with in a dialog box.^m^mRelated Topic^m   " More on Editor "~$130~
	pause = disabled
	dialog = 
	{
		title = File to Edit
		info = Enter the name of the file to edit. To start MS-DOS Editor without opening a file, press ENTER.
		prompt = File to edit?
		parameter = %1
	}
	}
	program = 
	{
	command = QBASIC %1
	title = MS-DOS QBasic
	help = Starts MS-DOS QBasic, a programming environment you can use to create, modify, run, and debug programs. After you choose MS-DOS QBasic, you can specify the file you want to work with in a dialog box.^m^mRelated Topic^m   " More on MS-DOS QBasic "~$131~
	pause = disabled
	dialog = 
	{
		title = MS-DOS QBasic File
		info = Enter the name of a QBasic program. To start without opening a program, press ENTER.
		prompt = QBasic File?
		parameter = %1
	}
	}
	group = 
	{
	title = Disk Utilities
	help = Displays program items you can choose to manage your disks. You can also choose to open the Main group or any group you may have added.
	program = 
	{
		command = diskcopy %1
		title = Disk Copy
		pause = enabled
		dialog = 
		{
		title = Disk Copy
		info = Enter the source and destination drives.
		prompt = Parameters . . .
		default = a: b:
		parameter = %1
		}
		help = Temporarily leaves MS-DOS Shell to copy the contents of a floppy disk to another floppy disk. After you choose Disk Copy, a dialog box suggests parameters and switches you can either accept or replace.^m^mRelated Topic^m   " More on Disk Copy "~$132~
	}
	program = 
	{
		command = backup %1
		title = Backup Fixed Disk
		pause = enabled
		dialog = 
		{
		title = Backup Fixed Disk
		info = Enter the source and destination drives.
		prompt = Parameters . . .
		default = c:\*.* a: /s
		parameter = %1 
		}
		help = Temporarily leaves MS-DOS Shell to copy files from one disk to another. After you choose Backup Fixed Disk, a dialog box suggests parameters and switches you can either accept or replace.^m^mRelated Topic^m   " More on Backup Fixed Disk "~$133~
	}
	program = 
	{
		command = restore %1
		title = Restore Fixed Disk
		pause = enabled
		dialog = 
		{
		title = Restore Fixed Disk
		info = Enter the source and destination drives.
		prompt = Parameters . . .
		parameter = %1
		}
		help = Temporarily leaves MS-DOS Shell to restore files that were backed up. After you choose Restore Fixed Disk, a dialog box suggests parameters and switches you can either accept or replace.^m^mRelated Topic^m   " More on Restore Fixed Disk "~$134~
	}
	program = 
	{
		command = format %1 /q
		title = Quick Format
		pause = enabled
		dialog = 
		{
		title = Quick Format
		info = Enter the drive to quick format.
		prompt = Parameters . . .
		default = a:
		parameter = %1
		}
		help = Temporarily leaves MS-DOS Shell to prepare a disk to accept MS-DOS files. After you choose Quick Format, a dialog box suggests parameters and switches you can either accept or replace.^m^mRelated Topic^m   " More on Quick Format "~$136~
		screenmode = text
		alttab = enabled
		altesc = enabled
		ctrlesc = enabled
		prevent = enabled
	}
	program = 
	{
		command = format %1
		title = Format
		pause = enabled
		dialog = 
		{
		title = Format
		info = Enter the drive to format.
		prompt = Parameters . . .
		default = a:
		parameter = %1
		}
		help = Temporarily leaves MS-DOS Shell to prepare a disk to accept MS-DOS files. After you choose Format, a dialog box suggests parameters and switches you can either accept or replace.^m^mRelated Topic^m   " More on Format "~$135~
	}
	program = 
	{
		command = undelete %1
		title = Undelete
		help = Recovers deleted files.^m^mWARNING: If your disk is full or if you are using task swapping, using this program item may render some deleted files unrecoverable.^m^mRelated Procedure^m   " Restoring Deleted Files "~I155~
		pause = enabled
		dialog = 
		{
		title = Undelete
		info = WARNING! This action may cause the permanent loss of some deleted files.  Press F1 for more information.
		prompt = Parameters . . .
		default = /LIST
		parameter = %1
		}
		screenmode = text
		alttab = enabled
		altesc = enabled
		ctrlesc = enabled
		prevent = enabled
	}
	}
}
color = 
{
	selection = 
	{
	title = Basic Blue
	foreground = 
	{
		base = black
		highlight = brightwhite
		selection = brightwhite
		alert = brightred
		menubar = black
		menu = black
		disabled = white
		accelerator = cyan
		dialog = black
		button = black
		elevator = white
		titlebar = black
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = blue
		selection = black
		alert = brightwhite
		menubar = white
		menu = brightwhite
		disabled = brightwhite
		accelerator = brightwhite
		dialog = brightwhite
		button = white
		elevator = white
		titlebar = white
		scrollbar = black
		borders = brightwhite
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = brightblack
	}
	}
	selection = 
	{
	title = Ocean
	foreground = 
	{
		base = black
		highlight = brightwhite
		selection = brightwhite
		alert = brightwhite
		menubar = black
		menu = black
		disabled = white
		accelerator = brightwhite
		dialog = black
		button = black
		elevator = white
		titlebar = black
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = blue
		selection = black
		alert = white
		menubar = cyan
		menu = cyan
		disabled = cyan
		accelerator = cyan
		dialog = cyan
		button = brightwhite
		elevator = white
		titlebar = white
		scrollbar = black
		borders = black
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = brightcyan
	}
	}
	selection = 
	{
	title = Monochrome-2 Colors
	foreground = 
	{
		base = black
		highlight = white
		selection = white
		alert = black
		menubar = black
		menu = black
		disabled = white
		accelerator = white
		dialog = black
		button = white
		elevator = black
		titlebar = white
		scrollbar = white
		borders = black
		drivebox = black
		driveicon = black
	}
	background = 
	{
		base = white
		highlight = black
		selection = black
		alert = white
		menubar = white
		menu = white
		disabled = white
		accelerator = black
		dialog = white
		button = black
		elevator = white
		titlebar = black
		scrollbar = black
		borders = black
		drivebox = white
		driveicon = white
	}
	}
	selection = 
	{
	title = Monochrome-4 Colors
	foreground = 
	{
		base = black
		highlight = brightwhite
		selection = brightwhite
		alert = black
		menubar = black
		menu = black
		disabled = white
		accelerator = brightwhite
		dialog = black
		button = black
		elevator = white
		titlebar = black
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = brightblack
		selection = brightblack
		alert = brightwhite
		menubar = brightwhite
		menu = white
		disabled = white
		accelerator = brightblack
		dialog = brightwhite
		button = white
		elevator = white
		titlebar = white
		scrollbar = black
		borders = black
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = black
	}
	}
	selection = 
	{
	title = Reverse
	foreground = 
	{
		base = white
		highlight = black
		selection = black
		alert = white
		menubar = white
		menu = white
		disabled = black
		accelerator = black
		dialog = white
		button = black
		elevator = white
		titlebar = black
		scrollbar = black
		borders = white
		drivebox = white
		driveicon = white
	}
	background = 
	{
		base = black
		highlight = white
		selection = white
		alert = black
		menubar = black
		menu = black
		disabled = black
		accelerator = white
		dialog = black
		button = white
		elevator = black
		titlebar = white
		scrollbar = white
		borders = black
		drivebox = black
		driveicon = black
	}
	}
	selection = 
	{
	title = Hot Pink
	foreground = 
	{
		base = black
		highlight = brightwhite
		selection = brightwhite
		alert = brightmagenta
		menubar = black
		menu = black
		disabled = white
		accelerator = magenta
		dialog = black
		button = brightwhite
		elevator = white
		titlebar = brightwhite
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = brightmagenta
		selection = magenta
		alert = brightwhite
		menubar = brightwhite
		menu = brightwhite
		disabled = brightwhite
		accelerator = brightwhite
		dialog = brightwhite
		button = magenta
		elevator = white
		titlebar = magenta
		scrollbar = black
		borders = black
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = brightred
	}
	}
	selection = 
	{
	title = Emerald City
	foreground = 
	{
		base = black
		highlight = black
		selection = brightwhite
		alert = green
		menubar = black
		menu = black
		disabled = white
		accelerator = green
		dialog = black
		button = brightwhite
		elevator = white
		titlebar = brightwhite
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = brightgreen
		selection = green
		alert = brightwhite
		menubar = brightwhite
		menu = brightwhite
		disabled = brightwhite
		accelerator = brightwhite
		dialog = brightwhite
		button = green
		elevator = white
		titlebar = green
		scrollbar = black
		borders = black
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = brightcyan
	}
	}
	selection = 
	{
	title = Turquoise
	foreground = 
	{
		base = black
		highlight = brightwhite
		selection = brightwhite
		alert = brightred
		menubar = brightwhite
		menu = black
		disabled = white
		accelerator = white
		dialog = black
		button = black
		elevator = white
		titlebar = black
		scrollbar = brightwhite
		borders = black
		drivebox = black
		driveicon = black
		cursor = black
	}
	background = 
	{
		base = brightwhite
		highlight = brightblue
		selection = black
		alert = brightwhite
		menubar = brightcyan
		menu = brightcyan
		disabled = brightcyan
		accelerator = brightcyan
		dialog = brightcyan
		button = brightwhite
		elevator = white
		titlebar = white
		scrollbar = black
		borders = black
		drivebox = brightwhite
		driveicon = brightwhite
		cursor = cyan
	}
	}
}

associations = 
{
	association = 
	{
	program = EDIT
	extension = TXT
	}
	association = 
	{
	program = QBASIC /run 
	extension = BAS
	}
}
