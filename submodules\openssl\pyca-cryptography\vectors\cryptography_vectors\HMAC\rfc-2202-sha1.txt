# 3. Test Cases for HMAC-SHA-1

Len = 64
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
# "Hi There"
Msg = 4869205468657265
MD = b617318655057264e28bc0b6fb378c8ef146be00

Len = 224
# "Jefe"
Key = 4a656665
# "what do ya want for nothing?"
Msg = 7768617420646f2079612077616e7420666f72206e6f7468696e673f
MD = effcdf6ae5eb2fa2d27416d5f184df9c259a7c79

Len = 400
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Msg = dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
MD = 125d7342b9ac11cd91a39af48aa17b4f63f175d3

Len = 400
Key = 0102030405060708090a0b0c0d0e0f10111213141516171819
Msg = cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd
MD = 4c9007f4026250c6bc8414f9bf50c86c2d7235da

Len = 160
Key = 0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
# Note: We test the full MD rather than the truncated one in this test
# "Test With Truncation"
Msg = 546573742057697468205472756e636174696f6e
MD = 4c1a03424b55e07fe7f27be1d58bb9324a9a5a04

Len = 432
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
# "Test Using Larger Than Block-Size Key - Hash Key First"
Msg = 54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374
MD = aa4ae5e15272d00e95705637ce8a3b55ed402112

Len = 584
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
# "Test Using Larger Than Block-Size Key and Larger Than One Block-Size Data"
Msg = 54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461
MD = e8e99d0f45237d786d6bbaa7965c7808bbff1a91
