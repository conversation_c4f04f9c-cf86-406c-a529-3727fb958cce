=pod

=head1 NAME

SSL_CTX_sessions - access internal session cache

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 struct lhash_st *SSL_CTX_sessions(SSL_CTX *ctx);

=head1 DESCRIPTION

SSL_CTX_sessions() returns a pointer to the lhash databases containing the
internal session cache for B<ctx>.

=head1 NOTES

The sessions in the internal session cache are kept in an
L<LHASH(3)> type database. It is possible to directly
access this database e.g. for searching. In parallel, the sessions
form a linked list which is maintained separately from the
L<LHASH(3)> operations, so that the database must not be
modified directly but by using the
L<SSL_CTX_add_session(3)> family of functions.

=head1 RETURN VALUES

SSL_CTX_sessions() returns a pointer to the lhash of B<SSL_SESSION>.

=head1 SEE ALSO

L<ssl(7)>, L<LHASH(3)>,
L<SSL_CTX_add_session(3)>,
L<SSL_CTX_set_session_cache_mode(3)>

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
