# Generated with generate_ssl_tests.pl

num_tests = 17

test-0 = 0-sni-session-ticket
test-1 = 1-sni-session-ticket
test-2 = 2-sni-session-ticket
test-3 = 3-sni-session-ticket
test-4 = 4-sni-session-ticket
test-5 = 5-sni-session-ticket
test-6 = 6-sni-session-ticket
test-7 = 7-sni-session-ticket
test-8 = 8-sni-session-ticket
test-9 = 9-sni-session-ticket
test-10 = 10-sni-session-ticket
test-11 = 11-sni-session-ticket
test-12 = 12-sni-session-ticket
test-13 = 13-sni-session-ticket
test-14 = 14-sni-session-ticket
test-15 = 15-sni-session-ticket
test-16 = 16-sni-session-ticket
# ===========================================================

[0-sni-session-ticket]
ssl_conf = 0-sni-session-ticket-ssl

[0-sni-session-ticket-ssl]
server = 0-sni-session-ticket-server
client = 0-sni-session-ticket-client
server2 = 0-sni-session-ticket-server2

[0-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
SessionTicketExpected = No
server = 0-sni-session-ticket-server-extra
client = 0-sni-session-ticket-client-extra

[0-sni-session-ticket-server-extra]
BrokenSessionTicket = Yes

[0-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[1-sni-session-ticket]
ssl_conf = 1-sni-session-ticket-ssl

[1-sni-session-ticket-ssl]
server = 1-sni-session-ticket-server
client = 1-sni-session-ticket-client
server2 = 1-sni-session-ticket-server2

[1-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 1-sni-session-ticket-server-extra
client = 1-sni-session-ticket-client-extra

[1-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[1-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[2-sni-session-ticket]
ssl_conf = 2-sni-session-ticket-ssl

[2-sni-session-ticket-ssl]
server = 2-sni-session-ticket-server
client = 2-sni-session-ticket-client
server2 = 2-sni-session-ticket-server2

[2-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 2-sni-session-ticket-server-extra
client = 2-sni-session-ticket-client-extra

[2-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[2-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[3-sni-session-ticket]
ssl_conf = 3-sni-session-ticket-ssl

[3-sni-session-ticket-ssl]
server = 3-sni-session-ticket-server
client = 3-sni-session-ticket-client
server2 = 3-sni-session-ticket-server2

[3-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 3-sni-session-ticket-server-extra
client = 3-sni-session-ticket-client-extra

[3-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[3-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[4-sni-session-ticket]
ssl_conf = 4-sni-session-ticket-ssl

[4-sni-session-ticket-ssl]
server = 4-sni-session-ticket-server
client = 4-sni-session-ticket-client
server2 = 4-sni-session-ticket-server2

[4-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 4-sni-session-ticket-server-extra
client = 4-sni-session-ticket-client-extra

[4-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[4-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[5-sni-session-ticket]
ssl_conf = 5-sni-session-ticket-ssl

[5-sni-session-ticket-ssl]
server = 5-sni-session-ticket-server
client = 5-sni-session-ticket-client
server2 = 5-sni-session-ticket-server2

[5-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 5-sni-session-ticket-server-extra
client = 5-sni-session-ticket-client-extra

[5-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[5-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[6-sni-session-ticket]
ssl_conf = 6-sni-session-ticket-ssl

[6-sni-session-ticket-ssl]
server = 6-sni-session-ticket-server
client = 6-sni-session-ticket-client
server2 = 6-sni-session-ticket-server2

[6-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 6-sni-session-ticket-server-extra
client = 6-sni-session-ticket-client-extra

[6-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[6-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[7-sni-session-ticket]
ssl_conf = 7-sni-session-ticket-ssl

[7-sni-session-ticket-ssl]
server = 7-sni-session-ticket-server
client = 7-sni-session-ticket-client
server2 = 7-sni-session-ticket-server2

[7-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 7-sni-session-ticket-server-extra
client = 7-sni-session-ticket-client-extra

[7-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[7-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[8-sni-session-ticket]
ssl_conf = 8-sni-session-ticket-ssl

[8-sni-session-ticket-ssl]
server = 8-sni-session-ticket-server
client = 8-sni-session-ticket-client
server2 = 8-sni-session-ticket-server2

[8-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 8-sni-session-ticket-server-extra
client = 8-sni-session-ticket-client-extra

[8-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[8-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[9-sni-session-ticket]
ssl_conf = 9-sni-session-ticket-ssl

[9-sni-session-ticket-ssl]
server = 9-sni-session-ticket-server
client = 9-sni-session-ticket-client
server2 = 9-sni-session-ticket-server2

[9-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 9-sni-session-ticket-server-extra
client = 9-sni-session-ticket-client-extra

[9-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[9-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[10-sni-session-ticket]
ssl_conf = 10-sni-session-ticket-ssl

[10-sni-session-ticket-ssl]
server = 10-sni-session-ticket-server
client = 10-sni-session-ticket-client
server2 = 10-sni-session-ticket-server2

[10-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 10-sni-session-ticket-server-extra
client = 10-sni-session-ticket-client-extra

[10-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[10-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[11-sni-session-ticket]
ssl_conf = 11-sni-session-ticket-ssl

[11-sni-session-ticket-ssl]
server = 11-sni-session-ticket-server
client = 11-sni-session-ticket-client
server2 = 11-sni-session-ticket-server2

[11-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 11-sni-session-ticket-server-extra
client = 11-sni-session-ticket-client-extra

[11-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[11-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[12-sni-session-ticket]
ssl_conf = 12-sni-session-ticket-ssl

[12-sni-session-ticket-ssl]
server = 12-sni-session-ticket-server
client = 12-sni-session-ticket-client
server2 = 12-sni-session-ticket-server2

[12-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 12-sni-session-ticket-server-extra
client = 12-sni-session-ticket-client-extra

[12-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[12-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[13-sni-session-ticket]
ssl_conf = 13-sni-session-ticket-ssl

[13-sni-session-ticket-ssl]
server = 13-sni-session-ticket-server
client = 13-sni-session-ticket-client
server2 = 13-sni-session-ticket-server2

[13-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 13-sni-session-ticket-server-extra
client = 13-sni-session-ticket-client-extra

[13-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[13-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[14-sni-session-ticket]
ssl_conf = 14-sni-session-ticket-ssl

[14-sni-session-ticket-ssl]
server = 14-sni-session-ticket-server
client = 14-sni-session-ticket-client
server2 = 14-sni-session-ticket-server2

[14-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 14-sni-session-ticket-server-extra
client = 14-sni-session-ticket-client-extra

[14-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[14-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[15-sni-session-ticket]
ssl_conf = 15-sni-session-ticket-ssl

[15-sni-session-ticket-ssl]
server = 15-sni-session-ticket-server
client = 15-sni-session-ticket-client
server2 = 15-sni-session-ticket-server2

[15-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = Success
ExpectedServerName = server1
SessionIdExpected = Yes
SessionTicketExpected = No
server = 15-sni-session-ticket-server-extra
client = 15-sni-session-ticket-client-extra

[15-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[15-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[16-sni-session-ticket]
ssl_conf = 16-sni-session-ticket-ssl

[16-sni-session-ticket-ssl]
server = 16-sni-session-ticket-server
client = 16-sni-session-ticket-client
server2 = 16-sni-session-ticket-server2

[16-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-sni-session-ticket-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedResult = Success
ExpectedServerName = server2
SessionIdExpected = Yes
SessionTicketExpected = No
server = 16-sni-session-ticket-server-extra
client = 16-sni-session-ticket-client-extra

[16-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[16-sni-session-ticket-client-extra]
ServerName = server2


