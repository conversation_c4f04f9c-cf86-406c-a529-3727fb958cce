// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "p2psocket.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

static bool certVerifyCallback(P2P_SOCKET soc, const char* x509) {
    printf("Certificate received: %p\n", x509);
    return true;
}

int main(int argc, char* argv[]) {
    printf("P2PSocket Test Program\n");
    
    SocketOptions options = {0};
    options.mode = MODE_CLIENT;
    options.type = SOCKET_SSL;
    options.cert_verify = certVerifyCallback;

    P2P_SOCKET soc = P2pCreate(&options);
    if (soc == NULL) {
        printf("Failed to create socket\n");
        return 1;
    }
    
    printf("Socket created successfully\n");
    
    P2pSetConnTimeout(soc, 5000); 
    
    P2pClose(soc);
    
    printf("Socket closed\n");
    
    return 0;
}
