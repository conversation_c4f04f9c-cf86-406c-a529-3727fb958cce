=pod

=head1 NAME

EVP_MD-MDC2 - The MDC2 EVP_MD implementation

=head1 DESCRIPTION

Support for computing MDC2 digests through the B<EVP_MD> API.

=head2 Identity

This implementation is only available with the legacy provider, and is
identified with the name "MDC2".

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head2 Settable Context Parameters

This implementation supports the following L<OSSL_PARAM(3)> entries,
settable for an B<EVP_MD_CTX> with L<EVP_MD_CTX_set_params(3)>:

=over 4

=item "pad-type" (B<OSSL_DIGEST_PARAM_PAD_TYPE>) <unsigned integer>

Sets the padding type to be used.
Normally the final MDC2 block is padded with zeros.
If the pad type is set to 2 then the final block is padded with 0x80 followed by
zeros.

=back

=head1 SEE ALSO

L<EVP_MD_CTX_set_params(3)>, L<provider-digest(7)>, L<OSSL_PROVIDER-legacy(7)>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
