#!/usr/bin/tclsh
lappend auto_path [file dirname [info script]]
package require ossltest
cd $::test::dir
start_tests "Тесты на команду cms - вторая подпись" 

test "Creating CA" {
	makeCA
} 0 1

makeFile cms_signed2.dat "Test data for 2 signatures"


foreach length {256 512} {

test "Creating users $length" {
	makeRegisteredUser U_cms_1_$length gost2012_$length:A CN USER1_$<NAME_EMAIL>
	makeRegisteredUser U_cms_2_$length gost2012_$length:A CN USER2_$<NAME_EMAIL>
} 0 1

test -createsfiles cms_signed2_1_$length.asn "Signing in DER format with 1st signature" {
	openssl "cms -sign -binary -outform der -inform der -nodetach -inkey U_cms_1_$length/seckey.pem -signer U_cms_1_$length/cert.pem -in cms_signed2.dat -out cms_signed2_1_$length.asn"
	file isfile cms_signed2_1_$length.asn
} 0 1

test -createsfiles cms_signed2_2_$length.asn "Signing in DER format with 2nd signature" {
	openssl "cms -resign -binary -outform der -inform der -nodetach -inkey U_cms_2_$length/seckey.pem -signer U_cms_2_$length/cert.pem -in cms_signed2_1_$length.asn -out cms_signed2_2_$length.asn"
	file isfile cms_signed2_2_$length.asn
} 0 1

test -createsfiles {was_signed.dat signer.certs} "Verifying signature in DER format" {
	grep "Verif" [openssl "cms -verify -inform der -in cms_signed2_2_$length.asn -noverify -signer signer.certs -out was_signed.dat"]
} 0 {CMS Verification successful
}

test "Signed data is extracted correctly" {
	string eq [getFile cms_signed2.dat] [getFile was_signed.dat]
} 0 1

### Test extracted sertificates

test "Extracting signer certificates" {
	set i 0
	set subjs {}
	set certs [regexp -all -inline -- {-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----} [getFile signer.certs]]
	foreach cert $certs {
		makeFile cert[incr i].pem $cert
		lappend subjs [regsub -all " = " [grep subject [openssl "x509 -in cert$i.pem -subject -noout"]] "=" ]
	}
	lsort $subjs
} 0  "{subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER1_$length, emailAddress=<EMAIL>
} {subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER2_$length, emailAddress=<EMAIL>
}"

test -createsfiles cms_signed2_1_$length\_op.msg "Signing opaque in S/MIME format with 1st signature" {
	openssl "cms -sign -binary -inform der -nodetach -inkey U_cms_1_$length/seckey.pem -signer U_cms_1_$length/cert.pem -in cms_signed2.dat -out cms_signed2_1_$length\_op.msg"
	file isfile cms_signed2_1_$length\_op.msg
} 0 1

test -createsfiles cms_signed2_2_$length\_op.msg "Signing opaque in S/MIME format with 2nd signature" {
	openssl "cms -resign -binary -nodetach -inkey U_cms_2_$length/seckey.pem -signer U_cms_2_$length/cert.pem -in cms_signed2_1_$length\_op.msg -out cms_signed2_2_$length\_op.msg"
	file isfile cms_signed2_2_$length\_op.msg
} 0 1

test -createsfiles {was_signed.dat signer.certs} "Verifying opaque signature in S/MIME format" {
	grep "Verif" [openssl "cms -verify -in cms_signed2_2_$length\_op.msg -noverify -signer signer.certs -out was_signed.dat"]
} 0 {CMS Verification successful
}

test "Signed data is extracted correctly" {
	string eq [getFile cms_signed2.dat] [getFile was_signed.dat]
} 0 1

### Test extracted sertificates

test "Extracting signer certificates" {
	set i 0
	set subjs {}
	set certs [regexp -all -inline -- {-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----} [getFile signer.certs]]
	foreach cert $certs {
		makeFile cert[incr i].pem $cert
		lappend subjs [regsub -all " = " [grep subject [openssl "x509 -in cert$i.pem -subject -noout"]] "=" ]
	}
	lsort $subjs
} 0 "{subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER1_$length, emailAddress=<EMAIL>
} {subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER2_$length, emailAddress=<EMAIL>
}"

test -createsfiles cms_signed2_1_$length\_det.asn "Signing detached in DER format with 1st signature" {
	openssl "cms -sign -binary -outform der -inkey U_cms_1_$length/seckey.pem -signer U_cms_1_$length/cert.pem -in cms_signed2.dat -out cms_signed2_1_$length\_det.asn"
	file isfile cms_signed2_1_$length\_det.asn
} 0 1

test -createsfiles cms_signed2_2_$length\_det.asn "Signing detached in DER format with 2nd signature" {
	openssl "cms -resign -binary -inkey U_cms_2_$length/seckey.pem -signer U_cms_2_$length/cert.pem -in cms_signed2_1_$length\_det.asn -content cms_signed2.dat -inform der -outform der -out cms_signed2_2_$length\_det.asn"
	file isfile cms_signed2_2_$length\_det.asn
} 0 1

test -createsfiles {was_signed.dat signer.certs} "Verifying detached signature in DER format" {
	grep "Verif" [openssl "cms -verify -in cms_signed2_2_$length\_det.asn -noverify -signer signer.certs -out was_signed.dat -content signed2.dat -inform der"]
} 0 {CMS Verification successful
}

test "Signed data is extracted correctly" {
	string eq [getFile cms_signed2.dat] [getFile was_signed.dat]
} 0 1

### Test extracted sertificates

test "Extracting signer certificates" {
	set i 0
	set subjs {}
	set certs [regexp -all -inline -- {-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----} [getFile signer.certs]]
	foreach cert $certs {
		makeFile cert_asn[incr i].pem $cert
		lappend subjs [regsub -all " = " [grep subject [openssl "x509 -in cert$i.pem -subject -noout"]] "=" ]
	}
	lsort  $subjs
} 0  "{subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER1_$length, emailAddress=<EMAIL>
} {subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER2_$length, emailAddress=<EMAIL>
}"

test -createsfiles cms_signed2_1_$length.msg "Signing in S/MIME format with 1st signature" {
	openssl "cms -sign -binary -inform der -inkey U_cms_1_$length/seckey.pem -signer U_cms_1_$length/cert.pem -in cms_signed2.dat -out cms_signed2_1_$length.msg"
	file isfile cms_signed2_1_$length.msg
} 0 1

test -createsfiles cms_signed2_2_$length.msg "Signing in S/MIME format with 2nd signature" {
	grep "SMIME" [openssl "cms -resign -binary -inkey U_cms_2_$length/seckey.pem -signer U_cms_2_$length/cert.pem -in cms_signed2_1_$length.msg -inform smime -out cms_signed2_2_$length.msg"]
} 0 ""

test -createsfiles {was_signed.dat  signer.certs} "Verifying signature in S/MIME format" {
	grep "Verif" [openssl "cms -verify -in cms_signed2_2_$length.msg -noverify -signer signer.certs -out was_signed.dat -inform smime"]
} 0 {CMS Verification successful
}

test "Signed data is extracted correctly" {
	string eq [getFile cms_signed2.dat] [getFile was_signed.dat]
} 0 1

### Test extracted sertificates

test "Extracting signer certificates" {
	set i 0
	set subjs {}
	set certs [regexp -all -inline -- {-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----} [getFile signer.certs]]
	foreach cert $certs {
		makeFile cert_cms[incr i].pem $cert
		lappend subjs [regsub -all " = " [grep subject [openssl "x509 -in cert$i.pem -subject -noout"]] "=" ]
	}
	lsort $subjs
} 0  "{subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER1_$length, emailAddress=<EMAIL>
} {subject=C=RU, O=Cryptocom, OU=OpenSSL Team, CN=USER2_$length, emailAddress=<EMAIL>
}"

}


test "Resigning in DER format with a unsuitable key length 512" {
	openssl "cms -resign -binary -inform der -nodetach -inkey U_cms_2_512/seckey.pem -signer U_cms_2_512/cert.pem -in cms_signed2_1_256.asn"
} 1 "no matching digest"

test "Resigning in DER format with a unsuitable key length 256" {
	openssl "cms -resign -binary -inform der -nodetach -inkey U_cms_2_256/seckey.pem -signer U_cms_2_256/cert.pem -in cms_signed2_1_512.asn"
} 1 "no matching digest"

test "Resigning opaque in S/MIME format with a unsuitable key length 512" {
	openssl "cms -resign -binary -nodetach -inkey U_cms_2_512/seckey.pem -signer U_cms_2_512/cert.pem -in cms_signed2_1_256_op.msg"
} 1 "no matching digest"

test "Resigning opaque in S/MIME format with a unsuitable key length 256" {
	openssl "cms -resign -binary -nodetach -inkey U_cms_2_256/seckey.pem -signer U_cms_2_256/cert.pem -in cms_signed2_1_512_op.msg"
} 1 "no matching digest"

test "Resigning detached in DER format with a unsuitable key length 512" {
	openssl "cms -resign -binary -inform der -inkey U_cms_2_512/seckey.pem -signer U_cms_2_512/cert.pem -in cms_signed2_1_256_det.asn -content cms_signed2.dat"
} 1 "no matching digest"

test "Resigning detached in DER format with a unsuitable key length 256" {
	openssl "cms -resign -binary -inform der -inkey U_cms_2_256/seckey.pem -signer U_cms_2_256/cert.pem -in cms_signed2_1_512_det.asn -content cms_signed2.dat"
} 1 "no matching digest"

test "Resigning in S/MIME format with a unsuitable key length 512" {
	openssl "cms -resign -binary -inkey U_cms_2_512/seckey.pem -signer U_cms_2_512/cert.pem -in cms_signed2_1_256.msg"
} 1 "no matching digest"

test "Resigning in S/MIME format with a unsuitable key length 256" {
	openssl "cms -resign -binary -inkey U_cms_2_256/seckey.pem -signer U_cms_2_256/cert.pem -in cms_signed2_1_512.msg"
} 1 "no matching digest"


end_tests



#./load_engine cms -sign -binary -outform der -inform der -nodetach -inkey certs/fstek.key -signer certs/fstek.crt -out cms_signed2 -in cms_signed1
#./load_engine cms -verify -inform der -in cms_signed2 -noverify
#./load_engine cms -verify -inform der -in cms_signed2 -noverify -signer sss
#cat sss
#history 
#vim sss
#./load_engine x509 -in sss sss2
#./load_engine x509 -in sss 
#./load_engine x509 -in sss -subject -noout
#./load_engine x509 -in sss2 -subject -noout
#./load_engine cms -verify -inform der -in cms_signed2 -noverify -signer sss -out qqq
