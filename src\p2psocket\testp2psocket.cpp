// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Unit tests for p2psocket.h basic interface (excluding Stream API)

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <signal.h>
#endif

#include <mutex>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <atomic>
#include "p2psocket.h"
#include "cert_wrapper.h"

// Test logging levels
enum TEST_LOG_LEVEL {
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_VERBOSE = 4
};

// Simple logging class for tests
class TestLogger {
public:
    static void Log(const char* message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << message << std::endl;
    }

    static void Log(const std::string& message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << buffer << std::endl;
    }
};

// Test result tracking
struct TestResult {
    int total_tests = 0;
    int passed_tests = 0;
    int failed_tests = 0;
    std::vector<std::string> failures;
    
    void AddTest(const std::string& test_name, bool passed, const std::string& error = "") {
        total_tests++;
        if (passed) {
            passed_tests++;
            TestLogger::Log(LOG_INFO, "PASS: " + test_name);
        } else {
            failed_tests++;
            std::string failure_msg = "FAIL: " + test_name;
            if (!error.empty()) {
                failure_msg += " - " + error;
            }
            failures.push_back(failure_msg);
            TestLogger::Log(LOG_ERROR, failure_msg);
        }
    }
    
    void PrintSummary() {
        TestLogger::Log("=== Test Summary ===");
        TestLogger::Log("Total tests: " + std::to_string(total_tests));
        TestLogger::Log("Passed: " + std::to_string(passed_tests));
        TestLogger::Log("Failed: " + std::to_string(failed_tests));
        
        if (failed_tests > 0) {
            TestLogger::Log("Failures:");
            for (const auto& failure : failures) {
                TestLogger::Log("  " + failure);
            }
        }
    }
};

// Global test result tracker
TestResult g_testResult;

// Test assertion macros
#define TEST_ASSERT(condition, test_name) \
    do { \
        bool result = (condition); \
        g_testResult.AddTest(test_name, result, result ? "" : #condition); \
    } while(0)

#define TEST_ASSERT_MSG(condition, test_name, msg) \
    do { \
        bool result = (condition); \
        g_testResult.AddTest(test_name, result, result ? "" : msg); \
    } while(0)

// Utility functions
uint64_t GetCurrentTimeMs() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

static void SleepMs(int ms) {
#ifdef _WIN32
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

// Certificate verification callback for tests
static bool TestCertVerifyCallback(P2P_SOCKET soc, const char* x509) {
    TestLogger::Log(LOG_INFO, "Certificate verification callback called");
    return true; // Accept all certificates for testing
}

// Test configuration
struct TestConfig {
    enum SOCKET_TYPE socket_type = SOCKET_QUIC;
    int worker_num = 4;
    int test_port = 4433;
    std::string test_ip = "127.0.0.1";
    int buffer_size = 64 * 1024;
    int timeout_ms = 5000;
};

TestConfig g_config;

// Global variables for server-client testing
std::atomic<bool> g_server_running(false);
std::atomic<bool> g_client_connected(false);
std::atomic<bool> g_test_completed(false);
P2P_SOCKET g_server_socket = nullptr;
P2P_SOCKET g_accepted_socket = nullptr;
std::mutex g_test_mutex;

// Helper function to create socket options
SocketOptions CreateSocketOptions(enum SOCKET_MODE mode, CertHandle cert_handle) {
    SocketOptions options = {};
    options.mode = mode;
    options.type = g_config.socket_type;
    options.wokernum = g_config.worker_num;
    options.cert_verify = TestCertVerifyCallback;
    options.cert = Cert_GetX509Str(cert_handle);
    options.privatekey = Cert_GetPkeyStr(cert_handle);
    options.log_level = P2P_LOG_INFO;
    return options;
}

// Server thread function for connection testing
void ServerThreadFunction(CertHandle cert_handle, int test_port) {
    TestLogger::Log("Server thread starting...");

    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, cert_handle);
    g_server_socket = P2pCreate(&server_options);

    if (!g_server_socket) {
        TestLogger::Log(LOG_ERROR, "Failed to create server socket");
        return;
    }

    // Bind to port
    int result = P2pBind(g_server_socket, "0.0.0.0", test_port);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to bind server socket");
        P2pClose(g_server_socket);
        return;
    }

    // Listen for connections
    result = P2pListen(g_server_socket);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to listen on server socket");
        P2pClose(g_server_socket);
        return;
    }

    g_server_running = true;
    TestLogger::Log("Server listening on port " + std::to_string(test_port));

    // Accept connection
    char client_ip[128] = {0};
    int client_port = 0;
    g_accepted_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);

    if (g_accepted_socket) {
        TestLogger::Log("Server accepted connection from " + std::string(client_ip) + ":" + std::to_string(client_port));

        // Simple echo server - read data and echo it back
        char buffer[1024];
        while (!g_test_completed) {
            int bytes_read = P2pRead(g_accepted_socket, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                TestLogger::Log("Server received " + std::to_string(bytes_read) + " bytes");
                int bytes_written = P2pWrite(g_accepted_socket, buffer, bytes_read);
                TestLogger::Log("Server echoed " + std::to_string(bytes_written) + " bytes");
            } else if (bytes_read < 0) {
                TestLogger::Log("Server read error or connection closed");
                break;
            }
            SleepMs(10); // Small delay to prevent busy waiting
        }

        P2pClose(g_accepted_socket);
        g_accepted_socket = nullptr;
    }

    P2pClose(g_server_socket);
    g_server_socket = nullptr;
    g_server_running = false;
    TestLogger::Log("Server thread exiting...");
}

// Basic socket creation and destruction tests
void TestBasicSocketOperations() {
    TestLogger::Log("=== Testing Basic Socket Operations ===");

    CertHandle cert_handle = Cert_Create();
    TEST_ASSERT(cert_handle != nullptr, "Certificate creation");

    if (cert_handle) {
        // Test socket creation
        SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
        P2P_SOCKET socket = P2pCreate(&options);
        TEST_ASSERT(socket != nullptr, "Socket creation");

        if (socket) {
            // Test socket close
            int result = P2pClose(socket);
            TEST_ASSERT(result == 0, "Socket close");
        }

        Cert_Destroy(cert_handle);
    }
}

// Test invalid parameter handling
void TestInvalidParameters() {
    TestLogger::Log("=== Testing Invalid Parameters ===");
    
    // Test null parameters
    P2P_SOCKET socket = P2pCreate(nullptr);
    TEST_ASSERT(socket == nullptr, "Create with null options");
    
    int result = P2pClose(nullptr);
    TEST_ASSERT(result != 0, "Close null socket");
    
    result = P2pBind(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Bind null socket");
    
    result = P2pConnect(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Connect null socket");
    
    result = P2pWrite(nullptr, "test", 4);
    TEST_ASSERT(result <= 0, "Write to null socket");
    
    char buffer[100];
    result = P2pRead(nullptr, buffer, sizeof(buffer));
    TEST_ASSERT(result <= 0, "Read from null socket");
}

// Test socket binding and port operations
void TestSocketBinding() {
    TestLogger::Log("=== Testing Socket Binding ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for binding test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test binding to specific port
        int result = P2pBind(socket, "0.0.0.0", g_config.test_port);
        TEST_ASSERT(result == 0, "Bind to specific port");

        // Test getting local port
        int local_port = P2pGetLocalPort(socket);
        TEST_ASSERT(local_port == g_config.test_port, "Get local port");

        // Test listen
        result = P2pListen(socket);
        TEST_ASSERT(result == 0, "Listen on bound socket");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for binding test");
    }

    Cert_Destroy(cert_handle);
}

// Test timeout and mode settings
void TestSocketSettings() {
    TestLogger::Log("=== Testing Socket Settings ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for settings test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test connection timeout setting
        int result = P2pSetConnTimeout(socket, g_config.timeout_ms);
        TEST_ASSERT(result == 0, "Set connection timeout");

        // Test send mode setting
        result = P2pSetSendMode(socket, 1); // Direct mode
        TEST_ASSERT(result == 0, "Set send mode");

        // Test read mode setting
        result = P2pSetReadMode(socket, 0); // Buffer mode
        TEST_ASSERT(result == 0, "Set read mode");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for settings test");
    }

    Cert_Destroy(cert_handle);
}

// Test polling operations
void TestPollingOperations() {
    TestLogger::Log("=== Testing Polling Operations ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for polling test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test socket polling
        PollEvent events;
        events.events = P2PPOLLIN | P2PPOLLOUT;

        int result = P2pPoll(socket, &events, 100); // 100ms timeout
        TEST_ASSERT(result >= 0, "Socket poll operation");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for polling test");
    }

    Cert_Destroy(cert_handle);
}

// Test real server-client connection and data transmission
void TestServerClientConnection() {
    TestLogger::Log("=== Testing Server-Client Connection and Data Transmission ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for connection test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 10; // Use different port to avoid conflicts

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;

    // Start server thread
    std::thread server_thread(ServerThreadFunction, server_cert, test_port);

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) { // Wait up to 5 seconds
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Server started successfully");

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);
        TEST_ASSERT(client_socket != nullptr, "Client socket creation");

        if (client_socket) {
            // Set connection timeout
            int result = P2pSetConnTimeout(client_socket, 5000); // 5 seconds
            TEST_ASSERT(result == 0, "Set client connection timeout");

            // Connect to server
            result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
            TEST_ASSERT(result == 0, "Client connect to server");

            if (result == 0) {
                g_client_connected = true;
                TestLogger::Log("Client connected successfully");

                // Test data transmission
                const char* test_message = "Hello from client!";
                int message_len = strlen(test_message);

                // Test P2pWrite
                int bytes_written = P2pWrite(client_socket, test_message, message_len);
                TEST_ASSERT(bytes_written == message_len, "Client write data");

                if (bytes_written > 0) {
                    // Test P2pRead - read echo from server
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, sizeof(read_buffer));
                    TEST_ASSERT(bytes_read == message_len, "Client read echoed data");
                    TEST_ASSERT(strcmp(read_buffer, test_message) == 0, "Echoed data matches sent data");

                    TestLogger::Log("Data echo test successful");
                }
            }

            P2pClose(client_socket);
        }
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test vectored I/O operations with real connection
void TestVectoredIO() {
    TestLogger::Log("=== Testing Vectored I/O Operations ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for vectored IO test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 20; // Use different port

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;

    // Start server thread
    std::thread server_thread(ServerThreadFunction, server_cert, test_port);

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) {
        SleepMs(100);
        wait_count++;
    }

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);

        if (client_socket) {
            P2pSetConnTimeout(client_socket, 5000);
            int result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);

            if (result == 0) {
                // Test P2pWritev with multiple buffers
                struct p2p_iovec iov[3];
                char data1[] = "Hello";
                char data2[] = " ";
                char data3[] = "World!";

                iov[0].iov_base = data1;
                iov[0].iov_len = strlen(data1);
                iov[1].iov_base = data2;
                iov[1].iov_len = strlen(data2);
                iov[2].iov_base = data3;
                iov[2].iov_len = strlen(data3);

                int total_len = iov[0].iov_len + iov[1].iov_len + iov[2].iov_len;
                int bytes_written = P2pWritev(client_socket, iov, 3);
                TEST_ASSERT(bytes_written == total_len, "Vectored write operation");

                if (bytes_written > 0) {
                    // Read the echoed data
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, sizeof(read_buffer));
                    TEST_ASSERT(bytes_read == total_len, "Read echoed vectored data");
                    TEST_ASSERT(strcmp(read_buffer, "Hello World!") == 0, "Vectored data integrity");

                    TestLogger::Log("Vectored I/O test successful");
                }
            } else {
                TEST_ASSERT(false, "Client connection failed for vectored IO test");
            }

            P2pClose(client_socket);
        }
    }

    // Test with null parameters
    P2P_SOCKET dummy_socket = nullptr;
    int result = P2pWritev(dummy_socket, nullptr, 0);
    TEST_ASSERT(result <= 0, "Writev with null socket");

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test edge cases and boundary conditions
void TestEdgeCases() {
    TestLogger::Log("=== Testing Edge Cases ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for edge cases test");
        return;
    }

    // Test with invalid socket options
    SocketOptions invalid_options = {};
    invalid_options.mode = (enum SOCKET_MODE)999; // Invalid mode
    invalid_options.type = (enum SOCKET_TYPE)999; // Invalid type

    P2P_SOCKET socket = P2pCreate(&invalid_options);
    TEST_ASSERT(socket == nullptr, "Create socket with invalid options");

    // Test with zero worker number
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    options.wokernum = 0;

    socket = P2pCreate(&options);
    // This might succeed or fail depending on implementation
    if (socket) {
        P2pClose(socket);
    }
    TEST_ASSERT(true, "Create socket with zero workers (implementation dependent)");

    // Test binding to invalid port
    options = CreateSocketOptions(MODE_SERVER, cert_handle);
    socket = P2pCreate(&options);

    if (socket) {
        int result = P2pBind(socket, "0.0.0.0", -1); // Invalid port
        TEST_ASSERT(result != 0, "Bind to invalid port");

        result = P2pBind(socket, "0.0.0.0", 65536); // Port out of range
        TEST_ASSERT(result != 0, "Bind to port out of range");

        result = P2pBind(socket, "invalid.ip.address", 4433); // Invalid IP
        TEST_ASSERT(result != 0, "Bind to invalid IP address");

        P2pClose(socket);
    }

    Cert_Destroy(cert_handle);
}


int main(int argc, char* argv[]) {
#ifndef _WIN32
    signal(SIGPIPE, SIG_IGN);
#endif

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-type" && i + 1 < argc) {
            g_config.socket_type = (enum SOCKET_TYPE)std::stoi(argv[++i]);
        } else if (arg == "-port" && i + 1 < argc) {
            g_config.test_port = std::stoi(argv[++i]);
        } else if (arg == "-ip" && i + 1 < argc) {
            g_config.test_ip = argv[++i];
        } else if (arg == "-workers" && i + 1 < argc) {
            g_config.worker_num = std::stoi(argv[++i]);
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -type <n>     Socket type (1=MULTITCP, 2=SSL, 3=QUIC)" << std::endl;
            std::cout << "  -port <n>     Test port (default: 4433)" << std::endl;
            std::cout << "  -ip <addr>    Test IP address (default: 127.0.0.1)" << std::endl;
            std::cout << "  -workers <n>  Number of workers (default: 4)" << std::endl;
            std::cout << "  -h, --help    Show this help message" << std::endl;
            return 0;
        }
    }

    TestLogger::Log("Starting P2P Socket Interface Unit Tests (Basic API Only)");
    TestLogger::Log("Configuration:");
    TestLogger::Log("  Socket type: " + std::to_string(g_config.socket_type));
    TestLogger::Log("  Test port: " + std::to_string(g_config.test_port));
    TestLogger::Log("  Test IP: " + g_config.test_ip);
    TestLogger::Log("  Workers: " + std::to_string(g_config.worker_num));
    TestLogger::Log("");

    // Run basic socket tests
    TestBasicSocketOperations();
    TestInvalidParameters();
    TestSocketBinding();
    TestSocketSettings();
    TestPollingOperations();

    // Run connection and data transmission tests
    TestServerClientConnection();
    TestVectoredIO();

    // Run advanced tests
    TestEdgeCases();

    // Print test summary
    TestLogger::Log("");
    g_testResult.PrintSummary();

    if (g_testResult.failed_tests == 0) {
        TestLogger::Log("All tests passed! ✓");
    } else {
        TestLogger::Log("Some tests failed! ✗");
    }

    TestLogger::Log("Unit tests completed");
    return g_testResult.failed_tests > 0 ? 1 : 0;
}
