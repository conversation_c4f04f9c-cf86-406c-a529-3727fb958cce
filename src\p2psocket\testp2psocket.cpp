// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Unit tests for p2psocket.h basic interface (excluding Stream API)

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <signal.h>
#endif

#include <mutex>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include <atomic>
#include "p2psocket.h"
#include "cert_wrapper.h"

// Test logging levels
enum TEST_LOG_LEVEL {
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_VERBOSE = 4
};

// Simple logging class for tests
class TestLogger {
public:
    static void Log(const char* message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << message << std::endl;
    }

    static void Log(const std::string& message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << buffer << std::endl;
    }
};

// Test result tracking
struct TestResult {
    int total_tests = 0;
    int passed_tests = 0;
    int failed_tests = 0;
    std::vector<std::string> failures;
    
    void AddTest(const std::string& test_name, bool passed, const std::string& error = "") {
        total_tests++;
        if (passed) {
            passed_tests++;
            TestLogger::Log(LOG_INFO, "PASS: " + test_name);
        } else {
            failed_tests++;
            std::string failure_msg = "FAIL: " + test_name;
            if (!error.empty()) {
                failure_msg += " - " + error;
            }
            failures.push_back(failure_msg);
            TestLogger::Log(LOG_ERROR, failure_msg);
        }
    }
    
    void PrintSummary() {
        TestLogger::Log("=== Test Summary ===");
        TestLogger::Log("Total tests: " + std::to_string(total_tests));
        TestLogger::Log("Passed: " + std::to_string(passed_tests));
        TestLogger::Log("Failed: " + std::to_string(failed_tests));
        
        if (failed_tests > 0) {
            TestLogger::Log("Failures:");
            for (const auto& failure : failures) {
                TestLogger::Log("  " + failure);
            }
        }
    }
};

// Global test result tracker
TestResult g_testResult;

// Test assertion macros
#define TEST_ASSERT(condition, test_name) \
    do { \
        bool test_result = (condition); \
        g_testResult.AddTest(test_name, test_result, test_result ? "" : #condition); \
    } while(0)

#define TEST_ASSERT_MSG(condition, test_name, msg) \
    do { \
        bool test_result = (condition); \
        g_testResult.AddTest(test_name, test_result, test_result ? "" : msg); \
    } while(0)

// Utility functions
uint64_t GetCurrentTimeMs() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

static void SleepMs(int ms) {
#ifdef _WIN32
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

// Certificate verification callback for tests
static bool TestCertVerifyCallback(P2P_SOCKET soc, const char* x509) {
    TestLogger::Log(LOG_INFO, "Certificate verification callback called");
    return true; // Accept all certificates for testing
}

// Test configuration
struct TestConfig {
    enum SOCKET_TYPE socket_type = SOCKET_QUIC;
    int worker_num = 4;
    int test_port = 4433;
    std::string test_ip = "127.0.0.1";
    int buffer_size = 64 * 1024;
    int timeout_ms = 5000;

    // Test control flags
    bool run_basic_ops = true;
    bool run_invalid_params = true;
    bool run_binding = true;
    bool run_settings = true;
    bool run_polling = true;
    bool run_connection = true;
    bool run_vectored_io = true;
    bool run_multi_client = true;
    bool run_socket_types = true;
    bool run_edge_cases = true;
};

TestConfig g_config;

// Global variables for server-client testing
std::atomic<bool> g_server_running(false);
std::atomic<bool> g_client_connected(false);
std::atomic<bool> g_test_completed(false);
P2P_SOCKET g_server_socket = nullptr;
P2P_SOCKET g_accepted_socket = nullptr;
std::mutex g_test_mutex;

// Global variables for multi-client testing
std::atomic<int> g_connected_clients(0);
std::atomic<int> g_expected_clients(0);
std::vector<P2P_SOCKET> g_client_sockets;
std::mutex g_client_sockets_mutex;

// Helper function to create socket options
SocketOptions CreateSocketOptions(enum SOCKET_MODE mode, CertHandle cert_handle) {
    SocketOptions options = {};
    options.mode = mode;
    options.type = g_config.socket_type;
    options.wokernum = g_config.worker_num;
    options.cert_verify = TestCertVerifyCallback;
    options.cert = Cert_GetX509Str(cert_handle);
    options.privatekey = Cert_GetPkeyStr(cert_handle);
    options.log_level = P2P_LOG_INFO;
    return options;
}

// Server thread function for connection testing
void ServerThreadFunction(CertHandle cert_handle, int test_port) {
    TestLogger::Log("Server thread starting...");

    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, cert_handle);
    g_server_socket = P2pCreate(&server_options);

    if (!g_server_socket) {
        TestLogger::Log(LOG_ERROR, "Failed to create server socket");
        return;
    }

    // Bind to port
    int result = P2pBind(g_server_socket, "0.0.0.0", test_port);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to bind server socket");
        P2pClose(g_server_socket);
        return;
    }

    // Listen for connections
    result = P2pListen(g_server_socket);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to listen on server socket");
        P2pClose(g_server_socket);
        return;
    }

    g_server_running = true;
    TestLogger::Log("Server listening on port " + std::to_string(test_port));

    // Accept connection
    char client_ip[128] = {0};
    int client_port = 0;
    g_accepted_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);

    if (g_accepted_socket) {
        TestLogger::Log("Server accepted connection from " + std::string(client_ip) + ":" + std::to_string(client_port));

        // Simple echo server - read data and echo it back
        char buffer[1024];
        while (!g_test_completed) {
            int bytes_read = P2pRead(g_accepted_socket, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                TestLogger::Log("Server received " + std::to_string(bytes_read) + " bytes");
                int bytes_written = P2pWrite(g_accepted_socket, buffer, bytes_read);
                TestLogger::Log("Server echoed " + std::to_string(bytes_written) + " bytes");
            } else if (bytes_read < 0) {
                TestLogger::Log("Server read error or connection closed");
                break;
            }
            SleepMs(10); // Small delay to prevent busy waiting
        }

        P2pClose(g_accepted_socket);
        g_accepted_socket = nullptr;
    }

    P2pClose(g_server_socket);
    g_server_socket = nullptr;
    g_server_running = false;
    TestLogger::Log("Server thread exiting...");
}

// Handle individual client connection for multi-client test
void HandleClientConnection(P2P_SOCKET client_socket, int client_id) {
    TestLogger::Log("Client handler " + std::to_string(client_id) + " started");

    char buffer[1024];
    int message_count = 0;

    while (!g_test_completed && message_count < 3) { // Each client sends 3 messages
        int bytes_read = P2pRead(client_socket, buffer, sizeof(buffer));
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0'; // Null terminate for string operations
            TestLogger::Log("Client handler " + std::to_string(client_id) + " received: " + std::string(buffer));

            // Echo back with client ID prefix
            std::string response = "Echo-Client" + std::to_string(client_id) + ":" + std::string(buffer);
            int bytes_written = P2pWrite(client_socket, response.c_str(), response.length());

            if (bytes_written > 0) {
                TestLogger::Log("Client handler " + std::to_string(client_id) + " echoed: " + response);
                message_count++;
            }
        } else if (bytes_read < 0) {
            TestLogger::Log("Client handler " + std::to_string(client_id) + " read error or connection closed");
            break;
        }
        SleepMs(10); // Small delay
    }

    P2pClose(client_socket);
    TestLogger::Log("Client handler " + std::to_string(client_id) + " exiting");
}

// Multi-client server function
void MultiClientServerFunction(CertHandle cert_handle, int test_port) {
    TestLogger::Log("Multi-client server thread starting...");

    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, cert_handle);
    g_server_socket = P2pCreate(&server_options);

    if (!g_server_socket) {
        TestLogger::Log(LOG_ERROR, "Failed to create multi-client server socket");
        return;
    }

    // Bind to port
    int result = P2pBind(g_server_socket, "0.0.0.0", test_port);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to bind multi-client server socket");
        P2pClose(g_server_socket);
        return;
    }

    // Listen for connections
    result = P2pListen(g_server_socket);
    if (result != 0) {
        TestLogger::Log(LOG_ERROR, "Failed to listen on multi-client server socket");
        P2pClose(g_server_socket);
        return;
    }

    g_server_running = true;
    TestLogger::Log("Multi-client server listening on port " + std::to_string(test_port));

    std::vector<std::thread> client_handlers;
    int accepted_count = 0;

    // Accept multiple client connections
    while (accepted_count < g_expected_clients && !g_test_completed) {
        char client_ip[128] = {0};
        int client_port = 0;

        // Set a timeout for accept to avoid infinite blocking
        PollEvent poll_event;
        poll_event.events = P2PPOLLIN;
        int poll_result = P2pPoll(g_server_socket, &poll_event, 2000); // 2 second timeout

        if (poll_result > 0 && (poll_event.revents & P2PPOLLIN)) {
            P2P_SOCKET client_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);

            if (client_socket) {
                TestLogger::Log("Multi-client server accepted connection " + std::to_string(accepted_count) +
                              " from " + std::string(client_ip) + ":" + std::to_string(client_port));

                {
                    std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
                    g_client_sockets.push_back(client_socket);
                }

                // Create handler thread for this client
                client_handlers.emplace_back([client_socket, accepted_count]() {
                    HandleClientConnection(client_socket, accepted_count);
                });
                accepted_count++;
                g_connected_clients++;
            }
        } else if (poll_result == 0) {
            TestLogger::Log("Multi-client server accept timeout");
        }
    }

    TestLogger::Log("Multi-client server accepted " + std::to_string(accepted_count) + " clients");

    // Wait for all client handlers to complete
    for (auto& handler : client_handlers) {
        if (handler.joinable()) {
            handler.join();
        }
    }

    P2pClose(g_server_socket);
    g_server_socket = nullptr;
    g_server_running = false;
    TestLogger::Log("Multi-client server thread exiting...");
}

// Client connection function for multi-client test
void ClientConnectionFunction(CertHandle cert_handle, int test_port, int client_id, bool* result) {
    TestLogger::Log("Client " + std::to_string(client_id) + " thread starting...");

    *result = false; // Initialize result

    // Create client socket
    SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET client_socket = P2pCreate(&client_options);

    if (!client_socket) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to create socket");
        return;
    }

    // Set connection timeout
    int timeout_result = P2pSetConnTimeout(client_socket, 5000); // 5 seconds
    if (timeout_result != 0) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to set timeout");
        P2pClose(client_socket);
        return;
    }

    // Connect to server
    int connect_result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
    if (connect_result != 0) {
        TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to connect");
        P2pClose(client_socket);
        return;
    }

    TestLogger::Log("Client " + std::to_string(client_id) + " connected successfully");

    // Send multiple messages and verify echoes
    bool all_messages_ok = true;
    for (int msg = 0; msg < 3; msg++) {
        std::string message = "Message" + std::to_string(msg) + "FromClient" + std::to_string(client_id);

        // Send message
        int bytes_written = P2pWrite(client_socket, message.c_str(), message.length());
        if (bytes_written != (int)message.length()) {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to send message " + std::to_string(msg));
            all_messages_ok = false;
            break;
        }

        TestLogger::Log("Client " + std::to_string(client_id) + " sent: " + message);

        // Read echo response
        char read_buffer[1024] = {0};
        int bytes_read = P2pRead(client_socket, read_buffer, sizeof(read_buffer));
        if (bytes_read <= 0) {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " failed to read echo for message " + std::to_string(msg));
            all_messages_ok = false;
            break;
        }

        std::string received(read_buffer, bytes_read);
        std::string expected_prefix = "Echo-Client" + std::to_string(client_id) + ":" + message;

        if (received == expected_prefix) {
            TestLogger::Log("Client " + std::to_string(client_id) + " received correct echo: " + received);
        } else {
            TestLogger::Log(LOG_ERROR, "Client " + std::to_string(client_id) + " received incorrect echo. Expected: " +
                          expected_prefix + ", Got: " + received);
            all_messages_ok = false;
            break;
        }

        SleepMs(100); // Small delay between messages
    }

    P2pClose(client_socket);
    *result = all_messages_ok;
    TestLogger::Log("Client " + std::to_string(client_id) + " thread exiting with result: " +
                   (all_messages_ok ? "SUCCESS" : "FAILURE"));
}

// Basic socket creation and destruction tests
void TestBasicSocketOperations() {
    TestLogger::Log("=== Testing Basic Socket Operations ===");

    CertHandle cert_handle = Cert_Create();
    TEST_ASSERT(cert_handle != nullptr, "Certificate creation");

    if (cert_handle) {
        // Test socket creation
        SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
        P2P_SOCKET socket = P2pCreate(&options);
        TEST_ASSERT(socket != nullptr, "Socket creation");

        if (socket) {
            // Test socket close
            int close_result = P2pClose(socket);
            TEST_ASSERT(close_result == 0, "Socket close");
        }

        Cert_Destroy(cert_handle);
    }
}

// Test invalid parameter handling
void TestInvalidParameters() {
    TestLogger::Log("=== Testing Invalid Parameters ===");
    
    // Test null parameters
    P2P_SOCKET socket = P2pCreate(nullptr);
    TEST_ASSERT(socket == nullptr, "Create with null options");
    
    int result = P2pClose(nullptr);
    TEST_ASSERT(result != 0, "Close null socket");
    
    result = P2pBind(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Bind null socket");
    
    result = P2pConnect(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Connect null socket");
    
    result = P2pWrite(nullptr, "test", 4);
    TEST_ASSERT(result <= 0, "Write to null socket");
    
    char buffer[100];
    result = P2pRead(nullptr, buffer, sizeof(buffer));
    TEST_ASSERT(result <= 0, "Read from null socket");
}

// Test socket binding and port operations
void TestSocketBinding() {
    TestLogger::Log("=== Testing Socket Binding ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for binding test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test binding to specific port
        int result = P2pBind(socket, "0.0.0.0", g_config.test_port);
        TEST_ASSERT(result == 0, "Bind to specific port");

        // Test getting local port
        int local_port = P2pGetLocalPort(socket);
        TEST_ASSERT(local_port == g_config.test_port, "Get local port");

        // Test listen
        result = P2pListen(socket);
        TEST_ASSERT(result == 0, "Listen on bound socket");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for binding test");
    }

    Cert_Destroy(cert_handle);
}

// Test timeout and mode settings
void TestSocketSettings() {
    TestLogger::Log("=== Testing Socket Settings ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for settings test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test connection timeout setting
        int result = P2pSetConnTimeout(socket, g_config.timeout_ms);
        TEST_ASSERT(result == 0, "Set connection timeout");

        // Test send mode setting
        result = P2pSetSendMode(socket, 1); // Direct mode
        TEST_ASSERT(result == 0, "Set send mode");

        // Test read mode setting
        result = P2pSetReadMode(socket, 0); // Buffer mode
        TEST_ASSERT(result == 0, "Set read mode");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for settings test");
    }

    Cert_Destroy(cert_handle);
}

// Test polling operations
void TestPollingOperations() {
    TestLogger::Log("=== Testing Polling Operations ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for polling test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test socket polling
        PollEvent events;
        events.events = P2PPOLLIN | P2PPOLLOUT;

        int result = P2pPoll(socket, &events, 100); // 100ms timeout
        TEST_ASSERT(result >= 0, "Socket poll operation");

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for polling test");
    }

    Cert_Destroy(cert_handle);
}

// Test real server-client connection and data transmission
void TestServerClientConnection() {
    TestLogger::Log("=== Testing Server-Client Connection and Data Transmission ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for connection test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 10; // Use different port to avoid conflicts

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;

    // Start server thread
    std::thread server_thread([server_cert, test_port]() {
        ServerThreadFunction(server_cert, test_port);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) { // Wait up to 5 seconds
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Server started successfully");

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);
        TEST_ASSERT(client_socket != nullptr, "Client socket creation");

        if (client_socket) {
            // Set connection timeout
            int result = P2pSetConnTimeout(client_socket, 5000); // 5 seconds
            TEST_ASSERT(result == 0, "Set client connection timeout");

            // Connect to server
            result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
            TEST_ASSERT(result == 0, "Client connect to server");

            if (result == 0) {
                g_client_connected = true;
                TestLogger::Log("Client connected successfully");

                // Test data transmission
                const char* test_message = "Hello from client!";
                int message_len = strlen(test_message);

                // Test P2pWrite
                int bytes_written = P2pWrite(client_socket, test_message, message_len);
                TEST_ASSERT(bytes_written == message_len, "Client write data");

                if (bytes_written > 0) {
                    // Test P2pRead - read echo from server
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, sizeof(read_buffer));
                    TEST_ASSERT(bytes_read == message_len, "Client read echoed data");
                    TEST_ASSERT(strcmp(read_buffer, test_message) == 0, "Echoed data matches sent data");

                    TestLogger::Log("Data echo test successful");
                }
            }

            P2pClose(client_socket);
        }
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test vectored I/O operations with real connection
void TestVectoredIO() {
    TestLogger::Log("=== Testing Vectored I/O Operations ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for vectored IO test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 20; // Use different port

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;

    // Start server thread
    std::thread server_thread([server_cert, test_port]() {
        ServerThreadFunction(server_cert, test_port);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) {
        SleepMs(100);
        wait_count++;
    }

    if (g_server_running) {
        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);

        if (client_socket) {
            P2pSetConnTimeout(client_socket, 5000);
            int result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);

            if (result == 0) {
                // Test P2pWritev with multiple buffers
                struct p2p_iovec iov[3];
                char data1[] = "Hello";
                char data2[] = " ";
                char data3[] = "World!";

                iov[0].iov_base = data1;
                iov[0].iov_len = strlen(data1);
                iov[1].iov_base = data2;
                iov[1].iov_len = strlen(data2);
                iov[2].iov_base = data3;
                iov[2].iov_len = strlen(data3);

                int total_len = iov[0].iov_len + iov[1].iov_len + iov[2].iov_len;
                int bytes_written = P2pWritev(client_socket, iov, 3);
                TEST_ASSERT(bytes_written == total_len, "Vectored write operation");

                if (bytes_written > 0) {
                    // Read the echoed data
                    char read_buffer[1024] = {0};
                    int bytes_read = P2pRead(client_socket, read_buffer, sizeof(read_buffer));
                    TEST_ASSERT(bytes_read == total_len, "Read echoed vectored data");
                    TEST_ASSERT(strcmp(read_buffer, "Hello World!") == 0, "Vectored data integrity");

                    TestLogger::Log("Vectored I/O test successful");
                }
            } else {
                TEST_ASSERT(false, "Client connection failed for vectored IO test");
            }

            P2pClose(client_socket);
        }
    }

    // Test with null parameters
    P2P_SOCKET dummy_socket = nullptr;
    int result = P2pWritev(dummy_socket, nullptr, 0);
    TEST_ASSERT(result <= 0, "Writev with null socket");

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test edge cases and boundary conditions
void TestEdgeCases() {
    TestLogger::Log("=== Testing Edge Cases ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for edge cases test");
        return;
    }

    // Test with invalid socket options
    SocketOptions invalid_options = {};
    invalid_options.mode = (enum SOCKET_MODE)999; // Invalid mode
    invalid_options.type = (enum SOCKET_TYPE)999; // Invalid type

    P2P_SOCKET socket = P2pCreate(&invalid_options);
    TEST_ASSERT(socket == nullptr, "Create socket with invalid options");

    // Test with zero worker number
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    options.wokernum = 0;

    socket = P2pCreate(&options);
    // This might succeed or fail depending on implementation
    if (socket) {
        P2pClose(socket);
    }
    TEST_ASSERT(true, "Create socket with zero workers (implementation dependent)");

    // Test binding to invalid port
    options = CreateSocketOptions(MODE_SERVER, cert_handle);
    socket = P2pCreate(&options);

    if (socket) {
        int result = P2pBind(socket, "0.0.0.0", -1); // Invalid port
        TEST_ASSERT(result != 0, "Bind to invalid port");

        result = P2pBind(socket, "0.0.0.0", 65536); // Port out of range
        TEST_ASSERT(result != 0, "Bind to port out of range");

        result = P2pBind(socket, "invalid.ip.address", 4433); // Invalid IP
        TEST_ASSERT(result != 0, "Bind to invalid IP address");

        P2pClose(socket);
    }

    Cert_Destroy(cert_handle);
}

// Test multiple client connections to one server
void TestMultipleClientConnections() {
    TestLogger::Log("=== Testing Multiple Client Connections ===");

    const int NUM_CLIENTS = 3;
    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for multi-client test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    int test_port = g_config.test_port + 30; // Use different port to avoid conflicts

    // Reset global state
    g_server_running = false;
    g_client_connected = false;
    g_test_completed = false;
    g_connected_clients = 0;
    g_expected_clients = NUM_CLIENTS;

    // Clear client sockets vector
    {
        std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
        g_client_sockets.clear();
    }

    TestLogger::Log("Starting multi-client test with " + std::to_string(NUM_CLIENTS) + " clients on port " + std::to_string(test_port));

    // Start server thread
    std::thread server_thread([server_cert, test_port]() {
        MultiClientServerFunction(server_cert, test_port);
    });

    // Wait for server to start
    int wait_count = 0;
    while (!g_server_running && wait_count < 50) { // Wait up to 5 seconds
        SleepMs(100);
        wait_count++;
    }

    TEST_ASSERT(g_server_running, "Multi-client server started successfully");

    if (g_server_running) {
        // Create multiple client threads
        std::vector<std::thread> client_threads;
        // Use array instead of vector<bool> to avoid proxy reference issues
        bool client_results[NUM_CLIENTS];
        for (int i = 0; i < NUM_CLIENTS; i++) {
            client_results[i] = false;
        }

        // Start all client threads with a small delay between them
        for (int i = 0; i < NUM_CLIENTS; i++) {
            client_threads.emplace_back([client_cert, test_port, i, &client_results]() {
                ClientConnectionFunction(client_cert, test_port, i, &client_results[i]);
            });
            SleepMs(200); // Small delay to stagger connections
        }

        // Wait for all clients to complete
        for (auto& client_thread : client_threads) {
            if (client_thread.joinable()) {
                client_thread.join();
            }
        }

        // Verify results
        int successful_clients = 0;
        for (int i = 0; i < NUM_CLIENTS; i++) {
            if (client_results[i]) {
                successful_clients++;
            }
            TEST_ASSERT(client_results[i], "Client " + std::to_string(i) + " connection and data exchange");
        }

        TestLogger::Log("Multi-client test completed: " + std::to_string(successful_clients) + "/" +
                       std::to_string(NUM_CLIENTS) + " clients successful");

        // Test overall success
        TEST_ASSERT(successful_clients == NUM_CLIENTS, "All clients connected and exchanged data successfully");

        // Verify server accepted the expected number of clients
        TEST_ASSERT(g_connected_clients.load() == NUM_CLIENTS, "Server accepted expected number of clients");
    }

    // Signal test completion and wait for server thread
    g_test_completed = true;
    if (server_thread.joinable()) {
        server_thread.join();
    }

    // Clean up any remaining client sockets
    {
        std::lock_guard<std::mutex> lock(g_client_sockets_mutex);
        for (auto socket : g_client_sockets) {
            if (socket) {
                P2pClose(socket);
            }
        }
        g_client_sockets.clear();
    }

    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);

    TestLogger::Log("Multi-client test cleanup completed");
}

int main(int argc, char* argv[]) {
#ifndef _WIN32
    signal(SIGPIPE, SIG_IGN);
#endif

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-type" && i + 1 < argc) {
            g_config.socket_type = (enum SOCKET_TYPE)std::stoi(argv[++i]);
        } else if (arg == "-port" && i + 1 < argc) {
            g_config.test_port = std::stoi(argv[++i]);
        } else if (arg == "-ip" && i + 1 < argc) {
            g_config.test_ip = argv[++i];
        } else if (arg == "-workers" && i + 1 < argc) {
            g_config.worker_num = std::stoi(argv[++i]);
        } else if (arg == "-test" && i + 1 < argc) {
            // Disable all tests first
            g_config.run_basic_ops = false;
            g_config.run_invalid_params = false;
            g_config.run_binding = false;
            g_config.run_settings = false;
            g_config.run_polling = false;
            g_config.run_connection = false;
            g_config.run_vectored_io = false;
            g_config.run_multi_client = false;
            g_config.run_socket_types = false;
            g_config.run_edge_cases = false;

            // Enable specific test
            std::string test_name = argv[++i];
            if (test_name == "basic") g_config.run_basic_ops = true;
            else if (test_name == "invalid") g_config.run_invalid_params = true;
            else if (test_name == "binding") g_config.run_binding = true;
            else if (test_name == "settings") g_config.run_settings = true;
            else if (test_name == "polling") g_config.run_polling = true;
            else if (test_name == "connection") g_config.run_connection = true;
            else if (test_name == "vectored") g_config.run_vectored_io = true;
            else if (test_name == "multiclient") g_config.run_multi_client = true;
            else if (test_name == "types") g_config.run_socket_types = true;
            else if (test_name == "edge") g_config.run_edge_cases = true;
            else if (test_name == "all") {
                g_config.run_basic_ops = true;
                g_config.run_invalid_params = true;
                g_config.run_binding = true;
                g_config.run_settings = true;
                g_config.run_polling = true;
                g_config.run_connection = true;
                g_config.run_vectored_io = true;
                g_config.run_multi_client = true;
                g_config.run_socket_types = true;
                g_config.run_edge_cases = true;
            } else {
                std::cout << "Unknown test: " << test_name << std::endl;
                return 1;
            }
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -type <n>     Socket type (1=MULTITCP, 2=SSL, 3=QUIC)" << std::endl;
            std::cout << "  -port <n>     Test port (default: 4433)" << std::endl;
            std::cout << "  -ip <addr>    Test IP address (default: 127.0.0.1)" << std::endl;
            std::cout << "  -workers <n>  Number of workers (default: 4)" << std::endl;
            std::cout << "  -test <name>  Run specific test:" << std::endl;
            std::cout << "                basic      - Basic socket operations" << std::endl;
            std::cout << "                invalid    - Invalid parameter tests" << std::endl;
            std::cout << "                binding    - Socket binding tests" << std::endl;
            std::cout << "                settings   - Socket settings tests" << std::endl;
            std::cout << "                polling    - Polling operations tests" << std::endl;
            std::cout << "                connection - Server-client connection tests" << std::endl;
            std::cout << "                vectored   - Vectored I/O tests" << std::endl;
            std::cout << "                multiclient- Multi-client tests" << std::endl;
            std::cout << "                types      - Multiple socket types tests" << std::endl;
            std::cout << "                edge       - Edge cases tests" << std::endl;
            std::cout << "                all        - Run all tests (default)" << std::endl;
            std::cout << "  -h, --help    Show this help message" << std::endl;
            return 0;
        }
    }

    TestLogger::Log("Starting P2P Socket Interface Unit Tests (Basic API Only)");
    TestLogger::Log("Configuration:");
    TestLogger::Log("  Socket type: " + std::to_string(g_config.socket_type));
    TestLogger::Log("  Test port: " + std::to_string(g_config.test_port));
    TestLogger::Log("  Test IP: " + g_config.test_ip);
    TestLogger::Log("  Workers: " + std::to_string(g_config.worker_num));
    TestLogger::Log("");

    // Run tests based on configuration
    if (g_config.run_basic_ops) {
        TestBasicSocketOperations();
    }

    if (g_config.run_invalid_params) {
        TestInvalidParameters();
    }

    if (g_config.run_binding) {
        TestSocketBinding();
    }

    if (g_config.run_settings) {
        TestSocketSettings();
    }

    if (g_config.run_polling) {
        TestPollingOperations();
    }

    if (g_config.run_connection) {
        TestServerClientConnection();
    }

    if (g_config.run_vectored_io) {
        TestVectoredIO();
    }

    if (g_config.run_multi_client) {
        TestMultipleClientConnections();
    }

    if (g_config.run_socket_types) {
        TestMultipleSocketTypes();
    }

    if (g_config.run_edge_cases) {
        TestEdgeCases();
    }

    // Print test summary
    TestLogger::Log("");
    g_testResult.PrintSummary();

    if (g_testResult.failed_tests == 0) {
        TestLogger::Log("All tests passed!");
    } else {
        TestLogger::Log("Some tests failed!");
    }

    TestLogger::Log("Unit tests completed");
    return g_testResult.failed_tests > 0 ? 1 : 0;
}
