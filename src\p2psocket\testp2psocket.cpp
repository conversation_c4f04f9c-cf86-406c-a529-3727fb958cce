// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Unit tests for p2psocket.h interface

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#include <signal.h>
#endif

#include <mutex>
#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include <cassert>
#include "p2psocket.h"
#include "cert_wrapper.h"

// Test logging levels
enum TEST_LOG_LEVEL {
    LOG_ERROR = 0,
    LOG_WARN = 1,
    LOG_INFO = 2,
    LOG_DEBUG = 3,
    LOG_VERBOSE = 4
};

// Simple logging class for tests
class TestLogger {
public:
    static void Log(const char* message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        const char* levelStr[] = {"ERROR", "WARN", "<PERSON>F<PERSON>", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << message << std::endl;
    }

    static void Log(const std::string& message) {
        std::cout << "[TEST] " << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        const char* levelStr[] = {"ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"};
        std::cout << "[" << levelStr[level] << "] " << buffer << std::endl;
    }
};

// Test result tracking
struct TestResult {
    int total_tests = 0;
    int passed_tests = 0;
    int failed_tests = 0;
    std::vector<std::string> failures;
    
    void AddTest(const std::string& test_name, bool passed, const std::string& error = "") {
        total_tests++;
        if (passed) {
            passed_tests++;
            TestLogger::Log(LOG_INFO, "PASS: " + test_name);
        } else {
            failed_tests++;
            std::string failure_msg = "FAIL: " + test_name;
            if (!error.empty()) {
                failure_msg += " - " + error;
            }
            failures.push_back(failure_msg);
            TestLogger::Log(LOG_ERROR, failure_msg);
        }
    }
    
    void PrintSummary() {
        TestLogger::Log("=== Test Summary ===");
        TestLogger::Log("Total tests: " + std::to_string(total_tests));
        TestLogger::Log("Passed: " + std::to_string(passed_tests));
        TestLogger::Log("Failed: " + std::to_string(failed_tests));
        
        if (failed_tests > 0) {
            TestLogger::Log("Failures:");
            for (const auto& failure : failures) {
                TestLogger::Log("  " + failure);
            }
        }
    }
};

// Global test result tracker
TestResult g_testResult;

// Test assertion macros
#define TEST_ASSERT(condition, test_name) \
    do { \
        bool result = (condition); \
        g_testResult.AddTest(test_name, result, result ? "" : #condition); \
    } while(0)

#define TEST_ASSERT_MSG(condition, test_name, msg) \
    do { \
        bool result = (condition); \
        g_testResult.AddTest(test_name, result, result ? "" : msg); \
    } while(0)

// Utility functions
uint64_t GetCurrentTimeMs() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

static void SleepMs(int ms) {
#ifdef _WIN32
    Sleep(ms);
#else
    usleep(ms * 1000);
#endif
}

// Certificate verification callback for tests
static bool TestCertVerifyCallback(P2P_SOCKET soc, const char* x509) {
    TestLogger::Log(LOG_INFO, "Certificate verification callback called");
    return true; // Accept all certificates for testing
}

// Test configuration
struct TestConfig {
    enum SOCKET_TYPE socket_type = SOCKET_QUIC;
    int worker_num = 4;
    int test_port = 4433;
    std::string test_ip = "127.0.0.1";
    int buffer_size = 64 * 1024;
    int timeout_ms = 5000;
};

TestConfig g_config;

// Helper function to create socket options
SocketOptions CreateSocketOptions(enum SOCKET_MODE mode, CertHandle cert_handle) {
    SocketOptions options = {};
    options.mode = mode;
    options.type = g_config.socket_type;
    options.wokernum = g_config.worker_num;
    options.cert_verify = TestCertVerifyCallback;
    options.cert = Cert_GetX509Str(cert_handle);
    options.privatekey = Cert_GetPkeyStr(cert_handle);
    options.log_level = P2P_LOG_INFO;
    return options;
}

// Basic socket creation and destruction tests
void TestBasicSocketOperations() {
    TestLogger::Log("=== Testing Basic Socket Operations ===");
    
    CertHandle cert_handle = Cert_Create();
    TEST_ASSERT(cert_handle != nullptr, "Certificate creation");
    
    if (cert_handle) {
        // Test socket creation
        SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
        P2P_SOCKET socket = P2pCreate(&options);
        TEST_ASSERT(socket != nullptr, "Socket creation");
        
        if (socket) {
            // Test socket close
            int result = P2pClose(socket);
            TEST_ASSERT(result == 0, "Socket close");
        }
        
        Cert_Destroy(cert_handle);
    }
}

// Test invalid parameter handling
void TestInvalidParameters() {
    TestLogger::Log("=== Testing Invalid Parameters ===");
    
    // Test null parameters
    P2P_SOCKET socket = P2pCreate(nullptr);
    TEST_ASSERT(socket == nullptr, "Create with null options");
    
    int result = P2pClose(nullptr);
    TEST_ASSERT(result != 0, "Close null socket");
    
    result = P2pBind(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Bind null socket");
    
    result = P2pConnect(nullptr, "127.0.0.1", 4433);
    TEST_ASSERT(result != 0, "Connect null socket");
    
    result = P2pWrite(nullptr, "test", 4);
    TEST_ASSERT(result <= 0, "Write to null socket");
    
    char buffer[100];
    result = P2pRead(nullptr, buffer, sizeof(buffer));
    TEST_ASSERT(result <= 0, "Read from null socket");
}

// Test socket binding and port operations
void TestSocketBinding() {
    TestLogger::Log("=== Testing Socket Binding ===");
    
    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for binding test");
        return;
    }
    
    SocketOptions options = CreateSocketOptions(MODE_SERVER, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);
    
    if (socket) {
        // Test binding to specific port
        int result = P2pBind(socket, "0.0.0.0", g_config.test_port);
        TEST_ASSERT(result == 0, "Bind to specific port");
        
        // Test getting local port
        int local_port = P2pGetLocalPort(socket);
        TEST_ASSERT(local_port == g_config.test_port, "Get local port");
        
        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for binding test");
    }
    
    Cert_Destroy(cert_handle);
}

// Test timeout and mode settings
void TestSocketSettings() {
    TestLogger::Log("=== Testing Socket Settings ===");
    
    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for settings test");
        return;
    }
    
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);
    
    if (socket) {
        // Test connection timeout setting
        int result = P2pSetConnTimeout(socket, g_config.timeout_ms);
        TEST_ASSERT(result == 0, "Set connection timeout");
        
        // Test send mode setting
        result = P2pSetSendMode(socket, 1); // Direct mode
        TEST_ASSERT(result == 0, "Set send mode");
        
        // Test read mode setting
        result = P2pSetReadMode(socket, 0); // Buffer mode
        TEST_ASSERT(result == 0, "Set read mode");
        
        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for settings test");
    }
    
    Cert_Destroy(cert_handle);
}

// Test Stream API basic operations
void TestStreamAPIBasics() {
    TestLogger::Log("=== Testing Stream API Basics ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for stream test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test stream creation with null options (should use defaults)
        P2P_STREAM stream = P2pStreamCreate(socket, nullptr);
        TEST_ASSERT(stream != nullptr, "Stream creation with null options");

        if (stream) {
            // Test stream information queries
            int stream_id = P2pStreamGetId(stream);
            TEST_ASSERT(stream_id >= 0, "Get stream ID");

            int buffered_bytes = P2pStreamGetBufferedBytes(stream);
            TEST_ASSERT(buffered_bytes >= 0, "Get buffered bytes");

            P2P_SOCKET parent_socket = P2pStreamGetSocket(stream);
            TEST_ASSERT(parent_socket == socket, "Get parent socket");

            int state = P2pStreamGetState(stream);
            TEST_ASSERT(state >= 0, "Get stream state");

            // Test stream close
            int result = P2pStreamClose(stream);
            TEST_ASSERT(result == 0, "Stream close");
        }

        // Test stream creation with custom options
        struct StreamOptions stream_opts = {};
        stream_opts.unidirectional = 0;
        stream_opts.priority = 128;
        stream_opts.buffer_size = 32 * 1024;

        stream = P2pStreamCreate(socket, &stream_opts);
        TEST_ASSERT(stream != nullptr, "Stream creation with custom options");

        if (stream) {
            P2pStreamClose(stream);
        }

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for stream test");
    }

    Cert_Destroy(cert_handle);
}

// Test Stream API invalid parameters
void TestStreamAPIInvalidParams() {
    TestLogger::Log("=== Testing Stream API Invalid Parameters ===");

    // Test with null socket
    P2P_STREAM stream = P2pStreamCreate(nullptr, nullptr);
    TEST_ASSERT(stream == nullptr, "Stream create with null socket");

    // Test operations on null stream
    int result = P2pStreamClose(nullptr);
    TEST_ASSERT(result != 0, "Close null stream");

    result = P2pStreamWrite(nullptr, "test", 4);
    TEST_ASSERT(result <= 0, "Write to null stream");

    char buffer[100];
    result = P2pStreamRead(nullptr, buffer, sizeof(buffer));
    TEST_ASSERT(result <= 0, "Read from null stream");

    result = P2pStreamGetState(nullptr);
    TEST_ASSERT(result < 0, "Get state of null stream");

    result = P2pStreamGetId(nullptr);
    TEST_ASSERT(result < 0, "Get ID of null stream");

    result = P2pStreamGetBufferedBytes(nullptr);
    TEST_ASSERT(result < 0, "Get buffered bytes of null stream");

    P2P_SOCKET socket = P2pStreamGetSocket(nullptr);
    TEST_ASSERT(socket == nullptr, "Get socket of null stream");
}

// Test vectored I/O operations
void TestVectoredIO() {
    TestLogger::Log("=== Testing Vectored I/O Operations ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for vectored IO test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test P2pWritev with null parameters
        int result = P2pWritev(socket, nullptr, 0);
        TEST_ASSERT(result <= 0, "Writev with null iovec");

        // Test with valid but empty iovec array
        struct p2p_iovec iov[2];
        char data1[] = "Hello";
        char data2[] = "World";

        iov[0].iov_base = data1;
        iov[0].iov_len = strlen(data1);
        iov[1].iov_base = data2;
        iov[1].iov_len = strlen(data2);

        // This should fail since socket is not connected
        result = P2pWritev(socket, iov, 2);
        TEST_ASSERT(result <= 0, "Writev on unconnected socket");

        // Test stream vectored operations
        P2P_STREAM stream = P2pStreamCreate(socket, nullptr);
        if (stream) {
            result = P2pStreamWritev(stream, nullptr, 0);
            TEST_ASSERT(result <= 0, "Stream writev with null iovec");

            result = P2pStreamWritev(stream, iov, 2);
            TEST_ASSERT(result <= 0, "Stream writev on unconnected socket");

            P2pStreamClose(stream);
        }

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for vectored IO test");
    }

    Cert_Destroy(cert_handle);
}

// Test polling operations
void TestPollingOperations() {
    TestLogger::Log("=== Testing Polling Operations ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for polling test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test socket polling
        PollEvent events;
        events.events = P2PPOLLIN | P2PPOLLOUT;

        int result = P2pPoll(socket, &events, 100); // 100ms timeout
        TEST_ASSERT(result >= 0, "Socket poll operation");

        // Test stream polling
        P2P_STREAM stream = P2pStreamCreate(socket, nullptr);
        if (stream) {
            struct StreamEvent stream_events[5];
            result = P2pStreamPoll(stream, stream_events, 5, 100);
            TEST_ASSERT(result >= 0, "Stream poll operation");

            P2pStreamClose(stream);
        }

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for polling test");
    }

    Cert_Destroy(cert_handle);
}

// Stream event callback for testing
void TestStreamEventCallback(P2P_STREAM stream, struct StreamEvent* event, void* user_data) {
    TestLogger::Log(LOG_DEBUG, "Stream event callback triggered");
    bool* callback_called = (bool*)user_data;
    if (callback_called) {
        *callback_called = true;
    }
}

// Test stream callback functionality
void TestStreamCallback() {
    TestLogger::Log("=== Testing Stream Callback ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for callback test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        P2P_STREAM stream = P2pStreamCreate(socket, nullptr);
        if (stream) {
            bool callback_called = false;

            // Test setting callback
            int result = P2pStreamSetCallback(stream, TestStreamEventCallback, &callback_called);
            TEST_ASSERT(result == 0, "Set stream callback");

            // Test setting null callback
            result = P2pStreamSetCallback(stream, nullptr, nullptr);
            TEST_ASSERT(result == 0, "Set null stream callback");

            P2pStreamClose(stream);
        }

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for callback test");
    }

    Cert_Destroy(cert_handle);
}

// Test server-client connection (simplified version)
void TestServerClientConnection() {
    TestLogger::Log("=== Testing Server-Client Connection ===");

    CertHandle server_cert = Cert_Create();
    CertHandle client_cert = Cert_Create();

    if (!server_cert || !client_cert) {
        TEST_ASSERT(false, "Certificate creation for connection test");
        if (server_cert) Cert_Destroy(server_cert);
        if (client_cert) Cert_Destroy(client_cert);
        return;
    }

    // Create server socket
    SocketOptions server_options = CreateSocketOptions(MODE_SERVER, server_cert);
    P2P_SOCKET server_socket = P2pCreate(&server_options);

    if (!server_socket) {
        TEST_ASSERT(false, "Server socket creation");
        Cert_Destroy(server_cert);
        Cert_Destroy(client_cert);
        return;
    }

    // Bind server to a port
    int test_port = g_config.test_port + 1; // Use different port to avoid conflicts
    int result = P2pBind(server_socket, "0.0.0.0", test_port);
    TEST_ASSERT(result == 0, "Server bind");

    if (result == 0) {
        result = P2pListen(server_socket);
        TEST_ASSERT(result == 0, "Server listen");

        // Create client socket
        SocketOptions client_options = CreateSocketOptions(MODE_CLIENT, client_cert);
        P2P_SOCKET client_socket = P2pCreate(&client_options);

        if (client_socket) {
            // Set a short timeout for the test
            P2pSetConnTimeout(client_socket, 2000); // 2 seconds

            // Try to connect (this will likely fail since we're not running a real server loop)
            result = P2pConnect(client_socket, g_config.test_ip.c_str(), test_port);
            // We expect this to fail in unit test environment, but the API should handle it gracefully
            TEST_ASSERT(result != 0, "Client connect (expected to fail in unit test)");

            P2pClose(client_socket);
        } else {
            TEST_ASSERT(false, "Client socket creation");
        }
    }

    P2pClose(server_socket);
    Cert_Destroy(server_cert);
    Cert_Destroy(client_cert);
}

// Test data transmission on unconnected sockets (should fail gracefully)
void TestDataTransmissionUnconnected() {
    TestLogger::Log("=== Testing Data Transmission on Unconnected Sockets ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for data transmission test");
        return;
    }

    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    P2P_SOCKET socket = P2pCreate(&options);

    if (socket) {
        // Test writing to unconnected socket
        const char* test_data = "Hello, World!";
        int result = P2pWrite(socket, test_data, strlen(test_data));
        TEST_ASSERT(result <= 0, "Write to unconnected socket");

        // Test reading from unconnected socket
        char buffer[100];
        result = P2pRead(socket, buffer, sizeof(buffer));
        TEST_ASSERT(result <= 0, "Read from unconnected socket");

        // Test stream operations on unconnected socket
        P2P_STREAM stream = P2pStreamCreate(socket, nullptr);
        if (stream) {
            result = P2pStreamWrite(stream, test_data, strlen(test_data));
            TEST_ASSERT(result <= 0, "Stream write to unconnected socket");

            result = P2pStreamRead(stream, buffer, sizeof(buffer));
            TEST_ASSERT(result <= 0, "Stream read from unconnected socket");

            P2pStreamClose(stream);
        }

        P2pClose(socket);
    } else {
        TEST_ASSERT(false, "Socket creation for data transmission test");
    }

    Cert_Destroy(cert_handle);
}

// Test multiple socket types
void TestMultipleSocketTypes() {
    TestLogger::Log("=== Testing Multiple Socket Types ===");

    enum SOCKET_TYPE types[] = {SOCKET_MULTITCP, SOCKET_SSL, SOCKET_QUIC};
    const char* type_names[] = {"MULTITCP", "SSL", "QUIC"};
    int num_types = sizeof(types) / sizeof(types[0]);

    for (int i = 0; i < num_types; i++) {
        TestLogger::Log("Testing socket type: " + std::string(type_names[i]));

        CertHandle cert_handle = Cert_Create();
        if (!cert_handle) {
            TEST_ASSERT(false, "Certificate creation for type " + std::string(type_names[i]));
            continue;
        }

        SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
        options.type = types[i];

        P2P_SOCKET socket = P2pCreate(&options);
        bool created = (socket != nullptr);
        TEST_ASSERT(created, "Create socket type " + std::string(type_names[i]));

        if (socket) {
            // Test basic operations
            int result = P2pSetConnTimeout(socket, 1000);
            TEST_ASSERT(result == 0, "Set timeout for " + std::string(type_names[i]));

            result = P2pSetSendMode(socket, 1);
            TEST_ASSERT(result == 0, "Set send mode for " + std::string(type_names[i]));

            result = P2pSetReadMode(socket, 0);
            TEST_ASSERT(result == 0, "Set read mode for " + std::string(type_names[i]));

            P2pClose(socket);
        }

        Cert_Destroy(cert_handle);
    }
}

// Test edge cases and boundary conditions
void TestEdgeCases() {
    TestLogger::Log("=== Testing Edge Cases ===");

    CertHandle cert_handle = Cert_Create();
    if (!cert_handle) {
        TEST_ASSERT(false, "Certificate creation for edge cases test");
        return;
    }

    // Test with invalid socket options
    SocketOptions invalid_options = {};
    invalid_options.mode = (enum SOCKET_MODE)999; // Invalid mode
    invalid_options.type = (enum SOCKET_TYPE)999; // Invalid type

    P2P_SOCKET socket = P2pCreate(&invalid_options);
    TEST_ASSERT(socket == nullptr, "Create socket with invalid options");

    // Test with zero worker number
    SocketOptions options = CreateSocketOptions(MODE_CLIENT, cert_handle);
    options.wokernum = 0;

    socket = P2pCreate(&options);
    // This might succeed or fail depending on implementation
    if (socket) {
        P2pClose(socket);
    }
    TEST_ASSERT(true, "Create socket with zero workers (implementation dependent)");

    // Test binding to invalid port
    options = CreateSocketOptions(MODE_SERVER, cert_handle);
    socket = P2pCreate(&options);

    if (socket) {
        int result = P2pBind(socket, "0.0.0.0", -1); // Invalid port
        TEST_ASSERT(result != 0, "Bind to invalid port");

        result = P2pBind(socket, "0.0.0.0", 65536); // Port out of range
        TEST_ASSERT(result != 0, "Bind to port out of range");

        result = P2pBind(socket, "invalid.ip.address", 4433); // Invalid IP
        TEST_ASSERT(result != 0, "Bind to invalid IP address");

        P2pClose(socket);
    }

    Cert_Destroy(cert_handle);
}

int main(int argc, char* argv[]) {
#ifndef _WIN32
    signal(SIGPIPE, SIG_IGN);
#endif

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-type" && i + 1 < argc) {
            g_config.socket_type = (enum SOCKET_TYPE)std::stoi(argv[++i]);
        } else if (arg == "-port" && i + 1 < argc) {
            g_config.test_port = std::stoi(argv[++i]);
        } else if (arg == "-ip" && i + 1 < argc) {
            g_config.test_ip = argv[++i];
        } else if (arg == "-workers" && i + 1 < argc) {
            g_config.worker_num = std::stoi(argv[++i]);
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  -type <n>     Socket type (1=MULTITCP, 2=SSL, 3=QUIC)" << std::endl;
            std::cout << "  -port <n>     Test port (default: 4433)" << std::endl;
            std::cout << "  -ip <addr>    Test IP address (default: 127.0.0.1)" << std::endl;
            std::cout << "  -workers <n>  Number of workers (default: 4)" << std::endl;
            std::cout << "  -h, --help    Show this help message" << std::endl;
            return 0;
        }
    }

    TestLogger::Log("Starting P2P Socket Interface Unit Tests");
    TestLogger::Log("Configuration:");
    TestLogger::Log("  Socket type: " + std::to_string(g_config.socket_type));
    TestLogger::Log("  Test port: " + std::to_string(g_config.test_port));
    TestLogger::Log("  Test IP: " + g_config.test_ip);
    TestLogger::Log("  Workers: " + std::to_string(g_config.worker_num));
    TestLogger::Log("");

    // Run basic socket tests
    TestBasicSocketOperations();
    TestInvalidParameters();
    TestSocketBinding();
    TestSocketSettings();

    // Run Stream API tests
    TestStreamAPIBasics();
    TestStreamAPIInvalidParams();
    TestVectoredIO();
    TestPollingOperations();
    TestStreamCallback();

    // Run advanced tests
    TestServerClientConnection();
    TestDataTransmissionUnconnected();
    TestMultipleSocketTypes();
    TestEdgeCases();

    // Print test summary
    TestLogger::Log("");
    g_testResult.PrintSummary();

    if (g_testResult.failed_tests == 0) {
        TestLogger::Log("All tests passed! ✓");
    } else {
        TestLogger::Log("Some tests failed! ✗");
    }

    TestLogger::Log("Unit tests completed");
    return g_testResult.failed_tests > 0 ? 1 : 0;
}
