# Vectors generated by OpenSSL
# POINT is hex encoded binary data while X and Y are hexadecimal integers

COUNT = 0
CURVE = SECP256R1
POINT = 037399336a9edf2197c2f8eb3d39aed9c34a66e45d918a07dc7684c42c9b37ac68
X = 7399336a9edf2197c2f8eb3d39aed9c34a66e45d918a07dc7684c42c9b37ac68
Y = 6699ececc4f5f0d756d3c450708a0694eb0a07a68b805070b40b058d27271f6d

COUNT = 1
CURVE = SECP256R1
POINT = 02d13b763988943682267deb6298ad3bdfec192459f9e9bf2d2227c8ec3e8ced91
X = d13b763988943682267deb6298ad3bdfec192459f9e9bf2d2227c8ec3e8ced91
Y = badeb7ee4662680c587a84de3f2d1ca7284b65790597408836eea3207e2f3a22

COUNT = 2
CURVE = SECP256K1
POINT = 032cac5ae983fcb88bc502dd48d561c810e2b40edc7b6b67ea52ceb415093be0d0
X = 2cac5ae983fcb88bc502dd48d561c810e2b40edc7b6b67ea52ceb415093be0d0
Y = 1445c276b193e10679bbdf638717c8db78021e79639e171e8cd37057b7798d0d

COUNT = 3
CURVE = SECP256K1
POINT = 02e836ff83c6ab4d7ea391f4897cb926b16cbe6eb0991dd81b4a294c65fe9a9691
X = e836ff83c6ab4d7ea391f4897cb926b16cbe6eb0991dd81b4a294c65fe9a9691
Y = 8cb0f15636148db6f54ad50c7da4aa2920519391678c24ad8db8097c6bf168b0
