mydir=lib$(S)krb5$(S)unicode
BUILDTOP=$(REL)..$(S)..$(S)..

##DOS##BUILDTOP = ..\..\..
##DOS##PREFIXDIR=unicode
##DOS##OBJFILE=..\$(OUTPRE)$(PREFIXDIR).lst

XXDIR = $(srcdir)/ucdata/
XXHEADERS = ucdata.h ure.h uctable.h
XXSRCS  = ucdata.c ucgendat.c ure.c urestubs.c

STLIBOBJS= \
	ucdata.o	\
	ure.o		\
	urestubs.o	\
	ucstr.o		

OBJS= \
	$(OUTPRE)ucdata.$(OBJEXT)	\
	$(OUTPRE)ure.$(OBJEXT)		\
	$(OUTPRE)urestubs.$(OBJEXT)	\
	$(OUTPRE)ucstr.$(OBJEXT)	

SRCS= \
	$(srcdir)/ucstr.c	

EXTRADEPSRCS = 

##DOS##LIBOBJS = $(OBJS)

all-unix: all-libobjs
clean-unix:: clean-libobjs

shared:
	mkdir shared

uctable.h: $(XXDIR)/uctable.h

$(XXDIR)/uctable.h: $(XXDIR)/ucgendat.c $(srcdir)/UnicodeData.txt $(srcdir)/CompositionExclusions.txt
	$(MAKE) ucgendat
	./ucgendat $(srcdir)/UnicodeData.txt -x $(srcdir)/CompositionExclusions.txt

ucgendat: ucgendat.o
	$(CC_LINK) $(ALL_CFLAGS) -o ucgendat ucgendat.o $(LIBS)

##DOS##!if 0
.links :
	@for i in $(XXSRCS) $(XXHEADERS); do \
		$(RM) $$i ; \
		ii=`find $(srcdir) -name $$i` ; \
		$(LN_S) $$ii . ; \
	done
	touch .links
##DOS##!endif
##DOS##.links:
##DOS##		$(CP) $(srcdir)\ucdata\ucdata.h ucdata.h
##DOS##		$(CP) $(srcdir)\ucdata\ucdata.c ucdata.c
##DOS##		$(CP) $(srcdir)\ucdata\ucgendat.c ucgendat.c
##DOS##		$(CP) $(srcdir)\ucdata\uctable.h uctable.h
##DOS##		$(CP) $(srcdir)\ure\ure.h ure.h
##DOS##		$(CP) $(srcdir)\ure\ure.c ure.c
##DOS##		$(CP) $(srcdir)\ure\urestubs.c urestubs.c
##DOS##		$(CP) nul .links

$(XXSRCS) $(XXHEADERS) : .links

clean:
	$(RM) *.dat .links $(XXHEADERS) $(XXSRCS) ucgendat

depend: .links

@libobj_frag@
