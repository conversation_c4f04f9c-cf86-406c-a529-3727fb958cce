# We can't make any commitment to support the DJGPP platform,
# and rely entirely on the OpenSSL community to help is fine
# tune and test.

my %targets = (
    "DJGPP" => {
        inherit_from     => [ "BASE_unix" ],
        CC               => "gcc",
        CFLAGS           => "-fomit-frame-pointer -O2 -Wall",
        cflags           => "-I/dev/env/WATT_ROOT/inc -DTERMIOS -DL_ENDIAN",
        sys_id           => "MSDOS",
        lflags           => add("-L/dev/env/WATT_ROOT/lib"),
        ex_libs          => add("-lwatt"),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => "a.out",
    },
);
