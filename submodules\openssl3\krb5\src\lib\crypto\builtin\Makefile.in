mydir=lib$(S)crypto$(S)builtin
BUILDTOP=$(REL)..$(S)..$(S)..
SUBDIRS=camellia des aes md4 md5 sha1 sha2 enc_provider hash_provider
LOCALINCLUDES = -I$(srcdir)/../krb -I$(srcdir)

##DOS##BUILDTOP = ..\..\..
##DOS##PREFIXDIR = builtin
##DOS##OBJFILE = ..\$(OUTPRE)builtin.lst

STLIBOBJS=\
	hmac.o	\
	init.o	\
	pbkdf2.o

OBJS=\
	$(OUTPRE)hmac.$(OBJEXT)	\
	$(OUTPRE)init.$(OBJEXT)	\
	$(OUTPRE)pbkdf2.$(OBJEXT)

SRCS=\
	$(srcdir)/hmac.c	\
	$(srcdir)/init.c	\
	$(srcdir)/pbkdf2.c	

STOBJLISTS= des/OBJS.ST md4/OBJS.ST 	\
	md5/OBJS.ST sha1/OBJS.ST sha2/OBJS.ST	\
	enc_provider/OBJS.ST 		\
	hash_provider/OBJS.ST 		\
	aes/OBJS.ST 			\
	camellia/OBJS.ST 		\
	OBJS.ST

SUBDIROBJLISTS= des/OBJS.ST md4/OBJS.ST 	\
		md5/OBJS.ST sha1/OBJS.ST sha2/OBJS.ST 	\
		enc_provider/OBJS.ST 		\
		hash_provider/OBJS.ST 		\
		aes/OBJS.ST			\
		camellia/OBJS.ST 

##DOS##LIBOBJS = $(OBJS)

all-unix: all-libobjs
includes: depend

depend: $(SRCS)

clean-unix:: clean-libobjs

@lib_frag@
@libobj_frag@

