{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 289, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of IEEE P1363 encoded ECDSA signatures."], "notes": {"EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission.", "SigSize": "The size of the signature should always be twice the number of bytes of the size of the order. But some libraries accept signatures with less bytes."}, "schema": "ecdsa_p1363_verify_schema.json", "testGroups": [{"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "KSexBRK64-3c_kZ4KBKLrSkDJpkZ9whgacjE32xzKDg", "y": "x3h5ZOqsAOWSH7FJimD0YGdms9loUAFVjRqXTnNBUT4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "wx": "2927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838", "wy": "00c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKSexBRK64+3c/kZ4KBKLrSkDJpkZ\n9whgacjE32xzKDjHeHlk6qwA5ZIfsUmKYPRgZ2az2WhQAVWNGpdOc0FRPg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c05f85a63a5be977ad714cea16b10035f07cadf7513ae8cca86f35b7692aafd69f", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "012478f1cf49f6d858ac900a7af177222661ac95e206d32ee63020beee955ca71100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2", "result": "invalid", "flags": []}, {"tcId": 3, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "db870e2eb60927a9536ff5850e88ddd918215f79475c0e23b752d6976369a391a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "012478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c000a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "db870e2fb60927a8536ff5850e88ddd95b3a64cba0446f9ec3990bd467067e40a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "002478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c001a07a59c3a41688548eb315e94effca0efd1ffe0a13467061783dde1cce167403", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "002478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c001a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c05f85a63b5be977ac714cea16b10035f0bfc6fca393d12e237b7beca62e4cb14e", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 10, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 11, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 12, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 13, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 14, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 15, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 16, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 17, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 18, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 19, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 20, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 21, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 22, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 23, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 24, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 25, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 26, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 27, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 28, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 29, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 30, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 31, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 32, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 33, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 34, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 35, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 36, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 37, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 38, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 39, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 40, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 41, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 42, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 43, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 44, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 45, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 46, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 47, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 48, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 49, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 50, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 51, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff000000010000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 52, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff000000010000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 53, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 54, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 55, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 56, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 57, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000001000000000000000000000000ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 58, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3932323038", "sig": "64a1aab5000d0e804f3e2fc02bdee9be8ff312334e2ba16d11547c97711c898e3c623e7f7598376825fa8bc09e727c75794cbb4ee8716ae15c31cd1cbe9ca3ee", "result": "valid", "flags": []}, {"tcId": 59, "comment": "special case hash", "msg": "33393439313934313732", "sig": "3a4f61f7f8c4546e3580f7848411786fee1229a07a6ecf5fb84870869188215d18c5ce44354e2274eadb8fea319f8d6f60944532dbaae86bfd8105f253041bcb", "result": "valid", "flags": []}, {"tcId": 60, "comment": "special case hash", "msg": "35333637363431383737", "sig": "3fa9975fb2b08b7b6e33f3843099da3f43f1dcfe9b171a60cafd5489ca9c5328985a86825a0cc728f5d9dac2a513b49127a06100f0fc4b8b1f200903e0df9ed2", "result": "valid", "flags": []}, {"tcId": 61, "comment": "special case hash", "msg": "35363731343831303935", "sig": "4d66e7ee5edd02ab96db25954050079ef8de1d0f02f34d4d75112eaf3f7312406292d1563140013c589be40e599862bdd6bda2103809928928a119b43851a2ce", "result": "valid", "flags": []}, {"tcId": 62, "comment": "special case hash", "msg": "3131323037313732393039", "sig": "a9228305f7b486f568eb65d44e49ba007e3f14b8f23c689c952e4ced1e6cf91eb73c74d28bd1268002bed784a6b06c40a90ee5938ea6d08f272d027e0f96a72c", "result": "valid", "flags": []}, {"tcId": 63, "comment": "special case hash", "msg": "3131323938303334323336", "sig": "3fa39842bfab6c38afa7963c60beb09484d4579fc75ef09efff44e91bc62ca835612add1924f0285ace5b158828e2b32ab2b6e7f10ee68dca1cc54591fee1fec", "result": "valid", "flags": []}, {"tcId": 64, "comment": "special case hash", "msg": "39383736303239363833", "sig": "06c04b02edfeecd8620f035ea4f449bd924593e86e5288a6f22d1923b0e2e8a9f666718e6fefb515bb9339d29cc0e58cfba89d605ca0066bca87f6a3f08ebcfa", "result": "valid", "flags": []}, {"tcId": 65, "comment": "special case hash", "msg": "3230323034323936353139", "sig": "1ddd953c32a5f84109cd4d9ec8c364dd318376ff5d228211a367483077d63880563dba4845de762baf04910618d587e0dd0c97dd1c9785c24ffdf2f8a660abf2", "result": "valid", "flags": []}, {"tcId": 66, "comment": "special case hash", "msg": "31343531363639313830", "sig": "9fe4ec4831ef4945f100d5d35a2e6312411ca5df6c900ca60690f2985d553482c674ad5e1bead2f767c9248e444452a4a8530dd47246cbbc968da865bdf212b6", "result": "valid", "flags": []}, {"tcId": 67, "comment": "special case hash", "msg": "31303933363835393531", "sig": "e8703d6b16a79fc2ab3653cece29d06f65dd6f2c230cb08ee30c5517407d75db8cfeb87b8e95ddacd638b37d315393c5005f3ab8bba0cc1cd1a050829b775bfb", "result": "valid", "flags": []}, {"tcId": 68, "comment": "special case hash", "msg": "36323139353630323031", "sig": "def608caf1f277d71403009f209c1d7eef11aaa7920397fbf429b8146181aecef3b8f2aa5b3df9a8b37313ea66ad5b74673f3e8614ff471b1eb6773217511fb0", "result": "valid", "flags": []}, {"tcId": 69, "comment": "special case hash", "msg": "35363832343734333033", "sig": "4f5d08e8d936ce831d02d6b23fb8fce0e0750101af3ab9c3b28636b95a5e24ad6f034480553bcecac221f8be8288163c55492e2e56a88f4d0341b61436a0a6c0", "result": "valid", "flags": []}, {"tcId": 70, "comment": "special case hash", "msg": "33373336353331373836", "sig": "bdd822bfe3733d9f4b88764fe091db2e8f8af366e4c44d876bf82e62bd48c7ee7fbf7750c5dc849a2c55dbdd067806f869652a7b3a57baa4733781d3128f02de", "result": "valid", "flags": []}, {"tcId": 71, "comment": "special case hash", "msg": "34373935393033373932", "sig": "1c4fc02961b7f4245566b410bf08f447502ea4f75b15690344681efa2edf7b4b7d63eef119dc88bc4a1b2c43ac21cd53892443661f8c3a97d558bf888c29f769", "result": "valid", "flags": []}, {"tcId": 72, "comment": "special case hash", "msg": "39333939363131303037", "sig": "6406f2d249ab1264e175476ca3300efd049fcad569dff40b922082b41cc7b7ce461872b803383f785077714a9566c4d652e87b2cad90dd4f4cc84bc55004c530", "result": "valid", "flags": []}, {"tcId": 73, "comment": "special case hash", "msg": "31303837343931313835", "sig": "415c924b9ba1902b340058117d90623602d48b8280583fb231dc93823b83a153f18be8cdc2063a26ab030504d3397dc6e9c6b6c56f4e3a59832c0e4643c0263c", "result": "valid", "flags": []}, {"tcId": 74, "comment": "special case hash", "msg": "33323336363738353030", "sig": "d12e96c7d2f177b7cf6d8a1ede060a2b174dc993d43f5fe60f75604824b64fef0c97d87035fcca0a5f47fe6461bb30cbaf05b37e4211ec3fcd51fc71a12239ca", "result": "valid", "flags": []}, {"tcId": 75, "comment": "special case hash", "msg": "31343438393937373033", "sig": "7df72a64c7e982c88f83b3a22802690098147e0e42ef4371ef069910858c0646adbaa7b10c6a3f995ed5f83d7bda4ba626b355f34a72bf92ff788300b70e72d0", "result": "valid", "flags": []}, {"tcId": 76, "comment": "special case hash", "msg": "35373134363332383037", "sig": "047c4306f8d30e425ae70e0bee9e0b94faa4ef18a9c6d7f2c95de0fe6e2a32377a4d0d0a596bd9ea3fe9850e9c8c77322594344623c0b46ac2a8c95948aefd98", "result": "valid", "flags": []}, {"tcId": 77, "comment": "special case hash", "msg": "323236343837343932", "sig": "57d603a367e23af39c95dd418c0176da8b211d50b1be82bf5ef621a2640204f75dc3f285ad015c4d71157bd11e5b8df6a89e4b267393b08b5ad5013bdae544b1", "result": "valid", "flags": []}, {"tcId": 78, "comment": "special case hash", "msg": "35333533343439343739", "sig": "11df6741021ec8cc567584aea16817c540859c4e5011551c00b097fcfc2337e5668551919d43206ac0571fc5ad3ac0efb489bea599e7bf99fe4c7468d6c2c5e0", "result": "valid", "flags": []}, {"tcId": 79, "comment": "special case hash", "msg": "34373837333033383830", "sig": "7451ffede471bd370406533436fc42a89daa0af4903d087cbc062fe7e54dbf70590895398f22b48ce72cbf7c3d3ee1dd7fb0ee645edb0b1b1de35f370e5bf5ee", "result": "valid", "flags": []}, {"tcId": 80, "comment": "special case hash", "msg": "32323332313935383233", "sig": "fc4c4d81da6f687a6426263193c1a680b67734a1b180647b8c76407cc4f0a9c656f775d372c9bee685374085be676c9cf31cf1f978a5e6ccb04e4a0761159cc7", "result": "valid", "flags": []}, {"tcId": 81, "comment": "special case hash", "msg": "3130373339333931393137", "sig": "feb978ca33c46ffba47eb63bb40de7833e43d5654575b54de1fea3d1de3c8ad5108078ba997bfa064521baf342c97b0c64bd25240c8fd0fd7533ae2d03081b70", "result": "valid", "flags": []}, {"tcId": 82, "comment": "special case hash", "msg": "31383831303237333135", "sig": "cc61729698467ba53da199ff481fe7433f194fc96367907e8dc5e1d9f42b1e2183dd9ef156e7c1f9c09b3bf86a4f1c88e5dd20cd74d997858e600797dbe74ad2", "result": "valid", "flags": []}, {"tcId": 83, "comment": "special case hash", "msg": "36303631363933393037", "sig": "d47f616303ff0eb813eac32e760ba30ad445e0af7dc57e70756104823f6a895f047f2217b399c46a426b936a124980a6011f0896f51dbe07632828a72d7173f1", "result": "valid", "flags": []}, {"tcId": 84, "comment": "special case hash", "msg": "38383935323237303934", "sig": "cff73dfa2bac67ce1340b25c885abb3e7979ef7f840f15d5f19e86640cdd40a3c7d1210802796c4f251049ee08a2c29f5c71064033d17010c65bf2e94499381e", "result": "valid", "flags": []}, {"tcId": 85, "comment": "special case hash", "msg": "31353830323334303934", "sig": "10acaf9c485ab1220355b95be269f124e12eb252f2224b0fc50785eb2ee3df4532443b557efc6896347fa778e1fcf33cbb769c9a7da896b20d93fea7c2791ea4", "result": "valid", "flags": []}, {"tcId": 86, "comment": "special case hash", "msg": "33393635393931353132", "sig": "f919da0651abc2bff994a879d2778fa5195d57400e003e8dd6adb3fc7a0cc4cc9b945d06bd119665b278a59bd24fdd2350817d0be87997bee57b70c479d64a2d", "result": "valid", "flags": []}, {"tcId": 87, "comment": "special case hash", "msg": "32323838373332313938", "sig": "cc38e7a018f6d70b2d9b49120cc9b4a169f2f72238821a86b81f553b6225d24e276efd8bf06ccce07c7aae35eaac3bd1c374dcf0cf0588d5e0e4171936688636", "result": "valid", "flags": []}, {"tcId": 88, "comment": "special case hash", "msg": "32323330383837333139", "sig": "ff85ad66621991c318b85cef73c576cb2a8d43c568c1aafc85b40ef2a9a6b41c732a79e6837ebf8434fea6e7fefa948f506ae455c1a3eb36a030185a23037d96", "result": "valid", "flags": []}, {"tcId": 89, "comment": "special case hash", "msg": "313239303536393337", "sig": "33f016e51eef9b1136380cb8b84c6b38b107e24c6731bd07cb1c7f4a29f33a8336b177bb8be94c8be67ff3a41fcc4d22b5c9eb377da713eb014ae01c64ca6dd7", "result": "valid", "flags": []}, {"tcId": 90, "comment": "special case hash", "msg": "32373438363536343338", "sig": "929413ee91f27454d74e91370a10a86fc98ac7305c8ab4ca59752bda3a7bfc37483b47a26a0d7d2e6bd37d351d9ee37c5ec2a4686d884d78b6beb7f6b08c50f9", "result": "valid", "flags": []}, {"tcId": 91, "comment": "special case hash", "msg": "37353833353032363034", "sig": "578202c7d0abac93ca43dde3cb44414e5601c1eb557604cb9adb4bde0a12633bfb9a7412e307aee95ef4b53540571a21559414e5306794ab5182cfb229dab3e9", "result": "valid", "flags": []}, {"tcId": 92, "comment": "special case hash", "msg": "32333237373534323739", "sig": "46d45ad0bb75b8639d0e91d8450fc31887c211328a5784fc83b4cb7f5b962c1bd6751d13ede2079b7aa1d822bdb32d7f3cf00273a1ff03df90c0ec7c62a47568", "result": "valid", "flags": []}, {"tcId": 93, "comment": "special case hash", "msg": "373735353038353834", "sig": "abe84c941783d5ced284fea56341ecc68d6bdd3196d318fbd074641f8c885bd5bdea3c44d48e01aa40935c1c9723ff733199563440f26b4ecf0b444b0418d9f5", "result": "valid", "flags": []}, {"tcId": 94, "comment": "special case hash", "msg": "3137393832363438333832", "sig": "05277cdbf491e336fe81be24e393a161a4fb89112c9ffed1ee6649c406713408ab6934332e68e108bb0484d21c457dcf381a620c3a4712fdbfeb658a3fafd60c", "result": "valid", "flags": []}, {"tcId": 95, "comment": "special case hash", "msg": "32333936373737333635", "sig": "293825737c8c14430ed10dbadd7da337275f9b61d1d26377f778ffaa00c139decdddec267a8678c96829bf6c1d6f38322e119937cfd2fee01e9dc9525f43ed6b", "result": "valid", "flags": []}, {"tcId": 96, "comment": "special case hash", "msg": "35393938313035383031", "sig": "2041fdd6111c45dfd29e750e082dcdadc9a584a8a2be46580fb0ba3b3dc65862421824fe987e4172a0f8bbcb7bcd9e1b073b7742ed9f9df98f2a1a37cd374ce3", "result": "valid", "flags": []}, {"tcId": 97, "comment": "special case hash", "msg": "3136363737383237303537", "sig": "267941db660e046ab14e795669e002b852f7788447c53ebef46a2056978b5574d00183bcaf75bc11e37653f952f6a6537151c3aa0a1b9e4e41b004a29185395b", "result": "valid", "flags": []}, {"tcId": 98, "comment": "special case hash", "msg": "323036323134333632", "sig": "5dcd7f6814739d47f80a363b9414e6cbfb5f0846223888510abd5b3903d7ae0943418f138bb3c857c0ad750ca8389ebcf3719cb389634ac54a91de9f18fd7238", "result": "valid", "flags": []}, {"tcId": 99, "comment": "special case hash", "msg": "36383432343936303435", "sig": "5e0e8cc0280409a0ce252da02b2424d2de3a52b406c3778932dbc60cb86c356793d25e929c5b00e950d89585ec6c01b6589ae0ec0af8a79c04df9e5b27b58bc5", "result": "valid", "flags": []}, {"tcId": 100, "comment": "special case hash", "msg": "33323639383937333231", "sig": "4fcf9c9d9ffbf4e0b98268c087071bffe0673bb8dcb32aa667f8a639c364ea47820db0730bee8227fc831643fcb8e2ef9c0f7059ce42da45cf74828effa8d772", "result": "valid", "flags": []}, {"tcId": 101, "comment": "special case hash", "msg": "31333837333234363932", "sig": "c60cd2e08248d58d1639b123633643c63f89aff611f998937ccb08c9113bcdcaac4bb470ce0164616dada7a173364ed3f9d16fd32c686136f904c99266fda17e", "result": "valid", "flags": []}, {"tcId": 102, "comment": "special case hash", "msg": "34313138383837353336", "sig": "7cfdaf6f22c1c7668d7b6f56f8a7be3fdeeb17a7863539555bbfa899dd70c5f1cee151adc71e68483b95a7857a862ae0c5a6eee478d93d40ccc7d40a31dcbd90", "result": "valid", "flags": []}, {"tcId": 103, "comment": "special case hash", "msg": "393838363036353435", "sig": "2270be7ee033a706b59746eab34816be7e15c8784061d5281060707a0abe0a7d56a163341ee95e7e3c04294a57f5f7d24bf3c3c6f13ef2f161077c47bd27665d", "result": "valid", "flags": []}, {"tcId": 104, "comment": "special case hash", "msg": "32343739313135383435", "sig": "16b5d2bfcaba21167a69f7433d0c476b21ded37d84dc74ca401a3ecddb2752a862852cf97d89adfb0ebbe6f398ee641bfea8a2271580aac8a3d8326d8c6e0ef9", "result": "valid", "flags": []}, {"tcId": 105, "comment": "special case hash", "msg": "35303736383837333637", "sig": "d907eefa664115848b90c3d5baa0236f08eafaf81c0d52bb9d0f8acb57490847fd91bc45a76e31cdc58c4bfb3df27f6470d20b19f0fba6a77b6c8846650ed8a6", "result": "valid", "flags": []}, {"tcId": 106, "comment": "special case hash", "msg": "393838353036393637", "sig": "048337b34f427e8774b3bf7c8ff4b1ae65d132ac8af94829bb2d32944579bb31bd6f8eab82213ccf80764644204bb6bf16c668729cdd31dd8596286c15686e8e", "result": "valid", "flags": []}, {"tcId": 107, "comment": "special case hash", "msg": "32373231333036313331", "sig": "b2bc46b7c44293557ab7ebeb0264924277193f87a25d94c924df1518ba7c7260abf1f6238ff696aaafaf4f0cbbe152c3d771c5bfc43f36d7e5f5235819d02c1a", "result": "valid", "flags": []}, {"tcId": 108, "comment": "special case hash", "msg": "33323034313031363535", "sig": "40d4b38a61232e654ffd08b91e18609851f4189f7bf8a425ad59d9cbb1b54c999e775a7bd0d934c3ed886037f5d3b356f60eda41191690566e99677d7aaf64f3", "result": "valid", "flags": []}, {"tcId": 109, "comment": "special case hash", "msg": "33313530363830393530", "sig": "ac8f64d7df8d9fea005744e3ac4af70aa3a38e5a0f3d069d85806a4f29710339c014e96decfef3857cc174f2c46ad0882bef0c4c8a17ce09441961e4ae8d2df3", "result": "valid", "flags": []}, {"tcId": 110, "comment": "special case hash", "msg": "31373237343630313033", "sig": "41b3766f41a673a01e2c0cab5ceedbcec8d82530a393f884d72aa4e6685dea0a073a55dca2da577cafb40e12dd20bf8529a13a6acdf9a1c7d4b2048d60876cb3", "result": "valid", "flags": []}, {"tcId": 111, "comment": "special case hash", "msg": "3134353731343631323235", "sig": "1942755aa8128382cd8e35a4350c22cc45ba5704d99e8a240970df11956ad866f64cf1e0816cf7ac5044f73ba938e142ef3305cb09becb80a0a5b9ad7ba3eb07", "result": "valid", "flags": []}, {"tcId": 112, "comment": "special case hash", "msg": "34313739353136303930", "sig": "51aba4ff1c7ddf17e0632ab71684d8de6dc700219ef346cb28ce9dafc3565b3bb6aaebe1af0ad01f07a68bf1cf57f9d6040b43c14b7eb8238542760e32ce3b0c", "result": "valid", "flags": []}, {"tcId": 113, "comment": "special case hash", "msg": "35383932373133303534", "sig": "91efbfcc731650e9f004c38b71db146c17bf871c82c4e87716f7ff2f7f9e51d0089ea631a7c5f05311c521d21ba798b5174881f0fd8095fb3a77515913efb6e0", "result": "valid", "flags": []}, {"tcId": 114, "comment": "special case hash", "msg": "33383936313832323937", "sig": "4a7e47bd281ea09b9e3a32934c7a969e1f788f978b41585989f4689e804663fbe65f6bd702403cbbed7f8ad0045f331d4a96fbf8c43f71f11615b7d1b9153b7f", "result": "valid", "flags": []}, {"tcId": 115, "comment": "special case hash", "msg": "38323833333436373332", "sig": "c795f5da86e10a604d4f94bf7cac381c73edad1461d66929e53aa57ca294e89fbae784ab6c7b58332ee05e7d54169edf55ce45f030e71ae8df63969fb327a10c", "result": "valid", "flags": []}, {"tcId": 116, "comment": "special case hash", "msg": "33333636393734383931", "sig": "ea68b24843b225f505e01c0e608b20b4d93e8faf6b9cf70cf8f9134a80e7b668a3abc044b4728f80fe414bdc66f032b262356720547bec7729fad94151c6adc7", "result": "valid", "flags": []}, {"tcId": 117, "comment": "special case hash", "msg": "32313939313533323239", "sig": "bfe7502140c57a24a77edc3d9b3c4bc11d21bdb0b196977b7f2b13ac973ad697947a01da9731849d72b67ef7bc40b012480fd389895aad1f6b1cdbeab3b93b8d", "result": "valid", "flags": []}, {"tcId": 118, "comment": "special case hash", "msg": "35363030333136383232", "sig": "3434ee1142740a0ab8623b97fc8dc2567eda45dadf6039b45c448819e840cf303c0fac0487841997202c29f3bf2df540b115b29dc619160d52203d4a1fd4b9f7", "result": "valid", "flags": []}, {"tcId": 119, "comment": "special case hash", "msg": "383639363531363935", "sig": "5338500e23ba96a0adc6ef84932e25fbad7435d9f70eb7f476c6912de12e33c8a002f5583ea8c0d7fb17136d0ee0415acf629879ce6b01ac52e3ecd7772a3704", "result": "valid", "flags": []}, {"tcId": 120, "comment": "special case hash", "msg": "36353833393236333732", "sig": "4ff2d4e31f4180de6901d2d20341d12387c9c55f4cf003a742f049b84af6fe050312f38771414555fa5ed2817dcc629a8c7cf69d306300e87bc167278ec3ef37", "result": "valid", "flags": []}, {"tcId": 121, "comment": "special case hash", "msg": "3133323035303135373235", "sig": "51d665bad5f2d6306c6bbfe1f27555887670061d4df36ec9f4ce6cdfaf9ea7ac2905e43f6207ee93df35a2e9fb9bc8098c448ae98a14e4ad1ebaea5d56b6e493", "result": "valid", "flags": []}, {"tcId": 122, "comment": "special case hash", "msg": "35303835333330373931", "sig": "b804e0235f135aba7b7531b6831f26cc9fb77d3f83854957431be20706b813699d317fd08e4e0467617db819cde1d7d4d74da489b2bce4db055ea01eccfafcf2", "result": "valid", "flags": []}, {"tcId": 123, "comment": "special case hash", "msg": "37383636383133313139", "sig": "8ab50ef3660ccb6af34c78e795ded6b256ffca5c94f249f3d907fb65235ef68049d5aaeae5a6d0c15b286e428b5e720cf37a822ede445baa143ffae69aba91b8", "result": "valid", "flags": []}, {"tcId": 124, "comment": "special case hash", "msg": "32303832353339343239", "sig": "571b9c46a47c5cc53a574c196c3fb07f3510c0f4443b9f2fe781252c24d343de68a9aebd50ff165c89b5b9cb6c1754191958f360b4d2851a481a3e1106ee7809", "result": "valid", "flags": []}, {"tcId": 125, "comment": "special case hash", "msg": "3130303635393536363937", "sig": "4cb7817b04dc73be60d3711803bc10687a6e3f4ab79c4c1a4e9d63a73174d4ebce398d2d6602d2af58a64042f830bf774aee18209d6fb5c743b6a6e437826b98", "result": "valid", "flags": []}, {"tcId": 126, "comment": "special case hash", "msg": "33303234313831363034", "sig": "684399c6cd6ebb1c5d5efb0d78dce40ebd48d9d944eb6548c9ce68d7fdc82229cf25c8e427fae359bfe60fa02964f4c9b8d6db54612e05c78c341f0a8c52d0b5", "result": "valid", "flags": []}, {"tcId": 127, "comment": "special case hash", "msg": "37373637383532383734", "sig": "20b7b36d5bc76fa182ca27152a99a956e6a0880000694296e31af98a7312d04beeeabc5521f9856e920eb7d29ed7e4042f178ff706dff8eeb24b429e3b63402a", "result": "valid", "flags": []}, {"tcId": 128, "comment": "special case hash", "msg": "353434313939393734", "sig": "6b65c95e8e121d2e6ee506cfd62cb88e0bfb3589da40876898ef66c43982aca909642c05ad619b4402fd297eb57e29cca5c2eb6823931ba82de32d7c652ba73e", "result": "valid", "flags": []}, {"tcId": 129, "comment": "special case hash", "msg": "35383433343830333931", "sig": "67c74cbf5ea4b777bf521ace099f4f094d8f58900e15e67e1b4bd399056629ed3d2884655c49b8b5f64e802a054e7bf09b0fc80ca18ebf927b82e58bb4a00400", "result": "valid", "flags": []}, {"tcId": 130, "comment": "special case hash", "msg": "373138383932363239", "sig": "79a5e40da5cf34c4c39adf7dfc5d454995a250314ebd212b5c8e3f4e6f875febb268920e403ba17828ff271938a6558a5b2dd000229f8edb4a9d9f9b6ac1b472", "result": "valid", "flags": []}, {"tcId": 131, "comment": "special case hash", "msg": "31373433323233343433", "sig": "c8b13006c3a51a322fff9321761b01de134f526be582b22e19693c443fc9fe4634e7f60179c6162ab980fcd58f173b0e6c30b524d35c67921677522dcef843a1", "result": "valid", "flags": []}, {"tcId": 132, "comment": "special case hash", "msg": "32343036303035393336", "sig": "3513db745489a487c88a6cedf8795b640f8f71578397bdabd6cc586c25bd66ad99a72cd3f0ca6c799149283ca0af37f86b88200d0c905bd3c9f1b859e55b1659", "result": "valid", "flags": []}, {"tcId": 133, "comment": "special case hash", "msg": "31363134303336393838", "sig": "3a6386afb08f7ff8140b5a270f764e8706ef2830fb177446f7b4eeb8a25aac644b70854b38c29245b2b980eba10ea936c68a38c1da5255ce2386db23afc7c06a", "result": "valid", "flags": []}, {"tcId": 134, "comment": "special case hash", "msg": "32303935343235363835", "sig": "b8fc54a8a6be3c55e99c06f99ccdcce7af5c18a3c5829726a870cc1068458f64cc7237c39c8e6a4a1c8c62f5f88636549c7410798b89684c502c3adfe5fb7ad2", "result": "valid", "flags": []}, {"tcId": 135, "comment": "special case hash", "msg": "31303038303938393833", "sig": "47b460851e5607f2021626635c565a63f78f558795e1b330d09115970dbbb8aba6a9f4f213e08d3c736d3e1c44a35140cb107619f265a5b13608ed729fd6d894", "result": "valid", "flags": []}, {"tcId": 136, "comment": "special case hash", "msg": "31353734313437393237", "sig": "8cfda4f7a65864ebbea3144863da9b075c07b5b42cb4569643ddfd70dd753b19595784b1ab217874b82b9585521f8090b9f6322884ab7a620464f51cf846c5b7", "result": "valid", "flags": []}, {"tcId": 137, "comment": "special case hash", "msg": "32383636373731353232", "sig": "4cd6a45bd7c8bf0edbdf073dbf1f746234cbbca31ec20b526b077c9f480096e77cf97ae0d33f50b73a5d7adf8aa4eeeb6ff10f89a8794efe1d874e23299c1b3d", "result": "valid", "flags": []}, {"tcId": 138, "comment": "special case hash", "msg": "31363934323830373837", "sig": "2e233f4df8ffebeaec64842b23cce161c80d303b016eca562429b227ae2b58ec46b6b56adec82f82b54daa6a5fca286740a1704828052072a5f0bc8c7b884242", "result": "valid", "flags": []}, {"tcId": 139, "comment": "special case hash", "msg": "39393231363932353638", "sig": "549f658d4a3f98233a2c93bd5b1a52d64af10815ae60becb4139cac822b579c327bdddf0dbcf374a2aec8accc47a8ac897f8d1823dda8eb2052590970b39ce2a", "result": "valid", "flags": []}, {"tcId": 140, "comment": "special case hash", "msg": "3131363039343339373938", "sig": "9fabcc1e5fd965226902f594559e231369e584453974e74f49d7d762e134fb9d293cccc510793bac45ce5da2bb6c9e906437f59435ca206655f74b625df07c7c", "result": "valid", "flags": []}, {"tcId": 141, "comment": "special case hash", "msg": "37313836313632313030", "sig": "2e5c140fd6f5f823addc8088ffaae967e7f4897274316769561dfb31435825d9eda47327d7cfae1daa344ff5582a467bd18eb9f01caeab9c6da3c0cc89df6713", "result": "valid", "flags": []}, {"tcId": 142, "comment": "special case hash", "msg": "33323934333437313737", "sig": "4c11e3b7efbe3908ad2118e54d7d34d6c6eb4570bf7fdb11a7679fe93afa254c712e90f421836e542dac49d10bb39db4a98b2735b6336d8a3c392f3b90e60bbe", "result": "valid", "flags": []}, {"tcId": 143, "comment": "special case hash", "msg": "3138353134343535313230", "sig": "dfb4619303f4ff689563d2275069fac44d63ea3c3b18f4fb1ac805d7df3d12ec68e37b846583901db256329f9cf64f40c416fba50dcb9be333a3e29c76ae32db", "result": "valid", "flags": []}, {"tcId": 144, "comment": "special case hash", "msg": "343736303433393330", "sig": "e70e8e17bd758ff0c48f91cb2c53d293f0f5ae82eb9dfe76ab98f9b06427863521dde32cb0389cad7bdf676d9b9b7d25bb034ad25a55ea71ee7ee26a18359dd2", "result": "valid", "flags": []}, {"tcId": 145, "comment": "special case hash", "msg": "32353637333738373431", "sig": "421397ecae30617a5a6081ad1badf6ce9d9d4cb2afdabf1f900e7fdb7fb0af5a57ca89dc22801c75fdbefdaeca65c675625f94de7d635062b08ed308df5762cc", "result": "valid", "flags": []}, {"tcId": 146, "comment": "special case hash", "msg": "35373339393334393935", "sig": "0610c08076909bb722fba105c23eac8f66b4db1d58f66a882fc90d59acdec8e0af59e8d570761cac589d49f11c884007f7ac1eea1a44c6f3fdad1d542187d25e", "result": "valid", "flags": []}, {"tcId": 147, "comment": "special case hash", "msg": "33343738333636313339", "sig": "59a1181cab0ee8ce94ab2b5ab4f4b13a422e38efe69f634bf947485a5b9ea49c9b3c913d98a4ab15f6a39f1802b8f2d28559aa1f8d03a3a88df00c89dc293a97", "result": "valid", "flags": []}, {"tcId": 148, "comment": "special case hash", "msg": "363439303532363032", "sig": "8cae6c4dfbf901bd66ab82541011fa15c8e90e2c18c01bd881acaa2b63cb587ba86acf943f29cef91d1b66a7de5547df6cdfc45dd7bef816dcb8de9f5a425d2d", "result": "valid", "flags": []}, {"tcId": 149, "comment": "special case hash", "msg": "34373633383837343936", "sig": "8b00c74b86474d782eac9974aea606d8f7ee78c79597e15687021f5991e86acd309dfe3686648eae104e87b3e9b5616a3ad479ca4f0b558ae4f1e5ab3115346a", "result": "valid", "flags": []}, {"tcId": 150, "comment": "special case hash", "msg": "353739303230303830", "sig": "433a915504c977809634a36fcf4480e4c8069fc127d201d30dfdb1f423c95fd4bcb1b89aafd50a1766b09741fc6a9a96e744ae9826d839bf85ffb50a91981773", "result": "valid", "flags": []}, {"tcId": 151, "comment": "special case hash", "msg": "35333434373837383438", "sig": "4b69abd2b39840a545cdd4a72d384234580e2fd938b7091d0ecdb562780857dbfdab9957119e0a4092af82f6cc29f3c8a692671ec86efb0a03c1112a0a1e0467", "result": "valid", "flags": []}, {"tcId": 152, "comment": "special case hash", "msg": "3139323636343130393230", "sig": "dab9d3686c28363ad017b4a2b36d35bf2eb80633613d44deb9501d42a3efbd381392a562d79f9ab19014e4f7e2f2668259f3720a76c120d4a3c3964e880f7679", "result": "valid", "flags": []}, {"tcId": 153, "comment": "special case hash", "msg": "33373033393135373035", "sig": "23f94e47b440ce379b74c9311232b19a64e3e7c9b90da34b0c1c3f3d7af28105e1425903b1479c2ce18b108a6d1ec8b7a4f0f657dedb00de3a3ceea7fdeee9be", "result": "valid", "flags": []}, {"tcId": 154, "comment": "special case hash", "msg": "3831353435373730", "sig": "9d706a8fa85d15bd0c3492c6672dfe529f4073b217b3947b5b2cfd61f87ccb716aaaaf369f82a0e542f72ded7d7eb90c8314ffa613a0ea81da1c8393dbae2bac", "result": "valid", "flags": []}, {"tcId": 155, "comment": "special case hash", "msg": "313935353330333737", "sig": "ac77918c4085c8a7ce5020b00c315629aee053a445cb4661eb50f6b62a47da29df2aea2b9c11a6ce39d3cd9e1faf4a53057e0b1b2e48a324be9e773203fe9fbb", "result": "valid", "flags": []}, {"tcId": 156, "comment": "special case hash", "msg": "31323637383130393033", "sig": "9db2dbd2935f147fae7f6a95c8e2307bd8537c3d96eb732ad6d5ebdd89bc754e93a9ab99d2de9d08fe0a61e26c8fe1ebbf88726e4b69d551b57d15f0ae16df5a", "result": "valid", "flags": []}, {"tcId": 157, "comment": "special case hash", "msg": "3131313830373230383135", "sig": "769f70093939afbd1fa15873decfa803ca523ace8040280ba78cf833497722bc369875aba5e1ced5a4ca8444ec9399a38038b00e153a0ae34d9b3c9781447eea", "result": "valid", "flags": []}, {"tcId": 158, "comment": "special case hash", "msg": "38333831383639323930", "sig": "26e5182b9822550ad52f46ad80781d6bef3d110a204db5e58a0746f796982200a9418e76029ced0cf78a571a9e59ad04086e91f70e6813981bb33c1dee891165", "result": "valid", "flags": []}, {"tcId": 159, "comment": "special case hash", "msg": "33313331323837323737", "sig": "e7bd6aefcf7b27e1f3fadbe713f9adb3d23398e88200cd2e94989c9d12e921779583e0de3b76f8d4b1e634a81cbc34af54e2f8599f3684ce48d372760c8204c4", "result": "valid", "flags": []}, {"tcId": 160, "comment": "special case hash", "msg": "3134333331393236353338", "sig": "8638ed7eaa83609a01a6af9c52ec9bfddda90442b1e6031d61cfa22e48b2e1e220c284d596f71c6c8df732f5a5a2006302301e1a792e2b39663d93a9760762d2", "result": "valid", "flags": []}, {"tcId": 161, "comment": "special case hash", "msg": "333434393038323336", "sig": "61d924307a96180b06383608ba91674e15c3ea06ff2534412b93a587dde649c159b84aa2115b2547edac88088ca6313e9fbe1ca6a361c7e57938f9dde3f4349c", "result": "valid", "flags": []}, {"tcId": 162, "comment": "special case hash", "msg": "36383239383335393239", "sig": "424fcfc3fd63d128c2eb125e88c7fe5d283b63470a786b82783edbb8a0b7a6d7b11548c2cd7fce9d44e795ca51af0b2f6a5180e9c9be0314007ed9e7f4bbe5e9", "result": "valid", "flags": []}, {"tcId": 163, "comment": "special case hash", "msg": "33343435313538303233", "sig": "a5f747ae6290fa9582c6ce8d5608621d495f061551bc4531bacba586a563b18462faf8f92291e12812835b3f1d43c967bceb885b110bd06e5a68e2d74781ae2b", "result": "valid", "flags": []}, {"tcId": 164, "comment": "special case hash", "msg": "3132363937393837363434", "sig": "b731dc0d92c2cc7a605d78233f7814699bdf1cab2df297b6844eec4015af8ea039b1a0cc88eb85bcdc356b3620c51f1298c60aec5306b107e900ffdba049dd6f", "result": "valid", "flags": []}, {"tcId": 165, "comment": "special case hash", "msg": "333939323432353533", "sig": "ef73c4fa322da39fb6503bab6b66b64d241056afbcd6908f84b61ccbbe890433f1ef85413e5764aa58a3128ccfcf388324fe5340e5edf8d0135ae76786ce415b", "result": "valid", "flags": []}, {"tcId": 166, "comment": "special case hash", "msg": "31363031393737393737", "sig": "694cd30e2ad0182579331474b271ee2d48723bc8415dc6513873586ce705b76bc5ac0c0ed5a4017d110cb45d63aa955dc7dc5ce23e7965c5397c3ff46a884636", "result": "valid", "flags": []}, {"tcId": 167, "comment": "special case hash", "msg": "3130383738373535313435", "sig": "f38b2236be3024e10b894ffb1cc68d0bb8d4cf0fcd2cfc1779f8883765d3cd96da69cd0b74c25566d60a486edd559fc39d569fb2751445a4798df8a36891802c", "result": "valid", "flags": []}, {"tcId": 168, "comment": "special case hash", "msg": "37303034323532393939", "sig": "a881732c205a0b4b95669c00756fd91973450109a46f17d5a9d971b5e92b9aa48acefdca4e06c16b47ccad1c57c05912637e107096ba230c92b97187db79e19e", "result": "valid", "flags": []}, {"tcId": 169, "comment": "special case hash", "msg": "31353635333235323833", "sig": "04452f554bae819b42effb84ef44a9f1cb7e2d75b4ba9ff9b9cfffaddde3fd1b61a3fbc5e73c350f2e3d85a7452cd231a3f3375fc11f5fe153b185f53b09c1d0", "result": "valid", "flags": []}, {"tcId": 170, "comment": "special case hash", "msg": "3233383236333432333530", "sig": "05814f57f58efc7cb490119e584e635e6f0ad1c19fb5dc2edafda075bb55f98e9dd5c6e39009d67d965903ecffe08a851775cc1248cc19c0b77798282131b8f6", "result": "valid", "flags": []}, {"tcId": 171, "comment": "special case hash", "msg": "31343437383437303635", "sig": "dc1c4a46085e198843b1f01980cd5e4a1ff6f8e8ff7014397f0afd5b247fb0a038a13dc723ed90b30251d742b14733a03292ff26530a1ebcaf3d10862a6eff82", "result": "valid", "flags": []}, {"tcId": 172, "comment": "special case hash", "msg": "3134323630323035353434", "sig": "1067667bf525734ca7f2510e36348fd9c2c9bccf032dfd571de6d45abd49361afa762568d3a19e5a1d8ea65e00202a5b16f9afae56733a01f86e35378c558da4", "result": "valid", "flags": []}, {"tcId": 173, "comment": "special case hash", "msg": "31393933383335323835", "sig": "e58d69dc56bc1031644847e3e046e2ea845a515d969d07ea1aa53aea5bd92fa1bfe50b80f7c512f5ab521fe7e1a131045fde78d4de826c91573baaba1e35ca97", "result": "valid", "flags": []}, {"tcId": 174, "comment": "special case hash", "msg": "34323932313533353233", "sig": "fe79c6b8c14d0f23d426e3d157f1b541f6bb91bf29957ef97c55949c9ba48a359da112c4a4cf4b1ff490c426f6c8ff122183964a0de56f7336ab382dc9d10285", "result": "valid", "flags": []}, {"tcId": 175, "comment": "special case hash", "msg": "34343539393031343936", "sig": "45d4ed7e9edacb5a730944ab0037fba0a136ed9d0d26b2f4d4058554f148fa6ff136f15fd30cfe5e5548b3f4965c16a66a7c12904686abe12da777619212ae8c", "result": "valid", "flags": []}, {"tcId": 176, "comment": "special case hash", "msg": "31333933393731313731", "sig": "4fb7c1727e40bae272f6143a50001b54b536f90233157896dbf845e263f248636fea5c924dca17519f6e502ef67efa08d39eb5cc3381266f0216864d2bd00a62", "result": "valid", "flags": []}, {"tcId": 177, "comment": "special case hash", "msg": "32333930363936343935", "sig": "779aac665dd988054b04f2e9d483ca79179b3372b58ca00fe43520f44fcb4c32b4eca1182cd51f0abd3ea2268dcda49a807ad4116a583102047498aa863653f5", "result": "valid", "flags": []}, {"tcId": 178, "comment": "special case hash", "msg": "3131343436303536323634", "sig": "db7ac6f65fb1c38d80064fd11861631237a09924b4eeca4e1569fa4b7d80ad24a38d178d37e13e1afa07a9d03da025d594461938a62a6c6744f5c8f7d7b7bb81", "result": "valid", "flags": []}, {"tcId": 179, "comment": "special case hash", "msg": "363835303034373530", "sig": "c90043b4aadf795d870ac223f33acdbd1948c31afff059054dc99528c6503fa6829f67b312bb134f6954a23c611a7f7b5b2a69efced9c48db589ac0b4d3da827", "result": "valid", "flags": []}, {"tcId": 180, "comment": "special case hash", "msg": "3232323035333630363139", "sig": "fa16c0125b6615b90e81f7499804308a90179bf3fcff6a4b2695271c68b23ded0d6cda5ce041dc5a5f319ad9c0de4927d0cf5e89e37b79216194413d42976d54", "result": "valid", "flags": []}, {"tcId": 181, "comment": "special case hash", "msg": "36323135363635313234", "sig": "1a4b5bd0f806549f46a3e71bfe412d6d89206017640ded66f3d0b2d9b26bec45aac5f74e3130264e01428570ee82ee47e245d160ed812ae252dedffd82e1ec2c", "result": "valid", "flags": []}, {"tcId": 182, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "f8e272234b51475ec4c6f327562a6e5c9080a96225e88b2e5f72a8eecbd41ab4516b91617fc39e3141b3bc769f6a3b2e468e687f50bdc29e19088af62d203f4b", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "tuCLG8yJ5_sLhNdJfjEFU0lb5Id-zMSz1teffGigVzQ", "y": "MXYPobzqSXJ1kXSsEQO8YBGYXM7iUZGNBXP7y3iWkRY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a0573431760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116", "wx": "00b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a05734", "wy": "31760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a0573431760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtuCLG8yJ5/sLhNdJfjEFU0lb5Id+\nzMSz1teffGigVzQxdg+hvOpJcnWRdKwRA7xgEZhczuJRkY0Fc/vLeJaRFg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 183, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "000000000000000000000000000000004319055358e8617b0c46353d039cdaabffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}, {"tcId": 184, "comment": "r too large", "msg": "313233343030", "sig": "ffffffff00000001000000000000000000000000fffffffffffffffffffffffcffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "NZDGoQNT1mm8lNji_54Uu-7Up_RbiHJVq343tnY4e7Y", "y": "Ffxvl845o4dMKzTMVxiJq_oKcGws-w5aR1DMJWkGlvg"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb615fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8", "wx": "3590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb6", "wy": "15fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb615fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENZDGoQNT1mm8lNji/54Uu+7Up/Rb\niHJVq343tnY4e7YV/G+Xzjmjh0wrNMxXGImr+gpwbCz7DlpHUMwlaQaW+A==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 185, "comment": "r,s are large", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254fffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Np6WQC8s_Ro3s6y97PxWKGLbypRKDxLXqqy40yXXZQo", "y": "pyNiGSK-K9rJGGKQ_c3aAo2UQ3lmUH2T8vwfXIh_3ts"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650aa723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb", "wx": "369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650a", "wy": "00a723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650aa723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENp6WQC8s/Ro3s6y97PxWKGLbypRK\nDxLXqqy40yXXZQqnI2IZIr4r2skYYpD9zdoCjZRDeWZQfZPy/B9ciH/e2w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 186, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd909135bdb6799286170f5ead2de4f6511453fe50914f3df2de54a36383df8dd4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "J6CoDqLhqnmOqbzDrtvwGreOScnsKtDgigQpoOHbTQ0", "y": "Mqjue-6dCkABTkhPNKkr1vM_5jYk6pV5ZXRBrHlmbn8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0427a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f", "wx": "27a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d", "wy": "32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000427a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJ6CoDqLhqnmOqbzDrtvwGreOScns\nKtDgigQpoOHbTQ0yqO577p0KQAFOSE80qSvW8z/mNiTqlXlldEGseWZufw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 187, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd27b4577ca009376f71303fd5dd227dcef5deb773ad5f5a84360644669ca249a5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "nP9hcS1LxbNjg0Hm4KV2qAmMnG0_GY04nEZp85jcCGc", "y": "87ngn1Z_Pf2cTSwRY-gr6t8Wx26PnXpkZzgA6nb6Hlk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59", "wx": "009cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867", "wy": "00f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEnP9hcS1LxbNjg0Hm4KV2qAmMnG0/\nGY04nEZp85jcCGfzueCfVn89/ZxNLBFj6Cvq3xbHbo+demRnOADqdvoeWQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 188, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 189, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0501", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "2RF8roEpXoJoL6OHmR5mjhVw4OkBAL9OY5ZIIkYFYbw", "y": "GflrF4ftFXaZKZeLo91_aMl631wW9nHnVs2PCMSUVso"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca", "wx": "00d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc", "wy": "19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE2RF8roEpXoJoL6OHmR5mjhVw4OkB\nAL9OY5ZIIkYFYbwZ+WsXh+0Vdpkpl4uj3X9oyXrfXBb2cedWzY8IxJRWyg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 190, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 191, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0503", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "jPy601JMIrmSUp-UPjzgstEmCFUB1uPt1PHb90vcoh4", "y": "r7JZsboXnKwJ6OQ6iMignnM5kQp8lBky5EuL5W8fzN4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21eafb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde", "wx": "008cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21e", "wy": "00afb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21eafb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEjPy601JMIrmSUp+UPjzgstEmCFUB\n1uPt1PHb90vcoh6vslmxuhecrAno5DqIyKCeczmRCnyUGTLkS4vlbx/M3g==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 192, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000005", "result": "valid", "flags": []}, {"tcId": 193, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0505", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "-7URJ-HxtqOOn-miVEYU7bjkOtfNjFbxSzI13aO8ERc", "y": "mr2XU6nmR-k0DDlfsrkThNbTP8tkViFDULbz-gD0Nkw"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc11179abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c", "wx": "00fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc1117", "wy": "009abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc11179abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE+7URJ+HxtqOOn+miVEYU7bjkOtfN\njFbxSzI13aO8EReavZdTqeZH6TQMOV+yuROE1tM/y2RWIUNQtvP6APQ2TA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 194, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000050000000000000000000000000000000000000000000000000000000000000006", "result": "valid", "flags": []}, {"tcId": 195, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0506", "result": "acceptable", "flags": ["SigSize"]}, {"tcId": 196, "comment": "r is larger than n", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325560000000000000000000000000000000000000000000000000000000000000006", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "3ICQVQDX107UfeUiTYc0VF8it3auCGyr__5s5Y1e-ZQ", "y": "3DBnzn0s36n01azilrdSgUrMacGaky2LFAd5J5Ad478"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf", "wx": "00dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994", "wy": "00dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3ICQVQDX107UfeUiTYc0VF8it3au\nCGyr//5s5Y1e+ZTcMGfOfSzfqfTVrOKWt1KBSsxpwZqTLYsUB3knkB3jvw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 197, "comment": "s is larger than n", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000005ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc75fbd8", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "G4JKEe7ZT7zZtyLQZhO7z37KALkTbyZSZCF483sakg4", "y": "6QDeSV2e9W-m0Z890eDtsj0jg1rIwtPRPAIn6FLlA-s"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920ee900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb", "wx": "1b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920e", "wy": "00e900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920ee900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEG4JKEe7ZT7zZtyLQZhO7z37KALkT\nbyZSZCF483sakg7pAN5JXZ71b6bRnz3R4O2yPSODWsjC09E8AifoUuUD6w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 198, "comment": "small r and s^-1", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000001008f1e3c7862c58b16bb76eddbb76eddbb516af4f63f2d74d76e0d28c9bb75ea88", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "KRSzDEx4Rpb_w93c7AXzbLFIi8NCufUp1Th6y55Iy40", "y": "Pb0w0NXW1qORCIY8LWpuhXHNMmH7nrmM5GElvY8TkTY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136", "wx": "2914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d", "wy": "3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKRSzDEx4Rpb/w93c7AXzbLFIi8NC\nufUp1Th6y55Iy409vTDQ1dbWo5EIhjwtam6Fcc0yYfueuYzkYSW9jxORNg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 199, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000000000000000002d9b4d347952d6ef3043e7329581dbb3974497710ab11505ee1c87ff907beebadd195a0ffe6d7a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "JXn1Rv4vKutfgi_rKPL4NxYY0EgVRVp-kDwQAkoX2kE", "y": "VSjpURR_dr7hMU5lpJxuxwaG5i04-8I0cvluPTsz_R8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da415528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f", "wx": "2579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da41", "wy": "5528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da415528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJXn1Rv4vKutfgi/rKPL4NxYY0EgV\nRVp+kDwQAkoX2kFVKOlRFH92vuExTmWknG7HBobmLTj7wjRy+W49OzP9Hw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 200, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000001033e67e37b32b445580bf4eff8b748b74000000008b748b748b748b7466e769ad4a16d3dcd87129b8e91d1b4d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "sQIZa_RV7lqvxviVUE08O2stN8Nfhmm9DwtpR5X72ZI", "y": "93e2-Cm5YorDXbDvQ_aonwpCgSYU5MFZJNjUfr5FuuU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5", "wx": "00b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992", "wy": "00f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsQIZa/RV7lqvxviVUE08O2stN8Nf\nhmm9DwtpR5X72ZL3d7b4KbliisNdsO9D9qifCkKBJhTkwVkk2NR+vkW65Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 201, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "TQVqsv9XZi_W7rviOTD-9c0ICD4kFGGQzQGWCx_NN0k", "y": "_n7FhHZRyFeJi-Dwnv1uARal2-Mn9vMICmX8lmv2TZE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91", "wx": "4d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749", "wy": "00fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETQVqsv9XZi/W7rviOTD+9c0ICD4k\nFGGQzQGWCx/NN0n+fsWEdlHIV4mL4PCe/W4BFqXb4yf28wgKZfyWa/ZNkQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 202, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "00000000000000000000000000000000000000062522bbd3ecbe7c39e93e7c25ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "NhxKYs2GdhMTjf4kzOvEt98bVfx0EPSZXuK2uasiIFg", "y": "TxFsbITlPSYv0TpfXea1fnoZgd5Ozf_fMyO06R2AZJw"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab2220584f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c", "wx": "361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab222058", "wy": "4f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab2220584f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENhxKYs2GdhMTjf4kzOvEt98bVfx0\nEPSZXuK2uasiIFhPEWxshOU9Ji/ROl9d5rV+ehmB3k7N/98zI7TpHYBknA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 203, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324d5555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "251cURPwCCKhRsnNoudctmNM0N_1Sv9uIodRcfV6Da0", "y": "HEJM3YPrAcAvb4029Cxtx-Odt0NY2orJvJ3FiQ1G9mc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667", "wx": "00db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad", "wy": "1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE251cURPwCCKhRsnNoudctmNM0N/1\nSv9uIodRcfV6Da0cQkzdg+sBwC9vjTb0LG3H4523Q1jaism8ncWJDUb2Zw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 204, "comment": "s == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c700000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 205, "comment": "s == 0", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c700000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "mfGfB7M-A8r0cD4EuTDVfW2bqkRGDFlqLTBk4LY-pBI", "y": "hqdMRhKoEu40jStD-A3mJ8EcddgVEeIqGZwyEZt5LGo"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0499f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea41286a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a", "wx": "0099f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea412", "wy": "0086a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000499f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea41286a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEmfGfB7M+A8r0cD4EuTDVfW2bqkRG\nDFlqLTBk4LY+pBKGp0xGEqgS7jSNK0P4DeYnwRx12BUR4ioZnDIRm3ksag==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 206, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "MT8zCbI2SExutOo4HgB4VEZ6YXNDoul9hFgBwBpjLP4", "y": "M_IxhUu6iajKP4AqJ2TTv2wyM8gRox5egCiguGLLGXc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977", "wx": "313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe", "wy": "33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEMT8zCbI2SExutOo4HgB4VEZ6YXND\noul9hFgBwBpjLP4z8jGFS7qJqMo/gConZNO/bDIzyBGjHl6AKKC4YssZdw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 207, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a97fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "06oB_lm62Sz_49tZ4ThTkfr9evTkzkYuiqwVcnTMigU", "y": "x6fmA-GFOKrBX4lhC-rMIeOYmObF92gKgcW9e9dEqYk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989", "wx": "00d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05", "wy": "00c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE06oB/lm62Sz/49tZ4ThTkfr9evTk\nzkYuiqwVcnTMigXHp+YD4YU4qsFfiWEL6swh45iY5sX3aAqBxb1710SpiQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 208, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a97fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "XjHszUcE6_ekJH6lf5NRq63_Y2efInbio7BQCevBuN8", "y": "ZIRlqSUBDbgjsqXzpgcjQ6bMmWGpxII5nQ2CBRwuMjI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232", "wx": "5e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df", "wy": "648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEXjHszUcE6/ekJH6lf5NRq63/Y2ef\nInbio7BQCevBuN9khGWpJQENuCOypfOmByNDpsyZYanEgjmdDYIFHC4yMg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 209, "comment": "u1 == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c7043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "zgpH-IH9cxWnM8QxeEj6M8cuON4Lj9o2thqpoWT1gIo", "y": "hbBdJRFepAl932P4eMjoNlfmbeE2qPnmLtgaWL8Rf_k"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a85b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9", "wx": "00ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a", "wy": "0085b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a85b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzgpH+IH9cxWnM8QxeEj6M8cuON4L\nj9o2thqpoWT1gIqFsF0lEV6kCX3fY/h4yOg2V+Zt4Tao+eYu2BpYvxF/+Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 210, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "zW9Ie0fzbA3qj0sExOasY3x2tyWSnGEfSK3c89L2WUE", "y": "tQ6o86SRGQ7gsgz7bv0RNgjnx8EnV3UA5_XEpOSQ_WA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60", "wx": "00cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941", "wy": "00b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzW9Ie0fzbA3qj0sExOasY3x2tyWS\nnGEfSK3c89L2WUG1DqjzpJEZDuCyDPtu/RE2COfHwSdXdQDn9cSk5JD9YA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 211, "comment": "u2 == 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "RW5fgGfWihsKLo_isorK1XVWhxVKDxZ3NOurvcBZBw0", "y": "cg2-lmWaZu8M8npz57Pz8UWmDgrSnx4h3MK7QvDYLB4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e", "wx": "456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d", "wy": "720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAERW5fgGfWihsKLo/isorK1XVWhxVK\nDxZ3NOurvcBZBw1yDb6WZZpm7wzyenPns/PxRaYOCtKfHiHcwrtC8NgsHg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 212, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c70aaaaaaaa00000000aaaaaaaaaaaaaaaa7def51c91a0fbf034d26872ca84218e1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Qr8MCsHjhQuvVRV0ioeONCSfcQNeIKn1TtRo7Cc8sPw", "y": "WzE4UAIwBVxx8S1T9cfQ49iqVKlMZoyzEeINGV_HGrs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0442bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb", "wx": "42bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc", "wy": "5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000442bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEQr8MCsHjhQuvVRV0ioeONCSfcQNe\nIKn1TtRo7Cc8sPxbMThQAjAFXHHxLVP1x9Dj2KpUqUxmjLMR4g0ZX8cauw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 213, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd6bfd55a8f8fdb68472e52873ef39ac3eace6d53df576f0ad2da4607bb52c0d46", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "_91I2mPTr2ciPxbFHrfpVgDrCw6Llk9PzYxTT6zjwsI", "y": "tOAJqyp2gpSA5pyeQ7Lx_gds-vs_qNJ91Na6tNbD21Q"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54", "wx": "00ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2", "wy": "00b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE/91I2mPTr2ciPxbFHrfpVgDrCw6L\nlk9PzYxTT6zjwsK04AmrKnaClIDmnJ5DsvH+B2z6+z+o0n3U1rq01sPbVA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 214, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd654937791db0686f712ff9b453eeadb0026c9b058bba49199ca3e8fac03c094f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "eTy_zm8zXc_t58aJjqHFN9dmHtaoydMI1kolYNIcbiw", "y": "SD0jpf8F2gDq-dUs9TYr6bU7lTFsajLp6-aNmsNcL9Y"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6", "wx": "793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c", "wy": "483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEeTy/zm8zXc/t58aJjqHFN9dmHtao\nydMI1kolYNIcbixIPSOl/wXaAOr51Sz1NivptTuVMWxqMunr5o2aw1wv1g==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 215, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdc51bbee23a95437abe5c978f8fe596a31c858ac8d55be9786aa5d36a5ac74e97", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "qfcCP1WdS7bJ9Lw2Q-KCSv9UUdkpR57D6l6zC60sNqw", "y": "anx36N0h9K1JsQPmfanTzaYrZT3RlPrSuo0d03uw6ps"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b", "wx": "00a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac", "wy": "6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEqfcCP1WdS7bJ9Lw2Q+KCSv9UUdkp\nR57D6l6zC60sNqxqfHfo3SH0rUmxA+Z9qdPNpitlPdGU+tK6jR3Te7Dqmw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 216, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd8ba4c3da7154ba564ab344ae12005aa482b6c1639ea191f8568afb6e47163c45", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "33nuCCsvx36c5GM0cfVpu8tc5ThW4wZ3dPN-imSix_8", "y": "qkiKbDTUmd929CfeNgm_z9n-rmf_4LDeWURjxFOwqxY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ffaa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16", "wx": "00df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ff", "wy": "00aa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ffaa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE33nuCCsvx36c5GM0cfVpu8tc5ThW\n4wZ3dPN+imSix/+qSIpsNNSZ33b0J942Cb/P2f6uZ//gsN5ZRGPEU7CrFg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 217, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd4c3dafcf4ba55bf1344ae12005aa4a74f46eaa85f5023131cc637ae2ea90ab26", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "TMO_ZeMuAChK38oA9A33VUFcSFCRrASJrpozcQOl-PA", "y": "Ejq4bdQzuTO08gY8ACFE3zz-unja0O2JwDd1QVMpCMI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2", "wx": "4cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0", "wy": "123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETMO/ZeMuAChK38oA9A33VUFcSFCR\nrASJrpozcQOl+PASOrht1DO5M7TyBjwAIUTfPP66eNrQ7YnAN3VBUykIwg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 218, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd987b5f9e974ab7e26895c2400b5494e9e8dd550bea04626398c6f5c5d521564c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Jkp61DmkgoqdyX7Pg3FVNV-Zrgtll1-FG1Qa06DgMvA", "y": "ZyaLcpjHPlgYZvvL0WFomxa4HPJi4AfOaOJaKMg-8EE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f067268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041", "wx": "264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f0", "wy": "67268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f067268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJkp61DmkgoqdyX7Pg3FVNV+Zrgtl\nl1+FG1Qa06DgMvBnJotymMc+WBhm+8vRYWibFrgc8mLgB85o4looyD7wQQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 219, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdfcf97e2fbf0e80d412005aa4a75086a3f004f59d512cb47271798733ab418606", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "HX_006QSBsgUNjXxKHbg6gh16l5KWiSSUNDtoz2qIR8", "y": "VuicC-r5EKyTTKEjgEVWAND9hbVqcDXLFxs_HHKhVWk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569", "wx": "1d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f", "wy": "56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHX/006QSBsgUNjXxKHbg6gh16l5K\nWiSSUNDtoz2qIR9W6JwL6vkQrJNMoSOARVYA0P2FtWpwNcsXGz8ccqFVaQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 220, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd79d482b60864d6c5cb4fd5db9e7e28ccd9a5948c316c8740fb429c0f37169a02", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "sJaF8zjc60IXeKFFjVK-1zTCNiQtorqigNb2t7huTxE", "y": "f-ajQUa0IteuvRpRsglI14cqUUxM_XaG3ENrcHM9ZHM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f117fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473", "wx": "00b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f11", "wy": "7fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f117fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsJaF8zjc60IXeKFFjVK+1zTCNiQt\norqigNb2t7huTxF/5qNBRrQi1669GlGyCUjXhypRTEz9dobcQ2twcz1kcw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 221, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd8ecd11081a4d0759c14f7bf46813d52cc6738115321be0a4da78a3356bb71510", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "3YEfLA9enU-7LvMYGMHNgHJHvBT80RcL7wDixx3AN7Q", "y": "Q6Fc348_vch-BiUMByDSYdK40If6e_lUj2KT8M5a6Jk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b443a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899", "wx": "00dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b4", "wy": "43a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b443a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3YEfLA9enU+7LvMYGMHNgHJHvBT8\n0RcL7wDixx3AN7RDoVzfjz+9yH4GJQwHINJh0rjQh/p7+VSPYpPwzlromQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 222, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffde8dbffed13c9a2093085c079714f11f24eb583d73ba2b416b3169183e7d9b4c2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "adYK4fOeHalYCdQIiUcHrSE09JQ6HbCJvr-BWjkfGNs", "y": "MrQB2Yv4lNO21Z5utFVzKFZC41itaHt9e_lgCxmHgJ4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e", "wx": "69d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db", "wy": "32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000469d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEadYK4fOeHalYCdQIiUcHrSE09JQ6\nHbCJvr+BWjkfGNsytAHZi/iU07bVnm60VXMoVkLjWK1oe317+WALGYeAng==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 223, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdca01552a838124bec68d6bc6086329e06673900eac5c262e5ce79a8521cd1eae", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "plhVOgYgyV6Ye1wxY7z-poxSBl9TydVT8qkk2LPtUR8", "y": "efDf7EU2tlql-zEpfpb2tGSqZpuSaLMVbEPUYSl4pXc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577", "wx": "00a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f", "wy": "79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEplhVOgYgyV6Ye1wxY7z+poxSBl9T\nydVT8qkk2LPtUR958N/sRTa2WqX7MSl+lva0ZKpmm5JosxVsQ9RhKXildw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 224, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd9402aa560702497c8d1ad78c10c653c11000256fb1a0add7c6156a474737180b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "vE0zVKapc92AiJGcwYEZToee15INsw0NEnjt90QTt7k", "y": "JFDRYrJtyyX7vVPqQEQYmYHXNwVZJb0uhr-wN0sJ88o"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b92450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca", "wx": "00bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b9", "wy": "2450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b92450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvE0zVKapc92AiJGcwYEZToee15IN\nsw0NEnjt90QTt7kkUNFism3LJfu9U+pARBiZgdc3BVklvS6Gv7A3Swnzyg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 225, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd5e03ff818a836e3a53a8435219297da1b98cbad0b6e535812f433a096ca11168", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "DrYock_OdkxofYdK3nuOCqSr8g7m42EPrJ_j5y-Xq1o", "y": "7Qn0hDZg6x2vAV05enwQc9euQ72guj4RcAh4Wr__oA8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5aed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f", "wx": "0eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5a", "wy": "00ed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5aed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEDrYock/OdkxofYdK3nuOCqSr8g7m\n42EPrJ/j5y+Xq1rtCfSENmDrHa8BXTl6fBBz165DvaC6PhFwCHhav/+gDw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 226, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffde28ddf709d4aa1bddf2e4bc7c7f2cb516cb642bb3e39c3feaf2fcf16ab9539f4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "56xcx_KWkS9wP1n-iOSbUh2iReEubu4WHuazsRJ2Eac", "y": "ezvt0qdzz1iwYpuTbdhdrS0MOWdjBu1j4am80OCLzMI"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a77b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2", "wx": "00e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a7", "wy": "7b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a77b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE56xcx/KWkS9wP1n+iOSbUh2iReEu\nbu4WHuazsRJ2Ead7O+3Sp3PPWLBim5Nt2F2tLQw5Z2MG7WPhqbzQ4IvMwg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 227, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffffaaaaaaaaffffffffffffffffe9a2538f37b28a2c513dee40fecbb71a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "JAe2Cr8-5e2vku1QWhHQ3c4Ooz7KWKAxuy8WLFEvQGI", "y": "-4G_82v5Z-g049XUaHMNzXBEACKrYAYaYvrFM1D-JZ8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f", "wx": "2407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062", "wy": "00fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJAe2Cr8+5e2vku1QWhHQ3c4Ooz7K\nWKAxuy8WLFEvQGL7gb/za/ln6DTj1dRocw3NcEQAIqtgBhpi+sUzUP4lnw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 228, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb62f26b5f2a2b26f6de86d42ad8a13da3ab3cccd0459b201de009e526adf21f2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "R7Ktlt_C8j_lkmgJ84BCssgBlivXOUzvv0qsslVLews", "y": "3yuTehan2WoqBoLNFkQoiQIIWX8s3Mc0_ac2ALXPbFk"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0447b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0bdf2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59", "wx": "47b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0b", "wy": "00df2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000447b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0bdf2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAER7Ktlt/C8j/lkmgJ84BCssgBlivX\nOUzvv0qsslVLewvfK5N6FqfZaioGgs0WRCiJAghZfyzcxzT9pzYAtc9sWQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 229, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbb1d9ac949dd748cd02bbbe749bd351cd57b38bb61403d700686aa7b4c90851e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "aaZbdfMa57STApL5CQJGG-_O5dFgaTnCjgG2Uqf7xJg", "y": "z2hhnlhgEo9Wzs9T66L_6CiJqbsEpfpMi3IryR1Vl4o"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a", "wx": "69a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498", "wy": "00cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000469a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaaZbdfMa57STApL5CQJGG+/O5dFg\naTnCjgG2Uqf7xJjPaGGeWGASj1bOz1Prov/oKImpuwSl+kyLcivJHVWXig==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 230, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd66755a00638cdaec1c732513ca0234ece52545dac11f816e818f725b4f60aaf2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "sgNxdshNsEpsdz4y-e0dayXvTDA8ZyXGky7CzCeIvLs", "y": "k2FQXmt3FpGttBWY8pLWUhciQEvxgyQbGVc4t3q9bP4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb9361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe", "wx": "00b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb", "wy": "009361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb9361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsgNxdshNsEpsdz4y+e0dayXvTDA8\nZyXGky7CzCeIvLuTYVBea3cWka20FZjyktZSFyJAS/GDJBsZVzi3er1s/g==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 231, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd55a00c9fcdaebb6032513ca0234ecfffe98ebe492fdf02e48ca48e982beb3669", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Hu-VrvcfeTr9ULsmBAZNY-iL73QEpNDiBkRiRa4ueDQ", "y": "yW6G3QQPl5S2NxLZDnGVdri5LEBqsPKIrZsye9EkRU8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f", "wx": "1eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834", "wy": "00c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHu+VrvcfeTr9ULsmBAZNY+iL73QE\npNDiBkRiRa4ueDTJbobdBA+XlLY3EtkOcZV2uLksQGqw8oitmzJ70SRFTw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 232, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdab40193f9b5d76c064a27940469d9fffd31d7c925fbe05c919491d3057d66cd2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "qXNImclU5betvKj3g0KLX7y9_T0oE_jS-Vsxp4qxB1Y", "y": "dmer-MAs5JUbxZslZBMMJ9e2TNvFytlcpC1bu3zU55M"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab107567667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793", "wx": "00a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab10756", "wy": "7667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab107567667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEqXNImclU5betvKj3g0KLX7y9/T0o\nE/jS+Vsxp4qxB1Z2Z6v4wCzklRvFmyVkEwwn17ZM28XK2VykLVu7fNTnkw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 233, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdca0234ebb5fdcb13ca0234ecffffffffcb0dadbbc7f549f8a26b4408d0dc8600", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "GuUWYjMaHb-rB1HTDfqyJzoEojngVaU3sWq1lflhI5Y", "y": "Q08hwr_mVVyfxKjoLasfpWMYgbAW4IMdnhu_V5n88y4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e", "wx": "1ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396", "wy": "434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEGuUWYjMaHb+rB1HTDfqyJzoEojng\nVaU3sWq1lflhI5ZDTyHCv+ZVXJ/EqOgtqx+lYxiBsBbggx2eG79XmfzzLg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 234, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff3ea3677e082b9310572620ae19933a9e65b285598711c77298815ad3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "U8kM3YsNrdIcRK1VezJ_Tb9XFEqvBll96z-UElIGpsE", "y": "RgNHW9ebMONjQM0JsLWebNRs6QFQ6f_lyKAXKyyYmOM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0453c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c14603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3", "wx": "53c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c1", "wy": "4603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000453c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c14603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU8kM3YsNrdIcRK1VezJ/Tb9XFEqv\nBll96z+UElIGpsFGA0db15sw42NAzQmwtZ5s1GzpAVDp/+XIoBcrLJiY4w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 235, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd266666663bbbbbbbe6666666666666665b37902e023fab7c8f055d86e5cc41f4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "M3l1OVFcUfQplnuONpMNn92h7bE67OyXcffN5fby504", "y": "ulHQtkVruQLbofPqQ2-WrSNV2kVNybMsUDxLxs_W1BA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0433797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74eba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410", "wx": "33797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74e", "wy": "00ba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000433797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74eba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEM3l1OVFcUfQplnuONpMNn92h7bE6\n7OyXcffN5fby5066UdC2RWu5Atuh8+pDb5atI1XaRU3JsyxQPEvGz9bUEA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 236, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff36db6db7a492492492492492146c573f4c6dfc8d08a443e258970b09", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Co9fHVu9J4P6fzfIaHkFf7L88lODqvuG0D1rr7QaF7M", "y": "6vbacV_pUDSf1XNhF7COFeMs8dL9wAPlEACfG0uh5kg"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648", "wx": "0a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3", "wy": "00eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECo9fHVu9J4P6fzfIaHkFf7L88lOD\nqvuG0D1rr7QaF7Pq9tpxX+lQNJ/Vc2EXsI4V4yzx0v3AA+UQAJ8bS6HmSA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 237, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfffffff2aaaaaab7fffffffffffffffc815d0e60b3e596ecb1ad3a27cfd49c4", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "HbyU6WwFa50stnc7sktp7Uc4UbrfknoplVr_KQ7zZ1o", "y": "ZeWHVhEiqoIm-suV3wgwjK3wHINRoVaRdtkXghETqnw"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c", "wx": "1dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a", "wy": "65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHbyU6WwFa50stnc7sktp7Uc4Ubrf\nknoplVr/KQ7zZ1pl5YdWESKqgib6y5XfCDCMrfAcg1GhVpF22ReCEROqfA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 238, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffff55555555ffffffffffffffffd344a71e6f651458a27bdc81fd976e37", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "CEq4hdv_fxLmza21nUVuUAeXd5Qlx1GMJZyDcYKJ5uk", "y": "kcNF06CT6GZwYFu8L_TGnQ7WlP1DPsa2uhv31Ww-a1E"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e991c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51", "wx": "084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e9", "wy": "0091c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e991c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECEq4hdv/fxLmza21nUVuUAeXd5Ql\nx1GMJZyDcYKJ5umRw0XToJPoZnBgW7wv9MadDtaU/UM+xra6G/fVbD5rUQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 239, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd3fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192aa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "ADrfpMYgogcJbNGO6P0qkOIBBs-CSgxj1t7HJ6n-f1A", "y": "lDDSa91fcegZ0StwBpkBRhrgg8yAkSLU-4a1xHUkTlo"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04003adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f509430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a", "wx": "3adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f50", "wy": "009430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004003adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f509430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEADrfpMYgogcJbNGO6P0qkOIBBs+C\nSgxj1t7HJ6n+f1CUMNJr3V9x6BnRK3AGmQFGGuCDzICRItT7hrXEdSROWg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 240, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd5d8ecd64a4eeba466815ddf3a4de9a8e6abd9c5db0a01eb80343553da648428f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "fJiy1H60M8DRjlM8-8iQnWb3t51ZJcyxfszsnRBcWIQ", "y": "jVypmzUL19EKte5vz-RmI_3APp-CgVj01MwIrR_4PeQ"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c58848d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4", "wx": "7c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c5884", "wy": "008d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c58848d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEfJiy1H60M8DRjlM8+8iQnWb3t51Z\nJcyxfszsnRBcWISNXKmbNQvX0Qq17m/P5GYj/cA+n4KBWPTUzAitH/g95A==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 241, "comment": "point duplication during verification", "msg": "313233343030", "sig": "6f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569b4cfa1996ec1d24cdbc8fa17fcabc3a5d4b2b36cf4b50a7b775ab78785710746", "result": "valid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "fJiy1H60M8DRjlM8-8iQnWb3t51ZJcyxfszsnRBcWIQ", "y": "cqNWY8r0KC_1ShGQMBuZ3AI_wWF9fqcLKzP3UuAHwhs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c588472a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b", "wx": "7c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c5884", "wy": "72a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c588472a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEfJiy1H60M8DRjlM8+8iQnWb3t51Z\nJcyxfszsnRBcWIRyo1ZjyvQoL/VKEZAwG5ncAj/BYX1+pwsrM/dS4AfCGw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 242, "comment": "duplication bug", "msg": "313233343030", "sig": "6f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569b4cfa1996ec1d24cdbc8fa17fcabc3a5d4b2b36cf4b50a7b775ab78785710746", "result": "invalid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "t6kOIedUfXMmeUADPOoFBCxQ98n6Xq60cc1iYMaF8uM", "y": "i7cwnQw7qySfqvPkQXnW3VMCN1xYD9BXCniMa-NoDGc"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e38bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67", "wx": "00b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e3", "wy": "008bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e38bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEt6kOIedUfXMmeUADPOoFBCxQ98n6\nXq60cc1iYMaF8uOLtzCdDDurJJ+q8+RBedbdUwI3XFgP0FcKeIxr42gMZw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 243, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "FVChczc7LVlDdPBkLNc94GoEXAnHpPOIxzHozYlxrfw", "y": "mjqYQ1g6hsDhxiy95nFl9AqSaxAoujiqOJXhiOu8cGY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc9a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066", "wx": "1550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc", "wy": "009a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc9a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEFVChczc7LVlDdPBkLNc94GoEXAnH\npPOIxzHozYlxrfyaOphDWDqGwOHGLL3mcWX0CpJrECi6OKo4leGI67xwZg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 244, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "010000000000000000000000000000000000000000000000000000000000000000003333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "MTRHd4GV2qF5GmUwzQaXrjS_nY0iWYQ5T3Lu81BZcRE", "y": "CZao-90acOzWTLALWVr-Fmm_74DZF1amLYTB2D4PIqs"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef3505971110996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab", "wx": "313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef350597111", "wy": "0996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef3505971110996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEMTRHd4GV2qF5GmUwzQaXrjS/nY0i\nWYQ5T3Lu81BZcREJlqj73Rpw7NZMsAtZWv4Wab/vgNkXVqYthMHYPg8iqw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 245, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "555555550000000055555555555555553ef7a8e48d07df81a693439654210c703333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "StpjSUFHbKY8LFgD7sLzOy0Xkg95ilvmJ19aVM0udjk", "y": "saBL6tXHMUxCdJLbIblUTYHKqBWVh-QaoCOqln8xqqE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1", "wx": "4ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639", "wy": "00b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEStpjSUFHbKY8LFgD7sLzOy0Xkg95\nilvmJ19aVM0udjmxoEvq1ccxTEJ0ktshuVRNgcqoFZWH5BqgI6qWfzGqoQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 246, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "qszgkycPpZrUErVFmgjkkHQ7lwhseBrDyNVAMLQaMRk", "y": "O-zklWFy1WvvtwEdaE53KQXkjSEVREp1rHoyWj8l9LE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a31193bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1", "wx": "00aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a3119", "wy": "3bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a31193bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEqszgkycPpZrUErVFmgjkkHQ7lwhs\neBrDyNVAMLQaMRk77OSVYXLVa++3AR1oTncpBeSNIRVESnWsejJaPyX0sQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 247, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "9iuNf-7_WoR6t5ISJp5V5i-ofr6TCCF0e1elEaXqmfA", "y": "Q57gV7sniYWCpoPD_bf5VATUHULydoA3UaMW6zqrfr8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf", "wx": "00f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0", "wy": "439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9iuNf+7/WoR6t5ISJp5V5i+ofr6T\nCCF0e1elEaXqmfBDnuBXuyeJhYKmg8P9t/lUBNQdQvJ2gDdRoxbrOqt+vw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 248, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "S6oH_257uaoiPRxhkyAF_pj-eLeH_atL02GbyIMwcqI", "y": "vKzWOALFavgmB5U-cqD108I70mVUTgIJUYJOpIVVXTM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33", "wx": "4baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2", "wy": "00bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES6oH/257uaoiPRxhkyAF/pj+eLeH\n/atL02GbyIMwcqK8rNY4AsVq+CYHlT5yoPXTwjvSZVROAglRgk6khVVdMw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 249, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc476699783333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "DHU-0bqS92aAD90K4cDX-PTNgwX9gD2LyogTl7WTfi0", "y": "tWhQmx-vPPJR3m25gQ6LjK7SNdoQ7t2-1id1yOXJRgo"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2db568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a", "wx": "0c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2d", "wy": "00b568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2db568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEDHU+0bqS92aAD90K4cDX+PTNgwX9\ngD2LyogTl7WTfi21aFCbH6888lHebbmBDouMrtI12hDu3b7WJ3XI5clGCg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 250, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997849249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "Aw_crmVB8ixbqyVOTxooXFB9HO_qA7-Qzxna88ti32k", "y": "X_LJTViPLCsrChK-vAEbzuT6G1RQbsB9CinSSgiRGTw"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df695ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c", "wx": "030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df69", "wy": "5ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df695ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAw/crmVB8ixbqyVOTxooXFB9HO/q\nA7+Qzxna88ti32lf8slNWI8sKysKEr68ARvO5PobVFBuwH0KKdJKCJEZPA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 251, "comment": "extreme value for k", "msg": "313233343030", "sig": "7cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997816a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "A_xiHq-Qwj2Pn6El0sWbhyjrzLMMo-Pbh5oGypDyDNw", "y": "rljT8Mau8OgFvhDqVOI89vA5f5rd3cKwkIiFUxaw70Q"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0403fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdcae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44", "wx": "03fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdc", "wy": "00ae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000403fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdcae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44", "keyPem": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEA/xiHq+Qwj2Pn6El0sWbhyjrzLMM\no+Pbh5oGypDyDNyuWNPwxq7w6AW+EOpU4jz28Dl/mt3dwrCQiIVTFrDvRA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 252, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "cPLOJNxikjuwnMktdDKbvQ0uaw41TAviOD0krNzLnkw", "y": "1C0flzRm9eVGKpOQhKKU6_x6RWKccO5d70belTbqe_c"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0470f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4cd42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7", "wx": "70f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4c", "wy": "00d42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000470f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4cd42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcPLOJNxikjuwnMktdDKbvQ0uaw41\nTAviOD0krNzLnkzULR+XNGb15UYqk5CEopTr/HpFYpxw7l3vRt6VNup79w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 253, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "cyuKwMMP5EMHQxI1Jxy11uX2d6Gc4_BYuTmnvxk0nTw", "y": "hYzHNa-Fd0aCdYR89ewZly5sIHOCduJwiyPFlb_EQz0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d", "wx": "732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c", "wy": "00858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcyuKwMMP5EMHQxI1Jxy11uX2d6Gc\n4/BYuTmnvxk0nTyFjMc1r4V3RoJ1hHz17BmXLmwgc4J24nCLI8WVv8RDPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 254, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "R6_5UBgloWZ4K7WKW0WQBurNvOXlMjrdrTTsG2REzc4", "y": "kZnDFQKtQnfHPd0MgHtyY0xFdiQEg32YFKXUtafD85g"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0447aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce9199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398", "wx": "47aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce", "wy": "009199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000447aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce9199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAER6/5UBgloWZ4K7WKW0WQBurNvOXl\nMjrdrTTsG2REzc6RmcMVAq1Cd8c93QyAe3JjTEV2JASDfZgUpdS1p8PzmA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 255, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2963333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "rtju_3dkS_g7kiL49XFz-oIX7H4HY-59cXH7YJL7pcA", "y": "ZIaobZT0iDS6WtuvNJaH-c7kADiWQrgo5oIHsUfKLEY"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c06486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46", "wx": "00aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c0", "wy": "6486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c06486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAErtju/3dkS/g7kiL49XFz+oIX7H4H\nY+59cXH7YJL7pcBkhqhtlPSINLpa2680lof5zuQAOJZCuCjmggexR8osRg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 256, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29649249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "98VKWFqQQwDQW1PvO4VOcZmaNEuJrcDKqijiVNubx8c", "y": "wWGnnzj_RGBRMDV35AY4-wIDKZQKY8JBuzLCIF61e30"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d", "wx": "00f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7", "wy": "00c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE98VKWFqQQwDQW1PvO4VOcZmaNEuJ\nrcDKqijiVNubx8fBYaefOP9EYFEwNXfkBjj7AgMplApjwkG7MsIgXrV7fQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 257, "comment": "extreme value for k", "msg": "313233343030", "sig": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29616a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "axfR8uEsQkf4vOblY6RA8ncDfYEt6zOg9KE5RdiYwpY", "y": "T-NC4v4af5uO5-tKfA-eFivOM1drMV7Oy7ZAaDe_UfU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpZP40Li/hp/m47n60p8D54WK84zV2sxXs7LtkBoN79R9Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 258, "comment": "testing point duplication", "msg": "313233343030", "sig": "43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 259, "comment": "testing point duplication", "msg": "313233343030", "sig": "bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d0249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "axfR8uEsQkf4vOblY6RA8ncDfYEt6zOg9KE5RdiYwpY", "y": "sBy9HAHlgGVxGBS1g_Bh6dQxzKmUzqExNEm_l8hArgo"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "00b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpawHL0cAeWAZXEYFLWD8GHp1DHMqZTOoTE0Sb+XyECuCg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 260, "comment": "testing point duplication", "msg": "313233343030", "sig": "43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 261, "comment": "testing point duplication", "msg": "313233343030", "sig": "bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d0249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "BKrsc2NXJvIT-4qeZNo7hjLkFJWpRNAEW1IuunJA-tU", "y": "h9kxV5iqo6W6AXdXh87QXqr3tOCfyB1tGqVG6DZdUl0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "wx": "04aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad5", "wy": "0087d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBKrsc2NXJvIT+4qeZNo7hjLkFJWp\nRNAEW1IuunJA+tWH2TFXmKqjpboBd1eHztBeqve04J/IHW0apUboNl1SXQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 262, "comment": "pseudorandom signature", "msg": "", "sig": "093f3825c0cf820cced816a3a67446c85606a6d529e43857643fccc11e1f705f769782888c63058630f97a5891c8700e82979e4f233586bfc5042fa73cb70a4e", "result": "valid", "flags": []}, {"tcId": 263, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "e8564e3e515a09f9f35258442b99e162d27e10975fcb7963d3c26319dc093f84c3af01ed0fd0148749ca323364846c862fc6f4beb682b7ead3b2d89b9da8bad4", "result": "valid", "flags": []}, {"tcId": 264, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "1412254f8c1dd2742a00ddee5192e7baa288741026871f3057ad9f983b5ab114bcdf878fa156f37040922698ad6fb6928601ddc26c40448ea660e67c25eda090", "result": "valid", "flags": []}, {"tcId": 265, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "9e0676048381839bb0a4703a0ae38facfe1e2c61bd25950c896aa975cd6ec8696ea0cedf96f11fff0e746941183492f4d17272c92449afd20e34041a6894ee82", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "TzN8z9Z3JqgF5PFgCuKEnfOAfsoRc4Ajn72BaQAAAAA", "y": "7Z3qEkzIw5ZBZBHpiMMPQn61BK9DoxRs1d9-pgZm1oU"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "wx": "4f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000", "wy": "00ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETzN8z9Z3JqgF5PFgCuKEnfOAfsoR\nc4Ajn72BaQAAAADtneoSTMjDlkFkEemIww9CfrUEr0OjFGzV336mBmbWhQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 266, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "554482404173a5582884b0d168a32ef8033d7eb780936c390e8eedf720c7f5640a15413f9ed0d454b92ab901119e7251a4d444ba1421ba639fa57e0d8cf6b313", "result": "valid", "flags": []}, {"tcId": 267, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "0b1d838dd54a462745e2c8d5f32637f26fb16dde20a385e45f8a20a8a1f8370eae855e0a10ef087075fda0ed84e2bc5786a681172ea9834e53351316df332bbd", "result": "valid", "flags": []}, {"tcId": 268, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "af89e4f2b03e5d1f0352e258ef71493040c17d70c36cfd044128302df2ed5e4a420f04148c3e6f06561bd448362d6c6fa3f9aeeb7e42843b4674e7ddfd0ba901", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "PPA9YU2JOc_UmaB4c_rCgWGPBrj_h-gBXD9JcmUASTU", "y": "hPoXTXkccr8s44gKiWDdKnx6EzioL4Wp5Zzb3oAAAAA"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "0084fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTWE+hdNeRxyvyzjiAqJYN0qfHoTOKgvhanlnNvegAAAAA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 269, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "6c1581f1485ccc4e657606fa1a38cf227e3870dc9f41e26b84e28483635e321b1b3e3c22af23e919b30330f8710f6ef3760c0e2237a9a9f5cf30a1d9f5bbd464", "result": "valid", "flags": []}, {"tcId": 270, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "dc83bf97ca28db0e04104a16fe3de694311a6cd9f230a300504ae71d8ec755b164a83af0ab3e6037003a1f4240dffd8a342afdee50604ed1afa416fd009e4668", "result": "valid", "flags": []}, {"tcId": 271, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "575b70b4375684291b95d81e3c820ed9bde9e5b7343036e4951f3c46894a6d9df10d716efbfeba953701b603fc9ef6ff6e47edef38c9eeef2d55e6486bc4d6e6", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "PPA9YU2JOc_UmaB4c_rCgWGPBrj_h-gBXD9JcmUASTU", "y": "ewXosYbjjUHTHHf1dp8i1YOF7MhX0HpWGmMkIX____8"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "7b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTV7BeixhuONQdMcd/V2nyLVg4XsyFfQelYaYyQhf////w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 272, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "8d4f113189dfd3d3239e331f76d3fca9cef86fcd5dc9b4ab2ca38aeba56c178b78389c3cf11dcff6d6c7f5efd277d480060691144b568a6f090c8902557bfc61", "result": "valid", "flags": []}, {"tcId": 273, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "834d10ec2d2d50eeebfecd6328f03fafbb488fc043c362cbc67880ec0ebd04b394c026feaf6e68759146fe5b6fd52eaa3c3c5552d83719d2cb900615e2a634db", "result": "valid", "flags": []}, {"tcId": 274, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "6894de495e7bb5566807d475d96a0d414a94f4f02c3ab7c2edc2916deafc1e1fa603642c20fabc07182867fcc6923d35be23ad3f97a5f93c6ec5b9cce8239569", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "KCnDH6ouQA40TtlLyj_NBUWVbrz-itD236X_jv____8", "y": "oBqvrwAOUlhYVa-nZ2reKEETCZBS31fn6zvTfr65Ii4"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "wx": "2829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffff", "wy": "00a01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKCnDH6ouQA40TtlLyj/NBUWVbrz+\nitD236X/jv////+gGq+vAA5SWFhVr6dnat4oQRMJkFLfV+frO9N+vrkiLg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 275, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "e500c086fedd59e090ce7bfb615751ed9abe4c09b839ee8f05320245b9796f3e807b1d0638c86ef6113fff0d63497800e1b848b5a303a54c748e45ca8f35d7d7", "result": "valid", "flags": []}, {"tcId": 276, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "b922c1abe1a8309c0acf90e586c6de8c33e37057673390a97ff098f71680b32bf86d92b051b7923d82555c205e21b54eab869766c716209648c3e6cc2629057d", "result": "valid", "flags": []}, {"tcId": 277, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "823c37e46c74ec8497d89245fde3bf53ddb462c00d840e983dcb1b72bbf8bf27c4552f2425d14f0f0fa988778403d60a58962e7c548715af83b2edabbb24a49f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "____-UgIHmoEWN2PnnOPJmX_kFmtaqwHCDGMTKmnpPU", "y": "Woq8ui3ahHQxHuVBSblzyuDA-4lVetC_eOZSmhZjvXM"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "wx": "00fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f5", "wy": "5a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE////+UgIHmoEWN2PnnOPJmX/kFmt\naqwHCDGMTKmnpPVairy6LdqEdDEe5UFJuXPK4MD7iVV60L945lKaFmO9cw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 278, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "577a08a95db6dcda9985109942d3786630f640190f920b95bd4d5d84e0f163efd762286e92925973fd38b67ef944a99c0ec5b499b7175cbb4369e053c1fcbb10", "result": "valid", "flags": []}, {"tcId": 279, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "7ba458cfe952326922c7aa2854bdc673ce3daaf65d464dfb9f700701503056b10df8821c92d20546fa741fb426bf56728a53182691964225c9b380b56b22ee6d", "result": "valid", "flags": []}, {"tcId": 280, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "5cd60c3b021b4be116f06f1d447f65e458329a8bbae1d9b5977d18cf561848614c635cd7aa9aebb5716d5ae09e57f8c481a741a029b40f71ec47344ef883e86e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "AAAAA_oV-WOUnV8DpvXH-G-eABXusjrrv_EXOTe6dI4", "y": "EJmHIHDo6HxVX6E2Wcyl1_rc_LACPqiJVIykivK6fnE"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "wx": "03fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e", "wy": "1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAAAAA/oV+WOUnV8DpvXH+G+eABXu\nsjrrv/EXOTe6dI4QmYcgcOjofFVfoTZZzKXX+tz8sAI+qIlUjKSK8rp+cQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 281, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "4b50e1e8cf830e04c17e7472caf60da8150ffa568e2c64498cc972a379e542e52e3adaa5afab89cca91693609555f40543578852cde29c21cb037c0c0b78478e", "result": "valid", "flags": []}, {"tcId": 282, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "5aea930c7d8fffcd5c6df2c9430ef76f8b5ed58a8b9c95847288abf8f09a1ac27ddfef7688a6053ce4eeeeefd6f1a9d71381b7548925f6682aa0a9d05cf5a3a3", "result": "valid", "flags": []}, {"tcId": 283, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "98b092c2d14b5b14a23e9368e0ce1be744dfae9f9a5cdaba51e7872099df96f290d3e4f87bd7bc94589f8150b6b01045cd8759a00af78b24d7de771887610df5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "vLspFMefBF6qbsu8YSgWs75dLWeWcH2BJen4UcGK8BU", "y": "AAAAABNSu0oPoupMzrmrY91oSt5aESe88wCmmKcZO8I"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "1352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BUAAAAAE1K7Sg+i6kzOuatj3WhK3loRJ7zzAKaYpxk7wg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 284, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "9e95f2856a9fff9a172b07817c8c60fe185cd3ce9582678f8cc4b02bc444621ac54ca51d8117d904f0d3773911cb2792348fae21c2da7dad25f990d122376e4c", "result": "valid", "flags": []}, {"tcId": 285, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "e77df8f9782696344c33de29ebdc9f8d3fcf463d950cdbe256fd4fc2fd44877e87028850c962cf2fb450ffe6b983981e499dc498fbd654fa454c9e07c8cb5ca8", "result": "valid", "flags": []}, {"tcId": 286, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "bd2dd6f5026d2b5ad7ead74bdf52b8cbcabc08facee0a1c8584658a85ed0c5dc3e8543e819bdae47d872e29a85ba38addf3eaeaad8786d79c3fb027f6f1ff4bf", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256", "kid": "none", "kty": "EC", "x": "vLspFMefBF6qbsu8YSgWs75dLWeWcH2BJen4UcGK8BU", "y": "_____uytRLbwXRWzMUZUnCKXtSKl7thDDP9ZZ1jmxD0"}, "key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "00fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BX////+7K1EtvBdFbMxRlScIpe1IqXu2EMM/1lnWObEPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 287, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "bd5c0294acc28c15c5d1ebc7274c9ca21a081c8a67da430a34a7fff1a564fabb7ec103a2385b4ff38b47d306434e9091de24dc9f1a25967ee06f8a0a53ac0181", "result": "valid", "flags": []}, {"tcId": 288, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3c7dbfb43dd80379ee2c23ad5472873a22c8a0179ac8f381ad9e0f193231dc1f7cf8e07530ade503b3d43a84b75a2a76fc40763daed4e9734e745c58c9ae72d3", "result": "valid", "flags": []}, {"tcId": 289, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "b38ca4dac6d949be5e5f969860269f0eedff2eb92f45bfc02470300cc96dd5261c7b22992bb13749cc0c5bc25330a17446e40db734203f9035172725fc70f863", "result": "valid", "flags": []}]}]}