=pod

=head1 NAME

OSSL_STORE_CTX, OSSL_STORE_post_process_info_fn, OSSL_STORE_open,
OSSL_STORE_ctrl, OSSL_STORE_load, OSSL_STORE_eof, OSSL_STORE_error,
OSSL_STORE_close - Types and functions to read objects from a URI

=head1 SYNOPSIS

 #include <openssl/store.h>

 typedef struct ossl_store_ctx_st OSSL_STORE_CTX;

 typedef OSSL_STORE_INFO *(*OSSL_STORE_post_process_info_fn)(OSSL_STORE_INFO *,
                                                             void *);

 OSSL_STORE_CTX *OSSL_STORE_open(const char *uri, const UI_METHOD *ui_method,
                                 void *ui_data,
                                 OSSL_STORE_post_process_info_fn post_process,
                                 void *post_process_data);
 int OSSL_STORE_ctrl(OSSL_STORE_CTX *ctx, int cmd, ... /* args */);
 OSSL_STORE_INFO *OSSL_STORE_load(OSSL_STORE_CTX *ctx);
 int OSSL_STORE_eof(OSSL_STORE_CTX *ctx);
 int OSSL_STORE_error(OSSL_STORE_CTX *ctx);
 int OSSL_STORE_close(OSSL_STORE_CTX *ctx);

=head1 DESCRIPTION

These functions help the application to fetch supported objects (see
L<OSSL_STORE_INFO(3)/SUPPORTED OBJECTS> for information on which those are)
from a given URI (see L</SUPPORTED SCHEMES> for more information on
the supported URI schemes).
The general method to do so is to "open" the URI using OSSL_STORE_open(),
read each available and supported object using OSSL_STORE_load() as long as
OSSL_STORE_eof() hasn't been reached, and finish it off with OSSL_STORE_close().

The retrieved information is stored in a B<OSSL_STORE_INFO>, which is further
described in L<OSSL_STORE_INFO(3)>.

=head2 Types

B<OSSL_STORE_CTX> is a context variable that holds all the internal
information for OSSL_STORE_open(), OSSL_STORE_load(), OSSL_STORE_eof() and
OSSL_STORE_close() to work together.

=head2 Functions

OSSL_STORE_open() takes a uri or path I<uri>, password UI method
I<ui_method> with associated data I<ui_data>, and post processing
callback I<post_process> with associated data I<post_process_data>,
opens a channel to the data located at that URI and returns a
B<OSSL_STORE_CTX> with all necessary internal information.
The given I<ui_method> and I<ui_data> will be reused by all
functions that use B<OSSL_STORE_CTX> when interaction is needed,
for instance to provide a password.
The given I<post_process> and I<post_process_data> will be reused by
OSSL_STORE_load() to manipulate or drop the value to be returned.
The I<post_process> function drops values by returning NULL, which
will cause OSSL_STORE_load() to start its process over with loading
the next object, until I<post_process> returns something other than
NULL, or the end of data is reached as indicated by OSSL_STORE_eof().

OSSL_STORE_ctrl() takes a B<OSSL_STORE_CTX>, and command number I<cmd> and
more arguments not specified here.
The available loader specific command numbers and arguments they each
take depends on the loader that's used and is documented together with
that loader.

There are also global controls available:

=over 4

=item B<OSSL_STORE_C_USE_SECMEM>

Controls if the loader should attempt to use secure memory for any
allocated B<OSSL_STORE_INFO> and its contents.
This control expects one argument, a pointer to an B<int> that is expected to
have the value 1 (yes) or 0 (no).
Any other value is an error.

=back

OSSL_STORE_load() takes a B<OSSL_STORE_CTX>, tries to load the next available
object and return it wrapped with  B<OSSL_STORE_INFO>.

OSSL_STORE_eof() takes a B<OSSL_STORE_CTX> and checks if we've reached the end
of data.

OSSL_STORE_error() takes a B<OSSL_STORE_CTX> and checks if an error occurred in
the last OSSL_STORE_load() call.
Note that it may still be meaningful to try and load more objects, unless
OSSL_STORE_eof() shows that the end of data has been reached.

OSSL_STORE_close() takes a B<OSSL_STORE_CTX>, closes the channel that was opened
by OSSL_STORE_open() and frees all other information that was stored in the
B<OSSL_STORE_CTX>, as well as the B<OSSL_STORE_CTX> itself.
If I<ctx> is NULL it does nothing.

=head1 SUPPORTED SCHEMES

The basic supported scheme is B<file:>.
Any other scheme can be added dynamically, using
OSSL_STORE_register_loader().

=head1 NOTES

A string without a scheme prefix (that is, a non-URI string) is
implicitly interpreted as using the F<file:> scheme.

There are some tools that can be used together with
OSSL_STORE_open() to determine if any failure is caused by an unparsable
URI, or if it's a different error (such as memory allocation
failures); if the URI was parsable but the scheme unregistered, the
top error will have the reason C<OSSL_STORE_R_UNREGISTERED_SCHEME>.

These functions make no direct assumption regarding the pass phrase received
from the password callback.
The loaders may make assumptions, however.
For example, the B<file:> scheme loader inherits the assumptions made by
OpenSSL functionality that handles the different file types; this is mostly
relevant for PKCS#12 objects.
See L<passphrase-encoding(7)> for further information.

=head1 RETURN VALUES

OSSL_STORE_open() returns a pointer to a B<OSSL_STORE_CTX> on success, or
NULL on failure.

OSSL_STORE_load() returns a pointer to a B<OSSL_STORE_INFO> on success, or
NULL on error or when end of data is reached.
Use OSSL_STORE_error() and OSSL_STORE_eof() to determine the meaning of a
returned NULL.

OSSL_STORE_eof() returns 1 if the end of data has been reached, otherwise
0.

OSSL_STORE_error() returns 1 if an error occurred in an OSSL_STORE_load() call,
otherwise 0.

OSSL_STORE_ctrl() and OSSL_STORE_close() returns 1 on success, or 0 on failure.

=head1 SEE ALSO

L<ossl_store(7)>, L<OSSL_STORE_INFO(3)>, L<OSSL_STORE_register_loader(3)>,
L<passphrase-encoding(7)>

=head1 HISTORY

OSSL_STORE_CTX(), OSSL_STORE_post_process_info_fn(), OSSL_STORE_open(),
OSSL_STORE_ctrl(), OSSL_STORE_load(), OSSL_STORE_eof() and OSSL_STORE_close()
were added in OpenSSL 1.1.1.

Handling of NULL I<ctx> argument for OSSL_STORE_close()
was introduced in OpenSSL 1.1.1h.

=head1 COPYRIGHT

Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
