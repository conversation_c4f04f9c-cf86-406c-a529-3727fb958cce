{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
KEYS({{ variant['name'] }});
IMPLEMENT_TEST_SUITE({{ variant['name'] }}, "{{ variant['name'] }}")
      {%- for classical_alg in variant['mix_with'] %}
KEYS({{ classical_alg['name'] }}_{{ variant['name'] }});
IMPLEMENT_TEST_SUITE({{ classical_alg['name'] }}_{{ variant['name'] }}, "{{ classical_alg['name'] }}_{{ variant['name'] }}")
      {%- endfor %}
   {%- endfor %}
{%- endfor %}

