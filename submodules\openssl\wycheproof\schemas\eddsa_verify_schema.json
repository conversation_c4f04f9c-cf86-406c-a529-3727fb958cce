{"type": "object", "definitions": {"EddsaTestGroup": {"type": "object", "properties": {"type": {"enum": ["EddsaVerify"]}, "jwk": {"type": "object", "description": "the private key in webcrypto format"}, "key": {"type": "object", "description": "unencoded key pair"}, "keyDer": {"type": "string", "format": "<PERSON>n", "description": "Asn encoded public key"}, "keyPem": {"type": "string", "format": "Pem", "description": "Pem encoded public key"}, "tests": {"type": "array", "items": {"$ref": "#/definitions/SignatureTestVector"}}}}, "SignatureTestVector": {"type": "object", "properties": {"tcId": {"type": "integer", "description": "Identifier of the test case"}, "comment": {"type": "string", "description": "A brief description of the test case"}, "msg": {"type": "string", "format": "HexBytes", "description": "The message to sign"}, "sig": {"type": "string", "format": "HexBytes", "description": "A signature for msg"}, "result": {"type": "string", "description": "Test result", "enum": ["valid", "invalid", "acceptable"]}, "flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags"}}}}, "properties": {"algorithm": {"type": "string", "description": "the primitive tested in the test file"}, "generatorVersion": {"type": "string", "description": "the version of the test vectors."}, "header": {"type": "array", "items": {"type": "string"}, "description": "additional documentation"}, "notes": {"type": "object", "description": "a description of the labels used in the test vectors"}, "numberOfTests": {"type": "integer", "description": "the number of test vectors in this test"}, "schema": {"enum": ["eddsa_verify_schema.json"]}, "testGroups": {"type": "array", "items": {"$ref": "#/definitions/EddsaTestGroup"}}}}