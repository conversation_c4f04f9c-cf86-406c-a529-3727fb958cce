/*
 * $Header$
 *
 * Copyright 2008 Massachusetts Institute of Technology.
 * All Rights Reserved.
 *
 * Export of this software from the United States of America may
 * require a specific license from the United States Government.
 * It is the responsibility of any person or organization contemplating
 * export to obtain such a license before exporting.
 *
 * WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 * distribute this software and its documentation for any purpose and
 * without fee is hereby granted, provided that the above copyright
 * notice appear in all copies and that both that copyright notice and
 * this permission notice appear in supporting documentation, and that
 * the name of M.I.T. not be used in advertising or publicity pertaining
 * to distribution of the software without specific, written prior
 * permission.  Furthermore if you modify this software you must label
 * your software as modified software and not distribute it in such a
 * fashion that it might be confused with the original M.I.T. software.
 * M.I.T. makes no representations about the suitability of
 * this software for any purpose.  It is provided "as is" without express
 * or implied warranty.
 */

[ uuid (906B0CE0-C70B-1067-B317-00DD010662DA),
  version(1.0),
  pointer_default(unique)
]

interface ccs_request {

typedef char CC_CHAR;
typedef unsigned char CC_UCHAR;
typedef int CC_INT32;
typedef unsigned int CC_UINT32;

const long HSIZE = 8;

void ccs_rpc_request(
    [in]                        const long  rpcmsg,         /* Message type */
    [in, size_is(HSIZE)]        const char  tsphandle[],
    [in,  string]               const char* pszUUID,        /* Requestor's UUID */
    [in]                        const long  lenRequest,     /* Length of buffer */
    [in,  size_is(lenRequest)]  const char* pszRequest,     /* Data buffer */
    [in]                        const long  serverStartTime,/* Which server session we're talking to */
    [out]                       long*       status );       /* Return code */

void ccs_rpc_connect(
    [in]                        const long  rpcmsg,         /* Message type */
    [in, size_is(HSIZE)]        const char  tsphandle[],
    [in,  string]               const char* pszUUID,        /* Requestor's UUID */
    [out]                       long*       status );       /* Return code */

CC_UINT32 ccs_authenticate(
    [in, string]                const CC_CHAR* name );
}
