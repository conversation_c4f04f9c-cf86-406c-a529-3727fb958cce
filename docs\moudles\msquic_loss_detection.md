[TOC]

# quic rtt 计算
smoothed_rtt：是指RTT样本的指数加权移动平均值  

rttvar：是指RTT样本的估计方差  
 
 在connection的第一个RTT计算上，smoothed_rtt设置为latest_rtt，smoothed_rtt和rttvar的计算方法如下。在连接中的第一个RTT示例：
smoothed_rtt = latest_rtt
rttvar = latest_rtt / 2
在随后的RTT样本中，smoothed_rtt和rttvar演变如下：
ack_delay = min（ACK帧中的Ack延迟，max_ack_delay）
adjusted_rtt = latest_rtt
if(min_rtt + ack_delay <latest_rtt):
  adjusted_rtt = latest_rtt  -  ack_delay
smoothed_rtt = 7/8 * smoothed_rtt + 1/8 * adjusted_rtt
rttvar_sample = abs（smoothed_rtt  -  adjusted_rtt）
rttvar = 3/4 * rttvar + 1/4 * rttvar_sample

# FR快速重传（数据包阈值）
根据TCP丢失检测的最佳实践，QUIC数据包重排序阈值（kPacketThreshold）的推荐初始值为3。  
不同于TCP的收到连续3个重复的ack，才触发FR快重传。QUIC在收到每个ack包后直接将packet_number小于3（经验值）的包即判断为丢失，不需要多个包的重复ACK，比TCP更加快速的进行快重传

# ER早期重传（时间阈值）
一旦相同数据包号空间内的后一个数据包先被ack，如果先前发送的包过了1个时间阈值，则应该声明为丢失。为避免过早地将数据包声明为丢失，时间阈值必须至少为kGranularity。

QUIC-IETF：

1、loss_delay = kTimeThreshold * max（SRTT，latest_RTT，kGranularity）

2、如果在最大ack包之前发送的数据包尚未被声明丢失，那么应该为剩余时间设置一个定时器。使用max（SRTT，latest_RTT）可以防止以下两种情况：

3、最新的RTT样本低于SRTT，可能是由于重新排序确认遇到了较短的路径;

4、最新的RTT样本高于SRTT，可能是由于实际RTT持续增加，但平滑的SRTT还没有赶上。

推荐时间阈值系数（kTimeThreshold）为9/8，以往返时间乘数表示。

实现可以尝试绝对阈值、先前连接的阈值、自适应阈值或包括RTT方差等。较小的阈值降低了重新排序弹性，但增加了伪重传。较大的阈值会增加丢失检测延迟。

# 基于超时时间的丢包检测
如果在预期的时间段内未确认ack-eliciting数据包或握手尚未完成，网络探测超时机制（PTO）会触发发送一个或两个探测数据包。PTO能够恢复丢失的尾包或未ack的包。QUIC中使用的PTO算法实现了Tail Loss Probe（TLP）、RTO和TCP的F-RTO等算法，其中一些超时时间的参数设置基于了TCP的重传超时时间经验值。

首先介绍一下QUIC中相关的超时定时器，QUIC中的PTO算法都是根据定时器来实现相关的触发策略。

超时重传定时器类型

RTO_MODE：传统TCP类型的RTO定时器。

TLP_MODE：尾包丢失探测器，默认情况下，QUIC在RTO超时重传之前最多发送两次

HANDSHAKE_MODE：在握手完成之前重新发送握手数据包。

LOSS_MODE：当分组在丢包检测算法预期之前未被确认时，重新调用丢包检测。

定时器设置优先级

HANDSHAKE_MODE > LOSS_MODE > TLP_MODE > RTO_MODE

定时器设置

定时器设置函数 SetRetransmissionAlarm

1. WritePacket 发送数据包时。

2. PostProcessAfterAckFrame 收到ack时。

3. OnRetransmissionTimeout 定时器超时。

4. NeuterUnencryptedPackets 握手期间的交互。

5. OnHandshakeComplete 客户端收到SHLO，握手完毕。

6. OnDecryptedPacket 服务端收到前向加密包，握手完毕




## ack 发送时机
1. 收到IMMEDIATE_ACK  
2. MaxAckDelayMs==0，收到任何包都发ack
3. 检测到packet 包序号不连续，判定有丢包
4. 从上一次发送ack 开始 经过MaxAckDelayMs之后，定时器触发发送ack

#cubic 
1. 首次发包窗口 为第一次检测到丢包前的发包数量* payload 长度
2. 检测到丢包后 需要根据最大发包窗口计算 k值
K = (WindowMax * (1 - BETA) / C) ^ (1/3)
W_cubic(t) = C*(t-K)^3 + WindowMax.
时间越接近K值 W_cubic 越趋近WindowMax， t 越大，曲线越陡峭
3. 慢启动窗口阈值，拥塞窗口大小都是第一次丢包时初始化， 其中beta = 7；
  Cubic->SlowStartThreshold =
  Cubic->CongestionWindow =
  Cubic->AimdWindow =
        Cubic->CongestionWindow * TEN_TIMES_BETA_CUBIC / 10;


4. 如果丢包后算出的最大发送窗大小比上一次的要小，代表带宽有下降的趋势， 最大发送窗需要更小才行  
  收敛值为17/20，如下， 如果发送窗时增长的， 则不作处理
if (Cubic->WindowLastMax > Cubic->WindowMax) {
    //
    // Fast convergence.
    //
    Cubic->WindowLastMax = Cubic->WindowMax;
    Cubic->WindowMax = Cubic->WindowMax * (10 + TEN_TIMES_BETA_CUBIC) / 20;
} else {
    Cubic->WindowLastMax = Cubic->WindowMax;
}



