=pod

=head1 NAME

EVP_rc5_32_12_16_cbc,
EVP_rc5_32_12_16_cfb,
EVP_rc5_32_12_16_cfb64,
EVP_rc5_32_12_16_ecb,
EVP_rc5_32_12_16_ofb
- EVP RC5 cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_rc5_32_12_16_cbc(void)
 const EVP_CIPHER *EVP_rc5_32_12_16_cfb(void)
 const EVP_CIPHER *EVP_rc5_32_12_16_cfb64(void)
 const EVP_CIPHER *EVP_rc5_32_12_16_ecb(void)
 const EVP_CIPHER *EVP_rc5_32_12_16_ofb(void)

=head1 DESCRIPTION

The RC5 encryption algorithm for EVP.

=over 4

=item EVP_rc5_32_12_16_cbc(),
EVP_rc5_32_12_16_cfb(),
EVP_rc5_32_12_16_cfb64(),
EVP_rc5_32_12_16_ecb(),
EVP_rc5_32_12_16_ofb()

RC5 encryption algorithm in CBC, CFB, ECB and OFB modes respectively. This is a
variable key length cipher with an additional "number of rounds" parameter. By
default the key length is set to 128 bits and 12 rounds. Alternative key lengths
can be set using L<EVP_CIPHER_CTX_set_key_length(3)>. The maximum key length is
2040 bits.

The following rc5 specific I<ctrl>s are supported (see
L<EVP_CIPHER_CTX_ctrl(3)>).

=over 4

=item EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_SET_RC5_ROUNDS, rounds, NULL)

Sets the number of rounds to B<rounds>. This must be one of RC5_8_ROUNDS,
RC5_12_ROUNDS or RC5_16_ROUNDS.

=item EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GET_RC5_ROUNDS, 0, &rounds)

Stores the number of rounds currently configured in B<*rounds> where B<*rounds>
is an int.

=back

=back

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.


=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

