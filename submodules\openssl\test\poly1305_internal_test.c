/*
 * Copyright 2016-2018 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/* Internal tests for the poly1305 module */

#include <stdio.h>
#include <string.h>

#include "testutil.h"
#include "crypto/poly1305.h"
#include "../crypto/poly1305/poly1305_local.h"
#include "internal/nelem.h"

typedef struct {
    size_t size;
    const unsigned char data[1024];
} SIZED_DATA;

typedef struct {
    SIZED_DATA input;
    SIZED_DATA key;
    SIZED_DATA expected;
} TESTDATA;

/**********************************************************************
 *
 * Test of poly1305 internal functions
 *
 ***/

static TESTDATA tests[] = {
    /*
     * RFC7539
     */
    {
        {
            34,
            {
                0x43, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x67, 0x72,
                0x61, 0x70, 0x68, 0x69, 0x63, 0x20, 0x46, 0x6f,
                0x72, 0x75, 0x6d, 0x20, 0x52, 0x65, 0x73, 0x65,
                0x61, 0x72, 0x63, 0x68, 0x20, 0x47, 0x72, 0x6f,

                0x75, 0x70
            }
        },
        {
            32,
            {
                0x85, 0xd6, 0xbe, 0x78, 0x57, 0x55, 0x6d, 0x33,
                0x7f, 0x44, 0x52, 0xfe, 0x42, 0xd5, 0x06, 0xa8,
                0x01, 0x03, 0x80, 0x8a, 0xfb, 0x0d, 0xb2, 0xfd,
                0x4a, 0xbf, 0xf6, 0xaf, 0x41, 0x49, 0xf5, 0x1b
            }
        },
        {
            16,
            {
                0xa8, 0x06, 0x1d, 0xc1, 0x30, 0x51, 0x36, 0xc6,
                0xc2, 0x2b, 0x8b, 0xaf, 0x0c, 0x01, 0x27, 0xa9
            }
        }
    },
    /*
     * test vectors from "The Poly1305-AES message-authentication code"
     */
    {
        {
            2,
            {
                0xf3, 0xf6
            }
        },
        {
            32,
            {
                0x85, 0x1f, 0xc4, 0x0c, 0x34, 0x67, 0xac, 0x0b,
                0xe0, 0x5c, 0xc2, 0x04, 0x04, 0xf3, 0xf7, 0x00,
                0x58, 0x0b, 0x3b, 0x0f, 0x94, 0x47, 0xbb, 0x1e,
                0x69, 0xd0, 0x95, 0xb5, 0x92, 0x8b, 0x6d, 0xbc
            }
        },
        {
            16,
            {
                0xf4, 0xc6, 0x33, 0xc3, 0x04, 0x4f, 0xc1, 0x45,
                0xf8, 0x4f, 0x33, 0x5c, 0xb8, 0x19, 0x53, 0xde
            }
        }
    },
    {
        {
            0,
            {
                0
            }
        },
        {
            32,
            {
                0xa0, 0xf3, 0x08, 0x00, 0x00, 0xf4, 0x64, 0x00,
                0xd0, 0xc7, 0xe9, 0x07, 0x6c, 0x83, 0x44, 0x03,
                0xdd, 0x3f, 0xab, 0x22, 0x51, 0xf1, 0x1a, 0xc7,
                0x59, 0xf0, 0x88, 0x71, 0x29, 0xcc, 0x2e, 0xe7
            }
        },
        {
            16,
            {
                0xdd, 0x3f, 0xab, 0x22, 0x51, 0xf1, 0x1a, 0xc7,
                0x59, 0xf0, 0x88, 0x71, 0x29, 0xcc, 0x2e, 0xe7
            }
        }
    },
    {
        {
            32,
            {
                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36
            }
        },
        {
            32,
            {
                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef
            }
        },
        {
            16,
            {
                0x0e, 0xe1, 0xc1, 0x6b, 0xb7, 0x3f, 0x0f, 0x4f,
                0xd1, 0x98, 0x81, 0x75, 0x3c, 0x01, 0xcd, 0xbe
            }
        }
    },
    {
        {
            63,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0x51, 0x54, 0xad, 0x0d, 0x2c, 0xb2, 0x6e, 0x01,
                0x27, 0x4f, 0xc5, 0x11, 0x48, 0x49, 0x1f, 0x1b
            }
        },
    },
    /*
     * self-generated vectors exercise "significant" lengths, such that
     * are handled by different code paths
     */
    {
        {
            64,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66
            }
        },
    },
    {
        {
            48,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57

            }
        },
        {
            16,
            {
                0x5b, 0x88, 0xd7, 0xf6, 0x22, 0x8b, 0x11, 0xe2,
                0xe2, 0x85, 0x79, 0xa5, 0xc0, 0xc1, 0xf7, 0x61
            }
        },
    },
    {
        {
            96,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0xbb, 0xb6, 0x13, 0xb2, 0xb6, 0xd7, 0x53, 0xba,
                0x07, 0x39, 0x5b, 0x91, 0x6a, 0xae, 0xce, 0x15
            }
        },
    },
    {
        {
            112,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0xc7, 0x94, 0xd7, 0x05, 0x7d, 0x17, 0x78, 0xc4,
                0xbb, 0xee, 0x0a, 0x39, 0xb3, 0xd9, 0x73, 0x42
            }
        },
    },
    {
        {
            128,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0xff, 0xbc, 0xb9, 0xb3, 0x71, 0x42, 0x31, 0x52,
                0xd7, 0xfc, 0xa5, 0xad, 0x04, 0x2f, 0xba, 0xa9
            }
        },
    },
    {
        {
            144,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36,

                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0x06, 0x9e, 0xd6, 0xb8, 0xef, 0x0f, 0x20, 0x7b,
                0x3e, 0x24, 0x3b, 0xb1, 0x01, 0x9f, 0xe6, 0x32
            }
        },
    },
    {
        {
            160,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36,

                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66,
                0x5b, 0x88, 0xd7, 0xf6, 0x22, 0x8b, 0x11, 0xe2,
                0xe2, 0x85, 0x79, 0xa5, 0xc0, 0xc1, 0xf7, 0x61
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0xcc, 0xa3, 0x39, 0xd9, 0xa4, 0x5f, 0xa2, 0x36,
                0x8c, 0x2c, 0x68, 0xb3, 0xa4, 0x17, 0x91, 0x33
            }
        },
    },
    {
        {
            288,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36,

                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66,
                0x5b, 0x88, 0xd7, 0xf6, 0x22, 0x8b, 0x11, 0xe2,
                0xe2, 0x85, 0x79, 0xa5, 0xc0, 0xc1, 0xf7, 0x61,

                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0x53, 0xf6, 0xe8, 0x28, 0xa2, 0xf0, 0xfe, 0x0e,
                0xe8, 0x15, 0xbf, 0x0b, 0xd5, 0x84, 0x1a, 0x34
            }
        },
    },
    {
        {
            320,
            {
                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36,

                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66,
                0x5b, 0x88, 0xd7, 0xf6, 0x22, 0x8b, 0x11, 0xe2,
                0xe2, 0x85, 0x79, 0xa5, 0xc0, 0xc1, 0xf7, 0x61,

                0xab, 0x08, 0x12, 0x72, 0x4a, 0x7f, 0x1e, 0x34,
                0x27, 0x42, 0xcb, 0xed, 0x37, 0x4d, 0x94, 0xd1,
                0x36, 0xc6, 0xb8, 0x79, 0x5d, 0x45, 0xb3, 0x81,
                0x98, 0x30, 0xf2, 0xc0, 0x44, 0x91, 0xfa, 0xf0,

                0x99, 0x0c, 0x62, 0xe4, 0x8b, 0x80, 0x18, 0xb2,
                0xc3, 0xe4, 0xa0, 0xfa, 0x31, 0x34, 0xcb, 0x67,
                0xfa, 0x83, 0xe1, 0x58, 0xc9, 0x94, 0xd9, 0x61,
                0xc4, 0xcb, 0x21, 0x09, 0x5c, 0x1b, 0xf9, 0xaf,

                0x48, 0x44, 0x3d, 0x0b, 0xb0, 0xd2, 0x11, 0x09,
                0xc8, 0x9a, 0x10, 0x0b, 0x5c, 0xe2, 0xc2, 0x08,
                0x83, 0x14, 0x9c, 0x69, 0xb5, 0x61, 0xdd, 0x88,
                0x29, 0x8a, 0x17, 0x98, 0xb1, 0x07, 0x16, 0xef,

                0x66, 0x3c, 0xea, 0x19, 0x0f, 0xfb, 0x83, 0xd8,
                0x95, 0x93, 0xf3, 0xf4, 0x76, 0xb6, 0xbc, 0x24,
                0xd7, 0xe6, 0x79, 0x10, 0x7e, 0xa2, 0x6a, 0xdb,
                0x8c, 0xaf, 0x66, 0x52, 0xd0, 0x65, 0x61, 0x36,

                0x81, 0x20, 0x59, 0xa5, 0xda, 0x19, 0x86, 0x37,
                0xca, 0xc7, 0xc4, 0xa6, 0x31, 0xbe, 0xe4, 0x66,
                0x5b, 0x88, 0xd7, 0xf6, 0x22, 0x8b, 0x11, 0xe2,
                0xe2, 0x85, 0x79, 0xa5, 0xc0, 0xc1, 0xf7, 0x61
            }
        },
        {
            32,
            {
                0x12, 0x97, 0x6a, 0x08, 0xc4, 0x42, 0x6d, 0x0c,
                0xe8, 0xa8, 0x24, 0x07, 0xc4, 0xf4, 0x82, 0x07,
                0x80, 0xf8, 0xc2, 0x0a, 0xa7, 0x12, 0x02, 0xd1,
                0xe2, 0x91, 0x79, 0xcb, 0xcb, 0x55, 0x5a, 0x57
            }
        },
        {
            16,
            {
                0xb8, 0x46, 0xd4, 0x4e, 0x9b, 0xbd, 0x53, 0xce,
                0xdf, 0xfb, 0xfb, 0xb6, 0xb7, 0xfa, 0x49, 0x33
            }
        },
    },
    /*
     * 4th power of the key spills to 131th bit in SIMD key setup
     */
    {
        {
            256,
            {
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
            }
        },
        {
            32,
            {
                0xad, 0x62, 0x81, 0x07, 0xe8, 0x35, 0x1d, 0x0f,
                0x2c, 0x23, 0x1a, 0x05, 0xdc, 0x4a, 0x41, 0x06,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x07, 0x14, 0x5a, 0x4c, 0x02, 0xfe, 0x5f, 0xa3,
                0x20, 0x36, 0xde, 0x68, 0xfa, 0xbe, 0x90, 0x66
            }
        },
    },
    /*
     * poly1305_ieee754.c failed this in final stage
     */
    {
        {
            252,
            {
                0x84, 0x23, 0x64, 0xe1, 0x56, 0x33, 0x6c, 0x09,
                0x98, 0xb9, 0x33, 0xa6, 0x23, 0x77, 0x26, 0x18,
                0x0d, 0x9e, 0x3f, 0xdc, 0xbd, 0xe4, 0xcd, 0x5d,
                0x17, 0x08, 0x0f, 0xc3, 0xbe, 0xb4, 0x96, 0x14,

                0xd7, 0x12, 0x2c, 0x03, 0x74, 0x63, 0xff, 0x10,
                0x4d, 0x73, 0xf1, 0x9c, 0x12, 0x70, 0x46, 0x28,
                0xd4, 0x17, 0xc4, 0xc5, 0x4a, 0x3f, 0xe3, 0x0d,
                0x3c, 0x3d, 0x77, 0x14, 0x38, 0x2d, 0x43, 0xb0,

                0x38, 0x2a, 0x50, 0xa5, 0xde, 0xe5, 0x4b, 0xe8,
                0x44, 0xb0, 0x76, 0xe8, 0xdf, 0x88, 0x20, 0x1a,
                0x1c, 0xd4, 0x3b, 0x90, 0xeb, 0x21, 0x64, 0x3f,
                0xa9, 0x6f, 0x39, 0xb5, 0x18, 0xaa, 0x83, 0x40,

                0xc9, 0x42, 0xff, 0x3c, 0x31, 0xba, 0xf7, 0xc9,
                0xbd, 0xbf, 0x0f, 0x31, 0xae, 0x3f, 0xa0, 0x96,
                0xbf, 0x8c, 0x63, 0x03, 0x06, 0x09, 0x82, 0x9f,
                0xe7, 0x2e, 0x17, 0x98, 0x24, 0x89, 0x0b, 0xc8,

                0xe0, 0x8c, 0x31, 0x5c, 0x1c, 0xce, 0x2a, 0x83,
                0x14, 0x4d, 0xbb, 0xff, 0x09, 0xf7, 0x4e, 0x3e,
                0xfc, 0x77, 0x0b, 0x54, 0xd0, 0x98, 0x4a, 0x8f,
                0x19, 0xb1, 0x47, 0x19, 0xe6, 0x36, 0x35, 0x64,

                0x1d, 0x6b, 0x1e, 0xed, 0xf6, 0x3e, 0xfb, 0xf0,
                0x80, 0xe1, 0x78, 0x3d, 0x32, 0x44, 0x54, 0x12,
                0x11, 0x4c, 0x20, 0xde, 0x0b, 0x83, 0x7a, 0x0d,
                0xfa, 0x33, 0xd6, 0xb8, 0x28, 0x25, 0xff, 0xf4,

                0x4c, 0x9a, 0x70, 0xea, 0x54, 0xce, 0x47, 0xf0,
                0x7d, 0xf6, 0x98, 0xe6, 0xb0, 0x33, 0x23, 0xb5,
                0x30, 0x79, 0x36, 0x4a, 0x5f, 0xc3, 0xe9, 0xdd,
                0x03, 0x43, 0x92, 0xbd, 0xde, 0x86, 0xdc, 0xcd,

                0xda, 0x94, 0x32, 0x1c, 0x5e, 0x44, 0x06, 0x04,
                0x89, 0x33, 0x6c, 0xb6, 0x5b, 0xf3, 0x98, 0x9c,
                0x36, 0xf7, 0x28, 0x2c, 0x2f, 0x5d, 0x2b, 0x88,
                0x2c, 0x17, 0x1e, 0x74
            }
        },
        {
            32,
            {
                0x95, 0xd5, 0xc0, 0x05, 0x50, 0x3e, 0x51, 0x0d,
                0x8c, 0xd0, 0xaa, 0x07, 0x2c, 0x4a, 0x4d, 0x06,
                0x6e, 0xab, 0xc5, 0x2d, 0x11, 0x65, 0x3d, 0xf4,
                0x7f, 0xbf, 0x63, 0xab, 0x19, 0x8b, 0xcc, 0x26
            }
        },
        {
            16,
            {
                0xf2, 0x48, 0x31, 0x2e, 0x57, 0x8d, 0x9d, 0x58,
                0xf8, 0xb7, 0xbb, 0x4d, 0x19, 0x10, 0x54, 0x31
            }
        },
    },
    /*
     * AVX2 in poly1305-x86.pl failed this with 176+32 split
     */
    {
        {
            208,
            {
                0x24, 0x8a, 0xc3, 0x10, 0x85, 0xb6, 0xc2, 0xad,
                0xaa, 0xa3, 0x82, 0x59, 0xa0, 0xd7, 0x19, 0x2c,
                0x5c, 0x35, 0xd1, 0xbb, 0x4e, 0xf3, 0x9a, 0xd9,
                0x4c, 0x38, 0xd1, 0xc8, 0x24, 0x79, 0xe2, 0xdd,

                0x21, 0x59, 0xa0, 0x77, 0x02, 0x4b, 0x05, 0x89,
                0xbc, 0x8a, 0x20, 0x10, 0x1b, 0x50, 0x6f, 0x0a,
                0x1a, 0xd0, 0xbb, 0xab, 0x76, 0xe8, 0x3a, 0x83,
                0xf1, 0xb9, 0x4b, 0xe6, 0xbe, 0xae, 0x74, 0xe8,

                0x74, 0xca, 0xb6, 0x92, 0xc5, 0x96, 0x3a, 0x75,
                0x43, 0x6b, 0x77, 0x61, 0x21, 0xec, 0x9f, 0x62,
                0x39, 0x9a, 0x3e, 0x66, 0xb2, 0xd2, 0x27, 0x07,
                0xda, 0xe8, 0x19, 0x33, 0xb6, 0x27, 0x7f, 0x3c,

                0x85, 0x16, 0xbc, 0xbe, 0x26, 0xdb, 0xbd, 0x86,
                0xf3, 0x73, 0x10, 0x3d, 0x7c, 0xf4, 0xca, 0xd1,
                0x88, 0x8c, 0x95, 0x21, 0x18, 0xfb, 0xfb, 0xd0,
                0xd7, 0xb4, 0xbe, 0xdc, 0x4a, 0xe4, 0x93, 0x6a,

                0xff, 0x91, 0x15, 0x7e, 0x7a, 0xa4, 0x7c, 0x54,
                0x44, 0x2e, 0xa7, 0x8d, 0x6a, 0xc2, 0x51, 0xd3,
                0x24, 0xa0, 0xfb, 0xe4, 0x9d, 0x89, 0xcc, 0x35,
                0x21, 0xb6, 0x6d, 0x16, 0xe9, 0xc6, 0x6a, 0x37,

                0x09, 0x89, 0x4e, 0x4e, 0xb0, 0xa4, 0xee, 0xdc,
                0x4a, 0xe1, 0x94, 0x68, 0xe6, 0x6b, 0x81, 0xf2,

                0x71, 0x35, 0x1b, 0x1d, 0x92, 0x1e, 0xa5, 0x51,
                0x04, 0x7a, 0xbc, 0xc6, 0xb8, 0x7a, 0x90, 0x1f,
                0xde, 0x7d, 0xb7, 0x9f, 0xa1, 0x81, 0x8c, 0x11,
                0x33, 0x6d, 0xbc, 0x07, 0x24, 0x4a, 0x40, 0xeb
            }
        },
        {
            32,
            {
                0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0xbc, 0x93, 0x9b, 0xc5, 0x28, 0x14, 0x80, 0xfa,
                0x99, 0xc6, 0xd6, 0x8c, 0x25, 0x8e, 0xc4, 0x2f
            }
        },
    },
    /*
     * test vectors from Google
     */
    {
        {
            0,
            {
                0x00,
            }
        },
        {
            32,
            {
                0xc8, 0xaf, 0xaa, 0xc3, 0x31, 0xee, 0x37, 0x2c,
                0xd6, 0x08, 0x2d, 0xe1, 0x34, 0x94, 0x3b, 0x17,
                0x47, 0x10, 0x13, 0x0e, 0x9f, 0x6f, 0xea, 0x8d,
                0x72, 0x29, 0x38, 0x50, 0xa6, 0x67, 0xd8, 0x6c
            }
        },
        {
            16,
            {
                0x47, 0x10, 0x13, 0x0e, 0x9f, 0x6f, 0xea, 0x8d,
                0x72, 0x29, 0x38, 0x50, 0xa6, 0x67, 0xd8, 0x6c
            }
        },
    },
    {
        {
            12,
            {
                0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x20, 0x77, 0x6f,
                0x72, 0x6c, 0x64, 0x21
            }
        },
        {
            32,
            {
                0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
                0x33, 0x32, 0x2d, 0x62, 0x79, 0x74, 0x65, 0x20,
                0x6b, 0x65, 0x79, 0x20, 0x66, 0x6f, 0x72, 0x20,
                0x50, 0x6f, 0x6c, 0x79, 0x31, 0x33, 0x30, 0x35
            }
        },
        {
            16,
            {
                0xa6, 0xf7, 0x45, 0x00, 0x8f, 0x81, 0xc9, 0x16,
                0xa2, 0x0d, 0xcc, 0x74, 0xee, 0xf2, 0xb2, 0xf0
            }
        },
    },
    {
        {
            32,
            {
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            32,
            {
                0x74, 0x68, 0x69, 0x73, 0x20, 0x69, 0x73, 0x20,
                0x33, 0x32, 0x2d, 0x62, 0x79, 0x74, 0x65, 0x20,
                0x6b, 0x65, 0x79, 0x20, 0x66, 0x6f, 0x72, 0x20,
                0x50, 0x6f, 0x6c, 0x79, 0x31, 0x33, 0x30, 0x35
            }
        },
        {
            16,
            {
                0x49, 0xec, 0x78, 0x09, 0x0e, 0x48, 0x1e, 0xc6,
                0xc2, 0x6b, 0x33, 0xb9, 0x1c, 0xcc, 0x03, 0x07
            }
        },
    },
    {
        {
            128,
            {
                0x89, 0xda, 0xb8, 0x0b, 0x77, 0x17, 0xc1, 0xdb,
                0x5d, 0xb4, 0x37, 0x86, 0x0a, 0x3f, 0x70, 0x21,
                0x8e, 0x93, 0xe1, 0xb8, 0xf4, 0x61, 0xfb, 0x67,
                0x7f, 0x16, 0xf3, 0x5f, 0x6f, 0x87, 0xe2, 0xa9,

                0x1c, 0x99, 0xbc, 0x3a, 0x47, 0xac, 0xe4, 0x76,
                0x40, 0xcc, 0x95, 0xc3, 0x45, 0xbe, 0x5e, 0xcc,
                0xa5, 0xa3, 0x52, 0x3c, 0x35, 0xcc, 0x01, 0x89,
                0x3a, 0xf0, 0xb6, 0x4a, 0x62, 0x03, 0x34, 0x27,

                0x03, 0x72, 0xec, 0x12, 0x48, 0x2d, 0x1b, 0x1e,
                0x36, 0x35, 0x61, 0x69, 0x8a, 0x57, 0x8b, 0x35,
                0x98, 0x03, 0x49, 0x5b, 0xb4, 0xe2, 0xef, 0x19,
                0x30, 0xb1, 0x7a, 0x51, 0x90, 0xb5, 0x80, 0xf1,

                0x41, 0x30, 0x0d, 0xf3, 0x0a, 0xdb, 0xec, 0xa2,
                0x8f, 0x64, 0x27, 0xa8, 0xbc, 0x1a, 0x99, 0x9f,
                0xd5, 0x1c, 0x55, 0x4a, 0x01, 0x7d, 0x09, 0x5d,
                0x8c, 0x3e, 0x31, 0x27, 0xda, 0xf9, 0xf5, 0x95
            }
        },
        {
            32,
            {
                0x2d, 0x77, 0x3b, 0xe3, 0x7a, 0xdb, 0x1e, 0x4d,
                0x68, 0x3b, 0xf0, 0x07, 0x5e, 0x79, 0xc4, 0xee,
                0x03, 0x79, 0x18, 0x53, 0x5a, 0x7f, 0x99, 0xcc,
                0xb7, 0x04, 0x0f, 0xb5, 0xf5, 0xf4, 0x3a, 0xea
            }
        },
        {
            16,
            {
                0xc8, 0x5d, 0x15, 0xed, 0x44, 0xc3, 0x78, 0xd6,
                0xb0, 0x0e, 0x23, 0x06, 0x4c, 0x7b, 0xcd, 0x51
            }
        },
    },
    {
        {
            528,
            {
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b,
                0x17, 0x03, 0x03, 0x02, 0x00, 0x00, 0x00, 0x00,

                0x06, 0xdb, 0x1f, 0x1f, 0x36, 0x8d, 0x69, 0x6a,
                0x81, 0x0a, 0x34, 0x9c, 0x0c, 0x71, 0x4c, 0x9a,
                0x5e, 0x78, 0x50, 0xc2, 0x40, 0x7d, 0x72, 0x1a,
                0xcd, 0xed, 0x95, 0xe0, 0x18, 0xd7, 0xa8, 0x52,

                0x66, 0xa6, 0xe1, 0x28, 0x9c, 0xdb, 0x4a, 0xeb,
                0x18, 0xda, 0x5a, 0xc8, 0xa2, 0xb0, 0x02, 0x6d,
                0x24, 0xa5, 0x9a, 0xd4, 0x85, 0x22, 0x7f, 0x3e,
                0xae, 0xdb, 0xb2, 0xe7, 0xe3, 0x5e, 0x1c, 0x66,

                0xcd, 0x60, 0xf9, 0xab, 0xf7, 0x16, 0xdc, 0xc9,
                0xac, 0x42, 0x68, 0x2d, 0xd7, 0xda, 0xb2, 0x87,
                0xa7, 0x02, 0x4c, 0x4e, 0xef, 0xc3, 0x21, 0xcc,
                0x05, 0x74, 0xe1, 0x67, 0x93, 0xe3, 0x7c, 0xec,

                0x03, 0xc5, 0xbd, 0xa4, 0x2b, 0x54, 0xc1, 0x14,
                0xa8, 0x0b, 0x57, 0xaf, 0x26, 0x41, 0x6c, 0x7b,
                0xe7, 0x42, 0x00, 0x5e, 0x20, 0x85, 0x5c, 0x73,
                0xe2, 0x1d, 0xc8, 0xe2, 0xed, 0xc9, 0xd4, 0x35,

                0xcb, 0x6f, 0x60, 0x59, 0x28, 0x00, 0x11, 0xc2,
                0x70, 0xb7, 0x15, 0x70, 0x05, 0x1c, 0x1c, 0x9b,
                0x30, 0x52, 0x12, 0x66, 0x20, 0xbc, 0x1e, 0x27,
                0x30, 0xfa, 0x06, 0x6c, 0x7a, 0x50, 0x9d, 0x53,

                0xc6, 0x0e, 0x5a, 0xe1, 0xb4, 0x0a, 0xa6, 0xe3,
                0x9e, 0x49, 0x66, 0x92, 0x28, 0xc9, 0x0e, 0xec,
                0xb4, 0xa5, 0x0d, 0xb3, 0x2a, 0x50, 0xbc, 0x49,
                0xe9, 0x0b, 0x4f, 0x4b, 0x35, 0x9a, 0x1d, 0xfd,

                0x11, 0x74, 0x9c, 0xd3, 0x86, 0x7f, 0xcf, 0x2f,
                0xb7, 0xbb, 0x6c, 0xd4, 0x73, 0x8f, 0x6a, 0x4a,
                0xd6, 0xf7, 0xca, 0x50, 0x58, 0xf7, 0x61, 0x88,
                0x45, 0xaf, 0x9f, 0x02, 0x0f, 0x6c, 0x3b, 0x96,

                0x7b, 0x8f, 0x4c, 0xd4, 0xa9, 0x1e, 0x28, 0x13,
                0xb5, 0x07, 0xae, 0x66, 0xf2, 0xd3, 0x5c, 0x18,
                0x28, 0x4f, 0x72, 0x92, 0x18, 0x60, 0x62, 0xe1,
                0x0f, 0xd5, 0x51, 0x0d, 0x18, 0x77, 0x53, 0x51,

                0xef, 0x33, 0x4e, 0x76, 0x34, 0xab, 0x47, 0x43,
                0xf5, 0xb6, 0x8f, 0x49, 0xad, 0xca, 0xb3, 0x84,
                0xd3, 0xfd, 0x75, 0xf7, 0x39, 0x0f, 0x40, 0x06,
                0xef, 0x2a, 0x29, 0x5c, 0x8c, 0x7a, 0x07, 0x6a,

                0xd5, 0x45, 0x46, 0xcd, 0x25, 0xd2, 0x10, 0x7f,
                0xbe, 0x14, 0x36, 0xc8, 0x40, 0x92, 0x4a, 0xae,
                0xbe, 0x5b, 0x37, 0x08, 0x93, 0xcd, 0x63, 0xd1,
                0x32, 0x5b, 0x86, 0x16, 0xfc, 0x48, 0x10, 0x88,

                0x6b, 0xc1, 0x52, 0xc5, 0x32, 0x21, 0xb6, 0xdf,
                0x37, 0x31, 0x19, 0x39, 0x32, 0x55, 0xee, 0x72,
                0xbc, 0xaa, 0x88, 0x01, 0x74, 0xf1, 0x71, 0x7f,
                0x91, 0x84, 0xfa, 0x91, 0x64, 0x6f, 0x17, 0xa2,

                0x4a, 0xc5, 0x5d, 0x16, 0xbf, 0xdd, 0xca, 0x95,
                0x81, 0xa9, 0x2e, 0xda, 0x47, 0x92, 0x01, 0xf0,
                0xed, 0xbf, 0x63, 0x36, 0x00, 0xd6, 0x06, 0x6d,
                0x1a, 0xb3, 0x6d, 0x5d, 0x24, 0x15, 0xd7, 0x13,

                0x51, 0xbb, 0xcd, 0x60, 0x8a, 0x25, 0x10, 0x8d,
                0x25, 0x64, 0x19, 0x92, 0xc1, 0xf2, 0x6c, 0x53,
                0x1c, 0xf9, 0xf9, 0x02, 0x03, 0xbc, 0x4c, 0xc1,
                0x9f, 0x59, 0x27, 0xd8, 0x34, 0xb0, 0xa4, 0x71,

                0x16, 0xd3, 0x88, 0x4b, 0xbb, 0x16, 0x4b, 0x8e,
                0xc8, 0x83, 0xd1, 0xac, 0x83, 0x2e, 0x56, 0xb3,
                0x91, 0x8a, 0x98, 0x60, 0x1a, 0x08, 0xd1, 0x71,
                0x88, 0x15, 0x41, 0xd5, 0x94, 0xdb, 0x39, 0x9c,

                0x6a, 0xe6, 0x15, 0x12, 0x21, 0x74, 0x5a, 0xec,
                0x81, 0x4c, 0x45, 0xb0, 0xb0, 0x5b, 0x56, 0x54,
                0x36, 0xfd, 0x6f, 0x13, 0x7a, 0xa1, 0x0a, 0x0c,
                0x0b, 0x64, 0x37, 0x61, 0xdb, 0xd6, 0xf9, 0xa9,

                0xdc, 0xb9, 0x9b, 0x1a, 0x6e, 0x69, 0x08, 0x54,
                0xce, 0x07, 0x69, 0xcd, 0xe3, 0x97, 0x61, 0xd8,
                0x2f, 0xcd, 0xec, 0x15, 0xf0, 0xd9, 0x2d, 0x7d,
                0x8e, 0x94, 0xad, 0xe8, 0xeb, 0x83, 0xfb, 0xe0
            }
        },
        {
            32,
            {
                0x99, 0xe5, 0x82, 0x2d, 0xd4, 0x17, 0x3c, 0x99,
                0x5e, 0x3d, 0xae, 0x0d, 0xde, 0xfb, 0x97, 0x74,
                0x3f, 0xde, 0x3b, 0x08, 0x01, 0x34, 0xb3, 0x9f,
                0x76, 0xe9, 0xbf, 0x8d, 0x0e, 0x88, 0xd5, 0x46
            }
        },
        {
            16,
            {
                0x26, 0x37, 0x40, 0x8f, 0xe1, 0x30, 0x86, 0xea,
                0x73, 0xf9, 0x71, 0xe3, 0x42, 0x5e, 0x28, 0x20
            }
        },
    },
    /*
     * test vectors from Hanno Böck
     */
    {
        {
            257,
            {
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0x80, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,

                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xce, 0xcc, 0xcc, 0xcc,

                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc5,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,

                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xe3, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,

                0xcc, 0xcc, 0xcc, 0xcc, 0xac, 0xcc, 0xcc, 0xcc,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xe6,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x00, 0x00, 0x00,
                0xaf, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,

                0xcc, 0xcc, 0xff, 0xff, 0xff, 0xf5, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0xff, 0xff, 0xff, 0xe7, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x71, 0x92, 0x05, 0xa8, 0x52, 0x1d,

                0xfc
            }
        },
        {
            32,
            {
                0x7f, 0x1b, 0x02, 0x64, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc
            }
        },
        {
            16,
            {
                0x85, 0x59, 0xb8, 0x76, 0xec, 0xee, 0xd6, 0x6e,
                0xb3, 0x77, 0x98, 0xc0, 0x45, 0x7b, 0xaf, 0xf9
            }
        },
    },
    {
        {
            39,
            {
                0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
                0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
                0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
                0xaa, 0xaa, 0xaa, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x80, 0x02, 0x64
            }
        },
        {
            32,
            {
                0xe0, 0x00, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
                0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa
            }
        },
        {
            16,
            {
                0x00, 0xbd, 0x12, 0x58, 0x97, 0x8e, 0x20, 0x54,
                0x44, 0xc9, 0xaa, 0xaa, 0x82, 0x00, 0x6f, 0xed
            }
        },
    },
    {
        {
            2,
            {
                0x02, 0xfc
            }
        },
        {
            32,
            {
                0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
                0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
                0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
                0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c
            }
        },
        {
            16,
            {
                0x06, 0x12, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c,
                0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c, 0x0c
            }
        },
    },
    {
        {
            415,
            {
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7a, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x5c, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x6e, 0x7b, 0x00, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7a, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x5c,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
                0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,

                0x7b, 0x6e, 0x7b, 0x00, 0x13, 0x00, 0x00, 0x00,
                0x00, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0xf2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x20, 0x00, 0xef, 0xff, 0x00,
                0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00,
                0x00, 0x00, 0x09, 0x00, 0x00, 0x00, 0x64, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x00,

                0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf2,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x20, 0x00, 0xef, 0xff, 0x00, 0x09,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x7a, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,

                0x00, 0x09, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfc
            }
        },
        {
            32,
            {
                0x00, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x1e, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x7b
            }
        },
        {
            16,
            {
                0x33, 0x20, 0x5b, 0xbf, 0x9e, 0x9f, 0x8f, 0x72,
                0x12, 0xab, 0x9e, 0x2a, 0xb9, 0xb7, 0xe4, 0xa5
            }
        },
    },
    {
        {
            118,
            {
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,

                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,

                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0xff, 0xff, 0xff, 0xe9,
                0xe9, 0xac, 0xac, 0xac, 0xac, 0xac, 0xac, 0xac,
                0xac, 0xac, 0xac, 0xac, 0x00, 0x00, 0xac, 0xac,

                0xec, 0x01, 0x00, 0xac, 0xac, 0xac, 0x2c, 0xac,
                0xa2, 0xac, 0xac, 0xac, 0xac, 0xac, 0xac, 0xac,
                0xac, 0xac, 0xac, 0xac, 0x64, 0xf2
            }
        },
        {
            32,
            {
                0x00, 0x00, 0x00, 0x7f, 0x00, 0x00, 0x00, 0x7f,
                0x01, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0xcf, 0x77, 0x77, 0x77, 0x77, 0x77,
                0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77
            }
        },
        {
            16,
            {
                0x02, 0xee, 0x7c, 0x8c, 0x54, 0x6d, 0xde, 0xb1,
                0xa4, 0x67, 0xe4, 0xc3, 0x98, 0x11, 0x58, 0xb9
            }
        },
    },
    /*
     * test vectors from Andrew Moon
     */
    { /* nacl */
        {
            131,
            {
                0x8e, 0x99, 0x3b, 0x9f, 0x48, 0x68, 0x12, 0x73,
                0xc2, 0x96, 0x50, 0xba, 0x32, 0xfc, 0x76, 0xce,
                0x48, 0x33, 0x2e, 0xa7, 0x16, 0x4d, 0x96, 0xa4,
                0x47, 0x6f, 0xb8, 0xc5, 0x31, 0xa1, 0x18, 0x6a,

                0xc0, 0xdf, 0xc1, 0x7c, 0x98, 0xdc, 0xe8, 0x7b,
                0x4d, 0xa7, 0xf0, 0x11, 0xec, 0x48, 0xc9, 0x72,
                0x71, 0xd2, 0xc2, 0x0f, 0x9b, 0x92, 0x8f, 0xe2,
                0x27, 0x0d, 0x6f, 0xb8, 0x63, 0xd5, 0x17, 0x38,

                0xb4, 0x8e, 0xee, 0xe3, 0x14, 0xa7, 0xcc, 0x8a,
                0xb9, 0x32, 0x16, 0x45, 0x48, 0xe5, 0x26, 0xae,
                0x90, 0x22, 0x43, 0x68, 0x51, 0x7a, 0xcf, 0xea,
                0xbd, 0x6b, 0xb3, 0x73, 0x2b, 0xc0, 0xe9, 0xda,

                0x99, 0x83, 0x2b, 0x61, 0xca, 0x01, 0xb6, 0xde,
                0x56, 0x24, 0x4a, 0x9e, 0x88, 0xd5, 0xf9, 0xb3,
                0x79, 0x73, 0xf6, 0x22, 0xa4, 0x3d, 0x14, 0xa6,
                0x59, 0x9b, 0x1f, 0x65, 0x4c, 0xb4, 0x5a, 0x74,

                0xe3, 0x55, 0xa5
            }
        },
        {
            32,
            {
                0xee, 0xa6, 0xa7, 0x25, 0x1c, 0x1e, 0x72, 0x91,
                0x6d, 0x11, 0xc2, 0xcb, 0x21, 0x4d, 0x3c, 0x25,
                0x25, 0x39, 0x12, 0x1d, 0x8e, 0x23, 0x4e, 0x65,
                0x2d, 0x65, 0x1f, 0xa4, 0xc8, 0xcf, 0xf8, 0x80
            }
        },
        {
            16,
            {
                0xf3, 0xff, 0xc7, 0x70, 0x3f, 0x94, 0x00, 0xe5,
                0x2a, 0x7d, 0xfb, 0x4b, 0x3d, 0x33, 0x05, 0xd9
            }
        },
    },
    { /* wrap 2^130-5 */
        {
            16,
            {
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
            }
        },
        {
            32,
            {
                0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
    },
    { /* wrap 2^128 */
        {
            16,
            {
                0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            32,
            {
                0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
            }
        },
        {
            16,
            {
                0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
    },
    { /* limb carry */
        {
            48,
            {
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

                0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            32,
            {
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
    },
    { /* 2^130-5 */
        {
            48,
            {
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xfb, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe,
                0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe,

                0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
                0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01
            }
        },
        {
            32,
            {
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

            }
        },
    },
    { /* 2^130-6 */
        {
            16,
            {
                0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
            }
        },
        {
            32,
            {
                0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
                0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
            }
        },
    },
    { /* 5*H+L reduction intermediate */
        {
            64,
            {
                0xe3, 0x35, 0x94, 0xd7, 0x50, 0x5e, 0x43, 0xb9,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x33, 0x94, 0xd7, 0x50, 0x5e, 0x43, 0x79, 0xcd,
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            32,
            {
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
    },
    { /* 5*H+L reduction final */
        {
            48,
            {
                0xe3, 0x35, 0x94, 0xd7, 0x50, 0x5e, 0x43, 0xb9,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x33, 0x94, 0xd7, 0x50, 0x5e, 0x43, 0x79, 0xcd,
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00

            }
        },
        {
            32,
            {
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        },
        {
            16,
            {
                0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            }
        }
    }
};

static int test_poly1305(int idx)
{
    POLY1305 poly1305;
    const TESTDATA test = tests[idx];
    const unsigned char *in = test.input.data;
    size_t inlen = test.input.size;
    const unsigned char *key = test.key.data;
    const unsigned char *expected = test.expected.data;
    size_t expectedlen = test.expected.size;
    unsigned char out[16];

    if (!TEST_size_t_eq(expectedlen, sizeof(out)))
        return 0;

    Poly1305_Init(&poly1305, key);
    Poly1305_Update(&poly1305, in, inlen);
    Poly1305_Final(&poly1305, out);

    if (!TEST_mem_eq(out, expectedlen, expected, expectedlen)) {
        TEST_info("Poly1305 test #%d failed.", idx);
        return 0;
    }

    if (inlen > 16) {
        Poly1305_Init(&poly1305, key);
        Poly1305_Update(&poly1305, in, 1);
        Poly1305_Update(&poly1305, in+1, inlen-1);
        Poly1305_Final(&poly1305, out);

        if (!TEST_mem_eq(out, expectedlen, expected, expectedlen)) {
            TEST_info("Poly1305 test #%d/1+(N-1) failed.", idx);
            return 0;
        }
    }

    if (inlen > 32) {
        size_t half = inlen / 2;

        Poly1305_Init(&poly1305, key);
        Poly1305_Update(&poly1305, in, half);
        Poly1305_Update(&poly1305, in+half, inlen-half);
        Poly1305_Final(&poly1305, out);

        if (!TEST_mem_eq(out, expectedlen, expected, expectedlen)) {
            TEST_info("Poly1305 test #%d/2 failed.", idx);
            return 0;
        }

        for (half = 16; half < inlen; half += 16) {
            Poly1305_Init(&poly1305, key);
            Poly1305_Update(&poly1305, in, half);
            Poly1305_Update(&poly1305, in+half, inlen-half);
            Poly1305_Final(&poly1305, out);

            if (!TEST_mem_eq(out, expectedlen, expected, expectedlen)) {
                TEST_info("Poly1305 test #%d/%zu+%zu failed.",
                          idx, half, inlen-half);
                return 0;
            }
        }
    }

    return 1;
}

int setup_tests(void)
{
    ADD_ALL_TESTS(test_poly1305, OSSL_NELEM(tests));
    return 1;
}
