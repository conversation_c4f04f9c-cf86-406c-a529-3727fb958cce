=pod

=head1 NAME

EVP_PKEY_CTX_get0_libctx,
EVP_PKEY_CTX_get0_propq,
EVP_PKEY_CTX_get0_provider
- functions for getting diverse information from an EVP_PKEY_CTX

=head1 SYNOPSIS

 #include <openssl/evp.h>

 OSSL_LIB_CTX *EVP_PKEY_CTX_get0_libctx(EVP_PKEY_CTX *ctx);
 const char *EVP_PKEY_CTX_get0_propq(const EVP_PKEY_CTX *ctx);
 const OSSL_PROVIDER *EVP_PKEY_CTX_get0_provider(const EVP_PKEY_CTX *ctx);

=head1 DESCRIPTION

EVP_PKEY_CTX_get0_libctx() and EVP_PKEY_CTX_get0_propq() obtain the
OSSL_LIB_CTX and property query string values respectively that were
associated with the EVP_PKEY_CTX when it was constructed.

EVP_PKEY_CTX_get0_provider() returns the provider associated with the
ongoing B<EVP_PKEY_CTX> operation.  If the operation is performed by
en B<ENGINE>, this function returns NULL.

=head1 RETURN VALUES

EVP_PKEY_CTX_get0_libctx() and EVP_PKEY_CTX_get0_propq() functions return the
OSSL_LIB_CTX and property query string associated with the EVP_PKEY_CTX or NULL
if they are not set. The returned values should not be freed by the caller.

EVP_PKEY_CTX_get0_provider() returns a provider if an operation performed by
a provider is ongoing, otherwise NULL.

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>

=head1 HISTORY

All functions were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
