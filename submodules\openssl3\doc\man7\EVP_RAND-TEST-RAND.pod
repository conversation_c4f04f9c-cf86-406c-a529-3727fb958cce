=pod

=head1 NAME

EVP_RAND-TEST-RAND - The test EVP_RAND implementation

=head1 DESCRIPTION

Support for a test generator through the B<EVP_RAND> API. This generator is
for test purposes only, it does not generate random numbers.

=head2 Identity

"TEST-RAND" is the name for this implementation; it can be used with the
EVP_RAND_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "state" (B<OSSL_RAND_PARAM_STATE>) <integer>

These parameter works as described in L<EVP_RAND(3)/PARAMETERS>.

=item "strength" (B<OSSL_RAND_PARAM_STRENGTH>) <unsigned integer>

=item "reseed_requests" (B<OSSL_DRBG_PARAM_RESEED_REQUESTS>) <unsigned integer>

=item "reseed_time_interval" (B<OSSL_DRBG_PARAM_RESEED_TIME_INTERVAL>) <integer>

=item "max_request" (B<OSSL_DRBG_PARAM_RESEED_REQUESTS>) <unsigned integer>

=item "min_entropylen" (B<OSSL_DRBG_PARAM_MIN_ENTROPYLEN>) <unsigned integer>

=item "max_entropylen" (B<OSSL_DRBG_PARAM_MAX_ENTROPYLEN>) <unsigned integer>

=item "min_noncelen" (B<OSSL_DRBG_PARAM_MIN_NONCELEN>) <unsigned integer>

=item "max_noncelen" (B<OSSL_DRBG_PARAM_MAX_NONCELEN>) <unsigned integer>

=item "max_perslen" (B<OSSL_DRBG_PARAM_MAX_PERSLEN>) <unsigned integer>

=item "max_adinlen" (B<OSSL_DRBG_PARAM_MAX_ADINLEN>) <unsigned integer>

=item "reseed_counter" (B<OSSL_DRBG_PARAM_RESEED_COUNTER>) <unsigned integer>

These parameters work as described in L<EVP_RAND(3)/PARAMETERS>, except that
they can all be set as well as read.

=item "test_entropy" (B<OSSL_RAND_PARAM_TEST_ENTROPY>) <octet string>

Sets the bytes returned when the test generator is sent an entropy request.
The current position is remembered across generate calls.
If there are insufficient data present to satisfy a call, an error is returned.

=item "test_nonce" (B<OSSL_RAND_PARAM_TEST_NONCE>) <octet string>

Sets the bytes returned when the test generator is sent a nonce request.
Each nonce request will return all of the bytes.

=item "generate" (B<OSSL_RAND_PARAM_GENERATE>) <integer>

If this parameter is zero, it will only emit the nonce and entropy data
supplied via the aforementioned parameters.  Otherwise, low quality
non-cryptographic pseudorandom output is produced.  This parameter defaults
to zero.

=back

=head1 NOTES

A context for a test generator can be obtained by calling:

 EVP_RAND *rand = EVP_RAND_fetch(NULL, "TEST-RAND", NULL);
 EVP_RAND_CTX *rctx = EVP_RAND_CTX_new(rand);

=head1 EXAMPLES

 EVP_RAND *rand;
 EVP_RAND_CTX *rctx;
 unsigned char bytes[100];
 OSSL_PARAM params[4], *p = params;
 unsigned char entropy[1000] = { ... };
 unsigned char nonce[20] = { ... };
 unsigned int strength = 48;

 rand = EVP_RAND_fetch(NULL, "TEST-RAND", NULL);
 rctx = EVP_RAND_CTX_new(rand, NULL);
 EVP_RAND_free(rand);

 *p++ = OSSL_PARAM_construct_uint(OSSL_RAND_PARAM_STRENGTH, &strength);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_RAND_PARAM_TEST_ENTROPY,
                                          entropy, sizeof(entropy));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_RAND_PARAM_TEST_NONCE,
                                          nonce, sizeof(nonce));
 *p = OSSL_PARAM_construct_end();
 EVP_RAND_instantiate(rctx, strength, 0, NULL, 0, params);

 EVP_RAND_generate(rctx, bytes, sizeof(bytes), strength, 0, NULL, 0);

 EVP_RAND_CTX_free(rctx);

=head1 SEE ALSO

L<EVP_RAND(3)>,
L<EVP_RAND(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
