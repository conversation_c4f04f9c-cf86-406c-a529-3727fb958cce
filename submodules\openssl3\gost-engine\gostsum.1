.\" Hey, Emacs!  This is an -*- nroff -*- source file.
.TH GOSTSUM 1 "02 Aug 2017" "Openssl" "Debian GNU/Linux"
.SH NAME
gostsum \- generates or checks GOST R34.11-94 message digests

.SH SYNOPSIS
.B gostsum
[\-bvt] [\-c [file]] | [file...]

.SH DESCRIPTION
.B gostsum
generates or checks GOST hash sums. The algorithm to generate the
is reasonably fast and strong enough for most cases. Exact
specification of the algorithm is in
.I GOST R34.11-94.

Normally
.B gostsum
generates checksums of all files given to it as a parameter and prints
the checksums followed by the filenames. If, however,
.B \-c
is specified, only one filename parameter is allowed. This file should
contain checksums and filenames to which these checksums refer to, and
the files listed in that file are checked against the checksums listed
there. See option
.B \-c
for more information.

.SS OPTIONS
.TP
.B \-b
Use binary mode. In unix environment, only difference between this and
the normal mode is an asterisk preceding the filename in the output.
.TP
.B \-c
Check gost hashes of all files listed in
.I file
against the checksum listed in the same file. The actual format of that
file is the same as output of
.B md5sum.
That is, each line in the file describes a file. A line looks like:

.B <hashsum>  <filename>

So, for example, if a file was created and its message digest calculated
like so:

.B echo foo > hash\-test\-file; gostsum hash\-test\-file

.B gostsum
would report:

1541e09d0aa5971f732991ae1bdfb63f2609edd7536b40f8c2ae7c1e2f99e072 hash-test-file

.TP
.B \-v
Be more verbose. Print filenames when checking (with \-c).

.TP
.B -t 
Use test parameter set. 
.B gostsum supports two sets of parameters (which are really parameters
of GOST 28147-89 block cipher) specified in the IETF draft 
.B draft-popov-cryptopro-cpalgs-02.txt
By default, cryptopro paramset is used. This option enables use of test
paramset as specified in appendices to the GOST.

.SH CAVEATS

The output of gost12sum has a reversed byte order compared to output of 
.B openssl dgst
command because of the Russian GOST requrements. 

.SH BUGS

This manpage is not quite accurate and has formatting inconsistent
with other manpages.

.B gostsum
does not accept standard options like
.BR \-\-help .

.SH AUTHOR
 	Victor Wagner <<EMAIL>>

