<?xml version="1.0" encoding="UTF-8"?>
<XPD:PROJECT xmlns:XPD="http://www.staruml.com" version="1">
<XPD:HEADER>
<XPD:SUBUNITS>
</XPD:SUBUNITS>
<XPD:PROFILES>
<XPD:PROFILE>UMLStandard</XPD:PROFILE>
</XPD:PROFILES>
</XPD:HEADER>
<XPD:BODY>
<XPD:OBJ name="DocumentElement" type="UMLProject" guid="eMTM5siBPkmesgn1ExGJ4wAA">
<XPD:ATTR name="Title" type="string">Untitled</XPD:ATTR>
<XPD:ATTR name="#OwnedElements" type="integer">5</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLModel" guid="Y8LpwWcNukWtaRfUBI5ZLgAA">
<XPD:ATTR name="Name" type="string">Use Case Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">useCaseModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLUseCaseDiagram" guid="01F2MJANekqll84kbF5YNQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">Y8LpwWcNukWtaRfUBI5ZLgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLUseCaseDiagramView" guid="Wa78Fn/Bb0SrcCepzNe+hwAA">
<XPD:REF name="Diagram">01F2MJANekqll84kbF5YNQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLModel" guid="IFqLwgQrJUOrRvknlfHP7gAA">
<XPD:ATTR name="Name" type="string">Analysis Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">analysisModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="qf/BpIoQNE2iR+jW/1YKGQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:ATTR name="DiagramType" type="string">RobustnessDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="Nj8POkxtUk2Ybm9uGd6QNwAA">
<XPD:REF name="Diagram">qf/BpIoQNE2iR+jW/1YKGQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedCollaborationInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedCollaborationInstanceSets[0]" type="UMLCollaborationInstanceSet" guid="RJjbkGXgxkyHu7/7jQk7WgAA">
<XPD:ATTR name="Name" type="string">CollaborationInstanceSet1</XPD:ATTR>
<XPD:REF name="RepresentedClassifier">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:ATTR name="#InteractionInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="InteractionInstanceSets[0]" type="UMLInteractionInstanceSet" guid="z5Jdy9Y9AkuwxNfYTGq/jgAA">
<XPD:ATTR name="Name" type="string">InteractionInstanceSet1</XPD:ATTR>
<XPD:REF name="Context">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLSequenceDiagram" guid="PrarycmgpEmAgPsPTXgTdAAA">
<XPD:ATTR name="Name" type="string">SequenceDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLSequenceDiagramView" guid="o16wrrjNA0qa+l2Fh/S4lgAA">
<XPD:REF name="Diagram">PrarycmgpEmAgPsPTXgTdAAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">36</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLSeqObjectView" guid="hPmViqUBXkyp20XJo5hMdgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">164</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1283</XPD:ATTR>
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="fAggghDBvEq/ulCEeW7p1wAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="J8Tym9syak6jMb998m2XnAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">app</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="7tw+uaP9y0KlpsxVOPDIWwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="L08Ynzs4RUePB4dOSv8aiQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="+2RX1PWkxkmtGMjAQcMkiwAA">
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[1]" type="UMLSeqObjectView" guid="QJ/ol6Ii20Cm5p+rCS0bnQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">408</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">124</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1315</XPD:ATTR>
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="T6OBOo60Q0W4cqZXEbC1RgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="pY6xEFcwOE+9xyDGKflCfAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">library</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="RGPC07sgskGBwg2p/ygfqwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="ktdSB1Q4R0mcUr92wegLdAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="PpgPcPMDNk2aqxVrk8rFfAAA">
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[2]" type="UMLSeqObjectView" guid="lDUJf8C6fEyt4j0+bqluBgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">636</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">44</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1319</XPD:ATTR>
<XPD:REF name="Model">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="8iotUIgixE62ZDVmD3Z3fQAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="Kb2lMHEsEk2Cuq5svpZgwQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">api</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="aEV4eOOZU0mHkw75pExuOgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="uGzZliJVtkG5NM6OsR+hEQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="ZEuU64rQXku57sf2f119/wAA">
<XPD:REF name="Model">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[3]" type="UMLSeqObjectView" guid="PnRdfgtnekueMRegs48acQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">844</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">44</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">102</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1277</XPD:ATTR>
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="wa79nq7jfkyCxJ9WKgFXjgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="ADa8z/SUOUquP1daSz7gqQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">platform</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kJ/gDpz3j0ee3eR61+pEaQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="OMFhMMF220KlJGqxz1ec2QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="utuP5e+xFEmMGXpPHYF8ogAA">
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[4]" type="UMLSeqObjectView" guid="33q5j2m07U66Vqancw44YQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">1048</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">75</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1269</XPD:ATTR>
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="3uXfUgftWE2YaJbZSLhjRwAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="6hCBIMsdDkKV2jSutLPPxQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">connection</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kyEWL0SI2EqTC4V3i20LggAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="qr6WtEdGD0erGJ2d4gw6swAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="5qlq1+nrd0+/KejphUw97gAA">
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[5]" type="UMLSeqObjectView" guid="fXwfWC6VWUCx60dQStWXbAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">1228</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1261</XPD:ATTR>
<XPD:REF name="Model">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="tK+HiDGuYU610fSe5xLTAAAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="67zticUUMEahTtNqcUMQAQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">stream</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="sbpjix6v7U+p1vB2gjnkkQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="ChfAsgiXcUyt2ImyO+zFvwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="piukC9S9LEWvDJjQgjtGjwAA">
<XPD:REF name="Model">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[6]" type="UMLSeqStimulusView" guid="z/BF72dr4kuVb3dOKjH8ZgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,106;463,106</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="mJgFFVIbl0Kb4O4Re+5yUAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">1 : MsQuicOpen2()</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="GbIXvH8SkUqsZolSq95kowAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="S90I5wQEs0aBXCatMG/TkwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="S9pKVUTolkyAH+KCx8eGtQAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">106</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[7]" type="UMLSeqStimulusView" guid="rN8XWYPv70i0Sd6rzg7/zAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,156;500,156;500,176;476,176</XPD:ATTR>
<XPD:REF name="Model">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="A9pm7fJ6ZEmazIw8AvCHcQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">2 : MsQuicLibraryLoad()</XPD:ATTR>
<XPD:REF name="Model">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="HostEdge">rN8XWYPv70i0Sd6rzg7/zAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="P6niAFdvYUSUTbiFE+iqfAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="HostEdge">rN8XWYPv70i0Sd6rzg7/zAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="5BYjToYYrEe4hCzV686GKgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="HostEdge">rN8XWYPv70i0Sd6rzg7/zAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="oTBwEuNd502iFOzP3TggdAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">176</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[8]" type="UMLSeqStimulusView" guid="1Jl03a5a6kyA0UsQhVv2jwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,214;888,214</XPD:ATTR>
<XPD:REF name="Model">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="HKU5J8Yp0UiNpZWHVrCz3gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">3 : CxPlatSystemLoad()</XPD:ATTR>
<XPD:REF name="Model">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="HostEdge">1Jl03a5a6kyA0UsQhVv2jwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="awoLiX7T/ESZPQmdpDXBgAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="HostEdge">1Jl03a5a6kyA0UsQhVv2jwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="9L7B+GJwbEO0tRn51ipWfgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="HostEdge">1Jl03a5a6kyA0UsQhVv2jwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="cqt73oLExUCK0V+v9xV7CQAA">
<XPD:ATTR name="Left" type="integer">888</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">214</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[9]" type="UMLSeqStimulusView" guid="C8OLOXCEwE+He7magad5AQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,255;500,255;500,275;476,275</XPD:ATTR>
<XPD:REF name="Model">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="Hn8o00z/9Ue5v30Cg+dUuQAA">
<XPD:ATTR name="Alpha" type="real">2.52529443873713</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">29.4108823397055</XPD:ATTR>
<XPD:ATTR name="Text" type="string">4 : MsQuicLibraryInitialize()</XPD:ATTR>
<XPD:REF name="Model">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="HostEdge">C8OLOXCEwE+He7magad5AQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="QLPIJmfj70eMUq9D4c9pDAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="HostEdge">C8OLOXCEwE+He7magad5AQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="TsagCSwiJEW5FVfMuvgLjgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="HostEdge">C8OLOXCEwE+He7magad5AQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="BONDFCUtjECWNS6dFS4lHQAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">275</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[10]" type="UMLSeqStimulusView" guid="Uu1VOlE9NEeSzZkZAT6mjAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,318;888,318</XPD:ATTR>
<XPD:REF name="Model">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="rkQ+/JwtWkayS4yjdWxKaAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">5 : CxPlatInitialize()</XPD:ATTR>
<XPD:REF name="Model">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="HostEdge">Uu1VOlE9NEeSzZkZAT6mjAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="cjdKav2E+UOFxBqU3vT3iwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="HostEdge">Uu1VOlE9NEeSzZkZAT6mjAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="AllgNduFYkabQGWMjzbj2wAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="HostEdge">Uu1VOlE9NEeSzZkZAT6mjAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="0rHUjf1Q8EqgXT/z5+Oe7QAA">
<XPD:ATTR name="Left" type="integer">888</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">318</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[11]" type="UMLSeqStimulusView" guid="wDVcYA9+x0WLoJPnxPXPkgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,348;888,348</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="dD9eiSncVEmuhy4t5tJ6KwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">6 : CxPlatWorkerPoolInit()</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="kVlo/+eDg02oTd+nY1yxqAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="BslqtKAHZUmD1gQXEPMWCAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="ECuN9eY+ukGrE26n49Dw3gAA">
<XPD:ATTR name="Left" type="integer">888</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">348</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[12]" type="UMLSeqStimulusView" guid="+DDnP53C/EGSEtA1lhbXDwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,382;463,382</XPD:ATTR>
<XPD:REF name="Model">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="PyRReHMtGEi321H+4j7aPwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">7 : MsQuic-&gt;RegistrationOpen()</XPD:ATTR>
<XPD:REF name="Model">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="HostEdge">+DDnP53C/EGSEtA1lhbXDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="wdTukg1/dUOgU+fblfFgGQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="HostEdge">+DDnP53C/EGSEtA1lhbXDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="21pX0IA8L0CSaiCM8hGJMAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="HostEdge">+DDnP53C/EGSEtA1lhbXDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="UzmbmFQSsUqMGOoh8piU5wAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">382</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[13]" type="UMLSeqStimulusView" guid="12W+IlObr068giGRWrqqXQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,424;463,424</XPD:ATTR>
<XPD:REF name="Model">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="hE4p+aYq6kaRcqF5OmU4oQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">8 : MsQuic-&gt;ConfigurationOpen()</XPD:ATTR>
<XPD:REF name="Model">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="HostEdge">12W+IlObr068giGRWrqqXQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="YQcXZS6BLUeA7nfjMKQMnQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="HostEdge">12W+IlObr068giGRWrqqXQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="5Gzv8mTaT0e3q7hFK6eQtQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="HostEdge">12W+IlObr068giGRWrqqXQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="nLjR1ixozk+OKMcRLa47xgAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">424</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[14]" type="UMLSeqStimulusView" guid="Jd1Xgs/HjU+YxuhHsj1MbAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,459;463,459</XPD:ATTR>
<XPD:REF name="Model">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="z4JgpRq/xkKsHEzeQOntxQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">9 : MsQuic-&gt;ConfigurationLoadCredential()</XPD:ATTR>
<XPD:REF name="Model">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="HostEdge">Jd1Xgs/HjU+YxuhHsj1MbAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="zXEK+vrZYUOb6enUuhJFRAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="HostEdge">Jd1Xgs/HjU+YxuhHsj1MbAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="W1YS1JAj00mH2rMIoyysKAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="HostEdge">Jd1Xgs/HjU+YxuhHsj1MbAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="7LDzVycBHE24Sja3RKzbAwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">459</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[15]" type="UMLSeqStimulusView" guid="wFiPEtIzaUOW0qx3Zcq3MAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,500;463,500</XPD:ATTR>
<XPD:REF name="Model">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="wb36RycXqU+DT7BjT0Av9wAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">10 : MsQuic-&gt;ListenerOpen:ServerListenerCallback()</XPD:ATTR>
<XPD:REF name="Model">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="HostEdge">wFiPEtIzaUOW0qx3Zcq3MAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="E9az49HrbUehjR677P+rTAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="HostEdge">wFiPEtIzaUOW0qx3Zcq3MAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="IbStsFyVW0iLSNCYm12OtgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="HostEdge">wFiPEtIzaUOW0qx3Zcq3MAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="5XFd2lvjzEmBAYqWWm/XgwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">500</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[16]" type="UMLSeqStimulusView" guid="AqitT6/q2EGKiMJXU62MZwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,537;463,537</XPD:ATTR>
<XPD:REF name="Model">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="12cq7S9w3Eu2f7xCFKIYMgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">11 : MsQuic-&gt;ListenerStart()</XPD:ATTR>
<XPD:REF name="Model">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="HostEdge">AqitT6/q2EGKiMJXU62MZwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="auGtW9ZoN0aJv+as53N6jgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="HostEdge">AqitT6/q2EGKiMJXU62MZwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="N87g0nMM9UiOM35PjUn2ZQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="HostEdge">AqitT6/q2EGKiMJXU62MZwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="IpVHHem4d0qWqgbw6beV0AAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">537</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[17]" type="UMLSeqStimulusView" guid="qWtBHJ2CeEmyXwIfnw5fjQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1085,584;1115,584;1115,604;1091,604</XPD:ATTR>
<XPD:REF name="Model">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="bSunKAHhA0mSEdWDlIkavAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">12 : QuicConnIndicateEvent()</XPD:ATTR>
<XPD:REF name="Model">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="HostEdge">qWtBHJ2CeEmyXwIfnw5fjQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="Iy3+n80qrE2rGwetSDnr0AAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="HostEdge">qWtBHJ2CeEmyXwIfnw5fjQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="adqifB4BvUaCPuHtHDzrYAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="HostEdge">qWtBHJ2CeEmyXwIfnw5fjQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="EwM5vXyqUkye9f3oFvwkjAAA">
<XPD:ATTR name="Left" type="integer">1078</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">604</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[18]" type="UMLSeqStimulusView" guid="kiyt/b70Z0OApMHqQFplzAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1085,647;205,647</XPD:ATTR>
<XPD:REF name="Model">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="T2Hn8raCaUWRashrpXcfuwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">13 : ServerConnectionCallback:QUIC_CONNECTION_EVENT_CONNECTED()</XPD:ATTR>
<XPD:REF name="Model">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="HostEdge">kiyt/b70Z0OApMHqQFplzAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="RyWp75p6PUycUfKqzImjiQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="HostEdge">kiyt/b70Z0OApMHqQFplzAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="argulqMgUUuVOjoqFKgeZwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="HostEdge">kiyt/b70Z0OApMHqQFplzAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="IP0hsmIl6Eiyc+OPevLz2gAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">647</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[19]" type="UMLSeqStimulusView" guid="FBwU0JXFqEa4rrcY4xGihAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,685;664,685</XPD:ATTR>
<XPD:REF name="Model">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="82Mh2mGGTUKVuODfnhSKGwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">14 : MsQuicConnectionSendResumptionTicket()</XPD:ATTR>
<XPD:REF name="Model">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="HostEdge">FBwU0JXFqEa4rrcY4xGihAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="sSDHt1ugu0WPXaRIiFWJqAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="HostEdge">FBwU0JXFqEa4rrcY4xGihAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="rQEss86vMk6Lp7tbyC31RAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="HostEdge">FBwU0JXFqEa4rrcY4xGihAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="/9GME+sSRUuSq9S9Yt+wxQAA">
<XPD:ATTR name="Left" type="integer">664</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">685</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[20]" type="UMLSeqStimulusView" guid="Rj9X+dy+wkinY9/M2z7ALQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1085,729;205,729</XPD:ATTR>
<XPD:REF name="Model">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="paDQ3qXaz0Kip0eGmIs+HwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">15 : ServerConnectionCallback:QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED()</XPD:ATTR>
<XPD:REF name="Model">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="HostEdge">Rj9X+dy+wkinY9/M2z7ALQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="6OwUxz8aTESJLG/2pqsBYAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="HostEdge">Rj9X+dy+wkinY9/M2z7ALQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="bb0yHmAFUkKmpvRb4AiHWAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="HostEdge">Rj9X+dy+wkinY9/M2z7ALQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="xvClPOvGpEqL77f++cB6VwAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">729</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[21]" type="UMLSeqStimulusView" guid="agnLR+f0hUqHh/DnAv99DwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,785;463,785</XPD:ATTR>
<XPD:REF name="Model">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="4qeAwDj39Em7NX2RDdgBqgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">16 : MsQuic-&gt;SetCallbackHandler:ServerStreamCallback()</XPD:ATTR>
<XPD:REF name="Model">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="HostEdge">agnLR+f0hUqHh/DnAv99DwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="1BHe/lCkn0i8ejwIqf/ymwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="HostEdge">agnLR+f0hUqHh/DnAv99DwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="AFdAUeiQq0mVNcmKd3ydfgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="HostEdge">agnLR+f0hUqHh/DnAv99DwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="PPAZxZj/kEuoxyFaOCwy1gAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">785</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[22]" type="UMLSeqStimulusView" guid="nc7eqj65WUS6Oz4tfhyjqgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1263,825;1293,825;1293,845;1269,845</XPD:ATTR>
<XPD:REF name="Model">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="Head">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:REF name="Tail">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="M6g1SYlMV0ec1l0gPpVcyQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">17 : QuicStreamIndicateEvent()</XPD:ATTR>
<XPD:REF name="Model">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="HostEdge">nc7eqj65WUS6Oz4tfhyjqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="5XaLlE2l0keALWVcBl+xPgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="HostEdge">nc7eqj65WUS6Oz4tfhyjqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="1AnxmDkLUk2ZdW3EyJCeMgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="HostEdge">nc7eqj65WUS6Oz4tfhyjqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="RrzeBBVPE0ywjN7/X/m8rwAA">
<XPD:ATTR name="Left" type="integer">1256</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">845</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[23]" type="UMLSeqStimulusView" guid="fQmVpu3Y/E+LAVAFUhcD/wAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1263,884;205,884</XPD:ATTR>
<XPD:REF name="Model">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="4NIfJL+u7UOiBpcyIIISOwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">18 : ServerStreamCallbak:QUIC_STREAM_EVENT_RECEIVE()</XPD:ATTR>
<XPD:REF name="Model">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="HostEdge">fQmVpu3Y/E+LAVAFUhcD/wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="jmsI0PXMaEagYTH24Q3eEQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="HostEdge">fQmVpu3Y/E+LAVAFUhcD/wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="7vS+EF+bvEuEG7172LHQKQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="HostEdge">fQmVpu3Y/E+LAVAFUhcD/wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="0ZkRwqCv7k+KhfT6TTvkdQAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">884</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[24]" type="UMLSeqStimulusView" guid="4QqUwYXqmEe0d7AF/jQn0wAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,930;1256,930</XPD:ATTR>
<XPD:REF name="Model">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="Head">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="hwf2NZaxeUCdZL2ZD4ov9AAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">19 : MsQuic-&gt;StreamSend()</XPD:ATTR>
<XPD:REF name="Model">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="HostEdge">4QqUwYXqmEe0d7AF/jQn0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="z9WVIXuNJE+A83xDym74dQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="HostEdge">4QqUwYXqmEe0d7AF/jQn0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="O32ahctcfk6fbkAjWaiqiwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="HostEdge">4QqUwYXqmEe0d7AF/jQn0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="WWZ9KZgA5kWiRU4jWYwqTwAA">
<XPD:ATTR name="Left" type="integer">1256</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">930</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[25]" type="UMLSeqStimulusView" guid="3vNqej6/+USIJgSBf61XnAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1256,948;205,948</XPD:ATTR>
<XPD:REF name="Model">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="Q/yGFTdRqEaglFm3LRQoWAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">20 : ServerStreamCallbak:QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE()</XPD:ATTR>
<XPD:REF name="Model">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:REF name="HostEdge">3vNqej6/+USIJgSBf61XnAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="KfzC+m78KEGbMOUySH4pPAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:REF name="HostEdge">3vNqej6/+USIJgSBf61XnAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="oFj52osyr0GniEFJ8zmbhwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:REF name="HostEdge">3vNqej6/+USIJgSBf61XnAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="dUZpbgAj7kidL2MxPaqlkwAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">948</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[26]" type="UMLSeqStimulusView" guid="PxmEjv48UUqNbMZRvwXZJQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,995;664,995</XPD:ATTR>
<XPD:REF name="Model">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="buTxBmVRjEaqaNdSf3S4WAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">21 : MsQuic-&gt;StreamClose()</XPD:ATTR>
<XPD:REF name="Model">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="HostEdge">PxmEjv48UUqNbMZRvwXZJQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="4YuujwGoRkO+3bAf2Q8pAQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="HostEdge">PxmEjv48UUqNbMZRvwXZJQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="TOv+O0Y7KkiQqsQ0uPe5HAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="HostEdge">PxmEjv48UUqNbMZRvwXZJQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="DeuN1RttfUmlh4Tjuns7jQAA">
<XPD:ATTR name="Left" type="integer">664</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">995</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[27]" type="UMLSeqStimulusView" guid="85o2O/uCaEq5gHneZ39IDAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">671,1025;1256,1025</XPD:ATTR>
<XPD:REF name="Model">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
<XPD:REF name="Head">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:REF name="Tail">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="1ecd4CXx60CmfxAv3lVI7gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">22 : QuicStreamClose()</XPD:ATTR>
<XPD:REF name="Model">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
<XPD:REF name="HostEdge">85o2O/uCaEq5gHneZ39IDAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="W5ULwTuBDU+eNmlRiVUuhgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
<XPD:REF name="HostEdge">85o2O/uCaEq5gHneZ39IDAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="W0UY3iWM6EiyEdAq9zDkJQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
<XPD:REF name="HostEdge">85o2O/uCaEq5gHneZ39IDAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="P+55RnL57ESJxtXP+ub4wwAA">
<XPD:ATTR name="Left" type="integer">1256</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1025</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[28]" type="UMLSeqStimulusView" guid="4r8d1sfh1kaHYlKHURxMkQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">1085,1069;205,1069</XPD:ATTR>
<XPD:REF name="Model">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="nkyZgPOMskCZctbhkePZJgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">23 : ServerConnectionCallback:QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE()</XPD:ATTR>
<XPD:REF name="Model">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
<XPD:REF name="HostEdge">4r8d1sfh1kaHYlKHURxMkQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="pevXjwoO0kW3rTCy2QiSXQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
<XPD:REF name="HostEdge">4r8d1sfh1kaHYlKHURxMkQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="1GqvxyhfAEWY5VdNP7Mi/AAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
<XPD:REF name="HostEdge">4r8d1sfh1kaHYlKHURxMkQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="N+CtzlWmAk+85N1TRyhGyAAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1069</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[29]" type="UMLSeqStimulusView" guid="boCVFeVk/0+N8tYbtlFtPAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,1105;664,1105</XPD:ATTR>
<XPD:REF name="Model">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="qwW0+lqNLE60HhhP7aNqdQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">24 : MsQuic-&gt;ConnectionClose()</XPD:ATTR>
<XPD:REF name="Model">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="HostEdge">boCVFeVk/0+N8tYbtlFtPAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="FWoDogEU+Eeo367FrphzQwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="HostEdge">boCVFeVk/0+N8tYbtlFtPAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="xraM1/zsFUy70r87VJH+dQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="HostEdge">boCVFeVk/0+N8tYbtlFtPAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="YtN7pmiziU2G8vkFSCI3uQAA">
<XPD:ATTR name="Left" type="integer">664</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1105</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[30]" type="UMLSeqStimulusView" guid="UcgELVjtBEWv+/oqGKkDBwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">671,1144;1078,1144</XPD:ATTR>
<XPD:REF name="Model">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="ZkKC2u0k8E2rPQU3rbAo0gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">25 : QuicConnRelease()</XPD:ATTR>
<XPD:REF name="Model">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
<XPD:REF name="HostEdge">UcgELVjtBEWv+/oqGKkDBwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="MYW2UMYrN0m/L+Y28LoZnAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
<XPD:REF name="HostEdge">UcgELVjtBEWv+/oqGKkDBwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="d/NVXLbmMkathk9gCxTpywAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
<XPD:REF name="HostEdge">UcgELVjtBEWv+/oqGKkDBwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="DTOXGLxdsE2tgToUVfX2uwAA">
<XPD:ATTR name="Left" type="integer">1078</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1144</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[31]" type="UMLSeqStimulusView" guid="GYsUV+00xk6jAr5bwusmLgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,1160;463,1160</XPD:ATTR>
<XPD:REF name="Model">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="ZNEt5eN9v0uJhjtCbSI8RwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">26 : MsQuic-&gt;ListenerClose()</XPD:ATTR>
<XPD:REF name="Model">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="HostEdge">GYsUV+00xk6jAr5bwusmLgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="qPnM/E/CQUm9TcP3si14LgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="HostEdge">GYsUV+00xk6jAr5bwusmLgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="pn0Yngy5d0qHr1FDZBcH9gAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="HostEdge">GYsUV+00xk6jAr5bwusmLgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="y9Wv6/LlE0mcSz1fNi7ApwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1160</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[32]" type="UMLSeqStimulusView" guid="+gdsu1Rh/kOL8+5TehziHwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,1204;463,1204</XPD:ATTR>
<XPD:REF name="Model">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="uLe4UUbBSU6pfziujTLZvwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">27 : MsQuic-&gt;ConfigurationClose()</XPD:ATTR>
<XPD:REF name="Model">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="HostEdge">+gdsu1Rh/kOL8+5TehziHwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="BJTyP+ltqk2qOb9dqviXqgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="HostEdge">+gdsu1Rh/kOL8+5TehziHwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="nvwW4zLgL0aDpWJqijksMgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="HostEdge">+gdsu1Rh/kOL8+5TehziHwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="0fK+PYxl1EecpcDp3an1AgAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1204</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[33]" type="UMLSeqStimulusView" guid="uBq7kwgwBEKh83CUGUYp0wAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,1244;463,1244</XPD:ATTR>
<XPD:REF name="Model">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="D84M0cIcXEK79DvnTL6QEgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">28 : MsQuic-&gt;RegistrationClose()</XPD:ATTR>
<XPD:REF name="Model">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="HostEdge">uBq7kwgwBEKh83CUGUYp0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="4+c3sXkbK0u/i9q+Sg/K6QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="HostEdge">uBq7kwgwBEKh83CUGUYp0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="lIq+I47DTUm6iKvSkkM2lgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="HostEdge">uBq7kwgwBEKh83CUGUYp0wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="4AgFHoDPDEGTApBhWNy/qAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1244</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[34]" type="UMLSeqStimulusView" guid="H63C738g8UK2ucaH4h4zbwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,1280;463,1280</XPD:ATTR>
<XPD:REF name="Model">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="wW9T2sAbEE+/6iQ9fda1XwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">29 : MsQuicClose()</XPD:ATTR>
<XPD:REF name="Model">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
<XPD:REF name="HostEdge">H63C738g8UK2ucaH4h4zbwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="p1kbU3ZLhEWGIjGHIkU3eQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
<XPD:REF name="HostEdge">H63C738g8UK2ucaH4h4zbwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="ZsXykhRd9UKh7ERSMoajewAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
<XPD:REF name="HostEdge">H63C738g8UK2ucaH4h4zbwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="5a0C5wy8dEOhL35sxVPmbwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1280</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[35]" type="UMLSeqStimulusView" guid="TgD3rnJqdkqlbIum/6x33gAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,1316;664,1316</XPD:ATTR>
<XPD:REF name="Model">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="WGIytoYmf0mftADpdhB8YQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">30 : CxPlatWorkerPoolUninit()</XPD:ATTR>
<XPD:REF name="Model">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
<XPD:REF name="HostEdge">TgD3rnJqdkqlbIum/6x33gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="Ed3jJYLh/06iogSH5QDbgwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
<XPD:REF name="HostEdge">TgD3rnJqdkqlbIum/6x33gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="k4P9zuDwwkWpewdGDqpMcAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
<XPD:REF name="HostEdge">TgD3rnJqdkqlbIum/6x33gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="EGXCxRjuIUm0/09BMOiTGQAA">
<XPD:ATTR name="Left" type="integer">664</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">1316</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingStimuli" type="integer">30</XPD:ATTR>
<XPD:OBJ name="ParticipatingStimuli[0]" type="UMLStimulus" guid="2STyiL6wBkSrtaDfatb64wAA">
<XPD:ATTR name="Name" type="string">MsQuicOpen2</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="FCqbeum8pkKP4j3ksd+iwwAA">
<XPD:REF name="Stimulus">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
<XPD:REF name="Views[1]">mJgFFVIbl0Kb4O4Re+5yUAAA</XPD:REF>
<XPD:REF name="Views[2]">GbIXvH8SkUqsZolSq95kowAA</XPD:REF>
<XPD:REF name="Views[3]">S90I5wQEs0aBXCatMG/TkwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[1]" type="UMLStimulus" guid="wo63JbqJWkmbC7qDUoj2wwAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryLoad</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="9PWtkiMhd0SboeQTgX75hgAA">
<XPD:REF name="Stimulus">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">rN8XWYPv70i0Sd6rzg7/zAAA</XPD:REF>
<XPD:REF name="Views[1]">A9pm7fJ6ZEmazIw8AvCHcQAA</XPD:REF>
<XPD:REF name="Views[2]">P6niAFdvYUSUTbiFE+iqfAAA</XPD:REF>
<XPD:REF name="Views[3]">5BYjToYYrEe4hCzV686GKgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[2]" type="UMLStimulus" guid="VmT2dGuUGkmPnkhQyJ0GTQAA">
<XPD:ATTR name="Name" type="string">CxPlatSystemLoad</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="whgMEu7V+Uydvv9TnXGY3AAA">
<XPD:REF name="Stimulus">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">1Jl03a5a6kyA0UsQhVv2jwAA</XPD:REF>
<XPD:REF name="Views[1]">HKU5J8Yp0UiNpZWHVrCz3gAA</XPD:REF>
<XPD:REF name="Views[2]">awoLiX7T/ESZPQmdpDXBgAAA</XPD:REF>
<XPD:REF name="Views[3]">9L7B+GJwbEO0tRn51ipWfgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[3]" type="UMLStimulus" guid="m2FDmd26TU6vc0CwDVdxJAAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryInitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="ELLYqIX0d0yGvNb8EM1I6QAA">
<XPD:REF name="Stimulus">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">C8OLOXCEwE+He7magad5AQAA</XPD:REF>
<XPD:REF name="Views[1]">Hn8o00z/9Ue5v30Cg+dUuQAA</XPD:REF>
<XPD:REF name="Views[2]">QLPIJmfj70eMUq9D4c9pDAAA</XPD:REF>
<XPD:REF name="Views[3]">TsagCSwiJEW5FVfMuvgLjgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[4]" type="UMLStimulus" guid="cN5IsxeUCkG0+fvctLFYygAA">
<XPD:ATTR name="Name" type="string">CxPlatInitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="m//WOwth6UKyNcoomxwkrAAA">
<XPD:REF name="Stimulus">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Uu1VOlE9NEeSzZkZAT6mjAAA</XPD:REF>
<XPD:REF name="Views[1]">rkQ+/JwtWkayS4yjdWxKaAAA</XPD:REF>
<XPD:REF name="Views[2]">cjdKav2E+UOFxBqU3vT3iwAA</XPD:REF>
<XPD:REF name="Views[3]">AllgNduFYkabQGWMjzbj2wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[5]" type="UMLStimulus" guid="VHIVhFa2PkGbARAlXReRbgAA">
<XPD:ATTR name="Name" type="string">CxPlatWorkerPoolInit</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="RjkQp/Xygke/OUxbcoTWsAAA">
<XPD:REF name="Stimulus">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
<XPD:REF name="Views[1]">dD9eiSncVEmuhy4t5tJ6KwAA</XPD:REF>
<XPD:REF name="Views[2]">kVlo/+eDg02oTd+nY1yxqAAA</XPD:REF>
<XPD:REF name="Views[3]">BslqtKAHZUmD1gQXEPMWCAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[6]" type="UMLStimulus" guid="pN9IkH+Y1kmdtQmUwZmRzAAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;RegistrationOpen</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="icJCwneyb0uOV6nj+86b0QAA">
<XPD:REF name="Stimulus">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">+DDnP53C/EGSEtA1lhbXDwAA</XPD:REF>
<XPD:REF name="Views[1]">PyRReHMtGEi321H+4j7aPwAA</XPD:REF>
<XPD:REF name="Views[2]">wdTukg1/dUOgU+fblfFgGQAA</XPD:REF>
<XPD:REF name="Views[3]">21pX0IA8L0CSaiCM8hGJMAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[7]" type="UMLStimulus" guid="J+KCug3h4k+wfUu7tV2esAAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ConfigurationOpen</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="uvGIAlnrOk+WMJm3s6FfzgAA">
<XPD:REF name="Stimulus">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">12W+IlObr068giGRWrqqXQAA</XPD:REF>
<XPD:REF name="Views[1]">hE4p+aYq6kaRcqF5OmU4oQAA</XPD:REF>
<XPD:REF name="Views[2]">YQcXZS6BLUeA7nfjMKQMnQAA</XPD:REF>
<XPD:REF name="Views[3]">5Gzv8mTaT0e3q7hFK6eQtQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[8]" type="UMLStimulus" guid="kWAGOh4qxk6MG0U8udc6FQAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ConfigurationLoadCredential</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="08PvcLOYkkatcyJ/VQ1ypAAA">
<XPD:REF name="Stimulus">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Jd1Xgs/HjU+YxuhHsj1MbAAA</XPD:REF>
<XPD:REF name="Views[1]">z4JgpRq/xkKsHEzeQOntxQAA</XPD:REF>
<XPD:REF name="Views[2]">zXEK+vrZYUOb6enUuhJFRAAA</XPD:REF>
<XPD:REF name="Views[3]">W1YS1JAj00mH2rMIoyysKAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[9]" type="UMLStimulus" guid="MaFWKMxHukihf2o1fyjMdwAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ListenerOpen:ServerListenerCallback</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="x4oviwhZUU21WWpK82UjjAAA">
<XPD:REF name="Stimulus">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">wFiPEtIzaUOW0qx3Zcq3MAAA</XPD:REF>
<XPD:REF name="Views[1]">wb36RycXqU+DT7BjT0Av9wAA</XPD:REF>
<XPD:REF name="Views[2]">E9az49HrbUehjR677P+rTAAA</XPD:REF>
<XPD:REF name="Views[3]">IbStsFyVW0iLSNCYm12OtgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[10]" type="UMLStimulus" guid="sEUpLpz27ECqS6P/+cL5+QAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ListenerStart</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="xBeFoev0YEe70/FHw7R8LwAA">
<XPD:REF name="Stimulus">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">AqitT6/q2EGKiMJXU62MZwAA</XPD:REF>
<XPD:REF name="Views[1]">12cq7S9w3Eu2f7xCFKIYMgAA</XPD:REF>
<XPD:REF name="Views[2]">auGtW9ZoN0aJv+as53N6jgAA</XPD:REF>
<XPD:REF name="Views[3]">N87g0nMM9UiOM35PjUn2ZQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[11]" type="UMLStimulus" guid="Nf5M0Bx5ykenFWrj2yrAcQAA">
<XPD:ATTR name="Name" type="string">QuicConnIndicateEvent</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="wCSmFUf5I0Cn//GZAv74pwAA">
<XPD:REF name="Stimulus">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">qWtBHJ2CeEmyXwIfnw5fjQAA</XPD:REF>
<XPD:REF name="Views[1]">bSunKAHhA0mSEdWDlIkavAAA</XPD:REF>
<XPD:REF name="Views[2]">Iy3+n80qrE2rGwetSDnr0AAA</XPD:REF>
<XPD:REF name="Views[3]">adqifB4BvUaCPuHtHDzrYAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[12]" type="UMLStimulus" guid="rrvKnoW990OOB1i8/ckoMgAA">
<XPD:ATTR name="Name" type="string">ServerConnectionCallback:QUIC_CONNECTION_EVENT_CONNECTED</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="ZRqf6WseTkK0c7YQXIF3sQAA">
<XPD:REF name="Stimulus">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">kiyt/b70Z0OApMHqQFplzAAA</XPD:REF>
<XPD:REF name="Views[1]">T2Hn8raCaUWRashrpXcfuwAA</XPD:REF>
<XPD:REF name="Views[2]">RyWp75p6PUycUfKqzImjiQAA</XPD:REF>
<XPD:REF name="Views[3]">argulqMgUUuVOjoqFKgeZwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[13]" type="UMLStimulus" guid="GbBj8Cp5zUeu7vbnakny8QAA">
<XPD:ATTR name="Name" type="string">MsQuicConnectionSendResumptionTicket</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="rXXKAeaTvEG3zfNmRv1NOgAA">
<XPD:REF name="Stimulus">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">FBwU0JXFqEa4rrcY4xGihAAA</XPD:REF>
<XPD:REF name="Views[1]">82Mh2mGGTUKVuODfnhSKGwAA</XPD:REF>
<XPD:REF name="Views[2]">sSDHt1ugu0WPXaRIiFWJqAAA</XPD:REF>
<XPD:REF name="Views[3]">rQEss86vMk6Lp7tbyC31RAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[14]" type="UMLStimulus" guid="QGuhTM6pXUC5Kliz6QWAXwAA">
<XPD:ATTR name="Name" type="string">ServerConnectionCallback:QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="IZGR77vXmEy6124cPZJZkQAA">
<XPD:REF name="Stimulus">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Rj9X+dy+wkinY9/M2z7ALQAA</XPD:REF>
<XPD:REF name="Views[1]">paDQ3qXaz0Kip0eGmIs+HwAA</XPD:REF>
<XPD:REF name="Views[2]">6OwUxz8aTESJLG/2pqsBYAAA</XPD:REF>
<XPD:REF name="Views[3]">bb0yHmAFUkKmpvRb4AiHWAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[15]" type="UMLStimulus" guid="FRCJ9EW54U25d9Y+stcXfgAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;SetCallbackHandler:ServerStreamCallback</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="XhYx17shNUWmgIRA3M7fBgAA">
<XPD:REF name="Stimulus">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">agnLR+f0hUqHh/DnAv99DwAA</XPD:REF>
<XPD:REF name="Views[1]">4qeAwDj39Em7NX2RDdgBqgAA</XPD:REF>
<XPD:REF name="Views[2]">1BHe/lCkn0i8ejwIqf/ymwAA</XPD:REF>
<XPD:REF name="Views[3]">AFdAUeiQq0mVNcmKd3ydfgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[16]" type="UMLStimulus" guid="IUsMqFRC20q9oFK/jGRpUQAA">
<XPD:ATTR name="Name" type="string">QuicStreamIndicateEvent</XPD:ATTR>
<XPD:REF name="Sender">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:REF name="Receiver">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="OyTVhyxFhEa4/gcGQdshdAAA">
<XPD:REF name="Stimulus">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">nc7eqj65WUS6Oz4tfhyjqgAA</XPD:REF>
<XPD:REF name="Views[1]">M6g1SYlMV0ec1l0gPpVcyQAA</XPD:REF>
<XPD:REF name="Views[2]">5XaLlE2l0keALWVcBl+xPgAA</XPD:REF>
<XPD:REF name="Views[3]">1AnxmDkLUk2ZdW3EyJCeMgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[17]" type="UMLStimulus" guid="DSLQNBhSrEeDP9Uk642tXgAA">
<XPD:ATTR name="Name" type="string">ServerStreamCallbak:QUIC_STREAM_EVENT_RECEIVE</XPD:ATTR>
<XPD:REF name="Sender">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="ZDxj1Y7zqUW4fVhD4nlihQAA">
<XPD:REF name="Stimulus">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">fQmVpu3Y/E+LAVAFUhcD/wAA</XPD:REF>
<XPD:REF name="Views[1]">4NIfJL+u7UOiBpcyIIISOwAA</XPD:REF>
<XPD:REF name="Views[2]">jmsI0PXMaEagYTH24Q3eEQAA</XPD:REF>
<XPD:REF name="Views[3]">7vS+EF+bvEuEG7172LHQKQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[18]" type="UMLStimulus" guid="g/hpQX3NFk6FZP7n0LE0gwAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;StreamSend</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="x8eW60UAbk6nZWErKbm1zwAA">
<XPD:REF name="Stimulus">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">4QqUwYXqmEe0d7AF/jQn0wAA</XPD:REF>
<XPD:REF name="Views[1]">hwf2NZaxeUCdZL2ZD4ov9AAA</XPD:REF>
<XPD:REF name="Views[2]">z9WVIXuNJE+A83xDym74dQAA</XPD:REF>
<XPD:REF name="Views[3]">O32ahctcfk6fbkAjWaiqiwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[19]" type="UMLStimulus" guid="SVrVC0D+ZEe6X8rPBX33dwAA">
<XPD:ATTR name="Name" type="string">ServerStreamCallbak:QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE</XPD:ATTR>
<XPD:REF name="Sender">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="OlFF70R1hU+9zL6p2EBxbAAA">
<XPD:REF name="Stimulus">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">3vNqej6/+USIJgSBf61XnAAA</XPD:REF>
<XPD:REF name="Views[1]">Q/yGFTdRqEaglFm3LRQoWAAA</XPD:REF>
<XPD:REF name="Views[2]">KfzC+m78KEGbMOUySH4pPAAA</XPD:REF>
<XPD:REF name="Views[3]">oFj52osyr0GniEFJ8zmbhwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[20]" type="UMLStimulus" guid="0o6HDh+XpEe9Xr0GfP01ywAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;StreamClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="7ecPolE9kEmjMUOyca/U9wAA">
<XPD:REF name="Stimulus">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">PxmEjv48UUqNbMZRvwXZJQAA</XPD:REF>
<XPD:REF name="Views[1]">buTxBmVRjEaqaNdSf3S4WAAA</XPD:REF>
<XPD:REF name="Views[2]">4YuujwGoRkO+3bAf2Q8pAQAA</XPD:REF>
<XPD:REF name="Views[3]">TOv+O0Y7KkiQqsQ0uPe5HAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[21]" type="UMLStimulus" guid="xzfvlh2hPkSAbI1smG89WgAA">
<XPD:ATTR name="Name" type="string">QuicStreamClose</XPD:ATTR>
<XPD:REF name="Sender">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:REF name="Receiver">YHPCGABMdUOkz/1EAH7BiAAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="hHr3t5EzvEm5kSBZqlk/ewAA">
<XPD:REF name="Stimulus">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">85o2O/uCaEq5gHneZ39IDAAA</XPD:REF>
<XPD:REF name="Views[1]">1ecd4CXx60CmfxAv3lVI7gAA</XPD:REF>
<XPD:REF name="Views[2]">W5ULwTuBDU+eNmlRiVUuhgAA</XPD:REF>
<XPD:REF name="Views[3]">W0UY3iWM6EiyEdAq9zDkJQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[22]" type="UMLStimulus" guid="o5c14aUNB0CcJ1byfjmQ5AAA">
<XPD:ATTR name="Name" type="string">ServerConnectionCallback:QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="66NWGIFH/kqWWWauHYnI6wAA">
<XPD:REF name="Stimulus">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">4r8d1sfh1kaHYlKHURxMkQAA</XPD:REF>
<XPD:REF name="Views[1]">nkyZgPOMskCZctbhkePZJgAA</XPD:REF>
<XPD:REF name="Views[2]">pevXjwoO0kW3rTCy2QiSXQAA</XPD:REF>
<XPD:REF name="Views[3]">1GqvxyhfAEWY5VdNP7Mi/AAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[23]" type="UMLStimulus" guid="f2T7MEloTEyHu+4K2MX5twAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ConnectionClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="Cnz51kL4NkelATlCVaWnswAA">
<XPD:REF name="Stimulus">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">boCVFeVk/0+N8tYbtlFtPAAA</XPD:REF>
<XPD:REF name="Views[1]">qwW0+lqNLE60HhhP7aNqdQAA</XPD:REF>
<XPD:REF name="Views[2]">FWoDogEU+Eeo367FrphzQwAA</XPD:REF>
<XPD:REF name="Views[3]">xraM1/zsFUy70r87VJH+dQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[24]" type="UMLStimulus" guid="FaVsZC1nLEqhEE7475TR9wAA">
<XPD:ATTR name="Name" type="string">QuicConnRelease</XPD:ATTR>
<XPD:REF name="Sender">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="dM30b1AMHEyhmHUZ1bZ56gAA">
<XPD:REF name="Stimulus">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">UcgELVjtBEWv+/oqGKkDBwAA</XPD:REF>
<XPD:REF name="Views[1]">ZkKC2u0k8E2rPQU3rbAo0gAA</XPD:REF>
<XPD:REF name="Views[2]">MYW2UMYrN0m/L+Y28LoZnAAA</XPD:REF>
<XPD:REF name="Views[3]">d/NVXLbmMkathk9gCxTpywAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[25]" type="UMLStimulus" guid="QzQEz5+TfkqKhxVWdsNJ4wAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ListenerClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="cDUS++mW20GCovSM1wWq1AAA">
<XPD:REF name="Stimulus">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">GYsUV+00xk6jAr5bwusmLgAA</XPD:REF>
<XPD:REF name="Views[1]">ZNEt5eN9v0uJhjtCbSI8RwAA</XPD:REF>
<XPD:REF name="Views[2]">qPnM/E/CQUm9TcP3si14LgAA</XPD:REF>
<XPD:REF name="Views[3]">pn0Yngy5d0qHr1FDZBcH9gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[26]" type="UMLStimulus" guid="IokJ1Z63nE2wKTRppqKbagAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;ConfigurationClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="P19LYKZYp0yeNaJ3baLsigAA">
<XPD:REF name="Stimulus">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">+gdsu1Rh/kOL8+5TehziHwAA</XPD:REF>
<XPD:REF name="Views[1]">uLe4UUbBSU6pfziujTLZvwAA</XPD:REF>
<XPD:REF name="Views[2]">BJTyP+ltqk2qOb9dqviXqgAA</XPD:REF>
<XPD:REF name="Views[3]">nvwW4zLgL0aDpWJqijksMgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[27]" type="UMLStimulus" guid="48kEY8i4ekSgNSkTy6dz3gAA">
<XPD:ATTR name="Name" type="string">MsQuic-&gt;RegistrationClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="/JAxR7buskCTOOWAfLoq7QAA">
<XPD:REF name="Stimulus">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">uBq7kwgwBEKh83CUGUYp0wAA</XPD:REF>
<XPD:REF name="Views[1]">D84M0cIcXEK79DvnTL6QEgAA</XPD:REF>
<XPD:REF name="Views[2]">4+c3sXkbK0u/i9q+Sg/K6QAA</XPD:REF>
<XPD:REF name="Views[3]">lIq+I47DTUm6iKvSkkM2lgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[28]" type="UMLStimulus" guid="wYJWBrHKqUWZ8wGEYnISQQAA">
<XPD:ATTR name="Name" type="string">MsQuicClose</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="qXGyUDiE50qCl+R4/E2qlAAA">
<XPD:REF name="Stimulus">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">H63C738g8UK2ucaH4h4zbwAA</XPD:REF>
<XPD:REF name="Views[1]">wW9T2sAbEE+/6iQ9fda1XwAA</XPD:REF>
<XPD:REF name="Views[2]">p1kbU3ZLhEWGIjGHIkU3eQAA</XPD:REF>
<XPD:REF name="Views[3]">ZsXykhRd9UKh7ERSMoajewAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[29]" type="UMLStimulus" guid="atmVXUb67kWVnYpFc7/sCwAA">
<XPD:ATTR name="Name" type="string">CxPlatWorkerPoolUninit</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="jcqzVRki8k6m/eel+ui3/gAA">
<XPD:REF name="Stimulus">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">TgD3rnJqdkqlbIum/6x33gAA</XPD:REF>
<XPD:REF name="Views[1]">WGIytoYmf0mftADpdhB8YQAA</XPD:REF>
<XPD:REF name="Views[2]">Ed3jJYLh/06iogSH5QDbgwAA</XPD:REF>
<XPD:REF name="Views[3]">k4P9zuDwwkWpewdGDqpMcAAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingInstances" type="integer">6</XPD:ATTR>
<XPD:OBJ name="ParticipatingInstances[0]" type="UMLObject" guid="B2op3V8XQ0eesPz/zKkoBQAA">
<XPD:ATTR name="Name" type="string">app</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">hPmViqUBXkyp20XJo5hMdgAA</XPD:REF>
<XPD:REF name="Views[1]">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">15</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="SendingStimuli[4]">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="SendingStimuli[5]">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="SendingStimuli[6]">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="SendingStimuli[7]">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="SendingStimuli[8]">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="SendingStimuli[9]">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="SendingStimuli[10]">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="SendingStimuli[11]">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="SendingStimuli[12]">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="SendingStimuli[13]">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="SendingStimuli[14]">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">5</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[4]">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[1]" type="UMLObject" guid="aRekjOsrRkGxXeUMaRdEOgAA">
<XPD:ATTR name="Name" type="string">library</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">QJ/ol6Ii20Cm5p+rCS0bnQAA</XPD:REF>
<XPD:REF name="Views[1]">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">6</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="SendingStimuli[4]">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="SendingStimuli[5]">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">13</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">wo63JbqJWkmbC7qDUoj2wwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">m2FDmd26TU6vc0CwDVdxJAAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">pN9IkH+Y1kmdtQmUwZmRzAAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[4]">J+KCug3h4k+wfUu7tV2esAAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[5]">kWAGOh4qxk6MG0U8udc6FQAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[6]">MaFWKMxHukihf2o1fyjMdwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[7]">sEUpLpz27ECqS6P/+cL5+QAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[8]">FRCJ9EW54U25d9Y+stcXfgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[9]">QzQEz5+TfkqKhxVWdsNJ4wAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[10]">IokJ1Z63nE2wKTRppqKbagAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[11]">48kEY8i4ekSgNSkTy6dz3gAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[12]">wYJWBrHKqUWZ8wGEYnISQQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[2]" type="UMLObject" guid="An+R1x1Jqk6mV8IzyMPWvwAA">
<XPD:ATTR name="Name" type="string">api</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">lDUJf8C6fEyt4j0+bqluBgAA</XPD:REF>
<XPD:REF name="Views[1]">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">2</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">GbBj8Cp5zUeu7vbnakny8QAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">0o6HDh+XpEe9Xr0GfP01ywAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">f2T7MEloTEyHu+4K2MX5twAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">atmVXUb67kWVnYpFc7/sCwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[3]" type="UMLObject" guid="xMzoSDbqnEK0EOuKrFyoJQAA">
<XPD:ATTR name="Name" type="string">platform</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">PnRdfgtnekueMRegs48acQAA</XPD:REF>
<XPD:REF name="Views[1]">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">3</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">VmT2dGuUGkmPnkhQyJ0GTQAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">cN5IsxeUCkG0+fvctLFYygAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[4]" type="UMLObject" guid="x4GPIF5LNU6b9TXPe2occgAA">
<XPD:ATTR name="Name" type="string">connection</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">33q5j2m07U66Vqancw44YQAA</XPD:REF>
<XPD:REF name="Views[1]">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">rrvKnoW990OOB1i8/ckoMgAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">QGuhTM6pXUC5Kliz6QWAXwAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">o5c14aUNB0CcJ1byfjmQ5AAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">2</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">Nf5M0Bx5ykenFWrj2yrAcQAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">FaVsZC1nLEqhEE7475TR9wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[5]" type="UMLObject" guid="YHPCGABMdUOkz/1EAH7BiAAA">
<XPD:ATTR name="Name" type="string">stream</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">fXwfWC6VWUCx60dQStWXbAAA</XPD:REF>
<XPD:REF name="Views[1]">piukC9S9LEWvDJjQgjtGjwAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">3</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">DSLQNBhSrEeDP9Uk642tXgAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">SVrVC0D+ZEe6X8rPBX33dwAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">3</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">IUsMqFRC20q9oFK/jGRpUQAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">g/hpQX3NFk6FZP7n0LE0gwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">xzfvlh2hPkSAbI1smG89WgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLModel" guid="md+fvOlUm0OVXhlV9hGlZQAA">
<XPD:ATTR name="Name" type="string">Design Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">designModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="m4ZRdEIg30uEQ1AKDhRiwwAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:REF name="DiagramOwner">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="+WcODUTEa0yzWzWZozxWSgAA">
<XPD:REF name="Diagram">m4ZRdEIg30uEQ1AKDhRiwwAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLClassView" guid="Z/m+NTtYakCizo+ZJ6PRwgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">356</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">120</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">213</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">95</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="18l4ty/7JkqfwF849znu2AAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="EvRVNDul20Ol+Qv55pYvzwAA">
<XPD:ATTR name="FontStyle" type="integer">1</XPD:ATTR>
<XPD:ATTR name="Text" type="string">library.c</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="lx49Ske3uEW4bZVaL9fufgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="cG52dQe9wEmS0zCw7F/UXQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="AttributeCompartment" type="UMLAttributeCompartmentView" guid="gKdcDpRpOkWcTxL/ZidPdQAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OperationCompartment" type="UMLOperationCompartmentView" guid="YXSBrfWAQUug1BGXKiQxtgAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="TemplateParameterCompartment" type="UMLTemplateParameterCompartmentView" guid="URf16QRAeUSOmCYkUK1naQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedElements" type="integer">3</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLClass" guid="JBFMD0tAmUOBm1nrDpCX0gAA">
<XPD:ATTR name="Name" type="string">library.c</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Z/m+NTtYakCizo+ZJ6PRwgAA</XPD:REF>
<XPD:REF name="Views[1]">gKdcDpRpOkWcTxL/ZidPdQAA</XPD:REF>
<XPD:REF name="Views[2]">YXSBrfWAQUug1BGXKiQxtgAA</XPD:REF>
<XPD:REF name="Views[3]">URf16QRAeUSOmCYkUK1naQAA</XPD:REF>
<XPD:ATTR name="#OwnedElements" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLInterface" guid="JT2S8CoRJU2JIea3BkEKiQAA">
<XPD:ATTR name="Name" type="string">Kim, Jeongil</XPD:ATTR>
<XPD:REF name="Namespace">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:ATTR name="#Operations" type="integer">4</XPD:ATTR>
<XPD:OBJ name="Operations[0]" type="UMLOperation" guid="99SPX+XZhEeX5Yg+VJEl/QAA">
<XPD:ATTR name="Name" type="string">MsQuicOpen2</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="npZ9vV5aB0yTW0bFO/W0WgAA">
<XPD:ATTR name="Name" type="string">const void** QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">99SPX+XZhEeX5Yg+VJEl/QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[1]" type="UMLOperation" guid="S/AXFe+Ro0mWFJthXDGG0QAA">
<XPD:ATTR name="Name" type="string">MsQuicClose</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="9l+2IKGa3kqRetrsHcZ5EAAA">
<XPD:ATTR name="Name" type="string">const void* QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">S/AXFe+Ro0mWFJthXDGG0QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[2]" type="UMLOperation" guid="FQAUYxQlNkCWUkFLWggmrgAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryLoad</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Operations[3]" type="UMLOperation" guid="XdHB1mkRzkqLeSO9Vtc35QAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryUninitialize</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLClass" guid="22zjv9K1y0aFzy8lJRR/KAAA">
<XPD:ATTR name="Name" type="string">Kum, Deukkyu</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLClass" guid="5oI76i2ZiUeFVSPnXc+4OwAA">
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[3]" type="UMLModel" guid="VFDhfGg3iE6vmYXwAyC6fAAA">
<XPD:ATTR name="Name" type="string">Implementation Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">implementationModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLComponentDiagram" guid="O/DoalvzX0Skw2gFKWB5ZQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">VFDhfGg3iE6vmYXwAyC6fAAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLComponentDiagramView" guid="4Ny79qmdTESrEmvxndV+BAAA">
<XPD:REF name="Diagram">O/DoalvzX0Skw2gFKWB5ZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[4]" type="UMLModel" guid="xmqKmxOf5EmOkrtUe6SHogAA">
<XPD:ATTR name="Name" type="string">Deployment Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">deploymentModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLDeploymentDiagram" guid="kW6AlkIWSUW41ygNV3gWHgAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">xmqKmxOf5EmOkrtUe6SHogAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLDeploymentDiagramView" guid="Hpb7DSjYcUCpDIAkjRJJLAAA">
<XPD:REF name="Diagram">kW6AlkIWSUW41ygNV3gWHgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:BODY>
</XPD:PROJECT>
