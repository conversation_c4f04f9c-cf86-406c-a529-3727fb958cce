<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_ccache_t Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_ccache_t Overview</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_ccache_t type represents a reference to a ccache. Callers can access a ccache and the credentials stored in it via a cc_ccache_t. A cc_ccache_t can be acquired via <a class="el" href="group__helper__macros.html#g256a5ba17fe0e4502e0722d9b081bbef">cc_context_open_ccache()</a>, <a class="el" href="group__helper__macros.html#g45a7ce29eb409baabadcae1bc95d5c57">cc_context_open_default_ccache()</a>, or <a class="el" href="group__helper__macros.html#gcff0b3e247a2adc95442324fec6c5651">cc_ccache_iterator_next()</a>.<p>
For API function documentation see <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> <a class="el" href="group__cc__ccache__reference.html#ga88aed938d3678e263f6507fcd6e5e38">cc_ccache_f</a>
<li>typedef <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a> <a class="el" href="group__cc__ccache__reference.html#g8eaae84d4f6a48e1e21eb4fe8a0b367a">cc_ccache_d</a>
<li>typedef <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a> * <a class="el" href="group__cc__ccache__reference.html#gf59e3c32bf0b7d9cc409438cebb2f048">cc_ccache_t</a>
</ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="ga88aed938d3678e263f6507fcd6e5e38"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_f" ref="ga88aed938d3678e263f6507fcd6e5e38" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g8eaae84d4f6a48e1e21eb4fe8a0b367a"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_d" ref="g8eaae84d4f6a48e1e21eb4fe8a0b367a" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a> <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="gf59e3c32bf0b7d9cc409438cebb2f048"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_t" ref="gf59e3c32bf0b7d9cc409438cebb2f048" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__ccache__d.html">cc_ccache_d</a>* <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
