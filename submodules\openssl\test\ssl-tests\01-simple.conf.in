# -*- mode: perl; -*-
# Copyright 2016-2016 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;

our @tests = (
    {
        name => "default",
        server => { },
        client => { },
        test   => { "ExpectedResult" => "Success" },
    },

    {
        name => "Server signature algorithms bug",
        # Should have no effect as we aren't doing client auth
        server => { "ClientSignatureAlgorithms" => "PSS+SHA512:RSA+SHA512" },
        client => { "SignatureAlgorithms" => "PSS+SHA256:RSA+SHA256" },
        test   => { "ExpectedResult" => "Success" },
    },

    {
        name => "verify-cert",
        server => { },
        client => {
            # Don't set up the client root file.
            "VerifyCAFile" => undef,
        },
        test   => {
          "ExpectedResult" => "ClientFail",
          "ExpectedClientAlert" => "UnknownCA",
        },
    },
);
