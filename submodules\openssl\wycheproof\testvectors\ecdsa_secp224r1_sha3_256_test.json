{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 393, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021c2f396442932cb80e2cca3381ebf0d975f33f6d7b77da96aefba1216a", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303d021c8ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021cd0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303f021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e0280008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264028000d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30400000021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3043498177303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422500303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "3040303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30432222498177021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304222212500021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3046221f021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640004deadbeef021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3043021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642222498177021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e76326422212500021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "3046021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264221f021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046aa00bb00cd00303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044aa02aabb303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30462225aa00bb00cd00021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "30442223aa02aabb021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642225aa00bb00cd00021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642223aa02aabb021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30422280021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640000021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642280021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30422280031d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640000021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642280031d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3042300102303d1d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d1d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d300", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d305000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30403000021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d33000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3041021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3bf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3040303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f02811d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e76326402811d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30400282001d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640282001d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021e008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021c008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021e00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021c00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30430285010000001d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3043021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640285010000001d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047028901000000000000001d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264028901000000000000001d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304202847fffffff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e76326402847fffffff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30420284ffffffff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640284ffffffff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30430285ffffffffff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3043021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640285ffffffffff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30460288ffffffffffffffff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3046021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640288ffffffffffffffff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e02ff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e76326402ff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e76326402", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021f008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640000021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021f00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021f0000008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021f000000d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640000021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021f008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640500021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021f00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d30500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e001d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e011d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e031d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e041d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303eff1d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264001d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264011d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264031d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264041d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264ff1d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632640200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "30422221020100021c8ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "3042021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632642221020100021cd0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d028ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d02d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632e4021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb0853", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021c008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e7632021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021c00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021eff008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021eff00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d018ed6690a135a8f918c0598c2d2fee3b4ec7c59a4dd66a65b6ad25ca1021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c8ed6690a135a8f918c0598c2d300b66f2b0a7928b5ac53d0b21a0827021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff712996f5eca5706e73fa673d2d0032edf43c9699367682e9f189cd9c021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c712996f5eca5706e73fa673d2cff4990d4f586d74a53ac2f4de5f7d9021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe712996f5eca5706e73fa673d2d011c4b1383a65b229959a4952da35f021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d018ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c712996f5eca5706e73fa673d2d0032edf43c9699367682e9f189cd9c021d00d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d01d0c69bbd6cd347f1d335cc7e140d53cfce327300afdfbbdbbd173310", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021cd0c69bbd6cd347f1d335cc7e140f268a0cc0928488256951045ede96", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021dff2f396442932cb80e2cca3381ebf1c2d312867d3d63fd6d699f44f72d", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021dfe2f396442932cb80e2cca3381ebf2ac3031cd8cff5020442442e8ccf0", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021d01d0c69bbd6cd347f1d335cc7e140e3d2ced7982c29c02929660bb08d3", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d008ed6690a135a8f918c0598c2d2ffcd120bc36966c9897d160e763264021c2f396442932cb80e2cca3381ebf1c2d312867d3d63fd6d699f44f72d", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3633313333", "sig": "303c021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021c7eb5cea4bda67eb17c42fd9e4ef8fc07a386c4d38b8e3fd7ac14e601", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "33313930393433323838", "sig": "303d021d00ce9c8f262a8fcfbff26a2ed56156dd7fa00df1b8dd78f28522f9599f021c3f8b90758650031ac943b6e89a2d401c03a4845f4825385edb0b9949", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "383433343734313535", "sig": "303e021d00a596c710492f86b31d7c3031ddffa41eb6ecd0d255272777765d965c021d00bc0e0d134f359088438f9d4865184a9134b22dc930a32df317cd2dad", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "33353732363936383239", "sig": "303c021c44bf0e8ef31adcf935bfdbdbffb848160ef5d5f97973303503ae43c6021c58194109101107d061575d48aefb8791da1aeca9214fcc4bf9b60dec", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "353931383333343239", "sig": "303d021d00bcc56b38d6a7b227a00f235f0aeef3ebf846cca2db14c29027339fc4021c4355863fcc75f246f213a9b4867deb2a7face8cdf5dfbbe43f8ac31a", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3832353330383232353839", "sig": "303d021c6ee3c0f02dbb1c5991fe897f8534bc9ba39e3c4a5c31d2326cebfb1c021d00e85b88cc3b25e3f6c9052993d3b43fb1e0d36840c64fbfb0b979f74f", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3133373334383238313432", "sig": "303c021c21dd71ac10881ea88296395ab8efbe822c081b5a6d448e6e5d6de917021c3b906e2910ac307a545c7c5e5a4155631be6ded9da8719f4590b5df2", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "313733383135363430", "sig": "303c021c3c8dd1c1da01ca7793890cecf967aef7b3199be89973f40f132f47cc021c7030a2afbf16e0200c9b5d9104009881b5667f5c991c3150d5ec0923", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32353533343636353034", "sig": "303c021c1bb538ffd49b566203b3390186d41052e2158bd8cabce482e2bd9cfd021c2621fe8a3ebd93982e7ad1f876e354a56809f8cdaf7289c247a93509", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32313833303339313937", "sig": "303b021c0b6b5578395738451e59bc461bfc558b0ffadc75045c4298b00f9539021b3147e9cdce81809e25b10531c59ae3f225c7a7681ff5135cf317bd", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "3130353236313736353435", "sig": "303e021d00cde67578f4666789a8b77812ca4c057feee8b7cb2ac67e038292c272021d00dc2dad5133d0de4d1d5f4e66c12641b0d036058382237da8c02570f8", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32353031353339393836", "sig": "303e021d00fdae74f7618e26dcaea23d96aa50bf3132e2ada0ba519b0cca94e477021d00d84fd4438476fd42fa02b510a88b8d66bd023c5080a54de3d3c8fc8b", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "3134393533313634363139", "sig": "303c021c3a9c3646b7af34c502284ef0070287672dd2b59e2e60f7272d50095c021c561225addbaab4b7bceba248b06dd462779bf1ee3198c2ea417ea42c", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "32303633383532393036", "sig": "303e021d00c4d91e546ac9dab2ccece18c49398d6342c0123149b598db9005320d021d00955ada4cbd17e4975467633fffb2321f5b4acb23f4b3021a063287f8", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "37383339373634393932", "sig": "303c021c2ffec2697a93f0c4c5a48bec8b15dad327b1b70017e6925fa76b683b021c205dbac588cae4f0ed3b8c7b4101399ce183d38211ed22306d0cda12", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32313239323634343639", "sig": "303d021d00fec15067a78ca643a53f827f56a8482c59d7e0ad38b07321d6fa9fe9021c3ce7e45b31390e026b485664cbf64e39b19703c8ef7b0f2d61bfe6b6", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "34383332303432363833", "sig": "303d021d00d57668679c06d00db112ebfd273f6aa56701b2008d77284f305201cb021c21cacc0f2900debc990cf2aa52c67bb7a9d183f331a3b984d63a157d", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "333635323231383136", "sig": "303d021d00c3c5260bed20289907b8b4fb6bd7fe69c257fe50fa84aaea7ec1ac0e021c38eb78dc31766a1b038e811dbe6b80683db5c06c7d466b6f1bd44fb8", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3234393137313236393635", "sig": "303c021c33efa91052f2a89dafd2b06cfa28b0c8243e3cac8246c1aea3cf4e60021c41f964715dd55418a5746f91ecff15b7c6163fb94c18979cf693c21d", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "32363232373930373738", "sig": "303d021d0085618ac165d6f879fd4f771c5f1a88019b04052c5f940ba052a541a5021c6640f1b8db137e516f405b64aa09d31e8c1dc9ac4d5abab6f9f8760a", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "313031323537373238393237", "sig": "303d021d009d1873a053a280bd698665ab4dd087be3080c2c3c3b9a2d728cc0704021c734210e8416e08fdcbb3251f383928976443c559f50e7164f084c807", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31303530383732323032", "sig": "303e021d00ab1fa9ddd8f16798fd015251fd71a4add962afb6f01b00f91e42352c021d009d71b11fe1a0628c012cdd938e838acbf22aebd64c1da01d5b2fbf4a", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32353037373237303332", "sig": "303e021d00869f3ec4765450ac03aa2a4a632d5f7a9603b4b52f37029dd2c7289b021d00a4bd2d056fe243fd3c6d719041b2093c81125f0ee7752730c3987311", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "373139333435393339", "sig": "303c021c0fd7a377de13b55ed9e39abed7153ab72b3864ae00089be6cb39c5ae021c01999722036ba44e9e00574b3de46a7c2af46974f3ce38181cebace1", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "31363337333035373633", "sig": "303c021c179ff07fe7b684e7231efcb22216709b6c5b64f3e2ab2b6962b7d0e2021c3077af624bfa19a3df87362e3a41ea0e7f904b32c06851cee0f5b6a1", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "34323431323733373133", "sig": "303c021c5d6817bbbcfa633f934456ab5946744128bd0eb7c5bbe6db16e9594d021c73c234f3f23187b318b984d099838ef57873ba6de48bd9fadcd2effe", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "343735363134373337", "sig": "303e021d00adf7cbd33018fe58d3da640dc8dcdb5db75a85b8409ef8a6d34a88df021d008ae5cafe6833ad40ea2698cd862df9a5718f3b00935885f89134d9ba", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "37313632333237373334", "sig": "303e021d00ba7720511153b6b96a38635e9997caff1ac31cdea4023241d01f966c021d00a0e72ea20c55bba47ee6aa7da3ebd1c1dbafee7152e3e22778644026", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "31323232343034313239", "sig": "303c021c10a8cc92e550c816999c0a9bde2b345a2a75c6f66861f060ff2a3742021c6ef54556c7883fc45e8b00638ff76c1f3eeafa895e4f2dce990249fc", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "33353234353833333638", "sig": "303d021c2980c3af3b6fe6fc5f0f82e74b848453cfed1460cf99a080bd5a8566021d0087d3abe0bc652743a75a54579a34b82a91c488990157c4a93645402e", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "3331363334333936", "sig": "303c021c2c3d2fef2d23ea431f36dd3258127326f83aba9989754fd733931bb0021c64de0f37c334eb07f57e5dcf925a7806f90f1af34c2544cf3d4d9f65", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "36353332353938343339", "sig": "303d021c5f08496864ba9b6b74810405fbba579a5aecca52c3c9851bce3ae580021d00fad2d32d584679eb0074285f34d5ee452ed0aac2222950bc3cb01960", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "373338353138353034", "sig": "303d021c0755fbc0cb4847101118d266e826cf23fdc664bfc4b9425eeb567342021d00c2fec316397cf167c1b234a7bab46c2a26b6b48b87790325995bf9a2", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "31343635323636353739", "sig": "303c021c254485afee6912f38bffae771553aaf734c779b769e792b2623ab056021c6e599ea2fe87d2228992cea340b14d8872ad3cb2abf35a1f453c7c24", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "32303334383839343934", "sig": "303e021d0099e668983f0c3c4168081d376646074358e923b05c8be3080ed0d2a0021d00e0b28c84f2ca323d89def878debd019f3a895c8deaccbe69b56c4807", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "34343730383836373835", "sig": "303e021d00acda06a1f01dbcd49e8998e2727755cb6462baf32811f204351589e3021d008ee9d910bb66295817c32d69b53ed6eabfa2e09fb39d46439a8a481d", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "353537363430343436", "sig": "303c021c5224c582ac8f7101bf6fe14a9617ca0a9878dbbe026ae230d1e63d0f021c61f0e486a1b7cce228874e7ccb6dc8dc95434afe6dbb7494b9f0e1c9", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "36333434393536363038", "sig": "303d021d009d05470e3988f76e782684ffd743bbd3a2bb683b0f2cddc873ff79ed021c3a1a4e796a78475db7000407279a665a2c406793110415e5655b6698", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34393432383039303331", "sig": "303c021c74d5a5f801ab103a8de9cefe365753e5e4e24aae88b18ead08f9e7e1021c22195ff2b1dff4f8ef7382a52f177a766a8f839b65b77076850c5edd", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "323636353831393339", "sig": "303e021d00eee00958aca3b5bc3ff48533ccdec3eb565663f173367cc95a9f314c021d00ebc3ed0d610e0b9fc63d8123b927a333af6ccc2fc1404291036e514d", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "33333332323134313134", "sig": "303d021c0f0a83fbdbb05c611f6430a8d2f47c53e445831c878203cb81513878021d00b4b1321f09a3ab5e4cc27befd89506651a4e40e22af69e58b3c88691", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "363136353237373135", "sig": "303d021c0a67da525ab869f3c6bb4dcc1821c2ac065728cd22d49b0ba5813ba8021d00b70065b12a6d2bc592783a7942ae0dae3ad1e7c6f27cacfc2b48dddb", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32333539393535383133", "sig": "303e021d00b41f01a5b75fba4835798156ac882e82a2e29859960132195c1f7e91021d00f421fba6d0061b92f8ab8ecbe7b5791bc43c5106c9ac9747e5da671a", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "323931333631393538", "sig": "303d021d00e3163b6dfd6585f50ac934bd25ea86065eff6376387a56cc210897de021c05d93a2dee9a55228dbc3df260152c458f8dd6f72b1d57f37f6f685c", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "31363230393033333936", "sig": "303c021c27a2c5db14c60f71c3f08196356ea7094db6559a4c5c7ab097aad799021c755741a777ad419b5c1853bc6f8da89c282a67f71cd1fc3abfe6ef1c", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33333633313032383432", "sig": "303c021c6b5c4a2123721cf74e151a3f3d97880d198cd7850a490b3736ed28a4021c4b0107b4c7f32a46315160b39f95d2bec469981960eeaf99f30e8d8b", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "363237373433343931", "sig": "303c021c219a8f9d6701d7b51d82b293d2f0ce4847e13abe9dfe8de426164040021c33623e698063becd8f28445ddb16caedfbe093a2c1d89925c28a12f9", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "33383536373938313437", "sig": "303d021d00a0676519c127f56b025695326eb68c5438b5d473c6b81b25d53793c2021c62ce33315ea1ae83dc48e7e774d701dc27b364484e3133de24f08e19", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "32383831313830363430", "sig": "303e021d00b16cb831277a401155134fb30d6938b9918665af7e59530fcd9cc0b2021d00f85a29c79b30ab6d9439eaece5901d65774ae1893ac603e3308c29ae", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "3130393537333934363738", "sig": "303d021c4eb1f5dd75615bda8368b94566dfdda9d7d8917f1863d3604059fb4f021d0080b5e243be6219350f60af1b50578f3d6204b5efcda10cdc338d08f7", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "353432373131393834", "sig": "303d021c600282c901cc3a4c8596a059b5ac217a9b999f0d3b69b24b3917d1cc021d00f1f401cd8cf106992244b3674ed9e55909b8683357be44fd48a1c3eb", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33313530303334333235", "sig": "303d021c54c666a30d72a0475a4cf0fe0bb58f13aadb361ffba89325c56ec48b021d0091f36a9fca040343fcc29c7fed35adb9db9a2f17c1f35de4afcc8f0a", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31393939383231333931", "sig": "303d021d00ffea346481a37d7f2728e2bbe35083bcbace7b91e06da2ad1825dbdf021c06cf6eee77ea7a4da0ed79a8a167adec51c8a2de906f3f7fecde799e", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31393731323039323631", "sig": "303c021c38fa40a57e4a04024c899051cc8080c5261dde66ea59fe532e852013021c3e99d123e596e993d677683bd25889549155edae098e59a29fe7d9cf", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "33353731383338383537", "sig": "303c021c7f62a985c5bbde0e11e0250a97d73fa38011bb83b6fa2d9836bf5c45021c3bd850832cc305e6b7d9566d36951ac4794b2d08ff712b18b0af6594", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "38363832323639383935", "sig": "303d021c311f063576c8373b96cc1652ab3be3a58eadea786e75b17a04c2bca0021d00bdb7096d675d1024291702dd991d5606c125e6129554922e02444fb1", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "32353438383536303331", "sig": "303d021c65db63a663e35fb97ea8f0752a3190134102f4fbbedd14bb5c1349a8021d00abdfe68c7f0c674f302488bc030558d35649f9a9c69d5801a575cb0b", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34393935373031333139", "sig": "303c021c075ceeeaa2ffbe5dc173d84df71145a056500a90f8fb902a24c0d363021c688cefcf26f584f8d598da2b960a512b6b65a425ed536a4bd570cf83", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "32343536363831373835", "sig": "303d021d00f84074b42b14acb8715ab229e4261c09b096a58b69f510f5f491ba6e021c304fe4129c6dbe481cc92d9dcbea983e40eafef17ea46039608a1431", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "34313835333731323336", "sig": "303d021d00ada080ecff37ca818f48dd5c0ebab78a645e973e138435637237f870021c4d85eb195089c83c92b483a53b036b33050aa14ba244eb48a0f97d9d", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "373237383439303034", "sig": "303d021d00a7c22c1d68e8bd563520cfc749d7de43d9ae045187a2424168eaacf3021c6959ae2c1fe30b45b049c4a5e418654711f21cbb925dce89e51a9ae4", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "31353632383139333138", "sig": "303c021c2ce15f3bc4f827e2cd5f59b7980f694e91c4b6a7b77c616f17121136021c3f71766ac9e52b98f58a6895112e43b75925183a29a73bd835f95593", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "3231383739393238333836", "sig": "303d021d00e69b6b3c9a08da2a90d59ac5454c10246bd8dec06590420391140693021c52d090e54b79fa780b46000a070b1a78ba9797b34b1761f09408c80b", "result": "valid", "flags": []}, {"tcId": 293, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "303d021d00c1a527a3efe3b397bef889b699b192a7663d9d60449dd9eccbfa8e55021c7ba2de0347d0895c6a24b26e80044586f6718beeabc316f18c88f014", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048e761a8fb0ae0f4d077c8331039186bacafac74ad25c499787d09ef4af5e802921def07b85dbaca11146382cc4121767d8cd0f0798e2bc0a", "wx": "008e761a8fb0ae0f4d077c8331039186bacafac74ad25c499787d09ef4", "wy": "00af5e802921def07b85dbaca11146382cc4121767d8cd0f0798e2bc0a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048e761a8fb0ae0f4d077c8331039186bacafac74ad25c499787d09ef4af5e802921def07b85dbaca11146382cc4121767d8cd0f0798e2bc0a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEjnYaj7CuD00HfIMxA5GGusr6x0rSXEmX\nh9Ce9K9egCkh3vB7hdusoRFGOCzEEhdn2M0PB5jivAo=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 294, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 295, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ca50630a872adcd558c388ca3b024cb59e1299bd45d9e324f605e2613c69a70c60f49e04b38e3738c5e591edaa51d7974de9e72725d8a690", "wx": "00ca50630a872adcd558c388ca3b024cb59e1299bd45d9e324f605e261", "wy": "3c69a70c60f49e04b38e3738c5e591edaa51d7974de9e72725d8a690"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ca50630a872adcd558c388ca3b024cb59e1299bd45d9e324f605e2613c69a70c60f49e04b38e3738c5e591edaa51d7974de9e72725d8a690", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEylBjCocq3NVYw4jKOwJMtZ4Smb1F2eMk\n9gXiYTxppwxg9J4Es443OMXlke2qUdeXTennJyXYppA=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041977aac0b91c2b65f580a5f33d8045a3a56e3a3ab48d8613f3ac0844c315f37b48cb771635e16afbca84948b9e4e35690a0990bddc6cab9a", "wx": "1977aac0b91c2b65f580a5f33d8045a3a56e3a3ab48d8613f3ac0844", "wy": "00c315f37b48cb771635e16afbca84948b9e4e35690a0990bddc6cab9a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041977aac0b91c2b65f580a5f33d8045a3a56e3a3ab48d8613f3ac0844c315f37b48cb771635e16afbca84948b9e4e35690a0990bddc6cab9a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEGXeqwLkcK2X1gKXzPYBFo6VuOjq0jYYT\n86wIRMMV83tIy3cWNeFq+8qElIueTjVpCgmQvdxsq5o=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04041ed3f4d372c3b7c4274e15c1a4c2e52011a5ea686de23b3b27bf3f6d8d6ebfa63b7467a691d6da259d932ece80b6ba946d992ca78c3aab", "wx": "041ed3f4d372c3b7c4274e15c1a4c2e52011a5ea686de23b3b27bf3f", "wy": "6d8d6ebfa63b7467a691d6da259d932ece80b6ba946d992ca78c3aab"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004041ed3f4d372c3b7c4274e15c1a4c2e52011a5ea686de23b3b27bf3f6d8d6ebfa63b7467a691d6da259d932ece80b6ba946d992ca78c3aab", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEBB7T9NNyw7fEJ04VwaTC5SARpepobeI7\nOye/P22Nbr+mO3RnppHW2iWdky7OgLa6lG2ZLKeMOqs=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046731bd2f7969febf93fa2382bd4fdc93ddeede8f2deac4c3abf1ce7a19516b15727d111c786b39ba11026d25a220b4fe52c5f56fd4ca5dec", "wx": "6731bd2f7969febf93fa2382bd4fdc93ddeede8f2deac4c3abf1ce7a", "wy": "19516b15727d111c786b39ba11026d25a220b4fe52c5f56fd4ca5dec"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046731bd2f7969febf93fa2382bd4fdc93ddeede8f2deac4c3abf1ce7a19516b15727d111c786b39ba11026d25a220b4fe52c5f56fd4ca5dec", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEZzG9L3lp/r+T+iOCvU/ck93u3o8t6sTD\nq/HOehlRaxVyfREceGs5uhECbSWiILT+UsX1b9TKXew=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044aa4667eacd6788f17ebde59e78dde177b2b378945ba487d325567d85d887d32e8cf6d5182433d8f81c945b4356d3ebc0e970dd0a9035387", "wx": "4aa4667eacd6788f17ebde59e78dde177b2b378945ba487d325567d8", "wy": "5d887d32e8cf6d5182433d8f81c945b4356d3ebc0e970dd0a9035387"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044aa4667eacd6788f17ebde59e78dde177b2b378945ba487d325567d85d887d32e8cf6d5182433d8f81c945b4356d3ebc0e970dd0a9035387", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESqRmfqzWeI8X695Z543eF3srN4lFukh9\nMlVn2F2IfTLoz21RgkM9j4HJRbQ1bT68DpcN0KkDU4c=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040322435ef8557da9306c645a0b614c6f6ce98d859697784cf74f2f23a8cd9e243e9088170133bd81eb6cd28571fcf207509819f443e5bbb5", "wx": "0322435ef8557da9306c645a0b614c6f6ce98d859697784cf74f2f23", "wy": "00a8cd9e243e9088170133bd81eb6cd28571fcf207509819f443e5bbb5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040322435ef8557da9306c645a0b614c6f6ce98d859697784cf74f2f23a8cd9e243e9088170133bd81eb6cd28571fcf207509819f443e5bbb5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAyJDXvhVfakwbGRaC2FMb2zpjYWWl3hM\n908vI6jNniQ+kIgXATO9gets0oVx/PIHUJgZ9EPlu7U=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 302, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045719bd78776367ffea95b9313ec825c70a3252326aa1ec66bc207bd3327ae05556f62f5650db898b316e689b5c377a8a64d743a89ab4153b", "wx": "5719bd78776367ffea95b9313ec825c70a3252326aa1ec66bc207bd3", "wy": "327ae05556f62f5650db898b316e689b5c377a8a64d743a89ab4153b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045719bd78776367ffea95b9313ec825c70a3252326aa1ec66bc207bd3327ae05556f62f5650db898b316e689b5c377a8a64d743a89ab4153b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVxm9eHdjZ//qlbkxPsglxwoyUjJqoexm\nvCB70zJ64FVW9i9WUNuJizFuaJtcN3qKZNdDqJq0FTs=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bb1e97c4b5bedec0af97169db06d040647bd40fa7853c8e8d0ad430b1025ec677e900574853cc5ce761a92bae929ec86076acc4859beacc8", "wx": "00bb1e97c4b5bedec0af97169db06d040647bd40fa7853c8e8d0ad430b", "wy": "1025ec677e900574853cc5ce761a92bae929ec86076acc4859beacc8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bb1e97c4b5bedec0af97169db06d040647bd40fa7853c8e8d0ad430b1025ec677e900574853cc5ce761a92bae929ec86076acc4859beacc8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEux6XxLW+3sCvlxadsG0EBke9QPp4U8jo\n0K1DCxAl7Gd+kAV0hTzFznYakrrpKeyGB2rMSFm+rMg=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b00ff7e1925b9717903a05d40ce9860ed12ebed8c686e05a9205a976110ee94a9a3267ab1565c66cdd5ed2844ccc5c6a7e78e4821b954f98", "wx": "00b00ff7e1925b9717903a05d40ce9860ed12ebed8c686e05a9205a976", "wy": "110ee94a9a3267ab1565c66cdd5ed2844ccc5c6a7e78e4821b954f98"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b00ff7e1925b9717903a05d40ce9860ed12ebed8c686e05a9205a976110ee94a9a3267ab1565c66cdd5ed2844ccc5c6a7e78e4821b954f98", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsA/34ZJblxeQOgXUDOmGDtEuvtjGhuBa\nkgWpdhEO6UqaMmerFWXGbN1e0oRMzFxqfnjkghuVT5g=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b4f18d031097f179effad146f5fa7e8574e6493dc4133a7e6bff6763b11ad9abcde8a93b78b6bc1f71d96168712263f6fdeb1da9b1193912", "wx": "00b4f18d031097f179effad146f5fa7e8574e6493dc4133a7e6bff6763", "wy": "00b11ad9abcde8a93b78b6bc1f71d96168712263f6fdeb1da9b1193912"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b4f18d031097f179effad146f5fa7e8574e6493dc4133a7e6bff6763b11ad9abcde8a93b78b6bc1f71d96168712263f6fdeb1da9b1193912", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtPGNAxCX8Xnv+tFG9fp+hXTmST3EEzp+\na/9nY7Ea2avN6Kk7eLa8H3HZYWhxImP2/esdqbEZORI=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a35240c68e8f4ecec640ad2cc54533275cd6b54f480476e0412f191e7c4bdef2aa2561fbb2d26f9034836265b81e555d56b6f446b6b863a8", "wx": "00a35240c68e8f4ecec640ad2cc54533275cd6b54f480476e0412f191e", "wy": "7c4bdef2aa2561fbb2d26f9034836265b81e555d56b6f446b6b863a8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a35240c68e8f4ecec640ad2cc54533275cd6b54f480476e0412f191e7c4bdef2aa2561fbb2d26f9034836265b81e555d56b6f446b6b863a8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEo1JAxo6PTs7GQK0sxUUzJ1zWtU9IBHbg\nQS8ZHnxL3vKqJWH7stJvkDSDYmW4HlVdVrb0Rra4Y6g=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ef67cfa95eba7f21e47e9f80e624d06297e3c516b5d4810bc03264f47ae076453ed06bc43999b713aafd0eb2aa8192f61a61d6560d66a3d8", "wx": "00ef67cfa95eba7f21e47e9f80e624d06297e3c516b5d4810bc03264f4", "wy": "7ae076453ed06bc43999b713aafd0eb2aa8192f61a61d6560d66a3d8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ef67cfa95eba7f21e47e9f80e624d06297e3c516b5d4810bc03264f47ae076453ed06bc43999b713aafd0eb2aa8192f61a61d6560d66a3d8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE72fPqV66fyHkfp+A5iTQYpfjxRa11IEL\nwDJk9HrgdkU+0GvEOZm3E6r9DrKqgZL2GmHWVg1mo9g=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ff16dbdd1335d3d31acd1fea3cbdd5fafbcfb13367cc5831574a0bae81763ffbd6bc8720d46e7ee3cda01b98a0cf479816ea46bea8aae199", "wx": "00ff16dbdd1335d3d31acd1fea3cbdd5fafbcfb13367cc5831574a0bae", "wy": "0081763ffbd6bc8720d46e7ee3cda01b98a0cf479816ea46bea8aae199"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ff16dbdd1335d3d31acd1fea3cbdd5fafbcfb13367cc5831574a0bae81763ffbd6bc8720d46e7ee3cda01b98a0cf479816ea46bea8aae199", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/xbb3RM109MazR/qPL3V+vvPsTNnzFgx\nV0oLroF2P/vWvIcg1G5+482gG5igz0eYFupGvqiq4Zk=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049c88d34bcdc70a09bd9cb4aab4e40fa900472d635c4ebd2366e5d4b9ecc54c3d44714953766bbb1257a3580a2aa85170e418969ba3a66841", "wx": "009c88d34bcdc70a09bd9cb4aab4e40fa900472d635c4ebd2366e5d4b9", "wy": "00ecc54c3d44714953766bbb1257a3580a2aa85170e418969ba3a66841"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049c88d34bcdc70a09bd9cb4aab4e40fa900472d635c4ebd2366e5d4b9ecc54c3d44714953766bbb1257a3580a2aa85170e418969ba3a66841", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEnIjTS83HCgm9nLSqtOQPqQBHLWNcTr0j\nZuXUuezFTD1EcUlTdmu7ElejWAoqqFFw5BiWm6OmaEE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 311, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a3ce180bd65ffc76d5502ae806a6b434d7e69b39b1940e44c83604cb4150ca512dddf3363897dd8d23f76564412188cc9be77c170dcef4e7", "wx": "00a3ce180bd65ffc76d5502ae806a6b434d7e69b39b1940e44c83604cb", "wy": "4150ca512dddf3363897dd8d23f76564412188cc9be77c170dcef4e7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a3ce180bd65ffc76d5502ae806a6b434d7e69b39b1940e44c83604cb4150ca512dddf3363897dd8d23f76564412188cc9be77c170dcef4e7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEo84YC9Zf/HbVUCroBqa0NNfmmzmxlA5E\nyDYEy0FQylEt3fM2OJfdjSP3ZWRBIYjMm+d8Fw3O9Oc=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047a1183e83dd8e38b2aef19c9e604a205ecf50abc9ad1b2bf3a062ba935d0ec70d1c66ba124872a47d044b8bb7b6a405b9a9bcce636f9e788", "wx": "7a1183e83dd8e38b2aef19c9e604a205ecf50abc9ad1b2bf3a062ba9", "wy": "35d0ec70d1c66ba124872a47d044b8bb7b6a405b9a9bcce636f9e788"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047a1183e83dd8e38b2aef19c9e604a205ecf50abc9ad1b2bf3a062ba935d0ec70d1c66ba124872a47d044b8bb7b6a405b9a9bcce636f9e788", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEehGD6D3Y44sq7xnJ5gSiBez1Crya0bK/\nOgYrqTXQ7HDRxmuhJIcqR9BEuLt7akBbmpvM5jb554g=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046fbbbfa60d49b603fbc7f6f6c922df0364c03f089af3a288ce4337d528c46eb6f43e9c4f2664ff72d587cd706c620cd718bceb1197482ed9", "wx": "6fbbbfa60d49b603fbc7f6f6c922df0364c03f089af3a288ce4337d5", "wy": "28c46eb6f43e9c4f2664ff72d587cd706c620cd718bceb1197482ed9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046fbbbfa60d49b603fbc7f6f6c922df0364c03f089af3a288ce4337d528c46eb6f43e9c4f2664ff72d587cd706c620cd718bceb1197482ed9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEb7u/pg1JtgP7x/b2ySLfA2TAPwia86KI\nzkM31SjEbrb0PpxPJmT/ctWHzXBsYgzXGLzrEZdILtk=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d50a93a475ab04521ca4bc4dcd06872e85fc587a7c56e68a6e94846a4511f0bd21af19dff4def09b04bcb20e21ad21e0f8c4a49f21856aa6", "wx": "00d50a93a475ab04521ca4bc4dcd06872e85fc587a7c56e68a6e94846a", "wy": "4511f0bd21af19dff4def09b04bcb20e21ad21e0f8c4a49f21856aa6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d50a93a475ab04521ca4bc4dcd06872e85fc587a7c56e68a6e94846a4511f0bd21af19dff4def09b04bcb20e21ad21e0f8c4a49f21856aa6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1QqTpHWrBFIcpLxNzQaHLoX8WHp8VuaK\nbpSEakUR8L0hrxnf9N7wmwS8sg4hrSHg+MSknyGFaqY=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044a0298d7a7a670d2538f6aae65ddf6be35d474bbdd1b6aa0a812d9749251866f630eda71e9d727964e563a2596ec04c4d0134fb997021ca3", "wx": "4a0298d7a7a670d2538f6aae65ddf6be35d474bbdd1b6aa0a812d974", "wy": "009251866f630eda71e9d727964e563a2596ec04c4d0134fb997021ca3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044a0298d7a7a670d2538f6aae65ddf6be35d474bbdd1b6aa0a812d9749251866f630eda71e9d727964e563a2596ec04c4d0134fb997021ca3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESgKY16emcNJTj2quZd32vjXUdLvdG2qg\nqBLZdJJRhm9jDtpx6dcnlk5WOiWW7ATE0BNPuZcCHKM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00a8ce483b42fb3461047c96ca00d0c226ee2850b79192346c7ce37d46", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044a8ead5e32234b2a78171bbe3215f1b721f9ae113c7e9711bd44cb2815580cc1e9f22a432e8070f700b949ea55cfcd9323589fe1edb06053", "wx": "4a8ead5e32234b2a78171bbe3215f1b721f9ae113c7e9711bd44cb28", "wy": "15580cc1e9f22a432e8070f700b949ea55cfcd9323589fe1edb06053"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044a8ead5e32234b2a78171bbe3215f1b721f9ae113c7e9711bd44cb2815580cc1e9f22a432e8070f700b949ea55cfcd9323589fe1edb06053", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESo6tXjIjSyp4Fxu+MhXxtyH5rhE8fpcR\nvUTLKBVYDMHp8ipDLoBw9wC5SepVz82TI1if4e2wYFM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0455bd003333e449dc59e181cb2e14e2240f26bc7f3f1713a73ba0e36c69ac8b1fb9c4c270ec57a6afd3f8c65f1e34c5176c7f7684e861167d", "wx": "55bd003333e449dc59e181cb2e14e2240f26bc7f3f1713a73ba0e36c", "wy": "69ac8b1fb9c4c270ec57a6afd3f8c65f1e34c5176c7f7684e861167d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000455bd003333e449dc59e181cb2e14e2240f26bc7f3f1713a73ba0e36c69ac8b1fb9c4c270ec57a6afd3f8c65f1e34c5176c7f7684e861167d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVb0AMzPkSdxZ4YHLLhTiJA8mvH8/FxOn\nO6DjbGmsix+5xMJw7Femr9P4xl8eNMUXbH92hOhhFn0=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047036b96d9033ea5bf6a63bc308936da1636b22c601f5fd1a3fc8b491028417ad37899b3ea1dca7b67ec60a7e7b0af04d024bad8aa3a5e4a3", "wx": "7036b96d9033ea5bf6a63bc308936da1636b22c601f5fd1a3fc8b491", "wy": "028417ad37899b3ea1dca7b67ec60a7e7b0af04d024bad8aa3a5e4a3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047036b96d9033ea5bf6a63bc308936da1636b22c601f5fd1a3fc8b491028417ad37899b3ea1dca7b67ec60a7e7b0af04d024bad8aa3a5e4a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEcDa5bZAz6lv2pjvDCJNtoWNrIsYB9f0a\nP8i0kQKEF603iZs+odyntn7GCn57CvBNAkutiqOl5KM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c7bb3d419456ee8a53d67867550ed5eb3c00d55638ac6d2132bb007b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0458fe2f40f6b361167bd9757e73908b21a8f43c420e13aef119211e646775a49681e2d646dea26bf257f8f7fac9f7fb2e9bbfd36649957b84", "wx": "58fe2f40f6b361167bd9757e73908b21a8f43c420e13aef119211e64", "wy": "6775a49681e2d646dea26bf257f8f7fac9f7fb2e9bbfd36649957b84"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000458fe2f40f6b361167bd9757e73908b21a8f43c420e13aef119211e646775a49681e2d646dea26bf257f8f7fac9f7fb2e9bbfd36649957b84", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEWP4vQPazYRZ72XV+c5CLIaj0PEIOE67x\nGSEeZGd1pJaB4tZG3qJr8lf49/rJ9/sum7/TZkmVe4Q=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00aab6daa4628852ca4b25e917ef4771547c75d5ec5127c9c6678cde4d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ae5524a4533101a5cfe3907d706cafa7f904d9dfe38ceec59457d62e18434e271edf58008c15d627dabbf5691e9c9eaa7930094ce885e2ce", "wx": "00ae5524a4533101a5cfe3907d706cafa7f904d9dfe38ceec59457d62e", "wy": "18434e271edf58008c15d627dabbf5691e9c9eaa7930094ce885e2ce"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ae5524a4533101a5cfe3907d706cafa7f904d9dfe38ceec59457d62e18434e271edf58008c15d627dabbf5691e9c9eaa7930094ce885e2ce", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErlUkpFMxAaXP45B9cGyvp/kE2d/jjO7F\nlFfWLhhDTice31gAjBXWJ9q79WkenJ6qeTAJTOiF4s4=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d008063db16787011a1c1212af044599a91f78869bd790f5dc588d842e5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e0af4ad4f06a41c72d502d6934c8c3f4b34f062d1cf723b3712c9af34a3d09ffd3506e11669609ea8fe8ee54b30188bc0ad136cdcf73038c", "wx": "00e0af4ad4f06a41c72d502d6934c8c3f4b34f062d1cf723b3712c9af3", "wy": "4a3d09ffd3506e11669609ea8fe8ee54b30188bc0ad136cdcf73038c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e0af4ad4f06a41c72d502d6934c8c3f4b34f062d1cf723b3712c9af34a3d09ffd3506e11669609ea8fe8ee54b30188bc0ad136cdcf73038c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4K9K1PBqQcctUC1pNMjD9LNPBi0c9yOz\ncSya80o9Cf/TUG4RZpYJ6o/o7lSzAYi8CtE2zc9zA4w=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c17688246313d0b42a8ce483b42fb1f0a6217394611f2b177ada94b47", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04deaf785750eef0b3e178ec726956b9338838b0be79ca5166041937b5a6b69318efc4961a50b44cd4b792c271539f3b4129e8e8dadc9684b3", "wx": "00deaf785750eef0b3e178ec726956b9338838b0be79ca5166041937b5", "wy": "00a6b69318efc4961a50b44cd4b792c271539f3b4129e8e8dadc9684b3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004deaf785750eef0b3e178ec726956b9338838b0be79ca5166041937b5a6b69318efc4961a50b44cd4b792c271539f3b4129e8e8dadc9684b3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE3q94V1Du8LPheOxyaVa5M4g4sL55ylFm\nBBk3taa2kxjvxJYaULRM1LeSwnFTnztBKejo2tyWhLM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0088246313d0b42a8ce483b42fb345942d2565666e25fd2f85a60ebae2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e14fb7fc849de20d33c6c5e6b358f5ba702eb2b9121def8d3deddbdf58153c8e0ef0b78993f4d17405c1fe2b20880d40b229f7de51a4d6b3", "wx": "00e14fb7fc849de20d33c6c5e6b358f5ba702eb2b9121def8d3deddbdf", "wy": "58153c8e0ef0b78993f4d17405c1fe2b20880d40b229f7de51a4d6b3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e14fb7fc849de20d33c6c5e6b358f5ba702eb2b9121def8d3deddbdf58153c8e0ef0b78993f4d17405c1fe2b20880d40b229f7de51a4d6b3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4U+3/ISd4g0zxsXms1j1unAusrkSHe+N\nPe3b31gVPI4O8LeJk/TRdAXB/isgiA1Asin33lGk1rM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c1048c627a1685519c907685f668c11b76a11dc9e381d35c5efc14b87", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f83a254c07c29022454c43be9bd5e99c630ff7d83206713a1fbfa0fa017a0adb068fb28a9418328eac1bc19c6c92c3f1666a773250571a19", "wx": "00f83a254c07c29022454c43be9bd5e99c630ff7d83206713a1fbfa0fa", "wy": "017a0adb068fb28a9418328eac1bc19c6c92c3f1666a773250571a19"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f83a254c07c29022454c43be9bd5e99c630ff7d83206713a1fbfa0fa017a0adb068fb28a9418328eac1bc19c6c92c3f1666a773250571a19", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+DolTAfCkCJFTEO+m9XpnGMP99gyBnE6\nH7+g+gF6CtsGj7KKlBgyjqwbwZxsksPxZmp3MlBXGhk=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c313d0b42a8ce483b42fb3461047c69e7886089d9f0c0fc2c4d917952", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0423ac1ccb807f76fa99207af67f662fb1ee10f1d5fddb715eafa8ad3db18eceeb7432c70250f8e92fa990baab18296547fb7901acdd8faf59", "wx": "23ac1ccb807f76fa99207af67f662fb1ee10f1d5fddb715eafa8ad3d", "wy": "00b18eceeb7432c70250f8e92fa990baab18296547fb7901acdd8faf59"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000423ac1ccb807f76fa99207af67f662fb1ee10f1d5fddb715eafa8ad3db18eceeb7432c70250f8e92fa990baab18296547fb7901acdd8faf59", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEI6wcy4B/dvqZIHr2f2Yvse4Q8dX923Fe\nr6itPbGOzut0MscCUPjpL6mQuqsYKWVH+3kBrN2Pr1k=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c1c22615f35d488bad614c3cc5578205bd25c0d73ed985e1214d094e1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04119d9f0c5f5f6206df598622ec7afc756a0c1c1b3d1133528a7a06cd0df17a9164719714488b9ba8021885d4eaa83e8842b11af368d06304", "wx": "119d9f0c5f5f6206df598622ec7afc756a0c1c1b3d1133528a7a06cd", "wy": "0df17a9164719714488b9ba8021885d4eaa83e8842b11af368d06304"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004119d9f0c5f5f6206df598622ec7afc756a0c1c1b3d1133528a7a06cd0df17a9164719714488b9ba8021885d4eaa83e8842b11af368d06304", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEEZ2fDF9fYgbfWYYi7Hr8dWoMHBs9ETNS\ninoGzQ3xepFkcZcUSIubqAIYhdTqqD6IQrEa82jQYwQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3844c2be6ba91175ac298798aaf040b7a4b81ae7db30bc2429a129c2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048888ae9b1ab8d57b18468a2c16f8c971a70711c6361a9afe14be4e33af32311a18ef6b965c8f6e252051794a3467de9f58c06a8545b743dc", "wx": "008888ae9b1ab8d57b18468a2c16f8c971a70711c6361a9afe14be4e33", "wy": "00af32311a18ef6b965c8f6e252051794a3467de9f58c06a8545b743dc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048888ae9b1ab8d57b18468a2c16f8c971a70711c6361a9afe14be4e33af32311a18ef6b965c8f6e252051794a3467de9f58c06a8545b743dc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEiIiumxq41XsYRoosFvjJcacHEcY2Gpr+\nFL5OM68yMRoY72uWXI9uJSBReUo0Z96fWMBqhUW3Q9w=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c5467241da17d9a30823e4b65006861137714285bc8c91a363e71bea3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042ce5a3f2b3fc7916953ebe71a7ff33921cb57167abaa871a07202196ea2d3b61820bdd5264f060680844d7217a21601e3ed37a79d5953b98", "wx": "2ce5a3f2b3fc7916953ebe71a7ff33921cb57167abaa871a07202196", "wy": "00ea2d3b61820bdd5264f060680844d7217a21601e3ed37a79d5953b98"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042ce5a3f2b3fc7916953ebe71a7ff33921cb57167abaa871a07202196ea2d3b61820bdd5264f060680844d7217a21601e3ed37a79d5953b98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELOWj8rP8eRaVPr5xp/8zkhy1cWerqoca\nByAhluotO2GCC91SZPBgaAhE1yF6IWAePtN6edWVO5g=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c031ed8b3c3808d0e0909578222c589a6c20acfdc6764385729a3691", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bae5f11eb77e354a0d0e33c4ce24839d726e1700e514ccbdede231454ffd009fbeea9c7307938f8adfed84de3600920286281d267c88609a", "wx": "00bae5f11eb77e354a0d0e33c4ce24839d726e1700e514ccbdede23145", "wy": "4ffd009fbeea9c7307938f8adfed84de3600920286281d267c88609a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bae5f11eb77e354a0d0e33c4ce24839d726e1700e514ccbdede231454ffd009fbeea9c7307938f8adfed84de3600920286281d267c88609a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEuuXxHrd+NUoNDjPEziSDnXJuFwDlFMy9\n7eIxRU/9AJ++6pxzB5OPit/thN42AJIChigdJnyIYJo=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d153ad8f462d2f9388fa8b43517aa2a7799dea0bbb1fba67c5674172df03b778b587abc18db23bd52f43913714d1f41b8c91907b24cf1ef8", "wx": "00d153ad8f462d2f9388fa8b43517aa2a7799dea0bbb1fba67c5674172", "wy": "00df03b778b587abc18db23bd52f43913714d1f41b8c91907b24cf1ef8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d153ad8f462d2f9388fa8b43517aa2a7799dea0bbb1fba67c5674172df03b778b587abc18db23bd52f43913714d1f41b8c91907b24cf1ef8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0VOtj0YtL5OI+otDUXqip3md6gu7H7pn\nxWdBct8Dt3i1h6vBjbI71S9DkTcU0fQbjJGQeyTPHvg=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0439b30cf6827e95c4bb1cf5a201e3611ea87660c671fcfe4837a55ba8bf990d7e7756ab4c0f08f0d674980caa2e559c93c84f7042fbf0ace0", "wx": "39b30cf6827e95c4bb1cf5a201e3611ea87660c671fcfe4837a55ba8", "wy": "00bf990d7e7756ab4c0f08f0d674980caa2e559c93c84f7042fbf0ace0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000439b30cf6827e95c4bb1cf5a201e3611ea87660c671fcfe4837a55ba8bf990d7e7756ab4c0f08f0d674980caa2e559c93c84f7042fbf0ace0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEObMM9oJ+lcS7HPWiAeNhHqh2YMZx/P5I\nN6VbqL+ZDX53VqtMDwjw1nSYDKouVZyTyE9wQvvwrOA=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ce72f433416794d9ba0e53735eb2277ffdfe84f852ff06b26b2ba48dcef033d6f897ce820f3178f0331b475ad9f8e6be2ff34788e09e55c4", "wx": "00ce72f433416794d9ba0e53735eb2277ffdfe84f852ff06b26b2ba48d", "wy": "00cef033d6f897ce820f3178f0331b475ad9f8e6be2ff34788e09e55c4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ce72f433416794d9ba0e53735eb2277ffdfe84f852ff06b26b2ba48dcef033d6f897ce820f3178f0331b475ad9f8e6be2ff34788e09e55c4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEznL0M0FnlNm6DlNzXrInf/3+hPhS/way\nayukjc7wM9b4l86CDzF48DMbR1rZ+Oa+L/NHiOCeVcQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0469211fb68e0ce40b590bdfb262753d3817a9777cbcc18292f63d94467fc0dcf4d6a02a0daf952f1bdc99ecb4bcefde8d7eb22ae14be44b5f", "wx": "69211fb68e0ce40b590bdfb262753d3817a9777cbcc18292f63d9446", "wy": "7fc0dcf4d6a02a0daf952f1bdc99ecb4bcefde8d7eb22ae14be44b5f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000469211fb68e0ce40b590bdfb262753d3817a9777cbcc18292f63d94467fc0dcf4d6a02a0daf952f1bdc99ecb4bcefde8d7eb22ae14be44b5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEaSEfto4M5AtZC9+yYnU9OBepd3y8wYKS\n9j2URn/A3PTWoCoNr5UvG9yZ7LS8796NfrIq4UvkS18=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eae4a3d43da31e5bbb6767ec18b03c22314dcde77f6adae7e6a1b66c3b2b6f2abe22f00376703cda54a6b6e4cbd7bac7614782ef9e94b26f", "wx": "00eae4a3d43da31e5bbb6767ec18b03c22314dcde77f6adae7e6a1b66c", "wy": "3b2b6f2abe22f00376703cda54a6b6e4cbd7bac7614782ef9e94b26f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eae4a3d43da31e5bbb6767ec18b03c22314dcde77f6adae7e6a1b66c3b2b6f2abe22f00376703cda54a6b6e4cbd7bac7614782ef9e94b26f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6uSj1D2jHlu7Z2fsGLA8IjFNzed/atrn\n5qG2bDsrbyq+IvADdnA82lSmtuTL17rHYUeC756Usm8=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041a51969a30f966894ed0e1b763da7cdd2a258a9a9d6efb019419c152fd8982295489e97f2d8d6ebe0409d759a5ca25cf9627f20e39f1e651", "wx": "1a51969a30f966894ed0e1b763da7cdd2a258a9a9d6efb019419c152", "wy": "00fd8982295489e97f2d8d6ebe0409d759a5ca25cf9627f20e39f1e651"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041a51969a30f966894ed0e1b763da7cdd2a258a9a9d6efb019419c152fd8982295489e97f2d8d6ebe0409d759a5ca25cf9627f20e39f1e651", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEGlGWmjD5ZolO0OG3Y9p83SolipqdbvsB\nlBnBUv2JgilUiel/LY1uvgQJ11mlyiXPlifyDjnx5lE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04909bb6c47b981b1eb3ad78d6ad6b04791f9952429f98a01416b778fd6c38107d55d28e37493d22e2aa2a4c66c9da2cc90be2202278870f92", "wx": "00909bb6c47b981b1eb3ad78d6ad6b04791f9952429f98a01416b778fd", "wy": "6c38107d55d28e37493d22e2aa2a4c66c9da2cc90be2202278870f92"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004909bb6c47b981b1eb3ad78d6ad6b04791f9952429f98a01416b778fd6c38107d55d28e37493d22e2aa2a4c66c9da2cc90be2202278870f92", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkJu2xHuYGx6zrXjWrWsEeR+ZUkKfmKAU\nFrd4/Ww4EH1V0o43ST0i4qoqTGbJ2izJC+IgIniHD5I=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eec9dbb6fe5ed5c8e4f8309cd81d506005efd52dca73e8874957db2c840f6693e77f92088c6e411075ff15817ca0f6e669a295d01d2442bd", "wx": "00eec9dbb6fe5ed5c8e4f8309cd81d506005efd52dca73e8874957db2c", "wy": "00840f6693e77f92088c6e411075ff15817ca0f6e669a295d01d2442bd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eec9dbb6fe5ed5c8e4f8309cd81d506005efd52dca73e8874957db2c840f6693e77f92088c6e411075ff15817ca0f6e669a295d01d2442bd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7snbtv5e1cjk+DCc2B1QYAXv1S3Kc+iH\nSVfbLIQPZpPnf5IIjG5BEHX/FYF8oPbmaaKV0B0kQr0=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a4b5e9304fb04bc6257fed45083fc7f50aacffb962d42b3b3a6c617758aa38fe0aa034025e4b7ed045eea3edad0a5ece26bfa7441239f521", "wx": "00a4b5e9304fb04bc6257fed45083fc7f50aacffb962d42b3b3a6c6177", "wy": "58aa38fe0aa034025e4b7ed045eea3edad0a5ece26bfa7441239f521"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a4b5e9304fb04bc6257fed45083fc7f50aacffb962d42b3b3a6c617758aa38fe0aa034025e4b7ed045eea3edad0a5ece26bfa7441239f521", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEpLXpME+wS8Ylf+1FCD/H9Qqs/7li1Cs7\nOmxhd1iqOP4KoDQCXkt+0EXuo+2tCl7OJr+nRBI59SE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047fef8ed425081537adbe4773037d77ccec1a3dae490c46360c92d067e5309792680df204f3ccaf51d9e73543f21e519377b504885b6e55c5", "wx": "7fef8ed425081537adbe4773037d77ccec1a3dae490c46360c92d067", "wy": "00e5309792680df204f3ccaf51d9e73543f21e519377b504885b6e55c5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047fef8ed425081537adbe4773037d77ccec1a3dae490c46360c92d067e5309792680df204f3ccaf51d9e73543f21e519377b504885b6e55c5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEf++O1CUIFTetvkdzA313zOwaPa5JDEY2\nDJLQZ+Uwl5JoDfIE88yvUdnnNUPyHlGTd7UEiFtuVcU=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e135428f7047f7eee01b2377ac2eb041d24637f40977b11b24f2904d9dc", "wx": "00d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e13", "wy": "5428f7047f7eee01b2377ac2eb041d24637f40977b11b24f2904d9dc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e135428f7047f7eee01b2377ac2eb041d24637f40977b11b24f2904d9dc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1MOMDfb3dD5XfOPQVKMuhLKnQY0angCg\nodMOE1Qo9wR/fu4Bsjd6wusEHSRjf0CXexGyTykE2dw=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c21cdafc19b3c56e71933d3692d76c92c00cd08d146b2ed4c03525393", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e13abd708fb808111fe4dc8853d14fbe2da9c80bf6884ee4db0d6fb2625", "wx": "00d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e13", "wy": "00abd708fb808111fe4dc8853d14fbe2da9c80bf6884ee4db0d6fb2625"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d4c38c0df6f7743e577ce3d054a32e84b2a7418d1a9e00a0a1d30e13abd708fb808111fe4dc8853d14fbe2da9c80bf6884ee4db0d6fb2625", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1MOMDfb3dD5XfOPQVKMuhLKnQY0angCg\nodMOE6vXCPuAgRH+TciFPRT74tqcgL9ohO5NsNb7JiU=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "duplication bug", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c21cdafc19b3c56e71933d3692d76c92c00cd08d146b2ed4c03525393", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ebc69137db89c0189696ee75ff03706b0d939639bb64e220d70ecee623a446d65b083da18cb14cb6a9e57f007558386065726ea34feab573", "wx": "00ebc69137db89c0189696ee75ff03706b0d939639bb64e220d70ecee6", "wy": "23a446d65b083da18cb14cb6a9e57f007558386065726ea34feab573"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ebc69137db89c0189696ee75ff03706b0d939639bb64e220d70ecee623a446d65b083da18cb14cb6a9e57f007558386065726ea34feab573", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE68aRN9uJwBiWlu51/wNwaw2Tljm7ZOIg\n1w7O5iOkRtZbCD2hjLFMtqnlfwB1WDhgZXJuo0/qtXM=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ff8f64c0c0f7f0e81d205b67a1c3bccf0c3dcf3bfdfdc80a61471e80a0cbbf29ebedf5381016937ad91335c5801bbe6fd4a1ee6199295601", "wx": "00ff8f64c0c0f7f0e81d205b67a1c3bccf0c3dcf3bfdfdc80a61471e80", "wy": "00a0cbbf29ebedf5381016937ad91335c5801bbe6fd4a1ee6199295601"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ff8f64c0c0f7f0e81d205b67a1c3bccf0c3dcf3bfdfdc80a61471e80a0cbbf29ebedf5381016937ad91335c5801bbe6fd4a1ee6199295601", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/49kwMD38OgdIFtnocO8zww9zzv9/cgK\nYUcegKDLvynr7fU4EBaTetkTNcWAG75v1KHuYZkpVgE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0411c0f94fc2820ead7b14208d0620a35f376f1c10b6af16060454b048b004d5322db3039c7fdd4888fdc0caffae81edbe53e80cd05df210b9", "wx": "11c0f94fc2820ead7b14208d0620a35f376f1c10b6af16060454b048", "wy": "00b004d5322db3039c7fdd4888fdc0caffae81edbe53e80cd05df210b9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000411c0f94fc2820ead7b14208d0620a35f376f1c10b6af16060454b048b004d5322db3039c7fdd4888fdc0caffae81edbe53e80cd05df210b9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEEcD5T8KCDq17FCCNBiCjXzdvHBC2rxYG\nBFSwSLAE1TItswOcf91IiP3Ayv+uge2+U+gM0F3yELk=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043e4fa16464ba762f06e7cec2fcbf66269ff742c10a53361217f2053e706b308fa36b5de586523d32244eea63a4d86f215930eae2bf99808e", "wx": "3e4fa16464ba762f06e7cec2fcbf66269ff742c10a53361217f2053e", "wy": "706b308fa36b5de586523d32244eea63a4d86f215930eae2bf99808e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043e4fa16464ba762f06e7cec2fcbf66269ff742c10a53361217f2053e706b308fa36b5de586523d32244eea63a4d86f215930eae2bf99808e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPk+hZGS6di8G587C/L9mJp/3QsEKUzYS\nF/IFPnBrMI+ja13lhlI9MiRO6mOk2G8hWTDq4r+ZgI4=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044825b311ea6b6ad86eb6f8fe9d29eead7a7a93daafaffae356a785b473160b436b4894f5ee3f50288dbdb66fe1c08f94f677ecdc5eee6e44", "wx": "4825b311ea6b6ad86eb6f8fe9d29eead7a7a93daafaffae356a785b4", "wy": "73160b436b4894f5ee3f50288dbdb66fe1c08f94f677ecdc5eee6e44"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044825b311ea6b6ad86eb6f8fe9d29eead7a7a93daafaffae356a785b473160b436b4894f5ee3f50288dbdb66fe1c08f94f677ecdc5eee6e44", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESCWzEeprathutvj+nSnurXp6k9qvr/rj\nVqeFtHMWC0NrSJT17j9QKI29tm/hwI+U9nfs3F7ubkQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04235c610afcdc0a22f84d753b1f7b9cee388f8f5d68127046500b4f1a605e49168429c44e190d3612f355bd7e63978fb6c9a61dcd53b13821", "wx": "235c610afcdc0a22f84d753b1f7b9cee388f8f5d68127046500b4f1a", "wy": "605e49168429c44e190d3612f355bd7e63978fb6c9a61dcd53b13821"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004235c610afcdc0a22f84d753b1f7b9cee388f8f5d68127046500b4f1a605e49168429c44e190d3612f355bd7e63978fb6c9a61dcd53b13821", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEI1xhCvzcCiL4TXU7H3uc7jiPj11oEnBG\nUAtPGmBeSRaEKcROGQ02EvNVvX5jl4+2yaYdzVOxOCE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049f77906d353c1b862ec4794687c69fa506405c4d0b57f4ef8491dba7ce9e810af65edf1ae583e6f9d6f2ddbc01365e1e744f2987af5527e0", "wx": "009f77906d353c1b862ec4794687c69fa506405c4d0b57f4ef8491dba7", "wy": "00ce9e810af65edf1ae583e6f9d6f2ddbc01365e1e744f2987af5527e0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049f77906d353c1b862ec4794687c69fa506405c4d0b57f4ef8491dba7ce9e810af65edf1ae583e6f9d6f2ddbc01365e1e744f2987af5527e0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEn3eQbTU8G4YuxHlGh8afpQZAXE0LV/Tv\nhJHbp86egQr2Xt8a5YPm+dby3bwBNl4edE8ph69VJ+A=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0431699a0079058d604ed7f87c9aeb44bf1978527bfe01025a0cdd2a0beb919883753f880b47d06a1acccdf7d77bf984fa48f26c959b12fe7a", "wx": "31699a0079058d604ed7f87c9aeb44bf1978527bfe01025a0cdd2a0b", "wy": "00eb919883753f880b47d06a1acccdf7d77bf984fa48f26c959b12fe7a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000431699a0079058d604ed7f87c9aeb44bf1978527bfe01025a0cdd2a0beb919883753f880b47d06a1acccdf7d77bf984fa48f26c959b12fe7a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMWmaAHkFjWBO1/h8mutEvxl4Unv+AQJa\nDN0qC+uRmIN1P4gLR9BqGszN99d7+YT6SPJslZsS/no=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043ffe7230477fe2ba4c3fd54ab1da6fe0c29eaa5b6c18982eb7038a2f3911699ad8e6c713a7ddb2c7d569f1ae648b1400115e416b2be74c36", "wx": "3ffe7230477fe2ba4c3fd54ab1da6fe0c29eaa5b6c18982eb7038a2f", "wy": "3911699ad8e6c713a7ddb2c7d569f1ae648b1400115e416b2be74c36"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043ffe7230477fe2ba4c3fd54ab1da6fe0c29eaa5b6c18982eb7038a2f3911699ad8e6c713a7ddb2c7d569f1ae648b1400115e416b2be74c36", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEP/5yMEd/4rpMP9VKsdpv4MKeqltsGJgu\ntwOKLzkRaZrY5scTp92yx9Vp8a5kixQAEV5BayvnTDY=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044abf00bb45592cbfde38b5381d1847bd8816d9113a99b18b7d1a0e071f47d0c50e5506c06af9e4db68ad58818fff05df0116048a0418b951", "wx": "4abf00bb45592cbfde38b5381d1847bd8816d9113a99b18b7d1a0e07", "wy": "1f47d0c50e5506c06af9e4db68ad58818fff05df0116048a0418b951"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044abf00bb45592cbfde38b5381d1847bd8816d9113a99b18b7d1a0e071f47d0c50e5506c06af9e4db68ad58818fff05df0116048a0418b951", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESr8Au0VZLL/eOLU4HRhHvYgW2RE6mbGL\nfRoOBx9H0MUOVQbAavnk22itWIGP/wXfARYEigQYuVE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0417564764dde6c5d5bc1ff0cc65478522aa0492cca7ecde374e5019ecc17e0cd326b5a30a5131097da640ea1f81b577ea98df9e5906574361", "wx": "17564764dde6c5d5bc1ff0cc65478522aa0492cca7ecde374e5019ec", "wy": "00c17e0cd326b5a30a5131097da640ea1f81b577ea98df9e5906574361"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000417564764dde6c5d5bc1ff0cc65478522aa0492cca7ecde374e5019ecc17e0cd326b5a30a5131097da640ea1f81b577ea98df9e5906574361", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEF1ZHZN3mxdW8H/DMZUeFIqoEksyn7N43\nTlAZ7MF+DNMmtaMKUTEJfaZA6h+BtXfqmN+eWQZXQ2E=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ad728313f562dc2284a6f6c4a102c569c3bc730279248b15d75df1680e900506b8e46beb36600bf2e2a0bdda494dfe3fbb4221b4587938d6", "wx": "00ad728313f562dc2284a6f6c4a102c569c3bc730279248b15d75df168", "wy": "0e900506b8e46beb36600bf2e2a0bdda494dfe3fbb4221b4587938d6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ad728313f562dc2284a6f6c4a102c569c3bc730279248b15d75df1680e900506b8e46beb36600bf2e2a0bdda494dfe3fbb4221b4587938d6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErXKDE/Vi3CKEpvbEoQLFacO8cwJ5JIsV\n113xaA6QBQa45GvrNmAL8uKgvdpJTf4/u0IhtFh5ONY=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0438b185b1b7d7497db0ebb1f0998575706bdcc0c6b4301c5c99083210ea4d43854b92d8c3aba8163803893095f448fd6beccf5ba90e6d075e", "wx": "38b185b1b7d7497db0ebb1f0998575706bdcc0c6b4301c5c99083210", "wy": "00ea4d43854b92d8c3aba8163803893095f448fd6beccf5ba90e6d075e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000438b185b1b7d7497db0ebb1f0998575706bdcc0c6b4301c5c99083210ea4d43854b92d8c3aba8163803893095f448fd6beccf5ba90e6d075e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEOLGFsbfXSX2w67HwmYV1cGvcwMa0MBxc\nmQgyEOpNQ4VLktjDq6gWOAOJMJX0SP1r7M9bqQ5tB14=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "extreme value for k", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c5731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf7021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 357, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00a8ce483b42fb3461047c96ca00d0c226ee2850b79192346c7ce37d46021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c5731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf7021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 359, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00a8ce483b42fb3461047c96ca00d0c226ee2850b79192346c7ce37d46021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "pseudorandom signature", "msg": "", "sig": "303c021c2a8e4fc8c813be0459fe6fd5a449fcd27118121180f37f96857498fb021c487fabaabee79f667da6505c5c171d299732d37784fd73775dfd3db3", "result": "valid", "flags": []}, {"tcId": 361, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303d021c1e5a15190a1d2631f2222d704489041f72e0c50548fd526eda975e1f021d00ebff8dcb8c1134ac5dfb271182495590fc8fd8ea7b0a4f7f8ec78900", "result": "valid", "flags": []}, {"tcId": 362, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303c021c295e399cbf4904e22850240598e009d6b40d6391e370aba5a04042d9021c2a0c5841560271a38c7b7c3bb064990e204bae693e2171a246942d40", "result": "valid", "flags": []}, {"tcId": 363, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303e021d00f04e2dc4d8f01de69a5bae38d0869be1926e0ca75a641f2fcd7784d7021d009613012233db115ba180f7363aafbde09dc0a5ebb6707613838a1413", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c4007008e430202f9577e43a0b21ffd169c046d5bf35c2b530115a618021d00d845d27c3ab6d1f81881f1c5f980d1c25844a484a87c99058d76e3b5", "result": "valid", "flags": []}, {"tcId": 365, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00917e785e5e0432f597d10dc400725a0344cf4856be31390573a1eaf3021d00b85d30901195e05cbef0e282a079f5c229eae8eb282be9176df9ed88", "result": "valid", "flags": []}, {"tcId": 366, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c3e4f9883f7acaadf2a076234fa99fd25a5d8369fb7766aa5b2eb3fd2021c42cb3e2eb9f5431fca4a7ec83637aca92fbebe8afa4ab4bced1088b9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00f33a24a2cd64f41d981ffa97c24cb73d28379146824c8d4c77c37f68021c7019a27bc87ab06d3a312b3027215104044ab9a917de5542071a5702", "result": "valid", "flags": []}, {"tcId": 368, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00ff961228b94551c201bb61c15286d119e02db2f45cdc66979debb3a1021c34a490221e2bf3097d369f3fcf9c6507a56780051f54ff961f773e20", "result": "valid", "flags": []}, {"tcId": 369, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d00ddced52bca9640b1a1a7f85bb12d8cf36f0cd60b27ecddd2a944dc49021d00826b3c1a839da54a8beeece69da8681c643fec79394d982dbf0c6837", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00d8f0e29a424ba0a53ddcb8f48fc4b65019d01e7e8dac3ff63847dcd3021c62ec0f1f7b36512ee98cae1fac6bae7505e84e6eb279623e064fa094", "result": "valid", "flags": []}, {"tcId": 371, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00f5df25249adba5717354fea143b93793f32ea8ba31cd377f9bbb6eea021c61376f02d5e7517f1cd2a5c36c452a76decb282daebfeea5a5b32e12", "result": "valid", "flags": []}, {"tcId": 372, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c4b9b6fefe18c73272ee66ab96fe340b3835b1f63f903b1ac76ba3457021c0c580a65c53b48d1180f0985fe0f9d5f57cf7eb5e572b9714411aa98", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c6f72f5934d17a126d0d6fe0afa599588c51963023ce93c312ec77baf021d00d5b4b96943f585cd1568a617e7c47b9dfaddfb58bec13c57c15a0a10", "result": "valid", "flags": []}, {"tcId": 374, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c1579cbc71f2c067d1449c5eaf32e121eca057a35f375bdc93f771a3c021d00c5e865acd21b480a65150e7010e5072cc5aac16e3316fa8fd32a078f", "result": "valid", "flags": []}, {"tcId": 375, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00ee501caf390634fc3757ead8e3f62e5c8e86c0448289ae5dffc6a30f021c36d39f3560c5afa05787248787235e8edd8c42e713ed43adfb82879a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c714796f7fe64f4de33bdd8eaeff4e7e3a8ea9664a0d3249e07bdec4f021d00ec82e64e1c6f652d1198c2996f893222d920d36d7e38507e86f37357", "result": "valid", "flags": []}, {"tcId": 377, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00b010d489c9661c1a283537428868c4b5bb29d9503de697ac574d22fd021d00ec677ca1c8b12eb0304cf090d952c63801ed9c82d751dbd76d4bc18a", "result": "valid", "flags": []}, {"tcId": 378, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00a8fb6a1a558cb2221560204babedf6c44d48109ebae78d27e784056b021c1e532d50e0b6721e9345248fcc37593077c4bab575b55d216fa2a3f2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d008085d6b7f3d979356f2a213bb243746ea678e96a705e6893bf2a51f7021d00b4d8be5c3f996ed40af1024b3a8c9f65c90efb41d8c5987aca115524", "result": "valid", "flags": []}, {"tcId": 380, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c5f1adbfcde7a0a929f43ba30e0d88ea2ada5b4a8bbf55336eb228fdf021d00eac90d6f0679bdeefc4284027c5e527cab4cb27619217783fb2c421e", "result": "valid", "flags": []}, {"tcId": 381, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c17b20a24457e94f8b882a4fc99692c2c44b5c853b9c234d03ad473b3021d00b03bf47ea4533e86229b96c65265423f89daadb9f3f69b0ee37c21bf", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00faccc9c38ebfa8e2748d32f8c41cc291c4a2b27cad4a411e5119d19b021c70e5ba90a65e03515594c919f17eac4d809596e6a2735b617b3852ab", "result": "valid", "flags": []}, {"tcId": 383, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d008f8e90c2a830534593bdf1e5614ed9ca75f8253956d17579a6a4532d021d00800c2d43eea0b7211f739f4e75ca5677ea0efb109b094aac354af676", "result": "valid", "flags": []}, {"tcId": 384, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c2728cc303c3ed54a05a371f16add7c2da6d2277b80a932b7b9749df7021d00ad2c93f83723c19e20385fab9116188114a1280be7d1fd9a661e5e77", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c3a224d4baaa5d5c332a3d62043b1aaf66b029880010c839c5c033aa3021c2de87b37b0305cf6112e0ac94200118ff493c0a379f4beb0b6602e02", "result": "valid", "flags": []}, {"tcId": 386, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d009159aa74a88b5917809605e14736a00e92f4aacda2b87dde950a5ff8021c4bd456c6914cf21c88be0bc9c64a3d0d7b2cbd5c776297fea3a12f5e", "result": "valid", "flags": []}, {"tcId": 387, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d008b21ed26d9455613a0431edb41d4227fc5711d1a6c70e4e0de801737021c2592becb967e25d234a2516986c18a1c687b2969db7178cd204d30c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c5c7c63a63a69787bfc469ab70a6856f7f322447f9ce74573d0f94d2d021c3e80ff0a9fbd8c11a08d7dc02237e435838de2d2b51eec1156e667d1", "result": "valid", "flags": []}, {"tcId": 389, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c036b3f33ccc347d6f0ae2b9e79ef85351d06e61870b1cb08054c909d021d009a27fe9d699cf6e2c2ed2ed70c9692f1f6b96fc5b4e50d9926a752ad", "result": "valid", "flags": []}, {"tcId": 390, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c3131bcf930d1136df1436c4780c095e00170cecb929f6ee71c7458f7021d00a1c6f0f97cad156078d248fc7e7974045d27888e8f6528af66047faf", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c1023fa4d5dcedab53a8fdfe2a8f8da941be08c63146e4ba2ed87bd4d021c367a88e393fd1ee4ec925f7f920d4c3fe3ba48edbd253261ec706c5e", "result": "valid", "flags": []}, {"tcId": 392, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00b525fb6204d3d60fd406b1066f0ae4bd7ec75b0adfd807de8201f10a021d00faed757f5a68d8a8338788ea531d6f7c85a88c9a8bae7f696ebb6eba", "result": "valid", "flags": []}, {"tcId": 393, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00e472e504ef4b293b7f4a6cc99ba33a702f35593f49cb284137776b44021d00c1efe440463fde3b604d48319e0ddb93261ae608d009942a01933241", "result": "valid", "flags": []}]}]}