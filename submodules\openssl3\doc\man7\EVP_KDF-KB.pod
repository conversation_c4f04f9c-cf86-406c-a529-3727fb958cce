=pod

=head1 NAME

EVP_KDF-KB - The Key-Based EVP_KDF implementation

=head1 DESCRIPTION

The EVP_KDF-KB algorithm implements the Key-Based key derivation function
(KBKDF).  KBKDF derives a key from repeated application of a keyed MAC to an
input secret (and other optional values).

=head2 Identity

"KBKDF" is the name for this implementation; it can be used with the
EVP_KDF_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "mode" (B<OSSL_KDF_PARAM_MODE>) <UTF8 string>

The mode parameter determines which flavor of KBKDF to use - currently the
choices are "counter" and "feedback". "counter" is the default, and will be
used if unspecified.

=item "mac" (B<OSSL_KDF_PARAM_MAC>) <UTF8 string>

The value is either CMAC, HMAC, KMAC128 or KMAC256.

=item "digest" (B<OSSL_KDF_PARAM_DIGEST>) <UTF8 string>

=item "cipher" (B<OSSL_KDF_PARAM_CIPHER>) <UTF8 string>

=item "properties" (B<OSSL_KDF_PARAM_PROPERTIES>) <UTF8 string>

=item "key" (B<OSSL_KDF_PARAM_KEY>) <octet string>

=item "salt" (B<OSSL_KDF_PARAM_SALT>) <octet string>

=item "info (B<OSSL_KDF_PARAM_INFO>) <octet string>

=item "seed" (B<OSSL_KDF_PARAM_SEED>) <octet string>

The seed parameter is unused in counter mode.

=item "use-l" (B<OSSL_KDF_PARAM_KBKDF_USE_L>) <integer>

Set to B<0> to disable use of the optional Fixed Input data 'L' (see SP800-108).
The default value of B<1> will be used if unspecified.

=item "use-separator" (B<OSSL_KDF_PARAM_KBKDF_USE_SEPARATOR>) <integer>

Set to B<0> to disable use of the optional Fixed Input data 'zero separator'
(see SP800-108) that is placed between the Label and Context.
The default value of B<1> will be used if unspecified.

=item "r" (B<OSSL_KDF_PARAM_KBKDF_R>) <integer>

Set the fixed value 'r', indicating the length of the counter in bits.

Supported values are B<8>, B<16>, B<24>, and B<32>.
The default value of B<32> will be used if unspecified.

=back

Depending on whether mac is CMAC or HMAC, either digest or cipher is required
(respectively) and the other is unused. They are unused for KMAC128 and KMAC256.

The parameters key, salt, info, and seed correspond to KI, Label, Context, and
IV (respectively) in SP800-108.  As in that document, salt, info, and seed are
optional and may be omitted.

"mac", "digest", cipher" and "properties" are described in
L<EVP_KDF(3)/PARAMETERS>.

=head1 NOTES

A context for KBKDF can be obtained by calling:

 EVP_KDF *kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
 EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);

The output length of an KBKDF is specified via the C<keylen>
parameter to the L<EVP_KDF_derive(3)> function.

Note that currently OpenSSL only implements counter and feedback modes.  Other
variants may be supported in the future.

=head1 EXAMPLES

This example derives 10 bytes using COUNTER-HMAC-SHA256, with KI "secret",
Label "label", and Context "context".

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[6], *p = params;

 kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                         "SHA2-256", 0);
 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
                                         "HMAC", 0);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
                                          "secret", strlen("secret"));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                          "label", strlen("label"));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "context", strlen("context"));
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
     error("EVP_KDF_derive");

 EVP_KDF_CTX_free(kctx);

This example derives 10 bytes using FEEDBACK-CMAC-AES256, with KI "secret",
Label "label", and IV "sixteen bytes iv".

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[8], *p = params;
 unsigned char *iv = "sixteen bytes iv";

 kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_CIPHER, "AES256", 0);
 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC, "CMAC", 0);
 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MODE, "FEEDBACK", 0);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
                                          "secret", strlen("secret"));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                          "label", strlen("label"));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "context", strlen("context"));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SEED,
                                          iv, strlen(iv));
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
     error("EVP_KDF_derive");

 EVP_KDF_CTX_free(kctx);

=head1 CONFORMING TO

NIST SP800-108, IETF RFC 6803, IETF RFC 8009.

=head1 SEE ALSO

L<EVP_KDF(3)>,
L<EVP_KDF_CTX_free(3)>,
L<EVP_KDF_CTX_get_kdf_size(3)>,
L<EVP_KDF_derive(3)>,
L<EVP_KDF(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

Support for KMAC was added in OpenSSL 3.1.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.
Copyright 2019 Red Hat, Inc.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
