=pod

=head1 NAME

CA.pl - friendlier interface for OpenSSL certificate programs

=head1 SYNOPSIS

B<CA.pl>
B<-?> |
B<-h> |
B<-help>

B<CA.pl>
B<-newcert> |
B<-newreq> |
B<-newreq-nodes> |
B<-xsign> |
B<-sign> |
B<-signCA> |
B<-signcert> |
B<-crl> |
B<-newca>
[B<-extra-cmd> extra-params]

B<CA.pl> B<-pkcs12> [B<-extra-pkcs12> extra-params] [B<certname>]

B<CA.pl> B<-verify> [B<-extra-verify> extra-params] B<certfile>...

B<CA.pl> B<-revoke> [B<-extra-ca> extra-params] B<certfile> [B<reason>]

=head1 DESCRIPTION

The B<CA.pl> script is a perl script that supplies the relevant command line
arguments to the B<openssl> command for some common certificate operations.
It is intended to simplify the process of certificate creation and management
by the use of some simple options.

=head1 OPTIONS

=over 4

=item B<?>, B<-h>, B<-help>

Prints a usage message.

=item B<-newcert>

Creates a new self signed certificate. The private key is written to the file
"newkey.pem" and the request written to the file "newreq.pem".
This argument invokes B<openssl req> command.

=item B<-newreq>

Creates a new certificate request. The private key is written to the file
"newkey.pem" and the request written to the file "newreq.pem".
Executes B<openssl req> command below the hood.

=item B<-newreq-nodes>

Is like B<-newreq> except that the private key will not be encrypted.
Uses B<openssl req> command.

=item B<-newca>

Creates a new CA hierarchy for use with the B<ca> program (or the B<-signcert>
and B<-xsign> options). The user is prompted to enter the filename of the CA
certificates (which should also contain the private key) or by hitting ENTER
details of the CA will be prompted for. The relevant files and directories
are created in a directory called "demoCA" in the current directory.
B<openssl req> and B<openssl ca> commands are get invoked.

=item B<-pkcs12>

Create a PKCS#12 file containing the user certificate, private key and CA
certificate. It expects the user certificate and private key to be in the
file "newcert.pem" and the CA certificate to be in the file demoCA/cacert.pem,
it creates a file "newcert.p12". This command can thus be called after the
B<-sign> option. The PKCS#12 file can be imported directly into a browser.
If there is an additional argument on the command line it will be used as the
"friendly name" for the certificate (which is typically displayed in the browser
list box), otherwise the name "My Certificate" is used.
Delegates work to B<openssl pkcs12> command.

=item B<-sign>, B<-signcert>, B<-xsign>

Calls the B<ca> program to sign a certificate request. It expects the request
to be in the file "newreq.pem". The new certificate is written to the file
"newcert.pem" except in the case of the B<-xsign> option when it is written
to standard output. Leverages B<openssl ca> command.

=item B<-signCA>

This option is the same as the B<-sign> option except it uses the
configuration file section B<v3_ca> and so makes the signed request a
valid CA certificate. This is useful when creating intermediate CA from
a root CA.  Extra params are passed on to B<openssl ca> command.

=item B<-signcert>

This option is the same as B<-sign> except it expects a self signed certificate
to be present in the file "newreq.pem".
Extra params are passed on to B<openssl x509> and B<openssl ca> commands.

=item B<-crl>

Generate a CRL. Executes B<openssl ca> command.

=item B<-revoke certfile [reason]>

Revoke the certificate contained in the specified B<certfile>. An optional
reason may be specified, and must be one of: B<unspecified>,
B<keyCompromise>, B<CACompromise>, B<affiliationChanged>, B<superseded>,
B<cessationOfOperation>, B<certificateHold>, or B<removeFromCRL>.
Leverages B<openssl ca> command.

=item B<-verify>

Verifies certificates against the CA certificate for "demoCA". If no
certificates are specified on the command line it tries to verify the file
"newcert.pem".  Invokes B<openssl verify> command.

=item B<-extra-req> | B<-extra-ca> | B<-extra-pkcs12> | B<-extra-x509> | B<-extra-verify> <extra-params>

The purpose of these parameters is to allow optional parameters to be supplied
to B<openssl> that this command executes. The B<-extra-cmd> are specific to the
option being used and the B<openssl> command getting invoked. For example
when this command invokes B<openssl req> extra parameters can be passed on
with the B<-extra-req> parameter. The
B<openssl> commands being invoked per option are documented below.
Users should consult B<openssl> command documentation for more information.

=back

=head1 EXAMPLES

Create a CA hierarchy:

 CA.pl -newca

Complete certificate creation example: create a CA, create a request, sign
the request and finally create a PKCS#12 file containing it.

 CA.pl -newca
 CA.pl -newreq
 CA.pl -sign
 CA.pl -pkcs12 "My Test Certificate"

=head1 DSA CERTIFICATES

Although the B<CA.pl> creates RSA CAs and requests it is still possible to
use it with DSA certificates and requests using the L<req(1)> command
directly. The following example shows the steps that would typically be taken.

Create some DSA parameters:

 openssl dsaparam -out dsap.pem 1024

Create a DSA CA certificate and private key:

 openssl req -x509 -newkey dsa:dsap.pem -keyout cacert.pem -out cacert.pem

Create the CA directories and files:

 CA.pl -newca

enter cacert.pem when prompted for the CA filename.

Create a DSA certificate request and private key (a different set of parameters
can optionally be created first):

 openssl req -out newreq.pem -newkey dsa:dsap.pem

Sign the request:

 CA.pl -sign

=head1 NOTES

Most of the filenames mentioned can be modified by editing the B<CA.pl> script.

If the demoCA directory already exists then the B<-newca> command will not
overwrite it and will do nothing. This can happen if a previous call using
the B<-newca> option terminated abnormally. To get the correct behaviour
delete the demoCA directory if it already exists.

Under some environments it may not be possible to run the B<CA.pl> script
directly (for example Win32) and the default configuration file location may
be wrong. In this case the command:

 perl -S CA.pl

can be used and the B<OPENSSL_CONF> environment variable changed to point to
the correct path of the configuration file.

The script is intended as a simple front end for the B<openssl> program for use
by a beginner. Its behaviour isn't always what is wanted. For more control over the
behaviour of the certificate commands call the B<openssl> command directly.

=head1 SEE ALSO

L<x509(1)>, L<ca(1)>, L<req(1)>, L<pkcs12(1)>,
L<config(5)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
