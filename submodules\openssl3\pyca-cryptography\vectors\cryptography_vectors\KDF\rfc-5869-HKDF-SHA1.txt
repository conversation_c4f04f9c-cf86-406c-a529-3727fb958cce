# A.4.  Test Case 4
# Basic test case with SHA-1

COUNT = 4

Hash = SHA-1
IKM  = 0b0b0b0b0b0b0b0b0b0b0b
salt = 000102030405060708090a0b0c
info = f0f1f2f3f4f5f6f7f8f9
L    = 42

PRK  = 9b6c18c432a7bf8f0e71c8eb88f4b30baa2ba243
OKM  = 085a01ea1b10f36933068b56efa5ad81a4f14b822f5b091568a9cdd4f155fda2c22e422478d305f3f896

# A.5.  Test Case 5
# Test with SHA-1 and longer inputs/outputs

COUNT = 5

Hash = SHA-1
IKM  = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
salt = 606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
info = b0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
L    = 82

PRK  = 8adae09a2a307059478d309b26c4115a224cfaf6
OKM  = 0bd770a74d1160f7c9f12cd5912a06ebff6adcae899d92191fe4305673ba2ffe8fa3f1a4e5ad79f3f334b3b202b2173c486ea37ce3d397ed034c7f9dfeb15c5e927336d0441f4c4300e2cff0d0900b52d3b4

# A.6.  Test Case 6
# Test with SHA-1 and zero-length salt/info

COUNT = 6

Hash = SHA-1
IKM  = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
salt =
info =
L    = 42

PRK  = da8c8a73c7fa77288ec6f5e7c297786aa0d32d01
OKM  = 0ac1af7002b3d761d1e55298da9d0506b9ae52057220a306e07b6b87e8df21d0ea00033de03984d34918

# A.7.  Test Case 7
# Test with SHA-1, salt not provided (defaults to HashLen zero octets),
# zero-length info

COUNT = 7

Hash = SHA-1
IKM  = 0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
salt =
info =
L    = 42

PRK  = 2adccada18779e7c2077ad2eb19d3f3e731385dd
OKM  = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48
