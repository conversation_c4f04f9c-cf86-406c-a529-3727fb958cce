=pod

=head1 NAME

OSSL_CORE_MAKE_FUNC,
SSL_OP_BIT,
EXT_UTF8STRING
- OpenSSL reserved symbols

=head1 SYNOPSIS

 #include <openssl/core_dispatch.h>

 #define OSSL_CORE_MAKE_FUNC(type,name,args)
 #define SSL_OP_BIT(n)
 #define EXT_UTF8STRING(nid)

=head1 DESCRIPTION

There are certain macros that may appear in OpenSSL header files that are
reserved for internal use. They should not be used by applications or assumed
to exist.

All the macros listed in the synopsis above are reserved.

=head1 RETURN VALUES

Not applicable.

=head1 HISTORY

The macros described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
