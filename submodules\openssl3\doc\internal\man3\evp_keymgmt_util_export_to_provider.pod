=pod

=head1 NAME

evp_keymgmt_util_export,
evp_keymgmt_util_export_to_provider,
evp_keymgmt_util_find_operation_cache,
evp_keymgmt_util_clear_operation_cache,
evp_keymgmt_util_cache_keydata,
evp_keymgmt_util_cache_keyinfo,
evp_keymgmt_util_fromdata,
OP_CACHE_ELEM
- internal KEYMGMT utility functions

=head1 SYNOPSIS

 #include "crypto/evp.h"

 typedef struct OP_CACHE_ELEM;

 int evp_keymgmt_util_export(const EVP_PKEY *pk, int selection,
                             OSSL_CALLBACK *export_cb, void *export_cbarg);
 void *evp_keymgmt_util_export_to_provider(EVP_PKEY *pk, EVP_KEYMGMT *keymgmt,
                                           int selection);
 OP_CACHE_ELEM *evp_keymgmt_util_find_operation_cache(EVP_PKEY *pk,
                                                      EVP_KEYMGMT *keymgmt,
                                                      int selection);
 int evp_keymgmt_util_clear_operation_cache(EVP_PKEY *pk, int locking);
 int evp_keymgmt_util_cache_keydata(EVP_PKEY *pk, EVP_KEYMGMT *keymgmt,
                                    void *keydata, int selection);
 void evp_keymgmt_util_cache_keyinfo(EVP_PKEY *pk);
 void *evp_keymgmt_util_fromdata(EVP_PKEY *target, EVP_KEYMGMT *keymgmt,
                                 int selection, const OSSL_PARAM params[]);

=head1 DESCRIPTION

evp_keymgmt_util_export() calls L<evp_keymgmt_export(3)> with the
I<keymgmt> and I<keydata> from I<pk>.  This is used as a
helper for L<EVP_PKEY_todata(3)>.

evp_keymgmt_util_export_to_provider() exports cached key material
(provider side key material) from the given key I<pk> to a provider
via a B<EVP_KEYMGMT> interface, if this hasn't already been done.
It maintains a cache of provider key references in I<pk> to keep track
of all provider side keys.

To export a legacy key, use L<evp_pkey_export_to_provider(3)> instead,
as this function ignores any legacy key data.

evp_keymgmt_util_find_operation_cache() finds
I<keymgmt> in I<pk>'s cache of provided keys for operations.
It should only be called while holding I<pk>'s lock (read or write).

evp_keymgmt_util_clear_operation_cache() can be used to explicitly
clear the cache of operation key references. If I<locking> is set to 1 then
then I<pk>'s lock will be obtained while doing the clear. Otherwise it will be
assumed that the lock has already been obtained or is not required.

evp_keymgmt_util_cache_keydata() can be used to add a provider key
object to a B<PKEY>.

evp_keymgmt_util_cache_keyinfo() can be used to get all kinds of
information from the provvider "origin" and save it in I<pk>'s
information cache.

evp_keymgmt_util_fromdata() can be used to add key object data to a
given key I<target> via a B<EVP_KEYMGMT> interface.  This is used as a
helper for L<EVP_PKEY_fromdata(3)>.

In all functions that take a I<selection> argument, the selection is used to
constraint the information requested on export. It is also used in the cache
so that key data is guaranteed to contain all the information requested in
the selection.

=head1 RETURN VALUES

evp_keymgmt_export_to_provider() and evp_keymgmt_util_fromdata()
return a pointer to the appropriate provider side key (created or
found again), or NULL on error.

evp_keymgmt_util_find_operation_cache() returns a pointer to the
operation cache slot.  If I<keymgmt> is NULL, or if there is no slot
with a match for I<keymgmt>, NULL is returned.

evp_keymgmt_util_cache_keydata() and evp_keymgmt_util_clear_operation_cache()
return 1 on success or 0 otherwise.

=head1 NOTES

"Legacy key" is the term used for any key that has been assigned to an
B<EVP_PKEY> with EVP_PKEY_assign_RSA() and similar functions.

=head1 SEE ALSO

L<EVP_PKEY_ASN1_METHOD(3)>, L<EVP_PKEY_assign_RSA(3)>

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
