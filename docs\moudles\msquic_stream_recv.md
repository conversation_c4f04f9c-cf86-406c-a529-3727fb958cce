以下是根据 `src/core/stream_recv.c` 生成的 Markdown 格式文档：

# `stream_recv.c` 文档

## 概述
此文件包含了流的接收特定逻辑。流负责管理应用程序数据的接收队列，该文件聚焦于接收方面的操作。

## 函数列表

### 1. `QuicStreamReceiveCompletePending`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamReceiveCompletePending(_In_ QUIC_STREAM* Stream);
```
- **功能**：完成流的接收挂起操作。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
- **实现步骤**：
  1. 设置 `ReceiveCompleteOperation` 指针。
  2. 处理接收完成长度。
  3. 调用 `QuicStreamReceiveComplete` 并根据结果调用 `QuicStreamRecvFlush`。
  4. 释放操作引用。

### 2. `QuicStreamRecvSetEnabledState`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS QuicStreamRecvSetEnabledState(_In_ QUIC_STREAM* Stream, _In_ BOOLEAN NewRecvEnabled);
```
- **功能**：设置流的接收启用状态。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `NewRecvEnabled`：新的接收启用状态。
- **实现步骤**：
  1. 检查流的状态，若处于无效状态则返回 `QUIC_STATUS_INVALID_STATE`。
  2. 若新状态与当前状态不同，更新状态。
  3. 若满足特定条件，记录跟踪事件并调用 `QuicStreamRecvQueueFlush`。
  4. 返回 `QUIC_STATUS_SUCCESS`。

### 3. `QuicStreamOnBytesDelivered`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamOnBytesDelivered(_In_ QUIC_STREAM* Stream, _In_ uint64_t BytesDelivered);
```
- **功能**：处理字节交付事件。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `BytesDelivered`：交付的字节数。
- **实现步骤**：
  1. 更新接收窗口交付字节数和连接发送的最大数据。
  2. 检查是否达到接收缓冲区排水阈值，若达到则可能增加虚拟缓冲区长度。
  3. 更新最大允许接收偏移量并设置发送标志。

### 4. `QuicStreamProcessStreamFrame`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS QuicStreamProcessStreamFrame(_In_ QUIC_STREAM* Stream, _In_ BOOLEAN EncryptedWith0Rtt, _In_ const QUIC_STREAM_EX* Frame);
```
- **功能**：处理流帧。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `EncryptedWith0Rtt`：是否使用 0 - RTT 加密。
  - `Frame`：指向 `QUIC_STREAM_EX` 结构体的指针。
- **实现步骤**：
  1. 检查流的状态，若处于无效状态则记录错误并返回相应状态。
  2. 若帧长度不为 0，将数据写入接收缓冲区并更新相关统计信息。
  3. 若帧包含 FIN 标志，更新接收最大长度。
  4. 若有数据准备好交付，设置相应标志并调用 `QuicStreamRecvQueueFlush`。
  5. 处理错误情况，记录警告并调用 `QuicConnTransportError`。

### 5. `QuicStreamRecv`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS QuicStreamRecv(_In_ QUIC_STREAM* Stream, _In_ QUIC_RX_PACKET* Packet, _In_ QUIC_FRAME_TYPE FrameType, _In_ uint16_t BufferLength, _In_reads_bytes_(BufferLength) const uint8_t * const Buffer, _Inout_ uint16_t* Offset, _Inout_ BOOLEAN* UpdatedFlowControl);
```
- **功能**：处理流的接收操作。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `Packet`：指向 `QUIC_RX_PACKET` 结构体的指针。
  - `FrameType`：帧类型。
  - `BufferLength`：缓冲区长度。
  - `Buffer`：缓冲区指针。
  - `Offset`：偏移量指针。
  - `UpdatedFlowControl`：是否更新流控制标志指针。
- **实现步骤**：
  1. 根据帧类型进行不同的处理，如 `QUIC_FRAME_RESET_STREAM`、`QUIC_FRAME_STOP_SENDING` 等。
  2. 调用相应的处理函数，如 `QuicStreamProcessResetFrame`、`QuicStreamProcessStopSendingFrame` 等。
  3. 记录处理完成事件。
  4. 返回处理状态。

### 6. `QuicStreamReceiveComplete`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
BOOLEAN QuicStreamReceiveComplete(_In_ QUIC_STREAM* Stream, _In_ uint64_t BufferLength);
```
- **功能**：处理流接收完成事件。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `BufferLength`：缓冲区长度。
- **实现步骤**：
  1. 检查流的状态，若已停止接收或远程关闭则返回 `FALSE`。
  2. 回收应用程序消耗的缓冲区空间。
  3. 更新接收挂起长度和性能计数器。
  4. 根据接收状态决定是否继续处理数据。
  5. 若所有数据已交付，处理关闭事件。
  6. 返回处理结果。

### 7. `QuicStreamRecvShutdown`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamRecvShutdown(_In_ QUIC_STREAM* Stream, _In_ BOOLEAN Silent, _In_ QUIC_VAR_INT ErrorCode);
```
- **功能**：关闭流的接收方向。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `Silent`：是否静默关闭。
  - `ErrorCode`：错误代码。
- **实现步骤**：
  1. 若静默关闭，设置相应标志。
  2. 检查流的状态，若已关闭则直接退出。
  3. 禁用未来的接收事件。
  4. 若需要，处理重置帧。
  5. 记录接收状态事件。
  6. 若静默关闭，尝试完成关闭操作。

### 8. `QuicStreamRecvQueueFlush`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamRecvQueueFlush(_In_ QUIC_STREAM* Stream, _In_ BOOLEAN AllowInlineFlush);
```
- **功能**：将流的接收刷新操作加入队列。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `AllowInlineFlush`：是否允许内联刷新。
- **实现步骤**：
  1. 检查流的接收状态和数据是否准备好。
  2. 若允许内联刷新，直接调用 `QuicStreamRecvFlush`。
  3. 若刷新操作未排队，分配操作并加入队列。

### 9. `QuicStreamIndicatePeerSendAbortedEvent`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamIndicatePeerSendAbortedEvent(_In_ QUIC_STREAM* Stream, _In_ QUIC_VAR_INT ErrorCode);
```
- **功能**：通知应用程序对等方已中止发送路径。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `ErrorCode`：错误代码。
- **实现步骤**：
  1. 记录远程关闭重置事件。
  2. 创建并设置 `QUIC_STREAM_EVENT` 结构体。
  3. 记录指示事件并调用 `QuicStreamIndicateEvent`。

### 10. `QuicStreamProcessReliableResetFrame`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamProcessReliableResetFrame(_In_ QUIC_STREAM* Stream, _In_ QUIC_VAR_INT ErrorCode, _In_ QUIC_VAR_INT ReliableOffset);
```
- **功能**：处理可靠重置帧。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `ErrorCode`：错误代码。
  - `ReliableOffset`：可靠偏移量。
- **实现步骤**：
  1. 检查可靠重置流是否已协商，若未协商则记录错误并终止连接。
  2. 更新接收最大长度和相关标志。
  3. 若接收缓冲区偏移量已达到最大长度，通知应用程序并关闭接收方向。
  4. 若还有数据待交付，缓存错误代码。

### 11. `QuicStreamProcessResetFrame`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamProcessResetFrame(_In_ QUIC_STREAM* Stream, _In_ uint64_t FinalSize, _In_ QUIC_VAR_INT ErrorCode);
```
- **功能**：处理重置帧。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `FinalSize`：最终大小。
  - `ErrorCode`：错误代码。
- **实现步骤**：
  1. 设置远程关闭重置标志。
  2. 若未确认关闭，更新相关标志并检查最终大小是否合理。
  3. 根据最终大小更新流控制统计信息。
  4. 记录接收状态事件。
  5. 若未发送停止发送标志，通知应用程序。
  6. 清除不应发送的标志。
  7. 尝试完成关闭操作。

### 12. `QuicStreamProcessStopSendingFrame`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicStreamProcessStopSendingFrame(_In_ QUIC_STREAM* Stream, _In_ QUIC_VAR_INT ErrorCode);
```
- **功能**：处理停止发送帧。
- **参数**：
  - `Stream`：指向 `QUIC_STREAM` 结构体的指针。
  - `ErrorCode`：错误代码。
- **实现步骤**：
  1. 检查流的状态，若未完全关闭或未重置则进行处理。
  2. 记录本地关闭停止发送事件。
  3. 创建并设置 `QUIC_STREAM_EVENT` 结构体。
  4. 记录指示事件并调用 `QuicStreamIndicateEvent`。
  5. 关闭发送方向。

## 四、muqic 的收包流程
文件实现了 QUIC 工作线程的核心收包处理功能。  
1. 当收到udp 包时，底层会调用QuicConnQueueRecvPackets 把包加入对应的connection 的收包队列等待处理。然后发送一个QUIC_OPER_TYPE_FLUSH_RECV 操作请求通知work 线程进行包处理。  
2. work线程收到QUIC_OPER_TYPE_FLUSH_RECV 操作请求后调用QuicConnFlushRecv 进行flush 操作，connecion 会调用QuicStreamRecv 方法对对应的流进行包处理。
3. 包的类型为数据包时， 会调用QuicStreamProcessStreamFrame 进行数据解析， 解析的过程中只要有数据（而且正好连续，没有丢包）， 那么就会调用QuicStreamRecvQueueFlush 通知worker 线程可以把数据回掉给app。
4. stream 发送QUIC_OPER_TYPE_FLUSH_STREAM_RECV 的操作请求给worker 线程。
5. worker 线程 会在对应的connection上调用QuicStreamRecvFlush 进行流输出。
6. stream 通过ServerStreamCallback 函数回掉QUIC_STREAM_EVENT_RECEIVE 事件给app。
7. stream 调用QuicStreamOnBytesDelivered 进行收到统计。
8. 关闭连接， 关闭接收。

整个收包的 时序图如下：
![](msquic/quicstreamrecv.png)