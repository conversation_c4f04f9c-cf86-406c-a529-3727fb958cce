# Wheel builds is a list of dicts, with keys
#
#  platform: the manylinux platform name
#  image: the docker image to build in
#  pythons: list of pythons in the image to build wheels for
- name: Sanity check build list
  assert:
    that: wheel_builds is defined

- name: Ensure pip installed
  include_role:
    name: ensure-pip

# We build an sdist of the checkout, and then build wheels from the
# sdist.  This ensures that nothing is left out of the sdist.
- name: Install setuptools-rust
  pip:
    name: setuptools-rust
  become: yes

- name: Create sdist
  command: |
    python3 setup.py sdist
  args:
    chdir: '{{ ansible_user_dir }}/{{ zuul.project.src_dir }}'

- name: Find output file
  find:
    paths: '{{ ansible_user_dir }}/{{ zuul.project.src_dir }}/dist'
    file_type: file
    patterns: "*.tar.gz"
  register: _sdist

- assert:
    that:
      - _sdist.matched == 1

- name: Create a build area
  file:
    path: '{{ ansible_user_dir }}/build'
    state: directory

- name: Create build area from sdist
  unarchive:
    src: '{{ _sdist.files[0].path }}'
    dest: '{{ ansible_user_dir }}/build'
    remote_src: yes

- name: Find cryptography subdir from sdist build dir
  set_fact:
    _build_dir: "{{ ansible_user_dir }}/build/{{ _sdist.files[0].path | basename | replace('.tar.gz', '') }}"

- name: Show _build_dir
  debug:
    var: _build_dir

- name: Install build script
  copy:
    src: build-wheels.sh
    dest: '{{ _build_dir }}'
    mode: 0755

- name: Run builds
  command: |
    docker run --rm \
      -e PLAT={{ item.platform }} \
      -e PYTHONS="{{ item.pythons | join(' ') }}" \
      -v {{ _build_dir }}:/io \
      {{ item.image }} \
      /io/build-wheels.sh
  become: yes
  loop: '{{ wheel_builds }}'

- name: Copy wheels to output
  synchronize:
    src: '{{ _build_dir }}/wheelhouse.final/'
    dest: '{{ zuul.executor.log_root }}/wheelhouse'
    mode: pull

- name: Return wheelhouse artifact
  zuul_return:
    data:
      zuul:
        artifacts:
          - name: "Wheelhouse"
            url: "wheelhouse"
            metadata:
              type: wheelhouse
