Doing the build
===============

.. _do_build:

Building within a single tree
-----------------------------

If you only need to build Kerberos for one platform, using a single
directory tree which contains both the source files and the object
files is the simplest.  However, if you need to maintain Kerberos for
a large number of platforms, you will probably want to use separate
build trees for each platform.  We recommend that you look at OS
Incompatibilities, for notes that we have on particular operating
systems.

If you don't want separate build trees for each architecture, then use
the following abbreviated procedure::

    cd /u1/krb5-VERSION/src
    ./configure
    make

That's it!

Building with separate build directories
----------------------------------------

If you wish to keep separate build directories for each platform, you
can do so using the following procedure.  (Note, this requires that
your make program support VPATH.  GNU's make will provide this
functionality, for example.)  If your make program does not support
this, see the next section.

For example, if you wish to store the binaries in ``tmpbuild`` build
directory you might use the following procedure::

   mkdir /u1/tmpbuild
   cd /u1/tmpbuild
   /u1/krb5-VERSION/src/configure
   make


Building using lndir
--------------------

If you wish to keep separate build directories for each platform, and
you do not have access to a make program which supports VPATH, all is
not lost.  You can use the lndir program to create symbolic link trees
in your build directory.

For example, if you wish to create a build directory for solaris
binaries you might use the following procedure::

    mkdir /u1/krb5-VERSION/solaris
    cd /u1/krb5-VERSION/solaris
    /u1/krb5-VERSION/src/util/lndir `pwd`/../src
    ./configure
    make

You must give an absolute pathname to lndir because it has a bug that
makes it fail for relative pathnames.  Note that this version differs
from the latest version as distributed and installed by the
XConsortium with X11R6.  Either version should be acceptable.


Installing the binaries
-----------------------

Once you have built Kerberos, you should install the binaries. You can
do this by running::

    make install

If you want to install the binaries into a destination directory that
is not their final destination, which may be convenient if you want to
build a binary distribution to be deployed on multiple hosts, you may
use::

    make install DESTDIR=/path/to/destdir

This will install the binaries under *DESTDIR/PREFIX*, e.g., the user
programs will install into *DESTDIR/PREFIX/bin*, the libraries into
*DESTDIR/PREFIX/lib*, etc.  *DESTDIR* must be an absolute path.

Some implementations of make allow multiple commands to be run in
parallel, for faster builds.  We test our Makefiles in parallel builds
with GNU make only; they may not be compatible with other parallel
build implementations.


Testing the build
-----------------

The Kerberos V5 distribution comes with built-in regression tests.  To
run them, simply type the following command while in the top-level
build directory (i.e., the directory where you sent typed make to
start building Kerberos; see :ref:`do_build`)::

    make check

On some operating systems, you have to run ``make install`` before
running ``make check``, or the test suite will pick up installed
versions of Kerberos libraries rather than the newly built ones.  You
can install into a prefix that isn't in the system library search
path, though.  Alternatively, you can configure with
**-**\ **-disable-rpath**, which renders the build tree less suitable
for installation, but allows testing without interference from
previously installed libraries.

There are additional regression tests available, which are not run
by ``make check``.  These tests require manual setup and teardown of
support infrastructure which is not easily automated, or require
excessive resources for ordinary use.  The procedure for running
the manual tests is documented at
https://k5wiki.kerberos.org/wiki/Manual_Testing.


Cleaning up the build
---------------------

* Use ``make clean`` to remove all files generated by running make
  command.
* Use ``make distclean`` to remove all files generated by running
  ./configure script.  After running ``make distclean`` your source
  tree (ideally) should look like the raw (just un-tarred) source
  tree.

Using autoconf
--------------

(If you are not a developer, you can ignore this section.)

In the Kerberos V5 source directory, there is a configure script which
automatically determines the compilation environment and creates the
proper Makefiles for a particular platform.  This configure script is
generated using autoconf, which you should already have installed if
you will be making changes to ``src/configure.in``.

Normal users will not need to worry about running autoconf; the
distribution comes with the configure script already prebuilt.

The autoconf package comes with a script called ``autoreconf`` that
will automatically run ``autoconf`` and ``autoheader`` as needed.  You
should run ``autoreconf`` from the top source directory, e.g.::

    cd /u1/krb5-VERSION/src
    autoreconf --verbose
