.\" Man page generated from reStructuredText.
.
.TH "KTUTIL" "1" " " "1.20" "MIT Kerberos"
.SH NAME
ktutil \- Kerberos keytab file maintenance utility
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBktutil\fP
.SH DESCRIPTION
.sp
The ktutil command invokes a command interface from which an
administrator can read, write, or edit entries in a keytab.  (Kerberos
V4 srvtab files are no longer supported.)
.SH COMMANDS
.SS list
.INDENT 0.0
.INDENT 3.5
\fBlist\fP [\fB\-t\fP] [\fB\-k\fP] [\fB\-e\fP]
.UNINDENT
.UNINDENT
.sp
Displays the current keylist.  If \fB\-t\fP, \fB\-k\fP, and/or \fB\-e\fP are
specified, also display the timestamp, key contents, or enctype
(respectively).
.sp
Alias: \fBl\fP
.SS read_kt
.INDENT 0.0
.INDENT 3.5
\fBread_kt\fP \fIkeytab\fP
.UNINDENT
.UNINDENT
.sp
Read the Kerberos V5 keytab file \fIkeytab\fP into the current keylist.
.sp
Alias: \fBrkt\fP
.SS write_kt
.INDENT 0.0
.INDENT 3.5
\fBwrite_kt\fP \fIkeytab\fP
.UNINDENT
.UNINDENT
.sp
Write the current keylist into the Kerberos V5 keytab file \fIkeytab\fP\&.
.sp
Alias: \fBwkt\fP
.SS clear_list
.INDENT 0.0
.INDENT 3.5
\fBclear_list\fP
.UNINDENT
.UNINDENT
.sp
Clear the current keylist.
.sp
Alias: \fBclear\fP
.SS delete_entry
.INDENT 0.0
.INDENT 3.5
\fBdelete_entry\fP \fIslot\fP
.UNINDENT
.UNINDENT
.sp
Delete the entry in slot number \fIslot\fP from the current keylist.
.sp
Alias: \fBdelent\fP
.SS add_entry
.INDENT 0.0
.INDENT 3.5
\fBadd_entry\fP {\fB\-key\fP|\fB\-password\fP} \fB\-p\fP \fIprincipal\fP
\fB\-k\fP \fIkvno\fP [\fB\-e\fP \fIenctype\fP] [\fB\-f\fP|\fB\-s\fP \fIsalt\fP]
.UNINDENT
.UNINDENT
.sp
Add \fIprincipal\fP to keylist using key or password.  If the \fB\-f\fP flag
is specified, salt information will be fetched from the KDC; in this
case the \fB\-e\fP flag may be omitted, or it may be supplied to force a
particular enctype.  If the \fB\-f\fP flag is not specified, the \fB\-e\fP
flag must be specified, and the default salt will be used unless
overridden with the \fB\-s\fP option.
.sp
Alias: \fBaddent\fP
.SS list_requests
.INDENT 0.0
.INDENT 3.5
\fBlist_requests\fP
.UNINDENT
.UNINDENT
.sp
Displays a listing of available commands.
.sp
Aliases: \fBlr\fP, \fB?\fP
.SS quit
.INDENT 0.0
.INDENT 3.5
\fBquit\fP
.UNINDENT
.UNINDENT
.sp
Quits ktutil.
.sp
Aliases: \fBexit\fP, \fBq\fP
.SH EXAMPLE
.INDENT 0.0
.INDENT 3.5
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
ktutil:  add_entry \-password \-p <EMAIL> \-k 1 \-e
    aes128\-cts\-hmac\-sha1\-96
<NAME_EMAIL>:
ktutil:  add_entry \-password \-p <EMAIL> \-k 1 \-e
    aes256\-cts\-hmac\-sha1\-96
<NAME_EMAIL>:
ktutil:  write_kt keytab
ktutil:
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kadmin(1), kdb5_util(8), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
