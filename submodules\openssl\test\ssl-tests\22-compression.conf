# Generated with generate_ssl_tests.pl

num_tests = 8

test-0 = 0-tlsv1_3-both-compress
test-1 = 1-tlsv1_3-client-compress
test-2 = 2-tlsv1_3-server-compress
test-3 = 3-tlsv1_3-neither-compress
test-4 = 4-tlsv1_2-both-compress
test-5 = 5-tlsv1_2-client-compress
test-6 = 6-tlsv1_2-server-compress
test-7 = 7-tlsv1_2-neither-compress
# ===========================================================

[0-tlsv1_3-both-compress]
ssl_conf = 0-tlsv1_3-both-compress-ssl

[0-tlsv1_3-both-compress-ssl]
server = 0-tlsv1_3-both-compress-server
client = 0-tlsv1_3-both-compress-client

[0-tlsv1_3-both-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = Compression
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-tlsv1_3-both-compress-client]
CipherString = DEFAULT
Options = Compression
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[1-tlsv1_3-client-compress]
ssl_conf = 1-tlsv1_3-client-compress-ssl

[1-tlsv1_3-client-compress-ssl]
server = 1-tlsv1_3-client-compress-server
client = 1-tlsv1_3-client-compress-client

[1-tlsv1_3-client-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-tlsv1_3-client-compress-client]
CipherString = DEFAULT
Options = Compression
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[2-tlsv1_3-server-compress]
ssl_conf = 2-tlsv1_3-server-compress-ssl

[2-tlsv1_3-server-compress-ssl]
server = 2-tlsv1_3-server-compress-server
client = 2-tlsv1_3-server-compress-client

[2-tlsv1_3-server-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = Compression
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-tlsv1_3-server-compress-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[3-tlsv1_3-neither-compress]
ssl_conf = 3-tlsv1_3-neither-compress-ssl

[3-tlsv1_3-neither-compress-ssl]
server = 3-tlsv1_3-neither-compress-server
client = 3-tlsv1_3-neither-compress-client

[3-tlsv1_3-neither-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-tlsv1_3-neither-compress-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[4-tlsv1_2-both-compress]
ssl_conf = 4-tlsv1_2-both-compress-ssl

[4-tlsv1_2-both-compress-ssl]
server = 4-tlsv1_2-both-compress-server
client = 4-tlsv1_2-both-compress-client

[4-tlsv1_2-both-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = Compression
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-tlsv1_2-both-compress-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = Compression
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
CompressionExpected = Yes
ExpectedResult = Success


# ===========================================================

[5-tlsv1_2-client-compress]
ssl_conf = 5-tlsv1_2-client-compress-ssl

[5-tlsv1_2-client-compress-ssl]
server = 5-tlsv1_2-client-compress-server
client = 5-tlsv1_2-client-compress-client

[5-tlsv1_2-client-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-tlsv1_2-client-compress-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = Compression
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[6-tlsv1_2-server-compress]
ssl_conf = 6-tlsv1_2-server-compress-ssl

[6-tlsv1_2-server-compress-ssl]
server = 6-tlsv1_2-server-compress-server
client = 6-tlsv1_2-server-compress-client

[6-tlsv1_2-server-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = Compression
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-tlsv1_2-server-compress-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
CompressionExpected = No
ExpectedResult = Success


# ===========================================================

[7-tlsv1_2-neither-compress]
ssl_conf = 7-tlsv1_2-neither-compress-ssl

[7-tlsv1_2-neither-compress-ssl]
server = 7-tlsv1_2-neither-compress-server
client = 7-tlsv1_2-neither-compress-client

[7-tlsv1_2-neither-compress-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-tlsv1_2-neither-compress-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
CompressionExpected = No
ExpectedResult = Success


