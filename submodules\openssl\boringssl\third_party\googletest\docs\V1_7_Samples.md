If you're like us, you'd like to look at some Google Test sample code.  The
[samples folder](../samples) has a number of well-commented samples showing how to use a
variety of Google Test features.

  * [Sample #1](../samples/sample1_unittest.cc) shows the basic steps of using Google Test to test C++ functions.
  * [Sample #2](../samples/sample2_unittest.cc) shows a more complex unit test for a class with multiple member functions.
  * [Sample #3](../samples/sample3_unittest.cc) uses a test fixture.
  * [Sample #4](../samples/sample4_unittest.cc) is another basic example of using Google Test.
  * [Sample #5](../samples/sample5_unittest.cc) teaches how to reuse a test fixture in multiple test cases by deriving sub-fixtures from it.
  * [Sample #6](../samples/sample6_unittest.cc) demonstrates type-parameterized tests.
  * [Sample #7](../samples/sample7_unittest.cc) teaches the basics of value-parameterized tests.
  * [Sample #8](../samples/sample8_unittest.cc) shows using `Combine()` in value-parameterized tests.
  * [Sample #9](../samples/sample9_unittest.cc) shows use of the listener API to modify Google Test's console output and the use of its reflection API to inspect test results.
  * [Sample #10](../samples/sample10_unittest.cc) shows use of the listener API to implement a primitive memory leak checker.
