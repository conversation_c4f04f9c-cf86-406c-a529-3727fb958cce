Copyright |copy| 1985-2019 by the Massachusetts Institute of Technology.

All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

* Redistributions of source code must retain the above copyright notice,
  this list of conditions and the following disclaimer.
* Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

Downloading of this software may constitute an export of cryptographic
software from the United States of America that is subject to the
United States Export Administration Regulations (EAR), 15 CFR 730-774.
Additional laws or regulations may apply.  It is the responsibility of
the person or entity contemplating export to comply with all
applicable export laws and regulations, including obtaining any
required license from the U.S. government.

The U.S. government prohibits export of encryption source code to
certain countries and individuals, including, but not limited to, the
countries of Cuba, Iran, North Korea, Sudan, Syria, and residents and
nationals of those countries.

Documentation components of this software distribution are licensed
under a Creative Commons Attribution-ShareAlike 3.0 Unported License.
(https://creativecommons.org/licenses/by-sa/3.0/)

Individual source code files are copyright MIT, Cygnus Support,
Novell, OpenVision Technologies, Oracle, Red Hat, Sun Microsystems,
FundsXpress, and others.

Project Athena, Athena, Athena MUSE, Discuss, Hesiod, Kerberos, Moira,
and Zephyr are trademarks of the Massachusetts Institute of Technology
(MIT).  No commercial use of these trademarks may be made without
prior written permission of MIT.

"Commercial use" means use of a name in a product or other for-profit
manner.  It does NOT prevent a commercial firm from referring to the
MIT trademarks in order to convey information (although in doing so,
recognition of their trademark status should be given).

-------------------

The following copyright and permission notice applies to the
OpenVision Kerberos Administration system located in
``kadmin/create``, ``kadmin/dbutil``, ``kadmin/passwd``,
``kadmin/server``, ``lib/kadm5``, and portions of
``lib/rpc``:

    Copyright, OpenVision Technologies, Inc., 1993-1996, All Rights Reserved

    WARNING:  Retrieving the OpenVision Kerberos Administration system source
    code, as described below, indicates your acceptance of the following
    terms.  If you do not agree to the following terms, do not retrieve the
    OpenVision Kerberos administration system.

    You may freely use and distribute the Source Code and Object Code
    compiled from it, with or without modification, but this Source Code is
    provided to you "AS IS" EXCLUSIVE OF ANY WARRANTY, INCLUDING, WITHOUT
    LIMITATION, ANY WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A
    PARTICULAR PURPOSE, OR ANY OTHER WARRANTY, WHETHER EXPRESS OR IMPLIED.
    IN NO EVENT WILL OPENVISION HAVE ANY LIABILITY FOR ANY LOST PROFITS,
    LOSS OF DATA OR COSTS OF PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, OR
    FOR ANY SPECIAL, INDIRECT, OR CONSEQUENTIAL DAMAGES ARISING OUT OF THIS
    AGREEMENT, INCLUDING, WITHOUT LIMITATION, THOSE RESULTING FROM THE USE
    OF THE SOURCE CODE, OR THE FAILURE OF THE SOURCE CODE TO PERFORM, OR FOR
    ANY OTHER REASON.

    OpenVision retains all copyrights in the donated Source Code. OpenVision
    also retains copyright to derivative works of the Source Code, whether
    created by OpenVision or by a third party. The OpenVision copyright
    notice must be preserved if derivative works are made based on the
    donated Source Code.

    OpenVision Technologies, Inc. has donated this Kerberos Administration
    system to MIT for inclusion in the standard Kerberos 5 distribution.
    This donation underscores our commitment to continuing Kerberos
    technology development and our gratitude for the valuable work which has
    been performed by MIT and the Kerberos community.

-------------------

    Portions contributed by Matt Crawford ``<EMAIL>`` were work
    performed at Fermi National Accelerator Laboratory, which is operated
    by Universities Research Association, Inc., under contract
    DE-AC02-76CHO3000 with the U.S. Department of Energy.

-------------------

Portions of ``src/lib/crypto`` have the following copyright:

    Copyright |copy| 1998 by the FundsXpress, INC.

    All rights reserved.

        Export of this software from the United States of America may require
        a specific license from the United States Government.  It is the
        responsibility of any person or organization contemplating export to
        obtain such a license before exporting.

    WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
    distribute this software and its documentation for any purpose and
    without fee is hereby granted, provided that the above copyright
    notice appear in all copies and that both that copyright notice and
    this permission notice appear in supporting documentation, and that
    the name of FundsXpress. not be used in advertising or publicity pertaining
    to distribution of the software without specific, written prior
    permission.  FundsXpress makes no representations about the suitability of
    this software for any purpose.  It is provided "as is" without express
    or implied warranty.

    THIS SOFTWARE IS PROVIDED "AS IS" AND WITHOUT ANY EXPRESS OR
    IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED
    WARRANTIES OF MERCHANTIBILITY AND FITNESS FOR A PARTICULAR PURPOSE.

-------------------

The implementation of the AES encryption algorithm in
``src/lib/crypto/builtin/aes`` has the following copyright:

    | Copyright |copy| 2001, Dr Brian Gladman ``<EMAIL>``,
        Worcester, UK.
    | All rights reserved.

    LICENSE TERMS

    The free distribution and use of this software in both source and binary
    form is allowed (with or without changes) provided that:

    1.  distributions of this source code include the above copyright
        notice, this list of conditions and the following disclaimer;
    2.  distributions in binary form include the above copyright
        notice, this list of conditions and the following disclaimer
        in the documentation and/or other associated materials;
    3.  the copyright holder's name is not used to endorse products
        built using this software without specific written permission.

    DISCLAIMER

    This software is provided 'as is' with no explcit or implied warranties
    in respect of any properties, including, but not limited to, correctness
    and fitness for purpose.

-------------------

Portions contributed by Red Hat, including the pre-authentication
plug-in framework and the NSS crypto implementation, contain the
following copyright:

    | Copyright |copy| 2006 Red Hat, Inc.
    | Portions copyright |copy| 2006 Massachusetts Institute of Technology
    | All Rights Reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are
    met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * Neither the name of Red Hat, Inc., nor the names of its contributors
      may be used to endorse or promote products derived from this software
      without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
    IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
    TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
    PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
    OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
    EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

The bundled verto source code is subject to the following license:

    Copyright 2011 Red Hat, Inc.

    Permission is hereby granted, free of charge, to any person
    obtaining a copy of this software and associated documentation files
    (the "Software"), to deal in the Software without restriction,
    including without limitation the rights to use, copy, modify, merge,
    publish, distribute, sublicense, and/or sell copies of the Software,
    and to permit persons to whom the Software is furnished to do so,
    subject to the following conditions:

    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
    BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
    ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
    CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.

-------------------

The MS-KKDCP client implementation has the following copyright:

    Copyright 2013,2014 Red Hat, Inc.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

       1.  Redistributions of source code must retain the above copyright
           notice, this list of conditions and the following disclaimer.

       2.  Redistributions in binary form must reproduce the above copyright
           notice, this list of conditions and the following disclaimer in
           the documentation and/or other materials provided with the
           distribution.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
    IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
    TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
    PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
    OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
    EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

The implementations of GSSAPI mechglue in GSSAPI-SPNEGO in
``src/lib/gssapi``, including the following files:

.. parsed-literal::

    lib/gssapi/generic/gssapi_err_generic.et
    lib/gssapi/mechglue/g_accept_sec_context.c
    lib/gssapi/mechglue/g_acquire_cred.c
    lib/gssapi/mechglue/g_canon_name.c
    lib/gssapi/mechglue/g_compare_name.c
    lib/gssapi/mechglue/g_context_time.c
    lib/gssapi/mechglue/g_delete_sec_context.c
    lib/gssapi/mechglue/g_dsp_name.c
    lib/gssapi/mechglue/g_dsp_status.c
    lib/gssapi/mechglue/g_dup_name.c
    lib/gssapi/mechglue/g_exp_sec_context.c
    lib/gssapi/mechglue/g_export_name.c
    lib/gssapi/mechglue/g_glue.c
    lib/gssapi/mechglue/g_imp_name.c
    lib/gssapi/mechglue/g_imp_sec_context.c
    lib/gssapi/mechglue/g_init_sec_context.c
    lib/gssapi/mechglue/g_initialize.c
    lib/gssapi/mechglue/g_inquire_context.c
    lib/gssapi/mechglue/g_inquire_cred.c
    lib/gssapi/mechglue/g_inquire_names.c
    lib/gssapi/mechglue/g_process_context.c
    lib/gssapi/mechglue/g_rel_buffer.c
    lib/gssapi/mechglue/g_rel_cred.c
    lib/gssapi/mechglue/g_rel_name.c
    lib/gssapi/mechglue/g_rel_oid_set.c
    lib/gssapi/mechglue/g_seal.c
    lib/gssapi/mechglue/g_sign.c
    lib/gssapi/mechglue/g_store_cred.c
    lib/gssapi/mechglue/g_unseal.c
    lib/gssapi/mechglue/g_userok.c
    lib/gssapi/mechglue/g_utils.c
    lib/gssapi/mechglue/g_verify.c
    lib/gssapi/mechglue/gssd_pname_to_uid.c
    lib/gssapi/mechglue/mglueP.h
    lib/gssapi/mechglue/oid_ops.c
    lib/gssapi/spnego/gssapiP_spnego.h
    lib/gssapi/spnego/spnego_mech.c

and the initial implementation of incremental propagation, including
the following new or changed files:

.. parsed-literal::

    include/iprop_hdr.h
    kadmin/server/ipropd_svc.c
    lib/kdb/iprop.x
    lib/kdb/kdb_convert.c
    lib/kdb/kdb_log.c
    lib/kdb/kdb_log.h
    lib/krb5/error_tables/kdb5_err.et
    kprop/kpropd_rpc.c
    kprop/kproplog.c

are subject to the following license:

    Copyright |copy| 2004 Sun Microsystems, Inc.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
    TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
    SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

-------------------

Kerberos V5 includes documentation and software developed at the
University of California at Berkeley, which includes this copyright
notice:

    | Copyright |copy| 1983 Regents of the University of California.
    | All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are
    met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
    3.  Neither the name of the University nor the names of its contributors
        may be used to endorse or promote products derived from this software
        without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

-------------------

Portions contributed by Novell, Inc., including the LDAP database
backend, are subject to the following license:

    | Copyright |copy| 2004-2005, Novell, Inc.
    | All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

    * Redistributions of source code must retain the above copyright notice,
      this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * The copyright holder's name is not used to endorse or promote products
      derived from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
    AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
    LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
    INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
    CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
    ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
    POSSIBILITY OF SUCH DAMAGE.

-------------------

Portions funded by Sandia National Laboratory
and developed by the University of Michigan's
Center for Information Technology Integration,
including the PKINIT implementation, are subject
to the following license:

    | COPYRIGHT |copy| 2006-2007
    | THE REGENTS OF THE UNIVERSITY OF MICHIGAN
    | ALL RIGHTS RESERVED

    Permission is granted to use, copy, create derivative works
    and redistribute this software and such derivative works
    for any purpose, so long as the name of The University of
    Michigan is not used in any advertising or publicity
    pertaining to the use of distribution of this software
    without specific, written prior authorization.  If the
    above copyright notice or any other identification of the
    University of Michigan is included in any copy of any
    portion of this software, then the disclaimer below must
    also be included.

    THIS SOFTWARE IS PROVIDED AS IS, WITHOUT REPRESENTATION
    FROM THE UNIVERSITY OF MICHIGAN AS TO ITS FITNESS FOR ANY
    PURPOSE, AND WITHOUT WARRANTY BY THE UNIVERSITY OF
    MICHIGAN OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING
    WITHOUT LIMITATION THE IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE
    REGENTS OF THE UNIVERSITY OF MICHIGAN SHALL NOT BE LIABLE
    FOR ANY DAMAGES, INCLUDING SPECIAL, INDIRECT, INCIDENTAL, OR
    CONSEQUENTIAL DAMAGES, WITH RESPECT TO ANY CLAIM ARISING
    OUT OF OR IN CONNECTION WITH THE USE OF THE SOFTWARE, EVEN
    IF IT HAS BEEN OR IS HEREAFTER ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGES.

-------------------

The pkcs11.h file included in the PKINIT code has the
following license:

    | Copyright 2006 g10 Code GmbH
    | Copyright 2006 Andreas Jellinghaus

    This file is free software; as a special exception the author gives
    unlimited permission to copy and/or distribute it, with or without
    modifications, as long as this notice is preserved.

    This file is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY, to the extent permitted by law; without even
    the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
    PURPOSE.

-------------------

Portions contributed by Apple Inc. are subject to the following license:

    Copyright 2004-2008 Apple Inc.  All Rights Reserved.

        Export of this software from the United States of America may require
        a specific license from the United States Government.  It is the
        responsibility of any person or organization contemplating export to
        obtain such a license before exporting.

    WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
    distribute this software and its documentation for any purpose and
    without fee is hereby granted, provided that the above copyright
    notice appear in all copies and that both that copyright notice and
    this permission notice appear in supporting documentation, and that
    the name of Apple Inc. not be used in advertising or publicity pertaining
    to distribution of the software without specific, written prior
    permission.  Apple Inc. makes no representations about the suitability of
    this software for any purpose.  It is provided "as is" without express
    or implied warranty.

    THIS SOFTWARE IS PROVIDED "AS IS" AND WITHOUT ANY EXPRESS OR
    IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED
    WARRANTIES OF MERCHANTIBILITY AND FITNESS FOR A PARTICULAR PURPOSE.

-------------------

The implementations of UTF-8 string handling in src/util/support and
src/lib/krb5/unicode are subject to the following copyright and
permission notice:

    | The OpenLDAP Public License
    | Version 2.8, 17 August 2003

    Redistribution and use of this software and associated documentation
    ("Software"), with or without modification, are permitted provided
    that the following conditions are met:

    1.  Redistributions in source form must retain copyright statements
        and notices,
    2.  Redistributions in binary form must reproduce applicable copyright
        statements and notices, this list of conditions, and the following
        disclaimer in the documentation and/or other materials provided
        with the distribution, and
    3.  Redistributions must contain a verbatim copy of this document.

    The OpenLDAP Foundation may revise this license from time to time.
    Each revision is distinguished by a version number.  You may use
    this Software under terms of this license revision or under the
    terms of any subsequent revision of the license.

    THIS SOFTWARE IS PROVIDED BY THE OPENLDAP FOUNDATION AND ITS
    CONTRIBUTORS "AS IS" AND ANY EXPRESSED OR IMPLIED WARRANTIES,
    INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT
    SHALL THE OPENLDAP FOUNDATION, ITS CONTRIBUTORS, OR THE AUTHOR(S)
    OR OWNER(S) OF THE SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
    BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
    LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
    CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
    ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
    POSSIBILITY OF SUCH DAMAGE.

    The names of the authors and copyright holders must not be used in
    advertising or otherwise to promote the sale, use or other dealing
    in this Software without specific, written prior permission.  Title
    to copyright in this Software shall at all times remain with copyright
    holders.

    OpenLDAP is a registered trademark of the OpenLDAP Foundation.

    Copyright 1999-2003 The OpenLDAP Foundation, Redwood City,
    California, USA.  All Rights Reserved.  Permission to copy and
    distribute verbatim copies of this document is granted.

-------------------

Marked test programs in src/lib/krb5/krb have the following copyright:

    | Copyright |copy| 2006 Kungliga Tekniska Högskola
    | (Royal Institute of Technology, Stockholm, Sweden).
    | All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
    3.  Neither the name of KTH nor the names of its contributors may be
        used to endorse or promote products derived from this software without
        specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY KTH AND ITS CONTRIBUTORS "AS IS" AND ANY
    EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL KTH OR ITS CONTRIBUTORS BE
    LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
    BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
    WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
    OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
    ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

The KCM Mach RPC definition file used on macOS has the following copyright:

    | Copyright |copy| 2009 Kungliga Tekniska Högskola
    | (Royal Institute of Technology, Stockholm, Sweden).
    | All rights reserved.

    Portions Copyright |copy| 2009 Apple Inc. All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.

    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.

    3.  Neither the name of the Institute nor the names of its contributors
        may be used to endorse or promote products derived from this software
        without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

-------------------

Portions of the RPC implementation in src/lib/rpc and src/include/gssrpc
have the following copyright and permission notice:

    Copyright |copy| 2010, Oracle America, Inc.

    All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in
        the documentation and/or other materials provided with the
        distribution.
    3.  Neither the name of the "Oracle America, Inc." nor the names of
        its contributors may be used to endorse or promote products
        derived from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
    IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
    TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
    PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
    HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
    SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
    TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

    Copyright |copy| 2006,2007,2009
    NTT (Nippon Telegraph and Telephone Corporation).  All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer as
        the first lines of this file unmodified.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.

    THIS SOFTWARE IS PROVIDED BY NTT "AS IS" AND ANY EXPRESS OR
    IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
    OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
    IN NO EVENT SHALL NTT BE LIABLE FOR ANY DIRECT, INDIRECT,
    INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
    NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
    THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

    Copyright 2000 by Carnegie Mellon University

    All Rights Reserved

    Permission to use, copy, modify, and distribute this software and its
    documentation for any purpose and without fee is hereby granted,
    provided that the above copyright notice appear in all copies and that
    both that copyright notice and this permission notice appear in
    supporting documentation, and that the name of Carnegie Mellon
    University not be used in advertising or publicity pertaining to
    distribution of the software without specific, written prior
    permission.

    CARNEGIE MELLON UNIVERSITY DISCLAIMS ALL WARRANTIES WITH REGARD TO
    THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND
    FITNESS, IN NO EVENT SHALL CARNEGIE MELLON UNIVERSITY BE LIABLE FOR
    ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
    ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT
    OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

-------------------

    Copyright |copy| 2002 Naval Research Laboratory (NRL/CCS)

    Permission to use, copy, modify and distribute this software and its
    documentation is hereby granted, provided that both the copyright
    notice and this permission notice appear in all copies of the software,
    derivative works or modified versions, and any portions thereof.

    NRL ALLOWS FREE USE OF THIS SOFTWARE IN ITS "AS IS" CONDITION AND
    DISCLAIMS ANY LIABILITY OF ANY KIND FOR ANY DAMAGES WHATSOEVER
    RESULTING FROM THE USE OF THIS SOFTWARE.

-------------------

    Copyright |copy| 1991, 1992, 1994 by Cygnus Support.

    Permission to use, copy, modify, and
    distribute this software and its documentation for any purpose and
    without fee is hereby granted, provided that the above copyright
    notice appear in all copies and that both that copyright notice and
    this permission notice appear in supporting documentation.
    Cygnus Support makes no representations about the suitability of
    this software for any purpose.  It is provided "as is" without express
    or implied warranty.

-------------------

    Copyright |copy| 2006 Secure Endpoints Inc.

    Permission is hereby granted, free of charge, to any person
    obtaining a copy of this software and associated documentation
    files (the "Software"), to deal in the Software without
    restriction, including without limitation the rights to use, copy,
    modify, merge, publish, distribute, sublicense, and/or sell copies
    of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be
    included in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
    BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
    ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
    CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.

-------------------

Portions of the implementation of the Fortuna-like PRNG are subject to
the following notice:

    | Copyright |copy| 2005 Marko Kreen
    | All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.

    THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

..

    Copyright |copy| 1994 by the University of Southern California

        EXPORT OF THIS SOFTWARE from the United States of America may
        require a specific license from the United States Government.
        It is the responsibility of any person or organization contemplating
        export to obtain such a license before exporting.

    WITHIN THAT CONSTRAINT, permission to copy, modify, and distribute
    this software and its documentation in source and binary forms is
    hereby granted, provided that any documentation or other materials
    related to such distribution or use acknowledge that the software
    was developed by the University of Southern California.

    DISCLAIMER OF WARRANTY.  THIS SOFTWARE IS PROVIDED "AS IS".  The
    University of Southern California MAKES NO REPRESENTATIONS OR
    WARRANTIES, EXPRESS OR IMPLIED.  By way of example, but not
    limitation, the University of Southern California MAKES NO
    REPRESENTATIONS OR WARRANTIES OF MERCHANTABILITY OR FITNESS FOR ANY
    PARTICULAR PURPOSE. The University of Southern
    California shall not be held liable for any liability nor for any
    direct, indirect, or consequential damages with respect to any
    claim by the user or distributor of the ksu software.

-------------------

    | Copyright |copy| 1995
    | The President and Fellows of Harvard University

    This code is derived from software contributed to Harvard by
    Jeremy Rassen.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
    3.  All advertising materials mentioning features or use of this software
        must display the following acknowledgement:

            This product includes software developed by the University of
            California, Berkeley and its contributors.

    4.  Neither the name of the University nor the names of its contributors
        may be used to endorse or promote products derived from this software
        without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

-------------------

    | Copyright |copy| 2008 by the Massachusetts Institute of Technology.
    | Copyright 1995 by Richard P. Basch.  All Rights Reserved.
    | Copyright 1995 by Lehman Brothers, Inc.  All Rights Reserved.

        Export of this software from the United States of America may
        require a specific license from the United States Government.
        It is the responsibility of any person or organization contemplating
        export to obtain such a license before exporting.

    WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
    distribute this software and its documentation for any purpose and
    without fee is hereby granted, provided that the above copyright
    notice appear in all copies and that both that copyright notice and
    this permission notice appear in supporting documentation, and that
    the name of Richard P. Basch, Lehman Brothers and M.I.T. not be used
    in advertising or publicity pertaining to distribution of the software
    without specific, written prior permission.  Richard P. Basch,
    Lehman Brothers and M.I.T. make no representations about the suitability
    of this software for any purpose.  It is provided "as is" without
    express or implied warranty.

-------------------

The following notice applies to ``src/lib/krb5/krb/strptime.c`` and
``src/include/k5-queue.h``.

    | Copyright |copy| 1997, 1998 The NetBSD Foundation, Inc.
    | All rights reserved.

    This code was contributed to The NetBSD Foundation by Klaus Klein.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
    3.  All advertising materials mentioning features or use of this software
        must display the following acknowledgement:

            This product includes software developed by the NetBSD
            Foundation, Inc. and its contributors.

    4.  Neither the name of The NetBSD Foundation nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE NETBSD FOUNDATION, INC. AND CONTRIBUTORS
    "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
    TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR CONTRIBUTORS
    BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
    INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
    CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
    ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
    POSSIBILITY OF SUCH DAMAGE.

-------------------

The following notice applies to Unicode library files in
``src/lib/krb5/unicode``:

    | Copyright 1997, 1998, 1999 Computing Research Labs,
    | New Mexico State University

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE COMPUTING RESEARCH LAB OR NEW MEXICO STATE UNIVERSITY BE LIABLE FOR ANY
    CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT
    OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR
    THE USE OR OTHER DEALINGS IN THE SOFTWARE.

-------------------

The following notice applies to ``src/util/support/strlcpy.c``:

    Copyright |copy| 1998 Todd C. Miller ``<EMAIL>``

    Permission to use, copy, modify, and distribute this software for any
    purpose with or without fee is hereby granted, provided that the above
    copyright notice and this permission notice appear in all copies.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
    WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
    ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
    WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
    ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
    OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

-------------------

The following notice applies to ``src/util/profile/argv_parse.c`` and
``src/util/profile/argv_parse.h``:

    Copyright 1999 by Theodore Ts'o.

    Permission to use, copy, modify, and distribute this software for
    any purpose with or without fee is hereby granted, provided that
    the above copyright notice and this permission notice appear in all
    copies.  THE SOFTWARE IS PROVIDED "AS IS" AND THEODORE TS'O (THE
    AUTHOR) DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
    INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS.
    IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER
    RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
    OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
    IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.  (Isn't
    it sick that the U.S. culture of lawsuit-happy lawyers requires
    this kind of disclaimer?)

-------------------

The following notice applies to SWIG-generated code in
``src/util/profile/profile_tcl.c``:

    Copyright |copy| 1999-2000, The University of Chicago

    This file may be freely redistributed without license or fee provided
    this copyright message remains intact.

-------------------

The following notice applies to portiions of ``src/lib/rpc`` and
``src/include/gssrpc``:

    Copyright |copy| 2000 The Regents of the University of Michigan.
    All rights reserved.

    Copyright |copy| 2000 Dug Song ``<EMAIL>``.
    All rights reserved, all wrongs reversed.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1.  Redistributions of source code must retain the above copyright
        notice, this list of conditions and the following disclaimer.
    2.  Redistributions in binary form must reproduce the above copyright
        notice, this list of conditions and the following disclaimer in the
        documentation and/or other materials provided with the distribution.
    3.  Neither the name of the University nor the names of its
        contributors may be used to endorse or promote products derived
        from this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESS OR IMPLIED
    WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
    DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
    BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
    LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
    NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

Implementations of the MD4 algorithm are subject to the following
notice:

    Copyright |copy| 1990, RSA Data Security, Inc. All rights reserved.

    License to copy and use this software is granted provided that
    it is identified as the "RSA Data Security, Inc. MD4 Message
    Digest Algorithm" in all material mentioning or referencing this
    software or this function.

    License is also granted to make and use derivative works
    provided that such works are identified as "derived from the RSA
    Data Security, Inc. MD4 Message Digest Algorithm" in all
    material mentioning or referencing the derived work.

    RSA Data Security, Inc. makes no representations concerning
    either the merchantability of this software or the suitability
    of this software for any particular purpose.  It is provided "as
    is" without express or implied warranty of any kind.

    These notices must be retained in any copies of any part of this
    documentation and/or software.

-------------------

Implementations of the MD5 algorithm are subject to the following
notice:

    Copyright |copy| 1990, RSA Data Security, Inc. All rights reserved.

    License to copy and use this software is granted provided that
    it is identified as the "RSA Data Security, Inc. MD5 Message-
    Digest Algorithm" in all material mentioning or referencing this
    software or this function.

    License is also granted to make and use derivative works
    provided that such works are identified as "derived from the RSA
    Data Security, Inc. MD5 Message-Digest Algorithm" in all
    material mentioning or referencing the derived work.

    RSA Data Security, Inc. makes no representations concerning
    either the merchantability of this software or the suitability
    of this software for any particular purpose.  It is provided "as
    is" without express or implied warranty of any kind.

    These notices must be retained in any copies of any part of this
    documentation and/or software.

-------------------

The following notice applies to ``src/lib/crypto/crypto_tests/t_mddriver.c``:

    Copyright |copy| 1990-2, RSA Data Security, Inc. Created 1990. All
    rights reserved.

    RSA Data Security, Inc. makes no representations concerning either
    the merchantability of this software or the suitability of this
    software for any particular purpose. It is provided "as is"
    without express or implied warranty of any kind.

    These notices must be retained in any copies of any part of this
    documentation and/or software.

-------------------

Portions of ``src/lib/krb5`` are subject to the following notice:

    | Copyright |copy| 1994 CyberSAFE Corporation.
    | Copyright 1990,1991,2007,2008 by the Massachusetts
        Institute of Technology.
    | All Rights Reserved.

        Export of this software from the United States of America may
        require a specific license from the United States Government.
        It is the responsibility of any person or organization contemplating
        export to obtain such a license before exporting.

    WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
    distribute this software and its documentation for any purpose and
    without fee is hereby granted, provided that the above copyright
    notice appear in all copies and that both that copyright notice and
    this permission notice appear in supporting documentation, and that
    the name of M.I.T. not be used in advertising or publicity pertaining
    to distribution of the software without specific, written prior
    permission.  Furthermore if you modify this software you must label
    your software as modified software and not distribute it in such a
    fashion that it might be confused with the original M.I.T. software.
    Neither M.I.T., the Open Computing Security Group, nor
    CyberSAFE Corporation make any representations about the suitability of
    this software for any purpose.  It is provided "as is" without express
    or implied warranty.

-------------------

Portions contributed by PADL Software are subject to the following
license:

    Copyright (c) 2011, PADL Software Pty Ltd.
    All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    1. Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.

    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.

    3. Neither the name of PADL Software nor the names of its contributors
       may be used to endorse or promote products derived from this software
       without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY PADL SOFTWARE AND CONTRIBUTORS "AS IS" AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL PADL SOFTWARE OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

-------------------

The bundled libev source code is subject to the following license:

    All files in libev are Copyright (C)2007,2008,2009 Marc Alexander Lehmann.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are
    met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
    "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
    LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
    A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
    OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
    SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
    LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
    OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

    Alternatively, the contents of this package may be used under the terms
    of the GNU General Public License ("GPL") version 2 or any later version,
    in which case the provisions of the GPL are applicable instead of the
    above. If you wish to allow the use of your version of this package only
    under the terms of the GPL and not to allow others to use your version of
    this file under the BSD license, indicate your decision by deleting the
    provisions above and replace them with the notice and other provisions
    required by the GPL in this and the other files of this package. If you do
    not delete the provisions above, a recipient may use your version of this
    file under either the BSD or the GPL.

-------------------

Files copied from the Intel AESNI Sample Library are subject to the
following license:

    Copyright |copy| 2010, Intel Corporation
    All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

        * Redistributions of source code must retain the above
          copyright notice, this list of conditions and the following
          disclaimer.
        * Redistributions in binary form must reproduce the above
          copyright notice, this list of conditions and the following
          disclaimer in the documentation and/or other materials
          provided with the distribution.
        * Neither the name of Intel Corporation nor the names of its
          contributors may be used to endorse or promote products
          derived from this software without specific prior written
          permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
    CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
    INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
    DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS
    BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
    EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
    TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
    ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
    TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
    THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

-------------------

The following notice applies to
``src/ccapi/common/win/OldCC/autolock.hxx``:

    Copyright (C) 1998 by Danilo Almeida.  All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.

    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in
      the documentation and/or other materials provided with the
      distribution.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
    "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
    LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
    FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
    COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
    INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
    SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
    STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
    ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
    OF THE POSSIBILITY OF SUCH DAMAGE.

-------------------

The following notice applies to portions of
``src/plugins/preauth/spake/edwards25519.c`` and
``src/plugins/preauth/spake/edwards25519_tables.h``:

The MIT License (MIT)

Copyright (c) 2015-2016 the fiat-crypto authors (see the AUTHORS file).

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to
deal in the Software without restriction, including without limitation the
rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
IN THE SOFTWARE.

-------------------

The following notice applies to portions of
``src/plugins/preauth/spake/edwards25519.c``:

Copyright (c) 2015-2016, Google Inc.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted, provided that the above
copyright notice and this permission notice appear in all copies.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
