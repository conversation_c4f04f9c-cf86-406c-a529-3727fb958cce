=pod

=head1 NAME

ossl_cmp_print_log,
ossl_cmp_alert,
ossl_cmp_err,
ossl_cmp_warn,
ossl_cmp_info,
ossl_cmp_debug,
ossl_cmp_log,
ossl_cmp_log1,
ossl_cmp_log2,
ossl_cmp_log3,
ossl_cmp_log4,
ossl_cmp_log_parse_metadata,
ossl_cmp_add_error_data,
ossl_cmp_add_error_line
- logging and error reporting support for CMP

=head1 SYNOPSIS

 #include "cmp_local.h"

 int ossl_cmp_print_log(OSSL_CMP_severity level, const OSSL_CMP_CTX *ctx,
                        const char *func, const char *file, int line,
                        const char *level_str, const char *format, ...);
 #define ossl_cmp_alert(ctx, msg)
 #define ossl_cmp_err(ctx, msg)
 #define ossl_cmp_warn(ctx, msg)
 #define ossl_cmp_info(ctx, msg)
 #define ossl_cmp_debug(ctx, (msg)
 #define ossl_cmp_log(level, ctx, msg)
 #define ossl_cmp_log1(level, ctx, fmt, arg1)
 #define ossl_cmp_log2(level, ctx, fmt, arg1, arg2)
 #define ossl_cmp_log3(level, ctx, fmt, arg1, arg2, arg3)
 #define ossl_cmp_log4(level, ctx, fmt, arg1, arg2, arg3, arg4)
 const char *ossl_cmp_log_parse_metadata(const char *buf,
                                         OSSL_CMP_severity *level, char **func,
                                         char **file, int *line);

 #define ossl_cmp_add_error_data(txt)
 #define ossl_cmp_add_error_line(txt)

=head1 DESCRIPTION

ossl_cmp_print_log() prints CMP log messages (i.e., diagnostic info) via the
log callback of the B<ctx> if present and the severity level is sufficient.
If the trace API if enabled the function uses it, prepending the function name,
filename, line number, and severity information to the message being output.
In any case the B<level>, B<func>, B<file>, and B<line> parameters
and the message constructed using the given B<format> and variable further
argument list are passed to the log callback function (unless it is NULL).
The B<ctx>, B<func>, B<file>, and B<level_str> arguments may be NULL.

ossl_cmp_alert(), ossl_cmp_err(), ossl_cmp_warn(), ossl_cmp_info(), and
ossl_cmp_debug() output a simple alert/error/warning/info/debug message
via ossl_cmp_print_log().

ossl_cmp_log(), ossl_cmp_log1(), ossl_cmp_log2(), ossl_cmp_log3(), and
ossl_cmp_log4() output a log message with the given severity,
constructing the message text from the given format and arguments.

ossl_cmp_log_parse_metadata() parses the given message buffer I<buf> populated
by ossl_cmp_log() etc.
according to the pattern OSSL_CMP_LOG_START#level ": %s\n", filling in
the variable pointed to by I<level> with the severity level or -1,
the variable pointed to by I<func> with the function name string or NULL,
the variable pointed to by I<file> with the filename string or NULL, and
the variable pointed to by I<line> with the line number or -1.
Any string returned via I<*func> and I<*file> must be freed by the caller.

ossl_cmp_add_error_data() is a macro calling
L<ERR_add_error_txt(3)> with the separator being ":".

ossl_cmp_add_error_line() is a macro calling
L<ERR_add_error_txt(3)> with the separator being "\n".

=head1 RETURN VALUES

ossl_cmp_log_parse_metadata() returns the pointer to the actual message text
after the OSSL_CMP_LOG_PREFIX and level and ':' if found in the buffer,
else the beginning of the buffer.

ossl_cmp_add_error_data() and
ossl_cmp_add_error_line()
do not return anything.

All other functions return 1 on success, 0 on error.

=head1 SEE ALSO

L<ERR_add_error_txt(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
