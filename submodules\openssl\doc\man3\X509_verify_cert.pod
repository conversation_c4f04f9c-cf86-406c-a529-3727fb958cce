=pod

=head1 NAME

X509_verify_cert - discover and verify X509 certificate chain

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_verify_cert(X509_STORE_CTX *ctx);

=head1 DESCRIPTION

The X509_verify_cert() function attempts to discover and validate a
certificate chain based on parameters in B<ctx>. A complete description of
the process is contained in the L<verify(1)> manual page.

=head1 RETURN VALUES

If a complete chain can be built and validated this function returns 1,
otherwise it return zero, in exceptional circumstances it can also
return a negative code.

If the function fails additional error information can be obtained by
examining B<ctx> using, for example X509_STORE_CTX_get_error().

=head1 NOTES

Applications rarely call this function directly but it is used by
OpenSSL internally for certificate validation, in both the S/MIME and
SSL/TLS code.

A negative return value from X509_verify_cert() can occur if it is invoked
incorrectly, such as with no certificate set in B<ctx>, or when it is called
twice in succession without reinitialising B<ctx> for the second call.
A negative return value can also happen due to internal resource problems or if
a retry operation is requested during internal lookups (which never happens
with standard lookup methods).
Applications must check for <= 0 return value on error.

=head1 BUGS

This function uses the header B<x509.h> as opposed to most chain verification
functions which use B<x509_vfy.h>.

=head1 SEE ALSO

L<X509_STORE_CTX_get_error(3)>

=head1 COPYRIGHT

Copyright 2009-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
