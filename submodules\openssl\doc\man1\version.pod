=pod

=head1 NAME

openssl-version,
version - print OpenSSL version information

=head1 SYNOPSIS

B<openssl version>
[B<-help>]
[B<-a>]
[B<-v>]
[B<-b>]
[B<-o>]
[B<-f>]
[B<-p>]
[B<-d>]
[B<-e>]

=head1 DESCRIPTION

This command is used to print out version information about OpenSSL.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-a>

All information, this is the same as setting all the other flags.

=item B<-v>

The current OpenSSL version.

=item B<-b>

The date the current version of OpenSSL was built.

=item B<-o>

Option information: various options set when the library was built.

=item B<-f>

Compilation flags.

=item B<-p>

Platform setting.

=item B<-d>

OPENSSLDIR setting.

=item B<-e>

ENGINESDIR setting.

=back

=head1 NOTES

The output of B<openssl version -a> would typically be used when sending
in a bug report.

=head1 COPYRIGHT

Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
