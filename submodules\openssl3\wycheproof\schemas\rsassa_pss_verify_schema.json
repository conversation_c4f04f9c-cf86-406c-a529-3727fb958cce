{"type": "object", "definitions": {"RsassaPssTestGroup": {"type": "object", "properties": {"type": {"enum": ["RsassaPssVerify"]}, "d": {"type": "string", "format": "BigInt", "description": "The private exponent"}, "e": {"type": "string", "format": "BigInt", "description": "The public exponent"}, "keyAsn": {"type": "string", "format": "Der", "description": "ASN encoding of the sequence [n, e]"}, "keyDer": {"type": "string", "format": "Der", "description": "ASN encoding of the public key"}, "keyPem": {"type": "string", "format": "Pem", "description": "Pem encoded public key"}, "keySize": {"type": "integer", "description": "the size of the modulus in bits"}, "mgf": {"type": "string", "description": "the message generating function (e.g. MGF1)"}, "mgfSha": {"type": "string", "description": "The hash function used for the message generating function."}, "n": {"type": "string", "format": "BigInt", "description": "The modulus of the key"}, "sLen": {"type": "integer", "description": "The salt length"}, "sha": {"type": "string", "description": "The hash function for hasing the message."}, "tests": {"type": "array", "items": {"$ref": "#/definitions/RsassaPssTestVector"}}}}, "RsassaPssTestVector": {"type": "object", "properties": {"tcId": {"type": "integer", "description": "Identifier of the test case"}, "comment": {"type": "string", "description": "A brief description of the test case"}, "msg": {"type": "string", "format": "HexBytes", "description": "The message to sign"}, "sig": {"type": "string", "format": "HexBytes", "description": "a signature for msg"}, "result": {"type": "string", "description": "Test result", "enum": ["valid", "invalid", "acceptable"]}, "flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags"}}}}, "properties": {"algorithm": {"type": "string", "description": "the primitive tested in the test file"}, "generatorVersion": {"type": "string", "description": "the version of the test vectors."}, "header": {"type": "array", "items": {"type": "string"}, "description": "additional documentation"}, "notes": {"type": "object", "description": "a description of the labels used in the test vectors"}, "numberOfTests": {"type": "integer", "description": "the number of test vectors in this test"}, "schema": {"enum": ["rsassa_pss_verify_schema.json"]}, "testGroups": {"type": "array", "items": {"$ref": "#/definitions/RsassaPssTestGroup"}}}}