=pod

=head1 NAME

X509_NAME_ENTRY_get_object, X509_NAME_ENTRY_get_data,
X509_NAME_ENTRY_set_object, X509_NAME_ENTRY_set_data,
X509_NAME_ENTRY_create_by_txt, X509_NAME_ENTRY_create_by_NID,
X509_NAME_ENTRY_create_by_OBJ - X509_NAME_ENTRY utility functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 ASN1_OBJECT *X509_NAME_ENTRY_get_object(const X509_NAME_ENTRY *ne);
 ASN1_STRING *X509_NAME_ENTRY_get_data(const X509_NAME_ENTRY *ne);

 int X509_NAME_ENTRY_set_object(X509_NAME_ENTRY *ne, const ASN1_OBJECT *obj);
 int X509_NAME_ENTRY_set_data(X509_NAME_ENTRY *ne, int type,
                              const unsigned char *bytes, int len);

 X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_txt(X509_NAME_ENTRY **ne, const char *field,
                                                int type, const unsigned char *bytes,
                                                int len);
 X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_NID(X509_NAME_ENTRY **ne, int nid,
                                                int type, const unsigned char *bytes,
                                                int len);
 X509_NAME_ENTRY *X509_NAME_ENTRY_create_by_OBJ(X509_NAME_ENTRY **ne,
                                                const ASN1_OBJECT *obj, int type,
                                                const unsigned char *bytes, int len);

=head1 DESCRIPTION

X509_NAME_ENTRY_get_object() retrieves the field name of B<ne> in
and B<ASN1_OBJECT> structure.

X509_NAME_ENTRY_get_data() retrieves the field value of B<ne> in
and B<ASN1_STRING> structure.

X509_NAME_ENTRY_set_object() sets the field name of B<ne> to B<obj>.

X509_NAME_ENTRY_set_data() sets the field value of B<ne> to string type
B<type> and value determined by B<bytes> and B<len>.

X509_NAME_ENTRY_create_by_txt(), X509_NAME_ENTRY_create_by_NID()
and X509_NAME_ENTRY_create_by_OBJ() create and return an
B<X509_NAME_ENTRY> structure.

=head1 NOTES

X509_NAME_ENTRY_get_object() and X509_NAME_ENTRY_get_data() can be
used to examine an B<X509_NAME_ENTRY> function as returned by
X509_NAME_get_entry() for example.

X509_NAME_ENTRY_create_by_txt(), X509_NAME_ENTRY_create_by_OBJ(),
X509_NAME_ENTRY_create_by_NID() and X509_NAME_ENTRY_set_data()
are seldom used in practice because B<X509_NAME_ENTRY> structures
are almost always part of B<X509_NAME> structures and the
corresponding B<X509_NAME> functions are typically used to
create and add new entries in a single operation.

The arguments of these functions support similar options to the similarly
named ones of the corresponding B<X509_NAME> functions such as
X509_NAME_add_entry_by_txt(). So for example B<type> can be set to
B<MBSTRING_ASC> but in the case of X509_set_data() the field name must be
set first so the relevant field information can be looked up internally.

=head1 RETURN VALUES

X509_NAME_ENTRY_get_object() returns a valid B<ASN1_OBJECT> structure if it is
set or NULL if an error occurred.

X509_NAME_ENTRY_get_data() returns a valid B<ASN1_STRING> structure if it is set
or NULL if an error occurred.

X509_NAME_ENTRY_set_object() and X509_NAME_ENTRY_set_data() return 1 on success
or 0 on error.

X509_NAME_ENTRY_create_by_txt(), X509_NAME_ENTRY_create_by_NID() and
X509_NAME_ENTRY_create_by_OBJ() return a valid B<X509_NAME_ENTRY> on success or
NULL if an error occurred.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<d2i_X509_NAME(3)>,
L<OBJ_nid2obj(3)>

=head1 COPYRIGHT

Copyright 2002-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
