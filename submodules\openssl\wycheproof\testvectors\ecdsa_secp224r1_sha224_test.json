{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 356, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021c2840bf24f6f66be287066b7cbf38788e1b7770b18fd1aa6a26d7c6dc", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021cd7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303e021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d028070049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a028000d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "303f0000021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "3042498177303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "30412500303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "303f303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30422221498177021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304122202500021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3045221e021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0004deadbeef021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3042021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2222498177021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a22212500021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3045021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a221f021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045aa00bb00cd00303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043aa02aabb303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "30452224aa00bb00cd00021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30432222aa02aabb021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2225aa00bb00cd00021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2223aa02aabb021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30412280021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0000021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2280021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30412280031c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0000021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2280031d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3041300102303c1c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c1c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb3584636100", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb3584636105000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "303f3000021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463613000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3040021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361bf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "303f303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301e021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e02811c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a02811d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f0282001c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0282001d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021b70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021e00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021c00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30420285010000001c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3042021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0285010000001d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046028901000000000000001c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a028901000000000000001d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304102847fffffff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a02847fffffff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30410284ffffffff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0284ffffffff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30420285ffffffffff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3042021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0285ffffffffff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30450288ffffffffffffffff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3045021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0288ffffffffffffffff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d02ff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a02ff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "301f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a02", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021e70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0000021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021f00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021e000070049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021f000000d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0000021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021e70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0500021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021f00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463610500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3020021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3020021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d001c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d011c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d031c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d041c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303dff1c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a001d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a011d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a031d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a041d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480aff1d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3020021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a0200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "30412220020170021b049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "3041021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a2221020100021cd7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021c72049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d02d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a488a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463e1", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021b70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a48021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021b049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021c00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb358463", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021dff70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021eff00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3021021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3021021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d0170049af31f8348673d56cece2b26fc2a84bbe2e2a2e84aeced767247021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff70049af31f8348673d56cece2b28cee4c34a02667b2df86234be1dcd021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c8ffb650ce07cb798c2a93131d4d81a785bfd0d5b70f4de586ee5b7f6021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ffb650ce07cb798c2a93131d4d7311b3cb5fd9984d2079dcb41e233021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe8ffb650ce07cb798c2a93131d4d903d57b441d1d5d17b51312898db9021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d0170049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d008ffb650ce07cb798c2a93131d4d81a785bfd0d5b70f4de586ee5b7f6021d00d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d01d7bf40db0909941d78f9948340c5b4b7a5fa6fca97e8a82091e08d9e", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021cd7bf40db0909941d78f9948340c78771e4888f4e702e5595d9283924", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021dff2840bf24f6f66be287066b7cbf3961eb3abe80737bf48124ca7b9c9f", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021dfe2840bf24f6f66be287066b7cbf3a4b485a059035681757df6e1f7262", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021d01d7bf40db0909941d78f9948340c69e14c5417f8c840b7edb35846361", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303c021c70049af31f8348673d56cece2b27e587a402f2a48f0b21a7911a480a021c2840bf24f6f66be287066b7cbf3961eb3abe80737bf48124ca7b9c9f", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3137353738", "sig": "303c021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021c3116e1a38e4ab2008eca032fb2d185e5c21a232eaf4507ae56177fd2", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32323534363238393834", "sig": "303d021d008ce2afe20b684576fdd91b4b34168c9c011996af5b0eb85fa929f381021c662af5ca651bffbc623c3a3b372779bd09e1948cd19188f5339a979d", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "31383237383738363130", "sig": "303e021d00da573cf73aed174710c232155735248f8ebef696374647527da52258021d00b251856b66a83c32bf0b7b81a01f1db4507e622125f301bd832a5ccc", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "31353138383631373039", "sig": "303e021d00c368da86582b2c82b696b2f7c79027968f3fd25cbba9688cdc67b17a021d00aba8e3c2ff1af9bb9c66ca88a3825a19ce17206e7a658ff47025891e", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32313239323333343232", "sig": "303d021d00ffcefcb57190d0b87efb789fb53407fd2c65c5ae3551da3eccf8ddd5021c05c89b41238f1e1def8fbe8d4afebf20be077e82972f91297487e118", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "31323231393739303539", "sig": "303c021c2b98c67ebf6597b08bc7f1b73ff8662cf125e9700ec973ece9c6ff48021c2e3f72a8f76e12c8cdf4487e0956c1ef4578e1da4d29d8db824d415b", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "32333032353637363131", "sig": "303d021c5794d70440f166904d24d0b910cd127c63a9eddca45a4d9032db47e8021d008ba5d290834d9a0963122d928da902f7b03467396072180bb1801b43", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31313035383638343732", "sig": "303e021d00c29c70b0b21782d1c727f4907aef5641b6d6c6e7b2a1ebfa57794223021d00aac2d3a02592f298dd3198e388425ec7a91d7e6be48248a64773614c", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31343636363134343432", "sig": "303d021c5c3ef3778c811e69ef0b0e370e45ec0d7eb88505c3e8ffb8c50b9993021d00e06b5c6e47dc4da9e64fd21bc3e1da13cf7c264fa64ccb89da87387c", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "343431393536343230", "sig": "303e021d00ed8f586563232cf15ebd014bd4f99727e337cfe4ce48694fe6748ec2021d00fff779a3eca9513522908e252a2b4aab2060608e6cd2d4f1b8c696cd", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "31393639343836303634", "sig": "303c021c64c084f6b775bbf7915c1964a68b0259629328598f13557872867830021c2a6f3b289d130ec3d99e4caaf601497895a069c1a5a75b559ad28444", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323335363732383833", "sig": "303d021c2b514e9b0e0eb68adc01915abbee9fa21f3034be5581dedaa6b15982021d00b8f71c5fdc68d698716bfc623b278216c0fcc0298497fc9c03db44e9", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "32323537383433373033", "sig": "303e021d00e4103f4a8a814485b6b406fe8dd72206bad6a50e7126bc655c3d2285021d009bcb99693284cac26e6641a861dbec24f9cc5dd7bb535339d09ac984", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "393434353030393436", "sig": "303d021d009edfb833446ec8b6fc84eea34ee40a85b732e5c99da8abc8bafcc515021c5052b40f9d407ae90003299cabe3e1a587b0558127cafb31de6b2638", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "31363837373839343130", "sig": "303d021d00ad2647c8ff377798a6aeaed436d30c7b25fb52428829ce6424dd34e8021c28f58671d77c86da302418c51e5ab86d137ba6ef4389722bc79b8751", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "333933323631323238", "sig": "303c021c5dfc6fad385bdb24b2b70a64fd4253405c0028bb36f4793aa3bd31fe021c1c210b74924171378992b03bb1bd78c5cfcfc879d2e5c736d35516c3", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "31303733353731303935", "sig": "303d021c766bef46229695e6829dd12cd558369ec34519ba4a72dcaf6f73f7b0021d00fc015ccdd1e943b910101607d81ff1398ca6a4d70c25832b02b221e4", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "3630383837343734", "sig": "303c021c120055f90ad1290c4c5fc5faf69b215139182c770d2b55e95712442f021c01ac47f7446543d4003b039d9f54daa9d0799f98291a32df4fcd472a", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "31313932353639393632", "sig": "303d021d00f480591f6f40a25b37a035fd91954145ec342e593d09e142f25da408021c5c6ba44ff52f52c51490743d9b650916be58d06d7c1fd99dfa2eab58", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "3930303736303933", "sig": "303c021c57daddb0cb6af939b1ea1aaf4bc72e56150c0c46a581827193e65d17021c3bc37bde4e60b789ba86a054d37f1191e0814926c1a0100168d16c17", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "31363032383336313337", "sig": "303c021c3a74102bd1fc617018efc4fbc042e719a81b55830aac1f1dcdedec65021c4bb9fe90015a45f31c8c95dda24f54fcdb64682c13f68d4da3d1abe0", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32303830323435363734", "sig": "303e021d00e3b6bb1b5beed048e0177e3e310fa14eb923a1e3274c0946f9275454021d00e044e0494ff46573c37007e3efa3233588f1d103ced1823c7e87e7c8", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "31373938323734363539", "sig": "303c021c3c212b5a7e65d9af44643bd62fa42a9b9cffe6bdb623e9b9e4337156021c29c8121a12427a324e5d551ff5a83d3c252e32257af2800d080817d2", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31313535353737373230", "sig": "303c021c1630554989fffd0e35f2d9105623d73a543634c48000484c422272ca021c214da487d5e51f73814dff80a08c77bd8a83a9889a1b26a5578ba954", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343332343734363634", "sig": "303d021c0a4609242f2193b94bc54f49bcf532a576e035cec50e043668574bef021d00aa68bd67624d8812002bbb3a5f530594451372d4ab36896a2929c3df", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "32343137323832323737", "sig": "303e021d00ef9ff446e8eef3e948f4129fe8804f81f5b7f116a5383f9e8bc359e4021d00f4c7055bd98f4a7ea49d9574160eac167809f6a78b9dd220958dd0f3", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "3737383734373731", "sig": "303d021c1a6c59d85d5b3120b28c0d30bc058a92dc725d8ef450c198cc3ca522021d008b17fefc8ab1ff0bb37a93446453d40f65bc2cb9636b11207f5c90a1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048bf7e792f7c86877f1fd0552e42d80653b59e3a29e762a22810daac7eec615bbad04b58dc2a7956090b8040bb5055325bba0aa8b3a5caa6f", "wx": "008bf7e792f7c86877f1fd0552e42d80653b59e3a29e762a22810daac7", "wy": "00eec615bbad04b58dc2a7956090b8040bb5055325bba0aa8b3a5caa6f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048bf7e792f7c86877f1fd0552e42d80653b59e3a29e762a22810daac7eec615bbad04b58dc2a7956090b8040bb5055325bba0aa8b3a5caa6f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEi/fnkvfIaHfx/QVS5C2AZTtZ46Kedioi\ngQ2qx+7GFbutBLWNwqeVYJC4BAu1BVMlu6Cqizpcqm8=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 257, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 258, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042646ff36d9697aaaed0d641117f94f60e138bab8e9912b558ae0a818ca48e45a33550c1b5bd20a00e4d9df3033c03222e87bd96a8197f2dd", "wx": "2646ff36d9697aaaed0d641117f94f60e138bab8e9912b558ae0a818", "wy": "00ca48e45a33550c1b5bd20a00e4d9df3033c03222e87bd96a8197f2dd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042646ff36d9697aaaed0d641117f94f60e138bab8e9912b558ae0a818ca48e45a33550c1b5bd20a00e4d9df3033c03222e87bd96a8197f2dd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJkb/NtlpeqrtDWQRF/lPYOE4urjpkStV\niuCoGMpI5FozVQwbW9IKAOTZ3zAzwDIi6HvZaoGX8t0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 259, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ea3ea2873b6fc099bfd779b0a2c23c2c4354e2fec4536f3b8e420988f97e1c7646b4eb3de616752f415ab3a6f696d1d674fb4b6732252382", "wx": "00ea3ea2873b6fc099bfd779b0a2c23c2c4354e2fec4536f3b8e420988", "wy": "00f97e1c7646b4eb3de616752f415ab3a6f696d1d674fb4b6732252382"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ea3ea2873b6fc099bfd779b0a2c23c2c4354e2fec4536f3b8e420988f97e1c7646b4eb3de616752f415ab3a6f696d1d674fb4b6732252382", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6j6ihztvwJm/13mwosI8LENU4v7EU287\njkIJiPl+HHZGtOs95hZ1L0Fas6b2ltHWdPtLZzIlI4I=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 260, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0492ae54e38b4e9c6ae9943193747c4c8acc6c96f422515288e9698a13e8f3a759a1a8273c53f4b4b18bfcf78d9bb988adb3b005002dbe434c", "wx": "0092ae54e38b4e9c6ae9943193747c4c8acc6c96f422515288e9698a13", "wy": "00e8f3a759a1a8273c53f4b4b18bfcf78d9bb988adb3b005002dbe434c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000492ae54e38b4e9c6ae9943193747c4c8acc6c96f422515288e9698a13e8f3a759a1a8273c53f4b4b18bfcf78d9bb988adb3b005002dbe434c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkq5U44tOnGrplDGTdHxMisxslvQiUVKI\n6WmKE+jzp1mhqCc8U/S0sYv8942buYits7AFAC2+Q0w=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 261, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b157315cc1aaeae64eb5b38452884195fdfe8a15fb5618284f48afe5e1fbbaad729477a45f3752b7f72ad2f9cd7dce4158a8e21b8127e8a7", "wx": "00b157315cc1aaeae64eb5b38452884195fdfe8a15fb5618284f48afe5", "wy": "00e1fbbaad729477a45f3752b7f72ad2f9cd7dce4158a8e21b8127e8a7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b157315cc1aaeae64eb5b38452884195fdfe8a15fb5618284f48afe5e1fbbaad729477a45f3752b7f72ad2f9cd7dce4158a8e21b8127e8a7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsVcxXMGq6uZOtbOEUohBlf3+ihX7Vhgo\nT0iv5eH7uq1ylHekXzdSt/cq0vnNfc5BWKjiG4En6Kc=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 262, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0487d9d964044b5b16801f32de9f3f9066194e8bf80affa3cb0d4ddb1db5eb9b6594e6d1bcacd0fd9d67c408f789dfb95feb79a6e2fb9c4cee", "wx": "0087d9d964044b5b16801f32de9f3f9066194e8bf80affa3cb0d4ddb1d", "wy": "00b5eb9b6594e6d1bcacd0fd9d67c408f789dfb95feb79a6e2fb9c4cee"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000487d9d964044b5b16801f32de9f3f9066194e8bf80affa3cb0d4ddb1db5eb9b6594e6d1bcacd0fd9d67c408f789dfb95feb79a6e2fb9c4cee", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEh9nZZARLWxaAHzLenz+QZhlOi/gK/6PL\nDU3bHbXrm2WU5tG8rND9nWfECPeJ37lf63mm4vucTO4=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 263, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04461b435af09ede35e74dac21f9af7b1b9998213039f8785d4a4905f518b89bde69de34a482638461d09386e7193ca90ca5b3038e2a3885d1", "wx": "461b435af09ede35e74dac21f9af7b1b9998213039f8785d4a4905f5", "wy": "18b89bde69de34a482638461d09386e7193ca90ca5b3038e2a3885d1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004461b435af09ede35e74dac21f9af7b1b9998213039f8785d4a4905f518b89bde69de34a482638461d09386e7193ca90ca5b3038e2a3885d1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERhtDWvCe3jXnTawh+a97G5mYITA5+Hhd\nSkkF9Ri4m95p3jSkgmOEYdCThucZPKkMpbMDjio4hdE=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 264, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 265, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048093af8db04b3dd2e7c3c59bb64a832c2fb8e8e141bae7ba1534950a10c5e87aecbd1fcdfc36cd18d41b3238b2ac613eb7c9de988d881816", "wx": "008093af8db04b3dd2e7c3c59bb64a832c2fb8e8e141bae7ba1534950a", "wy": "10c5e87aecbd1fcdfc36cd18d41b3238b2ac613eb7c9de988d881816"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048093af8db04b3dd2e7c3c59bb64a832c2fb8e8e141bae7ba1534950a10c5e87aecbd1fcdfc36cd18d41b3238b2ac613eb7c9de988d881816", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEgJOvjbBLPdLnw8WbtkqDLC+46OFBuue6\nFTSVChDF6HrsvR/N/DbNGNQbMjiyrGE+t8nemI2IGBY=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 266, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c6d71f4ba0933f1269f7d6df83fd0c9c67254f101dcc126dc15faa3e3c45dc9fedc71c9f2b0dd1b12b656241f5e335066f3f925bdbcfe98f", "wx": "00c6d71f4ba0933f1269f7d6df83fd0c9c67254f101dcc126dc15faa3e", "wy": "3c45dc9fedc71c9f2b0dd1b12b656241f5e335066f3f925bdbcfe98f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c6d71f4ba0933f1269f7d6df83fd0c9c67254f101dcc126dc15faa3e3c45dc9fedc71c9f2b0dd1b12b656241f5e335066f3f925bdbcfe98f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExtcfS6CTPxJp99bfg/0MnGclTxAdzBJt\nwV+qPjxF3J/txxyfKw3RsStlYkH14zUGbz+SW9vP6Y8=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 267, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04beb9d8dcba48146b9032688ecea947a231e7d0e6ce17d76b56ed634835503f3b4af414870ef03383784b1d846b3e07b8e9fc2d6190a3bfda", "wx": "00beb9d8dcba48146b9032688ecea947a231e7d0e6ce17d76b56ed6348", "wy": "35503f3b4af414870ef03383784b1d846b3e07b8e9fc2d6190a3bfda"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004beb9d8dcba48146b9032688ecea947a231e7d0e6ce17d76b56ed634835503f3b4af414870ef03383784b1d846b3e07b8e9fc2d6190a3bfda", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvrnY3LpIFGuQMmiOzqlHojHn0ObOF9dr\nVu1jSDVQPztK9BSHDvAzg3hLHYRrPge46fwtYZCjv9o=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 268, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041955ba3f90e7a739471a5d182b594c9747eb49d5356203f3bb8b939c807d88ce3a0885bfa5b5b7f6e9beb18285e7130524b6c1498b3269ee", "wx": "1955ba3f90e7a739471a5d182b594c9747eb49d5356203f3bb8b939c", "wy": "00807d88ce3a0885bfa5b5b7f6e9beb18285e7130524b6c1498b3269ee"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041955ba3f90e7a739471a5d182b594c9747eb49d5356203f3bb8b939c807d88ce3a0885bfa5b5b7f6e9beb18285e7130524b6c1498b3269ee", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEGVW6P5DnpzlHGl0YK1lMl0frSdU1YgPz\nu4uTnIB9iM46CIW/pbW39um+sYKF5xMFJLbBSYsyae4=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 269, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045cb9e5a5071f2b37aa3a5e5f389f54f996b0bc8a132ecb6885318fbf4ec5f8b93d8bf2a3b64fa7cac316392562c46567963c43a69f7a37fd", "wx": "5cb9e5a5071f2b37aa3a5e5f389f54f996b0bc8a132ecb6885318fbf", "wy": "4ec5f8b93d8bf2a3b64fa7cac316392562c46567963c43a69f7a37fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045cb9e5a5071f2b37aa3a5e5f389f54f996b0bc8a132ecb6885318fbf4ec5f8b93d8bf2a3b64fa7cac316392562c46567963c43a69f7a37fd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEXLnlpQcfKzeqOl5fOJ9U+ZawvIoTLsto\nhTGPv07F+Lk9i/Kjtk+nysMWOSVixGVnljxDpp96N/0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 270, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047b34ef8723a4309c0fa8a7ec3a783477652a82892370f6763314fe7bdee663853071e35fd3c76f991d7843c5e168ca659b93bd6015518fba", "wx": "7b34ef8723a4309c0fa8a7ec3a783477652a82892370f6763314fe7b", "wy": "00dee663853071e35fd3c76f991d7843c5e168ca659b93bd6015518fba"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047b34ef8723a4309c0fa8a7ec3a783477652a82892370f6763314fe7bdee663853071e35fd3c76f991d7843c5e168ca659b93bd6015518fba", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEezTvhyOkMJwPqKfsOng0d2UqgokjcPZ2\nMxT+e97mY4UwceNf08dvmR14Q8XhaMplm5O9YBVRj7o=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 271, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0403f26a9c13979cf5d090ea25dc966398022ceec31504abc4b10f76767d577dcf47e10e384c6b9a229a455a9fd33e54fe7960b8b0160aef16", "wx": "03f26a9c13979cf5d090ea25dc966398022ceec31504abc4b10f7676", "wy": "7d577dcf47e10e384c6b9a229a455a9fd33e54fe7960b8b0160aef16"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000403f26a9c13979cf5d090ea25dc966398022ceec31504abc4b10f76767d577dcf47e10e384c6b9a229a455a9fd33e54fe7960b8b0160aef16", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEA/JqnBOXnPXQkOol3JZjmAIs7sMVBKvE\nsQ92dn1Xfc9H4Q44TGuaIppFWp/TPlT+eWC4sBYK7xY=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 272, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b671296dd5f690502e4b1500e4acb4c82d3aa8dfbc5868a643f86a3ca46ba8c3a7b823259522291e2416232276cca8503cc8dbf941f1d93d", "wx": "00b671296dd5f690502e4b1500e4acb4c82d3aa8dfbc5868a643f86a3c", "wy": "00a46ba8c3a7b823259522291e2416232276cca8503cc8dbf941f1d93d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b671296dd5f690502e4b1500e4acb4c82d3aa8dfbc5868a643f86a3ca46ba8c3a7b823259522291e2416232276cca8503cc8dbf941f1d93d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtnEpbdX2kFAuSxUA5Ky0yC06qN+8WGim\nQ/hqPKRrqMOnuCMllSIpHiQWIyJ2zKhQPMjb+UHx2T0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 273, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 274, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0476e34b57a8c61df59cb0b7921cec6e5422344033f7accb7b3179e682cefd0a848309d1decf98a3b9e333691b95c17821cb681137630c02e2", "wx": "76e34b57a8c61df59cb0b7921cec6e5422344033f7accb7b3179e682", "wy": "00cefd0a848309d1decf98a3b9e333691b95c17821cb681137630c02e2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000476e34b57a8c61df59cb0b7921cec6e5422344033f7accb7b3179e682cefd0a848309d1decf98a3b9e333691b95c17821cb681137630c02e2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEduNLV6jGHfWcsLeSHOxuVCI0QDP3rMt7\nMXnmgs79CoSDCdHez5ijueMzaRuVwXghy2gRN2MMAuI=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 275, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0451839e545c872f4a381f278ed5b4c24cf38aac77b02953405618bf27394e41226594c499db6a7dd7a6901bda5e6474b1ffa10a6567210010", "wx": "51839e545c872f4a381f278ed5b4c24cf38aac77b02953405618bf27", "wy": "394e41226594c499db6a7dd7a6901bda5e6474b1ffa10a6567210010"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000451839e545c872f4a381f278ed5b4c24cf38aac77b02953405618bf27394e41226594c499db6a7dd7a6901bda5e6474b1ffa10a6567210010", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEUYOeVFyHL0o4HyeO1bTCTPOKrHewKVNA\nVhi/JzlOQSJllMSZ22p916aQG9peZHSx/6EKZWchABA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 276, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a3ec90053d1e100815d1becfe96c9b3646e52df794f6b03b766a7574c3b7e17e73acc8cefe71b6eb13d4f1c94c57e58bee43c69d9d41a964", "wx": "00a3ec90053d1e100815d1becfe96c9b3646e52df794f6b03b766a7574", "wy": "00c3b7e17e73acc8cefe71b6eb13d4f1c94c57e58bee43c69d9d41a964"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a3ec90053d1e100815d1becfe96c9b3646e52df794f6b03b766a7574c3b7e17e73acc8cefe71b6eb13d4f1c94c57e58bee43c69d9d41a964", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEo+yQBT0eEAgV0b7P6WybNkblLfeU9rA7\ndmp1dMO34X5zrMjO/nG26xPU8clMV+WL7kPGnZ1BqWQ=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 277, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b5c09b4851a67371eee7bbf02451e5208c40de61bc1a33df2710b384dcce4e5b83c32a800e8de28fa936d582cdcad185e894caac797f1d14", "wx": "00b5c09b4851a67371eee7bbf02451e5208c40de61bc1a33df2710b384", "wy": "00dcce4e5b83c32a800e8de28fa936d582cdcad185e894caac797f1d14"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b5c09b4851a67371eee7bbf02451e5208c40de61bc1a33df2710b384dcce4e5b83c32a800e8de28fa936d582cdcad185e894caac797f1d14", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtcCbSFGmc3Hu57vwJFHlIIxA3mG8GjPf\nJxCzhNzOTluDwyqADo3ij6k21YLNytGF6JTKrHl/HRQ=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 278, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04941e283be31300bfd4f6a12b876fd3267352551cc49e9eef73f76538c115e5fe3b92f643c6cef1c58f3f8657574d1f64957d4880995cde83", "wx": "00941e283be31300bfd4f6a12b876fd3267352551cc49e9eef73f76538", "wy": "00c115e5fe3b92f643c6cef1c58f3f8657574d1f64957d4880995cde83"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004941e283be31300bfd4f6a12b876fd3267352551cc49e9eef73f76538c115e5fe3b92f643c6cef1c58f3f8657574d1f64957d4880995cde83", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAElB4oO+MTAL/U9qErh2/TJnNSVRzEnp7v\nc/dlOMEV5f47kvZDxs7xxY8/hldXTR9klX1IgJlc3oM=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 279, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d008ac44bff876cbf7e2842eec13b63fcb3d6e7360aca5698f3ef0f1811", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0443c9ccd08a80bca18022722b0bdcd790d82a3ef8b65c3f34204bb4729ee1c1f00598130b2313a3e38a3798d03dac665cff20f36ce8a2024a", "wx": "43c9ccd08a80bca18022722b0bdcd790d82a3ef8b65c3f34204bb472", "wy": "009ee1c1f00598130b2313a3e38a3798d03dac665cff20f36ce8a2024a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000443c9ccd08a80bca18022722b0bdcd790d82a3ef8b65c3f34204bb4729ee1c1f00598130b2313a3e38a3798d03dac665cff20f36ce8a2024a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEQ8nM0IqAvKGAInIrC9zXkNgqPvi2XD80\nIEu0cp7hwfAFmBMLIxOj44o3mNA9rGZc/yDzbOiiAko=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 280, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d958e418fad1c5ea5c923e6185e03ed5539d3f5f58dfac8bb9f104596997e408c97be5fdc037a5c004389d4b97eb1f54635e985853c1f082", "wx": "00d958e418fad1c5ea5c923e6185e03ed5539d3f5f58dfac8bb9f10459", "wy": "6997e408c97be5fdc037a5c004389d4b97eb1f54635e985853c1f082"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d958e418fad1c5ea5c923e6185e03ed5539d3f5f58dfac8bb9f104596997e408c97be5fdc037a5c004389d4b97eb1f54635e985853c1f082", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE2VjkGPrRxepckj5hheA+1VOdP19Y36yL\nufEEWWmX5AjJe+X9wDelwAQ4nUuX6x9UY16YWFPB8II=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 281, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d629b434c9b5d157bd72e114fd839553f7f0e94600934a0a49e59aa4713a13c01775e75e2ebae75d9e29d2506184177b7dd0868693873596", "wx": "00d629b434c9b5d157bd72e114fd839553f7f0e94600934a0a49e59aa4", "wy": "713a13c01775e75e2ebae75d9e29d2506184177b7dd0868693873596"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d629b434c9b5d157bd72e114fd839553f7f0e94600934a0a49e59aa4713a13c01775e75e2ebae75d9e29d2506184177b7dd0868693873596", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1im0NMm10Ve9cuEU/YOVU/fw6UYAk0oK\nSeWapHE6E8AXdedeLrrnXZ4p0lBhhBd7fdCGhpOHNZY=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 282, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d1be91557d866ad5f2945b14ec3317bc43c1338fd06af6496201cce2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043d2e9bb9a712bf3ad42ac30659fdbda9be9956537f9f37cd05f0ff377d5982d6d9266d774942c44d9eb3501051d3b9688610131e7856ef36", "wx": "3d2e9bb9a712bf3ad42ac30659fdbda9be9956537f9f37cd05f0ff37", "wy": "7d5982d6d9266d774942c44d9eb3501051d3b9688610131e7856ef36"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043d2e9bb9a712bf3ad42ac30659fdbda9be9956537f9f37cd05f0ff377d5982d6d9266d774942c44d9eb3501051d3b9688610131e7856ef36", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPS6buacSvzrUKsMGWf29qb6ZVlN/nzfN\nBfD/N31ZgtbZJm13SULETZ6zUBBR07lohhATHnhW7zY=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 283, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7ac54a381d9bd3f2698359d6f658b5e4167d15a75b576e82d2efbd37", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a0be2f10144b9b42b016f1bd9fca30e4c24aae4775596c7cdb07ae60d60ff3a70f1541631f6087d3f3b3fe376d2305b50b94821106412479", "wx": "00a0be2f10144b9b42b016f1bd9fca30e4c24aae4775596c7cdb07ae60", "wy": "00d60ff3a70f1541631f6087d3f3b3fe376d2305b50b94821106412479"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a0be2f10144b9b42b016f1bd9fca30e4c24aae4775596c7cdb07ae60d60ff3a70f1541631f6087d3f3b3fe376d2305b50b94821106412479", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEoL4vEBRLm0KwFvG9n8ow5MJKrkd1WWx8\n2weuYNYP86cPFUFjH2CH0/Oz/jdtIwW1C5SCEQZBJHk=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 284, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c4fbb063e82402e16fe14edda4d7986b0b88344a1f53b0e2684ee7e31", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044d74397a586c8ac5e326bed03720bde7037e4a07aee7209f70493cab106778bfd081d17ab6dcb8fd8a454962941c26ecc19cda9fb77719db", "wx": "4d74397a586c8ac5e326bed03720bde7037e4a07aee7209f70493cab", "wy": "106778bfd081d17ab6dcb8fd8a454962941c26ecc19cda9fb77719db"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044d74397a586c8ac5e326bed03720bde7037e4a07aee7209f70493cab106778bfd081d17ab6dcb8fd8a454962941c26ecc19cda9fb77719db", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETXQ5elhsisXjJr7QNyC95wN+Sgeu5yCf\ncEk8qxBneL/QgdF6tty4/YpFSWKUHCbswZzan7d3Gds=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 285, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d3be5f50d726f99b8ac44bff876bfe78dd7ae630d227ef0ba87ae39b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048c2f149b1738243f81a6f12135395a2ba2718863622e66e33efc241f5638cf6ae9cfb39578cf3a719702052e5e9e940216c5136dcb6ef085", "wx": "008c2f149b1738243f81a6f12135395a2ba2718863622e66e33efc241f", "wy": "5638cf6ae9cfb39578cf3a719702052e5e9e940216c5136dcb6ef085"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048c2f149b1738243f81a6f12135395a2ba2718863622e66e33efc241f5638cf6ae9cfb39578cf3a719702052e5e9e940216c5136dcb6ef085", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEjC8Umxc4JD+BpvEhNTlaK6JxiGNiLmbj\nPvwkH1Y4z2rpz7OVeM86cZcCBS5enpQCFsUTbctu8IU=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 286, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00e5f50d726f99b8ac44bff876cbf710e47f9087d1afdfb1dab6d6daf1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ad5227e48afaa165e7b97ef8210687556e10643fda8a377aaf4f5bf412e86d4ae55f4460aba6a932f307ee78efdc136e9a3df6313100bf4f", "wx": "00ad5227e48afaa165e7b97ef8210687556e10643fda8a377aaf4f5bf4", "wy": "12e86d4ae55f4460aba6a932f307ee78efdc136e9a3df6313100bf4f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ad5227e48afaa165e7b97ef8210687556e10643fda8a377aaf4f5bf412e86d4ae55f4460aba6a932f307ee78efdc136e9a3df6313100bf4f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErVIn5Ir6oWXnuX74IQaHVW4QZD/aijd6\nr09b9BLobUrlX0Rgq6apMvMH7njv3BNumj32MTEAv08=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 287, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00cbea1ae4df337158897ff0ed97ef0b261e681f654be23a7011518ba5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043fb94a3165ecdef43fa27907ed075caf52c25420ac7bc7bb90408992023c4d7b4775b591ae223dd4da9ceaabd73b9743ddab8b40576e393f", "wx": "3fb94a3165ecdef43fa27907ed075caf52c25420ac7bc7bb90408992", "wy": "023c4d7b4775b591ae223dd4da9ceaabd73b9743ddab8b40576e393f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043fb94a3165ecdef43fa27907ed075caf52c25420ac7bc7bb90408992023c4d7b4775b591ae223dd4da9ceaabd73b9743ddab8b40576e393f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEP7lKMWXs3vQ/onkH7Qdcr1LCVCCse8e7\nkECJkgI8TXtHdbWRriI91Nqc6qvXO5dD3auLQFduOT8=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 288, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d726f99b8ac44bff876cbf7e28422aa07ec3cb1d9472bd704f4029f0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e45fcf0a7f4dc2a308dc7868251423fbf71a205a9546850a01a732fc9a73ca4d41175076f2f362b276ecb0ccdb6e0bb30c4a1b35c2e3ed82", "wx": "00e45fcf0a7f4dc2a308dc7868251423fbf71a205a9546850a01a732fc", "wy": "009a73ca4d41175076f2f362b276ecb0ccdb6e0bb30c4a1b35c2e3ed82"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e45fcf0a7f4dc2a308dc7868251423fbf71a205a9546850a01a732fc9a73ca4d41175076f2f362b276ecb0ccdb6e0bb30c4a1b35c2e3ed82", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE5F/PCn9NwqMI3HhoJRQj+/caIFqVRoUK\nAacy/Jpzyk1BF1B28vNisnbssMzbbguzDEobNcLj7YI=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 289, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d009720b755413cca9506b5d27589e58ac4bed856762ba7ae20ab5b43cc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043c59e13982fd9c1a45991b1e9d79e939a52a62ca479764f1477e28131b004c9bffd7f00c05e3168c625cc93ab7a0f1ba8d6fa26a4d5162cb", "wx": "3c59e13982fd9c1a45991b1e9d79e939a52a62ca479764f1477e2813", "wy": "1b004c9bffd7f00c05e3168c625cc93ab7a0f1ba8d6fa26a4d5162cb"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043c59e13982fd9c1a45991b1e9d79e939a52a62ca479764f1477e28131b004c9bffd7f00c05e3168c625cc93ab7a0f1ba8d6fa26a4d5162cb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPFnhOYL9nBpFmRsenXnpOaUqYspHl2Tx\nR34oExsATJv/1/AMBeMWjGJcyTq3oPG6jW+iak1RYss=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 290, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2e416eaa8279952a0d6ba4eb13cbfee69cf7bcae437232fbfa5a5d5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c6b8ff152d7a1b7a99ce3483bdeaaf5bd2ce64dc6b0f89cf3544b87c053ab6cf9cb510dc1440ab4e412a167f4c69365fcfc97f31d5ba4581", "wx": "00c6b8ff152d7a1b7a99ce3483bdeaaf5bd2ce64dc6b0f89cf3544b87c", "wy": "053ab6cf9cb510dc1440ab4e412a167f4c69365fcfc97f31d5ba4581"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c6b8ff152d7a1b7a99ce3483bdeaaf5bd2ce64dc6b0f89cf3544b87c053ab6cf9cb510dc1440ab4e412a167f4c69365fcfc97f31d5ba4581", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExrj/FS16G3qZzjSDveqvW9LOZNxrD4nP\nNUS4fAU6ts+ctRDcFECrTkEqFn9MaTZfz8l/MdW6RYE=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 291, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00c56225ffc3b65fbf142177609db189ab5bd013246f19e11ca5b5a127", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047c0772fb6553c0ec0dd1f73b5db380764d9f2f7afb4eac1e774dacd56e2e5de0db63bf03cf9675eae6d2dfe5424e79ab394951c9b60ad5df", "wx": "7c0772fb6553c0ec0dd1f73b5db380764d9f2f7afb4eac1e774dacd5", "wy": "6e2e5de0db63bf03cf9675eae6d2dfe5424e79ab394951c9b60ad5df"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047c0772fb6553c0ec0dd1f73b5db380764d9f2f7afb4eac1e774dacd56e2e5de0db63bf03cf9675eae6d2dfe5424e79ab394951c9b60ad5df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEfAdy+2VTwOwN0fc7XbOAdk2fL3r7Tqwe\nd02s1W4uXeDbY78Dz5Z16ubS3+VCTnmrOUlRybYK1d8=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 292, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00a7dd831f4120170b7f0a76ed26bc4ea9cc9e1a70048c1bb5f0a55437", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044108e0ccd47cba09fb7ed4d9f3455823780965157861c1bf8f93d34b46d6fdb71e9e89adaae71376b13fd17644b11eed00d498783da0ba1a", "wx": "4108e0ccd47cba09fb7ed4d9f3455823780965157861c1bf8f93d34b", "wy": "46d6fdb71e9e89adaae71376b13fd17644b11eed00d498783da0ba1a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044108e0ccd47cba09fb7ed4d9f3455823780965157861c1bf8f93d34b46d6fdb71e9e89adaae71376b13fd17644b11eed00d498783da0ba1a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEQQjgzNR8ugn7ftTZ80VYI3gJZRV4YcG/\nj5PTS0bW/bcenomtqucTdrE/0XZEsR7tANSYeD2guho=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 293, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042f2da40a1b72f67ba63613a243119c41c7252839cf106e86b5d8e6e35a1e0e2fc49b4f316f0c0e7236785749eb34ce923c23aef330af8733", "wx": "2f2da40a1b72f67ba63613a243119c41c7252839cf106e86b5d8e6e3", "wy": "5a1e0e2fc49b4f316f0c0e7236785749eb34ce923c23aef330af8733"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042f2da40a1b72f67ba63613a243119c41c7252839cf106e86b5d8e6e35a1e0e2fc49b4f316f0c0e7236785749eb34ce923c23aef330af8733", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELy2kChty9numNhOiQxGcQcclKDnPEG6G\ntdjm41oeDi/Em08xbwwOcjZ4V0nrNM6SPCOu8zCvhzM=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 294, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047dc09710f4f586af05b08f0c9dcd48b1308733c97767fc286d1c72834353a704c7950b8f4a11394bc8db06adccf19d8ed95c7f214a173137", "wx": "7dc09710f4f586af05b08f0c9dcd48b1308733c97767fc286d1c7283", "wy": "4353a704c7950b8f4a11394bc8db06adccf19d8ed95c7f214a173137"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047dc09710f4f586af05b08f0c9dcd48b1308733c97767fc286d1c72834353a704c7950b8f4a11394bc8db06adccf19d8ed95c7f214a173137", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEfcCXEPT1hq8FsI8Mnc1IsTCHM8l3Z/wo\nbRxyg0NTpwTHlQuPShE5S8jbBq3M8Z2O2Vx/IUoXMTc=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04dbb439e2c3e9d1822b94ccc7d98c9fcb668e65dd6a759ad2dfdcd32882663234e6da512d7d7d5fe79156ad0e19ffc62d618e3cf48276106d", "wx": "00dbb439e2c3e9d1822b94ccc7d98c9fcb668e65dd6a759ad2dfdcd328", "wy": "0082663234e6da512d7d7d5fe79156ad0e19ffc62d618e3cf48276106d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004dbb439e2c3e9d1822b94ccc7d98c9fcb668e65dd6a759ad2dfdcd32882663234e6da512d7d7d5fe79156ad0e19ffc62d618e3cf48276106d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE27Q54sPp0YIrlMzH2Yyfy2aOZd1qdZrS\n39zTKIJmMjTm2lEtfX1f55FWrQ4Z/8YtYY489IJ2EG0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e012dc20cca5bd2adfaa27f57419596ce09ed0f18a9148e30a0f6ed255beca1b5e3e2485ef9537ae48a67b72dbcf6d7b33372023a5c443e8", "wx": "00e012dc20cca5bd2adfaa27f57419596ce09ed0f18a9148e30a0f6ed2", "wy": "55beca1b5e3e2485ef9537ae48a67b72dbcf6d7b33372023a5c443e8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e012dc20cca5bd2adfaa27f57419596ce09ed0f18a9148e30a0f6ed255beca1b5e3e2485ef9537ae48a67b72dbcf6d7b33372023a5c443e8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4BLcIMylvSrfqif1dBlZbOCe0PGKkUjj\nCg9u0lW+yhtePiSF75U3rkime3Lbz217MzcgI6XEQ+g=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c510ab34abd4855c54d62407abe6ca090c73ba49aca9de9bf117bca242b3b00c272c22681af7c255120fac148ad73c81b47846e4ad2f5627", "wx": "00c510ab34abd4855c54d62407abe6ca090c73ba49aca9de9bf117bca2", "wy": "42b3b00c272c22681af7c255120fac148ad73c81b47846e4ad2f5627"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c510ab34abd4855c54d62407abe6ca090c73ba49aca9de9bf117bca242b3b00c272c22681af7c255120fac148ad73c81b47846e4ad2f5627", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExRCrNKvUhVxU1iQHq+bKCQxzukmsqd6b\n8Re8okKzsAwnLCJoGvfCVRIPrBSK1zyBtHhG5K0vVic=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0408a6e167536a47aaa224fec21ce077642efdb97d93ae16b9672279f433fb9f1abb25f2c0c3e6008ac857ede4a89ca8d9d08b8996614969ac", "wx": "08a6e167536a47aaa224fec21ce077642efdb97d93ae16b9672279f4", "wy": "33fb9f1abb25f2c0c3e6008ac857ede4a89ca8d9d08b8996614969ac"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000408a6e167536a47aaa224fec21ce077642efdb97d93ae16b9672279f433fb9f1abb25f2c0c3e6008ac857ede4a89ca8d9d08b8996614969ac", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECKbhZ1NqR6qiJP7CHOB3ZC79uX2Trha5\nZyJ59DP7nxq7JfLAw+YAishX7eSonKjZ0IuJlmFJaaw=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041a83e185fcf30e6c69cf292e497d63cc04e6fd07cb9365a74be3c39c6b2d56247df49cf94176c4e8efc84ec710cd0d614dd066c16f6ad3e0", "wx": "1a83e185fcf30e6c69cf292e497d63cc04e6fd07cb9365a74be3c39c", "wy": "6b2d56247df49cf94176c4e8efc84ec710cd0d614dd066c16f6ad3e0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041a83e185fcf30e6c69cf292e497d63cc04e6fd07cb9365a74be3c39c6b2d56247df49cf94176c4e8efc84ec710cd0d614dd066c16f6ad3e0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEGoPhhfzzDmxpzykuSX1jzATm/QfLk2Wn\nS+PDnGstViR99Jz5QXbE6O/ITscQzQ1hTdBmwW9q0+A=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042d59efd841a44b83fd42e6a2984a53fa93ad242c11678f92202cccfb95bcaf0b2f6eb0e6d4d83e3260e037d3dc0e48ab6c4141ce6b56cad0", "wx": "2d59efd841a44b83fd42e6a2984a53fa93ad242c11678f92202cccfb", "wy": "0095bcaf0b2f6eb0e6d4d83e3260e037d3dc0e48ab6c4141ce6b56cad0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042d59efd841a44b83fd42e6a2984a53fa93ad242c11678f92202cccfb95bcaf0b2f6eb0e6d4d83e3260e037d3dc0e48ab6c4141ce6b56cad0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELVnv2EGkS4P9QuaimEpT+pOtJCwRZ4+S\nICzM+5W8rwsvbrDm1Ng+MmDgN9PcDkirbEFBzmtWytA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041161c7add6f67f995b93e19eb18bd5e73fd71d6bb10dceef0b792e9c08c44cef9826b4ed67508c09d07ec857a0ea49ed1a7f1fa2c74cb838", "wx": "1161c7add6f67f995b93e19eb18bd5e73fd71d6bb10dceef0b792e9c", "wy": "08c44cef9826b4ed67508c09d07ec857a0ea49ed1a7f1fa2c74cb838"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041161c7add6f67f995b93e19eb18bd5e73fd71d6bb10dceef0b792e9c08c44cef9826b4ed67508c09d07ec857a0ea49ed1a7f1fa2c74cb838", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEEWHHrdb2f5lbk+GesYvV5z/XHWuxDc7v\nC3kunAjETO+YJrTtZ1CMCdB+yFeg6kntGn8fosdMuDg=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 302, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0484dc3d2ebfcf3480713baeff30ad0781bc8c4d06ab6ddd4f7f1045af7570537c5d71a78b1a041aca0fe35f642824abda8c3ff2e9fcf5c8cb", "wx": "0084dc3d2ebfcf3480713baeff30ad0781bc8c4d06ab6ddd4f7f1045af", "wy": "7570537c5d71a78b1a041aca0fe35f642824abda8c3ff2e9fcf5c8cb"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000484dc3d2ebfcf3480713baeff30ad0781bc8c4d06ab6ddd4f7f1045af7570537c5d71a78b1a041aca0fe35f642824abda8c3ff2e9fcf5c8cb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEhNw9Lr/PNIBxO67/MK0HgbyMTQarbd1P\nfxBFr3VwU3xdcaeLGgQayg/jX2QoJKvajD/y6fz1yMs=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041767574e645c550ef3d353f76d4428f9616ac288b36378857de332629fe09825a57f3a0ec11189f4560272297ab6d5e095401febb60d0dc9", "wx": "1767574e645c550ef3d353f76d4428f9616ac288b36378857de33262", "wy": "009fe09825a57f3a0ec11189f4560272297ab6d5e095401febb60d0dc9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041767574e645c550ef3d353f76d4428f9616ac288b36378857de332629fe09825a57f3a0ec11189f4560272297ab6d5e095401febb60d0dc9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEF2dXTmRcVQ7z01P3bUQo+WFqwoizY3iF\nfeMyYp/gmCWlfzoOwRGJ9FYCcil6ttXglUAf67YNDck=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c1ef359e4bd146f63d8155c5c2523fa3353c9820f84f28150bad3819a", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041767574e645c550ef3d353f76d4428f9616ac288b36378857de33262601f67da5a80c5f13eee760ba9fd8dd585492a1f6abfe01449f2f238", "wx": "1767574e645c550ef3d353f76d4428f9616ac288b36378857de33262", "wy": "601f67da5a80c5f13eee760ba9fd8dd585492a1f6abfe01449f2f238"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041767574e645c550ef3d353f76d4428f9616ac288b36378857de33262601f67da5a80c5f13eee760ba9fd8dd585492a1f6abfe01449f2f238", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEF2dXTmRcVQ7z01P3bUQo+WFqwoizY3iF\nfeMyYmAfZ9pagMXxPu52C6n9jdWFSSofar/gFEny8jg=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "duplication bug", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c1ef359e4bd146f63d8155c5c2523fa3353c9820f84f28150bad3819a", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e2ef8c8ccb58eba287d9279b349e7652cca3e7cda188a5f179d77142f87594f3664c0faf7b59670e353a370d1d68ad89d6a1e246b4d03bee", "wx": "00e2ef8c8ccb58eba287d9279b349e7652cca3e7cda188a5f179d77142", "wy": "00f87594f3664c0faf7b59670e353a370d1d68ad89d6a1e246b4d03bee"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e2ef8c8ccb58eba287d9279b349e7652cca3e7cda188a5f179d77142f87594f3664c0faf7b59670e353a370d1d68ad89d6a1e246b4d03bee", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4u+MjMtY66KH2SebNJ52Usyj582hiKXx\neddxQvh1lPNmTA+ve1lnDjU6Nw0daK2J1qHiRrTQO+4=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b8bf3ef9646abfffb84220104ec996a92cef33f9328ec4cb1ea699484fea51a0de9e9d801babd42ca0924b36498bc5900fbeb9cbd5ad9c1a", "wx": "00b8bf3ef9646abfffb84220104ec996a92cef33f9328ec4cb1ea69948", "wy": "4fea51a0de9e9d801babd42ca0924b36498bc5900fbeb9cbd5ad9c1a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b8bf3ef9646abfffb84220104ec996a92cef33f9328ec4cb1ea699484fea51a0de9e9d801babd42ca0924b36498bc5900fbeb9cbd5ad9c1a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEuL8++WRqv/+4QiAQTsmWqSzvM/kyjsTL\nHqaZSE/qUaDenp2AG6vULKCSSzZJi8WQD765y9WtnBo=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04286e80429c8796dcd885d95f960d209fed19f87e2ce423d166c8e2202e30882c09970d5dd58b67e5bb80affec74248a9cb4a783384c8b6a0", "wx": "286e80429c8796dcd885d95f960d209fed19f87e2ce423d166c8e220", "wy": "2e30882c09970d5dd58b67e5bb80affec74248a9cb4a783384c8b6a0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004286e80429c8796dcd885d95f960d209fed19f87e2ce423d166c8e2202e30882c09970d5dd58b67e5bb80affec74248a9cb4a783384c8b6a0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEKG6AQpyHltzYhdlflg0gn+0Z+H4s5CPR\nZsjiIC4wiCwJlw1d1Ytn5buAr/7HQkipy0p4M4TItqA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045599a3faf96aba7302bd3d98cfde69525b7292762383f4a0b5c310393faa45feb6c35d2b7bf25ffc633c420ebfc4e715765302c5a11ac793", "wx": "5599a3faf96aba7302bd3d98cfde69525b7292762383f4a0b5c31039", "wy": "3faa45feb6c35d2b7bf25ffc633c420ebfc4e715765302c5a11ac793"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045599a3faf96aba7302bd3d98cfde69525b7292762383f4a0b5c310393faa45feb6c35d2b7bf25ffc633c420ebfc4e715765302c5a11ac793", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVZmj+vlqunMCvT2Yz95pUltyknYjg/Sg\ntcMQOT+qRf62w10re/Jf/GM8Qg6/xOcVdlMCxaEax5M=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045b5234b8db6bbd396eae7d1ca4e6d877824c98cde9fbfab34b6b8ccb1f38ae9f87adc3e6d2474eb5e3cd9aeff0927320214be550f5e62ed4", "wx": "5b5234b8db6bbd396eae7d1ca4e6d877824c98cde9fbfab34b6b8ccb", "wy": "1f38ae9f87adc3e6d2474eb5e3cd9aeff0927320214be550f5e62ed4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045b5234b8db6bbd396eae7d1ca4e6d877824c98cde9fbfab34b6b8ccb1f38ae9f87adc3e6d2474eb5e3cd9aeff0927320214be550f5e62ed4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEW1I0uNtrvTlurn0cpObYd4JMmM3p+/qz\nS2uMyx84rp+HrcPm0kdOtePNmu/wknMgIUvlUPXmLtQ=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aced4ea8949e5ae37ef2f5eb5e00675d08e17c34be6677b0f269b6725e3ad0af49ebfff415ee4f2a838ead1f84cafaa652c17acc26130725", "wx": "00aced4ea8949e5ae37ef2f5eb5e00675d08e17c34be6677b0f269b672", "wy": "5e3ad0af49ebfff415ee4f2a838ead1f84cafaa652c17acc26130725"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aced4ea8949e5ae37ef2f5eb5e00675d08e17c34be6677b0f269b6725e3ad0af49ebfff415ee4f2a838ead1f84cafaa652c17acc26130725", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErO1OqJSeWuN+8vXrXgBnXQjhfDS+Znew\n8mm2cl460K9J6//0Fe5PKoOOrR+EyvqmUsF6zCYTByU=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043e8c1bcc16195e8769e25d4c859807dffe178bed5bca9db06efa15324e3b53b3048b8ccd8cdc1265be240c8ee204060486a99ad31eaad3a4", "wx": "3e8c1bcc16195e8769e25d4c859807dffe178bed5bca9db06efa1532", "wy": "4e3b53b3048b8ccd8cdc1265be240c8ee204060486a99ad31eaad3a4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043e8c1bcc16195e8769e25d4c859807dffe178bed5bca9db06efa15324e3b53b3048b8ccd8cdc1265be240c8ee204060486a99ad31eaad3a4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPowbzBYZXodp4l1MhZgH3/4Xi+1byp2w\nbvoVMk47U7MEi4zNjNwSZb4kDI7iBAYEhqma0x6q06Q=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0424819323b7be8ab0910f7f33bd2f7669c44b13f09479965e95287d13b0592345beafbfdb8cf3629269bdd817728d5d5cd3c28bc6c6414a70", "wx": "24819323b7be8ab0910f7f33bd2f7669c44b13f09479965e95287d13", "wy": "00b0592345beafbfdb8cf3629269bdd817728d5d5cd3c28bc6c6414a70"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000424819323b7be8ab0910f7f33bd2f7669c44b13f09479965e95287d13b0592345beafbfdb8cf3629269bdd817728d5d5cd3c28bc6c6414a70", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJIGTI7e+irCRD38zvS92acRLE/CUeZZe\nlSh9E7BZI0W+r7/bjPNikmm92BdyjV1c08KLxsZBSnA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0444cf57bac30a83da39f90bf3faacd52211a70fb92547db7778ea6c812b3fd1bf14688d2770c50cd5a890a3807ba0e8612136a1b11e030f82", "wx": "44cf57bac30a83da39f90bf3faacd52211a70fb92547db7778ea6c81", "wy": "2b3fd1bf14688d2770c50cd5a890a3807ba0e8612136a1b11e030f82"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000444cf57bac30a83da39f90bf3faacd52211a70fb92547db7778ea6c812b3fd1bf14688d2770c50cd5a890a3807ba0e8612136a1b11e030f82", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERM9XusMKg9o5+Qvz+qzVIhGnD7klR9t3\neOpsgSs/0b8UaI0ncMUM1aiQo4B7oOhhITahsR4DD4I=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04dc17f1001d326127f7375cffa70b7530bca4da1040dc43d0044aaca07a146f04c5294cfe7e1ed587da55bae70b7fa8e32f6aa800314d01dd", "wx": "00dc17f1001d326127f7375cffa70b7530bca4da1040dc43d0044aaca0", "wy": "7a146f04c5294cfe7e1ed587da55bae70b7fa8e32f6aa800314d01dd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004dc17f1001d326127f7375cffa70b7530bca4da1040dc43d0044aaca07a146f04c5294cfe7e1ed587da55bae70b7fa8e32f6aa800314d01dd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE3BfxAB0yYSf3N1z/pwt1MLyk2hBA3EPQ\nBEqsoHoUbwTFKUz+fh7Vh9pVuucLf6jjL2qoADFNAd0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0468e2d7088eac18ba775bf68c5c509e86afd6f93451b4e4ee1d73e277e24ff4e27ef6c519db676d822c5db040482888013c8f3881bc9ac65a", "wx": "68e2d7088eac18ba775bf68c5c509e86afd6f93451b4e4ee1d73e277", "wy": "00e24ff4e27ef6c519db676d822c5db040482888013c8f3881bc9ac65a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000468e2d7088eac18ba775bf68c5c509e86afd6f93451b4e4ee1d73e277e24ff4e27ef6c519db676d822c5db040482888013c8f3881bc9ac65a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEaOLXCI6sGLp3W/aMXFCehq/W+TRRtOTu\nHXPid+JP9OJ+9sUZ22dtgixdsEBIKIgBPI84gbyaxlo=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cd4171adcb8be75e7734061a048b2bf228d167c2742d27f854392046865eb958ebd320ba87662ad3ac7af568c6be0f09be090bcfe083b3e5", "wx": "00cd4171adcb8be75e7734061a048b2bf228d167c2742d27f854392046", "wy": "00865eb958ebd320ba87662ad3ac7af568c6be0f09be090bcfe083b3e5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004cd4171adcb8be75e7734061a048b2bf228d167c2742d27f854392046865eb958ebd320ba87662ad3ac7af568c6be0f09be090bcfe083b3e5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEzUFxrcuL5153NAYaBIsr8ijRZ8J0LSf4\nVDkgRoZeuVjr0yC6h2Yq06x69WjGvg8JvgkLz+CDs+U=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eefdf99ab69d1888772cabe21d406045e1beab82761a7040beeb7ed359718c889af80f22f320fbe662d5ea0f65dfb4a5589c294ce5b73359", "wx": "00eefdf99ab69d1888772cabe21d406045e1beab82761a7040beeb7ed3", "wy": "59718c889af80f22f320fbe662d5ea0f65dfb4a5589c294ce5b73359"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eefdf99ab69d1888772cabe21d406045e1beab82761a7040beeb7ed359718c889af80f22f320fbe662d5ea0f65dfb4a5589c294ce5b73359", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7v35mradGIh3LKviHUBgReG+q4J2GnBA\nvut+01lxjIia+A8i8yD75mLV6g9l37SlWJwpTOW3M1k=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "extreme value for k", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 320, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d008ac44bff876cbf7e2842eec13b63fcb3d6e7360aca5698f3ef0f1811021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 322, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d008ac44bff876cbf7e2842eec13b63fcb3d6e7360aca5698f3ef0f1811021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "pseudorandom signature", "msg": "", "sig": "303d021c2770403d42b7b45e553308d1f6a480640b61cac0ae36665d6f14d34e021d0085506b0404265ededf9a89fc7c9c7a55c16c5b0d781f774de8f46fa1", "result": "valid", "flags": []}, {"tcId": 324, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303d021d00b68da722bbba7f6a58417bb5d0dd88f40316fc628b0edfcb0f02b062021c5c742e330b6febadf9a12d58ba2a7199629457ef2e9e4cecd2f09f50", "result": "valid", "flags": []}, {"tcId": 325, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303c021c01ec1ff15c8a55d697a5424d674753f82f711593828368d2fbb41a17021c20d9089db7baf46b8135e17e01645e732d22d5adb20e3772da740eee", "result": "valid", "flags": []}, {"tcId": 326, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303c021c3e46e9ba4dc089ff30fa8c0209c31b11ff49dbeec090f9f53c000c75021c6f2e3b36369416602bca83206809ed898fcf158a56c25a5474143f68", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c77b38da37079d27b837613ac3e8248d66eabd5d637076c8e62c7991e021d00d40cd9f81efc52db4429c0c1af7c1d8a22b6c7babbe7fbd8b5b3f02f", "result": "valid", "flags": []}, {"tcId": 328, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d008c03b32c166c0c8b99d7f876acd109447efb13f6b82945e78d51a269021c657568f1a0a8bd7df5ffa43097ebb2b64435c8e3335bcaafc63f9ed5", "result": "valid", "flags": []}, {"tcId": 329, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00d199a375253d30f1d2b4493542e9934f9f1f8b0680117679f5bc4ad2021c11419ddbf02c8ad5f518f8dac33f86a85e777af51a034132e2767a6d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d008ff82699e2e82870be9cfdd8a408bb34f8f38a83a4ac8370f18f2bc8021c7e5008fab6a0d4159200077ef9918dad6592cd8359838852c636ac05", "result": "valid", "flags": []}, {"tcId": 331, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c3f3b60b529ae0f950c517264adf2e481616bc47416742d5103589660021d00f731ebe98e58384b3a64b4696d4cc9619828ad51d7c39980749709a6", "result": "valid", "flags": []}, {"tcId": 332, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00dc11ffdc6b78754a335f168c4033916a2158d125a3f4fed9dc736661021c6dd84364717d9f4b0790f2b282f9245ecb316874eac025600397f109", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00a59b25b786d55f26b04dfe90ee02a6bde64ed6e431dc9fbdc3ab360e021d00fc14b5ad20f39da9900e35437936c8626fccf6632e7a3d9e587e3311", "result": "valid", "flags": []}, {"tcId": 334, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c2eda1f96c1a6e3ad8a3321ce82cbb13a5b935b501abf6c06f7fd2b3f021d00e81050c3e5f53a3c7b9d0bdb9ed92a326dfeac44791ba1abe4d6e973", "result": "valid", "flags": []}, {"tcId": 335, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c60f5e093fda08fc14ac99d820a18ad1370c58150bea0aca24fc6db9d021d00c2220a0ebbf4896e68fdb5bd824f88291c1c862b916f9c4af87f8f5f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c2ead37846a5e36a490b75140bdc7b636c6e9f6d8f980f6fadb08f769021d00e1fe130ae1798c196d7be62c7a5ddb3168cf4b8d48b6b6b4dc94ab3b", "result": "valid", "flags": []}, {"tcId": 337, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00a8a4c9416d72c860573d073281cb08c86ad65313f06b15a329e82eb2021c5a6edd2f0816b7263d915d72c67d50a854e3abee5cde1b679a0cef09", "result": "valid", "flags": []}, {"tcId": 338, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c576bb86c517bfecdc930a4c8501725548d425afbb96d93f5c1e2a0e1021c77248c5ecd620c431438c50e6bee6858091b54a87f8548ae35c21027", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c34e41cba628fd8787ba1a528f6015d2cae015c1c9a866e08a7133801021d0083d422ffdd99cc3c6d7096ef927f0b11988d1824e6e93840ff666ccd", "result": "valid", "flags": []}, {"tcId": 340, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c2558a42e79689244bccd5e855f6a1e42b4ff726873f30b532b89ef53021c07f9bd947785187175d848b6e2d79f7ab3bbc1087b42590b0cfb256a", "result": "valid", "flags": []}, {"tcId": 341, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00d5fe7dd5fb4fd1ea5ce66c0824f53f96ce47fd9b6c63b4d57827fd17021d00bce5bc3af705afaacb81bfa6d552d6198962fece9fba41546c602ddc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d008c1da2f07cdcbce4db8067b863468cfc728df52980229028689e57b6021c32175c1390a4b2cab6359bab9f854957d4fd7976c9c6d920c871c051", "result": "valid", "flags": []}, {"tcId": 343, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00e46d4f11b86b5a12f6fe781d1f934ef2b30e78f6f9cc86a9996e20c0021d008351974b965526034a0ccef0e7d3bc13d91798151488c91533143f7b", "result": "valid", "flags": []}, {"tcId": 344, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c305ccf0b5d0cf33dc745bb7c7964c233f6cfd8892a1c1ae9f50b2f3f021c785f6e85f5e652587c6e15d0c45c427278cf65bb1429a57d8826ca39", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c0e4fde0ac8d37536505f7b8bdc2d22c5c334b064ac5ed27bea9c179e021d00c4d6bf829dd547000d6f70b9ad9e9c1503bebcf1d95c2608942ca19d", "result": "valid", "flags": []}, {"tcId": 346, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00818afcaf491da9d08a7cc29318d5e85dce568dcca7018059f44e9b7e021d00bf32a233d5fc6ed8e2d9270b1bdad4bbd2a0f2c293d289bd91ffbcf3", "result": "valid", "flags": []}, {"tcId": 347, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c0e05ed675c673e5e70a4fdd5a47b114c5d542d4f6d7a367597d713ea021c26d70d65c48430373363987810bdcc556e02718eab214403ae008db4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00ab7a19eecf63e9668278963b65236b2768e57cae0e268cb86a0ddda1021d008829f5d3a3394f9467ba62e66ef1768e3e54f93ed23ec962bc443c2e", "result": "valid", "flags": []}, {"tcId": 349, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c17111a77cf79bead456ed86a7d8a935531440281eb8b15a885e341c0021d00fdc3958d04f037b1d4bb2cee307b5201be062e0d4e089df1c1917668", "result": "valid", "flags": []}, {"tcId": 350, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00acafa1e33345eeba0c338c2204b4cd8ba21de7ec3e1213317038e968021c0b42fbbaeda98a35da0de4c79546f3a0f7d9dec275d2cd671f93c874", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00a3fe71a2a56f554e98fd10a8098c2a543c98bc6b3602ef39f2412308021c5d1d68f9a870ef2bc87484b3386549fae95811ab72bc0e3a514720da", "result": "valid", "flags": []}, {"tcId": 352, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c132f7625704756c13f2bfa449e60952f836f4904660b5b1da07e5a9f021d0082b4abafc40e8fd19b0c967f02fff152737ce01153658df445c4d7b7", "result": "valid", "flags": []}, {"tcId": 353, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00f36a8347c6fe0397a1161a364cbc4bdfb4d8b7894cbaa6edc55a4ff7021d009c9c90515da5e602d62e99f48eac414e913dd0b7cbf680c1a5399952", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c2125ecc08e52e9e39e590117de2145bd879626cb87180e52e9d3ce03021d008f7e838d0e8fb80005fe3c72fca1b7cc08ed321a34487896b0c90b04", "result": "valid", "flags": []}, {"tcId": 355, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00e485747ac2f3d045e010cdadab4fd5dbd5556c0008445fb73e07cd90021d00e2133a7906aeac504852e09e6d057f29ab21368cfc4e2394be565e68", "result": "valid", "flags": []}, {"tcId": 356, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00a4de0d931ddab90e667ebc0ad800ce49e971c60543abdc46cefff926021c550816170bd87593b9fb8ad5ed9ab4ddb12403ff6fe032252833bac4", "result": "valid", "flags": []}]}]}