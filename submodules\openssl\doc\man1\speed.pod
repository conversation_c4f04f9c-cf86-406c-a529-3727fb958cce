=pod

=head1 NAME

openssl-speed,
speed - test library performance

=head1 SYNOPSIS

B<openssl speed>
[B<-help>]
[B<-engine id>]
[B<-elapsed>]
[B<-evp algo>]
[B<-decrypt>]
[B<-rand file...>]
[B<-writerand file>]
[B<-primes num>]
[B<-seconds num>]
[B<-bytes num>]
[B<algorithm...>]

=head1 DESCRIPTION

This command is used to test the performance of cryptographic algorithms.
To see the list of supported algorithms, use the I<list --digest-commands>
or I<list --cipher-commands> command. The global CSPRNG is denoted by
the I<rand> algorithm name.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<speed>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=item B<-elapsed>

When calculating operations- or bytes-per-second, use wall-clock time
instead of CPU user time as divisor. It can be useful when testing speed
of hardware engines.

=item B<-evp algo>

Use the specified cipher or message digest algorithm via the EVP interface.
If B<algo> is an AEAD cipher, then you can pass <-aead> to benchmark a
TLS-like sequence. And if B<algo> is a multi-buffer capable cipher, e.g.
aes-128-cbc-hmac-sha1, then B<-mb> will time multi-buffer operation.

=item B<-decrypt>

Time the decryption instead of encryption. Affects only the EVP testing.

=item B<-rand file...>

A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is B<;> for MS-Windows, B<,> for OpenVMS, and B<:> for
all others.

=item [B<-writerand file>]

Writes random data to the specified I<file> upon exit.
This can be used with a subsequent B<-rand> flag.

=item B<-primes num>

Generate a B<num>-prime RSA key and use it to run the benchmarks. This option
is only effective if RSA algorithm is specified to test.

=item B<-seconds num>

Run benchmarks for B<num> seconds.

=item B<-bytes num>

Run benchmarks on B<num>-byte buffers. Affects ciphers, digests and the CSPRNG.

=item B<[zero or more test algorithms]>

If any options are given, B<speed> tests those algorithms, otherwise a
pre-compiled grand selection is tested.

=back

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
