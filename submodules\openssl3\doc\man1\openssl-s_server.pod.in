=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-s_server - SSL/TLS server program

=head1 SYNOPSIS

B<openssl> B<s_server>
[B<-help>]
[B<-port> I<+int>]
[B<-accept> I<val>]
[B<-unix> I<val>]
[B<-4>]
[B<-6>]
[B<-unlink>]
[B<-context> I<val>]
[B<-verify> I<int>]
[B<-Verify> I<int>]
[B<-cert> I<infile>]
[B<-cert2> I<infile>]
[B<-certform> B<DER>|B<PEM>|B<P12>]
[B<-cert_chain> I<infile>]
[B<-build_chain>]
[B<-serverinfo> I<val>]
[B<-key> I<filename>|I<uri>]
[B<-key2> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-pass> I<val>]
[B<-dcert> I<infile>]
[B<-dcertform> B<DER>|B<PEM>|B<P12>]
[B<-dcert_chain> I<infile>]
[B<-dkey> I<filename>|I<uri>]
[B<-dkeyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-dpass> I<val>]
[B<-nbio_test>]
[B<-crlf>]
[B<-debug>]
[B<-msg>]
[B<-msgfile> I<outfile>]
[B<-state>]
[B<-nocert>]
[B<-quiet>]
[B<-no_resume_ephemeral>]
[B<-www>]
[B<-WWW>]
[B<-http_server_binmode>]
[B<-no_ca_names>]
[B<-ignore_unexpected_eof>]
[B<-servername>]
[B<-servername_fatal>]
[B<-tlsextdebug>]
[B<-HTTP>]
[B<-id_prefix> I<val>]
[B<-keymatexport> I<val>]
[B<-keymatexportlen> I<+int>]
[B<-CRL> I<infile>]
[B<-CRLform> B<DER>|B<PEM>]
[B<-crl_download>]
[B<-chainCAfile> I<infile>]
[B<-chainCApath> I<dir>]
[B<-chainCAstore> I<uri>]
[B<-verifyCAfile> I<infile>]
[B<-verifyCApath> I<dir>]
[B<-verifyCAstore> I<uri>]
[B<-no_cache>]
[B<-ext_cache>]
[B<-verify_return_error>]
[B<-verify_quiet>]
[B<-ign_eof>]
[B<-no_ign_eof>]
[B<-no_etm>]
[B<-status>]
[B<-status_verbose>]
[B<-status_timeout> I<int>]
[B<-proxy> I<[http[s]://][userinfo@]host[:port][/path]>]
[B<-no_proxy> I<addresses>]
[B<-status_url> I<val>]
[B<-status_file> I<infile>]
[B<-ssl_config> I<val>]
[B<-trace>]
[B<-security_debug>]
[B<-security_debug_verbose>]
[B<-brief>]
[B<-rev>]
[B<-async>]
[B<-max_send_frag> I<+int>]
[B<-split_send_frag> I<+int>]
[B<-max_pipelines> I<+int>]
[B<-naccept> I<+int>]
[B<-read_buf> I<+int>]
[B<-bugs>]
[B<-no_comp>]
[B<-comp>]
[B<-no_ticket>]
[B<-serverpref>]
[B<-legacy_renegotiation>]
[B<-no_renegotiation>]
[B<-no_resumption_on_reneg>]
[B<-allow_no_dhe_kex>]
[B<-prioritize_chacha>]
[B<-strict>]
[B<-sigalgs> I<val>]
[B<-client_sigalgs> I<val>]
[B<-groups> I<val>]
[B<-curves> I<val>]
[B<-named_curve> I<val>]
[B<-cipher> I<val>]
[B<-ciphersuites> I<val>]
[B<-dhparam> I<infile>]
[B<-record_padding> I<val>]
[B<-debug_broken_protocol>]
[B<-nbio>]
[B<-psk_identity> I<val>]
[B<-psk_hint> I<val>]
[B<-psk> I<val>]
[B<-psk_session> I<file>]
[B<-srpvfile> I<infile>]
[B<-srpuserseed> I<val>]
[B<-timeout>]
[B<-mtu> I<+int>]
[B<-listen>]
[B<-sctp>]
[B<-sctp_label_bug>]
[B<-use_srtp> I<val>]
[B<-no_dhe>]
[B<-nextprotoneg> I<val>]
[B<-alpn> I<val>]
[B<-sendfile>]
[B<-keylogfile> I<outfile>]
[B<-recv_max_early_data> I<int>]
[B<-max_early_data> I<int>]
[B<-early_data>]
[B<-stateless>]
[B<-anti_replay>]
[B<-no_anti_replay>]
[B<-num_tickets>]
{- $OpenSSL::safe::opt_name_synopsis -}
{- $OpenSSL::safe::opt_version_synopsis -}
{- $OpenSSL::safe::opt_v_synopsis -}
{- $OpenSSL::safe::opt_s_synopsis -}
{- $OpenSSL::safe::opt_x_synopsis -}
{- $OpenSSL::safe::opt_trust_synopsis -}
{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command implements a generic SSL/TLS server which
listens for connections on a given port using SSL/TLS.

=head1 OPTIONS

In addition to the options below, this command also supports
the common and server only options documented
L<SSL_CONF_cmd(3)/Supported Command Line Commands>

=over 4

=item B<-help>

Print out a usage message.

=item B<-port> I<+int>

The TCP port to listen on for connections. If not specified 4433 is used.

=item B<-accept> I<val>

The optional TCP host and port to listen on for connections. If not specified, *:4433 is used.

=item B<-unix> I<val>

Unix domain socket to accept on.

=item B<-4>

Use IPv4 only.

=item B<-6>

Use IPv6 only.

=item B<-unlink>

For -unix, unlink any existing socket first.

=item B<-context> I<val>

Sets the SSL context id. It can be given any string value. If this option
is not present a default value will be used.

=item B<-verify> I<int>, B<-Verify> I<int>

The verify depth to use. This specifies the maximum length of the
client certificate chain and makes the server request a certificate from
the client. With the B<-verify> option a certificate is requested but the
client does not have to send one, with the B<-Verify> option the client
must supply a certificate or an error occurs.

If the cipher suite cannot request a client certificate (for example an
anonymous cipher suite or PSK) this option has no effect.

=item B<-cert> I<infile>

The certificate to use, most servers cipher suites require the use of a
certificate and some require a certificate with a certain public key type:
for example the DSS cipher suites require a certificate containing a DSS
(DSA) key. If not specified then the filename F<server.pem> will be used.

=item B<-cert2> I<infile>

The certificate file to use for servername; default is C<server2.pem>.

=item B<-certform> B<DER>|B<PEM>|B<P12>

The server certificate file format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-cert_chain>

A file or URI of untrusted certificates to use when attempting to build the
certificate chain related to the certificate specified via the B<-cert> option.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-build_chain>

Specify whether the application should build the server certificate chain to be
provided to the client.

=item B<-serverinfo> I<val>

A file containing one or more blocks of PEM data.  Each PEM block
must encode a TLS ServerHello extension (2 bytes type, 2 bytes length,
followed by "length" bytes of extension data).  If the client sends
an empty TLS ClientHello extension matching the type, the corresponding
ServerHello extension will be returned.

=item B<-key> I<filename>|I<uri>

The private key to use. If not specified then the certificate file will
be used.

=item B<-key2> I<filename>|I<uri>

The private Key file to use for servername if not given via B<-cert2>.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-pass> I<val>

The private key and certificate file password source.
For more information about the format of I<val>,
see L<openssl-passphrase-options(1)>.

=item B<-dcert> I<infile>, B<-dkey> I<filename>|I<uri>

Specify an additional certificate and private key, these behave in the
same manner as the B<-cert> and B<-key> options except there is no default
if they are not specified (no additional certificate and key is used). As
noted above some cipher suites require a certificate containing a key of
a certain type. Some cipher suites need a certificate carrying an RSA key
and some a DSS (DSA) key. By using RSA and DSS certificates and keys
a server can support clients which only support RSA or DSS cipher suites
by using an appropriate certificate.

=item B<-dcert_chain>

A file or URI of untrusted certificates to use when attempting to build the
server certificate chain when a certificate specified via the B<-dcert> option
is in use.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-dcertform> B<DER>|B<PEM>|B<P12>

The format of the additional certificate file; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-dkeyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The format of the additional private key; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-dpass> I<val>

The passphrase for the additional private key and certificate.
For more information about the format of I<val>,
see L<openssl-passphrase-options(1)>.

=item B<-nbio_test>

Tests non blocking I/O.

=item B<-crlf>

This option translated a line feed from the terminal into CR+LF.

=item B<-debug>

Print extensive debugging information including a hex dump of all traffic.

=item B<-security_debug>

Print output from SSL/TLS security framework.

=item B<-security_debug_verbose>

Print more output from SSL/TLS security framework

=item B<-msg>

Show all protocol messages with hex dump.

=item B<-msgfile> I<outfile>

File to send output of B<-msg> or B<-trace> to, default standard output.

=item B<-state>

Prints the SSL session states.

=item B<-CRL> I<infile>

The CRL file to use.

=item B<-CRLform> B<DER>|B<PEM>

The CRL file format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-crl_download>

Download CRLs from distribution points given in CDP extensions of certificates

=item B<-verifyCAfile> I<filename>

A file in PEM format CA containing trusted certificates to use
for verifying client certificates.

=item B<-verifyCApath> I<dir>

A directory containing trusted certificates to use
for verifying client certificates.
This directory must be in "hash format",
see L<openssl-verify(1)> for more information.

=item B<-verifyCAstore> I<uri>

The URI of a store containing trusted certificates to use
for verifying client certificates.

=item B<-chainCAfile> I<file>

A file in PEM format containing trusted certificates to use
when attempting to build the server certificate chain.

=item B<-chainCApath> I<dir>

A directory containing trusted certificates to use
for building the server certificate chain provided to the client.
This directory must be in "hash format",
see L<openssl-verify(1)> for more information.

=item B<-chainCAstore> I<uri>

The URI of a store containing trusted certificates to use
for building the server certificate chain provided to the client.
The URI may indicate a single certificate, as well as a collection of them.
With URIs in the C<file:> scheme, this acts as B<-chainCAfile> or
B<-chainCApath>, depending on if the URI indicates a directory or a
single file.
See L<ossl_store-file(7)> for more information on the C<file:> scheme.

=item B<-nocert>

If this option is set then no certificate is used. This restricts the
cipher suites available to the anonymous ones (currently just anonymous
DH).

=item B<-quiet>

Inhibit printing of session and certificate information.

=item B<-no_resume_ephemeral>

Disable caching and tickets if ephemeral (EC)DH is used.

=item B<-tlsextdebug>

Print a hex dump of any TLS extensions received from the server.

=item B<-www>

Sends a status message back to the client when it connects. This includes
information about the ciphers used and various session parameters.
The output is in HTML format so this option can be used with a web browser.
The special URL C</renegcert> turns on client cert validation, and C</reneg>
tells the server to request renegotiation.
The B<-early_data> option cannot be used with this option.

=item B<-WWW>, B<-HTTP>

Emulates a simple web server. Pages will be resolved relative to the
current directory, for example if the URL C<https://myhost/page.html> is
requested the file F<./page.html> will be sent.
If the B<-HTTP> flag is used, the files are sent directly, and should contain
any HTTP response headers (including status response line).
If the B<-WWW> option is used,
the response headers are generated by the server, and the file extension is
examined to determine the B<Content-Type> header.
Extensions of C<html>, C<htm>, and C<php> are C<text/html> and all others are
C<text/plain>.
In addition, the special URL C</stats> will return status
information like the B<-www> option.
Neither of these options can be used in conjunction with B<-early_data>.

=item B<-http_server_binmode>

When acting as web-server (using option B<-WWW> or B<-HTTP>) open files requested
by the client in binary mode.

=item B<-no_ca_names>

Disable TLS Extension CA Names. You may want to disable it for security reasons
or for compatibility with some Windows TLS implementations crashing when this
extension is larger than 1024 bytes.

=item B<-ignore_unexpected_eof>

Some TLS implementations do not send the mandatory close_notify alert on
shutdown. If the application tries to wait for the close_notify alert but the
peer closes the connection without sending it, an error is generated. When this
option is enabled the peer does not need to send the close_notify alert and a
closed connection will be treated as if the close_notify alert was received.
For more information on shutting down a connection, see L<SSL_shutdown(3)>.

=item B<-servername>

Servername for HostName TLS extension.

=item B<-servername_fatal>

On servername mismatch send fatal alert (default: warning alert).

=item B<-id_prefix> I<val>

Generate SSL/TLS session IDs prefixed by I<val>. This is mostly useful
for testing any SSL/TLS code (e.g. proxies) that wish to deal with multiple
servers, when each of which might be generating a unique range of session
IDs (e.g. with a certain prefix).

=item B<-keymatexport>

Export keying material using label.

=item B<-keymatexportlen>

Export the given number of bytes of keying material; default 20.

=item B<-no_cache>

Disable session cache.

=item B<-ext_cache>.

Disable internal cache, set up and use external cache.

=item B<-verify_return_error>

Verification errors normally just print a message but allow the
connection to continue, for debugging purposes.
If this option is used, then verification errors close the connection.

=item B<-verify_quiet>

No verify output except verify errors.

=item B<-ign_eof>

Ignore input EOF (default: when B<-quiet>).

=item B<-no_ign_eof>

Do not ignore input EOF.

=item B<-no_etm>

Disable Encrypt-then-MAC negotiation.

=item B<-status>

Enables certificate status request support (aka OCSP stapling).

=item B<-status_verbose>

Enables certificate status request support (aka OCSP stapling) and gives
a verbose printout of the OCSP response.

=item B<-status_timeout> I<int>

Sets the timeout for OCSP response to I<int> seconds.

=item B<-proxy> I<[http[s]://][userinfo@]host[:port][/path]>

The HTTP(S) proxy server to use for reaching the OCSP server unless B<-no_proxy>
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is C<https>; apart from that
the optional C<http://> or C<https://> prefix is ignored,
as well as any userinfo and path components.
Defaults to the environment variable C<http_proxy> if set, else C<HTTP_PROXY>
in case no TLS is used, otherwise C<https_proxy> if set, else C<HTTPS_PROXY>.

=item B<-no_proxy> I<addresses>

List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable C<no_proxy> if set, else C<NO_PROXY>.

=item B<-status_url> I<val>

Sets a fallback responder URL to use if no responder URL is present in the
server certificate. Without this option an error is returned if the server
certificate does not contain a responder address.
The optional userinfo and fragment URL components are ignored.
Any given query component is handled as part of the path component.

=item B<-status_file> I<infile>

Overrides any OCSP responder URLs from the certificate and always provides the
OCSP Response stored in the file. The file must be in DER format.

=item B<-ssl_config> I<val>

Configure SSL_CTX using the given configuration value.

=item B<-trace>

Show verbose trace output of protocol messages.

=item B<-brief>

Provide a brief summary of connection parameters instead of the normal verbose
output.

=item B<-rev>

Simple echo server that sends back received text reversed. Also sets B<-brief>.
Cannot be used in conjunction with B<-early_data>.

=item B<-async>

Switch on asynchronous mode. Cryptographic operations will be performed
asynchronously. This will only have an effect if an asynchronous capable engine
is also used via the B<-engine> option. For test purposes the dummy async engine
(dasync) can be used (if available).

=item B<-max_send_frag> I<+int>

The maximum size of data fragment to send.
See L<SSL_CTX_set_max_send_fragment(3)> for further information.

=item B<-split_send_frag> I<+int>

The size used to split data for encrypt pipelines. If more data is written in
one go than this value then it will be split into multiple pipelines, up to the
maximum number of pipelines defined by max_pipelines. This only has an effect if
a suitable cipher suite has been negotiated, an engine that supports pipelining
has been loaded, and max_pipelines is greater than 1. See
L<SSL_CTX_set_split_send_fragment(3)> for further information.

=item B<-max_pipelines> I<+int>

The maximum number of encrypt/decrypt pipelines to be used. This will only have
an effect if an engine has been loaded that supports pipelining (e.g. the dasync
engine) and a suitable cipher suite has been negotiated. The default value is 1.
See L<SSL_CTX_set_max_pipelines(3)> for further information.

=item B<-naccept> I<+int>

The server will exit after receiving the specified number of connections,
default unlimited.

=item B<-read_buf> I<+int>

The default read buffer size to be used for connections. This will only have an
effect if the buffer size is larger than the size that would otherwise be used
and pipelining is in use (see L<SSL_CTX_set_default_read_buffer_len(3)> for
further information).

=item B<-bugs>

There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.

=item B<-no_comp>

Disable negotiation of TLS compression.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.

=item B<-comp>

Enable negotiation of TLS compression.
This option was introduced in OpenSSL 1.1.0.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.

=item B<-no_ticket>

Disable RFC4507bis session ticket support. This option has no effect if TLSv1.3
is negotiated. See B<-num_tickets>.

=item B<-num_tickets>

Control the number of tickets that will be sent to the client after a full
handshake in TLSv1.3. The default number of tickets is 2. This option does not
affect the number of tickets sent after a resumption handshake.

=item B<-serverpref>

Use the server's cipher preferences, rather than the client's preferences.

=item B<-prioritize_chacha>

Prioritize ChaCha ciphers when preferred by clients. Requires B<-serverpref>.

=item B<-no_resumption_on_reneg>

Set the B<SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION> option.

=item B<-client_sigalgs> I<val>

Signature algorithms to support for client certificate authentication
(colon-separated list).

=item B<-named_curve> I<val>

Specifies the elliptic curve to use. NOTE: this is single curve, not a list.
For a list of all possible curves, use:

    $ openssl ecparam -list_curves

=item B<-cipher> I<val>

This allows the list of TLSv1.2 and below ciphersuites used by the server to be
modified. This list is combined with any TLSv1.3 ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
L<openssl-ciphers(1)> for more information.

=item B<-ciphersuites> I<val>

This allows the list of TLSv1.3 ciphersuites used by the server to be modified.
This list is combined with any TLSv1.2 and below ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
L<openssl-ciphers(1)> command for more information. The format for this list is
a simple colon (":") separated list of TLSv1.3 ciphersuite names.

=item B<-dhparam> I<infile>

The DH parameter file to use. The ephemeral DH cipher suites generate keys
using a set of DH parameters. If not specified then an attempt is made to
load the parameters from the server certificate file.
If this fails then a static set of parameters hard coded into this command
will be used.

=item B<-nbio>

Turns on non blocking I/O.

=item B<-timeout>

Enable timeouts.

=item B<-mtu>

Set link-layer MTU.

=item B<-psk_identity> I<val>

Expect the client to send PSK identity I<val> when using a PSK
cipher suite, and warn if they do not.  By default, the expected PSK
identity is the string "Client_identity".

=item B<-psk_hint> I<val>

Use the PSK identity hint I<val> when using a PSK cipher suite.

=item B<-psk> I<val>

Use the PSK key I<val> when using a PSK cipher suite. The key is
given as a hexadecimal number without leading 0x, for example -psk
1a2b3c4d.
This option must be provided in order to use a PSK cipher.

=item B<-psk_session> I<file>

Use the pem encoded SSL_SESSION data stored in I<file> as the basis of a PSK.
Note that this will only work if TLSv1.3 is negotiated.

=item B<-srpvfile>

The verifier file for SRP.
This option is deprecated.

=item B<-srpuserseed>

A seed string for a default user salt.
This option is deprecated.

=item B<-listen>

This option can only be used in conjunction with one of the DTLS options above.
With this option, this command will listen on a UDP port for incoming
connections.
Any ClientHellos that arrive will be checked to see if they have a cookie in
them or not.
Any without a cookie will be responded to with a HelloVerifyRequest.
If a ClientHello with a cookie is received then this command will
connect to that peer and complete the handshake.

=item B<-sctp>

Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in
conjunction with B<-dtls>, B<-dtls1> or B<-dtls1_2>. This option is only
available where OpenSSL has support for SCTP enabled.

=item B<-sctp_label_bug>

Use the incorrect behaviour of older OpenSSL implementations when computing
endpoint-pair shared secrets for DTLS/SCTP. This allows communication with
older broken implementations but breaks interoperability with correct
implementations. Must be used in conjunction with B<-sctp>. This option is only
available where OpenSSL has support for SCTP enabled.

=item B<-use_srtp>

Offer SRTP key management with a colon-separated profile list.

=item B<-no_dhe>

If this option is set then no DH parameters will be loaded effectively
disabling the ephemeral DH cipher suites.

=item B<-alpn> I<val>, B<-nextprotoneg> I<val>

These flags enable the Application-Layer Protocol Negotiation
or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the
IETF standard and replaces NPN.
The I<val> list is a comma-separated list of supported protocol
names.  The list should contain the most desirable protocols first.
Protocol names are printable ASCII strings, for example "http/1.1" or
"spdy/3".
The flag B<-nextprotoneg> cannot be specified if B<-tls1_3> is used.

=item B<-sendfile>

If this option is set and KTLS is enabled, SSL_sendfile() will be used
instead of BIO_write() to send the HTTP response requested by a client.
This option is only valid if B<-WWW> or B<-HTTP> is specified.

=item B<-keylogfile> I<outfile>

Appends TLS secrets to the specified keylog file such that external programs
(like Wireshark) can decrypt TLS connections.

=item B<-max_early_data> I<int>

Change the default maximum early data bytes that are specified for new sessions
and any incoming early data (when used in conjunction with the B<-early_data>
flag). The default value is approximately 16k. The argument must be an integer
greater than or equal to 0.

=item B<-recv_max_early_data> I<int>

Specify the hard limit on the maximum number of early data bytes that will
be accepted.

=item B<-early_data>

Accept early data where possible. Cannot be used in conjunction with B<-www>,
B<-WWW>, B<-HTTP> or B<-rev>.

=item B<-stateless>

Require TLSv1.3 cookies.

=item B<-anti_replay>, B<-no_anti_replay>

Switches replay protection on or off, respectively. Replay protection is on by
default unless overridden by a configuration file. When it is on, OpenSSL will
automatically detect if a session ticket has been used more than once, TLSv1.3
has been negotiated, and early data is enabled on the server. A full handshake
is forced if a session ticket is used a second or subsequent time. Any early
data that was sent will be rejected.

{- $OpenSSL::safe::opt_name_item -}

{- $OpenSSL::safe::opt_version_item -}

{- $OpenSSL::safe::opt_s_item -}

{- $OpenSSL::safe::opt_x_item -}

{- $OpenSSL::safe::opt_trust_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_v_item -}

If the server requests a client certificate, then
verification errors are displayed, for debugging, but the command will
proceed unless the B<-verify_return_error> option is used.

=back

=head1 CONNECTED COMMANDS

If a connection request is established with an SSL client and neither the
B<-www> nor the B<-WWW> option has been used then normally any data received
from the client is displayed and any key presses will be sent to the client.

Certain commands are also recognized which perform special operations. These
commands are a letter which must appear at the start of a line. They are listed
below.

=over 4

=item B<q>

End the current SSL connection but still accept new connections.

=item B<Q>

End the current SSL connection and exit.

=item B<r>

Renegotiate the SSL session (TLSv1.2 and below only).

=item B<R>

Renegotiate the SSL session and request a client certificate (TLSv1.2 and below
only).

=item B<P>

Send some plain text down the underlying TCP connection: this should
cause the client to disconnect due to a protocol violation.

=item B<S>

Print out some session cache status information.

=item B<k>

Send a key update message to the client (TLSv1.3 only)

=item B<K>

Send a key update message to the client and request one back (TLSv1.3 only)

=item B<c>

Send a certificate request to the client (TLSv1.3 only)

=back

=head1 NOTES

This command can be used to debug SSL clients. To accept connections
from a web browser the command:

 openssl s_server -accept 443 -www

can be used for example.

Although specifying an empty list of CAs when requesting a client certificate
is strictly speaking a protocol violation, some SSL clients interpret this to
mean any CA is acceptable. This is useful for debugging purposes.

The session parameters can printed out using the L<openssl-sess_id(1)> command.

=head1 BUGS

Because this program has a lot of options and also because some of the
techniques used are rather old, the C source for this command is rather
hard to read and not a model of how things should be done.
A typical SSL server program would be much simpler.

The output of common ciphers is wrong: it just gives the list of ciphers that
OpenSSL recognizes and the client supports.

There should be a way for this command to print out details
of any unknown cipher suites a client says it supports.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-sess_id(1)>,
L<openssl-s_client(1)>,
L<openssl-ciphers(1)>,
L<SSL_CONF_cmd(3)>,
L<SSL_CTX_set_max_send_fragment(3)>,
L<SSL_CTX_set_split_send_fragment(3)>,
L<SSL_CTX_set_max_pipelines(3)>,
L<ossl_store-file(7)>

=head1 HISTORY

The -no_alt_chains option was added in OpenSSL 1.1.0.

The
-allow-no-dhe-kex and -prioritize_chacha options were added in OpenSSL 1.1.1.

The B<-srpvfile>, B<-srpuserseed>, and B<-engine>
option were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
