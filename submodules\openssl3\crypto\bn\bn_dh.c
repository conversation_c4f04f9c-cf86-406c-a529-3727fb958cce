/*
 * Copyright 2014-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include "bn_local.h"
#include "internal/nelem.h"

# include <openssl/dh.h>
# include "crypto/bn_dh.h"

# if BN_BITS2 == 64
#  define BN_DEF(lo, hi) (BN_ULONG)hi << 32 | lo
# else
#  define BN_DEF(lo, hi) lo, hi
# endif

/* DH parameters from RFC3526 */

# ifndef FIPS_MODULE
/*
 * "1536-bit MODP Group" from RFC3526, Section 2.
 *
 * The prime is: 2^1536 - 2^1472 - 1 + 2^64 * { [2^1406 pi] + 741804 }
 *
 * RFC3526 specifies a generator of 2.
 * RFC2412 specifies a generator of 22.
 */
static const BN_ULONG modp_1536_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0xCA237327, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_1536_q[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x6511B993, 0x78BA3604),
    BN_DEF(0x255E4C02, 0xB3861AA7), BN_DEF(0xB84B4B36, 0xCF6A9483),
    BN_DEF(0x1042A95D, 0x0E3179AB), BN_DEF(0xEE51D6CB, 0xC1B2AE91),
    BN_DEF(0x7E9267AF, 0x348B1FD4), BN_DEF(0x0E2AE9CD, 0xCC6D241B),
    BN_DEF(0x50B1DF82, 0xE1003E5C), BN_DEF(0xF6722D9E, 0x24943328),
    BN_DEF(0xBE258FF3, 0xD74F9208), BN_DEF(0xAD44CFD2, 0xF71C35FD),
    BN_DEF(0x7A035BF6, 0x85FFAE5B), BN_DEF(0xD31BF6B5, 0x7A262174),
    BN_DEF(0x312F3F63, 0xF242DABB), BN_DEF(0xB6A8E122, 0xA7F09AB6),
    BN_DEF(0xF92F8A1B, 0x98158536), BN_DEF(0xE69D218D, 0xF7CA8CD9),
    BN_DEF(0xC71A026E, 0x28A5043C), BN_DEF(0x1D89CD91, 0x0105DF53),
    BN_DEF(0x4533E63A, 0x94812704), BN_DEF(0xC06E0E68, 0x62633145),
    BN_DEF(0x10B4611A, 0xE487ED51), BN_DEF(0xFFFFFFFF, 0x7FFFFFFF)
};
# endif /* FIPS_MODULE */

/*-
 * "2048-bit MODP Group" from RFC3526, Section 3.
 *
 * The prime is: 2^2048 - 2^1984 - 1 + 2^64 * { [2^1918 pi] + 124476 }
 *
 * RFC3526 specifies a generator of 2.
 */
static const BN_ULONG modp_2048_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x8AACAA68, 0x15728E5A),
    BN_DEF(0x98FA0510, 0x15D22618), BN_DEF(0xEA956AE5, 0x3995497C),
    BN_DEF(0x95581718, 0xDE2BCBF6), BN_DEF(0x6F4C52C9, 0xB5C55DF0),
    BN_DEF(0xEC07A28F, 0x9B2783A2), BN_DEF(0x180E8603, 0xE39E772C),
    BN_DEF(0x2E36CE3B, 0x32905E46), BN_DEF(0xCA18217C, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_2048_q[] = {
    BN_DEF(0xFFFFFFFF,  0x7FFFFFFF), BN_DEF(0x45565534,  0x0AB9472D),
    BN_DEF(0x4C7D0288,  0x8AE9130C), BN_DEF(0x754AB572,  0x1CCAA4BE),
    BN_DEF(0x4AAC0B8C,  0xEF15E5FB), BN_DEF(0x37A62964,  0xDAE2AEF8),
    BN_DEF(0x7603D147,  0xCD93C1D1), BN_DEF(0x0C074301,  0xF1CF3B96),
    BN_DEF(0x171B671D,  0x19482F23), BN_DEF(0x650C10BE,  0x78BA3604),
    BN_DEF(0x255E4C02,  0xB3861AA7), BN_DEF(0xB84B4B36,  0xCF6A9483),
    BN_DEF(0x1042A95D,  0x0E3179AB), BN_DEF(0xEE51D6CB,  0xC1B2AE91),
    BN_DEF(0x7E9267AF,  0x348B1FD4), BN_DEF(0x0E2AE9CD,  0xCC6D241B),
    BN_DEF(0x50B1DF82,  0xE1003E5C), BN_DEF(0xF6722D9E,  0x24943328),
    BN_DEF(0xBE258FF3,  0xD74F9208), BN_DEF(0xAD44CFD2,  0xF71C35FD),
    BN_DEF(0x7A035BF6,  0x85FFAE5B), BN_DEF(0xD31BF6B5,  0x7A262174),
    BN_DEF(0x312F3F63,  0xF242DABB), BN_DEF(0xB6A8E122,  0xA7F09AB6),
    BN_DEF(0xF92F8A1B,  0x98158536), BN_DEF(0xE69D218D,  0xF7CA8CD9),
    BN_DEF(0xC71A026E,  0x28A5043C), BN_DEF(0x1D89CD91,  0x0105DF53),
    BN_DEF(0x4533E63A,  0x94812704), BN_DEF(0xC06E0E68,  0x62633145),
    BN_DEF(0x10B4611A,  0xE487ED51), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/*-
 * "3072-bit MODP Group" from RFC3526, Section 4.
 *
 * The prime is: 2^3072 - 2^3008 - 1 + 2^64 * { [2^2942 pi] + 1690314 }
 *
 * RFC3526 specifies a generator of 2.
 */
static const BN_ULONG modp_3072_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0xA93AD2CA, 0x4B82D120),
    BN_DEF(0xE0FD108E, 0x43DB5BFC), BN_DEF(0x74E5AB31, 0x08E24FA0),
    BN_DEF(0xBAD946E2, 0x770988C0), BN_DEF(0x7A615D6C, 0xBBE11757),
    BN_DEF(0x177B200C, 0x521F2B18), BN_DEF(0x3EC86A64, 0xD8760273),
    BN_DEF(0xD98A0864, 0xF12FFA06), BN_DEF(0x1AD2EE6B, 0xCEE3D226),
    BN_DEF(0x4A25619D, 0x1E8C94E0), BN_DEF(0xDB0933D7, 0xABF5AE8C),
    BN_DEF(0xA6E1E4C7, 0xB3970F85), BN_DEF(0x5D060C7D, 0x8AEA7157),
    BN_DEF(0x58DBEF0A, 0xECFB8504), BN_DEF(0xDF1CBA64, 0xA85521AB),
    BN_DEF(0x04507A33, 0xAD33170D), BN_DEF(0x8AAAC42D, 0x15728E5A),
    BN_DEF(0x98FA0510, 0x15D22618), BN_DEF(0xEA956AE5, 0x3995497C),
    BN_DEF(0x95581718, 0xDE2BCBF6), BN_DEF(0x6F4C52C9, 0xB5C55DF0),
    BN_DEF(0xEC07A28F, 0x9B2783A2), BN_DEF(0x180E8603, 0xE39E772C),
    BN_DEF(0x2E36CE3B, 0x32905E46), BN_DEF(0xCA18217C, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_3072_q[] = {
    BN_DEF(0xFFFFFFFF,  0x7FFFFFFF), BN_DEF(0x549D6965,  0x25C16890),
    BN_DEF(0x707E8847,  0xA1EDADFE), BN_DEF(0x3A72D598,  0x047127D0),
    BN_DEF(0x5D6CA371,  0x3B84C460), BN_DEF(0xBD30AEB6,  0x5DF08BAB),
    BN_DEF(0x0BBD9006,  0x290F958C), BN_DEF(0x9F643532,  0x6C3B0139),
    BN_DEF(0x6CC50432,  0xF897FD03), BN_DEF(0x0D697735,  0xE771E913),
    BN_DEF(0x2512B0CE,  0x8F464A70), BN_DEF(0x6D8499EB,  0xD5FAD746),
    BN_DEF(0xD370F263,  0xD9CB87C2), BN_DEF(0xAE83063E,  0x457538AB),
    BN_DEF(0x2C6DF785,  0x767DC282), BN_DEF(0xEF8E5D32,  0xD42A90D5),
    BN_DEF(0x82283D19,  0xD6998B86), BN_DEF(0x45556216,  0x0AB9472D),
    BN_DEF(0x4C7D0288,  0x8AE9130C), BN_DEF(0x754AB572,  0x1CCAA4BE),
    BN_DEF(0x4AAC0B8C,  0xEF15E5FB), BN_DEF(0x37A62964,  0xDAE2AEF8),
    BN_DEF(0x7603D147,  0xCD93C1D1), BN_DEF(0x0C074301,  0xF1CF3B96),
    BN_DEF(0x171B671D,  0x19482F23), BN_DEF(0x650C10BE,  0x78BA3604),
    BN_DEF(0x255E4C02,  0xB3861AA7), BN_DEF(0xB84B4B36,  0xCF6A9483),
    BN_DEF(0x1042A95D,  0x0E3179AB), BN_DEF(0xEE51D6CB,  0xC1B2AE91),
    BN_DEF(0x7E9267AF,  0x348B1FD4), BN_DEF(0x0E2AE9CD,  0xCC6D241B),
    BN_DEF(0x50B1DF82,  0xE1003E5C), BN_DEF(0xF6722D9E,  0x24943328),
    BN_DEF(0xBE258FF3,  0xD74F9208), BN_DEF(0xAD44CFD2,  0xF71C35FD),
    BN_DEF(0x7A035BF6,  0x85FFAE5B), BN_DEF(0xD31BF6B5,  0x7A262174),
    BN_DEF(0x312F3F63,  0xF242DABB), BN_DEF(0xB6A8E122,  0xA7F09AB6),
    BN_DEF(0xF92F8A1B,  0x98158536), BN_DEF(0xE69D218D,  0xF7CA8CD9),
    BN_DEF(0xC71A026E,  0x28A5043C), BN_DEF(0x1D89CD91,  0x0105DF53),
    BN_DEF(0x4533E63A,  0x94812704), BN_DEF(0xC06E0E68,  0x62633145),
    BN_DEF(0x10B4611A,  0xE487ED51), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/*-
 * "4096-bit MODP Group" from RFC3526, Section 5.
 *
 * The prime is: 2^4096 - 2^4032 - 1 + 2^64 * { [2^3966 pi] + 240904 }
 *
 * RFC3526 specifies a generator of 2.
 */
static const BN_ULONG modp_4096_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x34063199, 0x4DF435C9),
    BN_DEF(0x90A6C08F, 0x86FFB7DC), BN_DEF(0x8D8FDDC1, 0x93B4EA98),
    BN_DEF(0xD5B05AA9, 0xD0069127), BN_DEF(0x2170481C, 0xB81BDD76),
    BN_DEF(0xCEE2D7AF, 0x1F612970), BN_DEF(0x515BE7ED, 0x233BA186),
    BN_DEF(0xA090C3A2, 0x99B2964F), BN_DEF(0x4E6BC05D, 0x287C5947),
    BN_DEF(0x1FBECAA6, 0x2E8EFC14), BN_DEF(0x04DE8EF9, 0xDBBBC2DB),
    BN_DEF(0x2AD44CE8, 0x2583E9CA), BN_DEF(0xB6150BDA, 0x1A946834),
    BN_DEF(0x6AF4E23C, 0x99C32718), BN_DEF(0xBDBA5B26, 0x88719A10),
    BN_DEF(0xA787E6D7, 0x1A723C12), BN_DEF(0xA9210801, 0x4B82D120),
    BN_DEF(0xE0FD108E, 0x43DB5BFC), BN_DEF(0x74E5AB31, 0x08E24FA0),
    BN_DEF(0xBAD946E2, 0x770988C0), BN_DEF(0x7A615D6C, 0xBBE11757),
    BN_DEF(0x177B200C, 0x521F2B18), BN_DEF(0x3EC86A64, 0xD8760273),
    BN_DEF(0xD98A0864, 0xF12FFA06), BN_DEF(0x1AD2EE6B, 0xCEE3D226),
    BN_DEF(0x4A25619D, 0x1E8C94E0), BN_DEF(0xDB0933D7, 0xABF5AE8C),
    BN_DEF(0xA6E1E4C7, 0xB3970F85), BN_DEF(0x5D060C7D, 0x8AEA7157),
    BN_DEF(0x58DBEF0A, 0xECFB8504), BN_DEF(0xDF1CBA64, 0xA85521AB),
    BN_DEF(0x04507A33, 0xAD33170D), BN_DEF(0x8AAAC42D, 0x15728E5A),
    BN_DEF(0x98FA0510, 0x15D22618), BN_DEF(0xEA956AE5, 0x3995497C),
    BN_DEF(0x95581718, 0xDE2BCBF6), BN_DEF(0x6F4C52C9, 0xB5C55DF0),
    BN_DEF(0xEC07A28F, 0x9B2783A2), BN_DEF(0x180E8603, 0xE39E772C),
    BN_DEF(0x2E36CE3B, 0x32905E46), BN_DEF(0xCA18217C, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_4096_q[] = {
    BN_DEF(0xFFFFFFFF,  0xFFFFFFFF), BN_DEF(0x9A0318CC,  0xA6FA1AE4),
    BN_DEF(0x48536047,  0xC37FDBEE), BN_DEF(0x46C7EEE0,  0xC9DA754C),
    BN_DEF(0xEAD82D54,  0x68034893), BN_DEF(0x10B8240E,  0xDC0DEEBB),
    BN_DEF(0x67716BD7,  0x8FB094B8), BN_DEF(0x28ADF3F6,  0x119DD0C3),
    BN_DEF(0xD04861D1,  0xCCD94B27), BN_DEF(0xA735E02E,  0x143E2CA3),
    BN_DEF(0x0FDF6553,  0x97477E0A), BN_DEF(0x826F477C,  0x6DDDE16D),
    BN_DEF(0x156A2674,  0x12C1F4E5), BN_DEF(0x5B0A85ED,  0x0D4A341A),
    BN_DEF(0x357A711E,  0x4CE1938C), BN_DEF(0x5EDD2D93,  0xC438CD08),
    BN_DEF(0x53C3F36B,  0x8D391E09), BN_DEF(0x54908400,  0x25C16890),
    BN_DEF(0x707E8847,  0xA1EDADFE), BN_DEF(0x3A72D598,  0x047127D0),
    BN_DEF(0x5D6CA371,  0x3B84C460), BN_DEF(0xBD30AEB6,  0x5DF08BAB),
    BN_DEF(0x0BBD9006,  0x290F958C), BN_DEF(0x9F643532,  0x6C3B0139),
    BN_DEF(0x6CC50432,  0xF897FD03), BN_DEF(0x0D697735,  0xE771E913),
    BN_DEF(0x2512B0CE,  0x8F464A70), BN_DEF(0x6D8499EB,  0xD5FAD746),
    BN_DEF(0xD370F263,  0xD9CB87C2), BN_DEF(0xAE83063E,  0x457538AB),
    BN_DEF(0x2C6DF785,  0x767DC282), BN_DEF(0xEF8E5D32,  0xD42A90D5),
    BN_DEF(0x82283D19,  0xD6998B86), BN_DEF(0x45556216,  0x0AB9472D),
    BN_DEF(0x4C7D0288,  0x8AE9130C), BN_DEF(0x754AB572,  0x1CCAA4BE),
    BN_DEF(0x4AAC0B8C,  0xEF15E5FB), BN_DEF(0x37A62964,  0xDAE2AEF8),
    BN_DEF(0x7603D147,  0xCD93C1D1), BN_DEF(0x0C074301,  0xF1CF3B96),
    BN_DEF(0x171B671D,  0x19482F23), BN_DEF(0x650C10BE,  0x78BA3604),
    BN_DEF(0x255E4C02,  0xB3861AA7), BN_DEF(0xB84B4B36,  0xCF6A9483),
    BN_DEF(0x1042A95D,  0x0E3179AB), BN_DEF(0xEE51D6CB,  0xC1B2AE91),
    BN_DEF(0x7E9267AF,  0x348B1FD4), BN_DEF(0x0E2AE9CD,  0xCC6D241B),
    BN_DEF(0x50B1DF82,  0xE1003E5C), BN_DEF(0xF6722D9E,  0x24943328),
    BN_DEF(0xBE258FF3,  0xD74F9208), BN_DEF(0xAD44CFD2,  0xF71C35FD),
    BN_DEF(0x7A035BF6,  0x85FFAE5B), BN_DEF(0xD31BF6B5,  0x7A262174),
    BN_DEF(0x312F3F63,  0xF242DABB), BN_DEF(0xB6A8E122,  0xA7F09AB6),
    BN_DEF(0xF92F8A1B,  0x98158536), BN_DEF(0xE69D218D,  0xF7CA8CD9),
    BN_DEF(0xC71A026E,  0x28A5043C), BN_DEF(0x1D89CD91,  0x0105DF53),
    BN_DEF(0x4533E63A,  0x94812704), BN_DEF(0xC06E0E68,  0x62633145),
    BN_DEF(0x10B4611A,  0xE487ED51), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/*-
 * "6144-bit MODP Group" from RFC3526, Section 6.
 *
 * The prime is: 2^6144 - 2^6080 - 1 + 2^64 * { [2^6014 pi] + 929484 }
 *
 * RFC3526 specifies a generator of 2.
 */
static const BN_ULONG modp_6144_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x6DCC4024, 0xE694F91E),
    BN_DEF(0x0B7474D6, 0x12BF2D5B), BN_DEF(0x3F4860EE, 0x043E8F66),
    BN_DEF(0x6E3C0468, 0x387FE8D7), BN_DEF(0x2EF29632, 0xDA56C9EC),
    BN_DEF(0xA313D55C, 0xEB19CCB1), BN_DEF(0x8A1FBFF0, 0xF550AA3D),
    BN_DEF(0xB7C5DA76, 0x06A1D58B), BN_DEF(0xF29BE328, 0xA79715EE),
    BN_DEF(0x0F8037E0, 0x14CC5ED2), BN_DEF(0xBF48E1D8, 0xCC8F6D7E),
    BN_DEF(0x2B4154AA, 0x4BD407B2), BN_DEF(0xFF585AC5, 0x0F1D45B7),
    BN_DEF(0x36CC88BE, 0x23A97A7E), BN_DEF(0xBEC7E8F3, 0x59E7C97F),
    BN_DEF(0x900B1C9E, 0xB5A84031), BN_DEF(0x46980C82, 0xD55E702F),
    BN_DEF(0x6E74FEF6, 0xF482D7CE), BN_DEF(0xD1721D03, 0xF032EA15),
    BN_DEF(0xC64B92EC, 0x5983CA01), BN_DEF(0x378CD2BF, 0x6FB8F401),
    BN_DEF(0x2BD7AF42, 0x33205151), BN_DEF(0xE6CC254B, 0xDB7F1447),
    BN_DEF(0xCED4BB1B, 0x44CE6CBA), BN_DEF(0xCF9B14ED, 0xDA3EDBEB),
    BN_DEF(0x865A8918, 0x179727B0), BN_DEF(0x9027D831, 0xB06A53ED),
    BN_DEF(0x413001AE, 0xE5DB382F), BN_DEF(0xAD9E530E, 0xF8FF9406),
    BN_DEF(0x3DBA37BD, 0xC9751E76), BN_DEF(0x602646DE, 0xC1D4DCB2),
    BN_DEF(0xD27C7026, 0x36C3FAB4), BN_DEF(0x34028492, 0x4DF435C9),
    BN_DEF(0x90A6C08F, 0x86FFB7DC), BN_DEF(0x8D8FDDC1, 0x93B4EA98),
    BN_DEF(0xD5B05AA9, 0xD0069127), BN_DEF(0x2170481C, 0xB81BDD76),
    BN_DEF(0xCEE2D7AF, 0x1F612970), BN_DEF(0x515BE7ED, 0x233BA186),
    BN_DEF(0xA090C3A2, 0x99B2964F), BN_DEF(0x4E6BC05D, 0x287C5947),
    BN_DEF(0x1FBECAA6, 0x2E8EFC14), BN_DEF(0x04DE8EF9, 0xDBBBC2DB),
    BN_DEF(0x2AD44CE8, 0x2583E9CA), BN_DEF(0xB6150BDA, 0x1A946834),
    BN_DEF(0x6AF4E23C, 0x99C32718), BN_DEF(0xBDBA5B26, 0x88719A10),
    BN_DEF(0xA787E6D7, 0x1A723C12), BN_DEF(0xA9210801, 0x4B82D120),
    BN_DEF(0xE0FD108E, 0x43DB5BFC), BN_DEF(0x74E5AB31, 0x08E24FA0),
    BN_DEF(0xBAD946E2, 0x770988C0), BN_DEF(0x7A615D6C, 0xBBE11757),
    BN_DEF(0x177B200C, 0x521F2B18), BN_DEF(0x3EC86A64, 0xD8760273),
    BN_DEF(0xD98A0864, 0xF12FFA06), BN_DEF(0x1AD2EE6B, 0xCEE3D226),
    BN_DEF(0x4A25619D, 0x1E8C94E0), BN_DEF(0xDB0933D7, 0xABF5AE8C),
    BN_DEF(0xA6E1E4C7, 0xB3970F85), BN_DEF(0x5D060C7D, 0x8AEA7157),
    BN_DEF(0x58DBEF0A, 0xECFB8504), BN_DEF(0xDF1CBA64, 0xA85521AB),
    BN_DEF(0x04507A33, 0xAD33170D), BN_DEF(0x8AAAC42D, 0x15728E5A),
    BN_DEF(0x98FA0510, 0x15D22618), BN_DEF(0xEA956AE5, 0x3995497C),
    BN_DEF(0x95581718, 0xDE2BCBF6), BN_DEF(0x6F4C52C9, 0xB5C55DF0),
    BN_DEF(0xEC07A28F, 0x9B2783A2), BN_DEF(0x180E8603, 0xE39E772C),
    BN_DEF(0x2E36CE3B, 0x32905E46), BN_DEF(0xCA18217C, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_6144_q[] = {
    BN_DEF(0xFFFFFFFF,  0x7FFFFFFF), BN_DEF(0x36E62012,  0x734A7C8F),
    BN_DEF(0x85BA3A6B,  0x095F96AD), BN_DEF(0x1FA43077,  0x021F47B3),
    BN_DEF(0xB71E0234,  0x1C3FF46B), BN_DEF(0x17794B19,  0x6D2B64F6),
    BN_DEF(0xD189EAAE,  0x758CE658), BN_DEF(0xC50FDFF8,  0x7AA8551E),
    BN_DEF(0xDBE2ED3B,  0x0350EAC5), BN_DEF(0x794DF194,  0x53CB8AF7),
    BN_DEF(0x07C01BF0,  0x0A662F69), BN_DEF(0x5FA470EC,  0x6647B6BF),
    BN_DEF(0x15A0AA55,  0xA5EA03D9), BN_DEF(0xFFAC2D62,  0x078EA2DB),
    BN_DEF(0x1B66445F,  0x91D4BD3F), BN_DEF(0xDF63F479,  0x2CF3E4BF),
    BN_DEF(0xC8058E4F,  0x5AD42018), BN_DEF(0xA34C0641,  0x6AAF3817),
    BN_DEF(0x373A7F7B,  0xFA416BE7), BN_DEF(0xE8B90E81,  0x7819750A),
    BN_DEF(0xE325C976,  0xACC1E500), BN_DEF(0x9BC6695F,  0x37DC7A00),
    BN_DEF(0x95EBD7A1,  0x999028A8), BN_DEF(0xF36612A5,  0xEDBF8A23),
    BN_DEF(0x676A5D8D,  0xA267365D), BN_DEF(0xE7CD8A76,  0x6D1F6DF5),
    BN_DEF(0x432D448C,  0x8BCB93D8), BN_DEF(0xC813EC18,  0x583529F6),
    BN_DEF(0xA09800D7,  0x72ED9C17), BN_DEF(0x56CF2987,  0xFC7FCA03),
    BN_DEF(0x1EDD1BDE,  0x64BA8F3B), BN_DEF(0x3013236F,  0x60EA6E59),
    BN_DEF(0x693E3813,  0x1B61FD5A), BN_DEF(0x9A014249,  0xA6FA1AE4),
    BN_DEF(0x48536047,  0xC37FDBEE), BN_DEF(0x46C7EEE0,  0xC9DA754C),
    BN_DEF(0xEAD82D54,  0x68034893), BN_DEF(0x10B8240E,  0xDC0DEEBB),
    BN_DEF(0x67716BD7,  0x8FB094B8), BN_DEF(0x28ADF3F6,  0x119DD0C3),
    BN_DEF(0xD04861D1,  0xCCD94B27), BN_DEF(0xA735E02E,  0x143E2CA3),
    BN_DEF(0x0FDF6553,  0x97477E0A), BN_DEF(0x826F477C,  0x6DDDE16D),
    BN_DEF(0x156A2674,  0x12C1F4E5), BN_DEF(0x5B0A85ED,  0x0D4A341A),
    BN_DEF(0x357A711E,  0x4CE1938C), BN_DEF(0x5EDD2D93,  0xC438CD08),
    BN_DEF(0x53C3F36B,  0x8D391E09), BN_DEF(0x54908400,  0x25C16890),
    BN_DEF(0x707E8847,  0xA1EDADFE), BN_DEF(0x3A72D598,  0x047127D0),
    BN_DEF(0x5D6CA371,  0x3B84C460), BN_DEF(0xBD30AEB6,  0x5DF08BAB),
    BN_DEF(0x0BBD9006,  0x290F958C), BN_DEF(0x9F643532,  0x6C3B0139),
    BN_DEF(0x6CC50432,  0xF897FD03), BN_DEF(0x0D697735,  0xE771E913),
    BN_DEF(0x2512B0CE,  0x8F464A70), BN_DEF(0x6D8499EB,  0xD5FAD746),
    BN_DEF(0xD370F263,  0xD9CB87C2), BN_DEF(0xAE83063E,  0x457538AB),
    BN_DEF(0x2C6DF785,  0x767DC282), BN_DEF(0xEF8E5D32,  0xD42A90D5),
    BN_DEF(0x82283D19,  0xD6998B86), BN_DEF(0x45556216,  0x0AB9472D),
    BN_DEF(0x4C7D0288,  0x8AE9130C), BN_DEF(0x754AB572,  0x1CCAA4BE),
    BN_DEF(0x4AAC0B8C,  0xEF15E5FB), BN_DEF(0x37A62964,  0xDAE2AEF8),
    BN_DEF(0x7603D147,  0xCD93C1D1), BN_DEF(0x0C074301,  0xF1CF3B96),
    BN_DEF(0x171B671D,  0x19482F23), BN_DEF(0x650C10BE,  0x78BA3604),
    BN_DEF(0x255E4C02,  0xB3861AA7), BN_DEF(0xB84B4B36,  0xCF6A9483),
    BN_DEF(0x1042A95D,  0x0E3179AB), BN_DEF(0xEE51D6CB,  0xC1B2AE91),
    BN_DEF(0x7E9267AF,  0x348B1FD4), BN_DEF(0x0E2AE9CD,  0xCC6D241B),
    BN_DEF(0x50B1DF82,  0xE1003E5C), BN_DEF(0xF6722D9E,  0x24943328),
    BN_DEF(0xBE258FF3,  0xD74F9208), BN_DEF(0xAD44CFD2,  0xF71C35FD),
    BN_DEF(0x7A035BF6,  0x85FFAE5B), BN_DEF(0xD31BF6B5,  0x7A262174),
    BN_DEF(0x312F3F63,  0xF242DABB), BN_DEF(0xB6A8E122,  0xA7F09AB6),
    BN_DEF(0xF92F8A1B,  0x98158536), BN_DEF(0xE69D218D,  0xF7CA8CD9),
    BN_DEF(0xC71A026E,  0x28A5043C), BN_DEF(0x1D89CD91,  0x0105DF53),
    BN_DEF(0x4533E63A,  0x94812704), BN_DEF(0xC06E0E68,  0x62633145),
    BN_DEF(0x10B4611A,  0xE487ED51), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/*
 * "8192-bit MODP Group" from RFC3526, Section 7.
 *
 * The prime is: 2^8192 - 2^8128 - 1 + 2^64 * { [2^8062 pi] + 4743158 }
 *
 * RFC3526 specifies a generator of 2.
 */
static const BN_ULONG modp_8192_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x98EDD3DF, 0x60C980DD),
    BN_DEF(0x80B96E71, 0xC81F56E8), BN_DEF(0x765694DF, 0x9E3050E2),
    BN_DEF(0x5677E9AA, 0x9558E447), BN_DEF(0xFC026E47, 0xC9190DA6),
    BN_DEF(0xD5EE382B, 0x889A002E), BN_DEF(0x481C6CD7, 0x4009438B),
    BN_DEF(0xEB879F92, 0x359046F4), BN_DEF(0x1ECFA268, 0xFAF36BC3),
    BN_DEF(0x7EE74D73, 0xB1D510BD), BN_DEF(0x5DED7EA1, 0xF9AB4819),
    BN_DEF(0x0846851D, 0x64F31CC5), BN_DEF(0xA0255DC1, 0x4597E899),
    BN_DEF(0x74AB6A36, 0xDF310EE0), BN_DEF(0x3F44F82D, 0x6D2A13F8),
    BN_DEF(0xB3A278A6, 0x062B3CF5), BN_DEF(0xED5BDD3A, 0x79683303),
    BN_DEF(0xA2C087E8, 0xFA9D4B7F), BN_DEF(0x2F8385DD, 0x4BCBC886),
    BN_DEF(0x6CEA306B, 0x3473FC64), BN_DEF(0x1A23F0C7, 0x13EB57A8),
    BN_DEF(0xA4037C07, 0x22222E04), BN_DEF(0xFC848AD9, 0xE3FDB8BE),
    BN_DEF(0xE39D652D, 0x238F16CB), BN_DEF(0x2BF1C978, 0x3423B474),
    BN_DEF(0x5AE4F568, 0x3AAB639C), BN_DEF(0x6BA42466, 0x2576F693),
    BN_DEF(0x8AFC47ED, 0x741FA7BF), BN_DEF(0x8D9DD300, 0x3BC832B6),
    BN_DEF(0x73B931BA, 0xD8BEC4D0), BN_DEF(0xA932DF8C, 0x38777CB6),
    BN_DEF(0x12FEE5E4, 0x74A3926F), BN_DEF(0x6DBE1159, 0xE694F91E),
    BN_DEF(0x0B7474D6, 0x12BF2D5B), BN_DEF(0x3F4860EE, 0x043E8F66),
    BN_DEF(0x6E3C0468, 0x387FE8D7), BN_DEF(0x2EF29632, 0xDA56C9EC),
    BN_DEF(0xA313D55C, 0xEB19CCB1), BN_DEF(0x8A1FBFF0, 0xF550AA3D),
    BN_DEF(0xB7C5DA76, 0x06A1D58B), BN_DEF(0xF29BE328, 0xA79715EE),
    BN_DEF(0x0F8037E0, 0x14CC5ED2), BN_DEF(0xBF48E1D8, 0xCC8F6D7E),
    BN_DEF(0x2B4154AA, 0x4BD407B2), BN_DEF(0xFF585AC5, 0x0F1D45B7),
    BN_DEF(0x36CC88BE, 0x23A97A7E), BN_DEF(0xBEC7E8F3, 0x59E7C97F),
    BN_DEF(0x900B1C9E, 0xB5A84031), BN_DEF(0x46980C82, 0xD55E702F),
    BN_DEF(0x6E74FEF6, 0xF482D7CE), BN_DEF(0xD1721D03, 0xF032EA15),
    BN_DEF(0xC64B92EC, 0x5983CA01), BN_DEF(0x378CD2BF, 0x6FB8F401),
    BN_DEF(0x2BD7AF42, 0x33205151), BN_DEF(0xE6CC254B, 0xDB7F1447),
    BN_DEF(0xCED4BB1B, 0x44CE6CBA), BN_DEF(0xCF9B14ED, 0xDA3EDBEB),
    BN_DEF(0x865A8918, 0x179727B0), BN_DEF(0x9027D831, 0xB06A53ED),
    BN_DEF(0x413001AE, 0xE5DB382F), BN_DEF(0xAD9E530E, 0xF8FF9406),
    BN_DEF(0x3DBA37BD, 0xC9751E76), BN_DEF(0x602646DE, 0xC1D4DCB2),
    BN_DEF(0xD27C7026, 0x36C3FAB4), BN_DEF(0x34028492, 0x4DF435C9),
    BN_DEF(0x90A6C08F, 0x86FFB7DC), BN_DEF(0x8D8FDDC1, 0x93B4EA98),
    BN_DEF(0xD5B05AA9, 0xD0069127), BN_DEF(0x2170481C, 0xB81BDD76),
    BN_DEF(0xCEE2D7AF, 0x1F612970), BN_DEF(0x515BE7ED, 0x233BA186),
    BN_DEF(0xA090C3A2, 0x99B2964F), BN_DEF(0x4E6BC05D, 0x287C5947),
    BN_DEF(0x1FBECAA6, 0x2E8EFC14), BN_DEF(0x04DE8EF9, 0xDBBBC2DB),
    BN_DEF(0x2AD44CE8, 0x2583E9CA), BN_DEF(0xB6150BDA, 0x1A946834),
    BN_DEF(0x6AF4E23C, 0x99C32718), BN_DEF(0xBDBA5B26, 0x88719A10),
    BN_DEF(0xA787E6D7, 0x1A723C12), BN_DEF(0xA9210801, 0x4B82D120),
    BN_DEF(0xE0FD108E, 0x43DB5BFC), BN_DEF(0x74E5AB31, 0x08E24FA0),
    BN_DEF(0xBAD946E2, 0x770988C0), BN_DEF(0x7A615D6C, 0xBBE11757),
    BN_DEF(0x177B200C, 0x521F2B18), BN_DEF(0x3EC86A64, 0xD8760273),
    BN_DEF(0xD98A0864, 0xF12FFA06), BN_DEF(0x1AD2EE6B, 0xCEE3D226),
    BN_DEF(0x4A25619D, 0x1E8C94E0), BN_DEF(0xDB0933D7, 0xABF5AE8C),
    BN_DEF(0xA6E1E4C7, 0xB3970F85), BN_DEF(0x5D060C7D, 0x8AEA7157),
    BN_DEF(0x58DBEF0A, 0xECFB8504), BN_DEF(0xDF1CBA64, 0xA85521AB),
    BN_DEF(0x04507A33, 0xAD33170D), BN_DEF(0x8AAAC42D, 0x15728E5A),
    BN_DEF(0x98FA0510, 0x15D22618), BN_DEF(0xEA956AE5, 0x3995497C),
    BN_DEF(0x95581718, 0xDE2BCBF6), BN_DEF(0x6F4C52C9, 0xB5C55DF0),
    BN_DEF(0xEC07A28F, 0x9B2783A2), BN_DEF(0x180E8603, 0xE39E772C),
    BN_DEF(0x2E36CE3B, 0x32905E46), BN_DEF(0xCA18217C, 0xF1746C08),
    BN_DEF(0x4ABC9804, 0x670C354E), BN_DEF(0x7096966D, 0x9ED52907),
    BN_DEF(0x208552BB, 0x1C62F356), BN_DEF(0xDCA3AD96, 0x83655D23),
    BN_DEF(0xFD24CF5F, 0x69163FA8), BN_DEF(0x1C55D39A, 0x98DA4836),
    BN_DEF(0xA163BF05, 0xC2007CB8), BN_DEF(0xECE45B3D, 0x49286651),
    BN_DEF(0x7C4B1FE6, 0xAE9F2411), BN_DEF(0x5A899FA5, 0xEE386BFB),
    BN_DEF(0xF406B7ED, 0x0BFF5CB6), BN_DEF(0xA637ED6B, 0xF44C42E9),
    BN_DEF(0x625E7EC6, 0xE485B576), BN_DEF(0x6D51C245, 0x4FE1356D),
    BN_DEF(0xF25F1437, 0x302B0A6D), BN_DEF(0xCD3A431B, 0xEF9519B3),
    BN_DEF(0x8E3404DD, 0x514A0879), BN_DEF(0x3B139B22, 0x020BBEA6),
    BN_DEF(0x8A67CC74, 0x29024E08), BN_DEF(0x80DC1CD1, 0xC4C6628B),
    BN_DEF(0x2168C234, 0xC90FDAA2), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG modp_8192_q[] = {
    BN_DEF(0xFFFFFFFF,  0xFFFFFFFF), BN_DEF(0xCC76E9EF,  0xB064C06E),
    BN_DEF(0x405CB738,  0xE40FAB74), BN_DEF(0x3B2B4A6F,  0x4F182871),
    BN_DEF(0xAB3BF4D5,  0xCAAC7223), BN_DEF(0x7E013723,  0xE48C86D3),
    BN_DEF(0x6AF71C15,  0xC44D0017), BN_DEF(0xA40E366B,  0x2004A1C5),
    BN_DEF(0x75C3CFC9,  0x1AC8237A), BN_DEF(0x8F67D134,  0xFD79B5E1),
    BN_DEF(0xBF73A6B9,  0xD8EA885E), BN_DEF(0xAEF6BF50,  0xFCD5A40C),
    BN_DEF(0x8423428E,  0xB2798E62), BN_DEF(0xD012AEE0,  0x22CBF44C),
    BN_DEF(0x3A55B51B,  0xEF988770), BN_DEF(0x1FA27C16,  0x369509FC),
    BN_DEF(0xD9D13C53,  0x03159E7A), BN_DEF(0xF6ADEE9D,  0x3CB41981),
    BN_DEF(0xD16043F4,  0xFD4EA5BF), BN_DEF(0x17C1C2EE,  0xA5E5E443),
    BN_DEF(0x36751835,  0x9A39FE32), BN_DEF(0x0D11F863,  0x89F5ABD4),
    BN_DEF(0x5201BE03,  0x91111702), BN_DEF(0x7E42456C,  0xF1FEDC5F),
    BN_DEF(0xF1CEB296,  0x11C78B65), BN_DEF(0x15F8E4BC,  0x1A11DA3A),
    BN_DEF(0x2D727AB4,  0x1D55B1CE), BN_DEF(0xB5D21233,  0x92BB7B49),
    BN_DEF(0xC57E23F6,  0x3A0FD3DF), BN_DEF(0x46CEE980,  0x1DE4195B),
    BN_DEF(0x39DC98DD,  0x6C5F6268), BN_DEF(0x54996FC6,  0x1C3BBE5B),
    BN_DEF(0x897F72F2,  0xBA51C937), BN_DEF(0x36DF08AC,  0x734A7C8F),
    BN_DEF(0x85BA3A6B,  0x095F96AD), BN_DEF(0x1FA43077,  0x021F47B3),
    BN_DEF(0xB71E0234,  0x1C3FF46B), BN_DEF(0x17794B19,  0x6D2B64F6),
    BN_DEF(0xD189EAAE,  0x758CE658), BN_DEF(0xC50FDFF8,  0x7AA8551E),
    BN_DEF(0xDBE2ED3B,  0x0350EAC5), BN_DEF(0x794DF194,  0x53CB8AF7),
    BN_DEF(0x07C01BF0,  0x0A662F69), BN_DEF(0x5FA470EC,  0x6647B6BF),
    BN_DEF(0x15A0AA55,  0xA5EA03D9), BN_DEF(0xFFAC2D62,  0x078EA2DB),
    BN_DEF(0x1B66445F,  0x91D4BD3F), BN_DEF(0xDF63F479,  0x2CF3E4BF),
    BN_DEF(0xC8058E4F,  0x5AD42018), BN_DEF(0xA34C0641,  0x6AAF3817),
    BN_DEF(0x373A7F7B,  0xFA416BE7), BN_DEF(0xE8B90E81,  0x7819750A),
    BN_DEF(0xE325C976,  0xACC1E500), BN_DEF(0x9BC6695F,  0x37DC7A00),
    BN_DEF(0x95EBD7A1,  0x999028A8), BN_DEF(0xF36612A5,  0xEDBF8A23),
    BN_DEF(0x676A5D8D,  0xA267365D), BN_DEF(0xE7CD8A76,  0x6D1F6DF5),
    BN_DEF(0x432D448C,  0x8BCB93D8), BN_DEF(0xC813EC18,  0x583529F6),
    BN_DEF(0xA09800D7,  0x72ED9C17), BN_DEF(0x56CF2987,  0xFC7FCA03),
    BN_DEF(0x1EDD1BDE,  0x64BA8F3B), BN_DEF(0x3013236F,  0x60EA6E59),
    BN_DEF(0x693E3813,  0x1B61FD5A), BN_DEF(0x9A014249,  0xA6FA1AE4),
    BN_DEF(0x48536047,  0xC37FDBEE), BN_DEF(0x46C7EEE0,  0xC9DA754C),
    BN_DEF(0xEAD82D54,  0x68034893), BN_DEF(0x10B8240E,  0xDC0DEEBB),
    BN_DEF(0x67716BD7,  0x8FB094B8), BN_DEF(0x28ADF3F6,  0x119DD0C3),
    BN_DEF(0xD04861D1,  0xCCD94B27), BN_DEF(0xA735E02E,  0x143E2CA3),
    BN_DEF(0x0FDF6553,  0x97477E0A), BN_DEF(0x826F477C,  0x6DDDE16D),
    BN_DEF(0x156A2674,  0x12C1F4E5), BN_DEF(0x5B0A85ED,  0x0D4A341A),
    BN_DEF(0x357A711E,  0x4CE1938C), BN_DEF(0x5EDD2D93,  0xC438CD08),
    BN_DEF(0x53C3F36B,  0x8D391E09), BN_DEF(0x54908400,  0x25C16890),
    BN_DEF(0x707E8847,  0xA1EDADFE), BN_DEF(0x3A72D598,  0x047127D0),
    BN_DEF(0x5D6CA371,  0x3B84C460), BN_DEF(0xBD30AEB6,  0x5DF08BAB),
    BN_DEF(0x0BBD9006,  0x290F958C), BN_DEF(0x9F643532,  0x6C3B0139),
    BN_DEF(0x6CC50432,  0xF897FD03), BN_DEF(0x0D697735,  0xE771E913),
    BN_DEF(0x2512B0CE,  0x8F464A70), BN_DEF(0x6D8499EB,  0xD5FAD746),
    BN_DEF(0xD370F263,  0xD9CB87C2), BN_DEF(0xAE83063E,  0x457538AB),
    BN_DEF(0x2C6DF785,  0x767DC282), BN_DEF(0xEF8E5D32,  0xD42A90D5),
    BN_DEF(0x82283D19,  0xD6998B86), BN_DEF(0x45556216,  0x0AB9472D),
    BN_DEF(0x4C7D0288,  0x8AE9130C), BN_DEF(0x754AB572,  0x1CCAA4BE),
    BN_DEF(0x4AAC0B8C,  0xEF15E5FB), BN_DEF(0x37A62964,  0xDAE2AEF8),
    BN_DEF(0x7603D147,  0xCD93C1D1), BN_DEF(0x0C074301,  0xF1CF3B96),
    BN_DEF(0x171B671D,  0x19482F23), BN_DEF(0x650C10BE,  0x78BA3604),
    BN_DEF(0x255E4C02,  0xB3861AA7), BN_DEF(0xB84B4B36,  0xCF6A9483),
    BN_DEF(0x1042A95D,  0x0E3179AB), BN_DEF(0xEE51D6CB,  0xC1B2AE91),
    BN_DEF(0x7E9267AF,  0x348B1FD4), BN_DEF(0x0E2AE9CD,  0xCC6D241B),
    BN_DEF(0x50B1DF82,  0xE1003E5C), BN_DEF(0xF6722D9E,  0x24943328),
    BN_DEF(0xBE258FF3,  0xD74F9208), BN_DEF(0xAD44CFD2,  0xF71C35FD),
    BN_DEF(0x7A035BF6,  0x85FFAE5B), BN_DEF(0xD31BF6B5,  0x7A262174),
    BN_DEF(0x312F3F63,  0xF242DABB), BN_DEF(0xB6A8E122,  0xA7F09AB6),
    BN_DEF(0xF92F8A1B,  0x98158536), BN_DEF(0xE69D218D,  0xF7CA8CD9),
    BN_DEF(0xC71A026E,  0x28A5043C), BN_DEF(0x1D89CD91,  0x0105DF53),
    BN_DEF(0x4533E63A,  0x94812704), BN_DEF(0xC06E0E68,  0x62633145),
    BN_DEF(0x10B4611A,  0xE487ED51), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/* DH parameters from RFC5114 */
static const BN_ULONG dh1024_160_p[] = {
    BN_DEF(0x2E4A4371, 0xDF1FB2BC), BN_DEF(0x6D4DA708, 0xE68CFDA7),
    BN_DEF(0x365C1A65, 0x45BF37DF), BN_DEF(0x0DC8B4BD, 0xA151AF5F),
    BN_DEF(0xF55BCCC0, 0xFAA31A4F), BN_DEF(0xE5644738, 0x4EFFD6FA),
    BN_DEF(0x219A7372, 0x98488E9C), BN_DEF(0x90C4BD70, 0xACCBDD7D),
    BN_DEF(0xD49B83BF, 0x24975C3C), BN_DEF(0xA9061123, 0x13ECB4AE),
    BN_DEF(0x2EE652C0, 0x9838EF1E), BN_DEF(0x75A23D18, 0x6073E286),
    BN_DEF(0x52D23B61, 0x9A6A9DCA), BN_DEF(0xFB06A3C6, 0x52C99FBC),
    BN_DEF(0xAE5D54EC, 0xDE92DE5E), BN_DEF(0xA080E01D, 0xB10B8F96)
};
static const BN_ULONG dh1024_160_q[] = {
    BN_DEF(0x49462353, 0x64B7CB9D), BN_DEF(0x8ABA4E7D, 0x81A8DF27),
    (BN_ULONG)0xF518AA87
};
static const BN_ULONG dh1024_160_g[] = {
    BN_DEF(0x22B3B2E5, 0x855E6EEB), BN_DEF(0xF97C2A24, 0x858F4DCE),
    BN_DEF(0x18D08BC8, 0x2D779D59), BN_DEF(0x8E73AFA3, 0xD662A4D1),
    BN_DEF(0x69B6A28A, 0x1DBF0A01), BN_DEF(0x7A091F53, 0xA6A24C08),
    BN_DEF(0x63F80A76, 0x909D0D22), BN_DEF(0xB9A92EE1, 0xD7FBD7D3),
    BN_DEF(0x9E2749F4, 0x5E91547F), BN_DEF(0xB01B886A, 0x160217B4),
    BN_DEF(0x5504F213, 0x777E690F), BN_DEF(0x5C41564B, 0x266FEA1E),
    BN_DEF(0x14266D31, 0xD6406CFF), BN_DEF(0x58AC507F, 0xF8104DD2),
    BN_DEF(0xEFB99905, 0x6765A442), BN_DEF(0xC3FD3412, 0xA4D1CBD5)
};

static const BN_ULONG dh2048_224_p[] = {
    BN_DEF(0x0C10E64F, 0x0AC4DFFE), BN_DEF(0x4E71B81C, 0xCF9DE538),
    BN_DEF(0xFFA31F71, 0x7EF363E2), BN_DEF(0x6B8E75B9, 0xE3FB73C1),
    BN_DEF(0x4BA80A29, 0xC9B53DCF), BN_DEF(0x16E79763, 0x23F10B0E),
    BN_DEF(0x13042E9B, 0xC52172E4), BN_DEF(0xC928B2B9, 0xBE60E69C),
    BN_DEF(0xB9E587E8, 0x80CD86A1), BN_DEF(0x98C641A4, 0x315D75E1),
    BN_DEF(0x44328387, 0xCDF93ACC), BN_DEF(0xDC0A486D, 0x15987D9A),
    BN_DEF(0x1FD5A074, 0x7310F712), BN_DEF(0xDE31EFDC, 0x278273C7),
    BN_DEF(0x415D9330, 0x1602E714), BN_DEF(0xBC8985DB, 0x81286130),
    BN_DEF(0x70918836, 0xB3BF8A31), BN_DEF(0xB9C49708, 0x6A00E0A0),
    BN_DEF(0x8BBC27BE, 0xC6BA0B2C), BN_DEF(0xED34DBF6, 0xC9F98D11),
    BN_DEF(0xB6C12207, 0x7AD5B7D0), BN_DEF(0x55B7394B, 0xD91E8FEF),
    BN_DEF(0xEFDA4DF8, 0x9037C9ED), BN_DEF(0xAD6AC212, 0x6D3F8152),
    BN_DEF(0x1274A0A6, 0x1DE6B85A), BN_DEF(0x309C180E, 0xEB3D688A),
    BN_DEF(0x7BA1DF15, 0xAF9A3C40), BN_DEF(0xF95A56DB, 0xE6FA141D),
    BN_DEF(0xB61D0A75, 0xB54B1597), BN_DEF(0x683B9FD1, 0xA20D64E5),
    BN_DEF(0x9559C51F, 0xD660FAA7), BN_DEF(0x9123A9D0, 0xAD107E1E)
};
static const BN_ULONG dh2048_224_q[] = {
    BN_DEF(0xB36371EB, 0xBF389A99), BN_DEF(0x4738CEBC, 0x1F80535A),
    BN_DEF(0x99717710, 0xC58D93FE), (BN_ULONG)0x801C0D34
};
static const BN_ULONG dh2048_224_g[] = {
    BN_DEF(0x191F2BFA, 0x84B890D3), BN_DEF(0x2A7065B3, 0x81BC087F),
    BN_DEF(0xF6EC0179, 0x19C418E1), BN_DEF(0x71CFFF4C, 0x7B5A0F1C),
    BN_DEF(0x9B6AA4BD, 0xEDFE72FE), BN_DEF(0x94B30269, 0x81E1BCFE),
    BN_DEF(0x8D6C0191, 0x566AFBB4), BN_DEF(0x409D13CD, 0xB539CCE3),
    BN_DEF(0x5F2FF381, 0x6AA21E7F), BN_DEF(0x770589EF, 0xD9E263E4),
    BN_DEF(0xD19963DD, 0x10E183ED), BN_DEF(0x150B8EEB, 0xB70A8137),
    BN_DEF(0x28C8F8AC, 0x051AE3D4), BN_DEF(0x0C1AB15B, 0xBB77A86F),
    BN_DEF(0x16A330EF, 0x6E3025E3), BN_DEF(0xD6F83456, 0x19529A45),
    BN_DEF(0x118E98D1, 0xF180EB34), BN_DEF(0x50717CBE, 0xB5F6C6B2),
    BN_DEF(0xDA7460CD, 0x09939D54), BN_DEF(0x22EA1ED4, 0xE2471504),
    BN_DEF(0x521BC98A, 0xB8A762D0), BN_DEF(0x5AC1348B, 0xF4D02727),
    BN_DEF(0x1999024A, 0xC1766910), BN_DEF(0xA8D66AD7, 0xBE5E9001),
    BN_DEF(0x620A8652, 0xC57DB17C), BN_DEF(0x00C29F52, 0xAB739D77),
    BN_DEF(0xA70C4AFA, 0xDD921F01), BN_DEF(0x10B9A6F0, 0xA6824A4E),
    BN_DEF(0xCFE4FFE3, 0x74866A08), BN_DEF(0x89998CAF, 0x6CDEBE7B),
    BN_DEF(0x8FFDAC50, 0x9DF30B5C), BN_DEF(0x4F2D9AE3, 0xAC4032EF)
};

static const BN_ULONG dh2048_256_p[] = {
    BN_DEF(0x1E1A1597, 0xDB094AE9), BN_DEF(0xD7EF09CA, 0x693877FA),
    BN_DEF(0x6E11715F, 0x6116D227), BN_DEF(0xC198AF12, 0xA4B54330),
    BN_DEF(0xD7014103, 0x75F26375), BN_DEF(0x54E710C3, 0xC3A3960A),
    BN_DEF(0xBD0BE621, 0xDED4010A), BN_DEF(0x89962856, 0xC0B857F6),
    BN_DEF(0x71506026, 0xB3CA3F79), BN_DEF(0xE6B486F6, 0x1CCACB83),
    BN_DEF(0x14056425, 0x67E144E5), BN_DEF(0xA41825D9, 0xF6A167B5),
    BN_DEF(0x96524D8E, 0x3AD83477), BN_DEF(0x51BFA4AB, 0xF13C6D9A),
    BN_DEF(0x35488A0E, 0x2D525267), BN_DEF(0xCAA6B790, 0xB63ACAE1),
    BN_DEF(0x81B23F76, 0x4FDB70C5), BN_DEF(0x12307F5C, 0xBC39A0BF),
    BN_DEF(0xB1E59BB8, 0xB941F54E), BN_DEF(0xD45F9088, 0x6C5BFC11),
    BN_DEF(0x4275BF7B, 0x22E0B1EF), BN_DEF(0x5B4758C0, 0x91F9E672),
    BN_DEF(0x6BCF67ED, 0x5A8A9D30), BN_DEF(0x97517ABD, 0x209E0C64),
    BN_DEF(0x830E9A7C, 0x3BF4296D), BN_DEF(0x34096FAA, 0x16C3D911),
    BN_DEF(0x61B2AA30, 0xFAF7DF45), BN_DEF(0xD61957D4, 0xE00DF8F1),
    BN_DEF(0x435E3B00, 0x5D2CEED4), BN_DEF(0x660DD0F2, 0x8CEEF608),
    BN_DEF(0x65195999, 0xFFBBD19C), BN_DEF(0xB4B6663C, 0x87A8E61D)
};
static const BN_ULONG dh2048_256_q[] = {
    BN_DEF(0x64F5FBD3, 0xA308B0FE), BN_DEF(0x1EB3750B, 0x99B1A47D),
    BN_DEF(0x40129DA2, 0xB4479976), BN_DEF(0xA709A097, 0x8CF83642)
};
static const BN_ULONG dh2048_256_g[] = {
    BN_DEF(0x6CC41659, 0x664B4C0F), BN_DEF(0xEF98C582, 0x5E2327CF),
    BN_DEF(0xD4795451, 0xD647D148), BN_DEF(0x90F00EF8, 0x2F630784),
    BN_DEF(0x1DB246C3, 0x184B523D), BN_DEF(0xCDC67EB6, 0xC7891428),
    BN_DEF(0x0DF92B52, 0x7FD02837), BN_DEF(0x64E0EC37, 0xB3353BBB),
    BN_DEF(0x57CD0915, 0xECD06E15), BN_DEF(0xDF016199, 0xB7D2BBD2),
    BN_DEF(0x052588B9, 0xC8484B1E), BN_DEF(0x13D3FE14, 0xDB2A3B73),
    BN_DEF(0xD182EA0A, 0xD052B985), BN_DEF(0xE83B9C80, 0xA4BD1BFF),
    BN_DEF(0xFB3F2E55, 0xDFC967C1), BN_DEF(0x767164E1, 0xB5045AF2),
    BN_DEF(0x6F2F9193, 0x1D14348F), BN_DEF(0x428EBC83, 0x64E67982),
    BN_DEF(0x82D6ED38, 0x8AC376D2), BN_DEF(0xAAB8A862, 0x777DE62A),
    BN_DEF(0xE9EC144B, 0xDDF463E5), BN_DEF(0xC77A57F2, 0x0196F931),
    BN_DEF(0x41000A65, 0xA55AE313), BN_DEF(0xC28CBB18, 0x901228F8),
    BN_DEF(0x7E8C6F62, 0xBC3773BF), BN_DEF(0x0C6B47B1, 0xBE3A6C1B),
    BN_DEF(0xAC0BB555, 0xFF4FED4A), BN_DEF(0x77BE463F, 0x10DBC150),
    BN_DEF(0x1A0BA125, 0x07F4793A), BN_DEF(0x21EF2054, 0x4CA7B18F),
    BN_DEF(0x60EDBD48, 0x2E775066), BN_DEF(0x73134D0B, 0x3FB32C9B)
};

/* Primes from RFC 7919 */
static const BN_ULONG ffdhe2048_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x61285C97, 0x886B4238),
    BN_DEF(0xC1B2EFFA, 0xC6F34A26), BN_DEF(0x7D1683B2, 0xC58EF183),
    BN_DEF(0x2EC22005, 0x3BB5FCBC), BN_DEF(0x4C6FAD73, 0xC3FE3B1B),
    BN_DEF(0xEEF28183, 0x8E4F1232), BN_DEF(0xE98583FF, 0x9172FE9C),
    BN_DEF(0x28342F61, 0xC03404CD), BN_DEF(0xCDF7E2EC, 0x9E02FCE1),
    BN_DEF(0xEE0A6D70, 0x0B07A7C8), BN_DEF(0x6372BB19, 0xAE56EDE7),
    BN_DEF(0xDE394DF4, 0x1D4F42A3), BN_DEF(0x60D7F468, 0xB96ADAB7),
    BN_DEF(0xB2C8E3FB, 0xD108A94B), BN_DEF(0xB324FB61, 0xBC0AB182),
    BN_DEF(0x483A797A, 0x30ACCA4F), BN_DEF(0x36ADE735, 0x1DF158A1),
    BN_DEF(0xF3EFE872, 0xE2A689DA), BN_DEF(0xE0E68B77, 0x984F0C70),
    BN_DEF(0x7F57C935, 0xB557135E), BN_DEF(0x3DED1AF3, 0x85636555),
    BN_DEF(0x5F066ED0, 0x2433F51F), BN_DEF(0xD5FD6561, 0xD3DF1ED5),
    BN_DEF(0xAEC4617A, 0xF681B202), BN_DEF(0x630C75D8, 0x7D2FE363),
    BN_DEF(0x249B3EF9, 0xCC939DCE), BN_DEF(0x146433FB, 0xA9E13641),
    BN_DEF(0xCE2D3695, 0xD8B9C583), BN_DEF(0x273D3CF1, 0xAFDC5620),
    BN_DEF(0xA2BB4A9A, 0xADF85458), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG ffdhe2048_q[] = {
    BN_DEF(0xFFFFFFFF,  0xFFFFFFFF), BN_DEF(0x30942E4B,  0x4435A11C),
    BN_DEF(0x60D977FD,  0x6379A513), BN_DEF(0xBE8B41D9,  0xE2C778C1),
    BN_DEF(0x17611002,  0x9DDAFE5E), BN_DEF(0xA637D6B9,  0xE1FF1D8D),
    BN_DEF(0x777940C1,  0xC7278919), BN_DEF(0x74C2C1FF,  0xC8B97F4E),
    BN_DEF(0x941A17B0,  0x601A0266), BN_DEF(0xE6FBF176,  0x4F017E70),
    BN_DEF(0x770536B8,  0x8583D3E4), BN_DEF(0xB1B95D8C,  0x572B76F3),
    BN_DEF(0xEF1CA6FA,  0x0EA7A151), BN_DEF(0xB06BFA34,  0xDCB56D5B),
    BN_DEF(0xD96471FD,  0xE88454A5), BN_DEF(0x59927DB0,  0x5E0558C1),
    BN_DEF(0xA41D3CBD,  0x98566527), BN_DEF(0x9B56F39A,  0x0EF8AC50),
    BN_DEF(0x79F7F439,  0xF15344ED), BN_DEF(0x707345BB,  0xCC278638),
    BN_DEF(0x3FABE49A,  0xDAAB89AF), BN_DEF(0x9EF68D79,  0x42B1B2AA),
    BN_DEF(0xAF833768,  0x9219FA8F), BN_DEF(0xEAFEB2B0,  0x69EF8F6A),
    BN_DEF(0x576230BD,  0x7B40D901), BN_DEF(0xB1863AEC,  0xBE97F1B1),
    BN_DEF(0x124D9F7C,  0xE649CEE7), BN_DEF(0x8A3219FD,  0xD4F09B20),
    BN_DEF(0xE7169B4A,  0xEC5CE2C1), BN_DEF(0x139E9E78,  0x57EE2B10),
    BN_DEF(0x515DA54D,  0xD6FC2A2C), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

static const BN_ULONG ffdhe3072_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x66C62E37, 0x25E41D2B),
    BN_DEF(0x3FD59D7C, 0x3C1B20EE), BN_DEF(0xFA53DDEF, 0x0ABCD06B),
    BN_DEF(0xD5C4484E, 0x1DBF9A42), BN_DEF(0x9B0DEADA, 0xABC52197),
    BN_DEF(0x22363A0D, 0xE86D2BC5), BN_DEF(0x9C9DF69E, 0x5CAE82AB),
    BN_DEF(0x71F54BFF, 0x64F2E21E), BN_DEF(0xE2D74DD3, 0xF4FD4452),
    BN_DEF(0xBC437944, 0xB4130C93), BN_DEF(0x85139270, 0xAEFE1309),
    BN_DEF(0xC186D91C, 0x598CB0FA), BN_DEF(0x91F7F7EE, 0x7AD91D26),
    BN_DEF(0xD6E6C907, 0x61B46FC9), BN_DEF(0xF99C0238, 0xBC34F4DE),
    BN_DEF(0x6519035B, 0xDE355B3B), BN_DEF(0x611FCFDC, 0x886B4238),
    BN_DEF(0xC1B2EFFA, 0xC6F34A26), BN_DEF(0x7D1683B2, 0xC58EF183),
    BN_DEF(0x2EC22005, 0x3BB5FCBC), BN_DEF(0x4C6FAD73, 0xC3FE3B1B),
    BN_DEF(0xEEF28183, 0x8E4F1232), BN_DEF(0xE98583FF, 0x9172FE9C),
    BN_DEF(0x28342F61, 0xC03404CD), BN_DEF(0xCDF7E2EC, 0x9E02FCE1),
    BN_DEF(0xEE0A6D70, 0x0B07A7C8), BN_DEF(0x6372BB19, 0xAE56EDE7),
    BN_DEF(0xDE394DF4, 0x1D4F42A3), BN_DEF(0x60D7F468, 0xB96ADAB7),
    BN_DEF(0xB2C8E3FB, 0xD108A94B), BN_DEF(0xB324FB61, 0xBC0AB182),
    BN_DEF(0x483A797A, 0x30ACCA4F), BN_DEF(0x36ADE735, 0x1DF158A1),
    BN_DEF(0xF3EFE872, 0xE2A689DA), BN_DEF(0xE0E68B77, 0x984F0C70),
    BN_DEF(0x7F57C935, 0xB557135E), BN_DEF(0x3DED1AF3, 0x85636555),
    BN_DEF(0x5F066ED0, 0x2433F51F), BN_DEF(0xD5FD6561, 0xD3DF1ED5),
    BN_DEF(0xAEC4617A, 0xF681B202), BN_DEF(0x630C75D8, 0x7D2FE363),
    BN_DEF(0x249B3EF9, 0xCC939DCE), BN_DEF(0x146433FB, 0xA9E13641),
    BN_DEF(0xCE2D3695, 0xD8B9C583), BN_DEF(0x273D3CF1, 0xAFDC5620),
    BN_DEF(0xA2BB4A9A, 0xADF85458), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG ffdhe3072_q[] = {
    BN_DEF(0xFFFFFFFF,  0xFFFFFFFF), BN_DEF(0xB363171B,  0x12F20E95),
    BN_DEF(0x1FEACEBE,  0x9E0D9077), BN_DEF(0xFD29EEF7,  0x055E6835),
    BN_DEF(0x6AE22427,  0x0EDFCD21), BN_DEF(0xCD86F56D,  0xD5E290CB),
    BN_DEF(0x911B1D06,  0x743695E2), BN_DEF(0xCE4EFB4F,  0xAE574155),
    BN_DEF(0x38FAA5FF,  0xB279710F), BN_DEF(0x716BA6E9,  0x7A7EA229),
    BN_DEF(0xDE21BCA2,  0x5A098649), BN_DEF(0xC289C938,  0x577F0984),
    BN_DEF(0x60C36C8E,  0x2CC6587D), BN_DEF(0x48FBFBF7,  0xBD6C8E93),
    BN_DEF(0xEB736483,  0x30DA37E4), BN_DEF(0x7CCE011C,  0xDE1A7A6F),
    BN_DEF(0xB28C81AD,  0x6F1AAD9D), BN_DEF(0x308FE7EE,  0x4435A11C),
    BN_DEF(0x60D977FD,  0x6379A513), BN_DEF(0xBE8B41D9,  0xE2C778C1),
    BN_DEF(0x17611002,  0x9DDAFE5E), BN_DEF(0xA637D6B9,  0xE1FF1D8D),
    BN_DEF(0x777940C1,  0xC7278919), BN_DEF(0x74C2C1FF,  0xC8B97F4E),
    BN_DEF(0x941A17B0,  0x601A0266), BN_DEF(0xE6FBF176,  0x4F017E70),
    BN_DEF(0x770536B8,  0x8583D3E4), BN_DEF(0xB1B95D8C,  0x572B76F3),
    BN_DEF(0xEF1CA6FA,  0x0EA7A151), BN_DEF(0xB06BFA34,  0xDCB56D5B),
    BN_DEF(0xD96471FD,  0xE88454A5), BN_DEF(0x59927DB0,  0x5E0558C1),
    BN_DEF(0xA41D3CBD,  0x98566527), BN_DEF(0x9B56F39A,  0x0EF8AC50),
    BN_DEF(0x79F7F439,  0xF15344ED), BN_DEF(0x707345BB,  0xCC278638),
    BN_DEF(0x3FABE49A,  0xDAAB89AF), BN_DEF(0x9EF68D79,  0x42B1B2AA),
    BN_DEF(0xAF833768,  0x9219FA8F), BN_DEF(0xEAFEB2B0,  0x69EF8F6A),
    BN_DEF(0x576230BD,  0x7B40D901), BN_DEF(0xB1863AEC,  0xBE97F1B1),
    BN_DEF(0x124D9F7C,  0xE649CEE7), BN_DEF(0x8A3219FD,  0xD4F09B20),
    BN_DEF(0xE7169B4A,  0xEC5CE2C1), BN_DEF(0x139E9E78,  0x57EE2B10),
    BN_DEF(0x515DA54D,  0xD6FC2A2C), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

static const BN_ULONG ffdhe4096_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0x5E655F6A, 0xC68A007E),
    BN_DEF(0xF44182E1, 0x4DB5A851), BN_DEF(0x7F88A46B, 0x8EC9B55A),
    BN_DEF(0xCEC97DCF, 0x0A8291CD), BN_DEF(0xF98D0ACC, 0x2A4ECEA9),
    BN_DEF(0x7140003C, 0x1A1DB93D), BN_DEF(0x33CB8B7A, 0x092999A3),
    BN_DEF(0x71AD0038, 0x6DC778F9), BN_DEF(0x918130C4, 0xA907600A),
    BN_DEF(0x2D9E6832, 0xED6A1E01), BN_DEF(0xEFB4318A, 0x7135C886),
    BN_DEF(0x7E31CC7A, 0x87F55BA5), BN_DEF(0x55034004, 0x7763CF1D),
    BN_DEF(0xD69F6D18, 0xAC7D5F42), BN_DEF(0xE58857B6, 0x7930E9E4),
    BN_DEF(0x164DF4FB, 0x6E6F52C3), BN_DEF(0x669E1EF1, 0x25E41D2B),
    BN_DEF(0x3FD59D7C, 0x3C1B20EE), BN_DEF(0xFA53DDEF, 0x0ABCD06B),
    BN_DEF(0xD5C4484E, 0x1DBF9A42), BN_DEF(0x9B0DEADA, 0xABC52197),
    BN_DEF(0x22363A0D, 0xE86D2BC5), BN_DEF(0x9C9DF69E, 0x5CAE82AB),
    BN_DEF(0x71F54BFF, 0x64F2E21E), BN_DEF(0xE2D74DD3, 0xF4FD4452),
    BN_DEF(0xBC437944, 0xB4130C93), BN_DEF(0x85139270, 0xAEFE1309),
    BN_DEF(0xC186D91C, 0x598CB0FA), BN_DEF(0x91F7F7EE, 0x7AD91D26),
    BN_DEF(0xD6E6C907, 0x61B46FC9), BN_DEF(0xF99C0238, 0xBC34F4DE),
    BN_DEF(0x6519035B, 0xDE355B3B), BN_DEF(0x611FCFDC, 0x886B4238),
    BN_DEF(0xC1B2EFFA, 0xC6F34A26), BN_DEF(0x7D1683B2, 0xC58EF183),
    BN_DEF(0x2EC22005, 0x3BB5FCBC), BN_DEF(0x4C6FAD73, 0xC3FE3B1B),
    BN_DEF(0xEEF28183, 0x8E4F1232), BN_DEF(0xE98583FF, 0x9172FE9C),
    BN_DEF(0x28342F61, 0xC03404CD), BN_DEF(0xCDF7E2EC, 0x9E02FCE1),
    BN_DEF(0xEE0A6D70, 0x0B07A7C8), BN_DEF(0x6372BB19, 0xAE56EDE7),
    BN_DEF(0xDE394DF4, 0x1D4F42A3), BN_DEF(0x60D7F468, 0xB96ADAB7),
    BN_DEF(0xB2C8E3FB, 0xD108A94B), BN_DEF(0xB324FB61, 0xBC0AB182),
    BN_DEF(0x483A797A, 0x30ACCA4F), BN_DEF(0x36ADE735, 0x1DF158A1),
    BN_DEF(0xF3EFE872, 0xE2A689DA), BN_DEF(0xE0E68B77, 0x984F0C70),
    BN_DEF(0x7F57C935, 0xB557135E), BN_DEF(0x3DED1AF3, 0x85636555),
    BN_DEF(0x5F066ED0, 0x2433F51F), BN_DEF(0xD5FD6561, 0xD3DF1ED5),
    BN_DEF(0xAEC4617A, 0xF681B202), BN_DEF(0x630C75D8, 0x7D2FE363),
    BN_DEF(0x249B3EF9, 0xCC939DCE), BN_DEF(0x146433FB, 0xA9E13641),
    BN_DEF(0xCE2D3695, 0xD8B9C583), BN_DEF(0x273D3CF1, 0xAFDC5620),
    BN_DEF(0xA2BB4A9A, 0xADF85458), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG ffdhe4096_q[] = {
    BN_DEF(0xFFFFFFFF,  0x7FFFFFFF), BN_DEF(0x2F32AFB5,  0xE345003F),
    BN_DEF(0xFA20C170,  0xA6DAD428), BN_DEF(0x3FC45235,  0xC764DAAD),
    BN_DEF(0xE764BEE7,  0x054148E6), BN_DEF(0xFCC68566,  0x15276754),
    BN_DEF(0xB8A0001E,  0x0D0EDC9E), BN_DEF(0x99E5C5BD,  0x0494CCD1),
    BN_DEF(0xB8D6801C,  0x36E3BC7C), BN_DEF(0x48C09862,  0x5483B005),
    BN_DEF(0x96CF3419,  0x76B50F00), BN_DEF(0x77DA18C5,  0x389AE443),
    BN_DEF(0xBF18E63D,  0x43FAADD2), BN_DEF(0xAA81A002,  0x3BB1E78E),
    BN_DEF(0x6B4FB68C,  0x563EAFA1), BN_DEF(0x72C42BDB,  0xBC9874F2),
    BN_DEF(0x8B26FA7D,  0xB737A961), BN_DEF(0xB34F0F78,  0x12F20E95),
    BN_DEF(0x1FEACEBE,  0x9E0D9077), BN_DEF(0xFD29EEF7,  0x055E6835),
    BN_DEF(0x6AE22427,  0x0EDFCD21), BN_DEF(0xCD86F56D,  0xD5E290CB),
    BN_DEF(0x911B1D06,  0x743695E2), BN_DEF(0xCE4EFB4F,  0xAE574155),
    BN_DEF(0x38FAA5FF,  0xB279710F), BN_DEF(0x716BA6E9,  0x7A7EA229),
    BN_DEF(0xDE21BCA2,  0x5A098649), BN_DEF(0xC289C938,  0x577F0984),
    BN_DEF(0x60C36C8E,  0x2CC6587D), BN_DEF(0x48FBFBF7,  0xBD6C8E93),
    BN_DEF(0xEB736483,  0x30DA37E4), BN_DEF(0x7CCE011C,  0xDE1A7A6F),
    BN_DEF(0xB28C81AD,  0x6F1AAD9D), BN_DEF(0x308FE7EE,  0x4435A11C),
    BN_DEF(0x60D977FD,  0x6379A513), BN_DEF(0xBE8B41D9,  0xE2C778C1),
    BN_DEF(0x17611002,  0x9DDAFE5E), BN_DEF(0xA637D6B9,  0xE1FF1D8D),
    BN_DEF(0x777940C1,  0xC7278919), BN_DEF(0x74C2C1FF,  0xC8B97F4E),
    BN_DEF(0x941A17B0,  0x601A0266), BN_DEF(0xE6FBF176,  0x4F017E70),
    BN_DEF(0x770536B8,  0x8583D3E4), BN_DEF(0xB1B95D8C,  0x572B76F3),
    BN_DEF(0xEF1CA6FA,  0x0EA7A151), BN_DEF(0xB06BFA34,  0xDCB56D5B),
    BN_DEF(0xD96471FD,  0xE88454A5), BN_DEF(0x59927DB0,  0x5E0558C1),
    BN_DEF(0xA41D3CBD,  0x98566527), BN_DEF(0x9B56F39A,  0x0EF8AC50),
    BN_DEF(0x79F7F439,  0xF15344ED), BN_DEF(0x707345BB,  0xCC278638),
    BN_DEF(0x3FABE49A,  0xDAAB89AF), BN_DEF(0x9EF68D79,  0x42B1B2AA),
    BN_DEF(0xAF833768,  0x9219FA8F), BN_DEF(0xEAFEB2B0,  0x69EF8F6A),
    BN_DEF(0x576230BD,  0x7B40D901), BN_DEF(0xB1863AEC,  0xBE97F1B1),
    BN_DEF(0x124D9F7C,  0xE649CEE7), BN_DEF(0x8A3219FD,  0xD4F09B20),
    BN_DEF(0xE7169B4A,  0xEC5CE2C1), BN_DEF(0x139E9E78,  0x57EE2B10),
    BN_DEF(0x515DA54D,  0xD6FC2A2C), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

static const BN_ULONG ffdhe6144_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0xD0E40E65, 0xA40E329C),
    BN_DEF(0x7938DAD4, 0xA41D570D), BN_DEF(0xD43161C1, 0x62A69526),
    BN_DEF(0x9ADB1E69, 0x3FDD4A8E), BN_DEF(0xDC6B80D6, 0x5B3B71F9),
    BN_DEF(0xC6272B04, 0xEC9D1810), BN_DEF(0xCACEF403, 0x8CCF2DD5),
    BN_DEF(0xC95B9117, 0xE49F5235), BN_DEF(0xB854338A, 0x505DC82D),
    BN_DEF(0x1562A846, 0x62292C31), BN_DEF(0x6AE77F5E, 0xD72B0374),
    BN_DEF(0x462D538C, 0xF9C9091B), BN_DEF(0x47A67CBE, 0x0AE8DB58),
    BN_DEF(0x22611682, 0xB3A739C1), BN_DEF(0x2A281BF6, 0xEEAAC023),
    BN_DEF(0x77CAF992, 0x94C6651E), BN_DEF(0x94B2BBC1, 0x763E4E4B),
    BN_DEF(0x0077D9B4, 0x587E38DA), BN_DEF(0x183023C3, 0x7FB29F8C),
    BN_DEF(0xF9E3A26E, 0x0ABEC1FF), BN_DEF(0x350511E3, 0xA00EF092),
    BN_DEF(0xDB6340D8, 0xB855322E), BN_DEF(0xA9A96910, 0xA52471F7),
    BN_DEF(0x4CFDB477, 0x388147FB), BN_DEF(0x4E46041F, 0x9B1F5C3E),
    BN_DEF(0xFCCFEC71, 0xCDAD0657), BN_DEF(0x4C701C3A, 0xB38E8C33),
    BN_DEF(0xB1C0FD4C, 0x917BDD64), BN_DEF(0x9B7624C8, 0x3BB45432),
    BN_DEF(0xCAF53EA6, 0x23BA4442), BN_DEF(0x38532A3A, 0x4E677D2C),
    BN_DEF(0x45036C7A, 0x0BFD64B6), BN_DEF(0x5E0DD902, 0xC68A007E),
    BN_DEF(0xF44182E1, 0x4DB5A851), BN_DEF(0x7F88A46B, 0x8EC9B55A),
    BN_DEF(0xCEC97DCF, 0x0A8291CD), BN_DEF(0xF98D0ACC, 0x2A4ECEA9),
    BN_DEF(0x7140003C, 0x1A1DB93D), BN_DEF(0x33CB8B7A, 0x092999A3),
    BN_DEF(0x71AD0038, 0x6DC778F9), BN_DEF(0x918130C4, 0xA907600A),
    BN_DEF(0x2D9E6832, 0xED6A1E01), BN_DEF(0xEFB4318A, 0x7135C886),
    BN_DEF(0x7E31CC7A, 0x87F55BA5), BN_DEF(0x55034004, 0x7763CF1D),
    BN_DEF(0xD69F6D18, 0xAC7D5F42), BN_DEF(0xE58857B6, 0x7930E9E4),
    BN_DEF(0x164DF4FB, 0x6E6F52C3), BN_DEF(0x669E1EF1, 0x25E41D2B),
    BN_DEF(0x3FD59D7C, 0x3C1B20EE), BN_DEF(0xFA53DDEF, 0x0ABCD06B),
    BN_DEF(0xD5C4484E, 0x1DBF9A42), BN_DEF(0x9B0DEADA, 0xABC52197),
    BN_DEF(0x22363A0D, 0xE86D2BC5), BN_DEF(0x9C9DF69E, 0x5CAE82AB),
    BN_DEF(0x71F54BFF, 0x64F2E21E), BN_DEF(0xE2D74DD3, 0xF4FD4452),
    BN_DEF(0xBC437944, 0xB4130C93), BN_DEF(0x85139270, 0xAEFE1309),
    BN_DEF(0xC186D91C, 0x598CB0FA), BN_DEF(0x91F7F7EE, 0x7AD91D26),
    BN_DEF(0xD6E6C907, 0x61B46FC9), BN_DEF(0xF99C0238, 0xBC34F4DE),
    BN_DEF(0x6519035B, 0xDE355B3B), BN_DEF(0x611FCFDC, 0x886B4238),
    BN_DEF(0xC1B2EFFA, 0xC6F34A26), BN_DEF(0x7D1683B2, 0xC58EF183),
    BN_DEF(0x2EC22005, 0x3BB5FCBC), BN_DEF(0x4C6FAD73, 0xC3FE3B1B),
    BN_DEF(0xEEF28183, 0x8E4F1232), BN_DEF(0xE98583FF, 0x9172FE9C),
    BN_DEF(0x28342F61, 0xC03404CD), BN_DEF(0xCDF7E2EC, 0x9E02FCE1),
    BN_DEF(0xEE0A6D70, 0x0B07A7C8), BN_DEF(0x6372BB19, 0xAE56EDE7),
    BN_DEF(0xDE394DF4, 0x1D4F42A3), BN_DEF(0x60D7F468, 0xB96ADAB7),
    BN_DEF(0xB2C8E3FB, 0xD108A94B), BN_DEF(0xB324FB61, 0xBC0AB182),
    BN_DEF(0x483A797A, 0x30ACCA4F), BN_DEF(0x36ADE735, 0x1DF158A1),
    BN_DEF(0xF3EFE872, 0xE2A689DA), BN_DEF(0xE0E68B77, 0x984F0C70),
    BN_DEF(0x7F57C935, 0xB557135E), BN_DEF(0x3DED1AF3, 0x85636555),
    BN_DEF(0x5F066ED0, 0x2433F51F), BN_DEF(0xD5FD6561, 0xD3DF1ED5),
    BN_DEF(0xAEC4617A, 0xF681B202), BN_DEF(0x630C75D8, 0x7D2FE363),
    BN_DEF(0x249B3EF9, 0xCC939DCE), BN_DEF(0x146433FB, 0xA9E13641),
    BN_DEF(0xCE2D3695, 0xD8B9C583), BN_DEF(0x273D3CF1, 0xAFDC5620),
    BN_DEF(0xA2BB4A9A, 0xADF85458), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG ffdhe6144_q[] = {
    BN_DEF(0xFFFFFFFF,  0xFFFFFFFF), BN_DEF(0x68720732,  0x5207194E),
    BN_DEF(0xBC9C6D6A,  0xD20EAB86), BN_DEF(0x6A18B0E0,  0xB1534A93),
    BN_DEF(0x4D6D8F34,  0x1FEEA547), BN_DEF(0xEE35C06B,  0x2D9DB8FC),
    BN_DEF(0x63139582,  0xF64E8C08), BN_DEF(0xE5677A01,  0xC66796EA),
    BN_DEF(0xE4ADC88B,  0x724FA91A), BN_DEF(0xDC2A19C5,  0x282EE416),
    BN_DEF(0x8AB15423,  0x31149618), BN_DEF(0x3573BFAF,  0x6B9581BA),
    BN_DEF(0xA316A9C6,  0x7CE4848D), BN_DEF(0x23D33E5F,  0x05746DAC),
    BN_DEF(0x91308B41,  0x59D39CE0), BN_DEF(0x95140DFB,  0x77556011),
    BN_DEF(0x3BE57CC9,  0xCA63328F), BN_DEF(0xCA595DE0,  0x3B1F2725),
    BN_DEF(0x003BECDA,  0xAC3F1C6D), BN_DEF(0x0C1811E1,  0x3FD94FC6),
    BN_DEF(0xFCF1D137,  0x855F60FF), BN_DEF(0x1A8288F1,  0x50077849),
    BN_DEF(0x6DB1A06C,  0x5C2A9917), BN_DEF(0xD4D4B488,  0xD29238FB),
    BN_DEF(0xA67EDA3B,  0x9C40A3FD), BN_DEF(0x2723020F,  0xCD8FAE1F),
    BN_DEF(0xFE67F638,  0x66D6832B), BN_DEF(0xA6380E1D,  0x59C74619),
    BN_DEF(0x58E07EA6,  0x48BDEEB2), BN_DEF(0x4DBB1264,  0x1DDA2A19),
    BN_DEF(0x657A9F53,  0x11DD2221), BN_DEF(0x1C29951D,  0x2733BE96),
    BN_DEF(0x2281B63D,  0x05FEB25B), BN_DEF(0x2F06EC81,  0xE345003F),
    BN_DEF(0xFA20C170,  0xA6DAD428), BN_DEF(0x3FC45235,  0xC764DAAD),
    BN_DEF(0xE764BEE7,  0x054148E6), BN_DEF(0xFCC68566,  0x15276754),
    BN_DEF(0xB8A0001E,  0x0D0EDC9E), BN_DEF(0x99E5C5BD,  0x0494CCD1),
    BN_DEF(0xB8D6801C,  0x36E3BC7C), BN_DEF(0x48C09862,  0x5483B005),
    BN_DEF(0x96CF3419,  0x76B50F00), BN_DEF(0x77DA18C5,  0x389AE443),
    BN_DEF(0xBF18E63D,  0x43FAADD2), BN_DEF(0xAA81A002,  0x3BB1E78E),
    BN_DEF(0x6B4FB68C,  0x563EAFA1), BN_DEF(0x72C42BDB,  0xBC9874F2),
    BN_DEF(0x8B26FA7D,  0xB737A961), BN_DEF(0xB34F0F78,  0x12F20E95),
    BN_DEF(0x1FEACEBE,  0x9E0D9077), BN_DEF(0xFD29EEF7,  0x055E6835),
    BN_DEF(0x6AE22427,  0x0EDFCD21), BN_DEF(0xCD86F56D,  0xD5E290CB),
    BN_DEF(0x911B1D06,  0x743695E2), BN_DEF(0xCE4EFB4F,  0xAE574155),
    BN_DEF(0x38FAA5FF,  0xB279710F), BN_DEF(0x716BA6E9,  0x7A7EA229),
    BN_DEF(0xDE21BCA2,  0x5A098649), BN_DEF(0xC289C938,  0x577F0984),
    BN_DEF(0x60C36C8E,  0x2CC6587D), BN_DEF(0x48FBFBF7,  0xBD6C8E93),
    BN_DEF(0xEB736483,  0x30DA37E4), BN_DEF(0x7CCE011C,  0xDE1A7A6F),
    BN_DEF(0xB28C81AD,  0x6F1AAD9D), BN_DEF(0x308FE7EE,  0x4435A11C),
    BN_DEF(0x60D977FD,  0x6379A513), BN_DEF(0xBE8B41D9,  0xE2C778C1),
    BN_DEF(0x17611002,  0x9DDAFE5E), BN_DEF(0xA637D6B9,  0xE1FF1D8D),
    BN_DEF(0x777940C1,  0xC7278919), BN_DEF(0x74C2C1FF,  0xC8B97F4E),
    BN_DEF(0x941A17B0,  0x601A0266), BN_DEF(0xE6FBF176,  0x4F017E70),
    BN_DEF(0x770536B8,  0x8583D3E4), BN_DEF(0xB1B95D8C,  0x572B76F3),
    BN_DEF(0xEF1CA6FA,  0x0EA7A151), BN_DEF(0xB06BFA34,  0xDCB56D5B),
    BN_DEF(0xD96471FD,  0xE88454A5), BN_DEF(0x59927DB0,  0x5E0558C1),
    BN_DEF(0xA41D3CBD,  0x98566527), BN_DEF(0x9B56F39A,  0x0EF8AC50),
    BN_DEF(0x79F7F439,  0xF15344ED), BN_DEF(0x707345BB,  0xCC278638),
    BN_DEF(0x3FABE49A,  0xDAAB89AF), BN_DEF(0x9EF68D79,  0x42B1B2AA),
    BN_DEF(0xAF833768,  0x9219FA8F), BN_DEF(0xEAFEB2B0,  0x69EF8F6A),
    BN_DEF(0x576230BD,  0x7B40D901), BN_DEF(0xB1863AEC,  0xBE97F1B1),
    BN_DEF(0x124D9F7C,  0xE649CEE7), BN_DEF(0x8A3219FD,  0xD4F09B20),
    BN_DEF(0xE7169B4A,  0xEC5CE2C1), BN_DEF(0x139E9E78,  0x57EE2B10),
    BN_DEF(0x515DA54D,  0xD6FC2A2C), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

static const BN_ULONG ffdhe8192_p[] = {
    BN_DEF(0xFFFFFFFF, 0xFFFFFFFF), BN_DEF(0xC5C6424C, 0xD68C8BB7),
    BN_DEF(0x838FF88C, 0x011E2A94), BN_DEF(0xA9F4614E, 0x0822E506),
    BN_DEF(0xF7A8443D, 0x97D11D49), BN_DEF(0x30677F0D, 0xA6BBFDE5),
    BN_DEF(0xC1FE86FE, 0x2F741EF8), BN_DEF(0x5D71A87E, 0xFAFABE1C),
    BN_DEF(0xFBE58A30, 0xDED2FBAB), BN_DEF(0x72B0A66E, 0xB6855DFE),
    BN_DEF(0xBA8A4FE8, 0x1EFC8CE0), BN_DEF(0x3F2FA457, 0x83F81D4A),
    BN_DEF(0xA577E231, 0xA1FE3075), BN_DEF(0x88D9C0A0, 0xD5B80194),
    BN_DEF(0xAD9A95F9, 0x624816CD), BN_DEF(0x50C1217B, 0x99E9E316),
    BN_DEF(0x0E423CFC, 0x51AA691E), BN_DEF(0x3826E52C, 0x1C217E6C),
    BN_DEF(0x09703FEE, 0x51A8A931), BN_DEF(0x6A460E74, 0xBB709987),
    BN_DEF(0x9C86B022, 0x541FC68C), BN_DEF(0x46FD8251, 0x59160CC0),
    BN_DEF(0x35C35F5C, 0x2846C0BA), BN_DEF(0x8B758282, 0x54504AC7),
    BN_DEF(0xD2AF05E4, 0x29388839), BN_DEF(0xC01BD702, 0xCB2C0F1C),
    BN_DEF(0x7C932665, 0x555B2F74), BN_DEF(0xA3AB8829, 0x86B63142),
    BN_DEF(0xF64B10EF, 0x0B8CC3BD), BN_DEF(0xEDD1CC5E, 0x687FEB69),
    BN_DEF(0xC9509D43, 0xFDB23FCE), BN_DEF(0xD951AE64, 0x1E425A31),
    BN_DEF(0xF600C838, 0x36AD004C), BN_DEF(0xCFF46AAA, 0xA40E329C),
    BN_DEF(0x7938DAD4, 0xA41D570D), BN_DEF(0xD43161C1, 0x62A69526),
    BN_DEF(0x9ADB1E69, 0x3FDD4A8E), BN_DEF(0xDC6B80D6, 0x5B3B71F9),
    BN_DEF(0xC6272B04, 0xEC9D1810), BN_DEF(0xCACEF403, 0x8CCF2DD5),
    BN_DEF(0xC95B9117, 0xE49F5235), BN_DEF(0xB854338A, 0x505DC82D),
    BN_DEF(0x1562A846, 0x62292C31), BN_DEF(0x6AE77F5E, 0xD72B0374),
    BN_DEF(0x462D538C, 0xF9C9091B), BN_DEF(0x47A67CBE, 0x0AE8DB58),
    BN_DEF(0x22611682, 0xB3A739C1), BN_DEF(0x2A281BF6, 0xEEAAC023),
    BN_DEF(0x77CAF992, 0x94C6651E), BN_DEF(0x94B2BBC1, 0x763E4E4B),
    BN_DEF(0x0077D9B4, 0x587E38DA), BN_DEF(0x183023C3, 0x7FB29F8C),
    BN_DEF(0xF9E3A26E, 0x0ABEC1FF), BN_DEF(0x350511E3, 0xA00EF092),
    BN_DEF(0xDB6340D8, 0xB855322E), BN_DEF(0xA9A96910, 0xA52471F7),
    BN_DEF(0x4CFDB477, 0x388147FB), BN_DEF(0x4E46041F, 0x9B1F5C3E),
    BN_DEF(0xFCCFEC71, 0xCDAD0657), BN_DEF(0x4C701C3A, 0xB38E8C33),
    BN_DEF(0xB1C0FD4C, 0x917BDD64), BN_DEF(0x9B7624C8, 0x3BB45432),
    BN_DEF(0xCAF53EA6, 0x23BA4442), BN_DEF(0x38532A3A, 0x4E677D2C),
    BN_DEF(0x45036C7A, 0x0BFD64B6), BN_DEF(0x5E0DD902, 0xC68A007E),
    BN_DEF(0xF44182E1, 0x4DB5A851), BN_DEF(0x7F88A46B, 0x8EC9B55A),
    BN_DEF(0xCEC97DCF, 0x0A8291CD), BN_DEF(0xF98D0ACC, 0x2A4ECEA9),
    BN_DEF(0x7140003C, 0x1A1DB93D), BN_DEF(0x33CB8B7A, 0x092999A3),
    BN_DEF(0x71AD0038, 0x6DC778F9), BN_DEF(0x918130C4, 0xA907600A),
    BN_DEF(0x2D9E6832, 0xED6A1E01), BN_DEF(0xEFB4318A, 0x7135C886),
    BN_DEF(0x7E31CC7A, 0x87F55BA5), BN_DEF(0x55034004, 0x7763CF1D),
    BN_DEF(0xD69F6D18, 0xAC7D5F42), BN_DEF(0xE58857B6, 0x7930E9E4),
    BN_DEF(0x164DF4FB, 0x6E6F52C3), BN_DEF(0x669E1EF1, 0x25E41D2B),
    BN_DEF(0x3FD59D7C, 0x3C1B20EE), BN_DEF(0xFA53DDEF, 0x0ABCD06B),
    BN_DEF(0xD5C4484E, 0x1DBF9A42), BN_DEF(0x9B0DEADA, 0xABC52197),
    BN_DEF(0x22363A0D, 0xE86D2BC5), BN_DEF(0x9C9DF69E, 0x5CAE82AB),
    BN_DEF(0x71F54BFF, 0x64F2E21E), BN_DEF(0xE2D74DD3, 0xF4FD4452),
    BN_DEF(0xBC437944, 0xB4130C93), BN_DEF(0x85139270, 0xAEFE1309),
    BN_DEF(0xC186D91C, 0x598CB0FA), BN_DEF(0x91F7F7EE, 0x7AD91D26),
    BN_DEF(0xD6E6C907, 0x61B46FC9), BN_DEF(0xF99C0238, 0xBC34F4DE),
    BN_DEF(0x6519035B, 0xDE355B3B), BN_DEF(0x611FCFDC, 0x886B4238),
    BN_DEF(0xC1B2EFFA, 0xC6F34A26), BN_DEF(0x7D1683B2, 0xC58EF183),
    BN_DEF(0x2EC22005, 0x3BB5FCBC), BN_DEF(0x4C6FAD73, 0xC3FE3B1B),
    BN_DEF(0xEEF28183, 0x8E4F1232), BN_DEF(0xE98583FF, 0x9172FE9C),
    BN_DEF(0x28342F61, 0xC03404CD), BN_DEF(0xCDF7E2EC, 0x9E02FCE1),
    BN_DEF(0xEE0A6D70, 0x0B07A7C8), BN_DEF(0x6372BB19, 0xAE56EDE7),
    BN_DEF(0xDE394DF4, 0x1D4F42A3), BN_DEF(0x60D7F468, 0xB96ADAB7),
    BN_DEF(0xB2C8E3FB, 0xD108A94B), BN_DEF(0xB324FB61, 0xBC0AB182),
    BN_DEF(0x483A797A, 0x30ACCA4F), BN_DEF(0x36ADE735, 0x1DF158A1),
    BN_DEF(0xF3EFE872, 0xE2A689DA), BN_DEF(0xE0E68B77, 0x984F0C70),
    BN_DEF(0x7F57C935, 0xB557135E), BN_DEF(0x3DED1AF3, 0x85636555),
    BN_DEF(0x5F066ED0, 0x2433F51F), BN_DEF(0xD5FD6561, 0xD3DF1ED5),
    BN_DEF(0xAEC4617A, 0xF681B202), BN_DEF(0x630C75D8, 0x7D2FE363),
    BN_DEF(0x249B3EF9, 0xCC939DCE), BN_DEF(0x146433FB, 0xA9E13641),
    BN_DEF(0xCE2D3695, 0xD8B9C583), BN_DEF(0x273D3CF1, 0xAFDC5620),
    BN_DEF(0xA2BB4A9A, 0xADF85458), BN_DEF(0xFFFFFFFF, 0xFFFFFFFF)
};
/* q = (p - 1) / 2 */
static const BN_ULONG ffdhe8192_q[] = {
    BN_DEF(0xFFFFFFFF,  0x7FFFFFFF), BN_DEF(0xE2E32126,  0x6B4645DB),
    BN_DEF(0x41C7FC46,  0x008F154A), BN_DEF(0x54FA30A7,  0x84117283),
    BN_DEF(0xFBD4221E,  0xCBE88EA4), BN_DEF(0x9833BF86,  0x535DFEF2),
    BN_DEF(0x60FF437F,  0x17BA0F7C), BN_DEF(0x2EB8D43F,  0x7D7D5F0E),
    BN_DEF(0xFDF2C518,  0x6F697DD5), BN_DEF(0x39585337,  0x5B42AEFF),
    BN_DEF(0x5D4527F4,  0x8F7E4670), BN_DEF(0x1F97D22B,  0xC1FC0EA5),
    BN_DEF(0xD2BBF118,  0x50FF183A), BN_DEF(0x446CE050,  0xEADC00CA),
    BN_DEF(0xD6CD4AFC,  0xB1240B66), BN_DEF(0x286090BD,  0x4CF4F18B),
    BN_DEF(0x07211E7E,  0x28D5348F), BN_DEF(0x1C137296,  0x0E10BF36),
    BN_DEF(0x84B81FF7,  0x28D45498), BN_DEF(0xB523073A,  0x5DB84CC3),
    BN_DEF(0x4E435811,  0xAA0FE346), BN_DEF(0x237EC128,  0x2C8B0660),
    BN_DEF(0x1AE1AFAE,  0x1423605D), BN_DEF(0xC5BAC141,  0x2A282563),
    BN_DEF(0xE95782F2,  0x149C441C), BN_DEF(0x600DEB81,  0xE596078E),
    BN_DEF(0x3E499332,  0xAAAD97BA), BN_DEF(0x51D5C414,  0xC35B18A1),
    BN_DEF(0xFB258877,  0x05C661DE), BN_DEF(0xF6E8E62F,  0xB43FF5B4),
    BN_DEF(0x64A84EA1,  0x7ED91FE7), BN_DEF(0xECA8D732,  0x0F212D18),
    BN_DEF(0x7B00641C,  0x1B568026), BN_DEF(0x67FA3555,  0x5207194E),
    BN_DEF(0xBC9C6D6A,  0xD20EAB86), BN_DEF(0x6A18B0E0,  0xB1534A93),
    BN_DEF(0x4D6D8F34,  0x1FEEA547), BN_DEF(0xEE35C06B,  0x2D9DB8FC),
    BN_DEF(0x63139582,  0xF64E8C08), BN_DEF(0xE5677A01,  0xC66796EA),
    BN_DEF(0xE4ADC88B,  0x724FA91A), BN_DEF(0xDC2A19C5,  0x282EE416),
    BN_DEF(0x8AB15423,  0x31149618), BN_DEF(0x3573BFAF,  0x6B9581BA),
    BN_DEF(0xA316A9C6,  0x7CE4848D), BN_DEF(0x23D33E5F,  0x05746DAC),
    BN_DEF(0x91308B41,  0x59D39CE0), BN_DEF(0x95140DFB,  0x77556011),
    BN_DEF(0x3BE57CC9,  0xCA63328F), BN_DEF(0xCA595DE0,  0x3B1F2725),
    BN_DEF(0x003BECDA,  0xAC3F1C6D), BN_DEF(0x0C1811E1,  0x3FD94FC6),
    BN_DEF(0xFCF1D137,  0x855F60FF), BN_DEF(0x1A8288F1,  0x50077849),
    BN_DEF(0x6DB1A06C,  0x5C2A9917), BN_DEF(0xD4D4B488,  0xD29238FB),
    BN_DEF(0xA67EDA3B,  0x9C40A3FD), BN_DEF(0x2723020F,  0xCD8FAE1F),
    BN_DEF(0xFE67F638,  0x66D6832B), BN_DEF(0xA6380E1D,  0x59C74619),
    BN_DEF(0x58E07EA6,  0x48BDEEB2), BN_DEF(0x4DBB1264,  0x1DDA2A19),
    BN_DEF(0x657A9F53,  0x11DD2221), BN_DEF(0x1C29951D,  0x2733BE96),
    BN_DEF(0x2281B63D,  0x05FEB25B), BN_DEF(0x2F06EC81,  0xE345003F),
    BN_DEF(0xFA20C170,  0xA6DAD428), BN_DEF(0x3FC45235,  0xC764DAAD),
    BN_DEF(0xE764BEE7,  0x054148E6), BN_DEF(0xFCC68566,  0x15276754),
    BN_DEF(0xB8A0001E,  0x0D0EDC9E), BN_DEF(0x99E5C5BD,  0x0494CCD1),
    BN_DEF(0xB8D6801C,  0x36E3BC7C), BN_DEF(0x48C09862,  0x5483B005),
    BN_DEF(0x96CF3419,  0x76B50F00), BN_DEF(0x77DA18C5,  0x389AE443),
    BN_DEF(0xBF18E63D,  0x43FAADD2), BN_DEF(0xAA81A002,  0x3BB1E78E),
    BN_DEF(0x6B4FB68C,  0x563EAFA1), BN_DEF(0x72C42BDB,  0xBC9874F2),
    BN_DEF(0x8B26FA7D,  0xB737A961), BN_DEF(0xB34F0F78,  0x12F20E95),
    BN_DEF(0x1FEACEBE,  0x9E0D9077), BN_DEF(0xFD29EEF7,  0x055E6835),
    BN_DEF(0x6AE22427,  0x0EDFCD21), BN_DEF(0xCD86F56D,  0xD5E290CB),
    BN_DEF(0x911B1D06,  0x743695E2), BN_DEF(0xCE4EFB4F,  0xAE574155),
    BN_DEF(0x38FAA5FF,  0xB279710F), BN_DEF(0x716BA6E9,  0x7A7EA229),
    BN_DEF(0xDE21BCA2,  0x5A098649), BN_DEF(0xC289C938,  0x577F0984),
    BN_DEF(0x60C36C8E,  0x2CC6587D), BN_DEF(0x48FBFBF7,  0xBD6C8E93),
    BN_DEF(0xEB736483,  0x30DA37E4), BN_DEF(0x7CCE011C,  0xDE1A7A6F),
    BN_DEF(0xB28C81AD,  0x6F1AAD9D), BN_DEF(0x308FE7EE,  0x4435A11C),
    BN_DEF(0x60D977FD,  0x6379A513), BN_DEF(0xBE8B41D9,  0xE2C778C1),
    BN_DEF(0x17611002,  0x9DDAFE5E), BN_DEF(0xA637D6B9,  0xE1FF1D8D),
    BN_DEF(0x777940C1,  0xC7278919), BN_DEF(0x74C2C1FF,  0xC8B97F4E),
    BN_DEF(0x941A17B0,  0x601A0266), BN_DEF(0xE6FBF176,  0x4F017E70),
    BN_DEF(0x770536B8,  0x8583D3E4), BN_DEF(0xB1B95D8C,  0x572B76F3),
    BN_DEF(0xEF1CA6FA,  0x0EA7A151), BN_DEF(0xB06BFA34,  0xDCB56D5B),
    BN_DEF(0xD96471FD,  0xE88454A5), BN_DEF(0x59927DB0,  0x5E0558C1),
    BN_DEF(0xA41D3CBD,  0x98566527), BN_DEF(0x9B56F39A,  0x0EF8AC50),
    BN_DEF(0x79F7F439,  0xF15344ED), BN_DEF(0x707345BB,  0xCC278638),
    BN_DEF(0x3FABE49A,  0xDAAB89AF), BN_DEF(0x9EF68D79,  0x42B1B2AA),
    BN_DEF(0xAF833768,  0x9219FA8F), BN_DEF(0xEAFEB2B0,  0x69EF8F6A),
    BN_DEF(0x576230BD,  0x7B40D901), BN_DEF(0xB1863AEC,  0xBE97F1B1),
    BN_DEF(0x124D9F7C,  0xE649CEE7), BN_DEF(0x8A3219FD,  0xD4F09B20),
    BN_DEF(0xE7169B4A,  0xEC5CE2C1), BN_DEF(0x139E9E78,  0x57EE2B10),
    BN_DEF(0x515DA54D,  0xD6FC2A2C), BN_DEF(0xFFFFFFFF,  0x7FFFFFFF),
};

/* Macro to make a BIGNUM from static data */

# define make_dh_bn(x)                   \
    extern const BIGNUM ossl_bignum_##x; \
    const BIGNUM ossl_bignum_##x = {     \
        (BN_ULONG *) x,                  \
        OSSL_NELEM(x),                   \
        OSSL_NELEM(x),                   \
        0, BN_FLG_STATIC_DATA };

static const BN_ULONG value_2 = 2;

const BIGNUM ossl_bignum_const_2 = {
    (BN_ULONG *)&value_2, 1, 1, 0, BN_FLG_STATIC_DATA
};

make_dh_bn(dh1024_160_p)
make_dh_bn(dh1024_160_q)
make_dh_bn(dh1024_160_g)
make_dh_bn(dh2048_224_p)
make_dh_bn(dh2048_224_q)
make_dh_bn(dh2048_224_g)
make_dh_bn(dh2048_256_p)
make_dh_bn(dh2048_256_q)
make_dh_bn(dh2048_256_g)

make_dh_bn(ffdhe2048_p)
make_dh_bn(ffdhe2048_q)
make_dh_bn(ffdhe3072_p)
make_dh_bn(ffdhe3072_q)
make_dh_bn(ffdhe4096_p)
make_dh_bn(ffdhe4096_q)
make_dh_bn(ffdhe6144_p)
make_dh_bn(ffdhe6144_q)
make_dh_bn(ffdhe8192_p)
make_dh_bn(ffdhe8192_q)

# ifndef FIPS_MODULE
make_dh_bn(modp_1536_p)
make_dh_bn(modp_1536_q)
# endif
make_dh_bn(modp_2048_p)
make_dh_bn(modp_2048_q)
make_dh_bn(modp_3072_p)
make_dh_bn(modp_3072_q)
make_dh_bn(modp_4096_p)
make_dh_bn(modp_4096_q)
make_dh_bn(modp_6144_p)
make_dh_bn(modp_6144_q)
make_dh_bn(modp_8192_p)
make_dh_bn(modp_8192_q)
