/**
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.security.wycheproof;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.RSAPrivateCrtKeySpec;
import java.security.spec.RSAPublicKeySpec;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

/** Tests PKCS #1 v 1.5 signatures */
// TODO(bleichen):
// - document stuff
// - Join other RSA tests
@RunWith(JUnit4.class)
public class RsaSignatureTest {
  static final RSAPublicKeySpec RSA_KEY1 =
      new RSAPublicKeySpec(
          new BigInteger(
              "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
                  + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
                  + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
                  + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f",
              16),
          new BigInteger("65537"));
  static final String ALGORITHM_KEY1 = "SHA256WithRSA";

  /**
   * Test signatures for RSA_KEY1 and MESSAGE = "Test". The first signature is valid. All other
   * signatures are invalid. The signatures were generated by modifying the PKCS #1 padding in
   * various ways. I.e. while the generation of the false signature did require the private RSA key,
   * failing the test often is a sign that signatures can be forged. Such forgeries are much too
   * frequent. The following list is just an incomplete selection of past vulnerabilities:
   *
   * <ul>
   *   <li>CVE-2006-4339: OpenSSL before 0.9.7 was vulnerable to signature forgeries. After the
   *       hasty patch OpenSSL still accepted at least 2^800 false 2048-bit signatures for each
   *       valid one. Even though unclear whether this was exploitable it was only fixed around
   *       2014.
   *   <li>CVE-2006-4340: Mozilla NSS before 3.11.3.
   *   <li>CVE-2006-4790: GnuTLS before version 1.4.4.
   *   <li>CVE-2012-2388: StrongSwan
   *   <li>CVE-2016-1494: Python-RSA before version 3.3 failed to correclty verify RSA signatures.
   *   <li>BouncyCastle was vulnerable at least until version 1.47. The bug was silently fixed
   *       around 2012.
   *   <li>Berserk: http://www.intelsecurity.com/advanced-threat-research/berserk.html
   *   <li>Truncated comparison of hashes e.g.: http://wiibrew.org/wiki/Signing_bug
   *   <li>CVE-2016-5547: OpenJDK8 RSA signature's throws an OutOfMemoryError for some invalid
   *       signatures
   * </ul>
   */
  static final String[] SIGNATURES_KEY1 = {
    // Message:Test
    // Digest:sha256
    // Key size:1024

    // Correct signature
    // padding:3031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "68ea71ee1911687eb54b3d19cedcfd44719d0b24accccc59bdafd84e4eba48ef"
        + "0be7f115e7073f9f273286a7dcee3b94cdbe208e30ae496987479d3aa12ab0e1"
        + "2685ab592d7693a494e6ad27d526ed3ab5912c7f81e09983931794c2165c22fd"
        + "859e0f9af1a93a4dfe144098c562731e6059d236b52cb865996c87a9baf7f103",

    // long form encoding of length
    // padding:308131300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "52f46d508e31f030b17c537888585f919037562e15f1924543601a41f9b701ee"
        + "416ad73d6576b4eaaa64e685289dc478751dfe2d7e588252bfe2d43f4b3a31c6"
        + "c6c39a9df884a2fc2e45f09c2150a830974b1c9d26090830b37bf06f1d57be1d"
        + "a34ebb016e9db7ce2c34e94872c89567ff6f2ab35a1a9fb6632e100c7d7af834",
    // padding:303230810d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "3f34017b3172aaeec72d208308e9b83150699f86634b948847eab56f0169fef5"
        + "1b5636a96866f4f0f4c649400489e047803a91f2b2f32ab715065e20770c4e27"
        + "88946b85aca5c90efdd6a9458dd9b6f797f96a3de88d2e4896afe147d8c03899"
        + "43828100061903a30eaff1dadd98d3e49dba56cdcfa5f215d9c615f974f4a0bc",
    // padding:3032300e06810960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "1478337676aa47ca72ea7557facff06f6c777f56063f4487d345e43dc56a6bc5"
        + "f8a891085d53a32c9d1c3cf7f469e7f56847b0b1b9b5b784526078271f21d055"
        + "0afc40f81e2b8e8dec851d87511cace965edceb83cb96c8d6616e1ee75bb22c5"
        + "4412fc942a6f71c9fc609a31a69d34b774a97c1ba4f85cca28d9993db8543f75",
    // padding:3032300e06096086480165030402010581000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "77ba423e600bdd761ed10e7c00698a87fe1322f5f42b2902a0be7a24b1cf44f6"
        + "13fa55edeb2ded0475f8e1a13e5368f9a2bfc4f2f926ef289a2207bf3689fc1c"
        + "8ec3e5463064a7f51bbc993966cc4016319b7c95f282372f1ff848d7fca753a8"
        + "1d905b3341b0fbf60ba186e750f3171cfc84288eff8742bda432bd6c8dc04f9f",
    // padding:3032300d06096086480165030402010500048120532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "9460ee79bb990bc3fe28cfca92363e6ff6900e3b61b3a402f06024a72b7a65d6"
        + "2094b4419e93900995eb121327f72b26b139bab3e5e2bd0c82e0cf6357f3b16f"
        + "1c1dd4407a9a820f20e3baaa2259614d9ee3e015e1c1778befa13aff1e545ea1"
        + "758cba4713631d63180a91b52df394294441642964a024f45b2251c90e002ec0",

    // length contains leading 0
    // padding:30820031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "96ac043d3cada45aed0dbdc4662dcf7855553a5effa1077048b51c7e9bfff7c2"
        + "bb3486ea42894d4b4afb26a3b3bd32cb68d5c4d8ca2622f50d8c56fdc25baf83"
        + "b9909ecb096419ddc13578dcc8121007f7204ee82c517ae03de70fa23ef23906"
        + "02029a0cbc8a96c5b781d857dbf12802aa561f5f41ea35aa0babb91b9f891762",
    // padding:30333082000d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "2a70643572a7cda975d9e2c0827837e60eaa78c297b1ff75b84f654a91fe3329"
        + "4ccbeda52676ece50fcc03018151e66c24940bd0574ab85a6599231d587f4a6e"
        + "0ae841cb6696e7dcfd182cb75001304e36887bc4fe3b373828f8b0e62ac2300a"
        + "626c9e6a2cd05bb7910e74da2978dae1948f855b3b455cd30367160e21581cab",
    // padding:3033300f0682000960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "27778e39b45dee1e7003f1d315d3466fc111791187ddc056784c158df92097e1"
        + "23021e11918b6df8d905304db732e83d904bc914271b03def4ee129c3fc8adcc"
        + "4f81b690e09e70e46c8b920093f304e64ecb7358740e976d28538a9eecf09ec1"
        + "e1cd47df9107968207b21538cabe076bcc07c3862c46a793fcf638c70a972885",
    // padding:3033300f0609608648016503040201058200000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "3a879e9f883b158908014f3617cae3315d47afdadd30840494f68d91c04dfe81"
        + "bd16a40c7d21238cd1816928d989a232a3492325ab0f95d4426e3fb7d58c9908"
        + "191dc557d8779dabb282287b7860c30e0796283428e0276447235809882ee990"
        + "deb0f4312c01e7ddf0690406eeacb660acc6957bb670904cfd8d04df5e3ebda2",
    // padding:3033300d0609608648016503040201050004820020532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "2b82155f363a3b283ae455f59e41c29dec2fbd8c7438b0e347aec5b38c7c895c"
        + "b7d326870e4fbdb935fcbb561f223bd926dbe8b95ef5eaab27920dbe30c641e9"
        + "9f526a9bc356af54198b459b59383135a82cd5b6edab7da0b1a51d939b2f9951"
        + "e1432d637c4f04a3546ed9c890143ae364602b94eabdaa2a45e4bdf0b5bdfa71",

    // wrong length
    // padding:3032300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "1dda56dc953aeee7fd76ae7166d92ab9e3d1d9759e76f8f1d7634a73cbf69e39"
        + "d8249153d7c2d83c9664db13552f0c78df34b8a67e7b6c10bcc61b5ead7ba62c"
        + "e0ec7ba8ac78d146f7e4cadee6f6250e0bc3100660e7afbe3afa17fa288d9754"
        + "9b4c8cacc00ac5c942673485739f89c9e5e63ad2be97a8f2313f5c5b095e7542",
    // padding:3030300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "692c143b82196a391a3546607336e6f3bc047412645cf0def0d62d1b42234c14"
        + "da138bb7f451b45073bbda2aba23412e83bc40d4e7de3e0684f2cad7d059f2d6"
        + "831aa3d2ece4964ca75cd41dce23c5ba495c15345b36947b4b5a051fe1b84e14"
        + "8b5ae21f112d2245b1acbaeef9dc4a0c408829b9d2b1b5ab1d3a40af0a27b99e",
    // padding:3031300e060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "66c31a45b2287425a20f65c3eba9cc58c370882f5fc62921935491fbd516df9b"
        + "af9b28304a21d9008b61a92779ecfb3b0c03f6d74354f5159956e3fc1d35bd73"
        + "76289378f05d7a71e05ab32794f2566a54635e8dc64740acbe10a293ceddbebe"
        + "8499b520f406023a134eb9927ebb788b92488f036d109ec0a40ac52372e847b3",
    // padding:3031300c060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "7b85536bdcda4ad3fc40129f2ff9dc85d9ec049913784064e735868664044627"
        + "8a2006d93fb33429407597e5d8c783e3f7aee8a7791d69139f3c802a6547f01b"
        + "f987415eec2447b0e8c4f3aee7ae2085d141fa34ca6634bc109dede93285d5c4"
        + "0cfcd98bd47ceb9cc1890dfff53b7ebb8038533580c7a67fe14c0c422e20cd64",
    // padding:3031300d060a60864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "5d77fba3cbb1905d83aa532fcc3227a95d7931bf0c2ab51f8118824de9dc029b"
        + "d2470adf48b41c694ec7359d00a1336990c30ee368dd40bd681ba74794415d39"
        + "97e7a756659397bf6abd44ca91c12a8580a3f5d1cdbc7f3be0c23c72334ce9b1"
        + "419e6540dab73f5ff8ab57d0bbbe92b688bd3495f9344822b622042c2491bc41",
    // padding:3031300d060860864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "7b5476fb78f389d1131764e7a13322f86008924c8c098f6d74f2df4dcc5a504c"
        + "d786b3eaae33295cd1e87a2bbd1a06cb385674d465110a9a990d52de9a67f1c1"
        + "3ecaaa86383d489423c084fae9ecd2e9b109f4f04b8c013e3409128f3a079c06"
        + "8c1ad27bc2a20e76ad149325b7b0f0bd804a4e33949a98aac49076260702b0b0",
    // padding:3031300d060960864801650304020105010420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "6034e1253e4860a29096e392076794cfcea166a30b340cc09f77baa5952c06d1"
        + "48bd89b750c3112930ef210a50a7d3f6569da89912b5e50e824116e73a155369"
        + "58f75779506d07e67ec9c0cd8de4b51dfbb0fe56926feed18ffbd83b0cdd50d5"
        + "6326c54adf97e629378ae5f0f02fcda3da1aa98cb1d1990946edec711a85a0d8",
    // padding:3031300d060960864801650304020105000421532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "a44cd265e1ecea83fc74e9eef746ef173277cc96f69a1798590ddee7ce5b5c34"
        + "a82ad58a5c042db19005e04eec4159900ea764c0d008c52b94577d1c438661fb"
        + "767902d9d1bbd6a90bdc4df685ec5951eac81d8b4dd36bceef7b6f919e85b6c9"
        + "94c7cf22a804f15cebe63b77f47b3bc2c2aaa68c6362c27a574b849efafe72e9",
    // padding:3031300d06096086480165030402010500041f532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "a160aa43f4873cada34bea5ccd2be9dce07940ee1c08eaad524a5019993bc753"
        + "ce92cccada706b483f106ff20b327b35e7c83955ad3bbff3f26ced3489877d1b"
        + "5bf285d61afcb30219c02a440da61030e301aadb901a525345d1a651a21c31a6"
        + "2ac9fb71738c3e215a8941ca9a3c4910679c5e774530c28788f6eddd7a31c024",

    // uint32 overflow in length
    // padding:30850100000031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20
    // a6ec113d682299550d7a6e0f345e25
    "1369c78f816a9baf027e255de0c258125be90f35b8daafee87f2ffef2d465e06"
        + "94af4401cc5cdc7ca78b08d5688ceefbddc02abc5495d47c6829d696f8370ea4"
        + "27e7e0225eaf22cda720bbb5881edd16b19bbf2ca86654c65b4ad481c13fb38a"
        + "f00d77922f46b311f936c51f4610f6bdb514b366aa05f029c1e63e3cfcf9763d",
    // padding:30363085010000000d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20
    // a6ec113d682299550d7a6e0f345e25
    "41d4c1ea43cb207af8bfc1552e31da7ca5744b68c4e00c3bf55f4edd4c81e91c"
        + "01f44fa05290dbaa1fdcdcc775f6032a049b4965345c16aac6994b06cda9e038"
        + "7dbff96cdb115e014f69bb057faca2f618c70a31edd0beaef7acdcc0fb7c83b2"
        + "f07a8b9de48aa04b7c973920af5b8dc20aac343251ddf4c2277985c3db1dac2f",
    // padding:303630120685010000000960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20
    // a6ec113d682299550d7a6e0f345e25
    "76bae6c330b9ab33aa9f2abe8559c51fb95f953a75e48053ab99078069214b50"
        + "9dd1b5080ac6819e32912619372d71a9ff1a67449dd699e5bc6ec0e18d1893df"
        + "b5bd571d933926d05b0d9fd7036ba4556e209369d1c57ec49cd9075e583c257c"
        + "6fd4899c2a8bbb157547812cc692f264bf54712c71ee090b974d99b4d1629696",
    // padding:303630120609608648016503040201058501000000000420532eaabd9574880dbf76b9b8cc00832c20
    // a6ec113d682299550d7a6e0f345e25
    "3480a5c22f092f259b5bc4fdb9a33c044c24a645b57d61920effde1dc0bbfe53"
        + "738023f16025841f9323b40f72c11091941bbdfaf7c2fbf77ad6626dbd6a3b7a"
        + "bb3ee916d96a922b11c86ce80ee67dec619bb98e9246d35a33b11b3a4e2a3a13"
        + "0e8b57ed4bcdd4b4e73aec3f9e3d50d3db5e29cffeb186846c72d09468d018ed",
    // padding:3036300d0609608648016503040201050004850100000020532eaabd9574880dbf76b9b8cc00832c20
    // a6ec113d682299550d7a6e0f345e25
    "5b3d3a198d4b36c6d9641db181fff59407a25bf1571f85e47bad1eaf13807987"
        + "2b93b9eb51aae09b48d6f4ef56badd96a6584277d8f3c6e4a4e11275f72021b5"
        + "0a1665ddaaa56a2a7caa7da6b4d502c5214e17042811154d411dd2197c250264"
        + "bb69ba43adf668d4f7b81d932afa55e378214bb19ddeb431f702a91dd11e23bb",

    // uint64 overflow in length
    // padding:3089010000000000000031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "1cc5577d04e34550e7f3d136064547efa30b9413e2c423b5a320eaaaf11cbebb"
        + "91e13bbe3874e4650e057a8e38c8a366c473f35e0de82b22f846721a09e3f279"
        + "ebdf54c8df395a9041333f09cb7bed5291bc1842857c4ce6ad5a1c2c476c1efd"
        + "dd5fe42824c25e0581aa7bb8f621d3b53566637c6266bb1bd0a5b7fb79c72616",
    // padding:303a308901000000000000000d060960864801650304020105000420532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "6e56d1746105344b34fb8299d173f4a5032cbce3556ca9d1eee35f8b31818efc"
        + "121a1a9599c24fef8531243016dd6288d67b4bf9fdbf2c90fba5b1661be03531"
        + "b5e15385ea465d1376010f0af761e8fb1afff7823dcef8dc100d97c192e9a7d0"
        + "3c82321d83fd8ecf67207c65cf182e1104ec5669536070cf1e3fe73c5e27edeb",
    // padding:303a3016068901000000000000000960864801650304020105000420532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "37a413f9202591b8860cd9d68515ab522ae800e9a71793b479f1fb74ab8c9b07"
        + "e72fe82dabe1189d028b813610e5e57c055af2d32837551fdb0cd93d7669a3c0"
        + "2a14c460f4c92136a4d11cfb7dcc76401bb5b699fbc64d302736d68c3591ecd5"
        + "9220107cd63f55c83edd38c4568e6f7749c0d9baebfb7c8ae1bf2179101745a9",
    // padding:303a3016060960864801650304020105890100000000000000000420532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "9fa8aac224bb50697103d457e7fc870853b23670ee5b8c7395d68ed82b30db18"
        + "ae34a569abdcdf19238ffca8f5e435327dbe605bdc1a6dd3eaa3c2beb33f0064"
        + "2984a2034bf3b3e8de3ec7009e35069d5b27253c4aadcb4f163148e157252e3b"
        + "9334abb6cf0299161c12908529f52de9416ec6218af7a6963fcc987c5024ea71",
    // padding:303a300d060960864801650304020105000489010000000000000020532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "0f50bc6b1b94aeb6805dee51c92860693de47c4925ab90b57a46e0485a9afeed"
        + "45083eade73bee684cd07048e632d1dd24aa2efc42c1f85e4fd7b7058dbeafb5"
        + "3a3d5b1cb1e7dded3352c3c92ded891839263a501afaa78fedfd04546c43d16f"
        + "7a52b800abc9ab1ef827ae0eb19d9b52def2435f1477a48dff61800b4db830e4",

    // length = 2**31 - 1
    // padding:30847fffffff300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "9dcc651cc0a1b4d406112c0d1ebd7a9fb5a2c9d9f9cffbeab2d2821e5ed01efa"
        + "9d191665794649bd1f588b729e8fba1eaa37a5a736a5863973c338a92b2665d6"
        + "ead13b72a19d2da778febb94b150e8d750340a3b856fca8b3b6e3cbfecb9c397"
        + "c23f46912ba546ab0f64ed88404ce317f8fb2278b68950e9712d6b11f5cdfcaa",
    // padding:303530847fffffff060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "0397d14205c2f52423ef69c874294dc2b37d5be5d5647f7e83f1dd6783cb41cc"
        + "e52e6de1dc8c9e93ca1ef887d4c0ea79cd8b26391d638bbd8080bce830bf1bd7"
        + "fb1de31346f28d609874fafd4a34fb7bee900441f55589ec3c5e190106d8816c"
        + "adfcfb445834739cafaaa3903ed93cedc41a76aa0ce18fb49a3a73b7b5928735",
    // padding:3035301106847fffffff60864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "2c3ffd881c1c0ce2e4c98282d6011179a89b1e84b17072bcbbb64164e5e05410"
        + "d0414a1fdbbc04564f3d80f3891f28c3f02e92bf97b4339b5bd4699614e236d4"
        + "223cef0688c44b297eb9c0e22246b4cb28983b102a446dc76671206c3b77af68"
        + "97f2f445512abda37bc9c37257dd4f1c6f0e6ec40929eb6b0058682b9d2f6c66",
    // padding:30353011060960864801650304020105847fffffff0420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "668bd06eafe953fca6a17b0da0f9006ceadb09ad904786b7530148df7eedc146"
        + "d20a5472c39677d65e59934c00227fb662b3474596e6072f56d2c00c3d31e66f"
        + "0da85f4670e75c3f2c910c0fec8c98bc31fb2eceff80350b78aec0d316e9bbb3"
        + "31544d8a3d0b1649291396c717e350bebba3d3c3a0b1d55f010879b8c7b7d4f9",
    // padding:3035300d0609608648016503040201050004847fffffff532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "87482257ae1d18d0357428b756ae35a48549536a3439ca3c148eee64f4c096d8"
        + "96219097d55c14a25eb1490779f6b1471aed238cc0d6aaf265c12ac086d04de9"
        + "b79a37518056dfacc12cb4916c17505fc7e2e6c1e0db720a286ea65bde4d3da1"
        + "d2dcb8d0276e8ce73f3f923209149955285c602572cfd24c82e8d96d45f569e6",

    // length = 2**32 - 1
    // padding:3084ffffffff300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "03aadd447f36952dfe73ae89e5c656b7d37ec92535e547cca62a7747f3831f2f"
        + "613c7dc094f3d5c4c6b9e02b21ed4626930ef3948b42ed41f4cf468d2474acad"
        + "f1c75599c5619e4872e6d3dfd93abe92234165135ed265e0c0f64fddf23e50c1"
        + "f9fdcede8778a8ca008ab00f8afa887da3f4699df9f1140953232f36d035b03f",
    // padding:30353084ffffffff060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "31afd9a0d827755352b16de04de42e98a8c72f08919ed475530a00c762b8a03b"
        + "de22634dd856a7eede4b4947d780cb3efe55775e16d7f46f209dbcb5569b2d94"
        + "69cc271aa850f74960f7c741928055925349821e32e1e0fe5a040010a39a4b6a"
        + "343f7f35c204106b3617e528a99dcaea8a93766adcfe7be31cdb98f7f7f14669",
    // padding:303530110684ffffffff60864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "16ac0aa2d727ef5fbf0305259ee6fa40827c92419f819673fd64cc2dc2dbfe7c"
        + "e1cfcf06e26d45f59cb3d9afd30d7a6265863fe856e0a0b1b9508b1e7a2dfb0f"
        + "87f5ebfc444bbdae504abde7daa33bffb991551940df682c8e2c45edef0563b3"
        + "4d4f11e1955e83c2145ee321165517d1532abd64dc613a280fc30670bba1f898",
    // padding:3035301106096086480165030402010584ffffffff0420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "0fe0c75dae62462e66e7277b03c9113727419f7d4db7b2a567c0c189fb6328e1"
        + "f73d5d44e2196b436f4c2f0f12950d419774c8a51c55f9b2217f904c4f03d5f5"
        + "754174719dfb85f62795ef75e6d54e703bf231fd8472250f529f85294f29f6c5"
        + "653ef585079c3b3d8f931da80a46c8afeef37696fb0e7986d413bb1996b8ad57",
    // padding:3035300d060960864801650304020105000484ffffffff532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "9ef993e6ccf015b0b0de75b51213a1c3efcaf66bf83655287484ef28d9848062"
        + "26a7af1704fa6a7fc02984b44449f83ae24761021e49ba6117505c1e609406b0"
        + "02215de27d696643c3354fb48e6c64e7300944edaeb96e4872275f75532f5aab"
        + "94358d4954522fc7903439e99223d8124e79a3f519050b6b576b77d5abe7c3e3",

    // length = 2**64 - 1
    // padding:3088ffffffffffffffff300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "97c602416f2131d34f2a57acecf26365a30c12f77e5beac095533848ce227302"
        + "092c6f44b47f011d6eb0a91f8024d1935d8bb274c42b57875115a94281fd3cb1"
        + "98f9334758d3200c1c721f6babef332c02a89968a7089f7783993bdd54f809f8"
        + "372437798d2364040c1faabfb00faabf28cd6ae4ffea29ae2c08a6a7e6074700",
    // padding:30393088ffffffffffffffff060960864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "2a970dc291a1dc935cca6985dda703bcc1ece2e40817ce8fa79b6e8fe84e1136"
        + "86e6e65570d46bf22147bcbc389cb5f86f92dc185f556d15e7614cef119fcd73"
        + "05a31fd2f8710812f35f9f0bd8a1a6e5be3163de644370c67181b7575635dfb9"
        + "f717f78631d62db714b2a19cea7079ff13c8926ae0c601e4befb6541b02a7e20",
    // padding:303930150688ffffffffffffffff60864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "6e16d110235cd11e32b114ca9dac0cd6a1b041a6d2c61941d49bb458241281f6"
        + "2a4e2b1bf3cebc3e67e8c062ec67a51a599a553b09732e23e1d09fb2b20be7fd"
        + "311a7122414d535651718a1421d4239276c227b96506729a09e3ff2779dd1c79"
        + "de4d402623039b826e2bb4d26d1b56775fce14ed0203a9ebd8f042d981705a77",
    // padding:3039301506096086480165030402010588ffffffffffffffff0420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "0716d252488e08f10a25cec94714e6105bd4e13ff019431190864cb0f4378d31"
        + "5f4bd0fdf186e1f2d45a6e97eb04fb2013273e178ce4f82a0b67bf9d021b1d8a"
        + "b73d753adf2073ee1ad6190b2163139db63778a3670b7cce23f45efb601bd596"
        + "44a431cbe534ecdf4c4c58ed02ed03863ee32d296b5736c010305fec655b1a44",
    // padding:3039300d060960864801650304020105000488ffffffffffffffff532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "03e52a2ed638bfb9184a0ee3698502af3a19bb959a984957de5101e6f7a62ccc"
        + "c2ec2a6293fa9d76fabf3ce7e4bf35c65a5f864bc003686a1e05b57c5af6ad58"
        + "8e05a5225479422d7b78c5bedddaec7f4b8c1e9ab7478c1ee253847324e02543"
        + "4b76a01b82a40123ab31ec9862c6016885dc6cbfe97801503369fd3688bdaaf8",

    // removing sequence
    // padding:
    "5df1c4a701c6fc1f2daf6f4538f29c3452667424c05edcbdaba4a1678c8b5bc0"
        + "e89656a0e48aef46642e0bb597813688904e9d74cbd377a3d9d2c965bd3ed06f"
        + "136f10367ea3eecf89a97508389448a31ae0e79ed3725d0c4e99a516daa41164"
        + "79bc53da5d7c2f26c7ec6310d4cb4174bb781405630a9b1c147b0e1da3a7faf9",
    // padding:30220420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25
    "3e43837b92ebe4df08586fced3dce46aeb2fdb6ec2bd0c58e823f6e6363b9b67"
        + "6786929d13ede60a8d8d0daaf71f0de8880ed0fdac8706eb2f324394145818b6"
        + "41d1049cc7552bc6273d86e901099c78297381faec5c518fb6de429700f3bbfe"
        + "f76cdecbb60088b9f2a77d75b8ff86f06cf23850e3183a267c0ea34f4f839015",

    // appending 0's to sequence
    // padding:3033300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250000
    "26d20fecdcf0b7d6a0472754aecbe115c39d580ce9d78b67d1a6395aa6ce6689"
        + "bf6d0d96545341fbf04956a48c47f7d30bda017acb1d8e24ce596aacd3e05b1a"
        + "fa571d19f5316142557f765e4c5d080bc5336b79e2c02d8833d076ac9d7794ff"
        + "be85c66d0db97e1f5bd2ecb46afb15c19a8fe083fa593420e996a483c2a3a766",
    // padding:3033300f0609608648016503040201050000000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "1163082ba8d48352df7eab96a0067539faff24374a630aa4393461a0aac71660"
        + "6625d706699dfc22cf3aff89fcc278f83a0adac87aa0bf192dd86a97031515de"
        + "1933a23849478ebed20e4203abfb47345bc18f38da5d45e829997b10107c5369"
        + "99b2ce10b2781e1db03e10cc2bdbc2e0ff4c3db5d271ce83c1e7e267e7c1e107",

    // prepending 0's to sequence
    // padding:30330000300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "0ded592bef1fa809841e0d7365e66af12f4239be0928656e7c49a043b9f2b18b"
        + "9bd2dfe93a810c6e6c8ae6cb8a5c9d6e9d39a96a10b3bbdb92a7b8f575c2db48"
        + "41c1b628160f956f54e0c58d3b6fd4d640b0a06d39476daba7be04b63a75f38b"
        + "bf7517d9751d2b12d2dc00e44de7263275dce6b0c0af65d3c04878d6fc1be2ac",
    // padding:3033300f0000060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "95a42e5d9bd9ad5a8579444e8167bdecec16116a7900117b298c82d5560f1d16"
        + "e9fbe963764727fef9111f2465e66177b576bdb8c70a58e3df6ff69edd2d6827"
        + "c97d626b09c24cc49f223cd5d2db2916c54fd8f2ac7301723449b1823f2ff48c"
        + "56849f7d608312d4bb7a97f90ba218f99cb773fba0a34909618f5d25854d7687",

    // appending unused 0's
    // padding:3031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250000
    "2344c598a8905b350f20de5cf0cee60253729a54be45b0b19acc109ac15862ef"
        + "ab2e7c96e92bc990ed6959a40d725c24c25c8d223a46f490905c1448d8dbf7c9"
        + "c427bc2e896bdce6d2c1daabdc93ce177f9525ac69d899bded12443338834a16"
        + "d885456057461740c5140cb9a89a017851f9e99e38c1727fe5ccad9a7a8709d6",
    // padding:3033300d0609608648016503040201050000000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "a08cbe4009080f73cef03116ea949d1dbacce7025f7f61040fb4e052754d5b2d"
        + "74c2dd06c0dfe1d09b97aa5739c809bec6d8cb27e852e9fef353bfa32964b994"
        + "95a6dc63d6ce77460ac280c74c0cabdef794f74930f7f8827af1c6690d22ec2d"
        + "f3af497837bbe900a890e3feeaca2c0d16b0017155390ff0396a35ecb62b5992",
    // padding:3033300f0609608648016503040201000005000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "500df36bd7d0b56642e2d5dab6e4ec0b148e7b8673cfab40e45c5dad5efc469b"
        + "3321ce027a3a7ff5689366a18a32267d161a1266491b055f11557c35bd0d4f43"
        + "df11b8a26f7b13c54be423b87b30b1dca956151c3ec3df03b30918a413179b0e"
        + "064bf434736b323408e3f1330743c8bdbbb9d466dc1e21710c12e2e3b638b172",

    // appending null value
    // padding:3033300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250500
    "11a382fe570e0cfeb515955b70ec89a9353cda0c5a5d3cfa3e16e41340eccaa1"
        + "8ba21ad87c4a54a7131c4a7cf9afed68b1c1645568bab9b0fe7dfe0437abbe1f"
        + "b6cf06bb690f46aa2eca034093ded661c38954341f3f35abe484015150307eca"
        + "fd06d4309836771dfe29bfe56350d68725e0cd02b1479c6f99eeba2d59f40626",
    // padding:3033300f0609608648016503040201050005000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "110f3f188df46da58cdd46b5d460ba3d2f8d00d907289634d52a3ce693eb232c"
        + "d6db738c48c8aa22d923d4f81d55925b3d4ff29ad9869f97a244d37b860cbd46"
        + "46c6318c041729a7aaf473b61a93cccd62fe223d1be00364f03d722f43c7beff"
        + "98c3fde573e7e6a0ce7d4a2a4bcf279765e29769bd4f884ce41fb808ac3d541a",
    // padding:3033300f060b608648016503040201050005000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "470416ee76f0bbdbd2812b533813e4463b799f4036e6955f3e174f6287e3c73d"
        + "57c32875607e2eaf06d612cc85170ba5df31286edb645ae9ceb9e62064050f3e"
        + "7f6b36fe8fdae7a3bd89b6acc523c923b9d3f3e5f57d80c9100b39dde75caf46"
        + "adcae56668149ce0b80762bc459ac598241dd79c6b4fe0220ad53e3c591243fe",
    // padding:3033300f0609608648016503040201050205000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "100714ee0d38c541c2632e96885a7ce0afcb22f0cbd84c556f19d1b44bce75a8"
        + "fdf141e975dda1812b4465050d4615a51c3b9816606c7ac88d6b684df938e7a8"
        + "852835dcf5bf0ee45f2e413290691832095af77eef0e7a86f72167dbb03758e6"
        + "8561f7f06afc6e902ba19fad57e00cb43c0fb2a5ead689a146c79c9e6188bd85",
    // padding:3033300d060960864801650304020105000422532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250500
    "44eaf5ded57ac5c25c17eb31c2e071400b46b9022641347b2edb0b14efbd4eac"
        + "5f71e4bfbe791e164c003667387e57ae22c6b00e69971d7245e381f6459e5f88"
        + "d9dc0fdb385b777fe99e5e4d79aec057e41a1e457fe2b91a5f4a8878d2eaa1c3"
        + "ad8393d281eca07ebd287364a19045029fa7ed0e62a21e5e42a88a52ea4abc8b",

    // including garbage
    // padding:303549803031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "236a815c2441d111d254172149ab2429cc4e6caf3335579bf438f22723de0a4a"
        + "5e532ed71f24c0fc6032c60aebb2b7e76cd0d14f262d1d9bba80a53dbdb12c9b"
        + "89902fc5f5511125d21b7df32e9b303c4b393fd6add6ac7536901ea8ae5785dc"
        + "fe90e85ad0c16146b1f15036c31d7758a364fb54cc1d183b8566bda592ba446c",
    // padding:303525003031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "762d30b302cd76b021e237f28017e48488ff3bb30ff9e92db5b1e76eec2ee91c"
        + "9af03e1c5038afc22591b1cd8cfae648a33ab77901f9f3736e50eea83f7c7a45"
        + "46dc55c0265fb17dfdd30250fa3881e34e51b4f2e54554ad098eee952ec888e9"
        + "11a0ea5df42c0560bcb4bdd718c88d834b534917e555c38fd1ec3593b2f25b39",
    // padding:30333031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e250004deadbeef
    "8cbf9d425abef67ff0a7fb648e70b82b1556ac80e46dcff37145b9041bee2bbb"
        + "fa56817e04994c9cf1123c6df2aeeb1637595eb1e20adef51d657943fd67826a"
        + "c5d5dfba106ae9cd243f12746917a446ce955034b46ceb0f4d542b7bcd06ad3e"
        + "6e10899d5338e6d8caf3d4de3cbf45d45a58d946a64d0bc13e97a4ab4e6b6016",
    // padding:303530114980300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "6477bd3337d601fe92e19e7f6b216f73eaca68aa408c5a570876ad8db6113505"
        + "43d1dd458b511e3095e57996ca589c00f2beb6b6fe4564f4373571d904958acd"
        + "1bcd33f57959a231bb126bb2b37bf1403d52836752198b6954567f07b31ed110"
        + "5dcc50004e4cd7e897516c536c205b339ff0d35463ca6871ea5dce7a8daed8d7",
    // padding:303530112500300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "975d07b7295268a8662aedbd2b65b5eb10bb496077f41b90d12d34ebc7e492f0"
        + "c7f3a41d4164a279f06ea616f91968628be4ceecd4a554477bc76cc6b2e6bda4"
        + "042dc253327c4b8fc40e9242cbc8b835114a7379a3081bae4b2803a99deb4a54"
        + "0f8c149ca5db3a61c7bc9f61cd7e55521660a06603849896c791a18d1c7360e1",
    // padding:3039300f300d060960864801650304020105000004deadbeef0420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "37352cd11eb5ff7380bfb7c0d3e8d9979ae7cb489a71c31a077d59496547b0c9"
        + "5a760387ed50eefde0b762222f05a6033740f6e010693edf3ef8ab5f9c57f4eb"
        + "1f6ccd83287dcc2e90857defe5ba4109bf79ad84ab069c85a25758d22536c688"
        + "2919245fa2d7e7921b3635d984deeb6555cabdfc46a42c75875d55924c8bac62",
    // padding:30353011260d4980060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "0c783603c7af53df27538b983fb7368e9f62d4552f008f2920a21cff3186d2ac"
        + "7de451fe8c28b71d38c657c41c79174c84004deffc69e69cebf2aba2a43ccbf0"
        + "52f6fbdc3c9d3683275913c4583dced686291bd1c0217a015d9ce732eb410c8b"
        + "27f2fa7c9ff516a81577490f5bffc8121c7ac674caa464956942786c5dca6b4d",
    // padding:30353011260d2500060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "1fb1960934683292a4c92cf3d582cd5fe68888a5b0f6c2e64538289da7f96a9e"
        + "fcc36bdbf1fdc0cc0b3b36c6af608309de58c6151112f3a78599ade4a718b359"
        + "547a4cac9a020e5e7e7117d1bfeb3ec21bfe9732825e624b27ddf8a946eb858b"
        + "30461706f769a54b0478e0753388951d98129383590186b80836608f7e06c72f",
    // padding:30393015260b06096086480165030402010004deadbeef05000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "7e4f953b288c20fd5bec56a00745db9be03590efcb637e2ce2119a0a1846e9f3"
        + "8c0ebc5f2498ebde6217d81c9939b6d6a6f35ba54ee50d6313d3f2579751e7ae"
        + "8d31ef4b0e99ca2e96c80459a7e5ff51f6f31e9c965be19097de13017c90037a"
        + "a482d197c986f50bf2d5e1acb3f3024605e46d963410a4a623c898d0d773a78e",
    // padding:3035301106096086480165030402012504498005000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "03e121f5766771a65a400b354f78ca27c9a04a25d4ab80fcd6ee91bbca3baf27"
        + "752abe5cfb7959779644c064bdbaede14847fc035a3a19523d83cae3c31a64ef"
        + "7538805e398e196ed8ee9ef6b3f58f10e7e16c95495f82ba430e5d997d165564"
        + "44bb1447ebb17829ab879e61ac297ebfd4b94aa99b68b0b498d8e434d4fb3c6b",
    // padding:3035301106096086480165030402012504250005000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "90a5d10e2e19f7e016d5126a3d3eb91432611ebfd411b07a4be15aa48c39df33"
        + "f3a2855f1e150ad34c7f83973bd73eca6575dcbac4086aa0a38db3d6e6ee2e9f"
        + "419768493fb4829f1f6d67f80359f82d95483d6057de17fd388ae46687c429de"
        + "a4d9f7a286c95fb1b9df0f1ba40a4263307789952b1bd07cdcb3b5cef10d9d2e",
    // padding:303930150609608648016503040201250205000004deadbeef0420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "a88d38e8c765b7e439f42294e71c1689a318ed5414efdf474196989829d4989f"
        + "ce8910798f4d7873fb43d3a501fa15c8019813104e4699597246db66f96c838e"
        + "45aa3596a1d26cbe9f6ee91c077422953b402f7e11f8768a2f132295bff79a0d"
        + "10ab843cbcf2c921113992336638f4052446f52815328ba4946510a6b701d448",
    // padding:3035300d06096086480165030402010500242449800420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "34508ce63c502b3f8185f7cc61c724185aab2c1bac68cf2a7e7f4234edcc0e38"
        + "cd15f73e73d02431c62c28a2241f629382ac5e9329ab71dd7e9152b10bf86b55"
        + "0c855aade6a5941ffacafb4bfd57066bd6e39bd0d8ecf57ad9a6f3ba48831800"
        + "bd8e6e9773a0ba3770cfb9ae329bb4451f450ee35796b5578104b7ff5ae2dc31",
    // padding:3035300d06096086480165030402010500242425000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "7f642b5702c331dd76b7ff66578a2c0547d91c556b7b9751443d911729fb5ce8"
        + "426515ba068e2839cfdc956eb813c25d65a2d5213b59302c0ed5e6fb95c49002"
        + "edb1605f8f622912fdc309d92e6e3f188ba19e991fab0a7018ae4f6e70927d91"
        + "cffec51b2dcc8113908faa1173ec9ed72350aa93a8cadef8bfa7305bae22bdf9",
    // padding:3039300d0609608648016503040201050024220420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e250004deadbeef
    "0abb75f2fac084da0b99bc823c021c4872e23302a6a25e400b6f25d60f7c9038"
        + "99a69dc548676106b44f37c1e6d2604eb995a16880a2a8e2cc9e0ccb2b984ae4"
        + "82036f69a6ad31a2b5836e73e0d30c3e10f8b93c7587d7c0f2371183edc3b8cd"
        + "0fd7bc325b1cf75e1079f8d6df53fe495722cc1ce707cca49bc6f4ed2ca6c4f9",

    // including undefined tags
    // padding:3039aa00bb00cd003031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "9f2234b108a45abaed850e19d2f9576f59bb83dbc6165da61c4798638f9c9858"
        + "7c7eb92a8c901dc4430e4a47dc05681ae811ffcad6f7a604c43551cd0f5d1235"
        + "49435d622f7efec578301efd49dc6b139abbc3c7d6a26858f6d18f09b863a145"
        + "d6483c9efc6c322fec1341b6362dc1d752c714efcdfb09097a0ce6df7dbe88a9",
    // padding:3037aa02aabb3031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c
    // 20a6ec113d682299550d7a6e0f345e25
    "24ba137a293599ab7e50a0a4f8c7a5cd02dda6a4568c93f84d00ff4729656456"
        + "3c9051b334db2fd2c081b23d322d4870a61b2435d651d7efb4e1b0920e759f7f"
        + "d81a937bbc85ff43dbe2b702dec3acf4db68d5fd7b8a2f6d32cc49a7300dd659"
        + "623b391927a2442d69c6c3c29e59eb80b1d0a95bec6d18a6223cf4357eb7cc96",
    // padding:30393015aa00bb00cd00300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "04023dd35fa479f8156794d02935f8669c023c774b95c5a0e02837e32ccaf7a4"
        + "ba5195835a15de6a21796eb96bdaed868f9e8b7f0a5a21c1a3058f53aadb62d6"
        + "ee74cd70b2c38f17e42a1f7ffd88955731b4e15368211ad53f617aacbb54a7e7"
        + "078740ba6daaca81c1b321b748ea1d13f7aece490226636ecac41bdc275175d6",
    // padding:30373013aa02aabb300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c
    // 20a6ec113d682299550d7a6e0f345e25
    "253bed76e4b8465ebfffd1b7214ce586294d3bea290517ca2bfc417ba9d8e72d"
        + "286570c348dc6084fd379c2bf4dae424189964639533e17c409ae18e445210ed"
        + "4dc98de4ad7336554740d1532d5010a1bd7ebbc33ba48a3365d50669e4f4522d"
        + "0e5ff7a3bdb1c42c42dee647a8a3ce16633eb33bbc0a869e12cf99f9481dcf85",
    // padding:303930152611aa00bb00cd00060960864801650304020105000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "0775598491297eb9004eed66234ded82e047ea2f06837425e6bd27f33b137366"
        + "7f3ff4961d60f85edede88ec2bba2680151da3763f0df9785b31771da7e64386"
        + "2ff9ba944ab54bb1356ee113e420002a873f1eb381660f3eb84b1d6b25ccb8b8"
        + "2ad12ad0a449c4de205144873329e80ae8a84d1d3c1660b3303cbef28b48a553",
    // padding:30373013260faa02aabb060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c
    // 20a6ec113d682299550d7a6e0f345e25
    "a73df043d06ae53a37773016a4e21d3f1093c50e079b189c4bd7db3e2e9875b1"
        + "4e5374cb8e7394a9f1b45c7e4e9dd516198bf5055b30ea4d205f39fddaab3da0"
        + "cec63524bdae2ae166a3874c59057d93855d6e6314fc5da8111ff58666a73c00"
        + "a105311859f27d2fb92f507531b9d681e219861e4f0b2b979c185af2690eb4f7",
    // padding:3039301506096086480165030402012508aa00bb00cd0005000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "17e5a889b8139593e192f7af684c918f2751f157214863f88984ff3d8c9e381d"
        + "1bee5ee788fc82869f4c3d8483e3c17c873a850a7a5c85e4518cbd8531b331a3"
        + "08a0368a868bb7995ce0f8a7ac5ba53b88c31c958dfabb36ed461472505b5984"
        + "18185b864f381342c29dc80e55ca7c2095e7788e7e8d385d61de605f74e431b9",
    // padding:3037301306096086480165030402012506aa02aabb05000420532eaabd9574880dbf76b9b8cc00832c
    // 20a6ec113d682299550d7a6e0f345e25
    "a659f7c44e4589e9f6658b0b57e82e65d5ee9fbe2376894f558a7ca4b6e3c503"
        + "2f953d1dccfb9b76bbc53dd5d1a52cfc092c6ca279b37c0a43c99ec0553d7ef4"
        + "d9bf9361a1c4a3fb7496aa58c0af518312e18819fffdafd1a230a38440a6fbb0"
        + "e69babaa977b8b5fe08ed7c6d59c0391ccd80b42a0c0102264b0ed6af8524e9e",
    // padding:3039300d060960864801650304020105002428aa00bb00cd000420532eaabd9574880dbf76b9b8cc00
    // 832c20a6ec113d682299550d7a6e0f345e25
    "83fd4599a47bc0852ee1a12b2d97fceae6d8442fd089df1d21ecc252a4109824"
        + "10bbd2cc6bbca219502c2934ac593a09beefdeb54b0692b3e5724b79b0f5c535"
        + "41b62b0c4bf80a658af71d5964fc6a1fd7823370d00e24dcead4bdc86bcd883f"
        + "e3f48dc7f8468ce99b7580306007021b68b48ace274e3c09a1b5e21fc7542ef0",
    // padding:3037300d060960864801650304020105002426aa02aabb0420532eaabd9574880dbf76b9b8cc00832c
    // 20a6ec113d682299550d7a6e0f345e25
    "4bab6fc6948143f8ec7c8ad86a0c5cda5bd8151c24ca7916857778729c882581"
        + "603363fde0ae2a28b6f8f2c8ce8d5f6b6e731bf8ef735bd31318069544295b54"
        + "b04ff2abd1e11900373931164586d7c830bae704f7314eebf1d32b3a171274ed"
        + "456e335d2a0b998ac441053ef096a037bfa6e5cdf3835c45ede383f0ee8feeec",

    // changing tag value
    // padding:2e31300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "5770bbbb883f93f7c29bdab32e496f2e9063110fe648705fd0b1dc927052fc9a"
        + "ce9b36d898d19cd4f862b777b7c790d767b8313f735ff567c34cfb31f2964454"
        + "0645beea182cabdf789ff9ac3f68cc20444af0b9d4ec0bc8992945063fdb733c"
        + "ccef7590a10bdf491bc21c38f25ff65a581b40343e30529c3dbb71f62189ba3f",
    // padding:3231300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "359dfbf40d3c2383f58bef1d518abe9852ca80d797393e4e1a9380ef08aa851d"
        + "585213e8897c6f701ab680b0f63ccf5ea4216331918ca9a984fb6ba549f4bd06"
        + "6ec1fc4f1ed053fa5658b01df674a21322ba7e21fba6cbb3a8eb5565fb7bc269"
        + "f99c65981efa650dde613ccd6d3927cdae45922d94dcf7ca5188bf5acf84035f",
    // padding:ff31300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "24a61067121e9b4363b816b7c5750584c23f3c3200ca929fdedbe95d7504c56e"
        + "a7dffd762074e44e96e22147943f2b704003967270b2be1bd1baadc3861c4cae"
        + "91bd41530c67220349db4481d324d9927d52fe85618ddab2598996c5813f3299"
        + "e1afb020b24003fa94f94a0c6c02b3183295e0de79eda021dccc5539cd7874ce",
    // padding:30312e0d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "11b8d1dc2fa2afbc32f048d7454ba032b432a2ecd438506aa72c697a5c118e9e"
        + "231a0c6b6340b5564402b7e837c59dd36f726fd626621b8f543964198484087e"
        + "ded70e7bb1dd63df2cea33198b9d02dd28e3b8bd006ba991a8b3bf06ac928bef"
        + "45cba2362f2e11a5fbfb0310e84e8b7ba1e17c315adc1f34519134c36689619d",
    // padding:3031320d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "8748f029d5294dc917cf4fb347e0046f903c088fd976ca97b1322738549df7c5"
        + "6cd67349d66596338fe418b29de9e8af8872fcdbb55e1a6f74e9965fe7a365b8"
        + "46b667d0ae50df23083be73cceb59db545a3e1a560f6ce0e9eaee57b5f95b848"
        + "7a3987c00f364d0f148ead6d7e6a37b05456b913b7a79c0547b80da2a2893881",
    // padding:3031ff0d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "2bfc087003f3b98f0c8c5273de34f5e4d5047e909cd80e222072f6a7926ced5a"
        + "e169131342640f2be11bde2f7565c3c63d0335614dd278915514de8421f4521f"
        + "0138109a5c9778f86647b8a42815b6b861f173f5a6df893873f99c5e62bc3c08"
        + "6150e3b7d7abb943ecbe5806068abc433e9052d9bdfa19a58d19da463dbf3b23",
    // padding:3031300d040960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "0fe03eea6c50ab664bebc7d64346762aa29b08b61f2877973cd543c9533c9d04"
        + "51db8d836eb46e8d64283306efd7ef6387cdc3c794f7474f2e7d51b9df078095"
        + "adc85fb810cae52434c9cee5048fbff72610778397fd83204f44bb87f7637373"
        + "d111dd16e18287bd9ffe816683bc3663f586082fe0811ff6a06c0264b67f7716",
    // padding:3031300d080960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "748b9e64195314003ca31f726bb3d3294abd8eb376365acc5b1cd36934bbe1a9"
        + "bae99ceb7c1a40c910bca6007ced7961ecc9ac74c7a6424cc87b6b9610320ab9"
        + "c5b527d986c6e8ed21e677bbe2ee7752e2dbcfceecc2dd6da3f6c6b9c81435e9"
        + "e060dcd67ba834729761dfc9570b79bb1b8ead7bc1325c2233e445eeed12dcad",
    // padding:3031300dff0960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "7379bc96dd40d37a7f8e58f87c10fb94f250a964a55b2abead479b368e60e442"
        + "e6eb864952308eb45eef1d318b6a5ffce634fcb886dbfa062060b9809cf89a09"
        + "a26fd334ca22a1917fd219900ec0c68164c308cb9cbca3fb2b89ed8637c5540f"
        + "7a5886ab1e52c503e20edd6316e41c746e53917e107ef5308590800ad378ac97",
    // padding:3031300d060960864801650304020103000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "4a2478918565be6b46fe61e5f66cd1befb7a3026b5a1502e9a42636b0b924a02"
        + "e85d7ffdfd8671b1d6d3e604e3ac6a5302db4e0ae0975d0661efa018d6ba0c63"
        + "2a6381368dcb75926542c74823a8c6d8732619764d5a61062fb3b17ae243bd69"
        + "1c97c8f9821af9526abcb522ec8e9dca32de1989e576e336af9dddc3e766541b",
    // padding:3031300d060960864801650304020107000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "5ec392c91cc165ae59013337e7f7d5f2f9b3a6d45b6f6beee6dbf93e7b960790"
        + "0f4672555a57de6e9e1aee1fc9b7adfc0dc00e122e84b0233c0d615dd0d79764"
        + "fdc9d1b0e541f2de0083ab479f313a07f55f51390d1c2274858b219b1ec0601b"
        + "82a2f7648ae95ec17099067a173e3e83959b6c06f149af0e4610761aab5be1a5",
    // padding:3031300d0609608648016503040201ff000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "6173aef07a7057c3e97f6b7d4d7266918876f9fad86000b4c8ec7f83ee491563"
        + "115b0cb5d580df8c97feb0d95866eabb79147926f5395c5189554749f4a2c75c"
        + "0d96325971635be029062e1f27536c5041bb42f42e1fa10e21bb8e9a2e2502f2"
        + "a7299dfe3bd8720ecb8a57238056ab0eb546de8dc0e56b317c73ab1e19772596",
    // padding:3031300d060960864801650304020105000220532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "3b80c72f3b7ada8b38b30527bca41180b4a89b066f44a17b9df5963dca46517d"
        + "9160326afee7a34b650b9e7746e764958ce6a0a6268481a8df40e0a95a81ab0f"
        + "0bd20c050becfc0c4b03ebda19749a4a1dd3ce925fafd9a4006a835eedf221a6"
        + "ceab6aac6bc74f743fe171ef8c01935f8901e1ec9ff6e33ae8311851fa14a65e",
    // padding:3031300d060960864801650304020105000620532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "4b0eaf3ae1c7a3322dcfabee0569aaafba51e0f34fa6afc325bacc853ccd2daa"
        + "3dca56c918325bf553af02ddd19fb597c368dd18892d52d9e935dc51d38347eb"
        + "ae2a7f90c78504355f6899ab4452d5f51d2025381d81042a08582dc50bc10782"
        + "46ee69652043bb747969a7450659e333193990f34a8ce3f036221193e700489c",
    // padding:3031300d06096086480165030402010500ff20532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "1fe7b390bcbd5bc1904e676111653e14e581e7817b45294bb790e4e62f3010aa"
        + "aa77e246b29729f2b7da65a2f437b8d9c4fe3b26baad367a19fd7b1758d04c2f"
        + "788c45e5309a833522b46d7255dd5ef70ed006ef966aa7c648bd0b893b8e1566"
        + "961c16e9554fb729ec81819f1b3da890d413a153f487c030c7581da9531bf134",

    // dropping value of sequence
    // padding:3000
    "317379f37cb7f21fd03259a27db3575d491a248df82e67b39d4956a1c619094f"
        + "cde001544f0fa70c64dc0d0440fb21d2860a20a911cbb397792bf3eafa5cc050"
        + "e78b1e7bb29d041cfa0287bdf54a90a7a8bff5c870e898fe34bb522477daf8e0"
        + "03bc22891b789ff215869cceb92610c4b03210d19506058d941e6fce7a3cd786",
    // padding:302430000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25
    "2de802ddacb7e47e27875943d5098419bca3b170bf74f1c4b4a8ac420d4469d9"
        + "aea97592fbeaaa1dcb5fd20bb97afc5f7abae17a9bb85c5490db97010c5217c8"
        + "8f9f52b5e209cf5fba5f0594f4e4450114dd0348ece336870a1333f7660caf95"
        + "9056ba13b77d35239eea164ddbc8808f8e7e1beb070f551b6e95f90d5bdbd925",

    // using composition
    // padding:303530013030300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "09342a8fb8402b5e50fbf8c5d1cae415ce02c0a803adfed88188982129e84809"
        + "18dc21616bb5f8381e8dfe13f63234090c32e542a005df70df5e8e00dd2a478d"
        + "10fff1b61efbdcf0e410236f7c031c9a5f7cd0db9098f8a32a6a49f408e72c4a"
        + "29b7d27e8041ba605bf089bbdb9777e19b31ecca0d49b90d54701721af79cf3a",
    // padding:30353011300106300c0960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "3ef90c414a64601c538c286f2c35f32445039799b8c266eed605027578edda79"
        + "6a409d905a751bf5c1cdea97840437fa82733d8f27efbbc05da732887078a8f5"
        + "47bbfb54607a54f893df7dde0c35c45f9c2402bed0405c72e98175e5b9d6f902"
        + "24e07d12e8c1bbad2fc8b1a14c42dd5fb7e554db5edae89d335705c672cd7b55",
    // padding:30353011260d0601600608864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "3ad3e4ec3636b5eb8aac2161c04d228491ca0d9da2abd69d8904054373940b39"
        + "b5c025c011c9b9508a25ec25b24a0837cdd6a27cb5c8ba3683d90ba5912ede9a"
        + "21f2f7e851dc49dfebea8807576be703a6a87ca44c370db76812b9929a54fb8e"
        + "2259453ccaf47da1b8ddc5b7322c20197604b9e028ec00bd7eb48012274d5b81",
    // padding:3035300d060960864801650304020105002424040153041f2eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "778d93be708d56defbb6dedcfec2a917a3772b2810e26143db1f9d0f26c4fbb8"
        + "de8db5818aa32ebb2cdcd7960e593ace2c3c3eb682c930cbffcfa6b34438ee2a"
        + "786a9707d5d10902f7f4d8fc677106275fcb6cb08f56f341e0f52af590e0bdfa"
        + "2f2bf95693265e87f5046bcf3e6de34810e8eaa479f3afa2b0a98b175007c209",

    // truncate sequence
    // padding:3030300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e
    "a3e0e0cb9f7cdf8a2b95139f7c475f274bb63252385f62e66f82158f429e74d8"
        + "3df9ab1040717d34b6a5e009b6ac95960826ee83bb298ecf900425ff03a8f156"
        + "053b57eac6086d61dd3a8085b84c83bebbe3270164e3147ddee8966a02679640"
        + "1fa48da70f5d949386eccad26b0016543f3f90c8ac2874100dce13f03845509c",
    // padding:30300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "84345c9d3de7b5da2156d3669a731c4baf6726c4c231bc8bcaef950d7ac37ca8"
        + "d86e9c9558404f313de3fdf09024d25491b0a933cc3958033210b1c4f90070dd"
        + "d083005873762566ff2cd7f6915b4cb430f5e7e1bca8c2ec32b4ddee48aba667"
        + "f9d614a27c3bb40c6cb7f0cd77d3d17257f197974d1871cc09c9583cc6af8e15",
    // padding:3030300c0609608648016503040201050420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "227aaebca262d2189c479ab46d8715a34100bc1975c2d3991a4ade27376f0687"
        + "56cc9d89e903713bc28394d202d81b32126d7eb09154261841227cba6ea0a60d"
        + "0ed9302f816fb4dd241dcd2d746d5c1b068c42c0b2bd567ef799cbfd0a83e8a3"
        + "0c4fa2f7296dceca38c36ab597ba992f658ef7955d32d38847870afbca35d836",
    // padding:3030300c0960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "a4316d438c7091b3bd5ec09aeea9095cb5046d8f08642b087c34985c34377bda"
        + "fe74285d00862fba20572ce7a06dfe62b4fc08704d1cfb161cd88478e7e1c545"
        + "1e0bdcce0fdd83c0e37fba5168ae03fcf4ccf60fa12c9b0acb39fe99b06933b9"
        + "e0774f41151e0564ef805144c0cb76101672c287912197155d91bf036e84d1ce",

    // prepend empty sequence
    // padding:30333000300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "361f80a57ddb48796e50b3e6467cb00a9e1e193330ecd2cd6a31f649b49eac27"
        + "e295450efe03e09e59f1829cc661d36b0fe904602c644aad7ec8cb2ca3099078"
        + "b6d4f7b9233dc159fd1a6189451fedbd176e436f6605f2b889fc7197ebb520ac"
        + "cd7f90e543da44453c7ba1948e83e31f5907d1989d982acbb348ca2216fe050d",
    // padding:3033300f3000060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "674c01596bf71fccd36aa81b000be007f6cff713e5f6ffe58b25e790f9a1f654"
        + "2ba3f68e1eeaf1bb1ac6c3d55aeaf08140f6cc3d0474f6bd87ee442568346553"
        + "ceb34efb5301a4d3a5b3f28a5fb038ccfe8444524d18adfa042aa1685fc3a5f9"
        + "005da5688853b8660ba74f0e32c5be38c743b0048ca9b9fc19a35a5ff4e2c48f",

    // append empty sequence
    // padding:3033300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e253000
    "07ba2b5d519b1f60dc455d6ad90b4135cb45c5da5a2a2c9b8cb954165394a0f4"
        + "0145ebf2b1a3ff1d47f5031d542d25041fe9b6d78aab623c40eedcd846761816"
        + "8ad02af8a696573c5c63cae0b2c26583b0240848d663fdd0195322bc2c8dbf9b"
        + "5db2ff9cc3e75e70480e51da0d6dd402fa87772ddef5256467205cf41a42d18a",
    // padding:3033300f0609608648016503040201050030000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "9fd302307455d4e946c1ccee65b0941c3550c823279cc52c4f29ecff72a12ac4"
        + "0ef6b7e37b7dd774b7735bbae89b0792908bafc47f0b0a11637042fc8541b346"
        + "151bdadc3990e64b6d1807dd0e7f9266ceb3f686a9813341f835562d3c8c8486"
        + "8a1f98db97d3e695ce4a25fce80b828d010d6323120362ac48700abff8a7116e",

    // sequence of sequence
    // padding:30333031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "247aa1cb69ccb72795c93809d7c3a5e52de98ec5285196058a6ab18ec2f5d9fe"
        + "f5545ab5df923f63bd58f5f247b3d824bf161bcb56d325d4e2fc7eb3765dd81b"
        + "5580422abf2a3bca8d8af94cf6a9a3133b1494f66d5cbe938d30b9308b5ce2cc"
        + "6d3df37d3299b6a7616d40afcc7935d80225e1a89a7a63ebff13a66e21280a6a",
    // padding:3033300f300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "6d6248f823020a9604bbafe5acc103d9bd020624585c95805533de22afa3b6b1"
        + "b511f8805296ee4d3e96d707c91e55df8959464ddb6d6a3d62b1cb248754302b"
        + "2833406300f4975d913f1b90f95e3673e2c57d6181d73a360e8c818b8a9dd1e7"
        + "a4fdcd68683f11dd47c2d395f20b0ce9c59eede6ae6aa58a707c4ea8d1a73a9a",

    // truncated sequence
    // padding:300f300d06096086480165030402010500
    "941d41c39aa8bf3879d16cb78c5486589e7b97e56a0249c4f613060d26b78659"
        + "8fd2d34bc4e99cc8888137975937307d6a328059a09f3b994bf955c7de4a2841"
        + "a0d10bbbebb2db3b332656f258c66c8d50cf9155ba94e1cb21a78e6147af7695"
        + "8ddd997665b6d8f67ea8f5e1fdbebd7df635f20494489c895d33ae4c7f248bad",

    // repeat element in sequence
    // padding:3053300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25
    "a32afeafa2c3b58bef55776ef6daaac6647485dde100d968e0449d1a2d5a1218"
        + "07ca2fdd70e2e9cf524cae4f263e11837000df85f0886b718ff45cd316c8d031"
        + "b746dabfb956dd6118a37e0dabcda1ce9c728afd9a5f2448f5b15d2798221888"
        + "8d457752485119f53219315bf63141c9c0802327226a096403ece022cb27c0df",

    // removing oid
    // padding:3026300205000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25
    "6098a732419cd71887548ccf4fbf3edeaf9fe7b220bd747ae1b995b746de1f4d"
        + "7b48c73ddb71903f50ccf7c93be9c8219de5a75ecc302ab50356069dfaf642f3"
        + "2ec580a283519fbcf04784860b0660174dfb7e1e527bb320960bde8f6c605bc3"
        + "c1055b878d2adbb44e1b6c41add15cb603345c4fe2d1c0158fa03f21b4c015e0",

    // appending 0's to oid
    // padding:3033300f060b608648016503040201000005000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "2f08cdca4d621007acd15b1f4e3c39882a8aef706878e8f101e7fb250798a352"
        + "8dcbf4d3327ceb0754a2ca0850794094dde8a875cb947d624d386ddb9593259c"
        + "53ef2311260ac3c9cd1277050ec98d105188f590f198ba908ddcf3f9ed18f5a9"
        + "6cc6b353fadde007658f87ff4c201db7621d69c8278305f3e9f2041a2dddfad0",

    // prepending 0's to oid
    // padding:3033300f060b000060864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "10078ea73abb9bbb879c9d8139b1758170fb73b34f39cdc83e6a725439e315a5"
        + "cba4421fe15e8c80d8fda0a9aba9a12c23aab41f7328d4191e6c7c3a53a505ab"
        + "518dce078439347945671ab06a2cd5375457b3bf181c40a1a4be1ea8305c9a40"
        + "1488532c7cdc1150fb9c46a2e846ce4a2fd9ee863d0b0b8af7f10360acc47f10",

    // dropping value of oid
    // padding:30283004060005000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e
    // 25
    "69a74665f61787b54b522937c534e95e91917f5dd4fa9e3472add6e21dc033a2"
        + "75408f35c71ff6cc029e25986fe6dced8ed053a9040aac32fc444e9252d2bd40"
        + "81fe3e51ace15a0f694c0b8953dd6afa7f8cac67f4d8e17513b415c14b439a63"
        + "4274893885907e2ea428a6e242154a58a031fedae31c73df7cd4e2f5591496cb",

    // modify first byte of oid
    // padding:3031300d060961864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "8f3b544724810d462cc9b19f356e61efe7c192dd63511a9f1f63286ca81f8947"
        + "7c2b464f8e51a97ee138dcf8c6709d79a78591081384af7cb5e182c9867b8260"
        + "13e6191efddddcc39909d3ffbb18944503b69d774c959831a8092f4790a49335"
        + "21100c3e9741c3b58e1d24b75425ee28fde4e40c249b4dccd726cb06cb9ad2e3",

    // modify last byte of oid
    // padding:3031300d060960864801650304020005000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "3933d6937e977caac37a07a5c4ae503565af57e6c4e830004147f8bbf6784f79"
        + "666d89cb4cac60e3f0aff2d5ed6a182921e490c958bfa49c86fcf0270914c102"
        + "275b0878f01795c7a2f44a8a6f5306aa67a81f9294089876801503989e749d15"
        + "2c3e34906291f1f54bb6232fdd3d51e807f70927bf38ef70bd2ba45f0323acf2",

    // truncate oid
    // padding:3030300c0608608648016503040205000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "7b2f6581fb0b4f913ed38c0ea20dff2bd60723f2bc3f1022ceb946e48adb75b1"
        + "e0be031dd8b706d82967f93c6b6ba496d8c4b49aea9970e139b18fefdce30a4e"
        + "c04f77625eaca4c7d1265cebbbcf53b63a113cf06bc50e4a416a771cd28785a0"
        + "075631a3ef60c9212e224aaa063e7d8109c27e248e6422b26acd02ec012b7bf3",
    // padding:3030300c0608864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "4dc9e86e076a395b530868d9fec9f858bd6e8c10cc1e32cae7653abb3f23991c"
        + "677e970ee468c7f35022f3241f5d35673a8cf4ce9134b1e63a994dc7abc8cf4b"
        + "9dbbb126b314312539931a0163c911f0234f5c3f683c9376f2ecaa3294d71a12"
        + "74f6c63b84ea8faf826eacb05e4fa5459b787ff384b2cfe0f1f4c755f32b5c50",

    // wrong oid
    // padding:302d300906052b0e03021a05000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d
    // 7a6e0f345e25
    "8bbc9167821885a728260bf9831120ecc42c14b2b07854169c86421146367d1b"
        + "ec66d8c3daadd115f16a29754e7fa8fb70a63966f7838484615d4364311b6c3f"
        + "6e73ecd8ced0adb52db2c374297119f5fe571bd5396529d13b7225e87db5b5b0"
        + "df38e4c56f2349071b09ff5c1ded919b398d4aff38c6ae29af6f6ff99d3e8836",

    // longer oid
    // padding:3032300e060a6086480165030402010105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "2054d402bf6a148b52972b830c8c8a16a6aeddbcd5c2ae3fd83de67c666e712f"
        + "a98650308658837a67ab87b2c444bedc7cf995c19af433da9343f260049b1bcb"
        + "436ebe27d8a502728dfb0daac5d2710e2c39fa000b909aede07ad7a0d27629e0"
        + "ac27ed9fcd41a39e09f7acdec4c2df77f38c535f46e3b96f2772a81e65e74bb8",

    // oid with modified node
    // padding:3031300d060960864801650304021105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "3a94d241563a2ad97574ec82baefccd9dd114e21fa9169d0f54c4d0f57826224"
        + "804ddc9b29c1905c59f39bd6aa3366705a85f5e6e18c0eb0f67986b5265e7371"
        + "865b618e90e5c5313f0b6fce2343aa12d4ed44d6770fa08d4f1342608a4fb627"
        + "a273f3a1f1340d1f5c55957ce51048e3690a845851009cbfe38d3c96e96d4172",
    // padding:30353011060d6086480165030402888080800105000420532eaabd9574880dbf76b9b8cc00832c20a6
    // ec113d682299550d7a6e0f345e25
    "079cb62831dbeb40a638402865cc92cb49913dae214babc3f4f8d69d64cf1436"
        + "2c23c8dd6ebcee9c44633dd54a62bb2f0042c20033728fc2f8ff482cf0be3ee1"
        + "03bacf757b50319495d9a838844ea1064f4bd1f1ebdc1b71a318c3c8f7d76ebd"
        + "79ef2f3991d4d87e110d60e5fc655adfa4a8e792e46c1c7aa96156b884e2f7a9",

    // large integer in oid
    // padding:303a3016061260864801650304028280808080808080800105000420532eaabd9574880dbf76b9b8cc
    // 00832c20a6ec113d682299550d7a6e0f345e25
    "2c9083459ba6504dc10e0e63edf8ede8bdb4a9728673306908ad4e8f25656d48"
        + "65f0748b9fd2cf7b51db0a2c659e0ce021fef3d2d3d0cf7c45343729c2001a19"
        + "d37e29398a9a7e92d7f62693252261f1f7406b54af5447db6e846f981722059b"
        + "7bb09ba95268c321c156ff659e0ce8e709d2819d5ce15f5dcfa54c55114a611a",

    // oid with invalid node
    // padding:3032300e060a608648016503040201e005000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "9a76669c75f0f11399699f76e7bfbefc0d29feb5a8d86de1f751eedbb5c9e7b8"
        + "1ecbc224534db67cfe1b611951a6ff499d86e11cac4a1725e2ff707085a81a76"
        + "c73d5b53d1b0b2c4fab2d2eebe57eca83242a261cfca768abcd8e1f42e3841d6"
        + "98bef3d4f16ac2dfab0fd42ef0abb0463474367dff7ec99d665a9838f2cfc24c",
    // padding:3032300e060a6080864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d
    // 682299550d7a6e0f345e25
    "6674ec2352f0d3e90f4b72086f39815db11b056babc57644c8a703014f439baa"
        + "46e8ed961714d5c7b5f0ec97ba3fe5ab867c16b7e1de089868dcb195fc20cc42"
        + "fa1b3d3060f50cca77281bb6be18d65a1ee8e5a381e21e7f02e819752b71327a"
        + "28719c7284f6425bc9241abb08d000faf58d48848d7f4b8d68b28266e663f36b",

    // appending 0's to null
    // padding:3033300f0609608648016503040201050200000420532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "8d18a5e0a81522b56eb9e4f43bee15475cdfc7881006150cc230e76028283375"
        + "a13425fe5a106f2626346a65817010a5510b157b234a16fcb9426909a524a288"
        + "161537be91ab13033ed296f5f8c1e5c3bdb963f12d7b5eded46106f7c2dc1ae9"
        + "c451415303cb7e6a3f59809b922183b9638197909d5730e5b1e89705fbbe8464",

    // appending 0's to digest
    // padding:3033300d060960864801650304020105000422532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e250000
    "51640e26e8764936a7d9d709b3e0f52a5f1843453f2c6107a8e7fd6dad8b1c02"
        + "ecc71659cd4134be952c03ee83c190bea4ea7260e5472c3cdf87b6ad45b5c974"
        + "957ee9b4bf6f30152c2d939f722cff32e5482db96f3e283532b96716d3624daf"
        + "16767e0ecdad16c97e56e4e076d64b92af329d2d6a2f8d14b59d1b84853659ab",

    // prepending 0's to digest
    // padding:3033300d0609608648016503040201050004220000532eaabd9574880dbf76b9b8cc00832c20a6ec11
    // 3d682299550d7a6e0f345e25
    "9080bd4ac03b7ecedd45f8165360d4848bdfe1c9212ee1a4debc1aa92886cd79"
        + "47a2df5435789bbb0b3e8f78815aac80e2cff14e1939e9ec32f42e7c29ed4029"
        + "c88cafb64e8523dc85217c40d1bba900468a69c5bd4d12ac67401698fbffaa51"
        + "59907ad459d3843e12487b3b2315c585881bc42e45543f7cf25110ab7e0a19f4",

    // dropping value of digest
    // padding:3011300d060960864801650304020105000400
    "5f66f645307346216d3ba9c3d8b29e96270cb3b2e686a676fe975c10b8c26fda"
        + "8d8eb172628bb3dcd726160c13ab8c5afb1d6ae943ea4c18d00465d97c0d2bcc"
        + "27a63c18457ff8d6e3f5ba373b4be7b6f4c610f83578613f4fe41a40d86230af"
        + "ce0bb8d4496425a5bf0a80c6b1b1e2a981cd44c31a9aa603748c3d2fd2b85478",

    // modify first byte of digest
    // padding:3031300d060960864801650304020105000420522eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "8ccff4ab4fd8534b6b50593f775bf6684391225adc37745e7ff25a4c4baff78a"
        + "252aa1177ea3f3f09d2791da50ba19cef40ab8915379f128bba3271069cc2c02"
        + "725e09f0b2cdfa0d313eba3f5a7e231588fd617b7d90b285e88a944d7d0a7fe9"
        + "cc558dfe8103391ab2e6fbf762d829a55ed4486b5d888957078ffcf49e8ec352",

    // modify last byte of digest
    // padding:3031300d060960864801650304020105000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e24
    "694b90d259b8dbe290f5851ede2ebf3bb718c1674ab1d3b7b6418e8ef7ea0760"
        + "bf3ce69d98a7a3baae5aee488cddfb877972fa88ad05996879d0ce15aca53591"
        + "423bf1b1b3ff02f823cdbb26bb80e3f7b83c3b7ac01ad7806335f871cd7b7e9e"
        + "64708c200a9cd092589131aeb7db15655174000cf7db782bd54325ea956a1a15",

    // truncate digest
    // padding:3030300d06096086480165030402010500041f532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e
    "888cd9bdece5ceeef0fea92727ef1a1d996960f3f551bf108682f81035903236"
        + "69ba1ab48becd14a49b87a900434d0ca7670d094b08b2f851834757bef580d2d"
        + "3278d85b88036ea90d4c2a673dfafeb0c3701332c2b77493110d9b28dade7e98"
        + "5ec27240c90498372fc00ac8e0e5547e4d59cdd19022b8d961f3b63630b5448d",
    // padding:3030300d06096086480165030402010500041f2eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "762c745262627d0df634d6cce41fb8af3cb855fc2d974b8093b035e9d11e510b"
        + "9b7e7d61581b8f262fc1c4b8a6da3f6d609512e32f16416c7449c623c1773417"
        + "032ddf2a559d7eb3af129fd02f83b5e35f5b5c065b1e0bc6481f38b6361f0b01"
        + "8b5e7166e8e67dddcf1550222f125efde241a27b0e7f670d15346dde082a8c4e",

    // wrong hash in padding
    // padding:3030300c06082a864886f70d020505000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d6822
    // 99550d7a6e0f345e25
    "27830ed405bc9d34009ec6258b766100273b4dcf2a9b3cf6ae31029837c6e24c"
        + "f6e819734c1fd10c2c23db34d227d98d3498850f083ecd78b648baccfd4647a5"
        + "72607dedbc2b8ab7a595c0594ece904380e7f395ba4840a81367e99275cde106"
        + "4fc6f7fbd564c5f26ddd0103991ae8262eaf16623685b43f77ea7a05d080166a",
    // padding:3031300d060960864801650304020205000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "4cdfa8cd615bcdefa253d75212e4ed0a1fd60841656c6a749690cb0c6c3cd723"
        + "b518560c3b11a734010acf6e38f0526338351d9b58351826b360c851d3c86429"
        + "f38eb689e8555aa2a23157e197faebdd29bc49f84c10dacca655cd5fa50fdec8"
        + "6a72f0ff1c7f8feeec31fee188fbfa72776a7b5cdae1c1506830bd3a00181b13",
    // padding:3031300d060960864801650304020305000420532eaabd9574880dbf76b9b8cc00832c20a6ec113d68
    // 2299550d7a6e0f345e25
    "024746d8dd71ecfe33cf0ad7ab8ddab9dfeb5740ec47b8ddd668f07b8f7610f7"
        + "26692404ac14c3a1947ff4246fe0a9e216131489125e71df68d60930fac06a20"
        + "e948a3e4948aff5e3f9772155f8bd6772b1cefd8180ae719afc061e2f0d68a69"
        + "769930b8d90ca4ecd6c7b20d04f0cc939502e698ad1c500403763c0205f6870d",

    // wrong hash in signature
    // padding:3020300c06082a864886f70d0205050004100cbc6611f5540bd0809a388dc95a615b
    "3a152ced8b5e0efa33cd57d4afe67f31ed3b9fb22e7b0ff32795cd9510374fa0"
        + "9fc63a3366465f83ba4d44e36418a5c1d171b6ca05d8c74a242983d5e5912cd0"
        + "5bdbd75fcfd5b4eda7cadab21e6dcefca8e2ab7303871ef360beff45564a01bd"
        + "c887d9e849e407c6aa5b12055647f6c9df49758d1272f7cb476f51088e21f246",
    // padding:3021300906052b0e03021a05000414640ab2bae07bedc4c163f679a746f7ab7fb5d1fa
    "3765b8800e6ccf29544d834034e39f8fe7a2e6dfd7e6b4a8f81df091bbfd7aa1"
        + "7edfa6005024fe04d35c340a2215fd3f1cf4b4dfdd3c8ad09e6df2c2256c7541"
        + "e19c2e80051d1ef5df5c384bfb6be88c4415eb2740db2d9fb3214890a8a0f191"
        + "46dfb7897bacc02700a89139dc8fb21b2a7bbfbd43604d7f384cc00aecefb4ef",
    // padding:3041300d0609608648016503040202050004307b8f4654076b80eb963911f19cfad1aaf4285ed48e82
    // 6f6cde1b01a79aa73fadb5446e667fc4f90417782c91270540f3
    "5c5b097c21ac2eb156de39d1eaebe3b96082f54b0171469a94edf7d2027ebfde"
        + "bc0837f766cfefec577e7b797c7a082df2ecc826e55d39927b01c2da26f8f681"
        + "4ec993e3b93ee87a3418322b65ac652b3bba6d34373a13fd40b66be489938fad"
        + "f67bbda762f6ee09a1ddc41382051d4a9a946e0df832bc65b7d5dd58cc5a402b",
    // padding:3051300d060960864801650304020305000440c6ee9e33cf5c6715a1d148fd73f7318884b41adcb916
    // 021e2bc0e800a5c5dd97f5142178f6ae88c8fdd98e1afb0ce4c8d2c54b5f37b30b7da1997bb33b0b8a31
    "0ede4ac9ffcb6d3d42c75cf73303a28ba6089941f68dcf392a75b071f6c149a1"
        + "09cab95b80a679ca3b29ae44e51c18a2db4c72211ae6b959c7f22e854c45f20f"
        + "5560446f33be4819f08d981d2fb176d48039ac4acd28127d593f9e219ad40e2a"
        + "5ee911b334b3b8bb290f2327524e3faae2c028745e03d58882bfe503c4ff04b2",

    // using PKCS#1 encryption padding
    // padding:0002ff...00<asn wrapped hash>
    "6c0b3edf5f6e5d3f07057d0b752e89cfdd1c289ad18a0ba94670cd36547734e2"
        + "c7bb32dd49709f0f7149944c450c23b7f2d360e3602cad5ddff7fd9d711eef6d"
        + "d4c32e66c4433f041fffefe112024a655bc5bacbd0914bbb2b2a41a91b1293fe"
        + "9478ddca926a13e6131cc5e9b70625eac1e533ce8171a2dc7b2c4a490e966445",
    // padding:0002ff...00<hash>
    "1acce04e348a5c8377c54d8ddd8ec2d8c5cb9b195863c32eb716745f3462b5f2"
        + "49b612aefb31ba484949d0a0cb5cb8e1f06c1cec58fe5ffff6ba796218c46c3e"
        + "527c7ab0c4276ccbafd133812faec33721a08542e7e3a34449bebbb28bd0f289"
        + "94c6801ba5c971991004e31de8f728f6bc37a4ec7b049c1f2dc64d4be9415462",

    // invalid PKCS#1 signature padding
    // padding:0001ff...ee00
    "61a4066d0b64964100ecf583325cad10b53912aba1bf3606720d2bdd8e21120b"
        + "b0b5e4323987d96039819ccce0e5e90854bc0e5c239ab198f75b00355a04e4eb"
        + "1f855f76697cd65732820575306eb9323954bc5913568a7278fcdeff8e8acad4"
        + "481e3559f8c44a0be3bc02bae437c3146e4516632b3fe788c3a0e44171155728",

    // PKCS#1 padding too short
    // padding:000001ff...
    "979a313677883b0980997f1cb525f43401739945860149dcad80f602df8abed4"
        + "fd85bcd6e174d9183a5a44008fd77b5a5abcffbcfd4f47ccd2dabef963d9b228"
        + "310d99000ed0cebbf61438cbe586985bcffb3923a8467a97ae791d0b04925c08"
        + "94b5a41583d6de72d4369f481f66abce41a577fb128fc0b0aeec746ec089d834",

    // invalid length
    // padding:2 bytes too long
    "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0000",
  };

  @Test
  public void testBasic() throws Exception {
    String algorithm = "SHA256WithRSA";
    String hashAlgorithm = "SHA-256";
    String message = "Hello";
    int keysize = 2048;

    KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
    keyGen.initialize(keysize);
    KeyPair keyPair = keyGen.generateKeyPair();
    RSAPublicKey pub = (RSAPublicKey) keyPair.getPublic();
    RSAPrivateKey priv = (RSAPrivateKey) keyPair.getPrivate();

    byte[] messageBytes = message.getBytes("UTF-8");
    Signature signer = Signature.getInstance(algorithm);
    Signature verifier = Signature.getInstance(algorithm);
    signer.initSign(priv);
    signer.update(messageBytes);
    byte[] signature = signer.sign();
    verifier.initVerify(pub);
    verifier.update(messageBytes);
    assertTrue(verifier.verify(signature));

    // Extract some parameters.
    byte[] rawHash = MessageDigest.getInstance(hashAlgorithm).digest(messageBytes);

    // Print keys and signature, so that it can be used to generate new test vectors.
    System.out.println("Message:" + message);
    System.out.println("Hash:" + TestUtil.bytesToHex(rawHash));
    System.out.println("Public key:");
    System.out.println("Modulus:" + pub.getModulus().toString());
    System.out.println("E:" + pub.getPublicExponent().toString());
    System.out.println("encoded:" + TestUtil.bytesToHex(pub.getEncoded()));
    System.out.println("Private key:");
    System.out.println("D:" + priv.getPrivateExponent().toString());
    System.out.println("encoded:" + TestUtil.bytesToHex(priv.getEncoded()));
    System.out.println("Signature:" + TestUtil.bytesToHex(signature));
  }

  /**
   * Tests an RSA signature implementation with a number of vectors. The test assumes that the first
   * test vector is valid, but everything else is invalid. Many of the test vectors are derived by
   * signing modified ASN encodings. Hence accepting an invalid signature does not mean by itself
   * that the implementation can be broken, but often points to a bigger problem. The test expects
   * that verifying an invalid signature either leads to a return value False or will result in a
   * SignatureException. Verifying an RSA signature should not result in an RuntimeException, so
   * that reasonably implementated applications can be expected to catch and treat invalid
   * signatures appropriately. While RuntimeExceptions may not be exploitable, they often indicate
   * an oversight in the implementation of the provider.
   * https://docs.oracle.com/javase/tutorial/essential/exceptions/runtime.html
   */
  private void testVectors(RSAPublicKeySpec key, String algorithm, String[] testvectors)
      throws Exception {
    byte[] message = "Test".getBytes("UTF-8");
    Signature verifier = Signature.getInstance(algorithm);
    KeyFactory kf = KeyFactory.getInstance("RSA");
    PublicKey pub = kf.generatePublic(key);
    int errors = 0;
    boolean first = true;
    for (String signature : testvectors) {
      byte[] signatureBytes = TestUtil.hexToBytes(signature);
      verifier.initVerify(pub);
      verifier.update(message);
      boolean verified = false;
      try {
        verified = verifier.verify(signatureBytes);
      } catch (SignatureException ex) {
        // verify can throw SignatureExceptions if the signature is malformed.
      }
      if (first && !verified) {
        System.out.println("Valid signature not verified:" + signature);
        errors++;
      } else if (!first && verified) {
        System.out.println("Incorrect signature verified:" + signature);
        errors++;
      }
      first = false;
    }
    assertEquals(0, errors);
  }

  /** SunJCE threw an OutOfMemoryError with one of the signatures. */
  @Test
  public void testVectorsAll() throws Exception {
    testVectors(RSA_KEY1, ALGORITHM_KEY1, SIGNATURES_KEY1);
  }

  /**
   * Signatures with legacy encoding. Such signatures are sometimes accepted to be compatible with
   * previously buggy implementations.
   */
  static final String[] LEGACY_SIGNATURES_KEY1 = {
    // A signature where the NULL parameter is missing in the ASN encoding.
    // padding = 302f300b06096086480165030402010420532eaabd9574880dbf
    // 76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25
    "253e1d19bbe91064f2364c1e7db3ba8eb6dc5b19202e440eab6fbdf28c8c6ec0"
        + "5b812983713c338c72b6e99b8edf506a89ff9fc8e5c2c52362097a56dc228060"
        + "eca01e1ff318c6c81617691438703411c1f953b21cd74331f87c9b8b189fdffd"
        + "fe8550bd2bd1d47be915f8604a0f472199dd705e19b1b815f99b68d60bc257c7",
  };

  /**
   * Tests legacy signatures. In this context we use the term legacy signatures for signatures that
   * are not conforming to the PKCS #1 standard, but are sometimes generated by buggy signers. So
   * far this test considers both accepting and rejecting such signatures as valid behavior.
   *
   * <p>Currently we check for just one type of legacy signatures: i.e., a missing NULL parameter in
   * the ASN encoding of the hash. BouncyCastle and the SunJCE accept this signature, Conscrypt does
   * not.
   *
   * <p>Some references that support accepting this signature:
   * https://codereview.chromium.org/1690123002/
   * https://groups.google.com/a/chromium.org/forum/#!topic/chromium-reviews/Jo5S7HtEABI claims that
   * 7% of the responses in the Online Certificate Status Protocol (OCSP) miss the NULL parameter
   */
  @Test
  public void testLegacySignatures() throws Exception {
    RSAPublicKeySpec key = RSA_KEY1;
    String algorithm = ALGORITHM_KEY1;
    byte[] message = "Test".getBytes("UTF-8");
    Signature verifier = Signature.getInstance(algorithm);
    KeyFactory kf = KeyFactory.getInstance("RSA");
    PublicKey pub = kf.generatePublic(key);
    for (String signature : LEGACY_SIGNATURES_KEY1) {
      byte[] signatureBytes = TestUtil.hexToBytes(signature);
      verifier.initVerify(pub);
      verifier.update(message);
      boolean verified = false;
      try {
        verified = verifier.verify(signatureBytes);
      } catch (SignatureException ex) {
        verified = false;
      }
      if (verified) {
        System.out.println("Verfied legacy signature:" + signature);
      } else {
        System.out.println("Rejected legacy signature:" + signature);
      }
    }
  }

  /**
   * Faults during the generation of a signature can leak the information about the private key.
   * A. K. Lenstra showed in "Memo on RSA signature generation in the presence of faults", 
   * (https://infoscience.epfl.ch/record/164524/files/nscan20.PDF) that PKCS #1 signatures are
   * especially susceptible to faults when the Chinese Remainder Theorem is used to compute the
   * signature: one single faulty signature is sufficient to leak the private key.
   *
   * One countermeasure that is often used in libraries is to blind the RSA computation and
   * verify the signature before returning it. Nowadays, libraries are expected to have at least
   * some countermeasures against faulty computations. In some cases (e.g. OpenSSL) the library
   * tries to fix a faulty computation by generating a correct signature without using Chinese
   * remaindering.
   *
   * The test here does not induce a fault. Instead it tries to sign with a faulty private key.
   * The expected outcome of the test is that underlying provider either detects that the fault
   * or generates a valid signature by ignoring the faulty CRT parameter.
   *
   * Since the test only simulates a fault, but does not actually induce a fault it is somewhat
   * incomplete. It does not detect all vulnerable implementations. The test should nonetheless
   * detect implementations that include no verification at all.
   */
  @Test
  public void testFaultySigner() throws Exception {
    BigInteger e = new BigInteger("65537");
    BigInteger d = new BigInteger(
        "1491581187972832788084570222215155297353839087630599492610691218"
            + "6098027383804966741416365668088258821394558334495197493887270311"
            + "7558637148793177374456685063919969705672268324029058661801838398"
            + "1099187046803818325657704350675941092582695993374867459573741707"
            + "2513551423973482044545986645893321692393572214394692273248819124"
            + "5866638922766330300631727125395012955305761836925591665625409882"
            + "5987442083465656021724458811783361811914866856391248003733867121"
            + "5531501554906114868306919889638573670925006068497222709802245970"
            + "0014474779292382225845722344584808716054088377124806520166137504"
            + "58797849822813881641713404303944154638273");
    BigInteger q = new BigInteger(
        "1327930250247153291239240833779228146841620599139480980326615632"
            + "6868823273498280322301518048955331731683358443542450740927959439"
            + "3056349447047388914345605165927201322192706870545643991584573901"
            + "9099563807204264522234257863225478717589651408831271029849307682"
            + "13198832542217762257092135384802889866043941823057701");
    BigInteger p = new BigInteger(
        "1546732137638443281784728718025150988901748595222448633054370906"
            + "7724307988669542799529278238746541544956234718616481585427107180"
            + "6134464028933334724614223213582911567222033332353858049787180486"
            + "8311341830570208335451999930773903649599388066890163502238099141"
            + "76306676019969635213034585825883528127235874684082417");

    BigInteger n = p.multiply(q);
    BigInteger dp = d.mod(p.subtract(BigInteger.ONE));
    BigInteger dq = d.mod(q.subtract(BigInteger.ONE));
    BigInteger crt = q.modInverse(p);
    RSAPrivateCrtKeySpec validKey = new RSAPrivateCrtKeySpec(n, e, d, p, q, dp, dq, crt);
    RSAPrivateCrtKeySpec invalidKey =
        new RSAPrivateCrtKeySpec(n, e, d, p, q, dp.add(BigInteger.valueOf(2)), dq, crt);
    byte[] message = "Test".getBytes("UTF-8");
    KeyFactory kf = KeyFactory.getInstance("RSA");
    PrivateKey validPrivKey = kf.generatePrivate(validKey);
    Signature signer = Signature.getInstance("SHA256WithRSA");
    signer.initSign(validPrivKey);
    signer.update(message);
    byte[] signature = signer.sign();
    PrivateKey invalidPrivKey = null;
    try {
      invalidPrivKey = kf.generatePrivate(invalidKey);
    } catch (InvalidKeySpecException ex) {
      // The provider checks the private key and notices a mismatch.
      // This is a good sign, though of course in this case it means that we can't
      // check for faults.
      System.out.println("Provider catches invalid RSA key:" + ex);
      return;
    }
    byte[] invalidSignature = null;
    try {
      signer.initSign(invalidPrivKey);
      signer.update(message);
      invalidSignature = signer.sign();
    } catch (Exception ex) {
      // We do not necessarily expect a checked exception here, since generating
      // an invalid signature typically indicates a programming error.
      // Though RuntimeExceptions are fine here.
      System.out.println("Generating PKCS#1 signature with faulty key throws:" + ex);
      return;
    }
    String signatureHex = TestUtil.bytesToHex(signature);
    String invalidSignatureHex = TestUtil.bytesToHex(invalidSignature);
    if (signatureHex.equals(invalidSignatureHex)) {
      // The provider generated a correct signature. This can for example happen if the provider
      // does not use the CRT parameters.
      System.out.println("Signature generation did not use faulty parameter");
      return;
    }
    fail("Generated faulty PKCS #1 signature with faulty parameters"
         + " valid signature:"
         + signatureHex
         + " invalid signature:"
         + invalidSignatureHex);
  }
}
