/*
 * Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define PASS 1
#define FAIL 0
#define ITM(x) x, sizeof(x)

#ifndef OPENSSL_NO_EC

struct ecdsa_keygen_st {
    const char *curve_name;
};

struct ecdsa_pub_verify_st {
    const char *curve_name;
    const unsigned char *pub;
    size_t pub_len;
    int pass;
};

struct ecdsa_siggen_st {
    const char *digest_alg;
    const char *curve_name;
    const unsigned char *msg;
    size_t msg_len;
};

struct ecdsa_sigver_st {
    const char *digest_alg;
    const char *curve_name;
    const unsigned char *msg;
    size_t msg_len;
    const unsigned char *pub;
    size_t pub_len;
    const unsigned char *r;
    size_t r_len;
    const unsigned char *s;
    size_t s_len;
    int pass;
};

static const struct ecdsa_keygen_st ecdsa_keygen_data[] = {
    { "P-224" },
};

static const unsigned char ecdsa_pv_pub0[] = {
    POINT_CONVERSION_UNCOMPRESSED,
    0x50, 0x0F, 0x05, 0x86, 0xD3, 0xAA, 0x8A, 0x48,
    0x46, 0x63, 0x0D, 0xD7, 0xC7, 0x5D, 0x5F, 0x1D,
    0xB2, 0xA7, 0x9B, 0xE8, 0xC8, 0xBB, 0xBE, 0x2C,
    0x93, 0x33, 0xDC, 0xCB, 0xBB, 0x2F, 0xB3, 0xCF,
    0x55, 0x88, 0x7A, 0x97, 0xD1, 0x75, 0x73, 0xFE,
    0x92, 0x02, 0x5C, 0xC8, 0xE3, 0xF4, 0x35, 0x4B,
    0x08, 0x7E, 0xF4, 0xD3, 0x7D, 0x86, 0x06, 0x92,
    0xBA, 0x15, 0x3F, 0xCF, 0x0C, 0xC4, 0xBF, 0xBC,
};
static const unsigned char ecdsa_pv_pub1[] = {
    POINT_CONVERSION_UNCOMPRESSED,
    0x1F, 0x74, 0xD6, 0x99, 0xEB, 0x1D, 0x4F, 0x26,
    0x25, 0x5E, 0xD4, 0x6A, 0xA6, 0xD5, 0x23, 0xB3,
    0xF5, 0x5D, 0x14, 0x38, 0xE0, 0x4D, 0x23, 0x7F,
    0x9A, 0xE5, 0xB7, 0x1B, 0xF9, 0x7F, 0xAD, 0x7E,
    0x0E, 0x1C, 0x06, 0xF4, 0x20, 0xF3, 0x8E, 0x93,
    0x79, 0x11, 0x15, 0xD6, 0x82, 0x12, 0x14, 0xC2,
    0xF9, 0x30, 0x61, 0x9A, 0xC3, 0x12, 0xE3, 0x88,
    0x4E, 0xB1, 0x1A, 0x4B, 0x54, 0x6D, 0xEA, 0xCF,
};
static const struct ecdsa_pub_verify_st ecdsa_pv_data[] = {
    {
        "P-256",
        ITM(ecdsa_pv_pub0),
        PASS
    },
    {
        "P-256",
        ITM(ecdsa_pv_pub1),
        FAIL
    },
};

static const unsigned char ecdsa_siggen_msg0[] = {
    0xB8, 0x65, 0x55, 0x9D, 0x54, 0x5C, 0xD2, 0xC7,
    0xC2, 0xCA, 0x96, 0xDF, 0xF3, 0x9B, 0x26, 0xED,
    0xF8, 0x16, 0x99, 0x05, 0x94, 0xA9, 0x3F, 0x69,
    0x5F, 0xE8, 0x73, 0xC5, 0xFE, 0x78, 0x28, 0x84,
    0xC0, 0xA7, 0xFA, 0x29, 0xBE, 0x37, 0x82, 0xC0,
    0x56, 0x41, 0x49, 0xAF, 0xF3, 0x59, 0xBB, 0x96,
    0xF6, 0x4B, 0x87, 0x45, 0xAB, 0x1F, 0xB2, 0xB3,
    0x8F, 0x14, 0xD4, 0xD7, 0x1C, 0x29, 0x08, 0x0C,
    0x79, 0x8A, 0x38, 0xAE, 0x32, 0x1C, 0x38, 0x80,
    0x5B, 0x45, 0x25, 0x46, 0x5D, 0xCE, 0x7D, 0x34,
    0xC0, 0x90, 0xEF, 0x06, 0x84, 0xA1, 0x0F, 0xF8,
    0x56, 0x2D, 0x46, 0xF7, 0xB7, 0xDE, 0x06, 0x7C,
    0x87, 0xA6, 0x7E, 0x71, 0x8D, 0x7B, 0x27, 0xE5,
    0x51, 0x0C, 0xE7, 0xBA, 0x18, 0x08, 0xE0, 0xD5,
    0x0B, 0x8C, 0xB6, 0x22, 0xA5, 0x8F, 0xB3, 0xF7,
    0xFB, 0xC6, 0x2A, 0x59, 0x02, 0x8A, 0x8C, 0x42,
};
static const struct ecdsa_siggen_st ecdsa_siggen_data[] = {
     {
        "SHA2-256",
        "P-384",
        ITM(ecdsa_siggen_msg0),
     },
};

static const unsigned char ecdsa_sigver_msg0[] = {
    0x0b, 0x00, 0xc4, 0x3f, 0xb9, 0xcb, 0x92, 0xd3,
    0x56, 0x83, 0xc6, 0x97, 0x23, 0xf8, 0xf1, 0x0b,
    0x0a, 0xa6, 0x60, 0xca, 0x3a, 0xed, 0xba, 0x38,
    0xf7, 0x86, 0xc2, 0x6d, 0xa1, 0xb6, 0x40, 0x2c,
    0x92, 0xfe, 0x44, 0x1a, 0x19, 0x61, 0x5c, 0x02,
    0xfa, 0xd3, 0x79, 0x99, 0xb8, 0x99, 0xe8, 0x70,
    0xaa, 0x26, 0x01, 0xf2, 0xe7, 0xdc, 0x69, 0xce,
    0x9a, 0xd2, 0xaa, 0x02, 0x15, 0xab, 0x0d, 0xcb,
    0x77, 0xaf, 0xe0, 0x81, 0x6d, 0x92, 0x6e, 0x09,
    0xcd, 0x93, 0xd6, 0x22, 0x67, 0xc2, 0xd3, 0x7c,
    0x58, 0x11, 0xc8, 0x77, 0x4e, 0x97, 0x92, 0x87,
    0xcd, 0xe4, 0xc9, 0x2a, 0x77, 0xb2, 0xf0, 0xe7,
    0xd3, 0x5a, 0x20, 0x36, 0x91, 0x75, 0x23, 0xa8,
    0xcb, 0x4a, 0xd0, 0xe5, 0x95, 0x3b, 0x24, 0x2a,
    0x86, 0xa0, 0xaa, 0xbe, 0xac, 0x59, 0xd7, 0xd0,
    0xfb, 0xdf, 0x33, 0xc9, 0x73, 0xaf, 0x8a, 0x06,
};
static const unsigned char ecdsa_sigver_msg1[] = {
    0x45, 0xBB, 0x9D, 0xDC, 0x1D, 0x0A, 0xF2, 0xD7,
    0x56, 0x07, 0x1F, 0x47, 0x2A, 0x17, 0xCE, 0x38,
    0xA8, 0x7E, 0x75, 0xED, 0x4F, 0xE4, 0x17, 0x65,
    0x11, 0x69, 0xDF, 0x04, 0xF0, 0x39, 0x28, 0xD0,
    0x75, 0xD5, 0xF0, 0x1C, 0x32, 0x84, 0x16, 0x74,
    0x7D, 0x61, 0x57, 0xDB, 0x92, 0x24, 0x60, 0xBA,
    0x58, 0x7B, 0x48, 0xB4, 0x44, 0xFB, 0xD7, 0x35,
    0xD7, 0xCF, 0x61, 0x34, 0x7F, 0x70, 0x38, 0xAE,
    0xE2, 0xB2, 0x6C, 0x8A, 0xD1, 0x27, 0xB4, 0xF0,
    0x33, 0xB9, 0xE9, 0x27, 0x1A, 0xEE, 0x34, 0x72,
    0x9D, 0x5E, 0x74, 0x28, 0xE7, 0x0B, 0x82, 0xE1,
    0x60, 0xC2, 0x43, 0xE6, 0x75, 0x4A, 0x2E, 0x66,
    0x88, 0x72, 0xCA, 0xC7, 0x97, 0xFE, 0x19, 0xCD,
    0xA4, 0x30, 0xBF, 0xC7, 0xDC, 0x37, 0xF8, 0x1B,
    0xB6, 0xD7, 0x7E, 0xAD, 0xD6, 0xC1, 0x20, 0xAC,
    0x79, 0x03, 0x89, 0xEA, 0xF4, 0x59, 0x21, 0xF2,
};

static const unsigned char ecdsa_sigver_pub0[] = {
    POINT_CONVERSION_UNCOMPRESSED,
    0x2c, 0xdf, 0x6f, 0x23, 0x3d, 0x73, 0x86, 0x25,
    0x1a, 0x29, 0xd6, 0xde, 0x98, 0xcf, 0x85, 0xf7,
    0x6a, 0x55, 0xba, 0xdb, 0x0f, 0x35, 0x94, 0x92,
    0xb3, 0x58, 0xf3, 0x89, 0x7f, 0x6c, 0x22, 0x10,
    0xd9, 0xd3, 0x54, 0xd2, 0x74, 0x9f, 0x64, 0x0d,
    0xd2, 0xf8, 0x3e, 0xfc, 0x7f, 0xb7, 0x16, 0x36,
};
static const unsigned char ecdsa_sigver_pub1[] = {
    POINT_CONVERSION_UNCOMPRESSED,
    0x00, 0xD4, 0x79, 0x9F, 0xD9, 0x99, 0xEC, 0x21,
    0x1E, 0xE6, 0x06, 0x58, 0xB7, 0xFB, 0x76, 0xFC,
    0xF5, 0x9A, 0xE1, 0x1E, 0x5A, 0x87, 0xD7, 0x0E,
    0x21, 0x7B, 0xDE, 0x21, 0x52, 0xE6, 0xE4, 0x09,
    0x2C, 0xB8, 0x5D, 0x99, 0xE2, 0x6A, 0xB1, 0xE5,
    0x79, 0x11, 0x49, 0xBD, 0x3D, 0xC7, 0x1C, 0x48,
    0xF5, 0x83, 0xFC, 0x9E, 0xF3, 0xAB, 0x2D, 0x30,
    0x64, 0xEC, 0x22, 0xCB, 0xEB, 0x95, 0xBF, 0xF2,
    0x2D, 0xCE,

    0x01, 0xB9, 0xFE, 0xBD, 0x4C, 0x4B, 0xDA, 0x1F,
    0x30, 0xC3, 0x5C, 0x0F, 0x5D, 0x3E, 0x36, 0x51,
    0xF2, 0xC0, 0xF7, 0xFC, 0x79, 0x25, 0x98, 0xF2,
    0x4B, 0x2B, 0x61, 0xFC, 0xD9, 0xC4, 0x5C, 0xC0,
    0x13, 0xA2, 0x4F, 0x2E, 0x34, 0xD4, 0x15, 0xC4,
    0x25, 0x13, 0xA9, 0x30, 0x35, 0x56, 0xB7, 0xCD,
    0xD0, 0xC4, 0x65, 0x5D, 0xFB, 0xB2, 0xE4, 0xBF,
    0x22, 0x67, 0xEF, 0xA1, 0x2E, 0xA2, 0x1B, 0x33,
    0xE4, 0x3D,
};
static const unsigned char ecdsa_sigver_r0[] = {
    0x6b, 0x35, 0x62, 0x67, 0xa3, 0xbd, 0x76, 0xc8,
    0xa3, 0xdc, 0x93, 0x18, 0x82, 0x6f, 0xd2, 0x43,
    0x52, 0x18, 0x93, 0x21, 0x8e, 0xce, 0x12, 0x65,
};
static const unsigned char ecdsa_sigver_r1[] = {
    0x01, 0xBF, 0xA6, 0x46, 0x6D, 0x4E, 0x1C, 0x42,
    0x18, 0x7D, 0x46, 0xC6, 0x5F, 0xA5, 0x05, 0xEA,
    0x1A, 0xEF, 0xDB, 0x46, 0xD1, 0x79, 0x3F, 0x2B,
    0xE2, 0x70, 0x0F, 0x14, 0x26, 0x30, 0x7F, 0x2D,
    0x1A, 0x41, 0xFD, 0x11, 0xC0, 0xBB, 0xD0, 0xD5,
    0x09, 0xAA, 0xE0, 0x1A, 0xFE, 0x59, 0x23, 0x7D,
    0x1B, 0x5C, 0xB9, 0x51, 0xCD, 0x3A, 0xA1, 0x32,
    0xC6, 0x92, 0xB0, 0x7D, 0x91, 0xC6, 0x30, 0xC0,
    0xA4, 0x2A,
};
static const unsigned char ecdsa_sigver_s0[] = {
    0x7b, 0x92, 0x4a, 0x13, 0x8d, 0x74, 0x87, 0xb2,
    0xd4, 0xc7, 0x21, 0x73, 0x2c, 0x8a, 0x09, 0x25,
    0xac, 0x19, 0xcf, 0x9c, 0xbc, 0xd7, 0x7b, 0xf8,
};
static const unsigned char ecdsa_sigver_s1[] = {
    0x00, 0x8D, 0x56, 0xBA, 0x60, 0x38, 0x23, 0x47,
    0xB8, 0x32, 0x73, 0x29, 0x40, 0x84, 0xF0, 0x2C,
    0x90, 0xB2, 0x1D, 0x56, 0xFF, 0x38, 0x68, 0xAA,
    0x42, 0xBA, 0x48, 0xA1, 0x52, 0x8C, 0xB4, 0xD6,
    0x15, 0xB7, 0x88, 0xB3, 0x71, 0xC6, 0x69, 0x60,
    0x6C, 0xEB, 0x4B, 0xF6, 0x19, 0x6A, 0x95, 0x8F,
    0x01, 0x09, 0xC6, 0x13, 0xE6, 0x17, 0x38, 0xC8,
    0x10, 0x49, 0x4F, 0x87, 0x43, 0x63, 0x62, 0x98,
    0xB1, 0xAC,
};
static const struct ecdsa_sigver_st ecdsa_sigver_data[] = {
    {
        "SHA-1",
        "P-192",
        ITM(ecdsa_sigver_msg0),
        ITM(ecdsa_sigver_pub0),
        ITM(ecdsa_sigver_r0),
        ITM(ecdsa_sigver_s0),
        PASS,
    },
    {
        "SHA2-512",
        "P-521",
        ITM(ecdsa_sigver_msg1),
        ITM(ecdsa_sigver_pub1),
        ITM(ecdsa_sigver_r1),
        ITM(ecdsa_sigver_s1),
        FAIL,
    },
};

#endif /* OPENSSL_NO_EC */


#ifndef OPENSSL_NO_DSA

struct dsa_paramgen_st {
    size_t L;
    size_t N;
};

struct dsa_pqver_st {
    const unsigned char *p;
    size_t p_len;
    const unsigned char *q;
    size_t q_len;
    const unsigned char *seed;
    size_t seed_len;
    int counter;
    int pass;
};

struct dsa_siggen_st {
    const char *digest_alg;
    size_t L;
    size_t N;
    const unsigned char *msg;
    size_t msg_len;
};

struct dsa_sigver_st {
    const char *digest_alg;
    const unsigned char *p;
    size_t p_len;
    const unsigned char *q;
    size_t q_len;
    const unsigned char *g;
    size_t g_len;
    const unsigned char *pub;
    size_t pub_len;
    const unsigned char *msg;
    size_t msg_len;
    const unsigned char *r;
    size_t r_len;
    const unsigned char *s;
    size_t s_len;
    int pass;
};

static const struct dsa_paramgen_st dsa_keygen_data[] = {
    { 2048, 224 },
};

static const struct dsa_paramgen_st dsa_paramgen_data[] = {
    { 2048, 256 },
};

/* dsa_pqver */
static const unsigned char dsa_pqver_p0[] = {
    0xEF, 0xC7, 0x95, 0xEB, 0x1E, 0x1C, 0x8F, 0x5E,
    0x4A, 0x85, 0xCD, 0x20, 0x66, 0xC7, 0xB9, 0x6C,
    0x4E, 0xC4, 0xE7, 0x3B, 0x7B, 0x8E, 0x0E, 0x8C,
    0x00, 0xF5, 0x2E, 0x68, 0xF5, 0xC2, 0x89, 0x47,
    0xA5, 0x7B, 0xA6, 0xA3, 0x30, 0xBC, 0xFA, 0x25,
    0x29, 0xBD, 0xE2, 0x4D, 0x05, 0x0B, 0x6D, 0x2D,
    0x49, 0x50, 0x53, 0xEF, 0x8C, 0xBE, 0xC3, 0xEC,
    0x92, 0xC1, 0x45, 0xE3, 0x95, 0x39, 0x72, 0x58,
    0xFD, 0x93, 0x23, 0x06, 0x37, 0xD6, 0x56, 0x1F,
    0x75, 0x92, 0xAD, 0x15, 0xA9, 0x88, 0x25, 0x3F,
    0xD6, 0x47, 0xB5, 0xB1, 0x32, 0x01, 0x2D, 0x70,
    0x55, 0xB9, 0x5D, 0xED, 0x1B, 0x40, 0x39, 0x78,
    0x74, 0xA6, 0xDF, 0x4B, 0xE4, 0x86, 0x8B, 0x56,
    0x46, 0x1E, 0xDB, 0x04, 0xD2, 0xD2, 0x50, 0xE9,
    0x5D, 0x88, 0xA8, 0x84, 0x55, 0xE3, 0xF3, 0xB7,
    0x07, 0x54, 0x9E, 0x98, 0x03, 0x9F, 0x31, 0x86,
    0xEB, 0x0D, 0x26, 0x97, 0x30, 0x31, 0x34, 0x64,
    0x35, 0x56, 0x40, 0x35, 0xEA, 0xE5, 0x00, 0x90,
    0xBD, 0x20, 0x93, 0xFC, 0xAD, 0x70, 0x9A, 0xF5,
    0xB8, 0xA4, 0xAD, 0xEC, 0xFE, 0x64, 0xF4, 0x2C,
    0x11, 0x25, 0x68, 0x27, 0x0E, 0x5C, 0x81, 0x57,
    0x64, 0x9A, 0x50, 0x86, 0xA3, 0x69, 0x61, 0x1E,
    0x0D, 0x62, 0xE9, 0x4D, 0x44, 0x1E, 0x1E, 0xE1,
    0x6D, 0x8F, 0x10, 0x67, 0x82, 0xB6, 0x6A, 0xD0,
    0x08, 0x59, 0xF3, 0xBA, 0xE8, 0x29, 0xE0, 0x60,
    0x1F, 0x3E, 0xBA, 0xAB, 0x6E, 0xB6, 0x5B, 0xAF,
    0xCC, 0x76, 0x5D, 0x70, 0x7F, 0x3A, 0xAA, 0x7E,
    0x27, 0x23, 0x6F, 0x8E, 0xF8, 0x06, 0xC1, 0x3E,
    0xAE, 0xBE, 0x22, 0x71, 0x93, 0xEC, 0x9A, 0x33,
    0x3C, 0xA4, 0x77, 0xD4, 0x76, 0x79, 0x10, 0x5A,
    0xF4, 0x07, 0x52, 0x66, 0x9D, 0xC5, 0xFD, 0xDA,
    0xA1, 0xE7, 0xA2, 0x45, 0x27, 0x08, 0x54, 0xB9,
    0x3B, 0xEC, 0x07, 0xFB, 0xE0, 0xF4, 0x4B, 0x7C,
    0xB1, 0x04, 0x2B, 0x0E, 0x65, 0x3A, 0xF7, 0x65,
    0x57, 0x65, 0xCF, 0x36, 0x28, 0x2A, 0x1C, 0x57,
    0x10, 0x28, 0x02, 0x26, 0xF7, 0x45, 0xAA, 0x1B,
    0x2E, 0xE3, 0x25, 0xEA, 0x28, 0xA1, 0x84, 0x1E,
    0xA1, 0xA3, 0xAB, 0x52, 0x25, 0xD4, 0x64, 0xB2,
    0xA8, 0xA5, 0xFD, 0x2F, 0x48, 0x90, 0x28, 0x8F,
    0x8B, 0x10, 0x7F, 0x6F, 0x80, 0xA9, 0x4B, 0xB3,
    0xC0, 0x5B, 0x27, 0xE9, 0x90, 0x90, 0x53, 0xA8,
    0x30, 0x88, 0xD4, 0x9B, 0x09, 0x62, 0xCD, 0x99,
    0x61, 0x63, 0x14, 0xDF, 0xC3, 0x5A, 0x60, 0xBE,
    0xA3, 0x40, 0xAB, 0x29, 0x3E, 0xB2, 0x02, 0x19,
    0x9D, 0x97, 0x75, 0x34, 0x0D, 0x71, 0x3B, 0xEC,
    0xF1, 0x13, 0x23, 0xE6, 0xCA, 0x35, 0x84, 0xFF,
    0x27, 0x4A, 0xE0, 0x11, 0x59, 0xEB, 0x1D, 0x8C,
    0xFF, 0xF3, 0x91, 0x90, 0x3C, 0xE9, 0x43, 0x31,
};
static const unsigned char dsa_pqver_p1[] = {
    0x83, 0xA6, 0x8F, 0xE5, 0xFE, 0xF0, 0x9D, 0x9E,
    0x8A, 0x80, 0x9C, 0x47, 0xEF, 0xBE, 0x1A, 0xD0,
    0x7F, 0xEA, 0x6D, 0x08, 0x59, 0x2D, 0x04, 0xB6,
    0xAC, 0x2A, 0x54, 0x47, 0x42, 0xB2, 0x5F, 0x28,
    0xF6, 0x30, 0x36, 0xE3, 0xDA, 0x4E, 0xDC, 0xC1,
    0x6E, 0x61, 0xCE, 0x45, 0x1C, 0x73, 0x87, 0x3E,
    0xB7, 0x94, 0xDB, 0x68, 0xEE, 0xFD, 0x8D, 0x93,
    0x5E, 0x5D, 0xAB, 0x77, 0xA2, 0xF0, 0xD6, 0x60,
    0xCD, 0x9D, 0x13, 0xE0, 0xA6, 0xE7, 0xEC, 0x45,
    0xBA, 0xD8, 0xB0, 0x3D, 0x4F, 0x75, 0x30, 0xB7,
    0x89, 0x96, 0x2B, 0x48, 0xFC, 0x73, 0xB8, 0x5C,
    0x59, 0xDC, 0x41, 0xEF, 0xCE, 0xC6, 0x7F, 0x66,
    0x4F, 0xB6, 0x1F, 0x9C, 0x91, 0xB4, 0xEE, 0xAA,
    0x2C, 0x4A, 0x7F, 0x1F, 0xBF, 0xE2, 0x9A, 0xF2,
    0x9F, 0x52, 0x83, 0x30, 0x97, 0x86, 0x7F, 0xA2,
    0x85, 0x20, 0x75, 0x75, 0xAD, 0x01, 0xE2, 0x40,
    0x3A, 0x82, 0xD8, 0x52, 0x91, 0x15, 0x67, 0x1B,
    0x00, 0x78, 0xFD, 0x3E, 0x61, 0x8A, 0xA8, 0x1D,
    0x1A, 0x07, 0x8E, 0x87, 0x48, 0x64, 0x1E, 0x5B,
    0x05, 0x34, 0x7E, 0x5D, 0xD6, 0x11, 0xC4, 0xB7,
    0x0E, 0xF3, 0x91, 0xC7, 0x2B, 0xAD, 0x22, 0x96,
    0xA3, 0xF7, 0x4E, 0xEB, 0xE4, 0x9F, 0x67, 0x91,
    0x9D, 0x65, 0x45, 0x8F, 0x92, 0x2F, 0x8B, 0x46,
    0xCC, 0x4B, 0xB9, 0xC5, 0xD0, 0x00, 0xFF, 0xBB,
    0x37, 0xD6, 0x20, 0x36, 0x7D, 0x4A, 0xC3, 0x75,
    0xAC, 0x58, 0xE5, 0x24, 0x54, 0x47, 0x80, 0x2C,
    0x83, 0xBD, 0xC8, 0xA7, 0x87, 0x20, 0x3D, 0xA8,
    0x78, 0xE2, 0xC5, 0x4E, 0xE8, 0x4E, 0x3C, 0xFA,
    0x75, 0xA0, 0x8D, 0x35, 0x8E, 0xF2, 0x61, 0x19,
    0x84, 0x9C, 0x71, 0x95, 0x5B, 0x09, 0xE1, 0xB6,
    0xC6, 0x6A, 0x7C, 0x34, 0x39, 0x67, 0x14, 0xAB,
    0xA7, 0x6B, 0x45, 0x01, 0xF0, 0x0F, 0x52, 0xB5,
    0x23, 0xD9, 0x67, 0x57, 0x91, 0x9F, 0xC2, 0xA9,
    0xB6, 0x7C, 0x15, 0x59, 0x3E, 0x22, 0x89, 0xD6,
    0x0B, 0x83, 0xB4, 0x29, 0xEF, 0x0B, 0x66, 0x30,
    0x2D, 0xE7, 0xC5, 0x04, 0x1F, 0x28, 0x7D, 0x9F,
    0xC9, 0x87, 0x05, 0xC6, 0x1B, 0x18, 0x1F, 0x3B,
    0x90, 0x00, 0x31, 0x5B, 0xDC, 0x19, 0x7D, 0x71,
    0xE4, 0xA4, 0x21, 0xB5, 0x37, 0xE7, 0x9B, 0xA4,
    0xBC, 0x04, 0xF8, 0x0A, 0x95, 0x3F, 0xDB, 0x30,
    0xA5, 0xC9, 0xC2, 0xD7, 0x19, 0x9D, 0x57, 0x77,
    0x44, 0xB7, 0x47, 0xBD, 0xA1, 0x01, 0xEB, 0x51,
    0xA4, 0xB2, 0x8B, 0x1A, 0x51, 0xA4, 0xCC, 0x07,
    0x57, 0x19, 0xFB, 0xFC, 0xAA, 0x42, 0xCC, 0x2A,
    0xCE, 0xF8, 0xFD, 0xF8, 0x92, 0xC4, 0xDC, 0x7B,
    0x0B, 0x92, 0x9A, 0xD7, 0xC5, 0xBC, 0x6D, 0x74,
    0x13, 0x0E, 0xD2, 0x8F, 0x86, 0xEB, 0x8D, 0xD7,
    0xC6, 0xAC, 0x43, 0xD8, 0x00, 0x80, 0x53, 0x57,
};
static const unsigned char dsa_pqver_q0[] = {
    0xCB, 0x74, 0xE6, 0x57, 0x37, 0x0F, 0x7A, 0x61,
    0x0B, 0x09, 0xCE, 0x91, 0x78, 0x06, 0x3C, 0x7F,
    0x20, 0xF5, 0xD1, 0x1E, 0x1D, 0xC2, 0x43, 0xBA,
    0x89, 0xC8, 0x4A, 0x49, 0x83, 0x38, 0xE1, 0x2D,
};
static const unsigned char dsa_pqver_q1[] = {
    0x85, 0x2B, 0x77, 0x9B, 0x1B, 0x70, 0x6F, 0x8C,
    0x10, 0xF3, 0x2F, 0xA9, 0xC2, 0xEE, 0xF6, 0x74,
    0x78, 0x5F, 0xD5, 0x5E, 0x2C, 0x34, 0xAF, 0xD1,
    0x25, 0x63, 0x96, 0x6D, 0x6D, 0x84, 0x68, 0x3F,
};
static const unsigned char dsa_pqver_seed0[] = {
    0x33, 0xDC, 0x43, 0xAF, 0xC4, 0x51, 0x5C, 0x3B,
    0x8B, 0x8A, 0x0D, 0x5D, 0xA2, 0x84, 0xDE, 0x6D,
    0xCC, 0x6C, 0xFD, 0x42, 0x37, 0x98, 0xFB, 0x66,
    0xAB, 0xD3, 0x73, 0x96, 0x1F, 0xC5, 0xD1, 0x46,
};
static const unsigned char dsa_pqver_seed1[] = {
    0xEE, 0xA4, 0x02, 0x70, 0x0B, 0x89, 0xB7, 0x96,
    0x52, 0x5C, 0x00, 0xC4, 0x8E, 0x14, 0x45, 0x0F,
    0x6A, 0x18, 0x00, 0xF7, 0x24, 0x52, 0x41, 0x0E,
    0x33, 0x41, 0xD2, 0x91, 0xC3, 0x16, 0x7D, 0x5D,
};
static const struct dsa_pqver_st dsa_pqver_data[] = {
    {
        ITM(dsa_pqver_p0),
        ITM(dsa_pqver_q0),
        ITM(dsa_pqver_seed0),
        1956,
        PASS
    },
    {
        ITM(dsa_pqver_p1),
        ITM(dsa_pqver_q1),
        ITM(dsa_pqver_seed1),
        685,
        FAIL
    },
};

/* dsa_siggen */
static const unsigned char dsa_siggen_msg0[]= {
    0x85, 0x01, 0x2F, 0x61, 0x1C, 0x36, 0xA8, 0xE1,
    0x54, 0x55, 0x13, 0xFA, 0x00, 0x58, 0x1E, 0xD4,
    0xF2, 0x4C, 0x54, 0x67, 0xB3, 0xEA, 0x48, 0x2C,
    0xD1, 0x27, 0xBE, 0x5F, 0x26, 0x35, 0xD4, 0x00,
    0xDD, 0x6C, 0xD8, 0xE8, 0x3C, 0x6D, 0x2D, 0x50,
    0x01, 0x53, 0xC7, 0xB5, 0xA3, 0x8E, 0x9A, 0x85,
    0xA6, 0x53, 0x8C, 0x46, 0x55, 0x02, 0xA1, 0x5E,
    0xEA, 0x6C, 0xCF, 0x4A, 0x86, 0xA9, 0x34, 0x1B,
    0x0B, 0xB6, 0x88, 0x9A, 0xDE, 0xC4, 0x27, 0x7F,
    0x93, 0xAA, 0x69, 0x54, 0x48, 0x33, 0x98, 0xA0,
    0x71, 0x45, 0x09, 0x5A, 0x51, 0xDF, 0xB6, 0x66,
    0x06, 0xB7, 0xAD, 0x64, 0xED, 0xC1, 0xFA, 0x6B,
    0xA8, 0x0F, 0xE8, 0x3C, 0x2E, 0x0C, 0xCB, 0xB0,
    0xAE, 0xDE, 0x25, 0x0C, 0x68, 0xA8, 0x15, 0x97,
    0xD0, 0xBC, 0x0B, 0x81, 0x15, 0xDC, 0x2B, 0xF3,
    0xF2, 0xB7, 0xA7, 0xA9, 0x74, 0xD6, 0x5D, 0xB8,
    0xB7, 0xD1, 0xFC, 0x5D, 0xCA, 0x69, 0x5D, 0x7D,
    0xC6, 0x1E, 0x37, 0x7D, 0xD3, 0xA9, 0x1E, 0xAE,
    0x60, 0x22, 0x3A, 0x4B, 0x7A, 0xB1, 0x3D, 0xA4,
    0x6D, 0xB3, 0xA5, 0x8C, 0x89, 0x91, 0xCF, 0xE6,
    0x5B, 0xF9, 0xB6, 0x87, 0x56, 0x75, 0xB9, 0x0B,
    0x08, 0x85, 0x32, 0x52, 0x81, 0x99, 0xA7, 0x98,
    0x44, 0x30, 0x3B, 0x44, 0xBC, 0xB8, 0xB2, 0x6D,
    0x59, 0x52, 0xD3, 0x84, 0x74, 0x65, 0x02, 0xF9,
    0x71, 0xB9, 0x16, 0x7A, 0x42, 0x62, 0xDE, 0x9B,
    0x66, 0xF6, 0x04, 0x2C, 0x1F, 0x96, 0xF7, 0x41,
    0x38, 0x1A, 0xF1, 0x8C, 0x8A, 0x40, 0x9F, 0x72,
    0x73, 0xF9, 0xE9, 0x35, 0x11, 0x1F, 0x02, 0x0C,
    0xB1, 0x51, 0xE8, 0x78, 0xDB, 0xE0, 0xB2, 0x35,
    0xBD, 0xC5, 0x84, 0x5B, 0x2B, 0x25, 0x66, 0x42,
    0x87, 0xE5, 0xA4, 0x77, 0x71, 0xB4, 0x4A, 0x59,
    0x31, 0xF1, 0x5A, 0xF5, 0x98, 0x9C, 0x61, 0xEA,
    0x52, 0x2F, 0x51, 0x85, 0xD9, 0x7F, 0x26, 0xDD,
    0x91, 0x63, 0x41, 0xD5, 0x99, 0xD1, 0x64, 0xCE,
    0xEE, 0x82, 0xD1, 0x73, 0x0A, 0x54, 0x3B, 0x03,
    0xD7, 0xC1, 0xF7, 0x01, 0xBD, 0x44, 0x99, 0xFE,
    0x9B, 0x1E, 0x2C, 0x8F, 0xF4, 0x55, 0xC5, 0x59,
    0x58, 0xAF, 0xCB, 0xAD, 0xB8, 0x22, 0x1A, 0x29,
    0xF3, 0x18, 0x39, 0x31, 0x9B, 0xFC, 0x08, 0x7E,
    0xBE, 0x45, 0xDA, 0x9E, 0xD8, 0x7F, 0x8C, 0x5D,
    0x10, 0xF9, 0xF8, 0xB4, 0xFA, 0x58, 0xE6, 0x28,
    0xB4, 0x6C, 0x70, 0x12, 0xD2, 0xFA, 0x49, 0xB2,
    0x5F, 0xD0, 0x81, 0x4A, 0xA1, 0xAA, 0xF8, 0x93,
    0xD2, 0x26, 0xE7, 0xDA, 0x7D, 0x79, 0xC5, 0xC8,
    0xC2, 0x9E, 0xA7, 0x01, 0xD5, 0x7A, 0xF9, 0x75,
    0x62, 0xDB, 0xDC, 0x93, 0x90, 0xDA, 0xA5, 0xA6,
    0x20, 0x58, 0x17, 0x9E, 0x47, 0x4E, 0xFB, 0xB8,
    0xFB, 0xCD, 0x2E, 0xF4, 0xCD, 0x49, 0x03, 0x90,
};
static struct dsa_siggen_st dsa_siggen_data[] = {
    {
        "SHA2-384",
        3072,
        256,
        ITM(dsa_siggen_msg0),
    },
};

/* dsa_sigver */
static const unsigned char dsa_sigver_p0[] = {
    0xD2, 0x90, 0x2E, 0x38, 0xA5, 0x32, 0xBB, 0x63,
    0xE0, 0xC3, 0x20, 0xD9, 0x26, 0x06, 0x21, 0x06,
    0x85, 0x3A, 0x4C, 0xE3, 0x13, 0x83, 0xCA, 0x43,
    0x8C, 0x9C, 0x76, 0xC0, 0x65, 0x60, 0x27, 0x7E,
    0x7C, 0xA0, 0x83, 0x9F, 0x65, 0x91, 0xF9, 0x16,
    0x5F, 0xE8, 0x60, 0x0C, 0xC6, 0x91, 0x20, 0x35,
    0xE7, 0xF1, 0x83, 0xE6, 0xF8, 0x8C, 0xBB, 0x4C,
    0xFF, 0xF5, 0x4D, 0x09, 0x8E, 0x83, 0x72, 0xCB,
    0x22, 0x5F, 0xD0, 0x85, 0xA9, 0x60, 0x3C, 0x4A,
    0xA6, 0xDD, 0x73, 0x1F, 0xCF, 0xD0, 0xD7, 0x42,
    0xB8, 0x72, 0x61, 0xDB, 0x91, 0xE3, 0xBB, 0x5C,
    0x21, 0x41, 0xFD, 0x97, 0xD0, 0x81, 0x72, 0x53,
    0x77, 0xE0, 0x15, 0x9E, 0xC0, 0xD0, 0x6A, 0xB4,
    0x7F, 0xF8, 0x63, 0x39, 0x1A, 0x25, 0x63, 0x84,
    0x4D, 0xBA, 0x2C, 0x29, 0x94, 0x28, 0xCE, 0x5B,
    0x9A, 0xC3, 0x14, 0xAD, 0x9D, 0x82, 0x1D, 0x8F,
    0xF3, 0xE9, 0x60, 0x65, 0x28, 0x0B, 0x0E, 0x48,
    0x6B, 0xCC, 0x05, 0x9D, 0x3B, 0x1F, 0x1D, 0x0A,
    0xA7, 0xF8, 0x22, 0xB0, 0xE1, 0x52, 0xB0, 0x25,
    0x8F, 0xEA, 0x25, 0x28, 0xC9, 0x6F, 0x44, 0xCD,
    0xA4, 0x16, 0x13, 0xE8, 0xD0, 0xDB, 0x43, 0x6E,
    0xCE, 0xEC, 0x0B, 0xA8, 0x3E, 0x53, 0x10, 0xA2,
    0x52, 0x0E, 0xBB, 0x63, 0x63, 0x84, 0x2C, 0x12,
    0x93, 0x29, 0x98, 0xAF, 0x38, 0x8F, 0x0B, 0x86,
    0x16, 0x99, 0x0E, 0x39, 0xA8, 0x4A, 0x0B, 0xCD,
    0xAA, 0x66, 0x8F, 0x4C, 0x15, 0xB7, 0xA5, 0xBB,
    0x22, 0x77, 0x8D, 0xE8, 0x05, 0x35, 0x2D, 0xAA,
    0x8D, 0x83, 0xDE, 0xBC, 0x15, 0x3D, 0xC2, 0x95,
    0x0E, 0x47, 0x85, 0x41, 0xAD, 0xE3, 0xB1, 0x70,
    0x76, 0x1B, 0x62, 0x9E, 0x96, 0x8B, 0x18, 0xD7,
    0xE3, 0xB5, 0xF8, 0x6E, 0x85, 0x67, 0x61, 0x54,
    0x7C, 0x85, 0x08, 0x91, 0xF4, 0x46, 0x3F, 0x01,
    0x99, 0x48, 0x18, 0x3C, 0x0D, 0xC7, 0x2D, 0xEC,
    0xA4, 0x11, 0x1D, 0x4F, 0x7F, 0xBF, 0x3A, 0xE8,
    0x9C, 0x1C, 0xAE, 0x9E, 0x30, 0x32, 0x1F, 0x81,
    0xEF, 0x14, 0xFE, 0x5C, 0xC2, 0x5C, 0xD0, 0x6A,
    0x7C, 0x18, 0x88, 0x9F, 0xC4, 0x97, 0x7D, 0x4B,
    0x3B, 0x01, 0xEB, 0x59, 0x58, 0x1C, 0x00, 0x6B,
    0x3E, 0xD6, 0x80, 0x80, 0x86, 0x06, 0x39, 0x88,
    0x0D, 0x23, 0x1E, 0xD6, 0x5E, 0x1F, 0x92, 0x3B,
    0xEC, 0x50, 0x0B, 0xA0, 0x83, 0x4F, 0x10, 0xDE,
    0xAF, 0x7B, 0x19, 0xBC, 0xBD, 0x72, 0xE6, 0x42,
    0xFE, 0xD7, 0xEF, 0x22, 0xD3, 0x83, 0x6B, 0x30,
    0xA3, 0x95, 0x0D, 0x3E, 0x61, 0x9E, 0xBC, 0x0E,
    0x14, 0x7E, 0x61, 0x05, 0x3D, 0xBA, 0x4E, 0xEF,
    0x31, 0x75, 0x5D, 0x10, 0x1E, 0xBA, 0xBD, 0xBA,
    0x89, 0x4D, 0x3A, 0x5B, 0x03, 0xB1, 0xAE, 0x27,
    0x47, 0x2D, 0x03, 0xB1, 0x8A, 0x74, 0x1B, 0xF3,
};
static const unsigned char dsa_sigver_q0[] = {
    0xAC, 0x71, 0x8D, 0x81, 0x05, 0x2F, 0xAB, 0x72,
    0xB9, 0x96, 0x94, 0x98, 0xB5, 0x19, 0x2B, 0xE2,
    0x78, 0x06, 0xAA, 0x32, 0xFC, 0xB9, 0xD2, 0xFD,
    0x26, 0xC4, 0x50, 0x6F, 0x81, 0xD8, 0x04, 0xAB,
};
static const unsigned char dsa_sigver_g0[] = {
    0x3D, 0x0B, 0x46, 0x39, 0x13, 0xFF, 0x67, 0xA8,
    0x8C, 0xE8, 0x8A, 0x46, 0x46, 0x9A, 0xE6, 0x70,
    0xA1, 0xF5, 0x48, 0xF5, 0x84, 0xF8, 0x93, 0x57,
    0x9A, 0x4F, 0x2C, 0xD4, 0x26, 0x49, 0x1C, 0x83,
    0x64, 0x14, 0x0B, 0x5B, 0xEF, 0x6F, 0x6F, 0x91,
    0x14, 0xC5, 0x4D, 0xE8, 0x86, 0x47, 0x5C, 0xFC,
    0xAE, 0xBF, 0xD8, 0x32, 0xE2, 0x96, 0xB9, 0x61,
    0x70, 0x3F, 0x24, 0x29, 0xFA, 0x41, 0x5D, 0x8E,
    0xD0, 0xB0, 0xF1, 0x26, 0xD5, 0x7C, 0xE6, 0x17,
    0x48, 0xE5, 0x04, 0x0E, 0x58, 0x14, 0xEE, 0xBA,
    0x64, 0xE9, 0xF1, 0x6A, 0x7C, 0x17, 0xAB, 0x7B,
    0x28, 0xCF, 0x69, 0x7D, 0xDC, 0x54, 0xCA, 0xF2,
    0x4C, 0x22, 0x17, 0xDD, 0xC3, 0x1A, 0x02, 0xE2,
    0x8E, 0xE6, 0xA4, 0xFB, 0x84, 0x27, 0x2B, 0xE8,
    0x14, 0xF3, 0x3D, 0xAC, 0x59, 0x0C, 0xAB, 0x69,
    0x0E, 0x73, 0xDF, 0x82, 0xC1, 0xDE, 0xD7, 0xD9,
    0xA7, 0xCA, 0x8F, 0x4B, 0xCE, 0x8A, 0x05, 0xBD,
    0x07, 0xC8, 0x29, 0xBB, 0x46, 0x29, 0x2A, 0x4F,
    0xA7, 0x12, 0x19, 0x91, 0x01, 0xA0, 0xAE, 0x16,
    0xEF, 0xC1, 0xC5, 0x4B, 0x03, 0xF0, 0x53, 0xDC,
    0xFC, 0x1C, 0xC4, 0x73, 0xB7, 0xBF, 0x53, 0xEB,
    0x19, 0x63, 0xCA, 0x30, 0x53, 0x54, 0x12, 0x90,
    0x0E, 0x43, 0xC7, 0x66, 0xFF, 0x29, 0xFC, 0xA4,
    0xE8, 0xF6, 0x4B, 0x76, 0x3B, 0xA2, 0x65, 0x6B,
    0x9E, 0xFA, 0xBA, 0x5B, 0x54, 0x94, 0x34, 0xF6,
    0xD1, 0x20, 0x2A, 0xF7, 0x39, 0x72, 0xA3, 0xDB,
    0xD1, 0x8F, 0xA6, 0x4D, 0x1B, 0xB1, 0x2D, 0xC5,
    0x7F, 0xC5, 0x2C, 0x7E, 0x6D, 0xD9, 0xC8, 0xC2,
    0x19, 0xC0, 0xC4, 0xC2, 0x77, 0xD9, 0x4C, 0x63,
    0x77, 0x59, 0x0C, 0x5B, 0xFE, 0x69, 0xEF, 0xBF,
    0x58, 0x47, 0x69, 0x3E, 0x49, 0xA7, 0x1B, 0x98,
    0x6C, 0xE5, 0xA7, 0x42, 0x8B, 0x0E, 0x68, 0x05,
    0x48, 0x80, 0x39, 0xF4, 0x02, 0x9E, 0xE2, 0x9F,
    0x1C, 0xDA, 0x24, 0xC5, 0xB8, 0xEC, 0x03, 0xEA,
    0x7A, 0x00, 0xDF, 0xCF, 0x58, 0xD0, 0xE7, 0xB7,
    0xF3, 0xE7, 0x36, 0xDD, 0x1F, 0x65, 0xF9, 0x2D,
    0x6F, 0xC3, 0xE4, 0x72, 0xFD, 0xBA, 0x58, 0x8D,
    0xB5, 0xDF, 0x61, 0x3D, 0x3B, 0xB5, 0xF3, 0x08,
    0xE7, 0x21, 0x5A, 0x7D, 0xFF, 0x02, 0x1E, 0x0E,
    0x4E, 0xB5, 0x0D, 0x3D, 0x33, 0xF4, 0xA7, 0x6D,
    0xF7, 0x96, 0xC2, 0x96, 0x85, 0x33, 0x9C, 0x58,
    0x72, 0x5C, 0x97, 0x73, 0x0E, 0xDC, 0x5C, 0x6B,
    0x3D, 0x68, 0xF7, 0xF0, 0x0F, 0xCC, 0x01, 0xBB,
    0x47, 0x01, 0x3C, 0xB0, 0x52, 0x48, 0x70, 0xB8,
    0x0C, 0x0F, 0x04, 0xB5, 0x8F, 0x70, 0x50, 0x12,
    0x7C, 0x9D, 0xD1, 0xC1, 0x2B, 0xFE, 0x95, 0x31,
    0x8F, 0x2D, 0xFA, 0xAC, 0xAE, 0x24, 0xDD, 0x13,
    0xDA, 0x76, 0xC2, 0x34, 0xB9, 0x4A, 0x3E, 0xC3,
};
static const unsigned char dsa_sigver_pub0[] = {
    0x91, 0x78, 0x1C, 0xBA, 0x8A, 0x2F, 0xF6, 0xEC,
    0x9B, 0xD4, 0x73, 0x2C, 0x1F, 0xC0, 0xFE, 0x79,
    0xCF, 0xAC, 0x0C, 0x3C, 0x0D, 0x81, 0x85, 0x3D,
    0xCD, 0x67, 0x2B, 0x77, 0x99, 0x4A, 0x51, 0x48,
    0x58, 0x03, 0xC9, 0x68, 0xE6, 0x19, 0x26, 0x28,
    0xDC, 0x86, 0x9F, 0x8F, 0xCE, 0xDD, 0x1B, 0xCD,
    0xDD, 0x63, 0x4E, 0xCE, 0x76, 0x4D, 0xD5, 0x0D,
    0x71, 0x73, 0x04, 0x03, 0x9C, 0x35, 0xD0, 0x56,
    0x98, 0x25, 0xA7, 0x06, 0xF2, 0x6B, 0xA9, 0x9F,
    0x9A, 0xB1, 0x2A, 0xBD, 0xB7, 0x71, 0x62, 0x99,
    0x06, 0x47, 0x77, 0x22, 0x57, 0xFA, 0x24, 0x21,
    0x21, 0xB0, 0x78, 0x26, 0x7E, 0xEC, 0xB6, 0xEB,
    0x82, 0x15, 0x53, 0x68, 0xAE, 0xAC, 0x5B, 0xDC,
    0xA6, 0x7F, 0x6B, 0x26, 0xE6, 0x59, 0x22, 0x55,
    0x1C, 0x3B, 0xFD, 0xD5, 0x90, 0xA9, 0x6F, 0xB3,
    0xE6, 0x99, 0x8E, 0x26, 0x72, 0xA1, 0x02, 0xAA,
    0x37, 0xF6, 0x89, 0x68, 0xF1, 0x5A, 0x6D, 0x54,
    0xAD, 0x9D, 0xF3, 0x03, 0xBE, 0x3F, 0x9D, 0x85,
    0x38, 0x25, 0xB8, 0xDF, 0xB8, 0x43, 0x21, 0xCA,
    0xF8, 0xDC, 0x12, 0x40, 0x1E, 0xEF, 0x37, 0x40,
    0xCE, 0x0E, 0x02, 0x88, 0x63, 0x98, 0x2E, 0x93,
    0x89, 0xB0, 0x43, 0xAC, 0x0E, 0x62, 0x4C, 0x38,
    0x14, 0xAC, 0x0C, 0xA3, 0x03, 0x10, 0x8E, 0xB4,
    0x60, 0x10, 0x9B, 0xCC, 0x16, 0xA7, 0xB8, 0x1D,
    0x73, 0x81, 0x26, 0x12, 0xA8, 0x9A, 0xFE, 0x17,
    0xBB, 0x2D, 0x33, 0x5E, 0x8C, 0xA4, 0x80, 0xBF,
    0x84, 0x37, 0xCA, 0x0F, 0x50, 0x23, 0x79, 0x20,
    0x2A, 0x8E, 0xD1, 0x1F, 0x9F, 0x89, 0x98, 0x4F,
    0xF5, 0xB6, 0x0F, 0xB9, 0x3C, 0xFC, 0x6C, 0x00,
    0xBC, 0x76, 0x2F, 0xB4, 0xFD, 0x22, 0x13, 0x37,
    0x26, 0xCD, 0x9B, 0xAF, 0x4C, 0x89, 0x16, 0xD0,
    0x73, 0x44, 0xF9, 0x71, 0x60, 0xA2, 0x3E, 0xFE,
    0x24, 0xFE, 0xFC, 0xFE, 0x90, 0x91, 0xED, 0x92,
    0x57, 0x0A, 0xFA, 0xEB, 0x21, 0x99, 0xE3, 0x9A,
    0xFF, 0x5C, 0x74, 0x85, 0xC2, 0x6D, 0x83, 0x90,
    0xEE, 0x84, 0x05, 0x1A, 0x00, 0xAC, 0x87, 0xA7,
    0x78, 0x87, 0xCA, 0x70, 0xFC, 0xB0, 0xF4, 0x3B,
    0x61, 0x7C, 0xD0, 0x09, 0x63, 0x2B, 0x5E, 0xC2,
    0xFE, 0x15, 0x41, 0xB3, 0x9F, 0xFC, 0x19, 0xE3,
    0x4D, 0x3C, 0x6F, 0x89, 0xEB, 0x8A, 0x43, 0xEC,
    0x8E, 0xFB, 0xEC, 0xCD, 0x99, 0x2E, 0x4B, 0x02,
    0x99, 0xAC, 0xC6, 0x62, 0xAA, 0xC5, 0x0F, 0xA3,
    0x0B, 0xBB, 0xCD, 0x51, 0x0C, 0x19, 0xA7, 0x7A,
    0x43, 0x6C, 0xAA, 0x26, 0x28, 0x2A, 0xC9, 0x9D,
    0x97, 0xAE, 0x83, 0x74, 0xDA, 0xC4, 0x03, 0x98,
    0x94, 0x58, 0x28, 0xBC, 0x32, 0x1D, 0xD4, 0xF2,
    0x6F, 0x89, 0x92, 0xD4, 0x80, 0x9B, 0xDE, 0x6B,
    0xC5, 0x6F, 0xDB, 0x7A, 0x03, 0x1C, 0xF5, 0x55,
};
static const unsigned char dsa_sigver_msg0[] = {
    0x32, 0xE9, 0x64, 0x47, 0xED, 0x3B, 0xF0, 0xC0,
    0xCA, 0xC2, 0x90, 0xF5, 0x10, 0x60, 0x99, 0x82,
    0x4D, 0x13, 0x44, 0xFA, 0x92, 0xD1, 0xFD, 0x50,
    0x26, 0x80, 0xEA, 0x7B, 0x7D, 0xC5, 0xF0, 0xB7,
};
static const unsigned char dsa_sigver_msg1[] = {
    0x31, 0xE9, 0x64, 0x47, 0xED, 0x3B, 0xF0, 0xC0,
    0xCA, 0xC2, 0x90, 0xF5, 0x10, 0x60, 0x99, 0x82,
    0x4D, 0x13, 0x44, 0xFA, 0x92, 0xD1, 0xFD, 0x50,
    0x26, 0x80, 0xEA, 0x7B, 0x7D, 0xC5, 0xF0, 0xB7
};
static const unsigned char dsa_sigver_r0[] = {
    0x2A, 0x24, 0x0F, 0xA7, 0x04, 0xF1, 0xE0, 0x60,
    0x3B, 0x07, 0xDE, 0xB6, 0x5F, 0x01, 0x20, 0x81,
    0xDD, 0x64, 0x22, 0x0F, 0x9F, 0x2E, 0x67, 0x33,
    0xB7, 0x56, 0xDE, 0x17, 0xD0, 0xED, 0x9D, 0x30,
};
static const unsigned char dsa_sigver_s0[] = {
    0x15, 0x93, 0x81, 0x6E, 0xC2, 0x07, 0x6E, 0x06,
    0x6A, 0xBF, 0x62, 0xBF, 0x93, 0xA6, 0xCB, 0x6E,
    0xBA, 0x1E, 0x72, 0x57, 0x27, 0x0E, 0x85, 0x9E,
    0x8C, 0x42, 0x9A, 0x41, 0x63, 0x27, 0x74, 0x4F,
};
static const struct dsa_sigver_st dsa_sigver_data[] = {
    {
        "SHA2-512",
        ITM(dsa_sigver_p0),
        ITM(dsa_sigver_q0),
        ITM(dsa_sigver_g0),
        ITM(dsa_sigver_pub0),
        ITM(dsa_sigver_msg0),
        ITM(dsa_sigver_r0),
        ITM(dsa_sigver_s0),
        PASS,
    },
    {
        "SHA2-512",
        ITM(dsa_sigver_p0),
        ITM(dsa_sigver_q0),
        ITM(dsa_sigver_g0),
        ITM(dsa_sigver_pub0),
        ITM(dsa_sigver_msg1),
        ITM(dsa_sigver_r0),
        ITM(dsa_sigver_s0),
        FAIL,
    },
};

#endif /* OPENSSL_NO_DSA */

struct cipher_st {
    const char *alg;
    const unsigned char *key;
    size_t key_len;
    const unsigned char *iv;
    size_t iv_len;
    const unsigned char *pt;
    size_t pt_len;
    const unsigned char *ct;
    size_t ct_len;
};

struct cipher_ccm_st {
    const char *alg;
    const unsigned char *key;
    size_t key_len;
    const unsigned char *iv;
    size_t iv_len;
    const unsigned char *aad;
    size_t aad_len;
    const unsigned char *pt;
    size_t pt_len;
    const unsigned char *ct; /* includes the tag */
    size_t ct_len;
};

struct cipher_gcm_st {
    const char *alg;
    const unsigned char *key;
    size_t key_len;
    const unsigned char *iv;
    size_t iv_len;
    const unsigned char *aad;
    size_t aad_len;
    const unsigned char *pt;
    size_t pt_len;
    const unsigned char *ct;
    size_t ct_len;
    const unsigned char *tag;
    size_t tag_len;
};


static const unsigned char cipher_enc_pt0[] = {
    0xF3, 0x44, 0x81, 0xEC, 0x3C, 0xC6, 0x27, 0xBA,
    0xCD, 0x5D, 0xC3, 0xFB, 0x08, 0xF2, 0x73, 0xE6,
};
static const unsigned char cipher_enc_pt1[] = {
    0x1B, 0x07, 0x7A, 0x6A, 0xF4, 0xB7, 0xF9, 0x82,
    0x29, 0xDE, 0x78, 0x6D, 0x75, 0x16, 0xB6, 0x39,
};
static const unsigned char cipher_enc_pt2[] = {
    0x91, 0x07, 0x41, 0x31, 0xF1, 0xF8, 0x6C, 0xCD,
    0x54, 0x8D, 0x22, 0xA6, 0x93, 0x40, 0xFF, 0x39,
};
static const unsigned char cipher_enc_pt3[] = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
    0x10, 0x11, 0x12, 0x13
};
static const unsigned char cipher_enc_key0[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};
static const unsigned char cipher_enc_key1[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};
static const unsigned char cipher_enc_key2[] = {
    0xE8, 0x70, 0x13, 0x1C, 0xE7, 0x03, 0xD6, 0x51,
    0x4E, 0x76, 0x1F, 0x95, 0xE6, 0xEE, 0x9E, 0xFB,
};
static const unsigned char cipher_enc_key3[] = {
    0xff, 0xfe, 0xfd, 0xfc, 0xfb, 0xfa, 0xf9, 0xf8,
    0xf7, 0xf6, 0xf5, 0xf4, 0xf3, 0xf2, 0xf1, 0xf0,
    0xbf, 0xbe, 0xbd, 0xbc, 0xbb, 0xba, 0xb9, 0xb8,
    0xb7, 0xb6, 0xb5, 0xb4, 0xb3, 0xb2, 0xb1, 0xb0,
};
static const unsigned char cipher_enc_ct0[] = {
    0x03, 0x36, 0x76, 0x3E, 0x96, 0x6D, 0x92, 0x59,
    0x5A, 0x56, 0x7C, 0xC9, 0xCE, 0x53, 0x7F, 0x5E,
};
static const unsigned char cipher_enc_ct1[] = {
    0x27, 0x5C, 0xFC, 0x04, 0x13, 0xD8, 0xCC, 0xB7,
    0x05, 0x13, 0xC3, 0x85, 0x9B, 0x1D, 0x0F, 0x72,
};
static const unsigned char cipher_enc_ct2[] = {
    0x3A, 0xF6, 0x4C, 0x70, 0x37, 0xEE, 0x48, 0x13,
    0xD8, 0x5F, 0xE9, 0xB3, 0x7F, 0xE6, 0x6A, 0xD4,
};
static const unsigned char cipher_enc_ct3[] = {
    0x9d, 0x84, 0xc8, 0x13, 0xf7, 0x19, 0xaa, 0x2c,
    0x7b, 0xe3, 0xf6, 0x61, 0x71, 0xc7, 0xc5, 0xc2,
    0xed, 0xbf, 0x9d, 0xac,
};
static const unsigned char cipher_enc_iv0[] = {
    0x00,
};
static const unsigned char cipher_enc_iv1[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};
static const unsigned char cipher_enc_iv2[] = {
    0x53, 0xF2, 0x25, 0xD8, 0xDE, 0x97, 0xF1, 0x4B,
    0xFE, 0x3E, 0xC6, 0x5E, 0xC3, 0xFF, 0xF7, 0xD3,
};
static const unsigned char cipher_enc_iv3[] = {
    0x9a, 0x78, 0x56, 0x34, 0x12, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

static const struct cipher_st cipher_enc_data[] = {
    {
        "AES-128-ECB",
        ITM(cipher_enc_key0),
        NULL, 0,     /* iv */
        ITM(cipher_enc_pt0),
        ITM(cipher_enc_ct0),
    },
    {
        "AES-192-CBC",
        ITM(cipher_enc_key1),
        ITM(cipher_enc_iv1),
        ITM(cipher_enc_pt1),
        ITM(cipher_enc_ct1),
    },
    {
        "AES-128-CTR",
        ITM(cipher_enc_key2),
        ITM(cipher_enc_iv2),
        ITM(cipher_enc_pt2),
        ITM(cipher_enc_ct2),
    },
    {
        "AES-128-XTS",
         ITM(cipher_enc_key3),
         ITM(cipher_enc_iv3),
         ITM(cipher_enc_pt3),
         ITM(cipher_enc_ct3),
    }
};

static const unsigned char aes_ccm_enc_pt0[] = {
    0xDA, 0x6A, 0xDC, 0xB2, 0xA5, 0xCC, 0xC3, 0xE7,
    0xFC, 0x4C, 0xDF, 0x0A, 0xAB, 0xDC, 0x3B, 0x38,
    0x26, 0x8B, 0x21, 0x82, 0xCA, 0x26, 0xE6, 0x2C,
};
static const unsigned char aes_ccm_enc_key0[] = {
    0xDE, 0xF8, 0x67, 0xBC, 0x40, 0xB6, 0x7C, 0xAA,
    0x4A, 0x00, 0xE8, 0x07, 0x24, 0x52, 0xAE, 0x72,
};
static const unsigned char aes_ccm_enc_aad0[] = {
    0x29, 0x8D, 0x01, 0x92, 0x3B, 0x50, 0x1F, 0x66,
    0xCE, 0xD3, 0x57, 0x4B, 0x7B, 0x4F, 0x07, 0x57,
};
static const unsigned char aes_ccm_enc_iv0[] = {
    0x44, 0xEE, 0x48, 0x11, 0x4D, 0x3B, 0x71, 0x33,
    0xF0, 0xF7, 0x12, 0xA2, 0xA5,
};
static const unsigned char aes_ccm_enc_ct0[] = {
    0x30, 0x4D, 0x19, 0x31, 0x02, 0x66, 0x95, 0x2E,
    0x23, 0x6D, 0xF4, 0xB9, 0xDF, 0xFE, 0xCA, 0x92,
    0x04, 0x2A, 0x32, 0xB5, 0x08, 0x2C, 0x8B, 0x6F,
    0x1B, 0x14, 0xB8, 0x62, 0x8B, 0xA0, 0x12, 0x4E,
    0x6A, 0x6F, 0x85, 0x86, 0x24, 0x6A, 0x83, 0xE0,
};
static const struct cipher_ccm_st aes_ccm_enc_data[] = {
    {
        "AES-128-CCM",
        ITM(aes_ccm_enc_key0),
        ITM(aes_ccm_enc_iv0),
        ITM(aes_ccm_enc_aad0),
        ITM(aes_ccm_enc_pt0),
        ITM(aes_ccm_enc_ct0),
    },
};

static const unsigned char aes_gcm_enc_pt0[] = {
    0x97,
};
static const unsigned char aes_gcm_enc_key0[] = {
    0x3E, 0x77, 0x38, 0xA6, 0x0A, 0xB3, 0x95, 0x90,
    0xFA, 0x8B, 0x3B, 0xCF, 0xE1, 0xA3, 0x8C, 0x7D,
};
static const unsigned char aes_gcm_enc_aad0[] = {
    0xE7, 0xEF, 0xCB, 0x0F, 0x3D, 0x94, 0x1D, 0x0F,
    0x3D, 0x65, 0x69, 0xFC, 0xDA, 0x77, 0x36, 0x5E,
    0xB9, 0x20, 0xF8, 0xDA, 0x81, 0xDF, 0x6B, 0x4C,
    0x2A, 0x70, 0x5E, 0xE3, 0x07, 0xCE, 0xCF, 0x62,
    0x84,
};
static const unsigned char aes_gcm_enc_iv0[] = {
    0x01, 0x02, 0x03, 0x04, 0xEB, 0xED, 0x2F, 0x4B,
    0xD1, 0x20, 0x2C, 0xCD,
};
static const unsigned char aes_gcm_enc_ct0[] = {
    0x63,
};
static const unsigned char aes_gcm_enc_tag0[] = {
    0xA2, 0x8E, 0xA5, 0xF6, 0x6A, 0x1C, 0xE6, 0xD7,
    0xFF, 0xCD, 0x7F, 0x49,
};
static const struct cipher_gcm_st aes_gcm_enc_data[] = {
    {
        "AES-128-GCM",
        ITM(aes_gcm_enc_key0),
        ITM(aes_gcm_enc_iv0),
        ITM(aes_gcm_enc_aad0),
        ITM(aes_gcm_enc_pt0),
        ITM(aes_gcm_enc_ct0),
        ITM(aes_gcm_enc_tag0),
    },
};

#ifndef OPENSSL_NO_DH

struct dh_safe_prime_keygen_st {
    const char *group_name;
};

struct dh_safe_prime_keyver_st {
    const char *group_name;
    const unsigned char *pub;
    size_t pub_len;
    const unsigned char *priv;
    size_t priv_len;
    int pass;
};

static const struct dh_safe_prime_keygen_st dh_safe_prime_keygen_data[] = {
    { "ffdhe2048" },
};

static const unsigned char dh_safe_prime_keyver_priv0[] = {
    0x39, 0x9C, 0x08, 0x8E, 0x4A, 0x1E, 0x1A, 0x03,
    0x18, 0x5A, 0x72, 0x4F, 0xCB, 0x13, 0xFB, 0x8F,
    0x7F, 0x14, 0x12, 0x48, 0x9A, 0x18, 0x03, 0x1C,
    0x03, 0x98, 0x7E, 0x3C, 0xEF, 0x57, 0xF2, 0x1E,
    0xD5, 0x54, 0x5C, 0x0F, 0x36, 0x5E, 0xB0, 0xF8,
    0xD1, 0x27, 0x79, 0x24, 0x69, 0xB5, 0x7C, 0xF1,
    0x39, 0xFC, 0xE3, 0x79, 0xF0, 0xD6, 0xA0, 0xE1,
    0xA5, 0xA7, 0x65, 0x01, 0xBA, 0xFE, 0xBB, 0x28,
    0xF0, 0x9C, 0x49, 0x90, 0x68, 0xB4, 0xE7, 0xE2,
    0xB6, 0xBF, 0xB9, 0xF8, 0x96, 0xAA, 0xF9, 0xE6,
    0x0B, 0x8A, 0x86, 0x0D, 0x31, 0x2C, 0x90, 0xDA,
    0x4E, 0xFB, 0xE3, 0x59, 0x3F, 0xFB, 0x14, 0x33,
    0xF6, 0xBD, 0xDA, 0x5D, 0x27, 0xCC, 0x7C, 0x1C,
    0x30, 0xC8, 0xB2, 0xAF, 0x2A, 0xA8, 0x25, 0x97,
    0xDC, 0x5E, 0xEF, 0x22, 0xCB, 0xF6, 0x88, 0x83,
    0xD0, 0x47, 0x3F, 0x5D, 0xF3, 0x2A, 0xE0, 0xCC,
    0x86, 0x49, 0x5A, 0x8F, 0x67, 0xF6, 0xC4, 0xD8,
    0x3F, 0x09, 0xE8, 0x49, 0x89, 0x2E, 0xC0, 0xB4,
    0x9C, 0x06, 0x9C, 0x40, 0x10, 0xFB, 0x20, 0xC6,
    0xF1, 0x5F, 0x52, 0x3F, 0x21, 0xBF, 0xBB, 0x13,
    0x6A, 0x81, 0x1C, 0xFF, 0x19, 0x2A, 0x36, 0xD2,
    0x13, 0x23, 0x33, 0xD1, 0xEF, 0x34, 0xF3, 0xA6,
    0xD5, 0x56, 0xAF, 0x1A, 0x63, 0xFC, 0x95, 0x86,
    0xD5, 0xEB, 0xBF, 0x49, 0x84, 0x73, 0x30, 0x1D,
    0xE2, 0xA1, 0xAE, 0x6D, 0x20, 0x69, 0xAE, 0xB6,
    0x1A, 0xB9, 0xF2, 0x7A, 0xE3, 0x17, 0x4E, 0x7C,
    0x8F, 0xE8, 0x34, 0x02, 0x59, 0xB4, 0x54, 0x9A,
    0x8D, 0x19, 0x04, 0x2A, 0x8E, 0xF8, 0x05, 0xA3,
    0x98, 0x5B, 0xBD, 0xB4, 0x6A, 0xDA, 0xAA, 0x1F,
    0xFC, 0x56, 0xA0, 0x4D, 0x22, 0x66, 0x31, 0xEA,
    0x8A, 0xC8, 0x61, 0x12, 0x40, 0xF0, 0x38, 0x0B,
    0xA3, 0x23, 0x40, 0x97, 0x7A, 0x18, 0xE3, 0x42,
};
static const unsigned char dh_safe_prime_keyver_pub0[] = {
    0xFA, 0xDA, 0x86, 0x67, 0xE9, 0x12, 0x67, 0x79,
    0x50, 0xAE, 0x64, 0x0B, 0x07, 0x47, 0x8F, 0xA5,
    0xD1, 0x27, 0x6C, 0xFC, 0x10, 0xD8, 0x90, 0x8D,
    0x93, 0x75, 0xAD, 0x31, 0xBD, 0x97, 0xBE, 0xD5,
    0xB5, 0x59, 0x2F, 0x37, 0x52, 0x32, 0x30, 0x75,
    0xD3, 0xA5, 0x36, 0x10, 0x62, 0x4C, 0x82, 0x06,
    0xB9, 0x29, 0xAE, 0x14, 0xD1, 0xB0, 0xD0, 0x23,
    0x7A, 0xE6, 0xEA, 0x7E, 0x8E, 0xE3, 0xC7, 0xEB,
    0x43, 0x78, 0xFA, 0x9A, 0x40, 0x9A, 0x6F, 0xF5,
    0x42, 0x8A, 0xF7, 0xF3, 0x92, 0xE6, 0x5D, 0x68,
    0x9B, 0x2A, 0x91, 0xB5, 0x37, 0x33, 0x3F, 0x35,
    0xA5, 0xFB, 0x54, 0xD1, 0x3C, 0x46, 0xC6, 0x3C,
    0x16, 0x3A, 0xD7, 0xF8, 0x55, 0x48, 0x9A, 0xB7,
    0xB1, 0x40, 0xBF, 0xAF, 0x26, 0x1B, 0x07, 0x0F,
    0x11, 0x04, 0x63, 0x06, 0xDA, 0x2D, 0x45, 0x0E,
    0x7F, 0x17, 0xA4, 0x38, 0xBD, 0x68, 0x5A, 0xA9,
    0xC4, 0x7F, 0x7E, 0xC7, 0xF0, 0xFC, 0x74, 0x87,
    0x55, 0xCD, 0x35, 0xA8, 0xAA, 0x59, 0xA7, 0xFA,
    0xC1, 0x34, 0x67, 0x04, 0xD8, 0xCC, 0xE1, 0x77,
    0x60, 0xE1, 0xBE, 0xC0, 0xA5, 0x52, 0xA4, 0x72,
    0x3A, 0x19, 0xFA, 0x76, 0xC8, 0x67, 0x60, 0x5E,
    0x1C, 0x43, 0xF4, 0x50, 0xA0, 0xCB, 0x33, 0x77,
    0x2D, 0x2D, 0x3B, 0x5D, 0x7D, 0x72, 0x2E, 0x38,
    0xCD, 0x71, 0xB1, 0xBF, 0xB5, 0x10, 0x80, 0xCD,
    0xA4, 0x5D, 0x70, 0x6E, 0xD5, 0x7E, 0xA2, 0xAA,
    0xDC, 0xA4, 0x7C, 0x7A, 0x7D, 0x21, 0x09, 0x6A,
    0x14, 0xB2, 0x21, 0x24, 0xA4, 0xF4, 0x6C, 0xD2,
    0xBA, 0x76, 0x99, 0xD7, 0x69, 0x44, 0xA8, 0x66,
    0x85, 0x08, 0x50, 0xBA, 0x42, 0x37, 0xA2, 0xC2,
    0xD5, 0x45, 0x7E, 0x7B, 0xE9, 0x4A, 0xAE, 0xEE,
    0x84, 0x2A, 0xEB, 0xA9, 0x4A, 0x69, 0x40, 0x83,
    0xBA, 0xCC, 0x1E, 0x1B, 0x25, 0x56, 0x13, 0x88,
};
static const struct dh_safe_prime_keyver_st dh_safe_prime_keyver_data[] = {
    {
        "ffdhe2048",
        ITM(dh_safe_prime_keyver_pub0),
        ITM(dh_safe_prime_keyver_priv0),
        PASS
    },
};

#endif /* OPENSSL_NO_DH */

struct rsa_keygen_st {
    size_t mod;
    const unsigned char *e;
    size_t e_len;
    const unsigned char *xp1;
    size_t xp1_len;
    const unsigned char *xp2;
    size_t xp2_len;
    const unsigned char *xp;
    size_t xp_len;
    const unsigned char *xq1;
    size_t xq1_len;
    const unsigned char *xq2;
    size_t xq2_len;
    const unsigned char *xq;
    size_t xq_len;

    const unsigned char *p1;
    size_t p1_len;
    const unsigned char *p2;
    size_t p2_len;
    const unsigned char *q1;
    size_t q1_len;
    const unsigned char *q2;
    size_t q2_len;
    const unsigned char *p;
    size_t p_len;
    const unsigned char *q;
    size_t q_len;
    const unsigned char *n;
    size_t n_len;
    const unsigned char *d;
    size_t d_len;
};

static const unsigned char rsa_keygen0_e[] = {
    0x01,0x00,0x01
};
static const unsigned char rsa_keygen0_xp[] = {
    0xcf,0x72,0x1b,0x9a,0xfd,0x0d,0x22,0x1a,0x74,0x50,0x97,0x22,0x76,0xd8,0xc0,
    0xc2,0xfd,0x08,0x81,0x05,0xdd,0x18,0x21,0x99,0x96,0xd6,0x5c,0x79,0xe3,0x02,
    0x81,0xd7,0x0e,0x3f,0x3b,0x34,0xda,0x61,0xc9,0x2d,0x84,0x86,0x62,0x1e,0x3d,
    0x5d,0xbf,0x92,0x2e,0xcd,0x35,0x3d,0x6e,0xb9,0x59,0x16,0xc9,0x82,0x50,0x41,
    0x30,0x45,0x67,0xaa,0xb7,0xbe,0xec,0xea,0x4b,0x9e,0xa0,0xc3,0x05,0xb3,0x88,
    0xd4,0x4c,0xac,0xeb,0xe4,0x03,0xc6,0xca,0xcb,0xd9,0xd3,0x4e,0xf6,0x7f,0x2c,
    0x27,0x1e,0x08,0x6c,0xc2,0xd6,0x45,0x1f,0x84,0xe4,0x3c,0x97,0x19,0xde,0xb8,
    0x55,0xaf,0x0e,0xcf,0x9e,0xb0,0x9c,0x20,0xd3,0x1f,0xa8,0xd7,0x52,0xc2,0x95,
    0x1c,0x80,0x15,0x42,0x4d,0x4f,0x19,0x16
};
static const unsigned char rsa_keygen0_xp1[] = {
    0xac,0x5f,0x7f,0x6e,0x33,0x3e,0x97,0x3a,0xb3,0x17,0x44,0xa9,0x0f,0x7a,0x54,
    0x70,0x27,0x06,0x93,0xd5,0x49,0xde,0x91,0x83,0xbc,0x8a,0x7b,0x95
};
static const unsigned char rsa_keygen0_xp2[] = {
    0x0b,0xf6,0xe8,0x79,0x5a,0x81,0xae,0x90,0x1d,0xa4,0x38,0x74,0x9c,0x0e,0x6f,
    0xe0,0x03,0xcf,0xc4,0x53,0x16,0x32,0x17,0xf7,0x09,0x5f,0xd9
};
static const unsigned char rsa_keygen0_xq[] = {
    0xfe,0xab,0xf2,0x7c,0x16,0x4a,0xf0,0x8d,0x31,0xc6,0x0a,0x82,0xe2,0xae,0xbb,
    0x03,0x7e,0x7b,0x20,0x4e,0x64,0xb0,0x16,0xad,0x3c,0x01,0x1a,0xd3,0x54,0xbf,
    0x2b,0xa4,0x02,0x9e,0xc3,0x0d,0x60,0x3d,0x1f,0xb9,0xc0,0x0d,0xe6,0x97,0x68,
    0xbb,0x8c,0x81,0xd5,0xc1,0x54,0x96,0x0f,0x99,0xf0,0xa8,0xa2,0xf3,0xc6,0x8e,
    0xec,0xbc,0x31,0x17,0x70,0x98,0x24,0xa3,0x36,0x51,0xa8,0x54,0xbd,0x9a,0x89,
    0x99,0x6e,0x57,0x5e,0xd0,0x39,0x86,0xc3,0xa3,0x1b,0xc7,0xcf,0xc4,0x4f,0x47,
    0x25,0x9e,0x2c,0x79,0xe1,0x2c,0xcc,0xe4,0x63,0xf4,0x02,0x84,0xf8,0xf6,0xa1,
    0x5c,0x93,0x14,0xf2,0x68,0x5f,0x3a,0x90,0x2f,0x4e,0x5e,0xf9,0x16,0x05,0xcf,
    0x21,0x63,0xca,0xfa,0xb0,0x08,0x02,0xc0
};
static const unsigned char rsa_keygen0_xq1[] = {
    0x9b,0x02,0xd4,0xba,0xf0,0xaa,0x14,0x99,0x6d,0xc0,0xb7,0xa5,0xe1,0xd3,0x70,
    0xb6,0x5a,0xa2,0x9b,0x59,0xd5,0x8c,0x1e,0x9f,0x3f,0x9a,0xde,0xeb,0x9e,0x9c,
    0x61,0xd6,0x5a,0xe1
};
static const unsigned char rsa_keygen0_xq2[] = {
    0x06,0x81,0x53,0xfd,0xa8,0x7b,0xa3,0x85,0x90,0x15,0x2c,0x97,0xb2,0xa0,0x17,
    0x48,0xb0,0x7f,0x0a,0x01,0x6d
};
/* expected values */
static const unsigned char rsa_keygen0_p1[] = {
    0xac,0x5f,0x7f,0x6e,0x33,0x3e,0x97,0x3a,0xb3,0x17,0x44,0xa9,0x0f,0x7a,0x54,
    0x70,0x27,0x06,0x93,0xd5,0x49,0xde,0x91,0x83,0xbc,0x8a,0x7b,0xc3
};
static const unsigned char rsa_keygen0_p2[] = {
    0x0b,0xf6,0xe8,0x79,0x5a,0x81,0xae,0x90,0x1d,0xa4,0x38,0x74,0x9c,0x0e,0x6f,
    0xe0,0x03,0xcf,0xc4,0x53,0x16,0x32,0x17,0xf7,0x09,0x5f,0xd9
};
static const unsigned char rsa_keygen0_q1[] = {
    0x9b,0x02,0xd4,0xba,0xf0,0xaa,0x14,0x99,0x6d,0xc0,0xb7,0xa5,0xe1,0xd3,0x70,
    0xb6,0x5a,0xa2,0x9b,0x59,0xd5,0x8c,0x1e,0x9f,0x3f,0x9a,0xde,0xeb,0x9e,0x9c,
    0x61,0xd6,0x5d,0x47
};
static const unsigned char rsa_keygen0_q2[] = {
    0x06,0x81,0x53,0xfd,0xa8,0x7b,0xa3,0x85,0x90,0x15,0x2c,0x97,0xb2,0xa0,0x17,
    0x48,0xb0,0x7f,0x0a,0x01,0x8f
};
static const unsigned char rsa_keygen0_p[] = {
    0xcf,0x72,0x1b,0x9a,0xfd,0x0d,0x22,0x1a,0x74,0x50,0x97,0x22,0x76,0xd8,0xc0,
    0xc2,0xfd,0x08,0x81,0x05,0xdd,0x18,0x21,0x99,0x96,0xd6,0x5c,0x79,0xe3,0x02,
    0x81,0xd7,0x0e,0x3f,0x3b,0x34,0xda,0x61,0xc9,0x2d,0x84,0x86,0x62,0x1e,0x3d,
    0x5d,0xbf,0x92,0x2e,0xcd,0x35,0x3d,0x6e,0xb9,0x59,0x16,0xc9,0x82,0x50,0x41,
    0x30,0x45,0x67,0xaa,0xb7,0xbe,0xec,0xea,0x4b,0x9e,0xa0,0xc3,0x05,0xbc,0x4c,
    0x01,0xa5,0x4b,0xbd,0xa4,0x20,0xb5,0x20,0xd5,0x59,0x6f,0x82,0x5c,0x8f,0x4f,
    0xe0,0x3a,0x4e,0x7e,0xfe,0x44,0xf3,0x3c,0xc0,0x0e,0x14,0x2b,0x32,0xe6,0x28,
    0x8b,0x63,0x87,0x00,0xc3,0x53,0x4a,0x5b,0x71,0x7a,0x5b,0x28,0x40,0xc4,0x18,
    0xb6,0x77,0x0b,0xab,0x59,0xa4,0x96,0x7d
};
static const unsigned char rsa_keygen0_q[] = {
    0xfe,0xab,0xf2,0x7c,0x16,0x4a,0xf0,0x8d,0x31,0xc6,0x0a,0x82,0xe2,0xae,0xbb,
    0x03,0x7e,0x7b,0x20,0x4e,0x64,0xb0,0x16,0xad,0x3c,0x01,0x1a,0xd3,0x54,0xbf,
    0x2b,0xa4,0x02,0x9e,0xc3,0x0d,0x60,0x3d,0x1f,0xb9,0xc0,0x0d,0xe6,0x97,0x68,
    0xbb,0x8c,0x81,0xd5,0xc1,0x54,0x96,0x0f,0x99,0xf0,0xa8,0xa2,0xf3,0xc6,0x8e,
    0xec,0xbc,0x31,0x17,0x70,0x98,0x24,0xa3,0x36,0x51,0xa8,0x54,0xc4,0x44,0xdd,
    0xf7,0x7e,0xda,0x47,0x4a,0x67,0x44,0x5d,0x4e,0x75,0xf0,0x4d,0x00,0x68,0xe1,
    0x4a,0xec,0x1f,0x45,0xf9,0xe6,0xca,0x38,0x95,0x48,0x6f,0xdc,0x9d,0x1b,0xa3,
    0x4b,0xfd,0x08,0x4b,0x54,0xcd,0xeb,0x3d,0xef,0x33,0x11,0x6e,0xce,0xe4,0x5d,
    0xef,0xa9,0x58,0x5c,0x87,0x4d,0xc8,0xcf
};
static const unsigned char rsa_keygen0_n[] = {
    0xce,0x5e,0x8d,0x1a,0xa3,0x08,0x7a,0x2d,0xb4,0x49,0x48,0xf0,0x06,0xb6,0xfe,
    0xba,0x2f,0x39,0x7c,0x7b,0xe0,0x5d,0x09,0x2d,0x57,0x4e,0x54,0x60,0x9c,0xe5,
    0x08,0x4b,0xe1,0x1a,0x73,0xc1,0x5e,0x2f,0xb6,0x46,0xd7,0x81,0xca,0xbc,0x98,
    0xd2,0xf9,0xef,0x1c,0x92,0x8c,0x8d,0x99,0x85,0x28,0x52,0xd6,0xd5,0xab,0x70,
    0x7e,0x9e,0xa9,0x87,0x82,0xc8,0x95,0x64,0xeb,0xf0,0x6c,0x0f,0x3f,0xe9,0x02,
    0x29,0x2e,0x6d,0xa1,0xec,0xbf,0xdc,0x23,0xdf,0x82,0x4f,0xab,0x39,0x8d,0xcc,
    0xac,0x21,0x51,0x14,0xf8,0xef,0xec,0x73,0x80,0x86,0xa3,0xcf,0x8f,0xd5,0xcf,
    0x22,0x1f,0xcc,0x23,0x2f,0xba,0xcb,0xf6,0x17,0xcd,0x3a,0x1f,0xd9,0x84,0xb9,
    0x88,0xa7,0x78,0x0f,0xaa,0xc9,0x04,0x01,0x20,0x72,0x5d,0x2a,0xfe,0x5b,0xdd,
    0x16,0x5a,0xed,0x83,0x02,0x96,0x39,0x46,0x37,0x30,0xc1,0x0d,0x87,0xc2,0xc8,
    0x33,0x38,0xed,0x35,0x72,0xe5,0x29,0xf8,0x1f,0x23,0x60,0xe1,0x2a,0x5b,0x1d,
    0x6b,0x53,0x3f,0x07,0xc4,0xd9,0xbb,0x04,0x0c,0x5c,0x3f,0x0b,0xc4,0xd4,0x61,
    0x96,0x94,0xf1,0x0f,0x4a,0x49,0xac,0xde,0xd2,0xe8,0x42,0xb3,0x4a,0x0b,0x64,
    0x7a,0x32,0x5f,0x2b,0x5b,0x0f,0x8b,0x8b,0xe0,0x33,0x23,0x34,0x64,0xf8,0xb5,
    0x7f,0x69,0x60,0xb8,0x71,0xe9,0xff,0x92,0x42,0xb1,0xf7,0x23,0xa8,0xa7,0x92,
    0x04,0x3d,0x6b,0xff,0xf7,0xab,0xbb,0x14,0x1f,0x4c,0x10,0x97,0xd5,0x6b,0x71,
    0x12,0xfd,0x93,0xa0,0x4a,0x3b,0x75,0x72,0x40,0x96,0x1c,0x5f,0x40,0x40,0x57,
    0x13
};
static const unsigned char rsa_keygen0_d[] = {
    0x47,0x47,0x49,0x1d,0x66,0x2a,0x4b,0x68,0xf5,0xd8,0x4a,0x24,0xfd,0x6c,0xbf,
    0x56,0xb7,0x70,0xf7,0x9a,0x21,0xc8,0x80,0x9e,0xf4,0x84,0xcd,0x88,0x01,0x28,
    0xea,0x50,0xab,0x13,0x63,0xdf,0xea,0x14,0x38,0xb5,0x07,0x42,0x81,0x2f,0xda,
    0xe9,0x24,0x02,0x7e,0xaf,0xef,0x74,0x09,0x0e,0x80,0xfa,0xfb,0xd1,0x19,0x41,
    0xe5,0xba,0x0f,0x7c,0x0a,0xa4,0x15,0x55,0xa2,0x58,0x8c,0x3a,0x48,0x2c,0xc6,
    0xde,0x4a,0x76,0xfb,0x72,0xb6,0x61,0xe6,0xd2,0x10,0x44,0x4c,0x33,0xb8,0xd2,
    0x74,0xb1,0x9d,0x3b,0xcd,0x2f,0xb1,0x4f,0xc3,0x98,0xbd,0x83,0xb7,0x7e,0x75,
    0xe8,0xa7,0x6a,0xee,0xcc,0x51,0x8c,0x99,0x17,0x67,0x7f,0x27,0xf9,0x0d,0x6a,
    0xb7,0xd4,0x80,0x17,0x89,0x39,0x9c,0xf3,0xd7,0x0f,0xdf,0xb0,0x55,0x80,0x1d,
    0xaf,0x57,0x2e,0xd0,0xf0,0x4f,0x42,0x69,0x55,0xbc,0x83,0xd6,0x97,0x83,0x7a,
    0xe6,0xc6,0x30,0x6d,0x3d,0xb5,0x21,0xa7,0xc4,0x62,0x0a,0x20,0xce,0x5e,0x5a,
    0x17,0x98,0xb3,0x6f,0x6b,0x9a,0xeb,0x6b,0xa3,0xc4,0x75,0xd8,0x2b,0xdc,0x5c,
    0x6f,0xec,0x5d,0x49,0xac,0xa8,0xa4,0x2f,0xb8,0x8c,0x4f,0x2e,0x46,0x21,0xee,
    0x72,0x6a,0x0e,0x22,0x80,0x71,0xc8,0x76,0x40,0x44,0x61,0x16,0xbf,0xa5,0xf8,
    0x89,0xc7,0xe9,0x87,0xdf,0xbd,0x2e,0x4b,0x4e,0xc2,0x97,0x53,0xe9,0x49,0x1c,
    0x05,0xb0,0x0b,0x9b,0x9f,0x21,0x19,0x41,0xe9,0xf5,0x61,0xd7,0x33,0x2e,0x2c,
    0x94,0xb8,0xa8,0x9a,0x3a,0xcc,0x6a,0x24,0x8d,0x19,0x13,0xee,0xb9,0xb0,0x48,
    0x61
};

static const struct rsa_keygen_st rsa_keygen_data[] = {
    {
        2048,
        ITM(rsa_keygen0_e),
        ITM(rsa_keygen0_xp1),
        ITM(rsa_keygen0_xp2),
        ITM(rsa_keygen0_xp),
        ITM(rsa_keygen0_xq1),
        ITM(rsa_keygen0_xq2),
        ITM(rsa_keygen0_xq),

        ITM(rsa_keygen0_p1),
        ITM(rsa_keygen0_p2),
        ITM(rsa_keygen0_q1),
        ITM(rsa_keygen0_q2),

        ITM(rsa_keygen0_p),
        ITM(rsa_keygen0_q),
        ITM(rsa_keygen0_n),
        ITM(rsa_keygen0_d),
    },
};

#define NO_PSS_SALT_LEN -1
struct rsa_siggen_st {
    const char *sig_pad_mode;
    size_t mod;
    const char *digest_alg;
    const unsigned char *msg;
    size_t msg_len;
    int pss_salt_len;
};
static const unsigned char rsa_siggen0_msg[] = {
    0xa3, 0x76, 0x35, 0xc2, 0x6d, 0x6b, 0xa0, 0xe1,
    0x2e, 0x0b, 0x58, 0x33, 0x0d, 0x30, 0xdd, 0x07,
    0xa9, 0x53, 0xd6, 0x37, 0x07, 0xad, 0xa8, 0x67,
};
static const struct rsa_siggen_st rsa_siggen_data[] = {
    {
        "pkcs1", /* pkcs1v1.5 */
        2048,
        "SHA384",
        ITM(rsa_siggen0_msg),
        NO_PSS_SALT_LEN,
    },
    {
        "x931",
        2048,
        "SHA384",
        ITM(rsa_siggen0_msg),
        NO_PSS_SALT_LEN,
    },
    {
        "pss",
        2048,
        "SHA384",
        ITM(rsa_siggen0_msg),
        62
    },
};

struct rsa_sigver_st {
    const char *sig_pad_mode;
    size_t mod;
    const char *digest_alg;
    const unsigned char *msg;
    size_t msg_len;
    const unsigned char *n;
    size_t n_len;
    const unsigned char *e;
    size_t e_len;
    const unsigned char *sig;
    size_t sig_len;
    int pss_salt_len;
    int pass;
};

static const unsigned char rsa_sigver15_0_n[] = {
    0xbb, 0xbc, 0xf3, 0x35, 0x6f, 0x8e, 0x2e, 0x4f,
    0x32, 0xb5, 0xbb, 0x47, 0x9d, 0x02, 0x2a, 0xac,
    0x93, 0x9e, 0x70, 0x50, 0x0f, 0x59, 0x0d, 0x38,
    0x1c, 0xe5, 0xda, 0x87, 0x61, 0x6b, 0xbf, 0xa8,
    0x2c, 0x2f, 0x97, 0xbc, 0x4b, 0xd4, 0xae, 0x21,
    0xed, 0xbe, 0x7a, 0x98, 0x15, 0xa8, 0xe2, 0xf0,
    0x5f, 0x4d, 0xf8, 0xe2, 0x7c, 0x7e, 0x87, 0x52,
    0x8e, 0xbf, 0xb6, 0x3f, 0x1a, 0x12, 0x96, 0x87,
    0x2c, 0xd2, 0xac, 0x85, 0x87, 0xe5, 0xcd, 0x4c,
    0x31, 0x2b, 0x98, 0x16, 0x9f, 0xcf, 0x3e, 0xef,
    0x50, 0xaa, 0xee, 0xc0, 0x6c, 0x80, 0x94, 0xc5,
    0xb1, 0xc7, 0x0d, 0xd4, 0x24, 0x94, 0x44, 0x3a,
    0x44, 0xdb, 0x10, 0xdc, 0x21, 0x57, 0xe0, 0x77,
    0xe5, 0x9c, 0xc4, 0x49, 0x06, 0xe3, 0x5a, 0xea,
    0x64, 0xf4, 0x54, 0xca, 0xfc, 0x5a, 0x2b, 0x92,
    0x76, 0xe1, 0x86, 0x6f, 0x3b, 0x4e, 0x7d, 0xe7,
    0xb9, 0x62, 0xc4, 0x63, 0x12, 0x65, 0x16, 0x58,
    0x11, 0x23, 0xba, 0x1b, 0x95, 0x06, 0x1c, 0xdd,
    0xdc, 0x49, 0x0b, 0x67, 0x7c, 0xb0, 0xdb, 0x45,
    0x88, 0x6e, 0x42, 0xdd, 0x67, 0xbf, 0xec, 0x0e,
    0xfa, 0x64, 0x06, 0x3e, 0xb9, 0x40, 0xee, 0xc6,
    0x56, 0xdf, 0xe7, 0xd8, 0xed, 0xf1, 0xf7, 0x53,
    0xec, 0xd6, 0x1e, 0xb1, 0x66, 0x66, 0x80, 0x16,
    0x5b, 0xba, 0x8c, 0x75, 0xe2, 0x6c, 0x19, 0xe7,
    0xf9, 0xc8, 0xae, 0x75, 0xc9, 0xc4, 0x19, 0xe6,
    0xba, 0xfd, 0x3e, 0x12, 0xf0, 0x88, 0x90, 0xee,
    0x39, 0xf8, 0x85, 0x3c, 0x20, 0x3b, 0xfe, 0xb9,
    0xa0, 0x07, 0x93, 0x6d, 0x20, 0x78, 0xf2, 0xc2,
    0xa5, 0x49, 0x51, 0xa3, 0xb7, 0x13, 0x83, 0xeb,
    0x19, 0x55, 0x08, 0x4f, 0x28, 0x32, 0x1a, 0x9b,
    0xab, 0x05, 0x9a, 0xaa, 0x28, 0xdc, 0xfa, 0xbf,
    0xf3, 0x52, 0x40, 0x0c, 0x4a, 0xb3, 0xd6, 0xb5,
};
static const unsigned char rsa_sigver15_0_e[] = {
    0x01, 0x00, 0x01,
};
static const unsigned char rsa_sigver15_0_msg[] = {
    0xba, 0x1a, 0x03, 0xda, 0x95, 0xd4, 0x36, 0x60,
    0xe6, 0x77, 0xc7, 0x80, 0x49, 0x42, 0xc7, 0x98,
    0xf6, 0x9e, 0xcf, 0x6f, 0xe5, 0xaf, 0x41, 0x6c,
    0x36, 0x29, 0xd0, 0x06, 0xcf, 0x65, 0x43, 0x7c,
    0x47, 0xb4, 0x75, 0xc6, 0x03, 0xf3, 0xa1, 0xcb,
    0x9e, 0x5f, 0xdc, 0xd4, 0x8e, 0xab, 0xe3, 0x41,
    0x05, 0x50, 0x17, 0x7b, 0x16, 0x25, 0xc6, 0x29,
    0x19, 0x2f, 0xac, 0xa7, 0x50, 0xba, 0xba, 0xb3,
    0xcb, 0xa8, 0x16, 0x6a, 0x88, 0x0a, 0x62, 0x74,
    0xdf, 0xed, 0x41, 0x7b, 0x1d, 0x76, 0x17, 0xe1,
    0x70, 0x32, 0x11, 0xb2, 0x03, 0xa7, 0x66, 0xd7,
    0x69, 0x2f, 0xdc, 0x8d, 0x3f, 0x06, 0x8d, 0x16,
    0x0d, 0xa1, 0xeb, 0xae, 0x6e, 0x41, 0x02, 0xc1,
    0x71, 0xc9, 0xfd, 0x5b, 0x3e, 0xcc, 0xec, 0xe0,
    0xfd, 0xeb, 0xc4, 0xfd, 0xf3, 0x5e, 0xa7, 0xde,
    0xee, 0xd0, 0x66, 0xa2, 0xe4, 0x70, 0x45, 0x0c,
};
static const unsigned char rsa_sigver15_0_sig[] = {
    0x4a, 0x8a, 0xcb, 0x88, 0x89, 0xd3, 0xa9, 0x48,
    0x84, 0x09, 0x2e, 0x2c, 0x50, 0x02, 0xb9, 0xad,
    0xe5, 0x10, 0xac, 0x27, 0x8f, 0x2d, 0x36, 0x7e,
    0x6e, 0x32, 0x5c, 0x1d, 0xcb, 0xfa, 0xb8, 0xc7,
    0x1a, 0x27, 0x11, 0x2b, 0x34, 0xf4, 0xa9, 0xda,
    0xa0, 0x99, 0x86, 0xbe, 0x81, 0xd0, 0xd9, 0x2a,
    0x88, 0x25, 0x99, 0xb3, 0x02, 0x50, 0xf1, 0xa5,
    0x4f, 0x3a, 0x1d, 0x7f, 0xcf, 0x7d, 0x76, 0x00,
    0x06, 0x87, 0x9f, 0x39, 0x3a, 0x3c, 0xc0, 0xc6,
    0x46, 0x7a, 0x65, 0x0d, 0x85, 0x06, 0xd8, 0x51,
    0xbe, 0xc5, 0x00, 0x80, 0xeb, 0x73, 0xbb, 0x71,
    0x8c, 0xcc, 0x72, 0x83, 0x1f, 0x9d, 0x73, 0x75,
    0xb8, 0xc8, 0x4c, 0x07, 0x5b, 0xda, 0x8c, 0x9b,
    0x6f, 0x65, 0x8c, 0x2e, 0x23, 0x62, 0x6c, 0x8d,
    0x94, 0x54, 0x5b, 0x7f, 0xe6, 0x5c, 0x90, 0xa3,
    0x07, 0xe2, 0x14, 0x4d, 0xe7, 0x71, 0x6c, 0xfd,
    0x64, 0x12, 0x14, 0x12, 0x14, 0x00, 0x1b, 0xc4,
    0x65, 0xe7, 0x28, 0x5c, 0x34, 0x2d, 0xda, 0x94,
    0xfd, 0x71, 0xcb, 0x27, 0xa6, 0x0e, 0x63, 0xd4,
    0xd6, 0x14, 0x65, 0xc0, 0xe3, 0x65, 0x94, 0x61,
    0x59, 0xb8, 0xc9, 0x3b, 0x9b, 0xc2, 0x82, 0xe2,
    0x76, 0xe7, 0x17, 0xf1, 0xef, 0x32, 0x9e, 0x8a,
    0x04, 0xf3, 0x1e, 0xcc, 0x16, 0xb4, 0x45, 0x0e,
    0x77, 0xdb, 0x8b, 0x38, 0x6c, 0xcc, 0x98, 0xf4,
    0xf8, 0xb5, 0x45, 0x2c, 0xde, 0x23, 0x36, 0xe7,
    0x83, 0xf0, 0xb4, 0xb5, 0xe3, 0xd3, 0xd4, 0x59,
    0xf1, 0x46, 0x7f, 0x0f, 0x55, 0x58, 0xff, 0x75,
    0xc7, 0x7f, 0xee, 0xf8, 0xe0, 0xb2, 0x52, 0xd8,
    0xba, 0x37, 0x4f, 0x7b, 0xba, 0xa3, 0xf0, 0x13,
    0xa7, 0x3a, 0x21, 0xac, 0xdc, 0x9e, 0x63, 0x36,
    0x38, 0xe7, 0x90, 0xeb, 0xea, 0x7f, 0x83, 0xf4,
    0x9d, 0xf3, 0x6b, 0x31, 0x44, 0x47, 0x27, 0x8e,
};

#define rsa_sigver15_1_n rsa_sigver15_0_n
#define rsa_sigver15_1_e rsa_sigver15_0_e
static const unsigned char rsa_sigver15_1_msg[] = {
    0x52, 0x68, 0x35, 0xd6, 0x4a, 0x95, 0xaa, 0xbd,
    0x02, 0x69, 0x7f, 0x92, 0xc7, 0x8c, 0x04, 0x71,
    0x17, 0x10, 0x5a, 0x0d, 0xab, 0x5e, 0x91, 0x45,
    0xb5, 0x70, 0x0d, 0xf8, 0x66, 0x41, 0x2e, 0x19,
    0xb3, 0x82, 0x30, 0x06, 0x59, 0x8f, 0x4f, 0x15,
    0x1e, 0xa1, 0x2f, 0x70, 0x5a, 0x45, 0x7c, 0x24,
    0xb7, 0x0d, 0xcd, 0x74, 0x35, 0x85, 0xcf, 0x73,
    0x71, 0x68, 0x9f, 0xd2, 0x26, 0x14, 0x77, 0xf6,
    0xf4, 0x3c, 0x8d, 0x4d, 0x60, 0xdd, 0x38, 0xe3,
    0x1d, 0x73, 0x55, 0x30, 0x8a, 0x6c, 0xe9, 0x35,
    0x7b, 0xdd, 0x08, 0xc7, 0x3c, 0x74, 0xf5, 0x2a,
    0xd3, 0xae, 0x8a, 0xe1, 0x86, 0x49, 0xda, 0xc5,
    0x9d, 0xfd, 0x16, 0x55, 0x69, 0x67, 0xad, 0x4d,
    0x85, 0x46, 0xb7, 0x7a, 0x5c, 0xe9, 0x94, 0xcc,
    0xeb, 0xe8, 0xd0, 0xad, 0xc9, 0x13, 0x4a, 0x91,
    0x64, 0xa8, 0x96, 0xce, 0x8d, 0xc9, 0x9f, 0xaf,
};
static const unsigned char rsa_sigver15_1_sig[] = {
    0x81, 0x96, 0xdb, 0x65, 0x66, 0x5d, 0xec, 0x14,
    0xb3, 0x42, 0xf6, 0x93, 0x89, 0xae, 0x49, 0x81,
    0x98, 0xda, 0x71, 0x6d, 0x72, 0x9b, 0xcb, 0x39,
    0xe4, 0x85, 0xd1, 0x9f, 0xbe, 0xb8, 0x69, 0x0e,
    0xbe, 0xc0, 0x07, 0x88, 0xee, 0xbf, 0xf9, 0x5d,
    0x20, 0x0f, 0x90, 0x48, 0x93, 0x53, 0xbb, 0xc6,
    0x2f, 0xf8, 0xb7, 0x1d, 0xd2, 0x15, 0x0f, 0x1f,
    0x25, 0xab, 0x5b, 0xae, 0x52, 0xe6, 0x8e, 0x06,
    0x43, 0xe1, 0xd9, 0x4a, 0x4c, 0xee, 0x24, 0x0e,
    0xeb, 0x4f, 0x9b, 0x1a, 0xcb, 0x6d, 0x22, 0x93,
    0xa8, 0xa8, 0xcd, 0x4b, 0xa2, 0xf6, 0x88, 0x1a,
    0xaf, 0x3b, 0x2b, 0xdf, 0x04, 0x2d, 0x2b, 0x27,
    0x54, 0x90, 0x41, 0xb0, 0x4a, 0xda, 0xb1, 0xdf,
    0xce, 0x39, 0xda, 0xd7, 0xda, 0x00, 0x97, 0x89,
    0x9a, 0xaf, 0x4c, 0xc3, 0x0b, 0x6d, 0xb3, 0xce,
    0x59, 0x0b, 0xd9, 0x91, 0x17, 0x31, 0x6a, 0xe7,
    0x92, 0xec, 0x62, 0xe1, 0xe9, 0x73, 0xc7, 0x14,
    0x06, 0x16, 0x42, 0x8a, 0x68, 0xb1, 0x7c, 0xb8,
    0xa1, 0x45, 0xf0, 0x06, 0xf6, 0x85, 0xb5, 0x93,
    0xf1, 0x45, 0xc4, 0xe5, 0xf1, 0x76, 0x71, 0xb4,
    0xdc, 0x03, 0x55, 0xde, 0xb1, 0xd3, 0x5a, 0x0f,
    0x1f, 0x4f, 0xdd, 0xaa, 0x87, 0x8e, 0x46, 0x4d,
    0xe3, 0xd0, 0x5b, 0x28, 0x01, 0xc4, 0x94, 0xf7,
    0x00, 0x93, 0xae, 0xa3, 0xb5, 0x64, 0x65, 0xa1,
    0x16, 0x29, 0x2e, 0xc7, 0xbb, 0xeb, 0x71, 0x02,
    0xf9, 0x26, 0xb6, 0xa6, 0x24, 0xdc, 0x6a, 0x0e,
    0x0d, 0xad, 0x50, 0xf5, 0x4b, 0xe7, 0x0e, 0x9a,
    0x39, 0x20, 0x70, 0xe2, 0xdf, 0x3b, 0x6f, 0x9d,
    0xe3, 0x8f, 0x15, 0x6d, 0x5a, 0xaf, 0x12, 0xf7,
    0xf7, 0x85, 0x6f, 0x0e, 0xe4, 0x6e, 0x27, 0xf7,
    0xb3, 0x44, 0x38, 0x73, 0x45, 0x80, 0x7a, 0x72,
    0x82, 0xf3, 0xc8, 0x32, 0xb8, 0x25, 0xef, 0xdc,
};

static const unsigned char rsa_sigverpss_0_n[] = {
    0xb2, 0xee, 0xdd, 0xdf, 0xa0, 0x35, 0x92, 0x21,
    0xf4, 0x8e, 0xc3, 0x24, 0x39, 0xed, 0xe2, 0x38,
    0xc0, 0xaa, 0xff, 0x35, 0x75, 0x27, 0x05, 0xd4,
    0x84, 0x78, 0x23, 0x50, 0xa5, 0x64, 0x1e, 0x11,
    0x45, 0x2a, 0xb1, 0xeb, 0x97, 0x07, 0x0b, 0xff,
    0xb3, 0x1f, 0xc4, 0xa4, 0x80, 0xae, 0x1c, 0x8c,
    0x66, 0x71, 0x95, 0x80, 0x60, 0xea, 0x4d, 0xde,
    0x90, 0x98, 0xe8, 0xe2, 0x96, 0xa7, 0x0e, 0x5f,
    0x00, 0x74, 0xed, 0x79, 0xc3, 0xe2, 0xc2, 0x4e,
    0xbe, 0x07, 0xbd, 0xb1, 0xb2, 0xeb, 0x6c, 0x29,
    0x9a, 0x59, 0x29, 0x81, 0xa3, 0x83, 0xa3, 0x00,
    0x24, 0xa8, 0xfd, 0x45, 0xbb, 0xca, 0x1e, 0x44,
    0x47, 0xbb, 0x82, 0x4a, 0x5b, 0x71, 0x46, 0xc0,
    0xb4, 0xcc, 0x1b, 0x5e, 0x88, 0x9c, 0x89, 0x69,
    0xb4, 0xb0, 0x7c, 0x8e, 0xea, 0x24, 0xc0, 0x2f,
    0xc8, 0x3f, 0x9d, 0x9f, 0x43, 0xd3, 0xf0, 0x25,
    0x67, 0xf1, 0xf0, 0x9b, 0xd4, 0xff, 0x17, 0x9f,
    0xc3, 0x41, 0x2f, 0x53, 0x33, 0xdd, 0x73, 0x8a,
    0x5c, 0x74, 0x04, 0x3b, 0x60, 0xcc, 0x9f, 0xca,
    0x01, 0xb0, 0x0d, 0xe0, 0xcf, 0xb2, 0xf0, 0x08,
    0x73, 0xb6, 0x67, 0x6c, 0x54, 0x9e, 0x1c, 0x01,
    0xb5, 0x34, 0xab, 0xcf, 0x77, 0xfe, 0x04, 0x01,
    0xc1, 0xd2, 0x4d, 0x47, 0x60, 0x5c, 0x68, 0x47,
    0x8a, 0x47, 0x3c, 0x3a, 0xa3, 0xb2, 0x75, 0x87,
    0x6e, 0x01, 0x7b, 0xdb, 0xe9, 0x6e, 0x63, 0xb2,
    0x65, 0xab, 0xc6, 0xed, 0x0d, 0xa6, 0x84, 0xff,
    0xf3, 0xcf, 0xd3, 0x9a, 0x96, 0x9b, 0x5c, 0x22,
    0xf8, 0x07, 0x7d, 0x63, 0x75, 0x50, 0x91, 0x5b,
    0xc4, 0x1f, 0x29, 0x1f, 0x5d, 0xb0, 0x6e, 0xfa,
    0x9b, 0x16, 0xf0, 0xe4, 0xda, 0x2c, 0x94, 0x20,
    0x9b, 0x44, 0x51, 0x38, 0xd0, 0xe4, 0x86, 0xc9,
    0x76, 0x12, 0x04, 0x1a, 0x25, 0x14, 0xb7, 0x14,
    0xdb, 0x6e, 0xd2, 0xc3, 0x57, 0x2c, 0x4c, 0xec,
    0xfe, 0x25, 0xed, 0x3e, 0xe3, 0x26, 0xa8, 0xd4,
    0xd0, 0x21, 0xbc, 0x09, 0x7e, 0xb0, 0x02, 0x3c,
    0xa3, 0x43, 0xa4, 0x1f, 0x73, 0x54, 0x5f, 0xa3,
    0xe2, 0x49, 0x4e, 0x25, 0xe8, 0xfc, 0xfb, 0xa9,
    0x29, 0xc0, 0x7d, 0xd0, 0x06, 0xd5, 0x5c, 0x52,
    0x68, 0x3c, 0xf8, 0xc5, 0xdb, 0x92, 0x27, 0x7c,
    0xd8, 0x56, 0x1a, 0x7d, 0xe3, 0x32, 0xe5, 0x08,
    0xc9, 0x36, 0x9d, 0x7e, 0xd2, 0x2d, 0xc2, 0x53,
    0xf2, 0x7e, 0xce, 0x8a, 0x10, 0x5c, 0xf7, 0xe9,
    0x99, 0xa6, 0xa8, 0xf5, 0x8d, 0x6c, 0xed, 0xf3,
    0xa1, 0xc8, 0x2a, 0x75, 0x77, 0x99, 0x18, 0xe1,
    0x32, 0xdb, 0x35, 0x4a, 0x8b, 0x4a, 0xec, 0xc2,
    0x15, 0xe9, 0x4b, 0x89, 0x13, 0x81, 0xfb, 0x0c,
    0xf9, 0xb4, 0xd8, 0xee, 0xb5, 0xba, 0x45, 0xa1,
    0xea, 0x01, 0xf9, 0xbb, 0xd5, 0xa1, 0x73, 0xa1,
    0x5b, 0xef, 0x98, 0xa8, 0xcf, 0x74, 0xf4, 0xd5,
    0x1a, 0xe2, 0xa7, 0xb9, 0x37, 0x43, 0xb1, 0x29,
    0x94, 0xc3, 0x71, 0x74, 0x34, 0x7d, 0x6f, 0xac,
    0x97, 0xb3, 0x5b, 0x3a, 0x0a, 0x3c, 0xe2, 0x94,
    0x6c, 0x39, 0xb8, 0xe9, 0x2c, 0xf9, 0xc3, 0x8b,
    0xd1, 0x80, 0x4d, 0x22, 0x64, 0x63, 0x20, 0x1b,
    0xeb, 0xf9, 0x09, 0x14, 0x86, 0x6e, 0xf4, 0x6d,
    0xfc, 0xe5, 0x1b, 0xf7, 0xf2, 0xe0, 0x4d, 0xc8,
    0xeb, 0x24, 0x35, 0x16, 0x0a, 0x81, 0x9f, 0x9e,
    0x47, 0xd8, 0xea, 0x85, 0xda, 0x77, 0x6c, 0x3d,
    0xd4, 0xa9, 0x15, 0xbd, 0xda, 0x5d, 0xf0, 0x72,
    0x8d, 0xb5, 0x12, 0x72, 0xb1, 0x62, 0xa0, 0xad,
    0xc8, 0x0e, 0x5b, 0x47, 0x4c, 0x69, 0xf7, 0x07,
    0xe8, 0xd9, 0x9b, 0xc7, 0x2f, 0xd5, 0x68, 0x1e,
    0x1c, 0xe0, 0x8f, 0x40, 0x45, 0x5f, 0x08, 0xc8,
    0x95, 0x57, 0xb7, 0x35, 0x92, 0x97, 0xf9, 0x7d,
};
static const unsigned char rsa_sigverpss_0_e[] = {
    0x01, 0x00, 0x01,
};
static const unsigned char rsa_sigverpss_0_msg[] = {
    0x32, 0x03, 0x0c, 0x2e, 0x06, 0xfc, 0x0f, 0xa5,
    0x65, 0xcd, 0x0f, 0x88, 0x52, 0x80, 0xc3, 0x43,
    0xda, 0x01, 0x36, 0x48, 0xf5, 0x76, 0xc8, 0x03,
    0xae, 0xce, 0x76, 0x0f, 0x83, 0x9d, 0x5c, 0xaa,
    0x0f, 0x27, 0x78, 0x66, 0xe6, 0xba, 0xb1, 0x22,
    0xc1, 0x42, 0x18, 0x39, 0xdb, 0x17, 0x6d, 0xf9,
    0x9a, 0x19, 0xe5, 0x57, 0x72, 0xff, 0x2a, 0xe0,
    0x07, 0xec, 0xa4, 0xf3, 0x91, 0x43, 0xf7, 0x2e,
    0x85, 0xbd, 0xcd, 0x26, 0x72, 0xb9, 0xd5, 0x5b,
    0x28, 0xd3, 0x0c, 0x6b, 0x20, 0xb7, 0x3b, 0x85,
    0x18, 0x38, 0xc0, 0x21, 0xfe, 0x9c, 0x92, 0xee,
    0x0f, 0x3a, 0x80, 0x0c, 0x40, 0x48, 0xb9, 0x7c,
    0xdd, 0xee, 0x91, 0xd5, 0x70, 0x9e, 0x82, 0x38,
    0xe4, 0xa8, 0x71, 0x85, 0xea, 0x09, 0x33, 0xcf,
    0x9c, 0x84, 0x50, 0x0e, 0x60, 0xf5, 0x07, 0x14,
    0x10, 0xe1, 0x92, 0xc3, 0x58, 0x51, 0xab, 0x7c,
};
static const unsigned char rsa_sigverpss_0_sig[] = {
    0x43, 0xb2, 0x4a, 0x50, 0xa7, 0xe2, 0x6c, 0x5d,
    0x50, 0xc5, 0x39, 0xc1, 0xc1, 0x35, 0xbd, 0x66,
    0xbd, 0x86, 0x54, 0xc5, 0x2e, 0x65, 0xfc, 0x19,
    0x19, 0x6a, 0x22, 0x43, 0x22, 0x11, 0x26, 0xae,
    0x51, 0x78, 0xfa, 0xfa, 0xc1, 0xf0, 0x77, 0x1b,
    0xd6, 0x5b, 0x93, 0xbd, 0x84, 0xe4, 0x35, 0xbd,
    0x8d, 0x91, 0xb2, 0x7c, 0xb2, 0xb1, 0xda, 0xd7,
    0x72, 0x62, 0x88, 0x3e, 0xe9, 0x40, 0x27, 0x4e,
    0xa5, 0x17, 0x94, 0xf1, 0xe9, 0xdd, 0x8c, 0x6c,
    0x5b, 0xc0, 0x0b, 0xe3, 0x7c, 0x8b, 0xc8, 0x10,
    0x57, 0x35, 0x69, 0xb7, 0x56, 0xe0, 0x2f, 0x61,
    0x2e, 0x13, 0x11, 0x79, 0xfa, 0x60, 0x8f, 0x2a,
    0x65, 0x73, 0xf5, 0x17, 0x34, 0x74, 0x72, 0x22,
    0xff, 0x22, 0x5b, 0x97, 0x59, 0x44, 0xf4, 0xfb,
    0x4a, 0x2b, 0x7e, 0x28, 0xe3, 0x79, 0x84, 0x24,
    0x63, 0xeb, 0xde, 0x63, 0x88, 0xe0, 0xbd, 0x28,
    0xef, 0x49, 0x6d, 0xd4, 0x2a, 0x87, 0x53, 0xba,
    0x5f, 0xde, 0xe3, 0xd4, 0xb2, 0xc2, 0x6f, 0x49,
    0x10, 0xae, 0x5e, 0x15, 0xdd, 0x0f, 0x91, 0xe2,
    0xeb, 0x1e, 0xc5, 0x36, 0x8e, 0xdf, 0xa6, 0x17,
    0x25, 0x21, 0x16, 0x06, 0x72, 0x37, 0x77, 0x19,
    0xe5, 0x88, 0x1b, 0x0b, 0x5b, 0x80, 0x44, 0x8f,
    0x13, 0xef, 0xbb, 0xfa, 0xf6, 0x4a, 0x11, 0x6a,
    0x6a, 0x0c, 0xe0, 0x42, 0x6b, 0x7d, 0xfd, 0xad,
    0xb0, 0x4b, 0xff, 0x3f, 0x20, 0xca, 0x5f, 0x64,
    0xcc, 0xc9, 0x5b, 0x89, 0xc2, 0x05, 0x33, 0xf9,
    0xa5, 0x31, 0x55, 0xfb, 0xdc, 0xeb, 0xd1, 0x24,
    0xbf, 0x17, 0x0f, 0xc8, 0xfd, 0xe9, 0x6a, 0xc1,
    0xa7, 0x94, 0x36, 0x72, 0x22, 0x29, 0x2c, 0x1c,
    0xd1, 0x8b, 0x7b, 0x37, 0x42, 0x25, 0x8d, 0xe3,
    0xcc, 0x06, 0x5f, 0x3c, 0x15, 0xfa, 0x74, 0x8a,
    0x83, 0xf0, 0xcc, 0xf5, 0x30, 0xd1, 0xa8, 0x88,
    0x9f, 0x4e, 0x1d, 0xd8, 0xe3, 0x1b, 0xb5, 0xe3,
    0xdb, 0xce, 0xbc, 0x03, 0xfe, 0xe6, 0xa2, 0xb4,
    0x94, 0x76, 0xd1, 0xb7, 0xce, 0xae, 0x6a, 0x7c,
    0xbd, 0x4f, 0xd6, 0xfe, 0x60, 0xd0, 0x78, 0xd4,
    0x04, 0x3f, 0xe0, 0x17, 0x2a, 0x41, 0x26, 0x5a,
    0x81, 0x80, 0xcd, 0x40, 0x7c, 0x4f, 0xd6, 0xd6,
    0x1d, 0x1f, 0x58, 0x59, 0xaf, 0xa8, 0x00, 0x91,
    0x69, 0xb1, 0xf8, 0x3b, 0xef, 0x59, 0x7e, 0x83,
    0x4e, 0xca, 0x1d, 0x33, 0x35, 0xb6, 0xa5, 0x9a,
    0x0e, 0xc5, 0xe5, 0x11, 0xdd, 0x5d, 0xb7, 0x32,
    0x66, 0x23, 0x63, 0x08, 0xbc, 0x2e, 0x9c, 0x10,
    0x30, 0xa4, 0x13, 0x38, 0xee, 0xc7, 0x10, 0xf6,
    0xed, 0xe9, 0xe1, 0xd1, 0x89, 0x8b, 0x94, 0x21,
    0xde, 0x76, 0x72, 0x90, 0xc4, 0xbc, 0x59, 0x31,
    0x1b, 0x1b, 0xd7, 0xa0, 0xd0, 0x3d, 0xaa, 0x43,
    0x66, 0xfa, 0x43, 0x8d, 0xcc, 0x37, 0xdc, 0x60,
    0x59, 0xaf, 0x02, 0x98, 0xe5, 0xe0, 0x17, 0xd6,
    0xc3, 0x84, 0xf2, 0xaa, 0x5d, 0x88, 0xa8, 0x78,
    0xbf, 0xbd, 0x18, 0x34, 0x9f, 0x5c, 0x6d, 0x22,
    0x0c, 0x77, 0x4f, 0x16, 0xf2, 0x85, 0x88, 0x2e,
    0x9a, 0x2b, 0x30, 0x1e, 0x17, 0xc8, 0xc7, 0xd4,
    0x20, 0x93, 0x47, 0x0d, 0x32, 0x7d, 0xcb, 0x77,
    0x85, 0x82, 0xc3, 0x80, 0x75, 0x10, 0x83, 0x33,
    0xd5, 0xde, 0x47, 0xd4, 0x22, 0x55, 0x4d, 0xca,
    0x4f, 0x90, 0xd2, 0x9f, 0x80, 0x58, 0x22, 0x4c,
    0x5a, 0xaa, 0x53, 0x9e, 0xeb, 0xde, 0x62, 0x8a,
    0xfb, 0xd7, 0x4b, 0x28, 0xd5, 0xe1, 0x02, 0xf9,
    0x61, 0x74, 0x42, 0x12, 0x32, 0x5d, 0x1b, 0x10,
    0x8f, 0x51, 0x8d, 0x7c, 0x59, 0xc5, 0xb7, 0x5a,
    0x68, 0xe7, 0xdd, 0xb0, 0xc0, 0x22, 0xbc, 0xf1,
    0x37, 0xcc, 0x63, 0xa2, 0x85, 0xb9, 0x11, 0x91,
    0x43, 0xb9, 0x7b, 0xfb, 0x4a, 0x21, 0xc9, 0xd5,
};

#define rsa_sigverpss_1_n rsa_sigverpss_0_n
#define rsa_sigverpss_1_e rsa_sigverpss_0_e
static const unsigned char rsa_sigverpss_1_msg[] = {
    0x29, 0xdc, 0x70, 0xd8, 0xa5, 0xde, 0x41, 0x1d,
    0xed, 0x05, 0x16, 0x04, 0x48, 0x05, 0x21, 0x05,
    0x1c, 0x40, 0x8a, 0xbb, 0x6c, 0x3c, 0x11, 0xf3,
    0x9f, 0x55, 0xf4, 0x03, 0x83, 0xaf, 0x13, 0x5d,
    0x91, 0x6f, 0x52, 0x63, 0x73, 0x2b, 0x3f, 0x7d,
    0xc3, 0x9e, 0xf5, 0x69, 0x16, 0xa1, 0x40, 0xd3,
    0x39, 0x57, 0x01, 0x26, 0xba, 0xa7, 0xd4, 0xa1,
    0xaa, 0xef, 0xf1, 0xad, 0xa2, 0xf6, 0x50, 0x6e,
    0x04, 0x23, 0x11, 0x98, 0x83, 0xed, 0x1a, 0x84,
    0xe6, 0x93, 0x02, 0x83, 0x08, 0x0d, 0x2e, 0x72,
    0x24, 0x42, 0x39, 0x8e, 0x4f, 0x7b, 0x99, 0x8a,
    0x46, 0x18, 0x80, 0xdf, 0x6a, 0x82, 0x01, 0x64,
    0x09, 0x60, 0x74, 0x1e, 0xdf, 0x0e, 0x1b, 0x59,
    0xdd, 0x4a, 0x06, 0xf7, 0x29, 0x31, 0x33, 0x09,
    0x65, 0x6b, 0xfa, 0x9c, 0x34, 0xa2, 0xa8, 0xd5,
    0xfa, 0x38, 0x6b, 0x41, 0xe4, 0x39, 0x6e, 0x66,
};
static const unsigned char rsa_sigverpss_1_sig[] = {
    0x48, 0x7f, 0x71, 0x82, 0x63, 0x1d, 0xf2, 0xee,
    0xe8, 0x79, 0xeb, 0x3a, 0xaf, 0x41, 0x8a, 0x7c,
    0xab, 0x0b, 0xd4, 0x57, 0xb6, 0x62, 0x9f, 0x6f,
    0xec, 0xc1, 0xd4, 0xef, 0x55, 0x51, 0xd1, 0x0a,
    0x0e, 0x1d, 0x8a, 0x64, 0x69, 0x08, 0x57, 0xf5,
    0x04, 0xa8, 0x6c, 0xde, 0x76, 0x4d, 0x81, 0xf4,
    0x95, 0x7e, 0x95, 0x6d, 0x41, 0x31, 0x2f, 0x9d,
    0xe7, 0x47, 0x45, 0x45, 0x9f, 0xa8, 0xf8, 0xe3,
    0x30, 0xa6, 0x41, 0x0f, 0x12, 0x05, 0x6d, 0x2b,
    0x1a, 0xae, 0xef, 0xd4, 0x6b, 0xc6, 0xf4, 0x61,
    0xa5, 0x07, 0xfe, 0xe8, 0xd0, 0xfd, 0xa3, 0x93,
    0x58, 0xb4, 0x22, 0x37, 0x1b, 0x84, 0xcb, 0xef,
    0xae, 0x24, 0xec, 0x62, 0xe2, 0x7d, 0xf4, 0x09,
    0x5a, 0xc3, 0x0f, 0x4b, 0x49, 0xb7, 0xe7, 0xb2,
    0x9b, 0x01, 0x2c, 0x8a, 0x39, 0xdd, 0x10, 0xec,
    0x30, 0xb9, 0x7e, 0x39, 0x98, 0x94, 0x2a, 0xa4,
    0xb3, 0x97, 0x7f, 0x85, 0x6e, 0x19, 0x75, 0x9e,
    0x91, 0x94, 0xaa, 0xb5, 0xb0, 0x1f, 0x72, 0x50,
    0xb5, 0x6d, 0x7a, 0xff, 0x90, 0xcc, 0x24, 0x80,
    0x20, 0x23, 0x1c, 0xf3, 0xbd, 0x01, 0xc7, 0x82,
    0x63, 0x04, 0xcc, 0xbd, 0xfb, 0x41, 0x9a, 0xb8,
    0xeb, 0x6d, 0x78, 0x02, 0xee, 0x4a, 0x6d, 0xbb,
    0xf7, 0xb7, 0xcf, 0x91, 0xca, 0x11, 0xf2, 0x62,
    0xec, 0x18, 0x14, 0xcd, 0x10, 0xd8, 0x60, 0xe5,
    0x20, 0x86, 0x74, 0x84, 0xd5, 0x35, 0x34, 0x69,
    0x65, 0x93, 0x31, 0x99, 0xb6, 0x2d, 0x43, 0x23,
    0x1d, 0x73, 0x55, 0xfa, 0x03, 0x76, 0x22, 0xcc,
    0x66, 0xbc, 0x20, 0x2f, 0x7f, 0x4f, 0x78, 0xdd,
    0xd1, 0x1f, 0xb6, 0x79, 0x6b, 0x58, 0x58, 0x57,
    0x56, 0x87, 0xbc, 0x72, 0x6c, 0x81, 0x0a, 0xe2,
    0xae, 0xb2, 0x4b, 0x66, 0x5b, 0x65, 0x35, 0x2b,
    0x89, 0x0b, 0xa8, 0x5c, 0x34, 0xb3, 0x5f, 0xb0,
    0x21, 0x5d, 0x4c, 0x60, 0x57, 0x73, 0xb6, 0x16,
    0x94, 0xa7, 0x55, 0x52, 0x2a, 0x87, 0x10, 0xc9,
    0x7c, 0x86, 0xb9, 0xdd, 0xf5, 0xb9, 0x30, 0xc0,
    0xe6, 0x2a, 0xc9, 0x08, 0x3a, 0x88, 0xdc, 0x27,
    0xea, 0x2f, 0xd9, 0x37, 0x06, 0x36, 0xd8, 0xe5,
    0x66, 0x11, 0x54, 0x72, 0x4c, 0xc8, 0xa2, 0xc1,
    0xed, 0xf5, 0x17, 0x3b, 0x06, 0x2b, 0x4c, 0xc9,
    0x49, 0x2b, 0x98, 0x6f, 0xb8, 0x77, 0x96, 0x0c,
    0x6b, 0x47, 0x81, 0x6c, 0xf3, 0x94, 0x3d, 0x3b,
    0x24, 0x2d, 0x26, 0x9c, 0x40, 0xc1, 0x1f, 0xa7,
    0xb2, 0xb4, 0x29, 0xb6, 0x05, 0xe5, 0x6e, 0x3c,
    0xab, 0xd4, 0xaa, 0x3d, 0x78, 0x63, 0x3e, 0xf2,
    0x75, 0x0d, 0xc3, 0x46, 0x0e, 0x68, 0xd7, 0x3d,
    0xb9, 0xcb, 0x9a, 0x0a, 0xce, 0xec, 0x6f, 0x21,
    0x8c, 0x86, 0xaa, 0xeb, 0x7b, 0x56, 0x41, 0xa6,
    0x7a, 0xd3, 0x03, 0x02, 0x5c, 0x76, 0x01, 0xf7,
    0x5d, 0x5e, 0x8e, 0x7d, 0xac, 0x35, 0x84, 0x11,
    0xc6, 0xbc, 0x9a, 0x53, 0xcc, 0x3b, 0x4f, 0x5b,
    0x23, 0x79, 0x30, 0x52, 0xc3, 0x73, 0x5d, 0xc8,
    0xf1, 0xec, 0x2e, 0x0d, 0xda, 0x64, 0x90, 0x50,
    0x62, 0xcf, 0x18, 0xc5, 0x52, 0x45, 0xe7, 0x38,
    0x1a, 0xec, 0x01, 0x18, 0xbb, 0x85, 0x97, 0x7f,
    0x68, 0x2b, 0x6f, 0xfc, 0xcd, 0x08, 0xc8, 0xe2,
    0xca, 0x7e, 0xa6, 0x4f, 0xca, 0x5d, 0xdd, 0xf8,
    0xfa, 0x52, 0x1c, 0x91, 0x82, 0x56, 0x07, 0xb2,
    0x03, 0x3e, 0xa2, 0x8d, 0x60, 0xff, 0x78, 0x05,
    0x1a, 0xfc, 0x6e, 0x27, 0x80, 0xbd, 0x90, 0x98,
    0x83, 0x46, 0xba, 0xec, 0xee, 0x89, 0xe3, 0x1b,
    0xc0, 0xcd, 0x2f, 0x05, 0x37, 0x18, 0xb5, 0xfa,
    0xc3, 0x91, 0x85, 0x0f, 0xb7, 0x74, 0x1c, 0x64,
    0xf0, 0xf8, 0x56, 0x35, 0xb8, 0x1d, 0xc3, 0x39,
    0x5c, 0xea, 0x8a, 0x92, 0x31, 0xd2, 0x11, 0x4b,
};

static const unsigned char rsa_sigverx931_0_n[] = {
    0xa0, 0x16, 0x14, 0x80, 0x8b, 0x17, 0x2b, 0xad,
    0xd7, 0x07, 0x31, 0x6d, 0xfc, 0xba, 0x25, 0x83,
    0x09, 0xa0, 0xf7, 0x71, 0xc6, 0x06, 0x22, 0x87,
    0xd6, 0xbd, 0x13, 0xd9, 0xfe, 0x7c, 0xf7, 0xe6,
    0x48, 0xdb, 0x27, 0xd8, 0xa5, 0x49, 0x8e, 0x8c,
    0xea, 0xbe, 0xe0, 0x04, 0x6f, 0x3d, 0x3b, 0x73,
    0xdc, 0xc5, 0xd4, 0xdc, 0x85, 0xef, 0xea, 0x10,
    0x46, 0xf3, 0x88, 0xb9, 0x93, 0xbc, 0xa0, 0xb6,
    0x06, 0x02, 0x82, 0xb4, 0x2d, 0x54, 0xec, 0x79,
    0x50, 0x8a, 0xfc, 0xfa, 0x62, 0x45, 0xbb, 0xd7,
    0x26, 0xcd, 0x88, 0xfa, 0xe8, 0x0f, 0x26, 0x5b,
    0x1f, 0x21, 0x3f, 0x3b, 0x5d, 0x98, 0x3f, 0x02,
    0x8c, 0xa1, 0xbf, 0xc0, 0x70, 0x4d, 0xd1, 0x41,
    0xfd, 0xb9, 0x55, 0x12, 0x90, 0xc8, 0x6e, 0x0f,
    0x19, 0xa8, 0x5c, 0x31, 0xd6, 0x16, 0x0e, 0xdf,
    0x08, 0x84, 0xcd, 0x4b, 0xfd, 0x28, 0x8d, 0x7d,
    0x6e, 0xea, 0xc7, 0x95, 0x4a, 0xc3, 0x84, 0x54,
    0x7f, 0xb0, 0x20, 0x29, 0x96, 0x39, 0x4c, 0x3e,
    0x85, 0xec, 0x22, 0xdd, 0xb9, 0x14, 0xbb, 0x04,
    0x2f, 0x4c, 0x0c, 0xe3, 0xfa, 0xae, 0x47, 0x79,
    0x59, 0x8e, 0x4e, 0x7d, 0x4a, 0x17, 0xae, 0x16,
    0x38, 0x66, 0x4e, 0xff, 0x45, 0x7f, 0xac, 0x5e,
    0x75, 0x9f, 0x51, 0x18, 0xe6, 0xad, 0x6b, 0x8b,
    0x3d, 0x08, 0x4d, 0x9a, 0xd2, 0x11, 0xba, 0xa8,
    0xc3, 0xb5, 0x17, 0xb5, 0xdf, 0xe7, 0x39, 0x89,
    0x27, 0x7b, 0xeb, 0xf4, 0xe5, 0x7e, 0xa9, 0x7b,
    0x39, 0x40, 0x6f, 0xe4, 0x82, 0x14, 0x3d, 0x62,
    0xb6, 0xd4, 0x43, 0xd0, 0x0a, 0x2f, 0xc1, 0x73,
    0x3d, 0x99, 0x37, 0xbe, 0x62, 0x13, 0x6a, 0x8b,
    0xeb, 0xc5, 0x64, 0xd5, 0x2a, 0x8b, 0x4f, 0x7f,
    0x82, 0x48, 0x69, 0x3e, 0x08, 0x1b, 0xb5, 0x77,
    0xd3, 0xdc, 0x1b, 0x2c, 0xe5, 0x59, 0xf6, 0x33,
    0x47, 0xa0, 0x0f, 0xff, 0x8a, 0x6a, 0x1d, 0x66,
    0x24, 0x67, 0x36, 0x7d, 0x21, 0xda, 0xc1, 0xd4,
    0x11, 0x6c, 0xe8, 0x5f, 0xd7, 0x8a, 0x53, 0x5c,
    0xb2, 0xe2, 0xf9, 0x14, 0x29, 0x0f, 0xcf, 0x28,
    0x32, 0x4f, 0xc6, 0x17, 0xf6, 0xbc, 0x0e, 0xb8,
    0x99, 0x7c, 0x14, 0xa3, 0x40, 0x3f, 0xf3, 0xe4,
    0x31, 0xbe, 0x54, 0x64, 0x5a, 0xad, 0x1d, 0xb0,
    0x37, 0xcc, 0xd9, 0x0b, 0xa4, 0xbc, 0xe0, 0x07,
    0x37, 0xd1, 0xe1, 0x65, 0xc6, 0x53, 0xfe, 0x60,
    0x6a, 0x64, 0xa4, 0x01, 0x00, 0xf3, 0x5b, 0x9a,
    0x28, 0x61, 0xde, 0x7a, 0xd7, 0x0d, 0x56, 0x1e,
    0x4d, 0xa8, 0x6a, 0xb5, 0xf2, 0x86, 0x2a, 0x4e,
    0xaa, 0x37, 0x23, 0x5a, 0x3b, 0x69, 0x66, 0x81,
    0xc8, 0x8e, 0x1b, 0x31, 0x0f, 0x28, 0x31, 0x9a,
    0x2d, 0xe5, 0x79, 0xcc, 0xa4, 0xca, 0x60, 0x45,
    0xf7, 0x83, 0x73, 0x5a, 0x01, 0x29, 0xda, 0xf7,

};
static const unsigned char rsa_sigverx931_0_e[] = {
    0x01, 0x00, 0x01,
};
static const unsigned char rsa_sigverx931_0_msg[] = {
    0x82, 0x2e, 0x41, 0x70, 0x9d, 0x1f, 0xe9, 0x47,
    0xec, 0xf1, 0x79, 0xcc, 0x05, 0xef, 0xdb, 0xcd,
    0xca, 0x8b, 0x8e, 0x61, 0x45, 0xad, 0xa6, 0xd9,
    0xd7, 0x4b, 0x15, 0xf4, 0x92, 0x3a, 0x2a, 0x52,
    0xe3, 0x44, 0x57, 0x2b, 0x74, 0x7a, 0x37, 0x41,
    0x50, 0xcb, 0xcf, 0x13, 0x49, 0xd6, 0x15, 0x54,
    0x97, 0xfd, 0xae, 0x9b, 0xc1, 0xbb, 0xfc, 0x5c,
    0xc1, 0x37, 0x58, 0x17, 0x63, 0x19, 0x9c, 0xcf,
    0xee, 0x9c, 0xe5, 0xbe, 0x06, 0xe4, 0x97, 0x47,
    0xd1, 0x93, 0xa1, 0x2c, 0x59, 0x97, 0x02, 0x01,
    0x31, 0x45, 0x8c, 0xe1, 0x5c, 0xac, 0xe7, 0x5f,
    0x6a, 0x23, 0xda, 0xbf, 0xe4, 0x25, 0xc6, 0x67,
    0xea, 0x5f, 0x73, 0x90, 0x1b, 0x06, 0x0f, 0x41,
    0xb5, 0x6e, 0x74, 0x7e, 0xfd, 0xd9, 0xaa, 0xbd,
    0xe2, 0x8d, 0xad, 0x99, 0xdd, 0x29, 0x70, 0xca,
    0x1b, 0x38, 0x21, 0x55, 0xde, 0x07, 0xaf, 0x00,

};
static const unsigned char rsa_sigverx931_0_sig[] = {
    0x29, 0xa9, 0x3a, 0x8e, 0x9e, 0x90, 0x1b, 0xdb,
    0xaf, 0x0b, 0x47, 0x5b, 0xb5, 0xc3, 0x8c, 0xc3,
    0x70, 0xbe, 0x73, 0xf9, 0x65, 0x8e, 0xc6, 0x1e,
    0x95, 0x0b, 0xdb, 0x24, 0x76, 0x79, 0xf1, 0x00,
    0x71, 0xcd, 0xc5, 0x6a, 0x7b, 0xd2, 0x8b, 0x18,
    0xc4, 0xdd, 0xf1, 0x2a, 0x31, 0x04, 0x3f, 0xfc,
    0x36, 0x06, 0x20, 0x71, 0x3d, 0x62, 0xf2, 0xb5,
    0x79, 0x0a, 0xd5, 0xd2, 0x81, 0xf1, 0xb1, 0x4f,
    0x9a, 0x17, 0xe8, 0x67, 0x64, 0x48, 0x09, 0x75,
    0xff, 0x2d, 0xee, 0x36, 0xca, 0xca, 0x1d, 0x74,
    0x99, 0xbe, 0x5c, 0x94, 0x31, 0xcc, 0x12, 0xf4,
    0x59, 0x7e, 0x17, 0x00, 0x4f, 0x7b, 0xa4, 0xb1,
    0xda, 0xdb, 0x3e, 0xa4, 0x34, 0x10, 0x4a, 0x19,
    0x0a, 0xd2, 0xa7, 0xa0, 0xc5, 0xe6, 0xef, 0x82,
    0xd4, 0x2e, 0x21, 0xbe, 0x15, 0x73, 0xac, 0xef,
    0x05, 0xdb, 0x6a, 0x8a, 0x1a, 0xcb, 0x8e, 0xa5,
    0xee, 0xfb, 0x28, 0xbf, 0x96, 0xa4, 0x2b, 0xd2,
    0x85, 0x2b, 0x20, 0xc3, 0xaf, 0x9a, 0x32, 0x04,
    0xa0, 0x49, 0x24, 0x47, 0xd0, 0x09, 0xf7, 0xcf,
    0x73, 0xb6, 0xf6, 0x70, 0xda, 0x3b, 0xf8, 0x5a,
    0x28, 0x2e, 0x14, 0x6c, 0x52, 0xbd, 0x2a, 0x7c,
    0x8e, 0xc1, 0xa8, 0x0e, 0xb1, 0x1e, 0x6b, 0x8d,
    0x76, 0xea, 0x70, 0x81, 0xa0, 0x02, 0x63, 0x74,
    0xbc, 0x7e, 0xb9, 0xac, 0x0e, 0x7b, 0x1b, 0x75,
    0x82, 0xe2, 0x98, 0x4e, 0x24, 0x55, 0xd4, 0xbd,
    0x14, 0xde, 0x58, 0x56, 0x3a, 0x5d, 0x4e, 0x57,
    0x0d, 0x54, 0x74, 0xe8, 0x86, 0x8c, 0xcb, 0x07,
    0x9f, 0x0b, 0xfb, 0xc2, 0x08, 0x5c, 0xd7, 0x05,
    0x3b, 0xc8, 0xd2, 0x15, 0x68, 0x8f, 0x3d, 0x3c,
    0x4e, 0x85, 0xa9, 0x25, 0x6f, 0xf5, 0x2e, 0xca,
    0xca, 0xa8, 0x27, 0x89, 0x61, 0x4e, 0x1f, 0x57,
    0x2d, 0x99, 0x10, 0x3f, 0xbc, 0x9e, 0x96, 0x5e,
    0x2f, 0x0a, 0x25, 0xa7, 0x5c, 0xea, 0x65, 0x2a,
    0x22, 0x35, 0xa3, 0xf9, 0x13, 0x89, 0x05, 0x2e,
    0x19, 0x73, 0x1d, 0x70, 0x74, 0x98, 0x15, 0x4b,
    0xab, 0x56, 0x52, 0xe0, 0x01, 0x42, 0x95, 0x6a,
    0x46, 0x2c, 0x78, 0xff, 0x26, 0xbc, 0x48, 0x10,
    0x38, 0x25, 0xab, 0x32, 0x7c, 0x79, 0x7c, 0x5d,
    0x6f, 0x45, 0x54, 0x74, 0x2d, 0x93, 0x56, 0x52,
    0x11, 0x34, 0x1e, 0xe3, 0x4b, 0x6a, 0x17, 0x4f,
    0x37, 0x14, 0x75, 0xac, 0xa3, 0xa1, 0xca, 0xda,
    0x38, 0x06, 0xa9, 0x78, 0xb9, 0x5d, 0xd0, 0x59,
    0x1b, 0x5d, 0x1e, 0xc2, 0x0b, 0xfb, 0x39, 0x37,
    0x44, 0x85, 0xb6, 0x36, 0x06, 0x95, 0xbc, 0x15,
    0x35, 0xb9, 0xe6, 0x27, 0x42, 0xe3, 0xc8, 0xec,
    0x30, 0x37, 0x20, 0x26, 0x9a, 0x11, 0x61, 0xc0,
    0xdb, 0xb2, 0x5a, 0x26, 0x78, 0x27, 0xb9, 0x13,
    0xc9, 0x1a, 0xa7, 0x67, 0x93, 0xe8, 0xbe, 0xcb,
};

#define rsa_sigverx931_1_n rsa_sigverx931_0_n
#define rsa_sigverx931_1_e rsa_sigverx931_0_e
static const unsigned char rsa_sigverx931_1_msg[] = {
    0x79, 0x02, 0xb9, 0xd2, 0x3e, 0x84, 0x02, 0xc8,
    0x2a, 0x94, 0x92, 0x14, 0x8d, 0xd5, 0xd3, 0x8d,
    0xb2, 0xf6, 0x00, 0x8b, 0x61, 0x2c, 0xd2, 0xf9,
    0xa8, 0xe0, 0x5d, 0xac, 0xdc, 0xa5, 0x34, 0xf3,
    0xda, 0x6c, 0xd4, 0x70, 0x92, 0xfb, 0x40, 0x26,
    0xc7, 0x9b, 0xe8, 0xd2, 0x10, 0x11, 0xcf, 0x7f,
    0x23, 0xd0, 0xed, 0x55, 0x52, 0x6d, 0xd3, 0xb2,
    0x56, 0x53, 0x8d, 0x7c, 0x4c, 0xb8, 0xcc, 0xb5,
    0xfd, 0xd0, 0x45, 0x4f, 0x62, 0x40, 0x54, 0x42,
    0x68, 0xd5, 0xe5, 0xdd, 0xf0, 0x76, 0x94, 0x59,
    0x1a, 0x57, 0x13, 0xb4, 0xc3, 0x70, 0xcc, 0xbd,
    0x4c, 0x2e, 0xc8, 0x6b, 0x9d, 0x68, 0xd0, 0x72,
    0x6a, 0x94, 0xd2, 0x18, 0xb5, 0x3b, 0x86, 0x45,
    0x95, 0xaa, 0x50, 0xda, 0x35, 0xeb, 0x69, 0x44,
    0x1f, 0xf3, 0x3a, 0x51, 0xbb, 0x1d, 0x08, 0x42,
    0x12, 0xd7, 0xd6, 0x21, 0xd8, 0x9b, 0x87, 0x55,
};

static const unsigned char rsa_sigverx931_1_sig[] = {
    0x3b, 0xba, 0xb3, 0xb1, 0xb2, 0x6a, 0x29, 0xb5,
    0xf9, 0x94, 0xf1, 0x00, 0x5c, 0x16, 0x67, 0x67,
    0x73, 0xd3, 0xde, 0x7e, 0x07, 0xfa, 0xaa, 0x95,
    0xeb, 0x5a, 0x55, 0xdc, 0xb2, 0xa9, 0x70, 0x5a,
    0xee, 0x8f, 0x8d, 0x69, 0x85, 0x2b, 0x00, 0xe3,
    0xdc, 0xe2, 0x73, 0x9b, 0x68, 0xeb, 0x93, 0x69,
    0x08, 0x03, 0x17, 0xd6, 0x50, 0x21, 0x14, 0x23,
    0x8c, 0xe6, 0x54, 0x3a, 0xd9, 0xfc, 0x8b, 0x14,
    0x81, 0xb1, 0x8b, 0x9d, 0xd2, 0xbe, 0x58, 0x75,
    0x94, 0x74, 0x93, 0xc9, 0xbb, 0x4e, 0xf6, 0x1f,
    0x73, 0x7d, 0x1a, 0x5f, 0xbd, 0xbf, 0x59, 0x37,
    0x5b, 0x98, 0x54, 0xad, 0x3a, 0xef, 0xa0, 0xef,
    0xcb, 0xc3, 0xe8, 0x84, 0xd8, 0x3d, 0xf5, 0x60,
    0xb8, 0xc3, 0x8d, 0x1e, 0x78, 0xa0, 0x91, 0x94,
    0xb7, 0xd7, 0xb1, 0xd4, 0xe2, 0xee, 0x81, 0x93,
    0xfc, 0x41, 0xf0, 0x31, 0xbb, 0x03, 0x52, 0xde,
    0x80, 0x20, 0x3a, 0x68, 0xe6, 0xc5, 0x50, 0x1b,
    0x08, 0x3f, 0x40, 0xde, 0xb3, 0xe5, 0x81, 0x99,
    0x7f, 0xdb, 0xb6, 0x5d, 0x61, 0x27, 0xd4, 0xfb,
    0xcd, 0xc5, 0x7a, 0xea, 0xde, 0x7a, 0x66, 0xef,
    0x55, 0x3f, 0x85, 0xea, 0x84, 0xc5, 0x0a, 0xf6,
    0x3c, 0x40, 0x38, 0xf7, 0x6c, 0x66, 0xe5, 0xbe,
    0x61, 0x41, 0xd3, 0xb1, 0x08, 0xe1, 0xb4, 0xf9,
    0x6e, 0xf6, 0x0e, 0x4a, 0x72, 0x6c, 0x61, 0x63,
    0x3e, 0x41, 0x33, 0x94, 0xd6, 0x27, 0xa4, 0xd9,
    0x3a, 0x20, 0x2b, 0x39, 0xea, 0xe5, 0x82, 0x48,
    0xd6, 0x5b, 0x58, 0x85, 0x44, 0xb0, 0xd2, 0xfd,
    0xfb, 0x3e, 0xeb, 0x78, 0xac, 0xbc, 0xba, 0x16,
    0x92, 0x0e, 0x20, 0xc1, 0xb2, 0xd1, 0x92, 0xa8,
    0x00, 0x88, 0xc0, 0x41, 0x46, 0x38, 0xb6, 0x54,
    0x70, 0x0c, 0x00, 0x62, 0x97, 0x6a, 0x8e, 0x66,
    0x5a, 0xa1, 0x6c, 0xf7, 0x6d, 0xc2, 0x27, 0x56,
    0x60, 0x5b, 0x0c, 0x52, 0xac, 0x5c, 0xae, 0x99,
    0x55, 0x11, 0x62, 0x52, 0x09, 0x48, 0x53, 0x90,
    0x3c, 0x0b, 0xd4, 0xdc, 0x7b, 0xe3, 0x4c, 0xe3,
    0xa8, 0x6d, 0xc5, 0xdf, 0xc1, 0x5c, 0x59, 0x25,
    0x99, 0x30, 0xde, 0x57, 0x6a, 0x84, 0x25, 0x34,
    0x3e, 0x64, 0x11, 0xdb, 0x7a, 0x82, 0x8e, 0x70,
    0xd2, 0x5c, 0x0e, 0x81, 0xa0, 0x24, 0x53, 0x75,
    0x98, 0xd6, 0x10, 0x01, 0x6a, 0x14, 0xed, 0xc3,
    0x6f, 0xc4, 0x18, 0xb8, 0xd2, 0x9f, 0x59, 0x53,
    0x81, 0x3a, 0x86, 0x31, 0xfc, 0x9e, 0xbf, 0x6c,
    0x52, 0x93, 0x86, 0x9c, 0xaa, 0x6c, 0x6f, 0x07,
    0x8a, 0x40, 0x33, 0x64, 0xb2, 0x70, 0x48, 0x85,
    0x05, 0x59, 0x65, 0x2d, 0x6b, 0x9a, 0xad, 0xab,
    0x20, 0x7e, 0x02, 0x6d, 0xde, 0xcf, 0x22, 0x0b,
    0xea, 0x6e, 0xbd, 0x1c, 0x39, 0x3a, 0xfd, 0xa4,
    0xde, 0x54, 0xae, 0xde, 0x5e, 0xf7, 0xb0, 0x6d,
};

static const struct rsa_sigver_st rsa_sigver_data[] = {
    {
        "pkcs1", /* pkcs1v1.5 */
        2048,
        "SHA224",
        ITM(rsa_sigver15_0_msg),
        ITM(rsa_sigver15_0_n),
        ITM(rsa_sigver15_0_e),
        ITM(rsa_sigver15_0_sig),
        NO_PSS_SALT_LEN,
        PASS
    },
    {
        "pkcs1", /* pkcs1v1.5 */
        2048,
        "SHA224",
        ITM(rsa_sigver15_1_msg),
        ITM(rsa_sigver15_1_n),
        ITM(rsa_sigver15_1_e),
        ITM(rsa_sigver15_1_sig),
        NO_PSS_SALT_LEN,
        FAIL
    },
    {
        "x931",
        3072,
        "SHA1",
        ITM(rsa_sigverx931_0_msg),
        ITM(rsa_sigverx931_0_n),
        ITM(rsa_sigverx931_0_e),
        ITM(rsa_sigverx931_0_sig),
        NO_PSS_SALT_LEN,
        PASS
    },
    {
        "x931",
        3072,
        "SHA256",
        ITM(rsa_sigverx931_1_msg),
        ITM(rsa_sigverx931_1_n),
        ITM(rsa_sigverx931_1_e),
        ITM(rsa_sigverx931_1_sig),
        NO_PSS_SALT_LEN,
        FAIL
    },
    {
        "pss",
        4096,
        "SHA384",
        ITM(rsa_sigverpss_0_msg),
        ITM(rsa_sigverpss_0_n),
        ITM(rsa_sigverpss_0_e),
        ITM(rsa_sigverpss_0_sig),
        62,
        PASS
    },
    {
        "pss",
        4096,
        "SHA384",
        ITM(rsa_sigverpss_1_msg),
        ITM(rsa_sigverpss_1_n),
        ITM(rsa_sigverpss_1_e),
        ITM(rsa_sigverpss_1_sig),
        62,
        FAIL
    },
};

struct rsa_decrypt_prim_st {
    const unsigned char *ct;
    size_t ct_len;
};

static const unsigned char rsa_decrypt_prim_0_ct[] = {
    0x09, 0x7e, 0x82, 0xfe, 0xc7, 0x24, 0x65, 0xe0,
    0x49, 0x2e, 0x78, 0xed, 0xf4, 0x7d, 0x05, 0x0d,
    0xff, 0x2f, 0x1a, 0x95, 0xeb, 0x74, 0x60, 0x3d,
    0xd3, 0x3a, 0xec, 0x8a, 0x2c, 0x8b, 0x00, 0xa5,
    0x75, 0x2c, 0x87, 0x7b, 0xa5, 0x76, 0x08, 0xee,
    0x99, 0xab, 0x5b, 0x21, 0x69, 0x90, 0x72, 0x0d,
    0x55, 0xe4, 0x7d, 0x1d, 0xcb, 0xaa, 0xeb, 0x32,
    0x24, 0xf7, 0xce, 0x95, 0xb5, 0x3e, 0x0e, 0x57,
    0xd4, 0x2a, 0x5b, 0xfc, 0x1f, 0xf7, 0x28, 0x3f,
    0xd6, 0x31, 0x36, 0x92, 0xc5, 0x13, 0xe3, 0x4e,
    0x28, 0x53, 0xbe, 0x60, 0x5f, 0x82, 0x12, 0x7a,
    0x50, 0xe6, 0x91, 0x40, 0xcf, 0x52, 0x3a, 0xd2,
    0x15, 0x20, 0xd5, 0x82, 0x6d, 0x5e, 0xab, 0x47,
    0xd1, 0x2d, 0x00, 0xf5, 0xea, 0xf4, 0x68, 0x88,
    0x38, 0x43, 0xd6, 0xcb, 0xaa, 0xd0, 0xd1, 0x75,
    0xe6, 0x87, 0x5f, 0xd1, 0x89, 0xd3, 0x57, 0x1b,
    0xf2, 0x45, 0x8a, 0x92, 0xe6, 0x95, 0xb8, 0x99,
    0x80, 0xe9, 0xe6, 0x5f, 0x2b, 0x48, 0x2b, 0xb3,
    0x2b, 0x80, 0x56, 0xf8, 0xd4, 0x96, 0x44, 0xb5,
    0xae, 0x6d, 0x4a, 0x3d, 0x7b, 0x0a, 0x54, 0x3c,
    0xa8, 0x21, 0x8b, 0x64, 0x96, 0xea, 0xc2, 0xef,
    0x60, 0xbb, 0xd3, 0x4e, 0xaf, 0x6c, 0x5b, 0x06,
    0x57, 0xe8, 0x5e, 0x2c, 0x87, 0x46, 0x12, 0xeb,
    0xfb, 0xe2, 0xdb, 0x7b, 0xac, 0x09, 0x8b, 0xa0,
    0x98, 0x6e, 0xc6, 0x3f, 0x98, 0xdd, 0x7d, 0xc6,
    0xc6, 0x32, 0xc2, 0xcc, 0x73, 0xe2, 0x15, 0xde,
    0xb2, 0x0f, 0x41, 0x08, 0x1e, 0x2e, 0xba, 0x93,
    0x65, 0x94, 0xab, 0x84, 0x0e, 0x1e, 0xda, 0x1b,
    0xf0, 0xe0, 0x13, 0x13, 0xe2, 0xa5, 0x31, 0xb8,
    0x80, 0xc1, 0x38, 0xc5, 0x08, 0x09, 0x0a, 0xe2,
    0x78, 0x7d, 0xd6, 0xcf, 0x8d, 0x6b, 0xe8, 0x1b,
    0x47, 0x83, 0x80, 0x71, 0xe2, 0xd3, 0x01, 0xbc,
};

static const unsigned char rsa_decrypt_prim_1_ct[] = {
    0xff, 0xd5, 0xaa, 0x3f, 0x0c, 0x7c, 0x78, 0x7e,
    0xe3, 0x8a, 0x4f, 0xcc, 0x20, 0x3f, 0x51, 0xe5,
    0xf4, 0x9c, 0xc5, 0x62, 0xcc, 0xa3, 0xcb, 0xce,
    0x39, 0x80, 0x35, 0xef, 0xd5, 0x95, 0x56, 0xcb,
    0xb2, 0x62, 0x8c, 0xe6, 0x8b, 0x20, 0xe4, 0x36,
    0xae, 0xe8, 0x07, 0x07, 0xc2, 0x23, 0x6a, 0xfc,
    0x83, 0xf0, 0x04, 0x88, 0x19, 0xf8, 0x9f, 0x5c,
    0x59, 0x4d, 0xb3, 0x81, 0x86, 0x9d, 0x3b, 0x61,
    0x73, 0x31, 0x03, 0xec, 0x9c, 0xdd, 0x75, 0xb7,
    0x37, 0x0a, 0x8d, 0x94, 0xd9, 0x9f, 0x6d, 0x85,
    0xb0, 0x5c, 0x08, 0xcc, 0xb4, 0x27, 0x8c, 0xf0,
    0xe6, 0xd6, 0xe0, 0xc1, 0x57, 0x59, 0xaa, 0xc7,
    0x8f, 0x5c, 0xa7, 0x4b, 0x3c, 0x81, 0x4a, 0xa3,
    0x9b, 0x18, 0x88, 0x04, 0x98, 0x54, 0x3d, 0x87,
    0x2a, 0x89, 0xb6, 0x41, 0xe8, 0xbd, 0x37, 0x17,
    0x03, 0xa8, 0xf1, 0x37, 0xa5, 0x5e, 0x02, 0x13,
    0x67, 0x08, 0xec, 0x9e, 0x97, 0xf5, 0xcc, 0x5f,
    0x75, 0x37, 0xbe, 0xce, 0xe8, 0x5e, 0xa1, 0xca,
    0x46, 0xa3, 0xda, 0xe4, 0x1f, 0xf8, 0xc4, 0xa3,
    0x26, 0xbb, 0xed, 0xa2, 0x71, 0xb2, 0x44, 0x00,
    0xd3, 0xe5, 0x06, 0xf1, 0xb4, 0xc1, 0xe0, 0x29,
    0xca, 0xeb, 0xe0, 0xdf, 0xd1, 0x69, 0x5f, 0xa9,
    0x03, 0x7c, 0x49, 0x93, 0xfb, 0xc2, 0xdf, 0x39,
    0xbc, 0x2a, 0x6b, 0x59, 0x7d, 0xf4, 0x84, 0x93,
    0xa2, 0x8b, 0x7a, 0x5a, 0x7a, 0xa9, 0xff, 0x41,
    0x4c, 0x52, 0x5c, 0xf9, 0x59, 0xd2, 0x91, 0xc3,
    0xa9, 0xe8, 0x23, 0x36, 0x5f, 0x2f, 0xb9, 0xbe,
    0x22, 0xc4, 0xfd, 0x84, 0x5f, 0x81, 0x3d, 0x94,
    0xf8, 0xa4, 0x9b, 0xae, 0xc0, 0xb5, 0x78, 0x4f,
    0x91, 0x76, 0x02, 0x5d, 0x60, 0x71, 0x8b, 0xeb,
    0x08, 0x42, 0xe3, 0xb3, 0x63, 0x05, 0x60, 0x59,
    0x98, 0xc1, 0x6d, 0x66, 0xb3, 0xc5, 0x8a, 0xbc,
};

static const struct rsa_decrypt_prim_st rsa_decrypt_prim_data[] = {
    {
        ITM(rsa_decrypt_prim_0_ct),
    },
    {
        ITM(rsa_decrypt_prim_1_ct),
    },
};

struct drbg_st {
    const char *drbg_name;
    const char *cipher;
    int use_df;

    const unsigned char *entropy_input;
    size_t entropy_input_len;
    const unsigned char *nonce;
    size_t nonce_len;
    const unsigned char *returned_bits;
    size_t returned_bits_len;
};

static const unsigned char drbg_entropy_input[] = {
    0x36, 0x40, 0x19, 0x40, 0xfa, 0x8b, 0x1f, 0xba,
    0x91, 0xa1, 0x66, 0x1f, 0x21, 0x1d, 0x78, 0xa0,
    0xb9, 0x38, 0x9a, 0x74, 0xe5, 0xbc, 0xcf, 0xec,
    0xe8, 0xd7, 0x66, 0xaf, 0x1a, 0x6d, 0x3b, 0x14
};

static const unsigned char drbg_nonce[] = {
    0x49, 0x6f, 0x25, 0xb0, 0xf1, 0x30, 0x1b, 0x4f,
    0x50, 0x1b, 0xe3, 0x03, 0x80, 0xa1, 0x37, 0xeb
};

static const unsigned char drbg_returned_bits[] = {
    0x58, 0x62, 0xeb, 0x38, 0xbd, 0x55, 0x8d, 0xd9,
    0x78, 0xa6, 0x96, 0xe6, 0xdf, 0x16, 0x47, 0x82,
    0xdd, 0xd8, 0x87, 0xe7, 0xe9, 0xa6, 0xc9, 0xf3,
    0xf1, 0xfb, 0xaf, 0xb7, 0x89, 0x41, 0xb5, 0x35,
    0xa6, 0x49, 0x12, 0xdf, 0xd2, 0x24, 0xc6, 0xdc,
    0x74, 0x54, 0xe5, 0x25, 0x0b, 0x3d, 0x97, 0x16,
    0x5e, 0x16, 0x26, 0x0c, 0x2f, 0xaf, 0x1c, 0xc7,
    0x73, 0x5c, 0xb7, 0x5f, 0xb4, 0xf0, 0x7e, 0x1d
};

static const unsigned char drbg_key_0[] = {
    0x33, 0x63, 0xd9, 0x00, 0x0e, 0x6d, 0xb4, 0x7c,
    0x16, 0xd3, 0xfc, 0x65, 0xf2, 0x87, 0x2c, 0x08,
    0xa3, 0x5f, 0x99, 0xb2, 0xd1, 0x74, 0xaf, 0xa5,
    0x37, 0xa6, 0x6e, 0xc1, 0x53, 0x05, 0x2d, 0x98
};

static const struct drbg_st drbg_data[] = {
    {
        "CTR-DRBG",
        "AES-256-CTR",
        1,
        ITM(drbg_entropy_input),
        ITM(drbg_nonce),
        ITM(drbg_returned_bits)
    }
};
