mydir=lib$(S)kadm5$(S)unit-test
BUILDTOP=$(REL)..$(S)..$(S)..
KDB_DEP_LIB=$(DL_LIB) $(THREAD_LINKOPTS)

SRCS= init-test.c destroy-test.c handle-test.c iter-test.c setkey-test.c \
	randkey-test.c lock-test.c

#
# The client-side test programs.
#

init-test: init-test.o $(KADMCLNT_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o init-test init-test.o \
		$(KADMCLNT_LIBS) $(KRB5_BASE_LIBS)

destroy-test: destroy-test.o $(KADMCLNT_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o destroy-test destroy-test.o \
		$(KADMCLNT_LIBS) $(KRB5_BASE_LIBS)

client-handle-test: client-handle-test.o $(KADMCLNT_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o client-handle-test client-handle-test.o \
		$(KADMCLNT_LIBS) $(KRB5_BASE_LIBS)

client-handle-test.o: handle-test.c
	$(CC) $(ALL_CFLAGS) -DCLIENT_TEST -o client-handle-test.o -c $(srcdir)/handle-test.c

client-iter-test: iter-test.o $(KADMLCNT_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o client-iter-test iter-test.o \
		$(KADMCLNT_LIBS) $(KRB5_BASE_LIBS)

client-setkey-test: setkey-test.o $(KADMCLNT_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o client-setkey-test setkey-test.o \
		$(KADMCLNT_LIBS) $(KRB5_BASE_LIBS)

#
# The server-side test programs.
#

randkey-test: randkey-test.o $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o randkey-test randkey-test.o \
		$(KADMSRV_LIBS) $(KDB_DEP_LIB) $(KRB5_BASE_LIBS)

server-handle-test: handle-test.o $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o server-handle-test handle-test.o \
		$(KADMSRV_LIBS) $(KDB_DEP_LIB) $(KRB5_BASE_LIBS)

lock-test: lock-test.o $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o lock-test lock-test.o \
		$(KADMSRV_LIBS) $(KDB_DEP_LIB) $(KRB5_BASE_LIBS)

server-iter-test: iter-test.o $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o server-iter-test iter-test.o \
		$(KADMSRV_LIBS) $(KDB_DEP_LIB) $(KRB5_BASE_LIBS)

server-setkey-test: setkey-test.o $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o server-setkey-test setkey-test.o \
		$(KADMSRV_LIBS) $(KDB_DEP_LIB) $(KRB5_BASE_LIBS)

#
# The unit-test targets
#

check: check-@DO_TEST@

check-:
	@echo "+++"
	@echo "+++ WARNING: lib/kadm5 unit tests not run."
	@echo "+++ Either tcl, runtest, or Perl is unavailable."
	@echo "+++"

check-ok unit-test: unit-test-client unit-test-server

unit-test-client: unit-test-client-setup unit-test-client-body \
	unit-test-client-cleanup

unit-test-server: unit-test-server-setup unit-test-server-body \
	unit-test-server-cleanup

test-randkey: randkey-test
	$(ENV_SETUP) $(VALGRIND) ./randkey-test

test-handle-server: server-handle-test
	$(ENV_SETUP) $(VALGRIND) ./server-handle-test

test-handle-client: client-handle-test
	$(ENV_SETUP) $(VALGRIND) ./client-handle-test

test-noauth: init-test
	$(ENV_SETUP) $(VALGRIND) ./init-test

test-destroy: destroy-test
	$(ENV_SETUP) $(VALGRIND) ./destroy-test

test-setkey-client: client-setkey-test
	$(ENV_SETUP) $(VALGRIND) ./client-setkey-test testkeys admin admin

unit-test-client-setup:
	$(ENV_SETUP) $(VALGRIND) $(START_SERVERS)

unit-test-client-cleanup:
	$(ENV_SETUP) $(STOP_SERVERS)

unit-test-server-setup:
	$(ENV_SETUP) $(VALGRIND) $(START_SERVERS_LOCAL)

unit-test-server-cleanup:
	$(ENV_SETUP) $(STOP_SERVERS_LOCAL)

unit-test-client-body: site.exp test-noauth test-destroy test-handle-client \
	test-setkey-client
	$(ENV_SETUP) $(RUNTEST) --tool api RPC=1 API=$(CLNTTCL) \
		KINIT=$(BUILDTOP)/clients/kinit/kinit \
		KDESTROY=$(BUILDTOP)/clients/kdestroy/kdestroy \
		KADMIN_LOCAL=$(BUILDTOP)/kadmin/cli/kadmin.local \
		PRIOCNTL_HACK=@PRIOCNTL_HACK@ VALGRIND="$(VALGRIND)" \
		$(RUNTESTFLAGS)
	-mv api.log capi.log
	-mv api.sum capi.sum

unit-test-server-body: site.exp test-handle-server lock-test 
	$(ENV_SETUP) $(RUNTEST) --tool api RPC=0 API=$(SRVTCL) \
		LOCKTEST=./lock-test \
		KADMIN_LOCAL=$(BUILDTOP)/kadmin/cli/kadmin.local \
		PRIOCNTL_HACK=@PRIOCNTL_HACK@ VALGRIND="$(VALGRIND)" \
		$(RUNTESTFLAGS)
	-mv api.log sapi.log
	-mv api.sum sapi.sum

clean:
	$(RM) init-test client_init.o init-test.o
	$(RM) destroy-test destroy-test.o
	$(RM) client-handle-test handle-test.o client-handle-test.o
	$(RM) client-iter-test iter-test.o
	$(RM) randkey-test randkey-test.o
	$(RM) server-handle-test handle-test.o
	$(RM) lock-test lock-test.o
	$(RM) server-iter-test iter-test.o
	$(RM) server-setkey-test client-setkey-test setkey-test.o
	$(RM) *.log *.plog *.sum *.psum unit-test-log.*
