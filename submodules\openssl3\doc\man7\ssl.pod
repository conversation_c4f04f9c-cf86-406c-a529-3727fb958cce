=pod

=head1 NAME

ssl - OpenSSL SSL/TLS library

=head1 SYNOPSIS

See the individual manual pages for details.

=head1 DESCRIPTION

The OpenSSL B<ssl> library implements several versions of the
Secure Sockets Layer, Transport Layer Security, and Datagram Transport Layer
Security protocols.
This page gives a brief overview of the extensive API and data types
provided by the library.

An B<SSL_CTX> object is created as a framework to establish
TLS/SSL enabled connections (see L<SSL_CTX_new(3)>).
Various options regarding certificates, algorithms etc. can be set
in this object.

When a network connection has been created, it can be assigned to an
B<SSL> object. After the B<SSL> object has been created using
L<SSL_new(3)>, L<SSL_set_fd(3)> or
L<SSL_set_bio(3)> can be used to associate the network
connection with the object.

When the TLS/SSL handshake is performed using
L<SSL_accept(3)> or L<SSL_connect(3)>
respectively.
L<SSL_read_ex(3)>, L<SSL_read(3)>, L<SSL_write_ex(3)> and L<SSL_write(3)> are
used to read and write data on the TLS/SSL connection.
L<SSL_shutdown(3)> can be used to shut down the
TLS/SSL connection.

=head1 DATA STRUCTURES

Here are some of the main data structures in the library.

=over 4

=item B<SSL_METHOD> (SSL Method)

This is a dispatch structure describing the internal B<ssl> library
methods/functions which implement the various protocol versions (SSLv3
TLSv1, ...). It's needed to create an B<SSL_CTX>.

=item B<SSL_CIPHER> (SSL Cipher)

This structure holds the algorithm information for a particular cipher which
are a core part of the SSL/TLS protocol. The available ciphers are configured
on a B<SSL_CTX> basis and the actual ones used are then part of the
B<SSL_SESSION>.

=item B<SSL_CTX> (SSL Context)

This is the global context structure which is created by a server or client
once per program life-time and which holds mainly default values for the
B<SSL> structures which are later created for the connections.

=item B<SSL_SESSION> (SSL Session)

This is a structure containing the current TLS/SSL session details for a
connection: B<SSL_CIPHER>s, client and server certificates, keys, etc.

=item B<SSL> (SSL Connection)

This is the main SSL/TLS structure which is created by a server or client per
established connection. This actually is the core structure in the SSL API.
At run-time the application usually deals with this structure which has
links to mostly all other structures.

=back

=head1 HEADER FILES

Currently the OpenSSL B<ssl> library provides the following C header files
containing the prototypes for the data structures and functions:

=over 4

=item F<< <openssl/ssl.h> >>

This is the common header file for the SSL/TLS API.  Include it into your
program to make the API of the B<ssl> library available. It internally
includes both more private SSL headers and headers from the B<crypto> library.
Whenever you need hard-core details on the internals of the SSL API, look
inside this header file.
This file also includes the others listed below.

=item F<< <openssl/ssl2.h> >>

Unused. Present for backwards compatibility only.

=item F<< <openssl/ssl3.h> >>

This is the sub header file dealing with the SSLv3 protocol only.

=item F<< <openssl/tls1.h> >>

This is the sub header file dealing with the TLSv1 protocol only.

=back

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
