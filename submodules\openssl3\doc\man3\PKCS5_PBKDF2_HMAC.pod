=pod

=head1 NAME

PKCS5_PBKDF2_HMAC, PKCS5_PBKDF2_HMAC_SHA1 - password based derivation routines with salt and iteration count

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int PKCS5_PBKDF2_HMAC(const char *pass, int passlen,
                       const unsigned char *salt, int saltlen, int iter,
                       const EVP_MD *digest,
                       int keylen, unsigned char *out);

 int PKCS5_PBKDF2_HMAC_SHA1(const char *pass, int passlen,
                            const unsigned char *salt, int saltlen, int iter,
                            int keylen, unsigned char *out);

=head1 DESCRIPTION

PKCS5_PBKDF2_HMAC() derives a key from a password using a salt and iteration count
as specified in RFC 2898.

B<pass> is the password used in the derivation of length B<passlen>. B<pass>
is an optional parameter and can be NULL. If B<passlen> is -1, then the
function will calculate the length of B<pass> using strlen().

B<salt> is the salt used in the derivation of length B<saltlen>. If the
B<salt> is NULL, then B<saltlen> must be 0. The function will not
attempt to calculate the length of the B<salt> because it is not assumed to
be NULL terminated.

B<iter> is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
B<iter> value less than 1 is invalid; such values will result in failure
and raise the PROV_R_INVALID_ITERATION_COUNT error.

B<digest> is the message digest function used in the derivation.
PKCS5_PBKDF2_HMAC_SHA1() calls PKCS5_PBKDF2_HMAC() with EVP_sha1().

The derived key will be written to B<out>. The size of the B<out> buffer
is specified via B<keylen>.

=head1 NOTES

A typical application of this function is to derive keying material for an
encryption algorithm from a password in the B<pass>, a salt in B<salt>,
and an iteration count.

Increasing the B<iter> parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.

These functions make no assumption regarding the given password.
It will simply be treated as a byte sequence.

=head1 RETURN VALUES

PKCS5_PBKDF2_HMAC() and PBKCS5_PBKDF2_HMAC_SHA1() return 1 on success or 0 on error.

=head1 SEE ALSO

L<evp(7)>, L<RAND_bytes(3)>,
L<EVP_BytesToKey(3)>,
L<passphrase-encoding(7)>

=head1 COPYRIGHT

Copyright 2014-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
