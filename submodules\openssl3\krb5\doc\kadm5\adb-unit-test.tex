% This document is included for historical purposes only, and does not
% apply to krb5 today.

\documentstyle[times,fullpage]{article}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Make _ actually generate an _, and allow line-breaking after it.
\let\underscore=\_
\catcode`_=13
\def_{\underscore\penalty75\relax}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\newcommand{\test}[1]{\begin{description}
\setlength{\itemsep}{0pt}
#1
\end{description}

}

\newcommand{\numtest}[2]{\begin{description}
\setlength{\itemsep}{0pt}
\Number{#1}
#2
\end{description}

}

\newcommand{\Number}[1]{\item[Number:] #1}
\newcommand{\Reason}[1]{\item[Reason:] #1}
%\newcommand{\Call}[1]{\item[Call:] #1}
\newcommand{\Expected}[1]{\item[Expected:] #1}
\newcommand{\Conditions}[1]{\item[Conditions:] #1}
\newcommand{\Priority}[1]{\item[Priority:] #1}
\newcommand{\Status}[1]{\item[Status:] #1}
%\newcommand{\Number}[1]{}
%\newcommand{\Reason}[1]{}
\newcommand{\Call}[1]{}
%\newcommand{\Expected}[1]{}
%\newcommand{\Conditions}[1]{}
%\newcommand{\Priority}[1]{}

\title{OpenV*Secure Admin Database API\\
Unit Test Description}
\author{Jonathan I. Kamens}

\begin{document}

\maketitle

%\tableofcontents

\section{Introduction}

The following is a description of a black-box unit test of the
OpenV*Secure Admin Database API (osa_adb).  Each API function is
listed, followed by the tests that should be performed on it.

The tests described here are based on the ``OV*Secure Admin Server
Implementation Design'' revision 1.14.

\section{osa_adb_get_lock and osa_adb_release_lock}

\numtest{1}{
\Reason{A shared lock can be acquired.}
\Status{Implemented}
}

\numtest{2}{
\Reason{An exclusive lock can be acquired and released.}
\Status{Implemented}
}

\numtest{3}{
\Reason{A permanent lock can be acquired and released.}
\Status{Implemented}
}

\numtest{4}{
\Reason{Attempting to release a lock when none is held fails with
NOTLOCKED.}
\Status{Implemented}
}

\numtest{5}{
\Reason{Two processes can both acquire a shared lock.}
\Status{Implemented}
}

\numtest{6}{
\Reason{An attempt to acquire a shared lock while another process holds an
exclusive lock fails with CANTLOCK_DB.}
\Status{Implemented}
}

\numtest{7}{
\Reason{An attempt to acquire an exclusive lock while another process holds a
shared lock fails with CANTLOCK_DB.}
\Status{Implemented}
}

\numtest{8}{
\Reason{An attempt to open the database while a process holds a
permanent lock fails with NO_LOCKFILE.}
\Status{Implemented}
}

\numtest{9}{
\Reason{An attempt to acquire an exclusive lock while a process holds a
permanent lock fails with NO_LOCKFILE.}
\Status{Implemented}
}

\numtest{10}{
\Reason{Acquiring a permanent lock deletes the lockfile.}
\Status{Implemented}
}

\numtest{11}{
\Reason{Releasing a permanent lock re-creates the lockfile.}
\Status{Implemented}
}

\numtest{12}{
\Reason{A process can perform a get operation while another process holds a
shared lock.}
\Status{Implemented}
}

\numtest{13}{
\Reason{A process that is running and has opened the adb principal database
can retrieve a principal created after the open occurred.}
\Status{Implemented, but not working}
}

\end{document}
