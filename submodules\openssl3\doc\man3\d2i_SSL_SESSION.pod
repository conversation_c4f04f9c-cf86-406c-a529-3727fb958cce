=pod

=head1 NAME

d2i_SSL_SESSION, i2d_SSL_SESSION - convert SSL_SESSION object from/to ASN1 representation

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 SSL_SESSION *d2i_SSL_SESSION(SSL_SESSION **a, const unsigned char **pp,
                              long length);
 int i2d_SSL_SESSION(SSL_SESSION *in, unsigned char **pp);

=head1 DESCRIPTION

These functions decode and encode an SSL_SESSION object.
For encoding details see L<d2i_X509(3)>.

SSL_SESSION objects keep internal link information about the session cache
list, when being inserted into one SSL_CTX object's session cache.
One SSL_SESSION object, regardless of its reference count, must therefore
only be used with one SSL_CTX object (and the SSL objects created
from this SSL_CTX object).

=head1 RETURN VALUES

d2i_SSL_SESSION() returns a pointer to the newly allocated SSL_SESSION
object. In case of failure the NULL-pointer is returned and the error message
can be retrieved from the error stack.

i2d_SSL_SESSION() returns the size of the ASN1 representation in bytes.
When the session is not valid, B<0> is returned and no operation is performed.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_SESSION_free(3)>,
L<SSL_CTX_sess_set_get_cb(3)>,
L<d2i_X509(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
