=pod

=head1 NAME

EVP_MD-common - The OpenSSL EVP_MD implementations, common things

=head1 DESCRIPTION

All the OpenSSL EVP_MD implementations understand the following
L<OSSL_PARAM(3)> entries that are
gettable with L<EVP_MD_get_params(3)>, as well as these:

=over 4

=item "blocksize" (B<OSSL_DIGEST_PARAM_BLOCK_SIZE>) <unsigned integer>

The digest block size.
The length of the "blocksize" parameter should not exceed that of a
B<size_t>.

This value can also be retrieved with L<EVP_MD_get_block_size(3)>.

=item "size" (B<OSSL_DIGEST_PARAM_SIZE>) <unsigned integer>

The digest output size.
The length of the "size" parameter should not exceed that of a B<size_t>.

This value can also be retrieved with L<EVP_MD_get_size(3)>.

=item "flags" (B<OSSL_DIGEST_PARAM_FLAGS>) <unsigned integer>

Diverse flags that describe exceptional behaviour for the digest.
These flags are described in L<EVP_MD_meth_set_flags(3)/DESCRIPTION>.

The length of the "flags" parameter should equal that of an
B<unsigned long int>.

=begin comment

The description of these flags should probably be moved.  Also,
<PERSON>VP_MD_FLAG_FIPS isn't relevant any more.

=end comment

This value can also be retrieved with L<EVP_MD_get_flags(3)>.

=back

=head1 SEE ALSO

L<EVP_MD_get_params(3)>, L<provider-digest(7)>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
