=pod

=head1 NAME

BN_zero, BN_one, BN_value_one, BN_set_word, BN_get_word - BIGNUM assignment
operations

=head1 SYNOPSIS

 #include <openssl/bn.h>

 void BN_zero(BIGNUM *a);
 int BN_one(BIGNUM *a);

 const BIGNUM *BN_value_one(void);

 int BN_set_word(BIGNUM *a, BN_ULONG w);
 unsigned BN_ULONG BN_get_word(BIGNUM *a);

=head1 DESCRIPTION

B<BN_ULONG> is a macro that will be an unsigned integral type optimized
for the most efficient implementation on the local platform.

BN_zero(), BN_one() and BN_set_word() set B<a> to the values 0, 1 and
B<w> respectively.  BN_zero() and BN_one() are macros.

BN_value_one() returns a B<BIGNUM> constant of value 1. This constant
is useful for use in comparisons and assignment.

BN_get_word() returns B<a>, if it can be represented as a B<BN_ULONG>.

=head1 RETURN VALUES

BN_get_word() returns the value B<a>, or all-bits-set if B<a> cannot
be represented as a single integer.

BN_one() and BN_set_word() return 1 on success, 0 otherwise.
BN_value_one() returns the constant.
BN_zero() never fails and returns no value.

=head1 BUGS

If a B<BIGNUM> is equal to the value of all-bits-set, it will collide
with the error condition returned by BN_get_word() which uses that
as an error value.

B<BN_ULONG> should probably be a typedef.

=head1 SEE ALSO

L<BN_bn2bin(3)>

=head1 HISTORY

In OpenSSL 0.9.8, BN_zero() was changed to not return a value; previous
versions returned an int.

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
