=pod

=head1 NAME

DTLS_timer_cb,
DTLS_set_timer_cb
- Set callback for controlling DTLS timer duration

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 typedef unsigned int (*DTLS_timer_cb)(SSL *s, unsigned int timer_us);

 void DTLS_set_timer_cb(SSL *s, DTLS_timer_cb cb);

=head1 DESCRIPTION

This function sets an optional callback function for controlling the
timeout interval on the DTLS protocol. The callback function will be
called by DTLS for every new DTLS packet that is sent.

=head1 RETURN VALUES

Returns void.

=head1 HISTORY

The DTLS_set_timer_cb() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
