=pod

=head1 NAME

OSSL_thread_stop_handler_fn,
ossl_init_thread_start,
ossl_init_thread_deregister
- internal thread routines

=head1 SYNOPSIS

 #include "crypto/cryptlib.h"
 #include <openssl/core.h>

 typedef void (*OSSL_thread_stop_handler_fn)(void *arg);

 int ossl_init_thread_start(const void *index, void *arg,
                            OSSL_thread_stop_handler_fn handfn);
 int ossl_init_thread_deregister(void *index);

=head1 DESCRIPTION

Thread aware code may be informed about when a thread is stopping, typically to
perform some cleanup operation.
Thread stop events may be detected by OpenSSL either automatically (using the
capabilities of the underlying threading library) where possible or explicitly
by the application calling OPENSSL_thread_stop() or OPENSSL_thread_stop_ex().

Thread aware code registers a "stop handler" for each new thread that it uses.
Typically, when a new thread is being used, code will add a new value to some
thread local variable and then register a stop handler. When the thread is
stopping the stop handler is called (while on that thread) and the code can
clean up the value stored in the thread local variable.

A new stop handler is registered using the function ossl_init_thread_start().
The I<index> parameter should be a unique value that can be used to identify a
set of common stop handlers and is passed in a later call to
ossl_init_thread_deregister. If no later call to ossl_init_thread_deregister is
made then NULL can be passed for this parameter. The I<arg> parameter is passed
back as an argument to the stop handler when it is later invoked. Finally the
I<handfn> is a function pointer to the stop handler itself.

In the event that previously registered stop handlers need to be deregistered
then this can be done using the function ossl_init_thread_deregister().
This will deregister all stop handlers (no matter which thread they were
registered for) which the same I<index> value.

=head1 RETURN VALUES

ossl_init_thread_start() and ossl_init_thread_deregister() return 1 for success
or 0 on error.

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
