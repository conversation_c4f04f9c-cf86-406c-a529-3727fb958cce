#
# lib/krb5/error_tables/kdb5_err.et
#
# Copyright 1990, 2008 by the Massachusetts Institute of Technology.
# All Rights Reserved.
#
# Export of this software from the United States of America may
#   require a specific license from the United States Government.
#   It is the responsibility of any person or organization contemplating
#   export to obtain such a license before exporting.
# 
# WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
# distribute this software and its documentation for any purpose and
# without fee is hereby granted, provided that the above copyright
# notice appear in all copies and that both that copyright notice and
# this permission notice appear in supporting documentation, and that
# the name of M.I.T. not be used in advertising or publicity pertaining
# to distribution of the software without specific, written prior
# permission.  Furthermore if you modify this software you must label
# your software as modified software and not distribute it in such a
# fashion that it might be confused with the original M.I.T. software.
# M.I.T. makes no representations about the suitability of
# this software for any purpose.  It is provided "as is" without express
# or implied warranty.
# 
#
# The Kerberos v5 database library error code table.
#
error_table kdb5

ec KRB5_KDB_RCSID,	"$Id$"
# /* From the server side routines */
ec KRB5_KDB_INUSE,		"Entry already exists in database"
ec KRB5_KDB_UK_SERROR,		"Database store error"
ec KRB5_KDB_UK_RERROR,		"Database read error"
ec KRB5_KDB_UNAUTH,		"Insufficient access to perform requested operation"
# KRB5_KDB_DATA isn't really an error, but...
ec KRB5_KDB_NOENTRY,		"No such entry in the database"

ec KRB5_KDB_ILL_WILDCARD,	"Illegal use of wildcard"

ec KRB5_KDB_DB_INUSE,		"Database is locked or in use--try again later"

ec KRB5_KDB_DB_CHANGED,		"Database was modified during read"

ec KRB5_KDB_TRUNCATED_RECORD,	"Database record is incomplete or corrupted"
ec KRB5_KDB_RECURSIVELOCK,	"Attempt to lock database twice"
ec KRB5_KDB_NOTLOCKED,		"Attempt to unlock database when not locked"
ec KRB5_KDB_BADLOCKMODE,	"Invalid kdb lock mode"
ec KRB5_KDB_DBNOTINITED,	"Database has not been initialized"
ec KRB5_KDB_DBINITED,		"Database has already been initialized"

ec KRB5_KDB_ILLDIRECTION,	"Bad direction for converting keys"

ec KRB5_KDB_NOMASTERKEY,	"Cannot find master key record in database"
ec KRB5_KDB_BADMASTERKEY,	"Master key does not match database"
ec KRB5_KDB_INVALIDKEYSIZE,	"Key size in database is invalid"
ec KRB5_KDB_CANTREAD_STORED,	"Cannot find/read stored master key"
ec KRB5_KDB_BADSTORED_MKEY,	"Stored master key is corrupted"
ec KRB5_KDB_NOACTMASTERKEY,	"Cannot find active master key"
ec KRB5_KDB_KVNONOMATCH,	"KVNO of new master key does not match expected value"
ec KRB5_KDB_STORED_MKEY_NOTCURRENT,	"Stored master key is not current"

ec KRB5_KDB_CANTLOCK_DB,	"Insufficient access to lock database"

ec KRB5_KDB_DB_CORRUPT,		"Database format error"
ec KRB5_KDB_BAD_VERSION,	"Unsupported version in database entry"

ec KRB5_KDB_BAD_SALTTYPE,	"Unsupported salt type"
ec KRB5_KDB_BAD_ENCTYPE,	"Unsupported encryption type"
ec KRB5_KDB_BAD_CREATEFLAGS,	"Bad database creation flags"
ec KRB5_KDB_NO_PERMITTED_KEY,	"No matching key in entry having a permitted enctype"
ec KRB5_KDB_NO_MATCHING_KEY,	"No matching key in entry"
ec KRB5_KDB_DBTYPE_NOTFOUND,	"Unable to find requested database type"
ec KRB5_KDB_DBTYPE_NOSUP,	"Database type not supported"
ec KRB5_KDB_DBTYPE_INIT,	"Database library failed to initialize"
ec KRB5_KDB_SERVER_INTERNAL_ERR,   "Server error"
ec KRB5_KDB_ACCESS_ERROR,       "Unable to access Kerberos database"
ec KRB5_KDB_INTERNAL_ERROR,     "Kerberos database internal error"
ec KRB5_KDB_CONSTRAINT_VIOLATION,  "Kerberos database constraints violated"
ec KRB5_LOG_CONV,		"Update log conversion error"
ec KRB5_LOG_UNSTABLE,		"Update log is unstable"
ec KRB5_LOG_CORRUPT,		"Update log is corrupt"
ec KRB5_LOG_ERROR,		"Generic update log error"
ec KRB5_KDB_DBTYPE_MISMATCH,    "Database module does not match KDC version"
ec KRB5_KDB_POLICY_REF,		"Policy is in use"
ec KRB5_KDB_STRINGS_TOOLONG,    "Too much string mapping data"

end
