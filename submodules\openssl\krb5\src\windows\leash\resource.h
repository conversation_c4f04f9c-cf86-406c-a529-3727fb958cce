//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by Leash.rc
//
#define IDD_ABOUTBOX                    100
#define IDD_TEST2_FORM                  102
#define ID_HELLO_WORLD                  114
#define IDR_MAINFRAME                   128
#define IDR_LeashTYPE                   129
#define IDD_DIALOG1                     130
#define IDD_FORMVIEW                    130
#define IDD_LEASH_FORMVIEW              130
#define IDI_TICKET_EXPIRED              141
#define IDI_LEASH                       143
#define IDI_TICKETTYPE_GOOD             144
#define IDI_TICKET_GOOD                 145
#define IDI_TICKET_LOW                  146
#define IDI_TICKETTYPE_NOTINSTALLED     147
#define IDI_TICKETTYPE_EXPIRED          148
#define IDI_TICKETTYPE_LOW              149
#define IDD_LEASH_ABOUTBOX              153
#define IDD_DIALOG2                     154
#define IDD_MESSAGE_BOX                 154
#define IDD_LEASH_MESSAGE_BOX           154
#define IDD_DIALOG3                     155
#define IDD_DIALOG4                     156
#define IDD_DEBUG_WINDOW                157
#define IDD_LEASH_DEBUG_WINDOW          157
#define IDD_PAGE1                       160
#define IDD_PAGE2                       161
#define IDD_KRB_PROP_CONTENT            161
#define IDD_LEASH_PROPERTIES            167
#define IDD_KERB5_PAGE_PROP             168
#define IDD_KRB5_PROP_CONTENT           168
#define IDD_KRB5_PROP_LOCATION          169
#define IDD_KRB_REALMHOST_MAINT         178
#define IDC_CURSOR1                     179
#define IDD_KRB_ADD_REALM               181
#define IDD_KRB_EDIT_REALM              182
#define IDD_DIALOG5                     187
#define IDD_KRB_ADD_KDC_HOSTSERVER      197
#define IDD_KRB_EDIT_KDC_HOSTSERVER     199
#define IDD_DIALOG6                     207
#define IDD_KRB_DOMAINREALM_MAINT       207
#define IDD_DIALOG7                     210
#define IDI_ICON1                       221
#define IDD_AUTHENTICATE                229
#define IDI_LEASH_PRINCIPAL_GOOD        230
#define IDI_LEASH_PRINCIPAL_LOW         231
#define IDI_LEASH_PRINCIPAL_EXPIRED     232
#define IDI_LEASH_PRINCIPAL_NONE        233
#define IDB_BITMAP1                     239
#define IDI_TOOLBAR_INIT                240
#define IDI_TOOLBAR_RENEW               241
#define IDI_TOOLBAR_IMPORT              242
#define IDI_TOOLBAR_DESTROY             243
#define IDI_TOOLBAR_PASSWORD            244
#define IDI_TOOLBAR_REFRESH             245
#define IDI_TOOLBAR_SYNC                246
#define IDI_TOOLBAR_INIT_DISABLED       247
#define IDI_TOOLBAR_RENEW_DISABLED      248
#define IDI_TOOLBAR_IMPORT_DISABLED     249
#define IDI_TOOLBAR_DESTROY_DISABLED    250
#define IDI_TOOLBAR_PASSWORD_DISABLED   251
#define IDI_TOOLBAR_REFRESH_DISABLED    252
#define IDI_TOOLBAR_SYNC_DISABLED       253
#define MENU_TRAYICON                   254
#define ID_LEASH_RESTORE                255
#define IDD_FRAMEOWNER                  256
#define ID_LEASH_MINIMIZE               257
#define IDI_LEASH_TRAY_GOOD             258
#define IDI_LEASH_TRAY_LOW              259
#define IDI_LEASH_TRAY_EXPIRED          260
#define IDI_LEASH_TRAY_NONE             261
#define IDI_LEASH_TICKET_ADDRESS        262
#define IDI_LEASH_TICKET_SESSION        263
#define IDI_LEASH_TICKET_ENCRYPTION     264
#define IDC_PROGRESS1                   1000
#define IDC_TRACKBAR1                   1001
#define IDC_TRACKBAR2                   1002
#define IDC_BUDDY_SPIN1                 1003
#define IDC_SPIN1                       1004
#define IDC_LISTVIEW1                   1005
#define IDC_TREEVIEW1                   1006
#define IDC_TREEVIEW                    1006
#define IDC_STATIC_TRACK1               1007
#define IDC_STATIC_TRACK2               1008
#define IDC_STATIC_LISTVIEW1            1009
#define IDC_STATIC_TREEVIEW1            1010
#define IDC_LABEL_KERB_TICKETS          1011
#define IDC_LIST1                       1012
#define IDC_LEASH_MODULE_LB             1012
#define IDC_LIST_UTILITY                1012
#define IDC_LIST_KDC_REALM              1012
#define IDC_LIST_REMOVE_HOST            1012
#define IDC_LISTBOX_DLL_LOADED          1013
#define IDC_STATIC_ABOUTBOX_LEASH       1014
#define IDC_STATIC_VERSION              1015
#define IDC_STATIC_COPYRIGHT            1016
#define IDC_STATIC_MODULES_LOADED       1018
#define IDC_LEASH_WARNING_MSG           1019
#define IDC_DEBUG_LISTBOX               1022
#define IDC_COPY_TO_CLIPBOARD           1023
#define IDC_LOG_FILE_LOCATION_LABEL     1024
#define IDC_LOG_FILE_LOCATION_TEXT      1025
#define IDC_LEASH_MODULES               1029
#define IDC_ALL_MODULES                 1030
#define IDC_PROPERTIES                  1031
#define IDC_STATIC_NO_OF_MODULES        1036
#define IDC_BUTTON_LEASHINI_DETAILS     1037
#define IDC_BUTTON_LEASHINI_HELP        1037
#define IDC_STATIC_LEASHINI_LOCATION    1038
#define IDC_BUTTON_LEASHINI_HELP2       1039
#define IDC_EDIT_LEASHINI               1040
#define IDC_EDIT_KRB_LOCATION           1041
#define IDC_EDIT_KRBREALM_LOCATION      1042
#define IDC_STATIC_KRBCON_LOCATION      1043
#define IDC_STATIC_KRBREALM_LOCATION    1044
#define IDC_EDIT_KRB_KRBREALM_LOC       1045
#define IDC_EDIT_KRB_LOC                1045
#define IDC_EDIT_KRBREALM_LOC           1046
#define IDC_BUTTON_KRB_BROWSE           1047
#define IDC_BUTTON_KRBREALM_BROWSE      1048
#define IDC_STATIC_LEASH                1049
#define IDC_STATIC_KRB                  1050
#define IDC_EDITKRB5_LOCATION           1051
#define IDC_STATIC_KRB5_LOCATION        1052
#define IDC_STATIC_KRB5INI_LOCATION     1052
#define IDC_BUTTON_KRB_DETAILS          1053
#define IDC_BUTTON_KRBFILES_HELP        1053
#define IDC_BUTTON_KRBTKT_HELP          1054
#define IDC_STATIC_NDIR                 1055
#define IDC_EDIT_NDIR                   1056
#define IDC_STATIC_LEASH_PROPERTIES     1057
#define IDC_STATIC_DEFAULT_REALM        1058
#define IDC_EDIT_DEFAULT_REALM          1059
#define IDC_EDIT_REALM_HOSTNAME         1060
#define IDC_RADIO_ADMIN_SERVER          1062
#define IDC_RADIO_NO_ADMIN_SERVER       1063
#define IDC_EDIT_KRBTKFILE              1064
#define IDC_STATIC_KRBTKFILE            1065
#define IDC_NOT_LOADED_MODULES          1066
#define IDC_STATIC_TICKET_FILE          1067
#define IDC_EDIT_TICKET_FILE            1068
#define IDC_STATIC_KRB5_TICKET_FILE     1068
#define IDC_BUTTON_TICKETFILE_BROWSE    1069
#define IDC_EDIT_KRB5_TXT_FILE          1069
#define IDC_STATIC_CONFIG_FILES         1070
#define IDC_BUTTON_KRB5_TICKETFILE_BROWSE 1070
#define IDC_STATIC_TICKETFILE           1071
#define IDC_CHECK_PROXIABLE             1073
#define IDC_CHECK_FORWARDABLE           1074
#define IDC_EDIT_KRB5INI_LOCATION       1076
#define IDC_BUTTON_KRB5INI_BROWSE       1077
#define IDC_BUTTON_KRB5INI_HELP         1078
#define IDC_STATIC__KRB5_TICKETFILE     1079
#define IDC_BUTTON_KRB5TKT_HELP         1080
#define IDC_EDIT_TIME_SERVER            1081
#define IDC_STATIC_TIMESERVER           1082
#define IDC_STATIC_OPTIONS              1083
#define IDC_STATIC_TICKET_OPTIONS       1085
#define IDC_BUTTON1                     1086
#define IDC_RESET_DEFAULTS              1086
#define IDC_BUTTON_KRB_HELP             1087
#define IDC_STATIC_KRBREALM             1088
#define IDC_BUTTON_KRBREALM_HELP        1089
#define IDC_STATIC_HOST                 1092
#define IDC_STATIC_DOMAIN               1093
#define IDC_EDIT_HOSTNAME               1096
#define IDC_EDIT_DOMAINNAME             1097
#define IDC_STATIC_REALM_HOSTNAME       1098
#define ID_BUTTON_HOSTNAME_REMOVE       1100
#define IDC_STATIC_CFG_LOCATION         1103
#define IDC_BUTTON_HOSTNAME_ADD         1104
#define IDC_BUTTON_HOSTMAINT_HELP       1105
#define IDC_BUTTON_HOSTNAME_EDIT        1106
#define IDC_STATIC_HAS_ADMINSERVER_     1107
#define IDC_STATIC_NO_ADMINSERVER       1108
#define IDC_BUTTON_REALM_HOST_ADD       1110
#define ID_BUTTON_REALM_HOST_REMOVE     1111
#define ID_BUTTON_REALM_REMOVE          1111
#define IDC_BUTTON_REALM_HOST_EDIT      1112
#define IDC_BUTTON_REALM_EDIT           1112
#define IDC_BUTTON_REALMHOST_MAINT_HELP 1113
#define IDC_EDIT_DOMAINHOSTNAME         1115
#define IDC_LIST_DOMAINREALM            1116
#define IDC_EDIT_DOMAINHOST             1117
#define IDC_BUTTON_KDCHOST_ADD          1117
#define IDC_EDIT_REALMNAME              1118
#define IDC_BUTTON_KDCHOST_REMOVE       1118
#define stc32                           0x045f
#define IDC_EDIT_DOMAINREALMNAME        1119
#define IDC_BUTTON_KDCHOST_EDIT         1119
#define IDC_STATIC_TICKET_FILEPATH      1120
#define IDC_LIST_KDC_HOST               1123
#define IDC_STATIC_REALM                1124
#define IDC_BUTTON_ADMINSERVER          1125
#define IDC_BUTTON_REMOVE_ADMINSERVER   1126
#define IDC_STATIC_NOTE                 1129
#define IDC_EDIT_KDC_HOST               1130
#define IDC_EDIT_REALM                  1131
#define IDC_BUTTON_REALMHOST_MAINT_HELP2 1136
#define IDC_BUTTON_HOST_ADD             1138
#define ID_BUTTON_HOST_REMOVE           1139
#define IDC_BUTTON_HOST_EDIT            1140
#define IDC_STATIC_KRBCON               1141
#define IDC_STATIC_KRBCON_LABEL         1142
#define IDC_STATIC_KRBREALM_LABEL       1143
#define IDC_STATIC_TXT                  1145
#define IDC_STATIC_TIMEHOST             1147
#define IDC_STATIC_CONFILES             1148
#define IDC_STATIC_KRBREALMS            1149
#define IDC_STATIC_INIFILES             1150
#define IDC_CHECK_CONFIRM_KRB5_EXISTS   1151
#define IDC_STATIC_KRB_DEFAULT_LIFETIME 1154
#define IDC_STATIC_TIME_UNITS           1155
#define IDC_STATIC_KRB_DEFAULT_RENEWTILL 1155
#define IDC_EDIT_DEFAULT_LIFETIME       1156
#define IDC_ABOUT_COPYRIGHT             1158
#define IDC_ABOUT_VERSION               1159
#define IDC_CHECK_RENEWABLE             1159
#define IDC_CHECK_NO_ADDRESS            1160
#define IDC_IPADDRESS_PUBLIC            1162
#define IDC_STATIC_IPADDR               1163
#define IDC_STATIC_NAME                 1164
#define IDC_STATIC_PWD                  1165
#define IDC_EDIT1                       1166
#define IDC_EDIT_LIFE_MIN_D             1166
#define IDC_COMBO1                      1167
#define IDC_EDIT_LIFETIME_D             1167
#define IDC_EDIT2                       1168
#define IDC_EDIT_LIFE_MIN_H             1168
#define IDC_STATIC_LIFETIME             1169
#define IDC_EDIT_RENEWTILL_D            1169
#define IDC_SLIDER1                     1170
#define IDC_EDIT_LIFETIME_H             1170
#define IDC_STATIC_KRB5                 1171
#define IDC_EDIT_RENEWTILL_H            1171
#define IDC_CHECK1                      1172
#define IDC_CHECK2                      1173
#define IDC_CHECK_PRESERVE_KINIT_OPTIONS 1173
#define IDC_CHECK3                      1174
#define IDC_SLIDER2                     1175
#define IDC_STATIC_LIFETIME_VALUE       1176
#define IDC_STATIC_RENEW_TILL_VALUE     1177
#define IDC_PICTURE                     1179
#define IDC_DNS_KDC                     1180
#define IDC_CHECK_CREATE_MISSING_CFG    1182
#define IDC_GROUP_LEASH_MISC            1183
#define IDC_STATIC_LIFETIME_RANGE       1184
#define IDC_STATIC_RENEW_TILL_RANGE     1185
#define IDC_EDIT_LIFE_MIN_M             1190
#define IDC_EDIT_LIFE_MAX_D             1191
#define IDC_EDIT_LIFE_MAX_H             1192
#define IDC_EDIT_LIFE_MAX_M             1193
#define IDC_STATIC_LIFE_RANGE_MIN       1194
#define IDC_EDIT_RENEW_MIN_D            1195
#define IDC_EDIT_RENEW_MIN_H            1196
#define IDC_EDIT_RENEW_MIN_M            1197
#define IDC_EDIT_RENEW_MAX_D            1198
#define IDC_EDIT_RENEW_MAX_H            1199
#define IDC_EDIT_RENEW_MAX_M            1200
#define IDC_EDIT_LIFETIME_M             1201
#define IDC_EDIT_RENEWTILL_M            1202
#define IDC_RADIO_MSLSA_IMPORT_OFF      1203
#define IDC_RADIO_MSLSA_IMPORT_ON       1204
#define IDC_RADIO_MSLSA_IMPORT_MATCH    1205
#define IDC_STATIC_LEASH_MSLSA          1206
#define IDC_LEASH_MAINVIEW              1207
#define IDC_LIST3                       1208
#define IDD_FILESPECIAL                 1536
#define IDD_LEASH_FILESPECIAL           1536
#define IDD_KRB_PROP_MISC               1537
#define ID_RENEW_TICKET                 32776
#define ID_DESTROY_TICKET               32777
#define ID_SYN_TIME                     32778
#define ID_CHANGE_PASSWORD              32779
#define ID_UPDATE_DISPLAY               32780
#define ID_DEBUG_MODE                   32781
#define ID_CFG_FILES                    32782
#define ID_HELP_LEASH_                  32783
#define ID_HELP_KERBEROS_               32784
#define ID_KILL_TIX_ONEXIT              32785
#define ID_LARGE_ICONS                  32786
#define ID_UPPERCASE_REALM              32787
#define ID_OPTIONS_RESETWINDOWSIZE      32789
#define ID_RESET_WINDOW_SIZE            32790
#define ID_SYSTEM_CONTROL_PANEL         32792
#define ID_KRB5_PROPERTIES              32794
#define ID_LEASH_PROPERTIES             32795
#define ID_OPTIONS_LOWTICKETALARMSOUND  32796
#define ID_LOW_TICKET_ALARM             32798
#define ID_KRBCHECK                     32799
#define ID_PROPERTIES                   32801
#define ID_NEW_ITEM                     32802
#define ID_HELP_CONTENTS                32803
#define ID_HELP_FIND                    32803
#define ID_HELP_LEASH32                 32804
#define ID_HELP_WHYUSELEASH32           32805
#define ID_IMPORT_TICKET                32806
#define ID_INIT_TICKET                  32807
#define ID_AUTO_RENEW                   32808
#define ID_OBTAIN_TGT_WITH_LPARAM       32809
#define ID_TIME_ISSUED                  32810
#define ID_RENEWABLE_UNTIL              32811
#define ID_SHOW_TICKET_FLAGS            32812
#define ID_BUTTON2                      32813
#define ID_BUTTON5                      32816
#define ID_BUTTON4                      32818
#define ID_FORGET_PRINCIPALS            32818
#define ID_ENCRYPTION_TYPE              32826
#define ID_VALID_UNTIL                  32828
#define ID_MAKE_DEFAULT                 32835
#define ID_CHECK2                       32836
#define ID_IMPORT_TICKETS               32836
#define ID_EXPORT_TICKET                32837
#define ID_AUTO_IMPORT_TICKET           32838
#define ID_AUTO_IMPORT_TICKETS          32840
#define ID_CCACHE_NAME                  32841

// Next default values for new objects
//
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_3D_CONTROLS                     1
#define _APS_NEXT_RESOURCE_VALUE        269
#define _APS_NEXT_COMMAND_VALUE         32841
#define _APS_NEXT_CONTROL_VALUE         1209
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
