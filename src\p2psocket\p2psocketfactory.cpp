// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "p2psocketfactory.h"
#include "quicsocket.h"

SocketInterface* P2pSocketFactory::CreateP2pSocket(int type,
                                                   SocketOptions* option,
                                                   bool isaccept,
                                                   bool isouter) {
  switch (type) {

    case QUIC:
      return QuicSocket::P2pCreate(option, isouter);
    default:
      break;
  }
  return NULL;
}

void P2pSocketFactory::SSLInit() {

}

void P2pSocketFactory::SSLUninit() {

}
