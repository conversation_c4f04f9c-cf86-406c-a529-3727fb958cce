


          
# MsQuic 收发缓存逻辑分析

## 1. 主要文件

MsQuic 的收发缓存逻辑主要涉及以下文件：

- `src/core/send.c`：发送管理模块，处理数据和控制帧的发送队列
- `src/core/send.h`：发送管理相关的数据结构和函数声明
- `src/core/packet_build.c` packet 报文构造和发送部分。
- `src/core/stream.c`：流管理模块，包含流的发送和接收逻辑
- `src/core/stream.h`：流相关的数据结构和函数声明
- `src/core/connection.c`：连接管理模块，协调发送和接收操作
- `src/core/connection.h`：连接相关的数据结构和函数声明

## 2. 关键数据结构

### 2.1 发送相关数据结构

```c
// 发送管理结构
typedef struct QUIC_SEND {
    // 发送流列表
    CXPLAT_LIST_ENTRY SendStreams;
    
    // 连接级别的发送标志
    uint32_t SendFlags;
    
    // 最大允许发送的数据量
    uint64_t MaxData;
    
    // 其他发送控制字段
    BOOLEAN FlushOperationPending;
    BOOLEAN Uninitialized;
    BOOLEAN DelayedAckTimerActive;
    
    // 初始令牌（用于客户端）
    uint8_t* InitialToken;
    uint16_t InitialTokenLength;
    
    // 上次刷新发送的时间
    uint64_t LastFlushTime;
} QUIC_SEND;

// 发送缓冲区结构
typedef struct QUIC_SEND_BUFFER {
    // 发送缓冲区大小
    uint64_t BufferLength;
    
    // 已经确认的字节数
    uint64_t BytesAcknowledged;
    
    // 已经发送的字节数
    uint64_t BytesSent;
    
    // 已经提交的字节数
    uint64_t BytesCommitted;
    
    // 缓冲区指针
    uint8_t* Buffer;
} QUIC_SEND_BUFFER;
```

### 2.2 流发送相关数据结构

```c
// 流发送状态
typedef struct QUIC_STREAM_SEND {
    // 发送队列
    QUIC_SEND_BUFFER SendBuffer;
    
    // 发送标志
    uint32_t SendFlags;
    
    // 发送优先级
    uint16_t SendPriority;
    
    // 发送状态标志
    BOOLEAN SendDelayed;
    BOOLEAN InRecovery;
    BOOLEAN ResetAcked;
    BOOLEAN FinAcked;
    
    // 最大允许发送的数据量
    uint64_t MaxAllowedSendOffset;
    
    // 已发送的最大偏移量
    uint64_t NextSendOffset;
} QUIC_STREAM_SEND;
```

### 2.3 接收相关数据结构

```c
// 流接收状态
typedef struct QUIC_STREAM_RECV {
    // 接收队列
    CXPLAT_LIST_ENTRY RecvList;
    
    // 接收缓冲区大小
    uint64_t RecvBuffer;
    
    // 已经消费的字节数
    uint64_t RecvOffset;
    
    // 最大允许接收的数据量
    uint64_t MaxAllowedRecvOffset;
    
    // 接收状态标志
    BOOLEAN ResetReceived;
    BOOLEAN FinReceived;
} QUIC_STREAM_RECV;
```

## 3. 发送缓存逻辑

### 3.1 发送流程

```mermaid
flowchart TD
    A[应用调用MsQuicStreamSend] --> B[QuicStreamSend]
    B --> C[将数据添加到发送缓冲区]
    C --> D[设置流发送标志]
    D --> E[将流添加到连接发送列表]
    E --> F[触发连接发送操作]
    F --> G[QuicSendFlush]
    G --> H[构建数据包]
    H --> I[加密数据包]
    I --> J[发送UDP数据报]
```

### 3.2 发送状态图

```mermaid
stateDiagram-v2
    [*] --> Ready: 初始化发送缓冲区
    Ready --> Sending: 应用调用StreamSend
    Sending --> Sent: 数据包已发送
    Sent --> Acked: 收到确认
    Sent --> Lost: 数据包丢失
    Lost --> Sending: 重传数据
    Acked --> [*]: 释放缓冲区
    
    state Sending {
        [*] --> Buffering: 添加到发送缓冲区
        Buffering --> Flushing: 触发发送操作
        Flushing --> Packetizing: 构建数据包
    }
```

## 4. 接收缓存逻辑

### 4.1 接收流程

```mermaid
flowchart TD
    A[接收UDP数据报] --> B[QuicBindingReceive]
    B --> C[查找连接]
    C --> D[解密数据包]
    D --> E[处理QUIC帧]
    E --> F{是否为流帧?}
    F -->|是| G[QuicStreamRecv]
    G --> H[将数据添加到接收队列]
    H --> I[通知应用程序]
    I --> J{同步处理?}
    J -->|是| K[应用程序处理数据]
    J -->|否| L[应用程序返回PENDING]
    K --> M[QuicStreamReceiveComplete]
    L --> N[稍后调用QuicStreamReceiveComplete]
    F -->|否| O[处理其他帧类型]
```

### 4.2 接收状态图

```mermaid
stateDiagram-v2
    [*] --> Waiting: 等待数据
    Waiting --> Receiving: 收到数据包
    Receiving --> Processing: 解析流帧
    Processing --> Buffering: 添加到接收队列
    Buffering --> Notifying: 通知应用程序
    Notifying --> Consuming: 应用程序处理数据
    Consuming --> Completing: 调用ReceiveComplete
    Completing --> Waiting: 等待更多数据
    Completing --> [*]: 流关闭
```

## 5. 关键执行函数

### 5.1 发送相关函数

- `QuicSendInitialize`：初始化发送管理结构
- `QuicSendUninitialize`：清理发送管理结构
- `QuicSendQueueFlush`：将发送操作加入队列
- `QuicSendQueueFlushForStream`：为特定流触发发送操作
- `QuicSendProcessFlushSendOperation`：处理发送刷新操作
- `QuicStreamSend`：处理流数据发送请求
- `QuicStreamSendFlush`：刷新流的发送队列
- `QuicStreamAddRef`/`QuicStreamRelease`：管理流的引用计数

### 5.2 接收相关函数

- `QuicStreamRecv`：处理接收到的流数据
- `QuicStreamReceiveComplete`：完成数据接收处理
- `QuicStreamIndicateReceive`：向应用程序指示接收事件
- `QuicStreamReceiveCompletePending`：完成挂起的接收操作

## 6. 流量控制机制

### 6.1 流量控制状态图

```mermaid
stateDiagram-v2
    [*] --> Normal: 初始状态
    Normal --> Blocked: 窗口耗尽
    Blocked --> Normal: 窗口更新
    Normal --> [*]: 连接关闭
    Blocked --> [*]: 连接关闭
    
    state Normal {
        [*] --> Sending: 可以发送
        Sending --> WindowUpdate: 接收方消费数据
        WindowUpdate --> Sending: 更新窗口
    }
    
    state Blocked {
        [*] --> WaitingUpdate: 发送BLOCKED帧
        WaitingUpdate --> ReceivedUpdate: 接收MAX_DATA帧
    }
```

### 6.2 流量控制机制

MsQuic 实现了两级流量控制：

1. **连接级流量控制**：限制整个连接可以发送的数据总量
   - `Connection->Send.MaxData`：连接可发送的最大数据量
   - `Connection->Send.OrderedStreamBytesReceived`：已接收的有序字节数

2. **流级流量控制**：限制单个流可以发送的数据量
   - `Stream->MaxAllowedSendOffset`：流可发送的最大数据量
   - `Stream->MaxAllowedRecvOffset`：流可接收的最大数据量

当发送方达到流量控制限制时，会发送 `STREAM_DATA_BLOCKED` 或 `DATA_BLOCKED` 帧，接收方消费数据后会发送 `MAX_STREAM_DATA` 或 `MAX_DATA` 帧来更新窗口。

## 7. 发送缓冲区管理

### 7.1 缓冲区分配与释放

1. **分配**：当应用程序调用 `MsQuicStreamSend` 时，数据被复制到流的发送缓冲区
2. **管理**：通过 `BytesCommitted`、`BytesSent` 和 `BytesAcknowledged` 跟踪缓冲区状态
3. **释放**：当数据被确认后，相应的缓冲区部分可以被释放

### 7.2 缓冲区状态跟踪

```
+-------------------+-------------------+-------------------+
|    已确认部分     |     已发送部分    |    已提交部分     |
| BytesAcknowledged |      BytesSent    |  BytesCommitted   |
+-------------------+-------------------+-------------------+
```

## 8. 接收缓冲区管理

### 8.1 接收缓冲区分配

1. 接收缓冲区大小由应用程序通过 `QUIC_PARAM_STREAM_RECV_BUFFER_SIZE` 参数设置
2. 默认接收缓冲区大小为 `QUIC_DEFAULT_STREAM_RECV_BUFFER_SIZE`

### 8.2 接收缓冲区状态跟踪

```
+-------------------+-------------------+-------------------+
|    已消费部分     |   已接收未消费    |    未接收部分     |
|    RecvOffset     |   接收队列中      | MaxAllowedRecvOffset |
+-------------------+-------------------+-------------------+
```

## 9. 总结

MsQuic 的收发缓存逻辑是其高性能的关键组成部分。通过精心设计的数据结构和状态管理，实现了高效的数据传输和流量控制。发送缓冲区管理确保了数据的可靠传输，而接收缓冲区管理则保证了数据的有序接收和处理。流量控制机制防止了发送方压垮接收方，保证了整个系统的稳定性和可靠性。