[TOC]

# msquic 握手过程
本文主要讲解msquic 的握手交换密钥的过程， 也即首次建立connect 的过程， 属于1rtt， 0rtt 只是在1rtt 上经行了些许调整  
1rrt 握手参看下图
![](msquic/1rtt-handshake.png)  
0rrt 握手参看下图
![](msquic/0rtt-handshake.png) 

几个概念：  
‌DPLPMTUD（Datagram Packetization Layer Path MTU Discovery）‌，一种用于数据报分组层（PL）的路径MTU发现技术，旨在避免IP分片并提高网络效率。DPLPMTUD通过发送探测包来发现网络路径上能支持的最大不分片数据报大小，并处理可能的黑洞和不一致的路径信息；  
ECN，显式拥塞通知；  
Spin bits，运行网络监控者计算链接的RTT；  
Path migration, 网络路径迁移（变更）  
NAT rebing，对端地址因为NAT等中间网络设备而变更了地址   
Push promises，PUSH_PROMISE帧，用于在发送者打算初始化流之前通知对端   
Extensions，包括http3的能力，Datagrams，丢包率计算， ACK确认延迟（用于提高吞吐率）


## ‌QUIC包的格式
‌QUIC包的格式主要包括长头包和短头包两种类型‌。长头包用于在1-RTT密钥建立之前，包含更多的信息，但占用带宽较多， 因此握手过程采用长包格式；短头包则在1-RTT密钥建立之后使用，占用带宽较少‌


长头包格式
长头包的格式如下：

‌Header Form‌：包中第一个字节的最高位（Mask: 0x80），设为1表示当前包是一个长头包。
‌Fixed Bit‌：第一字节的次高位（Mask: 0x40），保留位。
‌Long Packet Type‌：两个比特（Mask: 0x30），表示当前包的类型。
‌Type-Specific Bits‌：接下来的4比特，与特定的包类型相关。
‌Version‌：共4个字节，表示QUIC版本号。
‌Destination Connection ID Length‌和‌Destination Connection ID‌：长度信息使用1个字节来编码。
‌Source Connection ID Length‌和‌Source Connection ID‌：长度信息使用1个字节来编码。
‌Type-Specific Payload‌：特定载荷数据‌
1
。
短头包格式
短头包的格式相对简单，主要包括以下字段：

‌Connection ID‌：用于标识连接。
‌Packet Number‌：用于确保包的顺序和重传处理。
‌Length‌：表示数据部分的长度。
‌Payload‌：加密的数据内容，可以包含一个或多个frame，每个frame又分为type和payload‌
2
。
  
## msquic 握手第一步，client 发送hello client

我们先看看wireshake抓包的Initial[0](hello client) 的截图，如下： 
![](msquic/helloclient.png)  

hello client 包中主要包含如下信息    
‌Header Form‌：Long Form（1）  
version:1(握手的版本号信息， 生成的读写的加密key 会与此版本号信息相关)   
Destination Connection ID Length: 8  
Destination Connection ID: 1aabd0d6ceb8dbf6（长度为8位的随机生成的字符串）  
Packet Number: 0    
Payload : af1caba222f8b072ac37d8dfbe8b15b4ba17b7100***（tslstate， 包含cryto 和padding数据）    
打包hello client 头参看函数：QuicPacketEncodeLongHeaderV1，堆栈如下：    
 ![](msquic/buildheader.png)      
 tslstate 数据由SSL_do_handshake 生成（在connect 开始的时候初始化），堆栈如下：
 ![](msquic/sslhandshake.png)        
 打包tslstate 参看函数：QuicCryptoWriteCryptoFrames, 堆栈如下：
 ![](msquic/tlsstate.png) 

## msquic server 收到hello client并处理过程
quic收到包后工作线程会把收到的包通过 QUIC_OPER_TYPE_FLUSH_RECV 操作放进队列里进行处理堆栈如下：    
 ![](msquic/recv.png)    
 底层获取到包之后， 会放到connect 的包队列里等待处理    
 ```C++
BOOLEAN QueueOperation;
CxPlatDispatchLockAcquire(&Connection->ReceiveQueueLock);
if (Connection->ReceiveQueueCount >= QueueLimit) {
    /*包队列长度超过限制， 会直接丢弃*/
    QueueOperation = FALSE;
} else {
    *Connection->ReceiveQueueTail = Packets;
    Connection->ReceiveQueueTail = PacketsTail;
    Packets = NULL;
    QueueOperation = (Connection->ReceiveQueueCount == 0);
    Connection->ReceiveQueueCount += PacketChainLength;
    Connection->ReceiveQueueByteCount += PacketChainByteLength;
}
CxPlatDispatchLockRelease(&Connection->ReceiveQueueLock);
```
 增加queue 操作， 等待工作线程处理oper队列， 至于为什么不在当前线程里直接处理，是由于quic 会根据cpu个数起2n 个cpu work 线程进行处理， 可以更好的利用cpu(特别是对server 端而言， 如果很多个connection，会分配到不同的cpu上对收到的包进行处理)
 ```C++
QUIC_OPERATION* ConnOper =
    QuicOperationAlloc(Connection->Worker, QUIC_OPER_TYPE_FLUSH_RECV);
if (ConnOper != NULL) {
    QuicConnQueueOper(Connection, ConnOper);
} else {
    QuicTraceEvent(
        AllocFailure,
        "Allocation of '%s' failed. (%llu bytes)",
        "Flush Recv operation",
        0);
}
```

## msquic server 发送server hello

我们先看看wireshake抓包的Initial[0](server hello) 的截图，如下： 
![](msquic/serverhello.png) 
payload 字段由 CxPlatTlsProcessData()处理client hello 里的消息后生成
 ```C++
    Crypto->ResultFlags =
        CxPlatTlsProcessData(
            Crypto->TLS,
            CXPLAT_TLS_CRYPTO_DATA,
            Buffer.Buffer,
            &Buffer.Length,
            &Crypto->TlsState);
 ```
Buffer.Buffer 为hello client 带过来的内容

server hello 之后是 handshake 消息， 跟随在server hello 消息之后， 
由于handshake 消息比较长， 会分成两个包进行发送     
![](msquic/handshake-server.png) 

client 端收到 server hello 和 handshake 之后 会发送ack
```C++
QuicPacketBuilderAddFrame(Builder, QUIC_FRAME_ACK, FALSE)
 ```

 堆栈如下：
 ![](msquic/clientack.png) 

