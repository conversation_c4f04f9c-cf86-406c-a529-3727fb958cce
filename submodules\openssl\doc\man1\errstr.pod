=pod

=head1 NAME

openssl-errstr,
errstr - lookup error codes

=head1 SYNOPSIS

B<openssl errstr error_code>

=head1 DESCRIPTION

Sometimes an application will not load error message and only
numerical forms will be available. The B<errstr> utility can be used to
display the meaning of the hex code. The hex code is the hex digits after the
second colon.

=head1 OPTIONS

None.

=head1 EXAMPLES

The error code:

 27594:error:2006D080:lib(32):func(109):reason(128):bss_file.c:107:

can be displayed with:

 openssl errstr 2006D080

to produce the error message:

 error:2006D080:BIO routines:BIO_new_file:no such file

=head1 COPYRIGHT

Copyright 2004-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file L<PERSON><PERSON><PERSON> in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
