// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include "common.h"
#include "lenlog.h"
#include "socket.h"
class QuicSocket : public SocketInterface {
 public:
  ~QuicSocket() override;
  static SocketInterface* P2pCreate(SocketOptions* option,
      bool isouter);
  int P2pBind(const char* ipaddr, int port) override;
  int P2pConnect(const char* ipaddr, int port) override;
  int P2pWrite(const char* buffer, int len) override;
  int P2pRead(char* buffer, int len) override;
  SocketInterface* P2pAccept(char* ipaddr, int ipaddr_len, int* port) override;
  int P2pListen() override;
  int P2pClose() override;
  int P2pWritev(struct p2p_iovec* iov, int count) override;
  int P2pReadv(struct p2p_iovec* iov, int count) override;
  int P2pShutdown() override;
  int P2pGetLocalPort() override;
  int P2pSetRecvTimeout(int timeout) override;
  int P2pSetSendTimeout(int timeout) override;
  int P2pSetConnTimeout(int timeout) override;

  int P2pSetNonBlocking() override;

  int P2pPoll(PollEvent* events, int timeout) override;
  int P2pRegisterStateChanged(std::function<void(int, int)> callback) override;
  int P2pSetSendMode(int directMode) override;
  int P2pSetReadMode(int directMode) override;

  // Stream API overrides for QUIC socket
  P2P_STREAM P2pStreamCreate(struct StreamOptions* options) override;
  int P2pStreamClose(P2P_STREAM stream) override;
  int P2pStreamWrite(P2P_STREAM stream, const char* buffer, int len) override;
  int P2pStreamWritev(P2P_STREAM stream, struct p2p_iovec* iov, int count) override;
  int P2pStreamRead(P2P_STREAM stream, char* buffer, int len) override;
  int P2pStreamPoll(P2P_STREAM stream, struct StreamEvent* events, int max_events, int timeout) override;
  int P2pStreamGetState(P2P_STREAM stream) override;
  int P2pStreamSetCallback(P2P_STREAM stream, StreamEventCallback callback, void* user_data) override;
  int P2pStreamGetId(P2P_STREAM stream) override;
  int P2pStreamGetBufferedBytes(P2P_STREAM stream) override;
  P2P_SOCKET P2pStreamGetSocket(P2P_STREAM stream) override;

 protected:
     QuicSocket() = default;

 private:
  P2P_SOCKET quic_sock_ = NULL;
};
