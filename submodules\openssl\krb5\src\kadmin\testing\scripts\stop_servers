#!/bin/sh
#
# Usage: stop_servers [hostname [path]]
#
# This script turns a host into a OpenV*Secure primary server for the
# realm SECURE-TEST.OV.COM.  If no arguments are specified,
# the local host is affected.  Otherwise, the host hostname is
# affected; the path argument is the top of the Secure install tree on
# that host, and if it is not specified the current canonical value of
# TOP is used.

DUMMY=${TESTDIR=$TOP/testing}
DUMMY=${STESTDIR=$STOP/testing}
DUMMY=${STOP_SERVERS_LOCAL=$STESTDIR/scripts/stop_servers_local}
# This'll be wrong sometimes
DUMMY=${RSH_CMD=rsh}

local=1

if [ $# -gt 0 ]; then
	if [ $# != 1 -a $# != 2 ]; then
		echo "Usage: $0 [hostname [path]]" 1>&2
		exit 1
	fi

	local=0
	hostname=$1
	if [ $# = 1 ]; then
		rempath=`sh -c "cd $TOP && pwd"`
	else
		rempath=$2
	fi
fi

if [ $local = 0 ]; then
	if $VERBOSE; then
		echo "+++ Stopping servers on remote host $hostname..."
	fi

	STOP_SERVERS_LOCAL=`echo $STOP_SERVERS_LOCAL | sed "s%$TOP%$rempath%"`
	CMD="$RSH_CMD $hostname -n \
	      \"sh -c 'VERBOSE_TEST=$VERBOSE_TEST TOP=$rempath \
		$rempath/testing/scripts/env-setup.sh \
		   $STOP_SERVERS_LOCAL $rempath'\""

	if $VERBOSE; then
		echo "+++"
		echo "+++ Begin execution of stop_servers_local on $hostname"
		echo "+++"
		echo $CMD
	fi
	eval $CMD
	if $VERBOSE; then
		echo "+++"
		echo "+++ End execution of stop_servers_local on $hostname"
		echo "+++"
	fi
else
	$STOP_SERVERS_LOCAL
fi
