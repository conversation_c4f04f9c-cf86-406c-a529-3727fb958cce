# This is the KADM5 error table.  The order of error codes
# defined in this file MUST match that specified in the API
# functionality specification, which is the master version.
#
error_table ovk
# vv 0
error_code KADM5_FAILURE, "Operation failed for unspecified reason"
error_code KADM5_AUTH_GET, "Operation requires ``get'' privilege"
error_code KADM5_AUTH_ADD, "Operation requires ``add'' privilege"
error_code KADM5_AUTH_MODIFY, "Operation requires ``modify'' privilege"
error_code KADM5_AUTH_DELETE, "Operation requires ``delete'' privilege"
error_code KADM5_AUTH_INSUFFICIENT, "Insufficient authorization for operation"
error_code KADM5_BAD_DB, "Database inconsistency detected"
error_code KADM5_DUP, "Principal or policy already exists"
error_code KADM5_RPC_ERROR, "Communication failure with server"
error_code KADM5_NO_SRV, "No administration server found for realm"
error_code KADM5_BAD_HIST_KEY, "Password history principal key version mismatch"
error_code KADM5_NOT_INIT, "Connection to server not initialized"
error_code KADM5_UNK_PRINC, "Principal does not exist"
error_code KADM5_UNK_POLICY, "Policy does not exist"
error_code KADM5_BAD_MASK, "Invalid field mask for operation"
error_code KADM5_BAD_CLASS, "Invalid number of character classes"
error_code KADM5_BAD_LENGTH, "Invalid password length"
error_code KADM5_BAD_POLICY, "Illegal policy name"
error_code KADM5_BAD_PRINCIPAL, "Illegal principal name"
error_code KADM5_BAD_AUX_ATTR, "Invalid auxiliary attributes"
error_code KADM5_BAD_HISTORY, "Invalid password history count"
error_code KADM5_BAD_MIN_PASS_LIFE, "Password minimum life is greater than password maximum life"
error_code KADM5_PASS_Q_TOOSHORT, "Password is too short"
error_code KADM5_PASS_Q_CLASS, "Password does not contain enough character classes"
error_code KADM5_PASS_Q_DICT, "Password is in the password dictionary"
error_code KADM5_PASS_REUSE, "Cannot reuse password"
error_code KADM5_PASS_TOOSOON, "Current password's minimum life has not expired"
error_code KADM5_POLICY_REF, "Policy is in use"
error_code KADM5_INIT,	"Connection to server already initialized"
error_code KADM5_BAD_PASSWORD, "Incorrect password"
error_code KADM5_PROTECT_PRINCIPAL, "Cannot change protected principal"
error_code KADM5_BAD_SERVER_HANDLE, "Programmer error!  Bad Admin server handle"
error_code KADM5_BAD_STRUCT_VERSION, "Programmer error!  Bad API structure version"
error_code KADM5_OLD_STRUCT_VERSION, "API structure version specified by application is no longer supported (to fix, recompile application against current KADM5 API header files and libraries)"
error_code KADM5_NEW_STRUCT_VERSION, "API structure version specified by application is unknown to libraries (to fix, obtain current KADM5 API header files and libraries and recompile application)"
error_code KADM5_BAD_API_VERSION, "Programmer error!  Bad API version"
error_code KADM5_OLD_LIB_API_VERSION, "API version specified by application is no longer supported by libraries (to fix, update application to adhere to current API version and recompile)"
error_code KADM5_OLD_SERVER_API_VERSION, "API version specified by application is no longer supported by server (to fix, update application to adhere to current API version and recompile)"
error_code KADM5_NEW_LIB_API_VERSION, "API version specified by application is unknown to libraries (to fix, obtain current KADM5 API header files and libraries and recompile application)"
error_code KADM5_NEW_SERVER_API_VERSION, "API version specified by application is unknown to server (to fix, obtain and install newest KADM5 Admin Server)"
error_code KADM5_SECURE_PRINC_MISSING, "Database error! Required KADM5 principal missing"
error_code KADM5_NO_RENAME_SALT, "The salt type of the specified principal does not support renaming"
error_code KADM5_BAD_CLIENT_PARAMS, "Illegal configuration parameter for remote KADM5 client"
error_code KADM5_BAD_SERVER_PARAMS, "Illegal configuration parameter for local KADM5 client"
error_code KADM5_AUTH_LIST, "Operation requires ``list'' privilege"
error_code KADM5_AUTH_CHANGEPW, "Operation requires ``change-password'' privilege"
error_code KADM5_GSS_ERROR, "GSS-API (or Kerberos) error"
error_code KADM5_BAD_TL_TYPE, "Programmer error!  Illegal tagged data list type"
error_code KADM5_MISSING_CONF_PARAMS, "Required parameters in kdc.conf missing"
error_code KADM5_BAD_SERVER_NAME, "Bad krb5 admin server hostname"
error_code KADM5_AUTH_SETKEY, "Operation requires ``set-key'' privilege"
error_code KADM5_SETKEY_DUP_ENCTYPES, "Multiple values for single or folded enctype"
error_code KADM5_SETV4KEY_INVAL_ENCTYPE, "Invalid enctype for setv4key"
error_code KADM5_SETKEY3_ETYPE_MISMATCH, "Mismatched enctypes for setkey3"
error_code KADM5_MISSING_KRB5_CONF_PARAMS, "Missing parameters in krb5.conf required for kadmin client"
error_code KADM5_XDR_FAILURE,		"XDR encoding error"
error_code KADM5_CANT_RESOLVE, "Cannot resolve network address for admin server in requested realm"
error_code KADM5_PASS_Q_GENERIC, "Unspecified password quality failure"
error_code KADM5_BAD_KEYSALTS, "Invalid key/salt tuples"
error_code KADM5_SETKEY_BAD_KVNO, "Invalid multiple or duplicate kvnos in setkey operation"
error_code KADM5_AUTH_EXTRACT, "Operation requires ``extract-keys'' privilege"
error_code KADM5_PROTECT_KEYS, "Principal keys are locked down"
error_code KADM5_AUTH_INITIAL, "Operation requires initial ticket"
end
