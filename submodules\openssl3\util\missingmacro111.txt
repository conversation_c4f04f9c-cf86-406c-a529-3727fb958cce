# A list of macros that are known to be missing documentation as used by the
# find-doc-nits -v -o option. The list is as of commit 1708e3e85b (the release
# of 1.1.1).
BIO_get_flags(3)
BIO_set_retry_special(3)
BIO_set_retry_read(3)
BIO_set_retry_write(3)
BIO_clear_retry_flags(3)
BIO_get_retry_flags(3)
BIO_CB_return(3)
BIO_cb_pre(3)
BIO_cb_post(3)
BIO_set_app_data(3)
BIO_get_app_data(3)
BIO_set_conn_mode(3)
BIO_dup_state(3)
BIO_buffer_get_num_lines(3)
BIO_buffer_peek(3)
BIO_ctrl_dgram_connect(3)
BIO_ctrl_set_connected(3)
BIO_dgram_recv_timedout(3)
BIO_dgram_send_timedout(3)
BIO_dgram_get_peer(3)
BIO_dgram_set_peer(3)
BIO_dgram_get_mtu_overhead(3)
BIO_sock_cleanup(3)
ossl_bio__attr__(3)
BN_prime_checks_for_size(3)
BN_GF2m_sub(3)
BN_GF2m_cmp(3)
BUF_strdup(3)
BUF_strndup(3)
BUF_memdup(3)
BUF_strlcpy(3)
BUF_strlcat(3)
BUF_strnlen(3)
COMP_zlib_cleanup(3)
NCONF_get_number(3)
OPENSSL_MALLOC_MAX_NELEMS(3)
CRYPTO_cleanup_all_ex_data(3)
CRYPTO_num_locks(3)
CRYPTO_set_locking_callback(3)
CRYPTO_get_locking_callback(3)
CRYPTO_set_add_lock_callback(3)
CRYPTO_get_add_lock_callback(3)
CRYPTO_THREADID_set_numeric(3)
CRYPTO_THREADID_set_pointer(3)
CRYPTO_THREADID_set_callback(3)
CRYPTO_THREADID_get_callback(3)
CRYPTO_THREADID_current(3)
CRYPTO_THREADID_cmp(3)
CRYPTO_THREADID_cpy(3)
CRYPTO_THREADID_hash(3)
CRYPTO_set_id_callback(3)
CRYPTO_get_id_callback(3)
CRYPTO_thread_id(3)
CRYPTO_set_dynlock_create_callback(3)
CRYPTO_set_dynlock_lock_callback(3)
CRYPTO_set_dynlock_destroy_callback(3)
CRYPTO_get_dynlock_create_callback(3)
CRYPTO_get_dynlock_lock_callback(3)
CRYPTO_get_dynlock_destroy_callback(3)
OpenSSLDie(3)
OPENSSL_assert(3)
EVP_PKEY_CTX_set_dh_paramgen_subprime_len(3)
EVP_PKEY_CTX_set_dh_paramgen_type(3)
EVP_PKEY_CTX_set_dh_rfc5114(3)
EVP_PKEY_CTX_set_dhx_rfc5114(3)
EVP_PKEY_CTX_set_dh_kdf_type(3)
EVP_PKEY_CTX_get_dh_kdf_type(3)
EVP_PKEY_CTX_set0_dh_kdf_oid(3)
EVP_PKEY_CTX_get0_dh_kdf_oid(3)
EVP_PKEY_CTX_set_dh_kdf_md(3)
EVP_PKEY_CTX_get_dh_kdf_md(3)
EVP_PKEY_CTX_set_dh_kdf_outlen(3)
EVP_PKEY_CTX_get_dh_kdf_outlen(3)
EVP_PKEY_CTX_set0_dh_kdf_ukm(3)
EVP_PKEY_CTX_get0_dh_kdf_ukm(3)
DSA_is_prime(3)
OPENSSL_GLOBAL_REF(3)
OPENSSL_GLOBAL_REF(3)
ECParameters_dup(3)
EVP_PKEY_CTX_set_ecdh_cofactor_mode(3)
EVP_PKEY_CTX_get_ecdh_cofactor_mode(3)
EVP_PKEY_CTX_set_ecdh_kdf_type(3)
EVP_PKEY_CTX_get_ecdh_kdf_type(3)
EVP_PKEY_CTX_set_ecdh_kdf_md(3)
EVP_PKEY_CTX_get_ecdh_kdf_md(3)
EVP_PKEY_CTX_set_ecdh_kdf_outlen(3)
EVP_PKEY_CTX_get_ecdh_kdf_outlen(3)
EVP_PKEY_CTX_set0_ecdh_kdf_ukm(3)
EVP_PKEY_CTX_get0_ecdh_kdf_ukm(3)
ENGINE_load_openssl(3)
ENGINE_load_dynamic(3)
ENGINE_load_padlock(3)
ENGINE_load_capi(3)
ENGINE_load_afalg(3)
ENGINE_load_cryptodev(3)
ENGINE_load_rdrand(3)
EVP_PKEY_assign_SIPHASH(3)
EVP_PKEY_assign_POLY1305(3)
EVP_MD_nid(3)
EVP_MD_name(3)
EVP_CIPHER_name(3)
EVP_ENCODE_LENGTH(3)
EVP_DECODE_LENGTH(3)
BIO_set_md_ctx(3)
EVP_add_cipher_alias(3)
EVP_add_digest_alias(3)
EVP_delete_cipher_alias(3)
EVP_delete_digest_alias(3)
EVP_MD_CTX_create(3)
EVP_MD_CTX_init(3)
EVP_MD_CTX_destroy(3)
EVP_CIPHER_CTX_init(3)
EVP_CIPHER_CTX_cleanup(3)
OPENSSL_add_all_algorithms_conf(3)
OPENSSL_add_all_algorithms_noconf(3)
LHASH_HASH_FN(3)
LHASH_COMP_FN(3)
LHASH_DOALL_ARG_FN(3)
LHASH_OF(3)
DEFINE_LHASH_OF(3)
int_implement_lhash_doall(3)
OBJ_create_and_add_object(3)
OBJ_bsearch(3)
OBJ_bsearch_ex(3)
PEM_read_bio_OCSP_REQUEST(3)
PEM_read_bio_OCSP_RESPONSE(3)
PEM_write_bio_OCSP_REQUEST(3)
PEM_write_bio_OCSP_RESPONSE(3)
ASN1_BIT_STRING_digest(3)
OCSP_CERTSTATUS_dup(3)
PKCS7_get_signed_attributes(3)
PKCS7_get_attributes(3)
PKCS7_type_is_signed(3)
PKCS7_type_is_encrypted(3)
PKCS7_type_is_enveloped(3)
PKCS7_type_is_signedAndEnveloped(3)
PKCS7_type_is_data(3)
PKCS7_type_is_digest(3)
PKCS7_set_detached(3)
PKCS7_get_detached(3)
PKCS7_is_detached(3)
EVP_PKEY_CTX_get_rsa_padding(3)
EVP_PKEY_CTX_get_rsa_pss_saltlen(3)
EVP_PKEY_CTX_set_rsa_keygen_primes(3)
EVP_PKEY_CTX_set_rsa_mgf1_md(3)
EVP_PKEY_CTX_set_rsa_oaep_md(3)
EVP_PKEY_CTX_get_rsa_mgf1_md(3)
EVP_PKEY_CTX_get_rsa_oaep_md(3)
EVP_PKEY_CTX_set0_rsa_oaep_label(3)
EVP_PKEY_CTX_get0_rsa_oaep_label(3)
RSA_set_app_data(3)
RSA_get_app_data(3)
STACK_OF(3)
SKM_DEFINE_STACK_OF(3)
U64(3)
U64(3)
U64(3)
SSL_set_mtu(3)
DTLS_set_link_mtu(3)
DTLS_get_link_min_mtu(3)
SSL_heartbeat(3)
SSL_CTX_set_cert_flags(3)
SSL_set_cert_flags(3)
SSL_CTX_clear_cert_flags(3)
SSL_clear_cert_flags(3)
SSL_set_app_data(3)
SSL_get_app_data(3)
SSL_SESSION_set_app_data(3)
SSL_SESSION_get_app_data(3)
SSL_CTX_get_app_data(3)
SSL_CTX_set_app_data(3)
SSLeay_add_ssl_algorithms(3)
DTLSv1_get_timeout(3)
DTLSv1_handle_timeout(3)
SSL_num_renegotiations(3)
SSL_clear_num_renegotiations(3)
SSL_total_renegotiations(3)
SSL_CTX_set_tmp_ecdh(3)
SSL_set_tmp_ecdh(3)
SSL_CTX_get_extra_chain_certs(3)
SSL_CTX_get_extra_chain_certs_only(3)
SSL_get0_certificate_types(3)
SSL_CTX_set1_client_certificate_types(3)
SSL_set1_client_certificate_types(3)
SSL_get0_raw_cipherlist(3)
SSL_get0_ec_point_formats(3)
SSL_CTX_need_tmp_RSA(3)
SSL_CTX_set_tmp_rsa(3)
SSL_need_tmp_RSA(3)
SSL_set_tmp_rsa(3)
SSL_CTX_set_ecdh_auto(3)
SSL_set_ecdh_auto(3)
SSL_CTX_set_tmp_rsa_callback(3)
SSL_set_tmp_rsa_callback(3)
SSL_get_ex_new_index(3)
SSL_SESSION_get_ex_new_index(3)
SSL_CTX_get_ex_new_index(3)
SSL_CTX_set_default_read_ahead(3)
SSL_cache_hit(3)
TLS1_get_version(3)
TLS1_get_client_version(3)
SSL_set_tlsext_debug_callback(3)
SSL_set_tlsext_debug_arg(3)
SSL_get_tlsext_status_exts(3)
SSL_set_tlsext_status_exts(3)
SSL_get_tlsext_status_ids(3)
SSL_set_tlsext_status_ids(3)
SSL_CTX_get_tlsext_ticket_keys(3)
SSL_CTX_set_tlsext_ticket_keys(3)
SSL_get_dtlsext_heartbeat_pending(3)
SSL_set_dtlsext_heartbeat_no_requests(3)
SSL_get_tlsext_heartbeat_pending(3)
SSL_set_tlsext_heartbeat_no_requests(3)
UI_set_app_data(3)
UI_get_app_data(3)
X509_extract_key(3)
X509_REQ_extract_key(3)
X509_name_cmp(3)
X509_STORE_CTX_set_app_data(3)
X509_STORE_CTX_get_app_data(3)
X509_LOOKUP_load_file(3)
X509_LOOKUP_add_dir(3)
X509V3_conf_err(3)
X509V3_set_ctx_test(3)
X509V3_set_ctx_nodb(3)
EXT_BITSTRING(3)
EXT_IA5STRING(3)
