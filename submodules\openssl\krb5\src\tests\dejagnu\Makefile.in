mydir=tests$(S)dejagnu
BUILDTOP=$(REL)..$(S)..
RUNTEST = @RUNTEST@ $(DEJAFLAGS)
RUNTESTFLAGS =

SRCS=$(srcdir)/t_inetd.c

all: t_inetd

install:

check: check-runtest-@HAVE_RUNTEST@

check-runtest-no:
	@echo "+++"
	@echo "+++ WARNING: tests/dejagnu tests not run."
	@echo "+++ runtest is unavailable."
	@echo "+++"
	@echo 'Skipped dejagnu tests: runtest not found' >> $(SKIPTESTS)

# Set VALGRIND at run time, that may be changed when running 'make'.
check-runtest-yes: t_inetd site.exp
	$(RUNTEST) --tool krb VALGRIND="$(VALGRIND)" $(RUNTESTFLAGS)

t_inetd: t_inetd.o $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o t_inetd t_inetd.o $(KRB5_BASE_LIBS)

clean:
	$(RM) t_inetd t_inetd.o site.exp runenv.vars runenv.vals

clean-unix::
	$(RM) -rf tmpdir dbg.log krb.log krb.sum

runenv.vals: Makefile
	$(RUN_SETUP); for i in $(RUN_VARS); do \
		eval echo "{$$i=\$$$$i}"; done > runenv.vals

site.exp: runenv.vals Makefile
	echo "set runvarlist [list `cat runenv.vals | tr '\n' ' '`]" | \
		sed -e 's%=\.%='`pwd`'/.%g' > site.exp
	echo "set PRIOCNTL_HACK @PRIOCNTL_HACK@" >> site.exp

