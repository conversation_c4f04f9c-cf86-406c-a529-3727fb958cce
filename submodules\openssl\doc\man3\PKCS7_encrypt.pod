=pod

=head1 NAME

PKCS7_encrypt - create a PKCS#7 envelopedData structure

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 PKCS7 *PKCS7_encrypt(STACK_OF(X509) *certs, BIO *in, const EVP_CIPHER *cipher,
                      int flags);

=head1 DESCRIPTION

PKCS7_encrypt() creates and returns a PKCS#7 envelopedData structure. B<certs>
is a list of recipient certificates. B<in> is the content to be encrypted.
B<cipher> is the symmetric cipher to use. B<flags> is an optional set of flags.

=head1 NOTES

Only RSA keys are supported in PKCS#7 and envelopedData so the recipient
certificates supplied to this function must all contain RSA public keys, though
they do not have to be signed using the RSA algorithm.

EVP_des_ede3_cbc() (triple DES) is the algorithm of choice for S/MIME use
because most clients will support it.

Some old "export grade" clients may only support weak encryption using 40 or 64
bit RC2. These can be used by passing EVP_rc2_40_cbc() and EVP_rc2_64_cbc()
respectively.

The algorithm passed in the B<cipher> parameter must support ASN1 encoding of
its parameters.

Many browsers implement a "sign and encrypt" option which is simply an S/MIME
envelopedData containing an S/MIME signed message. This can be readily produced
by storing the S/MIME signed message in a memory BIO and passing it to
PKCS7_encrypt().

The following flags can be passed in the B<flags> parameter.

If the B<PKCS7_TEXT> flag is set MIME headers for type B<text/plain> are
prepended to the data.

Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if B<PKCS7_BINARY> is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If B<PKCS7_BINARY> is set then
B<PKCS7_TEXT> is ignored.

If the B<PKCS7_STREAM> flag is set a partial B<PKCS7> structure is output
suitable for streaming I/O: no data is read from the BIO B<in>.

=head1 NOTES

If the flag B<PKCS7_STREAM> is set the returned B<PKCS7> structure is B<not>
complete and outputting its contents via a function that does not
properly finalize the B<PKCS7> structure will give unpredictable
results.

Several functions including SMIME_write_PKCS7(), i2d_PKCS7_bio_stream(),
PEM_write_bio_PKCS7_stream() finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 B<BIO> directly using
BIO_new_PKCS7().

=head1 RETURN VALUES

PKCS7_encrypt() returns either a PKCS7 structure or NULL if an error occurred.
The error can be obtained from ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>, L<PKCS7_decrypt(3)>

=head1 HISTORY

The B<PKCS7_STREAM> flag was added in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
