.\" Copyright (c) 1990, 1993, 1994, 1995
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in the
.\"    documentation and/or other materials provided with the distribution.
.\" 3. All advertising materials mentioning features or use of this software
.\"    must display the following acknowledgement:
.\"	This product includes software developed by the University of
.\"	California, Berkeley and its contributors.
.\" 4. Neither the name of the University nor the names of its contributors
.\"    may be used to endorse or promote products derived from this software
.\"    without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
.\" ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
.\" IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
.\" ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
.\" FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
.\" DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
.\" OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
.\" HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
.\" LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
.\" OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"	@(#)db_recno.3	8.12 (Berkeley) 8/1/95
.\"
.TH DB_RECNO 3 "August 1, 1995"
.UC 7
.SH NAME
db_recno \- record number database access method
.SH DESCRIPTION
.so db.so
specific details of the recno access method.
.SH "ACCESS METHOD SPECIFIC INFORMATION"
The recno access method specific data structure provided to
.I db_open
is typedef'd and named RECNOINFO.
A RECNOINFO structure has at least the following fields,
which may be initialized before calling
.IR db_open :
.TP 5
u_int8_t bval;
The delimiting byte to be used to mark the end of a record for
variable-length records, and the pad character for fixed-length
records.
If no value is specified, newlines (``\en'') are used to mark the end
of variable-length records and fixed-length records are padded with
spaces.
.TP 5
char *bfname;
The recno access method stores the in-memory copies of its records
in a btree.
If bfname is non-NULL, it specifies the name of the btree file,
as if specified as the file name for a
.I db_open
of a btree file.
.TP 5
u_int cachesize;
A suggested maximum size, in bytes, of the memory cache.
This value is
.B only
advisory, and the access method will allocate more memory rather than fail.
If
.I cachesize
is  0 (no size is specified) a default size is used.
.TP 5
u_long flags;
The flag value is specified by
.IR or 'ing
any of the following values:
.RS
.TP 5
R_FIXEDLEN
The records are fixed-length, not byte delimited.
The structure element
.I reclen
specifies the length of the record, and the structure element
.I bval
is used as the pad character.
Any records, inserted into the database, that are less than
.I reclen
bytes long are automatically padded.
.TP 5
R_NOKEY
In the interface specified by
.IR db_open ,
the sequential record retrieval fills in both the caller's key and
data structures.
If the R_NOKEY flag is specified, the
.I cursor
functions are not required to fill in the key structure.
This permits applications to retrieve records at the end of files without
reading all of the intervening records.
.TP 5
R_SNAPSHOT
This flag requires that a snapshot of the file be taken when
.I db_open
is called, instead of permitting any unmodified records to be read from
the original file.
.RE
.TP 5
int lorder;
The byte order for integers in the stored database metadata.
The number should represent the order as an integer; for example,
big endian order would be the number 4,321.
If
.I lorder
is 0 (no order is specified) the current host order is used.
.TP 5
u_int psize;
The recno access method stores the in-memory copies of its records
in a btree.
This value is the size (in bytes) of the pages used for nodes in that tree.
If
.I psize
is 0 (no page size is specified) a page size is chosen based on the
underlying file system I/O block size.
See
.IR btree (3)
for more information.
.TP 5
size_t reclen;
The length of a fixed-length record.
.SH "DB OPERATIONS"
The data part of the key/data pair used by the recno access method
is the same as other access methods.
The key is different.
The
.I data
field of the key should be a pointer to a memory location of type
.IR recno_t ,
as defined in the <db.h> include file.
This type is normally the largest unsigned integral type available to
the implementation.
The
.I size
field of the key should be the size of that type.
.PP
The record number data structure is either variable or fixed-length
records stored in a flat-file format, accessed by the logical record
number.
The existence of record number five requires the existence of records
one through four, and the deletion of record number one causes
record number five to be renumbered to record number four, as well
as the cursor, if positioned after record number one, to shift down
one record.
The creation of record number five when records one through four do
not exist causes the logical creation of them with zero-length data.
.PP
Because there is no meta-data associated with the underlying recno access
method files, any changes made to the default values (e.g. fixed record
length or byte separator value) must be explicitly specified each time the
file is opened.
.PP
The functions returned by
.I db_open
for the btree access method are as described in
.IR db_open (3),
with the following exceptions and additions:
.TP 5
type
The type is DB_RECNO.
.TP 5
put
Using the
.I put
interface to create a new record will cause the creation of multiple,
empty records if the record number is more than one greater than the
largest record currently in the database.
.IP
The
.I put
function takes the following additional flags:
.RS
.TP 5
R_IAFTER
Append the data immediately after the data referenced by
.IR key ,
creating a new key/data pair.
The record number of the appended key/data pair is returned in the
.I key
structure.
.TP 5
R_IBEFORE
Insert the data immediately before the data referenced by
.IR key ,
creating a new key/data pair.
The record number of the inserted key/data pair is returned in the
.I key
structure.
.TP 5
R_SETCURSOR
Store the key/data pair, setting or initializing the position of the
cursor to reference it.
.RE
.TP 5
seq
The
.I seq
function takes the following additional flags:
.RS
.TP 5
R_LAST
The last key/data pair of the database is returned, and the cursor
is set or initialized to reference it.
.TP 5
R_PREV
Retrieve the key/data pair immediately before the cursor.
If the cursor is not yet set, this is the same as the R_LAST flag.
.RE
.IP
If the database file is a character special file and no complete
key/data pairs are currently available, the
.I seq
function returns 2.
.TP 5
sync
The
.I sync
function takes the following additional flag:
.RS
.TP 5
R_RECNOSYNC
This flag causes the
.I sync
function to apply to the btree file which underlies the recno file,
not the recno file itself.
(See the
.I bfname
field of RECNOINFO
structure, above, for more information.)
.RE
.SH ERRORS
The
.I recno
access method functions may fail and set
.I errno
for any of the errors specified for the library function
.IR db_open (3)
or the following:
.TP 5
[EINVAL]
An attempt was made to add a record to a fixed-length database that
was too large to fit.
.SH "SEE ALSO"
.IR db_btree (3),
.IR db_hash (3),
.IR db_lock (3),
.IR db_log (3),
.IR db_mpool (3),
.IR db_open (3),
.IR db_txn (3)
.sp
.IR "Document Processing in a Relational Database System" ,
Michael Stonebraker, Heidi Stettner, Joseph Kalash, Antonin Guttman,
Nadene Lynn, Memorandum No. UCB/ERL M82/32, May 1982.
.SH BUGS
The
.I sync
function's R_RECNOSYNC interface is a kluge,
and will be deleted in a future version of the interface.
