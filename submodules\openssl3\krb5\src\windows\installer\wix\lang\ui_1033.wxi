﻿<?xml version="1.0" encoding="utf-8"?>
<!--

  Copyright (C) 2004,2005, 2006 by the Massachusetts Institute of Technology.
  All rights reserved.
 
  Export of this software from the United States of America may
    require a specific license from the United States Government.
    It is the responsibility of any person or organization contemplating
    export to obtain such a license before exporting.
 
  WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
  distribute this software and its documentation for any purpose and
  without fee is hereby granted, provided that the above copyright
  notice appear in all copies and that both that copyright notice and
  this permission notice appear in supporting documentation, and that
  the name of M.I.T. not be used in advertising or publicity pertaining
  to distribution of the software without specific, written prior
  permission.  Furthermore if you modify this software you must label
  your software as modified software and not distribute it in such a
  fashion that it might be confused with the original M.I.T. software.
  M.I.T. makes no representations about the suitability of
  this software for any purpose.  It is provided "as is" without express
  or implied warranty.
  
  -->
<Include xmlns="http://schemas.microsoft.com/wix/2003/01/wi">
    <UI>
      <Dialog Id="AdminWelcomeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Property="InstallMode" Value="Server Image">1</Publish>
          <Publish Event="NewDialog" Value="AdminInstallPointDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="70" Width="220" Height="30" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] will create a server image of [ProductName], at a specified network location.  Click Next to continue or Cancel to exit the [Wizard].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="60" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Welcome to the [ProductName] [Wizard]</Text>
        </Control>
      </Dialog>
      <Dialog Id="ExitDialog" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Finish" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Finish]">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Cancel]" />
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="110" Width="220" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Click the Finish button to exit the [Wizard].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="100" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Completing the [ProductName] [Wizard]</Text>
        </Control>
      </Dialog>
      <Dialog Id="FatalError" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Finish" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Finish]">
          <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Cancel]" />
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="100" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}[ProductName] [Wizard] ended prematurely</Text>
        </Control>
        <Control Id="Description1" Type="Text" X="135" Y="110" Width="220" Height="40" Transparent="yes" NoPrefix="yes">
          <Text>[ProductName] setup ended prematurely because of an error.  Your system has not been modified.  To install this program at a later time, please run the installation again.</Text>
        </Control>
        <Control Id="Description2" Type="Text" X="135" Y="155" Width="220" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Click the Finish button to exit the [Wizard].</Text>
        </Control>
      </Dialog>
      <Dialog Id="PrepareDlg" Width="370" Height="270" Title="[ProductName] [Setup]" Modeless="yes">
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="110" Width="220" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Please wait while the [Wizard] prepares to guide you through the installation.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="100" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Welcome to the [ProductName] [Wizard]</Text>
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" TabSkip="yes" Text="[ButtonText_Back]" />
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Disabled="yes" TabSkip="yes" Text="[ButtonText_Next]" />
        <Control Id="ActionData" Type="Text" X="135" Y="125" Width="220" Height="30" Transparent="yes" NoPrefix="yes">
          <Subscribe Event="ActionData" Attribute="Text" />
        </Control>
        <Control Id="ActionText" Type="Text" X="135" Y="100" Width="220" Height="20" Transparent="yes" NoPrefix="yes">
          <Subscribe Event="ActionText" Attribute="Text" />
        </Control>
      </Dialog>
      <Dialog Id="ProgressDlg" Width="370" Height="270" Title="[ProductName] [Setup]" Modeless="yes">
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Next]" />
        <Control Id="Text" Type="Text" X="35" Y="65" Width="300" Height="20">
          <Text>Please wait while the [Wizard] [Progress2] [ProductName].  This may take several minutes.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Title" Type="Text" X="20" Y="15" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont][Progress1] [ProductName]</Text>
        </Control>
        <Control Id="ActionText" Type="Text" X="70" Y="100" Width="265" Height="10">
          <Subscribe Event="ActionText" Attribute="Text" />
        </Control>
        <Control Id="ProgressBar" Type="ProgressBar" X="35" Y="115" Width="300" Height="10" ProgressBlocks="yes" Text="Progress done">
          <Subscribe Event="SetProgress" Attribute="Progress" />
        </Control>
        <Control Id="StatusLabel" Type="Text" X="35" Y="100" Width="35" Height="10" Text="Status:" />
      </Dialog>
      <Dialog Id="UserExit" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Finish" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Finish]">
          <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Cancel]" />
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="100" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}[ProductName] [Wizard] was interrupted</Text>
        </Control>
        <Control Id="Description1" Type="Text" X="135" Y="110" Width="220" Height="40" Transparent="yes" NoPrefix="yes">
          <Text>[ProductName] setup was interrupted.  Your system has not been modified.  To install this program at a later time, please run the installation again.</Text>
        </Control>
        <Control Id="Description2" Type="Text" X="135" Y="155" Width="220" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Click the Finish button to exit the [Wizard].</Text>
        </Control>
      </Dialog>
      <Dialog Id="AdminBrowseDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="PathEdit" Type="PathEdit" X="84" Y="202" Width="261" Height="17" Property="TARGETDIR" />
        <Control Id="OK" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_OK]">
          <Publish Event="SetTargetPath" Value="TARGETDIR">1</Publish>
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="240" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="Reset" Value="0">1</Publish>
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="ComboLabel" Type="Text" X="25" Y="58" Width="44" Height="10" TabSkip="no" Text="&amp;Look in:" />
        <Control Id="DirectoryCombo" Type="DirectoryCombo" X="70" Y="55" Width="220" Height="80" Property="TARGETDIR" Removable="yes" Fixed="yes" Remote="yes">
          <Subscribe Event="IgnoreChange" Attribute="IgnoreChange" />
        </Control>
        <Control Id="Up" Type="PushButton" X="298" Y="55" Width="19" Height="19" ToolTip="Up One Level" Icon="yes" FixedSize="yes" IconSize="16" Text="Up">
          <Publish Event="DirectoryListUp" Value="0">1</Publish>
        </Control>
        <Control Id="NewFolder" Type="PushButton" X="325" Y="55" Width="19" Height="19" ToolTip="Create A New Folder" Icon="yes" FixedSize="yes" IconSize="16" Text="New">
          <Publish Event="DirectoryListNew" Value="0">1</Publish>
        </Control>
        <Control Id="DirectoryList" Type="DirectoryList" X="25" Y="83" Width="320" Height="110" Property="TARGETDIR" Sunken="yes" TabSkip="no" />
        <Control Id="PathLabel" Type="Text" X="25" Y="205" Width="59" Height="10" TabSkip="no" Text="&amp;Folder name:" />
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Browse to the destination folder</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Change current destination folder</Text>
        </Control>
      </Dialog>
      <Dialog Id="AdminInstallPointDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Text" Type="Text" X="25" Y="80" Width="320" Height="10" TabSkip="no">
          <Text>&amp;Enter a new network location or click Browse to browse to one.</Text>
        </Control>
        <Control Id="PathEdit" Type="PathEdit" X="25" Y="93" Width="320" Height="18" Property="TARGETDIR" />
        <Control Id="Browse" Type="PushButton" X="289" Y="119" Width="56" Height="17" Text="[ButtonText_Browse]">
          <Publish Event="SpawnDialog" Value="AdminBrowseDlg">1</Publish>
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="AdminWelcomeDlg">1</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="SetTargetPath" Value="TARGETDIR">1</Publish>
          <Publish Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="20" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Please specify a network location for the server image of [ProductName] product</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Network Location</Text>
        </Control>
      </Dialog>
      <Dialog Id="BrowseDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="PathEdit" Type="PathEdit" X="84" Y="202" Width="261" Height="18" Property="_BrowseProperty" Indirect="yes" />
        <Control Id="OK" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_OK]">
          <Publish Event="SetTargetPath" Value="[_BrowseProperty]">1</Publish>
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="240" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="Reset" Value="0">1</Publish>
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="ComboLabel" Type="Text" X="25" Y="58" Width="44" Height="10" TabSkip="no" Text="&amp;Look in:" />
        <Control Id="DirectoryCombo" Type="DirectoryCombo" X="70" Y="55" Width="220" Height="80" Property="_BrowseProperty" Indirect="yes" Fixed="yes" Remote="yes">
          <Subscribe Event="IgnoreChange" Attribute="IgnoreChange" />
        </Control>
        <Control Id="Up" Type="PushButton" X="298" Y="55" Width="19" Height="19" ToolTip="Up One Level" Icon="yes" FixedSize="yes" IconSize="16" Text="Up">
          <Publish Event="DirectoryListUp" Value="0">1</Publish>
        </Control>
        <Control Id="NewFolder" Type="PushButton" X="325" Y="55" Width="19" Height="19" ToolTip="Create A New Folder" Icon="yes" FixedSize="yes" IconSize="16" Text="New">
          <Publish Event="DirectoryListNew" Value="0">1</Publish>
        </Control>
        <Control Id="DirectoryList" Type="DirectoryList" X="25" Y="83" Width="320" Height="110" Property="_BrowseProperty" Sunken="yes" Indirect="yes" TabSkip="no" />
        <Control Id="PathLabel" Type="Text" X="25" Y="205" Width="59" Height="10" TabSkip="no" Text="&amp;Folder name:" />
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Browse to the destination folder</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Change current destination folder</Text>
        </Control>
      </Dialog>
      <Dialog Id="CancelDlg" Y="10" Width="260" Height="85" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="No" Type="PushButton" X="132" Y="57" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_No]">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Yes" Type="PushButton" X="72" Y="57" Width="56" Height="17" Text="[ButtonText_Yes]">
          <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="Text" Type="Text" X="48" Y="15" Width="194" Height="30">
          <Text>Are you sure you want to cancel [ProductName] installation?</Text>
        </Control>
        <Control Id="Icon" Type="Icon" X="15" Y="15" Width="24" Height="24" ToolTip="Information icon" FixedSize="yes" IconSize="32" Text="[InfoIcon]" />
      </Dialog>
      <Dialog Id="CustomizeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" TrackDiskSpace="yes">
        <Control Id="Tree" Type="SelectionTree" X="25" Y="85" Width="175" Height="95" Property="_BrowseProperty" Sunken="yes" TabSkip="no" Text="Tree of selections" />
        <Control Id="Browse" Type="PushButton" X="304" Y="200" Width="56" Height="17" Text="[ButtonText_Browse]">
          <Publish Event="SelectionBrowse" Value="BrowseDlg">1</Publish>
          <Condition Action="hide">Installed</Condition>
        </Control>
        <Control Id="Reset" Type="PushButton" X="42" Y="243" Width="56" Height="17" Text="[ButtonText_Reset]">
          <Publish Event="Reset" Value="0">1</Publish>
          <Subscribe Event="SelectionNoItems" Attribute="Enabled" />
        </Control>
        <Control Id="DiskCost" Type="PushButton" X="111" Y="243" Width="56" Height="17">
          <Text>Disk &amp;Usage</Text>
          <Publish Event="SpawnDialog" Value="DiskCostDlg">1</Publish>
          <Subscribe Event="SelectionNoItems" Attribute="Enabled" />
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">InstallMode = "Change"</Publish>
          <Publish Event="NewDialog" Value="SetupTypeDlg">InstallMode = "Custom"</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="NewDialog" Value="VerifyReadyDlg">( &amp;feaKfwClient &lt;&gt; 3 And !feaKfwClient &lt;&gt; 3 ) And Not FoundProcesses</Publish>
          <Publish Event="NewDialog" Value="RunningProcessDlg">( &amp;feaKfwClient &lt;&gt; 3 And !feaKfwClient &lt;&gt; 3 ) And FoundProcesses</Publish>
          <Publish Event="NewDialog" Value="KerberosOptions">&amp;feaKfwClient = 3 Or !feaKfwClient = 3</Publish>
          <Subscribe Event="SelectionNoItems" Attribute="Enabled" />
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="25" Y="55" Width="320" Height="20">
          <Text>Click on the icons in the tree below to change the way features will be installed.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Select the way you want features to be installed.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Custom Setup</Text>
        </Control>
        <Control Id="Box" Type="GroupBox" X="210" Y="81" Width="140" Height="98" />
        <Control Id="ItemDescription" Type="Text" X="215" Y="90" Width="131" Height="30">
          <Text>Multiline description of the currently selected item.</Text>
          <Subscribe Event="SelectionDescription" Attribute="Text" />
        </Control>
        <Control Id="ItemSize" Type="Text" X="215" Y="130" Width="131" Height="45">
          <Text>The size of the currently selected item.</Text>
          <Subscribe Event="SelectionSize" Attribute="Text" />
        </Control>
        <Control Id="Location" Type="Text" X="75" Y="200" Width="215" Height="20">
          <Text>&lt;The selection's path&gt;</Text>
          <Subscribe Event="SelectionPath" Attribute="Text" />
          <Subscribe Event="SelectionPathOn" Attribute="Visible" />
          <Condition Action="hide">Installed</Condition>
        </Control>
        <Control Id="LocationLabel" Type="Text" X="25" Y="200" Width="50" Height="10" Text="Location:">
          <Subscribe Event="SelectionPathOn" Attribute="Visible" />
          <Condition Action="hide">Installed</Condition>
        </Control>
      </Dialog>
      <Dialog Id="DiskCostDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="OK" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_OK]">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="20" Y="53" Width="330" Height="40">
          <Text>The highlighted volumes (if any) do not have enough disk space available for the currently selected features.  You can either remove some files from the highlighted volumes, or choose to install less features onto local drive(s), or select different destination drive(s).</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="20" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>The disk space required for the installation of the selected features.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Disk Space Requirements</Text>
        </Control>
        <Control Id="VolumeList" Type="VolumeCostList" X="20" Y="100" Width="330" Height="120" Sunken="yes" Fixed="yes" Remote="yes">
          <Text>{120}{70}{70}{70}{70}</Text>
        </Control>
      </Dialog>
      <Dialog Id="ErrorDlg" Y="10" Width="270" Height="105" Title="Installer Information" ErrorDialog="yes" NoMinimize="yes">
        <Control Id="ErrorText" Type="Text" X="48" Y="15" Width="205" Height="60" TabSkip="no" Text="Information text" />
        <Control Id="Y" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_Yes]">
          <Publish Event="EndDialog" Value="ErrorYes">1</Publish>
        </Control>
        <Control Id="A" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_Cancel]">
          <Publish Event="EndDialog" Value="ErrorAbort">1</Publish>
        </Control>
        <Control Id="C" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_Cancel]">
          <Publish Event="EndDialog" Value="ErrorCancel">1</Publish>
        </Control>
        <Control Id="ErrorIcon" Type="Icon" X="15" Y="15" Width="24" Height="24" ToolTip="Information icon" FixedSize="yes" IconSize="32" Text="[InfoIcon]" />
        <Control Id="I" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_Ignore]">
          <Publish Event="EndDialog" Value="ErrorIgnore">1</Publish>
        </Control>
        <Control Id="N" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_No]">
          <Publish Event="EndDialog" Value="ErrorNo">1</Publish>
        </Control>
        <Control Id="O" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_OK]">
          <Publish Event="EndDialog" Value="ErrorOk">1</Publish>
        </Control>
        <Control Id="R" Type="PushButton" X="100" Y="80" Width="56" Height="17" TabSkip="yes" Text="[ButtonText_Retry]">
          <Publish Event="EndDialog" Value="ErrorRetry">1</Publish>
        </Control>
      </Dialog>
      <Dialog Id="FilesInUse" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" KeepModeless="yes">
        <Control Id="Retry" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Retry]">
          <Publish Event="EndDialog" Value="Retry">1</Publish>
        </Control>
        <Control Id="Ignore" Type="PushButton" X="235" Y="243" Width="56" Height="17" Text="[ButtonText_Ignore]">
          <Publish Event="EndDialog" Value="Ignore">1</Publish>
        </Control>
        <Control Id="Exit" Type="PushButton" X="166" Y="243" Width="56" Height="17" Text="[ButtonText_Exit]">
          <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="20" Y="55" Width="330" Height="30">
          <Text>The following applications are using files that need to be updated by this setup. Close these applications and then click Retry to continue the installation or Cancel to exit it.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="23" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Some files that need to be updated are currently in use.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Files in Use</Text>
        </Control>
        <Control Id="List" Type="ListBox" X="20" Y="87" Width="330" Height="130" Property="FileInUseProcess" Sunken="yes" TabSkip="yes" />
      </Dialog>

      <Dialog Id="RunningProcessDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" KeepModeless="yes">

        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="CustomizeDlg">(InstallMode = "Custom" OR InstallMode = "Change") AND &amp;feaKfwClient &lt;&gt; 3 And !feaKfwClient &lt;&gt; 3</Publish>
          <Publish Event="NewDialog" Value="KerberosOptions">(InstallMode = "Custom" OR InstallMode = "Change") AND ( &amp;feaKfwClient = 3 Or !feaKfwClient = 3 )</Publish>
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">InstallMode = "Repair"</Publish>
          <Publish Event="NewDialog" Value="SetupTypeDlg">InstallMode = "Typical" OR InstallMode = "Complete"</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="20" Y="55" Width="330" Height="30">
          <Text>The following applications are currently running and need to be closed in order for the installation to progress.  Please close them manually or click Next to let [Wizard] close them for you.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="23" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Some running processes need to be closed.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Close Running Proccesses</Text>
        </Control>
        <Control Id="List" Type="ListBox" X="20" Y="87" Width="330" Height="130" Property="KillableProcesses" Sunken="yes" TabSkip="yes" />
      </Dialog>


      <Dialog Id="CCPErrorDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" KeepModeless="yes">

        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]" Disabled="yes">
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Text="[ButtonText_Finish]">
		  <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Disabled="yes" Text="[ButtonText_Cancel]">
        </Control>

		<Control Id="ErrorText" Type="Text" X="20" Y="70" Width="280" Height="75">
		  <Text>[ProductName] requires a newer version of iphlpapi.dll than the one that is currently installed on this computer.  This file is included in Microsoft Internet Explorer version 5.01 or later.  Please install this and re-run the [ProductName] installer.</Text>
		</Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="23" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>[ProgramName] requires a newer version of iphlpapi.dll</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Compliance Check Failed!</Text>
        </Control>
      </Dialog>

      <Dialog Id="LicenseAgreementDlg" Width="370" Height="270" Title="[ProductName] License Agreement" NoMinimize="yes">
        <Control Id="Buttons" Type="RadioButtonGroup" X="20" Y="187" Width="330" Height="40" Property="IAgree" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="WelcomeDlg">1</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="NewDialog" Value="UserRegistrationDlg">IAgree = "Yes" AND ShowUserRegistrationDlg = 1</Publish>
          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">CostingComplete = 1</Publish>
          <Publish Event="NewDialog" Value="RemovePreviousDlg">IAgree = "Yes" AND ShowUserRegistrationDlg &lt;&gt; 1 AND (UPGRADEPISMERE OR UPGRADEKFW OR UPGRADENSIS &lt;&gt; "")</Publish>
          <Publish Event="NewDialog" Value="SetupTypeDlg">IAgree = "Yes" AND ShowUserRegistrationDlg &lt;&gt; 1 AND NOT (UPGRADEPISMERE OR UPGRADEKFW OR UPGRADENSIS &lt;&gt; "")</Publish>
          <Condition Action="disable">IAgree &lt;&gt; "Yes"</Condition>
          <Condition Action="enable">IAgree = "Yes"</Condition>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="AgreementText" Type="ScrollableText" X="20" Y="60" Width="330" Height="120" Sunken="yes" TabSkip="no">
          <Text SourceFile=".\lang\license.rtf"/>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Please read the following license agreement carefully</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]End-User License Agreement</Text>
        </Control>
      </Dialog>
      <Dialog Id="MaintenanceTypeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="ChangeLabel" Type="Text" X="105" Y="65" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]&amp;Modify</Text>
        </Control>
        <Control Id="ChangeButton" Type="PushButton" X="50" Y="65" Width="38" Height="38" ToolTip="Modify Installation" Default="yes" Icon="yes" FixedSize="yes" IconSize="32" Text="[CustomSetupIcon]">
          <Publish Property="InstallMode" Value="Change">1</Publish>
          <Publish Property="Progress1" Value="Changing">1</Publish>
          <Publish Property="Progress2" Value="changes">1</Publish>
          <Publish Event="NewDialog" Value="CustomizeDlg">1</Publish>
        </Control>
        <Control Id="RepairLabel" Type="Text" X="105" Y="114" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]Re&amp;pair</Text>
        </Control>
        <Control Id="RepairButton" Type="PushButton" X="50" Y="114" Width="38" Height="38" ToolTip="Repair Installation" Icon="yes" FixedSize="yes" IconSize="32" Text="[RepairIcon]">
          <Publish Property="InstallMode" Value="Repair">1</Publish>
          <Publish Property="Progress1" Value="Repairing">1</Publish>
          <Publish Property="Progress2" Value="repaires">1</Publish>
          <Publish Event="NewDialog" Value="VerifyRepairDlg">1</Publish>
        </Control>
        <Control Id="RemoveLabel" Type="Text" X="105" Y="163" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]&amp;Remove</Text>
        </Control>
        <Control Id="RemoveButton" Type="PushButton" X="50" Y="163" Width="38" Height="38" ToolTip="Remove Installation" Icon="yes" FixedSize="yes" IconSize="32" Text="[RemoveIcon]">
          <Publish Property="InstallMode" Value="Remove">1</Publish>
          <Publish Property="Progress1" Value="Removing">1</Publish>
          <Publish Property="Progress2" Value="removes">1</Publish>
          <Publish Event="NewDialog" Value="VerifyRemoveDlg">1</Publish>
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="MaintenanceWelcomeDlg">1</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Next]" />
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Select the operation you wish to perform.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="240" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Modify, Repair or Remove installation</Text>
        </Control>
        <Control Id="ChangeText" Type="Text" X="105" Y="78" Width="230" Height="20">
          <Text>Allows users to change the way features are installed.</Text>
        </Control>
        <Control Id="RemoveText" Type="Text" X="105" Y="176" Width="230" Height="20">
          <Text>Removes [ProductName] from your computer.</Text>
        </Control>
        <Control Id="RepairText" Type="Text" X="105" Y="127" Width="230" Height="30">
          <Text>Repairs errors in the most recent installation state - fixes missing or corrupt files, shortcuts and registry entries.</Text>
        </Control>
      </Dialog>
      <Dialog Id="MaintenanceWelcomeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">CostingComplete = 1</Publish>
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="70" Width="220" Height="60" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] will allow you to change the way [ProductName] features are installed on your computer or even to remove [ProductName] from your computer.  Click Next to continue or Cancel to exit the [Wizard].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="60" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Welcome to the [ProductName] [Wizard]</Text>
        </Control>
      </Dialog>
      <Dialog Id="OutOfDiskDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="OK" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_OK]">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="20" Y="53" Width="330" Height="40">
          <Text>The highlighted volumes do not have enough disk space available for the currently selected features.  You can either remove some files from the highlighted volumes, or choose to install less features onto local drive(s), or select different destination drive(s).</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="20" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Disk space required for the installation exceeds available disk space.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Out of Disk Space</Text>
        </Control>
        <Control Id="VolumeList" Type="VolumeCostList" X="20" Y="100" Width="330" Height="120" Sunken="yes" Fixed="yes" Remote="yes">
          <Text>{120}{70}{70}{70}{70}</Text>
        </Control>
      </Dialog>
      <Dialog Id="OutOfRbDiskDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="No" Type="PushButton" X="304" Y="243" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_No]">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Yes" Type="PushButton" X="240" Y="243" Width="56" Height="17" Text="[ButtonText_Yes]">
          <Publish Event="EnableRollback" Value="False">1</Publish>
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="20" Y="53" Width="330" Height="40">
          <Text>The highlighted volumes do not have enough disk space available for the currently selected features.  You can either remove some files from the highlighted volumes, or choose to install less features onto local drive(s), or select different destination drive(s).</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="20" Y="20" Width="280" Height="20" Transparent="yes" NoPrefix="yes">
          <Text>Disk space required for the installation exceeds available disk space.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Out of Disk Space</Text>
        </Control>
        <Control Id="VolumeList" Type="VolumeCostList" X="20" Y="140" Width="330" Height="80" Sunken="yes" Fixed="yes" Remote="yes" ShowRollbackCost="yes">
          <Text>{120}{70}{70}{70}{70}</Text>
        </Control>
        <Control Id="Text2" Type="Text" X="20" Y="94" Width="330" Height="40">
          <Text>Alternatively, you may choose to disable the installer's rollback functionality.  This allows the installer to restore your computer's original state should the installation be interrupted in any way.  Click Yes if you wish to take the risk to disable rollback.</Text>
        </Control>
      </Dialog>
      <Dialog Id="RemovePreviousDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Confirm" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="Confirm">
          <Publish Event="NewDialog" Value="SetupTypeDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" FixedSize="yes" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="LicenseAgreementDlg">ShowUserRegistrationDlg &lt;&gt; 1</Publish>
          <Publish Event="NewDialog" Value="UserRegistrationDlg">ShowUserRegistrationDlg = 1</Publish>
        </Control>
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Other versions of [ProductName] need to be removed.</Text>
        </Control>
        <Control Id="Text" Type="Text" X="25" Y="70" Width="320" Height="45">
          <Text>Click Confirm to uninstall the following version of Kerberos for Windows installed on this computer.  Installation of [ProductName] cannot continue unless this program is removed.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Uninstall previous versions</Text>
        </Control>
        <Control Id="RemoveIcon" Type="Icon" X="25" Y="130" Width="32" Height="32" IconSize="32" Text="[RemoveIcon]" />
        <Control Id="RemoveProductPismere" Type="Text" X="60" Y="146" Width="200" Height="15" Hidden="yes" Transparent="yes" NoPrefix="yes">
          <Text>MIT Project Pismere Kerberos for Windows : Product code [UPGRADEPISMERE]</Text>
          <Condition Action="show">UPGRADEPISMERE</Condition>
        </Control>
        <Control Id="RemoveProductKfw" Type="Text" X="60" Y="146" Width="200" Height="15" Hidden="yes" Transparent="yes" NoPrefix="yes">
          <Text>MIT Kerberos for Windows : Product code [UPGRADEKFW]</Text>
          <Condition Action="show">UPGRADEKFW</Condition>
        </Control>
        <Control Id="RemoveProductKfwNSIS" Type="Text" X="60" Y="146" Width="200" Height="15" Hidden="yes" Transparent="yes" NoPrefix="yes">
          <Text>MIT Kerberos for Windows (NSIS installer): [NSISVERSION]</Text>
          <Condition Action="show">UPGRADENSIS &lt;&gt; ""</Condition>
        </Control>
      </Dialog>
      <Dialog Id="ResumeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Install" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Install]">
          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">CostingComplete = 1</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="70" Width="220" Height="30" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] will complete the installation of [ProductName] on your computer.  Click Install to continue or Cancel to exit the [Wizard].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="60" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Resuming the [ProductName] [Wizard]</Text>
        </Control>
      </Dialog>
      <Dialog Id="SetupTypeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="TypicalLabel" Type="Text" X="105" Y="65" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]&amp;Typical</Text>
        </Control>
        <Control Id="TypicalButton" Type="PushButton" X="50" Y="65" Width="38" Height="38" ToolTip="Typical Installation" Default="yes" Icon="yes" FixedSize="yes" IconSize="32" Text="[InstallerIcon]">
          <Publish Property="InstallMode" Value="Typical">1</Publish>
          <Publish Event="SetInstallLevel" Value="50">1</Publish>
          <Publish Event="NewDialog" Value="VerifyReadyDlg">Not FoundProcesses</Publish>
          <Publish Event="NewDialog" Value="RunningProcessDlg">FoundProcesses</Publish>
        </Control>
        <Control Id="CustomLabel" Type="Text" X="105" Y="118" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]C&amp;ustom</Text>
        </Control>
        <Control Id="CustomButton" Type="PushButton" X="50" Y="118" Width="38" Height="38" ToolTip="Custom Installation" Icon="yes" FixedSize="yes" IconSize="32" Text="[CustomSetupIcon]">
          <Publish Property="InstallMode" Value="Custom">1</Publish>
          <Publish Event="NewDialog" Value="CustomizeDlg">1</Publish>
        </Control>
        <Control Id="CompleteLabel" Type="Text" X="105" Y="171" Width="100" Height="10" TabSkip="no">
          <Text>[DlgTitleFont]C&amp;omplete</Text>
        </Control>
        <Control Id="CompleteButton" Type="PushButton" X="50" Y="171" Width="38" Height="38" ToolTip="Complete Installation" Icon="yes" FixedSize="yes" IconSize="32" Text="[CompleteSetupIcon]">
          <Publish Property="InstallMode" Value="Complete">1</Publish>
          <Publish Event="SetInstallLevel" Value="1000">1</Publish>
          <Publish Event="NewDialog" Value="VerifyReadyDlg">Not FoundProcesses</Publish>
          <Publish Event="NewDialog" Value="RunningProcessDlg">FoundProcesses</Publish>
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="LicenseAgreementDlg">ShowUserRegistrationDlg &lt;&gt; 1 AND NOT (UPGRADEPISMERE OR UPGRADEKFW)</Publish>
          <Publish Event="NewDialog" Value="UserRegistrationDlg">ShowUserRegistrationDlg = 1 AND NOT (UPGRADEPISMERE OR UPGRADEKFW)</Publish>
          <Publish Event="NewDialog" Value="RemovePreviousDlg">UPGRADEPISMERE OR UPGRADEKFW OR UPGRADENSIS &lt;&gt; ""</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Next]" />
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Choose the setup type that best suits your needs</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Choose Setup Type</Text>
        </Control>
        <Control Id="CompleteText" Type="Text" X="105" Y="184" Width="230" Height="20">
          <Text>All program features will be installed.  (Requires most disk space)</Text>
        </Control>
        <Control Id="CustomText" Type="Text" X="105" Y="131" Width="230" Height="30">
          <Text>Allows users to choose which program features will be installed and where they will be installed. Recommended for advanced users.</Text>
        </Control>
        <Control Id="TypicalText" Type="Text" X="105" Y="78" Width="230" Height="20">
          <Text>Installs the most common program features. Recommended for most users.</Text>
        </Control>
      </Dialog>
      <Dialog Id="UserRegistrationDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="NameLabel" Type="Text" X="45" Y="73" Width="100" Height="15" TabSkip="no" Text="&amp;User Name:" />
        <Control Id="NameEdit" Type="Edit" X="45" Y="85" Width="220" Height="18" Property="USERNAME" Text="{80}" />
        <Control Id="OrganizationLabel" Type="Text" X="45" Y="110" Width="100" Height="15" TabSkip="no" Text="&amp;Organization:" />
        <Control Id="OrganizationEdit" Type="Edit" X="45" Y="122" Width="220" Height="18" Property="COMPANYNAME" Text="{80}" />
        <Control Id="CDKeyLabel" Type="Text" X="45" Y="147" Width="50" Height="10" TabSkip="no">
          <Text>CD &amp;Key:</Text>
        </Control>
        <Control Id="CDKeyEdit" Type="MaskedEdit" X="45" Y="159" Width="250" Height="16" Property="PIDKEY" Text="[PIDTemplate]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="LicenseAgreementDlg">1</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="ValidateProductID" Value="0">0</Publish>
          <Publish Event="SpawnWaitDialog" Value="WaitForCostingDlg">CostingComplete = 1</Publish>
          <Publish Event="NewDialog" Value="SetupTypeDlg">ProductID</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>Please enter your customer information</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Customer Information</Text>
        </Control>
      </Dialog>

      <Dialog Id="KerberosOptions" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">

		<Control Id="txtLeash" Type="Text" X="25" Y="55" Width="280" Height="30">
			<Text>MIT Kerberos may be installed with the following optional functionality.  Please check those items that you wish to activate.</Text>
		</Control>

        <Control Id="koAutoStartText" Type="CheckBox" X="25" Y="95" Width="280" Height="30" CheckBoxValue="1" Property="LEASHAUTOSTART" >
			<Text>Autostart MIT Kerberos each time you login to Windows</Text>
        </Control>
        
        <Control Id="koAutoInitText" Type="CheckBox" X="25" Y="130" Width="280" Height="30" CheckBoxValue="-autoinit" Property="LEASHAUTOINIT" >
			<Text>Get tickets (if there are no existing tickets) when MIT Kerberos is started</Text>
        </Control>

        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="CustomizeDlg">1</Publish>
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="NewDialog" Value="VerifyReadyDlg">Not FoundProcesses</Publish>
          <Publish Event="NewDialog" Value="RunningProcessDlg">FoundProcesses</Publish>
          <Publish Event="AddLocal" Value="feaKfwLeashStartup">LEASHAUTOSTART = 1</Publish>
          <Publish Event="Remove" Value="feaKfwLeashStartup">LEASHAUTOSTART &lt;&gt; 1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>

        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>MIT Kerberos startup options</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Kerberos Options</Text>
        </Control>
      </Dialog>

      <Dialog Id="VerifyReadyDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" TrackDiskSpace="yes">
        <Control Id="Install" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Install]">
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="AdminInstallPointDlg">InstallMode = "Server Image"</Publish>
          <Publish Event="NewDialog" Value="CustomizeDlg">(InstallMode = "Custom" OR InstallMode = "Change") AND &amp;feaKfwClient &lt;&gt; 3 And !feaKfwClient &lt;&gt; 3</Publish>
          <Publish Event="NewDialog" Value="KerberosOptions">(InstallMode = "Custom" OR InstallMode = "Change") AND ( &amp;feaKfwClient = 3 Or !feaKfwClient = 3 )</Publish>
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">InstallMode = "Repair"</Publish>
          <Publish Event="NewDialog" Value="SetupTypeDlg">InstallMode = "Typical" OR InstallMode = "Complete"</Publish>
        </Control>
        <Control Id="Text" Type="Text" X="25" Y="70" Width="320" Height="20">
          <Text>Click Install to begin the installation.  If you want to review or change any of your installation settings, click Back.  Click Cancel to exit the wizard.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] is ready to begin the [InstallMode] installation</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Ready to Install</Text>
        </Control>
      </Dialog>
      <Dialog Id="VerifyRemoveDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" TrackDiskSpace="yes">
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>
        </Control>
        <Control Id="Remove" Type="PushButton" X="236" Y="243" Width="56" Height="17" Text="[ButtonText_Remove]">
          <Publish Event="Remove" Value="All">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Text" Type="Text" X="25" Y="70" Width="320" Height="30">
          <Text>Click Remove to remove [ProductName] from your computer.  If you want to review or change any of your installation settings, click Back.  Click Cancel to exit the wizard.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>You have chosen to remove the program from your computer.</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Remove Kerberos for Windows</Text>
        </Control>
      </Dialog>
      <Dialog Id="VerifyRepairDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes" TrackDiskSpace="yes">
        <Control Id="Repair" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Repair]">
          <Publish Event="ReinstallMode" Value="ecmus">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="Reinstall" Value="All">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace &lt;&gt; 1</Publish>
          <Publish Event="SpawnDialog" Value="OutOfRbDiskDlg">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND (PROMPTROLLBACKCOST="P" OR NOT PROMPTROLLBACKCOST)</Publish>
          <Publish Event="EndDialog" Value="Return">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="EnableRollback" Value="False">OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 0 AND PROMPTROLLBACKCOST="D"</Publish>
          <Publish Event="SpawnDialog" Value="OutOfDiskDlg">(OutOfDiskSpace = 1 AND OutOfNoRbDiskSpace = 1) OR (OutOfDiskSpace = 1 AND PROMPTROLLBACKCOST="F")</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="374" Height="44" TabSkip="no" Text="[BannerBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="[ButtonText_Back]">
          <Publish Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>
        </Control>
        <Control Id="Text" Type="Text" X="25" Y="70" Width="320" Height="30">
          <Text>Click Repair to repair the installation of [ProductName].  If you want to review or change any of your installation settings, click Back.  Click Cancel to exit the wizard.</Text>
        </Control>
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="374" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] is ready to begin the repair of [ProductName].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>[DlgTitleFont]Repair Kerberos for Windows</Text>
        </Control>
      </Dialog>
      <Dialog Id="WaitForCostingDlg" Y="10" Width="260" Height="85" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Return" Type="PushButton" X="102" Y="57" Width="56" Height="17" Default="yes" Cancel="yes" Text="[ButtonText_Return]">
          <Publish Event="EndDialog" Value="Exit">1</Publish>
        </Control>
        <Control Id="Text" Type="Text" X="48" Y="15" Width="194" Height="30">
          <Text>Please wait while the installer finishes determining your disk space requirements.</Text>
        </Control>
        <Control Id="Icon" Type="Icon" X="15" Y="15" Width="24" Height="24" ToolTip="Exclamation icon" FixedSize="yes" IconSize="32" Text="[ExclamationIcon]" />
      </Dialog>
      <Dialog Id="WelcomeDlg" Width="370" Height="270" Title="[ProductName] [Setup]" NoMinimize="yes">
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="[ButtonText_Next]">
          <Publish Event="NewDialog" Value="LicenseAgreementDlg">(Installed) Or (CCP_Success = 1)</Publish>
          <Publish Event="NewDialog" Value="CCPErrorDlg">(Not Installed) And (CCP_Success &lt;&gt; 1)</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="[ButtonText_Cancel]">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
        <Control Id="Bitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="234" TabSkip="no" Text="[DialogBitmap]" />
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Disabled="yes" Text="[ButtonText_Back]" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="374" Height="0" />
        <Control Id="Description" Type="Text" X="135" Y="110" Width="220" Height="30" Transparent="yes" NoPrefix="yes">
          <Text>The [Wizard] will install [ProductName] on your computer.  Click Next to continue or Cancel to exit the [Wizard].</Text>
        </Control>
        <Control Id="Title" Type="Text" X="135" Y="20" Width="220" Height="100" Transparent="yes" NoPrefix="yes">
          <Text>{\VerdanaBold13}Welcome to the [ProductName] [Wizard]</Text>
        </Control>
      </Dialog>
      <RadioButtonGroup Property="IAgree">
        <RadioButton Text="{\DlgFont8}I &amp;accept the terms in the License Agreement" X="5" Y="0" Width="250" Height="15" Value="Yes"></RadioButton>
        <RadioButton Text="{\DlgFont8}I &amp;do not accept the terms in the License Agreement" X="5" Y="20" Width="250" Height="15" Value="No"></RadioButton>
      </RadioButtonGroup>
      <TextStyle Id="DlgFont8" FaceName="Tahoma" Size="8" />
      <TextStyle Id="DlgFontBold8" FaceName="Tahoma" Size="8" Bold="yes" />
      <TextStyle Id="VerdanaBold13" FaceName="Verdana" Size="13" Bold="yes" />
      <UIText Id="AbsentPath" />
      <UIText Id="bytes">bytes</UIText>
      <UIText Id="GB">GB</UIText>
      <UIText Id="KB">KB</UIText>
      <UIText Id="MB">MB</UIText>
      <UIText Id="MenuAbsent">Entire feature will be unavailable</UIText>
      <UIText Id="MenuAdvertise">Feature will be installed when required</UIText>
      <UIText Id="MenuAllCD">Entire feature will be installed to run from CD</UIText>
      <UIText Id="MenuAllLocal">Entire feature will be installed on local hard drive</UIText>
      <UIText Id="MenuAllNetwork">Entire feature will be installed to run from network</UIText>
      <UIText Id="MenuCD">Will be installed to run from CD</UIText>
      <UIText Id="MenuLocal">Will be installed on local hard drive</UIText>
      <UIText Id="MenuNetwork">Will be installed to run from network</UIText>
      <UIText Id="ScriptInProgress">Gathering required information...</UIText>
      <UIText Id="SelAbsentAbsent">This feature will remain uninstalled</UIText>
      <UIText Id="SelAbsentAdvertise">This feature will be set to be installed when required</UIText>
      <UIText Id="SelAbsentCD">This feature will be installed to run from CD</UIText>
      <UIText Id="SelAbsentLocal">This feature will be installed on the local hard drive</UIText>
      <UIText Id="SelAbsentNetwork">This feature will be installed to run from the network</UIText>
      <UIText Id="SelAdvertiseAbsent">This feature will become unavailable</UIText>
      <UIText Id="SelAdvertiseAdvertise">Will be installed when required</UIText>
      <UIText Id="SelAdvertiseCD">This feature will be available to run from CD</UIText>
      <UIText Id="SelAdvertiseLocal">This feature will be installed on your local hard drive</UIText>
      <UIText Id="SelAdvertiseNetwork">This feature will be available to run from the network</UIText>
      <UIText Id="SelCDAbsent">This feature will be uninstalled completely, you won't be able to run it from CD</UIText>
      <UIText Id="SelCDAdvertise">This feature will change from run from CD state to set to be installed when required</UIText>
      <UIText Id="SelCDCD">This feature will remain to be run from CD</UIText>
      <UIText Id="SelCDLocal">This feature will change from run from CD state to be installed on the local hard drive</UIText>
      <UIText Id="SelChildCostNeg">This feature frees up [1] on your hard drive.</UIText>
      <UIText Id="SelChildCostPos">This feature requires [1] on your hard drive.</UIText>
      <UIText Id="SelCostPending">Compiling cost for this feature...</UIText>
      <UIText Id="SelLocalAbsent">This feature will be completely removed</UIText>
      <UIText Id="SelLocalAdvertise">This feature will be removed from your local hard drive, but will be set to be installed when required</UIText>
      <UIText Id="SelLocalCD">This feature will be removed from your local hard drive, but will be still available to run from CD</UIText>
      <UIText Id="SelLocalLocal">This feature will remain on you local hard drive</UIText>
      <UIText Id="SelLocalNetwork">This feature will be removed from your local hard drive, but will be still available to run from the network</UIText>
      <UIText Id="SelNetworkAbsent">This feature will be uninstalled completely, you won't be able to run it from the network</UIText>
      <UIText Id="SelNetworkAdvertise">This feature will change from run from network state to set to be installed when required</UIText>
      <UIText Id="SelNetworkLocal">This feature will change from run from network state to be installed on the local hard drive</UIText>
      <UIText Id="SelNetworkNetwork">This feature will remain to be run from the network</UIText>
      <UIText Id="SelParentCostNegNeg">This feature frees up [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures free up [4] on your hard drive.</UIText>
      <UIText Id="SelParentCostNegPos">This feature frees up [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures require [4] on your hard drive.</UIText>
      <UIText Id="SelParentCostPosNeg">This feature requires [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures free up [4] on your hard drive.</UIText>
      <UIText Id="SelParentCostPosPos">This feature requires [1] on your hard drive. It has [2] of [3] subfeatures selected. The subfeatures require [4] on your hard drive.</UIText>
      <UIText Id="TimeRemaining">Time remaining: {[1] minutes }{[2] seconds}</UIText>
      <UIText Id="VolumeCostAvailable">Available</UIText>
      <UIText Id="VolumeCostDifference">Difference</UIText>
      <UIText Id="VolumeCostRequired">Required</UIText>
      <UIText Id="VolumeCostSize">Disk Size</UIText>
      <UIText Id="VolumeCostVolume">Volume</UIText>
      <ProgressText Action="InstallValidate">Validating install</ProgressText>
      <ProgressText Action="InstallFiles" Template="File: [1],  Directory: [9],  Size: [6]">Copying new files</ProgressText>
      <ProgressText Action="InstallAdminPackage" Template="File: [1], Directory: [9], Size: [6]">Copying network install files</ProgressText>
      <ProgressText Action="FileCost">Computing space requirements</ProgressText>
      <ProgressText Action="CostInitialize">Computing space requirements</ProgressText>
      <ProgressText Action="CostFinalize">Computing space requirements</ProgressText>
      <ProgressText Action="CreateShortcuts" Template="Shortcut: [1]">Creating shortcuts</ProgressText>
      <ProgressText Action="PublishComponents" Template="Component ID: [1], Qualifier: [2]">Publishing Qualified Components</ProgressText>
      <ProgressText Action="PublishFeatures" Template="Feature: [1]">Publishing Product Features</ProgressText>
      <ProgressText Action="PublishProduct">Publishing product information</ProgressText>
      <ProgressText Action="RegisterClassInfo" Template="Class Id: [1]">Registering Class servers</ProgressText>
      <ProgressText Action="RegisterExtensionInfo" Template="Extension: [1]">Registering extension servers</ProgressText>
      <ProgressText Action="RegisterMIMEInfo" Template="MIME Content Type: [1], Extension: [2]">Registering MIME info</ProgressText>
      <ProgressText Action="RegisterProgIdInfo" Template="ProgId: [1]">Registering program identifiers</ProgressText>
      <ProgressText Action="AllocateRegistrySpace" Template="Free space: [1]">Allocating registry space</ProgressText>
      <ProgressText Action="AppSearch" Template="Property: [1], Signature: [2]">Searching for installed applications</ProgressText>
      <ProgressText Action="BindImage" Template="File: [1]">Binding executables</ProgressText>
      <ProgressText Action="CCPSearch">Searching for qualifying products</ProgressText>
      <ProgressText Action="CreateFolders" Template="Folder: [1]">Creating folders</ProgressText>
      <ProgressText Action="DeleteServices" Template="Service: [1]">Deleting services</ProgressText>
      <ProgressText Action="DuplicateFiles" Template="File: [1],  Directory: [9],  Size: [6]">Creating duplicate files</ProgressText>
      <ProgressText Action="FindRelatedProducts" Template="Found application: [1]">Searching for related applications</ProgressText>
      <ProgressText Action="InstallODBC">Installing ODBC components</ProgressText>
      <ProgressText Action="InstallServices" Template="Service: [2]">Installing new services</ProgressText>
      <ProgressText Action="LaunchConditions">Evaluating launch conditions</ProgressText>
      <ProgressText Action="MigrateFeatureStates" Template="Application: [1]">Migrating feature states from related applications</ProgressText>
      <ProgressText Action="MoveFiles" Template="File: [1],  Directory: [9],  Size: [6]">Moving files</ProgressText>
      <ProgressText Action="PatchFiles" Template="File: [1],  Directory: [2],  Size: [3]">Patching files</ProgressText>
      <ProgressText Action="ProcessComponents">Updating component registration</ProgressText>
      <ProgressText Action="RegisterComPlus" Template="AppId: [1]{{, AppType: [2], Users: [3], RSN: [4]}}">Registering COM+ Applications and Components</ProgressText>
      <ProgressText Action="RegisterFonts" Template="Font: [1]">Registering fonts</ProgressText>
      <ProgressText Action="RegisterProduct" Template="[1]">Registering product</ProgressText>
      <ProgressText Action="RegisterTypeLibraries" Template="LibID: [1]">Registering type libraries</ProgressText>
      <ProgressText Action="RegisterUser" Template="[1]">Registering user</ProgressText>
      <ProgressText Action="RemoveDuplicateFiles" Template="File: [1], Directory: [9]">Removing duplicated files</ProgressText>
      <ProgressText Action="RemoveEnvironmentStrings" Template="Name: [1], Value: [2], Action [3]">Updating environment strings</ProgressText>
      <ProgressText Action="RemoveExistingProducts" Template="Application: [1], Command line: [2]">Removing applications</ProgressText>
      <ProgressText Action="RemoveFiles" Template="File: [1], Directory: [9]">Removing files</ProgressText>
      <ProgressText Action="RemoveFolders" Template="Folder: [1]">Removing folders</ProgressText>
      <ProgressText Action="RemoveIniValues" Template="File: [1],  Section: [2],  Key: [3], Value: [4]">Removing INI files entries</ProgressText>
      <ProgressText Action="RemoveODBC">Removing ODBC components</ProgressText>
      <ProgressText Action="RemoveRegistryValues" Template="Key: [1], Name: [2]">Removing system registry values</ProgressText>
      <ProgressText Action="RemoveShortcuts" Template="Shortcut: [1]">Removing shortcuts</ProgressText>
      <ProgressText Action="RMCCPSearch">Searching for qualifying products</ProgressText>
      <ProgressText Action="SelfRegModules" Template="File: [1], Folder: [2]">Registering modules</ProgressText>
      <ProgressText Action="SelfUnregModules" Template="File: [1], Folder: [2]">Unregistering modules</ProgressText>
      <ProgressText Action="SetODBCFolders">Initializing ODBC directories</ProgressText>
      <ProgressText Action="StartServices" Template="Service: [1]">Starting services</ProgressText>
      <ProgressText Action="StopServices" Template="Service: [1]">Stopping services</ProgressText>
      <ProgressText Action="UnpublishComponents" Template="Component ID: [1], Qualifier: [2]">Unpublishing Qualified Components</ProgressText>
      <ProgressText Action="UnpublishFeatures" Template="Feature: [1]">Unpublishing Product Features</ProgressText>
      <ProgressText Action="UnregisterClassInfo" Template="Class Id: [1]">Unregister Class servers</ProgressText>
      <ProgressText Action="UnregisterComPlus" Template="AppId: [1]{{, AppType: [2]}}">Unregistering COM+ Applications and Components</ProgressText>
      <ProgressText Action="UnregisterExtensionInfo" Template="Extension: [1]">Unregistering extension servers</ProgressText>
      <ProgressText Action="UnregisterFonts" Template="Font: [1]">Unregistering fonts</ProgressText>
      <ProgressText Action="UnregisterMIMEInfo" Template="MIME Content Type: [1], Extension: [2]">Unregistering MIME info</ProgressText>
      <ProgressText Action="UnregisterProgIdInfo" Template="ProgId: [1]">Unregistering program identifiers</ProgressText>
      <ProgressText Action="UnregisterTypeLibraries" Template="LibID: [1]">Unregistering type libraries</ProgressText>
      <ProgressText Action="WriteEnvironmentStrings" Template="Name: [1], Value: [2], Action [3]">Updating environment strings</ProgressText>
      <ProgressText Action="WriteIniValues" Template="File: [1],  Section: [2],  Key: [3], Value: [4]">Writing INI files values</ProgressText>
      <ProgressText Action="WriteRegistryValues" Template="Key: [1], Name: [2], Value: [3]">Writing system registry values</ProgressText>
      <ProgressText Action="Advertise">Advertising application</ProgressText>
      <ProgressText Action="GenerateScript" Template="[1]">Generating script operations for action:</ProgressText>
      <ProgressText Action="InstallSFPCatalogFile" Template="File: [1],  Dependencies: [2]">Installing system catalog</ProgressText>
      <ProgressText Action="MsiPublishAssemblies" Template="Application Context:[1], Assembly Name:[2]">Publishing assembly information</ProgressText>
      <ProgressText Action="MsiUnpublishAssemblies" Template="Application Context:[1], Assembly Name:[2]">Unpublishing assembly information</ProgressText>
      <ProgressText Action="Rollback" Template="[1]">Rolling back action:</ProgressText>
      <ProgressText Action="RollbackCleanup" Template="File: [1]">Removing backup files</ProgressText>
      <ProgressText Action="UnmoveFiles" Template="File: [1], Directory: [9]">Removing moved files</ProgressText>
      <ProgressText Action="UnpublishProduct">Unpublishing product information</ProgressText>
      <Error Id="0">{{Fatal error: }}</Error>
      <Error Id="1">{{Error [1]. }}</Error>
      <Error Id="2">Warning [1]. </Error>
      <Error Id="3" />
      <Error Id="4">Info [1]. </Error>
      <Error Id="5">The installer has encountered an unexpected error installing this package. This may indicate a problem with this package. The error code is [1]. {{The arguments are: [2], [3], [4]}}</Error>
      <Error Id="6" />
      <Error Id="7">{{Disk full: }}</Error>
      <Error Id="8">Action [Time]: [1]. [2]</Error>
      <Error Id="9">[ProductName]</Error>
      <Error Id="10">{[2]}{, [3]}{, [4]}</Error>
      <Error Id="11">Message type: [1], Argument: [2]</Error>
      <Error Id="12">=== Logging started: [Date]  [Time] ===</Error>
      <Error Id="13">=== Logging stopped: [Date]  [Time] ===</Error>
      <Error Id="14">Action start [Time]: [1].</Error>
      <Error Id="15">Action ended [Time]: [1]. Return value [2].</Error>
      <Error Id="16">Time remaining: {[1] minutes }{[2] seconds}</Error>
      <Error Id="17">Out of memory. Shut down other applications before retrying.</Error>
      <Error Id="18">Installer is no longer responding.</Error>
      <Error Id="19">Installer stopped prematurely.</Error>
      <Error Id="20">Please wait while Windows configures [ProductName]</Error>
      <Error Id="21">Gathering required information...</Error>
      <Error Id="22">Removing older versions of this application...</Error>
      <Error Id="23">Preparing to remove older versions of this application...</Error>
      <Error Id="32">{[ProductName] }Setup completed successfully.</Error>
      <Error Id="33">{[ProductName] }Setup failed.</Error>
      <Error Id="1101">Error reading from file: [2]. {{ System error [3].}}  Verify that the file exists and that you can access it.</Error>
      <Error Id="1301">Cannot create the file '[2]'.  A directory with this name already exists.  Cancel the install and try installing to a different location.</Error>
      <Error Id="1302">Please insert the disk: [2]</Error>
      <Error Id="1303">The installer has insufficient privileges to access this directory: [2].  The installation cannot continue.  Log on as administrator or contact your system administrator.</Error>
      <Error Id="1304">Error writing to file: [2].  Verify that you have access to that directory.</Error>
      <Error Id="1305">Error reading from file [2]. {{ System error [3].}} Verify that the file exists and that you can access it.</Error>
      <Error Id="1306">Another application has exclusive access to the file '[2]'.  Please shut down all other applications, then click Retry.</Error>
      <Error Id="1307">There is not enough disk space to install this file: [2].  Free some disk space and click Retry, or click Cancel to exit.</Error>
      <Error Id="1308">Source file not found: [2].  Verify that the file exists and that you can access it.</Error>
      <Error Id="1309">Error reading from file: [3]. {{ System error [2].}}  Verify that the file exists and that you can access it.</Error>
      <Error Id="1310">Error writing to file: [3]. {{ System error [2].}}  Verify that you have access to that directory.</Error>
      <Error Id="1311">Source file not found{{(cabinet)}}: [2].  Verify that the file exists and that you can access it.</Error>
      <Error Id="1312">Cannot create the directory '[2]'.  A file with this name already exists.  Please rename or remove the file and click retry, or click Cancel to exit.</Error>
      <Error Id="1313">The volume [2] is currently unavailable.  Please select another.</Error>
      <Error Id="1314">The specified path '[2]' is unavailable.</Error>
      <Error Id="1315">Unable to write to the specified folder: [2].</Error>
      <Error Id="1316">A network error occurred while attempting to read from the file: [2]</Error>
      <Error Id="1317">An error occurred while attempting to create the directory: [2]</Error>
      <Error Id="1318">A network error occurred while attempting to create the directory: [2]</Error>
      <Error Id="1319">A network error occurred while attempting to open the source file cabinet: [2]</Error>
      <Error Id="1320">The specified path is too long: [2]</Error>
      <Error Id="1321">The Installer has insufficient privileges to modify this file: [2].</Error>
      <Error Id="1322">A portion of the folder path '[2]' is invalid.  It is either empty or exceeds the length allowed by the system.</Error>
      <Error Id="1323">The folder path '[2]' contains words that are not valid in folder paths.</Error>
      <Error Id="1324">The folder path '[2]' contains an invalid character.</Error>
      <Error Id="1325">'[2]' is not a valid short file name.</Error>
      <Error Id="1326">Error getting file security: [3] GetLastError: [2]</Error>
      <Error Id="1327">Invalid Drive: [2]</Error>
      <Error Id="1328">Error applying patch to file [2].  It has probably been updated by other means, and can no longer be modified by this patch.  For more information contact your patch vendor.  {{System Error: [3]}}</Error>
      <Error Id="1329">A file that is required cannot be installed because the cabinet file [2] is not digitally signed.  This may indicate that the cabinet file is corrupt.</Error>
      <Error Id="1330">A file that is required cannot be installed because the cabinet file [2] has an invalid digital signature.  This may indicate that the cabinet file is corrupt.{{  Error [3] was returned by WinVerifyTrust.}}</Error>
      <Error Id="1331">Failed to correctly copy [2] file: CRC error.</Error>
      <Error Id="1332">Failed to correctly move [2] file: CRC error.</Error>
      <Error Id="1333">Failed to correctly patch [2] file: CRC error.</Error>
      <Error Id="1334">The file '[2]' cannot be installed because the file cannot be found in cabinet file '[3]'. This could indicate a network error, an error reading from the CD-ROM, or a problem with this package.</Error>
      <Error Id="1335">The cabinet file '[2]' required for this installation is corrupt and cannot be used. This could indicate a network error, an error reading from the CD-ROM, or a problem with this package.</Error>
      <Error Id="1336">There was an error creating a temporary file that is needed to complete this installation.{{  Folder: [3]. System error code: [2]}}</Error>
      <Error Id="1401">Could not create key: [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel. </Error>
      <Error Id="1402">Could not open key: [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel. </Error>
      <Error Id="1403">Could not delete value [2] from key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel. </Error>
      <Error Id="1404">Could not delete key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel. </Error>
      <Error Id="1405">Could not read value [2] from key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel. </Error>
      <Error Id="1406">Could not write value [2] to key [3]. {{ System error [4].}}  Verify that you have sufficient access to that key, or contact your support personnel.</Error>
      <Error Id="1407">Could not get value names for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</Error>
      <Error Id="1408">Could not get sub key names for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</Error>
      <Error Id="1409">Could not read security information for key [2]. {{ System error [3].}}  Verify that you have sufficient access to that key, or contact your support personnel.</Error>
      <Error Id="1410">Could not increase the available registry space. [2] KB of free registry space is required for the installation of this application.</Error>
      <Error Id="1500">Another installation is in progress. You must complete that installation before continuing this one.</Error>
      <Error Id="1501">Error accessing secured data. Please make sure the Windows Installer is configured properly and try the install again.</Error>
      <Error Id="1502">User '[2]' has previously initiated an install for product '[3]'.  That user will need to run that install again before they can use that product.  Your current install will now continue.</Error>
      <Error Id="1503">User '[2]' has previously initiated an install for product '[3]'.  That user will need to run that install again before they can use that product.</Error>
      <Error Id="1601">Out of disk space -- Volume: '[2]'; required space: [3] KB; available space: [4] KB.  Free some disk space and retry.</Error>
      <Error Id="1602">Are you sure you want to cancel?</Error>
      <Error Id="1603">The file [2][3] is being held in use{ by the following process: Name: [4], Id: [5], Window Title: '[6]'}.  Close that application and retry.</Error>
      <Error Id="1604">The product '[2]' is already installed, preventing the installation of this product.  The two products are incompatible.</Error>
      <Error Id="1605">There is not enough disk space on the volume '[2]' to continue the install with recovery enabled. [3] KB are required, but only [4] KB are available. Click Ignore to continue the install without saving recovery information, click Retry to check for available space again, or click Cancel to quit the installation.</Error>
      <Error Id="1606">Could not access network location [2].</Error>
      <Error Id="1607">The following applications should be closed before continuing the install:</Error>
      <Error Id="1608">Could not find any previously installed compliant products on the machine for installing this product.</Error>
      <Error Id="1609">An error occurred while applying security settings. [2] is not a valid user or group. This could be a problem with the package, or a problem connecting to a domain controller on the network. Check your network connection and click Retry, or Cancel to end the install. {{Unable to locate the user's SID, system error [3]}}</Error>
      <Error Id="1701">The key [2] is not valid.  Verify that you entered the correct key.</Error>
      <Error Id="1702">The installer must restart your system before configuration of [2] can continue.  Click Yes to restart now or No if you plan to manually restart later.</Error>
      <Error Id="1703">You must restart your system for the configuration changes made to [2] to take effect. Click Yes to restart now or No if you plan to manually restart later.</Error>
      <Error Id="1704">An installation for [2] is currently suspended.  You must undo the changes made by that installation to continue.  Do you want to undo those changes?</Error>
      <Error Id="1705">A previous installation for this product is in progress.  You must undo the changes made by that installation to continue.  Do you want to undo those changes?</Error>
      <Error Id="1706">An installation package for the product [2] cannot be found. Try the installation again using a valid copy of the installation package '[3]'.</Error>
      <Error Id="1707">Installation completed successfully.</Error>
      <Error Id="1708">Installation failed.</Error>
      <Error Id="1709">Product: [2] -- [3]</Error>
      <Error Id="1710">You may either restore your computer to its previous state or continue the install later. Would you like to restore?</Error>
      <Error Id="1711">An error occurred while writing installation information to disk.  Check to make sure enough disk space is available, and click Retry, or Cancel to end the install.</Error>
      <Error Id="1712">One or more of the files required to restore your computer to its previous state could not be found.  Restoration will not be possible.</Error>
      <Error Id="1713">[2] cannot install one of its required products. Contact your technical support group.  {{System Error: [3].}}</Error>
      <Error Id="1714">The older version of [2] cannot be removed.  Contact your technical support group.  {{System Error [3].}}</Error>
      <Error Id="1715">Installed [2]</Error>
      <Error Id="1716">Configured [2]</Error>
      <Error Id="1717">Removed [2]</Error>
      <Error Id="1718">File [2] was rejected by digital signature policy.</Error>
      <Error Id="1719">The Windows Installer Service could not be accessed. This can occur if you are running Windows in safe mode, or if the Windows Installer is not correctly installed. Contact your support personnel for assistance.</Error>
      <Error Id="1720">There is a problem with this Windows Installer package. A script required for this install to complete could not be run. Contact your support personnel or package vendor.  {{Custom action [2] script error [3], [4]: [5] Line [6], Column [7], [8] }}</Error>
      <Error Id="1721">There is a problem with this Windows Installer package. A program required for this install to complete could not be run. Contact your support personnel or package vendor. {{Action: [2], location: [3], command: [4] }}</Error>
      <Error Id="1722">There is a problem with this Windows Installer package. A program run as part of the setup did not finish as expected. Contact your support personnel or package vendor.  {{Action [2], location: [3], command: [4] }}</Error>
      <Error Id="1723">There is a problem with this Windows Installer package. A DLL required for this install to complete could not be run. Contact your support personnel or package vendor.  {{Action [2], entry: [3], library: [4] }}</Error>
      <Error Id="1724">Removal completed successfully.</Error>
      <Error Id="1725">Removal failed.</Error>
      <Error Id="1726">Advertisement completed successfully.</Error>
      <Error Id="1727">Advertisement failed.</Error>
      <Error Id="1728">Configuration completed successfully.</Error>
      <Error Id="1729">Configuration failed.</Error>
      <Error Id="1730">You must be an Administrator to remove this application. To remove this application, you can log on as an Administrator, or contact your technical support group for assistance.</Error>
      <Error Id="1801">The path [2] is not valid.  Please specify a valid path.</Error>
      <Error Id="1802">Out of memory. Shut down other applications before retrying.</Error>
      <Error Id="1803">There is no disk in drive [2]. Please insert one and click Retry, or click Cancel to go back to the previously selected volume.</Error>
      <Error Id="1804">There is no disk in drive [2]. Please insert one and click Retry, or click Cancel to return to the browse dialog and select a different volume.</Error>
      <Error Id="1805">The folder [2] does not exist.  Please enter a path to an existing folder.</Error>
      <Error Id="1806">You have insufficient privileges to read this folder.</Error>
      <Error Id="1807">A valid destination folder for the install could not be determined.</Error>
      <Error Id="1901">Error attempting to read from the source install database: [2].</Error>
      <Error Id="1902">Scheduling reboot operation: Renaming file [2] to [3]. Must reboot to complete operation.</Error>
      <Error Id="1903">Scheduling reboot operation: Deleting file [2]. Must reboot to complete operation.</Error>
      <Error Id="1904">Module [2] failed to register.  HRESULT [3].  Contact your support personnel.</Error>
      <Error Id="1905">Module [2] failed to unregister.  HRESULT [3].  Contact your support personnel.</Error>
      <Error Id="1906">Failed to cache package [2]. Error: [3]. Contact your support personnel.</Error>
      <Error Id="1907">Could not register font [2].  Verify that you have sufficient permissions to install fonts, and that the system supports this font.</Error>
      <Error Id="1908">Could not unregister font [2]. Verify that you that you have sufficient permissions to remove fonts.</Error>
      <Error Id="1909">Could not create Shortcut [2]. Verify that the destination folder exists and that you can access it.</Error>
      <Error Id="1910">Could not remove Shortcut [2]. Verify that the shortcut file exists and that you can access it.</Error>
      <Error Id="1911">Could not register type library for file [2].  Contact your support personnel.</Error>
      <Error Id="1912">Could not unregister type library for file [2].  Contact your support personnel.</Error>
      <Error Id="1913">Could not update the ini file [2][3].  Verify that the file exists and that you can access it.</Error>
      <Error Id="1914">Could not schedule file [2] to replace file [3] on reboot.  Verify that you have write permissions to file [3].</Error>
      <Error Id="1915">Error removing ODBC driver manager, ODBC error [2]: [3]. Contact your support personnel.</Error>
      <Error Id="1916">Error installing ODBC driver manager, ODBC error [2]: [3]. Contact your support personnel.</Error>
      <Error Id="1917">Error removing ODBC driver: [4], ODBC error [2]: [3]. Verify that you have sufficient privileges to remove ODBC drivers.</Error>
      <Error Id="1918">Error installing ODBC driver: [4], ODBC error [2]: [3]. Verify that the file [4] exists and that you can access it.</Error>
      <Error Id="1919">Error configuring ODBC data source: [4], ODBC error [2]: [3]. Verify that the file [4] exists and that you can access it.</Error>
      <Error Id="1920">Service '[2]' ([3]) failed to start.  Verify that you have sufficient privileges to start system services.</Error>
      <Error Id="1921">Service '[2]' ([3]) could not be stopped.  Verify that you have sufficient privileges to stop system services.</Error>
      <Error Id="1922">Service '[2]' ([3]) could not be deleted.  Verify that you have sufficient privileges to remove system services.</Error>
      <Error Id="1923">Service '[2]' ([3]) could not be installed.  Verify that you have sufficient privileges to install system services.</Error>
      <Error Id="1924">Could not update environment variable '[2]'.  Verify that you have sufficient privileges to modify environment variables.</Error>
      <Error Id="1925">You do not have sufficient privileges to complete this installation for all users of the machine.  Log on as administrator and then retry this installation.</Error>
      <Error Id="1926">Could not set file security for file '[3]'. Error: [2].  Verify that you have sufficient privileges to modify the security permissions for this file.</Error>
      <Error Id="1927">Component Services (COM+ 1.0) are not installed on this computer.  This installation requires Component Services in order to complete successfully.  Component Services are available on Windows 2000.</Error>
      <Error Id="1928">Error registering COM+ Application.  Contact your support personnel for more information.</Error>
      <Error Id="1929">Error unregistering COM+ Application.  Contact your support personnel for more information.</Error>
      <Error Id="1930">The description for service '[2]' ([3]) could not be changed.</Error>
      <Error Id="1931">The Windows Installer service cannot update the system file [2] because the file is protected by Windows.  You may need to update your operating system for this program to work correctly. {{Package version: [3], OS Protected version: [4]}}</Error>
      <Error Id="1932">The Windows Installer service cannot update the protected Windows file [2]. {{Package version: [3], OS Protected version: [4], SFP Error: [5]}}</Error>
      <Error Id="1933">The Windows Installer service cannot update one or more protected Windows files. {{SFP Error: [2].  List of protected files:\r\n[3]}}</Error>
      <Error Id="1934">User installations are disabled via policy on the machine.</Error>
      <Error Id="1935">An error occurred during the installation of assembly component [2]. HRESULT: [3]. {{assembly interface: [4], function: [5], assembly name: [6]}}</Error>
      
      <Error Id="4001">Custom action data not found. STATUS [2]</Error>
      <Error Id="4003">NSIS Uninstallation failed. Status [2]</Error>
      <Error Id="4004">ABORT: [2]</Error>
      <Error Id="4005">Custom action failed. Phase [2]</Error>
      <Error Id="4006">Failed to determine running processes. Status [2]</Error>
      <Error Id="4007">Failed to install Kerberos network provider.  Status [2]</Error>
      <Error Id="4008">NSIS Uninstallation failed to initialize.  Uninstaller path [2] returned error [3]</Error>
      <AdminUISequence>
        <Show Dialog="FatalError" OnExit="error" />
        <Show Dialog="UserExit" OnExit="cancel" />
        <Show Dialog="ExitDialog" OnExit="success" />
        <Show Dialog="PrepareDlg" Before="CostInitialize" />
        <Show Dialog="AdminWelcomeDlg" After="CostFinalize" />
        <Show Dialog="ProgressDlg" After="AdminWelcomeDlg" />
      </AdminUISequence>
      <InstallUISequence>
		<Custom Action="ListRunningProcesses" After="MigrateFeatureStates" />
        <Show Dialog="FatalError" OnExit="error" />
        <Show Dialog="UserExit" OnExit="cancel" />
        <Show Dialog="ExitDialog" OnExit="success" />
        <Show Dialog="PrepareDlg" After="LaunchConditions" />
        <Show Dialog="WelcomeDlg" After="ListRunningProcesses">NOT Installed</Show>
        <Show Dialog="ResumeDlg" After="WelcomeDlg">Installed AND (RESUME OR Preselected)</Show>
        <Show Dialog="MaintenanceWelcomeDlg" After="ResumeDlg">Installed AND NOT RESUME AND NOT Preselected</Show>
        <Show Dialog="ProgressDlg" After="MaintenanceWelcomeDlg" />
      </InstallUISequence>
    </UI>
    <Property Id="ErrorDialog"><![CDATA[ErrorDlg]]></Property>
    <Property Id="DefaultUIFont"><![CDATA[DlgFont8]]></Property>
    <Property Id="ButtonText_No"><![CDATA[&No]]></Property>
    <Property Id="ButtonText_Install"><![CDATA[&Install]]></Property>
    <Property Id="ButtonText_Next"><![CDATA[&Next >]]></Property>
    <Property Id="Setup"><![CDATA[Setup]]></Property>
    <Property Id="ButtonText_Browse"><![CDATA[Br&owse]]></Property>
    <Property Id="CustomSetupIcon"><![CDATA[custicon]]></Property>
    <Property Id="RepairIcon"><![CDATA[repairic]]></Property>
    <Property Id="ExclamationIcon"><![CDATA[exclamic]]></Property>
    <Property Id="ButtonText_Repair"><![CDATA[&Repair]]></Property>
    <Property Id="ButtonText_Back"><![CDATA[< &Back]]></Property>
    <Property Id="InstallMode"><![CDATA[Typical]]></Property>
    <Property Id="Progress2"><![CDATA[installs]]></Property>
    <Property Id="Progress1"><![CDATA[Installing]]></Property>
    <Property Id="Wizard"><![CDATA[Setup Wizard]]></Property>
    <Property Id="RemoveIcon"><![CDATA[removico]]></Property>
    <Property Id="ButtonText_Yes"><![CDATA[&Yes]]></Property>
    <Property Id="ButtonText_Ignore"><![CDATA[&Ignore]]></Property>
    <Property Id="ButtonText_Reset"><![CDATA[&Reset]]></Property>
    <Property Id="ButtonText_Refresh"><![CDATA[&Refresh]]></Property>
    <Property Id="ButtonText_Remove"><![CDATA[&Remove]]></Property>
    <Property Id="ShowUserRegistrationDlg"><![CDATA[0]]></Property>
    <Property Id="ButtonText_Exit"><![CDATA[&Exit]]></Property>
    <Property Id="ButtonText_Return"><![CDATA[&Return]]></Property>
    <Property Id="ButtonText_OK"><![CDATA[OK]]></Property>
    <Property Id="CompleteSetupIcon"><![CDATA[completi]]></Property>
    <Property Id="ButtonText_Resume"><![CDATA[&Resume]]></Property>
    <Property Id="ButtonText_Close"><![CDATA[&Close]]></Property>
    <Property Id="InstallerIcon"><![CDATA[insticon]]></Property>
    <Property Id="ButtonText_Finish"><![CDATA[&Finish]]></Property>
    <Property Id="PROMPTROLLBACKCOST"><![CDATA[P]]></Property>
    <Property Id="PIDTemplate"><![CDATA[12345<###-%%%%%%%>@@@@@]]></Property>
    <Property Id="DlgTitleFont"><![CDATA[{&DlgFontBold8}]]></Property>
    <Property Id="ButtonText_Cancel"><![CDATA[Cancel]]></Property>
    <Property Id="InfoIcon"><![CDATA[info]]></Property>
    <Property Id="ButtonText_Retry"><![CDATA[&Retry]]></Property>
    <Property Id="IAgree"><![CDATA[No]]></Property>
    <Property Id="BannerBitmap"><![CDATA[bannrbmp]]></Property>
    <Property Id="DialogBitmap"><![CDATA[dlgbmp]]></Property>
    <Binary Id="bannrbmp" SourceFile="Binary\bannrbmp.bmp" />
    <Binary Id="completi" SourceFile="Binary\completi.ico" />
    <Binary Id="custicon" SourceFile="Binary\custicon.ico" />
    <Binary Id="dlgbmp" SourceFile="Binary\dlgbmp.bmp" />
    <Binary Id="exclamic" SourceFile="Binary\exclamic.ico" />
    <Binary Id="info" SourceFile="Binary\info.bmp" />
    <Binary Id="insticon" SourceFile="Binary\insticon.ico" />
    <Binary Id="New" SourceFile="Binary\New.bmp" />
    <Binary Id="removico" SourceFile="Binary\removico.ico" />
    <Binary Id="repairic" SourceFile="Binary\repairic.ico" />
    <Binary Id="Up" SourceFile="Binary\up.bmp" />
</Include>
