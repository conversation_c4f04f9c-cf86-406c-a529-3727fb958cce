# -*- mode: perl; -*-
# Copyright 2016-2016 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test DTLS CertStatus messages

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (
    {
        name => "certstatus-good",
        server => {
            extra => {
                "CertStatus" => "GoodResponse",
            },
        },
        client => {},
        test => {
            "Method" => "DTLS",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "certstatus-bad",
        server => {
            extra => {
                "CertStatus" => "BadResponse",
            },
        },
        client => {},
        test => {
            "Method" => "DTLS",
            "ExpectedResult" => "ClientFail"
        }
    }
);

our @tests_sctp = (
    {
        name => "certstatus-good",
        server => {
            extra => {
                "CertStatus" => "GoodResponse",
            },
        },
        client => {},
        test => {
            "Method" => "DTLS",
            "UseSCTP" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "certstatus-bad",
        server => {
            extra => {
                "CertStatus" => "BadResponse",
            },
        },
        client => {},
        test => {
            "Method" => "DTLS",
            "UseSCTP" => "Yes",
            "ExpectedResult" => "ClientFail"
        }
    },
);

push @tests, @tests_sctp unless disabled("sctp") || disabled("sock");
