=pod

=head1 NAME

cms_add1_signing_cert, cms_add1_signing_cert_v2
- add ESS signing-certificate signed attribute to a
CMS_SignerInfo data structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int cms_add1_signing_cert(CMS_SignerInfo *si, ESS_SIGNING_CERT *sc);

 int cms_add1_signing_cert_v2(CMS_SignerInfo *si, ESS_SIGNING_CERT_V2 *sc2);

=head1 DESCRIPTION

cms_add1_signing_cert() adds an ESS Signing Certificate I<sc> (version 1) signed
attribute to the CMS_SignerInfo I<si>.
cms_add1_signing_cert_v2() adds an ESS Signing Certificate I<sc2> (version 2) signed
attribute to the CMS_SignerInfo I<si>.
The ESS Signing Certificate attributes version 1 and 2 are defined in RFC 5035
which updates Section 5.4 of RFC 2634.

=head1 NOTES

This attribute is mandatory to make a CMS compliant with CAdES-BES
(European Standard ETSI EN 319 122-1 V1.1.1).
For a fuller description see L<openssl-cms(1)>).

=head1 RETURN VALUES

cms_add1_signing_cert() and cms_add1_signing_cert_v2() return 1 if attribute
is added or 0 if an error occurred.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
