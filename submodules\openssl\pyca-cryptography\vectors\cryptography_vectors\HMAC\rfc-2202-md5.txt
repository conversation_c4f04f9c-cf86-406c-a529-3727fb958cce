# 2. Test Cases for HMAC-MD5

Len = 64
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
# "Hi There"
Msg = 4869205468657265
MD = 9294727a3638bb1c13f48ef8158bfc9d

Len = 224
# "Jefe"
Key = 4a656665
# "what do ya want for nothing?"
Msg = 7768617420646f2079612077616e7420666f72206e6f7468696e673f
MD = 750c783e6ab0b503eaa86e310a5db738

Len = 400
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
Msg = dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
MD = 56be34521d144c88dbb8c733f0e8b3f6

Len = 400
Key = 0102030405060708090a0b0c0d0e0f10111213141516171819
Msg = cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd
MD = 697eaf0aca3a3aea3a75164746ffaa79

Len = 160
Key = 0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
# Note: We test the full MD rather than the truncated one in this test
# "Test With Truncation"
Msg = 546573742057697468205472756e636174696f6e
MD = 56461ef2342edc00f9bab995690efd4c

Len = 432
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
# "Test Using Larger Than Block-Size Key - Hash Key First"
Msg = 54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b6579202d2048617368204b6579204669727374
MD = 6b1ab7fe4bd7bf8f0b62e6ce61b9d0cd

Len = 584
Key = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
# "Test Using Larger Than Block-Size Key and Larger Than One Block-Size Data"
Msg = 54657374205573696e67204c6172676572205468616e20426c6f636b2d53697a65204b657920616e64204c6172676572205468616e204f6e6520426c6f636b2d53697a652044617461
MD = 6f630fad67cda0ee1fb1f562db3aa53e
