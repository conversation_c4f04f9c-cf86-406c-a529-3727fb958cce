#
# Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title = RC4 tests

Availablein = legacy
Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 0123456789abcdef
Ciphertext = 75b7878099e0c596

Availablein = legacy
Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 0000000000000000
Ciphertext = 7494c2e7104b0879

Availablein = legacy
Cipher = RC4
Key = 00000000000000000000000000000000
Plaintext = 0000000000000000
Ciphertext = de188941a3375d3a

Availablein = legacy
Cipher = RC4
Key = ef012345ef012345ef012345ef012345
Plaintext = 0000000000000000000000000000000000000000
Ciphertext = d6a141a7ec3c38dfbd615a1162e1c7ba36b67858

Availablein = legacy
Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 123456789ABCDEF0123456789ABCDEF0123456789ABCDEF012345678
Ciphertext = 66a0949f8af7d6891f7f832ba833c00c892ebe30143ce28740011ecf

Availablein = legacy
Cipher = RC4
Key = ef012345ef012345ef012345ef012345
Plaintext = 00000000000000000000
Ciphertext = d6a141a7ec3c38dfbd61

Title = RC4 tests (From RFC6229)

Availablein = legacy
Cipher = RC4-40
Key = 0102030405
Plaintext = 00000000000000000000000000000000
Ciphertext = b2396305f03dc027ccc3524a0a1118a8

Availablein = legacy
Cipher = RC4-40
Key = 833222772a
Plaintext = 00000000000000000000000000000000
Ciphertext = 80ad97bdc973df8a2e879e92a497efda

Availablein = legacy
Cipher = RC4
Key = 0102030405060708090a0b0c0d0e0f10
Plaintext = 00000000000000000000000000000000
Ciphertext = 9ac7cc9a609d1ef7b2932899cde41b97

Availablein = legacy
Cipher = RC4
Key = ebb46227c6cc8b37641910833222772a
Plaintext = 00000000000000000000000000000000
Ciphertext = 720c94b63edf44e131d950ca211a5a30

#Self generated. Long key
Availablein = legacy
Cipher = RC4
Key = ebb46227c6cc8b37641910833222772a00000000
Plaintext = 00000000000000000000000000000000
Ciphertext = 358b23dba47770e72c7ea8ce5bd68da3
