*~
*.a
*.dll
*.dylib
*.exe
*.exp
*.lib
*.map
*.o
*.obj
*.pc
*.pyc
*.so
binutils.versions
darwin.exports
hpux.exports
lib*.so.*
Makefile
OBJS.*
obj/
skiptests
testdir/
testlog
testtrace

# Ignore the build directory
/build/

# The autom4te cache directory
autom4te.cache/

# Generated by <PERSON><PERSON> for individual settings for directories
.directory

# Generated by Kate-Part
*.kate-swp
*.new

# KDevelop files
.kdev4/
*.kdev4

# Files generated by merges
*.orig

# macOS files
.DS_Store
.AppleDouble
.LSOverride

# macOS Resource Forks
._*

# macOS Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns

# macOS Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows image file caches
Thumbs.db
ehthumbs.db

# Folder config file
Desktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

# Visual Studio
.vscode

# TortoiseGit Project-level settings
/.tgitconfig

# Eclipse generated files

*.pydevproject
.autotools
.metadata
.gradle
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath

# Eclipse Core
.project

# Eclipse External tool builders
.externalToolBuilders/

# Eclipse Locally stored "Eclipse launch configurations"
*.launch

# Eclipse CDT-specific
.cproject

# Eclipse PDT-specific
.buildpath

# Eclipse sbteclipse plugin
.target

# Eclipse TeXlipse plugin
.texlipse

# Eclipse STS (Spring Tool Suite)
.springBeans

/doc/version.py

/doc/html/
/doc/pdf/

# Emacs tags table
/src/TAGS

/src/config.log
/src/config.status
/src/configure
/src/pyrunenv.vals
/src/runenv.py

/src/appl/gss-sample/gss-client
/src/appl/gss-sample/gss-server

/src/appl/sample/sclient/sclient

/src/appl/sample/sserver/sserver

/src/appl/simple/client/sim_client

/src/appl/simple/server/sim_server

/src/appl/user_user/uuclient
/src/appl/user_user/uuserver

/src/build-tools/krb5-config

/src/ccapi/lib/ccapi_err.c
/src/ccapi/lib/ccapi_err.h
/src/ccapi/lib/win/srctmp/

/src/ccapi/server/win/srctmp/

/src/ccapi/test/ccapi_ccache.c
/src/ccapi/test/ccapi_ccache_iterator.c
/src/ccapi/test/ccapi_context.c
/src/ccapi/test/ccapi_context_change_time.c
/src/ccapi/test/ccapi_credentials.c
/src/ccapi/test/ccapi_credentials_iterator.c
/src/ccapi/test/ccapi_err.c
/src/ccapi/test/ccapi_intermediates/
/src/ccapi/test/ccapi_ipc.c
/src/ccapi/test/ccapi_string.c
/src/ccapi/test/ccapi_test/
/src/ccapi/test/ccapi_v2.c
/src/ccapi/test/cci_cred_union.c
/src/ccapi/test/cci_debugging.h
/src/ccapi/test/cci_identifier.c
/src/ccapi/test/cci_message.c
/src/ccapi/test/cci_os_identifier.c
/src/ccapi/test/cci_types.h
/src/ccapi/test/ccs_reply.h
/src/ccapi/test/ccs_request.h
/src/ccapi/test/ccs_request_c.c
/src/ccapi/test/pingtest2.pdb
/src/ccapi/test/pingtest2.exe.manifest
/src/ccapi/test/testall1.exe
/src/ccapi/test/testall1.map
/src/ccapi/test/win-utils.h

/src/clients/kdestroy/kdestroy

/src/clients/kinit/kinit

/src/clients/klist/klist

/src/clients/kpasswd/kpasswd

/src/clients/ksu/ksu

/src/clients/kswitch/kswitch

/src/clients/kvno/kvno

/src/doc/Doxyfile
/src/doc/doxy/
/src/doc/paths.py
/src/doc/rst_apiref/
/src/doc/rst_composite/
/src/doc/html_subst/

/src/include/autoconf.h
/src/include/autoconf.h.in
/src/include/autoconf.stamp
/src/include/autoconf.stmp
/src/include/com_err.h
/src/include/db-config.h
/src/include/db.h
/src/include/gssapi/
/src/include/kadm5/
/src/include/kdc_j_encode.h
/src/include/krb5.stamp
/src/include/osconf.h
/src/include/private-and-public-decls
/src/include/profile.h
/src/include/ss/
/src/include/verto-k5ev.h
/src/include/verto.h
/src/include/*_err.h

/src/include/gssrpc/types.h

/src/include/krb5/krb5.h

/src/kadmin/cli/getdate.c
/src/kadmin/cli/kadmin
/src/kadmin/cli/kadmin.local
/src/kadmin/cli/kadmin_ct.c

/src/kadmin/dbutil/import_err.c
/src/kadmin/dbutil/import_err.h
/src/kadmin/dbutil/kdb5_util
/src/kadmin/dbutil/t_tdumputil

/src/kadmin/ktutil/ktutil
/src/kadmin/ktutil/ktutil_ct.c

/src/kadmin/server/kadmind

/src/kadmin/testing/admin_*
/src/kadmin/testing/init-*
/src/kadmin/testing/kadmin_*
/src/kadmin/testing/krb5-test-root/

/src/kadmin/testing/scripts/compare_dump.pl
/src/kadmin/testing/scripts/env-setup.sh
/src/kadmin/testing/scripts/env-setup.stamp
/src/kadmin/testing/scripts/make-host-keytab.pl
/src/kadmin/testing/scripts/qualname.pl
/src/kadmin/testing/scripts/simple_dump.pl
/src/kadmin/testing/scripts/verify_xrunner_report.pl

/src/kadmin/testing/util/kadm5_clnt_tcl
/src/kadmin/testing/util/kadm5_srv_tcl

/src/kdc/kdc5_err.[ch]
/src/kdc/krb5kdc
/src/kdc/rtest
/src/kdc/t_replay

/src/lib/k5sprt32.def

/src/lib/crypto/builtin/aes/aes-gen
/src/lib/crypto/builtin/aes/kresults.out

/src/lib/crypto/builtin/camellia/camellia-gen
/src/lib/crypto/builtin/camellia/kresults.out

/src/lib/crypto/builtin/des/destest
/src/lib/crypto/builtin/des/verify

/src/lib/crypto/builtin/sha1/t_shs
/src/lib/crypto/builtin/sha1/t_shs3

/src/lib/crypto/crypto_tests/aes-test
/src/lib/crypto/crypto_tests/camellia-test
/src/lib/crypto/crypto_tests/camellia-vt.txt
/src/lib/crypto/crypto_tests/t_cf2
/src/lib/crypto/crypto_tests/t_cf2.output
/src/lib/crypto/crypto_tests/t_cksum4
/src/lib/crypto/crypto_tests/t_cksum5
/src/lib/crypto/crypto_tests/t_cksums
/src/lib/crypto/crypto_tests/t_cmac
/src/lib/crypto/crypto_tests/t_combine
/src/lib/crypto/crypto_tests/t_crc
/src/lib/crypto/crypto_tests/t_cts
/src/lib/crypto/crypto_tests/t_decrypt
/src/lib/crypto/crypto_tests/t_derive
/src/lib/crypto/crypto_tests/t_encrypt
/src/lib/crypto/crypto_tests/t_fork
/src/lib/crypto/crypto_tests/t_hmac
/src/lib/crypto/crypto_tests/t_mddriver
/src/lib/crypto/crypto_tests/t_mddriver4
/src/lib/crypto/crypto_tests/t_nfold
/src/lib/crypto/crypto_tests/t_prf
/src/lib/crypto/crypto_tests/t_prf.output
/src/lib/crypto/crypto_tests/t_prng
/src/lib/crypto/crypto_tests/t_prng.output
/src/lib/crypto/crypto_tests/t_sha2
/src/lib/crypto/crypto_tests/t_short
/src/lib/crypto/crypto_tests/t_str2key
/src/lib/crypto/crypto_tests/vk.txt
/src/lib/crypto/crypto_tests/vt.txt

/src/lib/crypto/krb/t_fortuna
/src/lib/crypto/krb/t_fortuna.output

/src/lib/gssapi/merged-gssapi-header.h

/src/lib/gssapi/generic/errmap.h
/src/lib/gssapi/generic/gssapi.h
/src/lib/gssapi/generic/gssapi_err_generic.[ch]
/src/lib/gssapi/generic/t_seqstate

/src/lib/gssapi/krb5/error_map.h
/src/lib/gssapi/krb5/gssapi_err_krb5.[ch]

/src/lib/kadm5/chpass_util_strings.[ch]
/src/lib/kadm5/kadm_err.[ch]

/src/lib/kadm5/unit-test/*.log
/src/lib/kadm5/unit-test/*.sum
/src/lib/kadm5/unit-test/*-test

/src/lib/kdb/adb_err.[ch]

/src/lib/kdb/t_sort_key_data
/src/lib/kdb/t_stringattr
/src/lib/kdb/t_ulog
/src/lib/kdb/test.ulog

/src/lib/krad/t_attr
/src/lib/krad/t_attrset
/src/lib/krad/t_client
/src/lib/krad/t_code
/src/lib/krad/t_packet
/src/lib/krad/t_remote

/src/lib/krb5/ccache/kcmrpc.c
/src/lib/krb5/ccache/kcmrpc.h
/src/lib/krb5/ccache/t_cc
/src/lib/krb5/ccache/t_cccol
/src/lib/krb5/ccache/t_cccursor
/src/lib/krb5/ccache/t_marshal
/src/lib/krb5/ccache/testcache

/src/lib/krb5/error_tables/*_err.[ch]

/src/lib/krb5/keytab/t_keytab

/src/lib/krb5/krb/t_authdata
/src/lib/krb5/krb/t_cc_config
/src/lib/krb5/krb/t_copy_context
/src/lib/krb5/krb/t_deltat
/src/lib/krb5/krb/t_etypes
/src/lib/krb5/krb/t_expand
/src/lib/krb5/krb/t_expire_warn
/src/lib/krb5/krb/t_get_etype_info
/src/lib/krb5/krb/t_in_ccache
/src/lib/krb5/krb/t_kerb
/src/lib/krb5/krb/t_pac
/src/lib/krb5/krb/t_parse_host_string
/src/lib/krb5/krb/t_princ
/src/lib/krb5/krb/t_ser
/src/lib/krb5/krb/t_vfy_increds
/src/lib/krb5/krb/t_walk_rtree
/src/lib/krb5/krb/t_response_items
/src/lib/krb5/krb/t_sname_match
/src/lib/krb5/krb/t_valid_times

/src/lib/krb5/os/t_expand_path
/src/lib/krb5/os/t_locate_kdc
/src/lib/krb5/os/t_std_conf
/src/lib/krb5/os/t_trace

/src/lib/krb5/unicode/.links
/src/lib/krb5/unicode/ucdata.[ch]
/src/lib/krb5/unicode/ucgendat.c
/src/lib/krb5/unicode/uctable.h
/src/lib/krb5/unicode/ure.[ch]
/src/lib/krb5/unicode/urestubs.c

/src/lib/rpc/types.stamp

/src/lib/rpc/unit-test/*.log
/src/lib/rpc/unit-test/*.sum
/src/lib/rpc/unit-test/client
/src/lib/rpc/unit-test/dbg.log
/src/lib/rpc/unit-test/server

/src/man/*.sub

/src/plugins/kdb/db2/libdb2/test/__dbtest
/src/plugins/kdb/db2/libdb2/test/dbtest

/src/plugins/kdb/ldap/ldap_util/getdate.c
/src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util

/src/plugins/preauth/pkinit/pkinit_kdf_test

/src/po/*.mo

/src/kprop/kprop
/src/kprop/kpropd
/src/kprop/kproplog

/src/tests/adata
/src/tests/au.log
/src/tests/etinfo
/src/tests/forward
/src/tests/gcred
/src/tests/hist
/src/tests/hooks
/src/tests/hrealm
/src/tests/icinterleave
/src/tests/icred
/src/tests/kdbtest
/src/tests/kdc.conf
/src/tests/krb5.conf
/src/tests/localauth
/src/tests/plugorder
/src/tests/rdreq
/src/tests/responder
/src/tests/s2p
/src/tests/s4u2proxy
/src/tests/unlockiter

/src/tests/asn.1/expected_encode.out
/src/tests/asn.1/expected_trval.out
/src/tests/asn.1/krb5_decode_leak
/src/tests/asn.1/krb5_decode_test
/src/tests/asn.1/krb5_encode_test
/src/tests/asn.1/t_trval
/src/tests/asn.1/test.out
/src/tests/asn.1/trval.out

/src/tests/create/kdb5_mkdums

/src/tests/dejagnu/dbg.log
/src/tests/dejagnu/krb.log
/src/tests/dejagnu/krb.sum
/src/tests/dejagnu/runenv.vals
/src/tests/dejagnu/site.exp
/src/tests/dejagnu/t_inetd
/src/tests/dejagnu/tmpdir/

/src/tests/gss-threads/gss-client
/src/tests/gss-threads/gss-server

/src/tests/gssapi/ccinit
/src/tests/gssapi/ccrefresh
/src/tests/gssapi/t_accname
/src/tests/gssapi/t_add_cred
/src/tests/gssapi/t_ccselect
/src/tests/gssapi/t_ciflags
/src/tests/gssapi/t_credstore
/src/tests/gssapi/t_enctypes
/src/tests/gssapi/t_err
/src/tests/gssapi/t_export_cred
/src/tests/gssapi/t_export_name
/src/tests/gssapi/t_gssexts
/src/tests/gssapi/t_imp_cred
/src/tests/gssapi/t_imp_name
/src/tests/gssapi/t_invalid
/src/tests/gssapi/t_inq_cred
/src/tests/gssapi/t_inq_mechs_name
/src/tests/gssapi/t_iov
/src/tests/gssapi/t_lifetime
/src/tests/gssapi/t_namingexts
/src/tests/gssapi/t_oid
/src/tests/gssapi/t_pcontok
/src/tests/gssapi/t_prf
/src/tests/gssapi/t_s4u
/src/tests/gssapi/t_s4u2proxy_krb5
/src/tests/gssapi/t_saslname
/src/tests/gssapi/t_spnego
/src/tests/gssapi/t_srcattrs
/src/tests/gssapi/t_inq_ctx

/src/tests/hammer/kdc5_hammer

/src/tests/misc/test_chpw_message
/src/tests/misc/test_cxx_gss
/src/tests/misc/test_cxx_k5int
/src/tests/misc/test_cxx_kadm5
/src/tests/misc/test_cxx_krb5
/src/tests/misc/test_cxx_rpc
/src/tests/misc/test_getpw

/src/tests/ldap
/src/tests/mkeystash_compat/bigendian
/src/tests/mkeystash_compat/kdc.conf
/src/tests/mkeystash_compat/krb5.conf

/src/tests/resolve/addrinfo-test
/src/tests/resolve/fake-addrinfo-test
/src/tests/resolve/resolve

/src/tests/verify/kdb5_verify

/src/util/et/compile_et
/src/util/et/et?.[ch]
/src/util/et/t_com_err
/src/util/et/test?.[ch]
/src/util/et/test_et

/src/util/gss-kernel-lib/autoconf.h
/src/util/gss-kernel-lib/com_err.h
/src/util/gss-kernel-lib/gssapi/
/src/util/gss-kernel-lib/gssapi*.h
/src/util/gss-kernel-lib/k5-*.h
/src/util/gss-kernel-lib/k5seal*.c
/src/util/gss-kernel-lib/k5unseal*.c
/src/util/gss-kernel-lib/krb5.h
/src/util/gss-kernel-lib/krb5/
/src/util/gss-kernel-lib/osconf.h
/src/util/gss-kernel-lib/port-sockets.h
/src/util/gss-kernel-lib/profile.h
/src/util/gss-kernel-lib/socket-utils.h
/src/util/gss-kernel-lib/t_kgss_kernel
/src/util/gss-kernel-lib/t_kgss_user
/src/util/gss-kernel-lib/util_*.c

/src/util/k5ev/rename.h

/src/util/profile/*.bak
/src/util/profile/modtest.conf
/src/util/profile/prof_err.[ch]
/src/util/profile/profile.h
/src/util/profile/profile_tcl
/src/util/profile/test?.ini
/src/util/profile/test_include_dir/
/src/util/profile/test_load
/src/util/profile/test_parse
/src/util/profile/test_profile
/src/util/profile/test_vtable
/src/util/profile/testinc.ini
/src/util/profile/testinc2.ini

/src/util/ss/ct_c.awk
/src/util/ss/ct_c.sed
/src/util/ss/mk_cmds
/src/util/ss/ss_err.[ch]
/src/util/ss/std_rqs.c

/src/util/support/libkrb5support.exports
/src/util/support/t_base64
/src/util/support/t_hashtab
/src/util/support/t_hex
/src/util/support/t_json
/src/util/support/t_k5buf
/src/util/support/t_path
/src/util/support/t_path_win
/src/util/support/t_unal
/src/util/support/t_utf8
/src/util/support/t_utf16

/src/util/verto/rename.h

/src/plugins/kdb/db2/libdb2/test/t.be.db
/src/plugins/kdb/db2/libdb2/test/t.le.db

/src/windows/installer/wix/custom/custom.exp
/src/windows/installer/wix/kfw.wixobj
/src/windows/installer/wix/kfw.wixpdb

/src/windows/leash/htmlhelp/MITKerberosHelp.chm
/src/windows/leash/kfwribbon.bml
/src/windows/leash/kfwribbon.h
/src/windows/leash/kfwribbon.rc
/src/windows/leash/out2con.sav
