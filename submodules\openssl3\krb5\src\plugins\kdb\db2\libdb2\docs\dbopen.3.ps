%!PS-Adobe-3.0
%%Creator: groff version 1.08
%%DocumentNeededResources: font Times-Roman
%%+ font Times-Bold
%%+ font Times-Italic
%%DocumentSuppliedResources: procset grops 1.08 0
%%Pages: 4
%%PageOrder: Ascend
%%Orientation: Portrait
%%EndComments
%%BeginProlog
%%BeginResource: procset grops 1.08 0
/setpacking where{
pop
currentpacking
true setpacking
}if
/grops 120 dict dup begin
/SC 32 def
/A/show load def
/B{0 SC 3 -1 roll widthshow}bind def
/C{0 exch ashow}bind def
/D{0 exch 0 SC 5 2 roll awidthshow}bind def
/E{0 rmoveto show}bind def
/F{0 rmoveto 0 SC 3 -1 roll widthshow}bind def
/G{0 rmoveto 0 exch ashow}bind def
/H{0 rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/I{0 exch rmoveto show}bind def
/J{0 exch rmoveto 0 SC 3 -1 roll widthshow}bind def
/K{0 exch rmoveto 0 exch ashow}bind def
/L{0 exch rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/M{rmoveto show}bind def
/N{rmoveto 0 SC 3 -1 roll widthshow}bind def
/O{rmoveto 0 exch ashow}bind def
/P{rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/Q{moveto show}bind def
/R{moveto 0 SC 3 -1 roll widthshow}bind def
/S{moveto 0 exch ashow}bind def
/T{moveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/SF{
findfont exch
[exch dup 0 exch 0 exch neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/MF{
findfont
[5 2 roll
0 3 1 roll 
neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/level0 0 def
/RES 0 def
/PL 0 def
/LS 0 def
/PLG{
gsave newpath clippath pathbbox grestore
exch pop add exch pop
}bind def
/BP{
/level0 save def
1 setlinecap
1 setlinejoin
72 RES div dup scale
LS{
90 rotate
}{
0 PL translate
}ifelse
1 -1 scale
}bind def
/EP{
level0 restore
showpage
}bind def
/DA{
newpath arcn stroke
}bind def
/SN{
transform
.25 sub exch .25 sub exch
round .25 add exch round .25 add exch
itransform
}bind def
/DL{
SN
moveto
SN
lineto stroke
}bind def
/DC{
newpath 0 360 arc closepath
}bind def
/TM matrix def
/DE{
TM currentmatrix pop
translate scale newpath 0 0 .5 0 360 arc closepath
TM setmatrix
}bind def
/RC/rcurveto load def
/RL/rlineto load def
/ST/stroke load def
/MT/moveto load def
/CL/closepath load def
/FL{
currentgray exch setgray fill setgray
}bind def
/BL/fill load def
/LW/setlinewidth load def
/RE{
findfont
dup maxlength 1 index/FontName known not{1 add}if dict begin
{
1 index/FID ne{def}{pop pop}ifelse
}forall
/Encoding exch def
dup/FontName exch def
currentdict end definefont pop
}bind def
/DEFS 0 def
/EBEGIN{
moveto
DEFS begin
}bind def
/EEND/end load def
/CNT 0 def
/level1 0 def
/PBEGIN{
/level1 save def
translate
div 3 1 roll div exch scale
neg exch neg exch translate
0 setgray
0 setlinecap
1 setlinewidth
0 setlinejoin
10 setmiterlimit
[]0 setdash
/setstrokeadjust where{
pop
false setstrokeadjust
}if
/setoverprint where{
pop
false setoverprint
}if
newpath
/CNT countdictstack def
userdict begin
/showpage{}def
}bind def
/PEND{
clear
countdictstack CNT sub{end}repeat
level1 restore
}bind def
end def
/setpacking where{
pop
setpacking
}if
%%EndResource
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-Italic
grops begin/DEFS 1 dict def DEFS begin/u{.001 mul}bind def end/RES 72 def/PL
792 def/LS false def/ENC0[/asciicircum/asciitilde/Scaron/Zcaron/scaron/zcaron
/Ydieresis/trademark/quotesingle/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/space
/exclam/quotedbl/numbersign/dollar/percent/ampersand/quoteright/parenleft
/parenright/asterisk/plus/comma/hyphen/period/slash/zero/one/two/three/four
/five/six/seven/eight/nine/colon/semicolon/less/equal/greater/question/at/A/B/C
/D/E/F/G/H/I/J/K/L/M/N/O/P/Q/R/S/T/U/V/W/X/Y/Z/bracketleft/backslash
/bracketright/circumflex/underscore/quoteleft/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q
/r/s/t/u/v/w/x/y/z/braceleft/bar/braceright/tilde/.notdef/quotesinglbase
/guillemotleft/guillemotright/bullet/florin/fraction/perthousand/dagger
/daggerdbl/endash/emdash/ff/fi/fl/ffi/ffl/dotlessi/dotlessj/grave/hungarumlaut
/dotaccent/breve/caron/ring/ogonek/quotedblleft/quotedblright/oe/lslash
/quotedblbase/OE/Lslash/.notdef/exclamdown/cent/sterling/currency/yen/brokenbar
/section/dieresis/copyright/ordfeminine/guilsinglleft/logicalnot/minus
/registered/macron/degree/plusminus/twosuperior/threesuperior/acute/mu
/paragraph/periodcentered/cedilla/onesuperior/ordmasculine/guilsinglright
/onequarter/onehalf/threequarters/questiondown/Agrave/Aacute/Acircumflex/Atilde
/Adieresis/Aring/AE/Ccedilla/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute
/Icircumflex/Idieresis/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis
/multiply/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla/egrave/eacute
/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis/eth/ntilde/ograve
/oacute/ocircumflex/otilde/odieresis/divide/oslash/ugrave/uacute/ucircumflex
/udieresis/yacute/thorn/ydieresis]def/Times-Italic@0 ENC0/Times-Italic RE
/Times-Bold@0 ENC0/Times-Bold RE/Times-Roman@0 ENC0/Times-Roman RE
%%EndProlog
%%Page: 1 1
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.01(DBOPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.01(anual DBOPEN\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72
84 S(ME).18 E F0(dbopen \255 database access methods)108 96 Q F1(SYNOPSIS)72
112.8 Q/F2 10/Times-Bold@0 SF(#include <sys/types.h>)108 124.8 Q
(#include <limits.h>)108 136.8 Q(#include <db)108 148.8 Q(.h>)-.4 E(DB *)108
172.8 Q(dbopen\(const char *\214le, int \215ags, int mode, DBTYPE type,)108
184.8 Q(const v)158 196.8 Q(oid *openinf)-.1 E(o\);)-.25 E F1(DESCRIPTION)72
213.6 Q/F3 10/Times-Italic@0 SF(Dbopen)108 225.6 Q F0 .032
(is the library interf)2.532 F .031(ace to database \214les.)-.1 F .031
(The supported \214le formats are btree, hashed and UNIX \214le)5.031 F 2.82
(oriented. The)108 237.6 R .32
(btree format is a representation of a sorted, balanced tree structure.)2.82 F
.321(The hashed format is an)5.321 F -.15(ex)108 249.6 S .424
(tensible, dynamic hashing scheme.).15 F .423
(The \215at-\214le format is a byte stream \214le with \214x)5.423 F .423
(ed or v)-.15 F .423(ariable length)-.25 F 2.906(records. The)108 261.6 R .407
(formats and \214le format speci\214c information are described in detail in t\
heir respecti)2.906 F .707 -.15(ve m)-.25 H(anual).15 E(pages)108 273.6 Q F3
(btr)2.5 E(ee)-.37 E F0(\(3\),).18 E F3(hash)2.5 E F0(\(3\) and).28 E F3 -.37
(re)2.5 G(cno).37 E F0(\(3\).).18 E .433(Dbopen opens)108 290.4 R F3(\214le)
2.933 E F0 .433(for reading and/or writing.)2.933 F .433(Files ne)5.433 F -.15
(ve)-.25 G 2.933(ri).15 G .433(ntended to be preserv)346.737 290.4 R .433
(ed on disk may be created)-.15 F(by setting the \214le parameter to NULL.)108
302.4 Q(The)108 319.2 Q F3<8d61>4.661 E(gs)-.1 E F0(and)4.661 E F3 2.161
(mode ar)4.661 F(guments)-.37 E F0 2.161(are as speci\214ed to the)4.661 F F3
(open)4.661 E F0 2.162(\(2\) routine, ho).24 F(we)-.25 E -.15(ve)-.25 G 2.962
-.4(r, o).15 H 2.162(nly the O_CREA).4 F -.74(T,)-1.11 G .128
(O_EXCL, O_EXLOCK, O_NONBLOCK, O_RDONL)108 331.2 R 2.708 -1.29(Y, O)-1 H(_RD)
1.29 E .128(WR, O_SHLOCK and O_TR)-.3 F .127(UNC \215ags are)-.4 F 2.5
(meaningful. \(Note,)108 343.2 R(opening a database \214le O_WR)2.5 E(ONL)-.4 E
2.5(Yi)-1 G 2.5(sn)342.67 343.2 S(ot possible.\))354.06 343.2 Q(The)108 360 Q
F3(type)5.337 E F0(ar)5.337 E 2.837
(gument is of type DBTYPE \(as de\214ned in the <db)-.18 F 2.838
(.h> include \214le\) and may be set to)-.4 F(DB_BTREE, DB_HASH or DB_RECNO.)
108 372 Q(The)108 388.8 Q F3(openinfo)2.85 E F0(ar)2.85 E .349(gument is a poi\
nter to an access method speci\214c structure described in the access method')
-.18 F(s)-.55 E .03(manual page.)108 400.8 R(If)5.03 E F3(openinfo)2.53 E F0
.031(is NULL, each access method will use def)2.53 F .031
(aults appropriate for the system and the)-.1 F(access method.)108 412.8 Q F3
(Dbopen)108 429.6 Q F0 .416
(returns a pointer to a DB structure on success and NULL on error)2.917 F 5.416
(.T)-.55 G .416(he DB structure is de\214ned in)423.21 429.6 R(the <db)108
441.6 Q(.h> include \214le, and contains at least the follo)-.4 E
(wing \214elds:)-.25 E(typedef struct {)108 465.6 Q(DBTYPE type;)144 477.6 Q
(int \(*close\)\(const DB *db\);)144 489.6 Q
(int \(*del\)\(const DB *db, const DBT *k)144 501.6 Q -.15(ey)-.1 G 2.5(,u)-.5
G(_int \215ags\);)318.92 501.6 Q(int \(*fd\)\(const DB *db\);)144 513.6 Q
(int \(*get\)\(const DB *db, DBT *k)144 525.6 Q -.15(ey)-.1 G 2.5(,D)-.5 G
(BT *data, u_int \215ags\);)297.53 525.6 Q(int \(*put\)\(const DB *db, DBT *k)
144 537.6 Q -.15(ey)-.1 G 2.5(,c)-.5 G(onst DBT *data,)295.31 537.6 Q
(u_int \215ags\);)194 549.6 Q(int \(*sync\)\(const DB *db, u_int \215ags\);)144
561.6 Q(int \(*seq\)\(const DB *db, DBT *k)144 573.6 Q -.15(ey)-.1 G 2.5(,D)-.5
G(BT *data, u_int \215ags\);)298.64 573.6 Q 2.5(}D)108 585.6 S(B;)122.52 585.6
Q .101
(These elements describe a database type and a set of functions performing v)
108 602.4 R .101(arious actions.)-.25 F .101(These functions)5.101 F(tak)108
614.4 Q 3.039(eap)-.1 G .539(ointer to a structure as returned by)140.078 614.4
R F3(dbopen)3.038 E F0 3.038(,a).24 G .538
(nd sometimes one or more pointers to k)323.196 614.4 R -.15(ey)-.1 G .538
(/data struc-).15 F(tures and a \215ag v)108 626.4 Q(alue.)-.25 E 16.28
(type The)108 643.2 R
(type of the underlying access method \(and \214le format\).)2.5 E 12.95
(close A)108 660 R .988(pointer to a routine to \215ush an)3.488 F 3.489(yc)
-.15 G .989(ached information to disk, free an)293.968 660 R 3.489(ya)-.15 G
.989(llocated resources, and)446.662 660 R .112
(close the underlying \214le\(s\).)144 672 R .111(Since k)5.112 F -.15(ey)-.1 G
.111(/data pairs may be cached in memory).15 F 2.611(,f)-.65 G .111
(ailing to sync the \214le)455.666 672 R .494(with a)144 684 R F3(close)2.994 E
F0(or)2.994 E F3(sync)2.994 E F0 .495
(function may result in inconsistent or lost information.)2.994 F F3(Close)
5.495 E F0 .495(routines return)2.995 F(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15
G(istrib)132.57 732 Q 89.875(ution September)-.2 F(13, 1993)2.5 E(1)535 732 Q
EP
%%Page: 2 2
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.01(DBOPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.01(anual DBOPEN\(3\))340.17 48 R(-1 on error \(setting)144 84 Q
/F1 10/Times-Italic@0 SF(errno)2.5 E F0 2.5(\)a).18 G(nd 0 on success.)254.43
84 Q 21.28(del A)108 100.8 R(pointer to a routine to remo)2.5 E .3 -.15(ve k)
-.15 H -.15(ey).05 G(/data pairs from the database.).15 E(The parameter)144
117.6 Q F1<8d61>2.5 E(g)-.1 E F0(may be set to the follo)2.5 E(wing v)-.25 E
(alue:)-.25 E(R_CURSOR)144 134.4 Q .289
(Delete the record referenced by the cursor)180 146.4 R 5.288(.T)-.55 G .288
(he cursor must ha)363.342 146.4 R .588 -.15(ve p)-.2 H(re).15 E .288
(viously been initial-)-.25 F(ized.)180 158.4 Q F1(Delete)144 175.2 Q F0 .03
(routines return -1 on error \(setting)2.53 F F1(errno)2.53 E F0 .03
(\), 0 on success, and 1 if the speci\214ed).18 F F1 -.1(ke)2.53 G(y)-.2 E F0
-.1(wa)2.53 G 2.53(sn).1 G .03(ot in)521.91 175.2 R(the \214le.)144 187.2 Q
25.17(fd A)108 204 R .451
(pointer to a routine which returns a \214le descriptor representati)2.951 F
.75 -.15(ve o)-.25 H 2.95(ft).15 G .45(he underlying database.)431.73 204 R(A)
5.45 E .942(\214le descriptor referencing the same \214le will be returned to \
all processes which call)144 216 R F1(dbopen)3.442 E F0(with)3.442 E 1.629
(the same)144 228 R F1(\214le)4.129 E F0 4.129(name. This)4.129 F 1.628
(\214le descriptor may be safely used as a ar)4.128 F 1.628(gument to the)-.18
F F1(fcntl)4.128 E F0 1.628(\(2\) and).51 F F1(\215oc)144 240 Q(k)-.2 E F0 .425
(\(2\) locking functions.).67 F .425
(The \214le descriptor is not necessarily associated with an)5.425 F 2.925(yo)
-.15 G 2.925(ft)492.7 240 S .425(he under)501.735 240 R(-)-.2 E .198
(lying \214les used by the access method.)144 252 R .198
(No \214le descriptor is a)5.198 F -.25(va)-.2 G .198
(ilable for in memory databases.).25 F F1(Fd)5.198 E F0
(routines return -1 on error \(setting)144 264 Q F1(errno)2.5 E F0
(\), and the \214le descriptor on success.).18 E 21.28(get A)108 280.8 R
(pointer to a routine which is the interf)2.5 E .001(ace for k)-.1 F -.15(ey)
-.1 G .001(ed retrie).15 F -.25(va)-.25 G 2.501(lf).25 G .001
(rom the database.)399.755 280.8 R .001(The address and)5.001 F .061
(length of the data associated with the speci\214ed)144 292.8 R F1 -.1(ke)2.561
G(y)-.2 E F0 .06(are returned in the structure referenced by)2.561 F F1(data)
2.56 E F0(.).26 E F1(Get)144 304.8 Q F0(routines return -1 on error \(setting)
2.5 E F1(errno)2.5 E F0(\), 0 on success, and 1 if the).18 E F1 -.1(ke)2.5 G(y)
-.2 E F0 -.1(wa)2.5 G 2.5(sn).1 G(ot in the \214le.)471.66 304.8 Q 20.72(put A)
108 321.6 R(pointer to a routine to store k)2.5 E -.15(ey)-.1 G
(/data pairs in the database.).15 E(The parameter)144 338.4 Q F1<8d61>2.5 E(g)
-.1 E F0(may be set to one of the follo)2.5 E(wing v)-.25 E(alues:)-.25 E
(R_CURSOR)144 355.2 Q .051(Replace the k)180 367.2 R -.15(ey)-.1 G .051
(/data pair referenced by the cursor).15 F 5.052(.T)-.55 G .052
(he cursor must ha)393.98 367.2 R .352 -.15(ve p)-.2 H(re).15 E .052
(viously been)-.25 F(initialized.)180 379.2 Q(R_IAFTER)144 396 Q 1.165
(Append the data immediately after the data referenced by)180 408 R F1 -.1(ke)
3.664 G(y)-.2 E F0 3.664(,c).32 G 1.164(reating a ne)446.758 408 R 3.664(wk)
-.25 G -.15(ey)511.27 408 S(/data).15 E(pair)180 420 Q 6.065(.T)-.55 G 1.065
(he record number of the appended k)209.675 420 R -.15(ey)-.1 G 1.065
(/data pair is returned in the).15 F F1 -.1(ke)3.565 G(y)-.2 E F0(structure.)
3.565 E(\(Applicable only to the DB_RECNO access method.\))180 432 Q(R_IBEFORE)
144 448.8 Q 1.293(Insert the data immediately before the data referenced by)180
460.8 R F1 -.1(ke)3.793 G(y)-.2 E F0 3.793(,c).32 G 1.293(reating a ne)446.371
460.8 R 3.793(wk)-.25 G -.15(ey)511.27 460.8 S(/data).15 E(pair)180 472.8 Q
6.54(.T)-.55 G 1.54(he record number of the inserted k)210.15 472.8 R -.15(ey)
-.1 G 1.541(/data pair is returned in the).15 F F1 -.1(ke)4.041 G(y)-.2 E F0
(structure.)4.041 E(\(Applicable only to the DB_RECNO access method.\))180
484.8 Q(R_NOO)144 501.6 Q(VER)-.5 E(WRITE)-.55 E(Enter the ne)180 513.6 Q 2.5
(wk)-.25 G -.15(ey)242.69 513.6 S(/data pair only if the k).15 E .3 -.15(ey d)
-.1 H(oes not pre).15 E(viously e)-.25 E(xist.)-.15 E(R_SETCURSOR)144 530.4 Q
1.36(Store the k)180 542.4 R -.15(ey)-.1 G 1.36(/data pair).15 F 3.86(,s)-.4 G
1.359(etting or initializing the position of the cursor to reference it.)283.94
542.4 R(\(Applicable only to the DB_BTREE and DB_RECNO access methods.\))180
554.4 Q .563(R_SETCURSOR is a)144 571.2 R -.25(va)-.2 G .564
(ilable only for the DB_BTREE and DB_RECNO access methods because).25 F
(it implies that the k)144 583.2 Q -.15(ey)-.1 G 2.5(sh).15 G -2.25 -.2(av e)
241.81 583.2 T(an inherent order which does not change.)2.7 E .416
(R_IAFTER and R_IBEFORE are a)144 600 R -.25(va)-.2 G .416
(ilable only for the DB_RECNO access method because the).25 F(y)-.15 E 1.221
(each imply that the access method is able to create ne)144 612 R 3.722(wk)-.25
G -.15(ey)385.644 612 S 3.722(s. This).15 F 1.222(is only true if the k)3.722 F
-.15(ey)-.1 G 3.722(sa).15 G(re)532.23 612 Q
(ordered and independent, record numbers for e)144 624 Q(xample.)-.15 E .289
(The def)144 640.8 R .289(ault beha)-.1 F .289(vior of the)-.2 F F1(put)2.789 E
F0 .289(routines is to enter the ne)2.789 F 2.789(wk)-.25 G -.15(ey)388.998
640.8 S .288(/data pair).15 F 2.788(,r)-.4 G .288(eplacing an)444.284 640.8 R
2.788(yp)-.15 G(re)503.03 640.8 Q(viously)-.25 E -.15(ex)144 652.8 S(isting k)
.15 E -.15(ey)-.1 G(.)-.5 E F1(Put)144 669.6 Q F0 .37
(routines return -1 on error \(setting)2.87 F F1(errno)2.87 E F0 .37
(\), 0 on success, and 1 if the R_NOO).18 F(VER)-.5 E(WRITE)-.55 E F1<8d61>2.87
E(g)-.1 E F0 -.1(wa)144 681.6 S 2.5(ss).1 G(et and the k)165.84 681.6 Q .3 -.15
(ey a)-.1 H(lready e).15 E(xists in the \214le.)-.15 E(4.4 Berk)72 732 Q(ele)
-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 89.875(ution September)-.2 F(13, 1993)
2.5 E(2)535 732 Q EP
%%Page: 3 3
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.01(DBOPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.01(anual DBOPEN\(3\))340.17 48 R 20.17(seq A)108 84 R .002
(pointer to a routine which is the interf)2.502 F .002
(ace for sequential retrie)-.1 F -.25(va)-.25 G 2.502(lf).25 G .002
(rom the database.)416.694 84 R .001(The address)5.001 F .219
(and length of the k)144 96 R .519 -.15(ey a)-.1 H .219
(re returned in the structure referenced by).15 F/F1 10/Times-Italic@0 SF -.1
(ke)2.72 G(y)-.2 E F0 2.72(,a).32 G .22(nd the address and length of)426.42 96
R(the data are returned in the structure referenced by)144 108 Q F1(data)2.5 E
F0(.).26 E .937(Sequential k)144 124.8 R -.15(ey)-.1 G .937(/data pair retrie)
.15 F -.25(va)-.25 G 3.437(lm).25 G .936(ay be)289.748 124.8 R .936(gin at an)
-.15 F 3.436(yt)-.15 G .936(ime, and the position of the `)359.292 124.8 R
(`cursor')-.74 E 3.436('i)-.74 G 3.436(sn)519.894 124.8 S(ot)532.22 124.8 Q(af)
144 136.8 Q 1.585(fected by calls to the)-.25 F F1(del)4.085 E F0(,).51 E F1
-.1(ge)4.085 G(t).1 E F0(,).68 E F1(put)4.086 E F0 4.086(,o).68 G(r)308.452
136.8 Q F1(sync)4.086 E F0 4.086(routines. Modi\214cations)4.086 F 1.586
(to the database during a)4.086 F 1.404(sequential scan will be re\215ected in\
 the scan, i.e. records inserted behind the cursor will not be)144 148.8 R
(returned while records inserted in front of the cursor will be returned.)144
160.8 Q(The \215ag v)144 177.6 Q(alue)-.25 E/F2 10/Times-Bold@0 SF(must)2.5 E
F0(be set to one of the follo)2.5 E(wing v)-.25 E(alues:)-.25 E(R_CURSOR)144
194.4 Q .523(The data associated with the speci\214ed k)180 206.4 R .824 -.15
(ey i)-.1 H 3.024(sr).15 G 3.024(eturned. This)367.236 206.4 R(dif)3.024 E .524
(fers from the)-.25 F F1 -.1(ge)3.024 G(t).1 E F0(routines)3.024 E 1.143
(in that it sets or initializes the cursor to the location of the k)180 218.4 R
1.443 -.15(ey a)-.1 H 3.642(sw).15 G 3.642(ell. \(Note,)464.924 218.4 R 1.142
(for the)3.642 F 1.275(DB_BTREE access method, the returned k)180 230.4 R 1.575
-.15(ey i)-.1 H 3.775(sn).15 G 1.276(ot necessarily an e)386.425 230.4 R 1.276
(xact match for the)-.15 F .598(speci\214ed k)180 242.4 R -.15(ey)-.1 G 5.598
(.T)-.5 G .598(he returned k)246.396 242.4 R .898 -.15(ey i)-.1 H 3.098(st).15
G .598(he smallest k)325.188 242.4 R .898 -.15(ey g)-.1 H .598
(reater than or equal to the speci\214ed).15 F -.1(ke)180 254.4 S 1.3 -.65
(y, p)-.05 H(ermitting partial k).65 E .3 -.15(ey m)-.1 H
(atches and range searches.\)).15 E(R_FIRST)144 271.2 Q 1.043(The \214rst k)180
283.2 R -.15(ey)-.1 G 1.044(/data pair of the database is returned, and the cu\
rsor is set or initialized to).15 F(reference it.)180 295.2 Q(R_LAST)144 312 Q
.085(The last k)180 324 R -.15(ey)-.1 G .085(/data pair of the database is ret\
urned, and the cursor is set or initialized to ref-).15 F(erence it.)180 336 Q
(\(Applicable only to the DB_BTREE and DB_RECNO access methods.\))5 E(R_NEXT)
144 352.8 Q(Retrie)180 364.8 Q .604 -.15(ve t)-.25 H .304(he k).15 F -.15(ey)
-.1 G .304(/data pair immediately after the cursor).15 F 5.304(.I)-.55 G 2.804
(ft)410.622 364.8 S .305(he cursor is not yet set, this is)419.536 364.8 R
(the same as the R_FIRST \215ag.)180 376.8 Q(R_PREV)144 393.6 Q(Retrie)180
405.6 Q .755 -.15(ve t)-.25 H .455(he k).15 F -.15(ey)-.1 G .455
(/data pair immediately before the cursor).15 F 5.455(.I)-.55 G 2.955(ft)419.05
405.6 S .454(he cursor is not yet set, this)428.115 405.6 R .62
(is the same as the R_LAST \215ag.)180 417.6 R .621
(\(Applicable only to the DB_BTREE and DB_RECNO)5.621 F(access methods.\))180
429.6 Q .911(R_LAST and R_PREV are a)144 446.4 R -.25(va)-.2 G .911
(ilable only for the DB_BTREE and DB_RECNO access methods).25 F(because the)144
458.4 Q 2.5(ye)-.15 G(ach imply that the k)202.16 458.4 Q -.15(ey)-.1 G 2.5(sh)
.15 G -2.25 -.2(av e)302.18 458.4 T(an inherent order which does not change.)
2.7 E F1(Seq)144 475.2 Q F0 .061(routines return -1 on error \(setting)2.561 F
F1(errno)2.561 E F0 .061(\), 0 on success and 1 if there are no k).18 F -.15
(ey)-.1 G .061(/data pairs less).15 F .35
(than or greater than the speci\214ed or current k)144 487.2 R -.15(ey)-.1 G
5.349(.I)-.5 G 2.849(ft)346.467 487.2 S .349
(he DB_RECNO access method is being used,)355.426 487.2 R .025
(and if the database \214le is a character special \214le and no complete k)144
499.2 R -.15(ey)-.1 G .025(/data pairs are currently a).15 F -.25(va)-.2 G(il-)
.25 E(able, the)144 511.2 Q F1(seq)2.5 E F0(routines return 2.)2.5 E 15.17
(sync A)108 528 R .458(pointer to a routine to \215ush an)2.958 F 2.957(yc)-.15
G .457(ached information to disk.)289.72 528 R .457
(If the database is in memory only)5.457 F(,)-.65 E(the)144 540 Q F1(sync)2.5 E
F0(routine has no ef)2.5 E(fect and will al)-.25 E -.1(wa)-.1 G(ys succeed.).1
E(The \215ag v)144 556.8 Q(alue may be set to the follo)-.25 E(wing v)-.25 E
(alue:)-.25 E(R_RECNOSYNC)144 573.6 Q .077(If the DB_RECNO access method is be\
ing used, this \215ag causes the sync routine to apply)180 585.6 R .75(to the \
btree \214le which underlies the recno \214le, not the recno \214le itself.)180
597.6 R .75(\(See the)5.75 F F1(bfname)3.25 E F0(\214eld of the)180 609.6 Q F1
-.37(re)2.5 G(cno).37 E F0(\(3\) manual page for more information.\)).18 E F1
(Sync)144 626.4 Q F0(routines return -1 on error \(setting)2.5 E F1(errno)2.5 E
F0 2.5(\)a).18 G(nd 0 on success.)336.91 626.4 Q/F3 9/Times-Bold@0 SF(KEY/D)72
643.2 Q -1.35 -.855(AT A)-.315 H -.666(PA)3.105 G(IRS).666 E F0 .134
(Access to all \214le types is based on k)108 655.2 R -.15(ey)-.1 G .134
(/data pairs.).15 F .134(Both k)5.134 F -.15(ey)-.1 G 2.634(sa).15 G .134
(nd data are represented by the follo)359.078 655.2 R .135(wing data)-.25 F
(structure:)108 667.2 Q(typedef struct {)108 684 Q(4.4 Berk)72 732 Q(ele)-.1 E
2.5(yD)-.15 G(istrib)132.57 732 Q 89.875(ution September)-.2 F(13, 1993)2.5 E
(3)535 732 Q EP
%%Page: 4 4
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 124.01(DBOPEN\(3\) BSD)72 48 R(Programmer')2.5 E 2.5
(sM)-.55 G 124.01(anual DBOPEN\(3\))340.17 48 R -.2(vo)144 84 S(id *data;).2 E
(size_t size;)144 96 Q 2.5(}D)108 108 S(BT)122.52 108 Q(;)-.55 E
(The elements of the DBT structure are de\214ned as follo)108 124.8 Q(ws:)-.25
E 16.84(data A)108 141.6 R(pointer to a byte string.)2.5 E 17.95(size The)108
158.4 R(length of the byte string.)2.5 E -2.15 -.25(Ke y)108 175.2 T .829(and \
data byte strings may reference strings of essentially unlimited length althou\
gh an)3.579 F 3.328(yt)-.15 G 1.028 -.1(wo o)492.894 175.2 T 3.328(ft).1 G(hem)
522.78 175.2 Q 1.133(must \214t into a)108 187.2 R -.25(va)-.2 G 1.134
(ilable memory at the same time.).25 F 1.134
(It should be noted that the access methods pro)6.134 F 1.134(vide no)-.15 F
(guarantees about byte string alignment.)108 199.2 Q/F1 9/Times-Bold@0 SF(ERR)
72 216 Q(ORS)-.27 E F0(The)108 228 Q/F2 10/Times-Italic@0 SF(dbopen)3.389 E F0
.889(routine may f)3.389 F .889(ail and set)-.1 F F2(errno)3.388 E F0 .888
(for an)3.388 F 3.388(yo)-.15 G 3.388(ft)324.376 228 S .888
(he errors speci\214ed for the library routines)333.874 228 R F2(open)3.388 E
F0(\(2\)).24 E(and)108 240 Q F2(malloc)2.5 E F0(\(3\) or the follo).31 E(wing:)
-.25 E([EFTYPE])108 256.8 Q 2.5<418c>144 268.8 S(le is incorrectly formatted.)
159.28 268.8 Q([EINV)108 285.6 Q(AL])-1.35 E 2.812(Ap)144 297.6 S .313(aramete\
r has been speci\214ed \(hash function, pad byte etc.\) that is incompatible w\
ith the current)159.032 297.6 R .406
(\214le speci\214cation or which is not meaningful for the function \(for e)144
309.6 R .405(xample, use of the cursor with-)-.15 F .099
(out prior initialization\) or there is a mismatch between the v)144 321.6 R .1
(ersion number of \214le and the softw)-.15 F(are.)-.1 E(The)108 338.4 Q F2
(close)3.469 E F0 .969(routines may f)3.469 F .969(ail and set)-.1 F F2(errno)
3.469 E F0 .969(for an)3.469 F 3.469(yo)-.15 G 3.469(ft)320.18 338.4 S .969
(he errors speci\214ed for the library routines)329.759 338.4 R F2(close)3.468
E F0(\(2\),).18 E F2 -.37(re)108 350.4 S(ad).37 E F0(\(2\),).77 E F2(write)2.5
E F0(\(2\),).18 E F2(fr)2.5 E(ee)-.37 E F0(\(3\), or).18 E F2(fsync)2.5 E F0
(\(2\).).31 E(The)108 367.2 Q F2(del)2.969 E F0(,).51 E F2 -.1(ge)2.969 G(t).1
E F0(,).68 E F2(put)2.969 E F0(and)2.969 E F2(seq)2.969 E F0 .469
(routines may f)2.969 F .469(ail and set)-.1 F F2(errno)2.97 E F0 .47(for an)
2.97 F 2.97(yo)-.15 G 2.97(ft)377.59 367.2 S .47
(he errors speci\214ed for the library rou-)386.67 367.2 R(tines)108 379.2 Q F2
-.37(re)2.5 G(ad).37 E F0(\(2\),).77 E F2(write)2.5 E F0(\(2\),).18 E F2(fr)2.5
E(ee)-.37 E F0(\(3\) or).18 E F2(malloc)2.5 E F0(\(3\).).31 E(The)108 396 Q F2
(fd)2.5 E F0(routines will f)2.5 E(ail and set)-.1 E F2(errno)2.5 E F0
(to ENOENT for in memory databases.)2.5 E(The)108 412.8 Q F2(sync)2.5 E F0
(routines may f)2.5 E(ail and set)-.1 E F2(errno)2.5 E F0(for an)2.5 E 2.5(yo)
-.15 G 2.5(ft)307.71 412.8 S(he errors speci\214ed for the library routine)
316.32 412.8 Q F2(fsync)2.5 E F0(\(2\).).31 E F1(SEE ALSO)72 429.6 Q F2(btr)108
441.6 Q(ee)-.37 E F0(\(3\),).18 E F2(hash)2.5 E F0(\(3\),).28 E F2(mpool)2.5 E
F0(\(3\),).51 E F2 -.37(re)2.5 G(cno).37 E F0(\(3\)).18 E F2 .904(LIBTP: P)108
465.6 R(ortable)-.8 E 3.404(,M)-.1 G .904(odular T)189.738 465.6 R -.15(ra)-.55
G .904(nsactions for UNIX).15 F F0 3.404(,M).94 G(ar)328.884 465.6 Q .904
(go Seltzer)-.18 F 3.403(,M)-.4 G .903(ichael Olson, USENIX proceedings,)
392.041 465.6 R -.4(Wi)108 477.6 S(nter 1992.).4 E F1 -.09(BU)72 494.4 S(GS).09
E F0 .399(The typedef DBT is a mnemonic for `)108 506.4 R .399
(`data base thang')-.74 F .399(', and w)-.74 F .399
(as used because noone could think of a rea-)-.1 F(sonable name that w)108
518.4 Q(asn')-.1 E 2.5(ta)-.18 G(lready used.)216.03 518.4 Q
(The \214le descriptor interf)108 535.2 Q
(ace is a kluge and will be deleted in a future v)-.1 E(ersion of the interf)
-.15 E(ace.)-.1 E(None of the access methods pro)108 552 Q(vide an)-.15 E 2.5
(yf)-.15 G(orm of concurrent access, locking, or transactions.)275.16 552 Q
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 89.875
(ution September)-.2 F(13, 1993)2.5 E(4)535 732 Q EP
%%Trailer
end
%%EOF
