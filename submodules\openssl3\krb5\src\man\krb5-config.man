.\" Man page generated from reStructuredText.
.
.TH "KRB5-CONFIG" "1" " " "1.20" "MIT Kerberos"
.SH NAME
krb5-config \- tool for linking against MIT Kerberos libraries
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkrb5\-config\fP
[\fB\-\fP\fB\-help\fP | \fB\-\fP\fB\-all\fP | \fB\-\fP\fB\-version\fP | \fB\-\fP\fB\-vendor\fP | \fB\-\fP\fB\-prefix\fP | \fB\-\fP\fB\-exec\-prefix\fP | \fB\-\fP\fB\-defccname\fP | \fB\-\fP\fB\-defktname\fP | \fB\-\fP\fB\-defcktname\fP | \fB\-\fP\fB\-cflags\fP | \fB\-\fP\fB\-libs\fP [\fIlibraries\fP]]
.SH DESCRIPTION
.sp
krb5\-config tells the application programmer what flags to use to compile
and link programs against the installed Kerberos libraries.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-\fP\fB\-help\fP
prints a usage message.  This is the default behavior when no options
are specified.
.TP
\fB\-\fP\fB\-all\fP
prints the version, vendor, prefix, and exec\-prefix.
.TP
\fB\-\fP\fB\-version\fP
prints the version number of the Kerberos installation.
.TP
\fB\-\fP\fB\-vendor\fP
prints the name of the vendor of the Kerberos installation.
.TP
\fB\-\fP\fB\-prefix\fP
prints the prefix for which the Kerberos installation was built.
.TP
\fB\-\fP\fB\-exec\-prefix\fP
prints the prefix for executables for which the Kerberos installation
was built.
.TP
\fB\-\fP\fB\-defccname\fP
prints the built\-in default credentials cache location.
.TP
\fB\-\fP\fB\-defktname\fP
prints the built\-in default keytab location.
.TP
\fB\-\fP\fB\-defcktname\fP
prints the built\-in default client (initiator) keytab location.
.TP
\fB\-\fP\fB\-cflags\fP
prints the compilation flags used to build the Kerberos installation.
.TP
\fB\-\fP\fB\-libs\fP [\fIlibrary\fP]
prints the compiler options needed to link against \fIlibrary\fP\&.
Allowed values for \fIlibrary\fP are:
.TS
center;
|l|l|.
_
T{
krb5
T}	T{
Kerberos 5 applications (default)
T}
_
T{
gssapi
T}	T{
GSSAPI applications with Kerberos 5 bindings
T}
_
T{
kadm\-client
T}	T{
Kadmin client
T}
_
T{
kadm\-server
T}	T{
Kadmin server
T}
_
T{
kdb
T}	T{
Applications that access the Kerberos database
T}
_
.TE
.UNINDENT
.SH EXAMPLES
.sp
krb5\-config is particularly useful for compiling against a Kerberos
installation that was installed in a non\-standard location.  For example,
a Kerberos installation that is installed in \fB/opt/krb5/\fP but uses
libraries in \fB/usr/local/lib/\fP for text localization would produce
the following output:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
shell% krb5\-config \-\-libs krb5
\-L/opt/krb5/lib \-Wl,\-rpath \-Wl,/opt/krb5/lib \-L/usr/local/lib \-lkrb5 \-lk5crypto \-lcom_err
.ft P
.fi
.UNINDENT
.UNINDENT
.SH SEE ALSO
.sp
kerberos(7), cc(1)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
