=pod

=head1 NAME

CMS_EncryptedData_decrypt
- Decrypt CMS EncryptedData

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int CMS_EncryptedData_decrypt(CMS_ContentInfo *cms,
                               const unsigned char *key, size_t keylen,
                               BIO *dcont, BIO *out, unsigned int flags);

=head1 DESCRIPTION

CMS_EncryptedData_decrypt() decrypts a I<cms> EncryptedData object using the
symmetric I<key> of size I<keylen> bytes. I<out> is a BIO to write the content
to and I<flags> is an optional set of flags.
I<dcont> is used in the rare case where the encrypted content is detached. It
will normally be set to NULL.

The following flags can be passed in the I<flags> parameter.

If the B<CMS_TEXT> flag is set MIME headers for type C<text/plain> are deleted
from the content. If the content is not of type C<text/plain> then an error is
returned.

=head1 RETURN VALUES

CMS_EncryptedData_decrypt() returns 0 if an error occurred otherwise it
returns 1.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_EncryptedData_encrypt(3)>


=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
