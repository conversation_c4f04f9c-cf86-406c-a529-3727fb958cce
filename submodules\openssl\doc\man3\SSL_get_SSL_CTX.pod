=pod

=head1 NAME

SSL_get_SSL_CTX - get the SSL_CTX from which an SSL is created

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 SSL_CTX *SSL_get_SSL_CTX(const SSL *ssl);

=head1 DESCRIPTION

SSL_get_SSL_CTX() returns a pointer to the SSL_CTX object, from which
B<ssl> was created with L<SSL_new(3)>.

=head1 RETURN VALUES

The pointer to the SSL_CTX object is returned.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_new(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
