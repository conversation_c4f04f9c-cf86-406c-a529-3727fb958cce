/* ccapi/server/ccs_common.h */
/*
 * Copyright 2006 Massachusetts Institute of Technology.
 * All Rights Reserved.
 *
 * Export of this software from the United States of America may
 * require a specific license from the United States Government.
 * It is the responsibility of any person or organization contemplating
 * export to obtain such a license before exporting.
 *
 * WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 * distribute this software and its documentation for any purpose and
 * without fee is hereby granted, provided that the above copyright
 * notice appear in all copies and that both that copyright notice and
 * this permission notice appear in supporting documentation, and that
 * the name of M.I.T. not be used in advertising or publicity pertaining
 * to distribution of the software without specific, written prior
 * permission.  Furthermore if you modify this software you must label
 * your software as modified software and not distribute it in such a
 * fashion that it might be confused with the original M.I.T. software.
 * M.I.T. makes no representations about the suitability of
 * this software for any purpose.  It is provided "as is" without express
 * or implied warranty.
 */

#ifndef CCS_COMMON_H
#define CCS_COMMON_H

#include "cci_common.h"

#include <time.h>

#include "ccs_array.h"
#include "ccs_list.h"
#include "ccs_cache_collection.h"
#include "ccs_ccache_iterator.h"
#include "ccs_ccache.h"
#include "ccs_credentials_iterator.h"
#include "ccs_credentials.h"
#include "ccs_lock.h"
#include "ccs_lock_state.h"
#include "ccs_pipe.h"
#include "ccs_client.h"
#include "ccs_callback.h"
#include "ccs_server.h"

#endif /* CCS_COMMON_H */
