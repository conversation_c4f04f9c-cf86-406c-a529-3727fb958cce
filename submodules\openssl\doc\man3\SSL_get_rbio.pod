=pod

=head1 NAME

SSL_get_rbio, SSL_get_wbio - get BIO linked to an SSL object

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 BIO *SSL_get_rbio(SSL *ssl);
 BIO *SSL_get_wbio(SSL *ssl);

=head1 DESCRIPTION

SSL_get_rbio() and SSL_get_wbio() return pointers to the BIOs for the
read or the write channel, which can be different. The reference count
of the BIO is not incremented.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item NULL

No BIO was connected to the SSL object

=item Any other pointer

The BIO linked to B<ssl>.

=back

=head1 SEE ALSO

L<SSL_set_bio(3)>, L<ssl(7)> , L<bio(7)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
