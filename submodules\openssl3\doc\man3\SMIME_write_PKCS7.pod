=pod

=head1 NAME

SMIME_write_PKCS7 - convert PKCS#7 structure to S/MIME format

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 int SMIME_write_PKCS7(BIO *out, PKCS7 *p7, BIO *data, int flags);

=head1 DESCRIPTION

SMIME_write_PKCS7() adds the appropriate MIME headers to a PKCS#7
structure to produce an S/MIME message.

B<out> is the BIO to write the data to. B<p7> is the appropriate B<PKCS7>
structure. If streaming is enabled then the content must be supplied in the
B<data> argument. B<flags> is an optional set of flags.

=head1 NOTES

The following flags can be passed in the B<flags> parameter.

If B<PKCS7_DETACHED> is set then cleartext signing will be used,
this option only makes sense for signedData where B<PKCS7_DETACHED>
is also set when PKCS7_sign() is also called.

If the B<PKCS7_TEXT> flag is set MIME headers for type B<text/plain>
are added to the content, this only makes sense if B<PKCS7_DETACHED>
is also set.

If the B<PKCS7_STREAM> flag is set streaming is performed. This flag should
only be set if B<PKCS7_STREAM> was also set in the previous call to
PKCS7_sign() or PKCS7_encrypt().

If cleartext signing is being used and B<PKCS7_STREAM> not set then
the data must be read twice: once to compute the signature in PKCS7_sign()
and once to output the S/MIME message.

If streaming is performed the content is output in BER format using indefinite
length constructed encoding except in the case of signed data with detached
content where the content is absent and DER format is used.

=head1 BUGS

SMIME_write_PKCS7() always base64 encodes PKCS#7 structures, there
should be an option to disable this.

=head1 RETURN VALUES

SMIME_write_PKCS7() returns 1 for success or 0 for failure.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<PKCS7_sign(3)>,
L<PKCS7_verify(3)>, L<PKCS7_encrypt(3)>
L<PKCS7_decrypt(3)>

=head1 COPYRIGHT

Copyright 2002-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
