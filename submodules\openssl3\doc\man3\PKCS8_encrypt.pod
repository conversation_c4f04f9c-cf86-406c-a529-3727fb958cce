=pod

=head1 NAME

PKCS8_decrypt, PKCS8_decrypt_ex, PKCS8_encrypt, PKCS8_encrypt_ex,
PKCS8_set0_pbe, PKCS8_set0_pbe_ex - PKCS8 encrypt/decrypt functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 PKCS8_PRIV_KEY_INFO *PKCS8_decrypt(const X509_SIG *p8, const char *pass,
                                    int passlen);
 PKCS8_PRIV_KEY_INFO *PKCS8_decrypt_ex(const X509_SIG *p8, const char *pass,
                                       int passlen, OSSL_LIB_CTX *ctx,
                                       const char *propq);
 X509_SIG *PKCS8_encrypt(int pbe_nid, const EVP_CIPHER *cipher,
                         const char *pass, int passlen, unsigned char *salt,
                         int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8);
 X509_SIG *PKCS8_encrypt_ex(int pbe_nid, const EVP_CIPHER *cipher,
                            const char *pass, int passlen, unsigned char *salt,
                            int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8,
                            OSSL_LIB_CTX *ctx, const char *propq);
 X509_SIG *PKCS8_set0_pbe(const char *pass, int passlen,
                         PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe);
 X509_SIG *PKCS8_set0_pbe_ex(const char *pass, int passlen,
                             PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe,
                             OSSL_LIB_CTX *ctx);

=head1 DESCRIPTION

PKCS8_encrypt() and PKCS8_encrypt_ex() perform encryption of an object I<p8> using
the password I<pass> of length I<passlen>, salt I<salt> of length I<saltlen>
and iteration count I<iter>.
The resulting B<X509_SIG> contains the encoded algorithm parameters and encrypted
key.

PKCS8_decrypt() and PKCS8_decrypt_ex() perform decryption of an B<X509_SIG> in
I<p8> using the password I<pass> of length I<passlen> along with algorithm
parameters obtained from the I<p8>.

PKCS8_set0_pbe() and PKCS8_set0_pbe_ex() perform encryption of the I<p8inf>
using the password I<pass> of length I<passlen> and parameters I<pbe>.

Functions ending in _ex() allow for a library context I<ctx> and property query
I<propq> to be used to select algorithm implementations.

=head1 RETURN VALUES

PKCS8_encrypt(), PKCS8_encrypt_ex(), PKCS8_set0_pbe() and PKCS8_set0_pbe_ex()
return an encrypted key in a B<X509_SIG> structure or NULL if an error occurs.

PKCS8_decrypt() and PKCS8_decrypt_ex() return a B<PKCS8_PRIV_KEY_INFO> or NULL
if an error occurs.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<crypto(7)>

=head1 HISTORY

PKCS8_decrypt_ex(), PKCS8_encrypt_ex() and PKCS8_set0_pbe_ex() were added in
OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
