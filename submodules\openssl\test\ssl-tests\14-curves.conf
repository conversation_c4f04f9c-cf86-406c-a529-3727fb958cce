# Generated with generate_ssl_tests.pl

num_tests = 30

test-0 = 0-curve-sect163k1
test-1 = 1-curve-sect163r1
test-2 = 2-curve-sect163r2
test-3 = 3-curve-sect193r1
test-4 = 4-curve-sect193r2
test-5 = 5-curve-sect233k1
test-6 = 6-curve-sect233r1
test-7 = 7-curve-sect239k1
test-8 = 8-curve-sect283k1
test-9 = 9-curve-sect283r1
test-10 = 10-curve-sect409k1
test-11 = 11-curve-sect409r1
test-12 = 12-curve-sect571k1
test-13 = 13-curve-sect571r1
test-14 = 14-curve-secp160k1
test-15 = 15-curve-secp160r1
test-16 = 16-curve-secp160r2
test-17 = 17-curve-secp192k1
test-18 = 18-curve-prime192v1
test-19 = 19-curve-secp224k1
test-20 = 20-curve-secp224r1
test-21 = 21-curve-secp256k1
test-22 = 22-curve-prime256v1
test-23 = 23-curve-secp384r1
test-24 = 24-curve-secp521r1
test-25 = 25-curve-brainpoolP256r1
test-26 = 26-curve-brainpoolP384r1
test-27 = 27-curve-brainpoolP512r1
test-28 = 28-curve-X25519
test-29 = 29-curve-X448
# ===========================================================

[0-curve-sect163k1]
ssl_conf = 0-curve-sect163k1-ssl

[0-curve-sect163k1-ssl]
server = 0-curve-sect163k1-server
client = 0-curve-sect163k1-client

[0-curve-sect163k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect163k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-curve-sect163k1-client]
CipherString = ECDHE
Curves = sect163k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
ExpectedTmpKeyType = sect163k1


# ===========================================================

[1-curve-sect163r1]
ssl_conf = 1-curve-sect163r1-ssl

[1-curve-sect163r1-ssl]
server = 1-curve-sect163r1-server
client = 1-curve-sect163r1-client

[1-curve-sect163r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect163r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-curve-sect163r1-client]
CipherString = ECDHE
Curves = sect163r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
ExpectedTmpKeyType = sect163r1


# ===========================================================

[2-curve-sect163r2]
ssl_conf = 2-curve-sect163r2-ssl

[2-curve-sect163r2-ssl]
server = 2-curve-sect163r2-server
client = 2-curve-sect163r2-client

[2-curve-sect163r2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect163r2
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-curve-sect163r2-client]
CipherString = ECDHE
Curves = sect163r2
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
ExpectedTmpKeyType = sect163r2


# ===========================================================

[3-curve-sect193r1]
ssl_conf = 3-curve-sect193r1-ssl

[3-curve-sect193r1-ssl]
server = 3-curve-sect193r1-server
client = 3-curve-sect193r1-client

[3-curve-sect193r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect193r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-curve-sect193r1-client]
CipherString = ECDHE
Curves = sect193r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
ExpectedTmpKeyType = sect193r1


# ===========================================================

[4-curve-sect193r2]
ssl_conf = 4-curve-sect193r2-ssl

[4-curve-sect193r2-ssl]
server = 4-curve-sect193r2-server
client = 4-curve-sect193r2-client

[4-curve-sect193r2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect193r2
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-curve-sect193r2-client]
CipherString = ECDHE
Curves = sect193r2
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
ExpectedTmpKeyType = sect193r2


# ===========================================================

[5-curve-sect233k1]
ssl_conf = 5-curve-sect233k1-ssl

[5-curve-sect233k1-ssl]
server = 5-curve-sect233k1-server
client = 5-curve-sect233k1-client

[5-curve-sect233k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect233k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-curve-sect233k1-client]
CipherString = ECDHE
Curves = sect233k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
ExpectedTmpKeyType = sect233k1


# ===========================================================

[6-curve-sect233r1]
ssl_conf = 6-curve-sect233r1-ssl

[6-curve-sect233r1-ssl]
server = 6-curve-sect233r1-server
client = 6-curve-sect233r1-client

[6-curve-sect233r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect233r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-curve-sect233r1-client]
CipherString = ECDHE
Curves = sect233r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
ExpectedTmpKeyType = sect233r1


# ===========================================================

[7-curve-sect239k1]
ssl_conf = 7-curve-sect239k1-ssl

[7-curve-sect239k1-ssl]
server = 7-curve-sect239k1-server
client = 7-curve-sect239k1-client

[7-curve-sect239k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect239k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-curve-sect239k1-client]
CipherString = ECDHE
Curves = sect239k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success
ExpectedTmpKeyType = sect239k1


# ===========================================================

[8-curve-sect283k1]
ssl_conf = 8-curve-sect283k1-ssl

[8-curve-sect283k1-ssl]
server = 8-curve-sect283k1-server
client = 8-curve-sect283k1-client

[8-curve-sect283k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect283k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-curve-sect283k1-client]
CipherString = ECDHE
Curves = sect283k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success
ExpectedTmpKeyType = sect283k1


# ===========================================================

[9-curve-sect283r1]
ssl_conf = 9-curve-sect283r1-ssl

[9-curve-sect283r1-ssl]
server = 9-curve-sect283r1-server
client = 9-curve-sect283r1-client

[9-curve-sect283r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect283r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-curve-sect283r1-client]
CipherString = ECDHE
Curves = sect283r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = Success
ExpectedTmpKeyType = sect283r1


# ===========================================================

[10-curve-sect409k1]
ssl_conf = 10-curve-sect409k1-ssl

[10-curve-sect409k1-ssl]
server = 10-curve-sect409k1-server
client = 10-curve-sect409k1-client

[10-curve-sect409k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect409k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-curve-sect409k1-client]
CipherString = ECDHE
Curves = sect409k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = Success
ExpectedTmpKeyType = sect409k1


# ===========================================================

[11-curve-sect409r1]
ssl_conf = 11-curve-sect409r1-ssl

[11-curve-sect409r1-ssl]
server = 11-curve-sect409r1-server
client = 11-curve-sect409r1-client

[11-curve-sect409r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect409r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-curve-sect409r1-client]
CipherString = ECDHE
Curves = sect409r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = Success
ExpectedTmpKeyType = sect409r1


# ===========================================================

[12-curve-sect571k1]
ssl_conf = 12-curve-sect571k1-ssl

[12-curve-sect571k1-ssl]
server = 12-curve-sect571k1-server
client = 12-curve-sect571k1-client

[12-curve-sect571k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect571k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-curve-sect571k1-client]
CipherString = ECDHE
Curves = sect571k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = Success
ExpectedTmpKeyType = sect571k1


# ===========================================================

[13-curve-sect571r1]
ssl_conf = 13-curve-sect571r1-ssl

[13-curve-sect571r1-ssl]
server = 13-curve-sect571r1-server
client = 13-curve-sect571r1-client

[13-curve-sect571r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = sect571r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-curve-sect571r1-client]
CipherString = ECDHE
Curves = sect571r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = Success
ExpectedTmpKeyType = sect571r1


# ===========================================================

[14-curve-secp160k1]
ssl_conf = 14-curve-secp160k1-ssl

[14-curve-secp160k1-ssl]
server = 14-curve-secp160k1-server
client = 14-curve-secp160k1-client

[14-curve-secp160k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp160k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-curve-secp160k1-client]
CipherString = ECDHE
Curves = secp160k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedResult = Success
ExpectedTmpKeyType = secp160k1


# ===========================================================

[15-curve-secp160r1]
ssl_conf = 15-curve-secp160r1-ssl

[15-curve-secp160r1-ssl]
server = 15-curve-secp160r1-server
client = 15-curve-secp160r1-client

[15-curve-secp160r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp160r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-curve-secp160r1-client]
CipherString = ECDHE
Curves = secp160r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = Success
ExpectedTmpKeyType = secp160r1


# ===========================================================

[16-curve-secp160r2]
ssl_conf = 16-curve-secp160r2-ssl

[16-curve-secp160r2-ssl]
server = 16-curve-secp160r2-server
client = 16-curve-secp160r2-client

[16-curve-secp160r2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp160r2
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-curve-secp160r2-client]
CipherString = ECDHE
Curves = secp160r2
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedResult = Success
ExpectedTmpKeyType = secp160r2


# ===========================================================

[17-curve-secp192k1]
ssl_conf = 17-curve-secp192k1-ssl

[17-curve-secp192k1-ssl]
server = 17-curve-secp192k1-server
client = 17-curve-secp192k1-client

[17-curve-secp192k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp192k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-curve-secp192k1-client]
CipherString = ECDHE
Curves = secp192k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedResult = Success
ExpectedTmpKeyType = secp192k1


# ===========================================================

[18-curve-prime192v1]
ssl_conf = 18-curve-prime192v1-ssl

[18-curve-prime192v1-ssl]
server = 18-curve-prime192v1-server
client = 18-curve-prime192v1-client

[18-curve-prime192v1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = prime192v1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-curve-prime192v1-client]
CipherString = ECDHE
Curves = prime192v1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedResult = Success
ExpectedTmpKeyType = prime192v1


# ===========================================================

[19-curve-secp224k1]
ssl_conf = 19-curve-secp224k1-ssl

[19-curve-secp224k1-ssl]
server = 19-curve-secp224k1-server
client = 19-curve-secp224k1-client

[19-curve-secp224k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp224k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-curve-secp224k1-client]
CipherString = ECDHE
Curves = secp224k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedResult = Success
ExpectedTmpKeyType = secp224k1


# ===========================================================

[20-curve-secp224r1]
ssl_conf = 20-curve-secp224r1-ssl

[20-curve-secp224r1-ssl]
server = 20-curve-secp224r1-server
client = 20-curve-secp224r1-client

[20-curve-secp224r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp224r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-curve-secp224r1-client]
CipherString = ECDHE
Curves = secp224r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedResult = Success
ExpectedTmpKeyType = secp224r1


# ===========================================================

[21-curve-secp256k1]
ssl_conf = 21-curve-secp256k1-ssl

[21-curve-secp256k1-ssl]
server = 21-curve-secp256k1-server
client = 21-curve-secp256k1-client

[21-curve-secp256k1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp256k1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-curve-secp256k1-client]
CipherString = ECDHE
Curves = secp256k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedResult = Success
ExpectedTmpKeyType = secp256k1


# ===========================================================

[22-curve-prime256v1]
ssl_conf = 22-curve-prime256v1-ssl

[22-curve-prime256v1-ssl]
server = 22-curve-prime256v1-server
client = 22-curve-prime256v1-client

[22-curve-prime256v1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = prime256v1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[22-curve-prime256v1-client]
CipherString = ECDHE
Curves = prime256v1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedResult = Success
ExpectedTmpKeyType = prime256v1


# ===========================================================

[23-curve-secp384r1]
ssl_conf = 23-curve-secp384r1-ssl

[23-curve-secp384r1-ssl]
server = 23-curve-secp384r1-server
client = 23-curve-secp384r1-client

[23-curve-secp384r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp384r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-curve-secp384r1-client]
CipherString = ECDHE
Curves = secp384r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedResult = Success
ExpectedTmpKeyType = secp384r1


# ===========================================================

[24-curve-secp521r1]
ssl_conf = 24-curve-secp521r1-ssl

[24-curve-secp521r1-ssl]
server = 24-curve-secp521r1-server
client = 24-curve-secp521r1-client

[24-curve-secp521r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = secp521r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-curve-secp521r1-client]
CipherString = ECDHE
Curves = secp521r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedResult = Success
ExpectedTmpKeyType = secp521r1


# ===========================================================

[25-curve-brainpoolP256r1]
ssl_conf = 25-curve-brainpoolP256r1-ssl

[25-curve-brainpoolP256r1-ssl]
server = 25-curve-brainpoolP256r1-server
client = 25-curve-brainpoolP256r1-client

[25-curve-brainpoolP256r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = brainpoolP256r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-curve-brainpoolP256r1-client]
CipherString = ECDHE
Curves = brainpoolP256r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedResult = Success
ExpectedTmpKeyType = brainpoolP256r1


# ===========================================================

[26-curve-brainpoolP384r1]
ssl_conf = 26-curve-brainpoolP384r1-ssl

[26-curve-brainpoolP384r1-ssl]
server = 26-curve-brainpoolP384r1-server
client = 26-curve-brainpoolP384r1-client

[26-curve-brainpoolP384r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = brainpoolP384r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[26-curve-brainpoolP384r1-client]
CipherString = ECDHE
Curves = brainpoolP384r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedResult = Success
ExpectedTmpKeyType = brainpoolP384r1


# ===========================================================

[27-curve-brainpoolP512r1]
ssl_conf = 27-curve-brainpoolP512r1-ssl

[27-curve-brainpoolP512r1-ssl]
server = 27-curve-brainpoolP512r1-server
client = 27-curve-brainpoolP512r1-client

[27-curve-brainpoolP512r1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = brainpoolP512r1
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[27-curve-brainpoolP512r1-client]
CipherString = ECDHE
Curves = brainpoolP512r1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedResult = Success
ExpectedTmpKeyType = brainpoolP512r1


# ===========================================================

[28-curve-X25519]
ssl_conf = 28-curve-X25519-ssl

[28-curve-X25519-ssl]
server = 28-curve-X25519-server
client = 28-curve-X25519-client

[28-curve-X25519-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = X25519
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-curve-X25519-client]
CipherString = ECDHE
Curves = X25519
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedResult = Success
ExpectedTmpKeyType = X25519


# ===========================================================

[29-curve-X448]
ssl_conf = 29-curve-X448-ssl

[29-curve-X448-ssl]
server = 29-curve-X448-server
client = 29-curve-X448-client

[29-curve-X448-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = X448
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[29-curve-X448-client]
CipherString = ECDHE
Curves = X448
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedResult = Success
ExpectedTmpKeyType = X448


