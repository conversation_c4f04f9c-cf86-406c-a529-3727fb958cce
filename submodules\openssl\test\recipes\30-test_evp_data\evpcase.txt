#
# Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line.  Lines starting with a pound sign,
# like this prolog, are ignored.

# These tests exercise the case insensitive handling of object names.
# They are contrived

Title = Case insensitive AES tests

Cipher = Aes-128-eCb
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3AD77BB40D7A3660A89ECAF32466EF97

Cipher = AeS-128-cbC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 73BED6B8E3C1743B7116E69E22229516
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 3FF1CAA1681FAC09120ECA307586E1A7

Cipher = aES-128-CTR
Key = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = E4095D4FB7A7B3792D6175A3261311B8

Cipher = AES-128-GcM
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = ab6e47d42cec13bdf53a67b21257bddf
Plaintext = 00000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78

Title = Case insensitive digest tests

Digest = Sha3-256
Input = ""
Output = A7FFC6F8BF1ED76651C14756A061D662F580FF4DE43B49FA82D80A4B80F8434A

Digest = shA512
Input = "abc"
Output = ddaf35a193617abacc417349ae20413112e6fa4e89a97ea20a9eeee64b55d39a2192992a274fc1a836ba3c23a3feebbd454d4423643ce80e2a9ac94fa54ca49f
