#
# lib/krb5/error_tables/krb5_err.et
#
# Copyright 1989,1990,1991,2007 by the Massachusetts Institute of Technology.
# All Rights Reserved.
#
# Export of this software from the United States of America may
#   require a specific license from the United States Government.
#   It is the responsibility of any person or organization contemplating
#   export to obtain such a license before exporting.
# 
# WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
# distribute this software and its documentation for any purpose and
# without fee is hereby granted, provided that the above copyright
# notice appear in all copies and that both that copyright notice and
# this permission notice appear in supporting documentation, and that
# the name of M.I.T. not be used in advertising or publicity pertaining
# to distribution of the software without specific, written prior
# permission.  Furthermore if you modify this software you must label
# your software as modified software and not distribute it in such a
# fashion that it might be confused with the original M.I.T. software.
# M.I.T. makes no representations about the suitability of
# this software for any purpose.  It is provided "as is" without express
# or implied warranty.
# 
#
# The Kerberos v5 library error code table.
# Protocol error codes are ERROR_TABLE_BASE_krb5 + the protocol error
# code number; other error codes start at ERROR_TABLE_BASE_krb5 + 128.
#
error_table krb5


#
# Note: the first 128 error codes are hard-coded and must match the error
# numbers defined in the Kerberos protocol specification, RFC-1510 or
# its successors.
#

# vv 0
error_code KRB5KDC_ERR_NONE,		"No error"
error_code KRB5KDC_ERR_NAME_EXP,	"Client's entry in database has expired"
error_code KRB5KDC_ERR_SERVICE_EXP,	"Server's entry in database has expired"
error_code KRB5KDC_ERR_BAD_PVNO,	"Requested protocol version not supported"
error_code KRB5KDC_ERR_C_OLD_MAST_KVNO,	"Client's key is encrypted in an old master key"
error_code KRB5KDC_ERR_S_OLD_MAST_KVNO,	"Server's key is encrypted in an old master key"
error_code KRB5KDC_ERR_C_PRINCIPAL_UNKNOWN, "Client not found in Kerberos database"
error_code KRB5KDC_ERR_S_PRINCIPAL_UNKNOWN, "Server not found in Kerberos database"
error_code KRB5KDC_ERR_PRINCIPAL_NOT_UNIQUE, "Principal has multiple entries in Kerberos database"
error_code KRB5KDC_ERR_NULL_KEY,	"Client or server has a null key"
error_code KRB5KDC_ERR_CANNOT_POSTDATE,	"Ticket is ineligible for postdating"
error_code KRB5KDC_ERR_NEVER_VALID,	"Requested effective lifetime is negative or too short"
error_code KRB5KDC_ERR_POLICY,		"KDC policy rejects request"
error_code KRB5KDC_ERR_BADOPTION,	"KDC can't fulfill requested option"
error_code KRB5KDC_ERR_ETYPE_NOSUPP,	"KDC has no support for encryption type"
error_code KRB5KDC_ERR_SUMTYPE_NOSUPP,	"KDC has no support for checksum type"
error_code KRB5KDC_ERR_PADATA_TYPE_NOSUPP, "KDC has no support for padata type"
error_code KRB5KDC_ERR_TRTYPE_NOSUPP,	"KDC has no support for transited type"
error_code KRB5KDC_ERR_CLIENT_REVOKED,	"Client's credentials have been revoked"
error_code KRB5KDC_ERR_SERVICE_REVOKED,	"Credentials for server have been revoked"
error_code KRB5KDC_ERR_TGT_REVOKED,	"TGT has been revoked"
error_code KRB5KDC_ERR_CLIENT_NOTYET,	"Client not yet valid - try again later"
error_code KRB5KDC_ERR_SERVICE_NOTYET,	"Server not yet valid - try again later"
error_code KRB5KDC_ERR_KEY_EXP,  	"Password has expired"
error_code KRB5KDC_ERR_PREAUTH_FAILED,  "Preauthentication failed"
# ^^ 24
error_code KRB5KDC_ERR_PREAUTH_REQUIRED, "Additional pre-authentication required"
error_code KRB5KDC_ERR_SERVER_NOMATCH,	"Requested server and ticket don't match"
error_code KRB5KDC_ERR_MUST_USE_USER2USER,  "Server principal valid for user2user only"
error_code KRB5KDC_ERR_PATH_NOT_ACCEPTED,   "KDC policy rejects transited path"
error_code KRB5KDC_ERR_SVC_UNAVAILABLE, "A service is not available that is required to process the request"
error_code KRB5PLACEHOLD_30,		"KRB5 error code 30"
# vv 31
error_code KRB5KRB_AP_ERR_BAD_INTEGRITY, "Decrypt integrity check failed"
error_code KRB5KRB_AP_ERR_TKT_EXPIRED,	"Ticket expired"
error_code KRB5KRB_AP_ERR_TKT_NYV,	"Ticket not yet valid"
error_code KRB5KRB_AP_ERR_REPEAT,	"Request is a replay"
error_code KRB5KRB_AP_ERR_NOT_US,	"The ticket isn't for us"
error_code KRB5KRB_AP_ERR_BADMATCH,	"Ticket/authenticator don't match"
error_code KRB5KRB_AP_ERR_SKEW,	"Clock skew too great"
error_code KRB5KRB_AP_ERR_BADADDR,	"Incorrect net address"
error_code KRB5KRB_AP_ERR_BADVERSION,	"Protocol version mismatch"
error_code KRB5KRB_AP_ERR_MSG_TYPE,	"Invalid message type"
error_code KRB5KRB_AP_ERR_MODIFIED,	"Message stream modified"
error_code KRB5KRB_AP_ERR_BADORDER,	"Message out of order"
error_code KRB5KRB_AP_ERR_ILL_CR_TKT, "Illegal cross-realm ticket"
error_code KRB5KRB_AP_ERR_BADKEYVER,	"Key version is not available"
error_code KRB5KRB_AP_ERR_NOKEY,	"Service key not available"
error_code KRB5KRB_AP_ERR_MUT_FAIL,	"Mutual authentication failed"
error_code KRB5KRB_AP_ERR_BADDIRECTION,	"Incorrect message direction"
error_code KRB5KRB_AP_ERR_METHOD,	"Alternative authentication method required"
error_code KRB5KRB_AP_ERR_BADSEQ,	"Incorrect sequence number in message"
error_code KRB5KRB_AP_ERR_INAPP_CKSUM,	"Inappropriate type of checksum in message"
#^^ 50
error_code KRB5KRB_AP_PATH_NOT_ACCEPTED,	"Policy rejects transited path"
error_code KRB5KRB_ERR_RESPONSE_TOO_BIG,	"Response too big for UDP, retry with TCP"
error_code KRB5PLACEHOLD_53,	"KRB5 error code 53"
error_code KRB5PLACEHOLD_54,	"KRB5 error code 54"
error_code KRB5PLACEHOLD_55,	"KRB5 error code 55"
error_code KRB5PLACEHOLD_56,	"KRB5 error code 56"
error_code KRB5PLACEHOLD_57,	"KRB5 error code 57"
error_code KRB5PLACEHOLD_58,	"KRB5 error code 58"
error_code KRB5PLACEHOLD_59,	"KRB5 error code 59"
error_code KRB5KRB_ERR_GENERIC,	"Generic error (see e-text)"
error_code KRB5KRB_ERR_FIELD_TOOLONG,	"Field is too long for this implementation"
error_code KRB5KDC_ERR_CLIENT_NOT_TRUSTED,		"Client not trusted"
error_code KRB5KDC_ERR_KDC_NOT_TRUSTED,			"KDC not trusted"
error_code KRB5KDC_ERR_INVALID_SIG,			"Invalid signature"
error_code KRB5KDC_ERR_DH_KEY_PARAMETERS_NOT_ACCEPTED,	"Key parameters not accepted"
error_code KRB5KDC_ERR_CERTIFICATE_MISMATCH,		"Certificate mismatch"
error_code KRB5KRB_AP_ERR_NO_TGT,			"No ticket granting ticket"
error_code KRB5KDC_ERR_WRONG_REALM,			"Realm not local to KDC"
error_code KRB5KRB_AP_ERR_USER_TO_USER_REQUIRED,	"User to user required"
error_code KRB5KDC_ERR_CANT_VERIFY_CERTIFICATE,		"Can't verify certificate"
error_code KRB5KDC_ERR_INVALID_CERTIFICATE,		"Invalid certificate"
error_code KRB5KDC_ERR_REVOKED_CERTIFICATE,		"Revoked certificate"
error_code KRB5KDC_ERR_REVOCATION_STATUS_UNKNOWN,	"Revocation status unknown"
error_code KRB5KDC_ERR_REVOCATION_STATUS_UNAVAILABLE,	"Revocation status unavailable"
error_code KRB5KDC_ERR_CLIENT_NAME_MISMATCH,		"Client name mismatch"
error_code KRB5KDC_ERR_KDC_NAME_MISMATCH,		"KDC name mismatch"
error_code KRB5KDC_ERR_INCONSISTENT_KEY_PURPOSE,	"Inconsistent key purpose"
error_code KRB5KDC_ERR_DIGEST_IN_CERT_NOT_ACCEPTED,	"Digest in certificate not accepted"
error_code KRB5KDC_ERR_PA_CHECKSUM_MUST_BE_INCLUDED,	"Checksum must be included"
error_code KRB5KDC_ERR_DIGEST_IN_SIGNED_DATA_NOT_ACCEPTED,	"Digest in signed-data not accepted"
error_code KRB5KDC_ERR_PUBLIC_KEY_ENCRYPTION_NOT_SUPPORTED,	"Public key encryption not supported"
error_code KRB5PLACEHOLD_82,	"KRB5 error code 82"
error_code KRB5PLACEHOLD_83,	"KRB5 error code 83"
error_code KRB5PLACEHOLD_84,	"KRB5 error code 84"
error_code KRB5KRB_AP_ERR_IAKERB_KDC_NOT_FOUND,         "The IAKERB proxy could not find a KDC"
error_code KRB5KRB_AP_ERR_IAKERB_KDC_NO_RESPONSE,	"The KDC did not respond to the IAKERB proxy"
error_code KRB5PLACEHOLD_87,	"KRB5 error code 87"
error_code KRB5PLACEHOLD_88,	"KRB5 error code 88"
error_code KRB5PLACEHOLD_89,	"KRB5 error code 89"
error_code KRB5KDC_ERR_PREAUTH_EXPIRED,			"Preauthentication expired"
error_code KRB5KDC_ERR_MORE_PREAUTH_DATA_REQUIRED,	"More preauthentication data is required"
error_code KRB5PLACEHOLD_92,	"KRB5 error code 92"
error_code KRB5KDC_ERR_UNKNOWN_CRITICAL_FAST_OPTION, "An unsupported critical FAST option was requested"
error_code KRB5PLACEHOLD_94,	"KRB5 error code 94"
error_code KRB5PLACEHOLD_95,	"KRB5 error code 95"
error_code KRB5PLACEHOLD_96,	"KRB5 error code 96"
error_code KRB5PLACEHOLD_97,	"KRB5 error code 97"
error_code KRB5PLACEHOLD_98,	"KRB5 error code 98"
error_code KRB5PLACEHOLD_99,	"KRB5 error code 99"
error_code KRB5KDC_ERR_NO_ACCEPTABLE_KDF,		"No acceptable KDF offered"
error_code KRB5PLACEHOLD_101,	"KRB5 error code 101"
error_code KRB5PLACEHOLD_102,	"KRB5 error code 102"
error_code KRB5PLACEHOLD_103,	"KRB5 error code 103"
error_code KRB5PLACEHOLD_104,	"KRB5 error code 104"
error_code KRB5PLACEHOLD_105,	"KRB5 error code 105"
error_code KRB5PLACEHOLD_106,	"KRB5 error code 106"
error_code KRB5PLACEHOLD_107,	"KRB5 error code 107"
error_code KRB5PLACEHOLD_108,	"KRB5 error code 108"
error_code KRB5PLACEHOLD_109,	"KRB5 error code 109"
error_code KRB5PLACEHOLD_110,	"KRB5 error code 110"
error_code KRB5PLACEHOLD_111,	"KRB5 error code 111"
error_code KRB5PLACEHOLD_112,	"KRB5 error code 112"
error_code KRB5PLACEHOLD_113,	"KRB5 error code 113"
error_code KRB5PLACEHOLD_114,	"KRB5 error code 114"
error_code KRB5PLACEHOLD_115,	"KRB5 error code 115"
error_code KRB5PLACEHOLD_116,	"KRB5 error code 116"
error_code KRB5PLACEHOLD_117,	"KRB5 error code 117"
error_code KRB5PLACEHOLD_118,	"KRB5 error code 118"
error_code KRB5PLACEHOLD_119,	"KRB5 error code 119"
error_code KRB5PLACEHOLD_120,	"KRB5 error code 120"
error_code KRB5PLACEHOLD_121,	"KRB5 error code 121"
error_code KRB5PLACEHOLD_122,	"KRB5 error code 122"
error_code KRB5PLACEHOLD_123,	"KRB5 error code 123"
error_code KRB5PLACEHOLD_124,	"KRB5 error code 124"
error_code KRB5PLACEHOLD_125,	"KRB5 error code 125"
error_code KRB5PLACEHOLD_126,	"KRB5 error code 126"
error_code KRB5PLACEHOLD_127,	"KRB5 error code 127"

error_code KRB5_ERR_RCSID,	"$Id$"

error_code KRB5_LIBOS_BADLOCKFLAG,	"Invalid flag for file lock mode"
error_code KRB5_LIBOS_CANTREADPWD,	"Cannot read password"
error_code KRB5_LIBOS_BADPWDMATCH,	"Password mismatch"
error_code KRB5_LIBOS_PWDINTR,		"Password read interrupted"

error_code KRB5_PARSE_ILLCHAR,		"Illegal character in component name"
error_code KRB5_PARSE_MALFORMED,	"Malformed representation of principal"

error_code KRB5_CONFIG_CANTOPEN,	"Can't open/find Kerberos configuration file"
error_code KRB5_CONFIG_BADFORMAT,	"Improper format of Kerberos configuration file"
error_code KRB5_CONFIG_NOTENUFSPACE,	"Insufficient space to return complete information"

error_code KRB5_BADMSGTYPE,		"Invalid message type specified for encoding"

error_code KRB5_CC_BADNAME,		"Credential cache name malformed"
error_code KRB5_CC_UNKNOWN_TYPE,	"Unknown credential cache type" 
error_code KRB5_CC_NOTFOUND,		"Matching credential not found"
error_code KRB5_CC_END,			"End of credential cache reached"

error_code KRB5_NO_TKT_SUPPLIED,	"Request did not supply a ticket"

error_code KRB5KRB_AP_WRONG_PRINC,		"Wrong principal in request"
error_code KRB5KRB_AP_ERR_TKT_INVALID,	"Ticket has invalid flag set"

error_code KRB5_PRINC_NOMATCH,		"Requested principal and ticket don't match"
error_code KRB5_KDCREP_MODIFIED,	"KDC reply did not match expectations"
error_code KRB5_KDCREP_SKEW,		"Clock skew too great in KDC reply"
error_code KRB5_IN_TKT_REALM_MISMATCH,	"Client/server realm mismatch in initial ticket request"

error_code KRB5_PROG_ETYPE_NOSUPP,	"Program lacks support for encryption type"
error_code KRB5_PROG_KEYTYPE_NOSUPP,	"Program lacks support for key type"
error_code KRB5_WRONG_ETYPE,		"Requested encryption type not used in message"
error_code KRB5_PROG_SUMTYPE_NOSUPP,	"Program lacks support for checksum type"

error_code KRB5_REALM_UNKNOWN,		"Cannot find KDC for requested realm"
error_code KRB5_SERVICE_UNKNOWN,	"Kerberos service unknown"
error_code KRB5_KDC_UNREACH,		"Cannot contact any KDC for requested realm"
error_code KRB5_NO_LOCALNAME,		"No local name found for principal name"

error_code KRB5_MUTUAL_FAILED,		"Mutual authentication failed"

# some of these should be combined/supplanted by system codes

error_code KRB5_RC_TYPE_EXISTS,		"Replay cache type is already registered"
error_code KRB5_RC_MALLOC,		"No more memory to allocate (in replay cache code)"
error_code KRB5_RC_TYPE_NOTFOUND,	"Replay cache type is unknown"
error_code KRB5_RC_UNKNOWN,		"Generic unknown RC error"
error_code KRB5_RC_REPLAY,		"Message is a replay"
error_code KRB5_RC_IO,			"Replay cache I/O operation failed"
error_code KRB5_RC_NOIO,		"Replay cache type does not support non-volatile storage"
error_code KRB5_RC_PARSE,		"Replay cache name parse/format error"

error_code KRB5_RC_IO_EOF,		"End-of-file on replay cache I/O"
error_code KRB5_RC_IO_MALLOC,		"No more memory to allocate (in replay cache I/O code)"
error_code KRB5_RC_IO_PERM,		"Permission denied in replay cache code"
error_code KRB5_RC_IO_IO,		"I/O error in replay cache i/o code"
error_code KRB5_RC_IO_UNKNOWN,		"Generic unknown RC/IO error"
error_code KRB5_RC_IO_SPACE,		"Insufficient system space to store replay information"

error_code KRB5_TRANS_CANTOPEN,		"Can't open/find realm translation file"
error_code KRB5_TRANS_BADFORMAT,	"Improper format of realm translation file"

error_code KRB5_LNAME_CANTOPEN,		"Can't open/find lname translation database"
error_code KRB5_LNAME_NOTRANS,		"No translation available for requested principal"
error_code KRB5_LNAME_BADFORMAT,	"Improper format of translation database entry"

error_code KRB5_CRYPTO_INTERNAL,	"Cryptosystem internal error"

error_code KRB5_KT_BADNAME,		"Key table name malformed"
error_code KRB5_KT_UNKNOWN_TYPE,	"Unknown Key table type" 
error_code KRB5_KT_NOTFOUND,		"Key table entry not found"
error_code KRB5_KT_END,			"End of key table reached"
error_code KRB5_KT_NOWRITE,		"Cannot write to specified key table"
error_code KRB5_KT_IOERR,		"Error writing to key table"

error_code KRB5_NO_TKT_IN_RLM,		"Cannot find ticket for requested realm"
error_code KRB5DES_BAD_KEYPAR,		"DES key has bad parity"
error_code KRB5DES_WEAK_KEY,		"DES key is a weak key"

error_code KRB5_BAD_ENCTYPE,		"Bad encryption type"
error_code KRB5_BAD_KEYSIZE,		"Key size is incompatible with encryption type"
error_code KRB5_BAD_MSIZE,		"Message size is incompatible with encryption type"

error_code KRB5_CC_TYPE_EXISTS,		"Credentials cache type is already registered."
error_code KRB5_KT_TYPE_EXISTS,		"Key table type is already registered."

error_code KRB5_CC_IO,			"Credentials cache I/O operation failed"
error_code KRB5_FCC_PERM,		"Credentials cache permissions incorrect"
error_code KRB5_FCC_NOFILE,		"No credentials cache found"
error_code KRB5_FCC_INTERNAL,		"Internal credentials cache error"
error_code KRB5_CC_WRITE,		"Error writing to credentials cache"
error_code KRB5_CC_NOMEM,		"No more memory to allocate (in credentials cache code)"
error_code KRB5_CC_FORMAT,		"Bad format in credentials cache"
error_code KRB5_CC_NOT_KTYPE,		"No credentials found with supported encryption types"

# errors for dual tgt library calls
error_code KRB5_INVALID_FLAGS,		"Invalid KDC option combination (library internal error)"
error_code KRB5_NO_2ND_TKT,		"Request missing second ticket"

error_code KRB5_NOCREDS_SUPPLIED,	"No credentials supplied to library routine"

# errors for sendauth (and recvauth)

error_code KRB5_SENDAUTH_BADAUTHVERS,	"Bad sendauth version was sent"
error_code KRB5_SENDAUTH_BADAPPLVERS,	"Bad application version was sent (via sendauth)"
error_code KRB5_SENDAUTH_BADRESPONSE,	"Bad response (during sendauth exchange)"
error_code KRB5_SENDAUTH_REJECTED,	"Server rejected authentication (during sendauth exchange)"

# errors for preauthentication

error_code KRB5_PREAUTH_BAD_TYPE,	"Unsupported preauthentication type"
error_code KRB5_PREAUTH_NO_KEY,		"Required preauthentication key not supplied"
error_code KRB5_PREAUTH_FAILED,		"Generic preauthentication failure"

# version number errors

error_code KRB5_RCACHE_BADVNO,	"Unsupported replay cache format version number"
error_code KRB5_CCACHE_BADVNO,	"Unsupported credentials cache format version number"
error_code KRB5_KEYTAB_BADVNO,	"Unsupported key table format version number"

#
#

error_code KRB5_PROG_ATYPE_NOSUPP,	"Program lacks support for address type"
error_code KRB5_RC_REQUIRED,	"Message replay detection requires rcache parameter"
error_code KRB5_ERR_BAD_HOSTNAME,	"Hostname cannot be canonicalized"
error_code KRB5_ERR_HOST_REALM_UNKNOWN,	"Cannot determine realm for host"
error_code KRB5_SNAME_UNSUPP_NAMETYPE,	"Conversion to service principal undefined for name type"

error_code KRB5KRB_AP_ERR_V4_REPLY, "Initial Ticket response appears to be Version 4 error"
error_code KRB5_REALM_CANT_RESOLVE,	"Cannot resolve network address for KDC in requested realm"
error_code KRB5_TKT_NOT_FORWARDABLE,	"Requesting ticket can't get forwardable tickets"
error_code KRB5_FWD_BAD_PRINCIPAL, "Bad principal name while trying to forward credentials"

error_code KRB5_GET_IN_TKT_LOOP,  "Looping detected inside krb5_get_in_tkt"
error_code KRB5_CONFIG_NODEFREALM,	"Configuration file does not specify default realm"

error_code KRB5_SAM_UNSUPPORTED,  "Bad SAM flags in obtain_sam_padata"
error_code KRB5_SAM_INVALID_ETYPE,	"Invalid encryption type in SAM challenge"
error_code KRB5_SAM_NO_CHECKSUM,	"Missing checksum in SAM challenge"
error_code KRB5_SAM_BAD_CHECKSUM,	"Bad checksum in SAM challenge"
error_code KRB5_KT_NAME_TOOLONG,	"Keytab name too long"
error_code KRB5_KT_KVNONOTFOUND,	"Key version number for principal in key table is incorrect"
error_code KRB5_APPL_EXPIRED,	"This application has expired"
error_code KRB5_LIB_EXPIRED,	"This Krb5 library has expired"

error_code KRB5_CHPW_PWDNULL,		"New password cannot be zero length"
error_code KRB5_CHPW_FAIL,		"Password change failed"
error_code KRB5_KT_FORMAT,		"Bad format in keytab"

error_code KRB5_NOPERM_ETYPE,	"Encryption type not permitted"
error_code KRB5_CONFIG_ETYPE_NOSUPP,	"No supported encryption types (config file error?)"
error_code KRB5_OBSOLETE_FN,	"Program called an obsolete, deleted function"

# translated versions of getaddrinfo errors
error_code KRB5_EAI_FAIL,	"unknown getaddrinfo failure"
error_code KRB5_EAI_NODATA,	"no data available for host/domain name"
error_code KRB5_EAI_NONAME,	"host/domain name not found"
error_code KRB5_EAI_SERVICE,	"service name unknown"

error_code KRB5_ERR_NUMERIC_REALM, "Cannot determine realm for numeric host address"

error_code KRB5_ERR_BAD_S2K_PARAMS, "Invalid key generation parameters from KDC"

error_code KRB5_ERR_NO_SERVICE,	"service not available"

error_code KRB5_CC_READONLY,    "Ccache function not supported: read-only ccache type"
error_code KRB5_CC_NOSUPP,      "Ccache function not supported: not implemented"

error_code KRB5_DELTAT_BADFORMAT,	"Invalid format of Kerberos lifetime or clock skew string"

error_code KRB5_PLUGIN_NO_HANDLE,	"Supplied data not handled by this plugin"
error_code KRB5_PLUGIN_OP_NOTSUPP,  "Plugin does not support the operation"

error_code KRB5_ERR_INVALID_UTF8,	"Invalid UTF-8 string"
error_code KRB5_ERR_FAST_REQUIRED, "FAST protected pre-authentication required but not supported by KDC"

error_code KRB5_LOCAL_ADDR_REQUIRED,  "Auth context must contain local address"
error_code KRB5_REMOTE_ADDR_REQUIRED, "Auth context must contain remote address"

error_code KRB5_TRACE_NOSUPP, "Tracing unsupported"
end
