=pod

=head1 NAME

SSL_get0_peer_scts - get SCTs received

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 const STACK_OF(SCT) *SSL_get0_peer_scts(SSL *s);

=head1 DESCRIPTION

SSL_get0_peer_scts() returns the signed certificate timestamps (SCTs) that have
been received. If this is the first time that this function has been called for
a given B<SSL> instance, it will examine the TLS extensions, OCSP response and
the peer's certificate for SCTs. Future calls will return the same SCTs.

=head1 RESTRICTIONS

If no Certificate Transparency validation callback has been set (using
B<SSL_CTX_set_ct_validation_callback> or B<SSL_set_ct_validation_callback>),
this function is not guaranteed to return all of the SCTs that the peer is
capable of sending.

=head1 RETURN VALUES

SSL_get0_peer_scts() returns a list of SCTs found, or NULL if an error occurs.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_ct_validation_callback(3)>

=head1 COPYRIGHT

Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
