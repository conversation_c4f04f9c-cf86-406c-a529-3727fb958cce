This file contains message digests of all files listed in MANIFEST,
signed via the Module::Signature module, version 0.81.

To verify the content in this distribution, first make sure you have
Module::Signature installed, then type:

    % cpansign -v

It will check each file's integrity, as well as the signature's
validity.  If "==> Signature verified OK! <==" is not displayed,
the distribution may already have been compromised, and you should
not run its Makefile.PL or Build.PL.

-----BEGIN PGP SIGNED MESSAGE-----
Hash: SHA256

SHA1 8ba1381d3fc8d81457c35e5aff52b8e55d57be7c Changes
SHA1 b457bd56a70b838ccc55d183ab09de64b6996958 INSTALL
SHA1 f12894289cb0f379f24b8d63e2e761dbcba1b216 LICENSE
SHA1 2c21cb13f53da41c4b30011aca9014db2de46862 MANIFEST
SHA1 ea82a70dfcffe05202868dfe02826aaf1f6e0229 META.json
SHA1 9ad7419fb6209e81652da42967995c8fb8f1826b META.yml
SHA1 f7634b46dde2cf8c6f31fe46327d15151d654a2c Makefile.PL
SHA1 b94aaad0a0bf2c323061bfefb9cf1fd532f14e7b README
SHA1 090d77972c087a8905fa85522854afbf4ccc999b lib/Text/Template.pm
SHA1 ca5251a021e46b60603f10e757d689e52fde1feb lib/Text/Template/Preprocess.pm
SHA1 8efad25309730a4d501fb40fc03eda4697303372 t/author-pod-syntax.t
SHA1 19cc343f8a85c6805bbeb02580487483a6283887 t/author-signature.t
SHA1 ae085010c9f08576ef8584f224e38e6a98c1c178 t/basic.t
SHA1 006feb1a0b1e5780db52aa79bd38933664a8339a t/broken.t
SHA1 dee8cef1fcd43ce5de462018f8539d4a0fbc460f t/delimiters.t
SHA1 304955c4280159ec3a4c0f2717dcff9c887bb487 t/error.t
SHA1 c862dfc08e00e76b3f2aee953583d3cc8e5524a2 t/exported.t
SHA1 50ef92bda3b6b5cbd5a9307e6f17ce49ee8f245c t/hash.t
SHA1 d5dc210684aec8bb2c4817af96597c86047169c1 t/inline-comment.t
SHA1 31ff85f423178f2d6638d35edf859d73f63dd5c7 t/nested-tags.t
SHA1 62ae0720aa86146bccfa23d2c903fa142cb86d50 t/ofh.t
SHA1 68093417d49a2afdfcd4642bacea04466039b734 t/out.t
SHA1 a8b21fdca0f1d243775a00758105e0fcc58022aa t/prepend.t
SHA1 4e7e00eccede7c3231e93ef0f3cb011423be4eb5 t/preprocess.t
SHA1 a52d61ef92e6a88d694db0be4893b88417a72f9c t/rt29928.t
SHA1 5186ff459c6042af11bca92decd271887c7b2eae t/safe.t
SHA1 aa0c9ff96d66c1f74fc7ac73ce173c9f741f552e t/safe2.t
SHA1 b50a51577c0f2c13c9a48113dc7f061385a02219 t/safe3.t
SHA1 16d3abf7588da4c0056c6c6b7818470c8601577c t/strict.t
SHA1 f325ebf739e2aec3ae62427aef0c4e86de58ad29 t/taint.t
SHA1 4dac28585388482f1719f404cc357991af77e345 t/template-encoding.t
SHA1 ce1da9bf88d6ea62d7c756f0d730dfb3c5888b6e t/warnings.t
-----BEGIN PGP SIGNATURE-----

iQJFBAEBCAAvFiEE2EtuRfhGgngE8PsARAzvLrlUzY4FAl0kljYRHG1zY2hvdXRA
Y3Bhbi5vcmcACgkQRAzvLrlUzY6BdRAAlu/LI+71ax6OJfn6O6SslZCKdIfef1+P
UQ3eQCzbUOK1hP9p6TNOMFv0xPew7a1jSsM/wLjryXKLqGIclonBopRisZjCSaFa
DSH+5k4A9TWKo7n08C3nydVZPTfHXLEO6tHIH2umDHjMyC1gDkApjvSqH53OCtoV
vUTqQn9c6MxmuIWus1gjVynTvXWtBmaqrvTuQENFUXsGH5gVVbEr0VNIyt6Ip3p5
HnRe8UdykFJZvfhEhH1pbt2j513m9Bqce0eSb/eshraFugx7coD3gk2EXqp8eVzK
84vPEcNC8Moii8+qg48VDh9wBq1u/cba2U7PcVjj3lRk/pAni31XnlXLPWJ0dZLF
fR4glE86ESTGSyI2wGDxyEdev+e64sk7geYmL388kqtpqkKWvALmQkKUJg0T0ppp
LYv31DrsP4QM1duv46y3wVbqUPeQlKBOU81vfyAonrD02tRZjwRpCD67CZ9WSgZu
el85yPneIIijYEOzV8GYHKNiiZKpu2uidKCe+MFGxBtXe9hyi9x95tuyi9/pu2FB
YjbGJ8Wb4xhS7zdDWZb/axfwbcGMVml6a6xA98jVkwjHhPvKsf3qWVZZPtWneeN0
pGNpMta73wQxrp5/W1GfvBfPVlsker6SAARg2NMwS9Wmp+ZuD6gdJ+aN0/PPtNUd
HQJJFjr/xo4=
=nx9E
-----END PGP SIGNATURE-----
