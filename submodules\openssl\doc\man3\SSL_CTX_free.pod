=pod

=head1 NAME

SSL_CTX_free - free an allocated SSL_CTX object

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_CTX_free(SSL_CTX *ctx);

=head1 DESCRIPTION

SSL_CTX_free() decrements the reference count of B<ctx>, and removes the
SSL_CTX object pointed to by B<ctx> and frees up the allocated memory if the reference count has reached 0.

It also calls the free()ing procedures for indirectly affected items, if
applicable: the session cache, the list of ciphers, the list of Client CAs,
the certificates and keys.

If B<ctx> is NULL nothing is done.

=head1 WARNINGS

If a session-remove callback is set (SSL_CTX_sess_set_remove_cb()), this
callback will be called for each session being freed from B<ctx>'s
session cache. This implies, that all corresponding sessions from an
external session cache are removed as well. If this is not desired, the user
should explicitly unset the callback by calling
SSL_CTX_sess_set_remove_cb(B<ctx>, NULL) prior to calling SSL_CTX_free().

=head1 RETURN VALUES

SSL_CTX_free() does not provide diagnostic information.

=head1 SEE ALSO

L<SSL_CTX_new(3)>, L<ssl(7)>,
L<SSL_CTX_sess_set_get_cb(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
