# makefile: Constructs the Kerberos for Windows CCAPI DLL.
#
OBJS	= $(OUTPRE)ccapi_ccache.obj \
          $(OUTPRE)ccapi_ccache_iterator.obj \
          $(OUTPRE)ccapi_context.obj \
          $(OUTPRE)ccapi_context_change_time.obj \
          $(OUTPRE)ccapi_credentials.obj \
          $(OUTPRE)ccapi_credentials_iterator.obj \
          $(OUTPRE)ccapi_ipc.obj \
	  $(OUTPRE)ccapi_err.obj \
          $(OUTPRE)ccapi_os_ipc.obj \
          $(OUTPRE)ccapi_string.obj \
          $(OUTPRE)ccapi_v2.obj \
          $(OUTPRE)cci_array_internal.obj \
          $(OUTPRE)cci_cred_union.obj \
          $(OUTPRE)cci_debugging.obj \
          $(OUTPRE)cci_identifier.obj \
          $(OUTPRE)cci_message.obj \
          $(OUTPRE)cci_os_debugging.obj \
          $(OUTPRE)cci_os_identifier.obj \
          $(OUTPRE)ccs_reply_proc.obj \
          $(OUTPRE)ccs_reply_s.obj \
          $(OUTPRE)ccs_request_c.obj \
          $(OUTPRE)ccutils.obj \
          $(OUTPRE)client.obj \
          $(OUTPRE)dllmain.obj \
          $(OUTPRE)init.obj \
          $(OUTPRE)secure.obj \
          $(OUTPRE)tls.obj \
          $(OUTPRE)util.obj \
          $(OUTPRE)win-utils.obj

##### Options
# Set NODEBUG if building release instead of debug

#BUILDTOP is krb5/src and is relative to krb5/src/ccapi/lib/win, for making Makefile.
BUILDTOP= ..\..\..
CCAPI   = $(BUILDTOP)\CCAPI
CO      = $(CCAPI)\common
COWIN   = $(CCAPI)\common\win
CCUTIL  = $(CCAPI)\common\win\OldCC
LIBDIR  = $(CCAPI)\lib
LIBWIN  = $(LIBDIR)\win
POSIX   = $(BUILDTOP)\lib\krb5\posix
OLDCC   = $(LIBWIN)\OldCC
SRCTMP  = $(LIBWIN)\srctmp

!if defined(KRB5_KFW_COMPILE)
KFWINC= /I$(BUILDTOP)\..\..\krbcc\include
!endif

# Because all the sources are pulled together into the temp directory SRCTMP,
#  the only includes we need are to directories outside of ccapi.
LOCALINCLUDES = /I..\$(BUILDTOP) /I..\$(BUILDTOP)\include /I..\$(BUILDTOP)\include\krb5 $(KFWINC) \
    -I..\$(BUILDTOP)\util\et
MIDLINCLUDES  = /I..\$(BUILDTOP)\include

CPPFLAGS = $(CPPFLAGS) /EHsc -D_CRTAPI1=_cdecl -D_CRTAPI2=_cdecl -DWINVER=0x0501 \
-D_WIN32_WINNT=0x0501 -D_CRT_SECURE_NO_WARNINGS $(cvarsdll)


##### Linker
LINK	= link
LIBS	= ..\$(CLIB) ..\$(SLIB) kernel32.lib ws2_32.lib user32.lib advapi32.lib
LFLAGS	= /nologo $(LOPTS)

all: Makefile copysrc midl $(OUTPRE)$(CCLIB).dll finish

ccs_request.h ccs_request_c.c ccs_request_s.c : ccs_request.idl ccs_request.acf
    midl $(MIDL_OPTIMIZATION) $(MIDLI) -oldnames -cpp_cmd $(CC) -cpp_opt "-E" \
    ccs_request.idl

ccs_reply.h   ccs_reply_c.c   ccs_reply_s.c   : ccs_reply.idl   ccs_reply.acf
    midl $(MIDL_OPTIMIZATION) $(MIDLI) -oldnames -cpp_cmd $(CC) -cpp_opt "-E" \
    ccs_reply.idl

copysrc :
    echo "Copying all sources needed to build $(CCLIB).dll to $(SRCTMP)"
    if NOT exist $(SRCTMP)\nul mkdir $(SRCTMP)
    xcopy /D/Y   $(CO)\*.*     $(SRCTMP)
    xcopy /D/Y   $(COWIN)\*.*  $(SRCTMP)
    xcopy /D/Y   $(CCUTIL)\*.* $(SRCTMP)
    xcopy /D/Y   $(LIBDIR)\*.* $(SRCTMP)
    xcopy /D/Y   $(LIBWIN)\*.* $(SRCTMP)
    xcopy /D/Y   $(OLDCC)\*.*  $(SRCTMP)
    cd $(SRCTMP)
    if NOT exist $(OUTPRE)\nul mkdir $(OUTPRE)

midl : ccs_request.h ccs_reply.h

VERSIONRC = $(BUILDTOP)\..\windows\version.rc
CCLIBRES = $(OUTPRE)$(CCLIB).res
# Main program:
$(CCLIBRES): $(VERSIONRC)
	$(RC) $(RCFLAGS) -DCCAPI_LIB -fo $@ -r $**

$(OUTPRE)$(CCLIB).dll: $(OBJS) $(CCLIB).def $(CCLIBRES)
	$(LINK) $(LFLAGS) -entry:$(ENTRYPOINT) -dll /map:$*.map /out:$@ /DEF:$(CCLIB).def $(OBJS) \
	    /implib:$(CCLIB).lib $(dllflags) $(LIBS) $(KFWLIB) $(CCLIBRES) rpcrt4.lib $(conlibsdll) $(conflags)

$(CCLIB).def:
    echo ;$(CCLIB).def is generated by a Makefile rule. > $(CCLIB).def
    echo HEAPSIZE	8192  >> $(CCLIB).def
    echo EXPORTS          >> $(CCLIB).def
    type ccapi.exports    >> $(CCLIB).def
    type ccapi_v2.exports >> $(CCLIB).def
    type debug.exports    >> $(CCLIB).def

finish:
    echo "Finished in ccapi/lib/win."
    cd ..

install:
    echo "Doing nothing for make install"

clean:
	if exist $(OUTPRE)*.exe del $(OUTPRE)*.exe
	if exist $(OUTPRE)*.obj del $(OUTPRE)*.obj
	if exist $(OUTPRE)*.res del $(OUTPRE)*.res
	if exist $(OUTPRE)*.map del $(OUTPRE)*.map
	if exist $(OUTPRE)*.pdb del $(OUTPRE)*.pdb
	if exist *.err del *.err
    if exist $(SRCTMP) rmdir /s /q $(SRCTMP)
