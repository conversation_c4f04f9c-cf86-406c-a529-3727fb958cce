/*
 * Portable implementation of core functions for GOST R 34.11-2012.
 *
 * Copyright (c) 2013 Cryptocom LTD.
 * This file is distributed under the same license as OpenSSL.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

#ifdef __GOST3411_HAS_SSE2__
# error "GOST R 34.11-2012: portable implementation disabled in config.h"
#endif

#define X(x, y, z) { \
    z->QWORD[0] = x->QWORD[0] ^ y->QWORD[0]; \
    z->QWORD[1] = x->QWORD[1] ^ y->QWORD[1]; \
    z->QWORD[2] = x->QWORD[2] ^ y->QWORD[2]; \
    z->QWORD[3] = x->QWORD[3] ^ y->QWORD[3]; \
    z->QWORD[4] = x->QWORD[4] ^ y->QWORD[4]; \
    z->QWORD[5] = x->QWORD[5] ^ y->QWORD[5]; \
    z->QWORD[6] = x->QWORD[6] ^ y->QWORD[6]; \
    z->QWORD[7] = x->QWORD[7] ^ y->QWORD[7]; \
}

# define __XLPS_FOR for (_i = 0; _i <= 7; _i++)
#ifndef __GOST3411_BIG_ENDIAN__
# define _datai _i
#else
# define _datai 7 - _i
#endif

#define XLPS(x, y, data) { \
    register unsigned long long r0, r1, r2, r3, r4, r5, r6, r7; \
    int _i; \
    \
    r0 = x->QWORD[0] ^ y->QWORD[0]; \
    r1 = x->QWORD[1] ^ y->QWORD[1]; \
    r2 = x->QWORD[2] ^ y->QWORD[2]; \
    r3 = x->QWORD[3] ^ y->QWORD[3]; \
    r4 = x->QWORD[4] ^ y->QWORD[4]; \
    r5 = x->QWORD[5] ^ y->QWORD[5]; \
    r6 = x->QWORD[6] ^ y->QWORD[6]; \
    r7 = x->QWORD[7] ^ y->QWORD[7]; \
    \
    \
    __XLPS_FOR {\
        data->QWORD[_datai]  = Ax[0][r0 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[1][r1 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[2][r2 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[3][r3 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[4][r4 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[5][r5 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[6][r6 & 0xFF]; \
        data->QWORD[_datai] ^= Ax[7][r7 & 0xFF]; \
        r0 >>= 8; \
        r1 >>= 8; \
        r2 >>= 8; \
        r3 >>= 8; \
        r4 >>= 8; \
        r5 >>= 8; \
        r6 >>= 8; \
        r7 >>= 8; \
    }\
}

#define ROUND(i, Ki, data) { \
    XLPS(Ki, (&C[i]), Ki); \
    XLPS(Ki, data, data); \
}
