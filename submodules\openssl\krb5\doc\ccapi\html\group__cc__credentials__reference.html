<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_t Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_t Overview</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_credentials_t type is used to store a single set of credentials for either Kerberos v4 or Kerberos v5. In addition to its only function, release(), it contains a pointer to a <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> structure. A <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> structure contains an integer of the enumerator type cc_credentials_version, which is either <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa">cc_credentials_v4</a> or <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668">cc_credentials_v5</a>, and a pointer union, which contains either a <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a> pointer or a <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a> pointer, depending on the value in version.<p>
Variables of the type cc_credentials_t are allocated by the CCAPI implementation, and should be released with their release() function. API functions which receive credentials structures from the caller always accept <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a>, which is allocated by the caller, and accordingly disposed by the caller.<p>
For API functions see <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a>
<li>struct <a class="el" href="structcc__data.html">cc_data</a>
<li>struct <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a>
<li>struct <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a>
<li>struct <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a> <a class="el" href="group__cc__credentials__reference.html#g15918c5c162aa2edf2bd1890f8a78c70">cc_credentials_v4_t</a>
<li>typedef <a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="group__cc__credentials__reference.html#g5004a9b025c5cef3364c4a614b700f50">cc_data</a>
<li>typedef <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a> <a class="el" href="group__cc__credentials__reference.html#g5e882157ba93358ac4a78760a2ccdbd6">cc_credentials_v5_t</a>
<li>typedef <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> <a class="el" href="group__cc__credentials__reference.html#ge277840865f515373fcab3c6299901a3">cc_credentials_union</a>
<li>typedef <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a> <a class="el" href="group__cc__credentials__reference.html#g85b7bf154216b8f780ddaae4b8b0a96c">cc_credentials_f</a>
<li>typedef <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a> <a class="el" href="group__cc__credentials__reference.html#g04cec8816d2e51bb3ee5b1b3c1f5429d">cc_credentials_d</a>
<li>typedef <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a> * <a class="el" href="group__cc__credentials__reference.html#gf7b0ec03495a6818dd9cd0c1d395ece2">cc_credentials_t</a>
</ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g15918c5c162aa2edf2bd1890f8a78c70"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_v4_t" ref="g15918c5c162aa2edf2bd1890f8a78c70" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a> <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g5004a9b025c5cef3364c4a614b700f50"></a><!-- doxytag: member="CredentialsCache.h::cc_data" ref="g5004a9b025c5cef3364c4a614b700f50" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__data.html">cc_data</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g5e882157ba93358ac4a78760a2ccdbd6"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_v5_t" ref="g5e882157ba93358ac4a78760a2ccdbd6" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a> <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="ge277840865f515373fcab3c6299901a3"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_union" ref="ge277840865f515373fcab3c6299901a3" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g85b7bf154216b8f780ddaae4b8b0a96c"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_f" ref="g85b7bf154216b8f780ddaae4b8b0a96c" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a> <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g04cec8816d2e51bb3ee5b1b3c1f5429d"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_d" ref="g04cec8816d2e51bb3ee5b1b3c1f5429d" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a> <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="gf7b0ec03495a6818dd9cd0c1d395ece2"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_t" ref="gf7b0ec03495a6818dd9cd0c1d395ece2" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__credentials__d.html">cc_credentials_d</a>* <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
