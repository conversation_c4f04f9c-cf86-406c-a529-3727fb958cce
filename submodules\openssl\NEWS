
  NEWS
  ====

  This file gives a brief overview of the major changes between each OpenSSL
  release. For more details please read the CHANGES file.

  Major changes between OpenSSL 1.1.1v and OpenSSL 1.1.1w [11 Sep 2023]

      o Fix POLY1305 MAC implementation corrupting XMM registers on Windows
        (CVE-2023-4807)

  Major changes between OpenSSL 1.1.1u and OpenSSL 1.1.1v [1 Aug 2023]

      o Fix excessive time spent checking DH q parameter value (CVE-2023-3817)
      o Fix DH_check() excessive time with over sized modulus (CVE-2023-3446)

  Major changes between OpenSSL 1.1.1t and OpenSSL 1.1.1u [30 May 2023]

      o Mitigate for very slow `OBJ_obj2txt()` performance with gigantic
        OBJECT IDENTIFIER sub-identities.  (CVE-2023-2650)
      o Fixed documentation of X509_VERIFY_PARAM_add0_policy() (CVE-2023-0466)
      o Fixed handling of invalid certificate policies in leaf certificates
        (CVE-2023-0465)
      o Limited the number of nodes created in a policy tree ([CVE-2023-0464])

  Major changes between OpenSSL 1.1.1s and OpenSSL 1.1.1t [7 Feb 2023]

      o Fixed X.400 address type confusion in X.509 GeneralName (CVE-2023-0286)
      o Fixed Use-after-free following BIO_new_NDEF (CVE-2023-0215)
      o Fixed Double free after calling PEM_read_bio_ex (CVE-2022-4450)
      o Fixed Timing Oracle in RSA Decryption (CVE-2022-4304)

  Major changes between OpenSSL 1.1.1r and OpenSSL 1.1.1s [1 Nov 2022]

      o Fixed a regression introduced in OpenSSL 1.1.1r not refreshing the
        certificate data to be signed before signing the certificate.

  Major changes between OpenSSL 1.1.1q and OpenSSL 1.1.1r [11 Oct 2022]

      o Added a missing header for memcmp that caused compilation failure on
        some platforms

  Major changes between OpenSSL 1.1.1p and OpenSSL 1.1.1q [5 Jul 2022]

      o Fixed AES OCB failure to encrypt some bytes on 32-bit x86 platforms
        (CVE-2022-2097)

  Major changes between OpenSSL 1.1.1o and OpenSSL 1.1.1p [21 Jun 2022]

      o Fixed additional bugs in the c_rehash script which was not properly
        sanitising shell metacharacters to prevent command injection
        (CVE-2022-2068)

  Major changes between OpenSSL 1.1.1n and OpenSSL 1.1.1o [3 May 2022]

      o Fixed a bug in the c_rehash script which was not properly sanitising
        shell metacharacters to prevent command injection (CVE-2022-1292)

  Major changes between OpenSSL 1.1.1m and OpenSSL 1.1.1n [15 Mar 2022]

      o Fixed a bug in the BN_mod_sqrt() function that can cause it to loop
        forever for non-prime moduli (CVE-2022-0778)

  Major changes between OpenSSL 1.1.1l and OpenSSL 1.1.1m [14 Dec 2021]

      o None

  Major changes between OpenSSL 1.1.1k and OpenSSL 1.1.1l [24 Aug 2021]

      o Fixed an SM2 Decryption Buffer Overflow (CVE-2021-3711)
      o Fixed various read buffer overruns processing ASN.1 strings (CVE-2021-3712)

  Major changes between OpenSSL 1.1.1j and OpenSSL 1.1.1k [25 Mar 2021]

      o Fixed a problem with verifying a certificate chain when using the
        X509_V_FLAG_X509_STRICT flag (CVE-2021-3450)
      o Fixed an issue where an OpenSSL TLS server may crash if sent a
        maliciously crafted renegotiation ClientHello message from a client
        (CVE-2021-3449)

  Major changes between OpenSSL 1.1.1i and OpenSSL 1.1.1j [16 Feb 2021]

      o Fixed a NULL pointer deref in the X509_issuer_and_serial_hash()
        function (CVE-2021-23841)
      o Fixed the RSA_padding_check_SSLv23() function and the RSA_SSLV23_PADDING
        padding mode to correctly check for rollback attacks
      o Fixed an overflow in the EVP_CipherUpdate, EVP_EncryptUpdate and
        EVP_DecryptUpdate functions (CVE-2021-23840)
      o Fixed SRP_Calc_client_key so that it runs in constant time

  Major changes between OpenSSL 1.1.1h and OpenSSL 1.1.1i [8 Dec 2020]

      o Fixed NULL pointer deref in GENERAL_NAME_cmp (CVE-2020-1971)

  Major changes between OpenSSL 1.1.1g and OpenSSL 1.1.1h [22 Sep 2020]

      o Disallow explicit curve parameters in verifications chains when
        X509_V_FLAG_X509_STRICT is used
      o Enable 'MinProtocol' and 'MaxProtocol' to configure both TLS and DTLS
        contexts
      o Oracle Developer Studio will start reporting deprecation warnings

  Major changes between OpenSSL 1.1.1f and OpenSSL 1.1.1g [21 Apr 2020]

      o Fixed segmentation fault in SSL_check_chain() (CVE-2020-1967)

  Major changes between OpenSSL 1.1.1e and OpenSSL 1.1.1f [31 Mar 2020]

      o Revert the unexpected EOF reporting via SSL_ERROR_SSL

  Major changes between OpenSSL 1.1.1d and OpenSSL 1.1.1e [17 Mar 2020]

      o Fixed an overflow bug in the x64_64 Montgomery squaring procedure
        used in exponentiation with 512-bit moduli (CVE-2019-1551)
      o Properly detect unexpected EOF while reading in libssl and report
        it via SSL_ERROR_SSL

  Major changes between OpenSSL 1.1.1c and OpenSSL 1.1.1d [10 Sep 2019]

      o Fixed a fork protection issue (CVE-2019-1549)
      o Fixed a padding oracle in PKCS7_dataDecode and CMS_decrypt_set1_pkey
        (CVE-2019-1563)
      o For built-in EC curves, ensure an EC_GROUP built from the curve name is
        used even when parsing explicit parameters
      o Compute ECC cofactors if not provided during EC_GROUP construction
        (CVE-2019-1547)
      o Early start up entropy quality from the DEVRANDOM seed source has been
        improved for older Linux systems
      o Correct the extended master secret constant on EBCDIC systems
      o Use Windows installation paths in the mingw builds (CVE-2019-1552)
      o Changed DH_check to accept parameters with order q and 2q subgroups
      o Significantly reduce secure memory usage by the randomness pools
      o Revert the DEVRANDOM_WAIT feature for Linux systems

  Major changes between OpenSSL 1.1.1b and OpenSSL 1.1.1c [28 May 2019]

      o Prevent over long nonces in ChaCha20-Poly1305 (CVE-2019-1543)

  Major changes between OpenSSL 1.1.1a and OpenSSL 1.1.1b [26 Feb 2019]

      o Change the info callback signals for the start and end of a post-handshake
        message exchange in TLSv1.3.
      o Fix a bug in DTLS over SCTP. This breaks interoperability with older versions
        of OpenSSL like OpenSSL 1.1.0 and OpenSSL 1.0.2.

  Major changes between OpenSSL 1.1.1 and OpenSSL 1.1.1a [20 Nov 2018]

      o Timing vulnerability in DSA signature generation (CVE-2018-0734)
      o Timing vulnerability in ECDSA signature generation (CVE-2018-0735)

  Major changes between OpenSSL 1.1.0i and OpenSSL 1.1.1 [11 Sep 2018]

      o Support for TLSv1.3 added (see https://wiki.openssl.org/index.php/TLS1.3
        for further important information). The TLSv1.3 implementation includes:
          o Fully compliant implementation of RFC8446 (TLSv1.3) on by default
          o Early data (0-RTT)
          o Post-handshake authentication and key update
          o Middlebox Compatibility Mode
          o TLSv1.3 PSKs
          o Support for all five RFC8446 ciphersuites
          o RSA-PSS signature algorithms (backported to TLSv1.2)
          o Configurable session ticket support
          o Stateless server support
          o Rewrite of the packet construction code for "safer" packet handling
          o Rewrite of the extension handling code
      o Complete rewrite of the OpenSSL random number generator to introduce the
        following capabilities
          o The default RAND method now utilizes an AES-CTR DRBG according to
            NIST standard SP 800-90Ar1.
          o Support for multiple DRBG instances with seed chaining.
          o There is a public and private DRBG instance.
          o The DRBG instances are fork-safe.
          o Keep all global DRBG instances on the secure heap if it is enabled.
          o The public and private DRBG instance are per thread for lock free
            operation
      o Support for various new cryptographic algorithms including:
          o SHA3
          o SHA512/224 and SHA512/256
          o EdDSA (both Ed25519 and Ed448) including X509 and TLS support
          o X448 (adding to the existing X25519 support in 1.1.0)
          o Multi-prime RSA
          o SM2
          o SM3
          o SM4
          o SipHash
          o ARIA (including TLS support)
      o Significant Side-Channel attack security improvements
      o Add a new ClientHello callback to provide the ability to adjust the SSL
        object at an early stage.
      o Add 'Maximum Fragment Length' TLS extension negotiation and support
      o A new STORE module, which implements a uniform and URI based reader of
        stores that can contain keys, certificates, CRLs and numerous other
        objects.
      o Move the display of configuration data to configdata.pm.
      o Allow GNU style "make variables" to be used with Configure.
      o Claim the namespaces OSSL and OPENSSL, represented as symbol prefixes
      o Rewrite of devcrypto engine

  Major changes between OpenSSL 1.1.0h and OpenSSL 1.1.0i [under development]

      o Client DoS due to large DH parameter (CVE-2018-0732)
      o Cache timing vulnerability in RSA Key Generation (CVE-2018-0737)

  Major changes between OpenSSL 1.1.0g and OpenSSL 1.1.0h [under development]

      o Constructed ASN.1 types with a recursive definition could exceed the
        stack (CVE-2018-0739)
      o Incorrect CRYPTO_memcmp on HP-UX PA-RISC (CVE-2018-0733)
      o rsaz_1024_mul_avx2 overflow bug on x86_64 (CVE-2017-3738)

  Major changes between OpenSSL 1.1.0f and OpenSSL 1.1.0g [2 Nov 2017]

      o bn_sqrx8x_internal carry bug on x86_64 (CVE-2017-3736)
      o Malformed X.509 IPAddressFamily could cause OOB read (CVE-2017-3735)

  Major changes between OpenSSL 1.1.0e and OpenSSL 1.1.0f [25 May 2017]

      o config now recognises 64-bit mingw and chooses mingw64 instead of mingw

  Major changes between OpenSSL 1.1.0d and OpenSSL 1.1.0e [16 Feb 2017]

      o Encrypt-Then-Mac renegotiation crash (CVE-2017-3733)

  Major changes between OpenSSL 1.1.0c and OpenSSL 1.1.0d [26 Jan 2017]

      o Truncated packet could crash via OOB read (CVE-2017-3731)
      o Bad (EC)DHE parameters cause a client crash (CVE-2017-3730)
      o BN_mod_exp may produce incorrect results on x86_64 (CVE-2017-3732)

  Major changes between OpenSSL 1.1.0b and OpenSSL 1.1.0c [10 Nov 2016]

      o ChaCha20/Poly1305 heap-buffer-overflow (CVE-2016-7054)
      o CMS Null dereference (CVE-2016-7053)
      o Montgomery multiplication may produce incorrect results (CVE-2016-7055)

  Major changes between OpenSSL 1.1.0a and OpenSSL 1.1.0b [26 Sep 2016]

      o Fix Use After Free for large message sizes (CVE-2016-6309)

  Major changes between OpenSSL 1.1.0 and OpenSSL 1.1.0a [22 Sep 2016]

      o OCSP Status Request extension unbounded memory growth (CVE-2016-6304)
      o SSL_peek() hang on empty record (CVE-2016-6305)
      o Excessive allocation of memory in tls_get_message_header()
       (CVE-2016-6307)
      o Excessive allocation of memory in dtls1_preprocess_fragment()
       (CVE-2016-6308)

  Major changes between OpenSSL 1.0.2h and OpenSSL 1.1.0 [25 Aug 2016]

      o Copyright text was shrunk to a boilerplate that points to the license
      o "shared" builds are now the default when possible
      o Added support for "pipelining"
      o Added the AFALG engine
      o New threading API implemented
      o Support for ChaCha20 and Poly1305 added to libcrypto and libssl
      o Support for extended master secret
      o CCM ciphersuites
      o Reworked test suite, now based on perl, Test::Harness and Test::More
      o *Most* libcrypto and libssl public structures were made opaque,
        including:
        BIGNUM and associated types, EC_KEY and EC_KEY_METHOD,
        DH and DH_METHOD, DSA and DSA_METHOD, RSA and RSA_METHOD,
        BIO and BIO_METHOD, EVP_MD_CTX, EVP_MD, EVP_CIPHER_CTX,
        EVP_CIPHER, EVP_PKEY and associated types, HMAC_CTX,
        X509, X509_CRL, X509_OBJECT, X509_STORE_CTX, X509_STORE,
        X509_LOOKUP, X509_LOOKUP_METHOD
      o libssl internal structures made opaque
      o SSLv2 support removed
      o Kerberos ciphersuite support removed
      o RC4 removed from DEFAULT ciphersuites in libssl
      o 40 and 56 bit cipher support removed from libssl
      o All public header files moved to include/openssl, no more symlinking
      o SSL/TLS state machine, version negotiation and record layer rewritten
      o EC revision: now operations use new EC_KEY_METHOD.
      o Support for OCB mode added to libcrypto
      o Support for asynchronous crypto operations added to libcrypto and libssl
      o Deprecated interfaces can now be disabled at build time either
        relative to the latest release via the "no-deprecated" Configure
        argument, or via the "--api=1.1.0|1.0.0|0.9.8" option.
      o Application software can be compiled with -DOPENSSL_API_COMPAT=version
        to ensure that features deprecated in that version are not exposed.
      o Support for RFC6698/RFC7671 DANE TLSA peer authentication
      o Change of Configure to use --prefix as the main installation
        directory location rather than --openssldir.  The latter becomes
        the directory for certs, private key and openssl.cnf exclusively.
      o Reworked BIO networking library, with full support for IPv6.
      o New "unified" build system
      o New security levels
      o Support for scrypt algorithm
      o Support for X25519
      o Extended SSL_CONF support using configuration files
      o KDF algorithm support. Implement TLS PRF as a KDF.
      o Support for Certificate Transparency
      o HKDF support.

  Major changes between OpenSSL 1.0.2g and OpenSSL 1.0.2h [3 May 2016]

      o Prevent padding oracle in AES-NI CBC MAC check (CVE-2016-2107)
      o Fix EVP_EncodeUpdate overflow (CVE-2016-2105)
      o Fix EVP_EncryptUpdate overflow (CVE-2016-2106)
      o Prevent ASN.1 BIO excessive memory allocation (CVE-2016-2109)
      o EBCDIC overread (CVE-2016-2176)
      o Modify behavior of ALPN to invoke callback after SNI/servername
        callback, such that updates to the SSL_CTX affect ALPN.
      o Remove LOW from the DEFAULT cipher list.  This removes singles DES from
        the default.
      o Only remove the SSLv2 methods with the no-ssl2-method option.

  Major changes between OpenSSL 1.0.2f and OpenSSL 1.0.2g [1 Mar 2016]

      o Disable weak ciphers in SSLv3 and up in default builds of OpenSSL.
      o Disable SSLv2 default build, default negotiation and weak ciphers
        (CVE-2016-0800)
      o Fix a double-free in DSA code (CVE-2016-0705)
      o Disable SRP fake user seed to address a server memory leak
        (CVE-2016-0798)
      o Fix BN_hex2bn/BN_dec2bn NULL pointer deref/heap corruption
        (CVE-2016-0797)
      o Fix memory issues in BIO_*printf functions (CVE-2016-0799)
      o Fix side channel attack on modular exponentiation (CVE-2016-0702)

  Major changes between OpenSSL 1.0.2e and OpenSSL 1.0.2f [28 Jan 2016]

      o DH small subgroups (CVE-2016-0701)
      o SSLv2 doesn't block disabled ciphers (CVE-2015-3197)

  Major changes between OpenSSL 1.0.2d and OpenSSL 1.0.2e [3 Dec 2015]

      o BN_mod_exp may produce incorrect results on x86_64 (CVE-2015-3193)
      o Certificate verify crash with missing PSS parameter (CVE-2015-3194)
      o X509_ATTRIBUTE memory leak (CVE-2015-3195)
      o Rewrite EVP_DecodeUpdate (base64 decoding) to fix several bugs
      o In DSA_generate_parameters_ex, if the provided seed is too short,
        return an error

  Major changes between OpenSSL 1.0.2c and OpenSSL 1.0.2d [9 Jul 2015]

      o Alternate chains certificate forgery (CVE-2015-1793)
      o Race condition handling PSK identify hint (CVE-2015-3196)

  Major changes between OpenSSL 1.0.2b and OpenSSL 1.0.2c [12 Jun 2015]

      o Fix HMAC ABI incompatibility

  Major changes between OpenSSL 1.0.2a and OpenSSL 1.0.2b [11 Jun 2015]

      o Malformed ECParameters causes infinite loop (CVE-2015-1788)
      o Exploitable out-of-bounds read in X509_cmp_time (CVE-2015-1789)
      o PKCS7 crash with missing EnvelopedContent (CVE-2015-1790)
      o CMS verify infinite loop with unknown hash function (CVE-2015-1792)
      o Race condition handling NewSessionTicket (CVE-2015-1791)

  Major changes between OpenSSL 1.0.2 and OpenSSL 1.0.2a [19 Mar 2015]

      o OpenSSL 1.0.2 ClientHello sigalgs DoS fix (CVE-2015-0291)
      o Multiblock corrupted pointer fix (CVE-2015-0290)
      o Segmentation fault in DTLSv1_listen fix (CVE-2015-0207)
      o Segmentation fault in ASN1_TYPE_cmp fix (CVE-2015-0286)
      o Segmentation fault for invalid PSS parameters fix (CVE-2015-0208)
      o ASN.1 structure reuse memory corruption fix (CVE-2015-0287)
      o PKCS7 NULL pointer dereferences fix (CVE-2015-0289)
      o DoS via reachable assert in SSLv2 servers fix (CVE-2015-0293)
      o Empty CKE with client auth and DHE fix (CVE-2015-1787)
      o Handshake with unseeded PRNG fix (CVE-2015-0285)
      o Use After Free following d2i_ECPrivatekey error fix (CVE-2015-0209)
      o X509_to_X509_REQ NULL pointer deref fix (CVE-2015-0288)
      o Removed the export ciphers from the DEFAULT ciphers

  Major changes between OpenSSL 1.0.1l and OpenSSL 1.0.2 [22 Jan 2015]:

      o Suite B support for TLS 1.2 and DTLS 1.2
      o Support for DTLS 1.2
      o TLS automatic EC curve selection.
      o API to set TLS supported signature algorithms and curves
      o SSL_CONF configuration API.
      o TLS Brainpool support.
      o ALPN support.
      o CMS support for RSA-PSS, RSA-OAEP, ECDH and X9.42 DH.

  Major changes between OpenSSL 1.0.1k and OpenSSL 1.0.1l [15 Jan 2015]

      o Build fixes for the Windows and OpenVMS platforms

  Major changes between OpenSSL 1.0.1j and OpenSSL 1.0.1k [8 Jan 2015]

      o Fix for CVE-2014-3571
      o Fix for CVE-2015-0206
      o Fix for CVE-2014-3569
      o Fix for CVE-2014-3572
      o Fix for CVE-2015-0204
      o Fix for CVE-2015-0205
      o Fix for CVE-2014-8275
      o Fix for CVE-2014-3570

  Major changes between OpenSSL 1.0.1i and OpenSSL 1.0.1j [15 Oct 2014]

      o Fix for CVE-2014-3513
      o Fix for CVE-2014-3567
      o Mitigation for CVE-2014-3566 (SSL protocol vulnerability)
      o Fix for CVE-2014-3568

  Major changes between OpenSSL 1.0.1h and OpenSSL 1.0.1i [6 Aug 2014]

      o Fix for CVE-2014-3512
      o Fix for CVE-2014-3511
      o Fix for CVE-2014-3510
      o Fix for CVE-2014-3507
      o Fix for CVE-2014-3506
      o Fix for CVE-2014-3505
      o Fix for CVE-2014-3509
      o Fix for CVE-2014-5139
      o Fix for CVE-2014-3508

  Major changes between OpenSSL 1.0.1g and OpenSSL 1.0.1h [5 Jun 2014]

      o Fix for CVE-2014-0224
      o Fix for CVE-2014-0221
      o Fix for CVE-2014-0198
      o Fix for CVE-2014-0195
      o Fix for CVE-2014-3470
      o Fix for CVE-2010-5298

  Major changes between OpenSSL 1.0.1f and OpenSSL 1.0.1g [7 Apr 2014]

      o Fix for CVE-2014-0160
      o Add TLS padding extension workaround for broken servers.
      o Fix for CVE-2014-0076

  Major changes between OpenSSL 1.0.1e and OpenSSL 1.0.1f [6 Jan 2014]

      o Don't include gmt_unix_time in TLS server and client random values
      o Fix for TLS record tampering bug CVE-2013-4353
      o Fix for TLS version checking bug CVE-2013-6449
      o Fix for DTLS retransmission bug CVE-2013-6450

  Major changes between OpenSSL 1.0.1d and OpenSSL 1.0.1e [11 Feb 2013]:

      o Corrected fix for CVE-2013-0169

  Major changes between OpenSSL 1.0.1c and OpenSSL 1.0.1d [4 Feb 2013]:

      o Fix renegotiation in TLS 1.1, 1.2 by using the correct TLS version.
      o Include the fips configuration module.
      o Fix OCSP bad key DoS attack CVE-2013-0166
      o Fix for SSL/TLS/DTLS CBC plaintext recovery attack CVE-2013-0169
      o Fix for TLS AESNI record handling flaw CVE-2012-2686

  Major changes between OpenSSL 1.0.1b and OpenSSL 1.0.1c [10 May 2012]:

      o Fix TLS/DTLS record length checking bug CVE-2012-2333
      o Don't attempt to use non-FIPS composite ciphers in FIPS mode.

  Major changes between OpenSSL 1.0.1a and OpenSSL 1.0.1b [26 Apr 2012]:

      o Fix compilation error on non-x86 platforms.
      o Make FIPS capable OpenSSL ciphers work in non-FIPS mode.
      o Fix SSL_OP_NO_TLSv1_1 clash with SSL_OP_ALL in OpenSSL 1.0.0

  Major changes between OpenSSL 1.0.1 and OpenSSL 1.0.1a [19 Apr 2012]:

      o Fix for ASN1 overflow bug CVE-2012-2110
      o Workarounds for some servers that hang on long client hellos.
      o Fix SEGV in AES code.

  Major changes between OpenSSL 1.0.0h and OpenSSL 1.0.1 [14 Mar 2012]:

      o TLS/DTLS heartbeat support.
      o SCTP support.
      o RFC 5705 TLS key material exporter.
      o RFC 5764 DTLS-SRTP negotiation.
      o Next Protocol Negotiation.
      o PSS signatures in certificates, requests and CRLs.
      o Support for password based recipient info for CMS.
      o Support TLS v1.2 and TLS v1.1.
      o Preliminary FIPS capability for unvalidated 2.0 FIPS module.
      o SRP support.

  Major changes between OpenSSL 1.0.0g and OpenSSL 1.0.0h [12 Mar 2012]:

      o Fix for CMS/PKCS#7 MMA CVE-2012-0884
      o Corrected fix for CVE-2011-4619
      o Various DTLS fixes.

  Major changes between OpenSSL 1.0.0f and OpenSSL 1.0.0g [18 Jan 2012]:

      o Fix for DTLS DoS issue CVE-2012-0050

  Major changes between OpenSSL 1.0.0e and OpenSSL 1.0.0f [4 Jan 2012]:

      o Fix for DTLS plaintext recovery attack CVE-2011-4108
      o Clear block padding bytes of SSL 3.0 records CVE-2011-4576
      o Only allow one SGC handshake restart for SSL/TLS CVE-2011-4619
      o Check parameters are not NULL in GOST ENGINE CVE-2012-0027
      o Check for malformed RFC3779 data CVE-2011-4577

  Major changes between OpenSSL 1.0.0d and OpenSSL 1.0.0e [6 Sep 2011]:

      o Fix for CRL vulnerability issue CVE-2011-3207
      o Fix for ECDH crashes CVE-2011-3210
      o Protection against EC timing attacks.
      o Support ECDH ciphersuites for certificates using SHA2 algorithms.
      o Various DTLS fixes.

  Major changes between OpenSSL 1.0.0c and OpenSSL 1.0.0d [8 Feb 2011]:

      o Fix for security issue CVE-2011-0014

  Major changes between OpenSSL 1.0.0b and OpenSSL 1.0.0c [2 Dec 2010]:

      o Fix for security issue CVE-2010-4180
      o Fix for CVE-2010-4252
      o Fix mishandling of absent EC point format extension.
      o Fix various platform compilation issues.
      o Corrected fix for security issue CVE-2010-3864.

  Major changes between OpenSSL 1.0.0a and OpenSSL 1.0.0b [16 Nov 2010]:

      o Fix for security issue CVE-2010-3864.
      o Fix for CVE-2010-2939
      o Fix WIN32 build system for GOST ENGINE.

  Major changes between OpenSSL 1.0.0 and OpenSSL 1.0.0a [1 Jun 2010]:

      o Fix for security issue CVE-2010-1633.
      o GOST MAC and CFB fixes.

  Major changes between OpenSSL 0.9.8n and OpenSSL 1.0.0 [29 Mar 2010]:

      o RFC3280 path validation: sufficient to process PKITS tests.
      o Integrated support for PVK files and keyblobs.
      o Change default private key format to PKCS#8.
      o CMS support: able to process all examples in RFC4134
      o Streaming ASN1 encode support for PKCS#7 and CMS.
      o Multiple signer and signer add support for PKCS#7 and CMS.
      o ASN1 printing support.
      o Whirlpool hash algorithm added.
      o RFC3161 time stamp support.
      o New generalised public key API supporting ENGINE based algorithms.
      o New generalised public key API utilities.
      o New ENGINE supporting GOST algorithms.
      o SSL/TLS GOST ciphersuite support.
      o PKCS#7 and CMS GOST support.
      o RFC4279 PSK ciphersuite support.
      o Supported points format extension for ECC ciphersuites.
      o ecdsa-with-SHA224/256/384/512 signature types.
      o dsa-with-SHA224 and dsa-with-SHA256 signature types.
      o Opaque PRF Input TLS extension support.
      o Updated time routines to avoid OS limitations.

  Major changes between OpenSSL 0.9.8m and OpenSSL 0.9.8n [24 Mar 2010]:

      o CFB cipher definition fixes.
      o Fix security issues CVE-2010-0740 and CVE-2010-0433.

  Major changes between OpenSSL 0.9.8l and OpenSSL 0.9.8m [25 Feb 2010]:

      o Cipher definition fixes.
      o Workaround for slow RAND_poll() on some WIN32 versions.
      o Remove MD2 from algorithm tables.
      o SPKAC handling fixes.
      o Support for RFC5746 TLS renegotiation extension.
      o Compression memory leak fixed.
      o Compression session resumption fixed.
      o Ticket and SNI coexistence fixes.
      o Many fixes to DTLS handling.

  Major changes between OpenSSL 0.9.8k and OpenSSL 0.9.8l [5 Nov 2009]:

      o Temporary work around for CVE-2009-3555: disable renegotiation.

  Major changes between OpenSSL 0.9.8j and OpenSSL 0.9.8k [25 Mar 2009]:

      o Fix various build issues.
      o Fix security issues (CVE-2009-0590, CVE-2009-0591, CVE-2009-0789)

  Major changes between OpenSSL 0.9.8i and OpenSSL 0.9.8j [7 Jan 2009]:

      o Fix security issue (CVE-2008-5077)
      o Merge FIPS 140-2 branch code.

  Major changes between OpenSSL 0.9.8g and OpenSSL 0.9.8h [28 May 2008]:

      o CryptoAPI ENGINE support.
      o Various precautionary measures.
      o Fix for bugs affecting certificate request creation.
      o Support for local machine keyset attribute in PKCS#12 files.

  Major changes between OpenSSL 0.9.8f and OpenSSL 0.9.8g [19 Oct 2007]:

      o Backport of CMS functionality to 0.9.8.
      o Fixes for bugs introduced with 0.9.8f.

  Major changes between OpenSSL 0.9.8e and OpenSSL 0.9.8f [11 Oct 2007]:

      o Add gcc 4.2 support.
      o Add support for AES and SSE2 assembly language optimization
        for VC++ build.
      o Support for RFC4507bis and server name extensions if explicitly
        selected at compile time.
      o DTLS improvements.
      o RFC4507bis support.
      o TLS Extensions support.

  Major changes between OpenSSL 0.9.8d and OpenSSL 0.9.8e [23 Feb 2007]:

      o Various ciphersuite selection fixes.
      o RFC3779 support.

  Major changes between OpenSSL 0.9.8c and OpenSSL 0.9.8d [28 Sep 2006]:

      o Introduce limits to prevent malicious key DoS  (CVE-2006-2940)
      o Fix security issues (CVE-2006-2937, CVE-2006-3737, CVE-2006-4343)
      o Changes to ciphersuite selection algorithm

  Major changes between OpenSSL 0.9.8b and OpenSSL 0.9.8c [5 Sep 2006]:

      o Fix Daniel Bleichenbacher forged signature attack, CVE-2006-4339
      o New cipher Camellia

  Major changes between OpenSSL 0.9.8a and OpenSSL 0.9.8b [4 May 2006]:

      o Cipher string fixes.
      o Fixes for VC++ 2005.
      o Updated ECC cipher suite support.
      o New functions EVP_CIPHER_CTX_new() and EVP_CIPHER_CTX_free().
      o Zlib compression usage fixes.
      o Built in dynamic engine compilation support on Win32.
      o Fixes auto dynamic engine loading in Win32.

  Major changes between OpenSSL 0.9.8 and OpenSSL 0.9.8a [11 Oct 2005]:

      o Fix potential SSL 2.0 rollback, CVE-2005-2969
      o Extended Windows CE support

  Major changes between OpenSSL 0.9.7g and OpenSSL 0.9.8 [5 Jul 2005]:

      o Major work on the BIGNUM library for higher efficiency and to
        make operations more streamlined and less contradictory.  This
        is the result of a major audit of the BIGNUM library.
      o Addition of BIGNUM functions for fields GF(2^m) and NIST
        curves, to support the Elliptic Crypto functions.
      o Major work on Elliptic Crypto; ECDH and ECDSA added, including
        the use through EVP, X509 and ENGINE.
      o New ASN.1 mini-compiler that's usable through the OpenSSL
        configuration file.
      o Added support for ASN.1 indefinite length constructed encoding.
      o New PKCS#12 'medium level' API to manipulate PKCS#12 files.
      o Complete rework of shared library construction and linking
        programs with shared or static libraries, through a separate
        Makefile.shared.
      o Rework of the passing of parameters from one Makefile to another.
      o Changed ENGINE framework to load dynamic engine modules
        automatically from specifically given directories.
      o New structure and ASN.1 functions for CertificatePair.
      o Changed the ZLIB compression method to be stateful.
      o Changed the key-generation and primality testing "progress"
        mechanism to take a structure that contains the ticker
        function and an argument.
      o New engine module: GMP (performs private key exponentiation).
      o New engine module: VIA PadLOck ACE extension in VIA C3
        Nehemiah processors.
      o Added support for IPv6 addresses in certificate extensions.
        See RFC 1884, section 2.2.
      o Added support for certificate policy mappings, policy
        constraints and name constraints.
      o Added support for multi-valued AVAs in the OpenSSL
        configuration file.
      o Added support for multiple certificates with the same subject
        in the 'openssl ca' index file.
      o Make it possible to create self-signed certificates using
        'openssl ca -selfsign'.
      o Make it possible to generate a serial number file with
        'openssl ca -create_serial'.
      o New binary search functions with extended functionality.
      o New BUF functions.
      o New STORE structure and library to provide an interface to all
        sorts of data repositories.  Supports storage of public and
        private keys, certificates, CRLs, numbers and arbitrary blobs.
        This library is unfortunately unfinished and unused within
        OpenSSL.
      o New control functions for the error stack.
      o Changed the PKCS#7 library to support one-pass S/MIME
        processing.
      o Added the possibility to compile without old deprecated
        functionality with the OPENSSL_NO_DEPRECATED macro or the
        'no-deprecated' argument to the config and Configure scripts.
      o Constification of all ASN.1 conversion functions, and other
        affected functions.
      o Improved platform support for PowerPC.
      o New FIPS 180-2 algorithms (SHA-224, -256, -384 and -512).
      o New X509_VERIFY_PARAM structure to support parameterisation
        of X.509 path validation.
      o Major overhaul of RC4 performance on Intel P4, IA-64 and
        AMD64.
      o Changed the Configure script to have some algorithms disabled
        by default.  Those can be explicitly enabled with the new
        argument form 'enable-xxx'.
      o Change the default digest in 'openssl' commands from MD5 to
        SHA-1.
      o Added support for DTLS.
      o New BIGNUM blinding.
      o Added support for the RSA-PSS encryption scheme
      o Added support for the RSA X.931 padding.
      o Added support for BSD sockets on NetWare.
      o Added support for files larger than 2GB.
      o Added initial support for Win64.
      o Added alternate pkg-config files.

  Major changes between OpenSSL 0.9.7l and OpenSSL 0.9.7m [23 Feb 2007]:

      o FIPS 1.1.1 module linking.
      o Various ciphersuite selection fixes.

  Major changes between OpenSSL 0.9.7k and OpenSSL 0.9.7l [28 Sep 2006]:

      o Introduce limits to prevent malicious key DoS  (CVE-2006-2940)
      o Fix security issues (CVE-2006-2937, CVE-2006-3737, CVE-2006-4343)

  Major changes between OpenSSL 0.9.7j and OpenSSL 0.9.7k [5 Sep 2006]:

      o Fix Daniel Bleichenbacher forged signature attack, CVE-2006-4339

  Major changes between OpenSSL 0.9.7i and OpenSSL 0.9.7j [4 May 2006]:

      o Visual C++ 2005 fixes.
      o Update Windows build system for FIPS.

  Major changes between OpenSSL 0.9.7h and OpenSSL 0.9.7i [14 Oct 2005]:

      o Give EVP_MAX_MD_SIZE its old value, except for a FIPS build.

  Major changes between OpenSSL 0.9.7g and OpenSSL 0.9.7h [11 Oct 2005]:

      o Fix SSL 2.0 Rollback, CVE-2005-2969
      o Allow use of fixed-length exponent on DSA signing
      o Default fixed-window RSA, DSA, DH private-key operations

  Major changes between OpenSSL 0.9.7f and OpenSSL 0.9.7g [11 Apr 2005]:

      o More compilation issues fixed.
      o Adaptation to more modern Kerberos API.
      o Enhanced or corrected configuration for Solaris64, Mingw and Cygwin.
      o Enhanced x86_64 assembler BIGNUM module.
      o More constification.
      o Added processing of proxy certificates (RFC 3820).

  Major changes between OpenSSL 0.9.7e and OpenSSL 0.9.7f [22 Mar 2005]:

      o Several compilation issues fixed.
      o Many memory allocation failure checks added.
      o Improved comparison of X509 Name type.
      o Mandatory basic checks on certificates.
      o Performance improvements.

  Major changes between OpenSSL 0.9.7d and OpenSSL 0.9.7e [25 Oct 2004]:

      o Fix race condition in CRL checking code.
      o Fixes to PKCS#7 (S/MIME) code.

  Major changes between OpenSSL 0.9.7c and OpenSSL 0.9.7d [17 Mar 2004]:

      o Security: Fix Kerberos ciphersuite SSL/TLS handshaking bug
      o Security: Fix null-pointer assignment in do_change_cipher_spec()
      o Allow multiple active certificates with same subject in CA index
      o Multiple X509 verification fixes
      o Speed up HMAC and other operations

  Major changes between OpenSSL 0.9.7b and OpenSSL 0.9.7c [30 Sep 2003]:

      o Security: fix various ASN1 parsing bugs.
      o New -ignore_err option to OCSP utility.
      o Various interop and bug fixes in S/MIME code.
      o SSL/TLS protocol fix for unrequested client certificates.

  Major changes between OpenSSL 0.9.7a and OpenSSL 0.9.7b [10 Apr 2003]:

      o Security: counter the Klima-Pokorny-Rosa extension of
        Bleichbacher's attack
      o Security: make RSA blinding default.
      o Configuration: Irix fixes, AIX fixes, better mingw support.
      o Support for new platforms: linux-ia64-ecc.
      o Build: shared library support fixes.
      o ASN.1: treat domainComponent correctly.
      o Documentation: fixes and additions.

  Major changes between OpenSSL 0.9.7 and OpenSSL 0.9.7a [19 Feb 2003]:

      o Security: Important security related bugfixes.
      o Enhanced compatibility with MIT Kerberos.
      o Can be built without the ENGINE framework.
      o IA32 assembler enhancements.
      o Support for new platforms: FreeBSD/IA64 and FreeBSD/Sparc64.
      o Configuration: the no-err option now works properly.
      o SSL/TLS: now handles manual certificate chain building.
      o SSL/TLS: certain session ID malfunctions corrected.

  Major changes between OpenSSL 0.9.6 and OpenSSL 0.9.7 [30 Dec 2002]:

      o New library section OCSP.
      o Complete rewrite of ASN1 code.
      o CRL checking in verify code and openssl utility.
      o Extension copying in 'ca' utility.
      o Flexible display options in 'ca' utility.
      o Provisional support for international characters with UTF8.
      o Support for external crypto devices ('engine') is no longer
        a separate distribution.
      o New elliptic curve library section.
      o New AES (Rijndael) library section.
      o Support for new platforms: Windows CE, Tandem OSS, A/UX, AIX 64-bit,
        Linux x86_64, Linux 64-bit on Sparc v9
      o Extended support for some platforms: VxWorks
      o Enhanced support for shared libraries.
      o Now only builds PIC code when shared library support is requested.
      o Support for pkg-config.
      o Lots of new manuals.
      o Makes symbolic links to or copies of manuals to cover all described
        functions.
      o Change DES API to clean up the namespace (some applications link also
        against libdes providing similar functions having the same name).
        Provide macros for backward compatibility (will be removed in the
        future).
      o Unify handling of cryptographic algorithms (software and engine)
        to be available via EVP routines for asymmetric and symmetric ciphers.
      o NCONF: new configuration handling routines.
      o Change API to use more 'const' modifiers to improve error checking
        and help optimizers.
      o Finally remove references to RSAref.
      o Reworked parts of the BIGNUM code.
      o Support for new engines: Broadcom ubsec, Accelerated Encryption
        Processing, IBM 4758.
      o A few new engines added in the demos area.
      o Extended and corrected OID (object identifier) table.
      o PRNG: query at more locations for a random device, automatic query for
        EGD style random sources at several locations.
      o SSL/TLS: allow optional cipher choice according to server's preference.
      o SSL/TLS: allow server to explicitly set new session ids.
      o SSL/TLS: support Kerberos cipher suites (RFC2712).
        Only supports MIT Kerberos for now.
      o SSL/TLS: allow more precise control of renegotiations and sessions.
      o SSL/TLS: add callback to retrieve SSL/TLS messages.
      o SSL/TLS: support AES cipher suites (RFC3268).

  Major changes between OpenSSL 0.9.6j and OpenSSL 0.9.6k [30 Sep 2003]:

      o Security: fix various ASN1 parsing bugs.
      o SSL/TLS protocol fix for unrequested client certificates.

  Major changes between OpenSSL 0.9.6i and OpenSSL 0.9.6j [10 Apr 2003]:

      o Security: counter the Klima-Pokorny-Rosa extension of
        Bleichbacher's attack
      o Security: make RSA blinding default.
      o Build: shared library support fixes.

  Major changes between OpenSSL 0.9.6h and OpenSSL 0.9.6i [19 Feb 2003]:

      o Important security related bugfixes.

  Major changes between OpenSSL 0.9.6g and OpenSSL 0.9.6h [5 Dec 2002]:

      o New configuration targets for Tandem OSS and A/UX.
      o New OIDs for Microsoft attributes.
      o Better handling of SSL session caching.
      o Better comparison of distinguished names.
      o Better handling of shared libraries in a mixed GNU/non-GNU environment.
      o Support assembler code with Borland C.
      o Fixes for length problems.
      o Fixes for uninitialised variables.
      o Fixes for memory leaks, some unusual crashes and some race conditions.
      o Fixes for smaller building problems.
      o Updates of manuals, FAQ and other instructive documents.

  Major changes between OpenSSL 0.9.6f and OpenSSL 0.9.6g [9 Aug 2002]:

      o Important building fixes on Unix.

  Major changes between OpenSSL 0.9.6e and OpenSSL 0.9.6f [8 Aug 2002]:

      o Various important bugfixes.

  Major changes between OpenSSL 0.9.6d and OpenSSL 0.9.6e [30 Jul 2002]:

      o Important security related bugfixes.
      o Various SSL/TLS library bugfixes.

  Major changes between OpenSSL 0.9.6c and OpenSSL 0.9.6d [9 May 2002]:

      o Various SSL/TLS library bugfixes.
      o Fix DH parameter generation for 'non-standard' generators.

  Major changes between OpenSSL 0.9.6b and OpenSSL 0.9.6c [21 Dec 2001]:

      o Various SSL/TLS library bugfixes.
      o BIGNUM library fixes.
      o RSA OAEP and random number generation fixes.
      o Object identifiers corrected and added.
      o Add assembler BN routines for IA64.
      o Add support for OS/390 Unix, UnixWare with gcc, OpenUNIX 8,
        MIPS Linux; shared library support for Irix, HP-UX.
      o Add crypto accelerator support for AEP, Baltimore SureWare,
        Broadcom and Cryptographic Appliance's keyserver
        [in 0.9.6c-engine release].

  Major changes between OpenSSL 0.9.6a and OpenSSL 0.9.6b [9 Jul 2001]:

      o Security fix: PRNG improvements.
      o Security fix: RSA OAEP check.
      o Security fix: Reinsert and fix countermeasure to Bleichbacher's
        attack.
      o MIPS bug fix in BIGNUM.
      o Bug fix in "openssl enc".
      o Bug fix in X.509 printing routine.
      o Bug fix in DSA verification routine and DSA S/MIME verification.
      o Bug fix to make PRNG thread-safe.
      o Bug fix in RAND_file_name().
      o Bug fix in compatibility mode trust settings.
      o Bug fix in blowfish EVP.
      o Increase default size for BIO buffering filter.
      o Compatibility fixes in some scripts.

  Major changes between OpenSSL 0.9.6 and OpenSSL 0.9.6a [5 Apr 2001]:

      o Security fix: change behavior of OpenSSL to avoid using
        environment variables when running as root.
      o Security fix: check the result of RSA-CRT to reduce the
        possibility of deducing the private key from an incorrectly
        calculated signature.
      o Security fix: prevent Bleichenbacher's DSA attack.
      o Security fix: Zero the premaster secret after deriving the
        master secret in DH ciphersuites.
      o Reimplement SSL_peek(), which had various problems.
      o Compatibility fix: the function des_encrypt() renamed to
        des_encrypt1() to avoid clashes with some Unixen libc.
      o Bug fixes for Win32, HP/UX and Irix.
      o Bug fixes in BIGNUM, SSL, PKCS#7, PKCS#12, X.509, CONF and
        memory checking routines.
      o Bug fixes for RSA operations in threaded environments.
      o Bug fixes in misc. openssl applications.
      o Remove a few potential memory leaks.
      o Add tighter checks of BIGNUM routines.
      o Shared library support has been reworked for generality.
      o More documentation.
      o New function BN_rand_range().
      o Add "-rand" option to openssl s_client and s_server.

  Major changes between OpenSSL 0.9.5a and OpenSSL 0.9.6 [10 Oct 2000]:

      o Some documentation for BIO and SSL libraries.
      o Enhanced chain verification using key identifiers.
      o New sign and verify options to 'dgst' application.
      o Support for DER and PEM encoded messages in 'smime' application.
      o New 'rsautl' application, low level RSA utility.
      o MD4 now included.
      o Bugfix for SSL rollback padding check.
      o Support for external crypto devices [1].
      o Enhanced EVP interface.

    [1] The support for external crypto devices is currently a separate
        distribution.  See the file README.ENGINE.

  Major changes between OpenSSL 0.9.5 and OpenSSL 0.9.5a [1 Apr 2000]:

      o Bug fixes for Win32, SuSE Linux, NeXTSTEP and FreeBSD 2.2.8
      o Shared library support for HPUX and Solaris-gcc
      o Support of Linux/IA64
      o Assembler support for Mingw32
      o New 'rand' application
      o New way to check for existence of algorithms from scripts

  Major changes between OpenSSL 0.9.4 and OpenSSL 0.9.5 [25 May 2000]:

      o S/MIME support in new 'smime' command
      o Documentation for the OpenSSL command line application
      o Automation of 'req' application
      o Fixes to make s_client, s_server work under Windows
      o Support for multiple fieldnames in SPKACs
      o New SPKAC command line utility and associated library functions
      o Options to allow passwords to be obtained from various sources
      o New public key PEM format and options to handle it
      o Many other fixes and enhancements to command line utilities
      o Usable certificate chain verification
      o Certificate purpose checking
      o Certificate trust settings
      o Support of authority information access extension
      o Extensions in certificate requests
      o Simplified X509 name and attribute routines
      o Initial (incomplete) support for international character sets
      o New DH_METHOD, DSA_METHOD and enhanced RSA_METHOD
      o Read only memory BIOs and simplified creation function
      o TLS/SSL protocol bugfixes: Accept TLS 'client hello' in SSL 3.0
        record; allow fragmentation and interleaving of handshake and other
        data
      o TLS/SSL code now "tolerates" MS SGC
      o Work around for Netscape client certificate hang bug
      o RSA_NULL option that removes RSA patent code but keeps other
        RSA functionality
      o Memory leak detection now allows applications to add extra information
        via a per-thread stack
      o PRNG robustness improved
      o EGD support
      o BIGNUM library bug fixes
      o Faster DSA parameter generation
      o Enhanced support for Alpha Linux
      o Experimental MacOS support

  Major changes between OpenSSL 0.9.3 and OpenSSL 0.9.4 [9 Aug 1999]:

      o Transparent support for PKCS#8 format private keys: these are used
        by several software packages and are more secure than the standard
        form
      o PKCS#5 v2.0 implementation
      o Password callbacks have a new void * argument for application data
      o Avoid various memory leaks
      o New pipe-like BIO that allows using the SSL library when actual I/O
        must be handled by the application (BIO pair)

  Major changes between OpenSSL 0.9.2b and OpenSSL 0.9.3 [24 May 1999]:
      o Lots of enhancements and cleanups to the Configuration mechanism
      o RSA OEAP related fixes
      o Added `openssl ca -revoke' option for revoking a certificate
      o Source cleanups: const correctness, type-safe stacks and ASN.1 SETs
      o Source tree cleanups: removed lots of obsolete files
      o Thawte SXNet, certificate policies and CRL distribution points
        extension support
      o Preliminary (experimental) S/MIME support
      o Support for ASN.1 UTF8String and VisibleString
      o Full integration of PKCS#12 code
      o Sparc assembler bignum implementation, optimized hash functions
      o Option to disable selected ciphers

  Major changes between OpenSSL 0.9.1c and OpenSSL 0.9.2b [22 Mar 1999]:
      o Fixed a security hole related to session resumption
      o Fixed RSA encryption routines for the p < q case
      o "ALL" in cipher lists now means "everything except NULL ciphers"
      o Support for Triple-DES CBCM cipher
      o Support of Optimal Asymmetric Encryption Padding (OAEP) for RSA
      o First support for new TLSv1 ciphers
      o Added a few new BIOs (syslog BIO, reliable BIO)
      o Extended support for DSA certificate/keys.
      o Extended support for Certificate Signing Requests (CSR)
      o Initial support for X.509v3 extensions
      o Extended support for compression inside the SSL record layer
      o Overhauled Win32 builds
      o Cleanups and fixes to the Big Number (BN) library
      o Support for ASN.1 GeneralizedTime
      o Splitted ASN.1 SETs from SEQUENCEs
      o ASN1 and PEM support for Netscape Certificate Sequences
      o Overhauled Perl interface
      o Lots of source tree cleanups.
      o Lots of memory leak fixes.
      o Lots of bug fixes.

  Major changes between SSLeay 0.9.0b and OpenSSL 0.9.1c [23 Dec 1998]:
      o Integration of the popular NO_RSA/NO_DSA patches
      o Initial support for compression inside the SSL record layer
      o Added BIO proxy and filtering functionality
      o Extended Big Number (BN) library
      o Added RIPE MD160 message digest
      o Added support for RC2/64bit cipher
      o Extended ASN.1 parser routines
      o Adjustments of the source tree for CVS
      o Support for various new platforms
