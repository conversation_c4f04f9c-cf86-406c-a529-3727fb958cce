=pod

=head1 NAME

EVP_PKEY_meth_get_count, EVP_PKEY_meth_get0, EVP_PKEY_meth_get0_info - enumerate public key methods

=head1 SYNOPSIS

 #include <openssl/evp.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 size_t EVP_PKEY_meth_get_count(void);
 const EVP_PKEY_METHOD *EVP_PKEY_meth_get0(size_t idx);
 void EVP_PKEY_meth_get0_info(int *ppkey_id, int *pflags,
                              const EVP_PKEY_METHOD *meth);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use the OSSL_PROVIDER APIs.

EVP_PKEY_meth_count() returns a count of the number of public key methods
available: it includes standard methods and any methods added by the
application.

EVP_PKEY_meth_get0() returns the public key method B<idx>. The value of B<idx>
must be between zero and EVP_PKEY_meth_get_count() - 1.

EVP_PKEY_meth_get0_info() returns the public key ID (a NID) and any flags
associated with the public key method B<*meth>.

=head1 RETURN VALUES

EVP_PKEY_meth_count() returns the number of available public key methods.

EVP_PKEY_meth_get0() return a public key method or B<NULL> if B<idx> is
out of range.

EVP_PKEY_meth_get0_info() does not return a value.

=head1 SEE ALSO

L<EVP_PKEY_new(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2002-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
