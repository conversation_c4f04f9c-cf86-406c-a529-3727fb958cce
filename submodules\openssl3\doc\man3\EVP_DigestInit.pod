=pod

=head1 NAME

EVP_MD_fetch, <PERSON><PERSON>_MD_up_ref, <PERSON><PERSON>_MD_free,
<PERSON><PERSON>_MD_get_params, <PERSON><PERSON>_MD_gettable_params,
<PERSON>VP_MD_CTX_new, <PERSON>VP_MD_CTX_reset, <PERSON><PERSON>_MD_CTX_free, <PERSON><PERSON>_MD_CTX_dup,
<PERSON>VP_MD_CTX_copy, EVP_MD_CTX_copy_ex, EVP_MD_CTX_ctrl,
EVP_MD_CTX_set_params, EVP_MD_CTX_get_params,
<PERSON>VP_MD_settable_ctx_params, EVP_MD_gettable_ctx_params,
EVP_MD_CTX_settable_params, EVP_MD_CTX_gettable_params,
EVP_MD_CTX_set_flags, EVP_MD_CTX_clear_flags, EVP_MD_CTX_test_flags,
EVP_Q_digest, EVP_Digest, EVP_DigestInit_ex2, EVP_DigestInit_ex, EVP_DigestInit,
EVP_DigestUpdate, EVP_DigestFinal_ex, <PERSON>VP_DigestFinalXOF, <PERSON><PERSON>_DigestFinal,
<PERSON><PERSON>_MD_is_a, <PERSON><PERSON>_MD_get0_name, EVP_MD_get0_description,
EVP_MD_names_do_all, EVP_MD_get0_provider, EVP_MD_get_type,
EVP_MD_get_pkey_type, EVP_MD_get_size, EVP_MD_get_block_size, EVP_MD_get_flags,
EVP_MD_CTX_get0_name, EVP_MD_CTX_md, EVP_MD_CTX_get0_md, EVP_MD_CTX_get1_md,
EVP_MD_CTX_get_type, EVP_MD_CTX_get_size, EVP_MD_CTX_get_block_size,
EVP_MD_CTX_get0_md_data, EVP_MD_CTX_update_fn, EVP_MD_CTX_set_update_fn,
EVP_md_null,
EVP_get_digestbyname, EVP_get_digestbynid, EVP_get_digestbyobj,
EVP_MD_CTX_get_pkey_ctx, EVP_MD_CTX_set_pkey_ctx,
EVP_MD_do_all_provided,
EVP_MD_type, EVP_MD_nid, EVP_MD_name, EVP_MD_pkey_type, EVP_MD_size,
EVP_MD_block_size, EVP_MD_flags, EVP_MD_CTX_size, EVP_MD_CTX_block_size,
EVP_MD_CTX_type, EVP_MD_CTX_pkey_ctx, EVP_MD_CTX_md_data
- EVP digest routines

=head1 SYNOPSIS

 #include <openssl/evp.h>

 EVP_MD *EVP_MD_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
                      const char *properties);
 int EVP_MD_up_ref(EVP_MD *md);
 void EVP_MD_free(EVP_MD *md);
 int EVP_MD_get_params(const EVP_MD *digest, OSSL_PARAM params[]);
 const OSSL_PARAM *EVP_MD_gettable_params(const EVP_MD *digest);
 EVP_MD_CTX *EVP_MD_CTX_new(void);
 int EVP_MD_CTX_reset(EVP_MD_CTX *ctx);
 void EVP_MD_CTX_free(EVP_MD_CTX *ctx);
 void EVP_MD_CTX_ctrl(EVP_MD_CTX *ctx, int cmd, int p1, void* p2);
 int EVP_MD_CTX_get_params(EVP_MD_CTX *ctx, OSSL_PARAM params[]);
 int EVP_MD_CTX_set_params(EVP_MD_CTX *ctx, const OSSL_PARAM params[]);
 const OSSL_PARAM *EVP_MD_settable_ctx_params(const EVP_MD *md);
 const OSSL_PARAM *EVP_MD_gettable_ctx_params(const EVP_MD *md);
 const OSSL_PARAM *EVP_MD_CTX_settable_params(EVP_MD_CTX *ctx);
 const OSSL_PARAM *EVP_MD_CTX_gettable_params(EVP_MD_CTX *ctx);
 void EVP_MD_CTX_set_flags(EVP_MD_CTX *ctx, int flags);
 void EVP_MD_CTX_clear_flags(EVP_MD_CTX *ctx, int flags);
 int EVP_MD_CTX_test_flags(const EVP_MD_CTX *ctx, int flags);

 int EVP_Q_digest(OSSL_LIB_CTX *libctx, const char *name, const char *propq,
                  const void *data, size_t datalen,
                  unsigned char *md, size_t *mdlen);
 int EVP_Digest(const void *data, size_t count, unsigned char *md,
                unsigned int *size, const EVP_MD *type, ENGINE *impl);
 int EVP_DigestInit_ex2(EVP_MD_CTX *ctx, const EVP_MD *type,
                        const OSSL_PARAM params[]);
 int EVP_DigestInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
 int EVP_DigestUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
 int EVP_DigestFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
 int EVP_DigestFinalXOF(EVP_MD_CTX *ctx, unsigned char *md, size_t len);

 EVP_MD_CTX *EVP_MD_CTX_dup(const EVP_MD_CTX *in);
 int EVP_MD_CTX_copy_ex(EVP_MD_CTX *out, const EVP_MD_CTX *in);

 int EVP_DigestInit(EVP_MD_CTX *ctx, const EVP_MD *type);
 int EVP_DigestFinal(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);

 int EVP_MD_CTX_copy(EVP_MD_CTX *out, EVP_MD_CTX *in);

 const char *EVP_MD_get0_name(const EVP_MD *md);
 const char *EVP_MD_get0_description(const EVP_MD *md);
 int EVP_MD_is_a(const EVP_MD *md, const char *name);
 int EVP_MD_names_do_all(const EVP_MD *md,
                         void (*fn)(const char *name, void *data),
                         void *data);
 const OSSL_PROVIDER *EVP_MD_get0_provider(const EVP_MD *md);
 int EVP_MD_get_type(const EVP_MD *md);
 int EVP_MD_get_pkey_type(const EVP_MD *md);
 int EVP_MD_get_size(const EVP_MD *md);
 int EVP_MD_get_block_size(const EVP_MD *md);
 unsigned long EVP_MD_get_flags(const EVP_MD *md);

 const EVP_MD *EVP_MD_CTX_get0_md(const EVP_MD_CTX *ctx);
 EVP_MD *EVP_MD_CTX_get1_md(EVP_MD_CTX *ctx);
 const char *EVP_MD_CTX_get0_name(const EVP_MD_CTX *ctx);
 int EVP_MD_CTX_get_size(const EVP_MD_CTX *ctx);
 int EVP_MD_CTX_get_block_size(const EVP_MD_CTX *ctx);
 int EVP_MD_CTX_get_type(const EVP_MD_CTX *ctx);
 void *EVP_MD_CTX_get0_md_data(const EVP_MD_CTX *ctx);

 const EVP_MD *EVP_md_null(void);

 const EVP_MD *EVP_get_digestbyname(const char *name);
 const EVP_MD *EVP_get_digestbynid(int type);
 const EVP_MD *EVP_get_digestbyobj(const ASN1_OBJECT *o);

 EVP_PKEY_CTX *EVP_MD_CTX_get_pkey_ctx(const EVP_MD_CTX *ctx);
 void EVP_MD_CTX_set_pkey_ctx(EVP_MD_CTX *ctx, EVP_PKEY_CTX *pctx);

 void EVP_MD_do_all_provided(OSSL_LIB_CTX *libctx,
                             void (*fn)(EVP_MD *mac, void *arg),
                             void *arg);

 #define EVP_MD_type EVP_MD_get_type
 #define EVP_MD_nid EVP_MD_get_type
 #define EVP_MD_name EVP_MD_get0_name
 #define EVP_MD_pkey_type EVP_MD_get_pkey_type
 #define EVP_MD_size EVP_MD_get_size
 #define EVP_MD_block_size EVP_MD_get_block_size
 #define EVP_MD_flags EVP_MD_get_flags
 #define EVP_MD_CTX_size EVP_MD_CTX_get_size
 #define EVP_MD_CTX_block_size EVP_MD_CTX_get_block_size
 #define EVP_MD_CTX_type EVP_MD_CTX_get_type
 #define EVP_MD_CTX_pkey_ctx EVP_MD_CTX_get_pkey_ctx
 #define EVP_MD_CTX_md_data EVP_MD_CTX_get0_md_data

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 const EVP_MD *EVP_MD_CTX_md(const EVP_MD_CTX *ctx);

 int (*EVP_MD_CTX_update_fn(EVP_MD_CTX *ctx))(EVP_MD_CTX *ctx,
                                              const void *data, size_t count);

 void EVP_MD_CTX_set_update_fn(EVP_MD_CTX *ctx,
                               int (*update)(EVP_MD_CTX *ctx,
                                             const void *data, size_t count));

=head1 DESCRIPTION

The EVP digest routines are a high-level interface to message digests,
and should be used instead of the digest-specific functions.

The B<EVP_MD> type is a structure for digest method implementation.

=over 4

=item EVP_MD_fetch()

Fetches the digest implementation for the given I<algorithm> from any
provider offering it, within the criteria given by the I<properties>.
See L<crypto(7)/ALGORITHM FETCHING> for further information.

The returned value must eventually be freed with EVP_MD_free().

Fetched B<EVP_MD> structures are reference counted.

=item EVP_MD_up_ref()

Increments the reference count for an B<EVP_MD> structure.

=item EVP_MD_free()

Decrements the reference count for the fetched B<EVP_MD> structure.
If the reference count drops to 0 then the structure is freed.

=item EVP_MD_CTX_new()

Allocates and returns a digest context.

=item EVP_MD_CTX_reset()

Resets the digest context I<ctx>.  This can be used to reuse an already
existing context.

=item EVP_MD_CTX_free()

Cleans up digest context I<ctx> and frees up the space allocated to it.

=item EVP_MD_CTX_ctrl()

I<This is a legacy method. EVP_MD_CTX_set_params() and EVP_MD_CTX_get_params()
is the mechanism that should be used to set and get parameters that are used by
providers.>

Performs digest-specific control actions on context I<ctx>. The control command
is indicated in I<cmd> and any additional arguments in I<p1> and I<p2>.
EVP_MD_CTX_ctrl() must be called after EVP_DigestInit_ex2(). Other restrictions
may apply depending on the control type and digest implementation.

If this function happens to be used with a fetched B<EVP_MD>, it will
translate the controls that are known to OpenSSL into L<OSSL_PARAM(3)>
parameters with keys defined by OpenSSL and call EVP_MD_CTX_get_params() or
EVP_MD_CTX_set_params() as is appropriate for each control command.

See L</CONTROLS> below for more information, including what translations are
being done.

=item EVP_MD_get_params()

Retrieves the requested list of I<params> from a MD I<md>.
See L</PARAMETERS> below for more information.

=item EVP_MD_CTX_get_params()

Retrieves the requested list of I<params> from a MD context I<ctx>.
See L</PARAMETERS> below for more information.

=item EVP_MD_CTX_set_params()

Sets the list of I<params> into a MD context I<ctx>.
See L</PARAMETERS> below for more information.

=item EVP_MD_gettable_params()

Get a constant L<OSSL_PARAM(3)> array that describes the retrievable parameters
that can be used with EVP_MD_get_params().

=item EVP_MD_gettable_ctx_params(), EVP_MD_CTX_gettable_params()

Get a constant L<OSSL_PARAM(3)> array that describes the retrievable parameters
that can be used with EVP_MD_CTX_get_params().  EVP_MD_gettable_ctx_params()
returns the parameters that can be retrieved from the algorithm, whereas
EVP_MD_CTX_gettable_params() returns the parameters that can be retrieved
in the context's current state.

=item EVP_MD_settable_ctx_params(), EVP_MD_CTX_settable_params()

Get a constant L<OSSL_PARAM(3)> array that describes the settable parameters
that can be used with EVP_MD_CTX_set_params().  EVP_MD_settable_ctx_params()
returns the parameters that can be set from the algorithm, whereas
EVP_MD_CTX_settable_params() returns the parameters that can be set in the
context's current state.

=item EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags(), EVP_MD_CTX_test_flags()

Sets, clears and tests I<ctx> flags.  See L</FLAGS> below for more information.

=item EVP_Q_digest() is a quick one-shot digest function.

It hashes I<datalen> bytes of data at I<data> using the digest algorithm
I<name>, which is fetched using the optional I<libctx> and I<propq> parameters.
The digest value is placed in I<md> and its length is written at I<mdlen>
if the pointer is not NULL. At most B<EVP_MAX_MD_SIZE> bytes will be written.

=item EVP_Digest()

A wrapper around the Digest Init_ex, Update and Final_ex functions.
Hashes I<count> bytes of data at I<data> using a digest I<type> from ENGINE
I<impl>. The digest value is placed in I<md> and its length is written at I<size>
if the pointer is not NULL. At most B<EVP_MAX_MD_SIZE> bytes will be written.
If I<impl> is NULL the default implementation of digest I<type> is used.

=item EVP_DigestInit_ex2()

Sets up digest context I<ctx> to use a digest I<type>.
I<type> is typically supplied by a function such as EVP_sha1(), or a
value explicitly fetched with EVP_MD_fetch().

The parameters B<params> are set on the context after initialisation.

The I<type> parameter can be NULL if I<ctx> has been already initialized
with another EVP_DigestInit_ex() call and has not been reset with
EVP_MD_CTX_reset().

=item EVP_DigestInit_ex()

Sets up digest context I<ctx> to use a digest I<type>.
I<type> is typically supplied by a function such as EVP_sha1(), or a
value explicitly fetched with EVP_MD_fetch().

If I<impl> is non-NULL, its implementation of the digest I<type> is used if
there is one, and if not, the default implementation is used.

The I<type> parameter can be NULL if I<ctx> has been already initialized
with another EVP_DigestInit_ex() call and has not been reset with
EVP_MD_CTX_reset().

=item EVP_DigestUpdate()

Hashes I<cnt> bytes of data at I<d> into the digest context I<ctx>. This
function can be called several times on the same I<ctx> to hash additional
data.

=item EVP_DigestFinal_ex()

Retrieves the digest value from I<ctx> and places it in I<md>. If the I<s>
parameter is not NULL then the number of bytes of data written (i.e. the
length of the digest) will be written to the integer at I<s>, at most
B<EVP_MAX_MD_SIZE> bytes will be written.  After calling EVP_DigestFinal_ex()
no additional calls to EVP_DigestUpdate() can be made, but
EVP_DigestInit_ex2() can be called to initialize a new digest operation.

=item EVP_DigestFinalXOF()

Interfaces to extendable-output functions, XOFs, such as SHAKE128 and SHAKE256.
It retrieves the digest value from I<ctx> and places it in I<len>-sized I<md>.
After calling this function no additional calls to EVP_DigestUpdate() can be
made, but EVP_DigestInit_ex2() can be called to initialize a new operation.

=item EVP_MD_CTX_dup()

Can be used to duplicate the message digest state from I<in>.  This is useful
to avoid multiple EVP_MD_fetch() calls or if large amounts of data are to be
hashed which only differ in the last few bytes.

=item EVP_MD_CTX_copy_ex()

Can be used to copy the message digest state from I<in> to I<out>. This is
useful if large amounts of data are to be hashed which only differ in the last
few bytes.

=item EVP_DigestInit()

Behaves in the same way as EVP_DigestInit_ex2() except it doesn't set any
parameters and calls EVP_MD_CTX_reset() so it cannot be used with an I<type>
of NULL.

=item EVP_DigestFinal()

Similar to EVP_DigestFinal_ex() except after computing the digest
the digest context I<ctx> is automatically cleaned up with EVP_MD_CTX_reset().

=item EVP_MD_CTX_copy()

Similar to EVP_MD_CTX_copy_ex() except the destination I<out> does not have to
be initialized.

=item EVP_MD_is_a()

Returns 1 if I<md> is an implementation of an algorithm that's
identifiable with I<name>, otherwise 0.

If I<md> is a legacy digest (it's the return value from the likes of
EVP_sha256() rather than the result of an EVP_MD_fetch()), only cipher
names registered with the default library context (see
L<OSSL_LIB_CTX(3)>) will be considered.

=item EVP_MD_get0_name(),
EVP_MD_CTX_get0_name()

Return the name of the given message digest.  For fetched message
digests with multiple names, only one of them is returned; it's
recommended to use EVP_MD_names_do_all() instead.

=item EVP_MD_names_do_all()

Traverses all names for the I<md>, and calls I<fn> with each name and
I<data>.  This is only useful with fetched B<EVP_MD>s.

=item EVP_MD_get0_description()

Returns a description of the digest, meant for display and human consumption.
The description is at the discretion of the digest implementation.

=item EVP_MD_get0_provider()

Returns an B<OSSL_PROVIDER> pointer to the provider that implements the given
B<EVP_MD>.

=item EVP_MD_get_size(),
EVP_MD_CTX_get_size()

Return the size of the message digest when passed an B<EVP_MD> or an
B<EVP_MD_CTX> structure, i.e. the size of the hash.

=item EVP_MD_get_block_size(),
EVP_MD_CTX_get_block_size()

Return the block size of the message digest when passed an B<EVP_MD> or an
B<EVP_MD_CTX> structure.

=item EVP_MD_get_type(),
EVP_MD_CTX_get_type()

Return the NID of the OBJECT IDENTIFIER representing the given message digest
when passed an B<EVP_MD> structure.  For example, C<EVP_MD_get_type(EVP_sha1())>
returns B<NID_sha1>. This function is normally used when setting ASN1 OIDs.

=item EVP_MD_CTX_get0_md_data()

Return the digest method private data for the passed B<EVP_MD_CTX>.
The space is allocated by OpenSSL and has the size originally set with
EVP_MD_meth_set_app_datasize().

=item EVP_MD_CTX_get0_md(), EVP_MD_CTX_get1_md()

EVP_MD_CTX_get0_md() returns
the B<EVP_MD> structure corresponding to the passed B<EVP_MD_CTX>. This
will be the same B<EVP_MD> object originally passed to EVP_DigestInit_ex2() (or
other similar function) when the EVP_MD_CTX was first initialised. Note that
where explicit fetch is in use (see L<EVP_MD_fetch(3)>) the value returned from
this function will not have its reference count incremented and therefore it
should not be used after the EVP_MD_CTX is freed.
EVP_MD_CTX_get1_md() is the same except the ownership is passed to the
caller and is from the passed B<EVP_MD_CTX>.

=item EVP_MD_CTX_set_update_fn()

Sets the update function for I<ctx> to I<update>.
This is the function that is called by EVP_DigestUpdate(). If not set, the
update function from the B<EVP_MD> type specified at initialization is used.

=item EVP_MD_CTX_update_fn()

Returns the update function for I<ctx>.

=item EVP_MD_get_flags()

Returns the I<md> flags. Note that these are different from the B<EVP_MD_CTX>
ones. See L<EVP_MD_meth_set_flags(3)> for more information.

=item EVP_MD_get_pkey_type()

Returns the NID of the public key signing algorithm associated with this
digest. For example EVP_sha1() is associated with RSA so this will return
B<NID_sha1WithRSAEncryption>. Since digests and signature algorithms are no
longer linked this function is only retained for compatibility reasons.

=item EVP_md_null()

A "null" message digest that does nothing: i.e. the hash it returns is of zero
length.

=item EVP_get_digestbyname(),
EVP_get_digestbynid(),
EVP_get_digestbyobj()

Returns an B<EVP_MD> structure when passed a digest name, a digest B<NID> or an
B<ASN1_OBJECT> structure respectively.

The EVP_get_digestbyname() function is present for backwards compatibility with
OpenSSL prior to version 3 and is different to the EVP_MD_fetch() function
since it does not attempt to "fetch" an implementation of the cipher.
Additionally, it only knows about digests that are built-in to OpenSSL and have
an associated NID. Similarly EVP_get_digestbynid() and EVP_get_digestbyobj()
also return objects without an associated implementation.

When the digest objects returned by these functions are used (such as in a call
to EVP_DigestInit_ex()) an implementation of the digest will be implicitly
fetched from the loaded providers. This fetch could fail if no suitable
implementation is available. Use EVP_MD_fetch() instead to explicitly fetch
the algorithm and an associated implementation from a provider.

See L<crypto(7)/ALGORITHM FETCHING> for more information about fetching.

The digest objects returned from these functions do not need to be freed with
EVP_MD_free().

=item EVP_MD_CTX_get_pkey_ctx()

Returns the B<EVP_PKEY_CTX> assigned to I<ctx>. The returned pointer should not
be freed by the caller.

=item EVP_MD_CTX_set_pkey_ctx()

Assigns an B<EVP_PKEY_CTX> to B<EVP_MD_CTX>. This is usually used to provide
a customized B<EVP_PKEY_CTX> to L<EVP_DigestSignInit(3)> or
L<EVP_DigestVerifyInit(3)>. The I<pctx> passed to this function should be freed
by the caller. A NULL I<pctx> pointer is also allowed to clear the B<EVP_PKEY_CTX>
assigned to I<ctx>. In such case, freeing the cleared B<EVP_PKEY_CTX> or not
depends on how the B<EVP_PKEY_CTX> is created.

=item EVP_MD_do_all_provided()

Traverses all messages digests implemented by all activated providers
in the given library context I<libctx>, and for each of the implementations,
calls the given function I<fn> with the implementation method and the given
I<arg> as argument.

=back

=head1 PARAMETERS

See L<OSSL_PARAM(3)> for information about passing parameters.

EVP_MD_CTX_set_params() can be used with the following OSSL_PARAM keys:

=over 4

=item "xoflen" (B<OSSL_DIGEST_PARAM_XOFLEN>) <unsigned integer>

Sets the digest length for extendable output functions.
It is used by the SHAKE algorithm and should not exceed what can be given
using a B<size_t>.

=item "pad-type" (B<OSSL_DIGEST_PARAM_PAD_TYPE>) <unsigned integer>

Sets the padding type.
It is used by the MDC2 algorithm.

=back

EVP_MD_CTX_get_params() can be used with the following OSSL_PARAM keys:

=over 4

=item "micalg" (B<OSSL_PARAM_DIGEST_KEY_MICALG>) <UTF8 string>.

Gets the digest Message Integrity Check algorithm string. This is used when
creating S/MIME multipart/signed messages, as specified in RFC 3851.
It may be used by external engines or providers.

=back

=head1 CONTROLS

EVP_MD_CTX_ctrl() can be used to send the following standard controls:

=over 4

=item EVP_MD_CTRL_MICALG

Gets the digest Message Integrity Check algorithm string. This is used when
creating S/MIME multipart/signed messages, as specified in RFC 3851.
The string value is written to I<p2>.

When used with a fetched B<EVP_MD>, EVP_MD_CTX_get_params() gets called with
an L<OSSL_PARAM(3)> item with the key "micalg" (B<OSSL_DIGEST_PARAM_MICALG>).

=item EVP_MD_CTRL_XOF_LEN

This control sets the digest length for extendable output functions to I<p1>.
Sending this control directly should not be necessary, the use of
EVP_DigestFinalXOF() is preferred.
Currently used by SHAKE.

When used with a fetched B<EVP_MD>, EVP_MD_CTX_get_params() gets called with
an L<OSSL_PARAM(3)> item with the key "xoflen" (B<OSSL_DIGEST_PARAM_XOFLEN>).

=back

=head1 FLAGS

EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags() and EVP_MD_CTX_test_flags()
can be used the manipulate and test these B<EVP_MD_CTX> flags:

=over 4

=item EVP_MD_CTX_FLAG_ONESHOT

This flag instructs the digest to optimize for one update only, if possible.

=for comment EVP_MD_CTX_FLAG_CLEANED is internal, don't mention it

=for comment EVP_MD_CTX_FLAG_REUSE is internal, don't mention it

=for comment We currently avoid documenting flags that are only bit holder:
EVP_MD_CTX_FLAG_NON_FIPS_ALLOW, EVP_MD_CTX_FLAGS_PAD_*

=item EVP_MD_CTX_FLAG_NO_INIT

This flag instructs EVP_DigestInit() and similar not to initialise the
implementation specific data.

=item EVP_MD_CTX_FLAG_FINALISE

Some functions such as EVP_DigestSign only finalise copies of internal
contexts so additional data can be included after the finalisation call.
This is inefficient if this functionality is not required, and can be
disabled with this flag.

=back

=head1 RETURN VALUES

=over 4

=item EVP_MD_fetch()

Returns a pointer to a B<EVP_MD> for success or NULL for failure.

=item EVP_MD_up_ref()

Returns 1 for success or 0 for failure.

=item EVP_Q_digest(),
EVP_Digest(),
EVP_DigestInit_ex2(),
EVP_DigestInit_ex(),
EVP_DigestInit(),
EVP_DigestUpdate(),
EVP_DigestFinal_ex(),
EVP_DigestFinalXOF(), and
EVP_DigestFinal()

return 1 for
success and 0 for failure.

=item EVP_MD_CTX_ctrl()

Returns 1 if successful or 0 for failure.

=item EVP_MD_CTX_set_params(),
EVP_MD_CTX_get_params()

Returns 1 if successful or 0 for failure.

=item EVP_MD_CTX_settable_params(),
EVP_MD_CTX_gettable_params()

Return an array of constant L<OSSL_PARAM(3)>s, or NULL if there is none
to get.

=item EVP_MD_CTX_dup()

Returns a new EVP_MD_CTX if successful or NULL on failure.

=item EVP_MD_CTX_copy_ex()

Returns 1 if successful or 0 for failure.

=item EVP_MD_get_type(),
EVP_MD_get_pkey_type()

Returns the NID of the corresponding OBJECT IDENTIFIER or NID_undef if none
exists.

=item EVP_MD_get_size(),
EVP_MD_get_block_size(),
EVP_MD_CTX_get_size(),
EVP_MD_CTX_get_block_size()

Returns the digest or block size in bytes or -1 for failure.

=item EVP_md_null()

Returns a pointer to the B<EVP_MD> structure of the "null" message digest.

=item EVP_get_digestbyname(),
EVP_get_digestbynid(),
EVP_get_digestbyobj()

Returns either an B<EVP_MD> structure or NULL if an error occurs.

=item EVP_MD_CTX_set_pkey_ctx()

This function has no return value.

=item EVP_MD_names_do_all()

Returns 1 if the callback was called for all names. A return value of 0 means
that the callback was not called for any names.

=back

=head1 NOTES

The B<EVP> interface to message digests should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the digest used and much more flexible.

New applications should use the SHA-2 (such as L<EVP_sha256(3)>) or the SHA-3
digest algorithms (such as L<EVP_sha3_512(3)>). The other digest algorithms
are still in common use.

For most applications the I<impl> parameter to EVP_DigestInit_ex() will be
set to NULL to use the default digest implementation.

Ignoring failure returns of EVP_DigestInit_ex(), EVP_DigestInit_ex2(), or
EVP_DigestInit() can lead to undefined behavior on subsequent calls
updating or finalizing the B<EVP_MD_CTX> such as the EVP_DigestUpdate() or
EVP_DigestFinal() functions. The only valid calls on the B<EVP_MD_CTX>
when initialization fails are calls that attempt another initialization of
the context or release the context.

The functions EVP_DigestInit(), EVP_DigestFinal() and EVP_MD_CTX_copy() are
obsolete but are retained to maintain compatibility with existing code. New
applications should use EVP_DigestInit_ex(), EVP_DigestFinal_ex() and
EVP_MD_CTX_copy_ex() because they can efficiently reuse a digest context
instead of initializing and cleaning it up on each call and allow non default
implementations of digests to be specified.

If digest contexts are not cleaned up after use,
memory leaks will occur.

EVP_MD_CTX_get0_name(), EVP_MD_CTX_get_size(), EVP_MD_CTX_get_block_size(),
EVP_MD_CTX_get_type(), EVP_get_digestbynid() and EVP_get_digestbyobj() are
defined as macros.

EVP_MD_CTX_ctrl() sends commands to message digests for additional configuration
or control.

=head1 EXAMPLES

This example digests the data "Test Message\n" and "Hello World\n", using the
digest name passed on the command line.

 #include <stdio.h>
 #include <string.h>
 #include <openssl/evp.h>

 int main(int argc, char *argv[])
 {
     EVP_MD_CTX *mdctx;
     const EVP_MD *md;
     char mess1[] = "Test Message\n";
     char mess2[] = "Hello World\n";
     unsigned char md_value[EVP_MAX_MD_SIZE];
     unsigned int md_len, i;

     if (argv[1] == NULL) {
         printf("Usage: mdtest digestname\n");
         exit(1);
     }

     md = EVP_get_digestbyname(argv[1]);
     if (md == NULL) {
         printf("Unknown message digest %s\n", argv[1]);
         exit(1);
     }

     mdctx = EVP_MD_CTX_new();
     if (!EVP_DigestInit_ex2(mdctx, md, NULL)) {
         printf("Message digest initialization failed.\n");
         EVP_MD_CTX_free(mdctx);
         exit(1);
     }
     if (!EVP_DigestUpdate(mdctx, mess1, strlen(mess1))) {
         printf("Message digest update failed.\n");
         EVP_MD_CTX_free(mdctx);
         exit(1);
     }
     if (!EVP_DigestUpdate(mdctx, mess2, strlen(mess2))) {
         printf("Message digest update failed.\n");
         EVP_MD_CTX_free(mdctx);
         exit(1);
     }
     if (!EVP_DigestFinal_ex(mdctx, md_value, &md_len)) {
         printf("Message digest finalization failed.\n");
         EVP_MD_CTX_free(mdctx);
         exit(1);
     }
     EVP_MD_CTX_free(mdctx);

     printf("Digest is: ");
     for (i = 0; i < md_len; i++)
         printf("%02x", md_value[i]);
     printf("\n");

     exit(0);
 }

=head1 SEE ALSO

L<EVP_MD_meth_new(3)>,
L<openssl-dgst(1)>,
L<evp(7)>,
L<OSSL_PROVIDER(3)>,
L<OSSL_PARAM(3)>,
L<property(7)>,
L<crypto(7)/ALGORITHM FETCHING>,
L<provider-digest(7)>,
L<life_cycle-digest(7)>

The full list of digest algorithms are provided below.

L<EVP_blake2b512(3)>,
L<EVP_md2(3)>,
L<EVP_md4(3)>,
L<EVP_md5(3)>,
L<EVP_mdc2(3)>,
L<EVP_ripemd160(3)>,
L<EVP_sha1(3)>,
L<EVP_sha224(3)>,
L<EVP_sha3_224(3)>,
L<EVP_sm3(3)>,
L<EVP_whirlpool(3)>

=head1 HISTORY

The EVP_MD_CTX_create() and EVP_MD_CTX_destroy() functions were renamed to
EVP_MD_CTX_new() and EVP_MD_CTX_free() in OpenSSL 1.1.0, respectively.

The link between digests and signing algorithms was fixed in OpenSSL 1.0 and
later, so now EVP_sha1() can be used with RSA and DSA.

The EVP_dss1() function was removed in OpenSSL 1.1.0.

The EVP_MD_CTX_set_pkey_ctx() function was added in OpenSSL 1.1.1.

The EVP_Q_digest(), EVP_DigestInit_ex2(),
EVP_MD_fetch(), EVP_MD_free(), EVP_MD_up_ref(),
EVP_MD_get_params(), EVP_MD_CTX_set_params(), EVP_MD_CTX_get_params(),
EVP_MD_gettable_params(), EVP_MD_gettable_ctx_params(),
EVP_MD_settable_ctx_params(), EVP_MD_CTX_settable_params() and
EVP_MD_CTX_gettable_params() functions were added in OpenSSL 3.0.

The EVP_MD_type(), EVP_MD_nid(), EVP_MD_name(), EVP_MD_pkey_type(),
EVP_MD_size(), EVP_MD_block_size(), EVP_MD_flags(), EVP_MD_CTX_size(),
EVP_MD_CTX_block_size(), EVP_MD_CTX_type(), and EVP_MD_CTX_md_data()
functions were renamed to include C<get> or C<get0> in their names in
OpenSSL 3.0, respectively. The old names are kept as non-deprecated
alias macros.

The EVP_MD_CTX_md() function was deprecated in OpenSSL 3.0; use
EVP_MD_CTX_get0_md() instead.
EVP_MD_CTX_update_fn() and EVP_MD_CTX_set_update_fn() were deprecated
in OpenSSL 3.0.

EVP_MD_CTX_dup() was added in OpenSSL 3.1.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
