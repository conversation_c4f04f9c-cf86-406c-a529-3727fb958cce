Test vectors for RSA PKCS#1 v1.5 Encryption
===========================================

This file contains test vectors for the PKCS#1 v1.5 
encryption scheme. 15 RSA keys of different sizes have 
been generated. For each key, 20 random messages of length 
between 1 and 64 octets have been PKCS#1 v1.5 encrypted.
As specified in PKCS#1, the block type for this operation is 2.
The seed value of each example provides the pseudo random bytes 
to be used for padding. This makes the result predictable.
Note that each example can be used to test encryption and 
decryption.

Key lengths:

Key  1: 1024 bits
Key  2: 1024 bits
Key  3: 1024 bits
Key  4: 1024 bits
Key  5: 1024 bits
Key  6: 1024 bits
Key  7: 1025 bits
Key  8: 1026 bits
Key  9: 1027 bits
Key 10: 1028 bits
Key 11: 1029 bits
Key 12: 1030 bits
Key 13: 1031 bits
Key 14: 1536 bits
Key 15: 2048 bits

These test vectors have been derived from the OAEP test vectors.
============================================================================
# Thirteen RSA keys with bit sizes between 1024 and 1031, one 1536-bit key, 
# and one 2048-bit key are generated.

# For each key, 20 random messages are PKCS#1 v1.5 encrypted with random seeds.

# Example 1: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
a8 b3 b2 84 af 8e b5 0b 38 70 34 a8 60 f1 46 c4 
91 9f 31 87 63 cd 6c 55 98 c8 ae 48 11 a1 e0 ab 
c4 c7 e0 b0 82 d6 93 a5 e7 fc ed 67 5c f4 66 85 
12 77 2c 0c bc 64 a7 42 c6 c6 30 f5 33 c8 cc 72 
f6 2a e8 33 c4 0b f2 58 42 e9 84 bb 78 bd bf 97 
c0 10 7d 55 bd b6 62 f5 c4 e0 fa b9 84 5c b5 14 
8e f7 39 2d d3 aa ff 93 ae 1e 6b 66 7b b3 d4 24 
76 16 d4 f5 ba 10 d4 cf d2 26 de 88 d3 9f 16 fb 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
a8 b3 b2 84 af 8e b5 0b 38 70 34 a8 60 f1 46 c4 
91 9f 31 87 63 cd 6c 55 98 c8 ae 48 11 a1 e0 ab 
c4 c7 e0 b0 82 d6 93 a5 e7 fc ed 67 5c f4 66 85 
12 77 2c 0c bc 64 a7 42 c6 c6 30 f5 33 c8 cc 72 
f6 2a e8 33 c4 0b f2 58 42 e9 84 bb 78 bd bf 97 
c0 10 7d 55 bd b6 62 f5 c4 e0 fa b9 84 5c b5 14 
8e f7 39 2d d3 aa ff 93 ae 1e 6b 66 7b b3 d4 24 
76 16 d4 f5 ba 10 d4 cf d2 26 de 88 d3 9f 16 fb 

# Public exponent: 
01 00 01 

# Exponent: 
53 33 9c fd b7 9f c8 46 6a 65 5c 73 16 ac a8 5c 
55 fd 8f 6d d8 98 fd af 11 95 17 ef 4f 52 e8 fd 
8e 25 8d f9 3f ee 18 0f a0 e4 ab 29 69 3c d8 3b 
15 2a 55 3d 4a c4 d1 81 2b 8b 9f a5 af 0e 7f 55 
fe 73 04 df 41 57 09 26 f3 31 1f 15 c4 d6 5a 73 
2c 48 31 16 ee 3d 3d 2d 0a f3 54 9a d9 bf 7c bf 
b7 8a d8 84 f8 4d 5b eb 04 72 4d c7 36 9b 31 de 
f3 7d 0c f5 39 e9 cf cd d3 de 65 37 29 ea d5 d1 

# Prime 1: 
d3 27 37 e7 26 7f fe 13 41 b2 d5 c0 d1 50 a8 1b 
58 6f b3 13 2b ed 2f 8d 52 62 86 4a 9c b9 f3 0a 
f3 8b e4 48 59 8d 41 3a 17 2e fb 80 2c 21 ac f1 
c1 1c 52 0c 2f 26 a4 71 dc ad 21 2e ac 7c a3 9d 

# Prime 2: 
cc 88 53 d1 d5 4d a6 30 fa c0 04 f4 71 f2 81 c7 
b8 98 2d 82 24 a4 90 ed be b3 3d 3e 3d 5c c9 3c 
47 65 70 3d 1d d7 91 64 2f 1f 11 6a 0d d8 52 be 
24 19 b2 af 72 bf e9 a0 30 e8 60 b0 28 8b 5d 77 

# Prime exponent 1: 
0e 12 bf 17 18 e9 ce f5 59 9b a1 c3 88 2f e8 04 
6a 90 87 4e ef ce 8f 2c cc 20 e4 f2 74 1f b0 a3 
3a 38 48 ae c9 c9 30 5f be cb d2 d7 68 19 96 7d 
46 71 ac c6 43 1e 40 37 96 8d b3 78 78 e6 95 c1 

# Prime exponent 2: 
95 29 7b 0f 95 a2 fa 67 d0 07 07 d6 09 df d4 fc 
05 c8 9d af c2 ef 6d 6e a5 5b ec 77 1e a3 33 73 
4d 92 51 e7 90 82 ec da 86 6e fe f1 3c 45 9e 1a 
63 13 86 b7 e3 54 c8 99 f5 f1 12 ca 85 d7 15 83 

# Coefficient: 
4f 45 6c 50 24 93 bd c0 ed 2a b7 56 a3 a6 ed 4d 
67 35 2a 69 7d 42 16 e9 32 12 b1 27 a6 3d 54 11 
ce 6f a9 8d 5d be fd 73 26 3e 37 28 14 27 43 81 
81 66 ed 7d d6 36 87 dd 2a 8c a1 d2 f4 fb d8 e1 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# ----------------------------------

# Message:
66 28 19 4e 12 07 3d b0 3b a9 4c da 9e f9 53 23 
97 d5 0d ba 79 b9 87 00 4a fe fe 34 

# Seed:
01 73 41 ae 38 75 d5 f8 71 01 f8 cc 4f a9 b9 bc
15 6b b0 46 28 fc cd b2 f4 f1 1e 90 5b d3 a1 55
d3 76 f5 93 bd 73 04 21 08 74 eb a0 8a 5e 22 bc
cc b4 c9 d3 88 2a 93 a5 4d b0 22 f5 03 d1 63 38
b6 b7 ce 16 dc 7f 4b bf 9a 96 b5 97 72 d6 60 6e
97 47 c7 64 9b f9 e0 83 db 98 18 84 a9 54 ab 3c
6f

# Encryption:
50 b4 c1 41 36 bd 19 8c 2f 3c 3e d2 43 fc e0 36
e1 68 d5 65 17 98 4a 26 3c d6 64 92 b8 08 04 f1
69 d2 10 f2 b9 bd fb 48 b1 2f 9e a0 50 09 c7 7d
a2 57 cc 60 0c ce fe 3a 62 83 78 9d 8e a0 e6 07
ac 58 e2 69 0e c4 eb c1 01 46 e8 cb aa 5e d4 d5
cc e6 fe 7b 0f f9 ef c1 ea bb 56 4d bf 49 82 85
f4 49 ee 61 dd 7b 42 ee 5b 58 92 cb 90 60 1f 30
cd a0 7b f2 64 89 31 0b cd 23 b5 28 ce ab 3c 31

# PKCS#1 v1.5 Encryption Example 1.2
# ----------------------------------

# Message:
75 0c 40 47 f5 47 e8 e4 14 11 85 65 23 29 8a c9 
ba e2 45 ef af 13 97 fb e5 6f 9d d5 

# Seed:
ac 47 28 a8 42 8c 1e 52 24 71 a8 df 73 5a 8e 92
92 af 0d 55 bc b7 3a 12 ac 32 c2 64 f3 88 1c 7c
8a 71 0f 70 fe b1 04 85 c8 37 0f 78 1f ff d0 21
81 6f 05 87 39 76 6d a0 a9 c9 db 0e ae 7e 9a 25
b6 c4 33 18 d0 ca ac 23 65 22 ca 31 0f 17 fc 52
ad 42 29 c8 3a 24 e9 e5 45 eb 35 e9 82 6d 55 9f
57

# Encryption:
68 42 e5 e2 cc 00 41 d6 b0 c8 1a 56 2c 39 a6 17
37 9a 51 5c ab 74 ab cb 26 19 c7 74 0a 54 1d 95
55 dd 91 65 97 5b f8 a3 eb d0 d0 45 66 61 df b1
a6 86 1b a2 33 22 69 93 0e 0d b5 14 fc a0 73 3e
eb 9c 40 57 13 eb 1f 9d 76 80 33 ed 29 3e 1e 08
1a 12 5f 32 dd b9 ea 52 ed be 27 5c 4a f6 0f 8a
7b f8 32 bd 22 75 61 c2 08 dc 00 31 a8 4b 50 12
c9 dd 9f 74 45 9d cb 07 0b db e1 3c fa 8c 2d 50

# PKCS#1 v1.5 Encryption Example 1.3
# ----------------------------------

# Message:
d9 4a e0 83 2e 64 45 ce 42 33 1c b0 6d 53 1a 82 
b1 db 4b aa d3 0f 74 6d c9 16 df 24 d4 e3 c2 45 
1f ff 59 a6 42 3e b0 e1 d0 2d 4f e6 46 cf 69 9d 
fd 81 8c 6e 97 b0 51 

# Seed:
dd 2d 60 a5 e0 08 eb e1 d0 be 6f 60 db c4 3f 29
62 ef 50 bf de 54 2b bb e9 8f ed d1 fe ac 05 7e
77 1c f1 5f c6 32 c8 db 27 2e 28 d2 9b 57 93 ea
6a b8 06 21 8c 53 82 39 b9 3a 93 5e 65 d2 44 16
ec 6c 6e 99 ae 04

# Encryption:
70 9c 7d 2d 45 98 c9 60 65 b6 58 8d a2 f8 9f a8
7f 06 2d 72 41 ef 65 95 89 8f 63 7a da 57 ea e9
01 73 f0 fb 4b f6 a9 1e bd 96 50 69 07 c8 53 da
cf 20 84 94 be 94 d3 13 a0 41 85 d4 74 a9 07 41
2e ff c3 e0 24 d0 7e 4d 09 aa 24 5f bc b1 30 21
9b fa 5d e0 2d 4f 7e 2e c9 e6 2e 8a d3 2d ee 5f
f4 d8 e4 cf ec bc 50 33 a1 c2 c6 1c 52 33 ae 16
19 2a 48 1d 00 75 bf c7 ce 02 82 12 cd 27 be be

# PKCS#1 v1.5 Encryption Example 1.4
# ----------------------------------

# Message:
52 e6 50 d9 8e 7f 2a 04 8b 4f 86 85 21 53 b9 7e 
01 dd 31 6f 34 6a 19 f6 7a 85 

# Seed:
26 29 a7 aa c0 c3 90 5e 83 1e b6 02 38 8c 54 5a
f5 54 b9 6b 2a e5 15 32 e9 cc db 89 72 ef 30 b6
4a 2f 98 c6 95 29 7a 01 c5 81 2a 2c 40 15 82 f3
7b 14 4a 3e 90 e5 9d 81 b6 90 39 c6 4b 84 4b 02
8c 10 5c 8e 68 36 15 af b6 58 b6 c4 d9 f3 82 38
a7 63 01 bb 14 44 91 13 b6 9d e1 26 04 5e 26 f1
3e e6 d7

# Encryption:
54 dd b7 84 26 8e ad b3 95 5b d9 f9 49 88 42 59
5a d2 9f f8 a6 67 fe b4 1f 6f 53 0c b6 0b c9 26
ac 6c 71 c7 72 f8 03 d0 22 b4 1c a5 72 04 22 3b
27 ca 79 ec 5b 72 65 2c a9 af bf 40 dc 2f 6a 0e
13 bc d6 0d 37 f7 95 04 b0 ff cc 01 cf 53 42 d6
d3 4a c6 f1 f2 f9 f2 f4 87 46 25 b9 fd bb 7d da
2e c8 7d f0 cf 87 25 97 98 df 86 a0 6b d5 ae f7
35 4b 8c b1 cb 13 75 75 f4 cf bc 46 28 1b b3 31

# PKCS#1 v1.5 Encryption Example 1.5
# ----------------------------------

# Message:
8d a8 9f d9 e5 f9 74 a2 9f ef fb 46 2b 49 18 0f 
6c f9 e8 02 

# Seed:
c3 ca 84 60 0f 35 c8 65 5f c7 c6 4c 75 c5 87 38
53 d3 aa 8a 94 26 a5 1b 63 d7 e7 5d cf 6c ae 97
a4 25 3f ba 87 1d 6f 96 89 97 19 9b f0 1b 6a 4d
34 28 ce 4c 96 d1 c4 87 b2 83 0c b9 e3 5d 64 05
56 23 69 9a b4 97 9a 02 58 4b 92 e6 ba 39 e7 57
28 40 79 ab f1 33 a7 da 54 e5 42 52 17 a2 10 f6
7c 18 26 9b 51 1f 61 f8 c5

# Encryption:
a8 55 48 01 3b d0 e2 0e e0 eb d3 6f b7 48 97 7f
98 58 46 d7 61 0e ed 24 c3 6c d8 30 33 dd 2a a4
58 0b d1 53 35 20 9d cf 78 2e e2 6c 48 c3 06 44
b0 b5 cc 86 c8 cd 16 5a e2 1e ad f5 78 04 18 67
76 07 03 18 75 e2 21 ec df 3b 10 57 31 6f 3f 12
a4 7d 5d a4 0c 41 53 9b 63 64 30 da 2e 54 21 90
11 9e 42 9c 53 c2 22 6f 95 9b 19 cc f4 8a 3d 24
02 17 c4 de 70 d7 07 2a 7e 0d 95 b6 16 d1 15 a8

# PKCS#1 v1.5 Encryption Example 1.6
# ----------------------------------

# Message:
26 52 10 50 84 42 71 

# Seed:
3c 6a 04 71 da f0 0b 7c 2e fc 9e e8 80 41 65 4f
87 62 90 07 c1 24 32 22 11 a5 f4 ea 3c 58 23 85
7b c8 fc 7e 21 c9 45 48 b0 ee bd cf f7 91 60 e1
12 46 1e 40 50 91 10 cf dc 4f 0f 13 c7 fb 92 1a
ba c8 df aa c2 1a cb 0f 7b 8a 13 a4 b5 cc a5 23
d5 c7 dd f7 05 23 eb 57 0c 59 b6 c7 ae 97 67 e4
ec 9a 63 d1 13 6d 10 23 1b 40 1e 20 e7 41 02 84
83 48 01 7a 16 16

# Encryption:
5e 51 43 63 28 7d e9 b3 80 04 8c c4 43 5d 53 29
4a d5 94 1c 55 1a 97 e1 3c 16 dc 13 98 de 61 0d
c7 33 7b c6 bd e5 78 e9 e9 f5 6a f1 44 54 f2 e8
31 be ef 32 31 a8 50 68 e8 fe f7 2c 89 e1 df 1c
99 43 0a 60 f6 d9 42 89 cf ba 87 b2 b4 32 a4 0b
88 db 61 da e0 88 f9 ed 4e 28 4a 21 63 af 65 bf
2b 43 55 9a 5d a2 ae c5 bb 8f 43 f9 2c 1b 04 a5
14 6a 65 b6 e0 19 b4 cd d2 94 0c 35 d9 64 5b 2d

# PKCS#1 v1.5 Encryption Example 1.7
# ----------------------------------

# Message:
8a 84 7d d9 e2 

# Seed:
a1 3d ff 8a 48 e8 04 94 ae 66 e6 ba 9f 17 9a 01
0d 9e 6d 40 31 87 96 7f 99 fd d9 0e f9 0e 0a 94
07 3f f0 e4 d0 e6 66 4f f3 73 b5 09 95 3e 04 ef
77 83 be 0f b4 6c 8a 9f c0 ed 8c 1f 33 cb 4d 0d
2f 1d 0d 5c db a1 4d ca 50 8c a1 d7 3d 20 80 18
63 9b c8 e1 65 86 23 de 1e 5b a3 f0 5e d0 91 4d
2f 96 90 2f 25 20 33 2d 84 92 d3 73 4a cd eb bd
f4 3e 50 a4 3e 7a a8 72

# Encryption:
82 7a 67 e8 15 78 1c 4d 4e 2b 2e 16 9d 80 ca e9
36 68 72 a7 92 af bf 3c 0c d5 1c e2 8c 70 e8 6d
41 eb b9 75 2f 3f 92 db a5 1a db b6 85 1b 1f 78
45 61 a8 f1 97 20 8f de 02 97 0b 38 f2 a9 74 22
ec 7f 4f c8 a1 06 75 a9 db de 10 9e ed 0c e0 65
27 70 3e e0 5b 65 7d 34 08 f7 fd db 1e ec cf fa
e1 6b 1d a1 07 30 7a 2c f2 56 fa 60 e8 15 21 72
de 9f 95 27 fe 92 0a 90 1d 93 c4 f4 d5 7e 54 6e

# PKCS#1 v1.5 Encryption Example 1.8
# ----------------------------------

# Message:
37 32 36 b7 20 2d 39 b2 ee 30 cf b4 6d b0 95 11 
f6 f3 07 cc 61 cc 21 60 6c 18 a7 5b 8a 62 f8 22 
df 03 1b a0 e0 e8 2d d2 f3 e5 d3 1e 4e a2 57 b1 
5b 

# Seed:
21 99 74 a0 87 f0 a2 81 93 e6 49 a0 4a e9 d8 4c
f2 c8 a9 a4 6c df d8 f1 ac 62 c7 e0 f2 0f 4e 27
03 0c 72 b2 0a 5d b7 25 b4 a8 68 3d f5 55 6e e7
94 7f a0 a7 66 1b 6d 99 dc b7 9e 49 4f 46 73 f0
73 a0 41 dd 90 7c 87 32 4e 86 25 dd

# Encryption:
1a 6d f5 75 99 84 f2 c4 11 93 55 c5 db 35 c8 a4
78 16 4d 5e 5e e7 7b 49 91 f1 04 ba 91 b9 87 0f
15 91 be 1f 19 f5 5a 80 51 a6 2c 0e 59 49 3d f6
f0 0f e5 0e f7 3a 6c f0 c4 35 41 f5 32 0d ab 7b
2f c6 7c 93 22 5c cc d6 d5 03 47 aa 96 9a d8 7b
d3 d8 20 81 45 4f ea d8 10 dc 1a b8 c2 17 81 f7
61 2e 64 06 72 9b 32 2e 04 b1 62 4f 85 38 98 56
59 ae 34 d9 93 1e 01 9f 76 2c 79 7d 5c bf a3 2d

# PKCS#1 v1.5 Encryption Example 1.9
# ----------------------------------

# Message:
97 e0 b6 36 bf dd b8 e1 c8 a9 cf 5b 30 5c ef 3a 
8f 47 f9 a8 b3 34 4f 13 55 fa 3d ba b6 7b b9 72 
10 

# Seed:
c1 f8 9b cd c5 60 40 d5 e6 63 b7 4b fd e2 39 7b
58 46 08 cd f3 2d 5a 58 44 72 4a c7 e5 98 a8 6e
f7 11 4b 1b e0 87 30 cb 10 f6 61 fb fb 86 09 f7
c6 c3 74 42 a1 b1 1e 04 a6 11 ff 8c a5 ce 9a fc
1e b3 aa a9 2c 9f 28 ba e2 20 44 05 78 99 02 26
07 83 78 c4 93 41 13 74 c5 3e 3d dc

# Encryption:
87 3c c4 dc a4 27 97 2b 63 c6 cc 8a c1 1c cc 33
c9 59 f7 fc fe 4b 45 bb d4 7b 29 d9 c9 88 c0 1a
96 bc 1e ae 0e f9 b1 94 8d ce 2c c9 f0 aa 91 7e
86 a6 c1 1f 8d a3 da 29 df 90 59 01 f1 91 8a 76
16 8d e1 17 5e 27 35 12 8c 09 72 99 d6 6e a5 cc
f9 b9 5b 36 92 ee bf c6 ea 11 bc 37 09 1b 79 5f
18 80 3d 70 e7 95 58 e1 25 16 23 0f ed 55 15 e5
1b 45 ae 86 ce fe 47 b9 37 90 e4 99 4d c4 1e 05

# PKCS#1 v1.5 Encryption Example 1.10
# ----------------------------------

# Message:
82 b5 07 5b fc 88 f4 00 65 76 aa 80 a0 00 7a 74 
51 18 4d 4f 76 0c f9 24 28 04 22 2b 0e 07 26 f5 
55 03 0e 4e 6b 01 f7 93 b0 97 01 81 27 82 4a 3e 
40 24 57 d8 49 5f c0 

# Seed:
d5 5e 3d 48 97 e9 d8 65 01 4c b1 5d 3e e3 f9 fb
d2 9c 92 e5 c2 37 dc ae 46 67 2a 46 3e aa a4 f7
dd 09 86 1e 94 6a c6 5b 85 62 50 63 93 b8 51 92
ad 41 fb 0c 48 c8 c0 52 98 1b dd cd 5f 1f c8 b1
39 cd 47 ca cf fc

# Encryption:
2e 83 c3 d2 88 01 5a 5f 50 3d 3e 5d e7 d2 ad 91
06 54 5e f9 7d 63 e4 d0 6a 5a 0d 9d bc 29 f6 ba
fb 93 a5 17 3f a5 06 3a 69 39 db a6 c7 a4 28 c3
5e 7d be 6a 95 93 fe 5e c4 c1 98 78 89 3f 31 37
09 c8 76 02 72 6c b3 25 5b e7 5a dc 7f 2f 27 e6
db 91 c3 a3 43 ea ff 1c 28 d9 d5 f7 cb 65 74 e6
31 06 90 03 cd cf a0 77 43 a7 34 0d 58 83 9e 70
8b f3 6a f6 34 2d b8 df a4 1f ea da fc 69 53 ac

# PKCS#1 v1.5 Encryption Example 1.11
# ----------------------------------

# Message:
36 48 c3 6f 85 1f 52 f2 32 87 79 09 19 85 a3 c8 
12 e1 8a 70 55 d0 90 bb f0 32 4c 13 79 3b b8 22 
1a 57 

# Seed:
c1 27 71 85 c3 59 55 ca ed fd f9 de 55 d5 d9 5a
39 8d 58 f5 f3 33 19 1f c0 29 45 ef df ad fb 6d
b0 5e e2 a2 d3 41 83 ed f8 9a 1a 4d cc c4 65 91
b3 53 2b a7 03 93 62 c7 5d f1 94 ec 10 64 48 af
b7 f6 bf b2 80 7e 38 3e 15 99 54 25 5e 82 7c b9
da dc 8d 9b 7e 68 a1 aa 09 76 35

# Encryption:
88 62 f1 97 3f ef e0 af 02 d9 6c c4 58 33 4d ed
6c 02 d8 d7 ea f5 93 77 9c 5d 38 6c 4e 49 f7 68
f1 30 b4 87 b3 c9 1e 32 3a 47 7e 4c 11 0a 33 41
ff 46 ee e3 7c 77 3e 5c 0a c8 39 bc 55 cc 0c 07
0c ac 01 cd 45 18 3c fe e6 b8 8b fb 82 36 1d 35
60 19 7c de ab 42 e5 c7 55 d2 37 97 1a 88 da f6
10 cb 39 52 61 4b 36 40 56 cd 49 14 20 ef fe 3a
0b 8c e3 1f 2e 3e 49 ca d6 f3 b0 64 0f 44 91 de

# PKCS#1 v1.5 Encryption Example 1.12
# ----------------------------------

# Message:
94 f7 8c f4 5c 53 fc 46 e7 eb 1b 26 61 8a 29 e9 
48 50 12 c1 

# Seed:
e6 de 9e 9b 90 22 a5 5f 56 12 1d 5a c0 0a a6 df
29 9c 8a 36 94 22 e7 54 29 56 b6 da 2e 0d cd ee
96 8d b0 7d 99 5a 7b b8 76 f7 f8 cd 66 b2 f5 42
c0 53 08 f7 49 83 a3 f8 36 0c 6b 89 47 f8 7d 60
8b 03 1a 2c 68 dd e1 47 1a e4 96 ae 9b 16 e2 a8
11 81 eb 6f c2 f6 5b aa ad da 64 22 a9 34 31 f6
f3 b0 7b 5b 46 a3 cf 89 48

# Encryption:
3c 6d 3b 43 d2 3a db 79 d6 97 23 38 08 b0 74 48
76 97 f3 35 fd 99 cd e8 65 41 1f b1 82 28 92 56
1f dc 24 a8 b8 bb 2c 4f 65 3c 4d 15 6c 77 a7 5d
e3 16 00 b5 70 9e 8d 50 6e 98 e1 d3 73 cb da 01
f4 d9 fe b0 29 71 98 ca d0 ca 2a 7e 3b 1e 63 90
3b 10 43 ce 79 49 4c 57 54 f7 f9 0f c1 f0 73 a6
19 92 9e f1 26 39 4b 06 24 f3 b8 ba 6d 56 45 e9
90 e7 c0 13 2c e2 12 31 46 fd 9c ad f7 45 ec 61

# PKCS#1 v1.5 Encryption Example 1.13
# ----------------------------------

# Message:
77 9d 1e b8 4f a2 84 c3 7d 29 d5 e1 79 d0 03 06 
b4 13 c4 4a 80 0a 07 7e 59 85 3f 63 05 f9 2e 59 
fb 7f 81 

# Seed:
c3 e6 d1 8b da 97 78 2c a7 81 fa 76 d9 7a 6c 94
d8 54 d1 41 99 b4 ea 7d 82 c5 bc bb e6 cd aa b5
25 57 47 44 3c 59 bf 8c 77 ec da a6 4a e7 ce 61
e2 c7 30 01 32 b7 54 e9 16 2f 7c de 75 8f 48 0a
e5 88 cf d4 4a 94 6d 64 e2 72 0a 2d 17 52 55 45
22 04 84 83 81 18 ad 6e 6b 54

# Encryption:
72 2c a9 25 66 c7 3c c8 5d 19 ce 3f aa 14 cb 2e
79 84 9f 20 50 92 d1 58 92 82 31 3c 04 27 f0 67
79 8a fa e3 e3 f0 a5 61 f3 99 34 6e 9d 10 7d a0
4a f4 4b 0c 6f 04 4a de fe 09 7a 0c d1 4a 47 a9
9c d9 81 9a 98 41 37 06 30 7c bd 0d a0 16 97 46
9e ff 71 d3 14 41 63 94 93 fb ed 8e ee 1b a3 9f
dd 07 fc 0e a0 82 30 18 61 79 f9 0e 7e f1 3c 61
ee 56 f1 67 fc 2f 6b 15 79 3e 1a 32 24 ff a2 9e

# PKCS#1 v1.5 Encryption Example 1.14
# ----------------------------------

# Message:
88 

# Seed:
c0 16 9d 76 e4 ea 45 45 41 0d df 66 46 c1 ba 7d
d2 72 d7 c3 49 8b a6 b0 80 4b 42 61 30 a8 0f bf
ff 4b b7 b5 f5 59 b0 a9 09 0e 4a d9 b9 f4 16 a6
df dc 15 01 b1 ba 46 87 7b 1a 96 fa 84 91 dc fd
de 50 eb ee d2 4d 3f 98 96 24 13 34 6e d4 a3 39
3e 23 5b 77 bc 1e d6 74 68 ec e2 79 2a 2f d3 a8
34 8f c5 50 9b 59 06 f2 88 56 15 df 8c 14 61 37
7a 74 1d 59 52 fb 36 ea c0 20 1e 27

# Encryption:
4e d7 d1 29 1a 03 36 65 4d 5c a2 b1 f9 d2 0c b2
da 72 26 f7 11 6b 93 09 88 43 9b 44 63 98 11 04
bd 63 c2 ce 2b 77 d6 26 b3 10 9c 93 14 03 ac 5b
49 b4 24 7c 4f 69 67 b8 c0 db 06 3c 99 95 af 9d
36 54 06 50 93 8b 01 f9 39 06 f9 83 89 07 ed 59
36 ab b3 43 b0 e2 55 09 a1 d2 d4 c5 c8 b9 58 06
50 da 2c e1 1f a3 cf 3e 64 07 23 ea cb ee 87 fa
d3 ee 35 95 8b 45 07 5d f7 81 c4 13 46 6a f1 39

# PKCS#1 v1.5 Encryption Example 1.15
# ----------------------------------

# Message:
a2 dc 08 77 78 d5 43 40 8e 89 73 f1 36 31 59 ed 
b8 f0 78 3c 45 70 89 07 2d ce 66 d3 10 2b b4 fa 
e6 0a a6 0e 41 93 3c 48 a1 be 39 53 ec 2f 80 4c 
0c c9 6c 

# Seed:
18 11 20 14 06 53 29 d0 4b fb 0c f5 44 dd 38 d7
be f1 54 9b 4a 49 a1 67 76 dd a7 4d 0a 7e dc 49
67 b4 24 0c 37 14 2f a3 f6 63 9c 26 96 cd 7d 4b
18 a1 1e 2f b5 40 81 f2 de 5b d7 bd 15 cd da 92
c9 4c 3a 47 18 7b 5f f4 3b 53

# Encryption:
71 15 19 0a 21 04 88 f0 4f a0 c0 0c 93 a4 68 a8
03 0b 7b 9f c1 00 20 d8 31 0b cb 01 a5 c8 dd a1
d0 6c e2 41 dc 77 5b 43 e6 f1 3b 19 ab fc bf 36
16 e8 4f 10 7c 9e d8 0d 1b 86 bf 87 c9 8c 2b 62
9f fa da a6 ec 01 f4 e6 75 55 8e f5 26 06 ea f1
26 06 8f a7 53 4d d1 3b 92 0d 23 81 69 5a d7 75
ff f0 bb 7c ec 46 90 90 1d 6f 1e 17 36 b8 2c fe
3a 0c 22 4d 18 f1 29 15 fd c9 5c 18 39 7c 35 70

# PKCS#1 v1.5 Encryption Example 1.16
# ----------------------------------

# Message:
11 0b f2 b1 d0 dd 81 2f 2a 5a 21 f3 40 4f a2 f2 
c4 54 c4 43 2f df a7 0f 1b 0f 23 ec 69 c1 02 37 
73 a7 3a ba 

# Seed:
17 b8 5f 76 53 ff 0e f5 de 7f 25 69 6c d4 90 23
ad 8e ac 94 8a 83 e2 24 58 ec d5 d1 0a 43 86 6d
c7 91 55 5e 64 f0 78 c3 8c 75 2b 6e 9c 6e ee b3
39 ee c9 10 16 d2 58 88 6d 01 27 75 ad 64 36 02
a0 f0 d1 79 34 54 a0 60 94 71 16 22 88 22 39 50
82 6e d8 e2 02 5d a9 a4 e9

# Encryption:
5d 0f 2f d8 5e 6f 9d 9e 43 2f ad 86 0f dc 49 96
96 24 ff 4f a0 71 5d 36 1e 9f 00 b0 5b 3a a0 ba
9e b2 7b ae 61 0e fd e1 14 3c bc 93 3b 52 de a7
01 87 60 bb 25 1b e0 e1 e3 0c d1 c5 99 1a ef 74
4d b8 2f 16 6b 90 63 ef b7 e3 38 40 a2 56 90 05
65 4b 14 0e 11 5f a5 6c 30 40 6e 45 65 6e 81 99
af 39 4f 63 86 34 6d 5f 1a 30 0b 95 ba 48 fc 08
73 d6 18 d6 92 bb 02 5b f1 5e 9d 23 2c 64 1a da

# PKCS#1 v1.5 Encryption Example 1.17
# ----------------------------------

# Message:
d9 d9 37 13 1f f1 94 0a 86 bf 71 39 b4 81 14 36 
41 95 b4 00 52 22 a8 bb bc 26 1a 7f 2e 21 2b 8d 
d0 35 e5 3f 91 44 f5 61 0b 4c ec 32 ea 01 bd a9 
d3 c8 0c f2 94 64 f8 0f 5f 56 56 c8 

# Seed:
33 05 e1 da 60 e5 86 73 fc 46 cd 33 be 2a 66 d3
a1 02 c3 db 16 1e f4 8c 0d 60 ef 25 03 1b 40 14
16 78 26 24 6a a5 28 a3 a3 e5 b0 ab 95 07 8d 84
01 d9 29 03 59 5a fc 1a a8 54 e6 04 4e 5e b5 f5
be

# Encryption:
0e 12 16 74 89 f0 ba ef ca d6 39 34 bc 15 9f 1b
bd 9e 9b 28 7e 50 0f 49 09 23 c1 6a 85 56 4a 1d
a6 36 59 37 5f 22 af 7b a4 97 98 e1 57 8c f3 15
fa e3 e9 ed 56 99 c6 91 e3 c1 d0 bb 46 da 49 2d
01 34 9e 93 29 59 3d 43 81 d0 74 a0 a5 31 df 92
1b 31 31 6f 7e 2b 4f e9 15 34 72 83 24 23 35 f0
b0 b2 31 92 c7 21 02 f2 c6 36 24 b1 e7 89 65 45
0e 82 30 d4 87 7e 46 17 b0 3d 44 83 13 98 dd bf

# PKCS#1 v1.5 Encryption Example 1.18
# ----------------------------------

# Message:
81 b3 4a eb 8a fb 8a 3f 

# Seed:
02 f7 48 34 2d 01 0f b5 6a 6f 69 f2 1f 8c 6a 63
16 79 c8 c4 b8 f6 fe b5 25 cf 8e 72 fd a8 ef 8d
f6 62 31 28 c0 fe 74 bc 59 0c aa 34 f1 ee d1 ad
2d 61 42 dc c5 bc ae 84 ef 31 37 62 f2 e4 e7 03
03 d2 09 c8 d9 57 7a 7c 84 3d 2b 91 72 ed 4e fe
2a d6 29 61 4b 99 a9 1a 4c c8 32 5b a3 24 11 6e
cf 0c 5e 29 09 49 38 ae 49 89 84 f4 f4 cb b1 62
38 86 e0 39 73

# Encryption:
43 b2 76 c7 d3 68 ea 21 c6 80 71 16 cd e8 60 82
98 f2 40 02 07 2d 77 6e 56 e6 2c 35 72 bf b9 9d
a4 c5 6e 93 8a 47 dc 07 5f a1 ff 7a 61 8f b5 fa
ed 3e e3 7b 91 df c3 91 53 49 5a eb a9 df 6d 45
df 94 b0 e8 a8 ad 2d b3 7a 9f e4 6d 0f df 15 42
31 fd 6f 32 21 47 4e 8f 5c 19 1f db 85 38 e1 a6
03 e5 98 97 e1 50 fa f9 5b 65 da 14 06 67 ed b1
98 09 eb 4a 16 ac d0 1e cc 60 4b da 57 f2 0e f2

# PKCS#1 v1.5 Encryption Example 1.19
# ----------------------------------

# Message:
7b f9 fa d8 89 de 73 ed 87 3d 

# Seed:
fa 5d ad 45 a4 bb 5e 74 c4 cf 2e 21 3e d4 0a a9
61 75 98 d1 1d 49 ae 1c 32 d7 94 e0 9c d0 e5 c7
aa ae 81 b9 55 4d de 31 08 d6 0a 9a 82 f4 2c c6
c2 a6 89 f4 60 ff 1d 53 ad 85 bf 83 83 11 e7 58
9e 19 67 95 7b 51 56 7a a0 d3 3a fa 37 52 cd e6
c5 66 1d 4f 27 ae d3 df 52 90 5f 1c f2 25 33 04
61 8e 07 86 04 1e 70 b4 dc ba c8 c1 08 ba 34 ac
39 39 f4

# Encryption:
61 c2 55 0e 0b 36 a6 79 7f f8 64 93 80 1b 11 46
d8 90 59 49 83 52 e4 c2 62 27 5b 14 04 b1 33 15
e9 56 bb 3d 31 21 85 b5 21 b3 c7 08 e9 d9 54 02
17 19 a0 59 d9 84 72 4c 53 c0 4f 5a d2 74 be f9
ff 0a 79 50 b2 fd ec c5 29 0c d5 f3 bc 26 52 4c
d1 34 20 48 18 4b 0e 2c df 94 06 a4 53 a2 ef 9f
3b b2 3c 4e 7c 1c 8b 29 52 a0 20 2b cc 23 82 47
ea 32 7b 8c 07 00 c8 00 3f d6 34 ec 1e d9 bf 30

# PKCS#1 v1.5 Encryption Example 1.20
# ----------------------------------

# Message:
b6 a3 50 9b b3 b9 b0 b5 7c d5 8d e4 09 d9 53 20 
1a 04 2f 94 92 dc 1d 7e 34 a7 d0 94 1a 1a 1b 

# Seed:
3a 9e 15 06 57 86 b3 e0 1d 82 6b 86 2a 8f 70 2b
5c ac 8c 16 62 ee 7d 15 ff 32 3c df e7 1e bf 4a
d1 b1 f3 a6 bc bd d4 b0 01 08 77 ec ac 09 1f 61
59 08 e2 be 40 0b b0 c4 98 e3 55 d5 71 fd 10 89
5b 8e e9 c3 a9 c3 1e 4b 11 03 89 c3 d5 c4 6e bf
76 b3 b3 5a d1 f4 79 1b 6d 20 97 f1 09 f2

# Encryption:
6b 4b 6d 7b ab fe 4d 64 17 ac ad fb 78 57 2e 7c
87 e3 fe 1b d5 8e ef b0 d4 b1 27 9c 7b 7c 83 26
a6 8b b2 87 95 e0 9f 9b 1c e2 e2 4a 53 9f 4b 0d
93 b2 92 74 ce cf 7c d9 f0 b7 32 ae be da 91 11
bd fe 25 e2 68 a8 8e 34 22 e2 9b 52 bd 4b 7a 05
47 db 8f e1 2a 6f cf 1a 3c 06 a0 02 bf 87 0a 2f
ab b7 c4 57 e4 bb ce 3e 31 6f 72 32 44 9f 87 a9
d7 02 b1 2d 19 bd e7 f9 59 0f 94 67 b0 6b d5 8a

# =============================================

# Example 2: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
98 b7 05 82 ca 80 8f d1 d3 50 95 62 a0 ef 30 5a 
f6 d9 87 54 43 b3 5b df 24 d5 36 35 3e 3f 12 28 
dc d1 2a 78 56 83 56 c6 ff 32 3a bf 72 ac 1c db 
fe 71 2f b4 9f e5 94 a5 a2 17 5d 48 b6 73 25 38 
d8 df 37 cb 97 0b e4 a5 b5 62 c3 f2 98 db 9d df 
75 60 78 77 91 8c ce d1 d0 d1 f3 77 33 8c 0d 3d 
32 07 79 7e 86 2c 65 d1 14 39 e5 88 17 75 27 a7 
de d9 19 71 ad cf 91 e2 e8 34 e3 7f 05 a7 36 55 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
98 b7 05 82 ca 80 8f d1 d3 50 95 62 a0 ef 30 5a 
f6 d9 87 54 43 b3 5b df 24 d5 36 35 3e 3f 12 28 
dc d1 2a 78 56 83 56 c6 ff 32 3a bf 72 ac 1c db 
fe 71 2f b4 9f e5 94 a5 a2 17 5d 48 b6 73 25 38 
d8 df 37 cb 97 0b e4 a5 b5 62 c3 f2 98 db 9d df 
75 60 78 77 91 8c ce d1 d0 d1 f3 77 33 8c 0d 3d 
32 07 79 7e 86 2c 65 d1 14 39 e5 88 17 75 27 a7 
de d9 19 71 ad cf 91 e2 e8 34 e3 7f 05 a7 36 55 

# Public exponent: 
01 00 01 

# Exponent: 
06 14 a7 86 05 2d 28 4c d9 06 a8 e4 13 f7 62 2c 
05 0f 35 49 c0 26 58 9e a2 77 50 e0 be d9 41 0e 
5a 78 83 a1 e6 03 f5 c5 17 ad 36 d4 9f aa c5 bd 
66 bc b8 03 0f a8 d3 09 e3 51 dd d7 82 d8 43 df 
97 56 80 ae 73 ee a9 aa b2 89 b7 57 20 5d ad b8 
fd fb 98 9e c8 db 8e 70 95 f5 1f 24 52 9f 56 37 
aa 66 93 31 e2 56 9f 8b 85 4a be ce c9 9a a2 64 
c3 da 7c c6 86 6f 0c 0e 1f b8 46 98 48 58 1c 73 

# Prime 1: 
cb 61 a8 8c 8c 30 5a d9 a8 fb ec 2b a4 c8 6c cc 
c2 02 80 24 aa 16 90 c2 9b c8 26 4d 2f eb e8 7e 
4f 86 e9 12 ef 0f 5c 18 53 d7 1c bc 9b 14 ba ed 
3c 37 ce f6 c7 a3 59 8b 6f be 06 48 10 90 5b 57 

# Prime 2: 
c0 39 9f 0b 93 80 fa ba 38 ff 80 d2 ff f6 ed e7 
9c fd ab f6 58 97 20 77 a5 e2 b2 95 69 3e a5 10 
72 26 8b 91 74 6e ea 9b e0 4a d6 61 00 eb ed 73 
3d b4 cd 01 47 a1 8d 6d e8 c0 cd 8f bf 24 9c 33 

# Prime exponent 1: 
94 4c 3a 65 79 57 4c f7 87 33 62 ab 14 35 9c b7 
d5 03 93 c2 a8 4f 59 f0 bd 3c bd 48 ed 17 7c 68 
95 be 8e b6 e2 9f f5 8c 3b 9e 0f f3 2a b5 7b f3 
be 44 07 62 84 81 84 aa 9a a9 19 d5 74 56 7e 73 

# Prime exponent 2: 
45 eb ef d5 87 27 30 8c d2 b4 e6 08 5a 81 58 d2 
9a 41 8f ee c1 14 e0 03 85 bc eb 96 fb bc 84 d0 
71 a5 61 b9 5c 30 08 79 00 e2 58 0e db 05 f6 ce 
a7 90 7f cd ca 5f 92 91 7b 4b be ba 5e 1e 14 0f 

# Coefficient: 
c5 24 68 c8 fd 15 e5 da 2f 6c 8e ba 4e 97 ba eb 
e9 95 b6 7a 1a 7a d7 19 dd 9f ff 36 6b 18 4d 5a 
b4 55 07 59 09 29 20 44 ec b3 45 cf 2c dd 26 22 
8e 21 f8 51 83 25 5f 4a 9e 69 f4 c7 15 2e bb 0f 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 2.1
# ----------------------------------

# Message:
e9 a7 71 e0 a6 5f 28 70 8e 83 d5 e6 cc 89 8a 41 
d7 

# Seed:
16 8e 3e b5 80 9b 08 70 e1 f2 48 7e 1b e7 7a 17
6b 34 71 6d e1 41 ba 4c 90 59 da 90 e5 e5 1a 36
94 e8 58 fe d1 0b 92 6c 02 52 39 80 a8 90 9d a9
96 c6 43 33 ea 67 67 87 bc e6 77 f1 1f da 77 db
b1 a9 51 6e dd a9 b1 29 4f c2 e4 50 52 22 88 e9
30 be 7f a7 29 b2 50 e3 aa c5 20 51 1e 95 16 aa
86 3a f6 bc 07 5c bd bf f4 30 46 70

# Encryption:
71 c2 b8 fb 38 19 f1 34 c2 24 7c 6b ab b4 cf be
17 d7 b2 64 3f 87 ac e5 c5 71 27 7b e1 90 8e f3
a5 28 8e 34 38 4e 46 0a 70 38 6e 7e a1 d1 9d 3d
ca 1c e1 5b a9 32 39 a8 cd da 18 e3 17 fe 07 96
80 ce 7e 6a c6 d9 bd af 86 cb 9a eb f1 cf 46 cd
10 ef 6a 68 8b 0c b2 ce 76 5d d0 b3 25 20 42 39
66 ee e1 aa 05 c6 c2 8c 6f 35 24 fb 68 6b 5f b1
58 53 65 9e 58 3a c4 37 21 9d ef 8e dc 58 be 2d

# PKCS#1 v1.5 Encryption Example 2.2
# ----------------------------------

# Message:
66 4b f0 5d 61 2b af 61 52 4c 60 8e da 36 fc 6e 
a2 c9 3c 14 31 53 22 1b cf d6 ba 0c fb bd 6b 64 
14 47 e4 78 8b 0a 46 2c b5 b3 f9 fa fc 9a 75 

# Seed:
e7 f0 a2 79 18 ca d9 15 da 28 11 36 59 ff b5 df
a0 b5 1b 24 d5 a7 1c 20 27 f8 e4 d9 40 9e 8c 64
72 f0 c5 4b 5c 08 85 8d a6 3d 4b 81 72 b0 7d cf
8c 5a 7e 8f 9e 90 f0 17 c2 4b 44 d1 6b 67 0b dc
96 03 0c 83 53 a2 83 9b a4 c0 75 d2 4c 20

# Encryption:
06 86 90 18 13 db 05 3a c7 08 e3 fc ec 6b ae 03
60 08 8f d3 44 e9 d7 ea 11 8b b3 f5 37 53 14 25
1e 60 67 37 f5 82 4b 36 28 f6 65 03 48 f6 ab 55
3b 27 7d a0 15 44 d0 56 73 ba ed f4 55 cc 03 32
f6 13 f6 54 78 fc fe 06 67 34 c4 65 58 bc 23 3b
4b 6f 52 41 e4 f4 ac 53 fc 18 c5 53 84 c8 fd 96
18 3f 0b b5 51 5e 89 31 14 f9 c6 1c cc 11 fc 19
83 de 74 46 92 64 db db b0 c7 49 17 4e cd fb e3

# PKCS#1 v1.5 Encryption Example 2.3
# ----------------------------------

# Message:
5e 76 e6 6e d5 75 41 fc 23 d3 59 f4 ad bf 3f 56 
82 01 d3 c6 f0 e0 26 aa a5 67 63 56 cc 98 66 f1 
75 5d e9 8c b3 9f 23 6d af a9 e6 bc 79 4b 74 43 
b5 3a 2d 85 

# Seed:
5c 65 68 b6 e3 3b c1 3a d2 dc d6 01 2d 17 da 81
b1 3d bd 62 aa e4 0a 64 af 97 e2 19 e7 5d c1 81
12 60 77 d1 20 dd a1 9d 63 12 cf 1e 98 71 c1 15
f0 86 7f e6 62 d7 8a 40 31 97 6b dd ef 68 f5 2b
68 99 58 67 cd 80 95 05 dd

# Encryption:
19 e7 99 66 ff 1f bc 10 07 3d e7 3d f3 a5 31 63
78 74 e4 7d f6 39 25 6c 51 d0 bb a9 35 61 0b 46
34 f9 e5 b4 68 9b d9 21 73 5b 32 23 6e fc c6 e7
cc 49 a9 e0 6a 25 ac 96 59 b7 fe 82 9c b3 e8 b0
1f 10 31 79 42 23 65 74 1b 76 c8 34 21 49 ce dc
76 eb 0a d0 18 ed 42 35 fb d5 24 fd 87 c9 54 9a
b3 3f f2 3e e4 f8 20 0e fa 33 02 7e 9d ee c6 0f
ac 01 3d 1e 56 e6 e3 33 d4 93 a4 a9 46 0f e5 8a

# PKCS#1 v1.5 Encryption Example 2.4
# ----------------------------------

# Message:
5b 19 50 48 eb 90 eb 47 93 

# Seed:
bb bd 49 55 0e d1 ea 1b 6b c7 20 6c e0 b0 03 a6
32 a5 2b 0b ac 5f 32 71 0b 39 fa 64 b3 55 6a d6
f6 c8 2b d9 d5 31 b3 07 46 9e 86 3f 54 b5 fe 21
83 05 69 54 f2 a9 67 e4 dc 2b 32 6e 41 dd df 74
3a 76 4f 7e 82 88 68 29 a8 fa b2 77 2a 34 97 70
6b 95 38 a9 f8 42 96 c8 2d 9b af c2 9c 39 d4 68
4f 75 ff 6b b1 c1 2e 39 bb 80 56 af 2d 24 34 4b
2c ae 46 29

# Encryption:
42 c9 cb 68 21 b5 5d ae 30 d9 00 25 75 31 12 e6
ee 02 f4 ad 6f 0f 5b 3c c4 95 2a 12 7c 8a 16 f6
64 79 b8 14 4f 3c f2 9d 84 e4 3d 67 d6 77 12 c7
f5 b7 6d a2 c6 6b a0 e9 0c d4 b1 fc 1c 1b 3f 17
a3 92 e7 04 08 28 8a f6 9b 50 fe 8a 50 b3 29 6a
0d ab d7 c8 dc 39 84 a1 94 06 88 be 70 98 25 16
20 25 6c c2 1b 7c 76 ed 29 d8 6f f7 c0 1e c2 87
df 47 38 be 34 69 b3 0a 3f 8f b7 be 83 d9 36 1a

# PKCS#1 v1.5 Encryption Example 2.5
# ----------------------------------

# Message:
66 0b bd 40 06 9c c6 7b ad e4 1a 09 ec f4 3c c4 
51 3f 7c 7c c0 2d de 97 2d 2b 1f 29 29 5e 09 b9 
91 0c 59 ed ba 0e d2 dd f1 1a 6d 41 69 35 1f 97 
24 07 33 52 8f 91 b2 68 fa a7 af 90 6e 

# Seed:
30 7f 61 b1 83 a8 dc f9 15 5a b2 35 e6 1f b5 6b
a2 b8 79 5d c4 23 53 85 e8 ac f3 66 d2 52 33 b4
70 e0 5d 70 11 b6 fc 53 2f 0a 65 8a d1 3a fd 29
0c 6f 30 e2 79 5e e3 d3 9d bd c8 0f 56 0e ce 2f

# Encryption:
04 9b 26 05 0a 3a be f8 3e c2 77 61 11 e3 b7 2f
b9 a2 d6 a8 01 05 5d 6b 5e 0d a4 e9 5c cf 2e bd
0a 78 6a 97 21 aa 79 25 bf 15 be b6 27 13 a3 13
87 7d d8 5d 26 58 b2 08 e8 8e 64 45 fc 35 01 9b
0c ad 6b f4 d0 6e 2c a5 f1 19 49 ee ee 7e e4 7f
1d 5b 4c 88 24 1f 50 e4 d6 ed f0 18 3d 4f a3 5a
37 1f c4 07 36 4f 2d ca a4 cd ae ce fc ea 6d fa
c1 d5 13 f9 05 e7 47 94 47 44 bb 64 57 6b a1 c8

# PKCS#1 v1.5 Encryption Example 2.6
# ----------------------------------

# Message:
81 cb 0a 97 69 8f 82 3b 56 b4 5f 

# Seed:
93 8c 8d fd a0 8b 89 05 5b 68 af 01 1f 24 6c ec
1f 93 a2 77 1d a9 7d ba 20 95 4c 90 09 12 28 5e
5d b1 87 b2 9e 32 72 e9 9e 69 4e 12 14 17 25 28
45 30 84 06 4e 5c 60 f0 1e 78 6f c5 d0 d9 af 06
39 a4 98 c5 7a de 93 77 60 ae 51 74 84 af d7 02
5e a0 d5 5a 62 b1 1f 9a ab 7f a5 dd d0 93 e5 ea
ba d6 1b 67 a2 95 a7 75 be 96 c6 b7 6e c3 fe 47
29 50

# Encryption:
7f b8 f3 35 ee dc 4a f6 af 44 07 3d a1 96 45 7d
04 61 45 03 01 47 f8 42 0f c7 9b d5 89 77 4a 73
0a 6d 94 fb 7e fa dc 5a ee a7 c0 70 f1 89 24 91
25 e1 66 c6 d3 01 29 ec f2 c4 82 2a 50 49 6b c2
f2 1e 79 ac 57 db fb dd 71 a6 8b 58 d9 05 1b 48
0b f4 77 48 a1 3d fb 67 3e ae d7 71 0a 46 8f e7
2f 7d 74 e6 f4 a2 89 44 04 3a 52 d9 30 de 68 db
cb 6e e7 fb 8b 69 64 05 41 e3 ed 5b 75 4e 65 fe

# PKCS#1 v1.5 Encryption Example 2.7
# ----------------------------------

# Message:
05 f7 83 56 23 c8 cf aa e4 82 a9 10 85 b9 7f 6b 
95 92 8b b9 74 ac ad 02 36 4a af 13 17 ed 53 c9 
db 2f fb c8 a3 cb 3a 00 f4 4d ac ef 78 

# Seed:
80 c8 3d 25 47 be 41 ba f2 32 1b d3 0a 9a b7 74
9c 5e eb b5 a1 ff f0 b3 1d 6b db 0a d1 6d d0 c0
fb 3e c1 57 e7 8b 09 86 60 20 41 cd e8 89 57 a5
53 29 e3 e2 cf e8 5a 59 44 74 94 5e fa 33 35 85
ff fd 41 eb b8 e7 c5 18 c3 c9 25 9a ea 8d e6 35

# Encryption:
87 f9 ce 05 f0 ac 9c 05 e4 5f b7 bb 55 5a 7a 18
a9 cd c5 5f 54 4a 54 21 01 e9 a7 1c d2 03 66 82
0e 7f f6 dc a3 46 75 22 9d 86 e4 fb 58 71 f9 31
0b 12 bb 74 e2 86 18 d6 d6 58 65 87 f6 6a cc 89
68 a8 3c d8 07 f4 d2 12 97 73 1d 7c 22 c1 45 99
e7 57 19 fd 23 05 2b 8a a6 5b 7e 9c 5c 02 00 38
2d 35 d5 60 f2 d3 3d d0 49 e0 6a c8 27 cb dd 9a
f5 81 a6 b2 6d b6 1d 43 d7 12 4b 34 72 1d f1 42

# PKCS#1 v1.5 Encryption Example 2.8
# ----------------------------------

# Message:
e2 e0 f6 b3 28 d9 bb e9 fd 66 cd 87 98 7c 11 60 
ed 23 7b 1c 7c 65 6a 89 fb 1f 21 d7 09 40 3b 04 
10 f8 e4 e1 2e b9 69 0a eb eb 38 07 31 9a 93 65 
64 f6 67 17 a7 1c 48 62 cc c5 6e 

# Seed:
cf 18 e6 08 b1 56 14 5c 44 de 31 49 66 cb cc 66
74 a4 5a e0 df 90 04 06 e4 0d 3d fc 32 2f 39 40
4c ee b6 dc 58 f8 01 bb f2 ac 4f 47 84 1a bd 79
61 79 d0 82 4f 3b f5 51 8d 78 cc 66 ad 8d fb ed
b1 17

# Encryption:
14 01 aa 21 ec 6e ba a7 e3 a9 f7 13 c8 6b 50 8e
37 5f 6c 12 5b 29 62 6e bd 34 9f 64 e2 0f a4 8a
1b 06 84 79 ff f3 30 22 f6 6f 86 e9 7d 9c 5e dd
90 26 e3 18 3c e0 86 41 57 06 59 35 2f 87 a6 18
91 f3 d8 6a 3d 24 5f 02 45 e3 9d 99 89 2c 67 fa
2b ed 8e 37 54 8d e2 3d ef dd 1e 43 d5 d7 e3 d9
a3 c2 2c e6 a3 68 d8 4c 5a fa 1c c5 bf 49 b6 8f
e5 c2 5a 32 6b 0e ec 5e 44 c5 e2 ff 5a 35 9d d1

# PKCS#1 v1.5 Encryption Example 2.9
# ----------------------------------

# Message:
c6 95 78 ea 03 e2 69 b1 b9 16 33 a7 2f 9f b4 d1 
0c 

# Seed:
e0 a1 a9 ba e3 0a 7a c6 6c ab 3d 86 43 3c 1c a5
e8 ac 2b 74 e4 83 ca 7f 34 59 77 16 ee 16 18 90
6c 97 77 2f 28 86 f4 6d 78 31 21 b7 fe 1b 8f a5
fb ec 09 c0 68 e5 63 5c 89 e6 a0 a9 ac cf 2b 12
c6 47 06 b6 ae 9a 5a 74 ab b8 3f 64 e1 3a 8c 53
f9 26 76 04 66 b6 45 e2 8e 9a d6 46 1a e7 b8 9d
5e fc cf 7d 89 14 9a a2 e6 9f 0d 25

# Encryption:
78 f8 7d 6b 06 76 1b d7 e7 17 e0 c5 eb 40 e1 fb
80 89 9c 7b e4 01 7c 2e fb 07 59 78 ee 38 d0 f9
5e 98 03 dc d4 0f ee 97 92 c6 1d 4a 2d 85 da bd
ea 96 ca 29 f3 ca 1e 8b cf 81 76 55 d0 c0 94 74
d9 80 94 eb 6a 7e f0 33 3d 69 71 c9 38 36 fe 02
32 f7 18 46 3d c9 54 18 53 46 3b c1 cf 03 67 7e
78 6e e5 2e 72 71 c3 c1 1a c0 05 53 c6 75 27 07
e0 df 92 80 c4 f2 b7 d1 9f d6 f3 d8 bb cc 7b e6

# PKCS#1 v1.5 Encryption Example 2.10
# ----------------------------------

# Message:
76 72 cf c2 7a 41 d5 01 aa 4c 41 ba ab f4 52 5a 
7c 45 5f c8 

# Seed:
4c ee a1 a8 94 64 a5 d2 f8 9e 07 89 53 ca f7 76
36 58 98 a5 bd 5e 8e 44 8c 65 da 26 ff 98 90 0c
d0 80 61 ef 44 6c 69 b4 8d c4 60 9e d8 65 4a 64
6d 70 82 62 cb 84 09 ac 27 c4 a4 9a df ed 47 a8
5a d4 29 ed 75 07 75 78 e4 c2 73 c6 1e 2c 3b 46
be b4 72 f0 a3 45 a0 5d 61 a7 ea aa d8 a6 3e 0b
3d 49 52 f2 7c 40 81 32 9e

# Encryption:
25 2b 14 13 3f 1d b2 50 13 29 35 01 e3 56 53 4f
26 af e3 34 68 8e 68 d7 91 83 3a 0d 82 56 05 70
bb b3 ce 2b 16 d8 b5 f7 f8 9e 7e bc 7c f9 c2 94
ab 34 16 b7 c2 11 87 70 7f e5 e7 99 2e 72 0f f9
58 da a4 0f 5a d4 5b c7 47 47 96 39 a5 37 fe 0a
4a 75 fc fb 45 a5 3f 01 73 af c0 f3 cc 91 0b 86
ae 31 37 62 8d 90 ff 67 5a e1 ae 31 e1 64 05 37
ea 1a 7c cc fb 73 f8 be 5a ec a0 3b ab 19 3b b0

# PKCS#1 v1.5 Encryption Example 2.11
# ----------------------------------

# Message:
a1 6a d8 f2 e0 93 23 42 ed 21 e1 37 77 f4 65 2a 
35 50 dd b4 36 8b 5e a7 1c 66 db c3 bb fe b7 db 

# Seed:
55 88 4c 83 0d 4a 80 b7 9f 08 9d a7 4d c2 5a e0
c4 82 46 21 45 e1 d0 95 23 da 3c 93 44 bb 97 b0
52 fb dc 15 43 df b5 3c f2 37 82 59 68 7c 7b 1b
35 ca f2 f9 19 99 ed 4a ce 39 af 10 d6 be d0 fa
22 44 4c 12 9d 90 74 1c fc da 90 19 8e 27 82 fb
03 bd cc 7c fa fd 89 db 6f b0 fe d2 24

# Encryption:
08 32 6a ff 6d 03 cc 4e 26 10 dd 53 6a f7 f2 1d
76 22 7d 82 7d 52 80 d8 b8 3a b9 eb 30 e0 76 9c
fa 02 b5 c1 35 2b f4 d1 70 ce b6 6f 8b e6 98 78
4e 1a 6c 20 3f a5 ab 90 07 a6 f7 fc 20 65 20 4b
98 2f a5 61 fb b3 61 af 2b 8e ea 42 ab 3f ec 0e
d0 86 22 e5 f2 89 80 52 75 38 0b 69 34 2a 96 f7
6a 99 04 87 68 90 d9 2f 24 00 20 32 35 1d 8a 1c
bc 3d 27 b2 46 48 21 bb fb fb b9 a6 78 51 96 10

# PKCS#1 v1.5 Encryption Example 2.12
# ----------------------------------

# Message:
4a 

# Seed:
8a a0 ce d1 7f 09 ad ae 61 0a 46 03 0d ad 40 31
1b f1 46 9c 27 37 41 1e 40 f9 23 96 75 1d d5 66
37 c9 58 db c5 8a 17 fd bd dd db bf 79 75 18 78
98 be 1a a6 3c 5e ee 5f 9a 19 02 98 0f 59 51 84
b9 b5 b4 65 b9 2e 20 f7 ae 8b 5a 5c ee 7f 3b 57
d9 97 a0 6a 70 2d 23 83 50 a9 26 98 ef 27 5d ff
52 77 bd 2c 99 96 47 40 5a db e4 fb 3f 1b e7 5e
15 9a 4c 43 83 13 b7 fd 8a ca 9d ea

# Encryption:
4c 4e 5a b6 2d 0c 96 7a b8 29 21 42 9f fe 50 d2
24 0e 7e 0a 18 75 48 87 55 bb 7f f6 15 a8 c9 9a
bc 37 b2 e4 71 47 a9 27 d7 b9 8c 30 db 24 da 8c
d3 5e 13 d7 b7 14 14 d0 32 bd 0c 3c e3 8b 89 b1
1b 2c 3f 9d 83 08 16 71 6a 2e 8c cd 8c 79 e9 c7
49 31 a7 b8 a8 db 13 12 8c e4 0b 21 59 e4 98 da
98 f2 aa 35 2f 23 85 31 06 b6 61 d8 8e d0 6f f6
6a 56 e7 56 59 72 20 bd 10 15 81 53 ce 5c 02 63

# PKCS#1 v1.5 Encryption Example 2.13
# ----------------------------------

# Message:
bf fc 42 08 73 f5 af 5d d2 3b b0 

# Seed:
dd 31 cc d4 7d 4e 31 02 df 0f c5 9b 1b 84 77 af
3a 78 c2 fa 9c 8e cb 4f 0b 3b de 23 50 04 36 55
03 64 66 5f 81 c0 35 6a bc 0b 78 e9 73 19 11 14
02 75 c8 66 f7 5a d0 cb bc 88 ad 6b 5d 4d a5 2d
08 e2 2e e5 39 b5 8e 92 c6 19 63 87 e2 21 a0 87
39 6c be 57 ec 56 03 f6 16 26 27 98 3e ff 82 de
04 8b dc 1b 5e db b5 d4 ea 84 f5 02 24 bd 88 a9
05 da

# Encryption:
0d a2 d6 f7 bc b5 0a 47 2e da 24 60 9d a6 77 28
e5 3c 98 80 aa 5f b6 fb e6 0d 83 c1 1e 6b b3 cf
db 17 d1 4d bc e8 ec 55 c7 3a b0 14 3e 9b 27 56
bb 69 68 e5 af 1a ed cf 6a 80 c2 6d 49 0e 47 18
7e a5 d8 cd 2f ac b8 1c e6 4a 72 3c 40 f0 ba 4c
69 3e 1b 11 43 df 15 a4 20 91 70 9a b4 c7 cd 9d
47 07 9e cd 68 f6 a1 96 44 8a 44 67 9a 04 14 10
41 8f 11 a1 e1 bc e7 8e 77 26 04 a2 f2 77 81 95

# PKCS#1 v1.5 Encryption Example 2.14
# ----------------------------------

# Message:
1a 9b 87 29 21 0a 84 71 fc 5c d7 09 f2 ed d3 24 
01 50 24 4b ec 96 a9 2f f8 07 e3 b3 0d 29 5d 3c 
34 5c 04 4f 2e 95 60 37 

# Seed:
58 78 c9 1b 16 6e 90 c3 4e 6e 66 56 8c 15 1f 4d
44 43 40 b5 f1 d7 30 52 cc 56 33 ea 2e 47 ac ed
7b 17 8a 64 fb 09 a5 ad 08 46 ae e4 11 6d 67 80
ee 75 eb 20 85 16 68 82 0c de c0 f2 c4 96 e4 c2
88 d8 27 9c 1c 5d 4e c0 0d 98 0c 27 2e 87 05 18
48 6d ca ea 85

# Encryption:
37 00 ac 36 2c f6 0e 16 39 47 a1 98 d0 0f 3b 3b
26 e0 3e e2 fb 78 2b 42 88 b8 c1 de 76 e9 e8 99
46 c9 80 7c 56 e0 9c 7b 52 be 00 78 ac f6 92 96
4a cb 97 d1 fa 5c eb 57 76 a1 d5 56 b4 bc 9d b0
0b da 25 23 7a 75 1b 7c 22 9b 6b 57 f7 ff 75 1c
12 d1 f2 2a 4f b0 e9 0b 63 d0 42 d9 49 9e 0f 7e
fe ad d3 c5 88 f2 c7 43 a1 2c 56 7c 81 57 8d be
eb fd 37 74 da 34 ad 09 ee be 90 17 89 02 14 b5

# PKCS#1 v1.5 Encryption Example 2.15
# ----------------------------------

# Message:
a6 d0 e8 c1 ea 4a b4 ec c8 95 7d 62 28 15 79 67 
5a 64 8d 62 b7 f2 2b 2b 08 d1 31 3f 40 6f 13 7e 
99 42 67 35 cd b9 37 2f ec a1 ee 78 46 3f a5 de 
9c dd 84 75 6c 68 bd 1d 92 ba 96 5f 50 64 10 b1 

# Seed:
1c 25 c9 b8 32 16 9a 1f db 6c 14 8e 47 e6 6c 3c
c8 21 41 e6 11 a6 f3 0c c9 0c 50 49 e8 c5 02 b3
1c ad c7 62 39 b7 bd af 93 fa 97 34 3e 7e e5 51
bc 52 fd b5 ec 9e 40 0a f0 5d be ac da

# Encryption:
00 e8 b2 fc 76 df b4 a6 cc 43 64 de 8f 68 3c 3f
cd 0a 9e cf bd 4a 5a 72 24 f4 9a e9 b4 f3 b5 cd
c7 1c bb 8c 66 fd 35 f3 d1 8e ca 98 96 7b d4 00
5d f7 91 52 41 6f d4 7e 56 2c 55 ed c6 d6 12 12
28 6e f9 75 bc c8 02 69 25 92 65 39 00 97 3c 72
e0 1a 69 3b 05 fc 2d 58 56 ea ef 7a c0 8f f5 ec
d5 31 e2 c2 ce 92 77 45 a1 16 5a 51 aa 66 98 a1
ff cb 87 f8 1e f6 51 0b ca f9 cb 76 1e 9e 1f 0f

# PKCS#1 v1.5 Encryption Example 2.16
# ----------------------------------

# Message:
f3 40 5b 21 8f 3e c6 03 a9 80 69 00 99 c2 cf 5c 
be 0b 2b 05 96 79 c4 6b 7e 48 f6 fd c4 da 40 92 
d8 31 c8 b5 2b 2c c7 9b d2 bb f6 e9 f5 7b 4e 8c 
aa 94 b5 81 f9 f2 31 26 1f 0e 2b bb f5 3d bb 

# Seed:
f4 70 7f 58 64 2b 54 cb f8 0a 9b 50 48 a6 ec 0b
d3 5d 09 57 16 db 12 06 0c bf 50 58 5f b9 23 79
81 05 2f 7b b1 58 3c d8 7b c8 bf b5 5b 73 3e 89
0e b9 c0 8e f0 e8 80 e9 ba 0d 50 ec 95 41

# Encryption:
6d 9d 39 19 8b 5f cb 13 2d 93 15 11 49 d7 59 91
02 4a c2 2e b6 eb 2d c7 c6 05 8f 64 87 56 45 10
2b 95 25 4e 25 e9 f0 ae 45 06 d4 3c 60 1c 18 8a
31 4f 4b b4 e0 38 c8 15 39 41 6e 10 5e 80 97 fb
69 5a ab 36 fe f5 16 e6 a3 3f 36 f7 f9 5a d1 ff
15 88 90 25 b1 b2 e8 1e 1b f3 b2 de 5b a9 18 7c
a9 6c eb a9 fc ec ef 9c 53 e4 94 34 86 18 59 67
cf 7a 64 77 c3 29 f0 0e a6 95 52 5b ca 99 f2 c7

# PKCS#1 v1.5 Encryption Example 2.17
# ----------------------------------

# Message:
6a fa db e3 da 68 d9 02 85 bb 8f 1e 21 29 ff eb 
b1 c6 5b 95 88 d6 c2 c0 40 24 c2 38 b2 0c 65 d2 
ac a5 e3 82 76 00 0a 0e 6a 0d 05 37 ef ee f6 d3 
e3 d9 4f b9 

# Seed:
ee 17 6e a3 cf d4 90 b6 c0 49 d2 e7 4c 90 c0 ee
74 68 52 03 49 b8 51 65 3d b0 58 a1 c3 e9 56 e0
88 5f 26 1b 6e 71 cf 1e 62 3d 3b 9d 1d 56 fa 13
67 e4 7f f3 74 ad 39 30 9f fa 2e 67 11 28 d5 ab
b4 a6 1a 5b 0d c2 db 2c 08

# Encryption:
67 2f e9 51 59 a9 89 3f 34 98 b6 16 c1 7b 59 da
71 da 80 2f eb f7 cd 38 11 06 14 a1 b2 5d 96 aa
8a 74 aa aa 2a 0f 00 0e f8 ac a3 b4 1a d1 61 b6
26 33 f2 41 31 9c 33 e4 ec b7 70 6a b3 ad c6 a3
ef ea 22 43 0f 3f 5c 9c 4c e5 40 4e b8 e7 5a 10
93 69 c0 aa 0b 7d d7 13 bd 8b 77 cc a5 f7 4b ca
5b c5 55 69 6b 68 e1 17 2d b4 02 50 1d cd 26 49
68 5d b0 fd 88 c8 83 60 da cc 65 09 ff a8 df c2

# PKCS#1 v1.5 Encryption Example 2.18
# ----------------------------------

# Message:
6d 9f 9b 4b b1 23 ba 90 95 53 a7 57 3a 97 1f 64 
b7 25 24 cf eb 04 2d e3 92 15 f6 50 db 61 2d 66 
d7 ae 86 05 d0 44 19 54 62 5f a9 81 22 33 0e 92 

# Seed:
7f f8 68 5a ec f3 40 26 13 90 ad 07 42 73 0c b6
39 28 30 14 ab 37 73 55 6c 69 7f 97 ef 62 1a 4d
cb f8 ec 6e de c5 0d 8e c9 59 0a db af 23 51 dd
fa 0e 52 ea 6e d1 8e b6 c3 78 f3 80 85 ae 5e e4
cc 48 c1 89 1b a4 7b 20 10 d5 d4 35 39

# Encryption:
8d 30 65 5c f1 5b f1 0a 46 97 87 c6 a1 0e 79 25
4f f0 bd 11 93 8b c6 0a 81 a7 58 d9 3c f2 a0 30
24 59 fc 2f 0d 77 00 b8 6d d6 ed 61 83 83 b4 4b
45 87 04 ca 11 92 8e 50 4f 02 8e fe 50 37 17 2c
3e 51 b8 37 be 61 56 de 6a 09 c5 55 97 be 74 c9
7c aa 1d eb f3 14 cd 94 b9 1b 9f 94 cb f7 64 0f
86 c2 6d 1d 6a 0b 10 46 28 b5 87 11 4a a3 1d 99
f6 9c f9 57 37 93 2c 0c b5 33 33 74 de a0 7f ac

# PKCS#1 v1.5 Encryption Example 2.19
# ----------------------------------

# Message:
33 cc cb 59 7d e9 5c ed b8 b6 57 fc d8 f8 88 86 
da 04 c7 57 93 53 14 

# Seed:
53 42 f4 68 43 91 cd 74 f5 28 2d b8 31 41 ff f6
78 f2 3a 3e a6 52 e0 d4 27 fb 6a d9 76 c5 a7 10
a6 37 95 77 71 89 47 ac 72 7b 4d 58 a0 b0 bd 20
7a e3 73 a8 b9 9a c8 e2 51 eb 02 45 8a 9e dc 39
52 fb 28 42 6d 18 fd a1 8a 80 2b bf 0a 0b 8b 2c
f2 5c a3 a0 22 f7 78 c7 f4 7f c5 30 d0 b7 a5 bc
84 6e ea 91 80 f2

# Encryption:
8c 4a 63 d0 73 1e 2e 71 eb 46 15 18 9a 96 8b 3e
4a 24 28 56 b2 09 02 45 23 8b 66 45 97 8f 1e e8
d7 98 11 06 2e bd 2d 1f 3d 52 3a e6 00 e0 e5 a6
e4 05 c4 e4 b5 a1 6e 8d fb 49 24 30 4b 0d 1f f4
d6 41 bf 98 7f c6 d4 1d 3e b7 cd c5 31 34 d0 06
9c db 5a fe f7 f8 f9 ca c0 ee 52 30 b6 f8 86 22
a8 4d e5 2a d6 f7 50 47 84 37 06 ca 96 97 42 c5
8d a7 72 62 ff 1f 12 8a 66 4e 51 cd 63 5e 71 15

# PKCS#1 v1.5 Encryption Example 2.20
# ----------------------------------

# Message:
19 d6 

# Seed:
ea ac b5 78 ae ab f6 9d 4e ae eb 36 d0 4c d8 a2
2e 8f d7 a2 5f 04 43 a1 1e 4e 08 b3 ff ac 1e 05
42 1a 87 6c ca 91 31 82 50 be fa ff ef 9b 27 49
dc 40 2f ad 4f db 7c 1b 66 aa 5e 08 9f f9 9f 8b
30 0c dc 46 f4 8f 56 48 c9 40 8b 5f 8b 3f 5a 12
e6 50 50 dc bc 0d 53 43 d6 3d 58 08 19 21 65 2d
5b c8 2d d3 d7 0e 07 5d 32 d8 02 c2 97 64 78 fc
9f 09 93 dd 08 59 c9 0e 22 8e 87

# Encryption:
61 3b ba 5c 19 0a d7 72 e0 8c 29 07 6e 2e 9e 5f
12 ef c9 29 2e 3b 5c ee 52 c2 69 7f b7 b6 07 dc
72 e8 25 78 e8 b7 53 ba ca df 23 b4 77 25 21 3d
b8 9f 88 73 fa 79 b9 14 a4 b5 16 1e fd 9e 15 cf
a8 dd 1e ff e8 9f 89 47 a6 f3 82 6d c6 bf 53 be
ca 36 5b 93 81 18 45 62 a7 9e 21 ca 0e 68 eb f0
ab 82 ae 76 2b 28 c1 43 65 15 2a e0 f5 4f 2e 9d
14 43 9a 84 6b 38 3f 5e 2c 55 ef a7 00 85 97 b5

# =============================================

# Example 3: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
b9 30 96 d0 26 1e fe 00 0b 3d 17 04 f5 04 31 60 
ab d3 eb 56 6c 61 e5 3c 76 c4 01 e2 b6 55 21 bc 
12 d4 81 21 51 83 e8 f4 6c 2c a8 d0 0a da 5d fd 
04 dc f7 cf 36 cc 58 11 05 d9 9d 2a 7d d9 4b 56 
76 0a 65 64 fe e5 e8 aa eb 06 07 e1 45 19 62 10 
a3 1b 7e d8 dd 2a f3 2d 29 d2 ba d6 f1 5f fa 5a 
11 dc 73 5c c3 62 19 02 1e e8 d1 ee ed 34 63 9b 
5a 91 ac 6a 92 67 4e 18 39 70 c5 9d 5b 19 6d 4b 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
b9 30 96 d0 26 1e fe 00 0b 3d 17 04 f5 04 31 60 
ab d3 eb 56 6c 61 e5 3c 76 c4 01 e2 b6 55 21 bc 
12 d4 81 21 51 83 e8 f4 6c 2c a8 d0 0a da 5d fd 
04 dc f7 cf 36 cc 58 11 05 d9 9d 2a 7d d9 4b 56 
76 0a 65 64 fe e5 e8 aa eb 06 07 e1 45 19 62 10 
a3 1b 7e d8 dd 2a f3 2d 29 d2 ba d6 f1 5f fa 5a 
11 dc 73 5c c3 62 19 02 1e e8 d1 ee ed 34 63 9b 
5a 91 ac 6a 92 67 4e 18 39 70 c5 9d 5b 19 6d 4b 

# Public exponent: 
01 00 01 

# Exponent: 
01 07 ea 61 ad ec a5 e9 00 7c 59 13 4a 7d 38 fc 
7a f3 10 3a d2 c4 a2 be e3 97 08 be fc 83 dc 79 
b7 0d c9 75 92 db 6d f7 0f b3 c4 9c 25 35 fc fd 
9f c2 ce 7b 05 53 92 e3 ee b3 e7 97 93 cc 1b 60 
15 3f 4a 0b ff 26 be 66 7b bc db bf 6e 32 af a6 
fd 14 83 7f 3c 79 be 44 cb 1c 63 8f fa 5c 6b 17 
70 9a 96 e1 27 03 0b b1 11 6d ec fd e5 2b b0 40 
84 2a 94 d2 e6 74 f1 17 51 ec b9 03 ee 10 48 45 

# Prime 1: 
e7 fe c4 74 e0 eb 31 2d 1d 76 cb b2 72 2e fa 42 
10 68 b1 91 e2 33 b6 4e 46 08 7f dd 45 76 d3 85 
55 07 19 35 2e 10 9f e4 33 ac 4e 35 8e 7c 28 59 
eb a7 e4 3a 04 ee 85 9a 46 35 2c 12 43 a1 cc 6f 

# Prime 2: 
cc 5a 02 f9 55 7a 63 5c f5 e6 9b 0f 2b 3f 2e 61 
2e 1f 0a bb d4 bc f1 69 ca c0 84 dc f4 b9 b4 34 
43 a7 85 23 90 f8 19 41 9e c1 a8 38 7e f0 4d d2 
db 7d 60 b4 0a 21 f9 4f 46 d2 27 87 4b 3e 52 e5 

# Prime exponent 1: 
ab 92 8b 10 35 57 3b 23 36 6b 28 f2 6b e7 ba 45 
29 85 83 ed 73 f0 f2 9e a8 c4 98 6b b8 77 cc af 
0a d7 19 19 6f 5b f4 23 fc e3 2d 64 06 60 64 27 
3c 55 0a 40 ae 6d 08 79 b3 fa 97 01 5a eb 4a 19 

# Prime exponent 2: 
8c 7d 55 8e 15 36 0f 19 d9 f4 b0 a5 bd 15 b2 cd 
1c e8 3a 78 e7 c8 fe 2f bd 34 9e 23 4a 1c 61 c7 
8c bb 9e cc d4 dd bc 7f 60 a5 c3 01 14 44 21 3c 
d5 a9 5c d2 6a 24 f1 41 8f 6e eb be 17 fc c1 

# Coefficient: 
b0 b1 b9 cb 23 ac 2a 8a 56 36 66 69 9a 52 40 54 
be f3 81 aa bd 75 55 31 a1 df a8 85 dc 8f 98 86 
a5 5a f0 e3 68 63 91 c7 97 68 1a 8f ef cf 24 db 
d8 1e 03 13 16 99 8f 5d 81 8d 24 76 da d0 6d e8 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 3.1
# ----------------------------------

# Message:
44 e5 6a a7 7b d9 35 ac 59 a9 bd 32 37 83 e1 27 
42 

# Seed:
92 ee f6 19 f0 4f 52 02 8f 4c c3 e5 24 1f 0a a0
92 1b 4d 18 3c 1f 5b d6 8d 86 fb e9 e7 b7 d0 bb
10 4e d1 ca e0 7a c7 d8 0b fd 9c 1c ef f8 dc da
1d cc 69 30 f4 c5 51 37 34 6b fd 68 c1 9d 87 97
2f 7f 34 cb ae 56 63 26 0f eb 79 f7 60 22 1c d6
7b e0 66 d5 af 0f 07 3c 0f 2c 43 9e 8b cb 74 63
ed e4 4c 8b 15 0e ba f3 29 87 26 c3

# Encryption:
15 91 d1 ce 0f ad 66 d8 6f d4 2e fd b3 1e 9a 02
8a 31 57 fb 09 14 b2 47 eb 3d 22 d7 6f 97 69 b0
e1 9f 6c 06 4c a1 b9 89 06 39 ee 6e 37 b7 09 22
4d 6b 58 eb b6 55 ae 4b 69 ed 4c d7 5d 81 29 21
17 c0 69 30 d4 2a c4 d4 2e a7 35 14 21 8f 49 ea
07 ca 97 43 67 09 68 3d 67 a8 e9 e8 08 da 69 a5
0b 73 9c 42 eb 0d eb 94 a3 49 8f c5 45 0e b6 9a
ce 23 76 76 61 fe df 34 18 3a 1b 6f 42 5d d6 a0

# PKCS#1 v1.5 Encryption Example 3.2
# ----------------------------------

# Message:
a7 57 38 29 1f ad 54 13 95 7f a3 b9 f3 b2 ca ac 
9f 5a 

# Seed:
3a 19 1a af 45 ed 4c 25 89 20 5d 9c f6 a3 0f 07
70 0e 38 be 06 25 62 43 01 8d 23 c6 84 da ec e7
e8 67 e3 9d 76 c6 b6 f0 35 43 fc 15 af 81 bf 84
f9 64 ea f3 a9 5a 4b 80 86 28 fd 51 55 38 99 f8
11 c0 8c 62 60 9c 51 4c fa 1d bb 78 d5 a5 b3 3c
c0 b8 57 fc b1 ee cc 53 1b 13 26 34 43 90 59 f5
5a 73 3e 14 6e 1c a1 eb 5a 97 f4

# Encryption:
70 aa f7 24 39 6c 1a c5 0e db bf e8 34 1b 08 7b
a0 ff e2 87 60 5a 8c 3a 8c cf 85 ab 2e d2 fe 22
15 9d 62 aa 02 74 76 eb bf 07 70 02 6d 2d 3b 0c
0d 77 34 fa aa a8 d1 5e 2c e5 1c 85 53 5c 26 b4
15 0a d6 34 6e 3b fd 38 db 5d ac f7 52 e7 5d 75
31 40 54 d1 67 a9 6d 81 9f 34 38 a7 be c4 46 7f
c5 60 a6 94 46 94 85 e8 e7 8e 47 e4 e8 27 7c a7
d3 fd 2a d9 4a 30 46 4c 24 57 85 47 25 c6 16 15

# PKCS#1 v1.5 Encryption Example 3.3
# ----------------------------------

# Message:
87 31 2f 78 7d e0 65 97 50 d6 02 ac 11 02 

# Seed:
a2 29 e3 e8 ef 1c aa 66 ca f0 d8 ac d8 d6 6b 9e
41 cc 77 1f 26 e2 0f 12 ec c6 e2 aa 38 45 51 3d
d1 34 f7 c6 e5 74 f4 1b 21 5d 1d 11 17 56 da f9
71 cc f3 9c cd ce 78 16 19 d7 97 20 df 91 8d 33
9c 82 6d c0 49 b3 90 91 7c 17 ba 0f b1 30 2f ff
11 0a 14 dd 23 84 90 27 41 f9 12 b2 6a 1a db e0
ed 1e 8f d9 89 71 0b 40 3d 27 c4 e0 18 fb 9b

# Encryption:
03 38 46 d7 66 4c 8f 92 62 57 c7 fd 32 64 48 47
92 ac 7f 9b c8 75 8a 7a 16 ab b8 9f a3 cc c4 d1
3a 1e ed 88 af 73 23 bc 3c 74 e2 3f da b5 03 81
89 4c 86 26 df d0 ac 85 89 d4 62 34 d3 c3 5f 18
99 81 79 44 84 31 dc 81 6f b6 3e 55 cf 26 d7 4a
9d 2a 09 32 67 3c b4 be b8 29 cd 7d 49 50 88 48
c6 d0 c0 0d 5c 70 f7 fb 47 67 70 e4 03 19 23 7c
78 6b f4 e2 6c 48 d2 cf d9 6e e3 62 bf 29 28 25

# PKCS#1 v1.5 Encryption Example 3.4
# ----------------------------------

# Message:
9a 2b ca 75 e3 26 49 77 7b 9f 13 ec 30 fe 16 bb 
8a b4 6d 6d 5e 0c 64 63 a7 3d 8c 36 63 ad ab c7 
23 bd e7 2a 50 76 5e 7b 30 0e f6 b5 61 de e8 84 
84 88 0e 4d 61 2c 

# Seed:
8b fa e9 22 2f 75 a0 69 98 ed 6d 9b 14 9e 89 05
cf c8 db 05 5a 0e 32 ac ed f8 24 d2 f6 b5 b4 2b
3a ea c6 a7 10 4e 14 4d 5e 48 34 28 0e 36 44 5a
b8 50 f3 a6 de 16 4c 2c 79 0f e7 d9 d7 bc 7f 9b
db e2 52 17 da 2d ed

# Encryption:
6e c5 f5 59 c8 a3 20 d9 0d 1e b5 ef 09 1c 4d 12
55 a2 4a 69 19 41 0e b1 df 65 a9 7b 30 cd d7 fa
e1 8e 65 12 a0 27 e9 76 70 4b 4f a0 44 37 43 93
d5 01 e2 ba 46 18 62 00 ef 0d dd f1 9c 75 77 58
e4 67 94 30 bc d9 fd 11 9e a2 43 b3 49 dc f8 1c
34 32 d3 1f ba 91 1e c6 fc 68 6e aa df f6 b9 fd
f5 3a a4 c8 5a 49 a2 2a 05 1c 5f 18 07 f3 08 3b
1b 3e 61 17 b4 ef 12 08 de 0a 80 01 dc 29 1c 4e

# PKCS#1 v1.5 Encryption Example 3.5
# ----------------------------------

# Message:
df d6 3e 6e c6 1e 07 27 5b 8e 37 cc 63 69 e1 f3 
ec 0b fc 57 a2 98 b9 05 ae 5d 07 74 e0 f5 22 e6 
75 9c 7d 11 6f 8e 8e fe 69 45 0f a7 a8 38 9f 81 

# Seed:
c4 22 37 7b 89 86 4b 0d f3 8b 4f 9c 15 f9 8a 05
96 55 e1 c9 b0 c7 09 63 5c a6 06 49 d8 d2 47 5e
e1 6c b1 27 f6 76 39 12 96 4e 19 84 d6 da ad 4d
6a bd 04 b0 46 18 b3 2e 53 25 ba 95 eb 5e 76 db
d4 6d 9f b5 9d f0 7a 08 1e 95 6c b0 73

# Encryption:
9e 06 cd 91 a4 4a 9a de a6 a7 98 03 d3 e6 bb ab
17 db 10 62 b6 51 0b ed 40 07 55 66 74 95 44 c0
3d 7a 78 b1 37 b0 dc 1e 66 26 32 1f ed af c2 0d
cd bf 70 80 f7 f5 bd d5 67 44 ce 99 9f 76 70 5c
4f 5e 6f a1 5f 46 c5 ae 50 80 90 db bc 85 fb 86
89 9c 95 78 60 8d fd 77 8a a4 a7 9d 3d 73 63 54
cc fb fa 2c 86 f2 9a 7a 58 45 3d 75 7f d5 22 f7
84 08 d9 91 6b 1b d0 65 4b ff e6 e0 66 ba eb 50

# PKCS#1 v1.5 Encryption Example 3.6
# ----------------------------------

# Message:
5d 91 fb c1 a7 ba 79 93 9b 89 a2 40 8c ce 8e d4 
bb 26 66 dd fe 09 d9 19 21 a0 aa 69 09 6a 95 69 
92 c2 1c 

# Seed:
af 07 fe a3 21 ea a2 67 af 7f 09 80 6f 9e a8 b4
cf 13 5e d6 f1 43 2d 51 b2 8f 92 44 87 09 c2 ee
8a ed 7f 73 b6 28 2c bf d3 7f 82 db a8 72 3e 5e
5e 0a 81 f5 90 f8 2e 2f a8 4c 3b c0 0c 9b 9f 91
aa 55 3b 8b 2c 07 4b fe ca c2 f5 52 37 f4 cb 70
54 3a ba 49 94 68 cf 68 44 c3

# Encryption:
76 05 0e 22 64 22 0e 10 05 2c 49 b9 6c c8 41 1e
39 6a 7a 6e 4a ed b0 6b 48 fd b0 71 de 83 9b 40
1c ac 0c 46 8d e8 d1 ed 0b 56 8c e6 90 e8 03 7a
f5 de f6 b3 d2 db c7 b5 f2 fa de 35 6c 26 cf fc
dd 33 40 33 ea 2c 99 77 92 d9 30 a7 26 46 12 5c
0e e8 6a 4d d8 43 c8 24 c7 a5 2a c9 88 c9 2e 6c
69 b5 80 76 1c 49 88 1f 29 dd 8a 76 da 79 3f 43
2e 7d 5d c7 31 a2 5e 5b b5 02 58 d0 27 39 5f bd

# PKCS#1 v1.5 Encryption Example 3.7
# ----------------------------------

# Message:
04 ed d8 3c 65 65 6a 01 

# Seed:
88 f9 a2 71 97 f9 f2 57 fa 81 c0 e3 05 90 b7 3e
9e 11 c7 6b c8 9e 08 53 6b 4b 64 a2 50 6a eb 33
b4 50 74 73 08 09 a0 5c 45 b9 bc 95 71 73 69 cf
92 c1 bf 98 6e 53 ba 11 23 83 30 fd c4 e0 5e a1
07 33 4a b3 11 06 ae bd 9c 6c 29 e5 01 a5 7d 99
7c 01 bb c1 01 0b d5 2f 05 38 b9 51 59 f3 91 32
0d e6 db 23 d8 16 2c f1 46 58 4c 6e 07 6c 4e ae
86 20 72 eb 5b

# Encryption:
79 1b 37 91 48 a8 3a 03 4d 31 2a 82 bb b3 7b 11
1b 40 bc f6 a3 37 fd e2 89 b0 8e 07 2e 44 03 19
73 ff 9d 0c 27 f7 0d 64 a8 ea fc 6e b5 f8 eb 4e
52 e2 c4 19 7e cf a5 45 ed 63 ae 9a 12 83 79 d3
f5 62 a1 8f e3 ad 14 05 27 67 f0 54 1b 90 16 81
85 cb b7 8d b6 03 81 c0 92 bc 23 e1 aa 05 b4 08
92 f9 a1 16 e6 25 cb 14 8b 56 07 42 cc 12 78 c4
d2 1a 4a 7d 37 f6 98 2a ee 27 f2 a4 c0 c5 73 d2

# PKCS#1 v1.5 Encryption Example 3.8
# ----------------------------------

# Message:
3f 7e ea 78 1b 77 d8 5f e3 71 b3 e9 37 3e 7b 69 
21 7d 31 50 a0 2d 89 58 de 

# Seed:
49 99 c6 4c bf a3 85 24 ad ca b6 6f 64 45 4d 36
fb fc b2 98 6e 1f a4 75 3a 0e 03 88 9f f0 6e e1
60 0e ee 23 be 53 a9 74 42 b4 2c 69 62 18 66 63
2e 4a 6b 6a 1c 71 05 73 26 1d 71 f3 8a bf 9e 52
49 dd c8 e1 b7 7b 3f 12 6b a0 88 15 c4 fe 63 31
4f 9b 9e 8e 7a 40 c7 fc 72 86 25 20 ed 49 d4 12
59 ab 2e 0c

# Encryption:
74 fd 8b 98 56 d7 57 6e 0f 12 87 e0 e9 08 5a 38
01 e6 b6 77 4d b7 33 54 1d eb d3 9e 72 cf a8 29
1f ec 27 01 8c 9f 53 05 a4 4c cb 5a 3c b5 91 fe
d2 e6 a1 d1 d8 5c aa a7 4d c2 37 59 d6 66 5a 45
70 a6 37 f3 ab 30 4b 76 61 31 3b 96 71 3c 7b 7e
49 77 31 33 dd 5d 4e f9 d2 9a 1a f7 12 00 15 02
8d aa b3 df 04 2c 56 26 20 aa 49 d2 c0 14 41 4d
fb 15 77 d7 19 a9 58 82 64 71 2d e3 bf 4a 76 79

# PKCS#1 v1.5 Encryption Example 3.9
# ----------------------------------

# Message:
a3 85 08 d9 46 0c 63 f4 15 81 a8 86 9a 75 82 4b 
14 f5 c6 50 32 29 99 dc 41 13 50 d0 d4 e8 62 4f 
f0 9c eb 00 d3 be dc 5d 76 2a 40 c9 39 80 04 

# Seed:
6a 0a 28 8a 1e 67 43 0c 66 6a eb ea 44 b5 82 a9
09 69 cc 01 e9 0a ae 10 53 ce 55 ee b9 87 9b cc
62 25 39 15 e9 22 f1 09 66 67 bd a0 2a 14 e7 07
47 b3 59 35 24 c2 84 85 47 d2 11 4d 1d 0c dc b9
7e b4 df 45 5b ba c9 b0 cc 29 08 39 b7 3a

# Encryption:
a6 77 57 80 8f 5a bd c8 1e db 7f 69 2f 9f b8 52
f1 a1 66 1c 4a 00 98 05 c4 4b 21 6c d3 b1 32 2b
bb 25 d1 45 8e 31 b0 f0 7d 65 50 57 59 c4 b4 14
7f 23 cb ee 2a f4 a1 a5 93 8a 06 8c e9 c5 32 3f
f5 3f 4b 39 2e 12 50 d0 37 b3 1e 62 81 dc df b9
6b f4 bf ea a1 47 f0 96 c7 84 c9 2f 4a c5 70 91
12 28 02 50 29 c3 b5 23 30 3f e8 22 7e 8b 2c c0
ef 15 70 14 cb 67 31 aa c0 9b fe 6f fa 18 ea f6

# PKCS#1 v1.5 Encryption Example 3.10
# ----------------------------------

# Message:
f7 84 05 23 6a 9e b5 57 aa ce c6 00 7d bc 4c 0e 
de 78 ed 12 b0 4c 82 88 8a 82 c2 13 

# Seed:
86 f0 72 3b 31 68 e2 ae ac e9 ec 2e 95 fd a6 e6
d6 fc 8d 62 94 55 65 66 39 9d 73 11 e7 99 fa a9
b1 ee 1f 03 2a b2 e5 34 a9 1f bc d0 7c 8a 7d 04
a9 b4 85 f3 1e 07 23 fd 29 eb 21 88 06 9d 9b bd
76 29 dc 6e 3f c8 9b e6 04 bc f0 0c 52 fa 8e 1d
6c 62 55 5f d1 f6 0c ec 02 d4 d9 61 d8 28 da bc
4a

# Encryption:
6e 8d 2f b0 b2 ee f8 2f c1 10 ce e0 a9 d3 84 2f
2a 05 8a 24 40 7f a1 1b a9 05 d1 aa 50 e8 cc 12
de cc 07 3d bd 08 a8 c7 05 18 ef 25 db 96 fd a2
41 1c ca 08 72 87 88 95 6f 73 df a1 20 e0 ea 60
5b ff c9 3b 43 a4 41 a4 3d 0e aa 3f f0 73 e6 98
2e ef 52 96 39 06 07 e2 5a 58 8a 39 82 55 ba 00
5a 48 5e 6e 73 2e 3a 19 20 cd 43 a3 90 fb 66 d5
42 8d fd 62 89 74 b8 af f2 f0 60 2d a5 78 d6 25

# PKCS#1 v1.5 Encryption Example 3.11
# ----------------------------------

# Message:
56 1d 27 c1 d3 f6 d5 d1 a6 43 aa 47 e5 5d 78 eb 
00 f3 2d 42 89 6a 34 e0 c1 d7 1b c3 a5 45 7c 92 
05 be d1 3b 98 4c 52 59 

# Seed:
98 17 6e 1d 67 a2 46 2f 5d c1 bf a6 e0 75 95 42
10 4a c1 48 11 d3 18 79 38 25 04 55 c6 5e 4a aa
76 32 bd 2d 1d 75 2e 1f 34 c5 3c ab 26 76 76 a7
8c 10 c9 98 e7 73 fd 8f fe 35 c8 67 c4 43 be f7
98 65 aa 2d a2 91 5a 85 c7 02 63 23 69 3e 45 4d
8a b3 2a 77 15

# Encryption:
0b c0 47 83 c6 92 44 7a 3d e6 1f 53 b7 2f 7a a4
10 31 6d d5 09 a6 f4 9e 3a ba 56 ad 1f f8 6e be
9e 63 66 e1 7e 51 45 00 76 be a3 71 d4 c6 89 cd
61 49 5c d8 fa 29 c0 e8 7b 6d bf a8 e3 86 c2 e8
20 e4 c7 42 a4 87 e8 9b 27 5a 21 86 e2 38 40 be
9c 02 52 7b a7 17 e9 e6 0b 5b f4 17 71 1d f3 4d
7b 8e 2d 12 bc eb 85 93 85 fa 00 1d 4b 4b ff bb
c0 ed ef bd 40 02 41 84 68 c5 66 fd f6 b8 35 09

# PKCS#1 v1.5 Encryption Example 3.12
# ----------------------------------

# Message:
eb 5f 8c 0d c9 d9 01 06 1b 82 ae ff 8d 67 d8 bf 
fc 0c 04 7e cc 4a a3 46 b2 3b db a6 2a 87 e9 dc 
77 0b 11 69 5f bf 19 02 f2 4b 66 ce ab 

# Seed:
74 82 77 0f 3c f5 7e db 81 40 eb c3 3a 02 82 45
ee 06 48 52 06 89 a5 0e 33 f5 f4 67 f6 d1 e4 32
4e 1c 50 c8 99 e5 ad 2c 46 c9 7f 81 20 d1 c7 22
39 d6 a8 2d 8f 8e bc 80 b9 73 ee a8 c5 45 69 29
50 45 14 b4 b1 56 62 84 4f 29 50 62 f2 1e bd 92

# Encryption:
b8 40 43 54 a3 81 b7 c2 ab e5 f7 28 25 f3 d3 15
bd ac e6 c3 cf bd 88 b8 97 68 61 20 05 19 7c 61
66 38 83 f2 c2 57 4f 99 5e a6 f9 4e b3 4f 27 68
62 b3 3f 58 a8 83 92 23 70 6b e1 c1 ff 47 23 05
f1 1b a9 56 2a 0e b0 12 f1 aa f8 5c 22 e8 8f 2f
df ea ff 86 33 d3 cf eb 5f 76 4f 42 28 92 0d e3
0c 6b de 2c b4 e8 f0 3d 90 ed 54 8f 64 85 00 35
1a 5f 41 df 74 ad 65 e8 c3 be e9 50 5a 7d 70 e1

# PKCS#1 v1.5 Encryption Example 3.13
# ----------------------------------

# Message:
5a 7f 0e ae ba e4 9c f5 7c 47 5a 6d a6 79 43 a7 
d3 04 6e 3f 7c 7d 50 b0 9a 80 98 b5 44 69 39 68 
93 cf c0 b2 f0 8f 6c 2b ff 23 50 51 57 5e 6e 56 

# Seed:
fb 08 48 86 db 37 98 d2 b5 bb 35 a3 b1 d3 af 4f
df c0 45 6c bc 79 7b 96 40 d8 c4 4a 0e 03 4e 40
37 2b 34 fc 7c 1e 8b 66 01 1b 4e cd fa ec 6e e4
cd c8 28 cb 1a b4 91 27 4a c1 e3 9f 67 58 7a 55
47 67 09 b4 02 3f c5 69 cb e8 b4 fd 4b

# Encryption:
07 78 4e cb 8c c5 ba 02 d2 07 ba b0 55 c0 e5 5d
10 a9 b9 42 70 cc a2 50 ee 75 fa 1b 5a e1 90 b3
3b 96 96 eb 2e c9 72 b2 6a 0e 94 23 af 16 aa 37
89 17 62 76 06 0a 76 40 03 21 11 74 82 96 34 03
4f 97 12 c9 17 10 17 f2 fb 21 3f 25 c1 46 c2 65
1f 89 44 0c a5 36 e5 33 e3 05 cc 6b 01 13 39 8f
61 b4 63 b0 73 e1 be 05 07 3e 9d 64 bc ae ea 54
44 b8 20 c6 ab f3 46 54 30 ff 4d e4 a8 bc 0e 75

# PKCS#1 v1.5 Encryption Example 3.14
# ----------------------------------

# Message:
f9 1c 71 af 5a ea ca e1 79 e1 6e 87 c9 02 3b a9 
4d 84 d7 51 6c ec 6c 39 89 80 1f b3 e7 ad d0 64 
bd df 92 8b 50 00 94 0b bd e5 39 d6 23 37 9c 

# Seed:
de b2 60 25 8b e2 c8 53 35 21 57 b0 65 26 b1 43
ba 13 3c 4f 49 bf 3d f2 c0 50 ec b2 c9 ca 32 53
11 b3 c3 e3 d8 8d f6 c2 4a 89 4e ab 63 74 5b 62
53 e3 c4 6b ca 17 1a 26 a4 f2 fc 0a b6 2b 8a 2e
63 a0 18 eb 47 01 8c ab 95 1f 59 f0 20 3a

# Encryption:
0d ff ff 51 97 10 c9 ea dc 53 3b 10 8a 4c 29 74
fe 53 18 91 a3 41 07 a6 74 27 93 5b a7 20 cd c6
f6 ee 02 9a 1b 03 68 61 db 14 04 c5 86 49 90 54
1f a2 42 13 01 a7 b2 48 cb 11 f3 65 b6 a4 aa 94
6f 22 31 cb b1 47 32 b0 1a a4 a6 0b cb e5 20 ec
6c 38 53 a6 95 8a 93 c5 b6 8b 85 d4 bc 3d 84 15
ef 8b 1d 4f 63 03 8f 4d 94 2c a6 bc 7a 38 25 1f
15 a4 e3 3b 18 9c 25 0b cf bc 03 15 6e 4f 92 11

# PKCS#1 v1.5 Encryption Example 3.15
# ----------------------------------

# Message:
07 90 c0 81 f3 61 c9 5b 59 d5 27 d3 cb 50 71 0e 
66 e2 72 59 50 10 25 ed 3f 20 f3 0c 

# Seed:
fc fc 2d 56 cb 92 6d 90 5d b3 6e 1e 2e ff 1f bb
75 d6 53 51 7f 59 e8 6f 71 bc 4b c5 57 26 f0 88
b8 21 62 44 83 b3 e2 9a c2 1a 49 bd 85 91 34 90
8e 6c 0e c1 a0 dc 80 79 93 01 44 12 0d 1f 6b f9
3b c6 27 b9 99 69 b2 af e2 1a 7d e1 0d 96 f6 ef
43 c5 67 b5 e2 38 38 5c c1 1a 5a 2a 13 e1 78 55
8b

# Encryption:
7f 0e 6b 34 2d 6a 13 54 66 be 41 73 38 1a c0 4a
ba ab 7e 14 fd cf 51 01 89 87 e9 69 67 16 9a ea
97 78 03 eb b3 24 2a e9 ad b4 6f f5 11 20 93 4b
39 21 46 31 b0 3f 5a f5 bd ea 1c ac d3 28 ad dc
d4 0a 3a 29 96 6b f9 8b d7 c8 c6 fd 0f 4e 8b 97
2e 2d a1 0c 6c c5 52 05 86 7f 39 04 ed 60 f5 b5
be df 7c 3b 3c 7d d5 f3 87 54 8f 40 05 67 02 ea
72 01 76 dc e2 06 d4 13 d7 42 3f 94 3f cd f6 39

# PKCS#1 v1.5 Encryption Example 3.16
# ----------------------------------

# Message:
93 c4 1a 1a dd a8 f6 93 60 f4 1a 58 ec a0 b5 5e 
cb 37 a6 a9 00 fb c7 da cd 9c a3 99 c2 3d 31 72 
61 53 77 ac 0c c6 b0 ed 43 bf 59 7f 21 cd 25 9d 
8f 80 88 7b 15 9d 96 d6 61 61 d5 58 9b 95 f1 fe 

# Seed:
99 1a 2a 7c 06 1c 23 a8 eb c9 48 9a bc 1b 4a 64
a5 d4 e8 38 d9 fc ba 42 88 c0 1f ea d6 6d 59 f4
96 36 e4 a8 d7 52 4c b8 9d 7a dc 7a f3 f6 1a e6
b3 9b 58 8f b7 7e b7 02 23 62 ff d2 6b

# Encryption:
7e 54 a4 32 f5 25 c5 23 33 ab e3 bb 45 48 7e 03
9a f9 4d d3 ef c3 58 44 dd 8e 83 5e e1 00 61 78
e2 4d cd 19 fc 07 66 7b 4a 34 f3 bd 77 1d 09 a7
e2 9f 8c a1 7e 88 d0 29 b9 0d db 5f 28 13 be 99
00 0d 59 f5 43 2c 46 6a 84 28 75 77 20 4b f7 65
97 39 27 69 98 30 57 47 66 7f af d8 02 9c dc bb
59 18 39 3c 2c fc e4 d8 4a 92 20 ea 3e 38 19 72
53 36 f2 5f ee 8e 08 5d eb ed 33 32 d5 dd f1 ee

# PKCS#1 v1.5 Encryption Example 3.17
# ----------------------------------

# Message:
9e 2a 7b 37 74 b1 1e 62 b6 49 0b 56 51 a0 c1 8e 
09 2e 9f ab 8b 22 84 ae 46 43 be c3 6b 26 5e 5b 
a3 c5 1a c3 85 b2 c7 3d 22 0b 2d c2 e1 0b 0d 69 
0f 67 94 5a 0c 42 b3 bd 09 d0 a8 a7 

# Seed:
a1 8b fb 74 f6 de bc ed cf b4 7c 7d 5d bf 10 6e
77 4d 7e f6 63 8e c3 82 18 69 cd 2e d6 2d d5 32
5f 4e 57 33 b8 bf d5 fa fc 43 e4 16 4e 78 d4 38
99 4d 85 33 7d 7f 0d 38 f0 ea 3b a3 7f 4f 41 b6
a7

# Encryption:
18 c8 8a ee 25 36 d9 42 f7 62 2a 64 4f ad 6f ec
d3 32 28 c7 ae a0 ca da 0e 53 1f 4c cb f1 c1 f2
69 cc 95 86 29 a4 3b 97 52 fc af 2b f9 53 ec 9f
7e f4 bb 0e 62 d1 28 e0 cf 4b ab e9 2c 6d 92 84
9e 98 38 dd 88 e2 b4 68 bd ce fc 04 a9 e4 cb 55
e2 a5 18 ca 25 9f 9e 81 a4 9f 28 df 34 76 1f 9d
ea 2e 70 59 56 62 62 6c f9 6a c0 5a 7c 8b 10 33
33 e9 06 e1 32 63 9b 65 a7 66 f4 09 2c 8c a0 78

# PKCS#1 v1.5 Encryption Example 3.18
# ----------------------------------

# Message:
0a c5 2d 40 01 f2 5c 2c 9d b9 1c e5 0b dd f0 d5 
91 9e 19 96 2e 83 b0 7c b7 9a db 00 43 6e 13 66 
b0 aa 8f 3f d1 ee 79 6b 23 c8 bc 56 0c cf a4 bc 
bd b1 f8 40 4d d6 f7 55 15 20 d7 d9 e2 

# Seed:
22 ff dc fe c6 f0 6b 1b bd 14 53 97 70 43 a3 4e
dd f8 59 4d a0 22 13 09 14 97 55 42 f2 f0 0e 98
f3 1e 0d d0 c4 8f 7e e5 f0 9d 6a 52 71 21 ad 23
37 1c 6c d0 e0 79 0e b7 30 8b bb 08 96 dd 59 0d

# Encryption:
b2 69 57 c5 62 29 4d e1 f3 93 24 b1 cd 80 3c fc
39 fc ee 2d 3c 9d 13 79 f8 a1 12 07 9d 69 43 68
f5 55 03 c2 09 4d 98 8a 8a 5b 5a c5 49 be 1c f5
53 16 04 5d f5 b6 f6 33 a4 ef 1e 1f 01 9b a1 b5
42 bf 0a 87 fa 3e 5c a3 f6 b6 1c c8 56 61 28 a0
fa 41 8b 08 25 c9 0e c2 f1 ec 74 e5 87 cd 80 57
d9 52 96 7a c4 52 1c cd bf 63 26 f3 50 93 00 93
82 6d 2e fa 05 8e d6 44 15 37 4d b3 20 48 85 ca

# PKCS#1 v1.5 Encryption Example 3.19
# ----------------------------------

# Message:
a8 00 34 62 f8 06 b7 f6 61 fb 66 46 32 

# Seed:
c7 43 f4 a6 da 03 ab 2d e5 a7 31 cb 88 d8 ca 9b
61 c7 31 9a 5f 8b f9 d2 37 87 7a 05 d0 f3 68 c3
60 8a 05 2a c6 ce 13 73 17 95 47 55 42 ea 16 a8
62 91 3d 04 32 f0 8b d8 c8 b6 ff 81 95 69 1f ee
5e d1 42 fb 9e ca 94 67 52 4b d3 b5 fa 5a 4a c6
14 3b 0d 38 25 0a e6 21 d4 39 90 9c be 3a 6b 5c
01 fb ea 2d 7a 3f 1a e4 1d 61 fd d6 47 64 14 9f

# Encryption:
b7 38 e1 c4 29 f8 fc 06 82 fa ad c8 ca 87 ed 8f
16 df 93 0f af 43 b1 99 1a ac 71 d8 8f 26 4c 0d
82 9a c0 3d 23 c2 5f c5 f3 e8 5d d3 02 cb 7b 15
33 e6 8c 24 16 c5 1a 79 bb cc 7c 29 b0 7e 2e 0e
23 c6 f2 df 0d 07 81 91 7e ba 1a 57 08 62 8e ed
8a 15 b3 b1 84 af 70 0d 0d ab b1 4d f6 0b 09 ba
d2 12 7d f1 80 f4 d6 f7 29 65 87 60 d6 33 c7 77
5a 7b 59 6d 09 d9 03 49 1f 21 09 6c 34 c3 95 3b

# PKCS#1 v1.5 Encryption Example 3.20
# ----------------------------------

# Message:
ef 32 

# Seed:
43 99 cb 04 4a 60 07 6d 18 cc b3 4e 8b 07 8c 81
8e a7 7f 63 b0 a4 3a bd ec c5 77 81 93 a8 bb ba
5d 56 d0 fc 4e 82 a2 11 51 6b bd ef 44 e7 f4 e7
fe bb e1 e1 92 3c 99 9a 7e 96 1c d6 ee 1c 41 6a
85 96 e2 4b 63 83 a4 69 93 5f 33 d1 56 fd 5b cf
db d4 27 46 0d 48 66 83 06 1e 41 05 f3 5b 5e 75
23 20 15 5c 7f 69 ad 8e b4 02 cc 11 06 e0 28 9a
9b 49 65 82 3e 7a 51 cf e4 d2 6d

# Encryption:
13 1b 62 5e 86 e6 cd 1e 08 ac f1 95 d9 3c d3 a0
dc 8b a9 e2 dc d6 fc 99 6b e2 17 24 af 17 90 b6
88 d7 9d 3e a9 a9 50 98 ca bb e8 a5 d4 85 92 e4
74 6b 0e d2 af 7c af 89 b7 b6 15 2e 38 24 d9 15
89 ee ec 33 75 c7 1a 89 97 48 70 3a cc 1e 8d 1d
e4 71 ea 75 28 04 0b 79 5f 29 9e 66 8c ec 9f 5a
f3 eb 48 f9 8c 0d 85 20 67 77 3e 10 1f a2 4a eb
6b 40 4d af b4 2e 7a 63 b0 4a 66 bd 0e 9f 9c 94

# =============================================

# Example 4: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
e9 f2 5e 48 14 0b 5d cf 46 99 e3 03 7f a8 34 f0 
c7 8b 16 73 5f f7 9f 6b 18 ae 60 b5 18 48 d3 06 
99 ec 64 6d 85 7f 15 77 0e 2c 7a 0c 0c 90 0f b6 
04 0b 5f 34 48 4e 9c f5 ce da 23 d5 b2 50 ef 93 
28 6f 01 1e 9a 5b f9 e5 42 e5 c9 f4 42 de 54 58 
e2 3e 41 d1 d9 cd 9f 0c e1 cf 20 08 d3 ea 4d 80 
32 e8 54 cf fc df 5f 69 8d 13 16 e0 29 c4 88 fc 
bb 2b e2 9a 4e 7b fb 8e 6e 81 d3 42 12 3e e7 5b 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
e9 f2 5e 48 14 0b 5d cf 46 99 e3 03 7f a8 34 f0 
c7 8b 16 73 5f f7 9f 6b 18 ae 60 b5 18 48 d3 06 
99 ec 64 6d 85 7f 15 77 0e 2c 7a 0c 0c 90 0f b6 
04 0b 5f 34 48 4e 9c f5 ce da 23 d5 b2 50 ef 93 
28 6f 01 1e 9a 5b f9 e5 42 e5 c9 f4 42 de 54 58 
e2 3e 41 d1 d9 cd 9f 0c e1 cf 20 08 d3 ea 4d 80 
32 e8 54 cf fc df 5f 69 8d 13 16 e0 29 c4 88 fc 
bb 2b e2 9a 4e 7b fb 8e 6e 81 d3 42 12 3e e7 5b 

# Public exponent: 
01 00 01 

# Exponent: 
45 45 88 68 44 53 27 48 60 49 e1 bf df f5 61 13 
a8 aa 45 10 0d ab 07 4f d1 63 94 ec 1a 90 39 b8 
1b 2c b5 81 fe 84 e6 48 b5 f0 32 85 4d d4 fc 69 
f3 61 a0 a3 9d 03 76 13 8c d7 e7 c3 77 84 e2 a2 
f9 d4 f2 66 84 cc 5c c9 f5 12 ba 62 15 eb d2 32 
f9 aa 3d a4 69 db 43 da 1c 06 46 e7 5b 33 aa c5 
70 08 1b 5b 2e 96 ea b7 54 6a cf 93 17 85 aa 2f 
d1 82 4c be 2c 5f 9b f5 63 34 ec 15 66 d1 cf 45 

# Prime 1: 
fc df 1c 49 35 8a 1a ac 93 88 c4 6c aa 04 72 fa 
35 b2 1b df 99 a2 7b c2 ac 65 46 7b 88 d0 16 1f 
bc 70 f3 f4 fa 13 a5 f3 a9 8b 59 c0 67 ea bf 19 
62 16 a1 b8 9e 20 af b2 e5 e5 ed de ae 8e e1 ef 

# Prime 2: 
ec d7 51 d3 d3 f3 b2 08 bd 71 8a e4 35 5d 23 f9 
16 fa 8f f6 7d f0 36 61 6b fd a7 cb c8 7a eb ef 
aa 7e da 69 1f b9 8f bb 03 8a 02 07 22 01 3c a8 
ee 3d 04 8f 97 ba d2 a2 93 0b e4 b9 6f b7 4d 55 

# Prime exponent 1: 
1d 2f 73 08 50 11 9c 7a 86 9c a6 6f 14 40 67 34 
d1 b5 b8 d9 d9 d5 93 0b 28 f2 97 6b f2 a2 71 ab 
40 08 99 5f 90 ed 6b 9d ef d7 91 88 4f 76 1c 90 
45 6d ef 44 6b 9b c2 2b 97 b5 2d fb 21 92 84 29 

# Prime exponent 2: 
4d 50 95 03 c3 83 20 31 3e 36 9c 92 96 e1 0a e7 
3b 9b 1b f7 e9 70 cb 2f ce 63 05 ad be 8a 72 0e 
d0 e7 8c 41 18 fc 28 71 72 5c 51 01 27 16 a4 48 
b9 4c ed fa 3a 1b e0 ba f5 a9 c2 46 ce b3 55 e9 

# Coefficient: 
bc 15 f4 7c 0b b6 de 6a 7a 3a 1f e9 28 89 80 9b 
4a 3c 0c fa 65 0c 2f ec 36 b8 92 85 14 65 47 a5 
7d 2b 15 71 ac b9 d3 0a a7 91 ec 97 fd 51 fd e1 
ec 26 f5 6b 32 63 da ec 9e 29 2e 9c 17 37 36 4b 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 4.1
# ----------------------------------

# Message:
ca 24 72 1c 88 e0 47 74 f4 15 b4 c4 6c a0 fc 26 
d5 bb 53 aa fb 19 92 f6 de 78 5c 76 3a 

# Seed:
fc 7f 85 c1 38 6d c4 3c 3a 28 46 e4 da e4 d9 54
80 54 45 9d a2 31 82 f9 84 07 9b 07 1f db 5e 6d
9d 0f a0 b2 2e 3d e6 36 ee 5b 25 3a 42 f9 5e ed
44 22 95 6c 70 f4 8d fe cf 0e 55 5b 05 15 7b 15
6a 55 c8 bc 65 8c d3 b5 39 7f ab 78 d7 11 56 4e
89 c7 e6 24 8a a0 6a d1 05 c4 0c 31 c4 b1 99 7d

# Encryption:
e3 95 ff 1f a5 52 fc 2e 79 c4 a5 35 58 df 14 00
f8 70 4e b3 6c f7 cb 05 1b ab 93 15 0a c6 39 6a
dd 63 66 9b 04 24 8b 9d b3 6a 9c 94 f5 19 8c 6e
5d 9a 17 d4 74 ed b2 03 45 fd 6a 78 b5 1d e8 16
6e 98 ca b5 b6 d1 65 68 b4 1a 8e 93 e4 83 88 65
d4 bd 9c 51 cd e8 df be ee a5 88 2b 09 dc 70 bc
9f e7 49 b5 d2 4b b7 ca 51 1d b2 c2 b8 29 a7 c9
14 6c 77 4e b0 bd 7a af dc 5c 38 d3 d7 cd 58 27

# PKCS#1 v1.5 Encryption Example 4.2
# ----------------------------------

# Message:
25 c7 bc 4c b2 43 ac 1f 07 40 86 9c d2 6b a8 26 
f3 55 d4 77 c4 aa 6c be 54 3a dd ea 84 44 44 f4 
1c 35 92 bb 3d a7 d4 21 

# Seed:
27 2b e3 fb cc 76 14 99 6f 1a c0 e0 a5 e2 69 06
23 bb 0b 69 70 fc de 0b 6f 45 58 ee 62 34 26 fa
60 ad d6 c5 a8 a1 0d 4a 37 51 50 15 36 fe 8a 45
c5 42 f6 27 f4 22 9f a1 24 57 c1 13 31 13 72 05
55 2b 01 4c 91 b1 c4 e0 9e 45 96 78 34 0a 74 c2
85 e2 6a ef b9

# Encryption:
76 50 d6 f8 1a ef 5c 0e 32 0e c7 7f c8 9b 7c 3e
61 83 85 0d 10 c9 8a d7 e9 fe ea 47 e3 8c fb 37
a0 25 db 42 1f b6 d0 05 80 9e 38 bb 3c 51 95 1d
a9 d9 43 3b a7 ef b1 7d e7 d8 fe 3e 9b 9c e4 55
53 74 ea 66 3a 1b 5d a4 a0 92 29 4c e9 66 98 56
55 e2 dd d2 0d 7d e3 aa 35 37 05 8c fd 7e 7a 7b
97 fc dd 98 53 79 2b a8 3f cc 89 07 4a 8d 0f 3c
ef df 98 5b 9e 78 ae bf b0 59 67 36 4f 24 11 cd

# PKCS#1 v1.5 Encryption Example 4.3
# ----------------------------------

# Message:
cf 00 7e bd 23 da 06 97 1a f7 9a a6 34 d5 d2 55 
05 bd 52 29 

# Seed:
9c f7 23 7e 28 a8 6e 41 8d 66 4f e3 be 7a e3 0e
eb 95 5a 3f 71 02 b2 7d 5f a0 96 74 9c 7f c2 06
4c 88 0b f3 d3 0e aa 98 1f ce 39 86 a9 10 fe ea
e1 84 c0 10 25 04 8b a6 79 48 96 fc cf f7 4a 59
42 f9 62 f3 e3 63 71 f6 b3 55 18 29 43 4a d8 d0
0a 2c 59 7c f6 d4 51 ea ce 88 86 85 38 a4 80 f6
8c e6 8f c6 85 6e bb 57 dc

# Encryption:
b6 32 52 af 2e 8e a2 71 e7 06 fd 68 3d 0f 8c 10
b3 f4 a3 45 c4 f5 b6 78 5b a9 32 9f 44 62 43 c6
f3 69 e3 0e a8 fb 11 08 4d b9 79 88 e9 c3 87 4b
34 d6 fd 08 71 7d 9e 81 0e 9c 22 43 60 34 6b ec
cd 3e 0e 53 d1 0b 1e d4 58 e6 4f 3f b0 92 f4 8c
b6 6a e0 3b 64 f6 aa 9c 63 bd 27 9f ae 4c 33 f4
2a 9d 73 bb 39 11 8e b8 7d 25 12 b9 d9 36 a2 7e
d2 e4 49 60 7d bf 0e 3e 22 3a 53 95 26 35 59 9c

# PKCS#1 v1.5 Encryption Example 4.4
# ----------------------------------

# Message:
ef da 79 e9 c3 36 c2 34 ff 37 b4 f5 8f db d3 1a 
f3 67 5b 3d 2b 10 5e af be ad 4b bb ff f5 4e 68 
6a b5 

# Seed:
30 c2 09 43 f1 bf c4 23 61 d4 d2 2f 51 a8 d7 86
cb 2d 0d d5 ff 7d 70 5b 30 28 60 18 11 29 3d be
5d 72 c3 55 97 10 ce 0a e9 5d 2f 16 b2 39 a4 ac
84 45 53 7d 48 8e 3e 6d 2c f5 b7 a6 4c 06 c3 75
6e 11 60 67 63 63 3e db dd bf 26 be e6 51 18 42
d2 75 2d cd 88 89 6c b8 55 8a 87

# Encryption:
bb 91 b2 f6 f4 33 1d 64 d0 73 6a 2e a6 03 29 aa
16 c2 ed 7a 4d 5c a8 d7 84 e6 30 4c e4 84 4c 71
58 f8 22 d2 af 29 c8 90 97 7d 75 a9 35 e4 3d 93
b5 be 10 c1 d4 4f a0 0c e2 8e 75 f5 27 bd 84 a3
be 5a f5 be e9 45 67 c5 5e 15 ee 3e 93 42 6a d8
d5 0f 06 4c 57 93 ca 38 c4 3a 70 c5 f5 60 74 0b
16 ee a1 6b 7f 13 41 5f 75 1b 3f db 87 7a 88 29
33 21 f5 0f ff a6 f1 24 94 96 c2 b0 27 a2 18 ed

# PKCS#1 v1.5 Encryption Example 4.5
# ----------------------------------

# Message:
4a 01 fc 13 c1 d8 6f e7 b2 fd c7 92 f5 28 0f 87 
5a dc 5a e9 9f f9 11 d0 2c 8c 00 3d 39 bb ee 54 
b8 51 ef a3 4b 41 31 be 52 0d 81 00 ef 62 c2 5a 
4b 51 7e 9b 

# Seed:
be 6b a5 d1 1d f1 bf cb 2b 84 67 71 b6 c9 df c9
33 4d a8 6c 4b 7c 25 43 93 18 e8 ba 8e 47 49 2b
cd 51 1b d4 ca e1 67 7d 31 2c a2 2a 94 57 cc 81
d9 0e 4d 52 4b a2 26 5f 0f bc a1 8e 3c 3f 48 2d
ca a7 88 33 22 39 34 6d 6f

# Encryption:
16 bf cf b4 2d 28 b9 d1 62 70 cd 13 8d c3 ca 64
42 95 6a 41 82 5e d0 23 0b 71 09 16 13 33 3a 9e
7c 52 ce 8c c4 b0 bf 29 10 79 41 a0 d7 2c c3 4a
fd 00 48 bb f4 c7 16 c7 3a a9 b0 c7 8d 37 c1 93
71 9e be 03 a9 31 74 53 b5 53 d4 f5 b3 85 d1 41
fc 3b 0e d1 9b 96 dc 35 0d fd 4d 12 e3 dd 03 ff
18 39 d4 78 2c 6d fd 5f df 59 71 f3 dd b0 e3 12
a9 16 06 f3 13 73 02 0d b3 a7 6d 04 fd 6d 65 d6

# PKCS#1 v1.5 Encryption Example 4.6
# ----------------------------------

# Message:
7a b0 6e 19 69 22 c0 

# Seed:
de 0d 60 33 c1 e9 6b 5f d8 31 21 4f 30 d8 1b 8f
d9 a2 69 3e 5e 8a 36 ea ff d0 39 e7 47 3c 28 ee
43 a3 91 6c 78 c9 a1 12 95 8a 94 ce 67 1c cc 40
d9 7e d4 18 7a 3f ff a0 dc 12 9d 88 a8 b8 c4 96
6e a3 94 a9 10 89 61 25 f5 4d af bb 3b 17 b9 fa
10 c4 82 20 09 64 90 c6 f7 5b e2 51 83 e9 98 4d
f5 e4 d7 eb f9 47 5d 11 ea 39 33 5a c7 2f 93 d3
33 bd 74 22 19 42

# Encryption:
54 18 23 f9 05 57 6d a1 42 e2 65 d8 90 45 ab 66
20 fd 1a 74 c9 53 3a da 4b c7 b4 3d 95 62 9a 31
18 6f 4e 89 89 20 83 d2 54 9b 0e 63 8b df c0 d2
7e 14 ec 18 c4 5c a3 58 61 df e6 12 a3 a1 ed aa
fc 72 fb 46 81 a9 9e a6 e6 48 be 89 62 f1 56 1e
75 0d 14 49 f2 3f 43 0a f9 30 72 25 54 4d 8a 8b
89 65 af 5d d1 8c b7 89 53 ce 6d 16 d8 5e b2 11
af 0c 64 68 a2 af 9f 72 e7 86 61 b0 fc aa 48 15

# PKCS#1 v1.5 Encryption Example 4.7
# ----------------------------------

# Message:
9a da 9c 10 b8 ae 22 

# Seed:
cc 23 43 72 4a c5 0e e5 47 08 fc 5f d0 3f 09 a1
cc 12 22 a4 4b cd 44 03 87 7c 6b de 86 bf 43 e4
2c 10 84 f6 ef ff 20 fa c0 ac c3 1e ca 17 c7 38
d4 68 68 73 65 52 fd 2f 7e 93 b8 22 25 61 05 4e
6d ad c3 15 60 4e af 8f 77 f0 5d d8 58 3a 93 bf
03 cb 9c c2 13 9b c4 19 bb 10 e9 b2 01 b2 a7 e1
8b 03 79 0c c8 3e d6 05 d6 d5 66 33 05 34 71 39
c7 5e 1a e2 a5 6a

# Encryption:
9f 54 d0 de a0 5a 5d 00 72 23 5b c4 67 93 cf c4
7b 00 6d aa ac 02 41 c7 e6 6d 33 3e 23 c3 cf 97
63 b6 1d 9e ea dd d8 3f 5d 7f 0a a9 7d 16 c7 69
92 55 cf 7e 48 72 b6 a0 07 95 62 d2 60 7d 64 40
d7 ed 37 c6 71 3c e9 66 43 a4 41 f8 39 55 64 d2
6a de a5 82 3a 49 42 da 4a b8 e4 7b ed 58 81 b9
d1 84 05 79 57 df 65 39 e4 36 da 35 e3 0a 25 3a
f1 2d 54 1d 4b 0e f8 3c 5e f3 c1 35 ab 95 94 9a

# PKCS#1 v1.5 Encryption Example 4.8
# ----------------------------------

# Message:
d8 12 6f 4a 88 78 97 82 93 11 76 51 b3 0e 79 22 
d1 4a cf 

# Seed:
ad f9 b0 a9 15 2f 0e 6e c6 f4 39 59 71 ad 40 3f
02 e7 fa 98 f8 15 56 0a fa ff a7 ca d5 b4 47 4b
6e ce 65 ed ab e2 7e c2 4a 0a a4 73 ed 75 a6 1f
5c 24 90 a5 36 b1 a4 df 7b 03 41 77 37 c5 34 e1
d4 5b f7 26 94 38 6b ee 82 0c 48 db d1 83 17 bd
61 7c 04 b6 a4 17 e3 0e ed 79 58 8d c2 3f d4 db
a1 37 44 b4 b2 aa 5a f8 0a 8a

# Encryption:
3b 2b 85 ed fc d7 c7 c2 7b de de e1 c2 8a b6 18
7a bf 1c 96 d9 45 30 07 92 cf 8a f1 97 c2 f9 a3
91 b4 8b 83 32 cf de 7e 4c 7d c3 4c 42 30 29 56
92 ce fd a5 ef b2 57 35 49 2b 9f f7 84 c7 ba e7
35 11 c6 18 e3 aa 7b c8 7b c3 13 f2 67 09 a8 ea
4a d7 3a 34 9a b9 e5 ad 82 6c 96 ad 0e ca 97 e3
13 28 6b cc bf 8e 33 c9 1f 03 68 39 b9 94 8b 4e
b0 c3 8e 21 3e f4 7f 77 66 1a 27 f8 cf e4 99 03

# PKCS#1 v1.5 Encryption Example 4.9
# ----------------------------------

# Message:
f5 df 01 af e6 a9 22 51 8b 3f 4b 80 cd 4f ca 73 
b9 7b ab 61 71 6e 27 d2 51 bd 46 5f 4b 35 a1 a2 
32 e2 da 00 90 1c 29 4b f2 23 50 ce 49 0d 09 9f 
64 2b 53 75 61 2d b6 3b a1 f2 

# Seed:
6b f2 81 0d b8 fb 26 93 98 41 2d bb 88 06 02 82
d4 5f be 96 27 33 7e 54 34 26 1a 5d bc 19 3a d6
18 c1 1f 7b de c1 de 25 05 f8 60 37 fc 18 51 bf
6f b4 9d 23 60 62 73 47 49 9e fc 98 e2 92 05 da
90 6d 32

# Encryption:
40 12 fc c5 cf b9 78 de f8 8f b8 f8 17 4a a5 b4
a3 07 75 ac 45 59 f0 b2 f3 d3 b4 38 9b 82 8a 79
d1 40 25 10 c9 a0 33 7d 48 9d 11 82 ab 31 c8 38
ac 7c 80 b7 48 60 9a 2a a5 37 da 7a cc 3a 4a 7a
31 d2 ad 25 2b fd 59 28 0b 3d 18 13 a2 6f 93 c5
9e e8 c5 ee 68 87 18 f4 27 83 93 fe ce 32 3a 9d
ff 83 37 55 e8 9a c8 ee 1f a2 90 4b f2 4c df 4f
01 e6 ea ed b6 a8 ef 01 f4 07 be f3 30 9f 03 39

# PKCS#1 v1.5 Encryption Example 4.10
# ----------------------------------

# Message:
a3 82 3f af 

# Seed:
94 f6 70 fd 82 f6 91 32 75 ee a4 c6 71 16 ca aa
bd 33 57 8c f8 4d 22 63 64 38 a6 fd 7e cf ee fc
0b 18 7d ec f7 93 89 1c 6e 4c fc 52 b5 67 d8 72
bf fb ee 0a 67 47 2a 1a 48 c0 f1 ba 59 8a d8 25
89 01 c5 6a 55 92 f1 41 14 7e 81 33 9d 74 7e 06
32 de f0 0d 3d be e9 5c 4e 43 21 cc 25 b5 31 14
47 a3 02 c5 34 92 9c f7 e5 34 f9 ae 67 f4 1e 01
e2 2a 3d 7c e4 1b 3b 31 35

# Encryption:
7b 60 25 42 b6 4f 0a 1e 0e c2 aa 01 cb ed 37 7e
33 1e a3 ff 86 f3 56 fb 7a 58 83 76 4b e4 cb f7
d0 75 4c 58 29 64 31 36 f2 57 23 36 a2 3f 15 41
1d 83 85 14 a1 43 87 24 ad 74 09 e8 ec 8f e2 63
41 ae ea 56 68 34 5d 0d 82 3f b5 c2 1d f4 59 e8
bf 7c 15 b8 0b 07 2e 5f 8a 84 65 a4 4a a9 d0 9d
82 5c 03 15 a0 ec d2 d6 49 70 2b 10 9b e8 fe 35
eb 22 84 3a 20 e7 fd 87 4f 1c 6b 46 a8 0b 68 df

# PKCS#1 v1.5 Encryption Example 4.11
# ----------------------------------

# Message:
75 b9 a4 a0 bb 2d 46 43 e4 78 f6 54 f2 cc 1a 8c 
1b b4 67 19 76 0d 45 41 a8 a7 33 f3 3b 71 3d 32 
c6 0b fd 35 f1 61 74 83 48 47 e8 81 2c bd 7f 06 
ce 72 89 f3 72 c5 82 30 f2 b0 01 45 9b 5d 

# Seed:
49 a7 3d eb 93 e3 f1 be aa ad 3a 19 9a 70 56 9e
09 9a fa ca f7 a7 5f c4 ce 64 8f a8 2e af 2a 0f
e4 11 d2 64 fe 45 f7 45 25 c9 1f 3c 75 10 17 f8
0a 02 ba bf f3 57 99 62 6f 2b 8d db 9f 36 91

# Encryption:
e5 ae e7 0d e8 62 72 3c 51 73 df fb f6 92 6c 3d
33 16 d5 90 9c f5 a1 d6 63 e6 80 ab 2b b5 76 e3
5b 93 fd 43 27 43 a1 8e 8d b4 fa a3 32 f4 46 68
a3 d1 9e 5e 69 57 32 f8 4b bd 86 d0 dd ed 76 65
b7 0b 97 63 2e ab e2 36 4c af ef 7b 74 dc d1 bf
bd 62 5e 2b bb f6 65 4c c0 26 61 81 ac 0a 75 7c
3f ba bd 43 0a e8 63 71 eb 56 af 61 0f 77 cf 2f
ff 6e 24 8f 8c 57 91 60 b9 1d ce cc 0d 20 2b 50

# PKCS#1 v1.5 Encryption Example 4.12
# ----------------------------------

# Message:
15 06 eb 34 91 78 5a a7 21 06 bf 6c 85 d0 10 02 
04 6d 1c 16 d4 35 dd 4e 7c 4b 7e 8e 90 dd df 16 
33 2f 94 f4 b8 35 d0 e4 ad 55 d8 3a 81 b3 5c 54 
b6 79 d3 cf 

# Seed:
f6 6e 6a 84 75 84 40 86 e2 84 77 22 97 46 80 1d
43 50 d9 ad 07 68 f3 c3 d8 fa a8 10 7d 95 fb 20
5e 4b a8 c6 4b 73 8e 54 e5 ac 0d fe ab 99 6d 61
12 5c 26 79 80 72 59 bc 9e 47 d8 bd d2 c0 40 95
05 44 8b bf 87 2b f6 64 7a

# Encryption:
d1 fa 39 52 cc 61 45 ff 77 1b 6c 5a 68 27 5b bc
22 d0 03 92 03 66 17 37 5f 0c 2b ec 3e 28 85 83
ec fd c6 df 6a 82 8d e3 7f 77 c5 56 a8 cb c4 d4
43 36 e8 d2 e3 05 87 e3 31 58 73 17 e9 7b 05 a3
fd 78 02 5b 2d 49 6b 3b be eb 6c 72 5d 9e a5 61
a7 32 28 82 33 d6 8b 79 49 7f b0 b6 fe e0 a6 b6
8a c3 13 66 1b 4b 65 47 39 f9 18 f6 fd 3b ff c1
7c 1d f4 1f 01 44 95 d5 5f 95 90 14 7b 82 d1 5d

# PKCS#1 v1.5 Encryption Example 4.13
# ----------------------------------

# Message:
9e 3e 

# Seed:
9b 3d df 17 cd 74 e7 6c 69 b5 ca 3a 01 0a 0e 0f
bd 17 05 d6 9c 30 74 35 3b e7 d3 c0 c2 05 f0 99
c7 a8 10 b7 a1 ad e0 9f 5a 03 6b b7 69 ef f5 3a
53 d4 c6 f8 71 52 92 2d 9a 7b 86 ed eb a3 72 37
d7 f1 73 4d 9d 97 39 38 3f 48 80 af 3a d6 88 87
e0 fe 7c 87 a1 74 fb 32 38 b1 e5 1e ad 2a 84 34
40 c2 b2 7f 22 dd a4 22 8d ce 70 f9 1c 98 d4 71
a8 74 4d 27 65 55 79 58 81 02 44

# Encryption:
42 12 6b 49 2a 1e 7c c0 33 95 b2 ac 70 33 cf 6a
67 36 b1 2e 76 82 5a 17 3b 9e 01 1a e8 bf ed 44
fe cb 8d 9f 58 cc e1 99 11 fe 42 d4 55 e2 49 20
09 32 a9 b6 8f e2 e4 19 bc 63 9c 11 78 d1 1f fb
db d9 95 5d 45 9f 5e cf e0 90 20 09 8e 29 7b 8e
91 48 5e 94 bf 11 e7 bf 77 ed f5 a2 70 11 c8 2b
92 73 65 a1 2c 9c 77 c7 e4 9b b7 fe 2f 61 33 39
de 3f 51 20 87 79 53 86 ca 58 5a 70 24 78 27 90

# PKCS#1 v1.5 Encryption Example 4.14
# ----------------------------------

# Message:
70 aa 78 a4 d3 7f 74 c1 81 aa 27 40 7f 2f 9f e6 
63 a9 1b 16 be 9b ea 6f c6 12 88 7f 

# Seed:
d0 fd 16 c0 f0 d7 90 9a 38 86 17 08 11 e4 4f 24
fa df 94 ff 17 03 9a 56 84 a0 9b 24 e1 93 3f a0
c4 71 51 63 5d 75 7b 73 c2 3f f3 91 01 cb e2 52
9a 63 a7 f3 a0 19 5b 6e 47 51 07 11 de 17 1a 16
56 c9 ea b3 cf 82 d1 c6 52 26 b5 8f d0 fe 58 ec
31 96 24 7f 34 b1 a0 55 27 02 dc 03 75 12 c6 81
04

# Encryption:
95 36 d4 7e 1d 68 7f 1f 24 99 6c b4 6c e9 46 ae
54 d4 a1 49 b3 4b 5b c3 44 43 a2 01 51 83 87 f4
b6 38 18 37 cb 7e 4b 0a 44 75 13 70 42 f1 44 8c
1e a4 15 15 ef 31 c2 fc bf 62 e7 e9 58 67 b6 74
ac 23 0a ed 9c 7d 8d 61 c5 27 52 b2 fc 2a 0b ba
fc 77 b3 1c 51 49 30 de 98 23 b4 38 b6 fa aa 40
d2 55 31 03 3c 66 48 3f a0 02 3a f2 1d a6 4f cc
8b b8 c5 d5 2d 3f 6c 43 80 f1 d6 08 d8 c0 11 8f

# PKCS#1 v1.5 Encryption Example 4.15
# ----------------------------------

# Message:
01 16 a4 61 77 73 b6 dd b2 19 16 1c 4f d0 71 93 
7b bb 07 15 cc 62 7c 17 b8 e7 52 80 d9 9c dd 41 
6e a5 cd fa 09 06 b9 af 0a 20 cd 47 7f dc ad 14 
15 a1 9a 9d 1b 96 fd c3 c0 ed b9 

# Seed:
86 b1 58 60 9b fa 08 a8 ed e4 ef 3f 23 e1 2e b5
0d 24 55 74 26 4d 76 4d 87 12 67 db 8a 95 24 ea
3f a2 e3 84 5f fc 29 1b da 98 99 89 bf 71 5a a2
b0 8c 49 79 8a 81 9f 68 58 d9 fa 35 f9 4d f3 c7
e0 86

# Encryption:
74 a3 df 38 5d 20 87 7b ca 9d bc eb ca 2e 53 2c
6a be 95 62 d6 81 7b e1 6e 11 8a 60 f4 ab 0a 1a
c0 a8 46 66 53 a8 f8 17 0e 35 fc e1 4b 44 9c d5
9f 55 8e 02 0a 89 88 94 bd 2a 71 75 58 e6 65 0f
3a 12 85 70 d8 c1 69 a7 74 66 63 c1 d7 ef 62 14
5f 4b 75 c5 fe b6 38 6f db 85 33 94 c6 59 a9 1a
a2 aa e0 3b ef 91 13 dd 49 28 ff 28 b3 80 92 7a
d1 ba 4e 8a 37 ed d1 72 ef e8 e9 ea bb 61 4d 83

# PKCS#1 v1.5 Encryption Example 4.16
# ----------------------------------

# Message:
15 c5 fc c7 54 7d 63 76 1f 6a f1 f2 6e ed 9b e8 
13 4f 9f 92 12 7e 76 b0 3a 33 a9 7b 9b e3 f7 8b 
2e 22 fc 7c 85 06 99 a1 5c 0e 0e ce be 2a 71 80 
5f 02 4b 93 88 a3 bd b2 b3 60 d6 9c 5c 0c 46 

# Seed:
5b 4f 17 a9 de 91 73 7a 7f e8 54 e8 a1 76 be 5a
0a 16 fc 10 42 cb 87 0c c0 18 92 fc d3 8e a7 5b
07 3c 0f fa 01 4f 96 a3 58 e3 aa 5e 73 ea f8 a9
1f ce 75 47 0b de 64 e8 7a b8 91 ba 3b f2

# Encryption:
73 62 d7 39 8d 0c 25 1f 83 58 17 e4 79 37 a9 25
58 36 ca 02 30 45 7f f6 08 b0 78 d5 09 31 a8 80
33 ea 76 50 81 12 65 f8 e2 68 b5 33 15 d8 43 8e
52 a6 a4 b1 b3 89 5d 30 c3 da e1 1a 3b 8e a8 c3
0f 05 e9 d7 1d ef 46 d4 51 11 92 a1 0f 54 21 8d
39 36 cb 17 98 3a 1e 7a ff 18 18 89 39 b9 46 92
76 49 b0 fc 4f 7b bf cb fc 14 e1 c0 ec a0 7d 00
c9 03 db 78 16 9c 50 ef 0a 38 f1 da 19 ae 44 59

# PKCS#1 v1.5 Encryption Example 4.17
# ----------------------------------

# Message:
7c ad 18 f1 75 13 87 42 28 5e 90 35 d1 3a d4 1f 
c3 a8 52 10 e1 54 4e 24 de a3 fc fe 66 

# Seed:
9a 06 ca 10 fc c6 61 0e 77 df f9 0d d1 76 f8 2e
3f 96 e4 a9 d7 ab 87 2c 74 8e d4 22 f3 4b 33 48
61 94 40 f0 aa a2 2a 66 98 51 da c8 89 4a 8e fa
34 ea 2c 2d a5 e9 58 69 e0 ad c0 05 a4 9b a4 58
18 ca a4 74 11 5c 34 49 96 6a 85 c4 18 fc aa 8f
45 63 0e fe 0b 1b 4d 3d 69 be 1b c0 06 8a a7 99

# Encryption:
1e fa d4 14 46 b9 1f da dd 8b 80 61 9f 68 27 36
68 b7 58 5f d9 1f 34 49 ec 85 c2 42 d0 84 9e 4a
53 a5 97 7b 61 aa 40 d1 2c c4 85 ec 7e 4f f2 0f
98 86 91 cb 9d 73 af 46 ea 37 6a fc 69 ba 22 33
86 e9 f1 5d 03 26 97 da 75 e2 f9 52 be 2a f0 62
e8 24 6c f7 49 b8 9c 4c bc d6 4e 23 f8 82 bb 55
3c 3c e3 05 20 36 22 b5 a7 39 77 35 a6 34 aa b0
d1 7e f9 b5 55 9d dd 34 f4 87 2b 56 e7 98 6e fc

# PKCS#1 v1.5 Encryption Example 4.18
# ----------------------------------

# Message:
fd 98 c3 8b e3 19 30 70 b5 c4 33 4b 11 c2 5b 33 
4a 44 

# Seed:
f3 57 91 11 03 e9 87 d1 a9 f1 5c c2 e5 2f 42 39
0e 0f aa 50 02 c4 f1 7d 40 a4 af 50 f3 1a 23 17
50 e7 af 61 d9 af df 9c aa 38 61 a2 0d c7 21 89
58 61 fb 11 8e 08 8d 32 18 e6 fb 35 56 b1 62 d6
bd 67 91 1d bc 94 21 98 42 65 82 72 a5 d4 9b f5
ab b4 a0 87 94 95 c5 e6 e6 86 28 59 29 a5 5a 36
8f 52 4c 14 a4 0b 0c 61 38 0d 0e

# Encryption:
de ae 18 3b 56 c3 fb 38 41 ea 57 42 34 ac d3 0a
ff 00 d0 05 1f 57 80 37 58 a4 71 4a bc be dc da
8b d1 a4 8a 98 01 53 df 89 6b 13 76 aa 4b 45 95
80 13 d6 19 be 7e af f6 c1 a6 75 e2 92 ef c3 f4
39 3d db de ab 47 e8 90 a7 8c ef 69 00 24 49 57
87 48 90 6c 10 21 b8 91 b9 43 d8 18 d3 a6 1e 67
a3 15 61 2d 4c b1 cf 19 7c e5 df ab ef da eb 59
0b 8e 8c 73 68 5e 74 7e 59 a3 95 c8 45 c5 d0 c3

# PKCS#1 v1.5 Encryption Example 4.19
# ----------------------------------

# Message:
96 53 d7 94 69 f0 5d 40 19 65 a9 5c e8 74 fa 22 
5e c4 79 74 e8 d0 68 41 c1 3b 47 85 e0 0d 54 7f 
9d 31 44 c3 87 9e 6c cd af 78 7a ef c2 f8 45 2a 
4a 3c 88 4e 38 ee 

# Seed:
85 5d 6d 15 12 e5 43 a4 5d 3a 9a a9 68 5d 5d fd
a7 04 79 ba 39 52 63 64 14 1b b6 36 27 45 89 85
71 20 01 22 f4 bc 82 c6 22 43 45 c6 9d 3e f5 42
f1 23 bd e3 01 5b 60 c4 c0 ff b9 8d 63 01 31 ae
e8 1f e4 a0 b0 15 38

# Encryption:
69 ca 62 e2 9a 5b db 4b 04 e2 40 16 21 2c 25 91
40 a6 0c fa 81 eb 66 93 bf fa fc 9f 60 0d ce 10
82 2a 00 7b 6a de 93 fa cd a1 b2 b1 65 b5 57 76
0f 0a 67 5a c9 bc b2 06 b9 64 fb 90 cf 6a 2c f9
9f 18 6b 36 d2 eb 99 1d 82 53 a0 75 4f 9c c2 d7
2d e5 49 ab ae 90 94 f5 a8 6c e1 db 49 4d bb 6e
51 62 86 71 5b 3d d4 05 59 b3 10 7b 95 24 b7 29
ac 65 4c fb 40 f9 ab 35 d0 34 e0 27 19 7c bc 36

# PKCS#1 v1.5 Encryption Example 4.20
# ----------------------------------

# Message:
0b df 3f ce 8e 48 7d b2 2d 07 60 ab 71 15 86 ca 
8e 45 9c 39 4f f8 b1 a1 86 70 67 a9 31 51 99 a8 
01 24 74 b0 f9 0d cc 87 cb 

# Seed:
c9 b7 8d ce 9d fd 7f 04 04 ff 98 2e 06 b5 96 50
ba fe 31 ea 19 bc 1f 2e 1f 39 89 f4 ce fc e4 6f
c6 52 42 3d b3 c9 9d 92 a8 fb 58 f3 ee 39 3d 55
5b 76 84 88 9a 4b f8 15 a1 3e 3b 9b f2 43 71 40
66 b9 07 58 90 67 50 47 f1 7e 93 5b dc f0 e6 6f
cf e3 9b cf

# Encryption:
43 ad 3e 62 5f b1 72 15 57 8b ef 2f 46 5f aa 72
ae 69 43 83 36 9f f7 aa 15 12 01 a3 f2 59 c8 d8
ce 8c 16 bd 25 52 21 49 f6 66 e8 d6 92 a0 79 5e
a7 15 69 d2 88 1f 97 07 08 5d 3f 59 bd fa 28 73
66 d7 f5 a3 f7 6e a5 dc 10 9f cb 03 30 2d a0 b7
86 99 71 3e 0d 30 09 58 4f 97 17 6c 1b 9b a6 3e
80 cf a8 fd 4c 01 3d 74 b5 fa de 84 72 d5 2c 11
e2 e9 36 81 ba 19 d3 53 d3 1c e6 fa 3c 0a b6 0d

# =============================================

# Example 5: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
ab 29 d4 9c dc 92 5c 69 ca e7 52 92 fc 03 62 03 
73 c6 fb 36 d3 c2 49 dd 5b b5 0f 88 1a 4c 93 89 
af e7 3e 8c 56 b8 d6 67 a5 ea f2 b5 71 4a da f4 
ca a0 06 a4 9a c4 bd 4b 91 d5 45 cf 3c 10 00 9d 
31 8a 9d e0 f3 bb d8 38 4e 8c 7e 96 ca 15 95 e3 
2a 70 41 d1 68 ca a7 34 43 b8 85 bf 7f 61 4a e1 
21 2e 3b 5a dd a2 9d fa d5 01 b8 b1 a8 1c 3f 48 
a4 56 e1 33 ad 52 da 2a bc e5 d6 e7 82 f2 75 c9 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
ab 29 d4 9c dc 92 5c 69 ca e7 52 92 fc 03 62 03 
73 c6 fb 36 d3 c2 49 dd 5b b5 0f 88 1a 4c 93 89 
af e7 3e 8c 56 b8 d6 67 a5 ea f2 b5 71 4a da f4 
ca a0 06 a4 9a c4 bd 4b 91 d5 45 cf 3c 10 00 9d 
31 8a 9d e0 f3 bb d8 38 4e 8c 7e 96 ca 15 95 e3 
2a 70 41 d1 68 ca a7 34 43 b8 85 bf 7f 61 4a e1 
21 2e 3b 5a dd a2 9d fa d5 01 b8 b1 a8 1c 3f 48 
a4 56 e1 33 ad 52 da 2a bc e5 d6 e7 82 f2 75 c9 

# Public exponent: 
01 00 01 

# Exponent: 
07 7b b2 73 32 34 86 ec 4c 25 ed 67 06 34 1a a8 
a6 7a ec 58 43 0d 53 3f e5 86 c6 b9 4f 57 0a 3b 
42 90 c4 5c 0b dd 94 68 1f 29 a4 b7 58 8e ea 80 
39 cc a1 c5 b8 0b 82 70 27 9d d0 a9 c5 09 39 11 
93 e3 d5 c2 5c 10 75 c4 a1 d3 dc 32 74 06 6d ab 
81 7f b5 1b 16 bc 26 7e d9 a9 98 0f fb c0 92 85 
d9 7f 11 2f 15 26 95 e6 e0 9c ae 72 bb 55 06 6c 
b9 db d0 98 a7 5c eb 47 b4 62 72 00 5c 6b d2 15 

# Prime 1: 
e9 4e a6 b6 19 be c3 e4 78 87 8e 87 8c f1 23 b7 
a9 f1 2c ab b1 95 e0 aa e0 22 f3 17 73 46 69 31 
38 b1 1a 86 fa 5c b7 55 5a 10 b8 e4 62 2c e9 52 
0c 57 2b ef 29 1d f7 0a 16 d8 85 f5 97 e5 90 4d 

# Prime 2: 
bb cf cc 5a 60 95 34 ee 43 4a 6c bc a3 f7 e9 62 
e7 6d 45 5e 32 64 c1 9f 60 5f 6e 5f f6 13 7c 65 
c5 6d 7f b3 44 cd 52 bc 93 37 4f 3d 16 6c 9f 0c 
6f 9c 50 6b ad 19 33 09 72 d2 1c ac 19 ce 99 6d 

# Prime exponent 1: 
e8 a6 0d 88 39 54 09 73 a2 dd 4a 3b d1 48 05 1d 
f8 d1 0e 82 87 ab b5 45 b0 0c 29 ec 90 7e fe 16 
9f 39 bc 02 2d 56 97 5a fd 5c ff 82 7e 83 da 86 
7e d7 ce 6f c6 c8 a2 b7 e4 e0 35 75 19 eb 49 61 

# Prime exponent 2: 
8f f2 e2 27 37 35 c5 5f 05 56 6a e7 5f 29 a9 c9 
33 a6 2d ef d7 e2 20 01 7f 05 45 fc e9 07 e0 65 
c4 9f 7b ac 34 84 c6 f0 60 49 99 43 32 07 c9 13 
e8 0b c0 1b f7 8d 83 ef af 00 da 17 12 a4 a6 35 

# Coefficient: 
5f 1f f9 6a 8a 90 19 78 4f 92 26 e2 5c 9b fe 25 
08 02 39 43 73 90 25 dd 6b ab 03 7f f4 7d c6 42 
49 85 15 c2 f9 e6 ff 60 60 c5 b7 d2 34 30 d8 d5 
0f 1c 0f 6d 50 c1 80 b4 ae a8 a4 a8 2f cd 2b 74 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 5.1
# ----------------------------------

# Message:
5d c9 f8 b1 2d c8 12 a0 9a a4 b0 6d fc b5 7e 1d 
2e 8d 1c 7d 2c 07 6b 25 d5 c1 8e db c0 46 bd 63 
c7 ca 4a 59 9f 18 de 26 f3 be 73 8c c2 8d 16 67 
2c 00 6e 4d 9c 

# Seed:
43 d6 05 a5 74 0a 97 0b 32 37 27 af 35 2a 1b d4
8d e6 9d 95 05 e2 2c 2f ad 03 0c 3b 84 b6 de a2
d2 2f 91 64 06 a7 69 3c f5 06 c2 d2 51 88 6f 02
20 3e 3f 76 55 a3 0a 68 37 af 8a 8c be c7 b5 c9
2f c0 4c 8c 18 df e9 d3

# Encryption:
21 5a 35 f4 c0 43 5b 07 ed 5d 2c 4b 68 65 bc 28
1c ea 70 50 cf ea 7a 7e 86 e0 3f 8a cb 28 b5 8d
be e6 54 58 91 9c ea a5 a3 3e dd 98 20 1e a6 e7
63 2d 76 22 d5 a5 1d 35 a3 5f ed e8 6e f2 03 ee
f6 eb 34 75 ec 8f 19 e6 9c 0e d5 2c 05 dd 7d 59
e3 53 f5 2b 67 10 af 40 26 65 55 04 10 7d db 86
f9 57 e6 c0 6b a6 7b 1f 4f c9 f1 21 e1 5f 82 73
67 09 d2 de 8d 77 43 2d f0 8d cc d2 a0 cc 77 04

# PKCS#1 v1.5 Encryption Example 5.2
# ----------------------------------

# Message:
d7 74 07 d8 f6 9f 80 dc 08 eb a5 f4 26 28 2d e7 

# Seed:
b8 29 c7 89 be 38 1c d5 9d e2 14 89 db a1 41 e0
bd 1f a8 a3 c3 82 6d 59 c6 d1 10 e7 df 72 42 ba
98 c4 7a 43 92 c7 f2 ac 1c 6e 9d ae 22 b9 eb 74
a7 64 36 08 8b d0 ba 6e 19 91 e1 9e 3a f7 9c f3
f7 de fb 6a 11 61 80 25 34 ba 0e 7e d1 65 34 93
88 72 ec a6 77 05 8a c7 34 67 d4 9e d1 12 5b 50
df e5 d6 d6 5a 5d 24 53 99 b6 bf 1b f1

# Encryption:
2d 48 06 cf af e4 af 36 bd 02 f6 2d 6a 43 b0 0b
41 6f 70 8e 96 85 b1 7a c8 e3 a4 d8 c2 91 80 93
05 76 9d 78 98 f6 fc 85 91 7b a2 fd 8e 58 9f f7
a8 bb 84 bb 7c 12 20 2e d2 79 e0 64 09 a5 c0 a7
d3 24 bc 46 ae 4f 92 82 c9 02 3d 3d fb 3a 79 15
de fc 16 4b 3f 08 26 6a cf 12 41 f8 26 24 98 15
07 41 4e 56 29 78 35 1d c8 b7 a7 9e f5 31 40 21
01 a8 d3 d3 ea a3 53 9b bf 62 aa d9 9a 3b b1 1e

# PKCS#1 v1.5 Encryption Example 5.3
# ----------------------------------

# Message:
23 8a 

# Seed:
a5 88 1a cf f3 52 9f 25 1b 1b 9c 61 9c e9 f9 df
91 e0 a0 3d d8 89 16 36 46 87 1a 62 72 07 ee f1
47 68 0c 32 f4 50 a7 76 e1 9f 54 ec 05 5d c6 8b
04 bd 4d 89 37 6d f3 ea fa 6f ca f6 0e b8 31 84
39 10 b6 25 b6 4f 25 d9 29 9a fc 30 6a 23 76 53
80 45 51 94 b7 5d c0 13 5d 27 c3 b7 d7 2d f9 08
77 5c 7e 90 be fc 0c 5a df 74 a1 69 ed 58 68 f3
d6 34 32 7a 05 78 18 92 54 45 43

# Encryption:
77 10 ee a8 65 7d fd 15 65 16 65 62 df 0e 2e 84
0e c3 e3 de dc 0b 80 2b b0 21 3e 47 a5 ce 97 f4
b8 5b a9 ba 14 19 77 36 3d 8f 54 b0 6d 57 8d 5b
2a 96 e9 69 cf a9 15 df 21 9f 00 2a 85 d0 32 57
04 7b 31 16 a1 c4 dd af 79 1d 93 98 2d 1b 9f fa
24 31 86 e9 e2 b1 9e f0 74 1c e9 8d e2 a4 a1 58
6e 50 12 c4 81 de 23 a0 ef f8 82 fd 62 38 38 d2
01 1f 4f 63 73 8a ff d7 ef b8 c5 0f 46 a6 c2 0e

# PKCS#1 v1.5 Encryption Example 5.4
# ----------------------------------

# Message:
25 a2 7e b1 b2 1f 10 cf 9d 57 1c 33 05 61 0b 97 
f0 da ee 39 90 5c 65 94 bf bf 45 2a 9a 00 d9 e8 
2b 

# Seed:
25 04 61 80 11 c6 73 db 3c 41 22 79 dc 8a d1 65
ab 7b 64 73 ae 19 5e 8d 6d 41 21 49 18 19 8b 34
51 a6 50 8d 61 38 ce dc 51 8d 80 12 ba 0e c7 9b
38 6a f8 fa 40 b0 34 78 bb f2 ba 06 5e 58 2d 61
95 cc be 15 8f 11 78 1e ae b1 b1 72 0b 72 d9 b5
21 27 de b9 55 17 11 e8 87 db d0 b8

# Encryption:
35 72 bd ea 23 05 e1 78 5c 75 4d e7 44 c4 fa 3f
a2 cb 75 71 60 e5 cb 39 a3 14 97 e1 48 5b bd 7c
08 99 c5 35 85 b2 bb bb d9 90 81 b4 16 ef c6 85
78 db 78 e0 ec d0 8d a7 a3 95 3e 38 6b b2 5c 12
bb b8 7c 78 94 42 83 a8 c8 01 87 b4 50 8d ab bc
76 97 f4 3a 8a e7 8a 33 fe bb 15 f3 cd 58 1c 80
d4 9b 97 1b cb d4 8e 44 14 2f 58 c2 c9 1a db 1a
e1 45 aa 9a 83 b3 c5 81 5a a1 a8 ff 8d d2 31 fe

# PKCS#1 v1.5 Encryption Example 5.5
# ----------------------------------

# Message:
59 cf 0b 6b 50 ea 

# Seed:
2b f1 91 60 df 69 88 93 5b d2 46 10 6b 89 09 dd
7b b3 e5 16 90 df 84 d7 6e 4d 31 ac 82 10 44 56
34 6b 4c 3c 9b a7 b5 e9 e6 8e e2 08 6c 84 73 c6
83 02 e2 59 9a bf 6b 31 ce b3 f7 81 ad 6b 56 89
86 f2 1c d6 d7 55 32 8f b8 3a fd 55 48 50 1d 07
0a c2 dd 8f 5c df b6 2c ef 54 5e 81 5f e3 82 bc
0c 67 b6 76 e5 45 6e bb 9a b6 7d f4 77 40 c6 a4
3d e3 f9 a2 47 7a 9b

# Encryption:
2e d5 91 fd 4b 35 7e 94 f4 81 ba 84 ff 4e be 7a
e4 31 05 4e 5c d9 8a 99 58 96 48 e6 16 cd 68 e0
d4 72 4f a8 a6 c5 99 68 6b fe e1 74 7a d0 77 db
ed ad 45 f1 24 4d 7f 8e 00 da 3a 3a 06 d2 31 32
d3 17 1d 74 4e f1 4e 1e 97 cd da 10 9b d2 e5 56
a5 fc 7b bc 60 9a 7f f2 4c fa be f4 b5 6c bb b7
0e 05 06 53 b6 98 48 d7 11 30 75 a5 de be 7a 46
82 15 f8 dc 08 e7 ef 84 fd 55 77 8c d5 b5 96 e5

# PKCS#1 v1.5 Encryption Example 5.6
# ----------------------------------

# Message:
e9 44 52 f5 0a 5e db e6 75 73 ab 22 30 9f a2 1b 
ab c6 d2 25 20 e6 e8 3b f7 2e 7a fa 6d 71 e2 02 
96 da ea f5 4a 60 c8 03 63 04 87 9a 21 31 d1 78 
78 0e 34 8e e0 12 0b 99 7c 

# Seed:
c5 30 44 3a 16 ef d8 d6 d7 2a b4 44 3f 8d b2 44
91 de 99 d5 aa be 51 88 b3 f6 1d c0 48 3b 7e e0
0b 1c 13 25 9b 8a e2 40 9f 1a e6 2d 99 30 c1 1a
4d de f3 e8 35 82 93 88 93 f9 ac 66 8f 79 c6 4c
7f 5d 79 6d

# Encryption:
0e ee 90 c7 08 18 22 1a e2 70 4b be 38 d6 8f 8e
15 4c 6e e7 ad e5 3e 2a 1f 4d 1d ba ac 98 c5 75
91 eb b6 c6 38 bc b6 8e 18 14 35 b7 00 01 ba d1
80 19 2b fd a0 57 32 c0 5e 7f b5 af 22 aa 89 d2
a8 ff 80 cf 9f 08 62 f0 4c 05 ca ca 3d 2a 3a 5b
07 79 94 6c 6d df a0 4c d7 9f a1 64 d6 02 f1 b7
de 5c 95 be 85 e9 60 84 67 e2 5c 29 d0 35 c4 66
09 06 26 9f 6d c0 0a 47 2b 04 46 ea 56 e7 2a 59

# PKCS#1 v1.5 Encryption Example 5.7
# ----------------------------------

# Message:
1c bf a0 e7 b1 a1 0c 13 d7 50 77 b1 cb d8 03 10 
cd 24 10 34 0d 5f 53 72 93 46 4a 67 81 a9 cc 30 
2c b5 38 0e d9 26 7b 3e b2 3c db 13 

# Seed:
d3 6b 7e 17 99 05 9d 1e d1 34 7b 0b f8 24 7c 6b
e5 18 7d 8f 15 21 9e 3c b6 6e c6 2e 1a c4 1f f7
ed 35 7e d7 ca 03 84 e3 1d 39 94 85 61 fc 16 cb
d9 6b 7e 70 42 79 e5 72 bf 56 4e 06 c3 a3 40 1a
27 14 dd 51 d7 21 5b eb a1 c6 61 54 f6 0d d0 cd
4d

# Encryption:
9c 03 dc 01 33 a6 e6 aa ba 92 05 9b df 5a 6c c1
b1 44 b9 0d 2a 94 a4 8e 7b 3c b9 0b 0b b6 f6 24
c7 b1 d1 72 33 1e 43 23 d0 8d 2e 8e 09 95 32 dc
b3 b2 a8 7c a4 20 74 9f c6 34 5c 0d 86 e9 ab ca
71 af 09 a0 92 9e de ee de 83 e7 22 44 20 3b 2b
f4 5c eb 18 7e 9d b3 c7 d3 ad 05 b2 3b 59 62 4c
24 66 96 cf c7 58 06 39 14 02 e4 44 e3 97 49 69
88 e1 e1 f4 2c 6a de d3 0c dc 93 79 37 f3 00 54

# PKCS#1 v1.5 Encryption Example 5.8
# ----------------------------------

# Message:
e1 72 a6 b8 b4 96 f0 77 73 8b 74 f6 d8 b2 92 dd 
a6 07 f2 ad bf b3 72 be 37 ee 00 08 88 be a3 1f 
99 cb a1 cf 39 32 e4 be 37 17 c9 e1 68 90 1a 32 
d1 b8 20 be 4f b0 13 75 27 a2 48 18 77 fe 01 ee 

# Seed:
84 0c e1 3b bc 96 17 dc 9f 3f 26 b1 47 30 1a 6f
46 30 0d 77 81 a5 d9 81 16 2f 86 92 87 37 1f 1d
59 58 76 4f b0 0b 05 53 70 ec 71 1b ba 52 83 fc
b0 0b 83 bc 02 17 5e a1 01 7b cc 83 53

# Encryption:
99 3e 39 6f b5 7b 2e a6 a1 a3 fc ed 9a 69 d3 61
cb b6 26 5b 26 50 3c 17 5f 84 c6 1a 41 ea 3e 1c
e4 fb b6 2e 01 d6 42 0e 22 fe f1 d9 e2 8a 58 83
e2 ea c8 2e 05 f3 58 ea 75 f7 7d a4 89 7b 6b 64
9a a4 74 28 39 41 93 dd ec 64 8c 3a 7f b8 1c fc
f4 b5 1c e3 eb ba 78 ae dc a7 bb 91 7b 35 b3 e2
2a eb 20 1c ea 96 59 2e 50 e0 d2 84 1e 7d 2c e0
d6 9f f3 03 9d c0 1e 96 4a 97 7a 01 76 83 b3 87

# PKCS#1 v1.5 Encryption Example 5.9
# ----------------------------------

# Message:
c8 f0 ea 23 e0 66 11 e4 fd 27 b6 1d b7 92 0c 55 
f3 c0 a2 22 12 88 38 e4 cd b0 62 e1 76 b2 1f c2 
32 53 55 8c 5d 40 de 2d fd 62 0f b7 cd f1 39 9c 
2a f8 fc 77 ca 33 35 

# Seed:
16 4d 77 b5 d2 6a e6 d7 ab e7 ca ed 62 5d 87 c2
11 cc 50 9a d0 17 2c 20 83 3d 8f 98 ca e3 8a 2c
37 0e f2 1d 40 96 da 84 1d be ee 94 8e c6 34 03
ca bd 4a 5f 71 ac e4 93 64 aa 7d e2 0f 32 c9 88
33 7a 11 5f 83 46

# Encryption:
7a 8f 15 ee f5 10 ad e8 d5 c3 17 f9 06 4a d7 da
e6 c9 3e 7c f1 56 a7 37 22 02 32 58 f8 b5 74 47
34 70 00 34 a3 de 6f 13 7a f6 e9 00 46 d8 6e 9b
90 59 0f a5 a6 50 ce f4 fd b4 d3 36 02 33 af 86
f4 a7 a2 3c 24 3d 19 51 c6 66 b6 73 c3 3c 7d ec
4f 51 ac e3 4b 80 5c 0a 9e 67 e2 09 cc 7f 9e d6
9b 8f 5e b5 c5 53 e0 f1 5c 10 30 4b f5 6d 7b e1
71 f3 1c ce 88 f3 7d 1f b4 a2 a0 04 18 89 75 76

# PKCS#1 v1.5 Encryption Example 5.10
# ----------------------------------

# Message:
19 db 24 22 05 c0 3d 7f a9 93 5d 9e 04 fa 6e cf 
38 a5 1e a9 98 ac 8e 4b ac a6 cd fd 6a 0a ce 1d 
f3 67 e7 3d 23 c2 40 af 76 b6 2e 9f e9 21 5f e9 
43 

# Seed:
be 49 52 05 55 69 56 fa e2 a2 2e a7 0c e1 02 de
06 6c 9e 58 95 96 06 21 74 84 a5 b1 50 36 ff a1
d4 61 23 9d d4 7b 4f 38 1c ea 71 51 6e 2d b0 fc
36 9d 72 b4 40 69 65 12 a9 72 88 f0 6f c0 bc eb
96 82 86 e9 95 e0 2d 21 8d 9c 26 62

# Encryption:
55 12 b3 99 9b 30 c9 c1 44 0e 59 75 93 1d 55 f2
1e 9e b4 22 b6 2d af cd ab 5d 50 03 a7 5e b1 24
81 99 86 36 19 13 36 1d fc 46 ac 29 aa ba 8e 1a
a0 2e 1b a4 44 67 16 2d 20 f6 3a d1 70 fe 0d 87
a5 3d 93 c6 4e 02 6b 12 be 6b c2 b8 eb 0e 57 c0
39 eb 60 f3 2c 4b 52 70 35 f7 03 a7 a8 37 4b d7
fa a7 b5 40 4a 3c 5a ad b7 92 e2 5f f9 28 76 b2
3d d3 a7 42 2c 45 26 6c 6d 98 6e ec 53 34 b9 ba

# PKCS#1 v1.5 Encryption Example 5.11
# ----------------------------------

# Message:
49 a7 61 f8 c1 8e f9 23 62 d6 ab b2 4c 07 fc 72 
82 55 84 24 53 69 4e 17 60 58 99 f4 37 b3 1a c9 
8d b5 16 

# Seed:
d8 cd 85 ed fa 0a 84 d0 76 a9 f8 b2 f9 3d aa eb
a9 ae 37 43 81 ea 4f 8c ea bc 14 f6 2a 4e d7 63
8c 1e 39 67 57 de 3a e2 b7 ef a3 a1 7c 9a 55 86
da 84 a5 e5 0e cd ed 61 08 7f a6 f0 ce 93 82 87
99 8a c1 b9 bc 33 21 a7 ed 16 0d 28 67 04 e0 52
6e ce 7b 30 b4 68 14 64 9f ec

# Encryption:
9b 47 82 68 40 62 12 ca 05 30 f4 31 bd b2 63 72
61 50 84 ca 48 8d a4 34 51 d2 5a 22 b3 5a c6 fc
61 e3 70 74 a5 c2 2b c1 c7 01 db 19 32 b8 c5 57
b8 48 7c ea 56 60 50 e4 8a d6 e0 37 6f 8d b4 19
8c 4d 27 db 2e 6b 28 c2 5a ed 83 7e f4 77 42 d5
eb 8e b1 d8 b4 32 c9 d5 73 cd 4b 86 fd f3 2c 52
a3 d0 f6 cf 92 cf 3c d9 51 96 77 a5 8b 1d 1d 99
4f c1 c9 05 7a c1 06 e8 16 04 59 26 b4 5b 00 e5

# PKCS#1 v1.5 Encryption Example 5.12
# ----------------------------------

# Message:
84 e8 28 f7 15 f2 28 a6 02 65 

# Seed:
f7 f2 75 a8 53 d4 e1 26 d7 d0 c3 8e f7 03 f3 fb
da 7a 95 20 78 8d 7a 81 a3 1b 05 30 d4 3f e6 c9
4b 1b 1b b1 08 51 20 9d b2 6a c4 b8 88 ae ce ea
77 13 82 4c 29 38 b4 c6 43 1b 2b 03 c6 93 ab 7b
54 63 15 41 54 6a c4 03 94 79 85 48 fb ba 95 88
2d 91 a1 7c 27 e7 dd 53 02 6c 96 79 1e e5 5f 24
7d 7f 89 8f ea b3 70 9a 13 2a 78 26 66 a1 4d 0d
f1 a8 4e

# Encryption:
4e 3f e9 6e 8f 96 e5 b3 c6 11 ca db 96 ed 51 04
20 98 ec c5 47 be 7b 88 f8 de a7 65 ae 14 e8 35
0c fe 39 dc b1 c1 d6 e4 17 9c a4 04 d5 38 4d 87
b0 66 50 75 ee d7 da 7a af 71 00 8e 24 67 d7 0b
ff f6 23 b4 59 4b 6d ff f9 0e 3d 84 85 f9 41 89
48 6c a0 ca 9e 72 2b 2a 77 7e 25 b5 82 f7 ad 4e
c5 10 3a d7 67 85 9a c5 9a 1b 8c 5c 19 71 30 17
48 fc c2 64 f6 c9 29 36 4a 8d 9a f4 22 55 c0 f6

# PKCS#1 v1.5 Encryption Example 5.13
# ----------------------------------

# Message:
c4 79 7e 8a 6f 26 9b f2 5d 4c b4 ec 3f a4 6f 8f 
11 e6 b3 69 fb da ec 1e 51 97 83 58 d4 6f 3c f3 
b8 42 91 7d 96 7a a9 d3 0b 18 34 53 93 7a 68 26 
56 b2 7b 

# Seed:
17 77 42 19 7f 04 11 44 3b d8 7e 28 ea 88 d5 4b
4f 2c 7a b1 dc ae d8 1a 56 f8 d7 87 91 12 44 54
02 85 04 d3 22 e1 de 34 60 47 a4 93 93 3e 87 62
8c e0 8b 06 c0 17 08 2d cc d3 8b 48 94 65 14 ea
37 7c 2d fc 75 2a dc 24 fb 57

# Encryption:
79 84 c3 ba d8 6a 54 79 90 e0 47 5a 48 4f 8d ba
5d 4d ce b0 d3 e3 fc e0 71 d6 f8 71 76 b7 cb e6
f8 11 2d 81 d7 cd aa d7 7e d0 d5 78 8e 65 f8 bf
5f 0c 2a d0 df 07 cc df 54 b2 da ce fc 19 ba 65
20 1c cf d3 61 d1 86 b7 d9 e2 69 aa 1e 6a d2 c7
2b d2 3f 58 08 4f db c3 bc 60 e1 7a 33 e3 c5 5a
95 eb 0c 38 a0 81 bc 0d 39 81 db 26 a7 21 2d 6f
69 1b 33 6d ac 46 b6 4a e2 25 38 a7 b0 08 7f 25

# PKCS#1 v1.5 Encryption Example 5.14
# ----------------------------------

# Message:
a5 e9 de b1 c2 0f 98 2d 5b 7d 4b 87 d7 99 46 1f 
05 3d 91 9e 

# Seed:
59 75 9c f5 f8 38 d9 52 49 3d 0f 42 81 e3 ea 0f
e2 16 97 1c 0a 2e 24 54 b9 6c 8a 11 b4 c6 91 27
15 90 24 db 6e c5 c3 36 40 d1 20 29 58 99 f6 66
6c 94 17 b2 02 a8 6c 26 ef d7 c6 13 4c 92 fd 86
b8 32 3d 17 4a 62 53 48 81 38 2d 7c 6c 9e 1b 8c
1e 95 da f2 10 c3 e9 ba 43 e5 88 79 f3 4f 2f a7
1e f6 ae 4b 68 d6 41 47 be

# Encryption:
70 ac 10 2d 07 1e 3d 90 22 81 83 62 16 e0 81 29
0b b5 df cd 56 68 d1 1d ae fa 0a b0 64 59 9d 0f
91 4b 47 29 61 37 5b bf 5f 2a 66 6f ae 0f 6a 25
ba fd 44 f6 65 c1 7c 14 4d c4 d4 cc 0a 5d 5b a5
5c 47 a4 cd e9 59 b3 2b aa 52 32 a0 7f bf 93 78
c9 c5 3c a2 b3 77 81 c9 3a 1c c8 d6 52 94 78 a1
c6 73 03 4d da 7f f3 35 60 78 6a 46 4f 5b 4a 55
9c 62 6a e2 95 bc 91 d0 ee d9 37 5f 49 e3 e4 aa

# PKCS#1 v1.5 Encryption Example 5.15
# ----------------------------------

# Message:
73 9f a7 6d bd 12 73 03 b7 ab f9 3e 1d 7b a7 29 
75 5d 6c 81 1b 5e 93 35 5e 0c 01 1f 74 46 4c 7d 
b4 79 19 3c 3f b7 38 0a 62 a0 c0 06 a2 d1 dc 49 
a7 66 af 63 fb a4 52 7c d1 57 50 6d 62 c2 1a 

# Seed:
a0 b4 da ff 3e 26 ce bb 3e 4e 3a 43 db 36 c4 66
fb 8c e6 05 b2 5a f4 c9 da 74 4b 62 d4 1f 9e 62
c2 28 5c 39 0d 60 d1 8e 3d 7e 67 5b 4a c3 19 67
24 45 19 46 bc 1c cf 2a 9b 56 2c 45 33 c9

# Encryption:
2b 79 80 91 b3 a3 91 53 3d 62 dc 0e 41 7b a6 de
da 00 5b fc 30 ab 7d c8 2e 8f 9b cc 74 17 bc b0
04 34 8c 6d 00 e5 37 d2 72 2b 84 38 61 48 92 45
ab 0d 51 f2 11 44 7d ac 33 a3 f9 dd 6f 3c a6 6b
bf a0 d1 ad f9 8b c9 09 95 15 92 69 76 b9 25 8a
ab 63 20 4a d8 91 65 c8 7b be fd 8d 98 85 34 b3
74 07 df 7d 43 ad 39 1e ed 99 82 47 28 ef c3 a5
33 b7 89 b4 7e 8a a7 12 16 17 47 4f 33 25 c5 1a

# PKCS#1 v1.5 Encryption Example 5.16
# ----------------------------------

# Message:
5a 44 b5 47 bd a1 9e cc a1 dc 7b c0 45 50 20 5f 
66 c5 de 0a cb 

# Seed:
2f 54 85 d2 5f e5 ce 40 ae 62 a1 12 97 6c b4 a5
39 b7 4d 96 ef 8b e8 73 32 5e 20 4b b3 f8 66 07
a1 6a 2c 2a b5 0f 69 78 09 ed 03 01 72 70 ef 20
a1 02 c4 3a 2c 4c 3b e6 ab 7a 8a e2 dc b5 69 84
d5 e0 65 52 45 93 eb 70 70 83 4c e5 53 f1 75 69
20 bb cb be 4f b2 6d 35 d4 ad cb 59 df 52 46 35
6f f1 2e 7a a9 ee 6d ef

# Encryption:
62 d1 48 9a 40 3a 90 fa ac 67 7a bc 17 4a a7 24
3b c7 51 a9 64 69 5f 6c 32 b3 9d e0 18 fe 46 43
44 20 ea 76 59 be 2c 41 0b c5 6c 4e 3e 7a 1b 16
77 fd f4 ad fb 23 24 f0 41 ed d5 79 0c 4c df ab
36 55 e0 7e 41 fe 9d 73 29 21 51 b0 dd 5b 96 fc
84 a6 b2 0f 6f 3b c0 f6 0e ae a8 d8 29 17 b2 10
83 05 59 40 fc 02 ee 3e 17 c3 78 bd 4d 85 06 d0
8e c4 5f 33 de 57 80 f0 1b d3 18 a9 a1 67 87 ad

# PKCS#1 v1.5 Encryption Example 5.17
# ----------------------------------

# Message:
16 80 2d 80 3c 

# Seed:
02 f0 e7 62 c3 39 17 bf 6a 4d f1 1f a2 84 73 54
7a 5a b0 76 7d d3 a6 1a 8d 05 cc 1e 6f 98 34 5a
f1 a0 0e 42 f6 2b d8 ec ca cb d3 7f 4c cb 80 9e
9e 05 5f 73 d3 37 5a 60 41 d9 78 2c cd 85 d5 9d
14 f4 3e 5c 94 10 25 7b 90 6e 41 cc 8b 97 35 47
d0 62 2b d2 1d b2 9a 91 38 d3 b1 be de 38 ce 5c
94 c4 e1 d2 a0 a8 40 0b 45 ba cd 42 69 79 7c 38
5a a3 06 6e 65 80 72 99

# Encryption:
75 3b 9c ee 70 d4 18 1f 95 63 87 80 db 7a 04 f9
b1 2e a3 8e 5d ae e4 f2 89 4c 02 67 3a 53 f4 85
30 74 6f f2 85 8b 78 7e fc b4 2d 45 e5 31 be a1
c8 da e9 48 77 15 3b 95 6a c7 b2 87 46 ee 21 c6
31 bf 9a 3b a7 9d 2b a8 13 21 b7 96 03 b1 d0 1a
4e 90 9e d5 a2 7c bf 27 c9 57 78 3d 3f 79 50 e0
1d d8 f4 47 f1 0e ce d3 6f 2d 19 35 86 f5 cc 17
fb 62 2a 05 fa a1 fb 5c d2 aa 06 4c 08 67 b1 ca

# PKCS#1 v1.5 Encryption Example 5.18
# ----------------------------------

# Message:
d6 d0 f6 03 85 97 9c a5 06 fc 83 34 21 bc d2 2d 
a6 a0 6a 8f 37 0a 7f 02 fe f4 7c 1c b2 19 9f 26 
45 c7 54 90 fb f7 89 ad 52 47 04 da 8a 27 66 c6 

# Seed:
a0 3c 05 67 2e fe 23 b4 4b 26 c7 93 e7 05 3a 77
04 8b a2 db b4 b5 3a e9 75 18 54 e7 ad d7 d6 7c
12 cb d1 6c 0b 19 34 d2 c2 3e 77 cd cc 89 ce 1d
45 c7 61 15 8a a8 61 31 71 a7 39 01 ac 1f 61 c6
57 05 53 3d 3e 63 9b 57 a2 48 92 91 79

# Encryption:
00 78 dc 48 a6 61 58 99 2a aa 68 fc 3c cc 62 8f
92 a7 08 d0 b1 d4 38 37 20 8d 53 4b c3 f9 be 1c
14 12 ed 0f 9f 7b ef 49 43 0d c9 e9 98 f7 52 e0
74 77 68 b4 ae 38 14 46 96 c0 03 d6 d2 5e a1 a6
ca 6a ec 92 4a 9f 4d 9b 57 5a 8f 13 6b ba 29 bf
31 c1 3b 70 50 bd 55 d1 00 0d 43 3d aa 6c f1 0b
49 11 6c 80 63 10 7f d3 a5 bd f6 15 45 c5 d8 63
f6 a7 88 81 78 6c c8 dc 37 6d 36 c9 11 36 82 25

# PKCS#1 v1.5 Encryption Example 5.19
# ----------------------------------

# Message:
b0 38 1c ac 04 f3 10 15 04 96 8f 26 d6 55 47 45 
38 3b d1 71 d3 61 56 dd 36 80 b3 db 6f ad 7f 77 
1f 7d 

# Seed:
cc 54 8a 67 77 cd e9 53 c1 5a 71 fc 49 7c 0c 36
17 b1 bb 05 6b 03 28 8d 9a 54 8b 69 3b b4 30 8b
67 c1 da c3 8b b9 b8 c9 cc 89 6a c2 44 a9 e3 0d
13 24 30 f4 ce ae 57 90 34 3d bc e3 8d 05 6f 27
b8 6b dd 9d 32 a8 17 1f 3b 3c c2 fd 14 22 65 ac
9a 68 dc 35 36 27 33 9c d8 83 03

# Encryption:
45 6f 7c 3e 3b e8 5f f7 bd d6 b5 b2 50 bf b8 1c
d6 fb b1 86 d2 5e 0c 1c 52 59 d6 78 87 93 c5 41
ed ac b4 ec de db 8a 89 29 01 34 a6 06 f4 6a 81
99 1c 13 b1 20 e3 30 56 bc 0f c7 e9 7b 34 2d 0f
20 05 18 13 dd e4 5e 0b 59 6a 7d cd 69 04 90 3b
8f c0 76 a6 e2 4b 3a d8 01 34 16 d9 fd 18 48 30
55 4a d5 4d 55 48 bc ad da f5 92 0a 02 49 55 88
78 25 dd 37 1a e4 ef 90 06 9a 4f 31 1c 5a 17 29

# PKCS#1 v1.5 Encryption Example 5.20
# ----------------------------------

# Message:
e5 

# Seed:
fe 0b c7 ac b5 6c e1 4b 4e 2f 55 fb a0 e2 b1 71
54 90 7b e9 4c e4 85 c6 98 4d 61 f6 7c 04 a7 40
a2 8d 60 94 ae ae 33 b3 ea 0d 58 18 3e 1e c7 f6
01 b2 ab 82 0f b8 a7 ff ac 0b 4f 96 0e 1b 4a cb
e5 7c dc 35 b2 25 c4 97 fc ac a0 30 19 a2 95 b3
e6 6d a9 6f 53 79 cc d1 da 44 47 9a 4f 21 35 e1
06 3d 71 a2 82 c1 e6 6f db d5 d9 53 f5 71 8c 25
39 d0 0c e4 9b 45 18 20 45 4d 2d 49

# Encryption:
5c 53 36 77 a1 dc b3 63 95 da 9a 6d 34 77 bf bf
71 51 2c 6a 93 3d 04 1c a3 13 44 e1 cb e1 e5 55
88 a1 14 6e 3d ec 46 10 ef ac 41 b5 f8 02 66 02
66 52 b3 d7 aa 59 42 74 d9 d9 20 d3 3a a0 ad 9b
4e c5 9a a8 80 71 2a 1d 1c 36 8a 45 7f 35 c7 43
88 b0 42 f2 49 8e 9f 4b d4 5a 26 f3 21 dd 9e b3
33 ef 80 af e3 af 9f 72 9c a1 8f 42 c8 8a 71 73
85 8c 54 20 66 f8 a2 52 cc f0 fe af e6 b5 a9 24

# =============================================

# Example 6: A 1024-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
dd ca d6 a3 8b 37 04 cb e0 6b b1 b5 1d 11 62 58 
49 78 a4 29 1f b6 73 b4 ea 30 ed 8b 51 a4 bf 26 
1d c9 f0 f4 69 ce 99 88 a0 89 f0 84 36 64 64 a1 
80 cf d7 17 10 69 a6 f6 36 d7 5f 23 40 1b 30 cf 
43 ad cf 87 0e cd 24 58 2f 4d a2 95 22 91 51 dd 
cf c7 c9 91 86 b2 45 88 5c b6 31 96 c4 a5 72 6f 
20 7e e3 60 af 3a d8 c4 83 85 a4 e0 84 1d 7d 85 
1d 54 54 5d e7 67 d8 f9 99 dc 17 eb 0c 57 11 89 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
dd ca d6 a3 8b 37 04 cb e0 6b b1 b5 1d 11 62 58 
49 78 a4 29 1f b6 73 b4 ea 30 ed 8b 51 a4 bf 26 
1d c9 f0 f4 69 ce 99 88 a0 89 f0 84 36 64 64 a1 
80 cf d7 17 10 69 a6 f6 36 d7 5f 23 40 1b 30 cf 
43 ad cf 87 0e cd 24 58 2f 4d a2 95 22 91 51 dd 
cf c7 c9 91 86 b2 45 88 5c b6 31 96 c4 a5 72 6f 
20 7e e3 60 af 3a d8 c4 83 85 a4 e0 84 1d 7d 85 
1d 54 54 5d e7 67 d8 f9 99 dc 17 eb 0c 57 11 89 

# Public exponent: 
01 00 01 

# Exponent: 
5e 48 e0 3d dd ec a1 a9 59 d9 ee 4d a3 f1 c3 f0 
ec 2a d0 fe 7a 70 c1 77 63 27 31 94 4c 3c d0 d5 
8f 4c 4d 96 59 74 6e 68 5a 76 c9 3c 2b 33 3a 64 
3d b2 1d 29 bc dc 6d 11 b5 f6 90 87 17 b5 77 65 
99 ff 30 88 b2 01 4f fc 51 f1 8d 93 22 24 f1 05 
9b 22 39 ea 56 44 e8 06 9a 5c c4 31 ae f6 07 38 
0a a9 24 c1 58 ab cb dd 97 51 f5 4e 67 79 c4 ec 
23 21 68 b0 01 4f 4c 8b 49 7b e9 49 ae e6 57 51 

# Prime 1: 
f9 47 1f 2d 22 13 a9 2c f0 a7 c3 84 04 d4 0e 32 
2f 14 f4 39 61 f0 1e 92 33 63 cf 66 53 e8 98 4f 
b6 e6 8d 66 e6 f1 59 80 3a 44 bc dd 09 e0 56 19 
6d a4 00 7a e2 a3 7f 40 57 bf 7e 3b 5e 4a 80 23 

# Prime 2: 
e3 c5 f8 cb fd 51 c6 c2 66 b1 a3 2f b2 f6 fa 48 
9b 97 54 b2 27 c7 7f 53 59 c5 5d fa 03 80 fe 9a 
02 b4 d3 95 05 f3 ab 36 95 7c eb fa d5 34 d7 f9 
a2 67 eb ee 19 af e5 c6 24 04 3e 30 19 d0 ac 63 

# Prime exponent 1: 
2c f4 79 97 df 1c 62 97 1b 33 76 1c 19 ee d0 3c 
96 a7 15 c0 3f 7c 59 57 0e 73 82 94 b6 af 2c c0 
bb 80 6a a1 d3 26 55 78 bd 89 65 f2 0a f4 ed 32 
1a c6 c7 db 61 82 10 d1 de c1 47 17 97 d2 a1 b7 

# Prime exponent 2: 
a6 c0 29 bb d5 91 c7 cc 4c 78 61 fa aa af 8b 5e 
1a a2 78 7c 11 09 49 5a d1 0e e0 15 07 f9 a8 eb 
41 6d f6 b4 53 ee 26 fb 48 07 73 8f 68 05 4e d4 
50 24 7a a2 0e a6 2a b6 9b 69 8b c9 95 2c a2 f7 

# Coefficient: 
aa d0 60 de 5e db 35 81 8b d6 3e 85 d4 2e ee 55 
e4 cf 8f a4 2d 4e a5 92 83 ce f9 6a ca ad 55 5d 
47 8d 2f d7 c3 8f a9 31 fa 1a eb 6d e5 66 55 3d 
08 f1 cb c0 6a 51 0f c2 e5 69 b5 a8 22 67 ac 91 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 6.1
# ----------------------------------

# Message:
a3 e1 52 75 fb 33 ae c3 7b d3 dd 58 2e 19 f5 d3 
8b 9d 0d 

# Seed:
78 da a6 1c 07 f9 41 b4 b4 40 e7 38 33 8d 06 cc
15 7a 01 57 4a 72 fa 78 d3 63 dc e9 94 09 04 22
0a 71 32 35 69 96 f0 7c 01 d5 4e e5 4f a3 2c 5f
aa 1b 90 d2 43 0c 80 a2 32 17 e0 6b b1 c8 04 3d
61 ff a1 8a a1 d1 8e 15 e4 30 aa 12 1c df f4 3f
ab 2b b0 de bf 73 e3 d5 a7 5b 46 f9 0d f7 3d 65
6b b0 66 ac 1c 76 8f a3 83 e4

# Encryption:
9b f1 dc fc 65 4c a7 a2 3d 80 a1 2c 09 c2 59 d0
4b ae 07 e8 31 f6 09 e9 f0 35 d3 56 41 4e d1 06
f9 b4 62 35 fa 3d a9 1a 32 b8 fd c8 fb 88 30 c9
65 23 dc aa 9b 25 38 fc 01 02 dc 0f 7a 5f ae 86
35 b3 b1 23 14 c3 90 55 fa ac fb e0 0a 15 16 34
24 14 8c c9 e0 f0 ab 42 df 09 02 3c 05 2a 46 ae
3f 86 a1 8d cc 53 80 ce 39 06 2e 36 23 75 d3 a5
f9 e3 b3 4a 5d a2 5c f0 de 9c c1 9c 4d 04 f6 3f

# PKCS#1 v1.5 Encryption Example 6.2
# ----------------------------------

# Message:
44 3a b0 3c 46 04 db d8 bf 80 06 ae cc 2a a8 de 
64 a1 55 b4 f9 0c db 08 59 98 6d 5c a3 60 66 03 
59 80 8c 59 bd 9c 2d e7 75 fc 18 52 02 24 b6 69 
c3 25 

# Seed:
3a 6a 60 89 f7 27 dd 16 92 97 a5 79 fc af 8e a7
f8 e1 2d 54 27 f1 f7 90 1a 90 22 a2 1e 2d 82 d2
f0 8e 63 ba ca 26 7b 65 3a af 89 de 23 2b a3 d3
92 d1 cb 49 dd 76 b3 88 b2 da df b0 09 4f da 97
18 f1 78 37 38 d1 8f a7 aa a6 29

# Encryption:
5a 0a e0 de 28 fd 33 cc 98 01 bf 4d d3 06 71 23
74 53 63 ca ce 4d d8 b7 b8 b8 11 d6 48 2f 59 12
0c 0d 65 3e ee 86 a6 1a 01 2a 18 0e c5 d1 7e 99
14 6d 0c ca 0a 58 7c 85 cf 01 dc 7a dd 84 23 0d
91 80 bd df 5a 77 19 f6 9d 1f c4 81 af 6f 47 db
8c bd 4e e1 87 1a 57 3f c8 76 7c e8 be ed b5 c7
3f a9 3f 0c 53 80 4a fe 2e 76 cc eb b8 7c ba 00
cd ac 94 de 40 e2 b0 be aa 8e 2e 41 58 c7 78 50

# PKCS#1 v1.5 Encryption Example 6.3
# ----------------------------------

# Message:
d1 ba 30 e8 8c dd a3 3d 2b 41 89 6f 43 df 08 1e 
ef 20 ca f7 8d 7c 1b 97 0c 6a 61 72 e8 38 39 dc 
43 d4 be 29 f9 a7 72 ba 41 59 73 8b c6 13 29 68 
61 8d c7 19 7c 0e dc 57 a1 

# Seed:
9b b6 2b f9 a7 51 fe 41 81 66 43 68 98 ed e8 35
cf ae e2 f5 0e fc ad 60 94 2f 2f b2 da 1a 20 38
03 f0 be 80 be b1 7f fa d0 da b2 18 b1 28 02 7f
34 97 a4 f8 d4 1a b9 a1 be 26 4e 96 b4 45 48 76
7b e8 d4 a7

# Encryption:
81 19 1c 54 75 27 3d e6 6a 41 bf 56 04 f3 2b 58
b7 59 a1 4e 74 26 c7 46 de 4b 53 ba f7 80 bb a4
2b 42 e9 27 8a a9 45 27 f0 ee bf 85 5f 46 2d 6f
bb 29 78 a4 65 30 37 d8 b4 48 e1 b8 07 a1 83 1d
bc 53 22 d0 66 98 4e ec 28 11 4f b7 20 1c 79 6b
57 3a dc c5 cc 92 7f 3a 58 97 b2 00 55 64 ef 1a
2c 01 67 f5 43 65 51 18 34 ac 6f 49 58 b8 31 8d
09 e6 78 54 d6 31 ca 52 80 76 86 ab 70 5d 71 a3

# PKCS#1 v1.5 Encryption Example 6.4
# ----------------------------------

# Message:
a5 4b c2 27 8b 56 76 85 c9 b5 68 54 e4 c1 c4 06 
d9 16 17 47 c6 81 34 80 16 3e e3 af 23 b2 1e d1 
99 83 e0 f4 2b fc 93 25 cb 5c 82 e1 a4 7b 8f 

# Seed:
cc ba 5f a5 c9 4e 79 83 2a b0 46 c9 77 74 95 b1
63 bd 3a f2 17 59 0f ec 5a c4 8c 62 78 6a 27 f3
70 d2 85 99 55 e4 5a fb 2b 79 f1 2d e4 16 11 45
be 62 ac 13 a0 3b f4 81 95 fb 9e 5a 18 b8 2d eb
bf 3d df c8 08 25 d5 93 fc d4 02 e7 4e ef

# Encryption:
3d e4 c3 e0 59 35 7b 8a 54 83 7e a1 59 62 cf e2
00 7c 5e a8 98 5c 93 51 91 64 a6 89 b7 55 b6 1c
8b d2 73 96 9d 33 3c 4b cd 9b 06 03 53 c3 7b af
13 eb 42 2f 1c b9 77 56 e6 b4 94 6a df e1 af 75
e9 fe 2d 95 b4 b1 3d a1 73 2b d8 b8 be 11 97 02
79 19 53 0c 34 7f 3d d1 03 9e 34 8a 53 b1 16 f9
f8 fe 89 36 a8 4a cc 39 f2 e0 69 56 b6 78 d2 0f
df 95 17 07 2e 02 f5 70 78 fd c0 46 59 40 0c 5c

# PKCS#1 v1.5 Encryption Example 6.5
# ----------------------------------

# Message:
f3 c0 0b 00 9e 5e 08 a6 3b e1 e4 00 35 cd ac a5 
01 1c c7 01 cf 7e eb cb 99 f0 ff e1 7c fd 0a 4b 
f7 be fd 2d d5 36 ac 94 6d b7 97 fd bc 67 9c be 
68 f1 a2 f3 62 59 c5 58 f0 4d 

# Seed:
e2 2d 5e 43 b1 eb a1 ac 0c e6 5c 32 70 51 0e 0f
13 c9 4e 96 24 ee 52 56 59 ef 4d 57 37 88 20 c9
35 22 9b 30 99 aa 2b 23 50 61 4f 8c c4 29 58 15
a2 c9 ed b2 d9 c5 9c 73 d1 aa 90 0c 21 34 c0 a7
d0 90 9c

# Encryption:
53 3e 67 a2 bc 5b 3f 01 34 2d 8c b8 d9 25 d7 3b
4d b3 4c b6 75 da 90 39 22 6a 98 e4 d8 13 c6 20
11 09 90 a8 e6 ac ba 50 fd 04 f3 30 7d eb af 20
d4 f3 74 cf 6d e0 d9 b2 1d 86 e2 66 07 9c f2 f1
8b 45 03 20 8a 21 5d e2 b1 1c ca 9e 34 64 fc 5a
c1 dd 7e 96 b2 fc 04 09 e4 2f 46 a5 06 12 a6 b2
06 1e d1 61 9a 7f e4 67 96 ed 8f 52 06 9a 5b fc
84 08 d5 58 f5 2a 03 33 2e e8 ed de f8 f7 45 d9

# PKCS#1 v1.5 Encryption Example 6.6
# ----------------------------------

# Message:
aa fe 5b 27 11 11 ef b8 79 2f 5a a9 23 83 07 29 
76 b7 2a 0a 27 2f 90 c5 24 61 f8 8a fb 1b b6 b7 
ec 26 38 1c 65 76 a4 10 87 a0 39 80 9d 14 f6 11 
60 67 59 4e bb 

# Seed:
75 0e f0 86 9f 8c 75 7e f4 31 57 8b 45 a6 e7 41
bd 1d 96 0e fc 37 89 b1 0d 2b de 27 38 18 07 4e
bf e5 fa ae 10 ac 24 4f 89 f6 c0 2d e5 d1 ad ae
fc 7a 8f d2 4d 7b c3 76 fd 65 d3 5e 39 50 8c 42
36 ac be 2d 5c a7 69 4b

# Encryption:
34 4d 43 37 61 2b 22 dd 40 2b e3 79 e6 b2 65 0b
51 9e f3 7b 7a b4 85 81 94 52 d1 67 c1 b2 15 db
d3 fb 24 f9 b2 f9 29 86 69 cb 1a ee 14 1a 7d 89
01 64 20 43 11 1f c3 8b 3f 40 ef 0b 7f fd 7d f7
6c 2d 92 e3 29 41 1c 75 e0 f1 72 85 bb 6b b8 26
88 12 8e d9 bb 95 1c ae dd 7d 06 7e dd 0b 13 e8
27 5a c8 86 25 d9 7c e8 d2 0b 69 b3 57 38 b2 f4
72 6e 29 84 b8 df a8 66 95 aa e8 8d 9e 17 6d f6

# PKCS#1 v1.5 Encryption Example 6.7
# ----------------------------------

# Message:
a1 22 4d f9 aa b6 58 78 45 b2 a3 93 a5 a8 76 c1 
7d 95 9d 53 5b 54 19 d4 12 a9 a5 31 bb 43 7e 1d 
ac 1b 54 6d 62 

# Seed:
76 1d c4 f5 1b c8 51 8d 62 7c 45 b3 e9 81 8b 85
42 a0 6f fe 17 2b e3 af 5c e7 a9 05 3f e5 4e 69
70 12 49 73 37 4d fc c1 f4 9f cf fe 95 7a ae 8c
9c 3b 13 0f 46 05 d2 c3 ef a2 93 2a d0 83 de c5
8e 70 d4 f6 92 6c 80 b5 d4 89 1a 1a 55 9e db 0c
af fa ca d7 5e b2 64 83

# Encryption:
78 5f 6a aa b4 d2 f3 18 d4 af 37 b6 e0 07 4e d5
a4 19 4f a6 05 a7 ec 87 d0 5a 07 f3 34 9b 5b 92
f5 fc 47 90 ea b1 37 86 cb f0 35 c7 8f fc f1 34
4d 1f 3e cd aa e0 16 72 22 2e 6d 4a 96 55 59 e2
c0 82 91 cb 1d 4c 2d 4e 68 ff 8e e7 15 23 f6 dd
ae c5 0a 4a b2 2e dc a2 47 36 4c 92 d8 73 99 e8
a7 1d f7 43 6b 62 d8 ba 8a d2 02 94 cb c6 0d ca
e0 30 5c 79 73 f7 fc b4 a5 cb ed 15 71 3a 7a 16

# PKCS#1 v1.5 Encryption Example 6.8
# ----------------------------------

# Message:
1e 0e 3f 65 0c 32 db b2 f6 91 6f 36 f1 35 bc ae 
88 1d 54 55 07 40 2d 6a fd 3d 5b 3b d8 38 5a 50 
b4 fa d6 f7 89 49 fd e6 21 64 ed 76 89 e5 f9 a4 

# Seed:
ee a9 0a 54 d0 16 da 7e 8f 08 76 a7 33 b0 f2 a0
6f 90 a7 3c 1a 3c d6 39 b6 cf a9 06 ce 08 ef 78
8b b6 fe a7 4f 22 eb 91 a6 ab 32 84 cd fc 1e 72
e6 3e 78 d1 8a 64 d6 7f 9d 1a 29 19 49 ed 2e 32
3e 91 dc 03 38 76 ee eb 09 ff a7 1f 59

# Encryption:
6b bd ae 20 f9 89 bf aa 5d 65 da dc d6 1a 86 b6
63 07 a0 60 2f b5 51 a7 38 06 12 2d b1 88 ec 1d
41 e8 4d aa 2c c7 d6 be 54 1e 12 28 88 09 a1 7e
08 5f 2c af a8 ae 13 67 0e 0f 33 65 a1 47 1c b3
15 7c 06 e0 f6 3b 82 00 f3 16 0c 16 3f de 7c 90
1e f3 26 e5 70 0c 9f 5e 07 fe 01 98 81 0f a8 0c
8c 5d c5 3a 50 bc e2 54 f7 d1 99 01 cf 6c bb 60
34 13 e4 1f 90 30 e7 39 c8 ba 96 49 97 84 74 99

# PKCS#1 v1.5 Encryption Example 6.9
# ----------------------------------

# Message:
73 dd f0 a1 4d 57 ba 65 b4 f6 93 ac 76 1e 20 2b 
1e 5b 85 7c 8b 34 04 e1 41 85 df a8 aa f0 49 89 

# Seed:
fd 43 3d 7b 01 76 b7 31 d7 80 dd 6c 58 51 b8 2f
17 6d 62 b8 0d 96 ae b6 31 d7 fb 8a c5 be 55 ac
21 7f 4e 08 bc 7e cf 81 f8 e3 f5 dd aa cd c6 c2
17 8d f7 81 88 23 97 53 3a 63 8e 62 f0 74 ac 48
8f 4c 12 aa 57 be 2e ce 5b b6 e9 09 6c d9 22 59
a4 5b 0b 03 2f 10 14 31 a2 8b 86 40 fb

# Encryption:
ae 97 ff 43 4e 9a 5e e4 87 76 1d b3 56 90 0b 06
37 37 46 5b e5 05 8d c0 3a 28 a3 22 e5 c0 e0 91
b7 99 bf 65 94 55 cf be 05 42 7a c4 d0 44 05 e5
6e e0 4e 06 3a 23 73 db cb 9b 4f a1 6e 43 07 94
6a 49 b3 56 34 52 0d 41 6f 65 c3 c3 22 ed c5 46
71 56 91 28 a2 a1 52 a7 6f e4 44 c4 30 aa 6f 03
c4 12 9c 6e 21 31 75 5d 76 4c ec 4a 14 86 a8 1b
a8 a3 16 8d 16 e7 4d ca 8e 77 bb fd 67 ea 37 2b

# PKCS#1 v1.5 Encryption Example 6.10
# ----------------------------------

# Message:
ef 3e 76 98 e7 d9 cc 86 3b 46 6b b2 88 55 6e 4a 
c2 52 82 e0 94 fb 5b 57 c1 76 17 bb 98 

# Seed:
db 90 a7 58 0d 8d 42 9e 22 bd 7e a5 c7 b4 cd 0c
65 ad 0e 2e 27 f5 33 41 bc 23 a8 b1 35 8a 76 a0
b5 e9 4f c5 b4 2a 9f 75 6c c6 3b 9d 62 3f 55 17
67 fb c6 f7 11 4e 40 ba 73 61 fd 32 d6 f8 d7 72
34 37 43 6d f7 24 b1 32 7a ae f7 b9 57 56 27 68
f8 fa dd 57 86 2b a0 b3 14 09 6a 3b 38 77 0e 31

# Encryption:
43 f8 cc ce a8 12 d3 38 5f 43 fa 83 7c b5 e6 fa
59 0f 1a ff b8 62 d2 ca cc c6 d8 e8 bb 5d 5c 0d
50 ff b9 f8 f8 09 89 00 c1 2e 77 2a 84 7b 37 81
57 78 2b 0a 90 40 f7 a6 16 c2 eb 05 8e 44 d4 a7
e2 0b 48 5a c2 9f 40 d6 8e 03 c3 9d dc 8d ae 7e
7e 09 e2 8f 9d d2 19 0f 3a 9f 35 74 b2 f6 34 00
15 44 36 3c 86 1f e2 7f 7a 39 23 66 92 fe 35 82
14 0b 21 72 ce 64 7e ed 41 76 85 c6 e1 db 85 6c

# PKCS#1 v1.5 Encryption Example 6.11
# ----------------------------------

# Message:
f8 69 18 47 4f 88 16 97 11 1b dd dc 1f 00 61 3e 
b2 c3 d9 c1 78 76 68 35 3e bb 02 b3 20 a3 26 

# Seed:
cb 7d 99 da 9b 11 ea 57 f6 40 55 43 44 7d d1 5f
b5 cc a0 a1 0f 3b 69 b4 d2 33 09 ef f2 75 0c 48
6c a6 55 32 5b 55 a3 27 ff e8 bd 6d ca 99 bc 8e
fb 5b c2 94 2e c0 39 e2 84 25 e4 a5 6a 07 d3 80
12 f1 0d 21 5a 22 d6 37 68 59 43 d3 64 72 53 01
dd 40 e2 17 22 8a b7 91 59 89 98 83 6b bf

# Encryption:
2a 40 87 80 ad 51 4e 56 71 fe 1d fc 36 7d 7a a4
65 fc 34 69 f1 c1 52 ae e1 81 45 e0 f5 f0 75 9f
44 69 b4 3d 55 12 3f 5d c9 ff c6 17 f2 3f a4 9b
78 96 02 19 66 0e c6 22 74 d6 c5 9b a3 14 60 bd
10 94 1e bb 5e 05 69 41 51 c5 7b 5b 95 88 ca f0
9f 45 50 20 a5 4e 97 7c 3c c0 27 da e3 1f 2c e4
42 17 02 3e 10 ad b6 f2 d8 aa 0f 80 84 fc 45 86
05 80 3f d4 4b 21 ab c2 7b db 8d 4c 56 17 84 cf

# PKCS#1 v1.5 Encryption Example 6.12
# ----------------------------------

# Message:
6a 40 bb 60 70 8c 5a 99 2e cf dc 7e e5 3f 54 08 
4d 19 af fd 4d 21 

# Seed:
c3 f4 fa ca 56 c1 ff 07 d1 ad 10 37 07 d1 b6 68
20 47 b4 d9 d6 24 6c 2e 5c 4b 0c e6 55 3d 55 31
3f 8f 38 ae e5 e4 d8 07 3a 55 d2 4a d7 96 c4 b7
61 26 c8 aa 61 c4 6a 4e e8 5f a9 05 7a 52 6c d0
aa 24 5e 58 28 f2 18 1b 4a 64 79 86 87 48 e7 47
9f 40 34 53 3c 0f 3e 1e b4 35 fa 47 d5 3b 58 e4
2c 96 17 bf 7e 77 7e

# Encryption:
c4 64 11 c7 11 6f e6 f4 6f 1c a0 f7 4c 60 81 a5
5d f4 1d 2c 0b 8f c5 d3 8f aa 34 0b 5e cf 71 83
f8 5f 88 c4 fb 28 ac 5f b1 f1 8a eb fc fc 10 bf
dd 3a 19 00 2c 6b 52 24 14 92 70 4b 6f e6 3d 61
a7 30 10 c1 49 ff 63 03 28 3e 99 78 cd 84 54 04
fa 06 b8 c6 98 aa ea 8f 86 13 61 d8 86 b2 c0 f0
1b 47 a1 a9 a3 dd 90 3f 8a 58 ae d6 6a fc 85 e9
b7 1e fc 3f 55 a1 21 66 74 16 a3 00 00 13 13 e8

# PKCS#1 v1.5 Encryption Example 6.13
# ----------------------------------

# Message:
32 52 fe 99 62 09 74 e0 77 d6 eb 55 75 

# Seed:
bf 06 9b 4b cf 15 44 8b c3 9e 45 f4 42 6a ad 0d
5d 82 b7 4e 93 ae 1c dd 71 f7 1b b9 be b9 b2 3d
59 73 2f 9c 8b c6 34 3d 13 02 31 de 18 f7 c8 9f
c2 f4 22 18 9c d9 27 e5 09 b1 31 97 aa 56 d8 1a
73 76 f8 33 3e 47 42 44 8c c8 92 de 40 44 97 2c
7f 67 dc be 85 44 a9 0e ec 59 e7 95 ae 59 64 08
39 2a f5 77 6d a0 d6 cb 29 c7 e0 a7 86 81 30 a7

# Encryption:
76 53 cb ff 58 68 92 cd d2 58 bf e6 ba eb d9 91
45 eb 1b 22 89 4e 1a 76 4d 02 b2 ba 99 59 52 a0
12 58 20 8d e1 a0 1d 8e 8c bb 5c da f0 d6 03 69
4f 88 25 5e 80 90 97 b7 0e 9d 79 e6 2b d5 c0 d8
36 dc c2 9d d1 9b 05 a1 60 26 90 42 05 b6 0c 45
03 d4 fb e9 93 38 55 e8 68 02 c7 54 28 d9 a6 34
73 03 16 76 32 d3 3c 5d 9e cc 8a e2 49 3b 58 c3
6b 2a 65 53 a7 b9 e2 b1 35 8a e2 8d ad 50 28 0d

# PKCS#1 v1.5 Encryption Example 6.14
# ----------------------------------

# Message:
dc 94 

# Seed:
05 e8 05 bf fb d1 b7 4d 1a 5a 83 8d 85 71 67 b4
c7 d4 0a 6c d3 e6 8f 31 be 46 93 b9 8c ec 6d 75
89 49 19 ab 18 57 2c 75 82 2e 75 e9 de d7 2d a0
67 97 83 a7 72 18 14 e1 99 16 0b 75 07 f6 7f 4e
de 58 7c 88 be 76 81 5c 50 b2 61 df 30 8b 31 43
23 73 0c 1e 07 3a a7 29 98 cc 2f 8e 0a 8a 5b d6
5b f9 34 ac a8 a6 48 59 e2 5f 02 c5 a2 71 39 41
c8 c8 e6 27 20 84 6d fb 51 c2 ff

# Encryption:
6a dd e2 44 f0 63 8c 5a b7 45 ff ba b5 ab e1 fe
80 0b 0e ee 15 35 50 c4 8c 36 f4 24 9f 9d 5d 36
b4 7b 28 cc da 71 a7 dd 83 2e 64 35 ad 0e b8 5e
f7 55 6b b8 4b f9 5a 59 cc c9 c2 07 51 ed 3e 1a
d0 de 10 29 94 a7 12 c5 14 01 46 92 a6 7f 1d 87
ca 2e 4f ef 29 ec 83 e0 1e 29 ad 0c 97 8a cc a4
d5 c5 02 74 5a 6c 50 0d f2 00 04 bc 9b f1 6f 5f
cc 69 bf 52 70 9e 31 cf 6a 2b 9f e6 24 d9 a3 64

# PKCS#1 v1.5 Encryption Example 6.15
# ----------------------------------

# Message:
4f ae 6c f3 7e 9d 5f 59 

# Seed:
e7 f9 ed 4a 0c 3a 64 66 69 03 9b d0 1f 24 9f 32
be a1 e6 57 6c 21 d4 51 89 d0 f8 91 cf 4a fd 62
32 08 3a 32 1e d2 d3 4d 07 09 84 f5 e9 45 78 39
b2 eb c1 e0 3a 2c 0d 8d d7 09 73 1e 95 b2 e6 94
1c a0 90 71 87 af fd cf 5a 87 f5 cb ea d2 f9 05
2c 38 d0 ee 54 e1 f1 28 ff c3 37 de 45 66 a5 92
5e 1e 94 7a ef 50 88 1c bd ae d9 9b 1b bc 1a f5
13 23 2c 0f f4

# Encryption:
46 e6 70 5e b1 25 2d eb f8 ec 67 2c 4f de af 69
88 26 84 59 f9 95 89 bb d3 05 41 33 d4 83 ab e1
e2 7c aa b0 a7 f0 22 1b 1b 67 a1 4e bf 45 12 6e
60 13 48 b0 43 44 06 da 3a 8c 76 d4 f1 e3 f1 a0
e0 6d 0d b8 23 da 51 17 b9 2a 40 b6 f3 9f 57 e4
83 a7 da 36 e9 9b 67 7b b3 f7 6e 6c 5d b0 3f 3c
e4 45 04 eb d4 5e 9f 14 fe 0f 61 3a 2e b7 95 47
ab 57 8c 58 6d 3b 65 4a 06 fe 1e f3 7a 22 10 66

# PKCS#1 v1.5 Encryption Example 6.16
# ----------------------------------

# Message:
cd fd fd aa bc a3 76 7e 70 bb c5 e9 ab f6 

# Seed:
ac 77 7f 67 29 9d e4 c1 c4 c5 74 f3 1f 67 4f 6f
aa bd e0 31 f8 ec 33 e8 26 76 eb 32 a9 3f 65 79
aa c3 e9 59 35 9f c5 73 ee 5c 0e 3e 07 76 53 ef
f9 d5 f8 db 4b 1e 7a ca db 05 99 71 53 1f 49 b0
7c 93 eb 9f de dc f0 90 3a 7d 50 b4 79 67 6f cd
e8 74 0a fb d7 d3 7c 3a 21 02 41 2b de 1d 3a 82
44 15 6a d8 08 9d 45 df dc 91 cd e6 c3 a1 59

# Encryption:
51 3c 76 1e b1 92 9a ff 79 77 a9 ff 0e 61 b7 a1
d5 11 c8 cb 25 39 24 33 24 25 56 9a 07 e2 29 cb
39 01 71 53 90 f7 de 37 bd 36 2c 96 fc 0b 0d 79
81 0c 1e 8b 15 e1 3b 00 32 73 47 78 cf 96 4e 6f
6d 17 fc 41 c7 86 76 72 b2 54 0f 56 9d 0a 46 0a
80 fa d5 6b 5f 05 4a b8 e4 9e 40 9e 9e 0b 86 13
cb 3d a2 0c 35 c3 e1 bb 99 b7 ec ab 7a 00 fc 1f
c2 37 0f 9c 80 85 67 df 89 be 2e dd bd b6 b1 10

# PKCS#1 v1.5 Encryption Example 6.17
# ----------------------------------

# Message:
b8 20 c3 2e 73 7d aa 23 4f 29 ba 90 64 7f c3 bf 
0a 8e 

# Seed:
f5 ca f8 fe b4 2d f7 b6 b3 89 f5 05 99 a4 ec 20
e6 19 ca 22 d2 bf c9 1b c7 f1 4a e9 f1 22 9e 07
c8 88 ed 57 aa 6a d3 c1 04 10 44 c5 1a fb 55 21
7b 0c aa 23 34 ad 79 f4 7a bb f9 b4 20 83 33 b7
2b 4b a4 bb 5f d0 37 98 1d d8 01 70 c8 03 60 c1
4d 5a f5 1e 5f 82 ab f5 1f d3 6f 02 18 67 a6 ec
09 7b c3 39 22 ac ea d9 e3 35 58

# Encryption:
3e 23 25 18 41 db 2e 20 7a 27 10 1f 13 29 19 1a
b7 fa 6a 70 1a 52 62 61 48 23 53 a1 a2 21 ef a9
d3 a9 d4 59 cd 2a 2d 86 68 90 8b 78 3b b0 9c 87
9a 21 7f 1e 40 0f 95 b2 17 b0 40 a5 3e 34 1c 17
b9 3d 3c 3d 65 46 0c 5c 7f 2b 4d 79 a3 4b 5a 96
11 7a a3 67 51 d9 ea f2 33 b0 3f 68 db a6 a4 57
1b 90 71 77 f8 28 33 6e 82 5a 92 26 1b 62 36 39
be c9 8d 3a 09 f8 72 c2 ec 59 1b 4c 38 3a 69 c7

# PKCS#1 v1.5 Encryption Example 6.18
# ----------------------------------

# Message:
60 6b bd 61 37 15 dd cd 92 c7 b6 df 04 b3 60 72 
f0 11 62 d0 08 76 63 12 ca 69 77 dd 3a 06 eb 95 
e1 ba c7 bc 1b c6 60 2c 9e e6 44 

# Seed:
51 3a 5c 95 68 e8 9b 9a 53 d5 af 71 6e 55 fb 34
0f 8a 39 23 70 e8 88 a8 0c ae da 50 2e 7f 9d fc
17 51 95 a5 0e 47 07 13 96 d6 ec 55 4e b7 2f 18
3b e1 8f 3b f4 ec 73 b3 05 92 fa ef 2f b5 14 de
be fc c6 5c 8d 23 c4 ad 25 94 56 c1 e8 0b 36 0a
d4 59

# Encryption:
47 21 a2 39 40 18 87 30 f5 de ac 85 00 2e 38 31
b1 49 ae 57 af c6 97 35 a0 45 ee 3f c3 53 64 72
fc 83 3c 27 c8 7e ab 6f c9 05 e3 67 96 1b 31 24
98 63 6b e6 c7 7a a8 0b 8d a2 f5 d9 48 aa a7 7b
5f ab 72 4b bf 64 ee 89 d2 81 ee d4 7b 21 2c 32
95 26 65 77 f4 fd 93 a2 22 39 de e5 40 c9 40 0f
e5 6d 7a ef 51 df 36 a8 9a e9 2f 92 6f 55 83 31
e4 1b fd fe fe 35 75 8b 93 ce 11 40 ac 9b 6a 54

# PKCS#1 v1.5 Encryption Example 6.19
# ----------------------------------

# Message:
1d 82 de f8 c5 92 87 50 08 a5 f8 9e 7e b6 4e 25 
2e dd e9 dd b8 81 d3 73 62 31 7f 6e 6e 6e 99 3c 
60 23 3b 0f 

# Seed:
87 5e 07 5e f1 b0 54 b5 84 1e 08 45 0d 78 eb 54
a5 5c 88 b6 d0 18 13 c1 07 a0 9b 74 54 3b 3f 9f
e1 b7 c9 02 11 c6 2e a7 33 81 20 d4 ae 0f 73 ba
82 c0 1d 28 f8 19 4b 3a 39 6b df 50 f9 41 3a 7f
b0 3b e2 25 fe 05 45 db 80 cd f0 f6 10 a9 5f d9
03 76 e0 39 e3 83 1f f9 9e

# Encryption:
8f b5 46 26 3c 1d 18 75 c7 43 f5 2f 02 67 b2 f2
fe 68 8d 25 42 02 10 49 f5 34 89 ce 7c 35 91 30
f8 e1 1e 3c 46 1c 5e 86 3e 4d c1 9f 07 a1 3a 4c
1a 88 22 4b 26 c5 c0 cb e2 02 02 45 d9 1e 1b 83
42 42 52 52 f5 3f c4 0e 4b 14 fa 02 bb d7 47 37
d4 02 2c c5 4f e3 8f 0d 27 d8 fe a5 0f 2f dd 84
65 d4 f3 2f 8a c0 3e e0 06 be 62 33 e4 d1 a3 c1
6e b1 44 b5 d8 be 72 9a d3 04 f8 23 61 e0 7c fb

# PKCS#1 v1.5 Encryption Example 6.20
# ----------------------------------

# Message:
8a 8a 3e ab dc a7 ca b0 cc 29 6a 29 1e a8 a9 3d 
a4 d2 d2 a1 96 ff 2f 8d 18 1f ee 1f 

# Seed:
2e 47 36 e4 29 6a b4 66 18 b1 ef 34 da 26 77 6e
92 ef 66 f7 cd 17 4e f9 47 69 72 42 23 a7 65 b4
eb ef 08 a8 8f ed be 27 05 60 e5 f6 74 37 cf 49
d5 7e a6 62 0a ca 59 89 8a fd 52 cd ef 30 a8 b0
d9 85 5e 5d fb e3 47 de 77 c4 c0 28 0b 0f 1f d4
4d 4f e6 89 06 be 8a 4f 12 c5 33 08 7b f5 bc ea
fb

# Encryption:
03 f3 e4 ba 03 48 31 a0 e3 0a 4a 33 44 ce ad 61
b2 8b 43 be 31 53 2c 2d 76 37 53 9b 90 13 83 7e
dc b1 f2 16 d3 2f df ae 73 32 36 74 a2 81 45 65
db f2 9e 71 2d 18 c4 37 39 64 df 60 c9 39 9f da
54 14 a0 eb 45 bd e8 66 1a 89 09 12 95 a1 ef 71
61 6a 3c d1 45 e9 b3 18 b6 51 af 17 5d 4e c3 50
1d 5e b7 63 e8 d3 5a 2b 72 74 6e 02 0b 4b a5 99
73 83 4b 21 50 02 6b 43 2b 17 9a 9a e8 17 2b 7b

# =============================================

# Example 7: A 1025-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
01 70 93 41 d2 ec 08 04 a7 40 34 e8 fa 72 86 42 
98 3d 16 50 d7 46 e4 49 c9 ee 40 79 aa 15 e5 1f 
1f c1 34 24 2e 52 4b 0d 3d 0d bf 5a 51 21 93 9b 
12 5f cc 86 3e 51 41 60 b6 34 e3 7a a6 98 94 77 
6c 7d 33 e1 e7 c6 19 52 1d e4 82 a0 ae a4 5c 3c 
6a bc 3f 33 e2 5d 86 ff a1 39 33 25 65 9b f2 d4 
09 8d f1 69 b4 07 21 87 66 0e 27 77 00 19 9b 7a 
3e 34 84 b3 84 5f 6f bf 31 98 65 7d f8 cb f3 a8 
1b 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
01 70 93 41 d2 ec 08 04 a7 40 34 e8 fa 72 86 42 
98 3d 16 50 d7 46 e4 49 c9 ee 40 79 aa 15 e5 1f 
1f c1 34 24 2e 52 4b 0d 3d 0d bf 5a 51 21 93 9b 
12 5f cc 86 3e 51 41 60 b6 34 e3 7a a6 98 94 77 
6c 7d 33 e1 e7 c6 19 52 1d e4 82 a0 ae a4 5c 3c 
6a bc 3f 33 e2 5d 86 ff a1 39 33 25 65 9b f2 d4 
09 8d f1 69 b4 07 21 87 66 0e 27 77 00 19 9b 7a 
3e 34 84 b3 84 5f 6f bf 31 98 65 7d f8 cb f3 a8 
1b 

# Public exponent: 
01 00 01 

# Exponent: 
6f eb f7 98 12 1e 99 33 24 97 2c 8c 28 cc c6 5a 
2e 6a a1 5f cd e2 32 da 03 e4 46 4d b4 da 5f aa 
27 e4 2a 7c 7a 76 d9 ed 49 48 6b 27 a9 d7 85 c6 
7d 9a c0 c5 19 ad 8d ad a6 bf d1 15 d7 cd 75 b9 
9c 4b 59 c7 69 83 a7 01 5b 0d a6 97 3c 69 fa 95 
08 10 ae 27 68 d9 75 88 90 05 62 e8 6a 6d f7 c7 
14 b8 44 b1 e0 46 68 6a 5a fc 66 7b 13 57 3a 55 
be 9b 5b 38 b9 9b d3 cf 54 e4 34 4a 2f 2d 0e 21 

# Prime 1: 
01 40 ef 5b 50 70 54 d5 a6 94 46 4e f4 7e 49 86 
8c b3 3c 97 e0 e3 ed d7 7b f4 5a 52 5e 60 8d 7e 
21 5f 91 33 48 a9 b8 27 c8 39 73 7a 42 b6 ad fb 
b2 bb 49 7e 14 78 92 2f 71 88 97 d9 fe ed 27 7a 
6b 

# Prime 2: 
01 26 00 4b be 2d f7 6b 6f 38 ae a1 64 b1 fd 0f 
97 61 2b 2d b9 6c ed e2 8a 3a 91 ce 0d 74 25 3b 
b3 f9 55 9b 89 f4 81 33 75 65 2a 50 70 06 ce 99 
95 a9 8f 0e 6e f3 bc 22 e5 51 9b 61 3b fb cd 55 
11 

# Prime exponent 1: 
98 14 d4 e0 b9 5a 7a 24 05 ff 8b 4a b0 cd 86 c5 
05 ea a0 ce 21 34 83 f8 69 42 a3 e5 b5 3b 57 99 
d8 ff a5 9d b9 4f 5b 0a b6 ae d2 30 eb 00 67 55 
bb 0a 88 84 8a de 3b 3d 41 cd 33 b0 6a 11 5b 21 

# Prime exponent 2: 
1c 4b 1c fe f1 67 85 34 4f ed 26 65 0d 68 00 2f 
1e 4f cc 77 b2 ce f5 e5 3d 9b b8 8b bc e8 b4 65 
2e 92 99 84 5e 3e 6c d8 58 63 09 18 ae df 05 12 
cd 1d 92 53 05 2b ce e3 e8 c5 9e 46 ea 85 a9 31 

# Coefficient: 
fd c5 10 c4 d9 79 07 30 b0 fd 47 d3 13 ab d4 08 
58 c7 df d5 8f 4b 37 91 5f 7a 4b 1d b3 d7 d8 ab 
d0 6d 3d a0 02 de 98 4a 76 09 cc 18 a9 44 86 46 
0a 61 74 6c 7e a6 31 17 a6 8a a2 82 59 74 4f 49 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 7.1
# ----------------------------------

# Message:
da 50 9d ce 45 e2 47 00 37 9b fe 5a a1 a8 1c 24 
70 6c 18 42 d9 b1 3e 7a 2e 0a 15 d3 a4 af 8e 6d 
08 61 2d ca a1 5d 46 0e ce 87 29 88 e3 e9 0f b2 
7e 5c a5 c1 0f a1 fa cd cb 0e 

# Seed:
80 8c 20 46 fb 50 5c 37 69 5c 8d c3 5c 38 f9 f9
90 5a b4 8a 2b 8a 14 6e 8e 8e da 33 85 ce d9 5a
31 3b 2d c6 eb 41 83 67 fe ef f7 9a 02 ad 74 64
6d f7 a5 d8 70 54 ac dd af 34 ea fd 5c 1d b5 8e
5d ec 04 81

# Encryption:
00 fc 3d 0a aa f2 6c df 25 a1 a8 df cb 71 70 0f
b6 5e 2a b5 55 1a e5 f4 19 b2 d2 f9 4c ef 01 73
02 b0 0a bd 9e 6c 6e fa e9 44 74 d1 8e 68 da 0a
7c 17 ef 2c 5f cc 89 07 1d 3b 07 12 1b 9c 01 e3
0f f0 53 66 3f 61 f8 9f db c4 9b dc f8 e6 71 66
94 43 91 9d 41 34 28 45 e3 e9 9e 46 a8 a3 b4 8e
23 98 a8 8e 5b 45 d9 9a 17 dd 1f 21 2e da bc bc
d3 00 a8 4d 39 8e f5 79 35 bd ae 95 9e 60 54 e7
3a

# PKCS#1 v1.5 Encryption Example 7.2
# ----------------------------------

# Message:
ce 0a 79 47 49 87 41 60 e5 d2 e4 ff 

# Seed:
f4 b2 c9 f3 11 c1 fd 41 d4 79 44 b5 0e 17 55 d4
ea c5 ee 65 08 7c 9f c6 d2 f0 75 b7 38 c6 49 26
ea f7 d4 31 6b cd b6 36 54 d5 42 0d a6 1e 02 45
f1 95 b9 e8 24 aa 0b 06 c8 87 99 b1 27 fe 9b 03
6d f3 61 75 c0 a6 bd 80 e4 e0 af 6e bc 2f 42 70
b0 4c e3 0b 9f a2 7f 04 35 86 0f cc f4 0b 3d c7
ea ef fd ba b9 e1 76 66 19 bb 01 6c 17 90 26 63
cb 24

# Encryption:
01 0c f7 4b eb c2 63 6d 2c 49 d9 c6 22 bd 76 cc
0b 1d 02 eb b5 2f df ae c0 1c 4a c1 e7 56 07 1a
8e f7 6e 12 2a 4c 62 c6 ba 32 65 fa 4c 90 56 26
d1 13 d5 9d b7 9a d6 5f 86 40 b3 c4 34 4d 73 40
c1 6e 38 de b1 89 e3 a1 b1 1e aa b0 b2 60 6a 7f
82 f5 94 6d 41 9a ce bb bb 3a 93 7d 41 e2 9b 33
ed d3 ad 15 f1 e7 77 0f cf c3 0a e0 6a a0 1b cd
03 d5 df b1 62 f8 7a 18 39 67 55 3c 25 02 cb 5f
fe

# PKCS#1 v1.5 Encryption Example 7.3
# ----------------------------------

# Message:
41 d6 

# Seed:
55 6e 59 b2 c1 89 58 75 08 e2 08 92 fa 56 02 d2
49 fb fb 71 a1 09 05 ed 5a f7 b7 9b e8 71 11 a0
c6 9a dd b1 9e ef 31 6d 7c 0b 21 81 79 90 dc 3e
d8 c7 6c b2 3e 83 0e 17 c0 f4 38 73 15 9f ab 7c
5c 4a 6c f2 19 cb 1d c5 c5 da 45 53 a3 e0 bf 18
3b 8e 11 2b 61 f6 92 a7 dd f0 04 bd bf dc c8 d6
65 9c 3d d8 0c b2 3a ab e8 c6 fd f2 67 5d 07 3b
66 23 12 e4 ac cd e9 1c 12 25 e6 d1

# Encryption:
00 2a 10 04 95 59 66 55 30 4b dd f9 09 7f 78 d9
dd b5 eb 42 9b 66 63 5a 58 a2 98 f1 92 8e ed 61
53 4f 80 b4 ea 05 ee 39 b0 2a 64 56 6f 6c 45 6e
3b 58 6e 7c b4 3a 88 94 0e e1 29 34 0f 57 8e 56
e7 2d 8b 27 83 36 10 06 d9 71 29 ab ef 02 cf 1c
19 12 e1 7f 0c d1 d7 1c 0b 32 8d 0b 48 a4 ac 7a
ec e3 c0 05 a6 19 0e ac 22 c1 99 10 41 15 9a 2a
d1 db 08 96 01 5c 4f f1 d7 b3 54 47 cf c1 0a 24
be

# PKCS#1 v1.5 Encryption Example 7.4
# ----------------------------------

# Message:
0b b4 ab c7 dc 6c a4 23 5c 29 ed 0a 2c b6 3f d1 
41 34 1e 2c 4c 90 1e 6d c9 5d a9 f0 01 0f a2 e0 
22 cc fe 8a df db 6e cf 4d 89 f5 79 a1 0a 51 70 
be 18 c4 6a 24 1c a7 ee d1 07 ca fe 4a 9d 

# Seed:
bb 80 be 78 02 dd 8b f5 fb 5b 1b 86 39 66 54 f4
73 9f 5d 43 51 f1 9c e3 1b 72 c2 10 ae a1 a2 ed
21 42 d8 d6 f7 ae 37 4f 06 13 3a 31 5c 62 64 da
65 ce f2 33 ed 3d cc 81 59 d7 6c 3a 17 f3 6d 0c

# Encryption:
00 6f 1b 6c 1f b3 7a e5 c8 c0 24 44 9a bc 38 40
e8 e5 09 71 43 ee d1 6b ea f6 7b 6c 7a 48 24 ac
38 b6 f8 c3 53 b6 45 c8 ce a4 fa b0 9c 02 fa 6c
32 5a 50 74 38 b9 64 5e c8 23 00 86 b3 15 e3 4e
7a 56 ad b0 ec d8 9f d0 7b 98 73 9c 24 db 6f e1
1f f2 e5 ad 38 31 8e c3 1d f2 ed 25 08 de bc a7
a6 7e 24 01 96 aa 9a cd 80 32 9f 4c 43 43 c1 0b
72 69 9f 9d 6b 5b c2 43 99 5b 09 c4 6d d3 d8 03
ce

# PKCS#1 v1.5 Encryption Example 7.5
# ----------------------------------

# Message:
ed 26 ec 20 2d 5e 69 74 0d a3 48 84 06 bb bd 

# Seed:
f2 fd 08 cc 0d b9 a5 5a bf eb d9 2e 2a 9d 75 87
2a b7 df fb 0b 4d a1 36 06 e1 52 75 9f 86 6f 22
eb 87 29 fb 8c 9a 5c 45 ba 2f 4a ca e4 31 59 a7
ad 3c f7 8e 81 fd 09 54 90 9b 1f 0e 70 89 ca 86
a4 58 8d 8c 87 a1 a6 1f 1a 48 39 82 36 de f9 b4
97 5e 25 49 57 3f 60 ad b5 e8 61 b7 c3 b2 df eb
81 0d 13 e2 97 a6 cd 3f 2a fe cb 0e 4f 14 7c

# Encryption:
00 93 3c 58 a5 0c 70 15 0d a0 82 3a 7c 1e 36 7d
36 e5 21 3f 66 a3 00 50 fd ed 72 d5 75 5b 5f 9c
24 05 0b 41 14 f5 35 09 98 8b 62 54 20 cf d0 0a
c1 c8 cd 84 48 9c a2 6b 74 3f b4 7d 1b 64 d0 a8
80 8e e3 21 27 c7 71 22 4a 0d d5 a5 64 e6 36 ad
d7 3b cf f7 b4 73 e9 a1 2b 7d 46 4d 7d d4 a7 52
04 86 61 a8 b0 74 b9 fa 15 06 fe f6 03 dd 96 d1
19 96 a7 d9 e7 48 79 f9 9b b2 d9 1c 37 aa b1 35
72

# PKCS#1 v1.5 Encryption Example 7.6
# ----------------------------------

# Message:
dc 28 5a 26 39 59 39 e7 99 72 04 c7 1a 93 2f 79 
5b 4d e4 01 e9 6f 34 e1 89 36 32 37 e9 cf da dc 
61 

# Seed:
d8 0b fc c2 91 ce 51 8c e0 bb 6a e9 34 df d8 56
58 b2 39 fb 45 39 cb e0 5d a1 26 a8 98 b7 f3 64
88 7c 0d df b1 ea ea 1d 5a 90 de 76 95 66 5c 55
82 2a 11 72 cb 5b e8 a1 12 d2 8c 86 02 c5 13 be
48 c2 92 f0 59 72 e6 71 1a 44 cb c7 28 17 15 09
4b 49 0f de 29 16 5a 6b 6b 7d 99 db ed

# Encryption:
01 57 e7 b5 4a 34 1b 8d f8 bd 9b 99 fc 2e 6c 58
d8 86 fc 79 e7 44 2a 9e 76 d0 d6 7e 48 58 c4 ab
f0 ed 25 c3 3d f6 2e 2a ad a3 99 df ea dc 7f f6
89 28 e6 b9 00 71 02 dd f8 09 f5 90 8e ef c1 0f
2a 73 71 06 09 23 1f 5d 45 e0 0a dd a1 34 b6 02
dd 0e ee 0f 67 22 49 4b 7e 4f 7b 40 57 72 c8 31
b6 37 29 be 0f 1c 4b 6d 2a 54 2c 15 65 c7 df 23
1d 9e 89 2e 58 6a 18 ec 54 37 73 76 db 77 f8 13
84

# PKCS#1 v1.5 Encryption Example 7.7
# ----------------------------------

# Message:
46 47 d8 41 c5 a0 b9 97 3a 91 45 4d d1 a0 5e c0 
e5 75 03 ed 99 67 2a c0 02 df ee 77 7f 7f a5 06 
fb 41 b2 ec 8b 8d 2c db 9b ef 01 33 bd 5e 3a 7d 
0c 43 44 b2 8f a8 db 

# Seed:
f5 27 a1 4e 2e 06 e7 3f 1c 24 5d 19 0d 02 cc ed
01 1e 46 84 87 ac fa 5e 0b ce 39 78 6b 46 a9 a8
c7 51 a8 b4 40 6c cf a1 fc 5b 7a b9 ba da 7b 4a
d4 52 46 7e 50 b7 fc 41 31 8e dc 73 dc 2d 84 a2
8a 08 1f a1 79 05 bc

# Encryption:
00 4e 03 ca a9 48 1d 7f 96 78 20 7c 17 b6 82 2a
5f 69 17 ed 01 eb 40 2e 7f 23 01 35 a1 23 cd 9b
6d e3 be c3 b9 bf 33 8a de f8 07 6f b7 65 2f c7
e3 a3 73 fc 16 22 f1 dc 67 9d 41 5c 32 00 02 6d
8a 8a 50 f8 db 6a 58 3f 66 69 29 d0 31 8e bf 91
d9 59 12 fc 06 10 2d 9b e8 75 25 a7 36 b5 af 21
e1 6d e3 ef aa 66 e9 cf 41 ca 73 48 23 23 dd e8
0e ec 30 85 81 a4 4c a3 aa bf 76 dd 48 1d e6 52
9a

# PKCS#1 v1.5 Encryption Example 7.8
# ----------------------------------

# Message:
ba 10 d4 7a 9f 62 42 81 

# Seed:
37 9f dc fa 5d 8c 61 37 c3 6b ad 14 3f d9 b1 f7
e8 94 fa 0e 9a c0 fb 9c ec 60 e8 6c 82 53 97 5d
8e 78 74 21 08 49 5e 59 4a d0 89 8f d0 4c 91 e4
01 e7 c8 80 89 a8 7b 4a 4a 82 e8 34 cd 37 77 a7
d3 f8 0e 64 86 81 2d 4a b0 d3 37 23 16 2b cb c7
e0 81 a9 9d 3f 9b 5c 3b a4 4b 19 bd f8 84 a4 62
6f d7 de f7 67 40 57 be a0 82 e0 0d b4 b7 61 ad
75 3a b5 98 5a 94

# Encryption:
00 c4 96 5e 29 63 d7 bc 5b 10 44 d8 fc 75 eb 33
82 ce dd 99 07 41 97 92 ef c8 8c 92 b1 d5 c3 90
fa b1 90 01 1c 51 8a c9 ce 45 c1 b8 e7 27 6b ff
c7 c7 e0 5c 25 37 08 91 37 a9 8d f6 e0 c6 92 02
0e d6 54 af 83 33 9b ab 11 92 71 77 f2 f5 23 22
6b 4f e6 4b 99 c1 72 9f 6c 92 29 06 bc 16 d3 1f
0c 94 dc 2e a4 13 81 0d 55 94 0c 97 af dd 48 29
fb f1 61 8a 8c 9d e8 9d c2 40 06 e7 e4 21 a5 8d
38

# PKCS#1 v1.5 Encryption Example 7.9
# ----------------------------------

# Message:
ef c7 48 9f ec 77 9e 05 2e 37 9c 1a d9 04 59 03 
b6 84 2a 9c a4 1b 48 bd dc e5 80 80 a5 ed ec 63 
f3 6e e1 15 60 fd 

# Seed:
c8 ec ba 62 72 14 c4 14 d7 c3 fd d3 16 c2 d8 2a
98 53 57 28 b9 a9 37 6f 69 a9 53 d7 cd 1c ea d7
10 53 db e0 14 0c 7f 02 bd 71 e0 13 7f ea 29 cd
4c 21 a5 8b de c6 66 40 99 0d 28 c1 0b 70 17 59
3c 1d 11 fe 9a be bd 71 24 e1 d9 85 63 1e 94 e9
e5 12 41 26 0f 9e f1 f1

# Encryption:
00 32 18 4e f2 88 fa 0a aa 0c 2c 1a 19 e7 c2 9f
81 c7 01 2f 45 29 eb 9e eb 53 68 1f 62 47 f8 d4
35 69 13 45 a5 14 8a 2c 87 7b 2b 18 92 6b ae 9d
e5 b3 17 ba c0 e9 02 c9 60 25 ee c2 f9 ea bd 0f
9e 88 86 ef 95 19 c8 24 9f eb 83 46 65 c1 01 0d
b7 62 4f 48 7e 16 1f 89 f6 ae 00 18 c1 f4 e0 ab
54 72 f7 f0 99 35 61 cd 59 85 f3 83 d0 49 dd 83
2b 82 c8 37 48 b2 28 1b fb 99 d9 d5 00 8d c8 07
de

# PKCS#1 v1.5 Encryption Example 7.10
# ----------------------------------

# Message:
5b 26 4f f8 8d ef d3 c2 99 99 3d 81 12 9a 6e 5d 
d2 b5 7b 

# Seed:
0b e7 ab 5b 29 70 48 43 c1 c0 d7 e4 ef 5e 93 f3
ba 71 7d b7 81 5a f5 72 e3 a9 ab 3f 99 b1 ac 9a
22 b9 2d 9b 43 da 2b 99 65 c7 97 70 57 17 3c 03
57 3f 32 48 0a 92 70 19 af ff 0e 0e 34 e4 09 5e
4a 4d 39 2d cd 1b d9 f2 7d 32 fd e7 15 9f 02 3c
83 08 9e 88 a7 1f 24 33 64 8e f8 c8 40 45 b9 c3
6d 8e 5f 6e ff 03 4b 91 b7 02 34

# Encryption:
00 4c 65 29 35 56 f2 fd 15 ab 90 eb 22 e0 75 33
b3 dc 17 33 4f 5e ed 27 a3 99 31 80 c5 6c 8e 3d
8f 51 ee b2 75 95 f8 78 d2 36 65 ba 3a b0 e7 28
a5 ae f7 23 4f 60 36 b0 23 f8 71 c2 d6 55 2a 18
ad 5a 25 be c5 5b c7 6b ee 63 83 46 12 81 d3 9a
30 f6 d6 60 92 e0 cf f6 92 32 68 fc 04 3c df 74
7e 8d 54 89 50 4e 7d b3 0a 7b d9 1a 2b fc 6c 1b
34 14 40 57 01 75 3d 5b 85 ff 73 52 01 2d 55 e9
23

# PKCS#1 v1.5 Encryption Example 7.11
# ----------------------------------

# Message:
72 e2 09 00 e5 bc c2 3c f8 79 ed 35 31 88 9a 1e 
d5 aa 3d 5c 79 2e 34 e3 b1 26 90 d9 ac 24 03 d0 
f6 f7 8f 59 23 

# Seed:
11 62 75 e9 be 1b d4 e6 f2 03 ff e4 f0 ab 9a 32
73 84 10 e9 23 e8 39 71 ee 9a 6b 99 2c 65 0a 03
1f 94 0f 6e f3 3d 61 50 df f8 b3 9d cf 22 50 d5
66 5c 04 27 3b 2b e2 de c9 97 12 bb c0 12 f7 5a
31 b3 0b a0 6f f9 eb e3 b8 9f 58 a6 8f 26 85 e3
38 ff 6c b8 d4 18 1a 8a 14

# Encryption:
00 21 4a 83 be 45 3a 75 bc da 94 33 a7 b9 51 43
34 39 98 30 72 07 1c c8 21 12 b7 77 42 be 0c 38
22 6c a3 c6 f3 8d 55 b9 ca 3f 08 c8 79 37 89 79
6e 6c de 67 37 6d 67 3f 5e c5 7d ac c3 74 c4 c3
17 34 44 dd 8a 63 76 de 8f 9d dc 31 a4 c0 60 d7
72 f0 2e b7 49 51 2d cd 04 02 31 17 5d 0b 69 42
a9 47 b2 c8 2f 7c 19 e2 ce 87 50 00 af 84 27 4c
be 2a db f2 fb fb 53 7c cb e2 fb d0 72 87 13 78
75

# PKCS#1 v1.5 Encryption Example 7.12
# ----------------------------------

# Message:
16 b1 10 e2 90 9b 11 b0 cf 36 b0 52 c6 f3 93 6a 
2c a1 3f c3 a5 

# Seed:
c2 bf 9e 4f b1 ed 70 ca 21 2d 15 ee 8e b3 cd 66
0e b6 5c 52 78 e0 3a 3b 10 eb 12 b2 53 34 a4 72
8b 94 99 20 12 2f b9 92 bf 2c e4 30 10 3d 74 ab
74 e6 d6 e7 62 b8 5d 6e f9 be b7 d7 25 98 f2 93
a4 35 62 e9 06 89 54 11 11 51 1d 31 4a 9c 46 02
23 76 ad 05 5d 54 ce d6 ac 1f 36 e9 8c 2b 25 a0
ac de 64 dc f6 52 2c 3d 22

# Encryption:
01 6a a9 5a 08 23 82 c1 0c 04 53 10 fe fe cc 8d
17 db f2 16 d8 ed c0 4b da cd 4f 52 4d e4 85 b7
fe 8a 26 f1 4c ab d2 97 e7 f0 3b 3c 85 08 6a 16
14 7d 5f 61 93 91 9b b9 5a 53 c1 46 c7 84 c0 0b
53 32 e0 18 f6 43 cf 95 87 24 cd 08 07 5e b6 4c
a5 68 0c 27 c7 05 d4 0d 88 b9 d7 f4 26 b7 36 e3
c5 f6 39 4e a6 83 b6 5c 23 73 a6 fc eb 14 f2 ea
85 1c a8 e0 00 e2 4d c8 f7 e7 c8 1b 1d 4e 72 0c
36

# PKCS#1 v1.5 Encryption Example 7.13
# ----------------------------------

# Message:
3c 86 0a 28 fa e8 da 2a c0 d9 a3 39 89 97 7f fa 
04 

# Seed:
7c 96 99 90 6c 9f 16 65 12 5c 0b 10 67 3f 3d ad
98 c9 1a 4f f0 fa aa 64 7d b5 54 fd 62 27 ef 50
90 9c 97 b7 06 09 2b e2 10 db 2c 24 ba 9e 8e 6a
87 f9 dd d9 f3 f4 91 29 10 29 ac 6e e4 6e 08 d0
d7 a5 3c 46 2d b4 f0 fd 1c c2 3e c8 f5 5e da 07
f4 ca 0d 3e 3c d3 76 22 85 5b 4d b0 8f 64 be 3e
26 c3 e9 78 75 17 12 94 ea df 86 fc d6

# Encryption:
01 0c 1e 04 a8 58 c6 15 ee 96 95 f6 4a b2 db 99
80 6d a4 82 d2 b4 60 29 3c 46 dc 7b 71 7a 59 76
a3 c7 e3 6d 8d 47 a8 4a 34 d6 3c df ca 2c 1e 38
45 25 73 ed 44 c3 a0 40 40 5e cf 3f bf 36 83 41
c4 a1 fc 90 83 a8 f5 52 93 67 b9 9c b8 9f c5 a0
8b 8f 34 75 a0 d5 5e 3e 42 cc cb eb 20 d0 4a 19
97 ee da 4e 3c c9 e9 92 d2 37 ec 7d 32 fe 25 84
5a b0 24 d5 88 28 05 ed 52 f1 0e d7 d2 5d 62 d0
af

# PKCS#1 v1.5 Encryption Example 7.14
# ----------------------------------

# Message:
4e df 4a d3 44 0f 17 b1 0d 26 af cd f4 e4 44 d2 
aa 61 a1 97 55 a6 21 07 98 3f 01 22 fb 2a 

# Seed:
e0 20 09 39 39 b4 18 9e 93 4c b6 2d 27 ca 5e 97
19 65 2c 13 7f c4 9e 72 1c 4a b9 e9 f3 98 b0 a6
12 de 8a 8a 89 99 af 0d bc 1c ea 0b 61 63 da 42
61 23 28 14 ed 92 eb 21 60 cf 4b 26 d0 55 1b 1b
dd 91 9d 49 47 93 78 6e 1b 86 f7 9d 64 16 61 2a
28 22 61 36 3d 6c 9b 7a 0d 92 ad 17 58 ea ad 51

# Encryption:
00 8c f9 b3 f3 e7 60 90 d1 01 17 4e cd 97 d1 0b
ff 6d e4 d4 64 40 00 3f c0 d4 28 f1 9b 85 58 f3
13 74 a5 fa 28 3d 03 d4 dd 43 f9 3a 4d 9f 14 ca
00 68 dc 2c f3 e2 54 37 b0 5a b1 d4 06 21 ee bd
84 16 f5 82 8a e7 c6 cf d2 97 b4 51 8f 79 94 2b
91 43 23 28 4e 29 76 a5 4d 3c d9 11 63 3a 30 7e
da f1 ed b6 74 98 46 6b 3e 98 91 6f 99 04 f4 a0
ea 9b 87 a9 83 44 f0 73 83 3e df 9b 2b 53 94 cf
d7

# PKCS#1 v1.5 Encryption Example 7.15
# ----------------------------------

# Message:
8a 6d 53 b0 ad a1 85 4e 23 13 69 1a ab 23 06 3d 
e1 31 bc 36 c7 64 

# Seed:
26 85 45 8a eb fb d6 07 4e be b0 fc 0c c4 92 1c
27 3e 8c 0a 88 15 51 50 2e 4c 29 9f 33 4d d5 67
f5 16 75 b0 ff 30 f2 c4 82 63 20 03 64 99 66 99
f9 b1 72 af fe c0 e7 9e 5c 52 3d 1e 77 9e c0 6d
ec d4 76 a5 74 30 78 1e 2d c8 1f 25 d6 0d 3a 73
97 65 79 a2 f0 1f 07 58 4c f8 e5 fb 3e bd 8d 5a
93 2d 57 aa 8a 18 0a aa

# Encryption:
01 3a 3d 32 ee c7 35 eb da 13 f8 be 76 02 a0 47
5c f3 cf 28 5b 42 86 ad 93 d6 12 c3 ad 91 72 48
80 9d 1c 4c 18 0b 36 70 15 cc 66 c4 d8 e7 86 73
84 16 8c f4 cb 71 9b d5 93 35 53 55 cd d7 d7 53
0c 80 c8 67 74 5f 96 61 c6 c3 3b cb 97 f2 cf 75
a4 c2 7c 3c bb db be 7e af 4f 82 34 f2 12 e3 05
82 56 d4 43 9a 9f 97 81 df 48 ef db 02 35 68 f9
4a e4 59 46 54 a0 f9 ba f6 ea 30 b7 d8 d9 48 0a
d3

# PKCS#1 v1.5 Encryption Example 7.16
# ----------------------------------

# Message:
20 f0 20 44 e6 3a c9 2e b9 fa f1 a0 ce 35 ba 72 
09 

# Seed:
2d e1 fc c1 17 f8 21 f1 de a7 94 b5 ee b3 13 f4
29 e0 0b 97 6b 53 41 9d 3d 03 ec aa 1b 50 76 88
77 e8 b7 fb d6 c3 63 04 7e 15 c2 55 79 3b 3c be
0f 58 84 f0 a5 11 25 4d 31 bf b2 37 41 02 3c 1f
88 1f e0 16 a1 2e ef 1b 8a f2 2b 93 68 20 7e 7b
16 39 f7 27 1d eb e3 c8 df 52 93 ee c3 a0 32 f1
ce 55 9c 0a 04 77 1b bf 88 98 94 7c a4

# Encryption:
01 70 06 e8 6f 6c 58 58 fe 5a de 0d cb fa 9c cd
11 c0 2d 4e 7d 0d e6 c1 58 ad f9 ee f1 01 07 e7
a4 c3 6b d3 d9 29 ea 6a 47 6e cf a0 b6 ec d0 51
49 b5 12 15 95 48 93 a1 ab 26 69 c0 42 da 83 e8
c8 18 b0 0a e7 34 de 5f 9e 0b 97 ba 1f e3 e9 c4
6a e9 81 6b 63 b1 5c 2d cc 61 cb 3b 8b 2c 23 dd
b9 9f cd 54 e9 55 60 91 8b 9a 0f a3 c4 b6 27 3d
1b 28 a2 13 e1 20 b4 f2 42 86 96 5e be e9 4f f8
96

# PKCS#1 v1.5 Encryption Example 7.17
# ----------------------------------

# Message:
d3 88 3b 

# Seed:
5c a3 bb f9 92 d7 ae 35 94 c6 05 c3 9c 3e 97 90
25 cc b0 a3 5c 6e f0 fa 57 4a 98 be 05 ef 7c 32
8a 19 a2 77 5b a0 6f 2d d1 e0 ff 6f 0f 1f 6a 3b
20 fb da 21 62 d0 92 4f f5 5b 70 eb fe 2b 16 d4
ff 6a ef 8d 47 eb e5 96 38 e5 81 0f fd b5 8d b0
5f 4d 9b 4a 3a 42 3f 96 7f e5 79 f8 73 78 36 9d
5c 5c 07 e5 e3 cb 5d dd f3 89 62 11 80 27 0a 21
e0 10 78 c8 9a fb ab 18 9e 87 f7

# Encryption:
01 6f 55 05 f7 4f f1 10 4d a1 f8 a5 2e 50 bf e2
9c 99 87 10 c5 7d e4 40 98 a9 57 9e 7a 33 13 b6
29 60 31 02 f0 8d 2d 91 1f 91 7a 9c 96 62 60 8c
97 a1 ea 37 17 34 f6 7c bf 70 03 d9 3c 4c 31 4c
3a 0e 77 f3 65 8f a4 d0 72 25 62 c4 e1 3e 85 a7
c8 d0 d9 d4 fb f7 12 58 84 ba 62 ad 28 59 b4 d9
61 36 f7 a2 45 54 69 ce b6 0b 63 ba 84 74 e6 16
0c 83 17 92 1a 07 b4 b6 43 6f 37 6c 5f 98 25 7b
17

# PKCS#1 v1.5 Encryption Example 7.18
# ----------------------------------

# Message:
06 a2 ae 82 a4 85 32 07 c9 f9 75 

# Seed:
e2 c3 36 33 d5 47 54 ad dd 24 c8 5c 32 d2 8a d8
70 f1 60 3d 44 44 60 a0 3a ac ad 7d cb 80 96 01
56 bb b2 59 ca d3 46 be 90 c0 d4 f3 fb 18 ac 6e
9d 5a 9a e2 a5 ab 98 a1 f8 4b 8e 70 c7 1d 0c cc
0a 1a 2a a3 99 70 d9 c8 3b 4b 0c 25 ae a4 3a 5a
5d ea df 9d c6 11 b9 6d 11 33 4e f9 43 09 03 89
a8 d3 c6 6c f3 18 77 aa 2c df f1 11 99 43 27 8a
dd ff 5e

# Encryption:
01 22 41 0e 76 5b 2c 9e 90 bd ac bc ff 1b ca 8a
e6 4b e9 9c f0 13 29 74 85 72 17 68 37 0c 36 f8
c0 d9 db 8d 79 37 62 54 b9 c6 91 52 72 0e 05 ca
ef d4 ce 7e ae 08 b3 df e3 ea a9 1c 46 02 ef f3
8e 4d 81 bc d3 78 7a 14 d6 22 db b7 9c e8 64 4c
4f d1 d2 e4 1f 7c 1c 97 27 96 11 74 0f c5 00 00
31 78 b3 7b bd 81 c5 a5 82 9b 5c 14 bf 45 9c 42
38 b0 3b ee f7 3e 49 8f 86 5f 6c a7 9a 9e d6 0f
65

# PKCS#1 v1.5 Encryption Example 7.19
# ----------------------------------

# Message:
ad 8b 11 a9 27 de 

# Seed:
5e ca cf f6 3a 79 38 e9 98 06 8a 2f 4b a6 bc c1
02 0e 1f 28 c7 34 e4 34 e8 86 3c 48 e6 df fa e2
8d 18 52 72 7f fa 7f 2e fa 3d e7 01 3b 81 2a 02
b2 17 1a 0f 94 0b 36 d9 28 db db 96 0a 6b 22 03
0c 89 37 89 cb fd ea 97 35 e9 ad 10 94 a6 84 61
c2 eb 6f 71 8b e4 74 d9 3a 51 93 0e 3c da 02 c2
1f 63 63 91 4e 7e da 54 84 03 7a 76 ad c5 33 12
eb 9d bb e4 5e 23 a1 4b

# Encryption:
01 1f a4 3f 5f 4f bb 98 30 11 81 4d 4a f5 45 52
1d f0 b5 9e 9b 6f fd 71 33 3b 8f 9b ba aa 0f cd
c1 42 1c e4 bf 31 bf 99 59 fd c6 b0 9b 4f 42 51
dd ee e8 21 0f c3 a5 2f e7 c7 1a 87 6e 6d de 1d
fb 59 a4 da b2 7d 34 fd ce 5b bf c6 ed 62 3e 89
96 7f b6 fe 73 16 20 15 28 2c 5f 45 01 38 f2 50
4a b6 1c 1f 12 d2 64 9d 81 5d 6e 81 38 43 8f 8a
80 46 c4 e8 40 cb 71 85 98 e1 e4 a9 fc 25 a9 45
64

# PKCS#1 v1.5 Encryption Example 7.20
# ----------------------------------

# Message:
be e2 94 36 c2 a0 de 16 f6 60 42 91 70 07 fb 51 
30 f3 c1 aa 7d f2 c7 c3 bd 99 fb c1 b4 13 af 4f 
96 a6 5b 0e 54 3e c6 a5 0b e8 3a 9f 

# Seed:
23 91 a6 84 25 dd 8f 0b 83 c9 b3 61 24 67 a7 79
cf 92 f5 ff 96 11 c0 14 93 bc be f8 65 e1 5b ba
5e 8f cf 74 be b9 ea 25 73 a9 fc e5 41 64 d0 6b
aa 8b 6d f3 67 cc 4c 6a 11 4a a0 34 6c 45 4a 2a
9e 60 53 59 18 d3 66 0c 66 48 4e d9 53 72 7a 9c
9a 25

# Encryption:
00 98 9d db d2 8d 60 95 ad a6 88 1e 28 34 1c e7
a0 a1 ca 6b f7 f3 1f 77 2f 91 04 93 af db a2 d6
35 9d 50 b9 83 3f 83 d0 ab 87 13 ab e8 e2 10 2a
27 ab 2a 60 1f b7 7b 9a 25 d6 a0 af f4 0c fd cf
9e 12 c4 28 43 eb ad 32 83 24 a7 19 f2 9e 8d 79
ea 9e 5d 0d 98 86 95 73 60 34 db 5f ea 73 dd 36
00 13 6f 57 a3 98 fe 35 2b 27 8c 60 cb 74 ec 98
ad 57 a3 e1 d8 c4 47 8c a6 17 9f 4d 04 26 f0 f4
20

# =============================================

# Example 8: A 1026-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
02 52 e9 5b b1 1b a1 e1 c7 c9 5b 68 53 f8 de 0b 
fe 2b 64 03 ac 1b de 81 9d 91 89 07 74 7b 01 99 
fa db 80 59 c2 5a af 1a c5 65 a7 49 29 d0 15 a2 
01 f8 97 a9 ba fe 75 41 68 95 5b 35 5b b0 09 ce 
16 14 93 12 28 3c 39 ce e2 20 d0 f0 85 8b 13 81 
2e 86 a7 d4 e5 38 8b 7e ae 5a ca 7c 88 6a 76 d3 
b1 e6 dd 67 92 68 a8 23 11 e2 82 03 18 cb 8f 0f 
7e 85 f0 e6 69 2e b0 de df 30 88 1c af 73 15 d2 
7d 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
02 52 e9 5b b1 1b a1 e1 c7 c9 5b 68 53 f8 de 0b 
fe 2b 64 03 ac 1b de 81 9d 91 89 07 74 7b 01 99 
fa db 80 59 c2 5a af 1a c5 65 a7 49 29 d0 15 a2 
01 f8 97 a9 ba fe 75 41 68 95 5b 35 5b b0 09 ce 
16 14 93 12 28 3c 39 ce e2 20 d0 f0 85 8b 13 81 
2e 86 a7 d4 e5 38 8b 7e ae 5a ca 7c 88 6a 76 d3 
b1 e6 dd 67 92 68 a8 23 11 e2 82 03 18 cb 8f 0f 
7e 85 f0 e6 69 2e b0 de df 30 88 1c af 73 15 d2 
7d 

# Public exponent: 
01 00 01 

# Exponent: 
01 04 c8 5f d5 d8 d7 93 2a 29 85 c4 cb 7e 9e 13 
a2 c4 f1 90 3c 8b 70 f3 df 97 12 fa ee 20 17 b9 
20 82 c5 16 53 c0 bd de 9d e6 6c 39 01 b7 c2 2b 
e4 f2 4c c5 6d ff aa 75 d4 3b 18 a2 e0 c0 df e3 
72 6b 19 8c aa 0c 96 65 26 3a 93 79 6a 27 d3 29 
84 46 5f 4b 4a ff ca 0d 92 f4 e5 1a 37 e4 1a b1 
55 07 66 d5 ca 7e 90 d4 de 90 9b bd 79 4e 8b c5 
2b 74 99 a7 3e 46 8a b4 42 13 cb 3a 3b 54 52 d2 
ff 

# Prime 1: 
01 93 3f 8f 85 82 b8 ff f3 e6 bd 30 42 81 97 f0 
ac ec 63 e6 91 f7 9b 91 fb d4 d4 c4 94 2b 8a e9 
13 a4 01 f0 e1 7b a7 66 d0 a8 ee ec 4c b0 e3 da 
17 b6 d9 1f 1a 74 24 88 09 20 1e 37 30 15 18 61 
8f 

# Prime 2: 
01 79 ad 28 3c ac 68 af 21 6a 06 86 f4 38 b1 e0 
e5 c3 6b 95 5f 74 e1 07 f3 9c 0d dd cc 99 0a d5 
73 dc 48 a9 73 23 5b 6d 82 54 36 18 f2 e9 55 10 
5d 8c 4a 5f 49 14 be e2 5d e3 c6 93 41 de 07 ed 
33 

# Prime exponent 1: 
01 6e f5 3d 6f 3a d9 8d 9a 6f d4 a4 71 31 2b 8a 
8a 62 88 3a cf 84 6b 5e fa b3 e0 77 8f 7a dc 6b 
64 43 30 bc fb 04 a2 ff 0e 4d 5d 37 4e 46 fe e2 
7e 08 18 27 94 d4 32 56 55 2f 94 2f 96 a2 eb d0 
03 

# Prime exponent 2: 
e6 c6 e6 25 5c fc 82 b7 1a 40 6e ca 60 b2 60 c8 
45 07 42 39 18 04 41 85 9c 3f e4 de 16 4e 46 6c 
0b 98 e2 e2 16 22 d1 e1 a7 6f 7f 03 3f 8c d8 f9 
30 b6 69 4f 10 bc 2b 3a 4f ea 91 24 d8 75 63 7d 

# Coefficient: 
35 42 45 2e 83 2c 16 ac f9 c3 f4 13 49 23 5e dd 
d2 75 c7 05 3c 55 3b f8 9e 1a 84 57 12 48 33 3a 
ad 52 0e 3a 34 46 c4 c5 e3 fd d9 ba ec d9 29 48 
0e b5 b8 fb ef 8e 03 ad e9 e3 9a 28 fd 8e 75 6c 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 8.1
# ----------------------------------

# Message:
f7 26 37 ae c2 8d 2b 6e bf 8f 73 d7 48 75 df 01 
cd 12 24 8f 00 20 60 8e 61 c7 3d 1a 01 f9 72 f7 
4d ee 

# Seed:
cc 20 77 a2 40 01 f3 e3 86 96 94 e4 fe 27 72 bf
93 8f 76 27 66 7b 62 d5 90 f9 ee 4f cf ff bb b4
7a fd 5f ed 6a 18 08 44 a9 12 1a 32 ed 7c bc 56
cd 28 70 a2 d6 96 b9 43 d6 fd e9 76 7c 1b 96 48
61 6c 32 ed 6c 40 0d 42 3d d4 ab 72 16 af ad a0
28 40 2e b2 a1 c3 eb cc 24 5a fc 7f

# Encryption:
00 72 57 5d 3b 11 c5 ff d6 ae 24 f3 53 ff 74 99
27 b4 ae 5d f4 63 f7 0c 5f 3e f5 49 6b 0d 14 5e
b2 b8 a5 3c 28 d5 3e fe 8b f9 f2 7b 2e f4 ce ea
48 31 05 77 c3 d2 b4 b9 49 a1 2e 3d f3 f5 67 76
82 88 18 45 2d 81 bd 45 af 15 8c 87 bb 57 74 5b
8a 10 fc 1a 92 a0 ea 55 c8 51 39 f8 42 f7 3f 1d
61 3b 9a 96 4a f8 ed 72 0d 0e 08 47 f7 ae 5b 30
5c 05 f1 2c bc 4c 9c 16 84 ac f9 02 97 0d 82 09
49

# PKCS#1 v1.5 Encryption Example 8.2
# ----------------------------------

# Message:
2a 8e 3e e7 ea c6 b2 2e c6 58 ad 44 d6 66 c8 cd 
3f 57 ec ea 29 9b 5c b7 bf 9a 37 3d fa 66 97 2f 
13 e5 f3 a3 00 e8 0e 6d bf 74 15 68 0d 0d 24 90 
1a d6 b1 40 b0 00 aa b8 53 51 f9 2f 

# Seed:
fb 19 20 9d 8a 29 af af bc 05 3a c1 f3 20 ba 60
fc 1f e1 04 aa 78 39 c8 4c 9b 3e aa 18 a8 f9 43
bb 21 9f 59 cc 16 7a 38 4b ac 7b d3 65 82 4e 10
36 31 b9 7b b9 a6 d1 18 f4 f0 a9 51 fa 47 8a 05
ea 09

# Encryption:
02 1b 54 64 65 71 a9 a9 3f 0b 0a 03 82 e5 40 a8
c3 97 4c b3 1c 87 eb f1 7b 3b a6 2a 1c 95 21 c5
0d 7f 90 70 2e 13 ae 0e 22 26 38 34 e7 64 60 35
15 94 45 a0 87 7d 9a 4f 5b 16 17 7f 7f e0 52 bb
d0 23 d0 81 89 4f 2d 97 ca c2 45 20 76 ca 11 91
a1 71 a4 8f a1 cb bb c3 f0 f6 f3 bc 1a 44 78 40
3a 5a 48 8f eb b3 a4 13 80 16 3d 94 2c 97 7b b8
ec d2 86 6c 5f 5d 91 9e d2 0c 0d b3 ee 31 ef 2f
51

# PKCS#1 v1.5 Encryption Example 8.3
# ----------------------------------

# Message:
d9 9b 4f 10 d9 f3 2e 12 ec fa e2 63 0b 22 ac 02 
6a f9 64 b9 c7 15 d2 07 

# Seed:
e0 af 8b 7e ab 36 a6 ee 31 6d 78 13 67 f0 9e a1
1e 31 fd c1 ef 2c f9 c9 7c 37 9e aa cf 68 72 a8
21 3c be 4c e2 e2 9c 77 8b 35 95 40 10 06 3f 17
76 ab 5b 17 2d a6 24 b4 06 a1 c5 8e 0b 57 4a 03
b1 b1 b2 cd 7d 3a 9e 50 35 a9 a9 73 05 8f 04 97
65 2d 2f 73 b1 dc 8f 48 7c 09 cf e7 1d 8f f8 f1
45 8c 79 0e e0 c5

# Encryption:
01 02 3b be 85 57 c2 63 0a 26 22 46 db 7a bc 54
03 43 88 70 38 f6 4c 64 1f d7 4e e8 74 ae 96 70
fb 28 62 d4 24 70 3d a2 0b e4 f4 8b 23 9c d0 60
43 81 9d 8f 61 51 44 e2 b1 f0 0c 8f 88 49 2e 62
f6 e0 73 16 f8 49 05 35 3b 0b 18 80 ed 77 da 2b
62 d3 a9 3b b7 0f f6 a5 00 79 38 b9 73 b4 cd 5a
bf ee 0c f1 3f 5d 4a b2 16 01 02 68 5c ac 80 81
83 4f 95 55 80 6b b3 22 d0 dc 5b 8a 2b f1 28 45
62

# PKCS#1 v1.5 Encryption Example 8.4
# ----------------------------------

# Message:
ca ec a8 e5 9b 81 0c f7 51 12 f7 ed 04 7a 46 69 
2c a0 b7 a8 6e 18 41 d7 19 

# Seed:
76 1f 68 64 dc eb c9 5a 77 9b c0 b1 6a 95 86 6c
33 dc 8d ca ce 61 cd 7b f9 01 72 d9 9a a4 57 db
6d 90 88 7d 84 c4 73 8d 25 cd f0 e8 95 69 ae 47
d3 07 3e c4 46 ee ed d5 2d 57 20 8b db 69 45 57
03 46 57 a7 03 78 49 37 ba 69 4d 42 51 21 38 53
2b 8a d1 d9 86 fe 47 31 8b 28 23 de 82 ce 27 6f
a6 f0 d3 c8 ef

# Encryption:
01 1c 0c 03 f7 b6 e1 c1 a8 41 17 40 a6 e5 b4 73
c2 8d 62 21 17 6c 9d 4f 68 02 4c a5 7c da 27 3a
f5 54 74 03 60 99 0a 1b 74 de 34 bc ea 10 3c 2d
0c 36 57 60 02 08 0b 30 bd 28 f0 76 fb 75 fc 9b
eb 9e 05 d1 98 9a 31 1d 12 c1 f2 8f d6 93 92 ad
4b e5 2c e3 89 de c1 1e ba 94 6b e0 59 91 da 7f
d8 87 a8 d8 76 8d e7 36 b9 05 bf 4b db e8 8b 85
df c3 b2 5a ea 30 fe 90 df 1d 22 d8 a8 d6 15 65
1c

# PKCS#1 v1.5 Encryption Example 8.5
# ----------------------------------

# Message:
10 12 12 85 6e 60 cd 27 2f b1 69 cf 62 cf 47 f1 
bc 50 ef 9f 1f cf d2 14 81 6c 80 7f 18 4a 90 3f 
16 f0 e8 09 ac f5 e0 

# Seed:
60 43 c8 df 6a 7f 4a b8 3e 31 97 e8 cd 02 25 dc
38 66 b5 d8 e6 99 3c 2c c5 b8 76 35 1e e3 3c 71
c1 a4 cc dc e4 5f 3e 9d c7 b7 e5 1b 52 ee be 0e
27 0e 71 62 07 ca 14 03 e1 3e 72 3c e7 3f 10 45
5e db de 85 b0 82 90 52 ed af e5 6e 9a 22 02 4d
40 68 37 1d 36 c9 1f

# Encryption:
00 05 c5 cf f8 9b 93 3d 8b 65 23 b3 59 06 bb 3a
71 1a 0f 7f 50 3f 92 14 74 66 59 85 03 91 27 30
3b 00 11 e7 a4 2b 41 c3 3b d2 0d b3 1b 15 60 c9
b5 22 20 89 cd cf 53 b8 2c 95 f8 c1 ad cd 8b 78
3f d4 b4 8a 45 40 20 66 8e 0b 62 52 0e 52 58 52
09 db 52 9f 38 70 86 49 d8 e0 64 89 0b 22 8f b3
c1 98 1b 2a ef 3a 54 65 ce b1 30 21 eb e0 8d 02
e3 3a a2 dc 3c 39 28 4e f7 a8 58 dc ce b2 8f fa
28

# PKCS#1 v1.5 Encryption Example 8.6
# ----------------------------------

# Message:
6f 03 b5 72 52 30 de 7f 99 63 69 8e db 79 75 ec 
e8 

# Seed:
a3 0e e5 ae ab 59 31 03 3e fb 70 af bc 2d 3d 11
a6 33 84 cb 8c b3 3a 8f ca e6 14 66 84 e6 3f 0c
c3 2e 89 a7 e4 ea 43 32 7b f0 35 66 95 43 19 28
86 de c3 fb 4e 2d 08 11 ef a9 ae c5 10 9e 31 b6
a0 56 e5 3e 31 7f 6a 90 4b 13 a7 35 6f 5e 7a e6
0b 97 21 5e d1 48 17 d2 8f 74 b1 64 0b 1f 2e 42
ef 0d 38 ca 35 a3 54 a0 af ef a8 03 a4

# Encryption:
00 10 bd 2b 35 6f 9e ab c2 2f 7e 68 f7 2f 61 75
f9 ab 9d ed a9 64 23 b7 4b 11 de 82 c6 07 a1 38
86 3e 17 96 6c 07 f5 6c de 9e d6 bc 42 2e d9 fe
aa 1f 65 36 7c cf 91 cd 4c 91 58 a7 49 57 1a 0e
9f 96 07 cb 48 cd 00 a4 48 de 03 64 91 06 ce 0c
24 06 aa 50 aa 12 17 17 8c db 06 80 1c 70 a8 9a
7a 1a 83 06 8e 68 db 95 d2 4c a3 db 33 a7 e5 e4
3a 68 15 22 74 bb bf 40 06 d9 fb 69 f0 51 4c c9
e2

# PKCS#1 v1.5 Encryption Example 8.7
# ----------------------------------

# Message:
87 99 13 04 54 61 bc 0e ac 

# Seed:
c3 c6 a2 4a c3 40 a4 a3 ff 3b 2c 30 2b 56 eb 83
91 bb d9 5f af b6 64 78 38 44 38 ab d8 b4 5d 13
2b 26 99 09 b1 87 84 0a 68 44 ad 39 9f a8 13 72
05 e0 2c ca e7 77 5c e6 66 21 20 e1 54 b7 bf d3
0e 8e a4 d3 4a 7d b6 a2 34 88 a5 da ea 38 08 5b
ed 56 78 0c ff 87 27 12 5f 1a d5 f9 d5 a0 bc 3b
e5 80 bc 4f a0 68 26 7b 27 de 38 3c 55 c7 fc 17
64 c8 6a c2 1a

# Encryption:
01 d2 1e ce 33 f4 40 c7 2a 11 1d 62 ef 48 6c 77
c6 c8 90 cd 81 a4 eb 05 32 ad 2c 0e c7 68 dd 1d
e5 b2 11 2f df 04 e2 87 a9 5b 95 0b 1f ca 5a e6
93 cf 0e 8a cc 93 6f 37 47 14 de 74 95 90 8c 29
15 a7 07 21 3a c3 db da ba d8 1b 4b a1 cb 50 b9
5f 93 14 53 1f ec 83 3f 08 21 c0 4b 57 40 d7 3b
8c f1 f9 e3 d7 ab c9 74 49 26 13 8f b0 15 c0 f0
56 cc 4b a2 f3 16 37 34 db 44 3e bd 68 85 8c 4f
a6

# PKCS#1 v1.5 Encryption Example 8.8
# ----------------------------------

# Message:
5b 

# Seed:
42 b9 99 fb 16 3a 6f fa 67 a9 bc 4e 6b a1 2f 81
b1 d5 6e 54 bf 08 66 39 7f d3 14 eb 0c f9 7f 13
b7 80 4f 76 8f ba 54 06 21 f0 5c d3 72 64 d8 e8
f5 82 28 fb 6a b3 0e f5 4e 30 fa 8c fc b5 a8 7e
14 8f ba bf 85 88 6a b1 f0 d5 25 49 2f 56 c6 47
f7 22 9b 2b f3 94 a0 ce ab bd 37 c9 3e 6d a8 09
7e 82 72 7b 3d 53 e8 c2 ad 1f 77 13 e3 4e d1 3b
a5 9e 0a 91 4d b0 6f aa e4 9c b3 79 96

# Encryption:
00 90 e5 35 59 47 90 2b c3 fb 58 0a 84 77 09 03
a3 95 5b 3d d7 19 1c 92 8f 74 07 ba 74 72 65 dc
8c bc 1d 22 f9 37 72 72 b1 a8 c3 5c 23 8f 04 a1
19 37 d1 b4 35 4f 64 37 95 e9 86 de e2 e4 0a 40
67 41 b0 21 fd f0 5c 4b a1 15 62 e8 3e 9f 28 59
2e 0a 79 14 fe 88 b2 c0 fe 7a 5e ea f5 00 f7 e9
8a 5b a9 54 f7 50 77 bc 8f 65 9f 21 bb 22 03 31
4b e0 d6 d2 1e 63 20 c0 5e 6e 2d 55 79 79 22 6b
80

# PKCS#1 v1.5 Encryption Example 8.9
# ----------------------------------

# Message:
f6 35 29 90 13 24 a2 0f e5 e9 25 8a da 2f 95 37 
b0 1f 58 39 b4 45 97 e3 29 3a 12 25 ca 3a 2a df 
68 4a 72 a7 93 c6 9c 56 af 2d 34 98 d3 2a 09 2e 
91 4b 

# Seed:
7b fa 85 97 a4 34 cd ad fe 15 63 14 44 95 13 d7
6c 10 5d f1 bf c4 8c 4d 07 6a bf c0 5b 5d da 72
e0 dd 15 f9 fe 82 a9 95 5b d5 6d 33 43 e7 c6 f2
5a 60 74 12 07 e7 3a 2d 10 bb 95 d1 d7 29 a2 27
93 e6 c4 55 e9 16 23 5a 81 16 94 db

# Encryption:
00 ad f4 78 74 67 ea c2 ea 61 fe 7e f8 2f d8 7c
2d a5 89 9f 30 30 2b bc 11 27 86 d2 fb 11 c1 42
f3 f1 d8 cf 37 16 0d 2e 4a 43 98 3f fb d3 93 a4
1b 59 9e e6 a2 7e 24 64 25 50 2d 46 90 20 2f e5
f8 ee 1b c6 c1 d5 d1 6b e2 3b 97 3a ed f7 f9 11
1b d8 b1 42 84 42 65 fd 93 57 7a 43 c3 ac c6 e2
af 20 89 d9 d2 f3 f3 1a 5c 24 7a 7b 68 31 5b ae
25 d5 ae 81 40 a5 1f fc 00 97 10 7e c1 62 0a b3
b5

# PKCS#1 v1.5 Encryption Example 8.10
# ----------------------------------

# Message:
c6 d1 4b 04 71 45 f3 17 78 1d d7 38 2d c0 a9 72 
57 d5 54 bb 53 53 9e e9 a2 92 e7 da 5c b6 42 6f 

# Seed:
01 ff 38 d5 de d6 c4 3d c1 dc 5c 27 a7 e4 81 3f
44 8f 45 c9 6e df 4b d9 3e 96 fa da 9b c8 ec 5b
43 4f 06 19 a3 8e 04 35 6e 06 27 85 51 40 7b 7f
37 e4 2d 91 45 62 0a 81 98 18 50 a4 9e 28 51 17
2d 23 0b 37 82 41 10 f8 ff db 84 77 94 63 9d 26
50 cb ed 36 26 01 05 f1 f1 29 6e 52 a7 d4

# Encryption:
00 5e dc 93 97 89 cf bf aa ff 28 2b af 97 01 d6
1f 9d be e6 f2 d2 06 cf ab 77 57 61 f4 2c b2 74
ec ba 31 c7 cd 2f e6 03 1a ba 0b 84 d4 62 7d 30
37 e3 1c e7 e1 56 23 ba 7c b7 69 02 51 c0 63 27
a4 31 37 12 98 df 29 21 95 b6 45 11 62 d2 da 92
a7 07 8e 2d 07 c9 f5 6a 07 06 8a 9a 3e 17 3e 4a
ae 25 a5 d1 c6 8e 68 20 8c b5 25 3a 0a 53 aa 6e
2e f6 c2 95 d1 51 65 69 b8 62 cb 92 ca 82 3c cb
ab

# PKCS#1 v1.5 Encryption Example 8.11
# ----------------------------------

# Message:
c1 16 53 e8 10 b5 3e 65 11 f1 33 23 fe 52 26 a1 
70 c2 1f 6d aa 44 29 d9 68 ef da 05 29 d7 b6 e1 
0d ce d8 0c 6b 63 01 de d2 2f 52 91 1c 0f 7f f4 
53 5b d5 e2 0f f5 35 88 cd 3d e6 64 8a c0 2d 

# Seed:
85 fa 7c 6c e9 6d 0a 8a 1f ba 75 04 71 7c cb e1
37 13 80 93 95 6e ff 06 3f c2 ef d4 a4 6d 7d c7
4e 90 f1 da 9e 43 db a9 12 9f 14 ec 55 9a 4d 2d
6c 5a 19 cb f3 a6 8c 62 d0 98 34 52 a9 ee 0c

# Encryption:
01 b0 8d 49 83 13 a7 d7 4a 05 53 14 eb 43 15 ba
02 87 60 da d4 11 14 d5 94 2d 63 bf 8d 27 be 3f
49 cc d9 4a cf 9d 3a a2 2d 09 b9 9b f9 74 09 bc
f3 32 13 c0 99 67 07 86 82 03 a9 ab 27 70 8d 3f
ff 69 b8 9d 02 e3 6e 01 21 a1 19 b8 d4 d9 bf d4
fe 8b 16 8f d7 c1 2a 24 3f 7a 00 0b 39 bf 8d 56
48 17 24 20 80 23 bb 60 7b 30 50 5d d1 74 2f 87
9f 16 c1 0c e4 90 d3 4a 68 0d 27 ac 39 60 7d a2
4e

# PKCS#1 v1.5 Encryption Example 8.12
# ----------------------------------

# Message:
09 5b 77 c9 4d c1 b1 87 88 e4 00 e6 91 6a 4b 4c 
fd 73 ac e0 df 9a 3a c1 31 bd f9 ad 0a 12 db 76 
6d eb 22 53 59 d9 01 cd 56 ed 88 cd a3 d3 28 56 
54 02 

# Seed:
a3 4a 68 05 55 71 09 c2 61 dd df 5f 85 d3 71 ae
65 20 f4 5a df 46 96 01 b5 c3 59 fe c7 44 cb 2a
ae 80 98 3c 73 2d b6 c5 45 df 55 e0 20 8a cf bb
f1 c2 c5 e7 99 88 f3 4e cd 6e 5b b4 b5 25 c1 b8
bc b0 70 d0 d8 42 48 b1 f8 e7 48 0e

# Encryption:
00 39 56 36 a8 26 67 dc f0 0d 5d bd d8 54 12 06
94 8d 49 36 89 17 ec 0e 00 fd 7a c5 ca 8b f4 4e
c5 83 78 38 6e 59 4b c0 65 a9 a6 3c f2 a3 55 a6
08 b6 f0 ba cb a5 60 08 bb a4 72 2a 7c 47 05 45
a2 0f 38 78 53 d4 60 31 3b 2e 86 4e 17 b2 33 e5
96 35 41 32 af 17 3b 4d 04 49 26 47 79 02 62 d3
a4 3f 84 27 37 88 37 46 6b 06 73 a8 15 27 e6 be
10 45 80 32 6f ec 84 ba 37 1e a6 10 91 fa 40 33
a4

# PKCS#1 v1.5 Encryption Example 8.13
# ----------------------------------

# Message:
38 

# Seed:
54 7c 91 75 19 05 d5 a2 84 ac 3f e4 32 cb e0 30
55 b2 85 06 58 96 11 0e a3 6d 05 a1 40 08 3c e3
95 5f a8 28 41 ea f6 db 4a 50 d1 2c 07 4f 45 a6
88 b5 57 6d 6e 61 68 07 54 0a c1 17 58 5c 5b c3
be 52 60 72 7c df 12 3c 77 4d b4 0c ff 29 70 88
62 48 53 e4 69 51 36 b9 31 16 15 17 a7 b9 b5 dd
cd 9d 32 de 3d fe 3e e2 ea 68 8c f7 bf 88 2c cf
7b 9c 48 d5 e1 9e ff a6 50 4a 42 62 0b

# Encryption:
00 89 e3 9d cf df 91 69 3d ef e3 9d 12 bb 25 f8
0a 76 8d 44 1b 48 1d 6a 75 48 69 50 42 48 0c d4
a0 ba 97 83 d5 c5 bd 38 89 6d ce 06 ac b1 77 a4
ac 59 68 e6 55 a7 aa f5 0d 69 4a 64 97 13 b7 a4
bd d1 4c 81 9f 83 b2 04 7d e2 19 5f 73 03 66 54
53 a8 a1 11 5e 5b 48 ac 0e 9a 65 ed db 31 89 51
7b 04 6f cb c2 d1 43 81 77 6a 77 fb 46 8e 11 29
3c 78 c8 37 4c 8f 46 60 35 1a c2 b2 c7 84 5a 25
fe

# PKCS#1 v1.5 Encryption Example 8.14
# ----------------------------------

# Message:
80 64 6b 3c 4d f0 eb 79 1b da 0c cc 4d 97 d8 1e 
a8 f6 f7 4e b2 45 e2 c7 c3 48 fd 7f b9 90 16 a9 
d4 0a 60 5a c7 42 b2 7a d2 48 

# Seed:
11 b8 e7 98 d7 a1 42 d0 82 e0 59 8a 8c b4 f8 c2
aa 87 5d 5b 65 17 8c 4e e6 7a 5c b8 41 d1 cd a3
04 31 d0 20 df 28 80 d7 93 58 15 d5 9d 91 b9 99
3e 53 ac 34 1c 97 28 61 ea a2 66 97 cf 10 ca 8b
27 94 b4 53 03 be 03 48 9e be 07 4d 8f 23 98 54
c3 a6 06 fb

# Encryption:
01 f1 e5 a3 db d8 24 75 2d 2f ba 3c 32 42 e9 d9
96 e6 27 43 0d 49 3e 1b 44 6a 2d bc d8 6a 48 09
3e 37 a2 e1 28 b2 8c 49 d2 d1 72 bf 5a 97 7c 36
9b aa 9f fb 83 9b d2 fd f0 0b d3 0f f5 22 8b 57
6b 94 e6 d8 ec f9 44 24 7a da d0 19 f2 1d 06 fb
e4 18 d3 a6 d5 4c df 11 3e 8d 14 f6 ea 06 d8 db
79 64 93 bc b1 89 6f c4 f3 f3 03 86 c5 c8 ba b7
03 7c 87 9a fe a4 7c 8f b7 a3 c5 b5 0b 29 18 66
b9

# PKCS#1 v1.5 Encryption Example 8.15
# ----------------------------------

# Message:
6b 63 1c 7c 35 ea 75 a1 b0 

# Seed:
3e 42 40 c3 e0 9e a7 83 55 35 8c da 61 29 53 47
30 f4 8a 9c 9a 1a 52 13 28 4b fd 07 71 21 6a d4
ca 23 3d 99 3e e6 35 7f 4b 1b 12 a6 ba a1 77 43
13 4a 85 7f d7 69 a8 bc 78 d6 1f b1 4f ea 05 22
1d cf 5a eb 1a ff d4 0d 8b c6 94 5c 30 10 45 b6
86 c6 11 fa 43 7e 30 ca 0f ab 5a 4c de f5 20 5b
af 99 26 c6 07 be 96 37 b1 50 7f 50 83 e9 40 cc
fa 2f b3 86 19

# Encryption:
00 47 cb 9a 91 98 d9 83 b3 22 24 cf 27 fc 72 99
bb d4 ae 07 78 a8 3f c5 9c 47 45 fa 99 e9 17 bb
74 f8 bd 4e f1 3f 14 0c a9 b7 2e 2a a1 74 ce ea
26 48 94 e2 15 f4 1c 36 d4 e6 f3 46 f6 9b 4f 85
50 5c 54 cd 46 25 9c 71 2e 30 c4 92 94 ba db 1c
47 16 85 1f 2b 75 e3 96 12 cd 54 66 ba 56 e3 f3
15 99 c2 dc e2 3d 04 c9 3a 64 40 22 27 df 40 b5
14 c7 4d 0a a3 6e 1e 86 58 e2 92 77 b3 05 af 35
15

# PKCS#1 v1.5 Encryption Example 8.16
# ----------------------------------

# Message:
40 08 55 da 54 a6 d1 fe 5f b5 8a 73 d2 a5 e5 58 
38 70 fb d5 25 d2 f5 72 ad b5 96 30 06 d0 a1 33 
9b ea 88 9d 6d 46 a4 37 62 f5 13 bb 7c c0 36 22 
bf 85 92 44 d6 

# Seed:
49 6e 50 ba b5 ef 18 f2 2c 3f 62 b9 21 14 8d 36
c1 01 ad 0a 9a 20 38 67 58 08 ce 8b 62 f8 a6 a0
ba 8d 91 05 f9 2e d8 a0 2b 31 2f 32 4f 3f d3 91
92 bd 41 53 78 4f b5 59 05 c3 b6 69 30 7b ad a8
27 af a1 b5 cb 3d c1 1d c4

# Encryption:
00 d3 a8 5d ec 97 d3 44 88 ee 33 c6 58 ba 18 8e
64 cb 57 83 7d 2e dd bc ba 8e e5 2f 13 f1 e4 fe
9b ec 2f 92 e7 21 09 87 e1 c3 fe 34 5d 40 19 77
0b 07 74 94 51 b0 4d 67 30 d5 3a 91 01 5b 25 7e
81 0d ae 0a 0c 11 6a 4f 22 45 14 ed bd 39 b2 c6
5e 15 2d 3b 97 89 dd a4 f0 d4 5b f9 83 2d 27 9d
34 31 06 2b 4d e1 b0 67 7e f5 9c 6c 33 27 68 ec
da 3a a6 bc d1 0f 70 bd 06 03 0a 76 65 ed 3f 20
79

# PKCS#1 v1.5 Encryption Example 8.17
# ----------------------------------

# Message:
b8 7e db 45 5a 7e 85 53 9f 92 8e da e9 09 fb f8 
f7 a1 99 c0 3a 94 a9 a4 5a ca b2 5e fa ee cc 26 
29 74 ca e0 bb 72 24 3a 99 c6 47 20 79 f1 97 38 

# Seed:
03 5e 31 59 4a 56 65 60 5b 84 fe c9 3a df 92 58
50 85 1d f7 b3 94 e9 bb ec be 4d 72 c9 2f 27 03
b6 d6 08 96 e0 05 4c 59 ca fa 5e 0d 28 6c 81 2b
23 e5 37 88 5e 4c 34 38 a7 72 a1 61 0a e9 fa e9
18 e3 4d 49 92 c7 f2 63 f3 e8 e2 f9 80 b8

# Encryption:
02 1c 8c 95 9a ec 47 29 68 87 68 93 0d 67 e2 02
99 dd 47 90 2d b0 79 f2 39 b8 c2 88 b0 a7 04 47
c7 19 6b 84 91 2e aa 5b c3 af f6 ba 63 0c 2e aa
3f cb b2 4b e4 63 83 65 31 25 0b d4 c4 f2 a1 da
68 c8 bf 4f 40 cf 5c 98 b6 85 eb ec a4 d0 3e 76
b3 34 af 0b 1b 34 48 8b 58 2e 29 35 25 35 72 f7
fc fa a8 35 44 e7 fd 52 ef 45 8a cc cb 19 30 1a
4d dd 50 51 6f 16 a5 fb 78 f3 95 99 00 db cc a1
f2

# PKCS#1 v1.5 Encryption Example 8.18
# ----------------------------------

# Message:
30 2a c8 0e 30 c6 55 3e 93 59 df 85 b1 e2 4a 16 
c8 62 a2 0f f4 fd 9d 5f 14 6b e2 81 dc 30 66 ae 
b8 fa 00 b5 2a 99 

# Seed:
32 6c 99 35 8c 4e 5f a4 d2 c0 43 f0 2f 92 70 5f
79 1c f7 12 bc 01 04 5b 4c f3 49 b4 2a e5 ac fa
c3 78 38 11 bb f0 7f 34 bd 6c 85 19 55 cb a4 a8
b2 a7 d1 39 a7 8b c4 d8 8e 3a 88 e6 c4 cf 49 4e
6a 4a 52 35 41 45 e1 18 83 cb 5c 78 b5 72 f3 0a
51 ed 23 f2 56 4b 69 60

# Encryption:
02 03 29 87 cc f7 5a 63 86 24 17 80 64 db a4 b7
2b e5 39 e0 7e cd f5 30 03 56 cc 43 d7 29 fe 34
fb 35 bb bf 5a 32 17 2c e3 9e a1 fb 47 d3 8e 49
7a 03 b6 01 ee af da af 99 a3 4f d7 21 18 4a f2
e1 8e 83 d0 50 a4 10 8b 15 e5 e3 a2 7e 0e 63 6d
f8 d9 4f 98 16 58 eb de cd ab 0d ef cc 0d af 3a
c9 a2 7b 3f 22 a0 54 1d 35 00 27 52 e9 cc 4f b2
58 2f ae 25 11 a1 32 ec 5c 22 8a 88 72 bd ba d6
87

# PKCS#1 v1.5 Encryption Example 8.19
# ----------------------------------

# Message:
00 b3 82 01 74 6d ca f4 03 48 af 57 ba d7 25 70 
ca f5 a2 85 5f ec 6c 42 ee 22 dc fe 64 c9 97 ec 
62 a5 c9 75 62 4b bd 1f 8e c9 16 

# Seed:
93 91 05 c0 04 c0 1a a9 f4 74 46 d3 cc f5 30 e9
b2 17 4c 50 ae bc a0 a9 5c b7 a4 d8 39 54 ef b8
03 9e 59 1c 19 71 d7 76 ce c7 61 12 92 06 db 7a
d3 dd 87 16 68 25 5a 55 50 ac 4e 94 8b e0 5c 16
22 20 dc ef ec 13 ff 1f d8 a5 a7 f7 8b f7 15 b4
9d 03 ba

# Encryption:
01 2b a8 7d c0 3a 1a 38 bf ed f1 21 ed 87 22 82
7b d9 7d d6 3f dd fd d5 90 50 c5 3c 5e 7b 49 fb
7a e6 03 84 87 82 0e fd 5f b9 71 43 81 ce 8d ae
b5 6d 13 50 bf c6 7d f0 91 be a2 ac df fd e9 29
2a b1 29 1c c9 75 1d 9e 39 a8 26 f0 54 ad 1f 33
a2 b7 94 da 50 ef 80 67 29 17 e0 b3 81 40 45 d2
3d f4 5d ba 3f dc 6f 09 ab 0a 01 8b 06 0a a9 fa
fc 3a 5d 19 d5 eb 64 31 0b d6 02 a9 91 1d cb 7f
24

# PKCS#1 v1.5 Encryption Example 8.20
# ----------------------------------

# Message:
ba 28 33 66 04 76 d8 73 15 07 66 9f 50 29 8c 2c 
68 d4 4c 53 e3 d0 a8 03 78 6e 

# Seed:
86 ce 8e 92 ad 88 b1 46 2a 17 1a f2 8b 4e 99 08
8c 04 98 a2 9b 4c a5 e6 6f 64 f8 6a de c7 f9 64
e8 eb 82 57 c5 c0 1c da 16 5e 0f 75 11 db 14 7c
10 bc 07 51 5f 04 f0 5f 52 d2 bc 89 22 83 57 c6
61 b4 3f 43 d5 42 db e7 78 34 09 1c 7c e7 0b 18
2e 19 bc 93 6c 30 68 4a 83 1e 2c 3b 8a 4a 74 46
3f 0c 9f 1b

# Encryption:
01 12 ac 28 98 a2 5c 13 28 79 1f c9 6a 82 b7 29
18 b5 1a 66 8c 54 0b cf fc 8e 0b 3a af e2 05 ee
87 1f 5e b7 6a d1 16 d3 04 cc 04 42 bd b1 af 5b
9a 63 45 82 7e 67 8e 40 e3 3f c8 58 ef 6c 45 6b
e4 36 52 bb 2e bc 39 64 b4 bf 4a 93 a8 ee ef 84
04 ae f1 a0 44 41 5a 15 6f 7c fd 79 95 ac 25 e7
b0 30 26 69 8e 14 48 9e fc c1 ae 92 96 58 bb 66
3d e0 ac 44 a7 a5 5c aa 0b 0b 20 34 c2 aa ec 13
4e

# =============================================

# Example 9: A 1027-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
06 8e f0 b2 74 15 7d 7b 5e c7 29 03 86 59 56 18 
3e 0e 34 62 ec d2 21 14 ca d4 c7 ba ac 94 c7 c2 
c5 e6 cb df a2 ab fe af 8d 23 18 2e 9c 08 81 5d 
10 0d 8e 8f 62 1d 3c d4 af bb 99 85 96 6f d6 a4 
18 2b 59 90 88 a3 5d 77 fe 01 78 ac f7 53 1c 70 
d8 96 ff 78 8e dc 82 06 05 40 ef ef d9 f3 c2 4d 
5b e6 9a f7 f2 f4 44 85 b1 91 18 a4 68 81 4f c5 
13 e3 a1 ce d6 77 91 f9 03 6e ee 56 fa 9e 20 60 
2b 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
06 8e f0 b2 74 15 7d 7b 5e c7 29 03 86 59 56 18 
3e 0e 34 62 ec d2 21 14 ca d4 c7 ba ac 94 c7 c2 
c5 e6 cb df a2 ab fe af 8d 23 18 2e 9c 08 81 5d 
10 0d 8e 8f 62 1d 3c d4 af bb 99 85 96 6f d6 a4 
18 2b 59 90 88 a3 5d 77 fe 01 78 ac f7 53 1c 70 
d8 96 ff 78 8e dc 82 06 05 40 ef ef d9 f3 c2 4d 
5b e6 9a f7 f2 f4 44 85 b1 91 18 a4 68 81 4f c5 
13 e3 a1 ce d6 77 91 f9 03 6e ee 56 fa 9e 20 60 
2b 

# Public exponent: 
01 00 01 

# Exponent: 
71 94 0b e6 79 7b b3 28 5e bd c2 0c c9 27 5f 5d 
77 55 88 e9 af 6f 68 7a 2e 39 d2 c1 91 11 10 76 
95 dd ed e3 91 d2 19 6b 29 58 1d f3 15 4a 37 12 
fa 6f cd f8 5b b4 fd 48 64 1f 07 1f fd b1 de 08 
a1 d5 92 1c a1 0e 68 dc 04 13 13 c9 bb cb 80 81 
bd b5 d4 60 42 27 cb e5 78 07 41 65 74 d1 a3 8d 
a0 b2 34 4b b2 15 b4 18 2b 10 6b 2b 53 4a 8d 32 
06 f2 d7 d3 03 b8 dd 5b ce 29 2a bf 75 cd 76 49 

# Prime 1: 
02 95 10 b9 33 b7 c8 4e 41 ff cc 72 2e 32 38 60 
c9 b2 d0 88 3c 68 33 62 4b a5 bb b8 97 53 d7 60 
3a 7d cf 26 6c c8 f4 bb 07 48 48 26 0f 68 dd 82 
6b 63 8a 8d d2 ef af 68 aa ee 26 5a e8 98 39 b1 
63 

# Prime 2: 
02 8a 2d 03 34 49 44 62 f6 11 f3 60 3e 0d 37 4f 
3e 32 d2 fa b5 e9 d6 97 2d fd 70 79 64 46 a4 8b 
c3 03 ee 25 8b 75 a1 b7 ac e7 0d 48 58 51 79 4e 
42 84 f2 dc 51 d3 97 8d 55 53 7b ea 22 55 11 f4 
99 

# Prime exponent 1: 
01 b9 55 0d df da 3d 6f 09 9e e5 f6 00 a3 64 82 
31 86 20 b4 23 68 09 8e 01 24 e7 5b 88 23 e0 31 
0d 3b ba d5 53 61 22 09 cf 05 d1 ad 1f 32 8a 57 
ac ac 2a ef 1e 39 08 69 1f 5c 98 dc ae 56 1a 86 
33 

# Prime exponent 2: 
e8 ff a4 8c 52 4d 5d a1 8d 61 48 76 34 4a 43 ed 
a8 4d 0f 67 ad bb 27 46 65 f2 ae ae 0e da dc f3 
30 2f 61 e9 0e 68 bb e8 0c 8d 28 05 ee 7c a8 5d 
12 43 ee 4c ac a5 3d 12 fc ee 05 43 f4 74 90 d1 

# Coefficient: 
01 98 4a 81 5c db 46 ad 81 cd 82 10 aa 07 22 70 
0c 59 90 9d dc 9c 4a 49 f1 9b e1 5d a6 04 47 8c 
21 88 81 d5 43 a7 54 6b f7 75 69 4c 2b d1 3d da 
69 2c d0 bc 24 39 ef 22 ef b6 7f fc 5d 46 95 21 
b2 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 9.1
# ----------------------------------

# Message:
96 ad 3b a4 d2 9b dd 35 25 bb c5 d0 2e 88 c0 13 
3f fd ea 40 94 74 db 34 df 73 3c a5 9c 3a 23 2f 
6a 64 c2 14 3c a1 31 a7 f1 8d 00 5b b3 aa 6c a1 
ea be aa 13 6b ec 37 db 11 1d 4d 8f 61 

# Seed:
10 e9 f5 09 3c ec 87 2e db 16 16 2b 6b bd 52 12
f6 10 1a 71 d5 1d c1 42 0c ad d1 d5 50 fa f9 af
40 f5 73 d3 e3 ab 68 91 d8 a8 82 ef 06 56 bc 30
06 2a 05 b1 cc 27 7a 11 c9 ba 2e fd 51 03 c5 6e
23

# Encryption:
04 de c0 1b bf 8e 0c 22 96 d9 f5 bc 2d 2b a8 95
34 3e c3 0e c5 44 13 57 6d 80 84 b9 31 43 cf a2
d2 03 3b f4 c2 c2 e0 3f 5b 02 59 ed f1 4a 8f 3e
11 69 85 77 65 2a 2a b9 f5 1a b0 18 7c c7 51 75
c8 6d fc 9a 45 b1 e4 eb 8b 54 47 a9 4a b9 b5 17
7a 04 04 ba 49 e5 7f 83 c9 3f 7f e2 de 24 18 45
f5 81 f6 42 12 43 7e 0c 04 ff 34 ea 1d ee 6d d2
8f 6f f3 36 72 35 44 13 74 f0 f2 ef 71 a9 cd ae
9e

# PKCS#1 v1.5 Encryption Example 9.2
# ----------------------------------

# Message:
7f 92 ab b6 e5 2e d5 d4 20 84 9e d6 cc ab 36 c3 
d0 84 92 55 43 1e 19 3d 67 bd 94 4b 6c 0f ce fb 
77 29 cf 5a 31 

# Seed:
ce a9 68 be 78 ab 5f aa c2 27 dc 3c 6f c9 ce d4
9f 85 1e d5 8b 08 d5 ca 37 54 28 48 9a fb ef 3b
f5 ed 83 74 6d 95 9a 0a 56 e9 ac 66 ff 2e 7c 8b
8c 3a da 97 fa 15 dd 7f 99 13 41 74 70 70 cc ad
65 42 bd 7f 4b 33 f5 56 04 45 8b 91 03 ae 13 dc
89 b4 e6 2c cb f8 4f f7 3b

# Encryption:
02 5a 1e 65 eb 37 f4 b3 28 cc 31 b0 17 24 f8 3c
26 ed 8d 18 a6 51 65 21 3b a0 ef f7 ae 76 7e e6
0b 99 27 76 1a 06 94 b5 c3 bb 64 b7 e0 39 96 a3
56 8d 6f d1 ac 6b 7a 8b 71 97 5b b0 71 6d 94 5c
02 d4 73 68 96 6f ff b1 d4 49 fc 6e d7 3e 2d 19
83 1b 86 d1 87 67 51 29 36 69 e7 7d 6e 12 a0 f0
c9 62 df cd 40 0f b8 3c e8 26 07 16 31 7a d5 fd
e2 18 49 aa 6f 68 e7 0c e0 b5 b3 11 42 89 8a d1
a2

# PKCS#1 v1.5 Encryption Example 9.3
# ----------------------------------

# Message:
f5 b5 35 b6 3d 53 5e 21 37 73 2c 30 13 37 c9 53 
ea 2e dd 58 a7 8c 20 25 83 2d ca 9d 6b cd ad 87 
c9 97 c9 06 83 6b 2b f9 5c 83 f0 3f 7a fa 29 01 
34 6d 67 4a a6 9a 1b 47 f6 b9 16 fd 4b 

# Seed:
c8 9e fd b2 34 e0 1a 2f 07 76 29 57 e8 c0 a4 f9
1a ba e6 d4 f3 60 e8 9a 7c 48 6c 55 49 f3 d0 ad
75 77 7f 0c cd 97 a0 97 5e 98 44 d9 86 87 02 76
c9 82 5f 5d 1e ef 3d 1b 48 7a bc d1 9b 51 2d 08
be

# Encryption:
03 9a 1c e8 c0 91 62 51 a0 1e 5f b6 61 5d 2e 11
98 2f 4f ae 7d 46 df 21 da a2 98 f2 c7 46 11 b5
58 16 cc 27 fa 37 27 9a af 59 02 ec b6 c8 39 fa
90 0c f3 af 86 ff 40 a4 47 dc 40 22 35 f9 06 67
91 23 f3 cd 38 19 04 f3 3e ee 35 3a c9 0d 5f 7f
20 3a 6e a8 91 35 1a db 11 60 1b d9 d6 b0 a0 24
33 3a 24 55 be 1c a6 2e d8 2c f3 7c f8 de 4e 23
92 dd 54 e7 75 03 3c 56 99 0b ae 5c 43 91 b6 65
3a

# PKCS#1 v1.5 Encryption Example 9.4
# ----------------------------------

# Message:
a7 cf 29 82 03 47 a5 2a b9 cc 42 04 bb 6a c1 bd 
7f d9 0a 75 8a 15 6d fa 9c e7 19 27 e7 4e e2 15 
98 70 8c f6 9c fd 79 85 74 2f 50 27 ff a7 10 

# Seed:
10 9f 0d 6c a0 e1 3f 50 4c 07 d8 4a 5b be 43 ed
6a 94 ac ba fa b0 48 89 36 05 d3 41 c5 d2 8d 85
44 c3 fd 28 43 50 f2 c2 2d 1f 36 7d fb 9b 6a 67
51 d0 6a ae b1 7c 3c 0a 10 11 ae 38 fb a4 e4 6d
4e 44 c4 82 87 9e ba 06 44 37 48 71 b5 c5 3b

# Encryption:
04 48 c3 9c c4 57 d6 cc c3 0b 0d 76 ff 5a 13 2e
00 c7 5d 53 36 46 b8 44 0b 13 ce 73 0e 1b 7a d8
3b b2 ba 4a 08 2b b5 a3 3c f4 14 66 e0 67 fc f1
6c 6a 29 f1 87 9f 77 e9 b0 db f3 ec 08 05 75 be
ba c0 c5 67 6b ae e0 02 d5 73 d1 bc c8 a7 0f ed
2a b6 79 43 ef c9 bc 13 13 9e 5d 8d de 2c b3 0e
1b 93 4f 50 cf 6c 45 73 92 3f 73 98 de 66 70 cc
26 34 1f 3e 35 a4 19 36 1e 59 f6 08 98 f2 69 2b
94

# PKCS#1 v1.5 Encryption Example 9.5
# ----------------------------------

# Message:
b8 03 c7 ef 5f 9a 9b d5 84 01 

# Seed:
8e 77 58 ed 4d 1b a4 dc e0 88 92 6c 10 b2 f3 d4
c1 e2 67 1a e7 2e 65 9f 72 1f f7 be 6f c0 35 e3
85 d5 12 d0 7a 38 dc ca 1e b8 31 fe f9 06 10 04
44 ee 99 5c b0 7b c2 6a 5d c9 2b 27 2a 74 a9 2d
21 49 73 25 22 d5 39 43 4f a4 d0 3b 07 cf 96 99
95 8c 19 89 1d 1d 59 06 aa 36 d0 a8 d0 6c 6f c8
7a 45 51 bf 18 58 bd fe d5 f8 86 cc 8d 31 ee 4c
16 4e 98 1d

# Encryption:
04 b6 2d 4d 4b 9c 1c 3e 05 13 09 79 5c 69 24 8a
ed 38 9d e2 4a 6c 79 53 8a 2d 51 d5 d0 37 19 a8
a5 28 75 0d 5d 25 4a 1b 91 40 96 db 96 d8 3d 2d
9a aa 2a 16 5b b1 34 6e 44 c3 57 27 56 c3 8d 52
f8 d3 07 c0 4e 1e fe e5 84 78 22 31 7a b4 69 34
5b 86 c7 b8 5b 54 15 41 c9 92 ab ea 98 d1 08 a7
df c7 09 1c 30 c6 68 5a f0 e3 3d 04 66 ac e4 cc
cc b3 4e 5b 26 6d 3d 6b 94 7b c7 c0 ae 34 d5 e2
64

# PKCS#1 v1.5 Encryption Example 9.6
# ----------------------------------

# Message:
61 7e a9 e4 a5 6c 4e c1 d3 d7 fd 7f 32 81 ae 7f 
a9 32 b2 a0 a6 cf 55 eb 60 48 14 56 81 b3 58 8e 
df 70 12 69 f8 9f e6 4a a1 4a d8 df 0d 46 79 61 
31 

# Seed:
64 d7 eb d0 48 50 eb 6f 7a e1 af 48 12 0a 80 13
0f 32 ed b5 03 69 64 0b 22 2b 8d 63 ef f6 57 12
70 dc ab 31 76 d0 24 72 28 dc d1 c3 f3 cf d5 13
31 b7 56 a8 65 2a 14 dd aa b9 93 96 b9 19 9a 73
87 50 d6 9e fc d3 77 f1 84 ae 19 b5 a9

# Encryption:
02 df e7 29 58 59 58 81 d8 07 56 6d 3e 36 07 c0
22 e4 61 fe 1d be d3 cc 6d 63 de dc b7 19 0f 06
c7 d2 4b 4f 03 26 4c af bb 74 82 ec 28 b9 ba 48
9d 03 11 5a f5 8f de 47 5e da 58 bf 01 55 cd f1
af 16 df 20 6b cd 12 57 04 c3 e3 15 ad 3d 95 44
b9 e2 c2 fe a8 10 ce 48 45 56 d2 9e 07 ad bf 0f
f4 61 cc bc f6 62 bd 74 95 9a 43 7d 4c 80 11 ce
ad a5 50 2a f7 67 6d 9a 15 21 e3 18 9d af de 00
dd

# PKCS#1 v1.5 Encryption Example 9.7
# ----------------------------------

# Message:
c9 1f 2c 98 c7 5b 2f d4 cd 8d 5c 7f f2 e7 69 b1 
2f 28 f3 13 fb ac b5 1b 5e 50 14 df ce 9d 63 5e 
7c 6b 2d 88 af 5e ac 30 d1 62 b8 dd c2 2e d8 bc 
7b ee 50 6b fb f3 1e 51 ba 48 f4 26 

# Seed:
6c 44 59 61 f8 6f a2 98 d7 64 7c 22 24 d9 5b c1
27 da fc bd 5b 90 21 ec 7e 9e bd e9 6f 5d 7a 76
f4 ee d0 ac 92 2a 6e 93 eb 3b 4c 3c 43 f8 a5 7e
18 29 4e 1a 51 73 b0 f7 e2 dd 2c 4e 57 7a 4b ec
e7 14

# Encryption:
03 a8 85 aa f7 b7 d9 4f da e3 4b 41 c3 74 17 66
a4 09 c1 ee 02 32 b1 c5 3e 5f 6c 5a a5 4a c1 ef
0e 5b f9 9b 9f 7d f3 e9 b0 0c ed 94 76 ee 1a db
8f c5 71 68 91 f5 4d 45 ce da 70 b9 db 3b 8f ad
25 e7 77 95 af 14 81 ec 49 21 84 c4 9e 9d f8 23
ee 64 66 41 0f 65 01 3c e3 c5 79 16 63 5d a8 30
67 7f 93 2b da ba b5 c0 a2 08 d7 f4 36 7a 7e ab
a7 5b 04 5d 25 ac fd b4 15 fe e0 52 19 a5 84 23
7a

# PKCS#1 v1.5 Encryption Example 9.8
# ----------------------------------

# Message:
7b 1c 31 51 a3 8d 32 ec 7b 82 c4 8c 00 0a a4 81 
de 41 8e 80 3b 67 3d 2e 9a 0f c3 d5 e9 74 ad cd 
ce bd 3c 2a 8f 41 14 21 18 a5 5e 87 d0 4b ba d5 
b3 64 25 

# Seed:
b0 f5 be 9b 3f 23 7c c5 af ca 5a 99 fc cb 77 b6
ef d8 68 94 7f 98 55 4f be ce ac fa 88 4c 15 15
39 d7 cf 42 3e 72 60 31 bf eb 8d d2 d4 f3 01 da
6b df ca d6 e5 81 65 82 ad dc e1 0d 85 d5 ee 1d
04 46 f3 73 b9 5e e1 60 bc f0 35

# Encryption:
06 6c 30 09 dc 6c ba c7 b8 bd 51 41 c5 5c f8 6a
3d 79 69 d5 85 45 2e 3a 66 54 0f 94 02 db a2 15
e3 fb 3a 27 7f 0d 33 96 c8 c0 08 af 19 91 3e 3d
91 e4 0f 86 76 06 b5 bf 54 30 4c 04 71 da dd 64
04 b6 7a 48 57 bf 52 24 6c 0d 60 18 23 dd 03 80
c6 60 9d ac b9 2d b6 02 e5 57 64 ae 46 50 df 1a
db b4 b9 5f a5 af 4b cd 11 21 f1 3c 0a 73 23 ad
a8 c6 0d e3 d0 81 72 9d 19 7f 9c f8 e2 7c de 80
b1

# PKCS#1 v1.5 Encryption Example 9.9
# ----------------------------------

# Message:
b2 93 c6 f6 d0 5d 1f 38 b5 61 ea 3d 0d 0a d6 a2 
af 83 09 bb 9a de fa 77 8f d6 bb 9f ff 3e 01 0c 
40 4c 53 39 97 cc fe d7 e1 91 7a 66 92 61 cf cc 
a4 e3 70 29 99 1d 2d e9 b2 99 

# Seed:
87 bd 2f 6b b4 82 45 59 1d 65 f0 23 a6 5b 63 bb
ba 84 19 79 2c 01 41 09 2b 1d b1 be 53 e8 c9 b4
df 95 f0 ad 55 b9 6e 5e 57 61 5d 21 4b 49 77 87
0a 27 2f 72 31 66 c6 20 45 76 5e 6b 4a 73 a7 c9
eb e1 1d 24

# Encryption:
03 10 4e c6 c4 ab 9d ac ae 42 7f b1 06 99 bc ae
00 3d a5 6f 6d 07 50 95 87 14 5b 73 3e db 53 23
9a 6f 42 22 86 83 9c ac 06 4a df ee e5 dc 89 78
0c dd ad cc 80 72 19 d6 a9 7b 85 c1 3f 27 93 7d
70 32 7f 82 cc 36 a5 da 8e 45 63 77 fc a2 3a ee
51 4e 04 4a bf 1f 66 c3 e7 bd eb c2 cf f6 28 bd
52 4a 09 66 e2 63 8d 28 33 bf a3 43 eb c7 41 f7
6b 5b 70 a1 13 6f 4a bc 60 28 64 a2 ad 43 a7 91
bd

# PKCS#1 v1.5 Encryption Example 9.10
# ----------------------------------

# Message:
08 38 f4 a5 92 

# Seed:
89 c5 89 8c 6c 3d c6 cb db 1a cb 28 05 df 53 98
f0 b3 35 8a 18 e5 e6 3c 14 d2 0c 98 d2 1d 9f d8
b2 ec c9 a0 e8 3d af 0c 06 92 68 bb e8 6f ee a5
1c 93 79 1b 68 e5 d9 3b 74 5f ea f6 ad c4 2b 83
c3 09 c9 cd 3d fe 1c 06 15 3a b8 80 85 56 18 98
90 be 05 3a 92 54 88 d0 29 fe 50 40 e3 e7 d5 d5
31 b3 2e b9 d2 f4 ee a2 21 11 b3 8a 65 53 f0 0e
dd 23 65 57 5b b9 49 f3 63

# Encryption:
01 4f 27 97 dd e8 d4 60 18 ff 23 d8 9b e2 e3 ae
04 6e ed 31 97 c8 79 c6 0e 26 f3 d2 40 08 66 eb
50 d7 b4 5f 6b 01 ae 9c a0 06 84 7e fb e9 ab cc
9b c3 e3 56 90 07 2b 68 db 9e cd 92 6d 94 5f 78
7b 27 c3 75 3b f9 6b 2d 49 98 30 84 14 2c 42 a1
26 1a ff 7b 17 ff 4b 20 de 9b ff a5 86 24 ab 37
1d 4c e2 f9 64 69 a8 e1 03 8d 57 20 b8 1c f0 42
dc 78 bf da 9a 3c cb 61 60 81 2d de a1 58 bd 2f
5c

# PKCS#1 v1.5 Encryption Example 9.11
# ----------------------------------

# Message:
12 38 0c 5e 80 bb 95 bb c8 85 73 57 fe fd 17 bf 
9e 50 96 27 d2 8c df cd 12 bb 13 16 61 b3 42 df 
a6 ca 67 2e 13 a8 85 1f ce 19 b1 a8 ca f0 e3 3c 
d6 ef 53 8a 05 fa 54 26 9a 13 78 e7 

# Seed:
88 03 31 eb e9 1a b6 ce 16 84 d9 af 5d 97 7e b4
26 ca 71 56 e0 b6 f4 33 6c 6e 09 33 d6 fa 48 78
2c 0a c9 69 f3 dd e6 1d 8f d7 4c 47 fe 9e 30 61
71 0d 24 5b 1d 38 11 04 28 60 c1 f4 8d 2b 8f fd
80 9e

# Encryption:
04 6c 54 5f f4 96 c2 1f 69 01 27 24 54 18 cc 5f
b1 8f 09 10 2e 7a ca 87 e2 6e 20 82 fc 16 f6 2f
e9 f4 2a 72 22 71 a7 9e ae e9 62 5a 7e 63 2c 19
36 40 4c ec 62 11 d8 23 86 3b a0 2c 6b 0a 83 19
58 b4 ed 8f c6 25 a2 e5 2a 05 4f 8f 18 1f 13 0f
8b c4 b1 df bd 44 b7 0a 35 b3 5e 9c 7f 4a c5 5e
e5 e2 cb 06 8b 75 86 39 b2 cd 64 3d bf a8 2e 2d
97 20 e4 89 f5 c8 21 d8 eb dc 13 68 a9 d3 46 8a
37

# PKCS#1 v1.5 Encryption Example 9.12
# ----------------------------------

# Message:
ee e1 e4 5d 18 b1 47 c2 69 a6 0a 9c 64 20 18 ed 
6c d1 15 7c d0 ce 2b 29 68 df a4 b4 97 fc 40 b2 
24 bd 86 1e 25 35 12 2b 

# Seed:
5c 51 3e 51 44 52 b1 4a ee 33 b6 17 60 b8 58 c5
35 7c 7d 7f 20 e4 a3 7c 7e ef e4 19 ca e3 fd 16
f9 d8 3e 5e cd e1 9e e6 32 85 dd ce 66 80 ee 94
64 fe 83 75 6e 90 31 d6 37 9e 6a 6b 38 4c e2 77
bc 64 2c ed 83 b2 9c f7 4b 72 ce ff f5 30 71 04
e1 83 de 2c e6 a1

# Encryption:
03 a8 64 83 cb b7 2c 15 f5 a6 93 2b 01 2f 40 c4
b1 73 33 3b 26 86 f4 98 4b a6 6e 24 c7 cf 44 41
23 ba 2e a6 66 a1 75 5d 09 35 7b ee a4 37 9c e3
cd b0 a7 7a 6e e3 b7 ca 60 db 68 24 17 f7 16 3d
7d 3a c7 35 28 11 bb 94 c5 b7 71 f3 d3 fd a7 73
e5 ce bc 8b c6 60 11 55 b3 f4 e4 b4 ca 85 d9 ba
b8 ec 25 8c ec ff 44 33 c9 2e 8f 86 3d 96 fc c7
94 29 49 8a 9e 17 90 33 0b c4 87 c0 10 d7 99 24
5d

# PKCS#1 v1.5 Encryption Example 9.13
# ----------------------------------

# Message:
55 09 a5 c1 ac 54 89 dc b7 65 f3 7c eb be 7d 81 
cf 02 76 b1 f2 cf f9 5d 27 4b bd 04 

# Seed:
76 e8 1a 51 37 1f b5 07 41 14 1d cb 31 d5 1d 1c
46 1f cc 02 6a ea 85 20 15 d4 68 74 0b 45 23 40
5f 95 ba 87 9b 08 69 bf 03 1a 60 65 4f c4 e5 68
c1 95 7c e4 e4 2a 35 0a 95 bf 8c b2 a8 b5 fd e6
dc fd 25 05 c0 37 a2 9e de 68 9c 53 d8 32 19 e7
3e 64 08 17 bf 8a fc 9a b0 04 ec ae c8 43 e4 fe
4e 38

# Encryption:
02 55 de 28 0b 71 af ef af a2 0f 24 1e 08 1b 7e
c6 c1 62 dd da 18 84 fa 9f 82 5c 4c e7 63 6e fb
c1 1b 84 a6 eb ea 35 89 25 71 ac 9e 6b 1a d8 47
3f a5 73 c8 83 c9 f2 15 a1 95 80 c3 ea 30 2f 88
f4 4f 48 e4 d9 5c 34 40 d4 93 1f 17 66 a1 fe 7e
79 0e 5d 38 e8 5d 1f 63 85 0a 3c 70 7a d8 97 7b
88 c3 8c b9 ed 98 34 5c d3 50 c3 dd 45 30 9f c8
1c b4 62 76 e4 ad 64 7f a8 4c 14 12 5e fd 67 d8
ba

# PKCS#1 v1.5 Encryption Example 9.14
# ----------------------------------

# Message:
02 53 81 a5 5c 8e 48 7d 7f 4f f7 c3 6c fb 37 50 
07 d1 9f 93 71 13 6e 2b 3d f4 42 5e ee ee 5d 79 
c3 52 61 fb b4 ea 68 bd 91 e8 ed ab a2 32 9e 29 
31 53 06 c7 d7 18 33 15 5b 88 

# Seed:
aa 31 0f 62 da 90 da 4a 20 28 b3 81 39 9c b7 e0
ed 3b b5 10 57 5c 9b d0 f6 38 21 5b af 78 08 e3
24 5e af 38 b7 6e 26 52 29 0b 7c c6 20 90 7c da
0c b7 af 07 3c 12 2f f8 3d aa e1 98 6b 43 ec 1a
14 82 db a6

# Encryption:
04 9b fa c6 41 0b 77 80 39 7a 49 fb 88 93 b2 4d
90 3b 36 00 f3 3c 78 2c 14 75 b5 d2 4b 4f 4e 88
fd 11 24 ef 06 45 f3 d3 91 ed 31 00 6d d1 b7 c2
64 12 8d 0d b9 aa 0d 65 a0 9f fb e2 9a 94 cc a3
58 da 64 bd 1b 72 ff 55 88 88 08 10 5b e0 91 ae
23 ea 3f 34 75 05 17 9e cc b2 41 0d 89 de cb 62
33 0f 36 c7 44 26 2e b2 b0 78 b4 25 4b cd ce ce
21 1c f0 57 4a 24 68 e2 a1 e0 18 bc 31 f5 02 22
35

# PKCS#1 v1.5 Encryption Example 9.15
# ----------------------------------

# Message:
07 3c 43 67 ba fd 48 10 d7 70 44 60 c7 83 d3 50 
f7 cc d0 99 47 2f 79 f7 fd f2 3e f0 cf aa cc ab 
95 71 df 53 fd e4 

# Seed:
f9 5a 39 96 de f1 ca fc 1a 4a 0b 34 14 6b 73 de
6b 5a 92 cb 81 2e 20 b2 0c 12 2a 25 1e 6b 22 e0
46 9c 98 db 12 8d 43 ce fc dd 80 c7 ff 36 99 80
16 a8 92 a8 b6 34 b3 6b 9b 0c e8 7e a8 0b 5d d9
fd 7a 08 29 14 e7 3b 83 a3 84 1d e5 1b 71 b1 a6
b7 ef 7d e4 e4 b8 8e dc

# Encryption:
02 de e4 8b 0f 99 53 ee 12 c7 18 b3 03 b0 c8 9d
61 50 eb be 83 ad 62 4e 11 7e 3f 27 04 a0 b1 7a
2e be e8 38 52 56 f0 e6 42 80 fb 06 c3 b1 46 c0
99 af 23 a9 f2 47 93 39 37 81 a5 55 ac 4e a2 d8
8d 78 5d 8c db 6e 7a 2f 89 52 d2 50 3c cf 90 1f
12 39 f6 f7 b1 ac d4 41 21 c3 65 fd ae 37 07 46
de 45 26 e7 c6 56 0f 87 54 6e d5 77 cf 97 98 be
f4 7e 49 20 65 50 9c 49 21 2d 37 0d ea 05 22 d7
94

# PKCS#1 v1.5 Encryption Example 9.16
# ----------------------------------

# Message:
ba b2 0b 9a 6a 53 2e 6f 8a f0 78 b3 a1 85 e0 aa 
86 e6 16 81 bd 1b d7 75 04 4a 2c 95 8d 61 ed c2 
51 60 7c d9 f3 11 48 f5 a9 11 

# Seed:
37 6c f0 18 14 3b 0c e6 70 21 68 4c bb 36 e4 af
e2 ff 9d e1 8b c7 f0 2b ed 86 3b f1 bc 34 66 31
9e 72 0f ee f1 9d 38 e2 6b ca 7e 99 d4 09 6a 9e
ed 8d e5 bf 20 3f c7 dd 9c 84 85 df a5 b6 90 75
ef 0c c0 37 fc df b5 5a 0c 92 8c da c0 cb a0 49
7e b6 0e 3f

# Encryption:
04 8d d7 50 99 18 43 0e da bc 01 46 8d b7 e2 87
a7 38 1e 42 8b 5f c9 3c e8 de fd 7f 49 d5 e9 34
15 30 43 d1 37 ac a9 f7 b9 75 77 b7 0d c0 16 80
f2 db a9 1e 93 2b 53 ac f2 a7 f3 34 8f 56 54 b2
cf cf e7 5d 48 f0 fa 45 00 d1 ba 5d 29 24 7f f1
42 d6 b9 80 e1 1b 9d ba 68 83 f7 3b b8 55 a2 4c
c4 eb 90 68 23 6d aa 0f 2f 93 45 8d 72 72 6c 2d
8e 31 25 9a cd 3a 7f de b6 fe b5 c6 d2 ed 17 8d
db

# PKCS#1 v1.5 Encryption Example 9.17
# ----------------------------------

# Message:
19 b1 f4 cd 3d bd f0 5b 3d 5f 16 80 85 6c 5a 74 
4f 51 62 b5 20 bf cd fc 98 87 dd 92 be 9d 8c b6 
25 c7 25 ad 75 f4 f2 ca a5 ef 51 fa ca 71 b5 e6 
6f 84 f2 fc 6f 67 8a ce 80 9d 76 b8 42 ea fe 

# Seed:
78 a7 18 82 85 3d 85 af 16 5c c0 b2 2f fd c8 87
3d f4 9e 2d 6c c0 ad 0b 0a 99 5b 4a 25 9c 86 7d
c1 aa b6 42 8a 5b 8e 9f 3b e8 7d 87 f9 e6 ea c5
50 f4 df 11 36 8e 7f 6c fd 7d 0e 47 6a 45 9b

# Encryption:
06 2a 78 db 5c a6 9a 30 48 10 78 94 93 db 8c d6
44 fb 29 4e 17 fd 36 45 32 b8 ec 17 ce 3f da 70
47 64 65 62 4a 60 a6 ec db d5 d9 72 40 c8 d8 9d
ea c1 1d c3 0d 7b 85 14 41 40 8e dd 64 28 81 a0
11 2b 62 f8 cf de 34 d9 c1 13 85 c2 91 89 bf 38
93 a6 ab ac e0 4a 10 aa 68 0c d3 a6 ee 00 2c 30
70 35 d2 39 9a 2a 60 b5 a7 f1 ca af 05 84 d1 0f
d0 6d 6e fd 56 11 4c 05 e4 3d 42 d8 34 f0 3e 3f
e9

# PKCS#1 v1.5 Encryption Example 9.18
# ----------------------------------

# Message:
8c b7 3b c4 78 50 c1 7f eb d3 4f f4 b7 32 3b 50 
53 03 0b 96 22 14 03 cd ef 45 de c6 5b a6 0e a3 

# Seed:
ed 64 78 61 48 81 eb e3 fd d6 d9 ee 05 f2 76 5f
c8 a3 ea a5 80 31 23 5a f9 6f 86 e7 f2 c8 13 ec
04 80 66 1d 1c 2b 4d ef 74 2b 2e 41 41 9d f2 88
3e d5 86 79 cb 9a da e4 de c4 cf 77 f8 bc 29 41
d0 33 f8 87 7a 90 6d dc 88 d6 dd 3c 53 96 ad aa
c0 3e b2 a7 09 4c 0f df 0a 44 a6 b9 23 ab

# Encryption:
03 97 8d 4e ff c6 d6 2f 23 2b f4 6b 42 90 c5 01
1d 73 28 0a b0 e8 0a b9 d6 05 0a 85 2a 66 79 e0
17 e1 70 d9 e7 15 6c 8d cb d4 6c de da 70 ff 9a
55 16 8b 9f dc 03 65 b8 aa bb 90 9a ad 40 d5 95
c9 0b 9f 00 c1 e0 0a d6 f0 1d 54 43 07 b2 54 ea
f4 25 5d e6 2b fd 8a 5f 7e 79 dc fc 74 45 ce c6
3d f0 c2 bd 0a d9 6c b7 02 42 b3 0b 32 42 07 88
16 b5 8b 0a 9f fa 16 f1 d8 63 15 f8 a1 cb 2f 8f
6b

# PKCS#1 v1.5 Encryption Example 9.19
# ----------------------------------

# Message:
8e a5 56 43 df e7 cb e0 75 c1 7b 93 c6 ba 6b 5a 
74 ea 8d 1a ba b9 c7 28 ae 5b 00 86 6c 62 88 0d 
3c 00 05 20 37 cf 80 2d 2c f2 a8 e1 be a5 8c 7b 
a6 04 77 4d 4f 80 bc 04 

# Seed:
af 9d 9d 31 2e 22 57 0f bc 4b c8 5a 44 5e d8 d8
99 66 0e d2 4c f0 30 15 33 94 6e 5c b9 47 1a 27
c5 10 cd 17 55 91 d2 3d 36 3d c4 e3 e6 9c 7b b4
65 51 7a 4c d1 d1 ce 41 3e 10 16 ae af d5 5d 2c
b9 de bc fe 4b 1d

# Encryption:
05 87 11 19 16 bb 42 f8 47 57 69 1e fe de 7e b7
79 6a 5c f8 87 c3 7c a9 f6 1b 45 1f b1 ba de 38
9c fc 5f dc 21 98 41 bb 41 da 82 74 52 72 5f c8
2b 4d 1a de 56 9c ee c8 05 79 ed b1 b4 b1 5b 46
ad 7d 45 b2 45 98 80 23 ea 0d fc b3 74 4a 69 2e
b9 ac a8 5f 21 10 10 af b1 e9 89 4f 85 4a 5e 34
fe 89 aa 05 19 68 a3 b2 36 2c 15 0c 6d 97 0e dd
82 b9 e2 d1 3b 38 c3 c2 95 6d 91 50 08 86 41 d5
30

# PKCS#1 v1.5 Encryption Example 9.20
# ----------------------------------

# Message:
52 5c ff a3 

# Seed:
0c 99 30 4c c6 26 3d 1e e2 44 6e 24 8c 27 11 2c
8f 96 da 82 51 5f 06 f8 12 39 8a 88 81 1f 39 fb
05 62 05 c4 4d 6b d4 85 5a 62 c2 1c 60 1b 88 f8
02 34 e2 32 41 f7 16 32 2d 80 54 a8 4a 1f c3 c8
46 de fb 61 76 7c c4 d8 16 fa 7b 37 47 f8 72 9b
f3 37 2a c2 c2 29 d0 52 f4 5b c4 2f c3 80 50 dc
a3 f2 63 2e 60 07 b6 08 53 b7 e0 cc b3 da a4 94
e5 53 35 fd 04 f1 3d 5f ad a7

# Encryption:
06 3e 45 08 e5 31 2b 5c 38 69 4d e8 2a 71 ed a3
12 e9 ae d0 5b c1 43 d3 38 f7 f2 28 12 d9 3c 28
65 12 6a 9b 3a 42 c3 ca 19 ed b3 46 01 c0 b2 8a
75 66 3b 18 f2 39 f5 ad ca a4 e9 fa 9f 61 18 04
72 6f a1 29 62 bb a3 23 0c b8 86 d6 67 82 a4 7e
a9 50 2a 83 71 e7 57 3b b6 b0 26 6c 33 64 6b df
53 f8 de 36 8e f2 05 b1 1a 85 ba f2 1d af 3c e7
a2 59 9f 00 8b 99 45 ee b1 86 ec 19 2c 54 0a c2
3c

# =============================================

# Example 10: A 1028-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
0b 52 cb 6b 5c 3b 9e c5 aa ad 89 4e 51 77 f7 f4 
5b 8d 33 dc bb e9 6a 5b 26 f3 00 72 bf 15 73 a6 
c4 1f b0 a9 7a e1 e5 2e d8 c2 5c 62 b9 8b f5 9d 
e7 b6 8a b9 8c 2d 8b 93 c4 94 27 23 cc 4b ae d2 
b3 93 c0 7b 2b 11 90 9c 73 2d f7 c1 dc bb 43 3a 
83 9d 46 f4 28 e9 dd c8 d3 5f d3 3e ed 29 81 80 
f7 5f 2d 5c 9f e8 53 4f 03 47 f8 68 5c 28 c4 37 
ea 5b 81 1a 28 6e 81 0c 69 7a 88 cd 7e 45 36 4c 
1f 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
0b 52 cb 6b 5c 3b 9e c5 aa ad 89 4e 51 77 f7 f4 
5b 8d 33 dc bb e9 6a 5b 26 f3 00 72 bf 15 73 a6 
c4 1f b0 a9 7a e1 e5 2e d8 c2 5c 62 b9 8b f5 9d 
e7 b6 8a b9 8c 2d 8b 93 c4 94 27 23 cc 4b ae d2 
b3 93 c0 7b 2b 11 90 9c 73 2d f7 c1 dc bb 43 3a 
83 9d 46 f4 28 e9 dd c8 d3 5f d3 3e ed 29 81 80 
f7 5f 2d 5c 9f e8 53 4f 03 47 f8 68 5c 28 c4 37 
ea 5b 81 1a 28 6e 81 0c 69 7a 88 cd 7e 45 36 4c 
1f 

# Public exponent: 
01 00 01 

# Exponent: 
45 cc 14 17 b2 6f ce 3e 9f d3 10 89 b1 a3 cc c4 
6f 8f f2 1e ac 2e 1d 67 c0 ae 20 15 2d c5 0d 1c 
e7 ce 6f 26 40 4e 2e 64 95 b9 77 bf 13 f9 a4 05 
b2 45 80 d6 39 3a 85 22 54 96 e4 ab c4 9e be ff 
de 70 cf 26 76 68 66 f2 76 e1 5b 49 2a f8 03 3f 
1b ac 7f 66 b7 1a 3b af 57 1f fc cc 03 8a 48 86 
93 94 cc a3 fa 00 49 85 b4 34 a5 15 17 87 7e ae 
97 a3 84 94 7f 01 a7 2c f4 b2 01 93 dd 27 64 81 

# Prime 1: 
03 5f ed ee 7e 64 fd 68 65 79 31 a9 df 9d 55 62 
28 df c6 33 a7 50 02 76 8f 65 d7 15 f0 44 f2 3b 
3f e7 88 bb 17 86 4d 61 df 57 9e 68 df 80 de a3 
a6 31 90 62 9d ac 7d e6 29 ef 9a b8 f4 df 0b 27 
7f 

# Prime 2: 
03 5a f8 e6 fe 2f 84 61 fc 89 a6 45 00 f8 19 9c 
cb d3 f4 69 07 85 96 15 56 03 d3 f0 88 08 33 c3 
77 db 07 8e 43 7e 35 60 00 4a d1 d6 f8 d3 47 88 
3f 3e b0 1d 18 9d 52 ef f6 e2 60 e5 2f ba a6 2b 
61 

# Prime exponent 1: 
b2 43 b3 49 8b 67 2a aa 7d 96 7c d9 f2 6a 12 e6 
22 3e 90 9b fe 8b 73 91 65 4f 3b 6c a7 9d 60 12 
70 a1 2b 11 d0 99 99 0f 6f d1 f5 0b e1 58 8f 66 
14 a6 19 63 35 9b 88 e9 b0 52 39 c5 f5 5c 24 db 

# Prime exponent 2: 
01 35 25 ad e5 3f ee d2 61 54 9d 3d ea 8a 60 95 
79 1a 90 b5 98 5a 70 d8 be fb 63 b5 07 10 84 a8 
ce 23 4b 85 dd 2e b8 41 13 f9 d9 aa 18 25 da 71 
e3 e9 48 f3 3f df a7 70 ec 17 2f ae 6c ce 2d 6e 
21 

# Coefficient: 
1c 52 92 93 3c 58 d0 22 59 2f c0 03 38 bf 22 ed 
fc ba 33 16 16 b7 9f 8d 2e fd 1f a2 4c 64 f5 dc 
fa d9 aa 52 ba a4 6e 86 59 dd a9 ea b3 a7 86 3e 
61 37 4a c0 da e0 18 5f b5 8e cb 0b 74 65 ee 07 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 10.1
# ----------------------------------

# Message:
59 22 d0 9f df 65 22 80 2f bc f7 a2 8e c3 d9 fa 
df 60 72 03 a3 1b d0 da 96 3a a0 31 5f 35 e1 a0 
a3 74 d4 87 dd 08 62 a6 f6 be c9 32 b7 db 

# Seed:
61 39 5a 0f a1 2e 1e c2 ff ea 2a 88 90 65 3e 8f
37 8d c4 6c e2 b8 aa 4a 0a 1c 56 7b 30 e5 9c 3a
33 a6 68 98 97 14 fc 3d 45 ac 88 63 27 e3 6c 34
5d a8 58 f9 03 d1 e6 06 74 03 14 cb 80 8d 86 ab
c3 1b 49 b3 5b 3f 20 e3 79 29 dc c8 59 d5 91 41

# Encryption:
04 39 e7 dd 09 af 61 c0 ee 25 f3 e5 c2 95 1d a4
9d 3f d7 08 b2 97 a3 05 5f f9 98 3a 9e a5 38 b8
3d 59 da bd b8 5d af 82 ae 7b b1 97 8e 7d a2 a6
dc 05 87 ef 9c 73 2e e6 88 37 3f bc bf a2 da cf
f9 b3 c1 2f 70 4e e1 4f 83 24 bd 4c c6 bf 9d cd
3f 05 33 c3 b1 1a 0d 38 db cc 7b b7 ef 9a 72 29
6b 6c 13 dc 42 d7 ec 17 f8 51 a5 16 12 c7 49 4c
63 68 a7 fb ec 93 d2 2e 8f 9b 72 bd c0 44 9f b4
30

# PKCS#1 v1.5 Encryption Example 10.2
# ----------------------------------

# Message:
5f ba d4 3a 91 0a 29 0e 50 55 d5 14 e7 1c 8e ab 
f1 f9 33 20 eb d0 da 8f 90 d1 46 a8 f3 dd 5c 1a 
2c 72 0b 93 21 1e 48 29 34 14 9f 1d 21 b9 78 f4 
8a 0b b8 e4 cc a3 f5 d4 5d 3f 3e 3b a8 e1 

# Seed:
21 04 52 61 80 4b f7 54 bc b8 bf 34 98 b1 ad 10
af f3 3d a1 f2 25 ed a5 64 65 90 45 8b 20 70 9f
c8 d0 b4 98 90 7a 83 64 ce 1c 43 6b 6b 1e 73 18
1c 86 c6 77 af 45 c1 7f 9e 4a f3 75 9c ad 24 87

# Encryption:
02 9f 64 ac 33 01 34 00 7f 77 f7 2c 37 f4 17 7c
24 d6 60 be 4e ba c1 86 8a d1 1f 9e 30 51 b8 cd
77 c4 69 11 99 ac 49 19 db 2e d3 63 74 0d 2d de
32 91 f1 0d 92 68 e7 c7 de 37 ea 42 1e bb 1f ad
65 29 f2 92 19 2a 96 80 ba 96 3e da 93 74 03 7a
b9 52 9a 48 6f 35 cb d2 9e 09 ea 98 a5 04 5d 9e
16 bb 87 70 15 5d 70 af d2 17 a1 46 86 27 49 ec
1a d1 59 cf 6e 6a 63 df 14 2b 82 46 d8 44 a7 37
2d

# PKCS#1 v1.5 Encryption Example 10.3
# ----------------------------------

# Message:
22 bb c6 16 ec 6e f3 57 31 56 b4 17 61 ac 3b db 
57 bd 9b 70 36 ed c9 a6 97 88 75 e2 a6 14 cd e3 
ef ed af d3 88 9a 5d bd cc 5f ad 9e 9b ec f8 a8 
bb 80 33 ff 91 df ed 60 4b f8 c6 e9 bb c7 

# Seed:
3b 47 d2 ca 95 54 b3 4f 94 29 57 8d 4f de c5 d9
6e ca 89 b6 81 72 d1 db 13 56 b3 da 7a 69 f1 58
4d 4c 84 6b 18 43 2f 02 f2 59 ca dc 24 e1 54 ff
15 f8 06 f2 53 43 50 0e 13 b5 be 43 c7 b5 ae 7e

# Encryption:
00 c4 e6 c1 0c 25 5e 4b eb 2b 31 c1 65 68 46 b9
7c a2 3a 3b a3 2a b1 9c 64 85 20 11 3d 70 34 df
a4 6b 0c d2 3d 73 99 a9 3c b0 2f 1e ba 94 83 18
e3 79 1c 30 6a 2e b9 c0 c5 6d 8f 7e 83 25 0f 83
ad 49 2f ad f7 83 17 69 f0 2e 18 24 34 74 45 d0
41 9b 98 b8 e7 95 45 65 32 30 0b 92 d6 e4 55 b5
a4 eb a8 53 d6 f7 45 92 90 ab 02 31 99 48 53 a8
c0 7e 54 f5 9c 62 45 37 08 02 89 f9 31 43 eb c6
61

# PKCS#1 v1.5 Encryption Example 10.4
# ----------------------------------

# Message:
31 6c c4 45 0a 53 70 3c 05 8c 90 1f 50 7d 7a d0 
cb 63 96 c5 51 d4 f0 61 82 ab d3 a4 3a ac ba bb 
31 59 c0 26 b3 e7 db c1 60 f4 19 53 31 7e 0f 20 
80 8c 

# Seed:
c1 ec b1 16 4b 3e 66 23 f0 d9 b9 c5 eb f9 9e 78
8b 7b d9 4e b7 43 74 f6 1e d3 14 30 4c af 46 f8
4a 3f ea 1b a0 e4 7f c8 be 41 97 d1 d2 cd 41 41
cb 9a 61 5d 89 c4 bd 91 10 ca 6a c9 a5 60 f0 76
eb e6 9b 74 a1 0f e9 cc db 76 da bd

# Encryption:
06 16 b8 bc 77 2c 55 64 d4 51 b1 28 d9 b1 36 4c
9e 33 b6 e5 58 ec 67 f2 10 5c 98 00 11 7d 0b 73
cc ab 9d b5 1d 96 7a 9d 66 32 2c bc 5a 01 74 6e
47 31 dd 7d 04 e6 36 e6 4d 35 f2 c8 6c a3 9d 26
14 92 17 6b 8d ab db 13 4b e9 4b e5 1b 6c 02 3a
0d 55 fc c1 f0 4c 94 c8 6d 47 7d b4 03 b0 4f b6
c0 28 50 57 24 79 38 40 cb f4 68 aa aa 91 bc 54
e0 d6 47 7c e6 48 ce dd 12 76 f2 ad 2d 4d 42 3b
6c

# PKCS#1 v1.5 Encryption Example 10.5
# ----------------------------------

# Message:
d5 4e a3 7c bf bd b8 18 3b 3b 54 7f 6d f2 d5 d6 
3c 41 5b 1c 44 ed 63 93 c6 97 b4 d9 3a 97 fa 0d 
2f 4d 30 0a 68 e9 8f 7f d0 46 07 01 d1 57 9d 96 
83 61 2b 

# Seed:
d1 ce b0 c0 72 d6 88 8f a9 1a 75 7c bf 50 d6 69
76 bb b7 2e 61 94 62 61 4d 5d ec 0f c0 bb ca bb
07 07 89 c2 b0 89 50 91 b8 fa 9c 7e 75 ab 20 b8
97 58 f9 7f d0 47 bc 44 a5 68 a2 61 2d 0d 50 1d
15 d4 fb 82 e7 75 28 79 69 49 f3

# Encryption:
03 c1 38 c7 91 99 b0 b1 70 0b ab 2e 63 e1 d2 a0
03 1e f6 02 f7 dd f1 ed 3a 16 1a 5e 70 f1 64 51
da a1 01 f7 4c dd 65 47 a7 c6 52 c7 32 9b b1 73
7f b1 4c 1c c0 c0 c3 e7 61 2a e2 0e e0 21 ab 21
d7 0b f9 51 7b 4d 33 a9 d8 09 61 2e 7f 42 68 85
b7 9b 31 2e 26 6e 42 b2 02 b5 71 c6 6f 9a 10 7b
8f d7 c5 6c 05 0a 8e 1e b1 89 56 db 06 a0 20 9d
d1 6e f2 d9 05 24 db 87 91 7f 34 00 6b e6 b1 5a
13

# PKCS#1 v1.5 Encryption Example 10.6
# ----------------------------------

# Message:
f6 4b 25 11 cb e3 cc 65 81 

# Seed:
da ca 19 63 cb 3d e5 24 57 79 e9 64 77 ff da 27
7f 4b 92 3f a9 a8 d9 38 5d 52 31 69 23 3f 58 79
a3 c9 fc 7a 38 e1 b2 08 c3 2d e4 0b be 1d a0 77
47 1e 61 c8 b9 b7 09 3c 41 b0 a6 5c 99 64 32 d6
45 52 11 84 a6 6a fc b9 6f 07 a8 f8 ee bb 6e f8
17 e0 27 ee 2f 37 95 45 23 9e f6 f9 c1 d0 d8 60
e8 21 44 ec 71 59 7f 24 6a 83 cc f6 60 ea 4c 70
ee 1d f0 14 ef

# Encryption:
05 08 ed 84 5c 96 23 8e 3a 07 e8 ec 02 7f 7b 09
8a 83 f0 3e ac 1f ec 42 6f 4d 60 05 b6 0d 07 35
db 05 37 08 2f 23 b7 50 f6 db 54 88 21 41 81 e1
14 cf 1f 72 07 41 9c a2 93 75 0a a7 66 fa 7a 5b
9b e0 0e 37 29 2c 23 23 1c 6a b3 be 2d ed ee d3
21 b0 f9 cd 83 2c 5e db 41 67 7c 1a b9 83 e7 e3
eb c8 b5 19 93 82 1d 76 df 2a c2 29 8d 8b 80 ab
17 c3 8b 38 53 f1 8f e3 09 cc 7b f7 c4 a2 c2 7d
63

# PKCS#1 v1.5 Encryption Example 10.7
# ----------------------------------

# Message:
85 26 49 01 52 06 e2 a4 09 78 76 f8 ab b9 b8 46 

# Seed:
03 4f e3 4e 20 e6 06 a8 dc 7c bc d0 d6 ab 3e 07
97 c5 dc 4e d7 86 8e dc 79 59 89 3e 58 13 7d 26
32 b2 c5 a2 9a 81 35 c2 4a f6 99 b5 9d 68 10 3a
1f 42 33 93 e3 88 6e 3f a8 54 e3 97 21 50 19 41
3a f0 b0 d4 fb 1b d6 9b d4 93 4d 4d 1e 2e 9f 3b
9e 7c 46 d4 98 4f e9 e7 a3 7a b2 f1 c7 8b 0b 8d
9c d8 fa bb 3d 18 d4 c5 06 e2 fd 3f 85 ae

# Encryption:
01 6f bf 2a 7d 36 80 71 cc 7b e5 94 49 35 4b 9d
e0 5a 85 e1 bb 97 b2 51 4b 52 b8 d1 f3 e2 69 94
e1 2c fe da 59 e0 58 a2 f3 c8 79 23 50 c0 68 41
7f 99 44 1b fe 74 17 e0 b5 31 6e 16 3b a8 d1 f9
e3 2c 59 a4 44 98 82 f4 7a d9 b2 4f 68 76 d4 78
d3 f5 b7 d2 75 35 73 e3 f6 97 bf 64 c7 b3 ac 22
28 ce 69 e7 5e 8e 14 ab 93 f2 19 78 05 9a 14 3b
87 7c 6d d4 21 b6 e2 0b 07 35 a5 36 05 55 2a b0
a8

# PKCS#1 v1.5 Encryption Example 10.8
# ----------------------------------

# Message:
f4 fb 50 be ab ec ba 77 e7 1d d8 d1 6c 97 5a 86 
f6 19 ea 7d bf 41 96 9a 24 ba e2 b8 42 c2 69 e0 
b4 d6 a2 9e 82 9d be 2e 49 79 9c 9e d9 71 a3 03 
35 e6 ed 9b f3 9d 12 4b e0 f8 

# Seed:
9d bd 92 da ee b8 2b de 81 6c 59 3f 70 6b 15 92
56 85 22 bc a0 79 7f 9e 81 1d d9 dc 0e 89 6e 98
4a 4a 9a ce 77 de f5 a9 25 0d b7 95 81 af 33 fb
6d e2 42 7d 1a f6 a5 f6 92 9f a8 b6 7b ad 02 3f
04 b3 ff d2

# Encryption:
02 92 ed fb ac 38 e2 22 45 45 af 82 86 23 27 63
bc b1 8b ae 13 70 97 99 e3 50 77 58 d0 14 1a c2
97 6f 30 b8 54 67 d7 83 ab f6 fc 71 df 61 9e 1a
59 e6 23 4f 27 b6 32 fe aa bf d9 85 33 77 35 4d
93 3e f4 ec 59 94 c0 d5 0d 95 2e 0e ce 0b e1 be
4d 38 f1 1f 66 e7 c3 da 88 c8 3b 4b ed cc 06 2e
09 f9 af 95 ea ca c0 09 9c 52 5f 24 1e a7 d5 65
e1 b7 68 cb 97 08 f3 bc d5 91 77 b6 76 6a 37 34
88

# PKCS#1 v1.5 Encryption Example 10.9
# ----------------------------------

# Message:
76 91 3e 84 8f b7 b9 fa ca 91 ba f4 f4 42 01 06 
a9 4d 6c e2 5d 14 97 fc 4e 7f 85 96 54 79 5d 7f 
24 39 9f 3a 12 08 af 02 8e 61 67 8a 6d 19 a1 86 
40 b4 d5 0f 75 55 86 00 

# Seed:
9c ee aa 26 09 ba 90 eb 61 e4 f7 49 c4 cd a0 1a
ea 23 68 81 76 2d fc 15 0d 97 c1 1e b6 44 0c b0
57 e5 3c 3f d9 b3 9e 56 0a 46 e9 d3 fa 3a f3 fe
e5 ee 2d 02 fa 42 49 af 99 98 f5 3e d0 4d b0 10
e9 6d 8a 01 ff 6b

# Encryption:
01 96 30 89 c3 43 ed 88 b5 6d 6a cf ef bc 65 5a
37 cc fd 96 20 3f 21 87 64 69 5a d3 10 c0 b2 66
65 ee 74 5e b0 10 fc 83 be 8c 20 64 5c 43 47 dc
d9 44 65 5d 9f e6 cd 98 77 dc 70 e5 c1 88 cf c9
a6 6e 71 da 74 5c d3 e1 a7 b7 f5 a1 b2 5b 4f c1
47 0a df bf 7d 8e 45 fb 10 7c 5a eb 50 ea 8b 56
e0 4c a5 5b 9b 65 2a d8 34 d9 c9 65 77 b6 42 ef
33 f1 64 d4 93 1f b8 9e 0c 8c 6b f9 17 08 fd a6
db

# PKCS#1 v1.5 Encryption Example 10.10
# ----------------------------------

# Message:
c3 39 f8 57 e4 d0 23 1f a3 7c 06 f0 95 6b 4d 53 
a6 c5 8e 06 10 d8 da 43 17 dc 84 11 d3 a0 f8 98 
49 b9 4e 8d 7c cd b0 c7 d5 33 0c 25 

# Seed:
24 90 81 6a c9 69 76 ca 72 5e b4 98 e2 ff 04 0a
fe 3d 64 17 d3 2e 4f fb d9 fc 9e 3f a6 8c e8 49
c8 81 be 37 9f 17 50 4b 97 e1 e0 d2 2d 32 ff 8d
fe 76 bb 45 49 f7 13 b5 b8 d8 70 ed 36 59 dd d3
98 4b 6f b3 9f 6e bd dc 11 77 ad 69 8d 90 44 39
68 ab

# Encryption:
00 b5 fb 7b 1e 9d 71 df 8b 16 c6 3a 1a 49 6e 6f
7b f7 72 86 4c b4 11 55 2b 50 e0 b7 f1 5e 45 97
16 f5 64 62 43 68 76 33 a9 10 6c 34 6c 8c 6b 7c
fa c7 50 b4 56 6b 1b 88 aa c0 ac b9 16 c0 7a 78
0b e0 6d f7 97 5c cd 8b 72 60 78 68 7a 8e a5 d3
90 3e 04 ab 1d 23 dc 9b 1e d3 60 00 c9 bb c3 dc
81 61 e9 48 3a 18 ab b8 64 1e bb 1f dd 02 66 c0
84 bf 0b d0 9c c9 4e eb b9 28 3c 5a 5f 74 e3 60
13

# PKCS#1 v1.5 Encryption Example 10.11
# ----------------------------------

# Message:
47 58 65 a5 7b db 91 ad df 77 7c c9 d0 a1 7a 71 
a9 f9 71 0a 93 1b d0 c3 14 9b 23 91 a3 53 

# Seed:
56 2f 79 70 94 6a c4 cf 05 ed fb 26 34 16 7e 14
d6 65 8d 24 cd ae f7 5b 40 7c 90 04 81 8e fa 75
d1 64 5e 81 52 d1 fc 80 d6 99 df e9 9a 27 a7 df
99 7a 8a 66 47 5a af ce 41 95 98 e2 a1 91 99 fc
20 53 e3 ac dd 07 fe c8 ba 61 f2 b0 3e fc 7d ee
d8 15 cd a8 95 2e 21 e3 c0 b9 a9 35 2a b3 6f 5e

# Encryption:
03 e2 68 d7 d1 80 d0 3c 7c bc 85 07 ed 1f 83 0e
d3 7a 79 95 71 2d 7d 91 57 32 24 3d e6 85 d5 bd
99 a1 4a 8e 86 a6 7c dd 60 dc d9 0c 33 92 10 83
5a 46 ce ac 19 36 ab 3a a9 c3 81 88 2d 76 94 a8
38 3d 68 98 e2 73 44 bd 15 6f e9 28 2c 71 32 26
25 d6 8d 30 70 ef f0 1a c8 d5 95 f6 48 6d 79 b7
88 e3 69 12 fd 3c cf 28 4e dd 5f ce e2 40 9d ca
7d 4f 29 cc 18 2a 78 47 8b d3 ea 23 62 11 25 10
a5

# PKCS#1 v1.5 Encryption Example 10.12
# ----------------------------------

# Message:
9e 1e 53 f9 86 59 9d a8 98 d5 6d c1 c7 55 6f ef 
ca a3 39 5d 84 50 d5 2b 3b a7 

# Seed:
0b 17 63 cc 34 0e 6e 39 78 62 6a 06 d6 ef c4 0d
ed 73 db 53 5a 82 2e c0 4c 99 24 d9 ec 40 d3 85
20 15 d7 e1 02 9c 13 93 ad cf 01 50 30 8a cb 27
3c 36 35 68 28 a4 77 fc f9 a2 9a ca b9 09 35 70
42 50 26 da c1 4d a6 cd 30 4c da a5 4c 9a 4c 5a
99 4e d6 8a 5c ea c7 e0 81 de c4 64 6e 23 7e 47
1e 52 5e f8

# Encryption:
06 d5 19 a7 30 ea 5f 54 9f e1 9e 30 1b a5 15 2d
10 3a 3e ad 3f 89 ab 35 16 ff 7b 34 4c 4f 72 a1
c2 6a a9 0d 5a 01 a2 a6 51 93 d3 cf 63 41 e5 9a
31 fd 2d 7d fe 43 5c 09 84 d1 bb e8 11 32 01 0f
43 58 ee bf e8 3f af 24 1e 7f 35 af 98 b7 c7 ab
91 e4 f0 e8 a3 2a 2f 57 f0 7f 49 d5 c2 1f 1e 13
80 ba 0e 17 9a 38 d3 a2 ca 46 4f c1 4d 2b 74 a0
3a 88 84 aa 85 7b 66 01 47 02 b1 bc 4e 7c c5 e1
eb

# PKCS#1 v1.5 Encryption Example 10.13
# ----------------------------------

# Message:
1a e3 1e 0c be 44 97 ba 43 c5 d1 5f 53 5f e0 18 
84 1c 73 14 57 67 a6 a4 c8 

# Seed:
8f f0 fa a7 a2 b4 ff 55 3c cb b2 0f f3 10 ac 5e
0e a9 28 18 56 2e c9 a0 60 64 f5 de 79 86 b4 f6
c9 ae a2 b9 f0 11 42 38 e5 a4 99 a8 ae 20 ea e4
02 1f cd d8 f0 60 c9 93 a9 bf 64 2d a0 25 67 39
33 48 08 e3 88 e1 da 82 37 27 8b f4 f4 7e 05 01
5a 8b 88 c5 42 0c eb c8 bb 37 ee 43 52 83 7a f7
64 70 18 41 97

# Encryption:
04 a2 10 f7 6c 0f 84 93 99 09 53 58 98 86 f6 2c
1c 48 25 01 2a f4 4b dc f2 c9 9b 32 a7 0a 17 f7
64 a3 a9 7b 2b 04 7e 39 d8 0c de 15 4e be d5 d2
81 3e e8 4a c9 c8 d6 ec 6a 96 f4 02 db b3 26 d5
a6 e9 c0 f7 87 c1 5e 98 23 c5 08 b3 23 5f 3a 00
8d dd cd db 07 9e 80 ff 50 fe 37 25 4a 0c c4 68
29 7e e3 32 53 a7 4c 1e 03 70 26 05 69 58 ad 07
78 e0 aa 12 50 df 1c 14 85 7c b0 d7 1a a6 93 7e
31

# PKCS#1 v1.5 Encryption Example 10.14
# ----------------------------------

# Message:
a6 e3 d1 07 03 21 d8 ff 76 b8 5c 70 93 fa a0 42 
83 

# Seed:
d1 60 b1 2c 76 62 2c 3d 34 d8 55 87 e6 e8 1c f0
54 61 8a 34 6b 67 52 d5 36 9a 71 c4 92 35 7a 13
4b 7f 67 f3 34 a4 f3 b3 28 ba a9 f0 07 18 47 d0
da fd ac 22 5d 7b 7f 07 16 18 ea 86 81 fc d2 d2
30 5c 2f 64 63 1e 62 31 85 fc 09 96 e6 1c 84 f4
18 0c 63 7b f6 ea 2d 06 03 75 f1 6a 65 e5 b1 e2
b8 65 28 5d a3 d8 fb 1b 6a 60 ea 36 e1

# Encryption:
0a 70 03 b5 86 1a 85 a8 f7 2f 19 9d c6 5c 17 fc
58 a9 24 bc c4 08 19 e2 8c 95 83 e8 21 03 8c 00
05 36 5e 90 9e ee 4f 32 66 f5 9a 84 e3 25 b1 be
a9 d2 81 b4 82 3f 2f ce 44 15 3f 03 17 0f ad cb
16 a2 79 b2 c9 58 7f 6b 79 ef fc 55 a3 c4 34 e0
bc 5d af e9 d4 03 26 48 aa e7 e4 ed 1d 7f 14 1b
fe 72 26 83 fa 32 c8 9d dd 67 3c 3f 0c 03 c8 54
59 89 0a b7 fb c1 d9 a8 14 bf e9 b0 d1 2a b1 7f
51

# PKCS#1 v1.5 Encryption Example 10.15
# ----------------------------------

# Message:
d0 bc 89 03 9b a1 9e e5 0a 73 82 98 4e 42 8a ed 
88 61 4f 78 98 13 01 89 16 e2 e7 eb 0d b8 3d a2 
8e c2 3c 04 a0 c2 f7 6a 

# Seed:
c6 4d 10 a4 b5 2f 49 0a b6 2a 60 38 b3 2b 23 c6
3a 1c 85 97 0d 93 45 14 83 94 cd 35 cf d6 f6 b1
59 0f ce 8c 67 5b ee f8 7c d0 34 5e 5c ad e8 95
09 45 56 54 26 b6 b2 83 03 b0 b0 39 32 98 cd d9
5c 88 1f a0 65 33 ca 9d 2d 27 14 d0 f1 37 85 a6
1b 3a 17 a7 81 40

# Encryption:
00 8b 19 75 58 ca 4c e5 90 14 58 ab d8 e8 fd 52
d9 89 96 0f 01 c2 16 ef b5 81 f8 61 14 8d 1a 2b
44 eb 0b 7c 15 dd c3 45 dc 83 f9 03 7d db 8f e2
ea a2 cb ed 9b 1c 1e ee ae b8 65 bd 29 f4 4e e1
47 8c 95 e9 fb f4 44 82 8e ed cb c0 17 02 00 de
35 49 b2 2c 11 e4 b4 33 79 8b 9d 63 bb 49 f0 b0
2d af 56 58 ce ed ea a9 3c 0f ae b9 44 65 38 ba
92 eb 17 c8 5d b5 ee a2 04 ba 2f 49 c3 ce 65 bf
13

# PKCS#1 v1.5 Encryption Example 10.16
# ----------------------------------

# Message:
ce 0a 4a 59 39 f6 aa 26 3b bf 8e 1e 5e 94 31 03 
56 ab a5 75 59 c4 1b 39 44 27 70 b6 1f 6b 3c 9b 
5f e7 27 f3 19 52 28 08 d5 80 19 

# Seed:
27 cc e2 d4 3c 71 31 8e d4 fc 8c 0b db 9b 79 13
53 19 9f 3d 89 fc 12 ac 47 fd b6 d8 8b 44 c6 54
5e 9e ee d9 f4 c6 85 15 d4 f5 30 b8 13 7d 77 45
71 3b ad 0e bb 70 52 23 1f 6b 4c ed 18 7a ec d1
e7 a1 69 d8 6c bd 13 d5 0b 78 be 27 c6 54 5d 81
92 e0 ec

# Encryption:
0a d3 ae 7e 5a 98 65 2c 1b f3 45 c4 91 90 3f 55
17 c6 11 ac 31 00 05 87 7f a9 1a bc ee 64 85 f2
77 8a bf 0a 6c 73 87 87 37 63 9b 26 72 fd d0 0a
39 65 d7 d3 d8 d7 68 8e 77 b5 45 9e 14 5e ce 64
a9 a5 2c 37 97 f3 61 07 c9 36 8b df 79 90 f3 fb
6c 5c ff 59 f4 f8 a3 d4 90 93 df fe 74 06 c9 10
aa 57 23 22 8d ae 5a 9b 29 a4 cc 47 69 af 8b d5
fd 6e ce 88 58 a2 9d b1 b1 9c 08 1b f0 d6 b0 dc
78

# PKCS#1 v1.5 Encryption Example 10.17
# ----------------------------------

# Message:
03 bf b2 59 bd 92 46 de 3f 71 c3 13 9c 72 85 74 
b4 4d 97 ad 57 bb dd 67 01 94 95 c2 a2 00 41 39 
26 34 e3 1a ad 63 f6 3b 

# Seed:
5f a4 bb b7 0e 1f 9e cb 5e b2 14 7b f4 59 d1 d0
64 43 f0 25 c0 8e 36 4c cc 1b 16 0c 16 41 6f b1
74 b4 4b 95 36 38 a9 68 8c 5e a2 d0 60 17 1f 30
54 f3 ca 3f e0 13 b9 e3 18 8e e3 9d a9 1f 80 16
d4 b7 33 18 79 35 1f 32 2a e5 29 86 4d 90 74 09
8a a2 51 6b 44 ca

# Encryption:
01 78 e3 a0 17 ed 2f c1 18 bb 2e 03 28 be b2 8a
98 c8 53 b9 e5 a5 f8 30 80 c0 f1 01 7f dc a8 37
92 07 72 2c 25 73 7a 89 15 e0 eb 72 c5 22 85 e8
8f 61 30 a1 cf 56 ad 1d 96 ca 2c e4 b7 1e c3 68
94 7b d0 71 f8 36 59 10 c7 92 57 5b 8c d6 fd 27
e5 23 e8 59 65 dd a9 22 82 d5 df d1 c0 64 ea ab
6a be ac 65 e1 6a e0 4f 0d 40 b3 bb 68 41 0b 92
74 df ee a3 2a 43 f9 40 83 c1 07 80 35 3f 43 e4
3c

# PKCS#1 v1.5 Encryption Example 10.18
# ----------------------------------

# Message:
16 2c ca 9e d4 70 8d d1 03 40 e1 94 c3 5c 0a 5f 
a4 9a 5c 3b 5b 24 71 71 20 3c a4 8a 79 e5 2d ff 
40 f2 bc a9 9a 

# Seed:
4d 73 ff cd c2 69 43 a8 70 78 23 47 8d a3 1a ac
01 27 4b 17 17 c5 0c 6a d0 c6 2e e2 bc d5 57 95
2e f1 3f 15 ff af 66 bb 4f 45 39 99 11 c6 d6 5a
d1 87 57 2b c1 eb a0 d2 86 cc 45 aa 49 32 e7 3e
47 0c 5d c8 29 39 04 02 5c 86 3f bc 96 22 37 04
90 c9 1c 87 40 b0 94 d8 f0

# Encryption:
04 2b ce 0d fc ec a9 ff 05 a6 e3 f7 4a 6a 70 cd
cb e0 de c8 1e dd c8 8e 46 7f 47 bb 76 0d a4 4c
79 4d 3c dc ab 93 92 49 f8 49 d1 d4 39 56 64 1b
0b 79 c6 86 1c 5f 71 5b 9d 8d 8b e9 f0 76 6a db
3f 18 af 7d b3 6a d9 5f fc d7 e0 fa c1 62 85 23
e8 f6 9a de b7 ff 50 94 a2 b5 bf 92 b9 d0 ef b1
11 8b 85 39 26 25 fd 56 f8 8f aa 0a 16 d5 47 30
fb c5 ca f1 66 82 e9 e4 1b 7e 79 d2 02 2f 7c cf
96

# PKCS#1 v1.5 Encryption Example 10.19
# ----------------------------------

# Message:
8e 1b 52 cb 4e 00 77 27 c4 b5 f6 5d 78 47 f3 49 
16 89 1e f9 c7 3a d1 8c f2 71 f8 9f 18 2c 69 3e 
52 89 dc 31 da bc 34 cf 7f 44 fb de 57 9e 64 51 
69 ec ba 6e 4e 87 7f 7d ef 44 

# Seed:
ba 14 74 8e 6d a7 b6 ac 18 33 32 32 95 ad b5 42
2d bd 19 e2 ec 72 df d0 aa fd 7b d0 7b fb 97 e7
db 10 8a 3e aa dc ff 67 b6 7e c7 e4 c6 70 04 c1
78 34 6a 12 5d d3 b6 ca 55 23 70 59 4f 7f 42 cb
33 4b 44 85

# Encryption:
09 4b 40 30 e3 0c 8b 46 2e ca 00 f1 6b b1 70 19
d1 ea 31 25 f1 c6 4c 67 1c b0 0d 46 e3 ad 87 84
28 52 b2 01 49 6e e2 b3 d5 42 28 93 a8 0c 66 a3
0d a2 c9 f1 a3 2a d4 d4 e7 73 77 43 ca 1b 78 ef
7d 4a 63 c5 e1 e0 be 7e 51 88 cc e9 42 95 69 74
ff d4 81 e6 47 83 44 23 0c 37 da 5b 25 f6 b5 99
a5 9a 05 80 f1 a0 00 c2 59 54 f3 a4 63 f2 89 5f
32 b0 5c c0 88 21 b1 a0 23 60 9f 18 ca 7d 44 9c
b4

# PKCS#1 v1.5 Encryption Example 10.20
# ----------------------------------

# Message:
82 ff 62 af fa d8 00 30 94 cc 65 35 

# Seed:
1c 7f 21 fd 02 09 79 07 40 5e aa 77 ae 72 88 65
72 1d 93 86 f5 aa bc 2e 0a 95 f3 ed b0 5d 46 e2
33 79 3b a1 a3 88 e4 b0 dd a0 0c c3 2b 94 8a c2
65 ae 6a 2b f2 c3 90 a4 dc ce 11 0d 5e 86 64 47
28 f5 37 81 36 47 6c f7 1d c7 ba ac 50 cc 41 c2
01 48 dd 37 d5 f7 0a 81 2a 29 4e 24 2d e8 03 b4
8b 63 40 f4 28 d3 c9 bc a9 6f 76 6f c4 f4 41 67
e0 4a

# Encryption:
00 30 3c 35 2e 1f dd 2a f8 a8 e3 a1 69 b5 da c5
43 45 ca a3 35 a7 1a 37 e9 38 bf 98 47 08 86 56
84 18 ae 98 d0 aa df e7 9d 0c 65 1b 3f 51 6e 70
f1 01 74 fe 63 22 cd 37 6d a0 2f 22 6d 15 b3 6a
bf 6d 1c bd ab 6d d5 65 4d 99 25 f7 25 3c e9 91
51 2a 44 e9 fa 15 27 12 d7 b8 db 7f 18 33 75 80
fe 51 dc 32 a0 58 2c e2 60 53 f1 b9 49 28 69 b9
c4 7d f9 28 39 c0 50 2f 2d b4 cc 4d 0b 20 4f 6f
d1

# =============================================

# Example 11: A 1029-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
11 65 47 94 f4 64 9a 97 ac 87 ef 67 94 f6 a6 ff 
b5 cd ab 87 02 c2 32 54 fd e0 34 f3 12 9a ad 82 
5c f3 c0 cc 38 80 a9 6f b6 4e 48 d7 59 5e de 06 
c3 1d 0a cb d1 f8 ef 9c d1 f9 f6 f0 0b 24 ba 53 
45 ab a1 46 d4 1c 56 3b ae ce 3b 25 23 df 6a 9f 
43 01 8a 5f 08 69 b6 ec 99 34 69 88 6b 5d 23 17 
d5 9c ff d4 ed e9 46 6a 03 f6 df ec 17 5c ad 5a 
85 44 30 95 c7 30 b9 8b bf a0 48 9b 91 bb 27 39 
9d 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
11 65 47 94 f4 64 9a 97 ac 87 ef 67 94 f6 a6 ff 
b5 cd ab 87 02 c2 32 54 fd e0 34 f3 12 9a ad 82 
5c f3 c0 cc 38 80 a9 6f b6 4e 48 d7 59 5e de 06 
c3 1d 0a cb d1 f8 ef 9c d1 f9 f6 f0 0b 24 ba 53 
45 ab a1 46 d4 1c 56 3b ae ce 3b 25 23 df 6a 9f 
43 01 8a 5f 08 69 b6 ec 99 34 69 88 6b 5d 23 17 
d5 9c ff d4 ed e9 46 6a 03 f6 df ec 17 5c ad 5a 
85 44 30 95 c7 30 b9 8b bf a0 48 9b 91 bb 27 39 
9d 

# Public exponent: 
01 00 01 

# Exponent: 
1d 4c 4c 6e 13 da b2 84 6c e6 85 d0 c4 93 52 5b 
b0 ad 35 62 59 6d b9 ad 16 94 5d 44 5c e6 7c 54 
e9 38 f6 54 54 2b 09 34 48 02 91 ac 21 ae d0 98 
e8 5a d6 fe c6 d0 fe 15 4c 3c 34 2b 16 99 9a 8c 
ec e7 fa f9 90 b7 c8 ce 87 b6 6f e3 27 fb 35 2e 
d0 11 32 3d 2b 81 9a 36 ca be cc 5a ae 72 30 d3 
f8 e0 02 45 af 35 af 80 88 62 e5 4d 92 56 07 ee 
8b 58 e6 a9 f3 ad 8f a7 28 fc e2 68 56 c3 67 b1 

# Prime 1: 
04 41 0a b5 54 60 2d 7e fb 0c ce 92 71 a5 22 20 
f2 52 50 02 34 b3 82 02 fa fc 94 aa 26 b4 fb 04 
e5 f5 08 74 9a 44 ef b8 9b 75 78 db 00 7e 03 06 
23 89 48 69 12 80 f7 3c 12 7a 49 30 b4 63 39 ba 
d9 

# Prime 2: 
04 16 d3 fd ed 8f 9e 7b 01 9f 67 12 32 0e ec 11 
d8 cc 83 81 ea 86 26 6e e0 3f 00 72 e9 a2 fc dd 
80 81 ae 74 fe 27 88 70 f4 fd a2 a0 00 6c 4f 54 
23 9e 24 a1 5d 1a eb c6 7d 90 d8 18 4d 29 5f 52 
65 

# Prime exponent 1: 
04 06 7b 98 7b 5f 8a 8d 56 06 97 4d 11 06 1d 96 
29 55 63 e9 c2 6e 7d 3e 4e ba 43 fe 01 24 4d 46 
24 46 49 1a f4 82 f8 86 a9 6b 6d d1 64 d4 d8 0d 
ae 00 90 29 04 5e 4e 13 64 b4 9b 9e df 81 e8 b5 
79 

# Prime exponent 2: 
e1 38 72 e5 6d 84 d7 4a 1e 15 7a e0 b2 3d 30 09 
c8 d6 72 59 16 cb a6 07 31 06 d3 b2 58 92 b6 c5 
33 21 69 a2 54 b7 12 36 63 6b 5d a9 39 a4 47 a1 
f2 20 47 67 54 a7 6d 69 0e 84 af 95 ca 54 eb d1 

# Coefficient: 
02 51 37 ae 5d 25 fe 1d af 9b 38 32 f8 07 56 b8 
6d b5 ca 0e 37 2e 51 63 03 4e a3 39 1f 5e 54 58 
2a 1d d0 47 58 b9 92 a5 a8 e4 6e e1 6f 8a d3 8c 
a6 52 27 45 aa 7d f5 1d f8 9f c2 21 08 01 0e 00 
4e 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 11.1
# ----------------------------------

# Message:
4e a8 df da 3a 9c 26 70 ca 9b 80 fa 89 71 0a 

# Seed:
96 06 42 1b 97 3a b4 ba 2a e2 2f f5 38 70 7d 7d
4e 56 3e 8f 05 f2 0b c3 38 1f f4 5b 0c 29 19 a1
b3 46 b0 76 03 5a e7 74 b3 2a f8 ee 56 6c 73 e2
09 49 68 4e 44 74 80 df a3 4d bb 0c 22 66 61 50
90 9a 59 e1 bb 22 d0 95 ab e5 02 5a 76 d6 0e c1
01 c7 80 e8 3a 0f f1 59 46 5c 3e 7a c0 21 2b b3
8b 2f 1e 0e 6e 0b 54 d3 5f 4c 76 04 c2 d0 93

# Encryption:
09 bb cd 8b 63 b2 9c e9 52 26 ec 1d 51 91 2a f3
97 89 fc e5 e7 7e 7b e6 78 96 c2 c4 e4 c4 37 6f
a4 28 ec 5d 8a 49 7a cc a9 e0 82 1e 1f 6d e1 06
78 19 c4 8a 51 6f 06 91 cb ea cb b1 9d 1b a5 09
f0 4f c0 13 36 d9 0f 73 96 b4 3a a2 f2 b5 15 0c
28 a4 5f 35 a9 de 1a 29 73 ea 10 28 67 94 94 0a
b7 9f 12 9e 31 8c f9 7a 5f d7 2b 04 5b 46 6d 66
6e 5d e0 b7 14 f2 12 a0 b9 05 2a 0c 91 44 8e 52
0d

# PKCS#1 v1.5 Encryption Example 11.2
# ----------------------------------

# Message:
c6 e2 57 f3 a1 b3 5b bd c5 cf 42 0b d7 65 4c 52 
b2 52 df 32 c5 70 d4 28 e6 3e a2 

# Seed:
77 bb d7 2b 7b 8f d2 85 02 ec f7 9f d1 c8 af 8e
bf e2 44 32 70 93 b8 24 79 5e 95 c0 b4 36 eb 41
e5 f0 f2 ae c4 33 63 aa 2c 08 fc 33 f2 87 e6 53
80 40 e4 28 0d ba 47 23 e5 32 92 e0 a6 7b 21 ef
77 70 79 85 b7 2b 2e ca de 28 0a 61 fa 73 d7 3d
bb 2f db 17 f7 75 f4 5f a1 75 b7 7c b1 06 7d 6f
93 a4 37

# Encryption:
0d 21 49 5c 49 28 c2 6a 92 c1 6a 90 7f 08 33 35
ad e8 eb 0c 20 b0 b4 5f e0 e0 8e f3 9a e2 4e c0
b5 05 7e 60 78 dd 7a 1b 9d 10 2c 24 81 85 57 e9
0b 83 ff da 14 f3 cc 37 3c 84 c1 05 ce ee 71 bc
73 1a 7f 35 71 bd 7b bb a1 d4 f2 3f f4 df 0a 84
f3 12 99 0f f7 71 ff 11 8f 05 bc fa 22 2f 11 c1
ea 01 f6 a4 68 ca 5a 87 50 80 40 98 f8 46 a8 64
30 ea 23 e9 f0 7e 23 48 46 1e f0 0b a6 25 34 fc
e7

# PKCS#1 v1.5 Encryption Example 11.3
# ----------------------------------

# Message:
2d 30 7f 44 dd ff 9c 45 35 a0 6e da 01 4a 19 13 
04 fb 3f a8 e3 a2 be 88 bb 3b 7b e9 9f 94 d1 33 
9c 21 9a 51 ff 5c ad da 93 

# Seed:
5f ab 24 2d d9 f2 92 4b 20 d3 78 a8 87 b5 de 21
d1 95 76 9c 3b 53 71 c0 0f 4c 3f 1b 63 26 8b 01
0c 31 f3 2b b8 7c 9a 52 29 e0 d9 30 5f 5f c8 3b
89 34 b9 98 d8 ce df 91 6f 7a 4d 72 68 fe 3b e5
12 35 f8 be ac 80 0d 4f ff ca 6f fd db 29 da 4f
a1 c7 76 9d 51

# Encryption:
0f af b6 1c 37 16 c7 96 69 fc d1 64 52 46 a0 d3
b0 75 b2 8b 73 82 20 15 a8 ca dc 88 a2 2b 7d a5
39 43 e2 54 4d e3 96 be ee d3 b0 a9 89 ad 20 a7
3d dc d1 91 e8 aa 5c ae b6 e9 08 8a 4a 3a e8 40
67 f9 19 8e 92 4a 9c fe 14 50 b0 18 a6 b6 9d d2
37 58 e9 25 1c 76 cc ea e8 40 da 2c c6 25 17 39
e2 3b 9a 42 b6 34 46 ec a0 d6 15 99 a1 46 b7 41
fd 43 51 1c 73 66 3a 92 3e 37 57 f1 8a 17 1c 3b
12

# PKCS#1 v1.5 Encryption Example 11.4
# ----------------------------------

# Message:
1f 03 33 88 55 4b eb fa a0 1c 

# Seed:
9d 81 57 56 74 66 3b 3d 87 7d 4b 0a a4 fd c6 05
47 0a bc 55 0b 53 52 72 c8 23 70 18 86 7c 50 22
ca fe 6a 4a c8 37 37 e9 9c 6e ba fe 69 ca a7 fd
ca 70 27 c8 fb c4 37 ec 52 32 ce 2e 25 29 a0 f7
03 e2 01 f7 e5 10 32 35 ba 65 a6 47 94 f1 90 ef
42 54 93 b6 48 ed 24 a2 19 7d 0a b0 4d 81 c7 b3
35 a2 da fd 6b 59 c9 43 a0 9b 48 ea c3 5e 35 59
62 91 f5 f7

# Encryption:
00 1b df 33 93 34 2c 38 13 ee 3b 87 07 ea f5 46
47 ba de 4c 71 8a 4b 64 54 78 12 e0 10 c2 b7 46
c8 8c da fd c3 16 f0 36 9a 6c 43 0a e7 e6 c5 0f
05 d5 45 c3 f7 98 de b1 d9 a5 bb 69 c5 91 39 33
c2 d7 79 2f ae 9d 42 ad 76 49 4d c9 a3 e2 7c 21
1d b4 ef 19 85 18 7a 6c 4d 28 1c 47 72 17 74 a7
a1 1c 12 18 d4 dd 26 7b 57 48 58 77 aa 75 1f 6c
28 19 f8 1b 50 54 a2 8a 26 a5 3d f3 ea 34 82 b3
40

# PKCS#1 v1.5 Encryption Example 11.5
# ----------------------------------

# Message:
f7 1b c7 37 4b d5 9c 37 77 59 76 f3 35 24 4a 36 
84 3c 59 e7 48 9a d2 8b 1a 82 2d 2d 7d 0b 9a 6f 
e9 ac 5b f4 36 82 d6 3d 63 6e f6 

# Seed:
a1 ff 98 44 a7 3e c4 b3 be fb 1a 86 03 52 cf 9b
75 fc 66 ff 47 9a 2d ed 59 98 84 5a 79 77 3a 8c
62 06 a3 64 36 c8 80 a5 5e 18 71 18 35 45 6d 91
45 4b 5b a1 32 07 8f 20 37 fb da 72 86 25 1b ad
d1 2d 0a 97 81 af 3f 58 97 cd 94 7b 1e 14 25 f9
70 55 09

# Encryption:
09 3f b6 85 26 ce ad 01 0a 54 d1 16 cd 7d 60 35
09 9b f1 ab ff e9 33 1d e3 64 86 f0 53 a8 02 98
e4 ab fa b4 0d 3b 03 a3 e7 e9 25 59 73 38 f1 70
3b 04 53 5a 9c 87 c6 58 36 f1 61 11 aa e8 89 e6
c8 d9 0a 24 07 d4 41 a1 c2 a3 11 cb d9 46 11 a4
2e 93 c7 3a 3d 21 48 3f ad b8 f4 e2 46 d0 89 f1
cf d7 08 52 b4 dc 77 8a 60 d1 52 d3 fc a7 5f d6
06 67 04 e9 33 bf 99 f5 bb 77 af c9 a9 cb 3b 8c
b5

# PKCS#1 v1.5 Encryption Example 11.6
# ----------------------------------

# Message:
46 87 83 d4 ea c8 81 34 32 04 85 47 ce 24 1f 72 
db 1c 85 ce 4a db e3 ee 2c 

# Seed:
c6 e3 26 30 ed 90 d0 af b1 68 c0 8b 75 22 59 ef
4b 9e 81 17 62 f7 cf 4c 53 5c b4 a0 a0 4b 1d 43
65 4e d4 fa df c7 dd bc 3a ad 03 14 07 8b 22 6c
4f 3e 97 84 45 7c 91 c7 76 8c 5c 37 b7 60 08 93
2e 8d 04 57 85 05 73 ce 6b 41 a4 3c dd a9 70 03
18 36 84 0e 4e 60 a3 48 7b 47 a1 85 c8 6f 8a 16
f6 f2 21 d0 a0

# Encryption:
01 71 a1 2b 00 cf d1 09 67 4e 5b f7 f8 43 47 fd
a3 fe 4a 8e a2 f4 8e 0d 6b 6d 94 b4 9f d7 bd fb
26 e3 24 00 a7 12 51 ab 84 22 06 92 1d 83 72 3a
89 ea 09 93 00 25 92 0e 3e f8 a8 87 d2 bc 24 15
a7 f1 ed 37 ba 8a 5d 03 ef 92 6a ce f6 11 90 00
1c 5e a0 f8 cd 92 02 0c d8 96 67 e9 ea 5f 7f 2b
15 37 8a 21 0b 8a e9 14 81 90 98 da 1c be c9 c5
43 a2 63 30 0f 99 4f b0 b4 92 85 71 40 1c 20 2b
d6

# PKCS#1 v1.5 Encryption Example 11.7
# ----------------------------------

# Message:
af 63 1d 76 c9 7f d9 95 e4 94 aa 9b 4b d7 58 c5 
c6 72 c5 e4 15 8f 3a af 87 4b 

# Seed:
40 6e 1e 23 f9 92 bb 07 62 12 5c a4 63 bd 0f 2e
fd f1 bf cb 08 2a 8d f5 06 af 5f 72 70 c3 9f de
01 d9 ee d3 22 66 61 db 22 a9 c4 04 b7 d7 65 fa
38 4f 9a d4 f5 1b 93 69 d7 4b 0e 37 70 66 31 bc
65 36 f6 55 5e c7 fd eb d3 48 ef 3c f5 f8 a8 77
f6 06 43 7c 27 8c b8 16 3a d3 49 38 4b aa e3 2f
31 b6 86 e0

# Encryption:
01 ff 8f e7 9a f3 18 d7 56 f2 84 08 3b 51 b4 3b
66 aa e8 3c 6a a9 1a 99 93 4b 4d e8 4b d5 fd 24
fa 8d 07 c7 55 14 74 66 5e 62 36 0a 65 98 4e 67
a4 85 6c 3d bd 2c 75 f2 46 e2 22 22 e9 f4 b9 69
51 67 26 ed 28 7f 42 3a 67 47 82 1f bb b7 fa 17
62 35 c8 50 a8 61 f2 99 f7 39 4c 2c 43 07 b1 02
59 09 40 fe d1 20 6a d5 9b 9d d6 44 4e 1e 19 6e
94 73 25 22 4f fa ca 06 9e 9c df 8c 62 02 69 07
7d

# PKCS#1 v1.5 Encryption Example 11.8
# ----------------------------------

# Message:
73 cb 53 90 82 fb 06 dc ae 3c 20 68 e9 89 e7 c0 
d8 ff f0 fb 34 0b 6d e8 0d 0b a5 d1 e0 06 4f 22 
13 92 8a 4b af 20 a8 03 48 af 3c de 9d e6 3f 88 
6d 63 e5 6a 3e 32 cd 8e 

# Seed:
28 e2 a9 7c 7e 9e 03 3b 49 aa dc ee 8f dc 07 ed
fd fd b9 50 35 4b 70 8c e5 df 84 8d 1b 51 aa 2f
4a ff 99 74 16 01 81 42 29 47 b1 33 76 4c 5a 40
06 57 04 6a 49 36 3f fd f1 f9 3c a4 8b 3e 52 93
7e 1f 38 fb 50 fb

# Encryption:
0c 72 e6 95 48 c3 4e ca f2 48 b2 dc 6b b6 43 87
f4 f3 35 0f 66 8e 59 01 35 60 80 8c 41 3f a8 35
fd 36 0e 04 e4 74 7a 00 31 c8 a6 4a 9d 7a 07 b3
63 fa d2 93 b7 03 a7 dc 99 0f 80 6f b9 0e 39 12
21 a1 16 df 10 8f 54 6e ae 51 71 6b a0 45 01 ab
77 7b 0c 2a 17 71 2f 71 e4 06 27 5f 01 73 77 cf
24 88 c4 35 ef 6c 6e 7c 45 cd b9 8f 24 47 7c ed
18 0e b3 ef c8 70 3e 96 38 26 bf b3 44 f1 6e b4
a1

# PKCS#1 v1.5 Encryption Example 11.9
# ----------------------------------

# Message:
f2 f9 85 b8 03 12 73 cb 5f c8 9a 31 dd eb 4c 67 
a4 e4 f3 8c 09 d3 02 87 42 09 b3 9c 69 b7 1f 84 
95 88 86 8f a5 f8 

# Seed:
c1 20 44 6f 5b dd a0 6c 63 73 8f 18 15 55 95 f6
2b c2 65 67 28 4c 35 03 65 91 cd 5d 75 3e 4e f7
90 0d ff 33 bf dd 3b 10 8c 10 2d 08 98 80 c7 b6
9d 86 e9 ce 3d 68 8c da 15 6f d6 a9 92 31 05 8c
c3 18 33 96 38 09 46 e8 a9 69 a7 fc be 9e dc 95
9a 0e 50 45 32 ba b8 ea

# Encryption:
10 e0 bc 14 ba 16 01 26 98 cc 76 cb 82 04 5e 2b
fd bc b2 b1 18 f1 83 06 79 59 d7 13 7f d5 0f a8
8f e4 f9 ce cf 66 31 a9 9c cc ab 76 cd b7 74 4b
ab d0 6b 2b ed fb ca 77 24 da fd 91 e6 df a8 8b
ea 2b 44 a8 cb b0 62 19 b1 5c 2a e7 68 72 fa d2
88 e8 43 8a cd 39 5c e5 cb e2 8a 71 2b 67 f5 61
a1 78 6d 75 34 3e d9 ad 0d 0a 5e b6 fa ed 07 b0
6a ef 03 31 8f f1 af e4 72 db 4e e3 e2 1e c1 29
33

# PKCS#1 v1.5 Encryption Example 11.10
# ----------------------------------

# Message:
39 87 2c fd 6c f7 4b 4c cc 1a 70 d9 73 b3 18 99 
a6 7a ee de e5 d6 71 e0 5b d6 01 12 e6 45 12 bb 
e4 3b b8 40 

# Seed:
68 ac 3f 96 97 b7 50 75 4f a7 53 2e 41 61 c1 20
18 e0 33 a6 02 51 c8 dc a8 38 78 16 f4 23 79 ab
97 8e 15 57 8a e2 e9 4c 17 76 48 8b 0c fd ff 18
6f a6 d7 98 88 f8 16 9e e4 49 ea dd c8 e7 f5 a6
58 d0 99 7a 93 4f 58 6e 31 f7 47 30 be 60 3f 1e
e6 2f a6 c0 8b 0b ff ae 6b 88

# Encryption:
09 b7 70 07 f1 5d 65 9e fc ca ca 66 c1 e7 d9 62
e0 47 a1 e1 49 e5 2d cc 0e 1a dc 9e 18 3b f7 3b
5f 23 48 d3 43 28 24 1b 40 7f 61 82 2f 6d 57 e1
ab b3 22 d3 02 f4 53 0d 2c b9 a4 1a 27 70 23 8a
1b df 87 5e dd 79 78 10 d9 04 e9 7a 4d 7c 51 51
32 d6 ab bf 3a 4a 40 74 86 dd 00 4e a3 8a ff 8d
4e d3 82 5f e1 31 42 f1 36 fd 1d 71 3e 80 e0 cf
22 57 69 b4 19 cc a5 4c 15 6e 54 66 8b 30 6b 5f
2a

# PKCS#1 v1.5 Encryption Example 11.11
# ----------------------------------

# Message:
74 d5 6b f8 d9 c1 80 dc 09 93 71 a5 af 72 

# Seed:
33 78 70 f0 47 9c f1 28 3a 0c 87 c9 c4 af 54 ba
8f 85 06 44 d5 9a 20 25 26 3d 2b dc 49 bf a6 63
6e 75 18 f9 4b 6a b1 8f 85 b1 93 21 20 9b 76 9f
0c 19 75 d1 d5 ad a0 6f d2 a7 6c 82 45 0e 4e 09
cd ad b7 83 2f fb 8a e7 dc b4 74 10 b2 87 80 4d
dd c7 49 3d 61 0a 81 39 9b 6d f6 df 5e f1 52 09
29 84 fe 27 76 a4 f9 30 54 6b e1 dc 18 31 3c 14

# Encryption:
07 61 eb aa d8 ff 1c 3d be 71 0f 60 e3 be 9f 28
9f b2 7a 6b 53 77 75 5b 71 fb 38 4c 5f ac b1 60
3c 95 3e 1e 2c a1 1e 78 43 25 ae 42 f3 21 ae 5c
58 64 8c 84 f5 24 df 9d e9 f9 3f b4 b0 c2 e0 97
97 ff 2d 11 40 70 73 b9 5a 78 6d f5 1a 43 f7 99
82 d8 6c 49 fb 9e 50 14 b1 b7 68 76 0a 51 30 26
6d 06 99 30 6a 90 4e d2 df e2 01 38 d5 31 c5 dc
4b bf 4d cc f1 02 49 a6 e2 b3 55 f7 cc b3 26 a4
8a

# PKCS#1 v1.5 Encryption Example 11.12
# ----------------------------------

# Message:
61 10 63 b5 da 12 3c e2 12 96 17 df 38 59 95 57 
b9 5d 1b 05 e6 b6 6b cd 49 af e9 83 1a 04 21 a5 
be 4e 48 

# Seed:
10 f9 df 30 ec 97 77 fc ab 5a 92 4d ed 36 fd fd
6e 1f 38 14 49 ad 99 d2 0a ea 0e 39 72 ea 60 4e
a2 27 50 d0 60 1d 10 a3 77 da d1 a9 4f 9b 02 73
40 94 81 23 82 73 98 bb 22 b1 44 5f 71 c5 05 c6
23 aa f5 16 cd 9f b3 e9 77 f7 78 cf dd 3a 5d 28
c2 29 9e 4b 2a bd 9f 98 c4 35 5a

# Encryption:
07 f2 d5 8b c4 16 39 94 d7 6f 49 1e dd 69 74 3c
45 dd a0 c3 8c cb 07 69 de 9c f9 f4 fd 00 55 d3
0a 0c f0 02 80 0d 76 ed 8c 12 cb d3 6a f0 51 a9
d7 33 7b 29 bc 77 4d c3 c4 01 2b f5 c2 8a ea d8
c3 e0 36 aa 41 39 8a 8b 0f e9 91 c0 bf 66 b5 34
1c 99 d9 37 7d 94 70 4a d4 90 a9 f8 74 6f c5 ce
f7 26 e1 96 f3 41 f9 3a 1f 1e ae 2c 13 e0 0c fd
22 06 2f 8e b3 da 9d af b9 5a 1e 7b 81 b1 fd b6
56

# PKCS#1 v1.5 Encryption Example 11.13
# ----------------------------------

# Message:
80 76 4f 78 5f d4 17 6e 16 41 e1 29 a3 5a 9b 31 
b3 a8 9a 75 67 ad 6c 1f 0d 65 ec 8a f9 5f c1 6e 
15 28 14 09 

# Seed:
20 36 d0 98 a6 e9 35 f9 a4 11 20 1d 2b cb 62 9f
79 0a 94 db 2e c6 98 67 43 3b 17 61 d7 c6 95 4b
e9 1a 9f c7 19 19 0e 10 86 13 cd 58 4c bb 97 76
87 04 69 24 bc a6 b2 fe 1a 54 bf 76 ac f7 7b 36
8c 39 65 0f 6d 0a 49 8d bd ed ae 3f 4c 21 04 0a
8a ed 63 4d e4 ed 8a f1 34 6e

# Encryption:
04 9c 61 44 4e 92 47 72 f9 4a 79 5c cd 99 eb 2f
e4 30 99 7b 91 b4 2d e6 16 36 29 ab 98 d2 5a 71
e7 f9 68 86 a5 7e 97 9d 9c 94 c9 62 20 9c 1f 71
2c 70 57 1a 81 f3 77 ea f7 4e 80 e7 07 22 e1 be
3d 13 37 c5 04 5f 79 7b d5 7d f2 f5 ae 5e f3 3a
e5 79 e9 3b 38 fb 25 0d f0 c2 bc 59 b3 3a 74 86
7b 8f 3d fe 5b a7 85 d7 28 b8 9d 96 b3 00 2b c0
05 4d b5 bb 0d 84 fd a4 5d b4 a1 f2 62 8a b1 12
30

# PKCS#1 v1.5 Encryption Example 11.14
# ----------------------------------

# Message:
0a bc 2b c5 fc d0 40 18 9f 84 22 f1 ca 04 50 21 
da 95 

# Seed:
e4 73 2e 49 90 69 9f d7 47 40 c8 52 ae 8d 4d 70
7f bd 79 46 0f 88 74 09 84 ae 53 ff b9 fc 39 62
68 3e ad 0d 14 04 f5 31 51 d1 ae e8 0f cd 6a 1f
f6 5f c8 8e f0 8f d7 6d 9f dc a8 f9 e3 ac 7d 8d
82 b8 2e ce 78 9c 66 c5 40 22 80 b3 e5 68 10 1c
e2 a2 a7 b2 b2 f1 e9 65 19 cf b4 1e 60 49 3d 76
8e b5 b9 6c cc 49 bb 0f 6e c7 11 fd

# Encryption:
03 d8 ae 60 4f 92 95 31 73 c7 7a 01 ff a0 90 aa
0e 37 a3 8a 47 c9 72 19 c0 b9 f8 64 d3 48 74 6a
f7 f4 a6 32 11 29 c6 04 6a 99 4c ed 1c cf 33 24
da 93 71 53 88 8d d6 c6 70 19 a7 ca a7 65 5a 36
42 83 8e df a0 e2 ed 8d c2 5c 14 bf f8 bf f5 65
c7 18 f8 b6 c9 20 56 c9 bb e8 d9 30 83 70 c7 cd
75 a0 4e 11 b6 e2 5a a6 c3 c2 cd dd 17 2a 4b 6a
ee f0 4f b8 35 b6 86 37 cf 0c a0 b9 a9 11 b8 b8
74

# PKCS#1 v1.5 Encryption Example 11.15
# ----------------------------------

# Message:
8f 98 35 ef b6 9c ca 8c 07 bf ef 4d 8f 53 5d 0c 
bd a5 36 7b bd 41 08 0a 

# Seed:
32 d4 be 07 e6 c7 fb 81 d5 20 8c 25 01 c5 df 7c
56 d1 98 6d c6 d6 31 10 ad 21 d8 1e 57 ce 11 3f
3d bb fe be 0f 80 c0 16 b7 19 e9 d5 c3 d9 a3 bb
bb 2f 35 be 95 d4 56 22 2b 51 c3 d6 5b 38 8e 7a
da f6 b9 ce ae 1f d4 6a 6e 05 ca 1b b1 99 c2 7b
ac dc 8b 5f d1 4f 03 51 ae b3 fd d6 dc 1d 93 f3
b5 31 56 ef a3 c6

# Encryption:
0e 47 ad 4d 92 d1 9a 1e bc ac bf 87 5d 80 19 27
d4 fe 5a fa f6 6e d8 01 5c 55 9b 56 6a 9f 3c bf
0a be 8a 76 fe 73 24 f6 28 c2 e4 f3 45 84 a5 0f
f7 7e 82 2a 54 11 8e fa 9b ae 9d 0f a5 02 94 c6
18 0b af 3a 8b 0c 7e 45 3a 74 37 ab 1a 19 cc 00
30 7a 8c 6a ed 95 c3 15 b2 4b 47 90 07 24 56 c9
44 60 99 5d bb 1f e5 a1 2b 4c f4 45 42 96 f7 40
02 83 ce fd ce 6b 00 cc 80 49 dd 5d d8 cb 2a f3
6f

# PKCS#1 v1.5 Encryption Example 11.16
# ----------------------------------

# Message:
6d fb d9 3b 00 78 d4 9a e4 fe 1e 24 cc a9 7d 0a 
9a ff d7 be e0 62 ae d2 9d ef 0b 1c 0b 3a ef ad 
81 1d 6e 7a ce 8b 49 d7 24 2a 9f e6 e2 3c 22 

# Seed:
31 9f 9c da 2c 93 38 8e be 1a 50 e7 6c 93 97 55
9d ca f1 4f e1 03 52 aa 51 1d c5 ba a6 4d c1 52
fc c7 9c bb 23 d4 e6 9b 12 b9 f2 7a 79 09 15 98
87 bb 04 12 9a d6 35 16 81 33 86 37 4f 31 89 2d
4c dc 4f e6 39 69 b5 bf dd c6 67 f9 46 89 7d

# Encryption:
01 c5 ce 83 6b e2 20 8a 3d 81 4e 7e 60 c2 76 74
ac b7 cd 3e 31 c0 24 d9 d3 8f c2 29 53 aa fe 73
af 52 40 43 4d cf 54 a3 88 99 2e ac 36 ec 84 64
d9 a0 42 ac 58 d1 8a 70 39 8b 8a 77 3e 66 69 bb
3d 76 ee ac df 1f d1 52 47 40 99 bf a6 62 a4 81
db ab 4c a4 67 14 95 87 45 e2 b7 83 2a 59 cc b0
05 36 49 b7 e0 95 07 43 33 3f 5f cd 6f 65 19 7d
dc b4 e1 bc 12 a6 6e 8e 92 a8 65 9f ae e5 71 31
e2

# PKCS#1 v1.5 Encryption Example 11.17
# ----------------------------------

# Message:
e5 87 9f fc e0 b6 29 b8 85 7c 19 5c f5 d0 9f 7b 
93 bf f1 f7 a9 f2 d8 a4 5a 56 3b bb e9 e6 2d 

# Seed:
ba ce 2c ea 0b fe c2 5a 2d 34 d7 29 92 c2 b8 ea
0a ea 17 b7 a3 a8 be d6 0d 1b b5 10 13 34 7b 2d
03 6a 75 a4 ec 3e b2 c1 78 8d 44 a9 e1 c5 c8 8d
04 1e 82 af 87 81 55 dd c7 d8 1b 3e 27 cd dd 20
40 9d bd da 4a 64 bf 83 11 a7 b7 eb 77 29 93 12
66 1a 6e 37 df 35 02 f8 6a 22 49 2b ef ac f4

# Encryption:
01 e4 fa eb ca b8 9d 7b aa 3e 03 93 f7 16 84 b0
ae 53 df 8e b9 87 3e 65 a7 16 ec 2f 41 74 1f 8b
78 16 d2 e1 97 d9 76 fd 53 a8 ee 7f 92 4b f4 bf
d4 10 42 e1 64 45 e9 06 0b 55 a0 b6 dc 16 aa f3
06 44 91 d1 89 28 22 39 50 39 33 28 c1 47 db d0
35 31 ec 01 2d 8c 52 75 02 e7 eb 3d ca 50 9b 7d
e1 69 95 92 46 07 c8 b2 8a 2b da 9b cb 2c 77 81
46 1c 76 66 3b 88 7b 96 43 e2 31 7f 0e a1 d1 bb
14

# PKCS#1 v1.5 Encryption Example 11.18
# ----------------------------------

# Message:
0a 2e 24 13 0e 8a 9d 28 df cb 9d f9 76 5f 46 83 
e9 da 78 42 5a 28 19 98 06 a9 3b 32 2e fa 88 49 
3a c3 72 52 c2 9a 26 4f 3e 85 ae 56 53 8e 80 8d 
c5 56 42 a4 88 5f 05 46 40 f6 9c 89 81 fe 

# Seed:
20 43 1e f3 1f ce 19 93 9d a5 45 a0 85 30 11 2d
b0 fa 07 13 8d d8 6d b1 cc 65 e2 b0 3f c2 be 60
7c 3e 60 38 eb b7 89 17 55 b2 31 29 fb 96 9a 7f
e1 06 10 f2 eb c3 f0 77 b2 c8 f4 60 1e 09 ab 4c

# Encryption:
0c 14 b7 d3 2d 3d 4c e2 b0 8e e4 4f 51 6a ee 29
90 b6 30 52 40 b2 5d 23 34 aa 31 75 2a ef 28 02
16 39 f7 6a 4c 47 19 56 9b 30 05 2d 4b d0 8b 3a
07 f2 3b e6 86 23 7e 48 1e 67 bf 3f 5a 01 ad d7
56 da 77 2c 7d c1 3d 32 29 8b 9b bc 3d 33 e6 df
82 d8 5c 08 9d 34 76 00 49 7a 8b 8e a4 de 68 ab
b9 0e 5c 6a eb 26 9a 97 be 42 6c ef ac de bb cc
0c 1f 2c 40 9b bc 7c 72 d9 0b db 42 6b 13 cc c1
9a

# PKCS#1 v1.5 Encryption Example 11.19
# ----------------------------------

# Message:
7e fc 62 7b a5 fa 28 25 aa e0 ca 94 03 0e 70 47 
08 d3 5f 92 23 98 26 b4 2e 2d 4d bb e4 02 ab 7d 
19 6a 7f 54 88 0c f2 c5 a4 d0 fc e5 3a 20 a3 2b 
68 30 e6 2d cd 00 db cb f3 3b 5c 0c 70 44 01 

# Seed:
f0 49 79 19 c1 42 f3 a9 8e f5 5b cd 59 88 23 4f
dc 8a eb f7 36 d4 7a f9 70 90 a7 a9 dd e0 a7 35
09 f9 cd 41 36 26 bb 8b a7 67 c9 d6 38 49 1c 28
6e 67 bf 22 d6 70 d5 6b 24 c1 5b ad 70 35 1e

# Encryption:
01 db ce 3b 3f 84 b2 da 06 b1 67 e2 06 64 9d 42
4a 42 b8 e9 ea 54 53 a1 6b 5f c6 c2 e9 cb 17 ed
a1 ef fe 4e 78 36 a5 e5 8f 99 e5 31 53 0b 40 17
1e 4b 51 fc 0b 92 de 30 31 30 09 36 d2 59 5e 39
10 09 e2 e5 3c 32 f7 59 60 4a 6d ba d9 c9 70 90
0f a6 e4 1a 35 08 3f 78 7b 9b f3 be bc ea a1 a7
71 84 1b 5e 6e 4c 8b 50 96 29 00 7b 46 7e 3c ec
8a 1d 03 23 c3 c5 db c3 4d 8d 41 25 a3 98 c9 d5
3d

# PKCS#1 v1.5 Encryption Example 11.20
# ----------------------------------

# Message:
59 3d 3f cd 05 ac ee 30 29 81 5e 1e 76 a8 90 

# Seed:
12 35 ee 3e 7a 9d f5 96 7f e9 8d 97 10 ff dd 5f
7e b2 2d c0 71 47 af 43 6f e2 0a a5 26 bf 0b 94
19 0c ab b5 21 3d e9 8a 23 f5 ef 27 50 22 a2 f7
3e 60 e9 ef e2 c0 34 c5 5c eb 26 aa 80 6c de d6
73 9d db 2c bd 3e c3 b5 55 20 4a 79 84 65 c3 7c
67 57 19 56 86 a3 ea 3c 56 57 c3 60 a0 15 8d 99
2d 4f eb fa 04 29 ee b7 c9 2a 46 84 34 c5 b7

# Encryption:
0f 69 98 81 a1 52 46 18 bc 25 d4 e5 14 e2 07 30
68 ea 7d 35 38 4b af d4 6f c2 82 e1 d8 55 11 9e
e9 69 f2 11 c7 18 4a 07 03 06 47 fc 40 99 0e cf
2e a4 05 22 86 5d 91 77 8a 62 7e ca 8e 50 c2 bb
97 60 b0 45 da af 12 77 a4 fa 98 35 76 ca 8c fa
d7 60 83 29 c1 88 15 88 01 7d 63 72 2b 70 e9 8b
e5 24 e0 03 39 95 98 25 73 d3 38 70 78 c8 b7 c1
c5 f9 ae f2 64 a0 48 46 84 b3 42 66 43 73 7d 34
bb

# =============================================

# Example 12: A 1030-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
26 1f e0 28 44 59 c2 fa 6f 05 54 6b ed 58 5e 1e 
e0 a1 30 b7 1c 2b 8a 6f bb 3b dc 75 78 7b 26 55 
d0 ed 4e 32 5b 54 c7 b3 71 a6 fc f2 b6 78 82 77 
a5 0d 47 05 ba 23 c5 96 28 5d a7 e3 c9 30 4a 41 
e7 cc 48 8b 44 92 2f 7b e2 b4 7c 16 31 9e 33 74 
51 17 3d 40 b1 ea 48 1d 1a 9c 11 29 b1 fe b7 d0 
9f 67 49 7a eb 98 94 8f 1a bf 3b 77 86 bd 3b 87 
04 71 87 c8 f3 70 15 68 2b 3f 2d e5 0e 07 8e 8d 
0f 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
26 1f e0 28 44 59 c2 fa 6f 05 54 6b ed 58 5e 1e 
e0 a1 30 b7 1c 2b 8a 6f bb 3b dc 75 78 7b 26 55 
d0 ed 4e 32 5b 54 c7 b3 71 a6 fc f2 b6 78 82 77 
a5 0d 47 05 ba 23 c5 96 28 5d a7 e3 c9 30 4a 41 
e7 cc 48 8b 44 92 2f 7b e2 b4 7c 16 31 9e 33 74 
51 17 3d 40 b1 ea 48 1d 1a 9c 11 29 b1 fe b7 d0 
9f 67 49 7a eb 98 94 8f 1a bf 3b 77 86 bd 3b 87 
04 71 87 c8 f3 70 15 68 2b 3f 2d e5 0e 07 8e 8d 
0f 

# Public exponent: 
01 00 01 

# Exponent: 
05 df 76 83 72 cc 0a 64 d3 c2 14 18 30 24 23 13 
9f 47 95 73 e5 0b 5c 09 b6 e3 be 23 fb c9 aa 1a 
76 d3 27 99 a0 47 76 1f fc 21 07 94 48 17 01 04 
cc a5 e2 a1 4c e4 57 d0 0d 80 7d 42 c7 6a 55 f6 
16 87 4b a7 f7 ea a1 ce 63 cb f1 32 de b0 81 aa 
d2 fd 80 d1 24 c4 da 86 ec 6c 02 0e 8c a8 2d cd 
cd 35 54 e6 9b b1 98 72 26 2a 50 31 ab 5d b7 cc 
8c 92 59 a1 46 d5 8b 1d b9 4c c7 e7 56 25 3d 5a 
a1 

# Prime 1: 
06 77 25 ab 55 34 18 e1 eb 8a 41 f4 fd 92 d2 80 
b6 9f 85 b0 8c b4 02 f0 1a a4 96 92 c7 20 9b 36 
42 98 ee f4 e3 3d c4 22 bb a3 35 03 d1 1a 12 7d 
d2 86 43 f7 e7 75 58 97 e2 eb 96 c4 dd bb 91 12 
f1 

# Prime 2: 
05 e5 8c eb d4 7d 87 b1 e7 8d a2 fe ee 6d c5 f9 
bf a2 20 c3 55 e8 20 c8 fb aa 88 46 df 11 00 36 
2c d1 69 1f 5c bf 5c 78 68 c4 72 d0 28 2a be 01 
03 16 1f 4d 8c 62 af f3 5b b1 fb 7c c6 99 d9 9f 
ff 

# Prime exponent 1: 
01 6c c9 14 95 d6 c1 95 29 40 73 80 f7 52 20 aa 
d5 95 1a f5 ea 4c d2 48 f0 d6 4d 89 53 f1 cf c3 
89 b2 03 18 5d ed 03 09 54 a9 87 c9 ab 90 3f 7b 
13 a7 1b db cb 5b 85 87 14 30 30 2e 7b 60 17 7d 
41 

# Prime exponent 2: 
f9 cc 89 15 ff b3 dd c7 c0 93 71 76 49 2a 12 bb 
18 7c 2d 76 e0 d3 d4 0e 79 58 d5 c9 82 09 b3 ed 
54 b5 fd 9f 9e 77 11 e1 dc 68 57 73 de 26 d5 0a 
0a b6 21 62 12 ce a5 09 ce 79 1c 5b cd 07 cb 47 

# Coefficient: 
05 fe 93 3b 87 3f 1d 68 f6 be 2d 4d 5a a2 2d 96 
8f 2b 7a f5 41 a2 dc d6 4e 09 c0 30 b1 50 35 8a 
98 02 ae 86 59 95 d7 6a 50 03 7d 19 8d 7e 70 4d 
26 84 7a da 8a e3 d3 85 c2 96 5f b1 7f cf 15 2f 
77 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# ----------------------------------

# PKCS#1 v1.5 Encryption Example 12.1
# Message:
7d e6 9c d9 22 8b bc fb 9a 8c a8 c6 c3 ef af 05 
6f e4 a7 f4 

# Seed:
33 d6 2c d6 67 82 3f bf 13 d5 92 ae 4d 02 a2 37
0d 1d 99 db 06 c7 25 42 5e 0d 12 fc b4 83 4e f9
e5 49 9d 60 7e 8a ae fe ba 81 96 49 fb 3d 61 c7
05 f5 e9 a3 a2 f8 96 27 61 89 a3 20 0d 2f af f7
76 79 e0 56 34 9a 5b 9b 7b 44 49 b6 75 cd 48 b6
98 09 32 c2 cf c4 6b f8 9a 77 34 f6 8d d9 f4 fe
77 e1 d9 cf 1f 31 b2 1c 4c 61

# Encryption:
04 ca ef fc d5 1c 3f c9 23 63 46 77 4d a0 cf a7
7e 9e 64 65 f6 43 7f f4 6d 9f a4 58 b3 62 34 12
c3 10 30 09 fb fe 20 31 96 df 72 96 26 e0 ee 3a
fb 6b 10 a5 ac d7 2e 84 28 1d 9d 9b cb a3 e0 ef
77 dd 84 f3 db 19 2d 31 b5 b6 66 f7 6c 93 81 06
81 37 3b aa 58 e6 da db 01 fa 5c 65 ec 89 fa 51
cc 24 74 61 1b 9a 7c b0 0e 86 2f d3 d4 9b 1c d3
1a fc 2d b4 49 e0 9d ae 2d 0a 7d 4d f0 bc 32 0b
5a

# PKCS#1 v1.5 Encryption Example 12.2
# ----------------------------------

# Message:
97 ee a8 56 a9 bd bc 71 4e b3 ac 22 f6 eb 32 71 
96 69 c4 2f 94 30 c5 89 50 c6 4c 0d ab ff 3a 9e 
20 43 41 6c 67 ca aa ab 7c 68 cc b3 ca 99 a3 

# Seed:
9f 14 12 61 ce c4 f2 c5 2f 96 91 25 a3 6f 14 10
27 08 82 50 d3 6b 17 42 1c d0 96 14 76 19 06 46
8a fa b7 62 2c 0d 02 19 36 91 74 47 91 e0 d3 5b
6b c9 f3 37 7e 10 b2 85 6c 8e d9 19 9c 89 f4 a4
16 13 d3 c4 0c ca 37 3a 7c c6 3c 52 60 fe 5a

# Encryption:
0d 26 a0 5d e9 3b 70 7b 85 40 fd c1 98 89 d2 d1
e7 93 71 57 d3 2d 30 3c 52 8d e3 5e 55 3f 94 20
28 74 4a f6 a0 40 2e ca 0f cf 5a 85 26 1a d4 75
d8 71 0c c9 f8 b1 1b a2 c6 da f1 d6 72 69 0c 68
ed 11 e0 35 e9 c6 60 ec e1 d8 0c da b8 00 ea d3
c6 e0 78 61 7a 1b 0d 27 3d ed d8 d6 57 49 16 6b
d0 77 74 fb 4c 14 86 aa 8a 0a df 59 5d bc 3d 10
ff ae f1 83 84 98 a6 75 55 c7 7b 6e d9 83 d5 b9
de

# PKCS#1 v1.5 Encryption Example 12.3
# ----------------------------------

# Message:
8e 3e 77 2f 39 

# Seed:
9e b7 31 fa 6d 8d 5b 75 81 f8 fa d2 c8 22 5b c9
68 34 af 61 db 3d 40 9d d5 63 04 ae 23 ea 62 69
63 a4 d8 04 40 c2 4e 43 1e 41 97 60 90 3a c4 4b
fe 41 a7 50 81 a5 46 2b ae 65 47 c0 e7 a0 6e 91
60 df 9c 01 fa 6c 53 54 c8 33 18 db 65 6d ee 0a
43 77 87 fa 46 39 4e 55 2e a5 33 15 59 b4 01 7b
b6 a0 e1 d6 fc 8a 65 b4 5d a0 c4 5d 88 95 48 61
cd 6e 7a 41 7e 03 7b 1b 05

# Encryption:
24 4a 86 34 51 9a f4 9f 56 9b 69 86 ab 47 79 64
a6 b2 92 0d 84 3a 1d 97 ef d7 fe f8 3e 81 ba da
4c 4b 56 29 61 ef 4e 1f c3 33 46 4b 92 6b d7 4b
07 ad 50 c6 5b 68 16 83 d3 89 fe 41 d6 d2 13 b6
46 9f 18 2b 14 b4 62 d7 2c 1c e3 92 8c a8 06 d9
66 b5 2d 42 d0 bf d6 0c 9d 04 91 4d 50 83 7c da
e0 9b 33 0e 37 27 44 dc e1 7f 18 e9 4d 71 1c 8b
58 ea 44 9f 14 49 d3 69 fa ef 51 46 83 d3 01 60
79

# PKCS#1 v1.5 Encryption Example 12.4
# ----------------------------------

# Message:
dc fa e7 71 8c 24 7c 40 f9 a2 a3 c3 53 5c 50 92 
80 c8 73 c3 

# Seed:
b9 3b be a6 c1 85 3f 15 3b 5e 01 e7 e4 e5 d0 c6
3d 9d fb 24 5f c6 cf 64 04 3d 7a 92 20 b0 b8 1a
c2 af 65 6b 99 71 4b a4 30 e0 a3 96 95 d2 5f f2
69 b0 b9 b8 65 fc 4d 4e ee 5e 07 a5 b5 be 35 43
82 aa a4 14 bc 62 08 54 5c 86 ce 02 38 8c 07 b3
76 fb 02 98 c3 7d 1a c3 9e a1 89 b0 ad f7 80 f6
e8 30 bc e9 17 b5 0a db 7a 31

# Encryption:
0c 41 20 52 d4 ef 4a b5 1b 2f 62 37 05 f0 7f 41
fa d6 4d af fd ba 62 44 ef d4 7f 51 9d e2 e7 1a
01 a6 c5 7d 1f 28 b6 bf 7b 5c 8d bb 9f e7 b1 49
b0 eb aa 53 59 61 99 37 6d f4 90 32 3d 25 c2 17
bc 71 be 37 f1 81 03 5c f4 57 eb 5c 06 d6 a3 de
d3 d6 6d 5b 35 f0 61 81 bf 94 d0 ec 13 ec 44 7c
70 82 33 49 1c 55 4f 9e 99 1f 6b cb 8b 78 d3 3c
9c 36 95 5b 8d ce 51 79 ff 8b c5 92 44 f6 67 90
87

# PKCS#1 v1.5 Encryption Example 12.5
# ----------------------------------

# Message:
d7 1d d8 7a 13 99 1a 0d a2 c7 4a 58 b0 48 56 34 
b3 e0 4f ec 9e 3f 1c f2 60 4a 93 be d7 96 96 fa 
63 78 eb 1b a0 e5 d2 04 70 a4 

# Seed:
09 90 59 92 58 e9 7a 2e bf be 10 97 72 25 c4 16
76 2e 95 d2 55 3a 80 1f 72 6c c2 49 bc df 32 21
32 58 57 19 fc 12 39 9a cd 72 54 ae 77 da 34 3f
e2 a9 a3 ac b1 1c 14 e2 14 e2 d8 5a 76 70 8c 3e
72 17 3d a5 d9 90 58 e0 c8 70 9e df 28 c3 69 38
76 9f 1f 22

# Encryption:
0f f9 cc e7 b6 9d 7f ca 48 d7 c4 f6 cc ff 24 8c
3d b8 8b f1 b7 85 2c d9 d8 52 5c 3b 41 e4 4a 9b
54 0f 20 8f fb b8 c8 5b fa 89 0a c0 2e 99 49 59
d6 b0 7f 64 81 40 78 55 6f 8e c6 0d b3 57 ac ea
f8 39 11 5f ad 41 f8 91 8d 69 c2 1a 3a ff ef 6e
b1 4a 5d 2c d0 64 5c d7 06 b5 81 43 39 4a 27 35
36 82 e3 ba a1 98 00 2e 16 80 f2 8f 34 be 08 9a
57 84 ac e5 ca 6b 11 08 99 df b9 58 2f 2e 4b 2a
40

# PKCS#1 v1.5 Encryption Example 12.6
# ----------------------------------

# Message:
5e 4b 15 8f 8d cb cd d7 e3 08 38 5b 40 19 0f 5d 
ef 8c f3 30 5f c4 9d e6 3c 9e 35 b4 02 36 ae e1 
f4 56 20 5a 52 67 a2 aa 7d 88 cb 2c 11 af 7f 28 
99 d0 1d a1 b2 c7 46 6f fe f7 

# Seed:
a5 22 f9 80 8d 9c 01 d8 ff 79 77 5f 7b 22 09 8f
c5 fe 32 54 e1 b0 4e b1 cb 85 0e 10 e2 c5 06 5e
23 27 4d c0 a0 55 87 43 6d a3 75 59 33 5f e7 09
3f a5 e7 a2 a9 c9 a4 dc af 23 51 79 d0 e9 8f a3
3e 34 b6 16

# Encryption:
1c 0e 86 a6 36 6b eb 1e 12 d6 bc fa 6a d4 94 06
c8 b7 e4 8d 1d 5b e4 5c bd 83 19 49 87 49 6f aa
3e 21 92 7e c6 62 f5 02 ac 3f 91 a4 b4 b9 1d 16
0c 19 86 a5 ed 09 27 66 88 3b 85 55 e3 c9 31 4b
44 ba 33 83 db 28 74 23 af 91 b4 13 91 8c 08 02
c7 77 8e 46 c2 96 dc 9f 04 cd b8 b0 6a dc 7c 53
d8 59 f4 42 cc bd 5f fa b5 af 75 2b 97 9b f5 23
a4 0a 1d 08 d7 60 63 ff b3 d5 cf c8 2f 15 eb d6
d4

# PKCS#1 v1.5 Encryption Example 12.7
# ----------------------------------

# Message:
35 9b a5 07 56 a8 03 30 40 9d 3f 23 6a 34 0b 90 
f4 2f 73 2a 87 71 1f e2 23 52 d4 c8 25 0d 45 47 
5e 32 b9 58 83 e1 60 97 55 a1 3c df c1 bf 39 4c 
5c 67 36 9e ca 1f 9a 33 e8 ba 

# Seed:
f9 eb dd ac 9d e1 70 9a 06 bf 6b bd df 58 94 e2
3b 96 2b a0 c0 64 bf cd 7c c5 76 60 3b 0a 1a 1f
f3 5d 64 5e e8 7a c6 f8 21 a6 a1 51 e6 bb b0 5b
f3 e0 5c d9 a6 e2 cd 9c 6c f5 53 bf b0 7b d2 fd
a0 40 df b6

# Encryption:
08 33 60 e6 49 05 9d 00 65 8d ba 21 f2 df 28 a2
76 4c 45 89 f7 a7 7d 5a f9 95 79 a8 ab 44 80 c8
26 a7 7c 2f b7 95 4f 4f 31 fe 1d 9e b1 bf 40 e8
09 57 7f 39 30 1a d3 ab 95 b3 81 6c 90 ec 3f 1c
d6 29 c4 39 61 74 be d9 fe 1e 0f 47 68 23 e5 3b
41 d1 35 b4 9a 02 b0 0e ff c7 61 ec 90 94 23 af
15 85 52 37 b7 7e e0 7d f2 5a b4 e8 58 46 7d 4c
cf e8 08 43 24 1b bf 88 eb 4f 85 3e f4 b4 3b a3
ac

# PKCS#1 v1.5 Encryption Example 12.8
# ----------------------------------

# Message:
a9 f3 9f 8b a0 64 66 25 0c 26 5d f0 ca 46 57 0a 
16 01 12 cf 38 fd 74 59 99 

# Seed:
88 b3 c2 8b d3 99 9a 86 0b 8d e7 75 93 5b 8e d7
8f a2 f2 7c 26 8b 24 3a 02 45 af 86 72 25 47 19
f2 3a 4b 7f ed c0 0d 54 e1 2e 9d 70 1f 64 69 94
24 b6 b8 7d 14 d9 67 6e fa 95 9b e2 1a 04 b6 43
5d 25 10 03 dd 15 3d 7d 08 ff 28 d9 3a 93 20 dc
1b bc 3d b3 97 a5 48 94 f2 05 79 f9 3e 4e ad 65
c3 1a 40 7e b4

# Encryption:
19 83 96 50 b9 25 f1 f9 6d 61 1e 4d bb 91 49 93
54 5c 67 39 0c 32 93 5f bf 82 25 9d ad 10 f2 37
30 eb 48 f3 42 00 46 5a 20 38 72 77 e2 b9 61 e0
83 18 d2 07 74 47 c9 10 92 71 e4 de d8 ff 3d c4
f3 79 ee e4 55 ae 96 08 1a 1a e2 4b 96 ca 73 0a
62 f7 ac c5 a5 ea 52 28 e3 a4 8e a6 74 1c b3 0e
82 9d 55 09 cc c6 c2 87 c7 29 1f 3a 1d 89 d6 26
bc 98 15 77 de 52 a1 1f 12 ae 21 43 d3 b0 b5 2f
86

# PKCS#1 v1.5 Encryption Example 12.9
# ----------------------------------

# Message:
68 

# Seed:
53 8b 09 5e 4f 2a d6 77 30 68 74 21 91 52 42 25
1d 07 f6 61 ed ac 7f e7 9e 31 d6 c3 45 af f5 9d
f3 88 c7 18 2f ff 0c 04 27 b5 99 35 c6 91 da b8
bc 42 be 47 d7 69 11 88 0d 91 7b 86 27 18 b4 c1
c1 88 8d 42 20 b9 f8 23 1a cd f1 2d 9b 85 18 6b
e0 95 0c 1a ff 84 cd 0f e3 65 86 a5 0e 7e 04 b1
72 ca 9c 85 9e 2d ed 6b 8e a5 79 dd 5e 6e ee 77
2c ca a6 b4 a5 d8 ab 17 31 a8 35 d6 a8

# Encryption:
1e 86 8a db 0e 65 32 80 60 4e 8d 3b c4 d9 69 84
a2 0a a0 9d 48 0b 4d ff ac 62 ba 78 cb b7 ee d0
64 5a 6d 94 d9 d2 f2 d1 e9 17 c1 46 b4 1f aa 3d
1d 2c 19 01 05 36 8f b4 84 06 a1 d2 42 6a f3 dc
c8 5b d0 2d 5c 26 c8 97 cc b2 2e 57 59 12 64 1a
18 8e f4 ac 47 a0 a9 fe 9a a2 70 6d 8e 10 61 f5
d9 30 63 f4 90 17 00 3b 23 09 ca 7d 8d 36 70 3b
fd da 3f 7f 43 df 15 8a 15 bb 22 13 9a ae 15 10
77

# PKCS#1 v1.5 Encryption Example 12.10
# ----------------------------------

# Message:
35 65 87 e6 c6 c0 b4 6c 24 45 e0 18 63 52 76 ab 
84 5f d1 07 6d 10 7f 

# Seed:
e5 d3 3e 4d 93 c7 08 44 ee 4a 01 65 56 24 2d 08
e8 e6 2f 1a 7f 79 47 79 e2 23 ee 9d fb 23 1c 3a
52 0f 29 7e 50 73 e4 f9 2f 53 5c f1 44 55 7f 94
ee 1e 1e 5c c6 bf 4c 0c 0c 8e 5e d4 0d 06 63 56
06 f7 54 cc 2d d8 3f e0 2c 57 6c cd 2b 83 5f d0
f5 3a 99 08 3f 4d 15 bf e9 26 88 99 ef 09 ff 5f
2f 3c ec 9f 9b 8a 7e

# Encryption:
15 d3 64 a4 49 9b 30 a5 f7 8b 6d 7d 4f 66 7a 1f
76 d7 15 15 8f 28 01 19 b0 55 e1 f2 66 3f c7 96
e3 3c 0e b6 4e 34 a8 da da 5c 81 75 42 57 a8 bd
f0 ed 81 15 f6 07 b7 c9 cc a4 81 f7 45 20 b6 d9
ac 98 f5 e7 2c 2b af 3c bb 6b d9 ba ea 5d 75 86
0c bd ae 34 03 fd 5c 37 96 4a ae 64 36 6a b0 9b
c9 c6 72 76 95 14 48 19 3b b4 b1 af a2 70 79 c3
41 70 a2 69 56 51 0d 44 27 64 22 99 08 42 0f e8
0a

# PKCS#1 v1.5 Encryption Example 12.11
# ----------------------------------

# Message:
e1 53 27 6a 68 79 67 8f ee 19 89 48 28 d6 26 2e 
a3 9a d0 54 c8 9e dc b2 3f 72 dc da 1b 00 73 c2 
87 62 02 18 d9 2d 0e b3 0c 62 af bf 2b 45 df 62 
d0 66 65 f8 05 25 b6 72 7f 95 e3 42 29 e6 82 a8 

# Seed:
2a d7 14 2f cf a3 bd be b7 55 b2 c5 b5 cf 13 e6
96 9e b7 3b 7a 06 bc 29 bc ad 7e 75 30 a5 90 23
0f 6a 43 fc 03 d6 c3 a9 c6 41 e5 3a 41 77 d5 75
02 91 ec 6d 4b 33 f8 71 66 8a d8 56 90 77

# Encryption:
17 8e 49 77 0a 4c 8f fb 7f 65 f3 82 cd b5 76 e6
08 e9 75 c4 37 13 39 10 2b 95 2a 1e 71 cb cb 91
fd cd 0c 0e d5 a8 5f bd 26 3d a6 a7 4e 49 1f b0
4b 60 a5 96 1d 8e 6a c7 24 ec 8a 81 61 c2 2a e3
10 40 7f 59 f7 e6 02 da 48 aa 2d ad 68 3e 88 db
1d 84 29 5e 0f ba 5f f2 f6 73 df 32 85 4d 01 b7
1a 89 46 0f f0 e6 e1 be 98 ae bf a2 27 e3 97 ea
cc 8b 23 17 41 47 c4 4e 16 20 1e c6 ba bd 16 5b
a4

# PKCS#1 v1.5 Encryption Example 12.12
# ----------------------------------

# Message:
fa 4e b9 31 a7 e2 09 0f 31 ed b1 ff 7d 83 61 

# Seed:
43 7f a1 51 f4 34 05 db 22 42 2c b3 5d 1c 57 61
16 1c b9 a7 8a bc b6 f0 6d c4 e7 a8 69 48 1f 40
b2 1a e1 d3 3e 07 5c 48 5c d8 50 1a 3c aa 60 18
32 5b 7f 85 0d 4d 8f 6d cf d2 af fe 19 d3 e6 c7
08 74 e7 10 11 14 f0 fb b9 82 25 81 ba e6 ba 2b
a1 e7 49 88 81 a5 dc 5b b8 85 27 38 a8 2f b0 6d
15 27 b4 33 44 87 b3 24 01 3e 32 bc f1 7b ab

# Encryption:
22 6d 77 67 00 c5 e1 dd b9 99 4b 32 91 f1 d3 34
b6 9d d8 63 06 5f a8 34 21 e0 1d 52 06 ec 2c db
89 90 cf 78 97 31 09 dc 9f 12 6b 60 33 d6 d5 d6
91 8e c8 50 d6 9c 71 3c b5 bb e3 2e e5 9e 44 5b
cb 4e 50 c6 f1 64 43 4d 2a 6c a6 39 69 a2 9b 25
03 64 15 b0 f7 cb 21 b4 f8 b3 4a 8d 9b 74 65 3f
ff 4f 5d b9 d1 a6 b5 15 2a 64 48 36 b6 8b 8e de
9d c5 a1 69 dc 60 dc 5f ac 46 8f 24 27 84 58 10
62

# PKCS#1 v1.5 Encryption Example 12.13
# ----------------------------------

# Message:
58 81 15 e5 37 09 eb 15 a3 37 4c 25 32 9e 88 38 
26 e3 21 3f 37 a1 b7 a6 5d e1 2d f2 31 72 f1 7f 
a4 96 ff 49 2b 09 17 3b a0 c6 f5 8c 29 3d 47 f1 
48 

# Seed:
07 81 85 e0 83 cb b0 6f e1 a7 49 74 35 15 f3 a0
b4 b2 6f 85 3b 10 e5 68 e8 70 82 ce 44 41 2c a6
7e 59 88 8c cc 0f 50 31 01 52 1a ca bf d9 8f b7
b5 c1 1d 8a 94 1b a0 3c 49 5a a0 3e 13 52 2f 48
7f 6e 16 16 be c2 07 2b 39 96 fa ee 29

# Encryption:
0e 7e 50 a7 b2 47 b0 29 7d ec 65 c9 52 3f 67 ca
b6 b5 2a 02 5f 53 32 0e 94 86 cd 20 74 10 ca dc
74 e4 b0 3f c0 6b be d5 98 b0 22 b6 3b 37 76 2a
65 fd 35 1c b2 72 7f 3d 80 35 a4 cd ba 9c 6a 31
e4 ed 6b eb 4f ed 31 34 eb 63 df ce ab 4f 5f 24
59 e5 9f ca 01 74 75 8a ab 37 53 b5 c1 93 c8 1e
11 49 0f 97 b6 22 b7 3f a7 3f 8e ae 7d a8 39 34
84 b8 29 79 71 a3 e9 23 12 9c e4 35 7b 64 5c c6
38

# PKCS#1 v1.5 Encryption Example 12.14
# ----------------------------------

# Message:
20 f1 cf aa 63 67 c7 c3 9b 54 a0 

# Seed:
4a 1c 7b 70 f7 83 ba 0d 5a 26 d4 64 5c f0 5f a6
10 f5 be 01 77 fd de 9f 2c 34 50 57 db 42 44 57
fa a4 30 c9 42 4a 54 f7 e5 5f cf ee 6f ae a4 24
4c 03 97 74 81 ed fc bb 28 88 37 c6 a4 8d bd 72
96 67 7a 24 e0 6c c9 d8 5e 68 8c 14 09 0f a2 83
0f f4 96 79 33 ea f0 db 69 4b 6a e4 02 ca f9 c2
be ff 04 a5 2a 2f 84 7f e4 0f 4f fb df 3d 58 b7
c4 fd a8

# Encryption:
1e 52 84 20 bc bb a7 d5 9c 6c 40 d4 46 d1 aa 95
6a ff 03 05 36 5b 4d 7c e9 81 0f 22 c3 4f 09 a5
55 e5 ff be 51 75 03 7f 90 3e aa 6c 40 56 36 36
d3 81 f4 53 25 b5 e6 1a 2c 70 51 12 55 64 02 a7
dc be 86 cf a5 4a 6e 6a 50 37 8d 05 e2 1c 95 cc
6c 45 ff dd 05 17 f7 7a 36 b2 24 d1 70 0a 1f 3e
bb 81 d3 67 8a 66 e1 45 34 a8 0a 59 8b d7 17 26
fe 32 2e 73 9c 17 eb a5 08 da 8a 03 1a 27 f6 bb
28

# PKCS#1 v1.5 Encryption Example 12.15
# ----------------------------------

# Message:
73 60 cd b6 c1 59 96 b0 60 33 1e 9f 2a 36 89 95 
c0 64 ad da 56 55 95 27 78 2c 17 0c 69 1b b3 bd 
3b 

# Seed:
58 dd 80 f4 fd c5 c9 5d d9 eb 56 ac 80 a0 2d 53
e8 cc 2e fb 3c ba c6 72 7d 75 b1 e7 b3 5e 05 42
19 43 3a f5 aa e1 98 fd 62 f2 ec d2 ab 8e 26 38
77 b5 c9 1e d5 13 e2 35 49 7a 63 19 2d b9 a9 bc
b3 bc a9 7a e9 bf ce df 93 56 44 b2 d3 bc 20 e0
2a e5 e7 42 4e 81 2a 29 49 61 6b 82 01

# Encryption:
20 3b cd e1 b4 12 e3 ce a2 5b 86 30 b2 08 f9 78
ab 22 c1 da 81 25 07 6e 10 ff 91 d7 bc fa ef d7
dc d8 39 16 55 c8 86 e8 a9 45 a7 f5 7a 74 ce 2d
c0 e8 ec 7f db 17 cf 19 57 80 d3 ce 80 e0 dd e7
63 9c 67 72 05 b8 f9 ec ea 11 54 68 97 7b de a3
50 0b 23 9f ce 0d 7a 94 be 6d e3 33 ea 7e cc 22
c0 7e 65 d0 eb d6 5a 39 0a 18 5f 18 17 89 e7 ca
8c 1a c2 fc a8 28 bb 8d 28 22 7e 38 c9 88 9c e0
08

# PKCS#1 v1.5 Encryption Example 12.16
# ----------------------------------

# Message:
31 9b 82 94 bd f0 7c d4 06 85 e8 80 09 fa 7b 3f 
12 90 16 0e f3 30 67 ad d5 ef 4d 80 fd 

# Seed:
4e 07 d7 f9 63 be 24 ee 6d 8b d4 dd 95 e9 e3 33
49 30 e0 3e 65 8d 29 6b b7 95 48 6d 72 4e 07 41
dd 50 73 47 a5 b5 7b 79 c9 0d 3c 90 c1 ae a7 16
19 09 1a cc 81 98 b5 51 22 da 50 99 58 2e 0d b7
da 3c bd 3c fd 85 29 8f 31 55 4e 29 cb cb a1 19
9c e4 70 16 3c a0 33 7c c4 14 ad 32 76 70 81 11
d6

# Encryption:
04 fc 7b b6 fb 64 19 61 2b 6e f5 4b 3c a0 0a 87
46 51 95 c3 7f 7f 94 0c 23 3e cb 1d 4a 50 5d 3c
56 ed 23 e0 9e 03 f1 45 4f 04 b5 6f 6d a2 5d 0a
6f ca b0 c4 00 87 4a e9 80 6e e1 87 80 b7 5c 6c
a5 66 29 e5 77 e8 e7 b5 d2 fc 2c 44 0b 98 00 19
5b 58 51 1b c3 a7 95 44 12 c8 f2 73 cc 0e 9d 97
1a bb dd d7 02 8e 6f 84 87 6a 30 58 a4 54 fe 2f
33 c7 5e 3d d0 62 f1 19 cd 3e ca 81 06 b6 bf be
a4

# PKCS#1 v1.5 Encryption Example 12.17
# ----------------------------------

# Message:
79 c6 82 a2 b9 79 fe 5c 96 32 af 18 31 c2 aa cf 
0c 6b f5 66 88 5b f5 25 62 50 

# Seed:
5c 2a 95 6d 4b 5f 06 f7 50 83 5a b8 b2 9c f7 c6
41 d7 93 c5 56 e1 2a ab a9 56 df de 46 32 d5 e5
02 d5 90 41 78 3a fd 1d a7 b2 e2 d2 4e 22 d6 44
78 35 bf 6d 77 c6 ee bc 0d 2d 64 e7 ed 2c 14 17
18 ad 86 87 c5 97 b7 71 8f 38 bf 1a 33 16 ed ef
f6 b7 2e 28 51 82 88 07 bc ff a9 b8 e1 a8 52 f3
fa a8 07 f6

# Encryption:
25 1d 48 56 b7 a7 58 0d 53 88 01 62 28 1b b4 e4
1b de b2 a8 7d db d5 ae 1b 30 7d 44 48 be 1f 11
69 5f f7 22 c4 32 41 5d 0c 74 ba a3 fc 0d d5 11
66 ac 86 5b 31 0c 4f 5c 87 07 98 62 54 c8 96 04
cc da bc e6 c6 92 44 66 21 89 8b 4f 5a 08 bc df
64 62 e5 18 ee 3a eb 75 e2 6d 8f 63 a0 6f cb b3
df 09 8c ee bb 0f d2 f6 37 e3 a7 93 7f 4d 19 e3
a1 92 4c 16 08 2e dc 33 3c b6 de 37 63 72 46 e4
98

# PKCS#1 v1.5 Encryption Example 12.18
# ----------------------------------

# Message:
1e 0d 18 07 a2 bd 49 6b b0 cb a8 41 c7 71 58 bf 
a5 f8 aa 77 d0 56 75 c6 4a 5e b1 85 65 91 d3 48 
54 f8 65 92 5b 37 1b 68 6a aa b4 45 98 b4 a1 6a 
b6 b7 93 44 d3 c2 3f 7d 5b f1 73 b8 42 d7 8c 

# Seed:
db af a5 33 2f 0b 55 27 96 63 6d 8a 09 28 b7 d8
7d 9c ee 3d 4b e6 a3 1c 77 e1 4e 0e 5d fd d4 03
ef b9 8f a3 38 81 6e 12 62 c0 64 96 fd e9 d5 57
c0 0c c0 dd 35 fc 33 ac cd 79 a3 15 06 90 eb

# Encryption:
1b 2e ce a5 fa 90 03 fa b6 55 98 57 c6 d9 4f 95
70 03 f4 e4 1c 09 45 64 80 4f e6 96 39 e5 40 b6
8f 26 3f aa ec a1 99 69 7a 48 85 8b 8a 0f ce a4
91 05 7d f2 fb 8b 35 d0 93 d8 94 ae c1 7c 5c de
5d fd 8f 33 51 2b cc 66 cd e7 6d a0 73 36 98 5a
97 43 5b 6e fe e4 ee 09 d3 a2 8a ff 34 ce 6a e8
75 0d a4 1e 16 ca 47 e9 38 81 8e 44 a9 b4 1f e9
1a 6a 80 1d 35 5e e8 d6 60 e2 7e 4f e2 ce 7f 32
5b

# PKCS#1 v1.5 Encryption Example 12.19
# ----------------------------------

# Message:
1c 10 5d 5e be a3 66 46 a9 72 84 c1 7a 86 c4 

# Seed:
c6 f8 03 40 a6 55 7e 07 53 a9 57 3f e4 f5 21 49
77 ef db 08 2a 50 eb 5d 5c 51 7d 46 72 9f d2 a3
4a f7 ce e1 aa 46 b7 9d 47 e2 5d 83 09 0a eb 4f
50 22 98 c0 31 37 24 fa b5 4e 54 be bb 48 d6 cd
9b eb dd cb ee 0f 07 53 77 27 d6 5c 50 e7 d4 cc
0c 6d 19 fc 48 0b a5 78 67 26 2c f2 ee dd 9c fa
ba 60 7d 65 82 87 eb a4 2b 46 99 44 cc b6 12

# Encryption:
05 b2 3d c0 c6 2d 90 c1 77 1e ba 37 8e 43 17 9d
7c a6 af 51 5e 26 19 ae 4d 7c 8f c8 bd a3 78 cb
f7 a1 db ba 1a 14 f4 94 c4 e0 d7 13 38 79 7c 7e
e0 6e 1a 79 e9 ff 28 56 fb f7 4f e6 f1 a7 ca 6e
5b 9c a2 83 c4 c9 7e 61 cf 9f 40 73 e0 32 ca 27
d6 9b 1b 4e eb ea 77 cc 95 a9 28 1f 26 b6 4a 05
a9 39 44 c8 2c 5a 13 42 8e ce 21 fb 44 01 ad 42
6e 7a c1 f0 5b ff 84 b3 47 cd c4 db e5 2e 67 8a
ed

# PKCS#1 v1.5 Encryption Example 12.20
# ----------------------------------

# Message:
f1 04 02 f0 02 05 c5 27 57 ed 6e 9d 

# Seed:
e2 ae e7 fc f4 3c ed e0 75 52 a2 df e5 b5 a9 ef
80 82 76 d8 f1 0e b3 f9 bc 50 f1 bd 94 0a aa e6
34 c9 d8 a0 78 8d 44 d4 1e 8a 5c 60 36 49 ef ea
83 03 32 31 51 6c 69 d3 3e 12 f1 f5 d3 f0 ac 1f
c2 3a 9b 3f 5d a4 e0 ef 6e 45 50 cb 43 fd ff 02
b2 5d ac 86 27 2d 66 db 59 cd d6 35 f9 c0 dd 0d
aa 08 57 91 0c 15 88 1b 16 0d 70 ea 53 40 5a a9
59 f4

# Encryption:
0b 96 56 31 78 32 a9 40 c7 95 bb a5 8d aa 15 9a
4f 73 3e 82 6c e5 5a 4e c5 64 33 c5 16 84 44 4c
78 e2 e2 fc b3 7f 85 ad 87 79 16 52 2d 8a 35 a4
3f 53 c5 95 17 a8 18 e5 21 e1 98 2a 50 91 bf 2c
68 b0 0e e4 9a bd 90 dd 77 6c 02 f6 4f 34 f6 80
a8 8e ee 05 ec 08 88 92 bc 0a 95 55 b3 f2 0c 8b
ee 57 9c 0d dd e1 51 1a 18 af 98 bc 1d 9c f9 0b
81 bf 13 2a bd 58 97 0b 3e 84 d8 14 e2 7d 02 5b
73

# =============================================

# Example 13: A 1031-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
55 5d c2 ba f0 41 b7 f0 9a 04 78 42 3a eb cb 5f 
49 b9 5d be 15 70 e9 a5 42 12 8d 33 22 87 86 6a 
c4 cc 63 e7 6f 8e 3a ef 22 c9 75 3a 54 51 99 94 
24 52 41 8a 67 d1 a2 23 0d db 6f 42 22 c6 63 d3 
8e 80 05 0e eb 67 95 6e c5 f5 49 94 a0 be a6 95 
fa 59 fa df 2d cf e7 ac b5 4e d9 da 3d 0b eb 12 
2c 8a 69 1b 0b b5 1a e6 5a 77 4d 75 b1 b3 49 cb 
68 c5 17 cb d3 86 ae 48 2f 05 ee 46 03 ec f2 95 
5d 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
55 5d c2 ba f0 41 b7 f0 9a 04 78 42 3a eb cb 5f 
49 b9 5d be 15 70 e9 a5 42 12 8d 33 22 87 86 6a 
c4 cc 63 e7 6f 8e 3a ef 22 c9 75 3a 54 51 99 94 
24 52 41 8a 67 d1 a2 23 0d db 6f 42 22 c6 63 d3 
8e 80 05 0e eb 67 95 6e c5 f5 49 94 a0 be a6 95 
fa 59 fa df 2d cf e7 ac b5 4e d9 da 3d 0b eb 12 
2c 8a 69 1b 0b b5 1a e6 5a 77 4d 75 b1 b3 49 cb 
68 c5 17 cb d3 86 ae 48 2f 05 ee 46 03 ec f2 95 
5d 

# Public exponent: 
01 00 01 

# Exponent: 
05 97 87 bb 01 23 fc ed 98 d9 34 1b 7a 8a 99 9e 
db 50 76 30 8e 6d 00 11 20 3b e0 dd f9 a4 11 0d 
0b 69 2c 1e 2b f3 90 2e bc 03 a0 57 3a d0 c1 93 
af b1 67 b3 ae 4b 50 28 a5 ae bb 22 04 ef 23 f8 
e5 83 60 be 94 84 95 1e 34 71 11 76 06 2e 53 e3 
f6 38 74 fc 9e 35 91 a9 d9 06 8a a5 e6 c8 c7 a9 
ab 08 e9 79 24 70 06 6d 71 a0 7c 34 33 dd 70 3a 
42 a6 b3 a9 b1 bf fb 31 49 49 8d cf f8 b3 57 56 
ed 

# Prime 1: 
0a 7d bd 7e e6 de f3 87 5b 4c 55 9f 56 69 30 7d 
17 6b c1 25 b0 9f 5e b8 26 01 b8 13 48 57 6c 1c 
a7 dc f4 c2 bd 7f 2b 42 a2 c5 46 00 02 fc e0 a9 
a2 1d ac 5a 97 97 db 23 3d 9d 4a 92 93 ad d3 df 
73 

# Prime 2: 
08 23 0f cf 26 fd c5 75 11 9c f9 05 78 d3 66 56 
4c fd a8 65 a2 82 0f 38 bc d6 3e ca a0 95 57 65 
77 2a 88 32 fb 31 69 7f d0 dd 45 bb ec c0 4d d6 
a5 9e c1 1c 1d 5c 6e 87 7c 6b 9d 77 c6 a2 76 e3 
ef 

# Prime exponent 1: 
08 d1 c7 d9 c4 d0 2c bd df 7c 7e 86 62 a8 2f 41 
19 cd 56 24 0f 25 0b 05 97 bd de 37 27 9b 86 a1 
58 64 a1 c6 8c 73 39 b4 dd 0d 26 59 a0 29 61 dc 
79 12 a4 ef cb 7c b7 96 1a 97 fe 36 d6 bb 4c e0 
0d 

# Prime exponent 2: 
01 d4 91 3b d1 d3 87 d5 3b af 09 bd 17 5e c6 48 
67 6d 2f b2 1d 0a 10 8e 7c bb dc c8 0b b3 4c 43 
80 b8 ca 86 5d 46 d2 2a 74 3b 31 f2 81 e1 08 21 
5e 84 05 80 63 82 65 42 88 d4 54 49 9b 1e f4 8b 
75 

# Coefficient: 
08 7b 2e f2 11 44 8e 5c 96 1c 9f 6d fe e6 aa 26 
8e e8 97 7e 31 1c 07 cc 8d b4 72 da 6c 49 86 3f 
aa f3 3b f5 05 dc fd 81 f5 3a 53 be 12 1e 44 5e 
89 d7 e0 38 ef 27 f3 72 7b 51 2a 2b 80 94 41 20 
ea 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 13.1
# ----------------------------------

# Message:
7b c8 1d 81 46 e3 59 

# Seed:
76 65 5e 4a d8 fb 99 34 d1 84 7c c9 0c 02 47 3c
57 2b 5f df d1 64 a9 70 a3 cd 96 bc 8c d7 96 ac
80 2c 50 28 22 90 ea d4 c7 7a a5 cc 2a 7e 34 34
18 c9 df 47 ef b8 87 86 af 4c c8 d1 82 1c 50 07
39 09 e8 4f 7b 45 1a f4 a3 2a ee 6c 7c c8 97 ee
f7 66 0e 1c 4c 53 5d 10 03 9d 3a a2 03 5c 98 51
01 25 ec 5f 2a 2e 9d ac 63 95 89 ff 8b b3 9f 07
43 8d ef b7 d1 33 77

# Encryption:
14 6d 65 12 df df fb c0 2d 5d 54 bf f3 e0 36 a3
5b 4c 2a a9 44 d6 f6 72 47 93 30 65 37 08 1b d1
1e e5 68 a4 e9 71 9f 1e 31 f2 d1 4e 18 c2 da 62
44 70 c5 b0 ff ab 39 7f 92 31 b6 ef 46 34 f6 3a
18 28 5d f3 41 45 17 a5 1f 93 58 6d 66 b0 3c ab
1e 78 a4 eb 94 1b 9d 3d 7f 92 03 b5 9e 1c b3 bb
48 64 b6 46 dc 17 a0 64 11 eb d5 ff 03 37 20 58
88 1b 4a 24 b2 4f 4c 2d cd 5c b4 4c 2e ea be 6f
72

# PKCS#1 v1.5 Encryption Example 13.2
# ----------------------------------

# Message:
68 6a 81 0a 03 1d 80 61 e4 1a 77 6a 7d fb db 3a 
f6 26 fe 97 69 de ee a4 60 ba 28 67 ac f0 3d 9f 
92 4d 32 1e 8a 8f 42 5c 28 51 92 98 67 a3 26 ae 
27 5b 49 d2 

# Seed:
f7 9b 49 f6 a6 be 3e 68 88 4c 80 df e1 e8 50 1e
54 4c a7 82 23 88 43 d4 19 76 41 12 25 0e dc 1b
69 c7 d1 c3 58 7f dc 75 98 7c 62 cb 4c 33 bb 81
20 2f 72 db a7 ee e2 4b fc f8 9d 4d ae 15 0c 07
27 24 58 fb 01 b6 cd 27 09 25

# Encryption:
08 8a 47 f1 1c b1 34 de c4 b5 08 77 25 e8 a5 bd
04 f7 fe 58 2a 69 91 4f 68 3e e6 de 7c 32 4f ed
7e 07 f5 70 05 c0 e0 df 75 00 e3 70 a4 2e fd 6f
e5 b2 90 19 95 19 b1 98 06 b6 e6 91 69 8a fd 95
17 d7 80 da 0b eb f7 0a 26 d6 5c 5b 64 e3 40 a6
40 5e 88 95 55 df a0 a9 2c 42 9a e9 c3 ec 88 e8
88 ee da 04 5e 41 0e 3a 9e 61 99 ab 39 eb 1a c8
64 e2 28 c2 cc 1b 64 e3 36 16 95 b5 aa 11 3d c5
a7

# PKCS#1 v1.5 Encryption Example 13.3
# ----------------------------------

# Message:
18 54 4a ad 24 ab 07 5d 3a 

# Seed:
23 20 4b 8a d9 45 75 f6 e2 f4 6c 79 7b b8 73 aa
3f 46 1a cd 05 e7 db 78 46 a2 31 57 59 2d 52 e9
a9 a7 04 38 19 c4 88 96 27 5e d9 df 0b 1e 54 04
93 c0 77 ea 15 41 9a fa 87 dc 35 f9 fd f7 ab 8a
af 47 ee e1 d3 3e cc 28 95 06 45 f1 b6 91 38 2d
87 54 1e d0 64 06 93 0c 7e e1 10 9a a5 ca 75 a4
90 93 4e a8 d3 b2 a0 4b a1 52 1b e0 92 c8 68 f7
ea 5b e3 78 ea

# Encryption:
34 d6 e5 46 d6 9e 27 06 cd fc 6d 9e 74 18 f8 be
03 e4 57 56 a8 b1 9b 60 c2 1d 27 34 20 aa 7d 5e
bb bf 46 0e f4 c8 4f 6d 47 77 e6 bf 20 3f f9 18
5f d5 7d c7 23 b5 8f 35 c1 e9 b5 ff f4 7c f9 5a
e5 69 a5 cb 64 dd 9c b8 d6 40 7c 0b a5 ef d1 48
f1 8b 56 96 b0 47 da aa a2 77 ed 8d 45 28 61 4e
e3 da 52 38 71 32 b7 a9 cc fe 07 3b e5 03 32 c9
e2 83 73 53 c6 7d aa 0f df ef c3 b6 52 cf 7d 79
fd

# PKCS#1 v1.5 Encryption Example 13.4
# ----------------------------------

# Message:
d9 0f a3 8f 

# Seed:
3e 09 f6 50 6f 7f 71 91 47 ae 21 e3 94 53 ae 0b
41 60 98 da 10 3a f8 4c 0f 7c ef ae 50 01 99 c9
85 59 67 a8 30 0a 1a 7f 8d 2c c1 3b 91 72 7e eb
d9 3c f7 7d 3f ac b1 9e 83 a8 40 eb 58 3b ce a6
c6 11 3a 9c 6f 6f 1f b5 a0 b3 32 55 9a 6b 43 15
47 45 7c da 1d 3d ce e3 41 d1 71 c6 2e 95 e7 ea
ae 16 ae 0e af 1c 1f e8 10 fb b0 b7 13 8c fa 3d
66 c6 38 50 f3 81 e4 9a 2d ad

# Encryption:
27 13 3f 43 79 1b 39 22 7c 8e 95 4e 1c aa 83 0f
a6 59 95 f8 a9 f8 8e 0d 1f 75 67 85 ed 5c 8c 78
54 fb 1a 9d c4 a9 5f a4 4d bd f0 f2 85 99 c4 72
84 93 79 75 0a 6a 1b cb 42 ca fb 5b bf e0 8c 65
10 6d c8 40 cb ec 8e e2 cb 5a 20 6c 81 18 aa 06
1d a4 d2 1d 1b 5c 46 11 7f 77 c8 96 bc 71 ce e2
f8 75 7a ff 5c b2 b3 c3 47 74 55 8e 8b 82 ed b4
ef 9b f2 d4 f2 ca 4f c5 97 fa b4 0a 2d 74 e5 23
3a

# PKCS#1 v1.5 Encryption Example 13.5
# ----------------------------------

# Message:
fc bd d0 24 8d f8 57 af 17 5b 43 96 87 5c 00 6d 
51 14 cc 11 16 42 80 04 38 08 aa 79 88 6c 4c aa 
29 11 7b f3 ce 45 a9 be 55 07 c6 b8 bf 0d 30 

# Seed:
2c 9d 14 57 ba 44 50 df 40 21 e4 e5 11 83 f7 95
d0 91 c0 7f 6a e0 dd 96 2d 57 28 c5 4b 7f c3 c7
72 d7 9d 7b ec e1 bb 0c 99 6c 9a c0 7f 99 54 dd
a7 68 7b ec 86 bc db 31 40 b2 45 bb 5e 01 27 5b
38 d1 f2 52 33 5e 36 c6 8c 0e 58 f4 ce 11 7e

# Encryption:
2c ed de f2 02 69 1a ae 42 71 85 2a 89 83 de 7c
21 4c ee 00 db e1 a1 58 84 f4 c3 c1 de 7a 3e a0
1e cc f4 7a e8 6c 26 93 24 ae 52 37 73 37 b6 ca
82 e8 5d 55 3f bb 2e e5 f9 77 dc 66 4e 14 2f 1b
54 53 8f af 85 ca c2 7c 29 f2 ed 0c 40 42 67 33
5d 48 ea 40 e8 a3 dd d8 96 bb b3 e0 ed f0 e1 6f
9e 89 44 68 3b 8c fb aa b5 ea f2 25 30 f1 58 c4
88 0b c3 e4 60 c0 6a 24 03 42 21 0a a8 7b 79 09
68

# PKCS#1 v1.5 Encryption Example 13.6
# ----------------------------------

# Message:
25 f2 a3 06 ea be 6e 3a 18 30 dc 93 6b fa 41 cd 
eb b8 80 05 17 5d f4 a3 c9 c4 64 23 01 45 61 11 
5a a8 

# Seed:
cb 8e 0f f4 1b ab 01 a1 42 f9 6e 7d 7c d3 92 82
0a 82 a1 73 66 74 b9 21 35 ef 33 08 96 88 dd 30
b2 c2 f7 ba 11 1a f7 8d 3f 76 a3 7f 66 7d b3 97
5f ce 64 ec 9a fb 09 68 50 7f 7b 31 09 7e aa e4
88 1b 36 24 06 56 16 ca 09 0c 20 4c 1f a4 be 13
48 50 ea 5e 90 fe cd c8 45 b9 d8 39

# Encryption:
4c 7a c8 12 45 e7 d4 de 3c 4d bf b3 15 c4 68 34
1c bf 22 87 57 35 c5 f8 05 90 50 58 53 06 8d 6b
e7 03 9b 1c ee 6d 07 ec b7 66 9c 42 f6 b9 2f 2a
71 f6 45 f7 2c ba ff 76 34 16 37 d2 f1 d6 b1 fe
ce b0 7f 21 e1 4c 70 fb 77 bc 7f 87 b0 31 c2 c8
f2 ad a4 ec 43 e9 12 68 2c 2f 49 63 9e 75 71 57
18 77 fc 48 1c bf 26 98 b3 73 15 bd 4b 93 07 83
f4 79 45 64 2b 7d 81 5e 04 32 d4 5d d1 d0 d3 b2
a5

# PKCS#1 v1.5 Encryption Example 13.7
# ----------------------------------

# Message:
a0 da ab 4f 9f e0 4a 2a 51 bf 08 32 70 11 5d 0d 
06 dc 29 21 cf 8d d1 3f 5c ff 26 04 bc 55 1e fd 
98 3d 9b 25 b7 27 4f 3c cb 0a dc c1 1b 1a 39 54 
ab 9d b4 

# Seed:
c8 1d 1c 02 f0 6e 7f fd 6e 03 b8 1b 71 93 aa bd
ac 56 63 ab 14 25 a6 7d 1a 29 50 88 5f 5d 1c 4b
72 5e 20 9d cb ce 9f 7b f2 96 75 23 42 f1 84 fe
a0 6a 7d 6c b2 bd 39 b2 a3 18 07 51 64 a2 e7 61
b7 02 70 2b 01 92 46 c1 e5 0e 6e

# Encryption:
06 c0 6c 97 5f c6 b9 c2 60 d3 d8 81 3b 57 14 24
8b eb 39 9b 7d 68 fc ac 25 03 38 ca c5 4c 40 72
95 9f 62 03 84 44 e9 e6 66 ba b5 f9 36 06 70 47
bc af 4b a4 ed 68 c6 c8 18 57 46 29 32 56 e7 c7
24 16 65 81 54 de c0 67 f4 25 0d c6 b2 9b ba c1
8e 82 1e 49 a4 c9 b1 63 83 1f 7b b3 83 92 32 6c
6b 46 85 46 4f e4 f0 26 c9 ae 4d bc 58 49 47 7b
4c 26 0a a4 ac 02 d2 1a 26 40 20 f1 0c a1 1b 4b
0e

# PKCS#1 v1.5 Encryption Example 13.8
# ----------------------------------

# Message:
74 3c dd b3 61 c0 bb 32 47 64 95 ad 5d c6 3a 2b 
ce 5f ba c1 c8 c2 03 93 69 32 67 d8 43 f2 8b 8c 
f3 ea 13 e3 74 e0 9d 0a a3 f7 ae 5d 8f 72 d8 e6 
cb 9d bd 

# Seed:
f7 2e d2 d0 69 b8 ae 50 27 04 6e 03 27 b9 87 84
58 09 b4 e8 16 c8 86 58 24 fc 4a 23 01 b4 56 80
2b 18 9c bb 43 f0 48 32 ac 25 c8 48 d7 4f be 1d
62 5a a9 8a e0 5e b6 25 47 76 1c 78 b8 17 61 c3
03 f9 3d f3 fc 0f ea 3c 5a 7b b1

# Encryption:
38 3e bb f1 59 e1 d0 a2 1c 74 eb 61 e3 64 3c b6
31 be 18 c7 a2 a5 4e 24 89 33 58 7d 34 5e 99 52
72 46 6d c1 bd 61 3a dd b4 cd 7b a5 01 92 fc 2d
89 4d 7f da f7 83 63 b0 79 d9 98 01 9f 16 42 31
cc 2d 75 2d b7 6a 9f 9d 0c 52 04 bf b0 f9 93 05
53 09 6b 5b 76 80 b0 89 4f b9 9c 11 42 5c 67 d6
5d 96 5e 35 12 8e 15 47 46 b3 fe d8 d0 16 99 30
70 a7 0e 07 eb eb e0 6f 2f 4e 97 6c 9a 63 fb e3
20

# PKCS#1 v1.5 Encryption Example 13.9
# ----------------------------------

# Message:
e5 31 92 fe bc d3 69 58 bd 08 03 f2 ea 0a fd bf 
df 99 3b 58 a9 e4 ee 70 df 95 b0 6d 4e 7d 74 b6 
74 5b 87 f5 81 f3 42 f8 ae f9 ae 4c 31 82 c4 19 
9b 65 51 fe 18 f8 d3 b9 ff e4 

# Seed:
ad 98 2f a7 29 f2 7f ce 8a 67 49 03 ec ac 69 4d
d3 34 13 c7 8e 34 28 ae ed 46 9f 84 d9 57 5c 6d
a2 75 29 f2 c1 4b 53 b7 58 d2 0f b6 b2 47 c8 29
c5 fb 1a 16 af 55 07 9a 70 73 cc a0 56 25 96 2b
6d 1a 6b ef

# Encryption:
22 1d 88 a8 6c 9e d3 7a a0 9c f5 72 54 97 82 e5
8c a8 d4 85 1f 01 6a cf 28 9e e8 bf 23 79 0b 1a
8f 14 8c 16 55 08 bc 3f db 1e f9 c9 01 16 27 42
7c 5f 32 e5 ca b8 50 cb 6b c0 bc 04 a1 1d a2 f0
18 13 f3 41 41 7b 3e 63 2b ca e0 02 97 7d 64 ff
c9 62 c7 fa 75 72 f5 6d 26 17 e2 a5 2d 3e f9 17
c4 4a 33 b7 15 82 af f1 39 0b 7c 77 4d 60 7c 8d
57 8f 7b c9 0b 35 80 d7 7b 03 73 19 14 77 bc 14
26

# PKCS#1 v1.5 Encryption Example 13.10
# ----------------------------------

# Message:
d0 eb 48 0e 27 45 77 9c 8a 30 fe 82 0a ef 56 d4 
ce 39 ef 84 ea 40 c7 df 0c 

# Seed:
46 5f 91 e3 f0 7c b7 22 83 bc 2b be 52 8b 9a b3
68 16 54 fe 20 b6 1a 33 f1 23 ad e5 2e 83 2f fa
8a 1b 74 a4 44 3c c8 92 95 a2 1a 2a ac 98 74 da
0a 56 79 d1 8c b5 c4 cd 69 0b d0 a2 0c bd 9e 9a
a0 72 aa 87 13 44 8f 95 e5 d6 a6 e6 24 d5 b9 08
5c fc ec b7 99 2f 10 ea 2d a6 d6 26 eb e5 43 d8
70 22 36 ee 6c

# Encryption:
06 f3 f8 c7 0d 0f c4 e7 44 73 e6 8f d2 3c c9 df
1e dd 42 35 b4 28 b7 72 a0 83 b4 1c 34 51 62 5a
6f 15 de a4 bf 31 3b f4 f0 3f dc 4f e9 f6 a2 07
1a c6 9a a3 f0 fc 41 57 ef c6 21 f9 59 40 61 f6
c1 98 06 bd 5d 75 9a d0 23 ca d1 48 e4 47 d2 59
b6 2b f7 34 25 91 be 83 ba ec 77 71 4c fe 2b 90
1f 36 9a ea 68 02 48 ba bf 06 87 10 cb b9 70 48
4f 32 4a 23 52 53 a3 1e 02 25 34 ab ec 7b 39 96
06

# PKCS#1 v1.5 Encryption Example 13.11
# ----------------------------------

# Message:
1c 42 97 f6 df c0 7f fe 57 59 aa 1e aa 5b 79 37 
8a fc dd 1a 9a 33 a2 13 3a 39 ac 

# Seed:
a7 af 2a 86 01 e4 08 c3 18 fd 1e 0f 82 44 5b 50
95 44 d5 ec 97 a7 95 8f 59 4b 20 54 c5 09 f7 ef
fd d4 16 30 6b 2b 2c 91 b5 a6 37 a1 56 82 0d 60
1a 23 ff db 31 fb 35 d3 05 aa 93 74 57 8e ef b8
10 2e 8b 72 44 19 1f 4e c7 4a a2 6a 0b 7d b3 6c
ab 44 99 9c 81 b3 61 57 01 6b 55 89 06 e5 d7 08
8d 51 32

# Encryption:
3c df 2d c6 7a 4a a5 31 cf a1 42 80 08 bd 05 44
ab bd 03 29 22 dc c2 43 6d a0 b5 d7 ef 9a 70 17
e6 19 3a 8b af 38 c5 8e 91 96 2d 65 a3 75 f0 8c
1d 55 57 9c f9 4a 79 5c 9c 70 b6 e4 2e 16 43 ce
f5 40 dc e1 e9 86 dd 99 88 87 b6 95 52 44 4b 6d
e9 3b a7 d5 f7 64 83 54 bf cb 70 21 39 ed 39 54
94 7d 7b 18 0b 6c 02 bc ad 82 43 a0 ab 27 ca 66
52 76 29 1b 46 cc 31 8d a9 b5 f6 0a 04 af fe bc
b0

# PKCS#1 v1.5 Encryption Example 13.12
# ----------------------------------

# Message:
00 92 7f ca 7f 5e c7 6f 54 8d d4 82 63 e3 39 be 

# Seed:
de bc 2c 1f 22 d9 32 ff cb 89 7c f1 0a e6 2c 3e
05 1e 3f 78 46 3a e6 7d 95 61 cf 1a 73 d5 5c 4b
14 ac a6 c2 1d 83 ba f9 76 cd 8b f2 46 c2 29 78
61 a6 b1 e9 c9 ef 30 81 c5 1c 4b 68 7c 67 b5 dd
0f e0 f7 55 3f 73 8c 2c 8a 5f 81 d1 26 8a 0c 2d
4a 46 1d 63 5b 0e 59 d2 3b a4 17 ab b8 04 5e 9c
10 d6 6b 0e c8 92 f9 53 c6 f2 11 f0 2f f0

# Encryption:
49 b9 20 89 b5 2a b7 8c 33 b5 bb 30 32 cf 70 24
94 4a c6 8e 13 9d 2d 56 06 8c 7a 26 2a 53 e7 80
9f b5 b0 15 65 cc 65 61 d7 13 30 46 87 52 30 ac
21 75 64 72 96 cf 2b 48 47 e1 2b 73 63 19 72 53
21 be a2 64 75 7d e0 eb 49 88 72 d8 9a 4d 7c e4
a1 bd b7 33 5d ae a7 8b a1 96 fd 50 d9 03 82 e6
d6 2f 8d f7 ae 68 5a 1d fc 84 9e 11 99 7d ee 88
60 d1 0f 70 7b 0d 35 36 5a 81 24 30 73 1e 7a 50
8d

# PKCS#1 v1.5 Encryption Example 13.13
# ----------------------------------

# Message:
8b 6d f2 d6 da 63 1a c8 d5 55 6a 26 97 54 28 fc 
4d 20 ef 5b 4a 1f 06 8e d2 e5 

# Seed:
ff 2c 06 98 85 2d 1b 0a e3 c5 c5 c9 be 26 e8 3c
90 44 84 2c 16 07 f5 f4 08 6a 6d 6c f1 08 ad ca
61 ea f6 65 40 0d 7c ff 2a 3a da df af d8 0c 64
95 6d a2 d7 d7 c1 35 ab f5 a0 d1 76 06 25 56 eb
4d 8b 75 b9 5c d1 1e a9 c0 44 2f 84 6f 03 7d a8
77 29 02 bf fc de 65 59 e1 b5 9e 60 c6 d0 f9 89
6b a5 c3 c4

# Encryption:
41 52 76 69 80 33 39 eb d8 f2 d1 cc 18 6c 7e 8e
bb 80 cf 4b 94 9d 8a 28 43 65 32 9f 3c e4 6e bd
ac 0a 96 9f 67 61 90 0c fe 34 2b c8 4c 7d 69 51
ac cf 45 28 0b af 24 a0 cb b2 42 a9 42 18 ef 9f
d3 71 b1 e0 08 24 62 62 07 0b f5 54 ed 57 00 7b
97 39 79 16 35 86 1d 86 c6 5b 1a 82 56 f4 25 f9
f3 ae 51 9e 1b 1b dc 58 75 b8 78 dd cf c1 47 0f
ee f2 ae eb 01 4b 7e 33 ef b9 f4 dd 07 83 d1 71
23

# PKCS#1 v1.5 Encryption Example 13.14
# ----------------------------------

# Message:
ea 03 96 69 bc d7 a8 cd ce ab 58 55 91 b5 63 61 
e9 0b da 0e a4 40 10 49 64 e8 89 ec e1 8a eb 04 
ce 0a b5 b1 cc b2 30 ae 03 25 5a 39 

# Seed:
20 4f ae b3 13 96 5c c1 a1 8f af 10 39 fd de 68
1b c4 3b 22 3e 28 bc 47 1c 50 42 3d a0 bd 79 7d
6a 8c 73 8c 54 03 3c 8e 55 9e a2 d1 0c 3b 79 b8
0e 2e fd ec fe 89 1c ea 2e cb 34 51 a1 a8 e2 ec
2f 44 7b 79 8d 7d e5 64 1b da 4a a9 90 b3 01 e1
dd e7

# Encryption:
4c a1 c8 5c e3 c6 20 d4 29 91 cf 41 73 3e ad 26
a0 93 11 18 5f ff e5 8f 41 28 8f 6d 0b b6 84 5b
2d 5a cf 1a a0 6c 78 d7 1f 76 93 96 a9 43 42 03
e3 8b b0 1f d8 8e b2 3e 6b c5 1b a0 c5 f3 ee b3
27 13 ca d4 d0 87 80 50 61 ab 47 3a 15 67 e7 9b
bf 4e ae 49 36 f1 8d 02 05 b3 74 6a 17 e0 64 8c
52 22 3d d9 f9 99 72 81 b5 35 ab 2f b3 cf f0 3c
a8 90 f7 10 aa 88 fd 2d 0f 39 2f f4 a8 8d 31 1a
a1

# PKCS#1 v1.5 Encryption Example 13.15
# ----------------------------------

# Message:
0f 46 2b eb c6 

# Seed:
4e ae 5d 54 92 d9 44 ff db 04 2a d9 50 16 81 ad
b3 eb 6a b5 28 b6 e8 13 53 55 b6 23 ab 55 a7 40
87 b1 9f c5 9b 85 34 da 9a 88 da 29 f6 6f 71 f9
45 2a ed e0 e3 e9 39 07 70 9c 34 49 56 72 85 74
b6 6f b9 a6 f3 38 3d 58 a0 13 6f 94 c4 ed 86 ca
9d d3 8b ff 07 0f cc 2d ef 29 bd d6 fc 98 59 70
96 7f 02 fd 6f 93 01 cd 56 d0 48 a4 42 d7 02 40
9a 98 a1 32 ab 6b ed d4 fe

# Encryption:
29 36 9e b0 0a 3f 87 84 38 c9 38 f9 d7 bd a3 85
6c 45 a6 d7 7c 17 88 51 1b 98 2c 58 f6 3d be a3
3e 63 ae 1d 45 c2 df 6b a8 0f 0d e9 97 59 2e 1f
8a 3b 3a 09 ed 76 06 51 45 3e 10 99 78 cf de 3a
60 0a 74 fa a8 a9 1b 7c 72 4f 97 3c b8 0b 96 83
5f 05 0b 7d c0 9d 2f 15 74 76 c5 b7 05 1f 94 d9
c0 f3 17 e1 f1 88 e3 09 80 79 5b 09 6c f9 bb ce
b5 63 6b 3d b9 87 05 4a 56 08 75 22 75 96 29 97
e7

# PKCS#1 v1.5 Encryption Example 13.16
# ----------------------------------

# Message:
51 4d 3b 38 00 22 b3 78 2e 8a 77 d7 7b f2 4a fd 
f9 2f 33 97 47 4d ae 0d 4b d6 e4 fa 31 ca 60 83 
df 49 6b 36 62 6e 7f 8e 1c 91 9f 9f 2e 

# Seed:
d4 d3 a9 e8 fb eb 1d c6 07 b4 dd 78 87 ba 25 c3
d0 bd 81 34 84 bd 53 76 dc 83 ef b5 8e c5 b2 56
2c b4 4f 98 5e 59 f5 0b 9a de 3c 66 71 6c c6 f4
2e 51 c6 0a 2f 4d 8d 75 bf b9 b8 24 10 5d 1e e1
58 35 f5 f5 f2 54 fc 6f 68 0e ee 0b 85 af 54 7c
17

# Encryption:
19 fb 39 1a 31 00 87 bc 3d 08 79 1f d5 99 94 be
80 12 df fd 76 a0 2a db d4 79 cc 0c 15 56 60 5d
4d a2 a3 46 1c 7c 71 a8 5e d8 cf 85 e0 8f 45 da
de 51 8c 00 af 09 f4 93 ee 8a 55 46 ff be fb 05
3c ca 2e ef 06 84 76 3c f7 80 f2 e0 97 bd 8e 5c
2e a8 4c b1 a8 b8 f8 49 6c c9 18 16 7f 65 6c dc
9e 1d 3b 2a 23 38 b6 4c 61 e9 0e cc 27 4a 12 10
e3 db 57 83 ae 3c 00 ab d3 74 8a 81 0f d9 14 91
14

# PKCS#1 v1.5 Encryption Example 13.17
# ----------------------------------

# Message:
fb a1 63 17 b0 93 08 3e 37 20 aa 06 48 17 e7 4c 
a7 51 a5 17 87 4b 69 26 50 c4 14 7f 11 9f 68 90 
70 2c f8 b1 4f 0c 18 82 21 2d 72 40 6c 3a 45 d7 
d9 ff a4 31 24 10 a6 

# Seed:
9c c9 a4 bf 8c c2 75 a6 b9 b1 35 10 31 91 10 91
7a d8 53 24 c5 a4 34 5e 58 f1 fa 47 27 5e e5 92
15 69 dc bc bd 72 36 70 cf 4a 24 eb cd 57 f5 7e
99 8c 4b bd 4a 95 67 9d 60 ba a0 ab e8 79 66 8a
2c b6 ed 0f b9 4e 4a

# Encryption:
4c 41 b9 cb cc 6f a8 7f 23 f1 7a 36 d0 51 e7 78
0a 07 06 56 ca e7 be ba 14 fa 91 c5 55 b8 58 8e
88 09 e7 d3 35 4e 7e f5 e0 fa ef e1 cf 39 2e 6f
da d4 04 4a ef 08 e3 3e 6f c2 01 c5 47 fd bd f7
c7 3d 3b e0 96 ed 25 3f 9d f4 af 52 e1 3b 9a 19
25 aa 73 93 a6 42 95 30 20 92 01 e5 5b 20 e5 1d
50 05 d0 6b 58 35 3b fa fc ee c3 7d 60 e1 ca 0d
9d dd d8 68 0b d0 a5 d6 92 e7 4f 2d bd fc e2 66
02

# PKCS#1 v1.5 Encryption Example 13.18
# ----------------------------------

# Message:
5f fe 82 e0 33 54 42 45 b8 49 62 d3 92 7c 2f a5 
99 72 ef 59 c2 37 a3 86 a5 1b d0 ba 1f 2c 1f 8e 
45 b4 6a 05 ad 97 db 49 d3 ac c6 34 4f 1e da df 
65 64 c2 8c e1 

# Seed:
cc 23 59 bf d0 d5 7b ce bf 07 5b 87 a5 85 a9 bd
e6 59 3e b2 49 61 ef f1 98 7e 73 56 05 d4 e3 0e
97 19 37 f6 f3 f5 be 52 78 fd 47 6d c6 60 ee 07
30 cd 07 e5 d1 f4 20 09 a3 33 31 2d 93 28 f3 b0
08 5c 40 75 bc 70 9a 10 f1

# Encryption:
05 8b 50 e0 bc a6 b9 34 c0 1b f7 c3 3b b6 15 b7
22 ea 41 80 7a 7d 2c 7c b3 d4 38 e2 8d be 33 3e
d6 d8 37 47 7a f8 4b b0 06 bb b1 0b 36 94 4f 15
d4 f6 d2 8b 5e d2 49 d5 69 0c 08 37 a1 6e 15 7b
a8 80 22 74 10 1c d4 4e 7f ed 72 a7 59 81 c9 75
66 bc 70 e5 55 97 02 bf 5b 62 fb 09 b2 13 60 56
73 aa eb b7 fe 9b 1a e6 d8 04 03 a2 01 33 80 3e
1e d2 35 0b 8e 15 ff 01 9a 70 0f 2a be 87 d6 e7
33

# PKCS#1 v1.5 Encryption Example 13.19
# ----------------------------------

# Message:
22 63 3c c3 fe 7a 7b 4f 00 fa 99 9c 4f e0 d8 82 
c3 1b dc 0d 67 0c 0c c3 d2 88 96 1f be 63 72 e0 
e5 32 46 42 c7 b1 fa 85 2e 1b 4f 69 6f 12 f5 58 
66 

# Seed:
3e df ca f4 88 71 d2 91 d5 b7 f7 72 3d 92 d9 51
51 52 b6 bf 52 b8 23 69 9c 58 8f 75 f3 4e 37 95
55 0d 07 81 18 e2 86 c9 6e 90 07 ae e1 54 f5 7c
e7 f1 d4 60 39 47 3a 4a 37 a9 b5 90 a3 7e ea 59
94 7f e8 58 7c 95 69 88 bc b1 74 e9 7c

# Encryption:
3d eb bc 6c fb 0e ed 87 16 6b cc d5 4c 75 97 ad
36 ca 0a c9 6c f1 66 76 e1 87 4b f5 f1 0a 0e c6
9c 3b e2 25 96 78 ee a6 3a 18 08 d9 06 65 ff ce
9a f0 82 7e e6 29 ed d6 59 43 43 7f 8c a0 a6 71
17 2c 52 1c c0 d1 dd 01 e2 2f 20 a6 c7 9c 42 7a
da 88 56 00 0c 4e 03 5d 9c 5e f2 e1 05 f2 c9 f6
45 7d 9e e9 5b 43 bc 4b e3 29 4a 0e e1 d5 c8 33
ae 91 07 8a ed 09 e7 92 bb 42 c2 5e 00 b0 87 e5
61

# PKCS#1 v1.5 Encryption Example 13.20
# ----------------------------------

# Message:
55 2b 38 4c 5e 51 74 f5 1f 38 0d 8b 53 e3 c8 97 
b4 8c 66 9a 9c 2d 11 98 5b 86 54 de 7f 76 d9 62 
39 6a 37 b9 53 41 f9 9d ec e4 af d7 1d 3c 84 e1 
28 7b 0f 0f 86 ee ff cf d9 7b a1 88 e3 79 9e 

# Seed:
2e 2f 3e 3f 46 d4 74 0c b2 6c bc 65 aa e2 af e4
9d 0b e6 66 39 d0 db 10 df d6 af 60 64 46 f3 b7
de 98 21 2f 86 17 4b df a5 b2 e2 35 85 07 45 3c
20 ad a6 9a 4f fe 0a 35 e1 2e fb ab 3b b4 4c

# Encryption:
14 92 91 ee b5 36 fc 07 03 f7 bd f1 f0 31 a4 30
cc 83 ad c4 3e 09 68 64 91 26 69 34 ef 37 ea ea
b1 1b c7 f3 91 49 ab 33 43 66 94 59 36 73 cc c6
39 0b 52 9e 64 d3 42 e9 f2 1d 17 6d a2 1f a6 5a
bd 57 ee c6 0d de c7 d1 a0 93 db a3 76 44 5f 1b
cf e5 a6 aa ce 9f 13 42 af 39 db 8a d4 85 ba 22
2d 39 12 62 28 fa ee b4 9b b3 b2 71 fd 38 e1 15
25 d8 03 15 4e 74 08 4b 75 c3 db cd ff 2e 3d 10
42

# =============================================

# Example 14: A 1536-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
be 0c a0 1f 9c 17 21 66 f9 12 39 1e 5d 58 dd c3 
0d 5d d0 27 9a 49 bb 31 2a 31 e4 c8 a6 6a 52 fb 
4e 8b 67 42 fa ac b2 24 c3 03 9f 1e 19 8f 33 23 
b8 88 ba 0e 35 bb 94 c5 11 bd 22 b8 86 40 5a 71 
5e 40 9d e3 bc eb 4f c9 91 1b 0e 9c 3b 1e 42 e2 
57 d5 bb ea 07 22 b5 d5 dd 35 37 56 9d c7 56 06 
46 a7 50 b8 7e aa 6f 3a 40 5a 94 bf 2a da 72 b5 
0a 4b 01 87 bb 9d 00 ec 45 1d 50 a6 a9 1a 1e 2a 
91 19 2a 7f d7 56 b9 00 14 1f e8 8f 96 e2 08 0d 
fd d8 01 66 a7 bf 67 e3 71 44 d0 9e 3a f8 99 74 
e5 7c 72 b0 3a 2b 88 fd 29 95 25 2a ce 4f 30 e2 
e4 7c 28 18 05 72 40 53 6b 58 db 42 07 50 9e 59 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
be 0c a0 1f 9c 17 21 66 f9 12 39 1e 5d 58 dd c3 
0d 5d d0 27 9a 49 bb 31 2a 31 e4 c8 a6 6a 52 fb 
4e 8b 67 42 fa ac b2 24 c3 03 9f 1e 19 8f 33 23 
b8 88 ba 0e 35 bb 94 c5 11 bd 22 b8 86 40 5a 71 
5e 40 9d e3 bc eb 4f c9 91 1b 0e 9c 3b 1e 42 e2 
57 d5 bb ea 07 22 b5 d5 dd 35 37 56 9d c7 56 06 
46 a7 50 b8 7e aa 6f 3a 40 5a 94 bf 2a da 72 b5 
0a 4b 01 87 bb 9d 00 ec 45 1d 50 a6 a9 1a 1e 2a 
91 19 2a 7f d7 56 b9 00 14 1f e8 8f 96 e2 08 0d 
fd d8 01 66 a7 bf 67 e3 71 44 d0 9e 3a f8 99 74 
e5 7c 72 b0 3a 2b 88 fd 29 95 25 2a ce 4f 30 e2 
e4 7c 28 18 05 72 40 53 6b 58 db 42 07 50 9e 59 

# Public exponent: 
01 00 01 

# Exponent: 
f3 7d 28 d6 1f 28 99 a5 c0 e0 a0 74 9d 13 89 38 
7c 64 c8 c3 58 a9 71 da d1 3c ff 85 c5 9a 62 dd 
a7 bb c0 f7 e5 bd c6 5d ff 9d e9 c7 45 40 46 31 
75 81 48 16 8d fe 6a c0 a2 87 6a 56 05 3b ab 2a 
2a 9f f2 72 79 4d d5 d8 13 9e ed 10 bc fb 4d f3 
30 20 d5 9e 30 48 fd 2f 0c 43 14 26 14 5e 36 a1 
d0 a6 bf ce 44 43 ef 3c 7e 31 d4 a9 2f b8 51 7a 
49 f7 88 c3 b4 e1 37 39 5a 4b ee ea 63 e0 e0 ad 
c3 22 4f 98 09 25 03 7d f6 f5 b2 6c 00 72 39 b4 
f0 1f 8a 9a 61 ea 0b 51 19 bc 9d 54 96 a9 5b 60 
ea 76 6c cb ad e0 37 e3 40 32 4f 25 f0 2e 72 45 
c2 36 ea e4 36 7a 64 68 a7 a0 93 8d 85 c0 a1 

# Prime 1: 
df cc 92 74 2c 48 d3 34 c6 6f ca a6 d8 a7 e4 22 
54 43 0f 80 a8 35 9e a2 3b 9a 83 b2 41 e4 7f 39 
9b 3f fe 3d ab 3f 15 be 8f a5 c9 e6 46 df f9 7c 
cf 9b 43 17 61 07 80 ad 44 cb 1f bc ef bd 6e ba 
05 5d 96 94 3c 02 47 e0 c8 76 78 eb 0b f7 6c 88 
76 c3 ab b9 ef 72 cf 01 8f 58 11 a6 be e0 4f 09 

# Prime 2: 
d9 64 e9 6e a6 fa 43 70 b5 91 ee 79 e7 e7 2e cc 
21 81 53 78 7a 60 e2 f7 ae 94 fa 95 b9 bd 68 69 
d2 81 ac 3c cf b6 57 24 7c 58 3e af dc 13 d4 d7 
a7 d7 76 5e 44 67 df 76 b5 28 bf 94 bd 03 a3 ea 
73 b8 1b e2 6c ca d9 89 b9 f0 77 28 da d5 3b 38 
ef 7f e9 eb e9 11 40 cb ad 17 dc 7e cb 1d 58 d1 

# Prime exponent 1: 
9e 79 f6 9b 5d 60 94 6f 22 b5 b7 03 3f 18 64 6c 
0a cf 12 03 41 19 f7 23 5a a1 a7 f0 6a c8 ab 6e 
d7 89 11 38 0a 33 b9 ea 1f 3e 7f 22 19 be 30 a5 
39 3d f0 dc 75 51 22 c5 8f 99 66 f8 1b ac 40 e4 
69 38 44 90 e3 8d 99 e8 8b 0b 99 c4 97 cc b5 86 
4c d3 72 9f 4f f8 34 ae 1e 1b 77 24 64 b5 e2 41 

# Prime exponent 2: 
3a 05 e9 18 13 91 30 76 e0 bf 76 7b 2b 1e 55 2b 
3e b6 19 e5 4a 24 99 ef a9 b5 31 bc cc ba 75 27 
e9 7b 9a d1 10 4f 86 aa c2 55 7b 45 cc e3 ae 27 
71 30 dc f3 04 27 05 49 d5 c8 6e 79 f0 89 0c 33 
03 77 dc 59 6e d8 25 7f d1 15 11 78 e2 0f 8f a2 
fe a9 91 71 d9 df 35 00 27 ce fa 97 0c f7 64 41 

# Coefficient: 
18 b1 0d d7 3b 14 7d 86 b6 0c f7 fe bf 46 35 93 
c0 bd d6 bc 83 a8 39 57 3b 4b 3f e6 5c 0b 13 e7 
b6 94 b8 1a 56 57 21 0b e9 47 01 1d 8d 49 29 c1 
27 fd 2f 3e 31 06 c2 53 38 3b e6 35 41 d1 23 5b 
d7 9c 57 2e 92 e2 36 34 28 20 98 be e9 4d 48 bd 
62 8a 0e b2 1e d2 61 cf a3 5b 69 b9 77 70 e2 93 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 14.1
# ----------------------------------

# Message:
2b d6 e3 c1 de fd dd 5a 43 

# Seed:
e7 84 d5 25 03 e6 29 1f 25 8e 44 2d b5 77 f9 91
63 82 a0 d1 4c 7b 9d cc bd b6 07 af 01 f0 25 8f
dc 97 23 97 da 12 39 d4 44 9a 58 28 6e ce 20 08
f7 18 f6 90 ce e7 3a 02 7f ab f8 4b df 7a ce 45
f7 ed 2d 77 32 4c be e9 0e cc 6f 1e 7b 86 cc 29
35 a4 7b a1 56 65 0b 42 fc 71 aa d0 70 99 a2 7e
97 fe 5a 3f 25 fe 13 48 e4 42 39 12 12 a5 cf 1b
44 5a 1e 70 19 1f fa 8f ca de 63 5d 2e 44 65 f5
f9 13 c5 3e 33 b5 91 52 cd 8a 14 97 84 f2 7d 83
18 28 af 2d 66 6a 5c 30 9b 56 d0 71 9c fd 80 73
40 69 a2 3e 09 2d 83 15 39 9f 95 c4 0a d7 fd 0b
b5 f9 43 77

# Encryption:
85 ef 3e 47 76 77 bf 76 07 1a 27 bb c7 39 57 15
cb 35 07 96 e4 4f 1b 52 a0 8e 90 5e 08 ff 12 56
70 5a 9b f0 15 2e 87 2b dc 74 bb 1f 2f c8 63 1e
f8 81 2d 16 94 6a 30 b5 8f 44 64 d6 e7 b2 45 0b
e4 5b 48 cc ff 5d 8e cf 7a 00 b1 b7 8f c8 fa 54
71 3d d6 96 a1 4a cb 68 00 c0 d3 b6 9a 0b 44 43
77 fa 30 3a 7d 66 db 49 27 91 8a 4b fb 0f d4 93
bf aa 01 6a eb ff 99 53 30 a6 dc b6 21 5d d3 bd
b3 5d 7c d6 1c cf 0e 9c cc bf 51 e9 ea 65 8e a3
1d 12 43 44 4c 4b 72 ff f0 1a c9 3f 28 eb 7f 67
c1 83 2e 56 8e d7 2f d9 57 d5 b4 fd 2f 00 b6 02
31 71 b8 5a b0 ca a1 03 0e d3 e3 ed c9 50 31 45

# PKCS#1 v1.5 Encryption Example 14.2
# ----------------------------------

# Message:
10 47 35 d9 ad 72 60 46 47 3d f4 b1 3b 2f 5f a2 
85 c3 d2 33 ec d4 61 70 58 2d d2 28 cd ee 46 4d 
a5 09 5e 20 8c f8 fb ca 05 38 8b 

# Seed:
dc 6a 63 1e 29 75 45 d2 f6 fe 9c 69 a5 d3 06 a1
09 16 d7 e7 ef 0d c9 53 f2 1b 6a 04 14 31 ec 8b
a5 ce 1c 13 87 43 fa ac 54 97 d6 99 f2 ff 1d 4a
44 a3 e0 63 7c c5 e5 e6 38 cd 73 67 7d 09 af ee
3e c9 fe 80 52 94 7a 73 33 4c 32 70 47 28 56 f3
07 24 3a c5 8b f8 63 80 74 66 7d 7f 7c 18 e3 ab
32 7e a3 fc 78 91 f1 c5 8a b4 7e 4f fa 6e 7d 90
11 a3 3d 9b 40 a2 d7 89 ee 42 21 25 62 30 ca 8a
61 81 1b 09 72 cf d9 86 01 75 26 18 1d 24 ee bb
32 cc

# Encryption:
92 1d 2b 02 6d 6b 7e 22 20 1d e7 7f bf 67 99 90
f9 af f4 ea 7f e7 ce 45 02 21 5f 9e 7a a4 18 b8
5f 72 ea db 6b 69 42 bb 08 a0 8b e7 da 66 19 aa
5f 1d 2f f9 61 c9 dc 2c 34 1a e3 2a 25 4f de ab
a2 f6 45 0a c4 47 4b 62 74 f0 c3 46 f2 6d a4 ed
55 5a 8c 95 11 89 dc 83 69 f3 4d 76 d8 37 d6 f3
8a 95 18 a6 27 1c 5b 56 34 62 25 a5 ab 8d a6 03
2a 59 30 fd 5b 77 72 9d e6 32 e1 75 2f c7 2a 0c
34 ae ce 25 65 7b 28 1b e8 93 2c 56 50 c9 82 fa
14 5f de 0b cd d4 8a 73 aa 02 88 b4 de 46 11 33
f2 7d 51 e3 86 01 6a 72 72 6a 9e de 1d 32 df c7
e6 f9 78 0c 04 eb 70 ff ff c2 68 82 95 66 73 33

# PKCS#1 v1.5 Encryption Example 14.3
# ----------------------------------

# Message:
31 7e 6f 5e 17 50 0f e9 4f df f2 84 bb e5 03 01 
04 4d 14 22 d3 ca 70 05 98 

# Seed:
46 6d 53 c8 d0 bb 9e d4 60 ca a6 3d 79 bf b8 77
bc 4e a3 45 ca b4 35 7e 63 9a 95 dc ae 37 9d dc
ea 5d 64 fe e9 9f b6 f7 5f f2 4e b7 4d 44 03 44
d1 47 e4 33 14 a0 f8 9f 8b 96 14 82 15 36 8e b8
65 1f 6d 3c a0 d0 8d 0b 4c 73 e6 d1 a7 68 40 98
26 d4 3c 2f 81 f3 08 40 60 5c 43 d0 fe 67 1d 3f
02 4c 70 d0 b8 99 23 cf 90 4e 39 97 99 62 cd 51
5c 16 74 29 2a a3 0d ac 70 0d b4 eb 7e 63 d5 6f
df 08 c2 24 70 f2 43 86 19 45 d0 0f a4 e2 79 90
21 2c fe d4 28 5d f1 ed da 4b 0a ec 91 35 59 f5
9d 12 55 90

# Encryption:
14 1f ca 68 dd 2e 4d 1d e5 08 6d bb 78 5f f4 7d
81 e3 9c 31 1d 91 7a 99 39 a6 ff 5b 13 43 9c a9
56 c9 74 2b cf e4 50 b5 bd 03 5b 54 1f ab 30 7f
24 fb fb 3f 8b 90 21 5b 56 04 67 6e 56 96 f3 ba
95 fd b8 d0 90 a6 c2 4a 29 d9 90 fb ff 1d a2 02
81 42 55 8f 0a d7 53 43 c7 2f 38 82 3d b7 66 7b
05 e1 6b 51 92 b9 33 60 07 f7 58 10 6c 32 8b d4
76 11 8d f8 2a d0 75 48 a7 26 92 1f b2 e1 92 b4
3c 8c 30 cc 9b 84 34 63 0e 27 fd 8b 23 ef 8d 8f
22 be 7f 73 ae cd cb 2b 1e c5 53 9d 5f bb 2c ff
9e d5 e7 f1 9b 49 18 3d 22 1d fd 53 7d 4f 37 03
2e f3 2f 63 b6 ff 74 ee 24 a0 96 cf 45 59 27 09

# PKCS#1 v1.5 Encryption Example 14.4
# ----------------------------------

# Message:
90 96 3d da b3 78 91 e7 28 8b 53 be 5d 9d c5 67 
b1 a0 7a 15 66 c2 af dd d7 72 73 24 ba 

# Seed:
a4 9f 12 15 17 5b a0 4c 27 4a bc 05 1f 0c 17 a0
8a 63 64 89 43 db 2e 8c 76 22 bc bb 1d cc 56 7e
be 6a dd 8c 44 48 16 c9 d4 36 ee 93 ce fe 23 ec
41 dd fc b0 a4 03 bb 6d bf f0 ae 5d 6f cd d7 64
da c1 a7 2a 48 4f 36 47 1f 4f 3b d7 25 b2 db 5a
fa 6a cf 53 0e 4e 4e 86 b4 fd a8 78 20 47 87 1c
b0 55 ad 68 c8 41 de 54 5a 55 40 c8 cc 12 e7 e3
f1 58 14 a0 03 9a 81 03 4b de 9c 68 ae 22 85 e6
16 b7 b5 55 5f 98 c6 13 ce 2e f6 66 a0 22 46 5c
63 67 d0 bd a4 0c 12 e9 41 f6 99 8a 14 a2 b5 e3

# Encryption:
8a d5 bf 78 23 2a a3 6f b7 83 58 3e 71 bc 13 93
03 7d 13 e7 74 54 a9 dc 11 11 43 4e 75 dd 80 20
46 ba b4 20 25 42 0c 63 96 1e a0 06 26 57 ca 50
65 de b6 53 ac 78 eb 64 98 cf 14 d1 70 4c ec 59
11 69 60 8d b0 1f c9 3d 0d 68 e6 28 01 b4 65 a3
87 a9 c7 2f 7e b3 5b 0b a5 3a 5e fa 98 c9 f0 cb
7c 7c ba 18 04 cd 70 1d 02 97 b6 60 05 ae 25 ec
3b 6c d4 cf 31 94 a3 fa 65 d9 8c 4b 95 33 30 79
0e fd e7 4f a2 75 d2 a7 9d 33 10 a5 bc c2 c8 1c
91 fc 25 56 2c a9 39 f8 d9 c1 75 ed f4 b0 76 73
d5 39 24 e2 7b 15 52 88 1c 10 83 19 23 71 5d 14
9f 1f 31 9e 38 e9 1d fa 56 6c 54 53 ba cc 14 8b

# PKCS#1 v1.5 Encryption Example 14.5
# ----------------------------------

# Message:
3d 81 02 1f f6 47 33 37 e0 4b 92 

# Seed:
9b 70 29 73 13 77 e8 26 97 fa 56 b0 86 ce 49 d3
c4 b1 54 9d e8 1e 3e 99 e1 6c d9 72 29 7a 56 0f
f4 83 f2 ef 5b 71 b0 0f c6 84 74 4f 22 4e 85 7e
6d 72 39 f1 56 d7 b6 10 2f 23 04 f8 a5 50 50 b3
75 6c 54 8f 6e a2 6e a6 f7 39 4d 2b b3 79 33 35
84 e3 b4 81 d0 73 c0 a5 8a 0a d7 87 ad b4 80 e1
f2 0a 1a 59 0e 03 1e 6b 2b b7 f3 6a 93 61 0d df
70 83 a5 07 68 c9 98 62 3f 6e 64 37 6a 29 b4 a4
18 03 2d 27 39 bd 4e 74 7d df c7 7f e3 cf 27 93
a2 9b c7 67 bf ac c7 13 e5 f1 0e 53 1b 4c 71 89
97 b9 bb 6b 65 15 e0 71 13 28 89 74 7e 54 6b 13
46 8d

# Encryption:
1b 88 4b 06 7d 0b b1 59 7f 5f ab 93 30 95 75 5a
53 0d 9d 04 e2 75 4a 57 97 ff ff 5e f9 ce f1 89
54 99 98 23 00 50 3b 3f eb cb aa 09 d3 6b 7d ac
c3 0d f3 c8 68 f0 1a 5f 17 ed 4a 72 a8 5b 6a dc
80 a2 6a 1b 81 97 6b 39 3c ba 9b 0c 82 cb 1e 2c
58 3a b6 f3 14 ed a2 9a 43 32 21 b6 e3 ed 53 51
53 74 bb cd 2b 96 f5 cb 5b d5 81 5d 1a 5d cb f0
80 d2 d3 7c b9 6c 4d 96 1d c4 7e 13 0d b7 b8 d0
18 2e 33 69 de f4 c0 f6 c4 2c 6c 20 53 1a f1 a1
90 36 de f0 8d 4b ac a7 1b 99 af 3c 4e 1e d5 27
d5 1f 37 d0 ee 1e a2 c8 b8 eb f4 fd 52 79 85 1d
c3 20 e7 42 00 8e 04 04 4d 0d a0 64 36 61 3d 1b

# PKCS#1 v1.5 Encryption Example 14.6
# ----------------------------------

# Message:
5c 74 5d d4 a8 c5 92 93 4e d1 5e 22 fa 9d ec 4a 
4d c2 0a 9f 

# Seed:
0e 6a bb 82 9a 3b 16 d0 8b 0b d4 3e b9 5c 79 1a
f2 a3 39 12 ea 83 3e e6 89 3e d5 ad 3e e7 44 c8
b2 dd 28 fc ee 80 8d 3f 01 49 58 25 be 0f e6 37
32 07 a8 78 d4 a5 25 ea 72 cf b0 bb 58 a2 67 76
fb 39 ee bd 33 5f 04 cf 71 86 be 61 c5 63 5d 95
e5 60 ed df d4 5d d8 6a 4e 67 0d 33 56 8d 83 d2
ca 20 3e d2 8d 90 e4 8b 06 4d 09 a7 5f 82 8d ea
cd 5b 37 ef cf 78 4f b1 1b 17 d6 d9 f2 31 52 24
f8 e7 63 79 1b ac 6c f4 6e 1c 23 b0 b0 25 2c 19
ed f0 40 d3 5f 59 25 53 26 5b 88 6b 29 ca 56 eb
c5 38 63 71 41 4d 82 a3 40

# Encryption:
2b 0a 43 b3 cd c9 9d 6c fc 74 da 2a 86 ec d7 d5
76 24 75 d1 43 e5 48 6f a9 cb eb bf 27 d8 e4 14
1e f7 2b 6d 4f 13 f6 64 b3 d2 e9 ec 32 22 7c 1a
d5 b7 6c 0d 1b ef bd 65 89 68 c7 db 14 95 23 82
49 bc d9 5e 75 40 25 4e 65 74 98 23 b8 a3 4b f6
a1 41 07 21 df 34 f3 c8 d5 79 f3 6a ed 9e 0b ac
23 1e 54 c2 7f 76 73 19 7d 19 f5 1c e6 ac bb cb
7b 1a 55 e3 8e de be ed 34 61 07 3e 80 c7 9a 4f
c3 09 31 30 69 6b ff f2 bb cb 74 90 5f 2d 34 44
33 80 57 fd fa cb c4 db 81 93 5b 29 e9 9e 55 cc
c1 d4 8d 89 e9 dc 4a 63 a0 11 a6 23 32 cf 57 02
62 a0 63 59 cc 36 c0 5a 6f e1 8a fb 78 32 b3 2e

# PKCS#1 v1.5 Encryption Example 14.7
# ----------------------------------

# Message:
b0 e7 7f 42 c1 a1 e3 f4 15 57 23 a9 0d b8 8e b1 
53 cb 3d 3a 28 ed df 25 9c 47 05 6a 47 0c 91 5e 
c9 55 f8 31 89 f7 1a ea cd d5 5c 33 5f 

# Seed:
fb 36 01 10 5c aa 9a 2f dd 6d d6 c5 74 6f 4f ca
fe f4 41 bc b0 fb 8a 2c c0 3d 0b 57 34 9f 93 21
af 9d 99 84 e6 4e 2e c4 85 c9 ad 75 5f 14 0c 0b
66 db 1c fc 26 69 1c 26 77 31 d1 a3 af e6 87 20
2a 96 77 e4 2e 84 4d 47 7b 75 bc bb 8c 97 d9 9f
cf 72 b3 fb 6d 34 9f a9 c4 76 61 91 47 21 7a 04
dc 06 aa 3c 3c 17 64 95 38 0c b9 2c 0a 7e 09 7b
4b 4c 5f fe 04 63 1a b1 d1 bf ea 03 67 86 50 c8
5c 17 0f a4 a1 d6 4a 4c 13 5e 61 48 14 90 b6 2b

# Encryption:
a4 bd d4 92 fe c7 9c 3a 79 09 3a 44 33 42 72 d9
d1 7f 54 3d 02 02 c3 9e 40 8b fb 39 36 6b e2 de
61 df 50 ac 45 8c ae dd ef fb 69 e2 13 ca 92 b7
49 53 66 34 7e de 73 3f f3 99 ae c7 96 c3 c3 b1
df 34 9f 01 1d a0 1f ee 7d 21 c1 c2 61 84 3d e8
82 08 56 0c 0e 89 84 b9 52 23 ea 37 31 db 91 c5
93 7a 79 b0 db 93 87 59 1c e3 2e c7 f5 83 e6 00
f5 24 44 df a7 67 1c e8 27 38 98 fb bc a3 a4 aa
68 32 cb b3 54 3a be 96 47 e5 f8 c1 37 72 82 52
ce 54 40 fc e1 0e 4d 4e f7 5d 56 b8 14 d5 19 64
44 41 41 1c 10 a2 a1 4c 35 04 72 82 7c 99 ea 3e
e5 fd a3 88 0f 34 1d ca 8d 3d 3a 4e 5e 05 ba 42

# PKCS#1 v1.5 Encryption Example 14.8
# ----------------------------------

# Message:
b8 7f 04 b3 35 0e 12 63 da a3 f9 40 5e 6f d3 d2 
5d 8e fa 13 25 56 a4 95 71 f5 70 8a 42 52 7a f3 
1d b0 1e df 79 82 0f 93 26 64 5f b1 

# Seed:
ec 8d 01 4d 6e b8 d0 23 9a 97 73 bd d3 20 bf b3
f2 ee 8f c2 7d 5f dd 91 f3 f3 90 5d c8 a4 c5 ff
13 52 9a ee e4 61 85 4c cc d4 e0 9b 62 4a fa 64
7a 7c 04 81 4e b1 e5 7b a1 4d f4 c7 95 b4 2e 84
c4 d0 86 29 24 5d ac bb ed 27 39 9a 72 5a 94 8f
3b a2 f8 b6 4d 26 02 dd 0d 5f 55 cb 23 ea af fb
3a 66 50 8e 4a 68 9a d9 ee e6 44 e6 a2 6d 43 8f
36 63 fe a9 bd 03 12 bb 0e 7e 5a 6d ee 04 bb e8
a0 74 5a 73 d5 ac 89 fa a0 96 a5 2e d3 04 6d 77
de

# Encryption:
77 5d c3 24 fe 9d 5e 05 ad 01 50 13 d6 5f 0e ba
0d cd 52 ff 9d fc 17 95 ea 93 d0 f4 33 57 98 96
86 fe 3f 8b 04 62 23 bb a5 c7 84 9a ca c3 12 35
43 43 26 16 c3 10 3a c2 ba 8d b0 a1 d2 99 40 bb
a2 62 47 0e 5e 53 be 60 e0 eb 72 4d 07 cd 91 2a
ef bb 87 fb 51 98 0e 9e 1a c1 94 da 31 92 95 41
ca 43 22 4b 15 2b e6 f2 df 6c 5f 04 42 b4 f4 d2
cc 2f b0 27 39 d4 85 a0 11 62 bc 8d bb ad 14 76
eb 06 e2 45 ab 36 c4 c7 2d 3f 36 07 d0 50 84 a0
f6 b7 2d c8 ba c3 46 bd 19 09 1b 02 f5 98 2c 91
45 7c 7b 10 f4 47 2b 57 18 45 24 21 4b 23 82 5b
59 f4 34 cc 48 a2 b8 54 ca b5 0f f7 9e 59 09 1c

# PKCS#1 v1.5 Encryption Example 14.9
# ----------------------------------

# Message:
e1 95 a0 36 a5 30 e1 c2 a9 d7 a1 03 35 8d c2 bd 
25 b1 01 bf 70 44 50 ab 8e 50 62 cb 63 df 56 10 
35 

# Seed:
0e f8 81 de fc 45 ad 3f 3e 58 b1 10 5e 49 b4 23
ab 89 12 4a 65 b5 2c fd 81 cf d5 42 b9 1e 7c 4c
1a 60 71 a2 cf 12 b4 82 7e d5 d1 9c ba f8 fe ea
54 bb 3d 73 85 7e e8 7c 71 5c 71 b9 ed 1c 07 c3
af d9 0f ce 40 44 8b b5 7e 35 24 d0 38 80 98 39
e3 6a 4f 55 44 c3 e1 81 e8 c2 e2 93 cd 57 54 c8
65 74 ad e6 df ce 0a b3 4a 80 b4 d4 8a 9d 42 e7
11 5d 8c bc b1 fa 28 c8 a2 65 01 db 7d 0b b4 96
d0 1d d6 92 65 a0 26 e1 a9 7e 9d 3a 1a 65 a8 aa
8e c2 df 06 34 e6 f2 65 1e f4 35 40

# Encryption:
1a d7 7a 00 7c a4 37 ab d0 15 9e d4 b0 b6 81 54
16 f9 f0 9d 1b 12 15 fb 7c ff 11 52 97 60 1a 88
30 f2 09 17 86 35 63 85 3e d7 8e 9c 3d 7b a4 c9
7a 05 cf 19 dd 32 92 48 47 1a 47 03 a4 65 17 8b
85 d4 ec d5 42 24 12 98 c2 fe cd 41 3e 23 a7 0c
8a 5d 47 c2 0e 31 c2 da be 3c 82 a9 54 50 27 27
49 ae 2e bb 89 98 5d 00 b6 3d ed d9 59 6d 05 16
d1 2a 78 c3 74 b7 ed dc 7d ce e8 e4 fd d1 6c 1d
fa bf f7 ff d4 c1 fd 61 ce 04 be 8e 49 75 c5 cd
71 e2 cb 0e 54 1b 84 61 bb 81 fb 28 cc e7 73 65
3e 8b 16 b2 8a 8c 20 74 28 89 5f 28 53 55 87 a5
c9 9d 46 ba 4d f9 ae 08 50 18 51 3d 69 ab a3 f6

# PKCS#1 v1.5 Encryption Example 14.10
# ----------------------------------

# Message:
04 2a 39 22 aa 87 1e ea 0d 78 42 2c e7 85 66 ab 
bb 5b 08 c2 dd f1 ee 30 cf 

# Seed:
5a e8 51 14 b0 02 7a 23 c7 2b db 46 ae 7b b8 87
be c5 ba d7 a9 88 4e 93 f6 f4 fd 0b c9 38 bc 72
41 0c ce 96 a1 4f 4d e1 99 19 77 35 05 1e fc dc
c1 96 f3 ad bf aa 06 3c b3 f7 a2 34 c6 cf 99 d7
0f bb 7e 35 b6 ae c6 64 14 66 93 91 e3 cb e7 21
ec 99 1a 1e 5d fb b0 38 f2 70 36 85 93 74 9b 20
8d 08 9a ae e2 ef 35 c3 da f6 23 8b 5f e4 2d 13
dd e4 07 df 14 f2 d6 18 c9 79 c9 7d 2d e0 29 33
b5 7f e8 81 22 04 86 2b 2f 1d ee 98 3f 24 c2 c5
96 ea 66 8e 63 7d 0a 6a e6 dc 52 65 27 69 94 e7
e4 f0 2b 6e

# Encryption:
12 44 de 88 0e 0f 78 52 e9 96 95 9d 76 2f ca d9
15 65 a4 d0 ad 3b c5 27 50 d4 a0 44 0f 0b 5c 65
1a a0 e6 f4 92 06 1b 2c 86 24 c5 2e de 68 58 fa
25 18 ae 8e 8b 11 65 58 b2 c8 07 6c 17 ae 78 3d
8d b2 5f 0d 8f b1 f2 75 8a 82 ab 97 1f a7 28 3e
f0 74 9a 37 be 28 93 f8 94 37 fb 8e a9 00 72 b5
85 5a 26 08 fc 54 2f 5d 2e 0c b5 43 f4 fa c5 28
f9 43 52 d0 16 40 fc 2c 53 1b 79 81 0c 00 77 7b
c9 e1 0d d9 ea 99 96 e7 40 87 fa dc b7 1a a1 43
00 67 65 71 61 48 82 94 3f 4a 56 14 12 c0 54 67
dc a6 6c a4 9f 82 29 35 18 23 db 8a 6b 9f 80 3d
70 9c 11 87 ed 74 10 cf 91 00 15 59 5c ea b6 3e

# PKCS#1 v1.5 Encryption Example 14.11
# ----------------------------------

# Message:
f3 1d 3d 0c 30 fd 65 d7 b9 8b 70 99 44 78 2e 20 
52 5c a7 c1 f4 2d 5d 03 a0 f6 d2 75 9d f1 91 9e 
ea 82 f8 0f 10 00 fd 5c f8 59 df 59 87 1b ac 82 
dd 90 76 cf 

# Seed:
04 4b c3 77 e8 58 9f a5 af 1c 17 34 7d 50 e0 cb
bf 90 15 76 f2 41 de 69 0c 88 16 c1 29 cd 9f 2f
b0 83 1a 01 7b ec 30 b8 2b 68 f6 98 31 1a f6 e0
77 72 bc d8 98 fa 0c 27 f6 2a fd e8 95 89 77 84
46 25 55 2d 9b cb 5a 81 aa 3d 74 15 b2 42 a0 3b
12 fc 1d 3f e2 d2 ce 6e 5f 71 c4 a4 a4 c7 ca 83
e0 65 6f 50 02 ac 36 d8 d0 2b 69 ae 65 73 44 98
f1 31 95 2c ea 48 1f a2 c2 96 5b 6e f0 51 7e de
50 dd b0 9b 3e a7 02 6d 06

# Encryption:
86 f6 6f 0c 25 05 85 06 59 43 de 2f 71 1f ae 4f
f2 62 70 0e 0d 33 06 a2 4c 94 30 f8 7c fd 93 de
f4 c3 44 7c c7 21 0f d9 4a 14 33 62 f4 f9 45 c6
db e2 80 bd ef 5d 14 f7 5e b7 bb 31 32 0b cb d0
d8 8f 0c cf 2c 95 a7 4c b4 58 c6 27 2b 58 bf 74
30 93 c4 bb a2 d7 be e9 ea 2d d3 0c f7 2f e2 93
c9 0c 97 43 0a 04 7b 17 26 63 91 c5 1f 5c 39 8b
a3 df 8c b7 4b a3 e3 72 f9 55 5c cc 97 d6 db 76
14 ea 06 d5 c4 8c 1c 60 06 13 3d 0e 9d 69 95 79
93 85 92 0a d8 af c3 de ad f6 31 ce cc 55 9c af
f4 95 b0 8d 68 3f b2 2a a6 97 d7 1c 69 6e 46 b1
bf 4f d7 6b 8d 0b 39 f1 79 bf 66 84 1b bb 97 07

# PKCS#1 v1.5 Encryption Example 14.12
# ----------------------------------

# Message:
6a 35 55 57 9d ad 03 94 35 43 ff 74 e1 74 7c 25 
7a 83 d3 52 94 c2 53 93 83 e2 35 de 69 

# Seed:
5d 92 43 4e db d4 f5 bd 27 19 71 71 f8 53 ce b7
26 30 30 83 ad 45 67 a1 d7 c6 10 4d 19 2b 9b cc
df d0 da ed a2 74 e5 cd fb 3d 0c 5d 19 c9 68 25
81 ec 7a dc 1a 87 e0 81 51 41 5d 5a 9a dc 1a f4
50 b1 ba 88 d0 ef 32 ac 2d 1f 8a e3 45 95 28 14
75 3a f3 8e 12 63 5c ff 8c 09 21 59 b4 e7 5d ee
a1 98 3e d3 d2 d9 ec 2f e7 b9 a2 e1 6a 14 1e 81
8b 84 cd 9b 71 c1 29 a8 b3 c6 db 62 02 32 dc 03
a2 40 1f 73 1f f8 a6 3d a4 58 a7 d8 78 90 56 25
ae e1 fc 09 4d fb 07 b4 57 5a 7f 0a ad 23 3e 82

# Encryption:
93 10 27 2d 12 4b c5 cd b7 21 88 98 40 aa 77 15
e7 67 32 17 00 cb 39 b2 b8 a5 a8 2a e3 f0 2e b9
67 e8 db 46 84 3b c1 bf 62 ed 8b d2 ab e8 14 34
49 7f 99 00 64 02 53 98 2d 37 2b 2b f7 c1 b0 9b
d5 01 96 74 a8 34 fb bd ff 35 68 a2 82 4a ed 4a
80 48 d2 b8 61 36 27 75 bd 5f 0d 63 b3 48 36 3d
13 78 69 1f 5d d1 d7 96 10 74 ed 95 fc 90 07 bd
5f 5c 29 23 c1 7c 42 90 4e 2b f9 d2 48 77 9f df
b2 03 97 38 41 a1 12 90 c7 e9 e9 35 6d 4e ab 17
0e 43 1b fe 45 4a 88 01 0d 9a ff 33 70 0c ee 55
c7 04 c8 2a 7f fb 15 e2 53 ef 84 f4 01 9e 12 43
8f 7c 73 86 dc 53 5b 19 ca 86 af 71 d4 77 60 8e

# PKCS#1 v1.5 Encryption Example 14.13
# ----------------------------------

# Message:
cb 79 af 5a ea fd f2 ba d2 1d fe 62 92 66 42 cb 
a8 04 ec 7f d0 ea 5d 54 08 ac e9 ed ff 28 e7 e8 
df ff 6d f3 83 af 14 40 21 46 04 76 c0 c8 2c 

# Seed:
5f ce a2 57 cf a9 2c 84 f0 b8 93 7d 17 3f ae cf
75 03 ab 16 2e 0b ae ef 7e 4c 51 1f 3e 32 a3 24
ed 40 e2 42 a8 52 ba 57 89 5b 7c fe 4d 61 79 61
e0 36 f6 63 e0 22 8a 29 cd 1a 95 ac ac 08 a2 55
26 d1 eb ff 0a b3 f0 33 ed bf 1a e2 76 a8 d2 36
73 6c c7 af 51 b5 d2 bd c8 3d cc 7d 7d 3b f5 b8
22 af 2e ff ed 7e fc b6 17 e0 83 e5 1b e9 94 ae
d6 56 9b 23 83 b9 41 ae e5 94 c7 b0 12 e6 75 1c
37 b7 a5 4e 2a 19 18 24 a1 30 d5 5c f8 45

# Encryption:
11 fa 81 9d 3a 63 88 04 ee f1 d9 56 0a 11 f5 23
0a 0b ad fd 66 eb 68 4e 7d 69 df a2 89 8c 8e 0b
6e 04 af 8e fc 70 61 08 1e c5 9e 45 85 76 42 e8
b2 00 41 af 50 8d 9d 4e 28 82 20 f9 fd 38 9d e8
b2 91 24 ce 74 7e b6 8e 2e aa 8c 8f 6f b4 93 f6
11 ac 09 b7 23 09 5d 07 ee d9 24 f6 ab 8e 09 ff
93 c5 51 6d 1f 0e bf 62 c5 f0 22 f5 bb 4f 4c b5
b8 f5 d4 87 a1 7d f7 d0 12 d7 04 35 7a bf 17 48
67 ce 40 cd c5 50 11 b0 71 39 be a4 5c a0 e5 81
78 0d e6 54 17 cc 83 5f f2 69 84 fd 0f eb a1 87
69 c3 94 a2 e4 85 02 3e 31 d3 b0 a8 88 a7 b1 4c
78 1d d8 5b ab 40 86 74 f5 f5 7e 4b 76 3d 84 35

# PKCS#1 v1.5 Encryption Example 14.14
# ----------------------------------

# Message:
d1 62 33 fc 77 5c 31 9f 15 7a a2 00 47 6c d6 ed 
64 a1 ea 

# Seed:
bb 33 b2 2b 5f 46 79 4b e8 3e 6a ff 34 a0 e4 11
d1 f3 f4 b8 da f9 b5 85 87 24 ef fd b9 69 c9 55
25 c6 2d c4 4b b2 b0 83 38 60 03 05 4b bd 36 66
b7 82 82 60 6f e6 ea 17 27 31 be e1 16 72 d6 01
de 32 42 3d 83 f4 63 cc 29 30 f5 fb 79 da 15 34
c4 15 c9 65 82 75 65 94 a9 99 b2 26 35 42 48 a0
9f 14 1c ae cf 88 83 90 78 f7 7f 40 bd 48 51 34
9c 1f c7 5e 1e ce 6f d6 96 6b c9 c9 d5 ec 12 09
6e d5 04 35 46 85 9e 4f 95 7d 31 88 e1 d9 06 0b
59 c1 f7 f6 2e fb fe 82 5d da 45 35 3e 6f d4 fa
ba c9 83 f9 44 f4 c2 c7 9c bb

# Encryption:
10 c2 0c 0c 71 b1 1b be ce 14 c8 14 83 dc fa 73
0c 23 d4 dd 61 b8 75 5b 39 94 9d fd 4a 3c 50 33
21 60 ee c6 f7 71 0e 09 c7 a9 7a f9 3f 70 44 b9
2f 41 d0 9f a3 e6 c6 ce 1f 64 11 48 4e d4 75 40
a1 b5 9e 23 c1 93 34 b6 6d 68 20 c2 d4 4d b0 f6
aa f8 5a a2 7f 53 a4 1f 85 6b f6 a5 91 36 59 86
9b a4 ab dd ec be 87 41 3a c9 5b e6 40 09 59 3b
5c a2 d0 78 3a 16 f6 08 66 57 55 24 53 93 fc 14
4f e3 ea 5e 9a ac 9f 1f 99 1a 92 85 38 b4 69 97
d3 06 3c c1 a6 9a dc 19 2a 40 c7 6f 92 d4 7a 05
80 e3 c0 21 20 02 3e ce 70 32 80 7b 7c 09 13 43
aa 87 3f 6a 4d dd e4 3f b2 dc c3 79 ae ee f6 54

# PKCS#1 v1.5 Encryption Example 14.15
# ----------------------------------

# Message:
d2 f3 c2 e6 f4 3b 0f c9 fc 22 93 b8 45 88 e6 61 
15 

# Seed:
b9 81 64 89 52 5c d0 26 15 6f b4 e8 a4 a8 f3 4a
ea 8a 3a a1 a6 34 cc cb 32 58 91 8f e8 5c b8 d4
aa 02 a5 28 52 a8 94 1d 3e a6 c0 48 b5 8c c6 c7
56 85 67 56 25 e5 e4 dd 7d f9 3b 8a ce c0 79 67
0b 49 f3 dc 6e 07 63 ed 4c 8e 2d 0e e9 b5 ca 5b
b6 2d e0 06 91 98 38 e4 b6 9b a1 05 d4 c5 a4 cc
6d ca 67 b1 21 93 a0 32 b6 92 73 96 44 cc cf 72
3f 9f 69 e4 8b 94 c0 bd ce 5a a3 5f 75 dc 53 92
7b 81 e3 42 ce 72 c6 b6 5c f7 5e ae ca c5 fe 0d
ea 93 88 5b a4 20 af 99 32 d8 4c a4 b5 0e 07 e3
28 ec 5f 81 6d 2d 86 96 b2 0d f7 5f

# Encryption:
80 f1 df 25 f3 6f 31 4b 98 2c 9c 8a af c0 b8 a1
a2 f1 74 ab b2 98 08 69 c2 9d 19 be 1d 2d 93 b4
fb 42 99 90 6c 35 7f dd 40 e8 9a 19 54 92 a9 79
76 61 f0 5d 38 71 d1 bd 0a 5c 45 d8 f9 b0 fe e5
65 b0 00 4f f5 af c5 a6 f8 9a d6 03 e8 22 83 57
0b db 4c 6e 0c fc 31 3e 4e 66 5a 94 34 b3 2f cc
77 3d 6e da bc e8 5f e7 c8 0f 03 30 2a 84 e2 08
b5 bd 0a ad 91 ce 62 fb 8c 2b f5 4b a6 6f 7e 8d
00 21 92 16 29 20 a4 6e 36 de a5 66 1f dd 75 81
53 56 40 74 b8 55 9f 88 93 62 42 fc 09 98 14 8f
19 eb 50 fc 11 fb 24 a7 ed 8c 83 49 65 8f e9 d3
1e 62 74 d4 5d 6f 2b 60 9b b5 cc d1 7e 28 4c 99

# PKCS#1 v1.5 Encryption Example 14.16
# ----------------------------------

# Message:
21 ee 58 12 e3 24 6d ab 9c 3c 25 9b 21 37 d6 5f 
98 a0 5e 57 40 46 5c a2 2c 69 34 97 00 a4 2c be 
4f fb 39 3f e2 81 99 33 9c 51 03 1c d3 b2 2f 2f 
0a 83 

# Seed:
e9 de 5b d3 55 e6 af f1 9f a1 1a 2d 0d 3e dc 3f
64 69 bd 3c 75 72 06 d6 6b 3f 09 90 8f f6 18 af
48 01 ac 77 b5 2c de 03 d4 ae 74 9d 02 15 5e 5c
70 fc 99 5f 48 76 72 d2 80 63 58 55 db 4b 64 a2
26 09 b0 c1 60 67 16 3c 51 90 42 05 7c b3 67 12
c7 c2 fe fe d1 1f 73 c2 8b db 9d 25 f0 63 6a 4c
aa 11 26 9e 5f e6 5a 2b 17 56 86 a1 5f 1e 48 d2
8d 34 5d d9 a1 b2 90 0a 24 f9 dd da 3d f3 a6 9f
fb 9c f5 04 5a c4 a1 93 ae 90 2f

# Encryption:
7d 8c 53 1d 4d 35 49 e0 bd 2e e1 62 b6 82 53 9d
a6 17 22 b8 8e cf 8c 7d f6 d6 b8 1f ef 50 18 bc
4e a1 0a 7e 1a 4e aa 02 15 d9 b3 cd f4 13 47 92
9e ac 27 48 eb d7 79 94 5c 9b c4 61 dc 51 f4 8d
f6 52 75 53 f0 70 37 e5 33 ce b1 34 8a 46 a7 ea
79 7d 85 a2 6a 9f 44 c5 88 69 99 6e f1 14 69 bc
c1 0b 75 6c 02 d5 c0 e6 18 83 68 5c c3 7d 75 8d
fd e4 c9 b7 35 4e 3b 4f 31 6c a7 f7 fd e6 59 c3
fd 5e 33 2e 1b 63 92 a2 92 9e 13 17 66 ce 9b a1
d9 71 ad 24 6f 3d f0 22 43 38 63 8b b6 53 45 8c
d4 b5 26 d9 61 74 4d af ec d5 99 8a d7 2a ed 3c
34 59 9f 7a 40 98 e3 d2 df 9d 13 a2 1c e2 37 0c

# PKCS#1 v1.5 Encryption Example 14.17
# ----------------------------------

# Message:
a0 34 a6 c1 66 cf 0b 25 d2 dd e5 3a f4 b8 33 b4 
78 c6 b0 d2 fb 0c ef 13 7f bf 5c 27 12 70 64 91 
23 7f 7b 28 6d 12 11 d5 73 10 f8 a7 62 b1 b3 bf 
e1 9c 9a 4b 16 d3 e0 a8 

# Seed:
d8 44 17 97 22 87 13 0a 24 a6 06 f5 83 29 7a c9
11 52 8a dc db bd 7d e1 4a 5b 48 9b 67 86 f9 f6
f7 e0 b7 3b ab 53 8e b6 c4 5f f3 4b d5 dc 43 ea
e8 d8 c4 3f 71 65 16 a6 0d a2 47 53 6f 63 4b e0
65 d9 4e 7f 92 ad f5 2a 96 7e e0 5f d9 af d7 32
33 3f 99 ad 05 82 97 b2 8f 8e c6 fe ff 80 28 44
a0 09 7d f9 1a 97 70 2c 48 3a a1 c7 89 2c 7d 43
b6 b9 1c d4 d8 5d 3e d2 f1 e9 55 39 57 06 c3 b3
39 ba f2 a0 e0

# Encryption:
90 4e 04 07 23 ab 97 88 a5 ed 03 52 eb 96 c7 f3
d7 07 cf 0d bc 25 8c 51 dc f6 24 34 06 f0 c7 42
c6 cd f2 07 67 13 2c 09 5e 6c 82 a5 02 5b e7 b4
13 4d 8f a4 de 18 7f 8e cf 12 fd d3 be ab db 06
15 8a ef 46 c3 fe d2 d1 83 34 06 ec a7 a6 9e b2
d2 08 a2 f6 f4 40 d5 4f 6b e5 dd 56 47 09 ed a8
12 ac 06 29 a9 4c 7f 8a ee 78 be af 9e 93 78 c8
dd 9c 62 03 74 bb ac a3 94 18 59 dd 70 2f 6f 7b
25 a2 38 45 9e e8 97 59 de 94 22 b5 bb 6d 28 57
16 6c a2 12 0e 63 47 74 a0 d1 d4 2e b8 d9 48 15
a1 04 4b ea 8b fb 02 da 58 62 cd 9a 74 5f 15 92
47 8c 6f 57 bf d1 3a 5f df 4b 8f aa e8 a4 bf c4

# PKCS#1 v1.5 Encryption Example 14.18
# ----------------------------------

# Message:
8f c6 39 4c d6 e1 75 33 d1 ff 8e bb f3 e1 ae ae 
a3 30 ed 9f 5a 6e 1e fb 83 45 42 94 c6 ce 24 f6 
90 4a 0e 

# Seed:
be 1c 18 02 db 44 68 2e 58 c6 1f e3 87 57 ea 0e
38 4e ba dd 79 59 48 4b 38 bb 23 57 55 c4 61 77
e6 71 76 9f 36 57 3d 7c b0 ee 7e 82 08 7b 58 4b
58 bb 30 05 30 33 68 c6 59 0a d9 f2 88 2c fa 74
0d 51 dc 55 eb 0c 79 0f 5b b6 b6 a3 bd 71 f8 b2
14 6b b9 e8 03 4c 35 b7 ca bc be 10 93 6f fc 5f
0a 8d 7b 30 47 6a b9 16 85 ae d8 fa 95 8e 73 c1
ee a3 04 4c 56 b4 b8 70 da 89 37 1a 93 b8 96 52
79 b5 5b b9 2c c3 16 c2 3e f0 97 53 51 c7 49 81
7d b5 dc d8 6b 94 ad f6 03 bd

# Encryption:
b8 3c 71 8c e5 c6 ab 1e 40 ff a5 67 0a c1 66 66
4d 3a 68 33 cb 3b de f4 62 79 4a 25 d5 3e 17 04
60 06 85 8e 63 f8 8a b9 5d 04 f4 fb 67 74 f7 00
5d bb 2e 22 d5 19 36 0d 5e 13 38 ad 15 30 8f 6c
64 12 54 95 67 00 7e 02 1c b2 37 ed 4a f5 ee f3
bc f9 b7 31 59 9f 72 53 ce ba 83 04 f4 ee 8c 34
33 39 d0 a0 64 eb 77 f1 e9 3f d1 cc 7b a5 fc 3b
b8 18 4c ed 0e 86 97 ac 47 b7 46 20 c4 94 ea b5
86 ed 9f e7 6e 07 e4 bd 2a 2d 1b 95 59 5b 69 c6
46 77 a8 83 5d 56 ac 63 9d f8 f6 43 24 1b 3e 2c
46 88 d2 ad f2 28 de 2d a9 bb 0a 36 34 38 e7 51
b5 25 0d ea e3 05 a3 90 5e 7e 07 7a 4e 8c 74 66

# PKCS#1 v1.5 Encryption Example 14.19
# ----------------------------------

# Message:
89 a1 2d 22 bf 87 7d 44 0a 2e 03 ae a9 32 eb 51 
83 37 9b 3c 8b 90 be e8 fe d6 fc 6d af b0 cf 05 
27 

# Seed:
60 72 89 5e 47 80 c8 f7 7e 0b 19 5f c9 f5 db 78
33 f7 b6 f5 c8 1c 1d 30 cb e9 e8 0c cb 38 66 06
9f 8d b6 96 3a d4 6d 52 94 2a 5a 73 f6 a3 27 a9
4e d1 19 df ce 4c 37 65 84 37 13 b6 19 f9 c4 38
32 03 d5 5e 2a b6 1d a8 96 1a 81 03 7f 11 18 a7
82 97 eb 36 6a 1c 51 d9 f9 46 6b 71 5b b6 29 99
e0 a9 d6 e0 25 01 d4 7f 97 db 40 9e 38 86 e1 36
6c 3e ff 2b a7 90 e2 62 43 22 7a a1 58 80 82 bc
e6 d5 cd e3 ea 7e fb f1 5c 6c 7d fa 54 54 72 fa
9e 93 95 6a 45 51 d5 a7 7c a0 0e 26

# Encryption:
b7 ac bb cf f0 fa 9f ca 6f 0b bd e5 a2 f0 a1 e6
a0 ab ad b3 2c 89 e3 17 25 5b d1 8d 12 e1 a6 0b
3e 00 2c 1d 69 35 64 48 32 9a 49 b5 bd 24 19 10
fa 0c e0 3b 3e 68 a5 90 50 75 99 39 1e d1 53 97
92 29 3f ef 13 17 4d ac ea 6d 2a 05 b3 9f 68 48
28 a5 d6 d2 17 f8 4b 78 2c 8f c9 84 3c af 6a eb
17 8f 0e 2c 6b d2 a0 e6 f7 b8 09 29 61 17 cc 8a
6b f7 37 35 84 64 24 ad eb a1 ab 31 14 5f ca 3b
8a f7 68 c9 d0 a2 8e 09 d5 8a d4 96 42 31 08 f0
8c aa c1 74 ac d1 f3 ac 43 48 69 61 c5 c9 0e 1e
fb 89 bd db 7b b3 9f 4c a3 af 57 12 f5 53 af 59
4c d5 d3 64 13 29 14 26 10 07 aa 1d 5f 21 6b 8e

# PKCS#1 v1.5 Encryption Example 14.20
# ----------------------------------

# Message:
09 d6 94 8c e1 c1 f2 4c 6d 52 9c bc 5d 6d 6c 1b 
ea ab 56 95 b3 0c aa b7 44 96 9b f7 f9 db d2 83 
33 5e 98 a9 bc d6 5d ad 2b 4f 0e 3c ec 89 0b 05 
e6 7f e9 78 23 d4 

# Seed:
ed ed 62 a6 50 f7 de 3b 12 80 22 9d 5f b1 c4 40
8e 82 df e0 31 7e 64 30 fc f4 71 b1 e2 8d a8 cf
6b 4b f4 c5 d0 31 e3 ee 68 8b 64 07 72 aa 50 08
e5 86 3c 70 7a 40 22 c3 29 a6 66 4e 71 10 12 06
b5 4d b5 40 6e 7f a9 c8 22 6f 42 93 11 19 62 0c
a3 0d e7 d0 3b e1 43 94 0c bb 4d 1a 9c 86 e9 86
3c 98 7b ff 07 02 3d e1 98 64 f7 33 da 0c 89 bd
03 9f 19 f4 d8 ed 61 6f 7c 6e 94 94 18 5b 60 4a
1a 7b f7 49 0f d0 d8

# Encryption:
7a dd b0 5c bf 0b 17 aa 50 8a 0b 17 0c 4c 5a ee
84 ce 06 65 0d 08 c9 96 6d 95 d3 07 1a 9a 8f 3a
93 f9 6a 87 53 99 b4 78 c2 56 d0 41 5e 74 84 95
98 21 1f 9f 9d 0b 89 36 7b aa af 17 4b 7f 13 d8
49 0b aa a7 40 96 1f 52 d7 e3 53 c5 04 81 8a 00
0b 03 67 4d 9c e4 93 dc 3a 4e e9 16 13 01 f6 1d
e5 21 ae f3 f6 d4 1d 82 e1 c5 dc e0 2e 63 6e 77
40 a1 83 f8 02 3a d2 61 49 41 d9 b1 61 cd bd f4
8f 8a 56 2c 8f fe 44 f5 7a 74 6f 26 39 e5 e8 3b
cf d3 92 d2 3e 1f b4 a8 d8 5e 3b a5 e1 cb 9c 0e
53 f0 d9 1b 01 ce c0 f0 ef 9d fe 3f 2b 30 65 bd
55 b7 2f b1 70 60 ab e8 83 0a ca 00 44 64 fe 7d

# =============================================

# Example 15: A 2048-bit RSA key pair
# ---------------------------------------------------


# Public key
# ----------

# Modulus: 
dc fa 10 ff a7 46 65 ae ef 87 09 74 ea 99 b2 ce 
54 54 7c 67 f4 2a aa 6d d0 1a 2e d3 1f d2 c2 42 
af 5d 96 0b 1f 89 6e fb a3 54 3d 65 54 b7 b1 26 
87 a5 c6 88 56 8f 32 e0 26 c5 32 d2 59 93 b9 7a 
7c 28 42 ec 2b 8e 12 35 ee e2 41 4d 25 80 6c 6f 
ba e4 38 95 4e ba 9d 27 55 df fe eb 1b 47 70 09 
57 81 5a 8a 23 3f 97 b1 a2 c7 14 b3 e2 be 2e 42 
d8 be 30 b1 96 15 82 ea 99 48 91 0e 0c 79 7c 50 
fc 4b b4 55 f0 fc 45 e5 e3 4e 63 96 ac 5b 2d 46 
23 93 65 c7 f3 da af 09 09 40 0d 61 cf 9e 0c a8 
08 3e af 33 5a 6f ce b6 86 3c 1c c0 cf 5a 17 1a 
ff 35 d9 7e cb 60 ef 25 1c 7e c2 c8 a5 88 36 1d 
c4 12 66 a4 b7 ed 38 b0 26 ce 0d 53 78 64 49 db 
b1 1a 06 ea 33 cc f1 ec a5 75 20 1e d1 aa 47 3e 
d1 18 7e c1 d8 a7 44 ea 34 5b ed 7e a0 0e e4 e8 
1b ba 46 48 60 1d d5 37 dc 91 01 5d 31 f0 c2 c1 

# Exponent: 
01 00 01 

# Private key
# -----------

# Modulus: 
dc fa 10 ff a7 46 65 ae ef 87 09 74 ea 99 b2 ce 
54 54 7c 67 f4 2a aa 6d d0 1a 2e d3 1f d2 c2 42 
af 5d 96 0b 1f 89 6e fb a3 54 3d 65 54 b7 b1 26 
87 a5 c6 88 56 8f 32 e0 26 c5 32 d2 59 93 b9 7a 
7c 28 42 ec 2b 8e 12 35 ee e2 41 4d 25 80 6c 6f 
ba e4 38 95 4e ba 9d 27 55 df fe eb 1b 47 70 09 
57 81 5a 8a 23 3f 97 b1 a2 c7 14 b3 e2 be 2e 42 
d8 be 30 b1 96 15 82 ea 99 48 91 0e 0c 79 7c 50 
fc 4b b4 55 f0 fc 45 e5 e3 4e 63 96 ac 5b 2d 46 
23 93 65 c7 f3 da af 09 09 40 0d 61 cf 9e 0c a8 
08 3e af 33 5a 6f ce b6 86 3c 1c c0 cf 5a 17 1a 
ff 35 d9 7e cb 60 ef 25 1c 7e c2 c8 a5 88 36 1d 
c4 12 66 a4 b7 ed 38 b0 26 ce 0d 53 78 64 49 db 
b1 1a 06 ea 33 cc f1 ec a5 75 20 1e d1 aa 47 3e 
d1 18 7e c1 d8 a7 44 ea 34 5b ed 7e a0 0e e4 e8 
1b ba 46 48 60 1d d5 37 dc 91 01 5d 31 f0 c2 c1 

# Public exponent: 
01 00 01 

# Exponent: 
21 95 08 51 cd f2 53 20 31 8b 30 5a fa 0f 37 1f 
07 ae 5a 44 b3 14 eb d7 29 f5 dc b1 5d a7 fa 39 
47 ac dd 91 5d ae d5 74 bd 16 df 88 bf 85 f6 10 
60 b3 87 17 2f ae 6e 01 26 2b 38 64 c2 d3 c2 2f 
94 e0 4a 81 59 42 2b 4e d2 79 c4 8a 4c 9d 76 7d 
49 66 07 1a 5b bf 5d 04 3e 16 ff 46 ec 1b a0 71 
6f 00 bb c9 7b ff 5d 56 93 e2 14 e9 9c 97 21 f1 
2b 3e c6 28 2a e2 a4 85 72 1b 96 dd cf 74 03 fa 
03 7d 0c 57 ab 46 3c 44 8d e5 cc 12 26 5a dd 88 
6d 31 1e a8 d8 a5 90 3f a5 6c 5f 1c 9c f2 eb 11 
cb 65 7a 1a 7d 3e 41 35 2d c3 e6 86 89 8c 4c e4 
30 5e 8b 63 8e 1b 08 a2 a8 6c c9 eb 98 66 f3 49 
9a c7 7b 61 36 b8 1c b2 76 d6 14 cf eb 7b 6e d3 
f3 bc 77 5e 46 c0 00 66 eb ee e2 cf f7 16 6b 57 
52 05 98 94 7f f6 21 03 20 b2 88 fb 4f 2c 3f 8f 
e9 7b 27 94 14 eb f7 20 30 00 a1 9f c0 42 48 75 

# Prime 1: 
f1 23 bf e5 3d e9 7a 56 9d 91 ad cf 55 6f a6 25 
ad 30 f3 fd 3d 81 1f 9e 91 e6 af 44 b6 e7 80 cb 
0f 32 78 29 fb 21 19 0a e2 80 66 46 d7 28 cd 9b 
65 31 13 2b 1e bf ef 12 72 99 30 60 f1 ce 70 b1 
24 39 30 91 ee 85 93 b7 27 36 7e db ba 00 9e c5 
be 17 c4 ac ee 12 0c 84 12 67 d4 76 31 a1 6c 36 
a6 d1 c9 99 73 c1 b0 b5 a8 35 bf 39 fe af e8 f6 
42 1f d9 c2 a9 0b c2 79 76 65 9e 67 bc 83 12 4d 

# Prime 2: 
ea 98 39 b7 e3 7e a8 9b bd a2 7e 4c 93 47 1c b4 
fd 92 18 9a 0a 96 bc b4 d7 56 93 f1 8a 5c 2f 74 
2a f9 e3 6f de 67 9f bd 9e ae 34 5f a2 69 52 7b 
69 65 02 1c 4b df 54 d6 85 bf 08 96 0c c9 76 f6 
8d ca 21 ce bf 44 f2 68 a5 9d ab 8d 1a 25 e5 19 
f5 14 7e 1f 45 fe 28 7d 74 cf 72 5b ec 13 26 d3 
42 12 c5 6c f4 ff fa 20 2f 57 b6 8e e8 cc a9 43 
f3 c1 38 c4 cd e3 3b df 2c 94 40 df 65 32 24 45 

# Prime exponent 1: 
ca 0c 9b 60 b8 e4 a6 06 67 56 c6 5d 20 88 41 9d 
f6 25 3b 7b 68 8a 85 f4 f6 e9 64 d8 5d ad 52 a4 
52 62 86 7f 1e 96 18 06 9f cc d8 65 e9 28 9e 46 
e3 9e 20 22 94 4c 5c 44 87 d3 45 cf 25 2d 46 0d 
97 7d 77 ed fe fe db cb ae 46 a2 3a f7 fa 47 0f 
07 7d a0 e5 09 42 04 4c b1 a3 60 49 7c c2 76 0a 
c0 f2 ad 4a 2f cd 0e 84 d7 a1 d9 4d fd d2 65 8f 
d9 ce 18 47 5c 1f a7 5e e0 ce ba d0 cf 0a c0 4d 

# Prime exponent 2: 
52 81 71 23 3c 4e 4a 6c 63 b8 67 64 f5 13 38 84 
6a fd db cb 29 58 34 4c 01 c4 00 4a 1d d8 28 14 
5a 1d 02 a1 50 7d ef 4f 58 24 7a 64 fc 10 c0 a2 
88 c1 ae 89 57 21 d7 8b 8f 04 4d b7 c0 0d 86 da 
55 a9 b6 54 29 2e cd 76 82 70 be 69 e4 bd 59 22 
d4 ef fd 1f 70 95 5f 96 27 e3 e1 9b 74 9e 93 b4 
0e f3 dd 1d 61 d9 39 15 e2 b0 9d 93 0b 4b 17 68 
bf ac c0 13 6f 39 b0 cf df b4 d0 50 01 1e 2e 65 

# Coefficient: 
df 2e b2 32 2c c2 da ab f4 d1 46 55 08 f4 15 21 
cd a7 ce ff 23 eb e6 1d 00 d4 41 ee 72 8d da 5d 
16 c7 bf 92 0c d9 5f 34 be b4 fe 32 ee 81 7e f3 
36 2e 0b cd 1d 12 45 f7 b0 77 93 ea a1 90 dc 5a 
37 fd af 4c 68 e2 ca 13 97 2d 7f 51 48 b7 96 b6 
fb 6d 7a dd a0 7b d2 cd 13 be 98 ce be d1 ed c6 
ca 41 2e 39 53 50 c5 9a 1d 84 2b c4 aa 2f 3c 0b 
24 3f de 7d fd 95 35 6f 24 39 25 1a 11 72 c4 5e 

# PKCS#1 v1.5 encryption of 20 random messages with random seeds
# ---------------------------------------------------------------------------

# PKCS#1 v1.5 Encryption Example 15.1
# ----------------------------------

# Message:
2a ac ec 86 f4 23 dd 92 5e c1 58 82 2a 74 8c be 
6c 31 a0 

# Seed:
cc 4b 87 f6 74 49 7b b0 e3 3d 9e 2a 4a 80 70 b7
d7 8b 5f d2 c4 b4 f6 eb ac cd 4e e5 05 b7 1f ca
fe 21 56 33 7d df 27 b4 75 af 33 f6 c3 40 5b 8e
3c 0c 20 6e c2 81 29 22 fc d8 a3 66 1b 86 19 bb
c1 82 f8 07 f3 a1 07 2e 62 ca 2b f1 fa 8b 94 4e
58 a0 e2 03 db b7 53 f9 f1 b6 ef 62 7e be e5 98
96 7b 38 7a 5f 96 36 d8 b6 41 b3 89 84 b1 ca 03
7e 3a ae aa 17 10 f5 16 25 ea 85 f8 fb 9a 6e 02
9e 64 57 58 14 d5 30 fc 14 6b 34 45 ac 42 01 b4
e4 08 ad f6 55 f6 78 43 d8 87 1c ac e5 d9 06 d7
fc 03 8f ea 88 5b 96 fb 8e b1 a7 21 c6 c1 4a bb
eb 78 fb 4c 79 8a 19 58 99 59 89 84 55 a3 16 84
3c 6c d9 9e f5 8c 2b 0b 49 b8 ab 41 91 b4 02 a5
4c 92 97 31 0c d2 24 b1 7f 21 41 67 72 5c 48 fc
c6 1b c4 7c fa cc f1 5e b3 b0

# Encryption:
60 42 e7 45 58 9a f0 3a f8 75 20 f9 3c 45 d8 c3
59 85 ad a1 16 1a 37 d8 22 e9 f9 46 0f c7 5f cf
01 79 d8 49 1b 8f 5d 1e 4d e8 ce b3 1e 07 c4 86
5c 5a 3e fd bb b6 9a 88 03 b8 9e e6 5a 43 0a 58
09 c7 07 56 91 50 b5 80 bb 68 6a 94 c5 54 1c 46
ad cd 82 79 60 ce 24 4f f6 88 38 7d 16 16 e8 5b
4d 17 80 c6 48 36 06 cf 92 4b 54 f0 80 cf 41 54
e6 68 29 bf 6e 53 24 81 04 8e c4 1f ad c0 7d 75
5b b3 4b b2 81 45 21 9c b3 0d 47 d0 d6 18 70 91
80 e9 03 03 ff 9e f0 90 18 be d3 da 75 76 1d a7
94 81 1f 96 bc 9e 8d 7c 4b a1 b5 94 6b da 0b d3
13 fa ec 4c 99 3e d2 74 8e ed 8c ce 4b db 52 0b
a7 db 16 5f 9f e5 6a a8 45 4d 6f f3 38 74 fe ee
bf 29 de 2d f5 b7 f0 0a a1 d9 fb 07 3f c4 06 7b
58 dc 50 62 4e 12 7f 71 1d de 2c c2 cf da b4 91
9c cf 28 c8 36 60 df c2 27 b0 f5 00 ec 1f 90 4f

# PKCS#1 v1.5 Encryption Example 15.2
# ----------------------------------

# Message:
5c 8b f2 ac ab 08 bf fe fa 64 80 95 2b 24 da a5 
01 9d 12 5f ee 

# Seed:
5e 16 30 70 ef dd b7 9f 47 64 f8 a8 1d 44 46 0b
5c 40 0b ec 70 37 52 29 20 f7 72 95 9f d4 cf 3a
ef 2f 14 45 4d cd 9e 86 25 12 ca 69 db 83 68 a4
cd 8d 1a 44 da 59 5d 6b 43 93 91 c9 31 46 b1 23
f1 86 08 3c 4b 64 47 bf 7e 20 81 51 46 ac 75 49
ef b6 74 60 e8 ff 1b 2b ba 5c 95 a5 1e f8 13 d5
dc 4e 6c 38 92 bc 4f 43 9c 99 11 7e d0 6c 14 a6
c5 40 fd 4c 65 d1 95 d8 c6 1e a7 79 68 38 e5 a5
df af 11 d0 71 3c 19 1e 8a 0b 80 80 f7 a7 7e 70
3a b3 66 22 f1 c6 48 b7 65 43 5b 90 27 97 18 11
b1 15 2d 97 2f b7 6a a8 92 05 03 3d 95 78 18 7a
e6 34 88 fd a3 c8 6b 2f 28 e7 79 ac 4c 89 cd 25
20 17 d1 a9 95 8a 52 c5 b8 7e c1 bf 9c bd f7 de
0e 97 c5 8b a1 1b a3 a3 37 05 f3 f4 99 58 9a 3a
72 e2 c0 fc 5b 16 fc a2

# Encryption:
44 e6 71 e0 3b b6 67 80 ec 05 86 d5 6f 8f 6a 49
41 5a d4 bb ce 22 6d 75 d7 0f 06 ce 29 de ea 7d
a1 af a8 28 7e 44 36 3c 51 0f 34 eb 8b f3 1c a2
47 29 59 26 9c 18 df 09 36 ff 12 c6 16 6f 4f 45
96 cb 1c ae c4 1d ed a8 c5 09 99 bf 4c 94 4d 21
37 5b 36 75 31 91 b4 cb 7c aa 1b 43 e9 11 6c bf
1d a8 b2 01 d2 97 a4 d0 8b b0 e5 bd c8 95 32 70
f7 c2 80 96 78 c4 4b ea 75 e8 1f ac 22 d2 71 06
30 2b b6 9d a0 74 b6 ef a6 68 8c f8 35 c8 0b f5
e4 55 35 28 ec e0 b7 c1 b7 7b 66 6e a3 45 23 ec
1f cb 3e 25 05 4e 0b b8 e4 ba 02 7e 5c 21 bf 7a
51 43 bf 04 1c e9 cc bc fa fa 87 80 82 fe 41 f7
8c 70 bf 4e 53 cf 48 7c 1a ad b0 19 15 ce dd e8
cd 9f b8 4e fd 98 1a c9 8c d5 7a 82 56 d4 e9 e2
d0 86 2d ab 04 54 d3 ff 4f b9 85 26 4a 46 99 5a
b0 68 a7 4e dc 7e d8 ae ff 5f a3 0f 3a 7d 75 94

# PKCS#1 v1.5 Encryption Example 15.3
# ----------------------------------

# Message:
e2 00 4b 31 07 39 98 2c fa 9e 95 45 3a 

# Seed:
e9 3e 64 12 33 7a b7 b0 f1 b5 69 80 1a 3c 16 4a
6f 23 e3 c2 7c 7c 55 a8 c5 ac 9e af 31 88 55 f8
32 8b 5d 7a b4 cb 86 19 a0 0e 99 41 cc aa 94 85
70 52 71 82 61 74 43 c1 d2 1e 4a 6e 21 b3 f6 d4
f6 98 a6 1a e0 29 17 2c f4 da 03 9d 91 58 5a 87
da d1 28 c2 fa c5 53 1b 2f 45 dc ef 9b 9f c3 31
c8 04 48 85 28 84 7c 90 87 5d 19 07 5f ff b0 05
76 3d 88 e0 81 47 66 92 28 a9 aa d0 16 25 bc 61
11 2c c7 b7 72 f3 21 d4 33 d4 f2 69 78 20 9d 0e
79 67 6a f3 b8 a7 4b 97 3f 52 ab 91 90 85 f3 52
35 5f 85 6a a4 0f fa bc e5 43 d4 e7 6d 45 48 92
98 9c b3 83 f3 e6 6b bb 0e 8f ee bf f7 c6 a5 4e
f2 62 5f c4 05 0e 6f 87 a3 23 13 2a 4e 67 12 68
fb 83 cf f2 d8 22 51 b7 9c be 32 da a8 e5 53 20
2f e8 87 22 61 f6 0d 5b b5 11 f2 fa 2f 14 21 a3

# Encryption:
2b bf 6b 0c 5c f2 0e f2 f6 c5 a0 aa 48 45 4f 85
0a a5 f6 bb eb 03 0d b4 e2 be c1 1f b2 00 f0 1e
4e ae f0 44 d8 14 33 33 33 8e 5e 66 38 00 87 66
0e d0 17 3a 76 82 12 85 67 7e 37 1f 28 ec 45 00
f4 d5 9f ab ab 20 73 e7 34 36 5f c6 b0 94 ee 0a
db ce ac cf e2 49 88 ce 61 5d 60 5f c3 40 8c 03
be 22 1c 99 3f 61 aa 72 4f c8 71 4a 8a 4a 18 15
f9 e9 a9 98 82 aa 46 88 3e 70 47 4e 33 29 b9 91
e6 d5 3d c6 b5 00 86 19 92 34 3a 6d a8 9a 8b d6
f3 7f 34 e5 de ee f8 0e 7d 56 b9 3a 45 17 60 66
36 50 fa 45 5d 55 41 89 9a 76 aa d1 c6 27 5e c8
2c 46 07 12 26 59 b5 08 cb 5d c0 26 ac f9 3f a0
1a 5f ca 3d 81 c1 bb 20 a5 a5 cf 35 7a 23 c9 56
88 ea 42 eb 1e f2 c9 d4 6a e3 7f 8c be 61 5c 20
84 d9 89 b8 92 f0 16 7b 23 be 33 62 fa ce 80 8d
6a 5e b8 96 19 44 08 db 7c 01 63 9c 58 62 30 4c

# PKCS#1 v1.5 Encryption Example 15.4
# ----------------------------------

# Message:
db 6a f1 29 23 05 27 8c 5b 33 83 f8 a4 1d 6c 83 
52 21 14 c9 88 85 50 74 06 5b 23 f9 fe ae 8e a4 
31 fd 5d a3 6f 9b ab f9 dc 61 df 2e 39 23 47 83 
04 73 38 ec 4f 

# Seed:
cb 85 1f b2 76 a7 49 1c d3 ef e4 d3 39 50 16 c1
ec 2b 15 09 4a 1e c6 d9 30 d4 ca 21 b4 20 f8 47
ff 68 68 f0 14 d2 09 ff 80 7e 8b 1f 71 67 0b 32
50 94 c0 f6 e3 2f 84 f7 68 22 22 02 b2 1b e3 6a
28 6b 30 e0 82 ef 3b ba 64 7c eb ee af e3 10 69
44 18 d7 0a 67 9e b2 01 07 80 dd 0e 96 55 3c 43
cb c6 d0 0e ac 22 aa 71 f2 48 21 c4 d6 c1 77 8e
78 6c d8 c7 bf 2c bb f2 14 e2 03 e2 ef 2f 33 35
78 cf 1a 94 7e 27 e5 99 96 29 0c bc d6 ca 3f 8f
96 ba 67 e0 e3 41 30 cf df 86 ef 48 e6 7c 90 b8
b7 2e 6f 42 55 01 7d a2 d1 f3 ae dd 7f b1 d9 de
42 ef c0 37 ab e6 61 6e bd a8 d0 b4 0b d7 80 cb
db 68 ce 54 31 8f da fd

# Encryption:
c9 9a 9a b6 7c ad 0c 41 ec 84 7b c2 74 67 fd f5
bf 61 ed 6d 04 c6 5f 7d 9d d8 bb 70 07 a8 a9 b8
3a 3c 38 a9 cb 92 5e 3e 7b 3c 40 7d 64 66 93 15
b3 54 49 e7 54 28 ae 96 19 14 b0 b9 10 23 e7 83
19 1f 95 41 b6 78 65 97 1c 95 b0 be 18 93 1e b1
c8 47 c2 6a 29 83 c0 58 4e b2 17 c9 9c 70 5f 5a
d8 cb b0 9f 99 b0 6b df 7b d1 26 28 ae 36 67 c1
2c 72 27 d9 6f f9 c1 08 dc e3 22 51 06 f6 2f 9a
4a 3a 81 17 a9 92 f2 88 c3 b5 97 91 09 87 8f cd
59 c6 79 6b 19 98 48 2e a8 2f 30 1c 93 91 83 b2
dd 47 88 04 48 67 8a cb a1 2d 7b c8 a5 52 eb 32
76 34 e9 2d 0c dc f7 1e ff b6 66 ad 90 2d 9e 26
ad 18 60 e2 92 81 d0 2f b0 c5 49 3b f7 4a c0 2c
94 40 43 6e 0d 75 32 28 92 77 7d 32 5e c8 45 2d
e7 58 cc 6a 5c bb 02 d3 41 f4 5c 9a c8 ed f1 50
da d7 15 82 dc 77 95 8a 85 44 b0 b5 58 ee 2a 0b

# PKCS#1 v1.5 Encryption Example 15.5
# ----------------------------------

# Message:
e7 99 e4 eb c8 69 31 9e e2 25 80 

# Seed:
3a 07 57 cb 49 a3 fe 6a e8 0d 37 42 06 8b 5c 80
68 b8 c5 88 54 20 01 10 93 c2 20 99 ed aa db 49
1f 22 6f 85 60 66 16 3a b5 10 5e 78 79 c7 49 1c
18 35 98 5d c4 94 21 0e f7 8b ad 48 16 d9 b6 94
c9 c8 be 46 6a 4d 17 8a 7d de cb ce 53 65 40 0d
68 21 b6 e9 ae 0e 03 bb 69 ad c4 ec 73 4a fd cd
ea 6d c0 c4 85 96 c4 8b 27 35 ef 70 f3 73 4c 18
6d 03 18 37 8d 2b f8 09 96 88 67 2f a3 85 91 da
4a e6 fc 11 2b 72 7e c8 19 84 df 7b 56 c9 84 4e
25 b0 fc d8 1d 2b e7 d1 8d 01 64 6a 6d 9f e2 25
d3 69 7c 34 ed 2e 33 6c c0 37 3d aa 28 d8 82 e4
97 75 7b 0a 65 10 86 2e ac 10 d3 b2 4a db 25 2f
30 d4 34 e3 0a 63 76 46 9d 80 b9 57 11 16 17 78
df 35 88 9b 3a cf b1 f6 53 ea 63 07 2f 35 a3 c8
9f 6b a5 2a fb bd c2 8f 23 d3 8f 4d 4e 79 fc 39
7c 10

# Encryption:
3a 3e 72 5c 6e 4b b0 06 12 69 61 8c bb 8a 62 67
d9 55 83 ab aa 03 d8 df 85 a4 f6 a5 cc 35 9b f1
15 26 0d db 70 ae 7c 66 bb 8d 87 d6 33 1f f1 b0
b5 4b e5 64 8e 83 e8 3a 91 c5 4c f3 71 49 6e e5
ca 02 73 b1 9f 70 03 70 c2 c8 cd f4 28 13 38 a6
e7 2f 66 32 41 68 ea 8c db c6 4c 60 9b a9 67 91
c7 91 b5 ac 84 00 99 2a 8c 66 fb 09 f4 e3 13 07
49 12 af 0c b7 41 5f b2 15 eb 97 fa eb be f1 a4
47 23 9a 91 db 4a 34 fc a4 d8 43 46 25 9e e0 a1
da dd 10 b7 f0 1f c8 c5 3e 42 0f 88 cd fa 0c bc
f6 2b e4 4b 8a 79 40 86 4f f8 13 7e db 12 2a c2
da e5 41 0a 47 cf 07 62 e2 0f 20 86 cb f6 6f f4
d1 a9 f7 27 0f 00 9c b2 e0 7d 90 20 b4 8a 76 d2
41 08 e9 98 9b f9 04 e4 a7 1d db 91 74 0a d7 e5
d1 b6 8e c6 4e 3e 66 cd 0e 89 7d cc 66 57 39 eb
ac 45 19 93 f0 2c c5 bf c6 3a 60 2f 55 83 81 da

# PKCS#1 v1.5 Encryption Example 15.6
# ----------------------------------

# Message:
09 97 12 b8 26 ba 67 cf 92 92 25 bd 61 2e c0 65 
d4 51 ed e2 31 c8 d5 c2 04 04 d4 70 e7 9a 7a 1f 
24 6e 3e ec dc c7 5f 08 53 29 f8 f1 7b 81 d1 30 
30 0f c3 b9 f0 93 a2 

# Seed:
81 cd 64 c8 4d 77 65 fc 60 e4 de 3b a9 b4 dd 21
dd fb 74 fe 2d fb 7c f6 19 db a4 cb cc 17 6e d9
4e 6f 37 ed 1a 97 e3 bf d3 63 65 d2 64 4d 3b 6e
e6 c7 71 09 fa 18 41 2e e7 cd dd 3b e8 d4 bd ee
94 c0 96 f0 72 ca b6 f1 88 6e 3a 84 a7 fd b5 af
f3 dd 83 f7 e5 c5 b4 9b b1 b3 8f 8f aa 75 25 31
d8 9c 88 39 3e 9e b8 f5 7e dc 5b 9f e6 ed 2b c9
5d 27 2c a9 95 f7 e2 59 b0 08 32 d9 8b 87 23 12
cb ef 8a 04 8f 6e b7 91 97 84 ae d3 d3 1e b4 b1
2f d8 07 60 a1 34 c9 d6 c3 34 c2 dd 3d fd f4 97
5c f1 b5 1e 87 12 2b 97 33 33 44 96 08 ff bb 2c
f3 0a 02 bc 46 ea 24 7b 45 39 b1 86 07 bd 47 d3
cd f0 87 72 14 ba

# Encryption:
78 6b 3f 59 9d 1b 74 3e 23 58 26 24 fa 2c 94 a3
6f b6 bd 33 fd dd 57 64 08 cc 85 4a d7 cf 66 7f
17 38 0a f2 0b 0b 73 0c 6b e9 8c 01 80 76 b9 b5
04 1d af 2e eb 02 54 69 30 0a a4 36 43 35 be 26
7d 33 b0 6b 4a 7a 79 7a 3c 0a a5 fd 3f 91 6a 55
dc 27 4c 0a 24 87 f1 25 f9 da 82 59 6f 43 4c 7f
ba c7 ec e2 ef 6c 83 e0 34 8b f4 f2 c0 83 05 07
55 b5 6a 9c 63 47 f3 9c 76 b0 e0 ee dc 61 54 10
25 c2 3a a1 85 5c 0b 22 b4 46 fe 1e c5 f1 11 2c
5a 7f c2 85 ef dc 84 20 ec 01 a3 a7 c3 3f 73 5b
45 55 09 2a 9e 8d e1 6f 3f 7d 46 9f 88 cd 75 c0
1c 7f 2e 7d 54 6a 1b 9e 7f 49 84 fa 29 a2 cc 80
d3 10 f9 d7 81 8d f6 d9 ad 6c c2 05 37 4d 52 e8
e1 73 90 bd e7 2f 25 b7 12 a4 26 9f 23 ae b2 41
a6 66 e9 64 96 cd 84 b8 33 fb 53 d0 57 1f 7a c2
d1 96 4b 8f 2a 7e 13 33 6d 9a 7e 03 04 1d cf cb

# PKCS#1 v1.5 Encryption Example 15.7
# ----------------------------------

# Message:
5f 2a 5c 7f 93 e7 14 ee ca b3 a5 5a 69 c7 9a 3c 
bc 15 bd 19 df 27 98 9a 9b ab 59 fb be a9 ff cc 
66 3b f8 e1 e7 40 7d c2 73 

# Seed:
e0 19 60 82 6d 1a 69 f6 84 c9 c0 b8 5d 84 dc a5
81 1c 89 a2 c0 74 2f 33 a2 ad 19 9f b6 57 a1 aa
98 13 60 1d 29 93 6a 43 d9 b9 eb 4d 32 89 cd 7d
37 06 ab 86 b4 d6 6a da a7 e0 a1 34 51 b2 ed b2
bc 77 10 91 11 0c da dd 7e e2 e6 aa ea 2b 35 cf
ae 4c e3 b1 da 18 16 68 4c 89 c0 b3 fb 2f 87 79
b2 5c e0 c1 2d 42 b1 d3 d3 0b 8f 20 be b8 99 91
6e 4f d0 a1 58 86 37 19 2e 05 28 ce 6e a5 4c 8e
b7 54 fd 7f f0 03 25 81 a9 50 59 98 e6 9e 14 f0
72 bf 95 df ef f0 14 df 99 ed 78 53 b9 82 e8 89
41 29 a1 d2 7c 53 ea aa 23 4c 8d 14 a7 ff c5 f5
e2 18 7c ef 79 eb f5 2b 3d 6c 06 65 89 5b fb 87
e4 bd 61 0e 35 8f 35 26 da 05 92 c9 e5 02 b7 2b
76 e4 65 66

# Encryption:
77 d9 07 18 1c c3 b1 bb 19 81 e8 cb 22 f7 ad 75
f8 82 e2 6a fd 28 1b 64 db 70 c8 4c 6a 50 fe 74
24 9e 22 fb ee 90 e3 0d 0b 70 ae 2f 7e 12 ac dd
f6 78 f0 0d 22 7e 53 61 54 26 62 43 02 69 fe ea
34 12 47 92 af b3 f8 7b 30 f9 50 f4 ed f2 2c 44
04 c9 68 8d ec 38 ea 0b 99 cb 3d c3 84 bd 88 fa
31 83 d7 e0 7a 20 54 d7 3e a5 1d 42 86 bb 39 da
e3 ae 6d 0b 96 51 f1 ea 48 8f 80 5f 2a 21 6e a2
1a 56 76 b9 7d 1b 11 d3 b4 03 6c e1 67 fe f6 4e
0e ba 41 9a f6 73 98 3f c6 ee 01 c6 37 b1 64 e5
aa ac c9 9a de e9 f4 7d 21 92 54 69 6c 8f ce ec
6c 74 ac 4e 39 05 1e 15 26 94 01 73 82 64 f0 ca
5b f1 22 c5 5c 9e 5d d8 47 b1 d5 77 4e 74 08 c3
68 4a a9 74 b0 ba af 40 ed c2 2a 03 57 af 72 c8
16 cf 73 1f cb 63 96 53 60 69 9f 26 99 97 b8 48
0f 30 a6 b5 d5 7e 12 a5 cc 54 ec 0c 80 5f dc f6

# PKCS#1 v1.5 Encryption Example 15.8
# ----------------------------------

# Message:
55 61 39 59 3e ee 8b 6e 87 

# Seed:
2d e2 a5 72 24 f5 f5 b1 2e 22 3e b5 f8 2b 9f 47
24 9d 25 55 93 79 13 6e af 18 e2 f6 c8 33 e3 f0
1b de ea 9c 30 3b d9 67 7c 2a 85 71 7d 59 3a 28
02 ae cb c6 b3 b7 1f 2c 79 03 ff 69 0e 3f 3c 49
57 dd 74 cc 9c 2a 68 dc 1d 31 9c 1e 17 87 bb b7
f0 e6 e5 1e 39 a5 ba db ba 9f d4 67 66 19 74 31
2b 55 7a f1 89 52 54 9f 6e ba 9d f4 9f 70 ea b3
68 9f 9f a8 fb ea 1c 97 e1 bb 2f 09 3e 6a ca 9c
38 0e dc 54 6a 19 c4 4f 91 f6 dc aa 28 9b d1 14
fe a1 b0 36 f9 9b 1a 57 f8 61 43 d8 67 5b d0 7d
4d ea bc 9d 51 0c 61 70 99 44 9c cc ed 5c 45 07
b7 9e 85 1e fe b1 8d 06 b1 99 81 0b b6 b3 cb e4
12 73 ba a7 35 16 02 e5 f9 52 13 f9 69 55 ad 5f
dd 3a 20 52 db c7 5f cf 60 aa 22 47 f2 d4 e6 03
da 45 37 0d e1 c1 da 68 7e 26 8e e4 46 67 f9 4d
ad 13 bc 9b

# Encryption:
4a ce 54 a7 52 f5 56 e3 6e ab b1 19 48 95 84 12
14 0c 80 c3 1b 61 dc 40 f8 1a 6b 12 17 a0 1c e0
67 ab 37 f5 3d f4 c7 7d 9e a9 c2 d7 95 0c 8c d4
97 00 b8 cd 24 d4 e7 8f 7f a3 46 29 62 cb fd e6
d0 2f b0 e5 03 65 64 93 25 05 ae 1c 85 1a a6 d1
d8 4e fd 04 d5 78 ad 68 27 3a 36 a8 ae 23 d1 45
2f 94 a9 37 88 17 71 3e 76 4a 09 17 45 26 29 b5
dc 75 b5 7b 0d 5e 6a 72 8c 83 69 11 72 d2 cd 95
f8 ba d0 7d b4 68 eb f5 45 b7 f3 f2 c8 63 b6 e2
0c 67 c4 76 9d ed 03 91 a3 36 f3 a5 d8 7e 24 fc
f9 1a af 77 4b ee 77 a7 89 a5 90 80 09 c7 a5 5e
ac 92 af 4c 3d 46 1e 7b 40 61 6c e8 06 19 4b fc
20 74 c3 f4 f1 35 59 70 0b 27 08 a0 b7 55 78 96
70 a3 62 6a 14 63 88 11 bb 18 e1 5b 10 25 c3 b9
be f1 11 17 6b c1 f2 46 9e a9 9a ad 20 86 05 73
d6 c6 a1 fe 40 db 51 e3 6f e3 38 00 10 1b da 20

# PKCS#1 v1.5 Encryption Example 15.9
# ----------------------------------

# Message:
9a 13 96 62 2d 06 6c 10 56 08 58 c2 c4 cd 5c 04 
44 9e 2b 95 50 c5 bc 92 93 76 1a 91 04 41 1d a1 
8a 57 d9 b6 a9 97 33 3c db ce 77 e9 fd be 6b b8 
31 

# Seed:
f1 78 61 ac ff b2 4c ac ed 90 ba 38 aa 7e a0 f2
e5 4e ea a6 2a e6 64 98 f3 c2 8f 99 6b ce e2 53
be e8 19 9e 3e b8 0d 62 7f ee b6 e0 b3 94 90 ed
f7 6d 16 a2 a0 bc 20 09 32 52 a9 d7 f1 f9 38 8b
06 19 44 85 2a fb e7 3c e4 13 a3 fc a5 21 b9 47
4e 67 81 29 46 4d 91 b8 2b ca a5 9f 56 ec fb 12
4f 61 f5 04 67 13 01 05 b2 cb c6 94 3b 95 36 95
cf cd 20 c9 b6 ef 53 f3 f2 10 33 1d 39 32 dc 01
0c 73 59 29 09 6b 2e 68 ff 16 66 4b 0b 90 a0 fa
1b e4 60 57 85 92 b0 cb 4d 6c a4 5e a0 6b de 3e
8a 1e bf ef 70 d8 3e f7 9b 3a 74 db 06 0f c0 20
3b 74 80 7f 40 70 01 f4 b4 d9 9e c5 15 8e 8e 7e
4b 10 2a 51 5d e9 5d 2b 70 fe 1f b4

# Encryption:
10 0e ce 63 45 25 d4 67 f6 d4 a6 b6 6e de 1c c2
37 f6 1f b2 b6 70 23 a8 3d c4 56 b9 2c da 18 3e
d6 62 0f e5 7d 5a 67 33 2c 77 23 3a c1 e8 72 5b
36 f8 e1 b1 08 41 2c a6 fb 35 dc d4 d8 16 77 a2
b3 0d 5e af 25 e0 b9 19 1b 38 f7 ee f8 3f 91 21
a8 08 43 8c 92 ab 03 f5 20 80 7b c9 a8 94 70 5e
af 4e ed 06 68 23 a6 7a a2 a5 59 9c d9 5e 58 da
7c 09 48 36 d2 af eb a3 9d d0 09 a6 4a de 03 05
33 76 f0 29 36 cf 3f 56 bf 64 c1 f3 bd c0 7c 45
a9 5b 9f cd 93 96 cd 9a 8d 41 bc c5 64 24 93 7a
13 71 b3 84 7c 90 5b 9a b5 84 02 39 3d 40 46 e4
a0 15 c1 47 08 f7 4c e7 79 0e ba 8a f7 92 07 24
40 bc af b1 4c 0f 81 08 97 11 87 c8 0f 46 3a 1f
ff 25 86 46 ea 16 e5 1c 6e e3 61 b6 61 a1 4f 07
cd 4f 5a 82 c7 09 f4 94 f1 df 0f 80 3b 6f 64 a7
2f b9 c4 50 ff e2 68 fc ab 48 7d 4d 63 01 3e 41

# PKCS#1 v1.5 Encryption Example 15.10
# ----------------------------------

# Message:
b3 82 4f b5 45 a8 3f 82 ef 82 23 11 82 84 c5 45 
6b ab 60 0a df 79 f5 07 33 b6 66 8f bc 51 5d a5 
96 31 62 a6 d7 d7 e9 6f f9 1a ff 12 eb 3e 93 11 
e2 21 e7 0b c0 

# Seed:
b8 26 8e 4b ce 7e 53 f2 e8 be 98 b1 92 d6 3a d0
65 44 a8 0d 6e 62 d6 32 48 6e 15 e5 75 ba 70 6e
3e 76 89 30 dc 8e 41 1f 8e eb 0b 6e 8f 06 06 29
da 8a 24 23 68 e4 79 cc b3 31 69 70 70 b4 b3 52
4e 69 16 92 76 ba b0 a9 45 14 cc d6 60 70 25 28
ed 20 b5 d1 df 07 77 9a 62 c6 56 86 e7 d6 68 46
6f fc 74 8e b3 43 44 ca 6f 30 5c da 3d c3 e8 f0
1c 43 ea 91 79 da 46 21 47 f4 d3 ec 92 f8 88 b7
ee aa 41 0e 12 c8 6d 89 42 c7 d0 12 f4 5c 61 ff
a6 e2 b7 8f 84 3e 9a 75 d9 67 32 14 d5 58 ca f0
1b 45 f9 36 86 ee da 54 79 db 80 52 79 25 59 cc
23 6a 4a 1e e6 5d 3c a6 0e 09 a3 c1 84 d4 b3 95
d7 0b 8e f8 8d 78 09 1a

# Encryption:
c0 e9 8d 50 89 4a da 84 9f ce 89 83 f6 f8 95 74
03 4d 6c f3 b8 35 2b fc 50 72 4a 70 3d d4 f4 2f
40 06 ae 00 8a d9 72 33 ce f6 f1 6c e1 b4 23 f5
2c 6b 67 7e f0 05 13 1b a9 87 f9 8c 72 2f aa 49
42 ec ce 2c 99 66 37 40 a1 a1 e9 81 20 fa ed 97
fd 03 ff 36 fe 73 75 8e 70 df 17 f3 1f 1f 39 41
81 2d 34 ca e6 c3 9d e7 87 ef 57 04 bc 39 c9 20
ea 5b 0e b1 83 3e 83 b4 57 94 fd e0 ff 00 05 c6
27 33 c7 0a 29 6c a0 bd 47 f0 65 50 3d dc e2 d6
49 de 1c 32 8d df 60 32 a3 3f ad 46 ba 04 1d c0
a9 94 bf 0f 56 a4 65 f1 62 5f cb 81 ce 01 fa 29
9f c2 b3 c8 09 39 eb e6 a6 73 82 6e 2b 2f 12 ec
dd a5 03 5c 95 09 31 2d d1 9f 10 c3 5c 8a 8b 0d
a6 3c 08 51 97 00 6a 9b e2 36 10 8e b9 87 91 b2
6e 28 08 b5 cc d5 ac ec 73 8b ca 02 5b 24 18 2e
f4 ab 9c cc b1 71 a6 9f b4 23 a4 6e 03 7a 4d 0a

# PKCS#1 v1.5 Encryption Example 15.11
# ----------------------------------

# Message:
a3 a7 da 1b ed b2 ca 99 fc de b7 a4 6d 63 3e ca 
35 06 2d f2 89 6b 69 59 07 a7 f9 71 d2 cc 50 b6 
e3 d2 a3 67 d1 6e 72 7f 56 97 c0 

# Seed:
f0 18 a9 b1 3f be 56 0b fe 95 52 ed 8a 86 06 be
ea 90 05 5e d3 f6 2b b2 af 07 f6 92 cb 60 ac fb
6d 59 07 d6 0e 0a 59 7a 54 ca ea f8 44 91 1c dc
87 4f af 95 69 53 a2 7d 30 0e 9b 71 5b 10 4d f3
c2 32 c3 c9 63 82 cf 5b 5f 3d 07 b2 30 b5 25 bb
33 0e 31 9d 1a 7c 82 d1 53 af 81 7e f1 1c f7 2e
76 dd 50 b0 d7 e5 56 22 65 c8 34 5d a8 29 f5 60
d6 a5 4e 6f 1e 28 8a 3d c2 17 6d 19 a6 8a 0b 1c
5c 92 b1 6b 8b b2 9e 4d 01 df bd 0b 18 07 9b d4
0c fc d5 23 35 b3 a1 8b c4 ee 92 44 76 0e b4 94
f5 be 5b 19 71 88 6c a2 be ec e0 a3 94 4f ff 8b
e6 b4 2d 96 e1 c2 c7 2e 4e 90 f8 7e d1 36 15 46
7c fc 91 c2 6e b3 8a 7a f9 f5 66 86 93 1a 47 26
da 04

# Encryption:
25 17 7b fe 12 61 9b 44 f4 a4 fe 7c b7 6d e9 3d
4e d4 a0 5a 31 e5 be 8c c4 e5 60 66 1d e9 a3 4a
e3 17 cc 02 ff 63 10 67 08 32 8b d3 f7 87 63 ab
3e 57 65 2c 63 f1 05 f7 97 1d 2d 8d 70 1e 62 97
a7 9c 78 7b 7c cd 62 a5 3b 39 d9 c0 39 46 e6 6f
48 8a 92 e8 e1 7d c6 ec b0 f6 5b f0 1e 3a ff ee
99 76 87 31 1b e0 e9 45 ad d6 3f a3 f4 00 38 2c
b8 ff d8 91 57 54 01 8c c7 5e 82 82 26 b5 03 9c
d9 c5 7f c6 d9 9c be 8e a4 a3 d2 9c bd 09 d5 4d
95 cc 07 34 c2 35 44 f8 e1 fb c7 49 3e 06 d1 6c
0a 0a c1 53 0d 21 f0 33 7e 26 2f d9 d2 7f cc 4a
fe b5 74 d6 68 66 d4 ca 84 cf d6 e0 af 2b b9 77
a5 d9 9a 5b 0b 37 44 04 2d 33 2b 93 6b bd d8 69
e5 f2 c8 83 b4 00 ac 8b c0 68 3e 67 90 63 42 9d
98 d4 94 f3 18 04 d6 5b b3 c9 74 aa 72 e6 65 7d
4c 16 38 c6 79 c8 1a 16 45 3f 6b 0b aa 3f 05 17

# PKCS#1 v1.5 Encryption Example 15.12
# ----------------------------------

# Message:
49 f6 f8 a4 a8 6a 8a a0 97 63 aa c8 55 72 b0 e7 
ee 77 6a ef f8 a8 29 00 07 76 ff a6 

# Seed:
6d b6 a8 27 ac 2a 5e 06 16 c0 f4 43 b2 34 58 e1
75 ac f9 a3 b2 55 f5 c8 52 5e 72 53 42 4f aa 91
38 05 4c 3d ba a4 71 f2 7f e8 55 c1 c0 ce c3 62
59 7a 1a 1e 6e b4 f2 98 ac 3e b7 34 d3 1f f0 ef
10 08 c0 e0 2b 9b 06 e2 93 15 fc 09 4a 7e d2 6b
11 ea 55 27 0a 3d cb 67 06 f4 6a 94 50 bf 83 12
88 10 65 13 02 48 d2 64 47 66 a7 99 66 ef da db
aa f5 75 ef 4d d3 5a 93 7f f0 bf bf 3d 95 61 c7
54 40 9b e7 b8 84 7a 60 8d 79 1f b9 87 ee d4 6a
fe b0 db 1c a9 75 c0 5f 61 57 0d d0 70 98 5f 13
e4 e0 ed 7a 8c b3 91 ce 4d 42 08 32 b4 5a 8b 7e
9f 90 88 4e 61 18 98 f4 72 a0 ac 46 c5 7a a7 f8
46 8a a1 9d 9c 7b 31 2f 13 43 22 99 03 93 88 1d
32 aa 14 68 f6 e5 f8 eb 85 a2 c3 c2 da ed 92 b9
3c

# Encryption:
0f f9 5f 4a 2a 07 18 d6 73 f9 20 2c 80 9f 10 45
10 1f 52 b9 db a7 72 88 fe 28 8c b2 77 c4 db aa
35 db 93 27 ec ee c3 76 5a e0 33 e0 b6 b7 77 b2
2c a6 be 66 20 03 ea fa 2b fb da 60 6f d8 ce e7
ee e0 6c 6a 00 c9 45 a6 55 6c 60 b0 08 69 f9 99
97 1a 8c 57 af e1 dd ee 7a 75 77 04 7a 0d b9 0f
62 cd 24 7a 88 7f 32 27 ef 6d a9 07 a7 5b cf f1
9a a3 0c 90 8f 58 35 ef 10 10 0a dc 7f 6a d6 fb
31 99 79 0b 3f 4d 6a d1 9a 0d f5 02 7f db 8f 84
66 04 e0 2d af 33 55 d9 56 40 77 79 af 15 5a cf
a5 a3 2e 6d 61 74 dc 90 13 1d bd 7a be 58 5d d7
59 fa 3c b7 e9 f7 21 45 3f 3e 35 4f e7 be 0b 11
8e f5 b8 79 42 b5 fe d4 c3 0d 7a 08 fe 24 64 eb
79 a3 de ba b3 7b 6d 3a 0a b3 15 77 de 7e 93 22
9b 49 d1 e8 fd 56 32 d0 26 d8 3e e0 6a a7 85 34
ce e3 08 1b 22 2e c4 cd 94 88 cc de 4e 15 29 05

# PKCS#1 v1.5 Encryption Example 15.13
# ----------------------------------

# Message:
12 97 5d b7 3d 

# Seed:
6a 4b 4f b8 05 80 72 72 96 5b ff 2f 4c 80 0f 96
4f a3 ae b9 fb 43 3b 40 c8 ac c5 98 b4 84 02 98
93 a2 19 c9 53 21 34 c0 6a c8 42 5c 28 a5 f0 63
c2 84 20 0a 04 56 43 48 97 16 51 6a 98 7b f8 1b
a1 86 f4 ce b8 d8 d9 dc 1d 73 f2 26 7f d1 98 8d
6a 2f fd 68 cd 36 69 de 2b 04 70 09 43 d0 d4 44
ae 3d a8 f0 59 4e 62 61 e5 ff f6 07 f0 4d f3 1e
3d 9c 91 22 d7 6f b9 0f 3f 82 c3 93 25 3b 75 20
16 5b d1 f3 19 ab 3b 87 5e cc 6a b3 ed 02 47 d0
37 70 f8 d6 47 1d 69 cd 13 ee 25 7c 1f c8 b3 0f
19 b9 3f e4 f2 fc 9c 21 49 28 13 d1 fc 85 2a f7
0c dc 63 84 d2 ae 55 b9 1e 39 ae 6c 3b 19 fd 1a
7d ea f7 18 c0 5d 57 80 95 96 a2 88 fc 9a ef b8
15 ae a2 9f af 67 83 c0 05 35 fd 71 d6 25 49 40
b7 62 f5 07 26 1f 20 9b c8 ba 94 79 bb 3f 5d 64
64 23 19 cc 31 86 85 9c

# Encryption:
27 15 d6 eb 53 ae e6 d4 bd dd 3b e9 b6 63 14 4a
41 0d 03 81 77 9f 79 9d ca 88 07 92 ab 43 1a f9
98 9d eb 17 36 9d c6 74 38 ad d8 2b df 0a 59 dd
3b c2 78 01 02 58 c7 df 87 69 84 ff 52 76 78 bf
9b 34 c0 77 43 b5 d2 2f 4f 30 cb 5d 7d 8a 6c fd
50 58 24 f2 e0 94 02 4f b0 43 30 f6 6d a7 cb c0
1d 5e cc 8d fc a1 ca 9e 50 91 b9 d9 c8 e3 8d 0a
c3 93 1d e5 d1 fd c8 33 73 84 82 a7 fd 15 2c 1d
24 e6 9e a0 1d d3 e1 fa 77 23 54 a6 07 d6 2c 60
b5 61 dc 5c cb 01 2b 71 2f 5c 2d a1 89 2c 31 20
ef 99 0d 74 61 1d dd 35 66 af 1a cd 8b 48 50 06
1c 91 28 f8 2a d1 12 da 9f 68 ac 88 39 3f 9a 5c
2c 10 20 aa 77 e9 b6 2e 2d 1e 98 5f ef 86 4c c1
ae b4 51 ac 83 9c 72 0c b9 73 e7 b5 df 24 a9 8c
cb 6e 67 72 66 29 a3 66 fe a3 f9 a9 52 1d aa 19
b0 44 30 e8 10 f2 c4 5a 57 d4 25 3b ed b9 1d a0

# PKCS#1 v1.5 Encryption Example 15.14
# ----------------------------------

# Message:
8d e0 f5 a4 13 a7 f7 86 39 6f 09 a4 5e 5e 77 4f 
3c 60 9c e6 f1 b4 90 dd e2 22 b3 22 d5 34 0e 9b 
10 55 81 f4 c5 be 44 ea bb 3d 1b 23 f8 45 

# Seed:
f8 71 a8 97 ae e8 45 c3 bb 82 69 90 b7 31 d2 77
7c f4 76 b5 cf c5 59 6d f3 a5 23 ba 69 79 c7 a4
51 79 da 5f d6 0f 81 0b f4 14 c5 4e 18 2f 26 01
72 0f fe 8a 50 d7 d9 d1 1e 56 43 18 e9 02 6f 07
c5 aa a1 3e f8 91 28 3a 96 63 c1 e3 d2 7b 93 81
7d 01 a5 6f ce 33 d5 16 9b fe a6 2a 8e e6 c3 70
a3 aa 8c 71 94 d9 d3 53 d0 98 16 61 fe 85 81 62
50 e0 32 4e ae 1b 84 7f c7 32 91 91 60 d0 1e 11
92 ac 16 be 0e b8 e9 95 e4 86 40 27 6a 2d b7 b8
7f 84 b3 fa c0 4c e8 62 f0 22 38 62 3f 62 b9 2a
2e 32 7b 01 b8 c7 b9 e5 ec 87 05 5c 6c b7 b5 56
51 ab 5a c9 89 83 3b 03 4f 8b e0 11 6f 28 b1 e8
86 a2 ce d1 23 29 8e b0 04 07 5a 6b 5b 71 b1

# Encryption:
d3 67 ac a8 d4 f1 74 14 e9 bf 09 ad 81 1d 78 db
0e 85 0c 45 c8 f9 d7 02 44 75 d3 e5 6e 3c bf be
6e e8 ab bf d7 74 58 26 41 7c c8 ed 52 f5 4e 00
a9 2f 81 7e f9 83 e9 8d 10 0a 1f 99 0d b1 e2 90
a8 51 6d 60 9b b3 2e 50 2a 77 e1 1f 76 20 0b 00
25 76 5e 9b d2 85 9b a9 4a 69 63 33 a5 eb e2 eb
25 ba 9d 19 00 7f 64 36 0c c0 75 ca d7 f0 99 50
e4 b7 af cb ac 36 e6 ec b0 17 cf 4a 1f 25 a4 d2
b9 51 bb 85 e8 1c b2 b4 eb 6f 45 cd d4 00 d2 ac
4e 21 69 89 6d 94 15 82 44 9c e3 0f 69 c1 7c b4
49 32 1f 65 e4 4d f3 03 87 86 27 62 13 51 f5 2e
5a 07 52 e3 b5 eb 12 63 61 69 7f 53 cf 24 66 16
5c 3f bd 66 2b 83 75 80 b7 6d 45 9f f0 44 97 e5
fe 1b 3c d1 8c 4d 58 ff ed ab dd 04 a8 c1 08 d8
59 b6 52 98 63 9c 3a f8 0c d9 4e 23 87 a8 69 44
09 ef 9e 0b 78 b6 f4 67 39 0b e1 08 57 9c 9b 2c

# PKCS#1 v1.5 Encryption Example 15.15
# ----------------------------------

# Message:
9e 78 c8 2b fd 0f 23 d3 91 e7 60 01 64 01 9a ad 
28 f5 9b 14 15 44 04 d9 e9 66 13 3c 10 3f c3 7c 

# Seed:
bb 42 24 7d b2 40 ba ca cb cb e8 c6 8c b0 f7 0e
46 0a 49 73 da e6 56 99 35 8f ef 82 70 d6 5c 3d
0c 45 5a 37 9c 56 3c 59 7b 28 f4 ff a0 70 e0 ec
1e bb 9e d4 27 fe 89 ab cd 47 93 c4 22 c3 38 87
8c 8b 14 5b 46 c4 f7 13 78 a0 fd 7d 50 53 b8 67
98 bc 02 d9 32 93 fe d8 47 f1 8f e0 61 59 07 4a
c1 89 d9 56 23 20 74 db 6d dc f9 94 1a 70 f2 8b
60 e4 83 39 5a 2d 21 01 54 b6 2a ba b8 75 0f 5a
eb f1 ac ef e2 05 6f 5a bd 2f 0e 0e c4 94 af a8
2f c5 9b b3 57 f1 16 a9 4e c1 cd 06 03 b5 2f e5
6d 31 a4 3b 87 aa 63 77 88 c7 24 cb 6f 88 37 3c
92 f6 07 11 bf 3f 35 94 e2 3d ee 2f ec ed fe 6f
5f c8 86 e9 6a ee 7c 74 68 22 e5 6c ba 7e bd aa
2a 92 10 ea 81 98 e4 c2 2d e9 29 89 45

# Encryption:
81 8d cb ba 98 c3 46 79 3c 79 d4 bb 78 5a a6 40
19 1b 05 f8 83 5f f7 39 74 44 3b ce 35 7a 26 9d
44 64 6e 8c 79 10 2d ce 22 92 39 78 a3 94 1e bc
b9 90 4f c1 bb 1f b4 3f 11 27 5e 71 eb 7a 84 d2
74 be 10 4b 00 af 92 25 e2 a4 f7 f5 a0 48 ab b6
6c ba b6 52 5d 1b 11 5d a8 c0 bb 08 de ea ec 7f
80 eb 6c 39 50 4d c4 eb 38 15 4c e4 b6 91 b4 06
93 19 b2 93 c2 5d c3 0b 8f b3 8b ca 15 3e 2f a6
1b 33 76 dd c3 c5 3a 57 93 21 d9 10 a1 71 fc 42
aa f1 70 50 ed 6d 31 1a 7d f5 b9 a5 cf 3a 98 fd
69 aa 85 ac 23 46 c1 6f a0 3b 1e 53 d1 03 d6 f5
a0 4b 0d 9d 3f 18 83 53 1e 2f 63 41 fd 91 bd 63
a5 aa 99 3b 6e db 99 92 e5 db 17 a7 be 55 5e af
a3 bb ab 32 08 6e 92 b8 b7 91 06 69 68 e0 0f 8a
17 71 61 44 0c e5 38 57 97 89 c2 91 2e bd 7a d0
19 be 29 37 6c ad ee c2 99 21 07 c9 db 07 60 49

# PKCS#1 v1.5 Encryption Example 15.16
# ----------------------------------

# Message:
6d 72 08 b2 ff b0 1a d2 36 0c 46 09 f3 ba d3 15 
79 c8 d4 00 5c c9 60 14 2e 2b 69 6b 26 e9 4f 2d 
99 98 0b ad 38 41 05 b1 89 95 57 af 89 05 25 

# Seed:
72 ae 49 97 18 28 ba 54 23 ab 96 3c db 18 f4 bd
fd bc 74 73 cf 70 fb 77 21 34 41 f1 d4 46 ae 48
10 93 03 a2 60 c0 91 ab 51 99 d9 64 e6 21 63 c2
46 57 24 71 73 cb 05 11 b9 cb b1 63 45 9c 95 6f
9c 0b 18 83 b6 91 b5 e4 ea e0 4f 0a f2 4e a3 28
c6 de 88 2c eb c4 f8 9a 56 37 32 82 d6 0a f2 ba
96 7e 25 7a c4 01 a3 7f 94 41 c1 1f 2e 0e 42 1b
2d 15 1e b2 43 a9 7e ae 5a aa 86 cd 38 df 43 c2
6a 1d 6e 3b 12 c1 3f a3 59 7b 85 bb aa 13 09 45
2c 7e 9b 32 5d 8c 73 fa 79 9c 57 56 52 73 7b 92
a2 47 d2 3c 4c 70 12 40 c5 3d cf e2 ea 69 7a f6
1c 07 2f a7 6b db 05 2a 58 c9 19 e6 9e c5 7b 39
a2 a6 c4 7d 77 0e de 67 10 fc 7b dd e6 01

# Encryption:
4b 56 dc d3 04 bd c7 f0 cc b7 0d 2c 58 6f 52 74
60 1e e6 05 36 bb 21 80 61 67 13 50 d6 d3 ae 2e
28 4f 07 b5 ed 63 01 79 26 94 13 12 2b 98 37 60
90 f7 a4 a4 f6 43 4a f7 3a c4 0c 7a f4 b6 d7 a3
e3 14 70 2a b9 b8 5e 08 73 1d a0 d1 2e d3 f0 07
0b 0d 20 95 05 3b 3f 0d 09 d6 c2 cd 8f 98 ba de
d9 11 48 c3 5b 7b 33 c5 43 65 3c cc 32 d8 36 f5
f7 f2 ee 39 ca bd 0b d8 9d d9 a4 a9 4e 91 2b 4c
a9 7f 18 51 d0 17 45 1f 60 96 ac bf 20 a6 5e c5
a2 9f 08 f8 13 5c 73 18 a2 21 6d 1b 7d 10 37 95
c7 ec 8e e5 7a 79 28 0f 9a 84 4b d6 ab 71 28 82
0e 1f b5 e8 25 54 fe c0 2a 78 aa 8d 3f d6 a1 3e
0f ad 0e ce e7 ab 61 1d e4 b0 a0 48 1f 42 b8 d0
b5 5c b8 81 3d 1c a6 e2 61 5c f5 ae 8a e8 6d 0b
5d 46 95 50 75 49 f7 a3 73 66 a4 45 fb 55 b7 c4
b6 b5 8e a6 99 db e5 dd c8 19 3e 2b f3 d5 b8 40

# PKCS#1 v1.5 Encryption Example 15.17
# ----------------------------------

# Message:
7d eb 6d 40 41 48 23 2c 48 21 63 4d 3d f9 bc 

# Seed:
e6 f8 3c 8c 99 3e 60 15 af 43 04 09 68 4e 62 7f
3d 9b 84 ad 05 55 c6 a6 c0 91 13 a7 12 47 2a bb
36 f6 11 92 32 6c f8 40 82 aa bd 1e c9 5f 4d 1a
92 d9 10 7e 30 61 0c 8d 27 59 55 6d 5d 61 47 5c
a3 f3 cb d9 49 fa c2 20 3c 42 3d 56 c2 75 58 e6
11 8f aa 0f 6f 68 4a da 13 c3 15 3f 6d 25 53 38
bf f7 34 e9 5f 60 ad 29 19 ab f2 88 15 d3 cc 0b
1e fd 38 5d 0d 45 81 b0 ee 84 94 f2 bb e2 99 59
91 ac 1e a8 15 40 cb 7e 88 56 68 e5 a5 2e ca 90
57 ed 9a 1f b2 3f df 83 b5 75 51 35 8c 23 cd 43
ce 0e 7a 33 c7 25 66 b8 8f e5 93 43 f1 87 24 46
d3 2c 44 b3 99 0a a4 db 3e e5 93 42 4c 8e 09 46
d2 61 e3 27 0e f4 07 6b b3 5d f3 c3 c6 da 4b be
42 3f 59 1b 5c 93 ba 56 c5 cf 01 d4 f0 a9 40 96
71 09 d3 9b 93 9d f2 82 53 2e 54 83 10 8b

# Encryption:
aa 6e 6e 4a f6 89 26 4d 61 bf a8 f5 08 6d 82 79
dd c2 28 9c 55 41 af 45 35 19 c4 4b 95 ea e6 a1
5e 7e 7b d1 5f d3 1a 4f ad 5f 7c 85 90 5e fc a2
26 93 0d 67 da f5 58 b7 15 b2 1f 36 28 f6 1a 3b
04 2c 1a 38 f9 af 3a da 82 ec 44 88 c8 ad e5 f1
da 81 e1 a8 ab 90 e1 b3 12 dc da 83 5f 9e 92 5f
2e 72 46 3f a8 33 a0 8b 93 25 3f df e8 cf 4e 5f
3c fa 91 10 77 19 a6 a9 46 9a cd 71 25 ae 67 b2
bd 75 85 7c 59 be 0a be 98 40 74 e2 95 47 8a f2
74 0e 25 89 4e 56 a6 25 0e f7 36 21 94 13 81 03
74 3c a9 54 47 33 d2 50 55 71 be e0 0f 17 8a 2c
fa 38 e1 f8 f2 2f b2 39 30 d6 a7 5d d1 7c 68 9a
47 6d 87 15 31 85 95 10 8f cc e8 95 e3 44 9e ca
97 a7 dd ba e3 e6 d6 f0 e3 5e 66 6d 6f bd 78 78
48 f8 68 13 37 b1 d4 c5 23 8b 1c 24 de 77 a7 e6
75 e7 ae d8 dc 13 dd 9e b1 06 87 98 51 7c 0b 6e

# PKCS#1 v1.5 Encryption Example 15.18
# ----------------------------------

# Message:
03 1f c3 a3 ea a4 2b 0d 9f 6d 7d d5 99 3d 61 89 
cb b2 e0 e9 6f aa 33 d6 1f 31 7b 6b 3c 00 

# Seed:
16 ab 64 85 ca c0 39 71 14 88 0e 6f 72 bc 1f 1e
f3 4b a2 7d 1c 43 3d 77 f3 37 2b 1f d5 b2 1b a5
7a 50 5c d8 f3 5b 75 24 2c f1 b7 6d 38 1c 68 34
24 01 ee ef c8 42 53 a2 de 1a 19 13 e4 38 77 3b
7b cb 31 cb ab 25 8f 72 6a c9 34 a8 71 26 96 9a
db 7f 76 8c 1b ab 87 54 a1 3a 3c cb ba 6f 3d d8
d0 ec 6a 7c 6d 68 7d 04 95 b6 e2 2c 9a e7 67 af
c9 d9 4f 2e 45 a1 06 13 3c 42 c7 9b 52 c2 6a d4
b6 7b d8 ec e5 5e 84 32 5c a6 f4 9c 8e 95 31 f5
f6 b9 ca c3 5c 93 3e eb e3 b9 a1 1b 9b b1 c9 ea
8c e6 d2 28 5d a9 08 e5 91 97 82 b5 b3 08 fe 47
8d 98 e4 9a ea 54 3b f8 ca 1b 22 ed f4 3f db f6
af 31 cc 8c d4 fb 7d e2 d8 19 af a5 48 3e ac dd
56 d7 78 7b c8 bb 16 22 38 40 49 bc 30 9a 66

# Encryption:
61 c7 54 76 30 43 b5 84 e4 a8 54 6b 79 f0 a3 e2
e0 f3 b3 e2 db 6c 94 ad 81 7e 92 81 f4 85 c0 82
08 fe d4 f4 d7 d0 a7 8c 6c 31 1a 07 c7 5b 9b be
85 04 f4 b7 a6 92 99 02 16 de 12 58 5c 00 9a e5
8c 26 f0 85 07 1f 39 5e 5a f8 92 5f 39 33 f6 d9
f4 12 d4 25 54 64 54 e8 00 b7 e3 aa c7 8b 7a 08
b9 2e 79 8b b8 34 eb ea dc 4b 4a 63 56 af 1c a0
9b f5 86 74 5f 61 6d 51 74 8c c7 a3 7b 48 cb 10
97 7e 30 3b be 10 bf 27 c6 9b df f0 ba 5c ab 8f
62 d0 58 7a 09 d6 e0 22 82 32 33 c8 c7 aa 41 87
22 23 ed 15 a7 4a cc b0 f1 f8 22 f2 4d 75 94 ed
99 25 a1 c6 c0 c0 f9 ad 0e 07 1e b6 b5 eb e1 ba
b6 ba 3b 6d 99 a3 16 52 04 7f 46 92 60 be e8 71
0e 37 0f 04 ed 70 75 93 93 7a 08 dd 82 26 49 92
31 1f 4a f9 88 4c f8 ad 34 af b9 f6 75 99 3c 8a
bf 41 51 98 39 f7 6f a1 e9 31 bd ae 1d 08 a6 34

# PKCS#1 v1.5 Encryption Example 15.19
# ----------------------------------

# Message:
c9 c9 3a fe a9 97 b1 ee 36 fa 72 72 03 54 c7 04 
64 9b c6 ef 44 f5 18 7c 6c 28 54 2b 9a e9 55 a7 
19 3f ff ec 86 7b b6 2e 21 9e 68 

# Seed:
52 d4 c3 bf 0c 77 2c 02 d3 bb 71 1d 7f 14 6d 8c
49 76 f8 30 81 be 49 53 d4 af eb 07 8e 54 12 a3
2c 3d c7 37 b5 9b c4 54 c3 de 93 a1 ae d0 7c 1b
a6 4d 1d e4 36 80 14 d0 4f 64 40 55 6a 4a 16 97
9d 08 03 cc 29 22 da 97 ed 67 61 90 12 f8 f7 d3
b1 a4 55 84 b0 94 6a f6 db d4 a0 85 7d 4c 2d b9
9a 17 bb d2 7a cd 9a 62 e6 7f 88 79 db ef 27 f1
04 6a 86 7e 69 95 16 2b 2a 1a 1c cc dc ea f4 5b
e1 33 7e 5f a8 f8 5e da ad f7 50 8d 58 f3 26 aa
7c f3 01 f4 41 ba 55 8b 60 e0 fa c6 e0 d1 99 20
61 a0 a4 69 be 02 16 26 89 0e e8 7e 68 13 9b d5
19 ae 77 3e 3e c4 81 b7 f2 b3 77 33 1f 52 f2 e3
86 88 4f 76 57 23 e2 6c d4 a8 d9 68 61 4c 9c 33
dc db

# Encryption:
d0 68 67 02 2a 0f af 57 3d 62 cc 24 a6 e0 6a 44
cd 3d 83 ea c5 d0 80 9e bf 91 30 45 68 cb 1b 27
56 1b b2 92 e8 77 6e 21 6d 1d 02 3e 75 3c 12 4b
51 86 7b 94 dc 60 89 4d 62 c7 da e5 fa 1a 9f af
03 76 e1 75 8c b6 ad c7 14 17 e8 31 02 52 8f 23
f4 c5 e5 b5 03 62 a3 9e 2a a9 76 8b 10 62 08 6f
a8 c5 3f f1 c3 9a 0e e0 3d 38 3d 24 bf 49 07 22
b7 86 da ec 90 8c d7 15 1e 18 5d dd 17 88 26 78
02 7e 36 8b 05 12 cf 98 41 3f 3e 59 6f a3 db 4e
c1 96 ae 5f f5 24 a8 26 6d 76 0e 00 51 43 3d 18
98 ff c2 30 e9 6a 2f 0b fd f2 b2 44 29 ad fa 91
8a ba 1a 45 0f 76 78 34 72 30 12 93 84 8b d8 2d
5a 33 84 31 d6 cb 1c 10 6d c7 41 d2 34 bf 5a 80
db d3 25 da 64 a3 94 a0 06 5f 22 02 8d 0f dc 5a
df 0d 0d e2 9f 22 fb 8c 2a 41 33 1f e1 fc b6 15
89 e0 ec 75 00 ed 84 42 d6 18 46 df 6c ca 46 cc

# PKCS#1 v1.5 Encryption Example 15.20
# ----------------------------------

# Message:
69 b7 64 48 55 f9 1d 1c 61 c8 49 8e 4b a1 ba 4d 
84 5b a8 82 b1 73 

# Seed:
b2 2f c7 de 85 c5 f7 5a 2f 32 af 1b fb cd 57 89
71 56 87 de 06 e6 6d 06 4a e3 eb 8d fb 07 a2 57
5b e0 e9 e6 f2 9f 50 d7 39 6d 07 8b 36 ef 80 2f
75 1a 77 cc 92 d7 61 4c 91 dd 27 99 31 fc e0 07
eb f9 15 a0 f1 4e 31 2c e9 1f e5 aa 6f b3 74 51
61 4f e3 7c 73 fc 6f 6d 6f 8e 52 78 9b 5d 88 e8
6b eb 16 33 f5 dd d5 c0 70 f1 4f d3 cf ee 97 dd
4a 64 3d 35 d4 5d d9 bf 34 df 8c 31 0b 48 59 2e
94 68 31 b3 4e f3 c0 b9 16 f1 7c b0 ac b2 cf c1
c2 5d 03 09 ac c1 12 4f 26 5c 1a 83 ed 88 5c 87
fa 82 6f da 57 10 b5 4e 16 ec 0f 44 8c db 7e e0
58 0f f7 38 65 30 ea 46 1e 04 2a 0b 77 42 c4 61
97 6b b5 a3 80 ad bc b0 01 06 f2 67 1b 6c ce 4f
72 67 75 2f 80 66 80 42 78 35 0b 01 75 3e 31 b3
8e cc fb e9 05 69 f6

# Encryption:
ab 42 67 97 2c 77 96 83 93 88 d4 ad 87 de d7 4b
b6 53 e9 a7 05 0e 28 2e 82 19 28 75 68 9f 70 ee
1d a1 8a 1f 73 22 09 2c d2 9f d0 01 19 92 2a 6d
e1 26 01 98 0a a9 fa 6e 61 9e 27 75 e8 7a da e3
16 95 c1 30 4e 77 f5 2c ce 01 66 65 f2 26 7c 20
76 26 43 c6 00 3c 01 6d 84 80 44 3c 70 1d f6 c1
d8 d6 55 54 96 00 ee 45 5b 70 e4 73 31 9b 0d 44
45 e0 b7 55 2a 1f 80 8e 88 f3 26 48 42 73 5a e6
1d f0 32 5e d0 36 90 d6 d5 d6 93 ad 1f ed 22 66
84 50 37 9d b5 32 3d c0 1c 89 af fa e3 69 b9 c3
01 c3 19 c3 7d df 51 ed f4 6e 09 b2 1e 5d e9 14
83 e8 e3 cb 21 ee b7 05 7b c2 eb dc 3a aa 3d 65
00 c9 2f 99 b1 7b 31 80 bb a0 47 d7 60 73 77 63
36 b1 5d 05 4d 79 a4 40 cc 5e 98 5e a5 43 fc aa
25 db 1d d8 92 b7 1b b7 4a 5c f6 82 63 d8 fd 58
f1 a4 8e 6c 2f cb 8c 0b 71 a2 51 cf c1 a2 01 57

# =============================================
