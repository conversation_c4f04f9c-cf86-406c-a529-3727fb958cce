{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 395, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "wx": "2927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838", "wy": "00c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKSexBRK64+3c/kZ4KBKLrSkDJpkZ\n9whgacjE32xzKDjHeHlk6qwA5ZIfsUmKYPRgZ2az2WhQAVWNGpdOc0FRPg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221009364745a6a2d69f2283698fdfbee7b13de20bc93deb0230a9af3bd9fddf04401", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "304402208ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "3081450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "308200450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "30440221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "308501000000450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "30890100000000000000450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30450280008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02806c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "304700000221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "304a49817730450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3049250030450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "304730450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "304a22264981770221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "3049222525000221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304d22230221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0004deadbeef02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "304a0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e222549817702206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e2224250002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "304d0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e222202206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "304daa00bb00cd0030450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304baa02aabb30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d2229aa00bb00cd000221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b2227aa02aabb0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e2228aa00bb00cd0002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e2226aa02aabb02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "308030450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304922800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e000002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e228002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "308031450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304922800321008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e000002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e228003206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "31450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "32450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3049300102304421008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "30440221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "304421008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e15000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e15005000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "30800221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "304730000221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1503000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "30480221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150bf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "304730450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "30230221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "30670221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e15002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3046028121008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0281206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "304702820021008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e028200206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450222008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450220008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02216c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e021f6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a02850100000021008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e028501000000206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e0289010000000000000021008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02890100000000000000206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304902847fffffff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02847fffffff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490284ffffffff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0284ffffffff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0285ffffffffff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0285ffffffffff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0288ffffffffffffffff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0288ffffffffffffffff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304502ff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02ff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "302202206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "30230202206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "30240221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "30470223008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e000002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02226c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "304702230000008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e022200006c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e000002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "30470223008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e050002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "30470221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02226c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1500500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3024028102206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30250221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3024050002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30250221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450021008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450121008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450321008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450421008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045ff21008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e00206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e01206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e03206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e04206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36eff206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3024020002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30250221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3049222502010002208ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "30490221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e222402016c021f9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "30450221028ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206e9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c3ee02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1d0", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "30440220008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c302206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "30440221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e021f6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e1", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "30440221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e021f9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460222ff008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221ff6c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "302509018002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "30260221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "302502010002206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "30260221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221018ab9effc67388040d19b92d2e9fdea124fec6626e540f2b02edc15b83a73e8bf02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402208ab9effe6738803ed19b92d2e9fdea12d61e70cb9711b5a64768803241ad9e1d02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221ff7546100298c77fc02e646d2d160215ed6cfa9486c1d6abd4c4ddb50ac1ef3c9202206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402207546100198c77fc12e646d2d160215ed29e18f3468ee4a59b8977fcdbe5261e302206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221fe7546100398c77fbf2e646d2d160215edb01399d91abf0d4fd123ea47c58c174102206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221018ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402207546100298c77fc02e646d2d160215ed6cfa9486c1d6abd4c4ddb50ac1ef3c9202206c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221016c9b8ba395d2960fd7c96702041184eb9bad38c76f7f19ff4c7fd7e61ad606a1", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221ff6c9b8ba595d2960dd7c96702041184ec21df436c214fdcf5650c4260220fbbff", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e02209364745b6a2d69f1283698fdfbee7b142139c1e637988485a739f2dce18d1eb0", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221fe9364745c6a2d69f0283698fdfbee7b146452c7389080e600b3802819e529f95f", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221016c9b8ba495d2960ed7c96702041184ebdec63e19c8677b7a58c60d231e72e150", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221008ab9effd6738803fd19b92d2e9fdea1293056b793e29542b3b224af53e10c36e0221009364745b6a2d69f1283698fdfbee7b142139c1e637988485a739f2dce18d1eb0", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff000000010000000000000000000000010000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000001000000000000000000000001000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "313733323231", "sig": "3044022064a1aab5000d0e804f3e2fc02bdee9be8ff312334e2ba16d11547c97711c898e0220404a1a6da158ae1473fce08d338b8d6410a6a5903358b23fdd3a54fee2c2742b", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "33313930393433323838", "sig": "3045022100a9edb87925684bcc5b92d0f7455123656e3498a0d182be63e2e6077c2b43bc6e02202c729ea1b01d14ee8fe702096cddd9394e351d801411ec8eac6b758475ea0070", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "383433343734313535", "sig": "304402203fba20ca893dcaf04e89141337a96abc7e24e026a8ff4c86d950de1c31b6427202206be2eced4ce388ff8026dfd3b658144f30931b7083ee2af06e75158c15b12249", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "33353732363936383239", "sig": "3045022100c5aa31116f6006c479586ff7070014a35f22166701be8a5f1f1e9a43cb27dca0022068d1cee35ba3893b9cc3b5df5ac6afef55ebdb7ad9236b1fa8e438a538f8cb55", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "353931383333343239", "sig": "304502205b5b4d890504f56c16a4ac7947ac0057cdf640d2c39bac09fedc648bb0a16f1d022100f9c12e73a56d799e2827538187f0ed0ec331f6f0c089a4f6249d04c1b0c5cc8d", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3832353330383232353839", "sig": "304502204338e710478e8b922e50dc947f6fa0cd1903106cf02ee0742da69e8b624c5b6702210090c73bd0fcd07a4dd4a3664d559bd4795ac950d89463680852d33915de1a5745", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3133373334383238313432", "sig": "304502200fc97d7744e0b2762e6b48730d44c758ab238136a72693ff27339aaebefad581022100ab68ec80cf4afcca0f7d75f3c4b00e34ed4fe9101c98ed4d8c2f97eb865b1683", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "313733383135363430", "sig": "3045022100825f0b4230e30182b24b65151ec83d0aadc63ecfe0a91b5879ccf7fcce9eb40a022047f0211ad5471d055fe07c75f37f3fad8aeeff1ee11a54a17bab35212c46d5d6", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32353533343636353034", "sig": "304502203fedd83579431889710b67b6454d43ea7eddaaa9da950424e2c4ac730065a822022100b50cef5a9da8323fccd5bf13260dea6517c8ae6ccd6495f9ed7494cfd5891573", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32313833303339313937", "sig": "3044022069ba62b020a36333f7a0716577dd57d280132c540f66b9e2fe8d470121e0f135022066c7811587cb9247ec6d8c223b4c6d5533948fbabf072973d74cb19d3b2c91a6", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "3130353236313736353435", "sig": "30450221008bb763097d8ca8e9cb84e111f361f47de93499f50bc85401ea96a61d54fad7a802202587b81e277283d5c139b8e9a5f4aa0bb0b1c2b28963efddbf73a0eef341659a", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32353031353339393836", "sig": "30450221008260a1eb8ba8b52db95b0722887920a1f9989dfa1efd420d1f8f9ab3df0cffce02206752c5687e6889e008eb9ad6e41933796b4adcd6018420fdef250998f6adf603", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "3134393533313634363139", "sig": "3045022100b809c133b30c3a8ff11ea9024b131664b51c2768afb8536744e041015da9380602206cd015a49e19b260da6cd32a94806fb8bdceec5dc5542a7b2b938cce75137f30", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "32303633383532393036", "sig": "304502202069244f8e72562406d631f647a141831aca5907fbff09932797d8305ad3c19d022100dfb7a3ae1a4bdf76987d7de404c5d8b7c51a6ae8dbece9de345a4b71cb5e1f38", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "37383339373634393932", "sig": "304402204d82e457954761001da6c5dd0fb45d3b8aae12a270cdc5b97d66f810e306532602200cc6217e3aee3839fd809b207d47dea412991932adb2de18ee86431452c22595", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32313239323634343639", "sig": "3045022100a81c6258489e10bb1132a6f81f76c31d7465869708d89eb018c51bf774e8a0930220304d75b7bebe9abda5daafc419a765ffb8e5c02dd91836c40c430f052d5ca59c", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "34383332303432363833", "sig": "3045022100e8da776984d6af2b8d523d1bd6fab8b25409e669d172ef51e104648c1bf0dad1022049b4170fed1cc59ab000087a2c091b3f69a66c8562ed350472e982bd31a0d09e", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "333635323231383136", "sig": "3046022100c53d293775c5cfdb879b67ff4f4792942132c35c9dc7f8fda8b3a00967c75b47022100c36045151f70a5d6af2fd27cf1f13cb308b2e847151fa4b47e22f2df6220ae95", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3234393137313236393635", "sig": "30440220300ee6fa4de853ac6680302a9c439b82dfd046c314d7bbacb2e01274e61e9b5402200bb2b62f11b789848648fab7e0c46ca7b09cded01887ef6bda9f871bc5cc609f", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "32363232373930373738", "sig": "3045022100ac58a18fa5973efb06adba842affbd256a1c624606b146dae5a6ef85992cb428022048b4ecb8697e4cd20e0f30721ad94f4c18943879ce5d99d8c000d90465138bd0", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "313031323537373238393237", "sig": "304402206ebc2857fd53656b857005eb8f95c4f6fd3c99f9636a028e5244edc60bc9e18c022027719f12eb1de6cade547cb98523bdc7108622240f38d12f415c79cd0b1344d9", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31303530383732323032", "sig": "3044022067e8ca1907624419e3ccd88002dd7757f595abc84bd861cd0198364a4571ff6b02200db40b6a7200cdc1a09df432a5a763436ab4130cbdee024dea2a3ddd6c023ed9", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32353037373237303332", "sig": "3045022100f4d8208eed5cb4bfe4ef6ddbbc3742e780e4212a39cf79c9f85605ee64a962cb022043dee8a3c45a45a91e83a18ff3f881047b4fd6ad3003b3af37fb8211eaf7d584", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "373139333435393339", "sig": "304402203363ccc93e2413e47528ab086408bc0521be73353f2c2371bbf2d9dc16e63fb3022068f3d1074a2e06d33fc19a567a8af0edcae923560cf38da2dab82e2249c8dbbd", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "31363337333035373633", "sig": "304502210084960758125ad8de3df0c113ff35f1644e4d43f661c2d81848f3fe4e55846f1802205983630fc8975bab570d2c9f3cbbdecb4dd6179ec497aff312d807ec26ca940a", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "34323431323733373133", "sig": "3044022006ca924b5686a22f2e39c0f980fd58d62bbaf33c3a57f98a315332121e9ba60b02206516b98b31048722ca25e0a6c450461823c0a35f37d671439084fbc27c4779a1", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "343735363134373337", "sig": "304402200093c5ef07a7d955056b88cc8240060b4ffb42835a3df353cbe16ccb62eaf3f60220364dbea6d5ead4202d6fdc253bb0c2c0522b55823e8bb890235ab09ae9030ea2", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "37313632333237373334", "sig": "3045022100835274f3157b737975486c73e5bbdd15b61e2ebb9e580911e45fc288214d2e6702201d5dcebfc6d3ae3826b9a3211f5e2249acc967eb47dfd41a849241ffe779154f", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "31323232343034313239", "sig": "304502203797916fb5c401a691b710050df3eec163383f855f93b61322a56d862ad5572d022100eb7bff4000738a83dd64082eff710d5eb619427198ac290b291f4599768959ca", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "33353234353833333638", "sig": "3044022070992463694c845183db142a77bf5d73c17e9004a8b946b7b8eeb3fdf2b22e00022003843ec28e4c4d4f7726a5a5835575bb3e272f612246bc3aff288ac4a4e90c90", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "3331363334333936", "sig": "3046022100aeaf228b41a1f311d4df74717f4134ee992e5f2922eec65ec83e2db82a866472022100f5ef65eb9fc3feaaac04f71a9a5ceb73e8cd6cb75b4595b39b250a50476cb68f", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "36353332353938343339", "sig": "3045022100b84dbbddd3b8a2ddf67d27e4ef886f72d90cbb7ec2d6883728b27842d61505cc022032be7f0ff420ae3be212beb4c276d93e2527b0964d643c5807c8ee711e66e8e5", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "373338353138353034", "sig": "3046022100f7f6782978f376caf9434941535e1c87ad09b9d39ee936145a0b53b9250fd182022100fb752930c84c29e49f81a997a4d0f00fcdcb4a2f2bf8049cca5d7cf70b079cea", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "31343635323636353739", "sig": "3045022100fb4b434112c1302ffd49ebd993cf5ec59729cbe78739db3c470264e378d56e8d02203aba99bd10be0fba04ca8d9601ae8f68ca7ffe5814f4cfbde78c1cc07a29fd8f", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "32303334383839343934", "sig": "3046022100eafafe851aad76036013bd571772147b7257fb736ed7b4458e0dcf60a2c7b9c0022100b59c6409e51043b7e5c86a8d465978a4c8f78e13ef5b184fe5f46f201ff4efa8", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "34343730383836373835", "sig": "304502206c74d1679d871a46a43c3fe375e09d4f1b6413c59b5e070d7984dae0aadbc37e022100ff1d22228c9e9cf9958d677eed4c3a252b10273ce2d360457faabe7f7439c0e8", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "353537363430343436", "sig": "3046022100cae28b592e2d5bf6f9ea541e70bceedd07adde40bc2b5f883d35ae9560fc85c202210099eaffc16f570b7837d74177dae6e6cfd873ea89424581bc690d0e49c4218402", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "36333434393536363038", "sig": "304502206ce5133579dd044447206b9f6e1605d27f094b2c4466a5bf8e157873176baf3a022100bbe35524d9c1936bacecb6c270bf494eac75933caa8dbb2fef30f6572ed8667c", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34393432383039303331", "sig": "304402202855cf6e65812ea246e366ab961970d19387039a93f0bd406365d68b0356661302201b3a9593117380899d5c8f8f976ad4dee97db9f1225f735b1d41c2a115be93fb", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "323636353831393339", "sig": "304502210090ed3aa89579b73477e3fcba9f52a5a64b9b6d83b475a3881bc0e63d74f6bf9c022004cb5e2ca1d413b37a71607d5b5fa72ccc87a2edcd5c7f30daaa94241b749920", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "33333332323134313134", "sig": "30450220055e38188756831ced4b01e0f9d4db6b02293c7ee2c3fd47860d38377ee0f4190221009c29f1688f16e111914d9c843c0a8f0306c1c4ddd5167cdd54338a4f4ab79a91", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "363136353237373135", "sig": "3045022006b28cd8538d8cc563473cf6c7abb519e4c8bb4c37915ef76512f37de02c2164022100a62dc2afb01a1a9bc877edd54f25fd1f6d0378b3fbaa219ff9ef28c560cc8065", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32333539393535383133", "sig": "3045022100c4b97652702efb1d1b67e966e88789efc0d9eb76d32efbeaf9c1bca36b2ea9a5022013e1b2358c7a34fef3ea738ef1a48fab63a2616455c81f8095394a2230c852e3", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "323931333631393538", "sig": "304502206a86100848566b5f5f89c13643515d81390952b6b5ce56b64fc3349e4edf21df022100c9ca4ba3a6fd501dae9917283a6851692f57dfbfa49d7a31aa937534df760c87", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "31363230393033333936", "sig": "3046022100c1ea204ee71a0502fc47de5d89fad98b897b5c308a4030b4a29de9cc39ff1704022100a5261798ed9665358c31a2368c6705b53b5d7d17023c365ae532573593934481", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33333633313032383432", "sig": "3045022100b3c13e4907afa5a629398ff4fb50c48fae69dd3721a6f62ac13b901efcb4717c02206cb8a95728751b6274fb57e0e8fc87bd7911b1b94fb92edf09ef30fce410efe7", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "363237373433343931", "sig": "30460221009fafebd8841588e56116b2aa354557be814630bae5824e187405f3398f36bc5e022100f01264feb46aeefe68c967e439986f14aeb85ad99b520db572af8d1349d696a6", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "33383536373938313437", "sig": "3046022100b45703f2c6e95c2f2378913cb78ecf7a01932b66d85e6f687dbb618b056851e8022100e1333352ea3ad42d7fd9a52a9b6dd1252848a180606d30012e142d135156720b", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "32383831313830363430", "sig": "304502204138934da6329335702814333f4df8f907df7aa8e684cc38e2366961828ad937022100b90b1b0d77fa39c81f3df7a471499ebbe415a372e7c947eae8612646081aeb47", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "3130393537333934363738", "sig": "3045022100a52a6cca52d60aedc270cfd2fb0e0c2dde1ec4bb61434a7f11cd126ad46bec5602201dba92bb08e5665da3847abf695dbe18aeae37d9fdcd3617fab0c648f48d8ce6", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "353432373131393834", "sig": "3045022100b153363d48a58d339a7e53bfedddb63ea63484569bda2630d61c129a45d352e10220592dc8769b4834fae70f2cf3eea157ea9684c56d4875d296313cdf12e4939df8", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33313530303334333235", "sig": "304402203516e0c8c97110170a5121b5408043f33c6efbef0e5556165812713be6422ae802205d80c5dffa87d3856083bf67beb27e90ebdc2e54d84760c1588f6432ca733195", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31393939383231333931", "sig": "3046022100903d8397244bfc99f2a677507db419597fef6f0cbfd49e0c022709c06c93e358022100f1705f4a19ab86893e0e022bca9081022764bd986c1c891eb80202ec46f50870", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31393731323039323631", "sig": "304502203ba4d01ac8cbfc4abf848253d060a4e3faede188fd01c21657c20b61d1943f43022100ca3b5365ffbb98a5539cbe3e71b3d9fc59b5f1d5bee1122870e153ded9e1ce67", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "33353731383338383537", "sig": "30440220741b76d33821c8cac2361048f10d28060f43e2c30b42b3f1b64a432f322e705c022037331cdebe152ff84bf909183069f278f8b0779042d5486b2d9826b42546952d", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "38363832323639383935", "sig": "3045022100d7988135fd211a2cc09a4588f2d91de3a9a9498d5c5c3ef7e78e9bd80906a63f022025de3162aefbd6afedf01116b4e69d498eaefbf29599a7e0ab60614d64fb3db7", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "32353438383536303331", "sig": "3045022014580cf3aae5ebbb74fed09ff193f347f69ac5b38435eeb7c38a0fd95f5b7ad8022100fcd923fdbdcecb3ece3bd0069c81396b4acf6328648fbe5324ae0c5a276fd87c", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34393935373031333139", "sig": "30450220130821bb00d0f4416ef06761aa283d35383cc2d46ad6be76c96d839adce2dbb6022100d9deef38e7d0f136cc535f1f8931f271cbf0b0d9e4e20fc8db7a1fb3c616bb68", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "32343536363831373835", "sig": "3046022100ab4991cad903fc45f6afe22b939640736aec9788b9d8f94109343649d6327695022100ab126decd1743caf4b461a9c8029cf1230a54a0180e5225a79c075167c2911bc", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "34313835333731323336", "sig": "3045022100dc3b756b20b906f02dc03b46bbef56708be649bb4b23a41ac4333cd79d72749d0220309dfe0623034d6441332aebb327bf5b0fb2f3d6df5a6c02d836fc908e37b0e0", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "373237383439303034", "sig": "3046022100d733b4391a4876d30acdb95977e4fdfded201e698e42be54c5c690b4c83c9036022100f55565475dc58e468b4aaee60eda224770c5b30517944c065758cd5155ae1251", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "31353632383139333138", "sig": "3045022100aa9ea086e301728e0cac7568bf64095b9f51d070edb46679a9983500245e346802203aeb2415f10625c3a4e818da7ddecea27f56f0a393920f6a5d2f4f3054e2131f", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "3231383739393238333836", "sig": "3045022100d5a6ffddece918c5fe4e7d3a11344612bfb0cd2735ce071dfade01244c3b303c02200ff4ee3a70b9984e49277b3b15252c9f255b9ed51c7a4473cf55a7955083a985", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0458e71ffbfd2eabf4e4a465f68100f3d23d4702537dfcca5ee89d18a75ad7f75616535d3b19f050e443bf5dc38f7f7cda9df3798d4a2f65a413a9af5df002828c", "wx": "58e71ffbfd2eabf4e4a465f68100f3d23d4702537dfcca5ee89d18a75ad7f756", "wy": "16535d3b19f050e443bf5dc38f7f7cda9df3798d4a2f65a413a9af5df002828c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000458e71ffbfd2eabf4e4a465f68100f3d23d4702537dfcca5ee89d18a75ad7f75616535d3b19f050e443bf5dc38f7f7cda9df3798d4a2f65a413a9af5df002828c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEWOcf+/0uq/TkpGX2gQDz0j1HAlN9\n/Mpe6J0Yp1rX91YWU107GfBQ5EO/XcOPf3zanfN5jUovZaQTqa9d8AKCjA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 293, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "303502104319055358e8617b0c46353d039cdaab022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}, {"tcId": 294, "comment": "r too large", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000fffffffffffffffffffffffc022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a78dad1701d0551089d3a0ee329a22a9d8bf4263c8a50e0668d24306cf0240b03950b34bb638c683c167a00ac06232c2ef1718d3ed7ebcfc145a41031b04ee0", "wx": "0a78dad1701d0551089d3a0ee329a22a9d8bf4263c8a50e0668d24306cf0240b", "wy": "03950b34bb638c683c167a00ac06232c2ef1718d3ed7ebcfc145a41031b04ee0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040a78dad1701d0551089d3a0ee329a22a9d8bf4263c8a50e0668d24306cf0240b03950b34bb638c683c167a00ac06232c2ef1718d3ed7ebcfc145a41031b04ee0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECnja0XAdBVEInToO4ymiKp2L9CY8\nilDgZo0kMGzwJAsDlQs0u2OMaDwWegCsBiMsLvFxjT7X68/BRaQQMbBO4A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "r,s are large", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254f022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e5b027e1f5daf6e52eca80e35be28651bf849ff3de70d2a34c0d782b5aaad6853c8e2cff9b02c90bf4d7d49c7ff2a261d26aed7d4022b41392c85a857d434579", "wx": "00e5b027e1f5daf6e52eca80e35be28651bf849ff3de70d2a34c0d782b5aaad685", "wy": "3c8e2cff9b02c90bf4d7d49c7ff2a261d26aed7d4022b41392c85a857d434579"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e5b027e1f5daf6e52eca80e35be28651bf849ff3de70d2a34c0d782b5aaad6853c8e2cff9b02c90bf4d7d49c7ff2a261d26aed7d4022b41392c85a857d434579", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5bAn4fXa9uUuyoDjW+KGUb+En/Pe\ncNKjTA14K1qq1oU8jiz/mwLJC/TX1Jx/8qJh0mrtfUAitBOSyFqFfUNFeQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100909135bdb6799286170f5ead2de4f6511453fe50914f3df2de54a36383df8dd4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0424c5462bb7d1f1763ce28b3a9f851d86d7cb4c5f7c61ed9ed7d397f1a920ffc99460936b6919f88646844b27503555262ef8a81e6704f43e07deda12aa06f4ae", "wx": "24c5462bb7d1f1763ce28b3a9f851d86d7cb4c5f7c61ed9ed7d397f1a920ffc9", "wy": "009460936b6919f88646844b27503555262ef8a81e6704f43e07deda12aa06f4ae"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000424c5462bb7d1f1763ce28b3a9f851d86d7cb4c5f7c61ed9ed7d397f1a920ffc99460936b6919f88646844b27503555262ef8a81e6704f43e07deda12aa06f4ae", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJMVGK7fR8XY84os6n4UdhtfLTF98\nYe2e19OX8akg/8mUYJNraRn4hkaESydQNVUmLvioHmcE9D4H3toSqgb0rg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022027b4577ca009376f71303fd5dd227dcef5deb773ad5f5a84360644669ca249a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046226bb83d3cef01ae27b7d04a905397682d5e4a5964b5160dba8a055a2e2aeca7a3630d49d999d0e85e59fe762c9c567cb767ca2a0a7a7756ac917e6085b18e1", "wx": "6226bb83d3cef01ae27b7d04a905397682d5e4a5964b5160dba8a055a2e2aeca", "wy": "7a3630d49d999d0e85e59fe762c9c567cb767ca2a0a7a7756ac917e6085b18e1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046226bb83d3cef01ae27b7d04a905397682d5e4a5964b5160dba8a055a2e2aeca7a3630d49d999d0e85e59fe762c9c567cb767ca2a0a7a7756ac917e6085b18e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEYia7g9PO8Brie30EqQU5doLV5KWW\nS1Fg26igVaLirsp6NjDUnZmdDoXln+diycVny3Z8oqCnp3VqyRfmCFsY4Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0465af8c23310fe060a09e7366d82ea35f48f8e2c682eab3783de7d9711f5923bebebabfaf084741fc806b9698ef87c9459246b7846fa17400094ad0bb222c2cb6", "wx": "65af8c23310fe060a09e7366d82ea35f48f8e2c682eab3783de7d9711f5923be", "wy": "00bebabfaf084741fc806b9698ef87c9459246b7846fa17400094ad0bb222c2cb6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000465af8c23310fe060a09e7366d82ea35f48f8e2c682eab3783de7d9711f5923bebebabfaf084741fc806b9698ef87c9459246b7846fa17400094ad0bb222c2cb6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZa+MIzEP4GCgnnNm2C6jX0j44saC\n6rN4PefZcR9ZI76+ur+vCEdB/IBrlpjvh8lFkka3hG+hdAAJStC7Iiwstg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f26ea876edab91b4070c5ec6e36663fff86f1fe5ef73938b227766b1805773cd07059506a5296d5766d4c55c06eebccf81c04e52cb14c3b198a18808d570d417", "wx": "00f26ea876edab91b4070c5ec6e36663fff86f1fe5ef73938b227766b1805773cd", "wy": "07059506a5296d5766d4c55c06eebccf81c04e52cb14c3b198a18808d570d417"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f26ea876edab91b4070c5ec6e36663fff86f1fe5ef73938b227766b1805773cd07059506a5296d5766d4c55c06eebccf81c04e52cb14c3b198a18808d570d417", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE8m6odu2rkbQHDF7G42Zj//hvH+Xv\nc5OLIndmsYBXc80HBZUGpSltV2bUxVwG7rzPgcBOUssUw7GYoYgI1XDUFw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020105", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047811e16675799076c4f5ea78e5f833be4649925165672057443c436cf4017e0d8e377d53fecdf1556b1cdfdd8270d920cf7c6d32c946af2db4c864faec6b1eba", "wx": "7811e16675799076c4f5ea78e5f833be4649925165672057443c436cf4017e0d", "wy": "008e377d53fecdf1556b1cdfdd8270d920cf7c6d32c946af2db4c864faec6b1eba"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200047811e16675799076c4f5ea78e5f833be4649925165672057443c436cf4017e0d8e377d53fecdf1556b1cdfdd8270d920cf7c6d32c946af2db4c864faec6b1eba", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEeBHhZnV5kHbE9ep45fgzvkZJklFl\nZyBXRDxDbPQBfg2ON31T/s3xVWsc392CcNkgz3xtMslGry20yGT67Gseug==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020106", "result": "valid", "flags": []}, {"tcId": 302, "comment": "r is larger than n", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632556020106", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0403c840f0fcdfbe9cba931726d54a1f9553732be832d8ab701aebade4524b736d942379f10b74b70ec5a06d31c7b65eca6f77a047e25736aace32cf46edf9e90b", "wx": "03c840f0fcdfbe9cba931726d54a1f9553732be832d8ab701aebade4524b736d", "wy": "00942379f10b74b70ec5a06d31c7b65eca6f77a047e25736aace32cf46edf9e90b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000403c840f0fcdfbe9cba931726d54a1f9553732be832d8ab701aebade4524b736d942379f10b74b70ec5a06d31c7b65eca6f77a047e25736aace32cf46edf9e90b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEA8hA8Pzfvpy6kxcm1UoflVNzK+gy\n2KtwGuut5FJLc22UI3nxC3S3DsWgbTHHtl7Kb3egR+JXNqrOMs9G7fnpCw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "s is larger than n", "msg": "313233343030", "sig": "3026020105022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc75fbd8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044b2475ae911ab3e3334bd5acefce2225e35ad7f4523df52c13f581b87898cca195575d5296d1bd97efaa74a12cc0df3d556a614f176c25b06348af8d304ea6c7", "wx": "4b2475ae911ab3e3334bd5acefce2225e35ad7f4523df52c13f581b87898cca1", "wy": "0095575d5296d1bd97efaa74a12cc0df3d556a614f176c25b06348af8d304ea6c7"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044b2475ae911ab3e3334bd5acefce2225e35ad7f4523df52c13f581b87898cca195575d5296d1bd97efaa74a12cc0df3d556a614f176c25b06348af8d304ea6c7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAESyR1rpEas+MzS9Ws784iJeNa1/RS\nPfUsE/WBuHiYzKGVV11SltG9l++qdKEswN89VWphTxdsJbBjSK+NME6mxw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "small r and s^-1", "msg": "313233343030", "sig": "3027020201000221008f1e3c7862c58b16bb76eddbb76eddbb516af4f63f2d74d76e0d28c9bb75ea88", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04211cc26f1f60998bccfc6ae65cfe8f1bf2e70fc28b5aaf8e2a297f3f4460662c3ffc8dbd9b58a341d5160ff03b7a503649967a9a937edbbfc4bf154aa6e1a0ae", "wx": "211cc26f1f60998bccfc6ae65cfe8f1bf2e70fc28b5aaf8e2a297f3f4460662c", "wy": "3ffc8dbd9b58a341d5160ff03b7a503649967a9a937edbbfc4bf154aa6e1a0ae"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004211cc26f1f60998bccfc6ae65cfe8f1bf2e70fc28b5aaf8e2a297f3f4460662c3ffc8dbd9b58a341d5160ff03b7a503649967a9a937edbbfc4bf154aa6e1a0ae", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIRzCbx9gmYvM/GrmXP6PG/LnD8KL\nWq+OKil/P0RgZiw//I29m1ijQdUWD/A7elA2SZZ6mpN+27/EvxVKpuGgrg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302c02072d9b4d347952d6022100ef3043e7329581dbb3974497710ab11505ee1c87ff907beebadd195a0ffe6d7a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a7bb520f0cc16284831167d3622b276487a7bbf41bf911d367b484f1bd81a0c0c30d573d27d44e68fb9a109ac7faad2c57ae09de30d8203ab409cd3ca63af3a", "wx": "0a7bb520f0cc16284831167d3622b276487a7bbf41bf911d367b484f1bd81a0c", "wy": "0c30d573d27d44e68fb9a109ac7faad2c57ae09de30d8203ab409cd3ca63af3a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040a7bb520f0cc16284831167d3622b276487a7bbf41bf911d367b484f1bd81a0c0c30d573d27d44e68fb9a109ac7faad2c57ae09de30d8203ab409cd3ca63af3a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECnu1IPDMFihIMRZ9NiKydkh6e79B\nv5EdNntITxvYGgwMMNVz0n1E5o+5oQmsf6rSxXrgneMNggOrQJzTymOvOg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3032020d1033e67e37b32b445580bf4eff0221008b748b74000000008b748b748b748b7466e769ad4a16d3dcd87129b8e91d1b4d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d528a48fb391dd490d3f32810570613d16fe2709b82245027705e359549b0e155f4a5ac279d55c9ea6371f56403f816ee723632911df9804f01c7fa289eb2361", "wx": "00d528a48fb391dd490d3f32810570613d16fe2709b82245027705e359549b0e15", "wy": "5f4a5ac279d55c9ea6371f56403f816ee723632911df9804f01c7fa289eb2361"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d528a48fb391dd490d3f32810570613d16fe2709b82245027705e359549b0e155f4a5ac279d55c9ea6371f56403f816ee723632911df9804f01c7fa289eb2361", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE1Sikj7OR3UkNPzKBBXBhPRb+Jwm4\nIkUCdwXjWVSbDhVfSlrCedVcnqY3H1ZAP4Fu5yNjKRHfmATwHH+iiesjYQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302702020100022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d1f035f0a28c0c49e6248ff373874da5b26b47e7cd89c1b3bd15402dc9bd7b627a182a1884f30222976579d766da681a7f31fe55b14e770dd0f3f1c09654b29c", "wx": "00d1f035f0a28c0c49e6248ff373874da5b26b47e7cd89c1b3bd15402dc9bd7b62", "wy": "7a182a1884f30222976579d766da681a7f31fe55b14e770dd0f3f1c09654b29c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d1f035f0a28c0c49e6248ff373874da5b26b47e7cd89c1b3bd15402dc9bd7b627a182a1884f30222976579d766da681a7f31fe55b14e770dd0f3f1c09654b29c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0fA18KKMDEnmJI/zc4dNpbJrR+fN\nicGzvRVALcm9e2J6GCoYhPMCIpdleddm2mgafzH+VbFOdw3Q8/HAllSynA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3032020d062522bbd3ecbe7c39e93e7c25022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0407df495050dcb1738f4e2aac5ba2c8a1f8e09d262a3b001865af3fba086d7aa1b596cde482a6bfdc5e49e4069fce7c2d1145d1e0f7fed63f9e848446fae479ed", "wx": "07df495050dcb1738f4e2aac5ba2c8a1f8e09d262a3b001865af3fba086d7aa1", "wy": "00b596cde482a6bfdc5e49e4069fce7c2d1145d1e0f7fed63f9e848446fae479ed"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000407df495050dcb1738f4e2aac5ba2c8a1f8e09d262a3b001865af3fba086d7aa1b596cde482a6bfdc5e49e4069fce7c2d1145d1e0f7fed63f9e848446fae479ed", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEB99JUFDcsXOPTiqsW6LIofjgnSYq\nOwAYZa8/ughteqG1ls3kgqa/3F5J5AafznwtEUXR4Pf+1j+ehIRG+uR57Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3045022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324d50220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ccd70d8730532b28c78c27dbd3043fdde3e96f10ede406582c9cba2618dc03c195d592c366bd189683fd581dde22fb91176b55d94e48dd81467234777d8c223a", "wx": "00ccd70d8730532b28c78c27dbd3043fdde3e96f10ede406582c9cba2618dc03c1", "wy": "0095d592c366bd189683fd581dde22fb91176b55d94e48dd81467234777d8c223a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ccd70d8730532b28c78c27dbd3043fdde3e96f10ede406582c9cba2618dc03c195d592c366bd189683fd581dde22fb91176b55d94e48dd81467234777d8c223a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzNcNhzBTKyjHjCfb0wQ/3ePpbxDt\n5AZYLJy6JhjcA8GV1ZLDZr0YloP9WB3eIvuRF2tV2U5I3YFGcjR3fYwiOg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "s == 1", "msg": "313233343030", "sig": "30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020101", "result": "valid", "flags": []}, {"tcId": 311, "comment": "s == 0", "msg": "313233343030", "sig": "30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fc13b37baba182ba13dfc8ca74f5896483378aa9bd6f0aa931877ddc5e77262f1bf8b9cfdcbbe0d62eed81e5874310bd51178d1c6d01b6929a345d94190fdf3b", "wx": "00fc13b37baba182ba13dfc8ca74f5896483378aa9bd6f0aa931877ddc5e77262f", "wy": "1bf8b9cfdcbbe0d62eed81e5874310bd51178d1c6d01b6929a345d94190fdf3b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fc13b37baba182ba13dfc8ca74f5896483378aa9bd6f0aa931877ddc5e77262f1bf8b9cfdcbbe0d62eed81e5874310bd51178d1c6d01b6929a345d94190fdf3b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE/BOze6uhgroT38jKdPWJZIM3iqm9\nbwqpMYd93F53Ji8b+LnP3Lvg1i7tgeWHQxC9UReNHG0BtpKaNF2UGQ/fOw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a80220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0420adbb6cb9e09ce8ee4b6bdbc2e8047a0b9dc811eb415a2a258906efbd8a88cec16b2111b5991d98dc4c935da619b55f784c79f000830d514ffeb6ad3fcf0640", "wx": "20adbb6cb9e09ce8ee4b6bdbc2e8047a0b9dc811eb415a2a258906efbd8a88ce", "wy": "00c16b2111b5991d98dc4c935da619b55f784c79f000830d514ffeb6ad3fcf0640"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000420adbb6cb9e09ce8ee4b6bdbc2e8047a0b9dc811eb415a2a258906efbd8a88cec16b2111b5991d98dc4c935da619b55f784c79f000830d514ffeb6ad3fcf0640", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIK27bLngnOjuS2vbwugEegudyBHr\nQVoqJYkG772KiM7BayERtZkdmNxMk12mGbVfeEx58ACDDVFP/ratP88GQA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f033aa211cf11ee61c247567974fb8667c78f13a35bc2e6bead4436c261f144d99b4d07b6ce8008fecf8a4c4af561b972b00e63443a2f20038ee84ed0c238a3c", "wx": "00f033aa211cf11ee61c247567974fb8667c78f13a35bc2e6bead4436c261f144d", "wy": "0099b4d07b6ce8008fecf8a4c4af561b972b00e63443a2f20038ee84ed0c238a3c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f033aa211cf11ee61c247567974fb8667c78f13a35bc2e6bead4436c261f144d99b4d07b6ce8008fecf8a4c4af561b972b00e63443a2f20038ee84ed0c238a3c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE8DOqIRzxHuYcJHVnl0+4Znx48To1\nvC5r6tRDbCYfFE2ZtNB7bOgAj+z4pMSvVhuXKwDmNEOi8gA47oTtDCOKPA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bef2538da3d07158791556b2d0297ca9c1b306459c9323ce7d07a21282de1ace4e400c8e4eb57751faa0dde6bbebf96faaac9efc80e3de768fb4f5a37f95ead7", "wx": "00bef2538da3d07158791556b2d0297ca9c1b306459c9323ce7d07a21282de1ace", "wy": "4e400c8e4eb57751faa0dde6bbebf96faaac9efc80e3de768fb4f5a37f95ead7"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bef2538da3d07158791556b2d0297ca9c1b306459c9323ce7d07a21282de1ace4e400c8e4eb57751faa0dde6bbebf96faaac9efc80e3de768fb4f5a37f95ead7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvvJTjaPQcVh5FVay0Cl8qcGzBkWc\nkyPOfQeiEoLeGs5OQAyOTrV3Ufqg3ea76/lvqqye/IDj3naPtPWjf5Xq1w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "u1 == 1", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c7002205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bf2570b58f38183fabca3ca72255bd4651cbb7e8292287809bd8e5c285d24a532f859b7f75c2f5e8d3791a5ccb60fa3888895c63237c9ea65e43f87523e104e5", "wx": "00bf2570b58f38183fabca3ca72255bd4651cbb7e8292287809bd8e5c285d24a53", "wy": "2f859b7f75c2f5e8d3791a5ccb60fa3888895c63237c9ea65e43f87523e104e5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bf2570b58f38183fabca3ca72255bd4651cbb7e8292287809bd8e5c285d24a532f859b7f75c2f5e8d3791a5ccb60fa3888895c63237c9ea65e43f87523e104e5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvyVwtY84GD+ryjynIlW9RlHLt+gp\nIoeAm9jlwoXSSlMvhZt/dcL16NN5GlzLYPo4iIlcYyN8nqZeQ/h1I+EE5Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100a8ce483a42fb3462047c96ca00d1ab83ca565b2724cca9ac14411dcb8ee7e803", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049ba8f147332270987e5baab2ab0a4ebc9968eb8682c2872266a22b43c2cf55f7728d552fc65b5a3c7cee18876f1d8b46ae60153aec3b8a2b2c2527979f4a7d29", "wx": "009ba8f147332270987e5baab2ab0a4ebc9968eb8682c2872266a22b43c2cf55f7", "wy": "728d552fc65b5a3c7cee18876f1d8b46ae60153aec3b8a2b2c2527979f4a7d29"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049ba8f147332270987e5baab2ab0a4ebc9968eb8682c2872266a22b43c2cf55f7728d552fc65b5a3c7cee18876f1d8b46ae60153aec3b8a2b2c2527979f4a7d29", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEm6jxRzMicJh+W6qyqwpOvJlo64aC\nwociZqIrQ8LPVfdyjVUvxltaPHzuGIdvHYtGrmAVOuw7iissJSeXn0p9KQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "u2 == 1", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c700220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d9e64db2ea560162dad3ec67d6ebaab9e821a81da8d4584f00fb14813c7e96e153e9e96e17eb05228ff3c9cbc5318bbb87e88bec489dec2be7a20adce06cf8bd", "wx": "00d9e64db2ea560162dad3ec67d6ebaab9e821a81da8d4584f00fb14813c7e96e1", "wy": "53e9e96e17eb05228ff3c9cbc5318bbb87e88bec489dec2be7a20adce06cf8bd"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d9e64db2ea560162dad3ec67d6ebaab9e821a81da8d4584f00fb14813c7e96e153e9e96e17eb05228ff3c9cbc5318bbb87e88bec489dec2be7a20adce06cf8bd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE2eZNsupWAWLa0+xn1uuqueghqB2o\n1FhPAPsUgTx+luFT6eluF+sFIo/zycvFMYu7h+iL7Eid7Cvnogrc4Gz4vQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100aaaaaaaa00000000aaaaaaaaaaaaaaaa7def51c91a0fbf034d26872ca84218e1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04857c58d9010d1f8386e279cdcc369b32a8960259a3a646f6d89ad5273252f3fc65d2384cabf6a2158b1cd1b2e2477d10b1b719125e9226e99ae90a7afaab499e", "wx": "00857c58d9010d1f8386e279cdcc369b32a8960259a3a646f6d89ad5273252f3fc", "wy": "65d2384cabf6a2158b1cd1b2e2477d10b1b719125e9226e99ae90a7afaab499e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004857c58d9010d1f8386e279cdcc369b32a8960259a3a646f6d89ad5273252f3fc65d2384cabf6a2158b1cd1b2e2477d10b1b719125e9226e99ae90a7afaab499e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhXxY2QENH4OG4nnNzDabMqiWAlmj\npkb22JrVJzJS8/xl0jhMq/aiFYsc0bLiR30QsbcZEl6SJuma6Qp6+qtJng==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02201d109296e9ac43dfa92bcdbcaa64c6d3fb858a822b6e519d9fd2e45279d3bf1a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0489d887b0645d2f96b407b080cf6db3685cc9d4454d35a5ac7983bb5ebbfd2e20de4fcd410c3b6e11f5e4cccb19327c181c43c2d216869309f22495d34ee2796f", "wx": "0089d887b0645d2f96b407b080cf6db3685cc9d4454d35a5ac7983bb5ebbfd2e20", "wy": "00de4fcd410c3b6e11f5e4cccb19327c181c43c2d216869309f22495d34ee2796f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000489d887b0645d2f96b407b080cf6db3685cc9d4454d35a5ac7983bb5ebbfd2e20de4fcd410c3b6e11f5e4cccb19327c181c43c2d216869309f22495d34ee2796f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEidiHsGRdL5a0B7CAz22zaFzJ1EVN\nNaWseYO7Xrv9LiDeT81BDDtuEfXkzMsZMnwYHEPC0haGkwnyJJXTTuJ5bw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220027d377d04715e43754629961c6233961b921b3283c33fcb541cc27285092e8d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04040924291aa7975fd04f8b2e923a1f9121836fdfbf2fea123cc1870f4f6cc0f2c510ee34a325e772d232b576052f96d3ec4a33b086508682fc53099c0cd48e45", "wx": "040924291aa7975fd04f8b2e923a1f9121836fdfbf2fea123cc1870f4f6cc0f2", "wy": "00c510ee34a325e772d232b576052f96d3ec4a33b086508682fc53099c0cd48e45"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004040924291aa7975fd04f8b2e923a1f9121836fdfbf2fea123cc1870f4f6cc0f2c510ee34a325e772d232b576052f96d3ec4a33b086508682fc53099c0cd48e45", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBAkkKRqnl1/QT4sukjofkSGDb9+/\nL+oSPMGHD09swPLFEO40oyXnctIytXYFL5bT7EozsIZQhoL8UwmcDNSORQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ce1602ee5a6d686c5b8d8a3f44f419aa6064f0d35323341d77a65a4bc9e1989b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043e7ed2fbb89f7b643d4ab44895ff9fb16e8be7a8649e4ac4ee2f59ec8f68fc634ca91cc26043a8242e2969c871d3ca9833148135b27d377198182ceaa7e70fd4", "wx": "3e7ed2fbb89f7b643d4ab44895ff9fb16e8be7a8649e4ac4ee2f59ec8f68fc63", "wy": "4ca91cc26043a8242e2969c871d3ca9833148135b27d377198182ceaa7e70fd4"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043e7ed2fbb89f7b643d4ab44895ff9fb16e8be7a8649e4ac4ee2f59ec8f68fc634ca91cc26043a8242e2969c871d3ca9833148135b27d377198182ceaa7e70fd4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPn7S+7ife2Q9SrRIlf+fsW6L56hk\nnkrE7i9Z7I9o/GNMqRzCYEOoJC4pachx08qYMxSBNbJ9N3GYGCzqp+cP1A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220328ab273ff681a79a9662dc174ee014ef73d597d32ef42b17f443a33f5e430fe", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e67f559f552772b174d377b239e60750299d379b6bec6fc93adf040269d58c426c397f7984a149f07bf79fbba3b18c925a797cc6678e2eeabec47fb4ac461041", "wx": "00e67f559f552772b174d377b239e60750299d379b6bec6fc93adf040269d58c42", "wy": "6c397f7984a149f07bf79fbba3b18c925a797cc6678e2eeabec47fb4ac461041"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e67f559f552772b174d377b239e60750299d379b6bec6fc93adf040269d58c426c397f7984a149f07bf79fbba3b18c925a797cc6678e2eeabec47fb4ac461041", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5n9Vn1UncrF003eyOeYHUCmdN5tr\n7G/JOt8EAmnVjEJsOX95hKFJ8Hv3n7ujsYySWnl8xmeOLuq+xH+0rEYQQQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ab27431e81a7976e62dc174ee014f0479c909f17919ec453013b47f1aa221858", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0488b1d30e29fe0edeb93ab469d2698d0fbc2977f77f48293d0e87acc0856a51fc3d1b4f23fa3f6ef26f0e94cb7a63907b1923e30d08197115050b9da98a2b5f56", "wx": "0088b1d30e29fe0edeb93ab469d2698d0fbc2977f77f48293d0e87acc0856a51fc", "wy": "3d1b4f23fa3f6ef26f0e94cb7a63907b1923e30d08197115050b9da98a2b5f56"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000488b1d30e29fe0edeb93ab469d2698d0fbc2977f77f48293d0e87acc0856a51fc3d1b4f23fa3f6ef26f0e94cb7a63907b1923e30d08197115050b9da98a2b5f56", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiLHTDin+Dt65OrRp0mmND7wpd/d/\nSCk9DoeswIVqUfw9G08j+j9u8m8OlMt6Y5B7GSPjDQgZcRUFC52piitfVg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220564e863e034f2edbc5b82e9dc029e08f7c3a43817c25ea210ebcc52057e10b5f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0423a6ed2b1064923104d08ee4392b44bb51555a395477dc52546af6c787cc65aa81105b8c72c357d75215b210286df781d6731c4f0b87e9fe7066489653dc35d3", "wx": "23a6ed2b1064923104d08ee4392b44bb51555a395477dc52546af6c787cc65aa", "wy": "0081105b8c72c357d75215b210286df781d6731c4f0b87e9fe7066489653dc35d3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000423a6ed2b1064923104d08ee4392b44bb51555a395477dc52546af6c787cc65aa81105b8c72c357d75215b210286df781d6731c4f0b87e9fe7066489653dc35d3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEI6btKxBkkjEE0I7kOStEu1FVWjlU\nd9xSVGr2x4fMZaqBEFuMcsNX11IVshAobfeB1nMcTwuH6f5wZkiWU9w10w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022031f2cced76db7b4d74ee014f047c96c9f3ba3e21f11248bcf451526ac376c54c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04592d27cd81fbb61ebbdd782eaa1d86d53b59eaef43496677c345adc9896c562e355b8ffda4f8683da98653f0d6067bd8134c5c3e22e3dcdee6a5cdbd826f4915", "wx": "592d27cd81fbb61ebbdd782eaa1d86d53b59eaef43496677c345adc9896c562e", "wy": "355b8ffda4f8683da98653f0d6067bd8134c5c3e22e3dcdee6a5cdbd826f4915"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004592d27cd81fbb61ebbdd782eaa1d86d53b59eaef43496677c345adc9896c562e355b8ffda4f8683da98653f0d6067bd8134c5c3e22e3dcdee6a5cdbd826f4915", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEWS0nzYH7th673Xguqh2G1TtZ6u9D\nSWZ3w0WtyYlsVi41W4/9pPhoPamGU/DWBnvYE0xcPiLj3N7mpc29gm9JFQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220668fcfbedd4eed7eb6840c7f6cf1e3dde504afe5732ee0e1bcbeee15b94a2c64", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048c4aa42ef47c4e2d7b60ca2b5a0b3038a9f8e7ee1de77d299286db3cd635b754f65438558a2271c9444b77405a1f97e84036c3146c425006e65be83f97e41191", "wx": "008c4aa42ef47c4e2d7b60ca2b5a0b3038a9f8e7ee1de77d299286db3cd635b754", "wy": "00f65438558a2271c9444b77405a1f97e84036c3146c425006e65be83f97e41191"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048c4aa42ef47c4e2d7b60ca2b5a0b3038a9f8e7ee1de77d299286db3cd635b754f65438558a2271c9444b77405a1f97e84036c3146c425006e65be83f97e41191", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEjEqkLvR8Ti17YMorWgswOKn45+4d\n530pkobbPNY1t1T2VDhViiJxyURLd0BaH5foQDbDFGxCUAbmW+g/l+QRkQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220637e57bd4f085f9d3be20506bbc2b8eab268a33871b19da56b1ba0ac25927bd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f5618fb978b70f15b8e07a74edfbcea775dcb92055f9431b816cd4cb5d4fd63cbd1759fd35bae79bf5bb0394646b14fbcb1ed2614fdcc9a9f53663e09f8c6a09", "wx": "00f5618fb978b70f15b8e07a74edfbcea775dcb92055f9431b816cd4cb5d4fd63c", "wy": "00bd1759fd35bae79bf5bb0394646b14fbcb1ed2614fdcc9a9f53663e09f8c6a09"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f5618fb978b70f15b8e07a74edfbcea775dcb92055f9431b816cd4cb5d4fd63cbd1759fd35bae79bf5bb0394646b14fbcb1ed2614fdcc9a9f53663e09f8c6a09", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9WGPuXi3DxW44Hp07fvOp3XcuSBV\n+UMbgWzUy11P1jy9F1n9Nbrnm/W7A5RkaxT7yx7SYU/cyan1NmPgn4xqCQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100b0a105281711f1755bbfc1a0b6ea67add1085e84b73016989e20a90be3504d22", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0453c143435247e1e2144c4c32cb1c900b8e9cb160976bdcda1b24877ce7266a7441a21780d91554d349a4c7c61f799bda9ddc81a66323078245dcb3960417a660", "wx": "53c143435247e1e2144c4c32cb1c900b8e9cb160976bdcda1b24877ce7266a74", "wy": "41a21780d91554d349a4c7c61f799bda9ddc81a66323078245dcb3960417a660"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000453c143435247e1e2144c4c32cb1c900b8e9cb160976bdcda1b24877ce7266a7441a21780d91554d349a4c7c61f799bda9ddc81a66323078245dcb3960417a660", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU8FDQ1JH4eIUTEwyyxyQC46csWCX\na9zaGySHfOcmanRBoheA2RVU00mkx8YfeZvandyBpmMjB4JF3LOWBBemYA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100f177b6b38b29de112b6a1921aacd9c95bf24356c916075b623d05899bf7945c4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0493486f6653c2906152eb9d1c2b28e51c085f20ac54016a808f6e3c6b2cdcc02a35439b7b9ab9e86df0ca617737b49f28badf8f5636c9bbaa199bdd20063ec7ff", "wx": "0093486f6653c2906152eb9d1c2b28e51c085f20ac54016a808f6e3c6b2cdcc02a", "wy": "35439b7b9ab9e86df0ca617737b49f28badf8f5636c9bbaa199bdd20063ec7ff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000493486f6653c2906152eb9d1c2b28e51c085f20ac54016a808f6e3c6b2cdcc02a35439b7b9ab9e86df0ca617737b49f28badf8f5636c9bbaa199bdd20063ec7ff", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEk0hvZlPCkGFS650cKyjlHAhfIKxU\nAWqAj248ayzcwCo1Q5t7mrnobfDKYXc3tJ8out+PVjbJu6oZm90gBj7H/w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100e2ef6d681653bc2156d43243559b392bc161702b7ba94ce753e6e670828f6637", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045f2e06f0ec92b6499eb7d249ff0147639253e7abe0e4497226336a5c94caa7774eb3c28acf5012ba023971416c600a10fb6d28a23f3a2c1f77fb0686d06cdf80", "wx": "5f2e06f0ec92b6499eb7d249ff0147639253e7abe0e4497226336a5c94caa777", "wy": "4eb3c28acf5012ba023971416c600a10fb6d28a23f3a2c1f77fb0686d06cdf80"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200045f2e06f0ec92b6499eb7d249ff0147639253e7abe0e4497226336a5c94caa7774eb3c28acf5012ba023971416c600a10fb6d28a23f3a2c1f77fb0686d06cdf80", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEXy4G8OyStkmet9JJ/wFHY5JT56vg\n5ElyJjNqXJTKp3dOs8KKz1ASugI5cUFsYAoQ+20ooj86LB93+waG0GzfgA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100d467241ca17d9a31823e4b650068d5c1c39eaaea65f2241883fd744745a586aa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c2af0b9da06d54b1a5ff93800b579cbce295d0b2719da307b028bee3c657424b28c4928f185f68312b47de31ad87fac134de90cf114cc85d45a8fefd9a3a2350", "wx": "00c2af0b9da06d54b1a5ff93800b579cbce295d0b2719da307b028bee3c657424b", "wy": "28c4928f185f68312b47de31ad87fac134de90cf114cc85d45a8fefd9a3a2350"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004c2af0b9da06d54b1a5ff93800b579cbce295d0b2719da307b028bee3c657424b28c4928f185f68312b47de31ad87fac134de90cf114cc85d45a8fefd9a3a2350", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEwq8LnaBtVLGl/5OAC1ecvOKV0LJx\nnaMHsCi+48ZXQksoxJKPGF9oMStH3jGth/rBNN6QzxFMyF1FqP79mjojUA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100e70b0176ad36b436adc6c51fa27a0cd50ea5f5c07d1d695135b0128763225ef6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042a3c0253d54dc8a2a72a31f815b0bb6c36d852f8db14edf1e1b71cd3a7389a494891bafa1767b85e36f7507fa5eebd3da0024208fcfef28d56cd49a980ba1465", "wx": "2a3c0253d54dc8a2a72a31f815b0bb6c36d852f8db14edf1e1b71cd3a7389a49", "wy": "4891bafa1767b85e36f7507fa5eebd3da0024208fcfef28d56cd49a980ba1465"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042a3c0253d54dc8a2a72a31f815b0bb6c36d852f8db14edf1e1b71cd3a7389a494891bafa1767b85e36f7507fa5eebd3da0024208fcfef28d56cd49a980ba1465", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKjwCU9VNyKKnKjH4FbC7bDbYUvjb\nFO3x4bcc06c4mklIkbr6F2e4Xjb3UH+l7r09oAJCCPz+8o1WzUmpgLoUZQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffffaaaaaaaaffffffffffffffffe9a2538f37b28a2c513dee40fecbb71a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043dd345114090328ccc0bdeaf8269396593645720b0b326849d1fe81ec956f996ceee0a81d7f65e1205bb1b6963a8e0facfd2a6124701b1a152094d037a216f4e", "wx": "3dd345114090328ccc0bdeaf8269396593645720b0b326849d1fe81ec956f996", "wy": "00ceee0a81d7f65e1205bb1b6963a8e0facfd2a6124701b1a152094d037a216f4e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043dd345114090328ccc0bdeaf8269396593645720b0b326849d1fe81ec956f996ceee0a81d7f65e1205bb1b6963a8e0facfd2a6124701b1a152094d037a216f4e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPdNFEUCQMozMC96vgmk5ZZNkVyCw\nsyaEnR/oHslW+ZbO7gqB1/ZeEgW7G2ljqOD6z9KmEkcBsaFSCU0DeiFvTg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100b62f26b5f2a2b26f6de86d42ad8a13da3ab3cccd0459b201de009e526adf21f2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fc8207ca84c4af4229139de953da3bdebf694537c15406e172d631e98591f40c34a0d957e39e9686914e98ea467972cedec5a5c6bb55bec7916dc71f7a4c6f77", "wx": "00fc8207ca84c4af4229139de953da3bdebf694537c15406e172d631e98591f40c", "wy": "34a0d957e39e9686914e98ea467972cedec5a5c6bb55bec7916dc71f7a4c6f77"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fc8207ca84c4af4229139de953da3bdebf694537c15406e172d631e98591f40c34a0d957e39e9686914e98ea467972cedec5a5c6bb55bec7916dc71f7a4c6f77", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE/IIHyoTEr0IpE53pU9o73r9pRTfB\nVAbhctYx6YWR9Aw0oNlX456WhpFOmOpGeXLO3sWlxrtVvseRbccfekxvdw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bb1d9ac949dd748cd02bbbe749bd351cd57b38bb61403d700686aa7b4c90851e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0460bf7beb808286d8abff60c20faed73997395124542e6b7672089d88c14bbed57f4af9606f9be0199e4145698a62ad2545123a49eb14e0c33317f6909e3915b5", "wx": "60bf7beb808286d8abff60c20faed73997395124542e6b7672089d88c14bbed5", "wy": "7f4af9606f9be0199e4145698a62ad2545123a49eb14e0c33317f6909e3915b5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000460bf7beb808286d8abff60c20faed73997395124542e6b7672089d88c14bbed57f4af9606f9be0199e4145698a62ad2545123a49eb14e0c33317f6909e3915b5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEYL9764CChtir/2DCD67XOZc5USRU\nLmt2cgidiMFLvtV/Svlgb5vgGZ5BRWmKYq0lRRI6SesU4MMzF/aQnjkVtQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022066755a00638cdaec1c732513ca0234ece52545dac11f816e818f725b4f60aaf2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042dcd699362d3665b6c9260608b3faf989d45ac15b9da41fb348d5520ecdf4e0403e483670aadef4615c7a13fe1bf3bf927b4e47a667660b505ba47affee92ab6", "wx": "2dcd699362d3665b6c9260608b3faf989d45ac15b9da41fb348d5520ecdf4e04", "wy": "03e483670aadef4615c7a13fe1bf3bf927b4e47a667660b505ba47affee92ab6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042dcd699362d3665b6c9260608b3faf989d45ac15b9da41fb348d5520ecdf4e0403e483670aadef4615c7a13fe1bf3bf927b4e47a667660b505ba47affee92ab6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAELc1pk2LTZltskmBgiz+vmJ1FrBW5\n2kH7NI1VIOzfTgQD5INnCq3vRhXHoT/hvzv5J7TkemZ2YLUFukev/ukqtg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022055a00c9fcdaebb6032513ca0234ecfffe98ebe492fdf02e48ca48e982beb3669", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e6205f87fa837c474a2badac671578de77d6a077cd286aed45403508767114ffc18daaf2463dea80300c1f4d7e25b9f603eefb2e2cbf012f31a819c91cad7cf2", "wx": "00e6205f87fa837c474a2badac671578de77d6a077cd286aed45403508767114ff", "wy": "00c18daaf2463dea80300c1f4d7e25b9f603eefb2e2cbf012f31a819c91cad7cf2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e6205f87fa837c474a2badac671578de77d6a077cd286aed45403508767114ffc18daaf2463dea80300c1f4d7e25b9f603eefb2e2cbf012f31a819c91cad7cf2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5iBfh/qDfEdKK62sZxV43nfWoHfN\nKGrtRUA1CHZxFP/BjaryRj3qgDAMH01+Jbn2A+77Liy/AS8xqBnJHK188g==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ab40193f9b5d76c064a27940469d9fffd31d7c925fbe05c919491d3057d66cd2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04357e7687a79243d5e030eb120a3652c2fb95fcb148813f3da95d044bdc31c8d5e3ed90ea73567cb36c0fccd021da4ccccffe40dfe1b603428969788bed4416db", "wx": "357e7687a79243d5e030eb120a3652c2fb95fcb148813f3da95d044bdc31c8d5", "wy": "00e3ed90ea73567cb36c0fccd021da4ccccffe40dfe1b603428969788bed4416db"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004357e7687a79243d5e030eb120a3652c2fb95fcb148813f3da95d044bdc31c8d5e3ed90ea73567cb36c0fccd021da4ccccffe40dfe1b603428969788bed4416db", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENX52h6eSQ9XgMOsSCjZSwvuV/LFI\ngT89qV0ES9wxyNXj7ZDqc1Z8s2wPzNAh2kzMz/5A3+G2A0KJaXiL7UQW2w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ca0234ebb5fdcb13ca0234ecffffffffcb0dadbbc7f549f8a26b4408d0dc8600", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043d9723a8ea5ebaffacab8bf87b1d63e42da7bdf94e6c2520a0786b7b534dacf33725db2fb27248274ac2e6212f9071495c90ae684d056b57ad18e72bce8f36b0", "wx": "3d9723a8ea5ebaffacab8bf87b1d63e42da7bdf94e6c2520a0786b7b534dacf3", "wy": "3725db2fb27248274ac2e6212f9071495c90ae684d056b57ad18e72bce8f36b0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043d9723a8ea5ebaffacab8bf87b1d63e42da7bdf94e6c2520a0786b7b534dacf33725db2fb27248274ac2e6212f9071495c90ae684d056b57ad18e72bce8f36b0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPZcjqOpeuv+sq4v4ex1j5C2nvflO\nbCUgoHhre1NNrPM3JdsvsnJIJ0rC5iEvkHFJXJCuaE0Fa1etGOcrzo82sA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff3ea3677e082b9310572620ae19933a9e65b285598711c77298815ad3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044bab5b68667090ed13e5658cfe68f1247031aee80a8ccb52ba0505752f7cd3f085c70129c1715d9610a41bf7a063b81c1bc7ec34bb6a1c95ccd08e09f1476343", "wx": "4bab5b68667090ed13e5658cfe68f1247031aee80a8ccb52ba0505752f7cd3f0", "wy": "0085c70129c1715d9610a41bf7a063b81c1bc7ec34bb6a1c95ccd08e09f1476343"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044bab5b68667090ed13e5658cfe68f1247031aee80a8ccb52ba0505752f7cd3f085c70129c1715d9610a41bf7a063b81c1bc7ec34bb6a1c95ccd08e09f1476343", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFk<PERSON>EwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES6tbaGZwkO0T5WWM/mjxJHAxrugK\njMtSugUFdS980/CFxwEpwXFdlhCkG/egY7gcG8fsNLtqHJXM0I4J8UdjQw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220266666663bbbbbbbe6666666666666665b37902e023fab7c8f055d86e5cc41f4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047801780aaab4aaf31b7c94069609a5ecf623a6dd7e97964061c6b3e4103bb84a59c111796624cccbba09394bca04af79a31cbd36176d2ec4ceaa700730d57300", "wx": "7801780aaab4aaf31b7c94069609a5ecf623a6dd7e97964061c6b3e4103bb84a", "wy": "59c111796624cccbba09394bca04af79a31cbd36176d2ec4ceaa700730d57300"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200047801780aaab4aaf31b7c94069609a5ecf623a6dd7e97964061c6b3e4103bb84a59c111796624cccbba09394bca04af79a31cbd36176d2ec4ceaa700730d57300", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEeAF4Cqq0qvMbfJQGlgml7PYjpt1+\nl5ZAYcaz5BA7uEpZwRF5ZiTMy7oJOUvKBK95oxy9NhdtLsTOqnAHMNVzAA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff36db6db7a492492492492492146c573f4c6dfc8d08a443e258970b09", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0455b0451d911e9c64516ac9e9da3da1703eaaa46a8b0a7025c8c5ed38b5474713f1fde0cdee830bf169da9ca3d70d56f4607989873fbdcfcbb740e9a42faf860a", "wx": "55b0451d911e9c64516ac9e9da3da1703eaaa46a8b0a7025c8c5ed38b5474713", "wy": "00f1fde0cdee830bf169da9ca3d70d56f4607989873fbdcfcbb740e9a42faf860a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000455b0451d911e9c64516ac9e9da3da1703eaaa46a8b0a7025c8c5ed38b5474713f1fde0cdee830bf169da9ca3d70d56f4607989873fbdcfcbb740e9a42faf860a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVbBFHZEenGRRasnp2j2hcD6qpGqL\nCnAlyMXtOLVHRxPx/eDN7oML8WnanKPXDVb0YHmJhz+9z8u3QOmkL6+GCg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff2aaaaaab7fffffffffffffffc815d0e60b3e596ecb1ad3a27cfd49c4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042e19ea3a973a4c155814f8a7b641e12477d288f958b74f6031326356f5061fa41acdd1be10c052eaeb9c22d3f04cfec6e91bd23d6d3996eca9cd485e50e85909", "wx": "2e19ea3a973a4c155814f8a7b641e12477d288f958b74f6031326356f5061fa4", "wy": "1acdd1be10c052eaeb9c22d3f04cfec6e91bd23d6d3996eca9cd485e50e85909"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042e19ea3a973a4c155814f8a7b641e12477d288f958b74f6031326356f5061fa41acdd1be10c052eaeb9c22d3f04cfec6e91bd23d6d3996eca9cd485e50e85909", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAELhnqOpc6TBVYFPintkHhJHfSiPlY\nt09gMTJjVvUGH6QazdG+EMBS6uucItPwTP7G6RvSPW05luypzUheUOhZCQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffff55555555ffffffffffffffffd344a71e6f651458a27bdc81fd976e37", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046eafbcb683e05e0bdb2aa0ac0686f60b34ce66761b7ecffccd3da8fe8799d6244547b4aeca8a8e56dba45750cd9fc4f0e3f1333dcb855566c29bd14457cf489b", "wx": "6eafbcb683e05e0bdb2aa0ac0686f60b34ce66761b7ecffccd3da8fe8799d624", "wy": "4547b4aeca8a8e56dba45750cd9fc4f0e3f1333dcb855566c29bd14457cf489b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046eafbcb683e05e0bdb2aa0ac0686f60b34ce66761b7ecffccd3da8fe8799d6244547b4aeca8a8e56dba45750cd9fc4f0e3f1333dcb855566c29bd14457cf489b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEbq+8toPgXgvbKqCsBob2CzTOZnYb\nfs/8zT2o/oeZ1iRFR7SuyoqOVtukV1DNn8Tw4/EzPcuFVWbCm9FEV89Imw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02203fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192aa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0450f51ce959e24ac86b1054ea016c57d1da5f4cee008dd800757a817606234f78aa17f3ef6f7a6c51381c63d66697b1b5c196eb1da73d7b73c33f9115d7432d23", "wx": "50f51ce959e24ac86b1054ea016c57d1da5f4cee008dd800757a817606234f78", "wy": "00aa17f3ef6f7a6c51381c63d66697b1b5c196eb1da73d7b73c33f9115d7432d23"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000450f51ce959e24ac86b1054ea016c57d1da5f4cee008dd800757a817606234f78aa17f3ef6f7a6c51381c63d66697b1b5c196eb1da73d7b73c33f9115d7432d23", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEUPUc6VniSshrEFTqAWxX0dpfTO4A\njdgAdXqBdgYjT3iqF/Pvb3psUTgcY9Zml7G1wZbrHac9e3PDP5EV10MtIw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205d8ecd64a4eeba466815ddf3a4de9a8e6abd9c5db0a01eb80343553da648428f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee10660b936644e1ccad24578811dd45a325214e28a78e99a0ed2df7354fe9bca0ad", "wx": "009c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee10", "wy": "660b936644e1ccad24578811dd45a325214e28a78e99a0ed2df7354fe9bca0ad"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee10660b936644e1ccad24578811dd45a325214e28a78e99a0ed2df7354fe9bca0ad", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEnI7zbxmBVXLbFU6PR6jcXMgH1VGn\nFB/tiiwVRg/n7hBmC5NmROHMrSRXiBHdRaMlIU4op46ZoO0t9zVP6bygrQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "point duplication during verification", "msg": "313233343030", "sig": "304402206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022056be8aaebb8627ef5e37057feb3448f726fb605312992466ee8d9ed7cd43c1b1", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee1099f46c98bb1e3353dba877ee22ba5cdadeb1d75971665f12d208cab016435f52", "wx": "009c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee10", "wy": "0099f46c98bb1e3353dba877ee22ba5cdadeb1d75971665f12d208cab016435f52"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049c8ef36f19815572db154e8f47a8dc5cc807d551a7141fed8a2c15460fe7ee1099f46c98bb1e3353dba877ee22ba5cdadeb1d75971665f12d208cab016435f52", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEnI7zbxmBVXLbFU6PR6jcXMgH1VGn\nFB/tiiwVRg/n7hCZ9GyYux4zU9uod+4iulza3rHXWXFmXxLSCMqwFkNfUg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "duplication bug", "msg": "313233343030", "sig": "304402206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022056be8aaebb8627ef5e37057feb3448f726fb605312992466ee8d9ed7cd43c1b1", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f2a3ebc44fe94406cd6dc9bfc79a84600ae568cf533131e01505012649e39b8f0f886d549f83aa61ecd1eeb77ba7256e984f088c3b9183e84a16e96f93860e4f", "wx": "00f2a3ebc44fe94406cd6dc9bfc79a84600ae568cf533131e01505012649e39b8f", "wy": "0f886d549f83aa61ecd1eeb77ba7256e984f088c3b9183e84a16e96f93860e4f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004f2a3ebc44fe94406cd6dc9bfc79a84600ae568cf533131e01505012649e39b8f0f886d549f83aa61ecd1eeb77ba7256e984f088c3b9183e84a16e96f93860e4f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE8qPrxE/pRAbNbcm/x5qEYArlaM9T\nMTHgFQUBJknjm48PiG1Un4OqYezR7rd7pyVumE8IjDuRg+hKFulvk4YOTw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "30250201010220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e30751018c302c6916c21e2239baa41f0e69c5acfc371bb3e376ad364ea63802659cceeae0cabfee3ed33abacbc490e8716b5fbf11137647b524e4b855d7d659", "wx": "00e30751018c302c6916c21e2239baa41f0e69c5acfc371bb3e376ad364ea63802", "wy": "659cceeae0cabfee3ed33abacbc490e8716b5fbf11137647b524e4b855d7d659"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e30751018c302c6916c21e2239baa41f0e69c5acfc371bb3e376ad364ea63802659cceeae0cabfee3ed33abacbc490e8716b5fbf11137647b524e4b855d7d659", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE4wdRAYwwLGkWwh4iObqkHw5pxaz8\nNxuz43atNk6mOAJlnM7q4Mq/7j7TOrrLxJDocWtfvxETdke1JOS4VdfWWQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3045022101000000000000000000000000000000000000000000000000000000000000000002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ef28340fb027dabc05a2bd3be99c6cc2730ab0c3d8289e6a242f2b76cfccf9a2405cd0530183db6640119a20ad9c1c24ec87d4d9d5de42bffab54fd6cb6f9ed6", "wx": "00ef28340fb027dabc05a2bd3be99c6cc2730ab0c3d8289e6a242f2b76cfccf9a2", "wy": "405cd0530183db6640119a20ad9c1c24ec87d4d9d5de42bffab54fd6cb6f9ed6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ef28340fb027dabc05a2bd3be99c6cc2730ab0c3d8289e6a242f2b76cfccf9a2405cd0530183db6640119a20ad9c1c24ec87d4d9d5de42bffab54fd6cb6f9ed6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE7yg0D7An2rwFor076ZxswnMKsMPY\nKJ5qJC8rds/M+aJAXNBTAYPbZkARmiCtnBwk7IfU2dXeQr/6tU/Wy2+e1g==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c7002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0401b4e8eb0cf6f321006fc107246c1996f7034f56d82706cd8f14f05da0a7c514f158ad7ff3c6a08b2f057c6c28255f9513811f20ab18f7104df554d591913f78", "wx": "01b4e8eb0cf6f321006fc107246c1996f7034f56d82706cd8f14f05da0a7c514", "wy": "00f158ad7ff3c6a08b2f057c6c28255f9513811f20ab18f7104df554d591913f78"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000401b4e8eb0cf6f321006fc107246c1996f7034f56d82706cd8f14f05da0a7c514f158ad7ff3c6a08b2f057c6c28255f9513811f20ab18f7104df554d591913f78", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAbTo6wz28yEAb8EHJGwZlvcDT1bY\nJwbNjxTwXaCnxRTxWK1/88agiy8FfGwoJV+VE4EfIKsY9xBN9VTVkZE/eA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc476699780220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040b1cc580bb6f71e4bffb731a1e74f929c04a10ff94ac2312359d3f13213c3b4c870213c2ad3665a3d243dcb55780e21c8601c5f9803f27e31ff22f8ce77e739e", "wx": "0b1cc580bb6f71e4bffb731a1e74f929c04a10ff94ac2312359d3f13213c3b4c", "wy": "00870213c2ad3665a3d243dcb55780e21c8601c5f9803f27e31ff22f8ce77e739e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040b1cc580bb6f71e4bffb731a1e74f929c04a10ff94ac2312359d3f13213c3b4c870213c2ad3665a3d243dcb55780e21c8601c5f9803f27e31ff22f8ce77e739e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECxzFgLtvceS/+3MaHnT5KcBKEP+U\nrCMSNZ0/EyE8O0yHAhPCrTZlo9JD3LVXgOIchgHF+YA/J+Mf8i+M535zng==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042699736fdb603e90b1d9a04fcd90ed39756ed567214033ddb5ad579213089d2e96acfb0baeec9cfe2df150aa06b01ba58d03162b497c57a0d305adb4c5f7f375", "wx": "2699736fdb603e90b1d9a04fcd90ed39756ed567214033ddb5ad579213089d2e", "wy": "0096acfb0baeec9cfe2df150aa06b01ba58d03162b497c57a0d305adb4c5f7f375"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042699736fdb603e90b1d9a04fcd90ed39756ed567214033ddb5ad579213089d2e96acfb0baeec9cfe2df150aa06b01ba58d03162b497c57a0d305adb4c5f7f375", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJplzb9tgPpCx2aBPzZDtOXVu1Wch\nQDPdta1XkhMInS6WrPsLruyc/i3xUKoGsBuljQMWK0l8V6DTBa20xffzdQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042a3021069e8841f9d69ad4c2992b02dc7a2f5447afa55a4683c6451cdc4e728600ca4123520611085cb10ea80bdb851a0b09dd79703c420606ff658dba94c345", "wx": "2a3021069e8841f9d69ad4c2992b02dc7a2f5447afa55a4683c6451cdc4e7286", "wy": "00ca4123520611085cb10ea80bdb851a0b09dd79703c420606ff658dba94c345"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042a3021069e8841f9d69ad4c2992b02dc7a2f5447afa55a4683c6451cdc4e728600ca4123520611085cb10ea80bdb851a0b09dd79703c420606ff658dba94c345", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKjAhBp6IQfnWmtTCmSsC3HovVEev\npVpGg8ZFHNxOcoYAykEjUgYRCFyxDqgL24UaCwndeXA8QgYG/2WNupTDRQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997802203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04535212040d83b1802cd4a9c0b6ceb0a89de68b794ddf979c2ffb9a72e59eea007650166217eb39f4e03fecd48e9e7448032da261caa68d21df639ba68ee667a6", "wx": "535212040d83b1802cd4a9c0b6ceb0a89de68b794ddf979c2ffb9a72e59eea00", "wy": "7650166217eb39f4e03fecd48e9e7448032da261caa68d21df639ba68ee667a6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004535212040d83b1802cd4a9c0b6ceb0a89de68b794ddf979c2ffb9a72e59eea007650166217eb39f4e03fecd48e9e7448032da261caa68d21df639ba68ee667a6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEU1ISBA2DsYAs1KnAts6wqJ3mi3lN\n35ecL/uacuWe6gB2UBZiF+s59OA/7NSOnnRIAy2iYcqmjSHfY5umjuZnpg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04884a86a89981e216732916569f9e3f203806359ef9b9ced61ebb82d5f8030045079ceef71b8f9e1deb29aeddaf3bcc780dff88f92b705c68f572ec481139b84a", "wx": "00884a86a89981e216732916569f9e3f203806359ef9b9ced61ebb82d5f8030045", "wy": "079ceef71b8f9e1deb29aeddaf3bcc780dff88f92b705c68f572ec481139b84a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004884a86a89981e216732916569f9e3f203806359ef9b9ced61ebb82d5f8030045079ceef71b8f9e1deb29aeddaf3bcc780dff88f92b705c68f572ec481139b84a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiEqGqJmB4hZzKRZWn54/IDgGNZ75\nuc7WHruC1fgDAEUHnO73G4+eHesprt2vO8x4Df+I+StwXGj1cuxIETm4Sg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "extreme value for k", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cdb031f0e0bc432f0b959bc270456f6a500635732c76764010a5ea20f54a71d85cf6ce18411cdcb5056e4280e449c3ad6df90f9ae2dea4abc08280d99749643d", "wx": "00cdb031f0e0bc432f0b959bc270456f6a500635732c76764010a5ea20f54a71d8", "wy": "5cf6ce18411cdcb5056e4280e449c3ad6df90f9ae2dea4abc08280d99749643d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cdb031f0e0bc432f0b959bc270456f6a500635732c76764010a5ea20f54a71d85cf6ce18411cdcb5056e4280e449c3ad6df90f9ae2dea4abc08280d99749643d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzbAx8OC8Qy8LlZvCcEVvalAGNXMs\ndnZAEKXqIPVKcdhc9s4YQRzctQVuQoDkScOtbfkPmuLepKvAgoDZl0lkPQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2960220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046309ffd3c559fe1b0967213461d884b58d1cd549dbc297101d9db5a7e3fcf3d388f5fa86bd31043ca6077cd1da4b283f4179a23e9d680f66a2081ac502732714", "wx": "6309ffd3c559fe1b0967213461d884b58d1cd549dbc297101d9db5a7e3fcf3d3", "wy": "0088f5fa86bd31043ca6077cd1da4b283f4179a23e9d680f66a2081ac502732714"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046309ffd3c559fe1b0967213461d884b58d1cd549dbc297101d9db5a7e3fcf3d388f5fa86bd31043ca6077cd1da4b283f4179a23e9d680f66a2081ac502732714", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEYwn/08VZ/hsJZyE0YdiEtY0c1Unb\nwpcQHZ21p+P889OI9fqGvTEEPKYHfNHaSyg/QXmiPp1oD2aiCBrFAnMnFA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e564ff8412e92f5ee23fd299c92c57eb6ef0cbd17c28721b92625938d0eab1cff8941068815c9ad2d3b7f05845c41c4acebb92b3dc155aa7a51046948a4eed0", "wx": "6e564ff8412e92f5ee23fd299c92c57eb6ef0cbd17c28721b92625938d0eab1c", "wy": "00ff8941068815c9ad2d3b7f05845c41c4acebb92b3dc155aa7a51046948a4eed0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046e564ff8412e92f5ee23fd299c92c57eb6ef0cbd17c28721b92625938d0eab1cff8941068815c9ad2d3b7f05845c41c4acebb92b3dc155aa7a51046948a4eed0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEblZP+EEukvXuI/0pnJLFfrbvDL0X\nwochuSYlk40Oqxz/iUEGiBXJrS07fwWEXEHErOu5Kz3BVap6UQRpSKTu0A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0483fe782d906023da7ba700d097f8cc9618cb23f1cd89c213b98b8f9ae8fc023db15de38b856db24d4d6cc79b6d761fbd9ac94dad5f172883ba09278ba86d9955", "wx": "0083fe782d906023da7ba700d097f8cc9618cb23f1cd89c213b98b8f9ae8fc023d", "wy": "00b15de38b856db24d4d6cc79b6d761fbd9ac94dad5f172883ba09278ba86d9955"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000483fe782d906023da7ba700d097f8cc9618cb23f1cd89c213b98b8f9ae8fc023db15de38b856db24d4d6cc79b6d761fbd9ac94dad5f172883ba09278ba86d9955", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEg/54LZBgI9p7pwDQl/jMlhjLI/HN\nicITuYuPmuj8Aj2xXeOLhW2yTU1sx5ttdh+9mslNrV8XKIO6CSeLqG2ZVQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29602203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d1dddc947aaf9e6930cc46072f2cf2b68eb5e32dcf4ee84ea0647a201b299fbc6b382061309943abefa5938e8465e2f6afd051eab974d261797cd483934097a4", "wx": "00d1dddc947aaf9e6930cc46072f2cf2b68eb5e32dcf4ee84ea0647a201b299fbc", "wy": "6b382061309943abefa5938e8465e2f6afd051eab974d261797cd483934097a4"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d1dddc947aaf9e6930cc46072f2cf2b68eb5e32dcf4ee84ea0647a201b299fbc6b382061309943abefa5938e8465e2f6afd051eab974d261797cd483934097a4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0d3clHqvnmkwzEYHLyzyto614y3P\nTuhOoGR6IBspn7xrOCBhMJlDq++lk46EZeL2r9BR6rl00mF5fNSDk0CXpA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d17c1c5505bc710145ef74984864fe861e64302c16bb4a4bc69b47507b3f023541480e047b19bfe4bb885ec127cf254db1041ae1d5e8fd77e08294d398b62eb0", "wx": "00d17c1c5505bc710145ef74984864fe861e64302c16bb4a4bc69b47507b3f0235", "wy": "41480e047b19bfe4bb885ec127cf254db1041ae1d5e8fd77e08294d398b62eb0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d17c1c5505bc710145ef74984864fe861e64302c16bb4a4bc69b47507b3f023541480e047b19bfe4bb885ec127cf254db1041ae1d5e8fd77e08294d398b62eb0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0XwcVQW8cQFF73SYSGT+hh5kMCwW\nu0pLxptHUHs/AjVBSA4Eexm/5LuIXsEnzyVNsQQa4dXo/XfggpTTmLYusA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "extreme value for k", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpZP40Li/hp/m47n60p8D54WK84zV2sxXs7LtkBoN79R9Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "testing point duplication", "msg": "313233343030", "sig": "304402205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e0220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 365, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100a8ce483a42fb3462047c96ca00d1ab83ca565b2724cca9ac14411dcb8ee7e8030220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "00b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpawHL0cAeWAZXEYFLWD8GHp1DHMqZTOoTE0Sb+XyECuCg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "testing point duplication", "msg": "313233343030", "sig": "304402205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e0220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 367, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100a8ce483a42fb3462047c96ca00d1ab83ca565b2724cca9ac14411dcb8ee7e8030220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "wx": "04aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad5", "wy": "0087d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBKrsc2NXJvIT+4qeZNo7hjLkFJWp\nRNAEW1IuunJA+tWH2TFXmKqjpboBd1eHztBeqve04J/IHW0apUboNl1SXQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "pseudorandom signature", "msg": "", "sig": "3044022063f01899b4b0bfe9dc9929fd4526919b981acda781044ee3d2c337bf5fc748300220591381bdf1b1a9b01020b87314a128d06e4833342bf232779f61480739613927", "result": "valid", "flags": []}, {"tcId": 369, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3044022010228beaf773caeff22a94602e9eff1923dcc51b277f64b482ea63218c350b0d02202104c8343f8970a28c9eb221a63c857ef385e758eaccc5f7d2ae975553a1534b", "result": "valid", "flags": []}, {"tcId": 370, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "3045022100e6e1b8c20e9d00f0b6cf1b2c39cacd9c50ee3f990553250f074a4a3eed3afe43022052f3be1ae2d2f9b2bfea8e8c22d95af4574581a9f4b09a89f7b6a4ad1c5b2776", "result": "valid", "flags": []}, {"tcId": 371, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "3045022020f6203e48fc4c66ae8a74ec61d5124772daad058a74b871914d37dfe9d409c10221008b68de7a4786e29b3a726ea7fd8ef2a585b5c8dadf11281f2caa228eb3df3f96", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "wx": "4f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000", "wy": "00ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETzN8z9Z3JqgF5PFgCuKEnfOAfsoR\nc4Ajn72BaQAAAADtneoSTMjDlkFkEemIww9CfrUEr0OjFGzV336mBmbWhQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30440220207db605e42c96035d54352c5bc55cf27d5ded42cb6b42bdaee499ea64784db602207f83c09192aa04ce038e861699a0f27ca55bf32741dbc95bbf997dee57f538fc", "result": "valid", "flags": []}, {"tcId": 373, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3045022067c259a2580089ed52780755c75ea8a26b9057cc1995e4b044c8176cefe3cc7b022100d48f63d31333054bbd7fab676d207bbdc4dea1cf1b4f71aceb037b8dc7f79555", "result": "valid", "flags": []}, {"tcId": 374, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304502205dc675a2beae6deeb1ef682e922c5fe47156e069acd08073a0f8d9184d6baa6c022100e33cad4ce48f22ff6e50b47ba5dd44046a78ed7873cfd3a2c8b2d4b49aad2580", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "0084fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTWE+hdNeRxyvyzjiAqJYN0qfHoTOKgvhanlnNvegAAAAA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304502201a3b5c4b4a2fb0c2f9efb028a9efc78993f3151683cedf76214009ea418d3e5d022100e82e87332e7bd004cad9b13857939c01467fc1c3e4207efa45ef827985a82435", "result": "valid", "flags": []}, {"tcId": 376, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30460221008c44ef660ab8936fe01571168435c1918d005bd24ec76f72cea8f0faeb9f777a022100d793dcb3a6d47e2451e7d62e1c284ae25bbfaa0820f58adab79c201ba8d34a3e", "result": "valid", "flags": []}, {"tcId": 377, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304402204227a3dbff7ac5353cd32c8b3456397a7ee7c0e6809615fcee466b1dcde3eb49022022ea6ad811b27f944abe70b47f490d255760f8c3562e6f7e2c1da3dbe45eb540", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "7b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTV7BeixhuONQdMcd/V2nyLVg4XsyFfQelYaYyQhf////w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022036636162db85e8d300ee45c51b9da00a7c2cffd9a6fb200761a647ccbf5d7e8e0221009d18374cf1f87a9051e563838e75728d3f2ff7a86c10292851b6ce885c5b0c76", "result": "valid", "flags": []}, {"tcId": 379, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100a637fc972f3800705b8d5293096382d1c1ae7f670be45011b8bd29059f3049bd0221009c4abb6bbe06552d5d598b07728ccaed1738eac9fc985fd786fbc0a7347da828", "result": "valid", "flags": []}, {"tcId": 380, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3044022076bfc74f3b488b34835aee96ee96067f53da021cff4020a10996d6933a27c03202202fd1658fe4e09b2e711b10117f5c37d9c3ea8b6f55cdf1e5a5ddae2c966d7e4e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "wx": "2829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffff", "wy": "00a01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKCnDH6ouQA40TtlLyj/NBUWVbrz+\nitD236X/jv////+gGq+vAA5SWFhVr6dnat4oQRMJkFLfV+frO9N+vrkiLg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30450221008e0c35240e9e5b7bf2ab351afb13ac2655653baeabc247cab2c71cc40da44c000220079cbf8c9ba9b53608b219d6989875d960bdefcde224cf7ac6f8e791adaa4364", "result": "valid", "flags": []}, {"tcId": 382, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022100f6ceb6c8f76c337f51f4ec3859eb16caec969fc02a61dec1a70fa4223bdfb254022010c0334298a98a6e5c12e9c0cad587dcab43199b43cdf3785bd9c36b30925ccf", "result": "valid", "flags": []}, {"tcId": 383, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30450220083000a8e6121939f4b83612727b2091d8abbbdf9c92bf9bdcace8366150ce6f022100ba693f4e0b96dcbeeaa78c0d744365761151740323c346a54d74b332568d939f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "wx": "00fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f5", "wy": "5a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE////+UgIHmoEWN2PnnOPJmX/kFmt\naqwHCDGMTKmnpPVairy6LdqEdDEe5UFJuXPK4MD7iVV60L945lKaFmO9cw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30460221008a1bd6ef283948184b5a32d31860e97cc0c450931f024c30bb3b261f2552cdc7022100b7e50c0513a8ec730d112109e92761a21151e4bec68268e5c79ef804b757deaa", "result": "valid", "flags": []}, {"tcId": 385, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502200c18337b701b6000d3ce3574664c5dc44ead6a1f2ee0c27a728ea0b0f37990b9022100c31db9b199b3e1709c44a44118d1d7cb75324ee82ada2318744eb89651e6f6c0", "result": "valid", "flags": []}, {"tcId": 386, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502202133d6fc78b394d4c34e173120b1e48c7fed7b89a03e55cab90b1367155b438a0221009293e67ff4b981e50c48b0304f7b1e6b530416ee35188302b1dd2f21e5cb479a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "wx": "03fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e", "wy": "1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAAAAA/oV+WOUnV8DpvXH+G+eABXu\nsjrrv/EXOTe6dI4QmYcgcOjofFVfoTZZzKXX+tz8sAI+qIlUjKSK8rp+cQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502203604a98e926b2f9d7585e341a5ecc73a4e811c5c8da82b65790ff8a117a75bda022100a0ff07774c9a0d4bf83db294b970f2696cc29a73637aa454d4d3b45eb964bb88", "result": "valid", "flags": []}, {"tcId": 388, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502206bf7e8f8bc3a5a2e2249c92725cf0dffa9b72ead3cc56d05107a4d587563beb4022100a05332b5b424d97bfc080fe0353470610931cd538d2e4bcf78c6fc59b481d271", "result": "valid", "flags": []}, {"tcId": 389, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502210097e099c73088fc37052180d0483987e50c155c993cba2e6c93dd9bea5798e2c302204a9ec5f05739efb4ea93790ea22c3fc423d0aeb109cd13fb1b44d87ea52ca71f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "1352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BUAAAAAE1K7Sg+i6kzOuatj3WhK3loRJ7zzAKaYpxk7wg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502203988bce3195aaf7c9b008a9f1663a5e13a8bee7ddba33a1bc5d55aa49fd3903d022100c39f614828e2f71a4c66d86d1c3ec7e283f768033cff5ed09e93e3218d9df1c9", "result": "valid", "flags": []}, {"tcId": 391, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3046022100c3eb2e8f78a31f221d6003b949f8df6c7ec1e0c53803231e12438cb2b1b1d9ba022100b97cc1fcce1d8ddbb5e1bfa6d5300d7cac155494603c66f7eee8b8e9c9643431", "result": "valid", "flags": []}, {"tcId": 392, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304602210089e0c23ccf61e68dc0cb9777f18b18c84b2b02b4360c79eaa40d46ebe7f3d9b1022100a5d0164e398764e7d12d696750fcb092211c22dfdd3941e59cd73bc48eb91496", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "00fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BX////+7K1EtvBdFbMxRlScIpe1IqXu2EMM/1lnWObEPQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022100bbc1bb4ffceb61a0dbe5a12d9638dc9f004e797cf72cdba8d879fdbcd84dec14022065a7c17d9a6892cf5455a1904fdd9b57ce2b41549b9b2ca5d7d182c305e9a202", "result": "valid", "flags": []}, {"tcId": 394, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502202f093a33c69eeaf847e332b12bd0758be41dcf75d8131878f16e6f121cb3f4f102210093c304df074aef8cc2c8cddeaffda67eb2428ea7d3a113d51363e178d8068f71", "result": "valid", "flags": []}, {"tcId": 395, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3046022100b686774db39201a9462b96842adbeea16ae6003789bb18214dab9e5a758bf6ef022100ffc6b396293b94c96fcb325fae127608ebfd118a46f715b49b918caafb602a34", "result": "valid", "flags": []}]}]}