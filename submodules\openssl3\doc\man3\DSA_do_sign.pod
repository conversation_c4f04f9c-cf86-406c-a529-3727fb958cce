=pod

=head1 NAME

DSA_do_sign, DSA_do_verify - raw DSA signature operations

=head1 SYNOPSIS

 #include <openssl/dsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 DSA_SIG *DSA_do_sign(const unsigned char *dgst, int dlen, DSA *dsa);

 int DSA_do_verify(const unsigned char *dgst, int dgst_len,
                   DSA_SIG *sig, DSA *dsa);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_sign_init(3)>, L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify_init(3)> and L<EVP_PKEY_verify(3)>.

DSA_do_sign() computes a digital signature on the B<len> byte message
digest B<dgst> using the private key B<dsa> and returns it in a
newly allocated B<DSA_SIG> structure.

L<DSA_sign_setup(3)> may be used to precompute part
of the signing operation in case signature generation is
time-critical.

DSA_do_verify() verifies that the signature B<sig> matches a given
message digest B<dgst> of size B<len>.  B<dsa> is the signer's public
key.

=head1 RETURN VALUES

DSA_do_sign() returns the signature, NULL on error.  DSA_do_verify()
returns 1 for a valid signature, 0 for an incorrect signature and -1
on error. The error codes can be obtained by
L<ERR_get_error(3)>.

=head1 SEE ALSO

L<DSA_new(3)>, L<ERR_get_error(3)>, L<RAND_bytes(3)>,
L<DSA_SIG_new(3)>,
L<DSA_sign(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
