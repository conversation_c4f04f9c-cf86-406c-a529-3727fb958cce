=pod

=head1 NAME

ASN1_STRING_TABLE, ASN1_STRING_TABLE_add, ASN1_STRING_TABLE_get,
ASN1_STRING_TABLE_cleanup - ASN1_STRING_TABLE manipulation functions

=head1 SYNOPSIS

 #include <openssl/asn1.h>

 typedef struct asn1_string_table_st ASN1_STRING_TABLE;

 int ASN1_STRING_TABLE_add(int nid, long minsize, long maxsize,
                           unsigned long mask, unsigned long flags);
 ASN1_STRING_TABLE * ASN1_STRING_TABLE_get(int nid);
 void ASN1_STRING_TABLE_cleanup(void);

=head1 DESCRIPTION

=head2 Types

B<ASN1_STRING_TABLE> is a table which holds string information
(basically minimum size, maximum size, type and etc) for a NID object.

=head2 Functions

ASN1_STRING_TABLE_add() adds a new B<ASN1_STRING_TABLE> item into the
local ASN1 string table based on the B<nid> along with other parameters.

If the item is already in the table, fields of B<ASN1_STRING_TABLE> are
updated (depending on the values of those parameters, e.g., B<minsize>
and B<maxsize> >= 0, B<mask> and B<flags> != 0). If the B<nid> is standard,
a copy of the standard B<ASN1_STRING_TABLE> is created and updated with
other parameters.

ASN1_STRING_TABLE_get() searches for an B<ASN1_STRING_TABLE> item based
on B<nid>. It will search the local table first, then the standard one.

ASN1_STRING_TABLE_cleanup() frees all B<ASN1_STRING_TABLE> items added
by ASN1_STRING_TABLE_add().

=head1 RETURN VALUES

ASN1_STRING_TABLE_add() returns 1 on success, 0 if an error occurred.

ASN1_STRING_TABLE_get() returns a valid B<ASN1_STRING_TABLE> structure
or B<NULL> if nothing is found.

ASN1_STRING_TABLE_cleanup() does not return a value.

=head1 SEE ALSO

L<ERR_get_error(3)>

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
