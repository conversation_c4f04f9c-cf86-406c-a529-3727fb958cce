=pod

=head1 NAME

EVP_PKEY2PKCS8, EVP_PKCS82PKEY_ex, EVP_PKCS82PKEY
- Convert a private key to/from PKCS8

=head1 SYNOPSIS

 #include <openssl/x509.h>

 PKCS8_PRIV_KEY_INFO *EVP_PKEY2PKCS8(const EVP_PKEY *pkey);
 EVP_PKEY *EVP_PKCS82PKEY(const PKCS8_PRIV_KEY_INFO *p8);
 EVP_PKEY *EVP_PKCS82PKEY_ex(const PKCS8_PRIV_KEY_INFO *p8, OSSL_LIB_CTX *libctx,
                             const char *propq);

=head1 DESCRIPTION

EVP_PKEY2PKCS8() converts a private key I<pkey> into a returned PKCS8 object.

EVP_PKCS82PKEY_ex() converts a PKCS8 object I<p8> into a returned private key.
It uses I<libctx> and I<propq> when fetching algorithms.

EVP_PKCS82PKEY() is similar to EVP_PKCS82PKEY_ex() but uses default values of
NULL for the I<libctx> and I<propq>.

=head1 RETURN VALUES

EVP_PKEY2PKCS8() returns a PKCS8 object on success.
EVP_PKCS82PKEY() and EVP_PKCS82PKEY_ex() return a private key on success.

All functions return NULL if the operation fails.

=head1 SEE ALSO

L<PKCS8_pkey_add1_attr(3)>,

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
