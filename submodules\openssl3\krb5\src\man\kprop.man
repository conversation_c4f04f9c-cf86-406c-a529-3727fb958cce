.\" Man page generated from reStructuredText.
.
.TH "KPROP" "8" " " "1.20" "MIT Kerberos"
.SH NAME
kprop \- propagate a Kerberos V5 principal database to a replica server
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkprop\fP
[\fB\-r\fP \fIrealm\fP]
[\fB\-f\fP \fIfile\fP]
[\fB\-d\fP]
[\fB\-P\fP \fIport\fP]
[\fB\-s\fP \fIkeytab\fP]
\fIreplica_host\fP
.SH DESCRIPTION
.sp
kprop is used to securely propagate a Kerberos V5 database dump file
from the primary Kerberos server to a replica Kerberos server, which is
specified by \fIreplica_host\fP\&.  The dump file must be created by
kdb5_util(8)\&.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-r\fP \fIrealm\fP
Specifies the realm of the primary server.
.TP
\fB\-f\fP \fIfile\fP
Specifies the filename where the dumped principal database file is
to be found; by default the dumped database file is normally
\fB@LOCALSTATEDIR@\fP\fB/krb5kdc\fP\fB/replica_datatrans\fP\&.
.TP
\fB\-P\fP \fIport\fP
Specifies the port to use to contact the kpropd(8) server
on the remote host.
.TP
\fB\-d\fP
Prints debugging information.
.TP
\fB\-s\fP \fIkeytab\fP
Specifies the location of the keytab file.
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kpropd(8), kdb5_util(8), krb5kdc(8),
kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
