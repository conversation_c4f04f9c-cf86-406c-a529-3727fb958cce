=pod

=head1 NAME

PKCS12_init, PKCS12_init_ex - Create a new empty PKCS#12 structure

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 PKCS12 *PKCS12_init(int mode);
 PKCS12 *PKCS12_init_ex(int mode, OSSL_LIB_CTX *ctx, const char *propq);

=head1 DESCRIPTION

PKCS12_init() creates an empty PKCS#12 structure. Any PKCS#7 authSafes added
to this structure are enclosed first within a single PKCS#7 contentInfo
of type I<mode>. Currently the only supported type is B<NID_pkcs7_data>.

PKCS12_init_ex() creates an empty PKCS#12 structure and assigns the supplied
I<ctx> and I<propq> to be used to select algorithm implementations for
operations performed on the B<PKCS12> object.

=head1 RETURN VALUES

PKCS12_init() and PKCS12_init_ex() return a valid B<PKCS12> structure or NULL
if an error occurred.

=head1 SEE ALSO

L<d2i_PKCS12(3)>,
L<PKCS12_create(3)>,
L<passphrase-encoding(7)>

=head1 HISTORY

PKCS12_init_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
