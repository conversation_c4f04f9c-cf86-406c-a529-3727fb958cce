=pod

=head1 NAME

SMIME_read_PKCS7_ex, SMIME_read_PKCS7 - parse S/MIME message

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 PKCS7 *SMIME_read_PKCS7_ex(BIO *bio, BIO **bcont, PKCS7 **p7);
 PKCS7 *SMIME_read_PKCS7(BIO *in, BIO **bcont);

=head1 DESCRIPTION

SMIME_read_PKCS7() parses a message in S/MIME format.

B<in> is a BIO to read the message from.

If cleartext signing is used then the content is saved in
a memory bio which is written to B<*bcont>, otherwise
B<*bcont> is set to B<NULL>.

The parsed PKCS#7 structure is returned or B<NULL> if an
error occurred.

SMIME_read_PKCS7_ex() is similar to SMIME_read_PKCS7() but can optionally supply
a previously created I<p7> PKCS#7 object. If I<p7> is NULL then it is identical
to SMIME_read_PKCS7().
To create a I<p7> object use L<PKCS7_new_ex(3)>.

=head1 NOTES

If B<*bcont> is not B<NULL> then the message is clear text
signed. B<*bcont> can then be passed to PKCS7_verify() with
the B<PKCS7_DETACHED> flag set.

Otherwise the type of the returned structure can be determined
using PKCS7_type_is_enveloped(), etc.

To support future functionality if B<bcont> is not B<NULL>
B<*bcont> should be initialized to B<NULL>. For example:

 BIO *cont = NULL;
 PKCS7 *p7;

 p7 = SMIME_read_PKCS7(in, &cont);

=head1 BUGS

The MIME parser used by SMIME_read_PKCS7() is somewhat primitive.
While it will handle most S/MIME messages more complex compound
formats may not work.

The parser assumes that the PKCS7 structure is always base64
encoded and will not handle the case where it is in binary format
or uses quoted printable format.

The use of a memory BIO to hold the signed content limits the size
of message which can be processed due to memory restraints: a
streaming single pass option should be available.

=head1 RETURN VALUES

SMIME_read_PKCS7_ex() and SMIME_read_PKCS7() return a valid B<PKCS7> structure
or B<NULL> if an error occurred. The error can be obtained from ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<SMIME_read_PKCS7(3)>, L<PKCS7_sign(3)>,
L<PKCS7_verify(3)>, L<PKCS7_encrypt(3)>
L<PKCS7_decrypt(3)>

=head1 HISTORY

The function SMIME_read_PKCS7_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
