{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 450, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "wx": "2da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa", "wy": "4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMH<PERSON>wEAYHKoZIzj0CAQYFK4EEACIDYgAELaV92hCJJ2pUP5/9rAv/DZdsrXHrcoDn\n2b/Z/uS9svIPR/+IgnQ4l3LZjMV1ITiqS20FTWnc8+JexJ34cHFeNIg7GDYZfXb4\nrZYuePZXG7x0B7DWCR+eTYjwFCdEBhdP\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760230302d08b563b09fbd4bb648f56a35794a12d24f48cefb874eac860c115c043020c92da2a8a55ad7b52aa165bbb90ff909", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "************", "sig": "30650230fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760230cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "************", "sig": "308166023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "************", "sig": "30820066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "************", "sig": "3067023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "************", "sig": "30850100000066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "************", "sig": "3089010000000000000066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "************", "sig": "30847fffffff023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "************", "sig": "3084ffffffff023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "************", "sig": "3085ffffffffff023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "************", "sig": "3088ffffffffffffffff023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "************", "sig": "30ff023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "************", "sig": "3066028000fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76028000cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "************", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "************", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "************", "sig": "30680000023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "************", "sig": "306b4981773066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "************", "sig": "306a25003066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "************", "sig": "30683066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "************", "sig": "306b2236498177023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "************", "sig": "306a22352500023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "************", "sig": "306e2233023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760004deadbeef023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "************", "sig": "306b023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762236498177023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7622352500023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "************", "sig": "306e023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762233023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "************", "sig": "306eaa00bb00cd003066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "************", "sig": "306caa02aabb3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "************", "sig": "306e2239aa00bb00cd00023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "************", "sig": "306c2237aa02aabb023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "************", "sig": "306e023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762239aa00bb00cd00023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "************", "sig": "306c023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762237aa02aabb023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "************", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "************", "sig": "30803066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "************", "sig": "306a2280023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760000023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762280023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "************", "sig": "30803166023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "************", "sig": "306a2280033100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760000023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab762280033100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "************", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "************", "sig": "2e66023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "************", "sig": "2f66023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "************", "sig": "3166023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "************", "sig": "3266023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "************", "sig": "ff66023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "************", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "************", "sig": "306a30010230653100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b530", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "************", "sig": "30653100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a00", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a05000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "************", "sig": "3080023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "************", "sig": "30683000023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a3000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "************", "sig": "3069023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306abf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "************", "sig": "30683066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "************", "sig": "3033023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "************", "sig": "308199023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "************", "sig": "306702813100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "************", "sig": "3067023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7602813100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "************", "sig": "30680282003100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760282003100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "************", "sig": "3066023200fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "************", "sig": "3066023000fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023200cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023000cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "306b0285010000003100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "306b023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760285010000003100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "306f028901000000000000003100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "306f023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76028901000000000000003100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "306a02847fffffff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7602847fffffff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "306a0284ffffffff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760284ffffffff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "306b0285ffffffffff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "306b023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760285ffffffffff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "306e0288ffffffffffffffff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "306e023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760288ffffffffffffffff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "************", "sig": "306602ff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7602ff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "************", "sig": "3033023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "************", "sig": "303402023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "************", "sig": "3034023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7602", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "************", "sig": "3068023300fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760000023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023300cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "************", "sig": "30680233000000fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760233000000cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760000023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "************", "sig": "3068023300fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760500023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "************", "sig": "3068023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023300cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a0500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "************", "sig": "30350281023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "************", "sig": "3035023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "************", "sig": "30350500023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "************", "sig": "3035023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "************", "sig": "3066003100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "************", "sig": "3066013100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "************", "sig": "3066033100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "************", "sig": "3066043100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "************", "sig": "3066ff3100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76003100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76013100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76033100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76043100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76ff3100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "************", "sig": "30350200023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "************", "sig": "3035023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "************", "sig": "306a22350201000230fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "************", "sig": "306a023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab7622350201000230cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "************", "sig": "3066023102fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023102cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7abf6023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b530ea", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "************", "sig": "3065023000fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023000cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b530", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "************", "sig": "30670232ff00fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "************", "sig": "3067023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760232ff00cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "************", "sig": "3036090180023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "************", "sig": "3036023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "************", "sig": "3036020100023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "************", "sig": "3036023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023101fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e0318ffb708876bbed3734ce2578a5d7d5a7c049163f7cd4e9023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "30650230fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e0a2c9606ca008602e8700b2c0e74488dfcde81640a5f28203023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "30660231ff042dc5e4fb2d96936337926c3717cdabd87fd0188ef09a1f95d352116bc071f220e53f8cd00acfa5452bd0548d48548a023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "30650230042dc5e4fb2d96936337926c3717cdabd87fd0188ef09a1f5d369f935ff79fd178ff4d3f18bb77203217e9bf5a0d7dfd023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "30660231fe042dc5e4fb2d96936337926c3717cdabd87fd0188ef09a1fce70048f77894412c8cb31da875a282a583fb6e9c0832b17023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023101fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "30650230042dc5e4fb2d96936337926c3717cdabd87fd0188ef09a1f95d352116bc071f220e53f8cd00acfa5452bd0548d48548a023100cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023101cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b0e2408ef28c6a2b9de70678bbec067740af36cd19e07a59dd", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760230cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b15379f3eea3fbcfdf36d25d575aa5284ad55e9a4446f006f7", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760231ff302d08b563b09fbd4bb648f56a35794a12d24f48cefb874ee522be8f67cd0241711394f65caa303a3db54c50ec4acf96", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760231fe302d08b563b09fbd4bb648f56a35794a12d24f48cefb874f1dbf710d7395d46218f9874413f988bf50c932e61f85a623", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3066023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab76023101cfd2f74a9c4f6042b449b70a95ca86b5ed2db0b7310478b11add41709832fdbe8eec6b09a355cfc5c24ab3af13b5306a", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "3065023100fbd23a1b04d2696c9cc86d93c8e8325427802fe7710f65e06a2cadee943f8e0ddf1ac0732ff5305abad42fab72b7ab760230302d08b563b09fbd4bb648f56a35794a12d24f48cefb874ee522be8f67cd0241711394f65caa303a3db54c50ec4acf96", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529730201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529720201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529740201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000001000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "323030323839", "sig": "3066023100ac042e13ab83394692019170707bc21dd3d7b8d233d11b651757085bdd5767eabbb85322984f14437335de0cdf565684023100e0f133f6ff3dd2496a15abc92b34315a17f49679734720c0270f5dad3dcd2833f913b48a6cdf2ec6b2ffc9d3d72d545b", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32373239373236343137", "sig": "30650230202858607c9a8777f7001f0fb25b12f39d5fb1b86b767adb1a32fd8ca18dec71d0cf69a3839f3097d9132247b558e1b6023100d7be6ca34d3a846195b67e5bb517fb169ed4da4ee124b854637c7f86bcacbad64caa4e9e50fb932c1679b463ffa87b23", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "343331343737363137", "sig": "3066023100bbd2e99974a733000592ebad069c39adcb4ee32718f262dc167fa0b4080e788c95cf177bcff61b72dcdf7406906af99502310089552e369143d56a006b90b8b3fd68cdb49a8f26ec5ca4df03abc149273de0a4693af37362f3c689a6d8b6935663273e", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "36363033343338303333", "sig": "3065023100b35e96f93302bc08e339bfbb2fbe04872f1eca413084629041ffc0dd94eee677bc32cb09022ed6214153f2ed705edb2202305aee1acff79c6bdbbb4d3c656c3010eba72fa671df68637a5fbbc2dd2268bbb4fef4a85c90b49013e0c1a2aecd342b6c", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32383239363231343535", "sig": "30650230665d94550523110bbfd13d7f676262591a53baf25eb36351495c6f3c797d1d0b2a69f60f0d4a1277414a900c4eadc0b4023100cd30032e93a773a2603955c040415bd3ba1ef4e43ab476dd4f6eee5825ed1bc5b0531099aea51909a51a8c6641e20c5b", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "34333131383231373336", "sig": "3065023100dda8c851bc3920be100a4a1ff64f5415c969c2db653f3a8e62e950863aed9aa18a6cf9c98ed15725fba10d439ed94f3c0230407f8f0121e96febb30502b28a64a8b86099d602f14ec25b68567481b7f42709c7cef8b212d23cd5c6fe7e5f00ecb2b9", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3131373730373734313735", "sig": "306502304c33b7661835f75c0b71e9a6084f58152e0067049e67732ea1bdf7788b1d5d7efe6768551a56f1633c6456caed18a5e8023100f7b4bfcc041be40e5c24907c5174dd210e48725add92962fb223c1a0eaad11336c66612638a84b5865b50501f0667e9f", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31353938353135353635", "sig": "306502302b1b1e7c87e0401be935919c509e020b391b4d5242df3584f465c316c88543a85f58dd7eca620e2c16d86068013892f5023100a217fa8785fcbce6e5b1ad7d9bde09aa251a6e3e915df5f44ecb0d43baf33ec197537b2ac2d413663ff19c4d98943b07", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32383831313031363138", "sig": "3066023100f3e8882f07c78c0b60915c078af978a79e9e9f64201ed51408e3776fd33830a06e6066dab7218df8cbafdbd7efeed866023100ea37a3525bd2b48991e3c82d8e9902693be65eb0953cb97a7f45381d4ba7598f9f076c0e4222c92a7d9dea7ba0668453", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32303034373833333332", "sig": "30650231008a37c7ba932e249987adc4b860a1447727b75d2396ae577e60a4333e116a88755b9b0bc9ab6f998042f455c5ca3efe4502302bab3f9a0fc3f0d84ad1fe4652160cb06a4776013741c5df39547534f53d255bae0fcec9a80b65b75b869616d798615f", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "39353030323437373837", "sig": "306502303dade5c9f3c3df658b91e73b5837161520c547fa930c0682e445c90a99ab81524200c3578703c815fc794c178ae113e7023100866407b2dbc01d523df632447e01488ebf35a54944f4fb054cb89d31ed15bec3e8bceffe4a422ba27cb42675f37df5ac", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323039353030303630", "sig": "306502300dbabe75fa6538e0861bbb502f930747fb57cb4dcd344db394714e0ccbba35ab832c42ad4895a0bae2148a4cd222f338023100f46c223b0462a5dec8e180ed083736a86d05a29b5f1d600aef1b74bd37971d56d91661506c500260b5b51140336e66a9", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "38313933373839323237", "sig": "3065023100810c6c1b6eed6396b6d23932a8dc5a73b7640c5f5af2af291fd867cd484a940ce4bf65f453c991e08d5e78c0eeabb08d023047920914c935727f8fbfe9ea3919998ba8455349199b34fc7dfd9a982bd686b08302171defc5bce02893c57a0d1b2737", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "33363530363033323938", "sig": "3066023100b78c8f502aa5398aaf25ebafda69f36cf0420e40ef542d82e90e4a9a38ab4c7f35fb4ae418badebba8349e41aedf53c3023100e0df0dc985a24606c9be537ebaaf7dbda3ff7b0bcde19f0962cec1f43c42f2028187777693d08c07b1a9935cd1d1f512", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "3136333833393533353233", "sig": "3066023100eebe681fb8e6ed44747d37513de1233130c99ee379610b6ed2dd389b223755e29e18184abf5a79195e202a61fdd938ac023100c336c32e8342d3b4334369f49cc6d48e6d5913988cb3ac4d7debd07c4333a4612e2387f111d6c029aa65dc3cbfc5094a", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32303931373638323035", "sig": "3066023100edfaec6489e58021c699662c52fa0593bc278788380f3a5b13c42ff8f68b3e7afe266207589f331cf4a1e621b4fecef10231008fb91c6decd2d49c8d901a8ec43f7da91f87750abf72696d0c87c4258ae63ea3e5bdbc34e0031f95b36079d3791da027", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "39333634373032383235", "sig": "3066023100bd746ffa5059d9e887fab3be1c53c21d47e642cbd32bf3da4d1ef1e816e75cb02b14c58a6ecb50d4fce2bb86b8aa15fb023100bfacd071541d1cec2cf97a82162aba7aac62120013a83514d7dcc35d11f5646bb1c00c3dc62b8ee74ac5688aa7d60523", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "393236383638373931", "sig": "3066023100c42e5eeecfeb6f341a2c43e863bbc9bb9318e67c1464fbf0df53aee69156520a59c96fd4b2e833a531fd15e5b8a227b3023100d9dad675a1edb4ee2f711177db77219d6e006572476a7d359baf2a846a3655a97fe2e4b109cf6b5dd4c7bc657df9a9a1", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35313738313334383231", "sig": "3065023100f704c037bfc43538cea15d6fc20d034be90b0826ac65f64dfdff429c0ef0b84e96aaae207b2f9bb9eeab1d382d7634b402307bed532df07a9f04fb7790836dd06e358388912d5cd7b5ebfdc7abd2e1510443297763d6a9460e3bb204f282a9e219d5", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "34373335303130373531", "sig": "3065023100b36a254a2bec5f2dc62302457c20e8ee0747ec653b510bf9af9d9b9dc7ac05e4fd6578dfc6608019ad8034afd14da04b023073cdec3752fa0de71a3fa56e4dd89af0ecde8de54b39b638a1a3d85928a19e8c208feddf5d35a251d9b7350a4b183745", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "3134333533393131363839", "sig": "3065023018dc650e8c186c04a7560467fe8d87eb61dff7ca400924dd3f36cab1c81f25281b69b18561bbdaab5715b997aa21c103023100a65ab79877d06f7218f00ea72d871a77933ec8487a35f0ec21450567e6bd016d8878f36bc67655b4b09e8c765643e203", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31333834353439323034", "sig": "3066023100e80439c807bdba93211dd1a63159b9a4dde1b627eec62f09939c0751cdeee9b58fa3aef507b96cd3ec819d7ac460c6d7023100c0735ca53571c1601308dccf510a825eb6b959aed11cbfebce360c8f9f54e01f61b7195b07fdfdbeb5a4bf2b9af2d7b0", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32373632313932373839", "sig": "3066023100c673624116d7aa2943b60db72b9c59b734f937ea077788a581141539b0ddd3a82e4552cd9aed073cf234230dcc1a8e51023100a4f93ed4b0afe9964565de3b73e77258a7ae5734564b74b8ccb1c7e612c2b4fc869237a6dfd317bbaf2bd75b08c4e678", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31383331363534333331", "sig": "306502302d9323897383e720a08d05f2ed51f4f4e267174893253fc6e475188f00ee881ef71fdfb19337f6800e492290b7b8a3e2023100bdf5d45c4fb989db4353cca999065f9bfe51423bf61083b82b9c23bd2f25787f22ee5bcec9704fbb90a096e85dfd4cb5", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343336383939303330", "sig": "306402307f31cbd14dbb0d079b43cca200e758acb429ab33eb8f99ad2c2e1f0be51d0e20e888bed0563f5d80a7ba603bc184bd520230572b79750ad421c43ca2eec7c73ba7ece9013c09dd02bf56db860ebb04b060a971d9ba043975abf340ba801ae0bfc722", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "323034303431323232", "sig": "30660231009c4559b076beb6a49a558d7d9bd68ad57f26c7d8f5b4a70a4b44182799810518e3ffa9e88e06e4e80792695cdf598dcb023100977043d20870aca886b94b59d991f18167f9b846e64ce42c9c5f4e761a534fa178f99d8b9ff7d1d3119af4c206e3a9fb", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "33333337313139393735", "sig": "30650230216fa858f98c2c36e1d63612d8e303ad1e79c2be9df56e4b914b39071e3cf6730519aec82e7a4b953387c5968c7ee2a60231009dbb30990b547dac6716a9663dec4adab95c8b9627eb82324960880c06652f95f2315f77963769be04baac725726ef4d", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "36363935363230363738", "sig": "3066023100f4ade3885d93019dece7c2d0c60ed281e6df189389f6b32608d03e075f2c21038f6cdc1759b3121f4bcec62be0ced247023100c523d6c83fdcc6fee5f250e801435c4bd6ff32702aa379f4e07634d29c5fc0c598a30332bd7ee235cf5e7cfaa121d63c", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303933303137373437", "sig": "306402303657272051a5892983150131cb07b1a10de3a82a366e2c18a630100e75281f77c074117a2bd0f86df722bdb7793935200230656bf9b002f1de2a6a4e967d5bf2d1167594fb1e7f02fa8990d781568ee7986c2b161c3dfbd4e9c54b99af62d389e574", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "313233343137393136", "sig": "306402300e6b00b7868ab7f1a7d738cf231f11a85e7df4c8b8e3fd0c64e3a261f986a20255bf60fa982cfd86fcbd6ab8769941d8023036b8107a327026b93ba8fc9d12c087ce06de3e7f02a513f520ba83481789a33a6667e4d96f54784b3cbe5f6133f76412", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31373634333530363837", "sig": "3065023100e999a6f89b37eb88add82ee5500fbfea4d085ea89aa87dc51a5e0231fb98e230e27cc7d8fb3f4cb50d731b2d9f95e4a70230684791d376261275ce5179bf38fd38adbfa0445855d25401566380f3ac47b97a5aa0134a06daeeef1b5cd1203b7fa020", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "3131343137323431343431", "sig": "30650230121df556b6e29fef7ccb875dc2bae65f8bd4b33621426d7fc7acc2003a016a95ae5fe3f22924019ef9e5f216bf21d60602310097ea4126cb43ac8f1938ee82323c3e3177c09f8b7a7bb862fafc9cee1339e19329dc8a261cda1b050d782da06e79d68b", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32323638323436343933", "sig": "3065023100ec2e792cfbc85f6e2519cfcd49e9b10e9f08a5cbe88a68c7affdd840ac21f053631912f62b3d45acb3cdf7db9f78f87002300a0da2d5e10886f7d4d7910ab6c4e589fa7e89ef94529320d3c6d168b8056af2b07b0afdfe2e3317618fe0aa1fbb213d", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "32373234373936373737", "sig": "306402300df1b7596ade29c09eb4a87290ec102f2bcba8731281a5b3818809510b2d3e23e0c2b194219be1144ae354512d4e4c140230710158c3e7816269bb08af3b8b1bbbbe595fd5ab77a06d14b47a9003f9bc5713e437135873e43c7014ef07ae8be2894c", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "393733333935313139", "sig": "3066023100e364868bc629941f4aab4e8cb3bb876f396269d4f99e16eebcc5a8fe8ba787c2f226daa2990e1f6d5260bea7104fed0602310099ad584fe5e7ad210592d5eb158a0cf28b19d2d9c74a688111367d254b71e39121f6bc1aa0d7f4861fe9f4d1cc2d9c31", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "31353037303032373036", "sig": "30660231008e686e88746382c9371c8e779c271fcb05d10afa4b801bb45f9b48d35f2ef94a0859788104fc78b511c5324e209f658f023100beeec64ab193b195a2245391d51afd58a1139ec6edfee5a6c10a3ec9f71eee4caaf22d3df8931cca156fea6b3b79f67d", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33373433353638373832", "sig": "3065023011920756203720443ab0b6fd4f41788f7efe593ceb02ab61aa25caa1db7ba426e6c0719b44c44c581f14ae38bca2dc22023100d6c6acaefab23ffe971cadad8386398bdd7bb986f225ef5c2f9c08cc7b74fbd679686ac02ca55c2a009f829fafe3c28e", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "393437363731323438", "sig": "3066023100816f12f483675bdf87b82df29b287d2ce0bd281148c18ce3bd06b860447466164a8148d4db665d05a6fb733f7f7ba30f023100da8e42ca2999d24b4ad0586fe9b85e0f78ca614a0acbc3c37a2ea75289f811aef84c9d6299737ea5e4cb1e743a2600bc", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "373235343434333234", "sig": "306402301fd0fe36188f093ba2ecb8ddce12587583ea3638a07713a6f6573a9ffb4e50a3f6363fca961b8a0ac6a7f1414333f9e102302ac94be5c224b12fb72375b22d52e3180b8a236b4980a6bae5aa4cb8119c54ab4cd89fc0682f564102517310e4eb2e1e", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "35353334303231323139", "sig": "306402301e549378621758d1db17fd0cd84ec12031068c837afd13cc46cd8d0304230b45a544c7708b032505a048f627087e47030230261f1068011dfe1a99324e7d3fada3af75edddf3a84ff2d7089d587fdbf01a0a4cb49a6a1e8192cc16bb4cd37b909bb2", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3132333031383133373933", "sig": "306502310090dcc3df567225a8ff08cd8f26921edbcab65e4996a6f324475ad04ce614c55adf31f568415be6f411e8b6ddb3252314023019542e3b7e26354ed7fea107c67932169fdd4f1d02d19ff7146306e93625ea81de67dac1686c7774a70a860feb8b08a4", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "39313135333137363130", "sig": "306402307cef758170e1aa6d553f1cb93f02e4578de16da89bb7434192ede200bf7c5f1b0c4c25e5e07ffe0889152d88cc951c790230673af79be286c8bc787a97af62dd06fc2e817489c2bcfaa9ef61b77a2e7b95c49af946cdc6fed8988ce4d502a7107187", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32383934333937393632", "sig": "3065023100885c483933dc8b5609f0ebd2ed917b4820962db61101c8ec21e24851228f917e3e0bf3aed9eac628362b01fadcb88e3302304732acc7bf375d267f7f23fddc81c844fb23bc913b820ef00b23d0338a9b3142e20d93c25a22627ef78ecc22caa513ea", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31323333353833303333", "sig": "306402307b990db18a20a82be2345511c7638c7f84cc75d679600d0de57861028eff93a4853d53c9d9779e7386ffc9f08c104d1d023017c38689a0dafbd6589558f87d73f1403d18ebc85e6ad071946a5aba27c9d833c12a825cf6331e5e698522a40c54c869", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "323131323639323536", "sig": "306602310083f64e214381686f57693c04d640bf279a55446d3e8a43173263ab348b946a3b14dee892add49fa6057e91dc0391d7fa02310084d489c507e32f88fbf8b8acd6fbfd3039da7e767a81d0b64a832f9d079c34275ee84a5cce67c0603df89bf36707e62b", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33303035343833383634", "sig": "306502306585681d741ed1019b408ce93408aeffeffb1888b3b5e0da5f2821bf15b168b2ec06a4a46edd8f9137c48480c08f13d502310087403a6bf1741ab398d49465ca99128740dae50cae3cbc66748268143a2f8fd5af0c6d727f320a81783696be6b642d1b", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38333536373632373235", "sig": "3065023100df56c59e4b82d676c767e16c8c0adee66962ea6a8cd75f385cf44990257265b272e18fe19ca0573d829898bde3a3338502306ddcfaed53fedfc4d7ca7a6785dcd3992fa3ad6752c500ffeb18eb20c4a7994778f2dbe4ec1c827562e5100a5085fc44", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "38333239353030373630", "sig": "3064023040fd073f877efa572a7e5186d02e1655e7e48be7f0461d7f71f218aa502a7d2dd61896379148efcf7ec30b3fb5943b7d023043cb9cf273cce2c30bcdc6f7825e4530687736340d01301050c858b4029aa5bbd24f08d56b07126ab48f74083e5d54e5", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "31393237303834313635", "sig": "30650230289f2782535cfbb90798dbc3a92434ec21c7f40022ab59fdb86f739dff5bca37b7a102db0a4f9209589e1dc88272458c0231008337cfe11dc6b38ac8226b68bce4fb19f045f3b4c0da2185b49600176198509da073d9aaf1992e59640cb02fbd44530a", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "37333032333538323838", "sig": "30650230487ee0974d8aed3ea9659822e128176faa5c576b4acbdcd2f21f23a6a13a8ee2829a88fb73ad332be5bfa6c2454a4fe1023100ea3b02f673a0c8f3144c1124b72ea9ffd2db4adda6210dca1408fe39028e3640a8395f73621315a47f505f7825e5de70", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3133343733373337333833", "sig": "30640230339911a1ac00fd099886425cf72263937cef82141558193a58f482b6e24abe1cfe5593771691da6b30246c7024c393f8023065a02c5ec22db3f9b8768dc7f5eefedc7c55ca68981eaf2ee0864477ac326d3e5038b97f059de1acb2ca3e0c369663e7", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32353031353533363839", "sig": "3066023100da687f5087ea087b241f4d626f2d0230b7c02e66c0591ff642661856982c6939a47a502470deae8c69de091fe7eb9a68023100c31d0283df38c098486ff90ca69787f1ff1ef59875db08d647bc454659fa84b9dac9834dedd81cd60bcefba2f945d4d0", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "33353335303430303239", "sig": "3065023100a6bf038486ee5f960eb529e6e0e193a9bc2664017d577cf8167af1bc9ab079911aa965d8de11e352cbda6d7b4fc052d802304d552856919ba4cf08ee1aeb0a3b2a56dc148a5030582a74317b39e1bb06e2539f2c978a5fc08fa0b9b362341532c393", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31343230333537323930", "sig": "3064023064d404bcc4d61394a7335d9a5fd9b360b07bea1f229045b803cff6847578dad242d1ccfe4765ba569013ed27da2f0715023005d970b9f318b56d86a2173b2ccd0aab2763b754098037943fb18ee1c5da8208cb545e81106d2354f563d8ef0da705b8", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "393035313735303638", "sig": "3066023100e984af14b43e372cf258189a5c99d1b54e929eb929eb323da23496c6cc17a0ec7744ec97a712ce371285c23421528be3023100cbdfe095d11e373cdf6b9e8b077bad25a549edd37c4e182f0b6b9ceb444c2e2b379dbce246466b6a0d764e3c4155cb4e", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31363133373734323935", "sig": "306402307ddd669fc03d457c2c99b64a14799fcdc04e6ecbcb0c058f3d977e799a13677e6413e32737b39c3493977457236270be0230386606a482a682efb740fd5df6c83903794a6708a793af7350623edd184ef61545991b5a06e121a66eeb934e972032f6", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "383733363733343931", "sig": "3064023068ac4b7ca89a38cae29905c9a535ea3311e3be2bab7ac1f65f95cc31627ceb428ef422874f1fb79ae42775d64c5533b602306db4b6cfd5439fae0dc896332ec8fbcfddd5316bba8b0ea9b25a0bef8c3887f2f7bcd62ea2f75fc2ee6c8d4c1516842b", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34333535313036343035", "sig": "3065023053cf0308ea4b28e52ce27123d6cfacf6e8deb01dde37b2e0f564b5587b30843cd8785f57cde322fdb9213faa07b5b4840231008bcd5ad407f01b8cbdb18e42192feee4abcb231879cc183b8f5e6c888f57d48c2b5fab1d3c4369dbf0389febb6b3efa7", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34353339353735383736", "sig": "3065023100e00f0b21a679d371878f3a4c924c68365426a98fe3e88c8d221be37ce5155b45a56108facf0c863066066f8898700c4102301fbe0b4096692ffa698565f3998b9bf8299d563ab54f4d0737e62a99d7cd4af970fa6918925d93b72518d5c6eb342c00", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "383933363633323031", "sig": "306402306697d087b2f535d80d744e3f36f1e6925dbfdcc91378a0da7c0a056e11458addc7cf822b3a2247589edb014615c9538f0230718ebe1e4b46ace8e54209facf429bd1864e519af1cfedd58d9421eda697abc4e318152956a82876b1eaa41c186a72c2", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "33383036303638303338", "sig": "3066023100ad49f1cda72a2d8b06a15252610a3582c999a9e7dcb0252a557c49edf42114d2fd81e6053d703415a256c2ab76862f78023100ba44945e707220de5cbdd4aff453ee42e8c98b17760fbc21bf7db7e85bb2461a69064bcf4667f00e3856d60208e83f6b", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "32323332383137343937", "sig": "3065023077f0603819b69683a871538d8654479e0f2e074a393ce153b9e192c94b21afd60bc093ec88373990880d422bf5d8270b023100fb7e68ad7389276058da83963b5e544dccc4a61ad8b5377c42356ef6c853b363a3c4d8c5cbe93eb594a1ee2762dac2de", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "34303734333232353538", "sig": "3064023077f635aba21e0010367e071ea7ba273fca770dfce16da8e2370f67c47e7096c33455e3a469dbe40d4ddb6aa98bfbdfe402305109e7ebe6d89f80d5412bcddee586ffe658d08d18d34c3bf13af99e77d8b2ae265f32ae054d8e67053bdae80f1c8306", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "363130363735383237", "sig": "3066023100d9a417d59b5ce3079f364a9a04672989b7a22902d60cce65ee2ba15cd0ec8fd5191a16ec05574fb6119db4b639b9bc1e02310085be5ab44b63d47cb215f2ccee2736af31e4d643a9ec43ba8d111f11758a6c7e73631031607de8e971a6c291c2fa6d46", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "3137343138373339323133", "sig": "3066023100b2f6832448f8ed8107efd77c392d38f5997946d8bb47322d81276059bf0ad49632dbbee84ee0a5c3fd2598b4a262d906023100e2411b8ca995aa9711c2e273172c71d166fdd336bac39a3b3444c9dbfe9e3272486e4abf327eaf5b216bccc0900b7a15", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "35313237383432323837", "sig": "3065023100bce837f1423507353cdd93db53e9d468ff9f1a5ca680577441f222cf3c3b70931641a83b7eb18445bb55461ce842274e0230797481a3380202e427659ba7113a5d02ed2bfdbdf88186c843aa8ed908956ee6e82eff7d7f1bb6dc2afb63665847d83f", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "35303338363930383739", "sig": "3065023100a21859c29f1af1dd52dff9dee9ea6bba3e93411c2269a694e290fa6d47c10e6be33898c35880a9ebd89fd2795398592e02305fabca618898cfa105b61af5173a1cd9e0d9be0b15f82d2a31beff2b2df138720b80b3beef309ae980bd91511d45b446", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "33383737303432333937", "sig": "3065023100c3490395cc64dfbaea1a75f147eafde602066e840722d1a5cf37765798e17afd531218c7db1d06e6fa5b7c00d11463c0023004626f800fc028ec0bc583c873bca0b47004f72d45705506b080747693e1f771966310d9c7031b6853047c936788f7eb", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "333231373038313738", "sig": "306402302427377713b2ffcda16e539eb85d0f1d716ab5373bbc22c66d51789da2ff88c52824d45b260e0d1a151f0633ce62f48302300bee19617cbdf9001b57871cd486f9ddc0b022727d3508b59f9b465d6c43c17f101fead582fe99297f91eb9d4b552dab", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37363637303434323730", "sig": "3065023100f60077a94b71ab1d6a0547af84b53cc9b942a745644d14d16b17475030781cee1ba3976a55a2396c05b2e0c6afa1306302305fe9cf83ab2273b4115b5f55db9622b298424ed4ba84d6cdf89c1ee4e77d7799b874b47a53b51a5099b710939564ffae", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31313034373435303432", "sig": "306502303dd186bd0227d623f977eb9cb66ee7542a555aa60c2d89d0d65b41def1201ced714890e3d8e9e5aec9a897d61b6803d7023100ccca9bc91b35e0a6eb86a3f89f0acd90fbad7cbdaaabc968a97cb3bde1f21e5e15809576afba10708024275a500fa903", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "313533383730313534", "sig": "306502305778c5f0cc38d1eaf74b185676ba2e7dbd1ddf2181402fec68795cdb66206aea6260289a77e1ab90ef29af55372ef2f102310095f2ed540f3e4c64b3c7f263faa36c557e5fbccfab96e8a0379486e642b579a0bc516aa8f58b22d0ca829c6b975d122d", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32323631333835303439", "sig": "30650230269baee7c333b24ebafc68a6b75cdc937be92337ab14985beed20222b7ab49b2db117d2622121202b42832450e6bb708023100b030a6cfc43b6f2c431e93613c8104238bfa066a51d2a419b5ad852be40d69b63ccb2de9ab2ab0696d04f6a0058a8181", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "37353538373437363632", "sig": "3066023100cbb128f0b17a20d422c3fa16f8889d1beb0f2e9c24b65719cc88a7a3ac51c94212daf1863814edcfbff14c05cb8b2998023100e826d9c348f9f52186e6ff5fe7dda8da26abc27a92734586a691a07f059558157d11afb316515d7dd9f1c23eefc13f29", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "33343939333634313832", "sig": "3066023100e51b03bf50a43ddb7a14524c336f2f8257d177b0a01557657a2e156a256ed8b0c75720b9e7fc5abaca512ffdf9a6d049023100becc64336b9aa348a9b3ed4ff09cf4f5dff27b730660031aec088858258d7caca3639e3d325311a139fca4c97138bc23", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "32333639323733393835", "sig": "30650230177a00dce71c6811a3e303f42fbd84118106a28c65f270d534fbf6caf7af341b131c3dff2b9dab9732bfc13b8bcede15023100b2b4fa6c519dcc3a1317e9b9f5635e9f8f352215c7066a8b2e5b953b1768c09f0e17981cce27d030b7f0826dfe468154", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343933383339353635", "sig": "30650231009f180d741c934c8b5fef54986fe04e59c1121709ffccf9955eb529bb0d4f194cffae89079ec837aa71b36f6293b6577c02300e659e4149129c1ff578ef77fd645aa28bc76ef15e493dfda64e1c2130fa5936b45066e2678d7a6cb205c259140ceae2", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "32353334333739393337", "sig": "3066023100fd5d052c214815f462bf97f2648d9125d0b699f226879be26b581fe77bb060c2035daf878b2c41fbb1a2b59e91e91458023100953d630e92a72dc4542389c31c07bb1acc4822a8d4d34989f1e412e288916adb8acddadaa903d0cf2528127b2071e26a", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "383334383031353938", "sig": "3066023100ecd47c77cd0ad6d07b86e372ae85fcb26cbe86de5ea62e21265ec35df6912440c80252a0adb935de4657ce26d58baac0023100cf1c88dfbe0782282ad26c38541e69376021ece2589f1fa7320c406bc8e32f6504e6a7801be5da4c5636887aa6dfaf81", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32343131303537343836", "sig": "30650230415b96c16c2a4f837e429e5dd487dce404481e0922e214d4ca0952db08414f2747b6f75b8d8063f988961e34d343e920023100da971ce9385c3dce1a36852482341f64f057803bbbb27e2f51f7ca6de19f818525a6206d9005ce09aea81625c5388394", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "373836343638363335", "sig": "30650231008874629864ee14138b154be5bb497a0635554c7f21431222b29c169d702d27b3153e5503a32c68563098b8c336f5b314023046db1a0719623cd8bb38f15e3093ea829b9717f34a2219e03e0dedd9ae987d4389c370da97c709677787659f8996f613", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33303534373733373638", "sig": "3065023100b55460c9173b262db8e281a0106aa7ee28c41d6a3d1ab5eecad570ec32e9c41e8f98ff8f9818339f1233bca3e0f5cc2b02300ab47c58b0549a20e25e1e599f0d7c29a59869e630cb6e0ac559b8b7a330b197b396319439688131583f5d5992852f44", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "31393237303137373338", "sig": "3066023100922662f604542fb01c35dbc904626c0c83883df72cc955589a82aae2305b50a57ca5c54b4ad3ce40bf49a297d343c22b023100fb566679c426a12534fbe3fd28d60eba7b35a15256dba87e3874ad9851901963a381116394fce401ff87357fc18d4bdf", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "31303531333235363334", "sig": "306402307b1353d1112949c999386023a1f25fb63aa302c91846c94db05f462e372fdfab9fbef817fcb795e4ffea6928b1db9c3102300c1f69b6c15fef1e060ccf7d12f0ebf08202f4dc74ca1f2155f79bbf91bbff52984bfa2f396bb257d0d60f1963d7b542", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "34303139383636363832", "sig": "3064023040eb566e887378be48ec5548a4974046fece281bf842c28067c781d10b1a64e83c8a1c0298d2abef720f523ab3b294a302304f61d45339daa264848ef37f1d05f35b219df2fac7d0ac941790d4b7158b2cee19633d221193b05e1da473c39ff8d796", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3130343530323537333530", "sig": "306502310088f12b559a6975c085118d3e2c587fc0706a2b5bd8faa325fac0e2ad3a305a1ae624ef3d0b59136e1ab6ab5137ea80b602301049deeb0c9f6e6ce89efa749477b8c8b96e441c9ed0a650a8a2cd1f1ecfce9c9009b5ddffb1559d1a2dc40c71583139", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "333236393538353830", "sig": "3064023021cbc3b42183dbfbf11b1bf4852e358d8f3b3249c383d8f4d10915c37c70d8c77bca88f25ca18b2b30745d8b6e7b70f502303e81a263ca654fcd926d9d21b4d0d95d0cb9c2a810d8d9b544c35805b8ae6aaffa71122ad28c0a6d4a7892e8392a5039", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33303734363533323431", "sig": "3066023100b651ad5599345c763cdab23fa4103e528b05602c8acf0121fc407000b3fa0dc6139f396472864bc203e1b5fc79281ba8023100d7f573a9502e0f8bd7c6ce5511c21f16642fb81aa30fa4557f59cccf4a21b0c70b00b209413b28a0f4c8bed13b2f4c78", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "37373134363833343830", "sig": "30650230495ad55a38b11f61d891b4b771d180b81713c570877f5290b452d216aa6cfcddb5d2cbe63b14af2c83587aa128101538023100a0c3586cc1585fc1a815b8b6d4b7468a0c3d493643768bd949af1e946439b9624c2a79a30731ad537d4c7a6638fa8245", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31373933333831333230", "sig": "306502303560c7617e763a84e70e51790ee2ad764bab9f1f9701833cbe7daaad4776379140c71c9137b2df4df26834464b08992e023100dae6f459e9df5cd64191a80e15a11b571d79ea08a0c0407355dd854b480ecdf22486881e2b3c632705828432817e4e56", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "34383830363235353636", "sig": "3066023100ada0b173b36e55787f74d3d9e28c2464ea962f4320f70b568aa24119bf53056aa257006f1c375a5289c99293dbbc87d20231008e5bb37bb620539cf010082ca73bfb9c22244656eb2e1d846c2aac9ad2ef4473f7c702d1ee34d475d8c805a036b2d9ee", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "3439343337363438383537", "sig": "3066023100918f44f0dfcbbe4f80abe5fd887f15f8cd9f6477ba38e5b6e367ab2b5999450bd06ca4653a9ddcde73804431db45002e023100ffb6be06f1f047e120aa347bb79eb41101881382dfdda5b7b25a87883faeb4f4f331e871ab075b259c1fd5c810bc4ea4", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "34373038363839373836", "sig": "306402304272a729473798dcbb582415bcac1d46b6b3b26510cab220408c964c9ed4499e85d3d45214830a256ad2c7644cd559a202300c18795e6e2fefaf66e436c4ad48348752638cb7156eb51d081cc69cc1748a0ea555f924b1af8ddc89f2a07a25eb006f", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "33303239383732393531", "sig": "3065023100e7fca6ecb733bc504e3ea6cf0f0d188233c4192bfd4738a254c12b3fe656b18e7e4432a58977decfd3e8a7a617fd48ca02304034834ef3566cb988cf77e544b49fc1194343bd1dbfc33a543b3e4eb13a004c4de8001d41d388ecdda6949dc72ad182", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "33303137313930333835", "sig": "3065023100f9124df58b5f0768b69a28ae0ba3fe953ce595ed662177795a7b82550002dcb9ad05459bd1c62832f68f464d5f67996b023068991ba84968923046726edf2386c7b0b09532e2ba04a424460106d2f3f55866be04d862eebfbfa186b61382452d8b44", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393536333633393339", "sig": "30640230545c410f1ce821a1487a94f92226313693cbd1beee16f77c032449533b0b8d64cee954c31cfecc5f1b30b10f56c4becd0230426b3126ced0b611fa37372f85fcfe3637479a151725c2d66500653a7acbd2c2f474c759e9434192c77c819135452b1f", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "35333030373634333530", "sig": "30650231008838a90a3b591c45780fb05f0546255ee9cac9a909be7bc3859378f11c0ce483db8a9e8cf3d18aa6fef99af08a47763f023053d55440575bd74805cb1af903e5ebe56aecdb51781f8386ecd5be528472dca4445ee4769faa6097ddc32857305ce32e", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "31393334363634383434", "sig": "3066023100ec5bedca84cd99edafa98a82fe4464119e64d7c0ef8adbd314922cfb4f045e800e9e1b4c233d75eec58b0916966246c0023100fe23ad017b4b525954b80b468566ba5f418e5e05782df4c0c8d2ca377bc7d9a076942eb2a3f2cf257bbb1e2a11642dad", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "3335353435303535393632", "sig": "3066023100a686f038f8aba1e94cc210f2637adabb4f621a4da3015d424dd0cc06fd4049dff85b74fddfc6a84b7520c7796b7ae5fd023100cdbfc125d7f03511c3316b5d3068a506c67645272610ff73b7874d8f4e9045f1bc9165ce9995f4e7d68f270659c52570", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "31333031373232313038", "sig": "306602310088d55ac65133dab2ea64e7a30e35e8c8f8bd55636e4061e8e0dbef0b6052c671022c8c23c9225859ca2e72c7502f5e5f0231008b08b7606d594f81e42456ad80062ba81d986e0ebbd3a0acb53629cd16112c84bc6b8e8949708271afd4407902684f5d", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "35363137363931363932", "sig": "306502301f478f2150092529b16a201fee978200c8c13c2b0f15e8e301a9c78d0ac8929b67cf8a8664e98e8caed1a0c5a856d8af023100b1e546553d7efb04d03c7680e96fde773cb80207c8ec6b6d16db2d8b1ca3be228721694613d17801230e728bfea5370d", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33353831393332353334", "sig": "306402306474517a0b92fdc5d64e26e5d88616de83fc1be7e4ebb745575cd31cf04886fa07e00676b72e33ff6422e02a52b2d94302305a6c10b276d1fc9cd87797fd565f4a7b379835b16f4de07e4bcd4da5d5315943e74b5539683dc8e033249817b898aed9", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3135373136363738373434", "sig": "3066023100bfbea37843a7008a368863f83d3b0994222d5a53cd7be1c0816e2b2e56fe50921e01729d938976250276b18f4afb18b5023100940bbcdda97eb3e80715427a3b20df34ac1d293015c2cb46754a519ea6f81116f2c0ec780f81faf8bd3b35cf8564cf2b", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33313939373833333630", "sig": "3066023100925fbea8fe31438bc1c3208d848acc9416b2031e1a153fc9a25df2625d4a3e20c8ca045596d67ab3aff74add593e3897023100878e3060a4b83f2d0a2e3d4317f35fb9fba0212e75e9913a26f02b35bd732255cb25d1db2910c1c18b0edc1696fb3c3a", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "373430343735303832", "sig": "306502300624239416f6cf0755642ba9ae4aaf71842eaa1ed6e04c38c8e19909743b2660f0f191dbeecad3f2f53d8ef86e9c860302310087734389d9a063ca716f0915b5836c9ed840269dad20086eef59d278d7f13fd6748c0dc18ddd6953d6dcd9584be91178", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "343137343336353339", "sig": "30650231008037d7eb5c58311ce806a580fba2fbcd1db93466899ea527069812d41cfb29d0c092100efdbfb694ee6d5dfa1ffec2740230459feded2c47bd44e021152160cd1e8be4efe37f6b96fbe79b9fb09d6a376023fa5f967a03bf9d3e82d3c30e9dc3cae1", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "31323335363538383839", "sig": "30660231008f2dfef7d82610ab697f04ac6132dc2a5e5ac2b90db7620d7af3a58d8f66662c7d9178b39d87d187766f71a992c1003c023100e4e021d6d3951c25e786326191b47fb94d94b194f8c7ead99efe862d9f7d1256b8cf98ec671fc8b7b198aa91a16554e6", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "32343239323535343034", "sig": "306402306fa7a97e0a70f60703b691134e1d54708406f5f91c8dbc653ee953d139af4eccc83f565022eb109e20cb6aecec369751023054747ddb0d1ddab1b742596fa95468038e2ec102bcfa820fdbd324e4dc3c21e081259bf1248e591b38c5610b2fb3146c", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "3531383033303235343636", "sig": "3065023100fbf953098613151d7dd1b4c949c71112f2579147d9abf50abf2a8bac0381f26d8bf5376a656b05a9d39c3a020dade6ca02301a3aa4c1547990302bd35a4bff0e5e85969f9ae4fb1faf0d7094149a0f4890f6420a6aa64a99906a6e4864a38077b764", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "34343736333938323030", "sig": "3066023100c910fa2b3e62fa81ed99436a1954c6dca50019fe853e368ac4f2c836c8eaaa45d74cb2f3de1c492a5070cbb6fb00c94d023100fee95b6e898dc053aa0426e2d0102c6841cb66ff0b43bc9acb71959d9e80ee4ab853a08900036ded7ebd9df3ca525e55", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "39303630303335323132", "sig": "306502305c609212390b144969ce0cacd680d165422bb1dc28b98db296dc6eebe4e5dd84473e5cd901e416ba7f838b56b7af3611023100cd1e77c240313a0ce8b4861ad2832fabf2585917d98aab7b2a525aa2f3394bd9214b930ce139555e04e51ffbba019dd8", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31373536303533303938", "sig": "3066023100b126bf96837a4bf7e09680f4c826e3ad4cb9edce04d2a547134a91523710ab99d45ad7de0c213c588a79db4a952ff5e1023100c1c6ba25ea630772de4025689c178d78390aa50810c3ed17835d394bc4b1a568d1008abbfd1da87c0c4f7a284c77c052", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "34393830383630303338", "sig": "306402301554c3cdb6a325689d85e7f1a97cafdc5d9d1586957c1be8e04b1cc3ac28a260f3c58dbaccabc3fdfef7cb344607be4402301259d8eb8b44ec7865d93edbdccc62d5f2d37e9f0bfe84219c2861d9bea7c7dd58e88c2f2fc14cc586269a28070010a8", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "33363231383735333335", "sig": "3066023100ed2dd0845ab8621992c4507468586173d00ccca7faed06913db70eca582667437c6f589be97e5ea5c6a0065d593a3cee0231009016b1626e66fe53b5a7dd6661660b2fd735995b1d64d914c5c5c6a91452e050617314b14d57e464381c2730b879414b", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "36363433333334373231", "sig": "3065023058a36f6b0ad5a0211d5f58e2f74c7e7930cdb7efbbf45d0111ef606c253b4bd227a7e53c834619503d11113582ccf013023100cedecc59dd9f3ece325fde05c09f0e20614c79fce3996c71b5ab2b26451e832b476b8dff9c8a67b655d8499a171f5e71", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34343534393432373832", "sig": "30650230217e55870cdeb394b937e0ebca9cceff3d1d69ef2c0f4ccec8cf95b912d544caa244b1233d5e677de94c6a59048562d9023100b716db65e62fcc5f01426ef316ba17c612e0e5d3539e06a962211831f118cb0beb10f289f32d7ff276f119c234359822", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "3230313834343032", "sig": "3065023100b2bc708b7e0616f4b1128cd39397554cd046218a7385198f9f52c62ba10431eba1cc81409ae6695f7e29d7a19716aa8702301f34499351f6d29d222e08dc498985cc7230d4e7f49c2070b787ae2083e328a808ffcb047d89bc0e2bb295a6f8419f4d", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "3538313332313733", "sig": "3065023100e2d36ace4a4a5aba6c06afcb98fde61ab464d36c5f52fb51b9678e76bfc6e8bb131b8a118fe5ddab580a9300d84602e802301ce43e58f9f03d52df5720e3cacc7a46eaeacda51d0701581637da10233a0bf28fbacfb411dacbda9172cbe4b1b093c5", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31313833383631383131", "sig": "306402303f3357cc7dfe35a0268195bfd14187fde860a12f776978a12a2ca43dc0237bc48f475bd440fdcd43ec101f4d552bfdd6023068555a3e46b373adf3464329d392fbda0cb3f2490c7e6482c888755f62aca75ed416cd59bc0f84fcbc749af79e145d64", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31393232363032393036", "sig": "3065023026bdc539d084e97356bbca9aa23b52ebfe99fe6d26027b4252cfccbc9359cdaa4a526faf9ffcdc0f831e338baf9bdaea023100bce39066652e0e83c85affbbb56234788f1466770d9d81f014e7aad4694c167f06ee3bd0644f983415256bea8274540e", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "393735313433323037", "sig": "3065023100a977c3fef54b995745c8d34b56429b8deb9ed266a2a782b80f954afdcad3642bd1ac554c17d45ea5d35038524c960f8d023021e318cde939f7cbb2fc842b0885acb3af4905bd07a834678f8229e74f3d3d6b779f7253cbf0b1295da1775a43e956f4", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "38333135313136333833", "sig": "306502310080f7b25aea62a1f244dd0b4938e3be40a5077c278e0624561b9dc71c6e530557a8e362ebb5e7ea5244b06d68d986211802305a7f8b1dce45583bd9b9dd6b441126bc21526ce861181d2eed7a4bf9998ad4c6461da3107b0ed05711f744236f4d5d9f", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "333236333136383132", "sig": "3066023100cb33c16745d056d2f5450f88f77f10f27a405b4e2fa94d9dd71efefedae568723c036026d8911d987bf43f96ad726a1b023100c00433bcf5a4d87f937fb8b3235ef87e525352164de2f28f96d332022e8437b0b56c7eb3a28cda7f71c6661fe8368ddc", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "34303239363837313336", "sig": "3066023100cf6cf760e836a2bbd6d355890a6c2c342196a39d2b6e57fbb8d01ded5639fe5d2c0291074d44758cceafb816c3a1687d023100a1ff948964717d567b54d8c866f79ec0e6d147f7e5dbd6e266efc3c71cf6b6c116a6c2d675f1664520e8775545f7a056", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case hash", "msg": "36333230383831313931", "sig": "3066023100ff679d0731ed5aa6973a29dc14e56ec983521c0070600034acb6b845b9c01bb3e01bd86974495f70a919bc8991b96023023100d68741cf8770abc4dddf74089cab8679cdc77a807f587f6b112dd8fb7df47c469d440bedad5f3615e667c0e0689e66f8", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case hash", "msg": "35323235333930373830", "sig": "306402301f42b6c6ef17ec124bfeabdbc78fbc18b3773d7f3001719bf83547aed7bd8b85596d39c3d74ed5e39e558f2367b8c5fb02300e42b5d0addf488e15881d80abcd4710cd1cef8138fd41e868f6edbaf369807246b43e3d98e141a7fb34f25c8d8f52c3", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case hash", "msg": "31333439333933363934", "sig": "306502306aecc2ac6bb44f69f328f2d26ceaaa8b92fbbffcaef5258b9915311afc47259ac6cc03e1a88211a603c7342f0fe24706023100cdacb0fcb3c54d33420fd48331c5db26e4bfca9b7465c9b16163d9ec75175442d8a37564ea372d57a47a5828d8ccbcb3", "result": "valid", "flags": []}, {"tcId": 357, "comment": "special case hash", "msg": "3130333937393630373631", "sig": "3065023100834a374b3ec9b7859bb08427e9d880875af3c1a98ead009f24bab9ebe1d067a13f83fb78705ee8ac32e6950fc7a2aba4023043949adf582e52ceefe9e3ed95a0c4a228da95705da24102b93c45b09fa08ff9d358d98f8cb681f68434485695c43909", "result": "valid", "flags": []}, {"tcId": 358, "comment": "Signature generated without truncating the hash", "msg": "************", "sig": "306602310087658b9215c91a52e1b82d92d9ffd6cfebefe6433b5c46cfbde751da58b2d44b22c2da1dcbbdc9d0d606c53cde2d2ffb02310089d6a04210e58032c0768adcd13120835cb0fae97d1f387e4009df9154e9365f1788ffdcf854bf7b9eea893b7f397f01", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040b478a0cbf5eb4671e50812f1945e4f852d8890dacb40e9947ab238e6db8f1f97f0ef769e44a1c9455ca0f21f8cc24be4b4b3e825064d3ea8bf8562e7a23c9a61026f77251acb12478d3391e5b08f9ed5979b2ecb974d5025b683f146f30dc3c", "wx": "0b478a0cbf5eb4671e50812f1945e4f852d8890dacb40e9947ab238e6db8f1f97f0ef769e44a1c9455ca0f21f8cc24be", "wy": "4b4b3e825064d3ea8bf8562e7a23c9a61026f77251acb12478d3391e5b08f9ed5979b2ecb974d5025b683f146f30dc3c"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040b478a0cbf5eb4671e50812f1945e4f852d8890dacb40e9947ab238e6db8f1f97f0ef769e44a1c9455ca0f21f8cc24be4b4b3e825064d3ea8bf8562e7a23c9a61026f77251acb12478d3391e5b08f9ed5979b2ecb974d5025b683f146f30dc3c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEC0eKDL9etGceUIEvGUXk+FLYiQ2stA6Z\nR6sjjm248fl/Dvdp5EoclFXKDyH4zCS+S0s+glBk0+qL+FYueiPJphAm93JRrLEk\neNM5HlsI+e1ZebLsuXTVAltoPxRvMNw8\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "k*G has a large x-coordinate", "msg": "************", "sig": "304d0218389cb27e0bc8d21fa7e5f24cb74f58851313e696333ad68b023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "valid", "flags": []}, {"tcId": 360, "comment": "r too large", "msg": "************", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffe023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048482baecb7ce217056f34fc8a85875876002bba5e13fbe9857d3653aa30f6821d687f5eb4a2be7ff2c0b29a2e053abd90d3f5d116ce9307f9f6016d46ea72bb29eb4778a0480b1dff8f5ddaf458c788c16d3ee41398b72cc7b5c60d811517cae", "wx": "008482baecb7ce217056f34fc8a85875876002bba5e13fbe9857d3653aa30f6821d687f5eb4a2be7ff2c0b29a2e053abd9", "wy": "0d3f5d116ce9307f9f6016d46ea72bb29eb4778a0480b1dff8f5ddaf458c788c16d3ee41398b72cc7b5c60d811517cae"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048482baecb7ce217056f34fc8a85875876002bba5e13fbe9857d3653aa30f6821d687f5eb4a2be7ff2c0b29a2e053abd90d3f5d116ce9307f9f6016d46ea72bb29eb4778a0480b1dff8f5ddaf458c788c16d3ee41398b72cc7b5c60d811517cae", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEhIK67LfOIXBW80/IqFh1h2ACu6XhP76Y\nV9NlOqMPaCHWh/XrSivn/ywLKaLgU6vZDT9dEWzpMH+fYBbUbqcrsp60d4oEgLHf\n+PXdr0WMeIwW0+5BOYtyzHtcYNgRUXyu\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "r,s are large", "msg": "************", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52971", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d71539a7304a82f93aa69f89f0668fa273dfeebd1ead07171de49f3d071d5dc53a130c14d2189b8c032cc915a83422e88023068f0154419b434a9ce0bf12c99235595f4b616c2db97b28e523f947451198fa63aa7901e71f8d9b31bfd2cf7ad0", "wx": "00d71539a7304a82f93aa69f89f0668fa273dfeebd1ead07171de49f3d071d5dc53a130c14d2189b8c032cc915a83422e8", "wy": "008023068f0154419b434a9ce0bf12c99235595f4b616c2db97b28e523f947451198fa63aa7901e71f8d9b31bfd2cf7ad0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d71539a7304a82f93aa69f89f0668fa273dfeebd1ead07171de49f3d071d5dc53a130c14d2189b8c032cc915a83422e88023068f0154419b434a9ce0bf12c99235595f4b616c2db97b28e523f947451198fa63aa7901e71f8d9b31bfd2cf7ad0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE1xU5pzBKgvk6pp+J8GaPonPf7r0erQcX\nHeSfPQcdXcU6EwwU0hibjAMsyRWoNCLogCMGjwFUQZtDSpzgvxLJkjVZX0thbC25\neyjlI/lHRRGY+mOqeQHnH42bMb/Sz3rQ\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d1aee55fdc2a716ba2fabcb57020b72e539bf05c7902f98e105bf83d4cc10c2a159a3cf7e01d749d2205f4da6bd8fcf1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040f2ae91fdc85f7739a400be4c128dcfb377559c936bdeee620cd08449b7bcbef992c2534dc559cfe328067fc3e87df8ceca1b61fd631c99bdeedc28c4a780d10ab38fca2cc7ed53eab34309646d5c2c9dc7063fe5a7a58426e66b0ca3db1c22f", "wx": "0f2ae91fdc85f7739a400be4c128dcfb377559c936bdeee620cd08449b7bcbef992c2534dc559cfe328067fc3e87df8c", "wy": "00eca1b61fd631c99bdeedc28c4a780d10ab38fca2cc7ed53eab34309646d5c2c9dc7063fe5a7a58426e66b0ca3db1c22f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040f2ae91fdc85f7739a400be4c128dcfb377559c936bdeee620cd08449b7bcbef992c2534dc559cfe328067fc3e87df8ceca1b61fd631c99bdeedc28c4a780d10ab38fca2cc7ed53eab34309646d5c2c9dc7063fe5a7a58426e66b0ca3db1c22f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEDyrpH9yF93OaQAvkwSjc+zd1Wck2ve7m\nIM0IRJt7y++ZLCU03FWc/jKAZ/w+h9+M7KG2H9YxyZve7cKMSngNEKs4/KLMftU+\nqzQwlkbVwsnccGP+WnpYQm5msMo9scIv\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b6b681dc484f4f020fd3f7e626d88edc6ded1b382ef3e143d60887b51394260832d4d8f2ef70458f9fa90e38c2e19e4f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041718de897c6b650a9c9212a0c0e67a4698e63c3567f025e6e02dda9161ece01ea0a18afc29487a5ed01a48856c9aab4b5e4bacf9f45d4659438dc28ca21a0c461e38cbf911471e4141630f0f7c32d4aa7a1310d48eea3a413bf682201fed3f27", "wx": "1718de897c6b650a9c9212a0c0e67a4698e63c3567f025e6e02dda9161ece01ea0a18afc29487a5ed01a48856c9aab4b", "wy": "5e4bacf9f45d4659438dc28ca21a0c461e38cbf911471e4141630f0f7c32d4aa7a1310d48eea3a413bf682201fed3f27"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041718de897c6b650a9c9212a0c0e67a4698e63c3567f025e6e02dda9161ece01ea0a18afc29487a5ed01a48856c9aab4b5e4bacf9f45d4659438dc28ca21a0c461e38cbf911471e4141630f0f7c32d4aa7a1310d48eea3a413bf682201fed3f27", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEFxjeiXxrZQqckhKgwOZ6RpjmPDVn8CXm\n4C3akWHs4B6goYr8KUh6XtAaSIVsmqtLXkus+fRdRllDjcKMohoMRh44y/kRRx5B\nQWMPD3wy1Kp6ExDUjuo6QTv2giAf7T8n\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "small r and s", "msg": "************", "sig": "3006020102020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0493f92e69558939a64304de24eaf2799ffb29e3dfe00a8aa354317138c2942d449fa735eb5b5ec24429b03194fde9924019437f8818df2d550f0079f98571c83b75ac0ca26edd24793d0a95068d8258e17830763b03334ffc8bf5476488a7c1b0", "wx": "0093f92e69558939a64304de24eaf2799ffb29e3dfe00a8aa354317138c2942d449fa735eb5b5ec24429b03194fde99240", "wy": "19437f8818df2d550f0079f98571c83b75ac0ca26edd24793d0a95068d8258e17830763b03334ffc8bf5476488a7c1b0"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000493f92e69558939a64304de24eaf2799ffb29e3dfe00a8aa354317138c2942d449fa735eb5b5ec24429b03194fde9924019437f8818df2d550f0079f98571c83b75ac0ca26edd24793d0a95068d8258e17830763b03334ffc8bf5476488a7c1b0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEk/kuaVWJOaZDBN4k6vJ5n/sp49/gCoqj\nVDFxOMKULUSfpzXrW17CRCmwMZT96ZJAGUN/iBjfLVUPAHn5hXHIO3WsDKJu3SR5\nPQqVBo2CWOF4MHY7AzNP/Iv1R2SIp8Gw\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s", "msg": "************", "sig": "3006020102020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04969e39e9e2f3e48996e7faca4cc842790f8a1477d1c5699933563b470495f4ac68b76d7e1d4c39a7b70df2dfec1a0816f03e95c8b98c478330ae3e15e1b80e33caa3057f4305e84865d3a270cab9751214947d0bee6a23d412d19e606a9839d3", "wx": "00969e39e9e2f3e48996e7faca4cc842790f8a1477d1c5699933563b470495f4ac68b76d7e1d4c39a7b70df2dfec1a0816", "wy": "00f03e95c8b98c478330ae3e15e1b80e33caa3057f4305e84865d3a270cab9751214947d0bee6a23d412d19e606a9839d3"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004969e39e9e2f3e48996e7faca4cc842790f8a1477d1c5699933563b470495f4ac68b76d7e1d4c39a7b70df2dfec1a0816f03e95c8b98c478330ae3e15e1b80e33caa3057f4305e84865d3a270cab9751214947d0bee6a23d412d19e606a9839d3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAElp456eLz5ImW5/rKTMhCeQ+KFHfRxWmZ\nM1Y7RwSV9Kxot21+HUw5p7cN8t/sGggW8D6VyLmMR4Mwrj4V4bgOM8qjBX9DBehI\nZdOicMq5dRIUlH0L7moj1BLRnmBqmDnT\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "small r and s", "msg": "************", "sig": "3006020102020103", "result": "valid", "flags": []}, {"tcId": 367, "comment": "r is larger than n", "msg": "************", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52975020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f9feeef209b372bb678d2d1275c653d7bdb6908982879321e7f4ec7a083ad85762ee1b9d85c7227bdfbc56e44727030f4c57fa58b619384165a3b50a62c63dd3a8caedc6204cd35c404d1f16a6cbbe8283228448b468fdbb12d54e5d2cd70cea", "wx": "00f9feeef209b372bb678d2d1275c653d7bdb6908982879321e7f4ec7a083ad85762ee1b9d85c7227bdfbc56e44727030f", "wy": "4c57fa58b619384165a3b50a62c63dd3a8caedc6204cd35c404d1f16a6cbbe8283228448b468fdbb12d54e5d2cd70cea"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f9feeef209b372bb678d2d1275c653d7bdb6908982879321e7f4ec7a083ad85762ee1b9d85c7227bdfbc56e44727030f4c57fa58b619384165a3b50a62c63dd3a8caedc6204cd35c404d1f16a6cbbe8283228448b468fdbb12d54e5d2cd70cea", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+f7u8gmzcrtnjS0SdcZT1722kImCh5Mh\n5/Tsegg62Fdi7hudhccie9+8VuRHJwMPTFf6WLYZOEFlo7UKYsY906jK7cYgTNNc\nQE0fFqbLvoKDIoRItGj9uxLVTl0s1wzq\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "s is larger than n", "msg": "************", "sig": "3036020102023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accd7fffa", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0420d23d3e5a68e72896db6b2500f0797de558586f31d79b696b19a170299674024dc8acb4adbfa2ea83b0e5deda5adc8a20534957096a651f2ad6ecda4188496639d04ded5039df67b3e7d28586082007021284e8e11f79bedb42cb5a7267e55a", "wx": "20d23d3e5a68e72896db6b2500f0797de558586f31d79b696b19a170299674024dc8acb4adbfa2ea83b0e5deda5adc8a", "wy": "20534957096a651f2ad6ecda4188496639d04ded5039df67b3e7d28586082007021284e8e11f79bedb42cb5a7267e55a"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000420d23d3e5a68e72896db6b2500f0797de558586f31d79b696b19a170299674024dc8acb4adbfa2ea83b0e5deda5adc8a20534957096a651f2ad6ecda4188496639d04ded5039df67b3e7d28586082007021284e8e11f79bedb42cb5a7267e55a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEINI9Plpo5yiW22slAPB5feVYWG8x15tp\naxmhcCmWdAJNyKy0rb+i6oOw5d7aWtyKIFNJVwlqZR8q1uzaQYhJZjnQTe1QOd9n\ns+fShYYIIAcCEoTo4R95vttCy1pyZ+Va\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "small r and s^-1", "msg": "************", "sig": "3036020201000230489122448912244891224489122448912244891224489122347ce79bc437f4d071aaa92c7d6c882ae8734dc18cb0d553", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045f662a2de99ba5d6ba9d441581b1e0036056dc2d8a146080fe4cdbefa47efbcda2874ecfa49f4f8fa99ac309d917bf283a4117e5bc5014c832fa8e08eedca6c642d6181475151c1808b3e33fcfd0103a1e4210fbe8ff58355f8f6706743e0cda", "wx": "5f662a2de99ba5d6ba9d441581b1e0036056dc2d8a146080fe4cdbefa47efbcda2874ecfa49f4f8fa99ac309d917bf28", "wy": "3a4117e5bc5014c832fa8e08eedca6c642d6181475151c1808b3e33fcfd0103a1e4210fbe8ff58355f8f6706743e0cda"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045f662a2de99ba5d6ba9d441581b1e0036056dc2d8a146080fe4cdbefa47efbcda2874ecfa49f4f8fa99ac309d917bf283a4117e5bc5014c832fa8e08eedca6c642d6181475151c1808b3e33fcfd0103a1e4210fbe8ff58355f8f6706743e0cda", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEX2YqLembpda6nUQVgbHgA2BW3C2KFGCA\n/kzb76R++82ih07PpJ9Pj6mawwnZF78oOkEX5bxQFMgy+o4I7tymxkLWGBR1FRwY\nCLPjP8/QEDoeQhD76P9YNV+PZwZ0Pgza\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "smallish r and s^-1", "msg": "************", "sig": "303c02072d9b4d347952cd023100ce751512561b6f57c75342848a3ff98ccf9c3f0219b6b68d00449e6c971a85d2e2ce73554b59219d54d2083b46327351", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0422c42feccc69d4aacb408ba1d1f114bf54c90489792d2814b264248dbed9e156a64835c9ea97b837120d2a717f5848412a50bb39dfef060efd1a6dfb23018e7601d3b3ba80f19aab9e334cd5e1a7db144ef25d3745ef40099a510fdfb837070a", "wx": "22c42feccc69d4aacb408ba1d1f114bf54c90489792d2814b264248dbed9e156a64835c9ea97b837120d2a717f584841", "wy": "2a50bb39dfef060efd1a6dfb23018e7601d3b3ba80f19aab9e334cd5e1a7db144ef25d3745ef40099a510fdfb837070a"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000422c42feccc69d4aacb408ba1d1f114bf54c90489792d2814b264248dbed9e156a64835c9ea97b837120d2a717f5848412a50bb39dfef060efd1a6dfb23018e7601d3b3ba80f19aab9e334cd5e1a7db144ef25d3745ef40099a510fdfb837070a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEIsQv7Mxp1KrLQIuh0fEUv1TJBIl5LSgU\nsmQkjb7Z4VamSDXJ6pe4NxINKnF/WEhBKlC7Od/vBg79Gm37IwGOdgHTs7qA8Zqr\nnjNM1eGn2xRO8l03Re9ACZpRD9+4NwcK\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "100-bit r and small s^-1", "msg": "************", "sig": "3041020d1033e67e37b32b445580bf4efb02302ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad5215c51b320e460542f9cc38968ccdf4263684004eb79a452", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049dac20d645bb55d57cab932da900871184be9f92057aabef5af11fa2dc0a795566ee35d94b00ddb3a0c9d09e522a24409681ec7ec2a90696e49aabff89fd7bff27d7a779b3169000046f0566bc14580feb9e3a13b0b045970e13e58265a0be87", "wx": "009dac20d645bb55d57cab932da900871184be9f92057aabef5af11fa2dc0a795566ee35d94b00ddb3a0c9d09e522a2440", "wy": "009681ec7ec2a90696e49aabff89fd7bff27d7a779b3169000046f0566bc14580feb9e3a13b0b045970e13e58265a0be87"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049dac20d645bb55d57cab932da900871184be9f92057aabef5af11fa2dc0a795566ee35d94b00ddb3a0c9d09e522a24409681ec7ec2a90696e49aabff89fd7bff27d7a779b3169000046f0566bc14580feb9e3a13b0b045970e13e58265a0be87", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEnawg1kW7VdV8q5MtqQCHEYS+n5IFeqvv\nWvEfotwKeVVm7jXZSwDds6DJ0J5SKiRAloHsfsKpBpbkmqv/if17/yfXp3mzFpAA\nBG8FZrwUWA/rnjoTsLBFlw4T5YJloL6H\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "small r and 100 bit s^-1", "msg": "************", "sig": "303602020100023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04715a3ddb84b71d5921e96b1323d2d129e7c1fa16e31c0ebcea6b233ac8cfb61258875a364f1d3a548f09facbd001709d8e03bc73a762cf14f5470c8fee9f9650e7f9d808b1c81264eb9f85c6e94bff9ea398a88a0b173da4b585e0eed4352cb0", "wx": "715a3ddb84b71d5921e96b1323d2d129e7c1fa16e31c0ebcea6b233ac8cfb61258875a364f1d3a548f09facbd001709d", "wy": "008e03bc73a762cf14f5470c8fee9f9650e7f9d808b1c81264eb9f85c6e94bff9ea398a88a0b173da4b585e0eed4352cb0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004715a3ddb84b71d5921e96b1323d2d129e7c1fa16e31c0ebcea6b233ac8cfb61258875a364f1d3a548f09facbd001709d8e03bc73a762cf14f5470c8fee9f9650e7f9d808b1c81264eb9f85c6e94bff9ea398a88a0b173da4b585e0eed4352cb0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEcVo924S3HVkh6WsTI9LRKefB+hbjHA68\n6msjOsjPthJYh1o2Tx06VI8J+svQAXCdjgO8c6dizxT1RwyP7p+WUOf52AixyBJk\n65+FxulL/56jmKiKCxc9pLWF4O7UNSyw\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "100-bit r and s^-1", "msg": "************", "sig": "3041020d062522bbd3ecbe7c39e93e7c24023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0436d3a9ed200de6c6ebb1efee2d3c30e5f72c521c57a387979d1aa772d57088764033bd9da81bb73f8e834eb305b9444cc8a0b783cd1999ba35091372ce47e096b6e29ea54386f7501d7a7ceca2978fa0b3b3be95e03f883c27b38bdc251af860", "wx": "36d3a9ed200de6c6ebb1efee2d3c30e5f72c521c57a387979d1aa772d57088764033bd9da81bb73f8e834eb305b9444c", "wy": "00c8a0b783cd1999ba35091372ce47e096b6e29ea54386f7501d7a7ceca2978fa0b3b3be95e03f883c27b38bdc251af860"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000436d3a9ed200de6c6ebb1efee2d3c30e5f72c521c57a387979d1aa772d57088764033bd9da81bb73f8e834eb305b9444cc8a0b783cd1999ba35091372ce47e096b6e29ea54386f7501d7a7ceca2978fa0b3b3be95e03f883c27b38bdc251af860", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENtOp7SAN5sbrse/uLTww5fcsUhxXo4eX\nnRqnctVwiHZAM72dqBu3P46DTrMFuURMyKC3g80Zmbo1CRNyzkfglrbinqVDhvdQ\nHXp87KKXj6Czs76V4D+IPCezi9wlGvhg\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "r and s^-1 are close to n", "msg": "************", "sig": "3065023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc528f3023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0431a7e388c243a6dcc6509df7ba5ecc2566db33ba6f71d494140daf8fe0ba490859cf9d524d83eb76a06c02d616f508ab27a272ac157f661e1c0e9ba5944943d3e102bed4c52ffca7232cd8b955e0e792d982a7fa849d9c06ca1442354e1b3171", "wx": "31a7e388c243a6dcc6509df7ba5ecc2566db33ba6f71d494140daf8fe0ba490859cf9d524d83eb76a06c02d616f508ab", "wy": "27a272ac157f661e1c0e9ba5944943d3e102bed4c52ffca7232cd8b955e0e792d982a7fa849d9c06ca1442354e1b3171"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000431a7e388c243a6dcc6509df7ba5ecc2566db33ba6f71d494140daf8fe0ba490859cf9d524d83eb76a06c02d616f508ab27a272ac157f661e1c0e9ba5944943d3e102bed4c52ffca7232cd8b955e0e792d982a7fa849d9c06ca1442354e1b3171", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEMafjiMJDptzGUJ33ul7MJWbbM7pvcdSU\nFA2vj+C6SQhZz51STYPrdqBsAtYW9QirJ6JyrBV/Zh4cDpullElD0+ECvtTFL/yn\nIyzYuVXg55LZgqf6hJ2cBsoUQjVOGzFx\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "s == 1", "msg": "************", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020101", "result": "valid", "flags": []}, {"tcId": 376, "comment": "s == 0", "msg": "************", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047381fbdb53668ef60581152058fe6fc0fd38605253fb9b9d58e240893e8da5ba4488e5af092f431b5a359c4ac62a67d7e75d552ec90e3b6921ae1347f7609ebfe1524eae34671f824261ba3455908102be99e0231f929c718fa970286156760b", "wx": "7381fbdb53668ef60581152058fe6fc0fd38605253fb9b9d58e240893e8da5ba4488e5af092f431b5a359c4ac62a67d7", "wy": "00e75d552ec90e3b6921ae1347f7609ebfe1524eae34671f824261ba3455908102be99e0231f929c718fa970286156760b"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200047381fbdb53668ef60581152058fe6fc0fd38605253fb9b9d58e240893e8da5ba4488e5af092f431b5a359c4ac62a67d7e75d552ec90e3b6921ae1347f7609ebfe1524eae34671f824261ba3455908102be99e0231f929c718fa970286156760b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEc4H721NmjvYFgRUgWP5vwP04YFJT+5ud\nWOJAiT6NpbpEiOWvCS9DG1o1nErGKmfX511VLskOO2khrhNH92Cev+FSTq40Zx+C\nQmG6NFWQgQK+meAjH5KccY+pcChhVnYL\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "point at infinity during verify", "msg": "************", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045b896e18f0747794790cd9155c74d60bf5e9cf6b34b5c43b0776de78745c2a49414b24fb036e5ec658d00f0b5ee7af8eaa7569743b2c97130e6a92cf9c74c090275f09c4b7299f3e38c6a559bdf7b75cd78b291b714b4f2ae1b25878bc005608", "wx": "5b896e18f0747794790cd9155c74d60bf5e9cf6b34b5c43b0776de78745c2a49414b24fb036e5ec658d00f0b5ee7af8e", "wy": "00aa7569743b2c97130e6a92cf9c74c090275f09c4b7299f3e38c6a559bdf7b75cd78b291b714b4f2ae1b25878bc005608"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045b896e18f0747794790cd9155c74d60bf5e9cf6b34b5c43b0776de78745c2a49414b24fb036e5ec658d00f0b5ee7af8eaa7569743b2c97130e6a92cf9c74c090275f09c4b7299f3e38c6a559bdf7b75cd78b291b714b4f2ae1b25878bc005608", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEW4luGPB0d5R5DNkVXHTWC/Xpz2s0tcQ7\nB3beeHRcKklBSyT7A25exljQDwte56+OqnVpdDsslxMOapLPnHTAkCdfCcS3KZ8+\nOMalWb33t1zXiykbcUtPKuGyWHi8AFYI\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "edge case for signature malleability", "msg": "************", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040db3126187c353c0034e618ba3f4630e0b706312ec5211b1ac91f01caa23bee17456b5914e10c5cc5b9decbe078fd3c7a332aa70d7486cc7924ed61ec8a1b045ceba0f0a55a4bd3e7a9497dfbb1afc7193b6a34a8c0f6bf4dc5c707df14732eb", "wx": "0db3126187c353c0034e618ba3f4630e0b706312ec5211b1ac91f01caa23bee17456b5914e10c5cc5b9decbe078fd3c7", "wy": "00a332aa70d7486cc7924ed61ec8a1b045ceba0f0a55a4bd3e7a9497dfbb1afc7193b6a34a8c0f6bf4dc5c707df14732eb"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040db3126187c353c0034e618ba3f4630e0b706312ec5211b1ac91f01caa23bee17456b5914e10c5cc5b9decbe078fd3c7a332aa70d7486cc7924ed61ec8a1b045ceba0f0a55a4bd3e7a9497dfbb1afc7193b6a34a8c0f6bf4dc5c707df14732eb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEDbMSYYfDU8ADTmGLo/RjDgtwYxLsUhGx\nrJHwHKojvuF0VrWRThDFzFud7L4Hj9PHozKqcNdIbMeSTtYeyKGwRc66DwpVpL0+\nepSX37sa/HGTtqNKjA9r9NxccH3xRzLr\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "edge case for signature malleability", "msg": "************", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294ba", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040d0c15cb6cd38bea588f92c654f2c5b36d3fe1db8cae4f69dcdbeb51084e0ccadeb17990c1be62a55a32ff8df78157d090ba4770b1f1f2105fe3ed29fb793aaf6fe220c630b4fefa096460eccb31153f1dc6696f650d95e85681f229f30727f7", "wx": "0d0c15cb6cd38bea588f92c654f2c5b36d3fe1db8cae4f69dcdbeb51084e0ccadeb17990c1be62a55a32ff8df78157d0", "wy": "0090ba4770b1f1f2105fe3ed29fb793aaf6fe220c630b4fefa096460eccb31153f1dc6696f650d95e85681f229f30727f7"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040d0c15cb6cd38bea588f92c654f2c5b36d3fe1db8cae4f69dcdbeb51084e0ccadeb17990c1be62a55a32ff8df78157d090ba4770b1f1f2105fe3ed29fb793aaf6fe220c630b4fefa096460eccb31153f1dc6696f650d95e85681f229f30727f7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEDQwVy2zTi+pYj5LGVPLFs20/4duMrk9p\n3NvrUQhODMresXmQwb5ipVoy/433gVfQkLpHcLHx8hBf4+0p+3k6r2/iIMYwtP76\nCWRg7MsxFT8dxmlvZQ2V6FaB8inzByf3\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "u1 == 1", "msg": "************", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec63260230342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed28", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e3d48751eb34c96700557036cc7cbf49dba0b6761c5f82dfe122951aed52045612ec97f4a1fdef3105bf720051bf0c788fb468400d982bd9dbdf9a44b1f383eafc6420da722788085a5745baa750164628c1d7e7b7525fbbdd93d9308ef49ae9", "wx": "00e3d48751eb34c96700557036cc7cbf49dba0b6761c5f82dfe122951aed52045612ec97f4a1fdef3105bf720051bf0c78", "wy": "008fb468400d982bd9dbdf9a44b1f383eafc6420da722788085a5745baa750164628c1d7e7b7525fbbdd93d9308ef49ae9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e3d48751eb34c96700557036cc7cbf49dba0b6761c5f82dfe122951aed52045612ec97f4a1fdef3105bf720051bf0c788fb468400d982bd9dbdf9a44b1f383eafc6420da722788085a5745baa750164628c1d7e7b7525fbbdd93d9308ef49ae9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE49SHUes0yWcAVXA2zHy/SdugtnYcX4Lf\n4SKVGu1SBFYS7Jf0of3vMQW/cgBRvwx4j7RoQA2YK9nb35pEsfOD6vxkINpyJ4gI\nWldFuqdQFkYowdfnt1Jfu92T2TCO9Jrp\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "u1 == n - 1", "msg": "************", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100cbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede05a3783e9fef3c1bb2aa85d6b0a80a5a6062306811743c4b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048f326a3e9ee7e1d0927439d925cefeb8e1165029930ba125c309788adfd1dec07834392ec8f8cbcbd8f217e203082d0d5b6d713a80d496832e65d75936d4407524470b5f74304b075ea9484d18537af60054e620bd44fe4570a7bfe4242e9252", "wx": "008f326a3e9ee7e1d0927439d925cefeb8e1165029930ba125c309788adfd1dec07834392ec8f8cbcbd8f217e203082d0d", "wy": "5b6d713a80d496832e65d75936d4407524470b5f74304b075ea9484d18537af60054e620bd44fe4570a7bfe4242e9252"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048f326a3e9ee7e1d0927439d925cefeb8e1165029930ba125c309788adfd1dec07834392ec8f8cbcbd8f217e203082d0d5b6d713a80d496832e65d75936d4407524470b5f74304b075ea9484d18537af60054e620bd44fe4570a7bfe4242e9252", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEjzJqPp7n4dCSdDnZJc7+uOEWUCmTC6El\nwwl4it/R3sB4NDkuyPjLy9jyF+IDCC0NW21xOoDUloMuZddZNtRAdSRHC190MEsH\nXqlITRhTevYAVOYgvUT+RXCnv+QkLpJS\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "u2 == 1", "msg": "************", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04289ad7ccd69eac6bb4f5f104fa8832159c25fdbedc2dc0e010bfb408923f5e22cd7c78886e1d1918a7fcdb04bcc1dcc5a0e14f606516353edcd0ec7a0be00da3bbeb465fae7af639ee01472977b898db57543460a46b3f9044f812194622b59e", "wx": "289ad7ccd69eac6bb4f5f104fa8832159c25fdbedc2dc0e010bfb408923f5e22cd7c78886e1d1918a7fcdb04bcc1dcc5", "wy": "00a0e14f606516353edcd0ec7a0be00da3bbeb465fae7af639ee01472977b898db57543460a46b3f9044f812194622b59e"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004289ad7ccd69eac6bb4f5f104fa8832159c25fdbedc2dc0e010bfb408923f5e22cd7c78886e1d1918a7fcdb04bcc1dcc5a0e14f606516353edcd0ec7a0be00da3bbeb465fae7af639ee01472977b898db57543460a46b3f9044f812194622b59e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKJrXzNaerGu09fEE+ogyFZwl/b7cLcDg\nEL+0CJI/XiLNfHiIbh0ZGKf82wS8wdzFoOFPYGUWNT7c0Ox6C+ANo7vrRl+uevY5\n7gFHKXe4mNtXVDRgpGs/kET4EhlGIrWe\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "u2 == n - 1", "msg": "************", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa84ecde56a2cf73ea3abc092185cb1a51f34810f1ddd8c64d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0493fdc301fd5497c6bdf34008e44b544a4491c53fd03e68f730f2e0a8a696665425a6e18efaa72add19d7040f6daea9fc021ebf043a0dbe24c71741975a45f15991aa0376bfeef5c7dec3c5121f98a464097ede93dab4e46205a4932677d52d86", "wx": "0093fdc301fd5497c6bdf34008e44b544a4491c53fd03e68f730f2e0a8a696665425a6e18efaa72add19d7040f6daea9fc", "wy": "021ebf043a0dbe24c71741975a45f15991aa0376bfeef5c7dec3c5121f98a464097ede93dab4e46205a4932677d52d86"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000493fdc301fd5497c6bdf34008e44b544a4491c53fd03e68f730f2e0a8a696665425a6e18efaa72add19d7040f6daea9fc021ebf043a0dbe24c71741975a45f15991aa0376bfeef5c7dec3c5121f98a464097ede93dab4e46205a4932677d52d86", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEk/3DAf1Ul8a980AI5EtUSkSRxT/QPmj3\nMPLgqKaWZlQlpuGO+qcq3RnXBA9trqn8Ah6/BDoNviTHF0GXWkXxWZGqA3a/7vXH\n3sPFEh+YpGQJft6T2rTkYgWkkyZ31S2G\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023066b9e4d1b3768bee2b2defbc0e6911a38e0c774b97f62060830bb641c2d50a8ba9d8872f4ae86c33d327562482b20789", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0460cca8bb9e1bfaa97a3be18893c7043a77712deee44b913c939ee4b9601157feb515208125fc7655e0fb359307e0cc3fa4e53d3e06bd1002a2678182a95189c4890fe6ff1d4ed9759f4690ca373a766fbe4caaf69f85d696ac6745da022fc5f7", "wx": "60cca8bb9e1bfaa97a3be18893c7043a77712deee44b913c939ee4b9601157feb515208125fc7655e0fb359307e0cc3f", "wy": "00a4e53d3e06bd1002a2678182a95189c4890fe6ff1d4ed9759f4690ca373a766fbe4caaf69f85d696ac6745da022fc5f7"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000460cca8bb9e1bfaa97a3be18893c7043a77712deee44b913c939ee4b9601157feb515208125fc7655e0fb359307e0cc3fa4e53d3e06bd1002a2678182a95189c4890fe6ff1d4ed9759f4690ca373a766fbe4caaf69f85d696ac6745da022fc5f7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEYMyou54b+ql6O+GIk8cEOndxLe7kS5E8\nk57kuWARV/61FSCBJfx2VeD7NZMH4Mw/pOU9Pga9EAKiZ4GCqVGJxIkP5v8dTtl1\nn0aQyjc6dm++TKr2n4XWlqxnRdoCL8X3\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d3aacc47d028c849c7635156b94bc4aeab9eb29ccaf794fc0261569f9c8912ef6266ed43956849a2e4c0badaf2edc39b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f5f7dafb7cb762f4a86d038828add9f5a1bcb59421aaffe01f5b2b7009acc3a097ac12b792b149c36878f8e0fd9ff441db91e4fee136dbd5d7f2404a42d9548c2edfeefde40af2262fa1e52553485c9ff689f3820d8f075387225e782077d485", "wx": "00f5f7dafb7cb762f4a86d038828add9f5a1bcb59421aaffe01f5b2b7009acc3a097ac12b792b149c36878f8e0fd9ff441", "wy": "00db91e4fee136dbd5d7f2404a42d9548c2edfeefde40af2262fa1e52553485c9ff689f3820d8f075387225e782077d485"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f5f7dafb7cb762f4a86d038828add9f5a1bcb59421aaffe01f5b2b7009acc3a097ac12b792b149c36878f8e0fd9ff441db91e4fee136dbd5d7f2404a42d9548c2edfeefde40af2262fa1e52553485c9ff689f3820d8f075387225e782077d485", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE9ffa+3y3YvSobQOIKK3Z9aG8tZQhqv/g\nH1srcAmsw6CXrBK3krFJw2h4+OD9n/RB25Hk/uE229XX8kBKQtlUjC7f7v3kCvIm\nL6HlJVNIXJ/2ifOCDY8HU4ciXnggd9SF\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100f265d814894ace80d5706d12daf316f3450758b9c581286b807c89fd36449c04baef8ebe2bc988825696a1470589a52e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0484481d7dba583052bcd1586c4a99f3e21b4eb9df66381981c8343289d28052a2d50b41932d209b93c7ac3540b595b0481b6b0510ae966673280f1f7d12d5a66a2c69762c50821c191e93e906fa4ec44588ae8fe8f1769660308268f5df019f1f", "wx": "0084481d7dba583052bcd1586c4a99f3e21b4eb9df66381981c8343289d28052a2d50b41932d209b93c7ac3540b595b048", "wy": "1b6b0510ae966673280f1f7d12d5a66a2c69762c50821c191e93e906fa4ec44588ae8fe8f1769660308268f5df019f1f"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000484481d7dba583052bcd1586c4a99f3e21b4eb9df66381981c8343289d28052a2d50b41932d209b93c7ac3540b595b0481b6b0510ae966673280f1f7d12d5a66a2c69762c50821c191e93e906fa4ec44588ae8fe8f1769660308268f5df019f1f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEhEgdfbpYMFK80VhsSpnz4htOud9mOBmB\nyDQyidKAUqLVC0GTLSCbk8esNUC1lbBIG2sFEK6WZnMoDx99EtWmaixpdixQghwZ\nHpPpBvpOxEWIro/o8XaWYDCCaPXfAZ8f\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100cbeff5c13e09b437cbd2518ae59c5c357e7630cbd4c4cb1528c147efee6b14dcbc0fafcf1c116dfa4ddd7a042a79da3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04be352e662168556652a4e25acbb593c3898d323ed4c9dfb00fbeed0fd57601d910d226facf06c13b64203f21a6c1f8d0cbbdb29fd0413bd92f9376ee5848b90be84fa9b1d5eb5e699120036fd5c6df0d721e39752fb01848a3721c01c3631981", "wx": "00be352e662168556652a4e25acbb593c3898d323ed4c9dfb00fbeed0fd57601d910d226facf06c13b64203f21a6c1f8d0", "wy": "00cbbdb29fd0413bd92f9376ee5848b90be84fa9b1d5eb5e699120036fd5c6df0d721e39752fb01848a3721c01c3631981"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004be352e662168556652a4e25acbb593c3898d323ed4c9dfb00fbeed0fd57601d910d226facf06c13b64203f21a6c1f8d0cbbdb29fd0413bd92f9376ee5848b90be84fa9b1d5eb5e699120036fd5c6df0d721e39752fb01848a3721c01c3631981", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvjUuZiFoVWZSpOJay7WTw4mNMj7Uyd+w\nD77tD9V2AdkQ0ib6zwbBO2QgPyGmwfjQy72yn9BBO9kvk3buWEi5C+hPqbHV615p\nkSADb9XG3w1yHjl1L7AYSKNyHAHDYxmB\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100ff5c13e09b437cbd2518ae59c5c357e7630cbd4c4cb1555d71296100da3347dc5f026c06dbe99486ef3463ca655d88a6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e1eb96d55fc336b196fee593d9d42e0c52ed45ab2a90d9a07724f6efbd4d51263d2f6e1d8450ede4aee61a86bb7c0ae88c5b22c8c7bd73177963dcac910f2f184131b7968914bddcae43812bd3b66a7aeeedcfccacf02b9854b81c1d54ef98da", "wx": "00e1eb96d55fc336b196fee593d9d42e0c52ed45ab2a90d9a07724f6efbd4d51263d2f6e1d8450ede4aee61a86bb7c0ae8", "wy": "008c5b22c8c7bd73177963dcac910f2f184131b7968914bddcae43812bd3b66a7aeeedcfccacf02b9854b81c1d54ef98da"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e1eb96d55fc336b196fee593d9d42e0c52ed45ab2a90d9a07724f6efbd4d51263d2f6e1d8450ede4aee61a86bb7c0ae88c5b22c8c7bd73177963dcac910f2f184131b7968914bddcae43812bd3b66a7aeeedcfccacf02b9854b81c1d54ef98da", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE4euW1V/DNrGW/uWT2dQuDFLtRasqkNmg\ndyT2771NUSY9L24dhFDt5K7mGoa7fArojFsiyMe9cxd5Y9yskQ8vGEExt5aJFL3c\nrkOBK9O2anru7c/MrPArmFS4HB1U75ja\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100feb827c13686f97a4a315cb38b86afcec6197a989962aabb1aef747fc02f61d965eaca5b6f228192f17cae29fdf5e7d9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b5a2e0ded857a339984938bb7eb311939814a1671bb161bd45fe14e441ff29535e84b6e6a1355176b2d4c90cfa063a57debcd15e16b32a5aa86597310da1b106d8882d9967ff4d58f1dfb4d77c58aec4582ca97f845a2bdcbb200c6f6cbb09c5", "wx": "00b5a2e0ded857a339984938bb7eb311939814a1671bb161bd45fe14e441ff29535e84b6e6a1355176b2d4c90cfa063a57", "wy": "00debcd15e16b32a5aa86597310da1b106d8882d9967ff4d58f1dfb4d77c58aec4582ca97f845a2bdcbb200c6f6cbb09c5"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b5a2e0ded857a339984938bb7eb311939814a1671bb161bd45fe14e441ff29535e84b6e6a1355176b2d4c90cfa063a57debcd15e16b32a5aa86597310da1b106d8882d9967ff4d58f1dfb4d77c58aec4582ca97f845a2bdcbb200c6f6cbb09c5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEtaLg3thXozmYSTi7frMRk5gUoWcbsWG9\nRf4U5EH/KVNehLbmoTVRdrLUyQz6BjpX3rzRXhazKlqoZZcxDaGxBtiILZln/01Y\n8d+013xYrsRYLKl/hFor3LsgDG9suwnF\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02303e09b437cbd2518ae59c5c357e7630cbd4c4cb1555da9a1d2a658646e12f71ed983b89459ac429062d11f6ce0b53b14d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0429e5a700b55ed124ce61db14feb891c75df37797b96d7430b42c0dbc0347acd6dfee873d9eabce23a64ab3bdf7293103254b4fe4502daeb1d261f33ecf1a87e81c19d1fcb3d6c4147e2fbd2fad21ef25a8cbc641809f3505f9a148f65adf694d", "wx": "29e5a700b55ed124ce61db14feb891c75df37797b96d7430b42c0dbc0347acd6dfee873d9eabce23a64ab3bdf7293103", "wy": "254b4fe4502daeb1d261f33ecf1a87e81c19d1fcb3d6c4147e2fbd2fad21ef25a8cbc641809f3505f9a148f65adf694d"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000429e5a700b55ed124ce61db14feb891c75df37797b96d7430b42c0dbc0347acd6dfee873d9eabce23a64ab3bdf7293103254b4fe4502daeb1d261f33ecf1a87e81c19d1fcb3d6c4147e2fbd2fad21ef25a8cbc641809f3505f9a148f65adf694d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKeWnALVe0STOYdsU/riRx13zd5e5bXQw\ntCwNvANHrNbf7oc9nqvOI6ZKs733KTEDJUtP5FAtrrHSYfM+zxqH6BwZ0fyz1sQU\nfi+9L60h7yWoy8ZBgJ81BfmhSPZa32lN\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100ea4d0322e1725250d3a21591af916a45be6105ed6e774b0857fbaba69943dc1f411d9b1c1fb66d2b4b722c09dd32e210", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f88f95de4285d8e1222bb900aebefc42d7639a9c4af0baed4462e050d5395773d8605d3eb7ee3d9ad9c29bf9a50e33e043b63c509f022c7752d18b255834ed0b282d2a06b7587f7838fec43346d686b255742f460eef623d48ce2da5129f53f1", "wx": "00f88f95de4285d8e1222bb900aebefc42d7639a9c4af0baed4462e050d5395773d8605d3eb7ee3d9ad9c29bf9a50e33e0", "wy": "43b63c509f022c7752d18b255834ed0b282d2a06b7587f7838fec43346d686b255742f460eef623d48ce2da5129f53f1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f88f95de4285d8e1222bb900aebefc42d7639a9c4af0baed4462e050d5395773d8605d3eb7ee3d9ad9c29bf9a50e33e043b63c509f022c7752d18b255834ed0b282d2a06b7587f7838fec43346d686b255742f460eef623d48ce2da5129f53f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+I+V3kKF2OEiK7kArr78QtdjmpxK8Lrt\nRGLgUNU5V3PYYF0+t+49mtnCm/mlDjPgQ7Y8UJ8CLHdS0YslWDTtCygtKga3WH94\nOP7EM0bWhrJVdC9GDu9iPUjOLaUSn1Px\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100fac83b5ab08fa2d2263f04e12ee07ab55562a902ec02f649340351c805632f4bc78ee682b97c97c47877e884207044ef", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0458e9de6249a28ee74438064f2c57a5b04139d13d1fe310399c64e886d7817798d88aa6e8017724457fd67b1444271a39b7233c886c537589a6ec877a6140a85926168b2bed502c568810833d47e5a55b6410be70feab39791fb4277f347adbc1", "wx": "58e9de6249a28ee74438064f2c57a5b04139d13d1fe310399c64e886d7817798d88aa6e8017724457fd67b1444271a39", "wy": "00b7233c886c537589a6ec877a6140a85926168b2bed502c568810833d47e5a55b6410be70feab39791fb4277f347adbc1"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000458e9de6249a28ee74438064f2c57a5b04139d13d1fe310399c64e886d7817798d88aa6e8017724457fd67b1444271a39b7233c886c537589a6ec877a6140a85926168b2bed502c568810833d47e5a55b6410be70feab39791fb4277f347adbc1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWOneYkmijudEOAZPLFelsEE50T0f4xA5\nnGTohteBd5jYiqboAXckRX/WexREJxo5tyM8iGxTdYmm7Id6YUCoWSYWiyvtUCxW\niBCDPUflpVtkEL5w/qs5eR+0J380etvB\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02310099461b2e4c897411d4d21043f196ee5c71f388b46809df9f4457974031622353ae418682fdc83b4719c4c3464a1321ea", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a6734ac30bdba9498dea41492041fbfe95b3359c2cad630d4e31d1c0a10cba666738f35de5b7ceb56fd29c08cb5611daaed6a6876ca0e33b7887ea0964681ef703e861337493ebf6d54ce8db740ad207c755668f89dacc36edeae1a5c0036a05", "wx": "00a6734ac30bdba9498dea41492041fbfe95b3359c2cad630d4e31d1c0a10cba666738f35de5b7ceb56fd29c08cb5611da", "wy": "00aed6a6876ca0e33b7887ea0964681ef703e861337493ebf6d54ce8db740ad207c755668f89dacc36edeae1a5c0036a05"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a6734ac30bdba9498dea41492041fbfe95b3359c2cad630d4e31d1c0a10cba666738f35de5b7ceb56fd29c08cb5611daaed6a6876ca0e33b7887ea0964681ef703e861337493ebf6d54ce8db740ad207c755668f89dacc36edeae1a5c0036a05", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEpnNKwwvbqUmN6kFJIEH7/pWzNZwsrWMN\nTjHRwKEMumZnOPNd5bfOtW/SnAjLVhHartamh2yg4zt4h+oJZGge9wPoYTN0k+v2\n1Uzo23QK0gfHVWaPidrMNu3q4aXAA2oF\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u1", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100e5e928c572ce2e1abf3b1865ea62658aaaed4d0e9c0ecf6ee68362e04a1334fd856249c47cac58eaa6a724e96f1cb2df", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0445db79275eefb11d4a0febe14b31c1fb6cc4c6a0aaefabe674ac673e09cc844499d8fda96ce46c59fa43ecb8d3cd115e9116df7b21f6e27e53e6fb2abcb37586f22bdcf02cd5c235d71e083240538079d39ad7946b48d808e32235536a89f58a", "wx": "45db79275eefb11d4a0febe14b31c1fb6cc4c6a0aaefabe674ac673e09cc844499d8fda96ce46c59fa43ecb8d3cd115e", "wy": "009116df7b21f6e27e53e6fb2abcb37586f22bdcf02cd5c235d71e083240538079d39ad7946b48d808e32235536a89f58a"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000445db79275eefb11d4a0febe14b31c1fb6cc4c6a0aaefabe674ac673e09cc844499d8fda96ce46c59fa43ecb8d3cd115e9116df7b21f6e27e53e6fb2abcb37586f22bdcf02cd5c235d71e083240538079d39ad7946b48d808e32235536a89f58a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERdt5J17vsR1KD+vhSzHB+2zExqCq76vm\ndKxnPgnMhESZ2P2pbORsWfpD7LjTzRFekRbfeyH24n5T5vsqvLN1hvIr3PAs1cI1\n1x4IMkBTgHnTmteUa0jYCOMiNVNqifWK\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u1", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307932ec0a44a567406ab836896d798b79a283ac5ce2c09435c03e44fe9b224e025d77c75f15e4c4412b4b50a382c4d297", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04185b65203c076b524d888a491a6e934a33b1fa51e6b7ee5fa3a304f9c1f08991ffb3d4485ebdf0f984b0f45a6ba7e1491599239305c4d9f4cef234ecc6000bfd3d771969a7671d4304b0ec1a1a6154bbe26d99a57b1c5e75e1b5eef7447a8fce", "wx": "185b65203c076b524d888a491a6e934a33b1fa51e6b7ee5fa3a304f9c1f08991ffb3d4485ebdf0f984b0f45a6ba7e149", "wy": "1599239305c4d9f4cef234ecc6000bfd3d771969a7671d4304b0ec1a1a6154bbe26d99a57b1c5e75e1b5eef7447a8fce"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004185b65203c076b524d888a491a6e934a33b1fa51e6b7ee5fa3a304f9c1f08991ffb3d4485ebdf0f984b0f45a6ba7e1491599239305c4d9f4cef234ecc6000bfd3d771969a7671d4304b0ec1a1a6154bbe26d99a57b1c5e75e1b5eef7447a8fce", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEGFtlIDwHa1JNiIpJGm6TSjOx+lHmt+5f\no6ME+cHwiZH/s9RIXr3w+YSw9Fprp+FJFZkjkwXE2fTO8jTsxgAL/T13GWmnZx1D\nBLDsGhphVLvibZmlexxedeG17vdEeo/O\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffed2119d5fc12649fc808af3b6d9037d3a44eb32399970dd0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0458502135c1ced850e12947e2702f4048dede7754d6bef88cb599eb65fb326af1a4008540d682c21ec1469c9fe4801e4288df3893083bdc8824ba709d4247f8a29d1b5135bc11bb42445f4f6d22821024a7cc7975f475a0d5022682855d7c0ab8", "wx": "58502135c1ced850e12947e2702f4048dede7754d6bef88cb599eb65fb326af1a4008540d682c21ec1469c9fe4801e42", "wy": "0088df3893083bdc8824ba709d4247f8a29d1b5135bc11bb42445f4f6d22821024a7cc7975f475a0d5022682855d7c0ab8"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000458502135c1ced850e12947e2702f4048dede7754d6bef88cb599eb65fb326af1a4008540d682c21ec1469c9fe4801e4288df3893083bdc8824ba709d4247f8a29d1b5135bc11bb42445f4f6d22821024a7cc7975f475a0d5022682855d7c0ab8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWFAhNcHO2FDhKUficC9ASN7ed1TWvviM\ntZnrZfsyavGkAIVA1oLCHsFGnJ/kgB5CiN84kwg73IgkunCdQkf4op0bUTW8EbtC\nRF9PbSKCECSnzHl19HWg1QImgoVdfAq4\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023079b95c013b0472de04d8faeec3b779c39fe729ea84fb554cd091c7178c2f054eabbc62c3e1cfbac2c2e69d7aa45d9072", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04eae29d829ad084552675196c6f394556fbd73c7e10a48b8620282d4d3896ee0ab4d09996916c4ddc53a7cd3c3606d3e0ba37a1bc6db922bdd12c34c5d9478016d4d6793ede0aa18bb491564460d2db30c4016e36839ba636fdb908bc54468156", "wx": "00eae29d829ad084552675196c6f394556fbd73c7e10a48b8620282d4d3896ee0ab4d09996916c4ddc53a7cd3c3606d3e0", "wy": "00ba37a1bc6db922bdd12c34c5d9478016d4d6793ede0aa18bb491564460d2db30c4016e36839ba636fdb908bc54468156"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004eae29d829ad084552675196c6f394556fbd73c7e10a48b8620282d4d3896ee0ab4d09996916c4ddc53a7cd3c3606d3e0ba37a1bc6db922bdd12c34c5d9478016d4d6793ede0aa18bb491564460d2db30c4016e36839ba636fdb908bc54468156", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE6uKdgprQhFUmdRlsbzlFVvvXPH4QpIuG\nICgtTTiW7gq00JmWkWxN3FOnzTw2BtPgujehvG25Ir3RLDTF2UeAFtTWeT7eCqGL\ntJFWRGDS2zDEAW42g5umNv25CLxURoFW\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100bfd40d0caa4d9d42381f3d72a25683f52b03a1ed96fb72d03f08dcb9a8bc8f23c1a459deab03bcd39396c0d1e9053c81", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bc31a5f5793a1a3d81bf6dd37a55b13624081691990a5737645ebbe5a8f3921c9eaac0f23b9174c88451eb61c69d0cb883bba1948e818dbba902f5e184a534854c69c881b85f682ac12dc019e1c6d2da742bfccbefc5ed84e95b42930c09fc50", "wx": "00bc31a5f5793a1a3d81bf6dd37a55b13624081691990a5737645ebbe5a8f3921c9eaac0f23b9174c88451eb61c69d0cb8", "wy": "0083bba1948e818dbba902f5e184a534854c69c881b85f682ac12dc019e1c6d2da742bfccbefc5ed84e95b42930c09fc50"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bc31a5f5793a1a3d81bf6dd37a55b13624081691990a5737645ebbe5a8f3921c9eaac0f23b9174c88451eb61c69d0cb883bba1948e818dbba902f5e184a534854c69c881b85f682ac12dc019e1c6d2da742bfccbefc5ed84e95b42930c09fc50", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvDGl9Xk6Gj2Bv23TelWxNiQIFpGZClc3\nZF675ajzkhyeqsDyO5F0yIRR62HGnQy4g7uhlI6BjbupAvXhhKU0hUxpyIG4X2gq\nwS3AGeHG0tp0K/zL78XthOlbQpMMCfxQ\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02304c7d219db9af94ce7fffffffffffffffffffffffffffffffef15cf1058c8d8ba1e634c4122db95ec1facd4bb13ebf09a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04c76cbc996421f5acc36e665635759cdd2d615b4a497bea9c321fded0cbcd3546c8ecb7ab587b9fa4fd5bde6add66ef687ecafe0f227b59c452ee3c77670ad9bfb8b0373821d8c08821ccfcdc6f1938efa612e98411f99f50279f794b59e2b10e", "wx": "00c76cbc996421f5acc36e665635759cdd2d615b4a497bea9c321fded0cbcd3546c8ecb7ab587b9fa4fd5bde6add66ef68", "wy": "7ecafe0f227b59c452ee3c77670ad9bfb8b0373821d8c08821ccfcdc6f1938efa612e98411f99f50279f794b59e2b10e"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004c76cbc996421f5acc36e665635759cdd2d615b4a497bea9c321fded0cbcd3546c8ecb7ab587b9fa4fd5bde6add66ef687ecafe0f227b59c452ee3c77670ad9bfb8b0373821d8c08821ccfcdc6f1938efa612e98411f99f50279f794b59e2b10e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEx2y8mWQh9azDbmZWNXWc3S1hW0pJe+qc\nMh/e0MvNNUbI7LerWHufpP1b3mrdZu9ofsr+DyJ7WcRS7jx3ZwrZv7iwNzgh2MCI\nIcz83G8ZOO+mEumEEfmfUCefeUtZ4rEO\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d219db9af94ce7ffffffffffffffffffffffffffffffffffd189bdb6d9ef7be8504ca374756ea5b8f15e44067d209b9b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ef73af0706ca2030f3b64d23172f6f961fdad25e7b68151b81e59fcb0b46a297786360b85042e69bdb4c21c189d3ff35892c292dcbd50e19905a9b558ee7f441ad00130c7578e13b07831fa13859610c7408974e3b2f42e33c9db2436df40e89", "wx": "00ef73af0706ca2030f3b64d23172f6f961fdad25e7b68151b81e59fcb0b46a297786360b85042e69bdb4c21c189d3ff35", "wy": "00892c292dcbd50e19905a9b558ee7f441ad00130c7578e13b07831fa13859610c7408974e3b2f42e33c9db2436df40e89"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ef73af0706ca2030f3b64d23172f6f961fdad25e7b68151b81e59fcb0b46a297786360b85042e69bdb4c21c189d3ff35892c292dcbd50e19905a9b558ee7f441ad00130c7578e13b07831fa13859610c7408974e3b2f42e33c9db2436df40e89", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE73OvBwbKIDDztk0jFy9vlh/a0l57aBUb\ngeWfywtGopd4Y2C4UELmm9tMIcGJ0/81iSwpLcvVDhmQWptVjuf0Qa0AEwx1eOE7\nB4MfoThZYQx0CJdOOy9C4zydskNt9A6J\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a433b735f299cfffffffffffffffffffffffffffffffffffdbb02debbfa7c9f1487f3936a22ca3f6f5d06ea22d7c0dc3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ecbe4a557d3ae0adaff67f90c5c3b46541fc570954e00487af5e13c9df150807feb76a38ce9a4dd11f847f8db79ed97edeade0c36705e4f117d02ea0966e0fa01d0a31168899cebd32e544dae19bd59486cb10bac77ad90739c1b3a8744076f0", "wx": "00ecbe4a557d3ae0adaff67f90c5c3b46541fc570954e00487af5e13c9df150807feb76a38ce9a4dd11f847f8db79ed97e", "wy": "00deade0c36705e4f117d02ea0966e0fa01d0a31168899cebd32e544dae19bd59486cb10bac77ad90739c1b3a8744076f0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ecbe4a557d3ae0adaff67f90c5c3b46541fc570954e00487af5e13c9df150807feb76a38ce9a4dd11f847f8db79ed97edeade0c36705e4f117d02ea0966e0fa01d0a31168899cebd32e544dae19bd59486cb10bac77ad90739c1b3a8744076f0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE7L5KVX064K2v9n+QxcO0ZUH8VwlU4ASH\nr14Tyd8VCAf+t2o4zppN0R+Ef423ntl+3q3gw2cF5PEX0C6glm4PoB0KMRaImc69\nMuVE2uGb1ZSGyxC6x3rZBznBs6h0QHbw\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b9af94ce7fffffffffffffffffffffffffffffffffffffffd6efeefc876c9f23217b443c80637ef939e911219f96c179", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043be024650ba1e28f3b6ffbbbb7b2fd932d97d966b576c9fa2829147787225d3bebcf6fdf52ceceaa97f3e713321ae05b5202ae4f52a80ae69dbba2a647f0cdee63f132ca4d2a56180e1304b6ea49b800e368380696fca50d2555bb8467010a9a", "wx": "3be024650ba1e28f3b6ffbbbb7b2fd932d97d966b576c9fa2829147787225d3bebcf6fdf52ceceaa97f3e713321ae05b", "wy": "5202ae4f52a80ae69dbba2a647f0cdee63f132ca4d2a56180e1304b6ea49b800e368380696fca50d2555bb8467010a9a"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043be024650ba1e28f3b6ffbbbb7b2fd932d97d966b576c9fa2829147787225d3bebcf6fdf52ceceaa97f3e713321ae05b5202ae4f52a80ae69dbba2a647f0cdee63f132ca4d2a56180e1304b6ea49b800e368380696fca50d2555bb8467010a9a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEO+AkZQuh4o87b/u7t7L9ky2X2Wa1dsn6\nKCkUd4ciXTvrz2/fUs7Oqpfz5xMyGuBbUgKuT1KoCuadu6KmR/DN7mPxMspNKlYY\nDhMEtupJuADjaDgGlvylDSVVu4RnAQqa\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a276276276276276276276276276276276276276276276273d7228d4f84b769be0fd57b97e4c1ebcae9a5f635e80e9df", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04df85a11bb0c9c5f6a4e18b3ddffc6c597fd68319494847fe0034ff8aa1b9df00935d617ebb69d2e6524a856df4333b62231fa6392ec16aa1ec13d144777c6ad0264cbb5020744d1bcf43f4d1c3ad6962d0d41c1f62dc0ba1f36f3d8b5e72e670", "wx": "00df85a11bb0c9c5f6a4e18b3ddffc6c597fd68319494847fe0034ff8aa1b9df00935d617ebb69d2e6524a856df4333b62", "wy": "231fa6392ec16aa1ec13d144777c6ad0264cbb5020744d1bcf43f4d1c3ad6962d0d41c1f62dc0ba1f36f3d8b5e72e670"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004df85a11bb0c9c5f6a4e18b3ddffc6c597fd68319494847fe0034ff8aa1b9df00935d617ebb69d2e6524a856df4333b62231fa6392ec16aa1ec13d144777c6ad0264cbb5020744d1bcf43f4d1c3ad6962d0d41c1f62dc0ba1f36f3d8b5e72e670", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE34WhG7DJxfak4Ys93/xsWX/WgxlJSEf+\nADT/iqG53wCTXWF+u2nS5lJKhW30MztiIx+mOS7BaqHsE9FEd3xq0CZMu1AgdE0b\nz0P00cOtaWLQ1BwfYtwLofNvPYtecuZw\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023073333333333333333333333333333333333333333333333316e4d9f42d4eca22df403a0c578b86f0a9a93fe89995c7ed", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046efba105b9e276b04ecf896e141ff172027b1bbb18bab6c29c1db29dcfe7a912fbb28feac2346371811b79a03459e19db04f05153092410e09f8b52da437051112e951d982862e0a5ea61e8c41a407db2d00a7289d3531193ab37f6f8ef4e7b5", "wx": "6efba105b9e276b04ecf896e141ff172027b1bbb18bab6c29c1db29dcfe7a912fbb28feac2346371811b79a03459e19d", "wy": "00b04f05153092410e09f8b52da437051112e951d982862e0a5ea61e8c41a407db2d00a7289d3531193ab37f6f8ef4e7b5"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046efba105b9e276b04ecf896e141ff172027b1bbb18bab6c29c1db29dcfe7a912fbb28feac2346371811b79a03459e19db04f05153092410e09f8b52da437051112e951d982862e0a5ea61e8c41a407db2d00a7289d3531193ab37f6f8ef4e7b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEbvuhBbnidrBOz4luFB/xcgJ7G7sYurbC\nnB2ync/nqRL7so/qwjRjcYEbeaA0WeGdsE8FFTCSQQ4J+LUtpDcFERLpUdmChi4K\nXqYejEGkB9stAKconTUxGTqzf2+O9Oe1\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffda4233abf824c93f90115e76db206fa7489d6647332e1ba3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a82be5ec5f9deed5ca6d104480abb903ca317671e76e232a900ee2bf2474b097ade520837168a4ed5cec6dbfcc0ea0ffbbf3498d2b5fe438b2ff9f450c0f6db2c20c63ca120278f97cc8c188afa653914a6e40fc9299c578e1d27453eaf77e0c", "wx": "00a82be5ec5f9deed5ca6d104480abb903ca317671e76e232a900ee2bf2474b097ade520837168a4ed5cec6dbfcc0ea0ff", "wy": "00bbf3498d2b5fe438b2ff9f450c0f6db2c20c63ca120278f97cc8c188afa653914a6e40fc9299c578e1d27453eaf77e0c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a82be5ec5f9deed5ca6d104480abb903ca317671e76e232a900ee2bf2474b097ade520837168a4ed5cec6dbfcc0ea0ffbbf3498d2b5fe438b2ff9f450c0f6db2c20c63ca120278f97cc8c188afa653914a6e40fc9299c578e1d27453eaf77e0c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqCvl7F+d7tXKbRBEgKu5A8oxdnHnbiMq\nkA7ivyR0sJet5SCDcWik7Vzsbb/MDqD/u/NJjStf5Diy/59FDA9tssIMY8oSAnj5\nfMjBiK+mU5FKbkD8kpnFeOHSdFPq934M\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "edge case for u2", "msg": "************", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02303fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0445a99ab80d9721656e0097ec2929fb96318e99759e1559fc754e74c6d240ff1495ac899ef6f6c1f434e39eff0cbabfa005c6b0f31de5b9bfa20ea28a9070a6f3322908d5b0bfc0d7d3a8cdfa9e93b653b4768b454011beff9bb9bbb838162bc9", "wx": "45a99ab80d9721656e0097ec2929fb96318e99759e1559fc754e74c6d240ff1495ac899ef6f6c1f434e39eff0cbabfa0", "wy": "05c6b0f31de5b9bfa20ea28a9070a6f3322908d5b0bfc0d7d3a8cdfa9e93b653b4768b454011beff9bb9bbb838162bc9"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000445a99ab80d9721656e0097ec2929fb96318e99759e1559fc754e74c6d240ff1495ac899ef6f6c1f434e39eff0cbabfa005c6b0f31de5b9bfa20ea28a9070a6f3322908d5b0bfc0d7d3a8cdfa9e93b653b4768b454011beff9bb9bbb838162bc9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERamauA2XIWVuAJfsKSn7ljGOmXWeFVn8\ndU50xtJA/xSVrIme9vbB9DTjnv8Mur+gBcaw8x3lub+iDqKKkHCm8zIpCNWwv8DX\n06jN+p6TtlO0dotFQBG+/5u5u7g4FivJ\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "edge case for u2", "msg": "************", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100dfea06865526cea11c0f9eb9512b41fa9581d0f6cb7db9680336151dce79de818cdf33c879da322740416d1e5ae532fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb542766fa8a382f21dff2c3803018c94b52ecd28b7ee04f76297026ab13707ff68afd98b39d431c46b13ef3cea81b4158770", "wx": "00bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb542", "wy": "766fa8a382f21dff2c3803018c94b52ecd28b7ee04f76297026ab13707ff68afd98b39d431c46b13ef3cea81b4158770"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb542766fa8a382f21dff2c3803018c94b52ecd28b7ee04f76297026ab13707ff68afd98b39d431c46b13ef3cea81b4158770", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvUQXEji9Zt89QQZp2WH9zUzw1jXzDKaj\n5CKAU+8KrKoky1WJCUXsWlfz6P8hz7VCdm+oo4LyHf8sOAMBjJS1Ls0ot+4E92KX\nAmqxNwf/aK/ZiznUMcRrE+886oG0FYdw\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "point duplication during verification", "msg": "************", "sig": "3065023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce600230213963af02bb080baaa19382bc09cc2ec5d8692eaecd7a5b0fe49250ea46ebe897ff62811de1980ee73b0e4354a15e62", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb5428990575c7d0de200d3c7fcfe736b4ad132d74811fb089d68fd954ec8f800974f2674c62ace3b94ec10c3157f4bea788f", "wx": "00bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb542", "wy": "008990575c7d0de200d3c7fcfe736b4ad132d74811fb089d68fd954ec8f800974f2674c62ace3b94ec10c3157f4bea788f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bd44171238bd66df3d410669d961fdcd4cf0d635f30ca6a3e4228053ef0aacaa24cb55890945ec5a57f3e8ff21cfb5428990575c7d0de200d3c7fcfe736b4ad132d74811fb089d68fd954ec8f800974f2674c62ace3b94ec10c3157f4bea788f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvUQXEji9Zt89QQZp2WH9zUzw1jXzDKaj\n5CKAU+8KrKoky1WJCUXsWlfz6P8hz7VCiZBXXH0N4gDTx/z+c2tK0TLXSBH7CJ1o\n/ZVOyPgAl08mdMYqzjuU7BDDFX9L6niP\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "duplication bug", "msg": "************", "sig": "3065023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce600230213963af02bb080baaa19382bc09cc2ec5d8692eaecd7a5b0fe49250ea46ebe897ff62811de1980ee73b0e4354a15e62", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e3be38a2438036a73738ebc1babd703a9d001b681f069ba101037e5874cbf49b9aa5a0e114e89ad7135bc0975d47c891c206de0358a77eda6b6c8ebd0ba5af1d3fe8ff1d8e18841a9ed9adaa7c13d430531e36311a559a680f0fe7afaa7fa2fe", "wx": "00e3be38a2438036a73738ebc1babd703a9d001b681f069ba101037e5874cbf49b9aa5a0e114e89ad7135bc0975d47c891", "wy": "00c206de0358a77eda6b6c8ebd0ba5af1d3fe8ff1d8e18841a9ed9adaa7c13d430531e36311a559a680f0fe7afaa7fa2fe"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e3be38a2438036a73738ebc1babd703a9d001b681f069ba101037e5874cbf49b9aa5a0e114e89ad7135bc0975d47c891c206de0358a77eda6b6c8ebd0ba5af1d3fe8ff1d8e18841a9ed9adaa7c13d430531e36311a559a680f0fe7afaa7fa2fe", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE4744okOANqc3OOvBur1wOp0AG2gfBpuh\nAQN+WHTL9JuapaDhFOia1xNbwJddR8iRwgbeA1inftprbI69C6WvHT/o/x2OGIQa\nntmtqnwT1DBTHjYxGlWaaA8P56+qf6L+\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "point with x-coordinate 0", "msg": "************", "sig": "3035020101023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0433a24f4337d8a02e4a1ac37befe6ec55169baecd409cb75e2980281d07cc12081de5d8602be39dddd1a2281785b15c329e9dc79417c0ea1958b9ec8073ce60eb159f6aec9762496ff0416013d14d77de9c9bd215b396253704195aaa2e5ad174", "wx": "33a24f4337d8a02e4a1ac37befe6ec55169baecd409cb75e2980281d07cc12081de5d8602be39dddd1a2281785b15c32", "wy": "009e9dc79417c0ea1958b9ec8073ce60eb159f6aec9762496ff0416013d14d77de9c9bd215b396253704195aaa2e5ad174"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000433a24f4337d8a02e4a1ac37befe6ec55169baecd409cb75e2980281d07cc12081de5d8602be39dddd1a2281785b15c329e9dc79417c0ea1958b9ec8073ce60eb159f6aec9762496ff0416013d14d77de9c9bd215b396253704195aaa2e5ad174", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEM6JPQzfYoC5KGsN77+bsVRabrs1AnLde\nKYAoHQfMEggd5dhgK+Od3dGiKBeFsVwynp3HlBfA6hlYueyAc85g6xWfauyXYklv\n8EFgE9FNd96cm9IVs5YlNwQZWqouWtF0\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "point with x-coordinate 0", "msg": "************", "sig": "3065023101000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bcc18cbc9dadb319cbb464a6bf13c3fc23ffe396295e0c6629d6d423a95a55e9962d95bac76e5cec92be75f7400becd701d4032a396acd89fd62df36ca522bbf580a870f65d40eac8b734a1e645873f554888ae17e69c61bb0e0c85d38fbb78f", "wx": "00bcc18cbc9dadb319cbb464a6bf13c3fc23ffe396295e0c6629d6d423a95a55e9962d95bac76e5cec92be75f7400becd7", "wy": "01d4032a396acd89fd62df36ca522bbf580a870f65d40eac8b734a1e645873f554888ae17e69c61bb0e0c85d38fbb78f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bcc18cbc9dadb319cbb464a6bf13c3fc23ffe396295e0c6629d6d423a95a55e9962d95bac76e5cec92be75f7400becd701d4032a396acd89fd62df36ca522bbf580a870f65d40eac8b734a1e645873f554888ae17e69c61bb0e0c85d38fbb78f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvMGMvJ2tsxnLtGSmvxPD/CP/45YpXgxm\nKdbUI6laVemWLZW6x25c7JK+dfdAC+zXAdQDKjlqzYn9Yt82ylIrv1gKhw9l1A6s\ni3NKHmRYc/VUiIrhfmnGG7DgyF04+7eP\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "comparison with point at infinity ", "msg": "************", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043674230033c5da4bba115c351cf4544b97c76800938af5d76d1393ee2541b5d318c305d6f3f60fbd4840cd3e357b43b69f79b78bce247f5800af3504797cc14b15d5d4213106b5c7ab83e4a7d5f6dff31ecc12ab18caaad8c02ebb234215bd30", "wx": "3674230033c5da4bba115c351cf4544b97c76800938af5d76d1393ee2541b5d318c305d6f3f60fbd4840cd3e357b43b6", "wy": "009f79b78bce247f5800af3504797cc14b15d5d4213106b5c7ab83e4a7d5f6dff31ecc12ab18caaad8c02ebb234215bd30"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043674230033c5da4bba115c351cf4544b97c76800938af5d76d1393ee2541b5d318c305d6f3f60fbd4840cd3e357b43b69f79b78bce247f5800af3504797cc14b15d5d4213106b5c7ab83e4a7d5f6dff31ecc12ab18caaad8c02ebb234215bd30", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENnQjADPF2ku6EVw1HPRUS5fHaACTivXX\nbROT7iVBtdMYwwXW8/YPvUhAzT41e0O2n3m3i84kf1gArzUEeXzBSxXV1CExBrXH\nq4Pkp9X23/MezBKrGMqq2MAuuyNCFb0w\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ac7d897288e85320e94487ec773f2e9c421657227e742dfc9068273826bcad1a9aef4bc63ea74d112ecab091e51392097e797b58408de6b5f9adb46ec302e9e373887934e5a79ff5db51188b997cd250935b505662b66abcc726be81446bccea", "wx": "00ac7d897288e85320e94487ec773f2e9c421657227e742dfc9068273826bcad1a9aef4bc63ea74d112ecab091e5139209", "wy": "7e797b58408de6b5f9adb46ec302e9e373887934e5a79ff5db51188b997cd250935b505662b66abcc726be81446bccea"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ac7d897288e85320e94487ec773f2e9c421657227e742dfc9068273826bcad1a9aef4bc63ea74d112ecab091e51392097e797b58408de6b5f9adb46ec302e9e373887934e5a79ff5db51188b997cd250935b505662b66abcc726be81446bccea", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErH2JcojoUyDpRIfsdz8unEIWVyJ+dC38\nkGgnOCa8rRqa70vGPqdNES7KsJHlE5IJfnl7WECN5rX5rbRuwwLp43OIeTTlp5/1\n21EYi5l80lCTW1BWYrZqvMcmvoFEa8zq\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046ccf7c5d5a5ca8223535dcb03823673058c686cd9560ed2c094f3ede52dd0de59e5edfcfb829b0a95f939234c931c361b06d1b75f60f6c9de67548dcf100b77e6a667d7b6bec104a1cb86bfd4398127ce9ed50a3756ef7cbafb68c01e37349f1", "wx": "6ccf7c5d5a5ca8223535dcb03823673058c686cd9560ed2c094f3ede52dd0de59e5edfcfb829b0a95f939234c931c361", "wy": "00b06d1b75f60f6c9de67548dcf100b77e6a667d7b6bec104a1cb86bfd4398127ce9ed50a3756ef7cbafb68c01e37349f1"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046ccf7c5d5a5ca8223535dcb03823673058c686cd9560ed2c094f3ede52dd0de59e5edfcfb829b0a95f939234c931c361b06d1b75f60f6c9de67548dcf100b77e6a667d7b6bec104a1cb86bfd4398127ce9ed50a3756ef7cbafb68c01e37349f1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEbM98XVpcqCI1NdywOCNnMFjGhs2VYO0s\nCU8+3lLdDeWeXt/PuCmwqV+TkjTJMcNhsG0bdfYPbJ3mdUjc8QC3fmpmfXtr7BBK\nHLhr/UOYEnzp7VCjdW73y6+2jAHjc0nx\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045aea5b66a22e9ad05ed948050b7478d66d52bf5c8ae1b037bd35dbe36f90651068b929e294bad3852877f9ed8b56084c88c0ff92b7bbe5a36cad9a31baf4a8da756e7b13169d1b2c2177e0c119c91ceddd8414a5d29d862f12bf1816636514d4", "wx": "5aea5b66a22e9ad05ed948050b7478d66d52bf5c8ae1b037bd35dbe36f90651068b929e294bad3852877f9ed8b56084c", "wy": "0088c0ff92b7bbe5a36cad9a31baf4a8da756e7b13169d1b2c2177e0c119c91ceddd8414a5d29d862f12bf1816636514d4"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045aea5b66a22e9ad05ed948050b7478d66d52bf5c8ae1b037bd35dbe36f90651068b929e294bad3852877f9ed8b56084c88c0ff92b7bbe5a36cad9a31baf4a8da756e7b13169d1b2c2177e0c119c91ceddd8414a5d29d862f12bf1816636514d4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWupbZqIumtBe2UgFC3R41m1Sv1yK4bA3\nvTXb42+QZRBouSnilLrThSh3+e2LVghMiMD/kre75aNsrZoxuvSo2nVuexMWnRss\nIXfgwRnJHO3dhBSl0p2GLxK/GBZjZRTU\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044dfca708a087c8165edcc21010dbc83890e895a61bca93ebb32913b7c1bab5f13794978c0a8f437f9a193f3d49e87c99968d673558b512e86a3c04557cda68deff647165226ecde4a53eff3b3036e88259982cda28365608064d14e7d067740b", "wx": "4dfca708a087c8165edcc21010dbc83890e895a61bca93ebb32913b7c1bab5f13794978c0a8f437f9a193f3d49e87c99", "wy": "00968d673558b512e86a3c04557cda68deff647165226ecde4a53eff3b3036e88259982cda28365608064d14e7d067740b"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044dfca708a087c8165edcc21010dbc83890e895a61bca93ebb32913b7c1bab5f13794978c0a8f437f9a193f3d49e87c99968d673558b512e86a3c04557cda68deff647165226ecde4a53eff3b3036e88259982cda28365608064d14e7d067740b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAETfynCKCHyBZe3MIQENvIOJDolaYbypPr\nsykTt8G6tfE3lJeMCo9Df5oZPz1J6HyZlo1nNVi1EuhqPARVfNpo3v9kcWUibs3k\npT7/OzA26IJZmCzaKDZWCAZNFOfQZ3QL\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0407dc1276082eabff63a3b967d64157e68d8ae79491ae90d43a79c1548ce1cbfb48088e2b256ee8a4d6466d676eb82a3d14566877ffa534182760363c94cab0d4ad8d3a3fac2d78b98dc65f8a78b5484696a534d0894a029e70cdcbc1c6ce2e54", "wx": "07dc1276082eabff63a3b967d64157e68d8ae79491ae90d43a79c1548ce1cbfb48088e2b256ee8a4d6466d676eb82a3d", "wy": "14566877ffa534182760363c94cab0d4ad8d3a3fac2d78b98dc65f8a78b5484696a534d0894a029e70cdcbc1c6ce2e54"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000407dc1276082eabff63a3b967d64157e68d8ae79491ae90d43a79c1548ce1cbfb48088e2b256ee8a4d6466d676eb82a3d14566877ffa534182760363c94cab0d4ad8d3a3fac2d78b98dc65f8a78b5484696a534d0894a029e70cdcbc1c6ce2e54", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEB9wSdgguq/9jo7ln1kFX5o2K55SRrpDU\nOnnBVIzhy/tICI4rJW7opNZGbWduuCo9FFZod/+lNBgnYDY8lMqw1K2NOj+sLXi5\njcZfini1SEaWpTTQiUoCnnDNy8HGzi5U\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k", "msg": "************", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a82306a028e2fb16f5b6eaf6fc96d3430104486a35d25a35e66522a5f68f53e4c6def3893009acd8c75abbdcdd93acf5b6eada815252a46fd996d0fe4a735493cdea3a0a9f617ff9469df8c1b64ce2d95fd132ef398de3014263e95fed4fb213", "wx": "00a82306a028e2fb16f5b6eaf6fc96d3430104486a35d25a35e66522a5f68f53e4c6def3893009acd8c75abbdcdd93acf5", "wy": "00b6eada815252a46fd996d0fe4a735493cdea3a0a9f617ff9469df8c1b64ce2d95fd132ef398de3014263e95fed4fb213"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a82306a028e2fb16f5b6eaf6fc96d3430104486a35d25a35e66522a5f68f53e4c6def3893009acd8c75abbdcdd93acf5b6eada815252a46fd996d0fe4a735493cdea3a0a9f617ff9469df8c1b64ce2d95fd132ef398de3014263e95fed4fb213", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqCMGoCji+xb1tur2/JbTQwEESGo10lo1\n5mUipfaPU+TG3vOJMAms2Mdau9zdk6z1turagVJSpG/ZltD+SnNUk83qOgqfYX/5\nRp34wbZM4tlf0TLvOY3jAUJj6V/tT7IT\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b425811b3bc72bac8bb8b031e9d9c36c550a70dad6e5245e22f0b8c42c56f0a628419d04f3e89879f261ccf1006af569ec26b6d4eee78d11140e5bf311e7dc4def75f1cbde623bc2a5462d50cb478b61e9dcca74777cf260bc8ef8b7cdad5e08", "wx": "00b425811b3bc72bac8bb8b031e9d9c36c550a70dad6e5245e22f0b8c42c56f0a628419d04f3e89879f261ccf1006af569", "wy": "00ec26b6d4eee78d11140e5bf311e7dc4def75f1cbde623bc2a5462d50cb478b61e9dcca74777cf260bc8ef8b7cdad5e08"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b425811b3bc72bac8bb8b031e9d9c36c550a70dad6e5245e22f0b8c42c56f0a628419d04f3e89879f261ccf1006af569ec26b6d4eee78d11140e5bf311e7dc4def75f1cbde623bc2a5462d50cb478b61e9dcca74777cf260bc8ef8b7cdad5e08", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEtCWBGzvHK6yLuLAx6dnDbFUKcNrW5SRe\nIvC4xCxW8KYoQZ0E8+iYefJhzPEAavVp7Ca21O7njREUDlvzEefcTe918cveYjvC\npUYtUMtHi2Hp3Mp0d3zyYLyO+LfNrV4I\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045b031a8020ddb66a43f05f3085e333101f5fbac8737a867f6e83efd108cb2caeefd1210494bdf774c788eec1be43d60619a6290c3ccef1dcb3bac46556460254f5079f43bd5470d366770bdd9048c3c3109b67438adcb965d6a695d2a16335d2", "wx": "5b031a8020ddb66a43f05f3085e333101f5fbac8737a867f6e83efd108cb2caeefd1210494bdf774c788eec1be43d606", "wy": "19a6290c3ccef1dcb3bac46556460254f5079f43bd5470d366770bdd9048c3c3109b67438adcb965d6a695d2a16335d2"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045b031a8020ddb66a43f05f3085e333101f5fbac8737a867f6e83efd108cb2caeefd1210494bdf774c788eec1be43d60619a6290c3ccef1dcb3bac46556460254f5079f43bd5470d366770bdd9048c3c3109b67438adcb965d6a695d2a16335d2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWwMagCDdtmpD8F8wheMzEB9fushzeoZ/\nboPv0QjLLK7v0SEElL33dMeI7sG+Q9YGGaYpDDzO8dyzusRlVkYCVPUHn0O9VHDT\nZncL3ZBIw8MQm2dDity5ZdamldKhYzXS\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e17bc4a1caa870988f63da22635337513f59c055fb7dc873eba0df125dff66485d87bef520ebd4c576c0adf37db80d5ba6f702b447a50c9fc3cc87dd0f6d9a1707b5ebcb80dd3c0bf891bdedb1f235ab33c3f3e4ef7334e384a24efd503a0924", "wx": "00e17bc4a1caa870988f63da22635337513f59c055fb7dc873eba0df125dff66485d87bef520ebd4c576c0adf37db80d5b", "wy": "00a6f702b447a50c9fc3cc87dd0f6d9a1707b5ebcb80dd3c0bf891bdedb1f235ab33c3f3e4ef7334e384a24efd503a0924"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e17bc4a1caa870988f63da22635337513f59c055fb7dc873eba0df125dff66485d87bef520ebd4c576c0adf37db80d5ba6f702b447a50c9fc3cc87dd0f6d9a1707b5ebcb80dd3c0bf891bdedb1f235ab33c3f3e4ef7334e384a24efd503a0924", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE4XvEocqocJiPY9oiY1M3UT9ZwFX7fchz\n66DfEl3/Zkhdh771IOvUxXbArfN9uA1bpvcCtEelDJ/DzIfdD22aFwe168uA3TwL\n+JG97bHyNaszw/Pk73M044SiTv1QOgkk\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 422, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04051590a4cbe93ae21dc6f272d5376b240e229117b35f9cb892fb6a7ee00c567eb5b7e951af15251733fbf83e3b4a2ca0af4e5197d271ad16eb6561c186013a577bd17f399bfa20c908237c7a784d5fc3dd98665f20f85fecaf53ac79ec7f0017", "wx": "051590a4cbe93ae21dc6f272d5376b240e229117b35f9cb892fb6a7ee00c567eb5b7e951af15251733fbf83e3b4a2ca0", "wy": "00af4e5197d271ad16eb6561c186013a577bd17f399bfa20c908237c7a784d5fc3dd98665f20f85fecaf53ac79ec7f0017"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004051590a4cbe93ae21dc6f272d5376b240e229117b35f9cb892fb6a7ee00c567eb5b7e951af15251733fbf83e3b4a2ca0af4e5197d271ad16eb6561c186013a577bd17f399bfa20c908237c7a784d5fc3dd98665f20f85fecaf53ac79ec7f0017", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEBRWQpMvpOuIdxvJy1TdrJA4ikRezX5y4\nkvtqfuAMVn61t+lRrxUlFzP7+D47Siygr05Rl9JxrRbrZWHBhgE6V3vRfzmb+iDJ\nCCN8enhNX8PdmGZfIPhf7K9TrHnsfwAX\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04c1d95813b50699453f6b4859f2ea93c6e48dd46ea154786afa5f01a9ea7093761114885711285ed7bf78a66ad0f5fcec09342a946ef35fd9eada444687903f43640025cd2a1c2ca4dac580ed933d9fa9441ad80f3ae76b531acd8abbd4d672aa", "wx": "00c1d95813b50699453f6b4859f2ea93c6e48dd46ea154786afa5f01a9ea7093761114885711285ed7bf78a66ad0f5fcec", "wy": "09342a946ef35fd9eada444687903f43640025cd2a1c2ca4dac580ed933d9fa9441ad80f3ae76b531acd8abbd4d672aa"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004c1d95813b50699453f6b4859f2ea93c6e48dd46ea154786afa5f01a9ea7093761114885711285ed7bf78a66ad0f5fcec09342a946ef35fd9eada444687903f43640025cd2a1c2ca4dac580ed933d9fa9441ad80f3ae76b531acd8abbd4d672aa", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEwdlYE7UGmUU/a0hZ8uqTxuSN1G6hVHhq\n+l8Bqepwk3YRFIhXEShe1794pmrQ9fzsCTQqlG7zX9nq2kRGh5A/Q2QAJc0qHCyk\n2sWA7ZM9n6lEGtgPOudrUxrNirvU1nKq\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 424, "comment": "extreme value for k", "msg": "************", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3NhfeSpYmLG9dnpi/kpLcKfj0Hb0omhR8\n6doxE7XwuMAKYLHOHX6BnXpDHXyQ6g5f\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "testing point duplication", "msg": "************", "sig": "30640230342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed2802302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 426, "comment": "testing point duplication", "msg": "************", "sig": "3065023100cbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede05a3783e9fef3c1bb2aa85d6b0a80a5a6062306811743c4b02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "00c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3yeghtWnZ05CiYWdAbW0j1gcL4kLXZeuD\nFiXO7EoPRz71n04w4oF+YoW84oRvFfGg\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 427, "comment": "testing point duplication", "msg": "************", "sig": "30640230342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3a56f87db98089d208c89e902bb50ed2802302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 428, "comment": "testing point duplication", "msg": "************", "sig": "3065023100cbd2518ae59c5c357e7630cbd4c4cb1555da9a1d381d9ede05a3783e9fef3c1bb2aa85d6b0a80a5a6062306811743c4b02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "wx": "29bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc", "wy": "009a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKb23bV+nQb/XAjPLOmbMfUS+s7BmPZKo\nE2ZQR4vO+2HvGC4VWlQ0Wl6OXojwZOW8mlJat/dk2tPa4UaMK0GfO2K5upF9XoxP\nsexHQEo/x2R0snEwgb6dtMAOBDran8Sj\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "pseudorandom signature", "msg": "", "sig": "30650231009404b3ea09d8e0777f2f492c3f15736ff0e63e22389c676f9a1463b8d8153bbce5e522ee992cbd8d5e3f9378d33969fa023041593e5eb1eef51f034ee2e6384d17f5f466089bf064567571839bab3ec4cfb1a1b8533011e7cc3f9e337865385f86f1", "result": "valid", "flags": []}, {"tcId": 430, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "306402305662c815b9d1680cb027cbede73cbe0281b02fa97f99e6070ebc442d90dace4a8e2dc0f365c149bda35ca473e920cfcc0230367cf31980d9dbc7ff6a0a72c1fedf525a29fa3e83021f387030bc465607d5e65aa31385be28c2000b6a01dd83c8c02b", "result": "valid", "flags": []}, {"tcId": 431, "comment": "pseudorandom signature", "msg": "************", "sig": "306402304a886d736f5b9cad6044bc73f6a753af24c68ec366459f4d6bf9bddec936c8ee913e4c88490dee78ebb7234ea44b221d023067e53c5c9a53ac2879502e4cc6bb16e896ca89b931f439aaf91e3bd9686bc01d171eae952975ed2e8e9ccc9492fea51f", "result": "valid", "flags": []}, {"tcId": 432, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "30650230458766812c6e6818d9c26659700ff3d5f9de570275d0bb8faf16e2e28990486288d09b825424e67a738a4917dfb1afdd0231009383ea7a3b618018c1dd12c5df505ea4191638746578dcd700c086c6bc132ea028090eed26a7d4b0ffa779081fe3befc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "wx": "00ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aac", "wy": "00acbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE/////6pj8aI5rHAZfG6/zqV1bcASEj+C\nxR+odNZgKL4A6XahCAYGc3zHXEC9/kqsrL2FOJCIpipjmDhMIrUtSS8j9G5KJ6Ry\nStVVUdpcSDQ4CVokfLDDN48fUsNCX/nx\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 433, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3065023100a4d7ca043122dc46bda79650f5e1872e27cd9aa89744dc897af249521db2292650fd1cd66cebff3a3650414b5e1a70e102307ad031bc876682105cdaeba7405df3684aecf89586b63578acd005b5b61360c9e8cba3a8287a39fc6322138f962fda1e", "result": "valid", "flags": []}, {"tcId": 434, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30650231009a8e7c526acb3d0be87cf359fa1c7fde02baf47dfbdb4b48a2b6814346f3dcdbb9530a652873739d9493b807046f8d6202301c5781b742217b9465ce9daaabf6f94d4763bf362654092f9f23a8114b840229201e978353ce9f70a03a78f537967a37", "result": "valid", "flags": []}, {"tcId": 435, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306402306bbb20407a282269df53e14e38f4ec8075726a93dd2623f3b947592e57cb26f1d306ed79f02512d2b926ab0d1e8912ff02306789df1a3e41a6abbddc319e864aa536df09a0d49651b65b7b1a4374dec51cee619103ed202b22da0e223034299f0ccd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "wx": "00d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422", "wy": "00c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0YJ/xvbxLyGZLFpAmgZTsSHS7wKysKsB\nqRYc6VYoB0Cx41ayVXAbCm3cnsLKipQixu1dLO2NirdWD6W7iMc450VBiD2KKxwO\nK6fjbQMPxNm/uLIvJNuJfrrEndQAAAAA\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 436, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306502305b87f6174fe9312c2c9100ebdee5b3c8ef5c33c03b2a2056eaad4eed7e0634e73e62b2ff5653e880d1bf2a5ecb165a95023100ba21de53b2c2c925688088358a4997f4a2d9abc98a46dbf1277fc3221107519c2f7acbd13ddb13bc9bf9130e680378b0", "result": "valid", "flags": []}, {"tcId": 437, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402302fba05c6a994859807662dbbb5b7d09b1cf70bdf05578636fc8a19d3f887c572a26027fa21e9f7831867f414ecdd27f4023073cca8521906f6aad663b7def79e8ac0065f1c74c2be117b43302981abd47083226f297c7ba5c7b93ef359670cacff34", "result": "valid", "flags": []}, {"tcId": 438, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3064023041119aedab871dc7a6d2dd8bcf56e7d5297d61fd422096d550812a0d495a20d7f6765b733cf2e6c7c74a38051c71ffbb023011b37e5c0fbeecbb4ae1f390f80c3dadea079e7a1bc0ca3eb7b5b09fe9d74e1aa0557b0ddce29f0a15f67781ad2a671b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "wx": "1099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000", "wy": "00e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEJm7RRAPVfWoXMo94rO9XiUPT2+tZjGj\nFWwuUqM9fWFd0nn3n4tLr/fHE6wAAAAA5sm3NqiSny7Xvgx1OlTLtIuEaeBBHq+T\npKgkWboLaBu6j1+zg7SQbUkBozA+LxVX\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 439, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402302cccd76c899e528e85d6a310b93ea35113d3feb569246818f3b56bd3b75dc7246fa95cd051416593a09094c62a946e4802306d305b01bd4be2c7f855ee71c2da5cbf97dbb8fc23f6a887b8a365aaf1913213947ed4a07e1ead5741516e13eb70b64c", "result": "valid", "flags": []}, {"tcId": 440, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3065023100fdb90442feea49f2800a28d90f18421068b806ca6d088bec08bfa3df4fb7e866101d92e294d6e8b98f2efb52e57c8fe50230351f5065b69f2aa540d50e551965fb8a05bfabe9a4670682aef61a5ace5d5fd9310ab4a5b05f9c02f375ce0b5b6bd8ca", "result": "valid", "flags": []}, {"tcId": 441, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3065023100c183e7b548dcf2668e7414cf5fbb82c0158d3ac4e76a7862807682d954a0816482a601e6b6ddf728899bc6187774bc090230529f8b1e60c9f641f380a3f0aa39ed7eef7828a943c977a2562a41eeff040b871d422b9b5661d12d605f963eb0574f6f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "wx": "2b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69", "wy": "00d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAAAAACsInt11QWkBAUXyY/M0/BZ8wZ2u\ngiWXCuGcyMt+xzWT1qRlw3D1R4sOU51p0ZUdWXtWpnNFrLJYCVgfB80Ot42VOKP4\npl8wDmih63hQffdt5lDo+O5jpfDFaHyY\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 442, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306502307a29d180b75d661776ed0e5d0a720792a88b5429e8d6961465d5bcc871094141441c37b996cd0f9fd05a1b42210648fe023100e4a559272307a37bca803ba8306bdd42bc8fd1e62e21df178c7f9ac35003d05ccf303885a48c3fc8c8d2bc1677c8f00a", "result": "valid", "flags": []}, {"tcId": 443, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3065023100ad44bafd37a9f20b1c273fc604f61ec68759d956c47d25787d74925b54ad8d32f95ad49d1f6eaa847cc1fbb405008df702303b903e998f4d28156dd66e7954fb10d2a5a7f1f70e8402177cddf5577737bf633909b3037080acc469fb6ae5f0b33ec4", "result": "valid", "flags": []}, {"tcId": 444, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306402307a2fa750719b0b3a342a7256fb4e274a3783da3569fcd6442e3452f35bd57cb669fc9f93500839a2b76c37a986ef6cb8023034d8ea62222b7c3b71b776c322d2be021621fad3aca0a1cccfbb1f390ed171158a0919e5454cad6cedf81ad8228c0001", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2AAAAACCLP1rTs5N6zJ1gbMXs7KtKcB91\n7UKVfqTXhY0z9cJsauIKnMzaVplnANa0\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 445, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306502306bf105611bed561bd2f71d1ef2d6163a1a61965aaf7fe0220077ba91ac41c4f437bbf34135f78f0d6c9748c8852cdebe023100ed574398675aca014863cacfc6d4829a0f3adf8ed0a07ba7830e5b74c3efebb595d5f341a917562aaa6f76baa232014a", "result": "valid", "flags": []}, {"tcId": 446, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306402305c79378ac41d75a8799f8e2f41d9734ff5a3d4c2cbe0531d1e4da86b23d28add9edd6754b920514e10c1d3e091a876e202305725b3c6ae8a001fe23f6f902df8ff5ae3721c5b790d7a61ec893df7dddee36fcc09eb01b19b62bd2dd8e1e99f0902f5", "result": "valid", "flags": []}, {"tcId": 447, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3065023100ea45d18c2d84646ad4bf834ba484909e79d12a8acaa2e218fbf81ef662ad3f3ee5f044dea80f06f0fca384fcbf42f2080230699ad37f223aba03daf88e1a8bde46a871231cf56959d23760baf0f363fc8de77cee6909091c58a7cba9419d50928f94", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "00ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2/////990wKUsTGyFM2KfkzoTE1S1j+CK\nEr1qgVsoenHMCj2SlR31YzMlqWeY/ylL\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 448, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306602310082526fe3465a1a3514c0dc39fca27eb0fc2c7a8666f8dfffcfd1f809d621f3bb775f2df3fbd9434bb6b99e1693f5037c023100eec53fc8b529a4b283137e7136317d77577e942f325fe1848756818637289bceef76b699b7eee6181b258810f910ab67", "result": "valid", "flags": []}, {"tcId": 449, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3065023033f8ce53cb494e377fb351edd3a90e6d8a82007a39686042c35137e5f6c6b50e92ea6426dc07257f4e2833669c98af500231009d3c8aea4d601702791480fcf0c9e28e757b0457bf64e9529cef0a29f2c4074d3e7b2d838ecd311334ca671e542c841a", "result": "valid", "flags": []}, {"tcId": 450, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3064023038897bbcf7f806e2c7d32d9376462b0ac097717b984b3acfdb66cba4d20f2ea36f728342c21e6e9aaa517416c2ded86002300790f3b0f4a0c567ac9f58fdb42d1fc77ec08fc347736661cd73dd37993baad45af89a26d4154a6fafd81b4bae060e92", "result": "valid", "flags": []}]}]}