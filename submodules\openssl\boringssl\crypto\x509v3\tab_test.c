/* tabtest.c */
/*
 * Written by <PERSON> <PERSON> (<EMAIL>) for the OpenSSL project
 * 1999.
 */
/* ====================================================================
 * Copyright (c) 1999 The OpenSSL Project.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. All advertising materials mentioning features or use of this
 *    software must display the following acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit. (http://www.OpenSSL.org/)"
 *
 * 4. The names "OpenSSL Toolkit" and "OpenSSL Project" must not be used to
 *    endorse or promote products derived from this software without
 *    prior written permission. For written permission, please contact
 *    <EMAIL>.
 *
 * 5. Products derived from this software may not be called "OpenSSL"
 *    nor may "OpenSSL" appear in their names without prior written
 *    permission of the OpenSSL Project.
 *
 * 6. Redistributions of any form whatsoever must retain the following
 *    acknowledgment:
 *    "This product includes software developed by the OpenSSL Project
 *    for use in the OpenSSL Toolkit (http://www.OpenSSL.org/)"
 *
 * THIS SOFTWARE IS PROVIDED BY THE OpenSSL PROJECT ``AS IS'' AND ANY
 * EXPRESSED OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE OpenSSL PROJECT OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 * ====================================================================
 *
 * This product includes cryptographic software written by Eric Young
 * (<EMAIL>).  This product includes software written by Tim
 * Hudson (<EMAIL>).
 *
 */

/*
 * Simple program to check the ext_dat.h is correct and print out problems if
 * it is not.
 */

#include <stdio.h>

#include <openssl/base.h>
#include <openssl/crypto.h>
#include <openssl/obj.h>
#include <openssl/x509v3.h>

#if !defined(BORINGSSL_SHARED_LIBRARY)
# include "ext_dat.h"
#endif

int main(void)
{
#if !defined(BORINGSSL_SHARED_LIBRARY)
    unsigned i;
    int prev = -1, bad = 0;
    const X509V3_EXT_METHOD *const *tmp;
    CRYPTO_library_init();
    i = sizeof(standard_exts) / sizeof(X509V3_EXT_METHOD *);
    if (i != STANDARD_EXTENSION_COUNT)
        fprintf(stderr, "Extension number invalid expecting %d\n", i);
    tmp = standard_exts;
    for (i = 0; i < STANDARD_EXTENSION_COUNT; i++, tmp++) {
        if ((*tmp)->ext_nid < prev)
            bad = 1;
        prev = (*tmp)->ext_nid;

    }
    if (bad) {
        tmp = standard_exts;
        fprintf(stderr, "Extensions out of order!\n");
        for (i = 0; i < STANDARD_EXTENSION_COUNT; i++, tmp++)
            printf("%d : %s\n", (*tmp)->ext_nid, OBJ_nid2sn((*tmp)->ext_nid));
        return 1;
    } else {
        printf("PASS\n");
        return 0;
    }
#else
    /* TODO(davidben): Fix this test in the shared library build. */
    printf("PASS\n");
    return 0;
#endif
}
