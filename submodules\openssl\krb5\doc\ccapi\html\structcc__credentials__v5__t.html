<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_v5_t Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_v5_t Struct Reference<br>
<small>
[<a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a>]</small>
</h1><!-- doxytag: class="cc_credentials_v5_t" --><hr><a name="_details"></a><h2>Detailed Description</h2>
If a cc_credentials_t variable is used to store Kerberos v5 c redentials, and then credentials.credentials_v5 points to a v5 credentials structure. This structure is similar to a krb5_creds structure. 
<p>
<h2>Data Fields</h2>
<ul>
<li>char * <a class="el" href="structcc__credentials__v5__t.html#9a07d92f0eb56a4db24f14d21be5923b">client</a>
<li>char * <a class="el" href="structcc__credentials__v5__t.html#ffeefede74d4b54c220f6a43dd2beabd">server</a>
<li><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#c02ecbe79ca87f90c4f6771c330b9057">keyblock</a>
<li><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#43f17e3cff872e49a0d2b88deccb5c97">authtime</a>
<li><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#709d64dcb56e208a02f9dbd59ef703d7">starttime</a>
<li><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#0ebf7b3d5d28b0e9000c435af3ee9d59">endtime</a>
<li><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#1c710d76f8e94dc2c51e68bab258f15a">renew_till</a>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v5__t.html#7fb325cd1ddca84f9033cd2f5122e1d3">is_skey</a>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v5__t.html#c8380b3eee0768bc6c1fe8c719b72f04">ticket_flags</a>
<li><a class="el" href="structcc__data.html">cc_data</a> ** <a class="el" href="structcc__credentials__v5__t.html#80f824d334544ae2bd33c69eda1c9a09">addresses</a>
<li><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#6cf74018168214de0ea09704d9436c03">ticket</a>
<li><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#bb02ec4ece756277bceaa85626f2bc34">second_ticket</a>
<li><a class="el" href="structcc__data.html">cc_data</a> ** <a class="el" href="structcc__credentials__v5__t.html#0a8d97c740085a737b6aaec587f7fa9e">authdata</a>
</ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="9a07d92f0eb56a4db24f14d21be5923b"></a><!-- doxytag: member="cc_credentials_v5_t::client" ref="9a07d92f0eb56a4db24f14d21be5923b" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char* <a class="el" href="structcc__credentials__v5__t.html#9a07d92f0eb56a4db24f14d21be5923b">client</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the client principal.     </td>
  </tr>
</table>
<a class="anchor" name="ffeefede74d4b54c220f6a43dd2beabd"></a><!-- doxytag: member="cc_credentials_v5_t::server" ref="ffeefede74d4b54c220f6a43dd2beabd" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char* <a class="el" href="structcc__credentials__v5__t.html#ffeefede74d4b54c220f6a43dd2beabd">server</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the service principal.     </td>
  </tr>
</table>
<a class="anchor" name="c02ecbe79ca87f90c4f6771c330b9057"></a><!-- doxytag: member="cc_credentials_v5_t::keyblock" ref="c02ecbe79ca87f90c4f6771c330b9057" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#c02ecbe79ca87f90c4f6771c330b9057">keyblock</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Session encryption key info.     </td>
  </tr>
</table>
<a class="anchor" name="43f17e3cff872e49a0d2b88deccb5c97"></a><!-- doxytag: member="cc_credentials_v5_t::authtime" ref="43f17e3cff872e49a0d2b88deccb5c97" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#43f17e3cff872e49a0d2b88deccb5c97">authtime</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The time when the ticket was issued.     </td>
  </tr>
</table>
<a class="anchor" name="709d64dcb56e208a02f9dbd59ef703d7"></a><!-- doxytag: member="cc_credentials_v5_t::starttime" ref="709d64dcb56e208a02f9dbd59ef703d7" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#709d64dcb56e208a02f9dbd59ef703d7">starttime</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The time when the ticket becomes valid.     </td>
  </tr>
</table>
<a class="anchor" name="0ebf7b3d5d28b0e9000c435af3ee9d59"></a><!-- doxytag: member="cc_credentials_v5_t::endtime" ref="0ebf7b3d5d28b0e9000c435af3ee9d59" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#0ebf7b3d5d28b0e9000c435af3ee9d59">endtime</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The time when the ticket expires.     </td>
  </tr>
</table>
<a class="anchor" name="1c710d76f8e94dc2c51e68bab258f15a"></a><!-- doxytag: member="cc_credentials_v5_t::renew_till" ref="1c710d76f8e94dc2c51e68bab258f15a" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v5__t.html#1c710d76f8e94dc2c51e68bab258f15a">renew_till</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The time when the ticket becomes no longer renewable (if renewable).     </td>
  </tr>
</table>
<a class="anchor" name="7fb325cd1ddca84f9033cd2f5122e1d3"></a><!-- doxytag: member="cc_credentials_v5_t::is_skey" ref="7fb325cd1ddca84f9033cd2f5122e1d3" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v5__t.html#7fb325cd1ddca84f9033cd2f5122e1d3">is_skey</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
1 if the ticket is encrypted in another ticket's key, or 0 otherwise.     </td>
  </tr>
</table>
<a class="anchor" name="c8380b3eee0768bc6c1fe8c719b72f04"></a><!-- doxytag: member="cc_credentials_v5_t::ticket_flags" ref="c8380b3eee0768bc6c1fe8c719b72f04" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v5__t.html#c8380b3eee0768bc6c1fe8c719b72f04">ticket_flags</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket flags, as defined by the Kerberos 5 API.     </td>
  </tr>
</table>
<a class="anchor" name="80f824d334544ae2bd33c69eda1c9a09"></a><!-- doxytag: member="cc_credentials_v5_t::addresses" ref="80f824d334544ae2bd33c69eda1c9a09" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__data.html">cc_data</a>** <a class="el" href="structcc__credentials__v5__t.html#80f824d334544ae2bd33c69eda1c9a09">addresses</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The the list of network addresses of hosts that are allowed to authenticate using this ticket.     </td>
  </tr>
</table>
<a class="anchor" name="6cf74018168214de0ea09704d9436c03"></a><!-- doxytag: member="cc_credentials_v5_t::ticket" ref="6cf74018168214de0ea09704d9436c03" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#6cf74018168214de0ea09704d9436c03">ticket</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket data.     </td>
  </tr>
</table>
<a class="anchor" name="bb02ec4ece756277bceaa85626f2bc34"></a><!-- doxytag: member="cc_credentials_v5_t::second_ticket" ref="bb02ec4ece756277bceaa85626f2bc34" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__data.html">cc_data</a> <a class="el" href="structcc__credentials__v5__t.html#bb02ec4ece756277bceaa85626f2bc34">second_ticket</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Second ticket data.     </td>
  </tr>
</table>
<a class="anchor" name="0a8d97c740085a737b6aaec587f7fa9e"></a><!-- doxytag: member="cc_credentials_v5_t::authdata" ref="0a8d97c740085a737b6aaec587f7fa9e" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__data.html">cc_data</a>** <a class="el" href="structcc__credentials__v5__t.html#0a8d97c740085a737b6aaec587f7fa9e">authdata</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Authorization data.     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
