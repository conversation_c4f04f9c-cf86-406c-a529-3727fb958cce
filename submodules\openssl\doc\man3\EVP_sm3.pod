=pod

=head1 NAME

EVP_sm3
- SM3 for EVP

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_MD *EVP_sm3(void);

=head1 DESCRIPTION

SM3 is a cryptographic hash function with a 256-bit output, defined in GB/T
32905-2016.

=over 4

=item EVP_sm3()

The SM3 hash function.

=back


=head1 RETURN VALUES

These functions return a B<EVP_MD> structure that contains the
implementation of the symmetric cipher. See L<EVP_MD_meth_new(3)> for
details of the B<EVP_MD> structure.

=head1 CONFORMING TO

GB/T 32905-2016 and GM/T 0004-2012.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_DigestInit(3)>

=head1 COPYRIGHT

Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.
Copyright 2017 Ribose Inc. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

