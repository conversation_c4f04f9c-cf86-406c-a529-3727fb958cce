=pod

=head1 NAME

ossl_cmp_mock_srv_new,
ossl_cmp_mock_srv_free,
ossl_cmp_mock_srv_set1_certOut,
ossl_cmp_mock_srv_set1_chainOut,
ossl_cmp_mock_srv_set1_caPubsOut,
ossl_cmp_mock_srv_set_statusInfo,
ossl_cmp_mock_srv_set_sendError,
ossl_cmp_mock_srv_set_pollCount,
ossl_cmp_mock_srv_set_checkAfterTime
- functions used for testing with CMP mock server

=head1 SYNOPSIS

 #include "apps/cmp_mock_srv.h"

 OSSL_CMP_SRV_CTX *ossl_cmp_mock_srv_new(OSSL_LIB_CTX *libctx, const char *propq);
 void ossl_cmp_mock_srv_free(OSSL_CMP_SRV_CTX *srv_ctx);

 int ossl_cmp_mock_srv_set1_certOut(OSSL_CMP_SRV_CTX *srv_ctx, X509 *cert);
 int ossl_cmp_mock_srv_set1_chainOut(OSSL_CMP_SRV_CTX *srv_ctx,
                                     STACK_OF(X509) *chain);
 int ossl_cmp_mock_srv_set1_caPubsOut(OSSL_CMP_SRV_CTX *srv_ctx,
                                      STACK_OF(X509) *caPubs);
 int ossl_cmp_mock_srv_set_statusInfo(OSSL_CMP_SRV_CTX *srv_ctx, int status,
                                      int fail_info, const char *text);
 int ossl_cmp_mock_srv_set_sendError(OSSL_CMP_SRV_CTX *srv_ctx, int bodytype);
 int ossl_cmp_mock_srv_set_pollCount(OSSL_CMP_SRV_CTX *srv_ctx, int count);
 int ossl_cmp_mock_srv_set_checkAfterTime(OSSL_CMP_SRV_CTX *srv_ctx, int sec);

=head1 DESCRIPTION

ossl_cmp_mock_srv_new() allocates the contexts for the CMP mock server
associated with the library context I<libctx> and property query string
I<propq>, both of which may be NULL to select the defaults.

ossl_cmp_mock_srv_free() deallocates the contexts for the CMP mock server.

ossl_cmp_mock_srv_set1_certOut() sets the certificate to be returned in
cp/ip/kup.

ossl_cmp_mock_srv_set1_chainOut() sets the certificate chain to be added to
the extraCerts in a cp/ip/kup.
It should be useful for the validation of the certificate given via
ossl_cmp_mock_srv_set1_certOut().

ossl_cmp_mock_srv_set1_caPubsOut() sets the caPubs to be returned in an ip.

ossl_cmp_mock_srv_set_statusInfo() sets the status info to be returned.

ossl_cmp_mock_srv_set_sendError() enables enforcement of error responses
for requests of the given I<bodytype>, or for all requests if I<bodytype> is 1.
A I<bodytype> of -1 can be used to disable this feature, which is the default.

ossl_cmp_mock_srv_set_pollCount() sets the number of polls before cert response.

ossl_cmp_mock_srv_set_checkAfterTime() sets the number of seconds
the client should wait for the next poll.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

ossl_cmp_mock_srv() returns a B<OSSL_CMP_SRV_CTX> structure on success,
NULL on error.

ossl_cmp_mock_srv_free() does not return a value.

All other functions return 1 on success, 0 on error.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
