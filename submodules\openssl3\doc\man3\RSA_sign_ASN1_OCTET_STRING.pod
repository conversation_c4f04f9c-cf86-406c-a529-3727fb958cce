=pod

=head1 NAME

RSA_sign_ASN1_OCTET_STRING, RSA_verify_ASN1_OCTET_STRING - RSA signatures

=head1 SYNOPSIS

 #include <openssl/rsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int RSA_sign_ASN1_OCTET_STRING(int dummy, unsigned char *m,
                                unsigned int m_len, unsigned char *sigret,
                                unsigned int *siglen, RSA *rsa);

 int RSA_verify_ASN1_OCTET_STRING(int dummy, unsigned char *m,
                                  unsigned int m_len, unsigned char *sigbuf,
                                  unsigned int siglen, RSA *rsa);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use EVP PKEY APIs.

RSA_sign_ASN1_OCTET_STRING() signs the octet string B<m> of size
B<m_len> using the private key B<rsa> represented in DER using PKCS #1
padding. It stores the signature in B<sigret> and the signature size
in B<siglen>. B<sigret> must point to B<RSA_size(rsa)> bytes of
memory.

B<dummy> is ignored.

The random number generator must be seeded when calling
RSA_sign_ASN1_OCTET_STRING().
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see L<RAND(7)>), the operation will fail.

RSA_verify_ASN1_OCTET_STRING() verifies that the signature B<sigbuf>
of size B<siglen> is the DER representation of a given octet string
B<m> of size B<m_len>. B<dummy> is ignored. B<rsa> is the signer's
public key.

=head1 RETURN VALUES

RSA_sign_ASN1_OCTET_STRING() returns 1 on success, 0 otherwise.
RSA_verify_ASN1_OCTET_STRING() returns 1 on successful verification, 0
otherwise.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 BUGS

These functions serve no recognizable purpose.

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<RAND_bytes(3)>, L<RSA_sign(3)>,
L<RSA_verify(3)>,
L<RAND(7)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
