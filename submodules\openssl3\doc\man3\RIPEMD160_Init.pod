=pod

=head1 NAME

RIPEMD160, RIPEMD160_Init, RIPEMD160_Update, RIPEMD160_Final -
RIPEMD-160 hash function

=head1 SYNOPSIS

 #include <openssl/ripemd.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 unsigned char *RIPEMD160(const unsigned char *d, unsigned long n,
                          unsigned char *md);

 int RIPEMD160_Init(RIPEMD160_CTX *c);
 int RIPEMD160_Update(RIPEMD160_CTX *c, const void *data, unsigned long len);
 int RIPEMD160_Final(unsigned char *md, RIPEMD160_CTX *c);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_DigestInit_ex(3)>, L<EVP_DigestUpdate(3)>
and L<EVP_DigestFinal_ex(3)>.

RIPEMD-160 is a cryptographic hash function with a
160 bit output.

RIPEMD160() computes the RIPEMD-160 message digest of the B<n>
bytes at B<d> and places it in B<md> (which must have space for
RIPEMD160_DIGEST_LENGTH == 20 bytes of output). If B<md> is NULL, the digest
is placed in a static array.

The following functions may be used if the message is not completely
stored in memory:

RIPEMD160_Init() initializes a B<RIPEMD160_CTX> structure.

RIPEMD160_Update() can be called repeatedly with chunks of the message to
be hashed (B<len> bytes at B<data>).

RIPEMD160_Final() places the message digest in B<md>, which must have
space for RIPEMD160_DIGEST_LENGTH == 20 bytes of output, and erases
the B<RIPEMD160_CTX>.

=head1 RETURN VALUES

RIPEMD160() returns a pointer to the hash value.

RIPEMD160_Init(), RIPEMD160_Update() and RIPEMD160_Final() return 1 for
success, 0 otherwise.

=head1 NOTE

Applications should use the higher level functions
L<EVP_DigestInit(3)> etc. instead of calling these
functions directly.

=head1 CONFORMING TO

ISO/IEC 10118-3:2016 Dedicated Hash-Function 1 (RIPEMD-160).

=head1 SEE ALSO

L<EVP_DigestInit(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
