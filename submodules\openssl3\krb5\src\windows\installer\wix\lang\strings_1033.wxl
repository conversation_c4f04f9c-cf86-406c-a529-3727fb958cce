<?xml version="1.0" encoding="utf-8"?>
<WixLocalization Culture="en-US" xmlns="http://schemas.microsoft.com/wix/2006/localization">
<!--

  Copyright (C) 2004 by the Massachusetts Institute of Technology.
  All rights reserved.
 
  Export of this software from the United States of America may
    require a specific license from the United States Government.
    It is the responsibility of any person or organization contemplating
    export to obtain such a license before exporting.
 
  WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
  distribute this software and its documentation for any purpose and
  without fee is hereby granted, provided that the above copyright
  notice appear in all copies and that both that copyright notice and
  this permission notice appear in supporting documentation, and that
  the name of M.I.T. not be used in advertising or publicity pertaining
  to distribution of the software without specific, written prior
  permission.  Furthermore if you modify this software you must label
  your software as modified software and not distribute it in such a
  fashion that it might be confused with the original M.I.T. software.
  M.I.T. makes no representations about the suitability of
  this software for any purpose.  It is provided "as is" without express
  or implied warranty.
  
  -->
    <String Id="ProductName64">Kerberos for Windows (64-bit)</String>
    <String Id="ProductNameShort64">KFW64</String>
    <String Id="ProductName">Kerberos for Windows (32-bit)</String>
    <String Id="ProductNameShort">KFW32</String>
    <String Id="ProductMIT">MIT</String>
    <String Id="ProductDebug">Debug/Checked</String>
    <String Id="ProductBeta">Beta</String>
    
    <String Id="Manufacturer">Massachusetts Institute of Technology</String>
    
    <String Id="KerberosTitle">Kerberos for Windows</String>
    <String Id="KerberosDesc">Kerberos for Windows</String>
    
    <String Id="KerberosClientTitle">Client</String>
    <String Id="KerberosClientDesc">Kerberos client utilities, libraries and documentation</String>
    
    <String Id="StrKerberosClientDebugTitle">Debug symbols</String>
    <String Id="StrKerberosClientDebugDesc">Debugging symbols for Kerberos for Windows components.</String>

    <String Id="StrLeashExeTitle">Leash Credentials Manager</String>
    <String Id="StrLeashExeDesc">Leash credentials manager</String>
    
    <String Id="KerberosSDKTitle">SDK</String>
    <String Id="KerberosSDKDesc">Libraries and header files for developing software with Kerberos</String>
    
    <String Id="KerberosDocTitle">Documentation</String>
    <String Id="KerberosDocDesc">Documentation</String>
    
    <String Id="AdminRequired">You need administrative privileges to install Kerberos for Windows</String>
    <String Id="OsVersionRequired">This product requires Windows XP or Higher.  The current operating system is not supported.</String>
    <String Id="OsXPSP3">This product requires Windows XP Service Pack 3</String>
    <String Id="OsVistaSP2">This product requires Windows Vista Service Pack 2</String>
    <String Id="CMNotSelected">Neither Leash nor Network Identity Manager has been selected for this package.  Please contact your administrator or the provider of this installation package to resolve this issue.</String>
    <String Id="CMDupSelected">Both Leash and Network Identity Manager has been selected for this package.  Only one of these can be selected at one time.  Please contact your administrator or the provider of this installation package to resolve this issue.</String>

    <String Id="CantRemoveNSIS">The NSIS based installation of Kerberos for Windows could not be uninstalled because the NSIS uninstaller must be run in Full UI mode.</String>
    <String Id="IE501Required">Kerberos for Windows requires Microsoft Internet Explorer version 5.01 or higher.  Please resolve this and run the installer again.</String>

    <String Id="ARPComments">Build of</String>

    <String Id="StrPlatform64">This build of Kerberos for Windows is for 64-bit Windows versions.  Please install the 32-bit version on this operating system.</String>
    <String Id="StrPlatformNot64">This build of Kerberos for Windows is for 32-bit Windows versions.  Please install the 64-bit version on this operating system.</String>
</WixLocalization>
