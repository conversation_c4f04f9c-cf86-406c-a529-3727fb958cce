=pod

=head1 NAME

EVP_PKEY_decapsulate_init, EVP_PKEY_decapsulate
- Key decapsulation using a KEM algorithm with a private key

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_decapsulate_init(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
 int EVP_PKEY_decapsulate(EVP_PKEY_CTX *ctx,
                          unsigned char *unwrapped, size_t *unwrappedlen,
                          const unsigned char *wrapped, size_t wrappedlen);

=head1 DESCRIPTION

The EVP_PKEY_decapsulate_init() function initializes a private key algorithm
context I<ctx> for a decapsulation operation and then sets the I<params>
on the context in the same way as calling L<EVP_PKEY_CTX_set_params(3)>.
Note that I<ctx> usually is produced using L<EVP_PKEY_CTX_new_from_pkey(3)>,
specifying the private key to use.

The EVP_PKEY_decapsulate() function performs a private key decapsulation
operation using I<ctx>. The data to be decapsulated is specified using the
I<wrapped> and I<wrappedlen> parameters.
If I<unwrapped> is NULL then the maximum size of the output secret buffer
is written to I<*unwrappedlen>. If I<unwrapped> is not NULL and the
call is successful then the decapsulated secret data is written to I<unwrapped>
and the amount of data written to I<*unwrappedlen>.

=head1 NOTES

After the call to EVP_PKEY_decapsulate_init() algorithm-specific parameters
for the operation may be set or modified using L<EVP_PKEY_CTX_set_params(3)>.

=head1 RETURN VALUES

EVP_PKEY_decapsulate_init() and EVP_PKEY_decapsulate() return 1 for
success and 0 or a negative value for failure. In particular a return value of -2
indicates the operation is not supported by the private key algorithm.

=head1 EXAMPLES

Decapsulate data using RSA:

 #include <openssl/evp.h>

 /*
  * NB: assumes rsa_priv_key is an RSA private key,
  * and that in, inlen are already set up to contain encapsulated data.
  */

 EVP_PKEY_CTX *ctx = NULL;
 size_t secretlen = 0;
 unsigned char *secret = NULL;;

 ctx = EVP_PKEY_CTX_new_from_pkey(libctx, rsa_priv_key, NULL);
 if (ctx = NULL)
     /* Error */
 if (EVP_PKEY_decapsulate_init(ctx, NULL) <= 0)
     /* Error */

 /* Set the mode - only 'RSASVE' is currently supported */
 if (EVP_PKEY_CTX_set_kem_op(ctx, "RSASVE") <= 0)
     /* Error */

 /* Determine buffer length */
 if (EVP_PKEY_decapsulate(ctx, NULL, &secretlen, in, inlen) <= 0)
     /* Error */

 secret = OPENSSL_malloc(secretlen);
 if (secret == NULL)
     /* malloc failure */

 /* Decapsulated secret data is secretlen bytes long */
 if (EVP_PKEY_decapsulaterctx, secret, &secretlen, in, inlen) <= 0)
     /* Error */


=head1 SEE ALSO

L<EVP_PKEY_CTX_new_from_pkey(3)>,
L<EVP_PKEY_encapsulate(3)>,
L<EVP_KEM-RSA(7)>,

=head1 HISTORY

These functions were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
