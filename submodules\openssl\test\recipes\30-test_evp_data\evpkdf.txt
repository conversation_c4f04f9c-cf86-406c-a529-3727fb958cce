#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line.  Lines starting with a pound sign,
# like this prolog, are ignored.

Title = TLS1 PRF tests (from NIST test vectors)

KDF=TLS1-PRF
Ctrl.md = md:MD5-SHA1
Ctrl.Secret = hexsecret:bded7fa5c1699c010be23dd06ada3a48349f21e5f86263d512c0c5cc379f0e780ec55d9844b2f1db02a96453513568d0
Ctrl.label = seed:master secret
Ctrl.client_random = hexseed:e5acaf549cd25c22d964c0d930fa4b5261d2507fad84c33715b7b9a864020693
Ctrl.server_random = hexseed:135e4d557fdf3aa6406d82975d5c606a9734c9334b42136e96990fbd5358cdb2
Output = 2f6962dfbc744c4b2138bb6b3d33054c5ecc14f24851d9896395a44ab3964efc2090c5bf51a0891209f46c1e1e998f62

KDF=TLS1-PRF
Ctrl.md = md:MD5-SHA1
Ctrl.Secret = hexsecret:2f6962dfbc744c4b2138bb6b3d33054c5ecc14f24851d9896395a44ab3964efc2090c5bf51a0891209f46c1e1e998f62
Ctrl.label = seed:key expansion
Ctrl.server_random = hexseed:67267e650eb32444119d222a368c191af3082888dc35afe8368e638c828874be
Ctrl.client_random = hexseed:d58a7b1cd4fedaa232159df652ce188f9d997e061b9bf48e83b62990440931f6
Output = 3088825988e77fce68d19f756e18e43eb7fe672433504feaf99b3c503d9091b164f166db301d70c9fc0870b4a94563907bee1a61fb786cb717576890bcc51cb9ead97e01d0a2fea99c953377b195205ff07b369589178796edc963fd80fdbe518a2fc1c35c18ae8d

KDF=TLS1-PRF
Ctrl.md = md:SHA256
Ctrl.Secret = hexsecret:f8938ecc9edebc5030c0c6a441e213cd24e6f770a50dda07876f8d55da062bcadb386b411fd4fe4313a604fce6c17fbc
Ctrl.label = seed:master secret
Ctrl.client_random = hexseed:36c129d01a3200894b9179faac589d9835d58775f9b5ea3587cb8fd0364cae8c
Ctrl.server_random = hexseed:f6c9575ed7ddd73e1f7d16eca115415812a43c2b747daaaae043abfb50053fce
Output = 202c88c00f84a17a20027079604787461176455539e705be730890602c289a5001e34eeb3a043e5d52a65e66125188bf

KDF=TLS1-PRF
Ctrl.md = md:SHA256
Ctrl.Secret = hexsecret:202c88c00f84a17a20027079604787461176455539e705be730890602c289a5001e34eeb3a043e5d52a65e66125188bf
Ctrl.label = seed:key expansion
Ctrl.server_random = hexseed:ae6c806f8ad4d80784549dff28a4b58fd837681a51d928c3e30ee5ff14f39868
Ctrl.client_random = hexseed:62e1fd91f23f558a605f28478c58cf72637b89784d959df7e946d3f07bd1b616
Output = d06139889fffac1e3a71865f504aa5d0d2a2e89506c6f2279b670c3e1b74f531016a2530c51a3a0f7e1d6590d0f0566b2f387f8d11fd4f731cdd572d2eae927f6f2f81410b25e6960be68985add6c38445ad9f8c64bf8068bf9a6679485d966f1ad6f68b43495b10a683755ea2b858d70ccac7ec8b053c6bd41ca299d4e51928

# As above but use long name for KDF
KDF=tls1-prf
Ctrl.md = md:SHA256
Ctrl.Secret = hexsecret:202c88c00f84a17a20027079604787461176455539e705be730890602c289a5001e34eeb3a043e5d52a65e66125188bf
Ctrl.label = seed:key expansion
Ctrl.server_random = hexseed:ae6c806f8ad4d80784549dff28a4b58fd837681a51d928c3e30ee5ff14f39868
Ctrl.client_random = hexseed:62e1fd91f23f558a605f28478c58cf72637b89784d959df7e946d3f07bd1b616
Output = d06139889fffac1e3a71865f504aa5d0d2a2e89506c6f2279b670c3e1b74f531016a2530c51a3a0f7e1d6590d0f0566b2f387f8d11fd4f731cdd572d2eae927f6f2f81410b25e6960be68985add6c38445ad9f8c64bf8068bf9a6679485d966f1ad6f68b43495b10a683755ea2b858d70ccac7ec8b053c6bd41ca299d4e51928

# Missing digest.
KDF=TLS1-PRF
Ctrl.Secret = hexsecret:01
Ctrl.Seed = hexseed:02
Output = 03
Result = KDF_DERIVE_ERROR

# Missing secret.
KDF=TLS1-PRF
Ctrl.md = md:MD5-SHA1
Ctrl.Seed = hexseed:02
Output = 03
Result = KDF_DERIVE_ERROR

Title = HKDF tests (from RFC5869 test vectors)

KDF = HKDF
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = hexsalt:000102030405060708090a0b0c
Ctrl.info = hexinfo:f0f1f2f3f4f5f6f7f8f9
Output = 3cb25f25faacd57a90434f64d0362f2a2d2d0a90cf1a5a4c5db02d56ecc4c5bf34007208d5b887185865

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = hexsalt:000102030405060708090a0b0c
Output = 077709362c2e32df0ddc3f0dc47bba6390b6c73bb50f9c3122ec844ad7c2b3e5

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:077709362c2e32df0ddc3f0dc47bba6390b6c73bb50f9c3122ec844ad7c2b3e5
Ctrl.info = hexinfo:f0f1f2f3f4f5f6f7f8f9
Output = 3cb25f25faacd57a90434f64d0362f2a2d2d0a90cf1a5a4c5db02d56ecc4c5bf34007208d5b887185865

KDF = HKDF
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ctrl.salt = hexsalt:606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Ctrl.info = hexinfo:b0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Output = b11e398dc80327a1c8e7f78c596a49344f012eda2d4efad8a050cc4c19afa97c59045a99cac7827271cb41c65e590e09da3275600c2f09b8367793a9aca3db71cc30c58179ec3e87c14c01d5c1f3434f1d87

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ctrl.salt = hexsalt:606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Output = 06a6b88c5853361a06104c9ceb35b45cef760014904671014a193f40c15fc244

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:06a6b88c5853361a06104c9ceb35b45cef760014904671014a193f40c15fc244
Ctrl.info = hexinfo:b0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Output = b11e398dc80327a1c8e7f78c596a49344f012eda2d4efad8a050cc4c19afa97c59045a99cac7827271cb41c65e590e09da3275600c2f09b8367793a9aca3db71cc30c58179ec3e87c14c01d5c1f3434f1d87

KDF = HKDF
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = salt:
Ctrl.info = info:
Output = 8da4e775a563c18f715f802a063c5a31b8a11f5c5ee1879ec3454e5f3c738d2d9d201395faa4b61a96c8

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = salt:
Ctrl.info = info:
Output = 19ef24a32c717b167f33a91d6f648bdf96596776afdb6377ac434c1c293ccb04

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA256
Ctrl.IKM = hexkey:19ef24a32c717b167f33a91d6f648bdf96596776afdb6377ac434c1c293ccb04
Ctrl.info = info:
Output = 8da4e775a563c18f715f802a063c5a31b8a11f5c5ee1879ec3454e5f3c738d2d9d201395faa4b61a96c8

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = hexsalt:000102030405060708090a0b0c
Ctrl.info = hexinfo:f0f1f2f3f4f5f6f7f8f9
Output = 085a01ea1b10f36933068b56efa5ad81a4f14b822f5b091568a9cdd4f155fda2c22e422478d305f3f896

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = hexsalt:000102030405060708090a0b0c
Output = 9b6c18c432a7bf8f0e71c8eb88f4b30baa2ba243

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:9b6c18c432a7bf8f0e71c8eb88f4b30baa2ba243
Ctrl.info = hexinfo:f0f1f2f3f4f5f6f7f8f9
Output = 085a01ea1b10f36933068b56efa5ad81a4f14b822f5b091568a9cdd4f155fda2c22e422478d305f3f896

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ctrl.salt = hexsalt:606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Ctrl.info = hexinfo:b0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Output = 0bd770a74d1160f7c9f12cd5912a06ebff6adcae899d92191fe4305673ba2ffe8fa3f1a4e5ad79f3f334b3b202b2173c486ea37ce3d397ed034c7f9dfeb15c5e927336d0441f4c4300e2cff0d0900b52d3b4

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ctrl.salt = hexsalt:606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Output = 8adae09a2a307059478d309b26c4115a224cfaf6

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:8adae09a2a307059478d309b26c4115a224cfaf6
Ctrl.info = hexinfo:b0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Output = 0bd770a74d1160f7c9f12cd5912a06ebff6adcae899d92191fe4305673ba2ffe8fa3f1a4e5ad79f3f334b3b202b2173c486ea37ce3d397ed034c7f9dfeb15c5e927336d0441f4c4300e2cff0d0900b52d3b4

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = salt:
Ctrl.info = info:
Output = 0ac1af7002b3d761d1e55298da9d0506b9ae52057220a306e07b6b87e8df21d0ea00033de03984d34918

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Ctrl.salt = salt:
Output = da8c8a73c7fa77288ec6f5e7c297786aa0d32d01

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:da8c8a73c7fa77288ec6f5e7c297786aa0d32d01
Ctrl.info = info:
Output = 0ac1af7002b3d761d1e55298da9d0506b9ae52057220a306e07b6b87e8df21d0ea00033de03984d34918

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.salt = salt:
Ctrl.info = info:
Output = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48

KDF = HKDF
Ctrl.mode = mode:EXTRACT_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.salt = salt:
Output = 2adccada18779e7c2077ad2eb19d3f3e731385dd

KDF = HKDF
Ctrl.mode = mode:EXPAND_ONLY
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:2adccada18779e7c2077ad2eb19d3f3e731385dd
Ctrl.info = info:
Output = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48

KDF = HKDF
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.salt = salt:
Ctrl.info = info:
Output = 00
Result = KDF_DERIVE_ERROR

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.salt = salt:
Ctrl.info = info:
Output = 00
Result = KDF_DERIVE_ERROR

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.info = info:
Output = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48

KDF = HKDF
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.salt = salt:
Output = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48

KDF = HKDF
Ctrl.mode = mode:EXTRACT_AND_EXPAND
Ctrl.md = md:SHA1
Ctrl.IKM = hexkey:0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Ctrl.salt = salt:
Output = 2c91117204d745f3500d636a62f64f0ab3bae548aa53d423b0d1f27ebba6f5e5673a081d70cce7acfc48

Title = id-scrypt tests (from draft-josefsson-id-scrypt-kdf-03 and others)

KDF = scrypt
Ctrl.pass = pass:
Ctrl.salt = salt:
Ctrl.N = N:16
Ctrl.r = r:1
Ctrl.p = p:1
Output = 77d6576238657b203b19ca42c18a0497f16b4844e3074ae8dfdffa3fede21442fcd0069ded0948f8326a753a0fc81f17e8d3e0fb2e0d3628cf35e20c38d18906

KDF = scrypt
Ctrl.pass = pass:password
Ctrl.salt = salt:NaCl
Ctrl.N = N:1024
Ctrl.r = r:8
Ctrl.p = p:16
Output = fdbabe1c9d3472007856e7190d01e9fe7c6ad7cbc8237830e77376634b3731622eaf30d92e22a3886ff109279d9830dac727afb94a83ee6d8360cbdfa2cc0640

KDF = scrypt
Ctrl.hexpass = hexpass:70617373776f7264
Ctrl.salt = salt:NaCl
Ctrl.N = N:1024
Ctrl.r = r:8
Ctrl.p = p:16
Output = fdbabe1c9d3472007856e7190d01e9fe7c6ad7cbc8237830e77376634b3731622eaf30d92e22a3886ff109279d9830dac727afb94a83ee6d8360cbdfa2cc0640

KDF = scrypt
Ctrl.pass = pass:password
Ctrl.hexsalt = hexsalt:4e61436c
Ctrl.N = N:1024
Ctrl.r = r:8
Ctrl.p = p:16
Output = fdbabe1c9d3472007856e7190d01e9fe7c6ad7cbc8237830e77376634b3731622eaf30d92e22a3886ff109279d9830dac727afb94a83ee6d8360cbdfa2cc0640

KDF = scrypt
Ctrl.pass = pass:pleaseletmein
Ctrl.salt = salt:SodiumChloride
Ctrl.N = N:16384
Ctrl.r = r:8
Ctrl.p = p:1
Output = 7023bdcb3afd7348461c06cd81fd38ebfda8fbba904f8e3ea9b543f6545da1f2d5432955613f0fcf62d49705242a9af9e61e85dc0d651e40dfcf017b45575887

# Out of memory - request > 2 GB of memory
KDF = scrypt
Ctrl.pass = pass:pleaseletmein
Ctrl.salt = salt:SodiumChloride
Ctrl.N = N:2097152
Ctrl.r = r:8
Ctrl.p = p:1
Result = KDF_DERIVE_ERROR

