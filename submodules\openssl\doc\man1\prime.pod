=pod

=head1 NAME

openssl-prime,
prime - compute prime numbers

=head1 SYNOPSIS

B<openssl prime>
[B<-help>]
[B<-hex>]
[B<-generate>]
[B<-bits>]
[B<-safe>]
[B<-checks>]
[I<number...>]

=head1 DESCRIPTION

The B<prime> command checks if the specified numbers are prime.

If no numbers are given on the command line, the B<-generate> flag should
be used to generate primes according to the requirements specified by the
rest of the flags.

=head1 OPTIONS

=over 4

=item [B<-help>]

Display an option summary.

=item [B<-hex>]

Generate hex output.

=item [B<-generate>]

Generate a prime number.

=item [B<-bits num>]

Generate a prime with B<num> bits.

=item [B<-safe>]

When used with B<-generate>, generates a "safe" prime. If the number
generated is B<n>, then check that B<(n-1)/2> is also prime.

=item [B<-checks num>]

Perform the checks B<num> times to see that the generated number
is prime.  The default is 20.

=back

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
