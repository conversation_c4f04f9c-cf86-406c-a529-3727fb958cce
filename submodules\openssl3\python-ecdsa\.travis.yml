# workaround for 3.7 not available in default configuration
# travis-ci/travis-ci#9815
dist: trusty
sudo: false
language: python
cache: pip
addons:
  apt_packages:
      # needed for gmpy and gmpy2
      - libgmp-dev
      - libmpfr-dev
      - libmpc-dev
before_cache:
  - rm -f $HOME/.cache/pip/log/debug.log
# place the slowest (instrumental and py2.6) first
matrix:
  include:
      - python: 2.7
        env: INSTRUMENTAL=yes
        dist: bionic
        sudo: true
      - python: 2.6
        env: TOX_ENV=py26
      - python: 2.7
        env: TOX_ENV=py27
      - python: 2.7
        env: TOX_ENV=py27_old_gmpy
      - python: 2.7
        env: TOX_ENV=py27_old_gmpy2
      - python: 2.7
        env: TOX_ENV=py27_old_six
      - python: 2.7
        env: TOX_ENV=gmpypy27
      - python: 2.7
        env: TOX_ENV=gmpy2py27
      - python: 3.3
        env: TOX_ENV=py33
      - python: 3.4
        env: TOX_ENV=py34
      - python: 3.5
        env: TOX_ENV=py35
      - python: 3.6
        env: TOX_ENV=py36
      - python: 3.7
        env: TOX_ENV=py37
        dist: bionic
        sudo: true
      - python: 3.8
        env: TOX_ENV=py38
        dist: bionic
        sudo: true
      - python: 3.9
        env: TOX_ENV=codechecks
        dist: bionic
        sudo: true
      - python: 3.9
        env: TOX_ENV=py39
        dist: bionic
        sudo: true
      - python: 3.9
        env: TOX_ENV=gmpypy39
        dist: bionic
        sudo: true
      - python: 3.9
        env: TOX_ENV=gmpy2py39
        dist: bionic
        sudo: true
      - python: nightly
        env: TOX_ENV=py
        dist: bionic
        sudo: true
      - python: pypy
        env: TOX_ENV=pypy
      - python: pypy3
        env: TOX_ENV=pypy3
      # We use explicit version as the PATH needs major-minor part
      - name: "Python3.8.0 on Windows"
        os: windows
        language: shell
        before_install:
          - choco install python --version 3.8.0
          - python -m pip install --upgrade pip
        env: PATH=/c/Python38:/c/Python38/Scripts:$PATH
        install:
          - pip list
          - pip install six
          - pip install -r build-requirements.txt
          - pip list
        script:
          - coverage run --branch -m pytest src/ecdsa
        after_success:
          - coveralls

  allow_failures:
      - python: nightly

# for instrumental we're checking if the coverage changed from base branch
# so collect that info
before_install:
  - |
       echo -e "TRAVIS_PULL_REQUEST=$TRAVIS_PULL_REQUEST\n" \
               "TRAVIS_REPO_SLUG=$TRAVIS_REPO_SLUG\n" \
               "TRAVIS_PULL_REQUEST=$TRAVIS_PULL_REQUEST\n" \
               "TRAVIS_COMMIT=$TRAVIS_COMMIT\n" \
               "TRAVIS_PYTHON_VERSION=$TRAVIS_PYTHON_VERSION"
  - |
       # workaround https://github.com/travis-ci/travis-ci/issues/2666
       if [ "$TRAVIS_PULL_REQUEST" != "false" ]; then
         URL="https://github.com/${TRAVIS_REPO_SLUG}/pull/${TRAVIS_PULL_REQUEST}.patch"
         # `--location` makes curl follow redirects
         PR_FIRST=$(curl --silent --show-error --location $URL | head -1 | grep -o -E '\b[0-9a-f]{40}\b' | tr -d '\n')
         TRAVIS_COMMIT_RANGE=$PR_FIRST^..$TRAVIS_COMMIT
       fi
  # sanity check current commit
  - BRANCH=$(git rev-parse HEAD)
  - echo "TRAVIS_COMMIT_RANGE=$TRAVIS_COMMIT_RANGE"
  - git fetch origin master:refs/remotes/origin/master


install:
  - pip list
  - |
      if [[ -e build-requirements-${TRAVIS_PYTHON_VERSION}.txt ]]; then
        travis_retry pip install -r build-requirements-${TRAVIS_PYTHON_VERSION}.txt;
      else
        travis_retry pip install -r build-requirements.txt;
      fi
  - if [[ $TOX_ENV =~ gmpy2 ]] || [[ $INSTRUMENTAL ]]; then travis_retry pip install gmpy2; fi
  - if [[ $TOX_ENV =~ gmpyp ]]; then travis_retry pip install gmpy; fi
  - if [[ $INSTRUMENTAL ]]; then travis_retry pip install instrumental; fi
  - pip list
script:
  - if [[ $TOX_ENV ]]; then tox -e $TOX_ENV; fi
  - if [[ $TOX_ENV =~ gmpy2 ]]; then tox -e speedgmpy2; fi
  - if [[ $TOX_ENV =~ gmpyp ]]; then tox -e speedgmpy; fi
  - if ! [[ $TOX_ENV =~ gmpy ]]; then tox -e speed; fi
  - |
      if [[ $INSTRUMENTAL && $TRAVIS_PULL_REQUEST != "false" ]]; then
        git checkout $PR_FIRST^
        instrumental -t ecdsa -i 'test.*|.*_version|.*_compat' `which pytest` src/ecdsa/test*.py
        instrumental -f .instrumental.cov -s
        instrumental -f .instrumental.cov -s | python diff-instrumental.py --save .diff-instrumental
        git checkout $BRANCH
        instrumental -t ecdsa -i 'test.*|.*_version|.*_compat' `which pytest` src/ecdsa/test*.py
        instrumental -f .instrumental.cov -sr
      fi
  - |
      if [[ $INSTRUMENTAL && $TRAVIS_PULL_REQUEST == "false" ]]; then
        instrumental -t ecdsa -i 'test.*|.*_version|.*_compat' `which pytest` src/ecdsa
        instrumental -f .instrumental.cov -s
        # just log the values when merging
        instrumental -f .instrumental.cov -s | python diff-instrumental.py
      fi
  - |
      if [[ $INSTRUMENTAL && $TRAVIS_PULL_REQUEST != "false" ]]; then
          instrumental -f .instrumental.cov -s | python diff-instrumental.py --read .diff-instrumental --fail-under 70 --max-difference -0.1
      fi
after_success:
  - if [[ -z $INSTRUMENTAL ]]; then coveralls; fi

