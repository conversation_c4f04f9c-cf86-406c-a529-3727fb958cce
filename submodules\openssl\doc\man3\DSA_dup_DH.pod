=pod

=head1 NAME

DSA_dup_DH - create a DH structure out of DSA structure

=head1 SYNOPSIS

 #include <openssl/dsa.h>

 DH *DSA_dup_DH(const DSA *r);

=head1 DESCRIPTION

DSA_dup_DH() duplicates DSA parameters/keys as DH parameters/keys. q
is lost during that conversion, but the resulting DH parameters
contain its length.

=head1 RETURN VALUES

DSA_dup_DH() returns the new B<DH> structure, and NULL on error. The
error codes can be obtained by L<ERR_get_error(3)>.

=head1 NOTE

Be careful to avoid small subgroup attacks when using this.

=head1 SEE ALSO

L<DH_new(3)>, L<DSA_new(3)>, L<ERR_get_error(3)>

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
