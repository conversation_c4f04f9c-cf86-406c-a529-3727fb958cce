.\"
.\" $Id: ucpgba.man,v 1.1 1999/11/19 16:08:34 mleisher Exp $
.\"
.TH ucpgba 3 "19 November 1999"
.SH NAME 
ucpgba \- functions for doing bidirectional reordering of Unicode text and
logical and visual cursor motion

.SH SYNOPSIS
.nf
#include <ucdata.h>
#include <ucpgba.h>

ucstring_t *ucstring_create(unsigned long *source, unsigned long start,
                            unsigned long end, int default_direction,
                            int cursor_motion)
.sp
void ucstring_free(ucstring_t *string)
.sp
int ucstring_set_cursor_motion(ucstring_t *string, int cursor_motion)
.sp
int ucstring_cursor_right(ucstring_t *string, int count)
.sp
int ucstring_cursor_left(ucstring_t *string, int count)
.sp
void ucstring_cursor_info(ucstring_t *string, int *direction,
                          unsigned long *position)

.SH DESCRIPTION
.TP 4
.BR Macros
UCPGBA_LTR
.br
UCPGBA_RTL
.br
UCPGBA_CURSOR_VISUAL
.br
UCPGBA_CURSOR_LOGICAL

.TP 4
.BR ucstring_create()
This function will create a reordered string by using the implicit
directionality of the characters in the specified substring.
.sp
The `default_direction' parameter should be one of UCPGBA_LTR or UCPGBA_RTL
and is used only in cases where a string contains no characters with strong
directionality.
.sp
The `cursor_motion' parameter should be one of UCPGBA_CURSOR_VISUAL or
UCPGBA_CURSOR_LOGICAL, and is used to specify the initial cursor motion
behavior.  This behavior can be switched at any time using
ustring_set_cursor_motion().

.TP 4
.BR ucstring_free()
This function will deallocate the memory used by the string, incuding the
string itself.

.TP 4
.BR ucstring_cursor_info()
This function will return the text position of the internal cursor and the
directionality of the text at that position.  The position returned is the
original text position of the character.

.TP 4
.BR ucstring_set_cursor_motion()
This function will change the cursor motion type and return the previous
cursor motion type.

.TP 4
.BR ucstring_cursor_right()
This function will move the internal cursor to the right according to the
type of cursor motion set for the string.
.sp
If no cursor motion is performed, it returns 0.  Otherwise it will return a 1.

.TP 4
.BR ucstring_cursor_left()
This function will move the internal cursor to the left according to the
type of cursor motion set for the string.
.sp
If no cursor motion is performed, it returns 0.  Otherwise it will return a 1.

.SH "SEE ALSO"
ucdata(3)

.SH ACKNOWLEDGMENTS
These are people who have helped with patches or alerted me about problems.

.SH AUTHOR
Mark Leisher
.br
Computing Research Lab
.br
New Mexico State University
.br
Email: <EMAIL>
