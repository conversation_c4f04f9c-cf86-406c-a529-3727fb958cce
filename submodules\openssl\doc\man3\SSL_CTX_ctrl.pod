=pod

=head1 NAME

SSL_CTX_ctrl, SSL_CTX_callback_ctrl, SSL_ctrl, SSL_callback_ctrl - internal handling functions for SSL_CTX and SSL objects

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_CTX_ctrl(SSL_CTX *ctx, int cmd, long larg, void *parg);
 long SSL_CTX_callback_ctrl(SSL_CTX *, int cmd, void (*fp)());

 long SSL_ctrl(SSL *ssl, int cmd, long larg, void *parg);
 long SSL_callback_ctrl(SSL *, int cmd, void (*fp)());

=head1 DESCRIPTION

The SSL_*_ctrl() family of functions is used to manipulate settings of
the SSL_CTX and SSL objects. Depending on the command B<cmd> the arguments
B<larg>, B<parg>, or B<fp> are evaluated. These functions should never
be called directly. All functionalities needed are made available via
other functions or macros.

=head1 RETURN VALUES

The return values of the SSL*_ctrl() functions depend on the command
supplied via the B<cmd> parameter.

=head1 SEE ALSO

L<ssl(7)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
