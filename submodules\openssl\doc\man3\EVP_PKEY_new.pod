=pod

=head1 NAME

EVP_PKEY_new,
EVP_PKEY_up_ref,
EVP_PKEY_free,
EVP_PKEY_new_raw_private_key,
EVP_PKEY_new_raw_public_key,
EVP_PKEY_new_CMAC_key,
EVP_PKEY_new_mac_key,
EVP_PKEY_get_raw_private_key,
EVP_PKEY_get_raw_public_key
- public/private key allocation and raw key handling functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 EVP_PKEY *EVP_PKEY_new(void);
 int EVP_PKEY_up_ref(EVP_PKEY *key);
 void EVP_PKEY_free(EVP_PKEY *key);

 EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
                                        const unsigned char *key, size_t keylen);
 EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
                                       const unsigned char *key, size_t keylen);
 <PERSON><PERSON>_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
                                 size_t len, const EVP_CIPHER *cipher);
 EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
                                int keylen);

 int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
                                  size_t *len);
 int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
                                 size_t *len);

=head1 DESCRIPTION

The EVP_PKEY_new() function allocates an empty B<EVP_PKEY> structure which is
used by OpenSSL to store public and private keys. The reference count is set to
B<1>.

EVP_PKEY_up_ref() increments the reference count of B<key>.

EVP_PKEY_free() decrements the reference count of B<key> and, if the reference
count is zero, frees it up. If B<key> is NULL, nothing is done.

EVP_PKEY_new_raw_private_key() allocates a new B<EVP_PKEY>. If B<e> is non-NULL
then the new B<EVP_PKEY> structure is associated with the engine B<e>. The
B<type> argument indicates what kind of key this is. The value should be a NID
for a public key algorithm that supports raw private keys, i.e. one of
B<EVP_PKEY_HMAC>, B<EVP_PKEY_POLY1305>, B<EVP_PKEY_SIPHASH>, B<EVP_PKEY_X25519>,
B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>. B<key> points to the
raw private key data for this B<EVP_PKEY> which should be of length B<keylen>.
The length should be appropriate for the type of the key. The public key data
will be automatically derived from the given private key data (if appropriate
for the algorithm type).

EVP_PKEY_new_raw_public_key() works in the same way as
EVP_PKEY_new_raw_private_key() except that B<key> points to the raw public key
data. The B<EVP_PKEY> structure will be initialised without any private key
information. Algorithm types that support raw public keys are
B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>.

EVP_PKEY_new_CMAC_key() works in the same way as EVP_PKEY_new_raw_private_key()
except it is only for the B<EVP_PKEY_CMAC> algorithm type. In addition to the
raw private key data, it also takes a cipher algorithm to be used during
creation of a CMAC in the B<cipher> argument. The cipher should be a standard
encryption only cipher. For example AEAD and XTS ciphers should not be used.

EVP_PKEY_new_mac_key() works in the same way as EVP_PKEY_new_raw_private_key().
New applications should use EVP_PKEY_new_raw_private_key() instead.

EVP_PKEY_get_raw_private_key() fills the buffer provided by B<priv> with raw
private key data. The size of the B<priv> buffer should be in B<*len> on entry
to the function, and on exit B<*len> is updated with the number of bytes
actually written. If the buffer B<priv> is NULL then B<*len> is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the private
key data. This function only works for algorithms that support raw private keys.
Currently this is: B<EVP_PKEY_HMAC>, B<EVP_PKEY_POLY1305>, B<EVP_PKEY_SIPHASH>,
B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>.

EVP_PKEY_get_raw_public_key() fills the buffer provided by B<pub> with raw
public key data. The size of the B<pub> buffer should be in B<*len> on entry
to the function, and on exit B<*len> is updated with the number of bytes
actually written. If the buffer B<pub> is NULL then B<*len> is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the public
key data. This function only works for algorithms that support raw public  keys.
Currently this is: B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or
B<EVP_PKEY_ED448>.

=head1 NOTES

The B<EVP_PKEY> structure is used by various OpenSSL functions which require a
general private key without reference to any particular algorithm.

The structure returned by EVP_PKEY_new() is empty. To add a private or public
key to this empty structure use the appropriate functions described in
L<EVP_PKEY_set1_RSA(3)>, L<EVP_PKEY_set1_DSA>, L<EVP_PKEY_set1_DH> or
L<EVP_PKEY_set1_EC_KEY>.

=head1 RETURN VALUES

EVP_PKEY_new(), EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(),
EVP_PKEY_new_CMAC_key() and EVP_PKEY_new_mac_key() return either the newly
allocated B<EVP_PKEY> structure or B<NULL> if an error occurred.

EVP_PKEY_up_ref(), EVP_PKEY_get_raw_private_key() and
EVP_PKEY_get_raw_public_key() return 1 for success and 0 for failure.

=head1 SEE ALSO

L<EVP_PKEY_set1_RSA(3)>, L<EVP_PKEY_set1_DSA>, L<EVP_PKEY_set1_DH> or
L<EVP_PKEY_set1_EC_KEY>

=head1 HISTORY

The
EVP_PKEY_new() and EVP_PKEY_free() functions exist in all versions of OpenSSL.

The EVP_PKEY_up_ref() function was added in OpenSSL 1.1.0.

The
EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(),
EVP_PKEY_new_CMAC_key(), EVP_PKEY_new_raw_private_key() and
EVP_PKEY_get_raw_public_key() functions were added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
