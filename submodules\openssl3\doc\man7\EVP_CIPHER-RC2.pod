=pod

=head1 NAME

EVP_CIPHER-RC2 - The RC2 EVP_CIPHER implementations

=head1 DESCRIPTION

Support for RC2 symmetric encryption using the B<EVP_CIPHER> API.

=head2 Algorithm Names

The following algorithms are available in the legacy provider:

=over 4

=item "RC2-CBC", "RC2" or "RC2-128"

=item "RC2-40-CBC" or "RC2-40"

=item "RC2-64-CBC" or "RC2-64"

=item "RC2-ECB"

=item "RC2-CFB"

=item "RC2-OFB"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-legacy(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
