<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
<HEAD>
<meta name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1">
<link rel="stylesheet" type="text/css" href="Leash.css" />
<style type="text/css">
td
{
vertical-align:top;
}
</style>
<Title>Passwords</Title>
</HEAD>
<BODY>
<H1>About Passwords</H1>
<P>
Kerberos offers strong and reliable security for computers and network systems that use it for authentication, but  it is only as secure as the password you choose. A weak password can jeopardize not only your privacy and information, but also any computers or networks that you access with that password.</P>

<table>
<tr><th>On this page</th><th colspan="2">On other pages</th></tr>
<tr>
<td > <b>Learn about...</b>
<ul id="helpul">
<li><a href=#choose> Choosing  passwords </a></li>
<li><a href=#maintain> Maintaining passwords</a></li>
</ul>
</td>

<td > <b>Learn about...</b>
<ul id="helpul">
<li><a href="HTML/Password_Tips.htm"> Password tips</a></li>
<li><a href="HTML/Forget_Password.htm"> Forgotten passwords</a></li>
</ul>
</td>
<td>
<b>How to...</b>
<ul id="helpul">
<li><a href="HTML/Change_Password.htm">Change password</a></li>

</ul>
</td>
</tr>
</table>

<H2><a name="choose">Choosing  Passwords </a></H2>
<p>
Create a strong password that is not a word found in the dictionary or based on easily available information about yourself. Usually the easiest way to do that is to start with a phrase, not a word.
For best security choose a unique password that you have not used with any other application. That way if one application is somehow hacked or compromised, the damage is limited to that application. <br>
<a href="HTML/Password_Tips.htm">About: Password Tips and Examples</a></P>

<H2><a name="maintain">Maintaining Passwords</a></H2>
<p>
 If you ever suspect that your password has been compromised, change it immediately. It's a good idea to occasionally change your password even if you think it is still secure. This habit can limit the damage if your password is compromised without your knowledge.</P>


<H3>Related Help</H3>
<ul id="helpul">
<li> <a href="Html/Password_Tips.htm">Password tips and examples</a></li>
<li> <a href="Html/Change_Password.htm">Change password</a></li>
<li> <a href="Html/Forget_Password.htm">If you forget your password</a></li>
</ul>
</BODY>
</HTML>
