/*
 * $<PERSON>er$
 *
 * Copyright 2008 Massachusetts Institute of Technology.
 * All Rights Reserved.
 *
 * Export of this software from the United States of America may
 * require a specific license from the United States Government.
 * It is the responsibility of any person or organization contemplating
 * export to obtain such a license before exporting.
 *
 * WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 * distribute this software and its documentation for any purpose and
 * without fee is hereby granted, provided that the above copyright
 * notice appear in all copies and that both that copyright notice and
 * this permission notice appear in supporting documentation, and that
 * the name of M.I.T. not be used in advertising or publicity pertaining
 * to distribution of the software without specific, written prior
 * permission.  Furthermore if you modify this software you must label
 * your software as modified software and not distribute it in such a
 * fashion that it might be confused with the original M.I.T. software.
 * M.I.T. makes no representations about the suitability of
 * this software for any purpose.  It is provided "as is" without express
 * or implied warranty.
 */

[ uuid (6E3B5060-CA46-1067-B31A-00DD010662DA),
  version(1.0),
  pointer_default(unique)
]

/* This interface sends a cci_stream via rpc.
 */

interface ccs_reply {
    const long HSIZE = 8;

/* The reply from the server to a request from the client: */
void ccs_rpc_request_reply(
    [in]                    const long              rpcmsg,         /* Message type */
    [in, size_is(HSIZE)]    const char              tsphandle[],
    [in, string]            const char*             uuid,
    [in]                    const long              srvStartTime,   /* Server Start Time */
    [in]                    const long              cbIn,           /* Length of buffer */
    [in,  size_is(cbIn)]    const unsigned char     chIn[],         /* Data buffer */
    [out]                   long*                   status );       /* Return code */

void ccs_rpc_connect_reply(
    [in]                    const long      rpcmsg,         /* Message type */
    [in, size_is(HSIZE)]    const char      tsphandle[],
    [in, string]            const char*     uuid,
    [in]                    const long      srvStartTime,   /* Server Start Time */
    [out]                   long*           status );       /* Return code */

void ccapi_listen(
    handle_t                hBinding,   
    [in]                    const long rpcmsg,              /* Message type */
    [out]                   long* status );                 /* Return code */

    }
