# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR MIT
# This file is distributed under the same license as the mit-krb5 package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: mit-krb5 1.17.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-12-11 11:46-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=CHARSET\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: ../../src/clients/kdestroy/kdestroy.c:52
#, c-format
msgid "Usage: %s [-A] [-q] [-c cache_name] [-p princ_name]\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:54
#, c-format
msgid "\t-A destroy all credential caches in collection\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:55
#, c-format
msgid "\t-q quiet mode\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:56
#: ../../src/clients/kswitch/kswitch.c:42
#, c-format
msgid "\t-c specify name of credentials cache\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:57
#, c-format
msgid "\t-p specify principal name within collection\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:71
#: ../../src/clients/kdestroy/kdestroy.c:165
msgid "while listing credential caches"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:78
#, c-format
msgid "Other credential caches present, use -A to destroy all\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:110
#: ../../src/clients/kinit/kinit.c:346 ../../src/clients/ksu/main.c:288
#, c-format
msgid "Only one -c option allowed\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:118
#, c-format
msgid "Only one -p option allowed\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:125
#: ../../src/clients/kinit/kinit.c:374 ../../src/clients/klist/klist.c:176
#, c-format
msgid "Kerberos 4 is no longer supported\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:138
#, c-format
msgid "-A option is exclusive with -p option\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:150
#: ../../src/clients/klist/klist.c:239 ../../src/clients/ksu/main.c:134
#: ../../src/clients/ksu/main.c:140 ../../src/clients/kswitch/kswitch.c:94
#: ../../src/kadmin/ktutil/ktutil.c:52 ../../src/kdc/main.c:939
#: ../../src/kprop/kprop.c:102 ../../src/kprop/kpropd.c:1058
msgid "while initializing krb5"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:157
#: ../../src/clients/klist/klist.c:246
msgid "while setting default cache name"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:172
msgid "composing ccache name"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:177
#, c-format
msgid "while destroying cache %s"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:190
#: ../../src/clients/kswitch/kswitch.c:107 ../../src/clients/kvno/kvno.c:189
#: ../../src/clients/kvno/kvno.c:373 ../../src/kadmin/cli/keytab.c:373
#: ../../src/kadmin/dbutil/kdb5_util.c:547
#, c-format
msgid "while parsing principal name %s"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:196
#, c-format
msgid "while finding cache for %s"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:204
#: ../../src/clients/klist/klist.c:460
msgid "while resolving ccache"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:211 ../../src/clients/ksu/main.c:990
msgid "while destroying cache"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:214
#, c-format
msgid "Ticket cache NOT destroyed!\n"
msgstr ""

#: ../../src/clients/kdestroy/kdestroy.c:216
#, c-format
msgid "Ticket cache %cNOT%c destroyed!\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:170
#, c-format
msgid "\t-V verbose\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:171
#, c-format
msgid "\t-l lifetime\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:172
#, c-format
msgid "\t-s start time\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:173
#, c-format
msgid "\t-r renewable lifetime\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:174
#, c-format
msgid "\t-f forwardable\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:175
#, c-format
msgid "\t-F not forwardable\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:176
#, c-format
msgid "\t-p proxiable\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:177
#, c-format
msgid "\t-P not proxiable\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:178
#, c-format
msgid "\t-n anonymous\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:179
#, c-format
msgid "\t-a include addresses\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:180
#, c-format
msgid "\t-A do not include addresses\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:181
#, c-format
msgid "\t-v validate\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:182
#, c-format
msgid "\t-R renew\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:183
#, c-format
msgid "\t-C canonicalize\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:184
#, c-format
msgid "\t-E client is enterprise principal name\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:185
#, c-format
msgid "\t-k use keytab\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:186
#, c-format
msgid "\t-i use default client keytab (with -k)\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:187
#, c-format
msgid "\t-t filename of keytab to use\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:188
#, c-format
msgid "\t-c Kerberos 5 cache name\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:189
#, c-format
msgid "\t-S service\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:190
#, c-format
msgid "\t-T armor credential cache\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:191
#, c-format
msgid "\t-X <attribute>[=<value>]\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:264 ../../src/clients/kinit/kinit.c:272
#, c-format
msgid "Bad lifetime value %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:306
#, c-format
msgid "Bad start time value %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:324
#, c-format
msgid "Only one -t option allowed.\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:332
#, c-format
msgid "Only one armor_ccache\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:354
#, c-format
msgid "Only one -I option allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:363
msgid "while adding preauth option"
msgstr ""

#: ../../src/clients/kinit/kinit.c:389
#, c-format
msgid "Only one of -f and -F allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:393
#, c-format
msgid "Only one of -p and -P allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:397
#, c-format
msgid "Only one of --request-pac and --no-request-pac allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:402
#, c-format
msgid "Only one of -a and -A allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:406
#, c-format
msgid "Only one of -t and -i allowed\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:412
#, c-format
msgid "keytab specified, forcing -k\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:415 ../../src/clients/klist/klist.c:214
#, c-format
msgid "Extra arguments (starting with \"%s\").\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:441
msgid "while initializing Kerberos 5 library"
msgstr ""

#: ../../src/clients/kinit/kinit.c:449 ../../src/clients/kinit/kinit.c:603
#, c-format
msgid "resolving ccache %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:454
#, c-format
msgid "Using specified cache: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:462
msgid "while getting default ccache"
msgstr ""

#: ../../src/clients/kinit/kinit.c:476 ../../src/clients/kinit/kinit.c:555
#: ../../src/clients/kpasswd/kpasswd.c:30 ../../src/clients/ksu/main.c:241
#, c-format
msgid "when parsing name %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:484 ../../src/kadmin/dbutil/kdb5_util.c:311
#: ../../src/kprop/kprop.c:156
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:395
msgid "while getting default realm"
msgstr ""

#: ../../src/clients/kinit/kinit.c:495
msgid "while building principal"
msgstr ""

#: ../../src/clients/kinit/kinit.c:503
msgid "When resolving the default client keytab"
msgstr ""

#: ../../src/clients/kinit/kinit.c:510
msgid "When determining client principal name from keytab"
msgstr ""

#: ../../src/clients/kinit/kinit.c:519
msgid "when creating default server principal name"
msgstr ""

#: ../../src/clients/kinit/kinit.c:526
#, c-format
msgid "(principal %s)"
msgstr ""

#: ../../src/clients/kinit/kinit.c:529
msgid "for local services"
msgstr ""

#: ../../src/clients/kinit/kinit.c:550 ../../src/clients/kpasswd/kpasswd.c:42
#, c-format
msgid "Unable to identify user\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:564 ../../src/clients/kswitch/kswitch.c:113
#, c-format
msgid "while searching for ccache for %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:570
#, c-format
msgid "Using existing cache: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:579
msgid "while generating new ccache"
msgstr ""

#: ../../src/clients/kinit/kinit.c:583
#, c-format
msgid "Using new cache: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:595
#, c-format
msgid "Using default cache: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:608
#, c-format
msgid "Using specified input cache: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:615 ../../src/clients/ksu/krb_auth_su.c:160
msgid "when unparsing name"
msgstr ""

#: ../../src/clients/kinit/kinit.c:619
#, c-format
msgid "Using principal: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:700
msgid "getting local addresses"
msgstr ""

#: ../../src/clients/kinit/kinit.c:724
#, c-format
msgid "while setting up KDB keytab for realm %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:733 ../../src/clients/kvno/kvno.c:363
#, c-format
msgid "resolving keytab %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:738
#, c-format
msgid "Using keytab: %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:742
msgid "resolving default client keytab"
msgstr ""

#: ../../src/clients/kinit/kinit.c:752
#, c-format
msgid "while setting '%s'='%s'"
msgstr ""

#: ../../src/clients/kinit/kinit.c:757
#, c-format
msgid "PA Option %s = %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:798
msgid "getting initial credentials"
msgstr ""

#: ../../src/clients/kinit/kinit.c:801
msgid "validating credentials"
msgstr ""

#: ../../src/clients/kinit/kinit.c:804
msgid "renewing credentials"
msgstr ""

#: ../../src/clients/kinit/kinit.c:812
#, c-format
msgid "%s: Password incorrect while %s\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:815
#, c-format
msgid "while %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:824
#, c-format
msgid "when initializing cache %s"
msgstr ""

#: ../../src/clients/kinit/kinit.c:829
#, c-format
msgid "Initialized cache\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:833
msgid "while storing credentials"
msgstr ""

#: ../../src/clients/kinit/kinit.c:837
#, c-format
msgid "Stored credentials\n"
msgstr ""

#: ../../src/clients/kinit/kinit.c:843
msgid "while switching to new ccache"
msgstr ""

#: ../../src/clients/kinit/kinit.c:898
#, c-format
msgid "Authenticated to Kerberos v5\n"
msgstr ""

#: ../../src/clients/klist/klist.c:85
#, c-format
msgid ""
"Usage: %s [-e] [-V] [[-c] [-l] [-A] [-d] [-f] [-s] [-a [-n]]] [-k [-t] [-K]] "
"[name]\n"
msgstr ""

#: ../../src/clients/klist/klist.c:87
#, c-format
msgid "\t-c specifies credentials cache\n"
msgstr ""

#: ../../src/clients/klist/klist.c:88
#, c-format
msgid "\t-k specifies keytab\n"
msgstr ""

#: ../../src/clients/klist/klist.c:89
#, c-format
msgid "\t   (Default is credentials cache)\n"
msgstr ""

#: ../../src/clients/klist/klist.c:90
#, c-format
msgid "\t-i uses default client keytab if no name given\n"
msgstr ""

#: ../../src/clients/klist/klist.c:91
#, c-format
msgid "\t-l lists credential caches in collection\n"
msgstr ""

#: ../../src/clients/klist/klist.c:92
#, c-format
msgid "\t-A shows content of all credential caches\n"
msgstr ""

#: ../../src/clients/klist/klist.c:93
#, c-format
msgid "\t-e shows the encryption type\n"
msgstr ""

#: ../../src/clients/klist/klist.c:94
#, c-format
msgid "\t-V shows the Kerberos version and exits\n"
msgstr ""

#: ../../src/clients/klist/klist.c:95
#, c-format
msgid "\toptions for credential caches:\n"
msgstr ""

#: ../../src/clients/klist/klist.c:96
#, c-format
msgid "\t\t-d shows the submitted authorization data types\n"
msgstr ""

#: ../../src/clients/klist/klist.c:98
#, c-format
msgid "\t\t-f shows credentials flags\n"
msgstr ""

#: ../../src/clients/klist/klist.c:99
#, c-format
msgid "\t\t-s sets exit status based on valid tgt existence\n"
msgstr ""

#: ../../src/clients/klist/klist.c:101
#, c-format
msgid "\t\t-a displays the address list\n"
msgstr ""

#: ../../src/clients/klist/klist.c:102
#, c-format
msgid "\t\t\t-n do not reverse-resolve\n"
msgstr ""

#: ../../src/clients/klist/klist.c:103
#, c-format
msgid "\toptions for keytabs:\n"
msgstr ""

#: ../../src/clients/klist/klist.c:104
#, c-format
msgid "\t\t-t shows keytab entry timestamps\n"
msgstr ""

#: ../../src/clients/klist/klist.c:105
#, c-format
msgid "\t\t-K shows keytab entry keys\n"
msgstr ""

#: ../../src/clients/klist/klist.c:223
#, c-format
msgid "%s version %s\n"
msgstr ""

#: ../../src/clients/klist/klist.c:276
msgid "while getting default client keytab"
msgstr ""

#: ../../src/clients/klist/klist.c:282
msgid "while getting default keytab"
msgstr ""

#: ../../src/clients/klist/klist.c:288 ../../src/kadmin/cli/keytab.c:103
#, c-format
msgid "while resolving keytab %s"
msgstr ""

#: ../../src/clients/klist/klist.c:295 ../../src/kadmin/cli/keytab.c:87
msgid "while getting keytab name"
msgstr ""

#: ../../src/clients/klist/klist.c:303 ../../src/kadmin/cli/keytab.c:422
msgid "while starting keytab scan"
msgstr ""

#: ../../src/clients/klist/klist.c:326 ../../src/clients/klist/klist.c:482
#: ../../src/clients/ksu/ccache.c:455 ../../src/kadmin/dbutil/dump.c:564
#: ../../src/kadmin/dbutil/tabdump.c:549
msgid "while unparsing principal name"
msgstr ""

#: ../../src/clients/klist/klist.c:348 ../../src/kadmin/cli/keytab.c:466
msgid "while scanning keytab"
msgstr ""

#: ../../src/clients/klist/klist.c:353 ../../src/kadmin/cli/keytab.c:471
msgid "while ending keytab scan"
msgstr ""

#: ../../src/clients/klist/klist.c:370 ../../src/clients/klist/klist.c:433
msgid "while listing ccache collection"
msgstr ""

#: ../../src/clients/klist/klist.c:409
msgid "(Expired)"
msgstr ""

#: ../../src/clients/klist/klist.c:486
#, c-format
msgid ""
"Ticket cache: %s:%s\n"
"Default principal: %s\n"
"\n"
msgstr ""

#: ../../src/clients/klist/klist.c:498
msgid "while starting to retrieve tickets"
msgstr ""

#: ../../src/clients/klist/klist.c:512
msgid "while finishing ticket retrieval"
msgstr ""

#: ../../src/clients/klist/klist.c:517
msgid "while retrieving a ticket"
msgstr ""

#: ../../src/clients/klist/klist.c:665 ../../src/clients/ksu/ccache.c:440
#: ../../src/kprop/kpropd.c:1209 ../../src/kprop/kpropd.c:1269
msgid "while unparsing client name"
msgstr ""

#: ../../src/clients/klist/klist.c:670 ../../src/clients/ksu/ccache.c:445
#: ../../src/kprop/kprop.c:190
msgid "while unparsing server name"
msgstr ""

#: ../../src/clients/klist/klist.c:700 ../../src/clients/ksu/ccache.c:470
#, c-format
msgid "\tfor client %s"
msgstr ""

#: ../../src/clients/klist/klist.c:712 ../../src/clients/ksu/ccache.c:479
msgid "renew until "
msgstr ""

#: ../../src/clients/klist/klist.c:729 ../../src/clients/ksu/ccache.c:489
#, c-format
msgid "Flags: %s"
msgstr ""

#: ../../src/clients/klist/klist.c:748
#, c-format
msgid "Etype (skey, tkt): %s, "
msgstr ""

#: ../../src/clients/klist/klist.c:764
#, c-format
msgid "AD types: "
msgstr ""

#: ../../src/clients/klist/klist.c:780
#, c-format
msgid "\tAddresses: (none)\n"
msgstr ""

#: ../../src/clients/klist/klist.c:782
#, c-format
msgid "\tAddresses: "
msgstr ""

#: ../../src/clients/klist/klist.c:816 ../../src/clients/klist/klist.c:826
#, c-format
msgid "broken address (type %d length %d)"
msgstr ""

#: ../../src/clients/klist/klist.c:835
#, c-format
msgid "unknown addrtype %d"
msgstr ""

#: ../../src/clients/klist/klist.c:844
#, c-format
msgid "unprintable address (type %d, error %d %s)"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:13 ../../src/lib/krb5/krb/gic_pwd.c:395
msgid "Enter new password"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:14 ../../src/lib/krb5/krb/gic_pwd.c:403
msgid "Enter it again"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:34
#, c-format
msgid "Unable to identify user from password file\n"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:63
#, c-format
msgid "usage: %s [principal]\n"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:71
msgid "initializing kerberos library"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:76
msgid "allocating krb5_get_init_creds_opt"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:92
msgid "opening default ccache"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:97
msgid "getting principal from ccache"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:102
msgid "while setting FAST ccache"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:108
msgid "closing ccache"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:116
msgid "parsing client name"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:134
msgid "Password incorrect while getting initial ticket"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:136
msgid "getting initial ticket"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:146
msgid "while reading password"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:154
msgid "changing password"
msgstr ""

#: ../../src/clients/kpasswd/kpasswd.c:174
#: ../lib/kadm5/chpass_util_strings.c:30
#, c-format
msgid "Password changed.\n"
msgstr ""

#: ../../src/clients/ksu/authorization.c:352
#, c-format
msgid ""
"Error: bad entry - %s in %s file, must be either full path or just the cmd "
"name\n"
msgstr ""

#: ../../src/clients/ksu/authorization.c:360
#, c-format
msgid ""
"Error: bad entry - %s in %s file, since %s is just the cmd name, CMD_PATH "
"must be defined \n"
msgstr ""

#: ../../src/clients/ksu/authorization.c:375
#, c-format
msgid "Error: bad entry - %s in %s file, CMD_PATH contains no paths \n"
msgstr ""

#: ../../src/clients/ksu/authorization.c:384
#, c-format
msgid "Error: bad path %s in CMD_PATH for %s must start with '/' \n"
msgstr ""

#: ../../src/clients/ksu/authorization.c:500
msgid "Error: not found -> "
msgstr ""

#: ../../src/clients/ksu/authorization.c:706
#, c-format
msgid "home directory name `%s' too long, can't search for .k5login\n"
msgstr ""

#: ../../src/clients/ksu/ccache.c:358
#, c-format
msgid "home directory path for %s too long\n"
msgstr ""

#: ../../src/clients/ksu/ccache.c:451
msgid "while retrieving principal name"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:57
#: ../../src/clients/ksu/krb_auth_su.c:62
msgid "while copying client principal"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:69
msgid "while creating tgt for local realm"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:84
msgid "while retrieving creds from cache"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:95
msgid "while switching to target uid"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:100
#, c-format
msgid ""
"WARNING: Your password may be exposed if you enter it here and are logged \n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:102
#, c-format
msgid "         in remotely using an unsecure (non-encrypted) channel. \n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:114 ../../src/clients/ksu/main.c:473
msgid "while reclaiming root uid"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:121
#, c-format
msgid "does not have any appropriate tickets in the cache.\n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:133
msgid "while verifying ticket for server"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:167
msgid "while getting time of day"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:171
#, c-format
msgid "Kerberos password for %s: "
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:175
#, c-format
msgid "principal name %s too long for internal buffer space\n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:184
#, c-format
msgid "while reading password for '%s'\n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:191
#, c-format
msgid "No password given\n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:205
#, c-format
msgid "%s: Password incorrect\n"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:207
msgid "while getting initial credentials"
msgstr ""

#: ../../src/clients/ksu/krb_auth_su.c:227
#: ../../src/clients/ksu/krb_auth_su.c:241
#, c-format
msgid " %s while unparsing name\n"
msgstr ""

#: ../../src/clients/ksu/main.c:68
#, c-format
msgid ""
"Usage: %s [target user] [-n principal] [-c source cachename] [-k] [-r time] "
"[-pf] [-l lifetime] [-zZ] [-q] [-e command [args... ] ] [-a [args... ] ]\n"
msgstr ""

#: ../../src/clients/ksu/main.c:150
msgid ""
"program name too long - quitting to avoid triggering system logging bugs"
msgstr ""

#: ../../src/clients/ksu/main.c:176
msgid "while allocating memory"
msgstr ""

#: ../../src/clients/ksu/main.c:189
msgid "while setting euid to source user"
msgstr ""

#: ../../src/clients/ksu/main.c:199 ../../src/clients/ksu/main.c:234
#, c-format
msgid "Bad lifetime value (%s hours?)\n"
msgstr ""

#: ../../src/clients/ksu/main.c:211 ../../src/clients/ksu/main.c:296
msgid "when gathering parameters"
msgstr ""

#: ../../src/clients/ksu/main.c:255
#, c-format
msgid "-z option is mutually exclusive with -Z.\n"
msgstr ""

#: ../../src/clients/ksu/main.c:263
#, c-format
msgid "-Z option is mutually exclusive with -z.\n"
msgstr ""

#: ../../src/clients/ksu/main.c:276
#, c-format
msgid "while looking for credentials cache %s"
msgstr ""

#: ../../src/clients/ksu/main.c:282
#, c-format
msgid "malformed credential cache name %s\n"
msgstr ""

#: ../../src/clients/ksu/main.c:340
#, c-format
msgid "ksu: who are you?\n"
msgstr ""

#: ../../src/clients/ksu/main.c:344
#, c-format
msgid "Your uid doesn't match your passwd entry?!\n"
msgstr ""

#: ../../src/clients/ksu/main.c:359
#, c-format
msgid "ksu: unknown login %s\n"
msgstr ""

#: ../../src/clients/ksu/main.c:379
msgid "while getting source cache"
msgstr ""

#: ../../src/clients/ksu/main.c:388
msgid "while selecting the best principal"
msgstr ""

#: ../../src/clients/ksu/main.c:396
msgid "while returning to source uid after finding best principal"
msgstr ""

#: ../../src/clients/ksu/main.c:416
#, c-format
msgid "account %s: authorization failed\n"
msgstr ""

#: ../../src/clients/ksu/main.c:451
msgid "while parsing temporary name"
msgstr ""

#: ../../src/clients/ksu/main.c:456
msgid "while creating temporary cache"
msgstr ""

#: ../../src/clients/ksu/main.c:462 ../../src/clients/ksu/main.c:702
#, c-format
msgid "while copying cache %s to %s"
msgstr ""

#: ../../src/clients/ksu/main.c:480
#, c-format
msgid ""
"WARNING: Your password may be exposed if you enter it here and are logged\n"
msgstr ""

#: ../../src/clients/ksu/main.c:482
#, c-format
msgid "         in remotely using an unsecure (non-encrypted) channel.\n"
msgstr ""

#: ../../src/clients/ksu/main.c:488
#, c-format
msgid "Goodbye\n"
msgstr ""

#: ../../src/clients/ksu/main.c:492
#, c-format
msgid "Could not get a tgt for "
msgstr ""

#: ../../src/clients/ksu/main.c:514
#, c-format
msgid "Authentication failed.\n"
msgstr ""

#: ../../src/clients/ksu/main.c:522
msgid "When unparsing name"
msgstr ""

#: ../../src/clients/ksu/main.c:526
#, c-format
msgid "Authenticated %s\n"
msgstr ""

#: ../../src/clients/ksu/main.c:533
msgid "while switching to target for authorization check"
msgstr ""

#: ../../src/clients/ksu/main.c:540
msgid "while checking authorization"
msgstr ""

#: ../../src/clients/ksu/main.c:546
msgid "while switching back from target after authorization check"
msgstr ""

#: ../../src/clients/ksu/main.c:553
#, c-format
msgid "Account %s: authorization for %s for execution of\n"
msgstr ""

#: ../../src/clients/ksu/main.c:555
#, c-format
msgid "               %s successful\n"
msgstr ""

#: ../../src/clients/ksu/main.c:561
#, c-format
msgid "Account %s: authorization for %s successful\n"
msgstr ""

#: ../../src/clients/ksu/main.c:573
#, c-format
msgid "Account %s: authorization for %s for execution of %s failed\n"
msgstr ""

#: ../../src/clients/ksu/main.c:581
#, c-format
msgid "Account %s: authorization of %s failed\n"
msgstr ""

#: ../../src/clients/ksu/main.c:596
msgid "while calling cc_filter"
msgstr ""

#: ../../src/clients/ksu/main.c:604
msgid "while erasing target cache"
msgstr ""

#: ../../src/clients/ksu/main.c:624
#, c-format
msgid "ksu: permission denied (shell).\n"
msgstr ""

#: ../../src/clients/ksu/main.c:633
#, c-format
msgid "ksu: couldn't set environment variable USER\n"
msgstr ""

#: ../../src/clients/ksu/main.c:639
#, c-format
msgid "ksu: couldn't set environment variable HOME\n"
msgstr ""

#: ../../src/clients/ksu/main.c:644
#, c-format
msgid "ksu: couldn't set environment variable SHELL\n"
msgstr ""

#: ../../src/clients/ksu/main.c:655
#, c-format
msgid "ksu: initgroups failed.\n"
msgstr ""

#: ../../src/clients/ksu/main.c:660
#, c-format
msgid "Leaving uid as %s (%ld)\n"
msgstr ""

#: ../../src/clients/ksu/main.c:663
#, c-format
msgid "Changing uid to %s (%ld)\n"
msgstr ""

#: ../../src/clients/ksu/main.c:689
msgid "while getting name of target ccache"
msgstr ""

#: ../../src/clients/ksu/main.c:709
#, c-format
msgid "%s does not have correct permissions for %s, %s aborted"
msgstr ""

#: ../../src/clients/ksu/main.c:730
#, c-format
msgid "Internal error: command %s did not get resolved\n"
msgstr ""

#: ../../src/clients/ksu/main.c:747 ../../src/clients/ksu/main.c:783
#, c-format
msgid "while trying to execv %s"
msgstr ""

#: ../../src/clients/ksu/main.c:773
msgid "while calling waitpid"
msgstr ""

#: ../../src/clients/ksu/main.c:778
msgid "while trying to fork."
msgstr ""

#: ../../src/clients/ksu/main.c:800
msgid "while reading cache name from ccache"
msgstr ""

#: ../../src/clients/ksu/main.c:806
#, c-format
msgid "ksu: couldn't set environment variable %s\n"
msgstr ""

#: ../../src/clients/ksu/main.c:832
msgid "while resetting target ccache name"
msgstr ""

#: ../../src/clients/ksu/main.c:846
msgid "while determining target ccache name"
msgstr ""

#: ../../src/clients/ksu/main.c:885
msgid "while generating part of the target ccache name"
msgstr ""

#: ../../src/clients/ksu/main.c:891
msgid "while allocating memory for the target ccache name"
msgstr ""

#: ../../src/clients/ksu/main.c:910
msgid "while creating new target ccache"
msgstr ""

#: ../../src/clients/ksu/main.c:916
msgid "while initializing target cache"
msgstr ""

#: ../../src/clients/ksu/main.c:956
#, c-format
msgid "terminal name %s too long\n"
msgstr ""

#: ../../src/clients/ksu/main.c:984
msgid "while changing to target uid for destroying ccache"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:41
#, c-format
msgid "Usage: %s {-c cache_name | -p principal}\n"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:43
#, c-format
msgid "\t-p specify name of principal\n"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:66
#, c-format
msgid "Only one -c or -p option allowed\n"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:85
#, c-format
msgid "One of -c or -p must be specified\n"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:101
#, c-format
msgid "while resolving %s"
msgstr ""

#: ../../src/clients/kswitch/kswitch.c:122
msgid "while switching to credential cache"
msgstr ""

#: ../../src/clients/kvno/kvno.c:41
#, c-format
msgid "usage: %s [-C] [-u] [-c ccache] [-e etype]\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:42
#, c-format
msgid "\t[-k keytab] [-S sname] [-U for_user [-P]]\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:43
#, c-format
msgid "\t[--u2u ccache] service1 service2 ...\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:102 ../../src/clients/kvno/kvno.c:110
#, c-format
msgid "Options -u and -S are mutually exclusive\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:127
#, c-format
msgid "Options --u2u and -P are mutually exclusive\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:133
#, c-format
msgid "Option -P (constrained delegation) requires keytab to be specified\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:137
#, c-format
msgid ""
"Option -P (constrained delegation) requires option -U (protocol transition)\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:197
#, c-format
msgid "while formatting parsed principal name for '%s'"
msgstr ""

#: ../../src/clients/kvno/kvno.c:211
msgid "client and server principal names must match"
msgstr ""

#: ../../src/clients/kvno/kvno.c:227
#, c-format
msgid "while getting credentials for %s"
msgstr ""

#: ../../src/clients/kvno/kvno.c:234
#, c-format
msgid "while decoding ticket for %s"
msgstr ""

#: ../../src/clients/kvno/kvno.c:245
#, c-format
msgid "while decrypting ticket for %s"
msgstr ""

#: ../../src/clients/kvno/kvno.c:249
#, c-format
msgid "%s: kvno = %d, keytab entry valid\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:263
#, c-format
msgid "%s: constrained delegation failed"
msgstr ""

#: ../../src/clients/kvno/kvno.c:270
#, c-format
msgid "%s: kvno = %d\n"
msgstr ""

#: ../../src/clients/kvno/kvno.c:337 ../../src/kadmin/cli/kadmin.c:311
msgid "while initializing krb5 library"
msgstr ""

#: ../../src/clients/kvno/kvno.c:344
msgid "while converting etype"
msgstr ""

#: ../../src/clients/kvno/kvno.c:356
msgid "while opening ccache"
msgstr ""

#: ../../src/clients/kvno/kvno.c:381
#, c-format
msgid "while getting user-to-user ticket from %s"
msgstr ""

#: ../../src/clients/kvno/kvno.c:390
msgid "while getting client principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:103
#, c-format
msgid ""
"Usage: %s [-r realm] [-p principal] [-q query] [clnt|local args]\n"
"              [command args...]\n"
"\tclnt args: [-s admin_server[:port]] [[-c ccache]|[-k [-t keytab]]]|[-n]\n"
"\tlocal args: [-x db_args]* [-d dbname] [-e \"enc:salt ...\"] [-m]where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:164
#, c-format
msgid "Invalid date specification \"%s\".\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:192
#, c-format
msgid "Interval specification \"%s\" is in the past.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:322 ../../src/kadmin/cli/kadmin.c:361
#, c-format
msgid "%s: Cannot initialize. Not enough memory\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:380 ../../src/kadmin/cli/kadmin.c:840
#: ../../src/kadmin/cli/kadmin.c:1105 ../../src/kadmin/cli/kadmin.c:1620
#: ../../src/kadmin/cli/keytab.c:148 ../../src/kadmin/dbutil/kdb5_util.c:562
#, c-format
msgid "while parsing keysalts %s"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:403
#, c-format
msgid "%s: -q is exclusive with command-line query"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:411
#, c-format
msgid "%s: unable to get default realm\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:431
msgid "while opening default credentials cache"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:437
#, c-format
msgid "while opening credentials cache %s"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:459 ../../src/kadmin/cli/kadmin.c:513
#: ../../src/kadmin/cli/kadmin.c:521 ../../src/kadmin/cli/kadmin.c:528
#, c-format
msgid "%s: out of memory\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:468 ../../src/kadmin/cli/kadmin.c:483
#: ../../src/kprop/kpropd.c:680
msgid "while canonicalizing principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:477
msgid "creating host service principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:490
#, c-format
msgid "%s: unable to canonicalize principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:533
#, c-format
msgid "%s: unable to figure out a principal name\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:540
msgid "while setting up logging"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:549
#, c-format
msgid "Authenticating as principal %s with existing credentials.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:555
#, c-format
msgid "Authenticating as principal %s with password; anonymous requested.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:562
#, c-format
msgid "Authenticating as principal %s with keytab %s.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:565
#, c-format
msgid "Authenticating as principal %s with default keytab.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:572
#, c-format
msgid "Authenticating as principal %s with password.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:580 ../../src/kprop/kpropd.c:727
#, c-format
msgid "while initializing %s interface"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:595
#, c-format
msgid "while closing ccache %s"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:601
msgid "while mapping update log"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:617
msgid "while unlocking locked database"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:626
msgid "Administration credentials NOT DESTROYED.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:675
msgid "usage: delete_principal [-force] principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:680 ../../src/kadmin/cli/kadmin.c:855
msgid "while parsing principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:686 ../../src/kadmin/cli/kadmin.c:861
#: ../../src/kadmin/cli/kadmin.c:1214 ../../src/kadmin/cli/kadmin.c:1339
#: ../../src/kadmin/cli/kadmin.c:1409 ../../src/kadmin/cli/kadmin.c:1843
#: ../../src/kadmin/cli/kadmin.c:1887 ../../src/kadmin/cli/kadmin.c:1933
#: ../../src/kadmin/cli/kadmin.c:1973
msgid "while canonicalizing principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:690
#, c-format
msgid "Are you sure you want to delete the principal \"%s\"? (yes/no): "
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:694
#, c-format
msgid "Principal \"%s\" not deleted\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:701
#, c-format
msgid "while deleting principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:704
#, c-format
msgid "Principal \"%s\" deleted.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:705
msgid ""
"Make sure that you have removed this principal from all ACLs before "
"reusing.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:722
msgid "usage: rename_principal [-force] old_principal new_principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:729
msgid "while parsing old principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:735
msgid "while parsing new principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:741
msgid "while canonicalizing old principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:747
msgid "while canonicalizing new principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:751
#, c-format
msgid ""
"Are you sure you want to rename the principal \"%s\" to \"%s\"? (yes/no): "
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:755
#, c-format
msgid "Principal \"%s\" not renamed\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:762
#, c-format
msgid "while renaming principal \"%s\" to \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:766
#, c-format
msgid "Principal \"%s\" renamed to \"%s\".\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:767
msgid ""
"Make sure that you have removed the old principal from all ACLs before "
"reusing.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:782
msgid ""
"usage: change_password [-randkey] [-keepold] [-e keysaltlist] [-pw password] "
"principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:808
msgid "change_password: missing db argument"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:814
msgid "change_password: Not enough memory\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:822
msgid "change_password: missing password arg"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:833
msgid "change_password: missing keysaltlist arg"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:849
msgid "missing principal name"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:873 ../../src/kadmin/cli/kadmin.c:910
#, c-format
msgid "while changing password for \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:876 ../../src/kadmin/cli/kadmin.c:913
#, c-format
msgid "Password for \"%s\" changed.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:882 ../../src/kadmin/cli/kadmin.c:1290
#, c-format
msgid "while randomizing key for \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:885
#, c-format
msgid "Key for \"%s\" randomized.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:890 ../../src/kadmin/cli/kadmin.c:1250
#, c-format
msgid "Enter password for principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:892 ../../src/kadmin/cli/kadmin.c:1252
#, c-format
msgid "Re-enter password for principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:897 ../../src/kadmin/cli/kadmin.c:1256
#, c-format
msgid "while reading password for \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:951
msgid "Not enough memory\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:981 ../../src/kadmin/dbutil/kdb5_util.c:594
msgid "while getting time"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1122 ../../src/kadmin/cli/kadmin.c:1333
#: ../../src/kadmin/cli/kadmin.c:1404 ../../src/kadmin/cli/kadmin.c:1837
#: ../../src/kadmin/cli/kadmin.c:1881 ../../src/kadmin/cli/kadmin.c:1927
#: ../../src/kadmin/cli/kadmin.c:1967
msgid "while parsing principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1131
msgid "usage: add_principal [options] principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1132 ../../src/kadmin/cli/kadmin.c:1156
#: ../../src/kadmin/cli/kadmin.c:1643
msgid "\toptions are:\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1133
msgid ""
"\t\t[-randkey|-nokey] [-x db_princ_args]* [-expire expdate] [-pwexpire "
"pwexpdate] [-maxlife maxtixlife]\n"
"\t\t[-kvno kvno] [-policy policy] [-clearpolicy]\n"
"\t\t[-pw password] [-maxrenewlife maxrenewlife]\n"
"\t\t[-e keysaltlist]\n"
"\t\t[{+|-}attribute]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1138 ../../src/kadmin/cli/kadmin.c:1161
msgid "\tattributes are:\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1139 ../../src/kadmin/cli/kadmin.c:1162
msgid ""
"\t\tallow_postdated allow_forwardable allow_tgs_req allow_renewable\n"
"\t\tallow_proxiable allow_dup_skey allow_tix requires_preauth\n"
"\t\trequires_hwauth needchange allow_svr password_changing_service\n"
"\t\tok_as_delegate ok_to_auth_as_delegate no_auth_data_required\n"
"\t\tlockdown_keys\n"
"\n"
"where,\n"
"\t[-x db_princ_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1155
msgid "usage: modify_principal [options] principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1157
msgid ""
"\t\t[-x db_princ_args]* [-expire expdate] [-pwexpire pwexpdate] [-maxlife "
"maxtixlife]\n"
"\t\t[-kvno kvno] [-policy policy] [-clearpolicy]\n"
"\t\t[-maxrenewlife maxrenewlife] [-unlock] [{+|-}attribute]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1221 ../../src/kadmin/cli/kadmin.c:1362
#, c-format
msgid "WARNING: policy \"%s\" does not exist\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1228
#, c-format
msgid "NOTICE: no policy specified for %s; assigning \"default\"\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1234
#, c-format
msgid "WARNING: no policy specified for %s; defaulting to no policy\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1276
#, c-format
msgid "Admin server does not support -nokey while creating \"%s\"\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1298
#, c-format
msgid "while clearing DISALLOW_ALL_TIX for \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1345
#, c-format
msgid "while getting \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1371
#, c-format
msgid "while modifying \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1375
#, c-format
msgid "Principal \"%s\" modified.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1396
msgid "usage: get_principal [-terse] principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1415
#, c-format
msgid "while retrieving \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1420 ../../src/kadmin/cli/kadmin.c:1425
msgid "while unparsing principal"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1429
#, c-format
msgid "Principal: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1430
#, c-format
msgid "Expiration date: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1431 ../../src/kadmin/cli/kadmin.c:1433
#: ../../src/kadmin/cli/kadmin.c:1436 ../../src/kadmin/cli/kadmin.c:1444
msgid "[never]"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1432
#, c-format
msgid "Last password change: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1434
#, c-format
msgid "Password expiration date: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1437
#, c-format
msgid "Maximum ticket life: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1438
#, c-format
msgid "Maximum renewable life: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1440
#, c-format
msgid "Last modified: %s (%s)\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1442
#, c-format
msgid "Last successful authentication: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1448
#, c-format
msgid "Failed password attempts: %d\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1450
#, c-format
msgid "Number of keys: %d\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1457
#, c-format
msgid "<Encryption type 0x%x>"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1464
#, c-format
msgid "<Salt type 0x%x>"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1470
#, c-format
msgid "MKey: vno %d\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1472
#, c-format
msgid "Attributes:"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1475
msgid "while printing flags"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1484
msgid "[none]"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1486
msgid " [does not exist]"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1487
#, c-format
msgid "Policy: %s%s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1523
msgid "usage: get_principals [expression]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1528 ../../src/kadmin/cli/kadmin.c:1779
msgid "while retrieving list."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1633
#, c-format
msgid "%s: parser lost count!\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1642
#, c-format
msgid "usage; %s [options] policy\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1644
msgid ""
"\t\t[-maxlife time] [-minlife time] [-minlength length]\n"
"\t\t[-minclasses number] [-history number]\n"
"\t\t[-maxfailure number] [-failurecountinterval time]\n"
"\t\t[-allowedkeysalts keysalts]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1648
msgid "\t\t[-lockoutduration time]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1667
#, c-format
msgid "while creating policy \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1688
#, c-format
msgid "while modifying policy \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1700
msgid "usage: delete_policy [-force] policy\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1704
#, c-format
msgid "Are you sure you want to delete the policy \"%s\"? (yes/no): "
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1708
#, c-format
msgid "Policy \"%s\" not deleted.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1714
#, c-format
msgid "while deleting policy \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1726
msgid "usage: get_policy [-terse] policy\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1731
#, c-format
msgid "while retrieving policy \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1736
#, c-format
msgid "Policy: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1737
#, c-format
msgid "Maximum password life: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1738
#, c-format
msgid "Minimum password life: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1739
#, c-format
msgid "Minimum password length: %ld\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1740
#, c-format
msgid "Minimum number of password character classes: %ld\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1742
#, c-format
msgid "Number of old keys kept: %ld\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1743
#, c-format
msgid "Maximum password failures before lockout: %lu\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1745
#, c-format
msgid "Password failure count reset interval: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1747
#, c-format
msgid "Password lockout duration: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1750
#, c-format
msgid "Allowed key/salt types: %s\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1774
msgid "usage: get_policies [expression]\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1796
msgid "usage: get_privs\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1801
msgid "while retrieving privileges"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1804
#, c-format
msgid "current privileges:"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1830
msgid "usage: purgekeys [-all|-keepkvno oldest_kvno_to_keep] principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1850
#, c-format
msgid "while purging keys for principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1855
#, c-format
msgid "All keys for principal \"%s\" removed.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1857
#, c-format
msgid "Old keys for principal \"%s\" purged.\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1874
msgid "usage: get_strings principal\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1894
#, c-format
msgid "while getting attributes for principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1899
#, c-format
msgid "(No string attributes.)\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1918
msgid "usage: set_string principal key value\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1940
#, c-format
msgid "while setting attribute on principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1944
#, c-format
msgid "Attribute set for principal \"%s\".\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1959
msgid "usage: del_string principal key\n"
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1980
#, c-format
msgid "while deleting attribute from principal \"%s\""
msgstr ""

#: ../../src/kadmin/cli/kadmin.c:1984
#, c-format
msgid "Attribute removed from principal \"%s\".\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:55
#, c-format
msgid ""
"Usage: ktadd [-k[eytab] keytab] [-q] [-e keysaltlist] [-norandkey] "
"[principal | -glob princ-exp] [...]\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:62
#, c-format
msgid ""
"Usage: ktremove [-k[eytab] keytab] [-q] principal [kvno|\"all\"|\"old\"]\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:76 ../../src/kadmin/cli/keytab.c:97
msgid "while creating keytab name"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:81
msgid "while opening default keytab"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:165
#, c-format
msgid "cannot specify keysaltlist when not changing key\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:181
#, c-format
msgid "while expanding expression \"%s\"."
msgstr ""

#: ../../src/kadmin/cli/keytab.c:200 ../../src/kadmin/cli/keytab.c:240
msgid "while closing keytab"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:314
#, c-format
msgid "while parsing -add principal name %s"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:328
#, c-format
msgid "%s: Principal %s does not exist.\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:331
#, c-format
msgid "while changing %s's key"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:343
msgid "while adding key to keytab"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:348
#, c-format
msgid ""
"Entry for principal %s with kvno %d, encryption type %s added to keytab %s.\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:396
#, c-format
msgid "%s: Keytab %s does not exist.\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:400
#, c-format
msgid "%s: No entry for principal %s exists in keytab %s\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:404
#, c-format
msgid "%s: No entry for principal %s with kvno %d exists in keytab %s\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:410
msgid "while retrieving highest kvno from keytab"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:443
msgid "while temporarily ending keytab scan"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:448
msgid "while deleting entry from keytab"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:453
msgid "while restarting keytab scan"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:459
#, c-format
msgid "Entry for principal %s with kvno %d removed from keytab %s.\n"
msgstr ""

#: ../../src/kadmin/cli/keytab.c:481
#, c-format
msgid "%s: There is only one entry for principal %s in keytab %s\n"
msgstr ""

#: ../../src/kadmin/cli/ss_wrapper.c:53 ../../src/kadmin/ktutil/ktutil.c:58
msgid "creating invocation"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:165
msgid "while allocating temporary filename dump"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:176
msgid "while renaming dump file into place"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:196
msgid "while allocating dump_ok filename"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:202
#, c-format
msgid "while creating 'ok' file, '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:207
#, c-format
msgid "while locking 'ok' file, '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:260 ../../src/kadmin/dbutil/dump.c:289
#, c-format
msgid "%s: regular expression error: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:272
#, c-format
msgid "%s: regular expression match error: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:373
#, c-format
msgid "%s: tagged data list inconsistency for %s (counted %d, stored %d)\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:532
#, c-format
msgid ""
"Warning!  Multiple DES-CBC-CRC keys for principal %s; skipping duplicates.\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:543
#, c-format
msgid ""
"Warning!  No DES-CBC-CRC key for principal %s, cannot generate OV-compatible "
"record; skipping\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:572
#, c-format
msgid "while converting %s to new master key"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:593
#, c-format
msgid "%s(%d): %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:636
#, c-format
msgid "%s(%d): ignoring trash at end of line: "
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:699
msgid "cannot read tagged data type and length"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:703
msgid "data type or length overflowed"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:710
msgid "cannot read tagged data contents"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:743
msgid "cannot match size tokens"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:754
msgid "cannot allocate tl_data (too large)"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:776
msgid "cannot read name string"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:781
#, c-format
msgid "while parsing name %s"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:789
msgid "cannot read principal attributes"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:842
msgid "cannot read key size and version"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:846
msgid "unsupported key_data_ver version"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:857
msgid "cannot read key type and length"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:863
msgid "cannot read key data"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:873
msgid "cannot read extra data"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:882
#, c-format
msgid "while storing %s"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:921 ../../src/kadmin/dbutil/dump.c:960
#: ../../src/kadmin/dbutil/dump.c:1006 ../../src/kadmin/dbutil/dump.c:1025
#, c-format
msgid "cannot parse policy (%d read)\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:929 ../../src/kadmin/dbutil/dump.c:968
#: ../../src/kadmin/dbutil/dump.c:1046
msgid "while creating policy"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:933
#, c-format
msgid "created policy %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1083
#, c-format
msgid "unknown record type \"%s\"\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1212
#, c-format
msgid "%s: Unknown iprop dump version %d\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1316 ../../src/kadmin/dbutil/dump.c:1544
#, c-format
msgid "Iprop not enabled\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1353
msgid "Conditional dump is an undocumented option for use only for iprop dumps"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1366
msgid "Database not currently opened!"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1380 ../../src/kadmin/dbutil/kdb5_stash.c:116
#: ../../src/kadmin/dbutil/kdb5_util.c:448
msgid "while reading master key"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1386
msgid "while verifying master key"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1405 ../../src/kadmin/dbutil/dump.c:1415
msgid "while reading new master key"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1409
#, c-format
msgid "Please enter new master key....\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1433
#, c-format
msgid "while opening %s for writing"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1448
msgid "while reading update log header"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1463 ../../src/kadmin/dbutil/dump.c:1471
#, c-format
msgid "performing %s dump"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1501
#, c-format
msgid "%s: error processing line %d of %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1553
msgid "while parsing options"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1568
#, c-format
msgid "while opening %s"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1573 ../../src/kadmin/dbutil/dump.c:1672
msgid "standard input"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1578
#, c-format
msgid "%s: can't read dump header in %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1586 ../../src/kadmin/dbutil/dump.c:1603
#, c-format
msgid "%s: dump header bad in %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1612
#, c-format
msgid "Could not open iprop ulog\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1617
#, c-format
msgid "%s: dump version %s can only be loaded with the -update flag\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1626 ../../src/kadmin/dbutil/dump.c:1631
msgid "computing parameters for database"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1637
msgid "while creating database"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1646
msgid "while opening database"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1656
msgid "while permanently locking database"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1674
#, c-format
msgid "%s: %s restore failed\n"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1679
msgid "while unlocking database"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1689 ../../src/kadmin/dbutil/dump.c:1708
msgid "while reinitializing update log"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1699
msgid "while making newly loaded database live"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1715
msgid "while writing update log header"
msgstr ""

#: ../../src/kadmin/dbutil/dump.c:1729
#, c-format
msgid "while deleting bad database %s"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:79
msgid "while looking up the Kerberos configuration"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:105
msgid "while initializing the Kerberos admin interface"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:152
msgid "while canonicalizing local hostname"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:157
#: ../../src/kadmin/dbutil/kadm5_create.c:162
#, c-format
msgid "Out of memory\n"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:238
msgid "while appending realm to principal"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:243
msgid "while parsing admin principal name"
msgstr ""

#: ../../src/kadmin/dbutil/kadm5_create.c:254
#, c-format
msgid "while creating principal %s"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:191
#, c-format
msgid "Loading random data\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:194
msgid "Loading random data"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:204
#: ../../src/kadmin/dbutil/kdb5_util.c:392
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:608
msgid "while setting up master key name"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:215
#, c-format
msgid ""
"Initializing database '%s' for realm '%s',\n"
"master key name '%s'\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:220
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:517
#, c-format
msgid "You will be prompted for the database Master Password.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:221
#: ../../src/kadmin/dbutil/kdb5_mkey.c:255
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:518
#, c-format
msgid "It is important that you NOT FORGET this password.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:227
#: ../../src/kadmin/dbutil/kdb5_mkey.c:261
msgid "while creating new master key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:235
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:528
msgid "while reading master key from keyboard"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:245
#: ../../src/kadmin/dbutil/kdb5_mkey.c:280
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:620
msgid "while calculating master key salt"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:253
#: ../../src/kadmin/dbutil/kdb5_mkey.c:289
#: ../../src/kadmin/dbutil/kdb5_util.c:434
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:632
msgid "while transforming master key from password"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:263
msgid "while initializing random key generator"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:268
#, c-format
msgid "while creating database '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:286
msgid "while creating update log"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:297
msgid "while initializing update log"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:312
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:644
msgid "while adding entries to the database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:340
#: ../../src/kadmin/dbutil/kdb5_mkey.c:333
#: ../../src/kadmin/dbutil/kdb5_stash.c:133
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:669
msgid "while storing key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_create.c:341
#: ../../src/kadmin/dbutil/kdb5_mkey.c:334
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:670
#, c-format
msgid "Warning: couldn't stash master key.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_destroy.c:69
#, c-format
msgid "Deleting KDC database stored in '%s', are you sure?\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_destroy.c:71
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1111
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:360
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1476
#, c-format
msgid "(type 'yes' to confirm)? "
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_destroy.c:78
#, c-format
msgid "OK, deleting database '%s'...\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_destroy.c:83
#, c-format
msgid "deleting database '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_destroy.c:92
#, c-format
msgid "** Database '%s' destroyed.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:223
#, c-format
msgid "%s is an invalid enctype"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:245
#: ../../src/kadmin/dbutil/kdb5_mkey.c:421
#: ../../src/kadmin/dbutil/kdb5_mkey.c:564
#: ../../src/kadmin/dbutil/kdb5_mkey.c:941
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1102
#, c-format
msgid "while getting master key principal %s"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:251
#, c-format
msgid "Creating new master key for master key principal '%s'\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:254
#, c-format
msgid "You will be prompted for a new database Master Password.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:270
msgid "while reading new master key from keyboard"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:299
msgid "adding new master key to master principal"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:305
#: ../../src/kadmin/dbutil/kdb5_mkey.c:390
#: ../../src/kadmin/dbutil/kdb5_mkey.c:806
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1307
msgid "while getting current time"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:312
#: ../../src/kadmin/dbutil/kdb5_mkey.c:522
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1314
msgid "while updating the master key principal modification time"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:319
#: ../../src/kadmin/dbutil/kdb5_mkey.c:530
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1324
msgid "while adding master key entry to the database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:371
msgid "0 is an invalid KVNO value"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:382
#, c-format
msgid "%d is an invalid KVNO value"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:398
#, c-format
msgid "could not parse date-time string '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:430
msgid "while looking up active version of master key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:469
msgid "while adding new master key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:507
msgid "there must be one master key currently active"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:515
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1293
msgid "while updating actkvno data for master principal entry"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:556
#: ../../src/kadmin/dbutil/kdb5_mkey.c:903
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1072
msgid "master keylist not initialized"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:572
#: ../../src/kadmin/dbutil/kdb5_mkey.c:949
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1199
msgid "while looking up active kvno list"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:580
#: ../../src/kadmin/dbutil/kdb5_mkey.c:957
msgid "while looking up active master key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:592
msgid "while getting enctype description"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:609
#, c-format
msgid "KVNO: %d, Enctype: %s, Active on: %s *\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:614
#, c-format
msgid "KVNO: %d, Enctype: %s, Active on: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:618
#, c-format
msgid "KVNO: %d, Enctype: %s, No activate time set\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:623
msgid "asprintf could not allocate enough memory to hold output"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:756
msgid "getting string representation of principal name"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:780
#, c-format
msgid "determining master key used for principal '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:786
#, c-format
msgid "would skip:   %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:788
#, c-format
msgid "skipping: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:794
#, c-format
msgid "would update: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:798
#, c-format
msgid "updating: %s\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:802
#, c-format
msgid "error re-encrypting key for principal '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:813
#, c-format
msgid "while updating principal '%s' modification time"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:820
#, c-format
msgid "while updating principal '%s' key data in the database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:852
#, c-format
msgid ""
"\n"
"(type 'yes' to confirm)? "
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:914
#, c-format
msgid "converting glob pattern '%s' to regular expression"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:932
#, c-format
msgid "error compiling converted regexp '%s'"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:965
#, c-format
msgid "Re-encrypt all keys not using master key vno %u?"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:967
#, c-format
msgid "OK, doing nothing.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:973
#, c-format
msgid "Principals whose keys WOULD BE re-encrypted to master key vno %u:\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:976
#, c-format
msgid ""
"Principals whose keys are being re-encrypted to master key vno %u if "
"necessary:\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:992
msgid "trying to process principal database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:996
#, c-format
msgid "%u principals processed: %u would be updated, %u already current\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1000
#, c-format
msgid "%u principals processed: %u updated, %u already current\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1109
#, c-format
msgid ""
"Will purge all unused master keys stored in the '%s' principal, are you "
"sure?\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1120
#, c-format
msgid "OK, purging unused master keys from '%s'...\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1128
#, c-format
msgid "There is only one master key which can not be purged.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1137
msgid "while allocating args.kvnos"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1153
msgid "while finding master keys in use"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1162
#, c-format
msgid "Would purge the following master key(s) from %s:\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1165
#, c-format
msgid "Purging the following master key(s) from %s:\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1177
msgid "master key stash file needs updating, command aborting"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1183
#, c-format
msgid "KVNO: %d\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1188
#, c-format
msgid "All keys in use, nothing purged.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1193
#, c-format
msgid "%d key(s) would be purged.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1206
msgid "while looking up mkey aux data list"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1214
msgid "while allocating key_data"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1301
msgid "while updating mkey_aux data for master principal entry"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1328
#, c-format
msgid "%d key(s) purged.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_stash.c:97
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:542
#, c-format
msgid "while setting up enctype %d"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_stash.c:123
msgid "while getting master key list"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_stash.c:127
#, c-format
msgid "Using existing stashed keys to update stash file.\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:80
#, c-format
msgid ""
"Usage: kdb5_util [-r realm] [-d dbname] [-k mkeytype] [-kv mkeyVNO]\n"
"\t        [-M mkeyname] [-m] [-sf stashfilename] [-P password]\n"
"\t        [-x db_args]* cmd [cmd_options]\n"
"\tcreate  [-s]\n"
"\tdestroy [-f]\n"
"\tstash   [-f keyfile]\n"
"\tdump    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose]\n"
"\t        [-mkey_convert] [-new_mkey_file mkey_file]\n"
"\t        [-rev] [-recurse] [filename [princs...]]\n"
"\tload    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose] [-update] filename\n"
"\tark     [-e etype_list] principal\n"
"\tadd_mkey [-e etype] [-s]\n"
"\tuse_mkey kvno [time]\n"
"\tlist_mkeys\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:99
#, c-format
msgid ""
"\tupdate_princ_encryption [-f] [-n] [-v] [princ-pattern]\n"
"\tpurge_mkeys [-f] [-n] [-v]\n"
"\ttabdump [-H] [-c] [-e] [-n] [-o outfile] dumptype\n"
"\n"
"where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:215
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:264
msgid "while initializing Kerberos code"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:221
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:271
msgid "while creating sub-command arguments"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:239
msgid "while parsing command arguments"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:245
#: ../../src/kadmin/dbutil/kdb5_util.c:252
msgid "while parsing command arguments\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:263
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:295
msgid "while setting default realm name"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:268
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:302
#, c-format
msgid ": %s is an invalid enctype"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:276
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:311
#, c-format
msgid ": %s is an invalid mkeyVNO"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:321
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:435
msgid "while retreiving configuration parameters"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:381
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:883
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1491
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:568
msgid "while initializing database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:398
msgid "while retrieving master entry"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:417
msgid "while calculated master key salt"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:449
msgid "Warning: proceeding without master key"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:467
msgid "while seeding random number generator"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:477
#, c-format
msgid "%s: Could not map log\n"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:506
msgid "while closing database"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:553
#, c-format
msgid "while fetching principal %s"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:576
msgid "while finding mkey"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:601
msgid "while setting changetime"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:609
#, c-format
msgid "while saving principal %s"
msgstr ""

#: ../../src/kadmin/dbutil/kdb5_util.c:613
#, c-format
msgid "%s changed\n"
msgstr ""

#: ../../src/kadmin/dbutil/tabdump.c:573
#, c-format
msgid "opening %s for writing"
msgstr ""

#: ../../src/kadmin/dbutil/tabdump.c:659
msgid "performing tabular dump"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:73
#, c-format
msgid "%s: invalid arguments\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:78
msgid "while freeing ktlist"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:89
#, c-format
msgid "%s: must specify keytab to read\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:94
#, c-format
msgid "while reading keytab \"%s\""
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:104
#, c-format
msgid "%s: must specify the srvtab to read\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:109
#, c-format
msgid "while reading srvtab \"%s\""
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:119
#, c-format
msgid "%s: must specify keytab to write\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:124
#, c-format
msgid "while writing keytab \"%s\""
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:131
#, c-format
msgid "%s: writing srvtabs is no longer supported\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:178
#, c-format
msgid ""
"usage: %s (-key | -password) -p principal -k kvno [-e enctype] [-f|-s salt]\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:183
#, c-format
msgid "enctype must be specified if not using -f\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:190
msgid "while adding new entry"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:200
#, c-format
msgid "%s: must specify entry to delete\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:205
#, c-format
msgid "while deleting entry %d"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:233
#, c-format
msgid "%s: usage: %s [-t] [-k] [-e]\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil.c:272
msgid "While converting enctype to string"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil_funcs.c:196
#, c-format
msgid "Password for %.1000s"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil_funcs.c:214
#, c-format
msgid "Key for %s (hex): "
msgstr ""

#: ../../src/kadmin/ktutil/ktutil_funcs.c:226
#, c-format
msgid "addent: Error reading key.\n"
msgstr ""

#: ../../src/kadmin/ktutil/ktutil_funcs.c:234
#, c-format
msgid "addent: Illegal character in key.\n"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:240
#, c-format
msgid "%s: invalid restrictions: %s"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:288
#, c-format
msgid "Unrecognized ACL operation '%c' in %s"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:296
#, c-format
msgid "Cannot parse client principal '%s'"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:304
#, c-format
msgid "Cannot parse target principal '%s'"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:400
#, c-format
msgid "%s while opening ACL file %s"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:403
#, c-format
msgid "Cannot open %s: %s"
msgstr ""

#: ../../src/kadmin/server/auth_acl.c:419
#: ../../src/kadmin/server/auth_acl.c:422
#, c-format
msgid "%s: syntax error at line %d <%.10s...>"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:49
#, c-format
msgid "Unauthorized request: %s, client=%s, service=%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:50
#: ../../src/kadmin/server/ipropd_svc.c:224
#, c-format
msgid "Request: %s, %s, %s, client=%s, service=%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:164
#: ../../src/kadmin/server/ipropd_svc.c:283
#, c-format
msgid "%s: server handle is NULL"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:174
#: ../../src/kadmin/server/ipropd_svc.c:296
#, c-format
msgid "%s: setup_gss_names failed"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:182
#: ../../src/kadmin/server/ipropd_svc.c:305
#, c-format
msgid "%s: out of memory recording principal names"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:207
#, c-format
msgid "%s; Incoming SerialNo=%lu; Outgoing SerialNo=%lu"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:213
#, c-format
msgid "%s; Incoming SerialNo=%lu; Outgoing SerialNo=N/A"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:326
#, c-format
msgid "%s: getclhoststr failed"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:348
#, c-format
msgid "%s: cannot construct kdb5 util dump string too long; out of memory"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:368
#, c-format
msgid "%s: fork failed: %s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:380
#, c-format
msgid "%s: popen failed: %s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:394
#, c-format
msgid "%s: pclose(popen) failed: %s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:414
#, c-format
msgid "%s: exec failed: %s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:430
#, c-format
msgid "Request: %s, spawned resync process %d, client=%s, service=%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:494
#: ../../src/kadmin/server/kadm_rpc_svc.c:306
#, c-format
msgid "check_rpcsec_auth: failed inquire_context, stat=%u"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:524
#: ../../src/kadmin/server/kadm_rpc_svc.c:335
#, c-format
msgid "bad service principal %.*s%s"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:547
#, c-format
msgid "authentication attempt failed: %s, RPC authentication flavor %d"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:581
#, c-format
msgid "RPC unknown request: %d (%s)"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:589
#, c-format
msgid "RPC svc_getargs failed (%s)"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:599
#, c-format
msgid "RPC svc_sendreply failed (%s)"
msgstr ""

#: ../../src/kadmin/server/ipropd_svc.c:605
#, c-format
msgid "RPC svc_freeargs failed (%s)"
msgstr ""

#: ../../src/kadmin/server/kadm_rpc_svc.c:356
#, c-format
msgid "gss_to_krb5_name: failed display_name status %d"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:86
#, c-format
msgid ""
"Usage: kadmind [-x db_args]* [-r realm] [-m] [-nofork] [-port port-number]\n"
"\t\t[-proponly] [-p path-to-kdb5_util] [-F dump-file]\n"
"\t\t[-K path-to-kprop] [-k kprop-port] [-P pid_file]\n"
"\n"
"where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:110
#, c-format
msgid "%s: %s while %s, aborting\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:112
#, c-format
msgid "%s while %s, aborting\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:114
#, c-format
msgid "%s: %s, aborting\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:115
#, c-format
msgid "%s, aborting"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:282
#, c-format
msgid ""
"WARNING! Forged/garbled request: %s, claimed client = %.*s%s, server = %.*s"
"%s, addr = %s"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:288
#, c-format
msgid ""
"WARNING! Forged/garbled request: %d, claimed client = %.*s%s, server = %.*s"
"%s, addr = %s"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:302
#, c-format
msgid "Miscellaneous RPC error: %s, %s"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:318
#, c-format
msgid "%s Cannot decode status %d"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:336
#, c-format
msgid "Authentication attempt failed: %s, GSS-API error strings are:"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:341
msgid "   GSS-API error strings complete."
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:379
#, c-format
msgid "%s: cannot initialize. Not enough memory\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:451
#, c-format
msgid "%s: %s while initializing context, aborting\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:462
msgid "initializing"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:466
msgid "getting config parameters"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:468
msgid "Missing required realm configuration"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:470
msgid "Missing required ACL file configuration"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:472
msgid "-proponly can only be used when iprop_enable is true"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:478
msgid "initializing network"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:483
msgid "Cannot build GSSAPI auth names"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:487
msgid "Cannot set up KDB keytab"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:490
msgid "Cannot set GSSAPI authentication names"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:507
msgid "Cannot initialize GSSAPI service name"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:512
msgid "initializing ACL file"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:515
msgid "spawning daemon process"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:519
msgid "creating PID file"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:522
msgid "Seeding random number generator"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:525
msgid "getting random seed"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:532
msgid "mapping update log"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:536
#, c-format
msgid "%s: create IPROP svc (PROG=%d, VERS=%d)\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:544
msgid "starting"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:546 ../../src/kdc/main.c:1047
#, c-format
msgid "%s: starting...\n"
msgstr ""

#: ../../src/kadmin/server/ovsec_kadmd.c:549
msgid "finished, exiting"
msgstr ""

#: ../../src/kadmin/server/schpw.c:273
#, c-format
msgid "setpw request from %s by %.*s%s for %.*s%s: %s"
msgstr ""

#: ../../src/kadmin/server/schpw.c:278
#, c-format
msgid "chpw request from %s for %.*s%s: %s"
msgstr ""

#: ../../src/kadmin/server/schpw.c:446
#, c-format
msgid "chpw: Couldn't open admin keytab %s"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:394
#, c-format
msgid ""
"Unauthorized request: %s, %.*s%s, client=%.*s%s, service=%.*s%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:415
#: ../../src/kadmin/server/server_stubs.c:693
#: ../../src/kadmin/server/server_stubs.c:1618
msgid "success"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:425
#, c-format
msgid "Request: %s, %.*s%s, %s, client=%.*s%s, service=%.*s%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:673
#, c-format
msgid ""
"Unauthorized request: kadm5_rename_principal, %.*s%s to %.*s%s, client=%.*s"
"%s, service=%.*s%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:688
#, c-format
msgid ""
"Request: kadm5_rename_principal, %.*s%s to %.*s%s, %s, client=%.*s%s, "
"service=%.*s%s, addr=%s"
msgstr ""

#: ../../src/kadmin/server/server_stubs.c:1614
#, c-format
msgid ""
"Request: kadm5_init, %.*s%s, %s, client=%.*s%s, service=%.*s%s, addr=%s, "
"vers=%d, flavor=%d"
msgstr ""

#: ../../src/kdc/do_as_req.c:301
#, c-format
msgid "AS_REQ : handle_authdata (%d)"
msgstr ""

#: ../../src/kdc/do_tgs_req.c:643
msgid "not checking transit path"
msgstr ""

#: ../../src/kdc/do_tgs_req.c:666
#, c-format
msgid "TGS_REQ : handle_authdata (%d)"
msgstr ""

#: ../../src/kdc/fast_util.c:56
#, c-format
msgid "%s while handling ap-request armor"
msgstr ""

#: ../../src/kdc/fast_util.c:65
msgid "ap-request armor for something other than the local TGS"
msgstr ""

#: ../../src/kdc/fast_util.c:74
msgid "ap-request armor without subkey"
msgstr ""

#: ../../src/kdc/fast_util.c:162
msgid "Ap-request armor not permitted with TGS"
msgstr ""

#: ../../src/kdc/fast_util.c:169
#, c-format
msgid "Unknown FAST armor type %d"
msgstr ""

#: ../../src/kdc/fast_util.c:183
msgid "No armor key but FAST armored request present"
msgstr ""

#: ../../src/kdc/fast_util.c:218
msgid "FAST req_checksum invalid; request modified"
msgstr ""

#: ../../src/kdc/fast_util.c:224
msgid "Unkeyed checksum used in fast_req"
msgstr ""

#: ../../src/kdc/kdc_audit.c:110
#, c-format
msgid "audit plugin %s failed to open. error=%i"
msgstr ""

#: ../../src/kdc/kdc_authdata.c:78
#, c-format
msgid "while loading authdata module %s"
msgstr ""

#: ../../src/kdc/kdc_log.c:84
#, c-format
msgid "AS_REQ (%s) %s: ISSUE: authtime %u, %s, %s for %s"
msgstr ""

#: ../../src/kdc/kdc_log.c:90
#, c-format
msgid "AS_REQ (%s) %s: %s: %s for %s%s%s"
msgstr ""

#: ../../src/kdc/kdc_log.c:154
#, c-format
msgid "TGS_REQ (%s) %s: %s: authtime %u, %s%s %s for %s%s%s"
msgstr ""

#: ../../src/kdc/kdc_log.c:161
#, c-format
msgid "... PROTOCOL-TRANSITION s4u-client=%s"
msgstr ""

#: ../../src/kdc/kdc_log.c:165
#, c-format
msgid "... CONSTRAINED-DELEGATION s4u-client=%s"
msgstr ""

#: ../../src/kdc/kdc_log.c:169
#, c-format
msgid "TGS_REQ %s: %s: authtime %u, %s for %s, 2nd tkt client %s"
msgstr ""

#: ../../src/kdc/kdc_log.c:203
#, c-format
msgid "bad realm transit path from '%s' to '%s' via '%.*s%s'"
msgstr ""

#: ../../src/kdc/kdc_log.c:209
#, c-format
msgid "unexpected error checking transit from '%s' to '%s' via '%.*s%s': %s"
msgstr ""

#: ../../src/kdc/kdc_log.c:227
msgid "TGS_REQ: issuing alternate <un-unparseable> TGT"
msgstr ""

#: ../../src/kdc/kdc_log.c:230
#, c-format
msgid "TGS_REQ: issuing TGT %s"
msgstr ""

#: ../../src/kdc/kdc_preauth.c:215
#, c-format
msgid "preauth %s failed to initialize: %s"
msgstr ""

#: ../../src/kdc/kdc_preauth.c:226
#, c-format
msgid "preauth %s failed to setup loop: %s"
msgstr ""

#: ../../src/kdc/kdc_preauth.c:914
#, c-format
msgid "%spreauth required but hint list is empty"
msgstr ""

#: ../../src/kdc/kdc_preauth_ec.c:76
msgid "Encrypted Challenge used outside of FAST tunnel"
msgstr ""

#: ../../src/kdc/kdc_preauth_ec.c:120
msgid "Incorrect password in encrypted challenge"
msgstr ""

#: ../../src/kdc/kdc_util.c:236
msgid "TGS_REQ: SESSION KEY or MUTUAL"
msgstr ""

#: ../../src/kdc/kdc_util.c:314
msgid "PROCESS_TGS: failed lineage check"
msgstr ""

#: ../../src/kdc/kdc_util.c:468
#, c-format
msgid "TGS_REQ: UNKNOWN SERVER: server='%s'"
msgstr ""

#: ../../src/kdc/kdc_util.c:798
#, c-format
msgid "Required auth indicators not present in ticket: %s"
msgstr ""

#: ../../src/kdc/main.c:230
#, c-format
msgid "while getting context for realm %s"
msgstr ""

#: ../../src/kdc/main.c:338
#, c-format
msgid "while setting default realm to %s"
msgstr ""

#: ../../src/kdc/main.c:346
#, c-format
msgid "while initializing database for realm %s"
msgstr ""

#: ../../src/kdc/main.c:355
#, c-format
msgid "while setting up master key name %s for realm %s"
msgstr ""

#: ../../src/kdc/main.c:368
#, c-format
msgid "while fetching master key %s for realm %s"
msgstr ""

#: ../../src/kdc/main.c:376
#, c-format
msgid "while fetching master keys list for realm %s"
msgstr ""

#: ../../src/kdc/main.c:385
#, c-format
msgid "while resolving kdb keytab for realm %s"
msgstr ""

#: ../../src/kdc/main.c:394
#, c-format
msgid "while building TGS name for realm %s"
msgstr ""

#: ../../src/kdc/main.c:512
#, c-format
msgid "creating %d worker processes"
msgstr ""

#: ../../src/kdc/main.c:522
msgid "Unable to reinitialize main loop"
msgstr ""

#: ../../src/kdc/main.c:527
#, c-format
msgid "Unable to initialize signal handlers in pid %d"
msgstr ""

#: ../../src/kdc/main.c:557
#, c-format
msgid "worker %ld exited with status %d"
msgstr ""

#: ../../src/kdc/main.c:581
#, c-format
msgid "signal %d received in supervisor"
msgstr ""

#: ../../src/kdc/main.c:593
#, c-format
msgid ""
"usage: %s [-x db_args]* [-d dbpathname] [-r dbrealmname]\n"
"\t\t[-R replaycachename] [-m] [-k masterenctype]\n"
"\t\t[-M masterkeyname] [-p port] [-P pid_file]\n"
"\t\t[-n] [-w numworkers] [/]\n"
"\n"
"where,\n"
"\t[-x db_args]* - Any number of database specific arguments.\n"
"\t\t\tLook at each database module documentation for \t\t\tsupported "
"arguments\n"
msgstr ""

#: ../../src/kdc/main.c:668 ../../src/kdc/main.c:675 ../../src/kdc/main.c:790
#, c-format
msgid " KDC cannot initialize. Not enough memory\n"
msgstr ""

#: ../../src/kdc/main.c:694 ../../src/kdc/main.c:737 ../../src/kdc/main.c:748
#, c-format
msgid "%s: KDC cannot initialize. Not enough memory\n"
msgstr ""

#: ../../src/kdc/main.c:714 ../../src/kdc/main.c:827
#, c-format
msgid "%s: cannot initialize realm %s - see log file for details\n"
msgstr ""

#: ../../src/kdc/main.c:725
#, c-format
msgid "%s: cannot initialize realm %s. Not enough memory\n"
msgstr ""

#: ../../src/kdc/main.c:776
#, c-format
msgid "invalid enctype %s"
msgstr ""

#: ../../src/kdc/main.c:815
msgid "while attempting to retrieve default realm"
msgstr ""

#: ../../src/kdc/main.c:817
#, c-format
msgid "%s: %s, attempting to retrieve default realm\n"
msgstr ""

#: ../../src/kdc/main.c:925
#, c-format
msgid "%s: cannot get memory for realm list\n"
msgstr ""

#: ../../src/kdc/main.c:960
msgid "while initializing lookaside cache"
msgstr ""

#: ../../src/kdc/main.c:968
msgid "while creating main loop"
msgstr ""

#: ../../src/kdc/main.c:977
msgid "while loading KDC policy plugin"
msgstr ""

#: ../../src/kdc/main.c:1002
msgid "while initializing signal handlers"
msgstr ""

#: ../../src/kdc/main.c:1010
msgid "while initializing network"
msgstr ""

#: ../../src/kdc/main.c:1015
msgid "while detaching from tty"
msgstr ""

#: ../../src/kdc/main.c:1022
msgid "while creating PID file"
msgstr ""

#: ../../src/kdc/main.c:1031
msgid "creating worker processes"
msgstr ""

#: ../../src/kdc/main.c:1041
msgid "while loading audit plugin module(s)"
msgstr ""

#: ../../src/kdc/main.c:1045
msgid "commencing operation"
msgstr ""

#: ../../src/kdc/main.c:1053
msgid "shutting down"
msgstr ""

#: ../../src/kdc/policy.c:230
#, c-format
msgid "while loading policy module %s"
msgstr ""

#: ../../src/kprop/kprop.c:85
#, c-format
msgid ""
"\n"
"Usage: %s [-r realm] [-f file] [-d] [-P port] [-s srvtab] replica_host\n"
"\n"
msgstr ""

#: ../../src/kprop/kprop.c:114
#, c-format
msgid "Database propagation to %s: SUCCEEDED\n"
msgstr ""

#: ../../src/kprop/kprop.c:175
msgid "while setting client principal name"
msgstr ""

#: ../../src/kprop/kprop.c:184
msgid "while setting server principal name"
msgstr ""

#: ../../src/kprop/kprop.c:197
msgid "while resolving keytab"
msgstr ""

#: ../../src/kprop/kprop.c:205
msgid "while getting initial credentials\n"
msgstr ""

#: ../../src/kprop/kprop.c:241
msgid "while creating socket"
msgstr ""

#: ../../src/kprop/kprop.c:257
msgid "while converting server address"
msgstr ""

#: ../../src/kprop/kprop.c:267
msgid "while connecting to server"
msgstr ""

#: ../../src/kprop/kprop.c:274 ../../src/kprop/kpropd.c:1199
msgid "while getting local socket address"
msgstr ""

#: ../../src/kprop/kprop.c:279
msgid "while converting local address"
msgstr ""

#: ../../src/kprop/kprop.c:302
msgid "in krb5_auth_con_setaddrs"
msgstr ""

#: ../../src/kprop/kprop.c:310
msgid "while authenticating to server"
msgstr ""

#: ../../src/kprop/kprop.c:314 ../../src/kprop/kprop.c:513
#: ../../src/kprop/kpropd.c:1505
#, c-format
msgid "Generic remote error: %s\n"
msgstr ""

#: ../../src/kprop/kprop.c:320 ../../src/kprop/kprop.c:519
msgid "signalled from server"
msgstr ""

#: ../../src/kprop/kprop.c:322 ../../src/kprop/kprop.c:521
#, c-format
msgid "Error text from server: %s\n"
msgstr ""

#: ../../src/kprop/kprop.c:350
#, c-format
msgid "allocating database file name '%s'"
msgstr ""

#: ../../src/kprop/kprop.c:356
#, c-format
msgid "while trying to open %s"
msgstr ""

#: ../../src/kprop/kprop.c:363
msgid "database locked"
msgstr ""

#: ../../src/kprop/kprop.c:366 ../../src/kprop/kpropd.c:552
#, c-format
msgid "while trying to lock '%s'"
msgstr ""

#: ../../src/kprop/kprop.c:370 ../../src/kprop/kprop.c:378
#, c-format
msgid "while trying to stat %s"
msgstr ""

#: ../../src/kprop/kprop.c:374
msgid "while trying to malloc data_ok_fn"
msgstr ""

#: ../../src/kprop/kprop.c:383
#, c-format
msgid "'%s' more recent than '%s'."
msgstr ""

#: ../../src/kprop/kprop.c:399
#, c-format
msgid "while unlocking database '%s'"
msgstr ""

#: ../../src/kprop/kprop.c:432 ../../src/kprop/kprop.c:433
msgid "while encoding database size"
msgstr ""

#: ../../src/kprop/kprop.c:441
msgid "while sending database size"
msgstr ""

#: ../../src/kprop/kprop.c:451
msgid "while allocating i_vector"
msgstr ""

#: ../../src/kprop/kprop.c:474
#, c-format
msgid "while sending database block starting at %d"
msgstr ""

#: ../../src/kprop/kprop.c:484
msgid "Premature EOF found for database file!"
msgstr ""

#: ../../src/kprop/kprop.c:497
msgid "while reading response from server"
msgstr ""

#: ../../src/kprop/kprop.c:508
msgid "while decoding error response from server"
msgstr ""

#: ../../src/kprop/kprop.c:539
#, c-format
msgid "Kpropd sent database size %d, expecting %d"
msgstr ""

#: ../../src/kprop/kprop.c:584
msgid "while allocating filename for update_last_prop_file"
msgstr ""

#: ../../src/kprop/kprop.c:589
#, c-format
msgid "while creating 'last_prop' file, '%s'"
msgstr ""

#: ../../src/kprop/kpropd.c:171
#, c-format
msgid ""
"\n"
"Usage: %s [-r realm] [-s srvtab] [-dS] [-f replica_file]\n"
msgstr ""

#: ../../src/kprop/kpropd.c:173
#, c-format
msgid "\t[-F kerberos_db_file ] [-p kdb5_util_pathname]\n"
msgstr ""

#: ../../src/kprop/kpropd.c:174
#, c-format
msgid "\t[-x db_args]* [-P port] [-a acl_file]\n"
msgstr ""

#: ../../src/kprop/kpropd.c:175
#, c-format
msgid "\t[-A admin_server] [--pid-file=pid_file]\n"
msgstr ""

#: ../../src/kprop/kpropd.c:231
#, c-format
msgid "Killing fullprop child (%d)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:260
msgid "while checking if stdin is a socket"
msgstr ""

#: ../../src/kprop/kpropd.c:278
#, c-format
msgid "ready\n"
msgstr ""

#: ../../src/kprop/kpropd.c:284
#, c-format
msgid "Could not write pid file %s: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:296
#, c-format
msgid "Could not open /dev/null: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:303
#, c-format
msgid "Could not dup the inetd socket: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:338 ../../src/kprop/kpropd.c:351
msgid "do_iprop failed.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:390
#, c-format
msgid "getaddrinfo: %s\n"
msgstr ""

#: ../../src/kprop/kpropd.c:396
msgid "while obtaining socket"
msgstr ""

#: ../../src/kprop/kpropd.c:402
msgid "while setting SO_REUSEADDR option"
msgstr ""

#: ../../src/kprop/kpropd.c:410
msgid "while unsetting IPV6_V6ONLY option"
msgstr ""

#: ../../src/kprop/kpropd.c:415
msgid "while binding listener socket"
msgstr ""

#: ../../src/kprop/kpropd.c:426
#, c-format
msgid "waiting for a kprop connection\n"
msgstr ""

#: ../../src/kprop/kpropd.c:432
msgid "while accepting connection"
msgstr ""

#: ../../src/kprop/kpropd.c:438
msgid "while forking"
msgstr ""

#: ../../src/kprop/kpropd.c:453
#, c-format
msgid "waitpid() failed to wait for doit() (%d %s)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:457
msgid "while waiting to receive database"
msgstr ""

#: ../../src/kprop/kpropd.c:461
#, c-format
msgid "Database load process for full propagation completed.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:499
#, c-format
msgid ""
"%s: Standard input does not appear to be a network socket.\n"
"\t(Not run from inetd, and missing the -S option?)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:512
msgid "while attempting setsockopt (SO_KEEPALIVE)"
msgstr ""

#: ../../src/kprop/kpropd.c:517
#, c-format
msgid "Connection from %s"
msgstr ""

#: ../../src/kprop/kpropd.c:537
#, c-format
msgid "Rejected connection from unauthorized principal %s\n"
msgstr ""

#: ../../src/kprop/kpropd.c:541
#, c-format
msgid "Rejected connection from unauthorized principal %s"
msgstr ""

#: ../../src/kprop/kpropd.c:558
#, c-format
msgid "while opening database file, '%s'"
msgstr ""

#: ../../src/kprop/kpropd.c:564
#, c-format
msgid "while renaming %s to %s"
msgstr ""

#: ../../src/kprop/kpropd.c:570
#, c-format
msgid "while downgrading lock on '%s'"
msgstr ""

#: ../../src/kprop/kpropd.c:577
#, c-format
msgid "while unlocking '%s'"
msgstr ""

#: ../../src/kprop/kpropd.c:589
msgid "while sending # of received bytes"
msgstr ""

#: ../../src/kprop/kpropd.c:595
msgid "while trying to close database file"
msgstr ""

#: ../../src/kprop/kpropd.c:650
#, c-format
msgid "Incremental propagation enabled\n"
msgstr ""

#: ../../src/kprop/kpropd.c:661
#, c-format
msgid "%s: unable to get kiprop host based service name for realm %s\n"
msgstr ""

#: ../../src/kprop/kpropd.c:672
msgid "while trying to construct host service principal"
msgstr ""

#: ../../src/kprop/kpropd.c:691
#, c-format
msgid "Initializing kadm5 as client %s\n"
msgstr ""

#: ../../src/kprop/kpropd.c:705
#, c-format
msgid "kadm5 initialization failed!\n"
msgstr ""

#: ../../src/kprop/kpropd.c:714
msgid "while attempting to connect to master KDC ... retrying"
msgstr ""

#: ../../src/kprop/kpropd.c:718
#, c-format
msgid "Sleeping %d seconds to re-initialize kadm5 (RPC ERROR)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:734
#, c-format
msgid "while initializing %s interface, retrying"
msgstr ""

#: ../../src/kprop/kpropd.c:738
#, c-format
msgid "Sleeping %d seconds to re-initialize kadm5 (krb5kdc not running?)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:748
#, c-format
msgid "kadm5 initialization succeeded\n"
msgstr ""

#: ../../src/kprop/kpropd.c:770
msgid "reading update log header"
msgstr ""

#: ../../src/kprop/kpropd.c:781
#, c-format
msgid "Calling iprop_get_updates_1 (sno=%u sec=%u usec=%u)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:791
msgid "iprop_get_updates call failed"
msgstr ""

#: ../../src/kprop/kpropd.c:797
#, c-format
msgid "Reinitializing iprop because get updates failed\n"
msgstr ""

#: ../../src/kprop/kpropd.c:818
#, c-format
msgid "Still waiting for full resync\n"
msgstr ""

#: ../../src/kprop/kpropd.c:823
#, c-format
msgid "Full resync needed\n"
msgstr ""

#: ../../src/kprop/kpropd.c:824
msgid "kpropd: Full resync needed."
msgstr ""

#: ../../src/kprop/kpropd.c:829
msgid "iprop_full_resync call failed"
msgstr ""

#: ../../src/kprop/kpropd.c:840
#, c-format
msgid "Full resync request granted\n"
msgstr ""

#: ../../src/kprop/kpropd.c:841
msgid "Full resync request granted."
msgstr ""

#: ../../src/kprop/kpropd.c:850
#, c-format
msgid "Exponential backoff\n"
msgstr ""

#: ../../src/kprop/kpropd.c:856
#, c-format
msgid "Full resync permission denied\n"
msgstr ""

#: ../../src/kprop/kpropd.c:857
msgid "Full resync, permission denied."
msgstr ""

#: ../../src/kprop/kpropd.c:862
#, c-format
msgid "Full resync error from master\n"
msgstr ""

#: ../../src/kprop/kpropd.c:863
msgid " Full resync, error returned from master KDC."
msgstr ""

#: ../../src/kprop/kpropd.c:871
#, c-format
msgid "Full resync invalid result from master\n"
msgstr ""

#: ../../src/kprop/kpropd.c:873
msgid "Full resync, invalid return from master KDC."
msgstr ""

#: ../../src/kprop/kpropd.c:889
#, c-format
msgid "Got incremental updates (sno=%u sec=%u usec=%u)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:901
#, c-format
msgid "ulog_replay failed (%s), updates not registered\n"
msgstr ""

#: ../../src/kprop/kpropd.c:904
#, c-format
msgid "ulog_replay failed (%s), updates not registered."
msgstr ""

#: ../../src/kprop/kpropd.c:913
#, c-format
msgid "Incremental updates: %d updates / %lu us"
msgstr ""

#: ../../src/kprop/kpropd.c:916
#, c-format
msgid "Incremental updates: %d updates / %lu us\n"
msgstr ""

#: ../../src/kprop/kpropd.c:924
#, c-format
msgid "get_updates permission denied\n"
msgstr ""

#: ../../src/kprop/kpropd.c:925
msgid "get_updates, permission denied."
msgstr ""

#: ../../src/kprop/kpropd.c:930
#, c-format
msgid "get_updates error from master\n"
msgstr ""

#: ../../src/kprop/kpropd.c:931
msgid "get_updates, error returned from master KDC."
msgstr ""

#: ../../src/kprop/kpropd.c:939
#, c-format
msgid "get_updates master busy; backoff\n"
msgstr ""

#: ../../src/kprop/kpropd.c:948
#, c-format
msgid "KDC is synchronized with master.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:956
#, c-format
msgid "get_updates invalid result from master\n"
msgstr ""

#: ../../src/kprop/kpropd.c:957
msgid "get_updates, invalid return from master KDC."
msgstr ""

#: ../../src/kprop/kpropd.c:972
#, c-format
msgid "Busy signal received from master, backoff for %d secs\n"
msgstr ""

#: ../../src/kprop/kpropd.c:979
#, c-format
msgid "Waiting for %d seconds before checking for updates again\n"
msgstr ""

#: ../../src/kprop/kpropd.c:990
#, c-format
msgid "ERROR returned by master, bailing\n"
msgstr ""

#: ../../src/kprop/kpropd.c:991
msgid "ERROR returned by master KDC, bailing.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1108
msgid "copying db args"
msgstr ""

#: ../../src/kprop/kpropd.c:1133
msgid "Unable to get default realm"
msgstr ""

#: ../../src/kprop/kpropd.c:1140
msgid "Unable to set default realm"
msgstr ""

#: ../../src/kprop/kpropd.c:1150
msgid "while trying to construct my service name"
msgstr ""

#: ../../src/kprop/kpropd.c:1157
msgid "while allocating filename for temp file"
msgstr ""

#: ../../src/kprop/kpropd.c:1165
msgid "while initializing"
msgstr ""

#: ../../src/kprop/kpropd.c:1173
msgid "Unable to map log!\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1219
#, c-format
msgid "Error in krb5_auth_con_ini: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1227
#, c-format
msgid "Error in krb5_auth_con_setflags: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1235
#, c-format
msgid "Error in krb5_auth_con_setaddrs: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1243
#, c-format
msgid "Error in krb5_kt_resolve: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1252
#, c-format
msgid "Error in krb5_recvauth: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1259
#, c-format
msgid "Error in krb5_copy_prinicpal: %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1275
msgid "while unparsing ticket etype"
msgstr ""

#: ../../src/kprop/kpropd.c:1279
#, c-format
msgid "authenticated client: %s (etype == %s)\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1358
msgid "while reading size of database from client"
msgstr ""

#: ../../src/kprop/kpropd.c:1368
msgid "while decoding database size from client"
msgstr ""

#: ../../src/kprop/kpropd.c:1381
msgid "while initializing i_vector"
msgstr ""

#: ../../src/kprop/kpropd.c:1386
#, c-format
msgid "Full propagation transfer started.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1439
#, c-format
msgid "Full propagation transfer finished.\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1500
msgid "while decoding error packet from client"
msgstr ""

#: ../../src/kprop/kpropd.c:1509
msgid "signaled from server"
msgstr ""

#: ../../src/kprop/kpropd.c:1511
#, c-format
msgid "Error text from client: %s\n"
msgstr ""

#: ../../src/kprop/kpropd.c:1560
#, c-format
msgid "while trying to fork %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1564
#, c-format
msgid "while trying to exec %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1571
#, c-format
msgid "while waiting for %s"
msgstr ""

#: ../../src/kprop/kpropd.c:1577
#, c-format
msgid "%s load terminated"
msgstr ""

#: ../../src/kprop/kpropd.c:1583
#, c-format
msgid "%s returned a bad exit status (%d)"
msgstr ""

#: ../../src/kprop/kproplog.c:28
#, c-format
msgid ""
"\n"
"Usage: %s [-h] [-v] [-v] [-e num]\n"
"\t%s -R\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:132
#, c-format
msgid ""
"\n"
"Couldn't allocate memory"
msgstr ""

#: ../../src/kprop/kproplog.c:226
#, c-format
msgid "\t\tAttribute flags\n"
msgstr ""

#: ../../src/kprop/kproplog.c:231
#, c-format
msgid "\t\tMaximum ticket life\n"
msgstr ""

#: ../../src/kprop/kproplog.c:236
#, c-format
msgid "\t\tMaximum renewable life\n"
msgstr ""

#: ../../src/kprop/kproplog.c:241
#, c-format
msgid "\t\tPrincipal expiration\n"
msgstr ""

#: ../../src/kprop/kproplog.c:246
#, c-format
msgid "\t\tPassword expiration\n"
msgstr ""

#: ../../src/kprop/kproplog.c:251
#, c-format
msgid "\t\tLast successful auth\n"
msgstr ""

#: ../../src/kprop/kproplog.c:256
#, c-format
msgid "\t\tLast failed auth\n"
msgstr ""

#: ../../src/kprop/kproplog.c:261
#, c-format
msgid "\t\tFailed passwd attempt\n"
msgstr ""

#: ../../src/kprop/kproplog.c:266
#, c-format
msgid "\t\tPrincipal\n"
msgstr ""

#: ../../src/kprop/kproplog.c:271
#, c-format
msgid "\t\tKey data\n"
msgstr ""

#: ../../src/kprop/kproplog.c:278
#, c-format
msgid "\t\tTL data\n"
msgstr ""

#: ../../src/kprop/kproplog.c:285
#, c-format
msgid "\t\tLength\n"
msgstr ""

#: ../../src/kprop/kproplog.c:290
#, c-format
msgid "\t\tPassword last changed\n"
msgstr ""

#: ../../src/kprop/kproplog.c:295
#, c-format
msgid "\t\tModifying principal\n"
msgstr ""

#: ../../src/kprop/kproplog.c:300
#, c-format
msgid "\t\tModification time\n"
msgstr ""

#: ../../src/kprop/kproplog.c:305
#, c-format
msgid "\t\tModified where\n"
msgstr ""

#: ../../src/kprop/kproplog.c:310
#, c-format
msgid "\t\tPassword policy\n"
msgstr ""

#: ../../src/kprop/kproplog.c:315
#, c-format
msgid "\t\tPassword policy switch\n"
msgstr ""

#: ../../src/kprop/kproplog.c:320
#, c-format
msgid "\t\tPassword history KVNO\n"
msgstr ""

#: ../../src/kprop/kproplog.c:325
#, c-format
msgid "\t\tPassword history\n"
msgstr ""

#: ../../src/kprop/kproplog.c:359
#, c-format
msgid ""
"Corrupt update entry\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:364
#, c-format
msgid "Update Entry\n"
msgstr ""

#: ../../src/kprop/kproplog.c:366
#, c-format
msgid "\tUpdate serial # : %u\n"
msgstr ""

#: ../../src/kprop/kproplog.c:370
#, c-format
msgid "\tDummy entry\n"
msgstr ""

#: ../../src/kprop/kproplog.c:378
#, c-format
msgid ""
"Entry data decode failure\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:382
#, c-format
msgid "\tUpdate operation : "
msgstr ""

#: ../../src/kprop/kproplog.c:384
#, c-format
msgid "Delete\n"
msgstr ""

#: ../../src/kprop/kproplog.c:386
#, c-format
msgid "Add\n"
msgstr ""

#: ../../src/kprop/kproplog.c:390
#, c-format
msgid ""
"Could not allocate principal name\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:396
#, c-format
msgid "\tUpdate principal : %s\n"
msgstr ""

#: ../../src/kprop/kproplog.c:398
#, c-format
msgid "\tUpdate size : %u\n"
msgstr ""

#: ../../src/kprop/kproplog.c:399
#, c-format
msgid "\tUpdate committed : %s\n"
msgstr ""

#: ../../src/kprop/kproplog.c:403
#, c-format
msgid "\tUpdate time stamp : None\n"
msgstr ""

#: ../../src/kprop/kproplog.c:405
#, c-format
msgid "\tUpdate time stamp : %s"
msgstr ""

#: ../../src/kprop/kproplog.c:409
#, c-format
msgid "\tAttributes changed : %d\n"
msgstr ""

#: ../../src/kprop/kproplog.c:474
#, c-format
msgid ""
"Unable to initialize Kerberos\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:481
#, c-format
msgid ""
"Couldn't read database_name\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:485
#, c-format
msgid ""
"\n"
"Kerberos update log (%s)\n"
msgstr ""

#: ../../src/kprop/kproplog.c:489 ../../src/kprop/kproplog.c:505
#, c-format
msgid ""
"Unable to map log file %s\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:494
#, c-format
msgid ""
"Couldn't reinitialize ulog file %s\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:498
#, c-format
msgid "Reinitialized the ulog.\n"
msgstr ""

#: ../../src/kprop/kproplog.c:511
#, c-format
msgid ""
"Corrupt header log, exiting\n"
"\n"
msgstr ""

#: ../../src/kprop/kproplog.c:515
#, c-format
msgid "Update log dump :\n"
msgstr ""

#: ../../src/kprop/kproplog.c:516
#, c-format
msgid "\tLog version # : %u\n"
msgstr ""

#: ../../src/kprop/kproplog.c:517
#, c-format
msgid "\tLog state : "
msgstr ""

#: ../../src/kprop/kproplog.c:520
#, c-format
msgid "Stable\n"
msgstr ""

#: ../../src/kprop/kproplog.c:523
#, c-format
msgid "Unstable\n"
msgstr ""

#: ../../src/kprop/kproplog.c:526
#, c-format
msgid "Corrupt\n"
msgstr ""

#: ../../src/kprop/kproplog.c:529
#, c-format
msgid "Unknown state: %d\n"
msgstr ""

#: ../../src/kprop/kproplog.c:532
#, c-format
msgid "\tEntry block size : %u\n"
msgstr ""

#: ../../src/kprop/kproplog.c:533
#, c-format
msgid "\tNumber of entries : %u\n"
msgstr ""

#: ../../src/kprop/kproplog.c:536
#, c-format
msgid "\tLast serial # : None\n"
msgstr ""

#: ../../src/kprop/kproplog.c:539
#, c-format
msgid "\tFirst serial # : None\n"
msgstr ""

#: ../../src/kprop/kproplog.c:541
#, c-format
msgid "\tFirst serial # : "
msgstr ""

#: ../../src/kprop/kproplog.c:545
#, c-format
msgid "\tLast serial # : "
msgstr ""

#: ../../src/kprop/kproplog.c:550
#, c-format
msgid "\tLast time stamp : None\n"
msgstr ""

#: ../../src/kprop/kproplog.c:553
#, c-format
msgid "\tFirst time stamp : None\n"
msgstr ""

#: ../../src/kprop/kproplog.c:555
#, c-format
msgid "\tFirst time stamp : %s"
msgstr ""

#: ../../src/kprop/kproplog.c:559
#, c-format
msgid "\tLast time stamp : %s\n"
msgstr ""

#: ../../src/lib/apputils/net-server.c:221
msgid "Got signal to request exit"
msgstr ""

#: ../../src/lib/apputils/net-server.c:235
msgid "Got signal to reset"
msgstr ""

#: ../../src/lib/apputils/net-server.c:301
#, c-format
msgid "Invalid port %d"
msgstr ""

#: ../../src/lib/apputils/net-server.c:314
#, c-format
msgid "Removing address %s since wildcard address is being added"
msgstr ""

#: ../../src/lib/apputils/net-server.c:321
msgid "Address already added to server"
msgstr ""

#: ../../src/lib/apputils/net-server.c:484
#, c-format
msgid "closing down fd %d"
msgstr ""

#: ../../src/lib/apputils/net-server.c:498
#, c-format
msgid "descriptor %d closed but still in svc_fdset"
msgstr ""

#: ../../src/lib/apputils/net-server.c:524
msgid "cannot create io event"
msgstr ""

#: ../../src/lib/apputils/net-server.c:529
msgid "cannot save event"
msgstr ""

#: ../../src/lib/apputils/net-server.c:549
#, c-format
msgid "file descriptor number %d too high"
msgstr ""

#: ../../src/lib/apputils/net-server.c:556
msgid "cannot allocate storage for connection info"
msgstr ""

#: ../../src/lib/apputils/net-server.c:591
#, c-format
msgid "Cannot create TCP server socket on %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:600
#, c-format
msgid "TCP socket fd number %d (for %s) too high"
msgstr ""

#: ../../src/lib/apputils/net-server.c:607
#, c-format
msgid "Cannot enable SO_REUSEADDR on fd %d"
msgstr ""

#: ../../src/lib/apputils/net-server.c:612
#, c-format
msgid "setsockopt(%d,IPV6_V6ONLY,1) failed"
msgstr ""

#: ../../src/lib/apputils/net-server.c:615
#, c-format
msgid "setsockopt(%d,IPV6_V6ONLY,1) worked"
msgstr ""

#: ../../src/lib/apputils/net-server.c:618
msgid "no IPV6_V6ONLY socket option support"
msgstr ""

#: ../../src/lib/apputils/net-server.c:624
#, c-format
msgid "Cannot bind server socket on %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:694
#, c-format
msgid "Setting up %s socket for address %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:707
#, c-format
msgid "Cannot listen on %s server socket on %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:716
#, c-format
msgid "cannot set listening %s socket on %s non-blocking"
msgstr ""

#: ../../src/lib/apputils/net-server.c:724
#, c-format
msgid "cannot set SO_LINGER on %s socket on %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:731
#, c-format
msgid "Setting pktinfo on socket %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:736
#, c-format
msgid "Cannot request packet info for UDP socket address %s port %d"
msgstr ""

#: ../../src/lib/apputils/net-server.c:738
msgid ""
"System does not support pktinfo yet binding to a wildcard address.  Packets "
"are not guaranteed to return on the received address."
msgstr ""

#: ../../src/lib/apputils/net-server.c:750
msgid "Error attempting to add verto event"
msgstr ""

#: ../../src/lib/apputils/net-server.c:759
#, c-format
msgid "Cannot create RPC service: %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:769
#, c-format
msgid "Cannot register RPC service: %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:813
msgid "No addresses added to the net server"
msgstr ""

#: ../../src/lib/apputils/net-server.c:832
#, c-format
msgid "Failed getting address info (for %s): %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:862
#, c-format
msgid "Failed setting up a %s socket (for %s)"
msgstr ""

#: ../../src/lib/apputils/net-server.c:903
msgid "setting up network..."
msgstr ""

#: ../../src/lib/apputils/net-server.c:906
msgid "Error setting up network"
msgstr ""

#: ../../src/lib/apputils/net-server.c:909
#, c-format
msgid "set up %d sockets"
msgstr ""

#: ../../src/lib/apputils/net-server.c:912
msgid "no sockets set up?"
msgstr ""

#: ../../src/lib/apputils/net-server.c:975
#: ../../src/lib/apputils/net-server.c:1029
msgid "while dispatching (udp)"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1004
#, c-format
msgid "while sending reply to %s/%s from %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1009
#, c-format
msgid "short reply write %d vs %d\n"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1054
msgid "while receiving from network"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1097
msgid "too many connections"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1115
#, c-format
msgid "dropping %s fd %d from %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1185
#, c-format
msgid "allocating buffer for new TCP session from %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1217
msgid "while dispatching (tcp)"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1249
msgid "error allocating tcp dispatch private!"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1296
#, c-format
msgid "TCP client %s wants %lu bytes, cap is %lu"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1304
#, c-format
msgid "error constructing KRB_ERR_FIELD_TOOLONG error! %s"
msgstr ""

#: ../../src/lib/apputils/net-server.c:1343
#, c-format
msgid "getsockname failed: %s"
msgstr ""

#: ../../src/lib/crypto/krb/prng_fortuna.c:428
msgid "Random number generator could not be seeded"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:43
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:165
msgid "A required input parameter could not be read"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:44
msgid "A required input parameter could not be written"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:45
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:175
msgid "A parameter was malformed"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:48
msgid "calling error"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:59
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:195
msgid "An unsupported mechanism was requested"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:60
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:199
msgid "An invalid name was supplied"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:61
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:203
msgid "A supplied name was of an unsupported type"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:62
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:208
msgid "Incorrect channel bindings were supplied"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:63
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:179
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:274
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:334
msgid "An invalid status code was supplied"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:64
msgid "A token had an invalid signature"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:65
msgid "No credentials were supplied"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:66
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:223
msgid "No context has been established"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:67
msgid "A token was invalid"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:68
msgid "A credential was invalid"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:69
msgid "The referenced credentials have expired"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:70
msgid "The context has expired"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:71
msgid "Miscellaneous failure"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:72
msgid "The quality-of-protection requested could not be provided"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:73
msgid "The operation is forbidden by the local security policy"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:74
msgid "The operation or option is not available"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:77
msgid "routine error"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:89
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:311
msgid "The routine must be called again to complete its function"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:90
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:316
msgid "The token was a duplicate of an earlier token"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:91
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:321
msgid "The token's validity period has expired"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:92
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:325
msgid "A later token has already been processed"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:95
msgid "supplementary info code"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:106
#: ../lib/krb5/error_tables/krb5_err.c:23
msgid "No error"
msgstr ""

#: ../../src/lib/gssapi/generic/disp_major_status.c:107
#, c-format
msgid "Unknown %s (field = %d)"
msgstr ""

#: ../../src/lib/gssapi/krb5/acquire_cred.c:165
#, c-format
msgid "No key table entry found matching %s"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:161
msgid "The routine completed successfully"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:170
msgid "A required output parameter could not be written"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:212
msgid "A token had an invalid Message Integrity Check (MIC)"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:217
msgid ""
"No credentials were supplied, or the credentials were unavailable or "
"inaccessible"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:227
msgid "Invalid token was supplied"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:231
msgid "Invalid credential was supplied"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:235
msgid "The referenced credential has expired"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:239
msgid "The referenced context has expired"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:243
msgid "Unspecified GSS failure.  Minor code may provide more information"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:248
msgid "The quality-of-protection (QOP) requested could not be provided"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:253
msgid "The operation is forbidden by local security policy"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:258
msgid "The operation or option is not available or unsupported"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:263
msgid "The requested credential element already exists"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:268
msgid "The provided name was not mechanism specific (MN)"
msgstr ""

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:329
msgid "An expected per-message token was not received"
msgstr ""

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1814
msgid "SPNEGO cannot find mechanisms to negotiate"
msgstr ""

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1819
msgid "SPNEGO failed to acquire creds"
msgstr ""

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1824
msgid "SPNEGO acceptor did not select a mechanism"
msgstr ""

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1829
msgid "SPNEGO failed to negotiate a mechanism"
msgstr ""

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1834
msgid "SPNEGO acceptor did not return a valid token"
msgstr ""

#: ../../src/lib/kadm5/logger.c:54
#, c-format
msgid "%s: cannot parse <%s>\n"
msgstr ""

#: ../../src/lib/kadm5/logger.c:55
#, c-format
msgid "%s: warning - logging entry syntax error\n"
msgstr ""

#: ../../src/lib/kadm5/logger.c:56
#, c-format
msgid "%s: error writing to %s\n"
msgstr ""

#: ../../src/lib/kadm5/logger.c:57
#, c-format
msgid "%s: error writing to %s device\n"
msgstr ""

#: ../../src/lib/kadm5/logger.c:59
msgid "EMERGENCY"
msgstr ""

#: ../../src/lib/kadm5/logger.c:60
msgid "ALERT"
msgstr ""

#: ../../src/lib/kadm5/logger.c:61
msgid "CRITICAL"
msgstr ""

#: ../../src/lib/kadm5/logger.c:62
msgid "Error"
msgstr ""

#: ../../src/lib/kadm5/logger.c:63
msgid "Warning"
msgstr ""

#: ../../src/lib/kadm5/logger.c:64
msgid "Notice"
msgstr ""

#: ../../src/lib/kadm5/logger.c:65
msgid "info"
msgstr ""

#: ../../src/lib/kadm5/logger.c:66
msgid "debug"
msgstr ""

#: ../../src/lib/kadm5/logger.c:784
#, c-format
msgid "Couldn't open log file %s: %s\n"
msgstr ""

#: ../../src/lib/kadm5/srv/kadm5_hook.c:120
#, c-format
msgid "kadm5_hook %s failed postcommit %s: %s"
msgstr ""

#: ../../src/lib/kadm5/srv/pwqual_dict.c:106
msgid "No dictionary file specified, continuing without one."
msgstr ""

#: ../../src/lib/kadm5/srv/pwqual_dict.c:113
#, c-format
msgid "WARNING!  Cannot find dictionary file %s, continuing without one."
msgstr ""

#: ../../src/lib/kadm5/srv/pwqual_empty.c:42
msgid "Empty passwords are not allowed"
msgstr ""

#: ../../src/lib/kadm5/srv/pwqual_hesiod.c:114
msgid "Password may not match user information."
msgstr ""

#: ../../src/lib/kadm5/srv/pwqual_princ.c:54
msgid "Password may not match principal name"
msgstr ""

#: ../../src/lib/kadm5/srv/server_kdb.c:195
msgid "History entry contains no key data"
msgstr ""

#: ../../src/lib/kadm5/srv/server_misc.c:128
#, c-format
msgid "password quality module %s rejected password for %s: %s"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:216
msgid "No default realm set; cannot initialize KDB"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:368
#, c-format
msgid "Unable to find requested database type: %s"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:448 ../lib/krb5/error_tables/kdb5_err.c:55
msgid "Unable to find requested database type"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:456
msgid "plugin symbol 'kdb_function_table' lookup failed"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:464
#, c-format
msgid ""
"Unable to load requested database module '%s': plugin symbol "
"'kdb_function_table' not found"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:602
msgid "Cannot initialize database library"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:1762
#, c-format
msgid "Illegal version number for KRB5_TL_MKEY_AUX %d\n"
msgstr ""

#: ../../src/lib/kdb/kdb5.c:1934
#, c-format
msgid "Illegal version number for KRB5_TL_ACTKVNO %d\n"
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:164
#, c-format
msgid "keyfile (%s) is not a regular file: %s"
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:177
msgid "Could not create temp keytab file name."
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:202
#, c-format
msgid "Temporary stash file already exists: %s."
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:230
#, c-format
msgid "rename of temporary keyfile (%s) to (%s) failed: %s"
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:415
#, c-format
msgid "Can not fetch master key (error: %s)."
msgstr ""

#: ../../src/lib/kdb/kdb_default.c:483
msgid "Unable to decrypt latest master key with the provided master key\n"
msgstr ""

#: ../../src/lib/kdb/kdb_log.c:87
msgid "could not sync ulog update to disk"
msgstr ""

#: ../../src/lib/kdb/kdb_log.c:101
msgid "could not sync ulog header to disk"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:122
#, c-format
msgid "Subsidiary cache path %s has no parent directory"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:128
#, c-format
msgid "Subsidiary cache path %s filename does not begin with \"tkt\""
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:169
#, c-format
msgid "%s contains invalid filename"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:229
#, c-format
msgid "Credential cache directory %s does not exist"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:235
#, c-format
msgid "Credential cache directory %s exists but is not a directory"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_dir.c:400
msgid ""
"Can't create new subsidiary cache because default cache is not a directory "
"collection"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_kcm.c:756
#, c-format
msgid "Credentials cache 'KCM:%s' not found"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_keyring.c:1151
msgid ""
"Can't create new subsidiary cache because default cache is already a "
"subsidiary"
msgstr ""

#: ../../src/lib/krb5/ccache/cc_keyring.c:1219
#, c-format
msgid "Credentials cache keyring '%s' not found"
msgstr ""

#: ../../src/lib/krb5/ccache/cccursor.c:213
#, c-format
msgid "Can't find client principal %s in cache collection"
msgstr ""

#: ../../src/lib/krb5/ccache/cccursor.c:293
msgid "No Kerberos credentials available"
msgstr ""

#: ../../src/lib/krb5/ccache/cccursor.c:299
#, c-format
msgid "No Kerberos credentials available (default cache: %s)"
msgstr ""

#: ../../src/lib/krb5/keytab/kt_file.c:406
#, c-format
msgid "No key table entry found for %s"
msgstr ""

#: ../../src/lib/krb5/keytab/kt_file.c:823
#: ../../src/lib/krb5/keytab/kt_file.c:856
msgid "Cannot change keytab with keytab iterators active"
msgstr ""

#: ../../src/lib/krb5/keytab/kt_file.c:1046
#, c-format
msgid "Key table file '%s' not found"
msgstr ""

#: ../../src/lib/krb5/keytab/ktfns.c:127
#, c-format
msgid "Keytab %s is nonexistent or empty"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:250
msgid "Malformed request error"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:253 ../lib/krb5/error_tables/kdb5_err.c:58
msgid "Server error"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:256
msgid "Authentication error"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:259
msgid "Password change rejected"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:262
msgid "Access denied"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:265
msgid "Wrong protocol version"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:268
msgid "Initial password required"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:271
msgid "Success"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:274 ../lib/krb5/error_tables/krb5_err.c:257
msgid "Password change failed"
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:431
msgid ""
"The password must include numbers or symbols.  Don't include any part of "
"your name in the password."
msgstr ""

#: ../../src/lib/krb5/krb/chpw.c:437
#, c-format
msgid "The password must contain at least %d character."
msgid_plural "The password must contain at least %d characters."
msgstr[0] ""
msgstr[1] ""

#: ../../src/lib/krb5/krb/chpw.c:446
#, c-format
msgid "The password must be different from the previous password."
msgid_plural "The password must be different from the previous %d passwords."
msgstr[0] ""
msgstr[1] ""

#: ../../src/lib/krb5/krb/chpw.c:458
#, c-format
msgid "The password can only be changed once a day."
msgid_plural "The password can only be changed every %d days."
msgstr[0] ""
msgstr[1] ""

#: ../../src/lib/krb5/krb/chpw.c:504
msgid "Try a more complex password, or contact your administrator."
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:216
msgid "Error constructing AP-REQ armor"
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:394
msgid "Failed to decrypt FAST reply"
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:400
msgid "nonce modified in FAST response: KDC response modified"
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:466
msgid "Expecting FX_ERROR pa-data inside FAST container"
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:537
msgid "FAST response missing finish message in KDC reply"
msgstr ""

#: ../../src/lib/krb5/krb/fast.c:550
msgid "Ticket modified in KDC reply"
msgstr ""

#: ../../src/lib/krb5/krb/gc_via_tkt.c:198
#, c-format
msgid "KDC returned error string: %.*s"
msgstr ""

#: ../../src/lib/krb5/krb/gc_via_tkt.c:207
#, c-format
msgid "Server %s not found in Kerberos database"
msgstr ""

#: ../../src/lib/krb5/krb/get_in_tkt.c:202
msgid "Reply has wrong form of session key for anonymous request"
msgstr ""

#: ../../src/lib/krb5/krb/get_in_tkt.c:1704
msgid "Failed to store credentials"
msgstr ""

#: ../../src/lib/krb5/krb/get_in_tkt.c:1793
#, c-format
msgid "Client '%s' not found in Kerberos database"
msgstr ""

#: ../../src/lib/krb5/krb/gic_keytab.c:207
#, c-format
msgid "Keytab contains no suitable keys for %s"
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:75
#, c-format
msgid "Password for %s"
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:227
#, c-format
msgid "Warning: Your password will expire in less than one hour on %s"
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:231
#, c-format
msgid "Warning: Your password will expire in %d hour%s on %s"
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:235
#, c-format
msgid "Warning: Your password will expire in %d days on %s"
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:408
msgid "Password expired.  You must change it now."
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:427 ../../src/lib/krb5/krb/gic_pwd.c:431
#, c-format
msgid "%s.  Please try again."
msgstr ""

#: ../../src/lib/krb5/krb/gic_pwd.c:472
#, c-format
msgid "%.*s%s%s.  Please try again.\n"
msgstr ""

#: ../../src/lib/krb5/krb/parse.c:203
#, c-format
msgid "Principal %s is missing required realm"
msgstr ""

#: ../../src/lib/krb5/krb/parse.c:215
#, c-format
msgid "Principal %s has realm present"
msgstr ""

#: ../../src/lib/krb5/krb/plugin.c:169
#, c-format
msgid "Invalid module specifier %s"
msgstr ""

#: ../../src/lib/krb5/krb/plugin.c:406
#, c-format
msgid "Could not find %s plugin module named '%s'"
msgstr ""

#: ../../src/lib/krb5/krb/preauth2.c:309
msgid "krb5_init_creds calls must use same library context"
msgstr ""

#: ../../src/lib/krb5/krb/preauth2.c:717
msgid "Pre-authentication failed"
msgstr ""

#: ../../src/lib/krb5/krb/preauth2.c:1098
msgid "Unable to initialize preauth context"
msgstr ""

#: ../../src/lib/krb5/krb/preauth2.c:1111
#, c-format
msgid "Preauth module %s"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_encts.c:71
msgid "Encrypted timestamp is disabled"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:515
msgid "Please choose from the following:\n"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:516
msgid "Vendor:"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:528
msgid "Enter #"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:564
msgid "OTP Challenge:"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:593
msgid "OTP Token PIN"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:707
msgid "OTP value doesn't match any token formats"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:774
msgid "Enter OTP Token Value"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_otp.c:920
msgid "No supported tokens"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:49
msgid "Challenge for Enigma Logic mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:53
msgid "Challenge for Digital Pathways mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:57
msgid "Challenge for Activcard mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:60
msgid "Challenge for Enhanced S/Key mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:63
msgid "Challenge for Traditional S/Key mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:66
#: ../../src/lib/krb5/krb/preauth_sam2.c:69
msgid "Challenge for Security Dynamics mechanism"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:72
msgid "Challenge from authentication server"
msgstr ""

#: ../../src/lib/krb5/krb/preauth_sam2.c:166
msgid "SAM Authentication"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:145
#, c-format
msgid "Cannot find key for %s kvno %d in keytab"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:150
#, c-format
msgid "Cannot find key for %s kvno %d in keytab (request ticket server %s)"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:175
#, c-format
msgid "Cannot decrypt ticket for %s using keytab key for %s"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:197
#, c-format
msgid "Server principal %s does not match request ticket server %s"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:226
msgid "No keys in keytab"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:229
#, c-format
msgid "Server principal %s does not match any keys in keytab"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:236
#, c-format
msgid ""
"Request ticket server %s found in keytab but does not match server principal "
"%s"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:241
#, c-format
msgid "Request ticket server %s not found in keytab (ticket kvno %d)"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:247
#, c-format
msgid ""
"Request ticket server %s kvno %d not found in keytab; ticket is likely out "
"of date"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:252
#, c-format
msgid ""
"Request ticket server %s kvno %d not found in keytab; keytab is likely out "
"of date"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:261
#, c-format
msgid ""
"Request ticket server %s kvno %d found in keytab but not with enctype %s"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:266
#, c-format
msgid ""
"Request ticket server %s kvno %d enctype %s found in keytab but cannot "
"decrypt ticket"
msgstr ""

#: ../../src/lib/krb5/krb/rd_req_dec.c:871
#, c-format
msgid "Encryption type %s not permitted"
msgstr ""

#: ../../src/lib/krb5/os/expand_path.c:316
#, c-format
msgid "Can't find username for uid %lu"
msgstr ""

#: ../../src/lib/krb5/os/expand_path.c:405
#: ../../src/lib/krb5/os/expand_path.c:421
msgid "Invalid token"
msgstr ""

#: ../../src/lib/krb5/os/expand_path.c:506
msgid "variable missing }"
msgstr ""

#: ../../src/lib/krb5/os/locate_kdc.c:813
#, c-format
msgid "Cannot find KDC for realm \"%.*s\""
msgstr ""

#: ../../src/lib/krb5/os/sendto_kdc.c:519
#, c-format
msgid "Cannot contact any KDC for realm '%.*s'"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:106
#, c-format
msgid "Cannot fstat replay cache file %s: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:112
#, c-format
msgid ""
"Insecure mkstemp() file mode for replay cache file %s; try running this "
"program with umask 077"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:140
#, c-format
msgid "Cannot %s replay cache file %s: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:145
#, c-format
msgid "Cannot %s replay cache: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:268
#, c-format
msgid "Insecure file mode for replay cache file %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:274
#, c-format
msgid "rcache not owned by %d"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:398 ../../src/lib/krb5/rcache/rc_io.c:402
#: ../../src/lib/krb5/rcache/rc_io.c:407
#, c-format
msgid "Can't write to replay cache: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:428
#, c-format
msgid "Cannot sync replay cache file: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:447
#, c-format
msgid "Can't read from replay cache: %s"
msgstr ""

#: ../../src/lib/krb5/rcache/rc_io.c:478 ../../src/lib/krb5/rcache/rc_io.c:484
#: ../../src/lib/krb5/rcache/rc_io.c:489
#, c-format
msgid "Can't destroy replay cache: %s"
msgstr ""

#: ../../src/plugins/kdb/db2/kdb_db2.c:245
#: ../../src/plugins/kdb/db2/kdb_db2.c:819
#, c-format
msgid "Unsupported argument \"%s\" for db2"
msgstr ""

#: ../../src/plugins/kdb/db2/kdb_db2.c:387
#, c-format
msgid "Cannot open DB2 database '%s'"
msgstr ""

#: ../../src/plugins/kdb/db2/kdb_db2.c:989
msgid "Recursive iteration is not supported for hash databases"
msgstr ""

#: ../../src/plugins/kdb/db2/kdb_db2.c:996
msgid "Recursive iteration not supported in this version of libdb"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:69
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:893
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1094
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1501
msgid "while reading kerberos container information"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:129
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:143
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:504
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:518
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:151
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:166
msgid "while providing time specification"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:268
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:304
msgid "while creating policy object"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:279
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1509
msgid "while reading realm information"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:348
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:407
msgid "while destroying policy object"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:358
#, c-format
msgid "This will delete the policy object '%s', are you sure?\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:473
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:663
msgid "while modifying policy object"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:487
#, c-format
msgid "while reading information of policy '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:692
msgid "while viewing policy"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:701
#, c-format
msgid "while viewing policy '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:835
msgid "while listing policy objects"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:453
#, c-format
msgid "for subtree while creating realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:465
#, c-format
msgid "for container reference while creating realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:490
#, c-format
msgid "invalid search scope while creating realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:505
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:829
#, c-format
msgid "'%s' is an invalid option\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:513
#, c-format
msgid "Initializing database for realm '%s'\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:537
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:698
#, c-format
msgid "while creating realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:557
#, c-format
msgid "Enter DN of Kerberos container: "
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:592
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:900
#, c-format
msgid "while reading information of realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:736
msgid "while reading Kerberos container information"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:779
#, c-format
msgid "for subtree while modifying realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:790
#, c-format
msgid "for container reference while modifying realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:818
#, c-format
msgid "specified for search scope while modifying information of realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:857
#, c-format
msgid "while modifying information of realm '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:946
msgid "Realm Name"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:949
msgid "Subtree"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:952
msgid "Principal Container Reference"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:957
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:959
msgid "SearchScope"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:957
msgid "Invalid !"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:964
msgid "KDC Services"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:979
msgid "Admin Services"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:994
msgid "Passwd Services"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1010
msgid "Maximum Ticket Life"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1015
msgid "Maximum Renewable Life"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1022
msgid "Ticket flags"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1101
msgid "while listing realms"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1433
msgid "while adding entries to database"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1474
#, c-format
msgid "Deleting KDC database of '%s', are you sure?\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1485
#, c-format
msgid "OK, deleting database of '%s'...\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1518
#, c-format
msgid "deleting database of '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1523
#, c-format
msgid "** Database of '%s' destroyed.\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:79
msgid "ldap_service_password_file not configured"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:124
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:131
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:139
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:145
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:173
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:243
msgid "while setting service object password"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:152
msgid "while getting service password filename"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:165
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:481
#, c-format
msgid "Password for \"%s\""
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:168
#, c-format
msgid "Re-enter password for \"%s\""
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:179
#, c-format
msgid "%s: Invalid password\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:189
msgid "Failed to convert the password to hexadecimal"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:199
#, c-format
msgid "Failed to open file %s: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:221
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:263
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:272
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:299
msgid "Failed to write service object password to file"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:227
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:284
msgid "Error reading service object password file"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:252
#, c-format
msgid "Error creating file %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:105
#, c-format
msgid ""
"Usage: kdb5_ldap_util [-D user_dn [-w passwd]] [-H ldapuri] [-r realm]\n"
"\tcmd [cmd_options]\n"
"create          [-subtrees subtree_dn_list] [-sscope search_scope]\n"
"\t\t[-containerref container_reference_dn]\n"
"\t\t[-m|-P password|-sf stashfilename] [-s]\n"
"\t\t[-k mkeytype] [-kv mkeyVNO] [-M mkeyname]\n"
"\t\t[-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags]\n"
"modify          [-subtrees subtree_dn_list] [-sscope search_scope]\n"
"\t\t[-containerref container_reference_dn]\n"
"\t\t[-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags]\n"
"view\n"
"destroy         [-f]\n"
"list\n"
"stashsrvpw      [-f filename] service_dn\n"
"create_policy   [-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags] policy\n"
"modify_policy   [-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags] policy\n"
"view_policy     policy\n"
"destroy_policy  [-force] policy\n"
"list_policy\n"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:329
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:337
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:345
msgid "while reading ldap parameters"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:443
msgid "while initializing error handling"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:451
msgid "while initializing ldap handle"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:465
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:474
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:487
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:529
msgid "while retrieving ldap configuration"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:504
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:511
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:520
msgid "while initializing server list"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:551
msgid "while setting up lib handle"
msgstr ""

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:560
msgid "while reading ldap configuration"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:68
msgid "Unable to read Kerberos container"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:73
msgid "Unable to read Realm"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:214
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:73
msgid "Error processing LDAP DB params"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:220
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:79
msgid "Error reading LDAP server params"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:64
msgid "LDAP bind dn value missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:69
msgid "LDAP bind password value missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:78
msgid "Error reading password from stash"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:85
msgid "Service password length is zero"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:145
#, c-format
msgid "Cannot bind to LDAP server '%s' with SASL mechanism '%s': %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:158
#, c-format
msgid "Cannot bind to LDAP server '%s' as '%s': %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:183
#, c-format
msgid "Cannot create LDAP handle for '%s': %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:129
msgid "could not complete roll-back, error deleting Kerberos Container"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:56
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:67
msgid "Error reading kerberos container location from krb5.conf"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:75
msgid "Kerberos container location not specified"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:56
#, c-format
msgid "Error reading '%s' attribute: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:219
msgid "KDB module requires -update argument"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:225
#, c-format
msgid "'%s' value missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:283
#, c-format
msgid "unknown option '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:343
msgid "Minimum connections required per server is 2"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal.c:159
msgid "Default realm not set"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal.c:261
msgid "DN information missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal.c:473
msgid "dn information missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:137
msgid "Principal does not belong to realm"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:308
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:317
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:325
#, c-format
msgid "%s option not supported"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:332
#, c-format
msgid "unknown option: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:339
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:346
#, c-format
msgid "%s option value missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:671
msgid "DN is out of the realm subtree"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:703
msgid "ldap object is already kerberized"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:797
msgid "Principal does not belong to the default realm"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:865
#, c-format
msgid ""
"operation can not continue, more than one entry with principal name \"%s\" "
"found"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:928
#, c-format
msgid "'%s' not found"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:992
#, c-format
msgid ""
"link information can not be set/updated as the kerberos principal belongs to "
"an ldap object"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1007
#, c-format
msgid "Failed getting object references"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1014
#, c-format
msgid "kerberos principal is already linked to a ldap object"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1352
msgid "ticket policy object value: "
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1400
#, c-format
msgid "Principal delete failed (trying to replace entry): %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1410
#, c-format
msgid "Principal add failed: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1448
#, c-format
msgid "User modification failed: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1521
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:294
msgid "Error reading ticket policy"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1651
msgid "unable to decode stored principal key data"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1709
msgid "unable to decode stored principal pw history"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:223
msgid "Realm information not available"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:306
#, c-format
msgid "Realm Delete FAILED: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:386
msgid "subtree value: "
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:403
msgid "container reference value: "
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:486
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:549
msgid "Kerberos Container information is missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:498
msgid "Invalid Kerberos container DN"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:514
#, c-format
msgid "Kerberos Container create FAILED: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:557
#, c-format
msgid "Kerberos Container delete FAILED: %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:633
msgid "realm object value: "
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:48
msgid "Not a hexadecimal password"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:55
msgid "Password corrupt"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:78
#, c-format
msgid "Cannot open LDAP password file '%s': %s"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:108
#, c-format
msgid "Bind DN entry '%s' missing in LDAP password file '%s'"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:66
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:142
msgid "Ticket Policy Name missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:154
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:231
msgid "ticket policy object: "
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:219
msgid "Ticket Policy Object information missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:311
msgid "Ticket Policy Object DN missing"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:338
msgid "Delete Failed: One or more Principals associated with the Ticket Policy"
msgstr ""

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:447
msgid "Error reading container object"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:132
#, c-format
msgid "%s (path: %s): %s"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:164
#, c-format
msgid "Unsupported argument \"%s\" for LMDB"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:294
msgid "LMDB environment open failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:319
msgid "LMDB read failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:394
msgid "LMDB write failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:418
msgid "LMDB delete failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:521
#, c-format
msgid "LMDB file %s does not exist"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:566
msgid "LMDB open failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:593
#, c-format
msgid "LMDB file %s already exists"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:658
msgid "LMDB create error"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:676
#, c-format
msgid "Could not unlink %s"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:760
#, c-format
msgid "Unsupported argument \"%s\" for lmdb"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:806
msgid "LMDB lockout write failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:882
msgid "LMDB principal iteration failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:985
msgid "LMDB policy iteration failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:1016
msgid "LMDB transaction commit failure"
msgstr ""

#: ../../src/plugins/kdb/lmdb/kdb_lmdb.c:1115
msgid "LMDB lockout update failure"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:500
#, c-format
msgid "%s: %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:530
#, c-format
msgid "%s (depth %d): %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:773
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:4324
msgid "Pass phrase for"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1103
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1113
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1380
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1390
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1931
msgid "Failed to DER encode PKCS7"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1204
msgid "Failed to verify own certificate"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1364
msgid "Failed to add digest attribute"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1492
msgid "Failed to decode CMS message"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1510
msgid "Invalid pkinit packet: octet string expected"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1528
msgid "wrong oid\n"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1680
msgid "Failed to verify received certificate"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1718
msgid "Failed to verify CMS message"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1906
msgid "Failed to encrypt PKCS7 object"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1981
msgid "Failed to decode PKCS7"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1998
msgid "Failed to decrypt PKCS7 message"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:4444
#, c-format
msgid "Cannot read certificate file '%s'"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:4451
#, c-format
msgid "Cannot read key file '%s'"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:5313
#, c-format
msgid "Cannot open file '%s'"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:5320
#, c-format
msgid "Cannot read file '%s'"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:5995
#, c-format
msgid "unknown code 0x%x"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:419
#, c-format
msgid "Unsupported type while processing '%s'\n"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:453
msgid "Internal error parsing X509_user_identity\n"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:544
msgid "No user identity options specified"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:405
#, c-format
msgid "PKINIT: no freshness token, rejecting auth from %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:409
#, c-format
msgid "PKINIT: freshness token received from %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:411
#, c-format
msgid "PKINIT: no freshness token received from %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:542
msgid "Pkinit request not signed, but client not anonymous."
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:580
msgid "Anonymous pkinit without DH public value not supported."
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1304
#, c-format
msgid "No pkinit_identity supplied for realm %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1315
#, c-format
msgid "No pkinit_anchors supplied for realm %s"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1335
#, c-format
msgid "OCSP is not supported: (realm: %s)"
msgstr ""

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1736
msgid "No realms configured correctly for pkinit support"
msgstr ""

#: ../../src/plugins/preauth/spake/groups.c:237
msgid "No SPAKE preauth groups configured"
msgstr ""

#: ../../src/plugins/preauth/spake/groups.c:257
#, c-format
msgid "SPAKE challenge group not a permitted group: %s"
msgstr ""

#: ../../src/plugins/preauth/spake/spake_kdc.c:536
msgid "Unknown SPAKE request type"
msgstr ""

#: ../../src/util/support/errors.c:77
msgid "Kerberos library initialization failure"
msgstr ""

#: ../../src/util/support/errors.c:93
#, c-format
msgid "error %ld"
msgstr ""

#: ../../src/util/support/plugins.c:186
#, c-format
msgid "unable to find plugin [%s]: %s"
msgstr ""

#: ../../src/util/support/plugins.c:274
msgid "unknown failure"
msgstr ""

#: ../../src/util/support/plugins.c:277
#, c-format
msgid "unable to load plugin [%s]: %s"
msgstr ""

#: ../../src/util/support/plugins.c:300
#, c-format
msgid "unable to load DLL [%s]"
msgstr ""

#: ../../src/util/support/plugins.c:316
#, c-format
msgid "plugin unavailable: %s"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:23
msgid "No @ in SERVICE-NAME name string"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:24
msgid "STRING-UID-NAME contains nondigits"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:25
msgid "UID does not resolve to username"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:26
msgid "Validation error"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:27
msgid "Couldn't allocate gss_buffer_t data"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:28
msgid "Message context invalid"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:29
msgid "Buffer is the wrong size"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:30
msgid "Credential usage type is unknown"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:31
msgid "Unknown quality of protection specified"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:32
msgid "Local host name could not be determined"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:33
msgid "Hostname in SERVICE-NAME string could not be canonicalized"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:34
msgid "Mechanism is incorrect"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:35
msgid "Token header is malformed or corrupt"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:36
msgid "Packet was replayed in wrong direction"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:37
msgid "Token is missing data"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:38
msgid "Token was reflected"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:39
msgid "Received token ID does not match expected token ID"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:40
msgid "The given credential's usage does not match the requested usage"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:41
msgid "Storing of acceptor credentials is not supported by the mechanism"
msgstr ""

#: ../lib/gssapi/generic/gssapi_err_generic.c:42
msgid "Storing of non-default credentials is not supported by the mechanism"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:23
msgid "Principal in credential cache does not match desired name"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:24
msgid "No principal in keytab matches desired name"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:25
msgid "Credential cache has no TGT"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:26
msgid "Authenticator has no subkey"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:27
msgid "Context is already fully established"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:28
msgid "Unknown signature type in token"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:29
msgid "Invalid field length in token"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:30
msgid "Attempt to use incomplete security context"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:31
msgid "Bad magic number for krb5_gss_ctx_id_t"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:32
msgid "Bad magic number for krb5_gss_cred_id_t"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:33
msgid "Bad magic number for krb5_gss_enc_desc"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:34
msgid "Sequence number in token is corrupt"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:35
msgid "Credential cache is empty"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:36
msgid "Acceptor and Initiator share no checksum types"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:37
msgid "Requested lucid context version not supported"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:38
msgid "PRF input too long"
msgstr ""

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:39
msgid "Bad magic number for iakerb_ctx_id_t"
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:23
msgid "while getting policy info."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:24
msgid "while getting principal info."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:25
msgid "New passwords do not match - password not changed.\n"
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:26
msgid "New password"
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:27
msgid "New password (again)"
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:28
msgid ""
"You must type a password. Passwords must be at least one character long.\n"
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:29
msgid "yet no policy set!  Contact your system security administrator."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:31
msgid ""
"New password was found in a dictionary of possible passwords and\n"
"therefore may be easily guessed. Please choose another password.\n"
"See the kpasswd man page for help in choosing a good password."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:32
msgid "Password not changed."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:33
#, c-format
msgid ""
"New password is too short.\n"
"Please choose a password which is at least %d characters long."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:34
#, c-format
msgid ""
"New password does not have enough character classes.\n"
"The character classes are:\n"
"\t- lower-case letters,\n"
"\t- upper-case letters,\n"
"\t- digits,\n"
"\t- punctuation, and\n"
"\t- all other characters (e.g., control characters).\n"
"Please choose a password with at least %d character classes."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:35
#, c-format
msgid ""
"Password cannot be changed because it was changed too recently.\n"
"Please wait until %s before you change it.\n"
"If you need to change your password before then, contact your system\n"
"security administrator."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:36
msgid "New password was used previously. Please choose a different password."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:37
msgid "while trying to change password."
msgstr ""

#: ../lib/kadm5/chpass_util_strings.c:38
msgid "while reading new password."
msgstr ""

#: ../lib/kadm5/kadm_err.c:23
msgid "Operation failed for unspecified reason"
msgstr ""

#: ../lib/kadm5/kadm_err.c:24
msgid "Operation requires ``get'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:25
msgid "Operation requires ``add'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:26
msgid "Operation requires ``modify'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:27
msgid "Operation requires ``delete'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:28
msgid "Insufficient authorization for operation"
msgstr ""

#: ../lib/kadm5/kadm_err.c:29 ../lib/kdb/adb_err.c:29
msgid "Database inconsistency detected"
msgstr ""

#: ../lib/kadm5/kadm_err.c:30 ../lib/kdb/adb_err.c:24
msgid "Principal or policy already exists"
msgstr ""

#: ../lib/kadm5/kadm_err.c:31
msgid "Communication failure with server"
msgstr ""

#: ../lib/kadm5/kadm_err.c:32
msgid "No administration server found for realm"
msgstr ""

#: ../lib/kadm5/kadm_err.c:33
msgid "Password history principal key version mismatch"
msgstr ""

#: ../lib/kadm5/kadm_err.c:34
msgid "Connection to server not initialized"
msgstr ""

#: ../lib/kadm5/kadm_err.c:35
msgid "Principal does not exist"
msgstr ""

#: ../lib/kadm5/kadm_err.c:36
msgid "Policy does not exist"
msgstr ""

#: ../lib/kadm5/kadm_err.c:37
msgid "Invalid field mask for operation"
msgstr ""

#: ../lib/kadm5/kadm_err.c:38
msgid "Invalid number of character classes"
msgstr ""

#: ../lib/kadm5/kadm_err.c:39
msgid "Invalid password length"
msgstr ""

#: ../lib/kadm5/kadm_err.c:40
msgid "Illegal policy name"
msgstr ""

#: ../lib/kadm5/kadm_err.c:41
msgid "Illegal principal name"
msgstr ""

#: ../lib/kadm5/kadm_err.c:42
msgid "Invalid auxillary attributes"
msgstr ""

#: ../lib/kadm5/kadm_err.c:43
msgid "Invalid password history count"
msgstr ""

#: ../lib/kadm5/kadm_err.c:44
msgid "Password minimum life is greater than password maximum life"
msgstr ""

#: ../lib/kadm5/kadm_err.c:45
msgid "Password is too short"
msgstr ""

#: ../lib/kadm5/kadm_err.c:46
msgid "Password does not contain enough character classes"
msgstr ""

#: ../lib/kadm5/kadm_err.c:47
msgid "Password is in the password dictionary"
msgstr ""

#: ../lib/kadm5/kadm_err.c:48
msgid "Cannot reuse password"
msgstr ""

#: ../lib/kadm5/kadm_err.c:49
msgid "Current password's minimum life has not expired"
msgstr ""

#: ../lib/kadm5/kadm_err.c:50 ../lib/krb5/error_tables/kdb5_err.c:67
msgid "Policy is in use"
msgstr ""

#: ../lib/kadm5/kadm_err.c:51
msgid "Connection to server already initialized"
msgstr ""

#: ../lib/kadm5/kadm_err.c:52
msgid "Incorrect password"
msgstr ""

#: ../lib/kadm5/kadm_err.c:53
msgid "Cannot change protected principal"
msgstr ""

#: ../lib/kadm5/kadm_err.c:54
msgid "Programmer error! Bad Admin server handle"
msgstr ""

#: ../lib/kadm5/kadm_err.c:55
msgid "Programmer error! Bad API structure version"
msgstr ""

#: ../lib/kadm5/kadm_err.c:56
msgid ""
"API structure version specified by application is no longer supported (to "
"fix, recompile application against current KADM5 API header files and "
"libraries)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:57
msgid ""
"API structure version specified by application is unknown to libraries (to "
"fix, obtain current KADM5 API header files and libraries and recompile "
"application)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:58
msgid "Programmer error! Bad API version"
msgstr ""

#: ../lib/kadm5/kadm_err.c:59
msgid ""
"API version specified by application is no longer supported by libraries (to "
"fix, update application to adhere to current API version and recompile)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:60
msgid ""
"API version specified by application is no longer supported by server (to "
"fix, update application to adhere to current API version and recompile)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:61
msgid ""
"API version specified by application is unknown to libraries (to fix, obtain "
"current KADM5 API header files and libraries and recompile application)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:62
msgid ""
"API version specified by application is unknown to server (to fix, obtain "
"and install newest KADM5 Admin Server)"
msgstr ""

#: ../lib/kadm5/kadm_err.c:63
msgid "Database error! Required KADM5 principal missing"
msgstr ""

#: ../lib/kadm5/kadm_err.c:64
msgid "The salt type of the specified principal does not support renaming"
msgstr ""

#: ../lib/kadm5/kadm_err.c:65
msgid "Illegal configuration parameter for remote KADM5 client"
msgstr ""

#: ../lib/kadm5/kadm_err.c:66
msgid "Illegal configuration parameter for local KADM5 client"
msgstr ""

#: ../lib/kadm5/kadm_err.c:67
msgid "Operation requires ``list'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:68
msgid "Operation requires ``change-password'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:69
msgid "GSS-API (or Kerberos) error"
msgstr ""

#: ../lib/kadm5/kadm_err.c:70
msgid "Programmer error! Illegal tagged data list type"
msgstr ""

#: ../lib/kadm5/kadm_err.c:71
msgid "Required parameters in kdc.conf missing"
msgstr ""

#: ../lib/kadm5/kadm_err.c:72
msgid "Bad krb5 admin server hostname"
msgstr ""

#: ../lib/kadm5/kadm_err.c:73
msgid "Operation requires ``set-key'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:74
msgid "Multiple values for single or folded enctype"
msgstr ""

#: ../lib/kadm5/kadm_err.c:75
msgid "Invalid enctype for setv4key"
msgstr ""

#: ../lib/kadm5/kadm_err.c:76
msgid "Mismatched enctypes for setkey3"
msgstr ""

#: ../lib/kadm5/kadm_err.c:77
msgid "Missing parameters in krb5.conf required for kadmin client"
msgstr ""

#: ../lib/kadm5/kadm_err.c:78 ../lib/kdb/adb_err.c:30
msgid "XDR encoding error"
msgstr ""

#: ../lib/kadm5/kadm_err.c:79
msgid "Cannot resolve network address for admin server in requested realm"
msgstr ""

#: ../lib/kadm5/kadm_err.c:80
msgid "Unspecified password quality failure"
msgstr ""

#: ../lib/kadm5/kadm_err.c:81
msgid "Invalid key/salt tuples"
msgstr ""

#: ../lib/kadm5/kadm_err.c:82
msgid "Invalid multiple or duplicate kvnos in setkey operation"
msgstr ""

#: ../lib/kadm5/kadm_err.c:83
msgid "Operation requires ``extract-keys'' privilege"
msgstr ""

#: ../lib/kadm5/kadm_err.c:84
msgid "Principal keys are locked down"
msgstr ""

#: ../lib/kadm5/kadm_err.c:85
msgid "Operation requires initial ticket"
msgstr ""

#: ../lib/kdb/adb_err.c:23
msgid "No Error"
msgstr ""

#: ../lib/kdb/adb_err.c:25
msgid "Principal or policy does not exist"
msgstr ""

#: ../lib/kdb/adb_err.c:26
msgid "Database not initialized"
msgstr ""

#: ../lib/kdb/adb_err.c:27
msgid "Invalid policy name"
msgstr ""

#: ../lib/kdb/adb_err.c:28
msgid "Invalid principal name"
msgstr ""

#: ../lib/kdb/adb_err.c:31
msgid "Failure!"
msgstr ""

#: ../lib/kdb/adb_err.c:32
msgid "Bad lock mode"
msgstr ""

#: ../lib/kdb/adb_err.c:33
msgid "Cannot lock database"
msgstr ""

#: ../lib/kdb/adb_err.c:34
msgid "Database not locked"
msgstr ""

#: ../lib/kdb/adb_err.c:35
msgid "KADM5 administration database lock file missing"
msgstr ""

#: ../lib/kdb/adb_err.c:36
msgid "Insufficient permission to lock file"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:23
msgid "Plugin does not support interface version"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:24
msgid "Invalid module specifier"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:25
msgid "Plugin module name not found"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:26
msgid "The KDC should discard this request"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:27
msgid "Can't create new subsidiary cache"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:28
msgid "Invalid keyring anchor name"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:29
msgid "Unknown keyring collection version"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:30
msgid "Invalid UID in persistent keyring name"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:31
msgid "Malformed reply from KCM daemon"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:32
msgid "Mach RPC error communicating with KCM daemon"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:33
msgid "KCM daemon reply too big"
msgstr ""

#: ../lib/krb5/error_tables/k5e1_err.c:34
msgid "No KCM server found"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:24
msgid "Client's entry in database has expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:25
msgid "Server's entry in database has expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:26
msgid "Requested protocol version not supported"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:27
msgid "Client's key is encrypted in an old master key"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:28
msgid "Server's key is encrypted in an old master key"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:29
msgid "Client not found in Kerberos database"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:30
msgid "Server not found in Kerberos database"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:31
msgid "Principal has multiple entries in Kerberos database"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:32
msgid "Client or server has a null key"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:33
msgid "Ticket is ineligible for postdating"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:34
msgid "Requested effective lifetime is negative or too short"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:35
msgid "KDC policy rejects request"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:36
msgid "KDC can't fulfill requested option"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:37
msgid "KDC has no support for encryption type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:38
msgid "KDC has no support for checksum type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:39
msgid "KDC has no support for padata type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:40
msgid "KDC has no support for transited type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:41
msgid "Client's credentials have been revoked"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:42
msgid "Credentials for server have been revoked"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:43
msgid "TGT has been revoked"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:44
msgid "Client not yet valid - try again later"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:45
msgid "Server not yet valid - try again later"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:46
msgid "Password has expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:47
msgid "Preauthentication failed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:48
msgid "Additional pre-authentication required"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:49
msgid "Requested server and ticket don't match"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:50
msgid "Server principal valid for user2user only"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:51
msgid "KDC policy rejects transited path"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:52
msgid "A service is not available that is required to process the request"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:53
msgid "KRB5 error code 30"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:54
msgid "Decrypt integrity check failed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:55
msgid "Ticket expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:56
msgid "Ticket not yet valid"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:57
msgid "Request is a replay"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:58
msgid "The ticket isn't for us"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:59
msgid "Ticket/authenticator don't match"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:60
msgid "Clock skew too great"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:61
msgid "Incorrect net address"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:62
msgid "Protocol version mismatch"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:63
msgid "Invalid message type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:64
msgid "Message stream modified"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:65
msgid "Message out of order"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:66
msgid "Illegal cross-realm ticket"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:67
msgid "Key version is not available"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:68
msgid "Service key not available"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:69
#: ../lib/krb5/error_tables/krb5_err.c:181
msgid "Mutual authentication failed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:70
msgid "Incorrect message direction"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:71
msgid "Alternative authentication method required"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:72
msgid "Incorrect sequence number in message"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:73
msgid "Inappropriate type of checksum in message"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:74
msgid "Policy rejects transited path"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:75
msgid "Response too big for UDP, retry with TCP"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:76
msgid "KRB5 error code 53"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:77
msgid "KRB5 error code 54"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:78
msgid "KRB5 error code 55"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:79
msgid "KRB5 error code 56"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:80
msgid "KRB5 error code 57"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:81
msgid "KRB5 error code 58"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:82
msgid "KRB5 error code 59"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:83
msgid "Generic error (see e-text)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:84
msgid "Field is too long for this implementation"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:85
msgid "Client not trusted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:86
msgid "KDC not trusted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:87
msgid "Invalid signature"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:88
msgid "Key parameters not accepted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:89
msgid "Certificate mismatch"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:90
msgid "No ticket granting ticket"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:91
msgid "Realm not local to KDC"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:92
msgid "User to user required"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:93
msgid "Can't verify certificate"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:94
msgid "Invalid certificate"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:95
msgid "Revoked certificate"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:96
msgid "Revocation status unknown"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:97
msgid "Revocation status unavailable"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:98
msgid "Client name mismatch"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:99
msgid "KDC name mismatch"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:100
msgid "Inconsistent key purpose"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:101
msgid "Digest in certificate not accepted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:102
msgid "Checksum must be included"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:103
msgid "Digest in signed-data not accepted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:104
msgid "Public key encryption not supported"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:105
msgid "KRB5 error code 82"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:106
msgid "KRB5 error code 83"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:107
msgid "KRB5 error code 84"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:108
msgid "The IAKERB proxy could not find a KDC"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:109
msgid "The KDC did not respond to the IAKERB proxy"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:110
msgid "KRB5 error code 87"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:111
msgid "KRB5 error code 88"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:112
msgid "KRB5 error code 89"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:113
msgid "Preauthentication expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:114
msgid "More preauthentication data is required"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:115
msgid "KRB5 error code 92"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:116
msgid "An unsupported critical FAST option was requested"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:117
msgid "KRB5 error code 94"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:118
msgid "KRB5 error code 95"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:119
msgid "KRB5 error code 96"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:120
msgid "KRB5 error code 97"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:121
msgid "KRB5 error code 98"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:122
msgid "KRB5 error code 99"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:123
msgid "No acceptable KDF offered"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:124
msgid "KRB5 error code 101"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:125
msgid "KRB5 error code 102"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:126
msgid "KRB5 error code 103"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:127
msgid "KRB5 error code 104"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:128
msgid "KRB5 error code 105"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:129
msgid "KRB5 error code 106"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:130
msgid "KRB5 error code 107"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:131
msgid "KRB5 error code 108"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:132
msgid "KRB5 error code 109"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:133
msgid "KRB5 error code 110"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:134
msgid "KRB5 error code 111"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:135
msgid "KRB5 error code 112"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:136
msgid "KRB5 error code 113"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:137
msgid "KRB5 error code 114"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:138
msgid "KRB5 error code 115"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:139
msgid "KRB5 error code 116"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:140
msgid "KRB5 error code 117"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:141
msgid "KRB5 error code 118"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:142
msgid "KRB5 error code 119"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:143
msgid "KRB5 error code 120"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:144
msgid "KRB5 error code 121"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:145
msgid "KRB5 error code 122"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:146
msgid "KRB5 error code 123"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:147
msgid "KRB5 error code 124"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:148
msgid "KRB5 error code 125"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:149
msgid "KRB5 error code 126"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:150
msgid "KRB5 error code 127"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:151
#: ../lib/krb5/error_tables/kdb5_err.c:23
msgid "$Id$"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:152
msgid "Invalid flag for file lock mode"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:153
msgid "Cannot read password"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:154
msgid "Password mismatch"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:155
msgid "Password read interrupted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:156
msgid "Illegal character in component name"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:157
msgid "Malformed representation of principal"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:158
msgid "Can't open/find Kerberos configuration file"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:159
msgid "Improper format of Kerberos configuration file"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:160
msgid "Insufficient space to return complete information"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:161
msgid "Invalid message type specified for encoding"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:162
msgid "Credential cache name malformed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:163
msgid "Unknown credential cache type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:164
msgid "Matching credential not found"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:165
msgid "End of credential cache reached"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:166
msgid "Request did not supply a ticket"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:167
msgid "Wrong principal in request"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:168
msgid "Ticket has invalid flag set"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:169
msgid "Requested principal and ticket don't match"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:170
msgid "KDC reply did not match expectations"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:171
msgid "Clock skew too great in KDC reply"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:172
msgid "Client/server realm mismatch in initial ticket request"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:173
msgid "Program lacks support for encryption type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:174
msgid "Program lacks support for key type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:175
msgid "Requested encryption type not used in message"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:176
msgid "Program lacks support for checksum type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:177
msgid "Cannot find KDC for requested realm"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:178
msgid "Kerberos service unknown"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:179
msgid "Cannot contact any KDC for requested realm"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:180
msgid "No local name found for principal name"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:182
msgid "Replay cache type is already registered"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:183
msgid "No more memory to allocate (in replay cache code)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:184
msgid "Replay cache type is unknown"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:185
msgid "Generic unknown RC error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:186
msgid "Message is a replay"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:187
msgid "Replay cache I/O operation failed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:188
msgid "Replay cache type does not support non-volatile storage"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:189
msgid "Replay cache name parse/format error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:190
msgid "End-of-file on replay cache I/O"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:191
msgid "No more memory to allocate (in replay cache I/O code)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:192
msgid "Permission denied in replay cache code"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:193
msgid "I/O error in replay cache i/o code"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:194
msgid "Generic unknown RC/IO error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:195
msgid "Insufficient system space to store replay information"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:196
msgid "Can't open/find realm translation file"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:197
msgid "Improper format of realm translation file"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:198
msgid "Can't open/find lname translation database"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:199
msgid "No translation available for requested principal"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:200
msgid "Improper format of translation database entry"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:201
msgid "Cryptosystem internal error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:202
msgid "Key table name malformed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:203
msgid "Unknown Key table type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:204
msgid "Key table entry not found"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:205
msgid "End of key table reached"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:206
msgid "Cannot write to specified key table"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:207
msgid "Error writing to key table"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:208
msgid "Cannot find ticket for requested realm"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:209
msgid "DES key has bad parity"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:210
msgid "DES key is a weak key"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:211
msgid "Bad encryption type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:212
msgid "Key size is incompatible with encryption type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:213
msgid "Message size is incompatible with encryption type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:214
msgid "Credentials cache type is already registered."
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:215
msgid "Key table type is already registered."
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:216
msgid "Credentials cache I/O operation failed"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:217
msgid "Credentials cache permissions incorrect"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:218
msgid "No credentials cache found"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:219
msgid "Internal credentials cache error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:220
msgid "Error writing to credentials cache"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:221
msgid "No more memory to allocate (in credentials cache code)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:222
msgid "Bad format in credentials cache"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:223
msgid "No credentials found with supported encryption types"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:224
msgid "Invalid KDC option combination (library internal error)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:225
msgid "Request missing second ticket"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:226
msgid "No credentials supplied to library routine"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:227
msgid "Bad sendauth version was sent"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:228
msgid "Bad application version was sent (via sendauth)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:229
msgid "Bad response (during sendauth exchange)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:230
msgid "Server rejected authentication (during sendauth exchange)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:231
msgid "Unsupported preauthentication type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:232
msgid "Required preauthentication key not supplied"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:233
msgid "Generic preauthentication failure"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:234
msgid "Unsupported replay cache format version number"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:235
msgid "Unsupported credentials cache format version number"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:236
msgid "Unsupported key table format version number"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:237
msgid "Program lacks support for address type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:238
msgid "Message replay detection requires rcache parameter"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:239
msgid "Hostname cannot be canonicalized"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:240
msgid "Cannot determine realm for host"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:241
msgid "Conversion to service principal undefined for name type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:242
msgid "Initial Ticket response appears to be Version 4 error"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:243
msgid "Cannot resolve network address for KDC in requested realm"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:244
msgid "Requesting ticket can't get forwardable tickets"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:245
msgid "Bad principal name while trying to forward credentials"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:246
msgid "Looping detected inside krb5_get_in_tkt"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:247
msgid "Configuration file does not specify default realm"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:248
msgid "Bad SAM flags in obtain_sam_padata"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:249
msgid "Invalid encryption type in SAM challenge"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:250
msgid "Missing checksum in SAM challenge"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:251
msgid "Bad checksum in SAM challenge"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:252
msgid "Keytab name too long"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:253
msgid "Key version number for principal in key table is incorrect"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:254
msgid "This application has expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:255
msgid "This Krb5 library has expired"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:256
msgid "New password cannot be zero length"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:258
msgid "Bad format in keytab"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:259
msgid "Encryption type not permitted"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:260
msgid "No supported encryption types (config file error?)"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:261
msgid "Program called an obsolete, deleted function"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:262
msgid "unknown getaddrinfo failure"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:263
msgid "no data available for host/domain name"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:264
msgid "host/domain name not found"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:265
msgid "service name unknown"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:266
msgid "Cannot determine realm for numeric host address"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:267
msgid "Invalid key generation parameters from KDC"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:268
msgid "service not available"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:269
msgid "Ccache function not supported: read-only ccache type"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:270
msgid "Ccache function not supported: not implemented"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:271
msgid "Invalid format of Kerberos lifetime or clock skew string"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:272
msgid "Supplied data not handled by this plugin"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:273
msgid "Plugin does not support the operation"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:274
msgid "Invalid UTF-8 string"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:275
msgid "FAST protected pre-authentication required but not supported by KDC"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:276
msgid "Auth context must contain local address"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:277
msgid "Auth context must contain remote address"
msgstr ""

#: ../lib/krb5/error_tables/krb5_err.c:278
msgid "Tracing unsupported"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:24
msgid "Entry already exists in database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:25
msgid "Database store error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:26
msgid "Database read error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:27
msgid "Insufficient access to perform requested operation"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:28
msgid "No such entry in the database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:29
msgid "Illegal use of wildcard"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:30
msgid "Database is locked or in use--try again later"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:31
msgid "Database was modified during read"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:32
msgid "Database record is incomplete or corrupted"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:33
msgid "Attempt to lock database twice"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:34
msgid "Attempt to unlock database when not locked"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:35
msgid "Invalid kdb lock mode"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:36
msgid "Database has not been initialized"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:37
msgid "Database has already been initialized"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:38
msgid "Bad direction for converting keys"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:39
msgid "Cannot find master key record in database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:40
msgid "Master key does not match database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:41
msgid "Key size in database is invalid"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:42
msgid "Cannot find/read stored master key"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:43
msgid "Stored master key is corrupted"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:44
msgid "Cannot find active master key"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:45
msgid "KVNO of new master key does not match expected value"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:46
msgid "Stored master key is not current"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:47
msgid "Insufficient access to lock database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:48
msgid "Database format error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:49
msgid "Unsupported version in database entry"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:50
msgid "Unsupported salt type"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:51
msgid "Unsupported encryption type"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:52
msgid "Bad database creation flags"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:53
msgid "No matching key in entry having a permitted enctype"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:54
msgid "No matching key in entry"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:56
msgid "Database type not supported"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:57
msgid "Database library failed to initialize"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:59
msgid "Unable to access Kerberos database"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:60
msgid "Kerberos database internal error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:61
msgid "Kerberos database constraints violated"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:62
msgid "Update log conversion error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:63
msgid "Update log is unstable"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:64
msgid "Update log is corrupt"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:65
msgid "Generic update log error"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:66
msgid "Database module does not match KDC version"
msgstr ""

#: ../lib/krb5/error_tables/kdb5_err.c:68
msgid "Too much string mapping data"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:23
msgid "ASN.1 failed call to system time library"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:24
msgid "ASN.1 structure is missing a required field"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:25
msgid "ASN.1 unexpected field number"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:26
msgid "ASN.1 type numbers are inconsistent"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:27
msgid "ASN.1 value too large"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:28
msgid "ASN.1 encoding ended unexpectedly"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:29
msgid "ASN.1 identifier doesn't match expected value"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:30
msgid "ASN.1 length doesn't match expected value"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:31
msgid "ASN.1 badly-formatted encoding"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:32
msgid "ASN.1 parse error"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:33
msgid "ASN.1 bad return from gmtime"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:34
msgid "ASN.1 non-constructed indefinite encoding"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:35
msgid "ASN.1 missing expected EOC"
msgstr ""

#: ../lib/krb5/error_tables/asn1_err.c:36
msgid "ASN.1 object omitted in sequence"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:23
msgid "Kerberos V5 magic number table"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:24
msgid "Bad magic number for krb5_principal structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:25
msgid "Bad magic number for krb5_data structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:26
msgid "Bad magic number for krb5_keyblock structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:27
msgid "Bad magic number for krb5_checksum structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:28
msgid "Bad magic number for krb5_encrypt_block structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:29
msgid "Bad magic number for krb5_enc_data structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:30
msgid "Bad magic number for krb5_cryptosystem_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:31
msgid "Bad magic number for krb5_cs_table_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:32
msgid "Bad magic number for krb5_checksum_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:33
msgid "Bad magic number for krb5_authdata structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:34
msgid "Bad magic number for krb5_transited structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:35
msgid "Bad magic number for krb5_enc_tkt_part structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:36
msgid "Bad magic number for krb5_ticket structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:37
msgid "Bad magic number for krb5_authenticator structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:38
msgid "Bad magic number for krb5_tkt_authent structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:39
msgid "Bad magic number for krb5_creds structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:40
msgid "Bad magic number for krb5_last_req_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:41
msgid "Bad magic number for krb5_pa_data structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:42
msgid "Bad magic number for krb5_kdc_req structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:43
msgid "Bad magic number for krb5_enc_kdc_rep_part structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:44
msgid "Bad magic number for krb5_kdc_rep structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:45
msgid "Bad magic number for krb5_error structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:46
msgid "Bad magic number for krb5_ap_req structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:47
msgid "Bad magic number for krb5_ap_rep structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:48
msgid "Bad magic number for krb5_ap_rep_enc_part structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:49
msgid "Bad magic number for krb5_response structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:50
msgid "Bad magic number for krb5_safe structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:51
msgid "Bad magic number for krb5_priv structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:52
msgid "Bad magic number for krb5_priv_enc_part structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:53
msgid "Bad magic number for krb5_cred structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:54
msgid "Bad magic number for krb5_cred_info structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:55
msgid "Bad magic number for krb5_cred_enc_part structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:56
msgid "Bad magic number for krb5_pwd_data structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:57
msgid "Bad magic number for krb5_address structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:58
msgid "Bad magic number for krb5_keytab_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:59
msgid "Bad magic number for krb5_context structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:60
msgid "Bad magic number for krb5_os_context structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:61
msgid "Bad magic number for krb5_alt_method structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:62
msgid "Bad magic number for krb5_etype_info_entry structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:63
msgid "Bad magic number for krb5_db_context structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:64
msgid "Bad magic number for krb5_auth_context structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:65
msgid "Bad magic number for krb5_keytab structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:66
msgid "Bad magic number for krb5_rcache structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:67
msgid "Bad magic number for krb5_ccache structure"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:68
msgid "Bad magic number for krb5_preauth_ops"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:69
msgid "Bad magic number for krb5_sam_challenge"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:70
msgid "Bad magic number for krb5_sam_challenge_2"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:71
msgid "Bad magic number for krb5_sam_key"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:72
#: ../lib/krb5/error_tables/kv5m_err.c:73
msgid "Bad magic number for krb5_enc_sam_response_enc"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:74
msgid "Bad magic number for krb5_sam_response"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:75
msgid "Bad magic number for krb5_sam_response 2"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:76
msgid "Bad magic number for krb5_predicted_sam_response"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:77
msgid "Bad magic number for passwd_phrase_element"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:78
msgid "Bad magic number for GSSAPI OID"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:79
msgid "Bad magic number for GSSAPI QUEUE"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:80
msgid "Bad magic number for fast armored request"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:81
msgid "Bad magic number for FAST request"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:82
msgid "Bad magic number for FAST response"
msgstr ""

#: ../lib/krb5/error_tables/kv5m_err.c:83
msgid "Bad magic number for krb5_authdata_context"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:23
msgid "Cannot convert V5 keyblock"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:24
msgid "Cannot convert V5 address information"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:25
msgid "Cannot convert V5 principal"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:26
msgid "V5 realm name longer than V4 maximum"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:27
msgid "Kerberos V4 error"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:28
msgid "Encoding too large"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:29
msgid "Decoding out of data"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:30
msgid "Service not responding"
msgstr ""

#: ../lib/krb5/error_tables/krb524_err.c:31
msgid "Kerberos version 4 support is disabled"
msgstr ""
