# Copyright 1999-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Function codes
ASN1_F_A2D_ASN1_OBJECT:100:a2d_ASN1_OBJECT
ASN1_F_A2I_ASN1_INTEGER:102:a2i_ASN1_INTEGER
ASN1_F_A2I_ASN1_STRING:103:a2i_ASN1_STRING
ASN1_F_APPEND_EXP:176:append_exp
ASN1_F_ASN1_BIO_INIT:113:asn1_bio_init
ASN1_F_ASN1_BIT_STRING_SET_BIT:183:ASN1_BIT_STRING_set_bit
ASN1_F_ASN1_CB:177:asn1_cb
ASN1_F_ASN1_CHECK_TLEN:104:asn1_check_tlen
ASN1_F_ASN1_COLLECT:106:asn1_collect
ASN1_F_ASN1_D2I_EX_PRIMITIVE:108:asn1_d2i_ex_primitive
ASN1_F_ASN1_D2I_FP:109:ASN1_d2i_fp
ASN1_F_ASN1_D2I_READ_BIO:107:asn1_d2i_read_bio
ASN1_F_ASN1_DIGEST:184:ASN1_digest
ASN1_F_ASN1_DO_ADB:110:asn1_do_adb
ASN1_F_ASN1_DO_LOCK:233:asn1_do_lock
ASN1_F_ASN1_DUP:111:ASN1_dup
ASN1_F_ASN1_ENC_SAVE:115:asn1_enc_save
ASN1_F_ASN1_EX_C2I:204:asn1_ex_c2i
ASN1_F_ASN1_FIND_END:190:asn1_find_end
ASN1_F_ASN1_GENERALIZEDTIME_ADJ:216:ASN1_GENERALIZEDTIME_adj
ASN1_F_ASN1_GENERATE_V3:178:ASN1_generate_v3
ASN1_F_ASN1_GET_INT64:224:asn1_get_int64
ASN1_F_ASN1_GET_OBJECT:114:ASN1_get_object
ASN1_F_ASN1_GET_UINT64:225:asn1_get_uint64
ASN1_F_ASN1_I2D_BIO:116:ASN1_i2d_bio
ASN1_F_ASN1_I2D_FP:117:ASN1_i2d_fp
ASN1_F_ASN1_ITEM_D2I_FP:206:ASN1_item_d2i_fp
ASN1_F_ASN1_ITEM_DUP:191:ASN1_item_dup
ASN1_F_ASN1_ITEM_EMBED_D2I:120:asn1_item_embed_d2i
ASN1_F_ASN1_ITEM_EMBED_NEW:121:asn1_item_embed_new
ASN1_F_ASN1_ITEM_EX_I2D:144:ASN1_item_ex_i2d
ASN1_F_ASN1_ITEM_FLAGS_I2D:118:asn1_item_flags_i2d
ASN1_F_ASN1_ITEM_I2D_BIO:192:ASN1_item_i2d_bio
ASN1_F_ASN1_ITEM_I2D_FP:193:ASN1_item_i2d_fp
ASN1_F_ASN1_ITEM_PACK:198:ASN1_item_pack
ASN1_F_ASN1_ITEM_SIGN:195:ASN1_item_sign
ASN1_F_ASN1_ITEM_SIGN_CTX:220:ASN1_item_sign_ctx
ASN1_F_ASN1_ITEM_UNPACK:199:ASN1_item_unpack
ASN1_F_ASN1_ITEM_VERIFY:197:ASN1_item_verify
ASN1_F_ASN1_MBSTRING_NCOPY:122:ASN1_mbstring_ncopy
ASN1_F_ASN1_OBJECT_NEW:123:ASN1_OBJECT_new
ASN1_F_ASN1_OUTPUT_DATA:214:asn1_output_data
ASN1_F_ASN1_PCTX_NEW:205:ASN1_PCTX_new
ASN1_F_ASN1_PRIMITIVE_NEW:119:asn1_primitive_new
ASN1_F_ASN1_SCTX_NEW:221:ASN1_SCTX_new
ASN1_F_ASN1_SIGN:128:ASN1_sign
ASN1_F_ASN1_STR2TYPE:179:asn1_str2type
ASN1_F_ASN1_STRING_GET_INT64:227:asn1_string_get_int64
ASN1_F_ASN1_STRING_GET_UINT64:230:asn1_string_get_uint64
ASN1_F_ASN1_STRING_SET:186:ASN1_STRING_set
ASN1_F_ASN1_STRING_TABLE_ADD:129:ASN1_STRING_TABLE_add
ASN1_F_ASN1_STRING_TO_BN:228:asn1_string_to_bn
ASN1_F_ASN1_STRING_TYPE_NEW:130:ASN1_STRING_type_new
ASN1_F_ASN1_TEMPLATE_EX_D2I:132:asn1_template_ex_d2i
ASN1_F_ASN1_TEMPLATE_NEW:133:asn1_template_new
ASN1_F_ASN1_TEMPLATE_NOEXP_D2I:131:asn1_template_noexp_d2i
ASN1_F_ASN1_TIME_ADJ:217:ASN1_TIME_adj
ASN1_F_ASN1_TYPE_GET_INT_OCTETSTRING:134:ASN1_TYPE_get_int_octetstring
ASN1_F_ASN1_TYPE_GET_OCTETSTRING:135:ASN1_TYPE_get_octetstring
ASN1_F_ASN1_UTCTIME_ADJ:218:ASN1_UTCTIME_adj
ASN1_F_ASN1_VERIFY:137:ASN1_verify
ASN1_F_B64_READ_ASN1:209:b64_read_asn1
ASN1_F_B64_WRITE_ASN1:210:B64_write_ASN1
ASN1_F_BIO_NEW_NDEF:208:BIO_new_NDEF
ASN1_F_BITSTR_CB:180:bitstr_cb
ASN1_F_BN_TO_ASN1_STRING:229:bn_to_asn1_string
ASN1_F_C2I_ASN1_BIT_STRING:189:c2i_ASN1_BIT_STRING
ASN1_F_C2I_ASN1_INTEGER:194:c2i_ASN1_INTEGER
ASN1_F_C2I_ASN1_OBJECT:196:c2i_ASN1_OBJECT
ASN1_F_C2I_IBUF:226:c2i_ibuf
ASN1_F_C2I_UINT64_INT:101:c2i_uint64_int
ASN1_F_COLLECT_DATA:140:collect_data
ASN1_F_D2I_ASN1_OBJECT:147:d2i_ASN1_OBJECT
ASN1_F_D2I_ASN1_UINTEGER:150:d2i_ASN1_UINTEGER
ASN1_F_D2I_AUTOPRIVATEKEY:207:d2i_AutoPrivateKey
ASN1_F_D2I_PRIVATEKEY:154:d2i_PrivateKey
ASN1_F_D2I_PUBLICKEY:155:d2i_PublicKey
ASN1_F_DO_BUF:142:do_buf
ASN1_F_DO_CREATE:124:do_create
ASN1_F_DO_DUMP:125:do_dump
ASN1_F_DO_TCREATE:222:do_tcreate
ASN1_F_I2A_ASN1_OBJECT:126:i2a_ASN1_OBJECT
ASN1_F_I2D_ASN1_BIO_STREAM:211:i2d_ASN1_bio_stream
ASN1_F_I2D_ASN1_OBJECT:143:i2d_ASN1_OBJECT
ASN1_F_I2D_DSA_PUBKEY:161:i2d_DSA_PUBKEY
ASN1_F_I2D_EC_PUBKEY:181:i2d_EC_PUBKEY
ASN1_F_I2D_PRIVATEKEY:163:i2d_PrivateKey
ASN1_F_I2D_PUBLICKEY:164:i2d_PublicKey
ASN1_F_I2D_RSA_PUBKEY:165:i2d_RSA_PUBKEY
ASN1_F_LONG_C2I:166:long_c2i
ASN1_F_NDEF_PREFIX:127:ndef_prefix
ASN1_F_NDEF_SUFFIX:136:ndef_suffix
ASN1_F_OID_MODULE_INIT:174:oid_module_init
ASN1_F_PARSE_TAGGING:182:parse_tagging
ASN1_F_PKCS5_PBE2_SET_IV:167:PKCS5_pbe2_set_iv
ASN1_F_PKCS5_PBE2_SET_SCRYPT:231:PKCS5_pbe2_set_scrypt
ASN1_F_PKCS5_PBE_SET:202:PKCS5_pbe_set
ASN1_F_PKCS5_PBE_SET0_ALGOR:215:PKCS5_pbe_set0_algor
ASN1_F_PKCS5_PBKDF2_SET:219:PKCS5_pbkdf2_set
ASN1_F_PKCS5_SCRYPT_SET:232:pkcs5_scrypt_set
ASN1_F_SMIME_READ_ASN1:212:SMIME_read_ASN1
ASN1_F_SMIME_TEXT:213:SMIME_text
ASN1_F_STABLE_GET:138:stable_get
ASN1_F_STBL_MODULE_INIT:223:stbl_module_init
ASN1_F_UINT32_C2I:105:uint32_c2i
ASN1_F_UINT32_NEW:139:uint32_new
ASN1_F_UINT64_C2I:112:uint64_c2i
ASN1_F_UINT64_NEW:141:uint64_new
ASN1_F_X509_CRL_ADD0_REVOKED:169:X509_CRL_add0_revoked
ASN1_F_X509_INFO_NEW:170:X509_INFO_new
ASN1_F_X509_NAME_ENCODE:203:x509_name_encode
ASN1_F_X509_NAME_EX_D2I:158:x509_name_ex_d2i
ASN1_F_X509_NAME_EX_NEW:171:x509_name_ex_new
ASN1_F_X509_PKEY_NEW:173:X509_PKEY_new
ASYNC_F_ASYNC_CTX_NEW:100:async_ctx_new
ASYNC_F_ASYNC_INIT_THREAD:101:ASYNC_init_thread
ASYNC_F_ASYNC_JOB_NEW:102:async_job_new
ASYNC_F_ASYNC_PAUSE_JOB:103:ASYNC_pause_job
ASYNC_F_ASYNC_START_FUNC:104:async_start_func
ASYNC_F_ASYNC_START_JOB:105:ASYNC_start_job
ASYNC_F_ASYNC_WAIT_CTX_SET_WAIT_FD:106:ASYNC_WAIT_CTX_set_wait_fd
BIO_F_ACPT_STATE:100:acpt_state
BIO_F_ADDRINFO_WRAP:148:addrinfo_wrap
BIO_F_ADDR_STRINGS:134:addr_strings
BIO_F_BIO_ACCEPT:101:BIO_accept
BIO_F_BIO_ACCEPT_EX:137:BIO_accept_ex
BIO_F_BIO_ACCEPT_NEW:152:BIO_ACCEPT_new
BIO_F_BIO_ADDR_NEW:144:BIO_ADDR_new
BIO_F_BIO_BIND:147:BIO_bind
BIO_F_BIO_CALLBACK_CTRL:131:BIO_callback_ctrl
BIO_F_BIO_CONNECT:138:BIO_connect
BIO_F_BIO_CONNECT_NEW:153:BIO_CONNECT_new
BIO_F_BIO_CTRL:103:BIO_ctrl
BIO_F_BIO_GETS:104:BIO_gets
BIO_F_BIO_GET_HOST_IP:106:BIO_get_host_ip
BIO_F_BIO_GET_NEW_INDEX:102:BIO_get_new_index
BIO_F_BIO_GET_PORT:107:BIO_get_port
BIO_F_BIO_LISTEN:139:BIO_listen
BIO_F_BIO_LOOKUP:135:BIO_lookup
BIO_F_BIO_LOOKUP_EX:143:BIO_lookup_ex
BIO_F_BIO_MAKE_PAIR:121:bio_make_pair
BIO_F_BIO_METH_NEW:146:BIO_meth_new
BIO_F_BIO_NEW:108:BIO_new
BIO_F_BIO_NEW_DGRAM_SCTP:145:BIO_new_dgram_sctp
BIO_F_BIO_NEW_FILE:109:BIO_new_file
BIO_F_BIO_NEW_MEM_BUF:126:BIO_new_mem_buf
BIO_F_BIO_NREAD:123:BIO_nread
BIO_F_BIO_NREAD0:124:BIO_nread0
BIO_F_BIO_NWRITE:125:BIO_nwrite
BIO_F_BIO_NWRITE0:122:BIO_nwrite0
BIO_F_BIO_PARSE_HOSTSERV:136:BIO_parse_hostserv
BIO_F_BIO_PUTS:110:BIO_puts
BIO_F_BIO_READ:111:BIO_read
BIO_F_BIO_READ_EX:105:BIO_read_ex
BIO_F_BIO_READ_INTERN:120:bio_read_intern
BIO_F_BIO_SOCKET:140:BIO_socket
BIO_F_BIO_SOCKET_NBIO:142:BIO_socket_nbio
BIO_F_BIO_SOCK_INFO:141:BIO_sock_info
BIO_F_BIO_SOCK_INIT:112:BIO_sock_init
BIO_F_BIO_WRITE:113:BIO_write
BIO_F_BIO_WRITE_EX:119:BIO_write_ex
BIO_F_BIO_WRITE_INTERN:128:bio_write_intern
BIO_F_BUFFER_CTRL:114:buffer_ctrl
BIO_F_CONN_CTRL:127:conn_ctrl
BIO_F_CONN_STATE:115:conn_state
BIO_F_DGRAM_SCTP_NEW:149:dgram_sctp_new
BIO_F_DGRAM_SCTP_READ:132:dgram_sctp_read
BIO_F_DGRAM_SCTP_WRITE:133:dgram_sctp_write
BIO_F_DOAPR_OUTCH:150:doapr_outch
BIO_F_FILE_CTRL:116:file_ctrl
BIO_F_FILE_READ:130:file_read
BIO_F_LINEBUFFER_CTRL:129:linebuffer_ctrl
BIO_F_LINEBUFFER_NEW:151:linebuffer_new
BIO_F_MEM_WRITE:117:mem_write
BIO_F_NBIOF_NEW:154:nbiof_new
BIO_F_SLG_WRITE:155:slg_write
BIO_F_SSL_NEW:118:SSL_new
BN_F_BNRAND:127:bnrand
BN_F_BNRAND_RANGE:138:bnrand_range
BN_F_BN_BLINDING_CONVERT_EX:100:BN_BLINDING_convert_ex
BN_F_BN_BLINDING_CREATE_PARAM:128:BN_BLINDING_create_param
BN_F_BN_BLINDING_INVERT_EX:101:BN_BLINDING_invert_ex
BN_F_BN_BLINDING_NEW:102:BN_BLINDING_new
BN_F_BN_BLINDING_UPDATE:103:BN_BLINDING_update
BN_F_BN_BN2DEC:104:BN_bn2dec
BN_F_BN_BN2HEX:105:BN_bn2hex
BN_F_BN_COMPUTE_WNAF:142:bn_compute_wNAF
BN_F_BN_CTX_GET:116:BN_CTX_get
BN_F_BN_CTX_NEW:106:BN_CTX_new
BN_F_BN_CTX_START:129:BN_CTX_start
BN_F_BN_DIV:107:BN_div
BN_F_BN_DIV_RECP:130:BN_div_recp
BN_F_BN_EXP:123:BN_exp
BN_F_BN_EXPAND_INTERNAL:120:bn_expand_internal
BN_F_BN_GENCB_NEW:143:BN_GENCB_new
BN_F_BN_GENERATE_DSA_NONCE:140:BN_generate_dsa_nonce
BN_F_BN_GENERATE_PRIME_EX:141:BN_generate_prime_ex
BN_F_BN_GF2M_MOD:131:BN_GF2m_mod
BN_F_BN_GF2M_MOD_EXP:132:BN_GF2m_mod_exp
BN_F_BN_GF2M_MOD_MUL:133:BN_GF2m_mod_mul
BN_F_BN_GF2M_MOD_SOLVE_QUAD:134:BN_GF2m_mod_solve_quad
BN_F_BN_GF2M_MOD_SOLVE_QUAD_ARR:135:BN_GF2m_mod_solve_quad_arr
BN_F_BN_GF2M_MOD_SQR:136:BN_GF2m_mod_sqr
BN_F_BN_GF2M_MOD_SQRT:137:BN_GF2m_mod_sqrt
BN_F_BN_LSHIFT:145:BN_lshift
BN_F_BN_MOD_EXP2_MONT:118:BN_mod_exp2_mont
BN_F_BN_MOD_EXP_MONT:109:BN_mod_exp_mont
BN_F_BN_MOD_EXP_MONT_CONSTTIME:124:BN_mod_exp_mont_consttime
BN_F_BN_MOD_EXP_MONT_WORD:117:BN_mod_exp_mont_word
BN_F_BN_MOD_EXP_RECP:125:BN_mod_exp_recp
BN_F_BN_MOD_EXP_SIMPLE:126:BN_mod_exp_simple
BN_F_BN_MOD_INVERSE:110:BN_mod_inverse
BN_F_BN_MOD_INVERSE_NO_BRANCH:139:BN_mod_inverse_no_branch
BN_F_BN_MOD_LSHIFT_QUICK:119:BN_mod_lshift_quick
BN_F_BN_MOD_SQRT:121:BN_mod_sqrt
BN_F_BN_MONT_CTX_NEW:149:BN_MONT_CTX_new
BN_F_BN_MPI2BN:112:BN_mpi2bn
BN_F_BN_NEW:113:BN_new
BN_F_BN_POOL_GET:147:BN_POOL_get
BN_F_BN_RAND:114:BN_rand
BN_F_BN_RAND_RANGE:122:BN_rand_range
BN_F_BN_RECP_CTX_NEW:150:BN_RECP_CTX_new
BN_F_BN_RSHIFT:146:BN_rshift
BN_F_BN_SET_WORDS:144:bn_set_words
BN_F_BN_STACK_PUSH:148:BN_STACK_push
BN_F_BN_USUB:115:BN_usub
BUF_F_BUF_MEM_GROW:100:BUF_MEM_grow
BUF_F_BUF_MEM_GROW_CLEAN:105:BUF_MEM_grow_clean
BUF_F_BUF_MEM_NEW:101:BUF_MEM_new
CMS_F_CHECK_CONTENT:99:check_content
CMS_F_CMS_ADD0_CERT:164:CMS_add0_cert
CMS_F_CMS_ADD0_RECIPIENT_KEY:100:CMS_add0_recipient_key
CMS_F_CMS_ADD0_RECIPIENT_PASSWORD:165:CMS_add0_recipient_password
CMS_F_CMS_ADD1_RECEIPTREQUEST:158:CMS_add1_ReceiptRequest
CMS_F_CMS_ADD1_RECIPIENT_CERT:101:CMS_add1_recipient_cert
CMS_F_CMS_ADD1_SIGNER:102:CMS_add1_signer
CMS_F_CMS_ADD1_SIGNINGTIME:103:cms_add1_signingTime
CMS_F_CMS_COMPRESS:104:CMS_compress
CMS_F_CMS_COMPRESSEDDATA_CREATE:105:cms_CompressedData_create
CMS_F_CMS_COMPRESSEDDATA_INIT_BIO:106:cms_CompressedData_init_bio
CMS_F_CMS_COPY_CONTENT:107:cms_copy_content
CMS_F_CMS_COPY_MESSAGEDIGEST:108:cms_copy_messageDigest
CMS_F_CMS_DATA:109:CMS_data
CMS_F_CMS_DATAFINAL:110:CMS_dataFinal
CMS_F_CMS_DATAINIT:111:CMS_dataInit
CMS_F_CMS_DECRYPT:112:CMS_decrypt
CMS_F_CMS_DECRYPT_SET1_KEY:113:CMS_decrypt_set1_key
CMS_F_CMS_DECRYPT_SET1_PASSWORD:166:CMS_decrypt_set1_password
CMS_F_CMS_DECRYPT_SET1_PKEY:114:CMS_decrypt_set1_pkey
CMS_F_CMS_DIGESTALGORITHM_FIND_CTX:115:cms_DigestAlgorithm_find_ctx
CMS_F_CMS_DIGESTALGORITHM_INIT_BIO:116:cms_DigestAlgorithm_init_bio
CMS_F_CMS_DIGESTEDDATA_DO_FINAL:117:cms_DigestedData_do_final
CMS_F_CMS_DIGEST_VERIFY:118:CMS_digest_verify
CMS_F_CMS_ENCODE_RECEIPT:161:cms_encode_Receipt
CMS_F_CMS_ENCRYPT:119:CMS_encrypt
CMS_F_CMS_ENCRYPTEDCONTENT_INIT:179:cms_EncryptedContent_init
CMS_F_CMS_ENCRYPTEDCONTENT_INIT_BIO:120:cms_EncryptedContent_init_bio
CMS_F_CMS_ENCRYPTEDDATA_DECRYPT:121:CMS_EncryptedData_decrypt
CMS_F_CMS_ENCRYPTEDDATA_ENCRYPT:122:CMS_EncryptedData_encrypt
CMS_F_CMS_ENCRYPTEDDATA_SET1_KEY:123:CMS_EncryptedData_set1_key
CMS_F_CMS_ENVELOPEDDATA_CREATE:124:CMS_EnvelopedData_create
CMS_F_CMS_ENVELOPEDDATA_INIT_BIO:125:cms_EnvelopedData_init_bio
CMS_F_CMS_ENVELOPED_DATA_INIT:126:cms_enveloped_data_init
CMS_F_CMS_ENV_ASN1_CTRL:171:cms_env_asn1_ctrl
CMS_F_CMS_FINAL:127:CMS_final
CMS_F_CMS_GET0_CERTIFICATE_CHOICES:128:cms_get0_certificate_choices
CMS_F_CMS_GET0_CONTENT:129:CMS_get0_content
CMS_F_CMS_GET0_ECONTENT_TYPE:130:cms_get0_econtent_type
CMS_F_CMS_GET0_ENVELOPED:131:cms_get0_enveloped
CMS_F_CMS_GET0_REVOCATION_CHOICES:132:cms_get0_revocation_choices
CMS_F_CMS_GET0_SIGNED:133:cms_get0_signed
CMS_F_CMS_MSGSIGDIGEST_ADD1:162:cms_msgSigDigest_add1
CMS_F_CMS_RECEIPTREQUEST_CREATE0:159:CMS_ReceiptRequest_create0
CMS_F_CMS_RECEIPT_VERIFY:160:cms_Receipt_verify
CMS_F_CMS_RECIPIENTINFO_DECRYPT:134:CMS_RecipientInfo_decrypt
CMS_F_CMS_RECIPIENTINFO_ENCRYPT:169:CMS_RecipientInfo_encrypt
CMS_F_CMS_RECIPIENTINFO_KARI_ENCRYPT:178:cms_RecipientInfo_kari_encrypt
CMS_F_CMS_RECIPIENTINFO_KARI_GET0_ALG:175:CMS_RecipientInfo_kari_get0_alg
CMS_F_CMS_RECIPIENTINFO_KARI_GET0_ORIG_ID:173:\
	CMS_RecipientInfo_kari_get0_orig_id
CMS_F_CMS_RECIPIENTINFO_KARI_GET0_REKS:172:CMS_RecipientInfo_kari_get0_reks
CMS_F_CMS_RECIPIENTINFO_KARI_ORIG_ID_CMP:174:CMS_RecipientInfo_kari_orig_id_cmp
CMS_F_CMS_RECIPIENTINFO_KEKRI_DECRYPT:135:cms_RecipientInfo_kekri_decrypt
CMS_F_CMS_RECIPIENTINFO_KEKRI_ENCRYPT:136:cms_RecipientInfo_kekri_encrypt
CMS_F_CMS_RECIPIENTINFO_KEKRI_GET0_ID:137:CMS_RecipientInfo_kekri_get0_id
CMS_F_CMS_RECIPIENTINFO_KEKRI_ID_CMP:138:CMS_RecipientInfo_kekri_id_cmp
CMS_F_CMS_RECIPIENTINFO_KTRI_CERT_CMP:139:CMS_RecipientInfo_ktri_cert_cmp
CMS_F_CMS_RECIPIENTINFO_KTRI_DECRYPT:140:cms_RecipientInfo_ktri_decrypt
CMS_F_CMS_RECIPIENTINFO_KTRI_ENCRYPT:141:cms_RecipientInfo_ktri_encrypt
CMS_F_CMS_RECIPIENTINFO_KTRI_GET0_ALGS:142:CMS_RecipientInfo_ktri_get0_algs
CMS_F_CMS_RECIPIENTINFO_KTRI_GET0_SIGNER_ID:143:\
	CMS_RecipientInfo_ktri_get0_signer_id
CMS_F_CMS_RECIPIENTINFO_PWRI_CRYPT:167:cms_RecipientInfo_pwri_crypt
CMS_F_CMS_RECIPIENTINFO_SET0_KEY:144:CMS_RecipientInfo_set0_key
CMS_F_CMS_RECIPIENTINFO_SET0_PASSWORD:168:CMS_RecipientInfo_set0_password
CMS_F_CMS_RECIPIENTINFO_SET0_PKEY:145:CMS_RecipientInfo_set0_pkey
CMS_F_CMS_SD_ASN1_CTRL:170:cms_sd_asn1_ctrl
CMS_F_CMS_SET1_IAS:176:cms_set1_ias
CMS_F_CMS_SET1_KEYID:177:cms_set1_keyid
CMS_F_CMS_SET1_SIGNERIDENTIFIER:146:cms_set1_SignerIdentifier
CMS_F_CMS_SET_DETACHED:147:CMS_set_detached
CMS_F_CMS_SIGN:148:CMS_sign
CMS_F_CMS_SIGNED_DATA_INIT:149:cms_signed_data_init
CMS_F_CMS_SIGNERINFO_CONTENT_SIGN:150:cms_SignerInfo_content_sign
CMS_F_CMS_SIGNERINFO_SIGN:151:CMS_SignerInfo_sign
CMS_F_CMS_SIGNERINFO_VERIFY:152:CMS_SignerInfo_verify
CMS_F_CMS_SIGNERINFO_VERIFY_CERT:153:cms_signerinfo_verify_cert
CMS_F_CMS_SIGNERINFO_VERIFY_CONTENT:154:CMS_SignerInfo_verify_content
CMS_F_CMS_SIGN_RECEIPT:163:CMS_sign_receipt
CMS_F_CMS_SI_CHECK_ATTRIBUTES:183:CMS_si_check_attributes
CMS_F_CMS_STREAM:155:CMS_stream
CMS_F_CMS_UNCOMPRESS:156:CMS_uncompress
CMS_F_CMS_VERIFY:157:CMS_verify
CMS_F_KEK_UNWRAP_KEY:180:kek_unwrap_key
COMP_F_BIO_ZLIB_FLUSH:99:bio_zlib_flush
COMP_F_BIO_ZLIB_NEW:100:bio_zlib_new
COMP_F_BIO_ZLIB_READ:101:bio_zlib_read
COMP_F_BIO_ZLIB_WRITE:102:bio_zlib_write
COMP_F_COMP_CTX_NEW:103:COMP_CTX_new
CONF_F_CONF_DUMP_FP:104:CONF_dump_fp
CONF_F_CONF_LOAD:100:CONF_load
CONF_F_CONF_LOAD_FP:103:CONF_load_fp
CONF_F_CONF_PARSE_LIST:119:CONF_parse_list
CONF_F_DEF_LOAD:120:def_load
CONF_F_DEF_LOAD_BIO:121:def_load_bio
CONF_F_GET_NEXT_FILE:107:get_next_file
CONF_F_MODULE_ADD:122:module_add
CONF_F_MODULE_INIT:115:module_init
CONF_F_MODULE_LOAD_DSO:117:module_load_dso
CONF_F_MODULE_RUN:118:module_run
CONF_F_NCONF_DUMP_BIO:105:NCONF_dump_bio
CONF_F_NCONF_DUMP_FP:106:NCONF_dump_fp
CONF_F_NCONF_GET_NUMBER_E:112:NCONF_get_number_e
CONF_F_NCONF_GET_SECTION:108:NCONF_get_section
CONF_F_NCONF_GET_STRING:109:NCONF_get_string
CONF_F_NCONF_LOAD:113:NCONF_load
CONF_F_NCONF_LOAD_BIO:110:NCONF_load_bio
CONF_F_NCONF_LOAD_FP:114:NCONF_load_fp
CONF_F_NCONF_NEW:111:NCONF_new
CONF_F_PROCESS_INCLUDE:116:process_include
CONF_F_SSL_MODULE_INIT:123:ssl_module_init
CONF_F_STR_COPY:101:str_copy
CRYPTO_F_CMAC_CTX_NEW:120:CMAC_CTX_new
CRYPTO_F_CRYPTO_DUP_EX_DATA:110:CRYPTO_dup_ex_data
CRYPTO_F_CRYPTO_FREE_EX_DATA:111:CRYPTO_free_ex_data
CRYPTO_F_CRYPTO_GET_EX_NEW_INDEX:100:CRYPTO_get_ex_new_index
CRYPTO_F_CRYPTO_MEMDUP:115:CRYPTO_memdup
CRYPTO_F_CRYPTO_NEW_EX_DATA:112:CRYPTO_new_ex_data
CRYPTO_F_CRYPTO_OCB128_COPY_CTX:121:CRYPTO_ocb128_copy_ctx
CRYPTO_F_CRYPTO_OCB128_INIT:122:CRYPTO_ocb128_init
CRYPTO_F_CRYPTO_SET_EX_DATA:102:CRYPTO_set_ex_data
CRYPTO_F_FIPS_MODE_SET:109:FIPS_mode_set
CRYPTO_F_GET_AND_LOCK:113:get_and_lock
CRYPTO_F_OPENSSL_ATEXIT:114:OPENSSL_atexit
CRYPTO_F_OPENSSL_BUF2HEXSTR:117:OPENSSL_buf2hexstr
CRYPTO_F_OPENSSL_FOPEN:119:openssl_fopen
CRYPTO_F_OPENSSL_HEXSTR2BUF:118:OPENSSL_hexstr2buf
CRYPTO_F_OPENSSL_INIT_CRYPTO:116:OPENSSL_init_crypto
CRYPTO_F_OPENSSL_LH_NEW:126:OPENSSL_LH_new
CRYPTO_F_OPENSSL_SK_DEEP_COPY:127:OPENSSL_sk_deep_copy
CRYPTO_F_OPENSSL_SK_DUP:128:OPENSSL_sk_dup
CRYPTO_F_PKEY_HMAC_INIT:123:pkey_hmac_init
CRYPTO_F_PKEY_POLY1305_INIT:124:pkey_poly1305_init
CRYPTO_F_PKEY_SIPHASH_INIT:125:pkey_siphash_init
CRYPTO_F_SK_RESERVE:129:sk_reserve
CT_F_CTLOG_NEW:117:CTLOG_new
CT_F_CTLOG_NEW_FROM_BASE64:118:CTLOG_new_from_base64
CT_F_CTLOG_NEW_FROM_CONF:119:ctlog_new_from_conf
CT_F_CTLOG_STORE_LOAD_CTX_NEW:122:ctlog_store_load_ctx_new
CT_F_CTLOG_STORE_LOAD_FILE:123:CTLOG_STORE_load_file
CT_F_CTLOG_STORE_LOAD_LOG:130:ctlog_store_load_log
CT_F_CTLOG_STORE_NEW:131:CTLOG_STORE_new
CT_F_CT_BASE64_DECODE:124:ct_base64_decode
CT_F_CT_POLICY_EVAL_CTX_NEW:133:CT_POLICY_EVAL_CTX_new
CT_F_CT_V1_LOG_ID_FROM_PKEY:125:ct_v1_log_id_from_pkey
CT_F_I2O_SCT:107:i2o_SCT
CT_F_I2O_SCT_LIST:108:i2o_SCT_LIST
CT_F_I2O_SCT_SIGNATURE:109:i2o_SCT_signature
CT_F_O2I_SCT:110:o2i_SCT
CT_F_O2I_SCT_LIST:111:o2i_SCT_LIST
CT_F_O2I_SCT_SIGNATURE:112:o2i_SCT_signature
CT_F_SCT_CTX_NEW:126:SCT_CTX_new
CT_F_SCT_CTX_VERIFY:128:SCT_CTX_verify
CT_F_SCT_NEW:100:SCT_new
CT_F_SCT_NEW_FROM_BASE64:127:SCT_new_from_base64
CT_F_SCT_SET0_LOG_ID:101:SCT_set0_log_id
CT_F_SCT_SET1_EXTENSIONS:114:SCT_set1_extensions
CT_F_SCT_SET1_LOG_ID:115:SCT_set1_log_id
CT_F_SCT_SET1_SIGNATURE:116:SCT_set1_signature
CT_F_SCT_SET_LOG_ENTRY_TYPE:102:SCT_set_log_entry_type
CT_F_SCT_SET_SIGNATURE_NID:103:SCT_set_signature_nid
CT_F_SCT_SET_VERSION:104:SCT_set_version
DH_F_COMPUTE_KEY:102:compute_key
DH_F_DHPARAMS_PRINT_FP:101:DHparams_print_fp
DH_F_DH_BUILTIN_GENPARAMS:106:dh_builtin_genparams
DH_F_DH_CHECK:126:DH_check
DH_F_DH_CHECK_EX:121:DH_check_ex
DH_F_DH_CHECK_PARAMS_EX:122:DH_check_params_ex
DH_F_DH_CHECK_PUB_KEY_EX:123:DH_check_pub_key_ex
DH_F_DH_CMS_DECRYPT:114:dh_cms_decrypt
DH_F_DH_CMS_SET_PEERKEY:115:dh_cms_set_peerkey
DH_F_DH_CMS_SET_SHARED_INFO:116:dh_cms_set_shared_info
DH_F_DH_METH_DUP:117:DH_meth_dup
DH_F_DH_METH_NEW:118:DH_meth_new
DH_F_DH_METH_SET1_NAME:119:DH_meth_set1_name
DH_F_DH_NEW_BY_NID:104:DH_new_by_nid
DH_F_DH_NEW_METHOD:105:DH_new_method
DH_F_DH_PARAM_DECODE:107:dh_param_decode
DH_F_DH_PKEY_PUBLIC_CHECK:124:dh_pkey_public_check
DH_F_DH_PRIV_DECODE:110:dh_priv_decode
DH_F_DH_PRIV_ENCODE:111:dh_priv_encode
DH_F_DH_PUB_DECODE:108:dh_pub_decode
DH_F_DH_PUB_ENCODE:109:dh_pub_encode
DH_F_DO_DH_PRINT:100:do_dh_print
DH_F_GENERATE_KEY:103:generate_key
DH_F_PKEY_DH_CTRL_STR:120:pkey_dh_ctrl_str
DH_F_PKEY_DH_DERIVE:112:pkey_dh_derive
DH_F_PKEY_DH_INIT:125:pkey_dh_init
DH_F_PKEY_DH_KEYGEN:113:pkey_dh_keygen
DSA_F_DSAPARAMS_PRINT:100:DSAparams_print
DSA_F_DSAPARAMS_PRINT_FP:101:DSAparams_print_fp
DSA_F_DSA_BUILTIN_PARAMGEN:125:dsa_builtin_paramgen
DSA_F_DSA_BUILTIN_PARAMGEN2:126:dsa_builtin_paramgen2
DSA_F_DSA_DO_SIGN:112:DSA_do_sign
DSA_F_DSA_DO_VERIFY:113:DSA_do_verify
DSA_F_DSA_METH_DUP:127:DSA_meth_dup
DSA_F_DSA_METH_NEW:128:DSA_meth_new
DSA_F_DSA_METH_SET1_NAME:129:DSA_meth_set1_name
DSA_F_DSA_NEW_METHOD:103:DSA_new_method
DSA_F_DSA_PARAM_DECODE:119:dsa_param_decode
DSA_F_DSA_PRINT_FP:105:DSA_print_fp
DSA_F_DSA_PRIV_DECODE:115:dsa_priv_decode
DSA_F_DSA_PRIV_ENCODE:116:dsa_priv_encode
DSA_F_DSA_PUB_DECODE:117:dsa_pub_decode
DSA_F_DSA_PUB_ENCODE:118:dsa_pub_encode
DSA_F_DSA_SIGN:106:DSA_sign
DSA_F_DSA_SIGN_SETUP:107:DSA_sign_setup
DSA_F_DSA_SIG_NEW:102:DSA_SIG_new
DSA_F_OLD_DSA_PRIV_DECODE:122:old_dsa_priv_decode
DSA_F_PKEY_DSA_CTRL:120:pkey_dsa_ctrl
DSA_F_PKEY_DSA_CTRL_STR:104:pkey_dsa_ctrl_str
DSA_F_PKEY_DSA_KEYGEN:121:pkey_dsa_keygen
DSO_F_DLFCN_BIND_FUNC:100:dlfcn_bind_func
DSO_F_DLFCN_LOAD:102:dlfcn_load
DSO_F_DLFCN_MERGER:130:dlfcn_merger
DSO_F_DLFCN_NAME_CONVERTER:123:dlfcn_name_converter
DSO_F_DLFCN_UNLOAD:103:dlfcn_unload
DSO_F_DL_BIND_FUNC:104:dl_bind_func
DSO_F_DL_LOAD:106:dl_load
DSO_F_DL_MERGER:131:dl_merger
DSO_F_DL_NAME_CONVERTER:124:dl_name_converter
DSO_F_DL_UNLOAD:107:dl_unload
DSO_F_DSO_BIND_FUNC:108:DSO_bind_func
DSO_F_DSO_CONVERT_FILENAME:126:DSO_convert_filename
DSO_F_DSO_CTRL:110:DSO_ctrl
DSO_F_DSO_FREE:111:DSO_free
DSO_F_DSO_GET_FILENAME:127:DSO_get_filename
DSO_F_DSO_GLOBAL_LOOKUP:139:DSO_global_lookup
DSO_F_DSO_LOAD:112:DSO_load
DSO_F_DSO_MERGE:132:DSO_merge
DSO_F_DSO_NEW_METHOD:113:DSO_new_method
DSO_F_DSO_PATHBYADDR:105:DSO_pathbyaddr
DSO_F_DSO_SET_FILENAME:129:DSO_set_filename
DSO_F_DSO_UP_REF:114:DSO_up_ref
DSO_F_VMS_BIND_SYM:115:vms_bind_sym
DSO_F_VMS_LOAD:116:vms_load
DSO_F_VMS_MERGER:133:vms_merger
DSO_F_VMS_UNLOAD:117:vms_unload
DSO_F_WIN32_BIND_FUNC:101:win32_bind_func
DSO_F_WIN32_GLOBALLOOKUP:142:win32_globallookup
DSO_F_WIN32_JOINER:135:win32_joiner
DSO_F_WIN32_LOAD:120:win32_load
DSO_F_WIN32_MERGER:134:win32_merger
DSO_F_WIN32_NAME_CONVERTER:125:win32_name_converter
DSO_F_WIN32_PATHBYADDR:109:*
DSO_F_WIN32_SPLITTER:136:win32_splitter
DSO_F_WIN32_UNLOAD:121:win32_unload
EC_F_BN_TO_FELEM:224:BN_to_felem
EC_F_D2I_ECPARAMETERS:144:d2i_ECParameters
EC_F_D2I_ECPKPARAMETERS:145:d2i_ECPKParameters
EC_F_D2I_ECPRIVATEKEY:146:d2i_ECPrivateKey
EC_F_DO_EC_KEY_PRINT:221:do_EC_KEY_print
EC_F_ECDH_CMS_DECRYPT:238:ecdh_cms_decrypt
EC_F_ECDH_CMS_SET_SHARED_INFO:239:ecdh_cms_set_shared_info
EC_F_ECDH_COMPUTE_KEY:246:ECDH_compute_key
EC_F_ECDH_SIMPLE_COMPUTE_KEY:257:ecdh_simple_compute_key
EC_F_ECDSA_DO_SIGN_EX:251:ECDSA_do_sign_ex
EC_F_ECDSA_DO_VERIFY:252:ECDSA_do_verify
EC_F_ECDSA_SIGN_EX:254:ECDSA_sign_ex
EC_F_ECDSA_SIGN_SETUP:248:ECDSA_sign_setup
EC_F_ECDSA_SIG_NEW:265:ECDSA_SIG_new
EC_F_ECDSA_VERIFY:253:ECDSA_verify
EC_F_ECD_ITEM_VERIFY:270:ecd_item_verify
EC_F_ECKEY_PARAM2TYPE:223:eckey_param2type
EC_F_ECKEY_PARAM_DECODE:212:eckey_param_decode
EC_F_ECKEY_PRIV_DECODE:213:eckey_priv_decode
EC_F_ECKEY_PRIV_ENCODE:214:eckey_priv_encode
EC_F_ECKEY_PUB_DECODE:215:eckey_pub_decode
EC_F_ECKEY_PUB_ENCODE:216:eckey_pub_encode
EC_F_ECKEY_TYPE2PARAM:220:eckey_type2param
EC_F_ECPARAMETERS_PRINT:147:ECParameters_print
EC_F_ECPARAMETERS_PRINT_FP:148:ECParameters_print_fp
EC_F_ECPKPARAMETERS_PRINT:149:ECPKParameters_print
EC_F_ECPKPARAMETERS_PRINT_FP:150:ECPKParameters_print_fp
EC_F_ECP_NISTZ256_GET_AFFINE:240:ecp_nistz256_get_affine
EC_F_ECP_NISTZ256_INV_MOD_ORD:275:ecp_nistz256_inv_mod_ord
EC_F_ECP_NISTZ256_MULT_PRECOMPUTE:243:ecp_nistz256_mult_precompute
EC_F_ECP_NISTZ256_POINTS_MUL:241:ecp_nistz256_points_mul
EC_F_ECP_NISTZ256_PRE_COMP_NEW:244:ecp_nistz256_pre_comp_new
EC_F_ECP_NISTZ256_WINDOWED_MUL:242:ecp_nistz256_windowed_mul
EC_F_ECX_KEY_OP:266:ecx_key_op
EC_F_ECX_PRIV_ENCODE:267:ecx_priv_encode
EC_F_ECX_PUB_ENCODE:268:ecx_pub_encode
EC_F_EC_ASN1_GROUP2CURVE:153:ec_asn1_group2curve
EC_F_EC_ASN1_GROUP2FIELDID:154:ec_asn1_group2fieldid
EC_F_EC_GF2M_MONTGOMERY_POINT_MULTIPLY:208:ec_GF2m_montgomery_point_multiply
EC_F_EC_GF2M_SIMPLE_FIELD_INV:296:ec_GF2m_simple_field_inv
EC_F_EC_GF2M_SIMPLE_GROUP_CHECK_DISCRIMINANT:159:\
	ec_GF2m_simple_group_check_discriminant
EC_F_EC_GF2M_SIMPLE_GROUP_SET_CURVE:195:ec_GF2m_simple_group_set_curve
EC_F_EC_GF2M_SIMPLE_LADDER_POST:285:ec_GF2m_simple_ladder_post
EC_F_EC_GF2M_SIMPLE_LADDER_PRE:288:ec_GF2m_simple_ladder_pre
EC_F_EC_GF2M_SIMPLE_OCT2POINT:160:ec_GF2m_simple_oct2point
EC_F_EC_GF2M_SIMPLE_POINT2OCT:161:ec_GF2m_simple_point2oct
EC_F_EC_GF2M_SIMPLE_POINTS_MUL:289:ec_GF2m_simple_points_mul
EC_F_EC_GF2M_SIMPLE_POINT_GET_AFFINE_COORDINATES:162:\
	ec_GF2m_simple_point_get_affine_coordinates
EC_F_EC_GF2M_SIMPLE_POINT_SET_AFFINE_COORDINATES:163:\
	ec_GF2m_simple_point_set_affine_coordinates
EC_F_EC_GF2M_SIMPLE_SET_COMPRESSED_COORDINATES:164:\
	ec_GF2m_simple_set_compressed_coordinates
EC_F_EC_GFP_MONT_FIELD_DECODE:133:ec_GFp_mont_field_decode
EC_F_EC_GFP_MONT_FIELD_ENCODE:134:ec_GFp_mont_field_encode
EC_F_EC_GFP_MONT_FIELD_INV:297:ec_GFp_mont_field_inv
EC_F_EC_GFP_MONT_FIELD_MUL:131:ec_GFp_mont_field_mul
EC_F_EC_GFP_MONT_FIELD_SET_TO_ONE:209:ec_GFp_mont_field_set_to_one
EC_F_EC_GFP_MONT_FIELD_SQR:132:ec_GFp_mont_field_sqr
EC_F_EC_GFP_MONT_GROUP_SET_CURVE:189:ec_GFp_mont_group_set_curve
EC_F_EC_GFP_NISTP224_GROUP_SET_CURVE:225:ec_GFp_nistp224_group_set_curve
EC_F_EC_GFP_NISTP224_POINTS_MUL:228:ec_GFp_nistp224_points_mul
EC_F_EC_GFP_NISTP224_POINT_GET_AFFINE_COORDINATES:226:\
	ec_GFp_nistp224_point_get_affine_coordinates
EC_F_EC_GFP_NISTP256_GROUP_SET_CURVE:230:ec_GFp_nistp256_group_set_curve
EC_F_EC_GFP_NISTP256_POINTS_MUL:231:ec_GFp_nistp256_points_mul
EC_F_EC_GFP_NISTP256_POINT_GET_AFFINE_COORDINATES:232:\
	ec_GFp_nistp256_point_get_affine_coordinates
EC_F_EC_GFP_NISTP521_GROUP_SET_CURVE:233:ec_GFp_nistp521_group_set_curve
EC_F_EC_GFP_NISTP521_POINTS_MUL:234:ec_GFp_nistp521_points_mul
EC_F_EC_GFP_NISTP521_POINT_GET_AFFINE_COORDINATES:235:\
	ec_GFp_nistp521_point_get_affine_coordinates
EC_F_EC_GFP_NIST_FIELD_MUL:200:ec_GFp_nist_field_mul
EC_F_EC_GFP_NIST_FIELD_SQR:201:ec_GFp_nist_field_sqr
EC_F_EC_GFP_NIST_GROUP_SET_CURVE:202:ec_GFp_nist_group_set_curve
EC_F_EC_GFP_SIMPLE_BLIND_COORDINATES:287:ec_GFp_simple_blind_coordinates
EC_F_EC_GFP_SIMPLE_FIELD_INV:298:ec_GFp_simple_field_inv
EC_F_EC_GFP_SIMPLE_GROUP_CHECK_DISCRIMINANT:165:\
	ec_GFp_simple_group_check_discriminant
EC_F_EC_GFP_SIMPLE_GROUP_SET_CURVE:166:ec_GFp_simple_group_set_curve
EC_F_EC_GFP_SIMPLE_MAKE_AFFINE:102:ec_GFp_simple_make_affine
EC_F_EC_GFP_SIMPLE_OCT2POINT:103:ec_GFp_simple_oct2point
EC_F_EC_GFP_SIMPLE_POINT2OCT:104:ec_GFp_simple_point2oct
EC_F_EC_GFP_SIMPLE_POINTS_MAKE_AFFINE:137:ec_GFp_simple_points_make_affine
EC_F_EC_GFP_SIMPLE_POINT_GET_AFFINE_COORDINATES:167:\
	ec_GFp_simple_point_get_affine_coordinates
EC_F_EC_GFP_SIMPLE_POINT_SET_AFFINE_COORDINATES:168:\
	ec_GFp_simple_point_set_affine_coordinates
EC_F_EC_GFP_SIMPLE_SET_COMPRESSED_COORDINATES:169:\
	ec_GFp_simple_set_compressed_coordinates
EC_F_EC_GROUP_CHECK:170:EC_GROUP_check
EC_F_EC_GROUP_CHECK_DISCRIMINANT:171:EC_GROUP_check_discriminant
EC_F_EC_GROUP_COPY:106:EC_GROUP_copy
EC_F_EC_GROUP_GET_CURVE:291:EC_GROUP_get_curve
EC_F_EC_GROUP_GET_CURVE_GF2M:172:EC_GROUP_get_curve_GF2m
EC_F_EC_GROUP_GET_CURVE_GFP:130:EC_GROUP_get_curve_GFp
EC_F_EC_GROUP_GET_DEGREE:173:EC_GROUP_get_degree
EC_F_EC_GROUP_GET_ECPARAMETERS:261:EC_GROUP_get_ecparameters
EC_F_EC_GROUP_GET_ECPKPARAMETERS:262:EC_GROUP_get_ecpkparameters
EC_F_EC_GROUP_GET_PENTANOMIAL_BASIS:193:EC_GROUP_get_pentanomial_basis
EC_F_EC_GROUP_GET_TRINOMIAL_BASIS:194:EC_GROUP_get_trinomial_basis
EC_F_EC_GROUP_NEW:108:EC_GROUP_new
EC_F_EC_GROUP_NEW_BY_CURVE_NAME:174:EC_GROUP_new_by_curve_name
EC_F_EC_GROUP_NEW_FROM_DATA:175:ec_group_new_from_data
EC_F_EC_GROUP_NEW_FROM_ECPARAMETERS:263:EC_GROUP_new_from_ecparameters
EC_F_EC_GROUP_NEW_FROM_ECPKPARAMETERS:264:EC_GROUP_new_from_ecpkparameters
EC_F_EC_GROUP_SET_CURVE:292:EC_GROUP_set_curve
EC_F_EC_GROUP_SET_CURVE_GF2M:176:EC_GROUP_set_curve_GF2m
EC_F_EC_GROUP_SET_CURVE_GFP:109:EC_GROUP_set_curve_GFp
EC_F_EC_GROUP_SET_GENERATOR:111:EC_GROUP_set_generator
EC_F_EC_GROUP_SET_SEED:286:EC_GROUP_set_seed
EC_F_EC_KEY_CHECK_KEY:177:EC_KEY_check_key
EC_F_EC_KEY_COPY:178:EC_KEY_copy
EC_F_EC_KEY_GENERATE_KEY:179:EC_KEY_generate_key
EC_F_EC_KEY_NEW:182:EC_KEY_new
EC_F_EC_KEY_NEW_METHOD:245:EC_KEY_new_method
EC_F_EC_KEY_OCT2PRIV:255:EC_KEY_oct2priv
EC_F_EC_KEY_PRINT:180:EC_KEY_print
EC_F_EC_KEY_PRINT_FP:181:EC_KEY_print_fp
EC_F_EC_KEY_PRIV2BUF:279:EC_KEY_priv2buf
EC_F_EC_KEY_PRIV2OCT:256:EC_KEY_priv2oct
EC_F_EC_KEY_SET_PUBLIC_KEY_AFFINE_COORDINATES:229:\
	EC_KEY_set_public_key_affine_coordinates
EC_F_EC_KEY_SIMPLE_CHECK_KEY:258:ec_key_simple_check_key
EC_F_EC_KEY_SIMPLE_OCT2PRIV:259:ec_key_simple_oct2priv
EC_F_EC_KEY_SIMPLE_PRIV2OCT:260:ec_key_simple_priv2oct
EC_F_EC_PKEY_CHECK:273:ec_pkey_check
EC_F_EC_PKEY_PARAM_CHECK:274:ec_pkey_param_check
EC_F_EC_POINTS_MAKE_AFFINE:136:EC_POINTs_make_affine
EC_F_EC_POINTS_MUL:290:EC_POINTs_mul
EC_F_EC_POINT_ADD:112:EC_POINT_add
EC_F_EC_POINT_BN2POINT:280:EC_POINT_bn2point
EC_F_EC_POINT_CMP:113:EC_POINT_cmp
EC_F_EC_POINT_COPY:114:EC_POINT_copy
EC_F_EC_POINT_DBL:115:EC_POINT_dbl
EC_F_EC_POINT_GET_AFFINE_COORDINATES:293:EC_POINT_get_affine_coordinates
EC_F_EC_POINT_GET_AFFINE_COORDINATES_GF2M:183:\
	EC_POINT_get_affine_coordinates_GF2m
EC_F_EC_POINT_GET_AFFINE_COORDINATES_GFP:116:EC_POINT_get_affine_coordinates_GFp
EC_F_EC_POINT_GET_JPROJECTIVE_COORDINATES_GFP:117:\
	EC_POINT_get_Jprojective_coordinates_GFp
EC_F_EC_POINT_INVERT:210:EC_POINT_invert
EC_F_EC_POINT_IS_AT_INFINITY:118:EC_POINT_is_at_infinity
EC_F_EC_POINT_IS_ON_CURVE:119:EC_POINT_is_on_curve
EC_F_EC_POINT_MAKE_AFFINE:120:EC_POINT_make_affine
EC_F_EC_POINT_NEW:121:EC_POINT_new
EC_F_EC_POINT_OCT2POINT:122:EC_POINT_oct2point
EC_F_EC_POINT_POINT2BUF:281:EC_POINT_point2buf
EC_F_EC_POINT_POINT2OCT:123:EC_POINT_point2oct
EC_F_EC_POINT_SET_AFFINE_COORDINATES:294:EC_POINT_set_affine_coordinates
EC_F_EC_POINT_SET_AFFINE_COORDINATES_GF2M:185:\
	EC_POINT_set_affine_coordinates_GF2m
EC_F_EC_POINT_SET_AFFINE_COORDINATES_GFP:124:EC_POINT_set_affine_coordinates_GFp
EC_F_EC_POINT_SET_COMPRESSED_COORDINATES:295:EC_POINT_set_compressed_coordinates
EC_F_EC_POINT_SET_COMPRESSED_COORDINATES_GF2M:186:\
	EC_POINT_set_compressed_coordinates_GF2m
EC_F_EC_POINT_SET_COMPRESSED_COORDINATES_GFP:125:\
	EC_POINT_set_compressed_coordinates_GFp
EC_F_EC_POINT_SET_JPROJECTIVE_COORDINATES_GFP:126:\
	EC_POINT_set_Jprojective_coordinates_GFp
EC_F_EC_POINT_SET_TO_INFINITY:127:EC_POINT_set_to_infinity
EC_F_EC_PRE_COMP_NEW:196:ec_pre_comp_new
EC_F_EC_SCALAR_MUL_LADDER:284:ec_scalar_mul_ladder
EC_F_EC_WNAF_MUL:187:ec_wNAF_mul
EC_F_EC_WNAF_PRECOMPUTE_MULT:188:ec_wNAF_precompute_mult
EC_F_I2D_ECPARAMETERS:190:i2d_ECParameters
EC_F_I2D_ECPKPARAMETERS:191:i2d_ECPKParameters
EC_F_I2D_ECPRIVATEKEY:192:i2d_ECPrivateKey
EC_F_I2O_ECPUBLICKEY:151:i2o_ECPublicKey
EC_F_NISTP224_PRE_COMP_NEW:227:nistp224_pre_comp_new
EC_F_NISTP256_PRE_COMP_NEW:236:nistp256_pre_comp_new
EC_F_NISTP521_PRE_COMP_NEW:237:nistp521_pre_comp_new
EC_F_O2I_ECPUBLICKEY:152:o2i_ECPublicKey
EC_F_OLD_EC_PRIV_DECODE:222:old_ec_priv_decode
EC_F_OSSL_ECDH_COMPUTE_KEY:247:ossl_ecdh_compute_key
EC_F_OSSL_ECDSA_SIGN_SIG:249:ossl_ecdsa_sign_sig
EC_F_OSSL_ECDSA_VERIFY_SIG:250:ossl_ecdsa_verify_sig
EC_F_PKEY_ECD_CTRL:271:pkey_ecd_ctrl
EC_F_PKEY_ECD_DIGESTSIGN:272:pkey_ecd_digestsign
EC_F_PKEY_ECD_DIGESTSIGN25519:276:pkey_ecd_digestsign25519
EC_F_PKEY_ECD_DIGESTSIGN448:277:pkey_ecd_digestsign448
EC_F_PKEY_ECX_DERIVE:269:pkey_ecx_derive
EC_F_PKEY_EC_CTRL:197:pkey_ec_ctrl
EC_F_PKEY_EC_CTRL_STR:198:pkey_ec_ctrl_str
EC_F_PKEY_EC_DERIVE:217:pkey_ec_derive
EC_F_PKEY_EC_INIT:282:pkey_ec_init
EC_F_PKEY_EC_KDF_DERIVE:283:pkey_ec_kdf_derive
EC_F_PKEY_EC_KEYGEN:199:pkey_ec_keygen
EC_F_PKEY_EC_PARAMGEN:219:pkey_ec_paramgen
EC_F_PKEY_EC_SIGN:218:pkey_ec_sign
EC_F_VALIDATE_ECX_DERIVE:278:validate_ecx_derive
ENGINE_F_DIGEST_UPDATE:198:digest_update
ENGINE_F_DYNAMIC_CTRL:180:dynamic_ctrl
ENGINE_F_DYNAMIC_GET_DATA_CTX:181:dynamic_get_data_ctx
ENGINE_F_DYNAMIC_LOAD:182:dynamic_load
ENGINE_F_DYNAMIC_SET_DATA_CTX:183:dynamic_set_data_ctx
ENGINE_F_ENGINE_ADD:105:ENGINE_add
ENGINE_F_ENGINE_BY_ID:106:ENGINE_by_id
ENGINE_F_ENGINE_CMD_IS_EXECUTABLE:170:ENGINE_cmd_is_executable
ENGINE_F_ENGINE_CTRL:142:ENGINE_ctrl
ENGINE_F_ENGINE_CTRL_CMD:178:ENGINE_ctrl_cmd
ENGINE_F_ENGINE_CTRL_CMD_STRING:171:ENGINE_ctrl_cmd_string
ENGINE_F_ENGINE_FINISH:107:ENGINE_finish
ENGINE_F_ENGINE_GET_CIPHER:185:ENGINE_get_cipher
ENGINE_F_ENGINE_GET_DIGEST:186:ENGINE_get_digest
ENGINE_F_ENGINE_GET_FIRST:195:ENGINE_get_first
ENGINE_F_ENGINE_GET_LAST:196:ENGINE_get_last
ENGINE_F_ENGINE_GET_NEXT:115:ENGINE_get_next
ENGINE_F_ENGINE_GET_PKEY_ASN1_METH:193:ENGINE_get_pkey_asn1_meth
ENGINE_F_ENGINE_GET_PKEY_METH:192:ENGINE_get_pkey_meth
ENGINE_F_ENGINE_GET_PREV:116:ENGINE_get_prev
ENGINE_F_ENGINE_INIT:119:ENGINE_init
ENGINE_F_ENGINE_LIST_ADD:120:engine_list_add
ENGINE_F_ENGINE_LIST_REMOVE:121:engine_list_remove
ENGINE_F_ENGINE_LOAD_PRIVATE_KEY:150:ENGINE_load_private_key
ENGINE_F_ENGINE_LOAD_PUBLIC_KEY:151:ENGINE_load_public_key
ENGINE_F_ENGINE_LOAD_SSL_CLIENT_CERT:194:ENGINE_load_ssl_client_cert
ENGINE_F_ENGINE_NEW:122:ENGINE_new
ENGINE_F_ENGINE_PKEY_ASN1_FIND_STR:197:ENGINE_pkey_asn1_find_str
ENGINE_F_ENGINE_REMOVE:123:ENGINE_remove
ENGINE_F_ENGINE_SET_DEFAULT_STRING:189:ENGINE_set_default_string
ENGINE_F_ENGINE_SET_ID:129:ENGINE_set_id
ENGINE_F_ENGINE_SET_NAME:130:ENGINE_set_name
ENGINE_F_ENGINE_TABLE_REGISTER:184:engine_table_register
ENGINE_F_ENGINE_UNLOCKED_FINISH:191:engine_unlocked_finish
ENGINE_F_ENGINE_UP_REF:190:ENGINE_up_ref
ENGINE_F_INT_CLEANUP_ITEM:199:int_cleanup_item
ENGINE_F_INT_CTRL_HELPER:172:int_ctrl_helper
ENGINE_F_INT_ENGINE_CONFIGURE:188:int_engine_configure
ENGINE_F_INT_ENGINE_MODULE_INIT:187:int_engine_module_init
ENGINE_F_OSSL_HMAC_INIT:200:ossl_hmac_init
EVP_F_AESNI_INIT_KEY:165:aesni_init_key
EVP_F_AESNI_XTS_INIT_KEY:207:aesni_xts_init_key
EVP_F_AES_GCM_CTRL:196:aes_gcm_ctrl
EVP_F_AES_INIT_KEY:133:aes_init_key
EVP_F_AES_OCB_CIPHER:169:aes_ocb_cipher
EVP_F_AES_T4_INIT_KEY:178:aes_t4_init_key
EVP_F_AES_T4_XTS_INIT_KEY:208:aes_t4_xts_init_key
EVP_F_AES_WRAP_CIPHER:170:aes_wrap_cipher
EVP_F_AES_XTS_INIT_KEY:209:aes_xts_init_key
EVP_F_ALG_MODULE_INIT:177:alg_module_init
EVP_F_ARIA_CCM_INIT_KEY:175:aria_ccm_init_key
EVP_F_ARIA_GCM_CTRL:197:aria_gcm_ctrl
EVP_F_ARIA_GCM_INIT_KEY:176:aria_gcm_init_key
EVP_F_ARIA_INIT_KEY:185:aria_init_key
EVP_F_B64_NEW:198:b64_new
EVP_F_CAMELLIA_INIT_KEY:159:camellia_init_key
EVP_F_CHACHA20_POLY1305_CTRL:182:chacha20_poly1305_ctrl
EVP_F_CMLL_T4_INIT_KEY:179:cmll_t4_init_key
EVP_F_DES_EDE3_WRAP_CIPHER:171:des_ede3_wrap_cipher
EVP_F_DO_SIGVER_INIT:161:do_sigver_init
EVP_F_ENC_NEW:199:enc_new
EVP_F_EVP_CIPHERINIT_EX:123:EVP_CipherInit_ex
EVP_F_EVP_CIPHER_ASN1_TO_PARAM:204:EVP_CIPHER_asn1_to_param
EVP_F_EVP_CIPHER_CTX_COPY:163:EVP_CIPHER_CTX_copy
EVP_F_EVP_CIPHER_CTX_CTRL:124:EVP_CIPHER_CTX_ctrl
EVP_F_EVP_CIPHER_CTX_SET_KEY_LENGTH:122:EVP_CIPHER_CTX_set_key_length
EVP_F_EVP_CIPHER_PARAM_TO_ASN1:205:EVP_CIPHER_param_to_asn1
EVP_F_EVP_DECRYPTFINAL_EX:101:EVP_DecryptFinal_ex
EVP_F_EVP_DECRYPTUPDATE:166:EVP_DecryptUpdate
EVP_F_EVP_DIGESTFINALXOF:174:EVP_DigestFinalXOF
EVP_F_EVP_DIGESTINIT_EX:128:EVP_DigestInit_ex
EVP_F_EVP_ENCRYPTDECRYPTUPDATE:219:evp_EncryptDecryptUpdate
EVP_F_EVP_ENCRYPTFINAL_EX:127:EVP_EncryptFinal_ex
EVP_F_EVP_ENCRYPTUPDATE:167:EVP_EncryptUpdate
EVP_F_EVP_MD_CTX_COPY_EX:110:EVP_MD_CTX_copy_ex
EVP_F_EVP_MD_SIZE:162:EVP_MD_size
EVP_F_EVP_OPENINIT:102:EVP_OpenInit
EVP_F_EVP_PBE_ALG_ADD:115:EVP_PBE_alg_add
EVP_F_EVP_PBE_ALG_ADD_TYPE:160:EVP_PBE_alg_add_type
EVP_F_EVP_PBE_CIPHERINIT:116:EVP_PBE_CipherInit
EVP_F_EVP_PBE_SCRYPT:181:EVP_PBE_scrypt
EVP_F_EVP_PKCS82PKEY:111:EVP_PKCS82PKEY
EVP_F_EVP_PKEY2PKCS8:113:EVP_PKEY2PKCS8
EVP_F_EVP_PKEY_ASN1_ADD0:188:EVP_PKEY_asn1_add0
EVP_F_EVP_PKEY_CHECK:186:EVP_PKEY_check
EVP_F_EVP_PKEY_COPY_PARAMETERS:103:EVP_PKEY_copy_parameters
EVP_F_EVP_PKEY_CTX_CTRL:137:EVP_PKEY_CTX_ctrl
EVP_F_EVP_PKEY_CTX_CTRL_STR:150:EVP_PKEY_CTX_ctrl_str
EVP_F_EVP_PKEY_CTX_DUP:156:EVP_PKEY_CTX_dup
EVP_F_EVP_PKEY_CTX_MD:168:EVP_PKEY_CTX_md
EVP_F_EVP_PKEY_DECRYPT:104:EVP_PKEY_decrypt
EVP_F_EVP_PKEY_DECRYPT_INIT:138:EVP_PKEY_decrypt_init
EVP_F_EVP_PKEY_DECRYPT_OLD:151:EVP_PKEY_decrypt_old
EVP_F_EVP_PKEY_DERIVE:153:EVP_PKEY_derive
EVP_F_EVP_PKEY_DERIVE_INIT:154:EVP_PKEY_derive_init
EVP_F_EVP_PKEY_DERIVE_SET_PEER:155:EVP_PKEY_derive_set_peer
EVP_F_EVP_PKEY_ENCRYPT:105:EVP_PKEY_encrypt
EVP_F_EVP_PKEY_ENCRYPT_INIT:139:EVP_PKEY_encrypt_init
EVP_F_EVP_PKEY_ENCRYPT_OLD:152:EVP_PKEY_encrypt_old
EVP_F_EVP_PKEY_GET0_DH:119:EVP_PKEY_get0_DH
EVP_F_EVP_PKEY_GET0_DSA:120:EVP_PKEY_get0_DSA
EVP_F_EVP_PKEY_GET0_EC_KEY:131:EVP_PKEY_get0_EC_KEY
EVP_F_EVP_PKEY_GET0_HMAC:183:EVP_PKEY_get0_hmac
EVP_F_EVP_PKEY_GET0_POLY1305:184:EVP_PKEY_get0_poly1305
EVP_F_EVP_PKEY_GET0_RSA:121:EVP_PKEY_get0_RSA
EVP_F_EVP_PKEY_GET0_SIPHASH:172:EVP_PKEY_get0_siphash
EVP_F_EVP_PKEY_GET_RAW_PRIVATE_KEY:202:EVP_PKEY_get_raw_private_key
EVP_F_EVP_PKEY_GET_RAW_PUBLIC_KEY:203:EVP_PKEY_get_raw_public_key
EVP_F_EVP_PKEY_KEYGEN:146:EVP_PKEY_keygen
EVP_F_EVP_PKEY_KEYGEN_INIT:147:EVP_PKEY_keygen_init
EVP_F_EVP_PKEY_METH_ADD0:194:EVP_PKEY_meth_add0
EVP_F_EVP_PKEY_METH_NEW:195:EVP_PKEY_meth_new
EVP_F_EVP_PKEY_NEW:106:EVP_PKEY_new
EVP_F_EVP_PKEY_NEW_CMAC_KEY:193:EVP_PKEY_new_CMAC_key
EVP_F_EVP_PKEY_NEW_RAW_PRIVATE_KEY:191:EVP_PKEY_new_raw_private_key
EVP_F_EVP_PKEY_NEW_RAW_PUBLIC_KEY:192:EVP_PKEY_new_raw_public_key
EVP_F_EVP_PKEY_PARAMGEN:148:EVP_PKEY_paramgen
EVP_F_EVP_PKEY_PARAMGEN_INIT:149:EVP_PKEY_paramgen_init
EVP_F_EVP_PKEY_PARAM_CHECK:189:EVP_PKEY_param_check
EVP_F_EVP_PKEY_PUBLIC_CHECK:190:EVP_PKEY_public_check
EVP_F_EVP_PKEY_SET1_ENGINE:187:EVP_PKEY_set1_engine
EVP_F_EVP_PKEY_SET_ALIAS_TYPE:206:EVP_PKEY_set_alias_type
EVP_F_EVP_PKEY_SIGN:140:EVP_PKEY_sign
EVP_F_EVP_PKEY_SIGN_INIT:141:EVP_PKEY_sign_init
EVP_F_EVP_PKEY_VERIFY:142:EVP_PKEY_verify
EVP_F_EVP_PKEY_VERIFY_INIT:143:EVP_PKEY_verify_init
EVP_F_EVP_PKEY_VERIFY_RECOVER:144:EVP_PKEY_verify_recover
EVP_F_EVP_PKEY_VERIFY_RECOVER_INIT:145:EVP_PKEY_verify_recover_init
EVP_F_EVP_SIGNFINAL:107:EVP_SignFinal
EVP_F_EVP_VERIFYFINAL:108:EVP_VerifyFinal
EVP_F_INT_CTX_NEW:157:int_ctx_new
EVP_F_OK_NEW:200:ok_new
EVP_F_PKCS5_PBE_KEYIVGEN:117:PKCS5_PBE_keyivgen
EVP_F_PKCS5_V2_PBE_KEYIVGEN:118:PKCS5_v2_PBE_keyivgen
EVP_F_PKCS5_V2_PBKDF2_KEYIVGEN:164:PKCS5_v2_PBKDF2_keyivgen
EVP_F_PKCS5_V2_SCRYPT_KEYIVGEN:180:PKCS5_v2_scrypt_keyivgen
EVP_F_PKEY_SET_TYPE:158:pkey_set_type
EVP_F_RC2_MAGIC_TO_METH:109:rc2_magic_to_meth
EVP_F_RC5_CTRL:125:rc5_ctrl
EVP_F_R_32_12_16_INIT_KEY:242:r_32_12_16_init_key
EVP_F_S390X_AES_GCM_CTRL:201:s390x_aes_gcm_ctrl
EVP_F_UPDATE:173:update
KDF_F_PKEY_HKDF_CTRL_STR:103:pkey_hkdf_ctrl_str
KDF_F_PKEY_HKDF_DERIVE:102:pkey_hkdf_derive
KDF_F_PKEY_HKDF_INIT:108:pkey_hkdf_init
KDF_F_PKEY_SCRYPT_CTRL_STR:104:pkey_scrypt_ctrl_str
KDF_F_PKEY_SCRYPT_CTRL_UINT64:105:pkey_scrypt_ctrl_uint64
KDF_F_PKEY_SCRYPT_DERIVE:109:pkey_scrypt_derive
KDF_F_PKEY_SCRYPT_INIT:106:pkey_scrypt_init
KDF_F_PKEY_SCRYPT_SET_MEMBUF:107:pkey_scrypt_set_membuf
KDF_F_PKEY_TLS1_PRF_CTRL_STR:100:pkey_tls1_prf_ctrl_str
KDF_F_PKEY_TLS1_PRF_DERIVE:101:pkey_tls1_prf_derive
KDF_F_PKEY_TLS1_PRF_INIT:110:pkey_tls1_prf_init
KDF_F_TLS1_PRF_ALG:111:tls1_prf_alg
OBJ_F_OBJ_ADD_OBJECT:105:OBJ_add_object
OBJ_F_OBJ_ADD_SIGID:107:OBJ_add_sigid
OBJ_F_OBJ_CREATE:100:OBJ_create
OBJ_F_OBJ_DUP:101:OBJ_dup
OBJ_F_OBJ_NAME_NEW_INDEX:106:OBJ_NAME_new_index
OBJ_F_OBJ_NID2LN:102:OBJ_nid2ln
OBJ_F_OBJ_NID2OBJ:103:OBJ_nid2obj
OBJ_F_OBJ_NID2SN:104:OBJ_nid2sn
OBJ_F_OBJ_TXT2OBJ:108:OBJ_txt2obj
OCSP_F_D2I_OCSP_NONCE:102:d2i_ocsp_nonce
OCSP_F_OCSP_BASIC_ADD1_STATUS:103:OCSP_basic_add1_status
OCSP_F_OCSP_BASIC_SIGN:104:OCSP_basic_sign
OCSP_F_OCSP_BASIC_SIGN_CTX:119:OCSP_basic_sign_ctx
OCSP_F_OCSP_BASIC_VERIFY:105:OCSP_basic_verify
OCSP_F_OCSP_CERT_ID_NEW:101:OCSP_cert_id_new
OCSP_F_OCSP_CHECK_DELEGATED:106:ocsp_check_delegated
OCSP_F_OCSP_CHECK_IDS:107:ocsp_check_ids
OCSP_F_OCSP_CHECK_ISSUER:108:ocsp_check_issuer
OCSP_F_OCSP_CHECK_VALIDITY:115:OCSP_check_validity
OCSP_F_OCSP_MATCH_ISSUERID:109:ocsp_match_issuerid
OCSP_F_OCSP_PARSE_URL:114:OCSP_parse_url
OCSP_F_OCSP_REQUEST_SIGN:110:OCSP_request_sign
OCSP_F_OCSP_REQUEST_VERIFY:116:OCSP_request_verify
OCSP_F_OCSP_RESPONSE_GET1_BASIC:111:OCSP_response_get1_basic
OCSP_F_PARSE_HTTP_LINE1:118:parse_http_line1
OSSL_STORE_F_FILE_CTRL:129:file_ctrl
OSSL_STORE_F_FILE_FIND:138:file_find
OSSL_STORE_F_FILE_GET_PASS:118:file_get_pass
OSSL_STORE_F_FILE_LOAD:119:file_load
OSSL_STORE_F_FILE_LOAD_TRY_DECODE:124:file_load_try_decode
OSSL_STORE_F_FILE_NAME_TO_URI:126:file_name_to_uri
OSSL_STORE_F_FILE_OPEN:120:file_open
OSSL_STORE_F_OSSL_STORE_ATTACH_PEM_BIO:127:ossl_store_attach_pem_bio
OSSL_STORE_F_OSSL_STORE_EXPECT:130:OSSL_STORE_expect
OSSL_STORE_F_OSSL_STORE_FILE_ATTACH_PEM_BIO_INT:128:\
	ossl_store_file_attach_pem_bio_int
OSSL_STORE_F_OSSL_STORE_FIND:131:OSSL_STORE_find
OSSL_STORE_F_OSSL_STORE_GET0_LOADER_INT:100:ossl_store_get0_loader_int
OSSL_STORE_F_OSSL_STORE_INFO_GET1_CERT:101:OSSL_STORE_INFO_get1_CERT
OSSL_STORE_F_OSSL_STORE_INFO_GET1_CRL:102:OSSL_STORE_INFO_get1_CRL
OSSL_STORE_F_OSSL_STORE_INFO_GET1_NAME:103:OSSL_STORE_INFO_get1_NAME
OSSL_STORE_F_OSSL_STORE_INFO_GET1_NAME_DESCRIPTION:135:\
	OSSL_STORE_INFO_get1_NAME_description
OSSL_STORE_F_OSSL_STORE_INFO_GET1_PARAMS:104:OSSL_STORE_INFO_get1_PARAMS
OSSL_STORE_F_OSSL_STORE_INFO_GET1_PKEY:105:OSSL_STORE_INFO_get1_PKEY
OSSL_STORE_F_OSSL_STORE_INFO_NEW_CERT:106:OSSL_STORE_INFO_new_CERT
OSSL_STORE_F_OSSL_STORE_INFO_NEW_CRL:107:OSSL_STORE_INFO_new_CRL
OSSL_STORE_F_OSSL_STORE_INFO_NEW_EMBEDDED:123:ossl_store_info_new_EMBEDDED
OSSL_STORE_F_OSSL_STORE_INFO_NEW_NAME:109:OSSL_STORE_INFO_new_NAME
OSSL_STORE_F_OSSL_STORE_INFO_NEW_PARAMS:110:OSSL_STORE_INFO_new_PARAMS
OSSL_STORE_F_OSSL_STORE_INFO_NEW_PKEY:111:OSSL_STORE_INFO_new_PKEY
OSSL_STORE_F_OSSL_STORE_INFO_SET0_NAME_DESCRIPTION:134:\
	OSSL_STORE_INFO_set0_NAME_description
OSSL_STORE_F_OSSL_STORE_INIT_ONCE:112:ossl_store_init_once
OSSL_STORE_F_OSSL_STORE_LOADER_NEW:113:OSSL_STORE_LOADER_new
OSSL_STORE_F_OSSL_STORE_OPEN:114:OSSL_STORE_open
OSSL_STORE_F_OSSL_STORE_OPEN_INT:115:*
OSSL_STORE_F_OSSL_STORE_REGISTER_LOADER_INT:117:ossl_store_register_loader_int
OSSL_STORE_F_OSSL_STORE_SEARCH_BY_ALIAS:132:OSSL_STORE_SEARCH_by_alias
OSSL_STORE_F_OSSL_STORE_SEARCH_BY_ISSUER_SERIAL:133:\
	OSSL_STORE_SEARCH_by_issuer_serial
OSSL_STORE_F_OSSL_STORE_SEARCH_BY_KEY_FINGERPRINT:136:\
	OSSL_STORE_SEARCH_by_key_fingerprint
OSSL_STORE_F_OSSL_STORE_SEARCH_BY_NAME:137:OSSL_STORE_SEARCH_by_name
OSSL_STORE_F_OSSL_STORE_UNREGISTER_LOADER_INT:116:\
	ossl_store_unregister_loader_int
OSSL_STORE_F_TRY_DECODE_PARAMS:121:try_decode_params
OSSL_STORE_F_TRY_DECODE_PKCS12:122:try_decode_PKCS12
OSSL_STORE_F_TRY_DECODE_PKCS8ENCRYPTED:125:try_decode_PKCS8Encrypted
PEM_F_B2I_DSS:127:b2i_dss
PEM_F_B2I_PVK_BIO:128:b2i_PVK_bio
PEM_F_B2I_RSA:129:b2i_rsa
PEM_F_CHECK_BITLEN_DSA:130:check_bitlen_dsa
PEM_F_CHECK_BITLEN_RSA:131:check_bitlen_rsa
PEM_F_D2I_PKCS8PRIVATEKEY_BIO:120:d2i_PKCS8PrivateKey_bio
PEM_F_D2I_PKCS8PRIVATEKEY_FP:121:d2i_PKCS8PrivateKey_fp
PEM_F_DO_B2I:132:do_b2i
PEM_F_DO_B2I_BIO:133:do_b2i_bio
PEM_F_DO_BLOB_HEADER:134:do_blob_header
PEM_F_DO_I2B:146:do_i2b
PEM_F_DO_PK8PKEY:126:do_pk8pkey
PEM_F_DO_PK8PKEY_FP:125:do_pk8pkey_fp
PEM_F_DO_PVK_BODY:135:do_PVK_body
PEM_F_DO_PVK_HEADER:136:do_PVK_header
PEM_F_GET_HEADER_AND_DATA:143:get_header_and_data
PEM_F_GET_NAME:144:get_name
PEM_F_I2B_PVK:137:i2b_PVK
PEM_F_I2B_PVK_BIO:138:i2b_PVK_bio
PEM_F_LOAD_IV:101:load_iv
PEM_F_PEM_ASN1_READ:102:PEM_ASN1_read
PEM_F_PEM_ASN1_READ_BIO:103:PEM_ASN1_read_bio
PEM_F_PEM_ASN1_WRITE:104:PEM_ASN1_write
PEM_F_PEM_ASN1_WRITE_BIO:105:PEM_ASN1_write_bio
PEM_F_PEM_DEF_CALLBACK:100:PEM_def_callback
PEM_F_PEM_DO_HEADER:106:PEM_do_header
PEM_F_PEM_GET_EVP_CIPHER_INFO:107:PEM_get_EVP_CIPHER_INFO
PEM_F_PEM_READ:108:PEM_read
PEM_F_PEM_READ_BIO:109:PEM_read_bio
PEM_F_PEM_READ_BIO_DHPARAMS:141:PEM_read_bio_DHparams
PEM_F_PEM_READ_BIO_EX:145:PEM_read_bio_ex
PEM_F_PEM_READ_BIO_PARAMETERS:140:PEM_read_bio_Parameters
PEM_F_PEM_READ_BIO_PRIVATEKEY:123:PEM_read_bio_PrivateKey
PEM_F_PEM_READ_DHPARAMS:142:PEM_read_DHparams
PEM_F_PEM_READ_PRIVATEKEY:124:PEM_read_PrivateKey
PEM_F_PEM_SIGNFINAL:112:PEM_SignFinal
PEM_F_PEM_WRITE:113:PEM_write
PEM_F_PEM_WRITE_BIO:114:PEM_write_bio
PEM_F_PEM_WRITE_BIO_PRIVATEKEY_TRADITIONAL:147:\
	PEM_write_bio_PrivateKey_traditional
PEM_F_PEM_WRITE_PRIVATEKEY:139:PEM_write_PrivateKey
PEM_F_PEM_X509_INFO_READ:115:PEM_X509_INFO_read
PEM_F_PEM_X509_INFO_READ_BIO:116:PEM_X509_INFO_read_bio
PEM_F_PEM_X509_INFO_WRITE_BIO:117:PEM_X509_INFO_write_bio
PKCS12_F_OPENSSL_ASC2UNI:121:OPENSSL_asc2uni
PKCS12_F_OPENSSL_UNI2ASC:124:OPENSSL_uni2asc
PKCS12_F_OPENSSL_UNI2UTF8:127:OPENSSL_uni2utf8
PKCS12_F_OPENSSL_UTF82UNI:129:OPENSSL_utf82uni
PKCS12_F_PKCS12_CREATE:105:PKCS12_create
PKCS12_F_PKCS12_GEN_MAC:107:PKCS12_gen_mac
PKCS12_F_PKCS12_INIT:109:PKCS12_init
PKCS12_F_PKCS12_ITEM_DECRYPT_D2I:106:PKCS12_item_decrypt_d2i
PKCS12_F_PKCS12_ITEM_I2D_ENCRYPT:108:PKCS12_item_i2d_encrypt
PKCS12_F_PKCS12_ITEM_PACK_SAFEBAG:117:PKCS12_item_pack_safebag
PKCS12_F_PKCS12_KEY_GEN_ASC:110:PKCS12_key_gen_asc
PKCS12_F_PKCS12_KEY_GEN_UNI:111:PKCS12_key_gen_uni
PKCS12_F_PKCS12_KEY_GEN_UTF8:116:PKCS12_key_gen_utf8
PKCS12_F_PKCS12_NEWPASS:128:PKCS12_newpass
PKCS12_F_PKCS12_PACK_P7DATA:114:PKCS12_pack_p7data
PKCS12_F_PKCS12_PACK_P7ENCDATA:115:PKCS12_pack_p7encdata
PKCS12_F_PKCS12_PARSE:118:PKCS12_parse
PKCS12_F_PKCS12_PBE_CRYPT:119:PKCS12_pbe_crypt
PKCS12_F_PKCS12_PBE_KEYIVGEN:120:PKCS12_PBE_keyivgen
PKCS12_F_PKCS12_SAFEBAG_CREATE0_P8INF:112:PKCS12_SAFEBAG_create0_p8inf
PKCS12_F_PKCS12_SAFEBAG_CREATE0_PKCS8:113:PKCS12_SAFEBAG_create0_pkcs8
PKCS12_F_PKCS12_SAFEBAG_CREATE_PKCS8_ENCRYPT:133:\
	PKCS12_SAFEBAG_create_pkcs8_encrypt
PKCS12_F_PKCS12_SETUP_MAC:122:PKCS12_setup_mac
PKCS12_F_PKCS12_SET_MAC:123:PKCS12_set_mac
PKCS12_F_PKCS12_UNPACK_AUTHSAFES:130:PKCS12_unpack_authsafes
PKCS12_F_PKCS12_UNPACK_P7DATA:131:PKCS12_unpack_p7data
PKCS12_F_PKCS12_VERIFY_MAC:126:PKCS12_verify_mac
PKCS12_F_PKCS8_ENCRYPT:125:PKCS8_encrypt
PKCS12_F_PKCS8_SET0_PBE:132:PKCS8_set0_pbe
PKCS7_F_DO_PKCS7_SIGNED_ATTRIB:136:do_pkcs7_signed_attrib
PKCS7_F_PKCS7_ADD0_ATTRIB_SIGNING_TIME:135:PKCS7_add0_attrib_signing_time
PKCS7_F_PKCS7_ADD_ATTRIB_SMIMECAP:118:PKCS7_add_attrib_smimecap
PKCS7_F_PKCS7_ADD_CERTIFICATE:100:PKCS7_add_certificate
PKCS7_F_PKCS7_ADD_CRL:101:PKCS7_add_crl
PKCS7_F_PKCS7_ADD_RECIPIENT_INFO:102:PKCS7_add_recipient_info
PKCS7_F_PKCS7_ADD_SIGNATURE:131:PKCS7_add_signature
PKCS7_F_PKCS7_ADD_SIGNER:103:PKCS7_add_signer
PKCS7_F_PKCS7_BIO_ADD_DIGEST:125:PKCS7_bio_add_digest
PKCS7_F_PKCS7_COPY_EXISTING_DIGEST:138:pkcs7_copy_existing_digest
PKCS7_F_PKCS7_CTRL:104:PKCS7_ctrl
PKCS7_F_PKCS7_DATADECODE:112:PKCS7_dataDecode
PKCS7_F_PKCS7_DATAFINAL:128:PKCS7_dataFinal
PKCS7_F_PKCS7_DATAINIT:105:PKCS7_dataInit
PKCS7_F_PKCS7_DATAVERIFY:107:PKCS7_dataVerify
PKCS7_F_PKCS7_DECRYPT:114:PKCS7_decrypt
PKCS7_F_PKCS7_DECRYPT_RINFO:133:pkcs7_decrypt_rinfo
PKCS7_F_PKCS7_ENCODE_RINFO:132:pkcs7_encode_rinfo
PKCS7_F_PKCS7_ENCRYPT:115:PKCS7_encrypt
PKCS7_F_PKCS7_FINAL:134:PKCS7_final
PKCS7_F_PKCS7_FIND_DIGEST:127:PKCS7_find_digest
PKCS7_F_PKCS7_GET0_SIGNERS:124:PKCS7_get0_signers
PKCS7_F_PKCS7_RECIP_INFO_SET:130:PKCS7_RECIP_INFO_set
PKCS7_F_PKCS7_SET_CIPHER:108:PKCS7_set_cipher
PKCS7_F_PKCS7_SET_CONTENT:109:PKCS7_set_content
PKCS7_F_PKCS7_SET_DIGEST:126:PKCS7_set_digest
PKCS7_F_PKCS7_SET_TYPE:110:PKCS7_set_type
PKCS7_F_PKCS7_SIGN:116:PKCS7_sign
PKCS7_F_PKCS7_SIGNATUREVERIFY:113:PKCS7_signatureVerify
PKCS7_F_PKCS7_SIGNER_INFO_SET:129:PKCS7_SIGNER_INFO_set
PKCS7_F_PKCS7_SIGNER_INFO_SIGN:139:PKCS7_SIGNER_INFO_sign
PKCS7_F_PKCS7_SIGN_ADD_SIGNER:137:PKCS7_sign_add_signer
PKCS7_F_PKCS7_SIMPLE_SMIMECAP:119:PKCS7_simple_smimecap
PKCS7_F_PKCS7_VERIFY:117:PKCS7_verify
RAND_F_DATA_COLLECT_METHOD:127:data_collect_method
RAND_F_DRBG_BYTES:101:drbg_bytes
RAND_F_DRBG_GET_ENTROPY:105:drbg_get_entropy
RAND_F_DRBG_SETUP:117:drbg_setup
RAND_F_GET_ENTROPY:106:get_entropy
RAND_F_RAND_BYTES:100:RAND_bytes
RAND_F_RAND_DRBG_ENABLE_LOCKING:119:rand_drbg_enable_locking
RAND_F_RAND_DRBG_GENERATE:107:RAND_DRBG_generate
RAND_F_RAND_DRBG_GET_ENTROPY:120:rand_drbg_get_entropy
RAND_F_RAND_DRBG_GET_NONCE:123:rand_drbg_get_nonce
RAND_F_RAND_DRBG_INSTANTIATE:108:RAND_DRBG_instantiate
RAND_F_RAND_DRBG_NEW:109:RAND_DRBG_new
RAND_F_RAND_DRBG_RESEED:110:RAND_DRBG_reseed
RAND_F_RAND_DRBG_RESTART:102:rand_drbg_restart
RAND_F_RAND_DRBG_SET:104:RAND_DRBG_set
RAND_F_RAND_DRBG_SET_DEFAULTS:121:RAND_DRBG_set_defaults
RAND_F_RAND_DRBG_UNINSTANTIATE:118:RAND_DRBG_uninstantiate
RAND_F_RAND_LOAD_FILE:111:RAND_load_file
RAND_F_RAND_POOL_ACQUIRE_ENTROPY:122:rand_pool_acquire_entropy
RAND_F_RAND_POOL_ADD:103:rand_pool_add
RAND_F_RAND_POOL_ADD_BEGIN:113:rand_pool_add_begin
RAND_F_RAND_POOL_ADD_END:114:rand_pool_add_end
RAND_F_RAND_POOL_ATTACH:124:rand_pool_attach
RAND_F_RAND_POOL_BYTES_NEEDED:115:rand_pool_bytes_needed
RAND_F_RAND_POOL_GROW:125:rand_pool_grow
RAND_F_RAND_POOL_NEW:116:rand_pool_new
RAND_F_RAND_PSEUDO_BYTES:126:RAND_pseudo_bytes
RAND_F_RAND_WRITE_FILE:112:RAND_write_file
RSA_F_CHECK_PADDING_MD:140:check_padding_md
RSA_F_ENCODE_PKCS1:146:encode_pkcs1
RSA_F_INT_RSA_VERIFY:145:int_rsa_verify
RSA_F_OLD_RSA_PRIV_DECODE:147:old_rsa_priv_decode
RSA_F_PKEY_PSS_INIT:165:pkey_pss_init
RSA_F_PKEY_RSA_CTRL:143:pkey_rsa_ctrl
RSA_F_PKEY_RSA_CTRL_STR:144:pkey_rsa_ctrl_str
RSA_F_PKEY_RSA_SIGN:142:pkey_rsa_sign
RSA_F_PKEY_RSA_VERIFY:149:pkey_rsa_verify
RSA_F_PKEY_RSA_VERIFYRECOVER:141:pkey_rsa_verifyrecover
RSA_F_RSA_ALGOR_TO_MD:156:rsa_algor_to_md
RSA_F_RSA_BUILTIN_KEYGEN:129:rsa_builtin_keygen
RSA_F_RSA_CHECK_KEY:123:RSA_check_key
RSA_F_RSA_CHECK_KEY_EX:160:RSA_check_key_ex
RSA_F_RSA_CMS_DECRYPT:159:rsa_cms_decrypt
RSA_F_RSA_CMS_VERIFY:158:rsa_cms_verify
RSA_F_RSA_ITEM_VERIFY:148:rsa_item_verify
RSA_F_RSA_METH_DUP:161:RSA_meth_dup
RSA_F_RSA_METH_NEW:162:RSA_meth_new
RSA_F_RSA_METH_SET1_NAME:163:RSA_meth_set1_name
RSA_F_RSA_MGF1_TO_MD:157:*
RSA_F_RSA_MULTIP_INFO_NEW:166:rsa_multip_info_new
RSA_F_RSA_NEW_METHOD:106:RSA_new_method
RSA_F_RSA_NULL:124:*
RSA_F_RSA_NULL_PRIVATE_DECRYPT:132:*
RSA_F_RSA_NULL_PRIVATE_ENCRYPT:133:*
RSA_F_RSA_NULL_PUBLIC_DECRYPT:134:*
RSA_F_RSA_NULL_PUBLIC_ENCRYPT:135:*
RSA_F_RSA_OSSL_PRIVATE_DECRYPT:101:rsa_ossl_private_decrypt
RSA_F_RSA_OSSL_PRIVATE_ENCRYPT:102:rsa_ossl_private_encrypt
RSA_F_RSA_OSSL_PUBLIC_DECRYPT:103:rsa_ossl_public_decrypt
RSA_F_RSA_OSSL_PUBLIC_ENCRYPT:104:rsa_ossl_public_encrypt
RSA_F_RSA_PADDING_ADD_NONE:107:RSA_padding_add_none
RSA_F_RSA_PADDING_ADD_PKCS1_OAEP:121:RSA_padding_add_PKCS1_OAEP
RSA_F_RSA_PADDING_ADD_PKCS1_OAEP_MGF1:154:RSA_padding_add_PKCS1_OAEP_mgf1
RSA_F_RSA_PADDING_ADD_PKCS1_PSS:125:RSA_padding_add_PKCS1_PSS
RSA_F_RSA_PADDING_ADD_PKCS1_PSS_MGF1:152:RSA_padding_add_PKCS1_PSS_mgf1
RSA_F_RSA_PADDING_ADD_PKCS1_TYPE_1:108:RSA_padding_add_PKCS1_type_1
RSA_F_RSA_PADDING_ADD_PKCS1_TYPE_2:109:RSA_padding_add_PKCS1_type_2
RSA_F_RSA_PADDING_ADD_SSLV23:110:RSA_padding_add_SSLv23
RSA_F_RSA_PADDING_ADD_X931:127:RSA_padding_add_X931
RSA_F_RSA_PADDING_CHECK_NONE:111:RSA_padding_check_none
RSA_F_RSA_PADDING_CHECK_PKCS1_OAEP:122:RSA_padding_check_PKCS1_OAEP
RSA_F_RSA_PADDING_CHECK_PKCS1_OAEP_MGF1:153:RSA_padding_check_PKCS1_OAEP_mgf1
RSA_F_RSA_PADDING_CHECK_PKCS1_TYPE_1:112:RSA_padding_check_PKCS1_type_1
RSA_F_RSA_PADDING_CHECK_PKCS1_TYPE_2:113:RSA_padding_check_PKCS1_type_2
RSA_F_RSA_PADDING_CHECK_SSLV23:114:RSA_padding_check_SSLv23
RSA_F_RSA_PADDING_CHECK_X931:128:RSA_padding_check_X931
RSA_F_RSA_PARAM_DECODE:164:rsa_param_decode
RSA_F_RSA_PRINT:115:RSA_print
RSA_F_RSA_PRINT_FP:116:RSA_print_fp
RSA_F_RSA_PRIV_DECODE:150:rsa_priv_decode
RSA_F_RSA_PRIV_ENCODE:138:rsa_priv_encode
RSA_F_RSA_PSS_GET_PARAM:151:rsa_pss_get_param
RSA_F_RSA_PSS_TO_CTX:155:rsa_pss_to_ctx
RSA_F_RSA_PUB_DECODE:139:rsa_pub_decode
RSA_F_RSA_SETUP_BLINDING:136:RSA_setup_blinding
RSA_F_RSA_SIGN:117:RSA_sign
RSA_F_RSA_SIGN_ASN1_OCTET_STRING:118:RSA_sign_ASN1_OCTET_STRING
RSA_F_RSA_VERIFY:119:RSA_verify
RSA_F_RSA_VERIFY_ASN1_OCTET_STRING:120:RSA_verify_ASN1_OCTET_STRING
RSA_F_RSA_VERIFY_PKCS1_PSS_MGF1:126:RSA_verify_PKCS1_PSS_mgf1
RSA_F_SETUP_TBUF:167:setup_tbuf
SM2_F_PKEY_SM2_COPY:115:pkey_sm2_copy
SM2_F_PKEY_SM2_CTRL:109:pkey_sm2_ctrl
SM2_F_PKEY_SM2_CTRL_STR:110:pkey_sm2_ctrl_str
SM2_F_PKEY_SM2_DIGEST_CUSTOM:114:pkey_sm2_digest_custom
SM2_F_PKEY_SM2_INIT:111:pkey_sm2_init
SM2_F_PKEY_SM2_SIGN:112:pkey_sm2_sign
SM2_F_SM2_COMPUTE_MSG_HASH:100:sm2_compute_msg_hash
SM2_F_SM2_COMPUTE_USERID_DIGEST:101:sm2_compute_userid_digest
SM2_F_SM2_COMPUTE_Z_DIGEST:113:sm2_compute_z_digest
SM2_F_SM2_DECRYPT:102:sm2_decrypt
SM2_F_SM2_ENCRYPT:103:sm2_encrypt
SM2_F_SM2_PLAINTEXT_SIZE:104:sm2_plaintext_size
SM2_F_SM2_SIGN:105:sm2_sign
SM2_F_SM2_SIG_GEN:106:sm2_sig_gen
SM2_F_SM2_SIG_VERIFY:107:sm2_sig_verify
SM2_F_SM2_VERIFY:108:sm2_verify
SSL_F_ADD_CLIENT_KEY_SHARE_EXT:438:*
SSL_F_ADD_KEY_SHARE:512:add_key_share
SSL_F_BYTES_TO_CIPHER_LIST:519:bytes_to_cipher_list
SSL_F_CHECK_SUITEB_CIPHER_LIST:331:check_suiteb_cipher_list
SSL_F_CIPHERSUITE_CB:622:ciphersuite_cb
SSL_F_CONSTRUCT_CA_NAMES:552:construct_ca_names
SSL_F_CONSTRUCT_KEY_EXCHANGE_TBS:553:construct_key_exchange_tbs
SSL_F_CONSTRUCT_STATEFUL_TICKET:636:construct_stateful_ticket
SSL_F_CONSTRUCT_STATELESS_TICKET:637:construct_stateless_ticket
SSL_F_CREATE_SYNTHETIC_MESSAGE_HASH:539:create_synthetic_message_hash
SSL_F_CREATE_TICKET_PREQUEL:638:create_ticket_prequel
SSL_F_CT_MOVE_SCTS:345:ct_move_scts
SSL_F_CT_STRICT:349:ct_strict
SSL_F_CUSTOM_EXT_ADD:554:custom_ext_add
SSL_F_CUSTOM_EXT_PARSE:555:custom_ext_parse
SSL_F_D2I_SSL_SESSION:103:d2i_SSL_SESSION
SSL_F_DANE_CTX_ENABLE:347:dane_ctx_enable
SSL_F_DANE_MTYPE_SET:393:dane_mtype_set
SSL_F_DANE_TLSA_ADD:394:dane_tlsa_add
SSL_F_DERIVE_SECRET_KEY_AND_IV:514:derive_secret_key_and_iv
SSL_F_DO_DTLS1_WRITE:245:do_dtls1_write
SSL_F_DO_SSL3_WRITE:104:do_ssl3_write
SSL_F_DTLS1_BUFFER_RECORD:247:dtls1_buffer_record
SSL_F_DTLS1_CHECK_TIMEOUT_NUM:318:dtls1_check_timeout_num
SSL_F_DTLS1_HEARTBEAT:305:*
SSL_F_DTLS1_HM_FRAGMENT_NEW:623:dtls1_hm_fragment_new
SSL_F_DTLS1_PREPROCESS_FRAGMENT:288:dtls1_preprocess_fragment
SSL_F_DTLS1_PROCESS_BUFFERED_RECORDS:424:dtls1_process_buffered_records
SSL_F_DTLS1_PROCESS_RECORD:257:dtls1_process_record
SSL_F_DTLS1_READ_BYTES:258:dtls1_read_bytes
SSL_F_DTLS1_READ_FAILED:339:dtls1_read_failed
SSL_F_DTLS1_RETRANSMIT_MESSAGE:390:dtls1_retransmit_message
SSL_F_DTLS1_WRITE_APP_DATA_BYTES:268:dtls1_write_app_data_bytes
SSL_F_DTLS1_WRITE_BYTES:545:dtls1_write_bytes
SSL_F_DTLSV1_LISTEN:350:DTLSv1_listen
SSL_F_DTLS_CONSTRUCT_CHANGE_CIPHER_SPEC:371:dtls_construct_change_cipher_spec
SSL_F_DTLS_CONSTRUCT_HELLO_VERIFY_REQUEST:385:\
	dtls_construct_hello_verify_request
SSL_F_DTLS_GET_REASSEMBLED_MESSAGE:370:dtls_get_reassembled_message
SSL_F_DTLS_PROCESS_HELLO_VERIFY:386:dtls_process_hello_verify
SSL_F_DTLS_RECORD_LAYER_NEW:635:DTLS_RECORD_LAYER_new
SSL_F_DTLS_WAIT_FOR_DRY:592:dtls_wait_for_dry
SSL_F_EARLY_DATA_COUNT_OK:532:early_data_count_ok
SSL_F_FINAL_EARLY_DATA:556:final_early_data
SSL_F_FINAL_EC_PT_FORMATS:485:final_ec_pt_formats
SSL_F_FINAL_EMS:486:final_ems
SSL_F_FINAL_KEY_SHARE:503:final_key_share
SSL_F_FINAL_MAXFRAGMENTLEN:557:final_maxfragmentlen
SSL_F_FINAL_PSK:639:final_psk
SSL_F_FINAL_QUIC_TRANSPORT_PARAMS:3012:final_quic_transport_params
SSL_F_FINAL_RENEGOTIATE:483:final_renegotiate
SSL_F_FINAL_SERVER_NAME:558:final_server_name
SSL_F_FINAL_SIG_ALGS:497:final_sig_algs
SSL_F_GET_CERT_VERIFY_TBS_DATA:588:get_cert_verify_tbs_data
SSL_F_NSS_KEYLOG_INT:500:nss_keylog_int
SSL_F_OPENSSL_INIT_SSL:342:OPENSSL_init_ssl
SSL_F_OSSL_STATEM_CLIENT13_READ_TRANSITION:436:*
SSL_F_OSSL_STATEM_CLIENT13_WRITE_TRANSITION:598:\
	ossl_statem_client13_write_transition
SSL_F_OSSL_STATEM_CLIENT_CONSTRUCT_MESSAGE:430:*
SSL_F_OSSL_STATEM_CLIENT_POST_PROCESS_MESSAGE:593:\
	ossl_statem_client_post_process_message
SSL_F_OSSL_STATEM_CLIENT_PROCESS_MESSAGE:594:ossl_statem_client_process_message
SSL_F_OSSL_STATEM_CLIENT_READ_TRANSITION:417:ossl_statem_client_read_transition
SSL_F_OSSL_STATEM_CLIENT_WRITE_TRANSITION:599:\
	ossl_statem_client_write_transition
SSL_F_OSSL_STATEM_SERVER13_READ_TRANSITION:437:*
SSL_F_OSSL_STATEM_SERVER13_WRITE_TRANSITION:600:\
	ossl_statem_server13_write_transition
SSL_F_OSSL_STATEM_SERVER_CONSTRUCT_MESSAGE:431:*
SSL_F_OSSL_STATEM_SERVER_POST_PROCESS_MESSAGE:601:\
	ossl_statem_server_post_process_message
SSL_F_OSSL_STATEM_SERVER_POST_WORK:602:ossl_statem_server_post_work
SSL_F_OSSL_STATEM_SERVER_PRE_WORK:640:ossl_statem_server_pre_work
SSL_F_OSSL_STATEM_SERVER_PROCESS_MESSAGE:603:ossl_statem_server_process_message
SSL_F_OSSL_STATEM_SERVER_READ_TRANSITION:418:ossl_statem_server_read_transition
SSL_F_OSSL_STATEM_SERVER_WRITE_TRANSITION:604:\
	ossl_statem_server_write_transition
SSL_F_PARSE_CA_NAMES:541:parse_ca_names
SSL_F_PITEM_NEW:624:pitem_new
SSL_F_PQUEUE_NEW:625:pqueue_new
SSL_F_PROCESS_KEY_SHARE_EXT:439:*
SSL_F_QUIC_CHANGE_CIPHER_STATE:3000:quic_change_cipher_state
SSL_F_QUIC_GET_MESSAGE:3001:quic_get_message
SSL_F_QUIC_SET_ENCRYPTION_SECRETS:3002:quic_set_encryption_secrets
SSL_F_READ_STATE_MACHINE:352:read_state_machine
SSL_F_SET_CLIENT_CIPHERSUITE:540:set_client_ciphersuite
SSL_F_SRP_GENERATE_CLIENT_MASTER_SECRET:595:srp_generate_client_master_secret
SSL_F_SRP_GENERATE_SERVER_MASTER_SECRET:589:srp_generate_server_master_secret
SSL_F_SRP_VERIFY_SERVER_PARAM:596:srp_verify_server_param
SSL_F_SSL3_CHANGE_CIPHER_STATE:129:ssl3_change_cipher_state
SSL_F_SSL3_CHECK_CERT_AND_ALGORITHM:130:ssl3_check_cert_and_algorithm
SSL_F_SSL3_CTRL:213:ssl3_ctrl
SSL_F_SSL3_CTX_CTRL:133:ssl3_ctx_ctrl
SSL_F_SSL3_DIGEST_CACHED_RECORDS:293:ssl3_digest_cached_records
SSL_F_SSL3_DISPATCH_ALERT:3003:ssl3_dispatch_alert
SSL_F_SSL3_DO_CHANGE_CIPHER_SPEC:292:ssl3_do_change_cipher_spec
SSL_F_SSL3_DO_WRITE:3004:ssl3_do_write
SSL_F_SSL3_ENC:608:ssl3_enc
SSL_F_SSL3_FINAL_FINISH_MAC:285:ssl3_final_finish_mac
SSL_F_SSL3_FINISH_MAC:587:ssl3_finish_mac
SSL_F_SSL3_GENERATE_KEY_BLOCK:238:ssl3_generate_key_block
SSL_F_SSL3_GENERATE_MASTER_SECRET:388:ssl3_generate_master_secret
SSL_F_SSL3_GET_RECORD:143:ssl3_get_record
SSL_F_SSL3_INIT_FINISHED_MAC:397:ssl3_init_finished_mac
SSL_F_SSL3_OUTPUT_CERT_CHAIN:147:ssl3_output_cert_chain
SSL_F_SSL3_READ_BYTES:148:ssl3_read_bytes
SSL_F_SSL3_READ_N:149:ssl3_read_n
SSL_F_SSL3_SETUP_KEY_BLOCK:157:ssl3_setup_key_block
SSL_F_SSL3_SETUP_READ_BUFFER:156:ssl3_setup_read_buffer
SSL_F_SSL3_SETUP_WRITE_BUFFER:291:ssl3_setup_write_buffer
SSL_F_SSL3_WRITE_BYTES:158:ssl3_write_bytes
SSL_F_SSL3_WRITE_PENDING:159:ssl3_write_pending
SSL_F_SSL_ADD_CERT_CHAIN:316:ssl_add_cert_chain
SSL_F_SSL_ADD_CERT_TO_BUF:319:*
SSL_F_SSL_ADD_CERT_TO_WPACKET:493:ssl_add_cert_to_wpacket
SSL_F_SSL_ADD_CLIENTHELLO_RENEGOTIATE_EXT:298:*
SSL_F_SSL_ADD_CLIENTHELLO_TLSEXT:277:*
SSL_F_SSL_ADD_CLIENTHELLO_USE_SRTP_EXT:307:*
SSL_F_SSL_ADD_DIR_CERT_SUBJECTS_TO_STACK:215:SSL_add_dir_cert_subjects_to_stack
SSL_F_SSL_ADD_FILE_CERT_SUBJECTS_TO_STACK:216:\
	SSL_add_file_cert_subjects_to_stack
SSL_F_SSL_ADD_SERVERHELLO_RENEGOTIATE_EXT:299:*
SSL_F_SSL_ADD_SERVERHELLO_TLSEXT:278:*
SSL_F_SSL_ADD_SERVERHELLO_USE_SRTP_EXT:308:*
SSL_F_SSL_BAD_METHOD:160:ssl_bad_method
SSL_F_SSL_BUILD_CERT_CHAIN:332:ssl_build_cert_chain
SSL_F_SSL_BYTES_TO_CIPHER_LIST:161:SSL_bytes_to_cipher_list
SSL_F_SSL_CACHE_CIPHERLIST:520:ssl_cache_cipherlist
SSL_F_SSL_CERT_ADD0_CHAIN_CERT:346:ssl_cert_add0_chain_cert
SSL_F_SSL_CERT_DUP:221:ssl_cert_dup
SSL_F_SSL_CERT_NEW:162:ssl_cert_new
SSL_F_SSL_CERT_SET0_CHAIN:340:ssl_cert_set0_chain
SSL_F_SSL_CHECK_PRIVATE_KEY:163:SSL_check_private_key
SSL_F_SSL_CHECK_SERVERHELLO_TLSEXT:280:*
SSL_F_SSL_CHECK_SRP_EXT_CLIENTHELLO:606:ssl_check_srp_ext_ClientHello
SSL_F_SSL_CHECK_SRVR_ECC_CERT_AND_ALG:279:ssl_check_srvr_ecc_cert_and_alg
SSL_F_SSL_CHOOSE_CLIENT_VERSION:607:ssl_choose_client_version
SSL_F_SSL_CIPHER_DESCRIPTION:626:SSL_CIPHER_description
SSL_F_SSL_CIPHER_LIST_TO_BYTES:425:ssl_cipher_list_to_bytes
SSL_F_SSL_CIPHER_PROCESS_RULESTR:230:ssl_cipher_process_rulestr
SSL_F_SSL_CIPHER_STRENGTH_SORT:231:ssl_cipher_strength_sort
SSL_F_SSL_CLEAR:164:SSL_clear
SSL_F_SSL_CLIENT_HELLO_GET1_EXTENSIONS_PRESENT:627:\
	SSL_client_hello_get1_extensions_present
SSL_F_SSL_COMP_ADD_COMPRESSION_METHOD:165:SSL_COMP_add_compression_method
SSL_F_SSL_CONF_CMD:334:SSL_CONF_cmd
SSL_F_SSL_CREATE_CIPHER_LIST:166:ssl_create_cipher_list
SSL_F_SSL_CTRL:232:SSL_ctrl
SSL_F_SSL_CTX_CHECK_PRIVATE_KEY:168:SSL_CTX_check_private_key
SSL_F_SSL_CTX_ENABLE_CT:398:SSL_CTX_enable_ct
SSL_F_SSL_CTX_MAKE_PROFILES:309:ssl_ctx_make_profiles
SSL_F_SSL_CTX_NEW:169:SSL_CTX_new
SSL_F_SSL_CTX_SET_ALPN_PROTOS:343:SSL_CTX_set_alpn_protos
SSL_F_SSL_CTX_SET_CIPHER_LIST:269:SSL_CTX_set_cipher_list
SSL_F_SSL_CTX_SET_CLIENT_CERT_ENGINE:290:SSL_CTX_set_client_cert_engine
SSL_F_SSL_CTX_SET_CT_VALIDATION_CALLBACK:396:SSL_CTX_set_ct_validation_callback
SSL_F_SSL_CTX_SET_SESSION_ID_CONTEXT:219:SSL_CTX_set_session_id_context
SSL_F_SSL_CTX_SET_SSL_VERSION:170:SSL_CTX_set_ssl_version
SSL_F_SSL_CTX_SET_TLSEXT_MAX_FRAGMENT_LENGTH:551:\
	SSL_CTX_set_tlsext_max_fragment_length
SSL_F_SSL_CTX_USE_CERTIFICATE:171:SSL_CTX_use_certificate
SSL_F_SSL_CTX_USE_CERTIFICATE_ASN1:172:SSL_CTX_use_certificate_ASN1
SSL_F_SSL_CTX_USE_CERTIFICATE_FILE:173:SSL_CTX_use_certificate_file
SSL_F_SSL_CTX_USE_PRIVATEKEY:174:SSL_CTX_use_PrivateKey
SSL_F_SSL_CTX_USE_PRIVATEKEY_ASN1:175:SSL_CTX_use_PrivateKey_ASN1
SSL_F_SSL_CTX_USE_PRIVATEKEY_FILE:176:SSL_CTX_use_PrivateKey_file
SSL_F_SSL_CTX_USE_PSK_IDENTITY_HINT:272:SSL_CTX_use_psk_identity_hint
SSL_F_SSL_CTX_USE_RSAPRIVATEKEY:177:SSL_CTX_use_RSAPrivateKey
SSL_F_SSL_CTX_USE_RSAPRIVATEKEY_ASN1:178:SSL_CTX_use_RSAPrivateKey_ASN1
SSL_F_SSL_CTX_USE_RSAPRIVATEKEY_FILE:179:SSL_CTX_use_RSAPrivateKey_file
SSL_F_SSL_CTX_USE_SERVERINFO:336:SSL_CTX_use_serverinfo
SSL_F_SSL_CTX_USE_SERVERINFO_EX:543:SSL_CTX_use_serverinfo_ex
SSL_F_SSL_CTX_USE_SERVERINFO_FILE:337:SSL_CTX_use_serverinfo_file
SSL_F_SSL_DANE_DUP:403:ssl_dane_dup
SSL_F_SSL_DANE_ENABLE:395:SSL_dane_enable
SSL_F_SSL_DERIVE:590:ssl_derive
SSL_F_SSL_DO_CONFIG:391:ssl_do_config
SSL_F_SSL_DO_HANDSHAKE:180:SSL_do_handshake
SSL_F_SSL_DUP_CA_LIST:408:SSL_dup_CA_list
SSL_F_SSL_ENABLE_CT:402:SSL_enable_ct
SSL_F_SSL_GENERATE_PKEY_GROUP:559:ssl_generate_pkey_group
SSL_F_SSL_GENERATE_SESSION_ID:547:ssl_generate_session_id
SSL_F_SSL_GET_NEW_SESSION:181:ssl_get_new_session
SSL_F_SSL_GET_PREV_SESSION:217:ssl_get_prev_session
SSL_F_SSL_GET_SERVER_CERT_INDEX:322:*
SSL_F_SSL_GET_SIGN_PKEY:183:*
SSL_F_SSL_HANDSHAKE_HASH:560:ssl_handshake_hash
SSL_F_SSL_INIT_WBIO_BUFFER:184:ssl_init_wbio_buffer
SSL_F_SSL_KEY_UPDATE:515:SSL_key_update
SSL_F_SSL_LOAD_CLIENT_CA_FILE:185:SSL_load_client_CA_file
SSL_F_SSL_LOG_MASTER_SECRET:498:*
SSL_F_SSL_LOG_RSA_CLIENT_KEY_EXCHANGE:499:ssl_log_rsa_client_key_exchange
SSL_F_SSL_MODULE_INIT:392:ssl_module_init
SSL_F_SSL_NEW:186:SSL_new
SSL_F_SSL_NEXT_PROTO_VALIDATE:565:ssl_next_proto_validate
SSL_F_SSL_PARSE_CLIENTHELLO_RENEGOTIATE_EXT:300:*
SSL_F_SSL_PARSE_CLIENTHELLO_TLSEXT:302:*
SSL_F_SSL_PARSE_CLIENTHELLO_USE_SRTP_EXT:310:*
SSL_F_SSL_PARSE_SERVERHELLO_RENEGOTIATE_EXT:301:*
SSL_F_SSL_PARSE_SERVERHELLO_TLSEXT:303:*
SSL_F_SSL_PARSE_SERVERHELLO_USE_SRTP_EXT:311:*
SSL_F_SSL_PEEK:270:SSL_peek
SSL_F_SSL_PEEK_EX:432:SSL_peek_ex
SSL_F_SSL_PEEK_INTERNAL:522:ssl_peek_internal
SSL_F_SSL_PROCESS_QUIC_POST_HANDSHAKE:3005:SSL_process_quic_post_handshake
SSL_F_SSL_PROVIDE_QUIC_DATA:3006:SSL_provide_quic_data
SSL_F_SSL_READ:223:SSL_read
SSL_F_SSL_READ_EARLY_DATA:529:SSL_read_early_data
SSL_F_SSL_READ_EX:434:SSL_read_ex
SSL_F_SSL_READ_INTERNAL:523:ssl_read_internal
SSL_F_SSL_RENEGOTIATE:516:SSL_renegotiate
SSL_F_SSL_RENEGOTIATE_ABBREVIATED:546:SSL_renegotiate_abbreviated
SSL_F_SSL_SCAN_CLIENTHELLO_TLSEXT:320:*
SSL_F_SSL_SCAN_SERVERHELLO_TLSEXT:321:*
SSL_F_SSL_SESSION_DUP:348:ssl_session_dup
SSL_F_SSL_SESSION_NEW:189:SSL_SESSION_new
SSL_F_SSL_SESSION_PRINT_FP:190:SSL_SESSION_print_fp
SSL_F_SSL_SESSION_SET1_ID:423:SSL_SESSION_set1_id
SSL_F_SSL_SESSION_SET1_ID_CONTEXT:312:SSL_SESSION_set1_id_context
SSL_F_SSL_SET_ALPN_PROTOS:344:SSL_set_alpn_protos
SSL_F_SSL_SET_CERT:191:ssl_set_cert
SSL_F_SSL_SET_CERT_AND_KEY:621:ssl_set_cert_and_key
SSL_F_SSL_SET_CIPHER_LIST:271:SSL_set_cipher_list
SSL_F_SSL_SET_CT_VALIDATION_CALLBACK:399:SSL_set_ct_validation_callback
SSL_F_SSL_SET_FD:192:SSL_set_fd
SSL_F_SSL_SET_PKEY:193:ssl_set_pkey
SSL_F_SSL_SET_RFD:194:SSL_set_rfd
SSL_F_SSL_SET_SESSION:195:SSL_set_session
SSL_F_SSL_SET_SESSION_ID_CONTEXT:218:SSL_set_session_id_context
SSL_F_SSL_SET_SESSION_TICKET_EXT:294:SSL_set_session_ticket_ext
SSL_F_SSL_SET_TLSEXT_MAX_FRAGMENT_LENGTH:550:SSL_set_tlsext_max_fragment_length
SSL_F_SSL_SET_WFD:196:SSL_set_wfd
SSL_F_SSL_SHUTDOWN:224:SSL_shutdown
SSL_F_SSL_SRP_CTX_INIT:313:SSL_SRP_CTX_init
SSL_F_SSL_START_ASYNC_JOB:389:ssl_start_async_job
SSL_F_SSL_UNDEFINED_FUNCTION:197:ssl_undefined_function
SSL_F_SSL_UNDEFINED_VOID_FUNCTION:244:ssl_undefined_void_function
SSL_F_SSL_USE_CERTIFICATE:198:SSL_use_certificate
SSL_F_SSL_USE_CERTIFICATE_ASN1:199:SSL_use_certificate_ASN1
SSL_F_SSL_USE_CERTIFICATE_FILE:200:SSL_use_certificate_file
SSL_F_SSL_USE_PRIVATEKEY:201:SSL_use_PrivateKey
SSL_F_SSL_USE_PRIVATEKEY_ASN1:202:SSL_use_PrivateKey_ASN1
SSL_F_SSL_USE_PRIVATEKEY_FILE:203:SSL_use_PrivateKey_file
SSL_F_SSL_USE_PSK_IDENTITY_HINT:273:SSL_use_psk_identity_hint
SSL_F_SSL_USE_RSAPRIVATEKEY:204:SSL_use_RSAPrivateKey
SSL_F_SSL_USE_RSAPRIVATEKEY_ASN1:205:SSL_use_RSAPrivateKey_ASN1
SSL_F_SSL_USE_RSAPRIVATEKEY_FILE:206:SSL_use_RSAPrivateKey_file
SSL_F_SSL_VALIDATE_CT:400:ssl_validate_ct
SSL_F_SSL_VERIFY_CERT_CHAIN:207:ssl_verify_cert_chain
SSL_F_SSL_VERIFY_CLIENT_POST_HANDSHAKE:616:SSL_verify_client_post_handshake
SSL_F_SSL_WRITE:208:SSL_write
SSL_F_SSL_WRITE_EARLY_DATA:526:SSL_write_early_data
SSL_F_SSL_WRITE_EARLY_FINISH:527:*
SSL_F_SSL_WRITE_EX:433:SSL_write_ex
SSL_F_SSL_WRITE_INTERNAL:524:ssl_write_internal
SSL_F_STATEM_FLUSH:3007:statem_flush
SSL_F_STATE_MACHINE:353:state_machine
SSL_F_TLS12_CHECK_PEER_SIGALG:333:tls12_check_peer_sigalg
SSL_F_TLS12_COPY_SIGALGS:533:tls12_copy_sigalgs
SSL_F_TLS13_CHANGE_CIPHER_STATE:440:tls13_change_cipher_state
SSL_F_TLS13_ENC:609:tls13_enc
SSL_F_TLS13_FINAL_FINISH_MAC:605:tls13_final_finish_mac
SSL_F_TLS13_GENERATE_SECRET:591:tls13_generate_secret
SSL_F_TLS13_HKDF_EXPAND:561:tls13_hkdf_expand
SSL_F_TLS13_RESTORE_HANDSHAKE_DIGEST_FOR_PHA:617:\
	tls13_restore_handshake_digest_for_pha
SSL_F_TLS13_SAVE_HANDSHAKE_DIGEST_FOR_PHA:618:\
	tls13_save_handshake_digest_for_pha
SSL_F_TLS13_SETUP_KEY_BLOCK:441:tls13_setup_key_block
SSL_F_TLS1_CHANGE_CIPHER_STATE:209:tls1_change_cipher_state
SSL_F_TLS1_CHECK_DUPLICATE_EXTENSIONS:341:*
SSL_F_TLS1_ENC:401:tls1_enc
SSL_F_TLS1_EXPORT_KEYING_MATERIAL:314:tls1_export_keying_material
SSL_F_TLS1_GET_CURVELIST:338:tls1_get_curvelist
SSL_F_TLS1_PRF:284:tls1_PRF
SSL_F_TLS1_SAVE_U16:628:tls1_save_u16
SSL_F_TLS1_SETUP_KEY_BLOCK:211:tls1_setup_key_block
SSL_F_TLS1_SET_GROUPS:629:tls1_set_groups
SSL_F_TLS1_SET_RAW_SIGALGS:630:tls1_set_raw_sigalgs
SSL_F_TLS1_SET_SERVER_SIGALGS:335:tls1_set_server_sigalgs
SSL_F_TLS1_SET_SHARED_SIGALGS:631:tls1_set_shared_sigalgs
SSL_F_TLS1_SET_SIGALGS:632:tls1_set_sigalgs
SSL_F_TLS_CHOOSE_SIGALG:513:tls_choose_sigalg
SSL_F_TLS_CLIENT_KEY_EXCHANGE_POST_WORK:354:tls_client_key_exchange_post_work
SSL_F_TLS_COLLECT_EXTENSIONS:435:tls_collect_extensions
SSL_F_TLS_CONSTRUCT_CERTIFICATE_AUTHORITIES:542:\
	tls_construct_certificate_authorities
SSL_F_TLS_CONSTRUCT_CERTIFICATE_REQUEST:372:tls_construct_certificate_request
SSL_F_TLS_CONSTRUCT_CERT_STATUS:429:*
SSL_F_TLS_CONSTRUCT_CERT_STATUS_BODY:494:tls_construct_cert_status_body
SSL_F_TLS_CONSTRUCT_CERT_VERIFY:496:tls_construct_cert_verify
SSL_F_TLS_CONSTRUCT_CHANGE_CIPHER_SPEC:427:tls_construct_change_cipher_spec
SSL_F_TLS_CONSTRUCT_CKE_DHE:404:tls_construct_cke_dhe
SSL_F_TLS_CONSTRUCT_CKE_ECDHE:405:tls_construct_cke_ecdhe
SSL_F_TLS_CONSTRUCT_CKE_GOST:406:tls_construct_cke_gost
SSL_F_TLS_CONSTRUCT_CKE_PSK_PREAMBLE:407:tls_construct_cke_psk_preamble
SSL_F_TLS_CONSTRUCT_CKE_RSA:409:tls_construct_cke_rsa
SSL_F_TLS_CONSTRUCT_CKE_SRP:410:tls_construct_cke_srp
SSL_F_TLS_CONSTRUCT_CLIENT_CERTIFICATE:484:tls_construct_client_certificate
SSL_F_TLS_CONSTRUCT_CLIENT_HELLO:487:tls_construct_client_hello
SSL_F_TLS_CONSTRUCT_CLIENT_KEY_EXCHANGE:488:tls_construct_client_key_exchange
SSL_F_TLS_CONSTRUCT_CLIENT_VERIFY:489:*
SSL_F_TLS_CONSTRUCT_CTOS_ALPN:466:tls_construct_ctos_alpn
SSL_F_TLS_CONSTRUCT_CTOS_CERTIFICATE:355:*
SSL_F_TLS_CONSTRUCT_CTOS_COOKIE:535:tls_construct_ctos_cookie
SSL_F_TLS_CONSTRUCT_CTOS_EARLY_DATA:530:tls_construct_ctos_early_data
SSL_F_TLS_CONSTRUCT_CTOS_EC_PT_FORMATS:467:tls_construct_ctos_ec_pt_formats
SSL_F_TLS_CONSTRUCT_CTOS_EMS:468:tls_construct_ctos_ems
SSL_F_TLS_CONSTRUCT_CTOS_ETM:469:tls_construct_ctos_etm
SSL_F_TLS_CONSTRUCT_CTOS_HELLO:356:*
SSL_F_TLS_CONSTRUCT_CTOS_KEY_EXCHANGE:357:*
SSL_F_TLS_CONSTRUCT_CTOS_KEY_SHARE:470:tls_construct_ctos_key_share
SSL_F_TLS_CONSTRUCT_CTOS_MAXFRAGMENTLEN:549:tls_construct_ctos_maxfragmentlen
SSL_F_TLS_CONSTRUCT_CTOS_NPN:471:tls_construct_ctos_npn
SSL_F_TLS_CONSTRUCT_CTOS_PADDING:472:tls_construct_ctos_padding
SSL_F_TLS_CONSTRUCT_CTOS_POST_HANDSHAKE_AUTH:619:\
	tls_construct_ctos_post_handshake_auth
SSL_F_TLS_CONSTRUCT_CTOS_PSK:501:tls_construct_ctos_psk
SSL_F_TLS_CONSTRUCT_CTOS_PSK_KEX_MODES:509:tls_construct_ctos_psk_kex_modes
SSL_F_TLS_CONSTRUCT_CTOS_QUIC_TRANSPORT_PARAMS:3008:\
	tls_construct_ctos_quic_transport_params
SSL_F_TLS_CONSTRUCT_CTOS_QUIC_TRANSPORT_PARAMS_DRAFT:3013:\
	tls_construct_ctos_quic_transport_params_draft
SSL_F_TLS_CONSTRUCT_CTOS_RENEGOTIATE:473:tls_construct_ctos_renegotiate
SSL_F_TLS_CONSTRUCT_CTOS_SCT:474:tls_construct_ctos_sct
SSL_F_TLS_CONSTRUCT_CTOS_SERVER_NAME:475:tls_construct_ctos_server_name
SSL_F_TLS_CONSTRUCT_CTOS_SESSION_TICKET:476:tls_construct_ctos_session_ticket
SSL_F_TLS_CONSTRUCT_CTOS_SIG_ALGS:477:tls_construct_ctos_sig_algs
SSL_F_TLS_CONSTRUCT_CTOS_SRP:478:tls_construct_ctos_srp
SSL_F_TLS_CONSTRUCT_CTOS_STATUS_REQUEST:479:tls_construct_ctos_status_request
SSL_F_TLS_CONSTRUCT_CTOS_SUPPORTED_GROUPS:480:\
	tls_construct_ctos_supported_groups
SSL_F_TLS_CONSTRUCT_CTOS_SUPPORTED_VERSIONS:481:\
	tls_construct_ctos_supported_versions
SSL_F_TLS_CONSTRUCT_CTOS_USE_SRTP:482:tls_construct_ctos_use_srtp
SSL_F_TLS_CONSTRUCT_CTOS_VERIFY:358:*
SSL_F_TLS_CONSTRUCT_ENCRYPTED_EXTENSIONS:443:tls_construct_encrypted_extensions
SSL_F_TLS_CONSTRUCT_END_OF_EARLY_DATA:536:tls_construct_end_of_early_data
SSL_F_TLS_CONSTRUCT_EXTENSIONS:447:tls_construct_extensions
SSL_F_TLS_CONSTRUCT_FINISHED:359:tls_construct_finished
SSL_F_TLS_CONSTRUCT_HELLO_REQUEST:373:*
SSL_F_TLS_CONSTRUCT_HELLO_RETRY_REQUEST:510:tls_construct_hello_retry_request
SSL_F_TLS_CONSTRUCT_KEY_UPDATE:517:tls_construct_key_update
SSL_F_TLS_CONSTRUCT_NEW_SESSION_TICKET:428:tls_construct_new_session_ticket
SSL_F_TLS_CONSTRUCT_NEXT_PROTO:426:tls_construct_next_proto
SSL_F_TLS_CONSTRUCT_SERVER_CERTIFICATE:490:tls_construct_server_certificate
SSL_F_TLS_CONSTRUCT_SERVER_HELLO:491:tls_construct_server_hello
SSL_F_TLS_CONSTRUCT_SERVER_KEY_EXCHANGE:492:tls_construct_server_key_exchange
SSL_F_TLS_CONSTRUCT_STOC_ALPN:451:tls_construct_stoc_alpn
SSL_F_TLS_CONSTRUCT_STOC_CERTIFICATE:374:*
SSL_F_TLS_CONSTRUCT_STOC_COOKIE:613:tls_construct_stoc_cookie
SSL_F_TLS_CONSTRUCT_STOC_CRYPTOPRO_BUG:452:tls_construct_stoc_cryptopro_bug
SSL_F_TLS_CONSTRUCT_STOC_DONE:375:*
SSL_F_TLS_CONSTRUCT_STOC_EARLY_DATA:531:tls_construct_stoc_early_data
SSL_F_TLS_CONSTRUCT_STOC_EARLY_DATA_INFO:525:*
SSL_F_TLS_CONSTRUCT_STOC_EC_PT_FORMATS:453:tls_construct_stoc_ec_pt_formats
SSL_F_TLS_CONSTRUCT_STOC_EMS:454:tls_construct_stoc_ems
SSL_F_TLS_CONSTRUCT_STOC_ETM:455:tls_construct_stoc_etm
SSL_F_TLS_CONSTRUCT_STOC_HELLO:376:*
SSL_F_TLS_CONSTRUCT_STOC_KEY_EXCHANGE:377:*
SSL_F_TLS_CONSTRUCT_STOC_KEY_SHARE:456:tls_construct_stoc_key_share
SSL_F_TLS_CONSTRUCT_STOC_MAXFRAGMENTLEN:548:tls_construct_stoc_maxfragmentlen
SSL_F_TLS_CONSTRUCT_STOC_NEXT_PROTO_NEG:457:tls_construct_stoc_next_proto_neg
SSL_F_TLS_CONSTRUCT_STOC_PSK:504:tls_construct_stoc_psk
SSL_F_TLS_CONSTRUCT_STOC_QUIC_TRANSPORT_PARAMS:3009:\
	tls_construct_stoc_quic_transport_params
SSL_F_TLS_CONSTRUCT_STOC_QUIC_TRANSPORT_PARAMS_DRAFT:3014:\
	tls_construct_stoc_quic_transport_params_draft
SSL_F_TLS_CONSTRUCT_STOC_RENEGOTIATE:458:tls_construct_stoc_renegotiate
SSL_F_TLS_CONSTRUCT_STOC_SERVER_NAME:459:tls_construct_stoc_server_name
SSL_F_TLS_CONSTRUCT_STOC_SESSION_TICKET:460:tls_construct_stoc_session_ticket
SSL_F_TLS_CONSTRUCT_STOC_STATUS_REQUEST:461:tls_construct_stoc_status_request
SSL_F_TLS_CONSTRUCT_STOC_SUPPORTED_GROUPS:544:\
	tls_construct_stoc_supported_groups
SSL_F_TLS_CONSTRUCT_STOC_SUPPORTED_VERSIONS:611:\
	tls_construct_stoc_supported_versions
SSL_F_TLS_CONSTRUCT_STOC_USE_SRTP:462:tls_construct_stoc_use_srtp
SSL_F_TLS_EARLY_POST_PROCESS_CLIENT_HELLO:521:\
	tls_early_post_process_client_hello
SSL_F_TLS_FINISH_HANDSHAKE:597:tls_finish_handshake
SSL_F_TLS_GET_MESSAGE_BODY:351:tls_get_message_body
SSL_F_TLS_GET_MESSAGE_HEADER:387:tls_get_message_header
SSL_F_TLS_HANDLE_ALPN:562:tls_handle_alpn
SSL_F_TLS_HANDLE_STATUS_REQUEST:563:tls_handle_status_request
SSL_F_TLS_PARSE_CERTIFICATE_AUTHORITIES:566:tls_parse_certificate_authorities
SSL_F_TLS_PARSE_CLIENTHELLO_TLSEXT:449:*
SSL_F_TLS_PARSE_CTOS_ALPN:567:tls_parse_ctos_alpn
SSL_F_TLS_PARSE_CTOS_COOKIE:614:tls_parse_ctos_cookie
SSL_F_TLS_PARSE_CTOS_EARLY_DATA:568:tls_parse_ctos_early_data
SSL_F_TLS_PARSE_CTOS_EC_PT_FORMATS:569:tls_parse_ctos_ec_pt_formats
SSL_F_TLS_PARSE_CTOS_EMS:570:tls_parse_ctos_ems
SSL_F_TLS_PARSE_CTOS_KEY_SHARE:463:tls_parse_ctos_key_share
SSL_F_TLS_PARSE_CTOS_MAXFRAGMENTLEN:571:tls_parse_ctos_maxfragmentlen
SSL_F_TLS_PARSE_CTOS_POST_HANDSHAKE_AUTH:620:tls_parse_ctos_post_handshake_auth
SSL_F_TLS_PARSE_CTOS_PSK:505:tls_parse_ctos_psk
SSL_F_TLS_PARSE_CTOS_PSK_KEX_MODES:572:tls_parse_ctos_psk_kex_modes
SSL_F_TLS_PARSE_CTOS_QUIC_TRANSPORT_PARAMS:3010:\
	tls_parse_ctos_quic_transport_params
SSL_F_TLS_PARSE_CTOS_QUIC_TRANSPORT_PARAMS_DRAFT:3015:\
	tls_parse_ctos_quic_transport_params_draft
SSL_F_TLS_PARSE_CTOS_RENEGOTIATE:464:tls_parse_ctos_renegotiate
SSL_F_TLS_PARSE_CTOS_SERVER_NAME:573:tls_parse_ctos_server_name
SSL_F_TLS_PARSE_CTOS_SESSION_TICKET:574:tls_parse_ctos_session_ticket
SSL_F_TLS_PARSE_CTOS_SIG_ALGS:575:tls_parse_ctos_sig_algs
SSL_F_TLS_PARSE_CTOS_SIG_ALGS_CERT:615:tls_parse_ctos_sig_algs_cert
SSL_F_TLS_PARSE_CTOS_SRP:576:tls_parse_ctos_srp
SSL_F_TLS_PARSE_CTOS_STATUS_REQUEST:577:tls_parse_ctos_status_request
SSL_F_TLS_PARSE_CTOS_SUPPORTED_GROUPS:578:tls_parse_ctos_supported_groups
SSL_F_TLS_PARSE_CTOS_USE_SRTP:465:tls_parse_ctos_use_srtp
SSL_F_TLS_PARSE_STOC_ALPN:579:tls_parse_stoc_alpn
SSL_F_TLS_PARSE_STOC_COOKIE:534:tls_parse_stoc_cookie
SSL_F_TLS_PARSE_STOC_EARLY_DATA:538:tls_parse_stoc_early_data
SSL_F_TLS_PARSE_STOC_EARLY_DATA_INFO:528:*
SSL_F_TLS_PARSE_STOC_EC_PT_FORMATS:580:tls_parse_stoc_ec_pt_formats
SSL_F_TLS_PARSE_STOC_KEY_SHARE:445:tls_parse_stoc_key_share
SSL_F_TLS_PARSE_STOC_MAXFRAGMENTLEN:581:tls_parse_stoc_maxfragmentlen
SSL_F_TLS_PARSE_STOC_NPN:582:tls_parse_stoc_npn
SSL_F_TLS_PARSE_STOC_PSK:502:tls_parse_stoc_psk
SSL_F_TLS_PARSE_STOC_QUIC_TRANSPORT_PARAMS:3011:\
	tls_parse_stoc_quic_transport_params
SSL_F_TLS_PARSE_STOC_QUIC_TRANSPORT_PARAMS_DRAFT:3016:\
	tls_parse_stoc_quic_transport_params_draft
SSL_F_TLS_PARSE_STOC_RENEGOTIATE:448:tls_parse_stoc_renegotiate
SSL_F_TLS_PARSE_STOC_SCT:564:tls_parse_stoc_sct
SSL_F_TLS_PARSE_STOC_SERVER_NAME:583:tls_parse_stoc_server_name
SSL_F_TLS_PARSE_STOC_SESSION_TICKET:584:tls_parse_stoc_session_ticket
SSL_F_TLS_PARSE_STOC_STATUS_REQUEST:585:tls_parse_stoc_status_request
SSL_F_TLS_PARSE_STOC_SUPPORTED_VERSIONS:612:tls_parse_stoc_supported_versions
SSL_F_TLS_PARSE_STOC_USE_SRTP:446:tls_parse_stoc_use_srtp
SSL_F_TLS_POST_PROCESS_CLIENT_HELLO:378:tls_post_process_client_hello
SSL_F_TLS_POST_PROCESS_CLIENT_KEY_EXCHANGE:384:\
	tls_post_process_client_key_exchange
SSL_F_TLS_PREPARE_CLIENT_CERTIFICATE:360:tls_prepare_client_certificate
SSL_F_TLS_PROCESS_AS_HELLO_RETRY_REQUEST:610:tls_process_as_hello_retry_request
SSL_F_TLS_PROCESS_CERTIFICATE_REQUEST:361:tls_process_certificate_request
SSL_F_TLS_PROCESS_CERT_STATUS:362:*
SSL_F_TLS_PROCESS_CERT_STATUS_BODY:495:tls_process_cert_status_body
SSL_F_TLS_PROCESS_CERT_VERIFY:379:tls_process_cert_verify
SSL_F_TLS_PROCESS_CHANGE_CIPHER_SPEC:363:tls_process_change_cipher_spec
SSL_F_TLS_PROCESS_CKE_DHE:411:tls_process_cke_dhe
SSL_F_TLS_PROCESS_CKE_ECDHE:412:tls_process_cke_ecdhe
SSL_F_TLS_PROCESS_CKE_GOST:413:tls_process_cke_gost
SSL_F_TLS_PROCESS_CKE_PSK_PREAMBLE:414:tls_process_cke_psk_preamble
SSL_F_TLS_PROCESS_CKE_RSA:415:tls_process_cke_rsa
SSL_F_TLS_PROCESS_CKE_SRP:416:tls_process_cke_srp
SSL_F_TLS_PROCESS_CLIENT_CERTIFICATE:380:tls_process_client_certificate
SSL_F_TLS_PROCESS_CLIENT_HELLO:381:tls_process_client_hello
SSL_F_TLS_PROCESS_CLIENT_KEY_EXCHANGE:382:tls_process_client_key_exchange
SSL_F_TLS_PROCESS_ENCRYPTED_EXTENSIONS:444:tls_process_encrypted_extensions
SSL_F_TLS_PROCESS_END_OF_EARLY_DATA:537:tls_process_end_of_early_data
SSL_F_TLS_PROCESS_FINISHED:364:tls_process_finished
SSL_F_TLS_PROCESS_HELLO_REQ:507:tls_process_hello_req
SSL_F_TLS_PROCESS_HELLO_RETRY_REQUEST:511:tls_process_hello_retry_request
SSL_F_TLS_PROCESS_INITIAL_SERVER_FLIGHT:442:tls_process_initial_server_flight
SSL_F_TLS_PROCESS_KEY_EXCHANGE:365:tls_process_key_exchange
SSL_F_TLS_PROCESS_KEY_UPDATE:518:tls_process_key_update
SSL_F_TLS_PROCESS_NEW_SESSION_TICKET:366:tls_process_new_session_ticket
SSL_F_TLS_PROCESS_NEXT_PROTO:383:tls_process_next_proto
SSL_F_TLS_PROCESS_SERVER_CERTIFICATE:367:tls_process_server_certificate
SSL_F_TLS_PROCESS_SERVER_DONE:368:tls_process_server_done
SSL_F_TLS_PROCESS_SERVER_HELLO:369:tls_process_server_hello
SSL_F_TLS_PROCESS_SKE_DHE:419:tls_process_ske_dhe
SSL_F_TLS_PROCESS_SKE_ECDHE:420:tls_process_ske_ecdhe
SSL_F_TLS_PROCESS_SKE_PSK_PREAMBLE:421:tls_process_ske_psk_preamble
SSL_F_TLS_PROCESS_SKE_SRP:422:tls_process_ske_srp
SSL_F_TLS_PSK_DO_BINDER:506:tls_psk_do_binder
SSL_F_TLS_SCAN_CLIENTHELLO_TLSEXT:450:*
SSL_F_TLS_SETUP_HANDSHAKE:508:tls_setup_handshake
SSL_F_USE_CERTIFICATE_CHAIN_FILE:220:use_certificate_chain_file
SSL_F_WPACKET_INTERN_INIT_LEN:633:wpacket_intern_init_len
SSL_F_WPACKET_START_SUB_PACKET_LEN__:634:WPACKET_start_sub_packet_len__
SSL_F_WRITE_STATE_MACHINE:586:write_state_machine
TS_F_DEF_SERIAL_CB:110:def_serial_cb
TS_F_DEF_TIME_CB:111:def_time_cb
TS_F_ESS_ADD_SIGNING_CERT:112:ess_add_signing_cert
TS_F_ESS_ADD_SIGNING_CERT_V2:147:ess_add_signing_cert_v2
TS_F_ESS_CERT_ID_NEW_INIT:113:ess_CERT_ID_new_init
TS_F_ESS_CERT_ID_V2_NEW_INIT:156:ess_cert_id_v2_new_init
TS_F_ESS_SIGNING_CERT_NEW_INIT:114:ess_SIGNING_CERT_new_init
TS_F_ESS_SIGNING_CERT_V2_NEW_INIT:157:ess_signing_cert_v2_new_init
TS_F_INT_TS_RESP_VERIFY_TOKEN:149:int_ts_RESP_verify_token
TS_F_PKCS7_TO_TS_TST_INFO:148:PKCS7_to_TS_TST_INFO
TS_F_TS_ACCURACY_SET_MICROS:115:TS_ACCURACY_set_micros
TS_F_TS_ACCURACY_SET_MILLIS:116:TS_ACCURACY_set_millis
TS_F_TS_ACCURACY_SET_SECONDS:117:TS_ACCURACY_set_seconds
TS_F_TS_CHECK_IMPRINTS:100:ts_check_imprints
TS_F_TS_CHECK_NONCES:101:ts_check_nonces
TS_F_TS_CHECK_POLICY:102:ts_check_policy
TS_F_TS_CHECK_SIGNING_CERTS:103:ts_check_signing_certs
TS_F_TS_CHECK_STATUS_INFO:104:ts_check_status_info
TS_F_TS_COMPUTE_IMPRINT:145:ts_compute_imprint
TS_F_TS_CONF_INVALID:151:ts_CONF_invalid
TS_F_TS_CONF_LOAD_CERT:153:TS_CONF_load_cert
TS_F_TS_CONF_LOAD_CERTS:154:TS_CONF_load_certs
TS_F_TS_CONF_LOAD_KEY:155:TS_CONF_load_key
TS_F_TS_CONF_LOOKUP_FAIL:152:ts_CONF_lookup_fail
TS_F_TS_CONF_SET_DEFAULT_ENGINE:146:TS_CONF_set_default_engine
TS_F_TS_GET_STATUS_TEXT:105:ts_get_status_text
TS_F_TS_MSG_IMPRINT_SET_ALGO:118:TS_MSG_IMPRINT_set_algo
TS_F_TS_REQ_SET_MSG_IMPRINT:119:TS_REQ_set_msg_imprint
TS_F_TS_REQ_SET_NONCE:120:TS_REQ_set_nonce
TS_F_TS_REQ_SET_POLICY_ID:121:TS_REQ_set_policy_id
TS_F_TS_RESP_CREATE_RESPONSE:122:TS_RESP_create_response
TS_F_TS_RESP_CREATE_TST_INFO:123:ts_RESP_create_tst_info
TS_F_TS_RESP_CTX_ADD_FAILURE_INFO:124:TS_RESP_CTX_add_failure_info
TS_F_TS_RESP_CTX_ADD_MD:125:TS_RESP_CTX_add_md
TS_F_TS_RESP_CTX_ADD_POLICY:126:TS_RESP_CTX_add_policy
TS_F_TS_RESP_CTX_NEW:127:TS_RESP_CTX_new
TS_F_TS_RESP_CTX_SET_ACCURACY:128:TS_RESP_CTX_set_accuracy
TS_F_TS_RESP_CTX_SET_CERTS:129:TS_RESP_CTX_set_certs
TS_F_TS_RESP_CTX_SET_DEF_POLICY:130:TS_RESP_CTX_set_def_policy
TS_F_TS_RESP_CTX_SET_SIGNER_CERT:131:TS_RESP_CTX_set_signer_cert
TS_F_TS_RESP_CTX_SET_STATUS_INFO:132:TS_RESP_CTX_set_status_info
TS_F_TS_RESP_GET_POLICY:133:ts_RESP_get_policy
TS_F_TS_RESP_SET_GENTIME_WITH_PRECISION:134:TS_RESP_set_genTime_with_precision
TS_F_TS_RESP_SET_STATUS_INFO:135:TS_RESP_set_status_info
TS_F_TS_RESP_SET_TST_INFO:150:TS_RESP_set_tst_info
TS_F_TS_RESP_SIGN:136:ts_RESP_sign
TS_F_TS_RESP_VERIFY_SIGNATURE:106:TS_RESP_verify_signature
TS_F_TS_TST_INFO_SET_ACCURACY:137:TS_TST_INFO_set_accuracy
TS_F_TS_TST_INFO_SET_MSG_IMPRINT:138:TS_TST_INFO_set_msg_imprint
TS_F_TS_TST_INFO_SET_NONCE:139:TS_TST_INFO_set_nonce
TS_F_TS_TST_INFO_SET_POLICY_ID:140:TS_TST_INFO_set_policy_id
TS_F_TS_TST_INFO_SET_SERIAL:141:TS_TST_INFO_set_serial
TS_F_TS_TST_INFO_SET_TIME:142:TS_TST_INFO_set_time
TS_F_TS_TST_INFO_SET_TSA:143:TS_TST_INFO_set_tsa
TS_F_TS_VERIFY:108:*
TS_F_TS_VERIFY_CERT:109:ts_verify_cert
TS_F_TS_VERIFY_CTX_NEW:144:TS_VERIFY_CTX_new
UI_F_CLOSE_CONSOLE:115:close_console
UI_F_ECHO_CONSOLE:116:echo_console
UI_F_GENERAL_ALLOCATE_BOOLEAN:108:general_allocate_boolean
UI_F_GENERAL_ALLOCATE_PROMPT:109:general_allocate_prompt
UI_F_NOECHO_CONSOLE:117:noecho_console
UI_F_OPEN_CONSOLE:114:open_console
UI_F_UI_CONSTRUCT_PROMPT:121:UI_construct_prompt
UI_F_UI_CREATE_METHOD:112:UI_create_method
UI_F_UI_CTRL:111:UI_ctrl
UI_F_UI_DUP_ERROR_STRING:101:UI_dup_error_string
UI_F_UI_DUP_INFO_STRING:102:UI_dup_info_string
UI_F_UI_DUP_INPUT_BOOLEAN:110:UI_dup_input_boolean
UI_F_UI_DUP_INPUT_STRING:103:UI_dup_input_string
UI_F_UI_DUP_USER_DATA:118:UI_dup_user_data
UI_F_UI_DUP_VERIFY_STRING:106:UI_dup_verify_string
UI_F_UI_GET0_RESULT:107:UI_get0_result
UI_F_UI_GET_RESULT_LENGTH:119:UI_get_result_length
UI_F_UI_NEW_METHOD:104:UI_new_method
UI_F_UI_PROCESS:113:UI_process
UI_F_UI_SET_RESULT:105:UI_set_result
UI_F_UI_SET_RESULT_EX:120:UI_set_result_ex
X509V3_F_A2I_GENERAL_NAME:164:a2i_GENERAL_NAME
X509V3_F_ADDR_VALIDATE_PATH_INTERNAL:166:addr_validate_path_internal
X509V3_F_ASIDENTIFIERCHOICE_CANONIZE:161:ASIdentifierChoice_canonize
X509V3_F_ASIDENTIFIERCHOICE_IS_CANONICAL:162:ASIdentifierChoice_is_canonical
X509V3_F_BIGNUM_TO_STRING:167:bignum_to_string
X509V3_F_COPY_EMAIL:122:copy_email
X509V3_F_COPY_ISSUER:123:copy_issuer
X509V3_F_DO_DIRNAME:144:do_dirname
X509V3_F_DO_EXT_I2D:135:do_ext_i2d
X509V3_F_DO_EXT_NCONF:151:do_ext_nconf
X509V3_F_GNAMES_FROM_SECTNAME:156:gnames_from_sectname
X509V3_F_I2S_ASN1_ENUMERATED:121:i2s_ASN1_ENUMERATED
X509V3_F_I2S_ASN1_IA5STRING:149:i2s_ASN1_IA5STRING
X509V3_F_I2S_ASN1_INTEGER:120:i2s_ASN1_INTEGER
X509V3_F_I2V_AUTHORITY_INFO_ACCESS:138:i2v_AUTHORITY_INFO_ACCESS
X509V3_F_I2V_AUTHORITY_KEYID:173:i2v_AUTHORITY_KEYID
X509V3_F_LEVEL_ADD_NODE:168:level_add_node
X509V3_F_NOTICE_SECTION:132:notice_section
X509V3_F_NREF_NOS:133:nref_nos
X509V3_F_POLICY_CACHE_CREATE:169:policy_cache_create
X509V3_F_POLICY_CACHE_NEW:170:policy_cache_new
X509V3_F_POLICY_DATA_NEW:171:policy_data_new
X509V3_F_POLICY_SECTION:131:policy_section
X509V3_F_PROCESS_PCI_VALUE:150:process_pci_value
X509V3_F_R2I_CERTPOL:130:r2i_certpol
X509V3_F_R2I_PCI:155:r2i_pci
X509V3_F_S2I_ASN1_IA5STRING:100:s2i_ASN1_IA5STRING
X509V3_F_S2I_ASN1_INTEGER:108:s2i_ASN1_INTEGER
X509V3_F_S2I_ASN1_OCTET_STRING:112:s2i_ASN1_OCTET_STRING
X509V3_F_S2I_SKEY_ID:115:s2i_skey_id
X509V3_F_SET_DIST_POINT_NAME:158:set_dist_point_name
X509V3_F_SXNET_ADD_ID_ASC:125:SXNET_add_id_asc
X509V3_F_SXNET_ADD_ID_INTEGER:126:SXNET_add_id_INTEGER
X509V3_F_SXNET_ADD_ID_ULONG:127:SXNET_add_id_ulong
X509V3_F_SXNET_GET_ID_ASC:128:SXNET_get_id_asc
X509V3_F_SXNET_GET_ID_ULONG:129:SXNET_get_id_ulong
X509V3_F_TREE_INIT:172:tree_init
X509V3_F_V2I_ASIDENTIFIERS:163:v2i_ASIdentifiers
X509V3_F_V2I_ASN1_BIT_STRING:101:v2i_ASN1_BIT_STRING
X509V3_F_V2I_AUTHORITY_INFO_ACCESS:139:v2i_AUTHORITY_INFO_ACCESS
X509V3_F_V2I_AUTHORITY_KEYID:119:v2i_AUTHORITY_KEYID
X509V3_F_V2I_BASIC_CONSTRAINTS:102:v2i_BASIC_CONSTRAINTS
X509V3_F_V2I_CRLD:134:v2i_crld
X509V3_F_V2I_EXTENDED_KEY_USAGE:103:v2i_EXTENDED_KEY_USAGE
X509V3_F_V2I_GENERAL_NAMES:118:v2i_GENERAL_NAMES
X509V3_F_V2I_GENERAL_NAME_EX:117:v2i_GENERAL_NAME_ex
X509V3_F_V2I_IDP:157:v2i_idp
X509V3_F_V2I_IPADDRBLOCKS:159:v2i_IPAddrBlocks
X509V3_F_V2I_ISSUER_ALT:153:v2i_issuer_alt
X509V3_F_V2I_NAME_CONSTRAINTS:147:v2i_NAME_CONSTRAINTS
X509V3_F_V2I_POLICY_CONSTRAINTS:146:v2i_POLICY_CONSTRAINTS
X509V3_F_V2I_POLICY_MAPPINGS:145:v2i_POLICY_MAPPINGS
X509V3_F_V2I_SUBJECT_ALT:154:v2i_subject_alt
X509V3_F_V2I_TLS_FEATURE:165:v2i_TLS_FEATURE
X509V3_F_V3_GENERIC_EXTENSION:116:v3_generic_extension
X509V3_F_X509V3_ADD1_I2D:140:X509V3_add1_i2d
X509V3_F_X509V3_ADD_LEN_VALUE:174:x509v3_add_len_value
X509V3_F_X509V3_ADD_VALUE:105:X509V3_add_value
X509V3_F_X509V3_EXT_ADD:104:X509V3_EXT_add
X509V3_F_X509V3_EXT_ADD_ALIAS:106:X509V3_EXT_add_alias
X509V3_F_X509V3_EXT_I2D:136:X509V3_EXT_i2d
X509V3_F_X509V3_EXT_NCONF:152:X509V3_EXT_nconf
X509V3_F_X509V3_GET_SECTION:142:X509V3_get_section
X509V3_F_X509V3_GET_STRING:143:X509V3_get_string
X509V3_F_X509V3_GET_VALUE_BOOL:110:X509V3_get_value_bool
X509V3_F_X509V3_PARSE_LIST:109:X509V3_parse_list
X509V3_F_X509_PURPOSE_ADD:137:X509_PURPOSE_add
X509V3_F_X509_PURPOSE_SET:141:X509_PURPOSE_set
X509_F_ADD_CERT_DIR:100:add_cert_dir
X509_F_BUILD_CHAIN:106:build_chain
X509_F_BY_FILE_CTRL:101:by_file_ctrl
X509_F_CHECK_NAME_CONSTRAINTS:149:check_name_constraints
X509_F_CHECK_POLICY:145:check_policy
X509_F_DANE_I2D:107:dane_i2d
X509_F_DIR_CTRL:102:dir_ctrl
X509_F_GET_CERT_BY_SUBJECT:103:get_cert_by_subject
X509_F_I2D_X509_AUX:151:i2d_X509_AUX
X509_F_LOOKUP_CERTS_SK:152:lookup_certs_sk
X509_F_NETSCAPE_SPKI_B64_DECODE:129:NETSCAPE_SPKI_b64_decode
X509_F_NETSCAPE_SPKI_B64_ENCODE:130:NETSCAPE_SPKI_b64_encode
X509_F_NEW_DIR:153:new_dir
X509_F_X509AT_ADD1_ATTR:135:X509at_add1_attr
X509_F_X509V3_ADD_EXT:104:X509v3_add_ext
X509_F_X509_ATTRIBUTE_CREATE_BY_NID:136:X509_ATTRIBUTE_create_by_NID
X509_F_X509_ATTRIBUTE_CREATE_BY_OBJ:137:X509_ATTRIBUTE_create_by_OBJ
X509_F_X509_ATTRIBUTE_CREATE_BY_TXT:140:X509_ATTRIBUTE_create_by_txt
X509_F_X509_ATTRIBUTE_GET0_DATA:139:X509_ATTRIBUTE_get0_data
X509_F_X509_ATTRIBUTE_SET1_DATA:138:X509_ATTRIBUTE_set1_data
X509_F_X509_CHECK_PRIVATE_KEY:128:X509_check_private_key
X509_F_X509_CRL_DIFF:105:X509_CRL_diff
X509_F_X509_CRL_METHOD_NEW:154:X509_CRL_METHOD_new
X509_F_X509_CRL_PRINT_FP:147:X509_CRL_print_fp
X509_F_X509_EXTENSION_CREATE_BY_NID:108:X509_EXTENSION_create_by_NID
X509_F_X509_EXTENSION_CREATE_BY_OBJ:109:X509_EXTENSION_create_by_OBJ
X509_F_X509_GET_PUBKEY_PARAMETERS:110:X509_get_pubkey_parameters
X509_F_X509_LOAD_CERT_CRL_FILE:132:X509_load_cert_crl_file
X509_F_X509_LOAD_CERT_FILE:111:X509_load_cert_file
X509_F_X509_LOAD_CRL_FILE:112:X509_load_crl_file
X509_F_X509_LOOKUP_METH_NEW:160:X509_LOOKUP_meth_new
X509_F_X509_LOOKUP_NEW:155:X509_LOOKUP_new
X509_F_X509_NAME_ADD_ENTRY:113:X509_NAME_add_entry
X509_F_X509_NAME_CANON:156:x509_name_canon
X509_F_X509_NAME_ENTRY_CREATE_BY_NID:114:X509_NAME_ENTRY_create_by_NID
X509_F_X509_NAME_ENTRY_CREATE_BY_TXT:131:X509_NAME_ENTRY_create_by_txt
X509_F_X509_NAME_ENTRY_SET_OBJECT:115:X509_NAME_ENTRY_set_object
X509_F_X509_NAME_ONELINE:116:X509_NAME_oneline
X509_F_X509_NAME_PRINT:117:X509_NAME_print
X509_F_X509_OBJECT_NEW:150:X509_OBJECT_new
X509_F_X509_PRINT_EX_FP:118:X509_print_ex_fp
X509_F_X509_PUBKEY_DECODE:148:x509_pubkey_decode
X509_F_X509_PUBKEY_GET:161:X509_PUBKEY_get
X509_F_X509_PUBKEY_GET0:119:X509_PUBKEY_get0
X509_F_X509_PUBKEY_SET:120:X509_PUBKEY_set
X509_F_X509_REQ_CHECK_PRIVATE_KEY:144:X509_REQ_check_private_key
X509_F_X509_REQ_PRINT_EX:121:X509_REQ_print_ex
X509_F_X509_REQ_PRINT_FP:122:X509_REQ_print_fp
X509_F_X509_REQ_TO_X509:123:X509_REQ_to_X509
X509_F_X509_STORE_ADD_CERT:124:X509_STORE_add_cert
X509_F_X509_STORE_ADD_CRL:125:X509_STORE_add_crl
X509_F_X509_STORE_ADD_LOOKUP:157:X509_STORE_add_lookup
X509_F_X509_STORE_CTX_GET1_ISSUER:146:X509_STORE_CTX_get1_issuer
X509_F_X509_STORE_CTX_INIT:143:X509_STORE_CTX_init
X509_F_X509_STORE_CTX_NEW:142:X509_STORE_CTX_new
X509_F_X509_STORE_CTX_PURPOSE_INHERIT:134:X509_STORE_CTX_purpose_inherit
X509_F_X509_STORE_NEW:158:X509_STORE_new
X509_F_X509_TO_X509_REQ:126:X509_to_X509_REQ
X509_F_X509_TRUST_ADD:133:X509_TRUST_add
X509_F_X509_TRUST_SET:141:X509_TRUST_set
X509_F_X509_VERIFY_CERT:127:X509_verify_cert
X509_F_X509_VERIFY_PARAM_NEW:159:X509_VERIFY_PARAM_new

#Reason codes
ASN1_R_ADDING_OBJECT:171:adding object
ASN1_R_ASN1_PARSE_ERROR:203:asn1 parse error
ASN1_R_ASN1_SIG_PARSE_ERROR:204:asn1 sig parse error
ASN1_R_AUX_ERROR:100:aux error
ASN1_R_BAD_OBJECT_HEADER:102:bad object header
ASN1_R_BAD_TEMPLATE:230:bad template
ASN1_R_BMPSTRING_IS_WRONG_LENGTH:214:bmpstring is wrong length
ASN1_R_BN_LIB:105:bn lib
ASN1_R_BOOLEAN_IS_WRONG_LENGTH:106:boolean is wrong length
ASN1_R_BUFFER_TOO_SMALL:107:buffer too small
ASN1_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER:108:cipher has no object identifier
ASN1_R_CONTEXT_NOT_INITIALISED:217:context not initialised
ASN1_R_DATA_IS_WRONG:109:data is wrong
ASN1_R_DECODE_ERROR:110:decode error
ASN1_R_DEPTH_EXCEEDED:174:depth exceeded
ASN1_R_DIGEST_AND_KEY_TYPE_NOT_SUPPORTED:198:digest and key type not supported
ASN1_R_ENCODE_ERROR:112:encode error
ASN1_R_ERROR_GETTING_TIME:173:error getting time
ASN1_R_ERROR_LOADING_SECTION:172:error loading section
ASN1_R_ERROR_SETTING_CIPHER_PARAMS:114:error setting cipher params
ASN1_R_EXPECTING_AN_INTEGER:115:expecting an integer
ASN1_R_EXPECTING_AN_OBJECT:116:expecting an object
ASN1_R_EXPLICIT_LENGTH_MISMATCH:119:explicit length mismatch
ASN1_R_EXPLICIT_TAG_NOT_CONSTRUCTED:120:explicit tag not constructed
ASN1_R_FIELD_MISSING:121:field missing
ASN1_R_FIRST_NUM_TOO_LARGE:122:first num too large
ASN1_R_HEADER_TOO_LONG:123:header too long
ASN1_R_ILLEGAL_BITSTRING_FORMAT:175:illegal bitstring format
ASN1_R_ILLEGAL_BOOLEAN:176:illegal boolean
ASN1_R_ILLEGAL_CHARACTERS:124:illegal characters
ASN1_R_ILLEGAL_FORMAT:177:illegal format
ASN1_R_ILLEGAL_HEX:178:illegal hex
ASN1_R_ILLEGAL_IMPLICIT_TAG:179:illegal implicit tag
ASN1_R_ILLEGAL_INTEGER:180:illegal integer
ASN1_R_ILLEGAL_NEGATIVE_VALUE:226:illegal negative value
ASN1_R_ILLEGAL_NESTED_TAGGING:181:illegal nested tagging
ASN1_R_ILLEGAL_NULL:125:illegal null
ASN1_R_ILLEGAL_NULL_VALUE:182:illegal null value
ASN1_R_ILLEGAL_OBJECT:183:illegal object
ASN1_R_ILLEGAL_OPTIONAL_ANY:126:illegal optional any
ASN1_R_ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE:170:illegal options on item template
ASN1_R_ILLEGAL_PADDING:221:illegal padding
ASN1_R_ILLEGAL_TAGGED_ANY:127:illegal tagged any
ASN1_R_ILLEGAL_TIME_VALUE:184:illegal time value
ASN1_R_ILLEGAL_ZERO_CONTENT:222:illegal zero content
ASN1_R_INTEGER_NOT_ASCII_FORMAT:185:integer not ascii format
ASN1_R_INTEGER_TOO_LARGE_FOR_LONG:128:integer too large for long
ASN1_R_INVALID_BIT_STRING_BITS_LEFT:220:invalid bit string bits left
ASN1_R_INVALID_BMPSTRING_LENGTH:129:invalid bmpstring length
ASN1_R_INVALID_DIGIT:130:invalid digit
ASN1_R_INVALID_MIME_TYPE:205:invalid mime type
ASN1_R_INVALID_MODIFIER:186:invalid modifier
ASN1_R_INVALID_NUMBER:187:invalid number
ASN1_R_INVALID_OBJECT_ENCODING:216:invalid object encoding
ASN1_R_INVALID_SCRYPT_PARAMETERS:227:invalid scrypt parameters
ASN1_R_INVALID_SEPARATOR:131:invalid separator
ASN1_R_INVALID_STRING_TABLE_VALUE:218:invalid string table value
ASN1_R_INVALID_UNIVERSALSTRING_LENGTH:133:invalid universalstring length
ASN1_R_INVALID_UTF8STRING:134:invalid utf8string
ASN1_R_INVALID_VALUE:219:invalid value
ASN1_R_LIST_ERROR:188:list error
ASN1_R_MIME_NO_CONTENT_TYPE:206:mime no content type
ASN1_R_MIME_PARSE_ERROR:207:mime parse error
ASN1_R_MIME_SIG_PARSE_ERROR:208:mime sig parse error
ASN1_R_MISSING_EOC:137:missing eoc
ASN1_R_MISSING_SECOND_NUMBER:138:missing second number
ASN1_R_MISSING_VALUE:189:missing value
ASN1_R_MSTRING_NOT_UNIVERSAL:139:mstring not universal
ASN1_R_MSTRING_WRONG_TAG:140:mstring wrong tag
ASN1_R_NESTED_ASN1_STRING:197:nested asn1 string
ASN1_R_NESTED_TOO_DEEP:201:nested too deep
ASN1_R_NON_HEX_CHARACTERS:141:non hex characters
ASN1_R_NOT_ASCII_FORMAT:190:not ascii format
ASN1_R_NOT_ENOUGH_DATA:142:not enough data
ASN1_R_NO_CONTENT_TYPE:209:no content type
ASN1_R_NO_MATCHING_CHOICE_TYPE:143:no matching choice type
ASN1_R_NO_MULTIPART_BODY_FAILURE:210:no multipart body failure
ASN1_R_NO_MULTIPART_BOUNDARY:211:no multipart boundary
ASN1_R_NO_SIG_CONTENT_TYPE:212:no sig content type
ASN1_R_NULL_IS_WRONG_LENGTH:144:null is wrong length
ASN1_R_OBJECT_NOT_ASCII_FORMAT:191:object not ascii format
ASN1_R_ODD_NUMBER_OF_CHARS:145:odd number of chars
ASN1_R_SECOND_NUMBER_TOO_LARGE:147:second number too large
ASN1_R_SEQUENCE_LENGTH_MISMATCH:148:sequence length mismatch
ASN1_R_SEQUENCE_NOT_CONSTRUCTED:149:sequence not constructed
ASN1_R_SEQUENCE_OR_SET_NEEDS_CONFIG:192:sequence or set needs config
ASN1_R_SHORT_LINE:150:short line
ASN1_R_SIG_INVALID_MIME_TYPE:213:sig invalid mime type
ASN1_R_STREAMING_NOT_SUPPORTED:202:streaming not supported
ASN1_R_STRING_TOO_LONG:151:string too long
ASN1_R_STRING_TOO_SHORT:152:string too short
ASN1_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD:154:\
	the asn1 object identifier is not known for this md
ASN1_R_TIME_NOT_ASCII_FORMAT:193:time not ascii format
ASN1_R_TOO_LARGE:223:too large
ASN1_R_TOO_LONG:155:too long
ASN1_R_TOO_SMALL:224:too small
ASN1_R_TYPE_NOT_CONSTRUCTED:156:type not constructed
ASN1_R_TYPE_NOT_PRIMITIVE:195:type not primitive
ASN1_R_UNEXPECTED_EOC:159:unexpected eoc
ASN1_R_UNIVERSALSTRING_IS_WRONG_LENGTH:215:universalstring is wrong length
ASN1_R_UNKNOWN_FORMAT:160:unknown format
ASN1_R_UNKNOWN_MESSAGE_DIGEST_ALGORITHM:161:unknown message digest algorithm
ASN1_R_UNKNOWN_OBJECT_TYPE:162:unknown object type
ASN1_R_UNKNOWN_PUBLIC_KEY_TYPE:163:unknown public key type
ASN1_R_UNKNOWN_SIGNATURE_ALGORITHM:199:unknown signature algorithm
ASN1_R_UNKNOWN_TAG:194:unknown tag
ASN1_R_UNSUPPORTED_ANY_DEFINED_BY_TYPE:164:unsupported any defined by type
ASN1_R_UNSUPPORTED_CIPHER:228:unsupported cipher
ASN1_R_UNSUPPORTED_PUBLIC_KEY_TYPE:167:unsupported public key type
ASN1_R_UNSUPPORTED_TYPE:196:unsupported type
ASN1_R_WRONG_INTEGER_TYPE:225:wrong integer type
ASN1_R_WRONG_PUBLIC_KEY_TYPE:200:wrong public key type
ASN1_R_WRONG_TAG:168:wrong tag
ASYNC_R_FAILED_TO_SET_POOL:101:failed to set pool
ASYNC_R_FAILED_TO_SWAP_CONTEXT:102:failed to swap context
ASYNC_R_INIT_FAILED:105:init failed
ASYNC_R_INVALID_POOL_SIZE:103:invalid pool size
BIO_R_ACCEPT_ERROR:100:accept error
BIO_R_ADDRINFO_ADDR_IS_NOT_AF_INET:141:addrinfo addr is not af inet
BIO_R_AMBIGUOUS_HOST_OR_SERVICE:129:ambiguous host or service
BIO_R_BAD_FOPEN_MODE:101:bad fopen mode
BIO_R_BROKEN_PIPE:124:broken pipe
BIO_R_CONNECT_ERROR:103:connect error
BIO_R_GETHOSTBYNAME_ADDR_IS_NOT_AF_INET:107:gethostbyname addr is not af inet
BIO_R_GETSOCKNAME_ERROR:132:getsockname error
BIO_R_GETSOCKNAME_TRUNCATED_ADDRESS:133:getsockname truncated address
BIO_R_GETTING_SOCKTYPE:134:getting socktype
BIO_R_INVALID_ARGUMENT:125:invalid argument
BIO_R_INVALID_SOCKET:135:invalid socket
BIO_R_IN_USE:123:in use
BIO_R_LENGTH_TOO_LONG:102:length too long
BIO_R_LISTEN_V6_ONLY:136:listen v6 only
BIO_R_LOOKUP_RETURNED_NOTHING:142:lookup returned nothing
BIO_R_MALFORMED_HOST_OR_SERVICE:130:malformed host or service
BIO_R_NBIO_CONNECT_ERROR:110:nbio connect error
BIO_R_NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED:143:\
	no accept addr or service specified
BIO_R_NO_HOSTNAME_OR_SERVICE_SPECIFIED:144:no hostname or service specified
BIO_R_NO_PORT_DEFINED:113:no port defined
BIO_R_NO_SUCH_FILE:128:no such file
BIO_R_NULL_PARAMETER:115:null parameter
BIO_R_UNABLE_TO_BIND_SOCKET:117:unable to bind socket
BIO_R_UNABLE_TO_CREATE_SOCKET:118:unable to create socket
BIO_R_UNABLE_TO_KEEPALIVE:137:unable to keepalive
BIO_R_UNABLE_TO_LISTEN_SOCKET:119:unable to listen socket
BIO_R_UNABLE_TO_NODELAY:138:unable to nodelay
BIO_R_UNABLE_TO_REUSEADDR:139:unable to reuseaddr
BIO_R_UNAVAILABLE_IP_FAMILY:145:unavailable ip family
BIO_R_UNINITIALIZED:120:uninitialized
BIO_R_UNKNOWN_INFO_TYPE:140:unknown info type
BIO_R_UNSUPPORTED_IP_FAMILY:146:unsupported ip family
BIO_R_UNSUPPORTED_METHOD:121:unsupported method
BIO_R_UNSUPPORTED_PROTOCOL_FAMILY:131:unsupported protocol family
BIO_R_WRITE_TO_READ_ONLY_BIO:126:write to read only BIO
BIO_R_WSASTARTUP:122:WSAStartup
BN_R_ARG2_LT_ARG3:100:arg2 lt arg3
BN_R_BAD_RECIPROCAL:101:bad reciprocal
BN_R_BIGNUM_TOO_LONG:114:bignum too long
BN_R_BITS_TOO_SMALL:118:bits too small
BN_R_CALLED_WITH_EVEN_MODULUS:102:called with even modulus
BN_R_DIV_BY_ZERO:103:div by zero
BN_R_ENCODING_ERROR:104:encoding error
BN_R_EXPAND_ON_STATIC_BIGNUM_DATA:105:expand on static bignum data
BN_R_INPUT_NOT_REDUCED:110:input not reduced
BN_R_INVALID_LENGTH:106:invalid length
BN_R_INVALID_RANGE:115:invalid range
BN_R_INVALID_SHIFT:119:invalid shift
BN_R_NOT_A_SQUARE:111:not a square
BN_R_NOT_INITIALIZED:107:not initialized
BN_R_NO_INVERSE:108:no inverse
BN_R_NO_SOLUTION:116:no solution
BN_R_PRIVATE_KEY_TOO_LARGE:117:private key too large
BN_R_P_IS_NOT_PRIME:112:p is not prime
BN_R_TOO_MANY_ITERATIONS:113:too many iterations
BN_R_TOO_MANY_TEMPORARY_VARIABLES:109:too many temporary variables
CMS_R_ADD_SIGNER_ERROR:99:add signer error
CMS_R_ATTRIBUTE_ERROR:161:attribute error
CMS_R_CERTIFICATE_ALREADY_PRESENT:175:certificate already present
CMS_R_CERTIFICATE_HAS_NO_KEYID:160:certificate has no keyid
CMS_R_CERTIFICATE_VERIFY_ERROR:100:certificate verify error
CMS_R_CIPHER_INITIALISATION_ERROR:101:cipher initialisation error
CMS_R_CIPHER_PARAMETER_INITIALISATION_ERROR:102:\
	cipher parameter initialisation error
CMS_R_CMS_DATAFINAL_ERROR:103:cms datafinal error
CMS_R_CMS_LIB:104:cms lib
CMS_R_CONTENTIDENTIFIER_MISMATCH:170:contentidentifier mismatch
CMS_R_CONTENT_NOT_FOUND:105:content not found
CMS_R_CONTENT_TYPE_MISMATCH:171:content type mismatch
CMS_R_CONTENT_TYPE_NOT_COMPRESSED_DATA:106:content type not compressed data
CMS_R_CONTENT_TYPE_NOT_ENVELOPED_DATA:107:content type not enveloped data
CMS_R_CONTENT_TYPE_NOT_SIGNED_DATA:108:content type not signed data
CMS_R_CONTENT_VERIFY_ERROR:109:content verify error
CMS_R_CTRL_ERROR:110:ctrl error
CMS_R_CTRL_FAILURE:111:ctrl failure
CMS_R_DECRYPT_ERROR:112:decrypt error
CMS_R_ERROR_GETTING_PUBLIC_KEY:113:error getting public key
CMS_R_ERROR_READING_MESSAGEDIGEST_ATTRIBUTE:114:\
	error reading messagedigest attribute
CMS_R_ERROR_SETTING_KEY:115:error setting key
CMS_R_ERROR_SETTING_RECIPIENTINFO:116:error setting recipientinfo
CMS_R_INVALID_ENCRYPTED_KEY_LENGTH:117:invalid encrypted key length
CMS_R_INVALID_KEY_ENCRYPTION_PARAMETER:176:invalid key encryption parameter
CMS_R_INVALID_KEY_LENGTH:118:invalid key length
CMS_R_MD_BIO_INIT_ERROR:119:md bio init error
CMS_R_MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH:120:\
	messagedigest attribute wrong length
CMS_R_MESSAGEDIGEST_WRONG_LENGTH:121:messagedigest wrong length
CMS_R_MSGSIGDIGEST_ERROR:172:msgsigdigest error
CMS_R_MSGSIGDIGEST_VERIFICATION_FAILURE:162:msgsigdigest verification failure
CMS_R_MSGSIGDIGEST_WRONG_LENGTH:163:msgsigdigest wrong length
CMS_R_NEED_ONE_SIGNER:164:need one signer
CMS_R_NOT_A_SIGNED_RECEIPT:165:not a signed receipt
CMS_R_NOT_ENCRYPTED_DATA:122:not encrypted data
CMS_R_NOT_KEK:123:not kek
CMS_R_NOT_KEY_AGREEMENT:181:not key agreement
CMS_R_NOT_KEY_TRANSPORT:124:not key transport
CMS_R_NOT_PWRI:177:not pwri
CMS_R_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:125:not supported for this key type
CMS_R_NO_CIPHER:126:no cipher
CMS_R_NO_CONTENT:127:no content
CMS_R_NO_CONTENT_TYPE:173:no content type
CMS_R_NO_DEFAULT_DIGEST:128:no default digest
CMS_R_NO_DIGEST_SET:129:no digest set
CMS_R_NO_KEY:130:no key
CMS_R_NO_KEY_OR_CERT:174:no key or cert
CMS_R_NO_MATCHING_DIGEST:131:no matching digest
CMS_R_NO_MATCHING_RECIPIENT:132:no matching recipient
CMS_R_NO_MATCHING_SIGNATURE:166:no matching signature
CMS_R_NO_MSGSIGDIGEST:167:no msgsigdigest
CMS_R_NO_PASSWORD:178:no password
CMS_R_NO_PRIVATE_KEY:133:no private key
CMS_R_NO_PUBLIC_KEY:134:no public key
CMS_R_NO_RECEIPT_REQUEST:168:no receipt request
CMS_R_NO_SIGNERS:135:no signers
CMS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:136:\
	private key does not match certificate
CMS_R_RECEIPT_DECODE_ERROR:169:receipt decode error
CMS_R_RECIPIENT_ERROR:137:recipient error
CMS_R_SIGNER_CERTIFICATE_NOT_FOUND:138:signer certificate not found
CMS_R_SIGNFINAL_ERROR:139:signfinal error
CMS_R_SMIME_TEXT_ERROR:140:smime text error
CMS_R_STORE_INIT_ERROR:141:store init error
CMS_R_TYPE_NOT_COMPRESSED_DATA:142:type not compressed data
CMS_R_TYPE_NOT_DATA:143:type not data
CMS_R_TYPE_NOT_DIGESTED_DATA:144:type not digested data
CMS_R_TYPE_NOT_ENCRYPTED_DATA:145:type not encrypted data
CMS_R_TYPE_NOT_ENVELOPED_DATA:146:type not enveloped data
CMS_R_UNABLE_TO_FINALIZE_CONTEXT:147:unable to finalize context
CMS_R_UNKNOWN_CIPHER:148:unknown cipher
CMS_R_UNKNOWN_DIGEST_ALGORITHM:149:unknown digest algorithm
CMS_R_UNKNOWN_ID:150:unknown id
CMS_R_UNSUPPORTED_COMPRESSION_ALGORITHM:151:unsupported compression algorithm
CMS_R_UNSUPPORTED_CONTENT_ENCRYPTION_ALGORITHM:194:\
	unsupported content encryption algorithm
CMS_R_UNSUPPORTED_CONTENT_TYPE:152:unsupported content type
CMS_R_UNSUPPORTED_KEK_ALGORITHM:153:unsupported kek algorithm
CMS_R_UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM:179:\
	unsupported key encryption algorithm
CMS_R_UNSUPPORTED_RECIPIENTINFO_TYPE:155:unsupported recipientinfo type
CMS_R_UNSUPPORTED_RECIPIENT_TYPE:154:unsupported recipient type
CMS_R_UNSUPPORTED_TYPE:156:unsupported type
CMS_R_UNWRAP_ERROR:157:unwrap error
CMS_R_UNWRAP_FAILURE:180:unwrap failure
CMS_R_VERIFICATION_FAILURE:158:verification failure
CMS_R_WRAP_ERROR:159:wrap error
COMP_R_ZLIB_DEFLATE_ERROR:99:zlib deflate error
COMP_R_ZLIB_INFLATE_ERROR:100:zlib inflate error
COMP_R_ZLIB_NOT_SUPPORTED:101:zlib not supported
CONF_R_ERROR_LOADING_DSO:110:error loading dso
CONF_R_LIST_CANNOT_BE_NULL:115:list cannot be null
CONF_R_MISSING_CLOSE_SQUARE_BRACKET:100:missing close square bracket
CONF_R_MISSING_EQUAL_SIGN:101:missing equal sign
CONF_R_MISSING_INIT_FUNCTION:112:missing init function
CONF_R_MODULE_INITIALIZATION_ERROR:109:module initialization error
CONF_R_NO_CLOSE_BRACE:102:no close brace
CONF_R_NO_CONF:105:no conf
CONF_R_NO_CONF_OR_ENVIRONMENT_VARIABLE:106:no conf or environment variable
CONF_R_NO_SECTION:107:no section
CONF_R_NO_SUCH_FILE:114:no such file
CONF_R_NO_VALUE:108:no value
CONF_R_NUMBER_TOO_LARGE:121:number too large
CONF_R_RECURSIVE_DIRECTORY_INCLUDE:111:recursive directory include
CONF_R_SSL_COMMAND_SECTION_EMPTY:117:ssl command section empty
CONF_R_SSL_COMMAND_SECTION_NOT_FOUND:118:ssl command section not found
CONF_R_SSL_SECTION_EMPTY:119:ssl section empty
CONF_R_SSL_SECTION_NOT_FOUND:120:ssl section not found
CONF_R_UNABLE_TO_CREATE_NEW_SECTION:103:unable to create new section
CONF_R_UNKNOWN_MODULE_NAME:113:unknown module name
CONF_R_VARIABLE_EXPANSION_TOO_LONG:116:variable expansion too long
CONF_R_VARIABLE_HAS_NO_VALUE:104:variable has no value
CRYPTO_R_FIPS_MODE_NOT_SUPPORTED:101:fips mode not supported
CRYPTO_R_ILLEGAL_HEX_DIGIT:102:illegal hex digit
CRYPTO_R_ODD_NUMBER_OF_DIGITS:103:odd number of digits
CT_R_BASE64_DECODE_ERROR:108:base64 decode error
CT_R_INVALID_LOG_ID_LENGTH:100:invalid log id length
CT_R_LOG_CONF_INVALID:109:log conf invalid
CT_R_LOG_CONF_INVALID_KEY:110:log conf invalid key
CT_R_LOG_CONF_MISSING_DESCRIPTION:111:log conf missing description
CT_R_LOG_CONF_MISSING_KEY:112:log conf missing key
CT_R_LOG_KEY_INVALID:113:log key invalid
CT_R_SCT_FUTURE_TIMESTAMP:116:sct future timestamp
CT_R_SCT_INVALID:104:sct invalid
CT_R_SCT_INVALID_SIGNATURE:107:sct invalid signature
CT_R_SCT_LIST_INVALID:105:sct list invalid
CT_R_SCT_LOG_ID_MISMATCH:114:sct log id mismatch
CT_R_SCT_NOT_SET:106:sct not set
CT_R_SCT_UNSUPPORTED_VERSION:115:sct unsupported version
CT_R_UNRECOGNIZED_SIGNATURE_NID:101:unrecognized signature nid
CT_R_UNSUPPORTED_ENTRY_TYPE:102:unsupported entry type
CT_R_UNSUPPORTED_VERSION:103:unsupported version
DH_R_BAD_GENERATOR:101:bad generator
DH_R_BN_DECODE_ERROR:109:bn decode error
DH_R_BN_ERROR:106:bn error
DH_R_CHECK_INVALID_J_VALUE:115:check invalid j value
DH_R_CHECK_INVALID_Q_VALUE:116:check invalid q value
DH_R_CHECK_PUBKEY_INVALID:122:check pubkey invalid
DH_R_CHECK_PUBKEY_TOO_LARGE:123:check pubkey too large
DH_R_CHECK_PUBKEY_TOO_SMALL:124:check pubkey too small
DH_R_CHECK_P_NOT_PRIME:117:check p not prime
DH_R_CHECK_P_NOT_SAFE_PRIME:118:check p not safe prime
DH_R_CHECK_Q_NOT_PRIME:119:check q not prime
DH_R_DECODE_ERROR:104:decode error
DH_R_INVALID_PARAMETER_NAME:110:invalid parameter name
DH_R_INVALID_PARAMETER_NID:114:invalid parameter nid
DH_R_INVALID_PUBKEY:102:invalid public key
DH_R_KDF_PARAMETER_ERROR:112:kdf parameter error
DH_R_KEYS_NOT_SET:108:keys not set
DH_R_MISSING_PUBKEY:125:missing pubkey
DH_R_MODULUS_TOO_LARGE:103:modulus too large
DH_R_NOT_SUITABLE_GENERATOR:120:not suitable generator
DH_R_NO_PARAMETERS_SET:107:no parameters set
DH_R_NO_PRIVATE_VALUE:100:no private value
DH_R_PARAMETER_ENCODING_ERROR:105:parameter encoding error
DH_R_PEER_KEY_ERROR:111:peer key error
DH_R_SHARED_INFO_ERROR:113:shared info error
DH_R_UNABLE_TO_CHECK_GENERATOR:121:unable to check generator
DSA_R_BAD_Q_VALUE:102:bad q value
DSA_R_BN_DECODE_ERROR:108:bn decode error
DSA_R_BN_ERROR:109:bn error
DSA_R_DECODE_ERROR:104:decode error
DSA_R_INVALID_DIGEST_TYPE:106:invalid digest type
DSA_R_INVALID_PARAMETERS:112:invalid parameters
DSA_R_MISSING_PARAMETERS:101:missing parameters
DSA_R_MISSING_PRIVATE_KEY:111:missing private key
DSA_R_MODULUS_TOO_LARGE:103:modulus too large
DSA_R_NO_PARAMETERS_SET:107:no parameters set
DSA_R_PARAMETER_ENCODING_ERROR:105:parameter encoding error
DSA_R_Q_NOT_PRIME:113:q not prime
DSA_R_SEED_LEN_SMALL:110:seed_len is less than the length of q
DSO_R_CTRL_FAILED:100:control command failed
DSO_R_DSO_ALREADY_LOADED:110:dso already loaded
DSO_R_EMPTY_FILE_STRUCTURE:113:empty file structure
DSO_R_FAILURE:114:failure
DSO_R_FILENAME_TOO_BIG:101:filename too big
DSO_R_FINISH_FAILED:102:cleanup method function failed
DSO_R_INCORRECT_FILE_SYNTAX:115:incorrect file syntax
DSO_R_LOAD_FAILED:103:could not load the shared library
DSO_R_NAME_TRANSLATION_FAILED:109:name translation failed
DSO_R_NO_FILENAME:111:no filename
DSO_R_NULL_HANDLE:104:a null shared library handle was used
DSO_R_SET_FILENAME_FAILED:112:set filename failed
DSO_R_STACK_ERROR:105:the meth_data stack is corrupt
DSO_R_SYM_FAILURE:106:could not bind to the requested symbol name
DSO_R_UNLOAD_FAILED:107:could not unload the shared library
DSO_R_UNSUPPORTED:108:functionality not supported
EC_R_ASN1_ERROR:115:asn1 error
EC_R_BAD_SIGNATURE:156:bad signature
EC_R_BIGNUM_OUT_OF_RANGE:144:bignum out of range
EC_R_BUFFER_TOO_SMALL:100:buffer too small
EC_R_CANNOT_INVERT:165:cannot invert
EC_R_COORDINATES_OUT_OF_RANGE:146:coordinates out of range
EC_R_CURVE_DOES_NOT_SUPPORT_ECDH:160:curve does not support ecdh
EC_R_CURVE_DOES_NOT_SUPPORT_SIGNING:159:curve does not support signing
EC_R_D2I_ECPKPARAMETERS_FAILURE:117:d2i ecpkparameters failure
EC_R_DECODE_ERROR:142:decode error
EC_R_DISCRIMINANT_IS_ZERO:118:discriminant is zero
EC_R_EC_GROUP_NEW_BY_NAME_FAILURE:119:ec group new by name failure
EC_R_FIELD_TOO_LARGE:143:field too large
EC_R_GF2M_NOT_SUPPORTED:147:gf2m not supported
EC_R_GROUP2PKPARAMETERS_FAILURE:120:group2pkparameters failure
EC_R_I2D_ECPKPARAMETERS_FAILURE:121:i2d ecpkparameters failure
EC_R_INCOMPATIBLE_OBJECTS:101:incompatible objects
EC_R_INVALID_ARGUMENT:112:invalid argument
EC_R_INVALID_COMPRESSED_POINT:110:invalid compressed point
EC_R_INVALID_COMPRESSION_BIT:109:invalid compression bit
EC_R_INVALID_CURVE:141:invalid curve
EC_R_INVALID_DIGEST:151:invalid digest
EC_R_INVALID_DIGEST_TYPE:138:invalid digest type
EC_R_INVALID_ENCODING:102:invalid encoding
EC_R_INVALID_FIELD:103:invalid field
EC_R_INVALID_FORM:104:invalid form
EC_R_INVALID_GROUP_ORDER:122:invalid group order
EC_R_INVALID_KEY:116:invalid key
EC_R_INVALID_OUTPUT_LENGTH:161:invalid output length
EC_R_INVALID_PEER_KEY:133:invalid peer key
EC_R_INVALID_PENTANOMIAL_BASIS:132:invalid pentanomial basis
EC_R_INVALID_PRIVATE_KEY:123:invalid private key
EC_R_INVALID_TRINOMIAL_BASIS:137:invalid trinomial basis
EC_R_KDF_PARAMETER_ERROR:148:kdf parameter error
EC_R_KEYS_NOT_SET:140:keys not set
EC_R_LADDER_POST_FAILURE:136:ladder post failure
EC_R_LADDER_PRE_FAILURE:153:ladder pre failure
EC_R_LADDER_STEP_FAILURE:162:ladder step failure
EC_R_MISSING_OID:167:missing OID
EC_R_MISSING_PARAMETERS:124:missing parameters
EC_R_MISSING_PRIVATE_KEY:125:missing private key
EC_R_NEED_NEW_SETUP_VALUES:157:need new setup values
EC_R_NOT_A_NIST_PRIME:135:not a NIST prime
EC_R_NOT_IMPLEMENTED:126:not implemented
EC_R_NOT_INITIALIZED:111:not initialized
EC_R_NO_PARAMETERS_SET:139:no parameters set
EC_R_NO_PRIVATE_VALUE:154:no private value
EC_R_OPERATION_NOT_SUPPORTED:152:operation not supported
EC_R_PASSED_NULL_PARAMETER:134:passed null parameter
EC_R_PEER_KEY_ERROR:149:peer key error
EC_R_PKPARAMETERS2GROUP_FAILURE:127:pkparameters2group failure
EC_R_POINT_ARITHMETIC_FAILURE:155:point arithmetic failure
EC_R_POINT_AT_INFINITY:106:point at infinity
EC_R_POINT_COORDINATES_BLIND_FAILURE:163:point coordinates blind failure
EC_R_POINT_IS_NOT_ON_CURVE:107:point is not on curve
EC_R_RANDOM_NUMBER_GENERATION_FAILED:158:random number generation failed
EC_R_SHARED_INFO_ERROR:150:shared info error
EC_R_SLOT_FULL:108:slot full
EC_R_UNDEFINED_GENERATOR:113:undefined generator
EC_R_UNDEFINED_ORDER:128:undefined order
EC_R_UNKNOWN_COFACTOR:164:unknown cofactor
EC_R_UNKNOWN_GROUP:129:unknown group
EC_R_UNKNOWN_ORDER:114:unknown order
EC_R_UNSUPPORTED_FIELD:131:unsupported field
EC_R_WRONG_CURVE_PARAMETERS:145:wrong curve parameters
EC_R_WRONG_ORDER:130:wrong order
ENGINE_R_ALREADY_LOADED:100:already loaded
ENGINE_R_ARGUMENT_IS_NOT_A_NUMBER:133:argument is not a number
ENGINE_R_CMD_NOT_EXECUTABLE:134:cmd not executable
ENGINE_R_COMMAND_TAKES_INPUT:135:command takes input
ENGINE_R_COMMAND_TAKES_NO_INPUT:136:command takes no input
ENGINE_R_CONFLICTING_ENGINE_ID:103:conflicting engine id
ENGINE_R_CTRL_COMMAND_NOT_IMPLEMENTED:119:ctrl command not implemented
ENGINE_R_DSO_FAILURE:104:DSO failure
ENGINE_R_DSO_NOT_FOUND:132:dso not found
ENGINE_R_ENGINES_SECTION_ERROR:148:engines section error
ENGINE_R_ENGINE_CONFIGURATION_ERROR:102:engine configuration error
ENGINE_R_ENGINE_IS_NOT_IN_LIST:105:engine is not in the list
ENGINE_R_ENGINE_SECTION_ERROR:149:engine section error
ENGINE_R_FAILED_LOADING_PRIVATE_KEY:128:failed loading private key
ENGINE_R_FAILED_LOADING_PUBLIC_KEY:129:failed loading public key
ENGINE_R_FINISH_FAILED:106:finish failed
ENGINE_R_ID_OR_NAME_MISSING:108:'id' or 'name' missing
ENGINE_R_INIT_FAILED:109:init failed
ENGINE_R_INTERNAL_LIST_ERROR:110:internal list error
ENGINE_R_INVALID_ARGUMENT:143:invalid argument
ENGINE_R_INVALID_CMD_NAME:137:invalid cmd name
ENGINE_R_INVALID_CMD_NUMBER:138:invalid cmd number
ENGINE_R_INVALID_INIT_VALUE:151:invalid init value
ENGINE_R_INVALID_STRING:150:invalid string
ENGINE_R_NOT_INITIALISED:117:not initialised
ENGINE_R_NOT_LOADED:112:not loaded
ENGINE_R_NO_CONTROL_FUNCTION:120:no control function
ENGINE_R_NO_INDEX:144:no index
ENGINE_R_NO_LOAD_FUNCTION:125:no load function
ENGINE_R_NO_REFERENCE:130:no reference
ENGINE_R_NO_SUCH_ENGINE:116:no such engine
ENGINE_R_UNIMPLEMENTED_CIPHER:146:unimplemented cipher
ENGINE_R_UNIMPLEMENTED_DIGEST:147:unimplemented digest
ENGINE_R_UNIMPLEMENTED_PUBLIC_KEY_METHOD:101:unimplemented public key method
ENGINE_R_VERSION_INCOMPATIBILITY:145:version incompatibility
EVP_R_AES_KEY_SETUP_FAILED:143:aes key setup failed
EVP_R_ARIA_KEY_SETUP_FAILED:176:aria key setup failed
EVP_R_BAD_DECRYPT:100:bad decrypt
EVP_R_BAD_KEY_LENGTH:195:bad key length
EVP_R_BUFFER_TOO_SMALL:155:buffer too small
EVP_R_CAMELLIA_KEY_SETUP_FAILED:157:camellia key setup failed
EVP_R_CIPHER_PARAMETER_ERROR:122:cipher parameter error
EVP_R_COMMAND_NOT_SUPPORTED:147:command not supported
EVP_R_COPY_ERROR:173:copy error
EVP_R_CTRL_NOT_IMPLEMENTED:132:ctrl not implemented
EVP_R_CTRL_OPERATION_NOT_IMPLEMENTED:133:ctrl operation not implemented
EVP_R_DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH:138:data not multiple of block length
EVP_R_DECODE_ERROR:114:decode error
EVP_R_DIFFERENT_KEY_TYPES:101:different key types
EVP_R_DIFFERENT_PARAMETERS:153:different parameters
EVP_R_ERROR_LOADING_SECTION:165:error loading section
EVP_R_ERROR_SETTING_FIPS_MODE:166:error setting fips mode
EVP_R_EXPECTING_AN_HMAC_KEY:174:expecting an hmac key
EVP_R_EXPECTING_AN_RSA_KEY:127:expecting an rsa key
EVP_R_EXPECTING_A_DH_KEY:128:expecting a dh key
EVP_R_EXPECTING_A_DSA_KEY:129:expecting a dsa key
EVP_R_EXPECTING_A_EC_KEY:142:expecting a ec key
EVP_R_EXPECTING_A_POLY1305_KEY:164:expecting a poly1305 key
EVP_R_EXPECTING_A_SIPHASH_KEY:175:expecting a siphash key
EVP_R_FIPS_MODE_NOT_SUPPORTED:167:fips mode not supported
EVP_R_GET_RAW_KEY_FAILED:182:get raw key failed
EVP_R_ILLEGAL_SCRYPT_PARAMETERS:171:illegal scrypt parameters
EVP_R_INITIALIZATION_ERROR:134:initialization error
EVP_R_INPUT_NOT_INITIALIZED:111:input not initialized
EVP_R_INVALID_DIGEST:152:invalid digest
EVP_R_INVALID_FIPS_MODE:168:invalid fips mode
EVP_R_INVALID_IV_LENGTH:194:invalid iv length
EVP_R_INVALID_KEY:163:invalid key
EVP_R_INVALID_KEY_LENGTH:130:invalid key length
EVP_R_INVALID_OPERATION:148:invalid operation
EVP_R_KEYGEN_FAILURE:120:keygen failure
EVP_R_KEY_SETUP_FAILED:180:key setup failed
EVP_R_MEMORY_LIMIT_EXCEEDED:172:memory limit exceeded
EVP_R_MESSAGE_DIGEST_IS_NULL:159:message digest is null
EVP_R_METHOD_NOT_SUPPORTED:144:method not supported
EVP_R_MISSING_PARAMETERS:103:missing parameters
EVP_R_NOT_XOF_OR_INVALID_LENGTH:178:not XOF or invalid length
EVP_R_NO_CIPHER_SET:131:no cipher set
EVP_R_NO_DEFAULT_DIGEST:158:no default digest
EVP_R_NO_DIGEST_SET:139:no digest set
EVP_R_NO_KEY_SET:154:no key set
EVP_R_NO_OPERATION_SET:149:no operation set
EVP_R_ONLY_ONESHOT_SUPPORTED:177:only oneshot supported
EVP_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE:150:\
	operation not supported for this keytype
EVP_R_OPERATON_NOT_INITIALIZED:151:operaton not initialized
EVP_R_OUTPUT_WOULD_OVERFLOW:184:output would overflow
EVP_R_PARTIALLY_OVERLAPPING:162:partially overlapping buffers
EVP_R_PBKDF2_ERROR:181:pbkdf2 error
EVP_R_PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED:179:\
	pkey application asn1 method already registered
EVP_R_PRIVATE_KEY_DECODE_ERROR:145:private key decode error
EVP_R_PRIVATE_KEY_ENCODE_ERROR:146:private key encode error
EVP_R_PUBLIC_KEY_NOT_RSA:106:public key not rsa
EVP_R_UNKNOWN_CIPHER:160:unknown cipher
EVP_R_UNKNOWN_DIGEST:161:unknown digest
EVP_R_UNKNOWN_OPTION:169:unknown option
EVP_R_UNKNOWN_PBE_ALGORITHM:121:unknown pbe algorithm
EVP_R_UNSUPPORTED_ALGORITHM:156:unsupported algorithm
EVP_R_UNSUPPORTED_CIPHER:107:unsupported cipher
EVP_R_UNSUPPORTED_KEYLENGTH:123:unsupported keylength
EVP_R_UNSUPPORTED_KEY_DERIVATION_FUNCTION:124:\
	unsupported key derivation function
EVP_R_UNSUPPORTED_KEY_SIZE:108:unsupported key size
EVP_R_UNSUPPORTED_NUMBER_OF_ROUNDS:135:unsupported number of rounds
EVP_R_UNSUPPORTED_PRF:125:unsupported prf
EVP_R_UNSUPPORTED_PRIVATE_KEY_ALGORITHM:118:unsupported private key algorithm
EVP_R_UNSUPPORTED_SALT_TYPE:126:unsupported salt type
EVP_R_WRAP_MODE_NOT_ALLOWED:170:wrap mode not allowed
EVP_R_WRONG_FINAL_BLOCK_LENGTH:109:wrong final block length
EVP_R_XTS_DUPLICATED_KEYS:183:xts duplicated keys
KDF_R_INVALID_DIGEST:100:invalid digest
KDF_R_MISSING_ITERATION_COUNT:109:missing iteration count
KDF_R_MISSING_KEY:104:missing key
KDF_R_MISSING_MESSAGE_DIGEST:105:missing message digest
KDF_R_MISSING_PARAMETER:101:missing parameter
KDF_R_MISSING_PASS:110:missing pass
KDF_R_MISSING_SALT:111:missing salt
KDF_R_MISSING_SECRET:107:missing secret
KDF_R_MISSING_SEED:106:missing seed
KDF_R_UNKNOWN_PARAMETER_TYPE:103:unknown parameter type
KDF_R_VALUE_ERROR:108:value error
KDF_R_VALUE_MISSING:102:value missing
OBJ_R_OID_EXISTS:102:oid exists
OBJ_R_UNKNOWN_NID:101:unknown nid
OCSP_R_CERTIFICATE_VERIFY_ERROR:101:certificate verify error
OCSP_R_DIGEST_ERR:102:digest err
OCSP_R_ERROR_IN_NEXTUPDATE_FIELD:122:error in nextupdate field
OCSP_R_ERROR_IN_THISUPDATE_FIELD:123:error in thisupdate field
OCSP_R_ERROR_PARSING_URL:121:error parsing url
OCSP_R_MISSING_OCSPSIGNING_USAGE:103:missing ocspsigning usage
OCSP_R_NEXTUPDATE_BEFORE_THISUPDATE:124:nextupdate before thisupdate
OCSP_R_NOT_BASIC_RESPONSE:104:not basic response
OCSP_R_NO_CERTIFICATES_IN_CHAIN:105:no certificates in chain
OCSP_R_NO_RESPONSE_DATA:108:no response data
OCSP_R_NO_REVOKED_TIME:109:no revoked time
OCSP_R_NO_SIGNER_KEY:130:no signer key
OCSP_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:110:\
	private key does not match certificate
OCSP_R_REQUEST_NOT_SIGNED:128:request not signed
OCSP_R_RESPONSE_CONTAINS_NO_REVOCATION_DATA:111:\
	response contains no revocation data
OCSP_R_ROOT_CA_NOT_TRUSTED:112:root ca not trusted
OCSP_R_SERVER_RESPONSE_ERROR:114:server response error
OCSP_R_SERVER_RESPONSE_PARSE_ERROR:115:server response parse error
OCSP_R_SIGNATURE_FAILURE:117:signature failure
OCSP_R_SIGNER_CERTIFICATE_NOT_FOUND:118:signer certificate not found
OCSP_R_STATUS_EXPIRED:125:status expired
OCSP_R_STATUS_NOT_YET_VALID:126:status not yet valid
OCSP_R_STATUS_TOO_OLD:127:status too old
OCSP_R_UNKNOWN_MESSAGE_DIGEST:119:unknown message digest
OCSP_R_UNKNOWN_NID:120:unknown nid
OCSP_R_UNSUPPORTED_REQUESTORNAME_TYPE:129:unsupported requestorname type
OSSL_STORE_R_AMBIGUOUS_CONTENT_TYPE:107:ambiguous content type
OSSL_STORE_R_BAD_PASSWORD_READ:115:bad password read
OSSL_STORE_R_ERROR_VERIFYING_PKCS12_MAC:113:error verifying pkcs12 mac
OSSL_STORE_R_FINGERPRINT_SIZE_DOES_NOT_MATCH_DIGEST:121:\
	fingerprint size does not match digest
OSSL_STORE_R_INVALID_SCHEME:106:invalid scheme
OSSL_STORE_R_IS_NOT_A:112:is not a
OSSL_STORE_R_LOADER_INCOMPLETE:116:loader incomplete
OSSL_STORE_R_LOADING_STARTED:117:loading started
OSSL_STORE_R_NOT_A_CERTIFICATE:100:not a certificate
OSSL_STORE_R_NOT_A_CRL:101:not a crl
OSSL_STORE_R_NOT_A_KEY:102:not a key
OSSL_STORE_R_NOT_A_NAME:103:not a name
OSSL_STORE_R_NOT_PARAMETERS:104:not parameters
OSSL_STORE_R_PASSPHRASE_CALLBACK_ERROR:114:passphrase callback error
OSSL_STORE_R_PATH_MUST_BE_ABSOLUTE:108:path must be absolute
OSSL_STORE_R_SEARCH_ONLY_SUPPORTED_FOR_DIRECTORIES:119:\
	search only supported for directories
OSSL_STORE_R_UI_PROCESS_INTERRUPTED_OR_CANCELLED:109:\
	ui process interrupted or cancelled
OSSL_STORE_R_UNREGISTERED_SCHEME:105:unregistered scheme
OSSL_STORE_R_UNSUPPORTED_CONTENT_TYPE:110:unsupported content type
OSSL_STORE_R_UNSUPPORTED_OPERATION:118:unsupported operation
OSSL_STORE_R_UNSUPPORTED_SEARCH_TYPE:120:unsupported search type
OSSL_STORE_R_URI_AUTHORITY_UNSUPPORTED:111:uri authority unsupported
PEM_R_BAD_BASE64_DECODE:100:bad base64 decode
PEM_R_BAD_DECRYPT:101:bad decrypt
PEM_R_BAD_END_LINE:102:bad end line
PEM_R_BAD_IV_CHARS:103:bad iv chars
PEM_R_BAD_MAGIC_NUMBER:116:bad magic number
PEM_R_BAD_PASSWORD_READ:104:bad password read
PEM_R_BAD_VERSION_NUMBER:117:bad version number
PEM_R_BIO_WRITE_FAILURE:118:bio write failure
PEM_R_CIPHER_IS_NULL:127:cipher is null
PEM_R_ERROR_CONVERTING_PRIVATE_KEY:115:error converting private key
PEM_R_EXPECTING_PRIVATE_KEY_BLOB:119:expecting private key blob
PEM_R_EXPECTING_PUBLIC_KEY_BLOB:120:expecting public key blob
PEM_R_HEADER_TOO_LONG:128:header too long
PEM_R_INCONSISTENT_HEADER:121:inconsistent header
PEM_R_KEYBLOB_HEADER_PARSE_ERROR:122:keyblob header parse error
PEM_R_KEYBLOB_TOO_SHORT:123:keyblob too short
PEM_R_MISSING_DEK_IV:129:missing dek iv
PEM_R_NOT_DEK_INFO:105:not dek info
PEM_R_NOT_ENCRYPTED:106:not encrypted
PEM_R_NOT_PROC_TYPE:107:not proc type
PEM_R_NO_START_LINE:108:no start line
PEM_R_PROBLEMS_GETTING_PASSWORD:109:problems getting password
PEM_R_PVK_DATA_TOO_SHORT:124:pvk data too short
PEM_R_PVK_TOO_SHORT:125:pvk too short
PEM_R_READ_KEY:111:read key
PEM_R_SHORT_HEADER:112:short header
PEM_R_UNEXPECTED_DEK_IV:130:unexpected dek iv
PEM_R_UNSUPPORTED_CIPHER:113:unsupported cipher
PEM_R_UNSUPPORTED_ENCRYPTION:114:unsupported encryption
PEM_R_UNSUPPORTED_KEY_COMPONENTS:126:unsupported key components
PEM_R_UNSUPPORTED_PUBLIC_KEY_TYPE:110:unsupported public key type
PKCS12_R_CANT_PACK_STRUCTURE:100:cant pack structure
PKCS12_R_CONTENT_TYPE_NOT_DATA:121:content type not data
PKCS12_R_DECODE_ERROR:101:decode error
PKCS12_R_ENCODE_ERROR:102:encode error
PKCS12_R_ENCRYPT_ERROR:103:encrypt error
PKCS12_R_ERROR_SETTING_ENCRYPTED_DATA_TYPE:120:error setting encrypted data type
PKCS12_R_INVALID_NULL_ARGUMENT:104:invalid null argument
PKCS12_R_INVALID_NULL_PKCS12_POINTER:105:invalid null pkcs12 pointer
PKCS12_R_IV_GEN_ERROR:106:iv gen error
PKCS12_R_KEY_GEN_ERROR:107:key gen error
PKCS12_R_MAC_ABSENT:108:mac absent
PKCS12_R_MAC_GENERATION_ERROR:109:mac generation error
PKCS12_R_MAC_SETUP_ERROR:110:mac setup error
PKCS12_R_MAC_STRING_SET_ERROR:111:mac string set error
PKCS12_R_MAC_VERIFY_FAILURE:113:mac verify failure
PKCS12_R_PARSE_ERROR:114:parse error
PKCS12_R_PKCS12_ALGOR_CIPHERINIT_ERROR:115:pkcs12 algor cipherinit error
PKCS12_R_PKCS12_CIPHERFINAL_ERROR:116:pkcs12 cipherfinal error
PKCS12_R_PKCS12_PBE_CRYPT_ERROR:117:pkcs12 pbe crypt error
PKCS12_R_UNKNOWN_DIGEST_ALGORITHM:118:unknown digest algorithm
PKCS12_R_UNSUPPORTED_PKCS12_MODE:119:unsupported pkcs12 mode
PKCS7_R_CERTIFICATE_VERIFY_ERROR:117:certificate verify error
PKCS7_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER:144:cipher has no object identifier
PKCS7_R_CIPHER_NOT_INITIALIZED:116:cipher not initialized
PKCS7_R_CONTENT_AND_DATA_PRESENT:118:content and data present
PKCS7_R_CTRL_ERROR:152:ctrl error
PKCS7_R_DECRYPT_ERROR:119:decrypt error
PKCS7_R_DIGEST_FAILURE:101:digest failure
PKCS7_R_ENCRYPTION_CTRL_FAILURE:149:encryption ctrl failure
PKCS7_R_ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:150:\
	encryption not supported for this key type
PKCS7_R_ERROR_ADDING_RECIPIENT:120:error adding recipient
PKCS7_R_ERROR_SETTING_CIPHER:121:error setting cipher
PKCS7_R_INVALID_NULL_POINTER:143:invalid null pointer
PKCS7_R_INVALID_SIGNED_DATA_TYPE:155:invalid signed data type
PKCS7_R_NO_CONTENT:122:no content
PKCS7_R_NO_DEFAULT_DIGEST:151:no default digest
PKCS7_R_NO_MATCHING_DIGEST_TYPE_FOUND:154:no matching digest type found
PKCS7_R_NO_RECIPIENT_MATCHES_CERTIFICATE:115:no recipient matches certificate
PKCS7_R_NO_SIGNATURES_ON_DATA:123:no signatures on data
PKCS7_R_NO_SIGNERS:142:no signers
PKCS7_R_OPERATION_NOT_SUPPORTED_ON_THIS_TYPE:104:\
	operation not supported on this type
PKCS7_R_PKCS7_ADD_SIGNATURE_ERROR:124:pkcs7 add signature error
PKCS7_R_PKCS7_ADD_SIGNER_ERROR:153:pkcs7 add signer error
PKCS7_R_PKCS7_DATASIGN:145:pkcs7 datasign
PKCS7_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:127:\
	private key does not match certificate
PKCS7_R_SIGNATURE_FAILURE:105:signature failure
PKCS7_R_SIGNER_CERTIFICATE_NOT_FOUND:128:signer certificate not found
PKCS7_R_SIGNING_CTRL_FAILURE:147:signing ctrl failure
PKCS7_R_SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:148:\
	signing not supported for this key type
PKCS7_R_SMIME_TEXT_ERROR:129:smime text error
PKCS7_R_UNABLE_TO_FIND_CERTIFICATE:106:unable to find certificate
PKCS7_R_UNABLE_TO_FIND_MEM_BIO:107:unable to find mem bio
PKCS7_R_UNABLE_TO_FIND_MESSAGE_DIGEST:108:unable to find message digest
PKCS7_R_UNKNOWN_DIGEST_TYPE:109:unknown digest type
PKCS7_R_UNKNOWN_OPERATION:110:unknown operation
PKCS7_R_UNSUPPORTED_CIPHER_TYPE:111:unsupported cipher type
PKCS7_R_UNSUPPORTED_CONTENT_TYPE:112:unsupported content type
PKCS7_R_WRONG_CONTENT_TYPE:113:wrong content type
PKCS7_R_WRONG_PKCS7_TYPE:114:wrong pkcs7 type
RAND_R_ADDITIONAL_INPUT_TOO_LONG:102:additional input too long
RAND_R_ALREADY_INSTANTIATED:103:already instantiated
RAND_R_ARGUMENT_OUT_OF_RANGE:105:argument out of range
RAND_R_CANNOT_OPEN_FILE:121:Cannot open file
RAND_R_DRBG_ALREADY_INITIALIZED:129:drbg already initialized
RAND_R_DRBG_NOT_INITIALISED:104:drbg not initialised
RAND_R_ENTROPY_INPUT_TOO_LONG:106:entropy input too long
RAND_R_ENTROPY_OUT_OF_RANGE:124:entropy out of range
RAND_R_ERROR_ENTROPY_POOL_WAS_IGNORED:127:error entropy pool was ignored
RAND_R_ERROR_INITIALISING_DRBG:107:error initialising drbg
RAND_R_ERROR_INSTANTIATING_DRBG:108:error instantiating drbg
RAND_R_ERROR_RETRIEVING_ADDITIONAL_INPUT:109:error retrieving additional input
RAND_R_ERROR_RETRIEVING_ENTROPY:110:error retrieving entropy
RAND_R_ERROR_RETRIEVING_NONCE:111:error retrieving nonce
RAND_R_FAILED_TO_CREATE_LOCK:126:failed to create lock
RAND_R_FUNC_NOT_IMPLEMENTED:101:Function not implemented
RAND_R_FWRITE_ERROR:123:Error writing file
RAND_R_GENERATE_ERROR:112:generate error
RAND_R_INTERNAL_ERROR:113:internal error
RAND_R_IN_ERROR_STATE:114:in error state
RAND_R_NOT_A_REGULAR_FILE:122:Not a regular file
RAND_R_NOT_INSTANTIATED:115:not instantiated
RAND_R_NO_DRBG_IMPLEMENTATION_SELECTED:128:no drbg implementation selected
RAND_R_PARENT_LOCKING_NOT_ENABLED:130:parent locking not enabled
RAND_R_PARENT_STRENGTH_TOO_WEAK:131:parent strength too weak
RAND_R_PERSONALISATION_STRING_TOO_LONG:116:personalisation string too long
RAND_R_PREDICTION_RESISTANCE_NOT_SUPPORTED:133:\
	prediction resistance not supported
RAND_R_PRNG_NOT_SEEDED:100:PRNG not seeded
RAND_R_RANDOM_POOL_OVERFLOW:125:random pool overflow
RAND_R_RANDOM_POOL_UNDERFLOW:134:random pool underflow
RAND_R_REQUEST_TOO_LARGE_FOR_DRBG:117:request too large for drbg
RAND_R_RESEED_ERROR:118:reseed error
RAND_R_SELFTEST_FAILURE:119:selftest failure
RAND_R_TOO_LITTLE_NONCE_REQUESTED:135:too little nonce requested
RAND_R_TOO_MUCH_NONCE_REQUESTED:136:too much nonce requested
RAND_R_UNSUPPORTED_DRBG_FLAGS:132:unsupported drbg flags
RAND_R_UNSUPPORTED_DRBG_TYPE:120:unsupported drbg type
RSA_R_ALGORITHM_MISMATCH:100:algorithm mismatch
RSA_R_BAD_E_VALUE:101:bad e value
RSA_R_BAD_FIXED_HEADER_DECRYPT:102:bad fixed header decrypt
RSA_R_BAD_PAD_BYTE_COUNT:103:bad pad byte count
RSA_R_BAD_SIGNATURE:104:bad signature
RSA_R_BLOCK_TYPE_IS_NOT_01:106:block type is not 01
RSA_R_BLOCK_TYPE_IS_NOT_02:107:block type is not 02
RSA_R_DATA_GREATER_THAN_MOD_LEN:108:data greater than mod len
RSA_R_DATA_TOO_LARGE:109:data too large
RSA_R_DATA_TOO_LARGE_FOR_KEY_SIZE:110:data too large for key size
RSA_R_DATA_TOO_LARGE_FOR_MODULUS:132:data too large for modulus
RSA_R_DATA_TOO_SMALL:111:data too small
RSA_R_DATA_TOO_SMALL_FOR_KEY_SIZE:122:data too small for key size
RSA_R_DIGEST_DOES_NOT_MATCH:158:digest does not match
RSA_R_DIGEST_NOT_ALLOWED:145:digest not allowed
RSA_R_DIGEST_TOO_BIG_FOR_RSA_KEY:112:digest too big for rsa key
RSA_R_DMP1_NOT_CONGRUENT_TO_D:124:dmp1 not congruent to d
RSA_R_DMQ1_NOT_CONGRUENT_TO_D:125:dmq1 not congruent to d
RSA_R_D_E_NOT_CONGRUENT_TO_1:123:d e not congruent to 1
RSA_R_FIRST_OCTET_INVALID:133:first octet invalid
RSA_R_ILLEGAL_OR_UNSUPPORTED_PADDING_MODE:144:\
	illegal or unsupported padding mode
RSA_R_INVALID_DIGEST:157:invalid digest
RSA_R_INVALID_DIGEST_LENGTH:143:invalid digest length
RSA_R_INVALID_HEADER:137:invalid header
RSA_R_INVALID_LABEL:160:invalid label
RSA_R_INVALID_MESSAGE_LENGTH:131:invalid message length
RSA_R_INVALID_MGF1_MD:156:invalid mgf1 md
RSA_R_INVALID_MULTI_PRIME_KEY:167:invalid multi prime key
RSA_R_INVALID_OAEP_PARAMETERS:161:invalid oaep parameters
RSA_R_INVALID_PADDING:138:invalid padding
RSA_R_INVALID_PADDING_MODE:141:invalid padding mode
RSA_R_INVALID_PSS_PARAMETERS:149:invalid pss parameters
RSA_R_INVALID_PSS_SALTLEN:146:invalid pss saltlen
RSA_R_INVALID_SALT_LENGTH:150:invalid salt length
RSA_R_INVALID_TRAILER:139:invalid trailer
RSA_R_INVALID_X931_DIGEST:142:invalid x931 digest
RSA_R_IQMP_NOT_INVERSE_OF_Q:126:iqmp not inverse of q
RSA_R_KEY_PRIME_NUM_INVALID:165:key prime num invalid
RSA_R_KEY_SIZE_TOO_SMALL:120:key size too small
RSA_R_LAST_OCTET_INVALID:134:last octet invalid
RSA_R_MGF1_DIGEST_NOT_ALLOWED:152:mgf1 digest not allowed
RSA_R_MISSING_PRIVATE_KEY:179:missing private key
RSA_R_MODULUS_TOO_LARGE:105:modulus too large
RSA_R_MP_COEFFICIENT_NOT_INVERSE_OF_R:168:mp coefficient not inverse of r
RSA_R_MP_EXPONENT_NOT_CONGRUENT_TO_D:169:mp exponent not congruent to d
RSA_R_MP_R_NOT_PRIME:170:mp r not prime
RSA_R_NO_PUBLIC_EXPONENT:140:no public exponent
RSA_R_NULL_BEFORE_BLOCK_MISSING:113:null before block missing
RSA_R_N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES:172:n does not equal product of primes
RSA_R_N_DOES_NOT_EQUAL_P_Q:127:n does not equal p q
RSA_R_OAEP_DECODING_ERROR:121:oaep decoding error
RSA_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE:148:\
	operation not supported for this keytype
RSA_R_PADDING_CHECK_FAILED:114:padding check failed
RSA_R_PKCS_DECODING_ERROR:159:pkcs decoding error
RSA_R_PSS_SALTLEN_TOO_SMALL:164:pss saltlen too small
RSA_R_P_NOT_PRIME:128:p not prime
RSA_R_Q_NOT_PRIME:129:q not prime
RSA_R_RSA_OPERATIONS_NOT_SUPPORTED:130:rsa operations not supported
RSA_R_SLEN_CHECK_FAILED:136:salt length check failed
RSA_R_SLEN_RECOVERY_FAILED:135:salt length recovery failed
RSA_R_SSLV3_ROLLBACK_ATTACK:115:sslv3 rollback attack
RSA_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD:116:\
	the asn1 object identifier is not known for this md
RSA_R_UNKNOWN_ALGORITHM_TYPE:117:unknown algorithm type
RSA_R_UNKNOWN_DIGEST:166:unknown digest
RSA_R_UNKNOWN_MASK_DIGEST:151:unknown mask digest
RSA_R_UNKNOWN_PADDING_TYPE:118:unknown padding type
RSA_R_UNSUPPORTED_ENCRYPTION_TYPE:162:unsupported encryption type
RSA_R_UNSUPPORTED_LABEL_SOURCE:163:unsupported label source
RSA_R_UNSUPPORTED_MASK_ALGORITHM:153:unsupported mask algorithm
RSA_R_UNSUPPORTED_MASK_PARAMETER:154:unsupported mask parameter
RSA_R_UNSUPPORTED_SIGNATURE_TYPE:155:unsupported signature type
RSA_R_VALUE_MISSING:147:value missing
RSA_R_WRONG_SIGNATURE_LENGTH:119:wrong signature length
SM2_R_ASN1_ERROR:100:asn1 error
SM2_R_BAD_SIGNATURE:101:bad signature
SM2_R_BUFFER_TOO_SMALL:107:buffer too small
SM2_R_DIST_ID_TOO_LARGE:110:dist id too large
SM2_R_ID_NOT_SET:112:id not set
SM2_R_ID_TOO_LARGE:111:id too large
SM2_R_INVALID_CURVE:108:invalid curve
SM2_R_INVALID_DIGEST:102:invalid digest
SM2_R_INVALID_DIGEST_TYPE:103:invalid digest type
SM2_R_INVALID_ENCODING:104:invalid encoding
SM2_R_INVALID_FIELD:105:invalid field
SM2_R_NO_PARAMETERS_SET:109:no parameters set
SM2_R_USER_ID_TOO_LARGE:106:user id too large
SSL_R_APPLICATION_DATA_AFTER_CLOSE_NOTIFY:291:\
	application data after close notify
SSL_R_APP_DATA_IN_HANDSHAKE:100:app data in handshake
SSL_R_ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT:272:\
	attempt to reuse session in different context
SSL_R_AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE:143:\
	at least TLS 1.0 needed in FIPS mode
SSL_R_AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE:158:\
	at least (D)TLS 1.2 needed in Suite B mode
SSL_R_BAD_CHANGE_CIPHER_SPEC:103:bad change cipher spec
SSL_R_BAD_CIPHER:186:bad cipher
SSL_R_BAD_DATA:390:bad data
SSL_R_BAD_DATA_RETURNED_BY_CALLBACK:106:bad data returned by callback
SSL_R_BAD_DECOMPRESSION:107:bad decompression
SSL_R_BAD_DH_VALUE:102:bad dh value
SSL_R_BAD_DIGEST_LENGTH:111:bad digest length
SSL_R_BAD_EARLY_DATA:233:bad early data
SSL_R_BAD_ECC_CERT:304:bad ecc cert
SSL_R_BAD_ECPOINT:306:bad ecpoint
SSL_R_BAD_EXTENSION:110:bad extension
SSL_R_BAD_HANDSHAKE_LENGTH:332:bad handshake length
SSL_R_BAD_HANDSHAKE_STATE:236:bad handshake state
SSL_R_BAD_HELLO_REQUEST:105:bad hello request
SSL_R_BAD_HRR_VERSION:263:bad hrr version
SSL_R_BAD_KEY_SHARE:108:bad key share
SSL_R_BAD_KEY_UPDATE:122:bad key update
SSL_R_BAD_LEGACY_VERSION:292:bad legacy version
SSL_R_BAD_LENGTH:271:bad length
SSL_R_BAD_PACKET:240:bad packet
SSL_R_BAD_PACKET_LENGTH:115:bad packet length
SSL_R_BAD_PROTOCOL_VERSION_NUMBER:116:bad protocol version number
SSL_R_BAD_PSK:219:bad psk
SSL_R_BAD_PSK_IDENTITY:114:bad psk identity
SSL_R_BAD_RECORD_TYPE:443:bad record type
SSL_R_BAD_RSA_ENCRYPT:119:bad rsa encrypt
SSL_R_BAD_SIGNATURE:123:bad signature
SSL_R_BAD_SRP_A_LENGTH:347:bad srp a length
SSL_R_BAD_SRP_PARAMETERS:371:bad srp parameters
SSL_R_BAD_SRTP_MKI_VALUE:352:bad srtp mki value
SSL_R_BAD_SRTP_PROTECTION_PROFILE_LIST:353:bad srtp protection profile list
SSL_R_BAD_SSL_FILETYPE:124:bad ssl filetype
SSL_R_BAD_VALUE:384:bad value
SSL_R_BAD_WRITE_RETRY:127:bad write retry
SSL_R_BINDER_DOES_NOT_VERIFY:253:binder does not verify
SSL_R_BIO_NOT_SET:128:bio not set
SSL_R_BLOCK_CIPHER_PAD_IS_WRONG:129:block cipher pad is wrong
SSL_R_BN_LIB:130:bn lib
SSL_R_CALLBACK_FAILED:234:callback failed
SSL_R_CANNOT_CHANGE_CIPHER:109:cannot change cipher
SSL_R_CA_DN_LENGTH_MISMATCH:131:ca dn length mismatch
SSL_R_CA_KEY_TOO_SMALL:397:ca key too small
SSL_R_CA_MD_TOO_WEAK:398:ca md too weak
SSL_R_CCS_RECEIVED_EARLY:133:ccs received early
SSL_R_CERTIFICATE_VERIFY_FAILED:134:certificate verify failed
SSL_R_CERT_CB_ERROR:377:cert cb error
SSL_R_CERT_LENGTH_MISMATCH:135:cert length mismatch
SSL_R_CIPHERSUITE_DIGEST_HAS_CHANGED:218:ciphersuite digest has changed
SSL_R_CIPHER_CODE_WRONG_LENGTH:137:cipher code wrong length
SSL_R_CIPHER_OR_HASH_UNAVAILABLE:138:cipher or hash unavailable
SSL_R_CLIENTHELLO_TLSEXT:226:clienthello tlsext
SSL_R_COMPRESSED_LENGTH_TOO_LONG:140:compressed length too long
SSL_R_COMPRESSION_DISABLED:343:compression disabled
SSL_R_COMPRESSION_FAILURE:141:compression failure
SSL_R_COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE:307:\
	compression id not within private range
SSL_R_COMPRESSION_LIBRARY_ERROR:142:compression library error
SSL_R_CONNECTION_TYPE_NOT_SET:144:connection type not set
SSL_R_CONTEXT_NOT_DANE_ENABLED:167:context not dane enabled
SSL_R_COOKIE_GEN_CALLBACK_FAILURE:400:cookie gen callback failure
SSL_R_COOKIE_MISMATCH:308:cookie mismatch
SSL_R_CUSTOM_EXT_HANDLER_ALREADY_INSTALLED:206:\
	custom ext handler already installed
SSL_R_DANE_ALREADY_ENABLED:172:dane already enabled
SSL_R_DANE_CANNOT_OVERRIDE_MTYPE_FULL:173:dane cannot override mtype full
SSL_R_DANE_NOT_ENABLED:175:dane not enabled
SSL_R_DANE_TLSA_BAD_CERTIFICATE:180:dane tlsa bad certificate
SSL_R_DANE_TLSA_BAD_CERTIFICATE_USAGE:184:dane tlsa bad certificate usage
SSL_R_DANE_TLSA_BAD_DATA_LENGTH:189:dane tlsa bad data length
SSL_R_DANE_TLSA_BAD_DIGEST_LENGTH:192:dane tlsa bad digest length
SSL_R_DANE_TLSA_BAD_MATCHING_TYPE:200:dane tlsa bad matching type
SSL_R_DANE_TLSA_BAD_PUBLIC_KEY:201:dane tlsa bad public key
SSL_R_DANE_TLSA_BAD_SELECTOR:202:dane tlsa bad selector
SSL_R_DANE_TLSA_NULL_DATA:203:dane tlsa null data
SSL_R_DATA_BETWEEN_CCS_AND_FINISHED:145:data between ccs and finished
SSL_R_DATA_LENGTH_TOO_LONG:146:data length too long
SSL_R_DECRYPTION_FAILED:147:decryption failed
SSL_R_DECRYPTION_FAILED_OR_BAD_RECORD_MAC:281:\
	decryption failed or bad record mac
SSL_R_DH_KEY_TOO_SMALL:394:dh key too small
SSL_R_DH_PUBLIC_VALUE_LENGTH_IS_WRONG:148:dh public value length is wrong
SSL_R_DIGEST_CHECK_FAILED:149:digest check failed
SSL_R_DTLS_MESSAGE_TOO_BIG:334:dtls message too big
SSL_R_DUPLICATE_COMPRESSION_ID:309:duplicate compression id
SSL_R_ECC_CERT_NOT_FOR_SIGNING:318:ecc cert not for signing
SSL_R_ECDH_REQUIRED_FOR_SUITEB_MODE:374:ecdh required for suiteb mode
SSL_R_EE_KEY_TOO_SMALL:399:ee key too small
SSL_R_EMPTY_SRTP_PROTECTION_PROFILE_LIST:354:empty srtp protection profile list
SSL_R_ENCRYPTED_LENGTH_TOO_LONG:150:encrypted length too long
SSL_R_ERROR_IN_RECEIVED_CIPHER_LIST:151:error in received cipher list
SSL_R_ERROR_SETTING_TLSA_BASE_DOMAIN:204:error setting tlsa base domain
SSL_R_EXCEEDS_MAX_FRAGMENT_SIZE:194:exceeds max fragment size
SSL_R_EXCESSIVE_MESSAGE_SIZE:152:excessive message size
SSL_R_EXTENSION_NOT_RECEIVED:279:extension not received
SSL_R_EXTRA_DATA_IN_MESSAGE:153:extra data in message
SSL_R_EXT_LENGTH_MISMATCH:163:ext length mismatch
SSL_R_FAILED_TO_INIT_ASYNC:405:failed to init async
SSL_R_FRAGMENTED_CLIENT_HELLO:401:fragmented client hello
SSL_R_GOT_A_FIN_BEFORE_A_CCS:154:got a fin before a ccs
SSL_R_HTTPS_PROXY_REQUEST:155:https proxy request
SSL_R_HTTP_REQUEST:156:http request
SSL_R_ILLEGAL_POINT_COMPRESSION:162:illegal point compression
SSL_R_ILLEGAL_SUITEB_DIGEST:380:illegal Suite B digest
SSL_R_INAPPROPRIATE_FALLBACK:373:inappropriate fallback
SSL_R_INCONSISTENT_COMPRESSION:340:inconsistent compression
SSL_R_INCONSISTENT_EARLY_DATA_ALPN:222:inconsistent early data alpn
SSL_R_INCONSISTENT_EARLY_DATA_SNI:231:inconsistent early data sni
SSL_R_INCONSISTENT_EXTMS:104:inconsistent extms
SSL_R_INSUFFICIENT_SECURITY:241:insufficient security
SSL_R_INVALID_ALERT:205:invalid alert
SSL_R_INVALID_CCS_MESSAGE:260:invalid ccs message
SSL_R_INVALID_CERTIFICATE_OR_ALG:238:invalid certificate or alg
SSL_R_INVALID_COMMAND:280:invalid command
SSL_R_INVALID_COMPRESSION_ALGORITHM:341:invalid compression algorithm
SSL_R_INVALID_CONFIG:283:invalid config
SSL_R_INVALID_CONFIGURATION_NAME:113:invalid configuration name
SSL_R_INVALID_CONTEXT:282:invalid context
SSL_R_INVALID_CT_VALIDATION_TYPE:212:invalid ct validation type
SSL_R_INVALID_KEY_UPDATE_TYPE:120:invalid key update type
SSL_R_INVALID_MAX_EARLY_DATA:174:invalid max early data
SSL_R_INVALID_NULL_CMD_NAME:385:invalid null cmd name
SSL_R_INVALID_SEQUENCE_NUMBER:402:invalid sequence number
SSL_R_INVALID_SERVERINFO_DATA:388:invalid serverinfo data
SSL_R_INVALID_SESSION_ID:999:invalid session id
SSL_R_INVALID_SRP_USERNAME:357:invalid srp username
SSL_R_INVALID_STATUS_RESPONSE:328:invalid status response
SSL_R_INVALID_TICKET_KEYS_LENGTH:325:invalid ticket keys length
SSL_R_LENGTH_MISMATCH:159:length mismatch
SSL_R_LENGTH_TOO_LONG:404:length too long
SSL_R_LENGTH_TOO_SHORT:160:length too short
SSL_R_LIBRARY_BUG:274:library bug
SSL_R_LIBRARY_HAS_NO_CIPHERS:161:library has no ciphers
SSL_R_MISSING_DSA_SIGNING_CERT:165:missing dsa signing cert
SSL_R_MISSING_ECDSA_SIGNING_CERT:381:missing ecdsa signing cert
SSL_R_MISSING_FATAL:256:missing fatal
SSL_R_MISSING_PARAMETERS:290:missing parameters
SSL_R_MISSING_PSK_KEX_MODES_EXTENSION:310:missing psk kex modes extension
SSL_R_MISSING_QUIC_TRANSPORT_PARAMETERS_EXTENSION:801:\
	missing quic transport parameters extension
SSL_R_MISSING_RSA_CERTIFICATE:168:missing rsa certificate
SSL_R_MISSING_RSA_ENCRYPTING_CERT:169:missing rsa encrypting cert
SSL_R_MISSING_RSA_SIGNING_CERT:170:missing rsa signing cert
SSL_R_MISSING_SIGALGS_EXTENSION:112:missing sigalgs extension
SSL_R_MISSING_SIGNING_CERT:221:missing signing cert
SSL_R_MISSING_SRP_PARAM:358:can't find SRP server param
SSL_R_MISSING_SUPPORTED_GROUPS_EXTENSION:209:missing supported groups extension
SSL_R_MISSING_TMP_DH_KEY:171:missing tmp dh key
SSL_R_MISSING_TMP_ECDH_KEY:311:missing tmp ecdh key
SSL_R_MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA:293:\
	mixed handshake and non handshake data
SSL_R_NOT_ON_RECORD_BOUNDARY:182:not on record boundary
SSL_R_NOT_REPLACING_CERTIFICATE:289:not replacing certificate
SSL_R_NOT_SERVER:284:not server
SSL_R_NO_APPLICATION_PROTOCOL:235:no application protocol
SSL_R_NO_CERTIFICATES_RETURNED:176:no certificates returned
SSL_R_NO_CERTIFICATE_ASSIGNED:177:no certificate assigned
SSL_R_NO_CERTIFICATE_SET:179:no certificate set
SSL_R_NO_CHANGE_FOLLOWING_HRR:214:no change following hrr
SSL_R_NO_CIPHERS_AVAILABLE:181:no ciphers available
SSL_R_NO_CIPHERS_SPECIFIED:183:no ciphers specified
SSL_R_NO_CIPHER_MATCH:185:no cipher match
SSL_R_NO_CLIENT_CERT_METHOD:331:no client cert method
SSL_R_NO_COMPRESSION_SPECIFIED:187:no compression specified
SSL_R_NO_COOKIE_CALLBACK_SET:287:no cookie callback set
SSL_R_NO_GOST_CERTIFICATE_SENT_BY_PEER:330:\
	Peer haven't sent GOST certificate, required for selected ciphersuite
SSL_R_NO_METHOD_SPECIFIED:188:no method specified
SSL_R_NO_PEM_EXTENSIONS:389:no pem extensions
SSL_R_NO_PRIVATE_KEY_ASSIGNED:190:no private key assigned
SSL_R_NO_PROTOCOLS_AVAILABLE:191:no protocols available
SSL_R_NO_RENEGOTIATION:339:no renegotiation
SSL_R_NO_REQUIRED_DIGEST:324:no required digest
SSL_R_NO_SHARED_CIPHER:193:no shared cipher
SSL_R_NO_SHARED_GROUPS:410:no shared groups
SSL_R_NO_SHARED_SIGNATURE_ALGORITHMS:376:no shared signature algorithms
SSL_R_NO_SRTP_PROFILES:359:no srtp profiles
SSL_R_NO_SUITABLE_KEY_SHARE:101:no suitable key share
SSL_R_NO_SUITABLE_SIGNATURE_ALGORITHM:118:no suitable signature algorithm
SSL_R_NO_VALID_SCTS:216:no valid scts
SSL_R_NO_VERIFY_COOKIE_CALLBACK:403:no verify cookie callback
SSL_R_NULL_SSL_CTX:195:null ssl ctx
SSL_R_NULL_SSL_METHOD_PASSED:196:null ssl method passed
SSL_R_OCSP_CALLBACK_FAILURE:294:ocsp callback failure
SSL_R_OLD_SESSION_CIPHER_NOT_RETURNED:197:old session cipher not returned
SSL_R_OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED:344:\
	old session compression algorithm not returned
SSL_R_OVERFLOW_ERROR:237:overflow error
SSL_R_PACKET_LENGTH_TOO_LONG:198:packet length too long
SSL_R_PARSE_TLSEXT:227:parse tlsext
SSL_R_PATH_TOO_LONG:270:path too long
SSL_R_PEER_DID_NOT_RETURN_A_CERTIFICATE:199:peer did not return a certificate
SSL_R_PEM_NAME_BAD_PREFIX:391:pem name bad prefix
SSL_R_PEM_NAME_TOO_SHORT:392:pem name too short
SSL_R_PIPELINE_FAILURE:406:pipeline failure
SSL_R_POST_HANDSHAKE_AUTH_ENCODING_ERR:278:post handshake auth encoding err
SSL_R_PRIVATE_KEY_MISMATCH:288:private key mismatch
SSL_R_PROTOCOL_IS_SHUTDOWN:207:protocol is shutdown
SSL_R_PSK_IDENTITY_NOT_FOUND:223:psk identity not found
SSL_R_PSK_NO_CLIENT_CB:224:psk no client cb
SSL_R_PSK_NO_SERVER_CB:225:psk no server cb
SSL_R_READ_BIO_NOT_SET:211:read bio not set
SSL_R_READ_TIMEOUT_EXPIRED:312:read timeout expired
SSL_R_RECORD_LENGTH_MISMATCH:213:record length mismatch
SSL_R_RECORD_TOO_SMALL:298:record too small
SSL_R_RENEGOTIATE_EXT_TOO_LONG:335:renegotiate ext too long
SSL_R_RENEGOTIATION_ENCODING_ERR:336:renegotiation encoding err
SSL_R_RENEGOTIATION_MISMATCH:337:renegotiation mismatch
SSL_R_REQUEST_PENDING:285:request pending
SSL_R_REQUEST_SENT:286:request sent
SSL_R_REQUIRED_CIPHER_MISSING:215:required cipher missing
SSL_R_REQUIRED_COMPRESSION_ALGORITHM_MISSING:342:\
	required compression algorithm missing
SSL_R_SCSV_RECEIVED_WHEN_RENEGOTIATING:345:scsv received when renegotiating
SSL_R_SCT_VERIFICATION_FAILED:208:sct verification failed
SSL_R_SERVERHELLO_TLSEXT:275:serverhello tlsext
SSL_R_SESSION_ID_CONTEXT_UNINITIALIZED:277:session id context uninitialized
SSL_R_SHUTDOWN_WHILE_IN_INIT:407:shutdown while in init
SSL_R_SIGNATURE_ALGORITHMS_ERROR:360:signature algorithms error
SSL_R_SIGNATURE_FOR_NON_SIGNING_CERTIFICATE:220:\
	signature for non signing certificate
SSL_R_SRP_A_CALC:361:error with the srp params
SSL_R_SRTP_COULD_NOT_ALLOCATE_PROFILES:362:srtp could not allocate profiles
SSL_R_SRTP_PROTECTION_PROFILE_LIST_TOO_LONG:363:\
	srtp protection profile list too long
SSL_R_SRTP_UNKNOWN_PROTECTION_PROFILE:364:srtp unknown protection profile
SSL_R_SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH:232:\
	ssl3 ext invalid max fragment length
SSL_R_SSL3_EXT_INVALID_SERVERNAME:319:ssl3 ext invalid servername
SSL_R_SSL3_EXT_INVALID_SERVERNAME_TYPE:320:ssl3 ext invalid servername type
SSL_R_SSL3_SESSION_ID_TOO_LONG:300:ssl3 session id too long
SSL_R_SSL_COMMAND_SECTION_EMPTY:117:ssl command section empty
SSL_R_SSL_COMMAND_SECTION_NOT_FOUND:125:ssl command section not found
SSL_R_SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION:228:ssl ctx has no default ssl version
SSL_R_SSL_HANDSHAKE_FAILURE:229:ssl handshake failure
SSL_R_SSL_LIBRARY_HAS_NO_CIPHERS:230:ssl library has no ciphers
SSL_R_SSL_NEGATIVE_LENGTH:372:ssl negative length
SSL_R_SSL_SECTION_EMPTY:126:ssl section empty
SSL_R_SSL_SECTION_NOT_FOUND:136:ssl section not found
SSL_R_SSL_SESSION_ID_CALLBACK_FAILED:301:ssl session id callback failed
SSL_R_SSL_SESSION_ID_CONFLICT:302:ssl session id conflict
SSL_R_SSL_SESSION_ID_CONTEXT_TOO_LONG:273:ssl session id context too long
SSL_R_SSL_SESSION_ID_HAS_BAD_LENGTH:303:ssl session id has bad length
SSL_R_SSL_SESSION_ID_TOO_LONG:408:ssl session id too long
SSL_R_SSL_SESSION_VERSION_MISMATCH:210:ssl session version mismatch
SSL_R_STILL_IN_INIT:121:still in init
SSL_R_TLS_HEARTBEAT_PEER_DOESNT_ACCEPT:365:peer does not accept heartbeats
SSL_R_TLS_HEARTBEAT_PENDING:366:heartbeat request already pending
SSL_R_TLS_ILLEGAL_EXPORTER_LABEL:367:tls illegal exporter label
SSL_R_TLS_INVALID_ECPOINTFORMAT_LIST:157:tls invalid ecpointformat list
SSL_R_TOO_MANY_KEY_UPDATES:132:too many key updates
SSL_R_TOO_MANY_WARN_ALERTS:409:too many warn alerts
SSL_R_TOO_MUCH_EARLY_DATA:164:too much early data
SSL_R_UNABLE_TO_FIND_ECDH_PARAMETERS:314:unable to find ecdh parameters
SSL_R_UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS:239:\
	unable to find public key parameters
SSL_R_UNABLE_TO_LOAD_SSL3_MD5_ROUTINES:242:unable to load ssl3 md5 routines
SSL_R_UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES:243:unable to load ssl3 sha1 routines
SSL_R_UNEXPECTED_CCS_MESSAGE:262:unexpected ccs message
SSL_R_UNEXPECTED_END_OF_EARLY_DATA:178:unexpected end of early data
SSL_R_UNEXPECTED_MESSAGE:244:unexpected message
SSL_R_UNEXPECTED_RECORD:245:unexpected record
SSL_R_UNINITIALIZED:276:uninitialized
SSL_R_UNKNOWN_ALERT_TYPE:246:unknown alert type
SSL_R_UNKNOWN_CERTIFICATE_TYPE:247:unknown certificate type
SSL_R_UNKNOWN_CIPHER_RETURNED:248:unknown cipher returned
SSL_R_UNKNOWN_CIPHER_TYPE:249:unknown cipher type
SSL_R_UNKNOWN_CMD_NAME:386:unknown cmd name
SSL_R_UNKNOWN_COMMAND:139:unknown command
SSL_R_UNKNOWN_DIGEST:368:unknown digest
SSL_R_UNKNOWN_KEY_EXCHANGE_TYPE:250:unknown key exchange type
SSL_R_UNKNOWN_PKEY_TYPE:251:unknown pkey type
SSL_R_UNKNOWN_PROTOCOL:252:unknown protocol
SSL_R_UNKNOWN_SSL_VERSION:254:unknown ssl version
SSL_R_UNKNOWN_STATE:255:unknown state
SSL_R_UNSAFE_LEGACY_RENEGOTIATION_DISABLED:338:\
	unsafe legacy renegotiation disabled
SSL_R_UNSOLICITED_EXTENSION:217:unsolicited extension
SSL_R_UNSUPPORTED_COMPRESSION_ALGORITHM:257:unsupported compression algorithm
SSL_R_UNSUPPORTED_ELLIPTIC_CURVE:315:unsupported elliptic curve
SSL_R_UNSUPPORTED_PROTOCOL:258:unsupported protocol
SSL_R_UNSUPPORTED_SSL_VERSION:259:unsupported ssl version
SSL_R_UNSUPPORTED_STATUS_TYPE:329:unsupported status type
SSL_R_USE_SRTP_NOT_NEGOTIATED:369:use srtp not negotiated
SSL_R_VERSION_TOO_HIGH:166:version too high
SSL_R_VERSION_TOO_LOW:396:version too low
SSL_R_WRONG_CERTIFICATE_TYPE:383:wrong certificate type
SSL_R_WRONG_CIPHER_RETURNED:261:wrong cipher returned
SSL_R_WRONG_CURVE:378:wrong curve
SSL_R_WRONG_ENCRYPTION_LEVEL_RECEIVED:800:wrong encryption level received
SSL_R_WRONG_SIGNATURE_LENGTH:264:wrong signature length
SSL_R_WRONG_SIGNATURE_SIZE:265:wrong signature size
SSL_R_WRONG_SIGNATURE_TYPE:370:wrong signature type
SSL_R_WRONG_SSL_VERSION:266:wrong ssl version
SSL_R_WRONG_VERSION_NUMBER:267:wrong version number
SSL_R_X509_LIB:268:x509 lib
SSL_R_X509_VERIFICATION_SETUP_PROBLEMS:269:x509 verification setup problems
TS_R_BAD_PKCS7_TYPE:132:bad pkcs7 type
TS_R_BAD_TYPE:133:bad type
TS_R_CANNOT_LOAD_CERT:137:cannot load certificate
TS_R_CANNOT_LOAD_KEY:138:cannot load private key
TS_R_CERTIFICATE_VERIFY_ERROR:100:certificate verify error
TS_R_COULD_NOT_SET_ENGINE:127:could not set engine
TS_R_COULD_NOT_SET_TIME:115:could not set time
TS_R_DETACHED_CONTENT:134:detached content
TS_R_ESS_ADD_SIGNING_CERT_ERROR:116:ess add signing cert error
TS_R_ESS_ADD_SIGNING_CERT_V2_ERROR:139:ess add signing cert v2 error
TS_R_ESS_SIGNING_CERTIFICATE_ERROR:101:ess signing certificate error
TS_R_INVALID_NULL_POINTER:102:invalid null pointer
TS_R_INVALID_SIGNER_CERTIFICATE_PURPOSE:117:invalid signer certificate purpose
TS_R_MESSAGE_IMPRINT_MISMATCH:103:message imprint mismatch
TS_R_NONCE_MISMATCH:104:nonce mismatch
TS_R_NONCE_NOT_RETURNED:105:nonce not returned
TS_R_NO_CONTENT:106:no content
TS_R_NO_TIME_STAMP_TOKEN:107:no time stamp token
TS_R_PKCS7_ADD_SIGNATURE_ERROR:118:pkcs7 add signature error
TS_R_PKCS7_ADD_SIGNED_ATTR_ERROR:119:pkcs7 add signed attr error
TS_R_PKCS7_TO_TS_TST_INFO_FAILED:129:pkcs7 to ts tst info failed
TS_R_POLICY_MISMATCH:108:policy mismatch
TS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:120:\
	private key does not match certificate
TS_R_RESPONSE_SETUP_ERROR:121:response setup error
TS_R_SIGNATURE_FAILURE:109:signature failure
TS_R_THERE_MUST_BE_ONE_SIGNER:110:there must be one signer
TS_R_TIME_SYSCALL_ERROR:122:time syscall error
TS_R_TOKEN_NOT_PRESENT:130:token not present
TS_R_TOKEN_PRESENT:131:token present
TS_R_TSA_NAME_MISMATCH:111:tsa name mismatch
TS_R_TSA_UNTRUSTED:112:tsa untrusted
TS_R_TST_INFO_SETUP_ERROR:123:tst info setup error
TS_R_TS_DATASIGN:124:ts datasign
TS_R_UNACCEPTABLE_POLICY:125:unacceptable policy
TS_R_UNSUPPORTED_MD_ALGORITHM:126:unsupported md algorithm
TS_R_UNSUPPORTED_VERSION:113:unsupported version
TS_R_VAR_BAD_VALUE:135:var bad value
TS_R_VAR_LOOKUP_FAILURE:136:cannot find config variable
TS_R_WRONG_CONTENT_TYPE:114:wrong content type
UI_R_COMMON_OK_AND_CANCEL_CHARACTERS:104:common ok and cancel characters
UI_R_INDEX_TOO_LARGE:102:index too large
UI_R_INDEX_TOO_SMALL:103:index too small
UI_R_NO_RESULT_BUFFER:105:no result buffer
UI_R_PROCESSING_ERROR:107:processing error
UI_R_RESULT_TOO_LARGE:100:result too large
UI_R_RESULT_TOO_SMALL:101:result too small
UI_R_SYSASSIGN_ERROR:109:sys$assign error
UI_R_SYSDASSGN_ERROR:110:sys$dassgn error
UI_R_SYSQIOW_ERROR:111:sys$qiow error
UI_R_UNKNOWN_CONTROL_COMMAND:106:unknown control command
UI_R_UNKNOWN_TTYGET_ERRNO_VALUE:108:unknown ttyget errno value
UI_R_USER_DATA_DUPLICATION_UNSUPPORTED:112:user data duplication unsupported
X509V3_R_BAD_IP_ADDRESS:118:bad ip address
X509V3_R_BAD_OBJECT:119:bad object
X509V3_R_BN_DEC2BN_ERROR:100:bn dec2bn error
X509V3_R_BN_TO_ASN1_INTEGER_ERROR:101:bn to asn1 integer error
X509V3_R_DIRNAME_ERROR:149:dirname error
X509V3_R_DISTPOINT_ALREADY_SET:160:distpoint already set
X509V3_R_DUPLICATE_ZONE_ID:133:duplicate zone id
X509V3_R_ERROR_CONVERTING_ZONE:131:error converting zone
X509V3_R_ERROR_CREATING_EXTENSION:144:error creating extension
X509V3_R_ERROR_IN_EXTENSION:128:error in extension
X509V3_R_EXPECTED_A_SECTION_NAME:137:expected a section name
X509V3_R_EXTENSION_EXISTS:145:extension exists
X509V3_R_EXTENSION_NAME_ERROR:115:extension name error
X509V3_R_EXTENSION_NOT_FOUND:102:extension not found
X509V3_R_EXTENSION_SETTING_NOT_SUPPORTED:103:extension setting not supported
X509V3_R_EXTENSION_VALUE_ERROR:116:extension value error
X509V3_R_ILLEGAL_EMPTY_EXTENSION:151:illegal empty extension
X509V3_R_INCORRECT_POLICY_SYNTAX_TAG:152:incorrect policy syntax tag
X509V3_R_INVALID_ASNUMBER:162:invalid asnumber
X509V3_R_INVALID_ASRANGE:163:invalid asrange
X509V3_R_INVALID_BOOLEAN_STRING:104:invalid boolean string
X509V3_R_INVALID_EXTENSION_STRING:105:invalid extension string
X509V3_R_INVALID_INHERITANCE:165:invalid inheritance
X509V3_R_INVALID_IPADDRESS:166:invalid ipaddress
X509V3_R_INVALID_MULTIPLE_RDNS:161:invalid multiple rdns
X509V3_R_INVALID_NAME:106:invalid name
X509V3_R_INVALID_NULL_ARGUMENT:107:invalid null argument
X509V3_R_INVALID_NULL_NAME:108:invalid null name
X509V3_R_INVALID_NULL_VALUE:109:invalid null value
X509V3_R_INVALID_NUMBER:140:invalid number
X509V3_R_INVALID_NUMBERS:141:invalid numbers
X509V3_R_INVALID_OBJECT_IDENTIFIER:110:invalid object identifier
X509V3_R_INVALID_OPTION:138:invalid option
X509V3_R_INVALID_POLICY_IDENTIFIER:134:invalid policy identifier
X509V3_R_INVALID_PROXY_POLICY_SETTING:153:invalid proxy policy setting
X509V3_R_INVALID_PURPOSE:146:invalid purpose
X509V3_R_INVALID_SAFI:164:invalid safi
X509V3_R_INVALID_SECTION:135:invalid section
X509V3_R_INVALID_SYNTAX:143:invalid syntax
X509V3_R_ISSUER_DECODE_ERROR:126:issuer decode error
X509V3_R_MISSING_VALUE:124:missing value
X509V3_R_NEED_ORGANIZATION_AND_NUMBERS:142:need organization and numbers
X509V3_R_NO_CONFIG_DATABASE:136:no config database
X509V3_R_NO_ISSUER_CERTIFICATE:121:no issuer certificate
X509V3_R_NO_ISSUER_DETAILS:127:no issuer details
X509V3_R_NO_POLICY_IDENTIFIER:139:no policy identifier
X509V3_R_NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED:154:\
	no proxy cert policy language defined
X509V3_R_NO_PUBLIC_KEY:114:no public key
X509V3_R_NO_SUBJECT_DETAILS:125:no subject details
X509V3_R_OPERATION_NOT_DEFINED:148:operation not defined
X509V3_R_OTHERNAME_ERROR:147:othername error
X509V3_R_POLICY_LANGUAGE_ALREADY_DEFINED:155:policy language already defined
X509V3_R_POLICY_PATH_LENGTH:156:policy path length
X509V3_R_POLICY_PATH_LENGTH_ALREADY_DEFINED:157:\
	policy path length already defined
X509V3_R_POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY:159:\
	policy when proxy language requires no policy
X509V3_R_SECTION_NOT_FOUND:150:section not found
X509V3_R_UNABLE_TO_GET_ISSUER_DETAILS:122:unable to get issuer details
X509V3_R_UNABLE_TO_GET_ISSUER_KEYID:123:unable to get issuer keyid
X509V3_R_UNKNOWN_BIT_STRING_ARGUMENT:111:unknown bit string argument
X509V3_R_UNKNOWN_EXTENSION:129:unknown extension
X509V3_R_UNKNOWN_EXTENSION_NAME:130:unknown extension name
X509V3_R_UNKNOWN_OPTION:120:unknown option
X509V3_R_UNSUPPORTED_OPTION:117:unsupported option
X509V3_R_UNSUPPORTED_TYPE:167:unsupported type
X509V3_R_USER_TOO_LONG:132:user too long
X509_R_AKID_MISMATCH:110:akid mismatch
X509_R_BAD_SELECTOR:133:bad selector
X509_R_BAD_X509_FILETYPE:100:bad x509 filetype
X509_R_BASE64_DECODE_ERROR:118:base64 decode error
X509_R_CANT_CHECK_DH_KEY:114:cant check dh key
X509_R_CERT_ALREADY_IN_HASH_TABLE:101:cert already in hash table
X509_R_CRL_ALREADY_DELTA:127:crl already delta
X509_R_CRL_VERIFY_FAILURE:131:crl verify failure
X509_R_IDP_MISMATCH:128:idp mismatch
X509_R_INVALID_ATTRIBUTES:138:invalid attributes
X509_R_INVALID_DIRECTORY:113:invalid directory
X509_R_INVALID_FIELD_NAME:119:invalid field name
X509_R_INVALID_TRUST:123:invalid trust
X509_R_ISSUER_MISMATCH:129:issuer mismatch
X509_R_KEY_TYPE_MISMATCH:115:key type mismatch
X509_R_KEY_VALUES_MISMATCH:116:key values mismatch
X509_R_LOADING_CERT_DIR:103:loading cert dir
X509_R_LOADING_DEFAULTS:104:loading defaults
X509_R_METHOD_NOT_SUPPORTED:124:method not supported
X509_R_NAME_TOO_LONG:134:name too long
X509_R_NEWER_CRL_NOT_NEWER:132:newer crl not newer
X509_R_NO_CERTIFICATE_FOUND:135:no certificate found
X509_R_NO_CERTIFICATE_OR_CRL_FOUND:136:no certificate or crl found
X509_R_NO_CERT_SET_FOR_US_TO_VERIFY:105:no cert set for us to verify
X509_R_NO_CRL_FOUND:137:no crl found
X509_R_NO_CRL_NUMBER:130:no crl number
X509_R_PUBLIC_KEY_DECODE_ERROR:125:public key decode error
X509_R_PUBLIC_KEY_ENCODE_ERROR:126:public key encode error
X509_R_SHOULD_RETRY:106:should retry
X509_R_UNABLE_TO_FIND_PARAMETERS_IN_CHAIN:107:unable to find parameters in chain
X509_R_UNABLE_TO_GET_CERTS_PUBLIC_KEY:108:unable to get certs public key
X509_R_UNKNOWN_KEY_TYPE:117:unknown key type
X509_R_UNKNOWN_NID:109:unknown nid
X509_R_UNKNOWN_PURPOSE_ID:121:unknown purpose id
X509_R_UNKNOWN_TRUST_ID:120:unknown trust id
X509_R_UNSUPPORTED_ALGORITHM:111:unsupported algorithm
X509_R_WRONG_LOOKUP_TYPE:112:wrong lookup type
X509_R_WRONG_TYPE:122:wrong type
