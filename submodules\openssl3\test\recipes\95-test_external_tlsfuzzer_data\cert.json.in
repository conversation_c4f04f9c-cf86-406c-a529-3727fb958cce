[
    {"server_command": ["@SERVER@", "s_server", "-www",
                 "-key", "tests/serverX509Key.pem",
                 "-cert", "tests/serverX509Cert.pem",
                 "-verify", "1", "-CAfile", "tests/clientX509Cert.pem"],
     "comment": "Use ANY certificate just to ensure that server tries to authorise a client",
     "environment": {"PYTHONPATH" : "."},
     "server_hostname": "localhost",
     "server_port": @PORT@,
     "tests" : [
	 {"name" : "test-tls13-certificate-verify.py",
          "arguments" : ["-k", "tests/clientX509Key.pem",
                         "-c", "tests/clientX509Cert.pem",
			 "-s", "ecdsa_secp256r1_sha256 ecdsa_secp384r1_sha384 ecdsa_secp521r1_sha512 ed25519 ed448 rsa_pss_pss_sha256 rsa_pss_pss_sha384 rsa_pss_pss_sha512 rsa_pss_rsae_sha256 rsa_pss_rsae_sha384 rsa_pss_rsae_sha512 rsa_pkcs1_sha256 rsa_pkcs1_sha384 rsa_pkcs1_sha512 ecdsa_sha224 rsa_pkcs1_sha224",
	                 "-p", "@PORT@"]},
	 {"name" : "test-tls13-ecdsa-in-certificate-verify.py",
          "arguments" : ["-k", "tests/serverECKey.pem",
                         "-c", "tests/serverECCert.pem",
			 "-s", "ecdsa_secp256r1_sha256 ecdsa_secp384r1_sha384 ecdsa_secp521r1_sha512 ed25519 ed448 rsa_pss_pss_sha256 rsa_pss_pss_sha384 rsa_pss_pss_sha512 rsa_pss_rsae_sha256 rsa_pss_rsae_sha384 rsa_pss_rsae_sha512 rsa_pkcs1_sha256 rsa_pkcs1_sha384 rsa_pkcs1_sha512 ecdsa_sha224 rsa_pkcs1_sha224",
	                 "-p", "@PORT@"]}
     ]
    },
    {"server_command": ["@SERVER@", "s_server", "-www",
                 "-key", "tests/serverX509Key.pem",
                 "-cert", "tests/serverX509Cert.pem"],
     "environment": {"PYTHONPATH" : "."},
     "server_hostname": "localhost",
     "server_port": @PORT@,
     "tests" : [
	 {"name" : "test-tls13-conversation.py",
          "arguments" : ["-p", "@PORT@"]},
	 {"name" : "test-conversation.py",
          "arguments" : ["-p", "@PORT@",
		  "-d"]}
     ]
    }

]
