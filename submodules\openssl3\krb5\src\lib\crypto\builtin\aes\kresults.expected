FIPS test:
key:
    00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
input:
    00 11 22 33 44 55 66 77 88 99 AA BB CC DD EE FF
output:
    69 C4 E0 D8 6A 7B 04 30 D8 CD B7 80 70 B4 C5 5A
ok.

ECB tests:
key:
    46 64 31 29 64 86 ED 9C D7 1F C2 07 25 48 20 A2
test 0 - 32 bytes
input:
    C4 A8 5A EB 0B 20 41 49 4F 8B F1 F8 CD 30 F1 13
    94 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
output:
    1B 39 DA 37 40 D3 DF FE AC 89 D6 BB 4C 29 F1 0A
    E1 43 64 CB 16 D3 FF CF E8 FA 6A 2C EC A2 69 34

test 1 - 32 bytes
input:
    22 3C F8 A8 29 95 80 49 57 87 6E 9F A7 11 63 50
    6B 4E 5B 8C 8F A4 DB 1B 95 D3 E8 C5 C5 FB 5A 00
output:
    F3 B2 BB 53 D6 F4 A3 AE 9E EB B1 3D B2 F7 E9 90
    83 FE B6 7B 73 4F CE DB 8E 97 D4 06 96 11 B7 23

test 2 - 32 bytes
input:
    E7 37 52 90 60 E7 10 A9 3E 97 18 DD 3E 29 41 8E
    94 8F E9 20 1F 8D FB 3A 22 CF 22 E8 94 1D 42 7B
output:
    25 4F 90 96 01 9B 09 27 5E FF 95 69 E0 70 DC 50
    A3 D1 6F E1 EF 7B 6D 2F 4F 93 48 90 02 0D F1 8A

test 3 - 48 bytes
input:
    54 94 0B B4 7C 1B 5E BA B2 76 98 F1 9F D9 7F 33
    68 69 54 87 F6 4F C1 19 1E E3 01 B2 00 43 2E 54
    D7 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
output:
    D3 7F 6D 39 57 32 A6 C5 A4 49 F4 4B C6 EE 0A E0
    86 D8 A3 C0 E5 6D BB 39 5F C0 CC 0A DA 8F 87 C6
    14 C1 8E 34 7A A8 2F BB EA 53 F0 7A 64 53 5B 28

test 4 - 48 bytes
input:
    39 09 53 55 67 0E 07 DD A6 F8 7C 7F 78 AF E7 E1
    03 6F D7 53 30 F0 71 14 F1 24 14 34 52 69 0C 8B
    72 5F E0 D9 6D E8 B6 13 E0 32 92 58 E1 7A 39 00
output:
    AD 96 1C 40 05 54 CB 0E 37 32 AE 2C 64 DB EF 8E
    B5 76 2B EE F3 A1 04 A1 E0 3F FE CA 17 7B 4C 91
    53 2F B3 16 33 48 27 D6 49 62 E8 77 10 DC 46 E6

test 5 - 48 bytes
input:
    E5 E9 11 38 19 01 A9 2D F3 CD 42 27 1F AB 33 AB
    1D 93 8B F6 00 73 AC 14 54 DE A6 AC BF 20 E6 A4
    09 F7 DC 23 F8 86 50 EB 53 92 13 73 3D 46 1E 5A
output:
    1A 03 F3 10 7A F0 62 07 D1 22 60 2B 9E 07 D0 8D
    FE 7A E7 E7 DF 7F 12 C6 5E 29 F9 A2 55 C0 93 F1
    FF AC 97 44 E1 C0 C7 39 F8 7A 4B F8 ED 01 58 6B

test 6 - 64 bytes
input:
    D9 A9 50 DA 1D FC EE 71 DA 94 1D 9A B5 03 3E BE
    FA 1B E1 F3 A1 32 DE F4 C4 F1 67 02 38 85 5C 11
    2F AD EB 4C A9 D9 BD 84 6E DA 1E 23 DE 5C E1 D8
    77 C3 CB 18 F5 AA 0D B9 9B 74 BB D3 FA 18 E5 29
output:
    D0 18 22 59 85 05 AB 87 0D 9D D0 99 7B 15 CC 43
    4D C5 13 1B B5 3E 8F 4E B4 75 FC A5 E5 47 94 7F
    14 68 15 F7 6D F1 9E 12 B8 81 39 06 3C 3D F5 44
    83 BE 19 E3 3E 68 15 A0 50 93 03 73 0C 99 52 C3

CBC tests:
initial vector:
    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
test 0 - 32 bytes
input:
    C4 A8 5A EB 0B 20 41 49 4F 8B F1 F8 CD 30 F1 13
    94 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
output:
    1B 39 DA 37 40 D3 DF FE AC 89 D6 BB 4C 29 F1 0A
    D4 3C 74 F5 5B 8B 3E CF 67 F8 F7 00 03 27 8A 91

test 1 - 32 bytes
input:
    22 3C F8 A8 29 95 80 49 57 87 6E 9F A7 11 63 50
    6B 4E 5B 8C 8F A4 DB 1B 95 D3 E8 C5 C5 FB 5A 00
output:
    F3 B2 BB 53 D6 F4 A3 AE 9E EB B1 3D B2 F7 E9 90
    54 03 C8 DF 5F 11 82 94 93 4E B3 4B F9 B5 39 D1

test 2 - 32 bytes
input:
    E7 37 52 90 60 E7 10 A9 3E 97 18 DD 3E 29 41 8E
    94 8F E9 20 1F 8D FB 3A 22 CF 22 E8 94 1D 42 7B
output:
    25 4F 90 96 01 9B 09 27 5E FF 95 69 E0 70 DC 50
    D7 7C 5F B0 DA F7 80 2E 3F 3A 2E B7 5D F0 B3 23

test 3 - 48 bytes
input:
    54 94 0B B4 7C 1B 5E BA B2 76 98 F1 9F D9 7F 33
    68 69 54 87 F6 4F C1 19 1E E3 01 B2 00 43 2E 54
    D7 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
output:
    D3 7F 6D 39 57 32 A6 C5 A4 49 F4 4B C6 EE 0A E0
    88 D0 90 16 44 A7 38 01 63 5D BE 56 9E C3 78 7A
    51 6F 31 69 BF 9C 75 AD A1 C6 66 A6 1B A2 38 0D

test 4 - 48 bytes
input:
    39 09 53 55 67 0E 07 DD A6 F8 7C 7F 78 AF E7 E1
    03 6F D7 53 30 F0 71 14 F1 24 14 34 52 69 0C 8B
    72 5F E0 D9 6D E8 B6 13 E0 32 92 58 E1 7A 39 00
output:
    AD 96 1C 40 05 54 CB 0E 37 32 AE 2C 64 DB EF 8E
    F0 68 8B 66 32 FE 41 EF 11 51 1B 6E F0 C0 17 96
    A1 BD F6 34 5D F3 BC 03 86 72 D0 C3 13 FE C3 95

test 5 - 48 bytes
input:
    E5 E9 11 38 19 01 A9 2D F3 CD 42 27 1F AB 33 AB
    1D 93 8B F6 00 73 AC 14 54 DE A6 AC BF 20 E6 A4
    09 F7 DC 23 F8 86 50 EB 53 92 13 73 3D 46 1E 5A
output:
    1A 03 F3 10 7A F0 62 07 D1 22 60 2B 9E 07 D0 8D
    22 F4 2B 92 92 D4 D5 E7 EA 90 72 9F 03 31 10 1F
    65 DE 01 93 8B 51 17 F8 32 6F 4B 05 AF 02 E2 3D

test 6 - 64 bytes
input:
    D9 A9 50 DA 1D FC EE 71 DA 94 1D 9A B5 03 3E BE
    FA 1B E1 F3 A1 32 DE F4 C4 F1 67 02 38 85 5C 11
    2F AD EB 4C A9 D9 BD 84 6E DA 1E 23 DE 5C E1 D8
    77 C3 CB 18 F5 AA 0D B9 9B 74 BB D3 FA 18 E5 29
output:
    D0 18 22 59 85 05 AB 87 0D 9D D0 99 7B 15 CC 43
    F7 43 1B BF 3C 7D A0 21 1E 3D 3F 6A 4D 8A CE 08
    37 9D EB CD 52 52 3B C5 76 02 7D 35 19 76 05 7D
    76 22 5A 42 DF 73 CB 5D CE 88 C3 4C CE 92 00 E6

CTS tests:
initial vector:
    00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
test 0 - 17 bytes
input:
    C4 A8 5A EB 0B 20 41 49 4F 8B F1 F8 CD 30 F1 13
    94
(did CBC mode for 0)
output:
    D4 3C 74 F5 5B 8B 3E CF 67 F8 F7 00 03 27 8A 91
    1B

test 1 - 31 bytes
input:
    22 3C F8 A8 29 95 80 49 57 87 6E 9F A7 11 63 50
    6B 4E 5B 8C 8F A4 DB 1B 95 D3 E8 C5 C5 FB 5A
(did CBC mode for 0)
output:
    54 03 C8 DF 5F 11 82 94 93 4E B3 4B F9 B5 39 D1
    F3 B2 BB 53 D6 F4 A3 AE 9E EB B1 3D B2 F7 E9

test 2 - 32 bytes
input:
    E7 37 52 90 60 E7 10 A9 3E 97 18 DD 3E 29 41 8E
    94 8F E9 20 1F 8D FB 3A 22 CF 22 E8 94 1D 42 7B
(did CBC mode for 0)
output:
    D7 7C 5F B0 DA F7 80 2E 3F 3A 2E B7 5D F0 B3 23
    25 4F 90 96 01 9B 09 27 5E FF 95 69 E0 70 DC 50

test 3 - 33 bytes
input:
    54 94 0B B4 7C 1B 5E BA B2 76 98 F1 9F D9 7F 33
    68 69 54 87 F6 4F C1 19 1E E3 01 B2 00 43 2E 54
    D7
(did CBC mode for 16)
output:
    D3 7F 6D 39 57 32 A6 C5 A4 49 F4 4B C6 EE 0A E0
    51 6F 31 69 BF 9C 75 AD A1 C6 66 A6 1B A2 38 0D
    88

test 4 - 47 bytes
input:
    39 09 53 55 67 0E 07 DD A6 F8 7C 7F 78 AF E7 E1
    03 6F D7 53 30 F0 71 14 F1 24 14 34 52 69 0C 8B
    72 5F E0 D9 6D E8 B6 13 E0 32 92 58 E1 7A 39
(did CBC mode for 16)
output:
    AD 96 1C 40 05 54 CB 0E 37 32 AE 2C 64 DB EF 8E
    A1 BD F6 34 5D F3 BC 03 86 72 D0 C3 13 FE C3 95
    F0 68 8B 66 32 FE 41 EF 11 51 1B 6E F0 C0 17

test 5 - 48 bytes
input:
    E5 E9 11 38 19 01 A9 2D F3 CD 42 27 1F AB 33 AB
    1D 93 8B F6 00 73 AC 14 54 DE A6 AC BF 20 E6 A4
    09 F7 DC 23 F8 86 50 EB 53 92 13 73 3D 46 1E 5A
(did CBC mode for 16)
output:
    1A 03 F3 10 7A F0 62 07 D1 22 60 2B 9E 07 D0 8D
    65 DE 01 93 8B 51 17 F8 32 6F 4B 05 AF 02 E2 3D
    22 F4 2B 92 92 D4 D5 E7 EA 90 72 9F 03 31 10 1F

test 6 - 64 bytes
input:
    D9 A9 50 DA 1D FC EE 71 DA 94 1D 9A B5 03 3E BE
    FA 1B E1 F3 A1 32 DE F4 C4 F1 67 02 38 85 5C 11
    2F AD EB 4C A9 D9 BD 84 6E DA 1E 23 DE 5C E1 D8
    77 C3 CB 18 F5 AA 0D B9 9B 74 BB D3 FA 18 E5 29
(did CBC mode for 32)
output:
    D0 18 22 59 85 05 AB 87 0D 9D D0 99 7B 15 CC 43
    F7 43 1B BF 3C 7D A0 21 1E 3D 3F 6A 4D 8A CE 08
    76 22 5A 42 DF 73 CB 5D CE 88 C3 4C CE 92 00 E6
    37 9D EB CD 52 52 3B C5 76 02 7D 35 19 76 05 7D

