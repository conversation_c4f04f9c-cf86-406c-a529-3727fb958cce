name: CI
on:
  pull_request: {}
  push:
    branches:
      - main
      - '*.*.x'
    tags:
      - '*.*'
      - '*.*.*'

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

env:
  CARGO_UNSTABLE_SPARSE_REGISTRY: true

jobs:
  linux:
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.10", TOXENV: "flake"}
          - {VERSION: "3.10", TOXENV: "rust"}
          - {VERSION: "3.10", TOXENV: "docs", OPENSSL: {TYPE: "openssl", VERSION: "3.0.5"}}
          - {VERSION: "pypy-3.7", TOXENV: "pypy3-nocoverage"}
          - {VERSION: "pypy-3.8", TOXENV: "pypy3-nocoverage"}
          - {VERSION: "pypy-3.9", TOXENV: "pypy3-nocoverage"}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "openssl", VERSION: "1.1.0l"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1q"}}
          - {VERSION: "3.10", TOXENV: "py310-ssh", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1q"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1q", CONFIG_FLAGS: "no-engine no-rc2 no-srtp no-ct no-psk"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "openssl", VERSION: "3.0.5"}}
          - {VERSION: "3.10", TOXENV: "py310", TOXARGS: "--enable-fips=1", OPENSSL: {TYPE: "openssl", CONFIG_FLAGS: "enable-fips", VERSION: "3.0.5"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "libressl", VERSION: "3.1.5"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "libressl", VERSION: "3.2.7"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "libressl", VERSION: "3.3.6"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "libressl", VERSION: "3.4.3"}}
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "libressl", VERSION: "3.5.3"}}
          - {VERSION: "3.11-dev", TOXENV: "py311"}
          - {VERSION: "3.10", TOXENV: "py310-randomorder"}
          # Latest commit on the master branch, as of Sep 02, 2022.
          - {VERSION: "3.10", TOXENV: "py310", OPENSSL: {TYPE: "boringssl", VERSION: "8462a367bb57e9524c3d8eca9c62733c63a63cf4"}}
    name: "${{ matrix.PYTHON.TOXENV }} ${{ matrix.PYTHON.OPENSSL.TYPE }} ${{ matrix.PYTHON.OPENSSL.VERSION }} ${{ matrix.PYTHON.TOXARGS }} ${{ matrix.PYTHON.OPENSSL.CONFIG_FLAGS }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - name: Setup python
        id: setup-python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/.cache/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.PYTHON.VERSION }}-${{ steps.setup-python.outputs.python-version }}-cargo-4-${{ hashFiles('**/Cargo.lock') }}

      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"
      - run: python -m pip install tox requests coverage[toml]
      - name: Compute config hash and set config vars
        run: |
          DEFAULT_CONFIG_FLAGS="shared no-ssl2 no-ssl3"
          CONFIG_FLAGS="$DEFAULT_CONFIG_FLAGS $CONFIG_FLAGS"
          CONFIG_HASH=$(echo "$CONFIG_FLAGS" | sha1sum | sed 's/ .*$//')
          echo "CONFIG_FLAGS=${CONFIG_FLAGS}" >> $GITHUB_ENV
          echo "CONFIG_HASH=${CONFIG_HASH}" >> $GITHUB_ENV
          echo "OSSL_INFO=${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${CONFIG_FLAGS}" >> $GITHUB_ENV
          echo "OSSL_PATH=${{ github.workspace }}/osslcache/${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${CONFIG_HASH}" >> $GITHUB_ENV
        env:
          CONFIG_FLAGS: ${{ matrix.PYTHON.OPENSSL.CONFIG_FLAGS }}
        if: matrix.PYTHON.OPENSSL
      - name: Load cache
        uses: actions/cache@v3.0.8
        id: ossl-cache
        timeout-minutes: 5
        with:
          path: ${{ github.workspace }}/osslcache
          # When altering the openssl build process you may need to increment the value on the end of this cache key
          # so that you can prevent it from fetching the cache and skipping the build step.
          key: ${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${{ env.CONFIG_HASH }}-3
        if: matrix.PYTHON.OPENSSL
      - name: Build custom OpenSSL/LibreSSL
        run: .github/workflows/build_openssl.sh
        env:
          TYPE: ${{ matrix.PYTHON.OPENSSL.TYPE }}
          VERSION: ${{ matrix.PYTHON.OPENSSL.VERSION }}
        if: matrix.PYTHON.OPENSSL && steps.ossl-cache.outputs.cache-hit != 'true'
      - name: Set CFLAGS/LDFLAGS
        run: |
          echo "CFLAGS=${CFLAGS} -Werror=implicit-function-declaration -I${OSSL_PATH}/include" >> $GITHUB_ENV
          echo "LDFLAGS=${LDFLAGS} -L${OSSL_PATH}/lib -L${OSSL_PATH}/lib64 -Wl,-rpath=${OSSL_PATH}/lib -Wl,-rpath=${OSSL_PATH}/lib64" >> $GITHUB_ENV
        if: matrix.PYTHON.OPENSSL
      - name: Tests
        run: |
            tox -vvv -r --  --color=yes --wycheproof-root=wycheproof ${{ matrix.PYTHON.TOXARGS }}
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}

      - uses: ./.github/actions/upload-coverage

  linux-distros:
    runs-on: ubuntu-latest
    container: ghcr.io/pyca/cryptography-runner-${{ matrix.IMAGE.IMAGE }}
    strategy:
      fail-fast: false
      matrix:
        IMAGE:
          - {IMAGE: "rhel8", TOXENV: "py36"}
          - {IMAGE: "rhel8-fips", TOXENV: "py36", FIPS: true}
          - {IMAGE: "rhel8", TOXENV: "py38"}
          - {IMAGE: "rhel8-fips", TOXENV: "py38", FIPS: true}
          - {IMAGE: "buster", TOXENV: "py37"}
          - {IMAGE: "bullseye", TOXENV: "py39"}
          - {IMAGE: "bookworm", TOXENV: "py310"}
          - {IMAGE: "sid", TOXENV: "py310"}
          - {IMAGE: "ubuntu-bionic", TOXENV: "py36"}
          - {IMAGE: "ubuntu-focal", TOXENV: "py38"}
          - {IMAGE: "ubuntu-jammy", TOXENV: "py310"}
          - {IMAGE: "ubuntu-rolling", TOXENV: "py310"}
          - {IMAGE: "fedora", TOXENV: "py311"}
          - {IMAGE: "alpine", TOXENV: "py310"}
    name: "${{ matrix.IMAGE.TOXENV }} on ${{ matrix.IMAGE.IMAGE }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/.cache/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.IMAGE.IMAGE }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}

      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"
      # When run in a docker container the home directory doesn't have the same owner as the
      # apparent user so pip refuses to create a cache dir
      - name: create pip cache dir
        run: mkdir -p "${HOME}/.cache/pip"
      - run: |
          echo "OPENSSL_FORCE_FIPS_MODE=1" >> $GITHUB_ENV
          echo "CFLAGS=-DUSE_OSRANDOM_RNG_FOR_TESTING" >> $GITHUB_ENV
        if: matrix.IMAGE.FIPS
      - run: '/venv/bin/tox -vvv -- --wycheproof-root="wycheproof"'
        env:
          TOXENV: ${{ matrix.IMAGE.TOXENV }}
          RUSTUP_HOME: /root/.rustup
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - uses: ./.github/actions/upload-coverage

  linux-rust:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.9", TOXENV: "py39"}
        RUST:
          # Cover MSRV (and likely next MSRV). In-dev versions are below in
          # the linux-rust-coverage section. Once our MSRV is 1.60 we can
          # remove this section entirely.
          - 1.48.0
          - 1.49.0
    name: "${{ matrix.PYTHON.TOXENV }} with Rust ${{ matrix.RUST }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/.cache/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}-${{ matrix.RUST }}

      - name: Setup python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: dtolnay/rust-toolchain@1ce4a7352a1efe5dede2e52c75512b34256e4f44
        with:
          toolchain: ${{ matrix.RUST }}
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"
      - run: python -m pip install tox coverage[toml]
      - name: Tests
        run: |
            tox -vvv -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - uses: ./.github/actions/upload-coverage

  linux-rust-coverage:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        PYTHON:
          - {VERSION: "3.10", TOXENV: "py310"}
        RUST:
          - beta
          - nightly
    name: "Rust Coverage"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        id: cargo-cache
        timeout-minutes: 5
        with:
          path: |
            ~/.cache/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}-rust-${{ matrix.RUST }}-coverage

      - name: Setup python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: dtolnay/rust-toolchain@1ce4a7352a1efe5dede2e52c75512b34256e4f44
        with:
          toolchain: ${{ matrix.RUST }}
          components: llvm-tools-preview
      - run: cargo install cargo-binutils
        if: steps.cargo-cache.outputs.cache-hit != 'true'

      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"
      - run: python -m pip install tox coverage[toml]
      - name: Tests
        run: |
            tox -vvv -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
          RUSTFLAGS: "-Cinstrument-coverage"
          LLVM_PROFILE_FILE: "rust-cov/cov-%p.profraw"
      - name: Rust Tests
        run: |
            cd src/rust
            cargo test --no-default-features
        env:
          RUSTFLAGS: "-Cinstrument-coverage"
          LLVM_PROFILE_FILE: "rust-cov/cov-%m-%p.profraw"
      - name: Process coverage data
        run: |
            set -xe
            cd src/rust/
            cargo profdata -- merge -sparse ../../rust-cov/*.profraw -o pytest-rust-cov.profdata
            cargo profdata -- merge -sparse rust-cov/*.profraw -o cargo-test-rust-cov.profdata
            COV_UUID=$(python3 -c "import uuid; print(uuid.uuid4())")

            cargo cov -- export \
              ../../.tox/${{ matrix.PYTHON.TOXENV }}/lib/python${{ matrix.PYTHON.VERSION }}/site-packages/cryptography/hazmat/bindings/_rust.abi3.so \
              -instr-profile=pytest-rust-cov.profdata \
              --ignore-filename-regex='/.cargo/registry' \
              --ignore-filename-regex='/rustc/' \
              --ignore-filename-regex='/.rustup/toolchains/' --format=lcov > ../../${COV_UUID}-1.lcov
            cargo cov -- export \
              $(env RUSTFLAGS="-Cinstrument-coverage" cargo test --no-default-features --tests --no-run --message-format=json | jq -r "select(.profile.test == true) | .filenames[]") \
              -instr-profile=cargo-test-rust-cov.profdata \
              --ignore-filename-regex='/.cargo/registry' \
              --ignore-filename-regex='/rustc/' \
              --ignore-filename-regex='/.rustup/toolchains/' --format=lcov > ../../${COV_UUID}-2.lcov

            sed -E -i 's/SF:(.*)\/src\/rust\/(.*)/SF:src\/rust\/\2/g' ../../*.lcov
      - uses: ./.github/actions/upload-coverage

  macos:
    runs-on: macos-12
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.6", TOXENV: "py36", EXTRA_CFLAGS: ""}
          - {VERSION: "3.10", TOXENV: "py310", EXTRA_CFLAGS: "-DUSE_OSRANDOM_RNG_FOR_TESTING"}
    name: "${{ matrix.PYTHON.TOXENV }} on macOS"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/Library/Caches/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.PYTHON.VERSION }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}

      - run: python -m pip install tox requests coverage[toml]

      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"

      - name: Download OpenSSL
        run: |
          python .github/workflows/download_openssl.py macos openssl-macos-universal2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Tests
        run: |
          CRYPTOGRAPHY_SUPPRESS_LINK_FLAGS=1 \
            LDFLAGS="${HOME}/openssl-macos-universal2/lib/libcrypto.a ${HOME}/openssl-macos-universal2/lib/libssl.a" \
            CFLAGS="-I${HOME}/openssl-macos-universal2/include -Werror -Wno-error=deprecated-declarations -Wno-error=incompatible-pointer-types-discards-qualifiers -Wno-error=unused-function -mmacosx-version-min=10.10 -march=core2 $EXTRA_CFLAGS" \
            tox -vvv -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          EXTRA_CFLAGS: ${{ matrix.PYTHON.EXTRA_CFLAGS }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}

      - uses: ./.github/actions/upload-coverage

  windows:
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        WINDOWS:
          - {ARCH: 'x86', WINDOWS: 'win32'}
          - {ARCH: 'x64', WINDOWS: 'win64'}
        PYTHON:
          - {VERSION: "3.6", TOXENV: "py36-nocoverage", CL_FLAGS: ""}
          - {VERSION: "3.10", TOXENV: "py310", CL_FLAGS: "/D USE_OSRANDOM_RNG_FOR_TESTING"}
        JOB_NUMBER: [0, 1, 2]
    name: "${{ matrix.PYTHON.TOXENV }} on ${{ matrix.WINDOWS.WINDOWS }} (part ${{ matrix.JOB_NUMBER }})"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - name: Setup python
        id: setup-python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
          architecture: ${{ matrix.WINDOWS.ARCH }}
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/AppData/Local/pip/Cache/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.WINDOWS.ARCH }}-${{ steps.setup-python.outputs.python-version }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}

      - run: python -m pip install tox requests coverage[toml]
      - name: Download OpenSSL
        run: |
            python .github/workflows/download_openssl.py windows openssl-${{ matrix.WINDOWS.WINDOWS }}
            echo "INCLUDE=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}/include;$INCLUDE" >> $GITHUB_ENV
            echo "LIB=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}/lib;$LIB" >> $GITHUB_ENV
            echo "CL=${{ matrix.PYTHON.CL_FLAGS }}" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"

      - run: tox -vvv -r -- --color=yes --wycheproof-root=wycheproof --num-shards=3 --shard-id=${{ matrix.JOB_NUMBER }}
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}

      - uses: ./.github/actions/upload-coverage

  linux-downstream:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        DOWNSTREAM:
          - paramiko
          - pyopenssl
          - pyopenssl-release
          - twisted
          - aws-encryption-sdk
          - dynamodb-encryption-sdk
          - certbot
          - certbot-josepy
          - mitmproxy
        PYTHON:
          - 3.9
    name: "Downstream tests for ${{ matrix.DOWNSTREAM }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        timeout-minutes: 5
        with:
          path: |
            ~/.cache/pip/
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/registry/src/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-3-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: ${{ matrix.PYTHON }}
      - run: ./.github/downstream.d/${{ matrix.DOWNSTREAM }}.sh install
      - run: pip install .
        env:
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      # cryptography main has a version of "(X+1).0.0.dev1" where X is the
      # most recently released major version. A package used by a downstream
      # may depend on cryptography <=X. If you use entrypoints stuff, this can
      # lead to runtime errors due to version incompatibilities. Rename the
      # dist-info directory to pretend to be an older version to "solve" this.
      - run: |
          import json
          import pkg_resources
          import shutil
          import urllib.request

          d = pkg_resources.get_distribution("cryptography")
          with urllib.request.urlopen("https://pypi.org/pypi/cryptography/json") as r:
              latest_version = json.load(r)["info"]["version"]
          new_path = d.egg_info.replace(d.version, latest_version)
          shutil.move(d.egg_info, new_path)
        shell: python
      - run: ./.github/downstream.d/${{ matrix.DOWNSTREAM }}.sh run

  docs-linkcheck:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    name: "linkcheck"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v3.0.2
        with:
          persist-credentials: false
      - name: Setup python
        uses: actions/setup-python@v4.2.0
        with:
          python-version: 3.9
      - run: python -m pip install -U tox
      - run: tox -r --  --color=yes
        env:
          TOXENV: docs-linkcheck

  all-green:
    # https://github.community/t/is-it-possible-to-require-all-github-actions-tasks-to-pass-without-enumerating-them/117957/4?u=graingert
    runs-on: ubuntu-latest
    needs: [linux, linux-distros, linux-rust, linux-rust-coverage, macos, windows, linux-downstream]
    if: ${{ always() }}
    steps:
      - uses: actions/checkout@v3.0.2
        timeout-minutes: 3
        with:
          persist-credentials: false
      - run: |
          echo "# 😢 😢" >> $GITHUB_STEP_SUMMARY
          echo "At least one CI job failed."
          exit 1
        if: ${{ needs.linux.result != 'success' || needs.linux-distros.result != 'success' || needs.linux-rust.result != 'success' || needs.linux-rust-coverage.result != 'success' || needs.macos.result != 'success' || needs.windows.result != 'success' || needs.linux-downstream.result != 'success' }}
      - run: echo "# 🎉 🎉" >> $GITHUB_STEP_SUMMARY
      - name: Setup python
        if: ${{ always() }}
        uses: actions/setup-python@v4.2.0
        with:
          python-version: '3.10'
      - run: pip install coverage[toml]
        if: ${{ always() }}
      - name: Download coverage data
        if: ${{ always() }}
        uses: actions/download-artifact@v3.0.0
        with:
          name: coverage-data
      - name: Combine coverage and fail if it's <100%.
        if: ${{ always() }}
        id: combinecoverage
        run: |
          set +e
          python -m coverage combine
          echo "## Python Coverage" >> $GITHUB_STEP_SUMMARY
          python -m coverage report -m --fail-under=100 > COV_REPORT
          COV_EXIT_CODE=$?
          cat COV_REPORT
          if [ $COV_EXIT_CODE -ne 0 ]; then
            echo "🚨 Python Coverage failed. Under 100" | tee -a $GITHUB_STEP_SUMMARY
          fi
          echo '```' >> $GITHUB_STEP_SUMMARY
          cat COV_REPORT >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          exit $COV_EXIT_CODE
      - name: Combine rust coverage and fail if it's <100%.
        if: ${{ always() }}
        id: combinerustcoverage
        run: |
          set +e
          sudo apt-get install -y lcov
          RUST_COVERAGE_OUTPUT=$(lcov $(for f in *.lcov; do echo --add-tracefile "$f"; done) -o combined.lcov | grep lines)
          echo "## Rust Coverage" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo $RUST_COVERAGE_OUTPUT >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          if ! echo "$RUST_COVERAGE_OUTPUT" | grep "100.0%"; then
            echo "🚨 Rust Coverage failed. Under 100" | tee -a $GITHUB_STEP_SUMMARY
            exit 1
          fi
      - name: Create rust coverage HTML
        run: genhtml combined.lcov -o rust-coverage
        if: ${{ failure() && steps.combinerustcoverage.outcome == 'failure' }}
      - name: Create coverage HTML
        run: python -m coverage html
        if: ${{ failure() && steps.combinecoverage.outcome == 'failure' }}
      - name: Upload HTML report.
        uses: actions/upload-artifact@v3.1.0
        with:
          name: _html-report
          path: htmlcov
          if-no-files-found: ignore
        if: ${{ failure() && steps.combinecoverage.outcome == 'failure' }}
      - name: Upload rust HTML report.
        uses: actions/upload-artifact@v3.1.0
        with:
          name: _html-rust-report
          path: rust-coverage
          if-no-files-found: ignore
        if: ${{ failure() && steps.combinerustcoverage.outcome == 'failure' }}
