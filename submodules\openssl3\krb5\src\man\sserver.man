.\" Man page generated from reStructuredText.
.
.TH "SSERVER" "8" " " "1.20" "MIT Kerberos"
.SH NAME
sserver \- sample Kerberos version 5 server
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBsserver\fP
[ \fB\-p\fP \fIport\fP ]
[ \fB\-S\fP \fIkeytab\fP ]
[ \fIserver_port\fP ]
.SH DESCRIPTION
.sp
sserver and sclient(1) are a simple demonstration client/server
application.  When sclient connects to sserver, it performs a Kerberos
authentication, and then sserver returns to sclient the Kerberos
principal which was used for the Kerberos authentication.  It makes a
good test that Kerberos has been successfully installed on a machine.
.sp
The service name used by sserver and sclient is sample.  Hence,
sserver will require that there be a keytab entry for the service
\fBsample/<EMAIL>\fP\&.  This keytab is generated
using the kadmin(1) program.  The keytab file is usually
installed as \fB@KTNAME@\fP\&.
.sp
The \fB\-S\fP option allows for a different keytab than the default.
.sp
sserver is normally invoked out of inetd(8), using a line in
\fB/etc/inetd.conf\fP that looks like this:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
sample stream tcp nowait root /usr/local/sbin/sserver sserver
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
Since \fBsample\fP is normally not a port defined in \fB/etc/services\fP,
you will usually have to add a line to \fB/etc/services\fP which looks
like this:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
sample          13135/tcp
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
When using sclient, you will first have to have an entry in the
Kerberos database, by using kadmin(1), and then you have to get
Kerberos tickets, by using kinit(1)\&.  Also, if you are running
the sclient program on a different host than the sserver it will be
connecting to, be sure that both hosts have an entry in /etc/services
for the sample tcp port, and that the same port number is in both
files.
.sp
When you run sclient you should see something like this:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
sendauth succeeded, reply is:
reply len 32, contents:
<NAME_EMAIL>
.ft P
.fi
.UNINDENT
.UNINDENT
.SH COMMON ERROR MESSAGES
.INDENT 0.0
.IP 1. 3
kinit returns the error:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
kinit: Client not found in Kerberos database while getting
       initial credentials
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This means that you didn\(aqt create an entry for your username in the
Kerberos database.
.IP 2. 3
sclient returns the error:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
unknown service sample/tcp; check /etc/services
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This means that you don\(aqt have an entry in /etc/services for the
sample tcp port.
.IP 3. 3
sclient returns the error:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
connect: Connection refused
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This probably means you didn\(aqt edit /etc/inetd.conf correctly, or
you didn\(aqt restart inetd after editing inetd.conf.
.IP 4. 3
sclient returns the error:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
sclient: Server not found in Kerberos database while using
         sendauth
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This means that the \fBsample/<EMAIL>\fP service was not
defined in the Kerberos database; it should be created using
kadmin(1), and a keytab file needs to be generated to make
the key for that service principal available for sclient.
.IP 5. 3
sclient returns the error:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
sendauth rejected, error reply is:
    "No such file or directory"
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This probably means sserver couldn\(aqt find the keytab file.  It was
probably not installed in the proper directory.
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
sclient(1), kerberos(7), services(5), inetd(8)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
