testdata = [
    "//testvectors:all",
]

# java

java_library(
    name = "utils",
    srcs = [
        "java/com/google/security/wycheproof/EcUtil.java",
        "java/com/google/security/wycheproof/JsonUtil.java",
        "java/com/google/security/wycheproof/RandomUtil.java",
        "java/com/google/security/wycheproof/TestUtil.java",
    ],
    deps = [
        "@com_google_code_gson",
    ],
)

common_deps = [
    ":utils",
    "@com_google_code_gson",
    "@junit",
]

test_srcs = glob([
    "java/**/WycheproofRunner.java",
    "java/**/testcases/*Test.java",
])

# Bouncy Castle tests
load("//tools:build_defs.bzl", "bouncycastle_all_tests", "bouncycastle_tests")

# Generates BouncyCastleAllTests_1_xx target for all available versions,
# plus a BouncyCastleAllTests alias for latest stable.
#
# To test latest stable:
# $ bazel test BouncyCastleAllTests
#
# To test other versions, e.g., v1.52:
# $ bazel test BouncyCastleAllTests_1_52
#
# To test all known versions (warning, will take a long time):
# $ bazel test BouncyCastleAllTest_*
bouncycastle_all_tests(
    # This test takes a long time, because key generation for DSA and DH generate new parameters.
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/BouncyCastleAllTests.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.BouncyCastleAllTests",
    deps = common_deps,
    data = testdata,
)

java_test(
    name = "BouncyCastleAllTestsLocal",
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/BouncyCastleAllTests.java"] + test_srcs,
    # this target requires specifing a shell variable, thus won't work with the wildcard target patterns.
    # with tags=["manual"] it'll be excluded from said patterns.
    tags = ["manual"],
    test_class = "com.google.security.wycheproof.BouncyCastleAllTests",
    deps = common_deps + ["@local//:bouncycastle_jar"],
    data = testdata,
)

# Generates BouncyCastleTest_1_xx target for all available versions,
# plus a BouncyCastleTest alias for latest stable.
#
# To test latest stable:
# $ bazel test BouncyCastleTest
#
# To test other versions, e.g., v1.52:
# $ bazel test BouncyCastleTest_1_52
#
# To test all known versions:
# $ bazel test BouncyCastleTest_*
bouncycastle_tests(
    size = "large",
    srcs = ["java/com/google/security/wycheproof/BouncyCastleTest.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.BouncyCastleTest",
    deps = common_deps,
    data = testdata,
)

java_test(
    name = "BouncyCastleTestLocal",
    size = "large",
    srcs = ["java/com/google/security/wycheproof/BouncyCastleTest.java"] + test_srcs,
    # this target requires specifing a shell variable, thus won't work with the wildcard target patterns.
    # with tags=["manual"] it'll be excluded from said patterns.
    tags = ["manual"],
    test_class = "com.google.security.wycheproof.BouncyCastleTest",
    deps = common_deps + ["@local//:bouncycastle_jar"],
    data = testdata,
)

# Spongy Castle tests
load("//tools:build_defs.bzl", "spongycastle_all_tests", "spongycastle_tests")

# Generates SpongyCastleAllTests_1_xx target for all available versions,
# plus a SpongyCastleAllTests alias for latest stable.
#
# To test latest stable:
# $ bazel test SpongyCastleAllTests
#
# To test other versions, e.g., v1.52.0.0:
# $ bazel test SpongyCastleAllTests_1_52
#
# To test all known versions (warning, will take a long time):
# $ bazel test SpongyCastleAllTests_*
spongycastle_all_tests(
    # This test takes a long time, because key generation for DSA and DH generate new parameters.
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/SpongyCastleAllTests.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.SpongyCastleAllTests",
    deps = common_deps,
    data = testdata,
)

# Generates SpongyCastleTest_1_xx target for all available versions,
# plus a SpongyCastleTest alias for latest stable.
#
# To test latest stable:
# $ bazel test SpongyCastleTest
#
# To test other versions, e.g., v1.52.0.0:
# $ bazel test SpongyCastleTest_1_52
#
# To test all known versions:
# $ bazel test SpongyCastleTest_*
spongycastle_tests(
    size = "large",
    srcs = ["java/com/google/security/wycheproof/SpongyCastleTest.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.SpongyCastleTest",
    deps = common_deps,
    data = testdata,
)

# Conscrypt tests
load("//tools:build_defs.bzl", "conscrypt_tests", "conscrypt_all_tests")

conscrypt_tests(
    size = "large",
    srcs = ["java/com/google/security/wycheproof/ConscryptTest.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.ConscryptTest",
    deps = common_deps,
    data = testdata,
)

conscrypt_all_tests(
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/ConscryptAllTests.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.ConscryptAllTests",
    deps = common_deps,
    data = testdata,
)

# Amazon Corretto Crypto Provider tests
load("//tools:build_defs.bzl", "accp_tests", "accp_all_tests")

accp_tests(
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/AccpTest.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.AccpTest",
    deps = common_deps,
    data = testdata,
)

java_test(
    name = "AccpTestLocal",
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/AccpTest.java"] + test_srcs,
    # this target requires specifing a shell variable, thus won't work with the wildcard target patterns.
    # with tags=["manual"] it'll be excluded from said patterns.
    tags = ["manual"],
    test_class = "com.google.security.wycheproof.AccpTest",
    deps = common_deps + ["@local//:accp_jar"],
    data = testdata,
)

accp_all_tests(
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/AccpAllTests.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.AccpAllTests",
    deps = common_deps,
    data = testdata,
)

java_test(
    name = "AccpAllTestsLocal",
    size = "enormous",
    srcs = ["java/com/google/security/wycheproof/AccpAllTests.java"] + test_srcs,
    # this target requires specifing a shell variable, thus won't work with the wildcard target patterns.
    # with tags=["manual"] it'll be excluded from said patterns.
    tags = ["manual"],
    test_class = "com.google.security.wycheproof.AccpAllTests",
    deps = common_deps + ["@local//:accp_jar"],
    data = testdata,
)

# OpenJDK tests
java_test(
    name = "OpenJDKTest",
    size = "large",
    srcs = ["java/com/google/security/wycheproof/OpenJDKTest.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.OpenJDKTest",
    deps = common_deps,
    data = testdata,
)

java_test(
    name = "OpenJDKAllTests",
    size = "large",
    srcs = ["java/com/google/security/wycheproof/OpenJDKAllTests.java"] + test_srcs,
    test_class = "com.google.security.wycheproof.OpenJDKAllTests",
    deps = common_deps,
    data = testdata,
)

# Platform-independent tests
java_test(
    name = "ProviderIndependentTest",
    size = "small",
    srcs = ["java/com/google/security/wycheproof/ProviderIndependentTest.java"] + test_srcs,
    deps = common_deps,
    data = testdata,
)

# WebCrypto Javascript tests
load("@io_bazel_rules_closure//closure:defs.bzl", "closure_js_deps")

closure_js_deps(
    name = "E2EDeps",
    deps = ["@google_e2e//:e2e"],
)
