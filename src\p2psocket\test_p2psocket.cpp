#include <gtest/gtest.h>
#include "p2psocket.h"
#include "common_defines.h"
#include <thread>
#include <chrono>

class P2PSocketTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up default socket options
        memset(&options_, 0, sizeof(options_));
        options_.mode = MODE_CLIENT;
        options_.type = SOCKET_QUIC;
        options_.log_level = LOG_INFO;
    }

    void TearDown() override {
        if (socket_ != NULL) {
            P2pClose(socket_);
            socket_ = NULL;
        }
    }

    SocketOptions options_;
    P2P_SOCKET socket_ = NULL;
};

// Test socket creation
TEST_F(P2PSocketTest, CreateSocket) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr) << "Failed to create P2P socket";
}

// Test socket bind operation
TEST_F(P2PSocketTest, BindSocket) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);
    
    const char* ipaddr = "127.0.0.1";
    int port = 12345;
    
    int result = P2pBind(socket_, ipaddr, port);
    ASSERT_EQ(result, 0) << "Failed to bind socket to " << ipaddr << ":" << port;
}

// Test socket connect operation
TEST_F(P2PSocketTest, ConnectSocket) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);

    const char* ipaddr = "127.0.0.1";
    int port = 12345;

    // Try to connect (expected to fail since no server is running)
    int result = P2pConnect(socket_, ipaddr, port);
    EXPECT_NE(result, 0) << "Connect unexpectedly succeeded without server";
}

// Test socket write and read operations
TEST_F(P2PSocketTest, WriteAndRead) {
    // Create server socket
    SocketOptions server_options = options_;
    server_options.mode = MODE_SERVER;
    P2P_SOCKET server_socket = P2pCreate(&server_options);
    ASSERT_NE(server_socket, nullptr);

    const char* ipaddr = "127.0.0.1";
    int port = 12346;

    // Bind server socket
    ASSERT_EQ(P2pBind(server_socket, ipaddr, port), 0);
    ASSERT_EQ(P2pListen(server_socket), 0);

    // Create client socket
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);

    // Run server accept in a separate thread
    std::thread accept_thread([&]() {
        char client_ip[128];
        int client_port;
        P2P_SOCKET client_socket = P2pAccept(server_socket, client_ip, sizeof(client_ip), &client_port);
        EXPECT_NE(client_socket, nullptr);
        
        // Clean up client socket after test
        if (client_socket) {
            P2pClose(client_socket);
        }
    });

    // Connect from client
    ASSERT_EQ(P2pConnect(socket_, ipaddr, port), 0);

    // Test write
    const char* test_data = "Hello P2P!";
    int write_result = P2pWrite(socket_, test_data, strlen(test_data));
    EXPECT_EQ(write_result, strlen(test_data));

    // Clean up
    accept_thread.join();
    P2pClose(server_socket);
}

// Test connection timeout setting
TEST_F(P2PSocketTest, ConnectionTimeout) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);

    // Set connection timeout to 1 second
    int timeout = 1000; // milliseconds
    int result = P2pSetConnTimeout(socket_, timeout);
    ASSERT_EQ(result, 0) << "Failed to set connection timeout";

    // Try to connect to non-existent server (should timeout quickly)
    auto start = std::chrono::steady_clock::now();
    result = P2pConnect(socket_, "*********", 12345); // Using TEST-NET-1 IP address
    auto end = std::chrono::steady_clock::now();
    
    EXPECT_NE(result, 0) << "Connect should have failed";
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    EXPECT_GE(duration, timeout) << "Connection attempt returned before timeout";
    EXPECT_LE(duration, timeout * 2) << "Connection attempt took too long";
}

// Test stream creation and management
TEST_F(P2PSocketTest, StreamOperations) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);

    // Create a stream
    StreamOptions stream_options = {0};
    P2P_STREAM stream = P2pStreamCreate(socket_, &stream_options);
    ASSERT_NE(stream, nullptr) << "Failed to create stream";

    // Test stream state
    int state = P2pStreamGetState(stream);
    EXPECT_NE(state, -1) << "Failed to get stream state";

    // Test writing to stream (expected to fail since not connected)
    const char* test_data = "Test Stream Data";
    int write_result = P2pStreamWrite(stream, test_data, strlen(test_data));
    EXPECT_NE(write_result, strlen(test_data)) << "Stream write should have failed when not connected";

    // Clean up
    EXPECT_EQ(P2pStreamClose(stream), 0) << "Failed to close stream";
}

// Test polling functionality
TEST_F(P2PSocketTest, PollingOperations) {
    socket_ = P2pCreate(&options_);
    ASSERT_NE(socket_, nullptr);

    PollEvent events[1];
    int timeout = 100; // milliseconds

    // Poll with timeout (should timeout since no activity)
    int result = P2pPoll(socket_, events, timeout);
    EXPECT_EQ(result, 0) << "Poll should timeout with no events";
}

int main(int argc, char **argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
