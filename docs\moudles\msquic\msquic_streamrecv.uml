<?xml version="1.0" encoding="UTF-8"?>
<XPD:PROJECT xmlns:XPD="http://www.staruml.com" version="1">
<XPD:HEADER>
<XPD:SUBUNITS>
</XPD:SUBUNITS>
<XPD:PROFILES>
<XPD:PROFILE>UMLStandard</XPD:PROFILE>
</XPD:PROFILES>
</XPD:HEADER>
<XPD:BODY>
<XPD:OBJ name="DocumentElement" type="UMLProject" guid="eMTM5siBPkmesgn1ExGJ4wAA">
<XPD:ATTR name="Title" type="string">Untitled</XPD:ATTR>
<XPD:ATTR name="#OwnedElements" type="integer">5</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLModel" guid="Y8LpwWcNukWtaRfUBI5ZLgAA">
<XPD:ATTR name="Name" type="string">Use Case Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">useCaseModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLUseCaseDiagram" guid="01F2MJANekqll84kbF5YNQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">Y8LpwWcNukWtaRfUBI5ZLgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLUseCaseDiagramView" guid="Wa78Fn/Bb0SrcCepzNe+hwAA">
<XPD:REF name="Diagram">01F2MJANekqll84kbF5YNQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLModel" guid="IFqLwgQrJUOrRvknlfHP7gAA">
<XPD:ATTR name="Name" type="string">Analysis Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">analysisModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="qf/BpIoQNE2iR+jW/1YKGQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:ATTR name="DiagramType" type="string">RobustnessDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="Nj8POkxtUk2Ybm9uGd6QNwAA">
<XPD:REF name="Diagram">qf/BpIoQNE2iR+jW/1YKGQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedCollaborationInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedCollaborationInstanceSets[0]" type="UMLCollaborationInstanceSet" guid="RJjbkGXgxkyHu7/7jQk7WgAA">
<XPD:ATTR name="Name" type="string">CollaborationInstanceSet1</XPD:ATTR>
<XPD:REF name="RepresentedClassifier">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:ATTR name="#InteractionInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="InteractionInstanceSets[0]" type="UMLInteractionInstanceSet" guid="z5Jdy9Y9AkuwxNfYTGq/jgAA">
<XPD:ATTR name="Name" type="string">InteractionInstanceSet1</XPD:ATTR>
<XPD:REF name="Context">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLSequenceDiagram" guid="PrarycmgpEmAgPsPTXgTdAAA">
<XPD:ATTR name="Name" type="string">SequenceDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLSequenceDiagramView" guid="o16wrrjNA0qa+l2Fh/S4lgAA">
<XPD:REF name="Diagram">PrarycmgpEmAgPsPTXgTdAAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">20</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLSeqObjectView" guid="hPmViqUBXkyp20XJo5hMdgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">164</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1283</XPD:ATTR>
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="fAggghDBvEq/ulCEeW7p1wAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="J8Tym9syak6jMb998m2XnAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">app</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="7tw+uaP9y0KlpsxVOPDIWwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="L08Ynzs4RUePB4dOSv8aiQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="+2RX1PWkxkmtGMjAQcMkiwAA">
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[1]" type="UMLSeqObjectView" guid="QJ/ol6Ii20Cm5p+rCS0bnQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">408</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">124</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1315</XPD:ATTR>
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="T6OBOo60Q0W4cqZXEbC1RgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="pY6xEFcwOE+9xyDGKflCfAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">worker</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="RGPC07sgskGBwg2p/ygfqwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="ktdSB1Q4R0mcUr92wegLdAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="PpgPcPMDNk2aqxVrk8rFfAAA">
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[2]" type="UMLSeqObjectView" guid="PnRdfgtnekueMRegs48acQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">692</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">102</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1277</XPD:ATTR>
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="wa79nq7jfkyCxJ9WKgFXjgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="ADa8z/SUOUquP1daSz7gqQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">connection</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kJ/gDpz3j0ee3eR61+pEaQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="OMFhMMF220KlJGqxz1ec2QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="utuP5e+xFEmMGXpPHYF8ogAA">
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[3]" type="UMLSeqObjectView" guid="33q5j2m07U66Vqancw44YQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">944</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">75</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1269</XPD:ATTR>
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="3uXfUgftWE2YaJbZSLhjRwAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="6hCBIMsdDkKV2jSutLPPxQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">stream</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kyEWL0SI2EqTC4V3i20LggAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="qr6WtEdGD0erGJ2d4gw6swAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="5qlq1+nrd0+/KejphUw97gAA">
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[4]" type="UMLSeqStimulusView" guid="AxGxHEwJa0OC4T9gQnLtwAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,126;736,126</XPD:ATTR>
<XPD:REF name="Model">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="oqPMuEVVjkW4FFaqXZ2M2gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">1 : QuicConnQueueRecvPackets()</XPD:ATTR>
<XPD:REF name="Model">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:REF name="HostEdge">AxGxHEwJa0OC4T9gQnLtwAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="zgt52/X3O0SU39NGJtIbwAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:REF name="HostEdge">AxGxHEwJa0OC4T9gQnLtwAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="OFGwbizC50+6Ce3G56ZRyAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:REF name="HostEdge">AxGxHEwJa0OC4T9gQnLtwAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="fEK+kkkaEkW6ckFY4KjRgAAA">
<XPD:ATTR name="Left" type="integer">736</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">126</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[5]" type="UMLSeqStimulusView" guid="P5iqZbHoZUaoFkA3f/v9eQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">743,171;476,171</XPD:ATTR>
<XPD:REF name="Model">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="zcDimQmn5kSbtOZw8nDk8wAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">2 : QuicConnQueueOper:QUIC_OPER_TYPE_FLUSH_RECV()</XPD:ATTR>
<XPD:REF name="Model">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
<XPD:REF name="HostEdge">P5iqZbHoZUaoFkA3f/v9eQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="w9Fh2BVvTk6IFLW2Cn/UmAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
<XPD:REF name="HostEdge">P5iqZbHoZUaoFkA3f/v9eQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="t8b59CwxPkmqdUcZ91e7uAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
<XPD:REF name="HostEdge">P5iqZbHoZUaoFkA3f/v9eQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="TKpt7EnbYkmdLzN/ggk7uAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">171</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[6]" type="UMLSeqStimulusView" guid="/7H97BiWPEimjQYp8AwQggAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,211;500,211;500,231;476,231</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="WJqHlqRE0UCmK5TESEI9fQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">3 : QuicWorkerLoop()</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="6ABOwJaydkeMEGyOfuP4DwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="TLzxye8lR0ywxzgHukxEfwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="kDGxe6QFDEyocFGYNatesAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">231</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[7]" type="UMLSeqStimulusView" guid="6Mm/ggWCh0+801efQI90igAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,272;736,272</XPD:ATTR>
<XPD:REF name="Model">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="0bsE9+fWEE6E6QvS8UmuYwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">4 : QuicConnFlushRecv()</XPD:ATTR>
<XPD:REF name="Model">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="HostEdge">6Mm/ggWCh0+801efQI90igAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="sZaV8V7H8kqBLjg0b/olPQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="HostEdge">6Mm/ggWCh0+801efQI90igAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="6PeleYSOmUu8HCfI4cGRLAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="HostEdge">6Mm/ggWCh0+801efQI90igAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="4WXGRHaaI02a1u9sAkjskwAA">
<XPD:ATTR name="Left" type="integer">736</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">272</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[8]" type="UMLSeqStimulusView" guid="npzC1Sc490GbJtwn3ytYMgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">743,318;974,318</XPD:ATTR>
<XPD:REF name="Model">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="/JVvnBrLyESWPGqe0JrbngAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">5 : QuicStreamRecv:Packet()</XPD:ATTR>
<XPD:REF name="Model">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="HostEdge">npzC1Sc490GbJtwn3ytYMgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="Twa1D5Wrt0CsZOrLTAr3bgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="HostEdge">npzC1Sc490GbJtwn3ytYMgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="GKhpqDK9x0quf6GVJY09kQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="HostEdge">npzC1Sc490GbJtwn3ytYMgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="NE05C4iAG0O12HRlC/3meQAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">318</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[9]" type="UMLSeqStimulusView" guid="8jCzl8e8s0GlBbcunmlftQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,361;1011,361;1011,381;987,381</XPD:ATTR>
<XPD:REF name="Model">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="2aLR+rR+5EC9Bc+W6ODXmAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">6 : QuicStreamProcessStreamFrame()</XPD:ATTR>
<XPD:REF name="Model">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="HostEdge">8jCzl8e8s0GlBbcunmlftQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="IDV3/DHBKU+RQ3mgx7dm6QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="HostEdge">8jCzl8e8s0GlBbcunmlftQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="lIvH+StJkEyo9ueZp99DjwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="HostEdge">8jCzl8e8s0GlBbcunmlftQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="9Cz+bQmjNUyeOrdjTjHp+AAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">381</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[10]" type="UMLSeqStimulusView" guid="UqEChAZXjkCtloXDfYgvuQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,424;1011,424;1011,444;987,444</XPD:ATTR>
<XPD:REF name="Model">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="eAjq+nPZXkqdBdIe9HfzbAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">7 : QuicStreamRecvQueueFlush()</XPD:ATTR>
<XPD:REF name="Model">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="HostEdge">UqEChAZXjkCtloXDfYgvuQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="Zaat1qy1dkmAJ0NuISEiHQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="HostEdge">UqEChAZXjkCtloXDfYgvuQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="4BdgvlDGa0+DcOcJ2r4nWgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="HostEdge">UqEChAZXjkCtloXDfYgvuQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="lJ7k0l6LPU25KiVJ6Quw5QAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">444</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[11]" type="UMLSeqStimulusView" guid="xmcjuVtxgUeM8NC0yte9TQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,487;476,487</XPD:ATTR>
<XPD:REF name="Model">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="m0H3YrLwVUKa2MZbMI10fQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">8 : QuicConnQueueOper:QUIC_OPER_TYPE_FLUSH_STREAM_RECV()</XPD:ATTR>
<XPD:REF name="Model">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="HostEdge">xmcjuVtxgUeM8NC0yte9TQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="da80ZXBkBEG9H5Nh95g0AAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="HostEdge">xmcjuVtxgUeM8NC0yte9TQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="kenmqMDmrUOiBMOzoxCKdQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="HostEdge">xmcjuVtxgUeM8NC0yte9TQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="to8QyKjaOU6TWljC9E6ulQAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">487</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[12]" type="UMLSeqStimulusView" guid="lsdUv5gxI0GnPr8A65TKqAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,536;500,536;500,556;476,556</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="B6lJgjnH+UmL+ypZjbtGDAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">9 : QuicWorkerLoop()</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="YOf9RPv5f0WGx1XsPe5ZaQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="QWMFLpCu2E6eOqWYlJqTDgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="ZwuiHcjQPUun/x84OfHYvwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">556</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[13]" type="UMLSeqStimulusView" guid="lld2PVqvcEyfeQmLZyKgcgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,597;736,597</XPD:ATTR>
<XPD:REF name="Model">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="OFd8nALptESnSbuC5PqeSgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">10 : QuicConnDrainOperations()</XPD:ATTR>
<XPD:REF name="Model">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="HostEdge">lld2PVqvcEyfeQmLZyKgcgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="3mfDAe0x+kiOG1iGUQkDiAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="HostEdge">lld2PVqvcEyfeQmLZyKgcgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="302LbfLCnEO9iA6qSIsVIwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="HostEdge">lld2PVqvcEyfeQmLZyKgcgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="RxNoYqQWQUCKgT6BaM5qBQAA">
<XPD:ATTR name="Left" type="integer">736</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">597</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[14]" type="UMLSeqStimulusView" guid="gCilH9snu0mjeXzGje9dxAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">743,639;974,639</XPD:ATTR>
<XPD:REF name="Model">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="+RZbYITzM0Gl9zhmMl4ITgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">11 : QuicStreamRecvFlush()</XPD:ATTR>
<XPD:REF name="Model">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="HostEdge">gCilH9snu0mjeXzGje9dxAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="dAMqfsHF5EyZ83X6J4P2SgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="HostEdge">gCilH9snu0mjeXzGje9dxAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="VezKCMJCo0uFO/b1SrQesAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="HostEdge">gCilH9snu0mjeXzGje9dxAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="0S/leimFsUK8AVKierVElwAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">639</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[15]" type="UMLSeqStimulusView" guid="MuX+2RwWQUy0KPfaf/beEQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,676;1011,676;1011,696;987,696</XPD:ATTR>
<XPD:REF name="Model">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="FAahg4/SjE2eiZRqXT4uggAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">12 : QuicStreamIndicateEvent:QUIC_STREAM_EVENT()</XPD:ATTR>
<XPD:REF name="Model">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="HostEdge">MuX+2RwWQUy0KPfaf/beEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="wGJGGxG5QEimZIZeBqCeOQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="HostEdge">MuX+2RwWQUy0KPfaf/beEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="xUmaF3Kx6ESce0We3Eb8WgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="HostEdge">MuX+2RwWQUy0KPfaf/beEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="IHKb3/cd0keUvKMfS3UvFgAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">696</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[16]" type="UMLSeqStimulusView" guid="Y/hVaK1ZlkCjHCIyGs5bpQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,737;205,737</XPD:ATTR>
<XPD:REF name="Model">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
<XPD:REF name="Head">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="fkX+v0F85ki/9i0QE9wpJwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">13 : ServerStreamCallback:QUIC_STREAM_EVENT_RECEIVE()</XPD:ATTR>
<XPD:REF name="Model">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
<XPD:REF name="HostEdge">Y/hVaK1ZlkCjHCIyGs5bpQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="7LKK+f0DPkCFPIG/dGBvgwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
<XPD:REF name="HostEdge">Y/hVaK1ZlkCjHCIyGs5bpQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="vTHcLBMsgUSu52rJmpuLtQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
<XPD:REF name="HostEdge">Y/hVaK1ZlkCjHCIyGs5bpQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="9/0WU95adUGVfDzyvx3KBQAA">
<XPD:ATTR name="Left" type="integer">192</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">737</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[17]" type="UMLSeqStimulusView" guid="Ee3k+xbVxEyvD9zPvBcmSgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">981,762;1011,762;1011,782;987,782</XPD:ATTR>
<XPD:REF name="Model">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="DiZqxGIJVUSwg8hHZ5TFWQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">14 : QuicStreamOnBytesDelivered()</XPD:ATTR>
<XPD:REF name="Model">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:REF name="HostEdge">Ee3k+xbVxEyvD9zPvBcmSgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="SvvS17r7JEGj5l/rPKwJVgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:REF name="HostEdge">Ee3k+xbVxEyvD9zPvBcmSgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="19/meTwexUScK1AusNwemwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:REF name="HostEdge">Ee3k+xbVxEyvD9zPvBcmSgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="3MaZT/oCl0CSHs8o71uBxAAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">782</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[18]" type="UMLSeqStimulusView" guid="woHJetp9gE2Wu+zr3eYWDwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,807;736,807</XPD:ATTR>
<XPD:REF name="Model">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="gJXHJs4dAke25EpbN9wPxAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">15 : QuicConnTryClose()</XPD:ATTR>
<XPD:REF name="Model">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
<XPD:REF name="HostEdge">woHJetp9gE2Wu+zr3eYWDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="ESEATtSqSk+75jXgmnzUTgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
<XPD:REF name="HostEdge">woHJetp9gE2Wu+zr3eYWDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="W2k9lCnlAEezfCSAfDPaOQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
<XPD:REF name="HostEdge">woHJetp9gE2Wu+zr3eYWDwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="Amg7hd2NckaHQecCzWgUMgAA">
<XPD:ATTR name="Left" type="integer">736</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">807</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[19]" type="UMLSeqStimulusView" guid="y/d9qTBaxECTH5iC9zS3zwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">743,857;974,857</XPD:ATTR>
<XPD:REF name="Model">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="G6gPNQgeJk6A6LEiAtQ6/gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">16 : QuicStreamRecvShutdown()</XPD:ATTR>
<XPD:REF name="Model">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
<XPD:REF name="HostEdge">y/d9qTBaxECTH5iC9zS3zwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="WLmd6GUdCkuejE4b8lLUwgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
<XPD:REF name="HostEdge">y/d9qTBaxECTH5iC9zS3zwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="odqWC83JhUS0Xsl7N1WugQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
<XPD:REF name="HostEdge">y/d9qTBaxECTH5iC9zS3zwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="M6hJtgFnlEWGZ31PNtVdmgAA">
<XPD:ATTR name="Left" type="integer">974</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">857</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingStimuli" type="integer">16</XPD:ATTR>
<XPD:OBJ name="ParticipatingStimuli[0]" type="UMLStimulus" guid="9HrBdW7AeEGUt++SIxTG1AAA">
<XPD:ATTR name="Name" type="string">QuicConnQueueRecvPackets</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="jqAVV1ukt0WpetpsPFKoOQAA">
<XPD:REF name="Stimulus">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">AxGxHEwJa0OC4T9gQnLtwAAA</XPD:REF>
<XPD:REF name="Views[1]">oqPMuEVVjkW4FFaqXZ2M2gAA</XPD:REF>
<XPD:REF name="Views[2]">zgt52/X3O0SU39NGJtIbwAAA</XPD:REF>
<XPD:REF name="Views[3]">OFGwbizC50+6Ce3G56ZRyAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[1]" type="UMLStimulus" guid="s9FfrNPp3ECnijQ6FsKqyAAA">
<XPD:ATTR name="Name" type="string">QuicConnQueueOper:QUIC_OPER_TYPE_FLUSH_RECV</XPD:ATTR>
<XPD:REF name="Sender">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="UWcxBEKBSEeF3rZpUNuuzAAA">
<XPD:REF name="Stimulus">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">P5iqZbHoZUaoFkA3f/v9eQAA</XPD:REF>
<XPD:REF name="Views[1]">zcDimQmn5kSbtOZw8nDk8wAA</XPD:REF>
<XPD:REF name="Views[2]">w9Fh2BVvTk6IFLW2Cn/UmAAA</XPD:REF>
<XPD:REF name="Views[3]">t8b59CwxPkmqdUcZ91e7uAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[2]" type="UMLStimulus" guid="hL965ZJIfk2bvrDdeWudywAA">
<XPD:ATTR name="Name" type="string">QuicWorkerLoop</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="GqbR4o+WNU2g1I2plHvR2QAA">
<XPD:REF name="Stimulus">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
<XPD:REF name="Views[1]">WJqHlqRE0UCmK5TESEI9fQAA</XPD:REF>
<XPD:REF name="Views[2]">6ABOwJaydkeMEGyOfuP4DwAA</XPD:REF>
<XPD:REF name="Views[3]">TLzxye8lR0ywxzgHukxEfwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[3]" type="UMLStimulus" guid="0vguMXyvnEiT/m69pTTAAgAA">
<XPD:ATTR name="Name" type="string">QuicConnFlushRecv</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="3mPDCK7ookW4s9mdtNyx/wAA">
<XPD:REF name="Stimulus">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">6Mm/ggWCh0+801efQI90igAA</XPD:REF>
<XPD:REF name="Views[1]">0bsE9+fWEE6E6QvS8UmuYwAA</XPD:REF>
<XPD:REF name="Views[2]">sZaV8V7H8kqBLjg0b/olPQAA</XPD:REF>
<XPD:REF name="Views[3]">6PeleYSOmUu8HCfI4cGRLAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[4]" type="UMLStimulus" guid="nNjqEQJNiUKHfXQvQH2CkgAA">
<XPD:ATTR name="Name" type="string">QuicStreamRecv:Packet</XPD:ATTR>
<XPD:REF name="Sender">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="UYc9Grk72kWD1b7imLkjnAAA">
<XPD:REF name="Stimulus">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">npzC1Sc490GbJtwn3ytYMgAA</XPD:REF>
<XPD:REF name="Views[1]">/JVvnBrLyESWPGqe0JrbngAA</XPD:REF>
<XPD:REF name="Views[2]">Twa1D5Wrt0CsZOrLTAr3bgAA</XPD:REF>
<XPD:REF name="Views[3]">GKhpqDK9x0quf6GVJY09kQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[5]" type="UMLStimulus" guid="VBFsLyGSYUOytbp78V0x3AAA">
<XPD:ATTR name="Name" type="string">QuicStreamProcessStreamFrame</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="HywEr2OnMkmuefKD+RKcDgAA">
<XPD:REF name="Stimulus">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">8jCzl8e8s0GlBbcunmlftQAA</XPD:REF>
<XPD:REF name="Views[1]">2aLR+rR+5EC9Bc+W6ODXmAAA</XPD:REF>
<XPD:REF name="Views[2]">IDV3/DHBKU+RQ3mgx7dm6QAA</XPD:REF>
<XPD:REF name="Views[3]">lIvH+StJkEyo9ueZp99DjwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[6]" type="UMLStimulus" guid="4ArPO3kCPEuiuM2z3Vov5AAA">
<XPD:ATTR name="Name" type="string">QuicStreamRecvQueueFlush</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="fZi3w63obUunqXmeznTMvQAA">
<XPD:REF name="Stimulus">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">UqEChAZXjkCtloXDfYgvuQAA</XPD:REF>
<XPD:REF name="Views[1]">eAjq+nPZXkqdBdIe9HfzbAAA</XPD:REF>
<XPD:REF name="Views[2]">Zaat1qy1dkmAJ0NuISEiHQAA</XPD:REF>
<XPD:REF name="Views[3]">4BdgvlDGa0+DcOcJ2r4nWgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[7]" type="UMLStimulus" guid="H4qIhhN0WUikVcGWZKLieAAA">
<XPD:ATTR name="Name" type="string">QuicConnQueueOper:QUIC_OPER_TYPE_FLUSH_STREAM_RECV</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="bUYWvPKrhEiIg7vVqWU28AAA">
<XPD:REF name="Stimulus">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">xmcjuVtxgUeM8NC0yte9TQAA</XPD:REF>
<XPD:REF name="Views[1]">m0H3YrLwVUKa2MZbMI10fQAA</XPD:REF>
<XPD:REF name="Views[2]">da80ZXBkBEG9H5Nh95g0AAAA</XPD:REF>
<XPD:REF name="Views[3]">kenmqMDmrUOiBMOzoxCKdQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[8]" type="UMLStimulus" guid="FobcVtHgj0KB0KW/lmhxhwAA">
<XPD:ATTR name="Name" type="string">QuicWorkerLoop</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="xnGCScOsA0yKWRKyezzT9wAA">
<XPD:REF name="Stimulus">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
<XPD:REF name="Views[1]">B6lJgjnH+UmL+ypZjbtGDAAA</XPD:REF>
<XPD:REF name="Views[2]">YOf9RPv5f0WGx1XsPe5ZaQAA</XPD:REF>
<XPD:REF name="Views[3]">QWMFLpCu2E6eOqWYlJqTDgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[9]" type="UMLStimulus" guid="pb7S/ZgcC0CHvBTUgNVMnwAA">
<XPD:ATTR name="Name" type="string">QuicConnDrainOperations</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="Zj0XihVAWUGOtA2hdy5AlQAA">
<XPD:REF name="Stimulus">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">lld2PVqvcEyfeQmLZyKgcgAA</XPD:REF>
<XPD:REF name="Views[1]">OFd8nALptESnSbuC5PqeSgAA</XPD:REF>
<XPD:REF name="Views[2]">3mfDAe0x+kiOG1iGUQkDiAAA</XPD:REF>
<XPD:REF name="Views[3]">302LbfLCnEO9iA6qSIsVIwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[10]" type="UMLStimulus" guid="xIqWsZ0yg0Gy1IbJgZ4O9wAA">
<XPD:ATTR name="Name" type="string">QuicStreamRecvFlush</XPD:ATTR>
<XPD:REF name="Sender">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="O7+uhWpyYk+Ts2t14FirgwAA">
<XPD:REF name="Stimulus">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">gCilH9snu0mjeXzGje9dxAAA</XPD:REF>
<XPD:REF name="Views[1]">+RZbYITzM0Gl9zhmMl4ITgAA</XPD:REF>
<XPD:REF name="Views[2]">dAMqfsHF5EyZ83X6J4P2SgAA</XPD:REF>
<XPD:REF name="Views[3]">VezKCMJCo0uFO/b1SrQesAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[11]" type="UMLStimulus" guid="zgjFFGSc/U+l/HIY0gjIbgAA">
<XPD:ATTR name="Name" type="string">QuicStreamIndicateEvent:QUIC_STREAM_EVENT</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="GmEfOmRVy06U+L2eo+r67gAA">
<XPD:REF name="Stimulus">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">MuX+2RwWQUy0KPfaf/beEQAA</XPD:REF>
<XPD:REF name="Views[1]">FAahg4/SjE2eiZRqXT4uggAA</XPD:REF>
<XPD:REF name="Views[2]">wGJGGxG5QEimZIZeBqCeOQAA</XPD:REF>
<XPD:REF name="Views[3]">xUmaF3Kx6ESce0We3Eb8WgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[12]" type="UMLStimulus" guid="YpxuURWJ1UCgEPKuGO1GAAAA">
<XPD:ATTR name="Name" type="string">ServerStreamCallback:QUIC_STREAM_EVENT_RECEIVE</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="Ax4jCBI0+UGjmLtOoEyHfgAA">
<XPD:REF name="Stimulus">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Y/hVaK1ZlkCjHCIyGs5bpQAA</XPD:REF>
<XPD:REF name="Views[1]">fkX+v0F85ki/9i0QE9wpJwAA</XPD:REF>
<XPD:REF name="Views[2]">7LKK+f0DPkCFPIG/dGBvgwAA</XPD:REF>
<XPD:REF name="Views[3]">vTHcLBMsgUSu52rJmpuLtQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[13]" type="UMLStimulus" guid="PnIwNv9pk0ST4ClVNv7z3wAA">
<XPD:ATTR name="Name" type="string">QuicStreamOnBytesDelivered</XPD:ATTR>
<XPD:REF name="Sender">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="qikQeaBbq0uVjm4VCnJnogAA">
<XPD:REF name="Stimulus">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Ee3k+xbVxEyvD9zPvBcmSgAA</XPD:REF>
<XPD:REF name="Views[1]">DiZqxGIJVUSwg8hHZ5TFWQAA</XPD:REF>
<XPD:REF name="Views[2]">SvvS17r7JEGj5l/rPKwJVgAA</XPD:REF>
<XPD:REF name="Views[3]">19/meTwexUScK1AusNwemwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[14]" type="UMLStimulus" guid="Y0zdTcuLIkW6BR9kNdhp+QAA">
<XPD:ATTR name="Name" type="string">QuicConnTryClose</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="ZiMxrPMnCUCU5SujXmVI2gAA">
<XPD:REF name="Stimulus">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">woHJetp9gE2Wu+zr3eYWDwAA</XPD:REF>
<XPD:REF name="Views[1]">gJXHJs4dAke25EpbN9wPxAAA</XPD:REF>
<XPD:REF name="Views[2]">ESEATtSqSk+75jXgmnzUTgAA</XPD:REF>
<XPD:REF name="Views[3]">W2k9lCnlAEezfCSAfDPaOQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[15]" type="UMLStimulus" guid="1tijOmgS3EKhlqnCxWC+wwAA">
<XPD:ATTR name="Name" type="string">QuicStreamRecvShutdown</XPD:ATTR>
<XPD:REF name="Sender">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="NJ5Ta5cNIEKccVz2+LXDiwAA">
<XPD:REF name="Stimulus">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">y/d9qTBaxECTH5iC9zS3zwAA</XPD:REF>
<XPD:REF name="Views[1]">G6gPNQgeJk6A6LEiAtQ6/gAA</XPD:REF>
<XPD:REF name="Views[2]">WLmd6GUdCkuejE4b8lLUwgAA</XPD:REF>
<XPD:REF name="Views[3]">odqWC83JhUS0Xsl7N1WugQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingInstances" type="integer">6</XPD:ATTR>
<XPD:OBJ name="ParticipatingInstances[0]" type="UMLObject" guid="B2op3V8XQ0eesPz/zKkoBQAA">
<XPD:ATTR name="Name" type="string">app</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">hPmViqUBXkyp20XJo5hMdgAA</XPD:REF>
<XPD:REF name="Views[1]">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">1</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">1</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[1]" type="UMLObject" guid="aRekjOsrRkGxXeUMaRdEOgAA">
<XPD:ATTR name="Name" type="string">worker</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">QJ/ol6Ii20Cm5p+rCS0bnQAA</XPD:REF>
<XPD:REF name="Views[1]">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">5</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="SendingStimuli[4]">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[2]" type="UMLObject" guid="An+R1x1Jqk6mV8IzyMPWvwAA">
<XPD:ATTR name="Name" type="string">timer</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[3]" type="UMLObject" guid="xMzoSDbqnEK0EOuKrFyoJQAA">
<XPD:ATTR name="Name" type="string">connection</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">PnRdfgtnekueMRegs48acQAA</XPD:REF>
<XPD:REF name="Views[1]">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">s9FfrNPp3ECnijQ6FsKqyAAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">pb7S/ZgcC0CHvBTUgNVMnwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">0vguMXyvnEiT/m69pTTAAgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">9HrBdW7AeEGUt++SIxTG1AAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">Y0zdTcuLIkW6BR9kNdhp+QAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[4]" type="UMLObject" guid="x4GPIF5LNU6b9TXPe2occgAA">
<XPD:ATTR name="Name" type="string">stream</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">33q5j2m07U66Vqancw44YQAA</XPD:REF>
<XPD:REF name="Views[1]">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">6</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">YpxuURWJ1UCgEPKuGO1GAAAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">H4qIhhN0WUikVcGWZKLieAAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="SendingStimuli[4]">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="SendingStimuli[5]">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">7</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">zgjFFGSc/U+l/HIY0gjIbgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">xIqWsZ0yg0Gy1IbJgZ4O9wAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">nNjqEQJNiUKHfXQvQH2CkgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">4ArPO3kCPEuiuM2z3Vov5AAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[4]">VBFsLyGSYUOytbp78V0x3AAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[5]">PnIwNv9pk0ST4ClVNv7z3wAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[6]">1tijOmgS3EKhlqnCxWC+wwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[5]" type="UMLObject" guid="YHPCGABMdUOkz/1EAH7BiAAA">
<XPD:ATTR name="Name" type="string">stream</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLModel" guid="md+fvOlUm0OVXhlV9hGlZQAA">
<XPD:ATTR name="Name" type="string">Design Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">designModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="m4ZRdEIg30uEQ1AKDhRiwwAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:REF name="DiagramOwner">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="+WcODUTEa0yzWzWZozxWSgAA">
<XPD:REF name="Diagram">m4ZRdEIg30uEQ1AKDhRiwwAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLClassView" guid="Z/m+NTtYakCizo+ZJ6PRwgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">356</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">120</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">213</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">95</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="18l4ty/7JkqfwF849znu2AAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="EvRVNDul20Ol+Qv55pYvzwAA">
<XPD:ATTR name="FontStyle" type="integer">1</XPD:ATTR>
<XPD:ATTR name="Text" type="string">library.c</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="lx49Ske3uEW4bZVaL9fufgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="cG52dQe9wEmS0zCw7F/UXQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="AttributeCompartment" type="UMLAttributeCompartmentView" guid="gKdcDpRpOkWcTxL/ZidPdQAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OperationCompartment" type="UMLOperationCompartmentView" guid="YXSBrfWAQUug1BGXKiQxtgAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="TemplateParameterCompartment" type="UMLTemplateParameterCompartmentView" guid="URf16QRAeUSOmCYkUK1naQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedElements" type="integer">3</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLClass" guid="JBFMD0tAmUOBm1nrDpCX0gAA">
<XPD:ATTR name="Name" type="string">library.c</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Z/m+NTtYakCizo+ZJ6PRwgAA</XPD:REF>
<XPD:REF name="Views[1]">gKdcDpRpOkWcTxL/ZidPdQAA</XPD:REF>
<XPD:REF name="Views[2]">YXSBrfWAQUug1BGXKiQxtgAA</XPD:REF>
<XPD:REF name="Views[3]">URf16QRAeUSOmCYkUK1naQAA</XPD:REF>
<XPD:ATTR name="#OwnedElements" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLInterface" guid="JT2S8CoRJU2JIea3BkEKiQAA">
<XPD:ATTR name="Name" type="string">Kim, Jeongil</XPD:ATTR>
<XPD:REF name="Namespace">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:ATTR name="#Operations" type="integer">4</XPD:ATTR>
<XPD:OBJ name="Operations[0]" type="UMLOperation" guid="99SPX+XZhEeX5Yg+VJEl/QAA">
<XPD:ATTR name="Name" type="string">MsQuicOpen2</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="npZ9vV5aB0yTW0bFO/W0WgAA">
<XPD:ATTR name="Name" type="string">const void** QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">99SPX+XZhEeX5Yg+VJEl/QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[1]" type="UMLOperation" guid="S/AXFe+Ro0mWFJthXDGG0QAA">
<XPD:ATTR name="Name" type="string">MsQuicClose</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="9l+2IKGa3kqRetrsHcZ5EAAA">
<XPD:ATTR name="Name" type="string">const void* QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">S/AXFe+Ro0mWFJthXDGG0QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[2]" type="UMLOperation" guid="FQAUYxQlNkCWUkFLWggmrgAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryLoad</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Operations[3]" type="UMLOperation" guid="XdHB1mkRzkqLeSO9Vtc35QAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryUninitialize</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLClass" guid="22zjv9K1y0aFzy8lJRR/KAAA">
<XPD:ATTR name="Name" type="string">Kum, Deukkyu</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLClass" guid="5oI76i2ZiUeFVSPnXc+4OwAA">
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[3]" type="UMLModel" guid="VFDhfGg3iE6vmYXwAyC6fAAA">
<XPD:ATTR name="Name" type="string">Implementation Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">implementationModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLComponentDiagram" guid="O/DoalvzX0Skw2gFKWB5ZQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">VFDhfGg3iE6vmYXwAyC6fAAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLComponentDiagramView" guid="4Ny79qmdTESrEmvxndV+BAAA">
<XPD:REF name="Diagram">O/DoalvzX0Skw2gFKWB5ZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[4]" type="UMLModel" guid="xmqKmxOf5EmOkrtUe6SHogAA">
<XPD:ATTR name="Name" type="string">Deployment Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">deploymentModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLDeploymentDiagram" guid="kW6AlkIWSUW41ygNV3gWHgAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">xmqKmxOf5EmOkrtUe6SHogAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLDeploymentDiagramView" guid="Hpb7DSjYcUCpDIAkjRJJLAAA">
<XPD:REF name="Diagram">kW6AlkIWSUW41ygNV3gWHgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:BODY>
</XPD:PROJECT>
