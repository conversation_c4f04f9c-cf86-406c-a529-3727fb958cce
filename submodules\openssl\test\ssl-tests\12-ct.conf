# Generated with generate_ssl_tests.pl

num_tests = 6

test-0 = 0-ct-permissive-without-scts
test-1 = 1-ct-permissive-with-scts
test-2 = 2-ct-strict-without-scts
test-3 = 3-ct-strict-with-scts
test-4 = 4-ct-permissive-resumption
test-5 = 5-ct-strict-resumption
# ===========================================================

[0-ct-permissive-without-scts]
ssl_conf = 0-ct-permissive-without-scts-ssl

[0-ct-permissive-without-scts-ssl]
server = 0-ct-permissive-without-scts-server
client = 0-ct-permissive-without-scts-client

[0-ct-permissive-without-scts-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-ct-permissive-without-scts-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
client = 0-ct-permissive-without-scts-client-extra

[0-ct-permissive-without-scts-client-extra]
CTValidation = Permissive


# ===========================================================

[1-ct-permissive-with-scts]
ssl_conf = 1-ct-permissive-with-scts-ssl

[1-ct-permissive-with-scts-ssl]
server = 1-ct-permissive-with-scts-server
client = 1-ct-permissive-with-scts-client

[1-ct-permissive-with-scts-server]
Certificate = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1-key.pem

[1-ct-permissive-with-scts-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1_issuer.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
client = 1-ct-permissive-with-scts-client-extra

[1-ct-permissive-with-scts-client-extra]
CTValidation = Permissive


# ===========================================================

[2-ct-strict-without-scts]
ssl_conf = 2-ct-strict-without-scts-ssl

[2-ct-strict-without-scts-ssl]
server = 2-ct-strict-without-scts-server
client = 2-ct-strict-without-scts-client

[2-ct-strict-without-scts-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-ct-strict-without-scts-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedClientAlert = HandshakeFailure
ExpectedResult = ClientFail
client = 2-ct-strict-without-scts-client-extra

[2-ct-strict-without-scts-client-extra]
CTValidation = Strict


# ===========================================================

[3-ct-strict-with-scts]
ssl_conf = 3-ct-strict-with-scts-ssl

[3-ct-strict-with-scts-ssl]
server = 3-ct-strict-with-scts-server
client = 3-ct-strict-with-scts-client

[3-ct-strict-with-scts-server]
Certificate = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1-key.pem

[3-ct-strict-with-scts-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1_issuer.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
client = 3-ct-strict-with-scts-client-extra

[3-ct-strict-with-scts-client-extra]
CTValidation = Strict


# ===========================================================

[4-ct-permissive-resumption]
ssl_conf = 4-ct-permissive-resumption-ssl

[4-ct-permissive-resumption-ssl]
server = 4-ct-permissive-resumption-server
client = 4-ct-permissive-resumption-client
resume-server = 4-ct-permissive-resumption-server
resume-client = 4-ct-permissive-resumption-client

[4-ct-permissive-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1-key.pem

[4-ct-permissive-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1_issuer.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
HandshakeMode = Resume
ResumptionExpected = Yes
client = 4-ct-permissive-resumption-client-extra
resume-client = 4-ct-permissive-resumption-client-extra

[4-ct-permissive-resumption-client-extra]
CTValidation = Permissive


# ===========================================================

[5-ct-strict-resumption]
ssl_conf = 5-ct-strict-resumption-ssl

[5-ct-strict-resumption-ssl]
server = 5-ct-strict-resumption-server
client = 5-ct-strict-resumption-client
resume-server = 5-ct-strict-resumption-server
resume-client = 5-ct-strict-resumption-resume-client

[5-ct-strict-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1-key.pem

[5-ct-strict-resumption-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/embeddedSCTs1_issuer.pem
VerifyMode = Peer

[5-ct-strict-resumption-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
HandshakeMode = Resume
ResumptionExpected = Yes
client = 5-ct-strict-resumption-client-extra
resume-client = 5-ct-strict-resumption-resume-client-extra

[5-ct-strict-resumption-client-extra]
CTValidation = Strict

[5-ct-strict-resumption-resume-client-extra]
CTValidation = Strict


