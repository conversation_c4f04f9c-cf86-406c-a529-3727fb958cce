name: CI
on:
  pull_request: {}
  push:
    branches:
      - main
      - '*.*.x'
    tags:
      - '*.*'
      - '*.*.*'

permissions: read-all

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true


jobs:
  linux:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.9", TOXENV: "flake", COVERAGE: "false"}
          - {VERSION: "3.9", TOXENV: "rust", COVERAGE: "false"}
          - {VERSION: "3.9", TOXENV: "docs", COVERAGE: "false"}
          - {VERSION: "pypy-3.6", TOXENV: "pypy3-nocoverage", COVERAGE: "false"}
          - {VERSION: "pypy-3.7", TOXENV: "pypy3-nocoverage", COVERAGE: "false"}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "openssl", VERSION: "1.1.0l"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1l"}}
          - {VERSION: "3.9", TOXENV: "py39-ssh", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1l"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "openssl", VERSION: "1.1.1l", CONFIG_FLAGS: "no-engine no-rc2 no-srtp no-ct"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "openssl", VERSION: "3.0.0"}}
          - {VERSION: "3.9", TOXENV: "py39", TOXARGS: "--enable-fips=1", OPENSSL: {TYPE: "openssl", CONFIG_FLAGS: "enable-fips", VERSION: "3.0.0"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "2.9.2"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "3.0.2"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "3.1.5"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "3.2.7"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "3.3.5"}}
          - {VERSION: "3.9", TOXENV: "py39", OPENSSL: {TYPE: "libressl", VERSION: "3.4.0"}}
          - {VERSION: "3.10", TOXENV: "py310"}
        RUST:
          - stable
    name: "${{ matrix.PYTHON.TOXENV }} ${{ matrix.PYTHON.OPENSSL.TYPE }} ${{ matrix.PYTHON.OPENSSL.VERSION }} ${{ matrix.PYTHON.TOXARGS }} ${{ matrix.PYTHON.OPENSSL.CONFIG_FLAGS }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.PYTHON.VERSION }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: ${{ matrix.RUST }}
          override: true
          default: true
      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
      - run: python -m pip install tox requests coverage
      - name: Compute config hash and set config vars
        run: |
          DEFAULT_CONFIG_FLAGS="shared no-ssl2 no-ssl3"
          CONFIG_FLAGS="$DEFAULT_CONFIG_FLAGS $CONFIG_FLAGS"
          CONFIG_HASH=$(echo "$CONFIG_FLAGS" | sha1sum | sed 's/ .*$//')
          echo "CONFIG_FLAGS=${CONFIG_FLAGS}" >> $GITHUB_ENV
          echo "CONFIG_HASH=${CONFIG_HASH}" >> $GITHUB_ENV
          echo "OSSL_INFO=${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${CONFIG_FLAGS}" >> $GITHUB_ENV
          echo "OSSL_PATH=${{ github.workspace }}/osslcache/${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${CONFIG_HASH}" >> $GITHUB_ENV
        env:
          CONFIG_FLAGS: ${{ matrix.PYTHON.OPENSSL.CONFIG_FLAGS }}
        if: matrix.PYTHON.OPENSSL
      - name: Load cache
        uses: actions/cache@v2.1.6
        id: ossl-cache
        with:
          path: ${{ github.workspace }}/osslcache
          # When altering the openssl build process you may need to increment the value on the end of this cache key
          # so that you can prevent it from fetching the cache and skipping the build step.
          key: ${{ matrix.PYTHON.OPENSSL.TYPE }}-${{ matrix.PYTHON.OPENSSL.VERSION }}-${{ env.CONFIG_HASH }}-2
        if: matrix.PYTHON.OPENSSL
      - name: Build custom OpenSSL/LibreSSL
        run: .github/workflows/build_openssl.sh
        env:
          TYPE: ${{ matrix.PYTHON.OPENSSL.TYPE }}
          VERSION: ${{ matrix.PYTHON.OPENSSL.VERSION }}
        if: matrix.PYTHON.OPENSSL && steps.ossl-cache.outputs.cache-hit != 'true'
      - name: Set CFLAGS/LDFLAGS
        run: |
          echo "CFLAGS=${CFLAGS} -Werror=implicit-function-declaration -I${OSSL_PATH}/include" >> $GITHUB_ENV
          echo "LDFLAGS=${LDFLAGS} -L${OSSL_PATH}/lib -L${OSSL_PATH}/lib64 -Wl,-rpath=${OSSL_PATH}/lib -Wl,-rpath=${OSSL_PATH}/lib64" >> $GITHUB_ENV
        if: matrix.PYTHON.OPENSSL
      - name: Tests
        run: |
            tox -r --  --color=yes --wycheproof-root=wycheproof ${{ matrix.PYTHON.TOXARGS }}
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - uses: ./.github/actions/upload-coverage
        with:
          name: "tox -e ${{ matrix.PYTHON.TOXENV }} ${{ env.OSSL_INFO }}"
        if: matrix.PYTHON.COVERAGE != 'false'

  linux-distros:
    runs-on: ubuntu-latest
    container: ghcr.io/pyca/cryptography-runner-${{ matrix.IMAGE.IMAGE }}
    strategy:
      fail-fast: false
      matrix:
        IMAGE:
          - {IMAGE: "centos8", TOXENV: "py36"}
          - {IMAGE: "centos8-fips", TOXENV: "py36", FIPS: true}
          - {IMAGE: "buster", TOXENV: "py37"}
          - {IMAGE: "bullseye", TOXENV: "py39"}
          - {IMAGE: "bookworm", TOXENV: "py39"}
          - {IMAGE: "sid", TOXENV: "py39"}
          - {IMAGE: "ubuntu-bionic", TOXENV: "py36"}
          - {IMAGE: "ubuntu-focal", TOXENV: "py38"}
          - {IMAGE: "ubuntu-rolling", TOXENV: "py39"}
          - {IMAGE: "ubuntu-rolling", TOXENV: "py39-randomorder"}
          - {IMAGE: "fedora", TOXENV: "py39"}
          - {IMAGE: "alpine", TOXENV: "py39"}
    name: "${{ matrix.IMAGE.TOXENV }} on ${{ matrix.IMAGE.IMAGE }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.IMAGE.IMAGE }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}

      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
      - run: |
          echo "OPENSSL_FORCE_FIPS_MODE=1" >> $GITHUB_ENV
          echo "CFLAGS=-DUSE_OSRANDOM_RNG_FOR_TESTING" >> $GITHUB_ENV
        if: matrix.IMAGE.FIPS
      - run: 'tox -- --wycheproof-root="wycheproof"'
        env:
          TOXENV: ${{ matrix.IMAGE.TOXENV }}
          RUSTUP_HOME: /root/.rustup
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - uses: ./.github/actions/upload-coverage
        with:
          name: "${{ matrix.IMAGE.TOXENV }} on ${{ matrix.IMAGE.IMAGE }}"

  linux-rust:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.9", TOXENV: "py39"}
        RUST:
          # Cover MSRV (and likely next MSRV) and in-dev versions
          - 1.41.0
          - 1.45.0
          - beta
          - nightly
    name: "${{ matrix.PYTHON.TOXENV }} with Rust ${{ matrix.RUST }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}-${{ matrix.RUST }}

      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: ${{ matrix.RUST }}
          override: true
          default: true
      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
      - run: python -m pip install tox coverage
      - name: Tests
        run: |
            tox -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - uses: ./.github/actions/upload-coverage
        with:
          name: "${{ matrix.PYTHON.TOXENV }} with Rust ${{ matrix.RUST }}"

  linux-rust-coverage:
    runs-on: ubuntu-latest
    name: "Rust Coverage"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        id: cargo-cache
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}-rust-nightly-coverage

      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: 3.9
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: nightly
          override: true
          default: true
          components: llvm-tools-preview
      - uses: actions-rs/install@v0.1.2
        with:
          crate: cargo-binutils
          version: latest
        if: steps.cargo-cache.outputs.cache-hit != 'true'

      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
      - run: python -m pip install tox coverage
      - name: Tests
        run: |
            tox -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: py39
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
          RUSTFLAGS: "-Zinstrument-coverage"
          LLVM_PROFILE_FILE: "rust-cov/cov-%p.profraw"
      - name: Process coverage data
        run: |
            cd src/rust/
            cargo profdata -- merge -sparse ../../rust-cov/*.profraw -o rust-cov.profdata
            cargo cov -- export ../../.tox/py39/lib/python3.9/site-packages/cryptography/hazmat/bindings/_rust.abi3.so \
              -instr-profile=rust-cov.profdata \
              --ignore-filename-regex='/.cargo/registry' \
              --ignore-filename-regex='/rustc/' \
              --ignore-filename-regex='/.rustup/toolchains/' --format=lcov > ../../rust-cov.lcov

            sed -E -i 's/SF:src\/(.*)/SF:src\/rust\/src\/\1/g' ../../rust-cov.lcov

      - uses: ./.github/actions/upload-coverage
        with:
          name: "Rust Coverage"

  linux-cargo-coverage:
    runs-on: ubuntu-latest
    name: "Cargo Coverage"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        id: cargo-cache
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}-rust-nightly-cargo-coverage

      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: nightly
          override: true
          default: true
          components: llvm-tools-preview
      - uses: actions-rs/install@v0.1.2
        with:
          crate: cargo-binutils
          version: latest
        if: steps.cargo-cache.outputs.cache-hit != 'true'

      - name: Tests
        run: |
            cd src/rust
            cargo test --no-default-features
        env:
          RUSTFLAGS: "-Zinstrument-coverage"
          LLVM_PROFILE_FILE: "rust-cov/cov-%m-%p.profraw"
      - name: Process coverage data
        run: |
            cd src/rust/
            cargo profdata -- merge -sparse rust-cov/*.profraw -o rust-cov.profdata
            cargo cov -- export \
              $(env RUSTFLAGS="-Zinstrument-coverage" cargo test --no-default-features --tests --no-run --message-format=json | jq -r "select(.profile.test == true) | .filenames[]") \
              -instr-profile=rust-cov.profdata \
              --ignore-filename-regex='/.cargo/registry' \
              --ignore-filename-regex='/rustc/' \
              --ignore-filename-regex='/.rustup/toolchains/' --format=lcov > ../../rust-cov.lcov

            sed -E -i 's/SF:src\/(.*)/SF:src\/rust\/src\/\1/g' ../../rust-cov.lcov

      - uses: ./.github/actions/upload-coverage
        with:
          name: "Cargo Coverage"

  macos:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {VERSION: "3.6", TOXENV: "py36", EXTRA_CFLAGS: ""}
          - {VERSION: "3.10", TOXENV: "py310", EXTRA_CFLAGS: "-DUSE_OSRANDOM_RNG_FOR_TESTING"}
        RUST:
          - stable
    name: "${{ matrix.PYTHON.TOXENV }} on macOS"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.PYTHON.VERSION }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: ${{ matrix.RUST }}
          override: true
          default: true

      - run: python -m pip install tox requests coverage

      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"

      - name: Download OpenSSL
        run: |
          python .github/workflows/download_openssl.py macos openssl-macos-x86-64
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Tests
        run: |
          CRYPTOGRAPHY_SUPPRESS_LINK_FLAGS=1 \
            LDFLAGS="${HOME}/openssl-macos-x86-64/lib/libcrypto.a ${HOME}/openssl-macos-x86-64/lib/libssl.a" \
            CFLAGS="-I${HOME}/openssl-macos-x86-64/include -Werror -Wno-error=deprecated-declarations -Wno-error=incompatible-pointer-types-discards-qualifiers -Wno-error=unused-function -Wno-error=unused-command-line-argument -mmacosx-version-min=10.10 -march=core2 $EXTRA_CFLAGS" \
            tox -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          EXTRA_CFLAGS: ${{ matrix.PYTHON.EXTRA_CFLAGS }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}

      - uses: ./.github/actions/upload-coverage
        with:
          name: "${{ matrix.PYTHON.TOXENV }} on macOS"

  windows:
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        WINDOWS:
          - {ARCH: 'x86', WINDOWS: 'win32', RUST_TRIPLE: 'i686-pc-windows-msvc'}
          - {ARCH: 'x64', WINDOWS: 'win64', RUST_TRIPLE: 'x86_64-pc-windows-msvc'}
        PYTHON:
          - {VERSION: "3.6", TOXENV: "py36", MSVC_VERSION: "2019", CL_FLAGS: ""}
          - {VERSION: "3.10", TOXENV: "py310", MSVC_VERSION: "2019", CL_FLAGS: "/D USE_OSRANDOM_RNG_FOR_TESTING"}
        RUST:
          - stable
        JOB_NUMBER: [0, 1, 2, 3]
    name: "${{ matrix.PYTHON.TOXENV }} on ${{ matrix.WINDOWS.WINDOWS }} (part ${{ matrix.JOB_NUMBER }})"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - name: Setup python
        id: setup-python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
          architecture: ${{ matrix.WINDOWS.ARCH }}
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.WINDOWS.ARCH }}-${{ steps.setup-python.outputs.python-version }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}

      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: ${{ matrix.RUST }}
          override: true
          default: true
          target: ${{ matrix.WINDOWS.RUST_TRIPLE }}

      - run: python -m pip install tox requests coverage
      - name: Download OpenSSL
        run: |
            python .github/workflows/download_openssl.py windows openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}
            echo "INCLUDE=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}/include;$INCLUDE" >> $GITHUB_ENV
            echo "LIB=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}/lib;$LIB" >> $GITHUB_ENV
            echo "CL=${{ matrix.PYTHON.CL_FLAGS }}" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash
      - uses: actions/checkout@v2.3.4
        with:
          repository: "google/wycheproof"
          path: "wycheproof"

      - run: tox -r -- --color=yes --wycheproof-root=wycheproof --num-shards=4 --shard-id=${{ matrix.JOB_NUMBER }}
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}

      - uses: ./.github/actions/upload-coverage
        with:
          name: "${{ matrix.PYTHON.TOXENV }} on ${{ matrix.WINDOWS.WINDOWS }} (part ${{ matrix.JOB_NUMBER }})"

  linux-downstream:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        DOWNSTREAM:
          - paramiko
          - pyopenssl
          - pyopenssl-release
          - aws-encryption-sdk
          - dynamodb-encryption-sdk
          - certbot
          - certbot-josepy
          - mitmproxy
        RUST:
          - stable
        PYTHON:
          - 3.8
    name: "Downstream tests for ${{ matrix.DOWNSTREAM }}"
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          persist-credentials: false
      - uses: actions/cache@v2.1.6
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            src/rust/target/
          key: ${{ runner.os }}-cargo-2-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: ${{ matrix.RUST }}
          override: true
          default: true
      - run: python -m pip install -U pip wheel
      - run: ./.github/downstream.d/${{ matrix.DOWNSTREAM }}.sh install
      - run: pip uninstall -y enum34
      - run: pip install .
        env:
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
      - run: ./.github/downstream.d/${{ matrix.DOWNSTREAM }}.sh run
