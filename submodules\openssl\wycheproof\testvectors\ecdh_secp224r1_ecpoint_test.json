{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 96, "header": ["Test vectors of type EcdhWebTest are intended for", "testing an ECDH implementations where the public key", "is just an ASN encoded point."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format."}, "schema": "ecdh_ecpoint_test_schema.json", "testGroups": [{"curve": "secp224r1", "encoding": "ecpoint", "type": "EcdhEcpointTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "047d8ac211e1228eb094e285a957d9912e93deee433ed777440ae9fc719b01d050dfbe653e72f39491be87fb1a2742daa6e0a2aada98bb1aca", "private": "565577a49415ca761a0322ad54e4ad0ae7625174baf372c2816f5328", "shared": "b8ecdb552d39228ee332bafe4886dbff272f7109edf933bc7542bd4f", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "027d8ac211e1228eb094e285a957d9912e93deee433ed777440ae9fc71", "private": "565577a49415ca761a0322ad54e4ad0ae7625174baf372c2816f5328", "shared": "b8ecdb552d39228ee332bafe4886dbff272f7109edf933bc7542bd4f", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "04e73a6ca72f3a2fae6e0a01a0ed03bfa3058b04576942eaf063095e62ca16fd31fa0f38eeb592cbeea1147751fdd2a5b6cc0ead404467a5b6", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "00000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "045763fa2ae16367ad23d471cc9a52466f0d81d864e5640cefe384114594d9fecfbed4f254505ac8b41d2532055a07f0241c4818b552cbb636", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "00000000000000000000000100000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "04142c1fd80fa2121a59aa898144084ec033f7a56a34eee0b499e29ae51c6d8c1bbb1ef2a76d565899fe44ffc1207d530d7f598fb77f4bb76b", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "00000000000000ffffffffffffff0000000000000100000000000000", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "04ed6f793e10c80d12d871cf8988399c4898a9bf9ffd8f27399f63de25f0051cdf4eec7f368f922cfcd948893ceca0c92e540cc4367a99a66a", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "00000000ffffffffffffffff00000000000000010000000000000000", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "0408fcfc1a63c82860be12e4137433dfc40be9acdd245f9a8c4e56be61a385fc09f808383383f4b1d0d5365b6e5dcfacdc19bc7bcfed221274", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "04d883ed77f1861e8712800d31df67888fe39f150c79a27aa88caeda6b180f3f623e2ff3ab5370cf8179165b085af3dd4502850c0104caed9a", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "0003fffffff00000003fffffff00000003fffffff000000040000000", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "042b8b279b85ee3f3d2c0abeb36fdfc5aad6157d652d26489381a32cd73224bd757ef794acc92b0b3b9e7990618bb343a9a09bdb9d3616eff6", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "01fffffffc00000007fffffff00000001fffffffc000000080000001", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "048bd5f03391eeeae1744e8fc53d314efffafa4d3fa4f1b95c3388a9cd7c86358b273119c537133eb55e79c6ac510b10980b379b919ccf2e2f", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "0a15c112ff784b1445e889f955be7e3ffdf451a2c0e76ab5cb32cf41", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "04ce9631b6a16227778625c8e5421ae083cdd913abefde01dbe69f6c2b95386aff2b483b2c47151cfaabfd000614c683ce2e1778221ae42c1b", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "62989eaaa26a16f07330c3c51e0a4631fd016bfcede26552816aee39", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "041f441c98eda956a6a7fdbfd8d21910860ab59d16c3e52f8e7fad6ca5df61a55fc508fc0499c55492f1e87bb2faa0cb4170b79f3a85ec2f3d", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "661ac958c0febbc718ccf39cefc6b66c4231fbb9a76f35228a3bf5c3", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "04be74583cb9d3a05ae54923624e478a329a697d842dfae33141c844d7d9ba4fc96e0fe716ac0542e87368662fc2f0cb9b0ae57936ddec7190", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "6d7e41821abe1094d430237923d2a50de31768ab51b12dce8a09e34c", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "04a281ad992b363597ac93ff0de8ab1f7e51a6672dcbb58f9d739ba430ce0192874038daefc3130eec65811c7255da70fea65c1003f6892faa", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "04be3e22133f51203f631b81dde8c020cdea5daa1f99cfc05c88fad2dc0f243798d6e72d1de9e3cdca4144e0a6c0f2a584d07589006972c197", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0008001", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "04af14547c20afbd91bfe64ea03d45a76a71241f23520ef897ff91eff1b54ca6ca8c25fd73852ec6654617434eff7f0225684d4dea7a4f8a97", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "ffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "04b1e484925018729926acda56ff3e2f6c1e7e8f162b178d8e8afb45564fceaa6da5d998fe26b6b26a055169063a5ab6908852ca8b54e2de6c", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "fffff0000007fffffe000000ffffffc000001ffffff8000003ffffff", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "04937eb09fb145c8829cb7df20a4cbeed396791373de277871d6c5f9cc3b5b4fd56464a71fc4a2a6af3bd251952bffa829489e68a8d06f96b6", "private": "00a2b6442a37f9201b56758034d2009be64b0ab7c02d7e398cac9665d6", "shared": "ffffffff00000000ffffffff00000000ffffffff00000000ffffffff", "result": "valid", "flags": []}, {"tcId": 19, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000000000000000000000000000000000000037cac269c67bd55ea14efff4eadefe5e74978514af14c88fab46ec046", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "3fa0b9ff70b884f9f57bb84f7a9532d93f6ba803f89dd8ff008177d7", "result": "valid", "flags": []}, {"tcId": 20, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000001000000000000000000000000000000012ea2f4917bdfdb008306cc10a18e2557633ba861001829dcbfb96fba", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "be1ded8cb7ff8a585181f96d681e31b332fe27dcae922dca2310300d", "result": "valid", "flags": []}, {"tcId": 21, "comment": "edge cases for ephemeral key", "public": "0400000000000000ffffffffffffff000000000000010000000000000073ca5f8f104997a2399e0c7f25e72a75ec29fc4542533d3fea89a33a", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "a2e86a260e13515918a0cafdd87855f231b5624c560f976159e06a75", "result": "valid", "flags": []}, {"tcId": 22, "comment": "edge cases for ephemeral key", "public": "0400000000ffffffffffffffff000000000000000100000000000000006fe6805f59b19b0dd389452a1d4a420bfeb6c369cf6fed5b12e6e654", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "31ef7c8d10404a0046994f313a70574b027e87f9028eca242c1b5bf5", "result": "valid", "flags": []}, {"tcId": 23, "comment": "edge cases for ephemeral key", "public": "040000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff77c5cfa4e2c384938d48bd8dd98f54c86b279f1df8c0a1f6692439c9", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "d1976a8ef5f54f24f5a269ad504fdca849fc9c28587ba294ef267396", "result": "valid", "flags": []}, {"tcId": 24, "comment": "edge cases for ephemeral key", "public": "040003fffffff00000003fffffff00000003fffffff00000004000000001f0828136016bb97445461bc59f2175d8d23557d6b9381f26136e3d", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "ce7890d108ddb2e5474e6417fcf7a9f2b3bd018816062f4835260dc8", "result": "valid", "flags": []}, {"tcId": 25, "comment": "edge cases for ephemeral key", "public": "0401fffffffc00000007fffffff00000001fffffffc0000000800000012d8acca6f199d4a94b933ba1aa713a7debde8ac57b928f596ae66a66", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "30b6ff6e8051dae51e4fe34b2d9a0b1879153e007eb0b5bdf1791a9c", "result": "valid", "flags": []}, {"tcId": 26, "comment": "edge cases for ephemeral key", "public": "040a15c112ff784b1445e889f955be7e3ffdf451a2c0e76ab5cb32cf413d4df973c563c6decdd435e4f864557e4c273096d9941ca4260a266e", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "77ec668a00f72d85aa527624abb16c039fe490d17dd6c455a1ed7fd8", "result": "valid", "flags": []}, {"tcId": 27, "comment": "edge cases for ephemeral key", "public": "0462989eaaa26a16f07330c3c51e0a4631fd016bfcede26552816aee39389ee9436d616cab90032931aa7fbbfcfc13309f61e2423cc8dab93c", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "a3f432f6aba9a92f49a5ea64ffe7059a9d9b487a0b5223ddc988208b", "result": "valid", "flags": []}, {"tcId": 28, "comment": "edge cases for ephemeral key", "public": "04661ac958c0febbc718ccf39cefc6b66c4231fbb9a76f35228a3bf5c3103b8040e3cb41966fc64a68cacb0c14053f87d27e8ed7bf2d7fe51b", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "1530fd9caf03737af34a4ba716b558cbecbc35d18402535a0a142313", "result": "valid", "flags": []}, {"tcId": 29, "comment": "edge cases for ephemeral key", "public": "046d7e41821abe1094d430237923d2a50de31768ab51b12dce8a09e34c276cf273d75d367820dd556182def0957af0a314f48fed227c298dc0", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "cfc39ccacb94ad0e0552b2e47112f60fbbe7ae0dc32230b9273dd210", "result": "valid", "flags": []}, {"tcId": 30, "comment": "edge cases for ephemeral key", "public": "047fffffffffffffffffffffffffffffffffffffffffffffffffffffff7d8dbca36c56bcaae92e3475f799294f30768038e816a7d5f7f07d77", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "73bd63bd384a0faafb75cfed3e95d3892cbacf0db10f282c3b644771", "result": "valid", "flags": []}, {"tcId": 31, "comment": "edge cases for ephemeral key", "public": "04fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc000800174f1ff5ea7fbc72b92f61e06556c26bab84c0b082dd6400ca1c1eb6d", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "85b079c62e1f5b0fd6841dfa16026e15b641f65e13a14042567166bb", "result": "valid", "flags": []}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "04ffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0126fdd5fccd0b5aa7fd5bb5b1308584b30556248cec80208a2fe962", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "8a834ff40e3fc9f9d412a481e18537ea799536c5520c6c7baaf12166", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "04fffff0000007fffffe000000ffffffc000001ffffff8000003ffffff20cfa23077acc9fbcb71339c65880cd0b966b8a9497e65abed17f0b5", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "a0887269766e6efcbc81d2b38f2d4638663f12377468a23421044188", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "04ffffffff00000000ffffffff00000000ffffffff00000000ffffffff1c05ac2d4f10b69877c3243d51f887277b7bf735c326ab2f0d70da8c", "private": "2bc15cf3981f4e15bbad387b506df647989e5478160be862f8c26969", "shared": "c65d1911bc076a74588d8793ce7a0dcabf5793460cd2ebb02754a1be", "result": "valid", "flags": []}, {"tcId": 35, "comment": "point with coordinate y = 1", "public": "043b5889352ddf7468bf8c0729212aa1b2a3fcb1a844b8be91abb753d500000000000000000000000000000000000000000000000000000001", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "e973c413cc7dd34d4e3637522b2e033c20815412b67574a1f2f6bdd7", "result": "valid", "flags": []}, {"tcId": 36, "comment": "point with coordinate y = 1", "public": "04bf09e268942555c73ce9e00d272c9b12bf0c3fc13a639acc791167f6b05df0023c9bd41d0b0c461854582d0601182213f2219d44ea44914a", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "ec856e807808a9c5332e886759e03f01be02437cfe0214613e4e7dc7", "result": "valid", "flags": []}, {"tcId": 37, "comment": "point with coordinate y = 1", "public": "047b664cff2eef0a4f7dce24780113432f66feb25cb0931d033d63910f548ee514f6fdf1cb6f5709581c197d76a5eb218afaed19f205f4ab80", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "91d424e122c9c01720bbed6b53ec1b37a86996fa4fcf74bfd30f723d", "result": "valid", "flags": []}, {"tcId": 38, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "045a2b3ec1053390550b587557712bcc0bf85654d23099420154877ec4138322ca02e5fceae870227a43ae8982b67276f6d8f1dd7e12692474", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "012879a1ff456acb8726455836bc4f504c1bd799a4d96f514b3730c6", "result": "valid", "flags": []}, {"tcId": 39, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04fc229bb1df3e11351e7e4224f68f40c0d0e194023c6e0840cd45ee5ca242112fbab5736e821dad26493e4006e2c6125342e7d9bc25272856", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "fd6e5edb54d7dd554f8747ec87b8031258fc0bf1d2404b64db4540d4", "result": "valid", "flags": []}, {"tcId": 40, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0469a65f62d4159235801a246f2d13e45c8983a3362da480e7a51d42a65b7047abfc2a179d943bb196fede7ac3ad8a4fcacd4c4caa717b6b26", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "164e95bfa2a9c3a1f959feb88720bb7a37f988a08124639d8adf86df", "result": "valid", "flags": []}, {"tcId": 41, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04dc68eb945528af0051cbf23e3eea43b2bc4c728976231e7031e63a2744ba65a4e1e34e8ec50cf7e8df4458582b16413ab83f568508c59037", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "b0ffd55fa112aa48eddc960db4a1200d406e144aac9e109ad9892b2d", "result": "valid", "flags": []}, {"tcId": 42, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0481c89369d7be252920e08e2d6c6841b887efb4fc747db31dd1030b1919bf8ccb629b58fea6234e39812083fb0833a0c937e348eda22ea0c0", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "d6ab4567eff21277284be082d9e09eb08bb80685f4929dc3dca4b333", "result": "valid", "flags": []}, {"tcId": 43, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0451d830f792795409f1ee972d3b94289f59206fe09e12166920739a73d2f1831b26677901bfaf8323f82b81e1012d9d3f1c9296c59c97970f", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "b43de12912b40cbdd56e30fdfe9a2c24fb72687168c9cfe6b7476966", "result": "valid", "flags": []}, {"tcId": 44, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04ab63ce55145842149f99023f37a0a89b9fc4ae6a878fdae8caf31d17ffd0d55830eed46f8255f94b6dcf98a22f1ff26dabf773d556788881", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "588ee0af3bc60118a715325c6d56c850f73067dcb37b7596d0cfda5f", "result": "valid", "flags": []}, {"tcId": 45, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "041d64535d54bfcccb38165acbfac01ae33db20e802c5687343cb21b7eb59d86f1892a974741925624477eef21f4e72fa04ee6ce35dfffe5f2", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "7219ef73ac9e47ac2e03dead23fa8382ae898e2415017cdeb4739f0f", "result": "valid", "flags": []}, {"tcId": 46, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04d9d78436a3f9c1fa20e8c2318e61e62b94623e23a0ab746c5ac0cbc38262bd66c17515d3048944dae43b2bd6dd9d7c7a0f7042de2d1001c6", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "267b069aac5d768a720acc62c92f20b786fc48c7da42f1f5677424ee", "result": "valid", "flags": []}, {"tcId": 47, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0465eb3750c6401339caa69ebe6dec86dfc4d79bf657d68bbdd082c5a03eb81e85931352ff338ccbc3a1d332e2d8bc84342d516da06bef220f", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "bbdd4ac5890b9c0412e4ef3135f666e5b3ddb658ec837691e8129be8", "result": "valid", "flags": []}, {"tcId": 48, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04e92d3be1614555ae17a90647979fbb37468c55a1fff9e15f376d49994e470f515b7b3fe50cb55def16142df594c3e46d9d1354730778f9e8", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "f793ff0d14bd7690840c733162b589cd3413d8c41f4488b427da496f", "result": "valid", "flags": []}, {"tcId": 49, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "043c92710c9a7f6f98bbec9d2a4fa617cc70e96bc96ecd4597e329143f4750a027c6972459c091ab02c0e2a3082fccec429a38d3596e7aff2b", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "56c703d4716239c954109b9b841db75b04a790f1f72aa966aece3494", "result": "valid", "flags": []}, {"tcId": 50, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04568dfbfa42efc94ce207322e637b4c94f37a5668ad230e987a91d048dcadd244fc059cffab5fa8820a969353620e708e85bd5eec8a0c68ec", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "7823fe7eb642d50984fb32f911ef289419d85330c3398423d0eda05f", "result": "valid", "flags": []}, {"tcId": 51, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04ec10837e495b644904dba58d8dd82133c905a285ae7c2a06d5ccaf6bf0fbf00d13e21a399dc95ae5524a1a37044193e94e3300259b70e058", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "f7014d38f460836a51075cce9667b56b8851ba19011c8b0274b74a4b", "result": "valid", "flags": []}, {"tcId": 52, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04bee2f9352f42ceeb3bf3109e90e6578d0bd4888458df7d179d746977e50e53503dee83eca1824a290566588fa3591645b1a2d56861bda760", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "777f99f2bdaa72a1185388465ddda1d059872ad043c7cb85b94e28bb", "result": "valid", "flags": []}, {"tcId": 53, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04546facbcaa8b551c51715a9add5edc3c8a66dcc47a6223f605614cf7af6d92f5bdebea738658a42c6231e53c08237ccf52f79399579b2dcc", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "a1db178b716e51e0fa46c1d74a2603005326bca7e81170d4b33a3d2a", "result": "valid", "flags": []}, {"tcId": 54, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0423b1811fee891adb33c8bfee289964e92a9d3358daf975d0efb73e229a3332668b7d6da290a2edc941e8bd6f2e33745fc606756eddc013bb", "private": "00938f3dbe37135cdbdb9993a187a0e9b9f0def035fbc52ad59fc50421", "shared": "f455c8273416199505019861266ddb9bcde7bee3c3f15a98ee54607b", "result": "valid", "flags": []}, {"tcId": 55, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0458f53d67332415fe5b4b81999f8332fb6dcdb965d96dbcbab0fac375f29efef7ab4d94bb2d25d25205eae29fe8d9a85b811114a50f6c6859", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "d3af1857aca1689514fcfee8d8c40b8637d40452ae35c404f9e67494", "result": "valid", "flags": []}, {"tcId": 56, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04f2d6e58fcd3ed3f656a9bc687fe4c789ba9614d0359967bc0468eabfa1658a14ef0633f2485e29141e2c4a13bd328ec9bf6af4c7a774131b", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "933c385d5fadb57de53e4a5d385118fce830430703c3f585a5d4d0b5", "result": "valid", "flags": []}, {"tcId": 57, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0402ca5d1b7638b7b88ad02176bd10ff1cfe8812a62f9769a6d62e0c6c787b3e3b2a063940911bf987fc38deebf542400b8bbd9dfeb7d90a8a", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "75aea79d99e5c7edaab0284443b548843371d1d9b55f2d73a1a9092f", "result": "valid", "flags": []}, {"tcId": 58, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04a394d8bf9b479ec3c7ac3fc6a631d01d57d338b9fb5a0ed6e5130e050cfc600cfb08e67727ac5a33345ec1d48d4a9a18516c2203acbd2667", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "8c1d0850691cda7523ffccf1cba44b4d472193e6a3bb0727e490a8b5", "result": "valid", "flags": []}, {"tcId": 59, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04642e26421e96fa88f956d098ac26f02f1d6faa80e460e701a3789a66c38dd95c6b33de8768c85cbe6879d0d77e29fe5a18b26a35cb60c0b6", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "50b9ed4d99e2f24e0096eaeded0b552cf8deff5ca8f976964ae47e92", "result": "valid", "flags": []}, {"tcId": 60, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04f974d1cbbf4171d4773c3e84eab80bc3c6c2858dadcfbd11d64316905df36fbe345f28a3ef663125649474c6fc1ebe175c3865c4469e192b", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "5616ee3e63dfb424d329c2b9b50cf378bb77a8bd7e314a241b5942c7", "result": "valid", "flags": []}, {"tcId": 61, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0455561db3cc8fb08a71654ee9573a1a36a44f0913ca8ad7582cfafbfc62b31e5e78be98ad8c8ceab4bb82e8efc0acb29f1a8d031ed044046c", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "b1da14507b5c05159e15f77d085c017acd89f158011357a97802855d", "result": "valid", "flags": []}, {"tcId": 62, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04a363bcb9bddd5de84a2f4433c039f7be3fce6057b0d3b4a3459e54a2ba32302871e7ba5c3dd7ec9b76946cdc702c15a8d9ec0f4a04e7afb6", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "2f1bd4a5a497481c4a21222320ff61f32674a95d540cc3f4f3ca5849", "result": "valid", "flags": []}, {"tcId": 63, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "043a656d0e25bce27282f256b121fbfcde0a180ccd7aa601a5929fc74002f89e45b4dcb873c56da5d1a28fbca33a126177b217a098e0952e62", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "8c807d65ba7b9fd3061dffef26c025a89524a26b942edd3a984fe51d", "result": "valid", "flags": []}, {"tcId": 64, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04bf5f49ba0086eec289b068b783438ef24b6f28130bb1ed969ef8b041f11b0de95f15edcd835f01bab1f5faaa1749c2ca4f16a7d99d916ff4", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "8fda76f4d124e6727f855e5f4921cc05c48e2a8ed0fee7c75d6a8047", "result": "valid", "flags": []}, {"tcId": 65, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04a57232560d9d604655181f775859b0723d4e01a4c867844eb9d81dabb5d19507bbe9cda3346bad7c184daa432e7f794a5b9b8b8d4e55be3a", "private": "00c1781d86cac2c0af3fb50d54c554a67bd75d25ca796f0486e3fa84f9", "shared": "daf35bb7bf3a056bb62bb01ba00f581c107f64de85842b3a49bc2a4a", "result": "valid", "flags": []}, {"tcId": 66, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "03", "shared": "e71f2157bfe37697ea5193d4732dcc6e5412fa9d38387eacd391c1c6", "result": "valid", "flags": []}, {"tcId": 67, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "fa2664717c7fa0161ec2c669b2c0986cdc20456a6e5406302bb53c77", "result": "valid", "flags": []}, {"tcId": 68, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "01000000000000000000000000000000000000000000000000000000", "shared": "af6e5ad34497bae0745f53ad78ce8b285d79f400d5c6e6a071f8e6bd", "result": "valid", "flags": []}, {"tcId": 69, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "12fd302ff8c13c55a9c111f8bb6b0a13ecf88299c0ae3032ce2bcaff", "result": "valid", "flags": []}, {"tcId": 70, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "0080000000000000000000000000000000000000000000000000000000", "shared": "73f1a395b842f1a6752ae417e2c3dc90cafc4476d1d861b7e68ad030", "result": "valid", "flags": []}, {"tcId": 71, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03d13dd29455c5c2a3d", "shared": "b329c20ddb7c78ee4e622bb23a984c0d273ba34b6269f3d9e8f89f8e", "result": "valid", "flags": []}, {"tcId": 72, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13cd29455c5c2a3d", "shared": "6f48345209b290ffc5abbe754a201479e5d667a209468080d06197b4", "result": "valid", "flags": []}, {"tcId": 73, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13d529455c5c2a3d", "shared": "9f6e30c1c9dad42a153aacd4b49a8e5c721d085cd07b5d5aec244fc1", "result": "valid", "flags": []}, {"tcId": 74, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29445c5c2a3d", "shared": "8cadfb19a80949e61bd5b829ad0e76d18a5bb2eeb9ed7fe2b901cecd", "result": "valid", "flags": []}, {"tcId": 75, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29b7", "shared": "475fd96e0eb8cb8f100a5d7fe043a7a6851d1d611da2643a3c6ae708", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 76, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a37", "shared": "41ef931d669d1f57d8bb95a01a92321da74be8c6cbc3bbe0b2e73ebd", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 77, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "shared": "e71f2157bfe37697ea5193d4732dcc6e5412fa9d38387eacd391c1c6", "result": "valid", "flags": []}, {"tcId": 78, "comment": "edge case private key", "public": "04478e73465bb1183583f4064e67e8b4343af4a05d29dfc04eb60ac2302e5b9a3a1b32e4208d4c284ff26822e09c3a9a4683443e4a35175504", "private": "00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "shared": "11ff15126411299cbd49e2b7542e69e91ef132e2551a16ecfebb23a3", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 79, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffff000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffff000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff00000000000000000000000100000000000000000000000000000000000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff00000000000000000000000100000000000000000000000000000000000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff000000000000000000000001ffffffffffffffffffffffffffffffff000000000000000000000000", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "point is not on curve", "public": "04ffffffffffffffffffffffffffffffff000000000000000000000001ffffffffffffffffffffffffffffffff000000000000000000000001", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "", "public": "", "private": "00c6cafb74e2a5b5ed4b991cbbfbc28c18f6df208b6d05e7a2e6668014", "shared": "", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "invalid public key", "public": "020ca753db5ddeca474241f8d2dafc0844343fd0e37eded2f0192d51b2", "private": "00fc28a0ca0f8e36b0d4f71421845135a22aef543b9fddf8c775b2d18f", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}]}]}