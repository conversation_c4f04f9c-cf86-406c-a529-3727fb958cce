#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title = SM4 test vectors from IETF draft-ribose-cfrg-sm4

Cipher = SM4-ECB
Key = 0123456789ABCDEFFEDCBA9876543210
Plaintext  = 0123456789ABCDEFFEDCBA9876543210
Ciphertext = 681EDF34D206965E86B3E94F536E4246

Cipher = SM4-CBC
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 2677F46B09C122CC975533105BD4A22AF6125F7275CE552C3A2BBCF533DE8A3B

Cipher = SM4-CBC
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 2677F46B09C122CC975533105BD4A22AF6125F7275CE552C3A2BBCF533DE8A3BFFF5A4F208092C0901BA02D5772977369915E3FA2356C9F4EB6460ECC457E7f8E3CFA3DEEBFE9883E3A48BCF7C4A11AA3EC9E0D317C5D319BE72A5CDDDEC640C

Cipher = SM4-CBC
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 2677f46b09c122cc975533105bd4a22af6125f7275ce552c3a2bbcf533de8a3bfff5a4f208092c0901ba02d5772977369915e3fa2356c9f4eb6460ecc457e7f8e3cfa3deebfe9883e3a48bcf7c4a11aa3ec9e0d317c5d319be72a5cdddec640c6fc70bfa3ddaafffdd7c09b2774dcb2cec29f0c6f0b6773e985b3e395e924238505a8f120d9ca84de5c3cf7e45f097b14b3a46c5b1068669982a5c1f5f61be291b984f331d44ffb2758f771672448fc957fa1416c446427a41e25d5524a2418b9d96b2f17582f0f1aa9c204c6807f54f7b6833c5f00856659ddabc245936868c

Cipher = SM4-OFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A7056F2075D28B5235F58D50027E4177D2BCE

Cipher = SM4-CFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A70569ED258A85A0467CC92AAB393DD978995

Cipher = SM4-CTR
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = AAAAAAAAAAAAAAAABBBBBBBBBBBBBBBBCCCCCCCCCCCCCCCCDDDDDDDDDDDDDDDDEEEEEEEEEEEEEEEEFFFFFFFFFFFFFFFFEEEEEEEEEEEEEEEEAAAAAAAAAAAAAAAA
Ciphertext = C2B4759E78AC3CF43D0852F4E8D5F9FD7256E8A5FCB65A350EE00630912E44492A0B17E1B85B060D0FBA612D8A95831638B361FD5FFACD942F081485A83CA35D

Title = SM4 GCM test vectors from RFC8998

Cipher = SM4-GCM
Key = 0123456789abcdeffedcba9876543210
IV = 00001234567800000000abcd
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 83de3541e4c2b58177e065a9bf7b62ec
Plaintext = aaaaaaaaaaaaaaaabbbbbbbbbbbbbbbbccccccccccccccccddddddddddddddddeeeeeeeeeeeeeeeeffffffffffffffffeeeeeeeeeeeeeeeeaaaaaaaaaaaaaaaa
Ciphertext = 17f399f08c67d5ee19d0dc9969c4bb7d5fd46fd3756489069157b282bb200735d82710ca5c22f0ccfa7cbf93d496ac15a56834cbcf98c397b4024a2691233b8d

Title = SM4 CCM test vectors from RFC8998

Cipher = SM4-CCM
Key = 0123456789abcdeffedcba9876543210
IV = 00001234567800000000abcd
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 16842d4fa186f56ab33256971fa110f4
Plaintext = aaaaaaaaaaaaaaaabbbbbbbbbbbbbbbbccccccccccccccccddddddddddddddddeeeeeeeeeeeeeeeeffffffffffffffffeeeeeeeeeeeeeeeeaaaaaaaaaaaaaaaa
Ciphertext = 48af93501fa62adbcd414cce6034d895dda1bf8f132f042098661572e7483094fd12e518ce062c98acee28d95df4416bed31a2f04476c18bb40c84a74b97dc5b
