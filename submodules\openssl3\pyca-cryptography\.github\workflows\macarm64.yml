name: macOS arm64 CI
on:
  push:
    branches:
      - main
      - '*.*.x'
    tags:
      - '*.*'
      - '*.*.*'

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  macos-arm64:
    if: github.repository_owner == 'pyca'
    runs-on: [self-hosted, macos, ARM64]
    name: "macOS arm64"
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - {TOXENV: "py310", BIN_PATH: '/Library/Frameworks/Python.framework/Versions/3.10/bin/python3'}
    timeout-minutes: 10
    steps:
      - name: "Delete workspace" # self-hosted runners need this, sigh
        run: gfind ! -name '.' ! -name '..' -delete
      - uses: actions/checkout@v3.0.2
        with:
          persist-credentials: false
      - uses: actions/cache@v3.0.8
        with:
          path: |
            src/rust/target/
          key: ${{ runner.os }}-${{ matrix.PYTHON.TOXENV }}-cargo-macarm64-${{ hashFiles('**/Cargo.lock') }}

      - uses: actions/checkout@v3.0.2
        with:
          repository: "google/wycheproof"
          path: "wycheproof"
          ref: "master"
      - name: Setup venv and install deps
        run: |
          $BIN_PATH -m venv venv
          venv/bin/python -m pip install tox requests
        env:
          BIN_PATH: ${{ matrix.PYTHON.BIN_PATH }}
      - name: Download OpenSSL
        run: |
          venv/bin/python .github/workflows/download_openssl.py macos openssl-macos-universal2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Tests
        run: |
          CRYPTOGRAPHY_SUPPRESS_LINK_FLAGS=1 \
            LDFLAGS="${HOME}/openssl-macos-universal2/lib/libcrypto.a ${HOME}/openssl-macos-universal2/lib/libssl.a" \
            CFLAGS="-I${HOME}/openssl-macos-universal2/include -Werror -Wno-error=deprecated-declarations -Wno-error=incompatible-pointer-types-discards-qualifiers -Wno-error=unused-function $EXTRA_CFLAGS" \
            venv/bin/tox -r --  --color=yes --wycheproof-root=wycheproof
        env:
          TOXENV: ${{ matrix.PYTHON.TOXENV }}
          CARGO_TARGET_DIR: ${{ format('{0}/src/rust/target/', github.workspace) }}
