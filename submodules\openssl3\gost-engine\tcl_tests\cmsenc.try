#!/usr/bin/tclsh
lappend auto_path [file dirname [info script]]
package require ossltest
cd $::test::dir
set testname [file rootname [file tail $::argv0]]

start_tests "Тесты на cms -encrypt"
proc make_fn {alg} {
	return [string map {":" "_"} $alg]
}

proc map {str list} {
	set result {}
	foreach a $list {
		lappend result [subst $str]
	}
	return $result
}

if {![file exist encrypt.dat]} {
	makeFile encrypt.dat [string repeat "Test data to encrypt.\n" 100]
}

if {![info exist env(NO_RSA)]} {
test "Creating RSA CA" {
	makeCA ${testname}CA-RSA rsa:512
} 0 1

foreach user {U_cms_enc_rsa_1 U_cms_enc_rsa_2} {
test "Make registered user $user" {
	makeRegisteredUser $user rsa:512 CAname ${testname}CA-RSA
} 0 1
}


test -createsfiles cms_enc_rsa.msg "RSA User 1 encrypts message for RSA user 2" {
	openssl "cms -encrypt -in encrypt.dat -aes-128-cbc -out cms_enc_rsa.msg U_cms_enc_rsa_2/cert.pem"
	file isfile cms_enc_rsa.msg
} 0 1

test "RSA User 1 cannot decrypt message for RSA user 2" {
	grep "Error"	[openssl "cms -decrypt -in cms_enc_rsa.msg -recip U_cms_enc_rsa_1/cert.pem -inkey U_cms_enc_rsa_1/seckey.pem"]
} 1 {Error decrypting CMS}

test -createsfiles cms_decrypt.rsa "RSA User 2 (with cert) can decrypt message for RSA user 2" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -in cms_enc_rsa.msg -recip U_cms_enc_rsa_2/cert.pem -inkey U_cms_enc_rsa_2/seckey.pem -out cms_decrypt.rsa"
	set result [getFile cms_decrypt.rsa]
	string eq $expected $result
} 0 1

test -createsfiles cms_decrypt_nocert.rsa "RSA User 2 (without cert) can decrypt message for RSA user 2" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -in cms_enc_rsa.msg -inkey U_cms_enc_rsa_2/seckey.pem -out cms_decrypt_nocert.rsa"
	set result [getFile cms_decrypt_nocert.rsa]
	string eq $expected $result
} 0 1
}

test "Creating CA 2001" {
	makeCA ${testname}CA gost2001:A
} 0 1

test "Creating CA 2012" {
	makeCA
} 0 1

if {[info exist env(ENC_LIST)]} {
	set enc_list $env(ENC_LIST)
} else {
	switch -exact [engine_name] {
		"ccore" {set enc_list {gost2001:XA:1.2.643.******** gost2001:XB:1.2.643.******** gost2001:XA: gost2012_256:XA:1.2.643.******** gost2012_256:XB:1.2.643.*******.1.1 gost2012_256:XA: gost2012_512:A:1.2.643.******** gost2012_512:B:1.2.643.*******.1.1 gost2012_512:A:}}
		"open" {set enc_list {gost2001:XA:1.2.643.******** gost2001:XB:1.2.643.******** gost2001:XA: gost2012_256:XA:1.2.643.******** gost2012_256:XB:1.2.643.*******.1.1 gost2012_256:XA: gost2012_512:A:1.2.643.******** gost2012_512:B:1.2.643.*******.1.1 gost2012_512:A:}}
	}
}

save_env2 {OPENSSL_CONF CRYPT_PARAMS}
makeFile cmsenc1.cnf [regsub -all "\n\\s*CRYPT_PARAMS\\s*=\[\^\n]*" [getConfig] ""]
set ::env(OPENSSL_CONF) [file join [pwd] cmsenc1.cnf]

foreach enc_tuple $enc_list {
	if {![regexp {^([^:]*:[^:]*):(.*)$} $enc_tuple -> alg crypt_param]} {
		set alg $enc_tuple
		set crypt_param {}
	}
	set alg_fn [make_fn $enc_tuple]
	set username U_cms_enc_$alg_fn
	switch -glob $alg {
		gost2012* {set ::test::ca ${testname}CA-2012}
		* {set ::test::ca ${testname}CA}
	}

test "Creating user $username with key $alg" {
	makeRegisteredUser $username $alg

	if {![file exists $username/req.pem]&&[file exists $username/cert.pem]} {
		file delete $username/cert.pem
	}
	file exists $username/cert.pem
} 0 1

if {[string length $crypt_param]} {
	set env(CRYPT_PARAMS) $crypt_param
} else {
	if {[info exists env(CRYPT_PARAMS)]} {unset env(CRYPT_PARAMS)}
}

test -createsfiles cms_enc_$alg_fn.msg "Encrypting for $username" {
	grep "rror" [openssl "cms -encrypt -in encrypt.dat -gost89 -out cms_enc_$alg_fn.msg U_cms_enc_$alg_fn/cert.pem"]
} 0 ""

if {[info exists env(CRYPT_PARAMS)]} {unset env(CRYPT_PARAMS)}

test -createsfiles cms_enc_$alg_fn.pem "Extracting CMS from encrypted structure for $username" {
	openssl "cms -cmsout -out cms_enc_$alg_fn.pem -outform PEM -in cms_enc_$alg_fn.msg"
	file isfile cms_enc_$alg_fn.pem
} 0 1

test -skip {![file exists cms_enc_$alg_fn.pem]} "Checking oids in pkcs7 structure for $username" {
	extract_oids cms_enc_$alg_fn.pem PEM
} 0 [mkObjList [alg_long_name $alg] [pubkey_long_name $alg] [param_hash_long_name [param_hash $alg]] "GOST 28147-89" [encr_long_name $crypt_param]]

test -createsfiles cms_decrypt.$alg_fn "Decrypting file encrypted for $username" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -debug_decrypt -in cms_enc_$alg_fn.msg -recip U_cms_enc_$alg_fn/cert.pem -inkey U_cms_enc_$alg_fn/seckey.pem -out cms_decrypt.$alg_fn"
	set result [getFile cms_decrypt.$alg_fn]
	string eq $expected $result
} 0 1

if {[string length $crypt_param]} {
	set env(CRYPT_PARAMS) $crypt_param
} else {
	if {[info exists env(CRYPT_PARAMS)]} {unset env(CRYPT_PARAMS)}
}

test -createsfiles cms_enc_t_$alg_fn.msg "Encrypting for $username - text format" {
	grep "rror" [openssl "cms -encrypt -text -in encrypt.dat -gost89 -out cms_enc_t_$alg_fn.msg U_cms_enc_$alg_fn/cert.pem"]
} 0 ""

if {[info exists env(CRYPT_PARAMS)]} {unset env(CRYPT_PARAMS)}

test -createsfiles cms_decrypt_t.$alg_fn "Decrypting file text-encrypted for $username" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -debug_decrypt -text -in cms_enc_t_$alg_fn.msg -recip U_cms_enc_$alg_fn/cert.pem -inkey U_cms_enc_$alg_fn/seckey.pem -out cms_decrypt_t.$alg_fn"
	set result [getFile cms_decrypt_t.$alg_fn]
	string eq $expected $result
} 0 1

test -createsfiles cms_decrypt_t_nocert.$alg_fn "Decrypting file text-encrypted for $username without cert" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -debug_decrypt -text -in cms_enc_t_$alg_fn.msg -inkey U_cms_enc_$alg_fn/seckey.pem -out cms_decrypt_t_nocert.$alg_fn"
	set result [getFile cms_decrypt_t_nocert.$alg_fn]
	string eq $expected $result
} 0 1

}


test -createfiles cms_enc_4all "Encrypt for all" {
	grep "rror" [openssl "cms -encrypt -in encrypt.dat -gost89 -out cms_enc_4all.msg [map {U_cms_enc_[make_fn $a]/cert.pem} $enc_list]"]
} 0 ""

foreach enc_tuple $enc_list {
	if {![regexp {^([^:]*:[^:]*):(.*)$} $enc_tuple -> alg crypt_param]} {
		set alg $enc_tuple
		set crypt_param {}
	}
	set alg_fn [make_fn $enc_tuple]
	set username U_cms_enc_$alg_fn

test -skip {![file exists cms_enc_4all.msg]} -createsfiles cms_decrypt_4all.$alg_fn "Decrypting gost-encrypted file, recipient $alg_fn" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -in cms_enc_4all.msg -recip $username/cert.pem -inkey $username/seckey.pem -out cms_decrypt_4all.$alg_fn"
	set result [getFile cms_decrypt_4all.$alg_fn]
	string eq $expected $result
} 0 1

test -skip {![file exists cms_enc_4all.msg]} -createsfiles cms_decrypt_4all_nocert.$alg_fn "Decrypting gost-encrypted file without cert, recipient $alg_fn" {
	set expected [getFile encrypt.dat]
	openssl "cms -decrypt -debug_decrypt -in cms_enc_4all.msg -inkey $username/seckey.pem -out cms_decrypt_4all_nocert.$alg_fn"
	set result [getFile cms_decrypt_4all_nocert.$alg_fn]
	string eq $expected $result
} 0 1

}

restore_env2 {OPENSSL_CONF CRYPT_PARAMS}

end_tests
