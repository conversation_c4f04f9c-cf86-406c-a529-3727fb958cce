{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 446, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "wx": "2da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa", "wy": "4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMH<PERSON>wEAYHKoZIzj0CAQYFK4EEACIDYgAELaV92hCJJ2pUP5/9rAv/DZdsrXHrcoDn\n2b/Z/uS9svIPR/+IgnQ4l3LZjMV1ITiqS20FTWnc8+JexJ34cHFeNIg7GDYZfXb4\nrZYuePZXG7x0B7DWCR+eTYjwFCdEBhdP\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202307b0a10ee2dd0dd2fab75095af240d095e446faba7a50a19fbb197e4c4250926e30c5303a2c2d34250f17fcf5ab3181a6", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "30650230814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2023084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "308166023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "30820066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3067023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "30850100000066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "3089010000000000000066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3066028000814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202800084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30680000023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "306b4981773066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "306a25003066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30683066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "306b2236498177023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "306a22352500023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "306e2233023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20004deadbeef02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "306b023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2223649817702310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e22235250002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "306e023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2223302310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "306eaa00bb00cd003066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "306caa02aabb3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "306e2239aa00bb00cd00023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c2237aa02aabb023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "306e023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e22239aa00bb00cd0002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e22237aa02aabb02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30803066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "306a2280023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2000002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2228002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30803166023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "306a2280033100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2000002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2228003310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e66023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f66023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3166023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3266023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff66023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "306a30010230653100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "30653100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd00", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd05000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30683000023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd3000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3069023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cdbf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "30683066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "3033023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "308199023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "306702813100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3067023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20281310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30680282003100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2028200310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3066023200814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3066023000814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202320084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202300084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "306b0285010000003100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "306b023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2028501000000310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306f028901000000000000003100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306f023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202890100000000000000310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "306a02847fffffff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202847fffffff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "306a0284ffffffff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20284ffffffff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "306b0285ffffffffff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "306b023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20285ffffffffff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306e0288ffffffffffffffff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306e023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20288ffffffffffffffff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "306602ff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202ff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "303302310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "30340202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3034023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3068023300814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2000002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202330084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30680233000000814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2023300000084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2000002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3068023300814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2050002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3068023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202330084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd0500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3035028102310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3035023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3035050002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3035023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066003100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066013100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066033100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066043100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066ff3100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e200310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e201310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e203310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e204310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2ff310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3035020002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3035023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "306a22350201000230814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "306a023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e22235020100023084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3066023102814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310284f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a156202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a74d", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "3065023000814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a1502310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202300084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30670232ff00814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "3067023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20232ff0084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "303609018002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3036023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "303602010002310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3036023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023101814cc9a70febda342d4ada87fc39426f403d5e8980842845d38217e2bcceedb5caa7aef8bc35edeec4beb155610f3f5502310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650230814cc9a70febda342d4ada87fc39426f403d5e898084284644bb7cded46091f71a7393942ad49ef8eae67e7fc784ec6f02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30660231ff7eb33658f01425cbd2b5257803c6bd90bfc2a1767f7bd7b9f3e1359f376840298d725eb98c7ab98c282d68156bb5ea1e02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502307eb33658f01425cbd2b5257803c6bd90bfc2a1767f7bd7b9bb4483212b9f6e08e58c6c6bd52b610715198180387b139102310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30660231fe7eb33658f01425cbd2b5257803c6bd90bfc2a1767f7bd7ba2c7de81d4331124a3558510743ca12113b414eaa9ef0c0ab02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023101814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502307eb33658f01425cbd2b5257803c6bd90bfc2a1767f7bd7b9f3e1359f376840298d725eb98c7ab98c282d68156bb5ea1e02310084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310184f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e5fd3ad1cb7a61dc9507f6eeb2a65341ad0cac035dfee58d140", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e2023084f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e6044e681b3bdaf6d91cf3acfc5d3d2cbdaf0e8030a54ce7e5a", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20231ff7b0a10ee2dd0dd2fab75095af240d095e446faba7a50a19ff3b630ca4e19648ed8ab2287e37c8caa222be38ade6c5833", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e20231fe7b0a10ee2dd0dd2fab75095af240d095e446faba7a50a1a02c52e34859e236af809114d59acbe52f353fca2011a72ec0", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3066023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202310184f5ef11d22f22d0548af6a50dbf2f6a1bb9054585af5e600c49cf35b1e69b712754dd781c837355ddd41c752193a7cd", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023100814cc9a70febda342d4ada87fc39426f403d5e89808428460c1eca60c897bfd6728da14673854673d7d297ea944a15e202307b0a10ee2dd0dd2fab75095af240d095e446faba7a50a19ff3b630ca4e19648ed8ab2287e37c8caa222be38ade6c5833", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020100023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036020101023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529730201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529720201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc529740201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000001000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3038023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3036023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3637323636", "sig": "3066023100ac042e13ab83394692019170707bc21dd3d7b8d233d11b651757085bdd5767eabbb85322984f14437335de0cdf5656840231008f8a277dde5282671af958e3315e795a20e2885157b77663a67a77ef2379020c5d12be6c732fd725402cb9ee8c345284", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "33393439313934313732", "sig": "3065023100d51c53fa3e201c440a4e33ea0bbc1d3f3fe18b0cc2a4d6812dd217a9b426e54eb4024113b354441272174549c979857c02300992c5442dc6d5d6095a45720f5c5344acb78bc18817ef32c1334e6eba7726246577d4257942bdefe994c1575ed15a6e", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "35333637363431383737", "sig": "3065023100c8d44c8b70abed9e6ae6bbb9f4b72ed6e8b50a52a8e6e1bd3447c0828dad26fc6f395ba09069b307f040d1e86a42c022023001e0af500505bb88b3a2b0f132acb4da64adddc0598318cb7612b5812d29c2d0dde1413d0ce40044b44590e91b97bacd", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "35363731343831303935", "sig": "3065023100d3513bd06496d8576e01e8c4b284587acafd239acfd739a19a5899f0a00d269f990659a671b2e0e25f935b3a28a1f5fd0230366b35315ce114bffbb75a969543646ee253f046a8630fbbb121ecc5d62df4a7eb09d2878805d5dab9c9b3880b747b68", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "3131323037313732393039", "sig": "3065023100b08c4018556ca8833b524504e30c58346e1c0345b678fdf91891c464a33180ed85a99bc8911acf4f22aceb40440afc9402304a595f7eed2db9f6bd3e90355d5c0e96486dc64242319e41fc07be00a732354b62ec9c34319720b9ffb24c994b1cf875", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3131323938303334323336", "sig": "306502302b08f784617fd0707a83d3c2615efa0c45f28d7d928fc45cd8a886e116b45f4686aee97474d091012e27057b6ba8f7e6023100c440aa6ecb63e0d43c639b37e5810a96def7eec8e90a4c55e5b57971c48dfb4e850232fbb37bd32bb3b0523b815ff985", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "39383736303239363833", "sig": "306402300609f4ec120c8838bda916f668e9600af7652e1d3f7182734f97f54da5d106bbfd216c32f227b76d583de1c53949b2ee023046926dffc766ff90c3b921b3e51a2982a1072314c1fdfb4175de7adea5a6f97bdff587a473504a9c402aac7c05bd4785", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "3230323034323936353139", "sig": "306502305ae2220e4716e1ef0382afcc39db339e5bd5f05e8a188d4a5daaab71c6c35263ee8820a34558092877449ebb15898c5c023100c4d38e2e85451c43ee35b0c56196cbf3059acf2b8b529f06dc1de9b281d9b0f3f3983df8936e944ab0b18330a342ee88", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31343531363639313830", "sig": "3065023051fb84ed71d436c737ab24e2a45c68f8f623748be2caebd89e02bfc89309b8350042ab1b97849b9f680f044a58765175023100d4a8f60791657a8c12985fd896ac77e7d95cb050582f2466471dc2c6dcf90db05ce34beadbfcfe690dc56c0cc9944007", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "31303933363835393531", "sig": "3065023040159290d161df6b3f81a92cefb6df56149d588e7b886bf24939f5c8b6bb515d325b3764f0ed284a77fa9081ccfa5237023100bd55dfb47709287ce7b88dfd96ac7543eeba9bd31b8c91f203d2b90418122406399c80a53539b81f1cb60fa3b23a2563", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "36323139353630323031", "sig": "3066023100d7fb9f53865cdf9d4cad6f66981aea35a1454858ceb678d7b851c12a4c6644fe1915a4b219b51389a5ae2c98a433cc3a02310094ad75c3dea88740205cab41032dfe149341cf4ee94dcd2f0c8bbe5af5860b30b5e1f764b2c767b09fd10761050c989c", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "35363832343734333033", "sig": "30650230157ef8f85cdb9257983d06a7f29674752659097364b401e701705b3bd9ead884fd32141320ae76ae05f6fc7ec155d6c2023100ccadc3851020e41dd91bc28a6c073409136a47f20b8dbf2553fd456a8ed5fa7e73e4ec59dca499e0d082efbb9ad34dc7", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "33373336353331373836", "sig": "3066023100e763001769c76f6a6d06fad37b584d7f25832501491bec283b3b6836f947dc4e2cef021c6c6e525b0a6a3890d1da122a023100acbd88729cce3992d14ec99e69ff0712b82a33a1c1e8b90e1399c66fe196f7c99bdb3ff81db77dc25ae6f0c1a025117d", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "34373935393033373932", "sig": "3066023100c6425b6b046ec91ebc32b9e6de750e5d3d36d4ddc6dffd25ba47817385a9466f6fc52259c7d02c66af5bf12045b5659d02310084cdc06e35fecc85a3e00b16488eac3584942f663d8b59df111c0650139d7cda20d68dccae569d433170d832147bc94c", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "39333939363131303037", "sig": "306502303061f090e4932133a0e08ac984d1c8d8d4f565e21cf15427671503880341265cd44f35a437ee3c3a8857579dd7af0c3502310093ae374a0f63dcbe41a1b7b07a50faf2b33f35e0b6600bb36aa5cda05238640fa35c635c0fa78e1410f3a879bbb8a541", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "31303837343931313835", "sig": "306502300ccc627f35454cc84e08a828f5bd5f5e41eeeaa40475bcc2e71ff372e8c718a5e179d3b7f2d7051db9060c4c978eb638023100b12d0240afbdfc64c60861548c33663b8960316a55f860cc33d1908e89aa6fc9519f23a900e0488fa6a37cfb37856565", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323336363738353030", "sig": "3065023100e72419fb67ebbcc0de9c46ce5475c608f9de7e83fc5e582920b8e9848000d820d393fdac6c96ea35ce941cb14951640002306aa19934ef60f4a247bc261ba256283a94857a268f42a0939c95a536fbd4f8e1f1c285a7b164c12213abb9e3393cbe9f", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "31343438393937373033", "sig": "30660231008b740931f9afa8a04c08cde896b7fdd9aca3177d5e4a3e5a51e54bfa824b66ab11df4e90f49798d644babfede7830224023100afd91e7ce15059a5b5499e5aef4afa91fd090e4e5029b3f4348f0d4349df11745869f9255117eea405a78af5dd6a646d", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35373134363332383037", "sig": "3066023100989024bce204a7539fbd2b185ecf375590d873177c1ff26bbf755838ae5bcde180054663702ac3a4e68fe8b58fd88c70023100bdbedf64e424dbd7f979f83adef3fc85077fa76f8b1724815b5b8c24fde7fbd72f4b369a415d9bbf565cdc459bdce54c", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "323236343837343932", "sig": "3064023022624fc23403955c0c9f5b89871177fa53879c8424de3b4ab1bcbcddc6e57b870b0491b848e19f728722b3163f4aa32802305bb82642cdaa84d6977fb95b3ede4ec7f2d54881cf435636d3509816f13ebb7be24fd7d4e1e81fddf07bde685e8d630d", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "35333533343439343739", "sig": "3065023100da5a2daa7437df4566ebba6ac5ed424655633e354ef4d943dc95ddefb0dae69f3616e506cc8cb5bc433a82ba71f6feb402305107b24041bba45073ce54488a5aef861e7805bbb8f970aedc1c59149cfe72c7025e2d117337e8677c88ef43374e6907", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "34373837333033383830", "sig": "306402302b0659fb7fa5fc1fce767418c20978de9a6a59941fc54f8380619b2ab2a7d6039de5373fbb503c24f2ce38e9c57995de02300d94dba98dd874bfffeac96a9295b6ab667708b8e33252edc029574c484a132135b13e52db6f877987c1be4f51fca193", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32323332313935383233", "sig": "306402304a5a14f1ecf053bf3ec14843db8c7dd153e9545d20d76345a9e1d1a8fcb49558ca1ee5a9402311c2eaa102e646e57c2c02301573b8b4b633496da320e99a85c6f57b7ee543548180a77f7fced2d0665911cb4cde9de21bc1a981b97742c9040a6369", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "3130373339333931393137", "sig": "30650230104e66e6e26c36633c0af001f0d9a216236816923ec93b70bea0a8ff053a15aaaef5fe3483e5cc73564e60fe8364ce0e023100ec2df9100e34875a5dc436da824916487b38e7aeb02944860e257fd982b01782b3bd6b13b376e8a6dbd783dfa0d77169", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "31383831303237333135", "sig": "306402304b06795da82bda354e8d9422a76c7bc064027fcdd68f95b7bc6177a85b2d822c84dc31cb91fc016afa48816a3a019267023018e31018e312d3dd3dd49ec355fdb0def3bb3e44393c26cf1bc110b23a3aacf6c442bfcec5535ce37527d0e068f75c03", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "36303631363933393037", "sig": "3066023100ad75ca5a3df34e5a6d3ea4c9df534e8910cfb1d8c605fc398fbee4c05f2b715bd2146221920de8bac86c2b210221bcff023100a322d3df3bb2cf9e4215adf1ff459e70f2f86bec6dd6af5d04ae307d21ed5955136c8e258fdc0f9cbd6cf89c31aa691f", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "38383935323237303934", "sig": "3065023100b0fa6289cc61bab335932ea1ac6540462653cc747ef67827825f77689a4398602297835d08aa16e23a76dea9f75404ef0230278d654a0b50c57d13f9c9c8c7c694001167f8e3b71491772a7427f1410fb6de518740c22e455e58de48846479b300cc", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "31353830323334303934", "sig": "3065023100c216cb4fe97facb7cd66f02cd751155b94fa2f35f8a62ba565aca575728af533540ff5d769b7c15c1345ab6414e150680230278a8a372b75d6eb17a4f7c7f62d5555c7357a1a047026bead52185cbcc01d73b80a1577e86220b2278da2b1ee8c983a", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "33393635393931353132", "sig": "30660231009591c80453cffbcd0b8d6d20fce0cbb2a458e54aed7ba1c767e6c017af4c4aa07a76859c0b249f6692a3c9ace893f14e023100893b567cd2959cd60557d3d6013d6e1741421a6edc5bc18244b3e8d7744e57928ce006a3fbd6e6324cb8ea3e5177e7e3", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "32323838373332313938", "sig": "30650230350b5515ba9785f149e2a566c14f4178757bb325179888f526f7db11161aedcd752551381316c2713f5de21d3d517af002310097d48a90c3bb3444736bec69db0649f82428b39238ada6048a0bead84f2f3b73816b48fed4d57b5f87a194ce4004ed7b", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "32323330383837333139", "sig": "3066023100833210c45d2448d9a4d69622d6f2193e64c65c79d45d62e28f517ca5c68eef05a2e98b1faed4cc87cbdbec6fe6bb8987023100b777b44cd30e6a049dc56af19a251d955c1bbab0c307fe12e9e5382fd48c173db0292f0b1047da28ee18518e11688eea", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "313239303536393337", "sig": "306402307728ef10d9d5f3f32132716e6b403926929b05201700658d4b7f25a0692f153b8d666fd0da39888ab6234212659268d0023055df9466ee2c98225a2b0c4ff77622f9d11b4e48aa7f9279cdc2e245fdd9b9f4282106e25a458ff618bc3ca9422bea25", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32373438363536343338", "sig": "30640230552040701dba17be3b4d5d6e136ce412b6a4c50ce1ee53415d8100c69a8ee4726652648f50e695f8bb552d0df3e8d1c402301374972b2f35b2fd86d45ed0c9358b394e271575e429ac8aa60eb94b9df7e755d9317fb259269e9d3b1db8d48d91dc7e", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "37353833353032363034", "sig": "3065023100fe6ef07056ce647128584bec156b68b8005f42d8c85dfb122134c488cc0e72cf8f06700417d7ff694b45e894ec23cbbd02307f5e33c5bfa697c144d440b32d06221f630a9ccaa8e9a0489490c04b86e8daae0e41d2466429b4b3cc1d37348e36cc0b", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "32333237373534323739", "sig": "3065023100e009fc1a13d282bd37f10693350a5b421a0039713d29cb9e816e013c173bd1ec2bd6eb6bd88429023ee3d75d9a5ec06f02300b8bd481982a6e52355bcde5fe0092abac41f0543c31d1928b9a585e63e9520e24a65f46db2696e1b85a65c4e5240879", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "373735353038353834", "sig": "3065023100acee00dfdfcee7343aeffa8514b11020c5435027887529d255bdbd45a90f160c68f05bd4b567daa8fa14e5807f5167a402301c9fdf546190970aa33121a3043280669be694e5f700b52a805aa6101b4c58f0467e7b699641d1d03f6229b2faf4253f", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "3137393832363438333832", "sig": "30650231008a4ee1e3bb251982475877d18763fafcf49ccc8b0fec1da63b0edccbb8d3e38608a2e02d0d951031179e12ac899d30c3023073cb62ad7632cd42dff829abfbfcb6165207e3708ed10043c0cdee951c7f8012432696e9cf732dcbadb504630648419f", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "32333936373737333635", "sig": "306402303903b59f837ff5f41f42cbe3e2fc8e17d859cbb35386c4327d3947fb012b3629fea911c83cefdbd503aebbcc1114afd102300e5be9094b5a22ade00c24644f476baad0f7741dfb2ce9644a1c45769404f8dccc522017c2b8cc630f1a0ef5fee99fe8", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "35393938313035383031", "sig": "306502307717ffc8d0811f357299423c56ec181c58f1981f5c1dd4f346f6a2ad71d3582e203a11e8609c1146ff3247a1820f832c02310096c89ec707da3cd8b09084b065e3265327a536a974c4285155388011e348f2e7f005ae7e3e502732fc2971ac13fd72c0", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "3136363737383237303537", "sig": "3065023100a21519ce3533c80826f1e47fa9afde7096151144291134421990285a8d89a8c2d4afdadd547a923dcc17bfcdd0e9ffb9023040577245dd2e022c8ed8b5de7b8c26f31307429a7a64e5729311cc4128e3b486867e61b4a8a1cd0731792eb1466d08f3", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "323036323134333632", "sig": "3065023100a727addad0b2acd2942cb1e3f7b2917ca65453275198b06436a993bfc982d3f54620c395e253d57b8fe026efcf7252f902307a19811aa4c12c45c3c041e7c614d0d98051ca7a0c57a9a107d552793ba1d0debb373525aafcc13ae1acd50a42a89adf", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "36383432343936303435", "sig": "3065023022287277872d175d8a3ff5be9818658f845eb9c1b2edc093ae82a75aa31cc26fe1771b4bfbd4c320251388d7279b5245023100b47d1833867e889fcfd7ac171855293a50aa6db24c6522e374fe87be12bf49b13c8b5e1455a2f25aa7912f799eebe552", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "33323639383937333231", "sig": "3065023100a0f41362009b8e7e7545d0f7c4127e22d82ac1921eb61bf51e9ea711e41557a84f7bb6ace499a3bc9ebca8e83728787b02301f6e0c15a3e402370885e2aceb712280ebc45b63986357765b7e54b06cd00db8308e4715c39d48d246030bf960e6a2ff", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31333837333234363932", "sig": "306502304144e1c6ad29ad88aa5472d6d1a8d1f15de315f5b281f87cc392d66d7042547e6af7c733b31828f89c8a5dafce5bb9af023100f5d0d81f92428df2977757c88ba67f9e03abd4c15b1e87fa1dd49e601a9dd479e7c3dc03a8bfea60fcfc1c543931a7de", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "34313138383837353336", "sig": "306402305f177fc05542be6e09027b7eac5eb34f34fc10ad1429e4daaea75834de48dd22626f2bf653dfcc46234921d19b97406b02307def6c993a87560425f2c911046357c4b1c4c376bfa22bb45d533654fea6f565ba722147b2269ea7652f9c4af62ed118", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "393838363036353435", "sig": "3066023100bd77a8ff0cd798d8f6e75dfbbb16c3ee5bf3f626dcb5abdfd453b301cb4fd4caee8e84dd650a8b4cf6655dea163788c7023100ef8f42394469eb8cd7b2ac6942cdb5e70dd54980ad8c0c483099573d75b936880459c9d14f9e73645865a4f24ee2c4ce", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "32343739313135383435", "sig": "3066023100a02e2196258436da6a35a2f73cf6b08880f27757566ce80c7fc45f5dcbaec62d3fcebb784b4a650e24c1a997e4b971f7023100f1195d2ba3321b6938e04169d7baf605001b6311f08a5e82157a7675d54993f2fd1e41f8c84fc437a1a139d2e73e8d46", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "35303736383837333637", "sig": "30640230686c5dfe858629125fdee522b77a9b9be5e03a347d79cb4c407f17fd25c97293cd99711f33e77814bd30d2453d3a86c10230509ac9b18c1b2b5a2b1b889d994b950743a988c2fcfb683e89211a43da6ee362c2e414d84fe82db1904b81701c257822", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "393838353036393637", "sig": "306502310083ce818ecd276432a8ddfe75406d01329e76d7586cd6f611c1fe1a0913ad80014c2156381942d58dd6356e44ccdc52a8023036a35983b97a9ae2a19cf05ba947dd880c973d5c78f9676ebbcb0b40d639124030c137236232f1fad15afd71c52ad8ec", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "32373231333036313331", "sig": "306502307cb78ebb712b5a2e0b0573d28440a5da36bd2338805d90ef3b0c1178ae613be8ae8bf548af4e7403e5a5410462afc2e30231008631a82cbdb8c2c7df70f012405f06ad0ab20d6c4fbceb3e736f40fdff1a8e5f6e667a0e77259f277494de84ec0de50d", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "33323034313031363535", "sig": "306602310085110fe21156b7764b91bcb6cf44da3eb21d162395071c216a13b5920d67a31aaa20dfc4669cf32c04964d0831bcdc29023100e19187033d8b4e1edf7ab8eaaae1e13c80c0c4db51d921ccf62f424524cbd530d07de2cf902a0ecda5e01206ae61e240", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33313530363830393530", "sig": "306402300fd621a892ee5a3eb0bcb80f3184714a6635f568d92f41ad8d523887d5b82d2b930eb5ff2922fda1a3d299f5a045837f02301278725a607fa6f2fc7549b0de816fe2f88e3a1ec1ccaf9fb58e70a0f6646c2d7aad6e4f73d116e73096bdef231d0c89", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31373237343630313033", "sig": "3066023100802cbe405d3ce9663b0b13c639aa27730b3377ce42521098ae09096b7fc5e7ac998b6994344e89abfb50c05476f9cae80231009aa7258c0dc4eff4b2d583575368301e2a7865cfaa3753055a79c8b8e91e94496a5d539181c2fd77941df50fe87453cd", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "3134353731343631323235", "sig": "3066023100859b0446949d7f78a0301ac4cc02b599a758fd1be006bf1a12570015869e59b9a429ce1c77a750969f49e291f6ab899402310099a812a1acc2c646814315cf9b6290d2232236cdf131f9590088e75a55786cdfc9d9027ec70056408ab55445fd79fe60", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "34313739353136303930", "sig": "3065023100dbcc7ee9fa620e943193deae3f46b3142779caa2bce2df79a20639c8d01bce414a61f72764c1ec949c945320f5ee2a1d02301d9879787b880bd05db39bac07bfe3e7d0792932144e211e81f21da9621b83bff11bc52bcc7cb40cf5093f9bad8650fb", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "35383932373133303534", "sig": "306402307a1f9fbd0f6e776c3e3a3c798f5c0d9e20f0e2f3f4d22e5893dd09e5af69a46abc2f888d3c76834462008069275dfeb9023045e6d62a74d3eb81f0a3a62902b8949132821b45d8e6cad9bb3d8660451727cdf7b332a9ac7bb04604991312143f8a6a", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "33383936313832323937", "sig": "30640230047962e09e1b61823d23726bf72b4dde380e032b534e3273db157fa60908159ab7ee4cadce14fd06ebe8e08e8d8d5a0702301892f65ee09e34ce45dd44b5a172b200ce66b678b0e200c17e424e319f414f8dfbb2769a0259c9cc105191aa924e48d5", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "38323833333436373332", "sig": "30660231008f02799390ab861452cd4949942cbbcc25cad7c4334c4bc6146fbef8ad96c86f923fbf376d9ab79073e5fcb663f1ea91023100ce15d9862d100ff95ad7368922eec3f6d7060ce412c01ff13870aa61626ee49edf39bb27005ecbe406bb6825f74c0438", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "33333636393734383931", "sig": "306502301879c4d6cf7c5425515547575049be2a40c624a928cf281250f8bdcbf47e9f95310d0992c9887dc6318b3197114f358e023100e1116bf68320bade7d07a1a9651512d60b551af8625b98b5eb8ca222d4073ae5c140a80e5dbe59f073647daa00837aee", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "32313939313533323239", "sig": "3064023031dced9a6767f39045472749baec1644ae7d93a810a4b60eb213c02c42de65152ffc669af96089554570801a704e2a2d02303022ecfbc88a72b9c50ef65344765b615738f2b3d420ade68cbf3ec40bef0e10c5cc43bcfe003bb6f17ec23802c40569", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "35363030333136383232", "sig": "3066023100f4bdf786c61c5f1ce7568638ba9dbc9a134e27fc142003bf9870353980a8f4c2fbd03c8d0171e4048ef30db6fe15388a023100d0e96768bc6adc91f93ae5704e86888853f479f32a45bfd436dc8a030603d233c56880124b7971362aa11b71315ae304", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "383639363531363935", "sig": "3065023100ec0f635b7ce31988a07f41b3df35ca03c70e376bfb3b6ab24831a83be2121b9f9e93928b10a8f5fc0322bdb9edd406fe023066618ccb473c6dac3b14cfab6dfb24d219b37aec63425067c2c1c631d64a80b9cab6445f5a5439adb28bb99daa9234a5", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "36353833393236333732", "sig": "306402304f2bea24f7de57901e365d4c332ddb62d294d0c5fd58342a43bdd3ba5cbaf25adaddb5944bfef9dcc88f94d93650bbbb02300851b97ddc433e4521c600904970e2bf55aa901e1aaaaf06818377f84a28e033a49eebc21ffe9cff3cbefd0963fbed00", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "3133323035303135373235", "sig": "3064023072a9bab30f8da1437f17115cc37b6ef8cf6591ed934d596675ad7b000c6a74cca5f37210a68228a58023790e3726c357023012d697c4e20b18f63a3e0164dca8ca4a5fa0058ad7cd1c571cef356e85fd8f56ab7963d8aba824e8d31efb3e690c27b9", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "35303835333330373931", "sig": "3064023033b7105f4cc98a1ea2abad45dbbe3761b4613ddd350e62da91560da694be3e84b1684f9a8ee4b3f556c61d02af54446202302c86e3a216dc7dd784cdcbf5084bdf6cdc1c7e67dbd61f9f6ed161fda4d4c26167e5b12731cf2b0cf5d9a5f0b6124939", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "37383636383133313139", "sig": "30640230252e3b5b60b8f80748b83623e30013723115cabcc48770c0ab6e7ee29c429ef1d9da78db3a9a8504133b9bd6feceb82502301ba740f87907cf6d450080f7807a50f21c31cd245dd30f95849a168d63b37628e8043c292ab7f130a4468eaf8b47e56d", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "32303832353339343239", "sig": "3066023100b8694dbf8310ccd78398a1cffa51493f95e3317f238291771cb331f8e3a9753774ae3be78df16d22b3fbe9ad45bed793023100daaead431bbdbf8d82368fbbd2473695683206ee67092c146b266ed32f56b31cb0f033eebf6c75118730eef7b7f96ba7", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "3130303635393536363937", "sig": "3066023100d37ba39cd1b5289e7aa3f33afefa4df6821a07d3e8ee1c11e7df036c37e36214bb90264633d4c395644cd2cc2523833f0231008b0d58ed75af59e2abbcec9226836f176b27da2d9f3094f2d4a09898136436235025208cf5444265af66fed05b3dc27c", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "33303234313831363034", "sig": "3066023100b4ef419020c0dcbdeeeed76c255560f1ed783c0f9e7fcea4c08a0714b9d1f491fda9ae7bb1eb96d294b02799f82861290231008d987611063d2f28cb309a56eaf1ea65f27d95c97b77a5f037f2f914fed728267aaf62a37f3c7b44fc4b15125b349863", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37373637383532383734", "sig": "3066023100b2df7b11cf60ac93c078d19f37f889717aa5d9af1d00d0964f9e9f5257c3b51b3d3e47ca5b5aa72058ed63b52464e582023100b524968ea8c58d379e38f4cfa9da1527a2acb26d605d22f173fcf1e834db0d7f031cb9245cb62b8458ff499b8d3decbe", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "353434313939393734", "sig": "3066023100e0edc08b4122b75ebbd1635d07f0bb55771bda15573a5081da971955f9a63f6decdd4919911dbfea503ea8ed1faad93d023100ca7850c74ce878587056206c590a1097d197a2090cfe3e057becfa2700c7a531623ae7331e163def693e26a97feb540d", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "35383433343830333931", "sig": "3065023068f555eef5a323a929719bfd8cf81d6d8a977ecb35defd86fa54d8e5749c7b5f3e80087fbd39f8aa0cd29d8310bd6578023100e2c2314a50fc0ad78c1ec02ea77ee2e13dcef1460957c6b573f721d72c209ac5fb529ab20397234c59ed44f60400971a", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "373138383932363239", "sig": "30660231009e330e29f18123813e83b9c6abd68de96a57f97a4005b88d5b470a67a541b6d3af12124cf8658b751671c6698fb8b021023100d210fba9bde6ef077ca06b75e1cf7ce8dd70b08e9dd42d81a215ef9272f1779ae3e9f0dec510571d87237cc6bf3203e8", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "31373433323233343433", "sig": "30650230483192056f753f64ddf0f21072b73d68893e6fa5432c981c7a1955b6592a6045a5c1c58c383e70023c34e09b7964ec8d02310094b005d5f98c4fd2ad40ff8e03a8599f45e206082112f834df1d48502d2ac690cd3204f0078913794c9c39077ad6c58b", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "32343036303035393336", "sig": "306402302b7ec14fd77c4b33230dd0a4e2710fbd307e469baec54b6f25daac7e196b7b4b5df251cdddba7bdc9836ca1319bb900b0230590036192586ff66ae9a288199db9d02bbd5b703f8c329a9a1f986001b190f20ae96fe8b63681eda17bac2a57fd40f2e", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "31363134303336393838", "sig": "306402302611484e7ff47dfaece4aa883dd73f891869e2786f20c87b980055ddd792070c0d0d9a370878126bab89a402b9ea173c02304e0006b8aabe9d6a3c3018d9c87eae7f46461187d3c20b33e975c850599ec1cb52c76e1f507e439afc43f9f682e7a8d2", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "32303935343235363835", "sig": "306502302d504e38cdb1bb80bf29e07afbc66aea732accc85a722011069988f21eef685084f55efa30bfe32427eb8636db9171b4023100883e3d80d766ccb29e73a9e929111930da8353ec69769785633fe1b4505f9051e78d50c79a6b7c885c10b160bbb57fb6", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "31303038303938393833", "sig": "3064023028dc1b63dc61ecde754ff4913780e486339103178e27d761987dac0b03c9bdf4a4a96b8680fa07fc47ae175b780e896e02305a9898eedf8781b9afeb506e0272a12c0c79bb893b8a5893c5a0a1bf4324d46dde71a245be2fd8aa2975fdeb40adf8f3", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "31353734313437393237", "sig": "306402304c978a47b9e9449337178aa6413a794c4c9bf182a42062646a469b1d2c2c95621e818e661352b07e63254b6954e1459802306997345f05cfc05c0fd4d1dd133e555e5e5002e0929a59f60bbffc354234783ebf4fe5db10a870952cabd453635c1082", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32383636373731353232", "sig": "3065023036d8e2cfc80d0436e1fad3702ec05aa138618cdb745652cb85b0b121ee107bdf1ade0464dc0c6bd16875bcc364044d8c023100898b8775c9b39aa9fd130b5ab77e6c462ced6114898045b7f606142277d9eb2aa897f24c9ba4c8d112111de04dc57c10", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "31363934323830373837", "sig": "3065023100ce2bdcf924caaa81e79bd7dd983dfeeee91652e4ea6edd077f8b56ada4953733a22dd3a6336446a648aec4ffc367cb3e023008eb09faeef4b0e5c1262eda2127464f7e2981ea1736e80afc7c622461c3d26fe08694fb4914ce9dbba83704e3077b3c", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "39393231363932353638", "sig": "3066023100e3a1b4b0567d6c664dec02f3ee9cd8581129046944b0e6650f6e6a41b5d9d4bf79d7a6fd54ea5a218492cfa1bb03ca07023100986206925cbfa186c7d88f7100d87dd3b2d03b8789309a722d582f119eef48cd0ea5460917cf27246c31f90e28540424", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "3131363039343339373938", "sig": "306502310095a5e29940e42099c4637f4ae51e7d1ec02be0dcfb0b627030984c35e477e80cc57e7eef970e384dee16a9b9fc8f2bf202300ca166c390339653cde84e79a87e5ceb4f52c1a515a5878542fd82705b9983976fd31a4123b5d0bde95a0818114cf462", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "37313836313632313030", "sig": "3066023100c30c49d0ba131944e2075daacb1259d5580a712a08f73d889c4d3d484d73dd9719a439a986f48b072c4595c507a01083023100a5595c0691bc2d215f981fab513e3a88a452f2a1433367b99b02b6efe507519afedbe1ad0337899944e29c9ccccb2476", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "33323934333437313737", "sig": "30650231009fd0585f8740669885c162842bba25323ea12b1d05e524bb945cad4e31538742eda5128f467b3c562c5f0a99019d3406023043acfadd03915c2350e1d8e514c47eb36f3c3456169c9a562a6262c1c2d7d33378bf9fec7f220239d5c61e06414414a4", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3138353134343535313230", "sig": "306402304ecac0cdbf665c584f8a40614cd55d042706c54895b1de02984fe309122566c959a4dd3315e7d3f089879f8f45821336023009187da6587a3de90eba41f4e6510e711f4467f3122971566ecc39a4bd53e95b8a19380e20ec2a7c752d29de54fd2e8f", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "343736303433393330", "sig": "3065023037a1ba49f11e97ad0ec47e687c6c6e94f794f874720c0dd2da501437b50e5b00fb6ed33adf7cf1f9c870fd3d37165bf7023100b3ad08c9886b4ca1593a68938b67142c65ed4da1714c22204cba71300c094ccdbdf84c38a3f6d896db72ed5051a19266", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "32353637333738373431", "sig": "3066023100a0abe896d2f30207bc9b21e75400eedb88d3498d49806f41aa8e7f9bd815a33382f278db39710c2cb097937790d0236c0231009a29aded30e8ce4790756208d12044e18c34168608026000a883044dd0d91109d866b422a054c232810ddfbb2ae440bb", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "35373339393334393935", "sig": "3065023100b024fc3479d0ddde1c9e06b63c9bfb76a00d0f2f555220cb9a1311c2deec32eb3d6d2b648f5e8c104d5f88931754c0c20230767950cc149697edbae836f977bd38d89d141ff9774147b13ddd525b7a3f3a14a80d9979856f65b99a6faff173b5d6eb", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "33343738333636313339", "sig": "306402302a0ae7b5d42645051212cafb7339b9c5283d1fd9881d77ad5c18d25ee10907b7809740a510e65aecd61b53ba3a0f660a02304c0457dd19ef6e4d6ae65f45417ddf1a58c07663a86737d271becfa3ea5724b6018f1fa9e64fd08601a7dbd3957761d9", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "363439303532363032", "sig": "306502300c1657320faca6668c6e9f06f657a310b01939a7d9640fa0429872fe28bd1667688bc162221285ecfb14e8d80627450a023100f5272aa08c321aa4f7e520825cc720f6511d635598c648d4d514669b3ad803ad259c799e195a095982f66c176435be21", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "34373633383837343936", "sig": "3066023100d821798a7a72bfb483e6e9840e8d921200ef1976b7e514036bf9133a01740ce397c73fa046054438c5806c294a02c6800231008c5d12887fcd945ba123fc5a5605d13a5a3e7e781ad69c6103577ee9dc47adc3e39a21080dd50304b59e5f5cf3f5a385", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "353739303230303830", "sig": "3065023100c996bd6fa63c9586779f27523d5583135a594808514f98cc44cac1fa5cfa03c78c7f12f746c6bd20608ecbe3060eb068023027d40a11d52373df3054a28b0ab98a91ad689d1211d69919fc04cadc22ff0367d3ef9433012a760c1d1df3715c8d5cf3", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "35333434373837383438", "sig": "3065023042dd6c8d995938701a538909ed6aeae0ba50c995138de84e195bbb9c56180e108d4a6274548c7be6e121c4d218d2d4a0023100fae8668bb2003f0da1dc90bec67d354ccbb899432599c3198b96c5ca4bd2324c46998f4fb76a123467cf24570b1b6916", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "3139323636343130393230", "sig": "30650230061f185633291b9a768e15ec03a2b7c356c757b023b61e313fdf0c5349d128a78668d20b2561709b3bd8451b920f12ab0231008fc5edc66410dbf20a7cbc3498e405761756ed39866856e74256ac1f255f62b0edff519762ecdbbc8395d14715c4388e", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "33373033393135373035", "sig": "3065023069326e047c62e8bac5c090b76bf73ae652fa9a6aecfa1ccb8702f419094c9727511264fb1aeec00e425c7a0d746793d30231009dbddd22db4a77dbe16114bc6fbb981aecba7e82a9cbc1ed385e28a51793561770fb3f9696090efca24f268d8788f2c9", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "3831353435373730", "sig": "306402304ca1df89b23ed5efcdf601d295c45e402d786a14d62f7261104e4cb05b8cae17abb095799e71173841749615c829411b02301bb777e0a6fee8a2337a436a6fa26a487de4640ff97d57b44b55305989803863d748c7302f2dfde8b8cedd69bb602e2d", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "313935353330333737", "sig": "3065023067be1b06f67172c503a5ac50582235d30bc9079eaa4cdec69a39c096310f8d99186cc9af7c8b4369a291d3e921d60705023100ab645fc91f06b1ff7cc58fccf6f7cfac74db30d839748a78cb5f3b8fefc7a06f3b5ff0310a8580c6050bebb75eda972c", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "31323637383130393033", "sig": "3066023100d966442d6c29e5a4cc60e2374eccd373db3ebe405ee7c9664c4273100cd1899a1c58110487528616d8c5321dbf5227640231009bb0e4a2c041a3b7b672029fe480d155f57671ecd6eb598660d025acce1f613d03cd6cff4a214131c8c7a8ad22df1397", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "3131313830373230383135", "sig": "3064023008a84a2bc39b082ab82e6e45f088a36f1cb255f97ec8124eca929d4506d7dab63957c647994be2c2c7344f902de5b38f02300c9645e84a304ba0970ca5ce00b8c8a971fa0d0bcbec6a70134894c44d3075030ff04333ea3889f847a1ed769ee618ee", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "38333831383639323930", "sig": "306502310083004b034202bbf51a327d32ed3ddf67b46eda9bac695a4422744a4bd99aaac3b3e8ed80ddac6538939c9385d6c8f61602307b4e61926cb9afa8cdaaf44909df6dc6449887d59fe2acac05f7684a235fa77179bdbcc69fd8f359e8eda19e5a5d4807", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33313331323837323737", "sig": "3065023100ad93375a1d374c41e5de268a8c08c205ff5652445bfe3ddf4ca77a70f5819f9f06db861d82fc9637946f0fe38457f2bd02304bc043acbc6a68d4824ed768af9476ad5b93e4cb3bbac284fb5fbd548ae3b96c265c6d1ef4588a3e2da21b124c0d6b12", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3134333331393236353338", "sig": "30660231009e0d45d2dc93fd363dc919405818e39922f3f9dd0827bcad86d4ba80a44b45a6f60b8e593b580c91262b32859dbb1e53023100eb9b8dfe5ba4a055a974f19b488f3a6fa07161006ac94eb1fe1c12dd0e20f3a7be38a37ce96d671183c5871249b2a3c5", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "333434393038323336", "sig": "306502307a5d04cd2fda59d8565c79ea2a7f1289ab79cae9fde060094c805c591a2534e4393e28c3fd858529bf17643846aceb830231008de0d8c0092fd02d554afe25f814744beaaa17c6946a6387ec7046b602db8a6c900246c2fb63fcef2ac8d9394444a0fc", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "36383239383335393239", "sig": "3065023100a564eea0cdac051a769f8ff1e0c834a288ce514f67d138113727b53a1a6fc95ce237367b91f1b91b2f65d589adc8288e0230182e5b47b6fbd8e741a04e809487ba5fcb8a5f2f1b9af6ce214128623a4768e38e6ddc958ff39078c36c04a314708427", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "33343435313538303233", "sig": "306402306758867cd1ca1446cc41043d1625c967a0ae04d9db17bbb42fa9c076b3593125d63cd3e7471ee6cdba5235a21cec2f220230563db387adb537e1d89231d935ac790316925aeb29132b9f87bee91116c33bf50943fe39b671ce9535dca0a5d22bbfa4", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "3132363937393837363434", "sig": "3066023100cde033e38d3f791db87d8a6907516bd8021acd47e897df683fda529d48050f8b5688f6361daf1b14bc3f45fc7f76150f023100e14f4811a667c85335a4709a589ea46bac72055b794eaea92d28e834d5bc459c605fe4f27c1ab18d186d59e7d205cb67", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "333939323432353533", "sig": "3065023100f2384468b55553c68f9764d8248cfd7358d604fa377ebb13828c43a8ebdf308fbbbebfa49a9458bfda957d2068d24e3f02301fdf4891d56e3e90c02b05c14c27c17f56f8e6aa144f02328c90109e1f70c9e3f582f0d299c44da505c543cc89c6a990", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "31363031393737393737", "sig": "3065023100b1ccafedcc21ba90b342fa23c0149f3d12a939ab6c3342b36ae61fddbdc753927a7c3e978bd780cf25cd78c8c5efe28002304c32a73f3157bbe2384095eb67726b9cd3c2623b98a182a3b4f00e8db933e1113b7ada2695a7d79b471026462b20e289", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "3130383738373535313435", "sig": "3066023100f3ed170e449758299ae55eb85244745e1876621c1f708e07e55c0d2d9ab5f9af9e0a8b3c7bdf8936ab3c9ebd1908e9dc023100da62ccdb658868147286d7269bcbd4addb4dec9ea3d5d79fdbe0ccffa40d055170bddeb4ef4c5e0bc99fae5db62b4477", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "37303034323532393939", "sig": "306502310083455fc4629e7693c8e495fec2d29bb23bb6db79180fcfa83a4f9310d9db27e29297dee27ee80a71ab2f7a2d59f48b8802307736c056c8f2bb57e9fb6b8de0ab6d09879f6611e737634e7b6337aa5c5a01f515d5e3702dec9a702177c816e32bac67", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31353635333235323833", "sig": "3065023074961587cbe49bbf0a73fea82b8b2242f67b0ea09224774639f437c60378a36b2d511a9145d576b440dffd1f02286a8b0231008fb95d46c22889085cc1d3e20bcfbcbc52f4532445f76f08efae2de8b56fe8525204643330dfd23cce946687a0aef046", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "3233383236333432333530", "sig": "3065023100a3fd322330d0f0efccc54bd7d73c3159eb1bcca08cec369a4a08fd00f9ec6d482ced58eb08a0d7c2113bd5575de4917d0230164e3232a628c40fbba1de82bfb9627cec78a8040cf325a5a8bb8f864c2ac19e3524ac93f4db5713ce62ba256176e05e", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "31343437383437303635", "sig": "306502304c862ff9e4ff88f9a58e9fceaaf9bbb30740d3f6c8c6a69b5627fe234b144f8cdf09520735cfd708f5e341a78cc4873d023100a861972514a0e975cf2da214125ec93288524cc77492ed63c516424278e5ec8d41724467cb7c3111fa34c69193abb435", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "3134323630323035353434", "sig": "3065023062225e4e492a9773397870336168960a66b9e50391ef7289cb2d3878f32252dc1b904f6682545e14564e415bd93e01170231009f4d0327f79e043505c691e361fa2e00f87f41324777eca6966f4bea2fa0858876aa01980b2cad7f66037524de49bf65", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "31393933383335323835", "sig": "30640230450c65d2d88ba464eee3a5ce9310b519d5dcf608799fb2275eee987a67c2c4d7ac53716987cc5139c18c67ef07b1e20702301ee0439311a7bce1c4fed0a3152d1b354d96536c6ca0c9188ac1f1afcc5cd7305b5611ef0d19d8bd57c5059976dc5e68", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "34323932313533353233", "sig": "3066023100aa2575fb5bea0effb5247d20c3d0165d575831840b5c18b0245a99a61b7ad5d7bf8a8cfcc375e095a84e781025bee3ee0231009c8b7797ad330abc206060b28b6ca1c639d89f59582528bda1527e3ab081697a2ab576f9d09c2ee329dd73231667308d", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "34343539393031343936", "sig": "3064023001fc45285aa2c2e50458199ade2ded0dd36b1de03e8969175be4a6f09f9719b195ded8d9eb4ea132d95d19a3528fd6c9023059609a358c5919fef4781061804d4d64a067edecdcfd14620161aae3ef2735095a558e4f8ae345040123f093e5f70af2", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31333933393731313731", "sig": "3065023100d8e1f6b19e5b92e36060e59e53eeb788a4758c2c8ee9519f3949d5f3315abafbe937b8ed44d47e886a07c107aa8ac9f4023012550574318371e5168d0a339f20fcacaec87db211bba4d4e7c7e055b63b75fd31790ad285f4cc061378692b0a248e34", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "32333930363936343935", "sig": "306402304815aec44a7a6b86ae87fc2556accd77832fa33a4710e02ec5ef6f41f68a910e6af4d173ae462a759bd98079b371bf5d02306e78d562f9e8be65e8d7a74a7305e5d6cf2f3c4c980f2b18dfb8e9c8b0134ec86548053b3d125e56d5872294d2d14ebc", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "3131343436303536323634", "sig": "3065023100d302f9db6b2d94e194412f0d40a135a554aee014bd939b3d7e45c1221ef7ce45c2aed875f9a2bc43dbc8264d92e444a5023004e7247b258c6e7739979c0a07282f62958ac45e52dd76a41d5e1aca31a5cda73d7b026d67b4d609803001cb661d74c6", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "363835303034373530", "sig": "3065023100889f0e2a6ae2ddcad1cde3f65b61d4dd40985917ba841b47a1f802491f5af5067722b7683df0fca7ee19d2b73724c8fd02301f989bac23b51c49e5d7dcc319eed2fc767e9b432bf75af92814d9e67a5d4b3398eb15e98b70527abbc029abc1bea524", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "3232323035333630363139", "sig": "3066023100e69c70c679795ca7d2b66e2632529651c120055fa3cf25435fe8bb28987c02412ce73e6ca5ca7e0b42e9670c0a588175023100edd8513bff40cdca9e22659238fbcea2de2caeef53c5287a515db9168b3008ec446c9b94f28a6e021c69bc6637fc4634", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "36323135363635313234", "sig": "30640230068cbecfd47bfd688f495df05e45fd5fced6d8e240605c5b2be5e69368740b694b9b1ea034af3180e571dd38a86369ef02301a1d2976f748d1621128013c61abda5398a3e24f0073d1a6e07a1e96c12be4f1e2e7b144f9b5a350500acfc5cb0698d9", "result": "valid", "flags": []}, {"tcId": 354, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "306402300e2c56eb5f6612f0c2b22ab03d57d9a443075a2b7a0b460883e4f4876121e9b6f1ed67de20b79f028f7f66ed0281db7102303916b72b12d035a307b7c45a9878333a8c61445aad2330dc49a12b92e2e5dab72e53e5789f40afb90aea0ea4431f2dd1", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ca5ee479ad6624ab5870539a56a23b3816eef7bbc67156836dfb58c425fdb7213e31770f12b43152e887d88a3afb4b182aceec92b3139aca8396402a8f81bb5014e748eab2e2059f8656a883e62d78b9dc988b98332627f95232d37df26585d3", "wx": "00ca5ee479ad6624ab5870539a56a23b3816eef7bbc67156836dfb58c425fdb7213e31770f12b43152e887d88a3afb4b18", "wy": "2aceec92b3139aca8396402a8f81bb5014e748eab2e2059f8656a883e62d78b9dc988b98332627f95232d37df26585d3"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ca5ee479ad6624ab5870539a56a23b3816eef7bbc67156836dfb58c425fdb7213e31770f12b43152e887d88a3afb4b182aceec92b3139aca8396402a8f81bb5014e748eab2e2059f8656a883e62d78b9dc988b98332627f95232d37df26585d3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEyl7kea1mJKtYcFOaVqI7OBbu97vGcVaD\nbftYxCX9tyE+MXcPErQxUuiH2Io6+0sYKs7skrMTmsqDlkAqj4G7UBTnSOqy4gWf\nhlaog+YteLncmIuYMyYn+VIy033yZYXT\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "304d0218389cb27e0bc8d21fa7e5f24cb74f58851313e696333ad68b023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "valid", "flags": []}, {"tcId": 356, "comment": "r too large", "msg": "313233343030", "sig": "3066023100fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffe023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0470e6a90b4e076bf51dfa01fa44de49b448f7afa0f3d07677f1682ca776d404b2a0feef66b005ea28ba99b6ce21d0ca12424f7d179951fb89156cdf04aed6db056c98592c651b5a881abc34e2401127fb81c64e90cee83269c5141f9a3c7bce78", "wx": "70e6a90b4e076bf51dfa01fa44de49b448f7afa0f3d07677f1682ca776d404b2a0feef66b005ea28ba99b6ce21d0ca12", "wy": "424f7d179951fb89156cdf04aed6db056c98592c651b5a881abc34e2401127fb81c64e90cee83269c5141f9a3c7bce78"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000470e6a90b4e076bf51dfa01fa44de49b448f7afa0f3d07677f1682ca776d404b2a0feef66b005ea28ba99b6ce21d0ca12424f7d179951fb89156cdf04aed6db056c98592c651b5a881abc34e2401127fb81c64e90cee83269c5141f9a3c7bce78", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEcOapC04Ha/Ud+gH6RN5JtEj3r6Dz0HZ3\n8Wgsp3bUBLKg/u9msAXqKLqZts4h0MoSQk99F5lR+4kVbN8ErtbbBWyYWSxlG1qI\nGrw04kARJ/uBxk6QzugyacUUH5o8e854\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "r,s are large", "msg": "313233343030", "sig": "3066023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52971", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045a568474805fbf9acc1e5756d296696290b73d4d1c3b197f48aff03b919f0111823f90ea024af1c78e7c803e2297662d4c1c79edc9c694620c1f5b5cc7dd9ff89a42442747857cace26b6ebc99962ec3a68a8e4072226d6d98a2a866dd97c203", "wx": "5a568474805fbf9acc1e5756d296696290b73d4d1c3b197f48aff03b919f0111823f90ea024af1c78e7c803e2297662d", "wy": "4c1c79edc9c694620c1f5b5cc7dd9ff89a42442747857cace26b6ebc99962ec3a68a8e4072226d6d98a2a866dd97c203"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045a568474805fbf9acc1e5756d296696290b73d4d1c3b197f48aff03b919f0111823f90ea024af1c78e7c803e2297662d4c1c79edc9c694620c1f5b5cc7dd9ff89a42442747857cace26b6ebc99962ec3a68a8e4072226d6d98a2a866dd97c203", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWlaEdIBfv5rMHldW0pZpYpC3PU0cOxl/\nSK/wO5GfARGCP5DqAkrxx458gD4il2YtTBx57cnGlGIMH1tcx92f+JpCRCdHhXys\n4mtuvJmWLsOmio5AciJtbZiiqGbdl8ID\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d1aee55fdc2a716ba2fabcb57020b72e539bf05c7902f98e105bf83d4cc10c2a159a3cf7e01d749d2205f4da6bd8fcf1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0488531382963bfe4e179f0b457ecd446528b98d349edbd8e7d0f6c1673b4ae2a7629b3345a7eae2e7c48358c13bdbe0389375c849dd571d91f2a3bf8994f53f82261f38172806c4d725de2029e887bfe036f38d6985ea5a22c52169db6e4213da", "wx": "0088531382963bfe4e179f0b457ecd446528b98d349edbd8e7d0f6c1673b4ae2a7629b3345a7eae2e7c48358c13bdbe038", "wy": "009375c849dd571d91f2a3bf8994f53f82261f38172806c4d725de2029e887bfe036f38d6985ea5a22c52169db6e4213da"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000488531382963bfe4e179f0b457ecd446528b98d349edbd8e7d0f6c1673b4ae2a7629b3345a7eae2e7c48358c13bdbe0389375c849dd571d91f2a3bf8994f53f82261f38172806c4d725de2029e887bfe036f38d6985ea5a22c52169db6e4213da", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEiFMTgpY7/k4XnwtFfs1EZSi5jTSe29jn\n0PbBZztK4qdimzNFp+ri58SDWME72+A4k3XISd1XHZHyo7+JlPU/giYfOBcoBsTX\nJd4gKeiHv+A2841phepaIsUhadtuQhPa\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b6b681dc484f4f020fd3f7e626d88edc6ded1b382ef3e143d60887b51394260832d4d8f2ef70458f9fa90e38c2e19e4f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04080da57d67dba48eb50eef484cf668d981e1bf30c357c3fd21a43cdc41f267c3f186bf87e3680239bac09930f144263c5f28777ad8bcbfc3eb0369e0f7b18392a12397a4fbe15a2a1f6e2e5b4067c82681c89c73db25eca18c6b25768429cef0", "wx": "080da57d67dba48eb50eef484cf668d981e1bf30c357c3fd21a43cdc41f267c3f186bf87e3680239bac09930f144263c", "wy": "5f28777ad8bcbfc3eb0369e0f7b18392a12397a4fbe15a2a1f6e2e5b4067c82681c89c73db25eca18c6b25768429cef0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004080da57d67dba48eb50eef484cf668d981e1bf30c357c3fd21a43cdc41f267c3f186bf87e3680239bac09930f144263c5f28777ad8bcbfc3eb0369e0f7b18392a12397a4fbe15a2a1f6e2e5b4067c82681c89c73db25eca18c6b25768429cef0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAECA2lfWfbpI61Du9ITPZo2YHhvzDDV8P9\nIaQ83EHyZ8Pxhr+H42gCObrAmTDxRCY8Xyh3eti8v8PrA2ng97GDkqEjl6T74Voq\nH24uW0BnyCaByJxz2yXsoYxrJXaEKc7w\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040e74a096d7f6ee1be9b4160d6b79baba4d25b4fb6fbdd38f5a9ed5cc1ac79943be71ede093e504c7dc0832daeb898a05a8d005b30c894686f6ecb2bc696e25effaccd3c9e4b48122db567c0118a0b983b757c2f40082dc374f8f6117a8e76fc0", "wx": "0e74a096d7f6ee1be9b4160d6b79baba4d25b4fb6fbdd38f5a9ed5cc1ac79943be71ede093e504c7dc0832daeb898a05", "wy": "00a8d005b30c894686f6ecb2bc696e25effaccd3c9e4b48122db567c0118a0b983b757c2f40082dc374f8f6117a8e76fc0"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040e74a096d7f6ee1be9b4160d6b79baba4d25b4fb6fbdd38f5a9ed5cc1ac79943be71ede093e504c7dc0832daeb898a05a8d005b30c894686f6ecb2bc696e25effaccd3c9e4b48122db567c0118a0b983b757c2f40082dc374f8f6117a8e76fc0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEDnSgltf27hvptBYNa3m6uk0ltPtvvdOP\nWp7VzBrHmUO+ce3gk+UEx9wIMtrriYoFqNAFswyJRob27LK8aW4l7/rM08nktIEi\n21Z8ARiguYO3V8L0AILcN0+PYReo52/A\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a2ad0e27b40410d16077ddc5e415f109d328bf75e73a0f56876fef731285f83188b207a68690a40e76ed23e2c5e49fcf604f1c5d7d7df365005d40e209f4da7bb06f310d5a1660ad6236577fbb47955261f507d23b83013ffb951bd76908e76c", "wx": "00a2ad0e27b40410d16077ddc5e415f109d328bf75e73a0f56876fef731285f83188b207a68690a40e76ed23e2c5e49fcf", "wy": "604f1c5d7d7df365005d40e209f4da7bb06f310d5a1660ad6236577fbb47955261f507d23b83013ffb951bd76908e76c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a2ad0e27b40410d16077ddc5e415f109d328bf75e73a0f56876fef731285f83188b207a68690a40e76ed23e2c5e49fcf604f1c5d7d7df365005d40e209f4da7bb06f310d5a1660ad6236577fbb47955261f507d23b83013ffb951bd76908e76c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEoq0OJ7QEENFgd93F5BXxCdMov3XnOg9W\nh2/vcxKF+DGIsgemhpCkDnbtI+LF5J/PYE8cXX1982UAXUDiCfTae7BvMQ1aFmCt\nYjZXf7tHlVJh9QfSO4MBP/uVG9dpCOds\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "small r and s", "msg": "313233343030", "sig": "3006020102020103", "result": "valid", "flags": []}, {"tcId": 363, "comment": "r is larger than n", "msg": "313233343030", "sig": "3036023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52975020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a233025c12d20f49dc50dc802e79f03c7ce1750b9204b51325d90b5ade08f4a74ef6efb081ed3156d64a0110d60fffabb924881891ee984cf51949dee96cfd7c9759b1ff00f0dbdc718d52117079d5d8bd6c86c6f532276af38b779bf2350d7f", "wx": "00a233025c12d20f49dc50dc802e79f03c7ce1750b9204b51325d90b5ade08f4a74ef6efb081ed3156d64a0110d60fffab", "wy": "00b924881891ee984cf51949dee96cfd7c9759b1ff00f0dbdc718d52117079d5d8bd6c86c6f532276af38b779bf2350d7f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a233025c12d20f49dc50dc802e79f03c7ce1750b9204b51325d90b5ade08f4a74ef6efb081ed3156d64a0110d60fffabb924881891ee984cf51949dee96cfd7c9759b1ff00f0dbdc718d52117079d5d8bd6c86c6f532276af38b779bf2350d7f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEojMCXBLSD0ncUNyALnnwPHzhdQuSBLUT\nJdkLWt4I9KdO9u+wge0xVtZKARDWD/+ruSSIGJHumEz1GUne6Wz9fJdZsf8A8Nvc\ncY1SEXB51di9bIbG9TInavOLd5vyNQ1/\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "s is larger than n", "msg": "313233343030", "sig": "3036020102023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accd7fffa", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043c9bb63607cdea0585f38d9780c9ac3e9a5a58153e2aacc4bc7a1d638d12e32c4d3a90c0c114b232c6f16e23e4bebb24da2ac2ccedc5494fe534a9abaea3013de0176f1b0e91bcd62154bdf3f604091a5008b2466702d0e2f93e4a4b6c601a54", "wx": "3c9bb63607cdea0585f38d9780c9ac3e9a5a58153e2aacc4bc7a1d638d12e32c4d3a90c0c114b232c6f16e23e4bebb24", "wy": "00da2ac2ccedc5494fe534a9abaea3013de0176f1b0e91bcd62154bdf3f604091a5008b2466702d0e2f93e4a4b6c601a54"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043c9bb63607cdea0585f38d9780c9ac3e9a5a58153e2aacc4bc7a1d638d12e32c4d3a90c0c114b232c6f16e23e4bebb24da2ac2ccedc5494fe534a9abaea3013de0176f1b0e91bcd62154bdf3f604091a5008b2466702d0e2f93e4a4b6c601a54", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEPJu2NgfN6gWF842XgMmsPppaWBU+KqzE\nvHodY40S4yxNOpDAwRSyMsbxbiPkvrsk2irCzO3FSU/lNKmrrqMBPeAXbxsOkbzW\nIVS98/YECRpQCLJGZwLQ4vk+SktsYBpU\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s^-1", "msg": "313233343030", "sig": "3036020201000230489122448912244891224489122448912244891224489122347ce79bc437f4d071aaa92c7d6c882ae8734dc18cb0d553", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04559a66ef77752fd856976f36ed315619932204599bd7ef91d1a53ac1e7c90b3969cab8143b7a53c4bf5a3fe39f649eb61f00f86dd8b8556c4815b2a01c59eb6cc03c97b94b6db4318249fe489e36ac9635876b1ca2ec0999caef5e1a6a58a70d", "wx": "559a66ef77752fd856976f36ed315619932204599bd7ef91d1a53ac1e7c90b3969cab8143b7a53c4bf5a3fe39f649eb6", "wy": "1f00f86dd8b8556c4815b2a01c59eb6cc03c97b94b6db4318249fe489e36ac9635876b1ca2ec0999caef5e1a6a58a70d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004559a66ef77752fd856976f36ed315619932204599bd7ef91d1a53ac1e7c90b3969cab8143b7a53c4bf5a3fe39f649eb61f00f86dd8b8556c4815b2a01c59eb6cc03c97b94b6db4318249fe489e36ac9635876b1ca2ec0999caef5e1a6a58a70d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEVZpm73d1L9hWl2827TFWGZMiBFmb1++R\n0aU6wefJCzlpyrgUO3pTxL9aP+OfZJ62HwD4bdi4VWxIFbKgHFnrbMA8l7lLbbQx\ngkn+SJ42rJY1h2scouwJmcrvXhpqWKcN\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "303c02072d9b4d347952cd023100ce751512561b6f57c75342848a3ff98ccf9c3f0219b6b68d00449e6c971a85d2e2ce73554b59219d54d2083b46327351", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040548e79a17fd3a114d830ea88f218ee1ef7aa3f8dc139e0a8b9b60e25049a816ef449e8bd5dae867446495fdf20f47700363a1e8afefb02ebfd59df90b6d23ff7d5f706f9b26daebae1d4657ac342844ee9c2e0e9269f7efe7ab91e0303c115d", "wx": "0548e79a17fd3a114d830ea88f218ee1ef7aa3f8dc139e0a8b9b60e25049a816ef449e8bd5dae867446495fdf20f4770", "wy": "0363a1e8afefb02ebfd59df90b6d23ff7d5f706f9b26daebae1d4657ac342844ee9c2e0e9269f7efe7ab91e0303c115d"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040548e79a17fd3a114d830ea88f218ee1ef7aa3f8dc139e0a8b9b60e25049a816ef449e8bd5dae867446495fdf20f47700363a1e8afefb02ebfd59df90b6d23ff7d5f706f9b26daebae1d4657ac342844ee9c2e0e9269f7efe7ab91e0303c115d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEBUjnmhf9OhFNgw6ojyGO4e96o/jcE54K\ni5tg4lBJqBbvRJ6L1droZ0Rklf3yD0dwA2Oh6K/vsC6/1Z35C20j/31fcG+bJtrr\nrh1GV6w0KETunC4Okmn37+erkeAwPBFd\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3041020d1033e67e37b32b445580bf4efb02302ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad5215c51b320e460542f9cc38968ccdf4263684004eb79a452", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a0eb670630f9bbbd963c5750de7bcbae4ddfd37b13fe7690eec6861a3c56c8efb87dbbf85ccd953c659d382c3d7df76afb08840635a16ac7ecf3de2dc28a77c8af9d49e5a832551e3354a2b311e52be86720d9b2fbb78d11a8aec61606a29f0d", "wx": "00a0eb670630f9bbbd963c5750de7bcbae4ddfd37b13fe7690eec6861a3c56c8efb87dbbf85ccd953c659d382c3d7df76a", "wy": "00fb08840635a16ac7ecf3de2dc28a77c8af9d49e5a832551e3354a2b311e52be86720d9b2fbb78d11a8aec61606a29f0d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a0eb670630f9bbbd963c5750de7bcbae4ddfd37b13fe7690eec6861a3c56c8efb87dbbf85ccd953c659d382c3d7df76afb08840635a16ac7ecf3de2dc28a77c8af9d49e5a832551e3354a2b311e52be86720d9b2fbb78d11a8aec61606a29f0d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEoOtnBjD5u72WPFdQ3nvLrk3f03sT/naQ\n7saGGjxWyO+4fbv4XM2VPGWdOCw9ffdq+wiEBjWhasfs894twop3yK+dSeWoMlUe\nM1SisxHlK+hnINmy+7eNEaiuxhYGop8N\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "303602020100023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04254bce3041b00468445cb9ae597bc76c1279a8506142ce2427185b1d7f753d1c0aad94156b531a2071aa61c83ec842a3710d6c8c96766ae8b63396133e5872805e47d9ba39113e122d676d54dbb2460b59d986bdd33be346c021e8a71bb41ba9", "wx": "254bce3041b00468445cb9ae597bc76c1279a8506142ce2427185b1d7f753d1c0aad94156b531a2071aa61c83ec842a3", "wy": "710d6c8c96766ae8b63396133e5872805e47d9ba39113e122d676d54dbb2460b59d986bdd33be346c021e8a71bb41ba9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004254bce3041b00468445cb9ae597bc76c1279a8506142ce2427185b1d7f753d1c0aad94156b531a2071aa61c83ec842a3710d6c8c96766ae8b63396133e5872805e47d9ba39113e122d676d54dbb2460b59d986bdd33be346c021e8a71bb41ba9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEJUvOMEGwBGhEXLmuWXvHbBJ5qFBhQs4k\nJxhbHX91PRwKrZQVa1MaIHGqYcg+yEKjcQ1sjJZ2aui2M5YTPlhygF5H2bo5ET4S\nLWdtVNuyRgtZ2Ya90zvjRsAh6KcbtBup\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3041020d062522bbd3ecbe7c39e93e7c24023077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049129db4446c2c598c4f81070f70f66c37c39323e01418c095de9902e0e1b20f26bc3e011ba84c10626ffdce836690c9f8e4a104fec4aaa4350c238617ee50456accc49efc3b73eb9548e1600c2483f1c4bae9ddf3ff92af17afd19f86274589c", "wx": "009129db4446c2c598c4f81070f70f66c37c39323e01418c095de9902e0e1b20f26bc3e011ba84c10626ffdce836690c9f", "wy": "008e4a104fec4aaa4350c238617ee50456accc49efc3b73eb9548e1600c2483f1c4bae9ddf3ff92af17afd19f86274589c"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049129db4446c2c598c4f81070f70f66c37c39323e01418c095de9902e0e1b20f26bc3e011ba84c10626ffdce836690c9f8e4a104fec4aaa4350c238617ee50456accc49efc3b73eb9548e1600c2483f1c4bae9ddf3ff92af17afd19f86274589c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEkSnbREbCxZjE+BBw9w9mw3w5Mj4BQYwJ\nXemQLg4bIPJrw+ARuoTBBib/3Og2aQyfjkoQT+xKqkNQwjhhfuUEVqzMSe/Dtz65\nVI4WAMJIPxxLrp3fP/kq8Xr9GfhidFic\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3065023100ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc528f3023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a701a8111cdf97ced74a00a4514b2b526be8113e7df6cf7163aaee465880d26275b833b186d80f1862dc67ff768dde43e5a991f16f8f777311b17eabdc90b6ece3b5da776cfbebbc504382ca1abae1c6aa6a64d9c41110d97950514e99578ed8", "wx": "00a701a8111cdf97ced74a00a4514b2b526be8113e7df6cf7163aaee465880d26275b833b186d80f1862dc67ff768dde43", "wy": "00e5a991f16f8f777311b17eabdc90b6ece3b5da776cfbebbc504382ca1abae1c6aa6a64d9c41110d97950514e99578ed8"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a701a8111cdf97ced74a00a4514b2b526be8113e7df6cf7163aaee465880d26275b833b186d80f1862dc67ff768dde43e5a991f16f8f777311b17eabdc90b6ece3b5da776cfbebbc504382ca1abae1c6aa6a64d9c41110d97950514e99578ed8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEpwGoERzfl87XSgCkUUsrUmvoET599s9x\nY6ruRliA0mJ1uDOxhtgPGGLcZ/92jd5D5amR8W+Pd3MRsX6r3JC27OO12nds++u8\nUEOCyhq64caqamTZxBEQ2XlQUU6ZV47Y\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "s == 1", "msg": "313233343030", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020101", "result": "valid", "flags": []}, {"tcId": 372, "comment": "s == 0", "msg": "313233343030", "sig": "3035023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b6815ba05413bcf34f4c0704af590c1998d7fcd169541e1efe1567ca1dd71a22e35ac838b20c75281582044a57b58f456cdceb10612062779abadd8742c6e93ed74adf306f3b3a0f96b70dd1134b7558b64b55b200c5732c50f05aa032ae7c00", "wx": "00b6815ba05413bcf34f4c0704af590c1998d7fcd169541e1efe1567ca1dd71a22e35ac838b20c75281582044a57b58f45", "wy": "6cdceb10612062779abadd8742c6e93ed74adf306f3b3a0f96b70dd1134b7558b64b55b200c5732c50f05aa032ae7c00"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b6815ba05413bcf34f4c0704af590c1998d7fcd169541e1efe1567ca1dd71a22e35ac838b20c75281582044a57b58f456cdceb10612062779abadd8742c6e93ed74adf306f3b3a0f96b70dd1134b7558b64b55b200c5732c50f05aa032ae7c00", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEtoFboFQTvPNPTAcEr1kMGZjX/NFpVB4e\n/hVnyh3XGiLjWsg4sgx1KBWCBEpXtY9FbNzrEGEgYneaut2HQsbpPtdK3zBvOzoP\nlrcN0RNLdVi2S1WyAMVzLFDwWqAyrnwA\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041af19841ff3df8bdc4f8cce957e0dab763efe413929b279f1d46dde1c6f2bbc55af1bb1d8011fc587a4d599a4ae7cd8d5f663860c43c88e08399f00ef6641123787956a2b7012883b5ff7c46bd156d96d3c02a63ef86e060a2a0fa5b80d0c0e5", "wx": "1af19841ff3df8bdc4f8cce957e0dab763efe413929b279f1d46dde1c6f2bbc55af1bb1d8011fc587a4d599a4ae7cd8d", "wy": "5f663860c43c88e08399f00ef6641123787956a2b7012883b5ff7c46bd156d96d3c02a63ef86e060a2a0fa5b80d0c0e5"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041af19841ff3df8bdc4f8cce957e0dab763efe413929b279f1d46dde1c6f2bbc55af1bb1d8011fc587a4d599a4ae7cd8d5f663860c43c88e08399f00ef6641123787956a2b7012883b5ff7c46bd156d96d3c02a63ef86e060a2a0fa5b80d0c0e5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEGvGYQf89+L3E+MzpV+Dat2Pv5BOSmyef\nHUbd4cbyu8Va8bsdgBH8WHpNWZpK582NX2Y4YMQ8iOCDmfAO9mQRI3h5VqK3ASiD\ntf98Rr0VbZbTwCpj74bgYKKg+luA0MDl\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046836084fddfcfd527cb3847fb8b911c0fa002537fa460ca8f5d40f025603a4d89aa6ec640fde0cc4b31c46239a1d0bb76beed7019892e87287e23f0d35093ab14c4d41c0efe8463ede3494230a384eb1bc410de918c5484a25640741acb8cc0d", "wx": "6836084fddfcfd527cb3847fb8b911c0fa002537fa460ca8f5d40f025603a4d89aa6ec640fde0cc4b31c46239a1d0bb7", "wy": "6beed7019892e87287e23f0d35093ab14c4d41c0efe8463ede3494230a384eb1bc410de918c5484a25640741acb8cc0d"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046836084fddfcfd527cb3847fb8b911c0fa002537fa460ca8f5d40f025603a4d89aa6ec640fde0cc4b31c46239a1d0bb76beed7019892e87287e23f0d35093ab14c4d41c0efe8463ede3494230a384eb1bc410de918c5484a25640741acb8cc0d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEaDYIT938/VJ8s4R/uLkRwPoAJTf6Rgyo\n9dQPAlYDpNiapuxkD94MxLMcRiOaHQu3a+7XAZiS6HKH4j8NNQk6sUxNQcDv6EY+\n3jSUIwo4TrG8QQ3pGMVISiVkB0GsuMwN\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "306402307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b902307fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294ba", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b4b2d5a8b50ffabd34748e94498c1d4728d084f943fbddd4b3b6ee16eaa4da91613a82c98017132c94cd6fe4b87232f16d612228ed5d7d08bf0c8699677e3b8f3e718073b945a6c108d97a3b1433c79052b2655a18a3b2e621baa88198cb5f3c", "wx": "00b4b2d5a8b50ffabd34748e94498c1d4728d084f943fbddd4b3b6ee16eaa4da91613a82c98017132c94cd6fe4b87232f1", "wy": "6d612228ed5d7d08bf0c8699677e3b8f3e718073b945a6c108d97a3b1433c79052b2655a18a3b2e621baa88198cb5f3c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b4b2d5a8b50ffabd34748e94498c1d4728d084f943fbddd4b3b6ee16eaa4da91613a82c98017132c94cd6fe4b87232f16d612228ed5d7d08bf0c8699677e3b8f3e718073b945a6c108d97a3b1433c79052b2655a18a3b2e621baa88198cb5f3c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEtLLVqLUP+r00dI6USYwdRyjQhPlD+93U\ns7buFuqk2pFhOoLJgBcTLJTNb+S4cjLxbWEiKO1dfQi/DIaZZ347jz5xgHO5RabB\nCNl6OxQzx5BSsmVaGKOy5iG6qIGYy188\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "u1 == 1", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281757b30e19218a37cbd612086fbc158ca", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04842b3d89e54d9a4b5694d9251bba20ae4854c510dc0b6ef7033e4045ba4e64b6ddcd36299aac554dbac6db3e27c98123868258190297e1d6bae648a6dee2285886233afd1c3d6f196ad1db14262a579d74cf7855fffc65f5abd242b135ae87df", "wx": "00842b3d89e54d9a4b5694d9251bba20ae4854c510dc0b6ef7033e4045ba4e64b6ddcd36299aac554dbac6db3e27c98123", "wy": "00868258190297e1d6bae648a6dee2285886233afd1c3d6f196ad1db14262a579d74cf7855fffc65f5abd242b135ae87df"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004842b3d89e54d9a4b5694d9251bba20ae4854c510dc0b6ef7033e4045ba4e64b6ddcd36299aac554dbac6db3e27c98123868258190297e1d6bae648a6dee2285886233afd1c3d6f196ad1db14262a579d74cf7855fffc65f5abd242b135ae87df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEhCs9ieVNmktWlNklG7ogrkhUxRDcC273\nAz5ARbpOZLbdzTYpmqxVTbrG2z4nyYEjhoJYGQKX4da65kim3uIoWIYjOv0cPW8Z\natHbFCYqV510z3hV//xl9avSQrE1roff\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100bc07ff041506dc73a75086a43252fb43b6327af3c6b2cc7d322ff6d1d1162b5de29edcd0b69803fe2f8af8e3d103d0a9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049ab73dcfffc820e739a3ed9c316c6f15d27a032f8aa59325f7842cf4a34198ac6ff09eb1a311ce226bf1abb49d8085110135f4b0c2b6b195da9bbe1993e985b8607664f1a4b3d499ea1a112b6afc7e6b88357c9348b614ddfdc846a3f38bbdca", "wx": "009ab73dcfffc820e739a3ed9c316c6f15d27a032f8aa59325f7842cf4a34198ac6ff09eb1a311ce226bf1abb49d808511", "wy": "0135f4b0c2b6b195da9bbe1993e985b8607664f1a4b3d499ea1a112b6afc7e6b88357c9348b614ddfdc846a3f38bbdca"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049ab73dcfffc820e739a3ed9c316c6f15d27a032f8aa59325f7842cf4a34198ac6ff09eb1a311ce226bf1abb49d8085110135f4b0c2b6b195da9bbe1993e985b8607664f1a4b3d499ea1a112b6afc7e6b88357c9348b614ddfdc846a3f38bbdca", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEmrc9z//IIOc5o+2cMWxvFdJ6Ay+KpZMl\n94Qs9KNBmKxv8J6xoxHOImvxq7SdgIURATX0sMK2sZXam74Zk+mFuGB2ZPGks9SZ\n6hoRK2r8fmuINXyTSLYU3f3IRqPzi73K\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "u2 == 1", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0428771b137fb7d74c0ed0290416f47c8118997923c7b3b717fbbd5308a4bb0e494714bd3f1ff5e9e368887377284272ebf92e5df476a2fa0906ce4fad121c641abb539ab4ef270cd8f0497cc3e6e05b18561b730670f010741238a5d07b077045", "wx": "28771b137fb7d74c0ed0290416f47c8118997923c7b3b717fbbd5308a4bb0e494714bd3f1ff5e9e368887377284272eb", "wy": "00f92e5df476a2fa0906ce4fad121c641abb539ab4ef270cd8f0497cc3e6e05b18561b730670f010741238a5d07b077045"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000428771b137fb7d74c0ed0290416f47c8118997923c7b3b717fbbd5308a4bb0e494714bd3f1ff5e9e368887377284272ebf92e5df476a2fa0906ce4fad121c641abb539ab4ef270cd8f0497cc3e6e05b18561b730670f010741238a5d07b077045", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKHcbE3+310wO0CkEFvR8gRiZeSPHs7cX\n+71TCKS7DklHFL0/H/Xp42iIc3coQnLr+S5d9Hai+gkGzk+tEhxkGrtTmrTvJwzY\n8El8w+bgWxhWG3MGcPAQdBI4pdB7B3BF\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "3065023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023100aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa84ecde56a2cf73ea3abc092185cb1a51f34810f1ddd8c64d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "049d1baad217829d5f2d7db5bd085e9126232e8c49c58707cb153db1d1e20a109c90f7bcbae4f2c74d6595207cb0e5dd271eea30752a1425905d0811d0f42019e5088142b41945bee03948f206f2e7c3c1081ba9a297180e36b247ee9e70832035", "wx": "009d1baad217829d5f2d7db5bd085e9126232e8c49c58707cb153db1d1e20a109c90f7bcbae4f2c74d6595207cb0e5dd27", "wy": "1eea30752a1425905d0811d0f42019e5088142b41945bee03948f206f2e7c3c1081ba9a297180e36b247ee9e70832035"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200049d1baad217829d5f2d7db5bd085e9126232e8c49c58707cb153db1d1e20a109c90f7bcbae4f2c74d6595207cb0e5dd271eea30752a1425905d0811d0f42019e5088142b41945bee03948f206f2e7c3c1081ba9a297180e36b247ee9e70832035", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEnRuq0heCnV8tfbW9CF6RJiMujEnFhwfL\nFT2x0eIKEJyQ97y65PLHTWWVIHyw5d0nHuowdSoUJZBdCBHQ9CAZ5QiBQrQZRb7g\nOUjyBvLnw8EIG6milxgONrJH7p5wgyA1\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100c152aafea3a8612ec83a7dc9448f01941899d7041319bbd60bfdfb3c03da74c00c8fc4176128a6263268711edc6e8e90", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048e39e1e44f782b810ea93037c344371c4fb141c8bf196ea618f3a176547139a6d02121d2794cbe6481061694db579315c3184e8cd9b6c16b37699633d87f5600654b44cbcb5ab50ba872dfa001769eb765b2d1902e01d2e8af4e1fd6e9c0f30f", "wx": "008e39e1e44f782b810ea93037c344371c4fb141c8bf196ea618f3a176547139a6d02121d2794cbe6481061694db579315", "wy": "00c3184e8cd9b6c16b37699633d87f5600654b44cbcb5ab50ba872dfa001769eb765b2d1902e01d2e8af4e1fd6e9c0f30f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048e39e1e44f782b810ea93037c344371c4fb141c8bf196ea618f3a176547139a6d02121d2794cbe6481061694db579315c3184e8cd9b6c16b37699633d87f5600654b44cbcb5ab50ba872dfa001769eb765b2d1902e01d2e8af4e1fd6e9c0f30f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEjjnh5E94K4EOqTA3w0Q3HE+xQci/GW6m\nGPOhdlRxOabQISHSeUy+ZIEGFpTbV5MVwxhOjNm2wWs3aZYz2H9WAGVLRMvLWrUL\nqHLfoAF2nrdlstGQLgHS6K9OH9bpwPMP\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02304764eeac3e7a08daacfad7d1e1e3696042164b06f77bd78c3213ddea6f9fd449a34c97b9e560a6bf7195da41333c7565", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b96fca0e3f6ebf7326f0a8ce8bdf226a2560c22526bf154f7b467010f3a46baca73414070db0f7ab039f345548452ae26f7b744274e9bd6c791f47513e6b51eb42fea3816b3032b33a81695f04d4e775be06484cf7e6a69cba8bacbcb597b3e3", "wx": "00b96fca0e3f6ebf7326f0a8ce8bdf226a2560c22526bf154f7b467010f3a46baca73414070db0f7ab039f345548452ae2", "wy": "6f7b744274e9bd6c791f47513e6b51eb42fea3816b3032b33a81695f04d4e775be06484cf7e6a69cba8bacbcb597b3e3"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b96fca0e3f6ebf7326f0a8ce8bdf226a2560c22526bf154f7b467010f3a46baca73414070db0f7ab039f345548452ae26f7b744274e9bd6c791f47513e6b51eb42fea3816b3032b33a81695f04d4e775be06484cf7e6a69cba8bacbcb597b3e3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEuW/KDj9uv3Mm8KjOi98iaiVgwiUmvxVP\ne0ZwEPOka6ynNBQHDbD3qwOfNFVIRSrib3t0QnTpvWx5H0dRPmtR60L+o4FrMDKz\nOoFpXwTU53W+BkhM9+amnLqLrLy1l7Pj\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100cb4d5c0ff0abe29b2771fe9f179a5614e2e4c3cc1134a7aad08d8ec3fd8fcd07fd34b3473ca65ead1c7bb20bcf3ea5c9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044fd52b11ff747b59ef609e065a462cd85b73172d20f406fdd845d4eaa3ec173e06ee58a58e1810f051b275bbaa47ccb484d2382b9e72c526dc3764a11a4a962a7a4c7355e6f057fc976ab73cc384f9a29da50769809ecbf37358dd83c74fc25f", "wx": "4fd52b11ff747b59ef609e065a462cd85b73172d20f406fdd845d4eaa3ec173e06ee58a58e1810f051b275bbaa47ccb4", "wy": "0084d2382b9e72c526dc3764a11a4a962a7a4c7355e6f057fc976ab73cc384f9a29da50769809ecbf37358dd83c74fc25f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044fd52b11ff747b59ef609e065a462cd85b73172d20f406fdd845d4eaa3ec173e06ee58a58e1810f051b275bbaa47ccb484d2382b9e72c526dc3764a11a4a962a7a4c7355e6f057fc976ab73cc384f9a29da50769809ecbf37358dd83c74fc25f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAET9UrEf90e1nvYJ4GWkYs2FtzFy0g9Ab9\n2EXU6qPsFz4G7liljhgQ8FGydbuqR8y0hNI4K55yxSbcN2ShGkqWKnpMc1Xm8Ff8\nl2q3PMOE+aKdpQdpgJ7L83NY3YPHT8Jf\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02306e441db253bf798dbc07ff041506dc73a75086a43252fb439dd016110475d8381f65f7f27f9e1cfc9b48f06a2dfa8eb6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047d123e3dbab9913d698891023e28654cba2a94dc408a0dc386e63d8d22ff0f33358a231860b7c2e4f8429e9e8c9a1c5be7c95d1875f24ecdfeffc6136cf56f800f5434490f234f14d78505c2d4aea51e2a3a6a5d1693e72c4b1dd2a8746b875a", "wx": "7d123e3dbab9913d698891023e28654cba2a94dc408a0dc386e63d8d22ff0f33358a231860b7c2e4f8429e9e8c9a1c5b", "wy": "00e7c95d1875f24ecdfeffc6136cf56f800f5434490f234f14d78505c2d4aea51e2a3a6a5d1693e72c4b1dd2a8746b875a"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200047d123e3dbab9913d698891023e28654cba2a94dc408a0dc386e63d8d22ff0f33358a231860b7c2e4f8429e9e8c9a1c5be7c95d1875f24ecdfeffc6136cf56f800f5434490f234f14d78505c2d4aea51e2a3a6a5d1693e72c4b1dd2a8746b875a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEfRI+Pbq5kT1piJECPihlTLoqlNxAig3D\nhuY9jSL/DzM1iiMYYLfC5PhCnp6Mmhxb58ldGHXyTs3+/8YTbPVvgA9UNEkPI08U\n14UFwtSupR4qOmpdFpPnLEsd0qh0a4da\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023041db253bf798dbc07ff041506dc73a75086a43252fb43b63191efcd0914b6afb4bf8c77d008dbeac04277ef4aa59c394", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04608ce23a383452f8f4dcc5c0085d6793ec518985f0276a3409a23d7b7ca7e7dcb163601aca73840c3bd470aff70250bf674005a0be08939339363e314dca7ea67adfb60cd530628fe35f05416da8f20d5fb3b0ccd183a21dbb41c4e195d6303d", "wx": "608ce23a383452f8f4dcc5c0085d6793ec518985f0276a3409a23d7b7ca7e7dcb163601aca73840c3bd470aff70250bf", "wy": "674005a0be08939339363e314dca7ea67adfb60cd530628fe35f05416da8f20d5fb3b0ccd183a21dbb41c4e195d6303d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004608ce23a383452f8f4dcc5c0085d6793ec518985f0276a3409a23d7b7ca7e7dcb163601aca73840c3bd470aff70250bf674005a0be08939339363e314dca7ea67adfb60cd530628fe35f05416da8f20d5fb3b0ccd183a21dbb41c4e195d6303d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEYIziOjg0Uvj03MXACF1nk+xRiYXwJ2o0\nCaI9e3yn59yxY2AaynOEDDvUcK/3AlC/Z0AFoL4Ik5M5Nj4xTcp+pnrftgzVMGKP\n418FQW2o8g1fs7DM0YOiHbtBxOGV1jA9\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02310083b64a77ef31b780ffe082a0db8e74ea10d4864a5f6876c6323df9a12296d5f697f18efa011b7d58084efde954b38728", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0448d23de1869475a1de532399da1240bab560eb74a6c7b0871bf8ac8fb6cc17cf7b34fcd7c79fd99c76c605bdf3fcbe18e15b66ab91d0a03e203c2ff914d4bedc38c1ec5dcd1d12db9b43ef6f44581632683bf785aa4326566227ece3c16be796", "wx": "48d23de1869475a1de532399da1240bab560eb74a6c7b0871bf8ac8fb6cc17cf7b34fcd7c79fd99c76c605bdf3fcbe18", "wy": "00e15b66ab91d0a03e203c2ff914d4bedc38c1ec5dcd1d12db9b43ef6f44581632683bf785aa4326566227ece3c16be796"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000448d23de1869475a1de532399da1240bab560eb74a6c7b0871bf8ac8fb6cc17cf7b34fcd7c79fd99c76c605bdf3fcbe18e15b66ab91d0a03e203c2ff914d4bedc38c1ec5dcd1d12db9b43ef6f44581632683bf785aa4326566227ece3c16be796", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAESNI94YaUdaHeUyOZ2hJAurVg63Smx7CH\nG/isj7bMF897NPzXx5/ZnHbGBb3z/L4Y4Vtmq5HQoD4gPC/5FNS+3DjB7F3NHRLb\nm0Pvb0RYFjJoO/eFqkMmVmIn7OPBa+eW\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023053bf798dbc07ff041506dc73a75086a43252fb43b6327af3b42da6d3e9a72cde0b5c2de6bf072e780e94ad12dcab270a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045d5eb470f9c6a0bb18e8960b67011acf9f01df405ac5b4bf9f4611d6a8af1a26b11b0790e93ae2361525dde51bacac94d42ce151793b80cee679c848362ec272000316590ebc91547b3b6608dfbade21e04de1548ebb45cc4721eb64a16b8318", "wx": "5d5eb470f9c6a0bb18e8960b67011acf9f01df405ac5b4bf9f4611d6a8af1a26b11b0790e93ae2361525dde51bacac94", "wy": "00d42ce151793b80cee679c848362ec272000316590ebc91547b3b6608dfbade21e04de1548ebb45cc4721eb64a16b8318"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200045d5eb470f9c6a0bb18e8960b67011acf9f01df405ac5b4bf9f4611d6a8af1a26b11b0790e93ae2361525dde51bacac94d42ce151793b80cee679c848362ec272000316590ebc91547b3b6608dfbade21e04de1548ebb45cc4721eb64a16b8318", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEXV60cPnGoLsY6JYLZwEaz58B30BaxbS/\nn0YR1qivGiaxGweQ6TriNhUl3eUbrKyU1CzhUXk7gM7mechINi7CcgADFlkOvJFU\neztmCN+63iHgTeFUjrtFzEch62Sha4MY\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023024c53b0a00cf087a9a20a2b78bc81d5b383d04ba9b55a567405239d224387344c41cceff0f68ffc930dbaa0b3d346f45", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041da34a149ed562c8ec13e84cb067107bc28b50bfa47575d5a9948cde5a3d7357c38ea41fcfcdd1ab1a1bd9b6592b33d9e14aedfd0cfffcfecbdc21276e6a2c78b8729412c48339ae538b799b7d8e61163047a64cfcec9018aa00f99ae740e3f3", "wx": "1da34a149ed562c8ec13e84cb067107bc28b50bfa47575d5a9948cde5a3d7357c38ea41fcfcdd1ab1a1bd9b6592b33d9", "wy": "00e14aedfd0cfffcfecbdc21276e6a2c78b8729412c48339ae538b799b7d8e61163047a64cfcec9018aa00f99ae740e3f3"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041da34a149ed562c8ec13e84cb067107bc28b50bfa47575d5a9948cde5a3d7357c38ea41fcfcdd1ab1a1bd9b6592b33d9e14aedfd0cfffcfecbdc21276e6a2c78b8729412c48339ae538b799b7d8e61163047a64cfcec9018aa00f99ae740e3f3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEHaNKFJ7VYsjsE+hMsGcQe8KLUL+kdXXV\nqZSM3lo9c1fDjqQfz83Rqxob2bZZKzPZ4Urt/Qz//P7L3CEnbmoseLhylBLEgzmu\nU4t5m32OYRYwR6ZM/OyQGKoA+ZrnQOPz\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100c600ccb39bb3e2d85d880d76d1d519205f050c4b93deae0c5d63e8898ca8d7a5babbb944debe0f3c44332aae5770cb7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048b8675211b321f8b318ba60337cde32a6b04243979546383127a068a8749cb5e98c4231b198de62a2b069d3a94d1c7b19d33468a130b4fef66a59d4aee00ca40bdbeaf044b8b22841bb4c8ba419f891b3855f4bddf8dae3577d97120b9d3fa44", "wx": "008b8675211b321f8b318ba60337cde32a6b04243979546383127a068a8749cb5e98c4231b198de62a2b069d3a94d1c7b1", "wy": "009d33468a130b4fef66a59d4aee00ca40bdbeaf044b8b22841bb4c8ba419f891b3855f4bddf8dae3577d97120b9d3fa44"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048b8675211b321f8b318ba60337cde32a6b04243979546383127a068a8749cb5e98c4231b198de62a2b069d3a94d1c7b19d33468a130b4fef66a59d4aee00ca40bdbeaf044b8b22841bb4c8ba419f891b3855f4bddf8dae3577d97120b9d3fa44", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEi4Z1IRsyH4sxi6YDN83jKmsEJDl5VGOD\nEnoGiodJy16YxCMbGY3mKisGnTqU0cexnTNGihMLT+9mpZ1K7gDKQL2+rwRLiyKE\nG7TIukGfiRs4VfS9342uNXfZcSC50/pE\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02303ead55015c579ed137c58236bb70fe6be76628fbece64429bb655245f05cb91f4b8a499ae7880154ba83a84bf0569ae3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04442766bdb8b2cf4fef5f65d5d86b61681ec89220c983b51f15bfe12fb0bf9780e0c38bbcc888afb3c55ee828774b86f756b7f399c534c7acd46be4bc8bb38f087b0023b8f5166ab34192ca0b1cad62d663aa474c6f9286c8a054ef94ea42e3c7", "wx": "442766bdb8b2cf4fef5f65d5d86b61681ec89220c983b51f15bfe12fb0bf9780e0c38bbcc888afb3c55ee828774b86f7", "wy": "56b7f399c534c7acd46be4bc8bb38f087b0023b8f5166ab34192ca0b1cad62d663aa474c6f9286c8a054ef94ea42e3c7"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004442766bdb8b2cf4fef5f65d5d86b61681ec89220c983b51f15bfe12fb0bf9780e0c38bbcc888afb3c55ee828774b86f756b7f399c534c7acd46be4bc8bb38f087b0023b8f5166ab34192ca0b1cad62d663aa474c6f9286c8a054ef94ea42e3c7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERCdmvbiyz0/vX2XV2GthaB7IkiDJg7Uf\nFb/hL7C/l4Dgw4u8yIivs8Ve6Ch3S4b3VrfzmcU0x6zUa+S8i7OPCHsAI7j1Fmqz\nQZLKCxytYtZjqkdMb5KGyKBU75TqQuPH\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100de03ff820a836e39d3a8435219297da1db193d79e359663e7cc9a229e2a6ac9e9d5c75417fa455bc8e3b89274ee47d0e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0411342b314f31648931abb897c1371dd3a23e91f2405c4a81744be18e753919752208779de2d54e865eeefbb0bfb4998af533d7a4d6fc6cb5cb98915ce08d0f656e37a502e78f8c1b8baca728c2ecb05a2156f01cff16595b363cdb49c00c1aa2", "wx": "11342b314f31648931abb897c1371dd3a23e91f2405c4a81744be18e753919752208779de2d54e865eeefbb0bfb4998a", "wy": "00f533d7a4d6fc6cb5cb98915ce08d0f656e37a502e78f8c1b8baca728c2ecb05a2156f01cff16595b363cdb49c00c1aa2"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000411342b314f31648931abb897c1371dd3a23e91f2405c4a81744be18e753919752208779de2d54e865eeefbb0bfb4998af533d7a4d6fc6cb5cb98915ce08d0f656e37a502e78f8c1b8baca728c2ecb05a2156f01cff16595b363cdb49c00c1aa2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEETQrMU8xZIkxq7iXwTcd06I+kfJAXEqB\ndEvhjnU5GXUiCHed4tVOhl7u+7C/tJmK9TPXpNb8bLXLmJFc4I0PZW43pQLnj4wb\ni6ynKMLssFohVvAc/xZZWzY820nADBqi\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100e5a6ae07f855f14d93b8ff4f8bcd2b0a717261e6089a53d54bf86e22f8e37d73aaa7607cc2ab831404b3e5bb4e01e79e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043c96b49ff60ff05951b7b1aca65664f13128b714da620697ef0d90bfc01ef643baa5c608f16ca885038322a443aed3e6169a27f2ea7a36376ef92a900e5389a7b441fd051d693ce65250b881cfdd6487370372292c84369742b18106188b05c0", "wx": "3c96b49ff60ff05951b7b1aca65664f13128b714da620697ef0d90bfc01ef643baa5c608f16ca885038322a443aed3e6", "wy": "169a27f2ea7a36376ef92a900e5389a7b441fd051d693ce65250b881cfdd6487370372292c84369742b18106188b05c0"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043c96b49ff60ff05951b7b1aca65664f13128b714da620697ef0d90bfc01ef643baa5c608f16ca885038322a443aed3e6169a27f2ea7a36376ef92a900e5389a7b441fd051d693ce65250b881cfdd6487370372292c84369742b18106188b05c0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEPJa0n/YP8FlRt7GsplZk8TEotxTaYgaX\n7w2Qv8Ae9kO6pcYI8WyohQODIqRDrtPmFpon8up6Njdu+SqQDlOJp7RB/QUdaTzm\nUlC4gc/dZIc3A3IpLIQ2l0KxgQYYiwXA\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffed2119d5fc12649fc808af3b6d9037d3a44eb32399970dd0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04388dae49ea48afb558456fdb1d0b04d4f8f1c46f14d22de25862d35069a28ae9284d7a8074546e779ad2c5f17ce9b89bb353298f3c526aa0a10ed23bcb1ed9788812c8a3a6cbea82a3d9d8d465a4cca59dbd3d3d8a36098d644f1b45d36df537", "wx": "388dae49ea48afb558456fdb1d0b04d4f8f1c46f14d22de25862d35069a28ae9284d7a8074546e779ad2c5f17ce9b89b", "wy": "00b353298f3c526aa0a10ed23bcb1ed9788812c8a3a6cbea82a3d9d8d465a4cca59dbd3d3d8a36098d644f1b45d36df537"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004388dae49ea48afb558456fdb1d0b04d4f8f1c46f14d22de25862d35069a28ae9284d7a8074546e779ad2c5f17ce9b89bb353298f3c526aa0a10ed23bcb1ed9788812c8a3a6cbea82a3d9d8d465a4cca59dbd3d3d8a36098d644f1b45d36df537", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEOI2uSepIr7VYRW/bHQsE1PjxxG8U0i3i\nWGLTUGmiiukoTXqAdFRud5rSxfF86bibs1MpjzxSaqChDtI7yx7ZeIgSyKOmy+qC\no9nY1GWkzKWdvT09ijYJjWRPG0XTbfU3\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023079b95c013b0472de04d8faeec3b779c39fe729ea84fb554cd091c7178c2f054eabbc62c3e1cfbac2c2e69d7aa45d9072", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04c85200ac6411423573e3ebc1b7aea95e74add5ce3b41282baa885972acc085c8365c05c539ce47e799afc353d6788ce868cfce1eb2bfe009990084fb03c0919ab892313d7a12efc3514e8273685b9071892faefca4306adf7854afcebafffbf4", "wx": "00c85200ac6411423573e3ebc1b7aea95e74add5ce3b41282baa885972acc085c8365c05c539ce47e799afc353d6788ce8", "wy": "68cfce1eb2bfe009990084fb03c0919ab892313d7a12efc3514e8273685b9071892faefca4306adf7854afcebafffbf4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004c85200ac6411423573e3ebc1b7aea95e74add5ce3b41282baa885972acc085c8365c05c539ce47e799afc353d6788ce868cfce1eb2bfe009990084fb03c0919ab892313d7a12efc3514e8273685b9071892faefca4306adf7854afcebafffbf4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEyFIArGQRQjVz4+vBt66pXnSt1c47QSgr\nqohZcqzAhcg2XAXFOc5H55mvw1PWeIzoaM/OHrK/4AmZAIT7A8CRmriSMT16Eu/D\nUU6Cc2hbkHGJL678pDBq33hUr866//v0\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100bfd40d0caa4d9d42381f3d72a25683f52b03a1ed96fb72d03f08dcb9a8bc8f23c1a459deab03bcd39396c0d1e9053c81", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e63ae2881ed60884ef1aef52178a297bdfedf67f4e3c1d876ad10b42c03b5e67f7f8cfaf4dfea4def7ab82fde3ed9b910e2be22bc3fa46a2ed094ebd7c86a9512c8c40cd542fb539c34347ef2be4e7f1543af960fd2347354a7a1df71a237d51", "wx": "00e63ae2881ed60884ef1aef52178a297bdfedf67f4e3c1d876ad10b42c03b5e67f7f8cfaf4dfea4def7ab82fde3ed9b91", "wy": "0e2be22bc3fa46a2ed094ebd7c86a9512c8c40cd542fb539c34347ef2be4e7f1543af960fd2347354a7a1df71a237d51"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e63ae2881ed60884ef1aef52178a297bdfedf67f4e3c1d876ad10b42c03b5e67f7f8cfaf4dfea4def7ab82fde3ed9b910e2be22bc3fa46a2ed094ebd7c86a9512c8c40cd542fb539c34347ef2be4e7f1543af960fd2347354a7a1df71a237d51", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE5jriiB7WCITvGu9SF4ope9/t9n9OPB2H\natELQsA7Xmf3+M+vTf6k3vergv3j7ZuRDiviK8P6RqLtCU69fIapUSyMQM1UL7U5\nw0NH7yvk5/FUOvlg/SNHNUp6HfcaI31R\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02304c7d219db9af94ce7fffffffffffffffffffffffffffffffef15cf1058c8d8ba1e634c4122db95ec1facd4bb13ebf09a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e9c415f8a72055239570c3c370cf9380cdfabb6ebdbd8058e2fc65193080707895ea1566eeb26149603f4b4d4c1e79d496ae17a001424d21eae4eaa01067048bcd919625fdd7efd896d980633a0e2ca1f8c9b02c99b69a1e4fa53468a2fe244d", "wx": "00e9c415f8a72055239570c3c370cf9380cdfabb6ebdbd8058e2fc65193080707895ea1566eeb26149603f4b4d4c1e79d4", "wy": "0096ae17a001424d21eae4eaa01067048bcd919625fdd7efd896d980633a0e2ca1f8c9b02c99b69a1e4fa53468a2fe244d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e9c415f8a72055239570c3c370cf9380cdfabb6ebdbd8058e2fc65193080707895ea1566eeb26149603f4b4d4c1e79d496ae17a001424d21eae4eaa01067048bcd919625fdd7efd896d980633a0e2ca1f8c9b02c99b69a1e4fa53468a2fe244d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE6cQV+KcgVSOVcMPDcM+TgM36u269vYBY\n4vxlGTCAcHiV6hVm7rJhSWA/S01MHnnUlq4XoAFCTSHq5OqgEGcEi82RliX91+/Y\nltmAYzoOLKH4ybAsmbaaHk+lNGii/iRN\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100d219db9af94ce7ffffffffffffffffffffffffffffffffffd189bdb6d9ef7be8504ca374756ea5b8f15e44067d209b9b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04637223a93dd63af6b348f246e7b3bcb30beaa1dcc888af8e12e5086aa00f7792fbe457463c52422d435f430ad1bb4b21f9a1e01758d1e025b162d09d3df8b403226ed3b35e414c41651740d509d8cf6b5e558118607d10669902abebda3ca28d", "wx": "637223a93dd63af6b348f246e7b3bcb30beaa1dcc888af8e12e5086aa00f7792fbe457463c52422d435f430ad1bb4b21", "wy": "00f9a1e01758d1e025b162d09d3df8b403226ed3b35e414c41651740d509d8cf6b5e558118607d10669902abebda3ca28d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004637223a93dd63af6b348f246e7b3bcb30beaa1dcc888af8e12e5086aa00f7792fbe457463c52422d435f430ad1bb4b21f9a1e01758d1e025b162d09d3df8b403226ed3b35e414c41651740d509d8cf6b5e558118607d10669902abebda3ca28d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEY3IjqT3WOvazSPJG57O8swvqodzIiK+O\nEuUIaqAPd5L75FdGPFJCLUNfQwrRu0sh+aHgF1jR4CWxYtCdPfi0AyJu07NeQUxB\nZRdA1QnYz2teVYEYYH0QZpkCq+vaPKKN\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a433b735f299cfffffffffffffffffffffffffffffffffffdbb02debbfa7c9f1487f3936a22ca3f6f5d06ea22d7c0dc3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047f4dc23982ecc8b84f54241715c7e94e950f596ce033237639a15fefa5eb5c37cb2e562d6d5b3051ea15600e3341a565fed2b55b89d2793321374887b78827ee4ca2216eac2993b1b095844db76adc560450135c072ac1a2c4167520237fbc9d", "wx": "7f4dc23982ecc8b84f54241715c7e94e950f596ce033237639a15fefa5eb5c37cb2e562d6d5b3051ea15600e3341a565", "wy": "00fed2b55b89d2793321374887b78827ee4ca2216eac2993b1b095844db76adc560450135c072ac1a2c4167520237fbc9d"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200047f4dc23982ecc8b84f54241715c7e94e950f596ce033237639a15fefa5eb5c37cb2e562d6d5b3051ea15600e3341a565fed2b55b89d2793321374887b78827ee4ca2216eac2993b1b095844db76adc560450135c072ac1a2c4167520237fbc9d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEf03COYLsyLhPVCQXFcfpTpUPWWzgMyN2\nOaFf76XrXDfLLlYtbVswUeoVYA4zQaVl/tK1W4nSeTMhN0iHt4gn7kyiIW6sKZOx\nsJWETbdq3FYEUBNcByrBosQWdSAjf7yd\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100b9af94ce7fffffffffffffffffffffffffffffffffffffffd6efeefc876c9f23217b443c80637ef939e911219f96c179", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a0ae8c949f63f1b6a5d024c99e0a296ecd12d196d3b1625d4a76600082a14d455aab267c68f571d89ad0619cb8e476a134634336611e1fd1d728bcea588d0e1b652bbca0e52c1bfbd4387a6337ff41ce13a65c8306915d2a39897b985d909b36", "wx": "00a0ae8c949f63f1b6a5d024c99e0a296ecd12d196d3b1625d4a76600082a14d455aab267c68f571d89ad0619cb8e476a1", "wy": "34634336611e1fd1d728bcea588d0e1b652bbca0e52c1bfbd4387a6337ff41ce13a65c8306915d2a39897b985d909b36"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a0ae8c949f63f1b6a5d024c99e0a296ecd12d196d3b1625d4a76600082a14d455aab267c68f571d89ad0619cb8e476a134634336611e1fd1d728bcea588d0e1b652bbca0e52c1bfbd4387a6337ff41ce13a65c8306915d2a39897b985d909b36", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEoK6MlJ9j8bal0CTJngopbs0S0ZbTsWJd\nSnZgAIKhTUVaqyZ8aPVx2JrQYZy45HahNGNDNmEeH9HXKLzqWI0OG2UrvKDlLBv7\n1Dh6Yzf/Qc4TplyDBpFdKjmJe5hdkJs2\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100a276276276276276276276276276276276276276276276273d7228d4f84b769be0fd57b97e4c1ebcae9a5f635e80e9df", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047cad1637721f5988cb7967238b1f47fd0b63f30f207a165951fc6fb74ba868e5b462628595edc80f75182e564a89c7a0fc04c405938aab3d6828e72e86bc59a400719270f8ee3cb5ef929ab53287bb308b51abd2e3ffbc3d93b87471bc2e3730", "wx": "7cad1637721f5988cb7967238b1f47fd0b63f30f207a165951fc6fb74ba868e5b462628595edc80f75182e564a89c7a0", "wy": "00fc04c405938aab3d6828e72e86bc59a400719270f8ee3cb5ef929ab53287bb308b51abd2e3ffbc3d93b87471bc2e3730"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200047cad1637721f5988cb7967238b1f47fd0b63f30f207a165951fc6fb74ba868e5b462628595edc80f75182e564a89c7a0fc04c405938aab3d6828e72e86bc59a400719270f8ee3cb5ef929ab53287bb308b51abd2e3ffbc3d93b87471bc2e3730", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEfK0WN3IfWYjLeWcjix9H/Qtj8w8gehZZ\nUfxvt0uoaOW0YmKFle3ID3UYLlZKiceg/ATEBZOKqz1oKOcuhrxZpABxknD47jy1\n75KatTKHuzCLUavS4/+8PZO4dHG8Ljcw\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023073333333333333333333333333333333333333333333333316e4d9f42d4eca22df403a0c578b86f0a9a93fe89995c7ed", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042024ecde0e61262955b0301ae6b0a4fbd7771762feb2de35eed1823d2636c6e001f7bfcdbc4e65b1ea40224090411906d55362a570e80a2126f01d919b608440294039be03419d518b13cca6a1595414717f1b4ddb842b2c9d4f543e683b86a0", "wx": "2024ecde0e61262955b0301ae6b0a4fbd7771762feb2de35eed1823d2636c6e001f7bfcdbc4e65b1ea40224090411906", "wy": "00d55362a570e80a2126f01d919b608440294039be03419d518b13cca6a1595414717f1b4ddb842b2c9d4f543e683b86a0"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042024ecde0e61262955b0301ae6b0a4fbd7771762feb2de35eed1823d2636c6e001f7bfcdbc4e65b1ea40224090411906d55362a570e80a2126f01d919b608440294039be03419d518b13cca6a1595414717f1b4ddb842b2c9d4f543e683b86a0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEICTs3g5hJilVsDAa5rCk+9d3F2L+st41\n7tGCPSY2xuAB97/NvE5lsepAIkCQQRkG1VNipXDoCiEm8B2Rm2CEQClAOb4DQZ1R\nixPMpqFZVBRxfxtN24QrLJ1PVD5oO4ag\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02307fffffffffffffffffffffffffffffffffffffffffffffffda4233abf824c93f90115e76db206fa7489d6647332e1ba3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0440c5f2608956380c39695c7457ddce0880b5e8fab0a9a3726d0c8535b2ff6ca15814d83ed82c0ab33aba76e05e5c0476c9d15a2a0b2041237ff61c26519d1d74b141d7a4499fbdefc414a900937a8faf6ef560550c73cdb7edfe9314c480bb2b", "wx": "40c5f2608956380c39695c7457ddce0880b5e8fab0a9a3726d0c8535b2ff6ca15814d83ed82c0ab33aba76e05e5c0476", "wy": "00c9d15a2a0b2041237ff61c26519d1d74b141d7a4499fbdefc414a900937a8faf6ef560550c73cdb7edfe9314c480bb2b"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000440c5f2608956380c39695c7457ddce0880b5e8fab0a9a3726d0c8535b2ff6ca15814d83ed82c0ab33aba76e05e5c0476c9d15a2a0b2041237ff61c26519d1d74b141d7a4499fbdefc414a900937a8faf6ef560550c73cdb7edfe9314c480bb2b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEQMXyYIlWOAw5aVx0V93OCIC16PqwqaNy\nbQyFNbL/bKFYFNg+2CwKszq6duBeXAR2ydFaKgsgQSN/9hwmUZ0ddLFB16RJn73v\nxBSpAJN6j69u9WBVDHPNt+3+kxTEgLsr\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02303fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0474acdfd2ab763c593bca30d248f2bf26f1843acf9eb89b4dfcb8451d59683812cf3cbe9a264ea435912a8969c53d7cb8496dcb0a4efed69b87110fda20e68eb6feed2d5101a4955d43759f10b73e8ffc3131e0c12a765b68bd216ed1ec4f5d2f", "wx": "74acdfd2ab763c593bca30d248f2bf26f1843acf9eb89b4dfcb8451d59683812cf3cbe9a264ea435912a8969c53d7cb8", "wy": "496dcb0a4efed69b87110fda20e68eb6feed2d5101a4955d43759f10b73e8ffc3131e0c12a765b68bd216ed1ec4f5d2f"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000474acdfd2ab763c593bca30d248f2bf26f1843acf9eb89b4dfcb8451d59683812cf3cbe9a264ea435912a8969c53d7cb8496dcb0a4efed69b87110fda20e68eb6feed2d5101a4955d43759f10b73e8ffc3131e0c12a765b68bd216ed1ec4f5d2f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEdKzf0qt2PFk7yjDSSPK/JvGEOs+euJtN\n/LhFHVloOBLPPL6aJk6kNZEqiWnFPXy4SW3LCk7+1puHEQ/aIOaOtv7tLVEBpJVd\nQ3WfELc+j/wxMeDBKnZbaL0hbtHsT10v\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd023100dfea06865526cea11c0f9eb9512b41fa9581d0f6cb7db9680336151dce79de818cdf33c879da322740416d1e5ae532fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a3991734945baab27ca6383737b7dd45023f997aff5e165f0fd7d8e5c0b5f9c5e731588af2fe5bd8976a0b871c132edf21f363af", "wx": "00da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a3991734", "wy": "00945baab27ca6383737b7dd45023f997aff5e165f0fd7d8e5c0b5f9c5e731588af2fe5bd8976a0b871c132edf21f363af"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a3991734945baab27ca6383737b7dd45023f997aff5e165f0fd7d8e5c0b5f9c5e731588af2fe5bd8976a0b871c132edf21f363af", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE2jXWqCgYrlJUy2X8hqxCpHhzqyR6XKZk\n6fCV6N6aV/5yGGDmbLxr1JlDGkijmRc0lFuqsnymODc3t91FAj+Zev9eFl8P19jl\nwLX5xecxWIry/lvYl2oLhxwTLt8h82Ov\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "point duplication during verification", "msg": "313233343030", "sig": "3066023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce60023100e16043c2face20228dba6366e19ecc6db71b918bbe8a890b9dad2fcead184e071c9ac4acaee2f831a1e4cc337994f5ec", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a39917346ba4554d8359c7c8c84822bafdc0668500a1e9a0f028271a3f4a063a18cea7740d01a4266895f478e3ecd121de0c9c50", "wx": "00da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a3991734", "wy": "6ba4554d8359c7c8c84822bafdc0668500a1e9a0f028271a3f4a063a18cea7740d01a4266895f478e3ecd121de0c9c50"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004da35d6a82818ae5254cb65fc86ac42a47873ab247a5ca664e9f095e8de9a57fe721860e66cbc6bd499431a48a39917346ba4554d8359c7c8c84822bafdc0668500a1e9a0f028271a3f4a063a18cea7740d01a4266895f478e3ecd121de0c9c50", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE2jXWqCgYrlJUy2X8hqxCpHhzqyR6XKZk\n6fCV6N6aV/5yGGDmbLxr1JlDGkijmRc0a6RVTYNZx8jISCK6/cBmhQCh6aDwKCca\nP0oGOhjOp3QNAaQmaJX0eOPs0SHeDJxQ\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "duplication bug", "msg": "313233343030", "sig": "3066023100b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce60023100e16043c2face20228dba6366e19ecc6db71b918bbe8a890b9dad2fcead184e071c9ac4acaee2f831a1e4cc337994f5ec", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04820064193c71c7141fe41e711fe843a7474be6b05f50cb0be411cdf7fc78ea7ec96aeb3991ef7646bbde59152d381a32631c5adf93d488b45e67cc9890d8e779f63960193dc16bd1cc136b3e28cf499dfa8e7bff482a0115e6083987f7c042fc", "wx": "00820064193c71c7141fe41e711fe843a7474be6b05f50cb0be411cdf7fc78ea7ec96aeb3991ef7646bbde59152d381a32", "wy": "631c5adf93d488b45e67cc9890d8e779f63960193dc16bd1cc136b3e28cf499dfa8e7bff482a0115e6083987f7c042fc"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004820064193c71c7141fe41e711fe843a7474be6b05f50cb0be411cdf7fc78ea7ec96aeb3991ef7646bbde59152d381a32631c5adf93d488b45e67cc9890d8e779f63960193dc16bd1cc136b3e28cf499dfa8e7bff482a0115e6083987f7c042fc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEggBkGTxxxxQf5B5xH+hDp0dL5rBfUMsL\n5BHN9/x46n7Jaus5ke92RrveWRUtOBoyYxxa35PUiLReZ8yYkNjnefY5YBk9wWvR\nzBNrPijPSZ36jnv/SCoBFeYIOYf3wEL8\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3035020101023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0452fabc58eacfd3a4828f51c413205c20888941ee45ecac076ffc23145d83542034aa01253d6ebf34eeefaa371d6cee119f340712cd78155712746578f5632ded2b2e5afb43b085f81732792108e331a4b50d27f3578252ffb0daa9d78655a0ab", "wx": "52fabc58eacfd3a4828f51c413205c20888941ee45ecac076ffc23145d83542034aa01253d6ebf34eeefaa371d6cee11", "wy": "009f340712cd78155712746578f5632ded2b2e5afb43b085f81732792108e331a4b50d27f3578252ffb0daa9d78655a0ab"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000452fabc58eacfd3a4828f51c413205c20888941ee45ecac076ffc23145d83542034aa01253d6ebf34eeefaa371d6cee119f340712cd78155712746578f5632ded2b2e5afb43b085f81732792108e331a4b50d27f3578252ffb0daa9d78655a0ab", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEUvq8WOrP06SCj1HEEyBcIIiJQe5F7KwH\nb/wjFF2DVCA0qgElPW6/NO7vqjcdbO4RnzQHEs14FVcSdGV49WMt7SsuWvtDsIX4\nFzJ5IQjjMaS1DSfzV4JS/7DaqdeGVaCr\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3065023101000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a8fdb1a022d4e3a7ee29612bb110acbea27daecb827d344cb6c6a7acad61d371ddc7842147b74a18767e618712f04c1c64ac6daf8e08cd7b90a0c9d9123884c7a7abb4664a75b0897064c3c8956b0ca9c417237f8d5a7dd8421b0d48c9d52c7c", "wx": "00a8fdb1a022d4e3a7ee29612bb110acbea27daecb827d344cb6c6a7acad61d371ddc7842147b74a18767e618712f04c1c", "wy": "64ac6daf8e08cd7b90a0c9d9123884c7a7abb4664a75b0897064c3c8956b0ca9c417237f8d5a7dd8421b0d48c9d52c7c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a8fdb1a022d4e3a7ee29612bb110acbea27daecb827d344cb6c6a7acad61d371ddc7842147b74a18767e618712f04c1c64ac6daf8e08cd7b90a0c9d9123884c7a7abb4664a75b0897064c3c8956b0ca9c417237f8d5a7dd8421b0d48c9d52c7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqP2xoCLU46fuKWErsRCsvqJ9rsuCfTRM\ntsanrK1h03Hdx4QhR7dKGHZ+YYcS8EwcZKxtr44IzXuQoMnZEjiEx6ertGZKdbCJ\ncGTDyJVrDKnEFyN/jVp92EIbDUjJ1Sx8\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "3064023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326023033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04878e414a5d6a0e0d1ab3c5563c44e80c3b2ef265f27a33ed5cac109ad664c1269beae9031d8d178cbfdb1bfa7cc3cc79fabbb2b6f7ce54026863b0f297a4fe3de82d5044dacafede49d5afc60bc875f4b659c06c19bb74c7c27351687f52b411", "wx": "00878e414a5d6a0e0d1ab3c5563c44e80c3b2ef265f27a33ed5cac109ad664c1269beae9031d8d178cbfdb1bfa7cc3cc79", "wy": "00fabbb2b6f7ce54026863b0f297a4fe3de82d5044dacafede49d5afc60bc875f4b659c06c19bb74c7c27351687f52b411"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004878e414a5d6a0e0d1ab3c5563c44e80c3b2ef265f27a33ed5cac109ad664c1269beae9031d8d178cbfdb1bfa7cc3cc79fabbb2b6f7ce54026863b0f297a4fe3de82d5044dacafede49d5afc60bc875f4b659c06c19bb74c7c27351687f52b411", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEh45BSl1qDg0as8VWPEToDDsu8mXyejPt\nXKwQmtZkwSab6ukDHY0XjL/bG/p8w8x5+ruytvfOVAJoY7Dyl6T+PegtUETayv7e\nSdWvxgvIdfS2WcBsGbt0x8JzUWh/UrQR\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048faa8497ae3006b612999b03f91f7884d95543a266598e897b71e44ecfd9abd7908bfd122bb366c016a577cb1b2e2e412bb1a719289c749804ca677d14c0900fab031da8c70724723a0d54e3a0035da7dcddeef6fce80df2f81940817d27b2b5", "wx": "008faa8497ae3006b612999b03f91f7884d95543a266598e897b71e44ecfd9abd7908bfd122bb366c016a577cb1b2e2e41", "wy": "2bb1a719289c749804ca677d14c0900fab031da8c70724723a0d54e3a0035da7dcddeef6fce80df2f81940817d27b2b5"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200048faa8497ae3006b612999b03f91f7884d95543a266598e897b71e44ecfd9abd7908bfd122bb366c016a577cb1b2e2e412bb1a719289c749804ca677d14c0900fab031da8c70724723a0d54e3a0035da7dcddeef6fce80df2f81940817d27b2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEj6qEl64wBrYSmZsD+R94hNlVQ6JmWY6J\ne3HkTs/Zq9eQi/0SK7NmwBald8sbLi5BK7GnGSicdJgEymd9FMCQD6sDHajHByRy\nOg1U46ADXafc3e72/OgN8vgZQIF9J7K1\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04c59cc648629e62dc1855f653583da0ace631e0f4b4589b7fe5cc449e12df2dceeb862cae00cd100233b999af657ae16cb138f659dcc8d342fd17664d86c5bddaa866c20b0031f65c8442a0ed62b337d09adb63a443ab14e3587b9299053717f9", "wx": "00c59cc648629e62dc1855f653583da0ace631e0f4b4589b7fe5cc449e12df2dceeb862cae00cd100233b999af657ae16c", "wy": "00b138f659dcc8d342fd17664d86c5bddaa866c20b0031f65c8442a0ed62b337d09adb63a443ab14e3587b9299053717f9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004c59cc648629e62dc1855f653583da0ace631e0f4b4589b7fe5cc449e12df2dceeb862cae00cd100233b999af657ae16cb138f659dcc8d342fd17664d86c5bddaa866c20b0031f65c8442a0ed62b337d09adb63a443ab14e3587b9299053717f9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAExZzGSGKeYtwYVfZTWD2grOYx4PS0WJt/\n5cxEnhLfLc7rhiyuAM0QAjO5ma9leuFssTj2WdzI00L9F2ZNhsW92qhmwgsAMfZc\nhEKg7WKzN9Ca22OkQ6sU41h7kpkFNxf5\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04386bdc98fe3c156a790eee6d556e0036a4b84853358bd5ab6856db5985b9e8ea92e8d4c1f8d04ecd1e6de4548bf288215503292c2c570f57b42f2caf5e7ab94d87817a800b2af6ffcd4f13e30edb8caaf23c6d5be22abea18c2f9450ad1a4715", "wx": "386bdc98fe3c156a790eee6d556e0036a4b84853358bd5ab6856db5985b9e8ea92e8d4c1f8d04ecd1e6de4548bf28821", "wy": "5503292c2c570f57b42f2caf5e7ab94d87817a800b2af6ffcd4f13e30edb8caaf23c6d5be22abea18c2f9450ad1a4715"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004386bdc98fe3c156a790eee6d556e0036a4b84853358bd5ab6856db5985b9e8ea92e8d4c1f8d04ecd1e6de4548bf288215503292c2c570f57b42f2caf5e7ab94d87817a800b2af6ffcd4f13e30edb8caaf23c6d5be22abea18c2f9450ad1a4715", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEOGvcmP48FWp5Du5tVW4ANqS4SFM1i9Wr\naFbbWYW56OqS6NTB+NBOzR5t5FSL8oghVQMpLCxXD1e0LyyvXnq5TYeBeoALKvb/\nzU8T4w7bjKryPG1b4iq+oYwvlFCtGkcV\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04294c37b3ec91a1b0500042d8b97bc9619d17f784a9ea528c0602d700783bfbac9ac49bff1e527b39bb2a49d1dc3abd471e798679b7c58f4dfa33cfe40bb62e7df6d2f190b0f3804c700fa19eba28ad7fd6edd7e3a754af852921c2705f444f0b", "wx": "294c37b3ec91a1b0500042d8b97bc9619d17f784a9ea528c0602d700783bfbac9ac49bff1e527b39bb2a49d1dc3abd47", "wy": "1e798679b7c58f4dfa33cfe40bb62e7df6d2f190b0f3804c700fa19eba28ad7fd6edd7e3a754af852921c2705f444f0b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004294c37b3ec91a1b0500042d8b97bc9619d17f784a9ea528c0602d700783bfbac9ac49bff1e527b39bb2a49d1dc3abd471e798679b7c58f4dfa33cfe40bb62e7df6d2f190b0f3804c700fa19eba28ad7fd6edd7e3a754af852921c2705f444f0b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKUw3s+yRobBQAELYuXvJYZ0X94Sp6lKM\nBgLXAHg7+6yaxJv/HlJ7ObsqSdHcOr1HHnmGebfFj036M8/kC7YuffbS8ZCw84BM\ncA+hnroorX/W7dfjp1SvhSkhwnBfRE8L\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bac7cd8a7755a174fab58e5374ec55a5ce5313235ec51c919c6684bd49305b7005393f72bc4d810ca864fb046d2c83415a33b77f4145680bde63b669ea1f10f3ee1836018c11a6f97155d90827c83dbac388402ac8f59368ddaf2c33548611af", "wx": "00bac7cd8a7755a174fab58e5374ec55a5ce5313235ec51c919c6684bd49305b7005393f72bc4d810ca864fb046d2c8341", "wy": "5a33b77f4145680bde63b669ea1f10f3ee1836018c11a6f97155d90827c83dbac388402ac8f59368ddaf2c33548611af"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bac7cd8a7755a174fab58e5374ec55a5ce5313235ec51c919c6684bd49305b7005393f72bc4d810ca864fb046d2c83415a33b77f4145680bde63b669ea1f10f3ee1836018c11a6f97155d90827c83dbac388402ac8f59368ddaf2c33548611af", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEusfNindVoXT6tY5TdOxVpc5TEyNexRyR\nnGaEvUkwW3AFOT9yvE2BDKhk+wRtLINBWjO3f0FFaAveY7Zp6h8Q8+4YNgGMEab5\ncVXZCCfIPbrDiEAqyPWTaN2vLDNUhhGv\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "extreme value for k", "msg": "313233343030", "sig": "3064023008d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6102300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04984a1c04446a52ad6a54d64f2c6c49b61f23abe7dc6f33714896aefb0befb9a52b95b048561132c28c9850e851a6d00eb4e19f9de59d30ca26801f2789a3330b081e6bf57f84f3c6107defd05a959cef5f298acea5a6b87b38e22c5409ec9f71", "wx": "00984a1c04446a52ad6a54d64f2c6c49b61f23abe7dc6f33714896aefb0befb9a52b95b048561132c28c9850e851a6d00e", "wy": "00b4e19f9de59d30ca26801f2789a3330b081e6bf57f84f3c6107defd05a959cef5f298acea5a6b87b38e22c5409ec9f71"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004984a1c04446a52ad6a54d64f2c6c49b61f23abe7dc6f33714896aefb0befb9a52b95b048561132c28c9850e851a6d00eb4e19f9de59d30ca26801f2789a3330b081e6bf57f84f3c6107defd05a959cef5f298acea5a6b87b38e22c5409ec9f71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEmEocBERqUq1qVNZPLGxJth8jq+fcbzNx\nSJau+wvvuaUrlbBIVhEywoyYUOhRptAOtOGfneWdMMomgB8niaMzCwgea/V/hPPG\nEH3v0FqVnO9fKYrOpaa4ezjiLFQJ7J9x\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023055555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f00d6327b1226eaa1b0897295eeddadf7510249e6f0f811b57d7197eb6e61199a8f1c6665ec4821d3e18675d5399fdf787bf1e3fb7fee5cb3582a4159808b75e8b1de07eaffd49d3882d15c77443ad83213d21a4be9285223aa44a840e47eb56", "wx": "00f00d6327b1226eaa1b0897295eeddadf7510249e6f0f811b57d7197eb6e61199a8f1c6665ec4821d3e18675d5399fdf7", "wy": "0087bf1e3fb7fee5cb3582a4159808b75e8b1de07eaffd49d3882d15c77443ad83213d21a4be9285223aa44a840e47eb56"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f00d6327b1226eaa1b0897295eeddadf7510249e6f0f811b57d7197eb6e61199a8f1c6665ec4821d3e18675d5399fdf787bf1e3fb7fee5cb3582a4159808b75e8b1de07eaffd49d3882d15c77443ad83213d21a4be9285223aa44a840e47eb56", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE8A1jJ7EibqobCJcpXu3a33UQJJ5vD4Eb\nV9cZfrbmEZmo8cZmXsSCHT4YZ11Tmf33h78eP7f+5cs1gqQVmAi3Xosd4H6v/UnT\niC0Vx3RDrYMhPSGkvpKFIjqkSoQOR+tW\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04452b047743346898b087daaac5d982d378752ba534e569f21ac592c09654d0809b94ccf822045f2885cbd3b221453cd668a01f502f551af14aab35c2c30ec7bac0709f525fe7960439b1e9de53cdad245efd8930967cde6caf8d222c8200cd69", "wx": "452b047743346898b087daaac5d982d378752ba534e569f21ac592c09654d0809b94ccf822045f2885cbd3b221453cd6", "wy": "68a01f502f551af14aab35c2c30ec7bac0709f525fe7960439b1e9de53cdad245efd8930967cde6caf8d222c8200cd69"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004452b047743346898b087daaac5d982d378752ba534e569f21ac592c09654d0809b94ccf822045f2885cbd3b221453cd668a01f502f551af14aab35c2c30ec7bac0709f525fe7960439b1e9de53cdad245efd8930967cde6caf8d222c8200cd69", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERSsEd0M0aJiwh9qqxdmC03h1K6U05Wny\nGsWSwJZU0ICblMz4IgRfKIXL07IhRTzWaKAfUC9VGvFKqzXCww7HusBwn1Jf55YE\nObHp3lPNrSRe/YkwlnzebK+NIiyCAM1p\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702306666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0444a8f54795bdb81e00fc84fa8373d125b16da6e2bf4cfa9ee1dc13d7f157394683963c170f4c15e8cf21b5466b49fa72bb5693655b3e0a85e27e3e6d265fba0131f3083bf447f62b6e3e5275496f34daa522e16195d81488a31fe982c2b75f16", "wx": "44a8f54795bdb81e00fc84fa8373d125b16da6e2bf4cfa9ee1dc13d7f157394683963c170f4c15e8cf21b5466b49fa72", "wy": "00bb5693655b3e0a85e27e3e6d265fba0131f3083bf447f62b6e3e5275496f34daa522e16195d81488a31fe982c2b75f16"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000444a8f54795bdb81e00fc84fa8373d125b16da6e2bf4cfa9ee1dc13d7f157394683963c170f4c15e8cf21b5466b49fa72bb5693655b3e0a85e27e3e6d265fba0131f3083bf447f62b6e3e5275496f34daa522e16195d81488a31fe982c2b75f16", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERKj1R5W9uB4A/IT6g3PRJbFtpuK/TPqe\n4dwT1/FXOUaDljwXD0wV6M8htUZrSfpyu1aTZVs+CoXifj5tJl+6ATHzCDv0R/Yr\nbj5SdUlvNNqlIuFhldgUiKMf6YLCt18W\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702310099999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0410b336b3afb80c80ff50716e734110fe83cd5b8d41d7f2f94f0dec7ecf1facc663babb8ed94e4bdf3592e37464970afa9be144d354e9b456873c6387a12a3eefd3e2feb66f7519ac72ac502c09d20d72cae9d04c88549a285c081023e1c1da08", "wx": "10b336b3afb80c80ff50716e734110fe83cd5b8d41d7f2f94f0dec7ecf1facc663babb8ed94e4bdf3592e37464970afa", "wy": "009be144d354e9b456873c6387a12a3eefd3e2feb66f7519ac72ac502c09d20d72cae9d04c88549a285c081023e1c1da08"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000410b336b3afb80c80ff50716e734110fe83cd5b8d41d7f2f94f0dec7ecf1facc663babb8ed94e4bdf3592e37464970afa9be144d354e9b456873c6387a12a3eefd3e2feb66f7519ac72ac502c09d20d72cae9d04c88549a285c081023e1c1da08", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEELM2s6+4DID/UHFuc0EQ/oPNW41B1/L5\nTw3sfs8frMZjuruO2U5L3zWS43Rklwr6m+FE01TptFaHPGOHoSo+79Pi/rZvdRms\ncqxQLAnSDXLK6dBMiFSaKFwIECPhwdoI\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3066023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7023100db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0481f92630778777a01781e7924fced35fc09018d9b00820881b14a814c1836a1f73c3641f7a17c821ffd95da902efe132221d81323509391f7b61bd796011337e6af36ae0798c17043d79e8efcdae8e724adf96a2309207c2d2cfd88e8c483acb", "wx": "0081f92630778777a01781e7924fced35fc09018d9b00820881b14a814c1836a1f73c3641f7a17c821ffd95da902efe132", "wy": "221d81323509391f7b61bd796011337e6af36ae0798c17043d79e8efcdae8e724adf96a2309207c2d2cfd88e8c483acb"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000481f92630778777a01781e7924fced35fc09018d9b00820881b14a814c1836a1f73c3641f7a17c821ffd95da902efe132221d81323509391f7b61bd796011337e6af36ae0798c17043d79e8efcdae8e724adf96a2309207c2d2cfd88e8c483acb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEgfkmMHeHd6AXgeeST87TX8CQGNmwCCCI\nGxSoFMGDah9zw2QfehfIIf/ZXakC7+EyIh2BMjUJOR97Yb15YBEzfmrzauB5jBcE\nPXno782ujnJK35aiMJIHwtLP2I6MSDrL\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k", "msg": "313233343030", "sig": "3065023100aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab702300eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3NhfeSpYmLG9dnpi/kpLcKfj0Hb0omhR8\n6doxE7XwuMAKYLHOHX6BnXpDHXyQ6g5f\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "testing point duplication", "msg": "313233343030", "sig": "3064023043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281757b30e19218a37cbd612086fbc158ca02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 422, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100bc07ff041506dc73a75086a43252fb43b6327af3c6b2cc7d322ff6d1d1162b5de29edcd0b69803fe2f8af8e3d103d0a902302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "00c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3yeghtWnZ05CiYWdAbW0j1gcL4kLXZeuD\nFiXO7EoPRz71n04w4oF+YoW84oRvFfGg\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "testing point duplication", "msg": "313233343030", "sig": "3064023043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281757b30e19218a37cbd612086fbc158ca02302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 424, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100bc07ff041506dc73a75086a43252fb43b6327af3c6b2cc7d322ff6d1d1162b5de29edcd0b69803fe2f8af8e3d103d0a902302492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "wx": "29bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc", "wy": "009a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKb23bV+nQb/XAjPLOmbMfUS+s7BmPZKo\nE2ZQR4vO+2HvGC4VWlQ0Wl6OXojwZOW8mlJat/dk2tPa4UaMK0GfO2K5upF9XoxP\nsexHQEo/x2R0snEwgb6dtMAOBDran8Sj\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "pseudorandom signature", "msg": "", "sig": "306402302290c886bbad8f53089583d543a269a727665626d6b94a3796324c62d08988f66f6011e845811a03589e92abe1f17faf023066e2cb4380997f4e7f85022541adb22d24d1196be68a3db888b03eb3d2d40b0d9a3a6a00a1a4782ee0a00e8410ba2d86", "result": "valid", "flags": []}, {"tcId": 426, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "30650231008071d8cf9df9efef696ebafc59f74db90c1f1ecf5ccde18858de22fe4d7df2a25cb3001695d706dfd7984b39df65a0f4023027291e6339c2a7fed7a174bb97ffe41d8cfdc20c1260c6ec85d7259f0cc7781bf2ae7a6e6fb4c08e0d75b7381bb7d9b8", "result": "valid", "flags": []}, {"tcId": 427, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "30650230470014ccd7a1a5e5333d301c8ea528ac3b07b01944af30cec60f4bad94db108509e45ba381818b5bdfaf9daf0d372301023100e3d49d6a05a755aa871d7cb96fffb79fed7625f83f69498ba07c0d65166a67107c9a17ae6e1028e244377a44096217b2", "result": "valid", "flags": []}, {"tcId": 428, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "30640230377044d343f900175ac6833071be74964cd636417039e10e837da94b6919bffc3f5a517b945a450852af3259f5cbf108023032ea25006375c153581e80c09f53ad585c736f823c70147aba4fb47bb0a224fae4d8819adad80d4c144ecc2380954a9e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "wx": "00ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aac", "wy": "00acbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE/////6pj8aI5rHAZfG6/zqV1bcASEj+C\nxR+odNZgKL4A6XahCAYGc3zHXEC9/kqsrL2FOJCIpipjmDhMIrUtSS8j9G5KJ6Ry\nStVVUdpcSDQ4CVokfLDDN48fUsNCX/nx\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3066023100ccb13c4dc9805a9b4e06ee25ef8c7593eaff7326c432d4b12b923163cf1cbe5fe1cfd3546c1d0761d8874e83ffd2e15d023100db1b0c082ae314b539f05e8a14ad51e5db37f29cacea9b2aab63a04917d58d008cf3f7ba41d5ea280f3b6a67be3ae8f8", "result": "valid", "flags": []}, {"tcId": 430, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3065023100c79a30e36d2126b348dd9eb2f5db6aa98f79d80214027e51bcf3cabec188a7ebaf25cb7bbe9ec6bfed135e2a3b70e9160230241338ee2ac931adea9a56e7bfe909947128d54d5122a47b00c278e684e10102740d26e89e343290a5b2fa8b401faec6", "result": "valid", "flags": []}, {"tcId": 431, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306402300df82e4ec2960e3df614f8b49cec9a4ee1054365414241361feec9d9d9b6909d8775f222ec385a14afab46266db390c302300968485e854addba0f8354e677e955e1ef2df973d564c49f65f2562cb2a2b80d75e92f8784042955f7b8765f609ce221", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "wx": "00d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422", "wy": "00c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0YJ/xvbxLyGZLFpAmgZTsSHS7wKysKsB\nqRYc6VYoB0Cx41ayVXAbCm3cnsLKipQixu1dLO2NirdWD6W7iMc450VBiD2KKxwO\nK6fjbQMPxNm/uLIvJNuJfrrEndQAAAAA\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 432, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402301fafd83d728422e1485f1e52e5b631548647cc3c76c109c3177a73751d91a19012fa4628b218f2229fc4d55f105fe00102304474f9af7b4b0bb96fdb05ae918f799024e8d5b864e49ccd047cf97e7b9f8763cce015c11cf1f461c9027cb901055101", "result": "valid", "flags": []}, {"tcId": 433, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3066023100e6025bb957ab197fb4c080d0a5c647e428afb0d7cc235c605ae97545494fd31a9979790bb2da6e1cf186789422b15c970231008ae9872291430d1bb371ef72360dad5afbb6fb001f403d9aaa1445f0326eb1eef775c9dfe1d7ef8bf4e744822108d27e", "result": "valid", "flags": []}, {"tcId": 434, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3066023100877d5567c18fa568259005a89c2300d1b3825b732fa14964c1477d4b3098afd09384b97d497464adba41e9df8a74d339023100c40f0760717b4b3bae75742b6dc3dcf04cc22a449cfea19d305e0658cb705fda75163e7399e0b3125ca7d1919c13851e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "wx": "1099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000", "wy": "00e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEJm7RRAPVfWoXMo94rO9XiUPT2+tZjGj\nFWwuUqM9fWFd0nn3n4tLr/fHE6wAAAAA5sm3NqiSny7Xvgx1OlTLtIuEaeBBHq+T\npKgkWboLaBu6j1+zg7SQbUkBozA+LxVX\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 435, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3065023100e706b0045a6f54bd175e2437b48767b0204f93d8a4d9d3d00838278137e5b670de4305c5c55e49059b8b5f6e264654c90230405741adff94afd9a88e08d0b1021911fa4cedb2466b1a8fd302a5b5d96566ada63ccb82b6c5e8452fde860c545e0a19", "result": "valid", "flags": []}, {"tcId": 436, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306502300c57ce2bc579fbd3a759dfbf5e84c3cef2414846a2e300453e1e4c5188f24432b14ca647a733b6ad35c980a880d36145023100f12a119e22d48b82049df611f1c851fb22795056498a873c730fcb9fd8f314728de0298b9b22c348abc6de2aba97e972", "result": "valid", "flags": []}, {"tcId": 437, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30660231009a8f80697ccf2e0617612027d861a3a3a657fb75cc82810b40dd5072d39ff37eca29008390da356137e2c9babd814198023100a86537a83c3d57da50e4b29b47dcc3717c5a1ed0fff18ade8dcce4220eac63aab60b9bfed5f1bdd241dab655a9bdd75f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "wx": "2b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69", "wy": "00d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAAAAACsInt11QWkBAUXyY/M0/BZ8wZ2u\ngiWXCuGcyMt+xzWT1qRlw3D1R4sOU51p0ZUdWXtWpnNFrLJYCVgfB80Ot42VOKP4\npl8wDmih63hQffdt5lDo+O5jpfDFaHyY\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 438, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306602310093718f6f8542725f62de7039fc193d3fcc81d622230ccc94e9e265390b385af3a3ba50c91a9d6a5b1e07d79af2bd80b2023100d08499f3d298e8afecea122265a36dbf337259020654739783c8ec8ef783d072555b5907285ce83fc8ced9c8398c6269", "result": "valid", "flags": []}, {"tcId": 439, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3066023100ce26e42c490dec92cf59d6b1ba75c9a1400d6e5c3fd7c47e1eeb1cded30a3a3d18c81cdfdcbad2742a97293369ce21c202310094671085d941fd27d495452a4c8559a1fe24f3225f5b8ef75faf9d3fb01372c586e23b82714359d0e47144ff5d946161", "result": "valid", "flags": []}, {"tcId": 440, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3066023100ffc4738acf71f04a13104c328c138b331fb7202aef66f583ba543ed490d12993c18f724c81ad0f7ea18dae352e5c6480023100e67d4ccdeb68a9a731f06f77eae00175be076d92529b109a62542692c8749ddfde03bed1c119a5901a4e852f2115578f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2AAAAACCLP1rTs5N6zJ1gbMXs7KtKcB91\n7UKVfqTXhY0z9cJsauIKnMzaVplnANa0\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 441, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3065023100e6fa8455bc14e730e4ca1eb5faf6c8180f2f231069b93a0bb17d33ad5513d93a36214f5ce82ca6bd785ccbacf7249a4c02303979b4b480f496357c25aa3fc850c67ff1c5a2aabd80b6020d2eac3dd7833cf2387d0be64df54a0e9b59f12c3bebf886", "result": "valid", "flags": []}, {"tcId": 442, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306502301b49b037783838867fbaa57305b2aa28df1b0ec40f43140067fafdea63f87c02dfb0e6f41b760fbdf51005e90c0c3715023100e7d4eb6ee61611264ea8a668a70287e3d63489273da2b30ad0c221f1893feaea3e878c9a81c6cec865899dbda4fa79ae", "result": "valid", "flags": []}, {"tcId": 443, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "306502310091d9da3d577408189dcaae33d95ed0a0118afd460d5228fa352b6ea671b172eb413816a70621ddaf23c5e2ef79df0c110230053dadbfcd564bddbe44e0ecb4d1e608dbd35d4e83b6634cc72afb87a2d61675ee13960c243f6be70519e167b1d3ceb0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "00ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2/////990wKUsTGyFM2KfkzoTE1S1j+CK\nEr1qgVsoenHMCj2SlR31YzMlqWeY/ylL\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaVerify", "tests": [{"tcId": 444, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3065023100af0ed6ce6419662db80f02a2b632675445c7bf8a34bbacdc81cc5dd306c657ca4c5a3fb1b05f358d8f36fda8ae238806023046b472c0badb17e089c8f9697fd0b4ce71f0f4471b235483d4c8dd3d00aa282cde990253df38ba733b2ad82a601c7508", "result": "valid", "flags": []}, {"tcId": 445, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3066023100e2aa9468ccaaadad8b9f43a429c97f0c6a7eedcb4d4af72d639df0fe53f610b953408a8e24e8db138551770750680f7a023100d81020846d1c50ee9ae23601dd638cb71b38d37fb555268c2fa1ad8a761fa7b27afcab2fa69224d1f976699914e09de2", "result": "valid", "flags": []}, {"tcId": 446, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306402306bf6fa7a663802c3382cc5fd02004ec71e5a031e3d9bfc0858fa994e88497a7782308bc265b8237a6bbbdd38658b36fc02303a9d5941a013bf70d99cc3ff255ce85573688dac40344b5db7144b19bf57bb2701e6850a8f819796b67f7d0b6aea7e50", "result": "valid", "flags": []}]}]}