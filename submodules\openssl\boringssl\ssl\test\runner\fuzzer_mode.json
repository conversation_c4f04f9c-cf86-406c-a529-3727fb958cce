{"DisabledTests": {"BadCBCPadding*": "Fuzzer mode has no CBC padding.", "BadFinished-*": "Fuzzer mode ignores Finished checks.", "FalseStart-BadFinished": "Fuzzer mode ignores Finished checks.", "TrailingMessageData-*Finished*": "Fuzzer mode ignores Finished checks.", "DTLSIgnoreBadPackets*": "Fuzzer mode has no bad packets.", "TLSFatalBadPackets": "Fuzzer mode has no bad packets.", "*-BadRecord": "Fuzzer mode has no bad packets.", "BadRSAClientKeyExchange*": "Fuzzer mode does not notice a bad premaster secret.", "TrailingMessageData-TLS13-ServerHello": "Fuzzer mode will not read the peer's alert as a MAC error", "UnexpectedUnencryptedExtension-Client-TLS13": "Fuzzer mode will not read the peer's alert as a MAC error", "UnknownUnencryptedExtension-Client-TLS13": "Fuzzer mode will not read the peer's alert as a MAC error", "WrongMessageType-TLS13-ServerHello": "Fuzzer mode will not read the peer's alert as a MAC error", "BadECDSA-*": "Fuzzer mode always accepts a signature.", "*-InvalidSignature-*": "Fuzzer mode always accepts a signature.", "*Auth-Verify-RSA-PKCS1-*-TLS13": "Fuzzer mode always accepts a signature.", "*Auth-Verify-ECDSA-SHA1-TLS13": "Fuzzer mode always accepts a signature.", "Verify-*Auth-SignatureType*": "Fuzzer mode always accepts a signature.", "ECDSACurveMismatch-Verify-TLS13": "Fuzzer mode always accepts a signature.", "InvalidChannelIDSignature-*": "Fuzzer mode always accepts a signature.", "Resume-Server-CipherNotPreferred*": "Fuzzer mode does not encrypt tickets.", "Resume-Server-DeclineBadCipher*": "Fuzzer mode does not encrypt tickets.", "Resume-Server-DeclineCrossVersion*": "Fuzzer mode does not encrypt tickets.", "TicketCallback-SingleCall-*": "Fuzzer mode does not encrypt tickets.", "CorruptTicket-*": "Fuzzer mode does not encrypt tickets.", "ShimTicketRewritable": "Fuzzer mode does not encrypt tickets.", "Resume-Server-*Binder*": "Fuzzer mode does not check binders.", "SkipEarlyData*": "Trial decryption does not work with the NULL cipher."}}