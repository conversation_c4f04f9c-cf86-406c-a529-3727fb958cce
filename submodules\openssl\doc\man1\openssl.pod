=pod

=head1 NAME

openssl - OpenSSL command line tool

=head1 SYNOPSIS

B<openssl>
I<command>
[ I<command_opts> ]
[ I<command_args> ]

B<openssl> B<list> [ B<standard-commands> | B<digest-commands> | B<cipher-commands> | B<cipher-algorithms> | B<digest-algorithms> | B<public-key-algorithms>]

B<openssl> B<no->I<XXX> [ I<arbitrary options> ]

=head1 DESCRIPTION

OpenSSL is a cryptography toolkit implementing the Secure Sockets Layer (SSL
v2/v3) and Transport Layer Security (TLS v1) network protocols and related
cryptography standards required by them.

The B<openssl> program is a command line tool for using the various
cryptography functions of OpenSSL's B<crypto> library from the shell.
It can be used for

 o  Creation and management of private keys, public keys and parameters
 o  Public key cryptographic operations
 o  Creation of X.509 certificates, CSRs and CRLs
 o  Calculation of Message Digests
 o  Encryption and Decryption with Ciphers
 o  SSL/TLS Client and Server Tests
 o  Handling of S/MIME signed or encrypted mail
 o  Time Stamp requests, generation and verification

=head1 COMMAND SUMMARY

The B<openssl> program provides a rich variety of commands (I<command> in the
SYNOPSIS above), each of which often has a wealth of options and arguments
(I<command_opts> and I<command_args> in the SYNOPSIS).

Detailed documentation and use cases for most standard subcommands are available
(e.g., L<x509(1)> or L<openssl-x509(1)>).

Many commands use an external configuration file for some or all of their
arguments and have a B<-config> option to specify that file.
The environment variable B<OPENSSL_CONF> can be used to specify
the location of the file.
If the environment variable is not specified, then the file is named
B<openssl.cnf> in the default certificate storage area, whose value
depends on the configuration flags specified when the OpenSSL
was built.

The list parameters B<standard-commands>, B<digest-commands>,
and B<cipher-commands> output a list (one entry per line) of the names
of all standard commands, message digest commands, or cipher commands,
respectively, that are available in the present B<openssl> utility.

The list parameters B<cipher-algorithms> and
B<digest-algorithms> list all cipher and message digest names, one entry per line. Aliases are listed as:

 from => to

The list parameter B<public-key-algorithms> lists all supported public
key algorithms.

The command B<no->I<XXX> tests whether a command of the
specified name is available.  If no command named I<XXX> exists, it
returns 0 (success) and prints B<no->I<XXX>; otherwise it returns 1
and prints I<XXX>.  In both cases, the output goes to B<stdout> and
nothing is printed to B<stderr>.  Additional command line arguments
are always ignored.  Since for each cipher there is a command of the
same name, this provides an easy way for shell scripts to test for the
availability of ciphers in the B<openssl> program.  (B<no->I<XXX> is
not able to detect pseudo-commands such as B<quit>,
B<list>, or B<no->I<XXX> itself.)

=head2 Standard Commands

=over 4

=item B<asn1parse>

Parse an ASN.1 sequence.

=item B<ca>

Certificate Authority (CA) Management.

=item B<ciphers>

Cipher Suite Description Determination.

=item B<cms>

CMS (Cryptographic Message Syntax) utility.

=item B<crl>

Certificate Revocation List (CRL) Management.

=item B<crl2pkcs7>

CRL to PKCS#7 Conversion.

=item B<dgst>

Message Digest Calculation.

=item B<dh>

Diffie-Hellman Parameter Management.
Obsoleted by L<dhparam(1)>.

=item B<dhparam>

Generation and Management of Diffie-Hellman Parameters. Superseded by
L<genpkey(1)> and L<pkeyparam(1)>.

=item B<dsa>

DSA Data Management.

=item B<dsaparam>

DSA Parameter Generation and Management. Superseded by
L<genpkey(1)> and L<pkeyparam(1)>.

=item B<ec>

EC (Elliptic curve) key processing.

=item B<ecparam>

EC parameter manipulation and generation.

=item B<enc>

Encoding with Ciphers.

=item B<engine>

Engine (loadable module) information and manipulation.

=item B<errstr>

Error Number to Error String Conversion.

=item B<gendh>

Generation of Diffie-Hellman Parameters.
Obsoleted by L<dhparam(1)>.

=item B<gendsa>

Generation of DSA Private Key from Parameters. Superseded by
L<genpkey(1)> and L<pkey(1)>.

=item B<genpkey>

Generation of Private Key or Parameters.

=item B<genrsa>

Generation of RSA Private Key. Superseded by L<genpkey(1)>.

=item B<nseq>

Create or examine a Netscape certificate sequence.

=item B<ocsp>

Online Certificate Status Protocol utility.

=item B<passwd>

Generation of hashed passwords.

=item B<pkcs12>

PKCS#12 Data Management.

=item B<pkcs7>

PKCS#7 Data Management.

=item B<pkcs8>

PKCS#8 format private key conversion tool.

=item B<pkey>

Public and private key management.

=item B<pkeyparam>

Public key algorithm parameter management.

=item B<pkeyutl>

Public key algorithm cryptographic operation utility.

=item B<prime>

Compute prime numbers.

=item B<rand>

Generate pseudo-random bytes.

=item B<rehash>

Create symbolic links to certificate and CRL files named by the hash values.

=item B<req>

PKCS#10 X.509 Certificate Signing Request (CSR) Management.

=item B<rsa>

RSA key management.

=item B<rsautl>

RSA utility for signing, verification, encryption, and decryption. Superseded
by  L<pkeyutl(1)>.

=item B<s_client>

This implements a generic SSL/TLS client which can establish a transparent
connection to a remote server speaking SSL/TLS. It's intended for testing
purposes only and provides only rudimentary interface functionality but
internally uses mostly all functionality of the OpenSSL B<ssl> library.

=item B<s_server>

This implements a generic SSL/TLS server which accepts connections from remote
clients speaking SSL/TLS. It's intended for testing purposes only and provides
only rudimentary interface functionality but internally uses mostly all
functionality of the OpenSSL B<ssl> library.  It provides both an own command
line oriented protocol for testing SSL functions and a simple HTTP response
facility to emulate an SSL/TLS-aware webserver.

=item B<s_time>

SSL Connection Timer.

=item B<sess_id>

SSL Session Data Management.

=item B<smime>

S/MIME mail processing.

=item B<speed>

Algorithm Speed Measurement.

=item B<spkac>

SPKAC printing and generating utility.

=item B<srp>

Maintain SRP password file.

=item B<storeutl>

Utility to list and display certificates, keys, CRLs, etc.

=item B<ts>

Time Stamping Authority tool (client/server).

=item B<verify>

X.509 Certificate Verification.

=item B<version>

OpenSSL Version Information.

=item B<x509>

X.509 Certificate Data Management.

=back

=head2 Message Digest Commands

=over 4

=item B<blake2b512>

BLAKE2b-512 Digest

=item B<blake2s256>

BLAKE2s-256 Digest

=item B<md2>

MD2 Digest

=item B<md4>

MD4 Digest

=item B<md5>

MD5 Digest

=item B<mdc2>

MDC2 Digest

=item B<rmd160>

RMD-160 Digest

=item B<sha1>

SHA-1 Digest

=item B<sha224>

SHA-2 224 Digest

=item B<sha256>

SHA-2 256 Digest

=item B<sha384>

SHA-2 384 Digest

=item B<sha512>

SHA-2 512 Digest

=item B<sha3-224>

SHA-3 224 Digest

=item B<sha3-256>

SHA-3 256 Digest

=item B<sha3-384>

SHA-3 384 Digest

=item B<sha3-512>

SHA-3 512 Digest

=item B<shake128>

SHA-3 SHAKE128 Digest

=item B<shake256>

SHA-3 SHAKE256 Digest

=item B<sm3>

SM3 Digest

=back

=head2 Encoding and Cipher Commands

The following aliases provide convenient access to the most used encodings
and ciphers.

Depending on how OpenSSL was configured and built, not all ciphers listed
here may be present. See L<enc(1)> for more information and command usage.

=over 4

=item B<aes128>, B<aes-128-cbc>, B<aes-128-cfb>, B<aes-128-ctr>, B<aes-128-ecb>, B<aes-128-ofb>

AES-128 Cipher

=item B<aes192>, B<aes-192-cbc>, B<aes-192-cfb>, B<aes-192-ctr>, B<aes-192-ecb>, B<aes-192-ofb>

AES-192 Cipher

=item B<aes256>, B<aes-256-cbc>, B<aes-256-cfb>, B<aes-256-ctr>, B<aes-256-ecb>, B<aes-256-ofb>

AES-256 Cipher

=item B<aria128>, B<aria-128-cbc>, B<aria-128-cfb>, B<aria-128-ctr>, B<aria-128-ecb>, B<aria-128-ofb>

Aria-128 Cipher

=item B<aria192>, B<aria-192-cbc>, B<aria-192-cfb>, B<aria-192-ctr>, B<aria-192-ecb>, B<aria-192-ofb>

Aria-192 Cipher

=item B<aria256>, B<aria-256-cbc>, B<aria-256-cfb>, B<aria-256-ctr>, B<aria-256-ecb>, B<aria-256-ofb>

Aria-256 Cipher

=item B<base64>

Base64 Encoding

=item B<bf>, B<bf-cbc>, B<bf-cfb>, B<bf-ecb>, B<bf-ofb>

Blowfish Cipher

=item B<camellia128>, B<camellia-128-cbc>, B<camellia-128-cfb>, B<camellia-128-ctr>, B<camellia-128-ecb>, B<camellia-128-ofb>

Camellia-128 Cipher

=item B<camellia192>, B<camellia-192-cbc>, B<camellia-192-cfb>, B<camellia-192-ctr>, B<camellia-192-ecb>, B<camellia-192-ofb>

Camellia-192 Cipher

=item B<camellia256>, B<camellia-256-cbc>, B<camellia-256-cfb>, B<camellia-256-ctr>, B<camellia-256-ecb>, B<camellia-256-ofb>

Camellia-256 Cipher

=item B<cast>, B<cast-cbc>

CAST Cipher

=item B<cast5-cbc>, B<cast5-cfb>, B<cast5-ecb>, B<cast5-ofb>

CAST5 Cipher

=item B<chacha20>

Chacha20 Cipher

=item B<des>, B<des-cbc>, B<des-cfb>, B<des-ecb>, B<des-ede>, B<des-ede-cbc>, B<des-ede-cfb>, B<des-ede-ofb>, B<des-ofb>

DES Cipher

=item B<des3>, B<desx>, B<des-ede3>, B<des-ede3-cbc>, B<des-ede3-cfb>, B<des-ede3-ofb>

Triple-DES Cipher

=item B<idea>, B<idea-cbc>, B<idea-cfb>, B<idea-ecb>, B<idea-ofb>

IDEA Cipher

=item B<rc2>, B<rc2-cbc>, B<rc2-cfb>, B<rc2-ecb>, B<rc2-ofb>

RC2 Cipher

=item B<rc4>

RC4 Cipher

=item B<rc5>, B<rc5-cbc>, B<rc5-cfb>, B<rc5-ecb>, B<rc5-ofb>

RC5 Cipher

=item B<seed>, B<seed-cbc>, B<seed-cfb>, B<seed-ecb>, B<seed-ofb>

SEED Cipher

=item B<sm4>, B<sm4-cbc>, B<sm4-cfb>, B<sm4-ctr>, B<sm4-ecb>, B<sm4-ofb>

SM4 Cipher

=back

=head1 OPTIONS

Details of which options are available depend on the specific command.
This section describes some common options with common behavior.

=head2 Common Options

=over 4

=item B<-help>

Provides a terse summary of all options.

=back

=head2 Pass Phrase Options

Several commands accept password arguments, typically using B<-passin>
and B<-passout> for input and output passwords respectively. These allow
the password to be obtained from a variety of sources. Both of these
options take a single argument whose format is described below. If no
password argument is given and a password is required then the user is
prompted to enter one: this will typically be read from the current
terminal with echoing turned off.

Note that character encoding may be relevant, please see
L<passphrase-encoding(7)>.

=over 4

=item B<pass:password>

The actual password is B<password>. Since the password is visible
to utilities (like 'ps' under Unix) this form should only be used
where security is not important.

=item B<env:var>

Obtain the password from the environment variable B<var>. Since
the environment of other processes is visible on certain platforms
(e.g. ps under certain Unix OSes) this option should be used with caution.

=item B<file:pathname>

The first line of B<pathname> is the password. If the same B<pathname>
argument is supplied to B<-passin> and B<-passout> arguments then the first
line will be used for the input password and the next line for the output
password. B<pathname> need not refer to a regular file: it could for example
refer to a device or named pipe.

=item B<fd:number>

Read the password from the file descriptor B<number>. This can be used to
send the data via a pipe for example.

=item B<stdin>

Read the password from standard input.

=back

=head1 SEE ALSO

L<asn1parse(1)>, L<ca(1)>, L<ciphers(1)>, L<cms(1)>, L<config(5)>,
L<crl(1)>, L<crl2pkcs7(1)>, L<dgst(1)>,
L<dhparam(1)>, L<dsa(1)>, L<dsaparam(1)>,
L<ec(1)>, L<ecparam(1)>,
L<enc(1)>, L<engine(1)>, L<errstr(1)>, L<gendsa(1)>, L<genpkey(1)>,
L<genrsa(1)>, L<nseq(1)>, L<ocsp(1)>,
L<passwd(1)>,
L<pkcs12(1)>, L<pkcs7(1)>, L<pkcs8(1)>,
L<pkey(1)>, L<pkeyparam(1)>, L<pkeyutl(1)>, L<prime(1)>,
L<rand(1)>, L<rehash(1)>, L<req(1)>, L<rsa(1)>,
L<rsautl(1)>, L<s_client(1)>,
L<s_server(1)>, L<s_time(1)>, L<sess_id(1)>,
L<smime(1)>, L<speed(1)>, L<spkac(1)>, L<srp(1)>, L<storeutl(1)>,
L<ts(1)>,
L<verify(1)>, L<version(1)>, L<x509(1)>,
L<crypto(7)>, L<ssl(7)>, L<x509v3_config(5)>

=head1 HISTORY

The B<list->I<XXX>B<-algorithms> pseudo-commands were added in OpenSSL 1.0.0;
For notes on the availability of other commands, see their individual
manual pages.

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
