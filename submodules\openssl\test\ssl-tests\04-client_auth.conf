# Generated with generate_ssl_tests.pl

num_tests = 36

test-0 = 0-server-auth-flex
test-1 = 1-client-auth-flex-request
test-2 = 2-client-auth-flex-require-fail
test-3 = 3-client-auth-flex-require
test-4 = 4-client-auth-flex-require-non-empty-names
test-5 = 5-client-auth-flex-noroot
test-6 = 6-server-auth-TLSv1
test-7 = 7-client-auth-TLSv1-request
test-8 = 8-client-auth-TLSv1-require-fail
test-9 = 9-client-auth-TLSv1-require
test-10 = 10-client-auth-TLSv1-require-non-empty-names
test-11 = 11-client-auth-TLSv1-noroot
test-12 = 12-server-auth-TLSv1.1
test-13 = 13-client-auth-TLSv1.1-request
test-14 = 14-client-auth-TLSv1.1-require-fail
test-15 = 15-client-auth-TLSv1.1-require
test-16 = 16-client-auth-TLSv1.1-require-non-empty-names
test-17 = 17-client-auth-TLSv1.1-noroot
test-18 = 18-server-auth-TLSv1.2
test-19 = 19-client-auth-TLSv1.2-request
test-20 = 20-client-auth-TLSv1.2-require-fail
test-21 = 21-client-auth-TLSv1.2-require
test-22 = 22-client-auth-TLSv1.2-require-non-empty-names
test-23 = 23-client-auth-TLSv1.2-noroot
test-24 = 24-server-auth-DTLSv1
test-25 = 25-client-auth-DTLSv1-request
test-26 = 26-client-auth-DTLSv1-require-fail
test-27 = 27-client-auth-DTLSv1-require
test-28 = 28-client-auth-DTLSv1-require-non-empty-names
test-29 = 29-client-auth-DTLSv1-noroot
test-30 = 30-server-auth-DTLSv1.2
test-31 = 31-client-auth-DTLSv1.2-request
test-32 = 32-client-auth-DTLSv1.2-require-fail
test-33 = 33-client-auth-DTLSv1.2-require
test-34 = 34-client-auth-DTLSv1.2-require-non-empty-names
test-35 = 35-client-auth-DTLSv1.2-noroot
# ===========================================================

[0-server-auth-flex]
ssl_conf = 0-server-auth-flex-ssl

[0-server-auth-flex-ssl]
server = 0-server-auth-flex-server
client = 0-server-auth-flex-client

[0-server-auth-flex-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-server-auth-flex-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-client-auth-flex-request]
ssl_conf = 1-client-auth-flex-request-ssl

[1-client-auth-flex-request-ssl]
server = 1-client-auth-flex-request-server
client = 1-client-auth-flex-request-client

[1-client-auth-flex-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[1-client-auth-flex-request-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


# ===========================================================

[2-client-auth-flex-require-fail]
ssl_conf = 2-client-auth-flex-require-fail-ssl

[2-client-auth-flex-require-fail-ssl]
server = 2-client-auth-flex-require-fail-server
client = 2-client-auth-flex-require-fail-client

[2-client-auth-flex-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[2-client-auth-flex-require-fail-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateRequired


# ===========================================================

[3-client-auth-flex-require]
ssl_conf = 3-client-auth-flex-require-ssl

[3-client-auth-flex-require-ssl]
server = 3-client-auth-flex-require-server
client = 3-client-auth-flex-require-client

[3-client-auth-flex-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[3-client-auth-flex-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[4-client-auth-flex-require-non-empty-names]
ssl_conf = 4-client-auth-flex-require-non-empty-names-ssl

[4-client-auth-flex-require-non-empty-names-ssl]
server = 4-client-auth-flex-require-non-empty-names-server
client = 4-client-auth-flex-require-non-empty-names-client

[4-client-auth-flex-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[4-client-auth-flex-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[5-client-auth-flex-noroot]
ssl_conf = 5-client-auth-flex-noroot-ssl

[5-client-auth-flex-noroot-ssl]
server = 5-client-auth-flex-noroot-server
client = 5-client-auth-flex-noroot-client

[5-client-auth-flex-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[5-client-auth-flex-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[6-server-auth-TLSv1]
ssl_conf = 6-server-auth-TLSv1-ssl

[6-server-auth-TLSv1-ssl]
server = 6-server-auth-TLSv1-server
client = 6-server-auth-TLSv1-client

[6-server-auth-TLSv1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-server-auth-TLSv1-client]
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success


# ===========================================================

[7-client-auth-TLSv1-request]
ssl_conf = 7-client-auth-TLSv1-request-ssl

[7-client-auth-TLSv1-request-ssl]
server = 7-client-auth-TLSv1-request-server
client = 7-client-auth-TLSv1-request-client

[7-client-auth-TLSv1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[7-client-auth-TLSv1-request-client]
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success


# ===========================================================

[8-client-auth-TLSv1-require-fail]
ssl_conf = 8-client-auth-TLSv1-require-fail-ssl

[8-client-auth-TLSv1-require-fail-ssl]
server = 8-client-auth-TLSv1-require-fail-server
client = 8-client-auth-TLSv1-require-fail-client

[8-client-auth-TLSv1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[8-client-auth-TLSv1-require-fail-client]
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[9-client-auth-TLSv1-require]
ssl_conf = 9-client-auth-TLSv1-require-ssl

[9-client-auth-TLSv1-require-ssl]
server = 9-client-auth-TLSv1-require-server
client = 9-client-auth-TLSv1-require-client

[9-client-auth-TLSv1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[9-client-auth-TLSv1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[10-client-auth-TLSv1-require-non-empty-names]
ssl_conf = 10-client-auth-TLSv1-require-non-empty-names-ssl

[10-client-auth-TLSv1-require-non-empty-names-ssl]
server = 10-client-auth-TLSv1-require-non-empty-names-server
client = 10-client-auth-TLSv1-require-non-empty-names-client

[10-client-auth-TLSv1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[10-client-auth-TLSv1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[11-client-auth-TLSv1-noroot]
ssl_conf = 11-client-auth-TLSv1-noroot-ssl

[11-client-auth-TLSv1-noroot-ssl]
server = 11-client-auth-TLSv1-noroot-server
client = 11-client-auth-TLSv1-noroot-client

[11-client-auth-TLSv1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[11-client-auth-TLSv1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[12-server-auth-TLSv1.1]
ssl_conf = 12-server-auth-TLSv1.1-ssl

[12-server-auth-TLSv1.1-ssl]
server = 12-server-auth-TLSv1.1-server
client = 12-server-auth-TLSv1.1-client

[12-server-auth-TLSv1.1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-server-auth-TLSv1.1-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = Success


# ===========================================================

[13-client-auth-TLSv1.1-request]
ssl_conf = 13-client-auth-TLSv1.1-request-ssl

[13-client-auth-TLSv1.1-request-ssl]
server = 13-client-auth-TLSv1.1-request-server
client = 13-client-auth-TLSv1.1-request-client

[13-client-auth-TLSv1.1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[13-client-auth-TLSv1.1-request-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = Success


# ===========================================================

[14-client-auth-TLSv1.1-require-fail]
ssl_conf = 14-client-auth-TLSv1.1-require-fail-ssl

[14-client-auth-TLSv1.1-require-fail-ssl]
server = 14-client-auth-TLSv1.1-require-fail-server
client = 14-client-auth-TLSv1.1-require-fail-client

[14-client-auth-TLSv1.1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[14-client-auth-TLSv1.1-require-fail-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[15-client-auth-TLSv1.1-require]
ssl_conf = 15-client-auth-TLSv1.1-require-ssl

[15-client-auth-TLSv1.1-require-ssl]
server = 15-client-auth-TLSv1.1-require-server
client = 15-client-auth-TLSv1.1-require-client

[15-client-auth-TLSv1.1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[15-client-auth-TLSv1.1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[16-client-auth-TLSv1.1-require-non-empty-names]
ssl_conf = 16-client-auth-TLSv1.1-require-non-empty-names-ssl

[16-client-auth-TLSv1.1-require-non-empty-names-ssl]
server = 16-client-auth-TLSv1.1-require-non-empty-names-server
client = 16-client-auth-TLSv1.1-require-non-empty-names-client

[16-client-auth-TLSv1.1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[16-client-auth-TLSv1.1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success


# ===========================================================

[17-client-auth-TLSv1.1-noroot]
ssl_conf = 17-client-auth-TLSv1.1-noroot-ssl

[17-client-auth-TLSv1.1-noroot-ssl]
server = 17-client-auth-TLSv1.1-noroot-server
client = 17-client-auth-TLSv1.1-noroot-client

[17-client-auth-TLSv1.1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[17-client-auth-TLSv1.1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[18-server-auth-TLSv1.2]
ssl_conf = 18-server-auth-TLSv1.2-ssl

[18-server-auth-TLSv1.2-ssl]
server = 18-server-auth-TLSv1.2-server
client = 18-server-auth-TLSv1.2-client

[18-server-auth-TLSv1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-server-auth-TLSv1.2-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedResult = Success


# ===========================================================

[19-client-auth-TLSv1.2-request]
ssl_conf = 19-client-auth-TLSv1.2-request-ssl

[19-client-auth-TLSv1.2-request-ssl]
server = 19-client-auth-TLSv1.2-request-server
client = 19-client-auth-TLSv1.2-request-client

[19-client-auth-TLSv1.2-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[19-client-auth-TLSv1.2-request-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedResult = Success


# ===========================================================

[20-client-auth-TLSv1.2-require-fail]
ssl_conf = 20-client-auth-TLSv1.2-require-fail-ssl

[20-client-auth-TLSv1.2-require-fail-ssl]
server = 20-client-auth-TLSv1.2-require-fail-server
client = 20-client-auth-TLSv1.2-require-fail-client

[20-client-auth-TLSv1.2-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[20-client-auth-TLSv1.2-require-fail-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure


# ===========================================================

[21-client-auth-TLSv1.2-require]
ssl_conf = 21-client-auth-TLSv1.2-require-ssl

[21-client-auth-TLSv1.2-require-ssl]
server = 21-client-auth-TLSv1.2-require-server
client = 21-client-auth-TLSv1.2-require-client

[21-client-auth-TLSv1.2-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = SHA256+RSA
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[21-client-auth-TLSv1.2-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA
ExpectedResult = Success


# ===========================================================

[22-client-auth-TLSv1.2-require-non-empty-names]
ssl_conf = 22-client-auth-TLSv1.2-require-non-empty-names-ssl

[22-client-auth-TLSv1.2-require-non-empty-names-ssl]
server = 22-client-auth-TLSv1.2-require-non-empty-names-server
client = 22-client-auth-TLSv1.2-require-non-empty-names-client

[22-client-auth-TLSv1.2-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ClientSignatureAlgorithms = SHA256+RSA
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[22-client-auth-TLSv1.2-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA
ExpectedResult = Success


# ===========================================================

[23-client-auth-TLSv1.2-noroot]
ssl_conf = 23-client-auth-TLSv1.2-noroot-ssl

[23-client-auth-TLSv1.2-noroot-ssl]
server = 23-client-auth-TLSv1.2-noroot-server
client = 23-client-auth-TLSv1.2-noroot-client

[23-client-auth-TLSv1.2-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[23-client-auth-TLSv1.2-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA


# ===========================================================

[24-server-auth-DTLSv1]
ssl_conf = 24-server-auth-DTLSv1-ssl

[24-server-auth-DTLSv1-ssl]
server = 24-server-auth-DTLSv1-server
client = 24-server-auth-DTLSv1-client

[24-server-auth-DTLSv1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-server-auth-DTLSv1-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[25-client-auth-DTLSv1-request]
ssl_conf = 25-client-auth-DTLSv1-request-ssl

[25-client-auth-DTLSv1-request-ssl]
server = 25-client-auth-DTLSv1-request-server
client = 25-client-auth-DTLSv1-request-client

[25-client-auth-DTLSv1-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[25-client-auth-DTLSv1-request-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[26-client-auth-DTLSv1-require-fail]
ssl_conf = 26-client-auth-DTLSv1-require-fail-ssl

[26-client-auth-DTLSv1-require-fail-ssl]
server = 26-client-auth-DTLSv1-require-fail-server
client = 26-client-auth-DTLSv1-require-fail-client

[26-client-auth-DTLSv1-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[26-client-auth-DTLSv1-require-fail-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = DTLS


# ===========================================================

[27-client-auth-DTLSv1-require]
ssl_conf = 27-client-auth-DTLSv1-require-ssl

[27-client-auth-DTLSv1-require-ssl]
server = 27-client-auth-DTLSv1-require-server
client = 27-client-auth-DTLSv1-require-client

[27-client-auth-DTLSv1-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[27-client-auth-DTLSv1-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[28-client-auth-DTLSv1-require-non-empty-names]
ssl_conf = 28-client-auth-DTLSv1-require-non-empty-names-ssl

[28-client-auth-DTLSv1-require-non-empty-names-ssl]
server = 28-client-auth-DTLSv1-require-non-empty-names-server
client = 28-client-auth-DTLSv1-require-non-empty-names-client

[28-client-auth-DTLSv1-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[28-client-auth-DTLSv1-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[29-client-auth-DTLSv1-noroot]
ssl_conf = 29-client-auth-DTLSv1-noroot-ssl

[29-client-auth-DTLSv1-noroot-ssl]
server = 29-client-auth-DTLSv1-noroot-server
client = 29-client-auth-DTLSv1-noroot-client

[29-client-auth-DTLSv1-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[29-client-auth-DTLSv1-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = DTLS


# ===========================================================

[30-server-auth-DTLSv1.2]
ssl_conf = 30-server-auth-DTLSv1.2-ssl

[30-server-auth-DTLSv1.2-ssl]
server = 30-server-auth-DTLSv1.2-server
client = 30-server-auth-DTLSv1.2-client

[30-server-auth-DTLSv1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[30-server-auth-DTLSv1.2-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[31-client-auth-DTLSv1.2-request]
ssl_conf = 31-client-auth-DTLSv1.2-request-ssl

[31-client-auth-DTLSv1.2-request-ssl]
server = 31-client-auth-DTLSv1.2-request-server
client = 31-client-auth-DTLSv1.2-request-client

[31-client-auth-DTLSv1.2-request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Request

[31-client-auth-DTLSv1.2-request-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedResult = Success
Method = DTLS


# ===========================================================

[32-client-auth-DTLSv1.2-require-fail]
ssl_conf = 32-client-auth-DTLSv1.2-require-fail-ssl

[32-client-auth-DTLSv1.2-require-fail-ssl]
server = 32-client-auth-DTLSv1.2-require-fail-server
client = 32-client-auth-DTLSv1.2-require-fail-client

[32-client-auth-DTLSv1.2-require-fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[32-client-auth-DTLSv1.2-require-fail-client]
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = DTLS


# ===========================================================

[33-client-auth-DTLSv1.2-require]
ssl_conf = 33-client-auth-DTLSv1.2-require-ssl

[33-client-auth-DTLSv1.2-require-ssl]
server = 33-client-auth-DTLSv1.2-require-server
client = 33-client-auth-DTLSv1.2-require-client

[33-client-auth-DTLSv1.2-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[33-client-auth-DTLSv1.2-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[34-client-auth-DTLSv1.2-require-non-empty-names]
ssl_conf = 34-client-auth-DTLSv1.2-require-non-empty-names-ssl

[34-client-auth-DTLSv1.2-require-non-empty-names-ssl]
server = 34-client-auth-DTLSv1.2-require-non-empty-names-server
client = 34-client-auth-DTLSv1.2-require-non-empty-names-client

[34-client-auth-DTLSv1.2-require-non-empty-names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[34-client-auth-DTLSv1.2-require-non-empty-names-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedResult = Success
Method = DTLS


# ===========================================================

[35-client-auth-DTLSv1.2-noroot]
ssl_conf = 35-client-auth-DTLSv1.2-noroot-ssl

[35-client-auth-DTLSv1.2-noroot-ssl]
server = 35-client-auth-DTLSv1.2-noroot-server
client = 35-client-auth-DTLSv1.2-noroot-client

[35-client-auth-DTLSv1.2-noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyMode = Require

[35-client-auth-DTLSv1.2-noroot-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = DTLS


