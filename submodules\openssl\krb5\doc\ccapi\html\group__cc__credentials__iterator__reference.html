<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_iterator_t</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_iterator_t</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_credentials_iterator_t type represents an iterator that iterates over a set of credentials. A new instance of this type can be obtained by calling <a class="el" href="group__helper__macros.html#g893b31c419e71c2f528781d3036fa3ff">cc_ccache_new_credentials_iterator()</a>.<p>
For API function documentation see <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a> <a class="el" href="group__cc__credentials__iterator__reference.html#g220581901999fe870ab65046e56cd1d6">cc_credentials_iterator_f</a>
<li>typedef <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a> <a class="el" href="group__cc__credentials__iterator__reference.html#ga1ad98d009f803bb13c04331bb47aee8">cc_credentials_iterator_d</a>
<li>typedef <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a> * <a class="el" href="group__cc__credentials__iterator__reference.html#g0d07a146ead685954032d0d7a6af7d4a">cc_credentials_iterator_t</a>
</ul>
<h2>Variables</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="group__cc__credentials__iterator__reference.html#g7d765e583b5994785e214df663e8959c">cc_credentials_iterator_f::clone</a> )(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> in_credentials_iterator, <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> *out_credentials_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g59a9f96a6c00b64c0ab971f7e9b5aae2">cc_credentials_iterator_clone()</a></b>: Make a copy of a credentials iterator.  <a href="#g7d765e583b5994785e214df663e8959c"></a><br></dl></ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g220581901999fe870ab65046e56cd1d6"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_f" ref="g220581901999fe870ab65046e56cd1d6" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a> <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="ga1ad98d009f803bb13c04331bb47aee8"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_d" ref="ga1ad98d009f803bb13c04331bb47aee8" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a> <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g0d07a146ead685954032d0d7a6af7d4a"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_t" ref="g0d07a146ead685954032d0d7a6af7d4a" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_d</a>* <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr><h2>Variable Documentation</h2>
<a class="anchor" name="g7d765e583b5994785e214df663e8959c"></a><!-- doxytag: member="cc_credentials_iterator_f::clone" ref="g7d765e583b5994785e214df663e8959c" args=")(cc_credentials_iterator_t in_credentials_iterator, cc_credentials_iterator_t *out_credentials_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* clone)(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> in_credentials_iterator, <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> *out_credentials_iterator)<code> [inherited]</code>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g59a9f96a6c00b64c0ab971f7e9b5aae2">cc_credentials_iterator_clone()</a></b>: Make a copy of a credentials iterator. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_iterator</em>&nbsp;</td><td>a credentials iterator object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_credentials_iterator</em>&nbsp;</td><td>on exit, a copy of <em>in_credentials_iterator</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
