mydir=lib$(S)crypto$(S)builtin$(S)enc_provider
BUILDTOP=$(REL)..$(S)..$(S)..$(S)..
LOCALINCLUDES = -I$(srcdir)/../des 	\
		-I$(srcdir)/../aes 	\
		-I$(srcdir)/../camellia \
		-I$(srcdir)/../../krb 	\
		-I$(srcdir)/..

##DOS##BUILDTOP = ..\..\..\..
##DOS##PREFIXDIR = builtin\enc_provider
##DOS##OBJFILE = ..\..\$(OUTPRE)enc_provider.lst

STLIBOBJS= \
	des3.o 	\
	rc4.o 	\
	aes.o   \
	camellia.o

OBJS= \
	$(OUTPRE)des3.$(OBJEXT) 	\
	$(OUTPRE)aes.$(OBJEXT) 	\
	$(OUTPRE)camellia.$(OBJEXT)	\
	$(OUTPRE)rc4.$(OBJEXT)

SRCS= \
	$(srcdir)/des3.c 	\
	$(srcdir)/aes.c 	\
	$(srcdir)/camellia.c	\
	$(srcdir)/rc4.c

##DOS##LIBOBJS = $(OBJS)

all-unix: all-libobjs

includes: depend

depend: $(SRCS)

clean-unix:: clean-libobjs

@libobj_frag@

