=pod

=head1 NAME

X509_get_subject_name, X509_set_subject_name, X509_get_issuer_name,
X509_set_issuer_name, X509_REQ_get_subject_name, X509_REQ_set_subject_name,
X509_CRL_get_issuer, X509_CRL_set_issuer_name - get and set issuer or
subject names

=head1 SYNOPSIS

 #include <openssl/x509.h>

 X509_NAME *X509_get_subject_name(const X509 *x);
 int X509_set_subject_name(X509 *x, X509_NAME *name);

 X509_NAME *X509_get_issuer_name(const X509 *x);
 int X509_set_issuer_name(X509 *x, X509_NAME *name);

 X509_NAME *X509_REQ_get_subject_name(const X509_REQ *req);
 int X509_REQ_set_subject_name(X509_REQ *req, X509_NAME *name);

 X509_NAME *X509_CRL_get_issuer(const X509_CRL *crl);
 int X509_CRL_set_issuer_name(X509_CRL *x, X509_NAME *name);

=head1 DESCRIPTION

X509_get_subject_name() returns the subject name of certificate B<x>. The
returned value is an internal pointer which B<MUST NOT> be freed.

X509_set_subject_name() sets the issuer name of certificate B<x> to
B<name>. The B<name> parameter is copied internally and should be freed
up when it is no longer needed.

X509_get_issuer_name() and X509_set_issuer_name() are identical to
X509_get_subject_name() and X509_set_subject_name() except the get and
set the issuer name of B<x>.

Similarly X509_REQ_get_subject_name(), X509_REQ_set_subject_name(),
X509_CRL_get_issuer() and X509_CRL_set_issuer_name() get or set the subject
or issuer names of certificate requests of CRLs respectively.

=head1 RETURN VALUES

X509_get_subject_name(), X509_get_issuer_name(), X509_REQ_get_subject_name()
and X509_CRL_get_issuer() return an B<X509_NAME> pointer.

X509_set_subject_name(), X509_set_issuer_name(), X509_REQ_set_subject_name()
and X509_CRL_set_issuer_name() return 1 for success and 0 for failure.

=head1 SEE ALSO

L<d2i_X509(3)>,
L<ERR_get_error(3)>, L<d2i_X509(3)>
L<X509_CRL_get0_by_serial(3)>,
L<X509_get0_signature(3)>,
L<X509_get_ext_d2i(3)>,
L<X509_get_extension_flags(3)>,
L<X509_get_pubkey(3)>,
L<X509_NAME_add_entry_by_txt(3)>,
L<X509_NAME_ENTRY_get_object(3)>,
L<X509_NAME_get_index_by_NID(3)>,
L<X509_NAME_print_ex(3)>,
L<X509_new(3)>,
L<X509_sign(3)>,
L<X509V3_get_d2i(3)>,
L<X509_verify_cert(3)>

=head1 HISTORY

X509_REQ_get_subject_name() is a function in OpenSSL 1.1.0 and a macro in
earlier versions.

X509_CRL_get_issuer() is a function in OpenSSL 1.1.0. It was previously
added in OpenSSL 1.0.0 as a macro.

=head1 COPYRIGHT

Copyright 2015-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
