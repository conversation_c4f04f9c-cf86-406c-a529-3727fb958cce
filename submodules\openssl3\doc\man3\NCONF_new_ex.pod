=pod

=head1 NAME

NCONF_new_ex, NCONF_new, NCONF_free, NCONF_default, NCONF_load,
NCONF_get0_libctx, NCONF_get_section, NCONF_get_section_names
- functionality to Load and parse configuration files manually

=head1 SYNOPSIS

 #include <openssl/conf.h>

 typedef struct {
     char *section;
     char *name;
     char *value;
 } CONF_VALUE;

 CONF *NCONF_new_ex(OSSL_LIB_CTX *libctx, CONF_METHOD *meth);
 CONF *NCONF_new(CONF_METHOD *meth);
 void NCONF_free(CONF *conf);
 CONF_METHOD *NCONF_default(void);
 int NCONF_load(CONF *conf, const char *file, long *eline);
 OSSL_LIB_CTX *NCONF_get0_libctx(const CONF *conf);

 STACK_OF(CONF_VALUE) *NCONF_get_section(const CONF *conf, const char *name);
 STACK_OF(OPENSSL_CSTRING) *NCONF_get_section_names(const CONF *conf);

=head1 DESCRIPTION

NCONF_new_ex() creates a new CONF object in heap memory and assigns to
it a context I<libctx> that can be used during loading. If the method table
I<meth> is set to NULL then the default value of NCONF_default() is used.

NCONF_new() is similar to NCONF_new_ex() but sets the I<libctx> to NULL.

NCONF_free() frees the data associated with I<conf> and then frees the I<conf>
object.

NCONF_load() parses the file named I<filename> and adds the values found to
I<conf>. If an error occurs I<file> and I<eline> list the file and line that
the load failed on if they are not NULL.

NCONF_default() gets the default method table for processing a configuration file.

NCONF_get0_libctx() gets the library context associated with the I<conf>
parameter.

NCONF_get_section_names() gets the names of the sections associated with
the I<conf> as B<STACK_OF(OPENSSL_CSTRING)> strings. The individual strings
are associated with the I<conf> and will be invalid after I<conf> is
freed. The returned stack must be freed with sk_OPENSSL_CSTRING_free().

NCONF_get_section() gets the config values associated with the I<conf> from
the config section I<name> as B<STACK_OF(CONF_VALUE)> structures. The returned
stack is associated with the I<conf> and will be invalid after I<conf>
is freed. It must not be freed by the caller.

=head1 RETURN VALUES

NCONF_load() returns 1 on success or 0 on error.

NCONF_new_ex() and NCONF_new() return a newly created I<CONF> object
or NULL if an error occurs.

=head1 SEE ALSO

L<CONF_modules_load_file(3)>,

=head1 HISTORY

NCONF_new_ex(), NCONF_get0_libctx(), and NCONF_get_section_names() were added
in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
