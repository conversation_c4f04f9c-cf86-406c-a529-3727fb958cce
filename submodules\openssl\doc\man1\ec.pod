=pod

=head1 NAME

openssl-ec,
ec - EC key processing

=head1 SYNOPSIS

B<openssl> B<ec>
[B<-help>]
[B<-inform PEM|DER>]
[B<-outform PEM|DER>]
[B<-in filename>]
[B<-passin arg>]
[B<-out filename>]
[B<-passout arg>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-text>]
[B<-noout>]
[B<-param_out>]
[B<-pubin>]
[B<-pubout>]
[B<-conv_form arg>]
[B<-param_enc arg>]
[B<-no_public>]
[B<-check>]
[B<-engine id>]

=head1 DESCRIPTION

The B<ec> command processes EC keys. They can be converted between various
forms and their components printed out. B<Note> OpenSSL uses the
private key format specified in 'SEC 1: Elliptic Curve Cryptography'
(http://www.secg.org/). To convert an OpenSSL EC private key into the
PKCS#8 private key format use the B<pkcs8> command.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform DER|PEM>

This specifies the input format. The B<DER> option with a private key uses
an ASN.1 DER encoded SEC1 private key. When used with a public key it
uses the SubjectPublicKeyInfo structure as specified in RFC 3280.
The B<PEM> form is the default format: it consists of the B<DER> format base64
encoded with additional header and footer lines. In the case of a private key
PKCS#8 format is also accepted.

=item B<-outform DER|PEM>

This specifies the output format, the options have the same meaning and default
as the B<-inform> option.

=item B<-in filename>

This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.

=item B<-passin arg>

The input file password source. For more information about the format of B<arg>
see L<openssl(1)/Pass Phrase Options>.

=item B<-out filename>

This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should B<not> be the same as the input
filename.

=item B<-passout arg>

The output file password source. For more information about the format of B<arg>
see L<openssl(1)/Pass Phrase Options>.

=item B<-des|-des3|-idea>

These options encrypt the private key with the DES, triple DES, IDEA or
any other cipher supported by OpenSSL before outputting it. A pass phrase is
prompted for.
If none of these options is specified the key is written in plain text. This
means that using the B<ec> utility to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.

=item B<-text>

Prints out the public, private key components and parameters.

=item B<-noout>

This option prevents output of the encoded version of the key.

=item B<-pubin>

By default, a private key is read from the input file. With this option a
public key is read instead.

=item B<-pubout>

By default a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.

=item B<-conv_form>

This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: B<compressed> (the default
value), B<uncompressed> and B<hybrid>. For more information regarding
the point conversion forms please read the X9.62 standard.
B<Note> Due to patent issues the B<compressed> option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro B<OPENSSL_EC_BIN_PT_COMP> at compile time.

=item B<-param_enc arg>

This specifies how the elliptic curve parameters are encoded.
Possible value are: B<named_curve>, i.e. the ec parameters are
specified by an OID, or B<explicit> where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is B<named_curve>.
B<Note> the B<implicitlyCA> alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.

=item B<-no_public>

This option omits the public key components from the private key output.

=item B<-check>

This option checks the consistency of an EC private or public key.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<ec>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=back

=head1 NOTES

The PEM private key format uses the header and footer lines:

 -----BEGIN EC PRIVATE KEY-----
 -----END EC PRIVATE KEY-----

The PEM public key format uses the header and footer lines:

 -----BEGIN PUBLIC KEY-----
 -----END PUBLIC KEY-----

=head1 EXAMPLES

To encrypt a private key using triple DES:

 openssl ec -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl ec -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl ec -in key.pem -text -noout

To just output the public part of a private key:

 openssl ec -in key.pem -pubout -out pubkey.pem

To change the parameters encoding to B<explicit>:

 openssl ec -in key.pem -param_enc explicit -out keyout.pem

To change the point conversion form to B<compressed>:

 openssl ec -in key.pem -conv_form compressed -out keyout.pem

=head1 SEE ALSO

L<ecparam(1)>, L<dsa(1)>, L<rsa(1)>

=head1 COPYRIGHT

Copyright 2003-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
