<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : CCAPI Function Helper Macros</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>CCAPI Function Helper Macros</h1>
<p>
<h2>Defines</h2>
<ul>
<li>#define <a class="el" href="group__helper__macros.html#g8ff82ce108889d4ed29f46ffe6efc40e">cc_context_release</a>(context)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; release (context))
<li>#define <a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time</a>(context, change_time)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; get_change_time (context, change_time))
<li>#define <a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name</a>(context, name)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; get_default_ccache_name (context, name))
<li>#define <a class="el" href="group__helper__macros.html#g256a5ba17fe0e4502e0722d9b081bbef">cc_context_open_ccache</a>(context, name, ccache)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; open_ccache (context, name, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g45a7ce29eb409baabadcae1bc95d5c57">cc_context_open_default_ccache</a>(context, ccache)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; open_default_ccache (context, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache</a>(context, name, version, principal, ccache)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_ccache (context, name, version, principal, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache</a>(context, version, principal, ccache)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_default_ccache (context, version, principal, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache</a>(context, version, principal, ccache)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_new_ccache (context, version, principal, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g6957bc9570e4769a5b1213d2a1d90cd7">cc_context_new_ccache_iterator</a>(context, iterator)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; new_ccache_iterator (context, iterator))
<li>#define <a class="el" href="group__helper__macros.html#gcf4595340ddc8dafa539a86ac317625d">cc_context_lock</a>(context, type, block)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; lock (context, type, block))
<li>#define <a class="el" href="group__helper__macros.html#g544261b88c9ac0f2379a35648cae3f27">cc_context_unlock</a>(context)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; unlock (context))
<li>#define <a class="el" href="group__helper__macros.html#g9258ef05d06f3d4dc798ec654f78b967">cc_context_compare</a>(context, compare_to, equal)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; compare (context, compare_to, equal))
<li>#define <a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change</a>(context)&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; wait_for_change (context))
<li>#define <a class="el" href="group__helper__macros.html#ge517135d87d8775d77b426d57a491ef0">cc_ccache_release</a>(ccache)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; release (ccache))
<li>#define <a class="el" href="group__helper__macros.html#ge05b68d91bece2f99b531e96cde8d457">cc_ccache_destroy</a>(ccache)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; destroy (ccache))
<li>#define <a class="el" href="group__helper__macros.html#g535b92993b85d92b67fa622447afbe13">cc_ccache_set_default</a>(ccache)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_default (ccache))
<li>#define <a class="el" href="group__helper__macros.html#g934f93499765bdd179bb2342ae0f0fa6">cc_ccache_get_credentials_version</a>(ccache, version)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_credentials_version (ccache, version))
<li>#define <a class="el" href="group__helper__macros.html#g042bea6044879ec03996b190792e3ae9">cc_ccache_get_name</a>(ccache, name)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_name (ccache, name))
<li>#define <a class="el" href="group__helper__macros.html#g464aa49a2e8054c9c3c2a3410eaf5c54">cc_ccache_get_principal</a>(ccache, version, principal)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_principal (ccache, version, principal))
<li>#define <a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal</a>(ccache, version, principal)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_principal (ccache, version, principal))
<li>#define <a class="el" href="group__helper__macros.html#g35c1548dbacb8907da7b8c3124eabf39">cc_ccache_store_credentials</a>(ccache, credentials)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; store_credentials (ccache, credentials))
<li>#define <a class="el" href="group__helper__macros.html#ga1bbc05414ad4c17cea9cd5e5c50c7cc">cc_ccache_remove_credentials</a>(ccache, credentials)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; remove_credentials (ccache, credentials))
<li>#define <a class="el" href="group__helper__macros.html#g893b31c419e71c2f528781d3036fa3ff">cc_ccache_new_credentials_iterator</a>(ccache, iterator)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; new_credentials_iterator (ccache, iterator))
<li>#define <a class="el" href="group__helper__macros.html#gb8c2624719ee1c4be5f1b1bc4844f0cc">cc_ccache_lock</a>(ccache, type, block)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; lock (ccache, type, block))
<li>#define <a class="el" href="group__helper__macros.html#ge9b13c950cb6ee636c4a73d6c569a811">cc_ccache_unlock</a>(ccache)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; unlock (ccache))
<li>#define <a class="el" href="group__helper__macros.html#g884b0c60718fa1057574a3cd844e96ee">cc_ccache_get_last_default_time</a>(ccache, last_default_time)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_last_default_time (ccache, last_default_time))
<li>#define <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time</a>(ccache, change_time)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_change_time (ccache, change_time))
<li>#define <a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move</a>(source, destination)&nbsp;&nbsp;&nbsp;((source) -&gt; functions -&gt; move (source, destination))
<li>#define <a class="el" href="group__helper__macros.html#g197ff60fac986634fbef8ca102ec54a5">cc_ccache_compare</a>(ccache, compare_to, equal)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; compare (ccache, compare_to, equal))
<li>#define <a class="el" href="group__helper__macros.html#g1fa36a89752da4a491d2ecdad17f8b0e">cc_ccache_get_kdc_time_offset</a>(ccache, version, time_offset)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_kdc_time_offset (ccache, version, time_offset))
<li>#define <a class="el" href="group__helper__macros.html#g519bf0ab152e5a3d2beee8a76a27d16e">cc_ccache_set_kdc_time_offset</a>(ccache, version, time_offset)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_kdc_time_offset (ccache, version, time_offset))
<li>#define <a class="el" href="group__helper__macros.html#g803c35f92992dc0b73e8809d13ebabab">cc_ccache_clear_kdc_time_offset</a>(ccache, version)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; clear_kdc_time_offset (ccache, version))
<li>#define <a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change</a>(ccache)&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; wait_for_change (ccache))
<li>#define <a class="el" href="group__helper__macros.html#ge9bebfed2d574e69f29dd341bc8a63d9">cc_string_release</a>(string)&nbsp;&nbsp;&nbsp;((string) -&gt; functions -&gt; release (string))
<li>#define <a class="el" href="group__helper__macros.html#gab5cad8ca82847950956b0f493132c14">cc_credentials_release</a>(credentials)&nbsp;&nbsp;&nbsp;((credentials) -&gt; functions -&gt; release (credentials))
<li>#define <a class="el" href="group__helper__macros.html#g39ae30e49dba65b87c6b9794f20fb784">cc_credentials_compare</a>(credentials, compare_to, equal)&nbsp;&nbsp;&nbsp;((credentials) -&gt; functions -&gt; compare (credentials, compare_to, equal))
<li>#define <a class="el" href="group__helper__macros.html#g34f37496fb8bc414aafb0b265afecb1b">cc_ccache_iterator_release</a>(iterator)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; release (iterator))
<li>#define <a class="el" href="group__helper__macros.html#gcff0b3e247a2adc95442324fec6c5651">cc_ccache_iterator_next</a>(iterator, ccache)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; next (iterator, ccache))
<li>#define <a class="el" href="group__helper__macros.html#g904d7757fd7ac40f4ee9b448a389f2dd">cc_ccache_iterator_clone</a>(iterator, new_iterator)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; clone (iterator, new_iterator))
<li>#define <a class="el" href="group__helper__macros.html#g79f914583e8076ac24c0d5dde4ddb712">cc_credentials_iterator_release</a>(iterator)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; release (iterator))
<li>#define <a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next</a>(iterator, credentials)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; next (iterator, credentials))
<li>#define <a class="el" href="group__helper__macros.html#g59a9f96a6c00b64c0ab971f7e9b5aae2">cc_credentials_iterator_clone</a>(iterator, new_iterator)&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; clone (iterator, new_iterator))
</ul>
<hr><h2>Define Documentation</h2>
<a class="anchor" name="g8ff82ce108889d4ed29f46ffe6efc40e"></a><!-- doxytag: member="CredentialsCache.h::cc_context_release" ref="g8ff82ce108889d4ed29f46ffe6efc40e" args="(context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; release (context))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="g82f551af17455b78fa3a2e3f83c96907"></a><!-- doxytag: member="CredentialsCache.h::cc_context_get_change_time" ref="g82f551af17455b78fa3a2e3f83c96907" args="(context, change_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_get_change_time          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>change_time&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; get_change_time (context, change_time))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> get_change_time()     </td>
  </tr>
</table>
<a class="anchor" name="gcb4eb9f1db6f8ebf261339ad87cb6c51"></a><!-- doxytag: member="CredentialsCache.h::cc_context_get_default_ccache_name" ref="gcb4eb9f1db6f8ebf261339ad87cb6c51" args="(context, name)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_get_default_ccache_name          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>name&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; get_default_ccache_name (context, name))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> get_default_ccache_name()     </td>
  </tr>
</table>
<a class="anchor" name="g256a5ba17fe0e4502e0722d9b081bbef"></a><!-- doxytag: member="CredentialsCache.h::cc_context_open_ccache" ref="g256a5ba17fe0e4502e0722d9b081bbef" args="(context, name, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_open_ccache          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>name,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; open_ccache (context, name, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> open_ccache()     </td>
  </tr>
</table>
<a class="anchor" name="g45a7ce29eb409baabadcae1bc95d5c57"></a><!-- doxytag: member="CredentialsCache.h::cc_context_open_default_ccache" ref="g45a7ce29eb409baabadcae1bc95d5c57" args="(context, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_open_default_ccache          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; open_default_ccache (context, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> open_default_ccache()     </td>
  </tr>
</table>
<a class="anchor" name="g9fbcbd0f1b107cdaa2a0179e227f82cf"></a><!-- doxytag: member="CredentialsCache.h::cc_context_create_ccache" ref="g9fbcbd0f1b107cdaa2a0179e227f82cf" args="(context, name, version, principal, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_create_ccache          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>name,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>principal,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_ccache (context, name, version, principal, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> create_ccache()     </td>
  </tr>
</table>
<a class="anchor" name="g10a184dd699cae4df6f3480290804a72"></a><!-- doxytag: member="CredentialsCache.h::cc_context_create_default_ccache" ref="g10a184dd699cae4df6f3480290804a72" args="(context, version, principal, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_create_default_ccache          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>principal,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_default_ccache (context, version, principal, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> create_default_ccache()     </td>
  </tr>
</table>
<a class="anchor" name="g1a6dffb1db25590351646fdcf9824f09"></a><!-- doxytag: member="CredentialsCache.h::cc_context_create_new_ccache" ref="g1a6dffb1db25590351646fdcf9824f09" args="(context, version, principal, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_create_new_ccache          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>principal,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; create_new_ccache (context, version, principal, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> create_new_ccache()     </td>
  </tr>
</table>
<a class="anchor" name="g6957bc9570e4769a5b1213d2a1d90cd7"></a><!-- doxytag: member="CredentialsCache.h::cc_context_new_ccache_iterator" ref="g6957bc9570e4769a5b1213d2a1d90cd7" args="(context, iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_new_ccache_iterator          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; new_ccache_iterator (context, iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> new_ccache_iterator()     </td>
  </tr>
</table>
<a class="anchor" name="gcf4595340ddc8dafa539a86ac317625d"></a><!-- doxytag: member="CredentialsCache.h::cc_context_lock" ref="gcf4595340ddc8dafa539a86ac317625d" args="(context, type, block)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_lock          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>type,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>block&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; lock (context, type, block))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> lock()     </td>
  </tr>
</table>
<a class="anchor" name="g544261b88c9ac0f2379a35648cae3f27"></a><!-- doxytag: member="CredentialsCache.h::cc_context_unlock" ref="g544261b88c9ac0f2379a35648cae3f27" args="(context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_unlock          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; unlock (context))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> unlock()     </td>
  </tr>
</table>
<a class="anchor" name="g9258ef05d06f3d4dc798ec654f78b967"></a><!-- doxytag: member="CredentialsCache.h::cc_context_compare" ref="g9258ef05d06f3d4dc798ec654f78b967" args="(context, compare_to, equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_compare          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>compare_to,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>equal&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; compare (context, compare_to, equal))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> compare()     </td>
  </tr>
</table>
<a class="anchor" name="g9eb3508958528c00844a101275497e5a"></a><!-- doxytag: member="CredentialsCache.h::cc_context_wait_for_change" ref="g9eb3508958528c00844a101275497e5a" args="(context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_context_wait_for_change          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">context&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((context) -&gt; functions -&gt; wait_for_change (context))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__context__f.html">cc_context_f</a> wait_for_change()     </td>
  </tr>
</table>
<a class="anchor" name="ge517135d87d8775d77b426d57a491ef0"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_release" ref="ge517135d87d8775d77b426d57a491ef0" args="(ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; release (ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="ge05b68d91bece2f99b531e96cde8d457"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_destroy" ref="ge05b68d91bece2f99b531e96cde8d457" args="(ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_destroy          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; destroy (ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> destroy()     </td>
  </tr>
</table>
<a class="anchor" name="g535b92993b85d92b67fa622447afbe13"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_set_default" ref="g535b92993b85d92b67fa622447afbe13" args="(ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_set_default          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_default (ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> set_default()     </td>
  </tr>
</table>
<a class="anchor" name="g934f93499765bdd179bb2342ae0f0fa6"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_credentials_version" ref="g934f93499765bdd179bb2342ae0f0fa6" args="(ccache, version)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_credentials_version          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_credentials_version (ccache, version))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_credentials_version()     </td>
  </tr>
</table>
<a class="anchor" name="g042bea6044879ec03996b190792e3ae9"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_name" ref="g042bea6044879ec03996b190792e3ae9" args="(ccache, name)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_name          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>name&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_name (ccache, name))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_name()     </td>
  </tr>
</table>
<a class="anchor" name="g464aa49a2e8054c9c3c2a3410eaf5c54"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_principal" ref="g464aa49a2e8054c9c3c2a3410eaf5c54" args="(ccache, version, principal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_principal          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>principal&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_principal (ccache, version, principal))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_principal()     </td>
  </tr>
</table>
<a class="anchor" name="gfaa81492b5d7b3ba00208a9577ce0ba2"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_set_principal" ref="gfaa81492b5d7b3ba00208a9577ce0ba2" args="(ccache, version, principal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_set_principal          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>principal&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_principal (ccache, version, principal))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> set_principal()     </td>
  </tr>
</table>
<a class="anchor" name="g35c1548dbacb8907da7b8c3124eabf39"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_store_credentials" ref="g35c1548dbacb8907da7b8c3124eabf39" args="(ccache, credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_store_credentials          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>credentials&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; store_credentials (ccache, credentials))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> store_credentials()     </td>
  </tr>
</table>
<a class="anchor" name="ga1bbc05414ad4c17cea9cd5e5c50c7cc"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_remove_credentials" ref="ga1bbc05414ad4c17cea9cd5e5c50c7cc" args="(ccache, credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_remove_credentials          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>credentials&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; remove_credentials (ccache, credentials))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> remove_credentials()     </td>
  </tr>
</table>
<a class="anchor" name="g893b31c419e71c2f528781d3036fa3ff"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_new_credentials_iterator" ref="g893b31c419e71c2f528781d3036fa3ff" args="(ccache, iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_new_credentials_iterator          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; new_credentials_iterator (ccache, iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> new_credentials_iterator()     </td>
  </tr>
</table>
<a class="anchor" name="gb8c2624719ee1c4be5f1b1bc4844f0cc"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_lock" ref="gb8c2624719ee1c4be5f1b1bc4844f0cc" args="(ccache, type, block)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_lock          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>type,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>block&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; lock (ccache, type, block))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> lock()     </td>
  </tr>
</table>
<a class="anchor" name="ge9b13c950cb6ee636c4a73d6c569a811"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_unlock" ref="ge9b13c950cb6ee636c4a73d6c569a811" args="(ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_unlock          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; unlock (ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> unlock()     </td>
  </tr>
</table>
<a class="anchor" name="g884b0c60718fa1057574a3cd844e96ee"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_last_default_time" ref="g884b0c60718fa1057574a3cd844e96ee" args="(ccache, last_default_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_last_default_time          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>last_default_time&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_last_default_time (ccache, last_default_time))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_last_default_time()     </td>
  </tr>
</table>
<a class="anchor" name="gb19ef7d2b1bcfb474e18e157fb3bc9c6"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_change_time" ref="gb19ef7d2b1bcfb474e18e157fb3bc9c6" args="(ccache, change_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_change_time          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>change_time&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_change_time (ccache, change_time))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_change_time()     </td>
  </tr>
</table>
<a class="anchor" name="ge1238f80c37ae89486f2ba29bcbcae38"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_move" ref="ge1238f80c37ae89486f2ba29bcbcae38" args="(source, destination)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_move          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">source,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>destination&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((source) -&gt; functions -&gt; move (source, destination))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> move()     </td>
  </tr>
</table>
<a class="anchor" name="g197ff60fac986634fbef8ca102ec54a5"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_compare" ref="g197ff60fac986634fbef8ca102ec54a5" args="(ccache, compare_to, equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_compare          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>compare_to,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>equal&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; compare (ccache, compare_to, equal))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> compare()     </td>
  </tr>
</table>
<a class="anchor" name="g1fa36a89752da4a491d2ecdad17f8b0e"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_get_kdc_time_offset" ref="g1fa36a89752da4a491d2ecdad17f8b0e" args="(ccache, version, time_offset)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_get_kdc_time_offset          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>time_offset&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; get_kdc_time_offset (ccache, version, time_offset))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> get_kdc_time_offset()     </td>
  </tr>
</table>
<a class="anchor" name="g519bf0ab152e5a3d2beee8a76a27d16e"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_set_kdc_time_offset" ref="g519bf0ab152e5a3d2beee8a76a27d16e" args="(ccache, version, time_offset)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_set_kdc_time_offset          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>time_offset&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; set_kdc_time_offset (ccache, version, time_offset))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> set_kdc_time_offset()     </td>
  </tr>
</table>
<a class="anchor" name="g803c35f92992dc0b73e8809d13ebabab"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_clear_kdc_time_offset" ref="g803c35f92992dc0b73e8809d13ebabab" args="(ccache, version)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_clear_kdc_time_offset          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>version&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; clear_kdc_time_offset (ccache, version))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> clear_kdc_time_offset()     </td>
  </tr>
</table>
<a class="anchor" name="gc508ad0c010c88ad8ff0739b43a2b199"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_wait_for_change" ref="gc508ad0c010c88ad8ff0739b43a2b199" args="(ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_wait_for_change          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((ccache) -&gt; functions -&gt; wait_for_change (ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__f.html">cc_ccache_f</a> wait_for_change()     </td>
  </tr>
</table>
<a class="anchor" name="ge9bebfed2d574e69f29dd341bc8a63d9"></a><!-- doxytag: member="CredentialsCache.h::cc_string_release" ref="ge9bebfed2d574e69f29dd341bc8a63d9" args="(string)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_string_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">string&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((string) -&gt; functions -&gt; release (string))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__string__f.html">cc_string_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="gab5cad8ca82847950956b0f493132c14"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_release" ref="gab5cad8ca82847950956b0f493132c14" args="(credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_credentials_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">credentials&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((credentials) -&gt; functions -&gt; release (credentials))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="g39ae30e49dba65b87c6b9794f20fb784"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_compare" ref="g39ae30e49dba65b87c6b9794f20fb784" args="(credentials, compare_to, equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_credentials_compare          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">credentials,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>compare_to,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>equal&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((credentials) -&gt; functions -&gt; compare (credentials, compare_to, equal))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__credentials__f.html">cc_credentials_f</a> compare()     </td>
  </tr>
</table>
<a class="anchor" name="g34f37496fb8bc414aafb0b265afecb1b"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_release" ref="g34f37496fb8bc414aafb0b265afecb1b" args="(iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_iterator_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; release (iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="gcff0b3e247a2adc95442324fec6c5651"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_next" ref="gcff0b3e247a2adc95442324fec6c5651" args="(iterator, ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_iterator_next          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>ccache&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; next (iterator, ccache))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a> next()     </td>
  </tr>
</table>
<a class="anchor" name="g904d7757fd7ac40f4ee9b448a389f2dd"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_clone" ref="g904d7757fd7ac40f4ee9b448a389f2dd" args="(iterator, new_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_ccache_iterator_clone          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>new_iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; clone (iterator, new_iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a> clone()     </td>
  </tr>
</table>
<a class="anchor" name="g79f914583e8076ac24c0d5dde4ddb712"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_release" ref="g79f914583e8076ac24c0d5dde4ddb712" args="(iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_credentials_iterator_release          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; release (iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a> release()     </td>
  </tr>
</table>
<a class="anchor" name="g0c2f41d90f478b2415b699085f8fcaa4"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_next" ref="g0c2f41d90f478b2415b699085f8fcaa4" args="(iterator, credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_credentials_iterator_next          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>credentials&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; next (iterator, credentials))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a> next()     </td>
  </tr>
</table>
<a class="anchor" name="g59a9f96a6c00b64c0ab971f7e9b5aae2"></a><!-- doxytag: member="CredentialsCache.h::cc_credentials_iterator_clone" ref="g59a9f96a6c00b64c0ab971f7e9b5aae2" args="(iterator, new_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">#define cc_credentials_iterator_clone          </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top">iterator,         <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>new_iterator&nbsp;</td>
          <td class="mdname1" valign="top" nowrap>          </td>
          <td class="md" valign="top">&nbsp;)&nbsp;</td>
          <td class="md" nowrap>&nbsp;&nbsp;&nbsp;((iterator) -&gt; functions -&gt; clone (iterator, new_iterator))</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Helper macro for <a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_f</a> clone()     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
