[package]
name = "cryptography-rust"
version = "0.1.0"
authors = ["The cryptography developers <<EMAIL>>"]
edition = "2018"
publish = false

[dependencies]
once_cell = "1"
pyo3 = { version = "0.15.2" }
asn1 = { version = "0.12.2", default-features = false, features = ["derive"] }
pem = "1.1"
chrono = { version = "0.4.22", default-features = false, features = ["alloc", "clock"] }
ouroboros = "0.15"

[features]
extension-module = ["pyo3/extension-module"]
default = ["extension-module"]

[lib]
name = "cryptography_rust"
crate-type = ["cdylib"]

[profile.release]
lto = "thin"
overflow-checks = true
