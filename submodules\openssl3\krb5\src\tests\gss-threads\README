[Out of date; needs updating for thread safety test support.  -- KR 2005-02-09]

# Copyright 1993 by OpenVision Technologies, Inc.
# 
# Permission to use, copy, modify, distribute, and sell this software
# and its documentation for any purpose is hereby granted without fee,
# provided that the above copyright notice appears in all copies and
# that both that copyright notice and this permission notice appear in
# supporting documentation, and that the name of OpenVision not be used
# in advertising or publicity pertaining to distribution of the software
# without specific, written prior permission. OpenVision makes no
# representations about the suitability of this software for any
# purpose.  It is provided "as is" without express or implied warranty.
# 
# OPENVISION DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL OPENVISION BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF
# USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
# OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.

This directory contains a sample GSS-API client and server
application.  In addition to serving as an example of GSS-API
programming, this application is also intended to be a tool for
testing the performance of GSS-API implementations.

Each time the client is invoked, it performs one or more exchanges
with the server.  Each exchange with the server consists primarily of
the following steps:

	1. A TCP/IP connection is established.

	2. (optional, on by default) The client and server establish a
	   GSS-API context, and the server prints the identify of the
	   client.

      /	3. The client sends a message to the server.  The message may
     /     be plaintext, cryptographically "signed" but not encrypted,
     |     or encrypted (default).
     |
0 or |  4. The server decrypts the message (if necessary), verifies
more |     its signature (if there is one) and prints it.
times|
     |  5. The server sends either a signature block (the default) or an
     |     empty token back to the client to acknowledge the message.
     \
      \ 6. If the server sent a signature block, the client verifies
           it and prints a message indicating that it was verified.
  
	7. The client sends an empty block to the server to tell it
	   that the exchange is finished.
  
	8. The client and server close the TCP/IP connection and
	   destroy the GSS-API context.

The client also supports the -v1 flag which uses an older exchange
format compatible with previous releases of Kerberos and with samples
shipped in the Microsoft SDK.
  
The server's command line usage is
  
	gss-server [-port port] [-verbose] [-once] [-inetd] [-export]
		[-logfile file] service_name
  
where service_name is a GSS-API service name of the form
"service@host" (or just "service", in which case the local host name
is used).  The command-line options have the following meanings:
  
-port	The TCP port on which to accept connections.  Default is 4444.
  
-once	Tells the server to exit after a single exchange, rather than
	persisting.
  
-inetd	Tells the server that it is running out of inetd, so it should
	interact with the client on stdin rather than binding to a
	network port.  Implies "-once".
  
-export	Tells the server to test the gss_export_sec_context function
	after establishing a context with a client.

-logfile
	The file to which the server should append its output, rather
	than sending it to stdout.
  
The client's command line usage is

	gss-client [-port port] [-mech mechanism] [-d] [-f] [-q]
        [-seq] [-noreplay] [-nomutual]		
        [-ccount count] [-mcount count] [-na] [-nw] [-nx] [-nm]
		host service_name msg

where host is the host running the server, service_name is the service
name that the server will establish connections as (if you don't
specify the host name in the service name when running gss-server, and
it's running on a different machine from gss-client, make sure to
specify the server's host name in the service name you specify to
gss-client!) and msg is the message.  The command-line options have
the following meanings:

-port	The TCP port to which to connect.  Default is 4444.

-mech	The OID of the GSS-API mechanism to use.

-d	Tells the client to delegate credentials to the server.  For
	the Kerberos GSS-API mechanism, this means that a forwardable
	TGT will be sent to the server, which will put it in its
	credential cache (you must have acquired your tickets with
	"kinit -f" for this to work).

-seq Tells the client to enforce ordered message delivery via
    sequencing.  

-noreplay Tells the client to disable the use of replay
    detection.

-nomutual Tells the client to disable the use of mutual authentication.

-f	Tells the client that the "msg" argument is actually the name
	of a file whose contents should be used as the message.

-q	Tells the client to be quiet, i.e., to only print error
	messages.

-ccount	Specifies how many sessions the client should initiate with
	the server (the "connection count").

-mcount	Specifies how many times the message should be sent to the
	server in each session (the "message count").

-na	Tells the client not to do any authentication with the
	server.  Implies "-nw", "-nx" and "-nm".

-nw	Tells the client not to "wrap" messages.  Implies "-nx".

-nx	Tells the client not to encrypt messages.

-nm	Tells the client not to ask the server to send back a
	cryptographic checksum ("MIC").

To run the server on a host, you need to make sure that the principal
corresponding to service_name is in the default keytab on the server
host, and that the gss-server process can read the keytab.  For
example, the service name "host@server" corresponds to the Kerberos
principal "host/server.domain.com@REALM".

This sample application uses the following GSS-API functions:

	gss_accept_sec_context		gss_inquire_names_for_mech
	gss_acquire_cred		gss_oid_to_str
	gss_delete_sec_context		gss_release_buffer
	gss_display_name		gss_release_cred
	gss_display_status		gss_release_name
	gss_export_sec_context		gss_release_oid
	gss_get_mic			gss_release_oid_set
	gss_import_name			gss_str_to_oid
	gss_import_sec_context		gss_unwrap
	gss_init_sec_context		gss_verify_mic
	gss_inquire_context		gss_wrap
  
This application was originally written by Barry Jaspan of OpenVision
Technologies, Inc.  It was updated significantly by Jonathan Kamens of
OpenVision Technologies, Inc.

$Id$
