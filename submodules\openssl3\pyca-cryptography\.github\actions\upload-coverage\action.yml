name: Upload Coverage
description: Upload coverage files

runs:
  using: "composite"

  steps:
    - run: |
        COVERAGE_UUID=$(python3 -c "import uuid; print(uuid.uuid4())")
        echo "::set-output name=COVERAGE_UUID::${COVERAGE_UUID}"
        if [ -f .coverage ]; then
          mv .coverage .coverage.${COVERAGE_UUID}
        fi
      id: coverage-uuid
      shell: bash
    - uses: actions/upload-artifact@v3.1.0
      with:
        name: coverage-data
        path: |
          .coverage.*
          *.lcov
        if-no-files-found: ignore
