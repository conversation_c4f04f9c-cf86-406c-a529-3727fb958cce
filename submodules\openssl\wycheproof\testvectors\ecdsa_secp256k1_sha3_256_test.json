{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 388, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "wx": "00b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6f", "wy": "00f0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEuDj/ROW8F3vyEYnQdmCC/J2EMiaIf8l2\nA3EQC37iCm/wyddb+6ezGmvKGXRJbutW3jVwcZVdg8Sxutqgshgy6Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022100bbdbc26e1099b2713ada34df9cfa8edaf905a4a6d2a1f449f05de03df8c2a696", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "30440220eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "308145022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "30820045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3044022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "30850100000045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "3089010000000000000045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3045028000eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251028044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30470000022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "304a4981773045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "304925003045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30473045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "304a2226498177022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304922252500022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304d2223022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510004deadbeef022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "304a022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512225498177022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25122242500022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "304d022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512222022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "304daa00bb00cd003045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304baa02aabb3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d2229aa00bb00cd00022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b2227aa02aabb022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512228aa00bb00cd00022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512226aa02aabb022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30803045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30492280022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510000022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512280022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30803145022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30492280032100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510000022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512280032044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e45022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f45022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3145022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3245022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff45022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "304930010230442100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "3044022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739a", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "30442100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab00", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab05000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30473000022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab3000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3048022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aabbf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "30473045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "3023022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "3067022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "304602812100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102812044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30470282002100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510282002044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3045022200eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3045022000eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022144243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251021f44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a0285010000002100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510285010000002044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e028901000000000000002100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251028901000000000000002044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304902847fffffff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102847fffffff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490284ffffffff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510284ffffffff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0285ffffffffff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510285ffffffffff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0288ffffffffffffffff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510288ffffffffffffffff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304502ff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102ff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "3022022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302302022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3024022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3047022300eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510000022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022244243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30470223000000eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510222000044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510000022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3047022300eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510500022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3047022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022244243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab0500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30240281022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3025022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30240500022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3025022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045002100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045012100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045032100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045042100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045ff2100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251002044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251012044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251032044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251042044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251ff2044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30240200022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3025022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "304922250201000220eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "3049022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2512224020144021f243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3045022102eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022046243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2d1022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739a2b", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "3044022000eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "3044022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251021f44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739a", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "3044022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251021f243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460222ff00eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510221ff44243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3025090180022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3026022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3025020100022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3026022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022101eb044a2e719d94a33837717ce9bc5ff94062cf047015777244b442e323862392022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30440220eb044a2e719d94a33837717ce9bc5ffbcb051537118436fac50f85c98319a110022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221ff14fbb5d18e626b5cc7c88e831643a0057a4c0de23f3328c97b1e1ba9acb01daf022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3044022014fbb5d18e626b5cc7c88e831643a00434faeac8ee7bc9053af07a367ce65ef0022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221fe14fbb5d18e626b5cc7c88e831643a006bf9d30fb8fea888dbb4bbd1cdc79dc6e022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022101eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3044022014fbb5d18e626b5cc7c88e831643a0057a4c0de23f3328c97b1e1ba9acb01daf022044243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102210144243d91ef664d8ec525cb20630571227c5815268bef4c2d8f46dcdba7a9dbec", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510221ff44243d91ef664d8ec525cb206305712506fa5b592d5e0bb60fa21fc2073d596a", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510220bbdbc26e1099b2713ada34df9cfa8edc3e56c7c02359540e308b81b1288c6555", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe2510221febbdbc26e1099b2713ada34df9cfa8edd83a7ead97410b3d270b9232458562414", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe25102210144243d91ef664d8ec525cb2063057123c1a9383fdca6abf1cf747e4ed7739aab", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100eb044a2e719d94a33837717ce9bc5ffa85b3f21dc0ccd73684e1e456534fe251022100bbdbc26e1099b2713ada34df9cfa8edc3e56c7c02359540e308b81b1288c6555", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641400201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc300201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3232333836", "sig": "3046022100dd1b7d09a7bd8218961034a39a87fecf5314f00c4d25eb58a07ac85e85eab516022100b98c5232f0100d55db14eb0fe9e943fb45d8f192bbdd38147850f4d950f16a91", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "33313930393433323838", "sig": "3046022100840a6cd819f21a2a3c3be7461bf516f5191c32d059eea09699ac4132f794881902210094c53906a1595cf9fe14831b5298b4e297219afb895c18a19f4508fa4f6e0394", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "383433343734313535", "sig": "304502205928b7eeb84242914d4d5b871feb3b0d789455e44a41e3b60e0e43856a4a7a39022100d650930d76eb2444713b63b501a8e8b39615784306f1f2fa90915066e4f60192", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "33353732363936383239", "sig": "304502202ff05b06077811e7bf8a1b8804fa6bb7db793b0a8927745f5b543998dab306b3022100c9e7da07e2b2d28f169924bab22d90a107ca97f5022eac08d0a4577f30d89988", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "353931383333343239", "sig": "3045022019c5e74fd3ab3847d1ba8ec6ff682b184ed2ae466622890deb4206385c31b0a5022100c959ebce99b3446aacee56eecdbae1898fc71a6bacb4464a6a4b0276821b32e7", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "3832353330383232353839", "sig": "3045022054bb584a67c79e19d3f9627cc1eadacce8075e3f5c03e45c807b46d505ca73ab022100ab37fbc790a0400debbbde06b9771b63732d79de6a56e87275a968e0d4aaefbf", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3133373334383238313432", "sig": "30450220699d4d68c233f44bf1d3f70001a9acac7be906e09ac440c8d16044364696b94d0221009990c2cd8d7c6a227dce6a94900bc7b69a8ee6cf0ba062767c09d9e5b12e413c", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "313733383135363430", "sig": "3046022100e779c882a97701293daa1413f9fe49ab97bd8f742331461d0e3b93333c1db5bb022100ad3fd904ab463ec8bc7ff988c142acdbc5dd73d8dce919b458987c1f32ba3e9b", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32353533343636353034", "sig": "3046022100d121d4639e90e4741919d9cb3888d69c46d6fdc84980b5ecc249fa01cae19be5022100ac0559aa580e535e401ea9e2710f067a375ec69dc49fba668d7a14d8bde42d0d", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32313833303339313937", "sig": "3046022100de04d387ddd0189ef2ec494594ed056675788d6cac25f9826e50fec66f47be6f022100a55cbc3e87809b4dcc634cea32fc23cf7ac70f71ef1731de41414c0a71891cb5", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "3130353236313736353435", "sig": "304502205a8d2d504831a047c7277d9c13f7f456fd9569a311c5be93cbfa9a3122534ff3022100d0f9586630564236e9b133a7b53202b29d3a3caeb28f5d2360adfea238f41529", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32353031353339393836", "sig": "304502200e2dc3e0b7c51be950c814b4cd74b8707753bc5a7543d6589ae1464c93227bf70221009cea04df1218bb7a0c851da9fef4069cfca9fc00ef08c37976adfc4ec7b5e2b2", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "3134393533313634363139", "sig": "304502200c93646c509040bac868258bf3f2d13d26e98993e8680f0da846c1712be95109022100c65386f8b0a12fef25791cd93a045140af9c24fe3d3d700e02d23b1ce2da05f3", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "32303633383532393036", "sig": "30450221009906860d728638f6a260e13495f2c6099838e5c2f94828f10caf2c58970d3bf802204853235fd511b8db3956bd25b772fab54bad3867d1c637a9984016f785fdc6cf", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "37383339373634393932", "sig": "3045022100c6d609fc861a35134b4dc180a3b2a7b13ad8477358e80286f90499c58bd37dd302200978e0b21055dcc81844d297d6bbecbd074f09717b46c695ae60799d564a1f9f", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32313239323634343639", "sig": "304502201496fd7a5023faf78b0e1008b054f25c509d34713d4594cfabf24c1b2229643d022100f660ac1daa7700a55189d6710a373b350ea2446ae76fc8a3522df3e01a2bc2f1", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "34383332303432363833", "sig": "3044022031007f0306f171eb56c9bc7f7c0cd7d776acd86be680f600d3729aedc03aa9ef022059f529aecb6c8e7469830daea5065e6da8c349688ab4fa0ebec364035a68e58a", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "333635323231383136", "sig": "3046022100fbe1a139e3c74cef01d21d9c5a47a783080dbd9b86a202e933872a71a4b53838022100fe3164ad51c080ddd4126f42979e6b519075b2ec96060e02f9dd6fb6f9f3bfdf", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3234393137313236393635", "sig": "3044022004518c6be6586ceb5559014ff40311fe7e6d0ffcdfc655b6a06bbe203a185ed602201e0b927e43125aa196329bb0f09bf75d0481dba924f91e3e39e3e0878a972a83", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "32363232373930373738", "sig": "3046022100ad67c0270ea088a9daa805788b6aa5161c6e7e12d237515518914ab66d1dcb66022100c5fa3b243e9148e1dcfc27abd9991a2c0c2d25bde9822ce26f344bc9e03f9ee7", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "313031323537373238393237", "sig": "3045022100edd7e3fb8581ded7c0961f7365a1a39c6fa301d9728000aeb84c41d918c17dbb022025cfb4fdade11816359ccfd2001cc2b0e509de9cca0c1aa7eaae719637e11156", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31303530383732323032", "sig": "304402204ca5021a99c50916f997009a2f6addc6cb2a57cada7b1eb72821f66ec353516d022043d471d4043f8fbb0765c059d1b5386b49a530a626d26d2bed4323c0aea5d24a", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32353037373237303332", "sig": "3045022100eb3a1a9165de050206fa045882f7f3bd06bd02c2e825740d72d8cb2a07f45cfb0220394fa8625004c62cb1c8eea02c3411e6a036b4afe14727d497b31d7251d4a20c", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "373139333435393339", "sig": "304502207921badf49f2beba3bc6d696494e7f6c74edc3b722247adbc9cf54d02527ef30022100a45ef9b623bad9a24433afc7e4e2b25270cf07ab20e29ee822255b6ee8da233d", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "31363337333035373633", "sig": "304402203e2c342f84cb36f986b72bd19867c359ad195046ef30ca7549df842d33a51ccb02205b8bfcfc785ff44ccc2651b893b5dfbc12739cc3973988dbb209cd60f4c1b4e2", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "34323431323733373133", "sig": "3046022100919eb36b4949e319427b2113927fd40f767c11d2c6a991c558438790959c00710221009e4bfd8bcca87632071bdc109cd47e45c90f7cbbff3ff05a1591585b2f0f6537", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "343735363134373337", "sig": "3045022100e36e3c1918e378f12ccaefe24954c4fb77d8a227f7a234a045c2fa69ec0184c4022065f7b5def112fd96d3c3ddf3aa5bce418ae5cb7322387b18b5b15e2caa78f209", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "37313632333237373334", "sig": "304602210089674f75b7440869f9de0cdde21ef47003309be9f0ff7f858c6f43a3b9067096022100d37781ff993210da5470ba8ce3c16a088e58e79d7fd0f5e2d2336443d9b1aeb8", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "31323232343034313239", "sig": "30440220584c05af98b487e9a0b5dd5e0154d124aeefa55eb48a274721365e597549ec98022047b4127c6c09077615a921be38942baa053a88b73884dfadd6a745cc9c6fa096", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "33353234353833333638", "sig": "304402205f21b554fd91ca9cdd5109a00ab3ecb2d8b5137b4fd05c254c3faaa377b3da0602205d036a7dbebf9351c88d3bbe03991690cb7b67d3b5ca4266eb25029e3a1f75e6", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "3331363334333936", "sig": "304402206a309780826539059b3b2c9d4315bbb83b4c3afc218d440acf2d01ec0a5cdf8302205d3ea569a5ad21db62e4bc0b60251e5f65b01158f2c8821973ee6c47cd15fc34", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "36353332353938343339", "sig": "3045022041d51f04d6fdcc5f5cacf88e50e418ef0067f8d854dc991615003f1e49927a53022100c6f7c10cad03b89460a9794a171f2e10d84982c462cbf075b06738b3f904cc5c", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "373338353138353034", "sig": "3046022100da8e729ac23689e868129854fbbde5c9130ebad0e555047f6c4ffccdb0d75fde022100b693c1a3ccd93e2989f84e77e0ea5983b758f4c1a2a8c4b6219b6b006e9ba1e5", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "31343635323636353739", "sig": "304502200793b70b17c7db1ee4f84a0fcc27115355bca4036e33830bddb58aaaf21db1e9022100b884dc3329f826a3cc1766ab7f67cd31ad17b4d48e81b8641d6cf70400c80649", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "32303334383839343934", "sig": "304402203ee201732af7f4fb862991d162a11f79fae57233ff964782db1b35b2dee67f60022078e00f30babf2d483c9e9729c50ac07df9abe878ff8edd3cd7ea3cecc30b724e", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "34343730383836373835", "sig": "30460221008f2c4f9daeae645deb8237f2598485a7c3ac3b0e0b945641e4f24f59ffe7845a022100a7f781e40a73cc4f49159ed982ffb264097c5f34382314ba0128a52c9144fd33", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "353537363430343436", "sig": "30450220750dad3a83d3c3621a78dcd92f7da948c6fc68d7f0d9603835b2488515c539ae022100a0736c57503c76c2342e3f13e55f6dfb6637b1fb6ba5caf00b9e540daa7f70c6", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "36333434393536363038", "sig": "3046022100b3d14a7f7dd693c7fd62d073cb6bc77504431d8a992cc64af774703a981db0a1022100ab8a35acce773242850c086013869631e99cf9c5378d39d0fe10ca7b504c0cf9", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34393432383039303331", "sig": "3046022100ff35621d420e7a8343d222f18acb46ae5a5a29320f88e2f0977acfd96d7014410221009fc29bfd8a80a24959bd4494de1b3c0a3366131aefef4fe9d33f1f91d118bb27", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "323636353831393339", "sig": "30450220051291f27408436b4c56cc8993b3891c5c3a4bf3747041b4d915fdccc1c67a59022100f8d6971a948332617564b4c9581850f8992752f1afe30370a4d36af72376672f", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "33333332323134313134", "sig": "3046022100b820f2163d1a902e847c69392da7124bc31f56ecad5f73c3db142c9c8220cc6502210089c527e55e559aa5efb263860fbac04f1ce556f82bcccb49991bc2c575808aa7", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "363136353237373135", "sig": "304402200180c08e97d4fe407c0eab2eb7d17bae60e8ca9ad459e57cdf48389ed9ed953602207d5eaeffffba65afbf1ba9ca9bc0fe1181da76e5e41ade8687799b09e9104597", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32333539393535383133", "sig": "3046022100985f15f0eecc62112817bc234784d60404804ea7dba48f8c09cc02401c4e13ae022100c73d1bed7077734492c700ede8e6800e048523ef9bcffb53cc79945805ff711e", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "323931333631393538", "sig": "3046022100d9a5ae9012bcacfc12fa3db623d2099657d4f321460d0135bc731a70478b79bc022100a5d882aa5cf390737839443ab059d68282064d3d827bfef52fc176d0de60ed46", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "31363230393033333936", "sig": "3046022100f070e1285c47106a1ad23a774756a3d3453a48d245401604ef59a96b9a1910c2022100b43cf52041613dbf8d3a136a0d0f6bce87cd74262224e620f355ddeced20e5bd", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33333633313032383432", "sig": "3046022100963a3ae4b0a7ae86047e47f375c7e42de035f28fb430c408d0d815caebefa344022100a1edd8c2d39f04f99e05a793b7970dfa76f4b1fc0663d308edee9d3ecd077d66", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "363237373433343931", "sig": "3046022100e6adb9139cf47dae0890006732629c8e095c13df370717a42a8bc6e8936678ef022100a8df8acc7ee7551cf0409e8c1c2fd0df6e7e9b3827e95727fa492c274e4668fc", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "33383536373938313437", "sig": "3044022007662a36a2bb779a276145e78543c360c7d0a22a1749f69ead2788c75750d24802207c0a4dba499b27cc249a705ba7bbf512a7484b93f9a83ca9305dd49cde6a302b", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "32383831313830363430", "sig": "3045022036df003efdbec3bf53a2a45248c1e96e60c9bcf10b4f5dfb220744d2da51fc8e022100e5f103b3a74fa1d0a78e74d604f31889e6637cff2acbb31a70726e72f392d4ba", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "3130393537333934363738", "sig": "30440220712dc3233f462b0a37f020ec559bb1a19d879ae36210c75efcb9c071915116e1022006a981761249cc1929f5c18d6f2a76eef487bbda0c4470bb098b87b91328083f", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "353432373131393834", "sig": "3044022010e373d1cb4c05295b63ce7103817b7c0fd096d7c63f65f56d950a61e455c1cb022044cb5c8270c069ac408a6c9f31ace9229ab6078a36adc465107f0a3d6ddfea66", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "33313530303334333235", "sig": "3045022100cd1274f4c89ab194203ccb5c39e7d0bc364537b84b9dd48d922e43e79e4258c2022042e1fcf72eb65d76b13128d3065daa31312bf9c110f18b4799dce8eccae52d67", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31393939383231333931", "sig": "30450220516c983fe6567ac700f93028da6affc598dfa95391896c544c8f73c96314a0a0022100bfa56a1833668acfd14899e8cc160b79c5e92a30055dd7c700484f6bfce42cfd", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31393731323039323631", "sig": "3045022052279b3df58e2aa7ddaee1e5de155cb75d4f00ec7db74ae913a6ed33dea896d4022100ef5823ff9977fa492483bcbfc1d0bd765fd6dfa78cc11e658b4984b543e0e79e", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "33353731383338383537", "sig": "3045022001c2a04eef5827e7e04eb51802cc3859af6d84fe35aee4da4bc1b0ee154b7ef3022100dc57a107da6bb12624313660233cbdcc55ff7147ecb3a328af3e86225c89be53", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "38363832323639383935", "sig": "304502201ce1bb1fc78a38d4af211b5fceebd01126c10ceab1de6401e1df1dc495dbf5b5022100c9b564a0a5b9675eece3cbe33498634e7943893fe16c61ef894bd4be349a6874", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "32353438383536303331", "sig": "3045022100b7ae42b36f060c15c6745ea4d8bd91ae2eafe0e196c52cfac4e16ab74d3048b60220421bc2dcd0854dd4e69a3e930b2cb646557bd68c800c5a2ca7bbb3ddd32370aa", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34393935373031333139", "sig": "3045022100d51dc206df9cfb7198e22b957c644357542264badf5aede3f7474534da0d5b220220266d172a6d6775963f9ed4fb59065c8f1948c48a51463fe79bbf1b45df7e57b9", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "32343536363831373835", "sig": "3045022100f881b3e21684fbf899f762c8fc7c7423a2ad2c276257c99eae86b66ee39e4ae1022027207d5ccff773b26bf0d282d884b3c3a6724ba06a1671c9f9be8cbe6e3589e4", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "34313835333731323336", "sig": "304402202455ebf62b50f958781792fdc705755923a30c0eb7d515a0988c1a14de62caad022010bd68c881416205bd95a5f2765d69726e0bce5b2a0ec525aeb1bba7d35d8e4a", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "373237383439303034", "sig": "30450221009119d7949d9e4c55e4c712d257c4ba3ab9d657c7e0aa7840091cb2acfb4fc25a022042524fd0c4ae8b50644cba34f86c21a42ee045ce7c15b4eb817affc78d20fdb3", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "31353632383139333138", "sig": "30450220191e716669d84631a04cc085f03b2f1a4f55810f70bebbaf5ee13d68f2598ffb02210090f208a9f1c27911b5fb8d867bdf123dd601639c2dfa1f6a61fd2f82cadb1361", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "3231383739393238333836", "sig": "30460221008cc2cab9f257928181c4d3685d544bec0b88b95cbbdb8ad1b0543b46b24144730221009d1d158dab8e91c68b372ade107aac5c22f8be64463b0c23340dfc828d7b7df3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0427504e893fd62d0bfdeaa073106b16e8f8d2726a9762529764cfe8fe8a38460e21bb0ddff040b7aff8f08a60d5ae1a59472f394846ae4f58c4be0cc8a2a36501", "wx": "27504e893fd62d0bfdeaa073106b16e8f8d2726a9762529764cfe8fe8a38460e", "wy": "21bb0ddff040b7aff8f08a60d5ae1a59472f394846ae4f58c4be0cc8a2a36501"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000427504e893fd62d0bfdeaa073106b16e8f8d2726a9762529764cfe8fe8a38460e21bb0ddff040b7aff8f08a60d5ae1a59472f394846ae4f58c4be0cc8a2a36501", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEJ1BOiT/WLQv96qBzEGsW6PjScmqXYlKX\nZM/o/oo4Rg4huw3f8EC3r/jwimDVrhpZRy85SEauT1jEvgzIoqNlAQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 293, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "30360211014551231950b75fc4402da1722fc9baeb022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}, {"tcId": 294, "comment": "r too large", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2c022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f131f6dddde59bae7b0090a47bafbb33c157ac6da439324a6681bf67f575f90beccc2fb2c0be318fda9335bb83488bcafd33be82c38318bcf845fd0e5017c248", "wx": "00f131f6dddde59bae7b0090a47bafbb33c157ac6da439324a6681bf67f575f90b", "wy": "00eccc2fb2c0be318fda9335bb83488bcafd33be82c38318bcf845fd0e5017c248"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f131f6dddde59bae7b0090a47bafbb33c157ac6da439324a6681bf67f575f90beccc2fb2c0be318fda9335bb83488bcafd33be82c38318bcf845fd0e5017c248", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE8TH23d3lm657AJCke6+7M8FXrG2kOTJK\nZoG/Z/V1+QvszC+ywL4xj9qTNbuDSIvK/TO+gsODGLz4Rf0OUBfCSA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "r,s are large", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041101c496d5f8910a7749efff9dc46f68a7fd02d6975fdf15bf90efb70463cb4ede199e46e67d463aa8c752cac8a342b8fe0e9a5ba9a67416c8865c45e478007e", "wx": "1101c496d5f8910a7749efff9dc46f68a7fd02d6975fdf15bf90efb70463cb4e", "wy": "00de199e46e67d463aa8c752cac8a342b8fe0e9a5ba9a67416c8865c45e478007e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041101c496d5f8910a7749efff9dc46f68a7fd02d6975fdf15bf90efb70463cb4ede199e46e67d463aa8c752cac8a342b8fe0e9a5ba9a67416c8865c45e478007e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEEQHEltX4kQp3Se//ncRvaKf9AtaXX98V\nv5DvtwRjy07eGZ5G5n1GOqjHUsrIo0K4/g6aW6mmdBbIhlxF5HgAfg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203e9a7582886089c62fb840cf3b83061cd1cff3ae4341808bb5bdee6191174177", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e43a5c63ad0bc8d178a745192671c06500f0dbd757c3f2eae65089aaf0d648982954ff60c3460a27748445525c6cd30701725e1697891cb7f32feed128a3ae7", "wx": "6e43a5c63ad0bc8d178a745192671c06500f0dbd757c3f2eae65089aaf0d6489", "wy": "0082954ff60c3460a27748445525c6cd30701725e1697891cb7f32feed128a3ae7"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e43a5c63ad0bc8d178a745192671c06500f0dbd757c3f2eae65089aaf0d648982954ff60c3460a27748445525c6cd30701725e1697891cb7f32feed128a3ae7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbkOlxjrQvI0XinRRkmccBlAPDb11fD8u\nrmUImq8NZImClU/2DDRgondIRFUlxs0wcBcl4Wl4kct/Mv7tEoo65w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022024238e70b431b1a64efdf9032669939d4b77f249503fc6905feb7540dea3e6d2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ef4e8b5732f51a4b2547c6581381ccf750bb6d30a07cb758865414d9a45017fbf10247bcaa4ca73d5c9ad4c8a03a60a7f5cfa07fb57437b5a6f0a9bd381d78a5", "wx": "00ef4e8b5732f51a4b2547c6581381ccf750bb6d30a07cb758865414d9a45017fb", "wy": "00f10247bcaa4ca73d5c9ad4c8a03a60a7f5cfa07fb57437b5a6f0a9bd381d78a5"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ef4e8b5732f51a4b2547c6581381ccf750bb6d30a07cb758865414d9a45017fbf10247bcaa4ca73d5c9ad4c8a03a60a7f5cfa07fb57437b5a6f0a9bd381d78a5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE706LVzL1GkslR8ZYE4HM91C7bTCgfLdY\nhlQU2aRQF/vxAke8qkynPVya1MigOmCn9c+gf7V0N7Wm8Km9OB14pQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a973c15a44d2dcd50558e033d242155a29808b87491576566a83821b650e6f2dfc5ecd5482fa591f578308b09f2e704116a375ba1e2837912bae2972d340414d", "wx": "00a973c15a44d2dcd50558e033d242155a29808b87491576566a83821b650e6f2d", "wy": "00fc5ecd5482fa591f578308b09f2e704116a375ba1e2837912bae2972d340414d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a973c15a44d2dcd50558e033d242155a29808b87491576566a83821b650e6f2dfc5ecd5482fa591f578308b09f2e704116a375ba1e2837912bae2972d340414d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEqXPBWkTS3NUFWOAz0kIVWimAi4dJFXZW\naoOCG2UOby38Xs1UgvpZH1eDCLCfLnBBFqN1uh4oN5Errily00BBTQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048cd31f1656b21ec27276a533c35bf51d95490bfec57868a9b94433eda4579d61bb2c8e80c45d949bcaf6f0bbc76bc27c95939945052ad1a11014756556c6f978", "wx": "008cd31f1656b21ec27276a533c35bf51d95490bfec57868a9b94433eda4579d61", "wy": "00bb2c8e80c45d949bcaf6f0bbc76bc27c95939945052ad1a11014756556c6f978"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048cd31f1656b21ec27276a533c35bf51d95490bfec57868a9b94433eda4579d61bb2c8e80c45d949bcaf6f0bbc76bc27c95939945052ad1a11014756556c6f978", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjNMfFlayHsJydqUzw1v1HZVJC/7FeGip\nuUQz7aRXnWG7LI6AxF2Um8r28LvHa8J8lZOZRQUq0aEQFHVlVsb5eA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 301, "comment": "r is larger than n", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047f77dbb4e500dc9e405ebd9082afa9d0afb5c519fdce252910fcc9202895661cefce51d16a51700a672db8de2af070391a02da1c6a398b7dda94403a06db03d1", "wx": "7f77dbb4e500dc9e405ebd9082afa9d0afb5c519fdce252910fcc9202895661c", "wy": "00efce51d16a51700a672db8de2af070391a02da1c6a398b7dda94403a06db03d1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047f77dbb4e500dc9e405ebd9082afa9d0afb5c519fdce252910fcc9202895661cefce51d16a51700a672db8de2af070391a02da1c6a398b7dda94403a06db03d1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEf3fbtOUA3J5AXr2Qgq+p0K+1xRn9ziUp\nEPzJICiVZhzvzlHRalFwCmctuN4q8HA5GgLaHGo5i33alEA6BtsD0Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 302, "comment": "s is larger than n", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd04917c8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d4f41c9c4c15f02a199264a51266ed793952a7cea79125dcded805ed7a54c1350314fa927966b90b6c4e57cb521666fce4cb81b7e4d3550d729fe6dd6bbe5ab", "wx": "6d4f41c9c4c15f02a199264a51266ed793952a7cea79125dcded805ed7a54c13", "wy": "50314fa927966b90b6c4e57cb521666fce4cb81b7e4d3550d729fe6dd6bbe5ab"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046d4f41c9c4c15f02a199264a51266ed793952a7cea79125dcded805ed7a54c1350314fa927966b90b6c4e57cb521666fce4cb81b7e4d3550d729fe6dd6bbe5ab", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbU9BycTBXwKhmSZKUSZu15OVKnzqeRJd\nze2AXtelTBNQMU+pJ5ZrkLbE5Xy1IWZvzky4G35NNVDXKf5t1rvlqw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302702020101022100c58b162c58b162c58b162c58b162c58a1b242973853e16db75c8a1a71da4d39d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043bd4a602119bc50cfd05aa395c3c9f753b383bdd9539d27a1a143033fcfcaaa892d75438eba5af693196d4b7953184e2d649a0845d11af3c7d39e3b1f5449c19", "wx": "3bd4a602119bc50cfd05aa395c3c9f753b383bdd9539d27a1a143033fcfcaaa8", "wy": "0092d75438eba5af693196d4b7953184e2d649a0845d11af3c7d39e3b1f5449c19"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043bd4a602119bc50cfd05aa395c3c9f753b383bdd9539d27a1a143033fcfcaaa892d75438eba5af693196d4b7953184e2d649a0845d11af3c7d39e3b1f5449c19", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEO9SmAhGbxQz9Bao5XDyfdTs4O92VOdJ6\nGhQwM/z8qqiS11Q466WvaTGW1LeVMYTi1kmghF0Rrzx9OeOx9UScGQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302c02072d9b4d347952cc022100fcbc5103d0da267477d1791461cf2aa44bf9d43198f79507bd8779d69a13108e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047c981d6870575725427fc84ce6b5f706af6e8c62b4b5f7f72c3ee2860836996d29f07476cbf3f93a34e73f737658070642c66d0e34f5d56c715a26b099078413", "wx": "7c981d6870575725427fc84ce6b5f706af6e8c62b4b5f7f72c3ee2860836996d", "wy": "29f07476cbf3f93a34e73f737658070642c66d0e34f5d56c715a26b099078413"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047c981d6870575725427fc84ce6b5f706af6e8c62b4b5f7f72c3ee2860836996d29f07476cbf3f93a34e73f737658070642c66d0e34f5d56c715a26b099078413", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEfJgdaHBXVyVCf8hM5rX3Bq9ujGK0tff3\nLD7ihgg2mW0p8HR2y/P5OjTnP3N2WAcGQsZtDjT11WxxWiawmQeEEw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3032020d1033e67e37b32b445580bf4efc022100906f906f906f906f906f906f906f906ed8e426f7b1968c35a204236a579723d2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d75a78cf296b58aeb52faee6a9348385bcdc61f980da8ad6f28654d86fe516e20ce9952182f5f06cba50db8c65aa6f8cf1a32f2a46599c0a2abb4c1402cef467", "wx": "00d75a78cf296b58aeb52faee6a9348385bcdc61f980da8ad6f28654d86fe516e2", "wy": "0ce9952182f5f06cba50db8c65aa6f8cf1a32f2a46599c0a2abb4c1402cef467"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d75a78cf296b58aeb52faee6a9348385bcdc61f980da8ad6f28654d86fe516e20ce9952182f5f06cba50db8c65aa6f8cf1a32f2a46599c0a2abb4c1402cef467", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE11p4zylrWK61L67mqTSDhbzcYfmA2orW\n8oZU2G/lFuIM6ZUhgvXwbLpQ24xlqm+M8aMvKkZZnAoqu0wUAs70Zw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "3026020201010220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a35a42fb4057e11e332442d73729cdc684e7e0a7875ec933337e74ab1e17de62152e3a6558865d7f30a950c64e9f2e9d2f06c2703d2a1984a79445d3870a1cf", "wx": "0a35a42fb4057e11e332442d73729cdc684e7e0a7875ec933337e74ab1e17de6", "wy": "2152e3a6558865d7f30a950c64e9f2e9d2f06c2703d2a1984a79445d3870a1cf"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040a35a42fb4057e11e332442d73729cdc684e7e0a7875ec933337e74ab1e17de62152e3a6558865d7f30a950c64e9f2e9d2f06c2703d2a1984a79445d3870a1cf", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAECjWkL7QFfhHjMkQtc3Kc3GhOfgp4deyT\nMzfnSrHhfeYhUuOmVYhl1/MKlQxk6fLp0vBsJwPSoZhKeURdOHChzw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3031020d062522bbd3ecbe7c39e93e7c260220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04705e0c3ea1ca443a0105896e7af2b891a08243cca510cb5fffaebdd86ec6fc8c25d116fcf912e8246a64d5878436dfc958b59d4662a4b227a006876b5042fa58", "wx": "705e0c3ea1ca443a0105896e7af2b891a08243cca510cb5fffaebdd86ec6fc8c", "wy": "25d116fcf912e8246a64d5878436dfc958b59d4662a4b227a006876b5042fa58"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004705e0c3ea1ca443a0105896e7af2b891a08243cca510cb5fffaebdd86ec6fc8c25d116fcf912e8246a64d5878436dfc958b59d4662a4b227a006876b5042fa58", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEcF4MPqHKRDoBBYluevK4kaCCQ8ylEMtf\n/6692G7G/Iwl0Rb8+RLoJGpk1YeENt/JWLWdRmKksiegBodrUEL6WA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3045022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03640c1022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e322c7aad4a70024c4f80ea373e7e85f23dcbd11f186d55d5a744cd0f459f6db71d54db09ec66eeadbedbacfe2255bb87d0c1a737b3d3b1c7b76ce78d6342d7c", "wx": "00e322c7aad4a70024c4f80ea373e7e85f23dcbd11f186d55d5a744cd0f459f6db", "wy": "71d54db09ec66eeadbedbacfe2255bb87d0c1a737b3d3b1c7b76ce78d6342d7c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e322c7aad4a70024c4f80ea373e7e85f23dcbd11f186d55d5a744cd0f459f6db71d54db09ec66eeadbedbacfe2255bb87d0c1a737b3d3b1c7b76ce78d6342d7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE4yLHqtSnACTE+A6jc+foXyPcvRHxhtVd\nWnRM0PRZ9ttx1U2wnsZu6tvtus/iJVu4fQwac3s9Oxx7ds541jQtfA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "s == 1", "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020101", "result": "valid", "flags": []}, {"tcId": 310, "comment": "s == 0", "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044b242ee4a0a37835c590f6abe1af6668476c9c12c15b8aff776c7e7a8a452319b720cffae6423cf47aa375fe3b84346a83b09e0efa245eb89d99b2585451603d", "wx": "4b242ee4a0a37835c590f6abe1af6668476c9c12c15b8aff776c7e7a8a452319", "wy": "00b720cffae6423cf47aa375fe3b84346a83b09e0efa245eb89d99b2585451603d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044b242ee4a0a37835c590f6abe1af6668476c9c12c15b8aff776c7e7a8a452319b720cffae6423cf47aa375fe3b84346a83b09e0efa245eb89d99b2585451603d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESyQu5KCjeDXFkPar4a9maEdsnBLBW4r/\nd2x+eopFIxm3IM/65kI89Hqjdf47hDRqg7CeDvokXridmbJYVFFgPQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f9532aa189138b5e203f8f3a9acf03affa80794f37b647ac289267e8293ededc61ac8ac734bc4c7676bbbf57ead50b4981d9bceee0172e947c22c05f4424c9b2", "wx": "00f9532aa189138b5e203f8f3a9acf03affa80794f37b647ac289267e8293ededc", "wy": "61ac8ac734bc4c7676bbbf57ead50b4981d9bceee0172e947c22c05f4424c9b2"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f9532aa189138b5e203f8f3a9acf03affa80794f37b647ac289267e8293ededc61ac8ac734bc4c7676bbbf57ead50b4981d9bceee0172e947c22c05f4424c9b2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE+VMqoYkTi14gP486ms8Dr/qAeU83tkes\nKJJn6Ck+3txhrIrHNLxMdna7v1fq1QtJgdm87uAXLpR8IsBfRCTJsg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040f2256392bbc44714d5fd698b611b7140c3031845f14f8660baea5ec830088f5d5650dc0f784bd907f41b13936a2d13d0e05deb103efb069f8a771b527322155", "wx": "0f2256392bbc44714d5fd698b611b7140c3031845f14f8660baea5ec830088f5", "wy": "00d5650dc0f784bd907f41b13936a2d13d0e05deb103efb069f8a771b527322155"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040f2256392bbc44714d5fd698b611b7140c3031845f14f8660baea5ec830088f5d5650dc0f784bd907f41b13936a2d13d0e05deb103efb069f8a771b527322155", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEDyJWOSu8RHFNX9aYthG3FAwwMYRfFPhm\nC66l7IMAiPXVZQ3A94S9kH9BsTk2otE9DgXesQPvsGn4p3G1JzIhVQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04260b66d47b3a3be44364f1fbdd576b824893ce43c78e474db3c1b25106fb486503620b6068877f8b9018efe98191b24cf667053c09ca94da7bcf854bf6924332", "wx": "260b66d47b3a3be44364f1fbdd576b824893ce43c78e474db3c1b25106fb4865", "wy": "03620b6068877f8b9018efe98191b24cf667053c09ca94da7bcf854bf6924332"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004260b66d47b3a3be44364f1fbdd576b824893ce43c78e474db3c1b25106fb486503620b6068877f8b9018efe98191b24cf667053c09ca94da7bcf854bf6924332", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEJgtm1Hs6O+RDZPH73VdrgkiTzkPHjkdN\ns8GyUQb7SGUDYgtgaId/i5AY7+mBkbJM9mcFPAnKlNp7z4VL9pJDMg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "u1 == 1", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b802205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0430549bef5174962c5650944bbd7833220338e2e31f27775666f7d124d8ed7783f43ee6599a8458c9d786dd50cc676babf489757ade3e267d87bf2654a34adb20", "wx": "30549bef5174962c5650944bbd7833220338e2e31f27775666f7d124d8ed7783", "wy": "00f43ee6599a8458c9d786dd50cc676babf489757ade3e267d87bf2654a34adb20"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000430549bef5174962c5650944bbd7833220338e2e31f27775666f7d124d8ed7783f43ee6599a8458c9d786dd50cc676babf489757ade3e267d87bf2654a34adb20", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEMFSb71F0lixWUJRLvXgzIgM44uMfJ3dW\nZvfRJNjtd4P0PuZZmoRYydeG3VDMZ2ur9Il1et4+Jn2HvyZUo0rbIA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "3045022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022100a8ce483b42fb3461047c96ca00d1ab82c81e3d602cfdab62e059b19562bb03f3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0422283ca6f055439e8540454f63ff02e2e1141d10e34a54737599fae66266636dc8fef97c98fa1160f829b7c1326a069e0bb442428f1503e8cfbb616cd8118832", "wx": "22283ca6f055439e8540454f63ff02e2e1141d10e34a54737599fae66266636d", "wy": "00c8fef97c98fa1160f829b7c1326a069e0bb442428f1503e8cfbb616cd8118832"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000422283ca6f055439e8540454f63ff02e2e1141d10e34a54737599fae66266636dc8fef97c98fa1160f829b7c1326a069e0bb442428f1503e8cfbb616cd8118832", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEIig8pvBVQ56FQEVPY/8C4uEUHRDjSlRz\ndZn65mJmY23I/vl8mPoRYPgpt8EyagaeC7RCQo8VA+jPu2Fs2BGIMg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "u2 == 1", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04068f523d44cbb14a249394861c4f417c19dba72f74e1b123b4cbb89c74541b4144cd654d2b5871942e8d181f9e38f3946b3a73755a20e68ba555d56de6e290f4", "wx": "068f523d44cbb14a249394861c4f417c19dba72f74e1b123b4cbb89c74541b41", "wy": "44cd654d2b5871942e8d181f9e38f3946b3a73755a20e68ba555d56de6e290f4"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004068f523d44cbb14a249394861c4f417c19dba72f74e1b123b4cbb89c74541b4144cd654d2b5871942e8d181f9e38f3946b3a73755a20e68ba555d56de6e290f4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBo9SPUTLsUokk5SGHE9BfBnbpy904bEj\ntMu4nHRUG0FEzWVNK1hxlC6NGB+eOPOUazpzdVog5oulVdVt5uKQ9A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "3045022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022100aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9d1c9e899ca306ad27fe1945de0242b89", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04123670ccceb86a9d5fce24f070de8dfab093ee66047b17c1d7cca4734820daed76495f92804999f894c0184f72235b2db0a7d8ad077427b346d41f24eb2210a1", "wx": "123670ccceb86a9d5fce24f070de8dfab093ee66047b17c1d7cca4734820daed", "wy": "76495f92804999f894c0184f72235b2db0a7d8ad077427b346d41f24eb2210a1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004123670ccceb86a9d5fce24f070de8dfab093ee66047b17c1d7cca4734820daed76495f92804999f894c0184f72235b2db0a7d8ad077427b346d41f24eb2210a1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEEjZwzM64ap1fziTwcN6N+rCT7mYEexfB\n18ykc0gg2u12SV+SgEmZ+JTAGE9yI1stsKfYrQd0J7NG1B8k6yIQoQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02201d109296e9ac43dfa92bcdbcaa64c6d3fb858a822b6e519d9fd2e45279d3bf1a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f5ab53b565f170a3a83e61dc8cb5bb3a217398f0880db80c41da746d533993973d113d69a23e02aeb2e335b28b85490ace7df18279e2f4a7bd6f69c656fe6763", "wx": "00f5ab53b565f170a3a83e61dc8cb5bb3a217398f0880db80c41da746d53399397", "wy": "3d113d69a23e02aeb2e335b28b85490ace7df18279e2f4a7bd6f69c656fe6763"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f5ab53b565f170a3a83e61dc8cb5bb3a217398f0880db80c41da746d533993973d113d69a23e02aeb2e335b28b85490ace7df18279e2f4a7bd6f69c656fe6763", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE9atTtWXxcKOoPmHcjLW7OiFzmPCIDbgM\nQdp0bVM5k5c9ET1poj4CrrLjNbKLhUkKzn3xgnni9Ke9b2nGVv5nYw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203a8a53a9b98a2111e0c5e758a61f57822ead6ac1b9489d7b1bae29dc1dda7a87", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d0abcee886b680233390f1e6d5ce27056cbfec35ba9231725849a3714b06e8285bb11395652a85301cf5110d75d404a1f449ab2ac4767013fd586a9b58114006", "wx": "00d0abcee886b680233390f1e6d5ce27056cbfec35ba9231725849a3714b06e828", "wy": "5bb11395652a85301cf5110d75d404a1f449ab2ac4767013fd586a9b58114006"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d0abcee886b680233390f1e6d5ce27056cbfec35ba9231725849a3714b06e8285bb11395652a85301cf5110d75d404a1f449ab2ac4767013fd586a9b58114006", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE0KvO6Ia2gCMzkPHm1c4nBWy/7DW6kjFy\nWEmjcUsG6ChbsROVZSqFMBz1EQ111ASh9EmrKsR2cBP9WGqbWBFABg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100ac2416840b83e89e188d94463bd19cdc296fb2f891782dbd736b7241d371e890", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040f82392f4912bad8f4fcb151b290003174526a8cb27091d38c2aed163040698cdc34e9542d264ecffcd6339963804d68fc8a7376312b8a590d836e1ce1a9e637", "wx": "0f82392f4912bad8f4fcb151b290003174526a8cb27091d38c2aed163040698c", "wy": "00dc34e9542d264ecffcd6339963804d68fc8a7376312b8a590d836e1ce1a9e637"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040f82392f4912bad8f4fcb151b290003174526a8cb27091d38c2aed163040698cdc34e9542d264ecffcd6339963804d68fc8a7376312b8a590d836e1ce1a9e637", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAED4I5L0kSutj0/LFRspAAMXRSaoyycJHT\njCrtFjBAaYzcNOlULSZOz/zWM5ljgE1o/IpzdjErilkNg24c4anmNw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202ec9288261d8fbcda8ce483b42fb3460c908624c8869161e6b15d76e66ec5dff", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043b8497a00342aa0ca81408f40de05e938a151e6207a912bc35a13ab8ce8682d475d9d40f07fa88a7418e10d0f92bd10f646016be181c04af65e9ac1858f8e145", "wx": "3b8497a00342aa0ca81408f40de05e938a151e6207a912bc35a13ab8ce8682d4", "wy": "75d9d40f07fa88a7418e10d0f92bd10f646016be181c04af65e9ac1858f8e145"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043b8497a00342aa0ca81408f40de05e938a151e6207a912bc35a13ab8ce8682d475d9d40f07fa88a7418e10d0f92bd10f646016be181c04af65e9ac1858f8e145", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEO4SXoANCqgyoFAj0DeBek4oVHmIHqRK8\nNaE6uM6GgtR12dQPB/qIp0GOEND5K9EPZGAWvhgcBK9l6awYWPjhRQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0221009288261d8fbcda8ce483b42fb34610470f37567e692db81ce2caa2fe67594614", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e95914e5d692f4c30724c50a232d432a09664e1d485ecfc3a8299b7007b990b501a21060c529f3776a1df1b3828157dbcd432e84d3ac229585bc9234341788a8", "wx": "00e95914e5d692f4c30724c50a232d432a09664e1d485ecfc3a8299b7007b990b5", "wy": "01a21060c529f3776a1df1b3828157dbcd432e84d3ac229585bc9234341788a8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e95914e5d692f4c30724c50a232d432a09664e1d485ecfc3a8299b7007b990b501a21060c529f3776a1df1b3828157dbcd432e84d3ac229585bc9234341788a8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE6VkU5daS9MMHJMUKIy1DKglmTh1IXs/D\nqCmbcAe5kLUBohBgxSnzd2od8bOCgVfbzUMuhNOsIpWFvJI0NBeIqA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022025104c3b1f79b519c907685f668c208f63bfd0162312cffe05c2e76ffe7c4ae7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04af3e088449e97df3df478c59536965a18598122efc5bb20d23b9f5e41bc84e8a403177e836fa23bb3ba2b8fe6005c8d79e1392dc3b726dca4eca14e88c00fdfd", "wx": "00af3e088449e97df3df478c59536965a18598122efc5bb20d23b9f5e41bc84e8a", "wy": "403177e836fa23bb3ba2b8fe6005c8d79e1392dc3b726dca4eca14e88c00fdfd"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004af3e088449e97df3df478c59536965a18598122efc5bb20d23b9f5e41bc84e8a403177e836fa23bb3ba2b8fe6005c8d79e1392dc3b726dca4eca14e88c00fdfd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAErz4IhEnpffPfR4xZU2lloYWYEi78W7IN\nI7n15BvITopAMXfoNvojuzuiuP5gBcjXnhOS3DtybcpOyhTojAD9/Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022061d8fbcda8ce483b42fb3461047c96c9847a30c583c9b9f495591fa1e037b4fe", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04acaf208d26995e464ebcc54a683b04985c7be74448927e5c15332852886e6d748b182e2468f86cae75d045dc426383d2da3c7e3ab515580f3ff6523f03ce40dc", "wx": "00acaf208d26995e464ebcc54a683b04985c7be74448927e5c15332852886e6d74", "wy": "008b182e2468f86cae75d045dc426383d2da3c7e3ab515580f3ff6523f03ce40dc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004acaf208d26995e464ebcc54a683b04985c7be74448927e5c15332852886e6d748b182e2468f86cae75d045dc426383d2da3c7e3ab515580f3ff6523f03ce40dc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAErK8gjSaZXkZOvMVKaDsEmFx750RIkn5c\nFTMoUohubXSLGC4kaPhsrnXQRdxCY4PS2jx+OrUVWA8/9lI/A85A3A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100856739764fc50a930e4201325d77815bcf9b7681ed11213a053e816c5df8e6c6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047821e20d3938bbb48240ff48096e928e404ed91eefa37ea7cb2c8f339347b6ee6f7ada5c814f0f06eae9516a7848361cc3ac2eb4450a4455743d363f84f0dd1d", "wx": "7821e20d3938bbb48240ff48096e928e404ed91eefa37ea7cb2c8f339347b6ee", "wy": "6f7ada5c814f0f06eae9516a7848361cc3ac2eb4450a4455743d363f84f0dd1d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047821e20d3938bbb48240ff48096e928e404ed91eefa37ea7cb2c8f339347b6ee6f7ada5c814f0f06eae9516a7848361cc3ac2eb4450a4455743d363f84f0dd1d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFY<PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEeCHiDTk4u7SCQP9ICW6SjkBO2R7vo36n\nyyyPM5NHtu5vetpcgU8PBurpUWp4SDYcw6wutEUKRFV0PTY/hPDdHQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220512c9e1178d280d8464412f2bdf2dd9a7e8065b7ba9216f700779794c9a849bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049b0ef17c0bd3dea11be2c3358058a1e10b0283108bf79aaae34355c2329e84a0955f5cf7cb593ee756cf4c9f2f0a488a2993aeba923320bfab98c6f72e079d73", "wx": "009b0ef17c0bd3dea11be2c3358058a1e10b0283108bf79aaae34355c2329e84a0", "wy": "00955f5cf7cb593ee756cf4c9f2f0a488a2993aeba923320bfab98c6f72e079d73"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049b0ef17c0bd3dea11be2c3358058a1e10b0283108bf79aaae34355c2329e84a0955f5cf7cb593ee756cf4c9f2f0a488a2993aeba923320bfab98c6f72e079d73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEmw7xfAvT3qEb4sM1gFih4QsCgxCL95qq\n40NVwjKehKCVX1z3y1k+51bPTJ8vCkiKKZOuupIzIL+rmMb3Lgedcw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100c9e4be241bd52c69a2486b6d22291f502f64efab87e244d33cacce68672fd44b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044ba97363a7e14ce09480bd3b88491a7a501b5d4871b470498abc9a698c0699558ce9e198c1d48ec6650d59c15f9e1fb40dc0adccdea6329613e3a9a4decc80f8", "wx": "4ba97363a7e14ce09480bd3b88491a7a501b5d4871b470498abc9a698c069955", "wy": "008ce9e198c1d48ec6650d59c15f9e1fb40dc0adccdea6329613e3a9a4decc80f8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044ba97363a7e14ce09480bd3b88491a7a501b5d4871b470498abc9a698c0699558ce9e198c1d48ec6650d59c15f9e1fb40dc0adccdea6329613e3a9a4decc80f8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAES6lzY6fhTOCUgL07iEkaelAbXUhxtHBJ\niryaaYwGmVWM6eGYwdSOxmUNWcFfnh+0DcCtzN6mMpYT46mk3syA+A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100b0a10528cded5f2b80520ac549338c3f61bbb6f69877aa1b2fe9129c0ce717f2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040bad8d9f015770ed8ac654528717734214fff813809b5eb886f87c46d1bac68fc9134ebed0d79a82321cec4c77d5b91c1c7e3c34f6a69cc10140127421b87b92", "wx": "0bad8d9f015770ed8ac654528717734214fff813809b5eb886f87c46d1bac68f", "wy": "00c9134ebed0d79a82321cec4c77d5b91c1c7e3c34f6a69cc10140127421b87b92"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040bad8d9f015770ed8ac654528717734214fff813809b5eb886f87c46d1bac68fc9134ebed0d79a82321cec4c77d5b91c1c7e3c34f6a69cc10140127421b87b92", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEC62NnwFXcO2KxlRShxdzQhT/+BOAm164\nhvh8RtG6xo/JE06+0NeagjIc7Ex31bkcHH48NPamnMEBQBJ0Ibh7kg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100f177b6b48b29de102b6a1921aacd9c94bcec17a59991776cefe8ec63934c61b4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ab53f0d664c621138893fc5ee2b26ad2d686bbbb67eda1ee0dfb9609a3f5777afcf2d72bbd357bea8a1545fd4f162f3faf43bf74666cf23914c7e3d8dde79e97", "wx": "00ab53f0d664c621138893fc5ee2b26ad2d686bbbb67eda1ee0dfb9609a3f5777a", "wy": "00fcf2d72bbd357bea8a1545fd4f162f3faf43bf74666cf23914c7e3d8dde79e97"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ab53f0d664c621138893fc5ee2b26ad2d686bbbb67eda1ee0dfb9609a3f5777afcf2d72bbd357bea8a1545fd4f162f3faf43bf74666cf23914c7e3d8dde79e97", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEq1Pw1mTGIROIk/xe4rJq0taGu7tn7aHu\nDfuWCaP1d3r88tcrvTV76ooVRf1PFi8/r0O/dGZs8jkUx+PY3eeelw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100e2ef6d691653bc2056d43243559b392abf29526483da4e9e1fff7a3a56628227", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04418698cfe9d564e0e5d04a901c062042d864091573f2820f4592d40027dcfe2634909e2b92b3cbc595203553121ca46efdda2c23ca990e1e56137365c5a5b795", "wx": "418698cfe9d564e0e5d04a901c062042d864091573f2820f4592d40027dcfe26", "wy": "34909e2b92b3cbc595203553121ca46efdda2c23ca990e1e56137365c5a5b795"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004418698cfe9d564e0e5d04a901c062042d864091573f2820f4592d40027dcfe2634909e2b92b3cbc595203553121ca46efdda2c23ca990e1e56137365c5a5b795", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEQYaYz+nVZODl0EqQHAYgQthkCRVz8oIP\nRZLUACfc/iY0kJ4rkrPLxZUgNVMSHKRu/dosI8qZDh5WE3NlxaW3lQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100d467241da17d9a30823e4b650068d5c0c1668d236e2325cf501608111978a29a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dc0515e400e3527d2785e4a21d105af4cae862b31e07de117f11c9cd8dc9bc9b034eef9d96a56c0e74efa10a9f75e2a44d1337e8008175fbb40fe1c700144601", "wx": "00dc0515e400e3527d2785e4a21d105af4cae862b31e07de117f11c9cd8dc9bc9b", "wy": "034eef9d96a56c0e74efa10a9f75e2a44d1337e8008175fbb40fe1c700144601"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004dc0515e400e3527d2785e4a21d105af4cae862b31e07de117f11c9cd8dc9bc9b034eef9d96a56c0e74efa10a9f75e2a44d1337e8008175fbb40fe1c700144601", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE3AUV5ADjUn0nheSiHRBa9MroYrMeB94R\nfxHJzY3JvJsDTu+dlqVsDnTvoQqfdeKkTRM36ACBdfu0D+HHABRGAQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022056120b4205c1f44f0c46ca231de8ce6e14b7d97c48bc16deb9b5b920e9b8f448", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0453ff623b312669b48cc8a120b76a811e48a930548de8476d2c4607a5524ce592477ae28b239f626067a1d3dee97d769d37b41b184bae95009e401e443e930ef7", "wx": "53ff623b312669b48cc8a120b76a811e48a930548de8476d2c4607a5524ce592", "wy": "477ae28b239f626067a1d3dee97d769d37b41b184bae95009e401e443e930ef7"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000453ff623b312669b48cc8a120b76a811e48a930548de8476d2c4607a5524ce592477ae28b239f626067a1d3dee97d769d37b41b184bae95009e401e443e930ef7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEU/9iOzEmabSMyKEgt2qBHkipMFSN6Edt\nLEYHpVJM5ZJHeuKLI59iYGeh097pfXadN7QbGEuulQCeQB5EPpMO9w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100d55555555555555555555555555555547c74934474db157d2a8c3f088aced62a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d2d5348db9d837537c90e930ce35d4cd90e7d7a3460b1384790b632281b98ce843cc7b9a20c8734ac2c62a7d207105f5b2d85c2418939d35e3886f3893cb21b4", "wx": "00d2d5348db9d837537c90e930ce35d4cd90e7d7a3460b1384790b632281b98ce8", "wy": "43cc7b9a20c8734ac2c62a7d207105f5b2d85c2418939d35e3886f3893cb21b4"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d2d5348db9d837537c90e930ce35d4cd90e7d7a3460b1384790b632281b98ce843cc7b9a20c8734ac2c62a7d207105f5b2d85c2418939d35e3886f3893cb21b4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE0tU0jbnYN1N8kOkwzjXUzZDn16NGCxOE\neQtjIoG5jOhDzHuaIMhzSsLGKn0gcQX1sthcJBiTnTXjiG84k8shtA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100c1777c8853938e536213c02464a936000ba1e21c0fc62075d46c624e23b52f31", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c5fe4159e0b606879fc2a11088d658030ed7fef2e6711aab04869612fd09c3daac9dc7e198495afc0f43f4de434b8da233d8492cda28db460e8480aecb0a88f5", "wx": "00c5fe4159e0b606879fc2a11088d658030ed7fef2e6711aab04869612fd09c3da", "wy": "00ac9dc7e198495afc0f43f4de434b8da233d8492cda28db460e8480aecb0a88f5"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c5fe4159e0b606879fc2a11088d658030ed7fef2e6711aab04869612fd09c3daac9dc7e198495afc0f43f4de434b8da233d8492cda28db460e8480aecb0a88f5", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAExf5BWeC2BoefwqEQiNZYAw7X/vLmcRqr\nBIaWEv0Jw9qsncfhmEla/A9D9N5DS42iM9hJLNoo20YOhICuywqI9Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022030bbb794db588363b40679f6c182a50d3ce9679acdd3ffbe36d7813dacbdc818", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049a72b785c90a695b8e355f5d8fc151046c360d739136241c7fd1e77a0e8b8545a470b4b9a54d1d42956ac43b9c9f2f0f5489da16130b7ba1da38516c912009bc", "wx": "009a72b785c90a695b8e355f5d8fc151046c360d739136241c7fd1e77a0e8b8545", "wy": "00a470b4b9a54d1d42956ac43b9c9f2f0f5489da16130b7ba1da38516c912009bc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049a72b785c90a695b8e355f5d8fc151046c360d739136241c7fd1e77a0e8b8545a470b4b9a54d1d42956ac43b9c9f2f0f5489da16130b7ba1da38516c912009bc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEmnK3hckKaVuONV9dj8FRBGw2DXORNiQc\nf9Hneg6LhUWkcLS5pU0dQpVqxDucny8PVInaFhMLe6HaOFFskSAJvA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202c37fd995622c4fb7fffffffffffffffc7cee745110cb45ab558ed7c90c15a2f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0481e427bc8f0509b19a14c16e8883b12641d1d68e070c36ab49d1690e5decd061a993d77e9bc0f2b66edc6cd7ca8e32becf32596405622ea2756006deb3e8ac5f", "wx": "0081e427bc8f0509b19a14c16e8883b12641d1d68e070c36ab49d1690e5decd061", "wy": "00a993d77e9bc0f2b66edc6cd7ca8e32becf32596405622ea2756006deb3e8ac5f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000481e427bc8f0509b19a14c16e8883b12641d1d68e070c36ab49d1690e5decd061a993d77e9bc0f2b66edc6cd7ca8e32becf32596405622ea2756006deb3e8ac5f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEgeQnvI8FCbGaFMFuiIOxJkHR1o4HDDar\nSdFpDl3s0GGpk9d+m8Dytm7cbNfKjjK+zzJZZAViLqJ1YAbes+isXw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02207fd995622c4fb7ffffffffffffffffff5d883ffab5b32652ccdcaa290fccb97d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04756279b4827c83372130d4feab66a4397ed4463ac9ee1dc8adcaddcfcec59269b6323337d89af4208ad8818b67e26f9b8080316bc43fab53d1b3b7cea5db9947", "wx": "756279b4827c83372130d4feab66a4397ed4463ac9ee1dc8adcaddcfcec59269", "wy": "00b6323337d89af4208ad8818b67e26f9b8080316bc43fab53d1b3b7cea5db9947"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004756279b4827c83372130d4feab66a4397ed4463ac9ee1dc8adcaddcfcec59269b6323337d89af4208ad8818b67e26f9b8080316bc43fab53d1b3b7cea5db9947", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEdWJ5tIJ8gzchMNT+q2akOX7URjrJ7h3I\nrcrdz87Fkmm2MjM32Jr0IIrYgYtn4m+bgIAxa8Q/q1PRs7fOpduZRw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100ffb32ac4589f6ffffffffffffffffffebb107ff56b664ca599b954521f9972fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cf9345e850417aa81b01a941a02c5546950c27830841a435f4f3654927c6926d1ec53d04954a47f37915dddb48272fe733322d8250783991709b37d87fa296ef", "wx": "00cf9345e850417aa81b01a941a02c5546950c27830841a435f4f3654927c6926d", "wy": "1ec53d04954a47f37915dddb48272fe733322d8250783991709b37d87fa296ef"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004cf9345e850417aa81b01a941a02c5546950c27830841a435f4f3654927c6926d1ec53d04954a47f37915dddb48272fe733322d8250783991709b37d87fa296ef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEz5NF6FBBeqgbAalBoCxVRpUMJ4MIQaQ1\n9PNlSSfGkm0exT0ElUpH83kV3dtIJy/nMzItglB4OZFwmzfYf6KW7w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02205622c4fb7fffffffffffffffffffffff928a8f1c7ac7bec1808b9f61c01ec327", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f95f625795e6cc17b4c28b1ec643c36a34989084aa6a513812c3aa9bec0730312b22ce0eeeee9d45cee863c1b1d05381ac8b2c896a2cb17d3e9070d41d68bbea", "wx": "00f95f625795e6cc17b4c28b1ec643c36a34989084aa6a513812c3aa9bec073031", "wy": "2b22ce0eeeee9d45cee863c1b1d05381ac8b2c896a2cb17d3e9070d41d68bbea"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f95f625795e6cc17b4c28b1ec643c36a34989084aa6a513812c3aa9bec0730312b22ce0eeeee9d45cee863c1b1d05381ac8b2c896a2cb17d3e9070d41d68bbea", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE+V9iV5XmzBe0wosexkPDajSYkISqalE4\nEsOqm+wHMDErIs4O7u6dRc7oY8Gx0FOBrIssiWossX0+kHDUHWi76g==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022044104104104104104104104104104103b87853fd3b7d3f8e175125b4382f25ed", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c3f0aadef8675dc8832a29b397488d6a4fb54780e5967e8b43449498c16ad4bdcb391b545464668d4d0a80b8e283132448a3c0be0abed304cf0839b5920f3867", "wx": "00c3f0aadef8675dc8832a29b397488d6a4fb54780e5967e8b43449498c16ad4bd", "wy": "00cb391b545464668d4d0a80b8e283132448a3c0be0abed304cf0839b5920f3867"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c3f0aadef8675dc8832a29b397488d6a4fb54780e5967e8b43449498c16ad4bdcb391b545464668d4d0a80b8e283132448a3c0be0abed304cf0839b5920f3867", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEw/Cq3vhnXciDKimzl0iNak+1R4Dlln6L\nQ0SUmMFq1L3LORtUVGRmjU0KgLjigxMkSKPAvgq+0wTPCDm1kg84Zw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202739ce739ce739ce739ce739ce739ce705560298d1f2f08dc419ac273a5b54d9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0476b920709a9e5dc54a91bd4772ab2593a76f38841dae2880f547c3bb753ae7c15f01e6779d5e3aba75997bcf7e3f320868ba8f0bc1210ab80b42760a6a701206", "wx": "76b920709a9e5dc54a91bd4772ab2593a76f38841dae2880f547c3bb753ae7c1", "wy": "5f01e6779d5e3aba75997bcf7e3f320868ba8f0bc1210ab80b42760a6a701206"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000476b920709a9e5dc54a91bd4772ab2593a76f38841dae2880f547c3bb753ae7c15f01e6779d5e3aba75997bcf7e3f320868ba8f0bc1210ab80b42760a6a701206", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEdrkgcJqeXcVKkb1Hcqslk6dvOIQdriiA\n9UfDu3U658FfAeZ3nV46unWZe89+PzIIaLqPC8EhCrgLQnYKanASBg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100b777777777777777777777777777777688e6a1fe808a97a348671222ff16b863", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e3895147f4e36a13c3483ac00c88a78a8ffa42478afc2e9d8386205b0b1df8b2b4156b56ba217b1ca08bd77f819abb52d742f6b2f7d61353e4cc5663da487317", "wx": "00e3895147f4e36a13c3483ac00c88a78a8ffa42478afc2e9d8386205b0b1df8b2", "wy": "00b4156b56ba217b1ca08bd77f819abb52d742f6b2f7d61353e4cc5663da487317"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e3895147f4e36a13c3483ac00c88a78a8ffa42478afc2e9d8386205b0b1df8b2b4156b56ba217b1ca08bd77f819abb52d742f6b2f7d61353e4cc5663da487317", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE44lRR/TjahPDSDrADIinio/6QkeK/C6d\ng4YgWwsd+LK0FWtWuiF7HKCL13+BmrtS10L2svfWE1PkzFZj2khzFw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206492492492492492492492492492492406dd3a19b8d5fb875235963c593bd2d3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e733999ce348cf7b363dcf931953cf1c247c3a887408c064b9791c178ad350290b0849329da7008e6a2d00142883f8041b9917528fcc4c5bd3f795accff28eb6", "wx": "00e733999ce348cf7b363dcf931953cf1c247c3a887408c064b9791c178ad35029", "wy": "0b0849329da7008e6a2d00142883f8041b9917528fcc4c5bd3f795accff28eb6"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e733999ce348cf7b363dcf931953cf1c247c3a887408c064b9791c178ad350290b0849329da7008e6a2d00142883f8041b9917528fcc4c5bd3f795accff28eb6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE5zOZnONIz3s2Pc+TGVPPHCR8Ooh0CMBk\nuXkcF4rTUCkLCEkynacAjmotABQog/gEG5kXUo/MTFvT95Wsz/KOtg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100955555555555555555555555555555547c74934474db157d2a8c3f088aced62c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04069b66f716902cbd51dadff61644ac74c6a35e8c776ea22c9c3492d1d3faa2ece4905cc480bc967ce389b82c8e6692b159d3fe9a268bfc12010993934d7e24dd", "wx": "069b66f716902cbd51dadff61644ac74c6a35e8c776ea22c9c3492d1d3faa2ec", "wy": "00e4905cc480bc967ce389b82c8e6692b159d3fe9a268bfc12010993934d7e24dd"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004069b66f716902cbd51dadff61644ac74c6a35e8c776ea22c9c3492d1d3faa2ece4905cc480bc967ce389b82c8e6692b159d3fe9a268bfc12010993934d7e24dd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBptm9xaQLL1R2t/2FkSsdMajXox3bqIs\nnDSS0dP6ouzkkFzEgLyWfOOJuCyOZpKxWdP+miaL/BIBCZOTTX4k3Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b17", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049543bfd3a0b678654fc65458e3e62269b30bbd2a40282d92058c3311a61bd885333d78221d9aa0a9663a5df5123d95c3ff4a02606278666179e33c94fe1e0cd1", "wx": "009543bfd3a0b678654fc65458e3e62269b30bbd2a40282d92058c3311a61bd885", "wy": "333d78221d9aa0a9663a5df5123d95c3ff4a02606278666179e33c94fe1e0cd1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049543bfd3a0b678654fc65458e3e62269b30bbd2a40282d92058c3311a61bd885333d78221d9aa0a9663a5df5123d95c3ff4a02606278666179e33c94fe1e0cd1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFY<PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAElUO/06C2eGVPxlRY4+YiabMLvSpAKC2S\nBYwzEaYb2IUzPXgiHZqgqWY6XfUSPZXD/0oCYGJ4ZmF54zyU/h4M0Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100bffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364143", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a6884e6218642518a211f67b03aef6a84d3b32d18eea445b31913e8a1a00f4c531a318166cfcbce34307572eb823edc5d0334c5e5373af4e832e730047996aca", "wx": "00a6884e6218642518a211f67b03aef6a84d3b32d18eea445b31913e8a1a00f4c5", "wy": "31a318166cfcbce34307572eb823edc5d0334c5e5373af4e832e730047996aca"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a6884e6218642518a211f67b03aef6a84d3b32d18eea445b31913e8a1a00f4c531a318166cfcbce34307572eb823edc5d0334c5e5373af4e832e730047996aca", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEpohOYhhkJRiiEfZ7A672qE07MtGO6kRb\nMZE+ihoA9MUxoxgWbPy840MHVy64I+3F0DNMXlNzr06DLnMAR5lqyg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220185ddbca6dac41b1da033cfb60c152869e74b3cd66e9ffdf1b6bc09ed65ee40c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2dc66469bb8e8e04186e81a2b693cc2121ef22cb61803a2b4ebe1a3e0d367b295d", "wx": "00bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2d", "wy": "00c66469bb8e8e04186e81a2b693cc2121ef22cb61803a2b4ebe1a3e0d367b295d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2dc66469bb8e8e04186e81a2b693cc2121ef22cb61803a2b4ebe1a3e0d367b295d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEvUxvmrNjogT9Gr4PcVi4RBfMouDTVSd9\n3BfKwiq9vC3GZGm7jo4EGG6BoraTzCEh7yLLYYA6K06+Gj4NNnspXQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "point duplication during verification", "msg": "313233343030", "sig": "3045022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022100eaafe4ce77ccd9137f39edc5370d26b73f4dc6ceadfb40a488b2dc6c93f1993c", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2d399b96447171fbe7917e5d496c33dede10dd349e7fc5d4b141e5c1f1c984d2d2", "wx": "00bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2d", "wy": "399b96447171fbe7917e5d496c33dede10dd349e7fc5d4b141e5c1f1c984d2d2"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004bd4c6f9ab363a204fd1abe0f7158b84417cca2e0d355277ddc17cac22abdbc2d399b96447171fbe7917e5d496c33dede10dd349e7fc5d4b141e5c1f1c984d2d2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEvUxvmrNjogT9Gr4PcVi4RBfMouDTVSd9\n3BfKwiq9vC05m5ZEcXH755F+XUlsM97eEN00nn/F1LFB5cHxyYTS0g==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "duplication bug", "msg": "313233343030", "sig": "3045022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022100eaafe4ce77ccd9137f39edc5370d26b73f4dc6ceadfb40a488b2dc6c93f1993c", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e1815bb1653b8146a2e9160fb0e946112b8994b9d90ef8a36a8ef2ba187b705d11b344caed87db94b9c9eab8a5e3277a9aa46b31768cee5406c3cbcffce0a945", "wx": "00e1815bb1653b8146a2e9160fb0e946112b8994b9d90ef8a36a8ef2ba187b705d", "wy": "11b344caed87db94b9c9eab8a5e3277a9aa46b31768cee5406c3cbcffce0a945"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e1815bb1653b8146a2e9160fb0e946112b8994b9d90ef8a36a8ef2ba187b705d11b344caed87db94b9c9eab8a5e3277a9aa46b31768cee5406c3cbcffce0a945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE4YFbsWU7gUai6RYPsOlGESuJlLnZDvij\nao7yuhh7cF0Rs0TK7YfblLnJ6ril4yd6mqRrMXaM7lQGw8vP/OCpRQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0022033333333333333333333333333333332f222f8faefdb533f265d461c29a47373", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042be9265c148fc61379ca147e651e7f0a1c602cdd66f70b4b6ada2e83f56c1a71f5e1ede0139baa93af588cc7ec1b479b91d230c811575cb143af12c631d16a61", "wx": "2be9265c148fc61379ca147e651e7f0a1c602cdd66f70b4b6ada2e83f56c1a71", "wy": "00f5e1ede0139baa93af588cc7ec1b479b91d230c811575cb143af12c631d16a61"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200042be9265c148fc61379ca147e651e7f0a1c602cdd66f70b4b6ada2e83f56c1a71f5e1ede0139baa93af588cc7ec1b479b91d230c811575cb143af12c631d16a61", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEK+kmXBSPxhN5yhR+ZR5/ChxgLN1m9wtL\natoug/VsGnH14e3gE5uqk69YjMfsG0ebkdIwyBFXXLFDrxLGMdFqYQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04af3b3f73a409ffa51b10f3cdfa272d9b42358ca9aed2840bfaf5bd67e61fd1c49d07371ca919a069e46c473e6e45b2f2cd019fa21f84d0abfa285be5513781fb", "wx": "00af3b3f73a409ffa51b10f3cdfa272d9b42358ca9aed2840bfaf5bd67e61fd1c4", "wy": "009d07371ca919a069e46c473e6e45b2f2cd019fa21f84d0abfa285be5513781fb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004af3b3f73a409ffa51b10f3cdfa272d9b42358ca9aed2840bfaf5bd67e61fd1c49d07371ca919a069e46c473e6e45b2f2cd019fa21f84d0abfa285be5513781fb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAErzs/c6QJ/6UbEPPN+ictm0I1jKmu0oQL\n+vW9Z+Yf0cSdBzccqRmgaeRsRz5uRbLyzQGfoh+E0Kv6KFvlUTeB+w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3046022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022100b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e155240c3be314924ed787354325fdc3dcfe46d603798f2491152448e0e413b6ce1124313eb0048292f6edf9f248ff9624936e41be6c93dce2df9ab7997289fc", "wx": "00e155240c3be314924ed787354325fdc3dcfe46d603798f2491152448e0e413b6", "wy": "00ce1124313eb0048292f6edf9f248ff9624936e41be6c93dce2df9ab7997289fc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e155240c3be314924ed787354325fdc3dcfe46d603798f2491152448e0e413b6ce1124313eb0048292f6edf9f248ff9624936e41be6c93dce2df9ab7997289fc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE4VUkDDvjFJJO14c1QyX9w9z+RtYDeY8k\nkRUkSODkE7bOESQxPrAEgpL27fnySP+WJJNuQb5sk9zi35q3mXKJ/A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3046022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee502210099999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0487d4de4ed890da42d7e11a95e56070c95901500c53dd55b62952679884d2598ddf8a37ce6d8f86f4e8b3580d6e6a448520cb740888a3b0eac92bc9a2f1589b4e", "wx": "0087d4de4ed890da42d7e11a95e56070c95901500c53dd55b62952679884d2598d", "wy": "00df8a37ce6d8f86f4e8b3580d6e6a448520cb740888a3b0eac92bc9a2f1589b4e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000487d4de4ed890da42d7e11a95e56070c95901500c53dd55b62952679884d2598ddf8a37ce6d8f86f4e8b3580d6e6a448520cb740888a3b0eac92bc9a2f1589b4e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEh9TeTtiQ2kLX4RqV5WBwyVkBUAxT3VW2\nKVJnmITSWY3fijfObY+G9OizWA1uakSFIMt0CIijsOrJK8mi8VibTg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048c03d72664214f3bdaa6a2e1003b14864000e5993b41b71b68cdebc4a08f628a4a490efc9172983bec203e6096dd9778bec26f6e443e1dde67060dac18ca2440", "wx": "008c03d72664214f3bdaa6a2e1003b14864000e5993b41b71b68cdebc4a08f628a", "wy": "4a490efc9172983bec203e6096dd9778bec26f6e443e1dde67060dac18ca2440"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048c03d72664214f3bdaa6a2e1003b14864000e5993b41b71b68cdebc4a08f628a4a490efc9172983bec203e6096dd9778bec26f6e443e1dde67060dac18ca2440", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjAPXJmQhTzvapqLhADsUhkAA5Zk7Qbcb\naM3rxKCPYopKSQ78kXKYO+wgPmCW3Zd4vsJvbkQ+Hd5nBg2sGMokQA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041ae8bf7b21b3ae00fd01d19b4f72ae6b47bf752edf476cc5cdfa1c2345588e7154dc306165f4f907802478ed2aed41ec54ddf870bc62c2c373971194308411f0", "wx": "1ae8bf7b21b3ae00fd01d19b4f72ae6b47bf752edf476cc5cdfa1c2345588e71", "wy": "54dc306165f4f907802478ed2aed41ec54ddf870bc62c2c373971194308411f0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041ae8bf7b21b3ae00fd01d19b4f72ae6b47bf752edf476cc5cdfa1c2345588e7154dc306165f4f907802478ed2aed41ec54ddf870bc62c2c373971194308411f0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGui/eyGzrgD9AdGbT3Kua0e/dS7fR2zF\nzfocI0VYjnFU3DBhZfT5B4AkeO0q7UHsVN34cLxiwsNzlxGUMIQR8A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "extreme value for k", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee502200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c5dad21249273cd72ad06943b4e3be0822595bf9fa0459223d27354dea24179b97340abb326afd1eb6de5e525a23aad4929f8a09244c972841a0cb76680ff060", "wx": "00c5dad21249273cd72ad06943b4e3be0822595bf9fa0459223d27354dea24179b", "wy": "0097340abb326afd1eb6de5e525a23aad4929f8a09244c972841a0cb76680ff060"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c5dad21249273cd72ad06943b4e3be0822595bf9fa0459223d27354dea24179b97340abb326afd1eb6de5e525a23aad4929f8a09244c972841a0cb76680ff060", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAExdrSEkknPNcq0GlDtOO+CCJZW/n6BFki\nPSc1TeokF5uXNAq7Mmr9HrbeXlJaI6rUkp+KCSRMlyhBoMt2aA/wYA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f2c6643bf373a0812f993cd616993551d7bc7826d3d6bed0918ed4998b74e837d7160a452dd2c8d3e5f4f80a1efbc33793c35d6e243e9dfe9a39e26dfb7a1b9f", "wx": "00f2c6643bf373a0812f993cd616993551d7bc7826d3d6bed0918ed4998b74e837", "wy": "00d7160a452dd2c8d3e5f4f80a1efbc33793c35d6e243e9dfe9a39e26dfb7a1b9f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f2c6643bf373a0812f993cd616993551d7bc7826d3d6bed0918ed4998b74e837d7160a452dd2c8d3e5f4f80a1efbc33793c35d6e243e9dfe9a39e26dfb7a1b9f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE8sZkO/NzoIEvmTzWFpk1Ude8eCbT1r7Q\nkY7UmYt06DfXFgpFLdLI0+X0+Aoe+8M3k8NdbiQ+nf6aOeJt+3obnw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022100b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043a1bd608d3187c684d8d461a5406e2b86b09eedc5d2dd28fcc341bd2d483a6d85e3ab9d9e79ecb7e43135782ae60b12ff69b3349c1819b4ab27b738c7f803595", "wx": "3a1bd608d3187c684d8d461a5406e2b86b09eedc5d2dd28fcc341bd2d483a6d8", "wy": "5e3ab9d9e79ecb7e43135782ae60b12ff69b3349c1819b4ab27b738c7f803595"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043a1bd608d3187c684d8d461a5406e2b86b09eedc5d2dd28fcc341bd2d483a6d85e3ab9d9e79ecb7e43135782ae60b12ff69b3349c1819b4ab27b738c7f803595", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEOhvWCNMYfGhNjUYaVAbiuGsJ7txdLdKP\nzDQb0tSDptheOrnZ557LfkMTV4KuYLEv9pszScGBm0qye3OMf4A1lQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179802210099999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04aee2e5aa96d31bde8b0ec1e71d79e721c5fb094eba49d61dfba6e636a77b215aaf3534fa210143ce3cecc5bfe1e0b136ab6811d662376637efe1eddd212b6ff0", "wx": "00aee2e5aa96d31bde8b0ec1e71d79e721c5fb094eba49d61dfba6e636a77b215a", "wy": "00af3534fa210143ce3cecc5bfe1e0b136ab6811d662376637efe1eddd212b6ff0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004aee2e5aa96d31bde8b0ec1e71d79e721c5fb094eba49d61dfba6e636a77b215aaf3534fa210143ce3cecc5bfe1e0b136ab6811d662376637efe1eddd212b6ff0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEruLlqpbTG96LDsHnHXnnIcX7CU66SdYd\n+6bmNqd7IVqvNTT6IQFDzjzsxb/h4LE2q2gR1mI3Zjfv4e3dIStv8A==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04db0dc63f6dfff9b2564498a2423449cc5d894222ddda86eabd6d2bb2549d28d75b5bc20153ef6a2649dc6f116e6ca5c916740a9a645618003a5a448eee928fcc", "wx": "00db0dc63f6dfff9b2564498a2423449cc5d894222ddda86eabd6d2bb2549d28d7", "wy": "5b5bc20153ef6a2649dc6f116e6ca5c916740a9a645618003a5a448eee928fcc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004db0dc63f6dfff9b2564498a2423449cc5d894222ddda86eabd6d2bb2549d28d75b5bc20153ef6a2649dc6f116e6ca5c916740a9a645618003a5a448eee928fcc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE2w3GP23/+bJWRJiiQjRJzF2JQiLd2obq\nvW0rslSdKNdbW8IBU+9qJkncbxFubKXJFnQKmmRWGAA6WkSO7pKPzA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0482a004a2ff4aa7c2fd4c71bc88a4ee16d75c11f5ad8599a6eb41ea73e49f80bcf360abc795b4b21b46584a1bebc41720df51a25044880f287c5e5d83f83c1d20", "wx": "0082a004a2ff4aa7c2fd4c71bc88a4ee16d75c11f5ad8599a6eb41ea73e49f80bc", "wy": "00f360abc795b4b21b46584a1bebc41720df51a25044880f287c5e5d83f83c1d20"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000482a004a2ff4aa7c2fd4c71bc88a4ee16d75c11f5ad8599a6eb41ea73e49f80bcf360abc795b4b21b46584a1bebc41720df51a25044880f287c5e5d83f83c1d20", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEgqAEov9Kp8L9THG8iKTuFtdcEfWthZmm\n60Hqc+SfgLzzYKvHlbSyG0ZYShvrxBcg31GiUESIDyh8Xl2D+DwdIA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "extreme value for k", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179802200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5hIOtp3JqPEZV2k+/wOEQio/Re0SKaFVBmcR9CP+xDUuA==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "testing point duplication", "msg": "313233343030", "sig": "304402205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 364, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100a8ce483b42fb3461047c96ca00d1ab82c81e3d602cfdab62e059b19562bb03f302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "00b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5i3xSWI2Vw7mqJbBAPx7vdXAuhLt1l6q+ZjuC9vBO8ndw==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "testing point duplication", "msg": "313233343030", "sig": "304402205731b7c4bd04cb9efb836935ff2e547bf2909f86824af4d8df78acf76d7b3d4e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 366, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100a8ce483b42fb3461047c96ca00d1ab82c81e3d602cfdab62e059b19562bb03f302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "wx": "782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963", "wy": "00af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeCyO0X47Kng7VGTzOwllKnHGeOBexR6E\n4rz8Zjo96WOvmstCgLjH98QvTvmrpiRewewXEv04oPqWQY2M1qphUg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "pseudorandom signature", "msg": "", "sig": "3046022100fc0f737a79d525eefe3c940c162173cc6fd9a6d5cc5017754026c4113d0f15cc022100894d6fb59cc79199b89cf12b556ba49f8623b66da8c11a55e267e3318497688c", "result": "valid", "flags": []}, {"tcId": 368, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3043022076bae33ffa376b496bde93c7748d50a3a8b73bac045e54c40c7fcd344a10fa83021f3e25a20716a902d524d656ead090b7bbe1ac25ff71269d7038d4b08db5b1d7", "result": "valid", "flags": []}, {"tcId": 369, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "30450220016e2dfac600c8c994c0bb815b1072bb5bb680774121d342f93fe0a994f72c09022100c378944de05aaca70c71ed9a7fe4eed2b36ab3ddb4b32d09d53eebd91f2f9217", "result": "valid", "flags": []}, {"tcId": 370, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "3045022100a33c4acb033f3d0d50d244249a1277448b6a52f524e30f4b73d595fb955e924702207f31b50c698a971c8fab98521ef3a1d6fa483a676230467c8af3018452bf1de1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "01060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv8AAAABBgSS1aVnPg8l2NUPt+WMSdhtRtQhaVXgqj1A4Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30450220091bc829be861c20c4bb877f0da205b3911584708ddeef580ae46691b245b99d022100c03bb5e77a8fad94736775f31ae381015a93973954b2f3e541457fcb05bccb5f", "result": "valid", "flags": []}, {"tcId": 372, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3046022100fd6a7eec40d1062b9a4a7af4817b3ea8cd21596d6dc228b287a21b647caab29f022100ab861672dfe3b428c26e08f2f7ca464ad3e966bbf62931408ed1ce2735bab62b", "result": "valid", "flags": []}, {"tcId": 373, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3045022053ebb6debd028f195c039ef4e04276adfa2d9551a6e02d2c4143907ec889e6d0022100fa01a27240dd63aff235cd9778c90a7c25c993791cda584fdcca1a979f5faf54", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "00fffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv/////++fttKlqYwfDaJyrwSBpztieSuSvelqoeVcK7Tg==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3044022060ec4f23f1b2c0b5acae075bbf09be76ffc978aa4d354d309746047a69c43ddd0220798c3df3ada3c91845272b9573e70e683d4e49d90a51f6ad047e24da19355d3b", "result": "valid", "flags": []}, {"tcId": 375, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502204457c32fe6bf74ee82ff8a38b8076b48323769f3b7970f419352283984dde081022100c6380b3ed90ddba62394c19e02a3b8690d1615dd1120c0fe67b86e7961b8e7d5", "result": "valid", "flags": []}, {"tcId": 376, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022024820a985bc72c8817ffdec275db7406ed5b897fff0b713d98a721a42bb4c6d402210094f1397d1e577fd47cfed7ac01f2aead6888863a3f8ff21f00c34c41e840af99", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "wx": "013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0", "wy": "00f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAAAAAT/SIkjWTZX3PCm0irSGMYUL5QP9\nAPhGi18PcOD27nqkO8LG/SWx2CaSQcvdnbsNrJbcliMfQwcF+DhxfQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402201ed4e5132e4b11268ad55b9a4b7a54ad3e028976bbe85fef2e8cd0a3e4362c7002201d1ce94fd8ffda6df3c307150a98719f276381b0c9d261fba7feba37b402394b", "result": "valid", "flags": []}, {"tcId": 378, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402204f030196e9a558b5af5557c7347d132b1308b3a1ce88a6bc6bf566ed22b5da780220392ddc6e83f995a0030856ecd0822449d8dac2bead6d269ef4b307d535dce238", "result": "valid", "flags": []}, {"tcId": 379, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30450221009eaa256762ee3d5d3ed269a2907c4a836c92073918be335469e25743ea9ba0e102202c70e1dbee671e9bbd6b68695ae40d58d11ce82592cf9824914a1d8d9e429fcc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "wx": "25afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dffffffff", "wy": "00fa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEJa/WiayrrtZ8Hylt5ZQG+MVQ9XFGoLTs\nLJeHbf/////6RqduUgMi37xJHsTwzBl0IPxOpYg9j23VPDVLxPZ8NQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100db965e2d0d25c84c30ae8a3e31f12b55b8784b90f91d443a70f2c7cb4828f5bf022100aabb284a7715095cb11714ec76779c08ad5496d8870e2109467a21093f0b8bca", "result": "valid", "flags": []}, {"tcId": 381, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3044022058675835add3dd65f25c76b02545176c37a840748fb64a16b8bb113e361cf55d02203b1e25552a5c35732f33735f4dc6f50f947bbecb734599a987f1ffbf86b2842d", "result": "valid", "flags": []}, {"tcId": 382, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30440220786a687776da9c185afa16f90a596f5ddce3c2d3caece0344101be24581b86e1022075b13da23be046d551c68b54e72a990288dd73099800705e1a854366662b950e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "wx": "00d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb9", "wy": "3f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAE0S5sZrZ3NMPITSYBz1013Al+J2N/CspK\nT9t0tqrdO7k/W9/4i9VzbfiY5pkAbtdQ8RzwfFhmzXrXDHEh/////w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022100cfce7188667568bd7d5269a75bef42aa360705db5232d189adcf2323036852bb02205d06871c28d89198870f94264ae11744d254682e06f154332f976b803da8a1a2", "result": "valid", "flags": []}, {"tcId": 384, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100b21cc81843c74779fc5ba9fe1b0d5e7173f696c6e91398cf83a31bc735b6050b0221008945e8711789093c80fe6cec3947cc9c36ffe2505f1ef721bb507e05c9c07bd2", "result": "valid", "flags": []}, {"tcId": 385, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100fbc5087d1e6bbc32dae22a837d03151028ac69ad71e66e5fc841de0548c06dce022100e2dfa5e56de28d72d0e770e7666033c42431bcae1fc6cffd9593d54cbcfcfa7c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "wx": "6d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000", "wy": "00e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbUp/YNR3Sk8KqLve25U8fup5CUB+MWR1\nVmS8KAAAAADmWdNOTfONnoyeqt+6NmEsdpGVvobHeqw/NueLU4aA+w==\n-----END PUBLIC KEY-----", "sha": "SHA3-256", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304502205ea780b73ce027c03ff81e1b26e61076c8944a835d349cd757ece0c4ddf1da24022100bd9b87db26158d5b9132bb0f3df54a2ab6c9ae9a4e0b8496a539ab4ab588ccba", "result": "valid", "flags": []}, {"tcId": 387, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304402204618f1a11cf8cbc1966416785c3149f75a71ae256d445deb31008d51ba6088c20220408087725dd6ce18bfb7493a5460b54022245e5dbd731ed6d35db88a51d2ba6e", "result": "valid", "flags": []}, {"tcId": 388, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30450221009d9cdb94e5e9a66bf8eedfdf9f1af43713bb05d880dec89aec21631958970de60220732932649bea35f11dfe0926618e4f091c1b264ca128a9eef14e6d94d7c9f207", "result": "valid", "flags": []}]}]}