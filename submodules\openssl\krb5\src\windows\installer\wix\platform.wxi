<?xml version="1.0"?>
<Include>
<!-- Platform specific GUID's and other definitions -->
  <!-- Note that we need 32 bit dlls for both 32 and 64 bit platforms -->
  <?define cmf_comerr32_dll_guid="D8F455F9-E648-4C61-A69D-7116ADEC2DBB"?>
  <?define cmf_comerr32_dll_name="comerr32.dll"?>
  <?define cmf_gssapi32_dll_guid="5B0F2989-BB85-40BF-BB7A-E77693972CF9"?>
  <?define cmf_gssapi32_dll_name="gssapi32.dll"?>
  <?define cmf_krb5_32_dll_guid="31E40356-CBAC-4FC6-9A34-C6F6C72A27CA"?>
  <?define cmf_krb5_32_dll_name="krb5_32.dll"?>
  <?define cmf_k5sprt32_dll_guid="********-9201-4c02-866F-2038676771D4"?>
  <?define cmf_k5sprt32_dll_name="k5sprt32.dll"?>
  <?define cmf_krbcc32_dll_guid="A50FA27D-F203-4C19-9047-B7976171FB94"?>
  <?define cmf_krbcc32_dll_name="krbcc32.dll"?>
  <?define cmf_leashw32_dll_guid="8C145D48-A2FC-4C28-BC05-4368545F1184"?>
  <?define cmf_leashw32_dll_name="leashw32.dll"?>
  <?define cmf_xpprof32_dll_guid="A7DF8BAF-7188-4C24-89FB-C8EB51571FD2"?>
  <?define cmf_xpprof32_dll_name="xpprof32.dll"?>
  <?define cmf_spake32_dll_guid="36A1695B-A2B4-4A93-8C35-733A121923D4"?>
  <?define cmf_spake32_dll_name="spake32.dll"?>
  <?if $(sys.BUILDARCH) = "x64" ?>
        <?define UpgradeCode="6DA9CD86-6028-4852-8C94-452CAC229244"?>
        <?define PISystemFolder="System64Folder"?>
        <?define PIProgramFilesFolder="ProgramFiles64Folder"?>

        <?define cmf_kfwlogon_DLL_guid="CFC0B7C9-9A59-4022-89B3-25E0941D0369"?>
        <?define cmf_kfwcpcc_EXE_guid="83F51B6D-F3C6-44cf-AC91-D1D6498FFA44"?>
        <?define cmp_ClientSystemDebug_guid="7B25B1E0-A22A-43c7-BA97-C30FCA3B1230"?>
        <?define rcm_krb5_1_guid="21883AD6-1C00-4f1d-9922-477CF63CE6BF"?>
        <?define rcm_krb5_2_guid="88CEA446-4617-46bc-916F-2AAA1E1EACF8"?>
        <?define rcm_krb5_3_guid="28BFF4DB-D09E-4031-AB5D-232F6F707A45"?>
        <?define cmf_comerr64_dll_guid="3C34EFDA-3F93-4aa6-AA44-718AA25DD346"?>
        <?define cmf_comerr64_dll_name="comerr64.dll"?>
        <?define cmf_gss_exe_guid="805EBB97-4860-40d4-A038-5E56717EAFB4"?>
        <?define cmf_gss_client_exe_guid="5F192562-436C-4800-93C7-148F7D46F521"?>
        <?define cmf_gss_server_exe_guid="33651319-01CB-4f2c-9B96-50F0F53E9CDF"?>
        <?define cmf_gssapi64_dll_guid="ACD1D3FA-3E96-47a6-8A39-88BDA2EA3C9D"?>
        <?define cmf_gssapi64_dll_name="gssapi64.dll"?>
        <?define cmf_k524init_exe_guid="63630B70-D9B1-47bc-905D-E4DFA6F9D0D1"?>
        <?define cmf_kclnt64_dll_guid="C26B3CAA-607C-42db-956C-9AA379CCE892"?>
        <?define cmf_kclnt64_dll_name="kclnt64.dll"?>
        <?define cmf_kdestroy_exe_guid="58AB2858-3513-4e4d-B76C-915B0D0DEE82"?>
        <?define cmf_kcpytkt_exe_guid="B2BA9697-4E42-433d-BA85-79A49A622D84"?>
        <?define cmf_kdeltkt_exe_guid="998BF60D-75EF-4807-BDA3-7DDE47C4F00F"?>
        <?define cmf_kinit_exe_guid="6855B2B9-D3EB-42ce-B0EA-EFCD5960B635"?>
        <?define cmf_klist_exe_guid="1AE98193-6596-4460-9B42-15C734E28CE8"?>
        <?define cmf_kpasswd_exe_guid="669A2965-413F-4003-9F43-4615CC7C3B1A"?>
        <?define cmf_kswitch_exe_guid="EFBA642C-D1E8-441b-A31D-8B44715B2EAB"?>
        <?define cmf_kvno_exe_guid="56C95EBA-2A53-4567-AA44-9A0EE01BB390"?>
        <?define cmf_krb5_64_dll_guid="8B7190D6-76BD-442b-893B-1EED4E26EC0A"?>
        <?define cmf_krb5_64_dll_name="krb5_64.dll"?>
        <?define cmf_k5sprt64_dll_guid="2DD8CE7D-8C8E-4cfb-BC73-765858DD1418"?>
        <?define cmf_k5sprt64_dll_name="k5sprt64.dll"?>
        <?define cmf_krb524_dll_guid="4FAFB2A6-FC42-466b-9A86-42C3150252E2"?>
        <?define cmf_krb524_dll_name="krb524.dll"?>
        <?define cmf_krbcc64_dll_guid="56DAD1B9-4A59-46e6-81CA-54E2B6C9D618"?>
        <?define cmf_krbcc64_dll_name="krbcc64.dll"?>
        <?define cmf_ccapiserver_exe_guid="0192F58B-9129-426d-A271-E18455EDC80E"?>
        <?define cmf_ccapiserver_exe_name="ccapiserver.exe"?>
        <?define cmf_krbv4w32_dll_guid="D195FADB-A3B0-4023-A824-8895552CDC56"?>
        <?define cmf_krbv4w32_dll_name="krbv4w64.dll"?>
        <?define cmf_leash_exe_guid="D195FADB-A3B0-4023-A824-8895552CDC56"?>
        <?define csc_leash_exe_guid="9D43350A-A8AE-4405-AB82-64E90E5A1A70"?>
        <?define rcm_leash_1_guid="7E5517FF-FB76-431f-A92B-2895C9BE7E98"?>
        <?define rcm_leash_2_guid="008208AC-46BE-4ca4-BEA7-8FCAAD8BA7C3"?>
        <?define rcm_leash_3_guid="66D97178-E735-499b-961F-C7B8B8074E1A"?>
        <?define rcm_leash_4_guid="D20180CC-B68D-4a6d-95E4-5EB07B9EADCD"?>
        <?define rcm_leash_5_guid="AF3B73AE-86ED-42f5-987A-9831B8799D2F"?>
        <?define rcm_leash_6_guid="0C72D4F8-096B-4295-8F0A-6B5723A8593D"?>
        <?define csc_LeashStartup_guid="228192C5-D847-4c46-A726-0E8211742349"?>
        <?define cmf_leash32_hlp_guid="EF1B0ED6-9C01-4adb-9F11-C3FF07F669D5"?>
        <?define cmf_leash32_chm_guid="769B54EB-9B96-4abe-9B72-9EF346C8C03D"?>
        <?define cmf_leashw64_dll_guid="7222B9E1-EE70-4ccb-929F-43D45574AA83"?>
        <?define cmf_leashw64_dll_name="leashw64.dll"?>
        <?define rcm_leashdll_1_guid="64BC62DF-F1D6-423a-B4E0-E75E214BAC26"?>
        <?define rcm_leashdll_2_guid="2CEF4DC7-684B-4a53-9053-5EB26264B009"?>
        <?define rcm_leashdll_3_guid="D93DE8B4-919E-48ef-812D-BFCC26460608"?>
        <?define rcm_leashdll_4_guid="989B5FC5-63DC-47d6-BBBC-EE1B355127F6"?>
        <?define rcm_leashdll_5_guid="BF38025F-ED64-4dde-8BDD-250C1BB73909"?>
        <?define rcm_leashdll_6_guid="AC35CE64-9D8D-4a21-AB1F-18803C69E7B4"?>
        <?define rcm_leashdll_7_guid="02014C27-BAC8-4b86-95F8-43F9BF55064A"?>
        <?define rcm_leashdll_8_guid="DE70F57C-D3F8-4c1d-B868-A77DA04D9DD3"?>
        <?define rcm_leashdll_9_guid="A809042D-66F3-4a6a-B8EC-77C40C88F3E3"?>
        <?define rcm_leashdll_10_guid="E61875A4-F33C-419f-97B8-D2ACA3EBD4BB"?>
        <?define rcm_leashdll_11_guid="3732992E-34C4-430a-B081-C8601BA44A61"?>
        <?define rcm_leashdll_12_guid="2237072A-1955-4ca4-ABB3-78037E16F696"?>
        <?define rcm_leashdll_13_guid="6F626DB7-B0A6-4c1d-889E-A2DA742DFC4B"?>
        <?define rcm_leashdll_14_guid="109674B4-7390-49cc-9DB1-B9402E4C5645"?>
        <?define rcm_leashdll_15_guid="8AC8500D-2279-4a3c-82E9-4C0ED79A6EFC"?>
        <?define rcm_leashdll_16_guid="ED6BD7FE-7879-4da6-882D-98DEF6198F60"?>
        <?define rcm_leashdll_17_guid="E5F8A4DF-442E-4d24-B5E2-9F36A4F7E15D"?>
        <?define cmf_ms2mit_exe_guid="8AEC1FDA-4A45-4878-8C0B-465D46ACE306"?>
        <?define cmf_mit2ms_exe_guid="3CE7BAE8-22DA-4911-B370-4C700861BDFD"?>
        <?define cmf_xpprof64_dll_guid="********-50A4-4430-846B-F824C859E3DF"?>
        <?define cmf_xpprof64_dll_name="xpprof64.dll"?>
        <?define cmf_spake64_dll_guid="0E97B52A-EC8E-494C-BF5D-83AAACFEFDBA"?>
        <?define cmf_spake64_dll_name="spake64.dll"?>
        <?define cmf_nidmgr32_dll_guid="8538212A-9BD5-4d62-BF29-36D853385F0A"?>
        <?define cmf_nidmgr32_dll_name="nidmgr64.dll"?>
        <?define cmf_nidmgr32_dll_w2k_guid="01655D48-C596-48f8-A0C3-5DB3FC833444"?>
        <?define cmf_krb5cred_dll_guid="CC182AB1-E333-4501-8DEA-5A8D4FD36D0D"?>
        <?define cmf_krb5cred_dll_name="krb5cred.dll"?>
        <?define cmf_krb5cred_en_us_dll_guid="223B7E9D-290F-40b8-89B3-F8337A8E082D"?>
        <?define cmf_krb5cred_en_us_dll_name="krb5cred_en_us.dll"?>
        <?define cmf_bin_debug_guid="F3432C85-89D9-4bd6-BD82-4ED49A118338"?>
        <?define cmf_preauth_debug_guid="53491A4E-CB96-44D9-9B92-4ADF37C3A2D6"?>
<?elseif $(sys.BUILDARCH) = "x86"?>
        <?define UpgradeCode="61211594-AAA1-4A98-A299-757326763CC7"?>
        <?define PISystemFolder="SystemFolder"?>
        <?define PIProgramFilesFolder="ProgramFilesFolder"?>

        <?define cmf_kfwlogon_DLL_guid="2F104FEB-2D61-458A-BAE3-B153F151E728"?>
        <?define cmf_kfwcpcc_EXE_guid="C3284E7A-3665-45A6-B64E-C909B1D1BAA6"?>
        <?define cmp_ClientSystemDebug_guid="2D13ED48-53C2-4878-B196-2AD7F4100998"?>
        <?define rcm_krb5_1_guid="E190F8B9-51FA-4FB1-884C-C8AFA37F8653"?>
        <?define rcm_krb5_2_guid="AE7D4305-6193-4094-8C82-73862AE01DCE"?>
        <?define rcm_krb5_3_guid="853EE035-99AA-489A-8FB6-74C76624E92A"?>
        <?define cmf_gss_exe_guid="8CAF09C4-68A2-46DC-A618-AEF16D832E54"?>
        <?define cmf_gss_client_exe_guid="983E0887-0C8B-49AB-8F59-DFE3A4E45E89"?>
        <?define cmf_gss_server_exe_guid="B165FE41-D0DD-4DFC-92E6-A99ADA23BE8B"?>
        <?define cmf_k524init_exe_guid="20BE4EA5-C465-4AF3-9A4F-BB80934167E3"?>
        <?define cmf_kclnt32_dll_guid="D396C1E7-080E-49F5-92BA-73BCEDF09C8E"?>
        <?define cmf_kclnt32_dll_name="kclnt32.dll"?>
        <?define cmf_kdestroy_exe_guid="D1E9C111-7760-4EE6-86CF-D4B4064B0ABA"?>
        <?define cmf_kcpytkt_exe_guid="6B20E57C-0033-4dcf-B3C9-74ED0B2CF46E"?>
        <?define cmf_kdeltkt_exe_guid="C7528C87-9B61-439a-8EA8-AD4BE3D758F9"?>
        <?define cmf_kinit_exe_guid="80643A09-EF28-4714-BC62-B64FC5B17CAA"?>
        <?define cmf_klist_exe_guid="24FB6003-BC7A-4BF1-9503-82D398EC02D7"?>
        <?define cmf_kpasswd_exe_guid="3FA4AB96-FF12-460A-814E-3380E220787C"?>
        <?define cmf_kswitch_exe_guid="C1A575D3-DEF3-4fce-861E-9C6BA7F93072"?>
        <?define cmf_kvno_exe_guid="7759D524-1F88-4483-975F-DDD33A511512"?>
        <?define cmf_krb524_dll_guid="98685874-A9AA-4BC5-9830-271D9CF52C17"?>
        <?define cmf_krb524_dll_name="krb524.dll"?>
        <?define cmf_ccapiserver_exe_guid="7D5F0817-DACF-4B54-BB8D-94DD63626DB5"?>
        <?define cmf_ccapiserver_exe_name="ccapiserver.exe"?>
        <?define cmf_krbv4w32_dll_guid="DFA23F6C-5297-4876-AF52-6F7CF2CB34AC"?>
        <?define cmf_krbv4w32_dll_name="krbv4w32.dll"?>
        <?define cmf_leash_exe_guid="990D5F6B-4CEE-4706-96F4-F7AF12F97DF7"?>
        <?define csc_leash_exe_guid="8A096700-47B1-4A0B-B7B5-44F75086DEAE"?>
        <?define rcm_leash_1_guid="B91648A0-26F7-43BB-A954-202FF3811E3C"?>
        <?define rcm_leash_2_guid="0D8DCC52-F855-4C46-86A1-198E6EE0CB8A"?>
        <?define rcm_leash_3_guid="9610A7E3-251F-4286-B776-1C3AF5DE7815"?>
        <?define rcm_leash_4_guid="815AED84-2437-4EBC-B561-F847833DB3A5"?>
        <?define rcm_leash_5_guid="A0D3D75F-762E-4D5C-909B-53E7396CEDB6"?>
        <?define rcm_leash_6_guid="F675C145-6F9D-4BC4-9DA0-CAFB47A96A71"?>
        <?define csc_LeashStartup_guid="0DF73BCD-F34E-4B01-AA71-0EE08EB62F70"?>
        <?define cmf_leash32_hlp_guid="919616D6-1605-4A79-8E33-C18A0D0F25E3"?>
        <?define cmf_leash32_chm_guid="C50E5E0A-B822-4419-855B-1713637BCA6A"?>
        <?define rcm_leashdll_1_guid="54C949DA-AF1E-4412-81AF-F502BD5904D2"?>
        <?define rcm_leashdll_2_guid="9B553794-45E7-49FB-B6D9-1C3C9BB6E00D"?>
        <?define rcm_leashdll_3_guid="E3D1284C-17F6-41E3-9AA7-2ED05432060F"?>
        <?define rcm_leashdll_4_guid="A02831D5-48B2-4E82-A670-EDCEBC197273"?>
        <?define rcm_leashdll_5_guid="5FDB0C01-6668-43E3-9C83-2CD364D97BF3"?>
        <?define rcm_leashdll_6_guid="999BD59C-5C1E-446E-9D38-F4E26DD27D09"?>
        <?define rcm_leashdll_7_guid="C908AA17-DD21-4193-BA1D-535A2FD3D803"?>
        <?define rcm_leashdll_8_guid="02926245-2327-46F9-AEF6-89E2DB0D90E1"?>
        <?define rcm_leashdll_9_guid="B45BBA29-7A67-4FF7-AAA4-80044D46C451"?>
        <?define rcm_leashdll_10_guid="1DDC4D78-BDB4-48CD-A4E9-024FA9706100"?>
        <?define rcm_leashdll_11_guid="39134333-58C7-4C6B-B690-2322D3AE928A"?>
        <?define rcm_leashdll_12_guid="BBB6F5C3-290F-4A21-A630-E8630C6EAB67"?>
        <?define rcm_leashdll_13_guid="0F86A73E-DB31-45E7-9156-BE0EC99076A7"?>
        <?define rcm_leashdll_14_guid="FE0F06A2-62E7-46C1-9BFF-337C50DB78C7"?>
        <?define rcm_leashdll_15_guid="2DAC4693-6435-4278-A584-3D2B74BE87D5"?>
        <?define rcm_leashdll_16_guid="FE3DDD47-CCDE-44F8-8C86-97F2C3545443"?>
        <?define rcm_leashdll_17_guid="1B685E1B-32F2-49A5-9B7D-4288741A2C17"?>
        <?define cmf_ms2mit_exe_guid="63D189DC-5EE4-49E2-B5E9-6E74A28602C8"?>
        <?define cmf_mit2ms_exe_guid="4F487781-5B55-48c1-A3FA-8BC6ECA4FEA1"?>
        <?define cmf_krb5cred_dll_guid="27A7723A-F0D9-4F06-892C-54F0AC6014C3"?>
        <?define cmf_krb5cred_dll_name="krb5cred.dll"?>
        <?define cmf_krb5cred_en_us_dll_guid="EA9ABE05-A85B-43BB-8741-50D3C3128632"?>
        <?define cmf_krb5cred_en_us_dll_name="krb5cred_en_us.dll"?>
        <?define cmf_bin_debug_guid="********-**************-A612636841E3"?>
        <?define cmf_preauth_debug_guid="169C0A38-EB79-4AA9-B78B-998B55084ECD"?>
<?else?>
        <?error Unknown platform?>
<?endif?>
</Include>
