<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>Microsoft.Native.Quic.MsQuic.Schannel</id>
    <version>0.0.0</version>
    <title><PERSON><PERSON>ui<PERSON> (Schannel)</title>
    <authors>Microsoft</authors>
    <license type="expression">MIT</license>
    <icon>pkgicon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://github.com/microsoft/msquic</projectUrl>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <description>MsQuic native library for x64, x86 and arm64 using schannel for TLS</description>
    <repository type="git" url="$RepoRemote$" commit="$CommitHash$" />
    <tags>native quic msquic schannel</tags>
  </metadata>
</package>
