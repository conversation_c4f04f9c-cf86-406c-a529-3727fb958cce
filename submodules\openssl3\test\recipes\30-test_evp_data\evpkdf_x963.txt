#
# Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.


# Test vectors extracted from
# https://csrc.nist.gov/CSRC/media/Projects/Cryptographic-Algorithm-Validation-Program/documents/components/800-135testvectors/ansx963_2001.zip

Title = X963 KDF tests (from NIST test vectors)

KDF = X963KDF
Ctrl.digest = digest:SHA1
Ctrl.hexsecret = hexsecret:fd17198b89ab39c4ab5d7cca363b82f9fd7e23c3984dc8a2
Ctrl.hexinfo = hexinfo:856a53f3e36a26bbc5792879f307cce2
Output = 6e5fad865cb4a51c95209b16df0cc490bc2c9064405c5bccd4ee4832a531fbe7f10cb79e2eab6ab1149fbd5a23cfdabc41242269c9df22f628c4424333855b64e95e2d4fb8469c669f17176c07d103376b10b384ec5763d8b8c610409f19aca8eb31f9d85cc61a8d6d4a03d03e5a506b78d6847e93d295ee548c65afedd2efec

KDF = X963KDF
Ctrl.digest = digest:SHA224
Ctrl.hexsecret = hexsecret:da67a73072d521a8272c69023573012ddf9b46bff65b3900
Ctrl.hexinfo = hexinfo:727997aed53e78f74b1d66743a4ea4d2
Output = dfc3126c5eebf9a58d89730e8d8ff7cc772592f28c10b349b437d9d068698a22e532eae975dfaf9c5c6a9f2935eafb05353013c253444e61f07bc9ddd15948e614bdc7e445ba3b1893f42f87f18fb352d49956009a642c362d45410b43a9ab376e9261210739174759511d1f9e52f6ec73dfed446dbafaf7fd1a57113abc2e8d

KDF = X963KDF
Ctrl.digest = digest:SHA256
Ctrl.hexsecret = hexsecret:22518b10e70f2a3f243810ae3254139efbee04aa57c7af7d
Ctrl.hexinfo = hexinfo:75eef81aa3041e33b80971203d2c0c52
Output = c498af77161cc59f2962b9a713e2b215152d139766ce34a776df11866a69bf2e52a13d9c7c6fc878c50c5ea0bc7b00e0da2447cfd874f6cf92f30d0097111485500c90c3af8b487872d04685d14c8d1dc8d7fa08beb0ce0ababc11f0bd496269142d43525a78e5bc79a17f59676a5706dc54d54d4d1f0bd7e386128ec26afc21

KDF = X963KDF
Ctrl.digest = digest:SHA384
Ctrl.hexsecret = hexsecret:d8554db1b392cd55c3fe957bed76af09c13ac2a9392f88f6
Output = 671a46aada145162f8ddf1ca586a1cda

KDF = X963KDF
Ctrl.digest = digest:SHA384
Ctrl.hexsecret = hexsecret:c051fd22539c9de791d6c43a854b8f80a6bf70190050854a
Ctrl.hexinfo = hexinfo:1317504aa34759bb4c931e3b78201945
Output = cf6a84434734ac6949e1d7976743277be789906908ad3ca3a8923da7f476abbeb574306d7243031a85566914bfd247d2519c479953d9d55b6b831e56260806c39af21b74e3ecf470e3bd8332791c8a23c13352514fdef00c2d1a408ba31b2d3f9fdcb373895484649a645d1845eec91b5bfdc5ad28c7824984482002dd4a8677

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:87fc0d8c4477485bb574f5fcea264b30885dc8d90ad82782
Output = 947665fbb9152153ef460238506a0245

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:00aa5bb79b33e389fa58ceadc047197f14e73712f452caa9fc4c9adb369348b81507392f1a86ddfdb7c4ff8231c4bd0f44e44a1b55b1404747a9e2e753f55ef05a2d
Ctrl.hexinfo = hexinfo:e3b5b4c1b0d5cf1d2b3a2f9937895d31
Output = 4463f869f3cc18769b52264b0112b5858f7ad32a5a2d96d8cffabf7fa733633d6e4dd2a599acceb3ea54a6217ce0b50eef4f6b40a5c30250a5a8eeee208002267089dbf351f3f5022aa9638bf1ee419dea9c4ff745a25ac27bda33ca08bd56dd1a59b4106cf2dbbc0ab2aa8e2efa7b17902d34276951ceccab87f9661c3e8816

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:009dcd6ba5c8c803ca21f9996ca5dd86047d4ddc150fddace1b1ebe996c2007e3ee907c8ff03b9ef766e8ceb4dedf7489e5162e2278c0185e4be381bec17dd992cf8
Ctrl.hexinfo = hexinfo:1e60e51c11a538b0ea8990d69a4c6358
Output = 4e55036a32f32fc965046fdfbf686c108e43a69f8fc1a64ff1bd77763f2eedc8bf277d78b4ce31243e1adbe2c2d5dd59b47503b5b90b54f9d7a9a5aea49c7f0283cb64c3849a1d157000fd41ef6c1d1a5b62734e7c9a20dcfb57f2da974933f57ee619d72898d0e93d9a4254aaddf73941d6269298b4d49c0ac64a33802fe8f2

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:01bbc44314f24db4d67a2a7fb5ca3f7a5022790f5875895d448050eda5611a2f39de48e394c5a3df26208eb01f804d0a1d68eece6b6fa96d6db895e133e129094f78
Ctrl.hexinfo = hexinfo:433e3ee77d00e4a9634efd677e2ff21b
Output = f1255002293d5fbcf35ad0e532ae872171d11014616a2c52d7e5cb861b0251b9e505a77161c777bafc052b6525a6ecf34590605de72f13a1aff0a61a8a4a3364ebbe2f99224c13e043e497af8a26de749cd257e475b2f0e60e3b594901320a692a4af422f9636e4814b33f67d181a086265013b0d4efd9e1a94ea8a576afde66

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:01a33032a2bf6f8e9d6972dd339536c9e248ae9881844ff1bd04af48085be4ca1834f2a94ce1019dd9620d1e3a68203a5b291f40b5f8e3238a2a036312b89061cc60
Ctrl.hexinfo = hexinfo:d3297ad6b9757d1f5a9d5b0e72176d74
Output = 63565d1d3443620fba4218c97887ff40d6d68bf56b429c22018be5d91c318187ebe8a9399c5cc6c4a849288ab784d4340714ae3fdb426c4a83db9ce2ba8aea80d448e50ad543749b47bcaae519f7f00badd8d48296e81069104dcd293c605b08159ef2ef14c7833739d0414274136ae4db05ba4fa31b29c59de46d9be539525f

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:004b20a501776ea54cbdabffec2a664b7a93f8d67b17405a82bd9cbf3685a4659beb2deff1b6ecaa7ab187b6d4fd407f10db6992c65308410deb133be31a0de0c1c9
Ctrl.hexinfo = hexinfo:fd5462cb37aa298e95f8e34bb49d85ca
Output = cafcbc117317661bf15277c2881e05e345c1720b0c1c4040c33fe4a3ecf8032802642d29828a077ca91b6fac216b7a06517740c7d633c279dd2115eb7a34fd337376247219f53da32df57070f47c2e0816710080d6492e1c3e8cac818c3cfca2a3ce5cf1515f066b1815d2d2f69fa3111a9e81570963b90a536da0376c12265b

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:01fb44335b437771777f14d44e5b634c18c7f570b935228fd3073e3cbde299dfb9f4d64ad720d30e875e8c6bbe181027459c9d3f92a276a38e22faf25f208576a63f
Ctrl.hexinfo = hexinfo:2359d18657243d61963ceca3fa93587d
Output = 1544e54cd293e533959bdd893337f01ef0c7685a4d8d403d438b0223a7e18330c312a0f16bd819f4359fdd74ae85cc603d35e3d9cba896177452c8dee5214066fca420c3ab522a245af215beb7de52ebb0bdd15d0596b8b763cf7e25610a53efa726b899a1d9727b25ec673ee91ff2111f03cf761a7880d69625e784becfd4e0

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:0109afa3904193690d3f2c49e42d08c8c5cd2ea907a0d699c876e418e303b485374c8d6cf5a32af1491b3ea8a3503692b4a0fd78f9b4082e2a6e72345db4532d749f
Ctrl.hexinfo = hexinfo:7c19631d3cd65915fa4789cf7b1c0979
Output = fb60175568a66ef4202e110396663085fe2a9d6d2071e55d03c30ea499fee850c99c4e42a7227cca2eaf4d75e37dde205ae07260e84aeee6ef0819d98bd00d0ff5ba55994e7bf2a578baf2ee9aa862d94bf431fa14429010ebc30d7e602de726cdffacaeabc8541237fbc0c975abbf203c018c688ee354d07978654b90de9569

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:00632e165775f3c5b6e81d4042f809e904b8167687747638874b39ffce1993f46e8fc44e2a1c3df59563003bad3e25c85b61819e9addc0fdbe173dd4115c38f62ef6
Ctrl.hexinfo = hexinfo:2bf0f18b7f21c4ec9c20b84c75f66b7c
Output = c324fed01b75c37fc96703031403d5cc6857dc7ffa48192d9a10d5c69dd6274ecd0eb9a278f9e6b616c27bbf2e3e016635b311940390c52c61a4f4b3383ca6046961dbd2455ff6a982e8269864edd3cc1b1053da7daf9699c61b05f1acca7b79e68db655fd526fdc392bd36dcaf1c5b2fafb8975e318070d4bb948829ac41bb6

KDF = X963KDF
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:0096172bf47d06d544ae98471490cf9e52ee59ea7a2208b33b26c52d4952bb8f41b2211d3f9ff32e77ca8cc906ba8d246ff266ddf1df8f53824ccb15b8fb39724703
Ctrl.hexinfo = hexinfo:cf3a74ba86af42f1ae85477ead645583
Output = 995d1ab8557dfeafcb347f8182583fa0ac5e6cb3912393592590989f38a0214f6cf7d6fbe23917b0966c6a870876de2a2c13a45fa7aa1715be137ed332e1ffc204ce4dcce33ece6dec7f3da61fa049780040e44142cc8a1e5121cf56b386f65b7c261a192f05e5fefae4221a602bc51c41ef175dc45fb7eab8642421b4f7e3e7

KDF = X942KDF-CONCAT
Ctrl.digest = digest:SHA512
Ctrl.hexsecret = hexsecret:0037cd001a0ad87f35ddf58ab355d6144ba2ed0749a7435dab548ba0bfbe723c047e2396b4eef99653412a92c8db74bb5c03063f2eb0525ae87356750ae3676faa86
Ctrl.hexinfo = hexinfo:eb17da8851c41c7ac6710b1c49f324f8
Output = 829a28b81f9e95b5f306604067499c07d5944ca034ed130d513951f7143e4e162bad8adb2833e53b8235c293cd2a809659ac7f7e392cba6a543660e5d95070c0c9e6a9cdc38123e22da61bb4cbb6ad6d1a58a069e934fc231bd9fe39a24afcbf322ccea385f0418f3b01c1edd6e7124593a1cefe3e48fcd95daaf72cfd973c59
