import base64
import socket
from k5test import *

realm = K5Realm()

# CVE-2012-1014 KDC dereferences uninitialized pointer

# Affects only krb5-1.10.x.

s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
a = (hostname, realm.portbase)

x1 = base64.b16decode('6A5E305BA103020105A2030201')
x2 = base64.b16decode('A44F304DA007030500FEDCBA90A10E30' +
                      '0CA003020101A10530031B0141A2031B' +
                      '0141A30E300CA003020101A10530031B' +
                      '0141A511180F31393934303631303036' +
                      '303331375AA70302012AA80530030201' +
                      '01')

for x in range(11, 128):
    s.sendto(x1 + bytes([x]) + x2, a)

# Make sure kinit still works.

realm.kinit(realm.user_princ, password('user'))

success('CVE-2012-1014 regression test')
