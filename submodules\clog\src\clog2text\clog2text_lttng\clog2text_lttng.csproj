<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net6.0</TargetFrameworks>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>1701;1702;3026</NoWarn>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.8.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\clogutils\clogutils.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <Version>0.0.1</Version>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackAsTool>true</PackAsTool>
    <PackageOutputPath>../../nupkg</PackageOutputPath>
    <ToolCommandName>clog2text_lttng</ToolCommandName>
    <PackageId>Microsoft.Logging.CLOG2Text.Lttng</PackageId>
    <Authors>Microsoft</Authors>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <Copyright>© Microsoft Corporation. All rights reserved</Copyright>
    <Title>CLOG Lttng log converter</Title>
    <ProjectURL>https://github.com/microsoft/CLOG</ProjectURL>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>git://github.com/microsoft/CLOG</RepositoryUrl>
  </PropertyGroup>

</Project>
