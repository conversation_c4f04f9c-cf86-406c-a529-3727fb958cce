krb5_ldap_open
krb5_ldap_close
krb5_ldap_db_init
krb5_ldap_lib_init
krb5_ldap_lib_cleanup
krb5_ldap_get_age
krb5_ldap_read_server_params
krb5_ldap_put_principal
krb5_ldap_get_principal
krb5_ldap_delete_principal
krb5_ldap_rename_principal
krb5_ldap_iterate
krb5_ldap_read_krbcontainer_dn
krb5_ldap_list_realm
krb5_ldap_read_realm_params
krb5_ldap_free_realm_params
krb5_ldap_modify_realm
krb5_ldap_create_krbcontainer
krb5_ldap_create_realm
krb5_ldap_delete_realm
krb5_ldap_list_policy
krb5_ldap_free_policy
krb5_ldap_read_policy
krb5_ldap_modify_policy
krb5_ldap_delete_policy
krb5_ldap_create_policy
krb5_ldap_create_password_policy
krb5_ldap_put_password_policy
krb5_ldap_get_password_policy
krb5_ldap_delete_password_policy
krb5_ldap_iterate_password_policy
krb5_dbe_free_contents
krb5_ldap_free_server_params
krb5_ldap_free_server_context_params
krb5_ldap_delete_realm_1
krb5_ldap_lock
krb5_ldap_unlock
krb5_ldap_create
krb5_ldap_check_policy_as
krb5_ldap_audit_as_req
krb5_ldap_check_allowed_to_delegate
