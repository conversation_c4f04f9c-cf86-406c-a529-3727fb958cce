- hosts: all
  tasks:
    - name: <PERSON><PERSON> wycheproof
      git:
        repo: https://github.com/google/wycheproof
        dest: "{{ ansible_facts.env['HOME'] }}/wycheproof"
        depth: 1

    - name: Install tox
      include_role:
        name: ensure-tox

    - name: Install required packages
      package:
        name:
          - build-essential
          - libssl-dev
          - libffi-dev
          - python3-dev
      become: yes
      when: ansible_distribution in ['Debian', 'Ubuntu']

    - name: Install required packages
      package:
        name:
          - redhat-rpm-config
          - gcc
          - libffi-devel
          - openssl-devel
          - python3-devel
          - python2-devel
      become: yes
      when: ansible_distribution == 'CentOS'

    - name: Install rust
      include_role:
        name: ensure-rust

