{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
    ALG("{{variant['name']}}", oqs_{{ variant['name'] }}_keymgmt_functions),
     {%- for classical_alg in variant['mix_with'] -%}
    ALG("{{ classical_alg['name'] }}_{{variant['name']}}", oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_keymgmt_functions),
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}
{% for kem in config['kems'] %}
{% if kem['bit_security'] == 256 %}    KEMKMALG2({{ kem['name_group'] }}, {{ kem['bit_security'] }}){% else %}    KEMKMALG3({{ kem['name_group'] }}, {{ kem['bit_security'] }}){% endif %},
{%- endfor %}

