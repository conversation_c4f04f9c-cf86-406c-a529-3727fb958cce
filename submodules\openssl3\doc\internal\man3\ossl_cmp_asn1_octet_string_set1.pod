=pod

=head1 NAME

ossl_cmp_asn1_octet_string_set1,
ossl_cmp_asn1_octet_string_set1_bytes
- ASN.1 octet string utility functions

=head1 SYNOPSIS

 #include "cmp_local.h"

 int ossl_cmp_asn1_octet_string_set1(ASN1_OCTET_STRING **tgt,
                                     const ASN1_OCTET_STRING *src);
 int ossl_cmp_asn1_octet_string_set1_bytes(ASN1_OCTET_STRING **tgt,
                                           const unsigned char *bytes, int len);

=head1 DESCRIPTION

ossl_cmp_asn1_octet_string_set1() frees any previous value of the variable
referenced via the I<tgt> argument and assigns either a copy of
the ASN1_OCTET_STRING given as the I<src> argument or NULL.

ossl_cmp_asn1_octet_string_set1_bytes() frees any previous value of the variable
referenced via the I<tgt> argument and assigns either a copy of the given byte
string (with the given length) or NULL.

=head1 RETURN VALUES

All functions return 1 on success, 0 on error.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
