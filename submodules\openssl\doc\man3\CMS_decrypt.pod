=pod

=head1 NAME

CMS_decrypt - decrypt content from a CMS envelopedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int CMS_decrypt(CMS_ContentInfo *cms, EVP_PKEY *pkey, X509 *cert,
                 B<PERSON> *dcont, BIO *out, unsigned int flags);

=head1 DESCRIPTION

CMS_decrypt() extracts and decrypts the content from a CMS EnvelopedData
structure. B<pkey> is the private key of the recipient, B<cert> is the
recipient's certificate, B<out> is a BIO to write the content to and
B<flags> is an optional set of flags.

The B<dcont> parameter is used in the rare case where the encrypted content
is detached. It will normally be set to NULL.

=head1 NOTES

Although the recipients certificate is not needed to decrypt the data it is
needed to locate the appropriate (of possible several) recipients in the CMS
structure.

If B<cert> is set to NULL all possible recipients are tried. This case however
is problematic. To thwart the MMA attack (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s attack on
PKCS #1 v1.5 RSA padding) all recipients are tried whether they succeed or
not. If no recipient succeeds then a random symmetric key is used to decrypt
the content: this will typically output garbage and may (but is not guaranteed
to) ultimately return a padding error only. If CMS_decrypt() just returned an
error when all recipient encrypted keys failed to decrypt an attacker could
use this in a timing attack. If the special flag B<CMS_DEBUG_DECRYPT> is set
then the above behaviour is modified and an error B<is> returned if no
recipient encrypted key can be decrypted B<without> generating a random
content encryption key. Applications should use this flag with
B<extreme caution> especially in automated gateways as it can leave them
open to attack.

It is possible to determine the correct recipient key by other means (for
example looking them up in a database) and setting them in the CMS structure
in advance using the CMS utility functions such as CMS_set1_pkey(). In this
case both B<cert> and B<pkey> should be set to NULL.

To process KEKRecipientInfo types CMS_set1_key() or CMS_RecipientInfo_set0_key()
and CMS_RecipientInfo_decrypt() should be called before CMS_decrypt() and
B<cert> and B<pkey> set to NULL.

The following flags can be passed in the B<flags> parameter.

If the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are deleted
from the content. If the content is not of type B<text/plain> then an error is
returned.

=head1 RETURN VALUES

CMS_decrypt() returns either 1 for success or 0 for failure.
The error can be obtained from ERR_get_error(3)

=head1 BUGS

The lack of single pass processing and the need to hold all data in memory as
mentioned in CMS_verify() also applies to CMS_decrypt().

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_encrypt(3)>

=head1 COPYRIGHT

Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
