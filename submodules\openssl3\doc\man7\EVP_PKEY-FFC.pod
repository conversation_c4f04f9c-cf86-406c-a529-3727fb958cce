=pod

=head1 NAME

EVP_PKEY-FFC - EVP_PKEY DSA and DH/DHX shared FFC parameters.

=head1 DESCRIPTION

Finite field cryptography (FFC) is a method of implementing discrete logarithm
cryptography using finite field mathematics. DSA is an example of FF<PERSON> and
<PERSON><PERSON><PERSON><PERSON> key establishment algorithms specified in SP800-56A can also be
implemented as FFC.

The B<DSA>, B<DH> and B<DHX> keytypes are implemented in OpenSSL's default and
FIPS providers.
The implementations support the basic DSA, DH and DHX keys, containing the public
and private keys I<pub> and I<priv> as well as the three main domain parameters
I<p>, I<q> and I<g>.

For B<DSA> (and B<DH> that is not a named group) the FIPS186-4 standard
specifies that the values used for FFC parameter generation are also required
for parameter validation.
This means that optional FFC domain parameter values for I<seed>, I<pcounter>
and I<gindex> may need to be stored for validation purposes.
For B<DH> the I<seed> and I<pcounter> can be stored in ASN1 data
(but the I<gindex> is not). For B<DSA> however, these fields are not stored in
the ASN1 data so they need to be stored externally if validation is required.

The B<DH> key type uses PKCS#3 format which saves p and g, but not the 'q' value.
The B<DHX> key type uses X9.42 format which saves the value of 'q' and this
must be used for FIPS186-4.

=head2 FFC parameters

In addition to the common parameters that all keytypes should support (see
L<provider-keymgmt(7)/Common parameters>), the B<DSA>, B<DH> and B<DHX> keytype
implementations support the following.

=over 4

=item "pub" (B<OSSL_PKEY_PARAM_PUB_KEY>) <unsigned integer>

The public key value.

=item "priv" (B<OSSL_PKEY_PARAM_PRIV_KEY>) <unsigned integer>

The private key value.

=back

=head2 FFC DSA, DH and DHX domain parameters

=over 4

=item "p" (B<OSSL_PKEY_PARAM_FFC_P>) <unsigned integer>

A DSA or Diffie-Hellman prime "p" value.

=item "g" (B<OSSL_PKEY_PARAM_FFC_G>) <unsigned integer>

A DSA or Diffie-Hellman generator "g" value.

=back

=head2 FFC DSA and DHX domain parameters

=over 4

=item "q" (B<OSSL_PKEY_PARAM_FFC_Q>) <unsigned integer>

A DSA or Diffie-Hellman prime "q" value.

=item "seed" (B<OSSL_PKEY_PARAM_FFC_SEED>) <octet string>

An optional domain parameter I<seed> value used during generation and validation
of I<p>, I<q> and canonical I<g>.
For validation this needs to set the I<seed> that was produced during generation.

=item "gindex" (B<OSSL_PKEY_PARAM_FFC_GINDEX>) <integer>

Sets the index to use for canonical generation and verification of the generator
I<g>.
Set this to a positive value from 0..FF to use this mode. This I<gindex> can
then be reused during key validation to verify the value of I<g>. If this value
is not set or is -1 then unverifiable generation of the generator I<g> will be
used.

=item "pcounter" (B<OSSL_PKEY_PARAM_FFC_PCOUNTER>) <integer>

An optional domain parameter I<counter> value that is output during generation
of I<p>. This value must be saved if domain parameter validation is required.

=item "hindex" (B<OSSL_PKEY_PARAM_FFC_H>) <integer>

For unverifiable generation of the generator I<g> this value is output during
generation of I<g>. Its value is the first integer larger than one that
satisfies g = h^j mod p (where g != 1 and "j" is the cofactor).

=item "j" (B<OSSL_PKEY_PARAM_FFC_COFACTOR>) <unsigned integer>

An optional informational cofactor parameter that should equal to (p - 1) / q.

=item "validate-pq" (B<OSSL_PKEY_PARAM_FFC_VALIDATE_PQ>) <unsigned integer>

=item "validate-g" (B<OSSL_PKEY_PARAM_FFC_VALIDATE_G>) <unsigned integer>

These boolean values are used during FIPS186-4 or FIPS186-2 key validation checks
(See L<EVP_PKEY_param_check(3)>) to select validation options. By default
I<validate-pq> and I<validate-g> are both set to 1 to check that p,q and g are
valid. Either of these may be set to 0 to skip a test, which is mainly useful
for testing purposes.

=item "validate-legacy" (B<OSSL_PKEY_PARAM_FFC_VALIDATE_LEGACY>) <unsigned integer>

This boolean value is used during key validation checks
(See L<EVP_PKEY_param_check(3)>) to select the validation type. The default
value of 0 selects FIPS186-4 validation. Setting this value to 1 selects
FIPS186-2 validation.

=back

=head2 FFC key generation parameters

The following key generation types are available for DSA and DHX algorithms:

=over 4

=item "type" (B<OSSL_PKEY_PARAM_FFC_TYPE>) <UTF8 string>

Sets the type of parameter generation. The shared valid values are:

=over 4

=item "fips186_4"

The current standard.

=item "fips186_2"

The old standard that should only be used for legacy purposes.

=item "default"

This can choose one of "fips186_4" or "fips186_2" depending on other
parameters set for parameter generation.

=back

=item "pbits" (B<OSSL_PKEY_PARAM_FFC_PBITS>) <unsigned integer>

Sets the size (in bits) of the prime 'p'.

=item "qbits" (B<OSSL_PKEY_PARAM_FFC_QBITS>) <unsigned integer>

Sets the size (in bits) of the prime 'q'.

For "fips186_4" this can be either 224 or 256.
For "fips186_2" this has a size of 160.

=item "digest" (B<OSSL_PKEY_PARAM_FFC_DIGEST>)  <UTF8 string>

Sets the Digest algorithm to be used as part of the Key Generation Function
associated with the given Key Generation I<ctx>.
This must also be set for key validation.

=item "properties" (B<OSSL_PKEY_PARAM_FFC_DIGEST_PROPS>) <UTF8 string>

Sets properties to be used upon look up of the implementation for the selected
Digest algorithm for the Key Generation Function associated with the given key
generation I<ctx>. This may also be set for key validation.

=item "seed" (B<OSSL_PKEY_PARAM_FFC_SEED>) <octet string>

For "fips186_4" or "fips186_2" generation this sets the I<seed> data to use
instead of generating a random seed internally. This should be used for
testing purposes only. This will either produce fixed values for the generated
parameters OR it will fail if the seed did not generate valid primes.

=item "gindex" (B<OSSL_PKEY_PARAM_FFC_GINDEX>) <integer>

=item "pcounter" (B<OSSL_PKEY_PARAM_FFC_PCOUNTER>) <integer>

=item "hindex" (B<OSSL_PKEY_PARAM_FFC_H>) <integer>

These types are described above.

=back

=head1 CONFORMING TO

The following sections of SP800-56Ar3:

=over 4

=item ******* FFC Domain Parameter Selection/Generation

=back

The following sections of FIPS186-4:

=over 4

=item A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function.

=item A.2.3 Generation of canonical generator g.

=item A.2.1 Unverifiable Generation of the Generator g.

=back

=head1 SEE ALSO

L<EVP_PKEY-DSA(7)>,
L<EVP_PKEY-DH(7)>,
L<EVP_SIGNATURE-DSA(7)>,
L<EVP_KEYEXCH-DH(7)>
L<EVP_KEYMGMT(3)>,
L<EVP_PKEY(3)>,
L<provider-keymgmt(7)>,
L<OSSL_PROVIDER-default(7)>,
L<OSSL_PROVIDER-FIPS(7)>,

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
