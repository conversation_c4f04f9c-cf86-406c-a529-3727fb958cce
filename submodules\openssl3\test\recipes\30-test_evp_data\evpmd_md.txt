#
# Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title = MD2 tests

Availablein = legacy
Digest = MD2
Input =
Output = 8350e5a3e24c153df2275c9f80692773

Availablein = legacy
Digest = MD2
Input = "a"
Output = 32ec01ec4a6dac72c0ab96fb34c0b5d1

Availablein = legacy
Digest = MD2
Input = "abc"
Output = da853b0d3f88d99b30283a69e6ded6bb

Availablein = legacy
Digest = MD2
Input = "message digest"
Output = ab4f496bfb2a530b219ff33031fe06b0

Availablein = legacy
Digest = MD2
Input = "abcdefghijklmnopqrstuvwxyz"
Output = 4e8ddff3650292ab5a4108c3aa47940b

Availablein = legacy
Digest = MD2
Input = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
Output = da33def2a42df13975352846c30338cd

Availablein = legacy
Digest = MD2
Input = "12345678901234567890123456789012345678901234567890123456789012345678901234567890"
Output = d5976f79d83d3a0dc9806c3c66f3efd8

Title = MD4 tests

Availablein = legacy
Digest = MD4
Input = ""
Output = 31d6cfe0d16ae931b73c59d7e0c089c0

Availablein = legacy
Digest = MD4
Input = "a"
Output = bde52cb31de33e46245e05fbdbd6fb24

Availablein = legacy
Digest = MD4
Input = "abc"
Output = a448017aaf21d8525fc10ae87aa6729d

Availablein = legacy
Digest = MD4
Input = "message digest"
Output = d9130a8164549fe818874806e1c7014b

Availablein = legacy
Digest = MD4
Input = "abcdefghijklmnopqrstuvwxyz"
Output = d79e1c308aa5bbcdeea8ed63df412da9

Availablein = legacy
Digest = MD4
Input = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
Output = 043f8582f241db351ce627e153e7f0e4

Availablein = legacy
Digest = MD4
Input = "12345678901234567890123456789012345678901234567890123456789012345678901234567890"
Output = e33b4ddc9c38f2199c3e7b164fcc0536

Title =  MD5 tests

Digest = MD5
Input =
Output = d41d8cd98f00b204e9800998ecf8427e

Digest = MD5
Input = 61
Output = 0cc175b9c0f1b6a831c399e269772661

Digest = MD5
Input = 616263
Output = 900150983cd24fb0d6963f7d28e17f72

Digest = MD5
Input = 6d65737361676520646967657374
Output = f96b697d7cb7938d525a2f31aaf161d0

Digest = MD5
Input = 6162636465666768696a6b6c6d6e6f707172737475767778797a
Output = c3fcd3d76192e4007dfb496cca67e13b

Digest = MD5
Input = 4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839
Output = d174ab98d277d9f5a5611c2c9f419d9f

Digest = MD5
Input = 3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930
Output = 57edf4a22be3c955ac49da2e2107b67a

Title = MD5-SHA1

Digest = MD5-SHA1
Input =
Output = d41d8cd98f00b204e9800998ecf8427eda39a3ee5e6b4b0d3255bfef95601890afd80709

Digest = MD5-SHA1
Input = "abc"
Output = 900150983cd24fb0d6963f7d28e17f72a9993e364706816aba3e25717850c26c9cd0d89d

Digest = MD5-SHA1
Input = "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
Output = 8215ef0796a20bcaaae116d3876c664a84983e441c3bd26ebaae4aa1f95129e5e54670f1
