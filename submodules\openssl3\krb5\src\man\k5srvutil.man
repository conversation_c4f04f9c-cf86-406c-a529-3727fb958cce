.\" Man page generated from reStructuredText.
.
.TH "K5SRVUTIL" "1" " " "1.20" "MIT Kerberos"
.SH NAME
k5srvutil \- host key table (keytab) manipulation utility
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBk5srvutil\fP \fIoperation\fP
[\fB\-i\fP]
[\fB\-f\fP \fIfilename\fP]
[\fB\-e\fP \fIkeysalts\fP]
.SH DESCRIPTION
.sp
k5srvutil allows an administrator to list keys currently in
a keytab, to obtain new keys for a principal currently in a keytab,
or to delete non\-current keys from a keytab.
.sp
\fIoperation\fP must be one of the following:
.INDENT 0.0
.TP
\fBlist\fP
Lists the keys in a keytab, showing version number and principal
name.
.TP
\fBchange\fP
Uses the kadmin protocol to update the keys in the Kerberos
database to new randomly\-generated keys, and updates the keys in
the keytab to match.  If a key\(aqs version number doesn\(aqt match the
version number stored in the Kerberos server\(aqs database, then the
operation will fail.  If the \fB\-i\fP flag is given, k5srvutil will
prompt for confirmation before changing each key.  If the \fB\-k\fP
option is given, the old and new keys will be displayed.
Ordinarily, keys will be generated with the default encryption
types and key salts.  This can be overridden with the \fB\-e\fP
option.  Old keys are retained in the keytab so that existing
tickets continue to work, but \fBdelold\fP should be used after
such tickets expire, to prevent attacks against the old keys.
.TP
\fBdelold\fP
Deletes keys that are not the most recent version from the keytab.
This operation should be used some time after a change operation
to remove old keys, after existing tickets issued for the service
have expired.  If the \fB\-i\fP flag is given, then k5srvutil will
prompt for confirmation for each principal.
.TP
\fBdelete\fP
Deletes particular keys in the keytab, interactively prompting for
each key.
.UNINDENT
.sp
In all cases, the default keytab is used unless this is overridden by
the \fB\-f\fP option.
.sp
k5srvutil uses the kadmin(1) program to edit the keytab in
place.
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kadmin(1), ktutil(1), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
