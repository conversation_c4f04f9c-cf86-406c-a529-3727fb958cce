/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OSSL_ENGINES_E_OSSLTEST_ERR_H
# define OSSL_ENGINES_E_OSSLTEST_ERR_H

# define OSSLTESTerr(f, r) ERR_OSSLTEST_error((f), (r), OPENSSL_FILE, OPENSSL_LINE)


/*
 * OSSLTEST function codes.
 */
# define OSSLTEST_F_BIND_OSSLTEST                         100
# define OSSLTEST_F_OSSLTEST_AES128_INIT_KEY              101

/*
 * OSSLTEST reason codes.
 */
# define OSSLTEST_R_INIT_FAILED                           100

#endif
