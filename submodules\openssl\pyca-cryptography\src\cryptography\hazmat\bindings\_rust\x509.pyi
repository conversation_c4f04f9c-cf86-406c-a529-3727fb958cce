import datetime
import typing

from cryptography import x509

def load_pem_x509_certificate(data: bytes) -> x509.Certificate: ...
def load_der_x509_certificate(data: bytes) -> x509.Certificate: ...
def load_pem_x509_crl(data: bytes) -> x509.CertificateRevocationList: ...
def load_der_x509_crl(data: bytes) -> x509.CertificateRevocationList: ...
def load_pem_x509_csr(data: bytes) -> x509.CertificateSigningRequest: ...
def load_der_x509_csr(data: bytes) -> x509.CertificateSigningRequest: ...
def encode_certificate_extension(ext: x509.Extension) -> bytes: ...
def encode_crl_extension(ext: x509.Extension) -> bytes: ...
def encode_crl_entry_extension(ext: x509.Extension) -> bytes: ...
def encode_name_bytes(name: x509.Name) -> bytes: ...

class Sct: ...
class Certificate: ...
class RevokedCertificate: ...
class CertificateRevocationList: ...
class CertificateSigningRequest: ...
