.\" Man page generated from reStructuredText.
.
.TH "K5LOGIN" "5" " " "1.20" "MIT Kerberos"
.SH NAME
k5login \- Kerberos V5 acl file for host access
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH DESCRIPTION
.sp
The .k5login file, which resides in a user\(aqs home directory, contains
a list of the Kerberos principals.  Anyone with valid tickets for a
principal in the file is allowed host access with the UID of the user
in whose home directory the file resides.  One common use is to place
a .k5login file in root\(aqs home directory, thereby granting system
administrators remote root access to the host via Kerberos.
.SH EXAMPLES
.sp
Suppose the user \fBalice\fP had a .k5login file in her home directory
containing just the following line:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
<EMAIL>
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This would allow \fBbob\fP to use Kerberos network applications, such as
ssh(1), to access \fBalice\fP\(aqs account, using \fBbob\fP\(aqs Kerberos
tickets.  In a default configuration (with \fBk5login_authoritative\fP set
to true in krb5.conf(5)), this .k5login file would not let
\fBalice\fP use those network applications to access her account, since
she is not listed!  With no .k5login file, or with \fBk5login_authoritative\fP
set to false, a default rule would permit the principal \fBalice\fP in the
machine\(aqs default realm to access the \fBalice\fP account.
.sp
Let us further suppose that \fBalice\fP is a system administrator.
Alice and the other system administrators would have their principals
in root\(aqs .k5login file on each host:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
<EMAIL>

joeadmin/<EMAIL>
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
This would allow either system administrator to log in to these hosts
using their Kerberos tickets instead of having to type the root
password.  Note that because \fBbob\fP retains the Kerberos tickets for
his own principal, \<EMAIL>\fP, he would not have any of the
privileges that require \fBalice\fP\(aqs tickets, such as root access to
any of the site\(aqs hosts, or the ability to change \fBalice\fP\(aqs
password.
.SH SEE ALSO
.sp
kerberos(1)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
