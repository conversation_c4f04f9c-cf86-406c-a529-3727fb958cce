#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line.  Lines starting with a pound sign,
# like this prolog, are ignored.


# DES EDE3 CFB1
# echo -n "Hello World" |
#  apps/openssl enc -des-ede3-cfb1 \
#   -K 000102030405060708090A0B0C0D0E0F1011121314151617 -iv 0001020304050607 |
#  xxd -ps -u

Title = DES Tests (various sources)

Cipher = DES-EDE3-CFB1
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
IV = 0001020304050607
Plaintext = "Hello World"
Ciphertext = 3CF55D656E9C0664513358

Cipher = DES-EDE3-CFB1
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
IV = 0001020304050607
Operation = DECRYPT
Plaintext = "Hello World"
Ciphertext = 3CF55D656E9C0664513358

Cipher = DESX-CBC
Key = 0123456789abcdeff1e0d3c2b5a49786fedcba9876543210
IV = fedcba9876543210
Plaintext = 37363534333231204E6F77206973207468652074696D6520666F722000000000
Ciphertext = 846B2914851E9A2954732F8AA0A611C115CDC2D7951B1053A63C5E03B21AA3C4

# DES EDE3 CBC tests (from destest)
Cipher = DES-EDE3-CBC
Key = 0123456789abcdeff1e0d3c2b5a49786fedcba9876543210
IV = fedcba9876543210
Plaintext = 37363534333231204E6F77206973207468652074696D6520666F722000000000
Ciphertext = 3FE301C962AC01D02213763C1CBD4CDC799657C064ECF5D41C673812CFDE9675

# DES ECB tests (from destest)

Cipher = DES-ECB
Key = 0000000000000000
Plaintext = 0000000000000000
Ciphertext = 8CA64DE9C1B123A7

Cipher = DES-ECB
Key = FFFFFFFFFFFFFFFF
Plaintext = FFFFFFFFFFFFFFFF
Ciphertext = 7359B2163E4EDC58

Cipher = DES-ECB
Key = 3000000000000000
Plaintext = 1000000000000001
Ciphertext = 958E6E627A05557B

Cipher = DES-ECB
Key = 1111111111111111
Plaintext = 1111111111111111
Ciphertext = F40379AB9E0EC533

Cipher = DES-ECB
Key = 0123456789ABCDEF
Plaintext = 1111111111111111
Ciphertext = 17668DFC7292532D

Cipher = DES-ECB
Key = 1111111111111111
Plaintext = 0123456789ABCDEF
Ciphertext = 8A5AE1F81AB8F2DD

Cipher = DES-ECB
Key = FEDCBA9876543210
Plaintext = 0123456789ABCDEF
Ciphertext = ED39D950FA74BCC4

Title = AES (from FIPS-197 test vectors)

Cipher = AES-128-ECB
Key = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 69C4E0D86A7B0430D8CDB78070B4C55A

# AES 192 ECB tests (from FIPS-197 test vectors, encrypt)

Cipher = AES-192-ECB
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = DDA97CA4864CDFE06EAF70A0EC0D7191


# AES 256 ECB tests (from FIPS-197 test vectors, encrypt)

Cipher = AES-256-ECB
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 8EA2B7CA516745BFEAFC49904B496089


# AES 128 ECB tests (from NIST test vectors, encrypt)

#AES-128-ECB:00000000000000000000000000000000::00000000000000000000000000000000:C34C052CC0DA8D73451AFE5F03BE297F:1

# AES 128 ECB tests (from NIST test vectors, decrypt)

#AES-128-ECB:00000000000000000000000000000000::44416AC2D1F53C583303917E6BE9EBE0:00000000000000000000000000000000:0

# AES 192 ECB tests (from NIST test vectors, decrypt)

#AES-192-ECB:000000000000000000000000000000000000000000000000::48E31E9E256718F29229319C19F15BA4:00000000000000000000000000000000:0

# AES 256 ECB tests (from NIST test vectors, decrypt)

#AES-256-ECB:0000000000000000000000000000000000000000000000000000000000000000::058CCFFDBBCB382D1F6F56585D8A4ADE:00000000000000000000000000000000:0

# AES 128 CBC tests (from NIST test vectors, encrypt)

#AES-128-CBC:00000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:8A05FC5E095AF4848A08D328D3688E3D:1

# AES 192 CBC tests (from NIST test vectors, encrypt)

#AES-192-CBC:000000000000000000000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:7BD966D53AD8C1BB85D2ADFAE87BB104:1

# AES 256 CBC tests (from NIST test vectors, encrypt)

#AES-256-CBC:0000000000000000000000000000000000000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:FE3C53653E2F45B56FCD88B2CC898FF0:1

# AES 128 CBC tests (from NIST test vectors, decrypt)

#AES-128-CBC:00000000000000000000000000000000:00000000000000000000000000000000:FACA37E0B0C85373DF706E73F7C9AF86:00000000000000000000000000000000:0

# AES tests from NIST document SP800-38A
# For all ECB encrypts and decrypts, the transformed sequence is
#   AES-bits-ECB:key::plaintext:ciphertext:encdec
# ECB-AES128.Encrypt and ECB-AES128.Decrypt

Title = AES tests from NIST document SP800-38A

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3AD77BB40D7A3660A89ECAF32466EF97

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = F5D3D58503B9699DE785895A96FDBAAF

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 43B1CD7F598ECE23881B00E3ED030688

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 7B0C785E27E8AD3F8223207104725DD4

# ECB-AES192.Encrypt and ECB-AES192.Decrypt
Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = BD334F1D6E45F25FF712A214571FA5CC

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 974104846D0AD3AD7734ECB3ECEE4EEF

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = EF7AFD2270E2E60ADCE0BA2FACE6444E

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 9A4B41BA738D6C72FB16691603C18E0E

# ECB-AES256.Encrypt and ECB-AES256.Decrypt
Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = F3EED1BDB5D2A03C064B5A7E3DB181F8

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 591CCB10D410ED26DC5BA74A31362870

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = B6ED21B99CA6F4F9F153E7B1BEAFED1D

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 23304B7A39F9F3FF067D8D8F9E24ECC7

# For all CBC encrypts and decrypts, the transformed sequence is
#   AES-bits-CBC:key:IV/ciphertext':plaintext:ciphertext:encdec
# CBC-AES128.Encrypt and CBC-AES128.Decrypt
Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 7649ABAC8119B246CEE98E9B12E9197D

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 7649ABAC8119B246CEE98E9B12E9197D
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 5086CB9B507219EE95DB113A917678B2

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 5086CB9B507219EE95DB113A917678B2
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 73BED6B8E3C1743B7116E69E22229516

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 73BED6B8E3C1743B7116E69E22229516
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 3FF1CAA1681FAC09120ECA307586E1A7

# CBC-AES192.Encrypt and CBC-AES192.Decrypt
Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 4F021DB243BC633D7178183A9FA071E8

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 4F021DB243BC633D7178183A9FA071E8
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = B4D9ADA9AD7DEDF4E5E738763F69145A

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = B4D9ADA9AD7DEDF4E5E738763F69145A
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 571B242012FB7AE07FA9BAAC3DF102E0

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 571B242012FB7AE07FA9BAAC3DF102E0
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 08B0E27988598881D920A9E64F5615CD

# CBC-AES256.Encrypt and CBC-AES256.Decrypt
Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = F58C4C04D6E5F1BA779EABFB5F7BFBD6

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = F58C4C04D6E5F1BA779EABFB5F7BFBD6
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 9CFC4E967EDB808D679F777BC6702C7D

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 9CFC4E967EDB808D679F777BC6702C7D
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 39F23369A9D9BACFA530E26304231461

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39F23369A9D9BACFA530E26304231461
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = B2EB05E2C39BE9FCDA6C19078C6A9D1B

# We don't support CFB{1,8}-AESxxx.{En,De}crypt
# For all CFB128 encrypts and decrypts, the transformed sequence is
#   AES-bits-CFB:key:IV/ciphertext':plaintext:ciphertext:encdec
# CFB128-AES128.Encrypt
Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 3B3FD92EB72DAD20333449F8E83CFB4A
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = C8A64537A0B3A93FCDE3CDAD9F1CE58B

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = C8A64537A0B3A93FCDE3CDAD9F1CE58B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 26751F67A3CBB140B1808CF187A4F4DF

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 26751F67A3CBB140B1808CF187A4F4DF
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C04B05357C5D1C0EEAC4C66F9FF7F2E6

# CFB128-AES128.Decrypt
Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 3B3FD92EB72DAD20333449F8E83CFB4A
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = C8A64537A0B3A93FCDE3CDAD9F1CE58B

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = C8A64537A0B3A93FCDE3CDAD9F1CE58B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 26751F67A3CBB140B1808CF187A4F4DF

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 26751F67A3CBB140B1808CF187A4F4DF
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C04B05357C5D1C0EEAC4C66F9FF7F2E6

# CFB128-AES192.Encrypt
Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = CDC80D6FDDF18CAB34C25909C99A4174
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 67CE7F7F81173621961A2B70171D3D7A

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 67CE7F7F81173621961A2B70171D3D7A
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C05F9F9CA9834FA042AE8FBA584B09FF

# CFB128-AES192.Decrypt
Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = CDC80D6FDDF18CAB34C25909C99A4174
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 67CE7F7F81173621961A2B70171D3D7A

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 67CE7F7F81173621961A2B70171D3D7A
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C05F9F9CA9834FA042AE8FBA584B09FF

# CFB128-AES256.Encrypt
Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DC7E84BFDA79164B7ECD8486985D3860
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 39FFED143B28B1C832113C6331E5407B

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39FFED143B28B1C832113C6331E5407B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = DF10132415E54B92A13ED0A8267AE2F9

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DF10132415E54B92A13ED0A8267AE2F9
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 75A385741AB9CEF82031623D55B1E471

# CFB128-AES256.Decrypt
Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DC7E84BFDA79164B7ECD8486985D3860
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 39FFED143B28B1C832113C6331E5407B

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39FFED143B28B1C832113C6331E5407B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = DF10132415E54B92A13ED0A8267AE2F9

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DF10132415E54B92A13ED0A8267AE2F9
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 75A385741AB9CEF82031623D55B1E471

# For all OFB encrypts and decrypts, the transformed sequence is
#   AES-bits-CFB:key:IV/output':plaintext:ciphertext:encdec
# OFB-AES128.Encrypt
Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 50FE67CC996D32B6DA0937E99BAFEC60
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 7789508D16918F03F53C52DAC54ED825

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = D9A4DADA0892239F6B8B3D7680E15674
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 9740051E9C5FECF64344F7A82260EDCC

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A78819583F0308E7A6BF36B1386ABF23
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 304C6528F659C77866A510D9C1D6AE5E

# OFB-AES128.Decrypt
Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 50FE67CC996D32B6DA0937E99BAFEC60
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 7789508D16918F03F53C52DAC54ED825

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = D9A4DADA0892239F6B8B3D7680E15674
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 9740051E9C5FECF64344F7A82260EDCC

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A78819583F0308E7A6BF36B1386ABF23
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 304C6528F659C77866A510D9C1D6AE5E

# OFB-AES192.Encrypt
Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = A609B38DF3B1133DDDFF2718BA09565E
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = FCC28B8D4C63837C09E81700C1100401

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 52EF01DA52602FE0975F78AC84BF8A50
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 8D9A9AEAC0F6596F559C6D4DAF59A5F2

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = BD5286AC63AABD7EB067AC54B553F71D
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 6D9F200857CA6C3E9CAC524BD9ACC92A

# OFB-AES192.Decrypt
Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = A609B38DF3B1133DDDFF2718BA09565E
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = FCC28B8D4C63837C09E81700C1100401

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 52EF01DA52602FE0975F78AC84BF8A50
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 8D9A9AEAC0F6596F559C6D4DAF59A5F2

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = BD5286AC63AABD7EB067AC54B553F71D
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 6D9F200857CA6C3E9CAC524BD9ACC92A

# OFB-AES256.Encrypt
Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = B7BF3A5DF43989DD97F0FA97EBCE2F4A
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 4FEBDC6740D20B3AC88F6AD82A4FB08D

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E1C656305ED1A7A6563805746FE03EDC
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 71AB47A086E86EEDF39D1C5BBA97C408

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 41635BE625B48AFC1666DD42A09D96E7
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 0126141D67F37BE8538F5A8BE740E484

# OFB-AES256.Decrypt
Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = B7BF3A5DF43989DD97F0FA97EBCE2F4A
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 4FEBDC6740D20B3AC88F6AD82A4FB08D

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E1C656305ED1A7A6563805746FE03EDC
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 71AB47A086E86EEDF39D1C5BBA97C408

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 41635BE625B48AFC1666DD42A09D96E7
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 0126141D67F37BE8538F5A8BE740E484

Title = AES Counter test vectors from RFC3686

Cipher = aes-128-ctr
Key = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = E4095D4FB7A7B3792D6175A3261311B8

Cipher = aes-128-ctr
Key = 7E24067817FAE0D743D6CE1F32539163
IV = 006CB6DBC0543B59DA48D90B00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 5104A106168A72D9790D41EE8EDAD388EB2E1EFC46DA57C8FCE630DF9141BE28

Cipher = aes-128-ctr
Key = 7691BE035E5020A8AC6E618529F9A0DC
IV = 00E0017B27777F3F4A1786F000000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = C1CF48A89F2FFDD9CF4652E9EFDB72D74540A42BDE6D7836D59A5CEAAEF3105325B2072F

Cipher = aes-192-ctr
Key = 16AF5B145FC9F579C175F93E3BFB0EED863D06CCFDB78515
IV = 0000004836733C147D6D93CB00000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 4B55384FE259C9C84E7935A003CBE928

Cipher = aes-192-ctr
Key = 7C5CB2401B3DC33C19E7340819E0F69C678C3DB8E6F6A91A
IV = 0096B03B020C6EADC2CB500D00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 453243FC609B23327EDFAAFA7131CD9F8490701C5AD4A79CFC1FE0FF42F4FB00

Cipher = aes-192-ctr
Key = 02BF391EE8ECB159B959617B0965279BF59B60A786D3E0FE
IV = 0007BDFD5CBD60278DCC091200000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = 96893FC55E5C722F540B7DD1DDF7E758D288BC95C69165884536C811662F2188ABEE0935

Cipher = aes-256-ctr
Key = 776BEFF2851DB06F4C8A0542C8696F6C6A81AF1EEC96B4D37FC1D689E6C1C104
IV = 00000060DB5672C97AA8F0B200000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 145AD01DBF824EC7560863DC71E3E0C0

Cipher = aes-256-ctr
Key = F6D66D6BD52D59BB0796365879EFF886C66DD51A5B6A99744B50590C87A23884
IV = 00FAAC24C1585EF15A43D87500000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = F05E231B3894612C49EE000B804EB2A9B8306B508F839D6A5530831D9344AF1C

Cipher = aes-256-ctr
Key = FF7A617CE69148E4F1726E2F43581DE2AA62D9F805532EDFF1EED687FB54153D
IV = 001CC5B751A51D70A1C1114800000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = EB6C52821D0BBBF7CE7594462ACA4FAAB407DF866569FD07F48CC0B583D6071F1EC0E6B8

# Self-generated vector to trigger false carry on big-endian platforms
Cipher = aes-128-ctr
Key = 7E24067817FAE0D743D6CE1F32539163
IV = 00000000000000007FFFFFFFFFFFFFFF
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = A2D459477E6432BD74184B1B5370D2243CDC202BC43583B2A55D288CDBBD1E03

# AES CCM 256 bit key
Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2bf
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726

Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2be
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726
Operation = DECRYPT
Result = CIPHERUPDATE_ERROR

# Test that the tag can be set after specifying AAD.
Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2bf
SetTagLate = TRUE
Operation = DECRYPT
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726

# AES GCM test vectors from http://csrc.nist.gov/groups/ST/toolkit/BCM/documents/proposedmodes/gcm/gcm-spec.pdf
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 58e2fccefa7e3061367f1d57a4e7455a
Plaintext =
Ciphertext =

Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = ab6e47d42cec13bdf53a67b21257bddf
Plaintext = 00000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD =
Tag = 4d5c2af327cd64a62cf35abd2ba6fab4
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 42831ec2217774244b7221b784d0d49ce3aa212f2c02a4e035c17e2329aca12e21d514b25466931c7d8f6a5aac84aa051ba30b396a0aac973d58e091473f5985

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 5bc94fbc3221a5db94fae95ae7121a47
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 42831ec2217774244b7221b784d0d49ce3aa212f2c02a4e035c17e2329aca12e21d514b25466931c7d8f6a5aac84aa051ba30b396a0aac973d58e091

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 3612d2e79e3b0785561be14aaca2fccb
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 61353b4c2806934a777ff51fa22a4755699b2a714fcdc6f83766e5f97b6c742373806900e49f24b22b097544d4896b424989b5e1ebac0f07c23f4598

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 619cc5aefffe0bfa462af43c1699d050
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 8ce24998625615b603a033aca13fb894be9112a5c3a211a8ba262a3cca7e2ca701e4a9a4fba43c90ccdcb281d48c7c6fd62875d2aca417034c34aee5

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 619cc5aefffe0bfa462af43c1699d051
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 8ce24998625615b603a033aca13fb894be9112a5c3a211a8ba262a3cca7e2ca701e4a9a4fba43c90ccdcb281d48c7c6fd62875d2aca417034c34aee5
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

Cipher = aes-192-gcm
Key = 000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = cd33b28ac773f74ba00ed1f312572435
Plaintext =
Ciphertext =

Cipher = aes-192-gcm
Key = 000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 2ff58d80033927ab8ef4d4587514f0fb
Plaintext = 00000000000000000000000000000000
Ciphertext = 98e7247c07f0fe411c267e4384b0f600

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbaddecaf888
AAD =
Tag = 9924a7c8587336bfb118024db8674a14
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 3980ca0b3c00e841eb06fac4872a2757859e1ceaa6efd984628593b40ca1e19c7d773d00c144c525ac619d18c84a3f4718e2448b2fe324d9ccda2710acade256

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 2519498e80f1478f37ba55bd6d27618c
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 3980ca0b3c00e841eb06fac4872a2757859e1ceaa6efd984628593b40ca1e19c7d773d00c144c525ac619d18c84a3f4718e2448b2fe324d9ccda2710

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 65dcc57fcf623a24094fcca40d3533f8
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 0f10f599ae14a154ed24b36e25324db8c566632ef2bbb34f8347280fc4507057fddc29df9a471f75c66541d4d4dad1c9e93a19a58e8b473fa0f062f7

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = dcf566ff291c25bbb8568fc3d376a6d9
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = d27e88681ce3243c4830165a8fdcf9ff1de9a1d8e6b447ef6ef7b79828666e4581e79012af34ddd9e2f037589b292db3e67c036745fa22e7e9b7373b

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = dcf566ff291c25bbb8568fc3d376a6d8
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = d27e88681ce3243c4830165a8fdcf9ff1de9a1d8e6b447ef6ef7b79828666e4581e79012af34ddd9e2f037589b292db3e67c036745fa22e7e9b7373b
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

Cipher = aes-256-gcm
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 530f8afbc74536b9a963b4f1c4cb738b
Plaintext =
Ciphertext =

Cipher = aes-256-gcm
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = d0d1c8a799996bf0265b98b5d48ab919
Plaintext = 00000000000000000000000000000000
Ciphertext = cea7403d4d606b6e074ec5d3baf39d18

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD =
Tag = b094dac5d93471bdec1a502270e3cc6c
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662898015ad

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 76fc6ece0f4e1768cddf8853bb2d551b
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 3a337dbf46a792c45e454913fe2ea8f2
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = c3762df1ca787d32ae47c13bf19844cbaf1ae14d0b976afac52ff7d79bba9de0feb582d33934a4f0954cc2363bc73f7862ac430e64abe499f47c9b1f

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = a44a8266ee1c8eb0c8b5d4cf5ae9f19a
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 5a8def2f0c9e53f1f75d7853659e2a20eeb2b22aafde6419a058ab4f6f746bf40fc0c3b780f244452da3ebf1c5d82cdea2418997200ef82e44ae7e3f

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = a44a8266ee1c8eb0c8b5d4cf5ae9f19b
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 5a8def2f0c9e53f1f75d7853659e2a20eeb2b22aafde6419a058ab4f6f746bf40fc0c3b780f244452da3ebf1c5d82cdea2418997200ef82e44ae7e3f
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

# local add-ons, primarily streaming ghash tests
# 128 bytes aad
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662898015ad
Tag = 5fea793a2d6f974d37e68e0cb8ff9492
Plaintext =
Ciphertext =

# 48 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 9dd0a376b08e40eb00c35f29f9ea61a4
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0

# 80 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 98885a3a22bd4742fe7b72172193b163
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0c94da219118e297d7b7ebcbcc9c388f28ade7d85a8ee35616f7124a9d5270291

# 128 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = cac45f60e31efd3b5a43b98a22ce1aa1
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0c94da219118e297d7b7ebcbcc9c388f28ade7d85a8ee35616f7124a9d527029195b84d1b96c690ff2f2de30bf2ec89e00253786e126504f0dab90c48a30321de3345e6b0461e7c9e6c6b7afedde83f40

# 192 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = 566f8ef683078bfdeeffa869d751a017
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606

# 240 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = fd0c7011ff07f0071324bdfb2d0f3a29
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606872ca10dee15b3249b1a1b958f23134c4bccb7d03200bce420a2f8eb66dcf3644d1423c1b5699003c13ecef4bf38a3b6

# 288 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = 8b307f6b33286d0ab026a9ed3fe1e85f
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606872ca10dee15b3249b1a1b958f23134c4bccb7d03200bce420a2f8eb66dcf3644d1423c1b5699003c13ecef4bf38a3b60eedc34033bac1902783dc6d89e2e774188a439c7ebcc0672dbda4ddcfb2794613b0be41315ef778708a70ee7d75165c

# 80 bytes plaintext, submitted by Intel
Cipher = aes-128-gcm
Key = 843ffcf5d2b72694d19ed01d01249412
IV = dbcca32ebf9b804617c3aa9e
AAD = 00000000000000000000000000000000101112131415161718191a1b1c1d1e1f
Tag = 3b629ccfbc1119b7319e1dce2cd6fd6d
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ciphertext = 6268c6fa2a80b2d137467f092f657ac04d89be2beaa623d61b5a868c8f03ff95d3dcee23ad2f1ab3a6c80eaf4b140eb05de3457f0fbc111a6b43d0763aa422a3013cf1dc37fe417d1fbfc449b75d4cc5

#AES OCB Test vectors
Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 197B9C3C441D3C83EAFB2BEF633B9182
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 0001020304050607
Tag = 16DC76A46D47E1EAD537209E8A96D14E
Plaintext = 0001020304050607
Ciphertext = 92B657130A74B85A

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 0001020304050607
Tag = 98B91552C8C009185044E30A6EB2FE21
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 971EFFCAE19AD4716F88E87B871FBEED
Plaintext = 0001020304050607
Ciphertext = 92B657130A74B85A

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F
Tag = 776C9924D6723A1FC4524532AC3E5BEB
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F
Tag = 7DDB8E6CEA6814866212509619B19CC6
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 13CC8B747807121A4CBB3E4BD6B456AF
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Tag = 5FA94FC3F38820F1DC3F3D1FD4E55E1C
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = BEA5E8798DBE7110031C144DA0B26122FCFCEE7A2A8D4D48

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Tag = 282026DA3068BC9FA118681D559F10F6
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 6EF2F52587FDA0ED97DC7EEDE241DF68
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = BEA5E8798DBE7110031C144DA0B26122FCFCEE7A2A8D4D48

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Tag = B2A040DD3BD5164372D76D7BB6824240
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Tag = E1E072633BADE51A60E85951D9C42A1B
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 4A3BAE824465CFDAF8C41FC50C7DF9D9
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 659C623211DEEA0DE30D2C381879F4C8
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB68C65778B058A635

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 7AEB7A69A1687DD082CA27B0D9A37096
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 060C8467F4ABAB5E8B3C2067A2E115DC
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB68C65778B058A635

#AES OCB Non standard test vectors - generated from reference implementation
Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 1b6c44f34e3abb3cbf8976e7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 09a4fd29de949d9a9aa9924248422097ad4883b4713e6c214ff6567ada08a96766fc4e2ee3e3a5a1

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B0C0D0E
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 1ad62009901f40cba7cd7156f94a7324
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 5e2fa7367ffbdb3938845cfd415fcc71ec79634eb31451609d27505f5e2978f43c44213d8fa441ee

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = C203F98CE28F7DAD3F31C021
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F3031
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C822D6

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 8346D7D47C5D893ED472F5AB
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F4041
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F714FF

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 5822A9A70FDF55D29D2984A6
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F5051
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB8294170634D

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 81772B6741ABB4ECA9D2DEB2
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F6061
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7050FAA

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 3E52A01D068DE85456DB03B7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7051CB4824B8114E9A720CBC1CE0185B156B486

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 3E52A01D068DE85456DB03B6
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7051CB4824B8114E9A720CBC1CE0185B156B486
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

#Test vectors generated to validate aesni_ocb_encrypt on x86
Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = C14DFF7D62A13C4A3422456207453190
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B819333

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = D47D84F6FF912C79B6A4223AB9BE2DB8
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC204

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 41970D13737B7BD1B5FBF49ED4412CA5
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = BE0228651ED4E48A11BDED68D953F3A0
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 17BC6E10B16E5FDC52836E7D589518C7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = E84AAC18666116990A3A37B3A5FC55BD
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B11CF99263D693AEBDF8ADE1A1D838DED

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 3E5EA7EE064FE83B313E28D411E91EAD
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B11CF99263D693AEBDF8ADE1A1D838DED48D9E09F452F8E6FBEB76A3DED47611C

Title = AES XTS test vectors from IEEE Std 1619-2007

# Using the same key twice for encryption is always banned.
Cipher = aes-128-xts
Operation = ENCRYPT
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 917cf69ebd68b2ec9b9fe9a3eadda692cd43d2f59598ed858c02c2652fbf922e
Result = KEY_SET_ERROR

# Using the same key twice for decryption is allowed outside of FIPS mode.
Cipher = aes-128-xts
Operation = DECRYPT
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 917cf69ebd68b2ec9b9fe9a3eadda692cd43d2f59598ed858c02c2652fbf922e

Cipher = aes-128-xts
Key = 1111111111111111111111111111111122222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 4444444444444444444444444444444444444444444444444444444444444444
Ciphertext = c454185e6a16936e39334038acef838bfb186fff7480adc4289382ecd6d394f0

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f022222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 4444444444444444444444444444444444444444444444444444444444444444
Ciphertext = af85336b597afc1a900b2eb21ec949d292df4c047e0b21532186a5971a227a89

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f4341332e4ca60482a4ba1a03b3e65008fc5da76b70bf1690db4eae29c5f1badd03c5ccf2a55d705ddcd86d449511ceb7ec30bf12b1fa35b913f9f747a8afd1b130e94bff94effd01a91735ca1726acd0b197c4e5b03393697e126826fb6bbde8ecc1e08298516e2c9ed03ff3c1b7860f6de76d4cecd94c8119855ef5297ca67e9f3e7ff72b1e99785ca0a7e7720c5b36dc6d72cac9574c8cbbc2f801e23e56fd344b07f22154beba0f08ce8891e643ed995c94d9a69c9f1b5f499027a78572aeebd74d20cc39881c213ee770b1010e4bea718846977ae119f7a023ab58cca0ad752afe656bb3c17256a9f6e9bf19fdd5a38fc82bbe872c5539edb609ef4f79c203ebb140f2e583cb2ad15b4aa5b655016a8449277dbd477ef2c8d6c017db738b18deb4a427d1923ce3ff262735779a418f20a282df920147beabe421ee5319d0568

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 01000000000000000000000000000000
Plaintext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f4341332e4ca60482a4ba1a03b3e65008fc5da76b70bf1690db4eae29c5f1badd03c5ccf2a55d705ddcd86d449511ceb7ec30bf12b1fa35b913f9f747a8afd1b130e94bff94effd01a91735ca1726acd0b197c4e5b03393697e126826fb6bbde8ecc1e08298516e2c9ed03ff3c1b7860f6de76d4cecd94c8119855ef5297ca67e9f3e7ff72b1e99785ca0a7e7720c5b36dc6d72cac9574c8cbbc2f801e23e56fd344b07f22154beba0f08ce8891e643ed995c94d9a69c9f1b5f499027a78572aeebd74d20cc39881c213ee770b1010e4bea718846977ae119f7a023ab58cca0ad752afe656bb3c17256a9f6e9bf19fdd5a38fc82bbe872c5539edb609ef4f79c203ebb140f2e583cb2ad15b4aa5b655016a8449277dbd477ef2c8d6c017db738b18deb4a427d1923ce3ff262735779a418f20a282df920147beabe421ee5319d0568
Ciphertext = 264d3ca8512194fec312c8c9891f279fefdd608d0c027b60483a3fa811d65ee59d52d9e40ec5672d81532b38b6b089ce951f0f9c35590b8b978d175213f329bb1c2fd30f2f7f30492a61a532a79f51d36f5e31a7c9a12c286082ff7d2394d18f783e1a8e72c722caaaa52d8f065657d2631fd25bfd8e5baad6e527d763517501c68c5edc3cdd55435c532d7125c8614deed9adaa3acade5888b87bef641c4c994c8091b5bcd387f3963fb5bc37aa922fbfe3df4e5b915e6eb514717bdd2a74079a5073f5c4bfd46adf7d282e7a393a52579d11a028da4d9cd9c77124f9648ee383b1ac763930e7162a8d37f350b2f74b8472cf09902063c6b32e8c2d9290cefbd7346d1c779a0df50edcde4531da07b099c638e83a755944df2aef1aa31752fd323dcb710fb4bfbb9d22b925bc3577e1b8949e729a90bbafeacf7f7879e7b1147e28ba0bae940db795a61b15ecf4df8db07b824bb062802cc98a9545bb2aaeed77cb3fc6db15dcd7d80d7d5bc406c4970a3478ada8899b329198eb61c193fb6275aa8ca340344a75a862aebe92eee1ce032fd950b47d7704a3876923b4ad62844bf4a09c4dbe8b4397184b7471360c9564880aedddb9baa4af2e75394b08cd32ff479c57a07d3eab5d54de5f9738b8d27f27a9f0ab11799d7b7ffefb2704c95c6ad12c39f1e867a4b7b1d7818a4b753dfd2a89ccb45e001a03a867b187f225dd

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 02000000000000000000000000000000
Plaintext = 264d3ca8512194fec312c8c9891f279fefdd608d0c027b60483a3fa811d65ee59d52d9e40ec5672d81532b38b6b089ce951f0f9c35590b8b978d175213f329bb1c2fd30f2f7f30492a61a532a79f51d36f5e31a7c9a12c286082ff7d2394d18f783e1a8e72c722caaaa52d8f065657d2631fd25bfd8e5baad6e527d763517501c68c5edc3cdd55435c532d7125c8614deed9adaa3acade5888b87bef641c4c994c8091b5bcd387f3963fb5bc37aa922fbfe3df4e5b915e6eb514717bdd2a74079a5073f5c4bfd46adf7d282e7a393a52579d11a028da4d9cd9c77124f9648ee383b1ac763930e7162a8d37f350b2f74b8472cf09902063c6b32e8c2d9290cefbd7346d1c779a0df50edcde4531da07b099c638e83a755944df2aef1aa31752fd323dcb710fb4bfbb9d22b925bc3577e1b8949e729a90bbafeacf7f7879e7b1147e28ba0bae940db795a61b15ecf4df8db07b824bb062802cc98a9545bb2aaeed77cb3fc6db15dcd7d80d7d5bc406c4970a3478ada8899b329198eb61c193fb6275aa8ca340344a75a862aebe92eee1ce032fd950b47d7704a3876923b4ad62844bf4a09c4dbe8b4397184b7471360c9564880aedddb9baa4af2e75394b08cd32ff479c57a07d3eab5d54de5f9738b8d27f27a9f0ab11799d7b7ffefb2704c95c6ad12c39f1e867a4b7b1d7818a4b753dfd2a89ccb45e001a03a867b187f225dd
Ciphertext = fa762a3680b76007928ed4a4f49a9456031b704782e65e16cecb54ed7d017b5e18abd67b338e81078f21edb7868d901ebe9c731a7c18b5e6dec1d6a72e078ac9a4262f860beefa14f4e821018272e411a951502b6e79066e84252c3346f3aa62344351a291d4bedc7a07618bdea2af63145cc7a4b8d4070691ae890cd65733e7946e9021a1dffc4c59f159425ee6d50ca9b135fa6162cea18a939838dc000fb386fad086acce5ac07cb2ece7fd580b00cfa5e98589631dc25e8e2a3daf2ffdec26531659912c9d8f7a15e5865ea8fb5816d6207052bd7128cd743c12c8118791a4736811935eb982a532349e31dd401e0b660a568cb1a4711f552f55ded59f1f15bf7196b3ca12a91e488ef59d64f3a02bf45239499ac6176ae321c4a211ec545365971c5d3f4f09d4eb139bfdf2073d33180b21002b65cc9865e76cb24cd92c874c24c18350399a936ab3637079295d76c417776b94efce3a0ef7206b15110519655c956cbd8b2489405ee2b09a6b6eebe0c53790a12a8998378b33a5b71159625f4ba49d2a2fdba59fbf0897bc7aabd8d707dc140a80f0f309f835d3da54ab584e501dfa0ee977fec543f74186a802b9a37adb3e8291eca04d66520d229e60401e7282bef486ae059aa70696e0e305d777140a7a883ecdcb69b9ff938e8a4231864c69ca2c2043bed007ff3e605e014bcf518138dc3a25c5e236171a2d01d6

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = fd000000000000000000000000000000
Plaintext = 8e41b78c390b5af9d758bb214a67e9f6bf7727b09ac6124084c37611398fa45daad94868600ed391fb1acd4857a95b466e62ef9f4b377244d1c152e7b30d731aad30c716d214b707aed99eb5b5e580b3e887cf7497465651d4b60e6042051da3693c3b78c14489543be8b6ad0ba629565bba202313ba7b0d0c94a3252b676f46cc02ce0f8a7d34c0ed229129673c1f61aed579d08a9203a25aac3a77e9db60267996db38df637356d9dcd1632e369939f2a29d89345c66e05066f1a3677aef18dea4113faeb629e46721a66d0a7e785d3e29af2594eb67dfa982affe0aac058f6e15864269b135418261fc3afb089472cf68c45dd7f231c6249ba0255e1e033833fc4d00a3fe02132d7bc3873614b8aee34273581ea0325c81f0270affa13641d052d36f0757d484014354d02d6883ca15c24d8c3956b1bd027bcf41f151fd8023c5340e5606f37e90fdb87c86fb4fa634b3718a30bace06a66eaf8f63c4aa3b637826a87fe8cfa44282e92cb1615af3a28e53bc74c7cba1a0977be9065d0c1a5dec6c54ae38d37f37aa35283e048e5530a85c4e7a29d7b92ec0c3169cdf2a805c7604bce60049b9fb7b8eaac10f51ae23794ceba68bb58112e293b9b692ca721b37c662f8574ed4dba6f88e170881c82cddc1034a0ca7e284bf0962b6b26292d836fa9f73c1ac770eef0f2d3a1eaf61d3e03555fd424eedd67e18a18094f888
Ciphertext = d55f684f81f4426e9fde92a5ff02df2ac896af63962888a97910c1379e20b0a3b1db613fb7fe2e07004329ea5c22bfd33e3dbe4cf58cc608c2c26c19a2e2fe22f98732c2b5cb844cc6c0702d91e1d50fc4382a7eba5635cd602432a2306ac4ce82f8d70c8d9bc15f918fe71e74c622d5cf71178bf6e0b9cc9f2b41dd8dbe441c41cd0c73a6dc47a348f6702f9d0e9b1b1431e948e299b9ec2272ab2c5f0c7be86affa5dec87a0bee81d3d50007edaa2bcfccb35605155ff36ed8edd4a40dcd4b243acd11b2b987bdbfaf91a7cac27e9c5aea525ee53de7b2d3332c8644402b823e94a7db26276d2d23aa07180f76b4fd29b9c0823099c9d62c519880aee7e9697617c1497d47bf3e571950311421b6b734d38b0db91eb85331b91ea9f61530f54512a5a52a4bad589eb69781d537f23297bb459bdad2948a29e1550bf4787e0be95bb173cf5fab17dab7a13a052a63453d97ccec1a321954886b7a1299faaeecae35c6eaaca753b041b5e5f093bf83397fd21dd6b3012066fcc058cc32c3b09d7562dee29509b5839392c9ff05f51f3166aaac4ac5f238038a3045e6f72e48ef0fe8bc675e82c318a268e43970271bf119b81bf6a982746554f84e72b9f00280a320a08142923c23c883423ff949827f29bbacdc1ccdb04938ce6098c95ba6b32528f4ef78eed778b2e122ddfd1cbdd11d1c0a6783e011fc536d63d053260637

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = fe000000000000000000000000000000
Plaintext = d55f684f81f4426e9fde92a5ff02df2ac896af63962888a97910c1379e20b0a3b1db613fb7fe2e07004329ea5c22bfd33e3dbe4cf58cc608c2c26c19a2e2fe22f98732c2b5cb844cc6c0702d91e1d50fc4382a7eba5635cd602432a2306ac4ce82f8d70c8d9bc15f918fe71e74c622d5cf71178bf6e0b9cc9f2b41dd8dbe441c41cd0c73a6dc47a348f6702f9d0e9b1b1431e948e299b9ec2272ab2c5f0c7be86affa5dec87a0bee81d3d50007edaa2bcfccb35605155ff36ed8edd4a40dcd4b243acd11b2b987bdbfaf91a7cac27e9c5aea525ee53de7b2d3332c8644402b823e94a7db26276d2d23aa07180f76b4fd29b9c0823099c9d62c519880aee7e9697617c1497d47bf3e571950311421b6b734d38b0db91eb85331b91ea9f61530f54512a5a52a4bad589eb69781d537f23297bb459bdad2948a29e1550bf4787e0be95bb173cf5fab17dab7a13a052a63453d97ccec1a321954886b7a1299faaeecae35c6eaaca753b041b5e5f093bf83397fd21dd6b3012066fcc058cc32c3b09d7562dee29509b5839392c9ff05f51f3166aaac4ac5f238038a3045e6f72e48ef0fe8bc675e82c318a268e43970271bf119b81bf6a982746554f84e72b9f00280a320a08142923c23c883423ff949827f29bbacdc1ccdb04938ce6098c95ba6b32528f4ef78eed778b2e122ddfd1cbdd11d1c0a6783e011fc536d63d053260637
Ciphertext = 72efc1ebfe1ee25975a6eb3aa8589dda2b261f1c85bdab442a9e5b2dd1d7c3957a16fc08e526d4b1223f1b1232a11af274c3d70dac57f83e0983c498f1a6f1aecb021c3e70085a1e527f1ce41ee5911a82020161529cd82773762daf5459de94a0a82adae7e1703c808543c29ed6fb32d9e004327c1355180c995a07741493a09c21ba01a387882da4f62534b87bb15d60d197201c0fd3bf30c1500a3ecfecdd66d8721f90bcc4c17ee925c61b0a03727a9c0d5f5ca462fbfa0af1c2513a9d9d4b5345bd27a5f6e653f751693e6b6a2b8ead57d511e00e58c45b7b8d005af79288f5c7c22fd4f1bf7a898b03a5634c6a1ae3f9fae5de4f296a2896b23e7ed43ed14fa5a2803f4d28f0d3ffcf24757677aebdb47bb388378708948a8d4126ed1839e0da29a537a8c198b3c66ab00712dd261674bf45a73d67f76914f830ca014b65596f27e4cf62de66125a5566df9975155628b400fbfb3a29040ed50faffdbb18aece7c5c44693260aab386c0a37b11b114f1c415aebb653be468179428d43a4d8bc3ec38813eca30a13cf1bb18d524f1992d44d8b1a42ea30b22e6c95b199d8d182f8840b09d059585c31ad691fa0619ff038aca2c39a943421157361717c49d322028a74648113bd8c9d7ec77cf3c89c1ec8718ceff8516d96b34c3c614f10699c9abc4ed0411506223bea16af35c883accdbe1104eef0cfdb54e12fb230a

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = ff000000000000000000000000000000
Plaintext = 72efc1ebfe1ee25975a6eb3aa8589dda2b261f1c85bdab442a9e5b2dd1d7c3957a16fc08e526d4b1223f1b1232a11af274c3d70dac57f83e0983c498f1a6f1aecb021c3e70085a1e527f1ce41ee5911a82020161529cd82773762daf5459de94a0a82adae7e1703c808543c29ed6fb32d9e004327c1355180c995a07741493a09c21ba01a387882da4f62534b87bb15d60d197201c0fd3bf30c1500a3ecfecdd66d8721f90bcc4c17ee925c61b0a03727a9c0d5f5ca462fbfa0af1c2513a9d9d4b5345bd27a5f6e653f751693e6b6a2b8ead57d511e00e58c45b7b8d005af79288f5c7c22fd4f1bf7a898b03a5634c6a1ae3f9fae5de4f296a2896b23e7ed43ed14fa5a2803f4d28f0d3ffcf24757677aebdb47bb388378708948a8d4126ed1839e0da29a537a8c198b3c66ab00712dd261674bf45a73d67f76914f830ca014b65596f27e4cf62de66125a5566df9975155628b400fbfb3a29040ed50faffdbb18aece7c5c44693260aab386c0a37b11b114f1c415aebb653be468179428d43a4d8bc3ec38813eca30a13cf1bb18d524f1992d44d8b1a42ea30b22e6c95b199d8d182f8840b09d059585c31ad691fa0619ff038aca2c39a943421157361717c49d322028a74648113bd8c9d7ec77cf3c89c1ec8718ceff8516d96b34c3c614f10699c9abc4ed0411506223bea16af35c883accdbe1104eef0cfdb54e12fb230a
Ciphertext = 3260ae8dad1f4a32c5cafe3ab0eb95549d461a67ceb9e5aa2d3afb62dece0553193ba50c75be251e08d1d08f1088576c7efdfaaf3f459559571e12511753b07af073f35da06af0ce0bbf6b8f5ccc5cea500ec1b211bd51f63b606bf6528796ca12173ba39b8935ee44ccce646f90a45bf9ccc567f0ace13dc2d53ebeedc81f58b2e41179dddf0d5a5c42f5d8506c1a5d2f8f59f3ea873cbcd0eec19acbf325423bd3dcb8c2b1bf1d1eaed0eba7f0698e4314fbeb2f1566d1b9253008cbccf45a2b0d9c5c9c21474f4076e02be26050b99dee4fd68a4cf890e496e4fcae7b70f94ea5a9062da0daeba1993d2ccd1dd3c244b8428801495a58b216547e7e847c46d1d756377b6242d2e5fb83bf752b54e0df71e889f3a2bb0f4c10805bf3c590376e3c24e22ff57f7fa965577375325cea5d920db94b9c336b455f6e894c01866fe9fbb8c8d3f70a2957285f6dfb5dcd8cbf54782f8fe7766d4723819913ac773421e3a31095866bad22c86a6036b2518b2059b4229d18c8c2ccbdf906c6cc6e82464ee57bddb0bebcb1dc645325bfb3e665ef7251082c88ebb1cf203bd779fdd38675713c8daadd17e1cabee432b09787b6ddf3304e38b731b45df5df51b78fcfb3d32466028d0ba36555e7e11ab0ee0666061d1645d962444bc47a38188930a84b4d561395c73c087021927ca638b7afc8a8679ccb84c26555440ec7f10445cd


Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ff000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 1c3b3a102f770386e4836c99e370cf9bea00803f5e482357a4ae12d414a3e63b5d31e276f8fe4a8d66b317f9ac683f44680a86ac35adfc3345befecb4bb188fd5776926c49a3095eb108fd1098baec70aaa66999a72a82f27d848b21d4a741b0c5cd4d5fff9dac89aeba122961d03a757123e9870f8acf1000020887891429ca2a3e7a7d7df7b10355165c8b9a6d0a7de8b062c4500dc4cd120c0f7418dae3d0b5781c34803fa75421c790dfe1de1834f280d7667b327f6c8cd7557e12ac3a0f93ec05c52e0493ef31a12d3d9260f79a289d6a379bc70c50841473d1a8cc81ec583e9645e07b8d9670655ba5bbcfecc6dc3966380ad8fecb17b6ba02469a020a84e18e8f84252070c13e9f1f289be54fbc481457778f616015e1327a02b140f1505eb309326d68378f8374595c849d84f4c333ec4423885143cb47bd71c5edae9be69a2ffeceb1bec9de244fbe15992b11b77c040f12bd8f6a975a44a0f90c29a9abc3d4d893927284c58754cce294529f8614dcd2aba991925fedc4ae74ffac6e333b93eb4aff0479da9a410e4450e0dd7ae4c6e2910900575da401fc07059f645e8b7e9bfdef33943054ff84011493c27b3429eaedb4ed5376441a77ed43851ad77f16f541dfd269d50d6a5f14fb0aab1cbb4c1550be97f7ab4066193c4caa773dad38014bd2092fa755c824bb5e54c4f36ffda9fcea70b9c6e693e148c151

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffff0000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 77a31251618a15e6b92d1d66dffe7b50b50bad552305ba0217a610688eff7e11e1d0225438e093242d6db274fde801d4cae06f2092c728b2478559df58e837c2469ee4a4fa794e4bbc7f39bc026e3cb72c33b0888f25b4acf56a2a9804f1ce6d3d6e1dc6ca181d4b546179d55544aa7760c40d06741539c7e3cd9d2f6650b2013fd0eeb8c2b8e3d8d240ccae2d4c98320a7442e1c8d75a42d6e6cfa4c2eca1798d158c7aecdf82490f24bb9b38e108bcda12c3faf9a21141c3613b58367f922aaa26cd22f23d708dae699ad7cb40a8ad0b6e2784973dcb605684c08b8d6998c69aac049921871ebb65301a4619ca80ecb485a31d744223ce8ddc2394828d6a80470c092f5ba413c3378fa6054255c6f9df4495862bbb3287681f931b687c888abf844dfc8fc28331e579928cd12bd2390ae123cf03818d14dedde5c0c24c8ab018bfca75ca096f2d531f3d1619e785f1ada437cab92e980558b3dce1474afb75bfedbf8ff54cb2618e0244c9ac0d3c66fb51598cd2db11f9be39791abe447c63094f7c453b7ff87cb5bb36b7c79efb0872d17058b83b15ab0866ad8a58656c5a7e20dbdf308b2461d97c0ec0024a2715055249cf3b478ddd4740de654f75ca686e0d7345c69ed50cdc2a8b332b1f8824108ac937eb050585608ee734097fc09054fbff89eeaeea791f4a7ab1f9868294a4f9e27b42af8100cb9d59cef9645803

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffff00000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = e387aaa58ba483afa7e8eb469778317ecf4cf573aa9d4eac23f2cdf914e4e200a8b490e42ee646802dc6ee2b471b278195d60918ececb44bf79966f83faba0499298ebc699c0c8634715a320bb4f075d622e74c8c932004f25b41e361025b5a87815391f6108fc4afa6a05d9303c6ba68a128a55705d415985832fdeaae6c8e19110e84d1b1f199a2692119edc96132658f09da7c623efcec712537a3d94c0bf5d7e352ec94ae5797fdb377dc1551150721adf15bd26a8efc2fcaad56881fa9e62462c28f30ae1ceaca93c345cf243b73f542e2074a705bd2643bb9f7cc79bb6e7091ea6e232df0f9ad0d6cf502327876d82207abf2115cdacf6d5a48f6c1879a65b115f0f8b3cb3c59d15dd8c769bc014795a1837f3901b5845eb491adfefe097b1fa30a12fc1f65ba22905031539971a10f2f36c321bb51331cdefb39e3964c7ef079994f5b69b2edd83a71ef549971ee93f44eac3938fcdd61d01fa71799da3a8091c4c48aa9ed263ff0749df95d44fef6a0bb578ec69456aa5408ae32c7af08ad7ba8921287e3bbee31b767be06a0e705c864a769137df28292283ea81a2480241b44d9921cdbec1bc28dc1fda114bd8e5217ac9d8ebafa720e9da4f9ace231cc949e5b96fe76ffc21063fddc83a6b8679c00d35e09576a875305bed5f36ed242c8900dd1fa965bc950dfce09b132263a1eef52dd6888c309f5a7d712826

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffffff000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = bf53d2dade78e822a4d949a9bc6766b01b06a8ef70d26748c6a7fc36d80ae4c5520f7c4ab0ac8544424fa405162fef5a6b7f229498063618d39f0003cb5fb8d1c86b643497da1ff945c8d3bedeca4f479702a7a735f043ddb1d6aaade3c4a0ac7ca7f3fa5279bef56f82cd7a2f38672e824814e10700300a055e1630b8f1cb0e919f5e942010a416e2bf48cb46993d3cb6a51c19bacf864785a00bc2ecff15d350875b246ed53e68be6f55bd7e05cfc2b2ed6432198a6444b6d8c247fab941f569768b5c429366f1d3f00f0345b96123d56204c01c63b22ce78baf116e525ed90fdea39fa469494d3866c31e05f295ff21fea8d4e6e13d67e47ce722e9698a1c1048d68ebcde76b86fcf976eab8aa9790268b7068e017a8b9b749409514f1053027fd16c3786ea1bac5f15cb79711ee2abe82f5cf8b13ae73030ef5b9e4457e75d1304f988d62dd6fc4b94ed38ba831da4b7634971b6cd8ec325d9c61c00f1df73627ed3745a5e8489f3a95c69639c32cd6e1d537a85f75cc844726e8a72fc0077ad22000f1d5078f6b866318c668f1ad03d5a5fced5219f2eabbd0aa5c0f460d183f04404a0d6f469558e81fab24a167905ab4c7878502ad3e38fdbe62a41556cec37325759533ce8f25f367c87bb5578d667ae93f9e2fd99bcbc5f2fbba88cf6516139420fcff3b7361d86322c4bd84c82f335abb152c4a93411373aaa8220

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffffffff0000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 64497e5a831e4a932c09be3e5393376daa599548b816031d224bbf50a818ed2350eae7e96087c8a0db51ad290bd00c1ac1620857635bf246c176ab463be30b808da548081ac847b158e1264be25bb0910bbc92647108089415d45fab1b3d2604e8a8eff1ae4020cfa39936b66827b23f371b92200be90251e6d73c5f86de5fd4a950781933d79a28272b782a2ec313efdfcc0628f43d744c2dc2ff3dcb66999b50c7ca895b0c64791eeaa5f29499fb1c026f84ce5b5c72ba1083cddb5ce45434631665c333b60b11593fb253c5179a2c8db813782a004856a1653011e93fb6d876c18366dd8683f53412c0c180f9c848592d593f8609ca736317d356e13e2bff3a9f59cd9aeb19cd482593d8c46128bb32423b37a9adfb482b99453fbe25a41bf6feb4aa0bef5ed24bf73c762978025482c13115e4015aac992e5613a3b5c2f685b84795cb6e9b2656d8c88157e52c42f978d8634c43d06fea928f2822e465aa6576e9bf419384506cc3ce3c54ac1a6f67dc66f3b30191e698380bc999b05abce19dc0c6dcc2dd001ec535ba18deb2df1a101023108318c75dc98611a09dc48a0acdec676fabdf222f07e026f059b672b56e5cbc8e1d21bbd867dd927212054681d70ea737134cdfce93b6f82ae22423274e58a0821cc5502e2d0ab4585e94de6975be5e0b4efce51cd3e70c25a1fbbbd609d273ad5b0d59631c531f6a0a57b9


Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f10
Ciphertext = 6c1625db4671522d3d7599601de7ca09ed

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f1011
Ciphertext = d069444b7a7e0cab09e24447d24deb1fedbf

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112
Ciphertext = e5df1351c0544ba1350b3363cd8ef4beedbf9d

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f10111213
Ciphertext = 9d84c813f719aa2c7be3f66171c7c5c2edbf9dac

Cipher = aes-128-xts
Key = e0e1e2e3e4e5e6e7e8e9eaebecedeeefc0c1c2c3c4c5c6c7c8c9cacbcccdcecf
IV = 21436587a90000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 38b45812ef43a05bd957e545907e223b954ab4aaf088303ad910eadf14b42be68b2461149d8c8ba85f992be970bc621f1b06573f63e867bf5875acafa04e42ccbd7bd3c2a0fb1fff791ec5ec36c66ae4ac1e806d81fbf709dbe29e471fad38549c8e66f5345d7c1eb94f405d1ec785cc6f6a68f6254dd8339f9d84057e01a17741990482999516b5611a38f41bb6478e6f173f320805dd71b1932fc333cb9ee39936beea9ad96fa10fb4112b901734ddad40bc1878995f8e11aee7d141a2f5d48b7a4e1e7f0b2c04830e69a4fd1378411c2f287edf48c6c4e5c247a19680f7fe41cefbd49b582106e3616cbbe4dfb2344b2ae9519391f3e0fb4922254b1d6d2d19c6d4d537b3a26f3bcc51588b32f3eca0829b6a5ac72578fb814fb43cf80d64a233e3f997a3f02683342f2b33d25b492536b93becb2f5e1a8b82f5b883342729e8ae09d16938841a21a97fb543eea3bbff59f13c1a18449e398701c1ad51648346cbc04c27bb2da3b93a1372ccae548fb53bee476f9e9c91773b1bb19828394d55d3e1a20ed69113a860b6829ffa847224604435070221b257e8dff783615d2cae4803a93aa4334ab482a0afac9c0aeda70b45a481df5dec5df8cc0f423c77a5fd46cd312021d4b438862419a791be03bb4d97c0e59578542531ba466a83baf92cefc151b5cc1611a167893819b63fb8a6b18e86de60290fa72b797b0ce59f3

# Exercise different lengths covering even ciphertext stealing cases
Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5B079C6307EA0914559C6D2FB6384F8AADF94

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce84

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f7071
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CEF4F253466EF4953ADC8FE2F5BC1FF57593FD

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad0265

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE842973C68248EDDFE26FB9B096659C8A5D6BB7

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD0265C4DD16E65A24575A709F174593F19FF85EA9

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE519215FA160C664D4B07D757A034AB3B35A10C

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f91

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE51A10C421110E6D81588EDE82103A252D82C6CBC24F9357BD1FB882AA4B2CC2E7FA750

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebf
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f434

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE51A10C421110E6D81588EDE82103A252D8A750E8768DEFFFED9122810AAEB99F910409B03D164E727C31290FD4E039500872AF

# AES wrap tests from RFC3394
Cipher = id-aes128-wrap
Key = 000102030405060708090A0B0C0D0E0F
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 1FA68B0A8112B447AEF34BD8FB5A7B829D3E862371D2CFE5

Cipher = id-aes192-wrap
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 96778B25AE6CA435F92B5B97C050AED2468AB8A17AD84E5D

Cipher = id-aes256-wrap
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 64E8C3F9CE0F5BA263E9777905818A2A93C8191E7D6E8AE7

Cipher = id-aes192-wrap
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 00112233445566778899AABBCCDDEEFF0001020304050607
Ciphertext = 031D33264E15D33268F24EC260743EDCE1C6C7DDEE725A936BA814915C6762D2

Cipher = id-aes256-wrap
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF0001020304050607
Ciphertext = A8F9BC1612C68B3FF6E6F4FBE30E71E4769C8B80A32CB8958CD5D17D6B254DA1

Cipher = id-aes256-wrap
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF000102030405060708090A0B0C0D0E0F
Ciphertext = 28C9F404C4B810F4CBCCB35CFB87F8263F5786E2D80ED326CBC7F0E71A99F43BFB988B9B7A02DD21

# Same as previous example but with invalid unwrap key: should be rejected
# without returning any plaintext
Cipher = id-aes256-wrap
Operation = DECRYPT
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E00
Plaintext = 00112233445566778899AABBCCDDEEFF000102030405060708090A0B0C0D0E0F
Ciphertext = 28C9F404C4B810F4CBCCB35CFB87F8263F5786E2D80ED326CBC7F0E71A99F43BFB988B9B7A02DD21
Result = CIPHERUPDATE_ERROR


# AES wrap tests from RFC5649
Cipher = id-aes192-wrap-pad
Key = 5840df6e29b02af1ab493b705bf16ea1ae8338f4dcc176a8
Plaintext = c37b7e6492584340bed12207808941155068f738
Ciphertext = 138bdeaa9b8fa7fc61f97742e72248ee5ae6ae5360d1ae6a5f54f373fa543b6a

Cipher = id-aes192-wrap-pad
Key = 5840df6e29b02af1ab493b705bf16ea1ae8338f4dcc176a8
Plaintext = 466f7250617369
Ciphertext = afbeb0f07dfbf5419200f2ccb50bb24f

Title = RC4 tests

Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 0123456789abcdef
Ciphertext = 75b7878099e0c596

Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 0000000000000000
Ciphertext = 7494c2e7104b0879

Cipher = RC4
Key = 00000000000000000000000000000000
Plaintext = 0000000000000000
Ciphertext = de188941a3375d3a

Cipher = RC4
Key = ef012345ef012345ef012345ef012345
Plaintext = 0000000000000000000000000000000000000000
Ciphertext = d6a141a7ec3c38dfbd615a1162e1c7ba36b67858

Cipher = RC4
Key = 0123456789abcdef0123456789abcdef
Plaintext = 123456789ABCDEF0123456789ABCDEF0123456789ABCDEF012345678
Ciphertext = 66a0949f8af7d6891f7f832ba833c00c892ebe30143ce28740011ecf

Cipher = RC4
Key = ef012345ef012345ef012345ef012345
Plaintext = 00000000000000000000
Ciphertext = d6a141a7ec3c38dfbd61

Title = Camellia tests from RFC3713

# For all ECB encrypts and decrypts, the transformed sequence is
#   CAMELLIA-bits-ECB:key::plaintext:ciphertext:encdec
Cipher = CAMELLIA-128-ECB
Key = 0123456789abcdeffedcba9876543210
Plaintext = 0123456789abcdeffedcba9876543210
Ciphertext = 67673138549669730857065648eabe43

Cipher = CAMELLIA-192-ECB
Key = 0123456789abcdeffedcba98765432100011223344556677
Plaintext = 0123456789abcdeffedcba9876543210
Ciphertext = b4993401b3e996f84ee5cee7d79b09b9

Cipher = CAMELLIA-256-ECB
Key = 0123456789abcdeffedcba987654321000112233445566778899aabbccddeeff
Plaintext = 0123456789abcdeffedcba9876543210
Ciphertext = 9acc237dff16d76c20ef7c919e3a7509

# ECB-CAMELLIA128.Encrypt
Cipher = CAMELLIA-128-ECB
Key = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 77CF412067AF8270613529149919546F

Cipher = CAMELLIA-192-ECB
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = B22F3C36B72D31329EEE8ADDC2906C68

Cipher = CAMELLIA-256-ECB
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 2EDF1F3418D53B88841FC8985FB1ECF2


# ECB-CAMELLIA128.Encrypt and ECB-CAMELLIA128.Decrypt
Cipher = CAMELLIA-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 432FC5DCD628115B7C388D770B270C96

Cipher = CAMELLIA-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 0BE1F14023782A22E8384C5ABB7FAB2B

Cipher = CAMELLIA-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = A0A1ABCD1893AB6FE0FE5B65DF5F8636

Cipher = CAMELLIA-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = E61925E0D5DFAA9BB29F815B3076E51A


# ECB-CAMELLIA192.Encrypt and ECB-CAMELLIA192.Decrypt
Cipher = CAMELLIA-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CCCC6C4E138B45848514D48D0D3439D3

Cipher = CAMELLIA-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 5713C62C14B2EC0F8393B6AFD6F5785A

Cipher = CAMELLIA-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = B40ED2B60EB54D09D030CF511FEEF366

Cipher = CAMELLIA-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 909DBD95799096748CB27357E73E1D26


# ECB-CAMELLIA256.Encrypt and ECB-CAMELLIA256.Decrypt
Cipher = CAMELLIA-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = BEFD219B112FA00098919CD101C9CCFA

Cipher = CAMELLIA-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = C91D3A8F1AEA08A9386CF4B66C0169EA

Cipher = CAMELLIA-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = A623D711DC5F25A51BB8A80D56397D28

Cipher = CAMELLIA-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 7960109FB6DC42947FCFE59EA3C5EB6B


# For all CBC encrypts and decrypts, the transformed sequence is
#   CAMELLIA-bits-CBC:key:IV/ciphertext':plaintext:ciphertext:encdec
# CBC-CAMELLIA128.Encrypt and CBC-CAMELLIA128.Decrypt
Cipher = CAMELLIA-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 1607CF494B36BBF00DAEB0B503C831AB

Cipher = CAMELLIA-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 1607CF494B36BBF00DAEB0B503C831AB
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = A2F2CF671629EF7840C5A5DFB5074887

Cipher = CAMELLIA-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A2F2CF671629EF7840C5A5DFB5074887
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 0F06165008CF8B8B5A63586362543E54

Cipher = CAMELLIA-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 36A84CDAFD5F9A85ADA0F0A993D6D577
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 74C64268CDB8B8FAF5B34E8AF3732980


# CBC-CAMELLIA192.Encrypt and CBC-CAMELLIA192.Decrypt
Cipher = CAMELLIA-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 2A4830AB5AC4A1A2405955FD2195CF93

Cipher = CAMELLIA-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 2A4830AB5AC4A1A2405955FD2195CF93
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 5D5A869BD14CE54264F892A6DD2EC3D5

Cipher = CAMELLIA-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 5D5A869BD14CE54264F892A6DD2EC3D5
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 37D359C3349836D884E310ADDF68C449

Cipher = CAMELLIA-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 37D359C3349836D884E310ADDF68C449
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 01FAAA930B4AB9916E9668E1428C6B08


# CBC-CAMELLIA256.Encrypt and CBC-CAMELLIA256.Decrypt
Cipher = CAMELLIA-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = E6CFA35FC02B134A4D2C0B6737AC3EDA

Cipher = CAMELLIA-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E6CFA35FC02B134A4D2C0B6737AC3EDA
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 36CBEB73BD504B4070B1B7DE2B21EB50

Cipher = CAMELLIA-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 36CBEB73BD504B4070B1B7DE2B21EB50
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = E31A6055297D96CA3330CDF1B1860A83

Cipher = CAMELLIA-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E31A6055297D96CA3330CDF1B1860A83
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 5D563F6D1CCCF236051C0C5C1C58F28F


# We don't support CFB{1,8}-CAMELLIAxxx.{En,De}crypt
# For all CFB128 encrypts and decrypts, the transformed sequence is
#   CAMELLIA-bits-CFB:key:IV/ciphertext':plaintext:ciphertext:encdec
# CFB128-CAMELLIA128.Encrypt
Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 14F7646187817EB586599146B82BD719

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 14F7646187817EB586599146B82BD719
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = A53D28BB82DF741103EA4F921A44880B

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A53D28BB82DF741103EA4F921A44880B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 9C2157A664626D1DEF9EA420FDE69B96

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 9C2157A664626D1DEF9EA420FDE69B96
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 742A25F0542340C7BAEF24CA8482BB09


# CFB128-CAMELLIA128.Decrypt
Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 14F7646187817EB586599146B82BD719

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 14F7646187817EB586599146B82BD719
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = A53D28BB82DF741103EA4F921A44880B

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A53D28BB82DF741103EA4F921A44880B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 9C2157A664626D1DEF9EA420FDE69B96

Cipher = CAMELLIA-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 9C2157A664626D1DEF9EA420FDE69B96
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 742A25F0542340C7BAEF24CA8482BB09


# CFB128-CAMELLIA192.Encrypt
Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = C832BB9780677DAA82D9B6860DCD565E

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = C832BB9780677DAA82D9B6860DCD565E
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 86F8491627906D780C7A6D46EA331F98

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 86F8491627906D780C7A6D46EA331F98
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 69511CCE594CF710CB98BB63D7221F01

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 69511CCE594CF710CB98BB63D7221F01
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = D5B5378A3ABED55803F25565D8907B84


# CFB128-CAMELLIA192.Decrypt
Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = C832BB9780677DAA82D9B6860DCD565E

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = C832BB9780677DAA82D9B6860DCD565E
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 86F8491627906D780C7A6D46EA331F98

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 86F8491627906D780C7A6D46EA331F98
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 69511CCE594CF710CB98BB63D7221F01

Cipher = CAMELLIA-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 69511CCE594CF710CB98BB63D7221F01
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = D5B5378A3ABED55803F25565D8907B84


# CFB128-CAMELLIA256.Encrypt
Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CF6107BB0CEA7D7FB1BD31F5E7B06C93

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = CF6107BB0CEA7D7FB1BD31F5E7B06C93
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 89BEDB4CCDD864EA11BA4CBE849B5E2B

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 89BEDB4CCDD864EA11BA4CBE849B5E2B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 555FC3F34BDD2D54C62D9E3BF338C1C4

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 555FC3F34BDD2D54C62D9E3BF338C1C4
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 5953ADCE14DB8C7F39F1BD39F359BFFA


# CFB128-CAMELLIA256.Decrypt
Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CF6107BB0CEA7D7FB1BD31F5E7B06C93

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = CF6107BB0CEA7D7FB1BD31F5E7B06C93
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 89BEDB4CCDD864EA11BA4CBE849B5E2B

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 89BEDB4CCDD864EA11BA4CBE849B5E2B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 555FC3F34BDD2D54C62D9E3BF338C1C4

Cipher = CAMELLIA-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 555FC3F34BDD2D54C62D9E3BF338C1C4
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 5953ADCE14DB8C7F39F1BD39F359BFFA


# For all OFB encrypts and decrypts, the transformed sequence is
#   CAMELLIA-bits-OFB:key:IV/output':plaintext:ciphertext:encdec
# OFB-CAMELLIA128.Encrypt
Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 14F7646187817EB586599146B82BD719

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 50FE67CC996D32B6DA0937E99BAFEC60
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 25623DB569CA51E01482649977E28D84

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = D9A4DADA0892239F6B8B3D7680E15674
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = C776634A60729DC657D12B9FCA801E98

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A78819583F0308E7A6BF36B1386ABF23
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = D776379BE0E50825E681DA1A4C980E8E


# OFB-CAMELLIA128.Decrypt
Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 14F7646187817EB586599146B82BD719

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 50FE67CC996D32B6DA0937E99BAFEC60
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 25623DB569CA51E01482649977E28D84

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = D9A4DADA0892239F6B8B3D7680E15674
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = C776634A60729DC657D12B9FCA801E98

Cipher = CAMELLIA-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A78819583F0308E7A6BF36B1386ABF23
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = D776379BE0E50825E681DA1A4C980E8E


# OFB-CAMELLIA192.Encrypt
Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = C832BB9780677DAA82D9B6860DCD565E

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = A609B38DF3B1133DDDFF2718BA09565E
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 8ECEB7D0350D72C7F78562AEBDF99339

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 52EF01DA52602FE0975F78AC84BF8A50
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = BDD62DBBB9700846C53B507F544696F0

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = BD5286AC63AABD7EB067AC54B553F71D
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = E28014E046B802F385C4C2E13EAD4A72


# OFB-CAMELLIA192.Decrypt
Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = C832BB9780677DAA82D9B6860DCD565E

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = A609B38DF3B1133DDDFF2718BA09565E
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 8ECEB7D0350D72C7F78562AEBDF99339

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 52EF01DA52602FE0975F78AC84BF8A50
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = BDD62DBBB9700846C53B507F544696F0

Cipher = CAMELLIA-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = BD5286AC63AABD7EB067AC54B553F71D
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = E28014E046B802F385C4C2E13EAD4A72


# OFB-CAMELLIA256.Encrypt
Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CF6107BB0CEA7D7FB1BD31F5E7B06C93

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = B7BF3A5DF43989DD97F0FA97EBCE2F4A
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 127AD97E8E3994E4820027D7BA109368

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E1C656305ED1A7A6563805746FE03EDC
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 6BFF6265A6A6B7A535BC65A80B17214E

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 41635BE625B48AFC1666DD42A09D96E7
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 0A4A0404E26AA78A27CB271E8BF3CF20


# OFB-CAMELLIA256.Decrypt
Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CF6107BB0CEA7D7FB1BD31F5E7B06C93

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = B7BF3A5DF43989DD97F0FA97EBCE2F4A
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 127AD97E8E3994E4820027D7BA109368

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E1C656305ED1A7A6563805746FE03EDC
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 6BFF6265A6A6B7A535BC65A80B17214E

Cipher = CAMELLIA-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 41635BE625B48AFC1666DD42A09D96E7
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 0A4A0404E26AA78A27CB271E8BF3CF20


# Camellia test vectors from RFC5528
Cipher = CAMELLIA-128-CTR
Key = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = D09DC29A8214619A20877C76DB1F0B3F

Cipher = CAMELLIA-128-CTR
Key = 7E24067817FAE0D743D6CE1F32539163
IV = 006CB6DBC0543B59DA48D90B00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = DBF3C78DC08396D4DA7C907765BBCB442B8E8E0F31F0DCA72C7417E35360E048

Cipher = CAMELLIA-128-CTR
Key = 7691BE035E5020A8AC6E618529F9A0DC
IV = 00E0017B27777F3F4A1786F000000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = B19D1FCDCB75EB882F849CE24D85CF739CE64B2B5C9D73F14F2D5D9DCE9889CDDF508696

Cipher = CAMELLIA-192-CTR
Key = 16AF5B145FC9F579C175F93E3BFB0EED863D06CCFDB78515
IV = 0000004836733C147D6D93CB00000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 2379399E8A8D2B2B16702FC78B9E9696

Cipher = CAMELLIA-192-CTR
Key = 7C5CB2401B3DC33C19E7340819E0F69C678C3DB8E6F6A91A
IV = 0096B03B020C6EADC2CB500D00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 7DEF34F7A5D0E415674B7FFCAE67C75DD018B86FF23051E056392A99F35A4CED

Cipher = CAMELLIA-192-CTR
Key = 02BF391EE8ECB159B959617B0965279BF59B60A786D3E0FE
IV = 0007BDFD5CBD60278DCC091200000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = 5710E556E1487A20B5AC0E73F19E4E7876F37FDC91B1EF4D4DADE8E666A64D0ED557AB57

Cipher = CAMELLIA-256-CTR
Key = 776BEFF2851DB06F4C8A0542C8696F6C6A81AF1EEC96B4D37FC1D689E6C1C104
IV = 00000060DB5672C97AA8F0B200000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 3401F9C8247EFFCEBD6994714C1BBB11

Cipher = CAMELLIA-256-CTR
Key = F6D66D6BD52D59BB0796365879EFF886C66DD51A5B6A99744B50590C87A23884
IV = 00FAAC24C1585EF15A43D87500000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = D6C30392246F7808A83C2B22A8839E45E51CD48A1CDF406EBC9CC2D3AB834108

Cipher = CAMELLIA-256-CTR
Key = FF7A617CE69148E4F1726E2F43581DE2AA62D9F805532EDFF1EED687FB54153D
IV = 001CC5B751A51D70A1C1114800000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = A4DA23FCE6A5FFAA6D64AE9A0652A42CD161A34B65F9679F75C01F101F71276F15EF0D8D

Title = SM4 test vectors from IETF draft-ribose-cfrg-sm4

Cipher = SM4-ECB
Key = 0123456789ABCDEFFEDCBA9876543210
Plaintext  = 0123456789ABCDEFFEDCBA9876543210
Ciphertext = 681EDF34D206965E86B3E94F536E4246

Cipher = SM4-CBC
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 2677F46B09C122CC975533105BD4A22AF6125F7275CE552C3A2BBCF533DE8A3B

Cipher = SM4-OFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A7056F2075D28B5235F58D50027E4177D2BCE

Cipher = SM4-CFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A70569ED258A85A0467CC92AAB393DD978995

Cipher = SM4-CTR
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = AAAAAAAAAAAAAAAABBBBBBBBBBBBBBBBCCCCCCCCCCCCCCCCDDDDDDDDDDDDDDDDEEEEEEEEEEEEEEEEFFFFFFFFFFFFFFFFEEEEEEEEEEEEEEEEAAAAAAAAAAAAAAAA
Ciphertext = C2B4759E78AC3CF43D0852F4E8D5F9FD7256E8A5FCB65A350EE00630912E44492A0B17E1B85B060D0FBA612D8A95831638B361FD5FFACD942F081485A83CA35D

Title = ARIA test vectors from RFC5794 (and others)

Cipher = ARIA-128-ECB
Key = 000102030405060708090a0b0c0d0e0f
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = d718fbd6ab644c739da95f3be6451778

Cipher = ARIA-192-ECB
Key = 000102030405060708090a0b0c0d0e0f1011121314151617
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = 26449c1805dbe7aa25a468ce263a9e79

Cipher = ARIA-256-ECB
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = f92bd7c79fb72e2f2b8f80c1972d24fc

# Additional ARIA mode vectors from http://*************/ARIA/doc/ARIA-testvector-e.pdf
Cipher = ARIA-128-ECB
Key = 00112233445566778899aabbccddeeff
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = c6ecd08e22c30abdb215cf74e2075e6e29ccaac63448708d331b2f816c51b17d9e133d1528dbf0af5787c7f3a3f5c2bf6b6f345907a3055612ce072ff54de7d788424da6e8ccfe8172b391be499354165665ba7864917000a6eeb2ecb4a698edfc7887e7f556377614ab0a282293e6d884dbb84206cdb16ed1754e77a1f243fd086953f752cc1e46c7c794ae85537dcaec8dd721f55c93b6edfe2adea43873e8

Cipher = ARIA-128-CBC
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 49d61860b14909109cef0d22a9268134fadf9fb23151e9645fba75018bdb1538b53334634bbf7d4cd4b5377033060c155fe3948ca75de1031e1d85619e0ad61eb419a866b3c2dbfd10a4ed18b22149f75897f0b8668b0c1c542c687778835fb7cd46e45f85eaa7072437dd9fa6793d6f8d4ccefc4eb1ac641ac1bd30b18c6d64c49bca137eb21c2e04da62712ca2b4f540c57112c38791852cfac7a5d19ed83a

Cipher = ARIA-128-CFB
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 3720e53ba7d615383406b09f0a05a200c07c21e6370f413a5d132500a68285017c61b434c7b7ca9685a51071861e4d4bb873b599b479e2d573dddeafba89f812ac6a9e44d554078eb3be94839db4b33da3f59c063123a7ef6f20e10579fa4fd239100ca73b52d4fcafeadee73f139f78f9b7614c2b3b9dbe010f87db06a89a9435f79ce8121431371f4e87b984e0230c22a6dacb32fc42dcc6accef33285bf11

Cipher = ARIA-128-CFB8
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 373c8f6a965599ec785cc8f8149f6c81b632ccb8e0c6eb6a9707ae52c59257a41f94701c1096933127a90195ed0c8e98690547572423bb45c3d70e4a18ee56b967c10e000ba4df5fba7c404134a343d8375d04b151d161ef83417fe1748447d30a6723c406733df7d18aa39a20752d2381942e244811bb97f72eae446b1815aa690cd1b1adcbd007c0088ecdc91cb2e2caf0e11e72459878137eea64ac62a9a1

Cipher = ARIA-128-OFB
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 3720e53ba7d615383406b09f0a05a2000063063f0560083483faeb041c8adecef30cf80cefb002a0d280759168ec01db3d49f61aced260bd43eec0a2731730eec6fa4f2304319cf8ccac2d7be7833e4f8ae6ce967012c1c6badc5d28e7e4144f6bf5cebe01253ee202afce4bc61f28dec069a6f16f6c8a7dd2afae44148f6ff4d0029d5c607b5fa6b8c8a6301cde5c7033565cd0b8f0974ab490b236197ba04a

Cipher = ARIA-128-CTR
Key = 00112233445566778899aabbccddeeff
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = ac5d7de805a0bf1c57c854501af60fa11497e2a34519dea1569e91e5b5ccae2ff3bfa1bf975f4571f48be191613546c3911163c085f871f0e7ae5f2a085b81851c2a3ddf20ecb8fa51901aec8ee4ba32a35dab67bb72cd9140ad188a967ac0fbbdfa94ea6cce47dcf8525ab5a814cfeb2bb60ee2b126e2d9d847c1a9e96f9019e3e6a7fe40d3829afb73db1cc245646addb62d9b907baaafbe46a73dbc131d3d

Cipher = ARIA-192-ECB
Key = 00112233445566778899aabbccddeeff0011223344556677
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 8d1470625f59ebacb0e55b534b3e462b5f23d33bff78f46c3c15911f4a21809aaccad80b4bda915aa9dae6bcebe06a6c83f77fd5391acfe61de2f646b5d447edbfd5bb49b12fbb9145b227895a757b2af1f7188734863d7b8b6ede5a5b2f06a0a233c8523d2db778fb31b0e311f32700152f33861e9d040c83b5eb40cd88ea49975709dc629365a189f78a3ec40345fc6a5a307a8f9a4413091e007eca5645a0

Cipher = ARIA-192-CBC
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = afe6cf23974b533c672a826264ea785f4e4f7f780dc7f3f1e0962b80902386d514e9c3e77259de92dd1102ffab086c1ea52a71260db5920a83295c25320e421147ca45d532f327b856ea947cd2196ae2e040826548b4c891b0ed0ca6e714dbc4631998d548110d666b3d54c2a091955c6f05beb4f62309368696c9791fc4c551564a2637f194346ec45fbca6c72a5b4612e208d531d6c34cc5c64eac6bd0cf8c

Cipher = ARIA-192-CFB
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 4171f7192bf4495494d2736129640f5c4d87a9a213664c9448477c6ecc2013598d9766952dd8c3868f17e36ef66fd84bfa45d1593d2d6ee3ea2115047d710d4fb66187caa3a315b3c8ea2d313962edcfe5a3e2028d5ba9a09fd5c65c19d3440e477f0cab0628ec6902c73ee02f1afee9f80115be7b9df82d1e28228e28581a20560e195cbb9e2b327bf56fd2d0ae5502e42c13e9b4015d4da42dc859252e7da4

Cipher = ARIA-192-CFB8
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 411d3b4f57f705aa4d13c46e2cf426af7c8c916ed7923d889f0047bbf11471b6d54f8757ef519339105be3cb69babb976a57d5631fc23cc3051fe9d36e8b8e27a2b2c0c4d31928ccbf30ea8239b46ba1b77f6198e7ecd2ce27b35958148e826f06aaf385bd30362ff141583e7c1d8924d44d36a1133094074631e18adafa9d2e55de98f6895c89d4266ebd33f3d4be5153a96fa12132ece2e81e66e55baa7ade

Cipher = ARIA-192-OFB
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 4171f7192bf4495494d2736129640f5cc224d26d364b5a06ddde13d0f1e74faa846de354c63cda77469d1a2d425c47ff41734c71b3fa1fcdc11e0b2de22bfeed54898e233df652c75ae136e61de6524e62b3f806fb2e8e616eb410a1b9500537e327ffb04f19f7f82fde2b122100261f81b82723bf936be7beaaf3067d1c036001f1ade71422268d274d7dc6c6ae1970b27a5f2c2f39c1d241fe8cac5ccd74e9

Cipher = ARIA-192-CTR
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 08625ca8fe569c19ba7af3760a6ed1cef4d199263e999dde14082dbba7560b79a4c6b456b8707dce751f9854f18893dfdb3f4e5afa539733e6f1e70b98ba37891f8f81e95df8efc26c7ce043504cb18958b865e4e316cd2aa1c97f31bf23dc046ef326b95a692a191ba0f2a41c5fe9ae070f236ff7078e703b42666caafbdd20bad74ac4c20c0f46c7ca24c151716575c947da16c90cfe1bf217a41cfebe7531

Cipher = ARIA-256-ECB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 58a875e6044ad7fffa4f58420f7f442d8e191016f28e79aefc01e204773280d7018e5f7a938ec30711719953bae86542cd7ebc752474c1a5f6eaaace2a7e29462ee7dfa5afdb84177ead95ccd4b4bb6e1ed17b9534cff0a5fc2941429cfee2ee49c7adbeb7e9d1b0d2a8531d942079596a27ed79f5b1dd13ecd604b07a48885a3afa0627a0e4e60a3c703af292f1baa77b702f16c54aa74bc727ea95c7468b00

Cipher = ARIA-256-CBC
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 523a8a806ae621f155fdd28dbc34e1ab7b9b42432ad8b2efb96e23b13f0a6e52f36185d50ad002c5f601bee5493f118b243ee2e313642bffc3902e7b2efd9a12fa682edd2d23c8b9c5f043c18b17c1ec4b5867918270fbec1027c19ed6af833da5d620994668ca22f599791d292dd6273b2959082aafb7a996167cce1eec5f0cfd15f610d87e2dda9ba68ce1260ca54b222491418374294e7909b1e8551cd8de

Cipher = ARIA-256-CFB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26834705b0f2c0e2588d4a7f09009635f28bb93d8c31f870ec1e0bdb082b66fa402dd9c202be300c4517d196b14d4ce11dce97f7aaba54341b0d872cc9b63753a3e8556a14be6f7b3e27e3cfc39caf80f2a355aa50dc83c09c7b11828694f8e4aa726c528976b53f2c877f4991a3a8d28adb63bd751846ffb2350265e179d4990753ae8485ff9b4133ddad5875b84a90cbcfa62a045d726df71b6bda0eeca0be

Cipher = ARIA-256-CFB8
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26baa33651e1f66434fec88ef27fd2b9a79e246dd89a3ffa00e8bdb37155433e6c24bd0b87d9a85baa9f485ccb984f5ec24d6a3ef5e3c81396177f039cf580dfdb55d6e1c47a28921dfe369e12fd357b289ad3a5544e1c1bd616d454db9c5f91f603373f29d5b2ed1b4b51de80f28537bbd43d5e3b5dd071dc91153cbbe732dfc325821b06ed8acaae656dcf2da9f13e4f29db671476f1e644ff06d9b67d6bd4

Cipher = ARIA-256-OFB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26834705b0f2c0e2588d4a7f0900963584c256815c4292b59f8d3f966a75b52345b4f5f98c785d3f368a8d5ff89b7f950ceab3cd63773c2621d652b8ef98b4196afb2c2b30496bc5b7d9e7f9084f9d855f63a511751c8909e7a6deadbe0a67a4fb89383ca5d209c6f66f793fc471195c476fb9c1eab2ac91e680e454b4f3ed9a67fb52f09c29b965b23cfa6f3f6bbb2a86c6cdbaa2857bf2486f543231892a52

Cipher = ARIA-256-CTR
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 30026c329666141721178b99c0a1f1b2f06940253f7b3089e2a30ea86aa3c88f5940f05ad7ee41d71347bb7261e348f18360473fdf7d4e7723bffb4411cc13f6cdd89f3bc7b9c768145022c7a74f14d7c305cd012a10f16050c23f1ae5c23f45998d13fbaa041e51619577e0772764896a5d4516d8ffceb3bf7e05f613edd9a60cdcedaff9cfcaf4e00d445a54334f73ab2cad944e51d266548e61c6eb0aa1cd

Title = ARIA GCM test vectors from RFC8269

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 5abace3f37f5a736f4be984bbffbedc1
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 4d8a9a0675550c704b17d8c9ddc81a5cd6f7da34f2fe1b3db7cb3dfb9697102ea0f3c1fc2dbc873d44bceeae8e4442974ba21ff6789d3272613fb9631a7cf3f14bacbeb421633a90ffbe58c2fa6bdca534f10d0de0502ce1d531b6336e58878278531e5c22bc6c85bbd784d78d9e680aa19031aaf89101d669d7a3965c1f7e16229d7463e0535f4e253f5d18187d40b8ae0f564bd970b5e7e2adfb211e89a953

Cipher = ARIA-256-GCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = e210d6ced2cf430ff841472915e7ef48
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 6f9e4bcbc8c85fc0128fb1e4a0a20cb9932ff74581f54fc013dd054b19f99371425b352d97d3f337b90b63d1b082adeeea9d2d7391897d591b985e55fb50cb5350cf7d38dc27dda127c078a149c8eb98083d66363a46e3726af217d3a00275ad5bf772c7610ea4c23006878f0ee69a8397703169a419303f40b72e4573714d19e2697df61e7c7252e5abc6bade876ac4961bfac4d5e867afca351a48aed52822

Title = ARIA GCM self-generated test vectors

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Shorter than default IV
IV = 0001020304
AAD = 8008315ebf2e6fe020e8f5eb
Tag = ebaa2645bb154542117ee46031aa176e
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 1723ccfc0ed44a12520473cfeb63bc933cd450a943f5f1cba78e19d72f80cc102acc51f2459a06cf6435182b8ddd451f83e13479efe5ec7dfbf16229f4017920fb41457a9b6fe1a401b30b2f332d827ae2f86e962326927c1ed8bfedac1f7a00ddde63bd392a8f28a488ba5974689f8d15b9b1739fb50aae0ff244026ec72064003c621b33ffc8086b0a97eefb70604a2826f6499f6eb12d67a0da03fc8e1482

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Longer than default IV
IV = 000102030405060708090a0b0c0d0e0f
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 61f7f44c7da3c60195b29ae0b46051a4
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 0d3e98fcaf7a2c4fe9198d66add90d113e5e0ff47598c40a4bf501960d935a4156c9a4d46c9358a608e10a16479a4247c9ab9bb4a02809e3eac3571b832590fe2ca3e2d545741e36282d96c041fc7d39a46ed60214c2c0ec70f27768dfea4f9563b5d5c2ac33b1368a78f2908f5daf942433fec6ab588f09e908e95cc8dfa85d1a0dfd5835dc14e148323230c63eedc99a9ce942214cb3768b97b821d613629f

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Extra long IV
IV = 000102030405060708090a0b0c0d0e0f1011
AAD = 8008315ebf2e6fe020e8f5eb
Tag = c8b31ab6c2ddccab06b76af4e56e664e
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 616a7bce24206501082cef7267c09a4affa54f8f82eb7fb2cdebdcaab4b6ab05c37e891c2d0fc90d15c5fb684247625c8bc0befad86896ae1c8f5a8506954caba4e13df0a0eb23853d4474e7f3b2c57bb398456a24d198e14566bce8a5f8d3bcdb12994d2fdc0f5cf19aeff990c1fe119e01f9fcc86757b1d43a9accf7b2f913c2208a46c1967f403867f89b46ffe96864c63f042265806ea5270e0dddd0e8dd


Title = ARIA CCM test vectors from IETF draft-ietf-avtcore-aria-srtp-02

# 16-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 40f04b6467e300f6b336aedf9df4185b
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 87b6bd222c55365a9c7d0b215b77ea41 
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# 8-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = dd2282c93a67fe4b
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 828dc0088f99a7ef
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# 12-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 01f3dedd15238da5ebfb1590
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 3615b7f90a651de15da20fb6
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# Test that the tag can be set after specifying AAD.
Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 3615b7f90a651de15da20fb6
SetTagLate = TRUE
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1


Title = SEED test vectors from RFC4269

Cipher = SEED-ECB
Key = 00000000000000000000000000000000
Operation = DECRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 5EBAC6E0054E166819AFF1CC6D346CDB

Cipher = SEED-ECB
Key = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 00000000000000000000000000000000
Ciphertext = C11F22F20140505084483597E4370F43

Cipher = SEED-ECB
Key = 4706480851E61BE85D74BFB3FD956185
Operation = DECRYPT
Plaintext = 83A2F8A288641FB9A4E9A5CC2F131C7D
Ciphertext = EE54D13EBCAE706D226BC3142CD40D4A

Cipher = SEED-ECB
Key = 28DBC3BC49FFD87DCFA509B11D422BE7
Operation = DECRYPT
Plaintext = B41E6BE2EBA84A148E2EED84593C5EC7
Ciphertext = 9B9B7BFCD1813CB95D0B3618F40F5122

Cipher = SEED-ECB
Key = 00000000000000000000000000000000
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 5EBAC6E0054E166819AFF1CC6D346CDB

Cipher = SEED-ECB
Key = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 00000000000000000000000000000000
Ciphertext = C11F22F20140505084483597E4370F43

Cipher = SEED-ECB
Key = 4706480851E61BE85D74BFB3FD956185
Operation = ENCRYPT
Plaintext = 83A2F8A288641FB9A4E9A5CC2F131C7D
Ciphertext = EE54D13EBCAE706D226BC3142CD40D4A

Cipher = SEED-ECB
Key = 28DBC3BC49FFD87DCFA509B11D422BE7
Operation = ENCRYPT
Plaintext = B41E6BE2EBA84A148E2EED84593C5EC7
Ciphertext = 9B9B7BFCD1813CB95D0B3618F40F5122

Title = Chacha20 test vectors from RFC7539

# A.1 Test Vector 1
Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 76b8e0ada0f13d90405d6ae55386bd28bdd219b8a08ded1aa836efcc8b770dc7da41597c5157488d7724e03fb8d84a376a43b8f41518a11cc387b669b2ee6586

# A.1 Test Vector 2
Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 01000000000000000000000000000000
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 9f07e7be5551387a98ba977c732d080dcb0f29a048e3656912c6533e32ee7aed29b721769ce64e43d57133b074d839d531ed1f28510afb45ace10a1f4b794d6f

# A.2 Test Vector 1 is the same as A.1 Test Vector 1
# A.2 Test Vector 2
Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000001
#Counter (first 4 bytes) expressed in little-endian order
IV = 01000000000000000000000000000002
Plaintext = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Ciphertext = a3fbf07df3fa2fde4f376ca23e82737041605d9f4f4f57bd8cff2c1d4b7955ec2a97948bd3722915c8f3d337f7d370050e9e96d647b7c39f56e031ca5eb6250d4042e02785ececfa4b4bb5e8ead0440e20b6e8db09d881a7c6132f420e52795042bdfa7773d8a9051447b3291ce1411c680465552aa6c405b7764d5e87bea85ad00f8449ed8f72d0d662ab052691ca66424bc86d2df80ea41f43abf937d3259dc4b2d0dfb48a6c9139ddd7f76966e928e635553ba76c5c879d7b35d49eb2e62b0871cdac638939e25e8a1e0ef9d5280fa8ca328b351c3c765989cbcf3daa8b6ccc3aaf9f3979c92b3720fc88dc95ed84a1be059c6499b9fda236e7e818b04b0bc39c1e876b193bfe5569753f88128cc08aaa9b63d1a16f80ef2554d7189c411f5869ca52c5b83fa36ff216b9c1d30062bebcfd2dc5bce0911934fda79a86f6e698ced759c3ff9b6477338f3da4f9cd8514ea9982ccafb341b2384dd902f3d1ab7ac61dd29c6f21ba5b862f3730e37cfdc4fd806c22f221

# A.2 Test Vector 3
Cipher = chacha20
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
#Counter (first 4 bytes) expressed in little-endian order
IV = 2a000000000000000000000000000002
Plaintext = 2754776173206272696c6c69672c20616e642074686520736c6974687920746f7665730a446964206779726520616e642067696d626c6520696e2074686520776162653a0a416c6c206d696d737920776572652074686520626f726f676f7665732c0a416e6420746865206d6f6d65207261746873206f757467726162652e
Ciphertext = 62e6347f95ed87a45ffae7426f27a1df5fb69110044c0d73118effa95b01e5cf166d3df2d721caf9b21e5fb14c616871fd84c54f9d65b283196c7fe4f60553ebf39c6402c42234e32a356b3e764312a61a5532055716ead6962568f87d3f3f7704c6a8d1bcd1bf4d50d6154b6da731b187b58dfd728afa36757a797ac188d1

Title = Chacha20

Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000001
IV = 00000000000000000000000000000000
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 4540f05a9f1fb296d7736e7b208e3c96eb4fe1834688d2604f450952ed432d41bbe2a0b6ea7566d2a5d1e7e20d42af2c53d792b1c43fea817e9ad275ae546963

Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000001
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = de9cba7bf3d69ef5e786dc63973f653a0b49e015adbff7134fcb7df137821031e85a050278a7084527214f73efc7fa5b5277062eb7a0433e445f41e31afab757

Cipher = chacha20
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000100000000000000
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = ef3fdfd6c61578fbf5cf35bd3dd33b8009631634d21e42ac33960bd138e50d32111e4caf237ee53ca8ad6426194a88545ddc497a0b466e7d6bbdb0041b2f586b

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c9

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c730

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444a

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37C7C5E6A87478BF41EE85A518C0F4EFA9BDE828C5A71B8E46597B634AFD204D3C501334239C3414285ED72D3A9169EABBD4DC25D52BB7516D3BA712D75AD8C0AE

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37C7C5E6A87478BF41EE85A518C0F4EFA9BDE828C5A71B8E46597B634AFD204D3C501334239C3414285ED72D3A9169EABBD4DC25D52BB7516D3BA712D75AD8C0AE5D493C19E38A77939E7A058D713E9CCCCA58045F436B434B1C80D365472406E392951987DB6905C80D431DA18451135BE7E82BCAB358CB3971E61405B2FF1798

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37C7C5E6A87478BF41EE85A518C0F4EFA9BDE828C5A71B8E46597B634AFD204D3C501334239C3414285ED72D3A9169EABBD4DC25D52BB7516D3BA712D75AD8C0AE5D493C19E38A77939E7A058D713E9CCCCA58045F436B434B1C80D365472406E392951987DB6905C80D431DA18451135BE7E82BCAB358CB3971E61405B2FF17980D6E7E67E861E28201C1EE30B441040FD06878D65042C95582A4318207BFC700BE0CE32889AEC2FFE5085E8967910D879FA0E8C0FF85FDC510B9FF2FBF87CFCB

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37C7C5E6A87478BF41EE85A518C0F4EFA9BDE828C5A71B8E46597B634AFD204D3C501334239C3414285ED72D3A9169EABBD4DC25D52BB7516D3BA712D75AD8C0AE5D493C19E38A77939E7A058D713E9CCCCA58045F436B434B1C80D365472406E392951987DB6905C80D431DA18451135BE7E82BCAB358CB3971E61405B2FF17980D6E7E67E861E28201C1EE30B441040FD06878D65042C95582A4318207BFC700BE0CE32889AEC2FFE5085E8967910D879FA0E8C0FF85FDC510B9FF2FBF87CFCB29577D68099E04FFA05F752A73D377C70D3A8BC2DA80E6E780EC057182C33AD1DE387252258A1E18E6FAD910327CE7F42FD1E1E0515F9586E2F2EFCB9F472B1D

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = f798a189f195e66982105ffb640bb7757f579da31602fc93ec01ac56f85ac3c134a4547b733b46413042c9440049176905d3be59ea1c53f15916155c2be8241a38008b9a26bc35941e2444177c8ade6689de95264986d95889fb60e84629c9bd9a5acb1cc118be563eb9b3a4a472f82e09a7e778492b562ef7130e88dfe031c79db9d4f7c7a899151b9a475032b63fc385245fe054e3dd5a97a5f576fe064025d3ce042c566ab2c507b138db853e3d6959660996546cc9c4a6eafdc777c040d70eaf46f76dad3979e5c5360c3317166a1c894c94a371876a94df7628fe4eaaf2ccb27d5aaae0ad7ad0f9d4b6ad3b54098746d4524d38407a6deb3ab78fab78c94213668bbbd394c5de93b853178addd6b97f9fa1ec3e56c00c9ddff0a44a204241175a4cab0f961ba53ede9bdf960b94f9829b1f3414726429b362c5b538e391520f489b7ed8d20ae3fd49e9e259e44397514d618c96c4846be3c680bdc11c71dcbbe29ccf80d62a0938fa549391e6ea57ecbe2606790ec15d2224ae307c144226b7c4e8c2f97d2a1d67852d29beba110edd445197012062a393a9c92803ad3b4f31d7bc6033ccf7932cfed3f019044d25905916777286f82f9a4cc1ffe430ffd1dcfc27deed327b9f9630d2fa969fb6f0603cd19dd9a9519e673bcfcd9014125291a44669ef7285e74ed3729b677f801c3cdf058c50963168b496043716c7307cd9e0cdd137fccb0f05b47cdbb95c5f54831622c3652a32b2531fe326bcd6e2bbf56a194fa196fbd1a54952110f51c73433865f7664b836685e3664b3d8444aF89A242805E18C975F1146324996FDE17007CF3E6E8F4E764022533EDBFE07D4733E48BB372D75B0EF48EC983EB78532161CC529E5ABB89837DFCCA6261DBB37C7C5E6A87478BF41EE85A518C0F4EFA9BDE828C5A71B8E46597B634AFD204D3C501334239C3414285ED72D3A9169EABBD4DC25D52BB7516D3BA712D75AD8C0AE5D493C19E38A77939E7A058D713E9CCCCA58045F436B434B1C80D365472406E392951987DB6905C80D431DA18451135BE7E82BCAB358CB3971E61405B2FF17980D6E7E67E861E28201C1EE30B441040FD06878D65042C95582A4318207BFC700BE0CE32889AEC2FFE5085E8967910D879FA0E8C0FF85FDC510B9FF2FBF87CFCB29577D68099E04FFA05F752A73D377C70D3A8BC2DA80E6E780EC057182C33AD1DE387252258A1E18E6FAD910327CE7F42FD1E1E0515F9586E2F2EFCB9F472B1DBDBAC354A4162151E9D92C79FB08BB4DDC56F19448C0175A46E2E6C491FEC71419AA43A349BEA768A92C75DE68FD9591E68067F3197094D3FB87ED81785EA075

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 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

Cipher = chacha20
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
IV = 00000000000000000001020304050607
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 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

# RFC7539
Cipher = chacha20-poly1305
Key = 808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9f
IV = 070000004041424344454647
AAD = 50515253c0c1c2c3c4c5c6c7
Tag = 1ae10b594f09e26a7e902ecbd0600691
Plaintext = 4c616469657320616e642047656e746c656d656e206f662074686520636c617373206f66202739393a204966204920636f756c64206f6666657220796f75206f6e6c79206f6e652074697020666f7220746865206675747572652c2073756e73637265656e20776f756c642062652069742e
Ciphertext = d31a8d34648e60db7b86afbc53ef7ec2a4aded51296e08fea9e2b5a736ee62d63dbea45e8ca9671282fafb69da92728b1a71de0a9e060b2905d6a5b67ecd3b3692ddbd7f2d778b8c9803aee328091b58fab324e4fad675945585808b4831d7bc3ff4def08e4b7a9de576d26586cec64b6116

Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = 000000000102030405060708
AAD = f33388860000000000004e91
Tag = eead9d67890cbb22392336fea1851f38
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67726573732e2fe2809d
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c29a6ad5cb4022b02709b

Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = 000000000102030405060708
AAD = f33388860000000000004e91
Tag = eead9d67890cbb22392336fea1851f39
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67726573732e2fe2809d
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c29a6ad5cb4022b02709b
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

# self-generated vectors
Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = 000000000102030405060708
AAD = f33388860000000000004e91
Tag = d96119a40cd17f2527306866a3ef0413
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d4472616674732061732072
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a

Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = 000000000102030405060708
AAD = f33388860000000000004e91
Tag = 53aee3189d2b747032378a6186feb43f
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c299da65ba25e6a85842bf0440fd98a9a2266b061c4b3a13327c090f9a0789f58aad805275e4378a525f19232bfbfb749ede38480f405cf43ec2f1f8619ebcbc80a89e92a859c7911e674977ab17d4a7126a6b8a477358ff14a344d276ef6e504e10268ac3619fcf90c2d6c03fc2e3d1f290d9bf26c1fa1495dd8f97eec6229a55c2354e4524143551a5cc370a1c622c9390530cff21c3e1ed50c5e3daf97518ccce34156bdbd7eafab8bd417aef25c6c927301731bd319d247a1d5c3186ed10bfd9a7a24bac30e3e4503ed9204154d338b79ea276e7058e7f20f4d4fd1ac93d63f611af7b6d006c2a72add0eedc497b19cb30a198816664f0da00155f2e2d6ac61

Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = 000000000102030405060708
AAD = f33388860000000000004e91
Tag = e0723bce23528ce6ccb10ff9627038bf
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c299da65ba25e6a85842bf0440fd98a9a2266b061c4b3a13327c090f9a0789f58aad805275e4378a525f19232bfbfb749ede38480f405cf43ec2f1f8619ebcbc80a89e92a859c7911e674977ab17d4a7126a6b8a477358ff14a344d276ef6e504e10268ac3619fcf90c2d6c03fc2e3d1f290d9bf26c1fa1495dd8f97eec6229a55c2354e4524143551a5cc370a1c622c9390530cff21c3e1ed50c5e3daf97518ccce34156bdbd7eafab8bd417aef25c6c927301731bd319d247a1d5c3186ed10bfd9a7a24bac30e3e4503ed9204154d338b79ea276e7058e7f20f4d4fd1ac93d63f611af7b6d006c2a72add0eedc497b19cb30a198816664f0da00155f2e2d6ac61045b296d614301e0ad4983308028850dd4feffe3a8163970306e4047f5a165cb4befbc129729cd2e286e837e9b606486d402acc3dec5bf8b92387f6e486f2140

Cipher = chacha20-poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
IV = ff000000000102030405060708
AAD = f33388860000000000004e91
Tag = e0723bce23528ce6ccb10ff9627038bf
Plaintext = 496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d6f6e74687320616e64206d617920626520757064617465642c207265706c616365642c206f72206f62736f6c65746564206279206f7468657220646f63756d656e747320617420616e792074696d652e20497420697320696e617070726f70726961746520746f2075736520496e7465726e65742d447261667473206173207265666572656e6365206d6174657269616c206f7220746f2063697465207468656d206f74686572207468616e206173202fe2809c776f726b20696e2070726f67496e7465726e65742d4472616674732061726520647261667420646f63756d656e74732076616c696420666f722061206d6178696d756d206f6620736978206d
Ciphertext = 64a0861575861af460f062c79be643bd5e805cfd345cf389f108670ac76c8cb24c6cfc18755d43eea09ee94e382d26b0bdb7b73c321b0100d4f03b7f355894cf332f830e710b97ce98c8a84abd0b948114ad176e008d33bd60f982b1ff37c8559797a06ef4f0ef61c186324e2b3506383606907b6a7c02b0f9f6157b53c867e4b9166c767b804d46a59b5216cde7a4e99040c5a40433225ee282a1b0a06c523eaf4534d7f83fa1155b0047718cbc546a0d072b04b3564eea1b422273f548271a0bb2316053fa76991955ebd63159434ecebb4e466dae5a1073a6727627097a1049e617d91d361094fa68f0ff77987130305beaba2eda04df997b714d6c6f2c299da65ba25e6a85842bf0440fd98a9a2266b061c4b3a13327c090f9a0789f58aad805275e4378a525f19232bfbfb749ede38480f405cf43ec2f1f8619ebcbc80a89e92a859c7911e674977ab17d4a7126a6b8a477358ff14a344d276ef6e504e10268ac3619fcf90c2d6c03fc2e3d1f290d9bf26c1fa1495dd8f97eec6229a55c2354e4524143551a5cc370a1c622c9390530cff21c3e1ed50c5e3daf97518ccce34156bdbd7eafab8bd417aef25c6c927301731bd319d247a1d5c3186ed10bfd9a7a24bac30e3e4503ed9204154d338b79ea276e7058e7f20f4d4fd1ac93d63f611af7b6d006c2a72add0eedc497b19cb30a198816664f0da00155f2e2d6ac61045b296d614301e0ad4983308028850dd4feffe3a8163970306e4047f5a165cb4befbc129729cd2e286e837e9b606486d402acc3dec5bf8b92387f6e486f2140
Result = INVALID_IV_LENGTH
