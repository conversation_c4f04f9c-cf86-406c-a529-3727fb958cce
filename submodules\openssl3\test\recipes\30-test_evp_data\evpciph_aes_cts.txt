#
# Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Original test vectors were taken from https://www.ietf.org/rfc/rfc3962.txt for CS3
# These have an IV of all zeros, for a 128 bit AES key.

Title = AES CBC Test vectors

#------------------------------------------------------
# AES_CBC results for aligned block lengths. (Result should be the same as 32 byte CTS1 & CTS2)

# 32 bytes input
Cipher = AES-128-CBC
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a8

# 48 bytes input
Cipher = AES-128-CBC
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a89dad8bbb96c4cdc03bc103e1a194bbd8

# 64 bytes input
Cipher = AES-128-CBC
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a89dad8bbb96c4cdc03bc103e1a194bbd84807efe836ee89a526730dbc2f7bc840

Title = AES CBC CTS1 Test vectors

#------------------------------------------------------
# Manually edited using the same inputs to also produce CS1 ciphertext
# where aligned blocks are the same as CBC mode, and partial lengths
# have the last 2 blocks swapped compared to CS3.

# 17 bytes Input((Default is CS1 if CTSMode is not specified)
Cipher = AES-128-CBC-CTS
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = 97c6353568f2bf8cb4d8a580362da7ff7f
NextIV = c6353568f2bf8cb4d8a580362da7ff7f

# 31 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = 97687268d6ecccc0c07b25e25ecfe5fc00783e0efdb2c1d445d4c8eff7ed22
NextIV = fc00783e0efdb2c1d445d4c8eff7ed22

# 32 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a8
NextIV = 39312523a78662d5be7fcbcc98ebf5a8

# 47 bytes input
Cipher = AES-128-CBC-CTS
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5b3fffd940c16a18c1b5549d2f838029e
NextIV = b3fffd940c16a18c1b5549d2f838029e

# 64 bytes input (CS1 is equivalent to CBC when the last block in full)
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a89dad8bbb96c4cdc03bc103e1a194bbd84807efe836ee89a526730dbc2f7bc840
NextIV = 4807efe836ee89a526730dbc2f7bc840

#-------------------------------------------------------------------------------
# Generated test values using an IV.

# 47 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV =000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c
Ciphertext = 5432a630742dee7beb70f9f1400ee6a0b557cfb581949a4bdf3bb67dedd472426da5c54a9990f5ae0b7825f51f0060

# 127 bytes
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f7570
Ciphertext = 5432a630742dee7beb70f9f1400ee6a0b557cfb581949a4bdf3bb67dedd472b9fc50e4e7dacf9e3d94b6cc031f9997a22d2fea7e6ef4aba2b717b0fa3f150e5e86e46b9e51c6ea5091a92aa791ce826b2e4fbaaf0e0314939625434b9530ce56f299891a48d26bdc287f54b230340d652a4721bf0f082ede80b6399800a92f

# 129 bytes
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e49
Ciphertext = 5432a630742dee7beb70f9f1400ee6a0b557cfb581949a4bdf3bb67dedd472b9fc50e4e7dacf9e3d94b6cc031f9997a22d2fea7e6ef4aba2b717b0fa3f150e5e86e46b9e51c6ea5091a92aa791ce826b2e4fbaaf0e0314939625434b9530ce56f299891a48d26bdc287f54b230340d14fde9fd1098b9b1db788b5868a8d009eeef

# 17 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = e9de1b402de8f79f947cc6b5880588d9b6

# 31 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = e9de17d6248fb492bdea1fb2e09c8edea2b610546f3b1e1d231821e283e153

# 32 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = e9de17d6248fb492bdea1fb2e09c8e8e31d005cc9fea948fed1ba6308dad9dd1

#------------------------------------------------------------------------------
# Failure test

# 15 bytes should fail for CS1
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F
Result = CIPHERUPDATE_ERROR

# Original test vectors were taken from https://www.ietf.org/rfc/rfc3962.txt for CS3
# These have an IV of all zeros, for a 128 bit AES key.

Title = AES CBC CTS2 Test vectors

#------------------------------------------------------
# Manually edited using the same inputs to also produce CS2 ciphertext
# where aligned blocks are the same as CBC mode, and partial lengths
# are the same as CS3.

# 17 bytes Input (For partial blocks the output should match CS3)
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = c6353568f2bf8cb4d8a580362da7ff7f97
NextIV = c6353568f2bf8cb4d8a580362da7ff7f

# 31 bytes input (For partial blocks the output should match CS3)
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = fc00783e0efdb2c1d445d4c8eff7ed2297687268d6ecccc0c07b25e25ecfe5
NextIV = fc00783e0efdb2c1d445d4c8eff7ed22

# 32 bytes input (Aligned blocks should match normal CBC mode)
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a8
NextIV = 39312523a78662d5be7fcbcc98ebf5a8

# 47 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c
Ciphertext = 97687268d6ecccc0c07b25e25ecfe584b3fffd940c16a18c1b5549d2f838029e39312523a78662d5be7fcbcc98ebf5
NextIV = b3fffd940c16a18c1b5549d2f838029e

# 64 bytes input (CS2 is equivalent to CBC when the last block in full)
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a89dad8bbb96c4cdc03bc103e1a194bbd84807efe836ee89a526730dbc2f7bc840
NextIV = 4807efe836ee89a526730dbc2f7bc840

# Generated test values using an IV.

# 17 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = de1b402de8f79f947cc6b5880588d9b6e9

# 31 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = dea2b610546f3b1e1d231821e283e153e9de17d6248fb492bdea1fb2e09c8e

# 32 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = e9de17d6248fb492bdea1fb2e09c8e8e31d005cc9fea948fed1ba6308dad9dd1

# Failure test - 15 bytes should fail for CS2
Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F
Result = CIPHERUPDATE_ERROR


Title = AES CBC CTS3 Test vectors

# 17 bytes Input
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = c6353568f2bf8cb4d8a580362da7ff7f97
NextIV = c6353568f2bf8cb4d8a580362da7ff7f

# 31 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = fc00783e0efdb2c1d445d4c8eff7ed2297687268d6ecccc0c07b25e25ecfe5
NextIV = fc00783e0efdb2c1d445d4c8eff7ed22

# 32 bytes input (CS3 always swaps the last 2 byte blocks - so it is not equivalent to CBC for a full block)
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 39312523a78662d5be7fcbcc98ebf5a897687268d6ecccc0c07b25e25ecfe584
NextIV = 39312523a78662d5be7fcbcc98ebf5a8

# 47 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c
Ciphertext = 97687268d6ecccc0c07b25e25ecfe584b3fffd940c16a18c1b5549d2f838029e39312523a78662d5be7fcbcc98ebf5
NextIV = b3fffd940c16a18c1b5549d2f838029e

# 48 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20
Ciphertext = 97687268d6ecccc0c07b25e25ecfe5849dad8bbb96c4cdc03bc103e1a194bbd839312523a78662d5be7fcbcc98ebf5a8
NextIV = 9dad8bbb96c4cdc03bc103e1a194bbd8

# 64 bytes input (CS3 always swaps the last 2 byte blocks - so it is not equivalent to CBC for a full block)
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c20616e6420776f6e746f6e20736f75702e
Ciphertext = 97687268d6ecccc0c07b25e25ecfe58439312523a78662d5be7fcbcc98ebf5a84807efe836ee89a526730dbc2f7bc8409dad8bbb96c4cdc03bc103e1a194bbd8


# Generated test values using an IV.
# 47 bytes input
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320436869636b656e2c20706c656173652c
Ciphertext = 5432a630742dee7beb70f9f1400ee6a0426da5c54a9990f5ae0b7825f51f0060b557cfb581949a4bdf3bb67dedd472

# 17 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e20
IV =000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = de1b402de8f79f947cc6b5880588d9b6e9

# 31 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = dea2b610546f3b1e1d231821e283e153e9de17d6248fb492bdea1fb2e09c8e

# 32 Bytes
Cipher = AES-192-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e20
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 31d005cc9fea948fed1ba6308dad9dd1e9de17d6248fb492bdea1fb2e09c8e8e

# 17 Bytes
Cipher = AES-256-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b652074686520
Ciphertext = 6b5f5abc21c4d04156c73850da3bba29e9

# 31 Bytes
Cipher = AES-256-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c20476175277320
Ciphertext = f22553af78ee4f468f02fbe6f0f2168ee954e79fae9310dc75b6070e1d6253

# 32 Bytes
Cipher = AES-256-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69636869636b656e207465726979616b69
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 4920776f756c64206c696b65207468652047656e6572616c2047617527732043
Ciphertext = 2c0463982174df10baa9d8f782c5a5b3e954e79fae9310dc75b6070e1d625346

# Failure tests
# 15 bytes should fail for CS3
Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F
Result = CIPHERUPDATE_ERROR

# 16 bytes input
Cipher = AES-128-CBC
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F00
Ciphertext = 011ca8de3bd20ebc2f8701d56dcf768e

# 16 bytes with CS3 should return the same as plain CBC mode.
Cipher = AES-128-CBC-CTS
CTSMode = CS1
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F00
Ciphertext = 011ca8de3bd20ebc2f8701d56dcf768e

Cipher = AES-128-CBC-CTS
CTSMode = CS2
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F00
Ciphertext = 011ca8de3bd20ebc2f8701d56dcf768e

Cipher = AES-128-CBC-CTS
CTSMode = CS3
Key = 636869636b656e207465726979616b69
IV = 00000000000000000000000000000000
Plaintext = 0102030405060708090A0B0C0D0E0F00
Ciphertext = 011ca8de3bd20ebc2f8701d56dcf768e

