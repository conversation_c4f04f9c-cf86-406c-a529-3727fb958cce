=pod

=head1 NAME

CMS_sign - create a CMS SignedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ContentInfo *CMS_sign(X509 *signcert, EVP_PKEY *pkey, STACK_OF(X509) *certs,
                           BIO *data, unsigned int flags);

=head1 DESCRIPTION

CMS_sign() creates and returns a CMS SignedData structure. B<signcert> is
the certificate to sign with, B<pkey> is the corresponding private key.
B<certs> is an optional additional set of certificates to include in the CMS
structure (for example any intermediate CAs in the chain). Any or all of
these parameters can be B<NULL>, see B<NOTES> below.

The data to be signed is read from BIO B<data>.

B<flags> is an optional set of flags.

=head1 NOTES

Any of the following flags (ored together) can be passed in the B<flags>
parameter.

Many S/MIME clients expect the signed content to include valid MIME headers. If
the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are prepended
to the data.

If B<CMS_NOCERTS> is set the signer's certificate will not be included in the
CMS_ContentInfo structure, the signer's certificate must still be supplied in
the B<signcert> parameter though. This can reduce the size of the signature if
the signers certificate can be obtained by other means: for example a
previously signed message.

The data being signed is included in the CMS_ContentInfo structure, unless
B<CMS_DETACHED> is set in which case it is omitted. This is used for
CMS_ContentInfo detached signatures which are used in S/MIME plaintext signed
messages for example.

Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if B<CMS_BINARY> is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it.

The SignedData structure includes several CMS signedAttributes including the
signing time, the CMS content type and the supported list of ciphers in an
SMIMECapabilities attribute. If B<CMS_NOATTR> is set then no signedAttributes
will be used. If B<CMS_NOSMIMECAP> is set then just the SMIMECapabilities are
omitted.

If present the SMIMECapabilities attribute indicates support for the following
algorithms in preference order: 256 bit AES, Gost R3411-94, Gost 28147-89, 192
bit AES, 128 bit AES, triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2.
If any of these algorithms is not available then it will not be included: for example the GOST algorithms will not be included if the GOST ENGINE is
not loaded.

OpenSSL will by default identify signing certificates using issuer name
and serial number. If B<CMS_USE_KEYID> is set it will use the subject key
identifier value instead. An error occurs if the signing certificate does not
have a subject key identifier extension.

If the flags B<CMS_STREAM> is set then the returned B<CMS_ContentInfo>
structure is just initialized ready to perform the signing operation. The
signing is however B<not> performed and the data to be signed is not read from
the B<data> parameter. Signing is deferred until after the data has been
written. In this way data can be signed in a single pass.

If the B<CMS_PARTIAL> flag is set a partial B<CMS_ContentInfo> structure is
output to which additional signers and capabilities can be added before
finalization.

If the flag B<CMS_STREAM> is set the returned B<CMS_ContentInfo> structure is
B<not> complete and outputting its contents via a function that does not
properly finalize the B<CMS_ContentInfo> structure will give unpredictable
results.

Several functions including SMIME_write_CMS(), i2d_CMS_bio_stream(),
PEM_write_bio_CMS_stream() finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 B<BIO> directly using
BIO_new_CMS().

If a signer is specified it will use the default digest for the signing
algorithm. This is B<SHA1> for both RSA and DSA keys.

If B<signcert> and B<pkey> are NULL then a certificates only CMS structure is
output.

The function CMS_sign() is a basic CMS signing function whose output will be
suitable for many purposes. For finer control of the output format the
B<certs>, B<signcert> and B<pkey> parameters can all be B<NULL> and the
B<CMS_PARTIAL> flag set. Then one or more signers can be added using the
function CMS_add1_signer(), non default digests can be used and custom
attributes added. CMS_final() must then be called to finalize the
structure if streaming is not enabled.

=head1 BUGS

Some attributes such as counter signatures are not supported.

=head1 RETURN VALUES

CMS_sign() returns either a valid CMS_ContentInfo structure or NULL if an error
occurred. The error can be obtained from ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_verify(3)>

=head1 HISTORY

The B<CMS_STREAM> flag is only supported for detached data in OpenSSL 0.9.8,
it is supported for embedded data in OpenSSL 1.0.0 and later.

=head1 COPYRIGHT

Copyright 2008-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
