# Generated with generate_ssl_tests.pl

num_tests = 7

test-0 = 0-disable-extended-master-secret-server-sha
test-1 = 1-disable-extended-master-secret-client-sha
test-2 = 2-disable-extended-master-secret-both-sha
test-3 = 3-disable-extended-master-secret-both-resume
test-4 = 4-disable-extended-master-secret-server-sha2
test-5 = 5-disable-extended-master-secret-client-sha2
test-6 = 6-disable-extended-master-secret-both-sha2
# ===========================================================

[0-disable-extended-master-secret-server-sha]
ssl_conf = 0-disable-extended-master-secret-server-sha-ssl

[0-disable-extended-master-secret-server-sha-ssl]
server = 0-disable-extended-master-secret-server-sha-server
client = 0-disable-extended-master-secret-server-sha-client

[0-disable-extended-master-secret-server-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -ExtendedMasterSecret
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-disable-extended-master-secret-server-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
FIPSversion = <=3.1.0


# ===========================================================

[1-disable-extended-master-secret-client-sha]
ssl_conf = 1-disable-extended-master-secret-client-sha-ssl

[1-disable-extended-master-secret-client-sha-ssl]
server = 1-disable-extended-master-secret-client-sha-server
client = 1-disable-extended-master-secret-client-sha-client

[1-disable-extended-master-secret-client-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-disable-extended-master-secret-client-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
Options = -ExtendedMasterSecret
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
FIPSversion = <=3.1.0


# ===========================================================

[2-disable-extended-master-secret-both-sha]
ssl_conf = 2-disable-extended-master-secret-both-sha-ssl

[2-disable-extended-master-secret-both-sha-ssl]
server = 2-disable-extended-master-secret-both-sha-server
client = 2-disable-extended-master-secret-both-sha-client

[2-disable-extended-master-secret-both-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -ExtendedMasterSecret
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-disable-extended-master-secret-both-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
Options = -ExtendedMasterSecret
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
FIPSversion = <=3.1.0


# ===========================================================

[3-disable-extended-master-secret-both-resume]
ssl_conf = 3-disable-extended-master-secret-both-resume-ssl

[3-disable-extended-master-secret-both-resume-ssl]
server = 3-disable-extended-master-secret-both-resume-server
client = 3-disable-extended-master-secret-both-resume-client
resume-server = 3-disable-extended-master-secret-both-resume-resume-server
resume-client = 3-disable-extended-master-secret-both-resume-resume-client

[3-disable-extended-master-secret-both-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -ExtendedMasterSecret
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-disable-extended-master-secret-both-resume-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-disable-extended-master-secret-both-resume-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
Options = -ExtendedMasterSecret
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[3-disable-extended-master-secret-both-resume-resume-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
FIPSversion = <=3.1.0
HandshakeMode = Resume


# ===========================================================

[4-disable-extended-master-secret-server-sha2]
ssl_conf = 4-disable-extended-master-secret-server-sha2-ssl

[4-disable-extended-master-secret-server-sha2-ssl]
server = 4-disable-extended-master-secret-server-sha2-server
client = 4-disable-extended-master-secret-server-sha2-client

[4-disable-extended-master-secret-server-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -ExtendedMasterSecret
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-disable-extended-master-secret-server-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
FIPSversion = <=3.1.0


# ===========================================================

[5-disable-extended-master-secret-client-sha2]
ssl_conf = 5-disable-extended-master-secret-client-sha2-ssl

[5-disable-extended-master-secret-client-sha2-ssl]
server = 5-disable-extended-master-secret-client-sha2-server
client = 5-disable-extended-master-secret-client-sha2-client

[5-disable-extended-master-secret-client-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-disable-extended-master-secret-client-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
Options = -ExtendedMasterSecret
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
FIPSversion = <=3.1.0


# ===========================================================

[6-disable-extended-master-secret-both-sha2]
ssl_conf = 6-disable-extended-master-secret-both-sha2-ssl

[6-disable-extended-master-secret-both-sha2-ssl]
server = 6-disable-extended-master-secret-both-sha2-server
client = 6-disable-extended-master-secret-both-sha2-client

[6-disable-extended-master-secret-both-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -ExtendedMasterSecret
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-disable-extended-master-secret-both-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
Options = -ExtendedMasterSecret
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
FIPSversion = <=3.1.0


