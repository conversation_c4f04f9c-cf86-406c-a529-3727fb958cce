/* Autogenerated: ECCKiila https://gitlab.com/nisec/ecckiila */
/*-
 * MIT License
 * 
 * Copyright (c) 2020 <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#if defined(__SIZEOF_INT128__) && !defined(PEDANTIC)

#include <stdint.h>
#include <string.h>
#define LIMB_BITS 64
#define LIMB_CNT 5
/* Field elements */
typedef uint64_t fe_t[LIMB_CNT];
typedef uint64_t limb_t;

#ifdef OPENSSL_NO_ASM
#define FIAT_ID_TC26_GOST_3410_2012_256_PARAMSETA_NO_ASM
#endif

#define fe_copy(d, s) memcpy(d, s, sizeof(fe_t))
#define fe_set_zero(d) memset(d, 0, sizeof(fe_t))

#define fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(c, a, b) \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_add(c, a, b);          \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(c, c)
#define fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(c, a, b) \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_sub(c, a, b);          \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(c, c)

/* Projective points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t T;
    fe_t Z;
} pt_prj_t;

/* Affine points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t T;
} pt_aff_t;

/* BEGIN verbatim fiat code https://github.com/mit-plv/fiat-crypto */
/*-
 * MIT License
 *
 * Copyright (c) 2020 the fiat-crypto authors (see the AUTHORS file)
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/* Autogenerated: unsaturated_solinas --static --use-value-barrier id_tc26_gost_3410_2012_256_paramSetA 64 5 '2^256 - 617' */
/* curve description: id_tc26_gost_3410_2012_256_paramSetA */
/* machine_wordsize = 64 (from "64") */
/* requested operations: (all) */
/* n = 5 (from "5") */
/* s-c = 2^256 - [(1, 617)] (from "2^256 - 617") */
/* tight_bounds_multiplier = 1 (from "") */
/*  */
/* Computed values: */
/* carry_chain = [0, 1, 2, 3, 4, 0, 1] */
/* eval z = z[0] + (z[1] << 52) + (z[2] << 103) + (z[3] << 154) + (z[4] << 205) */
/* bytes_eval z = z[0] + (z[1] << 8) + (z[2] << 16) + (z[3] << 24) + (z[4] << 32) + (z[5] << 40) + (z[6] << 48) + (z[7] << 56) + (z[8] << 64) + (z[9] << 72) + (z[10] << 80) + (z[11] << 88) + (z[12] << 96) + (z[13] << 104) + (z[14] << 112) + (z[15] << 120) + (z[16] << 128) + (z[17] << 136) + (z[18] << 144) + (z[19] << 152) + (z[20] << 160) + (z[21] << 168) + (z[22] << 176) + (z[23] << 184) + (z[24] << 192) + (z[25] << 200) + (z[26] << 208) + (z[27] << 216) + (z[28] << 224) + (z[29] << 232) + (z[30] << 240) + (z[31] << 248) */
/* balance = [0x1ffffffffffb2e, 0xffffffffffffe, 0xffffffffffffe, 0xffffffffffffe, 0xffffffffffffe] */

#include <stdint.h>
typedef unsigned char fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1;
typedef signed char fiat_id_tc26_gost_3410_2012_256_paramSetA_int1;
typedef signed __int128 fiat_id_tc26_gost_3410_2012_256_paramSetA_int128;
typedef unsigned __int128 fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128;

#if (-1 & 3) != 3
#error "This code only works on a two's complement system"
#endif

#if !defined(FIAT_ID_TC26_GOST_3410_2012_256_PARAMSETA_NO_ASM) && \
    (defined(__GNUC__) || defined(__clang__))
static __inline__ uint64_t
fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u64(uint64_t a) {
    __asm__("" : "+r"(a) : /* no inputs */);
    return a;
}
#else
#define fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u64(x) (x)
#endif

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u52 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^52
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^52⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xfffffffffffff]
 *   arg3: [0x0 ~> 0xfffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xfffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u52(
    uint64_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    uint64_t x1;
    uint64_t x2;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT64_C(0xfffffffffffff));
    x3 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x1 >> 52);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u52 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^52
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^52⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xfffffffffffff]
 *   arg3: [0x0 ~> 0xfffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xfffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u52(
    uint64_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    int64_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_int1 x2;
    uint64_t x3;
    x1 = ((int64_t)(arg2 - (int64_t)arg1) - (int64_t)arg3);
    x2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(x1 >> 52);
    x3 = (x1 & UINT64_C(0xfffffffffffff));
    *out1 = x3;
    *out2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^51
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^51⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7ffffffffffff]
 *   arg3: [0x0 ~> 0x7ffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7ffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51(
    uint64_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    uint64_t x1;
    uint64_t x2;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT64_C(0x7ffffffffffff));
    x3 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x1 >> 51);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^51
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^51⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7ffffffffffff]
 *   arg3: [0x0 ~> 0x7ffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7ffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51(
    uint64_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    int64_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_int1 x2;
    uint64_t x3;
    x1 = ((int64_t)(arg2 - (int64_t)arg1) - (int64_t)arg3);
    x2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(x1 >> 51);
    x3 = (x1 & UINT64_C(0x7ffffffffffff));
    *out1 = x3;
    *out2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64 is a single-word conditional move.
 * Postconditions:
 *   out1 = (if arg1 = 0 then arg2 else arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 *   arg3: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(
    uint64_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1,
    uint64_t arg2, uint64_t arg3) {
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x1;
    uint64_t x2;
    uint64_t x3;
    x1 = (!(!arg1));
    x2 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(0x0 - x1) &
          UINT64_C(0xffffffffffffffff));
    x3 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u64(x2) &
           arg3) |
          (fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u64((~x2)) &
           arg2));
    *out1 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul multiplies two field elements and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 *   arg2: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x2;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x3;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x4;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x5;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x6;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x7;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x8;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x9;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x10;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x11;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x12;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x13;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x14;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x15;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x16;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x17;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x18;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x19;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x20;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x21;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x22;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x23;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x24;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x25;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x26;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x27;
    uint64_t x28;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x29;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x30;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x31;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x32;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x33;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x34;
    uint64_t x35;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x36;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x37;
    uint64_t x38;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x39;
    uint64_t x40;
    uint64_t x41;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x42;
    uint64_t x43;
    uint64_t x44;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x45;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x50;
    uint64_t x51;
    uint64_t x52;
    x1 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) *
           (arg2[4])));
    x2 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) *
           (arg2[3])));
    x3 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) *
           (arg2[2])));
    x4 = (UINT16_C(0x269) *
          (((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) *
            (arg2[1])) *
           0x2));
    x5 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) *
           (arg2[4])));
    x6 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) *
           (arg2[3])));
    x7 = (UINT16_C(0x269) *
          (((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) *
            (arg2[2])) *
           0x2));
    x8 = (UINT16_C(0x269) *
          ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           (arg2[4])));
    x9 = (UINT16_C(0x269) *
          (((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
            (arg2[3])) *
           0x2));
    x10 = (UINT16_C(0x269) *
           (((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
             (arg2[4])) *
            0x2));
    x11 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) *
           (arg2[0]));
    x12 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) *
           ((arg2[1]) * 0x2));
    x13 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) *
           (arg2[0]));
    x14 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           ((arg2[2]) * 0x2));
    x15 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           ((arg2[1]) * 0x2));
    x16 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           (arg2[0]));
    x17 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           ((arg2[3]) * 0x2));
    x18 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           ((arg2[2]) * 0x2));
    x19 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           ((arg2[1]) * 0x2));
    x20 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           (arg2[0]));
    x21 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg2[4]));
    x22 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg2[3]));
    x23 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg2[2]));
    x24 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg2[1]));
    x25 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg2[0]));
    x26 = (x25 + (x10 + (x9 + (x7 + x4))));
    x27 = (x26 >> 52);
    x28 = (uint64_t)(x26 & UINT64_C(0xfffffffffffff));
    x29 = (x21 + (x17 + (x14 + (x12 + x11))));
    x30 = (x22 + (x18 + (x15 + (x13 + x1))));
    x31 = (x23 + (x19 + (x16 + (x5 + x2))));
    x32 = (x24 + (x20 + (x8 + (x6 + x3))));
    x33 = (x27 + x32);
    x34 = (x33 >> 51);
    x35 = (uint64_t)(x33 & UINT64_C(0x7ffffffffffff));
    x36 = (x34 + x31);
    x37 = (x36 >> 51);
    x38 = (uint64_t)(x36 & UINT64_C(0x7ffffffffffff));
    x39 = (x37 + x30);
    x40 = (uint64_t)(x39 >> 51);
    x41 = (uint64_t)(x39 & UINT64_C(0x7ffffffffffff));
    x42 = (x40 + x29);
    x43 = (uint64_t)(x42 >> 51);
    x44 = (uint64_t)(x42 & UINT64_C(0x7ffffffffffff));
    x45 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)UINT16_C(0x269) *
           x43);
    x46 = (x28 + x45);
    x47 = (uint64_t)(x46 >> 52);
    x48 = (uint64_t)(x46 & UINT64_C(0xfffffffffffff));
    x49 = (x47 + x35);
    x50 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x49 >> 51);
    x51 = (x49 & UINT64_C(0x7ffffffffffff));
    x52 = (x50 + x38);
    out1[0] = x48;
    out1[1] = x51;
    out1[2] = x52;
    out1[3] = x41;
    out1[4] = x44;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square squares a field element and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg1) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x9;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x10;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x11;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x12;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x13;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x14;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x15;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x16;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x17;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x18;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x19;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x20;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x21;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x22;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x23;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x24;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x25;
    uint64_t x26;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x27;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x28;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x29;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x30;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x31;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x32;
    uint64_t x33;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x34;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x35;
    uint64_t x36;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x37;
    uint64_t x38;
    uint64_t x39;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x40;
    uint64_t x41;
    uint64_t x42;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x43;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128 x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x48;
    uint64_t x49;
    uint64_t x50;
    x1 = ((arg1[4]) * UINT16_C(0x269));
    x2 = (x1 * 0x2);
    x3 = ((arg1[4]) * 0x2);
    x4 = ((arg1[3]) * UINT16_C(0x269));
    x5 = (x4 * 0x2);
    x6 = ((arg1[3]) * 0x2);
    x7 = ((arg1[2]) * 0x2);
    x8 = ((arg1[1]) * 0x2);
    x9 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[4]) * x1);
    x10 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) * x2);
    x11 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[3]) * x4);
    x12 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) * x2);
    x13 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           (x5 * 0x2));
    x14 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[2]) *
           ((arg1[2]) * 0x2));
    x15 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           (x2 * 0x2));
    x16 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           (x6 * 0x2));
    x17 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           (x7 * 0x2));
    x18 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[1]) *
           ((arg1[1]) * 0x2));
    x19 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) * x3);
    x20 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) * x6);
    x21 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) * x7);
    x22 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) * x8);
    x23 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)(arg1[0]) *
           (arg1[0]));
    x24 = (x23 + (x15 + x13));
    x25 = (x24 >> 52);
    x26 = (uint64_t)(x24 & UINT64_C(0xfffffffffffff));
    x27 = (x19 + (x16 + x14));
    x28 = (x20 + (x17 + x9));
    x29 = (x21 + (x18 + x10));
    x30 = (x22 + (x12 + x11));
    x31 = (x25 + x30);
    x32 = (x31 >> 51);
    x33 = (uint64_t)(x31 & UINT64_C(0x7ffffffffffff));
    x34 = (x32 + x29);
    x35 = (x34 >> 51);
    x36 = (uint64_t)(x34 & UINT64_C(0x7ffffffffffff));
    x37 = (x35 + x28);
    x38 = (uint64_t)(x37 >> 51);
    x39 = (uint64_t)(x37 & UINT64_C(0x7ffffffffffff));
    x40 = (x38 + x27);
    x41 = (uint64_t)(x40 >> 51);
    x42 = (uint64_t)(x40 & UINT64_C(0x7ffffffffffff));
    x43 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint128)UINT16_C(0x269) *
           x41);
    x44 = (x26 + x43);
    x45 = (uint64_t)(x44 >> 52);
    x46 = (uint64_t)(x44 & UINT64_C(0xfffffffffffff));
    x47 = (x45 + x33);
    x48 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x47 >> 51);
    x49 = (x47 & UINT64_C(0x7ffffffffffff));
    x50 = (x48 + x36);
    out1[0] = x46;
    out1[1] = x49;
    out1[2] = x50;
    out1[3] = x39;
    out1[4] = x42;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry reduces a field element.
 * Postconditions:
 *   eval out1 mod m = eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    x1 = (arg1[0]);
    x2 = ((x1 >> 52) + (arg1[1]));
    x3 = ((x2 >> 51) + (arg1[2]));
    x4 = ((x3 >> 51) + (arg1[3]));
    x5 = ((x4 >> 51) + (arg1[4]));
    x6 = ((x1 & UINT64_C(0xfffffffffffff)) + (UINT16_C(0x269) * (x5 >> 51)));
    x7 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x6 >> 52) +
          (x2 & UINT64_C(0x7ffffffffffff)));
    x8 = (x6 & UINT64_C(0xfffffffffffff));
    x9 = (x7 & UINT64_C(0x7ffffffffffff));
    x10 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x7 >> 51) +
           (x3 & UINT64_C(0x7ffffffffffff)));
    x11 = (x4 & UINT64_C(0x7ffffffffffff));
    x12 = (x5 & UINT64_C(0x7ffffffffffff));
    out1[0] = x8;
    out1[1] = x9;
    out1[2] = x10;
    out1[3] = x11;
    out1[4] = x12;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_add adds two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 + eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 *   arg2: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_add(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = ((arg1[0]) + (arg2[0]));
    x2 = ((arg1[1]) + (arg2[1]));
    x3 = ((arg1[2]) + (arg2[2]));
    x4 = ((arg1[3]) + (arg2[3]));
    x5 = ((arg1[4]) + (arg2[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_sub subtracts two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 - eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 *   arg2: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_sub(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = ((UINT64_C(0x1ffffffffffb2e) + (arg1[0])) - (arg2[0]));
    x2 = ((UINT64_C(0xffffffffffffe) + (arg1[1])) - (arg2[1]));
    x3 = ((UINT64_C(0xffffffffffffe) + (arg1[2])) - (arg2[2]));
    x4 = ((UINT64_C(0xffffffffffffe) + (arg1[3])) - (arg2[3]));
    x5 = ((UINT64_C(0xffffffffffffe) + (arg1[4])) - (arg2[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_opp negates a field element.
 * Postconditions:
 *   eval out1 mod m = -eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = (UINT64_C(0x1ffffffffffb2e) - (arg1[0]));
    x2 = (UINT64_C(0xffffffffffffe) - (arg1[1]));
    x3 = (UINT64_C(0xffffffffffffe) - (arg1[2]));
    x4 = (UINT64_C(0xffffffffffffe) - (arg1[3]));
    x5 = (UINT64_C(0xffffffffffffe) - (arg1[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz is a multi-limb conditional select.
 * Postconditions:
 *   eval out1 = (if arg1 = 0 then eval arg2 else eval arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg3: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
    uint64_t out1[5], fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1,
    const uint64_t arg2[5], const uint64_t arg3[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(&x1, arg1, (arg2[0]),
                                                          (arg3[0]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(&x2, arg1, (arg2[1]),
                                                          (arg3[1]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(&x3, arg1, (arg2[2]),
                                                          (arg3[2]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(&x4, arg1, (arg2[3]),
                                                          (arg3[3]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(&x5, arg1, (arg2[4]),
                                                          (arg3[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes serializes a field element to bytes in little-endian order.
 * Postconditions:
 *   out1 = map (λ x, ⌊((eval arg1 mod m) mod 2^(8 * (x + 1))) / 2^(8 * x)⌋) [0..31]
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(
    uint8_t out1[32], const uint64_t arg1[5]) {
    uint64_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x2;
    uint64_t x3;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x4;
    uint64_t x5;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x6;
    uint64_t x7;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x8;
    uint64_t x9;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x10;
    uint64_t x11;
    uint64_t x12;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x13;
    uint64_t x14;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x15;
    uint64_t x16;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x17;
    uint64_t x18;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x19;
    uint64_t x20;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint8_t x26;
    uint64_t x27;
    uint8_t x28;
    uint64_t x29;
    uint8_t x30;
    uint64_t x31;
    uint8_t x32;
    uint64_t x33;
    uint8_t x34;
    uint64_t x35;
    uint8_t x36;
    uint8_t x37;
    uint64_t x38;
    uint8_t x39;
    uint64_t x40;
    uint8_t x41;
    uint64_t x42;
    uint8_t x43;
    uint64_t x44;
    uint8_t x45;
    uint64_t x46;
    uint8_t x47;
    uint64_t x48;
    uint8_t x49;
    uint8_t x50;
    uint64_t x51;
    uint8_t x52;
    uint64_t x53;
    uint8_t x54;
    uint64_t x55;
    uint8_t x56;
    uint64_t x57;
    uint8_t x58;
    uint64_t x59;
    uint8_t x60;
    uint64_t x61;
    uint8_t x62;
    uint64_t x63;
    uint8_t x64;
    uint8_t x65;
    uint64_t x66;
    uint8_t x67;
    uint64_t x68;
    uint8_t x69;
    uint64_t x70;
    uint8_t x71;
    uint64_t x72;
    uint8_t x73;
    uint64_t x74;
    uint8_t x75;
    uint64_t x76;
    uint8_t x77;
    uint8_t x78;
    uint64_t x79;
    uint8_t x80;
    uint64_t x81;
    uint8_t x82;
    uint64_t x83;
    uint8_t x84;
    uint64_t x85;
    uint8_t x86;
    uint64_t x87;
    uint8_t x88;
    uint64_t x89;
    uint8_t x90;
    uint8_t x91;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u52(
        &x1, &x2, 0x0, (arg1[0]), UINT64_C(0xffffffffffd97));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51(
        &x3, &x4, x2, (arg1[1]), UINT64_C(0x7ffffffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51(
        &x5, &x6, x4, (arg1[2]), UINT64_C(0x7ffffffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51(
        &x7, &x8, x6, (arg1[3]), UINT64_C(0x7ffffffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u51(
        &x9, &x10, x8, (arg1[4]), UINT64_C(0x7ffffffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u64(
        &x11, x10, 0x0, UINT64_C(0xffffffffffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u52(
        &x12, &x13, 0x0, x1, (x11 & UINT64_C(0xffffffffffd97)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51(
        &x14, &x15, x13, x3, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51(
        &x16, &x17, x15, x5, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51(
        &x18, &x19, x17, x7, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u51(
        &x20, &x21, x19, x9, (x11 & UINT64_C(0x7ffffffffffff)));
    x22 = (x20 << 5);
    x23 = (x18 << 2);
    x24 = (x16 << 7);
    x25 = (x14 << 4);
    x26 = (uint8_t)(x12 & UINT8_C(0xff));
    x27 = (x12 >> 8);
    x28 = (uint8_t)(x27 & UINT8_C(0xff));
    x29 = (x27 >> 8);
    x30 = (uint8_t)(x29 & UINT8_C(0xff));
    x31 = (x29 >> 8);
    x32 = (uint8_t)(x31 & UINT8_C(0xff));
    x33 = (x31 >> 8);
    x34 = (uint8_t)(x33 & UINT8_C(0xff));
    x35 = (x33 >> 8);
    x36 = (uint8_t)(x35 & UINT8_C(0xff));
    x37 = (uint8_t)(x35 >> 8);
    x38 = (x25 + (uint64_t)x37);
    x39 = (uint8_t)(x38 & UINT8_C(0xff));
    x40 = (x38 >> 8);
    x41 = (uint8_t)(x40 & UINT8_C(0xff));
    x42 = (x40 >> 8);
    x43 = (uint8_t)(x42 & UINT8_C(0xff));
    x44 = (x42 >> 8);
    x45 = (uint8_t)(x44 & UINT8_C(0xff));
    x46 = (x44 >> 8);
    x47 = (uint8_t)(x46 & UINT8_C(0xff));
    x48 = (x46 >> 8);
    x49 = (uint8_t)(x48 & UINT8_C(0xff));
    x50 = (uint8_t)(x48 >> 8);
    x51 = (x24 + (uint64_t)x50);
    x52 = (uint8_t)(x51 & UINT8_C(0xff));
    x53 = (x51 >> 8);
    x54 = (uint8_t)(x53 & UINT8_C(0xff));
    x55 = (x53 >> 8);
    x56 = (uint8_t)(x55 & UINT8_C(0xff));
    x57 = (x55 >> 8);
    x58 = (uint8_t)(x57 & UINT8_C(0xff));
    x59 = (x57 >> 8);
    x60 = (uint8_t)(x59 & UINT8_C(0xff));
    x61 = (x59 >> 8);
    x62 = (uint8_t)(x61 & UINT8_C(0xff));
    x63 = (x61 >> 8);
    x64 = (uint8_t)(x63 & UINT8_C(0xff));
    x65 = (uint8_t)(x63 >> 8);
    x66 = (x23 + (uint64_t)x65);
    x67 = (uint8_t)(x66 & UINT8_C(0xff));
    x68 = (x66 >> 8);
    x69 = (uint8_t)(x68 & UINT8_C(0xff));
    x70 = (x68 >> 8);
    x71 = (uint8_t)(x70 & UINT8_C(0xff));
    x72 = (x70 >> 8);
    x73 = (uint8_t)(x72 & UINT8_C(0xff));
    x74 = (x72 >> 8);
    x75 = (uint8_t)(x74 & UINT8_C(0xff));
    x76 = (x74 >> 8);
    x77 = (uint8_t)(x76 & UINT8_C(0xff));
    x78 = (uint8_t)(x76 >> 8);
    x79 = (x22 + (uint64_t)x78);
    x80 = (uint8_t)(x79 & UINT8_C(0xff));
    x81 = (x79 >> 8);
    x82 = (uint8_t)(x81 & UINT8_C(0xff));
    x83 = (x81 >> 8);
    x84 = (uint8_t)(x83 & UINT8_C(0xff));
    x85 = (x83 >> 8);
    x86 = (uint8_t)(x85 & UINT8_C(0xff));
    x87 = (x85 >> 8);
    x88 = (uint8_t)(x87 & UINT8_C(0xff));
    x89 = (x87 >> 8);
    x90 = (uint8_t)(x89 & UINT8_C(0xff));
    x91 = (uint8_t)(x89 >> 8);
    out1[0] = x26;
    out1[1] = x28;
    out1[2] = x30;
    out1[3] = x32;
    out1[4] = x34;
    out1[5] = x36;
    out1[6] = x39;
    out1[7] = x41;
    out1[8] = x43;
    out1[9] = x45;
    out1[10] = x47;
    out1[11] = x49;
    out1[12] = x52;
    out1[13] = x54;
    out1[14] = x56;
    out1[15] = x58;
    out1[16] = x60;
    out1[17] = x62;
    out1[18] = x64;
    out1[19] = x67;
    out1[20] = x69;
    out1[21] = x71;
    out1[22] = x73;
    out1[23] = x75;
    out1[24] = x77;
    out1[25] = x80;
    out1[26] = x82;
    out1[27] = x84;
    out1[28] = x86;
    out1[29] = x88;
    out1[30] = x90;
    out1[31] = x91;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes deserializes a field element from bytes in little-endian order.
 * Postconditions:
 *   eval out1 mod m = bytes_eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(
    uint64_t out1[5], const uint8_t arg1[32]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    uint64_t x13;
    uint64_t x14;
    uint64_t x15;
    uint64_t x16;
    uint64_t x17;
    uint64_t x18;
    uint64_t x19;
    uint64_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint8_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint8_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint8_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint8_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    x1 = ((uint64_t)(arg1[31]) << 43);
    x2 = ((uint64_t)(arg1[30]) << 35);
    x3 = ((uint64_t)(arg1[29]) << 27);
    x4 = ((uint64_t)(arg1[28]) << 19);
    x5 = ((uint64_t)(arg1[27]) << 11);
    x6 = ((uint64_t)(arg1[26]) << 3);
    x7 = ((uint64_t)(arg1[25]) << 46);
    x8 = ((uint64_t)(arg1[24]) << 38);
    x9 = ((uint64_t)(arg1[23]) << 30);
    x10 = ((uint64_t)(arg1[22]) << 22);
    x11 = ((uint64_t)(arg1[21]) << 14);
    x12 = ((uint64_t)(arg1[20]) << 6);
    x13 = ((uint64_t)(arg1[19]) << 49);
    x14 = ((uint64_t)(arg1[18]) << 41);
    x15 = ((uint64_t)(arg1[17]) << 33);
    x16 = ((uint64_t)(arg1[16]) << 25);
    x17 = ((uint64_t)(arg1[15]) << 17);
    x18 = ((uint64_t)(arg1[14]) << 9);
    x19 = ((uint64_t)(arg1[13]) * 0x2);
    x20 = ((uint64_t)(arg1[12]) << 44);
    x21 = ((uint64_t)(arg1[11]) << 36);
    x22 = ((uint64_t)(arg1[10]) << 28);
    x23 = ((uint64_t)(arg1[9]) << 20);
    x24 = ((uint64_t)(arg1[8]) << 12);
    x25 = ((uint64_t)(arg1[7]) << 4);
    x26 = ((uint64_t)(arg1[6]) << 48);
    x27 = ((uint64_t)(arg1[5]) << 40);
    x28 = ((uint64_t)(arg1[4]) << 32);
    x29 = ((uint64_t)(arg1[3]) << 24);
    x30 = ((uint64_t)(arg1[2]) << 16);
    x31 = ((uint64_t)(arg1[1]) << 8);
    x32 = (arg1[0]);
    x33 = (x31 + (uint64_t)x32);
    x34 = (x30 + x33);
    x35 = (x29 + x34);
    x36 = (x28 + x35);
    x37 = (x27 + x36);
    x38 = (x26 + x37);
    x39 = (x38 & UINT64_C(0xfffffffffffff));
    x40 = (uint8_t)(x38 >> 52);
    x41 = (x25 + (uint64_t)x40);
    x42 = (x24 + x41);
    x43 = (x23 + x42);
    x44 = (x22 + x43);
    x45 = (x21 + x44);
    x46 = (x20 + x45);
    x47 = (x46 & UINT64_C(0x7ffffffffffff));
    x48 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x46 >> 51);
    x49 = (x19 + (uint64_t)x48);
    x50 = (x18 + x49);
    x51 = (x17 + x50);
    x52 = (x16 + x51);
    x53 = (x15 + x52);
    x54 = (x14 + x53);
    x55 = (x13 + x54);
    x56 = (x55 & UINT64_C(0x7ffffffffffff));
    x57 = (uint8_t)(x55 >> 51);
    x58 = (x12 + (uint64_t)x57);
    x59 = (x11 + x58);
    x60 = (x10 + x59);
    x61 = (x9 + x60);
    x62 = (x8 + x61);
    x63 = (x7 + x62);
    x64 = (x63 & UINT64_C(0x7ffffffffffff));
    x65 = (uint8_t)(x63 >> 51);
    x66 = (x6 + (uint64_t)x65);
    x67 = (x5 + x66);
    x68 = (x4 + x67);
    x69 = (x3 + x68);
    x70 = (x2 + x69);
    x71 = (x1 + x70);
    out1[0] = x39;
    out1[1] = x47;
    out1[2] = x56;
    out1[3] = x64;
    out1[4] = x71;
}

/* END verbatim fiat code */

/*-
 * Finite field inversion via FLT.
 * NB: this is not a real Fiat function, just named that way for consistency.
 * Autogenerated: ecp/id_tc26_gost_3410_2012_256_paramSetA/fe_inv.op3
 * custom repunit addition chain
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(fe_t output,
                                                          const fe_t t1) {
    int i;
    /* temporary variables */
    fe_t acc, t16, t164, t2, t246, t32, t4, t64, t8, t80, t82;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, acc, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t4, acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t4);
    for (i = 0; i < 3; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t8, acc, t4);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t8);
    for (i = 0; i < 7; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t16, acc, t8);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t16);
    for (i = 0; i < 15; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t32, acc, t16);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t32);
    for (i = 0; i < 31; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t64, acc, t32);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t64);
    for (i = 0; i < 15; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t80, acc, t16);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t80);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t82, acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t82);
    for (i = 0; i < 81; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t164, acc, t82);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t164);
    for (i = 0; i < 81; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t246, acc, t82);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t246);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t2);
    for (i = 0; i < 3; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(output, acc, t1);
}

/* curve coefficient constants */

static const limb_t const_one[5] = {
    UINT64_C(0x0000000000000001), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000)};

static const limb_t const_d[5] = {
    UINT64_C(0x0002C32D6DC7BFFB), UINT64_C(0x0002897009AF7E52),
    UINT64_C(0x0005AA3026573BEC), UINT64_C(0x0006A055E2F0E73E),
    UINT64_C(0x0000302FB5BE0C1F)};

static const limb_t const_S[5] = {
    UINT64_C(0x00074F34A48E0ECD), UINT64_C(0x00075DA3FD94206B),
    UINT64_C(0x00029573F66A3104), UINT64_C(0x000057EA8743C630),
    UINT64_C(0x0003F3F412907CF8)};

static const limb_t const_T[5] = {
    UINT64_C(0x000075DCE7A14AAA), UINT64_C(0x0005C192AC47EA63),
    UINT64_C(0x0000F1B2B10E89FC), UINT64_C(0x0007C563A5D2D135),
    UINT64_C(0x00000807F39FACAF)};

/* LUT for scalar multiplication by comb interleaving */
static const pt_aff_t lut_cmb[14][16] = {
    {
        {{UINT64_C(0x000000000000000D), UINT64_C(0x0000000000000000),
          UINT64_C(0x0000000000000000), UINT64_C(0x0000000000000000),
          UINT64_C(0x0000000000000000)},
         {UINT64_C(0x000B2592DBA300E7), UINT64_C(0x0003E87F22E81F92),
          UINT64_C(0x00060EC939CFDF1B), UINT64_C(0x0006CD212230E3EA),
          UINT64_C(0x00030650F195523A)},
         {UINT64_C(0x0000E8752747155F), UINT64_C(0x0002CE74C5C99A73),
          UINT64_C(0x0006C037EF8E5465), UINT64_C(0x00006AAEBC7B92EB),
          UINT64_C(0x0007521C44952CFD)}},
        {{UINT64_C(0x000A63D93451134C), UINT64_C(0x0003F7A46C1C4CA7),
          UINT64_C(0x0002AF35693661CE), UINT64_C(0x0005313F13E9CC60),
          UINT64_C(0x00054E15958A5D97)},
         {UINT64_C(0x0004525DDCDB5791), UINT64_C(0x000736AACCCB7BC8),
          UINT64_C(0x000174F4885153CF), UINT64_C(0x0000CB27666A2367),
          UINT64_C(0x00016BDEBB39E143)},
         {UINT64_C(0x000C732788CBC030), UINT64_C(0x0005C721E676D06F),
          UINT64_C(0x00024101FDBBCCE2), UINT64_C(0x00059473AE8CD7CA),
          UINT64_C(0x000586898AC013B2)}},
        {{UINT64_C(0x000A894E457058AD), UINT64_C(0x00047D7F48E61013),
          UINT64_C(0x00035B4A672FE227), UINT64_C(0x0002A23302C060B2),
          UINT64_C(0x0000F011D09FB149)},
         {UINT64_C(0x000FB80EB0B22BCC), UINT64_C(0x000606DA3C1FAC0B),
          UINT64_C(0x00049BB0CE05B2EE), UINT64_C(0x000513F1BE099F41),
          UINT64_C(0x0002AEE30C375862)},
         {UINT64_C(0x0004ED5422831C0C), UINT64_C(0x0007ED2E735EF7E7),
          UINT64_C(0x0006F38521F75F30), UINT64_C(0x00040F086DBFA045),
          UINT64_C(0x0002BA6DCB264756)}},
        {{UINT64_C(0x000E66CD5DB0B356), UINT64_C(0x00068408F9A4857D),
          UINT64_C(0x0001F32E8FC1FD9F), UINT64_C(0x0006283C082BF3EE),
          UINT64_C(0x0007B28870E5B67C)},
         {UINT64_C(0x000FE1FDB8EFDCBE), UINT64_C(0x0003F21273425384),
          UINT64_C(0x0005870EEC15951B), UINT64_C(0x0006C844F52108D5),
          UINT64_C(0x0001CD6F3261AFA2)},
         {UINT64_C(0x000712D2BEAA7E64), UINT64_C(0x0006CA4E1492A890),
          UINT64_C(0x0001F6F63F92C63C), UINT64_C(0x0007E2FFA1F1C544),
          UINT64_C(0x0002725A45A8E238)}},
        {{UINT64_C(0x0003E27F874C6781), UINT64_C(0x000080BDDC3E44D5),
          UINT64_C(0x0005AFC7A11A0693), UINT64_C(0x0001B7278AA23AFD),
          UINT64_C(0x0004D7CF0D085A3B)},
         {UINT64_C(0x000D80090DD1AE96), UINT64_C(0x0002433971543626),
          UINT64_C(0x000276F2FB600A27), UINT64_C(0x0005E6BA3FC73B39),
          UINT64_C(0x0005F6DBA95D6230)},
         {UINT64_C(0x000E7885A2499A0E), UINT64_C(0x0004547B7F6C185E),
          UINT64_C(0x000003DCACBBA1C5), UINT64_C(0x000642667D8CF12B),
          UINT64_C(0x0002EA8E4237A0D7)}},
        {{UINT64_C(0x000FDE7B60226F30), UINT64_C(0x0006E61454CC4C1B),
          UINT64_C(0x000479C4C24E0377), UINT64_C(0x0000B58C33813041),
          UINT64_C(0x0002403059A551B8)},
         {UINT64_C(0x000F0EF2E06AE25F), UINT64_C(0x00065B351500FA13),
          UINT64_C(0x0004884D79149E34), UINT64_C(0x00020A52F78F4D69),
          UINT64_C(0x00052A46E43D1E6F)},
         {UINT64_C(0x000D0ECC557F4E9D), UINT64_C(0x0007D12B76DDBF72),
          UINT64_C(0x00001E35D93266D1), UINT64_C(0x00060EC982223C71),
          UINT64_C(0x0001388A4FBA5CC2)}},
        {{UINT64_C(0x000288DD0895A16C), UINT64_C(0x000602071D0EFE2E),
          UINT64_C(0x0007DEA3E3BA4068), UINT64_C(0x00049DB99EA55E0F),
          UINT64_C(0x00057C4FBA5BF59F)},
         {UINT64_C(0x00015BEBA4E2B732), UINT64_C(0x0001F7F8E714E8B3),
          UINT64_C(0x0000A952EF156C40), UINT64_C(0x00017371509C8E81),
          UINT64_C(0x000362B2B97378ED)},
         {UINT64_C(0x000A85D2D8D51136), UINT64_C(0x0005F222A81C7F67),
          UINT64_C(0x0007182E92B465DA), UINT64_C(0x00037F522C4E8E6D),
          UINT64_C(0x0004310842F64A2B)}},
        {{UINT64_C(0x0005A383213DC33C), UINT64_C(0x0007E9B816CD42C6),
          UINT64_C(0x0007FA8AE87087AA), UINT64_C(0x0004E55F40D4A3EE),
          UINT64_C(0x0004F6D263536B6D)},
         {UINT64_C(0x00020F67059F55A3), UINT64_C(0x00007763EEA276D3),
          UINT64_C(0x00019A17F705B71A), UINT64_C(0x0005944E63EB82FE),
          UINT64_C(0x0007650A0DC5D1CB)},
         {UINT64_C(0x000FCD65DD4C828B), UINT64_C(0x0004CA6637691EA4),
          UINT64_C(0x0003CCA5D1052701), UINT64_C(0x000712031F0647A6),
          UINT64_C(0x00021CFF42584B2A)}},
        {{UINT64_C(0x0006C8A92F922362), UINT64_C(0x00044311FB926B9A),
          UINT64_C(0x00024A255F064898), UINT64_C(0x0004502C0490E488),
          UINT64_C(0x000181B9C7BE07A7)},
         {UINT64_C(0x000AC7EB3F012FCF), UINT64_C(0x00066FE14D676159),
          UINT64_C(0x0000E913992C9940), UINT64_C(0x0005A66B28860B80),
          UINT64_C(0x0004FA013BB9A8C7)},
         {UINT64_C(0x0000AD18CD3393AB), UINT64_C(0x000615E7DB492BCF),
          UINT64_C(0x00065C59E4108193), UINT64_C(0x00001C03C21B9CD4),
          UINT64_C(0x00053FD4D84C40B2)}},
        {{UINT64_C(0x0003FC89A2C6B0CB), UINT64_C(0x0000F3FE6593C6B3),
          UINT64_C(0x00033E7B9EFD7774), UINT64_C(0x000202569ACD3FD6),
          UINT64_C(0x000304E71BD74A19)},
         {UINT64_C(0x00070B3E67C53736), UINT64_C(0x00062C9877C0935F),
          UINT64_C(0x00005596CE3DED78), UINT64_C(0x000065742F67B602),
          UINT64_C(0x0001F8FD1E49E6FF)},
         {UINT64_C(0x000C98D8B880CCBF), UINT64_C(0x0002C564715F7115),
          UINT64_C(0x0003131E65C288F0), UINT64_C(0x00032E11B2E67FC3),
          UINT64_C(0x000306235C4203E9)}},
        {{UINT64_C(0x0002EE57BC2F49E9), UINT64_C(0x00072B3E30AC4A02),
          UINT64_C(0x00007DB1E3840852), UINT64_C(0x0001DD33D665342A),
          UINT64_C(0x0001A7633225AEB5)},
         {UINT64_C(0x000B7F7B5B762A44), UINT64_C(0x000331A035435FA2),
          UINT64_C(0x00067E3DC1DDB9A7), UINT64_C(0x0004B831B6E64D06),
          UINT64_C(0x0006F2281134099E)},
         {UINT64_C(0x000B980EF7392E55), UINT64_C(0x0000B89E617A9CA2),
          UINT64_C(0x0007ABA1A8BE9857), UINT64_C(0x00039ACA46853BD7),
          UINT64_C(0x00075B4D3E20EED0)}},
        {{UINT64_C(0x000250EDAC297799), UINT64_C(0x0005F2C18C18BF27),
          UINT64_C(0x0001CA515336C8CA), UINT64_C(0x000559E66BDEB0DF),
          UINT64_C(0x0001DEEDAD609A8E)},
         {UINT64_C(0x000955E31C58DEDB), UINT64_C(0x000669D9C0842B27),
          UINT64_C(0x0006DEB790D54552), UINT64_C(0x0003F019C46019DA),
          UINT64_C(0x000471F509C91C79)},
         {UINT64_C(0x000732102C57B4C9), UINT64_C(0x0006A2A9C28278B1),
          UINT64_C(0x0002324651BB700F), UINT64_C(0x00035DAF397E77FD),
          UINT64_C(0x000663BBD174F83C)}},
        {{UINT64_C(0x000B929354FC042F), UINT64_C(0x00011C492ECCD647),
          UINT64_C(0x00009741F291BEF2), UINT64_C(0x0001B4C7302C0158),
          UINT64_C(0x00053AE1A0A0CF65)},
         {UINT64_C(0x0003EF8B2C9BC0FD), UINT64_C(0x00002CC7CA0F574C),
          UINT64_C(0x0006D4B65A91C0C0), UINT64_C(0x00037510A240479F),
          UINT64_C(0x0000C64FA252A385)},
         {UINT64_C(0x000A88BD59ED7C65), UINT64_C(0x0007ADDAE9CF1B08),
          UINT64_C(0x00019BA0F9C07F82), UINT64_C(0x000678DB16C95452),
          UINT64_C(0x000056623A2D43CD)}},
        {{UINT64_C(0x000B7E8A540D0BB7), UINT64_C(0x000584F51D57A3A5),
          UINT64_C(0x0001D8F204E79366), UINT64_C(0x00050AB42D36BB78),
          UINT64_C(0x0001F1939E53C5F0)},
         {UINT64_C(0x0006F36130EDF74C), UINT64_C(0x0004F887F8A3C1AE),
          UINT64_C(0x000287BA42F1A8B2), UINT64_C(0x00059F26236FEDC0),
          UINT64_C(0x0004E7E15665AB6B)},
         {UINT64_C(0x000BEF0A5861C3BC), UINT64_C(0x0001C09CD88554A1),
          UINT64_C(0x00061D92810426B4), UINT64_C(0x0000B7288E75D464),
          UINT64_C(0x0006309911A5EDED)}},
        {{UINT64_C(0x00085B811B64F095), UINT64_C(0x0005D8DFEF0DA78A),
          UINT64_C(0x0000FAC6BD61618C), UINT64_C(0x0004BAAA6C8B2729),
          UINT64_C(0x0004B56E9D2ABD89)},
         {UINT64_C(0x00043E639CD4D3BA), UINT64_C(0x00074DEABEABDB28),
          UINT64_C(0x0007E3FBA43ED6F6), UINT64_C(0x0003AEE8B7AFF1C3),
          UINT64_C(0x0003F53F79D2EE85)},
         {UINT64_C(0x000D24432D4F4AA5), UINT64_C(0x00078D7CF1ED698D),
          UINT64_C(0x0004FF1A468ED10E), UINT64_C(0x000401D16C0D8D9F),
          UINT64_C(0x0002F258FE117334)}},
        {{UINT64_C(0x000FB19D593A119F), UINT64_C(0x000573B9C8EF8EF8),
          UINT64_C(0x0003D940302F6503), UINT64_C(0x000182EF3D826EE0),
          UINT64_C(0x0003F7471C590F65)},
         {UINT64_C(0x000299B06DAA3C6C), UINT64_C(0x0007746E79EDD442),
          UINT64_C(0x0003AC79E8C5E3A4), UINT64_C(0x0007EEC70EB2D955),
          UINT64_C(0x000725068BB9E5F2)},
         {UINT64_C(0x0002BC325E5A930B), UINT64_C(0x000107F46E629456),
          UINT64_C(0x000640B07B73E5D1), UINT64_C(0x00001279DBE60561),
          UINT64_C(0x000225709C5657E7)}},
    },
    {
        {{UINT64_C(0x0000192B0CEABA3B), UINT64_C(0x0000C849471353C7),
          UINT64_C(0x0007933577181965), UINT64_C(0x000142C9EA69016E),
          UINT64_C(0x0004567E065866D3)},
         {UINT64_C(0x000B6B66959B52D6), UINT64_C(0x000430844ADC8F3E),
          UINT64_C(0x0006380BD764FFC3), UINT64_C(0x00016D901A803EAC),
          UINT64_C(0x000086B9A477DC01)},
         {UINT64_C(0x00023367321E8B93), UINT64_C(0x00032EA758921A81),
          UINT64_C(0x0004882218493C4B), UINT64_C(0x000363B5842D9A7D),
          UINT64_C(0x00015A99D8110971)}},
        {{UINT64_C(0x000300698CE29666), UINT64_C(0x0004EECA6154042B),
          UINT64_C(0x000166B994CFB4CC), UINT64_C(0x0004ACF422430E4C),
          UINT64_C(0x000025F72D8D2619)},
         {UINT64_C(0x000675E071BC36F8), UINT64_C(0x0004DFF904D1F8C7),
          UINT64_C(0x0003D505499703DF), UINT64_C(0x00020099265EDEB5),
          UINT64_C(0x0003CC88B51F7700)},
         {UINT64_C(0x0007D2FCAA25E5B3), UINT64_C(0x000265D6E2479291),
          UINT64_C(0x000011547C45B8FB), UINT64_C(0x0007BB46D4B019E2),
          UINT64_C(0x00056F6CD9966342)}},
        {{UINT64_C(0x000716456F1AF846), UINT64_C(0x0006EC66D0F766DB),
          UINT64_C(0x00046A2790A7CDEB), UINT64_C(0x0007D9FE58069E97),
          UINT64_C(0x0006F455E4C8BAF4)},
         {UINT64_C(0x0002DE3D52161A5B), UINT64_C(0x0001322EF1230B3D),
          UINT64_C(0x000064226A5D94E2), UINT64_C(0x0004F0C2721E80E3),
          UINT64_C(0x00055430C4FD1472)},
         {UINT64_C(0x000F3FE53622B34E), UINT64_C(0x000400E37E2F6B5E),
          UINT64_C(0x000786904CA5BE13), UINT64_C(0x000447C21E951630),
          UINT64_C(0x00030E6760567848)}},
        {{UINT64_C(0x000B726FC584B729), UINT64_C(0x000414D51BCE0A43),
          UINT64_C(0x000618F6813D930C), UINT64_C(0x00040A56D66C1756),
          UINT64_C(0x0003D984C56008C4)},
         {UINT64_C(0x000EB2683ACBED46), UINT64_C(0x000652CB350659D6),
          UINT64_C(0x00013634382FCA77), UINT64_C(0x0002F5D25E61B8FF),
          UINT64_C(0x0000B4620F79B12A)},
         {UINT64_C(0x0001E7A57930B5B8), UINT64_C(0x00073653EC2B76D1),
          UINT64_C(0x000613302FE0B30D), UINT64_C(0x0000A6C4E5761F63),
          UINT64_C(0x0006ADF86A72CC92)}},
        {{UINT64_C(0x00019186A824FDD7), UINT64_C(0x00011924F7F2750C),
          UINT64_C(0x00029AD4044367A7), UINT64_C(0x0007E9538B9DECCA),
          UINT64_C(0x000280D45D483E14)},
         {UINT64_C(0x000BB52AB225DCB5), UINT64_C(0x00028B165CB1E4DA),
          UINT64_C(0x0005BC09B05FCBE4), UINT64_C(0x000381D12C463328),
          UINT64_C(0x00076D0B0F76F027)},
         {UINT64_C(0x000FEDF306C25C01), UINT64_C(0x0003FECB41117000),
          UINT64_C(0x0002FD6A35E50E23), UINT64_C(0x000337ED65B03733),
          UINT64_C(0x0000574ECBAE7CDC)}},
        {{UINT64_C(0x0006DB58E6ED0D33), UINT64_C(0x0006B3191FF18B0A),
          UINT64_C(0x0003EA3B063F5E91), UINT64_C(0x00046C617AC41DAE),
          UINT64_C(0x00055B8985867163)},
         {UINT64_C(0x000FE332D0609F49), UINT64_C(0x00029E2B6C0590BB),
          UINT64_C(0x00027B083A8281E3), UINT64_C(0x00023F54A6691576),
          UINT64_C(0x0001F9B2324B7AF4)},
         {UINT64_C(0x000B28B2B17C7FE4), UINT64_C(0x0006080357FBA43E),
          UINT64_C(0x000615C5B81FBEF7), UINT64_C(0x0003954270A7909A),
          UINT64_C(0x0001A2F47D0A04EB)}},
        {{UINT64_C(0x000BA17581986197), UINT64_C(0x00034870F30008DA),
          UINT64_C(0x00013CA300CCA469), UINT64_C(0x0000B162692148B6),
          UINT64_C(0x0003F7CF95609F4D)},
         {UINT64_C(0x000BAE83E74F002F), UINT64_C(0x00069841112F14B8),
          UINT64_C(0x00078C4EFE35A991), UINT64_C(0x00060BCE9ED310B3),
          UINT64_C(0x000754449FD8083C)},
         {UINT64_C(0x000AF607F5B16E11), UINT64_C(0x000353A2B8ACC91E),
          UINT64_C(0x0000EF1D9E0EF391), UINT64_C(0x0004541CFF0EA8DF),
          UINT64_C(0x00016AEAE7B3F2F0)}},
        {{UINT64_C(0x0004D0C351D81066), UINT64_C(0x0000558E4F8F8B3B),
          UINT64_C(0x00016AB2E6954DA6), UINT64_C(0x0001322A57507762),
          UINT64_C(0x000048180B80AB80)},
         {UINT64_C(0x0008458BFFCB303A), UINT64_C(0x00022F59A0891BA3),
          UINT64_C(0x00015FD6CA3084EF), UINT64_C(0x000096545EB531A3),
          UINT64_C(0x0006E575563BC013)},
         {UINT64_C(0x00051F33B08869E8), UINT64_C(0x00026605DC18D15D),
          UINT64_C(0x00055CAC08CDE2C6), UINT64_C(0x000688FFA5C8BAE7),
          UINT64_C(0x0001B5E984904B9C)}},
        {{UINT64_C(0x0006B8B443814972), UINT64_C(0x00055FB9A76E0CC9),
          UINT64_C(0x00009CE2AAB60A30), UINT64_C(0x0003EF83A2F57151),
          UINT64_C(0x00054D67D9A4F513)},
         {UINT64_C(0x0007BECDA40A587F), UINT64_C(0x00066610E3756505),
          UINT64_C(0x000667AED7C671A5), UINT64_C(0x00045C3E37289FA8),
          UINT64_C(0x0003EE1DEB8BF15F)},
         {UINT64_C(0x00036DA9491A8276), UINT64_C(0x0007F3BA7F45D5F3),
          UINT64_C(0x0005491780986582), UINT64_C(0x00017D2A21BAB7FA),
          UINT64_C(0x00002173C231405F)}},
        {{UINT64_C(0x000F3DB9923407C2), UINT64_C(0x00034756D5DCF8D4),
          UINT64_C(0x00012004CC7C2A34), UINT64_C(0x000333FE8258F7D8),
          UINT64_C(0x00022BB7C7E2AFDF)},
         {UINT64_C(0x0000F405EE8CE31C), UINT64_C(0x0000EAD90D22334E),
          UINT64_C(0x00033D2A3C4C1541), UINT64_C(0x0000C208CD2F4BA6),
          UINT64_C(0x000531D1A514C9B5)},
         {UINT64_C(0x0009F9F19D9C76D2), UINT64_C(0x00050FDE66CC4397),
          UINT64_C(0x00007E0C2178203B), UINT64_C(0x0002A55E72D96E80),
          UINT64_C(0x0003C184A5AB3FAA)}},
        {{UINT64_C(0x0001563D94E10FCD), UINT64_C(0x0002F2A35D0DB543),
          UINT64_C(0x0006E7768E05CFD7), UINT64_C(0x000275C5FC14D586),
          UINT64_C(0x000433EC2F348324)},
         {UINT64_C(0x0004D3D54C94DA0B), UINT64_C(0x000794DDFDB2EE1E),
          UINT64_C(0x000682353D05BB00), UINT64_C(0x00062D8667C93A51),
          UINT64_C(0x0004DADCA5A8CE84)},
         {UINT64_C(0x0008950DCBA27A08), UINT64_C(0x00059BB3D4E3ABE9),
          UINT64_C(0x0003822C18A53312), UINT64_C(0x000244B26BFD9780),
          UINT64_C(0x00021363F385A671)}},
        {{UINT64_C(0x0008D4A03FC03231), UINT64_C(0x00023E2A19529871),
          UINT64_C(0x0007E6F5F8C1E22D), UINT64_C(0x0005603802B4085E),
          UINT64_C(0x0003FFA733F66BE0)},
         {UINT64_C(0x000CC0D81C2C08E7), UINT64_C(0x00018D23FC4F115C),
          UINT64_C(0x00003051462BF328), UINT64_C(0x0004DB5D451FA8EB),
          UINT64_C(0x00010ACD436DB7AA)},
         {UINT64_C(0x000316FC2858C718), UINT64_C(0x0001C94A21AE0F16),
          UINT64_C(0x0007FCCA25487FEF), UINT64_C(0x00075860E5CD985C),
          UINT64_C(0x0005097BC8151C25)}},
        {{UINT64_C(0x000DD0D0A35B9B1D), UINT64_C(0x00069BAAA4B56FF0),
          UINT64_C(0x0004C778899053BE), UINT64_C(0x00002FDC9FA10ADD),
          UINT64_C(0x0007A47AFD52F5E3)},
         {UINT64_C(0x000E3285C26BE9BF), UINT64_C(0x00033A7071D32E1A),
          UINT64_C(0x0005D4D66BCC2FAF), UINT64_C(0x00048588A74F8EA8),
          UINT64_C(0x00067F167FCDC66E)},
         {UINT64_C(0x0008784A422379C4), UINT64_C(0x0007470006E5302B),
          UINT64_C(0x000021E9843D3B99), UINT64_C(0x0003D41527492DDA),
          UINT64_C(0x0006326EC5940067)}},
        {{UINT64_C(0x000FF43284BB2391), UINT64_C(0x00053CD58E6D70AA),
          UINT64_C(0x0006AE6FA42E6DDF), UINT64_C(0x00028B006426C7D9),
          UINT64_C(0x00052B4EB53AA929)},
         {UINT64_C(0x0005CDDE09C1B9A5), UINT64_C(0x0000A96E981F65EC),
          UINT64_C(0x0001B2C7538B9E62), UINT64_C(0x000441A392439DEB),
          UINT64_C(0x0006A2B722DC6381)},
         {UINT64_C(0x000900CFD32A9792), UINT64_C(0x0001A7B4A6CF54D6),
          UINT64_C(0x0003CBAB4ADCD36D), UINT64_C(0x0007B9C1AA60B86D),
          UINT64_C(0x0000702520E53679)}},
        {{UINT64_C(0x000C9A305D7834CB), UINT64_C(0x0005B7B9FE0CCCE7),
          UINT64_C(0x000016A6095A241B), UINT64_C(0x0004B712FDF75B13),
          UINT64_C(0x0002C53655BBD91A)},
         {UINT64_C(0x0008A6ED2314DDC8), UINT64_C(0x0007DA70CDF279E7),
          UINT64_C(0x0006D2D0C3606E50), UINT64_C(0x0003283DAAA8966F),
          UINT64_C(0x0006EF64B4E41560)},
         {UINT64_C(0x00042A16ECAEB238), UINT64_C(0x0005A981B79EAFF2),
          UINT64_C(0x0005427BFC071A2B), UINT64_C(0x00049349A5AB5626),
          UINT64_C(0x00041BC86960BD2C)}},
        {{UINT64_C(0x000ABCDBE3A9E5D5), UINT64_C(0x000405363A4B3483),
          UINT64_C(0x000120CFBB24A873), UINT64_C(0x0005AAE474A462D0),
          UINT64_C(0x00035D540394697D)},
         {UINT64_C(0x000A64659EFE52C3), UINT64_C(0x0004DB773D047E7A),
          UINT64_C(0x0002162ABB1CFD3D), UINT64_C(0x00015B391C39D9D5),
          UINT64_C(0x00045B7D89DC266B)},
         {UINT64_C(0x000B2D1FBC9FC441), UINT64_C(0x00012722CD49F3E8),
          UINT64_C(0x0004A0CFFD68110C), UINT64_C(0x0001B6ECFF11A156),
          UINT64_C(0x0007D2F58CE5E926)}},
    },
    {
        {{UINT64_C(0x000E1788C8C3CC62), UINT64_C(0x00043FD128363765),
          UINT64_C(0x00069AE472062EAB), UINT64_C(0x00035D256FE91217),
          UINT64_C(0x00067910C5563600)},
         {UINT64_C(0x000F768B1233A3DB), UINT64_C(0x0007DF4C7CD2EA86),
          UINT64_C(0x0002CAE86EF8DA2A), UINT64_C(0x0002329535DE9283),
          UINT64_C(0x0005A07C9F89C3C4)},
         {UINT64_C(0x000054FF768F9CA3), UINT64_C(0x00043D9E4B66B860),
          UINT64_C(0x0007DF913CA8B203), UINT64_C(0x000719B3B558DDAA),
          UINT64_C(0x00049B2BB9B923F3)}},
        {{UINT64_C(0x000890BE7F18CD54), UINT64_C(0x00044AEBC732FC6C),
          UINT64_C(0x0000F98B4946A855), UINT64_C(0x00012B28B7A38B2D),
          UINT64_C(0x00030C9FAD322292)},
         {UINT64_C(0x0009D307A4D23D40), UINT64_C(0x00056FC224E7F4C7),
          UINT64_C(0x00054A676DE6F4D3), UINT64_C(0x0006E886B5E9AB59),
          UINT64_C(0x0005165D23A85BC8)},
         {UINT64_C(0x000ED25967873511), UINT64_C(0x0006C6A9C182B080),
          UINT64_C(0x0006978A72802608), UINT64_C(0x0006BF34B9518468),
          UINT64_C(0x0006871D973DF84B)}},
        {{UINT64_C(0x000F875D00421E67), UINT64_C(0x00042EDCCB1C82C1),
          UINT64_C(0x000048FC0A2E1BB9), UINT64_C(0x000733FCF9823770),
          UINT64_C(0x00049255AE9DEDFF)},
         {UINT64_C(0x000B76A27F806A6B), UINT64_C(0x0002C8618FECD22F),
          UINT64_C(0x0007CA416389DB51), UINT64_C(0x00072B231F4E3DF8),
          UINT64_C(0x00037E6C81D4C8A3)},
         {UINT64_C(0x000315AE714C164A), UINT64_C(0x0000651074CD146C),
          UINT64_C(0x00070A110B6FA83C), UINT64_C(0x000292BEB2445915),
          UINT64_C(0x0007BAE86E66340A)}},
        {{UINT64_C(0x0005A42303FA5032), UINT64_C(0x00079C0EED4C5482),
          UINT64_C(0x00070FAF28317AA9), UINT64_C(0x000252F900BCA786),
          UINT64_C(0x0004C6DF6AC32D72)},
         {UINT64_C(0x000899EF622C1092), UINT64_C(0x00024300EA163F80),
          UINT64_C(0x0005D78541F9C58D), UINT64_C(0x0000A82CF29A5335),
          UINT64_C(0x00057651841E4857)},
         {UINT64_C(0x0002DDAF4534E914), UINT64_C(0x0005C3F8E3CEDF3D),
          UINT64_C(0x0007E428DFE30ABB), UINT64_C(0x0000CFA8EF984856),
          UINT64_C(0x0007F54DE1239314)}},
        {{UINT64_C(0x0003E194BB770B21), UINT64_C(0x0005FE5004F0D44C),
          UINT64_C(0x00000E420E05B0CD), UINT64_C(0x0007F4C1EB04ADEA),
          UINT64_C(0x00042CF75F5849CB)},
         {UINT64_C(0x000DFBFF2F58474E), UINT64_C(0x000023E38CACC0A2),
          UINT64_C(0x0007940AAA1D2AEF), UINT64_C(0x00066B3671EF7D12),
          UINT64_C(0x0002262304E8FD69)},
         {UINT64_C(0x0008190A96FA31A5), UINT64_C(0x000342B2840B29B6),
          UINT64_C(0x000692023CF41B60), UINT64_C(0x000438CCB9AD9326),
          UINT64_C(0x0006A74AEEF91383)}},
        {{UINT64_C(0x00018FBC15EE9825), UINT64_C(0x000471ADE4E86042),
          UINT64_C(0x000460985BA9C276), UINT64_C(0x0000273A2CD2F02D),
          UINT64_C(0x0003D497885E0289)},
         {UINT64_C(0x000FB3F88C29D5D7), UINT64_C(0x00006A92797F73B7),
          UINT64_C(0x00040AF0BB921EC0), UINT64_C(0x00056F0E3B22C834),
          UINT64_C(0x0002624649A67166)},
         {UINT64_C(0x00027A77B2D5B395), UINT64_C(0x000485B55DD21EC2),
          UINT64_C(0x00018F0DD390533C), UINT64_C(0x0002C22DC574DCED),
          UINT64_C(0x0002023C88609399)}},
        {{UINT64_C(0x0000652B6A1715E4), UINT64_C(0x00010936CCCDBCBB),
          UINT64_C(0x00075D99584830CC), UINT64_C(0x00013967DE55AE9B),
          UINT64_C(0x000497B11ABB2BA5)},
         {UINT64_C(0x00029098D0D4B7BF), UINT64_C(0x000148C12832BF02),
          UINT64_C(0x0001ACF592227FC4), UINT64_C(0x000361B3459461F5),
          UINT64_C(0x00017789D92B4734)},
         {UINT64_C(0x00051C79B949CE9C), UINT64_C(0x0003A90D40F0533B),
          UINT64_C(0x0000FF1DE7855E3C), UINT64_C(0x00063C0B316F0292),
          UINT64_C(0x00079EE9916B16AB)}},
        {{UINT64_C(0x000BAF756382E001), UINT64_C(0x00074E66D14AA815),
          UINT64_C(0x0005B5DA0DFB4345), UINT64_C(0x0006A98DDBA8EDB0),
          UINT64_C(0x00036757B46A7CB2)},
         {UINT64_C(0x000D2030D203D167), UINT64_C(0x0003C138F45B97D2),
          UINT64_C(0x0000D68249398DBC), UINT64_C(0x00024992D9AC907A),
          UINT64_C(0x00054B2D81985107)},
         {UINT64_C(0x000C135EB42B443B), UINT64_C(0x000459E57D3F091F),
          UINT64_C(0x0007365731E3CB09), UINT64_C(0x00037913422A2661),
          UINT64_C(0x00022EB791C1E99D)}},
        {{UINT64_C(0x000DBBF41597ABD7), UINT64_C(0x00047A1841FA5CB7),
          UINT64_C(0x0005DFFB8D0489BA), UINT64_C(0x000207F59D5AFDD6),
          UINT64_C(0x0005862958391C33)},
         {UINT64_C(0x00023D6607BF974F), UINT64_C(0x000447A73195D140),
          UINT64_C(0x0001CEA4FE1AACB4), UINT64_C(0x00009432CE509804),
          UINT64_C(0x0002E2C660E2C2A9)},
         {UINT64_C(0x00076405764963F7), UINT64_C(0x0007B075E1B18E3F),
          UINT64_C(0x0003C86F3DB152BE), UINT64_C(0x000717D762BDBB19),
          UINT64_C(0x00009430702D8371)}},
        {{UINT64_C(0x000AF7F915E85486), UINT64_C(0x0000B0BBC2BBB069),
          UINT64_C(0x0004A8AC30D65B82), UINT64_C(0x0001A98BB8FFCCD1),
          UINT64_C(0x0000A60C0955A82C)},
         {UINT64_C(0x00028B7AA8D6E8B3), UINT64_C(0x000599AA3AFD1F06),
          UINT64_C(0x000495C8A53271EA), UINT64_C(0x00010D4AB4581526),
          UINT64_C(0x00005A91911011B4)},
         {UINT64_C(0x00091E63537A8EAF), UINT64_C(0x0004DDC52ABAE41E),
          UINT64_C(0x0003DD0926FDA797), UINT64_C(0x00053806DE4C5BB3),
          UINT64_C(0x0005E6F1B98E4E5F)}},
        {{UINT64_C(0x000ED0D0E611D440), UINT64_C(0x00028F43B4F900C1),
          UINT64_C(0x0004C3467D15A321), UINT64_C(0x0003FA40B92F63A1),
          UINT64_C(0x0004EB1D7C0F97C7)},
         {UINT64_C(0x000F31CB2B424218), UINT64_C(0x00028FC2CBD4B770),
          UINT64_C(0x000049E2E21B9426), UINT64_C(0x0004FBA6C0CA6B59),
          UINT64_C(0x0000910A8BCBADA6)},
         {UINT64_C(0x000062AA84E5A1E8), UINT64_C(0x00055785384B963C),
          UINT64_C(0x000718EE20099ACE), UINT64_C(0x0001AAE6F92CB06B),
          UINT64_C(0x0005C36213B6B09F)}},
        {{UINT64_C(0x00042CD518E7BB1F), UINT64_C(0x000621C5EDE20FF0),
          UINT64_C(0x000784AA45E52004), UINT64_C(0x0001EF5EE32943D1),
          UINT64_C(0x00020EB45F6DCE70)},
         {UINT64_C(0x0001A99725161F74), UINT64_C(0x0000C92D8DE52D6B),
          UINT64_C(0x0004943C545783B6), UINT64_C(0x0000F2A34F6C04CA),
          UINT64_C(0x0004BA95172EB757)},
         {UINT64_C(0x00058B38359EEF2E), UINT64_C(0x00072A003FEEF0E3),
          UINT64_C(0x0005A1A497C5B5E4), UINT64_C(0x0007346A30D5043D),
          UINT64_C(0x0004DCC170CE73B1)}},
        {{UINT64_C(0x0007FCA11D67A5F5), UINT64_C(0x00051F99020E8DA7),
          UINT64_C(0x00032448AD151580), UINT64_C(0x0002D682339FD143),
          UINT64_C(0x0005C59954328210)},
         {UINT64_C(0x000065B65920EA67), UINT64_C(0x00064F016ACC8DB2),
          UINT64_C(0x0002B54FD32DCADE), UINT64_C(0x0007514AC407F433),
          UINT64_C(0x0006FB8E73CDEC87)},
         {UINT64_C(0x0008D3F0CEB6FEF4), UINT64_C(0x000008CD1DAF58F2),
          UINT64_C(0x0005E29BD86720C8), UINT64_C(0x0001F89E2EA7D372),
          UINT64_C(0x0000F12D2FEBC32E)}},
        {{UINT64_C(0x00074C08FE9F6EBB), UINT64_C(0x00019BC5A03773E2),
          UINT64_C(0x0002DC6D42FDA236), UINT64_C(0x00026B76B5DD35AA),
          UINT64_C(0x0004AC140E7698BA)},
         {UINT64_C(0x00007B671BE5225C), UINT64_C(0x00075E4E1423B035),
          UINT64_C(0x0005C5078EE3FB12), UINT64_C(0x00018FE0631F18BA),
          UINT64_C(0x00049C97C216ADA7)},
         {UINT64_C(0x000288CA79DE3FDD), UINT64_C(0x0005F95B9694502C),
          UINT64_C(0x0007A90B27755E7B), UINT64_C(0x0000D6B962A5D7B4),
          UINT64_C(0x0002B73E8EABF5E7)}},
        {{UINT64_C(0x000CF4B90C4F175B), UINT64_C(0x00038B2E4ADFE275),
          UINT64_C(0x00051C55B83D6C95), UINT64_C(0x00055A2D1264C7D4),
          UINT64_C(0x00062E83FE154821)},
         {UINT64_C(0x0008971A5D058482), UINT64_C(0x0000037DDD623E19),
          UINT64_C(0x00032867F2B615A5), UINT64_C(0x000623016E6AEBA8),
          UINT64_C(0x00012A36AB822905)},
         {UINT64_C(0x000120EE148C51AE), UINT64_C(0x00040334FADAA4FB),
          UINT64_C(0x000334965ACA2453), UINT64_C(0x0006D3E8020844BE),
          UINT64_C(0x00067F6DE44BCF25)}},
        {{UINT64_C(0x000105D43C9E9178), UINT64_C(0x0004721A89809540),
          UINT64_C(0x0006FC38C213B1E4), UINT64_C(0x0002647D42F65B1A),
          UINT64_C(0x0000376348D66B34)},
         {UINT64_C(0x000C9949B00A3DA8), UINT64_C(0x0000A0919FC02F1D),
          UINT64_C(0x00028254055C25E6), UINT64_C(0x0001D9499A0AE6F5),
          UINT64_C(0x0001AACE22F92850)},
         {UINT64_C(0x0000AD0E76427572), UINT64_C(0x0004C84545B77E18),
          UINT64_C(0x000288F5B54FDDB7), UINT64_C(0x00038ED121222ABA),
          UINT64_C(0x0003A0976AA7E43F)}},
    },
    {
        {{UINT64_C(0x00081FCF9F18835D), UINT64_C(0x000431F4C3B0D6B0),
          UINT64_C(0x0004948FEC6A3648), UINT64_C(0x0004A9B64ED01851),
          UINT64_C(0x0005A8976BDBA97F)},
         {UINT64_C(0x000B16BF1437E1C3), UINT64_C(0x00037D028CF83551),
          UINT64_C(0x0001D4890BE8B6E0), UINT64_C(0x00014CEE0109B29D),
          UINT64_C(0x0001D11B49FC55FA)},
         {UINT64_C(0x000FF921373DC1C7), UINT64_C(0x00038DE820056599),
          UINT64_C(0x0000120CE481802B), UINT64_C(0x0006FFC1F2C088F4),
          UINT64_C(0x0001EE2D0C6B1DF4)}},
        {{UINT64_C(0x00000FF81329CB03), UINT64_C(0x00068780226033B3),
          UINT64_C(0x0006A2545BA20041), UINT64_C(0x0005B2BDDF462150),
          UINT64_C(0x0003958DB9028FB7)},
         {UINT64_C(0x000C6BC63284596C), UINT64_C(0x000348523D1DBF57),
          UINT64_C(0x00046E3F936916F1), UINT64_C(0x0005F78F7E010886),
          UINT64_C(0x0004BAEFBF32EDCF)},
         {UINT64_C(0x0006B10E2C0DA1C1), UINT64_C(0x000039C183458580),
          UINT64_C(0x00012DB6C7B007C5), UINT64_C(0x0003F3B7176CF39B),
          UINT64_C(0x0003C1F7265871AC)}},
        {{UINT64_C(0x00085B51F8BCFE19), UINT64_C(0x00014E262925BD43),
          UINT64_C(0x00012F59369D7DEC), UINT64_C(0x0005891E437C785D),
          UINT64_C(0x000079EFC6B4ADC9)},
         {UINT64_C(0x00057113F86AEFDE), UINT64_C(0x00031D2A3CBB809F),
          UINT64_C(0x00033DCF8AE19C6C), UINT64_C(0x00002213DF3BA392),
          UINT64_C(0x0002650EDFE84235)},
         {UINT64_C(0x0004FEF6E8E8730E), UINT64_C(0x0002E2C593B029F4),
          UINT64_C(0x0002D3E99D33A67F), UINT64_C(0x00014E290C55B9BF),
          UINT64_C(0x0004403429D2BE3D)}},
        {{UINT64_C(0x000409570F86A1D1), UINT64_C(0x00022B63BCCD0F01),
          UINT64_C(0x0004F7D7B94F1868), UINT64_C(0x0001450E62B7DF7C),
          UINT64_C(0x000694DAA72BBCAC)},
         {UINT64_C(0x000F24FC804E499A), UINT64_C(0x0001C5150B7A2ED2),
          UINT64_C(0x0004AA24EEC36D37), UINT64_C(0x0002132213761A03),
          UINT64_C(0x00075A13822D103C)},
         {UINT64_C(0x000067187C39EB19), UINT64_C(0x0007765373B31B66),
          UINT64_C(0x00029CEAE18E4D79), UINT64_C(0x0002C737CC0BD795),
          UINT64_C(0x0003193EDEEA0158)}},
        {{UINT64_C(0x0005B52C3101E326), UINT64_C(0x0007176502D653B5),
          UINT64_C(0x0001FD26BF9CB194), UINT64_C(0x0004FB67EADB3779),
          UINT64_C(0x0004D0C770A8ACCF)},
         {UINT64_C(0x00011F79B7B344FC), UINT64_C(0x00006663499E87A4),
          UINT64_C(0x000450DAEF594E13), UINT64_C(0x00078C2F195A18D1),
          UINT64_C(0x0007978E36566E63)},
         {UINT64_C(0x0002EB14A8C94333), UINT64_C(0x0001C2F764E483AE),
          UINT64_C(0x00031C462060C077), UINT64_C(0x0003EF30123095CE),
          UINT64_C(0x00021B6A8AF629FF)}},
        {{UINT64_C(0x0006C49C35F232E3), UINT64_C(0x0000B80EEFEC6616),
          UINT64_C(0x000680164DFC3701), UINT64_C(0x00072FF54666B634),
          UINT64_C(0x00068AB4AFC182CD)},
         {UINT64_C(0x000176EF0C655243), UINT64_C(0x00019A1EE66FD2EB),
          UINT64_C(0x0006AF04A498FFA9), UINT64_C(0x0002514F98E20D4A),
          UINT64_C(0x0001A0821E95F930)},
         {UINT64_C(0x000D58CC3800FE7F), UINT64_C(0x00050C9E092A5673),
          UINT64_C(0x00051CB3347178DD), UINT64_C(0x0002C9C1FB2FE677),
          UINT64_C(0x00015EE68A8526B0)}},
        {{UINT64_C(0x000CD72A8856201E), UINT64_C(0x00038E8C397ED335),
          UINT64_C(0x0000B8E2262FC710), UINT64_C(0x000027DC02E17AF3),
          UINT64_C(0x0006A5F450F30E8E)},
         {UINT64_C(0x000B680DE0C07AE0), UINT64_C(0x00054D907D909686),
          UINT64_C(0x000757C2922B0737), UINT64_C(0x000660842FC9A0BA),
          UINT64_C(0x0003FC7B58E7239A)},
         {UINT64_C(0x0004651B7E8AE200), UINT64_C(0x000484D3CB6C7A8E),
          UINT64_C(0x0002F474BA01A0C0), UINT64_C(0x00044AA7003C772D),
          UINT64_C(0x0000371F52FFC53B)}},
        {{UINT64_C(0x0005C3B547E0B571), UINT64_C(0x000209CCABD0AB4B),
          UINT64_C(0x0002D25C9DBF3DB1), UINT64_C(0x000032A6A6DE8522),
          UINT64_C(0x00017A0AD8ECF369)},
         {UINT64_C(0x0009010B6E71332D), UINT64_C(0x0007B69ECC73F71C),
          UINT64_C(0x0000FFC2CCAF6490), UINT64_C(0x00064FDB227E8E46),
          UINT64_C(0x00055085B8768622)},
         {UINT64_C(0x00039AD933E2C137), UINT64_C(0x000171F8E3C0681B),
          UINT64_C(0x0001BCC516668190), UINT64_C(0x00063314F511F47A),
          UINT64_C(0x0002FE194634EA7D)}},
        {{UINT64_C(0x000FE52986682B6C), UINT64_C(0x0007C7793C6CFA0B),
          UINT64_C(0x0002F120DB9FC778), UINT64_C(0x00056B1B5D1B696B),
          UINT64_C(0x0006F7B105C7285D)},
         {UINT64_C(0x000F08F3C79E5FA2), UINT64_C(0x0006714A9E5021DE),
          UINT64_C(0x000556DFCA302233), UINT64_C(0x00036A6272A322E0),
          UINT64_C(0x0007958876DF034E)},
         {UINT64_C(0x0001B28B59F70354), UINT64_C(0x0007AB27CE51E80D),
          UINT64_C(0x0000567BCD43D179), UINT64_C(0x00041EF880C69F63),
          UINT64_C(0x00057CCC9DA97DCC)}},
        {{UINT64_C(0x00006BDC7393E9B9), UINT64_C(0x00056D46935E2D25),
          UINT64_C(0x00040C99AE846E20), UINT64_C(0x000277C2A48B7D6B),
          UINT64_C(0x0006F9657DBA5EC0)},
         {UINT64_C(0x000E84FD78E1468C), UINT64_C(0x0001F647949AC006),
          UINT64_C(0x000373B20611C903), UINT64_C(0x00015E6FAFF6FA2A),
          UINT64_C(0x00053520AB3070E2)},
         {UINT64_C(0x000CC15CC69411F0), UINT64_C(0x000552FB8719E7D5),
          UINT64_C(0x0007C3B5010848F9), UINT64_C(0x0000E8AF5525076D),
          UINT64_C(0x00058459D1B894E4)}},
        {{UINT64_C(0x000B9009E3531F14), UINT64_C(0x0000237005679DB2),
          UINT64_C(0x0007BF223FD5732A), UINT64_C(0x0006429263F091F4),
          UINT64_C(0x0007493705D2ADC2)},
         {UINT64_C(0x000793C60C5EACBA), UINT64_C(0x000332F859C4D8EE),
          UINT64_C(0x0000D090DF892E4A), UINT64_C(0x00041A3040EC7085),
          UINT64_C(0x0001F585E3FC2C2A)},
         {UINT64_C(0x00084D3FD3FF468A), UINT64_C(0x00038C0475145DFA),
          UINT64_C(0x00056070E08A4838), UINT64_C(0x0002AC8E15465366),
          UINT64_C(0x0000F1485199EC2C)}},
        {{UINT64_C(0x000012693C9129E3), UINT64_C(0x00011009F59E7410),
          UINT64_C(0x0005E068432FB821), UINT64_C(0x000509F6A285BC64),
          UINT64_C(0x00064B88E6E8B8A6)},
         {UINT64_C(0x000739F7074FD4A7), UINT64_C(0x00032712493D3F9E),
          UINT64_C(0x0002E3550E33660D), UINT64_C(0x0006EE093485A6A5),
          UINT64_C(0x0004CAE5F8CDBF10)},
         {UINT64_C(0x000BB27495CF7ED3), UINT64_C(0x00001CEB73051EC2),
          UINT64_C(0x000686DC320D7C1D), UINT64_C(0x0007C5AD5EC9F58E),
          UINT64_C(0x0001B98ED417D210)}},
        {{UINT64_C(0x000BCF4C24A4C93E), UINT64_C(0x00002415C387FA3B),
          UINT64_C(0x0004B3C58228DDFC), UINT64_C(0x0002037F66598FCF),
          UINT64_C(0x000793FF104F8F35)},
         {UINT64_C(0x000F1F6A9319DCA8), UINT64_C(0x0007F0E26C6BFDCF),
          UINT64_C(0x00024F06F6521950), UINT64_C(0x0001BF65931AAD98),
          UINT64_C(0x0004DA8B59BE0CB7)},
         {UINT64_C(0x000C49B7018AF82E), UINT64_C(0x0000264DF5E55202),
          UINT64_C(0x00004C1B40891F09), UINT64_C(0x00037D39139B0395),
          UINT64_C(0x000545CEBFF22B6C)}},
        {{UINT64_C(0x0000535E19606923), UINT64_C(0x000389F3CEB25CB9),
          UINT64_C(0x000771A87A5F04FF), UINT64_C(0x000712E80B91B905),
          UINT64_C(0x00001F1EA4402629)},
         {UINT64_C(0x0003A2CFFE51751A), UINT64_C(0x0001780B59BF2F57),
          UINT64_C(0x00033B2134560E7F), UINT64_C(0x0000C2FD87484CD4),
          UINT64_C(0x0000C1E827BF8C54)},
         {UINT64_C(0x000BB159A554E7F7), UINT64_C(0x000679D0F2669B49),
          UINT64_C(0x0007C8AF96EB162D), UINT64_C(0x000739F8391987C4),
          UINT64_C(0x0004FD6BA815924D)}},
        {{UINT64_C(0x000CD58A76375AB1), UINT64_C(0x0000E0B8DB7493B2),
          UINT64_C(0x0001F26DC2178BD2), UINT64_C(0x000267C3B843A318),
          UINT64_C(0x0003B57558845D7B)},
         {UINT64_C(0x00062248E93DA9D4), UINT64_C(0x0001B750D5841F57),
          UINT64_C(0x0005825717C4CDF9), UINT64_C(0x000395BD7231BF89),
          UINT64_C(0x0005BB6B5E2E6AF9)},
         {UINT64_C(0x0009C6B9394024E3), UINT64_C(0x00075C5446BE7237),
          UINT64_C(0x00065E6C801E7BB5), UINT64_C(0x00002DB2F90A55CC),
          UINT64_C(0x00045DBB4D1CCBDE)}},
        {{UINT64_C(0x00053CBFDBE0980A), UINT64_C(0x0001407AA377A7AD),
          UINT64_C(0x0001290C47515626), UINT64_C(0x0006B6C6AB3118E7),
          UINT64_C(0x0002C22D483E81D6)},
         {UINT64_C(0x00012ACDC7AE7440), UINT64_C(0x00069C66640BC7A6),
          UINT64_C(0x00002A4F768809CC), UINT64_C(0x00014168BEC8D796),
          UINT64_C(0x000052716E75E07B)},
         {UINT64_C(0x0001BB2F947F80B6), UINT64_C(0x000073A86D41B9DC),
          UINT64_C(0x0000F191E9DE78B6), UINT64_C(0x00065DF33E390DB9),
          UINT64_C(0x00003308F79176BD)}},
    },
    {
        {{UINT64_C(0x000BA39C423A0F8A), UINT64_C(0x0006FF2C4C49644C),
          UINT64_C(0x000527B0CF9945D1), UINT64_C(0x000462BF78F17E13),
          UINT64_C(0x000072F5DF073B45)},
         {UINT64_C(0x000A52B0C9431B86), UINT64_C(0x00007413199BDB5A),
          UINT64_C(0x0006FF18C34250CB), UINT64_C(0x0005F22C96AC669A),
          UINT64_C(0x00009930CFA11CAD)},
         {UINT64_C(0x00049D54A46B4F00), UINT64_C(0x0001AA620DEF496D),
          UINT64_C(0x0001FF21C12D3280), UINT64_C(0x0006DA8184B69B09),
          UINT64_C(0x000387F6AA984BF6)}},
        {{UINT64_C(0x0001654E2D2F5512), UINT64_C(0x00078F8B53D7341A),
          UINT64_C(0x0000470E5BC40FE6), UINT64_C(0x0006C2A23BF4231D),
          UINT64_C(0x00008AD4AC154BD4)},
         {UINT64_C(0x00025588B0E427F0), UINT64_C(0x00004B7426292145),
          UINT64_C(0x0004EFE16DBDD657), UINT64_C(0x000737056241360D),
          UINT64_C(0x000503BBEF8BB0A2)},
         {UINT64_C(0x000E71495D3F3EE8), UINT64_C(0x0003B55E84166513),
          UINT64_C(0x0005A32066E89C7A), UINT64_C(0x00060FD619143FDC),
          UINT64_C(0x0007AF54E13C9A0B)}},
        {{UINT64_C(0x000F35F2359ED4D4), UINT64_C(0x0006DAD5A1EDD9F7),
          UINT64_C(0x00033E72BB06FA4C), UINT64_C(0x0004CB0BF2699447),
          UINT64_C(0x00078ADC100A9438)},
         {UINT64_C(0x00058677010F1D44), UINT64_C(0x0000EE3DA34D7805),
          UINT64_C(0x0003C19261DBCE0E), UINT64_C(0x0000BE1D2B1915F1),
          UINT64_C(0x0004C27417301A56)},
         {UINT64_C(0x00010519E74F0FD3), UINT64_C(0x000667928B6E2AED),
          UINT64_C(0x000112B7D96F8351), UINT64_C(0x00025AC5C3C73843),
          UINT64_C(0x00064F071A913A63)}},
        {{UINT64_C(0x000134221F75E904), UINT64_C(0x0001E877FC436184),
          UINT64_C(0x0001043119533F1B), UINT64_C(0x00036E12894B6885),
          UINT64_C(0x0001A97DC3A85DD9)},
         {UINT64_C(0x0005B0E5800AD26D), UINT64_C(0x0003C8403B9B7909),
          UINT64_C(0x000578BF28F187B8), UINT64_C(0x0006F76A622B025F),
          UINT64_C(0x00001A581506806A)},
         {UINT64_C(0x000B1B3A779043A2), UINT64_C(0x0002BD2CCEFC7D40),
          UINT64_C(0x0006880561392822), UINT64_C(0x000470820DC7AD05),
          UINT64_C(0x0000A8A6DFE39EFC)}},
        {{UINT64_C(0x0003B4AE7341A857), UINT64_C(0x0006F61AE4525D94),
          UINT64_C(0x00047463C3F47708), UINT64_C(0x00013CE30C0E6CD4),
          UINT64_C(0x0001A4D6B43BC1E8)},
         {UINT64_C(0x000448110CF677D7), UINT64_C(0x0002B26F500047C8),
          UINT64_C(0x0000A9EDEE30B5CA), UINT64_C(0x00074CEAB89A5F26),
          UINT64_C(0x00069DC9E784890E)},
         {UINT64_C(0x0002384FEB025EE8), UINT64_C(0x000760169DF0E4ED),
          UINT64_C(0x000679A94C441675), UINT64_C(0x0006E39A8B9E9323),
          UINT64_C(0x0004F60517E33211)}},
        {{UINT64_C(0x000D1F112D03827C), UINT64_C(0x0003E266C25B8CB9),
          UINT64_C(0x0004DDAE5E9A861A), UINT64_C(0x0003FDDD5A19E3FC),
          UINT64_C(0x000525AC4177127A)},
         {UINT64_C(0x0005E3FBAF1DE590), UINT64_C(0x00006D0AC9D49EBF),
          UINT64_C(0x00071A842BB0750F), UINT64_C(0x00030A965193BEB1),
          UINT64_C(0x00000074F0F13CF1)},
         {UINT64_C(0x000569358B8229BF), UINT64_C(0x0004D8605014C12D),
          UINT64_C(0x00067CF8F3ABB9E2), UINT64_C(0x0001913D15FE7E56),
          UINT64_C(0x0004E005335EB124)}},
        {{UINT64_C(0x000DB4B52128B9C8), UINT64_C(0x000286D98EC1991F),
          UINT64_C(0x00057D6985B266D5), UINT64_C(0x0000D63A8186A6CA),
          UINT64_C(0x0003E458C6D43002)},
         {UINT64_C(0x000FAC1001DCF643), UINT64_C(0x0001A63A60E69551),
          UINT64_C(0x000770074385A527), UINT64_C(0x000716E487ACFCBE),
          UINT64_C(0x0003AFC421F39471)},
         {UINT64_C(0x0001FF85BA0DD581), UINT64_C(0x000185663E421B6B),
          UINT64_C(0x0003D0CC19588A5B), UINT64_C(0x00050A4ED90A0BD8),
          UINT64_C(0x0001ADEA0DFB4B55)}},
        {{UINT64_C(0x000F8CD682890618), UINT64_C(0x0001B4C0FC4BF1B7),
          UINT64_C(0x0004593054904595), UINT64_C(0x00044163E1D05297),
          UINT64_C(0x0004FF913B7848EE)},
         {UINT64_C(0x00044061A1C77BF2), UINT64_C(0x000118C6EAF78CBE),
          UINT64_C(0x000121317990C4B0), UINT64_C(0x00014F94111D4DBF),
          UINT64_C(0x00050FC488F73A1C)},
         {UINT64_C(0x0003F8A423FF3B98), UINT64_C(0x00057AB9DDB49662),
          UINT64_C(0x000307128F5CD0A4), UINT64_C(0x0005C206361126E4),
          UINT64_C(0x00058F1A9B6F58C2)}},
        {{UINT64_C(0x00026D3D15CAFC7E), UINT64_C(0x00011891BC41E2F2),
          UINT64_C(0x0006F04870D432A4), UINT64_C(0x000745C21E77AAA6),
          UINT64_C(0x0003DAC061530272)},
         {UINT64_C(0x0001DAF75081BEA5), UINT64_C(0x00056D5195DD18E2),
          UINT64_C(0x0001DCAC8C45410E), UINT64_C(0x0004D2DA16F980D8),
          UINT64_C(0x00063744829E7170)},
         {UINT64_C(0x000DF1C42FA10272), UINT64_C(0x0000741CA2FAA037),
          UINT64_C(0x0000467C87311FE7), UINT64_C(0x000523429446FA40),
          UINT64_C(0x00068BD1A87367D1)}},
        {{UINT64_C(0x000C458B0171251B), UINT64_C(0x0001FE9DE818F3CF),
          UINT64_C(0x00054A1370707A2C), UINT64_C(0x000062F84AA462F0),
          UINT64_C(0x0007CF5A9BF230F7)},
         {UINT64_C(0x00054404A1098FC2), UINT64_C(0x0002411906266825),
          UINT64_C(0x0000A193956A70E8), UINT64_C(0x0000FE137D04BC1F),
          UINT64_C(0x0004E42D21EFFD81)},
         {UINT64_C(0x000F2EAC10355E39), UINT64_C(0x00054F7E065688EB),
          UINT64_C(0x0001A95E0CA9F2C6), UINT64_C(0x0007E704C61469A2),
          UINT64_C(0x0007B68ADBD82DC6)}},
        {{UINT64_C(0x000899180C1E2363), UINT64_C(0x0007958932146E24),
          UINT64_C(0x00043C3BDBE7E084), UINT64_C(0x00059983B137251C),
          UINT64_C(0x000181505C38246A)},
         {UINT64_C(0x0000FD89855EFABB), UINT64_C(0x0003B63D1B15E2DE),
          UINT64_C(0x000255391D67495A), UINT64_C(0x0004DD2784B95163),
          UINT64_C(0x000512AAAA077953)},
         {UINT64_C(0x000F74E51E9D1E14), UINT64_C(0x0007A0923F8922E5),
          UINT64_C(0x000481DF01D3BA5E), UINT64_C(0x00078147FE69A25E),
          UINT64_C(0x0002677F7C367D95)}},
        {{UINT64_C(0x000C31AE3ACBF754), UINT64_C(0x00015B533DE0199D),
          UINT64_C(0x00037F5398951F25), UINT64_C(0x0007F6D8289614BF),
          UINT64_C(0x0007A746B03A9AD8)},
         {UINT64_C(0x000A5E8B067361B9), UINT64_C(0x000753D2A11E2709),
          UINT64_C(0x00068EB1296DD194), UINT64_C(0x0007F60FCC2F2F13),
          UINT64_C(0x000241ECD0A2E455)},
         {UINT64_C(0x000AB9A10A22A500), UINT64_C(0x0001B6D59F5E169E),
          UINT64_C(0x00031BBC448574D5), UINT64_C(0x0004717C01E2B0BB),
          UINT64_C(0x0003D21E8ACA1878)}},
        {{UINT64_C(0x000499194C8B1C66), UINT64_C(0x00023DD23CF4A5FF),
          UINT64_C(0x000260F22A1F1E67), UINT64_C(0x00046F48D856E7A8),
          UINT64_C(0x00027B4E0522C56B)},
         {UINT64_C(0x000197955EC21F63), UINT64_C(0x0004F7579537C60A),
          UINT64_C(0x0001EED38E27A397), UINT64_C(0x000642709A47DD6D),
          UINT64_C(0x0000CEC4BAE6CE94)},
         {UINT64_C(0x0007E224BD3DE334), UINT64_C(0x0002BC2A4E547CE3),
          UINT64_C(0x0006C9441E441A2F), UINT64_C(0x00066ABBA46DB7C3),
          UINT64_C(0x0007D6D7B2C94D89)}},
        {{UINT64_C(0x00094A1B918036C4), UINT64_C(0x0007000DC73238D4),
          UINT64_C(0x00077504C3F1AB6E), UINT64_C(0x0004E6A5D5C7C6A1),
          UINT64_C(0x0003821648B33D3A)},
         {UINT64_C(0x00081E39CDB93F79), UINT64_C(0x0004EBBC689100FB),
          UINT64_C(0x0001DD37C191E986), UINT64_C(0x000036795A799602),
          UINT64_C(0x000041C1B41711DB)},
         {UINT64_C(0x000A4A21AC6CAF3D), UINT64_C(0x000744CF7594E6F1),
          UINT64_C(0x000030561165E9A9), UINT64_C(0x00074B8AB965B245),
          UINT64_C(0x000244902E385A94)}},
        {{UINT64_C(0x000464BD11AFA1E1), UINT64_C(0x0002E00B44F4A68C),
          UINT64_C(0x0006C58D5152071B), UINT64_C(0x0007CC78F1BEA1EE),
          UINT64_C(0x0003D52810ACC281)},
         {UINT64_C(0x000C04B80D28DBA4), UINT64_C(0x0007C5EAA4A27BFF),
          UINT64_C(0x00066248F5BFC255), UINT64_C(0x0006BDB404771492),
          UINT64_C(0x00019FA7BB5E292E)},
         {UINT64_C(0x0006358792FF5820), UINT64_C(0x0003BB145A4D0D14),
          UINT64_C(0x000647A0D099804C), UINT64_C(0x0001CE6985BBEA5D),
          UINT64_C(0x00071F7ADDB5DEC0)}},
        {{UINT64_C(0x000FF792012E3AD1), UINT64_C(0x000318FC42C34144),
          UINT64_C(0x00018AA9596D5383), UINT64_C(0x0004A1E0677AA264),
          UINT64_C(0x0007C9D08A010323)},
         {UINT64_C(0x000B38DA3FFA9D8B), UINT64_C(0x0007B37A58DB49D5),
          UINT64_C(0x0003CAB96E3D6DD9), UINT64_C(0x0004877EC3C59320),
          UINT64_C(0x0005F79E6AB22005)},
         {UINT64_C(0x000D9227D42E392D), UINT64_C(0x000383E4A65149E3),
          UINT64_C(0x0001FC4C8988D7D7), UINT64_C(0x0000301C333E8D0D),
          UINT64_C(0x000566D13B877137)}},
    },
    {
        {{UINT64_C(0x0002819CD64EC5D5), UINT64_C(0x000718864EB85DEA),
          UINT64_C(0x0000B38F635D18B2), UINT64_C(0x0000FA0D19EC1914),
          UINT64_C(0x0007FC6E0B53A132)},
         {UINT64_C(0x000681A57637B42A), UINT64_C(0x00009516D77453A8),
          UINT64_C(0x00041747D2AFF787), UINT64_C(0x000484C8CAA8775B),
          UINT64_C(0x00069B6EDB5BEB15)},
         {UINT64_C(0x000F673ED008533F), UINT64_C(0x00005983AE2E9902),
          UINT64_C(0x000696120C054601), UINT64_C(0x0002DD10C21D3665),
          UINT64_C(0x0003650B63683F24)}},
        {{UINT64_C(0x0001C85E071E87C9), UINT64_C(0x00038E791D5639BC),
          UINT64_C(0x0007EB7B3E0A3BD5), UINT64_C(0x00067F05BA2C2992),
          UINT64_C(0x0005A217D19B8E87)},
         {UINT64_C(0x0009CC57757E7DC9), UINT64_C(0x0004C152B026BBF3),
          UINT64_C(0x0007CE941D2BB495), UINT64_C(0x00022801029CDB1F),
          UINT64_C(0x00015E56AA9988B8)},
         {UINT64_C(0x0001F20BB815BF7F), UINT64_C(0x00045BBFD494947D),
          UINT64_C(0x000752E00CADD615), UINT64_C(0x00030A1ADE235D5B),
          UINT64_C(0x00004F475EAAC468)}},
        {{UINT64_C(0x0005DEC978DC22CC), UINT64_C(0x00069E99CC23AF7D),
          UINT64_C(0x0001A491C445CF0B), UINT64_C(0x0001A2B80570EAF4),
          UINT64_C(0x000519998F23DA17)},
         {UINT64_C(0x00033F2D4F6DFC80), UINT64_C(0x0003473C4B91929A),
          UINT64_C(0x000657675912C778), UINT64_C(0x00027483C8C74BA9),
          UINT64_C(0x00045AEDD79F572E)},
         {UINT64_C(0x0005B55CF68CF232), UINT64_C(0x0005978D5F58C198),
          UINT64_C(0x000463464AA8D74F), UINT64_C(0x00000768A84F1143),
          UINT64_C(0x0004D85E72D498F7)}},
        {{UINT64_C(0x00065396EB5E1B7C), UINT64_C(0x0003AD9BC86D4A9D),
          UINT64_C(0x0007D80AED5F83A2), UINT64_C(0x0007BF7E181211DD),
          UINT64_C(0x00053259A33A1C38)},
         {UINT64_C(0x0004ED7967092AA9), UINT64_C(0x00037E220CEFD1A7),
          UINT64_C(0x0007D56552E4BE39), UINT64_C(0x000467E0E1384212),
          UINT64_C(0x0007CB81AF42B8E6)},
         {UINT64_C(0x000F60010D427117), UINT64_C(0x0003FE077DD17DAC),
          UINT64_C(0x0003D70C978E0E1D), UINT64_C(0x0004EC475F514EFA),
          UINT64_C(0x000678A033281D8A)}},
        {{UINT64_C(0x0001DDD15D89D0DA), UINT64_C(0x0005AE12E0DC3F81),
          UINT64_C(0x00021725313E6CD1), UINT64_C(0x00058E25DD51CD68),
          UINT64_C(0x00051CC1640E208C)},
         {UINT64_C(0x000A5BFACD788203), UINT64_C(0x00076782156DA54A),
          UINT64_C(0x0006546FAACEBFB5), UINT64_C(0x0000751697EBD387),
          UINT64_C(0x000403792F66F908)},
         {UINT64_C(0x0001ED1BA791D84F), UINT64_C(0x0007C0C66505B59A),
          UINT64_C(0x0003039E8770455C), UINT64_C(0x000176EBE864CCC6),
          UINT64_C(0x0000A9D7490BB5E2)}},
        {{UINT64_C(0x000ADBB841243625), UINT64_C(0x00070EF045AE92E4),
          UINT64_C(0x0003ED707AC6DC1C), UINT64_C(0x0002EC569D895E88),
          UINT64_C(0x0007A1F82B8A9F9B)},
         {UINT64_C(0x000480ED4CC26515), UINT64_C(0x0007142FB964F1D8),
          UINT64_C(0x0002D589D76131D9), UINT64_C(0x0002041F5E307B5D),
          UINT64_C(0x0004DC0C8DC698E2)},
         {UINT64_C(0x000639BA125B4EB0), UINT64_C(0x0006735AE5885BC9),
          UINT64_C(0x000402130AA29EFE), UINT64_C(0x0006483ECF5F928A),
          UINT64_C(0x0005C0C2A7450215)}},
        {{UINT64_C(0x00011E1BA90F4D05), UINT64_C(0x0003C4ACA739B18D),
          UINT64_C(0x0006DA6C4D05BB4B), UINT64_C(0x0006C9D11A68C07A),
          UINT64_C(0x0007E4FF60853DCC)},
         {UINT64_C(0x000FC3B1EAB98C72), UINT64_C(0x000194A565C0D2C0),
          UINT64_C(0x0006774E78078878), UINT64_C(0x0002845C8FBE34B8),
          UINT64_C(0x0003FA8FFB470A30)},
         {UINT64_C(0x000DF02100A29B8E), UINT64_C(0x0001EDB288FCEFEF),
          UINT64_C(0x0003A1F05EE527CB), UINT64_C(0x0000800DA11B67CF),
          UINT64_C(0x0000F28747F25BA7)}},
        {{UINT64_C(0x000AB1FE778D5AC8), UINT64_C(0x0007AC5F7720F73D),
          UINT64_C(0x0001F29E35DE087B), UINT64_C(0x0006594AE6F4B147),
          UINT64_C(0x0003BD0E462820EB)},
         {UINT64_C(0x00046E4EE5708B31), UINT64_C(0x0005F6D4A65795AD),
          UINT64_C(0x00065A6A14FD4DBD), UINT64_C(0x0005175D21ECC1C7),
          UINT64_C(0x0007FE00455D7273)},
         {UINT64_C(0x0007B6F8CC74B2E7), UINT64_C(0x000741AB0632D05A),
          UINT64_C(0x00049D46261751F0), UINT64_C(0x0006736BD5AEA112),
          UINT64_C(0x00069AD164B202C5)}},
        {{UINT64_C(0x00012BDCF8236C6C), UINT64_C(0x000367FF9D0F9B9A),
          UINT64_C(0x0003F319B67073D2), UINT64_C(0x000760E8B33BDBA0),
          UINT64_C(0x00056660C3661195)},
         {UINT64_C(0x0006A7EA308B1685), UINT64_C(0x0006C261A0B19E51),
          UINT64_C(0x0004E083123A5FB3), UINT64_C(0x00047A0C38DD3BA2),
          UINT64_C(0x00053C6D2A152664)},
         {UINT64_C(0x000FAF64FB917431), UINT64_C(0x000565756E7FAAF4),
          UINT64_C(0x0003D721A24A3704), UINT64_C(0x00013317369CBD42),
          UINT64_C(0x00016612511B5AA0)}},
        {{UINT64_C(0x000E652B12074C2E), UINT64_C(0x00059D0BAF050179),
          UINT64_C(0x00003B5736856800), UINT64_C(0x000647959933E44E),
          UINT64_C(0x0003D56153518775)},
         {UINT64_C(0x0000F380F6F1A960), UINT64_C(0x0001ACBBE16F804F),
          UINT64_C(0x0002997050169D92), UINT64_C(0x0006923F3FB9A04C),
          UINT64_C(0x000760D8364CDE1D)},
         {UINT64_C(0x00087864C2C7818E), UINT64_C(0x00065BCE2C21225E),
          UINT64_C(0x0000829D71C17447), UINT64_C(0x000624DD657FE09E),
          UINT64_C(0x000078584F6FEFD9)}},
        {{UINT64_C(0x0006D584A0FE7A46), UINT64_C(0x00069D33D6A2E6E8),
          UINT64_C(0x00067E621C9A0CE7), UINT64_C(0x00077A830F59FBFF),
          UINT64_C(0x00014C1BD946B545)},
         {UINT64_C(0x0001B2A1355A4052), UINT64_C(0x000675CBE17382B6),
          UINT64_C(0x000641AF5CC81253), UINT64_C(0x0005EB097D44D21F),
          UINT64_C(0x00009FC2AAAE02CC)},
         {UINT64_C(0x000D414D936E58DD), UINT64_C(0x0000B296AC88EB92),
          UINT64_C(0x00076AB84DD6056C), UINT64_C(0x000685E1AB03C5DC),
          UINT64_C(0x00028348D45F6358)}},
        {{UINT64_C(0x00016CC86D940FFC), UINT64_C(0x0006FA98A7F59E71),
          UINT64_C(0x00029CA45E235967), UINT64_C(0x0001DDB48758B49F),
          UINT64_C(0x00061464300AF54F)},
         {UINT64_C(0x00043C3AC9312B0D), UINT64_C(0x000064FC92BC9377),
          UINT64_C(0x000239F95BD3C366), UINT64_C(0x0000529C3D8CF33A),
          UINT64_C(0x000181E4547EBD24)},
         {UINT64_C(0x000E16213D8C28C0), UINT64_C(0x000231D13CE48188),
          UINT64_C(0x0007F4B9BEA03675), UINT64_C(0x0004042E3AC3D778),
          UINT64_C(0x0001EAA475E87AA7)}},
        {{UINT64_C(0x0001DAA732854120), UINT64_C(0x0002D8C6031F046B),
          UINT64_C(0x00048CA4CD8BA89E), UINT64_C(0x00006937503A6724),
          UINT64_C(0x00061B37A983648E)},
         {UINT64_C(0x00013AC756B13B17), UINT64_C(0x0007F99EB1FE63B2),
          UINT64_C(0x00012EF62AA29F91), UINT64_C(0x000275A63AC0B82C),
          UINT64_C(0x00041F85AC1748DC)},
         {UINT64_C(0x0003C3A345E37216), UINT64_C(0x0000BC95899511A0),
          UINT64_C(0x00067984E11988E2), UINT64_C(0x0003790C6A6BD91C),
          UINT64_C(0x0004AE8857A370C0)}},
        {{UINT64_C(0x00059767D1DC7159), UINT64_C(0x00059021E287D705),
          UINT64_C(0x00059EECEC3BE7D9), UINT64_C(0x0001EAA18D4EADD0),
          UINT64_C(0x00065A975E23F03E)},
         {UINT64_C(0x000A95AFCF439414), UINT64_C(0x00015B75C723F702),
          UINT64_C(0x0000BF98929522FB), UINT64_C(0x0003085D78E648C0),
          UINT64_C(0x00037D7D62CA4475)},
         {UINT64_C(0x0004A0DA8919F5F5), UINT64_C(0x000367D06413644F),
          UINT64_C(0x000354956C041EA1), UINT64_C(0x00010755634E2C5D),
          UINT64_C(0x000170791147DD80)}},
        {{UINT64_C(0x000827F7944FE6CC), UINT64_C(0x00001C751F20EF72),
          UINT64_C(0x00035F7CA3D66415), UINT64_C(0x0005210C82C852BA),
          UINT64_C(0x00004C02A5485650)},
         {UINT64_C(0x0003B5BDD64B5C0A), UINT64_C(0x0004689EBDCCFF34),
          UINT64_C(0x00070E43EECF0D36), UINT64_C(0x0006DE61D82DCC84),
          UINT64_C(0x00012E98D82A7298)},
         {UINT64_C(0x0001E9E5518D471A), UINT64_C(0x0007A1BB46DE49D9),
          UINT64_C(0x00035B2927B223D1), UINT64_C(0x0001CCAE464874F4),
          UINT64_C(0x00039DDDEF590E1E)}},
        {{UINT64_C(0x00024A7A1C775995), UINT64_C(0x0007157DD78B8376),
          UINT64_C(0x000135892F3211E4), UINT64_C(0x00068F7235FB1FF7),
          UINT64_C(0x0007B084D8E8F5BA)},
         {UINT64_C(0x00047354D60A9B5C), UINT64_C(0x0005528D484B558C),
          UINT64_C(0x0005EA409EB39DEF), UINT64_C(0x0001301D88A26EAB),
          UINT64_C(0x0003E8EEEA1E9E16)},
         {UINT64_C(0x0007E18F66EB4040), UINT64_C(0x0003D687AAC18DF1),
          UINT64_C(0x000043407B1B01C5), UINT64_C(0x00076026A3612E1D),
          UINT64_C(0x00036AC22C1DA7F0)}},
    },
    {
        {{UINT64_C(0x000A1DB3FDA77DAC), UINT64_C(0x0004D9593DB5C4FA),
          UINT64_C(0x0002D0C711ADB1E7), UINT64_C(0x0004FA1FD3056EC4),
          UINT64_C(0x0001AEA89A06C580)},
         {UINT64_C(0x0000B6E56502BC8A), UINT64_C(0x00030FB5DA75A109),
          UINT64_C(0x00061CCC955CD549), UINT64_C(0x00072ED7E235BE62),
          UINT64_C(0x0004FFC550D2C672)},
         {UINT64_C(0x0001401B07FD2FC2), UINT64_C(0x0001363A97814A08),
          UINT64_C(0x00040A250BB0D833), UINT64_C(0x0003FDC7EADB00FF),
          UINT64_C(0x0006D0C5F0015D6E)}},
        {{UINT64_C(0x0006C4A3C3DD75D0), UINT64_C(0x00035FB0DFF6D283),
          UINT64_C(0x00008EC03452D8B5), UINT64_C(0x0000660AA38D2E81),
          UINT64_C(0x00070999FB1B3622)},
         {UINT64_C(0x00076C0468E5E93B), UINT64_C(0x000691D7E3FC4A00),
          UINT64_C(0x00049CB4185CDAF8), UINT64_C(0x00016C0C829B0E8D),
          UINT64_C(0x0006AA160D0C7282)},
         {UINT64_C(0x0005FA2B7F6BD2E2), UINT64_C(0x000158DA48E6F8A3),
          UINT64_C(0x000604218B1883CE), UINT64_C(0x0000CFFE74536FDE),
          UINT64_C(0x00005D042AB776AA)}},
        {{UINT64_C(0x00027DD79FD8B383), UINT64_C(0x0004E3C07F0FC4F0),
          UINT64_C(0x00071E194BBE44D0), UINT64_C(0x000516439A5B3612),
          UINT64_C(0x0003F479BE23976A)},
         {UINT64_C(0x000D07783284BAF3), UINT64_C(0x00044583E7D6E11C),
          UINT64_C(0x0001D132C130CABE), UINT64_C(0x000625DDBFB712E6),
          UINT64_C(0x00070A1B85622B32)},
         {UINT64_C(0x00034C89E9034A6C), UINT64_C(0x00078F18070D16D9),
          UINT64_C(0x0005C48940FE1968), UINT64_C(0x0007B463D52BFF8C),
          UINT64_C(0x0004F9CB24AA3CA3)}},
        {{UINT64_C(0x0000210FBD862AF4), UINT64_C(0x00042D00326CF364),
          UINT64_C(0x0005909CDA828553), UINT64_C(0x000765038B375968),
          UINT64_C(0x00061244E59E8942)},
         {UINT64_C(0x000316B2C8856415), UINT64_C(0x0002A4748F779AB3),
          UINT64_C(0x000239C5E875089A), UINT64_C(0x00036401DD98269B),
          UINT64_C(0x00011DDAE2A7E2F2)},
         {UINT64_C(0x000E9134B0007750), UINT64_C(0x0005EF4908EA5F8B),
          UINT64_C(0x00079BE217CC5E96), UINT64_C(0x0000599ECA99A489),
          UINT64_C(0x0004C47BBF939482)}},
        {{UINT64_C(0x00003A337C362E13), UINT64_C(0x0007780615B15DD1),
          UINT64_C(0x000773A3946A0160), UINT64_C(0x000700FAAF105D15),
          UINT64_C(0x0001A74BE2B6494D)},
         {UINT64_C(0x0005CA35FE3DB4CB), UINT64_C(0x00071DF8C08DEDDD),
          UINT64_C(0x0005F7E4ACF08CA7), UINT64_C(0x00036F543C236FDD),
          UINT64_C(0x00069ABF13BDA30B)},
         {UINT64_C(0x000D0BDB4C5FE93D), UINT64_C(0x0002B22A4FA5285B),
          UINT64_C(0x0006FA80F65CB79E), UINT64_C(0x000379BFA1F4BF5E),
          UINT64_C(0x00046594A6F4C0D4)}},
        {{UINT64_C(0x0007305A30FD1F79), UINT64_C(0x0001C6FFD9905695),
          UINT64_C(0x0004645BE794D673), UINT64_C(0x000342101D707890),
          UINT64_C(0x0001BB588F62A0A3)},
         {UINT64_C(0x000105A272D17F2A), UINT64_C(0x0007B3D6FA991E49),
          UINT64_C(0x000227280611E9A7), UINT64_C(0x0001A93AAA75915F),
          UINT64_C(0x00023FA441B3482E)},
         {UINT64_C(0x00049C1150A4394C), UINT64_C(0x00040C4F9BAC625E),
          UINT64_C(0x000019D525AD587E), UINT64_C(0x0004FF7128EF9688),
          UINT64_C(0x0004FD444389AB05)}},
        {{UINT64_C(0x000238E42AD5EEEC), UINT64_C(0x0005DE60189ED21B),
          UINT64_C(0x0001C3ED84BF7F92), UINT64_C(0x00051D4CE9A6E138),
          UINT64_C(0x0004D7509030CE74)},
         {UINT64_C(0x00014535B829610F), UINT64_C(0x0006763BE3F420BB),
          UINT64_C(0x000224420EC61552), UINT64_C(0x0001D944B492451E),
          UINT64_C(0x00043B7C34AD7E8B)},
         {UINT64_C(0x0008748FB4B47D51), UINT64_C(0x0005ABB8E9B2C218),
          UINT64_C(0x0001F774AE8AEF2A), UINT64_C(0x0004E251530C1540),
          UINT64_C(0x0002C17E01A2CD3E)}},
        {{UINT64_C(0x000A5AEDEE07B0B0), UINT64_C(0x0005F89D3947B76A),
          UINT64_C(0x0002BB62F90E7DE8), UINT64_C(0x000654A1AF24BC3B),
          UINT64_C(0x0003546F95E0EE6F)},
         {UINT64_C(0x00085679601B8BC5), UINT64_C(0x0004BB095D71C7D0),
          UINT64_C(0x00053ECC1C751399), UINT64_C(0x00041FE3795CBDE2),
          UINT64_C(0x0006EA98F1D1D81D)},
         {UINT64_C(0x00064ED26F676ADD), UINT64_C(0x0005A7A5BEE185B3),
          UINT64_C(0x000130CC8FEC6AE2), UINT64_C(0x000629FDE7C00F10),
          UINT64_C(0x000585D5A1CBFA57)}},
        {{UINT64_C(0x000AE0784632D10F), UINT64_C(0x00027BFA28563B52),
          UINT64_C(0x0004DECE15823B3A), UINT64_C(0x00010DA131FF58A7),
          UINT64_C(0x00048DEFFB0FE190)},
         {UINT64_C(0x000C811EC58F82EA), UINT64_C(0x000326DA9E04E555),
          UINT64_C(0x00071F177799421B), UINT64_C(0x0003E3A474D3E8B8),
          UINT64_C(0x0003D5D6D4DE3CEF)},
         {UINT64_C(0x000D9FFAF07322DD), UINT64_C(0x000628E14EB89510),
          UINT64_C(0x00075D001D2380EC), UINT64_C(0x00050D7E5EF7D399),
          UINT64_C(0x00079765D0A00A45)}},
        {{UINT64_C(0x00090F5A32267E76), UINT64_C(0x00019A1B8E57CC65),
          UINT64_C(0x00048B29601AC029), UINT64_C(0x00069FA23DE54FFB),
          UINT64_C(0x00002FCBE3187F20)},
         {UINT64_C(0x000125C2704C278B), UINT64_C(0x00028493D1EF2120),
          UINT64_C(0x00054F9AD9FF374B), UINT64_C(0x0006985537375711),
          UINT64_C(0x00007534EB65121D)},
         {UINT64_C(0x0004BD9A12979647), UINT64_C(0x000788B5BC284E24),
          UINT64_C(0x0004CBD296C6FF6B), UINT64_C(0x0007FA0CD5EE5D70),
          UINT64_C(0x0002BC98A33DCE4E)}},
        {{UINT64_C(0x000753D51E36094F), UINT64_C(0x00011309101DCED4),
          UINT64_C(0x0000279505A1BAA2), UINT64_C(0x0006AEDE7A85AAE1),
          UINT64_C(0x000037AE84B6FE0A)},
         {UINT64_C(0x0007739A4BFF6011), UINT64_C(0x000710D32F9567E8),
          UINT64_C(0x00040FE60EC00E34), UINT64_C(0x0000E77282D60FC2),
          UINT64_C(0x00012AF7C9905886)},
         {UINT64_C(0x00010065259DAB06), UINT64_C(0x00002C1EC736FA56),
          UINT64_C(0x00041E7AC35292DC), UINT64_C(0x0000DDF0A6E3C65E),
          UINT64_C(0x00060CA6ABEF89FE)}},
        {{UINT64_C(0x000C71B130D21942), UINT64_C(0x000650CBCD04857A),
          UINT64_C(0x00003A21705F79DE), UINT64_C(0x000441B7586ABB4B),
          UINT64_C(0x00044FFAB824B35E)},
         {UINT64_C(0x0004FC14956E331F), UINT64_C(0x0002DF101A872454),
          UINT64_C(0x0001ACAC797EC78F), UINT64_C(0x0006FBE58D90CBB2),
          UINT64_C(0x0002083D4F2ED3D2)},
         {UINT64_C(0x0002485E01980997), UINT64_C(0x000679010A4C7EE8),
          UINT64_C(0x00057361E75E0DF0), UINT64_C(0x0000548EBB7B0F63),
          UINT64_C(0x0000ADFA873F0ADC)}},
        {{UINT64_C(0x000CEBAD25601C16), UINT64_C(0x00040CF38C58593E),
          UINT64_C(0x000548AC165085FC), UINT64_C(0x00000EC8D84A3956),
          UINT64_C(0x0006990D99B70E27)},
         {UINT64_C(0x0002D6F54C35029C), UINT64_C(0x000194EECC0B8D55),
          UINT64_C(0x000599D2C75B688B), UINT64_C(0x000646453AC905E4),
          UINT64_C(0x0003DAEFFC1F7F77)},
         {UINT64_C(0x0002FB3C155DFF91), UINT64_C(0x000666676455E39E),
          UINT64_C(0x000094B59579A3D7), UINT64_C(0x000131163E64B240),
          UINT64_C(0x0002BE896BAC1C14)}},
        {{UINT64_C(0x0005372C822B631C), UINT64_C(0x0002C792A5D553F9),
          UINT64_C(0x00079A45AF14B5DB), UINT64_C(0x0005728AC606211C),
          UINT64_C(0x00059EF170252365)},
         {UINT64_C(0x0000B216D3837D4E), UINT64_C(0x00049608E9DA7038),
          UINT64_C(0x00071C88398BEB38), UINT64_C(0x0001D39322C696DB),
          UINT64_C(0x0003270FEE25BE63)},
         {UINT64_C(0x000F312227C8BD36), UINT64_C(0x0006EA625A52C28E),
          UINT64_C(0x00022DD277705C08), UINT64_C(0x0002D9A720B669E6),
          UINT64_C(0x00075102EDD2BA08)}},
        {{UINT64_C(0x000CA132F90FE15B), UINT64_C(0x0007F9D119339816),
          UINT64_C(0x0006E56945E2B2A2), UINT64_C(0x0004514FE66AAADD),
          UINT64_C(0x00017AF1BC738FB9)},
         {UINT64_C(0x0004346FAA16001E), UINT64_C(0x0001C1A89BB2AE60),
          UINT64_C(0x0001399CEC955395), UINT64_C(0x0003C0BBC0A6E99B),
          UINT64_C(0x00064D29AE483CCC)},
         {UINT64_C(0x00040ACC3E0904B8), UINT64_C(0x00036DC23E8EE05E),
          UINT64_C(0x00066D427ADA3210), UINT64_C(0x00011F6E098F35F7),
          UINT64_C(0x00039151730A7BBA)}},
        {{UINT64_C(0x00091ADA9145C93C), UINT64_C(0x00024194BA72DF78),
          UINT64_C(0x0003678D46D1C849), UINT64_C(0x000389372773D592),
          UINT64_C(0x00018C36FC253B2C)},
         {UINT64_C(0x0005F6DDFD39F849), UINT64_C(0x0002633241A26C74),
          UINT64_C(0x000474A8B991D2BE), UINT64_C(0x0002CE50E2C4FE43),
          UINT64_C(0x000293E1C206D813)},
         {UINT64_C(0x000F47D948A0883B), UINT64_C(0x000489F014B14B93),
          UINT64_C(0x0006D2AC8A2E3258), UINT64_C(0x0000CE310A0B2212),
          UINT64_C(0x0001253D16B5D0A2)}},
    },
    {
        {{UINT64_C(0x0001FC6BA4B27045), UINT64_C(0x00038F66C4585DD0),
          UINT64_C(0x00044510A543BBBA), UINT64_C(0x0001FFA25BB30E5E),
          UINT64_C(0x0002D3C1BD993CDE)},
         {UINT64_C(0x000A2BB5F0A56D1F), UINT64_C(0x00026C278F22FDAC),
          UINT64_C(0x0000972239E1602A), UINT64_C(0x0006D636075CCB7A),
          UINT64_C(0x000255FEE96DB6F1)},
         {UINT64_C(0x000D53D8C21D2AC3), UINT64_C(0x00073D8BC6569D5D),
          UINT64_C(0x000611171F31EC1E), UINT64_C(0x0007BF39C3FB085A),
          UINT64_C(0x00078CDD196C484E)}},
        {{UINT64_C(0x0007AA3E7172E704), UINT64_C(0x00041E6CDC54B308),
          UINT64_C(0x0000B05762F3EFE5), UINT64_C(0x000444BD48FEF0C8),
          UINT64_C(0x0004428AB2D08617)},
         {UINT64_C(0x000BDD2F9B4370E5), UINT64_C(0x0002F2670834C4F7),
          UINT64_C(0x0003BB1958A52A76), UINT64_C(0x00047CD537305248),
          UINT64_C(0x0001EFD0574CCBFF)},
         {UINT64_C(0x000AEB5EF88DCA62), UINT64_C(0x000117EAA2E61A6B),
          UINT64_C(0x0004FCBB9750C8D0), UINT64_C(0x000495792CD27339),
          UINT64_C(0x0001B23C157916F6)}},
        {{UINT64_C(0x0007891B674E71F0), UINT64_C(0x0007C4F6FCAD4468),
          UINT64_C(0x000476D6C5509A66), UINT64_C(0x0005F5A8B9CC77CD),
          UINT64_C(0x0004199343D1FC80)},
         {UINT64_C(0x0003F18F3D8F0055), UINT64_C(0x000752C7D8BA3224),
          UINT64_C(0x000436F715E78E8E), UINT64_C(0x0001036795BF501D),
          UINT64_C(0x000236B8319E54B6)},
         {UINT64_C(0x0002E41C100EDD83), UINT64_C(0x00058577EB7BBD8B),
          UINT64_C(0x0001F51303AAC160), UINT64_C(0x0005DC3CA305853E),
          UINT64_C(0x00072B9415F81A28)}},
        {{UINT64_C(0x0005265E75BE3284), UINT64_C(0x0007167FAA26A5DF),
          UINT64_C(0x000123D1A2FDB52D), UINT64_C(0x00061BBE43A46D84),
          UINT64_C(0x000261B8D88701BD)},
         {UINT64_C(0x000C87F87048B464), UINT64_C(0x00053BAEC34F322B),
          UINT64_C(0x0004DC58AFE19A7D), UINT64_C(0x000090CE2820F2C2),
          UINT64_C(0x0006C48AC43ECD33)},
         {UINT64_C(0x000B402CCF1F1A30), UINT64_C(0x000429DB8F417641),
          UINT64_C(0x000249FD2DC1A2A4), UINT64_C(0x00049F1F51516171),
          UINT64_C(0x00065C94E325A506)}},
        {{UINT64_C(0x0005D0EAFD053602), UINT64_C(0x0000B2EB6445EFF1),
          UINT64_C(0x0003EBD9F65805D5), UINT64_C(0x0003CA4B8EBF6A36),
          UINT64_C(0x00078BFD685BE998)},
         {UINT64_C(0x000F64B891DDF860), UINT64_C(0x000529576B60556C),
          UINT64_C(0x0001B37FD3E24669), UINT64_C(0x0000033818EEF5C8),
          UINT64_C(0x0002B11EBF7E5937)},
         {UINT64_C(0x0003152B742C702B), UINT64_C(0x0006632B1ECD6036),
          UINT64_C(0x0007923AFB88728F), UINT64_C(0x0006244DA1C3448B),
          UINT64_C(0x0000AD2FB0196EC3)}},
        {{UINT64_C(0x000AD563EB3B07A3), UINT64_C(0x0001213A17B47AC3),
          UINT64_C(0x00053EC21980E6FC), UINT64_C(0x0002C4ACC165507F),
          UINT64_C(0x0007A622F28DA7BE)},
         {UINT64_C(0x000C8F45E5659E8D), UINT64_C(0x0002A78FCB23CE95),
          UINT64_C(0x00024430FD4597D0), UINT64_C(0x000347620F5EDF93),
          UINT64_C(0x0003DC647EA1BA77)},
         {UINT64_C(0x0007F8F8703A7DCA), UINT64_C(0x000795C87DAACF66),
          UINT64_C(0x00007534BD87B9EE), UINT64_C(0x0005099B2C521F78),
          UINT64_C(0x000273750C74E81D)}},
        {{UINT64_C(0x00024EDC3D8ABF8E), UINT64_C(0x000300DFA97BDDE9),
          UINT64_C(0x000446C6F830946C), UINT64_C(0x00068F06F4ACDD60),
          UINT64_C(0x00018F286BEDB687)},
         {UINT64_C(0x0003287C7040CAF8), UINT64_C(0x0005A30953D9648B),
          UINT64_C(0x0004C0CED1BF2F16), UINT64_C(0x0005022BA173833A),
          UINT64_C(0x000471670E5DD4C8)},
         {UINT64_C(0x000575C7D920A1FA), UINT64_C(0x000028011ACAA137),
          UINT64_C(0x000267F433A9BB5D), UINT64_C(0x0000C0E4B91042B4),
          UINT64_C(0x0001740F314FE330)}},
        {{UINT64_C(0x0009146BFD90884A), UINT64_C(0x00024724AE1312FD),
          UINT64_C(0x0000CC37AB3544BD), UINT64_C(0x0002E4F30E1D877C),
          UINT64_C(0x00078F98059C7CDB)},
         {UINT64_C(0x00080EF557A2B367), UINT64_C(0x00034596D52275E4),
          UINT64_C(0x0003D07B43BC2723), UINT64_C(0x000485142DA02EA6),
          UINT64_C(0x00052ED77068B052)},
         {UINT64_C(0x00050C37F84E0B1E), UINT64_C(0x0007F90DAF676333),
          UINT64_C(0x000014A034E40FE3), UINT64_C(0x0001A1CCC5B06FA4),
          UINT64_C(0x0002B57B6AAF5AFB)}},
        {{UINT64_C(0x0005A1EC835F93D9), UINT64_C(0x00027C73660ABD19),
          UINT64_C(0x00051ED4ACB98418), UINT64_C(0x0005934D9A8C4481),
          UINT64_C(0x000056DCE5E599B7)},
         {UINT64_C(0x000D82F2893E91E9), UINT64_C(0x00007DB2ED2F5FC8),
          UINT64_C(0x000470BFB2A14A2D), UINT64_C(0x000243357800B12B),
          UINT64_C(0x0001586B880D973F)},
         {UINT64_C(0x0002894A1AEE30F0), UINT64_C(0x00000A56EEFC3625),
          UINT64_C(0x000328B708D399DB), UINT64_C(0x0001E0245E856F01),
          UINT64_C(0x000331A120A8FA38)}},
        {{UINT64_C(0x0000B01B89AE8C48), UINT64_C(0x000160C2BE55859D),
          UINT64_C(0x00032ED748BC62AF), UINT64_C(0x0003A96EF5B049CB),
          UINT64_C(0x0007D5C6C7C53881)},
         {UINT64_C(0x000F575F2E610488), UINT64_C(0x00050DD2D252D63F),
          UINT64_C(0x00032900A6CAB78A), UINT64_C(0x00052436AFB000AE),
          UINT64_C(0x0001058C522E70B4)},
         {UINT64_C(0x000C32E693EA545B), UINT64_C(0x000363FF953257C4),
          UINT64_C(0x0006BB224E619CF6), UINT64_C(0x0004725D9B6CC315),
          UINT64_C(0x00028D022032739E)}},
        {{UINT64_C(0x000130FFBDDA7B5B), UINT64_C(0x00008B75F47AA474),
          UINT64_C(0x000704583AA71B9F), UINT64_C(0x000003F123BF1718),
          UINT64_C(0x0000C86888DA8826)},
         {UINT64_C(0x0001E99131EE0CC8), UINT64_C(0x00015BD0E81C213F),
          UINT64_C(0x00060DB057C2D8C7), UINT64_C(0x0003931F8CDBD796),
          UINT64_C(0x0000B711D1643ABA)},
         {UINT64_C(0x000CAE2E3BD0339C), UINT64_C(0x000535FE10F2B9B9),
          UINT64_C(0x0003BAC64D32897B), UINT64_C(0x0002E04FBE091647),
          UINT64_C(0x00069D6BFB6020F3)}},
        {{UINT64_C(0x0003BF20FBF44C75), UINT64_C(0x0003DFDF8AA48E71),
          UINT64_C(0x000755E830F9348A), UINT64_C(0x0004DCF2FBC753A0),
          UINT64_C(0x000563C950DED5E7)},
         {UINT64_C(0x00095173064904B2), UINT64_C(0x0001AD16621A4F26),
          UINT64_C(0x0000190EBCA98F19), UINT64_C(0x00025C88E57E353D),
          UINT64_C(0x0007235C78DDE09A)},
         {UINT64_C(0x000AB2477B99C193), UINT64_C(0x000787D92E572642),
          UINT64_C(0x000254AE25CB36B6), UINT64_C(0x00014FF7F61A68BA),
          UINT64_C(0x0002AE23C86DA540)}},
        {{UINT64_C(0x000AA69FC65B55EE), UINT64_C(0x000602BFEB958D5E),
          UINT64_C(0x00041B4D60AAC3E7), UINT64_C(0x0006E4587652A12A),
          UINT64_C(0x000538D39DB42EA9)},
         {UINT64_C(0x000A78E9FAE803AA), UINT64_C(0x00050F1F50C82CCF),
          UINT64_C(0x0000690C7B716417), UINT64_C(0x00009E9B33D5B1F8),
          UINT64_C(0x00046AD0D9B59A8E)},
         {UINT64_C(0x000E0FB21CA97546), UINT64_C(0x000796398B48AA4B),
          UINT64_C(0x0005BFE2E571682E), UINT64_C(0x00079B204C96268F),
          UINT64_C(0x00036514FC4F006A)}},
        {{UINT64_C(0x000AB6ADDA89E077), UINT64_C(0x000456219E30B23F),
          UINT64_C(0x00073DF2B5B296A7), UINT64_C(0x0007416CF096B608),
          UINT64_C(0x000506FAF22F148F)},
         {UINT64_C(0x0004D01231B41F08), UINT64_C(0x00004E0DE454C9D9),
          UINT64_C(0x0001C359EA53295F), UINT64_C(0x0005C16FFE73D620),
          UINT64_C(0x0001FD9A40888D64)},
         {UINT64_C(0x00082DD110EAA0DA), UINT64_C(0x0002885D3B8FB45F),
          UINT64_C(0x0007AAD0C23075A0), UINT64_C(0x000013BF01DF39A9),
          UINT64_C(0x000025FFC049C3BB)}},
        {{UINT64_C(0x000188B1DC7CFBB3), UINT64_C(0x00073B99A1AD10BA),
          UINT64_C(0x000799A4C5F58A95), UINT64_C(0x0005036A5F90050D),
          UINT64_C(0x0007A61558C47079)},
         {UINT64_C(0x0008E5B50FC0616E), UINT64_C(0x0006BE2B98B6BAB1),
          UINT64_C(0x000214A72D8A4D1E), UINT64_C(0x000761E21C815E1A),
          UINT64_C(0x0003D750B6148769)},
         {UINT64_C(0x000A9F7E8D06E7C1), UINT64_C(0x000028F98763EEF5),
          UINT64_C(0x00079EA1FD1DE978), UINT64_C(0x00021613CA3BB40B),
          UINT64_C(0x00070E4362C5401C)}},
        {{UINT64_C(0x0009D08B1089B486), UINT64_C(0x000165F2FBD68266),
          UINT64_C(0x00035E25AA132009), UINT64_C(0x00044F5F944F1D77),
          UINT64_C(0x00025F71B14DA654)},
         {UINT64_C(0x000BD353769CE1F9), UINT64_C(0x0006FA3F3DDDC54C),
          UINT64_C(0x0002B84C2EC34B40), UINT64_C(0x0002BC5399680FE7),
          UINT64_C(0x00077DF80D7D51A4)},
         {UINT64_C(0x0004E2691B0328F3), UINT64_C(0x00050F4537961493),
          UINT64_C(0x00044E2C41C84DCA), UINT64_C(0x00052B780EFDCD69),
          UINT64_C(0x0005376F2A050E19)}},
    },
    {
        {{UINT64_C(0x000DA03985F38262), UINT64_C(0x00037D5F62914A3C),
          UINT64_C(0x000793B68F7811A9), UINT64_C(0x00032B90BCD39CE3),
          UINT64_C(0x00075AA44FE7E19C)},
         {UINT64_C(0x000F05AB91A2B12A), UINT64_C(0x00059AF4F8982C00),
          UINT64_C(0x00048CCE588AE51A), UINT64_C(0x00049E4C670BBE65),
          UINT64_C(0x0006B3D82691A651)},
         {UINT64_C(0x0008F5698465745B), UINT64_C(0x00075BE3EB74331D),
          UINT64_C(0x000737D69788EE86), UINT64_C(0x00062141377DCE86),
          UINT64_C(0x0006EFBEF11283CC)}},
        {{UINT64_C(0x000FB2D42C8285AF), UINT64_C(0x0006FD40664A5490),
          UINT64_C(0x00057F0694D97426), UINT64_C(0x0002A7A28B3356DA),
          UINT64_C(0x00028920886AF6EC)},
         {UINT64_C(0x000A8C24C0BF2760), UINT64_C(0x00068C3EC242BF3A),
          UINT64_C(0x0000CE0D0E1B7F97), UINT64_C(0x00061C58FABECF34),
          UINT64_C(0x00004CA319E801FD)},
         {UINT64_C(0x000325DE46C5736B), UINT64_C(0x000468915384C263),
          UINT64_C(0x0006347A5799363C), UINT64_C(0x0007210BB1344417),
          UINT64_C(0x00011E398C2404CC)}},
        {{UINT64_C(0x0006351562580ED8), UINT64_C(0x0006220AA1C9D62B),
          UINT64_C(0x0001CDF640634049), UINT64_C(0x0002FA7088B01B0A),
          UINT64_C(0x00019ACCA28277EE)},
         {UINT64_C(0x000C65B688EBF493), UINT64_C(0x0005B03149C63AE5),
          UINT64_C(0x0006AFA8197C4FD1), UINT64_C(0x0002D0DA5C748E78),
          UINT64_C(0x000662C002D4D15F)},
         {UINT64_C(0x0001118B8CBBB7E7), UINT64_C(0x0004CBAB82FD2BF5),
          UINT64_C(0x0002348B0CE469A5), UINT64_C(0x00023EB3398A797E),
          UINT64_C(0x0004E228589713E3)}},
        {{UINT64_C(0x0000B4E4D027C85C), UINT64_C(0x0000D803053DA772),
          UINT64_C(0x00065984BB60A337), UINT64_C(0x0007DC2376F7272A),
          UINT64_C(0x0000E9430E355BA4)},
         {UINT64_C(0x000A8E389D48606F), UINT64_C(0x00021F690AA2DBC8),
          UINT64_C(0x000142274ABC9F41), UINT64_C(0x00072BF593710713),
          UINT64_C(0x0002FD7F4AE2EE22)},
         {UINT64_C(0x000BF1C20DDD2691), UINT64_C(0x0005411630C2A271),
          UINT64_C(0x0002D6C2990BAF6C), UINT64_C(0x00016E01E690E20C),
          UINT64_C(0x0000E8F8FFA954EC)}},
        {{UINT64_C(0x0005A3AB6DAFDFD3), UINT64_C(0x0000D9D486BDD09A),
          UINT64_C(0x0002D2E51B7C9711), UINT64_C(0x00017CE407134280),
          UINT64_C(0x0003AD3997FA1672)},
         {UINT64_C(0x0003D8F172453802), UINT64_C(0x0002317FA3185EC2),
          UINT64_C(0x0000BA91852E2031), UINT64_C(0x0005EC464B3ED108),
          UINT64_C(0x00057C6D8CF0E0FD)},
         {UINT64_C(0x0008CA2766DB4A5B), UINT64_C(0x00073D443B130A20),
          UINT64_C(0x00058BF472C3BA39), UINT64_C(0x0006F5CC0907C053),
          UINT64_C(0x0003FC5C6F14BD87)}},
        {{UINT64_C(0x0004B65645E73CF6), UINT64_C(0x0007C5AB1C053774),
          UINT64_C(0x0005018E4FF1FC18), UINT64_C(0x000769511D97D00F),
          UINT64_C(0x0006AD6BD6EAA9E9)},
         {UINT64_C(0x00046576871857E4), UINT64_C(0x000215AA0839B591),
          UINT64_C(0x0004E1CAA9F6A2CA), UINT64_C(0x0007AB0F33E726C5),
          UINT64_C(0x00067F4BE171AA28)},
         {UINT64_C(0x0006531996604B7F), UINT64_C(0x000453BF42EC79D2),
          UINT64_C(0x00032113012CCEA4), UINT64_C(0x00006D0B5464300F),
          UINT64_C(0x0002176B1E5D6D50)}},
        {{UINT64_C(0x0001D3C100DC72D5), UINT64_C(0x0003654CB2834C97),
          UINT64_C(0x0005E965AC7DA69D), UINT64_C(0x00078DC0DD9AE1DD),
          UINT64_C(0x00019E21FECCAAF3)},
         {UINT64_C(0x00013C3FDF2B1672), UINT64_C(0x000619ED60AD922A),
          UINT64_C(0x0006A134A3138156), UINT64_C(0x000074D7A0EB94F4),
          UINT64_C(0x000655B9265AD61E)},
         {UINT64_C(0x00091D8E2767E0B1), UINT64_C(0x00065AEFAC257813),
          UINT64_C(0x00038076D1470BE1), UINT64_C(0x0003C981EA0EB22B),
          UINT64_C(0x00015A7A2D75B203)}},
        {{UINT64_C(0x000CC309EA9DA550), UINT64_C(0x000167109F6B7EEA),
          UINT64_C(0x00036256E39C62A5), UINT64_C(0x00023FE04A59A8D0),
          UINT64_C(0x00048F3DCFC704E4)},
         {UINT64_C(0x000DD13D87BC3FD8), UINT64_C(0x0001E1039D0C27A5),
          UINT64_C(0x00013884321DFE6C), UINT64_C(0x00018DF5EB967D7E),
          UINT64_C(0x00027626CDA767FD)},
         {UINT64_C(0x00081A4C254FE9AA), UINT64_C(0x00039710CABEFE25),
          UINT64_C(0x0001FE4ED0D3AF13), UINT64_C(0x00036B4C87AE2C9F),
          UINT64_C(0x00011248B666F136)}},
        {{UINT64_C(0x000E797C4CCA0B46), UINT64_C(0x000549315E592B39),
          UINT64_C(0x000446B1DF8247A1), UINT64_C(0x00030ED36C5BB7F3),
          UINT64_C(0x0002AA80BB30E10E)},
         {UINT64_C(0x000987E78A6334CD), UINT64_C(0x00021A51601C5BC5),
          UINT64_C(0x00056CB555352F37), UINT64_C(0x000225D060DB2429),
          UINT64_C(0x000385DB17BD6237)},
         {UINT64_C(0x0002ED3E680A5E84), UINT64_C(0x0005712851DFAB33),
          UINT64_C(0x00039A12254B9A45), UINT64_C(0x000216D42F43EEBB),
          UINT64_C(0x000357331598DA83)}},
        {{UINT64_C(0x000F7C1D3279D8A5), UINT64_C(0x00043F72163137B9),
          UINT64_C(0x00042C3FE876167F), UINT64_C(0x0003B0E73C83522A),
          UINT64_C(0x0001DB409981C582)},
         {UINT64_C(0x000122CD3118D65B), UINT64_C(0x00071763F291C87A),
          UINT64_C(0x000561465C8B3331), UINT64_C(0x0004944F4C61C7FD),
          UINT64_C(0x0002AAC9443A36DB)},
         {UINT64_C(0x0004D4FA4218C372), UINT64_C(0x00076C45E03C77EB),
          UINT64_C(0x000072FB97E90D33), UINT64_C(0x0005082CF2079905),
          UINT64_C(0x0002FE26BFD54FFA)}},
        {{UINT64_C(0x0007D19A42E4631A), UINT64_C(0x0006571B981A6AEC),
          UINT64_C(0x0004D393AE74429A), UINT64_C(0x000446A87D160168),
          UINT64_C(0x0003AF53BFC0F685)},
         {UINT64_C(0x0002D55D97B6EB19), UINT64_C(0x00039E6461F719F3),
          UINT64_C(0x000190259D2C913D), UINT64_C(0x0003C976B6FBF37A),
          UINT64_C(0x00019F876AD4BC35)},
         {UINT64_C(0x000EDF4304B4688B), UINT64_C(0x0000C939FCAC59CD),
          UINT64_C(0x00056BFC00BE589D), UINT64_C(0x0004B4C590A12AC7),
          UINT64_C(0x0007EF9566506AD0)}},
        {{UINT64_C(0x000A585FE0E0C2E0), UINT64_C(0x00018D0780A3AD5B),
          UINT64_C(0x0006DD4F15327657), UINT64_C(0x00009AB856FE2440),
          UINT64_C(0x000750F36E6E9B65)},
         {UINT64_C(0x0008B6024E61BB41), UINT64_C(0x0006393F246B2AD3),
          UINT64_C(0x000446AF58B6CA59), UINT64_C(0x00000520765881CA),
          UINT64_C(0x00061089ABFD62AB)},
         {UINT64_C(0x000633226585ECE4), UINT64_C(0x00076BF57CC213CB),
          UINT64_C(0x000484A01C226640), UINT64_C(0x00033C1F700F8FBA),
          UINT64_C(0x0000B0E1B3037271)}},
        {{UINT64_C(0x00054E468A9EFE70), UINT64_C(0x000608519FBFBF2D),
          UINT64_C(0x0005333558A69061), UINT64_C(0x0004D0D77DF73B72),
          UINT64_C(0x0005963DB206A396)},
         {UINT64_C(0x000394333A1E85F3), UINT64_C(0x000353C23C03493E),
          UINT64_C(0x0000C828A97CB1A5), UINT64_C(0x00078C43450120ED),
          UINT64_C(0x000555DF3569380F)},
         {UINT64_C(0x00042EFA777C3D87), UINT64_C(0x0006535188CEB3F6),
          UINT64_C(0x000711F155E76C84), UINT64_C(0x0000E3BA9883F418),
          UINT64_C(0x00057A3C7F7ECCF3)}},
        {{UINT64_C(0x000B073EF993682F), UINT64_C(0x00033F05FA3CCE30),
          UINT64_C(0x00068B17D4A39381), UINT64_C(0x0004A922A545C066),
          UINT64_C(0x0006F6DAE315DA70)},
         {UINT64_C(0x00014D0E6682401A), UINT64_C(0x0007CCF46A4DD19B),
          UINT64_C(0x00075F65E59EC735), UINT64_C(0x0001444919609445),
          UINT64_C(0x00056DEA40CFB96B)},
         {UINT64_C(0x00016AF62D5F1A62), UINT64_C(0x00031733B90868F3),
          UINT64_C(0x0006590B06472897), UINT64_C(0x00049790ACFA5796),
          UINT64_C(0x0003AB09B226B35A)}},
        {{UINT64_C(0x000329065372355E), UINT64_C(0x0007AB688B6A2989),
          UINT64_C(0x00045AC32571E188), UINT64_C(0x0005840E91DC5D76),
          UINT64_C(0x0000A0DFC537909E)},
         {UINT64_C(0x0005F472964EF0CE), UINT64_C(0x0003D2FA150C40C5),
          UINT64_C(0x0007CBF1211260FB), UINT64_C(0x0003709955BA6654),
          UINT64_C(0x000217DBDD3D5D0D)},
         {UINT64_C(0x000A1158C12DB2A4), UINT64_C(0x0004C90A2B817CA8),
          UINT64_C(0x00061F4B5B3A38EC), UINT64_C(0x000380423D6EF40B),
          UINT64_C(0x00000B4B0EF2C8F0)}},
        {{UINT64_C(0x00028D6E002BCA29), UINT64_C(0x0007A3997D7D546A),
          UINT64_C(0x0005D8C776975959), UINT64_C(0x0000317399D2025E),
          UINT64_C(0x000754B217E301A9)},
         {UINT64_C(0x0002CDE1681E8828), UINT64_C(0x000380F9FC21B2AB),
          UINT64_C(0x00003AD0F0DC0117), UINT64_C(0x0003D01C67CEC59B),
          UINT64_C(0x00039049C45C39C8)},
         {UINT64_C(0x0000CE99521AEEAB), UINT64_C(0x0003B4C67C5B669C),
          UINT64_C(0x0000E043A30EE5B7), UINT64_C(0x0003F0C4E94F62CF),
          UINT64_C(0x0001AB4F30E0A8F5)}},
    },
    {
        {{UINT64_C(0x000DC91F212AB480), UINT64_C(0x0005945E1877E8EC),
          UINT64_C(0x00013573E60D6704), UINT64_C(0x00069D9C4A961CA2),
          UINT64_C(0x0003DE09EC8A48AF)},
         {UINT64_C(0x000E81E566BFFA33), UINT64_C(0x00033E06B06E4111),
          UINT64_C(0x0002D32A23297AFD), UINT64_C(0x0005F9BFCE126AC6),
          UINT64_C(0x0001FF61C1A88D8E)},
         {UINT64_C(0x0008D3EC31CFC3EE), UINT64_C(0x000046D8793EAB63),
          UINT64_C(0x0000C143A50BCCDC), UINT64_C(0x0006EAB8519D9BAD),
          UINT64_C(0x00074830BD46D5B4)}},
        {{UINT64_C(0x0000574A2032B148), UINT64_C(0x0004328CBA692C33),
          UINT64_C(0x000232F42D123335), UINT64_C(0x000181DEF8EAD0DD),
          UINT64_C(0x000451E2578C52D5)},
         {UINT64_C(0x000695C6731F0527), UINT64_C(0x0005CD46B5C17880),
          UINT64_C(0x00015EAA924D0904), UINT64_C(0x00007F6FB72E84D0),
          UINT64_C(0x0000CEB928AFDBBD)},
         {UINT64_C(0x0004D3626FF5B12C), UINT64_C(0x00051FE69CA7F94B),
          UINT64_C(0x000772203CEE99E4), UINT64_C(0x0005F1FAC7E8C081),
          UINT64_C(0x0003E578A1665444)}},
        {{UINT64_C(0x000B298801B1FE77), UINT64_C(0x0002586B0CE008AC),
          UINT64_C(0x00045E778DC50E87), UINT64_C(0x0000FDCB5F3CC8F7),
          UINT64_C(0x00017C5D39EFC586)},
         {UINT64_C(0x0002A41D8771BA87), UINT64_C(0x0004C8102431A7BA),
          UINT64_C(0x0000B390AB74C50C), UINT64_C(0x0002C546F7C05D4D),
          UINT64_C(0x0006AAD492B3116F)},
         {UINT64_C(0x0008FF03C1367A73), UINT64_C(0x0002A4A748616572),
          UINT64_C(0x000605B500CB4A72), UINT64_C(0x0005C39BB9BB5B8B),
          UINT64_C(0x000569FE03A16B9F)}},
        {{UINT64_C(0x000275341CBCADF6), UINT64_C(0x000430EFD84DCC71),
          UINT64_C(0x00022B997FAB00F0), UINT64_C(0x00030C5632CF0693),
          UINT64_C(0x0002A0826D840314)},
         {UINT64_C(0x000C11CD5CF6EE57), UINT64_C(0x0001257E6308FBB1),
          UINT64_C(0x0002F7CE59284FB8), UINT64_C(0x00057DD9BDD3EF59),
          UINT64_C(0x0003452936337369)},
         {UINT64_C(0x000530B11F6BDDA7), UINT64_C(0x0007A51865D93007),
          UINT64_C(0x00076B5BC60F0AC9), UINT64_C(0x000212F031E350EA),
          UINT64_C(0x000075AED0A2ADAE)}},
        {{UINT64_C(0x000AD78B391BD2D5), UINT64_C(0x0003A11ABEA7FF04),
          UINT64_C(0x00046A25F0F350A4), UINT64_C(0x0001543CC7EF57AB),
          UINT64_C(0x00069223D8309EB4)},
         {UINT64_C(0x000B33AD1F86A15A), UINT64_C(0x00036B112A03501B),
          UINT64_C(0x000344A2066D4EF2), UINT64_C(0x00055436BD4B9A9D),
          UINT64_C(0x00041A71A942A508)},
         {UINT64_C(0x0006FF9618F443D0), UINT64_C(0x0000F3000CB2F1BA),
          UINT64_C(0x00022C2897DAADE0), UINT64_C(0x0004F604C596FCBC),
          UINT64_C(0x000585C83B55AFF4)}},
        {{UINT64_C(0x00084B6AF7D110A1), UINT64_C(0x0001EFC9DBBC4234),
          UINT64_C(0x000796661C37007B), UINT64_C(0x00010DB07259FC43),
          UINT64_C(0x000202355E8495E8)},
         {UINT64_C(0x000DA5E823077C6C), UINT64_C(0x0003C9503E1777C0),
          UINT64_C(0x0001A5E4F1DED645), UINT64_C(0x0000DE482043B271),
          UINT64_C(0x0004B4CCC6B33E65)},
         {UINT64_C(0x00011797174CD365), UINT64_C(0x000242D89859CD09),
          UINT64_C(0x0000714A4593BD77), UINT64_C(0x00051B9197490AE5),
          UINT64_C(0x0006E6A726266AD0)}},
        {{UINT64_C(0x0006163606CF5614), UINT64_C(0x0003B9BA82C96535),
          UINT64_C(0x000075E66F7CF6D4), UINT64_C(0x000411E3154EC181),
          UINT64_C(0x00029B40D6F2E74B)},
         {UINT64_C(0x0005D2E429379A65), UINT64_C(0x00008B1A72434343),
          UINT64_C(0x00041E8D9808E74A), UINT64_C(0x0004F14CB75EB689),
          UINT64_C(0x0004F434008C1B41)},
         {UINT64_C(0x00067B260BE03B58), UINT64_C(0x0001EEF4691ADD3A),
          UINT64_C(0x0002850460D0C4EE), UINT64_C(0x0005AFCC1FFDAA08),
          UINT64_C(0x0003AB9267B9F6DC)}},
        {{UINT64_C(0x000B5389B777649D), UINT64_C(0x0001F24DBD666B73),
          UINT64_C(0x00036DFB5DE2A31F), UINT64_C(0x0005EB994849E227),
          UINT64_C(0x0004279FBFCC1BD4)},
         {UINT64_C(0x000B7EA950A7028F), UINT64_C(0x000732F69E7CF7DE),
          UINT64_C(0x00066069E71F3D7B), UINT64_C(0x00028033D27359B1),
          UINT64_C(0x000183D3130F9EED)},
         {UINT64_C(0x000D74EA5E42F626), UINT64_C(0x0004B6BC58EA9C64),
          UINT64_C(0x0003C2C8FEA82634), UINT64_C(0x0002CE04C5B571DD),
          UINT64_C(0x0001E397436718BF)}},
        {{UINT64_C(0x000B7484714F9403), UINT64_C(0x0004FC7BF02108DB),
          UINT64_C(0x0003678FA400AD90), UINT64_C(0x00028EF7C8AC0FE1),
          UINT64_C(0x000504A42505BDEF)},
         {UINT64_C(0x000A0A066ABF7FBB), UINT64_C(0x00072AEC8D4433CE),
          UINT64_C(0x0002A0384B583EC1), UINT64_C(0x0002AB91E995640C),
          UINT64_C(0x000169BC2751E588)},
         {UINT64_C(0x0009EB12660357B5), UINT64_C(0x00077A9937FD5E5F),
          UINT64_C(0x00021FF5CE16F36C), UINT64_C(0x0001C906DF80DF41),
          UINT64_C(0x0000CC48BA3DF0EA)}},
        {{UINT64_C(0x000E23A04EED9E35), UINT64_C(0x0004ADC00E39212A),
          UINT64_C(0x00017453A70FE352), UINT64_C(0x0006D407B1DB39B2),
          UINT64_C(0x0005D825F613189B)},
         {UINT64_C(0x00040B93828FA640), UINT64_C(0x00017C0D3D748E6A),
          UINT64_C(0x0003E2BC36916CA3), UINT64_C(0x00026F60A9DD3D92),
          UINT64_C(0x0002CB47886C390F)},
         {UINT64_C(0x0008E7468212D22E), UINT64_C(0x0005E3F127FB8644),
          UINT64_C(0x0006C85DFBC55D78), UINT64_C(0x0007DFDFC6C531F3),
          UINT64_C(0x000674E26E15BF6B)}},
        {{UINT64_C(0x000742AB1CE45F49), UINT64_C(0x000352695906DAC7),
          UINT64_C(0x00067D9DD8206042), UINT64_C(0x0002A8F523687321),
          UINT64_C(0x000209C2CC3DF3F2)},
         {UINT64_C(0x000582E07DACB336), UINT64_C(0x000659136B8B103E),
          UINT64_C(0x00075BFAC5E74BD0), UINT64_C(0x00030FA01037A173),
          UINT64_C(0x00027A6E9B54A029)},
         {UINT64_C(0x00010636DC94228F), UINT64_C(0x000334F99AD2D208),
          UINT64_C(0x000210CE39BF48FB), UINT64_C(0x0005CE98A1D2F02D),
          UINT64_C(0x0007E987948C2C36)}},
        {{UINT64_C(0x00060BD25865CC5A), UINT64_C(0x000717975E64B3FD),
          UINT64_C(0x000791116DD316E7), UINT64_C(0x000027FC70709E15),
          UINT64_C(0x000014CE5C4B9FED)},
         {UINT64_C(0x000FCB5EE438E62F), UINT64_C(0x0003918BFDF256E1),
          UINT64_C(0x000122BF1C544888), UINT64_C(0x00014620D1E4D6BE),
          UINT64_C(0x00031786B5D1AA6E)},
         {UINT64_C(0x000D8369134DE3F1), UINT64_C(0x000358E513D33827),
          UINT64_C(0x0001AD06806E9F25), UINT64_C(0x0007751294480EB8),
          UINT64_C(0x00070E23870117F1)}},
        {{UINT64_C(0x00006F3BCD1B2F70), UINT64_C(0x0000FA5C78F50611),
          UINT64_C(0x000411C55245B3EE), UINT64_C(0x0004944F3F6FC1FB),
          UINT64_C(0x000044A8C001AA25)},
         {UINT64_C(0x000D4183C7441852), UINT64_C(0x000055D95E60582E),
          UINT64_C(0x0007BCDE3E7457F2), UINT64_C(0x00008C8DB0B27127),
          UINT64_C(0x000151434B629450)},
         {UINT64_C(0x000E723B7F1ECEB3), UINT64_C(0x0004AD5542379FD9),
          UINT64_C(0x0007AD503D0F9616), UINT64_C(0x0007C70002B01072),
          UINT64_C(0x00014758CEF08E44)}},
        {{UINT64_C(0x000C404279D4475B), UINT64_C(0x00069FDFED5F482D),
          UINT64_C(0x0004DDA9A420B9DE), UINT64_C(0x000442DE4FB067AC),
          UINT64_C(0x00011575126C1A87)},
         {UINT64_C(0x0000D4A13CE38DD3), UINT64_C(0x00055443D3A075FC),
          UINT64_C(0x0002D0CC46239CD5), UINT64_C(0x00037A0D9341BE9F),
          UINT64_C(0x0007D72576A16B0E)},
         {UINT64_C(0x00018F1C0EA31782), UINT64_C(0x00017F5C99216478),
          UINT64_C(0x00070B5287151CE4), UINT64_C(0x00069D184D521E3F),
          UINT64_C(0x00050B904149AB2A)}},
        {{UINT64_C(0x000B9757956238BE), UINT64_C(0x000719EF66129C48),
          UINT64_C(0x0003A4A2018A1497), UINT64_C(0x00060697B014C86D),
          UINT64_C(0x0004211842CFCBB6)},
         {UINT64_C(0x00000FC466BA4129), UINT64_C(0x0005C2C02129FAE5),
          UINT64_C(0x0007CC7E722EDDB4), UINT64_C(0x000463EFB0DEA9BC),
          UINT64_C(0x0005CEAB5704312E)},
         {UINT64_C(0x0003CB3EF741A489), UINT64_C(0x000447C22A9AC8F6),
          UINT64_C(0x00070DC1AC662C00), UINT64_C(0x000643FBD84746C4),
          UINT64_C(0x00047AC14BC4BE7F)}},
        {{UINT64_C(0x0001F0ED4EAB4BF3), UINT64_C(0x0003BDBDE234EFD5),
          UINT64_C(0x0007F9817DB30238), UINT64_C(0x0005F1CA682E32C5),
          UINT64_C(0x0004EA29A4CB0AC2)},
         {UINT64_C(0x000119149BBD8CE2), UINT64_C(0x00053B67849C8636),
          UINT64_C(0x0000EE5D57DDF5A8), UINT64_C(0x0004ACD87314DB39),
          UINT64_C(0x00050B40A053EECC)},
         {UINT64_C(0x000A4EC0C58B1A27), UINT64_C(0x0004144223834F69),
          UINT64_C(0x00006C3A5FA7A66C), UINT64_C(0x0001E7C747C08629),
          UINT64_C(0x0006C75F05A9CE16)}},
    },
    {
        {{UINT64_C(0x000DA9D6805AB8BD), UINT64_C(0x000551D3DA5834BB),
          UINT64_C(0x00044E2A3C850244), UINT64_C(0x00003C161B738B16),
          UINT64_C(0x00016FA62C9D5E85)},
         {UINT64_C(0x0002A0101ADD9A68), UINT64_C(0x00005F00A8B73A3E),
          UINT64_C(0x0002700C6352EA8B), UINT64_C(0x00058F72766D9CF5),
          UINT64_C(0x0000786CDD193DF0)},
         {UINT64_C(0x0008EF307292DAA3), UINT64_C(0x00078A0B85552DDA),
          UINT64_C(0x0002B5E21BAB14CA), UINT64_C(0x00042FBE2B9E130A),
          UINT64_C(0x0006A1FF64B8DFE0)}},
        {{UINT64_C(0x000C37A44368992F), UINT64_C(0x000503AD37567295),
          UINT64_C(0x00022D6E783EB997), UINT64_C(0x00073209A3FE24D5),
          UINT64_C(0x00061BEA701A085E)},
         {UINT64_C(0x000F79A9906695E1), UINT64_C(0x0006563AA33BECCA),
          UINT64_C(0x0006D9C258571274), UINT64_C(0x0002082E0EFF1CF3),
          UINT64_C(0x00006858AC5958CD)},
         {UINT64_C(0x00073B5DF922B953), UINT64_C(0x0007D833BB16576F),
          UINT64_C(0x0002D7E0952EDC43), UINT64_C(0x0004C40B62DE9061),
          UINT64_C(0x0006802A03BBC4A8)}},
        {{UINT64_C(0x000AA4FF9CD029FB), UINT64_C(0x0006A8F58A61B495),
          UINT64_C(0x00076DE5AEDD2889), UINT64_C(0x000418E79FA28A81),
          UINT64_C(0x0004AC6187BA7E86)},
         {UINT64_C(0x000BB424077E0F78), UINT64_C(0x0006BE304793DFB9),
          UINT64_C(0x0001D1A8E8B74E8C), UINT64_C(0x000400EE04CA634E),
          UINT64_C(0x0000D23EE5F7A76A)},
         {UINT64_C(0x000E8C82769B883D), UINT64_C(0x00061FF0A44B7C0C),
          UINT64_C(0x00071C3F9C0C1C14), UINT64_C(0x0001A72BC400F4FB),
          UINT64_C(0x000063C977E0C2A2)}},
        {{UINT64_C(0x000B7EA553156CDE), UINT64_C(0x0004F77ED1DA0116),
          UINT64_C(0x0005313D6AB54554), UINT64_C(0x00068205E8633D72),
          UINT64_C(0x0004C0F6F5D7AA5B)},
         {UINT64_C(0x0002AC03322F3ABE), UINT64_C(0x00013B9DB1E063DB),
          UINT64_C(0x0001653067E1D575), UINT64_C(0x00068FA0726EBED0),
          UINT64_C(0x00056D9BF5F7ACB5)},
         {UINT64_C(0x0008092305F9E607), UINT64_C(0x00050ADE9DDED289),
          UINT64_C(0x000677C866291C9D), UINT64_C(0x0000A00D6AD82313),
          UINT64_C(0x000204C6129E7A63)}},
        {{UINT64_C(0x000CC52FB8AA29BE), UINT64_C(0x00001FD5EBA86F95),
          UINT64_C(0x0003EDEE7EE1584C), UINT64_C(0x0007A98D872F3541),
          UINT64_C(0x00041BB59C74740D)},
         {UINT64_C(0x000B91A314979A22), UINT64_C(0x000445CC55FA6E68),
          UINT64_C(0x00029A9728593F50), UINT64_C(0x0003D453E9BB2BF4),
          UINT64_C(0x000458E9C150C319)},
         {UINT64_C(0x0008B902F4166F58), UINT64_C(0x00008BF4504AEFA7),
          UINT64_C(0x000507F7245632D2), UINT64_C(0x00046F4236C007FE),
          UINT64_C(0x0005F8B80AB194EF)}},
        {{UINT64_C(0x000E0E279B58D515), UINT64_C(0x000494C3FDFB0D3E),
          UINT64_C(0x0006ADD516425B77), UINT64_C(0x0000C99F3F472FC4),
          UINT64_C(0x00023C04A4713378)},
         {UINT64_C(0x00033C225C70B892), UINT64_C(0x0005DEF1B82B20A8),
          UINT64_C(0x00062D4704CA33EF), UINT64_C(0x00001505BB87DFBE),
          UINT64_C(0x00018D6306B777E3)},
         {UINT64_C(0x000A6436A9F762A4), UINT64_C(0x000102ABB9E7477D),
          UINT64_C(0x00004BD6FA6C8FD0), UINT64_C(0x0002E4FAD22E39A3),
          UINT64_C(0x0007D2D079A6B2DA)}},
        {{UINT64_C(0x000B537BD4E0A6B1), UINT64_C(0x00064415199F9972),
          UINT64_C(0x0002D496FA892827), UINT64_C(0x00029834D9583EE0),
          UINT64_C(0x0001F325BFC1AEE2)},
         {UINT64_C(0x000BE9617256C790), UINT64_C(0x0002301EF1F61C9F),
          UINT64_C(0x0002A218C8615200), UINT64_C(0x00060780EED8C6E7),
          UINT64_C(0x0004A46B99106694)},
         {UINT64_C(0x00037B99B0CAB13C), UINT64_C(0x00078242AB738B41),
          UINT64_C(0x00018258BBD69E1C), UINT64_C(0x00043D3E9A166688),
          UINT64_C(0x00055321313F52D3)}},
        {{UINT64_C(0x00067501A6250F6D), UINT64_C(0x000051135CC1DB41),
          UINT64_C(0x0005B70CAFF3B24D), UINT64_C(0x0007AF6469DAB79A),
          UINT64_C(0x0005A486101FF730)},
         {UINT64_C(0x000B81D6FB3B7613), UINT64_C(0x000705F8371DE858),
          UINT64_C(0x0004616EECCC6DE2), UINT64_C(0x0003B624C3D98F88),
          UINT64_C(0x0002580784E014F7)},
         {UINT64_C(0x0003C6B7855856C3), UINT64_C(0x0002ACC5BCB17705),
          UINT64_C(0x0004E8DA7BC00D1D), UINT64_C(0x0006A7AF33BBD40A),
          UINT64_C(0x0004CA608C42E191)}},
        {{UINT64_C(0x00014957F896B8DD), UINT64_C(0x0002EE29ED7F94C7),
          UINT64_C(0x00006C535FD580D1), UINT64_C(0x0007EE5C2C854B58),
          UINT64_C(0x000390B6176CC0A5)},
         {UINT64_C(0x000D8E851115F82F), UINT64_C(0x00057712C2473469),
          UINT64_C(0x0005FED753D7DE49), UINT64_C(0x000100E99106EB96),
          UINT64_C(0x00032A5894C01232)},
         {UINT64_C(0x0003F3BD2CBCD3E4), UINT64_C(0x00051B2182A69BC1),
          UINT64_C(0x00047F72BDF9D1E5), UINT64_C(0x0004386BB2531556),
          UINT64_C(0x0006251703B0886A)}},
        {{UINT64_C(0x000DA5B8DDCC32C8), UINT64_C(0x0004B6E439859BAC),
          UINT64_C(0x000565788B32A991), UINT64_C(0x0007DA2C259D0EFC),
          UINT64_C(0x00026E2BCA2F2403)},
         {UINT64_C(0x00036BD2B233B8DF), UINT64_C(0x000025EC957D6035),
          UINT64_C(0x000421C4260564F0), UINT64_C(0x00024C08F5533B05),
          UINT64_C(0x0004195603852EB3)},
         {UINT64_C(0x0007B58166F4C3F1), UINT64_C(0x000262CCAD523D27),
          UINT64_C(0x0002167831F73FA9), UINT64_C(0x0002330A70233315),
          UINT64_C(0x0001E997E60115B3)}},
        {{UINT64_C(0x000B11A8A180A2DD), UINT64_C(0x000740DE93A3B1BB),
          UINT64_C(0x0001A75730E8D6F7), UINT64_C(0x0002787677BCD20B),
          UINT64_C(0x0002C62508D0E0E3)},
         {UINT64_C(0x000193FF85DC4853), UINT64_C(0x0006FF8B716F7C95),
          UINT64_C(0x0005695A400F15D0), UINT64_C(0x0006FCEA3FD445A5),
          UINT64_C(0x0006827935BB527C)},
         {UINT64_C(0x000E756880792D5C), UINT64_C(0x00017C4CBFDCB6F6),
          UINT64_C(0x0003621A1F5AD47B), UINT64_C(0x00008ACFEC1E2F0B),
          UINT64_C(0x000680EE7BD4FDC9)}},
        {{UINT64_C(0x000B22617017AD54), UINT64_C(0x0006D79A05652478),
          UINT64_C(0x00078436BB1A4FC2), UINT64_C(0x000588C3017ACA81),
          UINT64_C(0x000721CFE104D7FC)},
         {UINT64_C(0x00058F6241221394), UINT64_C(0x0003B5CC42F98968),
          UINT64_C(0x0005B8C26DF7368C), UINT64_C(0x000258033A96E20A),
          UINT64_C(0x0006CDFD92327819)},
         {UINT64_C(0x000D5F316E441B95), UINT64_C(0x0007262AFF6C2AD0),
          UINT64_C(0x000615C867B8A858), UINT64_C(0x0002304A98935CF1),
          UINT64_C(0x0001C346C9D50FBE)}},
        {{UINT64_C(0x000B8D64E6633E8B), UINT64_C(0x0004415800321D6A),
          UINT64_C(0x000112873DB9D153), UINT64_C(0x0007454E53F98187),
          UINT64_C(0x0006D9C7B5E085F8)},
         {UINT64_C(0x0008054C9FC0114A), UINT64_C(0x00060A1D43700A51),
          UINT64_C(0x0007F536F76DCA7B), UINT64_C(0x00059A4ADEA37323),
          UINT64_C(0x0006F027E38F2362)},
         {UINT64_C(0x000304C8AA59BE30), UINT64_C(0x0007FE5A68FCF77D),
          UINT64_C(0x0006BDE087AB92A0), UINT64_C(0x000025E8F669E08C),
          UINT64_C(0x00018E93C6B2C356)}},
        {{UINT64_C(0x0004A344E63D4ACA), UINT64_C(0x0007C308BC5ED73E),
          UINT64_C(0x0003A32DEAF4E9FA), UINT64_C(0x0004472F4E854593),
          UINT64_C(0x000047D341D3DDFD)},
         {UINT64_C(0x0007532D39E9B3BB), UINT64_C(0x000217DF5A0A7433),
          UINT64_C(0x00074975495E1565), UINT64_C(0x0002821D2EEF04CF),
          UINT64_C(0x00054FC7406A916F)},
         {UINT64_C(0x000913F345C8BBFF), UINT64_C(0x0006989D1646C723),
          UINT64_C(0x00027764F8A16ACB), UINT64_C(0x000008C125AF8D5F),
          UINT64_C(0x0006800FE91196F4)}},
        {{UINT64_C(0x00082C098B00AA79), UINT64_C(0x000770B1C497ACD1),
          UINT64_C(0x000344FD551B8CCE), UINT64_C(0x00023F33BE497F56),
          UINT64_C(0x0002EAF897898B65)},
         {UINT64_C(0x0007F51FA8DC697B), UINT64_C(0x00074CE3069BBE12),
          UINT64_C(0x0004C428EA0209F5), UINT64_C(0x00002F2D8D0BB62C),
          UINT64_C(0x00004E256AFA4C76)},
         {UINT64_C(0x00001D22BDE52262), UINT64_C(0x0006DE17872B5135),
          UINT64_C(0x00066D4BFED89F63), UINT64_C(0x000389FDDE527F9D),
          UINT64_C(0x0000EB2F6E615478)}},
        {{UINT64_C(0x000C58D612641095), UINT64_C(0x00054CA77FCC5570),
          UINT64_C(0x0001DA1FC218000A), UINT64_C(0x00037F0150DC6C3C),
          UINT64_C(0x00022955963EF643)},
         {UINT64_C(0x0009C17EB03D6EA7), UINT64_C(0x0002DDDAC1318776),
          UINT64_C(0x0004E4B91FA27202), UINT64_C(0x0001E2352AB304E3),
          UINT64_C(0x00060A0E261D6691)},
         {UINT64_C(0x000B576743FFBA33), UINT64_C(0x0005A197F3AFDA0C),
          UINT64_C(0x000429DD4FCB8A3F), UINT64_C(0x0001AF629993F4B3),
          UINT64_C(0x00045EB119867A22)}},
    },
    {
        {{UINT64_C(0x000AC144EF178112), UINT64_C(0x000094A0264CAF23),
          UINT64_C(0x0004A0AC5A24DEFD), UINT64_C(0x0007C7625D1D184E),
          UINT64_C(0x0005737B194A6A86)},
         {UINT64_C(0x0008F38EC35EE99B), UINT64_C(0x0006D9BAA844775F),
          UINT64_C(0x00046F84DF77B762), UINT64_C(0x0002544D6FE58FDC),
          UINT64_C(0x00077B72BB7289FB)},
         {UINT64_C(0x000DD166D7288C6E), UINT64_C(0x0001C67A51DA413C),
          UINT64_C(0x0006973C6FAD10E1), UINT64_C(0x00031DE5E5EDA939),
          UINT64_C(0x000393F71ED28892)}},
        {{UINT64_C(0x000B1C84EECF7E6B), UINT64_C(0x00056A60AC35BAB2),
          UINT64_C(0x0002F06DF1910A93), UINT64_C(0x0000618DC5FAEB06),
          UINT64_C(0x0001D49317BBBDF6)},
         {UINT64_C(0x000493F2A36F71D9), UINT64_C(0x00040122E257F266),
          UINT64_C(0x00007974D73D3705), UINT64_C(0x0001276FD01A753E),
          UINT64_C(0x000756654735EC6A)},
         {UINT64_C(0x0007DB79632DD089), UINT64_C(0x0001FE3FA5F4A3F7),
          UINT64_C(0x0001716E3C167A6F), UINT64_C(0x00053B6AA21FEB09),
          UINT64_C(0x00023EC2880EEB4A)}},
        {{UINT64_C(0x00025A84454FFE86), UINT64_C(0x000780F2E547DCDF),
          UINT64_C(0x000701A2C14FDED4), UINT64_C(0x0004B8A116F259A3),
          UINT64_C(0x000695574E1284BB)},
         {UINT64_C(0x000D30440055360D), UINT64_C(0x00003410B657F1F4),
          UINT64_C(0x0006EE23795C3521), UINT64_C(0x0004C9A2F7E3A24B),
          UINT64_C(0x0000FD8B32023BD7)},
         {UINT64_C(0x000AE6F82F35FF69), UINT64_C(0x0003F3D6F04FB2C4),
          UINT64_C(0x00005F2596E4AACD), UINT64_C(0x0007F09E9725486A),
          UINT64_C(0x00062C57118684FB)}},
        {{UINT64_C(0x00071AB6451B13D2), UINT64_C(0x000619FA9ABFE206),
          UINT64_C(0x0004C28E4E8FA08C), UINT64_C(0x0002CEE0DAC526F3),
          UINT64_C(0x0005F61B37975A6F)},
         {UINT64_C(0x00015634247F1514), UINT64_C(0x00043246C09599E2),
          UINT64_C(0x000502B5C89D4562), UINT64_C(0x00055B6E5B70BD64),
          UINT64_C(0x0000924E7D2DA3A4)},
         {UINT64_C(0x000DC2A7C58A69B5), UINT64_C(0x0007579FCF83B046),
          UINT64_C(0x000490212587ACC9), UINT64_C(0x00042C0D4FB52DDA),
          UINT64_C(0x0006EA269EA5E4FF)}},
        {{UINT64_C(0x000FAB3168E74931), UINT64_C(0x000009FC282310C8),
          UINT64_C(0x0002E357A087E03B), UINT64_C(0x0002E82B3F54812E),
          UINT64_C(0x0000A1378335E998)},
         {UINT64_C(0x0009E98B211CF746), UINT64_C(0x000294635069D795),
          UINT64_C(0x00066C9A8A94C197), UINT64_C(0x00049DBA942CC086),
          UINT64_C(0x00038BE03A6F71F4)},
         {UINT64_C(0x000D42F0FB6C56DF), UINT64_C(0x0003D1EF830454E8),
          UINT64_C(0x000225CD3F51B513), UINT64_C(0x00002F23E7710F71),
          UINT64_C(0x0007ED09816C213D)}},
        {{UINT64_C(0x000475A30D2F95C3), UINT64_C(0x0006534DABA67C75),
          UINT64_C(0x0005FFF4349E9DCF), UINT64_C(0x0005741BFE20E51E),
          UINT64_C(0x0007C9C06E28F6DB)},
         {UINT64_C(0x0004C91D367D647C), UINT64_C(0x0005DB920659E98B),
          UINT64_C(0x0006E2DC6EDEAE13), UINT64_C(0x00045FADDDCD7438),
          UINT64_C(0x0006A3D3CCDD5BDE)},
         {UINT64_C(0x000030AFB5D712D2), UINT64_C(0x0004232A6E545B6F),
          UINT64_C(0x0006CC8095D31E5A), UINT64_C(0x0005E95D17B4F93D),
          UINT64_C(0x000640826F938DB4)}},
        {{UINT64_C(0x00054E864AE39A36), UINT64_C(0x00075453A7420C12),
          UINT64_C(0x0007550FC0647586), UINT64_C(0x0000CE261F056DAC),
          UINT64_C(0x0006EC06688237F9)},
         {UINT64_C(0x00009D858855A118), UINT64_C(0x00035D6E1CAC4C58),
          UINT64_C(0x00051BC56EDB0309), UINT64_C(0x00056720925976FA),
          UINT64_C(0x0003095195EC98C0)},
         {UINT64_C(0x00028453ABBCD94C), UINT64_C(0x0000442BD67826FA),
          UINT64_C(0x00027091B2C7D123), UINT64_C(0x00053D5CE098F2DF),
          UINT64_C(0x00033B91CBED8C33)}},
        {{UINT64_C(0x00031BB1AEC4EB7B), UINT64_C(0x000404BD71986FBA),
          UINT64_C(0x000789F140F41C34), UINT64_C(0x00046319EC635090),
          UINT64_C(0x00062555BCEF03FC)},
         {UINT64_C(0x000B7957AF9DF20B), UINT64_C(0x00061499B0C64744),
          UINT64_C(0x0005A1460AB6F031), UINT64_C(0x000254B48FE3B63C),
          UINT64_C(0x000138DA1B0B8458)},
         {UINT64_C(0x0008FC31F72B00C5), UINT64_C(0x00009260DAF93E82),
          UINT64_C(0x0007724F21722EEC), UINT64_C(0x00065104B46DE00D),
          UINT64_C(0x0005C74C4AADE4A1)}},
        {{UINT64_C(0x000C942D7E200CDA), UINT64_C(0x0004DCCF2C9062F8),
          UINT64_C(0x00033C6DC30EF168), UINT64_C(0x0005876E586F49F0),
          UINT64_C(0x0005C625124B23D5)},
         {UINT64_C(0x000AF942F3B8567F), UINT64_C(0x00068DD426ED4B8D),
          UINT64_C(0x00074CE2434E2244), UINT64_C(0x0005BC01452BAF26),
          UINT64_C(0x00068D340AA53E01)},
         {UINT64_C(0x00062BADB88E1FEE), UINT64_C(0x00001B7927E30590),
          UINT64_C(0x0007FB96EBA04357), UINT64_C(0x00063C4C5B983654),
          UINT64_C(0x0003CB27C00B0E03)}},
        {{UINT64_C(0x0003A8ABA3BA06D8), UINT64_C(0x0005183A547A5052),
          UINT64_C(0x0002FA684E05B048), UINT64_C(0x00037DC680820759),
          UINT64_C(0x0003A758E398D110)},
         {UINT64_C(0x000AE41D00F8BBE3), UINT64_C(0x0007C91843988CEE),
          UINT64_C(0x00008AC196646158), UINT64_C(0x0004F9CA63F0D300),
          UINT64_C(0x0004AE71F5595151)},
         {UINT64_C(0x0009C5C331E75B70), UINT64_C(0x000034AEB589633E),
          UINT64_C(0x0003A2AFA63836A6), UINT64_C(0x00071AA62CA57C97),
          UINT64_C(0x0002588D39981664)}},
        {{UINT64_C(0x0002F5861915F134), UINT64_C(0x0002E22B903AD5D1),
          UINT64_C(0x0004B62079B373C7), UINT64_C(0x0006E593A78A5789),
          UINT64_C(0x0006ACFA4114B928)},
         {UINT64_C(0x000845994C9F8482), UINT64_C(0x00018D23FF32CC4E),
          UINT64_C(0x0001EF02A3741210), UINT64_C(0x0003A1F60A318F68),
          UINT64_C(0x00074BBE122C022E)},
         {UINT64_C(0x000235F03B8817CF), UINT64_C(0x0004136EC580474B),
          UINT64_C(0x00030B5BE4C70BFD), UINT64_C(0x0006771287C6A491),
          UINT64_C(0x0004EE10D834F1D6)}},
        {{UINT64_C(0x0009238C49B5B594), UINT64_C(0x0006D951D5930D4A),
          UINT64_C(0x00052C92A1704BF4), UINT64_C(0x00058049C0061E24),
          UINT64_C(0x0003414A1DFBD8A0)},
         {UINT64_C(0x0003B17B475F899E), UINT64_C(0x0000A207F4C1C87F),
          UINT64_C(0x00005DAC41647336), UINT64_C(0x000017D58BC6FE28),
          UINT64_C(0x00006090A9CB1708)},
         {UINT64_C(0x0000E353DF9AED9F), UINT64_C(0x000211A100A66400),
          UINT64_C(0x000537F1DBBF53C6), UINT64_C(0x00061176C39FF089),
          UINT64_C(0x0000ED4DD7183518)}},
        {{UINT64_C(0x000D4A93DE96CDC1), UINT64_C(0x0001C832B89CB8A0),
          UINT64_C(0x000203277525319E), UINT64_C(0x0004CEE0F6725575),
          UINT64_C(0x00070ED69AF9BF53)},
         {UINT64_C(0x000C17F8BAA5AAC7), UINT64_C(0x0006AF4ABBDC969F),
          UINT64_C(0x0003A0E69B82101E), UINT64_C(0x0006234479D6A63E),
          UINT64_C(0x0002BF20728C2007)},
         {UINT64_C(0x00063FEF01AE2A02), UINT64_C(0x0001C86F09FC4AF4),
          UINT64_C(0x00069A1DE168EBCE), UINT64_C(0x00063F996EFB8433),
          UINT64_C(0x00072A687DA051BD)}},
        {{UINT64_C(0x000A8952F3FB5F0C), UINT64_C(0x00022194DC688FAE),
          UINT64_C(0x0006F9F5064736BF), UINT64_C(0x0007A6A4D691B9DB),
          UINT64_C(0x0007F5B139E354FB)},
         {UINT64_C(0x0000B63808606E6E), UINT64_C(0x0005E413F0FE0384),
          UINT64_C(0x00024FB74A712242), UINT64_C(0x00027B1CB73DDC68),
          UINT64_C(0x0006285935149BF9)},
         {UINT64_C(0x000D30A9D3AC7820), UINT64_C(0x0002B97ED3978DF1),
          UINT64_C(0x0003681BAB9F5289), UINT64_C(0x0000D844B826D276),
          UINT64_C(0x000784A758F5FDDE)}},
        {{UINT64_C(0x0005EF9827213EE3), UINT64_C(0x00042F84E4310A41),
          UINT64_C(0x0004A5143748EB06), UINT64_C(0x0003190974F9FF92),
          UINT64_C(0x0005016A42C2EB4B)},
         {UINT64_C(0x00000CB993776C25), UINT64_C(0x0005E9694BF91DF4),
          UINT64_C(0x00056BC66736D688), UINT64_C(0x0002EC931ADE934B),
          UINT64_C(0x0001A9C364E091BF)},
         {UINT64_C(0x000E211DC7632283), UINT64_C(0x00035BE411AEEFD3),
          UINT64_C(0x00015DAFEBCCAC46), UINT64_C(0x00037714AEB785FD),
          UINT64_C(0x0006253CC0A245FF)}},
        {{UINT64_C(0x00029AF81A0D1BD2), UINT64_C(0x00003B61C799BE14),
          UINT64_C(0x0001C03C6426B11A), UINT64_C(0x0007F386EC4C5C3C),
          UINT64_C(0x0000B2BC5FE81F9E)},
         {UINT64_C(0x000AD04EE82724D3), UINT64_C(0x0005B1D7846B04CA),
          UINT64_C(0x00033D35B52BC659), UINT64_C(0x000135A47CD22439),
          UINT64_C(0x0007D7B6CAEA433E)},
         {UINT64_C(0x000850D32772E3DB), UINT64_C(0x000582BED8E87C91),
          UINT64_C(0x000198367AE74473), UINT64_C(0x0002F5A5839CEAE9),
          UINT64_C(0x0002B8174D7564F2)}},
    },
    {
        {{UINT64_C(0x0005F71E4CC72DC5), UINT64_C(0x000729408117FF19),
          UINT64_C(0x000309B7A677423D), UINT64_C(0x0004839F84C2AB89),
          UINT64_C(0x00008A95BA320E5D)},
         {UINT64_C(0x00078965FDB1CB39), UINT64_C(0x0005F0C0D8298F25),
          UINT64_C(0x00055C4EEDD50E22), UINT64_C(0x00023DC216E90B73),
          UINT64_C(0x000797D03A50DC08)},
         {UINT64_C(0x000D4CA0AD220D40), UINT64_C(0x00028CD933850D3B),
          UINT64_C(0x0004D7EE3B9C333B), UINT64_C(0x0002966D4130006D),
          UINT64_C(0x0000FBD61D1E8F9C)}},
        {{UINT64_C(0x00009E516DAADA87), UINT64_C(0x0004EC925BC0EEEE),
          UINT64_C(0x0006EFFBA132BCEB), UINT64_C(0x000724D09DAD4807),
          UINT64_C(0x000452C79585B373)},
         {UINT64_C(0x000F1401F95C5FB5), UINT64_C(0x0005A1CE98BCB0CF),
          UINT64_C(0x000216304328D3D2), UINT64_C(0x0004BF1910725FC2),
          UINT64_C(0x0004002028E1F6C0)},
         {UINT64_C(0x000BCA6D33FB4AF5), UINT64_C(0x000699B08CFF3D9E),
          UINT64_C(0x0000EA0CDC3C332C), UINT64_C(0x000630F4F9EAC5C7),
          UINT64_C(0x000780B9E89D8FFA)}},
        {{UINT64_C(0x000CF75D097A1682), UINT64_C(0x00032545E3AC653A),
          UINT64_C(0x00029DB14F338894), UINT64_C(0x000351894CA83CAE),
          UINT64_C(0x000318CEC53A3C6A)},
         {UINT64_C(0x000F2E2D2A2EA810), UINT64_C(0x00054DDA2C9BB050),
          UINT64_C(0x000290BE850E8961), UINT64_C(0x000287BC5EBC0BFC),
          UINT64_C(0x00062D4620BB48F9)},
         {UINT64_C(0x0005A0F8E756C417), UINT64_C(0x0004937AF0050D8E),
          UINT64_C(0x0003DF98E8FBE4F8), UINT64_C(0x00073EF8BBA04BAC),
          UINT64_C(0x0003743A4A505CD5)}},
        {{UINT64_C(0x000AC304DDD850C1), UINT64_C(0x000116AE8E058E54),
          UINT64_C(0x0003E025C2CAAABC), UINT64_C(0x0006FC6FE28AC49A),
          UINT64_C(0x00016E67A015DEA5)},
         {UINT64_C(0x0000CF3F158429CD), UINT64_C(0x000480063B01884B),
          UINT64_C(0x000363368F078BE2), UINT64_C(0x000504AB7790BA1A),
          UINT64_C(0x0006710350B446FA)},
         {UINT64_C(0x000D8B8DE6397785), UINT64_C(0x000188CD6C441A62),
          UINT64_C(0x0004AD0920B22E3C), UINT64_C(0x0005021ECF3C72DE),
          UINT64_C(0x0003D133001D1ED5)}},
        {{UINT64_C(0x000B60A8C5FAF1CD), UINT64_C(0x0004C53C423A845B),
          UINT64_C(0x000520A9E9FA2A69), UINT64_C(0x00027D388A1F08D1),
          UINT64_C(0x000032396160EC47)},
         {UINT64_C(0x00038139094FAD75), UINT64_C(0x0003CD9876593CD5),
          UINT64_C(0x00071333878088E4), UINT64_C(0x000201E8433445E6),
          UINT64_C(0x000112302E7408FF)},
         {UINT64_C(0x000CBFE63FC06EE2), UINT64_C(0x00027425B1F20DEB),
          UINT64_C(0x000457B85B95B8B5), UINT64_C(0x00076DF4D98AEAFE),
          UINT64_C(0x0004EC884BB0909D)}},
        {{UINT64_C(0x000560BECBEEDE54), UINT64_C(0x000355F2A19E2A7B),
          UINT64_C(0x0003AC869895D894), UINT64_C(0x0001B3A88FAF6472),
          UINT64_C(0x00030150F70F46D2)},
         {UINT64_C(0x000C4423AB869B40), UINT64_C(0x0003A52F86375064),
          UINT64_C(0x00012F5BC0F20340), UINT64_C(0x0006CA222C7412D4),
          UINT64_C(0x00032F1254041880)},
         {UINT64_C(0x0002B2DBAB6499A6), UINT64_C(0x00011049F2328790),
          UINT64_C(0x000696584124F8E2), UINT64_C(0x000142AB6165955E),
          UINT64_C(0x00063E346DAEF2ED)}},
        {{UINT64_C(0x000D39355730C824), UINT64_C(0x0003FC8CAC7D5529),
          UINT64_C(0x00051C9605C77F3A), UINT64_C(0x0007A2A90B74D60C),
          UINT64_C(0x0006FA2B0304B184)},
         {UINT64_C(0x0004EB3441C3FCC1), UINT64_C(0x0002CACCC532F441),
          UINT64_C(0x0005E09ACF224276), UINT64_C(0x00058A77259116FC),
          UINT64_C(0x0000BDC2A63D5387)},
         {UINT64_C(0x000F8EFB7463FFE8), UINT64_C(0x0001057CA428A821),
          UINT64_C(0x0004ED380A470ED0), UINT64_C(0x00076F49D6CEF5F0),
          UINT64_C(0x0003F9D2D6743030)}},
        {{UINT64_C(0x0000AF30896CC3EF), UINT64_C(0x000201D4DF630DBA),
          UINT64_C(0x00052E42F5AFF8BF), UINT64_C(0x00000EB77C14ACF5),
          UINT64_C(0x0003CA2DFB8C5EDF)},
         {UINT64_C(0x000B5A205B171808), UINT64_C(0x0004C56363842D6C),
          UINT64_C(0x000292D70469611A), UINT64_C(0x0006D916356CD45A),
          UINT64_C(0x0006B0027CD19FAB)},
         {UINT64_C(0x00022B2713528A82), UINT64_C(0x0006E7BE33E7CEF0),
          UINT64_C(0x000009FC8141F131), UINT64_C(0x0000150A7927D149),
          UINT64_C(0x0003B14B0DA7FF44)}},
        {{UINT64_C(0x00032EFED0F84103), UINT64_C(0x00019069CDCE6F9F),
          UINT64_C(0x0005C6ACB2ABA640), UINT64_C(0x0006C2B81330E238),
          UINT64_C(0x0003A809BBA9B370)},
         {UINT64_C(0x0004BE6C8B688B18), UINT64_C(0x0001351C261FED04),
          UINT64_C(0x0002C96D1BC48370), UINT64_C(0x0001E95E38FF6352),
          UINT64_C(0x0007BEAD8D5751AA)},
         {UINT64_C(0x00098F021AA4DB09), UINT64_C(0x00078CD1DC9071DF),
          UINT64_C(0x000605F5C2A08E6A), UINT64_C(0x0002E7D15E83EE69),
          UINT64_C(0x0000E6B2FF6D9875)}},
        {{UINT64_C(0x0004DE5A07C4323E), UINT64_C(0x0002D406347B7064),
          UINT64_C(0x0005AA73A0846C15), UINT64_C(0x0000B5C4D6C73A87),
          UINT64_C(0x0002AA137FBF2494)},
         {UINT64_C(0x0004685341C33279), UINT64_C(0x000082B2B0E321E2),
          UINT64_C(0x0005643452806D72), UINT64_C(0x000626BA7990B3AF),
          UINT64_C(0x0007CF53B95B4365)},
         {UINT64_C(0x00013532346149FF), UINT64_C(0x0005947C275C07F1),
          UINT64_C(0x00034F1270EB410F), UINT64_C(0x000514A7C01BF670),
          UINT64_C(0x000407161B0257A8)}},
        {{UINT64_C(0x00054B5C52AC8719), UINT64_C(0x0002D86CBEB0D6B0),
          UINT64_C(0x0004F84A08F1266B), UINT64_C(0x0004D65F37B998EF),
          UINT64_C(0x0006C27CE40B4135)},
         {UINT64_C(0x0007BF812153FC77), UINT64_C(0x0005CDDD65D79B11),
          UINT64_C(0x00054A7071923FF6), UINT64_C(0x00034FBFFF7FA918),
          UINT64_C(0x000261A5361256A4)},
         {UINT64_C(0x000B13BCE19AAFB7), UINT64_C(0x0006D3B746CE4B8F),
          UINT64_C(0x00053A3635869292), UINT64_C(0x0007909536E7719B),
          UINT64_C(0x000008EB20D956CC)}},
        {{UINT64_C(0x00065CF97D492C5D), UINT64_C(0x00029D9BCEE03B91),
          UINT64_C(0x0006041F5A49C183), UINT64_C(0x0002F8117A3A828F),
          UINT64_C(0x000369E3DAD33DF2)},
         {UINT64_C(0x000D5176A425F79F), UINT64_C(0x000343D6AB0DE966),
          UINT64_C(0x00064EFB5C97C515), UINT64_C(0x0002BF9162998A39),
          UINT64_C(0x00050EF13FEEC9EB)},
         {UINT64_C(0x00039C0CFD97E85B), UINT64_C(0x000770E1CCBB013E),
          UINT64_C(0x0005F03BD9F7D9C2), UINT64_C(0x000690F1EE2F80E3),
          UINT64_C(0x00071787D1339A82)}},
        {{UINT64_C(0x000F7F42B80EB7F2), UINT64_C(0x000544C8293F8329),
          UINT64_C(0x000122DA47640B5D), UINT64_C(0x0000FEAFA087BC4E),
          UINT64_C(0x00059F0BB3B97004)},
         {UINT64_C(0x0002AA28745CD9B5), UINT64_C(0x0007BC8D5CD03005),
          UINT64_C(0x0001422C4B7A9189), UINT64_C(0x000282692E6090A7),
          UINT64_C(0x0000867B47D33593)},
         {UINT64_C(0x0003413E422FA5A2), UINT64_C(0x0006887164EBB90B),
          UINT64_C(0x00065C5E1BFDB4A4), UINT64_C(0x0004B98C578628EE),
          UINT64_C(0x0004E5352F622184)}},
        {{UINT64_C(0x0002549D88E38B2F), UINT64_C(0x0004908DEAD628D9),
          UINT64_C(0x000073E0A2E146D7), UINT64_C(0x000664C6FFF36209),
          UINT64_C(0x000130159F64A292)},
         {UINT64_C(0x000B964E8ECBEDBE), UINT64_C(0x00030E5C798B68B0),
          UINT64_C(0x0004BFD953930A67), UINT64_C(0x0007CA03ABB678FB),
          UINT64_C(0x00033FEF5B3D5B01)},
         {UINT64_C(0x00098E12CCF396A9), UINT64_C(0x0003A8CA995F3788),
          UINT64_C(0x0007482D4E5FAAC6), UINT64_C(0x00031D5A37583FCA),
          UINT64_C(0x0006D982702EDC91)}},
        {{UINT64_C(0x000BE837A054ACCB), UINT64_C(0x0004ECB51A8EF070),
          UINT64_C(0x0002F2D57B4FCF60), UINT64_C(0x0004C1513045FB2B),
          UINT64_C(0x00047770E3380A5B)},
         {UINT64_C(0x000425BEF82D311B), UINT64_C(0x00002005D68620DC),
          UINT64_C(0x0007325F0CD3FBE0), UINT64_C(0x0004EEFB9471E0EC),
          UINT64_C(0x00028F8C67917260)},
         {UINT64_C(0x000B0A66B88E76E6), UINT64_C(0x0000F3A2C9DA4F2E),
          UINT64_C(0x0001EA49E958D92A), UINT64_C(0x000026E1BDBB6E03),
          UINT64_C(0x0005029291E8FB70)}},
        {{UINT64_C(0x000E54C9AEAECCC3), UINT64_C(0x000361674AC1790E),
          UINT64_C(0x0006F9F28B87D9E7), UINT64_C(0x00018B8D2B452066),
          UINT64_C(0x0007DB9345D70FBC)},
         {UINT64_C(0x000F3DF99A78836C), UINT64_C(0x0004914D0D5AE148),
          UINT64_C(0x000571EBD41C1239), UINT64_C(0x0005A51875241CFB),
          UINT64_C(0x0007336AA1FEBF60)},
         {UINT64_C(0x000A212F131FA1A5), UINT64_C(0x0000D92E88DEBB89),
          UINT64_C(0x000558BFDAECA4D2), UINT64_C(0x000269A780A65DC8),
          UINT64_C(0x0007265543B97C9D)}},
    },
    {
        {{UINT64_C(0x0009BE522B9D7F04), UINT64_C(0x0004BC705622740B),
          UINT64_C(0x0007E15EA4E4D172), UINT64_C(0x00038BD4A6FDEDBC),
          UINT64_C(0x0005E8C142089478)},
         {UINT64_C(0x00048C72EDCC8AA4), UINT64_C(0x0006C5E6065406FA),
          UINT64_C(0x0006AF3701C24620), UINT64_C(0x00042EA0F2E70B8F),
          UINT64_C(0x00033DFF74481155)},
         {UINT64_C(0x000852E6A6E72D7C), UINT64_C(0x00042B699FEF3BF1),
          UINT64_C(0x0005907EE2406991), UINT64_C(0x0004D36621FDF916),
          UINT64_C(0x0006D7911C011B7B)}},
        {{UINT64_C(0x000EBFE9B94F7A12), UINT64_C(0x0004824A85A9AC4B),
          UINT64_C(0x00014EEF07D3D55D), UINT64_C(0x000513B829B2AE5C),
          UINT64_C(0x0006011380B7D9CC)},
         {UINT64_C(0x000A21168961EEA8), UINT64_C(0x00063CBB9BA57577),
          UINT64_C(0x0006606B05EF2134), UINT64_C(0x0006B72A1D536E54),
          UINT64_C(0x000661849203E9CC)},
         {UINT64_C(0x0004C06869AEA3D6), UINT64_C(0x00021F578255BAF7),
          UINT64_C(0x00067D1096E12882), UINT64_C(0x00050B150BD416A4),
          UINT64_C(0x0002910322AC87CD)}},
        {{UINT64_C(0x000644D63DB19259), UINT64_C(0x000547C0FCA86C59),
          UINT64_C(0x0007A198D5D3FAEE), UINT64_C(0x0001E28699860365),
          UINT64_C(0x0000EFE6732ABA42)},
         {UINT64_C(0x000888204C12F590), UINT64_C(0x0001C8E5F2BFF85C),
          UINT64_C(0x0001772AF32270BC), UINT64_C(0x0005D962EAAB3158),
          UINT64_C(0x0001A34AB97418FB)},
         {UINT64_C(0x0008D80BBC8528CC), UINT64_C(0x000241D41B76D92E),
          UINT64_C(0x0004513FE5B63EE3), UINT64_C(0x00062D1B5DE35CE4),
          UINT64_C(0x0005157C5E57C598)}},
        {{UINT64_C(0x000BDB25BE3A9FD2), UINT64_C(0x000302ED2946793F),
          UINT64_C(0x00012C506EF80331), UINT64_C(0x0006D13E5DECA318),
          UINT64_C(0x00001A5DF0E20F25)},
         {UINT64_C(0x000BC9FC9FF75DF0), UINT64_C(0x0000BF1C056F5479),
          UINT64_C(0x00036E0E95E2CF70), UINT64_C(0x00058A068DA69D3D),
          UINT64_C(0x0001555FC948F50D)},
         {UINT64_C(0x000998A0950C1A1C), UINT64_C(0x0005358898A6636E),
          UINT64_C(0x0001CC47B1220D25), UINT64_C(0x0007E929E197B7F1),
          UINT64_C(0x0006FE5D94BC2454)}},
        {{UINT64_C(0x000EABC2EB1D0B59), UINT64_C(0x0007A05C59336123),
          UINT64_C(0x0007918FF8448B64), UINT64_C(0x00057A94A01CAB48),
          UINT64_C(0x0002FF15B4CC7487)},
         {UINT64_C(0x00008799019AECD9), UINT64_C(0x0003D8F58AC75312),
          UINT64_C(0x0000D4A7DF2730C1), UINT64_C(0x0005E75D83B3FAC3),
          UINT64_C(0x0007180E7E60D802)},
         {UINT64_C(0x0005AEF92672CCE1), UINT64_C(0x0006637504959CA3),
          UINT64_C(0x00053E5589BFA337), UINT64_C(0x0003A21A8478ED35),
          UINT64_C(0x0002DD36E5AC50F9)}},
        {{UINT64_C(0x00062DF8E260C004), UINT64_C(0x0004DE30E71B999A),
          UINT64_C(0x00045C3FA066284D), UINT64_C(0x000331D9BB25623D),
          UINT64_C(0x000202123595AEC6)},
         {UINT64_C(0x00047A9FF0131346), UINT64_C(0x0003A150D3AAC3F1),
          UINT64_C(0x00029452C6C88616), UINT64_C(0x0007CA8D3EC1A917),
          UINT64_C(0x0006EEC72C9A0BF5)},
         {UINT64_C(0x000F10845A21F448), UINT64_C(0x00001AAE742835C2),
          UINT64_C(0x00025E4CFB83A17F), UINT64_C(0x0007BF8EB92AF3AB),
          UINT64_C(0x000699BE9783E779)}},
        {{UINT64_C(0x0001DC9C52A66972), UINT64_C(0x0002C0B904C59FBA),
          UINT64_C(0x0006C89144084C6D), UINT64_C(0x00014AB65019CF29),
          UINT64_C(0x00006EA2401AC315)},
         {UINT64_C(0x000ADC23F3836291), UINT64_C(0x0002F1AD8C01F7C1),
          UINT64_C(0x000205DF0C8922CF), UINT64_C(0x0001751FC5AEB310),
          UINT64_C(0x0006F64DB4075476)},
         {UINT64_C(0x000F82F426A80AE1), UINT64_C(0x00013C4A6E62FC16),
          UINT64_C(0x0001FBCDF27E0B34), UINT64_C(0x00078E30B91EF3E6),
          UINT64_C(0x000059CBEA565341)}},
        {{UINT64_C(0x00062CB978126A51), UINT64_C(0x0004E2E7313211FE),
          UINT64_C(0x00066E6E57EED552), UINT64_C(0x00009AE384794AC9),
          UINT64_C(0x00077726F8AB9C55)},
         {UINT64_C(0x0006D6C6C9E72C58), UINT64_C(0x00069723C749EA4F),
          UINT64_C(0x0005631BCF116547), UINT64_C(0x0004E31FAB9D03A6),
          UINT64_C(0x0002B7F538841EE2)},
         {UINT64_C(0x000BD957A2809D3C), UINT64_C(0x000317F1DC4351FD),
          UINT64_C(0x000701BA3438D796), UINT64_C(0x00009FBD6563EC5D),
          UINT64_C(0x0003E98ABE5B2F47)}},
        {{UINT64_C(0x000AE4190E276A10), UINT64_C(0x0001FFB966B8121D),
          UINT64_C(0x0004FEEDA4D78B7E), UINT64_C(0x0005DB20D5551162),
          UINT64_C(0x00030DC06EF26E4D)},
         {UINT64_C(0x000A970255208159), UINT64_C(0x0001A104788B4AEF),
          UINT64_C(0x00004D4C840BE3BE), UINT64_C(0x0006BD8A71949A6C),
          UINT64_C(0x000651C9FF191728)},
         {UINT64_C(0x000A44663267D2A4), UINT64_C(0x0001FC0CAB11E883),
          UINT64_C(0x000785A12DE63089), UINT64_C(0x00006EC9E0189600),
          UINT64_C(0x000375BF7E8AB1B8)}},
        {{UINT64_C(0x000BFFBBC3B4CE41), UINT64_C(0x0003376A2003A823),
          UINT64_C(0x000091EBF7910590), UINT64_C(0x00061FB8363DCA05),
          UINT64_C(0x00042807BC01AD4E)},
         {UINT64_C(0x000ACF6D9A5C7646), UINT64_C(0x000560F4B3AE3DC2),
          UINT64_C(0x000183CCAE76BD95), UINT64_C(0x00065764DB513046),
          UINT64_C(0x000214F42294E850)},
         {UINT64_C(0x0000F3EDC763536B), UINT64_C(0x0004E9D5EF6B046E),
          UINT64_C(0x000150D442C37994), UINT64_C(0x0001B54E80DE1472),
          UINT64_C(0x00007A1D71EDF00C)}},
        {{UINT64_C(0x00087407DBA8E90B), UINT64_C(0x0007E1C66322205F),
          UINT64_C(0x0004FAC245ACCD04), UINT64_C(0x0002B13B4E297D86),
          UINT64_C(0x000323B2D23AEDAE)},
         {UINT64_C(0x0009941B92E19A5C), UINT64_C(0x000195BC242E04DB),
          UINT64_C(0x0007CBD2DF69FF04), UINT64_C(0x0007CA419CC6C1BA),
          UINT64_C(0x0002904A1B8DDF4F)},
         {UINT64_C(0x00040957907BF307), UINT64_C(0x00015C68151C4FE7),
          UINT64_C(0x0003DD3F01FB8E0F), UINT64_C(0x0003D31C808FC101),
          UINT64_C(0x00066F1F09C544BB)}},
        {{UINT64_C(0x0003F8A6A6C266C9), UINT64_C(0x0001000A75D2D728),
          UINT64_C(0x000149100C338128), UINT64_C(0x00048CB6ED6D33B4),
          UINT64_C(0x00069B7380105C27)},
         {UINT64_C(0x000BBCA015BD1302), UINT64_C(0x000453E91EF883C4),
          UINT64_C(0x000087A2C55564F3), UINT64_C(0x00028661DD26634D),
          UINT64_C(0x0000504737350EB4)},
         {UINT64_C(0x0003AE59A5B3D5E2), UINT64_C(0x0001545256AA2218),
          UINT64_C(0x00064A0782A0BF22), UINT64_C(0x000365507C34B4AA),
          UINT64_C(0x00032799B2146183)}},
        {{UINT64_C(0x0003D6DBFB2DFB8B), UINT64_C(0x00022DF9277F3ECF),
          UINT64_C(0x0000C42749A48685), UINT64_C(0x0001B0E7B30B31B5),
          UINT64_C(0x00075EBF4439768D)},
         {UINT64_C(0x00039F41CE757E5C), UINT64_C(0x0007F8600191731B),
          UINT64_C(0x0006B2813E3967F5), UINT64_C(0x00015D1CE8E34A7E),
          UINT64_C(0x00072FBC1F6F13D8)},
         {UINT64_C(0x0004F6CF2E028861), UINT64_C(0x00020F869603ABDE),
          UINT64_C(0x00042A4AA3F9DAAB), UINT64_C(0x0004A8C0AC2A2950),
          UINT64_C(0x0004B1809E50B53C)}},
        {{UINT64_C(0x00062DDA4C355837), UINT64_C(0x0002F17314E1EE8D),
          UINT64_C(0x00078EBCB6E2323E), UINT64_C(0x0004543455D081FD),
          UINT64_C(0x000791C9DA3066C1)},
         {UINT64_C(0x0007AEA83D07CA0E), UINT64_C(0x000601D3C4BBCA6C),
          UINT64_C(0x000438ECD2EB6B9E), UINT64_C(0x0005FE33D87D61C1),
          UINT64_C(0x0002C2CD6F6EF8D2)},
         {UINT64_C(0x0002DAEC4B332C81), UINT64_C(0x000600000F256A01),
          UINT64_C(0x000647117D50194B), UINT64_C(0x0002A7D5456F092C),
          UINT64_C(0x000398A204AC0606)}},
        {{UINT64_C(0x00076419370EC4A0), UINT64_C(0x00012369939759B2),
          UINT64_C(0x00023B2960FD0EE0), UINT64_C(0x0007AF17DB515E9B),
          UINT64_C(0x0002CC3165DDFC91)},
         {UINT64_C(0x0005EE1C6281CBB1), UINT64_C(0x00060699623D7D65),
          UINT64_C(0x0000F3D85C23D284), UINT64_C(0x000018587E7669D1),
          UINT64_C(0x000227AAF5D77845)},
         {UINT64_C(0x000153114ADB9FC3), UINT64_C(0x000164F7102A9765),
          UINT64_C(0x0006E7834D58BEE3), UINT64_C(0x0006F111F5E5ACB0),
          UINT64_C(0x000162CF7ADDFFCA)}},
        {{UINT64_C(0x00055D7C37A14EAC), UINT64_C(0x0005223EE335640C),
          UINT64_C(0x000291B30C58F170), UINT64_C(0x00023A4EB047D54F),
          UINT64_C(0x0002C3DDA0AF63CB)},
         {UINT64_C(0x000C4C6EF153A948), UINT64_C(0x00004C4867916BA5),
          UINT64_C(0x00035BE98811B8E0), UINT64_C(0x0004F3937F720308),
          UINT64_C(0x00041AC616B68B67)},
         {UINT64_C(0x000D47D0742C184D), UINT64_C(0x00063D7733EDA001),
          UINT64_C(0x000609767EC0E926), UINT64_C(0x0006C3F5D59B36D0),
          UINT64_C(0x0004B14DD7938C89)}},
    }};

/*-
 * Q := 2P, both projective, Q and P same pointers OK
 * Autogenerated: op3/dbl_proj_ed_eone.op3
 * https://www.hyperelliptic.org/EFD/g1p/auto-code/twisted/extended-1/doubling/dbl-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_double(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    /* set pointers for Edwards curve arith */
    const limb_t *X = P->X;
    const limb_t *Y = P->Y;
    const limb_t *Z = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t0, X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t1, Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t2, Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, t2, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X, Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(Y3, X3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, Y3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, t0, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t2, Y3, t3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t3, t0, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, Z3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, Z3, t3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, t2, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y3, t3);
}

/*-
 * R := Q + P where R and Q are projective, P affine.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_mixed_ed_eone.op3
 * https://hyperelliptic.org/EFD/g1p/auto-code/twisted/extended/addition/madd-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_add_mixed(pt_prj_t *R, const pt_prj_t *Q, const pt_aff_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    const limb_t *d = const_d;
    /* set pointers for Edwards curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *T1 = Q->T;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *T2 = P->T;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *T3 = R->T;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, X1, X2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t1, Y1, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, d, T2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, T1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, X2, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t3, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, Z1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, Z1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t1, t1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Z3, t3);
}

/*-
 * R := Q + P all projective.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_proj_ed_eone.op3
 * https://hyperelliptic.org/EFD/g1p/auto-code/twisted/extended/addition/add-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_add_proj(pt_prj_t *R, const pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    const limb_t *d = const_d;
    /* set pointers for Edwards curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *T1 = Q->T;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *T2 = P->T;
    const limb_t *Z2 = P->Z;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *T3 = R->T;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, X1, X2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t1, Y1, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, d, T2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, T1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t3, Z1, Z2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, X2, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, Z3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, t3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, t3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t1, t1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Z3, t3);
}

/*-
 * from P projective Edwards to Q projective legacy: Q=P OK
 * Autogenerated: op3/edwards2legacy_gost.op3
 * https://tools.ietf.org/html/rfc7836#section-5.2
 */
static void point_edwards2legacy(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0;
    /* constants */
    const limb_t *S = const_S;
    const limb_t *T = const_T;
    const limb_t *X1 = P->X;
    const limb_t *Y1 = P->Y;
    const limb_t *Z1 = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(T3, Z1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t0, Z1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, S, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Z1, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, X1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, t0, T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t0, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, X1, t0);
}

/*-
 * from P affine legacy to Q projective Edwards: Q=P not OK
 * Autogenerated: op3/legacy2edwards_gost.op3
 * https://tools.ietf.org/html/rfc7836#section-5.2
 */
static void point_legacy2edwards(pt_prj_t *Q, const pt_aff_t *P) {
    /* constants */
    const limb_t *S = const_S;
    const limb_t *T = const_T;
    const limb_t *X1 = P->X;
    const limb_t *Y1 = P->Y;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, X1, T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, T3, S);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Y1, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, T3, S);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y1, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, X3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(Z3, Z3);
}

/* constants */
#define RADIX 5
#define DRADIX (1 << RADIX)
#define DRADIX_WNAF ((DRADIX) << 1)

/*-
 * precomp for wnaf scalar multiplication:
 * precomp[0] = 1P
 * precomp[1] = 3P
 * precomp[2] = 5P
 * precomp[3] = 7P
 * precomp[4] = 9P
 * ...
 */
static void precomp_wnaf(pt_prj_t precomp[DRADIX / 2], const pt_aff_t *P) {
    int i;

    /* move from legacy affine to Edwards projective */
    point_legacy2edwards(&precomp[0], P);
    point_double(&precomp[DRADIX / 2 - 1], &precomp[0]);

    for (i = 1; i < DRADIX / 2; i++)
        point_add_proj(&precomp[i], &precomp[DRADIX / 2 - 1], &precomp[i - 1]);
}

/* fetch a scalar bit */
static int scalar_get_bit(const unsigned char in[32], int idx) {
    int widx, rshift;

    widx = idx >> 3;
    rshift = idx & 0x7;

    if (idx < 0 || widx >= 32) return 0;

    return (in[widx] >> rshift) & 0x1;
}

/*-
 * Compute "regular" wnaf representation of a scalar.
 * See "Exponent Recoding and Regular Exponentiation Algorithms",
 * Tunstall et al., AfricaCrypt 2009, Alg 6.
 * It forces an odd scalar and outputs digits in
 * {\pm 1, \pm 3, \pm 5, \pm 7, \pm 9, ...}
 * i.e. signed odd digits with _no zeroes_ -- that makes it "regular".
 */
static void scalar_rwnaf(int8_t out[52], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = (in[0] & (DRADIX_WNAF - 1)) | 1;
    for (i = 0; i < 51; i++) {
        d = (window & (DRADIX_WNAF - 1)) - DRADIX;
        out[i] = d;
        window = (window - d) >> RADIX;
        window += scalar_get_bit(in, (i + 1) * RADIX + 1) << 1;
        window += scalar_get_bit(in, (i + 1) * RADIX + 2) << 2;
        window += scalar_get_bit(in, (i + 1) * RADIX + 3) << 3;
        window += scalar_get_bit(in, (i + 1) * RADIX + 4) << 4;
        window += scalar_get_bit(in, (i + 1) * RADIX + 5) << 5;
    }
    out[i] = window;
}

/*-
 * Compute "textbook" wnaf representation of a scalar.
 * NB: not constant time
 */
static void scalar_wnaf(int8_t out[257], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = in[0] & (DRADIX_WNAF - 1);
    for (i = 0; i < 257; i++) {
        d = 0;
        if ((window & 1) && ((d = window & (DRADIX_WNAF - 1)) & DRADIX))
            d -= DRADIX_WNAF;
        out[i] = d;
        window = (window - d) >> 1;
        window += scalar_get_bit(in, i + 1 + RADIX) << RADIX;
    }
}

/*-
 * Simultaneous scalar multiplication: interleaved "textbook" wnaf.
 * NB: not constant time
 */
static void var_smul_wnaf_two(pt_aff_t *out, const unsigned char a[32],
                              const unsigned char b[32], const pt_aff_t *P) {
    int i, d, is_neg, is_inf = 1, flipped = 0;
    int8_t anaf[257] = {0};
    int8_t bnaf[257] = {0};
    pt_prj_t Q = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_wnaf(anaf, a);
    scalar_wnaf(bnaf, b);

    for (i = 256; i >= 0; i--) {
        if (!is_inf) point_double(&Q, &Q);
        if ((d = bnaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &precomp[d].X);
                fe_copy(Q.Y, &precomp[d].Y);
                fe_copy(Q.T, &precomp[d].T);
                fe_copy(Q.Z, &precomp[d].Z);
                is_inf = 0;
            } else
                point_add_proj(&Q, &Q, &precomp[d]);
        }
        if ((d = anaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &lut_cmb[0][d].X);
                fe_copy(Q.Y, &lut_cmb[0][d].Y);
                fe_copy(Q.T, &lut_cmb[0][d].T);
                fe_copy(Q.Z, const_one);
                is_inf = 0;
            } else
                point_add_mixed(&Q, &Q, &lut_cmb[0][d]);
        }
    }

    if (is_inf) {
        /* initialize accumulator to inf: all-zero scalars */
        fe_set_zero(Q.X);
        fe_copy(Q.Y, const_one);
        fe_set_zero(Q.T);
        fe_copy(Q.Z, const_one);
    }

    if (flipped) {
        /* correct sign */
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
    }

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Variable point scalar multiplication with "regular" wnaf.
 */
static void var_smul_rwnaf(pt_aff_t *out, const unsigned char scalar[32],
                           const pt_aff_t *P) {
    int i, j, d, diff, is_neg;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, lut = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_rwnaf(rnaf, scalar);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    /* initialize accumulator to high digit */
    d = (rnaf[51] - 1) >> 1;
    for (j = 0; j < DRADIX / 2; j++) {
        diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, diff, Q.X,
                                                            precomp[j].X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, diff, Q.Y,
                                                            precomp[j].Y);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, diff, Q.T,
                                                            precomp[j].T);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, diff, Q.Z,
                                                            precomp[j].Z);
    }

    for (i = 50; i >= 0; i--) {
        for (j = 0; j < RADIX; j++) point_double(&Q, &Q);
        d = rnaf[i];
        /* is_neg = (d < 0) ? 1 : 0 */
        is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
        /* d = abs(d) */
        d = (d ^ -is_neg) + is_neg;
        d = (d - 1) >> 1;
        for (j = 0; j < DRADIX / 2; j++) {
            diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.X, diff, lut.X, precomp[j].X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.Y, diff, lut.Y, precomp[j].Y);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.T, diff, lut.T, precomp[j].T);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.Z, diff, lut.Z, precomp[j].Z);
        }
        /* negate lut point if digit is negative */
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->X, lut.X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->T, lut.T);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.X, is_neg,
                                                            lut.X, out->X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.T, is_neg,
                                                            lut.T, out->T);
        point_add_proj(&Q, &Q, &lut);
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.X, precomp[0].X);
    fe_copy(lut.Y, precomp[0].Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.T, precomp[0].T);
    fe_copy(lut.Z, precomp[0].Z);
    point_add_proj(&lut, &lut, &Q);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, scalar[0] & 1,
                                                        lut.X, Q.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, scalar[0] & 1,
                                                        lut.Y, Q.Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, scalar[0] & 1,
                                                        lut.T, Q.T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, scalar[0] & 1,
                                                        lut.Z, Q.Z);

    point_double(&Q, &Q);
    point_double(&Q, &Q);

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Fixed scalar multiplication: comb with interleaving.
 */
static void fixed_smul_cmb(pt_aff_t *out, const unsigned char scalar[32]) {
    int i, j, k, d, diff, is_neg = 0;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, R = {0};
    pt_aff_t lut = {0};

    scalar_rwnaf(rnaf, scalar);

    /* initalize accumulator to inf */
    fe_set_zero(Q.X);
    fe_copy(Q.Y, const_one);
    fe_set_zero(Q.T);
    fe_copy(Q.Z, const_one);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    for (i = 3; i >= 0; i--) {
        for (j = 0; i != 3 && j < RADIX; j++) point_double(&Q, &Q);
        for (j = 0; j < 14; j++) {
            if (j * 4 + i > 51) continue;
            d = rnaf[j * 4 + i];
            /* is_neg = (d < 0) ? 1 : 0 */
            is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
            /* d = abs(d) */
            d = (d ^ -is_neg) + is_neg;
            d = (d - 1) >> 1;
            for (k = 0; k < DRADIX / 2; k++) {
                diff = (1 - (-(d ^ k) >> (8 * sizeof(int) - 1))) & 1;
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.X, diff, lut.X, lut_cmb[j][k].X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.Y, diff, lut.Y, lut_cmb[j][k].Y);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.T, diff, lut.T, lut_cmb[j][k].T);
            }
            /* negate lut point if digit is negative */
            fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->X, lut.X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->T, lut.T);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.X, is_neg,
                                                                lut.X, out->X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.T, is_neg,
                                                                lut.T, out->T);
            point_add_mixed(&Q, &Q, &lut);
        }
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.X, lut_cmb[0][0].X);
    fe_copy(lut.Y, lut_cmb[0][0].Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.T, lut_cmb[0][0].T);
    point_add_mixed(&R, &Q, &lut);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, scalar[0] & 1, R.X,
                                                        Q.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, scalar[0] & 1, R.Y,
                                                        Q.Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, scalar[0] & 1, R.T,
                                                        Q.T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, scalar[0] & 1, R.Z,
                                                        Q.Z);

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Wrapper: simultaneous scalar mutiplication.
 * outx, outy := a * G + b * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul_two(unsigned char outx[32], unsigned char outy[32],
                          const unsigned char a[32], const unsigned char b[32],
                          const unsigned char inx[32],
                          const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.X, inx);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.Y, iny);
    /* simultaneous scalar multiplication */
    var_smul_wnaf_two(&P, a, b, &P);

    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: fixed scalar mutiplication.
 * outx, outy := scalar * G
 * Everything is LE byte ordering.
 */
static void point_mul_g(unsigned char outx[32], unsigned char outy[32],
                        const unsigned char scalar[32]) {
    pt_aff_t P;

    /* fixed scmul function */
    fixed_smul_cmb(&P, scalar);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: variable point scalar mutiplication.
 * outx, outy := scalar * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul(unsigned char outx[32], unsigned char outy[32],
                      const unsigned char scalar[32],
                      const unsigned char inx[32],
                      const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.X, inx);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.Y, iny);
    /* var scmul function */
    var_smul_rwnaf(&P, scalar, &P);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}


#include <openssl/ec.h>

/* the zero field element */
static const unsigned char const_zb[32] = {0};

/*-
 * An OpenSSL wrapper for simultaneous scalar multiplication.
 * r := n * G + m * q
 */
    int
    point_mul_two_id_tc26_gost_3410_2012_256_paramSetA(
        const EC_GROUP *group, EC_POINT *r, const BIGNUM *n, const EC_POINT *q,
        const BIGNUM *m, BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(n, b_n, 32) != 32 || BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the simultaneous scalar multiplication */
    point_mul_two(b_x, b_y, b_n, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for variable point scalar multiplication.
 * r := m * q
 */
    int
    point_mul_id_tc26_gost_3410_2012_256_paramSetA(const EC_GROUP *group,
                                                   EC_POINT *r,
                                                   const EC_POINT *q,
                                                   const BIGNUM *m,
                                                   BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the variable scalar multiplication */
    point_mul(b_x, b_y, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for fixed scalar multiplication.
 * r := n * G
 */
    int
    point_mul_g_id_tc26_gost_3410_2012_256_paramSetA(const EC_GROUP *group,
                                                     EC_POINT *r,
                                                     const BIGNUM *n,
                                                     BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL || BN_bn2lebinpad(n, b_n, 32) != 32)
        goto err;
    /* do the fixed scalar multiplication */
    point_mul_g(b_x, b_y, b_n);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}



#else /* __SIZEOF_INT128__ */

#include <stdint.h>
#include <string.h>
#define LIMB_BITS 32
#define LIMB_CNT 11
/* Field elements */
typedef uint32_t fe_t[LIMB_CNT];
typedef uint32_t limb_t;

#ifdef OPENSSL_NO_ASM
#define FIAT_ID_TC26_GOST_3410_2012_256_PARAMSETA_NO_ASM
#endif

#define fe_copy(d, s) memcpy(d, s, sizeof(fe_t))
#define fe_set_zero(d) memset(d, 0, sizeof(fe_t))

#define fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(c, a, b) \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_add(c, a, b);          \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(c, c)
#define fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(c, a, b) \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_sub(c, a, b);          \
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(c, c)

/* Projective points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t T;
    fe_t Z;
} pt_prj_t;

/* Affine points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t T;
} pt_aff_t;

/* BEGIN verbatim fiat code https://github.com/mit-plv/fiat-crypto */
/*-
 * MIT License
 *
 * Copyright (c) 2020 the fiat-crypto authors (see the AUTHORS file)
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/* Autogenerated: unsaturated_solinas --static --use-value-barrier id_tc26_gost_3410_2012_256_paramSetA 32 '(auto)' '2^256 - 617' */
/* curve description: id_tc26_gost_3410_2012_256_paramSetA */
/* machine_wordsize = 32 (from "32") */
/* requested operations: (all) */
/* n = 11 (from "(auto)") */
/* s-c = 2^256 - [(1, 617)] (from "2^256 - 617") */
/* tight_bounds_multiplier = 1 (from "") */
/*  */
/* Computed values: */
/* carry_chain = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, 1] */
/* eval z = z[0] + (z[1] << 24) + (z[2] << 47) + (z[3] << 70) + (z[4] << 94) + (z[5] << 117) + (z[6] << 140) + (z[7] << 163) + (z[8] << 187) + (z[9] << 210) + (z[10] << 233) */
/* bytes_eval z = z[0] + (z[1] << 8) + (z[2] << 16) + (z[3] << 24) + (z[4] << 32) + (z[5] << 40) + (z[6] << 48) + (z[7] << 56) + (z[8] << 64) + (z[9] << 72) + (z[10] << 80) + (z[11] << 88) + (z[12] << 96) + (z[13] << 104) + (z[14] << 112) + (z[15] << 120) + (z[16] << 128) + (z[17] << 136) + (z[18] << 144) + (z[19] << 152) + (z[20] << 160) + (z[21] << 168) + (z[22] << 176) + (z[23] << 184) + (z[24] << 192) + (z[25] << 200) + (z[26] << 208) + (z[27] << 216) + (z[28] << 224) + (z[29] << 232) + (z[30] << 240) + (z[31] << 248) */
/* balance = [0x1fffb2e, 0xfffffe, 0xfffffe, 0x1fffffe, 0xfffffe, 0xfffffe, 0xfffffe, 0x1fffffe, 0xfffffe, 0xfffffe, 0xfffffe] */

#include <stdint.h>
typedef unsigned char fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1;
typedef signed char fiat_id_tc26_gost_3410_2012_256_paramSetA_int1;

#if (-1 & 3) != 3
#error "This code only works on a two's complement system"
#endif

#if !defined(FIAT_ID_TC26_GOST_3410_2012_256_PARAMSETA_NO_ASM) && \
    (defined(__GNUC__) || defined(__clang__))
static __inline__ uint32_t
fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u32(uint32_t a) {
    __asm__("" : "+r"(a) : /* no inputs */);
    return a;
}
#else
#define fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u32(x) (x)
#endif

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u24 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^24
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^24⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffff]
 *   arg3: [0x0 ~> 0xffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u24(
    uint32_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    uint32_t x1;
    uint32_t x2;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT32_C(0xffffff));
    x3 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x1 >> 24);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u24 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^24
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^24⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffff]
 *   arg3: [0x0 ~> 0xffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u24(
    uint32_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    int32_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_int1 x2;
    uint32_t x3;
    x1 = ((int32_t)(arg2 - arg1) - (int32_t)arg3);
    x2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(x1 >> 24);
    x3 = (x1 & UINT32_C(0xffffff));
    *out1 = x3;
    *out2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^23
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^23⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7fffff]
 *   arg3: [0x0 ~> 0x7fffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7fffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
    uint32_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    uint32_t x1;
    uint32_t x2;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT32_C(0x7fffff));
    x3 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x1 >> 23);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^23
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^23⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7fffff]
 *   arg3: [0x0 ~> 0x7fffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7fffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
    uint32_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 *out2,
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    int32_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_int1 x2;
    uint32_t x3;
    x1 = ((int32_t)(arg2 - arg1) - (int32_t)arg3);
    x2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(x1 >> 23);
    x3 = (x1 & UINT32_C(0x7fffff));
    *out1 = x3;
    *out2 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32 is a single-word conditional move.
 * Postconditions:
 *   out1 = (if arg1 = 0 then arg2 else arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffff]
 *   arg3: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(
    uint32_t *out1, fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1,
    uint32_t arg2, uint32_t arg3) {
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x1;
    uint32_t x2;
    uint32_t x3;
    x1 = (!(!arg1));
    x2 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_int1)(0x0 - x1) &
          UINT32_C(0xffffffff));
    x3 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u32(x2) &
           arg3) |
          (fiat_id_tc26_gost_3410_2012_256_paramSetA_value_barrier_u32((~x2)) &
           arg2));
    *out1 = x3;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul multiplies two field elements and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 *   arg2: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    uint64_t x13;
    uint64_t x14;
    uint64_t x15;
    uint64_t x16;
    uint64_t x17;
    uint64_t x18;
    uint64_t x19;
    uint64_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint64_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint64_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint64_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint64_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    uint64_t x72;
    uint64_t x73;
    uint64_t x74;
    uint64_t x75;
    uint64_t x76;
    uint64_t x77;
    uint64_t x78;
    uint64_t x79;
    uint64_t x80;
    uint64_t x81;
    uint64_t x82;
    uint64_t x83;
    uint64_t x84;
    uint64_t x85;
    uint64_t x86;
    uint64_t x87;
    uint64_t x88;
    uint64_t x89;
    uint64_t x90;
    uint64_t x91;
    uint64_t x92;
    uint64_t x93;
    uint64_t x94;
    uint64_t x95;
    uint64_t x96;
    uint64_t x97;
    uint64_t x98;
    uint64_t x99;
    uint64_t x100;
    uint64_t x101;
    uint64_t x102;
    uint64_t x103;
    uint64_t x104;
    uint64_t x105;
    uint64_t x106;
    uint64_t x107;
    uint64_t x108;
    uint64_t x109;
    uint64_t x110;
    uint64_t x111;
    uint64_t x112;
    uint64_t x113;
    uint64_t x114;
    uint64_t x115;
    uint64_t x116;
    uint64_t x117;
    uint64_t x118;
    uint64_t x119;
    uint64_t x120;
    uint64_t x121;
    uint64_t x122;
    uint64_t x123;
    uint32_t x124;
    uint64_t x125;
    uint64_t x126;
    uint64_t x127;
    uint64_t x128;
    uint64_t x129;
    uint64_t x130;
    uint64_t x131;
    uint64_t x132;
    uint64_t x133;
    uint64_t x134;
    uint64_t x135;
    uint64_t x136;
    uint32_t x137;
    uint64_t x138;
    uint64_t x139;
    uint32_t x140;
    uint64_t x141;
    uint64_t x142;
    uint32_t x143;
    uint64_t x144;
    uint64_t x145;
    uint32_t x146;
    uint64_t x147;
    uint64_t x148;
    uint32_t x149;
    uint64_t x150;
    uint64_t x151;
    uint32_t x152;
    uint64_t x153;
    uint64_t x154;
    uint32_t x155;
    uint64_t x156;
    uint64_t x157;
    uint32_t x158;
    uint64_t x159;
    uint64_t x160;
    uint32_t x161;
    uint64_t x162;
    uint32_t x163;
    uint32_t x164;
    uint64_t x165;
    uint64_t x166;
    uint32_t x167;
    uint32_t x168;
    uint32_t x169;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x170;
    uint32_t x171;
    uint32_t x172;
    x1 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[10])));
    x2 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[9])));
    x3 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[8])) * 0x2));
    x4 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[7])));
    x5 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[6])));
    x6 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[5])));
    x7 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[4])) * 0x2));
    x8 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[3])));
    x9 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[2])));
    x10 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[1])) * 0x2));
    x11 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[10])));
    x12 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[9])) * 0x2));
    x13 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[8])) * 0x2));
    x14 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[7])));
    x15 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[6])));
    x16 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[5])) * 0x2));
    x17 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[4])) * 0x2));
    x18 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[3])));
    x19 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[2])) * 0x2));
    x20 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[10])) * 0x2));
    x21 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[9])) * 0x2));
    x22 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[8])) * 0x2));
    x23 = (UINT16_C(0x269) * ((uint64_t)(arg1[8]) * (arg2[7])));
    x24 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[6])) * 0x2));
    x25 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[5])) * 0x2));
    x26 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[4])) * 0x2));
    x27 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[3])) * 0x2));
    x28 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[10])));
    x29 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[9])));
    x30 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[8])));
    x31 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[7])));
    x32 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[6])));
    x33 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[5])));
    x34 = (UINT16_C(0x269) * (((uint64_t)(arg1[7]) * (arg2[4])) * 0x2));
    x35 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[10])));
    x36 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[9])));
    x37 = (UINT16_C(0x269) * (((uint64_t)(arg1[6]) * (arg2[8])) * 0x2));
    x38 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[7])));
    x39 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[6])));
    x40 = (UINT16_C(0x269) * (((uint64_t)(arg1[6]) * (arg2[5])) * 0x2));
    x41 = (UINT16_C(0x269) * ((uint64_t)(arg1[5]) * (arg2[10])));
    x42 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[9])) * 0x2));
    x43 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[8])) * 0x2));
    x44 = (UINT16_C(0x269) * ((uint64_t)(arg1[5]) * (arg2[7])));
    x45 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[6])) * 0x2));
    x46 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[10])) * 0x2));
    x47 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[9])) * 0x2));
    x48 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[8])) * 0x2));
    x49 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[7])) * 0x2));
    x50 = (UINT16_C(0x269) * ((uint64_t)(arg1[3]) * (arg2[10])));
    x51 = (UINT16_C(0x269) * ((uint64_t)(arg1[3]) * (arg2[9])));
    x52 = (UINT16_C(0x269) * (((uint64_t)(arg1[3]) * (arg2[8])) * 0x2));
    x53 = (UINT16_C(0x269) * ((uint64_t)(arg1[2]) * (arg2[10])));
    x54 = (UINT16_C(0x269) * (((uint64_t)(arg1[2]) * (arg2[9])) * 0x2));
    x55 = (UINT16_C(0x269) * (((uint64_t)(arg1[1]) * (arg2[10])) * 0x2));
    x56 = ((uint64_t)(arg1[10]) * (arg2[0]));
    x57 = ((uint64_t)(arg1[9]) * ((arg2[1]) * 0x2));
    x58 = ((uint64_t)(arg1[9]) * (arg2[0]));
    x59 = ((uint64_t)(arg1[8]) * ((arg2[2]) * 0x2));
    x60 = ((uint64_t)(arg1[8]) * ((arg2[1]) * 0x2));
    x61 = ((uint64_t)(arg1[8]) * (arg2[0]));
    x62 = ((uint64_t)(arg1[7]) * (arg2[3]));
    x63 = ((uint64_t)(arg1[7]) * (arg2[2]));
    x64 = ((uint64_t)(arg1[7]) * (arg2[1]));
    x65 = ((uint64_t)(arg1[7]) * (arg2[0]));
    x66 = ((uint64_t)(arg1[6]) * ((arg2[4]) * 0x2));
    x67 = ((uint64_t)(arg1[6]) * (arg2[3]));
    x68 = ((uint64_t)(arg1[6]) * (arg2[2]));
    x69 = ((uint64_t)(arg1[6]) * ((arg2[1]) * 0x2));
    x70 = ((uint64_t)(arg1[6]) * (arg2[0]));
    x71 = ((uint64_t)(arg1[5]) * ((arg2[5]) * 0x2));
    x72 = ((uint64_t)(arg1[5]) * ((arg2[4]) * 0x2));
    x73 = ((uint64_t)(arg1[5]) * (arg2[3]));
    x74 = ((uint64_t)(arg1[5]) * ((arg2[2]) * 0x2));
    x75 = ((uint64_t)(arg1[5]) * ((arg2[1]) * 0x2));
    x76 = ((uint64_t)(arg1[5]) * (arg2[0]));
    x77 = ((uint64_t)(arg1[4]) * ((arg2[6]) * 0x2));
    x78 = ((uint64_t)(arg1[4]) * ((arg2[5]) * 0x2));
    x79 = ((uint64_t)(arg1[4]) * ((arg2[4]) * 0x2));
    x80 = ((uint64_t)(arg1[4]) * ((arg2[3]) * 0x2));
    x81 = ((uint64_t)(arg1[4]) * ((arg2[2]) * 0x2));
    x82 = ((uint64_t)(arg1[4]) * ((arg2[1]) * 0x2));
    x83 = ((uint64_t)(arg1[4]) * (arg2[0]));
    x84 = ((uint64_t)(arg1[3]) * (arg2[7]));
    x85 = ((uint64_t)(arg1[3]) * (arg2[6]));
    x86 = ((uint64_t)(arg1[3]) * (arg2[5]));
    x87 = ((uint64_t)(arg1[3]) * ((arg2[4]) * 0x2));
    x88 = ((uint64_t)(arg1[3]) * (arg2[3]));
    x89 = ((uint64_t)(arg1[3]) * (arg2[2]));
    x90 = ((uint64_t)(arg1[3]) * (arg2[1]));
    x91 = ((uint64_t)(arg1[3]) * (arg2[0]));
    x92 = ((uint64_t)(arg1[2]) * ((arg2[8]) * 0x2));
    x93 = ((uint64_t)(arg1[2]) * (arg2[7]));
    x94 = ((uint64_t)(arg1[2]) * (arg2[6]));
    x95 = ((uint64_t)(arg1[2]) * ((arg2[5]) * 0x2));
    x96 = ((uint64_t)(arg1[2]) * ((arg2[4]) * 0x2));
    x97 = ((uint64_t)(arg1[2]) * (arg2[3]));
    x98 = ((uint64_t)(arg1[2]) * (arg2[2]));
    x99 = ((uint64_t)(arg1[2]) * ((arg2[1]) * 0x2));
    x100 = ((uint64_t)(arg1[2]) * (arg2[0]));
    x101 = ((uint64_t)(arg1[1]) * ((arg2[9]) * 0x2));
    x102 = ((uint64_t)(arg1[1]) * ((arg2[8]) * 0x2));
    x103 = ((uint64_t)(arg1[1]) * (arg2[7]));
    x104 = ((uint64_t)(arg1[1]) * ((arg2[6]) * 0x2));
    x105 = ((uint64_t)(arg1[1]) * ((arg2[5]) * 0x2));
    x106 = ((uint64_t)(arg1[1]) * ((arg2[4]) * 0x2));
    x107 = ((uint64_t)(arg1[1]) * (arg2[3]));
    x108 = ((uint64_t)(arg1[1]) * ((arg2[2]) * 0x2));
    x109 = ((uint64_t)(arg1[1]) * ((arg2[1]) * 0x2));
    x110 = ((uint64_t)(arg1[1]) * (arg2[0]));
    x111 = ((uint64_t)(arg1[0]) * (arg2[10]));
    x112 = ((uint64_t)(arg1[0]) * (arg2[9]));
    x113 = ((uint64_t)(arg1[0]) * (arg2[8]));
    x114 = ((uint64_t)(arg1[0]) * (arg2[7]));
    x115 = ((uint64_t)(arg1[0]) * (arg2[6]));
    x116 = ((uint64_t)(arg1[0]) * (arg2[5]));
    x117 = ((uint64_t)(arg1[0]) * (arg2[4]));
    x118 = ((uint64_t)(arg1[0]) * (arg2[3]));
    x119 = ((uint64_t)(arg1[0]) * (arg2[2]));
    x120 = ((uint64_t)(arg1[0]) * (arg2[1]));
    x121 = ((uint64_t)(arg1[0]) * (arg2[0]));
    x122 =
        (x121 +
         (x55 +
          (x54 + (x52 + (x49 + (x45 + (x40 + (x34 + (x27 + (x19 + x10))))))))));
    x123 = (x122 >> 24);
    x124 = (uint32_t)(x122 & UINT32_C(0xffffff));
    x125 =
        (x111 +
         (x101 +
          (x92 + (x84 + (x77 + (x71 + (x66 + (x62 + (x59 + (x57 + x56))))))))));
    x126 =
        (x112 +
         (x102 +
          (x93 + (x85 + (x78 + (x72 + (x67 + (x63 + (x60 + (x58 + x1))))))))));
    x127 =
        (x113 +
         (x103 +
          (x94 + (x86 + (x79 + (x73 + (x68 + (x64 + (x61 + (x11 + x2))))))))));
    x128 =
        (x114 +
         (x104 +
          (x95 + (x87 + (x80 + (x74 + (x69 + (x65 + (x20 + (x12 + x3))))))))));
    x129 =
        (x115 +
         (x105 +
          (x96 + (x88 + (x81 + (x75 + (x70 + (x28 + (x21 + (x13 + x4))))))))));
    x130 =
        (x116 +
         (x106 +
          (x97 + (x89 + (x82 + (x76 + (x35 + (x29 + (x22 + (x14 + x5))))))))));
    x131 =
        (x117 +
         (x107 +
          (x98 + (x90 + (x83 + (x41 + (x36 + (x30 + (x23 + (x15 + x6))))))))));
    x132 =
        (x118 +
         (x108 +
          (x99 + (x91 + (x46 + (x42 + (x37 + (x31 + (x24 + (x16 + x7))))))))));
    x133 =
        (x119 +
         (x109 +
          (x100 + (x50 + (x47 + (x43 + (x38 + (x32 + (x25 + (x17 + x8))))))))));
    x134 =
        (x120 +
         (x110 +
          (x53 + (x51 + (x48 + (x44 + (x39 + (x33 + (x26 + (x18 + x9))))))))));
    x135 = (x123 + x134);
    x136 = (x135 >> 23);
    x137 = (uint32_t)(x135 & UINT32_C(0x7fffff));
    x138 = (x136 + x133);
    x139 = (x138 >> 23);
    x140 = (uint32_t)(x138 & UINT32_C(0x7fffff));
    x141 = (x139 + x132);
    x142 = (x141 >> 24);
    x143 = (uint32_t)(x141 & UINT32_C(0xffffff));
    x144 = (x142 + x131);
    x145 = (x144 >> 23);
    x146 = (uint32_t)(x144 & UINT32_C(0x7fffff));
    x147 = (x145 + x130);
    x148 = (x147 >> 23);
    x149 = (uint32_t)(x147 & UINT32_C(0x7fffff));
    x150 = (x148 + x129);
    x151 = (x150 >> 23);
    x152 = (uint32_t)(x150 & UINT32_C(0x7fffff));
    x153 = (x151 + x128);
    x154 = (x153 >> 24);
    x155 = (uint32_t)(x153 & UINT32_C(0xffffff));
    x156 = (x154 + x127);
    x157 = (x156 >> 23);
    x158 = (uint32_t)(x156 & UINT32_C(0x7fffff));
    x159 = (x157 + x126);
    x160 = (x159 >> 23);
    x161 = (uint32_t)(x159 & UINT32_C(0x7fffff));
    x162 = (x160 + x125);
    x163 = (uint32_t)(x162 >> 23);
    x164 = (uint32_t)(x162 & UINT32_C(0x7fffff));
    x165 = ((uint64_t)UINT16_C(0x269) * x163);
    x166 = (x124 + x165);
    x167 = (uint32_t)(x166 >> 24);
    x168 = (uint32_t)(x166 & UINT32_C(0xffffff));
    x169 = (x167 + x137);
    x170 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x169 >> 23);
    x171 = (x169 & UINT32_C(0x7fffff));
    x172 = (x170 + x140);
    out1[0] = x168;
    out1[1] = x171;
    out1[2] = x172;
    out1[3] = x143;
    out1[4] = x146;
    out1[5] = x149;
    out1[6] = x152;
    out1[7] = x155;
    out1[8] = x158;
    out1[9] = x161;
    out1[10] = x164;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square squares a field element and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg1) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint64_t x1;
    uint64_t x2;
    uint32_t x3;
    uint64_t x4;
    uint64_t x5;
    uint32_t x6;
    uint64_t x7;
    uint64_t x8;
    uint32_t x9;
    uint64_t x10;
    uint64_t x11;
    uint32_t x12;
    uint64_t x13;
    uint64_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint64_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint64_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint64_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint64_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    uint64_t x72;
    uint64_t x73;
    uint64_t x74;
    uint64_t x75;
    uint64_t x76;
    uint64_t x77;
    uint64_t x78;
    uint64_t x79;
    uint64_t x80;
    uint64_t x81;
    uint64_t x82;
    uint64_t x83;
    uint64_t x84;
    uint64_t x85;
    uint64_t x86;
    uint64_t x87;
    uint64_t x88;
    uint32_t x89;
    uint64_t x90;
    uint64_t x91;
    uint64_t x92;
    uint64_t x93;
    uint64_t x94;
    uint64_t x95;
    uint64_t x96;
    uint64_t x97;
    uint64_t x98;
    uint64_t x99;
    uint64_t x100;
    uint64_t x101;
    uint32_t x102;
    uint64_t x103;
    uint64_t x104;
    uint32_t x105;
    uint64_t x106;
    uint64_t x107;
    uint32_t x108;
    uint64_t x109;
    uint64_t x110;
    uint32_t x111;
    uint64_t x112;
    uint64_t x113;
    uint32_t x114;
    uint64_t x115;
    uint64_t x116;
    uint32_t x117;
    uint64_t x118;
    uint64_t x119;
    uint32_t x120;
    uint64_t x121;
    uint64_t x122;
    uint32_t x123;
    uint64_t x124;
    uint64_t x125;
    uint32_t x126;
    uint64_t x127;
    uint32_t x128;
    uint32_t x129;
    uint64_t x130;
    uint64_t x131;
    uint32_t x132;
    uint32_t x133;
    uint32_t x134;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x135;
    uint32_t x136;
    uint32_t x137;
    x1 = ((uint64_t)(arg1[10]) * UINT16_C(0x269));
    x2 = (x1 * 0x2);
    x3 = ((arg1[10]) * 0x2);
    x4 = ((uint64_t)(arg1[9]) * UINT16_C(0x269));
    x5 = (x4 * 0x2);
    x6 = ((arg1[9]) * 0x2);
    x7 = ((uint64_t)(arg1[8]) * UINT16_C(0x269));
    x8 = (x7 * 0x2);
    x9 = ((arg1[8]) * 0x2);
    x10 = ((uint64_t)(arg1[7]) * UINT16_C(0x269));
    x11 = (x10 * 0x2);
    x12 = ((arg1[7]) * 0x2);
    x13 = ((uint64_t)(arg1[6]) * UINT16_C(0x269));
    x14 = (x13 * 0x2);
    x15 = ((arg1[6]) * 0x2);
    x16 = ((arg1[5]) * 0x2);
    x17 = ((arg1[4]) * 0x2);
    x18 = ((arg1[3]) * 0x2);
    x19 = ((arg1[2]) * 0x2);
    x20 = ((arg1[1]) * 0x2);
    x21 = ((arg1[10]) * x1);
    x22 = ((arg1[9]) * x2);
    x23 = ((arg1[9]) * (x4 * 0x2));
    x24 = ((arg1[8]) * (x2 * 0x2));
    x25 = ((arg1[8]) * (x5 * 0x2));
    x26 = ((arg1[8]) * (x7 * 0x2));
    x27 = ((arg1[7]) * x2);
    x28 = ((arg1[7]) * x5);
    x29 = ((arg1[7]) * x8);
    x30 = ((arg1[7]) * x10);
    x31 = ((arg1[6]) * x2);
    x32 = ((arg1[6]) * x5);
    x33 = ((arg1[6]) * (x8 * 0x2));
    x34 = ((arg1[6]) * x11);
    x35 = ((arg1[6]) * x13);
    x36 = ((arg1[5]) * x2);
    x37 = ((arg1[5]) * (x5 * 0x2));
    x38 = ((arg1[5]) * (x8 * 0x2));
    x39 = ((arg1[5]) * x11);
    x40 = ((arg1[5]) * (x14 * 0x2));
    x41 = ((uint64_t)(arg1[5]) * ((arg1[5]) * 0x2));
    x42 = ((arg1[4]) * (x2 * 0x2));
    x43 = ((arg1[4]) * (x5 * 0x2));
    x44 = ((arg1[4]) * (x8 * 0x2));
    x45 = ((arg1[4]) * (x11 * 0x2));
    x46 = ((uint64_t)(arg1[4]) * (x15 * 0x2));
    x47 = ((uint64_t)(arg1[4]) * (x16 * 0x2));
    x48 = ((uint64_t)(arg1[4]) * ((arg1[4]) * 0x2));
    x49 = ((arg1[3]) * x2);
    x50 = ((arg1[3]) * x5);
    x51 = ((arg1[3]) * (x8 * 0x2));
    x52 = ((uint64_t)(arg1[3]) * x12);
    x53 = ((uint64_t)(arg1[3]) * x15);
    x54 = ((uint64_t)(arg1[3]) * x16);
    x55 = ((uint64_t)(arg1[3]) * (x17 * 0x2));
    x56 = ((uint64_t)(arg1[3]) * (arg1[3]));
    x57 = ((arg1[2]) * x2);
    x58 = ((arg1[2]) * (x5 * 0x2));
    x59 = ((uint64_t)(arg1[2]) * (x9 * 0x2));
    x60 = ((uint64_t)(arg1[2]) * x12);
    x61 = ((uint64_t)(arg1[2]) * x15);
    x62 = ((uint64_t)(arg1[2]) * (x16 * 0x2));
    x63 = ((uint64_t)(arg1[2]) * (x17 * 0x2));
    x64 = ((uint64_t)(arg1[2]) * x18);
    x65 = ((uint64_t)(arg1[2]) * (arg1[2]));
    x66 = ((arg1[1]) * (x2 * 0x2));
    x67 = ((uint64_t)(arg1[1]) * (x6 * 0x2));
    x68 = ((uint64_t)(arg1[1]) * (x9 * 0x2));
    x69 = ((uint64_t)(arg1[1]) * x12);
    x70 = ((uint64_t)(arg1[1]) * (x15 * 0x2));
    x71 = ((uint64_t)(arg1[1]) * (x16 * 0x2));
    x72 = ((uint64_t)(arg1[1]) * (x17 * 0x2));
    x73 = ((uint64_t)(arg1[1]) * x18);
    x74 = ((uint64_t)(arg1[1]) * (x19 * 0x2));
    x75 = ((uint64_t)(arg1[1]) * ((arg1[1]) * 0x2));
    x76 = ((uint64_t)(arg1[0]) * x3);
    x77 = ((uint64_t)(arg1[0]) * x6);
    x78 = ((uint64_t)(arg1[0]) * x9);
    x79 = ((uint64_t)(arg1[0]) * x12);
    x80 = ((uint64_t)(arg1[0]) * x15);
    x81 = ((uint64_t)(arg1[0]) * x16);
    x82 = ((uint64_t)(arg1[0]) * x17);
    x83 = ((uint64_t)(arg1[0]) * x18);
    x84 = ((uint64_t)(arg1[0]) * x19);
    x85 = ((uint64_t)(arg1[0]) * x20);
    x86 = ((uint64_t)(arg1[0]) * (arg1[0]));
    x87 = (x86 + (x66 + (x58 + (x51 + (x45 + x40)))));
    x88 = (x87 >> 24);
    x89 = (uint32_t)(x87 & UINT32_C(0xffffff));
    x90 = (x76 + (x67 + (x59 + (x52 + (x46 + x41)))));
    x91 = (x77 + (x68 + (x60 + (x53 + (x47 + x21)))));
    x92 = (x78 + (x69 + (x61 + (x54 + (x48 + x22)))));
    x93 = (x79 + (x70 + (x62 + (x55 + (x24 + x23)))));
    x94 = (x80 + (x71 + (x63 + (x56 + (x27 + x25)))));
    x95 = (x81 + (x72 + (x64 + (x31 + (x28 + x26)))));
    x96 = (x82 + (x73 + (x65 + (x36 + (x32 + x29)))));
    x97 = (x83 + (x74 + (x42 + (x37 + (x33 + x30)))));
    x98 = (x84 + (x75 + (x49 + (x43 + (x38 + x34)))));
    x99 = (x85 + (x57 + (x50 + (x44 + (x39 + x35)))));
    x100 = (x88 + x99);
    x101 = (x100 >> 23);
    x102 = (uint32_t)(x100 & UINT32_C(0x7fffff));
    x103 = (x101 + x98);
    x104 = (x103 >> 23);
    x105 = (uint32_t)(x103 & UINT32_C(0x7fffff));
    x106 = (x104 + x97);
    x107 = (x106 >> 24);
    x108 = (uint32_t)(x106 & UINT32_C(0xffffff));
    x109 = (x107 + x96);
    x110 = (x109 >> 23);
    x111 = (uint32_t)(x109 & UINT32_C(0x7fffff));
    x112 = (x110 + x95);
    x113 = (x112 >> 23);
    x114 = (uint32_t)(x112 & UINT32_C(0x7fffff));
    x115 = (x113 + x94);
    x116 = (x115 >> 23);
    x117 = (uint32_t)(x115 & UINT32_C(0x7fffff));
    x118 = (x116 + x93);
    x119 = (x118 >> 24);
    x120 = (uint32_t)(x118 & UINT32_C(0xffffff));
    x121 = (x119 + x92);
    x122 = (x121 >> 23);
    x123 = (uint32_t)(x121 & UINT32_C(0x7fffff));
    x124 = (x122 + x91);
    x125 = (x124 >> 23);
    x126 = (uint32_t)(x124 & UINT32_C(0x7fffff));
    x127 = (x125 + x90);
    x128 = (uint32_t)(x127 >> 23);
    x129 = (uint32_t)(x127 & UINT32_C(0x7fffff));
    x130 = ((uint64_t)UINT16_C(0x269) * x128);
    x131 = (x89 + x130);
    x132 = (uint32_t)(x131 >> 24);
    x133 = (uint32_t)(x131 & UINT32_C(0xffffff));
    x134 = (x132 + x102);
    x135 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x134 >> 23);
    x136 = (x134 & UINT32_C(0x7fffff));
    x137 = (x135 + x105);
    out1[0] = x133;
    out1[1] = x136;
    out1[2] = x137;
    out1[3] = x108;
    out1[4] = x111;
    out1[5] = x114;
    out1[6] = x117;
    out1[7] = x120;
    out1[8] = x123;
    out1[9] = x126;
    out1[10] = x129;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_carry reduces a field element.
 * Postconditions:
 *   eval out1 mod m = eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_carry(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    uint32_t x12;
    uint32_t x13;
    uint32_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint32_t x21;
    uint32_t x22;
    uint32_t x23;
    uint32_t x24;
    x1 = (arg1[0]);
    x2 = ((x1 >> 24) + (arg1[1]));
    x3 = ((x2 >> 23) + (arg1[2]));
    x4 = ((x3 >> 23) + (arg1[3]));
    x5 = ((x4 >> 24) + (arg1[4]));
    x6 = ((x5 >> 23) + (arg1[5]));
    x7 = ((x6 >> 23) + (arg1[6]));
    x8 = ((x7 >> 23) + (arg1[7]));
    x9 = ((x8 >> 24) + (arg1[8]));
    x10 = ((x9 >> 23) + (arg1[9]));
    x11 = ((x10 >> 23) + (arg1[10]));
    x12 = ((x1 & UINT32_C(0xffffff)) + (UINT16_C(0x269) * (x11 >> 23)));
    x13 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x12 >> 24) +
           (x2 & UINT32_C(0x7fffff)));
    x14 = (x12 & UINT32_C(0xffffff));
    x15 = (x13 & UINT32_C(0x7fffff));
    x16 = ((fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x13 >> 23) +
           (x3 & UINT32_C(0x7fffff)));
    x17 = (x4 & UINT32_C(0xffffff));
    x18 = (x5 & UINT32_C(0x7fffff));
    x19 = (x6 & UINT32_C(0x7fffff));
    x20 = (x7 & UINT32_C(0x7fffff));
    x21 = (x8 & UINT32_C(0xffffff));
    x22 = (x9 & UINT32_C(0x7fffff));
    x23 = (x10 & UINT32_C(0x7fffff));
    x24 = (x11 & UINT32_C(0x7fffff));
    out1[0] = x14;
    out1[1] = x15;
    out1[2] = x16;
    out1[3] = x17;
    out1[4] = x18;
    out1[5] = x19;
    out1[6] = x20;
    out1[7] = x21;
    out1[8] = x22;
    out1[9] = x23;
    out1[10] = x24;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_add adds two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 + eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 *   arg2: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_add(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = ((arg1[0]) + (arg2[0]));
    x2 = ((arg1[1]) + (arg2[1]));
    x3 = ((arg1[2]) + (arg2[2]));
    x4 = ((arg1[3]) + (arg2[3]));
    x5 = ((arg1[4]) + (arg2[4]));
    x6 = ((arg1[5]) + (arg2[5]));
    x7 = ((arg1[6]) + (arg2[6]));
    x8 = ((arg1[7]) + (arg2[7]));
    x9 = ((arg1[8]) + (arg2[8]));
    x10 = ((arg1[9]) + (arg2[9]));
    x11 = ((arg1[10]) + (arg2[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_sub subtracts two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 - eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 *   arg2: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_sub(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = ((UINT32_C(0x1fffb2e) + (arg1[0])) - (arg2[0]));
    x2 = ((UINT32_C(0xfffffe) + (arg1[1])) - (arg2[1]));
    x3 = ((UINT32_C(0xfffffe) + (arg1[2])) - (arg2[2]));
    x4 = ((UINT32_C(0x1fffffe) + (arg1[3])) - (arg2[3]));
    x5 = ((UINT32_C(0xfffffe) + (arg1[4])) - (arg2[4]));
    x6 = ((UINT32_C(0xfffffe) + (arg1[5])) - (arg2[5]));
    x7 = ((UINT32_C(0xfffffe) + (arg1[6])) - (arg2[6]));
    x8 = ((UINT32_C(0x1fffffe) + (arg1[7])) - (arg2[7]));
    x9 = ((UINT32_C(0xfffffe) + (arg1[8])) - (arg2[8]));
    x10 = ((UINT32_C(0xfffffe) + (arg1[9])) - (arg2[9]));
    x11 = ((UINT32_C(0xfffffe) + (arg1[10])) - (arg2[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_opp negates a field element.
 * Postconditions:
 *   eval out1 mod m = -eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = (UINT32_C(0x1fffb2e) - (arg1[0]));
    x2 = (UINT32_C(0xfffffe) - (arg1[1]));
    x3 = (UINT32_C(0xfffffe) - (arg1[2]));
    x4 = (UINT32_C(0x1fffffe) - (arg1[3]));
    x5 = (UINT32_C(0xfffffe) - (arg1[4]));
    x6 = (UINT32_C(0xfffffe) - (arg1[5]));
    x7 = (UINT32_C(0xfffffe) - (arg1[6]));
    x8 = (UINT32_C(0x1fffffe) - (arg1[7]));
    x9 = (UINT32_C(0xfffffe) - (arg1[8]));
    x10 = (UINT32_C(0xfffffe) - (arg1[9]));
    x11 = (UINT32_C(0xfffffe) - (arg1[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz is a multi-limb conditional select.
 * Postconditions:
 *   eval out1 = (if arg1 = 0 then eval arg2 else eval arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg3: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
    uint32_t out1[11], fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 arg1,
    const uint32_t arg2[11], const uint32_t arg3[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x1, arg1, (arg2[0]),
                                                          (arg3[0]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x2, arg1, (arg2[1]),
                                                          (arg3[1]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x3, arg1, (arg2[2]),
                                                          (arg3[2]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x4, arg1, (arg2[3]),
                                                          (arg3[3]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x5, arg1, (arg2[4]),
                                                          (arg3[4]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x6, arg1, (arg2[5]),
                                                          (arg3[5]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x7, arg1, (arg2[6]),
                                                          (arg3[6]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x8, arg1, (arg2[7]),
                                                          (arg3[7]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x9, arg1, (arg2[8]),
                                                          (arg3[8]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x10, arg1, (arg2[9]),
                                                          (arg3[9]));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(
        &x11, arg1, (arg2[10]), (arg3[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes serializes a field element to bytes in little-endian order.
 * Postconditions:
 *   out1 = map (λ x, ⌊((eval arg1 mod m) mod 2^(8 * (x + 1))) / 2^(8 * x)⌋) [0..31]
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(
    uint8_t out1[32], const uint32_t arg1[11]) {
    uint32_t x1;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x2;
    uint32_t x3;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x4;
    uint32_t x5;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x6;
    uint32_t x7;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x8;
    uint32_t x9;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x10;
    uint32_t x11;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x12;
    uint32_t x13;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x14;
    uint32_t x15;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x16;
    uint32_t x17;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x18;
    uint32_t x19;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x20;
    uint32_t x21;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x22;
    uint32_t x23;
    uint32_t x24;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x25;
    uint32_t x26;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x27;
    uint32_t x28;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x29;
    uint32_t x30;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x31;
    uint32_t x32;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x33;
    uint32_t x34;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x35;
    uint32_t x36;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x37;
    uint32_t x38;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x39;
    uint32_t x40;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x41;
    uint32_t x42;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x43;
    uint32_t x44;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x45;
    uint32_t x46;
    uint32_t x47;
    uint32_t x48;
    uint32_t x49;
    uint32_t x50;
    uint32_t x51;
    uint32_t x52;
    uint32_t x53;
    uint32_t x54;
    uint8_t x55;
    uint32_t x56;
    uint8_t x57;
    uint8_t x58;
    uint8_t x59;
    uint32_t x60;
    uint8_t x61;
    uint8_t x62;
    uint32_t x63;
    uint8_t x64;
    uint32_t x65;
    uint8_t x66;
    uint32_t x67;
    uint8_t x68;
    uint8_t x69;
    uint32_t x70;
    uint8_t x71;
    uint32_t x72;
    uint8_t x73;
    uint32_t x74;
    uint8_t x75;
    uint8_t x76;
    uint32_t x77;
    uint8_t x78;
    uint32_t x79;
    uint8_t x80;
    uint32_t x81;
    uint8_t x82;
    uint8_t x83;
    uint32_t x84;
    uint8_t x85;
    uint32_t x86;
    uint8_t x87;
    uint32_t x88;
    uint8_t x89;
    uint8_t x90;
    uint32_t x91;
    uint8_t x92;
    uint32_t x93;
    uint8_t x94;
    uint32_t x95;
    uint8_t x96;
    uint8_t x97;
    uint32_t x98;
    uint8_t x99;
    uint32_t x100;
    uint8_t x101;
    uint32_t x102;
    uint8_t x103;
    uint8_t x104;
    uint32_t x105;
    uint8_t x106;
    uint32_t x107;
    uint8_t x108;
    uint32_t x109;
    uint8_t x110;
    uint8_t x111;
    uint32_t x112;
    uint8_t x113;
    uint32_t x114;
    uint8_t x115;
    uint32_t x116;
    uint8_t x117;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x118;
    uint32_t x119;
    uint8_t x120;
    uint32_t x121;
    uint8_t x122;
    uint8_t x123;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u24(
        &x1, &x2, 0x0, (arg1[0]), UINT32_C(0xfffd97));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x3, &x4, x2, (arg1[1]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x5, &x6, x4, (arg1[2]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u24(
        &x7, &x8, x6, (arg1[3]), UINT32_C(0xffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x9, &x10, x8, (arg1[4]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x11, &x12, x10, (arg1[5]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x13, &x14, x12, (arg1[6]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u24(
        &x15, &x16, x14, (arg1[7]), UINT32_C(0xffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x17, &x18, x16, (arg1[8]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x19, &x20, x18, (arg1[9]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_subborrowx_u23(
        &x21, &x22, x20, (arg1[10]), UINT32_C(0x7fffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_cmovznz_u32(&x23, x22, 0x0,
                                                          UINT32_C(0xffffffff));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u24(
        &x24, &x25, 0x0, x1, (x23 & UINT32_C(0xfffd97)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x26, &x27, x25, x3, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x28, &x29, x27, x5, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u24(
        &x30, &x31, x29, x7, (x23 & UINT32_C(0xffffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x32, &x33, x31, x9, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x34, &x35, x33, x11, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x36, &x37, x35, x13, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u24(
        &x38, &x39, x37, x15, (x23 & UINT32_C(0xffffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x40, &x41, x39, x17, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x42, &x43, x41, x19, (x23 & UINT32_C(0x7fffff)));
    fiat_id_tc26_gost_3410_2012_256_paramSetA_addcarryx_u23(
        &x44, &x45, x43, x21, (x23 & UINT32_C(0x7fffff)));
    x46 = (x44 * (uint32_t)0x2);
    x47 = (x42 << 2);
    x48 = (x40 << 3);
    x49 = (x38 << 3);
    x50 = (x36 << 4);
    x51 = (x34 << 5);
    x52 = (x32 << 6);
    x53 = (x30 << 6);
    x54 = (x28 << 7);
    x55 = (uint8_t)(x24 & UINT8_C(0xff));
    x56 = (x24 >> 8);
    x57 = (uint8_t)(x56 & UINT8_C(0xff));
    x58 = (uint8_t)(x56 >> 8);
    x59 = (uint8_t)(x26 & UINT8_C(0xff));
    x60 = (x26 >> 8);
    x61 = (uint8_t)(x60 & UINT8_C(0xff));
    x62 = (uint8_t)(x60 >> 8);
    x63 = (x54 + (uint32_t)x62);
    x64 = (uint8_t)(x63 & UINT8_C(0xff));
    x65 = (x63 >> 8);
    x66 = (uint8_t)(x65 & UINT8_C(0xff));
    x67 = (x65 >> 8);
    x68 = (uint8_t)(x67 & UINT8_C(0xff));
    x69 = (uint8_t)(x67 >> 8);
    x70 = (x53 + (uint32_t)x69);
    x71 = (uint8_t)(x70 & UINT8_C(0xff));
    x72 = (x70 >> 8);
    x73 = (uint8_t)(x72 & UINT8_C(0xff));
    x74 = (x72 >> 8);
    x75 = (uint8_t)(x74 & UINT8_C(0xff));
    x76 = (uint8_t)(x74 >> 8);
    x77 = (x52 + (uint32_t)x76);
    x78 = (uint8_t)(x77 & UINT8_C(0xff));
    x79 = (x77 >> 8);
    x80 = (uint8_t)(x79 & UINT8_C(0xff));
    x81 = (x79 >> 8);
    x82 = (uint8_t)(x81 & UINT8_C(0xff));
    x83 = (uint8_t)(x81 >> 8);
    x84 = (x51 + (uint32_t)x83);
    x85 = (uint8_t)(x84 & UINT8_C(0xff));
    x86 = (x84 >> 8);
    x87 = (uint8_t)(x86 & UINT8_C(0xff));
    x88 = (x86 >> 8);
    x89 = (uint8_t)(x88 & UINT8_C(0xff));
    x90 = (uint8_t)(x88 >> 8);
    x91 = (x50 + (uint32_t)x90);
    x92 = (uint8_t)(x91 & UINT8_C(0xff));
    x93 = (x91 >> 8);
    x94 = (uint8_t)(x93 & UINT8_C(0xff));
    x95 = (x93 >> 8);
    x96 = (uint8_t)(x95 & UINT8_C(0xff));
    x97 = (uint8_t)(x95 >> 8);
    x98 = (x49 + (uint32_t)x97);
    x99 = (uint8_t)(x98 & UINT8_C(0xff));
    x100 = (x98 >> 8);
    x101 = (uint8_t)(x100 & UINT8_C(0xff));
    x102 = (x100 >> 8);
    x103 = (uint8_t)(x102 & UINT8_C(0xff));
    x104 = (uint8_t)(x102 >> 8);
    x105 = (x48 + (uint32_t)x104);
    x106 = (uint8_t)(x105 & UINT8_C(0xff));
    x107 = (x105 >> 8);
    x108 = (uint8_t)(x107 & UINT8_C(0xff));
    x109 = (x107 >> 8);
    x110 = (uint8_t)(x109 & UINT8_C(0xff));
    x111 = (uint8_t)(x109 >> 8);
    x112 = (x47 + (uint32_t)x111);
    x113 = (uint8_t)(x112 & UINT8_C(0xff));
    x114 = (x112 >> 8);
    x115 = (uint8_t)(x114 & UINT8_C(0xff));
    x116 = (x114 >> 8);
    x117 = (uint8_t)(x116 & UINT8_C(0xff));
    x118 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x116 >> 8);
    x119 = (x46 + (uint32_t)x118);
    x120 = (uint8_t)(x119 & UINT8_C(0xff));
    x121 = (x119 >> 8);
    x122 = (uint8_t)(x121 & UINT8_C(0xff));
    x123 = (uint8_t)(x121 >> 8);
    out1[0] = x55;
    out1[1] = x57;
    out1[2] = x58;
    out1[3] = x59;
    out1[4] = x61;
    out1[5] = x64;
    out1[6] = x66;
    out1[7] = x68;
    out1[8] = x71;
    out1[9] = x73;
    out1[10] = x75;
    out1[11] = x78;
    out1[12] = x80;
    out1[13] = x82;
    out1[14] = x85;
    out1[15] = x87;
    out1[16] = x89;
    out1[17] = x92;
    out1[18] = x94;
    out1[19] = x96;
    out1[20] = x99;
    out1[21] = x101;
    out1[22] = x103;
    out1[23] = x106;
    out1[24] = x108;
    out1[25] = x110;
    out1[26] = x113;
    out1[27] = x115;
    out1[28] = x117;
    out1[29] = x120;
    out1[30] = x122;
    out1[31] = x123;
}

/*
 * The function fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes deserializes a field element from bytes in little-endian order.
 * Postconditions:
 *   eval out1 mod m = bytes_eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(
    uint32_t out1[11], const uint8_t arg1[32]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    uint32_t x12;
    uint32_t x13;
    uint32_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint32_t x21;
    uint32_t x22;
    uint32_t x23;
    uint32_t x24;
    uint32_t x25;
    uint32_t x26;
    uint32_t x27;
    uint32_t x28;
    uint8_t x29;
    uint32_t x30;
    uint32_t x31;
    uint8_t x32;
    uint32_t x33;
    uint32_t x34;
    uint32_t x35;
    uint32_t x36;
    uint32_t x37;
    fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1 x38;
    uint32_t x39;
    uint32_t x40;
    uint32_t x41;
    uint32_t x42;
    uint8_t x43;
    uint32_t x44;
    uint32_t x45;
    uint32_t x46;
    uint32_t x47;
    uint8_t x48;
    uint32_t x49;
    uint32_t x50;
    uint32_t x51;
    uint32_t x52;
    uint8_t x53;
    uint32_t x54;
    uint32_t x55;
    uint32_t x56;
    uint32_t x57;
    uint8_t x58;
    uint32_t x59;
    uint32_t x60;
    uint32_t x61;
    uint32_t x62;
    uint8_t x63;
    uint32_t x64;
    uint32_t x65;
    uint32_t x66;
    uint32_t x67;
    uint8_t x68;
    uint32_t x69;
    uint32_t x70;
    uint32_t x71;
    uint32_t x72;
    uint8_t x73;
    uint32_t x74;
    uint32_t x75;
    uint32_t x76;
    uint32_t x77;
    uint8_t x78;
    uint32_t x79;
    uint32_t x80;
    x1 = ((uint32_t)(arg1[31]) << 15);
    x2 = ((uint32_t)(arg1[30]) << 7);
    x3 = ((uint32_t)(arg1[29]) << 22);
    x4 = ((uint32_t)(arg1[28]) << 14);
    x5 = ((uint32_t)(arg1[27]) << 6);
    x6 = ((uint32_t)(arg1[26]) << 21);
    x7 = ((uint32_t)(arg1[25]) << 13);
    x8 = ((uint32_t)(arg1[24]) << 5);
    x9 = ((uint32_t)(arg1[23]) << 21);
    x10 = ((uint32_t)(arg1[22]) << 13);
    x11 = ((uint32_t)(arg1[21]) << 5);
    x12 = ((uint32_t)(arg1[20]) << 20);
    x13 = ((uint32_t)(arg1[19]) << 12);
    x14 = ((uint32_t)(arg1[18]) << 4);
    x15 = ((uint32_t)(arg1[17]) << 19);
    x16 = ((uint32_t)(arg1[16]) << 11);
    x17 = ((uint32_t)(arg1[15]) << 3);
    x18 = ((uint32_t)(arg1[14]) << 18);
    x19 = ((uint32_t)(arg1[13]) << 10);
    x20 = ((uint32_t)(arg1[12]) << 2);
    x21 = ((uint32_t)(arg1[11]) << 18);
    x22 = ((uint32_t)(arg1[10]) << 10);
    x23 = ((uint32_t)(arg1[9]) << 2);
    x24 = ((uint32_t)(arg1[8]) << 17);
    x25 = ((uint32_t)(arg1[7]) << 9);
    x26 = ((uint32_t)(arg1[6]) * 0x2);
    x27 = ((uint32_t)(arg1[5]) << 16);
    x28 = ((uint32_t)(arg1[4]) << 8);
    x29 = (arg1[3]);
    x30 = ((uint32_t)(arg1[2]) << 16);
    x31 = ((uint32_t)(arg1[1]) << 8);
    x32 = (arg1[0]);
    x33 = (x31 + (uint32_t)x32);
    x34 = (x30 + x33);
    x35 = (x28 + (uint32_t)x29);
    x36 = (x27 + x35);
    x37 = (x36 & UINT32_C(0x7fffff));
    x38 = (fiat_id_tc26_gost_3410_2012_256_paramSetA_uint1)(x36 >> 23);
    x39 = (x26 + (uint32_t)x38);
    x40 = (x25 + x39);
    x41 = (x24 + x40);
    x42 = (x41 & UINT32_C(0x7fffff));
    x43 = (uint8_t)(x41 >> 23);
    x44 = (x23 + (uint32_t)x43);
    x45 = (x22 + x44);
    x46 = (x21 + x45);
    x47 = (x46 & UINT32_C(0xffffff));
    x48 = (uint8_t)(x46 >> 24);
    x49 = (x20 + (uint32_t)x48);
    x50 = (x19 + x49);
    x51 = (x18 + x50);
    x52 = (x51 & UINT32_C(0x7fffff));
    x53 = (uint8_t)(x51 >> 23);
    x54 = (x17 + (uint32_t)x53);
    x55 = (x16 + x54);
    x56 = (x15 + x55);
    x57 = (x56 & UINT32_C(0x7fffff));
    x58 = (uint8_t)(x56 >> 23);
    x59 = (x14 + (uint32_t)x58);
    x60 = (x13 + x59);
    x61 = (x12 + x60);
    x62 = (x61 & UINT32_C(0x7fffff));
    x63 = (uint8_t)(x61 >> 23);
    x64 = (x11 + (uint32_t)x63);
    x65 = (x10 + x64);
    x66 = (x9 + x65);
    x67 = (x66 & UINT32_C(0xffffff));
    x68 = (uint8_t)(x66 >> 24);
    x69 = (x8 + (uint32_t)x68);
    x70 = (x7 + x69);
    x71 = (x6 + x70);
    x72 = (x71 & UINT32_C(0x7fffff));
    x73 = (uint8_t)(x71 >> 23);
    x74 = (x5 + (uint32_t)x73);
    x75 = (x4 + x74);
    x76 = (x3 + x75);
    x77 = (x76 & UINT32_C(0x7fffff));
    x78 = (uint8_t)(x76 >> 23);
    x79 = (x2 + (uint32_t)x78);
    x80 = (x1 + x79);
    out1[0] = x34;
    out1[1] = x37;
    out1[2] = x42;
    out1[3] = x47;
    out1[4] = x52;
    out1[5] = x57;
    out1[6] = x62;
    out1[7] = x67;
    out1[8] = x72;
    out1[9] = x77;
    out1[10] = x80;
}

/* END verbatim fiat code */

/*-
 * Finite field inversion via FLT.
 * NB: this is not a real Fiat function, just named that way for consistency.
 * Autogenerated: ecp/id_tc26_gost_3410_2012_256_paramSetA/fe_inv.op3
 * custom repunit addition chain
 */
static void fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(fe_t output,
                                                          const fe_t t1) {
    int i;
    /* temporary variables */
    fe_t acc, t16, t164, t2, t246, t32, t4, t64, t8, t80, t82;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, acc, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t4, acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t4);
    for (i = 0; i < 3; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t8, acc, t4);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t8);
    for (i = 0; i < 7; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t16, acc, t8);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t16);
    for (i = 0; i < 15; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t32, acc, t16);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t32);
    for (i = 0; i < 31; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t64, acc, t32);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t64);
    for (i = 0; i < 15; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t80, acc, t16);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t80);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t82, acc, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t82);
    for (i = 0; i < 81; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t164, acc, t82);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t164);
    for (i = 0; i < 81; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t246, acc, t82);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, t246);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t2);
    for (i = 0; i < 3; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(acc, acc);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(output, acc, t1);
}

/* curve coefficient constants */

static const limb_t const_one[11] = {
    UINT32_C(0x00000001), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000)};

static const limb_t const_d[11] = {
    UINT32_C(0x00C7BFFB), UINT32_C(0x00432D6D), UINT32_C(0x006FCA45),
    UINT32_C(0x005C026B), UINT32_C(0x0077D8A2), UINT32_C(0x0040995C),
    UINT32_C(0x004FAD51), UINT32_C(0x00F17873), UINT32_C(0x007F502A),
    UINT32_C(0x002DF060), UINT32_C(0x000302FB)};

static const limb_t const_S[11] = {
    UINT32_C(0x008E0ECD), UINT32_C(0x004F34A4), UINT32_C(0x00040D6E),
    UINT32_C(0x0068FF65), UINT32_C(0x006209D7), UINT32_C(0x004FD9A8),
    UINT32_C(0x000C14AB), UINT32_C(0x0043A1E3), UINT32_C(0x00602BF5),
    UINT32_C(0x001483E7), UINT32_C(0x003F3F41)};

static const limb_t const_T[11] = {
    UINT32_C(0x00A14AAA), UINT32_C(0x0075DCE7), UINT32_C(0x007D4C60),
    UINT32_C(0x0064AB11), UINT32_C(0x0013F970), UINT32_C(0x004AC43A),
    UINT32_C(0x004D478D), UINT32_C(0x00D2E968), UINT32_C(0x003FE2B1),
    UINT32_C(0x001CFD65), UINT32_C(0x0000807F)};

/* LUT for scalar multiplication by comb interleaving */
static const pt_aff_t lut_cmb[14][16] = {
    {
        {{UINT32_C(0x0000000D), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000)},
         {UINT32_C(0x00A300E7), UINT32_C(0x002592DB), UINT32_C(0x0003F256),
          UINT32_C(0x001FC8BA), UINT32_C(0x003E36FA), UINT32_C(0x0024E73F),
          UINT32_C(0x007AB076), UINT32_C(0x00911871), UINT32_C(0x006B6690),
          UINT32_C(0x000CAA91), UINT32_C(0x0030650F)},
         {UINT32_C(0x0047155F), UINT32_C(0x00687527), UINT32_C(0x00334E61),
          UINT32_C(0x009D3172), UINT32_C(0x0028CAB3), UINT32_C(0x005FBE39),
          UINT32_C(0x003AF601), UINT32_C(0x005E3DC9), UINT32_C(0x00743557),
          UINT32_C(0x0024A967), UINT32_C(0x007521C4)}},
        {{UINT32_C(0x0051134C), UINT32_C(0x0063D934), UINT32_C(0x000994F4),
          UINT32_C(0x00E91B07), UINT32_C(0x00439CFD), UINT32_C(0x0055A4D9),
          UINT32_C(0x00181579), UINT32_C(0x0089F4E6), UINT32_C(0x005E989F),
          UINT32_C(0x002C52EC), UINT32_C(0x0054E159)},
         {UINT32_C(0x00DB5791), UINT32_C(0x00525DDC), UINT32_C(0x006F7908),
          UINT32_C(0x00AAB332), UINT32_C(0x00279FCD), UINT32_C(0x00522145),
          UINT32_C(0x0059CBA7), UINT32_C(0x00B33511), UINT32_C(0x000C6593),
          UINT32_C(0x0059CF0A), UINT32_C(0x0016BDEB)},
         {UINT32_C(0x00CBC030), UINT32_C(0x00732788), UINT32_C(0x005A0DF8),
          UINT32_C(0x00C8799D), UINT32_C(0x0019C571), UINT32_C(0x0007F6EF),
          UINT32_C(0x00729208), UINT32_C(0x00D7466B), UINT32_C(0x004ACA39),
          UINT32_C(0x0056009D), UINT32_C(0x00586898)}},
        {{UINT32_C(0x007058AD), UINT32_C(0x00094E45), UINT32_C(0x00420275),
          UINT32_C(0x005FD239), UINT32_C(0x00444F1F), UINT32_C(0x00299CBF),
          UINT32_C(0x002C9ADA), UINT32_C(0x00816030), UINT32_C(0x00255119),
          UINT32_C(0x0004FD8A), UINT32_C(0x000F011D)},
         {UINT32_C(0x00B22BCC), UINT32_C(0x00380EB0), UINT32_C(0x0075817F),
          UINT32_C(0x00B68F07), UINT32_C(0x0065DD81), UINT32_C(0x00433816),
          UINT32_C(0x005064DD), UINT32_C(0x00DF04CF), UINT32_C(0x000A89F8),
          UINT32_C(0x0061BAC3), UINT32_C(0x002AEE30)},
         {UINT32_C(0x00831C0C), UINT32_C(0x006D5422), UINT32_C(0x005EFCE9),
          UINT32_C(0x004B9CD7), UINT32_C(0x003E61FB), UINT32_C(0x001487DD),
          UINT32_C(0x0011779C), UINT32_C(0x0036DFD0), UINT32_C(0x005A0784),
          UINT32_C(0x0059323A), UINT32_C(0x002BA6DC)}},
        {{UINT32_C(0x00B0B356), UINT32_C(0x0066CD5D), UINT32_C(0x0010AFBC),
          UINT32_C(0x00023E69), UINT32_C(0x007B3FA1), UINT32_C(0x003A3F07),
          UINT32_C(0x007B8F99), UINT32_C(0x000415F9), UINT32_C(0x0073141E),
          UINT32_C(0x00072DB3), UINT32_C(0x007B2887)},
         {UINT32_C(0x00EFDCBE), UINT32_C(0x0061FDB8), UINT32_C(0x004A709F),
          UINT32_C(0x00849CD0), UINT32_C(0x002A36FC), UINT32_C(0x003BB056),
          UINT32_C(0x00356C38), UINT32_C(0x007A9084), UINT32_C(0x000B6422),
          UINT32_C(0x00130D7D), UINT32_C(0x001CD6F3)},
         {UINT32_C(0x00AA7E64), UINT32_C(0x0012D2BE), UINT32_C(0x0055120E),
          UINT32_C(0x00938524), UINT32_C(0x000C79B2), UINT32_C(0x0058FE4B),
          UINT32_C(0x00510FB7), UINT32_C(0x00D0F8E2), UINT32_C(0x0063F17F),
          UINT32_C(0x002D4711), UINT32_C(0x002725A4)}},
        {{UINT32_C(0x004C6781), UINT32_C(0x00627F87), UINT32_C(0x00489AA7),
          UINT32_C(0x002F770F), UINT32_C(0x000D2620), UINT32_C(0x001E8468),
          UINT32_C(0x003F6D7E), UINT32_C(0x00C5511D), UINT32_C(0x006CDB93),
          UINT32_C(0x006842D1), UINT32_C(0x004D7CF0)},
         {UINT32_C(0x00D1AE96), UINT32_C(0x0000090D), UINT32_C(0x0006C4DB),
          UINT32_C(0x00CE5C55), UINT32_C(0x00144E90), UINT32_C(0x004BED80),
          UINT32_C(0x004E53B7), UINT32_C(0x001FE39D), UINT32_C(0x0042F35D),
          UINT32_C(0x004AEB11), UINT32_C(0x005F6DBA)},
         {UINT32_C(0x00499A0E), UINT32_C(0x007885A2), UINT32_C(0x00030BDC),
          UINT32_C(0x001EDFDB), UINT32_C(0x00438B15), UINT32_C(0x0072B2EE),
          UINT32_C(0x004AC01E), UINT32_C(0x003EC678), UINT32_C(0x005F2133),
          UINT32_C(0x0011BD06), UINT32_C(0x002EA8E4)}},
        {{UINT32_C(0x00226F30), UINT32_C(0x005E7B60), UINT32_C(0x0009837F),
          UINT32_C(0x00851533), UINT32_C(0x0006EFB9), UINT32_C(0x00130938),
          UINT32_C(0x001063CE), UINT32_C(0x0019C098), UINT32_C(0x00605AC6),
          UINT32_C(0x004D2A8D), UINT32_C(0x00240305)},
         {UINT32_C(0x006AE25F), UINT32_C(0x000EF2E0), UINT32_C(0x001F427E),
          UINT32_C(0x00CD4540), UINT32_C(0x003C6996), UINT32_C(0x0035E452),
          UINT32_C(0x005A6442), UINT32_C(0x007BC7A6), UINT32_C(0x003D0529),
          UINT32_C(0x0021E8F3), UINT32_C(0x0052A46E)},
         {UINT32_C(0x007F4E9D), UINT32_C(0x000ECC55), UINT32_C(0x0037EE5A),
          UINT32_C(0x004ADDB7), UINT32_C(0x004DA3F4), UINT32_C(0x005764C9),
          UINT32_C(0x001C40F1), UINT32_C(0x00C1111E), UINT32_C(0x000B0764),
          UINT32_C(0x007DD2E6), UINT32_C(0x001388A4)}},
        {{UINT32_C(0x0095A16C), UINT32_C(0x0008DD08), UINT32_C(0x005FC5C5),
          UINT32_C(0x0081C743), UINT32_C(0x0000D180), UINT32_C(0x000F8EE9),
          UINT32_C(0x0003FEF5), UINT32_C(0x00CF52AF), UINT32_C(0x007E4EDC),
          UINT32_C(0x0052DFAC), UINT32_C(0x0057C4FB)},
         {UINT32_C(0x00E2B732), UINT32_C(0x005BEBA4), UINT32_C(0x001D1662),
          UINT32_C(0x00FE39C5), UINT32_C(0x0058807D), UINT32_C(0x004BBC55),
          UINT32_C(0x0020454A), UINT32_C(0x00A84E47), UINT32_C(0x0034B9B8),
          UINT32_C(0x004B9BC7), UINT32_C(0x00362B2B)},
         {UINT32_C(0x00D51136), UINT32_C(0x0005D2D8), UINT32_C(0x000FECF5),
          UINT32_C(0x0088AA07), UINT32_C(0x004BB57C), UINT32_C(0x003A4AD1),
          UINT32_C(0x001B78C1), UINT32_C(0x00162747), UINT32_C(0x002DBFA9),
          UINT32_C(0x0017B251), UINT32_C(0x00431084)}},
        {{UINT32_C(0x003DC33C), UINT32_C(0x00238321), UINT32_C(0x002858CB),
          UINT32_C(0x006E05B3), UINT32_C(0x000F55FA), UINT32_C(0x002BA1C2),
          UINT32_C(0x007BBFD4), UINT32_C(0x00A06A51), UINT32_C(0x003672AF),
          UINT32_C(0x001A9B5B), UINT32_C(0x004F6D26)},
         {UINT32_C(0x009F55A3), UINT32_C(0x000F6705), UINT32_C(0x004EDA64),
          UINT32_C(0x00D8FBA8), UINT32_C(0x006E341D), UINT32_C(0x005FDC16),
          UINT32_C(0x003F8CD0), UINT32_C(0x0031F5C1), UINT32_C(0x002ECA27),
          UINT32_C(0x006E2E8E), UINT32_C(0x007650A0)},
         {UINT32_C(0x004C828B), UINT32_C(0x004D65DD), UINT32_C(0x0023D49F),
          UINT32_C(0x00998DDA), UINT32_C(0x004E0332), UINT32_C(0x00174414),
          UINT32_C(0x00699E65), UINT32_C(0x008F8323), UINT32_C(0x002B8901),
          UINT32_C(0x0012C259), UINT32_C(0x0021CFF4)}},
        {{UINT32_C(0x00922362), UINT32_C(0x0048A92F), UINT32_C(0x004D734D),
          UINT32_C(0x00C47EE4), UINT32_C(0x00113110), UINT32_C(0x00157C19),
          UINT32_C(0x00221251), UINT32_C(0x00024872), UINT32_C(0x001E2816),
          UINT32_C(0x003DF03D), UINT32_C(0x00181B9C)},
         {UINT32_C(0x00012FCF), UINT32_C(0x0047EB3F), UINT32_C(0x006C2B35),
          UINT32_C(0x00F85359), UINT32_C(0x0032819B), UINT32_C(0x004E64B2),
          UINT32_C(0x00600748), UINT32_C(0x00944305), UINT32_C(0x001ED335),
          UINT32_C(0x005DCD46), UINT32_C(0x004FA013)},
         {UINT32_C(0x003393AB), UINT32_C(0x002D18CD), UINT32_C(0x002579E1),
          UINT32_C(0x0079F6D2), UINT32_C(0x00032785), UINT32_C(0x00679042),
          UINT32_C(0x003532E2), UINT32_C(0x00E10DCE), UINT32_C(0x00480E01),
          UINT32_C(0x00426205), UINT32_C(0x0053FD4D)}},
        {{UINT32_C(0x00C6B0CB), UINT32_C(0x007C89A2), UINT32_C(0x0078D667),
          UINT32_C(0x00FF9964), UINT32_C(0x006EE83C), UINT32_C(0x006E7BF5),
          UINT32_C(0x007599F3), UINT32_C(0x004D669F), UINT32_C(0x0065012B),
          UINT32_C(0x005EBA50), UINT32_C(0x00304E71)},
         {UINT32_C(0x00C53736), UINT32_C(0x000B3E67), UINT32_C(0x00126BEE),
          UINT32_C(0x00261DF0), UINT32_C(0x005AF18B), UINT32_C(0x005B38F7),
          UINT32_C(0x000082AC), UINT32_C(0x0017B3DB), UINT32_C(0x007C32BA),
          UINT32_C(0x00724F37), UINT32_C(0x001F8FD1)},
         {UINT32_C(0x0080CCBF), UINT32_C(0x0018D8B8), UINT32_C(0x006E22B9),
          UINT32_C(0x00591C57), UINT32_C(0x0011E0B1), UINT32_C(0x0079970A),
          UINT32_C(0x0070D898), UINT32_C(0x00D9733F), UINT32_C(0x00259708),
          UINT32_C(0x0062101F), UINT32_C(0x00306235)}},
        {{UINT32_C(0x002F49E9), UINT32_C(0x006E57BC), UINT32_C(0x00094045),
          UINT32_C(0x00CF8C2B), UINT32_C(0x0010A5CA), UINT32_C(0x00478E10),
          UINT32_C(0x000A83ED), UINT32_C(0x00EB329A), UINT32_C(0x0054EE99),
          UINT32_C(0x00112D75), UINT32_C(0x001A7633)},
         {UINT32_C(0x00762A44), UINT32_C(0x007F7B5B), UINT32_C(0x006BF456),
          UINT32_C(0x00680D50), UINT32_C(0x00734ECC), UINT32_C(0x00770776),
          UINT32_C(0x0041B3F1), UINT32_C(0x00DB7326), UINT32_C(0x007A5C18),
          UINT32_C(0x0009A04C), UINT32_C(0x006F2281)},
         {UINT32_C(0x00392E55), UINT32_C(0x00180EF7), UINT32_C(0x00539457),
          UINT32_C(0x0027985E), UINT32_C(0x0030AE2E), UINT32_C(0x0006A2FA),
          UINT32_C(0x0075FD5D), UINT32_C(0x0023429D), UINT32_C(0x0041CD65),
          UINT32_C(0x00710776), UINT32_C(0x0075B4D3)}},
        {{UINT32_C(0x00297799), UINT32_C(0x0050EDAC), UINT32_C(0x0017E4E4),
          UINT32_C(0x00B06306), UINT32_C(0x0011957C), UINT32_C(0x00454CDB),
          UINT32_C(0x0037CE52), UINT32_C(0x0035EF58), UINT32_C(0x003AACF3),
          UINT32_C(0x006B04D4), UINT32_C(0x001DEEDA)},
         {UINT32_C(0x0058DEDB), UINT32_C(0x0055E31C), UINT32_C(0x000564F2),
          UINT32_C(0x00767021), UINT32_C(0x000AA59A), UINT32_C(0x005E4355),
          UINT32_C(0x0076B6F5), UINT32_C(0x00E2300C), UINT32_C(0x0065F80C),
          UINT32_C(0x004E48E3), UINT32_C(0x00471F50)},
         {UINT32_C(0x0057B4C9), UINT32_C(0x0032102C), UINT32_C(0x004F162E),
          UINT32_C(0x00AA70A0), UINT32_C(0x00601FA8), UINT32_C(0x001946ED),
          UINT32_C(0x007F5192), UINT32_C(0x009CBF3B), UINT32_C(0x0071AED7),
          UINT32_C(0x000BA7C1), UINT32_C(0x00663BBD)}},
        {{UINT32_C(0x00FC042F), UINT32_C(0x00129354), UINT32_C(0x001AC8F7),
          UINT32_C(0x00124BB3), UINT32_C(0x007DE447), UINT32_C(0x0007CA46),
          UINT32_C(0x005604BA), UINT32_C(0x00981600), UINT32_C(0x0014DA63),
          UINT32_C(0x0005067B), UINT32_C(0x0053AE1A)},
         {UINT32_C(0x009BC0FD), UINT32_C(0x006F8B2C), UINT32_C(0x006AE987),
          UINT32_C(0x0031F283), UINT32_C(0x0001800B), UINT32_C(0x00596A47),
          UINT32_C(0x0067F6A5), UINT32_C(0x00512023), UINT32_C(0x0015BA88),
          UINT32_C(0x0012951C), UINT32_C(0x000C64FA)},
         {UINT32_C(0x00ED7C65), UINT32_C(0x0008BD59), UINT32_C(0x00636115),
          UINT32_C(0x0076BA73), UINT32_C(0x007F05EB), UINT32_C(0x0003E701),
          UINT32_C(0x00148CDD), UINT32_C(0x008B64AA), UINT32_C(0x00373C6D),
          UINT32_C(0x00516A1E), UINT32_C(0x00056623)}},
        {{UINT32_C(0x000D0BB7), UINT32_C(0x007E8A54), UINT32_C(0x007474B6),
          UINT32_C(0x003D4755), UINT32_C(0x0026CD61), UINT32_C(0x0048139E),
          UINT32_C(0x005E0EC7), UINT32_C(0x00169B5D), UINT32_C(0x0042855A),
          UINT32_C(0x00729E2F), UINT32_C(0x001F1939)},
         {UINT32_C(0x00EDF74C), UINT32_C(0x00736130), UINT32_C(0x007835CD),
          UINT32_C(0x0021FE28), UINT32_C(0x0051653E), UINT32_C(0x00690BC6),
          UINT32_C(0x0070143D), UINT32_C(0x0011B7F6), UINT32_C(0x002ECF93),
          UINT32_C(0x00332D5B), UINT32_C(0x004E7E15)},
         {UINT32_C(0x0061C3BC), UINT32_C(0x006F0A58), UINT32_C(0x002A9437),
          UINT32_C(0x00273621), UINT32_C(0x004D6870), UINT32_C(0x004A0410),
          UINT32_C(0x001930EC), UINT32_C(0x00473AEA), UINT32_C(0x00345B94),
          UINT32_C(0x000D2F6F), UINT32_C(0x00630991)}},
        {{UINT32_C(0x0064F095), UINT32_C(0x005B811B), UINT32_C(0x0034F150),
          UINT32_C(0x0037FBC3), UINT32_C(0x00431976), UINT32_C(0x001AF585),
          UINT32_C(0x004A47D6), UINT32_C(0x00364593), UINT32_C(0x00265D55),
          UINT32_C(0x006955EC), UINT32_C(0x004B56E9)},
         {UINT32_C(0x00D4D3BA), UINT32_C(0x003E639C), UINT32_C(0x007B6508),
          UINT32_C(0x007AAFAA), UINT32_C(0x002DEDD3), UINT32_C(0x006E90FB),
          UINT32_C(0x0070FF1F), UINT32_C(0x005BD7F8), UINT32_C(0x0015D774),
          UINT32_C(0x004E9774), UINT32_C(0x003F53F7)},
         {UINT32_C(0x004F4AA5), UINT32_C(0x0024432D), UINT32_C(0x002D31BA),
          UINT32_C(0x005F3C7B), UINT32_C(0x00221DE3), UINT32_C(0x00691A3B),
          UINT32_C(0x0067E7F8), UINT32_C(0x00B606C6), UINT32_C(0x005200E8),
          UINT32_C(0x00708B99), UINT32_C(0x002F258F)}},
        {{UINT32_C(0x003A119F), UINT32_C(0x00319D59), UINT32_C(0x0071DF1F),
          UINT32_C(0x00EE723B), UINT32_C(0x004A075C), UINT32_C(0x0000C0BD),
          UINT32_C(0x00381ECA), UINT32_C(0x009EC137), UINT32_C(0x0014C177),
          UINT32_C(0x0062C87B), UINT32_C(0x003F7471)},
         {UINT32_C(0x00AA3C6C), UINT32_C(0x0019B06D), UINT32_C(0x003A8845),
          UINT32_C(0x001B9E7B), UINT32_C(0x004749DD), UINT32_C(0x0067A317),
          UINT32_C(0x00555D63), UINT32_C(0x0087596C), UINT32_C(0x004BF763),
          UINT32_C(0x005DCF2F), UINT32_C(0x00725068)},
         {UINT32_C(0x005A930B), UINT32_C(0x003C325E), UINT32_C(0x00528AC5),
          UINT32_C(0x00FD1B98), UINT32_C(0x004BA241), UINT32_C(0x0041EDCF),
          UINT32_C(0x00587205), UINT32_C(0x00EDF302), UINT32_C(0x001C093C),
          UINT32_C(0x0062B2BF), UINT32_C(0x00225709)}},
    },
    {
        {{UINT32_C(0x00EABA3B), UINT32_C(0x00192B0C), UINT32_C(0x006A78E0),
          UINT32_C(0x001251C4), UINT32_C(0x0032CA32), UINT32_C(0x0055DC60),
          UINT32_C(0x005BBC99), UINT32_C(0x00F53480), UINT32_C(0x004CA164),
          UINT32_C(0x0032C336), UINT32_C(0x004567E0)},
         {UINT32_C(0x009B52D6), UINT32_C(0x006B6695), UINT32_C(0x0011E7D6),
          UINT32_C(0x002112B7), UINT32_C(0x007F870C), UINT32_C(0x002F5D93),
          UINT32_C(0x002B31C0), UINT32_C(0x000D401F), UINT32_C(0x0004B6C8),
          UINT32_C(0x0023BEE0), UINT32_C(0x00086B9A)},
         {UINT32_C(0x001E8B93), UINT32_C(0x00336732), UINT32_C(0x00435024),
          UINT32_C(0x00A9D624), UINT32_C(0x007896CB), UINT32_C(0x00086124),
          UINT32_C(0x001F6441), UINT32_C(0x00C216CD), UINT32_C(0x0045B1DA),
          UINT32_C(0x0040884B), UINT32_C(0x0015A99D)}},
        {{UINT32_C(0x00E29666), UINT32_C(0x0000698C), UINT32_C(0x00008566),
          UINT32_C(0x00B29855), UINT32_C(0x0069993B), UINT32_C(0x0066533E),
          UINT32_C(0x00130B35), UINT32_C(0x00112187), UINT32_C(0x0066567A),
          UINT32_C(0x006C6930), UINT32_C(0x00025F72)},
         {UINT32_C(0x00BC36F8), UINT32_C(0x0075E071), UINT32_C(0x003F18EC),
          UINT32_C(0x00FE4134), UINT32_C(0x0007BF37), UINT32_C(0x0015265C),
          UINT32_C(0x002D5EA8), UINT32_C(0x00932F6F), UINT32_C(0x0001004C),
          UINT32_C(0x0028FBB8), UINT32_C(0x003CC88B)},
         {UINT32_C(0x0025E5B3), UINT32_C(0x0052FCAA), UINT32_C(0x0072522F),
          UINT32_C(0x0075B891), UINT32_C(0x0071F699), UINT32_C(0x0051F116),
          UINT32_C(0x0078808A), UINT32_C(0x006A580C), UINT32_C(0x000BDDA3),
          UINT32_C(0x004CB31A), UINT32_C(0x0056F6CD)}},
        {{UINT32_C(0x001AF846), UINT32_C(0x0016456F), UINT32_C(0x006CDB6E),
          UINT32_C(0x0019B43D), UINT32_C(0x001BD7BB), UINT32_C(0x001E429F),
          UINT32_C(0x0025E351), UINT32_C(0x002C034F), UINT32_C(0x0053ECFF),
          UINT32_C(0x002645D7), UINT32_C(0x006F455E)},
         {UINT32_C(0x00161A5B), UINT32_C(0x005E3D52), UINT32_C(0x006167A5),
          UINT32_C(0x008BBC48), UINT32_C(0x0029C44C), UINT32_C(0x0009A976),
          UINT32_C(0x0038C321), UINT32_C(0x00390F40), UINT32_C(0x004A7861),
          UINT32_C(0x0027E8A3), UINT32_C(0x0055430C)},
         {UINT32_C(0x0022B34E), UINT32_C(0x003FE536), UINT32_C(0x006D6BDE),
          UINT32_C(0x0038DF8B), UINT32_C(0x007C2700), UINT32_C(0x00413296),
          UINT32_C(0x000C3C34), UINT32_C(0x000F4A8B), UINT32_C(0x002223E1),
          UINT32_C(0x0002B3C2), UINT32_C(0x0030E676)}},
        {{UINT32_C(0x0084B729), UINT32_C(0x00726FC5), UINT32_C(0x00414876),
          UINT32_C(0x003546F3), UINT32_C(0x00261905), UINT32_C(0x005A04F6),
          UINT32_C(0x0055B0C7), UINT32_C(0x006B360B), UINT32_C(0x0012052B),
          UINT32_C(0x002B0046), UINT32_C(0x003D984C)},
         {UINT32_C(0x00CBED46), UINT32_C(0x0032683A), UINT32_C(0x004B3ADD),
          UINT32_C(0x00B2CD41), UINT32_C(0x0014EF94), UINT32_C(0x0050E0BF),
          UINT32_C(0x003FC9B1), UINT32_C(0x002F30DC), UINT32_C(0x00297AE9),
          UINT32_C(0x007BCD89), UINT32_C(0x000B4620)},
         {UINT32_C(0x0030B5B8), UINT32_C(0x0067A579), UINT32_C(0x006EDA23),
          UINT32_C(0x0094FB0A), UINT32_C(0x00661BCD), UINT32_C(0x0040BF82),
          UINT32_C(0x0058F099), UINT32_C(0x0072BB0F), UINT32_C(0x00485362),
          UINT32_C(0x00539664), UINT32_C(0x006ADF86)}},
        {{UINT32_C(0x0024FDD7), UINT32_C(0x001186A8), UINT32_C(0x004EA183),
          UINT32_C(0x00493DFC), UINT32_C(0x004F4E46), UINT32_C(0x0050110D),
          UINT32_C(0x003294D6), UINT32_C(0x00C5CEF6), UINT32_C(0x0053F4A9),
          UINT32_C(0x006A41F0), UINT32_C(0x00280D45)},
         {UINT32_C(0x0025DCB5), UINT32_C(0x00352AB2), UINT32_C(0x003C9B57),
          UINT32_C(0x00C5972C), UINT32_C(0x0017C8A2), UINT32_C(0x0026C17F),
          UINT32_C(0x004A2DE0), UINT32_C(0x00962319), UINT32_C(0x001DC0E8),
          UINT32_C(0x007BB781), UINT32_C(0x0076D0B0)},
         {UINT32_C(0x00C25C01), UINT32_C(0x006DF306), UINT32_C(0x002E001F),
          UINT32_C(0x00B2D044), UINT32_C(0x001C46FF), UINT32_C(0x0028D794),
          UINT32_C(0x004CD7EB), UINT32_C(0x00B2D81B), UINT32_C(0x00719BF6),
          UINT32_C(0x005D73E6), UINT32_C(0x000574EC)}},
        {{UINT32_C(0x00ED0D33), UINT32_C(0x005B58E6), UINT32_C(0x0031614D),
          UINT32_C(0x00C647FC), UINT32_C(0x003D23AC), UINT32_C(0x006C18FD),
          UINT32_C(0x006B9F51), UINT32_C(0x00BD620E), UINT32_C(0x000E3630),
          UINT32_C(0x002C338B), UINT32_C(0x0055B898)},
         {UINT32_C(0x00609F49), UINT32_C(0x006332D0), UINT32_C(0x0032177F),
          UINT32_C(0x008ADB01), UINT32_C(0x0003C6A7), UINT32_C(0x0020EA0A),
          UINT32_C(0x005D93D8), UINT32_C(0x0053348A), UINT32_C(0x00511FAA),
          UINT32_C(0x00125BD7), UINT32_C(0x001F9B23)},
         {UINT32_C(0x007C7FE4), UINT32_C(0x0028B2B1), UINT32_C(0x007487D6),
          UINT32_C(0x0000D5FE), UINT32_C(0x007DEF82), UINT32_C(0x0016E07E),
          UINT32_C(0x0026B0AE), UINT32_C(0x003853C8), UINT32_C(0x002DCAA1),
          UINT32_C(0x00685027), UINT32_C(0x001A2F47)}},
        {{UINT32_C(0x00986197), UINT32_C(0x00217581), UINT32_C(0x00011B57),
          UINT32_C(0x001C3CC0), UINT32_C(0x0048D2D2), UINT32_C(0x000C0332),
          UINT32_C(0x002D89E5), UINT32_C(0x003490A4), UINT32_C(0x003458B1),
          UINT32_C(0x002B04FA), UINT32_C(0x003F7CF9)},
         {UINT32_C(0x004F002F), UINT32_C(0x002E83E7), UINT32_C(0x00629717),
          UINT32_C(0x0010444B), UINT32_C(0x005323A6), UINT32_C(0x003BF8D6),
          UINT32_C(0x002CFC62), UINT32_C(0x004F6988), UINT32_C(0x007305E7),
          UINT32_C(0x007EC041), UINT32_C(0x00754449)},
         {UINT32_C(0x00B16E11), UINT32_C(0x007607F5), UINT32_C(0x001923D5),
          UINT32_C(0x00E8AE2B), UINT32_C(0x006722D4), UINT32_C(0x0076783B),
          UINT32_C(0x0037C778), UINT32_C(0x007F8754), UINT32_C(0x00422A0E),
          UINT32_C(0x003D9F97), UINT32_C(0x0016AEAE)}},
        {{UINT32_C(0x00D81066), UINT32_C(0x0050C351), UINT32_C(0x00716769),
          UINT32_C(0x006393E3), UINT32_C(0x001B4C15), UINT32_C(0x004B9A55),
          UINT32_C(0x00588B55), UINT32_C(0x002BA83B), UINT32_C(0x00009915),
          UINT32_C(0x005C055C), UINT32_C(0x00048180)},
         {UINT32_C(0x00CB303A), UINT32_C(0x00458BFF), UINT32_C(0x00237470),
          UINT32_C(0x00D66822), UINT32_C(0x0009DE8B), UINT32_C(0x005B28C2),
          UINT32_C(0x0068CAFE), UINT32_C(0x002F5A98), UINT32_C(0x004C4B2A),
          UINT32_C(0x0031DE00), UINT32_C(0x006E5755)},
         {UINT32_C(0x008869E8), UINT32_C(0x001F33B0), UINT32_C(0x001A2BAA),
          UINT32_C(0x00817706), UINT32_C(0x00458C99), UINT32_C(0x00302337),
          UINT32_C(0x0039EAE5), UINT32_C(0x00D2E45D), UINT32_C(0x0073447F),
          UINT32_C(0x0024825C), UINT32_C(0x001B5E98)}},
        {{UINT32_C(0x00814972), UINT32_C(0x0038B443), UINT32_C(0x0041992D),
          UINT32_C(0x00EE69DB), UINT32_C(0x00146157), UINT32_C(0x000AAAD8),
          UINT32_C(0x005444E7), UINT32_C(0x00D17AB8), UINT32_C(0x004DF7C1),
          UINT32_C(0x004D27A8), UINT32_C(0x0054D67D)},
         {UINT32_C(0x000A587F), UINT32_C(0x003ECDA4), UINT32_C(0x002CA0AF),
          UINT32_C(0x008438DD), UINT32_C(0x00634B99), UINT32_C(0x003B5F19),
          UINT32_C(0x006A333D), UINT32_C(0x001B944F), UINT32_C(0x007E2E1F),
          UINT32_C(0x005C5F8A), UINT32_C(0x003EE1DE)},
         {UINT32_C(0x001A8276), UINT32_C(0x006DA949), UINT32_C(0x003ABE66),
          UINT32_C(0x00EE9FD1), UINT32_C(0x004B05FC), UINT32_C(0x005E0261),
          UINT32_C(0x007EAA48), UINT32_C(0x0010DD5B), UINT32_C(0x007CBE95),
          UINT32_C(0x00118A02), UINT32_C(0x0002173C)}},
        {{UINT32_C(0x003407C2), UINT32_C(0x003DB992), UINT32_C(0x001F1A9E),
          UINT32_C(0x00D5B577), UINT32_C(0x005468D1), UINT32_C(0x001331F0),
          UINT32_C(0x00760900), UINT32_C(0x00412C7B), UINT32_C(0x007D99FF),
          UINT32_C(0x003F157E), UINT32_C(0x0022BB7C)},
         {UINT32_C(0x008CE31C), UINT32_C(0x007405EE), UINT32_C(0x004669C1),
          UINT32_C(0x00B64348), UINT32_C(0x002A823A), UINT32_C(0x0028F130),
          UINT32_C(0x006999E9), UINT32_C(0x006697A5), UINT32_C(0x00546104),
          UINT32_C(0x0028A64D), UINT32_C(0x00531D1A)},
         {UINT32_C(0x009C76D2), UINT32_C(0x0079F19D), UINT32_C(0x000872F3),
          UINT32_C(0x00F799B3), UINT32_C(0x00407743), UINT32_C(0x003085E0),
          UINT32_C(0x002003F0), UINT32_C(0x00396CB7), UINT32_C(0x002952AF),
          UINT32_C(0x002D59FD), UINT32_C(0x003C184A)}},
        {{UINT32_C(0x00E10FCD), UINT32_C(0x00563D94), UINT32_C(0x0036A862),
          UINT32_C(0x00A8D743), UINT32_C(0x001FAEBC), UINT32_C(0x005A3817),
          UINT32_C(0x0061B73B), UINT32_C(0x00FE0A6A), UINT32_C(0x00113AE2),
          UINT32_C(0x0079A419), UINT32_C(0x00433EC2)},
         {UINT32_C(0x0094DA0B), UINT32_C(0x0053D54C), UINT32_C(0x005DC3C9),
          UINT32_C(0x00377F6C), UINT32_C(0x007601E5), UINT32_C(0x0054F416),
          UINT32_C(0x00147411), UINT32_C(0x0033E49D), UINT32_C(0x001316C3),
          UINT32_C(0x002D4674), UINT32_C(0x004DADCA)},
         {UINT32_C(0x00A27A08), UINT32_C(0x00150DCB), UINT32_C(0x00757D31),
          UINT32_C(0x00ECF538), UINT32_C(0x00662566), UINT32_C(0x00306294),
          UINT32_C(0x00601C11), UINT32_C(0x0035FECB), UINT32_C(0x00452259),
          UINT32_C(0x001C2D33), UINT32_C(0x0021363F)}},
        {{UINT32_C(0x00C03231), UINT32_C(0x0054A03F), UINT32_C(0x00530E31),
          UINT32_C(0x008A8654), UINT32_C(0x00445A8F), UINT32_C(0x0057E307),
          UINT32_C(0x0017BF37), UINT32_C(0x00015A04), UINT32_C(0x0002B01C),
          UINT32_C(0x001FB35F), UINT32_C(0x003FFA73)},
         {UINT32_C(0x002C08E7), UINT32_C(0x0040D81C), UINT32_C(0x00622B99),
          UINT32_C(0x0048FF13), UINT32_C(0x00665063), UINT32_C(0x004518AF),
          UINT32_C(0x003AC182), UINT32_C(0x00A28FD4), UINT32_C(0x002A6DAE),
          UINT32_C(0x001B6DBD), UINT32_C(0x0010ACD4)},
         {UINT32_C(0x0058C718), UINT32_C(0x0016FC28), UINT32_C(0x0041E2C6),
          UINT32_C(0x0052886B), UINT32_C(0x007FDE72), UINT32_C(0x00289521),
          UINT32_C(0x00173FE6), UINT32_C(0x0072E6CC), UINT32_C(0x0017AC30),
          UINT32_C(0x0040A8E1), UINT32_C(0x005097BC)}},
        {{UINT32_C(0x005B9B1D), UINT32_C(0x0050D0A3), UINT32_C(0x002DFE1B),
          UINT32_C(0x00EAA92D), UINT32_C(0x00277DA6), UINT32_C(0x00622641),
          UINT32_C(0x0037663B), UINT32_C(0x004FD085), UINT32_C(0x000C17EE),
          UINT32_C(0x006A97AF), UINT32_C(0x007A47AF)},
         {UINT32_C(0x006BE9BF), UINT32_C(0x003285C2), UINT32_C(0x0065C35C),
          UINT32_C(0x009C1C74), UINT32_C(0x005F5ECE), UINT32_C(0x0059AF30),
          UINT32_C(0x002A2EA6), UINT32_C(0x0053A7C7), UINT32_C(0x003A42C4),
          UINT32_C(0x007E6E33), UINT32_C(0x0067F167)},
         {UINT32_C(0x002379C4), UINT32_C(0x00784A42), UINT32_C(0x00260570),
          UINT32_C(0x00C001B9), UINT32_C(0x007733D1), UINT32_C(0x002610F4),
          UINT32_C(0x0076810F), UINT32_C(0x0093A496), UINT32_C(0x001DEA0A),
          UINT32_C(0x002CA003), UINT32_C(0x006326EC)}},
        {{UINT32_C(0x00BB2391), UINT32_C(0x00743284), UINT32_C(0x002E155F),
          UINT32_C(0x0035639B), UINT32_C(0x005BBF4F), UINT32_C(0x003E90B9),
          UINT32_C(0x00767573), UINT32_C(0x00321363), UINT32_C(0x00254580),
          UINT32_C(0x0029D549), UINT32_C(0x0052B4EB)},
         {UINT32_C(0x00C1B9A5), UINT32_C(0x004DDE09), UINT32_C(0x006CBD8B),
          UINT32_C(0x005BA607), UINT32_C(0x003CC42A), UINT32_C(0x001D4E2E),
          UINT32_C(0x007ACD96), UINT32_C(0x00C921CE), UINT32_C(0x000620D1),
          UINT32_C(0x0016E31C), UINT32_C(0x006A2B72)},
         {UINT32_C(0x002A9792), UINT32_C(0x0000CFD3), UINT32_C(0x006A9AD2),
          UINT32_C(0x00ED29B3), UINT32_C(0x0026DA69), UINT32_C(0x002D2B73),
          UINT32_C(0x001B5E5D), UINT32_C(0x00D5305C), UINT32_C(0x0067DCE0),
          UINT32_C(0x000729B3), UINT32_C(0x00070252)}},
        {{UINT32_C(0x007834CB), UINT32_C(0x001A305D), UINT32_C(0x00199CF9),
          UINT32_C(0x00EE7F83), UINT32_C(0x0048376D), UINT32_C(0x00182568),
          UINT32_C(0x0044C0B5), UINT32_C(0x007EFBAD), UINT32_C(0x006A5B89),
          UINT32_C(0x002DDEC8), UINT32_C(0x002C5365)},
         {UINT32_C(0x0014DDC8), UINT32_C(0x0026ED23), UINT32_C(0x004F3CF1),
          UINT32_C(0x009C337C), UINT32_C(0x005CA1F6), UINT32_C(0x00430D81),
          UINT32_C(0x001BF696), UINT32_C(0x00D5544B), UINT32_C(0x0001941E),
          UINT32_C(0x002720AB), UINT32_C(0x006EF64B)},
         {UINT32_C(0x00AEB238), UINT32_C(0x002A16EC), UINT32_C(0x0055FE48),
          UINT32_C(0x00606DE7), UINT32_C(0x0034576A), UINT32_C(0x006FF01C),
          UINT32_C(0x0009AA13), UINT32_C(0x00D2D5AB), UINT32_C(0x003249A4),
          UINT32_C(0x004B05E9), UINT32_C(0x0041BC86)}},
        {{UINT32_C(0x00A9E5D5), UINT32_C(0x003CDBE3), UINT32_C(0x00669075),
          UINT32_C(0x004D8E92), UINT32_C(0x0050E701), UINT32_C(0x003EEC92),
          UINT32_C(0x00340906), UINT32_C(0x003A5231), UINT32_C(0x0076D572),
          UINT32_C(0x001CA34B), UINT32_C(0x0035D540)},
         {UINT32_C(0x00FE52C3), UINT32_C(0x0064659E), UINT32_C(0x000FCF54),
          UINT32_C(0x00DDCF41), UINT32_C(0x007A7B36), UINT32_C(0x002AEC73),
          UINT32_C(0x007550B1), UINT32_C(0x008E1CEC), UINT32_C(0x002CAD9C),
          UINT32_C(0x004EE133), UINT32_C(0x0045B7D8)},
         {UINT32_C(0x009FC441), UINT32_C(0x002D1FBC), UINT32_C(0x003E7D16),
          UINT32_C(0x00C8B352), UINT32_C(0x00221849), UINT32_C(0x003FF5A0),
          UINT32_C(0x0055A506), UINT32_C(0x007F88D0), UINT32_C(0x0018DB76),
          UINT32_C(0x00672F49), UINT32_C(0x007D2F58)}},
    },
    {
        {{UINT32_C(0x00C3CC62), UINT32_C(0x001788C8), UINT32_C(0x0046ECBC),
          UINT32_C(0x00F44A0D), UINT32_C(0x005D570F), UINT32_C(0x0011C818),
          UINT32_C(0x0005F4D7), UINT32_C(0x00B7F489), UINT32_C(0x0001AE92),
          UINT32_C(0x002AB1B0), UINT32_C(0x0067910C)},
         {UINT32_C(0x0033A3DB), UINT32_C(0x00768B12), UINT32_C(0x005D50DE),
          UINT32_C(0x00D31F34), UINT32_C(0x003455F7), UINT32_C(0x0021BBE3),
          UINT32_C(0x0020D657), UINT32_C(0x009AEF49), UINT32_C(0x0011194A),
          UINT32_C(0x007C4E1E), UINT32_C(0x005A07C9)},
         {UINT32_C(0x008F9CA3), UINT32_C(0x0054FF76), UINT32_C(0x00570C00),
          UINT32_C(0x006792D9), UINT32_C(0x0064070F), UINT32_C(0x0044F2A2),
          UINT32_C(0x006ABEFC), UINT32_C(0x00DAAC6E), UINT32_C(0x004F8CD9),
          UINT32_C(0x004DC91F), UINT32_C(0x0049B2BB)}},
        {{UINT32_C(0x0018CD54), UINT32_C(0x0010BE7F), UINT32_C(0x005F8D91),
          UINT32_C(0x00BAF1CC), UINT32_C(0x0050AB12), UINT32_C(0x002D251A),
          UINT32_C(0x004B47CC), UINT32_C(0x005BD1C5), UINT32_C(0x00489594),
          UINT32_C(0x00699114), UINT32_C(0x0030C9FA)},
         {UINT32_C(0x00D23D40), UINT32_C(0x005307A4), UINT32_C(0x007E98F3),
          UINT32_C(0x00F08939), UINT32_C(0x0069A75B), UINT32_C(0x001DB79B),
          UINT32_C(0x00566A53), UINT32_C(0x005AF4D5), UINT32_C(0x00237443),
          UINT32_C(0x001D42DE), UINT32_C(0x005165D2)},
         {UINT32_C(0x00873511), UINT32_C(0x00525967), UINT32_C(0x0056101D),
          UINT32_C(0x00AA7060), UINT32_C(0x004C11B1), UINT32_C(0x0029CA00),
          UINT32_C(0x001A34BC), UINT32_C(0x005CA8C2), UINT32_C(0x002F5F9A),
          UINT32_C(0x0039EFC2), UINT32_C(0x006871D9)}},
        {{UINT32_C(0x00421E67), UINT32_C(0x00075D00), UINT32_C(0x0010583F),
          UINT32_C(0x00B732C7), UINT32_C(0x0037730B), UINT32_C(0x007028B8),
          UINT32_C(0x005C0247), UINT32_C(0x007CC11B), UINT32_C(0x007F99FE),
          UINT32_C(0x0074EF6F), UINT32_C(0x0049255A)},
         {UINT32_C(0x00806A6B), UINT32_C(0x0076A27F), UINT32_C(0x001A45F6),
          UINT32_C(0x001863FB), UINT32_C(0x0036A2B2), UINT32_C(0x00058E27),
          UINT32_C(0x007E3E52), UINT32_C(0x008FA71E), UINT32_C(0x000F9591),
          UINT32_C(0x000EA645), UINT32_C(0x0037E6C8)},
         {UINT32_C(0x004C164A), UINT32_C(0x0015AE71), UINT32_C(0x00228D86),
          UINT32_C(0x00441D33), UINT32_C(0x00507819), UINT32_C(0x00442DBE),
          UINT32_C(0x00457850), UINT32_C(0x0059222C), UINT32_C(0x0029495F),
          UINT32_C(0x007331A0), UINT32_C(0x007BAE86)}},
        {{UINT32_C(0x00FA5032), UINT32_C(0x00242303), UINT32_C(0x000A904B),
          UINT32_C(0x0003BB53), UINT32_C(0x007553E7), UINT32_C(0x003CA0C5),
          UINT32_C(0x0061B87D), UINT32_C(0x00805E53), UINT32_C(0x0049297C),
          UINT32_C(0x0056196B), UINT32_C(0x004C6DF6)},
         {UINT32_C(0x002C1092), UINT32_C(0x0019EF62), UINT32_C(0x0047F011),
          UINT32_C(0x00C03A85), UINT32_C(0x000B1A90), UINT32_C(0x001507E7),
          UINT32_C(0x004D6EBC), UINT32_C(0x00794D29), UINT32_C(0x005C5416),
          UINT32_C(0x0020F242), UINT32_C(0x00576518)},
         {UINT32_C(0x0034E914), UINT32_C(0x005DAF45), UINT32_C(0x005BE7A5),
          UINT32_C(0x00FE38F3), UINT32_C(0x00157770), UINT32_C(0x00237F8C),
          UINT32_C(0x0015BF21), UINT32_C(0x0077CC24), UINT32_C(0x005067D4),
          UINT32_C(0x00091C98), UINT32_C(0x007F54DE)}},
        {{UINT32_C(0x00770B21), UINT32_C(0x006194BB), UINT32_C(0x001A8987),
          UINT32_C(0x0094013C), UINT32_C(0x00619B7F), UINT32_C(0x00083816),
          UINT32_C(0x007A8072), UINT32_C(0x00F58256), UINT32_C(0x002FFA60),
          UINT32_C(0x007AC24E), UINT32_C(0x0042CF75)},
         {UINT32_C(0x0058474E), UINT32_C(0x007BFF2F), UINT32_C(0x0018145B),
          UINT32_C(0x00F8E32B), UINT32_C(0x0055DE08), UINT32_C(0x002AA874),
          UINT32_C(0x0044BCA0), UINT32_C(0x0038F7BE), UINT32_C(0x0027359B),
          UINT32_C(0x002747EB), UINT32_C(0x00226230)},
         {UINT32_C(0x00FA31A5), UINT32_C(0x00190A96), UINT32_C(0x006536D0),
          UINT32_C(0x00ACA102), UINT32_C(0x0036C0D0), UINT32_C(0x0008F3D0),
          UINT32_C(0x0049B490), UINT32_C(0x005CD6C9), UINT32_C(0x000E1C66),
          UINT32_C(0x0077C89C), UINT32_C(0x006A74AE)}},
        {{UINT32_C(0x00EE9825), UINT32_C(0x000FBC15), UINT32_C(0x000C0843),
          UINT32_C(0x006B793A), UINT32_C(0x0004ED1C), UINT32_C(0x00616EA7),
          UINT32_C(0x000B6304), UINT32_C(0x00166978), UINT32_C(0x0024139D),
          UINT32_C(0x0042F014), UINT32_C(0x003D4978)},
         {UINT32_C(0x0029D5D7), UINT32_C(0x0033F88C), UINT32_C(0x006E76FF),
          UINT32_C(0x00A49E5F), UINT32_C(0x003D801A), UINT32_C(0x0042EE48),
          UINT32_C(0x000D2057), UINT32_C(0x001D9164), UINT32_C(0x001AB787),
          UINT32_C(0x004D338B), UINT32_C(0x00262464)},
         {UINT32_C(0x00D5B395), UINT32_C(0x007A77B2), UINT32_C(0x0043D844),
          UINT32_C(0x006D5774), UINT32_C(0x00267921), UINT32_C(0x00374E41),
          UINT32_C(0x003B4C78), UINT32_C(0x00E2BA6E), UINT32_C(0x00656116),
          UINT32_C(0x0043049C), UINT32_C(0x002023C8)}},
        {{UINT32_C(0x001715E4), UINT32_C(0x00652B6A), UINT32_C(0x00379760),
          UINT32_C(0x004DB333), UINT32_C(0x00619842), UINT32_C(0x00656120),
          UINT32_C(0x0026FAEC), UINT32_C(0x00EF2AD7), UINT32_C(0x00149CB3),
          UINT32_C(0x0055D95D), UINT32_C(0x00497B11)},
         {UINT32_C(0x00D4B7BF), UINT32_C(0x001098D0), UINT32_C(0x0057E045),
          UINT32_C(0x00304A0C), UINT32_C(0x007F8852), UINT32_C(0x00564889),
          UINT32_C(0x007D4D67), UINT32_C(0x00A2CA30), UINT32_C(0x0051B0D9),
          UINT32_C(0x00495A39), UINT32_C(0x0017789D)},
         {UINT32_C(0x0049CE9C), UINT32_C(0x001C79B9), UINT32_C(0x000A676A),
          UINT32_C(0x0043503C), UINT32_C(0x003C78EA), UINT32_C(0x00779E15),
          UINT32_C(0x002487F8), UINT32_C(0x0098B781), UINT32_C(0x002F1E05),
          UINT32_C(0x000B58B5), UINT32_C(0x0079EE99)}},
        {{UINT32_C(0x0082E001), UINT32_C(0x002F7563), UINT32_C(0x005502B7),
          UINT32_C(0x0099B452), UINT32_C(0x00068BD3), UINT32_C(0x006837ED),
          UINT32_C(0x006C2DAE), UINT32_C(0x00EDD476), UINT32_C(0x004B54C6),
          UINT32_C(0x002353E5), UINT32_C(0x0036757B)},
         {UINT32_C(0x0003D167), UINT32_C(0x002030D2), UINT32_C(0x0072FA5A),
          UINT32_C(0x004E3D16), UINT32_C(0x001B78F0), UINT32_C(0x000924E6),
          UINT32_C(0x001E86B4), UINT32_C(0x006CD648), UINT32_C(0x001D24C9),
          UINT32_C(0x000CC288), UINT32_C(0x0054B2D8)},
         {UINT32_C(0x002B443B), UINT32_C(0x00135EB4), UINT32_C(0x006123F8),
          UINT32_C(0x00795F4F), UINT32_C(0x00161316), UINT32_C(0x005CC78F),
          UINT32_C(0x001879B2), UINT32_C(0x00A11513), UINT32_C(0x0075BC89),
          UINT32_C(0x000E0F4C), UINT32_C(0x0022EB79)}},
        {{UINT32_C(0x0097ABD7), UINT32_C(0x003BF415), UINT32_C(0x004B96FB),
          UINT32_C(0x0086107E), UINT32_C(0x0013751E), UINT32_C(0x006E3412),
          UINT32_C(0x0075AEFF), UINT32_C(0x00CEAD7E), UINT32_C(0x004D03FA),
          UINT32_C(0x0041C8E1), UINT32_C(0x00586295)},
         {UINT32_C(0x00BF974F), UINT32_C(0x003D6607), UINT32_C(0x003A2804),
          UINT32_C(0x00E9CC65), UINT32_C(0x00596911), UINT32_C(0x0013F86A),
          UINT32_C(0x00010E75), UINT32_C(0x0067284C), UINT32_C(0x00244A19),
          UINT32_C(0x00071615), UINT32_C(0x002E2C66)},
         {UINT32_C(0x004963F7), UINT32_C(0x00640576), UINT32_C(0x0031C7EE),
          UINT32_C(0x001D786C), UINT32_C(0x00257DEC), UINT32_C(0x003CF6C5),
          UINT32_C(0x00465E43), UINT32_C(0x00B15EDD), UINT32_C(0x00478BEB),
          UINT32_C(0x00016C1B), UINT32_C(0x00094307)}},
        {{UINT32_C(0x00E85486), UINT32_C(0x0077F915), UINT32_C(0x00760D35),
          UINT32_C(0x002EF0AE), UINT32_C(0x0037042C), UINT32_C(0x0030C359),
          UINT32_C(0x00346545), UINT32_C(0x00DC7FE6), UINT32_C(0x0030D4C5),
          UINT32_C(0x004AAD41), UINT32_C(0x000A60C0)},
         {UINT32_C(0x00D6E8B3), UINT32_C(0x000B7AA8), UINT32_C(0x0023E0C5),
          UINT32_C(0x006A8EBF), UINT32_C(0x0063D566), UINT32_C(0x002294C9),
          UINT32_C(0x0049A4AE), UINT32_C(0x005A2C0A), UINT32_C(0x005086A5),
          UINT32_C(0x0008808D), UINT32_C(0x0005A919)},
         {UINT32_C(0x007A8EAF), UINT32_C(0x001E6353), UINT32_C(0x005C83D2),
          UINT32_C(0x00714AAE), UINT32_C(0x004F2F37), UINT32_C(0x00249BF6),
          UINT32_C(0x006CDEE8), UINT32_C(0x006F262D), UINT32_C(0x007E9C03),
          UINT32_C(0x004C7272), UINT32_C(0x005E6F1B)}},
        {{UINT32_C(0x0011D440), UINT32_C(0x0050D0E6), UINT32_C(0x0020183D),
          UINT32_C(0x00D0ED3E), UINT32_C(0x004642A3), UINT32_C(0x0019F456),
          UINT32_C(0x0068661A), UINT32_C(0x005C97B1), UINT32_C(0x001DFD20),
          UINT32_C(0x00607CBE), UINT32_C(0x004EB1D7)},
         {UINT32_C(0x00424218), UINT32_C(0x0031CB2B), UINT32_C(0x0016EE1E),
          UINT32_C(0x00F0B2F5), UINT32_C(0x00284CA3), UINT32_C(0x000B886E),
          UINT32_C(0x0056424F), UINT32_C(0x00606535), UINT32_C(0x001A7DD3),
          UINT32_C(0x005E5D6D), UINT32_C(0x000910A8)},
         {UINT32_C(0x00E5A1E8), UINT32_C(0x0062AA84), UINT32_C(0x0072C780),
          UINT32_C(0x00E14E12), UINT32_C(0x00359D55), UINT32_C(0x00388026),
          UINT32_C(0x001AF8C7), UINT32_C(0x007C9658), UINT32_C(0x007CD573),
          UINT32_C(0x001DB584), UINT32_C(0x005C3621)}},
        {{UINT32_C(0x00E7BB1F), UINT32_C(0x002CD518), UINT32_C(0x0041FE08),
          UINT32_C(0x00717B78), UINT32_C(0x00400988), UINT32_C(0x00291794),
          UINT32_C(0x00747C25), UINT32_C(0x007194A1), UINT32_C(0x0040F7AF),
          UINT32_C(0x007B6E73), UINT32_C(0x0020EB45)},
         {UINT32_C(0x00161F74), UINT32_C(0x00299725), UINT32_C(0x0025AD63),
          UINT32_C(0x004B6379), UINT32_C(0x00076C32), UINT32_C(0x0071515E),
          UINT32_C(0x0032A4A1), UINT32_C(0x00A7B602), UINT32_C(0x005C7951),
          UINT32_C(0x003975BA), UINT32_C(0x004BA951)},
         {UINT32_C(0x009EEF2E), UINT32_C(0x000B3835), UINT32_C(0x005E1C6B),
          UINT32_C(0x00800FFB), UINT32_C(0x006BC9CA), UINT32_C(0x00125F16),
          UINT32_C(0x000F6D0D), UINT32_C(0x00186A82), UINT32_C(0x00479A35),
          UINT32_C(0x0006739D), UINT32_C(0x004DCC17)}},
        {{UINT32_C(0x0067A5F5), UINT32_C(0x007CA11D), UINT32_C(0x0051B4EF),
          UINT32_C(0x00E64083), UINT32_C(0x002B0147), UINT32_C(0x0022B454),
          UINT32_C(0x0050D922), UINT32_C(0x0019CFE8), UINT32_C(0x00416B41),
          UINT32_C(0x00219410), UINT32_C(0x005C5995)},
         {UINT32_C(0x0020EA67), UINT32_C(0x0065B659), UINT32_C(0x0011B640),
          UINT32_C(0x00C05AB3), UINT32_C(0x0015BD93), UINT32_C(0x003F4CB7),
          UINT32_C(0x000CD5AA), UINT32_C(0x006203FA), UINT32_C(0x001FA8A5),
          UINT32_C(0x001E6F64), UINT32_C(0x006FB8E7)},
         {UINT32_C(0x00B6FEF4), UINT32_C(0x0053F0CE), UINT32_C(0x006B1E51),
          UINT32_C(0x0033476B), UINT32_C(0x00419002), UINT32_C(0x006F619C),
          UINT32_C(0x005CAF14), UINT32_C(0x001753E9), UINT32_C(0x0038FC4F),
          UINT32_C(0x007F5E19), UINT32_C(0x000F12D2)}},
        {{UINT32_C(0x009F6EBB), UINT32_C(0x004C08FE), UINT32_C(0x006E7C4E),
          UINT32_C(0x00F1680D), UINT32_C(0x00446C66), UINT32_C(0x00350BF6),
          UINT32_C(0x006A96E3), UINT32_C(0x005AEE9A), UINT32_C(0x006935BB),
          UINT32_C(0x0073B4C5), UINT32_C(0x004AC140)},
         {UINT32_C(0x00E5225C), UINT32_C(0x007B671B), UINT32_C(0x007606A0),
          UINT32_C(0x00938508), UINT32_C(0x007625D7), UINT32_C(0x001E3B8F),
          UINT32_C(0x002EAE28), UINT32_C(0x00318F8C), UINT32_C(0x001CC7F0),
          UINT32_C(0x0010B56D), UINT32_C(0x0049C97C)},
         {UINT32_C(0x00DE3FDD), UINT32_C(0x0008CA79), UINT32_C(0x000A0585),
          UINT32_C(0x0056E5A5), UINT32_C(0x003CF77E), UINT32_C(0x002C9DD5),
          UINT32_C(0x006D3D48), UINT32_C(0x00B152EB), UINT32_C(0x001C6B5C),
          UINT32_C(0x00755FAF), UINT32_C(0x002B73E8)}},
        {{UINT32_C(0x004F175B), UINT32_C(0x0074B90C), UINT32_C(0x007C4EB9),
          UINT32_C(0x00CB92B7), UINT32_C(0x00592AE2), UINT32_C(0x0056E0F5),
          UINT32_C(0x007528E2), UINT32_C(0x00893263), UINT32_C(0x0006AD16),
          UINT32_C(0x0070AA41), UINT32_C(0x0062E83F)},
         {UINT32_C(0x00058482), UINT32_C(0x00171A5D), UINT32_C(0x0047C331),
          UINT32_C(0x00DF7758), UINT32_C(0x002B4A00), UINT32_C(0x001FCAD8),
          UINT32_C(0x006A1943), UINT32_C(0x00B73575), UINT32_C(0x00171180),
          UINT32_C(0x005C1148), UINT32_C(0x0012A36A)},
         {UINT32_C(0x008C51AE), UINT32_C(0x0020EE14), UINT32_C(0x00549F62),
          UINT32_C(0x00CD3EB6), UINT32_C(0x0048A700), UINT32_C(0x00596B28),
          UINT32_C(0x002F99A4), UINT32_C(0x00010422), UINT32_C(0x001769F4),
          UINT32_C(0x00225E79), UINT32_C(0x0067F6DE)}},
        {{UINT32_C(0x009E9178), UINT32_C(0x0005D43C), UINT32_C(0x0012A802),
          UINT32_C(0x0086A260), UINT32_C(0x0063C91C), UINT32_C(0x0063084E),
          UINT32_C(0x0046B7E1), UINT32_C(0x00A17B2D), UINT32_C(0x0051323E),
          UINT32_C(0x0046B359), UINT32_C(0x00037634)},
         {UINT32_C(0x000A3DA8), UINT32_C(0x001949B0), UINT32_C(0x0005E3B9),
          UINT32_C(0x002467F0), UINT32_C(0x004BCC28), UINT32_C(0x00501570),
          UINT32_C(0x003D5412), UINT32_C(0x00CD0573), UINT32_C(0x0040ECA4),
          UINT32_C(0x0017C942), UINT32_C(0x001AACE2)},
         {UINT32_C(0x00427572), UINT32_C(0x002D0E76), UINT32_C(0x006FC301),
          UINT32_C(0x0011516D), UINT32_C(0x003B6F32), UINT32_C(0x0056D53F),
          UINT32_C(0x002E9447), UINT32_C(0x00909115), UINT32_C(0x007DC768),
          UINT32_C(0x00553F21), UINT32_C(0x003A0976)}},
    },
    {
        {{UINT32_C(0x0018835D), UINT32_C(0x001FCF9F), UINT32_C(0x001AD610),
          UINT32_C(0x007D30EC), UINT32_C(0x006C910C), UINT32_C(0x003FB1A8),
          UINT32_C(0x001464A4), UINT32_C(0x0027680C), UINT32_C(0x007E54DB),
          UINT32_C(0x005EDD4B), UINT32_C(0x005A8976)},
         {UINT32_C(0x0037E1C3), UINT32_C(0x0016BF14), UINT32_C(0x0006AA36),
          UINT32_C(0x0040A33E), UINT32_C(0x006DC0DF), UINT32_C(0x00242FA2),
          UINT32_C(0x00274EA4), UINT32_C(0x000084D9), UINT32_C(0x0068A677),
          UINT32_C(0x004FE2AF), UINT32_C(0x001D11B4)},
         {UINT32_C(0x003DC1C7), UINT32_C(0x00792137), UINT32_C(0x002CB33F),
          UINT32_C(0x007A0801), UINT32_C(0x000056E3), UINT32_C(0x00339206),
          UINT32_C(0x003D0090), UINT32_C(0x00F96044), UINT32_C(0x00537FE0),
          UINT32_C(0x006358EF), UINT32_C(0x001EE2D0)}},
        {{UINT32_C(0x0029CB03), UINT32_C(0x000FF813), UINT32_C(0x00067660),
          UINT32_C(0x00E00898), UINT32_C(0x000083A1), UINT32_C(0x00516E88),
          UINT32_C(0x00543512), UINT32_C(0x00EFA310), UINT32_C(0x005ED95E),
          UINT32_C(0x0048147D), UINT32_C(0x003958DB)},
         {UINT32_C(0x0084596C), UINT32_C(0x006BC632), UINT32_C(0x0037EAF8),
          UINT32_C(0x00148F47), UINT32_C(0x002DE2D2), UINT32_C(0x007E4DA4),
          UINT32_C(0x0021A371), UINT32_C(0x00BF0084), UINT32_C(0x003EFBC7),
          UINT32_C(0x0079976E), UINT32_C(0x004BAEFB)},
         {UINT32_C(0x000DA1C1), UINT32_C(0x00310E2C), UINT32_C(0x0030B00D),
          UINT32_C(0x007060D1), UINT32_C(0x000F8A0E), UINT32_C(0x005B1EC0),
          UINT32_C(0x0066C96D), UINT32_C(0x008BB679), UINT32_C(0x0031F9DB),
          UINT32_C(0x0032C38D), UINT32_C(0x003C1F72)}},
        {{UINT32_C(0x00BCFE19), UINT32_C(0x005B51F8), UINT32_C(0x0037A870),
          UINT32_C(0x00898A49), UINT32_C(0x007BD853), UINT32_C(0x0064DA75),
          UINT32_C(0x0017497A), UINT32_C(0x0021BE3C), UINT32_C(0x0026C48F),
          UINT32_C(0x0035A56E), UINT32_C(0x00079EFC)},
         {UINT32_C(0x006AEFDE), UINT32_C(0x007113F8), UINT32_C(0x007013EA),
          UINT32_C(0x004A8F2E), UINT32_C(0x0038D8C7), UINT32_C(0x003E2B86),
          UINT32_C(0x006499EE), UINT32_C(0x00EF9DD1), UINT32_C(0x00541109),
          UINT32_C(0x007F4211), UINT32_C(0x002650ED)},
         {UINT32_C(0x00E8730E), UINT32_C(0x007EF6E8), UINT32_C(0x00053E89),
          UINT32_C(0x00B164EC), UINT32_C(0x004CFEB8), UINT32_C(0x002674CE),
          UINT32_C(0x006FD69F), UINT32_C(0x00862ADC), UINT32_C(0x0074A714),
          UINT32_C(0x004E95F1), UINT32_C(0x00440342)}},
        {{UINT32_C(0x0086A1D1), UINT32_C(0x0009570F), UINT32_C(0x0021E028),
          UINT32_C(0x00D8EF33), UINT32_C(0x0030D08A), UINT32_C(0x005EE53C),
          UINT32_C(0x005F27BE), UINT32_C(0x00315BEF), UINT32_C(0x0030A287),
          UINT32_C(0x00395DE5), UINT32_C(0x00694DAA)},
         {UINT32_C(0x004E499A), UINT32_C(0x0024FC80), UINT32_C(0x0045DA5E),
          UINT32_C(0x004542DE), UINT32_C(0x005A6E71), UINT32_C(0x0013BB0D),
          UINT32_C(0x0000E551), UINT32_C(0x0009BB0D), UINT32_C(0x00710991),
          UINT32_C(0x00116881), UINT32_C(0x0075A138)},
         {UINT32_C(0x0039EB19), UINT32_C(0x0067187C), UINT32_C(0x00636CC0),
          UINT32_C(0x0094DCEC), UINT32_C(0x001AF3DD), UINT32_C(0x002B8639),
          UINT32_C(0x006554E7), UINT32_C(0x00E605EB), UINT32_C(0x0061639B),
          UINT32_C(0x0077500A), UINT32_C(0x003193ED)}},
        {{UINT32_C(0x0001E326), UINT32_C(0x00352C31), UINT32_C(0x004A76AB),
          UINT32_C(0x00D940B5), UINT32_C(0x006329C5), UINT32_C(0x001AFE72),
          UINT32_C(0x005E4FE9), UINT32_C(0x00F56D9B), UINT32_C(0x003E7DB3),
          UINT32_C(0x00054566), UINT32_C(0x004D0C77)},
         {UINT32_C(0x00B344FC), UINT32_C(0x001F79B7), UINT32_C(0x0050F482),
          UINT32_C(0x0098D267), UINT32_C(0x001C2619), UINT32_C(0x006BBD65),
          UINT32_C(0x00346286), UINT32_C(0x008CAD0C), UINT32_C(0x000FC617),
          UINT32_C(0x0032B373), UINT32_C(0x007978E3)},
         {UINT32_C(0x00C94333), UINT32_C(0x006B14A8), UINT32_C(0x001075C5),
          UINT32_C(0x00BDD939), UINT32_C(0x0000EE70), UINT32_C(0x00188183),
          UINT32_C(0x007398E2), UINT32_C(0x0009184A), UINT32_C(0x007DF798),
          UINT32_C(0x0057B14F), UINT32_C(0x0021B6A8)}},
        {{UINT32_C(0x00F232E3), UINT32_C(0x00449C35), UINT32_C(0x000CC2CD),
          UINT32_C(0x0003BBFB), UINT32_C(0x006E022E), UINT32_C(0x005937F0),
          UINT32_C(0x000D3400), UINT32_C(0x00A3335B), UINT32_C(0x003797FA),
          UINT32_C(0x007E0C16), UINT32_C(0x0068AB4A)},
         {UINT32_C(0x00655243), UINT32_C(0x0076EF0C), UINT32_C(0x007A5D62),
          UINT32_C(0x0087B99B), UINT32_C(0x007F5266), UINT32_C(0x00129263),
          UINT32_C(0x0052B578), UINT32_C(0x00CC7106), UINT32_C(0x004128A7),
          UINT32_C(0x0074AFC9), UINT32_C(0x001A0821)},
         {UINT32_C(0x0000FE7F), UINT32_C(0x0058CC38), UINT32_C(0x004ACE7A),
          UINT32_C(0x0027824A), UINT32_C(0x0071BB43), UINT32_C(0x004CD1C5),
          UINT32_C(0x001DE8E5), UINT32_C(0x00FD97F3), UINT32_C(0x004164E0),
          UINT32_C(0x00542935), UINT32_C(0x0015EE68)}},
        {{UINT32_C(0x0056201E), UINT32_C(0x00572A88), UINT32_C(0x005A66B9),
          UINT32_C(0x00A30E5F), UINT32_C(0x000E20E3), UINT32_C(0x000898BF),
          UINT32_C(0x003CC5C7), UINT32_C(0x000170BD), UINT32_C(0x003813EE),
          UINT32_C(0x00079874), UINT32_C(0x006A5F45)},
         {UINT32_C(0x00C07AE0), UINT32_C(0x00680DE0), UINT32_C(0x0012D0D6),
          UINT32_C(0x00641F64), UINT32_C(0x000E6F53), UINT32_C(0x000A48AC),
          UINT32_C(0x002EBABE), UINT32_C(0x0017E4D0), UINT32_C(0x006B3042),
          UINT32_C(0x0047391C), UINT32_C(0x003FC7B5)},
         {UINT32_C(0x008AE200), UINT32_C(0x00651B7E), UINT32_C(0x000F51C8),
          UINT32_C(0x0034F2DB), UINT32_C(0x00418121), UINT32_C(0x0052E806),
          UINT32_C(0x004B57A3), UINT32_C(0x00801E3B), UINT32_C(0x006E2553),
          UINT32_C(0x0017FE29), UINT32_C(0x000371F5)}},
        {{UINT32_C(0x00E0B571), UINT32_C(0x0043B547), UINT32_C(0x0015696B),
          UINT32_C(0x00732AF4), UINT32_C(0x007B6282), UINT32_C(0x007276FC),
          UINT32_C(0x00489692), UINT32_C(0x00536F42), UINT32_C(0x00241953),
          UINT32_C(0x0047679B), UINT32_C(0x0017A0AD)},
         {UINT32_C(0x0071332D), UINT32_C(0x00010B6E), UINT32_C(0x007EE392),
          UINT32_C(0x00A7B31C), UINT32_C(0x004921ED), UINT32_C(0x000B32BD),
          UINT32_C(0x001187FE), UINT32_C(0x00913F47), UINT32_C(0x000B27ED),
          UINT32_C(0x0043B431), UINT32_C(0x0055085B)},
         {UINT32_C(0x00E2C137), UINT32_C(0x001AD933), UINT32_C(0x000D0367),
          UINT32_C(0x007E38F0), UINT32_C(0x0003205C), UINT32_C(0x0014599A),
          UINT32_C(0x001E8DE6), UINT32_C(0x007A88FA), UINT32_C(0x0077198A),
          UINT32_C(0x0031A753), UINT32_C(0x002FE194)}},
        {{UINT32_C(0x00682B6C), UINT32_C(0x00652986), UINT32_C(0x001F417F),
          UINT32_C(0x00DE4F1B), UINT32_C(0x000EF1F1), UINT32_C(0x00036E7F),
          UINT32_C(0x005AD789), UINT32_C(0x00AE8DB4), UINT32_C(0x0076B58D),
          UINT32_C(0x002E3942), UINT32_C(0x006F7B10)},
         {UINT32_C(0x009E5FA2), UINT32_C(0x0008F3C7), UINT32_C(0x00043BDE),
          UINT32_C(0x0052A794), UINT32_C(0x0044679C), UINT32_C(0x007F28C0),
          UINT32_C(0x00382AB6), UINT32_C(0x00395191), UINT32_C(0x0039B531),
          UINT32_C(0x0036F81A), UINT32_C(0x00795887)},
         {UINT32_C(0x00F70354), UINT32_C(0x00328B59), UINT32_C(0x003D01A3),
          UINT32_C(0x00C9F394), UINT32_C(0x0022F3EA), UINT32_C(0x006F350F),
          UINT32_C(0x0058C2B3), UINT32_C(0x0040634F), UINT32_C(0x00320F7C),
          UINT32_C(0x006D4BEE), UINT32_C(0x0057CCC9)}},
        {{UINT32_C(0x0093E9B9), UINT32_C(0x006BDC73), UINT32_C(0x0045A4A0),
          UINT32_C(0x0051A4D7), UINT32_C(0x005C415B), UINT32_C(0x0066BA11),
          UINT32_C(0x005AE064), UINT32_C(0x005245BE), UINT32_C(0x00013BE1),
          UINT32_C(0x006DD2F6), UINT32_C(0x006F9657)},
         {UINT32_C(0x00E1468C), UINT32_C(0x0004FD78), UINT32_C(0x005800DD),
          UINT32_C(0x0091E526), UINT32_C(0x0012067D), UINT32_C(0x00481847),
          UINT32_C(0x000A9B9D), UINT32_C(0x00D7FB7D), UINT32_C(0x0008AF37),
          UINT32_C(0x00598387), UINT32_C(0x0053520A)},
         {UINT32_C(0x009411F0), UINT32_C(0x00415CC6), UINT32_C(0x003CFAB9),
          UINT32_C(0x00BEE1C6), UINT32_C(0x0011F354), UINT32_C(0x00540421),
          UINT32_C(0x005B7E1D), UINT32_C(0x00AA9283), UINT32_C(0x00107457),
          UINT32_C(0x000DC4A7), UINT32_C(0x0058459D)}},
        {{UINT32_C(0x00531F14), UINT32_C(0x001009E3), UINT32_C(0x0073B657),
          UINT32_C(0x00DC0159), UINT32_C(0x00665408), UINT32_C(0x0008FF55),
          UINT32_C(0x007D3DF9), UINT32_C(0x0031F848), UINT32_C(0x000B2149),
          UINT32_C(0x002E956E), UINT32_C(0x00749370)},
         {UINT32_C(0x005EACBA), UINT32_C(0x0013C60C), UINT32_C(0x001B1DCF),
          UINT32_C(0x00BE1671), UINT32_C(0x005C94CC), UINT32_C(0x00437E24),
          UINT32_C(0x00214684), UINT32_C(0x00207638), UINT32_C(0x002A0D18),
          UINT32_C(0x001FE161), UINT32_C(0x001F585E)},
         {UINT32_C(0x00FF468A), UINT32_C(0x004D3FD3), UINT32_C(0x000BBF50),
          UINT32_C(0x00011D45), UINT32_C(0x001070E3), UINT32_C(0x00438229),
          UINT32_C(0x0059AB03), UINT32_C(0x000AA329), UINT32_C(0x00315647),
          UINT32_C(0x000CCF61), UINT32_C(0x000F1485)}},
        {{UINT32_C(0x009129E3), UINT32_C(0x0012693C), UINT32_C(0x004E8200),
          UINT32_C(0x00027D67), UINT32_C(0x00704244), UINT32_C(0x00210CBE),
          UINT32_C(0x00192F03), UINT32_C(0x005142DE), UINT32_C(0x001A84FB),
          UINT32_C(0x003745C5), UINT32_C(0x0064B88E)},
         {UINT32_C(0x004FD4A7), UINT32_C(0x0039F707), UINT32_C(0x0027F3CE),
          UINT32_C(0x00C4924F), UINT32_C(0x004C1AC9), UINT32_C(0x005438CD),
          UINT32_C(0x0029571A), UINT32_C(0x009A42D3), UINT32_C(0x00437704),
          UINT32_C(0x00466DF8), UINT32_C(0x004CAE5F)},
         {UINT32_C(0x00CF7ED3), UINT32_C(0x00327495), UINT32_C(0x0023D857),
          UINT32_C(0x003ADCC1), UINT32_C(0x00783A07), UINT32_C(0x0070C835),
          UINT32_C(0x0063B436), UINT32_C(0x00AF64FA), UINT32_C(0x0043E2D6),
          UINT32_C(0x0020BE90), UINT32_C(0x001B98ED)}},
        {{UINT32_C(0x00A4C93E), UINT32_C(0x004F4C24), UINT32_C(0x007F4777),
          UINT32_C(0x000570E1), UINT32_C(0x003BF809), UINT32_C(0x001608A3),
          UINT32_C(0x0073E59E), UINT32_C(0x00B32CC7), UINT32_C(0x005501BF),
          UINT32_C(0x00027C79), UINT32_C(0x00793FF1)},
         {UINT32_C(0x0019DCA8), UINT32_C(0x001F6A93), UINT32_C(0x007FB9FE),
          UINT32_C(0x00389B1A), UINT32_C(0x0032A1FC), UINT32_C(0x001BD948),
          UINT32_C(0x00661278), UINT32_C(0x00C98D56), UINT32_C(0x005CDFB2),
          UINT32_C(0x004DF065), UINT32_C(0x004DA8B5)},
         {UINT32_C(0x008AF82E), UINT32_C(0x0049B701), UINT32_C(0x002A4058),
          UINT32_C(0x00937D79), UINT32_C(0x003E1209), UINT32_C(0x006D0224),
          UINT32_C(0x00654260), UINT32_C(0x0089CD81), UINT32_C(0x0031BE9C),
          UINT32_C(0x007F915B), UINT32_C(0x00545CEB)}},
        {{UINT32_C(0x00606923), UINT32_C(0x00535E19), UINT32_C(0x004B9720),
          UINT32_C(0x007CF3AC), UINT32_C(0x0009FEE2), UINT32_C(0x0021E97C),
          UINT32_C(0x00417B8D), UINT32_C(0x0005C8DC), UINT32_C(0x00278974),
          UINT32_C(0x00220131), UINT32_C(0x0001F1EA)},
         {UINT32_C(0x0051751A), UINT32_C(0x0022CFFE), UINT32_C(0x0065EAE7),
          UINT32_C(0x0002D66F), UINT32_C(0x001CFE5E), UINT32_C(0x0004D158),
          UINT32_C(0x003519D9), UINT32_C(0x00C3A426), UINT32_C(0x0050617E),
          UINT32_C(0x003DFC62), UINT32_C(0x000C1E82)},
         {UINT32_C(0x0054E7F7), UINT32_C(0x003159A5), UINT32_C(0x00536937),
          UINT32_C(0x00743C99), UINT32_C(0x002C5B9E), UINT32_C(0x003E5BAC),
          UINT32_C(0x00713E45), UINT32_C(0x001C8CC3), UINT32_C(0x00379CFC),
          UINT32_C(0x0040AC92), UINT32_C(0x004FD6BA)}},
        {{UINT32_C(0x00375AB1), UINT32_C(0x00558A76), UINT32_C(0x00127659),
          UINT32_C(0x002E36DD), UINT32_C(0x0017A438), UINT32_C(0x0037085E),
          UINT32_C(0x00460F93), UINT32_C(0x00DC21D1), UINT32_C(0x006D33E1),
          UINT32_C(0x004422EB), UINT32_C(0x003B5755)},
         {UINT32_C(0x003DA9D4), UINT32_C(0x002248E9), UINT32_C(0x0003EAEC),
          UINT32_C(0x00D43561), UINT32_C(0x001BF26D), UINT32_C(0x005C5F13),
          UINT32_C(0x00626C12), UINT32_C(0x00B918DF), UINT32_C(0x0065CADE),
          UINT32_C(0x00717357), UINT32_C(0x005BB6B5)},
         {UINT32_C(0x004024E3), UINT32_C(0x0046B939), UINT32_C(0x004E46F3),
          UINT32_C(0x001511AF), UINT32_C(0x00776BD7), UINT32_C(0x00320079),
          UINT32_C(0x007332F3), UINT32_C(0x007C852A), UINT32_C(0x007816D9),
          UINT32_C(0x0068E65E), UINT32_C(0x0045DBB4)}},
        {{UINT32_C(0x00E0980A), UINT32_C(0x003CBFDB), UINT32_C(0x0074F5AA),
          UINT32_C(0x001EA8DD), UINT32_C(0x002C4C50), UINT32_C(0x00311D45),
          UINT32_C(0x0039C948), UINT32_C(0x0055988C), UINT32_C(0x005B5B63),
          UINT32_C(0x0041F40E), UINT32_C(0x002C22D4)},
         {UINT32_C(0x00AE7440), UINT32_C(0x002ACDC7), UINT32_C(0x0078F4C2),
          UINT32_C(0x00199902), UINT32_C(0x001399A7), UINT32_C(0x003DDA20),
          UINT32_C(0x00658152), UINT32_C(0x005F646B), UINT32_C(0x006CA0B4),
          UINT32_C(0x0073AF03), UINT32_C(0x00052716)},
         {UINT32_C(0x007F80B6), UINT32_C(0x003B2F94), UINT32_C(0x00373B83),
          UINT32_C(0x00EA1B50), UINT32_C(0x00716C1C), UINT32_C(0x0047A779),
          UINT32_C(0x006E478C), UINT32_C(0x009F1C86), UINT32_C(0x00772EF9),
          UINT32_C(0x003C8BB5), UINT32_C(0x0003308F)}},
    },
    {
        {{UINT32_C(0x003A0F8A), UINT32_C(0x00239C42), UINT32_C(0x002C8997),
          UINT32_C(0x00CB1312), UINT32_C(0x000BA3BF), UINT32_C(0x00433E65),
          UINT32_C(0x0004E93D), UINT32_C(0x00BC78BF), UINT32_C(0x0016315F),
          UINT32_C(0x007839DA), UINT32_C(0x00072F5D)},
         {UINT32_C(0x00431B86), UINT32_C(0x0052B0C9), UINT32_C(0x007B6B54),
          UINT32_C(0x0004C666), UINT32_C(0x0021961D), UINT32_C(0x00630D09),
          UINT32_C(0x0026B7F8), UINT32_C(0x004B5633), UINT32_C(0x0036F916),
          UINT32_C(0x007D08E5), UINT32_C(0x0009930C)},
         {UINT32_C(0x006B4F00), UINT32_C(0x001D54A4), UINT32_C(0x00692DA9),
          UINT32_C(0x0098837B), UINT32_C(0x0065006A), UINT32_C(0x000704B4),
          UINT32_C(0x00424FF9), UINT32_C(0x00C25B4D), UINT32_C(0x005B6D40),
          UINT32_C(0x0054C25F), UINT32_C(0x00387F6A)}},
        {{UINT32_C(0x002F5512), UINT32_C(0x00654E2D), UINT32_C(0x00668342),
          UINT32_C(0x00E2D4F5), UINT32_C(0x001FCDE3), UINT32_C(0x00396F10),
          UINT32_C(0x00474238), UINT32_C(0x001DFA11), UINT32_C(0x00536151),
          UINT32_C(0x0060AA5E), UINT32_C(0x0008AD4A)},
         {UINT32_C(0x00E427F0), UINT32_C(0x005588B0), UINT32_C(0x002428A4),
          UINT32_C(0x00DD098A), UINT32_C(0x002CAE12), UINT32_C(0x0005B6F7),
          UINT32_C(0x0003677F), UINT32_C(0x00B1209B), UINT32_C(0x000B9B82),
          UINT32_C(0x007C5D85), UINT32_C(0x00503BBE)},
         {UINT32_C(0x003F3EE8), UINT32_C(0x0071495D), UINT32_C(0x004CA27C),
          UINT32_C(0x0057A105), UINT32_C(0x0038F4ED), UINT32_C(0x00019BA2),
          UINT32_C(0x00772D19), UINT32_C(0x000C8A1F), UINT32_C(0x002F07EB),
          UINT32_C(0x0009E4D0), UINT32_C(0x007AF54E)}},
        {{UINT32_C(0x009ED4D4), UINT32_C(0x0035F235), UINT32_C(0x003B3EFE),
          UINT32_C(0x00B5687B), UINT32_C(0x007499B6), UINT32_C(0x004AEC1B),
          UINT32_C(0x0011D9F3), UINT32_C(0x00F934CA), UINT32_C(0x00626585),
          UINT32_C(0x000054A1), UINT32_C(0x0078ADC1)},
         {UINT32_C(0x000F1D44), UINT32_C(0x00067701), UINT32_C(0x002F00AB),
          UINT32_C(0x008F68D3), UINT32_C(0x001C1C3B), UINT32_C(0x0049876F),
          UINT32_C(0x007C5E0C), UINT32_C(0x00958C8A), UINT32_C(0x00585F0E),
          UINT32_C(0x003980D2), UINT32_C(0x004C2741)},
         {UINT32_C(0x004F0FD3), UINT32_C(0x000519E7), UINT32_C(0x00455DA2),
          UINT32_C(0x00E4A2DB), UINT32_C(0x0006A399), UINT32_C(0x005F65BE),
          UINT32_C(0x0010C895), UINT32_C(0x00E1E39C), UINT32_C(0x000D2D62),
          UINT32_C(0x005489D3), UINT32_C(0x0064F071)}},
        {{UINT32_C(0x0075E904), UINT32_C(0x0034221F), UINT32_C(0x006C3082),
          UINT32_C(0x001DFF10), UINT32_C(0x007E367A), UINT32_C(0x0044654C),
          UINT32_C(0x00214821), UINT32_C(0x0044A5B4), UINT32_C(0x0065B709),
          UINT32_C(0x001D42EE), UINT32_C(0x001A97DC)},
         {UINT32_C(0x000AD26D), UINT32_C(0x0030E580), UINT32_C(0x006F212B),
          UINT32_C(0x00100EE6), UINT32_C(0x000F70F2), UINT32_C(0x007CA3C6),
          UINT32_C(0x0017EBC5), UINT32_C(0x00311581), UINT32_C(0x002B7BB5),
          UINT32_C(0x00283403), UINT32_C(0x0001A581)},
         {UINT32_C(0x009043A2), UINT32_C(0x001B3A77), UINT32_C(0x000FA816),
          UINT32_C(0x004B33BF), UINT32_C(0x005044AF), UINT32_C(0x001584E4),
          UINT32_C(0x00417440), UINT32_C(0x0006E3D6), UINT32_C(0x00723841),
          UINT32_C(0x007F1CF7), UINT32_C(0x000A8A6D)}},
        {{UINT32_C(0x0041A857), UINT32_C(0x0034AE73), UINT32_C(0x004BB287),
          UINT32_C(0x0086B914), UINT32_C(0x006E11BD), UINT32_C(0x000F0FD1),
          UINT32_C(0x003523A3), UINT32_C(0x00860736), UINT32_C(0x00209E71),
          UINT32_C(0x0021DE0F), UINT32_C(0x001A4D6B)},
         {UINT32_C(0x00F677D7), UINT32_C(0x0048110C), UINT32_C(0x0008F908),
          UINT32_C(0x009BD400), UINT32_C(0x006B94AC), UINT32_C(0x0037B8C2),
          UINT32_C(0x0049854F), UINT32_C(0x005C4D2F), UINT32_C(0x003BA675),
          UINT32_C(0x003C2448), UINT32_C(0x0069DC9E)},
         {UINT32_C(0x00025EE8), UINT32_C(0x00384FEB), UINT32_C(0x001C9DA4),
          UINT32_C(0x0005A77C), UINT32_C(0x002CEBD8), UINT32_C(0x00253110),
          UINT32_C(0x0048F3CD), UINT32_C(0x0045CF49), UINT32_C(0x004771CD),
          UINT32_C(0x003F1990), UINT32_C(0x004F6051)}},
        {{UINT32_C(0x0003827C), UINT32_C(0x001F112D), UINT32_C(0x0071973A),
          UINT32_C(0x0099B096), UINT32_C(0x000C34F8), UINT32_C(0x00397A6A),
          UINT32_C(0x007F26ED), UINT32_C(0x00AD0CF1), UINT32_C(0x0069FEEE),
          UINT32_C(0x000BB893), UINT32_C(0x00525AC4)},
         {UINT32_C(0x001DE590), UINT32_C(0x0063FBAF), UINT32_C(0x0013D7EB),
          UINT32_C(0x0042B275), UINT32_C(0x006A1E1B), UINT32_C(0x0010AEC1),
          UINT32_C(0x002C78D4), UINT32_C(0x0028C9DF), UINT32_C(0x0045854B),
          UINT32_C(0x000789E7), UINT32_C(0x0000074F)},
         {UINT32_C(0x008229BF), UINT32_C(0x0069358B), UINT32_C(0x001825AA),
          UINT32_C(0x00181405), UINT32_C(0x0073C536), UINT32_C(0x0063CEAE),
          UINT32_C(0x0015B3E7), UINT32_C(0x008AFF3F), UINT32_C(0x0010C89E),
          UINT32_C(0x001AF589), UINT32_C(0x004E0053)}},
        {{UINT32_C(0x0028B9C8), UINT32_C(0x0034B521), UINT32_C(0x003323FB),
          UINT32_C(0x00B663B0), UINT32_C(0x004DAAA1), UINT32_C(0x002616C9),
          UINT32_C(0x0032ABEB), UINT32_C(0x0040C353), UINT32_C(0x00086B1D),
          UINT32_C(0x0036A180), UINT32_C(0x003E458C)},
         {UINT32_C(0x00DCF643), UINT32_C(0x002C1001), UINT32_C(0x0052AA3F),
          UINT32_C(0x008E9839), UINT32_C(0x004A4E69), UINT32_C(0x001D0E16),
          UINT32_C(0x002FBB80), UINT32_C(0x0043D67E), UINT32_C(0x00478B72),
          UINT32_C(0x000F9CA3), UINT32_C(0x003AFC42)},
         {UINT32_C(0x000DD581), UINT32_C(0x007F85BA), UINT32_C(0x00436D63),
          UINT32_C(0x00598F90), UINT32_C(0x0014B661), UINT32_C(0x00306562),
          UINT32_C(0x00761E86), UINT32_C(0x006C8505), UINT32_C(0x00568527),
          UINT32_C(0x006FDA5A), UINT32_C(0x001ADEA0)}},
        {{UINT32_C(0x00890618), UINT32_C(0x000CD682), UINT32_C(0x007E36FF),
          UINT32_C(0x00303F12), UINT32_C(0x000B2A6D), UINT32_C(0x00415241),
          UINT32_C(0x0025E2C9), UINT32_C(0x00F0E829), UINT32_C(0x003A20B1),
          UINT32_C(0x005BC247), UINT32_C(0x004FF913)},
         {UINT32_C(0x00C77BF2), UINT32_C(0x004061A1), UINT32_C(0x007197C8),
          UINT32_C(0x0031BABD), UINT32_C(0x00096046), UINT32_C(0x0045E643),
          UINT32_C(0x006FC909), UINT32_C(0x00088EA6), UINT32_C(0x0070A7CA),
          UINT32_C(0x0047B9D0), UINT32_C(0x0050FC48)},
         {UINT32_C(0x00FF3B98), UINT32_C(0x0078A423), UINT32_C(0x0012CC47),
          UINT32_C(0x00AE776D), UINT32_C(0x0021495E), UINT32_C(0x004A3D73),
          UINT32_C(0x00391838), UINT32_C(0x001B0893), UINT32_C(0x000AE103),
          UINT32_C(0x005B7AC6), UINT32_C(0x0058F1A9)}},
        {{UINT32_C(0x00CAFC7E), UINT32_C(0x006D3D15), UINT32_C(0x003C5E44),
          UINT32_C(0x00246F10), UINT32_C(0x00654846), UINT32_C(0x0021C350),
          UINT32_C(0x0029B782), UINT32_C(0x000F3BD5), UINT32_C(0x004BA2E1),
          UINT32_C(0x000A9813), UINT32_C(0x003DAC06)},
         {UINT32_C(0x0081BEA5), UINT32_C(0x005AF750), UINT32_C(0x00231C43),
          UINT32_C(0x00546577), UINT32_C(0x00021D5B), UINT32_C(0x00323115),
          UINT32_C(0x00360EE5), UINT32_C(0x000B7CC0), UINT32_C(0x0042696D),
          UINT32_C(0x0014F38B), UINT32_C(0x00637448)},
         {UINT32_C(0x00A10272), UINT32_C(0x0071C42F), UINT32_C(0x005406FB),
          UINT32_C(0x000728BE), UINT32_C(0x003FCE1D), UINT32_C(0x00721CC4),
          UINT32_C(0x00100233), UINT32_C(0x004A237D), UINT32_C(0x004691A1),
          UINT32_C(0x00439B3E), UINT32_C(0x0068BD1A)}},
        {{UINT32_C(0x0071251B), UINT32_C(0x00458B01), UINT32_C(0x001E79F8),
          UINT32_C(0x00A77A06), UINT32_C(0x0074587F), UINT32_C(0x004DC1C1),
          UINT32_C(0x003C2A50), UINT32_C(0x00255231), UINT32_C(0x005C317C),
          UINT32_C(0x005F9187), UINT32_C(0x007CF5A9)},
         {UINT32_C(0x00098FC2), UINT32_C(0x004404A1), UINT32_C(0x004D04AA),
          UINT32_C(0x00464189), UINT32_C(0x0061D090), UINT32_C(0x004E55A9),
          UINT32_C(0x0007C50C), UINT32_C(0x00BE825E), UINT32_C(0x00047F09),
          UINT32_C(0x000F7FEC), UINT32_C(0x004E42D2)},
         {UINT32_C(0x00355E39), UINT32_C(0x002EAC10), UINT32_C(0x00511D7E),
          UINT32_C(0x00DF8195), UINT32_C(0x00658D53), UINT32_C(0x007832A7),
          UINT32_C(0x00688D4A), UINT32_C(0x00630A34), UINT32_C(0x001BF382),
          UINT32_C(0x005EC16E), UINT32_C(0x007B68AD)}},
        {{UINT32_C(0x001E2363), UINT32_C(0x0019180C), UINT32_C(0x000DC491),
          UINT32_C(0x00624C85), UINT32_C(0x004109E5), UINT32_C(0x006F6F9F),
          UINT32_C(0x004721E1), UINT32_C(0x00D89B92), UINT32_C(0x002ACCC1),
          UINT32_C(0x0061C123), UINT32_C(0x00181505)},
         {UINT32_C(0x005EFABB), UINT32_C(0x007D8985), UINT32_C(0x003C5BC1),
          UINT32_C(0x008F46C5), UINT32_C(0x0012B4ED), UINT32_C(0x0064759D),
          UINT32_C(0x0058D2A9), UINT32_C(0x00C25CA8), UINT32_C(0x004E6E93),
          UINT32_C(0x00503BCA), UINT32_C(0x00512AAA)},
         {UINT32_C(0x009D1E14), UINT32_C(0x0074E51E), UINT32_C(0x00245CBE),
          UINT32_C(0x00248FE2), UINT32_C(0x0074BDE8), UINT32_C(0x007C074E),
          UINT32_C(0x0017A40E), UINT32_C(0x00FF34D1), UINT32_C(0x0057C0A3),
          UINT32_C(0x0061B3EC), UINT32_C(0x002677F7)}},
        {{UINT32_C(0x00CBF754), UINT32_C(0x0031AE3A), UINT32_C(0x000333B8),
          UINT32_C(0x00D4CF78), UINT32_C(0x003E4A56), UINT32_C(0x004E6254),
          UINT32_C(0x002FDBFA), UINT32_C(0x00144B0A), UINT32_C(0x0063FB6C),
          UINT32_C(0x0001D4D6), UINT32_C(0x007A746B)},
         {UINT32_C(0x007361B9), UINT32_C(0x005E8B06), UINT32_C(0x0044E134),
          UINT32_C(0x00F4A847), UINT32_C(0x002329D4), UINT32_C(0x0044A5B7),
          UINT32_C(0x0044F475), UINT32_C(0x00E61797), UINT32_C(0x0057FB07),
          UINT32_C(0x00051722), UINT32_C(0x00241ECD)},
         {UINT32_C(0x0022A500), UINT32_C(0x0039A10A), UINT32_C(0x0042D3D5),
          UINT32_C(0x00B567D7), UINT32_C(0x0069AA6D), UINT32_C(0x00711215),
          UINT32_C(0x002ED8DD), UINT32_C(0x0000F158), UINT32_C(0x006238BE),
          UINT32_C(0x005650C3), UINT32_C(0x003D21E8)}},
        {{UINT32_C(0x008B1C66), UINT32_C(0x0019194C), UINT32_C(0x0014BFE9),
          UINT32_C(0x00748F3D), UINT32_C(0x003CCE8F), UINT32_C(0x0048A87C),
          UINT32_C(0x006A1307), UINT32_C(0x006C2B73), UINT32_C(0x002E37A4),
          UINT32_C(0x0029162B), UINT32_C(0x0027B4E0)},
         {UINT32_C(0x00C21F63), UINT32_C(0x0017955E), UINT32_C(0x0078C143),
          UINT32_C(0x00D5E54D), UINT32_C(0x00472F3D), UINT32_C(0x004E389E),
          UINT32_C(0x005B4F76), UINT32_C(0x004D23EE), UINT32_C(0x00532138),
          UINT32_C(0x00573674), UINT32_C(0x000CEC4B)},
         {UINT32_C(0x003DE334), UINT32_C(0x006224BD), UINT32_C(0x000F9C6F),
          UINT32_C(0x000A9395), UINT32_C(0x00345EAF), UINT32_C(0x00107910),
          UINT32_C(0x0070F64A), UINT32_C(0x00D236DB), UINT32_C(0x0027355D),
          UINT32_C(0x00164A6C), UINT32_C(0x007D6D7B)}},
        {{UINT32_C(0x008036C4), UINT32_C(0x004A1B91), UINT32_C(0x00471A92),
          UINT32_C(0x000371CC), UINT32_C(0x0056DDC0), UINT32_C(0x00130FC6),
          UINT32_C(0x00287BA8), UINT32_C(0x00EAE3E3), UINT32_C(0x006A7352),
          UINT32_C(0x004599E9), UINT32_C(0x00382164)},
         {UINT32_C(0x00B93F79), UINT32_C(0x001E39CD), UINT32_C(0x00201F70),
          UINT32_C(0x00EF1A24), UINT32_C(0x00530D3A), UINT32_C(0x005F0647),
          UINT32_C(0x00008EE9), UINT32_C(0x00AD3CCB), UINT32_C(0x006C1B3C),
          UINT32_C(0x0020B88E), UINT32_C(0x00041C1B)},
         {UINT32_C(0x006CAF3D), UINT32_C(0x004A21AC), UINT32_C(0x001CDE34),
          UINT32_C(0x0033DD65), UINT32_C(0x005353D1), UINT32_C(0x00584597),
          UINT32_C(0x00114182), UINT32_C(0x005CB2D9), UINT32_C(0x0053A5C5),
          UINT32_C(0x0071C2D4), UINT32_C(0x00244902)}},
        {{UINT32_C(0x00AFA1E1), UINT32_C(0x0064BD11), UINT32_C(0x0014D188),
          UINT32_C(0x0002D13D), UINT32_C(0x000E36B8), UINT32_C(0x00354548),
          UINT32_C(0x007BB62C), UINT32_C(0x0078DF50), UINT32_C(0x0007E63C),
          UINT32_C(0x00056614), UINT32_C(0x003D5281)},
         {UINT32_C(0x0028DBA4), UINT32_C(0x0004B80D), UINT32_C(0x004F7FF8),
          UINT32_C(0x007AA928), UINT32_C(0x0004ABF1), UINT32_C(0x0023D6FF),
          UINT32_C(0x0024B312), UINT32_C(0x00023B8A), UINT32_C(0x003B5EDA),
          UINT32_C(0x005AF149), UINT32_C(0x0019FA7B)},
         {UINT32_C(0x00FF5820), UINT32_C(0x00358792), UINT32_C(0x0021A28C),
          UINT32_C(0x00C51693), UINT32_C(0x000098EE), UINT32_C(0x00034266),
          UINT32_C(0x0017723D), UINT32_C(0x00C2DDF5), UINT32_C(0x0000E734),
          UINT32_C(0x006DAEF6), UINT32_C(0x0071F7AD)}},
        {{UINT32_C(0x002E3AD1), UINT32_C(0x00779201), UINT32_C(0x0068289F),
          UINT32_C(0x003F10B0), UINT32_C(0x002706C6), UINT32_C(0x002565B5),
          UINT32_C(0x00190C55), UINT32_C(0x0033BD51), UINT32_C(0x000E50F0),
          UINT32_C(0x00500819), UINT32_C(0x007C9D08)},
         {UINT32_C(0x00FA9D8B), UINT32_C(0x0038DA3F), UINT32_C(0x00693AB6),
          UINT32_C(0x00DE9636), UINT32_C(0x005BB3EC), UINT32_C(0x0065B8F5),
          UINT32_C(0x00481E55), UINT32_C(0x0061E2C9), UINT32_C(0x001643BF),
          UINT32_C(0x00559100), UINT32_C(0x005F79E6)},
         {UINT32_C(0x002E392D), UINT32_C(0x001227D4), UINT32_C(0x00293C7B),
          UINT32_C(0x00F92994), UINT32_C(0x002FAEE0), UINT32_C(0x00322623),
          UINT32_C(0x00434FE2), UINT32_C(0x00199F46), UINT32_C(0x005C180E),
          UINT32_C(0x005C3B89), UINT32_C(0x00566D13)}},
    },
    {
        {{UINT32_C(0x004EC5D5), UINT32_C(0x00019CD6), UINT32_C(0x000BBD45),
          UINT32_C(0x002193AE), UINT32_C(0x003165C6), UINT32_C(0x003D8D74),
          UINT32_C(0x0045059C), UINT32_C(0x008CF60C), UINT32_C(0x00487D06),
          UINT32_C(0x005A9D09), UINT32_C(0x007FC6E0)},
         {UINT32_C(0x0037B42A), UINT32_C(0x0001A576), UINT32_C(0x000A750D),
          UINT32_C(0x0045B5DD), UINT32_C(0x006F0E25), UINT32_C(0x001F4ABF),
          UINT32_C(0x0056E0BA), UINT32_C(0x0065543B), UINT32_C(0x00564264),
          UINT32_C(0x005ADF58), UINT32_C(0x0069B6ED)},
         {UINT32_C(0x0008533F), UINT32_C(0x00673ED0), UINT32_C(0x0053205E),
          UINT32_C(0x0060EB8B), UINT32_C(0x000C0216), UINT32_C(0x00483015),
          UINT32_C(0x001974B0), UINT32_C(0x00610E9B), UINT32_C(0x00116E88),
          UINT32_C(0x001B41F9), UINT32_C(0x003650B6)}},
        {{UINT32_C(0x001E87C9), UINT32_C(0x00485E07), UINT32_C(0x00473783),
          UINT32_C(0x009E4755), UINT32_C(0x0077AAE3), UINT32_C(0x006CF828),
          UINT32_C(0x0064BF5B), UINT32_C(0x00DD1614), UINT32_C(0x001F3F82),
          UINT32_C(0x000CDC74), UINT32_C(0x005A217D)},
         {UINT32_C(0x007E7DC9), UINT32_C(0x004C5775), UINT32_C(0x00577E73),
          UINT32_C(0x0054AC09), UINT32_C(0x00692B30), UINT32_C(0x005074AE),
          UINT32_C(0x0047FE74), UINT32_C(0x00814E6D), UINT32_C(0x00611400),
          UINT32_C(0x0054CC45), UINT32_C(0x0015E56A)},
         {UINT32_C(0x0015BF7F), UINT32_C(0x00720BB8), UINT32_C(0x00128FA3),
          UINT32_C(0x00EFF525), UINT32_C(0x002C2B16), UINT32_C(0x000032B7),
          UINT32_C(0x0056FA97), UINT32_C(0x006F11AE), UINT32_C(0x0021850D),
          UINT32_C(0x00755623), UINT32_C(0x0004F475)}},
        {{UINT32_C(0x00DC22CC), UINT32_C(0x005EC978), UINT32_C(0x0075EFAB),
          UINT32_C(0x00A67308), UINT32_C(0x001E17A7), UINT32_C(0x00471117),
          UINT32_C(0x003D0D24), UINT32_C(0x0002B875), UINT32_C(0x005CD15C),
          UINT32_C(0x00791ED0), UINT32_C(0x00519998)},
         {UINT32_C(0x006DFC80), UINT32_C(0x003F2D4F), UINT32_C(0x00325346),
          UINT32_C(0x00CF12E4), UINT32_C(0x000EF0D1), UINT32_C(0x001D644B),
          UINT32_C(0x006A72BB), UINT32_C(0x00E463A5), UINT32_C(0x00393A41),
          UINT32_C(0x003CFAB9), UINT32_C(0x0045AEDD)},
         {UINT32_C(0x008CF232), UINT32_C(0x00355CF6), UINT32_C(0x0018330B),
          UINT32_C(0x00E357D6), UINT32_C(0x002E9F65), UINT32_C(0x00192AA3),
          UINT32_C(0x0050E31A), UINT32_C(0x00542788), UINT32_C(0x005C03B4),
          UINT32_C(0x0016A4C7), UINT32_C(0x004D85E7)}},
        {{UINT32_C(0x005E1B7C), UINT32_C(0x005396EB), UINT32_C(0x002953AC),
          UINT32_C(0x0066F21B), UINT32_C(0x000744EB), UINT32_C(0x002BB57E),
          UINT32_C(0x00777EC0), UINT32_C(0x000C0908), UINT32_C(0x0063DFBF),
          UINT32_C(0x0019D0E1), UINT32_C(0x0053259A)},
         {UINT32_C(0x00092AA9), UINT32_C(0x006D7967), UINT32_C(0x007A34E9),
          UINT32_C(0x0088833B), UINT32_C(0x007C72DF), UINT32_C(0x00154B92),
          UINT32_C(0x0004BEAB), UINT32_C(0x00709C21), UINT32_C(0x001A33F0),
          UINT32_C(0x007A15C7), UINT32_C(0x007CB81A)},
         {UINT32_C(0x00427117), UINT32_C(0x0060010D), UINT32_C(0x002FB59E),
          UINT32_C(0x0081DF74), UINT32_C(0x001C3AFF), UINT32_C(0x00325E38),
          UINT32_C(0x003E9EB8), UINT32_C(0x00AFA8A7), UINT32_C(0x002A7623),
          UINT32_C(0x001940EC), UINT32_C(0x00678A03)}},
        {{UINT32_C(0x0089D0DA), UINT32_C(0x005DD15D), UINT32_C(0x0007F023),
          UINT32_C(0x0084B837), UINT32_C(0x0059A36B), UINT32_C(0x0014C4F9),
          UINT32_C(0x005A10B9), UINT32_C(0x00EEA8E6), UINT32_C(0x0032C712),
          UINT32_C(0x00207104), UINT32_C(0x0051CC16)},
         {UINT32_C(0x00788203), UINT32_C(0x005BFACD), UINT32_C(0x0034A954),
          UINT32_C(0x00E0855B), UINT32_C(0x007F6BD9), UINT32_C(0x003EAB3A),
          UINT32_C(0x0061F2A3), UINT32_C(0x004BF5E9), UINT32_C(0x00203A8B),
          UINT32_C(0x007B37C8), UINT32_C(0x00403792)},
         {UINT32_C(0x0091D84F), UINT32_C(0x006D1BA7), UINT32_C(0x0036B343),
          UINT32_C(0x00319941), UINT32_C(0x000AB9F0), UINT32_C(0x007A1DC1),
          UINT32_C(0x0031981C), UINT32_C(0x00F43266), UINT32_C(0x0008BB75),
          UINT32_C(0x00485DAF), UINT32_C(0x000A9D74)}},
        {{UINT32_C(0x00243625), UINT32_C(0x005BB841), UINT32_C(0x00525C95),
          UINT32_C(0x00BC116B), UINT32_C(0x003839C3), UINT32_C(0x0041EB1B),
          UINT32_C(0x00221F6B), UINT32_C(0x004EC4AF), UINT32_C(0x006D762B),
          UINT32_C(0x005C54FC), UINT32_C(0x007A1F82)},
         {UINT32_C(0x00C26515), UINT32_C(0x0000ED4C), UINT32_C(0x001E3B09),
          UINT32_C(0x000BEE59), UINT32_C(0x0063B3C5), UINT32_C(0x00275D84),
          UINT32_C(0x005756AC), UINT32_C(0x00AF183D), UINT32_C(0x0009020F),
          UINT32_C(0x006E34C7), UINT32_C(0x004DC0C8)},
         {UINT32_C(0x005B4EB0), UINT32_C(0x0039BA12), UINT32_C(0x000B792C),
          UINT32_C(0x00D6B962), UINT32_C(0x003DFD9C), UINT32_C(0x004C2A8A),
          UINT32_C(0x0022A010), UINT32_C(0x0067AFC9), UINT32_C(0x0057241F),
          UINT32_C(0x003A2810), UINT32_C(0x005C0C2A)}},
        {{UINT32_C(0x000F4D05), UINT32_C(0x001E1BA9), UINT32_C(0x003631A2),
          UINT32_C(0x002B29CE), UINT32_C(0x007696F1), UINT32_C(0x00313416),
          UINT32_C(0x001EB6D3), UINT32_C(0x008D3460), UINT32_C(0x003364E8),
          UINT32_C(0x000429EE), UINT32_C(0x007E4FF6)},
         {UINT32_C(0x00B98C72), UINT32_C(0x0043B1EA), UINT32_C(0x001A581F),
          UINT32_C(0x00295970), UINT32_C(0x0010F065), UINT32_C(0x0039E01E),
          UINT32_C(0x002E33BA), UINT32_C(0x0047DF1A), UINT32_C(0x0041422E),
          UINT32_C(0x005A3851), UINT32_C(0x003FA8FF)},
         {UINT32_C(0x00A29B8E), UINT32_C(0x00702100), UINT32_C(0x001DFDFB),
          UINT32_C(0x006CA23F), UINT32_C(0x004F967B), UINT32_C(0x00417B94),
          UINT32_C(0x0073DD0F), UINT32_C(0x00D08DB3), UINT32_C(0x001C4006),
          UINT32_C(0x003F92DD), UINT32_C(0x000F2874)}},
        {{UINT32_C(0x008D5AC8), UINT32_C(0x0031FE77), UINT32_C(0x001EE7B5),
          UINT32_C(0x0017DDC8), UINT32_C(0x0010F7EB), UINT32_C(0x0078D778),
          UINT32_C(0x0051CF94), UINT32_C(0x00737A58), UINT32_C(0x002F2CA5),
          UINT32_C(0x00314107), UINT32_C(0x003BD0E4)},
         {UINT32_C(0x00708B31), UINT32_C(0x006E4EE5), UINT32_C(0x0072B5A8),
          UINT32_C(0x00B52995), UINT32_C(0x001B7B7D), UINT32_C(0x002853F5),
          UINT32_C(0x0071F2D3), UINT32_C(0x0090F660), UINT32_C(0x004E8BAE),
          UINT32_C(0x002AEB93), UINT32_C(0x007FE004)},
         {UINT32_C(0x0074B2E7), UINT32_C(0x0036F8CC), UINT32_C(0x005A0B4F),
          UINT32_C(0x006AC18C), UINT32_C(0x0023E1D0), UINT32_C(0x0018985D),
          UINT32_C(0x0044A4EA), UINT32_C(0x00EAD750), UINT32_C(0x001739B5),
          UINT32_C(0x00259016), UINT32_C(0x0069AD16)}},
        {{UINT32_C(0x00236C6C), UINT32_C(0x002BDCF8), UINT32_C(0x00737342),
          UINT32_C(0x00FFE743), UINT32_C(0x0067A4D9), UINT32_C(0x0066D9C1),
          UINT32_C(0x00681F98), UINT32_C(0x00599DED), UINT32_C(0x0057B074),
          UINT32_C(0x001B308C), UINT32_C(0x0056660C)},
         {UINT32_C(0x008B1685), UINT32_C(0x0027EA30), UINT32_C(0x0033CA2D),
          UINT32_C(0x0098682C), UINT32_C(0x003F67B0), UINT32_C(0x000C48E9),
          UINT32_C(0x0068A704), UINT32_C(0x001C6E9D), UINT32_C(0x00123D06),
          UINT32_C(0x0050A933), UINT32_C(0x0053C6D2)},
         {UINT32_C(0x00917431), UINT32_C(0x002F64FB), UINT32_C(0x00755E9F),
          UINT32_C(0x005D5B9F), UINT32_C(0x006E0959), UINT32_C(0x00068928),
          UINT32_C(0x00509EB9), UINT32_C(0x009B4E5E), UINT32_C(0x0000998B),
          UINT32_C(0x0008DAD5), UINT32_C(0x00166125)}},
        {{UINT32_C(0x00074C2E), UINT32_C(0x00652B12), UINT32_C(0x00202F3C),
          UINT32_C(0x0042EBC1), UINT32_C(0x00500167), UINT32_C(0x005CDA15),
          UINT32_C(0x001381DA), UINT32_C(0x00CC99F2), UINT32_C(0x005723CA),
          UINT32_C(0x001A8C3B), UINT32_C(0x003D5615)},
         {UINT32_C(0x00F1A960), UINT32_C(0x007380F6), UINT32_C(0x007009E1),
          UINT32_C(0x002EF85B), UINT32_C(0x003B246B), UINT32_C(0x0041405A),
          UINT32_C(0x001314CB), UINT32_C(0x009FDCD0), UINT32_C(0x0077491F),
          UINT32_C(0x003266F0), UINT32_C(0x00760D83)},
         {UINT32_C(0x00C7818E), UINT32_C(0x007864C2), UINT32_C(0x00244BD0),
          UINT32_C(0x00F38B08), UINT32_C(0x00688F96), UINT32_C(0x0075C705),
          UINT32_C(0x00278414), UINT32_C(0x00B2BFF0), UINT32_C(0x0067126E),
          UINT32_C(0x007B7F7E), UINT32_C(0x00078584)}},
        {{UINT32_C(0x00FE7A46), UINT32_C(0x005584A0), UINT32_C(0x005CDD0D),
          UINT32_C(0x004CF5A8), UINT32_C(0x0019CFA7), UINT32_C(0x00087268),
          UINT32_C(0x007FF3F3), UINT32_C(0x0087ACFD), UINT32_C(0x0017BD41),
          UINT32_C(0x004A35AA), UINT32_C(0x0014C1BD)},
         {UINT32_C(0x005A4052), UINT32_C(0x0032A135), UINT32_C(0x007056C3),
          UINT32_C(0x0072F85C), UINT32_C(0x0024A79D), UINT32_C(0x003D7320),
          UINT32_C(0x0007F20D), UINT32_C(0x00BEA269), UINT32_C(0x0032F584),
          UINT32_C(0x00557016), UINT32_C(0x0009FC2A)},
         {UINT32_C(0x006E58DD), UINT32_C(0x00414D93), UINT32_C(0x001D725A),
          UINT32_C(0x00A5AB22), UINT32_C(0x000AD82C), UINT32_C(0x00613758),
          UINT32_C(0x00773B55), UINT32_C(0x00D581E2), UINT32_C(0x006342F0),
          UINT32_C(0x0022FB1A), UINT32_C(0x0028348D)}},
        {{UINT32_C(0x00940FFC), UINT32_C(0x006CC86D), UINT32_C(0x0033CE22),
          UINT32_C(0x00A629FD), UINT32_C(0x0032CFBE), UINT32_C(0x0011788D),
          UINT32_C(0x0027D4E5), UINT32_C(0x0043AC5A), UINT32_C(0x003CEEDA),
          UINT32_C(0x000057AA), UINT32_C(0x00614643)},
         {UINT32_C(0x00312B0D), UINT32_C(0x003C3AC9), UINT32_C(0x00126EE8),
          UINT32_C(0x003F24AF), UINT32_C(0x0006CC19), UINT32_C(0x00656F4F),
          UINT32_C(0x004E91CF), UINT32_C(0x001EC679), UINT32_C(0x0010294E),
          UINT32_C(0x0023F5E9), UINT32_C(0x00181E45)},
         {UINT32_C(0x008C28C0), UINT32_C(0x0016213D), UINT32_C(0x0010311C),
          UINT32_C(0x00744F39), UINT32_C(0x006CEA8C), UINT32_C(0x0066FA80),
          UINT32_C(0x005E3FA5), UINT32_C(0x001D61EB), UINT32_C(0x001E0217),
          UINT32_C(0x002F43D5), UINT32_C(0x001EAA47)}},
        {{UINT32_C(0x00854120), UINT32_C(0x005AA732), UINT32_C(0x00608D63),
          UINT32_C(0x003180C7), UINT32_C(0x00513CB6), UINT32_C(0x0013362E),
          UINT32_C(0x00492465), UINT32_C(0x00A81D33), UINT32_C(0x0038349B),
          UINT32_C(0x004C1B24), UINT32_C(0x0061B37A)},
         {UINT32_C(0x00B13B17), UINT32_C(0x003AC756), UINT32_C(0x004C7642),
          UINT32_C(0x0067AC7F), UINT32_C(0x003F23FE), UINT32_C(0x0058AA8A),
          UINT32_C(0x000B0977), UINT32_C(0x001D605C), UINT32_C(0x00713AD3),
          UINT32_C(0x0060BA46), UINT32_C(0x0041F85A)},
         {UINT32_C(0x00E37216), UINT32_C(0x0043A345), UINT32_C(0x00223407),
          UINT32_C(0x00256265), UINT32_C(0x0011C42F), UINT32_C(0x00138466),
          UINT32_C(0x004733CC), UINT32_C(0x003535EC), UINT32_C(0x0001BC86),
          UINT32_C(0x003D1B86), UINT32_C(0x004AE885)}},
        {{UINT32_C(0x00DC7159), UINT32_C(0x001767D1), UINT32_C(0x007AE0AB),
          UINT32_C(0x000878A1), UINT32_C(0x004FB364), UINT32_C(0x0033B0EF),
          UINT32_C(0x00742CF7), UINT32_C(0x00C6A756), UINT32_C(0x0078F550),
          UINT32_C(0x00711F81), UINT32_C(0x0065A975)},
         {UINT32_C(0x00439414), UINT32_C(0x0015AFCF), UINT32_C(0x007EE055),
          UINT32_C(0x00DD71C8), UINT32_C(0x0045F656), UINT32_C(0x00624A54),
          UINT32_C(0x003005FC), UINT32_C(0x00BC7324), UINT32_C(0x0055842E),
          UINT32_C(0x00165223), UINT32_C(0x0037D7D6)},
         {UINT32_C(0x0019F5F5), UINT32_C(0x0020DA89), UINT32_C(0x006C89E9),
          UINT32_C(0x00F41904), UINT32_C(0x003D42D9), UINT32_C(0x0055B010),
          UINT32_C(0x00175AA4), UINT32_C(0x00B1A716), UINT32_C(0x000083AA),
          UINT32_C(0x000A3EEC), UINT32_C(0x00170791)}},
        {{UINT32_C(0x004FE6CC), UINT32_C(0x0027F794), UINT32_C(0x001DEE50),
          UINT32_C(0x001D47C8), UINT32_C(0x00482A07), UINT32_C(0x00728F59),
          UINT32_C(0x002E9AFB), UINT32_C(0x00416429), UINT32_C(0x00429086),
          UINT32_C(0x002A42B2), UINT32_C(0x0004C02A)},
         {UINT32_C(0x004B5C0A), UINT32_C(0x0035BDD6), UINT32_C(0x001FE687),
          UINT32_C(0x0027AF73), UINT32_C(0x001A6D1A), UINT32_C(0x000FBB3C),
          UINT32_C(0x00213872), UINT32_C(0x00EC16E6), UINT32_C(0x00636F30),
          UINT32_C(0x00415394), UINT32_C(0x0012E98D)},
         {UINT32_C(0x008D471A), UINT32_C(0x0069E551), UINT32_C(0x00493B23),
          UINT32_C(0x006ED1B7), UINT32_C(0x0047A3E8), UINT32_C(0x00249EC8),
          UINT32_C(0x003D1AD9), UINT32_C(0x0023243A), UINT32_C(0x0078E657),
          UINT32_C(0x007AC870), UINT32_C(0x0039DDDE)}},
        {{UINT32_C(0x00775995), UINT32_C(0x004A7A1C), UINT32_C(0x00706EC4),
          UINT32_C(0x005F75E2), UINT32_C(0x0023C9C5), UINT32_C(0x0024BCC8),
          UINT32_C(0x007DC9AC), UINT32_C(0x001AFD8F), UINT32_C(0x006B47B9),
          UINT32_C(0x004747AD), UINT32_C(0x007B084D)},
         {UINT32_C(0x000A9B5C), UINT32_C(0x007354D6), UINT32_C(0x006AB188),
          UINT32_C(0x00A35212), UINT32_C(0x003BDF54), UINT32_C(0x00027ACE),
          UINT32_C(0x002AEF52), UINT32_C(0x00C45137), UINT32_C(0x0058980E),
          UINT32_C(0x0050F4F0), UINT32_C(0x003E8EEE)},
         {UINT32_C(0x00EB4040), UINT32_C(0x00618F66), UINT32_C(0x0031BE2F),
          UINT32_C(0x00A1EAB0), UINT32_C(0x00038AF5), UINT32_C(0x0001EC6C),
          UINT32_C(0x0007421A), UINT32_C(0x0051B097), UINT32_C(0x0043B013),
          UINT32_C(0x0060ED3F), UINT32_C(0x0036AC22)}},
    },
    {
        {{UINT32_C(0x00A77DAC), UINT32_C(0x001DB3FD), UINT32_C(0x00389F54),
          UINT32_C(0x00564F6D), UINT32_C(0x0063CF36), UINT32_C(0x001C46B6),
          UINT32_C(0x00311686), UINT32_C(0x00E982B7), UINT32_C(0x00027D0F),
          UINT32_C(0x0050362C), UINT32_C(0x001AEA89)},
         {UINT32_C(0x0002BC8A), UINT32_C(0x0036E565), UINT32_C(0x00342121),
          UINT32_C(0x00ED769D), UINT32_C(0x002A92C3), UINT32_C(0x00325573),
          UINT32_C(0x0018B0E6), UINT32_C(0x00F11ADF), UINT32_C(0x004B976B),
          UINT32_C(0x00069633), UINT32_C(0x004FFC55)},
         {UINT32_C(0x00FD2FC2), UINT32_C(0x00401B07), UINT32_C(0x00294102),
          UINT32_C(0x008EA5E0), UINT32_C(0x0030664D), UINT32_C(0x00142EC3),
          UINT32_C(0x003FE051), UINT32_C(0x00F56D80), UINT32_C(0x0039FEE3),
          UINT32_C(0x00000AEB), UINT32_C(0x006D0C5F)}},
        {{UINT32_C(0x00DD75D0), UINT32_C(0x0044A3C3), UINT32_C(0x005A506D),
          UINT32_C(0x00EC37FD), UINT32_C(0x00316AD7), UINT32_C(0x0000D14B),
          UINT32_C(0x00204476), UINT32_C(0x0051C697), UINT32_C(0x00083305),
          UINT32_C(0x0058D9B1), UINT32_C(0x0070999F)},
         {UINT32_C(0x00E5E93B), UINT32_C(0x006C0468), UINT32_C(0x0009400E),
          UINT32_C(0x0075F8FF), UINT32_C(0x0035F1A4), UINT32_C(0x00506173),
          UINT32_C(0x002364E5), UINT32_C(0x00414D87), UINT32_C(0x0008B606),
          UINT32_C(0x00686394), UINT32_C(0x006AA160)},
         {UINT32_C(0x006BD2E2), UINT32_C(0x007A2B7F), UINT32_C(0x005F146B),
          UINT32_C(0x00369239), UINT32_C(0x00079C56), UINT32_C(0x00062C62),
          UINT32_C(0x0077B021), UINT32_C(0x003A29B7), UINT32_C(0x002867FF),
          UINT32_C(0x0055BBB5), UINT32_C(0x0005D042)}},
        {{UINT32_C(0x00D8B383), UINT32_C(0x007DD79F), UINT32_C(0x00789E04),
          UINT32_C(0x00F01FC3), UINT32_C(0x0009A138), UINT32_C(0x00652EF9),
          UINT32_C(0x0004B8F0), UINT32_C(0x00CD2D9B), UINT32_C(0x002A8B21),
          UINT32_C(0x00711CBB), UINT32_C(0x003F479B)},
         {UINT32_C(0x0084BAF3), UINT32_C(0x00077832), UINT32_C(0x005C239A),
          UINT32_C(0x0060F9F5), UINT32_C(0x00157D11), UINT32_C(0x004B04C3),
          UINT32_C(0x00398E89), UINT32_C(0x00DFDB89), UINT32_C(0x004B12EE),
          UINT32_C(0x002B1159), UINT32_C(0x0070A1B8)},
         {UINT32_C(0x00034A6C), UINT32_C(0x004C89E9), UINT32_C(0x0022DB26),
          UINT32_C(0x00C601C3), UINT32_C(0x0032D1E3), UINT32_C(0x002503F8),
          UINT32_C(0x00632E24), UINT32_C(0x00EA95FF), UINT32_C(0x000FDA31),
          UINT32_C(0x002551E5), UINT32_C(0x004F9CB2)}},
        {{UINT32_C(0x00862AF4), UINT32_C(0x00210FBD), UINT32_C(0x001E6C80),
          UINT32_C(0x00400C9B), UINT32_C(0x000AA70B), UINT32_C(0x00736A0A),
          UINT32_C(0x005A2C84), UINT32_C(0x00C59BAC), UINT32_C(0x000BB281),
          UINT32_C(0x002CF44A), UINT32_C(0x0061244E)},
         {UINT32_C(0x00856415), UINT32_C(0x0016B2C8), UINT32_C(0x00735666),
          UINT32_C(0x001D23DD), UINT32_C(0x001134A9), UINT32_C(0x0017A1D4),
          UINT32_C(0x0026D1CE), UINT32_C(0x00EECC13), UINT32_C(0x0049B200),
          UINT32_C(0x00153F17), UINT32_C(0x0011DDAE)},
         {UINT32_C(0x00007750), UINT32_C(0x001134B0), UINT32_C(0x004BF17D),
          UINT32_C(0x00D2423A), UINT32_C(0x003D2D7B), UINT32_C(0x00085F31),
          UINT32_C(0x00227CDF), UINT32_C(0x00654CD2), UINT32_C(0x00082CCF),
          UINT32_C(0x007C9CA4), UINT32_C(0x004C47BB)}},
        {{UINT32_C(0x00362E13), UINT32_C(0x003A337C), UINT32_C(0x002BBA20),
          UINT32_C(0x0001856C), UINT32_C(0x0002C1DE), UINT32_C(0x000E51A8),
          UINT32_C(0x00457B9D), UINT32_C(0x0057882E), UINT32_C(0x0037807D),
          UINT32_C(0x0015B24A), UINT32_C(0x001A74BE)},
         {UINT32_C(0x003DB4CB), UINT32_C(0x004A35FE), UINT32_C(0x003DBBAB),
          UINT32_C(0x007E3023), UINT32_C(0x00194FC7), UINT32_C(0x0012B3C2),
          UINT32_C(0x00776FBF), UINT32_C(0x001E11B7), UINT32_C(0x002DB7AA),
          UINT32_C(0x001DED18), UINT32_C(0x0069ABF1)},
         {UINT32_C(0x005FE93D), UINT32_C(0x000BDB4C), UINT32_C(0x00250B7A),
          UINT32_C(0x008A93E9), UINT32_C(0x006F3CAC), UINT32_C(0x0003D972),
          UINT32_C(0x0057B7D4), UINT32_C(0x00D0FA5F), UINT32_C(0x0051BCDF),
          UINT32_C(0x0037A606), UINT32_C(0x0046594A)}},
        {{UINT32_C(0x00FD1F79), UINT32_C(0x00305A30), UINT32_C(0x000AD2AE),
          UINT32_C(0x00BFF664), UINT32_C(0x002CE671), UINT32_C(0x006F9E53),
          UINT32_C(0x00242322), UINT32_C(0x000EB83C), UINT32_C(0x000DA108),
          UINT32_C(0x007B1505), UINT32_C(0x001BB588)},
         {UINT32_C(0x00D17F2A), UINT32_C(0x0005A272), UINT32_C(0x0023C922),
          UINT32_C(0x00F5BEA6), UINT32_C(0x00534FEC), UINT32_C(0x00201847),
          UINT32_C(0x0057D139), UINT32_C(0x00553AC8), UINT32_C(0x0038D49D),
          UINT32_C(0x000D9A41), UINT32_C(0x0023FA44)},
         {UINT32_C(0x00A4394C), UINT32_C(0x001C1150), UINT32_C(0x000C4BC9),
          UINT32_C(0x0013E6EB), UINT32_C(0x0030FD03), UINT32_C(0x005496B5),
          UINT32_C(0x002200CE), UINT32_C(0x009477CB), UINT32_C(0x00167FB8),
          UINT32_C(0x001C4D58), UINT32_C(0x004FD444)}},
        {{UINT32_C(0x00D5EEEC), UINT32_C(0x0038E42A), UINT32_C(0x005A4364),
          UINT32_C(0x00980627), UINT32_C(0x007F2577), UINT32_C(0x003612FD),
          UINT32_C(0x004E0E1F), UINT32_C(0x0074D370), UINT32_C(0x00528EA6),
          UINT32_C(0x00018673), UINT32_C(0x004D7509)},
         {UINT32_C(0x0029610F), UINT32_C(0x004535B8), UINT32_C(0x00041762),
          UINT32_C(0x008EF8FD), UINT32_C(0x002AA59D), UINT32_C(0x00083B18),
          UINT32_C(0x00479122), UINT32_C(0x005A4922), UINT32_C(0x002CECA2),
          UINT32_C(0x00256BF4), UINT32_C(0x0043B7C3)},
         {UINT32_C(0x00B47D51), UINT32_C(0x00748FB4), UINT32_C(0x00584310),
          UINT32_C(0x00EE3A6C), UINT32_C(0x005E556A), UINT32_C(0x0052BA2B),
          UINT32_C(0x00500FBB), UINT32_C(0x00A9860A), UINT32_C(0x007A7128),
          UINT32_C(0x000D1669), UINT32_C(0x002C17E0)}},
        {{UINT32_C(0x0007B0B0), UINT32_C(0x005AEDEE), UINT32_C(0x0076ED54),
          UINT32_C(0x00274E51), UINT32_C(0x007BD17E), UINT32_C(0x000BE439),
          UINT32_C(0x000ED5DB), UINT32_C(0x00D7925E), UINT32_C(0x003F2A50),
          UINT32_C(0x002F0773), UINT32_C(0x003546F9)},
         {UINT32_C(0x001B8BC5), UINT32_C(0x00567960), UINT32_C(0x0038FA10),
          UINT32_C(0x00C2575C), UINT32_C(0x0027332E), UINT32_C(0x003071D4),
          UINT32_C(0x0078A9F6), UINT32_C(0x00BCAE5E), UINT32_C(0x00760FF1),
          UINT32_C(0x000E8EC0), UINT32_C(0x006EA98F)},
         {UINT32_C(0x00676ADD), UINT32_C(0x004ED26F), UINT32_C(0x0030B66C),
          UINT32_C(0x00E96FB8), UINT32_C(0x0055C569), UINT32_C(0x00323FB1),
          UINT32_C(0x00440986), UINT32_C(0x00F3E007), UINT32_C(0x005F14FE),
          UINT32_C(0x000E5FD2), UINT32_C(0x00585D5A)}},
        {{UINT32_C(0x0032D10F), UINT32_C(0x00607846), UINT32_C(0x00476A55),
          UINT32_C(0x00FE8A15), UINT32_C(0x0076749E), UINT32_C(0x00385608),
          UINT32_C(0x0029E6F6), UINT32_C(0x0098FFAC), UINT32_C(0x004086D0),
          UINT32_C(0x00587F0C), UINT32_C(0x0048DEFF)},
         {UINT32_C(0x008F82EA), UINT32_C(0x00011EC5), UINT32_C(0x001CAAB9),
          UINT32_C(0x00B6A781), UINT32_C(0x000436C9), UINT32_C(0x005DDE65),
          UINT32_C(0x002E38F8), UINT32_C(0x003A69F4), UINT32_C(0x003DF1D2),
          UINT32_C(0x0026F1E7), UINT32_C(0x003D5D6D)},
         {UINT32_C(0x007322DD), UINT32_C(0x001FFAF0), UINT32_C(0x0012A21B),
          UINT32_C(0x003853AE), UINT32_C(0x0001D98A), UINT32_C(0x0000748E),
          UINT32_C(0x00667AE8), UINT32_C(0x002F7BE9), UINT32_C(0x001686BF),
          UINT32_C(0x00050052), UINT32_C(0x0079765D)}},
        {{UINT32_C(0x00267E76), UINT32_C(0x000F5A32), UINT32_C(0x00798CB2),
          UINT32_C(0x0086E395), UINT32_C(0x00005266), UINT32_C(0x0025806B),
          UINT32_C(0x007EE459), UINT32_C(0x001EF2A7), UINT32_C(0x00034FD1),
          UINT32_C(0x0018C3F9), UINT32_C(0x0002FCBE)},
         {UINT32_C(0x004C278B), UINT32_C(0x0025C270), UINT32_C(0x00642402),
          UINT32_C(0x0024F47B), UINT32_C(0x006E96A1), UINT32_C(0x006B67FC),
          UINT32_C(0x00446A7C), UINT32_C(0x009B9BAB), UINT32_C(0x00774C2A),
          UINT32_C(0x005B2890), UINT32_C(0x0007534E)},
         {UINT32_C(0x00979647), UINT32_C(0x003D9A12), UINT32_C(0x0009C489),
          UINT32_C(0x002D6F0A), UINT32_C(0x007ED7E2), UINT32_C(0x004A5B1B),
          UINT32_C(0x005C265E), UINT32_C(0x006AF72E), UINT32_C(0x003BFD06),
          UINT32_C(0x0019EE72), UINT32_C(0x002BC98A)}},
        {{UINT32_C(0x0036094F), UINT32_C(0x0053D51E), UINT32_C(0x0039DA8E),
          UINT32_C(0x00C24407), UINT32_C(0x00754444), UINT32_C(0x00541686),
          UINT32_C(0x0038413C), UINT32_C(0x003D42D5), UINT32_C(0x002B576F),
          UINT32_C(0x0025B7F0), UINT32_C(0x00037AE8)},
         {UINT32_C(0x00FF6011), UINT32_C(0x00739A4B), UINT32_C(0x002CFD0E),
          UINT32_C(0x0034CBE5), UINT32_C(0x001C69C4), UINT32_C(0x00183B00),
          UINT32_C(0x0070A07F), UINT32_C(0x00416B07), UINT32_C(0x001873B9),
          UINT32_C(0x004C82C4), UINT32_C(0x0012AF7C)},
         {UINT32_C(0x009DAB06), UINT32_C(0x00006525), UINT32_C(0x005F4AC2),
          UINT32_C(0x0007B1CD), UINT32_C(0x0025B80B), UINT32_C(0x006B0D4A),
          UINT32_C(0x0017A0F3), UINT32_C(0x005371E3), UINT32_C(0x00786EF8),
          UINT32_C(0x005F7C4F), UINT32_C(0x0060CA6A)}},
        {{UINT32_C(0x00D21942), UINT32_C(0x0071B130), UINT32_C(0x0010AF58),
          UINT32_C(0x0032F341), UINT32_C(0x0073BD94), UINT32_C(0x0005C17D),
          UINT32_C(0x0052C1D1), UINT32_C(0x00AC355D), UINT32_C(0x007A20DB),
          UINT32_C(0x0041259A), UINT32_C(0x0044FFAB)},
         {UINT32_C(0x006E331F), UINT32_C(0x007C1495), UINT32_C(0x00648A89),
          UINT32_C(0x00C406A1), UINT32_C(0x000F1EB7), UINT32_C(0x0031E5FB),
          UINT32_C(0x006C8D65), UINT32_C(0x00C6C865), UINT32_C(0x004B7DF2),
          UINT32_C(0x0079769E), UINT32_C(0x002083D4)},
         {UINT32_C(0x00980997), UINT32_C(0x00485E01), UINT32_C(0x000FDD04),
          UINT32_C(0x00404293), UINT32_C(0x001BE19E), UINT32_C(0x00079D78),
          UINT32_C(0x0058EB9B), UINT32_C(0x005DBD87), UINT32_C(0x00702A47),
          UINT32_C(0x0039F856), UINT32_C(0x000ADFA8)}},
        {{UINT32_C(0x00601C16), UINT32_C(0x006BAD25), UINT32_C(0x000B27D9),
          UINT32_C(0x003CE316), UINT32_C(0x000BF903), UINT32_C(0x00305942),
          UINT32_C(0x0055AA45), UINT32_C(0x006C251C), UINT32_C(0x001C0764),
          UINT32_C(0x004DB871), UINT32_C(0x006990D9)},
         {UINT32_C(0x0035029C), UINT32_C(0x0056F54C), UINT32_C(0x0071AAA5),
          UINT32_C(0x003BB302), UINT32_C(0x00511665), UINT32_C(0x004B1D6D),
          UINT32_C(0x00792CCE), UINT32_C(0x009D6482), UINT32_C(0x005F2322),
          UINT32_C(0x0060FBFB), UINT32_C(0x003DAEFF)},
         {UINT32_C(0x005DFF91), UINT32_C(0x007B3C15), UINT32_C(0x003C73C5),
          UINT32_C(0x0099D915), UINT32_C(0x0047AF99), UINT32_C(0x005655E6),
          UINT32_C(0x001004A5), UINT32_C(0x001F3259), UINT32_C(0x0050988B),
          UINT32_C(0x005D60E0), UINT32_C(0x002BE896)}},
        {{UINT32_C(0x002B631C), UINT32_C(0x00372C82), UINT32_C(0x002A7F2A),
          UINT32_C(0x00E4A975), UINT32_C(0x006BB6B1), UINT32_C(0x0016BC52),
          UINT32_C(0x00473CD2), UINT32_C(0x00630310), UINT32_C(0x0016B945),
          UINT32_C(0x0001291B), UINT32_C(0x0059EF17)},
         {UINT32_C(0x00837D4E), UINT32_C(0x003216D3), UINT32_C(0x004E0701),
          UINT32_C(0x00823A76), UINT32_C(0x00567125), UINT32_C(0x0020E62F),
          UINT32_C(0x0036F8E4), UINT32_C(0x0091634B), UINT32_C(0x000CE9C9),
          UINT32_C(0x00712DF3), UINT32_C(0x003270FE)},
         {UINT32_C(0x00C8BD36), UINT32_C(0x00312227), UINT32_C(0x005851DE),
          UINT32_C(0x00989694), UINT32_C(0x003811BA), UINT32_C(0x0049DDC1),
          UINT32_C(0x0079916E), UINT32_C(0x00905B34), UINT32_C(0x00216CD3),
          UINT32_C(0x006E95D0), UINT32_C(0x0075102E)}},
        {{UINT32_C(0x000FE15B), UINT32_C(0x002132F9), UINT32_C(0x007302D9),
          UINT32_C(0x0074464C), UINT32_C(0x006545FE), UINT32_C(0x0025178A),
          UINT32_C(0x0037772B), UINT32_C(0x00F33555), UINT32_C(0x006628A7),
          UINT32_C(0x00639C7D), UINT32_C(0x0017AF1B)},
         {UINT32_C(0x0016001E), UINT32_C(0x00346FAA), UINT32_C(0x0055CC08),
          UINT32_C(0x006A26EC), UINT32_C(0x00272A70), UINT32_C(0x0073B255),
          UINT32_C(0x0066C9CC), UINT32_C(0x00E05374), UINT32_C(0x0031E05D),
          UINT32_C(0x007241E6), UINT32_C(0x0064D29A)},
         {UINT32_C(0x000904B8), UINT32_C(0x000ACC3E), UINT32_C(0x005C0BC8),
          UINT32_C(0x00708FA3), UINT32_C(0x006420DB), UINT32_C(0x0009EB68),
          UINT32_C(0x007DF36A), UINT32_C(0x0004C79A), UINT32_C(0x00688FB7),
          UINT32_C(0x001853DD), UINT32_C(0x00391517)}},
        {{UINT32_C(0x0045C93C), UINT32_C(0x001ADA91), UINT32_C(0x005BEF12),
          UINT32_C(0x00652E9C), UINT32_C(0x00109290), UINT32_C(0x00351B47),
          UINT32_C(0x00649B3C), UINT32_C(0x0093B9EA), UINT32_C(0x0031C49B),
          UINT32_C(0x006129D9), UINT32_C(0x0018C36F)},
         {UINT32_C(0x0039F849), UINT32_C(0x0076DDFD), UINT32_C(0x004D8E8B),
          UINT32_C(0x00CC9068), UINT32_C(0x00257C98), UINT32_C(0x0022E647),
          UINT32_C(0x0010E3A5), UINT32_C(0x0071627F), UINT32_C(0x004D6728),
          UINT32_C(0x001036C0), UINT32_C(0x00293E1C)},
         {UINT32_C(0x00A0883B), UINT32_C(0x0047D948), UINT32_C(0x0029727E),
          UINT32_C(0x007C052C), UINT32_C(0x0064B122), UINT32_C(0x003228B8),
          UINT32_C(0x0004B695), UINT32_C(0x00850591), UINT32_C(0x00086718),
          UINT32_C(0x0035AE85), UINT32_C(0x001253D1)}},
    },
    {
        {{UINT32_C(0x00B27045), UINT32_C(0x007C6BA4), UINT32_C(0x000BBA03),
          UINT32_C(0x00D9B116), UINT32_C(0x007774E3), UINT32_C(0x0042950E),
          UINT32_C(0x0017A228), UINT32_C(0x002DD987), UINT32_C(0x0078FFD1),
          UINT32_C(0x006CC9E6), UINT32_C(0x002D3C1B)},
         {UINT32_C(0x00A56D1F), UINT32_C(0x002BB5F0), UINT32_C(0x005FB594),
          UINT32_C(0x0009E3C8), UINT32_C(0x0040549B), UINT32_C(0x0008E785),
          UINT32_C(0x005E84B9), UINT32_C(0x0003AE65), UINT32_C(0x00476B1B),
          UINT32_C(0x004B6DB7), UINT32_C(0x00255FEE)},
         {UINT32_C(0x001D2AC3), UINT32_C(0x0053D8C2), UINT32_C(0x0053ABBA),
          UINT32_C(0x0062F195), UINT32_C(0x00583DCF), UINT32_C(0x005C7CC7),
          UINT32_C(0x0016B088), UINT32_C(0x00E1FD84), UINT32_C(0x003BDF9C),
          UINT32_C(0x004B6242), UINT32_C(0x0078CDD1)}},
        {{UINT32_C(0x0072E704), UINT32_C(0x002A3E71), UINT32_C(0x0016610F),
          UINT32_C(0x009B3715), UINT32_C(0x005FCB07), UINT32_C(0x005D8BCF),
          UINT32_C(0x00320582), UINT32_C(0x00A47F78), UINT32_C(0x005E225E),
          UINT32_C(0x00168430), UINT32_C(0x004428AB)},
         {UINT32_C(0x004370E5), UINT32_C(0x005D2F9B), UINT32_C(0x00189EF7),
          UINT32_C(0x0099C20D), UINT32_C(0x0054ECBC), UINT32_C(0x00656294),
          UINT32_C(0x00121DD8), UINT32_C(0x009B9829), UINT32_C(0x007E3E6A),
          UINT32_C(0x003A665F), UINT32_C(0x001EFD05)},
         {UINT32_C(0x008DCA62), UINT32_C(0x006B5EF8), UINT32_C(0x00434D75),
          UINT32_C(0x00FAA8B9), UINT32_C(0x0011A045), UINT32_C(0x006E5D43),
          UINT32_C(0x004E67E5), UINT32_C(0x00966939), UINT32_C(0x005A4ABC),
          UINT32_C(0x002BC8B7), UINT32_C(0x001B23C1)}},
        {{UINT32_C(0x004E71F0), UINT32_C(0x00091B67), UINT32_C(0x00288D0F),
          UINT32_C(0x003DBF2B), UINT32_C(0x0034CDF1), UINT32_C(0x005B1542),
          UINT32_C(0x007363B6), UINT32_C(0x005CE63B), UINT32_C(0x0002FAD4),
          UINT32_C(0x001E8FE4), UINT32_C(0x00419934)},
         {UINT32_C(0x008F0055), UINT32_C(0x00718F3D), UINT32_C(0x00464487),
          UINT32_C(0x00B1F62E), UINT32_C(0x001D1DD4), UINT32_C(0x005C579E),
          UINT32_C(0x000761B7), UINT32_C(0x00CADFA8), UINT32_C(0x005881B3),
          UINT32_C(0x000CF2A5), UINT32_C(0x00236B83)},
         {UINT32_C(0x000EDD83), UINT32_C(0x00641C10), UINT32_C(0x0077B165),
          UINT32_C(0x005DFADE), UINT32_C(0x0002C161), UINT32_C(0x004C0EAB),
          UINT32_C(0x004F8FA8), UINT32_C(0x005182C2), UINT32_C(0x0022EE1E),
          UINT32_C(0x002FC0D1), UINT32_C(0x0072B941)}},
        {{UINT32_C(0x00BE3284), UINT32_C(0x00265E75), UINT32_C(0x0054BBEA),
          UINT32_C(0x009FEA89), UINT32_C(0x006A5BC5), UINT32_C(0x00468BF6),
          UINT32_C(0x0061091E), UINT32_C(0x0021D236), UINT32_C(0x00770DDF),
          UINT32_C(0x0044380D), UINT32_C(0x00261B8D)},
         {UINT32_C(0x0048B464), UINT32_C(0x0007F870), UINT32_C(0x00664579),
          UINT32_C(0x00EBB0D3), UINT32_C(0x0034FB4E), UINT32_C(0x0062BF86),
          UINT32_C(0x0030A6E2), UINT32_C(0x00141079), UINT32_C(0x004C4867),
          UINT32_C(0x0021F669), UINT32_C(0x006C48AC)},
         {UINT32_C(0x001F1A30), UINT32_C(0x00402CCF), UINT32_C(0x002EC836),
          UINT32_C(0x0076E3D0), UINT32_C(0x0045490A), UINT32_C(0x0074B706),
          UINT32_C(0x005C524F), UINT32_C(0x00A8A8B0), UINT32_C(0x001A4F8F),
          UINT32_C(0x00192D28), UINT32_C(0x0065C94E)}},
        {{UINT32_C(0x00053602), UINT32_C(0x0050EAFD), UINT32_C(0x003DFE2B),
          UINT32_C(0x00BAD911), UINT32_C(0x000BAA2C), UINT32_C(0x0067D960),
          UINT32_C(0x000D9F5E), UINT32_C(0x00C75FB5), UINT32_C(0x0061E525),
          UINT32_C(0x0042DF4C), UINT32_C(0x0078BFD6)},
         {UINT32_C(0x00DDF860), UINT32_C(0x0064B891), UINT32_C(0x000AAD9E),
          UINT32_C(0x0055DAD8), UINT32_C(0x000CD34A), UINT32_C(0x007F4F89),
          UINT32_C(0x00720D9B), UINT32_C(0x000C777A), UINT32_C(0x005C019C),
          UINT32_C(0x007BF2C9), UINT32_C(0x002B11EB)},
         {UINT32_C(0x002C702B), UINT32_C(0x00152B74), UINT32_C(0x002C06C6),
          UINT32_C(0x00CAC7B3), UINT32_C(0x00651F98), UINT32_C(0x006BEE21),
          UINT32_C(0x0022FC91), UINT32_C(0x00D0E1A2), UINT32_C(0x000F1226),
          UINT32_C(0x0000CB76), UINT32_C(0x000AD2FB)}},
        {{UINT32_C(0x003B07A3), UINT32_C(0x005563EB), UINT32_C(0x000F5875),
          UINT32_C(0x004E85ED), UINT32_C(0x004DF848), UINT32_C(0x00086603),
          UINT32_C(0x001FE9F6), UINT32_C(0x0060B2A8), UINT32_C(0x00796256),
          UINT32_C(0x00146D3D), UINT32_C(0x007A622F)},
         {UINT32_C(0x00659E8D), UINT32_C(0x000F45E5), UINT32_C(0x0079D2B9),
          UINT32_C(0x00E3F2C8), UINT32_C(0x002FA0A9), UINT32_C(0x0043F516),
          UINT32_C(0x0064D221), UINT32_C(0x0007AF6F), UINT32_C(0x005DA3B1),
          UINT32_C(0x00750DD3), UINT32_C(0x003DC647)},
         {UINT32_C(0x003A7DCA), UINT32_C(0x0078F870), UINT32_C(0x0059ECCF),
          UINT32_C(0x00721F6A), UINT32_C(0x0073DDE5), UINT32_C(0x0052F61E),
          UINT32_C(0x005E03A9), UINT32_C(0x0096290F), UINT32_C(0x007684CD),
          UINT32_C(0x0063A740), UINT32_C(0x00273750)}},
        {{UINT32_C(0x008ABF8E), UINT32_C(0x004EDC3D), UINT32_C(0x007BBD24),
          UINT32_C(0x0037EA5E), UINT32_C(0x0028D8C0), UINT32_C(0x001BE0C2),
          UINT32_C(0x00582236), UINT32_C(0x007A566E), UINT32_C(0x001F4783),
          UINT32_C(0x005F6DB4), UINT32_C(0x0018F286)},
         {UINT32_C(0x0040CAF8), UINT32_C(0x00287C70), UINT32_C(0x002C9166),
          UINT32_C(0x00C254F6), UINT32_C(0x005E2D68), UINT32_C(0x003B46FC),
          UINT32_C(0x004EA606), UINT32_C(0x00D0B9C1), UINT32_C(0x00228115),
          UINT32_C(0x0072EEA6), UINT32_C(0x00471670)},
         {UINT32_C(0x0020A1FA), UINT32_C(0x0075C7D9), UINT32_C(0x005426EA),
          UINT32_C(0x000046B2), UINT32_C(0x0076BA0A), UINT32_C(0x0050CEA6),
          UINT32_C(0x002D133F), UINT32_C(0x005C8821), UINT32_C(0x00406072),
          UINT32_C(0x000A7F19), UINT32_C(0x001740F3)}},
        {{UINT32_C(0x0090884A), UINT32_C(0x00146BFD), UINT32_C(0x00625FB2),
          UINT32_C(0x00C92B84), UINT32_C(0x00097A91), UINT32_C(0x005EACD5),
          UINT32_C(0x005F0661), UINT32_C(0x00870EC3), UINT32_C(0x006D7279),
          UINT32_C(0x002CE3E6), UINT32_C(0x0078F980)},
         {UINT32_C(0x00A2B367), UINT32_C(0x000EF557), UINT32_C(0x004EBC90),
          UINT32_C(0x0065B548), UINT32_C(0x004E46D1), UINT32_C(0x006D0EF0),
          UINT32_C(0x00299E83), UINT32_C(0x0016D017), UINT32_C(0x004A428A),
          UINT32_C(0x00034582), UINT32_C(0x0052ED77)},
         {UINT32_C(0x004E0B1E), UINT32_C(0x000C37F8), UINT32_C(0x006C666A),
          UINT32_C(0x00436BD9), UINT32_C(0x001FC7FE), UINT32_C(0x0000D390),
          UINT32_C(0x006900A5), UINT32_C(0x0062D837), UINT32_C(0x006CD0E6),
          UINT32_C(0x00557AD7), UINT32_C(0x002B57B6)}},
        {{UINT32_C(0x005F93D9), UINT32_C(0x0021EC83), UINT32_C(0x0057A32B),
          UINT32_C(0x001CD982), UINT32_C(0x0008309F), UINT32_C(0x0052B2E6),
          UINT32_C(0x002068F6), UINT32_C(0x00CD4622), UINT32_C(0x005EC9A6),
          UINT32_C(0x002F2CCD), UINT32_C(0x00056DCE)},
         {UINT32_C(0x003E91E9), UINT32_C(0x0002F289), UINT32_C(0x006BF91B),
          UINT32_C(0x006CBB4B), UINT32_C(0x00145A1F), UINT32_C(0x007ECA85),
          UINT32_C(0x004AE385), UINT32_C(0x00BC0058), UINT32_C(0x007D219A),
          UINT32_C(0x00406CB9), UINT32_C(0x001586B8)},
         {UINT32_C(0x00EE30F0), UINT32_C(0x00094A1A), UINT32_C(0x0006C4A5),
          UINT32_C(0x0095BBBF), UINT32_C(0x0033B602), UINT32_C(0x005C234E),
          UINT32_C(0x00405945), UINT32_C(0x002F42B7), UINT32_C(0x0060F012),
          UINT32_C(0x000547D1), UINT32_C(0x00331A12)}},
        {{UINT32_C(0x00AE8C48), UINT32_C(0x00301B89), UINT32_C(0x0030B3A1),
          UINT32_C(0x0030AF95), UINT32_C(0x00455E58), UINT32_C(0x005D22F1),
          UINT32_C(0x0072D976), UINT32_C(0x007AD824), UINT32_C(0x0005D4B7),
          UINT32_C(0x003E29C4), UINT32_C(0x007D5C6C)},
         {UINT32_C(0x00610488), UINT32_C(0x00575F2E), UINT32_C(0x005AC7FE),
          UINT32_C(0x0074B494), UINT32_C(0x006F1543), UINT32_C(0x00029B2A),
          UINT32_C(0x002B9948), UINT32_C(0x0057D800), UINT32_C(0x0052921B),
          UINT32_C(0x00117385), UINT32_C(0x001058C5)},
         {UINT32_C(0x00EA545B), UINT32_C(0x0032E693), UINT32_C(0x004AF898),
          UINT32_C(0x00FFE54C), UINT32_C(0x0039ECD8), UINT32_C(0x00093986),
          UINT32_C(0x004575D9), UINT32_C(0x00CDB661), UINT32_C(0x007A392E),
          UINT32_C(0x0001939C), UINT32_C(0x0028D022)}},
        {{UINT32_C(0x00DA7B5B), UINT32_C(0x0030FFBD), UINT32_C(0x00548E82),
          UINT32_C(0x00DD7D1E), UINT32_C(0x00373E22), UINT32_C(0x0060EA9C),
          UINT32_C(0x00463822), UINT32_C(0x0091DF8B), UINT32_C(0x001801F8),
          UINT32_C(0x0046D441), UINT32_C(0x000C8688)},
         {UINT32_C(0x00EE0CC8), UINT32_C(0x00699131), UINT32_C(0x000427E3),
          UINT32_C(0x00F43A07), UINT32_C(0x00318E56), UINT32_C(0x00415F0B),
          UINT32_C(0x0065B06D), UINT32_C(0x00C66DEB), UINT32_C(0x0069C98F),
          UINT32_C(0x000B21D5), UINT32_C(0x000B711D)},
         {UINT32_C(0x00D0339C), UINT32_C(0x002E2E3B), UINT32_C(0x00573739),
          UINT32_C(0x007F843C), UINT32_C(0x0012F74D), UINT32_C(0x001934CA),
          UINT32_C(0x0011DDD6), UINT32_C(0x00DF048B), UINT32_C(0x004D7027),
          UINT32_C(0x005B0107), UINT32_C(0x0069D6BF)}},
        {{UINT32_C(0x00F44C75), UINT32_C(0x003F20FB), UINT32_C(0x0011CE27),
          UINT32_C(0x00F7E2A9), UINT32_C(0x006914F7), UINT32_C(0x0020C3E4),
          UINT32_C(0x00683AAF), UINT32_C(0x007DE3A9), UINT32_C(0x001E6E79),
          UINT32_C(0x0006F6AF), UINT32_C(0x00563C95)},
         {UINT32_C(0x004904B2), UINT32_C(0x00517306), UINT32_C(0x0049E4D2),
          UINT32_C(0x00459886), UINT32_C(0x001E326B), UINT32_C(0x003AF2A6),
          UINT32_C(0x004F40C8), UINT32_C(0x0072BF1A), UINT32_C(0x00692E44),
          UINT32_C(0x0046EF04), UINT32_C(0x007235C7)},
         {UINT32_C(0x0099C193), UINT32_C(0x0032477B), UINT32_C(0x0064C855),
          UINT32_C(0x00F64B95), UINT32_C(0x006D6DE1), UINT32_C(0x0038972C),
          UINT32_C(0x002E92A5), UINT32_C(0x00FB0D34), UINT32_C(0x0000A7FB),
          UINT32_C(0x00436D2A), UINT32_C(0x002AE23C)}},
        {{UINT32_C(0x005B55EE), UINT32_C(0x00269FC6), UINT32_C(0x0031ABD5),
          UINT32_C(0x00AFFAE5), UINT32_C(0x0007CF80), UINT32_C(0x003582AB),
          UINT32_C(0x004AA0DA), UINT32_C(0x003B2950), UINT32_C(0x0027722C),
          UINT32_C(0x006DA175), UINT32_C(0x00538D39)},
         {UINT32_C(0x00E803AA), UINT32_C(0x0078E9FA), UINT32_C(0x000599F4),
          UINT32_C(0x00C7D432), UINT32_C(0x00482F43), UINT32_C(0x0031EDC5),
          UINT32_C(0x007E0348), UINT32_C(0x0099EAD8), UINT32_C(0x00384F4D),
          UINT32_C(0x004DACD4), UINT32_C(0x0046AD0D)},
         {UINT32_C(0x00A97546), UINT32_C(0x000FB21C), UINT32_C(0x0015497C),
          UINT32_C(0x008E62D2), UINT32_C(0x00505DE5), UINT32_C(0x000B95C5),
          UINT32_C(0x0023EDFF), UINT32_C(0x00264B13), UINT32_C(0x002BCD90),
          UINT32_C(0x00627803), UINT32_C(0x0036514F)}},
        {{UINT32_C(0x0089E077), UINT32_C(0x0036ADDA), UINT32_C(0x001647F5),
          UINT32_C(0x0088678C), UINT32_C(0x002D4F15), UINT32_C(0x004AD6CA),
          UINT32_C(0x000239EF), UINT32_C(0x00784B5B), UINT32_C(0x003FA0B6),
          UINT32_C(0x001178A4), UINT32_C(0x00506FAF)},
         {UINT32_C(0x00B41F08), UINT32_C(0x00501231), UINT32_C(0x00193B29),
          UINT32_C(0x00837915), UINT32_C(0x0052BE13), UINT32_C(0x0067A94C),
          UINT32_C(0x00080E1A), UINT32_C(0x00FF39EB), UINT32_C(0x0012E0B7),
          UINT32_C(0x0004446B), UINT32_C(0x001FD9A4)},
         {UINT32_C(0x00EAA0DA), UINT32_C(0x002DD110), UINT32_C(0x00768BF0),
          UINT32_C(0x00174EE3), UINT32_C(0x006B40A2), UINT32_C(0x004308C1),
          UINT32_C(0x006A7D56), UINT32_C(0x0080EF9C), UINT32_C(0x006C09DF),
          UINT32_C(0x00024E1D), UINT32_C(0x00025FFC)}},
        {{UINT32_C(0x007CFBB3), UINT32_C(0x0008B1DC), UINT32_C(0x00221743),
          UINT32_C(0x00E6686B), UINT32_C(0x00152BCE), UINT32_C(0x001317D6),
          UINT32_C(0x00437CCD), UINT32_C(0x002FC802), UINT32_C(0x006681B5),
          UINT32_C(0x00462383), UINT32_C(0x007A6155)},
         {UINT32_C(0x00C0616E), UINT32_C(0x0065B50F), UINT32_C(0x00575631),
          UINT32_C(0x008AE62D), UINT32_C(0x001A3DAF), UINT32_C(0x001CB629),
          UINT32_C(0x000690A5), UINT32_C(0x000E40AF), UINT32_C(0x0027B0F1),
          UINT32_C(0x0030A43B), UINT32_C(0x003D750B)},
         {UINT32_C(0x0006E7C1), UINT32_C(0x001F7E8D), UINT32_C(0x007DDEB5),
          UINT32_C(0x003E61D8), UINT32_C(0x0052F00A), UINT32_C(0x0007F477),
          UINT32_C(0x0002FCF5), UINT32_C(0x00E51DDA), UINT32_C(0x00710B09),
          UINT32_C(0x00162A00), UINT32_C(0x0070E436)}},
        {{UINT32_C(0x0089B486), UINT32_C(0x00508B10), UINT32_C(0x00504CD3),
          UINT32_C(0x007CBEF5), UINT32_C(0x00401259), UINT32_C(0x0016A84C),
          UINT32_C(0x005DDAF1), UINT32_C(0x00CA278E), UINT32_C(0x005227AF),
          UINT32_C(0x000A6D32), UINT32_C(0x0025F71B)},
         {UINT32_C(0x009CE1F9), UINT32_C(0x00535376), UINT32_C(0x0038A997),
          UINT32_C(0x008FCF77), UINT32_C(0x001681BE), UINT32_C(0x0030BB0D),
          UINT32_C(0x0079D5C2), UINT32_C(0x00CCB407), UINT32_C(0x00115E29),
          UINT32_C(0x006BEA8D), UINT32_C(0x0077DF80)},
         {UINT32_C(0x000328F3), UINT32_C(0x0062691B), UINT32_C(0x00429269),
          UINT32_C(0x00D14DE5), UINT32_C(0x001B9543), UINT32_C(0x00310721),
          UINT32_C(0x005A6271), UINT32_C(0x00077EE6), UINT32_C(0x006695BC),
          UINT32_C(0x00502870), UINT32_C(0x005376F2)}},
    },
    {
        {{UINT32_C(0x00F38262), UINT32_C(0x00203985), UINT32_C(0x0029479B),
          UINT32_C(0x0057D8A4), UINT32_C(0x002352DF), UINT32_C(0x005A3DE0),
          UINT32_C(0x0038FC9D), UINT32_C(0x005E69CE), UINT32_C(0x007195C8),
          UINT32_C(0x007F3F0C), UINT32_C(0x0075AA44)},
         {UINT32_C(0x00A2B12A), UINT32_C(0x0005AB91), UINT32_C(0x0005801E),
          UINT32_C(0x00BD3E26), UINT32_C(0x004A3566), UINT32_C(0x0039622B),
          UINT32_C(0x00196466), UINT32_C(0x003385DF), UINT32_C(0x00464F26),
          UINT32_C(0x00348D32), UINT32_C(0x006B3D82)},
         {UINT32_C(0x0065745B), UINT32_C(0x00756984), UINT32_C(0x000663B1),
          UINT32_C(0x00F8FADD), UINT32_C(0x005D0DD6), UINT32_C(0x005A5E23),
          UINT32_C(0x0021B9BE), UINT32_C(0x009BBEE7), UINT32_C(0x003310A0),
          UINT32_C(0x0008941E), UINT32_C(0x006EFBEF)}},
        {{UINT32_C(0x008285AF), UINT32_C(0x0032D42C), UINT32_C(0x004A921F),
          UINT32_C(0x00501992), UINT32_C(0x00684DBF), UINT32_C(0x001A5365),
          UINT32_C(0x0036ABF8), UINT32_C(0x004599AB), UINT32_C(0x003153D1),
          UINT32_C(0x004357B7), UINT32_C(0x00289208)},
         {UINT32_C(0x00BF2760), UINT32_C(0x000C24C0), UINT32_C(0x0057E755),
          UINT32_C(0x000FB090), UINT32_C(0x007F2FA3), UINT32_C(0x0034386D),
          UINT32_C(0x004D0670), UINT32_C(0x007D5F67), UINT32_C(0x00770E2C),
          UINT32_C(0x004F400F), UINT32_C(0x0004CA31)},
         {UINT32_C(0x00C5736B), UINT32_C(0x0025DE46), UINT32_C(0x00184C66),
          UINT32_C(0x002454E1), UINT32_C(0x006C791A), UINT32_C(0x00695E64),
          UINT32_C(0x0005F1A3), UINT32_C(0x00D89A22), UINT32_C(0x00339085),
          UINT32_C(0x00612026), UINT32_C(0x0011E398)}},
        {{UINT32_C(0x00580ED8), UINT32_C(0x00351562), UINT32_C(0x003AC56C),
          UINT32_C(0x0082A872), UINT32_C(0x00009388), UINT32_C(0x0059018D),
          UINT32_C(0x00428E6F), UINT32_C(0x0044580D), UINT32_C(0x00397D38),
          UINT32_C(0x001413BF), UINT32_C(0x0019ACCA)},
         {UINT32_C(0x00EBF493), UINT32_C(0x0065B688), UINT32_C(0x00475CB8),
          UINT32_C(0x000C5271), UINT32_C(0x001FA36C), UINT32_C(0x002065F1),
          UINT32_C(0x001E357D), UINT32_C(0x002E3A47), UINT32_C(0x007D686D),
          UINT32_C(0x0016A68A), UINT32_C(0x00662C00)},
         {UINT32_C(0x00BBB7E7), UINT32_C(0x00118B8C), UINT32_C(0x00257EA2),
          UINT32_C(0x00EAE0BF), UINT32_C(0x00534B32), UINT32_C(0x002C3391),
          UINT32_C(0x005F91A4), UINT32_C(0x009CC53C), UINT32_C(0x000D1F59),
          UINT32_C(0x0044B89F), UINT32_C(0x004E2285)}},
        {{UINT32_C(0x0027C85C), UINT32_C(0x0034E4D0), UINT32_C(0x0034EE41),
          UINT32_C(0x0000C14F), UINT32_C(0x00466E36), UINT32_C(0x0012ED82),
          UINT32_C(0x004AB2CC), UINT32_C(0x00BB7B93), UINT32_C(0x0013EE11),
          UINT32_C(0x0071AADD), UINT32_C(0x000E9430)},
         {UINT32_C(0x0048606F), UINT32_C(0x000E389D), UINT32_C(0x005B7915),
          UINT32_C(0x00DA42A8), UINT32_C(0x003E8287), UINT32_C(0x001D2AF2),
          UINT32_C(0x0044CA11), UINT32_C(0x00C9B883), UINT32_C(0x000B95FA),
          UINT32_C(0x00571771), UINT32_C(0x002FD7F4)},
         {UINT32_C(0x00DD2691), UINT32_C(0x0071C20D), UINT32_C(0x00544E37),
          UINT32_C(0x00458C30), UINT32_C(0x005ED950), UINT32_C(0x000A642E),
          UINT32_C(0x000316B6), UINT32_C(0x00F34871), UINT32_C(0x0030B700),
          UINT32_C(0x007D4AA7), UINT32_C(0x000E8F8F)}},
        {{UINT32_C(0x00AFDFD3), UINT32_C(0x0023AB6D), UINT32_C(0x003A134B),
          UINT32_C(0x007521AF), UINT32_C(0x002E2236), UINT32_C(0x00146DF2),
          UINT32_C(0x00201697), UINT32_C(0x000389A1), UINT32_C(0x0048BE72),
          UINT32_C(0x003FD0B3), UINT32_C(0x003AD399)},
         {UINT32_C(0x00453802), UINT32_C(0x0058F172), UINT32_C(0x000BD847),
          UINT32_C(0x005FE8C6), UINT32_C(0x0040628C), UINT32_C(0x004614B8),
          UINT32_C(0x004205D4), UINT32_C(0x00259F68), UINT32_C(0x0076F623),
          UINT32_C(0x00678707), UINT32_C(0x0057C6D8)},
         {UINT32_C(0x00DB4A5B), UINT32_C(0x004A2766), UINT32_C(0x00614411),
          UINT32_C(0x00510EC4), UINT32_C(0x007473CF), UINT32_C(0x0051CB0E),
          UINT32_C(0x0014EC5F), UINT32_C(0x000483E0), UINT32_C(0x001F7AE6),
          UINT32_C(0x0078A5EC), UINT32_C(0x003FC5C6)}},
        {{UINT32_C(0x00E73CF6), UINT32_C(0x00365645), UINT32_C(0x0026EE89),
          UINT32_C(0x006AC701), UINT32_C(0x007831F1), UINT32_C(0x00393FC7),
          UINT32_C(0x0003E80C), UINT32_C(0x008ECBE8), UINT32_C(0x0027B4A8),
          UINT32_C(0x0037554F), UINT32_C(0x006AD6BD)},
         {UINT32_C(0x001857E4), UINT32_C(0x00657687), UINT32_C(0x0036B228),
          UINT32_C(0x006A820E), UINT32_C(0x00459485), UINT32_C(0x002AA7DA),
          UINT32_C(0x0031670E), UINT32_C(0x0099F393), UINT32_C(0x0023D587),
          UINT32_C(0x000B8D51), UINT32_C(0x0067F4BE)},
         {UINT32_C(0x00604B7F), UINT32_C(0x00531996), UINT32_C(0x000F3A4C),
          UINT32_C(0x00EFD0BB), UINT32_C(0x001D4914), UINT32_C(0x004C04B3),
          UINT32_C(0x0003D908), UINT32_C(0x00AA3218), UINT32_C(0x00403685),
          UINT32_C(0x0072EB6A), UINT32_C(0x002176B1)}},
        {{UINT32_C(0x00DC72D5), UINT32_C(0x0053C100), UINT32_C(0x006992E3),
          UINT32_C(0x00532CA0), UINT32_C(0x004D3AD9), UINT32_C(0x0016B1F6),
          UINT32_C(0x00776F4B), UINT32_C(0x006ECD70), UINT32_C(0x004FC6E0),
          UINT32_C(0x00766557), UINT32_C(0x0019E21F)},
         {UINT32_C(0x002B1672), UINT32_C(0x003C3FDF), UINT32_C(0x00324542),
          UINT32_C(0x007B582B), UINT32_C(0x0002AD86), UINT32_C(0x00528C4E),
          UINT32_C(0x003D3509), UINT32_C(0x00D075CA), UINT32_C(0x00783A6B),
          UINT32_C(0x0032D6B0), UINT32_C(0x00655B92)},
         {UINT32_C(0x0067E0B1), UINT32_C(0x001D8E27), UINT32_C(0x002F0272),
          UINT32_C(0x00BBEB09), UINT32_C(0x0017C396), UINT32_C(0x005B451C),
          UINT32_C(0x000ADC03), UINT32_C(0x00F50759), UINT32_C(0x000DE4C0),
          UINT32_C(0x006BAD90), UINT32_C(0x0015A7A2)}},
        {{UINT32_C(0x009DA550), UINT32_C(0x004309EA), UINT32_C(0x006FDD59),
          UINT32_C(0x00C427DA), UINT32_C(0x00454A59), UINT32_C(0x005B8E71),
          UINT32_C(0x00341B12), UINT32_C(0x00252CD4), UINT32_C(0x00111FF0),
          UINT32_C(0x007E3827), UINT32_C(0x0048F3DC)},
         {UINT32_C(0x00BC3FD8), UINT32_C(0x00513D87), UINT32_C(0x0004F4BB),
          UINT32_C(0x0040E743), UINT32_C(0x007CD878), UINT32_C(0x0010C877),
          UINT32_C(0x005F89C4), UINT32_C(0x00F5CB3E), UINT32_C(0x0074C6FA),
          UINT32_C(0x006D3B3F), UINT32_C(0x0027626C)},
         {UINT32_C(0x004FE9AA), UINT32_C(0x001A4C25), UINT32_C(0x005FC4B0),
          UINT32_C(0x00C432AF), UINT32_C(0x005E26E5), UINT32_C(0x003B434E),
          UINT32_C(0x0027CFF2), UINT32_C(0x0043D716), UINT32_C(0x0059B5A6),
          UINT32_C(0x00333789), UINT32_C(0x0011248B)}},
        {{UINT32_C(0x00CA0B46), UINT32_C(0x00797C4C), UINT32_C(0x0025673C),
          UINT32_C(0x004C5796), UINT32_C(0x000F4352), UINT32_C(0x00477E09),
          UINT32_C(0x007CE235), UINT32_C(0x00B62DDB), UINT32_C(0x00398769),
          UINT32_C(0x00598708), UINT32_C(0x002AA80B)},
         {UINT32_C(0x006334CD), UINT32_C(0x0007E78A), UINT32_C(0x000B78B3),
          UINT32_C(0x00945807), UINT32_C(0x005E6E86), UINT32_C(0x005554D4),
          UINT32_C(0x000A6B65), UINT32_C(0x00306D92), UINT32_C(0x005D12E8),
          UINT32_C(0x003DEB11), UINT32_C(0x00385DB1)},
         {UINT32_C(0x000A5E84), UINT32_C(0x006D3E68), UINT32_C(0x00756665),
          UINT32_C(0x004A1477), UINT32_C(0x00348B5C), UINT32_C(0x0048952E),
          UINT32_C(0x002EDCD0), UINT32_C(0x0017A1F7), UINT32_C(0x000D0B6A),
          UINT32_C(0x002CC6D4), UINT32_C(0x00357331)}},
        {{UINT32_C(0x0079D8A5), UINT32_C(0x007C1D32), UINT32_C(0x0026F73E),
          UINT32_C(0x00DC858C), UINT32_C(0x002CFF0F), UINT32_C(0x007FA1D8),
          UINT32_C(0x000AA161), UINT32_C(0x009E41A9), UINT32_C(0x0009D873),
          UINT32_C(0x004C0E2C), UINT32_C(0x001DB409)},
         {UINT32_C(0x0018D65B), UINT32_C(0x0022CD31), UINT32_C(0x00390F42),
          UINT32_C(0x00D8FCA4), UINT32_C(0x006663C5), UINT32_C(0x0019722C),
          UINT32_C(0x007F6B0A), UINT32_C(0x00A630E3), UINT32_C(0x006E4A27),
          UINT32_C(0x0021D1B6), UINT32_C(0x002AAC94)},
         {UINT32_C(0x0018C372), UINT32_C(0x0054FA42), UINT32_C(0x000EFD69),
          UINT32_C(0x0011780F), UINT32_C(0x001A67DB), UINT32_C(0x006E5FA4),
          UINT32_C(0x00414397), UINT32_C(0x007903CC), UINT32_C(0x006A8416),
          UINT32_C(0x007EAA7F), UINT32_C(0x002FE26B)}},
        {{UINT32_C(0x00E4631A), UINT32_C(0x00519A42), UINT32_C(0x004D5D8F),
          UINT32_C(0x00C6E606), UINT32_C(0x00053595), UINT32_C(0x004EB9D1),
          UINT32_C(0x005A269C), UINT32_C(0x003E8B00), UINT32_C(0x00162354),
          UINT32_C(0x007E07B4), UINT32_C(0x003AF53B)},
         {UINT32_C(0x00B6EB19), UINT32_C(0x00555D97), UINT32_C(0x00633E65),
          UINT32_C(0x0099187D), UINT32_C(0x00227AE7), UINT32_C(0x001674B2),
          UINT32_C(0x005E8C81), UINT32_C(0x005B7DF9), UINT32_C(0x0055E4BB),
          UINT32_C(0x0056A5E1), UINT32_C(0x0019F876)},
         {UINT32_C(0x00B4688B), UINT32_C(0x005F4304), UINT32_C(0x000B39BD),
          UINT32_C(0x004E7F2B), UINT32_C(0x00313A32), UINT32_C(0x007002F9),
          UINT32_C(0x0031EB5F), UINT32_C(0x00C85095), UINT32_C(0x00425A62),
          UINT32_C(0x00328356), UINT32_C(0x007EF956)}},
        {{UINT32_C(0x00E0C2E0), UINT32_C(0x00585FE0), UINT32_C(0x0075AB74),
          UINT32_C(0x0041E028), UINT32_C(0x006CAE63), UINT32_C(0x003C54C9),
          UINT32_C(0x001036EA), UINT32_C(0x002B7F12), UINT32_C(0x00144D5C),
          UINT32_C(0x007374DB), UINT32_C(0x00750F36)},
         {UINT32_C(0x0061BB41), UINT32_C(0x0036024E), UINT32_C(0x00655A71),
          UINT32_C(0x004FC91A), UINT32_C(0x0014B38E), UINT32_C(0x003D62DB),
          UINT32_C(0x0072A235), UINT32_C(0x003B2C40), UINT32_C(0x002C0290),
          UINT32_C(0x005FEB15), UINT32_C(0x0061089A)},
         {UINT32_C(0x0085ECE4), UINT32_C(0x00332265), UINT32_C(0x0042796C),
          UINT32_C(0x00FD5F30), UINT32_C(0x004C81DA), UINT32_C(0x00007089),
          UINT32_C(0x006EA425), UINT32_C(0x00B807C7), UINT32_C(0x00459E0F),
          UINT32_C(0x00181B93), UINT32_C(0x000B0E1B)}},
        {{UINT32_C(0x009EFE70), UINT32_C(0x004E468A), UINT32_C(0x0077E5AA),
          UINT32_C(0x001467EF), UINT32_C(0x0020C382), UINT32_C(0x0055629A),
          UINT32_C(0x005CA999), UINT32_C(0x00BEFB9D), UINT32_C(0x005A686B),
          UINT32_C(0x0010351C), UINT32_C(0x005963DB)},
         {UINT32_C(0x001E85F3), UINT32_C(0x0014333A), UINT32_C(0x006927C7),
          UINT32_C(0x00F08F00), UINT32_C(0x00634AD4), UINT32_C(0x0022A5F2),
          UINT32_C(0x003B4641), UINT32_C(0x00A28090), UINT32_C(0x003FC621),
          UINT32_C(0x002B49C0), UINT32_C(0x00555DF3)},
         {UINT32_C(0x007C3D87), UINT32_C(0x002EFA77), UINT32_C(0x00567EC8),
          UINT32_C(0x00D46233), UINT32_C(0x00590994), UINT32_C(0x0045579D),
          UINT32_C(0x0006388F), UINT32_C(0x004C41FA), UINT32_C(0x004C71DD),
          UINT32_C(0x007BF667), UINT32_C(0x0057A3C7)}},
        {{UINT32_C(0x0093682F), UINT32_C(0x00073EF9), UINT32_C(0x0019C616),
          UINT32_C(0x00C17E8F), UINT32_C(0x002702CF), UINT32_C(0x005F528E),
          UINT32_C(0x0019B458), UINT32_C(0x0052A2E0), UINT32_C(0x00425491),
          UINT32_C(0x0018AED3), UINT32_C(0x006F6DAE)},
         {UINT32_C(0x0082401A), UINT32_C(0x004D0E66), UINT32_C(0x003A3362),
          UINT32_C(0x003D1A93), UINT32_C(0x000E6BF3), UINT32_C(0x0017967B),
          UINT32_C(0x00117AFB), UINT32_C(0x008CB04A), UINT32_C(0x002CA224),
          UINT32_C(0x00067DCB), UINT32_C(0x0056DEA4)},
         {UINT32_C(0x005F1A62), UINT32_C(0x006AF62D), UINT32_C(0x000D1E62),
          UINT32_C(0x00CCEE42), UINT32_C(0x00512EC5), UINT32_C(0x002C191C),
          UINT32_C(0x0065B2C8), UINT32_C(0x00567D2B), UINT32_C(0x006A4BC8),
          UINT32_C(0x0011359A), UINT32_C(0x003AB09B)}},
        {{UINT32_C(0x0072355E), UINT32_C(0x00290653), UINT32_C(0x00453126),
          UINT32_C(0x00DA22DA), UINT32_C(0x004311EA), UINT32_C(0x000C95C7),
          UINT32_C(0x005DA2D6), UINT32_C(0x0048EE2E), UINT32_C(0x007AC207),
          UINT32_C(0x0029BC84), UINT32_C(0x000A0DFC)},
         {UINT32_C(0x004EF0CE), UINT32_C(0x00747296), UINT32_C(0x000818AB),
          UINT32_C(0x00BE8543), UINT32_C(0x0041F6F4), UINT32_C(0x00448449),
          UINT32_C(0x00153E5F), UINT32_C(0x00AADD33), UINT32_C(0x0035B84C),
          UINT32_C(0x0069EAE8), UINT32_C(0x00217DBD)},
         {UINT32_C(0x002DB2A4), UINT32_C(0x001158C1), UINT32_C(0x002F9514),
          UINT32_C(0x00428AE0), UINT32_C(0x0071D932), UINT32_C(0x002D6CE8),
          UINT32_C(0x0002F0FA), UINT32_C(0x001EB77A), UINT32_C(0x0041C021),
          UINT32_C(0x00779647), UINT32_C(0x0000B4B0)}},
        {{UINT32_C(0x002BCA29), UINT32_C(0x000D6E00), UINT32_C(0x002A8D45),
          UINT32_C(0x00E65F5F), UINT32_C(0x0032B3E8), UINT32_C(0x001DDA5D),
          UINT32_C(0x0017AEC6), UINT32_C(0x00CCE901), UINT32_C(0x002418B9),
          UINT32_C(0x003F180D), UINT32_C(0x00754B21)},
         {UINT32_C(0x001E8828), UINT32_C(0x004DE168), UINT32_C(0x00365565),
          UINT32_C(0x003E7F08), UINT32_C(0x00022EE0), UINT32_C(0x0043C370),
          UINT32_C(0x0066C1D6), UINT32_C(0x0033E762), UINT32_C(0x0021E80E),
          UINT32_C(0x0022E1CE), UINT32_C(0x0039049C)},
         {UINT32_C(0x001AEEAB), UINT32_C(0x004E9952), UINT32_C(0x006CD381),
          UINT32_C(0x00319F16), UINT32_C(0x004B6EED), UINT32_C(0x000E8C3B),
          UINT32_C(0x0033C702), UINT32_C(0x0074A7B1), UINT32_C(0x0055F862),
          UINT32_C(0x00070547), UINT32_C(0x001AB4F3)}},
    },
    {
        {{UINT32_C(0x002AB480), UINT32_C(0x00491F21), UINT32_C(0x007D1D9B),
          UINT32_C(0x0017861D), UINT32_C(0x004E0965), UINT32_C(0x004F9835),
          UINT32_C(0x002889AB), UINT32_C(0x00254B0E), UINT32_C(0x003F4ECE),
          UINT32_C(0x00645245), UINT32_C(0x003DE09E)},
         {UINT32_C(0x00BFFA33), UINT32_C(0x0001E566), UINT32_C(0x0048223D),
          UINT32_C(0x0081AC1B), UINT32_C(0x0075FACF), UINT32_C(0x00288CA5),
          UINT32_C(0x00319699), UINT32_C(0x00E70935), UINT32_C(0x003AFCDF),
          UINT32_C(0x000D446C), UINT32_C(0x001FF61C)},
         {UINT32_C(0x00CFC3EE), UINT32_C(0x0053EC31), UINT32_C(0x00556C71),
          UINT32_C(0x00B61E4F), UINT32_C(0x0019B811), UINT32_C(0x000E942F),
          UINT32_C(0x006B460A), UINT32_C(0x0028CECD), UINT32_C(0x0053755C),
          UINT32_C(0x006A36AD), UINT32_C(0x0074830B)}},
        {{UINT32_C(0x0032B148), UINT32_C(0x00574A20), UINT32_C(0x00258660),
          UINT32_C(0x00A32E9A), UINT32_C(0x00666B0C), UINT32_C(0x0050B448),
          UINT32_C(0x00375197), UINT32_C(0x007C7568), UINT32_C(0x0054C0EF),
          UINT32_C(0x003C6296), UINT32_C(0x00451E25)},
         {UINT32_C(0x001F0527), UINT32_C(0x0015C673), UINT32_C(0x002F100D),
          UINT32_C(0x0051AD70), UINT32_C(0x00120973), UINT32_C(0x002A4934),
          UINT32_C(0x00340AF5), UINT32_C(0x00DB9742), UINT32_C(0x00743FB7),
          UINT32_C(0x00457EDD), UINT32_C(0x000CEB92)},
         {UINT32_C(0x00F5B12C), UINT32_C(0x0053626F), UINT32_C(0x007F2969),
          UINT32_C(0x00F9A729), UINT32_C(0x0033C947), UINT32_C(0x0000F3BA),
          UINT32_C(0x00207B91), UINT32_C(0x0063F460), UINT32_C(0x0012F8FD),
          UINT32_C(0x000B32A2), UINT32_C(0x003E578A)}},
        {{UINT32_C(0x00B1FE77), UINT32_C(0x00298801), UINT32_C(0x00011596),
          UINT32_C(0x001AC338), UINT32_C(0x001D0E96), UINT32_C(0x005E3714),
          UINT32_C(0x003DE2F3), UINT32_C(0x00AF9E64), UINT32_C(0x00187EE5),
          UINT32_C(0x004F7E2C), UINT32_C(0x0017C5D3)},
         {UINT32_C(0x0071BA87), UINT32_C(0x00241D87), UINT32_C(0x0034F745),
          UINT32_C(0x0004090C), UINT32_C(0x000A1932), UINT32_C(0x0042ADD3),
          UINT32_C(0x0053459C), UINT32_C(0x007BE02E), UINT32_C(0x003D62A3),
          UINT32_C(0x0015988B), UINT32_C(0x006AAD49)},
         {UINT32_C(0x00367A73), UINT32_C(0x007F03C1), UINT32_C(0x002CAE51),
          UINT32_C(0x0029D218), UINT32_C(0x0014E4A9), UINT32_C(0x0054032D),
          UINT32_C(0x0062F02D), UINT32_C(0x00DCDDAD), UINT32_C(0x007EE1CD),
          UINT32_C(0x001D0B5C), UINT32_C(0x00569FE0)}},
        {{UINT32_C(0x00BCADF6), UINT32_C(0x0075341C), UINT32_C(0x00398E24),
          UINT32_C(0x003BF613), UINT32_C(0x0001E10C), UINT32_C(0x0065FEAC),
          UINT32_C(0x0024D15C), UINT32_C(0x00196783), UINT32_C(0x0051862B),
          UINT32_C(0x006C2018), UINT32_C(0x002A0826)},
         {UINT32_C(0x00F6EE57), UINT32_C(0x0011CD5C), UINT32_C(0x001F7638),
          UINT32_C(0x005F98C2), UINT32_C(0x001F7049), UINT32_C(0x003964A1),
          UINT32_C(0x005657BE), UINT32_C(0x00DEE9F7), UINT32_C(0x0026BEEC),
          UINT32_C(0x00319B9B), UINT32_C(0x00345293)},
         {UINT32_C(0x006BDDA7), UINT32_C(0x0030B11F), UINT32_C(0x002600EA),
          UINT32_C(0x00461976), UINT32_C(0x001593E9), UINT32_C(0x006F183C),
          UINT32_C(0x003ABB5A), UINT32_C(0x0018F1A8), UINT32_C(0x00390978),
          UINT32_C(0x0005156D), UINT32_C(0x00075AED)}},
        {{UINT32_C(0x001BD2D5), UINT32_C(0x00578B39), UINT32_C(0x007FE095),
          UINT32_C(0x0046AFA9), UINT32_C(0x002148E8), UINT32_C(0x0017C3CD),
          UINT32_C(0x006AE351), UINT32_C(0x0063F7AB), UINT32_C(0x0050AA1E),
          UINT32_C(0x004184F5), UINT32_C(0x0069223D)},
         {UINT32_C(0x0086A15A), UINT32_C(0x0033AD1F), UINT32_C(0x006A0376),
          UINT32_C(0x00C44A80), UINT32_C(0x001DE4DA), UINT32_C(0x000819B5),
          UINT32_C(0x00275A25), UINT32_C(0x005EA5CD), UINT32_C(0x0022AA1B),
          UINT32_C(0x004A1528), UINT32_C(0x0041A71A)},
         {UINT32_C(0x00F443D0), UINT32_C(0x007F9618), UINT32_C(0x005E374D),
          UINT32_C(0x00C0032C), UINT32_C(0x005BC03C), UINT32_C(0x00225F6A),
          UINT32_C(0x002F1161), UINT32_C(0x0062CB7E), UINT32_C(0x00527B02),
          UINT32_C(0x005AAD7F), UINT32_C(0x00585C83)}},
        {{UINT32_C(0x00D110A1), UINT32_C(0x004B6AF7), UINT32_C(0x00084690),
          UINT32_C(0x00F276EF), UINT32_C(0x0000F67B), UINT32_C(0x001870DC),
          UINT32_C(0x0010FCB3), UINT32_C(0x00392CFE), UINT32_C(0x002086D8),
          UINT32_C(0x007424AF), UINT32_C(0x00202355)},
         {UINT32_C(0x00077C6C), UINT32_C(0x0025E823), UINT32_C(0x006EF81B),
          UINT32_C(0x00540F85), UINT32_C(0x002C8AF2), UINT32_C(0x0013C77B),
          UINT32_C(0x001C4D2F), UINT32_C(0x001021D9), UINT32_C(0x00146F24),
          UINT32_C(0x003599F3), UINT32_C(0x004B4CCC)},
         {UINT32_C(0x004CD365), UINT32_C(0x00179717), UINT32_C(0x0039A122),
          UINT32_C(0x00B62616), UINT32_C(0x007AEE90), UINT32_C(0x0029164E),
          UINT32_C(0x0039438A), UINT32_C(0x00CBA485), UINT32_C(0x00428DC8),
          UINT32_C(0x00313356), UINT32_C(0x006E6A72)}},
        {{UINT32_C(0x00CF5614), UINT32_C(0x00163606), UINT32_C(0x002CA6AC),
          UINT32_C(0x006EA0B2), UINT32_C(0x006DA8EE), UINT32_C(0x0019BDF3),
          UINT32_C(0x006043AF), UINT32_C(0x008AA760), UINT32_C(0x002E08F1),
          UINT32_C(0x0037973A), UINT32_C(0x0029B40D)},
         {UINT32_C(0x00379A65), UINT32_C(0x0052E429), UINT32_C(0x0068686B),
          UINT32_C(0x00C69C90), UINT32_C(0x004E9422), UINT32_C(0x00366023),
          UINT32_C(0x002260F4), UINT32_C(0x005BAF5B), UINT32_C(0x000678A6),
          UINT32_C(0x000460DA), UINT32_C(0x004F4340)},
         {UINT32_C(0x00E03B58), UINT32_C(0x007B260B), UINT32_C(0x005BA74C),
          UINT32_C(0x00BD1A46), UINT32_C(0x0009DC7B), UINT32_C(0x00118343),
          UINT32_C(0x00021428), UINT32_C(0x000FFED5), UINT32_C(0x0072D7E6),
          UINT32_C(0x003DCFB6), UINT32_C(0x003AB926)}},
        {{UINT32_C(0x0077649D), UINT32_C(0x005389B7), UINT32_C(0x004D6E76),
          UINT32_C(0x00936F59), UINT32_C(0x00463E7C), UINT32_C(0x006D778A),
          UINT32_C(0x0009DB6F), UINT32_C(0x00A424F1), UINT32_C(0x0052F5CC),
          UINT32_C(0x007E60DE), UINT32_C(0x004279FB)},
         {UINT32_C(0x00A7028F), UINT32_C(0x007EA950), UINT32_C(0x001EFBD6),
          UINT32_C(0x00BDA79F), UINT32_C(0x007AF7CC), UINT32_C(0x00279C7C),
          UINT32_C(0x006C7303), UINT32_C(0x00E939AC), UINT32_C(0x00354019),
          UINT32_C(0x00187CF7), UINT32_C(0x00183D31)},
         {UINT32_C(0x0042F626), UINT32_C(0x0074EA5E), UINT32_C(0x00538C9A),
          UINT32_C(0x00AF163A), UINT32_C(0x004C692D), UINT32_C(0x0023FAA0),
          UINT32_C(0x00775E16), UINT32_C(0x0062DAB8), UINT32_C(0x007D6702),
          UINT32_C(0x001B38C5), UINT32_C(0x001E3974)}},
        {{UINT32_C(0x004F9403), UINT32_C(0x00748471), UINT32_C(0x00211B76),
          UINT32_C(0x001EFC08), UINT32_C(0x005B213F), UINT32_C(0x003E9002),
          UINT32_C(0x00785B3C), UINT32_C(0x00E45607), UINT32_C(0x003D477B),
          UINT32_C(0x00282DEF), UINT32_C(0x00504A42)},
         {UINT32_C(0x00BF7FBB), UINT32_C(0x000A066A), UINT32_C(0x000679D4),
          UINT32_C(0x00BB2351), UINT32_C(0x007D83CA), UINT32_C(0x00612D60),
          UINT32_C(0x00031501), UINT32_C(0x00F4CAB2), UINT32_C(0x002155C8),
          UINT32_C(0x003A8F2C), UINT32_C(0x00169BC2)},
         {UINT32_C(0x000357B5), UINT32_C(0x006B1266), UINT32_C(0x002BCBF3),
          UINT32_C(0x00A64DFF), UINT32_C(0x0066D9DE), UINT32_C(0x0057385B),
          UINT32_C(0x005050FF), UINT32_C(0x006FC06F), UINT32_C(0x0028E483),
          UINT32_C(0x0051EF87), UINT32_C(0x000CC48B)}},
        {{UINT32_C(0x00ED9E35), UINT32_C(0x0023A04E), UINT32_C(0x0024255C),
          UINT32_C(0x0070038E), UINT32_C(0x0046A52B), UINT32_C(0x004E9C3F),
          UINT32_C(0x006C8BA2), UINT32_C(0x00D8ED9C), UINT32_C(0x006F6A03),
          UINT32_C(0x003098C4), UINT32_C(0x005D825F)},
         {UINT32_C(0x008FA640), UINT32_C(0x000B9382), UINT32_C(0x0011CD48),
          UINT32_C(0x00034F5D), UINT32_C(0x0059465F), UINT32_C(0x0070DA45),
          UINT32_C(0x00649F15), UINT32_C(0x0054EE9E), UINT32_C(0x003D37B0),
          UINT32_C(0x004361C8), UINT32_C(0x002CB478)},
         {UINT32_C(0x0012D22E), UINT32_C(0x00674682), UINT32_C(0x0070C891),
          UINT32_C(0x00FC49FE), UINT32_C(0x003AF178), UINT32_C(0x0077EF15),
          UINT32_C(0x007CF642), UINT32_C(0x00E36298), UINT32_C(0x002FEFEF),
          UINT32_C(0x0070ADFB), UINT32_C(0x00674E26)}},
        {{UINT32_C(0x00E45F49), UINT32_C(0x0042AB1C), UINT32_C(0x005B58EE),
          UINT32_C(0x009A5641), UINT32_C(0x004084D4), UINT32_C(0x00776081),
          UINT32_C(0x004873EC), UINT32_C(0x0091B439), UINT32_C(0x0049547A),
          UINT32_C(0x0061EF9F), UINT32_C(0x00209C2C)},
         {UINT32_C(0x00ACB336), UINT32_C(0x0002E07D), UINT32_C(0x006207CB),
          UINT32_C(0x0044DAE2), UINT32_C(0x0017A196), UINT32_C(0x006B179D),
          UINT32_C(0x005CFADF), UINT32_C(0x00081BD0), UINT32_C(0x002587D0),
          UINT32_C(0x005AA501), UINT32_C(0x0027A6E9)},
         {UINT32_C(0x0094228F), UINT32_C(0x000636DC), UINT32_C(0x005A4102),
          UINT32_C(0x003E66B4), UINT32_C(0x0011F6CD), UINT32_C(0x0038E6FD),
          UINT32_C(0x000B5086), UINT32_C(0x0050E978), UINT32_C(0x005AE74C),
          UINT32_C(0x00246161), UINT32_C(0x007E9879)}},
        {{UINT32_C(0x0065CC5A), UINT32_C(0x000BD258), UINT32_C(0x00167FAC),
          UINT32_C(0x00E5D799), UINT32_C(0x002DCFC5), UINT32_C(0x0045B74C),
          UINT32_C(0x00057C88), UINT32_C(0x0038384F), UINT32_C(0x003413FE),
          UINT32_C(0x00625CFF), UINT32_C(0x00014CE5)},
         {UINT32_C(0x0038E62F), UINT32_C(0x004B5EE4), UINT32_C(0x004ADC3F),
          UINT32_C(0x0062FF7C), UINT32_C(0x001110E4), UINT32_C(0x007C7151),
          UINT32_C(0x002F8915), UINT32_C(0x0068F26B), UINT32_C(0x0038A310),
          UINT32_C(0x002E8D53), UINT32_C(0x0031786B)},
         {UINT32_C(0x004DE3F1), UINT32_C(0x00036913), UINT32_C(0x006704FB),
          UINT32_C(0x003944F4), UINT32_C(0x003E4AD6), UINT32_C(0x001A01BA),
          UINT32_C(0x002E0D68), UINT32_C(0x004A2407), UINT32_C(0x0047BA89),
          UINT32_C(0x003808BF), UINT32_C(0x0070E238)}},
        {{UINT32_C(0x001B2F70), UINT32_C(0x006F3BCD), UINT32_C(0x0020C220),
          UINT32_C(0x00971E3D), UINT32_C(0x0067DC3E), UINT32_C(0x00154916),
          UINT32_C(0x007EE08E), UINT32_C(0x009FB7E0), UINT32_C(0x00164A27),
          UINT32_C(0x00000D51), UINT32_C(0x00044A8C)},
         {UINT32_C(0x00441852), UINT32_C(0x004183C7), UINT32_C(0x000B05DA),
          UINT32_C(0x00765798), UINT32_C(0x002FE415), UINT32_C(0x0078F9D1),
          UINT32_C(0x0049FDE6), UINT32_C(0x00D85938), UINT32_C(0x00404646),
          UINT32_C(0x005B14A2), UINT32_C(0x00151434)},
         {UINT32_C(0x001ECEB3), UINT32_C(0x00723B7F), UINT32_C(0x0073FB3C),
          UINT32_C(0x0055508D), UINT32_C(0x002C2D2B), UINT32_C(0x0040F43E),
          UINT32_C(0x001CBD6A), UINT32_C(0x00015808), UINT32_C(0x0013E380),
          UINT32_C(0x00778472), UINT32_C(0x0014758C)}},
        {{UINT32_C(0x00D4475B), UINT32_C(0x00404279), UINT32_C(0x006905B8),
          UINT32_C(0x00F7FB57), UINT32_C(0x0073BDA7), UINT32_C(0x00269082),
          UINT32_C(0x006B26ED), UINT32_C(0x0027D833), UINT32_C(0x001E216F),
          UINT32_C(0x001360D4), UINT32_C(0x00115751)},
         {UINT32_C(0x00E38DD3), UINT32_C(0x0054A13C), UINT32_C(0x000EBF81),
          UINT32_C(0x0010F4E8), UINT32_C(0x0039AB55), UINT32_C(0x0031188E),
          UINT32_C(0x0027D686), UINT32_C(0x00C9A0DF), UINT32_C(0x0039BD06),
          UINT32_C(0x00350B58), UINT32_C(0x007D7257)},
         {UINT32_C(0x00A31782), UINT32_C(0x000F1C0E), UINT32_C(0x002C8F03),
          UINT32_C(0x00D72648), UINT32_C(0x0039C85F), UINT32_C(0x004A1C54),
          UINT32_C(0x000FF85A), UINT32_C(0x0026A90F), UINT32_C(0x002B4E8C),
          UINT32_C(0x000A4D59), UINT32_C(0x0050B904)}},
        {{UINT32_C(0x006238BE), UINT32_C(0x00175795), UINT32_C(0x00538917),
          UINT32_C(0x007BD984), UINT32_C(0x00292FC6), UINT32_C(0x00080628),
          UINT32_C(0x001B5D25), UINT32_C(0x00D80A64), UINT32_C(0x005B034B),
          UINT32_C(0x00167E5D), UINT32_C(0x00421184)},
         {UINT32_C(0x00BA4129), UINT32_C(0x000FC466), UINT32_C(0x003F5CA0),
          UINT32_C(0x00B0084A), UINT32_C(0x003B6970), UINT32_C(0x0079C8BB),
          UINT32_C(0x006F3E63), UINT32_C(0x00D86F54), UINT32_C(0x003A31F7),
          UINT32_C(0x00382189), UINT32_C(0x005CEAB5)},
         {UINT32_C(0x0041A489), UINT32_C(0x004B3EF7), UINT32_C(0x00591EC7),
          UINT32_C(0x00F08AA6), UINT32_C(0x00580111), UINT32_C(0x0006B198),
          UINT32_C(0x0031386E), UINT32_C(0x00EC23A3), UINT32_C(0x007F21FD),
          UINT32_C(0x005E25F3), UINT32_C(0x0047AC14)}},
        {{UINT32_C(0x00AB4BF3), UINT32_C(0x0070ED4E), UINT32_C(0x001DFAA3),
          UINT32_C(0x006F788D), UINT32_C(0x000470EF), UINT32_C(0x0005F6CC),
          UINT32_C(0x00317FCC), UINT32_C(0x00341719), UINT32_C(0x000AF8E5),
          UINT32_C(0x00265856), UINT32_C(0x004EA29A)},
         {UINT32_C(0x00BD8CE2), UINT32_C(0x0019149B), UINT32_C(0x0010C6C2),
          UINT32_C(0x00D9E127), UINT32_C(0x006B514E), UINT32_C(0x00755F77),
          UINT32_C(0x004E4772), UINT32_C(0x00398A6D), UINT32_C(0x0032566C),
          UINT32_C(0x00029F76), UINT32_C(0x0050B40A)},
         {UINT32_C(0x008B1A27), UINT32_C(0x004EC0C5), UINT32_C(0x0069ED34),
          UINT32_C(0x001088E0), UINT32_C(0x004CD905), UINT32_C(0x00697E9E),
          UINT32_C(0x000A4361), UINT32_C(0x00A3E043), UINT32_C(0x0058F3E3),
          UINT32_C(0x002D4E70), UINT32_C(0x006C75F0)}},
    },
    {
        {{UINT32_C(0x005AB8BD), UINT32_C(0x0029D680), UINT32_C(0x0006977B),
          UINT32_C(0x0074F696), UINT32_C(0x00048954), UINT32_C(0x0028F214),
          UINT32_C(0x0045A271), UINT32_C(0x000DB9C5), UINT32_C(0x00141E0B),
          UINT32_C(0x0064EAF4), UINT32_C(0x0016FA62)},
         {UINT32_C(0x00DD9A68), UINT32_C(0x0020101A), UINT32_C(0x006747C5),
          UINT32_C(0x00C02A2D), UINT32_C(0x00551617), UINT32_C(0x00318D4B),
          UINT32_C(0x003D5380), UINT32_C(0x003B36CE), UINT32_C(0x0042C7B9),
          UINT32_C(0x0068C9EF), UINT32_C(0x000786CD)},
         {UINT32_C(0x0092DAA3), UINT32_C(0x006F3072), UINT32_C(0x0025BB51),
          UINT32_C(0x0082E155), UINT32_C(0x002995E2), UINT32_C(0x00086EAC),
          UINT32_C(0x004295AF), UINT32_C(0x0015CF09), UINT32_C(0x000217DF),
          UINT32_C(0x0025C6FF), UINT32_C(0x006A1FF6)}},
        {{UINT32_C(0x0068992F), UINT32_C(0x0037A443), UINT32_C(0x004E52B8),
          UINT32_C(0x00EB4DD5), UINT32_C(0x00732F40), UINT32_C(0x0039E0FA),
          UINT32_C(0x0035516B), UINT32_C(0x00D1FF12), UINT32_C(0x007B9904),
          UINT32_C(0x0000D042), UINT32_C(0x0061BEA7)},
         {UINT32_C(0x006695E1), UINT32_C(0x0079A990), UINT32_C(0x007D995E),
          UINT32_C(0x008EA8CE), UINT32_C(0x0024E995), UINT32_C(0x0009615C),
          UINT32_C(0x003CF6CE), UINT32_C(0x00077F8E), UINT32_C(0x00350417),
          UINT32_C(0x0062CAC6), UINT32_C(0x0006858A)},
         {UINT32_C(0x0022B953), UINT32_C(0x003B5DF9), UINT32_C(0x004AEDEE),
          UINT32_C(0x000CEEC5), UINT32_C(0x003887F6), UINT32_C(0x000254BB),
          UINT32_C(0x001856BF), UINT32_C(0x00B16F48), UINT32_C(0x00226205),
          UINT32_C(0x001DDE25), UINT32_C(0x006802A0)}},
        {{UINT32_C(0x00D029FB), UINT32_C(0x0024FF9C), UINT32_C(0x003692B5),
          UINT32_C(0x003D6298), UINT32_C(0x005113AA), UINT32_C(0x0016BB74),
          UINT32_C(0x00207B6F), UINT32_C(0x00CFD145), UINT32_C(0x001A0C73),
          UINT32_C(0x003DD3F4), UINT32_C(0x004AC618)},
         {UINT32_C(0x007E0F78), UINT32_C(0x00342407), UINT32_C(0x007BF737),
          UINT32_C(0x008C11E4), UINT32_C(0x001D19AF), UINT32_C(0x0023A2DD),
          UINT32_C(0x00538E8D), UINT32_C(0x00026531), UINT32_C(0x002A0077),
          UINT32_C(0x002FBD3B), UINT32_C(0x000D23EE)},
         {UINT32_C(0x009B883D), UINT32_C(0x000C8276), UINT32_C(0x006F819D),
          UINT32_C(0x00FC2912), UINT32_C(0x00382987), UINT32_C(0x007E7030),
          UINT32_C(0x003EF8E1), UINT32_C(0x00E2007A), UINT32_C(0x0008D395),
          UINT32_C(0x003F0615), UINT32_C(0x00063C97)}},
        {{UINT32_C(0x00156CDE), UINT32_C(0x007EA553), UINT32_C(0x004022D6),
          UINT32_C(0x00DFB476), UINT32_C(0x000AA93D), UINT32_C(0x0075AAD5),
          UINT32_C(0x005CA989), UINT32_C(0x00F4319E), UINT32_C(0x006F4102),
          UINT32_C(0x002EBD52), UINT32_C(0x004C0F6F)},
         {UINT32_C(0x002F3ABE), UINT32_C(0x002C0332), UINT32_C(0x000C7B65),
          UINT32_C(0x00E76C78), UINT32_C(0x002AEA4E), UINT32_C(0x00419F87),
          UINT32_C(0x00340B29), UINT32_C(0x0039375F), UINT32_C(0x005747D0),
          UINT32_C(0x002FBD65), UINT32_C(0x0056D9BF)},
         {UINT32_C(0x00F9E607), UINT32_C(0x00092305), UINT32_C(0x005A5130),
          UINT32_C(0x00B7A777), UINT32_C(0x00393B42), UINT32_C(0x002198A4),
          UINT32_C(0x0044F3BE), UINT32_C(0x00B56C11), UINT32_C(0x000C5006),
          UINT32_C(0x0014F3D3), UINT32_C(0x00204C61)}},
        {{UINT32_C(0x00AA29BE), UINT32_C(0x00452FB8), UINT32_C(0x000DF2B9),
          UINT32_C(0x00F57AEA), UINT32_C(0x00309807), UINT32_C(0x0039FB85),
          UINT32_C(0x00505F6F), UINT32_C(0x00C3979A), UINT32_C(0x0037D4C6),
          UINT32_C(0x0063A3A0), UINT32_C(0x0041BB59)},
         {UINT32_C(0x00979A22), UINT32_C(0x0011A314), UINT32_C(0x004DCD17),
          UINT32_C(0x0073157E), UINT32_C(0x007EA111), UINT32_C(0x005CA164),
          UINT32_C(0x007D14D4), UINT32_C(0x00F4DD95), UINT32_C(0x0065EA29),
          UINT32_C(0x000A8618), UINT32_C(0x00458E9C)},
         {UINT32_C(0x00166F58), UINT32_C(0x003902F4), UINT32_C(0x005DF4F1),
          UINT32_C(0x00FD1412), UINT32_C(0x0065A422), UINT32_C(0x005C9158),
          UINT32_C(0x007FA83F), UINT32_C(0x001B6003), UINT32_C(0x003E37A1),
          UINT32_C(0x00558CA7), UINT32_C(0x005F8B80)}},
        {{UINT32_C(0x0058D515), UINT32_C(0x000E279B), UINT32_C(0x0061A7DC),
          UINT32_C(0x0030FF7E), UINT32_C(0x0036EF25), UINT32_C(0x00545909),
          UINT32_C(0x0071356E), UINT32_C(0x009FA397), UINT32_C(0x006064CF),
          UINT32_C(0x0023899B), UINT32_C(0x0023C04A)},
         {UINT32_C(0x0070B892), UINT32_C(0x003C225C), UINT32_C(0x00641506),
          UINT32_C(0x00BC6E0A), UINT32_C(0x0067DF77), UINT32_C(0x001C1328),
          UINT32_C(0x006FB16A), UINT32_C(0x00DDC3EF), UINT32_C(0x000C0A82),
          UINT32_C(0x0035BBBF), UINT32_C(0x0018D630)},
         {UINT32_C(0x00F762A4), UINT32_C(0x006436A9), UINT32_C(0x0068EFB4),
          UINT32_C(0x00AAEE79), UINT32_C(0x001FA040), UINT32_C(0x005BE9B2),
          UINT32_C(0x0068C25E), UINT32_C(0x0069171C), UINT32_C(0x0069727D),
          UINT32_C(0x004D3596), UINT32_C(0x007D2D07)}},
        {{UINT32_C(0x00E0A6B1), UINT32_C(0x00537BD4), UINT32_C(0x00732E56),
          UINT32_C(0x00054667), UINT32_C(0x00504F91), UINT32_C(0x005BEA24),
          UINT32_C(0x003816A4), UINT32_C(0x006CAC1F), UINT32_C(0x00094C1A),
          UINT32_C(0x007E0D77), UINT32_C(0x001F325B)},
         {UINT32_C(0x0056C790), UINT32_C(0x00696172), UINT32_C(0x004393F7),
          UINT32_C(0x0007BC7D), UINT32_C(0x0024008C), UINT32_C(0x00632185),
          UINT32_C(0x0039D510), UINT32_C(0x00776C63), UINT32_C(0x005303C0),
          UINT32_C(0x00488334), UINT32_C(0x004A46B9)},
         {UINT32_C(0x00CAB13C), UINT32_C(0x007B99B0), UINT32_C(0x00716826),
          UINT32_C(0x0090AADC), UINT32_C(0x003C39E0), UINT32_C(0x0062EF5A),
          UINT32_C(0x00220C12), UINT32_C(0x004D0B33), UINT32_C(0x004E1E9F),
          UINT32_C(0x0009FA96), UINT32_C(0x00553213)}},
        {{UINT32_C(0x00250F6D), UINT32_C(0x007501A6), UINT32_C(0x003B682C),
          UINT32_C(0x0044D730), UINT32_C(0x00649A14), UINT32_C(0x0032BFCE),
          UINT32_C(0x0066ADB8), UINT32_C(0x0034ED5B), UINT32_C(0x0043D7B2),
          UINT32_C(0x0000FFB9), UINT32_C(0x005A4861)},
         {UINT32_C(0x003B7613), UINT32_C(0x0001D6FB), UINT32_C(0x003D0B17),
          UINT32_C(0x007E0DC7), UINT32_C(0x005BC5C1), UINT32_C(0x003BB331),
          UINT32_C(0x0062230B), UINT32_C(0x0061ECC7), UINT32_C(0x005DDB12),
          UINT32_C(0x002700A7), UINT32_C(0x00258078)},
         {UINT32_C(0x005856C3), UINT32_C(0x0046B785), UINT32_C(0x002EE0A7),
          UINT32_C(0x00316F2C), UINT32_C(0x001A3AAB), UINT32_C(0x0069EF00),
          UINT32_C(0x0002A746), UINT32_C(0x0099DDEA), UINT32_C(0x004753D7),
          UINT32_C(0x0062170C), UINT32_C(0x004CA608)}},
        {{UINT32_C(0x0096B8DD), UINT32_C(0x004957F8), UINT32_C(0x007298E2),
          UINT32_C(0x008A7B5F), UINT32_C(0x0001A2BB), UINT32_C(0x004D7F56),
          UINT32_C(0x00560362), UINT32_C(0x001642A5), UINT32_C(0x0017F72E),
          UINT32_C(0x003B6605), UINT32_C(0x00390B61)},
         {UINT32_C(0x0015F82F), UINT32_C(0x000E8511), UINT32_C(0x00668D3B),
          UINT32_C(0x00C4B091), UINT32_C(0x003C935D), UINT32_C(0x005D4F5F),
          UINT32_C(0x0065AFF6), UINT32_C(0x00C88375), UINT32_C(0x00488074),
          UINT32_C(0x00260091), UINT32_C(0x0032A589)},
         {UINT32_C(0x00BCD3E4), UINT32_C(0x0073BD2C), UINT32_C(0x00537827),
          UINT32_C(0x00C860A9), UINT32_C(0x0023CB46), UINT32_C(0x004AF7E7),
          UINT32_C(0x0055A3FB), UINT32_C(0x00D9298A), UINT32_C(0x002A1C35),
          UINT32_C(0x001D8443), UINT32_C(0x00625170)}},
        {{UINT32_C(0x00CC32C8), UINT32_C(0x0025B8DD), UINT32_C(0x0033759B),
          UINT32_C(0x00B90E61), UINT32_C(0x0053232D), UINT32_C(0x00622CCA),
          UINT32_C(0x003F2B2B), UINT32_C(0x0012CE87), UINT32_C(0x000FED16),
          UINT32_C(0x00517920), UINT32_C(0x0026E2BC)},
         {UINT32_C(0x0033B8DF), UINT32_C(0x006BD2B2), UINT32_C(0x002C06A6),
          UINT32_C(0x007B255F), UINT32_C(0x0049E009), UINT32_C(0x00109815),
          UINT32_C(0x0041610E), UINT32_C(0x007AA99D), UINT32_C(0x004D2604),
          UINT32_C(0x001C2975), UINT32_C(0x00419560)},
         {UINT32_C(0x00F4C3F1), UINT32_C(0x00358166), UINT32_C(0x0047A4EF),
          UINT32_C(0x00B32B54), UINT32_C(0x007F5298), UINT32_C(0x0060C7DC),
          UINT32_C(0x004550B3), UINT32_C(0x00381199), UINT32_C(0x004D1985),
          UINT32_C(0x003008AD), UINT32_C(0x001E997E)}},
        {{UINT32_C(0x0080A2DD), UINT32_C(0x0011A8A1), UINT32_C(0x00763776),
          UINT32_C(0x0037A4E8), UINT32_C(0x002DEFD0), UINT32_C(0x005CC3A3),
          UINT32_C(0x0002CD3A), UINT32_C(0x003BDE69), UINT32_C(0x000D3C3B),
          UINT32_C(0x00468707), UINT32_C(0x002C6250)},
         {UINT32_C(0x00DC4853), UINT32_C(0x0013FF85), UINT32_C(0x006F92A3),
          UINT32_C(0x00E2DC5B), UINT32_C(0x002BA1BF), UINT32_C(0x0069003C),
          UINT32_C(0x00696B4A), UINT32_C(0x001FEA22), UINT32_C(0x00737E75),
          UINT32_C(0x002DDA93), UINT32_C(0x00682793)},
         {UINT32_C(0x00792D5C), UINT32_C(0x00756880), UINT32_C(0x0016DEDC),
          UINT32_C(0x00132FF7), UINT32_C(0x0028F65F), UINT32_C(0x00687D6B),
          UINT32_C(0x0042DB10), UINT32_C(0x00F60F17), UINT32_C(0x00244567),
          UINT32_C(0x005EA7EE), UINT32_C(0x00680EE7)}},
        {{UINT32_C(0x0017AD54), UINT32_C(0x00226170), UINT32_C(0x00248F16),
          UINT32_C(0x00E68159), UINT32_C(0x001F85B5), UINT32_C(0x005AEC69),
          UINT32_C(0x00207C21), UINT32_C(0x0080BD65), UINT32_C(0x0072C461),
          UINT32_C(0x000826BF), UINT32_C(0x00721CFE)},
         {UINT32_C(0x00221394), UINT32_C(0x000F6241), UINT32_C(0x00312D0B),
          UINT32_C(0x007310BE), UINT32_C(0x006D18ED), UINT32_C(0x0009B7DC),
          UINT32_C(0x0002ADC6), UINT32_C(0x009D4B71), UINT32_C(0x00652C01),
          UINT32_C(0x001193C0), UINT32_C(0x006CDFD9)},
         {UINT32_C(0x00441B95), UINT32_C(0x005F316E), UINT32_C(0x00055A1A),
          UINT32_C(0x008ABFDB), UINT32_C(0x0050B1C9), UINT32_C(0x00219EE2),
          UINT32_C(0x003C70AE), UINT32_C(0x004C49AE), UINT32_C(0x00791825),
          UINT32_C(0x004EA87D), UINT32_C(0x001C346C)}},
        {{UINT32_C(0x00633E8B), UINT32_C(0x000D64E6), UINT32_C(0x0043AD57),
          UINT32_C(0x0056000C), UINT32_C(0x0022A710), UINT32_C(0x001CF6E7),
          UINT32_C(0x0061C894), UINT32_C(0x0029FCC0), UINT32_C(0x0063A2A7),
          UINT32_C(0x002F042F), UINT32_C(0x006D9C7B)},
         {UINT32_C(0x00C0114A), UINT32_C(0x00054C9F), UINT32_C(0x00014A30),
          UINT32_C(0x008750DC), UINT32_C(0x0014F782), UINT32_C(0x005BDDB7),
          UINT32_C(0x0048FFA9), UINT32_C(0x006F51B9), UINT32_C(0x000ACD25),
          UINT32_C(0x001C791B), UINT32_C(0x006F027E)},
         {UINT32_C(0x0059BE30), UINT32_C(0x0004C8AA), UINT32_C(0x001EEFA6),
          UINT32_C(0x00969A3F), UINT32_C(0x002541FF), UINT32_C(0x00021EAE),
          UINT32_C(0x002335EF), UINT32_C(0x007B34F0), UINT32_C(0x005812F4),
          UINT32_C(0x0035961A), UINT32_C(0x0018E93C)}},
        {{UINT32_C(0x003D4ACA), UINT32_C(0x002344E6), UINT32_C(0x005AE7C9),
          UINT32_C(0x00C22F17), UINT32_C(0x0053F5F0), UINT32_C(0x0037ABD3),
          UINT32_C(0x0064DD19), UINT32_C(0x00A742A2), UINT32_C(0x00762397),
          UINT32_C(0x000E9EEF), UINT32_C(0x00047D34)},
         {UINT32_C(0x00E9B3BB), UINT32_C(0x00532D39), UINT32_C(0x004E866E),
          UINT32_C(0x00F7D682), UINT32_C(0x002ACA85), UINT32_C(0x00552578),
          UINT32_C(0x0033FA4B), UINT32_C(0x00977782), UINT32_C(0x003D410E),
          UINT32_C(0x0003548B), UINT32_C(0x0054FC74)},
         {UINT32_C(0x00C8BBFF), UINT32_C(0x0013F345), UINT32_C(0x0058E472),
          UINT32_C(0x00274591), UINT32_C(0x005597A6), UINT32_C(0x0013E285),
          UINT32_C(0x0057D3BB), UINT32_C(0x0092D7C6), UINT32_C(0x00500460),
          UINT32_C(0x00488CB7), UINT32_C(0x006800FE)}},
        {{UINT32_C(0x0000AA79), UINT32_C(0x002C098B), UINT32_C(0x00759A30),
          UINT32_C(0x002C7125), UINT32_C(0x00199DDC), UINT32_C(0x0075546E),
          UINT32_C(0x00559A27), UINT32_C(0x00DF24BF), UINT32_C(0x00151F99),
          UINT32_C(0x003C4C5B), UINT32_C(0x002EAF89)},
         {UINT32_C(0x00DC697B), UINT32_C(0x00751FA8), UINT32_C(0x0077C24F),
          UINT32_C(0x0038C1A6), UINT32_C(0x0013EBD3), UINT32_C(0x0023A808),
          UINT32_C(0x000B2621), UINT32_C(0x00C685DB), UINT32_C(0x00581796),
          UINT32_C(0x0057D263), UINT32_C(0x0004E256)},
         {UINT32_C(0x00E52262), UINT32_C(0x001D22BD), UINT32_C(0x006A26A0),
          UINT32_C(0x0085E1CA), UINT32_C(0x003EC7B7), UINT32_C(0x002FFB62),
          UINT32_C(0x0067736A), UINT32_C(0x00EF293F), UINT32_C(0x0061C4FE),
          UINT32_C(0x00730AA3), UINT32_C(0x000EB2F6)}},
        {{UINT32_C(0x00641095), UINT32_C(0x0058D612), UINT32_C(0x000AAE18),
          UINT32_C(0x0029DFF3), UINT32_C(0x00001553), UINT32_C(0x007F0860),
          UINT32_C(0x000F0ED0), UINT32_C(0x00A86E36), UINT32_C(0x000DBF80),
          UINT32_C(0x0031F7B2), UINT32_C(0x00229559)},
         {UINT32_C(0x003D6EA7), UINT32_C(0x00417EB0), UINT32_C(0x0030EED3),
          UINT32_C(0x0076B04C), UINT32_C(0x006404B7), UINT32_C(0x00647E89),
          UINT32_C(0x0038E725), UINT32_C(0x00955982), UINT32_C(0x0044F11A),
          UINT32_C(0x0030EB34), UINT32_C(0x0060A0E2)},
         {UINT32_C(0x00FFBA33), UINT32_C(0x00576743), UINT32_C(0x007B4196),
          UINT32_C(0x0065FCEB), UINT32_C(0x00147F68), UINT32_C(0x00753F2E),
          UINT32_C(0x002CE14E), UINT32_C(0x004CC9FA), UINT32_C(0x0008D7B1),
          UINT32_C(0x004C33D1), UINT32_C(0x0045EB11)}},
    },
    {
        {{UINT32_C(0x00178112), UINT32_C(0x004144EF), UINT32_C(0x0015E475),
          UINT32_C(0x00280993), UINT32_C(0x003DFA25), UINT32_C(0x00316893),
          UINT32_C(0x0013A505), UINT32_C(0x002E8E8C), UINT32_C(0x001BE3B1),
          UINT32_C(0x004A5354), UINT32_C(0x005737B1)},
         {UINT32_C(0x005EE99B), UINT32_C(0x00738EC3), UINT32_C(0x000EEBF1),
          UINT32_C(0x006EAA11), UINT32_C(0x006EC5B6), UINT32_C(0x00137DDE),
          UINT32_C(0x0077237C), UINT32_C(0x00B7F2C7), UINT32_C(0x006D2A26),
          UINT32_C(0x005B944F), UINT32_C(0x0077B72B)},
         {UINT32_C(0x00288C6E), UINT32_C(0x005166D7), UINT32_C(0x0048279B),
          UINT32_C(0x009E9476), UINT32_C(0x0021C271), UINT32_C(0x0071BEB4),
          UINT32_C(0x004E74B9), UINT32_C(0x00F2F6D4), UINT32_C(0x00498EF2),
          UINT32_C(0x00769444), UINT32_C(0x00393F71)}},
        {{UINT32_C(0x00CF7E6B), UINT32_C(0x001C84EE), UINT32_C(0x00375656),
          UINT32_C(0x00982B0D), UINT32_C(0x0015275A), UINT32_C(0x0037C644),
          UINT32_C(0x00419783), UINT32_C(0x00E2FD75), UINT32_C(0x005830C6),
          UINT32_C(0x003DDDEF), UINT32_C(0x001D4931)},
         {UINT32_C(0x006F71D9), UINT32_C(0x0013F2A3), UINT32_C(0x007E4CC9),
          UINT32_C(0x0048B895), UINT32_C(0x006E0B00), UINT32_C(0x00535CF4),
          UINT32_C(0x004F83CB), UINT32_C(0x00E80D3A), UINT32_C(0x002893B7),
          UINT32_C(0x0039AF63), UINT32_C(0x00756654)},
         {UINT32_C(0x002DD089), UINT32_C(0x005B7963), UINT32_C(0x00147EEF),
          UINT32_C(0x008FE97D), UINT32_C(0x0074DE7F), UINT32_C(0x0038F059),
          UINT32_C(0x00424B8B), UINT32_C(0x00510FF5), UINT32_C(0x002A9DB5),
          UINT32_C(0x0040775A), UINT32_C(0x0023EC28)}},
        {{UINT32_C(0x004FFE86), UINT32_C(0x005A8445), UINT32_C(0x007B9BE4),
          UINT32_C(0x003CB951), UINT32_C(0x003DA9E0), UINT32_C(0x000B053F),
          UINT32_C(0x0068F80D), UINT32_C(0x008B792C), UINT32_C(0x006E5C50),
          UINT32_C(0x00709425), UINT32_C(0x00695574)},
         {UINT32_C(0x0055360D), UINT32_C(0x00304400), UINT32_C(0x007E3E9A),
          UINT32_C(0x00042D95), UINT32_C(0x006A420D), UINT32_C(0x000DE570),
          UINT32_C(0x0012F771), UINT32_C(0x007BF1D1), UINT32_C(0x005E64D1),
          UINT32_C(0x001011DE), UINT32_C(0x000FD8B3)},
         {UINT32_C(0x0035FF69), UINT32_C(0x0066F82F), UINT32_C(0x00765895),
          UINT32_C(0x00F5BC13), UINT32_C(0x00559AFC), UINT32_C(0x00165B92),
          UINT32_C(0x001A82F9), UINT32_C(0x004B92A4), UINT32_C(0x006FF84F),
          UINT32_C(0x000C3427), UINT32_C(0x0062C571)}},
        {{UINT32_C(0x001B13D2), UINT32_C(0x001AB645), UINT32_C(0x007C40CE),
          UINT32_C(0x007EA6AF), UINT32_C(0x00411986), UINT32_C(0x00393A3E),
          UINT32_C(0x003CE614), UINT32_C(0x006D6293), UINT32_C(0x003D6770),
          UINT32_C(0x003CBAD3), UINT32_C(0x005F61B3)},
         {UINT32_C(0x007F1514), UINT32_C(0x00563424), UINT32_C(0x00333C42),
          UINT32_C(0x0091B025), UINT32_C(0x000AC50C), UINT32_C(0x00572275),
          UINT32_C(0x00592815), UINT32_C(0x002DB85E), UINT32_C(0x0012ADB7),
          UINT32_C(0x00696D1D), UINT32_C(0x000924E7)},
         {UINT32_C(0x008A69B5), UINT32_C(0x0042A7C5), UINT32_C(0x007608DB),
          UINT32_C(0x00E7F3E0), UINT32_C(0x005993D5), UINT32_C(0x0004961E),
          UINT32_C(0x0076A481), UINT32_C(0x00A7DA96), UINT32_C(0x007E1606),
          UINT32_C(0x00752F27), UINT32_C(0x006EA269)}},
        {{UINT32_C(0x00E74931), UINT32_C(0x002B3168), UINT32_C(0x0062191F),
          UINT32_C(0x007F0A08), UINT32_C(0x00407602), UINT32_C(0x005E821F),
          UINT32_C(0x004B971A), UINT32_C(0x009FAA40), UINT32_C(0x00617415),
          UINT32_C(0x0019AF4C), UINT32_C(0x000A1378)},
         {UINT32_C(0x001CF746), UINT32_C(0x00698B21), UINT32_C(0x003AF2B3),
          UINT32_C(0x0018D41A), UINT32_C(0x00032EA5), UINT32_C(0x006A2A53),
          UINT32_C(0x0021B364), UINT32_C(0x004A1660), UINT32_C(0x00524EDD),
          UINT32_C(0x00537B8F), UINT32_C(0x0038BE03)},
         {UINT32_C(0x006C56DF), UINT32_C(0x0042F0FB), UINT32_C(0x000A9D1A),
          UINT32_C(0x007BE0C1), UINT32_C(0x006A26F4), UINT32_C(0x0034FD46),
          UINT32_C(0x005C512E), UINT32_C(0x00F3B887), UINT32_C(0x00741791),
          UINT32_C(0x000B6109), UINT32_C(0x007ED098)}},
        {{UINT32_C(0x002F95C3), UINT32_C(0x0075A30D), UINT32_C(0x004F8EA8),
          UINT32_C(0x00D36AE9), UINT32_C(0x003B9F94), UINT32_C(0x0050D27A),
          UINT32_C(0x0047AFFF), UINT32_C(0x00FF1072), UINT32_C(0x006EBA0D),
          UINT32_C(0x007147B6), UINT32_C(0x007C9C06)},
         {UINT32_C(0x007D647C), UINT32_C(0x00491D36), UINT32_C(0x003D3169),
          UINT32_C(0x00E48196), UINT32_C(0x005C2776), UINT32_C(0x0071BB7A),
          UINT32_C(0x000E3716), UINT32_C(0x00EEE6BA), UINT32_C(0x007A2FD6),
          UINT32_C(0x0066EADE), UINT32_C(0x006A3D3C)},
         {UINT32_C(0x00D712D2), UINT32_C(0x0030AFB5), UINT32_C(0x000B6DE0),
          UINT32_C(0x00CA9B95), UINT32_C(0x003CB508), UINT32_C(0x0002574C),
          UINT32_C(0x004F7664), UINT32_C(0x008BDA7C), UINT32_C(0x0052F4AE),
          UINT32_C(0x007C9C6D), UINT32_C(0x00640826)}},
        {{UINT32_C(0x00E39A36), UINT32_C(0x004E864A), UINT32_C(0x0041824A),
          UINT32_C(0x0014E9D0), UINT32_C(0x006B0DD5), UINT32_C(0x003F0191),
          UINT32_C(0x006B3AA8), UINT32_C(0x000F82B6), UINT32_C(0x00646713),
          UINT32_C(0x004411BF), UINT32_C(0x006EC066)},
         {UINT32_C(0x0055A118), UINT32_C(0x001D8588), UINT32_C(0x00098B01),
          UINT32_C(0x005B872B), UINT32_C(0x000612D7), UINT32_C(0x0015BB6C),
          UINT32_C(0x003EA8DE), UINT32_C(0x00492CBB), UINT32_C(0x0002B390),
          UINT32_C(0x002F64C6), UINT32_C(0x00309519)},
         {UINT32_C(0x00BCD94C), UINT32_C(0x000453AB), UINT32_C(0x0004DF45),
          UINT32_C(0x000AF59E), UINT32_C(0x00224611), UINT32_C(0x0046CB1F),
          UINT32_C(0x0037D384), UINT32_C(0x00704C79), UINT32_C(0x004E9EAE),
          UINT32_C(0x005F6C61), UINT32_C(0x0033B91C)}},
        {{UINT32_C(0x00C4EB7B), UINT32_C(0x001BB1AE), UINT32_C(0x000DF746),
          UINT32_C(0x002F5C66), UINT32_C(0x00386901), UINT32_C(0x004503D0),
          UINT32_C(0x00243C4F), UINT32_C(0x00F631A8), UINT32_C(0x0072318C),
          UINT32_C(0x0067781F), UINT32_C(0x0062555B)},
         {UINT32_C(0x009DF20B), UINT32_C(0x007957AF), UINT32_C(0x0048E896),
          UINT32_C(0x00266C31), UINT32_C(0x00606385), UINT32_C(0x00182ADB),
          UINT32_C(0x000F2D0A), UINT32_C(0x0047F1DB), UINT32_C(0x00612A5A),
          UINT32_C(0x00585C22), UINT32_C(0x00138DA1)},
         {UINT32_C(0x002B00C5), UINT32_C(0x007C31F7), UINT32_C(0x0027D051),
          UINT32_C(0x009836BE), UINT32_C(0x005DD824), UINT32_C(0x003C85C8),
          UINT32_C(0x00037B92), UINT32_C(0x005A36F0), UINT32_C(0x00072882),
          UINT32_C(0x00556F25), UINT32_C(0x005C74C4)}},
        {{UINT32_C(0x00200CDA), UINT32_C(0x00142D7E), UINT32_C(0x000C5F19),
          UINT32_C(0x0033CB24), UINT32_C(0x0062D137), UINT32_C(0x00370C3B),
          UINT32_C(0x007C19E3), UINT32_C(0x002C37A4), UINT32_C(0x0056C3B7),
          UINT32_C(0x0012591E), UINT32_C(0x005C6251)},
         {UINT32_C(0x00B8567F), UINT32_C(0x007942F3), UINT32_C(0x002971B5),
          UINT32_C(0x007509BB), UINT32_C(0x004489A3), UINT32_C(0x00090D38),
          UINT32_C(0x0049BA67), UINT32_C(0x00A295D7), UINT32_C(0x0006DE00),
          UINT32_C(0x005529F0), UINT32_C(0x0068D340)},
         {UINT32_C(0x008E1FEE), UINT32_C(0x002BADB8), UINT32_C(0x0060B20C),
          UINT32_C(0x00DE49F8), UINT32_C(0x0006AE06), UINT32_C(0x005BAE81),
          UINT32_C(0x00153FDC), UINT32_C(0x002DCC1B), UINT32_C(0x000F1E26),
          UINT32_C(0x00005870), UINT32_C(0x003CB27C)}},
        {{UINT32_C(0x00BA06D8), UINT32_C(0x0028ABA3), UINT32_C(0x004A0A47),
          UINT32_C(0x000E951E), UINT32_C(0x00609146), UINT32_C(0x00213816),
          UINT32_C(0x005657D3), UINT32_C(0x00404103), UINT32_C(0x0041BEE3),
          UINT32_C(0x001CC688), UINT32_C(0x003A758E)},
         {UINT32_C(0x00F8BBE3), UINT32_C(0x00641D00), UINT32_C(0x00119DD5),
          UINT32_C(0x004610E6), UINT32_C(0x0042B1F2), UINT32_C(0x00065991),
          UINT32_C(0x00400456), UINT32_C(0x0031F869), UINT32_C(0x00467CE5),
          UINT32_C(0x002ACA8A), UINT32_C(0x004AE71F)},
         {UINT32_C(0x00E75B70), UINT32_C(0x0045C331), UINT32_C(0x002C67D3),
          UINT32_C(0x002BAD62), UINT32_C(0x006D4C0D), UINT32_C(0x003E98E0),
          UINT32_C(0x0025DD15), UINT32_C(0x001652BE), UINT32_C(0x00138D53),
          UINT32_C(0x004CC0B3), UINT32_C(0x002588D3)}},
        {{UINT32_C(0x0015F134), UINT32_C(0x00758619), UINT32_C(0x005ABA25),
          UINT32_C(0x008AE40E), UINT32_C(0x00678EB8), UINT32_C(0x0001E6CD),
          UINT32_C(0x006265B1), UINT32_C(0x00D3C52B), UINT32_C(0x002372C9),
          UINT32_C(0x0008A5C9), UINT32_C(0x006ACFA4)},
         {UINT32_C(0x009F8482), UINT32_C(0x0045994C), UINT32_C(0x005989D0),
          UINT32_C(0x0048FFCC), UINT32_C(0x00242063), UINT32_C(0x000A8DD0),
          UINT32_C(0x005A0F78), UINT32_C(0x000518C7), UINT32_C(0x0039D0FB),
          UINT32_C(0x00116011), UINT32_C(0x0074BBE1)},
         {UINT32_C(0x008817CF), UINT32_C(0x0035F03B), UINT32_C(0x0008E964),
          UINT32_C(0x00DBB160), UINT32_C(0x0017FB04), UINT32_C(0x006F931C),
          UINT32_C(0x0024585A), UINT32_C(0x0043E352), UINT32_C(0x005B3B89),
          UINT32_C(0x0041A78E), UINT32_C(0x004EE10D)}},
        {{UINT32_C(0x00B5B594), UINT32_C(0x00238C49), UINT32_C(0x0061A952),
          UINT32_C(0x00547564), UINT32_C(0x0017E9B6), UINT32_C(0x004A85C1),
          UINT32_C(0x00092964), UINT32_C(0x00E0030F), UINT32_C(0x0002C024),
          UINT32_C(0x006FDEC5), UINT32_C(0x003414A1)},
         {UINT32_C(0x005F899E), UINT32_C(0x00317B47), UINT32_C(0x00390FE7),
          UINT32_C(0x0081FD30), UINT32_C(0x00666C28), UINT32_C(0x00310591),
          UINT32_C(0x000A02ED), UINT32_C(0x00C5E37F), UINT32_C(0x00200BEA),
          UINT32_C(0x004E58B8), UINT32_C(0x0006090A)},
         {UINT32_C(0x009AED9F), UINT32_C(0x006353DF), UINT32_C(0x004C8001),
          UINT32_C(0x00684029), UINT32_C(0x00278C84), UINT32_C(0x00476EFD),
          UINT32_C(0x002269BF), UINT32_C(0x0061CFF8), UINT32_C(0x006308BB),
          UINT32_C(0x0038C1A8), UINT32_C(0x000ED4DD)}},
        {{UINT32_C(0x0096CDC1), UINT32_C(0x004A93DE), UINT32_C(0x0017141A),
          UINT32_C(0x000CAE27), UINT32_C(0x00633C72), UINT32_C(0x001DD494),
          UINT32_C(0x005D5019), UINT32_C(0x007B392A), UINT32_C(0x004E6770),
          UINT32_C(0x0057CDFA), UINT32_C(0x0070ED69)},
         {UINT32_C(0x00A5AAC7), UINT32_C(0x0017F8BA), UINT32_C(0x0012D3F8),
          UINT32_C(0x00D2AEF7), UINT32_C(0x00203DAB), UINT32_C(0x001A6E08),
          UINT32_C(0x000F9D07), UINT32_C(0x003CEB53), UINT32_C(0x001F11A2),
          UINT32_C(0x00146100), UINT32_C(0x002BF207)},
         {UINT32_C(0x00AE2A02), UINT32_C(0x003FEF01), UINT32_C(0x00095E8C),
          UINT32_C(0x001BC27F), UINT32_C(0x00579C72), UINT32_C(0x007785A3),
          UINT32_C(0x000CF4D0), UINT32_C(0x00B77DC2), UINT32_C(0x00771FCC),
          UINT32_C(0x006D028D), UINT32_C(0x0072A687)}},
        {{UINT32_C(0x00FB5F0C), UINT32_C(0x000952F3), UINT32_C(0x0011F5D5),
          UINT32_C(0x0065371A), UINT32_C(0x006D7E88), UINT32_C(0x0054191C),
          UINT32_C(0x0076F7CF), UINT32_C(0x006B48DC), UINT32_C(0x006FD352),
          UINT32_C(0x004F1AA7), UINT32_C(0x007F5B13)},
         {UINT32_C(0x00606E6E), UINT32_C(0x00363808), UINT32_C(0x00407081),
          UINT32_C(0x0004FC3F), UINT32_C(0x00448579), UINT32_C(0x005D29C4),
          UINT32_C(0x001A127D), UINT32_C(0x005B9EEE), UINT32_C(0x00653D8E),
          UINT32_C(0x0028A4DF), UINT32_C(0x00628593)},
         {UINT32_C(0x00AC7820), UINT32_C(0x0030A9D3), UINT32_C(0x0071BE3A),
          UINT32_C(0x005FB4E5), UINT32_C(0x002512AE), UINT32_C(0x006EAE7D),
          UINT32_C(0x001D9B40), UINT32_C(0x005C1369), UINT32_C(0x00786C22),
          UINT32_C(0x0047AFEE), UINT32_C(0x00784A75)}},
        {{UINT32_C(0x00213EE3), UINT32_C(0x006F9827), UINT32_C(0x0021482B),
          UINT32_C(0x00E1390C), UINT32_C(0x00560D0B), UINT32_C(0x0050DD23),
          UINT32_C(0x0064A528), UINT32_C(0x00BA7CFF), UINT32_C(0x002D8C84),
          UINT32_C(0x0016175A), UINT32_C(0x005016A4)},
         {UINT32_C(0x00776C25), UINT32_C(0x000CB993), UINT32_C(0x0023BE80),
          UINT32_C(0x005A52FE), UINT32_C(0x002D117A), UINT32_C(0x00199CDB),
          UINT32_C(0x0052EB5E), UINT32_C(0x008D6F49), UINT32_C(0x007D7649),
          UINT32_C(0x0027048D), UINT32_C(0x001A9C36)},
         {UINT32_C(0x00632283), UINT32_C(0x00211DC7), UINT32_C(0x005DFA7C),
          UINT32_C(0x00F9046B), UINT32_C(0x00588CD6), UINT32_C(0x003FAF32),
          UINT32_C(0x007F4AED), UINT32_C(0x00575BC2), UINT32_C(0x007DBB8A),
          UINT32_C(0x0005122F), UINT32_C(0x006253CC)}},
        {{UINT32_C(0x000D1BD2), UINT32_C(0x001AF81A), UINT32_C(0x0037C285),
          UINT32_C(0x00D871E6), UINT32_C(0x0062340E), UINT32_C(0x0071909A),
          UINT32_C(0x000F0E01), UINT32_C(0x0076262E), UINT32_C(0x007BF9C3),
          UINT32_C(0x007F40FC), UINT32_C(0x000B2BC5)},
         {UINT32_C(0x002724D3), UINT32_C(0x00504EE8), UINT32_C(0x00609955),
          UINT32_C(0x0075E11A), UINT32_C(0x000CB36C), UINT32_C(0x0056D4AF),
          UINT32_C(0x000E59E9), UINT32_C(0x003E6912), UINT32_C(0x00789AD2),
          UINT32_C(0x00575219), UINT32_C(0x007D7B6C)},
         {UINT32_C(0x0072E3DB), UINT32_C(0x0050D327), UINT32_C(0x000F9230),
          UINT32_C(0x00AFB63A), UINT32_C(0x0008E760), UINT32_C(0x0059EB9D),
          UINT32_C(0x003A4CC1), UINT32_C(0x00C1CE75), UINT32_C(0x00497AD2),
          UINT32_C(0x006BAB27), UINT32_C(0x002B8174)}},
    },
    {
        {{UINT32_C(0x00C72DC5), UINT32_C(0x00771E4C), UINT32_C(0x007FE32B),
          UINT32_C(0x00502045), UINT32_C(0x00047BCA), UINT32_C(0x005E99DD),
          UINT32_C(0x0062584D), UINT32_C(0x00C26155), UINT32_C(0x007641CF),
          UINT32_C(0x00519072), UINT32_C(0x0008A95B)},
         {UINT32_C(0x00B1CB39), UINT32_C(0x000965FD), UINT32_C(0x0031E4AF),
          UINT32_C(0x0030360A), UINT32_C(0x001C457C), UINT32_C(0x003BB754),
          UINT32_C(0x005CEAE2), UINT32_C(0x000B7485), UINT32_C(0x00211EE1),
          UINT32_C(0x005286E0), UINT32_C(0x00797D03)},
         {UINT32_C(0x00220D40), UINT32_C(0x004CA0AD), UINT32_C(0x0021A77A),
          UINT32_C(0x00364CE1), UINT32_C(0x006676A3), UINT32_C(0x0038EE70),
          UINT32_C(0x001B66BF), UINT32_C(0x00A09800), UINT32_C(0x00714B36),
          UINT32_C(0x0068F47C), UINT32_C(0x000FBD61)}},
        {{UINT32_C(0x00AADA87), UINT32_C(0x001E516D), UINT32_C(0x001DDDC1),
          UINT32_C(0x002496F0), UINT32_C(0x0079D73B), UINT32_C(0x006E84CA),
          UINT32_C(0x0001F77F), UINT32_C(0x004ED6A4), UINT32_C(0x004F9268),
          UINT32_C(0x002C2D9B), UINT32_C(0x00452C79)},
         {UINT32_C(0x005C5FB5), UINT32_C(0x001401F9), UINT32_C(0x001619FE),
          UINT32_C(0x0073A62F), UINT32_C(0x0027A568), UINT32_C(0x00410CA3),
          UINT32_C(0x007090B1), UINT32_C(0x0088392F), UINT32_C(0x00025F8C),
          UINT32_C(0x00470FB6), UINT32_C(0x00400202)},
         {UINT32_C(0x00FB4AF5), UINT32_C(0x004A6D33), UINT32_C(0x0067B3D7),
          UINT32_C(0x006C233F), UINT32_C(0x006659A6), UINT32_C(0x003370F0),
          UINT32_C(0x0071C750), UINT32_C(0x007CF562), UINT32_C(0x006B187A),
          UINT32_C(0x0044EC7F), UINT32_C(0x00780B9E)}},
        {{UINT32_C(0x007A1682), UINT32_C(0x00775D09), UINT32_C(0x000CA759),
          UINT32_C(0x005178EB), UINT32_C(0x001128C9), UINT32_C(0x00453CCE),
          UINT32_C(0x002B94ED), UINT32_C(0x00A6541E), UINT32_C(0x0029A8C4),
          UINT32_C(0x0029D1E3), UINT32_C(0x00318CEC)},
         {UINT32_C(0x002EA810), UINT32_C(0x002E2D2A), UINT32_C(0x00760A1E),
          UINT32_C(0x00768B26), UINT32_C(0x0012C353), UINT32_C(0x007A143A),
          UINT32_C(0x007F1485), UINT32_C(0x002F5E05), UINT32_C(0x006543DE),
          UINT32_C(0x0005DA47), UINT32_C(0x0062D462)},
         {UINT32_C(0x0056C417), UINT32_C(0x0020F8E7), UINT32_C(0x0021B1CB),
          UINT32_C(0x00DEBC01), UINT32_C(0x0049F124), UINT32_C(0x0063A3EF),
          UINT32_C(0x006B1EFC), UINT32_C(0x005DD025), UINT32_C(0x00579F7C),
          UINT32_C(0x005282E6), UINT32_C(0x003743A4)}},
        {{UINT32_C(0x00D850C1), UINT32_C(0x004304DD), UINT32_C(0x0031CA95),
          UINT32_C(0x00ABA381), UINT32_C(0x00557845), UINT32_C(0x00170B2A),
          UINT32_C(0x00269F01), UINT32_C(0x00F14562), UINT32_C(0x00177E37),
          UINT32_C(0x0000AEF5), UINT32_C(0x0016E67A)},
         {UINT32_C(0x008429CD), UINT32_C(0x004F3F15), UINT32_C(0x00310961),
          UINT32_C(0x00018EC0), UINT32_C(0x0017C520), UINT32_C(0x005A3C1E),
          UINT32_C(0x00069B19), UINT32_C(0x00BBC85D), UINT32_C(0x006A8255),
          UINT32_C(0x0005A237), UINT32_C(0x00671035)},
         {UINT32_C(0x00397785), UINT32_C(0x000B8DE6), UINT32_C(0x00034C5B),
          UINT32_C(0x00335B11), UINT32_C(0x005C7862), UINT32_C(0x002482C8),
          UINT32_C(0x0037A568), UINT32_C(0x00679E39), UINT32_C(0x0056810F),
          UINT32_C(0x0000E8F6), UINT32_C(0x003D1330)}},
        {{UINT32_C(0x00FAF1CD), UINT32_C(0x0060A8C5), UINT32_C(0x00508B76),
          UINT32_C(0x004F108E), UINT32_C(0x0054D331), UINT32_C(0x0027A7E8),
          UINT32_C(0x00346905), UINT32_C(0x00450F84), UINT32_C(0x001D3E9C),
          UINT32_C(0x000B0762), UINT32_C(0x00032396)},
         {UINT32_C(0x004FAD75), UINT32_C(0x00013909), UINT32_C(0x00279AA7),
          UINT32_C(0x00661D96), UINT32_C(0x0011C8F3), UINT32_C(0x004E1E02),
          UINT32_C(0x0079B899), UINT32_C(0x00219A22), UINT32_C(0x007D00F4),
          UINT32_C(0x0073A047), UINT32_C(0x00112302)},
         {UINT32_C(0x00C06EE2), UINT32_C(0x003FE63F), UINT32_C(0x0041BD79),
          UINT32_C(0x00096C7C), UINT32_C(0x00716A9D), UINT32_C(0x00616E56),
          UINT32_C(0x003FA2BD), UINT32_C(0x006CC575), UINT32_C(0x0077B6FA),
          UINT32_C(0x005D8484), UINT32_C(0x004EC884)}},
        {{UINT32_C(0x00EEDE54), UINT32_C(0x0060BECB), UINT32_C(0x00454F6A),
          UINT32_C(0x007CA867), UINT32_C(0x003128D5), UINT32_C(0x001A6257),
          UINT32_C(0x001C9D64), UINT32_C(0x0047D7B2), UINT32_C(0x0048D9D4),
          UINT32_C(0x00387A36), UINT32_C(0x0030150F)},
         {UINT32_C(0x00869B40), UINT32_C(0x004423AB), UINT32_C(0x006A0C98),
          UINT32_C(0x004BE18D), UINT32_C(0x000680E9), UINT32_C(0x006F03C8),
          UINT32_C(0x0035097A), UINT32_C(0x00163A09), UINT32_C(0x00036511),
          UINT32_C(0x002020C4), UINT32_C(0x0032F125)},
         {UINT32_C(0x006499A6), UINT32_C(0x0032DBAB), UINT32_C(0x0050F205),
          UINT32_C(0x00127C8C), UINT32_C(0x0071C444), UINT32_C(0x00610493),
          UINT32_C(0x0057B4B2), UINT32_C(0x00B0B2CA), UINT32_C(0x0034A155),
          UINT32_C(0x006D7797), UINT32_C(0x0063E346)}},
        {{UINT32_C(0x0030C824), UINT32_C(0x00393557), UINT32_C(0x002AA53A),
          UINT32_C(0x00232B1F), UINT32_C(0x007E74FF), UINT32_C(0x0058171D),
          UINT32_C(0x000328E4), UINT32_C(0x0085BA6B), UINT32_C(0x0013D154),
          UINT32_C(0x0018258C), UINT32_C(0x006FA2B0)},
         {UINT32_C(0x00C3FCC1), UINT32_C(0x006B3441), UINT32_C(0x005E8829),
          UINT32_C(0x00B3314C), UINT32_C(0x0004ECB2), UINT32_C(0x006B3C89),
          UINT32_C(0x003F2F04), UINT32_C(0x0092C88B), UINT32_C(0x001EC53B),
          UINT32_C(0x0031EA9C), UINT32_C(0x000BDC2A)},
         {UINT32_C(0x0063FFE8), UINT32_C(0x000EFB74), UINT32_C(0x0015043F),
          UINT32_C(0x005F290A), UINT32_C(0x001DA041), UINT32_C(0x0060291C),
          UINT32_C(0x007C2769), UINT32_C(0x00EB677A), UINT32_C(0x0043B7A4),
          UINT32_C(0x0033A181), UINT32_C(0x003F9D2D)}},
        {{UINT32_C(0x006CC3EF), UINT32_C(0x002F3089), UINT32_C(0x0061B741),
          UINT32_C(0x007537D8), UINT32_C(0x00717E80), UINT32_C(0x000BD6BF),
          UINT32_C(0x003D6972), UINT32_C(0x00BE0A56), UINT32_C(0x007C075B),
          UINT32_C(0x005C62F6), UINT32_C(0x003CA2DF)},
         {UINT32_C(0x00171808), UINT32_C(0x005A205B), UINT32_C(0x0005AD96),
          UINT32_C(0x0058D8E1), UINT32_C(0x00423531), UINT32_C(0x005C11A5),
          UINT32_C(0x00169496), UINT32_C(0x001AB66A), UINT32_C(0x002F6C8B),
          UINT32_C(0x00668CFD), UINT32_C(0x006B0027)},
         {UINT32_C(0x00528A82), UINT32_C(0x002B2713), UINT32_C(0x0079DE04),
          UINT32_C(0x00EF8CF9), UINT32_C(0x006263B9), UINT32_C(0x00720507),
          UINT32_C(0x0052404F), UINT32_C(0x003C93E8), UINT32_C(0x00100A85),
          UINT32_C(0x006D3FFA), UINT32_C(0x003B14B0)}},
        {{UINT32_C(0x00F84103), UINT32_C(0x002EFED0), UINT32_C(0x004DF3E6),
          UINT32_C(0x001A7373), UINT32_C(0x004C8064), UINT32_C(0x0032CAAE),
          UINT32_C(0x000E2E35), UINT32_C(0x00099871), UINT32_C(0x0043615C),
          UINT32_C(0x005D4D9B), UINT32_C(0x003A809B)},
         {UINT32_C(0x00688B18), UINT32_C(0x003E6C8B), UINT32_C(0x007DA089),
          UINT32_C(0x00470987), UINT32_C(0x0006E04D), UINT32_C(0x00346F12),
          UINT32_C(0x0054964B), UINT32_C(0x001C7FB1), UINT32_C(0x0028F4AF),
          UINT32_C(0x006ABA8D), UINT32_C(0x007BEAD8)},
         {UINT32_C(0x00A4DB09), UINT32_C(0x000F021A), UINT32_C(0x000E3BF3),
          UINT32_C(0x00347724), UINT32_C(0x001CD5E3), UINT32_C(0x00570A82),
          UINT32_C(0x001A702F), UINT32_C(0x00AF41F7), UINT32_C(0x005573E8),
          UINT32_C(0x007B6CC3), UINT32_C(0x000E6B2F)}},
        {{UINT32_C(0x00C4323E), UINT32_C(0x005E5A07), UINT32_C(0x006E0C89),
          UINT32_C(0x00018D1E), UINT32_C(0x00582AB5), UINT32_C(0x004E8211),
          UINT32_C(0x0021ED53), UINT32_C(0x006B639D), UINT32_C(0x00505AE2),
          UINT32_C(0x007DF924), UINT32_C(0x002AA137)},
         {UINT32_C(0x00C33279), UINT32_C(0x00685341), UINT32_C(0x00643C48),
          UINT32_C(0x00ACAC38), UINT32_C(0x005AE420), UINT32_C(0x00514A01),
          UINT32_C(0x006BEB21), UINT32_C(0x003CC859), UINT32_C(0x0017135D),
          UINT32_C(0x004ADA1B), UINT32_C(0x007CF53B)},
         {UINT32_C(0x006149FF), UINT32_C(0x00353234), UINT32_C(0x0000FE22),
          UINT32_C(0x001F09D7), UINT32_C(0x00021F65), UINT32_C(0x0049C3AD),
          UINT32_C(0x001C1A78), UINT32_C(0x00E00DFB), UINT32_C(0x00228A53),
          UINT32_C(0x005812BD), UINT32_C(0x00407161)}},
        {{UINT32_C(0x00AC8719), UINT32_C(0x004B5C52), UINT32_C(0x001AD60A),
          UINT32_C(0x001B2FAC), UINT32_C(0x004CD6B6), UINT32_C(0x002823C4),
          UINT32_C(0x003BE7C2), UINT32_C(0x009BDCCC), UINT32_C(0x00566B2F),
          UINT32_C(0x00205A09), UINT32_C(0x006C27CE)},
         {UINT32_C(0x0053FC77), UINT32_C(0x003F8121), UINT32_C(0x0073622F),
          UINT32_C(0x00775975), UINT32_C(0x007FED73), UINT32_C(0x0041C648),
          UINT32_C(0x00462A53), UINT32_C(0x00FFBFD4), UINT32_C(0x0011A7DF),
          UINT32_C(0x003092B5), UINT32_C(0x00261A53)},
         {UINT32_C(0x009AAFB7), UINT32_C(0x0013BCE1), UINT32_C(0x004971F6),
          UINT32_C(0x00EDD1B3), UINT32_C(0x002525B4), UINT32_C(0x0058D61A),
          UINT32_C(0x0066E9D1), UINT32_C(0x009B73B8), UINT32_C(0x0033C84A),
          UINT32_C(0x0006CAB6), UINT32_C(0x00008EB2)}},
        {{UINT32_C(0x00492C5D), UINT32_C(0x005CF97D), UINT32_C(0x0007722C),
          UINT32_C(0x0066F3B8), UINT32_C(0x000306A7), UINT32_C(0x007D6927),
          UINT32_C(0x0023F020), UINT32_C(0x00BD1D41), UINT32_C(0x00497C08),
          UINT32_C(0x005699EF), UINT32_C(0x00369E3D)},
         {UINT32_C(0x0025F79F), UINT32_C(0x005176A4), UINT32_C(0x003D2CDA),
          UINT32_C(0x00F5AAC3), UINT32_C(0x000A2AD0), UINT32_C(0x006D725F),
          UINT32_C(0x000E7277), UINT32_C(0x00B14CC5), UINT32_C(0x002D5FC8),
          UINT32_C(0x007F764F), UINT32_C(0x0050EF13)},
         {UINT32_C(0x0097E85B), UINT32_C(0x001C0CFD), UINT32_C(0x006027C7),
          UINT32_C(0x0038732E), UINT32_C(0x003385DC), UINT32_C(0x006F67DF),
          UINT32_C(0x0038EF81), UINT32_C(0x00F717C0), UINT32_C(0x000B4878),
          UINT32_C(0x00099CD4), UINT32_C(0x0071787D)}},
        {{UINT32_C(0x000EB7F2), UINT32_C(0x007F42B8), UINT32_C(0x0070653E),
          UINT32_C(0x00320A4F), UINT32_C(0x0016BB51), UINT32_C(0x00691D90),
          UINT32_C(0x00138916), UINT32_C(0x00D043DE), UINT32_C(0x00107F57),
          UINT32_C(0x001DCB80), UINT32_C(0x0059F0BB)},
         {UINT32_C(0x005CD9B5), UINT32_C(0x002A2874), UINT32_C(0x000600A5),
          UINT32_C(0x00235734), UINT32_C(0x002313EF), UINT32_C(0x00312DEA),
          UINT32_C(0x0029CA11), UINT32_C(0x00973048), UINT32_C(0x004D4134),
          UINT32_C(0x003E99AC), UINT32_C(0x000867B4)},
         {UINT32_C(0x002FA5A2), UINT32_C(0x00413E42), UINT32_C(0x00772166),
          UINT32_C(0x001C593A), UINT32_C(0x006949A2), UINT32_C(0x00786FF6),
          UINT32_C(0x003BB2E2), UINT32_C(0x002BC314), UINT32_C(0x00125CC6),
          UINT32_C(0x007B110C), UINT32_C(0x004E5352)}},
        {{UINT32_C(0x00E38B2F), UINT32_C(0x00549D88), UINT32_C(0x00451B24),
          UINT32_C(0x00237AB5), UINT32_C(0x000DAF24), UINT32_C(0x00028B85),
          UINT32_C(0x0002439F), UINT32_C(0x007FF9B1), UINT32_C(0x004B3263),
          UINT32_C(0x007B2514), UINT32_C(0x00130159)},
         {UINT32_C(0x00CBEDBE), UINT32_C(0x00164E8E), UINT32_C(0x006D1617),
          UINT32_C(0x00971E62), UINT32_C(0x0014CEC3), UINT32_C(0x00654E4C),
          UINT32_C(0x003EE5FE), UINT32_C(0x00D5DB3C), UINT32_C(0x0007E501),
          UINT32_C(0x0059EAD8), UINT32_C(0x0033FEF5)},
         {UINT32_C(0x00F396A9), UINT32_C(0x000E12CC), UINT32_C(0x0066F113),
          UINT32_C(0x0032A657), UINT32_C(0x00558CEA), UINT32_C(0x0035397E),
          UINT32_C(0x0072BA41), UINT32_C(0x001BAC1F), UINT32_C(0x00458EAD),
          UINT32_C(0x000176E4), UINT32_C(0x006D9827)}},
        {{UINT32_C(0x0054ACCB), UINT32_C(0x006837A0), UINT32_C(0x005E0E17),
          UINT32_C(0x002D46A3), UINT32_C(0x001EC13B), UINT32_C(0x0055ED3F),
          UINT32_C(0x004AD796), UINT32_C(0x009822FD), UINT32_C(0x006E60A8),
          UINT32_C(0x0019C052), UINT32_C(0x0047770E)},
         {UINT32_C(0x002D311B), UINT32_C(0x0025BEF8), UINT32_C(0x00441B88),
          UINT32_C(0x000175A1), UINT32_C(0x0077C008), UINT32_C(0x007C334F),
          UINT32_C(0x003B3992), UINT32_C(0x00CA38F0), UINT32_C(0x0002777D),
          UINT32_C(0x003C8B93), UINT32_C(0x0028F8C6)},
         {UINT32_C(0x008E76E6), UINT32_C(0x000A66B8), UINT32_C(0x0049E5D6),
          UINT32_C(0x00E8B276), UINT32_C(0x0032543C), UINT32_C(0x0027A563),
          UINT32_C(0x0000CF52), UINT32_C(0x00DEDDB7), UINT32_C(0x00401370),
          UINT32_C(0x000F47DB), UINT32_C(0x00502929)}},
        {{UINT32_C(0x00AECCC3), UINT32_C(0x0054C9AE), UINT32_C(0x002F21DC),
          UINT32_C(0x0059D2B0), UINT32_C(0x0033CED8), UINT32_C(0x004A2E1F),
          UINT32_C(0x0019B7CF), UINT32_C(0x0095A290), UINT32_C(0x0070C5C6),
          UINT32_C(0x002EB87D), UINT32_C(0x007DB934)},
         {UINT32_C(0x0078836C), UINT32_C(0x003DF99A), UINT32_C(0x005C291E),
          UINT32_C(0x00534356), UINT32_C(0x00247324), UINT32_C(0x002F5070),
          UINT32_C(0x003EEB8F), UINT32_C(0x003A920E), UINT32_C(0x0002D28C),
          UINT32_C(0x000FF5FB), UINT32_C(0x007336AA)},
         {UINT32_C(0x001FA1A5), UINT32_C(0x00212F13), UINT32_C(0x00577134),
          UINT32_C(0x004BA237), UINT32_C(0x0049A436), UINT32_C(0x007F6BB2),
          UINT32_C(0x00722AC5), UINT32_C(0x00C0532E), UINT32_C(0x007534D3),
          UINT32_C(0x001DCBE4), UINT32_C(0x00726554)}},
    },
    {
        {{UINT32_C(0x009D7F04), UINT32_C(0x003E522B), UINT32_C(0x004E8173),
          UINT32_C(0x001C1588), UINT32_C(0x0022E52F), UINT32_C(0x007A9393),
          UINT32_C(0x006F3F0A), UINT32_C(0x00537EF6), UINT32_C(0x0061C5EA),
          UINT32_C(0x001044A3), UINT32_C(0x005E8C14)},
         {UINT32_C(0x00CC8AA4), UINT32_C(0x000C72ED), UINT32_C(0x0000DF49),
          UINT32_C(0x00798195), UINT32_C(0x000C41B1), UINT32_C(0x005C0709),
          UINT32_C(0x0063F579), UINT32_C(0x00797385), UINT32_C(0x00561750),
          UINT32_C(0x0022408A), UINT32_C(0x0033DFF7)},
         {UINT32_C(0x00E72D7C), UINT32_C(0x0052E6A6), UINT32_C(0x00677E30),
          UINT32_C(0x00DA67FB), UINT32_C(0x0053230A), UINT32_C(0x007B8901),
          UINT32_C(0x0045AC83), UINT32_C(0x0010FEFC), UINT32_C(0x006E69B3),
          UINT32_C(0x006008DB), UINT32_C(0x006D7911)}},
        {{UINT32_C(0x004F7A12), UINT32_C(0x003FE9B9), UINT32_C(0x0035897D),
          UINT32_C(0x0092A16A), UINT32_C(0x002ABB20), UINT32_C(0x003C1F4F),
          UINT32_C(0x00170A77), UINT32_C(0x0014D957), UINT32_C(0x003289DC),
          UINT32_C(0x0005BECE), UINT32_C(0x00601138)},
         {UINT32_C(0x0061EEA8), UINT32_C(0x00211689), UINT32_C(0x002EAEF4),
          UINT32_C(0x002EE6E9), UINT32_C(0x0042698F), UINT32_C(0x002C17BC),
          UINT32_C(0x00153303), UINT32_C(0x000EA9B7), UINT32_C(0x00335B95),
          UINT32_C(0x00101F4E), UINT32_C(0x00661849)},
         {UINT32_C(0x00AEA3D6), UINT32_C(0x00406869), UINT32_C(0x00375EE9),
          UINT32_C(0x00D5E095), UINT32_C(0x00510487), UINT32_C(0x00425B84),
          UINT32_C(0x002933E8), UINT32_C(0x0085EA0B), UINT32_C(0x0036858A),
          UINT32_C(0x0015643E), UINT32_C(0x00291032)}},
        {{UINT32_C(0x00B19259), UINT32_C(0x0044D63D), UINT32_C(0x000D8B2C),
          UINT32_C(0x00F03F2A), UINT32_C(0x0075DD51), UINT32_C(0x0063574F),
          UINT32_C(0x00597D0C), UINT32_C(0x004CC301), UINT32_C(0x0008F143),
          UINT32_C(0x001955D2), UINT32_C(0x000EFE67)},
         {UINT32_C(0x0012F590), UINT32_C(0x0008204C), UINT32_C(0x007F0B91),
          UINT32_C(0x00397CAF), UINT32_C(0x00617872), UINT32_C(0x002BCC89),
          UINT32_C(0x00560BB9), UINT32_C(0x00755598), UINT32_C(0x006EECB1),
          UINT32_C(0x004BA0C7), UINT32_C(0x001A34AB)},
         {UINT32_C(0x008528CC), UINT32_C(0x00580BBC), UINT32_C(0x005B25D1),
          UINT32_C(0x007506DD), UINT32_C(0x007DC690), UINT32_C(0x007F96D8),
          UINT32_C(0x00392289), UINT32_C(0x00AEF1AE), UINT32_C(0x0063168D),
          UINT32_C(0x0072BE2C), UINT32_C(0x005157C5)}},
        {{UINT32_C(0x003A9FD2), UINT32_C(0x005B25BE), UINT32_C(0x004F27F7),
          UINT32_C(0x00BB4A51), UINT32_C(0x000662C0), UINT32_C(0x0041BBE0),
          UINT32_C(0x00460962), UINT32_C(0x002EF651), UINT32_C(0x0017689F),
          UINT32_C(0x00071079), UINT32_C(0x0001A5DF)},
         {UINT32_C(0x00F75DF0), UINT32_C(0x0049FC9F), UINT32_C(0x006A8F37),
          UINT32_C(0x00C7015B), UINT32_C(0x001EE02F), UINT32_C(0x003A578B),
          UINT32_C(0x004F5B70), UINT32_C(0x0046D34E), UINT32_C(0x0036C503),
          UINT32_C(0x004A47A8), UINT32_C(0x001555FC)},
         {UINT32_C(0x000C1A1C), UINT32_C(0x0018A095), UINT32_C(0x004C6DD3),
          UINT32_C(0x00622629), UINT32_C(0x001A4B4D), UINT32_C(0x001EC488),
          UINT32_C(0x007C4E62), UINT32_C(0x00F0CBDB), UINT32_C(0x0053F494),
          UINT32_C(0x0025E122), UINT32_C(0x006FE5D9)}},
        {{UINT32_C(0x001D0B59), UINT32_C(0x002BC2EB), UINT32_C(0x006C247D),
          UINT32_C(0x0017164C), UINT32_C(0x0016C9E8), UINT32_C(0x003FE112),
          UINT32_C(0x00523C8C), UINT32_C(0x00500E55), UINT32_C(0x001EBD4A),
          UINT32_C(0x002663A4), UINT32_C(0x002FF15B)},
         {UINT32_C(0x009AECD9), UINT32_C(0x00079901), UINT32_C(0x006A6241),
          UINT32_C(0x003D62B1), UINT32_C(0x006182F6), UINT32_C(0x001F7C9C),
          UINT32_C(0x0030C6A5), UINT32_C(0x00C1D9FD), UINT32_C(0x000AF3AE),
          UINT32_C(0x007306C0), UINT32_C(0x007180E7)},
         {UINT32_C(0x0072CCE1), UINT32_C(0x002EF926), UINT32_C(0x0033946B),
          UINT32_C(0x00DD4125), UINT32_C(0x00466F98), UINT32_C(0x005626FE),
          UINT32_C(0x004D69F2), UINT32_C(0x00423C76), UINT32_C(0x0065D10D),
          UINT32_C(0x002D6287), UINT32_C(0x002DD36E)}},
        {{UINT32_C(0x0060C004), UINT32_C(0x002DF8E2), UINT32_C(0x0073334C),
          UINT32_C(0x008C39C6), UINT32_C(0x00509B37), UINT32_C(0x007E8198),
          UINT32_C(0x000F62E1), UINT32_C(0x00DD92B1), UINT32_C(0x001998EC),
          UINT32_C(0x002CAD76), UINT32_C(0x00202123)},
         {UINT32_C(0x00131346), UINT32_C(0x007A9FF0), UINT32_C(0x00587E28),
          UINT32_C(0x005434EA), UINT32_C(0x000C2CE8), UINT32_C(0x004B1B22),
          UINT32_C(0x0045D4A2), UINT32_C(0x009F60D4), UINT32_C(0x0057E546),
          UINT32_C(0x0064D05F), UINT32_C(0x006EEC72)},
         {UINT32_C(0x0021F448), UINT32_C(0x0010845A), UINT32_C(0x0006B85E),
          UINT32_C(0x00AB9D0A), UINT32_C(0x0042FE06), UINT32_C(0x0033EE0E),
          UINT32_C(0x006AD2F2), UINT32_C(0x005C9579), UINT32_C(0x0067DFC7),
          UINT32_C(0x003C1F3B), UINT32_C(0x00699BE9)}},
        {{UINT32_C(0x00A66972), UINT32_C(0x005C9C52), UINT32_C(0x0033F743),
          UINT32_C(0x002E4131), UINT32_C(0x0018DAB0), UINT32_C(0x00451021),
          UINT32_C(0x004A7644), UINT32_C(0x00280CE7), UINT32_C(0x0054A55B),
          UINT32_C(0x0000D618), UINT32_C(0x0006EA24)},
         {UINT32_C(0x00836291), UINT32_C(0x005C23F3), UINT32_C(0x003EF835),
          UINT32_C(0x006B6300), UINT32_C(0x00459EBC), UINT32_C(0x007C3224),
          UINT32_C(0x0044102E), UINT32_C(0x00E2D759), UINT32_C(0x0058BA8F),
          UINT32_C(0x00203AA3), UINT32_C(0x006F64DB)},
         {UINT32_C(0x00A80AE1), UINT32_C(0x0002F426), UINT32_C(0x005F82DF),
          UINT32_C(0x00129B98), UINT32_C(0x0016684F), UINT32_C(0x0037C9F8),
          UINT32_C(0x00798FDE), UINT32_C(0x005C8F79), UINT32_C(0x0007C718),
          UINT32_C(0x0052B29A), UINT32_C(0x00059CBE)}},
        {{UINT32_C(0x00126A51), UINT32_C(0x002CB978), UINT32_C(0x00423FCC),
          UINT32_C(0x00B9CC4C), UINT32_C(0x002AA538), UINT32_C(0x00395FBB),
          UINT32_C(0x00327373), UINT32_C(0x00C23CA5), UINT32_C(0x00544D71),
          UINT32_C(0x00455CE2), UINT32_C(0x0077726F)},
         {UINT32_C(0x00E72C58), UINT32_C(0x0056C6C9), UINT32_C(0x003D49ED),
          UINT32_C(0x00C8F1D2), UINT32_C(0x004A8FA5), UINT32_C(0x006F3C45),
          UINT32_C(0x0069AB18), UINT32_C(0x00D5CE81), UINT32_C(0x000A718F),
          UINT32_C(0x004420F7), UINT32_C(0x002B7F53)},
         {UINT32_C(0x00809D3C), UINT32_C(0x005957A2), UINT32_C(0x006A3FB7),
          UINT32_C(0x00FC7710), UINT32_C(0x002F2CC5), UINT32_C(0x0068D0E3),
          UINT32_C(0x0017780D), UINT32_C(0x00B2B1F6), UINT32_C(0x001C4FDE),
          UINT32_C(0x0072D97A), UINT32_C(0x003E98AB)}},
        {{UINT32_C(0x00276A10), UINT32_C(0x0064190E), UINT32_C(0x000243B5),
          UINT32_C(0x00EE59AE), UINT32_C(0x0016FC7F), UINT32_C(0x0036935E),
          UINT32_C(0x0058A7F7), UINT32_C(0x006AAA88), UINT32_C(0x0036ED90),
          UINT32_C(0x00779372), UINT32_C(0x0030DC06)},
         {UINT32_C(0x00208159), UINT32_C(0x00170255), UINT32_C(0x00695DF5),
          UINT32_C(0x00411E22), UINT32_C(0x00477C68), UINT32_C(0x0032102F),
          UINT32_C(0x001B026A), UINT32_C(0x0038CA4D), UINT32_C(0x00235EC5),
          UINT32_C(0x0078C8B9), UINT32_C(0x00651C9F)},
         {UINT32_C(0x0067D2A4), UINT32_C(0x00446632), UINT32_C(0x003D1074),
          UINT32_C(0x00032AC4), UINT32_C(0x0061127F), UINT32_C(0x0004B798),
          UINT32_C(0x00003C2D), UINT32_C(0x00F00C4B), UINT32_C(0x00603764),
          UINT32_C(0x0074558D), UINT32_C(0x00375BF7)}},
        {{UINT32_C(0x00B4CE41), UINT32_C(0x007FBBC3), UINT32_C(0x00750477),
          UINT32_C(0x00DA8800), UINT32_C(0x000B20CD), UINT32_C(0x002FDE44),
          UINT32_C(0x0001448F), UINT32_C(0x001B1EE5), UINT32_C(0x003B0FDC),
          UINT32_C(0x00600D6A), UINT32_C(0x0042807B)},
         {UINT32_C(0x005C7646), UINT32_C(0x004F6D9A), UINT32_C(0x0047B855),
          UINT32_C(0x003D2CEB), UINT32_C(0x007B2B58), UINT32_C(0x0032B9DA),
          UINT32_C(0x00118C1E), UINT32_C(0x006DA898), UINT32_C(0x00432BB2),
          UINT32_C(0x0014A742), UINT32_C(0x00214F42)},
         {UINT32_C(0x0063536B), UINT32_C(0x0073EDC7), UINT32_C(0x00608DC1),
          UINT32_C(0x00757BDA), UINT32_C(0x0073293A), UINT32_C(0x00510B0D),
          UINT32_C(0x001C8A86), UINT32_C(0x00406F0A), UINT32_C(0x0030DAA7),
          UINT32_C(0x000F6F80), UINT32_C(0x0007A1D7)}},
        {{UINT32_C(0x00A8E90B), UINT32_C(0x007407DB), UINT32_C(0x00440BF0),
          UINT32_C(0x007198C8), UINT32_C(0x001A09F8), UINT32_C(0x000916B3),
          UINT32_C(0x0061A7D6), UINT32_C(0x00A714BE), UINT32_C(0x0039589D),
          UINT32_C(0x0011D76D), UINT32_C(0x00323B2D)},
         {UINT32_C(0x00E19A5C), UINT32_C(0x00141B92), UINT32_C(0x00409B73),
          UINT32_C(0x006F090B), UINT32_C(0x007E0865), UINT32_C(0x004B7DA7),
          UINT32_C(0x006EBE5E), UINT32_C(0x00CE6360), UINT32_C(0x003FE520),
          UINT32_C(0x005C6EFA), UINT32_C(0x002904A1)},
         {UINT32_C(0x007BF307), UINT32_C(0x00095790), UINT32_C(0x0009FCE8),
          UINT32_C(0x001A0547), UINT32_C(0x001C1E57), UINT32_C(0x007C07EE),
          UINT32_C(0x00405EE9), UINT32_C(0x004047E0), UINT32_C(0x006DE98E),
          UINT32_C(0x004E2A25), UINT32_C(0x0066F1F0)}},
        {{UINT32_C(0x00C266C9), UINT32_C(0x0078A6A6), UINT32_C(0x005AE507),
          UINT32_C(0x00029D74), UINT32_C(0x00025040), UINT32_C(0x004030CE),
          UINT32_C(0x006D0A48), UINT32_C(0x0076B699), UINT32_C(0x001E465B),
          UINT32_C(0x000082E1), UINT32_C(0x0069B738)},
         {UINT32_C(0x00BD1302), UINT32_C(0x003CA015), UINT32_C(0x00107897),
          UINT32_C(0x00FA47BE), UINT32_C(0x0049E714), UINT32_C(0x000B1555),
          UINT32_C(0x0053443D), UINT32_C(0x00EE9331), UINT32_C(0x00514330),
          UINT32_C(0x0039A875), UINT32_C(0x00050473)},
         {UINT32_C(0x00B3D5E2), UINT32_C(0x002E59A5), UINT32_C(0x00444307),
          UINT32_C(0x001495AA), UINT32_C(0x007E4455), UINT32_C(0x001E0A82),
          UINT32_C(0x002AB250), UINT32_C(0x003E1A5A), UINT32_C(0x000DB2A8),
          UINT32_C(0x0010A30C), UINT32_C(0x0032799B)}},
        {{UINT32_C(0x002DFB8B), UINT32_C(0x0056DBFB), UINT32_C(0x0067D9E7),
          UINT32_C(0x007E49DF), UINT32_C(0x000D0A8B), UINT32_C(0x001D2692),
          UINT32_C(0x006D4621), UINT32_C(0x00D98598), UINT32_C(0x0034D873),
          UINT32_C(0x0021CBB4), UINT32_C(0x0075EBF4)},
         {UINT32_C(0x00757E5C), UINT32_C(0x001F41CE), UINT32_C(0x002E6367),
          UINT32_C(0x00180064), UINT32_C(0x004FEBFE), UINT32_C(0x0004F8E5),
          UINT32_C(0x001FB594), UINT32_C(0x007471A5), UINT32_C(0x0060AE8E),
          UINT32_C(0x007B789E), UINT32_C(0x0072FBC1)},
         {UINT32_C(0x00028861), UINT32_C(0x0076CF2E), UINT32_C(0x00757BC9),
          UINT32_C(0x00E1A580), UINT32_C(0x00355683), UINT32_C(0x002A8FE7),
          UINT32_C(0x00542152), UINT32_C(0x00561514), UINT32_C(0x00725460),
          UINT32_C(0x007285A9), UINT32_C(0x004B1809)}},
        {{UINT32_C(0x00355837), UINT32_C(0x002DDA4C), UINT32_C(0x003DD1AC),
          UINT32_C(0x005CC538), UINT32_C(0x00647CBC), UINT32_C(0x0072DB88),
          UINT32_C(0x007F7C75), UINT32_C(0x002AE840), UINT32_C(0x00062A1A),
          UINT32_C(0x00518336), UINT32_C(0x00791C9D)},
         {UINT32_C(0x0007CA0E), UINT32_C(0x002EA83D), UINT32_C(0x00794D8F),
          UINT32_C(0x0074F12E), UINT32_C(0x00573D80), UINT32_C(0x00334BAD),
          UINT32_C(0x007061C7), UINT32_C(0x00EC3EB0), UINT32_C(0x004AFF19),
          UINT32_C(0x007B77C6), UINT32_C(0x002C2CD6)},
         {UINT32_C(0x00332C81), UINT32_C(0x005AEC4B), UINT32_C(0x002D4025),
          UINT32_C(0x000003C9), UINT32_C(0x00329780), UINT32_C(0x0045F540),
          UINT32_C(0x004B3238), UINT32_C(0x00A2B784), UINT32_C(0x001953EA),
          UINT32_C(0x00256030), UINT32_C(0x00398A20)}},
        {{UINT32_C(0x000EC4A0), UINT32_C(0x00641937), UINT32_C(0x006B364E),
          UINT32_C(0x00DA64E5), UINT32_C(0x001DC048), UINT32_C(0x002583F4),
          UINT32_C(0x0026D1D9), UINT32_C(0x00EDA8AF), UINT32_C(0x0047D78B),
          UINT32_C(0x002EEFE4), UINT32_C(0x002CC316)},
         {UINT32_C(0x0081CBB1), UINT32_C(0x006E1C62), UINT32_C(0x002FACAB),
          UINT32_C(0x00A6588F), UINT32_C(0x00250981), UINT32_C(0x0061708F),
          UINT32_C(0x0074479E), UINT32_C(0x003F3B34), UINT32_C(0x00140C2C),
          UINT32_C(0x002EBBC2), UINT32_C(0x00227AAF)},
         {UINT32_C(0x00DB9FC3), UINT32_C(0x0053114A), UINT32_C(0x0052ECA2),
          UINT32_C(0x003DC40A), UINT32_C(0x007DC659), UINT32_C(0x000D3562),
          UINT32_C(0x002C373C), UINT32_C(0x00FAF2D6), UINT32_C(0x002B7888),
          UINT32_C(0x0056EFFE), UINT32_C(0x00162CF7)}},
        {{UINT32_C(0x00A14EAC), UINT32_C(0x005D7C37), UINT32_C(0x002C818A),
          UINT32_C(0x008FB8CD), UINT32_C(0x0062E148), UINT32_C(0x004C3163),
          UINT32_C(0x0053D48D), UINT32_C(0x005823EA), UINT32_C(0x002D1D27),
          UINT32_C(0x00057B1E), UINT32_C(0x002C3DDA)},
         {UINT32_C(0x0053A948), UINT32_C(0x004C6EF1), UINT32_C(0x002D74B8),
          UINT32_C(0x001219E4), UINT32_C(0x0071C013), UINT32_C(0x00262046),
          UINT32_C(0x00421ADF), UINT32_C(0x00BFB901), UINT32_C(0x001E79C9),
          UINT32_C(0x0035B45B), UINT32_C(0x0041AC61)},
         {UINT32_C(0x002C184D), UINT32_C(0x0047D074), UINT32_C(0x0034003A),
          UINT32_C(0x005DCCFB), UINT32_C(0x00524D8F), UINT32_C(0x0059FB03),
          UINT32_C(0x0034304B), UINT32_C(0x00EACD9B), UINT32_C(0x002761FA),
          UINT32_C(0x003C9C64), UINT32_C(0x004B14DD)}},
    }};

/*-
 * Q := 2P, both projective, Q and P same pointers OK
 * Autogenerated: op3/dbl_proj_ed_eone.op3
 * https://www.hyperelliptic.org/EFD/g1p/auto-code/twisted/extended-1/doubling/dbl-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_double(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    /* set pointers for Edwards curve arith */
    const limb_t *X = P->X;
    const limb_t *Y = P->Y;
    const limb_t *Z = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t0, X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t1, Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(t2, Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, t2, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X, Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(Y3, X3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, Y3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, t0, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t2, Y3, t3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t3, t0, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, Z3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, Z3, t3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, t2, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y3, t3);
}

/*-
 * R := Q + P where R and Q are projective, P affine.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_mixed_ed_eone.op3
 * https://hyperelliptic.org/EFD/g1p/auto-code/twisted/extended/addition/madd-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_add_mixed(pt_prj_t *R, const pt_prj_t *Q, const pt_aff_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    const limb_t *d = const_d;
    /* set pointers for Edwards curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *T1 = Q->T;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *T2 = P->T;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *T3 = R->T;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, X1, X2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t1, Y1, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, d, T2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, T1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, X2, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t3, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, Z1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, Z1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t1, t1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Z3, t3);
}

/*-
 * R := Q + P all projective.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_proj_ed_eone.op3
 * https://hyperelliptic.org/EFD/g1p/auto-code/twisted/extended/addition/add-2008-hwcd.op3
 * ASSERT: e = 1
 */
static void point_add_proj(pt_prj_t *R, const pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3;
    /* constants */
    const limb_t *d = const_d;
    /* set pointers for Edwards curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *T1 = Q->T;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *T2 = P->T;
    const limb_t *Z2 = P->Z;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *T3 = R->T;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, X1, X2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t1, Y1, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, d, T2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t2, T1, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t3, Z1, Z2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(X3, X1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, X2, Y2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, Z3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(Z3, t3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t3, t3, t2);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t1, t1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, t3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, T3, t1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Z3, t3);
}

/*-
 * from P projective Edwards to Q projective legacy: Q=P OK
 * Autogenerated: op3/edwards2legacy_gost.op3
 * https://tools.ietf.org/html/rfc7836#section-5.2
 */
static void point_edwards2legacy(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0;
    /* constants */
    const limb_t *S = const_S;
    const limb_t *T = const_T;
    const limb_t *X1 = P->X;
    const limb_t *Y1 = P->Y;
    const limb_t *Z1 = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(T3, Z1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(t0, Z1, Y1);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, S, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Z1, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, X1, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(t0, t0, T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(t0, T3, t0);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, X1, t0);
}

/*-
 * from P affine legacy to Q projective Edwards: Q=P not OK
 * Autogenerated: op3/legacy2edwards_gost.op3
 * https://tools.ietf.org/html/rfc7836#section-5.2
 */
static void point_legacy2edwards(pt_prj_t *Q, const pt_aff_t *P) {
    /* constants */
    const limb_t *S = const_S;
    const limb_t *T = const_T;
    const limb_t *X1 = P->X;
    const limb_t *Y1 = P->Y;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *T3 = Q->T;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, X1, T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_add(Y3, T3, S);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, T3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Z3, Y1, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_sub(T3, T3, S);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y1, T3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(T3, X3, Y3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(X3, X3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(Y3, Y3, Z3);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_square(Z3, Z3);
}

/* constants */
#define RADIX 5
#define DRADIX (1 << RADIX)
#define DRADIX_WNAF ((DRADIX) << 1)

/*-
 * precomp for wnaf scalar multiplication:
 * precomp[0] = 1P
 * precomp[1] = 3P
 * precomp[2] = 5P
 * precomp[3] = 7P
 * precomp[4] = 9P
 * ...
 */
static void precomp_wnaf(pt_prj_t precomp[DRADIX / 2], const pt_aff_t *P) {
    int i;

    /* move from legacy affine to Edwards projective */
    point_legacy2edwards(&precomp[0], P);
    point_double(&precomp[DRADIX / 2 - 1], &precomp[0]);

    for (i = 1; i < DRADIX / 2; i++)
        point_add_proj(&precomp[i], &precomp[DRADIX / 2 - 1], &precomp[i - 1]);
}

/* fetch a scalar bit */
static int scalar_get_bit(const unsigned char in[32], int idx) {
    int widx, rshift;

    widx = idx >> 3;
    rshift = idx & 0x7;

    if (idx < 0 || widx >= 32) return 0;

    return (in[widx] >> rshift) & 0x1;
}

/*-
 * Compute "regular" wnaf representation of a scalar.
 * See "Exponent Recoding and Regular Exponentiation Algorithms",
 * Tunstall et al., AfricaCrypt 2009, Alg 6.
 * It forces an odd scalar and outputs digits in
 * {\pm 1, \pm 3, \pm 5, \pm 7, \pm 9, ...}
 * i.e. signed odd digits with _no zeroes_ -- that makes it "regular".
 */
static void scalar_rwnaf(int8_t out[52], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = (in[0] & (DRADIX_WNAF - 1)) | 1;
    for (i = 0; i < 51; i++) {
        d = (window & (DRADIX_WNAF - 1)) - DRADIX;
        out[i] = d;
        window = (window - d) >> RADIX;
        window += scalar_get_bit(in, (i + 1) * RADIX + 1) << 1;
        window += scalar_get_bit(in, (i + 1) * RADIX + 2) << 2;
        window += scalar_get_bit(in, (i + 1) * RADIX + 3) << 3;
        window += scalar_get_bit(in, (i + 1) * RADIX + 4) << 4;
        window += scalar_get_bit(in, (i + 1) * RADIX + 5) << 5;
    }
    out[i] = window;
}

/*-
 * Compute "textbook" wnaf representation of a scalar.
 * NB: not constant time
 */
static void scalar_wnaf(int8_t out[257], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = in[0] & (DRADIX_WNAF - 1);
    for (i = 0; i < 257; i++) {
        d = 0;
        if ((window & 1) && ((d = window & (DRADIX_WNAF - 1)) & DRADIX))
            d -= DRADIX_WNAF;
        out[i] = d;
        window = (window - d) >> 1;
        window += scalar_get_bit(in, i + 1 + RADIX) << RADIX;
    }
}

/*-
 * Simultaneous scalar multiplication: interleaved "textbook" wnaf.
 * NB: not constant time
 */
static void var_smul_wnaf_two(pt_aff_t *out, const unsigned char a[32],
                              const unsigned char b[32], const pt_aff_t *P) {
    int i, d, is_neg, is_inf = 1, flipped = 0;
    int8_t anaf[257] = {0};
    int8_t bnaf[257] = {0};
    pt_prj_t Q = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_wnaf(anaf, a);
    scalar_wnaf(bnaf, b);

    for (i = 256; i >= 0; i--) {
        if (!is_inf) point_double(&Q, &Q);
        if ((d = bnaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &precomp[d].X);
                fe_copy(Q.Y, &precomp[d].Y);
                fe_copy(Q.T, &precomp[d].T);
                fe_copy(Q.Z, &precomp[d].Z);
                is_inf = 0;
            } else
                point_add_proj(&Q, &Q, &precomp[d]);
        }
        if ((d = anaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &lut_cmb[0][d].X);
                fe_copy(Q.Y, &lut_cmb[0][d].Y);
                fe_copy(Q.T, &lut_cmb[0][d].T);
                fe_copy(Q.Z, const_one);
                is_inf = 0;
            } else
                point_add_mixed(&Q, &Q, &lut_cmb[0][d]);
        }
    }

    if (is_inf) {
        /* initialize accumulator to inf: all-zero scalars */
        fe_set_zero(Q.X);
        fe_copy(Q.Y, const_one);
        fe_set_zero(Q.T);
        fe_copy(Q.Z, const_one);
    }

    if (flipped) {
        /* correct sign */
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.X, Q.X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(Q.T, Q.T);
    }

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Variable point scalar multiplication with "regular" wnaf.
 */
static void var_smul_rwnaf(pt_aff_t *out, const unsigned char scalar[32],
                           const pt_aff_t *P) {
    int i, j, d, diff, is_neg;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, lut = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_rwnaf(rnaf, scalar);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    /* initialize accumulator to high digit */
    d = (rnaf[51] - 1) >> 1;
    for (j = 0; j < DRADIX / 2; j++) {
        diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, diff, Q.X,
                                                            precomp[j].X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, diff, Q.Y,
                                                            precomp[j].Y);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, diff, Q.T,
                                                            precomp[j].T);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, diff, Q.Z,
                                                            precomp[j].Z);
    }

    for (i = 50; i >= 0; i--) {
        for (j = 0; j < RADIX; j++) point_double(&Q, &Q);
        d = rnaf[i];
        /* is_neg = (d < 0) ? 1 : 0 */
        is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
        /* d = abs(d) */
        d = (d ^ -is_neg) + is_neg;
        d = (d - 1) >> 1;
        for (j = 0; j < DRADIX / 2; j++) {
            diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.X, diff, lut.X, precomp[j].X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.Y, diff, lut.Y, precomp[j].Y);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.T, diff, lut.T, precomp[j].T);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                lut.Z, diff, lut.Z, precomp[j].Z);
        }
        /* negate lut point if digit is negative */
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->X, lut.X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->T, lut.T);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.X, is_neg,
                                                            lut.X, out->X);
        fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.T, is_neg,
                                                            lut.T, out->T);
        point_add_proj(&Q, &Q, &lut);
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.X, precomp[0].X);
    fe_copy(lut.Y, precomp[0].Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.T, precomp[0].T);
    fe_copy(lut.Z, precomp[0].Z);
    point_add_proj(&lut, &lut, &Q);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, scalar[0] & 1,
                                                        lut.X, Q.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, scalar[0] & 1,
                                                        lut.Y, Q.Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, scalar[0] & 1,
                                                        lut.T, Q.T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, scalar[0] & 1,
                                                        lut.Z, Q.Z);

    point_double(&Q, &Q);
    point_double(&Q, &Q);

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Fixed scalar multiplication: comb with interleaving.
 */
static void fixed_smul_cmb(pt_aff_t *out, const unsigned char scalar[32]) {
    int i, j, k, d, diff, is_neg = 0;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, R = {0};
    pt_aff_t lut = {0};

    scalar_rwnaf(rnaf, scalar);

    /* initalize accumulator to inf */
    fe_set_zero(Q.X);
    fe_copy(Q.Y, const_one);
    fe_set_zero(Q.T);
    fe_copy(Q.Z, const_one);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    for (i = 3; i >= 0; i--) {
        for (j = 0; i != 3 && j < RADIX; j++) point_double(&Q, &Q);
        for (j = 0; j < 14; j++) {
            if (j * 4 + i > 51) continue;
            d = rnaf[j * 4 + i];
            /* is_neg = (d < 0) ? 1 : 0 */
            is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
            /* d = abs(d) */
            d = (d ^ -is_neg) + is_neg;
            d = (d - 1) >> 1;
            for (k = 0; k < DRADIX / 2; k++) {
                diff = (1 - (-(d ^ k) >> (8 * sizeof(int) - 1))) & 1;
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.X, diff, lut.X, lut_cmb[j][k].X);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.Y, diff, lut.Y, lut_cmb[j][k].Y);
                fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(
                    lut.T, diff, lut.T, lut_cmb[j][k].T);
            }
            /* negate lut point if digit is negative */
            fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->X, lut.X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(out->T, lut.T);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.X, is_neg,
                                                                lut.X, out->X);
            fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(lut.T, is_neg,
                                                                lut.T, out->T);
            point_add_mixed(&Q, &Q, &lut);
        }
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.X, lut_cmb[0][0].X);
    fe_copy(lut.Y, lut_cmb[0][0].Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_opp(lut.T, lut_cmb[0][0].T);
    point_add_mixed(&R, &Q, &lut);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.X, scalar[0] & 1, R.X,
                                                        Q.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Y, scalar[0] & 1, R.Y,
                                                        Q.Y);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.T, scalar[0] & 1, R.T,
                                                        Q.T);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_selectznz(Q.Z, scalar[0] & 1, R.Z,
                                                        Q.Z);

    /* move from Edwards projective to legacy projective */
    point_edwards2legacy(&Q, &Q);
    /* convert to affine -- NB depends on coordinate system */
    fiat_id_tc26_gost_3410_2012_256_paramSetA_inv(Q.Z, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Wrapper: simultaneous scalar mutiplication.
 * outx, outy := a * G + b * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul_two(unsigned char outx[32], unsigned char outy[32],
                          const unsigned char a[32], const unsigned char b[32],
                          const unsigned char inx[32],
                          const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.X, inx);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.Y, iny);
    /* simultaneous scalar multiplication */
    var_smul_wnaf_two(&P, a, b, &P);

    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: fixed scalar mutiplication.
 * outx, outy := scalar * G
 * Everything is LE byte ordering.
 */
static void point_mul_g(unsigned char outx[32], unsigned char outy[32],
                        const unsigned char scalar[32]) {
    pt_aff_t P;

    /* fixed scmul function */
    fixed_smul_cmb(&P, scalar);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: variable point scalar mutiplication.
 * outx, outy := scalar * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul(unsigned char outx[32], unsigned char outy[32],
                      const unsigned char scalar[32],
                      const unsigned char inx[32],
                      const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.X, inx);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_from_bytes(P.Y, iny);
    /* var scmul function */
    var_smul_rwnaf(&P, scalar, &P);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outx, P.X);
    fiat_id_tc26_gost_3410_2012_256_paramSetA_to_bytes(outy, P.Y);
}


#include <openssl/ec.h>

/* the zero field element */
static const unsigned char const_zb[32] = {0};

/*-
 * An OpenSSL wrapper for simultaneous scalar multiplication.
 * r := n * G + m * q
 */
    int
    point_mul_two_id_tc26_gost_3410_2012_256_paramSetA(
        const EC_GROUP *group, EC_POINT *r, const BIGNUM *n, const EC_POINT *q,
        const BIGNUM *m, BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(n, b_n, 32) != 32 || BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the simultaneous scalar multiplication */
    point_mul_two(b_x, b_y, b_n, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for variable point scalar multiplication.
 * r := m * q
 */
    int
    point_mul_id_tc26_gost_3410_2012_256_paramSetA(const EC_GROUP *group,
                                                   EC_POINT *r,
                                                   const EC_POINT *q,
                                                   const BIGNUM *m,
                                                   BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the variable scalar multiplication */
    point_mul(b_x, b_y, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for fixed scalar multiplication.
 * r := n * G
 */
    int
    point_mul_g_id_tc26_gost_3410_2012_256_paramSetA(const EC_GROUP *group,
                                                     EC_POINT *r,
                                                     const BIGNUM *n,
                                                     BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL || BN_bn2lebinpad(n, b_n, 32) != 32)
        goto err;
    /* do the fixed scalar multiplication */
    point_mul_g(b_x, b_y, b_n);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}



#endif /* __SIZEOF_INT128__ */
