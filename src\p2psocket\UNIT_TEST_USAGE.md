# P2P Socket 基础接口单元测试使用说明

## 概述

`testp2psocket.cpp` 是专门为 p2psocket.h 基础接口设计的单元测试，专注测试以下核心 API：

### 测试的接口
```c
// 基础 Socket 操作
PLUGIN_API P2P_SOCKET P2pCreate(SocketOptions* option);
PLUGIN_API int P2pClose(P2P_SOCKET soc);

// 网络操作
PLUGIN_API int P2pBind(P2P_SOCKET soc, const char* ipaddr, int port);
PLUGIN_API int P2pListen(P2P_SOCKET soc);
PLUGIN_API P2P_SOCKET P2pAccept(P2P_SOCKET soc, char* ipaddr, int ipaddr_len, int* port);
PLUGIN_API int P2pConnect(P2P_SOCKET soc, const char* ipaddr, int port);

// 数据传输
PLUGIN_API int P2pWrite(P2P_SOCKET soc, const char* buffer, int len);
PLUGIN_API int P2pRead(P2P_SOCKET soc, char* buffer, int len);
PLUGIN_API int P2pWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count);

// 配置和查询
PLUGIN_API int P2pSetConnTimeout(P2P_SOCKET soc, int timeout);
PLUGIN_API int P2pSetSendMode(P2P_SOCKET soc, int directMode);
PLUGIN_API int P2pSetReadMode(P2P_SOCKET soc, int directMode);
PLUGIN_API int P2pGetLocalPort(P2P_SOCKET soc);
PLUGIN_API int P2pPoll(P2P_SOCKET soc, PollEvent* events, int timeout);
```

## 测试特点

### 真实连接测试
- 创建真实的服务器-客户端连接
- 使用 echo 服务器验证数据传输
- 测试多线程环境下的连接处理

### 全面的错误处理
- 测试无效参数处理
- 测试边界条件
- 测试不同 socket 类型

### 不包含 Stream API
- 专注于基础 socket 接口
- 不测试 P2pStreamXXX 相关函数

## 编译和运行

### 使用项目构建系统
```bash
# 使用 build_p2psocket.bat 构建
cd /path/to/QuicChannel
build_p2psocket.bat

# 运行测试
cd bin
testp2psocket.exe
```

### 使用脚本运行
```bash
# Windows
cd src/p2psocket
run_unit_tests.bat

# Linux/Unix
cd src/p2psocket
./run_unit_tests.sh
```

### 手动运行
```bash
# 默认设置（QUIC）
./testp2psocket

# 指定 socket 类型
./testp2psocket -type 3  # QUIC
./testp2psocket -type 2  # SSL
./testp2psocket -type 1  # MULTITCP

# 自定义配置
./testp2psocket -type 3 -port 5000 -ip ************* -workers 8
```

## 测试流程

### 1. 基础操作测试
- Socket 创建和销毁
- 无效参数处理
- Socket 绑定和监听
- 配置设置

### 2. 连接测试
- 启动服务器线程
- 客户端连接到服务器
- 数据传输验证
- 连接清理

### 3. 高级测试
- 向量化 I/O 操作
- 多种 socket 类型
- 边界条件测试

## 预期结果

成功运行时应该看到：
```
[TEST] Starting P2P Socket Interface Unit Tests (Basic API Only)
[TEST] Configuration:
[TEST]   Socket type: 3
[TEST]   Test port: 4433
[TEST]   Test IP: 127.0.0.1
[TEST]   Workers: 4

[INFO] PASS: Certificate creation
[INFO] PASS: Socket creation
[TEST] Server thread starting...
[TEST] Server listening on port 4443
[TEST] Client connected successfully
[TEST] Data echo test successful
...
[TEST] All tests passed! ✓
```

## 故障排除

### 常见问题
1. **端口冲突**: 测试使用不同端口避免冲突
2. **证书问题**: 自动生成测试证书
3. **连接超时**: 可调整超时设置
4. **权限问题**: 确保有网络访问权限

### 调试选项
- 查看详细日志输出
- 检查返回的错误码
- 验证网络配置

## 集成到 CI/CD

测试返回值：
- 0: 所有测试通过
- 非0: 有测试失败

可以在构建脚本中使用：
```bash
./testp2psocket
if [ $? -eq 0 ]; then
    echo "Unit tests passed"
else
    echo "Unit tests failed"
    exit 1
fi
```
