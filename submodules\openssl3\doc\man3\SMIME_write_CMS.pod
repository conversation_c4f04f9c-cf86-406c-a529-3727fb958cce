=pod

=head1 NAME

SMIME_write_CMS - convert CMS structure to S/MIME format

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int SMIME_write_CMS(BIO *out, CMS_ContentInfo *cms, BIO *data, int flags);

=head1 DESCRIPTION

SMIME_write_CMS() adds the appropriate MIME headers to a CMS
structure to produce an S/MIME message.

B<out> is the BIO to write the data to. B<cms> is the appropriate
B<CMS_ContentInfo> structure. If streaming is enabled then the content must be
supplied in the B<data> argument. B<flags> is an optional set of flags.

=head1 NOTES

The following flags can be passed in the B<flags> parameter.

If B<CMS_DETACHED> is set then cleartext signing will be used, this option only
makes sense for SignedData where B<CMS_DETACHED> is also set when CMS_sign() is
called.

If the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are added to
the content, this only makes sense if B<CMS_DETACHED> is also set.

If the B<CMS_STREAM> flag is set streaming is performed. This flag should only
be set if B<CMS_STREAM> was also set in the previous call to a CMS_ContentInfo
creation function.

If cleartext signing is being used and B<CMS_STREAM> not set then the data must
be read twice: once to compute the signature in CMS_sign() and once to output
the S/MIME message.

If streaming is performed the content is output in BER format using indefinite
length constructed encoding except in the case of signed data with detached
content where the content is absent and DER format is used.

=head1 BUGS

SMIME_write_CMS() always base64 encodes CMS structures, there should be an
option to disable this.

=head1 RETURN VALUES

SMIME_write_CMS() returns 1 for success or 0 for failure.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_sign(3)>,
L<CMS_verify(3)>, L<CMS_encrypt(3)>
L<CMS_decrypt(3)>

=head1 COPYRIGHT

Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
