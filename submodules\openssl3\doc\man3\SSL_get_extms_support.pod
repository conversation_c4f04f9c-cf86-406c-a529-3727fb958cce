=pod

=head1 NAME

SSL_get_extms_support - extended master secret support

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_get_extms_support(SSL *ssl);

=head1 DESCRIPTION

SSL_get_extms_support() indicates whether the current session used extended
master secret.

This function is implemented as a macro.

=head1 RETURN VALUES

SSL_get_extms_support() returns 1 if the current session used extended
master secret, 0 if it did not and -1 if a handshake is currently in
progress i.e. it is not possible to determine if extended master secret
was used.

=head1 SEE ALSO

L<ssl(7)>

=head1 COPYRIGHT

Copyright 2015-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
