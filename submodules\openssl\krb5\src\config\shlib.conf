# This shell script fragment should set a bunch of variables:
#
# CC_LINK_STATIC: How to link a program if we're only building static
#  libraries for krb5 (but may use other shared libs, and there may
#  be a shared krb5 lib already installed that we shouldn't use).
# CC_LINK_SHARED: How to link a program if we're building shared
#  libraries.
# CXX_LINK_STATIC, CXX_LINK_SHARED: Variants for C++.
# STLIBEXT: Static library extension.
# SHLIBEXT: Shared library extension.
# SHLIBVEXT: Shared library extension, with major version.
# SHLIBSEXT: Shared library extension, with full version.
# (... finish documenting these ...)

#
# Set up some defaults.
#
STLIBEXT=.a
# Default to being unable to build shared libraries.
SHLIBEXT=.so-nobuild
SHLIBVEXT=.so.v-nobuild
SHLIBSEXT=.so.s-nobuild
# Most systems support profiled libraries.
PFLIBEXT=_p.a
# Most systems install shared libs as mode 644, etc. while hpux wants 755
INSTALL_SHLIB='$(INSTALL_DATA)'
# Most systems use the same objects for shared libraries and dynamically
# loadable objects.
DYNOBJEXT='$(SHLIBEXT)'
MAKE_DYNOBJ_COMMAND='$(MAKE_SHLIB_COMMAND)'
DYNOBJ_EXPDEPS='$(SHLIB_EXPDEPS)'
DYNOBJ_EXPFLAGS='$(SHLIB_EXPFLAGS)'
#
use_linker_init_option=no
use_linker_fini_option=no

STOBJEXT=.o
SHOBJEXT=.so
PFOBJEXT=.po
# Default for systems w/o shared libraries
CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
#
SHLIB_EXPORT_FILE_DEP='$(SHLIB_EXPORT_FILE)'
# This will do for most platforms, and we'll substitute for
# LDCOMBINE, SHLIB_EXPFLAGS, and LDCOMBINE_TAIL below.
MAKE_SHLIB_COMMAND=x
INIT_FINI_PREP=:

# Default to static or shared libraries?
default_static=no
default_shared=yes

# Set up architecture-specific variables.
case $krb5_cv_host in
alpha*-dec-osf*)
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	# Alpha OSF/1 doesn't need separate PIC objects
	SHOBJEXT=.o
	INIT_FINI_PREP=initfini=
	LDCOMBINE='$(CC) $(CFLAGS) $(PTHREAD_CFLAGS) -shared -Wl,-expect_unresolved -Wl,\* -Wl,-update_registry -Wl,$(BUILDTOP)/so_locations -Wl,-soname -Wl,$(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) -Wl,-hidden -Wl,-input,osf1.exports $$initfini'
	SHLIB_EXPORT_FILE_DEP=osf1.exports
	use_linker_init_option=yes
	use_linker_fini_option=yes
	EXTRA_FILES="$EXTRA_FILES export"
	SHLIB_RPATH_FLAGS='-rpath $(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROFFLAGS=-pg
	RPATH_FLAG='-Wl,-rpath -Wl,'
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	if test "$ac_cv_c_compiler_gnu" = yes \
		&& test "$krb5_cv_prog_gnu_ld" = yes; then
		# Really should check for gnu ld vs system ld, too.
		CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	else
		# Need -oldstyle_liblookup to avoid picking up shared libs from
		# other builds.  OSF/1 / Tru64 ld programs look through the entire
		# library path for shared libs prior to looking through the
		# entire library path for static libs.
		CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) -Wl,-oldstyle_liblookup $(CFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	fi
	if test "$ac_cv_cxx_compiler_gnu" = yes \
		&& test "$krb5_cv_prog_gnu_ld" = yes; then
		# Really should check for gnu ld vs system ld, too.
		CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	else
		# Need -oldstyle_liblookup to avoid picking up shared libs from
		# other builds.  OSF/1 / Tru64 ld programs look through the entire
		# library path for shared libs prior to looking through the
		# entire library path for static libs.
		CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) -Wl,-oldstyle_liblookup $(CXXFLAGS) $(PTHREAD_CFLAGS) $(LDFLAGS)'
	fi
	# _RLD_ROOT hack needed to repoint "root" directory for purposes
	# of searching for shared libs, since RPATHs take precedence over
	# LD_LIBRARY_PATH.
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`$${LD_LIBRARY_PATH+:$$LD_LIBRARY_PATH} _RLD_ROOT=$${_RLD_ROOT+$$_RLD_ROOT}$${_RLD_ROOT-/}'
	RUN_VARS='LD_LIBRARY_PATH _RLD_ROOT'
	;;

# Note: "-Wl,+s" when building executables enables the use of the
# SHLIB_PATH environment variable for finding shared libraries 
# in non-standard directories.  If a non-standard search-path for
#  shared libraries is compiled into the executable (using 
# -Wl,+b,$KRB5_SHLIBDIR), then the order of "-Wl,+b,..." and "-Wl,+s" 
# on the commandline of the linker will determine which path
# (compiled-in or SHLIB_PATH) will be searched first.
#
# +I initproc routine gets called at load and unload time for
#    shl_load calls, but appears to never be called for link-time
#    specified libraries.
# +e sym exports symbol and supposedly prevents other symbols
#    from being exported, according to the man page, but the
#    latter bit doesn't actually seem to work
# -O +dpv should display any routines eliminated as unused, but -b
#	  apparently turns that off
*-*-hpux*)
	INSTALL_SHLIB='$(INSTALL)'
	case $host_cpu in
	hppa*)
		SHLIBEXT=.sl
		;;
	ia64*)
		SHLIBEXT=.so
		;;
	esac
	SHLIBVEXT='.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.$(LIBMAJOR)'
	RPATH_FLAG='-Wl,+b,'
	if test "$ac_cv_c_compiler_gnu" = yes; then
		PICFLAGS=-fPIC
		SHLIB_RPATH_FLAGS='-Wl,+b,$(SHLIB_RDIRS)'
		SHLIB_EXPFLAGS='-Wl,+s $(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
		LDCOMBINE='gcc -fPIC -shared -Wl,+h,$(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) -Wl,-c,hpux.exports'
	else
		PICFLAGS=+z
		SHLIB_RPATH_FLAGS='+b $(SHLIB_RDIRS)'
		SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
		LDCOMBINE='ld -b +h $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) -c hpux.exports'
	fi
	MAKE_SHLIB_COMMAND="${LDCOMBINE} -o \$@ \$\$objlist \$(LDFLAGS) \$(SHLIB_EXPFLAGS) ${LDCOMBINE_TAIL}"
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='SHLIB_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='SHLIB_PATH'
	SHLIB_EXPORT_FILE_DEP=hpux.exports
	# Do *not* set use_linker_init_option=yes here, because in the
	# case where the library is specified at program link time, the
	# initialization function appears not to get called, only for
	# shl_load.  But for finalization functions, the shl_load case
	# is the one we care about.
	#
	# Not setting use_linker_init_option here should cause compilation
	# failures if the user tries to disable delayed initialization.
	use_linker_fini_option=yes
	;;

mips-sgi-irix6.3)	# This is a Kludge; see below
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	SHOBJEXT=.o
	# Kludge follows: (gcc makes n32 object files but ld expects o32, so we reeducate ld)
	if test "$ac_cv_c_compiler_gnu" = yes; then
		LDCOMBINE='ld -n32 -shared -ignore_unresolved -update_registry $(BUILDTOP)/so_locations -soname $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	else
		LDCOMBINE='ld -shared -ignore_unresolved -update_registry $(BUILDTOP)/so_locations -soname $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	fi
	SHLIB_RPATH_FLAGS='-rpath $(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	# no gprof for Irix...
	PROFFLAGS=-p
	RPATH_FLAG='-Wl,-rpath -Wl,'
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	# This grossness is necessary due to the presence of *three*
	# supported ABIs on Irix, and the precedence of the rpath over
	# LD_LIBRARY*_PATH.  Like OSF/1, _RLD*_ROOT needs to be set to
	# work around this lossage.
	#
	# Set the N32 and 64 variables first because the unqualified
	# variables affect all three and can cause the sed command to fail.
	#
	# This loop is to reduce the clutter a slight bit.
	add='`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_ENV=
	for i in N32 64 ''; do
		RUN_ENV="${RUN_ENV+$RUN_ENV }LD_LIBRARY${i}_PATH=$add\$\${LD_LIBRARY${i}_PATH+:\$\$LD_LIBRARY${i}_PATH}"
		RUN_ENV="${RUN_ENV} _RLD${i}_ROOT=\$\${_RLD${i}_ROOT+\$\${_RLD${i}_ROOT}}\$\${_RLD${i}_ROOT-/}"
		RUN_VARS="$RUN_VARS LD_LIBRARY${i}_PATH _RLD${i}_ROOT"
	done
	;;

mips-sgi-irix*)
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	SHOBJEXT=.o
	if test "$ac_cv_c_compiler_gnu" = yes; then
		LDCOMBINE_TAIL=""
		INIT_FINI_PREP="initfini="
	else
		use_linker_init_option=yes
		use_linker_fini_option=yes
		INIT_FINI_PREP='initfini=; for f in . $(LIBINITFUNC); do if test $$f = .; then :; else initfini="$$initfini -Wl,-init,$${f}__auxinit"; fi; done; for f in . $(LIBFINIFUNC); do if test $$f = .; then :; else initfini="$$initfini -Wl,-fini,$${f}"; fi; done'
		LDCOMBINE_TAIL='-Wl,-exports_file -Wl,$(SHLIB_EXPORT_FILE)'
	fi
	opts='-Wl,-ignore_unresolved -Wl,-update_registry -Wl,$(BUILDTOP)/so_locations'

	if test "$ac_cv_c_compiler_gnu" = yes \
		&& test "$krb5_cv_prog_gnu_ld" = yes; then
		opts=''
	fi
	LDCOMBINE='$(CC) -shared '$opts' -Wl,-soname -Wl,$(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) $$initfini'
	SHLIB_RPATH_FLAGS='-rpath $(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	# no gprof for Irix...
	PROFFLAGS=-p
	RPATH_FLAG='-Wl,-rpath -Wl,'
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	# This grossness is necessary due to the presence of *three*
	# supported ABIs on Irix, and the precedence of the rpath over
	# LD_LIBRARY*_PATH.  Like OSF/1, _RLD*_ROOT needs to be set to
	# work around this lossage.
	#
	# Set the N32 and 64 variables first because the unqualified
	# variables affect all three and can cause the sed command to fail.
	#
	# This loop is to reduce the clutter a slight bit.
	add='`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_ENV=
	for i in N32 64 ''; do
		RUN_ENV="${RUN_ENV+$RUN_ENV }LD_LIBRARY${i}_PATH=$add\$\${LD_LIBRARY${i}_PATH+:\$\$LD_LIBRARY${i}_PATH}"
		RUN_ENV="${RUN_ENV} _RLD${i}_ROOT=\$\${_RLD${i}_ROOT+\$\${_RLD${i}_ROOT}}\$\${_RLD${i}_ROOT-/}"
		RUN_VARS="$RUN_VARS LD_LIBRARY${i}_PATH _RLD${i}_ROOT"
	done
	;;

# untested...
mips-sni-sysv4)
	if test "$ac_cv_c_compiler_gnu" = yes; then
		PICFLAGS=-fpic
		LDCOMBINE='$(CC) -G -Wl,-h -Wl,$(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	else
		PICFLAGS=-Kpic
		LDCOMBINE='$(CC) -G -h $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	fi
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	SHLIBEXT=.so
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	RPATH_FLAG=-R
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

mips-*-netbsd*)
	PICFLAGS=-fPIC
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	LDCOMBINE='ld -shared -soname $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	RPATH_FLAG='-Wl,-rpath -Wl,'
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

*-*-netbsd*)
	PICFLAGS=-fPIC
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBEXT=.so
	LDCOMBINE='$(CC) -shared'
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	RPATH_FLAG=-R
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

*-*-freebsd*)
	case $krb5_cv_host in
		sparc64-*)
			PICFLAGS=-fPIC
			;;
		*)
			PICFLAGS=-fpic
			;;
	esac
	SHLIBVEXT='.so.$(LIBMAJOR)'
	RPATH_FLAG='-Wl,--enable-new-dtags -Wl,-rpath -Wl,'
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	SHLIBEXT=.so
	LDCOMBINE='ld -Bshareable'
	SHLIB_RPATH_FLAGS='--enable-new-dtags -rpath $(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

*-*-openbsd*)
	PICFLAGS=-fpic
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBEXT=.so
	LDCOMBINE='ld -Bshareable'
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	RPATH_FLAG=-R
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

*-*-darwin* | *-*-rhapsody*)
	SHLIBVEXT='.$(LIBMAJOR).$(LIBMINOR).dylib'
	SHLIBSEXT='.$(LIBMAJOR).dylib'
	SHLIB_EXPFLAGS='$(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	SHLIBEXT=.dylib
	DYNOBJEXT=.so
	SHLIB_EXPORT_FILE_DEP=darwin.exports
	LDCOMBINE='$(CC) -undefined error -dead_strip -dynamiclib -compatibility_version $(LIBMAJOR) -current_version $(LIBMAJOR).$(LIBMINOR) -install_name "$(KRB5_LIBDIR)/$(LIBPREFIX)$(LIBBASE)$(SHLIBVEXT)" -exported_symbols_list darwin.exports $(CFLAGS)'
	# The -dylib_file option tells the linker where to find indirect dependent
	# libraries, without adding them to the dependency list.  We need this because
	# the direct dependent libraries contain the pathname where the indirect
	# dependent libraries will be installed (but haven't been yet).
	LDCOMBINE_TAIL=""
	for lib in libkrb5support.1.1.dylib libkadm5srv.5.1.dylib libkdb5.4.0.dylib; do
	    LDCOMBINE_TAIL="$LDCOMBINE_TAIL -dylib_file \"\$(KRB5_LIBDIR)/$lib\":\$(TOPLIBD)/$lib"
	done
	MAKE_DYNOBJ_COMMAND='$(CC) -bundle $(CFLAGS) $(LDFLAGS) -o $@ $$objlist $(DYNOBJ_EXPFLAGS) $(LDFLAGS) -exported_symbols_list darwin.exports'" ${LDCOMBINE_TAIL}"
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) -dynamic $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) -dynamic $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='DYLD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='DYLD_LIBRARY_PATH'
	;;

*-*-solaris*)
	if test "$ac_cv_c_compiler_gnu" = yes; then
		PICFLAGS=-fPIC
		LDCOMBINE='$(CC) $(CFLAGS) -shared -h $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	else
		PICFLAGS=-KPIC
		# Solaris cc doesn't default to stuffing the SONAME field...
		LDCOMBINE='$(CC) $(CFLAGS) -dy -G -z text -h $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) $$initfini'
		#
		case $krb5_cv_host in
		*-*-solaris2.[1-7] | *-*-solaris2.[1-7].*)
		    # Did Solaris 7 and earlier have a linker option for this?
		    ;;
		*)
		    INIT_FINI_PREP='initfini=; for f in . $(LIBINITFUNC); do if test $$f = .; then :; else initfini="$$initfini -Wl,-z,initarray=$${f}__auxinit"; fi; done; for f in . $(LIBFINIFUNC); do if test $$f = .; then :; else initfini="$$initfini -Wl,-z,finiarray=$$f"; fi; done'
		    use_linker_init_option=yes
		    use_linker_fini_option=yes
		    ;;
		esac
	fi
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROFFLAGS=-pg
	RPATH_FLAG=-R
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(PURE) $(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(PURE) $(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(PURE) $(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(PURE) $(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	;;

*-*-linux* | *-*-gnu* | *-*-k*bsd*-gnu)
	PICFLAGS=-fPIC
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBSEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	# Linux ld doesn't default to stuffing the SONAME field...
	# Use objdump -x to examine the fields of the library
	# UNDEF_CHECK is suppressed by --enable-asan
	LDCOMBINE='$(CC) -shared -fPIC -Wl,-h,$(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT) $(UNDEF_CHECK)'
	UNDEF_CHECK='-Wl,--no-undefined'
	# $(EXPORT_CHECK) runs export-check.pl when in maintainer mode.
	LDCOMBINE_TAIL='-Wl,--version-script binutils.versions $(EXPORT_CHECK)'
	SHLIB_EXPORT_FILE_DEP=binutils.versions
	RPATH_FLAG='-Wl,--enable-new-dtags -Wl,-rpath -Wl,'
	# For cases where we do have dependencies on other libraries
	# built in this tree...
	SHLIB_RPATH_FLAGS='$(RPATH_FLAG)$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROFFLAGS=-pg
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'

	## old version:
	# Linux libc does weird stuff at shlib link time, must be
	# explicitly listed here.  This also makes it get used even
	# for the libraries marked as not having any dependencies; while
	# that's not strictly correct, the resulting behavior -- not adding
	# extra -R directories -- is still what we want.
	#LDCOMBINE='ld -shared -h $(LIBPREFIX)$(LIBBASE)$(SHLIBSEXT)'
	#LDCOMBINE_TAIL="-lc"
	#SHLIB_EXPFLAGS='-R$(SHLIB_RDIRS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'

	;;

*-*-bsdi4*)
	PICFLAGS=-fpic
	SHLIBVEXT='.so.$(LIBMAJOR)'
	SHLIBEXT=.so
	LDCOMBINE='ld -Bshareable'
	SHLIB_RPATH_FLAGS='-R$(SHLIB_RDIRS)'
	SHLIB_EXPFLAGS='$(SHLIB_RPATH_FLAGS) $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROG_RPATH_FLAGS='-Wl,-rpath,$(PROG_RPATH)'
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH)'
	RUN_ENV='LD_LIBRARY_PATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/
/:/g"`'
	RUN_VARS='LD_LIBRARY_PATH'
	PROFFLAGS=-pg
	;;

*-*-aix5*)
	SHLIBVEXT='.so.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBEXT=.so
	# AIX doesn't need separate PIC objects
	SHOBJEXT=.o
	SHLIB_EXPFLAGS='  $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROFFLAGS=-pg
	if test "$ac_cv_c_compiler_gnu" = "yes" ; then
	  wl_prefix=-Wl,
	  RPATH_FLAG='-Wl,-blibpath:'
	  LDCOMBINE='$(CC) -shared -v -o $@ $$objlist -nostartfiles -Xlinker -bgcbypass:1 -Xlinker -bfilelist -Xlinker -bM:SRE -Xlinker -bE:$(SHLIB_EXPORT_FILE) -Xlinker -bernotok -Xlinker -brtl $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	else
	  wl_prefix=
	  RPATH_FLAG=-blibpath:
	  LDCOMBINE='/bin/ld -o $@ $$objlist -H512 -T512 -bnoentry -bgcbypass:1 -bnodelcsect -bfilelist -bM:SRE -bE:$(SHLIB_EXPORT_FILE) -bernotok -brtl $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	fi
	# Assume initialization always delayed.
	INIT_FINI_PREP="wl=${wl_prefix}; "'i=1; initfini=; for f in . $(LIBFINIFUNC); do if test $$f != .; then initfini="$$initfini $${wl}-binitfini::$$f:$$i"; else :; fi; i=`expr $$i + 1`; done'
	use_linker_fini_option=yes
	MAKE_SHLIB_COMMAND="${INIT_FINI_PREP} && ${LDCOMBINE}"
	RPATH_TAIL=:/usr/lib:/lib
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH):'"$RPATH_TAIL"
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	# $(PROG_RPATH) is here to handle things like a shared tcl library
	RUN_ENV='LIBPATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`:$(PROG_RPATH):/usr/lib:/usr/local/lib'
	RUN_VARS='LIBPATH'
	;;

*-*-aix4.*)
	SHLIBVEXT='.a.$(LIBMAJOR).$(LIBMINOR)'
	SHLIBEXT=.a
	# AIX doesn't need separate PIC objects
	SHOBJEXT=.o
	SHLIB_EXPFLAGS='  $(SHLIB_DIRS) $(SHLIB_EXPLIBS)'
	PROFFLAGS=-pg
	# Dynamically loaded object can have whatever suffix, but don't
	# make archives like for shared libraries.
	DYNOBJEXT=.so
	#
	if test "$ac_cv_c_compiler_gnu" = "yes" ; then
	  wl_prefix=-Wl,
	  RPATH_FLAG='-Wl,-blibpath:'
	  LDCOMBINE='$(CC) -shared -v -o shr.o.$(LIBMAJOR).$(LIBMINOR) $$objlist -nostartfiles -Xlinker -bgcbypass:1 -Xlinker -bfilelist -Xlinker -bM:SRE -Xlinker -bE:$(SHLIB_EXPORT_FILE) -Xlinker -bernotok $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	  LDCOMBINE_DYN='$(CC) -shared -v -o $@ $$objlist -nostartfiles -Xlinker -bgcbypass:1 -Xlinker -bfilelist -Xlinker -bM:SRE -Xlinker -bE:$(SHLIB_EXPORT_FILE) -Xlinker -bernotok $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	else
	  wl_prefix=
	  RPATH_FLAG=-blibpath:
	  LDCOMBINE='/bin/ld -o shr.o.$(LIBMAJOR).$(LIBMINOR) $$objlist -H512 -T512 -bnoentry -bgcbypass:1 -bnodelcsect -bfilelist -bM:SRE -bE:$(SHLIB_EXPORT_FILE) -bernotok $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	  LDCOMBINE_DYN='/bin/ld -o $@ $$objlist -H512 -T512 -bnoentry -bgcbypass:1 -bnodelcsect -bfilelist -bM:SRE -bE:$(SHLIB_EXPORT_FILE) -bernotok $(SHLIB_EXPFLAGS) $(LDFLAGS) -lc $$initfini'
	fi
	# Assume initialization always delayed.
	INIT_FINI_PREP="wl=${wl_prefix}; "'i=1; initfini=; for f in . $(LIBFINIFUNC); do if test $$f != .; then initfini="$$initfini $${wl}-binitfini::$$f:$$i"; else :; fi; i=`expr $$i + 1`; done'
	use_linker_fini_option=yes
	MAKE_SHLIB_COMMAND="${INIT_FINI_PREP} && ${LDCOMBINE}"' && ar cq $@ shr.o.$(LIBMAJOR).$(LIBMINOR) && chmod +x $@ && rm -f shr.o.$(LIBMAJOR).$(LIBMINOR)'
	MAKE_DYNOBJ_COMMAND="${INIT_FINI_PREP} && ${LDCOMBINE_DYN}"
	RPATH_TAIL=:/usr/lib:/lib
	PROG_RPATH_FLAGS='$(RPATH_FLAG)$(PROG_RPATH):'"$RPATH_TAIL"
	CC_LINK_SHARED='$(CC) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CFLAGS) $(LDFLAGS)'
	CC_LINK_STATIC='$(CC) $(PROG_LIBPATH) $(CFLAGS) $(LDFLAGS)'
	CXX_LINK_SHARED='$(CXX) $(PROG_LIBPATH) $(PROG_RPATH_FLAGS) $(CXXFLAGS) $(LDFLAGS)'
	CXX_LINK_STATIC='$(CXX) $(PROG_LIBPATH) $(CXXFLAGS) $(LDFLAGS)'
	# $(PROG_RPATH) is here to handle things like a shared tcl library
	RUN_ENV='LIBPATH=`echo $(PROG_LIBPATH) | sed -e "s/-L//g" -e "s/ /:/g"`:$(PROG_RPATH):/usr/lib:/usr/local/lib'
	RUN_VARS='LIBPATH'
	;;
esac

if test "${MAKE_SHLIB_COMMAND}" = "x" ; then
  if test "${INIT_FINI_PREP}" != ":"; then
    MAKE_SHLIB_COMMAND="${INIT_FINI_PREP} && ${LDCOMBINE} -o \$@ \$\$objlist \$(SHLIB_EXPFLAGS) \$(LDFLAGS) ${LDCOMBINE_TAIL}"
  else
    MAKE_SHLIB_COMMAND="${LDCOMBINE} -o \$@ \$\$objlist \$(SHLIB_EXPFLAGS) \$(LDFLAGS) ${LDCOMBINE_TAIL}"
  fi
fi
