=pod

=head1 NAME

CMS_encrypt_ex, CMS_encrypt - create a CMS envelopedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ContentInfo *CMS_encrypt_ex(STACK_OF(X509) *certs, BIO *in,
                                 const EVP_CIPHER *cipher, unsigned int flags,
                                 OSSL_LIB_CTX *libctx, const char *propq);
 CMS_ContentInfo *CMS_encrypt(STACK_OF(X509) *certs, BIO *in,
                              const EVP_CIPHER *cipher, unsigned int flags);

=head1 DESCRIPTION

CMS_encrypt_ex() creates and returns a CMS EnvelopedData or
AuthEnvelopedData structure. I<certs> is a list of recipient certificates.
I<in> is the content to be encrypted. I<cipher> is the symmetric cipher to use.
I<flags> is an optional set of flags. The library context I<libctx> and the
property query I<propq> are used internally when retrieving algorithms from
providers.

Only certificates carrying RSA, <PERSON><PERSON><PERSON><PERSON> or <PERSON> keys are supported by this
function.

E<PERSON>_des_ede3_cbc() (triple DES) is the algorithm of choice for S/MIME use
because most clients will support it.

The algorithm passed in the B<cipher> parameter must support ASN1 encoding of
its parameters. If the cipher mode is GCM, then an AuthEnvelopedData structure
containing MAC is used. Otherwise an EnvelopedData structure is used. Currently
the AES variants with GCM mode are the only supported AEAD algorithms.

Many browsers implement a "sign and encrypt" option which is simply an S/MIME
envelopedData containing an S/MIME signed message. This can be readily produced
by storing the S/MIME signed message in a memory BIO and passing it to
CMS_encrypt().

The following flags can be passed in the B<flags> parameter.

If the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are
prepended to the data.

Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if B<CMS_BINARY> is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If B<CMS_BINARY> is set then
B<CMS_TEXT> is ignored.

OpenSSL will by default identify recipient certificates using issuer name
and serial number. If B<CMS_USE_KEYID> is set it will use the subject key
identifier value instead. An error occurs if all recipient certificates do not
have a subject key identifier extension.

If the B<CMS_STREAM> flag is set a partial B<CMS_ContentInfo> structure is
returned suitable for streaming I/O: no data is read from the BIO B<in>.

If the B<CMS_PARTIAL> flag is set a partial B<CMS_ContentInfo> structure is
returned to which additional recipients and attributes can be added before
finalization.

The data being encrypted is included in the CMS_ContentInfo structure, unless
B<CMS_DETACHED> is set in which case it is omitted. This is rarely used in
practice and is not supported by SMIME_write_CMS().

If the flag B<CMS_STREAM> is set the returned B<CMS_ContentInfo> structure is
B<not> complete and outputting its contents via a function that does not
properly finalize the B<CMS_ContentInfo> structure will give unpredictable
results.

Several functions including SMIME_write_CMS(), i2d_CMS_bio_stream(),
PEM_write_bio_CMS_stream() finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 B<BIO> directly using
BIO_new_CMS().

The recipients specified in B<certs> use a CMS KeyTransRecipientInfo info
structure. KEKRecipientInfo is also supported using the flag B<CMS_PARTIAL>
and CMS_add0_recipient_key().

The parameter B<certs> may be NULL if B<CMS_PARTIAL> is set and recipients
added later using CMS_add1_recipient_cert() or CMS_add0_recipient_key().

CMS_encrypt() is similar to CMS_encrypt_ex() but uses default values
of NULL for the library context I<libctx> and the property query I<propq>.

=head1 RETURN VALUES

CMS_encrypt_ex() and CMS_encrypt() return either a CMS_ContentInfo
structure or NULL if an error occurred. The error can be obtained from
ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_decrypt(3)>

=head1 HISTORY

The function CMS_encrypt_ex() was added in OpenSSL 3.0.

The B<CMS_STREAM> flag was first supported in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2008-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
