The files nid.h, obj_mac.num, and obj_dat.h are generated from objects.txt and
obj_mac.num. To regenerate them, run:

    go run objects.go

objects.txt contains the list of all built-in OIDs. It is processed by
objects.go to output obj_mac.num, obj_dat.h, and nid.h.

obj_mac.num is the list of NID values for each OID. This is an input/output
file so NID values are stable across regenerations.

nid.h is the header which defines macros for all the built-in OIDs in C.

obj_dat.h contains the ASN1_OBJECTs corresponding to built-in OIDs themselves
along with lookup tables for search by short name, OID, etc.
