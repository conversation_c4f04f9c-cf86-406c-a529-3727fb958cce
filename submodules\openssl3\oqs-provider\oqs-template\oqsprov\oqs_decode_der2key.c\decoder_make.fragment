{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
MAKE_DECODER("{{ variant['name'] }}", {{ variant['name'] }}, oqsx, PrivateKeyInfo);
MAKE_DECODER("{{ variant['name'] }}", {{ variant['name'] }}, oqsx, SubjectPublicKeyInfo);
     {%- for classical_alg in variant['mix_with'] %}
MAKE_DECODER("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, oqsx, PrivateKeyInfo);
MAKE_DECODER("{{ classical_alg['name'] }}_{{ variant['name'] }}", {{ classical_alg['name'] }}_{{ variant['name'] }}, oqsx, SubjectPublicKeyInfo);
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}

