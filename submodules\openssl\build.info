{-
     our $sover = $config{shlib_version_number};
     our $sover_filename = $sover;
     $sover_filename =~ s|\.|_|g
         if $config{target} =~ /^mingw/ || $config{target} =~ /^VC-/;
     $sover_filename =
         sprintf "%02d%02d", split m|\.|, $config{shlib_version_number}
         if $config{target} =~ /^vms/;
     "";
-}
LIBS=libcrypto libssl
INCLUDE[libcrypto]=. include
INCLUDE[libssl]=. include
DEPEND[libssl]=libcrypto

# Empty DEPEND "indices" means the dependencies are expected to be built
# unconditionally before anything else.
DEPEND[]=include/openssl/opensslconf.h include/crypto/bn_conf.h \
         include/crypto/dso_conf.h
DEPEND[include/openssl/opensslconf.h]=configdata.pm
GENERATE[include/openssl/opensslconf.h]=include/openssl/opensslconf.h.in
DEPEND[include/crypto/bn_conf.h]=configdata.pm
GENERATE[include/crypto/bn_conf.h]=include/crypto/bn_conf.h.in
DEPEND[include/crypto/dso_conf.h]=configdata.pm
GENERATE[include/crypto/dso_conf.h]=include/crypto/dso_conf.h.in

IF[{- defined $target{shared_defflag} -}]
  IF[{- $config{target} =~ /^mingw/ -}]
    GENERATE[libcrypto.def]=util/mkdef.pl crypto 32
    DEPEND[libcrypto.def]=util/libcrypto.num
    GENERATE[libssl.def]=util/mkdef.pl ssl 32
    DEPEND[libssl.def]=util/libssl.num

    SHARED_SOURCE[libcrypto]=libcrypto.def
    SHARED_SOURCE[libssl]=libssl.def
  ELSIF[{- $config{target} =~ /^aix/ -}]
    GENERATE[libcrypto.map]=util/mkdef.pl crypto aix
    DEPEND[libcrypto.map]=util/libcrypto.num
    GENERATE[libssl.map]=util/mkdef.pl ssl aix
    DEPEND[libssl.map]=util/libssl.num

    SHARED_SOURCE[libcrypto]=libcrypto.map
    SHARED_SOURCE[libssl]=libssl.map
  ELSE
    GENERATE[libcrypto.map]=util/mkdef.pl crypto linux
    DEPEND[libcrypto.map]=util/libcrypto.num
    GENERATE[libssl.map]=util/mkdef.pl ssl linux
    DEPEND[libssl.map]=util/libssl.num

    SHARED_SOURCE[libcrypto]=libcrypto.map
    SHARED_SOURCE[libssl]=libssl.map
  ENDIF
ENDIF
# VMS and VC don't have parametrised .def / .symvec generation, so they get
# special treatment, since we know they do use these files
IF[{- $config{target} =~ /^VC-/ -}]
  GENERATE[libcrypto.def]=util/mkdef.pl crypto 32
  DEPEND[libcrypto.def]=util/libcrypto.num
  GENERATE[libssl.def]=util/mkdef.pl ssl 32
  DEPEND[libssl.def]=util/libssl.num

  SHARED_SOURCE[libcrypto]=libcrypto.def
  SHARED_SOURCE[libssl]=libssl.def
ELSIF[{- $config{target} =~ /^vms/ -}]
  GENERATE[libcrypto.opt]=util/mkdef.pl crypto "VMS"
  DEPEND[libcrypto.opt]=util/libcrypto.num
  GENERATE[libssl.opt]=util/mkdef.pl ssl "VMS"
  DEPEND[libssl.opt]=util/libssl.num

  SHARED_SOURCE[libcrypto]=libcrypto.opt
  SHARED_SOURCE[libssl]=libssl.opt
ENDIF

IF[{- $config{target} =~ /^(?:Cygwin|mingw|VC-)/ -}]
  GENERATE[libcrypto.rc]=util/mkrc.pl libcrypto
  GENERATE[libssl.rc]=util/mkrc.pl libssl

  SHARED_SOURCE[libcrypto]=libcrypto.rc
  SHARED_SOURCE[libssl]=libssl.rc
ENDIF

IF[{- $config{target} =~ /^Cygwin/ -}]
 SHARED_NAME[libcrypto]=cygcrypto-{- $sover_filename -}
 SHARED_NAME[libssl]=cygssl-{- $sover_filename -}
ELSIF[{- $config{target} =~ /^mingw/ -}]
 SHARED_NAME[libcrypto]=libcrypto-{- $sover_filename -}{- $config{target} eq "mingw64" ? "-x64" : "" -}
 SHARED_NAME[libssl]=libssl-{- $sover_filename -}{- $config{target} eq "mingw64" ? "-x64" : "" -}
ELSIF[{- $config{target} =~ /^VC-/ -}]
 SHARED_NAME[libcrypto]=libcrypto-{- $sover_filename -}{- $target{multilib} -}
 SHARED_NAME[libssl]=libssl-{- $sover_filename -}{- $target{multilib} -}
ENDIF

# VMS has a cultural standard where all libraries are prefixed.
# For OpenSSL, the choice is 'ossl$' (this prefix was claimed in a
# conversation with VSI, Tuesday January 26 2016)
# Also, it seems it's usual to have the pointer size the libraries
# were built for as part of the name.
IF[{- $config{target} =~ /^vms/ -}]
 RENAME[libcrypto]=ossl$libcrypto{- $target{pointer_size} -}
 RENAME[libssl]=ossl$libssl{- $target{pointer_size} -}
 SHARED_NAME[libcrypto]=ossl$libcrypto{- $sover_filename -}_shr{- $target{pointer_size} -}
 SHARED_NAME[libssl]=ossl$libssl{- $sover_filename -}_shr{- $target{pointer_size} -}
ENDIF
