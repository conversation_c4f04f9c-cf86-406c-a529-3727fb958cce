[libdefaults]
	default_realm = __REALM__
	default_keytab_name = FILE:__K5ROOT__/v5srvtab
	dns_fallback = no
	plugin_base_dir = __PLUGIN_DIR__
	allow_weak_crypto = true

[realms]
	__REALM__ = {
		kdc = __KDCHOST__:1750
		admin_server = __KDCHOST__:1751
		database_module = foobar_db2_module_blah
	}

[domain_realm]
	__LOCALHOST__ = __REALM__
	__KDCHOST__ = __REALM__

[logging]
	admin_server = FILE:__K5ROOT__/syslog
	kdc = FILE:__K5ROOT__/syslog
	default = FILE:__K5ROOT__/syslog


# THIS SHOULD BE IN KDC.CONF INSTEAD!
[dbmodules]
	db_module_dir = __MODDIR__
	foobar_db2_module_blah = {
		db_library = db2
		database_name = __K5ROOT__/kdb5
	}
