#  CAVS 11.1
#  "PQGGen" information for "dsa_values"
#  Mod sizes selected: 1024
#  Generated on Fri Apr 29 15:23:36 2011

[mod = 1024]

Intermediate values of P:
	counter = 0
	P = cb0ef5f3b647e499962327f64776fa3135aba9081ba863d601f915d7ea9b8382d841d527d3ca1af36afd922ebdaa15d7b6c8a49e6471b45de8617edcf66444cdd1291915e3be125618a1032c0c7d85bb457a06c502b8e024e0b278377e8d196fe925d8cc133585d0ce5593a178e96e58382ac6e30e5b175b48538afa5768de19
	counter = 1
	P = e4f63f3a403de17c57b1db4a5ea77f4f9baba5b638ef2f7ed76c80b259e8deed786f31a29aad9bf37d7045d66f9e56ed51613ca507de485fa7efa56e807f6c962a50d22269885bd54bf1435a6bc492988b8786d8bc288aa947f07768d4eaea4c7d1d4bdc0f069e28e5e08930bda263a6deee14612f1a6519acad25f1fd35b845
	counter = 2
	P = a3c0094f0d4b54ea4fd8c6b3cf47bbb32d9d67e987c3b40a9aae1b5e63772c07032a8631d7824af5a2812952841c73c8085dac909e972b6febe6fd4c13dea732e3945c2d84ddd5b62443a8f1aaeb20a332058c674518b614093bf18aaa3c4e4a81da330315cd361a1794287b6ea0d5db178ba2681ab976935aabaf8ab11c782b
	counter = 3
	P = f4bc33a477f7b1af5181a03ce4badb9c1f3ad1e9fa4f8f343f90772d2939c14dfc06d4fee410dadda998c391787de3097121437b6039212a57cbe44be9a3ce542182da90bd9ba332daa276602e40ca06ca09f7bff05f06823976228c8551020879b789a664a2edb684443c15e0ab8a87e853853b67e53ec1f2c9932677eaad2f
	counter = 4
	P = e6813f2378468a9a0dfc89e406e85fc8cf289d4610373d5ed5cdb82e707d1717ddf5398bcb062cbade0e6bd6ee263f9932cc10174ecb5ba9bbbbbb6f787c04af79ce122e046398b0c4414a41472c16a6722b1d07aea209b3146af508ff8685284340ec0b816ea0f0069d8dffadac5b78fdb98eb044e7b249961d2bf4dbe773e7
P = bffcb67173e740288bc3a0022ec13e92597ad48f69276f3a46123559e4d618a38df0665b9f920a3b5ad151fef35da64bc24262d1b11fcef69d2a3dae92c0741e9329c043e6c3a7c15ec01779ad5ff4bc42ec9800874413b6d8ed6cf7f25227bf5f93c30345ea193389495f3252169515ed8c2a238a3438609c03d33dee45ff65
Q = c3febb36098f869185e234d440e0799102e313a7
G = 8ce2ed722c732387e221e6f7580e082263b6c8a98642b4ac4a21ac313f2752f68a0008ead7062b0b40147a10461320660ee50b1b048b7cfd3441c66e0818669d7a948e84fd890d1b0291f75a3ab605b9a44f657f6afd21efed706f8ca5b7ec1d9895fc20d525959a827253622eea9a562458fe33e342d41c832a507573aef374
Seed = 40e6c273821f582e1c2fd3fc2fbf07f6bfd5b1aa
c = 735
H = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002

Intermediate values of P:
	counter = 0
	P = a5f76fd7e67e4ff83378cb367d4cb2f0824cbd19735c7cfab742cb0501200a49a633e53c41d3913242391637a30e27cbb15e0c34cfc607ad440f300f78eda999ddf46b5bfe64207ae143b3470d53bb1705a65fcddbb872b505a922e6ebaf36378d8c421085dbbdc4a3b6f2fbd942c96f70700c585964f1b1dcc5ab33d34a4b69
	counter = 1
	P = a51f1ca7d663830a7c1409e11b1cb43a8632a13085a68096e02d0b3f0e0ed1b2f58470dd04fcfb6b9b299ad9677170c12b291a33798f2f6c2206453822c36c6f69d6736717f0299433976ee3af57dfad78c32f0344cddf731129cd8f44ab9f511225d5c229054fa0ae352048a7c2d55965104fbe893855c1ac76f8f65f98d887
	counter = 2
	P = bd8d8ac2abe3ba0181acc372a8c85083132659e000878ffb0f7679a67dedca9c5b920d7b6ccc398630c26829e392f5c85e361a0d293277b9ea1ac19e02c8ac5d4b8221b419b171f135346d1393636e5d449e69b95e9b9384d1cf37e89a59e72eb92a2d8012e07b7c4bdff39e579f390ffd98f7c8a5cad7af51cb16e7e1cf1065
	counter = 3
	P = ff2f550282bf342d49bb1ec6becd50a3b115c9f29c41d569bc02d84e2564f3a94ca0c671d4344b2c2d35aab542d925926778171d2701dd3e71ec53e89e3f487396ca3c047209e6814cb7ba7b0b522d53aac4f33baaf86a9f6aa4dc729d316d976169353aa35bf2ea3f72dd5b895140d4cfb2fa75926513d989679398cac15439
	counter = 4
	P = ee61c03fc7420721de7768df7f60f8f10590e7aad30ec65974824d56644ac6f673cc94abb071f573808652814a0951c758bcee287f9c55ebae1c6e02d2f0a1ce996b6da830b8c10dcc95988e85d1f9d14a4c877d896b496fac3deacca7f33d4e1a36a768a882fed781b0ba403ac86b05d3ecbff8d8ee57bc1cd5c992143af8f9
P = e5c5bf986604fb103f23d828d5ed2443a04b4e1d09272248ea5f6d5b5fc809452e767731354e88bc4b64deef6874eddb81e5ffca765de3947421ab6f2a3461e235aa21466b42d3a1dc46b60ef543f986872cdc665b9822bbe4e300610c891a038d9c288fdb74c9a6451a272cdd8dfd85c2de565cd5e08e00efa727cd9d678445
Q = ff459fc62404880b4eb110af1975d2314767f447
G = 9636b492a308b87e1adb00a99f5e92521f5d367f87b8aa843870a6f4861abb334bdf9233e623626f4e82c9b52923b570014920d47958abbf574dc6566a483ffedeb517cab8619e5a8e0bf4141be365cbd49ceb62ec487e1b656be7e21443c481622095b6d316bf95862e9ee3386b69abac87888f897e56804f42c56cc294e26b
Seed = 92bc9f80353c21388ee934f4bf6bb0b80812768c
c = 862
H = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002

Intermediate values of P:
	counter = 0
	P = cffaeb9062b5051467ddd64e4798d8fc0663d2a0783858cf327f890ca9694bf7993827904a980da97c5e3f2c13f1a9226889b41fa4b51e214bd49531eb66ec01e3ec6f7e4e9e9b3d72a833036531f25389012b67fd67577d1993254caa4b1e414909df799c20dcb7c24bc96314f7ab9a7ff8d135ca526e0168d6fc7b89254d1d
	counter = 1
	P = a718f63d7779d96b17e046472894485bf6aace15c9cbc38506e8cf400eaf19e604648fed6589efeff6a34d606c4302f81d444bd4acd34a797cc5c33f39ed6f44af4c91dccd3d83fea615a3bf1744667dc62152ab7102c2c201243738d696fcff2800ae44e257e911f9f7aa0be9392eb81170494e742b9df1ffb4ebe401d544b7
	counter = 2
	P = cfdd1fc21163980ce6195c0a978e162228ae6ff71ad7b399526ac8969b6bc9ac1c4b9da675191c75e5f13a1b324805d8882901e4965a4466a8a0f4597e4ef4cfa848ed19ddbf81b1f142d73631dc01b69ab9d76f4a71c2aefefb43e2c909ef6f54b8930445a4e6869edfa4c03958f9da86bb616c595f3bec01a062baa1b41217
	counter = 3
	P = f89943759b2b3db0c5c5cbcbae1e45d301c1332ad81493fc7d5c9eab800a345d4fe5c916c046a3bd7bf3d2c0b4bad9df802238946dfdb77ba301f464270117b8b4c695942ac6a6e15cb671332106f2b56bee93326314ce72667849b9f0e2ce57f066da1c9a2391ebc8c2bd25dd760a9959c124b352751a21d86e9d637c403f47
	counter = 4
	P = fa83db2456e59c5aa588cb81038d87e3b86315eb8534d000ccaaecda575fce35281edac4d0250dc371271aee00e3838294d8aa9777853070b9ebb37e44c642fbe3fb111b73ffeba47084daa5558dc2ab92d41339dbd626d868385702ff36c23e216ab6abb4e046bca971a35316c8c96c99349d9cfb4f62203bbb5a89757aa4d3
P = 9b1a62e08984cc39a00eeb4419395421c5a8fc9043fb3919b120fd7e9948d7ef3107a30097664b3eafec3197b11fb886d3dd5debe3f473415941ff316b78900071cf7219918e15dfcb8338c6ad3b042407ab7871efa0aa372afbf5c3441e24261133e05141f7c419fe352610dbbcab00285c20014451282f236d8ba7c444fecd
Q = 9ebd7807109cb6f0c19f75400e5454b3cd1adbcd
G = 5987d7a413a4527ae6ea05f0427395fb59c98d5689249f8071a1e6aa2d0f15bfb688746df0b89cb5c3e8021bd0ef343ed700ba39634ba2716e280dc56db1b52868a8d82d9f8a8db2c271304cc62cb88f7ff5c23fc794f249b267a39d05d207961ffec757deefbd3e851a3f46718888fdd13df72e5e8584aa34b129bfa3090e52
Seed = cd8739710ce410621963e52c2638ae370ea82c9b
c = 123
H = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002

Intermediate values of P:
	counter = 0
	P = e86cff9dedf9477e69c7cd6bdb82b22cd0f9d10e33681447206963cff3b1c7679315b6cc11df2c6a49d93fc19421be9f0aa0cafdc80c09bad9dcb0ac8a14bcc2714309b255f8f81c56fb8e9c219c547a09d9c541ad0713aaf3fd4d9910342eb7113930e52238376fe32ebb463f627395b873719c8576f8f363c83ba3c0e28fff
	counter = 1
	P = d82a224106f7e962781f9a110695ab86a25484d6ff6e72e642dfff1bc4d6d8d3d3c2a7cfcf1de98e5667e94271271407039c6b6d47c6f37a9ffcf5fc40a3aa21c7969a8a1a4fcd6bf800e13e6841ef5a2ae04e0868c1e6c372e559fb2e36065a891abb3705830b944c851ebd9d0ed2b89e709e5d10a30ddad76eefc219606e3f
	counter = 2
	P = cf65deed11243220d077303e042226757b9438557502a8bf47cf51b71a206f2c7023e35f05f79b26b995ca78fe7cc6e9c5b6790686bc5ad8213cbffdcf59b0cfe172b2db6baa0f28d084816635be10257cf76186ad6b759b5fbaaa9b7fa6cddeaf2c4a32e700e487b0aace528e90f9ef118f92cced2932cfa50cac4ca8e17dc5
	counter = 3
	P = 82c2748e8a934ae28937883da6d1d9847da46f4c2d9e5dfca3c94382ce347eaaafc46033a50d07c9f19ef4b824211bcc70b719951e90958653b4171fd02995565a93c654c84a3f3602f1137d938ab6580ca135d127a20f5ee41253ac0e2fb1de0df462bd17869283ca3d4f1a867c9b0069cbe8304980cb21fbcba278272ae857
	counter = 4
	P = a74dce58316e0108eac23952a86f9e0ffa526f64b5f26c501f706c70c8afebd77aeb3f316fe418471ddfac91dec676f75e712156c92dd03d64b39407e46d28bf5a2e352815ba7846c3737f60734b74c09669225dce9d0142318181d18486f135f7fddc59d71b7eedd665cc127d7e149594aee680ff21c1f882be406845ec7fdb
P = 96e437ef25d35b8a930776ac2687eac85f8413ec23a0b1b62767820696984f9ad1daac29f008b782139a0305b9ffb67906ffd8b9262cbbe49511d980c9bb22be143615577814b3d826cee5344dc19e1be439fbccce06d268ac3d471a3fe3ae829e2b718ecd737d5ac53e1211fefe1f8a5526cc327b5f438725b341e53cb10faf
Q = 97824ae0342a4acf6c4261fc5376d7dabdabbe73
G = 15b5e0779d22646cd7d6803e90fbb24cddf242b2d874196bb58020484bf3cd7a06d36c8c22715173a5c22aa0e23f24232e14eac589024d5fcfa98bf359e810d01a63342d556d84ae8dd1e6e87550186a73f9a2e73ed16f5e2ceda2189c5432576dab1f49b36bbc6b7e8f7c9f11c3829ea649934f1a84b063cdc789f1ad0262ce
Seed = ea6c29d32280f6969a138b263e6fe225756e3881
c = 545
H = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002

Intermediate values of P:
	counter = 0
	P = 815466b4321c6561bd1d9d3606a6df525df602fe381e79bf480ac29593c15f43ee1f387b7f9c6d66bc78dbb85dea800e22f6508a160bdcf80b79cbeccb45489abec6c668e737a045f4c0d44d3c82f31bcd01c90094279b159d162feb520c9e1723ff3293fee7c5d76d074e2a03931e43094602885a4ddafa7181be9b6ff5904d
	counter = 1
	P = e3ddeffe8a29f33c09b4f4e8188f60c2858206e24f92b81074a98533e7dcb24776cd419395eaea8e6570c189c72f1058be85dc8b3dd50f86e41155ed7cbd3f7ca11435b378e1449b9dae9a70a4f97662bd4b49cc8004f3570ad8148981653b177eb14623bc4807d7d91128658270f1ecbbf9d5b491206b2e50d7823d9a34ab17
	counter = 2
	P = b4162935f4dde300d1954eb14388e45392aca051b7d4b8c70761501bd01d624f8e6a3e8a52e3d6a2114b5082bc0ddd98dedf24163297dff7e95a4fe81351d2b76d4e0bd50af5b4adc4b24d88c47f7559acd9f847cf2b2f3d01106cb4a2e8d422640bec39aedc3e639e8da015400c63569ba5d127af11ccba19e158a8494d9b37
	counter = 3
	P = c1b61c34760bde586b576f7d4786cbc81eb6f7251b174af76cd4c25a3447f55498c013a835529bd8c86dc249b52597d9c44eb8959f183cbff99ffa1937b9a79ee4cbc1b553b62f9aa25cb125f5c7b796dd5fa50abfbd1997a3b326c243623485635e251b59d56ce73741c94c6fb5a459bd5eb8e6a0b702fca79482464cedbc6d
	counter = 4
	P = 8f45fd4333311cf24cc7774124873a43e3849a15fe7830503846871abd4ad6385d051c643507eb2e678ba916b2b0392fc9f50550330e7f6891bf051f69bc8b56518e62da5f0d715103bf04437ce311fb4626c2d770c268ab8d8293a15813d6bc7f0199e922bc5a117a8db215b6afe64ea42a8d1c7e17994147de2452ce0d29bb
P = dbd706068fdafbaf310398646b07fc368e1eb96ec687aa6b8c3fc82ba6b808704e8b017d5bd0c6151011ef93eaaf0781930f1e675be04f3c1f99bab77e48de2982f0cbc966bd86424683c012cbea2d86b58df726b51fd74d3327f67aea5d5a3481460cfde014353f755adc1465cb1cd92be468d4c127877ded93fe90e4b7c857
Q = e3ef21c6c282520a0928071b124689b47f52b8c3
G = db7f29dd3526b3657af092d25295daa4ada36f593f92de18a5b7f5113312fa12a4c7534ecfa715a1e370d69c2a4d9ae2c82b307b380a31e9b4894ae658e654604dc72046ee9755d8b1baf223d6713ba1ae0ead1de8e388b9f9415afd979d125ae52e69f5d9918f854e28db3c3a5acb8a4cac59de7bbeaf7bea99c370926db197
Seed = d7c137669294edf32479b093c25c0bbbe46cbf65
c = 243
H = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002

