#!/usr/bin/tclsh
lappend auto_path [file dirname [info script]]
package require ossltest
cd $::test::dir
set testname [file rootname [file tail $::argv0]]

start_tests "Тесты на команду smime" 

test "Creating CA 2001" {
	makeCA ${testname}CA gost2001:A
} 0 1

test "Creating CA 2012" {
	makeCA
} 0 1


if {[info exists env(ALG_LIST)]} {
	set alg_list $env(ALG_LIST)
} else {
	switch -exact [engine_name] {
		"ccore" {set alg_list {gost2001:A gost2001:B gost2001:C gost2012_256:A gost2012_256:B gost2012_256:C gost2012_512:A gost2012_512:B}}
		"open" {set alg_list {gost2001:A gost2001:B gost2001:C gost2012_256:A gost2012_256:B gost2012_256:C gost2012_512:A gost2012_512:B}}
	}
}

foreach alg $alg_list {
	set alg_fn [string map {":" "_"} $alg]
	set username U_smime_$alg_fn
	switch -glob $alg {
		gost2012* {set ::test::ca ${testname}CA-2012
		           set ca_sign_alg hash_with_sign12_512
		          }
		* {set ::test::ca ${testname}CA
		   set ca_sign_alg hash_with_sign01_cp
		  }
	}

test "Creating user with signing key $alg" {
	makeRegisteredUser $username $alg

	if {![file exists $username/req.pem]&&[file exists $username/cert.pem]} {
		file delete $username/cert.pem
	}	
	file exists $username/cert.pem
} 0 1

test -skip {![file exists $username/cert.pem]} -createsfiles [list sign.dat sign_$alg_fn.msg] "Signing a message without cert by $alg" {
	makeFile sign.dat [string repeat "Test data to sign.\n" 100] 
	openssl	"smime -sign -in sign.dat -text -out sign_$alg_fn.msg -signer $username/cert.pem -inkey $username/seckey.pem -nocerts"
	file isfile sign_$alg_fn.msg
} 0 1

test -skip {![file exist sign_$alg_fn.msg]} "Checking micalg param in signed without cert $alg message" {
	regexp -- micalg="[micalg [alg_hash $alg]]" [grep micalg [getFile sign_$alg_fn.msg]]
} 0 1

test -createsfiles sign_$alg_fn.pem -skip {![file exist sign_$alg_fn.msg]} "Extracting PKCS7 from signed without cert $alg message" {
	openssl "smime -pk7out -out sign_$alg_fn.pem -in sign_$alg_fn.msg"
	file isfile sign_$alg_fn.pem
} 0 1


test -skip {![file exists sign_$alg_fn.pem]} "Checking oids in pkcs7 struct" {
	extract_oids sign_$alg_fn.pem PEM
} 0 [mkObjList [hash_long_name $alg] [hash_long_name $alg] "GOST R 34.11-2012 with 256 bit hash" "GOST R 34.11-2012 with 512 bit hash" "GOST R 34.11-94" "GOST 28147-89" [alg_long_name $alg]] 
#[mkObjList [alg_hash $alg] [alg_hash $alg] hash_12_256 hash_12_512 hash_94 crypt89_cc [alg_id $alg]] 
# hash_12_256 hash_12_512 hash_94 crypt89_cc are from sMIMECapabilities

test -skip {![file exists sign_$alg_fn.msg]} "Verifying a message signed with $alg without ca " {
	grep Veri [openssl "smime -verify -text -in sign_$alg_fn.msg -out verified.txt -noverify -certfile $username/cert.pem"]
} 0 "Verification successful
"

test -skip {![file exists sign_$alg_fn.msg]}  "Verifying a message signed with $alg with ca" {
	grep Veri [openssl "smime -verify -text -in sign_$alg_fn.msg -out verified.txt -certfile $username/cert.pem -CAfile $::test::ca/cacert.pem"]
} 0 "Verification successful
"

test -skip {![file exists sign_$alg_fn.msg]} -createsfiles [list bad_$alg_fn.msg verified.txt] "Verifying corrupted messages signed with $alg" {
	set corrupted [getFile sign_$alg_fn.msg]
	set index [string first "Test data" $corrupted ]
	makeFile bad_$alg_fn.msg [string replace $corrupted $index [expr $index+9] "Bad data"]
	grep Verification [openssl "smime -verify -text -in bad_$alg_fn.msg -out verified.txt -noverify -certfile $username/cert.pem"]
} 1 "Verification failure"

test -skip {![file exists $username/cert.pem]}  -createsfiles [list sign.dat sign_c_$alg_fn.msg] "Signing a message by $alg with cert" {
	makeFile sign.dat [string repeat "Test data to sign.\n" 100] 
	openssl	"smime -sign -in sign.dat -crlfeol -text -out sign_c_$alg_fn.msg -signer $username/cert.pem -inkey $username/seckey.pem"
	file isfile sign_c_$alg_fn.msg
} 0 1

test -skip {![file exist sign_c_$alg_fn.msg]} "Checking micalg param in signed with cert $alg message" {
	regexp -- micalg="[micalg [alg_hash $alg]]" [grep micalg [getFile sign_c_$alg_fn.msg]]
} 0 1
 
test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert inside without ca" {
	grep Veri [openssl "smime -verify -text -in sign_c_$alg_fn.msg -out verified.txt -noverify"]
} 0 "Verification successful
"

test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert with ca" {
	grep Veri [openssl "smime -verify -text -in sign_c_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 0 "Verification successful
"
test -skip {![file exists $username/cert.pem]} -createsfiles {sign.dat sign_op_$alg_fn.msg} "Signing a message by $alg with cert using opaque signing" {
	makeFile sign.dat [string repeat "Test data to sign.\n" 100] 
	openssl	"smime -sign -in sign.dat -text -out sign_op_$alg_fn.msg -signer $username/cert.pem -inkey $username/seckey.pem -nodetach"
	file isfile sign_op_$alg_fn.msg
} 0 1

test -createsfiles verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a message signed by $alg having cert inside without ca" {
	grep Veri [openssl "smime -verify -text -in sign_op_$alg_fn.msg -out verified.txt -noverify"]
} 0 "Verification successful
"

test -createsfiles verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a $alg opaque  message with ca" {
	grep Veri [openssl "smime -verify -text -in sign_op_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 0 "Verification successful
"

test -createsfiles broken_op_$alg_fn.msg  -skip {![file exists sign_op_$alg_fn.msg]} "Verifying broken $alg opaque message" {
	set data [getFile sign_op_$alg_fn.msg]
	regexp "(.*)\n\r?\n(.+)" $data match header encoded
	set asnstruct [::base64::decode $encoded]
	makeFile broken_op_$alg_fn.msg "$header\n\n[::base64::encode [regsub -all\
		"Test data" $asnstruct "Best data"]]"
	grep Verification [openssl "smime -verify -text -in broken_op_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 1 "Verification failure"
	

test -createsfiles "sign_det_$alg_fn.msg" -skip {![file exists $username/cert.pem]||![file exists sign.dat]} "Creating detached $alg signature" {
	openssl "smime -sign -binary -in sign.dat -out sign_det_$alg_fn.msg -signer $username/cert.pem -inkey $username/seckey.pem"
	file exists sign_det_$alg_fn.msg
} 0 1

test -skip {![file exist sign_det_$alg_fn.msg]} "Checking micalg param in detached $alg signature" {
	regexp -- micalg="[micalg [alg_hash $alg]]" [grep micalg [getFile sign_det_$alg_fn.msg]]
} 0 1

test -createsfiles sign_det_$alg_fn.pem -skip {![file exist sign_det_$alg_fn.msg]} "Extracting PKCS7 from signed $alg message" {
	openssl "smime -pk7out -out sign_det_$alg_fn.pem -in sign_det_$alg_fn.msg"
	file isfile sign_det_$alg_fn.pem
} 0 1

#We expect cryptocom oids because of cert signed by ca with Cryptocom algs
# Result sequence
# 1. digest 
# 2.  algorithm of CA key
# 3. algorithm of current key
# 4. algorithm of CA key
# 5. digest
# 6. digests from sMIMECapabilities
# 7. encryption from sMIMECapabilities
# 8. algorithm of current key
test -skip {![file exists sign_det_$alg_fn.pem]} "Checking oids in pkcs7 struct" {
	extract_oids sign_det_$alg_fn.pem PEM
} 0 [mkObjList [hash_long_name $alg] [smime_hash_with_sign_long_name $ca_sign_alg] [alg_long_name $alg] [pubkey_long_name $alg] [param_hash_long_name [param_hash $alg]] [smime_hash_with_sign_long_name  $ca_sign_alg] [hash_long_name $alg] "GOST R 34.11-2012 with 256 bit hash" "GOST R 34.11-2012 with 512 bit hash" "GOST R 34.11-94" "GOST 28147-89" [alg_long_name $alg]]

test -skip {![file exists sign_det_$alg_fn.pem]} "Verifying detached $alg signature" {
	grep Veri [openssl "smime -verify -content sign.dat -inform PEM -in sign_det_$alg_fn.pem -out verified.txt -noverify"]
} 0 "Verification successful
"

test -skip {![file exists sign_det_$alg_fn.msg]} -createsfiles {bad.dat} "Verifying corrupted $alg detached signature" {
	makeFile bad.dat [regsub Test [getFile sign.dat] Best]
	grep Verification [openssl "smime -verify -content bad.dat -in sign_det_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 1 "Verification failure"


}
end_tests
