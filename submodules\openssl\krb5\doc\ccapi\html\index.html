<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : Credentials Cache API (CCAPI) Documentation</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>Credentials Cache API (CCAPI) Documentation</h1>
<p>
<h2><a class="anchor" name="toc">
Table of Contents</a></h2>
<ul>
<li><a class="el" href="index.html#introduction">Introduction</a> </li>
<li><a class="el" href="index.html#error_handling">Error Handling</a> </li>
<li><a class="el" href="index.html#synchronization_atomicity">Synchronization and Atomicity</a> </li>
<li><a class="el" href="index.html#memory_management">Object Memory Management</a> </li>
<li><a class="el" href="index.html#opaque_types">Opaque Types</a></li>
</ul>
<ul>
<li><a class="el" href="group__ccapi__constants__reference.html">Constants</a> </li>
<li><a class="el" href="group__ccapi__types__reference.html">Basic Types</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__context__reference.html">cc_context_t Overview</a> </li>
<li><a class="el" href="structcc__context__f.html">cc_context_t Functions</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__ccache__reference.html">cc_ccache_t Overview</a> </li>
<li><a class="el" href="structcc__ccache__f.html">cc_ccache_t Functions</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a> </li>
<li><a class="el" href="structcc__credentials__f.html">cc_credentials_t Functions</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__ccache__iterator__reference.html">cc_ccache_iterator_t Overview</a> </li>
<li><a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_t Functions</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__credentials__iterator__reference.html">cc_credentials_iterator_t</a> </li>
<li><a class="el" href="structcc__credentials__iterator__f.html">cc_credentials_iterator_t Functions</a></li>
</ul>
<ul>
<li><a class="el" href="group__cc__string__reference.html">cc_string_t Overview</a> </li>
<li><a class="el" href="structcc__string__f.html">cc_string_t Functions</a></li>
</ul>
<h2><a class="anchor" name="introduction">
Introduction</a></h2>
This is the specification for an API which provides Credentials Cache services for both Kerberos v5 and v4. The idea behind this API is that multiple Kerberos implementations can share a single collection of credentials caches, mediated by this API specification. On the Mac OS and Microsoft Windows platforms this will allow single-login, even when more than one Kerberos shared library is in use on a particular system.<p>
Abstractly, a credentials cache collection contains one or more credentials caches, or ccaches. A ccache is uniquely identified by its name, which is a string internal to the API and not intended to be presented to users. The user presentable identifier of a ccache is its principal.<p>
Unlike the previous versions of the API, version 3 of the API stores both Kerberos v4 and v5 credentials in the same ccache.<p>
At any given time, one ccache is the "default" ccache. The exact meaning of a default ccache is OS-specific; refer to implementation requirements for details.<h2><a class="anchor" name="error_handling">
Error Handling</a></h2>
All functions of the API return some of the error constants listed FIXME; the exact list of error constants returned by any API function is provided in the function descriptions below.<p>
When returning an error constant other than ccNoError or ccIteratorEnd, API functions never modify any of the values passed in by reference.<h2><a class="anchor" name="synchronization_atomicity">
Synchronization and Atomicity</a></h2>
Every function in the API is atomic. In order to make a series of calls atomic, callers should lock the ccache or cache collection they are working with to advise other callers not to modify that container. Note that advisory locks are per container so even if you have a read lock on the cache collection other callers can obtain write locks on ccaches in that cache collection.<p>
Note that iterators do not iterate over ccaches and credentials atomically because locking ccaches and the cache collection over every iteration would degrade performance considerably under high load. However, iterators do guarantee a consistent view of items they are iterating over. Iterators will never return duplicate entries or skip entries when items are removed or added to the container they are iterating over.<p>
An application can always lock a ccache or the cache collection to guarantee that other callers participating in the advisory locking system do not modify the ccache or cache collection.<p>
Implementations should not use copy-on-write techniques to implement locks because those techniques imply that same parts of the ccache collection remain visible to some callers even though they are not present in the collection, which is a potential security risk. For example, a copy-on-write technique might make a copy of the entire collection when a read lock is acquired, so as to allow the owner of the lock to access the collection in an apparently unmodified state, while also allowing others to make modifications to the collection. However, this would also enable the owner of the lock to indefinitely (until the expiration time) use credentials that have actually been deleted from the collection.<h2><a class="anchor" name="memory_management">
Object Memory Management</a></h2>
The lifetime of an object returned by the API is until release() is called for it. Releasing one object has no effect on existence of any other object. For example, a ccache obtained within a context continue to exist when the context is released.<p>
Every object returned by the API (cc_context_t, cc_ccache_t, cc_ccache_iterator_t, cc_credentials_t, cc_credentials_iterator_t, cc_string_t) is owned by the caller of the API, and it is the responsibility of the caller to call release() for every object to prevent memory leaks.<h2><a class="anchor" name="opaque_types">
Opaque Types</a></h2>
All of the opaque high-level types in CCache API are implemented as structures of function pointers and private data. To perform some operation on a type, the caller of the API has to first obtain an instance of that type, and then call the appropriate function pointer from that instance. For example, to call get_change_time() on a cc_context_t, one would call <a class="el" href="group__cc__context__reference.html#ge4174587d8bb261e32194bbb9585fb82">cc_initialize()</a> which creates a new cc_context_t and then call its get_change_time(), like this:<p>
<div class="fragment"><pre class="fragment"> <a class="code" href="structcc__context__d.html">cc_context_t</a> context;
 <a class="code" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> err = <a class="code" href="group__cc__context__reference.html#ge4174587d8bb261e32194bbb9585fb82">cc_initialize</a> (&amp;context, <a class="code" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55b26f201de4113dda3b4ec78dcda95d5a1">ccapi_version_3</a>, nil, nil);
 <span class="keywordflow">if</span> (err == <a class="code" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>)
 time = context-&gt;<a class="code" href="structcc__context__d.html#ac5b195bc75b92f5c1924e6a3a6aa611">functions</a>-&gt;<a class="code" href="structcc__context__f.html#51bd5a48dcd263bfb3128cc5838b4cd7">get_change_time</a> (context)
</pre></div><p>
All API functions also have convenience preprocessor macros, which make the API seem completely function-based. For example, cc_context_get_change_time (context, time) is equivalent to context-&gt;functions-&gt;get_change_time (context, time). The convenience macros follow the following naming convention:<p>
The API function some_function() <div class="fragment"><pre class="fragment"> cc_type_t an_object;
 result = an_object-&gt;functions-&gt;some_function (opaque_pointer, args)
</pre></div><p>
has an equivalent convenience macro of the form cc_type_some_function(): <div class="fragment"><pre class="fragment"> cc_type_t an_object;
 result = cc_type_some_function (an_object, args)
</pre></div><p>
The specifications below include the names for both the functions and the convenience macros, in that order. For clarity, it is recommended that clients using the API use the convenience macros, but that is merely a stylistic choice.<p>
Implementing the API in this manner allows us to extend and change the interface in the future, while preserving compatibility with older clients.<p>
For example, consider the case when the signature or the semantics of a cc_ccache_t function is changed. The API version number is incremented. The library implementation contains both a function with the old signature and semantics and a function with the new signature and semantics. When a context is created, the API version number used in that context is stored in the context, and therefore it can be used whenever a ccache is created in that context. When a ccache is created in a context with the old API version number, the function pointer structure for the ccache is filled with pointers to functions implementing the old semantics; when a ccache is created in a context with the new API version number, the function pointer structure for the ccache is filled with poitners to functions implementing the new semantics.<p>
Similarly, if a function is added to the API, the version number in the context can be used to decide whether to include the implementation of the new function in the appropriate function pointer structure or not. <hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:05 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
