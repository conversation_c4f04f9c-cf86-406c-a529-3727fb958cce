.. hazmat::

Hash-based message authentication codes (HMAC)
==============================================

.. currentmodule:: cryptography.hazmat.primitives.hmac

.. testsetup::

    import binascii
    key = binascii.unhexlify(b"0" * 32)

Hash-based message authentication codes (or HMACs) are a tool for calculating
message authentication codes using a cryptographic hash function coupled with a
secret key. You can use an HMAC to verify both the integrity and authenticity
of a message.

.. class:: HMAC(key, algorithm, backend=None)

    HMAC objects take a ``key`` and a
    :class:`~cryptography.hazmat.primitives.hashes.HashAlgorithm` instance.
    The ``key`` should be :doc:`randomly generated bytes </random-numbers>` and
    is recommended to be equal in length to the ``digest_size`` of the hash
    function chosen. You must keep the ``key`` secret.

    This is an implementation of :rfc:`2104`.

    .. doctest::

        >>> from cryptography.hazmat.primitives import hashes, hmac
        >>> key = b'test key. Beware! A real key should use os.urandom or TRNG to generate'
        >>> h = hmac.HMAC(key, hashes.SHA256())
        >>> h.update(b"message to hash")
        >>> signature = h.finalize()
        >>> signature
        b'k\xd9\xb29\xefS\xf8\xcf\xec\xed\xbf\x95\xe6\x97X\x18\x9e%\x11DU1\x9fq}\x9a\x9c\xe0)y`='

    If the backend doesn't support the requested ``algorithm`` an
    :class:`~cryptography.exceptions.UnsupportedAlgorithm` exception will be
    raised.

    If ``algorithm`` isn't a
    :class:`~cryptography.hazmat.primitives.hashes.HashAlgorithm` instance
    then ``TypeError`` will be raised.

    To check that a given signature is correct use the :meth:`verify` method.
    You will receive an exception if the signature is wrong:

    .. doctest::

        >>> h = hmac.HMAC(key, hashes.SHA256())
        >>> h.update(b"message to hash")
        >>> h_copy = h.copy() # get a copy of `h' to be reused
        >>> h.verify(signature)
        >>>
        >>> h_copy.verify(b"an incorrect signature")
        Traceback (most recent call last):
        ...
        cryptography.exceptions.InvalidSignature: Signature did not match digest.

    :param key: Secret key as ``bytes``.
    :type key: :term:`bytes-like`
    :param algorithm: An
        :class:`~cryptography.hazmat.primitives.hashes.HashAlgorithm`
        instance such as those described in
        :ref:`Cryptographic Hashes <cryptographic-hash-algorithms>`.
    :param backend: An optional
        :class:`~cryptography.hazmat.backends.interfaces.HMACBackend`
        instance.

    :raises cryptography.exceptions.UnsupportedAlgorithm: This is raised if the
        provided ``backend`` does not implement
        :class:`~cryptography.hazmat.backends.interfaces.HMACBackend`

    .. method:: update(msg)

        :param msg: The bytes to hash and authenticate.
        :type msg: :term:`bytes-like`
        :raises cryptography.exceptions.AlreadyFinalized: See :meth:`finalize`
        :raises TypeError: This exception is raised if ``msg`` is not ``bytes``.

    .. method:: copy()

        Copy this :class:`HMAC` instance, usually so that we may call
        :meth:`finalize` to get an intermediate digest value while we continue
        to call :meth:`update` on the original instance.

        :return: A new instance of :class:`HMAC` that can be updated
            and finalized independently of the original instance.
        :raises cryptography.exceptions.AlreadyFinalized: See :meth:`finalize`

    .. method:: verify(signature)

        Finalize the current context and securely compare digest to
        ``signature``.

        :param bytes signature: The bytes to compare the current digest
                                against.
        :raises cryptography.exceptions.AlreadyFinalized: See :meth:`finalize`
        :raises cryptography.exceptions.InvalidSignature: If signature does not
                                                          match digest
        :raises TypeError: This exception is raised if ``signature`` is not
                           ``bytes``.

    .. method:: finalize()

        Finalize the current context and return the message digest as bytes.

        After ``finalize`` has been called this object can no longer be used
        and :meth:`update`, :meth:`copy`, :meth:`verify` and :meth:`finalize`
        will raise an :class:`~cryptography.exceptions.AlreadyFinalized`
        exception.

        :return bytes: The message digest as bytes.
        :raises cryptography.exceptions.AlreadyFinalized:
