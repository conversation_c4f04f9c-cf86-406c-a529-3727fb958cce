db2-alpha.0 -> db2-alpha.1
		This fixes a number of bugs in the alpha release.
		1. 64-bit functionality.  The test suite now runs on alphas.
		Memory leak fixed.
		Pairs no longer disappear when pages are exactly full.
		Flush meta-data correctly on sync.
1.86 -> db2-alpha.0
		This is an interim release.  We are in the process of
		adding logging, locking, and transaction support to all
		the db access methods.  This will necessitate database
		format changes, interface changes, and major upheaval.
		In the meantime, this PRELIMINARY release is to correct
		some known bugs in the hash pacakge.  This release uses
		a different page format from 1.86, so you will need to
		dump and reload any existing databases if you upgrade
		to this (and may have to do it again when 2.0 becomes
		available.
1.85 -> 1.86
	btree:	Fix to split code for single large record at the end of a
		page.
1.84 -> 1.85
	recno:	#ifdef out use of mmap, it's not portable enough.

1.83 -> 1.84	Thu Aug 18 15:46:07 EDT 1994
	recno:	Rework fixed-length records so that closing and reopening
		the file now works.  Pad short records on input.  Never do
		signed comparison in recno input reading functions.

1.82 -> 1.83	Tue Jul 26 15:33:44 EDT 1994
	btree:	Rework cursor deletion code yet again; bugs with
		deleting empty pages that only contained the cursor
		record.

1.81 -> 1.82	Sat Jul 16 11:01:50 EDT 1994
	btree:	Fix bugs introduced by new cursor/deletion code.
		Replace return kbuf/dbuf with real DBT's.

1.80 -> 1.81
	btree:	Fix bugs introduced by new cursor/deletion code.
	all:	Add #defines for Purify.

1.79 -> 1.80	Wed Jul 13 22:41:54 EDT 1994
	btree	Change deletion to coalesce empty pages.  This is a major
		change, cursors and duplicate pages all had to be reworked.
		Return to a fixed stack.
	recno:	Affected by cursor changes.  New cursor structures should
		permit multiple cursors in the future.

1.78 -> 1.79	Mon Jun 20 17:36:47 EDT 1994
	all:	Minor cleanups of 1.78 for porting reasons; only
		major change was inlining check of NULL pointer
		so that __fix_realloc goes away.

1.77 -> 1.78	Thu Jun 16 19:06:43 EDT 1994
	all:	Move "standard" size typedef's into db.h.

1.76 -> 1.77	Thu Jun 16 16:48:38 EDT 1994
	hash:	Delete __init_ routine, has special meaning to OSF 2.0.

1.74 -> 1.76
	all:	Finish up the port to the Alpha.

1.73 -> 1.74
	recno:	Don't put the record if rec_search fails, in rec_rdelete.
		Create fixed-length intermediate records past "end" of DB
		correctly.
		Realloc bug when reading in fixed records.
	all:	First cut at port to Alpha (64-bit architecture) using
		4.4BSD basic integral types typedef's.
		Cast allocation pointers to shut up old compilers.
		Rework PORT directory into OS/machine directories.

1.72 -> 1.73
	btree:	If enough duplicate records were inserted and then deleted
		that internal pages had references to empty pages of the
		duplicate keys, the search function ended up on the wrong
		page.

1.7  -> 1.72	12 Oct 1993
	hash:	Support NET/2 hash formats.

1.7  -> 1.71	16 Sep 1993
	btree/recno:
		Fix bug in internal search routines that caused
		return of invalid pointers.

1.6  -> 1.7	07 Sep 1993
	hash:	Fixed big key overflow bugs.
	test:	Portability hacks, rewrite test script, Makefile.
	btree/recno:
		Stop copying non-overflow key/data pairs.
	PORT:	Break PORT directory up into per architecture/OS
		subdirectories.

1.5  -> 1.6	06 Jun 1993
	hash:	In PAIRFITS, the first comparison should look at (P)[2].
		The hash_realloc function was walking off the end of memory.
		The overflow page number was wrong when bumping splitpoint.

1.4  -> 1.5	23 May 1993
	hash:	Set hash default fill factor dynamically.
	recno:	Fixed bug in sorted page splits.
		Add page size parameter support.
		Allow recno to specify the name of the underlying btree;
			used for vi recovery.
	btree/recno:
		Support 64K pages.
	btree/hash/recno:
		Provide access to an underlying file descriptor.
		Change sync routines to take a flag argument, recno
			uses this to sync out the underlying btree.

1.3  -> 1.4	10 May 1993
	recno:	Delete the R_CURSORLOG flag from the recno interface.
		Zero-length record fix for non-mmap reads.
		Try and make SIZE_T_MAX test in open portable.

1.2  -> 1.3	01 May 1993
	btree:	Ignore user byte-order setting when reading already
		existing database.  Fixes to byte-order conversions.

1.1  -> 1.2	15 Apr 1993
		No bug fixes, only compatibility hacks.
