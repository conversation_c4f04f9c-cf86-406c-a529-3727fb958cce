=pod

=head1 NAME

PKCS12_decrypt_skey, PKCS12_decrypt_skey_ex - PKCS12 shrouded keyBag
decrypt functions

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 PKCS8_PRIV_KEY_INFO *PKCS12_decrypt_skey(const PKCS12_SAFEBAG *bag,
                                          const char *pass, int passlen);
 PKCS8_PRIV_KEY_INFO *PKCS12_decrypt_skey_ex(const PKCS12_SAFEBAG *bag,
                                             const char *pass, int passlen,
                                             OSSL_LIB_CTX *ctx,
                                             const char *propq);

=head1 DESCRIPTION

PKCS12_decrypt_skey() Decrypt the PKCS#8 shrouded keybag contained within I<bag>
using the supplied password I<pass> of length I<passlen>.

PKCS12_decrypt_skey_ex() is similar to the above but allows for a library context
I<ctx> and property query I<propq> to be used to select algorithm implementations.

=head1 RETURN VALUES

Both functions will return the decrypted key or NULL if an error occurred.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<PKCS8_decrypt_ex(3)>,
L<PKCS8_encrypt_ex(3)>,
L<PKCS12_add_key_ex(3)>,
L<PKCS12_SAFEBAG_create_pkcs8_encrypt_ex(3)>

=head1 HISTORY

PKCS12_decrypt_skey_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
