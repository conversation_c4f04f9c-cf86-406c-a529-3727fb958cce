#pragma warning disable IDE0073
//
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
//
#pragma warning restore IDE0073

#pragma warning disable CS0649

// Polyfill for MemoryMarshal on .NET Standard
#if NETSTANDARD && !NETSTANDARD2_1_OR_GREATER
using MemoryMarshal = Microsoft.Quic.Polyfill.MemoryMarshal;
#else
using MemoryMarshal = System.Runtime.InteropServices.MemoryMarshal;
#endif
