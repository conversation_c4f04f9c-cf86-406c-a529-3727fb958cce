# MsQuic 连接交互逻辑分析

本文档分析了MsQuic项目中连接(Connection)相关的核心代码和执行逻辑，包括关键数据结构、状态管理和函数实现。

## 1. 连接核心数据结构

### QUIC_CONNECTION 结构

`QUIC_CONNECTION`是MsQuic中表示QUIC连接的核心数据结构，定义在`connection.h`中。该结构包含了连接的所有状态信息和管理逻辑。主要组成部分包括：

- **基本信息**：引用计数、连接状态、工作线程ID等
- **标识信息**：服务器ID、分区ID等
- **连接ID管理**：源CID、目标CID列表
- **路径管理**：活跃路径和备用路径
- **定时器**：各类定时器的过期时间
- **数据包处理**：接收队列、操作队列
- **流管理**：流集合(QUIC_STREAM_SET)CXPLAT_DATAPATH_PARTITION
- **拥塞控制**：拥塞控制状态(QUIC_CONGESTION_CONTROL)
- **丢包检测**：丢包检测逻辑(QUIC_LOSS_DETECTION)
- **加密状态**：不同加密级别的数据包空间
- **发送管理**：发送控制(QUIC_SEND)和发送缓冲区(QUIC_SEND_BUFFER)
- **数据报管理**：数据报相关逻辑(QUIC_DATAGRAM)
- **统计信息**：连接统计数据(QUIC_CONN_STATS)

### QUIC_CONNECTION_STATE 结构

`QUIC_CONNECTION_STATE`是一个位域联合体，用于跟踪连接的各种状态标志，如是否已分配、是否已启动、是否处于握手状态等。

### QUIC_CONN_STATS 结构
`QUIC_CONN_STATS` 是 connection 的统计数据，按照不同层级和业务种类，定义各种统计字段。

### QUIC_WORKER 结构

`QUIC_WORKER`结构定义在`worker.h`中，负责处理连接上的操作。每个连接都会被分配给一个工作线程，该线程负责处理连接上的所有操作。

## 2. 连接生命周期管理

### 连接创建

```c
QUIC_STATUS
QuicConnAlloc(
    _In_ QUIC_REGISTRATION* Registration,
    _In_opt_ QUIC_WORKER* Worker,
    _In_opt_ const QUIC_RX_PACKET* Packet,
    _Outptr_ QUIC_CONNECTION** NewConnection
    )
```

此函数负责分配和初始化一个新的QUIC连接。主要步骤包括：

1. 分配连接结构内存
2. 初始化基本字段和引用计数
3. 初始化各个子模块(流、发送缓冲区、拥塞控制等)
4. 根据是客户端还是服务器设置不同的初始状态

### 连接状态转换

连接状态转换由`QuicConnUpdateState`函数管理，它处理连接从一个状态到另一个状态的转换，并触发相应的回调和清理操作。

### 连接关闭

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicConnCloseLocally(
    _In_ QUIC_CONNECTION* Connection,
    _In_ uint32_t Flags,
    _In_ uint64_t ErrorCode,
    _In_opt_z_ const char* ErrorMsg
    )
```

此函数处理本地发起的连接关闭，包括发送CONNECTION_CLOSE帧和清理资源。

## 3. 连接与工作线程交互

### 连接分配给工作线程

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QuicWorkerAssignConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection
    )
```

此函数将连接分配给指定的工作线程，建立连接和工作线程之间的关联。

### 连接操作队列

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QuicWorkerQueueConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection
    )
```

此函数将连接排队到工作线程的连接队列中，等待工作线程处理。

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QuicWorkerQueueOperation(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_OPERATION* Operation
    )
```

此函数将操作排队到工作线程的操作队列中，等待工作线程处理。

<!-- ## 3. connect发包主要流程与worker线程调度 -->

MsQuic中，应用层发起连接请求（如MsQuicConnectionOpen/MsQuicConnectionStart）后，会生成一个连接对象（QUIC_CONNECTION），并将连接操作（如QUIC_OPERATION_CONNECT）投递到worker线程队列。worker线程负责轮询和处理这些operation，最终完成连接的初始化和发包。

### 3.1 Worker线程调度与Operation分发

- 应用层调用MsQuicConnectionStart后，内部会调用QuicConnStart，将QUIC_OPERATION_CONNECT操作投递到对应的worker线程队列（QuicWorkerQueueOperation）。
- Worker线程主循环（WorkerThreadProc）不断从队列中取出operation，调用QuicOperationProcess进行分发。
- 对于QUIC_OPERATION_CONNECT类型，分发到QuicConnHandleConnect。

### 3.2 Connect处理与发包流程

- QuicConnHandleConnect负责初始化连接参数、生成Initial包等。
- 调用QuicSendFlush进行包构建和发送。
- QuicSendFlush会根据连接状态、流控等构建QUIC包，通过QuicPacketBuilderFinalize完成包封装。
- 最终调用底层的QuicDatapathSend，通过UDP套接字发送数据包。

### 3.3 典型发送函数调用堆栈

```
WorkerThreadProc
  → QuicWorkerProcessConnection
    → QuicConnDrainOperations
      → QuicSendFlush           
        → QuicPacketBuilderFinalize
          → QuicPacketBuilderSendBatch
             → QuicBindingSend
                 → CxPlatSocketSend

        QuicSendFlush
        → QuicSendPathChallenges
         → QuicPacketBuilderFinalize
          → QuicPacketBuilderSendBatch
             → QuicBindingSend
                 → CxPlatSocketSend
```

### 3.4 小结

整个connect发包流程体现了MsQuic的异步事件驱动模型，worker线程负责调度和处理所有operation，connect操作经过一系列函数调用，最终完成QUIC包的构建和发送。

## 4. 数据包接收和处理

### 数据包接收路径

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QuicConnRecvPacket(
    _In_ QUIC_CONNECTION* Connection,
    _In_ QUIC_RX_PACKET* Packet
    )
```

此函数是连接接收数据包的入口点，负责将接收到的数据包加入连接的接收队列，并在必要时唤醒工作线程。

### 数据包处理

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicConnProcessPackets(
    _In_ QUIC_CONNECTION* Connection
    )
```

此函数处理连接接收队列中的所有数据包，包括解密、验证和分发到相应的处理逻辑。

## 5. 发送管理

### 发送控制

`QUIC_SEND`结构管理连接的发送状态，包括下一个数据包编号、流量控制窗口、发送标志等。

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicConnSendFlush(
    _In_ QUIC_CONNECTION* Connection
    )
```

此函数刷新连接的发送队列，将待发送的数据包发送出去。

### 拥塞控制

`QUIC_CONGESTION_CONTROL`结构管理连接的拥塞控制状态，实现了BBR和Cubic等拥塞控制算法。

## 6. 流管理

`QUIC_STREAM_SET`结构管理连接上的所有流，包括流的创建、关闭和数据传输。

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
QuicStreamOpen(
    _In_ QUIC_CONNECTION* Connection,
    _In_ QUIC_STREAM_OPEN_FLAGS Flags,
    _In_ QUIC_STREAM_CALLBACK_HANDLER Handler,
    _In_opt_ void* Context,
    _Outptr_ QUIC_STREAM** NewStream
    )
```

此函数创建一个新的流，并将其添加到连接的流集合中。

## 7. 定时器管理

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicConnTimerSet(
    _Inout_ QUIC_CONNECTION* Connection,
    _In_ QUIC_CONN_TIMER_TYPE Type,
    _In_ uint64_t DelayUs
    )

_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicConnTimerSetEx(
    _Inout_ QUIC_CONNECTION* Connection,
    _In_ QUIC_CONN_TIMER_TYPE Type,
    _In_ uint64_t DelayUs,
    _In_ uint64_t TimeNow
    );
```

此函数设置连接的定时器，用于处理各种超时事件，如空闲超时、丢包重传等。

## 8. 关键交互流程

### 连接建立流程

1. 客户端通过`MsQuicConnectionOpen`创建连接
2. 客户端通过`MsQuicConnectionStart`启动连接
3. 客户端发送Initial包，包含ClientHello
4. 服务器接收Initial包，创建连接并分配工作线程
5. 服务器发送Initial包，包含ServerHello和加密参数
6. TLS握手完成后，连接进入已建立状态

### 数据传输流程

1. 应用程序通过`MsQuicStreamOpen`创建流
2. 应用程序通过`MsQuicStreamSend`发送数据
3. 数据被分割成帧并加入发送队列
4. 工作线程调用`QuicConnSendFlush`将数据发送出去
5. 接收方接收数据包并通过流回调通知应用程序

### 连接关闭流程

1. 应用程序通过`MsQuicConnectionShutdown`关闭连接
2. 连接发送CONNECTION_CLOSE帧
3. 连接进入关闭状态，开始清理资源
4. 连接引用计数降为0后，连接被释放

### 关键执行函数

`QuicWorkerProcessConnection` connection 执行函数,在worker中调用.

`QuicConnDrainOperations`, --- oper 执行函数，遍历`operQ` 队列。
`QuicConnProcessApiOperation`

## 9. 连接状态图

```mermaid
stateDiagram-v2
    [*] --> 已分配: QuicConnAlloc
    已分配 --> 已启动: MsQuicConnectionStart
    已启动 --> 握手中: 发送Initial包
    握手中 --> 已建立: TLS握手完成
    已建立 --> 数据传输: 创建流并发送数据
    数据传输 --> 数据传输: 数据包处理循环
    数据传输 --> 关闭中: MsQuicConnectionShutdown
    已建立 --> 关闭中: MsQuicConnectionShutdown
    握手中 --> 关闭中: 握手失败/超时
    关闭中 --> 已关闭: 发送CONNECTION_CLOSE
    已关闭 --> [*]: 引用计数为0
    
    note right of 已分配
        连接结构已分配内存
        初始化基本字段和引用计数
        初始化各子模块
    end note
    
    note right of 握手中
        客户端: 发送ClientHello
        服务器: 发送ServerHello
        交换加密参数
    end note
    
    note right of 已建立
        加密通道已建立
        可以开始创建流和传输数据
    end note
    
    note right of 数据传输
        通过QuicStreamOpen创建流
        通过QuicStreamSend发送数据
        通过QuicConnRecvPacket接收数据
        通过QuicConnProcessPackets处理数据
    end note
    
    note right of 关闭中
        本地或远程发起关闭
        发送CONNECTION_CLOSE帧
        开始清理资源
    end note
```

## 10. 连接与工作线程交互图

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Conn as QUIC_CONNECTION
    participant Worker as QUIC_WORKER
    participant Peer as 对端
    
    App->>Conn: MsQuicConnectionOpen
    Conn->>Worker: QuicWorkerAssignConnection
    App->>Conn: MsQuicConnectionStart
    Conn->>Worker: QuicWorkerQueueConnection
    Worker->>Conn: 处理连接操作
    Conn->>Peer: 发送Initial包(ClientHello)
    Peer->>Conn: 发送Initial包(ServerHello)
    Conn->>Worker: QuicWorkerQueueOperation
    Worker->>Conn: 处理TLS握手
    Conn->>App: 连接建立回调
    
    App->>Conn: MsQuicStreamOpen
    App->>Conn: MsQuicStreamSend
    Conn->>Worker: QuicWorkerQueueOperation
    Worker->>Conn: QuicConnSendFlush
    Conn->>Peer: 发送数据包
    
    Peer->>Conn: 发送数据包
    Conn->>Worker: QuicConnRecvPacket
    Worker->>Conn: QuicConnProcessPackets
    Conn->>App: 流数据回调
    
    App->>Conn: MsQuicConnectionShutdown
    Conn->>Worker: QuicWorkerQueueOperation
    Worker->>Conn: QuicConnCloseLocally
    Conn->>Peer: 发送CONNECTION_CLOSE帧
    Conn->>App: 连接关闭回调
```

## 11. 总结

MsQuic的连接实现是一个复杂而完整的系统，它通过工作线程模型实现了高效的并发处理，通过状态机管理连接的生命周期，通过各种子模块实现了QUIC协议的各个功能。连接是MsQuic的核心组件，它将各个功能模块组织在一起，实现了完整的QUIC协议栈。

通过上述状态图和交互图，我们可以清晰地看到QUIC连接的生命周期和状态转换过程，以及连接与工作线程之间的交互关系。这些图表有助于理解MsQuic的连接管理机制和数据处理流程。