=pod

=head1 NAME

SSL_CTX_config, SSL_config - configure SSL_CTX or SSL structure

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_config(SSL_CTX *ctx, const char *name);
 int SSL_config(SSL *s, const char *name);

=head1 DESCRIPTION

The functions SSL_CTX_config() and SSL_config() configure an B<SSL_CTX> or
B<SSL> structure using the configuration B<name>.

=head1 NOTES

By calling SSL_CTX_config() or SSL_config() an application can perform many
complex tasks based on the contents of the configuration file: greatly
simplifying application configuration code. A degree of future proofing
can also be achieved: an application can support configuration features
in newer versions of OpenSSL automatically.

A configuration file must have been previously loaded, for example using
CONF_modules_load_file(). See L<config(5)> for details of the configuration
file syntax.

=head1 RETURN VALUES

SSL_CTX_config() and SSL_config() return 1 for success or 0 if an error
occurred.

=head1 EXAMPLES

If the file "config.cnf" contains the following:

 testapp = test_sect

 [test_sect]
 # list of configuration modules

 ssl_conf = ssl_sect

 [ssl_sect]
 server = server_section

 [server_section]
 RSA.Certificate = server-rsa.pem
 ECDSA.Certificate = server-ecdsa.pem
 Ciphers = ALL:!RC4

An application could call:

 if (CONF_modules_load_file("config.cnf", "testapp", 0) <= 0) {
     fprintf(stderr, "Error processing config file\n");
     goto err;
 }

 ctx = SSL_CTX_new(TLS_server_method());

 if (SSL_CTX_config(ctx, "server") == 0) {
     fprintf(stderr, "Error configuring server.\n");
     goto err;
 }

In this example two certificates and the cipher list are configured without
the need for any additional application code.

=head1 SEE ALSO

L<config(5)>,
L<SSL_CONF_cmd(3)>,
L<CONF_modules_load_file(3)>

=head1 HISTORY

The SSL_CTX_config() and SSL_config() functions were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2015-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
