


          
# MsQuic Packet 实现分析

## 1. Packet 概述

QUIC 协议中的 Packet（数据包）是网络通信的基本单位，MsQuic 实现了 QUIC 协议规范中定义的各种类型的数据包。在 MsQuic 中，packet 相关的实现主要分布在 `packet.c` 和 `packet_builder.c` 两个文件中，前者负责数据包的验证、解析和处理，后者负责数据包的构建和发送。

## 2. Packet 核心数据结构

### 2.1 接收数据包结构 (QUIC_RX_PACKET)

`QUIC_RX_PACKET` 是表示接收到的 QUIC 数据包的核心数据结构，包含了数据包的所有信息：

- **基本信息**：接收时间、可用缓冲区长度等
- **包头信息**：包头长度、是否为短头包等
- **连接 ID**：目标和源连接 ID 及其长度
- **负载信息**：负载长度、负载缓冲区指针等
- **验证状态**：是否已验证包头等

### 2.2 发送数据包构建器 (QUIC_PACKET_BUILDER)

`QUIC_PACKET_BUILDER` 是负责构建发送数据包的核心数据结构：

- **连接引用**：指向所属连接的指针
- **路径信息**：指向使用的路径的指针
- **包类型信息**：包类型、加密级别等
- **数据报信息**：当前数据报、数据报长度等
- **包头信息**：包头长度、包号长度等
- **加密信息**：加密密钥、加密开销等
- **批处理信息**：批处理 ID、批处理计数等

### 2.3 数据包类型

MsQuic 支持 QUIC 协议中定义的各种数据包类型：

- **长头包**：
  - Initial（初始包）
  - Handshake（握手包）
  - 0-RTT（零往返时间包）
  - Retry（重试包）
- **短头包**：
  - 1-RTT（一往返时间包）

## 3. Packet 构建与发送流程

### 3.1 数据包构建流程

数据包构建的主要流程如下：

1. **初始化构建器**：调用 `QuicPacketBuilderInitialize` 初始化 `QUIC_PACKET_BUILDER` 结构
2. **准备构建**：调用 `QuicPacketBuilderPrepare` 准备构建特定类型的数据包
3. **添加帧**：调用各种帧添加函数将帧添加到数据包中
4. **完成构建**：调用 `QuicPacketBuilderFinalize` 完成数据包构建
5. **发送数据包**：调用 `QuicPacketBuilderSendBatch` 发送构建好的数据包
6. **清理构建器**：调用 `QuicPacketBuilderCleanup` 清理构建器资源

### 3.2 数据包发送限制条件

数据包发送受到多种限制条件：

- **MTU 限制**：数据包大小不能超过路径 MTU
- **拥塞控制限制**：发送量受拥塞控制算法限制
- **流量控制限制**：发送量受流量控制窗口限制
- **放大保护限制**：服务器在验证客户端地址前，发送量受限制
- **对端限制**：对端通过传输参数设置的限制

### 3.3 数据包构建关键函数

```c
// 初始化数据包构建器
BOOLEAN QuicPacketBuilderInitialize(
    _Inout_ QUIC_PACKET_BUILDER* Builder,
    _In_ QUIC_CONNECTION* Connection,
    _In_ QUIC_PATH* Path);

// 准备构建数据包
BOOLEAN QuicPacketBuilderPrepare(
    _Inout_ QUIC_PACKET_BUILDER* Builder,
    _In_ QUIC_PACKET_KEY_TYPE NewPacketKeyType,
    _In_ BOOLEAN IsTailLossProbe,
    _In_ BOOLEAN IsPathMtuDiscovery);

// 完成数据包构建
void QuicPacketBuilderFinalize(
    _Inout_ QUIC_PACKET_BUILDER* Builder,
    _In_ BOOLEAN FlushBatchedDatagrams);

// 发送批处理的数据包
void QuicPacketBuilderSendBatch(
    _Inout_ QUIC_PACKET_BUILDER* Builder);

// 清理数据包构建器
void QuicPacketBuilderCleanup(
    _Inout_ QUIC_PACKET_BUILDER* Builder);
```

## 4. Packet 接收与处理流程

### 4.1 数据包接收流程

数据包接收的主要流程如下：

1. **接收 UDP 数据报**：通过 UDP 套接字接收数据报
2. **解析数据报**：将数据报解析为一个或多个 QUIC 数据包
3. **验证数据包**：验证数据包的有效性
4. **处理数据包**：根据数据包类型进行相应处理
5. **解密数据包**：使用相应的密钥解密数据包
6. **处理帧**：处理数据包中的各种帧

### 4.2 数据包验证关键函数

```c
// 验证数据包不变部分
BOOLEAN QuicPacketValidateInvariant(
    _In_ const void* Owner,
    _Inout_ QUIC_RX_PACKET* Packet,
    _In_ BOOLEAN IsBindingShared);

// 验证长头数据包
BOOLEAN QuicPacketValidateLongHeaderV1(
    _In_ const void* Owner,
    _In_ BOOLEAN IsServer,
    _Inout_ QUIC_RX_PACKET* Packet,
    _Outptr_result_buffer_maybenull_(*TokenLength) const uint8_t** Token,
    _Out_ uint16_t* TokenLength,
    _In_ BOOLEAN IgnoreFixedBit);
```

## 5. 发送与接收的差异

### 5.1 发送时的数据包构建

发送时，数据包构建过程是主动的，由应用程序或协议栈触发：

1. 应用程序调用发送 API
2. 协议栈将数据分帧并加入发送队列
3. 工作线程调用 `QuicConnSendFlush` 触发发送
4. 创建 `QUIC_PACKET_BUILDER` 并构建数据包
5. 加密数据包并发送

### 5.2 接收时的数据包处理

接收时，数据包处理过程是被动的，由网络事件触发：

1. UDP 套接字接收到数据报
2. 数据报被解析为 QUIC 数据包
3. 验证数据包的有效性
4. 查找对应的连接或创建新连接
5. 解密数据包并处理其中的帧

## 6. Packet 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 创建构建器: QuicPacketBuilderInitialize
    创建构建器 --> 准备构建: QuicPacketBuilderPrepare
    准备构建 --> 添加帧: QuicStreamSendFrames等
    添加帧 --> 完成构建: QuicPacketBuilderFinalize
    完成构建 --> 发送数据包: QuicPacketBuilderSendBatch
    发送数据包 --> 清理构建器: QuicPacketBuilderCleanup
    清理构建器 --> [*]
    
    准备构建 --> 准备构建: 添加更多帧
    完成构建 --> 准备构建: 构建新数据包
```

## 7. Packet 处理流程图

```mermaid
flowchart TD
    A[接收UDP数据报] --> B[解析数据报为QUIC数据包]
    B --> C{验证数据包}
    C -->|无效| D[丢弃数据包]
    C -->|有效| E{查找连接}
    E -->|找到| F[解密数据包]
    E -->|未找到| G[创建新连接]
    G --> F
    F --> H[处理数据包中的帧]
    H --> I[更新连接状态]
    
    J[应用程序发送数据] --> K[将数据分帧]
    K --> L[创建数据包构建器]
    L --> M[准备构建数据包]
    M --> N[添加帧到数据包]
    N --> O[完成数据包构建]
    O --> P[发送数据包]
    P --> Q[清理构建器]
```

## 8. 数据包大小限制

QUIC 数据包的大小受到多种因素的限制：

1. **路径 MTU**：默认为 1500 字节，可通过 PMTUD 发现更大的 MTU
2. **初始包最小大小**：客户端初始包必须至少为 1200 字节
3. **对端最大 UDP 负载大小**：对端通过传输参数指定的最大 UDP 负载大小
4. **放大保护限制**：服务器在验证客户端地址前，发送量不能超过接收量的 3 倍

## 9. 关键代码分析

### 9.1 数据包构建器初始化

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != FALSE)
BOOLEAN
QuicPacketBuilderInitialize(
    _Inout_ QUIC_PACKET_BUILDER* Builder,
    _In_ QUIC_CONNECTION* Connection,
    _In_ QUIC_PATH* Path
    )
{
    // 初始化基本字段
    Builder->Connection = Connection;
    Builder->Path = Path;
    Builder->PacketBatchSent = FALSE;
    Builder->PacketBatchRetransmittable = FALSE;
    Builder->WrittenConnectionCloseFrame = FALSE;
    Builder->Metadata = &Builder->MetadataStorage.Metadata;
    Builder->EncryptionOverhead = CXPLAT_ENCRYPTION_OVERHEAD;
    Builder->TotalDatagramsLength = 0;

    // 获取源连接 ID
    if (Connection->SourceCids.Next == NULL) {
        return FALSE;
    }
    Builder->SourceCid = CXPLAT_CONTAINING_RECORD(
        Connection->SourceCids.Next, QUIC_CID_HASH_ENTRY, Link);

    // 计算发送限额
    uint64_t TimeNow = CxPlatTimeUs64();
    uint64_t TimeSinceLastSend;
    if (Connection->Send.LastFlushTimeValid) {
        TimeSinceLastSend = CxPlatTimeDiff64(Connection->Send.LastFlushTime, TimeNow);
    } else {
        TimeSinceLastSend = 0;
    }
    Builder->SendAllowance = QuicCongestionControlGetSendAllowance(
        &Connection->CongestionControl, TimeSinceLastSend, Connection->Send.LastFlushTimeValid);
    if (Builder->SendAllowance > Path->Allowance) {
        Builder->SendAllowance = Path->Allowance;
    }
    Connection->Send.LastFlushTime = TimeNow;
    Connection->Send.LastFlushTimeValid = TRUE;

    return TRUE;
}
```

### 9.2 数据包验证

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != FALSE)
BOOLEAN
QuicPacketValidateInvariant(
    _In_ const void* Owner,
    _Inout_ QUIC_RX_PACKET* Packet,
    _In_ BOOLEAN IsBindingShared
    )
{
    // 验证数据包长度
    if (Packet->AvailBufferLength == 0 ||
        Packet->AvailBufferLength < QuicMinPacketLengths[Packet->Invariant->IsLongHeader]) {
        return FALSE;
    }

    // 处理长头包
    if (Packet->Invariant->IsLongHeader) {
        Packet->IsShortHeader = FALSE;
        // 验证目标连接 ID
        DestCidLen = Packet->Invariant->LONG_HDR.DestCidLength;
        if (Packet->AvailBufferLength < MIN_INV_LONG_HDR_LENGTH + DestCidLen) {
            return FALSE;
        }
        DestCid = Packet->Invariant->LONG_HDR.DestCid;
        // 验证源连接 ID
        SourceCidLen = *(DestCid + DestCidLen);
        Packet->HeaderLength = MIN_INV_LONG_HDR_LENGTH + DestCidLen + SourceCidLen;
        if (Packet->AvailBufferLength < Packet->HeaderLength) {
            return FALSE;
        }
        SourceCid = DestCid + sizeof(uint8_t) + DestCidLen;
    } else {
        // 处理短头包
        Packet->IsShortHeader = TRUE;
        DestCidLen = IsBindingShared ? MsQuicLib.CidTotalLength : 0;
        SourceCidLen = 0;
        Packet->HeaderLength = sizeof(uint8_t) + DestCidLen;
        if (Packet->AvailBufferLength < Packet->HeaderLength) {
            return FALSE;
        }
        DestCid = Packet->Invariant->SHORT_HDR.DestCid;
        SourceCid = NULL;
    }

    // 缓存或验证连接 ID
    if (Packet->DestCid != NULL) {
        // 验证连接 ID 是否匹配
        if (Packet->DestCidLen != DestCidLen ||
            memcmp(Packet->DestCid, DestCid, DestCidLen) != 0) {
            return FALSE;
        }
        if (!Packet->IsShortHeader) {
            if (Packet->SourceCidLen != SourceCidLen ||
                memcmp(Packet->SourceCid, SourceCid, SourceCidLen) != 0) {
                return FALSE;
            }
        }
    } else {
        // 缓存连接 ID
        Packet->DestCidLen = DestCidLen;
        Packet->SourceCidLen = SourceCidLen;
        Packet->DestCid = DestCid;
        Packet->SourceCid = SourceCid;
    }

    Packet->ValidatedHeaderInv = TRUE;
    return TRUE;
}
```

## 10. Packet 数据格式

QUIC 协议定义了两种主要的数据包格式：长头包（Long Header Packet）和短头包（Short Header Packet）。这两种格式在 MsQuic 中都有实现。

### 10.1 长头包格式

长头包主要用于连接建立阶段，包含以下字段：

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+
|1|  Type (7)   |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                         Version (32)                           |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| DCID Len (8)  |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|               Destination Connection ID (0..160)             ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| SCID Len (8)  |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                 Source Connection ID (0..160)                ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                       Payload (*)                            ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

**字段说明**：
- **首字节**：最高位为1，表示长头包，后7位表示包类型
- **Version**：QUIC 协议版本号
- **DCID Len**：目标连接 ID 长度
- **Destination Connection ID**：目标连接 ID
- **SCID Len**：源连接 ID 长度
- **Source Connection ID**：源连接 ID
- **Payload**：数据包负载，包含加密的帧

**长头包类型**：
- **Initial (0x00)**：初始包，用于开始连接
- **0-RTT (0x01)**：零往返时间包，用于在握手完成前发送应用数据
- **Handshake (0x02)**：握手包，用于完成加密握手
- **Retry (0x03)**：重试包，用于服务器要求客户端重新发送 Initial 包

### 10.2 短头包格式

短头包用于连接建立后的数据传输，格式更为简洁：

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+
|0|1|S|R|R|K|P P|
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                Destination Connection ID (*)                 ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                      Packet Number (*)                       ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                        Protected Payload (*)                 ...
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

**字段说明**：
- **首字节**：最高位为0，表示短头包
  - 第1位固定为1（固定位）
  - S位：表示是否使用自旋位
  - R位：保留位
  - K位：表示是否有包头保护密钥阶段
  - P位：包号长度，00=1字节，01=2字节，10=3字节，11=4字节
- **Destination Connection ID**：目标连接 ID
- **Packet Number**：包号，长度由首字节中的P位决定
- **Protected Payload**：受保护的负载，包含加密的帧

### 10.3 数据包负载

数据包负载由一系列帧（Frame）组成，每个帧都有特定的类型和格式。MsQuic 支持 QUIC 协议定义的所有帧类型：

| 帧类型 | 名称 | 描述 |
|--------|------|------|
| 0x00 | PADDING | 填充帧，不包含任何数据 |
| 0x01 | PING | 用于保持连接活跃或测量RTT |
| 0x02-0x03 | ACK | 确认帧，用于确认收到的数据包 |
| 0x04 | RESET_STREAM | 重置流帧，用于终止流 |
| 0x05 | STOP_SENDING | 停止发送帧，请求对端停止在特定流上发送数据 |
| 0x06 | CRYPTO | 加密帧，用于传输TLS握手消息 |
| 0x07 | NEW_TOKEN | 新令牌帧，用于服务器提供地址验证令牌 |
| 0x08-0x0f | STREAM | 流帧，用于传输应用数据 |
| 0x10 | MAX_DATA | 最大数据帧，用于流量控制 |
| 0x11 | MAX_STREAM_DATA | 最大流数据帧，用于流级流量控制 |
| 0x12-0x13 | MAX_STREAMS | 最大流数帧，限制可以创建的流数量 |
| 0x14 | DATA_BLOCKED | 数据阻塞帧，表示因流量控制而阻塞 |
| 0x15 | STREAM_DATA_BLOCKED | 流数据阻塞帧，表示流级流量控制阻塞 |
| 0x16-0x17 | STREAMS_BLOCKED | 流阻塞帧，表示无法创建更多流 |
| 0x18 | NEW_CONNECTION_ID | 新连接ID帧，提供新的连接ID |
| 0x19 | RETIRE_CONNECTION_ID | 废弃连接ID帧，废弃不再使用的连接ID |
| 0x1a | PATH_CHALLENGE | 路径挑战帧，用于路径验证 |
| 0x1b | PATH_RESPONSE | 路径响应帧，响应路径挑战 |
| 0x1c-0x1d | CONNECTION_CLOSE | 连接关闭帧，用于关闭连接 |
| 0x1e | HANDSHAKE_DONE | 握手完成帧，表示握手已完成 |

### 10.4 MsQuic 中的数据包表示

在 MsQuic 中，数据包格式通过以下结构体表示：

```c
// 长头包格式
typedef struct QUIC_LONG_HEADER_V1 {
    uint8_t PnLength        : 2;
    uint8_t Reserved        : 2;    // 必须为0
    uint8_t Type            : 2;
    uint8_t FixedBit        : 1;    // 必须为1
    uint8_t IsLongHeader    : 1;    // 必须为1
    uint32_t Version;
    uint8_t DestCidLength;
    uint8_t DestCid[0];
    // 变长字段：
    // uint8_t SourceCidLength;
    // uint8_t SourceCid[SourceCidLength];
    // 根据包类型的其他字段
    // 变长包号
    // 加密负载
} QUIC_LONG_HEADER_V1;

// 短头包格式
typedef struct QUIC_SHORT_HEADER_V1 {
    uint8_t PnLength        : 2;
    uint8_t KeyPhase        : 1;
    uint8_t Reserved        : 2;    // 必须为0
    uint8_t SpinBit         : 1;
    uint8_t FixedBit        : 1;    // 必须为1
    uint8_t IsLongHeader    : 1;    // 必须为0
    uint8_t DestCid[0];
    // 变长包号
    // 加密负载
} QUIC_SHORT_HEADER_V1;
```

### 10.5 数据包加密

QUIC 数据包的负载部分是加密的，使用 TLS 1.3 派生的密钥。MsQuic 实现了以下加密级别：

1. **Initial 加密**：使用从客户端目标连接 ID 派生的密钥
2. **Handshake 加密**：使用 TLS 握手交换的密钥
3. **1-RTT 加密**：使用 TLS 握手完成后的应用数据密钥
4. **0-RTT 加密**：使用从之前会话恢复的密钥

数据包头部也受到保护，使用头部保护算法（Header Protection）来混淆包号和某些标志位，防止中间设备根据这些信息进行流量分析。

### 10.6 数据包编码示例

以下是 MsQuic 中编码 Initial 包的简化示例：

```c
// 编码 Initial 包头
uint16_t
QuicPacketEncodeLongHeaderV1(
    _In_ const QUIC_PACKET_KEY* PacketKey,
    _In_ QUIC_LONG_HEADER_TYPE Type,
    _In_ const QUIC_CID* const DestCid,
    _In_ const QUIC_CID* const SourceCid,
    _In_ uint16_t TokenLength,
    _In_reads_opt_(TokenLength)
        const uint8_t* const Token,
    _In_ uint32_t PacketNumber,
    _In_ uint16_t BufferLength,
    _Out_ uint16_t* PayloadLengthOffset,
    _Out_ uint8_t* Buffer,
    _Out_ uint16_t* HeaderLength
    )
{
    // 设置首字节
    uint8_t FirstByte =
        0xC0 | // 长头包标志（最高位为1，固定位为1）
        (uint8_t)(Type << 4) | // 包类型
        (PacketNumber > 0xFFFFFF ? 3 : (PacketNumber > 0xFFFF ? 2 : (PacketNumber > 0xFF ? 1 : 0))); // 包号长度
    
    // 写入首字节和版本号
    Buffer[0] = FirstByte;
    QuicUint32ToNetworkBytes(QUIC_VERSION_LATEST, Buffer + 1);
    
    // 写入目标连接 ID
    Buffer[5] = DestCid->Length;
    memcpy(Buffer + 6, DestCid->Data, DestCid->Length);
    
    // 写入源连接 ID
    Buffer[6 + DestCid->Length] = SourceCid->Length;
    memcpy(Buffer + 7 + DestCid->Length, SourceCid->Data, SourceCid->Length);
    
    // 写入令牌（如果有）
    uint16_t Offset = 7 + DestCid->Length + SourceCid->Length;
    if (TokenLength > 0) {
        QuicVarIntEncode(TokenLength, Buffer + Offset, &Offset);
        memcpy(Buffer + Offset, Token, TokenLength);
        Offset += TokenLength;
    } else {
        QuicVarIntEncode(0, Buffer + Offset, &Offset);
    }
    
    // 负载长度位置
    *PayloadLengthOffset = Offset;
    Offset += 2; // 预留两字节给负载长度
    
    // 写入包号
    QuicPacketEncodePacketNumber(PacketNumber, FirstByte & 0x03, Buffer + Offset);
    Offset += 1 + (FirstByte & 0x03);
    
    *HeaderLength = Offset;
    return Offset;
}
```

这个示例展示了 MsQuic 如何按照 QUIC 协议规范编码长头包的过程，包括设置首字节、写入版本号、连接 ID、令牌和包号等字段。

通过了解 QUIC 数据包的格式和编码过程，我们可以更好地理解 MsQuic 的数据包处理机制和网络通信流程。


## 11. 总结

MsQuic 的 Packet 实现是 QUIC 协议的核心组件，它通过 `packet.c` 和 `packet_builder.c` 两个文件实现了 QUIC 数据包的接收处理和发送构建功能。数据包构建器 `QUIC_PACKET_BUILDER` 负责构建和发送数据包，而接收到的数据包通过 `QUIC_RX_PACKET` 结构表示和处理。

数据包的处理流程体现了 QUIC 协议的特点：
1. 支持多种数据包类型（长头包和短头包）
2. 基于连接 ID 的连接查找
3. 数据包加密和认证
4. 拥塞控制和流量控制
5. 路径 MTU 发现

通过状态图和流程图，我们可以清晰地看到 QUIC 数据包的生命周期和处理流程，这有助于理解 MsQuic 的数据包处理机制和网络通信流程。