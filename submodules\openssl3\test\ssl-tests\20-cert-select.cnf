# Generated with generate_ssl_tests.pl

num_tests = 57

test-0 = 0-ECDSA CipherString Selection
test-1 = 1-ECDSA CipherString Selection
test-2 = 2-ECDSA CipherString Selection
test-3 = 3-RSA CipherString Selection
test-4 = 4-P-256 CipherString and Signature Algorithm Selection
test-5 = 5-ECDSA CipherString Selection, no ECDSA certificate
test-6 = 6-ECDSA Signature Algorithm Selection
test-7 = 7-ECDSA Signature Algorithm Selection SHA384
test-8 = 8-ECDSA Signature Algorithm Selection compressed point
test-9 = 9-ECDSA Signature Algorithm Selection, no ECDSA certificate
test-10 = 10-RSA Signature Algorithm Selection
test-11 = 11-RSA-PSS Signature Algorithm Selection
test-12 = 12-RSA key exchange with all RSA certificate types
test-13 = 13-Suite B P-256 Hash Algorithm Selection
test-14 = 14-Suite B P-384 Hash Algorithm Selection
test-15 = 15-Ed25519 CipherString and Signature Algorithm Selection
test-16 = 16-Ed448 CipherString and Signature Algorithm Selection
test-17 = 17-Ed25519 CipherString and Curves Selection
test-18 = 18-Ed448 CipherString and Curves Selection
test-19 = 19-TLS 1.2 Ed25519 Client Auth
test-20 = 20-TLS 1.2 Ed448 Client Auth
test-21 = 21-ECDSA Signature Algorithm Selection SHA1
test-22 = 22-ECDSA with brainpool
test-23 = 23-RSA-PSS Certificate CipherString Selection
test-24 = 24-RSA-PSS Certificate Legacy Signature Algorithm Selection
test-25 = 25-RSA-PSS Certificate Unified Signature Algorithm Selection
test-26 = 26-Only RSA-PSS Certificate
test-27 = 27-Only RSA-PSS Certificate Valid Signature Algorithms
test-28 = 28-RSA-PSS Certificate, no PSS signature algorithms
test-29 = 29-Only RSA-PSS Restricted Certificate
test-30 = 30-RSA-PSS Restricted Certificate Valid Signature Algorithms
test-31 = 31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm
test-32 = 32-RSA-PSS Restricted Certificate Invalid Signature Algorithms
test-33 = 33-RSA key exchange with only RSA-PSS certificate
test-34 = 34-Only RSA-PSS Certificate, TLS v1.1
test-35 = 35-TLS 1.3 ECDSA Signature Algorithm Selection
test-36 = 36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point
test-37 = 37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1
test-38 = 38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS
test-39 = 39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS
test-40 = 40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate
test-41 = 41-TLS 1.3 RSA Signature Algorithm Selection, no PSS
test-42 = 42-TLS 1.3 RSA-PSS Signature Algorithm Selection
test-43 = 43-TLS 1.3 RSA Client Auth Signature Algorithm Selection
test-44 = 44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names
test-45 = 45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection
test-46 = 46-TLS 1.3 Ed25519 Signature Algorithm Selection
test-47 = 47-TLS 1.3 Ed448 Signature Algorithm Selection
test-48 = 48-TLS 1.3 Ed25519 CipherString and Groups Selection
test-49 = 49-TLS 1.3 Ed448 CipherString and Groups Selection
test-50 = 50-TLS 1.3 Ed25519 Client Auth
test-51 = 51-TLS 1.3 Ed448 Client Auth
test-52 = 52-TLS 1.3 ECDSA with brainpool but no suitable groups
test-53 = 53-TLS 1.3 ECDSA with brainpool
test-54 = 54-TLS 1.2 DSA Certificate Test
test-55 = 55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms
test-56 = 56-TLS 1.3 DSA Certificate Test
# ===========================================================

[0-ECDSA CipherString Selection]
ssl_conf = 0-ECDSA CipherString Selection-ssl

[0-ECDSA CipherString Selection-ssl]
server = 0-ECDSA CipherString Selection-server
client = 0-ECDSA CipherString Selection-client

[0-ECDSA CipherString Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-ECDSA CipherString Selection-client]
CipherString = aECDSA
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = P-256
ExpectedServerSignType = EC


# ===========================================================

[1-ECDSA CipherString Selection]
ssl_conf = 1-ECDSA CipherString Selection-ssl

[1-ECDSA CipherString Selection-ssl]
server = 1-ECDSA CipherString Selection-server
client = 1-ECDSA CipherString Selection-client

[1-ECDSA CipherString Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Groups = P-384
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-ECDSA CipherString Selection-client]
CipherString = aECDSA
Groups = P-256:P-384
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = P-256
ExpectedServerSignType = EC


# ===========================================================

[2-ECDSA CipherString Selection]
ssl_conf = 2-ECDSA CipherString Selection-ssl

[2-ECDSA CipherString Selection-ssl]
server = 2-ECDSA CipherString Selection-server
client = 2-ECDSA CipherString Selection-client

[2-ECDSA CipherString Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Groups = P-256:P-384
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-ECDSA CipherString Selection-client]
CipherString = aECDSA
Groups = P-384
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = ServerFail


# ===========================================================

[3-RSA CipherString Selection]
ssl_conf = 3-RSA CipherString Selection-ssl

[3-RSA CipherString Selection-ssl]
server = 3-RSA CipherString Selection-server
client = 3-RSA CipherString Selection-client

[3-RSA CipherString Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-RSA CipherString Selection-client]
CipherString = aRSA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignType = RSA-PSS


# ===========================================================

[4-P-256 CipherString and Signature Algorithm Selection]
ssl_conf = 4-P-256 CipherString and Signature Algorithm Selection-ssl

[4-P-256 CipherString and Signature Algorithm Selection-ssl]
server = 4-P-256 CipherString and Signature Algorithm Selection-server
client = 4-P-256 CipherString and Signature Algorithm Selection-client

[4-P-256 CipherString and Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-P-256 CipherString and Signature Algorithm Selection-client]
CipherString = aECDSA
MaxProtocol = TLSv1.2
SignatureAlgorithms = ECDSA+SHA256:ed25519
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[5-ECDSA CipherString Selection, no ECDSA certificate]
ssl_conf = 5-ECDSA CipherString Selection, no ECDSA certificate-ssl

[5-ECDSA CipherString Selection, no ECDSA certificate-ssl]
server = 5-ECDSA CipherString Selection, no ECDSA certificate-server
client = 5-ECDSA CipherString Selection, no ECDSA certificate-client

[5-ECDSA CipherString Selection, no ECDSA certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-ECDSA CipherString Selection, no ECDSA certificate-client]
CipherString = aECDSA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = ServerFail


# ===========================================================

[6-ECDSA Signature Algorithm Selection]
ssl_conf = 6-ECDSA Signature Algorithm Selection-ssl

[6-ECDSA Signature Algorithm Selection-ssl]
server = 6-ECDSA Signature Algorithm Selection-server
client = 6-ECDSA Signature Algorithm Selection-client

[6-ECDSA Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-ECDSA Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[7-ECDSA Signature Algorithm Selection SHA384]
ssl_conf = 7-ECDSA Signature Algorithm Selection SHA384-ssl

[7-ECDSA Signature Algorithm Selection SHA384-ssl]
server = 7-ECDSA Signature Algorithm Selection SHA384-server
client = 7-ECDSA Signature Algorithm Selection SHA384-client

[7-ECDSA Signature Algorithm Selection SHA384-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-ECDSA Signature Algorithm Selection SHA384-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA384
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA384
ExpectedServerSignType = EC


# ===========================================================

[8-ECDSA Signature Algorithm Selection compressed point]
ssl_conf = 8-ECDSA Signature Algorithm Selection compressed point-ssl

[8-ECDSA Signature Algorithm Selection compressed point-ssl]
server = 8-ECDSA Signature Algorithm Selection compressed point-server
client = 8-ECDSA Signature Algorithm Selection compressed point-client

[8-ECDSA Signature Algorithm Selection compressed point-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-cecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-cecdsa-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-ECDSA Signature Algorithm Selection compressed point-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[9-ECDSA Signature Algorithm Selection, no ECDSA certificate]
ssl_conf = 9-ECDSA Signature Algorithm Selection, no ECDSA certificate-ssl

[9-ECDSA Signature Algorithm Selection, no ECDSA certificate-ssl]
server = 9-ECDSA Signature Algorithm Selection, no ECDSA certificate-server
client = 9-ECDSA Signature Algorithm Selection, no ECDSA certificate-client

[9-ECDSA Signature Algorithm Selection, no ECDSA certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-ECDSA Signature Algorithm Selection, no ECDSA certificate-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = ServerFail


# ===========================================================

[10-RSA Signature Algorithm Selection]
ssl_conf = 10-RSA Signature Algorithm Selection-ssl

[10-RSA Signature Algorithm Selection-ssl]
server = 10-RSA Signature Algorithm Selection-server
client = 10-RSA Signature Algorithm Selection-client

[10-RSA Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-RSA Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA


# ===========================================================

[11-RSA-PSS Signature Algorithm Selection]
ssl_conf = 11-RSA-PSS Signature Algorithm Selection-ssl

[11-RSA-PSS Signature Algorithm Selection-ssl]
server = 11-RSA-PSS Signature Algorithm Selection-server
client = 11-RSA-PSS Signature Algorithm Selection-client

[11-RSA-PSS Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-RSA-PSS Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA-PSS+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[12-RSA key exchange with all RSA certificate types]
ssl_conf = 12-RSA key exchange with all RSA certificate types-ssl

[12-RSA key exchange with all RSA certificate types-ssl]
server = 12-RSA key exchange with all RSA certificate types-server
client = 12-RSA key exchange with all RSA certificate types-client

[12-RSA key exchange with all RSA certificate types-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PSS.Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
PSS.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-RSA key exchange with all RSA certificate types-client]
CipherString = kRSA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = Success
ExpectedServerCertType = RSA


# ===========================================================

[13-Suite B P-256 Hash Algorithm Selection]
ssl_conf = 13-Suite B P-256 Hash Algorithm Selection-ssl

[13-Suite B P-256 Hash Algorithm Selection-ssl]
server = 13-Suite B P-256 Hash Algorithm Selection-server
client = 13-Suite B P-256 Hash Algorithm Selection-client

[13-Suite B P-256 Hash Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = SUITEB128
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/p256-server-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/p256-server-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-Suite B P-256 Hash Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA384:ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/p384-root.pem
VerifyMode = Peer

[test-13]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[14-Suite B P-384 Hash Algorithm Selection]
ssl_conf = 14-Suite B P-384 Hash Algorithm Selection-ssl

[14-Suite B P-384 Hash Algorithm Selection-ssl]
server = 14-Suite B P-384 Hash Algorithm Selection-server
client = 14-Suite B P-384 Hash Algorithm Selection-client

[14-Suite B P-384 Hash Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = SUITEB128
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/p384-server-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/p384-server-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-Suite B P-384 Hash Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256:ECDSA+SHA384
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/p384-root.pem
VerifyMode = Peer

[test-14]
ExpectedResult = Success
ExpectedServerCertType = P-384
ExpectedServerSignHash = SHA384
ExpectedServerSignType = EC


# ===========================================================

[15-Ed25519 CipherString and Signature Algorithm Selection]
ssl_conf = 15-Ed25519 CipherString and Signature Algorithm Selection-ssl

[15-Ed25519 CipherString and Signature Algorithm Selection-ssl]
server = 15-Ed25519 CipherString and Signature Algorithm Selection-server
client = 15-Ed25519 CipherString and Signature Algorithm Selection-client

[15-Ed25519 CipherString and Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-Ed25519 CipherString and Signature Algorithm Selection-client]
CipherString = aECDSA
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
SignatureAlgorithms = ed25519:ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = Ed25519
ExpectedServerSignType = Ed25519


# ===========================================================

[16-Ed448 CipherString and Signature Algorithm Selection]
ssl_conf = 16-Ed448 CipherString and Signature Algorithm Selection-ssl

[16-Ed448 CipherString and Signature Algorithm Selection-ssl]
server = 16-Ed448 CipherString and Signature Algorithm Selection-server
client = 16-Ed448 CipherString and Signature Algorithm Selection-client

[16-Ed448 CipherString and Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-Ed448 CipherString and Signature Algorithm Selection-client]
CipherString = aECDSA
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
SignatureAlgorithms = ed448:ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-16]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = Ed448
ExpectedServerSignType = Ed448


# ===========================================================

[17-Ed25519 CipherString and Curves Selection]
ssl_conf = 17-Ed25519 CipherString and Curves Selection-ssl

[17-Ed25519 CipherString and Curves Selection-ssl]
server = 17-Ed25519 CipherString and Curves Selection-server
client = 17-Ed25519 CipherString and Curves Selection-client

[17-Ed25519 CipherString and Curves Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-Ed25519 CipherString and Curves Selection-client]
CipherString = aECDSA
Curves = X25519
MaxProtocol = TLSv1.2
SignatureAlgorithms = ECDSA+SHA256:ed25519
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedResult = Success
ExpectedServerCertType = Ed25519
ExpectedServerSignType = Ed25519


# ===========================================================

[18-Ed448 CipherString and Curves Selection]
ssl_conf = 18-Ed448 CipherString and Curves Selection-ssl

[18-Ed448 CipherString and Curves Selection-ssl]
server = 18-Ed448 CipherString and Curves Selection-server
client = 18-Ed448 CipherString and Curves Selection-client

[18-Ed448 CipherString and Curves Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-Ed448 CipherString and Curves Selection-client]
CipherString = aECDSA
Curves = X448
MaxProtocol = TLSv1.2
SignatureAlgorithms = ECDSA+SHA256:ed448
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-18]
ExpectedResult = Success
ExpectedServerCertType = Ed448
ExpectedServerSignType = Ed448


# ===========================================================

[19-TLS 1.2 Ed25519 Client Auth]
ssl_conf = 19-TLS 1.2 Ed25519 Client Auth-ssl

[19-TLS 1.2 Ed25519 Client Auth-ssl]
server = 19-TLS 1.2 Ed25519 Client Auth-server
client = 19-TLS 1.2 Ed25519 Client Auth-client

[19-TLS 1.2 Ed25519 Client Auth-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[19-TLS 1.2 Ed25519 Client Auth-client]
CipherString = DEFAULT
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/client-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/client-ed25519-key.pem
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedClientCertType = Ed25519
ExpectedClientSignType = Ed25519
ExpectedResult = Success


# ===========================================================

[20-TLS 1.2 Ed448 Client Auth]
ssl_conf = 20-TLS 1.2 Ed448 Client Auth-ssl

[20-TLS 1.2 Ed448 Client Auth-ssl]
server = 20-TLS 1.2 Ed448 Client Auth-server
client = 20-TLS 1.2 Ed448 Client Auth-client

[20-TLS 1.2 Ed448 Client Auth-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[20-TLS 1.2 Ed448 Client Auth-client]
CipherString = DEFAULT
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/client-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/client-ed448-key.pem
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedClientCertType = Ed448
ExpectedClientSignType = Ed448
ExpectedResult = Success


# ===========================================================

[21-ECDSA Signature Algorithm Selection SHA1]
ssl_conf = 21-ECDSA Signature Algorithm Selection SHA1-ssl

[21-ECDSA Signature Algorithm Selection SHA1-ssl]
server = 21-ECDSA Signature Algorithm Selection SHA1-server
client = 21-ECDSA Signature Algorithm Selection SHA1-client

[21-ECDSA Signature Algorithm Selection SHA1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-ECDSA Signature Algorithm Selection SHA1-client]
CipherString = DEFAULT:@SECLEVEL=0
SignatureAlgorithms = ECDSA+SHA1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA1
ExpectedServerSignType = EC


# ===========================================================

[22-ECDSA with brainpool]
ssl_conf = 22-ECDSA with brainpool-ssl

[22-ECDSA with brainpool-ssl]
server = 22-ECDSA with brainpool-server
client = 22-ECDSA with brainpool-client

[22-ECDSA with brainpool-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-cert.pem
CipherString = DEFAULT
Groups = brainpoolP256r1
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-key.pem

[22-ECDSA with brainpool-client]
CipherString = aECDSA
Groups = brainpoolP256r1
MaxProtocol = TLSv1.2
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = brainpoolP256r1
ExpectedServerSignType = EC


# ===========================================================

[23-RSA-PSS Certificate CipherString Selection]
ssl_conf = 23-RSA-PSS Certificate CipherString Selection-ssl

[23-RSA-PSS Certificate CipherString Selection-ssl]
server = 23-RSA-PSS Certificate CipherString Selection-server
client = 23-RSA-PSS Certificate CipherString Selection-client

[23-RSA-PSS Certificate CipherString Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PSS.Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
PSS.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-RSA-PSS Certificate CipherString Selection-client]
CipherString = aRSA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignType = RSA-PSS


# ===========================================================

[24-RSA-PSS Certificate Legacy Signature Algorithm Selection]
ssl_conf = 24-RSA-PSS Certificate Legacy Signature Algorithm Selection-ssl

[24-RSA-PSS Certificate Legacy Signature Algorithm Selection-ssl]
server = 24-RSA-PSS Certificate Legacy Signature Algorithm Selection-server
client = 24-RSA-PSS Certificate Legacy Signature Algorithm Selection-client

[24-RSA-PSS Certificate Legacy Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PSS.Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
PSS.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-RSA-PSS Certificate Legacy Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA-PSS+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[25-RSA-PSS Certificate Unified Signature Algorithm Selection]
ssl_conf = 25-RSA-PSS Certificate Unified Signature Algorithm Selection-ssl

[25-RSA-PSS Certificate Unified Signature Algorithm Selection-ssl]
server = 25-RSA-PSS Certificate Unified Signature Algorithm Selection-server
client = 25-RSA-PSS Certificate Unified Signature Algorithm Selection-client

[25-RSA-PSS Certificate Unified Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.2
PSS.Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
PSS.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-RSA-PSS Certificate Unified Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = rsa_pss_pss_sha256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[26-Only RSA-PSS Certificate]
ssl_conf = 26-Only RSA-PSS Certificate-ssl

[26-Only RSA-PSS Certificate-ssl]
server = 26-Only RSA-PSS Certificate-server
client = 26-Only RSA-PSS Certificate-client

[26-Only RSA-PSS Certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[26-Only RSA-PSS Certificate-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[27-Only RSA-PSS Certificate Valid Signature Algorithms]
ssl_conf = 27-Only RSA-PSS Certificate Valid Signature Algorithms-ssl

[27-Only RSA-PSS Certificate Valid Signature Algorithms-ssl]
server = 27-Only RSA-PSS Certificate Valid Signature Algorithms-server
client = 27-Only RSA-PSS Certificate Valid Signature Algorithms-client

[27-Only RSA-PSS Certificate Valid Signature Algorithms-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[27-Only RSA-PSS Certificate Valid Signature Algorithms-client]
CipherString = DEFAULT
SignatureAlgorithms = rsa_pss_pss_sha512
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA512
ExpectedServerSignType = RSA-PSS


# ===========================================================

[28-RSA-PSS Certificate, no PSS signature algorithms]
ssl_conf = 28-RSA-PSS Certificate, no PSS signature algorithms-ssl

[28-RSA-PSS Certificate, no PSS signature algorithms-ssl]
server = 28-RSA-PSS Certificate, no PSS signature algorithms-server
client = 28-RSA-PSS Certificate, no PSS signature algorithms-client

[28-RSA-PSS Certificate, no PSS signature algorithms-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[28-RSA-PSS Certificate, no PSS signature algorithms-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedResult = ServerFail


# ===========================================================

[29-Only RSA-PSS Restricted Certificate]
ssl_conf = 29-Only RSA-PSS Restricted Certificate-ssl

[29-Only RSA-PSS Restricted Certificate-ssl]
server = 29-Only RSA-PSS Restricted Certificate-server
client = 29-Only RSA-PSS Restricted Certificate-client

[29-Only RSA-PSS Restricted Certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-key.pem

[29-Only RSA-PSS Restricted Certificate-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[30-RSA-PSS Restricted Certificate Valid Signature Algorithms]
ssl_conf = 30-RSA-PSS Restricted Certificate Valid Signature Algorithms-ssl

[30-RSA-PSS Restricted Certificate Valid Signature Algorithms-ssl]
server = 30-RSA-PSS Restricted Certificate Valid Signature Algorithms-server
client = 30-RSA-PSS Restricted Certificate Valid Signature Algorithms-client

[30-RSA-PSS Restricted Certificate Valid Signature Algorithms-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-key.pem

[30-RSA-PSS Restricted Certificate Valid Signature Algorithms-client]
CipherString = DEFAULT
SignatureAlgorithms = rsa_pss_pss_sha256:rsa_pss_pss_sha512
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm]
ssl_conf = 31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-ssl

[31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-ssl]
server = 31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-server
client = 31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-client

[31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-key.pem

[31-RSA-PSS Restricted Cert client prefers invalid Signature Algorithm-client]
CipherString = DEFAULT
SignatureAlgorithms = rsa_pss_pss_sha512:rsa_pss_pss_sha256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedResult = Success
ExpectedServerCertType = RSA-PSS
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[32-RSA-PSS Restricted Certificate Invalid Signature Algorithms]
ssl_conf = 32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-ssl

[32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-ssl]
server = 32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-server
client = 32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-client

[32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-restrict-key.pem

[32-RSA-PSS Restricted Certificate Invalid Signature Algorithms-client]
CipherString = DEFAULT
SignatureAlgorithms = rsa_pss_pss_sha512
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedResult = ServerFail


# ===========================================================

[33-RSA key exchange with only RSA-PSS certificate]
ssl_conf = 33-RSA key exchange with only RSA-PSS certificate-ssl

[33-RSA key exchange with only RSA-PSS certificate-ssl]
server = 33-RSA key exchange with only RSA-PSS certificate-server
client = 33-RSA key exchange with only RSA-PSS certificate-client

[33-RSA key exchange with only RSA-PSS certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[33-RSA key exchange with only RSA-PSS certificate-client]
CipherString = kRSA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedResult = ServerFail


# ===========================================================

[34-Only RSA-PSS Certificate, TLS v1.1]
ssl_conf = 34-Only RSA-PSS Certificate, TLS v1.1-ssl

[34-Only RSA-PSS Certificate, TLS v1.1-ssl]
server = 34-Only RSA-PSS Certificate, TLS v1.1-server
client = 34-Only RSA-PSS Certificate, TLS v1.1-client

[34-Only RSA-PSS Certificate, TLS v1.1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[34-Only RSA-PSS Certificate, TLS v1.1-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedResult = ServerFail


# ===========================================================

[35-TLS 1.3 ECDSA Signature Algorithm Selection]
ssl_conf = 35-TLS 1.3 ECDSA Signature Algorithm Selection-ssl

[35-TLS 1.3 ECDSA Signature Algorithm Selection-ssl]
server = 35-TLS 1.3 ECDSA Signature Algorithm Selection-server
client = 35-TLS 1.3 ECDSA Signature Algorithm Selection-client

[35-TLS 1.3 ECDSA Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[35-TLS 1.3 ECDSA Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point]
ssl_conf = 36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-ssl

[36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-ssl]
server = 36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-server
client = 36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-client

[36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-cecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-cecdsa-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[36-TLS 1.3 ECDSA Signature Algorithm Selection compressed point-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-36]
ExpectedResult = Success
ExpectedServerCANames = empty
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1]
ssl_conf = 37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-ssl

[37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-ssl]
server = 37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-server
client = 37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-client

[37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[37-TLS 1.3 ECDSA Signature Algorithm Selection SHA1-client]
CipherString = DEFAULT:@SECLEVEL=0
SignatureAlgorithms = ECDSA+SHA1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-37]
ExpectedResult = ServerFail


# ===========================================================

[38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS]
ssl_conf = 38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-ssl

[38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-ssl]
server = 38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-server
client = 38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-client

[38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[38-TLS 1.3 ECDSA Signature Algorithm Selection with PSS-client]
CipherString = DEFAULT
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
SignatureAlgorithms = ECDSA+SHA256:RSA-PSS+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-38]
ExpectedResult = Success
ExpectedServerCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedServerCertType = P-256
ExpectedServerSignHash = SHA256
ExpectedServerSignType = EC


# ===========================================================

[39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS]
ssl_conf = 39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-ssl

[39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-ssl]
server = 39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-server
client = 39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-client

[39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[39-TLS 1.3 RSA Signature Algorithm Selection SHA384 with PSS-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA384:RSA-PSS+SHA384
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-39]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignHash = SHA384
ExpectedServerSignType = RSA-PSS


# ===========================================================

[40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate]
ssl_conf = 40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-ssl

[40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-ssl]
server = 40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-server
client = 40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-client

[40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[40-TLS 1.3 ECDSA Signature Algorithm Selection, no ECDSA certificate-client]
CipherString = DEFAULT
SignatureAlgorithms = ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-40]
ExpectedResult = ServerFail


# ===========================================================

[41-TLS 1.3 RSA Signature Algorithm Selection, no PSS]
ssl_conf = 41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-ssl

[41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-ssl]
server = 41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-server
client = 41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-client

[41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[41-TLS 1.3 RSA Signature Algorithm Selection, no PSS-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-41]
ExpectedResult = ServerFail


# ===========================================================

[42-TLS 1.3 RSA-PSS Signature Algorithm Selection]
ssl_conf = 42-TLS 1.3 RSA-PSS Signature Algorithm Selection-ssl

[42-TLS 1.3 RSA-PSS Signature Algorithm Selection-ssl]
server = 42-TLS 1.3 RSA-PSS Signature Algorithm Selection-server
client = 42-TLS 1.3 RSA-PSS Signature Algorithm Selection-client

[42-TLS 1.3 RSA-PSS Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[42-TLS 1.3 RSA-PSS Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = RSA-PSS+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-42]
ExpectedResult = Success
ExpectedServerCertType = RSA
ExpectedServerSignHash = SHA256
ExpectedServerSignType = RSA-PSS


# ===========================================================

[43-TLS 1.3 RSA Client Auth Signature Algorithm Selection]
ssl_conf = 43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-ssl

[43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-ssl]
server = 43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-server
client = 43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-client

[43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = PSS+SHA256
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[43-TLS 1.3 RSA Client Auth Signature Algorithm Selection-client]
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-client-chain.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
RSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
RSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-43]
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA-PSS
ExpectedResult = Success


# ===========================================================

[44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names]
ssl_conf = 44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-ssl

[44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-ssl]
server = 44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-server
client = 44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-client

[44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = PSS+SHA256
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[44-TLS 1.3 RSA Client Auth Signature Algorithm Selection non-empty CA Names-client]
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-client-chain.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
RSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
RSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-44]
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedClientSignHash = SHA256
ExpectedClientSignType = RSA-PSS
ExpectedResult = Success


# ===========================================================

[45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection]
ssl_conf = 45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-ssl

[45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-ssl]
server = 45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-server
client = 45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-client

[45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = ECDSA+SHA256
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[45-TLS 1.3 ECDSA Client Auth Signature Algorithm Selection-client]
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-client-chain.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-ecdsa-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
RSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
RSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-45]
ExpectedClientCertType = P-256
ExpectedClientSignHash = SHA256
ExpectedClientSignType = EC
ExpectedResult = Success


# ===========================================================

[46-TLS 1.3 Ed25519 Signature Algorithm Selection]
ssl_conf = 46-TLS 1.3 Ed25519 Signature Algorithm Selection-ssl

[46-TLS 1.3 Ed25519 Signature Algorithm Selection-ssl]
server = 46-TLS 1.3 Ed25519 Signature Algorithm Selection-server
client = 46-TLS 1.3 Ed25519 Signature Algorithm Selection-client

[46-TLS 1.3 Ed25519 Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[46-TLS 1.3 Ed25519 Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ed25519
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-46]
ExpectedResult = Success
ExpectedServerCertType = Ed25519
ExpectedServerSignType = Ed25519


# ===========================================================

[47-TLS 1.3 Ed448 Signature Algorithm Selection]
ssl_conf = 47-TLS 1.3 Ed448 Signature Algorithm Selection-ssl

[47-TLS 1.3 Ed448 Signature Algorithm Selection-ssl]
server = 47-TLS 1.3 Ed448 Signature Algorithm Selection-server
client = 47-TLS 1.3 Ed448 Signature Algorithm Selection-client

[47-TLS 1.3 Ed448 Signature Algorithm Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[47-TLS 1.3 Ed448 Signature Algorithm Selection-client]
CipherString = DEFAULT
SignatureAlgorithms = ed448
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-47]
ExpectedResult = Success
ExpectedServerCertType = Ed448
ExpectedServerSignType = Ed448


# ===========================================================

[48-TLS 1.3 Ed25519 CipherString and Groups Selection]
ssl_conf = 48-TLS 1.3 Ed25519 CipherString and Groups Selection-ssl

[48-TLS 1.3 Ed25519 CipherString and Groups Selection-ssl]
server = 48-TLS 1.3 Ed25519 CipherString and Groups Selection-server
client = 48-TLS 1.3 Ed25519 CipherString and Groups Selection-client

[48-TLS 1.3 Ed25519 CipherString and Groups Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[48-TLS 1.3 Ed25519 CipherString and Groups Selection-client]
CipherString = DEFAULT
Groups = X25519
SignatureAlgorithms = ECDSA+SHA256:ed25519
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-48]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignType = EC


# ===========================================================

[49-TLS 1.3 Ed448 CipherString and Groups Selection]
ssl_conf = 49-TLS 1.3 Ed448 CipherString and Groups Selection-ssl

[49-TLS 1.3 Ed448 CipherString and Groups Selection-ssl]
server = 49-TLS 1.3 Ed448 CipherString and Groups Selection-server
client = 49-TLS 1.3 Ed448 CipherString and Groups Selection-client

[49-TLS 1.3 Ed448 CipherString and Groups Selection-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ECDSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
ECDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem
Ed25519.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed25519-cert.pem
Ed25519.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed25519-key.pem
Ed448.Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
Ed448.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[49-TLS 1.3 Ed448 CipherString and Groups Selection-client]
CipherString = DEFAULT
Groups = X448
SignatureAlgorithms = ECDSA+SHA256:ed448
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-49]
ExpectedResult = Success
ExpectedServerCertType = P-256
ExpectedServerSignType = EC


# ===========================================================

[50-TLS 1.3 Ed25519 Client Auth]
ssl_conf = 50-TLS 1.3 Ed25519 Client Auth-ssl

[50-TLS 1.3 Ed25519 Client Auth-ssl]
server = 50-TLS 1.3 Ed25519 Client Auth-server
client = 50-TLS 1.3 Ed25519 Client Auth-client

[50-TLS 1.3 Ed25519 Client Auth-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[50-TLS 1.3 Ed25519 Client Auth-client]
CipherString = DEFAULT
EdDSA.Certificate = ${ENV::TEST_CERTS_DIR}/client-ed25519-cert.pem
EdDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/client-ed25519-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-50]
ExpectedClientCertType = Ed25519
ExpectedClientSignType = Ed25519
ExpectedResult = Success


# ===========================================================

[51-TLS 1.3 Ed448 Client Auth]
ssl_conf = 51-TLS 1.3 Ed448 Client Auth-ssl

[51-TLS 1.3 Ed448 Client Auth-ssl]
server = 51-TLS 1.3 Ed448 Client Auth-server
client = 51-TLS 1.3 Ed448 Client Auth-client

[51-TLS 1.3 Ed448 Client Auth-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[51-TLS 1.3 Ed448 Client Auth-client]
CipherString = DEFAULT
EdDSA.Certificate = ${ENV::TEST_CERTS_DIR}/client-ed448-cert.pem
EdDSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/client-ed448-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-51]
ExpectedClientCertType = Ed448
ExpectedClientSignType = Ed448
ExpectedResult = Success


# ===========================================================

[52-TLS 1.3 ECDSA with brainpool but no suitable groups]
ssl_conf = 52-TLS 1.3 ECDSA with brainpool but no suitable groups-ssl

[52-TLS 1.3 ECDSA with brainpool but no suitable groups-ssl]
server = 52-TLS 1.3 ECDSA with brainpool but no suitable groups-server
client = 52-TLS 1.3 ECDSA with brainpool but no suitable groups-client

[52-TLS 1.3 ECDSA with brainpool but no suitable groups-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-cert.pem
CipherString = DEFAULT
Groups = brainpoolP256r1
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-key.pem

[52-TLS 1.3 ECDSA with brainpool but no suitable groups-client]
CipherString = aECDSA
Groups = brainpoolP256r1
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-52]
ExpectedResult = ClientFail


# ===========================================================

[53-TLS 1.3 ECDSA with brainpool]
ssl_conf = 53-TLS 1.3 ECDSA with brainpool-ssl

[53-TLS 1.3 ECDSA with brainpool-ssl]
server = 53-TLS 1.3 ECDSA with brainpool-server
client = 53-TLS 1.3 ECDSA with brainpool-client

[53-TLS 1.3 ECDSA with brainpool-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-cert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-brainpoolP256r1-key.pem

[53-TLS 1.3 ECDSA with brainpool-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
RequestCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-53]
ExpectedResult = ServerFail


# ===========================================================

[54-TLS 1.2 DSA Certificate Test]
ssl_conf = 54-TLS 1.2 DSA Certificate Test-ssl

[54-TLS 1.2 DSA Certificate Test-ssl]
server = 54-TLS 1.2 DSA Certificate Test-server
client = 54-TLS 1.2 DSA Certificate Test-client

[54-TLS 1.2 DSA Certificate Test-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = ALL
DHParameters = ${ENV::TEST_CERTS_DIR}/dhp2048.pem
DSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-dsa-cert.pem
DSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-dsa-key.pem
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[54-TLS 1.2 DSA Certificate Test-client]
CipherString = ALL
SignatureAlgorithms = DSA+SHA256:DSA+SHA1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-54]
ExpectedResult = Success


# ===========================================================

[55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms]
ssl_conf = 55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-ssl

[55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-ssl]
server = 55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-server
client = 55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-client

[55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientSignatureAlgorithms = ECDSA+SHA1:DSA+SHA256:RSA+SHA256
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Request

[55-TLS 1.3 Client Auth No TLS 1.3 Signature Algorithms-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-55]
ExpectedResult = ServerFail


# ===========================================================

[56-TLS 1.3 DSA Certificate Test]
ssl_conf = 56-TLS 1.3 DSA Certificate Test-ssl

[56-TLS 1.3 DSA Certificate Test-ssl]
server = 56-TLS 1.3 DSA Certificate Test-server
client = 56-TLS 1.3 DSA Certificate Test-client

[56-TLS 1.3 DSA Certificate Test-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = ALL
DSA.Certificate = ${ENV::TEST_CERTS_DIR}/server-dsa-cert.pem
DSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/server-dsa-key.pem
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[56-TLS 1.3 DSA Certificate Test-client]
CipherString = ALL
SignatureAlgorithms = DSA+SHA1:DSA+SHA256:ECDSA+SHA256
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-56]
ExpectedResult = ServerFail


