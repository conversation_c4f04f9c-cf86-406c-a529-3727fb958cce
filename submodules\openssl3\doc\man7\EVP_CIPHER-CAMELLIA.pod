=pod

=head1 NAME

EVP_CIPHER-CAMELLIA - The CAMELLIA EVP_CIPHER implementations

=head1 DESCRIPTION

Support for CAMELLIA symmetric encryption using the B<EVP_CIPHER> API.

=head2 Algorithm Names

The following algorithms are available in the default provider:

=over 4

=item "CAMELLIA-128-CBC", "CAMELLIA-192-CBC" and  "CAMELLIA-256-CBC"

=item "CAMELLIA-128-CBC-CTS", "CAMELLIA-192-CBC-CTS" and "CAMELLIA-256-CBC-CTS"

=item "CAMELLIA-128-CFB", "CAMELLIA-192-CFB", "CAMELLIA-256-CFB",
"CAMELLIA-128-CFB1", "CAMELLIA-192-CFB1", "CAMELLIA-256-CFB1",
"CAMELLIA-128-CFB8", "CAMELLIA-192-CFB8" and "CAMELLIA-256-CFB8"

=item "CAMELLIA-128-CTR", "CAMELLIA-192-CTR" and "CAMELLIA-256-CTR"

=item "CAMELLIA-128-ECB", "CAMELLIA-192-ECB" and "CAMELLIA-256-ECB"

=item "CAMELLIA-192-OFB", "CAMELLIA-128-OFB" and "CAMELLIA-256-OFB"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
