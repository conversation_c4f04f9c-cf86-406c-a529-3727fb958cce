=pod

=head1 NAME

SSL_CTX_flush_sessions - remove expired sessions

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_CTX_flush_sessions(SSL_CTX *ctx, long tm);

=head1 DESCRIPTION

SSL_CTX_flush_sessions() causes a run through the session cache of
B<ctx> to remove sessions expired at time B<tm>.

=head1 NOTES

If enabled, the internal session cache will collect all sessions established
up to the specified maximum number (see SSL_CTX_sess_set_cache_size()).
As sessions will not be reused ones they are expired, they should be
removed from the cache to save resources. This can either be done
automatically whenever 255 new sessions were established (see
L<SSL_CTX_set_session_cache_mode(3)>)
or manually by calling SSL_CTX_flush_sessions().

The parameter B<tm> specifies the time which should be used for the
expiration test, in most cases the actual time given by time(0)
will be used.

SSL_CTX_flush_sessions() will only check sessions stored in the internal
cache. When a session is found and removed, the remove_session_cb is however
called to synchronize with the external cache (see
L<SSL_CTX_sess_set_get_cb(3)>).

=head1 RETURN VALUES

SSL_CTX_flush_sessions() does not return a value.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_session_cache_mode(3)>,
L<SSL_CTX_set_timeout(3)>,
L<SSL_CTX_sess_set_get_cb(3)>

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
