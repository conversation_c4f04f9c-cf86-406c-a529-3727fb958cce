# This file specifies the Certificate Transparency logs
# that are to be trusted.

# Google's list of logs can be found here:
#       www.certificate-transparency.org/known-logs
# A Python program to convert the log list to OpenSSL's format can be
# found here:
#       https://github.com/google/certificate-transparency/blob/master/python/utilities/log_list/print_log_list.py
# Use the "--openssl_output" flag.
