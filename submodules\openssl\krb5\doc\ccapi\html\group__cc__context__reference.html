<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_context_t Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_context_t Overview</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_context_t type gives the caller access to a ccache collection. Before being able to call any functions in the CCache API, the caller needs to acquire an instance of cc_context_t by calling <a class="el" href="group__cc__context__reference.html#ge4174587d8bb261e32194bbb9585fb82">cc_initialize()</a>.<p>
For API function documentation see <a class="el" href="structcc__context__f.html">cc_context_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__context__d.html">cc_context_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__context__f.html">cc_context_f</a> <a class="el" href="group__cc__context__reference.html#gf285100b13ebff78db5ce1efb30e689d">cc_context_f</a>
<li>typedef <a class="el" href="structcc__context__d.html">cc_context_d</a> <a class="el" href="group__cc__context__reference.html#g989a601250a2a05fd23b46c546084add">cc_context_d</a>
<li>typedef <a class="el" href="structcc__context__d.html">cc_context_d</a> * <a class="el" href="group__cc__context__reference.html#gea6f60fd799984a7178bcb4d4c7290c0">cc_context_t</a>
</ul>
<h2>Functions</h2>
<ul>
<li>CCACHE_API <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="group__cc__context__reference.html#ge4174587d8bb261e32194bbb9585fb82">cc_initialize</a> (<a class="el" href="structcc__context__d.html">cc_context_t</a> *out_context, <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> in_version, <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> *out_supported_version, char const **out_vendor)
<dl class="el"><dd class="mdescRight">Initialize a new cc_context.  <a href="#ge4174587d8bb261e32194bbb9585fb82"></a><br></dl></ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="gf285100b13ebff78db5ce1efb30e689d"></a><!-- doxytag: member="CredentialsCache.h::cc_context_f" ref="gf285100b13ebff78db5ce1efb30e689d" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__context__f.html">cc_context_f</a> <a class="el" href="structcc__context__f.html">cc_context_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g989a601250a2a05fd23b46c546084add"></a><!-- doxytag: member="CredentialsCache.h::cc_context_d" ref="g989a601250a2a05fd23b46c546084add" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__context__d.html">cc_context_d</a> <a class="el" href="structcc__context__d.html">cc_context_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="gea6f60fd799984a7178bcb4d4c7290c0"></a><!-- doxytag: member="CredentialsCache.h::cc_context_t" ref="gea6f60fd799984a7178bcb4d4c7290c0" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__context__d.html">cc_context_d</a>* <a class="el" href="structcc__context__d.html">cc_context_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="ge4174587d8bb261e32194bbb9585fb82"></a><!-- doxytag: member="CredentialsCache.h::cc_initialize" ref="ge4174587d8bb261e32194bbb9585fb82" args="(cc_context_t *out_context, cc_int32 in_version, cc_int32 *out_supported_version, char const **out_vendor)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">CCACHE_API <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> cc_initialize           </td>
          <td class="md" valign="top">(&nbsp;</td>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__context__d.html">cc_context_t</a> *&nbsp;</td>
          <td class="mdname" nowrap> <em>out_context</em>, </td>
        </tr>
        <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>&nbsp;</td>
          <td class="mdname" nowrap> <em>in_version</em>, </td>
        </tr>
        <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> *&nbsp;</td>
          <td class="mdname" nowrap> <em>out_supported_version</em>, </td>
        </tr>
        <tr>
          <td class="md" nowrap align="right"></td>
          <td class="md"></td>
          <td class="md" nowrap>char const **&nbsp;</td>
          <td class="mdname" nowrap> <em>out_vendor</em></td>
        </tr>
        <tr>
          <td class="md"></td>
          <td class="md">)&nbsp;</td>
          <td class="md" colspan="2"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Initialize a new cc_context. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>out_context</em>&nbsp;</td><td>on exit, a new context object. Must be free with <a class="el" href="group__helper__macros.html#g8ff82ce108889d4ed29f46ffe6efc40e">cc_context_release()</a>. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_version</em>&nbsp;</td><td>the requested API version. This should be the maximum version the application supports. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_supported_version</em>&nbsp;</td><td>if non-NULL, on exit contains the maximum API version supported by the implementation. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_vendor</em>&nbsp;</td><td>if non-NULL, on exit contains a pointer to a read-only C string which contains a string describing the vendor which implemented the credentials cache API. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. May return CCAPI v2 error CC_BAD_API_VERSION if <a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55bafee271af4e43ec6c9bb2e3e849cc1f9">ccapi_version_2</a> is passed in. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
