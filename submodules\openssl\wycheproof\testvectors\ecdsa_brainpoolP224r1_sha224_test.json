{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 359, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "GroupIsomorphism": "Some EC groups have isomorphic groups that allow an efficient implementation. This is a test vector that contains values that are edge cases on such an isomorphic group.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04572eab7376d052dfc40923db25342ea9cbfce4b8581e104a4c8f37c94a700ec5dc05a481b2b695320c6f1ad2dd8628633cdb75a91245c265", "wx": "572eab7376d052dfc40923db25342ea9cbfce4b8581e104a4c8f37c9", "wy": "4a700ec5dc05a481b2b695320c6f1ad2dd8628633cdb75a91245c265"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004572eab7376d052dfc40923db25342ea9cbfce4b8581e104a4c8f37c94a700ec5dc05a481b2b695320c6f1ad2dd8628633cdb75a91245c265", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFcuq3N20FLfxAkj2yU0LqnL/OS4\nWB4QSkyPN8lKcA7F3AWkgbK2lTIMbxrS3YYoYzzbdakSRcJl\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021c139c78243a6e36e124d5f5e14b4cb8754abdf20ff1a501d5666a428f", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303d021ccb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021cc424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303f021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e028000cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3028000c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30400000021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3043498177303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422500303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "3040303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30432222498177021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304222212500021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3046221f021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30004deadbeef021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3043021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32222498177021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d322212500021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "3046021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3221f021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046aa00bb00cd00303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044aa02aabb303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30462225aa00bb00cd00021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "30442223aa02aabb021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32225aa00bb00cd00021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32223aa02aabb021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30422280021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30000021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32280021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30422280031d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30000021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32280031d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3042300102303d1d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d1d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d511000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d511005000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30403000021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51103000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3041021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110bf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3040303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f02811d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d302811d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30400282001d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30282001d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021e00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021c00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021e00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021c00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30430285010000001d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3043021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30285010000001d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047028901000000000000001d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3028901000000000000001d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304202847fffffff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d302847fffffff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30420284ffffffff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30284ffffffff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30430285ffffffffff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3043021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30285ffffffffff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30460288ffffffffffffffff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3046021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30288ffffffffffffffff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e02ff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d302ff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d302", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021f00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30000021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021f00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021f000000cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021f000000c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30000021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021f00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30500021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021f00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51100500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e001d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e011d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e031d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e041d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303eff1d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3001d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3011d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3031d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3041d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3ff1d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d30200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "30422221020100021ccb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "3042021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d32221020100021cc424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d02cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d02c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af263146004896153021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5190", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021c00cb68ac9765c7641785df237e9951e1429581879af2631460048961021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021c00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d51", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021eff00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021eff00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01a329e1418c0aca9daff753a40f22dcdb669843e66041d103aa30f572021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021cf3a777ed3f83fd915bc6f3592380e5a9c46acb4f848457bc5ee1ce34021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff349753689a389be87a20dc8166ae1ebd6a7e78650d9ceb9ffb769e2d021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c0c588812c07c026ea4390ca6dc7f1a563b9534b07b7ba843a11e31cc021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe5cd61ebe73f535625008ac5bf0dd23249967bc199fbe2efc55cf0a8e021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c349753689a389be87a20dc8166ae1ebd6a7e78650d9ceb9ffb769e2d021d00c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d019be5f1301218962b2f5a6a69a0553ebc576f8686ea187771e4e4e4af", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021cec6387dbc591c91edb2a0a1eb4b3478ab5420df00e5afe2a9995bd71", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021dff3bdb437a142ad05afabdc5bbd57bbcdc79a735c483c64531c0c2aef0", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021dfe641a0ecfede769d4d0a595965faac143a890797915e7888e1b1b1b51", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021d01c424bc85ebd52fa505423a442a8443238658ca3b7c39bace3f3d5110", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00cb68ac9765c7641785df237e9951e1429581879af2631460048961d3021c3bdb437a142ad05afabdc5bbd57bbcdc79a735c483c64531c0c2aef0", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a00201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c1000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3935333838", "sig": "303d021c0e7ecab2276f035c0dc70520ebd5ae3cb7b7a8f21fa5687eee92c462021d0085a85332f8c899b53d43091b02e6956b391817e175a8b1f40dca7e00", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32323534363238393834", "sig": "303d021c2fc2ef9f7663f66f13b04e49f206c22441eb3ee1917b8bf81a9b5376021d00d1df3dd0270e5884e9848ea2812b66f5015be96d2585fed3957b313c", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "31383237383738363130", "sig": "303e021d00c682587bf43e0c954eb58bbcfeb94dfac8bad404995ac26e8e51ff20021d0092bf10da10324cc322f79c412daed305b275fc1993bf3af523ded62a", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "31353138383631373039", "sig": "303d021c025d1cf16f03341f3c8d16a77839b5c1d696363dae898d91e14ad522021d00b7379cc35c97a8f08b0efb32ee82ce0e1911695d372ee6d679ec5466", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32313239323333343232", "sig": "303e021d00c0c949e0f0f8571802ea7e02617ca925b95d290a174f686d80bba1d4021d00b966173ce3f13ca54ccdc8a249fea72e3260ad3e7854a731051d8c03", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "31323231393739303539", "sig": "303d021c5d57f14275947c9bffea66f9f30ae203194535f8020c42bbc1efec73021d00c358651ba2527c32d858657ccf08e535d32851fbd8c35477ba175680", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "32333032353637363131", "sig": "303c021c133bb12f1096989dc867d874f675fdf9e97975c2e22c71e44f59bf35021c6557eeb2b5b4f1f7c85184dcc653850c34b1c3480d2f32d1567c25c1", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31313035383638343732", "sig": "303d021d00d1dc7d1cc886ca90e0d960b6c7fda92ed582ec616c1c79b171ef3108021c41a7d21438e7c34b27176005ef67c04a63f362d2daf10b62c53b88b4", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31343636363134343432", "sig": "303e021d009ec3b7b4a0f1235169b64a20584d36b96bb7a2bde00d23163cc3b1bf021d00ac32ef3b9e948d967f96cd08507809e3b9a0e093be3e76b818331dd5", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "343431393536343230", "sig": "303d021c392ae381da4ccfd9d5ad093d49b22d579411f7c1cd04e88473ab6ef5021d00a2898b8bf120d18e4ee0d15c419044324de3c0927ee90fd6f38711f4", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "31393639343836303634", "sig": "303d021d00a37b0cc87f6d4620303030d7ac4a6572f94c0cf44f0a035c0e59d07c021c30c7acd8db9e72012208f08db5381a1e54fa098c0314a09a3058c421", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323335363732383833", "sig": "303d021c62f660d93a45dfa3b661f8b6a4d5e06e5a1ee8a8855abafa4073b513021d00d6978f5da8afcc5b395fd4b5f3c0fdb7a2689e6de46d08fb9de71860", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "32323537383433373033", "sig": "303c021c61af9a2ca20503fd62bc3c6f8434995c6cf3037eb6f9ff621348cf53021c09f8f647138769548db460efdd8323f8cad18a7071d3d04d6ad33d82", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "393434353030393436", "sig": "303d021c0e459cfe37017c8b605e38bf5d25176576d475fa88ded27be26abca7021d00b1bb6a60ccc3d48e8d1d4c53f90142806e44d9949ebcaa05b83e20f4", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "31363837373839343130", "sig": "303e021d00d0184882bdd6fa09996c2fecf3cd26ed86a3ce15987e06db850b8b2b021d00cff072b27c33f91681d3e95a47bec000cc96c5dc91f68eccc21ca3c4", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "333933323631323238", "sig": "303c021c357e96ab54f4dbb3a6a3a9d1ede6df5294639aedfdea96e7ffc9da31021c24eb6b7f55906739313ea2665a0504a3b0bf7a9b329c690f4a2edf51", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "31303733353731303935", "sig": "303d021c0baaf79d5235e3268e55431cbd790046c2581ebd3f8b90627bd46b8b021d0083d56f6b56ca9381b14ca888281b481cf828e9b43b0d418108e82d58", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "3630383837343734", "sig": "303c021c7ebef1ad41de9434eb3f6f83338f0109666c264d89123342b0900f05021c78a3a9fa7201c48f928344004a1f518053099df86908de29eeb76a4c", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "31313932353639393632", "sig": "303e021d009f20abeff0af965da9c51c99507cf5f91d75f23fe02b61150296167c021d00bf6fed8a8ef726f2f6629c4e4b50b3c2ce14ff439fe9bfe6157868d4", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "3930303736303933", "sig": "303c021c5ad67ff0ddf8cb88407974f83fea43d9d147cbb23dba261fadadaedb021c7fd31ccc4b3605db42b700a245df8fc60efbf1406afb4d6c8c16e0a3", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "31363032383336313337", "sig": "303c021c40e0bd9a2fa5a88678c85c3f4d27e2ae2dd046f29a3639ec2ff2511a021c16cd49215cb00533fd139ae1d3631d45fb06d5ecd1138ab46c48a45c", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32303830323435363734", "sig": "303d021d009a705ddc8767e54cec4d1c37a2460b3c0b31e9811c3a427526499c01021c36a6b9dcd7c8a81e34afdf845c4cc0e73455011bbcd7987f887258ba", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "31373938323734363539", "sig": "303d021d00a51a625b99568d003e4b96e693136ba75221e8e56c9ab5e9ec6816ee021c7e208918785516cda7cf70870dd812e80e8f9f1b5248d919b1ff1d06", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31313535353737373230", "sig": "303d021c4720f937100df52e6ae1baf40f8bc950e5af2b1f947d0417804a8225021d008b822273fb5d473c9c88aa3c8ddea167619cda12ee41ce65de268a75", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343332343734363634", "sig": "303d021c3d281d98b6a676a6eda7570d7b4f9a08e924c71afd2cb6e062a7ebfd021d008446e42747a352518ef68eff055695b4766bdc2ad8d3fa97bc202b43", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "32343137323832323737", "sig": "303c021c4d3469e939ffead941e3cbcebca3bcb6f3c029641f97700e02817738021c42cb87a10ea17c0e58c3822f6ae1f3a8918d86a8325def4a8c8082f2", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "3737383734373731", "sig": "303c021c21e51b79e8554e22937c3e5b1983b37762591e21f5706e5c1982a50c021c4bcdbd23b0a471db84d1ee3edf7677bbb14307ecc5e1023174ec5b8c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a0ef7db1bee0aedb5a5634f4f3b1b88d97d2a07f806a718efe19014daee1043f9e929c32d74ab0e4eeba2623f17ba281b6be87745b59f60e", "wx": "00a0ef7db1bee0aedb5a5634f4f3b1b88d97d2a07f806a718efe19014d", "wy": "00aee1043f9e929c32d74ab0e4eeba2623f17ba281b6be87745b59f60e"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004a0ef7db1bee0aedb5a5634f4f3b1b88d97d2a07f806a718efe19014daee1043f9e929c32d74ab0e4eeba2623f17ba281b6be87745b59f60e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABKDvfbG+4K7bWlY09POxuI2X0qB/\ngGpxjv4ZAU2u4QQ/npKcMtdKsOTuuiYj8Xuigba+h3RbWfYO\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 257, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00dbeedf884b0c29fbcd51d9212d5f021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939c", "result": "valid", "flags": []}, {"tcId": 258, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939c", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043adda407bad7f593e83d7d484fd14c23dda17f8d460c222aa7257577cd62443b2b770291f65904dacf75ff975f1a667187e0e4f50c14889c", "wx": "3adda407bad7f593e83d7d484fd14c23dda17f8d460c222aa7257577", "wy": "00cd62443b2b770291f65904dacf75ff975f1a667187e0e4f50c14889c"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043adda407bad7f593e83d7d484fd14c23dda17f8d460c222aa7257577cd62443b2b770291f65904dacf75ff975f1a667187e0e4f50c14889c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABDrdpAe61/WT6D19SE/RTCPdoX+N\nRgwiKqcldXfNYkQ7K3cCkfZZBNrPdf+XXxpmcYfg5PUMFIic\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 259, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939e021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939d", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0424587ce1dbff281dcab1794519806281ad4e0997492510677fb651069296996e83b808676cbf6f28c92b84303314b63a0308134f222d0ec2", "wx": "24587ce1dbff281dcab1794519806281ad4e0997492510677fb65106", "wy": "009296996e83b808676cbf6f28c92b84303314b63a0308134f222d0ec2"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000424587ce1dbff281dcab1794519806281ad4e0997492510677fb651069296996e83b808676cbf6f28c92b84303314b63a0308134f222d0ec2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABCRYfOHb/ygdyrF5RRmAYoGtTgmX\nSSUQZ3+2UQaSlplug7gIZ2y/byjJK4QwMxS2OgMIE08iLQ7C\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 260, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c63f0e34258bb9061547906d0c3827c504422c139e6d6e1078b37aa44", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c45c51d5a9b213e41ca6f15cb8aa1bc0b8b73d3a8a23a14f5a3da4dfbc78cc6176d3b831e68800671768043c11bf63a695918df6ec87378a", "wx": "00c45c51d5a9b213e41ca6f15cb8aa1bc0b8b73d3a8a23a14f5a3da4df", "wy": "00bc78cc6176d3b831e68800671768043c11bf63a695918df6ec87378a"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004c45c51d5a9b213e41ca6f15cb8aa1bc0b8b73d3a8a23a14f5a3da4dfbc78cc6176d3b831e68800671768043c11bf63a695918df6ec87378a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMRcUdWpshPkHKbxXLiqG8C4tz06\niiOhT1o9pN+8eMxhdtO4MeaIAGcXaAQ8Eb9jppWRjfbshzeK\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 261, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c6eb1fbfa8df87d4fa10c833f7dd1bbe7ef0144ff71537975378f91ec", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0436a5344da08a421edc6c3beb7de97a7559fc101c1489ff2b5036d8f6207bf4666e4df606bd0d9823a52b58ddfdfc1da70513c5f9990f8085", "wx": "36a5344da08a421edc6c3beb7de97a7559fc101c1489ff2b5036d8f6", "wy": "207bf4666e4df606bd0d9823a52b58ddfdfc1da70513c5f9990f8085"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000436a5344da08a421edc6c3beb7de97a7559fc101c1489ff2b5036d8f6207bf4666e4df606bd0d9823a52b58ddfdfc1da70513c5f9990f8085", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABDalNE2gikIe3Gw7633penVZ/BAc\nFIn/K1A22PYge/Rmbk32Br0NmCOlK1jd/fwdpwUTxfmZD4CF\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 262, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044095c095a9648951da352b837f368e0be67d79fd57eadfffeddfb455ccdcfabea19e96d4d20e42b8ae23c2519426018e25a64dea85d8a68b", "wx": "4095c095a9648951da352b837f368e0be67d79fd57eadfffeddfb455", "wy": "00ccdcfabea19e96d4d20e42b8ae23c2519426018e25a64dea85d8a68b"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00044095c095a9648951da352b837f368e0be67d79fd57eadfffeddfb455ccdcfabea19e96d4d20e42b8ae23c2519426018e25a64dea85d8a68b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABECVwJWpZIlR2jUrg382jgvmfXn9\nV+rf/+3ftFXM3Pq+oZ6W1NIOQriuI8JRlCYBjiWmTeqF2KaL\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 263, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cc352ac48aacb6495ec3831b21ccd4d3197136292bf6f20f2280256664321991e67f7dbc22602ecbdb3122edce5ff85d923143cecc0d4f6d", "wx": "00cc352ac48aacb6495ec3831b21ccd4d3197136292bf6f20f22802566", "wy": "64321991e67f7dbc22602ecbdb3122edce5ff85d923143cecc0d4f6d"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004cc352ac48aacb6495ec3831b21ccd4d3197136292bf6f20f2280256664321991e67f7dbc22602ecbdb3122edce5ff85d923143cecc0d4f6d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMw1KsSKrLZJXsODGyHM1NMZcTYp\nK/byDyKAJWZkMhmR5n99vCJgLsvbMSLtzl/4XZIxQ87MDU9t\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 264, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 265, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a0020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049148f29c67f83c705eefb59c92954775f90c15e225da2e996abcdd1dc9db1aa1e15277c4555d24118239e53fd2f0b5e7ea807eb3de1ee350", "wx": "009148f29c67f83c705eefb59c92954775f90c15e225da2e996abcdd1d", "wy": "00c9db1aa1e15277c4555d24118239e53fd2f0b5e7ea807eb3de1ee350"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00049148f29c67f83c705eefb59c92954775f90c15e225da2e996abcdd1dc9db1aa1e15277c4555d24118239e53fd2f0b5e7ea807eb3de1ee350", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABJFI8pxn+DxwXu+1nJKVR3X5DBXi\nJdoumWq83R3J2xqh4VJ3xFVdJBGCOeU/0vC15+qAfrPeHuNQ\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 266, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020101021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5ba6a26", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049bf045a43a5f14d5e412ee181f111d6e53961120531f3c50ca701e78be9eb95146f4f2be96949976a7aa49d31593a7da2edd907652398c3a", "wx": "009bf045a43a5f14d5e412ee181f111d6e53961120531f3c50ca701e78", "wy": "00be9eb95146f4f2be96949976a7aa49d31593a7da2edd907652398c3a"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00049bf045a43a5f14d5e412ee181f111d6e53961120531f3c50ca701e78be9eb95146f4f2be96949976a7aa49d31593a7da2edd907652398c3a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABJvwRaQ6XxTV5BLuGB8RHW5TlhEg\nUx88UMpwHni+nrlRRvTyvpaUmXanqknTFZOn2i7dkHZSOYw6\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 267, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020102021d009dfe5cfd9b02fe7a6f747bf31dd581d0a93cfecc66a1173d611dfd3c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0487739e2821ed9567e88702fa8c6d083c97c1f3f1eb32d13f751fb0736d02eba05e8cb94672d09ebc11051d52ec7bd4dc7767301b67034212", "wx": "0087739e2821ed9567e88702fa8c6d083c97c1f3f1eb32d13f751fb073", "wy": "6d02eba05e8cb94672d09ebc11051d52ec7bd4dc7767301b67034212"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000487739e2821ed9567e88702fa8c6d083c97c1f3f1eb32d13f751fb0736d02eba05e8cb94672d09ebc11051d52ec7bd4dc7767301b67034212", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABIdznigh7ZVn6IcC+oxtCDyXwfPx\n6zLRP3UfsHNtAuugXoy5RnLQnrwRBR1S7HvU3HdnMBtnA0IS\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 268, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c43e235748bd3b1bfa14c92234a90261acc3e9086810801a36746bcee", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041a515cbe957bfc070e4c4a75d6fd5e7c15b1e255eb42fead06c9d2636252cc0d234318394df7db65b0a52e06953ca6c21ec95774d39efdc9", "wx": "1a515cbe957bfc070e4c4a75d6fd5e7c15b1e255eb42fead06c9d263", "wy": "6252cc0d234318394df7db65b0a52e06953ca6c21ec95774d39efdc9"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00041a515cbe957bfc070e4c4a75d6fd5e7c15b1e255eb42fead06c9d2636252cc0d234318394df7db65b0a52e06953ca6c21ec95774d39efdc9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABBpRXL6Ve/wHDkxKddb9XnwVseJV\n60L+rQbJ0mNiUswNI0MYOU3322WwpS4GlTymwh7JV3TTnv3J\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 269, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302e020d1033e67e37b32b445580bf4efb021d00a8bdf46532d8136beb21dbf178090c7e7dad2caa8eb52cef8d830fd8", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d6a16e194e12b96db8e1bb0250d950f7b3129b14bba0efb157c4423e625a0c8c20838bd97fbc89f1670028754a09ad28f62de5eea6e07bc1", "wx": "00d6a16e194e12b96db8e1bb0250d950f7b3129b14bba0efb157c4423e", "wy": "625a0c8c20838bd97fbc89f1670028754a09ad28f62de5eea6e07bc1"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004d6a16e194e12b96db8e1bb0250d950f7b3129b14bba0efb157c4423e625a0c8c20838bd97fbc89f1670028754a09ad28f62de5eea6e07bc1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABNahbhlOErltuOG7AlDZUPezEpsU\nu6DvsVfEQj5iWgyMIIOL2X+8ifFnACh1SgmtKPYt5e6m4HvB\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 270, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302202020102021c73168a8994e5f71793081cb7afbe3c0af4bf7aa336cf9de31ef85314", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c012950d074bb01b0a1988a5b59b959104275baf757e53029b046a1542f50fe27f3ebac9036558ef30ebcb812027bf0ef46cda51969541bb", "wx": "00c012950d074bb01b0a1988a5b59b959104275baf757e53029b046a15", "wy": "42f50fe27f3ebac9036558ef30ebcb812027bf0ef46cda51969541bb"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004c012950d074bb01b0a1988a5b59b959104275baf757e53029b046a1542f50fe27f3ebac9036558ef30ebcb812027bf0ef46cda51969541bb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMASlQ0HS7AbChmIpbWblZEEJ1uv\ndX5TApsEahVC9Q/ifz66yQNlWO8w68uBICe/DvRs2lGWlUG7\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 271, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302d020d062522bbd3ecbe7c39e93e7c24021c73168a8994e5f71793081cb7afbe3c0af4bf7aa336cf9de31ef85314", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d15c13a1be99d9eb77d688104a18e24242d205a4026f4a65629e59ee7e3ddf9abbb7d532b6e81a6e11f30d5b55feb8ee707c4fedf99c0607", "wx": "00d15c13a1be99d9eb77d688104a18e24242d205a4026f4a65629e59ee", "wy": "7e3ddf9abbb7d532b6e81a6e11f30d5b55feb8ee707c4fedf99c0607"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004d15c13a1be99d9eb77d688104a18e24242d205a4026f4a65629e59ee7e3ddf9abbb7d532b6e81a6e11f30d5b55feb8ee707c4fedf99c0607", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABNFcE6G+mdnrd9aIEEoY4kJC0gWk\nAm9KZWKeWe5+Pd+au7fVMrboGm4R8w1bVf647nB8T+35nAYH\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 272, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7931f021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043fe01972c0622ea812d30652c9fe2febee708123b1626d744f87db0da572c7e1e3a48195e6221d983f782fdc9e7c55bd5fdf7b679b0f8756", "wx": "3fe01972c0622ea812d30652c9fe2febee708123b1626d744f87db0d", "wy": "00a572c7e1e3a48195e6221d983f782fdc9e7c55bd5fdf7b679b0f8756"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043fe01972c0622ea812d30652c9fe2febee708123b1626d744f87db0da572c7e1e3a48195e6221d983f782fdc9e7c55bd5fdf7b679b0f8756", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABD/gGXLAYi6oEtMGUsn+L+vucIEj\nsWJtdE+H2w2lcsfh46SBleYiHZg/eC/cnnxVvV/fe2ebD4dW\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 273, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a020101", "result": "valid", "flags": []}, {"tcId": 274, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d4b6e5112406fb743b6bb55f49ea2030d904420831ebddacd67bba89652265384b75d850e7c27f4e33ed6c576df0ff969470a9ef25ffafcd", "wx": "00d4b6e5112406fb743b6bb55f49ea2030d904420831ebddacd67bba89", "wy": "652265384b75d850e7c27f4e33ed6c576df0ff969470a9ef25ffafcd"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004d4b6e5112406fb743b6bb55f49ea2030d904420831ebddacd67bba89652265384b75d850e7c27f4e33ed6c576df0ff969470a9ef25ffafcd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABNS25REkBvt0O2u1X0nqIDDZBEII\nMevdrNZ7uollImU4S3XYUOfCf04z7WxXbfD/lpRwqe8l/6/N\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 275, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c6be09a551321b343150c1812bae87dcc688b5e25b6ef5e51d2d3c9cf021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0416c2129d54b93479b56a9ff5b83e4c750bb4f33ee1e70f38b5449f2d34ccaf79c51c7dff3a7f9a05cd15a396e0cffe25421c37e9b80e1489", "wx": "16c2129d54b93479b56a9ff5b83e4c750bb4f33ee1e70f38b5449f2d", "wy": "34ccaf79c51c7dff3a7f9a05cd15a396e0cffe25421c37e9b80e1489"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000416c2129d54b93479b56a9ff5b83e4c750bb4f33ee1e70f38b5449f2d34ccaf79c51c7dff3a7f9a05cd15a396e0cffe25421c37e9b80e1489", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABBbCEp1UuTR5tWqf9bg+THULtPM+\n4ecPOLVEny00zK95xRx9/zp/mgXNFaOW4M/+JUIcN+m4DhSJ\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 276, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c6be09a551321b343150c1812bae87dcc688b5e25b6ef5e51d2d3c9d0021c6be09a551321b343150c1812bae87dcc688b5e25b6ef5e51d2d3c9cf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0465aba0e4427a0ae558721a5a8e72cb3762eb50223bbe4c41a450fe49c81d3ae486478b4298c943283d2ec2130bac22fabc52f743b1ab7fa7", "wx": "65aba0e4427a0ae558721a5a8e72cb3762eb50223bbe4c41a450fe49", "wy": "00c81d3ae486478b4298c943283d2ec2130bac22fabc52f743b1ab7fa7"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000465aba0e4427a0ae558721a5a8e72cb3762eb50223bbe4c41a450fe49c81d3ae486478b4298c943283d2ec2130bac22fabc52f743b1ab7fa7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABGWroORCegrlWHIaWo5yyzdi61Ai\nO75MQaRQ/knIHTrkhkeLQpjJQyg9LsITC6wi+rxS90Oxq3+n\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 277, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c6be09a551321b343150c1812bae87dcc688b5e25b6ef5e51d2d3c9d0021c6be09a551321b343150c1812bae87dcc688b5e25b6ef5e51d2d3c9d0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0455a7b0100613fabd957b42600835c6d42e01e04252593bdde3b1727887708a05aba2f93f1a1e1ecb703ec9a8ee6d6013a101d397012a8cce", "wx": "55a7b0100613fabd957b42600835c6d42e01e04252593bdde3b17278", "wy": "0087708a05aba2f93f1a1e1ecb703ec9a8ee6d6013a101d397012a8cce"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000455a7b0100613fabd957b42600835c6d42e01e04252593bdde3b1727887708a05aba2f93f1a1e1ecb703ec9a8ee6d6013a101d397012a8cce", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFWnsBAGE/q9lXtCYAg1xtQuAeBC\nUlk73eOxcniHcIoFq6L5PxoeHstwPsmo7m1gE6EB05cBKozO\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 278, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041ada54dc015861680d8bb2d311b90e82db75aa9e8217b92611fa03cb84c611551197298b3274875cb94686e758f0a1a9675c0bc157451a76", "wx": "1ada54dc015861680d8bb2d311b90e82db75aa9e8217b92611fa03cb", "wy": "0084c611551197298b3274875cb94686e758f0a1a9675c0bc157451a76"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00041ada54dc015861680d8bb2d311b90e82db75aa9e8217b92611fa03cb84c611551197298b3274875cb94686e758f0a1a9675c0bc157451a76", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABBraVNwBWGFoDYuy0xG5DoLbdaqe\nghe5JhH6A8uExhFVEZcpizJ0h1y5RobnWPChqWdcC8FXRRp2\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 279, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303c021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a021c628580a9adb02604525b1ee6b135e1a9c745021824582c52385a8173", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c67b6429785334a608dde949a8abe641dbd3601ebce1e675fe71a8e527d2e8727dc4f618493550bb940151bca6826f714c5b31854038f44d", "wx": "00c67b6429785334a608dde949a8abe641dbd3601ebce1e675fe71a8e5", "wy": "27d2e8727dc4f618493550bb940151bca6826f714c5b31854038f44d"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004c67b6429785334a608dde949a8abe641dbd3601ebce1e675fe71a8e527d2e8727dc4f618493550bb940151bca6826f714c5b31854038f44d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMZ7ZCl4UzSmCN3pSair5kHb02Ae\nvOHmdf5xqOUn0uhyfcT2GEk1ULuUAVG8poJvcUxbMYVAOPRN\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 280, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041dcc7a5ad111a33627f92dd875ba4a06f6a7c2befdd1050488d057a7341cae0be72a99776db5bd79b463e2d3882764af9c0245d084a3342d", "wx": "1dcc7a5ad111a33627f92dd875ba4a06f6a7c2befdd1050488d057a7", "wy": "341cae0be72a99776db5bd79b463e2d3882764af9c0245d084a3342d"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00041dcc7a5ad111a33627f92dd875ba4a06f6a7c2befdd1050488d057a7341cae0be72a99776db5bd79b463e2d3882764af9c0245d084a3342d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABB3MelrREaM2J/kt2HW6Sgb2p8K+\n/dEFBIjQV6c0HK4L5yqZd221vXm0Y+LTiCdkr5wCRdCEozQt\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 281, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a021d008fd6231c198244597165756e4e8b5265e0b9d2dcf3e9d317c3c50d15", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bdf708a01c6a814728d394b7f29bf6579734862d8af8e6ff786fbe49901cd462946e5e36cc97c9896df2e18177456d282a7a26a38084c086", "wx": "00bdf708a01c6a814728d394b7f29bf6579734862d8af8e6ff786fbe49", "wy": "00901cd462946e5e36cc97c9896df2e18177456d282a7a26a38084c086"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004bdf708a01c6a814728d394b7f29bf6579734862d8af8e6ff786fbe49901cd462946e5e36cc97c9896df2e18177456d282a7a26a38084c086", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABL33CKAcaoFHKNOUt/Kb9leXNIYt\nivjm/3hvvkmQHNRilG5eNsyXyYlt8uGBd0VtKCp6JqOAhMCG\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 282, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d00b6ea09c6ec5e0484b94f25d890145b0ae3ffbb98b716addd92debdce", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040c531fb3d996faa22407df1305ff6ae0bfe94e1c2022f4730d0f8a4abd8073950459562e539ac0895433757e25209b12534ff30fe3d37c71", "wx": "0c531fb3d996faa22407df1305ff6ae0bfe94e1c2022f4730d0f8a4a", "wy": "00bd8073950459562e539ac0895433757e25209b12534ff30fe3d37c71"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040c531fb3d996faa22407df1305ff6ae0bfe94e1c2022f4730d0f8a4abd8073950459562e539ac0895433757e25209b12534ff30fe3d37c71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABAxTH7PZlvqiJAffEwX/auC/6U4c\nICL0cw0Pikq9gHOVBFlWLlOawIlUM3V+JSCbElNP8w/j03xx\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 283, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c2f62a9cf48e3ca602eef4e33afa43f2dceb922a40a67de79f7b1ae38", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046782954082418e0002a0812672ac2123b6334b341340555096bcf6c61f6fa1a8fea617d9dda14461d63aa448f205a39b25501a6b1d42ee5f", "wx": "6782954082418e0002a0812672ac2123b6334b341340555096bcf6c6", "wy": "1f6fa1a8fea617d9dda14461d63aa448f205a39b25501a6b1d42ee5f"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00046782954082418e0002a0812672ac2123b6334b341340555096bcf6c61f6fa1a8fea617d9dda14461d63aa448f205a39b25501a6b1d42ee5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABGeClUCCQY4AAqCBJnKsISO2M0s0\nE0BVUJa89sYfb6Go/qYX2d2hRGHWOqRI8gWjmyVQGmsdQu5f\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 284, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c618dfc54408bec1cb37c7ee52b60adbc8d3a6c26457c39d013e88e81", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045b5e6eaba7597ae641420ace6af2575839f161b27b91b270f18bf7d0496ab3c3072fa6ee5578fc814f74d148ecbc2a98cfdc5d40ec7e6980", "wx": "5b5e6eaba7597ae641420ace6af2575839f161b27b91b270f18bf7d0", "wy": "496ab3c3072fa6ee5578fc814f74d148ecbc2a98cfdc5d40ec7e6980"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00045b5e6eaba7597ae641420ace6af2575839f161b27b91b270f18bf7d0496ab3c3072fa6ee5578fc814f74d148ecbc2a98cfdc5d40ec7e6980", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFtebqunWXrmQUIKzmryV1g58WGy\ne5GycPGL99BJarPDBy+m7lV4/IFPdNFI7LwqmM/cXUDsfmmA\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 285, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c010de57124c0930ef800e764b5585927977e2ad2d8b82e7cb648af52", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048e661a06ad55b5227801ea4309a72b9cd94973bc873c0405e1247d1e64898b822c363cac8821302de38a914268aaa67db2561878f0f90a02", "wx": "008e661a06ad55b5227801ea4309a72b9cd94973bc873c0405e1247d1e", "wy": "64898b822c363cac8821302de38a914268aaa67db2561878f0f90a02"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00048e661a06ad55b5227801ea4309a72b9cd94973bc873c0405e1247d1e64898b822c363cac8821302de38a914268aaa67db2561878f0f90a02", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABI5mGgatVbUieAHqQwmnK5zZSXO8\nhzwEBeEkfR5kiYuCLDY8rIghMC3jipFCaKqmfbJWGHjw+QoC\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 286, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c033ef5010beced04c4928868513ed1878ce677a6ed810e9b99dd9794", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b3d2b93f1488657262140f96c108aa0485939bd99440240a7a7d54e388968174b061853739f8b0471c76126539dc57cc6d7c1f539f686674", "wx": "00b3d2b93f1488657262140f96c108aa0485939bd99440240a7a7d54e3", "wy": "0088968174b061853739f8b0471c76126539dc57cc6d7c1f539f686674"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004b3d2b93f1488657262140f96c108aa0485939bd99440240a7a7d54e388968174b061853739f8b0471c76126539dc57cc6d7c1f539f686674", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABLPSuT8UiGVyYhQPlsEIqgSFk5vZ\nlEAkCnp9VOOIloF0sGGFNzn4sEccdhJlOdxXzG18H1OfaGZ0\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 287, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c067dea0217d9da09892510d0a27da30f19ccef4ddb021d3733bb2f28", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ba830dbf83075cd182bc9322c1f6299a4ce3cf4ddde0e6fcee50f0d62b153f6f377a88809c9dd50d8d61eb6794514448165786a7c6558dcc", "wx": "00ba830dbf83075cd182bc9322c1f6299a4ce3cf4ddde0e6fcee50f0d6", "wy": "2b153f6f377a88809c9dd50d8d61eb6794514448165786a7c6558dcc"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004ba830dbf83075cd182bc9322c1f6299a4ce3cf4ddde0e6fcee50f0d62b153f6f377a88809c9dd50d8d61eb6794514448165786a7c6558dcc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABLqDDb+DB1zRgryTIsH2KZpM489N\n3eDm/O5Q8NYrFT9vN3qIgJyd1Q2NYetnlFFESBZXhqfGVY3M\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 288, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c45847e02fd01a3cc9e063f961fb920ab3271ec09996f75bca7fe6d3f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0477f40222e4a79a0fa7e510887e69eba31f6dd7067121dafe739bbe13d0ffab7222cf6d827c51eb53abac506bc0a5d7c1a5a7e1683d49e43e", "wx": "77f40222e4a79a0fa7e510887e69eba31f6dd7067121dafe739bbe13", "wy": "00d0ffab7222cf6d827c51eb53abac506bc0a5d7c1a5a7e1683d49e43e"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000477f40222e4a79a0fa7e510887e69eba31f6dd7067121dafe739bbe13d0ffab7222cf6d827c51eb53abac506bc0a5d7c1a5a7e1683d49e43e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABHf0AiLkp5oPp+UQiH5p66MfbdcG\ncSHa/nObvhPQ/6tyIs9tgnxR61OrrFBrwKXXwaWn4Wg9SeQ+\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 289, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c0b4cbe866d1920634138c8798fcc41479447e5ae760794e1e5797928", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04597b5a3c106b8c4e9a7e7a517cd740e77667c8a2d06c510e5e3b728d9cc249e827f5fff902122eb26badc4a7da6555b489ba98982d388125", "wx": "597b5a3c106b8c4e9a7e7a517cd740e77667c8a2d06c510e5e3b728d", "wy": "009cc249e827f5fff902122eb26badc4a7da6555b489ba98982d388125"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004597b5a3c106b8c4e9a7e7a517cd740e77667c8a2d06c510e5e3b728d9cc249e827f5fff902122eb26badc4a7da6555b489ba98982d388125", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFl7WjwQa4xOmn56UXzXQOd2Z8ii\n0GxRDl47co2cwknoJ/X/+QISLrJrrcSn2mVVtIm6mJgtOIEl\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 290, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c20d72ae339e5620170c90a4ce5bca08ded1700b2b6c80ec612c8d5d1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040f2453e7585cb1392ff4fa11869f8c10b2f9cf4f2a18b866e8f37c2bd1566ef04928797579d40f3310ebaf477a4e78a235861928328634df", "wx": "0f2453e7585cb1392ff4fa11869f8c10b2f9cf4f2a18b866e8f37c2b", "wy": "00d1566ef04928797579d40f3310ebaf477a4e78a235861928328634df"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040f2453e7585cb1392ff4fa11869f8c10b2f9cf4f2a18b866e8f37c2bd1566ef04928797579d40f3310ebaf477a4e78a235861928328634df", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABA8kU+dYXLE5L/T6EYafjBCy+c9P\nKhi4ZujzfCvRVm7wSSh5dXnUDzMQ669Hek54ojWGGSgyhjTf\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 291, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d009d235aa9e9f9c6453e39a78613836ea14c2ddf31c91b747aef010a89", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0410cb3dbce4da518e04eb125cf3b44bef0451bad3e7cbbad5328b85bb358651b478bcf200684fd310e6d14acd23dc2a760475df0f5b8a758c", "wx": "10cb3dbce4da518e04eb125cf3b44bef0451bad3e7cbbad5328b85bb", "wy": "358651b478bcf200684fd310e6d14acd23dc2a760475df0f5b8a758c"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000410cb3dbce4da518e04eb125cf3b44bef0451bad3e7cbbad5328b85bb358651b478bcf200684fd310e6d14acd23dc2a760475df0f5b8a758c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABBDLPbzk2lGOBOsSXPO0S+8EUbrT\n58u61TKLhbs1hlG0eLzyAGhP0xDm0UrNI9wqdgR13w9binWM\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 292, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d009ca7987f3367a9516eca57855098d4aaaf289438d9ad7b39dcc81110", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042c27732aaaa3f8b16664a48a1dd06fc0fe40f65742751e5c04b7eff507804b2dbee79ffe56dc4f4a6062ced6f375b80b5ad2cf3a2921b395", "wx": "2c27732aaaa3f8b16664a48a1dd06fc0fe40f65742751e5c04b7eff5", "wy": "07804b2dbee79ffe56dc4f4a6062ced6f375b80b5ad2cf3a2921b395"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00042c27732aaaa3f8b16664a48a1dd06fc0fe40f65742751e5c04b7eff507804b2dbee79ffe56dc4f4a6062ced6f375b80b5ad2cf3a2921b395", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABCwncyqqo/ixZmSkih3Qb8D+QPZX\nQnUeXAS37/UHgEstvuef/lbcT0pgYs7W83W4C1rSzzopIbOV\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 293, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c7295bc38b76bccd7635d6561d1f053dd9b079419249f94368c8d3133", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048ced556877ee15af314aed5dfc43a00fbb7626fbdc7b81ff7dbea2f898f5e26f7fc3276da2a8e869b0afbc41ef3b40326080aa85ce62c2ab", "wx": "008ced556877ee15af314aed5dfc43a00fbb7626fbdc7b81ff7dbea2f8", "wy": "0098f5e26f7fc3276da2a8e869b0afbc41ef3b40326080aa85ce62c2ab"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00048ced556877ee15af314aed5dfc43a00fbb7626fbdc7b81ff7dbea2f898f5e26f7fc3276da2a8e869b0afbc41ef3b40326080aa85ce62c2ab", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABIztVWh37hWvMUrtXfxDoA+7dib7\n3HuB/32+oviY9eJvf8MnbaKo6Gmwr7xB7ztAMmCAqoXOYsKr\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 294, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d00938f2db2b72061abd7eb6e5c8fe685391e966ec0c769d0c538e0678a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0442b19b22506c4fd89fa28c5909d97f8ffebdc82804dcc7bf6a570ae21a974ee08b484fa05e1fbb89c48c50754ba1e40a658a5ced409c6361", "wx": "42b19b22506c4fd89fa28c5909d97f8ffebdc82804dcc7bf6a570ae2", "wy": "1a974ee08b484fa05e1fbb89c48c50754ba1e40a658a5ced409c6361"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000442b19b22506c4fd89fa28c5909d97f8ffebdc82804dcc7bf6a570ae21a974ee08b484fa05e1fbb89c48c50754ba1e40a658a5ced409c6361", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABEKxmyJQbE/Yn6KMWQnZf4/+vcgo\nBNzHv2pXCuIal07gi0hPoF4fu4nEjFB1S6HkCmWKXO1AnGNh\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c7f907c8e32e60e2ba4033ee7d65f3fe8fd23719c7a9c6f5e52f18c47", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042095e12116cebdd4e8bc1cc184b538b1515f789e3be4b03a4183fae5d0926e446875abdcd12c8239e607961cadd00a2e899d821db11d5679", "wx": "2095e12116cebdd4e8bc1cc184b538b1515f789e3be4b03a4183fae5", "wy": "00d0926e446875abdcd12c8239e607961cadd00a2e899d821db11d5679"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00042095e12116cebdd4e8bc1cc184b538b1515f789e3be4b03a4183fae5d0926e446875abdcd12c8239e607961cadd00a2e899d821db11d5679", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABCCV4SEWzr3U6LwcwYS1OLFRX3ie\nO+SwOkGD+uXQkm5EaHWr3NEsgjnmB5YcrdAKLomdgh2xHVZ5\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c34c3978c3a1dac921f6235c82a02edb9342285469426bb10f82897c4", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0458f82eb2ca6e3474a90e29ac56dcb63d88e669e0a40204e6202af7c5a0e85e4039f343255b4fe4bdc1191a7845bdd7eb908ecd8779a27963", "wx": "58f82eb2ca6e3474a90e29ac56dcb63d88e669e0a40204e6202af7c5", "wy": "00a0e85e4039f343255b4fe4bdc1191a7845bdd7eb908ecd8779a27963"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000458f82eb2ca6e3474a90e29ac56dcb63d88e669e0a40204e6202af7c5a0e85e4039f343255b4fe4bdc1191a7845bdd7eb908ecd8779a27963", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFj4LrLKbjR0qQ4prFbctj2I5mng\npAIE5iAq98Wg6F5AOfNDJVtP5L3BGRp4Rb3X65COzYd5onlj\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d009701d6523d3d3f5b8ac8402680b3cab8966e2651cfc1739fcd3c0749", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0471f2c4a7c3f71311a793458ff12262a863518fb30dbb7a80701030b8b6b08428fabdb69c8a8e9e327daed0795fb84e0d8817086022d3b23b", "wx": "71f2c4a7c3f71311a793458ff12262a863518fb30dbb7a80701030b8", "wy": "00b6b08428fabdb69c8a8e9e327daed0795fb84e0d8817086022d3b23b"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000471f2c4a7c3f71311a793458ff12262a863518fb30dbb7a80701030b8b6b08428fabdb69c8a8e9e327daed0795fb84e0d8817086022d3b23b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABHHyxKfD9xMRp5NFj/EiYqhjUY+z\nDbt6gHAQMLi2sIQo+r22nIqOnjJ9rtB5X7hODYgXCGAi07I7\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c564277fa54371830eb7850278b9699d85bc5905831a42a9bf4d07af3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043cfcf64eece994c35c56e915e4ed1883ba6ec34fe396c11acd8f47d263cdfbaa34401100b5b10af771bb46c0d53446f7aa847956c9363494", "wx": "3cfcf64eece994c35c56e915e4ed1883ba6ec34fe396c11acd8f47d2", "wy": "63cdfbaa34401100b5b10af771bb46c0d53446f7aa847956c9363494"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043cfcf64eece994c35c56e915e4ed1883ba6ec34fe396c11acd8f47d263cdfbaa34401100b5b10af771bb46c0d53446f7aa847956c9363494", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABDz89k7s6ZTDXFbpFeTtGIO6bsNP\n45bBGs2PR9JjzfuqNEARALWxCvdxu0bA1TRG96qEeVbJNjSU\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c7794fbdee638f657ac1e4c65284c144b3efa7bf4109e6cca605c4f4c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c404decbc0697b207fa08982ef0fedb001eeb43f37404dab97a9a7747191bc240dfd440274e06955611f9923fad6949b2cc157a185c8229", "wx": "4c404decbc0697b207fa08982ef0fedb001eeb43f37404dab97a9a77", "wy": "47191bc240dfd440274e06955611f9923fad6949b2cc157a185c8229"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00044c404decbc0697b207fa08982ef0fedb001eeb43f37404dab97a9a7747191bc240dfd440274e06955611f9923fad6949b2cc157a185c8229", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABExATey8BpeyB/oImC7w/tsAHutD\n83QE2rl6mndHGRvCQN/UQCdOBpVWEfmSP61pSbLMFXoYXIIp\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d00b5200da7a45837f5b71c47e1b94c7862a1e4becba30a908ada219487", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047be4b0ea0b15b96f91312c15c81629e40c4418f70b86c5bcdc258fd979cbef8ea2a77ca092db0eb954a9e33e82b9c5f110c8c990b9235a57", "wx": "7be4b0ea0b15b96f91312c15c81629e40c4418f70b86c5bcdc258fd9", "wy": "79cbef8ea2a77ca092db0eb954a9e33e82b9c5f110c8c990b9235a57"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00047be4b0ea0b15b96f91312c15c81629e40c4418f70b86c5bcdc258fd979cbef8ea2a77ca092db0eb954a9e33e82b9c5f110c8c990b9235a57", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABHvksOoLFblvkTEsFcgWKeQMRBj3\nC4bFvNwlj9l5y++Ooqd8oJLbDrlUqeM+grnF8RDIyZC5I1pX\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021c652b78716ed799aec6bacac3a3e0a7bb360f2832493f286d191a626c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0464a64cffa54066499264991e47a0f14bca6319a1c27e1508e2016b56bda7c17a04d9cb88eadb7296cf87dfbfadfe65056837a797d66997dd", "wx": "64a64cffa54066499264991e47a0f14bca6319a1c27e1508e2016b56", "wy": "00bda7c17a04d9cb88eadb7296cf87dfbfadfe65056837a797d66997dd"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000464a64cffa54066499264991e47a0f14bca6319a1c27e1508e2016b56bda7c17a04d9cb88eadb7296cf87dfbfadfe65056837a797d66997dd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABGSmTP+lQGZJkmSZHkeg8UvKYxmh\nwn4VCOIBa1a9p8F6BNnLiOrbcpbPh9+/rf5lBWg3p5fWaZfd\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 302, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d0097c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a793a2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043d4c4e3c5ba7a533c8a3386d6ff77a81351346e1894b2560b406a63ea349775946799eeb274926b4d957328f6c7d50f6760291acdaeb114f", "wx": "3d4c4e3c5ba7a533c8a3386d6ff77a81351346e1894b2560b406a63e", "wy": "00a349775946799eeb274926b4d957328f6c7d50f6760291acdaeb114f"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043d4c4e3c5ba7a533c8a3386d6ff77a81351346e1894b2560b406a63ea349775946799eeb274926b4d957328f6c7d50f6760291acdaeb114f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABD1MTjxbp6UzyKM4bW/3eoE1E0bh\niUslYLQGpj6jSXdZRnme6ydJJrTZVzKPbH1Q9nYCkaza6xFP\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffa021d00aba8d89c2c94ba58e70db786a6181dc0e71d16f3f43d9600fc4c8ff3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b0a76082c35da862a1e2e8626ffa94ed18fcb1d897ec7ab52c322553ff", "wx": "4db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b0", "wy": "00a76082c35da862a1e2e8626ffa94ed18fcb1d897ec7ab52c322553ff"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00044db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b0a76082c35da862a1e2e8626ffa94ed18fcb1d897ec7ab52c322553ff", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABE246KxD8i33XJwJ/hk7nNg9XJtz\n830UlHYXJLCnYILDXahioeLoYm/6lO0Y/LHYl+x6tSwyJVP/\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303d021c7af295e6e4787252f34c527af562ca27214a66f6d6db4fd2c112b564021d00b1d010f74062eeaac0cecb2c3c2c4d288a576bf6f0a00347c6a5b562", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b03060b1e6c89b03e4472fcdb57b3cea6eb3ed2ebfab5fd4c94ca36d00", "wx": "4db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b0", "wy": "3060b1e6c89b03e4472fcdb57b3cea6eb3ed2ebfab5fd4c94ca36d00"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00044db8e8ac43f22df75c9c09fe193b9cd83d5c9b73f37d1494761724b03060b1e6c89b03e4472fcdb57b3cea6eb3ed2ebfab5fd4c94ca36d00", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABE246KxD8i33XJwJ/hk7nNg9XJtz\n830UlHYXJLAwYLHmyJsD5EcvzbV7POpus+0uv6tf1MlMo20A\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "duplication bug", "msg": "313233343030", "sig": "303d021c7af295e6e4787252f34c527af562ca27214a66f6d6db4fd2c112b564021d00b1d010f74062eeaac0cecb2c3c2c4d288a576bf6f0a00347c6a5b562", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042b92268208d522450c42f3fcbda409c3ace2a5f857ea10612c6093f8315eb2d448134e716b032078b68301622e3c2186ab583d976e769feb", "wx": "2b92268208d522450c42f3fcbda409c3ace2a5f857ea10612c6093f8", "wy": "315eb2d448134e716b032078b68301622e3c2186ab583d976e769feb"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00042b92268208d522450c42f3fcbda409c3ace2a5f857ea10612c6093f8315eb2d448134e716b032078b68301622e3c2186ab583d976e769feb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABCuSJoII1SJFDELz/L2kCcOs4qX4\nV+oQYSxgk/gxXrLUSBNOcWsDIHi2gwFiLjwhhqtYPZdudp/r\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a021c2b26a42207a714813b9e70077df698b829d158dbe2c625ba5454b71f", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044d4bd5693d86dd9a6016ba806d8031f94dc8e2d33c6f5871a00b64732a4662f29524ece754828b9d829c0a0724d9bd9d288d21f87e3fb1fa", "wx": "4d4bd5693d86dd9a6016ba806d8031f94dc8e2d33c6f5871a00b6473", "wy": "2a4662f29524ece754828b9d829c0a0724d9bd9d288d21f87e3fb1fa"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00044d4bd5693d86dd9a6016ba806d8031f94dc8e2d33c6f5871a00b64732a4662f29524ece754828b9d829c0a0724d9bd9d288d21f87e3fb1fa", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABE1L1Wk9ht2aYBa6gG2AMflNyOLT\nPG9YcaALZHMqRmLylSTs51SCi52CnAoHJNm9nSiNIfh+P7H6\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04be0efb4841df37abcdcf3f28ddb0d5751a92a0fe7a3e88d1ab02832cbb53ccd66b9c0e424380693d6416fc2e1a3c793a355f7d05f963f435", "wx": "00be0efb4841df37abcdcf3f28ddb0d5751a92a0fe7a3e88d1ab02832c", "wy": "00bb53ccd66b9c0e424380693d6416fc2e1a3c793a355f7d05f963f435"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004be0efb4841df37abcdcf3f28ddb0d5751a92a0fe7a3e88d1ab02832cbb53ccd66b9c0e424380693d6416fc2e1a3c793a355f7d05f963f435", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABL4O+0hB3zerzc8/KN2w1XUakqD+\nej6I0asCgyy7U8zWa5wOQkOAaT1kFvwuGjx5OjVffQX5Y/Q1\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021d00b8eebf6d455e57e0b65de0201bd7b315458133ae5e2ca1b0d721ec3f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bc0272e3693a05e788392c880f9de95c72e293fd1b13f1e22a9907a3699506e4590fa90c6257b1c4e3632ccc486cb833cbbcbf21b4a26041", "wx": "00bc0272e3693a05e788392c880f9de95c72e293fd1b13f1e22a9907a3", "wy": "699506e4590fa90c6257b1c4e3632ccc486cb833cbbcbf21b4a26041"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004bc0272e3693a05e788392c880f9de95c72e293fd1b13f1e22a9907a3699506e4590fa90c6257b1c4e3632ccc486cb833cbbcbf21b4a26041", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABLwCcuNpOgXniDksiA+d6Vxy4pP9\nGxPx4iqZB6NplQbkWQ+pDGJXscTjYyzMSGy4M8u8vyG0omBB\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021d00ac9a90881e9c5204ee79c01df7da62e0a745636f8b1896e95152dc7f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a83bc3e9043cb938dae167bbea2f7d623486f4038df45312e8467bda7363fa58af363a71835da09413c88227849c6f0ffe8e4e40aff51023", "wx": "00a83bc3e9043cb938dae167bbea2f7d623486f4038df45312e8467bda", "wy": "7363fa58af363a71835da09413c88227849c6f0ffe8e4e40aff51023"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004a83bc3e9043cb938dae167bbea2f7d623486f4038df45312e8467bda7363fa58af363a71835da09413c88227849c6f0ffe8e4e40aff51023", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABKg7w+kEPLk42uFnu+ovfWI0hvQD\njfRTEuhGe9pzY/pYrzY6cYNdoJQTyIInhJxvD/6OTkCv9RAj\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021c2b26a42207a714813b9e70077df698b829d158dbe2c625ba5454b720", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043bd08a1c46638564521ded3fa77ce9c95538e49703ebb9f8d36be6f7276ffa128051671f7e4c63e9b8132de9f3389cc525d72682b6019ec3", "wx": "3bd08a1c46638564521ded3fa77ce9c95538e49703ebb9f8d36be6f7", "wy": "276ffa128051671f7e4c63e9b8132de9f3389cc525d72682b6019ec3"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043bd08a1c46638564521ded3fa77ce9c95538e49703ebb9f8d36be6f7276ffa128051671f7e4c63e9b8132de9f3389cc525d72682b6019ec3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABDvQihxGY4VkUh3tP6d86clVOOSX\nA+u5+NNr5vcnb/oSgFFnH35MY+m4Ey3p8zicxSXXJoK2AZ7D\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a760", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a3db2b3e2c62c42bf4fb0e11c2908fd17fe83da3ac9c0980234efdbd3cbeec4027bd7c109b27ae2f7cf04dc65eeaf13faa224d32a20f3163", "wx": "00a3db2b3e2c62c42bf4fb0e11c2908fd17fe83da3ac9c0980234efdbd", "wy": "3cbeec4027bd7c109b27ae2f7cf04dc65eeaf13faa224d32a20f3163"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004a3db2b3e2c62c42bf4fb0e11c2908fd17fe83da3ac9c0980234efdbd3cbeec4027bd7c109b27ae2f7cf04dc65eeaf13faa224d32a20f3163", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABKPbKz4sYsQr9PsOEcKQj9F/6D2j\nrJwJgCNO/b08vuxAJ718EJsnri988E3GXurxP6oiTTKiDzFj\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c33b7e498bcda1a33e61a67af56a36d12df7032255ddf5e1ec65a5669021c58e37518c6e47a84de10ccb254c03693271145f13e00a91237a4a547", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04606ce6f8c77ac17d5b7515d5851eed155ea120cd07ca4277b35b8d365f716b62aee9a81a011bd1d2bceaf37d5f3a61e5f7307e0bb9c892c8", "wx": "606ce6f8c77ac17d5b7515d5851eed155ea120cd07ca4277b35b8d36", "wy": "5f716b62aee9a81a011bd1d2bceaf37d5f3a61e5f7307e0bb9c892c8"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004606ce6f8c77ac17d5b7515d5851eed155ea120cd07ca4277b35b8d365f716b62aee9a81a011bd1d2bceaf37d5f3a61e5f7307e0bb9c892c8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABGBs5vjHesF9W3UV1YUe7RVeoSDN\nB8pCd7NbjTZfcWtirumoGgEb0dK86vN9Xzph5fcwfgu5yJLI\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021c47eb118e0cc1222cb8b2bab72745a932f05ce96e79f4e98be1e2868a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042affbb8269cb7883dada3350394579912ef756a8df6bdd7da35d398e90213d9382b3d5fb9dde82724d38e5678c17e610f417cfe6f7efcd91", "wx": "2affbb8269cb7883dada3350394579912ef756a8df6bdd7da35d398e", "wy": "0090213d9382b3d5fb9dde82724d38e5678c17e610f417cfe6f7efcd91"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00042affbb8269cb7883dada3350394579912ef756a8df6bdd7da35d398e90213d9382b3d5fb9dde82724d38e5678c17e610f417cfe6f7efcd91", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABCr/u4Jpy3iD2tozUDlFeZEu91ao\n32vdfaNdOY6QIT2TgrPV+53egnJNOOVnjBfmEPQXz+b3782R\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021d00b8eebf6d455e57e0b65de0201bd7b315458133ae5e2ca1b0d721ec3f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0419cdd44e2a33113a884558e7ee0efb41bafe1adcdcf95df6de6a25115f428ee998a34856f2ac3f6f39c7237bf1f9de232175d747b5cd97fe", "wx": "19cdd44e2a33113a884558e7ee0efb41bafe1adcdcf95df6de6a2511", "wy": "5f428ee998a34856f2ac3f6f39c7237bf1f9de232175d747b5cd97fe"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a000419cdd44e2a33113a884558e7ee0efb41bafe1adcdcf95df6de6a25115f428ee998a34856f2ac3f6f39c7237bf1f9de232175d747b5cd97fe", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABBnN1E4qMxE6iEVY5+4O+0G6/hrc\n3Pld9t5qJRFfQo7pmKNIVvKsP285xyN78fneIyF110e1zZf+\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021d00ac9a90881e9c5204ee79c01df7da62e0a745636f8b1896e95152dc7f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b72f7c1a3c8562cb8de9925eec041ccc263649c6524762b9f4585ee39df756da08d1274ad72d8cac293aa60d150c77131f9fa28ccdffdfa0", "wx": "00b72f7c1a3c8562cb8de9925eec041ccc263649c6524762b9f4585ee3", "wy": "009df756da08d1274ad72d8cac293aa60d150c77131f9fa28ccdffdfa0"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004b72f7c1a3c8562cb8de9925eec041ccc263649c6524762b9f4585ee39df756da08d1274ad72d8cac293aa60d150c77131f9fa28ccdffdfa0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABLcvfBo8hWLLjemSXuwEHMwmNknG\nUkdiufRYXuOd91baCNEnStctjKwpOqYNFQx3Ex+foozN/9+g\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021c2b26a42207a714813b9e70077df698b829d158dbe2c625ba5454b720", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049d3703b3205b123c903a0446973247c16a88d103fea9d04dd02a702b65186b777b57eadee8154c02fce0e95c3f061468499bac3dc6029e8c", "wx": "009d3703b3205b123c903a0446973247c16a88d103fea9d04dd02a702b", "wy": "65186b777b57eadee8154c02fce0e95c3f061468499bac3dc6029e8c"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00049d3703b3205b123c903a0446973247c16a88d103fea9d04dd02a702b65186b777b57eadee8154c02fce0e95c3f061468499bac3dc6029e8c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABJ03A7MgWxI8kDoERpcyR8FqiNED\n/qnQTdAqcCtlGGt3e1fq3ugVTAL84OlcPwYUaEmbrD3GAp6M\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a760", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043ea572505a48b1bed085953da7d4c963c2c5b6ad99779d9d54ba401294470074e0252da159a0c0d0b2f8d4c242cb94bab2c2020c4b2df499", "wx": "3ea572505a48b1bed085953da7d4c963c2c5b6ad99779d9d54ba4012", "wy": "0094470074e0252da159a0c0d0b2f8d4c242cb94bab2c2020c4b2df499"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00043ea572505a48b1bed085953da7d4c963c2c5b6ad99779d9d54ba401294470074e0252da159a0c0d0b2f8d4c242cb94bab2c2020c4b2df499", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABD6lclBaSLG+0IWVPafUyWPCxbat\nmXednVS6QBKURwB04CUtoVmgwNCy+NTCQsuUurLCAgxLLfSZ\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d021c58e37518c6e47a84de10ccb254c03693271145f13e00a91237a4a547", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd", "wx": "0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d", "wy": "58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABA2QKa0sflz0NAgjsqh9xoyeTOMX\nTB5u/e4SwH1Yqlb3csBybyTGuJ5OzawkNUuemcqj9tN2FALN\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a75f", "result": "invalid", "flags": []}, {"tcId": 320, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c628580a9adb02604525b1ee6b135e1a9c745021824582c52385a8173021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a75f", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d7f16ddb2b382f4170551778727042b637b5368bdcd36932208b4be32", "wx": "0d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d", "wy": "7f16ddb2b382f4170551778727042b637b5368bdcd36932208b4be32"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d7f16ddb2b382f4170551778727042b637b5368bdcd36932208b4be32", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABA2QKa0sflz0NAgjsqh9xoyeTOMX\nTB5u/e4SwH1/Ft2ys4L0FwVRd4cnBCtje1Novc02kyIItL4y\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c753bb40078934081d7bd113ec49b19ef09d1ba33498690516d4d122c021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a75f", "result": "invalid", "flags": []}, {"tcId": 322, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c628580a9adb02604525b1ee6b135e1a9c745021824582c52385a8173021c1ed2753ce0e50ea573ba500559f948838b95889d0fb21af2ce85a75f", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b554fc25e9f098eaf1466c35328c97305d0d4aa0e4462e8baf7a8e7ed08fc40eb01dc855577baea9e3070770616f57b17ea9854cad93881a", "wx": "00b554fc25e9f098eaf1466c35328c97305d0d4aa0e4462e8baf7a8e7e", "wy": "00d08fc40eb01dc855577baea9e3070770616f57b17ea9854cad93881a"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004b554fc25e9f098eaf1466c35328c97305d0d4aa0e4462e8baf7a8e7ed08fc40eb01dc855577baea9e3070770616f57b17ea9854cad93881a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABLVU/CXp8Jjq8UZsNTKMlzBdDUqg\n5EYui696jn7Qj8QOsB3IVVd7rqnjBwdwYW9XsX6phUytk4ga\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "pseudorandom signature", "msg": "", "sig": "303e021d00b982bea80d10816bb450a3faaaed4ed54fb197b3bff95af25d7d3786021d009e6ea2e58713f1304d29debf8559a74a89e018bae28b05556e5482a1", "result": "valid", "flags": []}, {"tcId": 324, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303c021c4dabc5fe962b5f8a6681e94a2165d9b6be1940f20e27ceb73fc4ea7d021c746e9bba7efb90fcecc263c229a16d809d3547c28a26cd71a52abdc5", "result": "valid", "flags": []}, {"tcId": 325, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303d021d0095b11e320007a2e0f8ce00f9058ca9b919e8d6aad544a8f9808b44a1021c15a962019c85a5b1fa7474162d03cd0e528e8b93bcc84920af579f61", "result": "valid", "flags": []}, {"tcId": 326, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303e021d009e4dab9e0b0097e365783fc05f010c160d361df7925b0ddbdfece88b021d008406a365f078f031e6fad6511d69f8a65483c19a5a800c39490f7510", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04802a0f51204ef6a829211bc0740887461ee4aba736e9caee000000007fb931e06300451362d444106eeb5dabddca650fec4be55fc545f7c8", "wx": "00802a0f51204ef6a829211bc0740887461ee4aba736e9caee00000000", "wy": "7fb931e06300451362d444106eeb5dabddca650fec4be55fc545f7c8"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004802a0f51204ef6a829211bc0740887461ee4aba736e9caee000000007fb931e06300451362d444106eeb5dabddca650fec4be55fc545f7c8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABIAqD1EgTvaoKSEbwHQIh0Ye5Kun\nNunK7gAAAAB/uTHgYwBFE2LURBBu612r3cplD+xL5V/FRffI\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c0c93fd7f6dd0b697d5c287ee61aee4dcbedcc20885c1e6215b8b3608021c3bc7a1beccf1a8e83af2f5162fc539a1d062bd639a2fbec512907a27", "result": "valid", "flags": []}, {"tcId": 328, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d009e0b620a2f313ada756463a22988afb6571b3b030a4285b185e1cc80021d00c3eba04c42e64d4028acabcdcb7b2eed1b3cfb560b8d7d14fb26aca3", "result": "valid", "flags": []}, {"tcId": 329, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00a306f500da4f0a30946479936aaf9c637676b0f02d20ae0d981c25eb021c015647f2500bcbe3204bdb804972b841890b4e53196cd8b188993151", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cb320c84f26c00a1b4ad7146914cae126529165de7363d8aef9abd05a397d46b87283176b7f69da1f94615ca4431fc47b2a0e60c00000000", "wx": "00cb320c84f26c00a1b4ad7146914cae126529165de7363d8aef9abd05", "wy": "00a397d46b87283176b7f69da1f94615ca4431fc47b2a0e60c00000000"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004cb320c84f26c00a1b4ad7146914cae126529165de7363d8aef9abd05a397d46b87283176b7f69da1f94615ca4431fc47b2a0e60c00000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMsyDITybAChtK1xRpFMrhJlKRZd\n5zY9iu+avQWjl9Rrhygxdrf2naH5RhXKRDH8R7Kg5gwAAAAA\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c04f00dd44fdd8ae6b08b86ccbdd7d615aa9e498a89b35094c8a9a6fe021c49617a1617c56ce90d41c53eef4e628f24c047a06e02c1f92123441f", "result": "valid", "flags": []}, {"tcId": 331, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c1286f6a7375bf68051e31b2e32b5f6c0988c9189799256e7ce64e291021c52d3c1f9e777f23c17cbc832d0e5a84bb68b13debf393878d1a06498", "result": "valid", "flags": []}, {"tcId": 332, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c582825df2368dcb92fbba3fa6454d149d3b860e3ff326afe36215813021c49334fc6a70418dbc454da6a997bc8376270c3a38863adb2aa70bb0f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040000000081df971744a25ac99472c3ff5a8fc49b86fc9fb570448ff977f2d07c1c9296b2f77478d13d5ab1c63993962f2dd08ee7c313dece", "wx": "0081df971744a25ac99472c3ff5a8fc49b86fc9fb570448ff9", "wy": "77f2d07c1c9296b2f77478d13d5ab1c63993962f2dd08ee7c313dece"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000081df971744a25ac99472c3ff5a8fc49b86fc9fb570448ff977f2d07c1c9296b2f77478d13d5ab1c63993962f2dd08ee7c313dece", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABAAAAACB35cXRKJayZRyw/9aj8Sb\nhvyftXBEj/l38tB8HJKWsvd0eNE9WrHGOZOWLy3QjufDE97O\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c5a11718c90a02459800f109e4e840cc261d782d64e1c8a4712dd9081021d00d283b1c1e110a4620a696fdf74a9c7792352139d54cced8c973d9e7e", "result": "valid", "flags": []}, {"tcId": 334, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00d577f23e592414e351b3928a593c5d2f89f0c72df513bfbc6535babb021c1bb09dd235124a14e0246946f280450f15576912aeb735b73ce828bc", "result": "valid", "flags": []}, {"tcId": 335, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00af8f836e63995dc715a4d3c6842c4e6c6cf4586df76e4659d809eec9021d0085befd0b1bb8ae182c05d071dad180224d22533dce737d4dda74d5d1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3b000000002801d48382861684b8d2cbd7e5989a0d7c15a7e819b573aa", "wx": "0d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3b", "wy": "2801d48382861684b8d2cbd7e5989a0d7c15a7e819b573aa"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3b000000002801d48382861684b8d2cbd7e5989a0d7c15a7e819b573aa", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABA0otPf+H2xvpqd9EeQ70+knF1jf\nNMZfpXem3TsAAAAAKAHUg4KGFoS40svX5ZiaDXwVp+gZtXOq\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c156aa78692c78e9769aba728c9eea78835b55000901ba50794a33efc021d00b9785df40a2213377481311b1a81d310e76341927b8fba0d6e3ec7ad", "result": "valid", "flags": []}, {"tcId": 337, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d009274d46a7ffa1299a372e821bd89728de83ef87c46af67043a634b02021c19e4bbec8b03fa772a3622bf4893e581efadf9d20bd60806d82676b6", "result": "valid", "flags": []}, {"tcId": 338, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00cf6a9cba285e56493cbb462b7b16128a0cf1c7058447945daef34149021c29a687839e8ee03c5372a113733c081f413d1f9405ddfe47e18fcc54", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3bd7c134a9fe419202a79219a0bcff0bafcb066d4a1bc4e20d65134d55", "wx": "0d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3b", "wy": "00d7c134a9fe419202a79219a0bcff0bafcb066d4a1bc4e20d65134d55"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00040d28b4f7fe1f6c6fa6a77d11e43bd3e9271758df34c65fa577a6dd3bd7c134a9fe419202a79219a0bcff0bafcb066d4a1bc4e20d65134d55", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABA0otPf+H2xvpqd9EeQ70+knF1jf\nNMZfpXem3TvXwTSp/kGSAqeSGaC8/wuvywZtShvE4g1lE01V\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c592e54a0ea950ac7cd830f56c7954a769f81aa55e8e101bee19b3b27021c48375fdd4d9014c9b60b63c70bfe98c844be668f2d3a2e259262b945", "result": "valid", "flags": []}, {"tcId": 340, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c1ec0ef4d5bedafe5081f7adae32db4d0aa946f130acedabae26d90dc021c627e81d7eb358f59e8a8630527d4e8946d1cad2196761836d97d953c", "result": "valid", "flags": []}, {"tcId": 341, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c5faf035ed5774eeb0adc187ff485a846aa2abcf1e7f859b1b910f25c021d008bf12a1c00b18f66c228352de49cc4fb827a09fc86f722ce561ba5fa", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04512e581731c9c460bb705b60da976ccb1b0ef421785106ba2ccdd2380f21d5bacdf81c0cb78fa151237db3130ad4def373f3e523398c2cf7", "wx": "512e581731c9c460bb705b60da976ccb1b0ef421785106ba2ccdd238", "wy": "0f21d5bacdf81c0cb78fa151237db3130ad4def373f3e523398c2cf7"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004512e581731c9c460bb705b60da976ccb1b0ef421785106ba2ccdd2380f21d5bacdf81c0cb78fa151237db3130ad4def373f3e523398c2cf7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABFEuWBcxycRgu3BbYNqXbMsbDvQh\neFEGuizN0jgPIdW6zfgcDLePoVEjfbMTCtTe83Pz5SM5jCz3\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303d021c52b2d369f18df56372afe7feb38413f232b4fb9ca16c6f6fedc64189021d00c1b19f137773ef3201cd341c381e4f9449cc0e6c688a351d7a6070b2", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 343, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c5b889d288aaa81674d32006e81279c57ed56a035c878d3e2b687bec3021c0da621d5fa9813263c7f58f8e0155d6f0c330a56c594defc2ebdf0a0", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 344, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00b6f8a80187180aad8a5c896be214314601a1585f2ccb28bc7e8e8f01021d00a90c68c14a67f5d59cec70dc0f473b5c14013b056d12cbc0f7153b1d", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ac55d1b3fed4aee03fa3615d225a9cba5c0284416fbaf9a76135f76217a888395bced34977a8482370d56ebc62aa1ca81bc330f49d4a141d", "wx": "00ac55d1b3fed4aee03fa3615d225a9cba5c0284416fbaf9a76135f762", "wy": "17a888395bced34977a8482370d56ebc62aa1ca81bc330f49d4a141d"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004ac55d1b3fed4aee03fa3615d225a9cba5c0284416fbaf9a76135f76217a888395bced34977a8482370d56ebc62aa1ca81bc330f49d4a141d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABKxV0bP+1K7gP6NhXSJanLpcAoRB\nb7r5p2E192IXqIg5W87TSXeoSCNw1W68YqocqBvDMPSdShQd\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "x-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c16d4a8509c9bce2c73f8db4b73257c7e33f41726c25c4c64546b1dcc021c79ba35a96d2345ad194f391091209dfcced79917e04df3b65f44d1eb", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 346, "comment": "x-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c5da15697bbe4eba7707e349ff3239d508455378113d24e7e1d7a020c021c45be44a570fb530c49d759712c10041345f7c0890a7946d91d32bac6", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 347, "comment": "x-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00c1f8d43479c4f29b19b9b2c7b5747010491440746cc800d5be89b011021d008154348b7c37f0504dca2b115941f7ba5857321eae8f64175be9cbb9", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59b120f96343deb80093ecb7695c2d2a5be9937a258723d78ed00aa1edf", "wx": "008b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59b", "wy": "120f96343deb80093ecb7695c2d2a5be9937a258723d78ed00aa1edf"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00048b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59b120f96343deb80093ecb7695c2d2a5be9937a258723d78ed00aa1edf", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABIsxny7QdKIOxC2GnH+ZvZFG7YJj\nKXv+AE8nxZsSD5Y0PeuACT7LdpXC0qW+mTeiWHI9eO0Aqh7f\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "y-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00871581b50092578211160e470dddaa640d5a2d9e224fafca879106d4021d00be70fd5c7591a3130f5c2af536ffff8e72c16251744c97968f921728", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 349, "comment": "y-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00a98735e5659022b0274ae6f7bcb1646e9e6b4b88408db3f926eccc89021d00a923ff5e15e0d764cd5cefffc5c40c082c6eb772db7662fb1b82d525", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 350, "comment": "y-coordinate of the public key is small on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00a96b5c24e33d59004cf31aaef44ae4c7579e0b5b219ab25d7f1c690a021d0088c01378847c3841f49ec14840e2d023d7b912b503f2d98a923be8c9", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59bc5b19e75e857e67ceb4cb98fb2ff31c9176764ff259d11087e1ea220", "wx": "008b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59b", "wy": "00c5b19e75e857e67ceb4cb98fb2ff31c9176764ff259d11087e1ea220"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a00048b319f2ed074a20ec42d869c7f99bd9146ed8263297bfe004f27c59bc5b19e75e857e67ceb4cb98fb2ff31c9176764ff259d11087e1ea220", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABIsxny7QdKIOxC2GnH+ZvZFG7YJj\nKXv+AE8nxZvFsZ516FfmfOtMuY+y/zHJF2dk/yWdEQh+HqIg\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "y-coordinate of the public key is large on brainpoolP224t1", "msg": "4d657373616765", "sig": "303d021c6b57b73ab7c39b5698549dd5cdd4df7398181b556e7c7283375e3f86021d009f59d1863d6fd6013247d4e678a1c4fc1d896dc661fa31fb73c33f00", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 352, "comment": "y-coordinate of the public key is large on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c2f85af7e535f66cfc9a9dab7be781631dd622be435d7642b5b51fcc7021c619301c21c934255df93eedd5b459c8dd280fdd07ee656a7147d4d69", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 353, "comment": "y-coordinate of the public key is large on brainpoolP224t1", "msg": "4d657373616765", "sig": "303d021d0098a8a98fcc82f804a823cc91072437cfd88322b8671686517f1978ab021c6ac3e83776685bcecefebae473ac07c771e83b0c5a557efe83502036", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c5dc5135f050a96bbb0d21885195b49a574d5198baac4b4602b21bc8b5f38b7fe661003faee1b7af670e165bfab70b018965e83329d405bc", "wx": "00c5dc5135f050a96bbb0d21885195b49a574d5198baac4b4602b21bc8", "wy": "00b5f38b7fe661003faee1b7af670e165bfab70b018965e83329d405bc"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004c5dc5135f050a96bbb0d21885195b49a574d5198baac4b4602b21bc8b5f38b7fe661003faee1b7af670e165bfab70b018965e83329d405bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMXcUTXwUKlruw0hiFGVtJpXTVGY\nuqxLRgKyG8i184t/5mEAP67ht69nDhZb+rcLAYll6DMp1AW8\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c699c40b735236bd9239770a5de2c1a7554631e6ba6ef512f8553d02f021c0bca9c516ca405ffc9ae2dcee12ad7d96b586bfdc818a3d45dcfcf26", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 355, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c768a819d3943fc30781aaef28fa120184c7212d0911fe03dfc8c6260021c51b3db0e1c3e939149ccbf9d4619bbbff0e2e174110f6ece4313b4ca", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 356, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303d021c58fcc0ffb125c23c435735b7c390692037c03a6765ef7b5365a17dd4021d009518d29d78eda9cb2503fde38d3b73d6fb90a0d40a23f0ec26166969", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c1b56a1ad154e11556b723fc7493f36e66509d8f68fad0e62c40f0859b04780a85e69abf98def3335ce643cd3554167a8b50d596b9538895", "wx": "00c1b56a1ad154e11556b723fc7493f36e66509d8f68fad0e62c40f085", "wy": "009b04780a85e69abf98def3335ce643cd3554167a8b50d596b9538895"}, "keyDer": "3052301406072a8648ce3d020106092b2403030208010105033a0004c1b56a1ad154e11556b723fc7493f36e66509d8f68fad0e62c40f0859b04780a85e69abf98def3335ce643cd3554167a8b50d596b9538895", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFIwFAYHKoZIzj0CAQYJKyQDAwIIAQEFAzoABMG1ahrRVOEVVrcj/HST825mUJ2P\naPrQ5ixA8IWbBHgKheaav5je8zNc5kPNNVQWeotQ1Za5U4iV\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303e021d00d193ee0a3d42a23af018ab90896b35d5c250187bf9fb1ccac364748c021d00a0922accc7562d017109e91d2f83e48bfa3c1fa2ee04d8469be94033", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 358, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303d021c0974521d7ce753dea5d1156fb4d992cc614079eb8677ab36a4078a4f021d008374dfbae8d0429a6fba60fbb5d2fd559856a5d739f39aa2bf1da1c9", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 359, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP224t1", "msg": "4d657373616765", "sig": "303c021c625f473ca2d15bb7f12da1235f90adcb69ed4818746cae2e2db26fe6021c4ab817f6f1b9c8c49f681bed1568346f53ecbfacfd52d45e27abcbb0", "result": "valid", "flags": ["GroupIsomorphism"]}]}]}