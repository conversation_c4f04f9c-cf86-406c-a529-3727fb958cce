=pod

=head1 NAME

UI_UTIL_read_pw_string, UI_UTIL_read_pw,
UI_UTIL_wrap_read_pem_callback - user interface utilities

=head1 SYNOPSIS

 #include <openssl/ui.h>

 int UI_UTIL_read_pw_string(char *buf, int length, const char *prompt,
                            int verify);
 int UI_UTIL_read_pw(char *buf, char *buff, int size, const char *prompt,
                     int verify);
 UI_METHOD *UI_UTIL_wrap_read_pem_callback(pem_password_cb *cb, int rwflag);

=head1 DESCRIPTION

UI_UTIL_read_pw_string() asks for a passphrase, using B<prompt> as a
prompt, and stores it in B<buf>.
The maximum allowed size is given with B<length>, including the
terminating NUL byte.
If B<verify> is nonzero, the password will be verified as well.

UI_UTIL_read_pw() does the same as UI_UTIL_read_pw_string(), the
difference is that you can give it an external buffer B<buff> for the
verification passphrase.

UI_UTIL_wrap_read_pem_callback() can be used to create a temporary
B<UI_METHOD> that wraps a given PEM password callback B<cb>.
B<rwflag> is used to specify if this method will be used for
passphrase entry without (0) or with (1) verification.
When not used any more, the returned method should be freed with
UI_destroy_method().

=head1 NOTES

UI_UTIL_read_pw_string() and UI_UTIL_read_pw() use default
B<UI_METHOD>.
See L<UI_get_default_method(3)> and friends for more information.

The result from the B<UI_METHOD> created by
UI_UTIL_wrap_read_pem_callback() will generate password strings in the
encoding that the given password callback generates.
The default password prompting functions (apart from
UI_UTIL_read_pw_string() and UI_UTIL_read_pw(), there is
PEM_def_callback(), EVP_read_pw_string() and EVP_read_pw_string_min())
all use the default B<UI_METHOD>.

=head1 RETURN VALUES

UI_UTIL_read_pw_string() and UI_UTIL_read_pw() return 0 on success or a negative
value on error.

UI_UTIL_wrap_read_pem_callback() returns a valid B<UI_METHOD> structure or NULL
if an error occurred.

=head1 SEE ALSO

L<UI_get_default_method(3)>

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
