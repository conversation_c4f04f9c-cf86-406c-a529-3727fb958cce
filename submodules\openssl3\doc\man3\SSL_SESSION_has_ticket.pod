=pod

=head1 NAME

SSL_SESSION_get0_ticket,
SSL_SESSION_has_ticket, SSL_SESSION_get_ticket_lifetime_hint
- get details about the ticket associated with a session

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_SESSION_has_ticket(const SSL_SESSION *s);
 unsigned long SSL_SESSION_get_ticket_lifetime_hint(const SSL_SESSION *s);
 void SSL_SESSION_get0_ticket(const SSL_SESSION *s, const unsigned char **tick,
                              size_t *len);

=head1 DESCRIPTION

SSL_SESSION_has_ticket() returns 1 if there is a Session Ticket associated with
this session, and 0 otherwise.

SSL_SESSION_get_ticket_lifetime_hint returns the lifetime hint in seconds
associated with the session ticket.

SSL_SESSION_get0_ticket obtains a pointer to the ticket associated with a
session. The length of the ticket is written to B<*len>. If B<tick> is non
NULL then a pointer to the ticket is written to B<*tick>. The pointer is only
valid while the connection is in use. The session (and hence the ticket pointer)
may also become invalid as a result of a call to SSL_CTX_flush_sessions().

=head1 RETURN VALUES

SSL_SESSION_has_ticket() returns 1 if session ticket exists or 0 otherwise.

SSL_SESSION_get_ticket_lifetime_hint() returns the number of seconds.

=head1 SEE ALSO

L<ssl(7)>,
L<d2i_SSL_SESSION(3)>,
L<SSL_SESSION_get_time(3)>,
L<SSL_SESSION_free(3)>

=head1 HISTORY

The SSL_SESSION_has_ticket(), SSL_SESSION_get_ticket_lifetime_hint()
and SSL_SESSION_get0_ticket() functions were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2015-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
