---
name: Bug report
labels: 'issue: bug report'
about: Report a defect in the software

---

<!--
Thank you for your bug report. If this is your first one,
please take the time to read the following lines before posting it.

NOTE:

    If you're asking about how to use OpenSSL, this isn't the right
    forum.  Please see our User Support resources:
    https://github.com/openssl/openssl/blob/master/SUPPORT.md

Please remember to tell us in what OpenSSL version you found the issue.

For build issues:

    If this is a build issue, please include the configuration output
    as well as a log of all errors.  Don't forget to include the exact
    commands you typed.

    With OpenSSL before 1.1.1, the configuration output comes from the
    configuration command.  With OpenSSL 1.1.1 and on, it's the output
    of `perl configdata.pm --dump`

For other issues:

    If it isn't a build issue, example code or commands to reproduce
    the issue is highly appreciated.
    Also, please remember to tell us if you worked with your own
    OpenSSL build or if it is system provided.

Please remember to put ``` lines before and after any commands plus
output and code, like this:

    ```
    $ echo output output output
    output output output
    ```

    ```
    #include <stdio.h>

    int main() {
        int foo = 1;
        printf("%d\n", foo);
    }
    ```
-->
