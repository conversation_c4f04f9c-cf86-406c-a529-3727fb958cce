Issues to be addressed for src/lib/crypto: -*- text -*-


Many files here and in subdirectories pollute the namespace.
However, some applications wanting to directly use some of those
routines will expect those names to be available.

Workaround: Shared library export lists?  Define and export internal
names, and provide wrapper library code or weak functions under the
polluting names?


Some routines assume "int" is big enough to describe all buffers that
may be supplied.
