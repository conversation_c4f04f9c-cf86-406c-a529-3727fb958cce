.\" Man page generated from reStructuredText.
.
.TH "KPROPLOG" "8" " " "1.20" "MIT Kerberos"
.SH NAME
kproplog \- display the contents of the Kerberos principal update log
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkproplog\fP [\fB\-h\fP] [\fB\-e\fP \fInum\fP] [\-v]
\fBkproplog\fP [\-R]
.SH DESCRIPTION
.sp
The kproplog command displays the contents of the KDC database update
log to standard output.  It can be used to keep track of incremental
updates to the principal database.  The update log file contains the
update log maintained by the kadmind(8) process on the primary
KDC server and the kpropd(8) process on the replica KDC
servers.  When updates occur, they are logged to this file.
Subsequently any KDC replica configured for incremental updates will
request the current data from the primary KDC and update their log
file with any updates returned.
.sp
The kproplog command requires read access to the update log file.  It
will display update entries only for the KDC it runs on.
.sp
If no options are specified, kproplog displays a summary of the update
log.  If invoked on the primary, kproplog also displays all of the
update entries.  If invoked on a replica KDC server, kproplog displays
only a summary of the updates, which includes the serial number of the
last update received and the associated time stamp of the last update.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-R\fP
Reset the update log.  This forces full resynchronization.  If
used on a replica then that replica will request a full resync.
If used on the primary then all replicas will request full
resyncs.
.TP
\fB\-h\fP
Display a summary of the update log.  This information includes
the database version number, state of the database, the number of
updates in the log, the time stamp of the first and last update,
and the version number of the first and last update entry.
.TP
\fB\-e\fP \fInum\fP
Display the last \fInum\fP update entries in the log.  This is useful
when debugging synchronization between KDC servers.
.TP
\fB\-v\fP
Display individual attributes per update.  An example of the
output generated for one entry:
.INDENT 7.0
.INDENT 3.5
.sp
.nf
.ft C
Update Entry
   Update serial # : 4
   Update operation : Add
   Update principal : <EMAIL>
   Update size : 424
   Update committed : True
   Update time stamp : Fri Feb 20 23:37:42 2004
   Attributes changed : 6
         Principal
         Key data
         Password last changed
         Modifying principal
         Modification time
         TL data
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kpropd(8), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
