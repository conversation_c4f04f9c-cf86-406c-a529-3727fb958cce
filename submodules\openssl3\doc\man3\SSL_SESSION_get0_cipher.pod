=pod

=head1 NAME

SSL_SESSION_get0_cipher,
SSL_SESSION_set_cipher
- set and retrieve the SSL cipher associated with a session

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 const SSL_CIPHER *SSL_SESSION_get0_cipher(const SSL_SESSION *s);
 int SSL_SESSION_set_cipher(SSL_SESSION *s, const SSL_CIPHER *cipher);

=head1 DESCRIPTION

SSL_SESSION_get0_cipher() retrieves the cipher that was used by the
connection when the session was created, or NULL if it cannot be determined.

The value returned is a pointer to an object maintained within B<s> and
should not be released.

SSL_SESSION_set_cipher() can be used to set the ciphersuite associated with the
SSL_SESSION B<s> to B<cipher>. For example, this could be used to set up a
session based PSK (see L<SSL_CTX_set_psk_use_session_callback(3)>).

=head1 RETURN VALUES

SSL_SESSION_get0_cipher() returns the SSL_CIPHER associated with the SSL_SESSION
or NULL if it cannot be determined.

SSL_SESSION_set_cipher() returns 1 on success or 0 on failure.

=head1 SEE ALSO

L<ssl(7)>,
L<d2i_SSL_SESSION(3)>,
L<SSL_SESSION_get_time(3)>,
L<SSL_SESSION_get0_hostname(3)>,
L<SSL_SESSION_free(3)>,
L<SSL_CTX_set_psk_use_session_callback(3)>

=head1 HISTORY

The SSL_SESSION_get0_cipher() function was added in OpenSSL 1.1.0.
The SSL_SESSION_set_cipher() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2016-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
