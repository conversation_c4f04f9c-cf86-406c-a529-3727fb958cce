/* Autogenerated: ECCKiila https://gitlab.com/nisec/ecckiila */
/*-
 * MIT License
 * 
 * Copyright (c) 2020 <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
#if defined(__SIZEOF_INT128__) && !defined(PEDANTIC)

#include <stdint.h>
#include <string.h>
#define LIMB_BITS 64
#define LIMB_CNT 5
/* Field elements */
typedef uint64_t fe_t[LIMB_CNT];
typedef uint64_t limb_t;

#ifdef OPENSSL_NO_ASM
#define FIAT_ID_GOSTR3410_2001_CRYPTOPRO_A_PARAMSET_NO_ASM
#endif

#define fe_copy(d, s) memcpy(d, s, sizeof(fe_t))
#define fe_set_zero(d) memset(d, 0, sizeof(fe_t))

#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(c, a, b) \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add(c, a, b);          \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(c, c)
#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(c, a, b) \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub(c, a, b);          \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(c, c)

/* Projective points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t Z;
} pt_prj_t;

/* Affine points */
typedef struct {
    fe_t X;
    fe_t Y;
} pt_aff_t;

/* BEGIN verbatim fiat code https://github.com/mit-plv/fiat-crypto */
/*-
 * MIT License
 *
 * Copyright (c) 2020 the fiat-crypto authors (see the AUTHORS file)
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/* Autogenerated: unsaturated_solinas --static --use-value-barrier id_GostR3410_2001_CryptoPro_A_ParamSet 64 5 '2^256 - 617' */
/* curve description: id_GostR3410_2001_CryptoPro_A_ParamSet */
/* machine_wordsize = 64 (from "64") */
/* requested operations: (all) */
/* n = 5 (from "5") */
/* s-c = 2^256 - [(1, 617)] (from "2^256 - 617") */
/* tight_bounds_multiplier = 1 (from "") */
/*  */
/* Computed values: */
/* carry_chain = [0, 1, 2, 3, 4, 0, 1] */
/* eval z = z[0] + (z[1] << 52) + (z[2] << 103) + (z[3] << 154) + (z[4] << 205) */
/* bytes_eval z = z[0] + (z[1] << 8) + (z[2] << 16) + (z[3] << 24) + (z[4] << 32) + (z[5] << 40) + (z[6] << 48) + (z[7] << 56) + (z[8] << 64) + (z[9] << 72) + (z[10] << 80) + (z[11] << 88) + (z[12] << 96) + (z[13] << 104) + (z[14] << 112) + (z[15] << 120) + (z[16] << 128) + (z[17] << 136) + (z[18] << 144) + (z[19] << 152) + (z[20] << 160) + (z[21] << 168) + (z[22] << 176) + (z[23] << 184) + (z[24] << 192) + (z[25] << 200) + (z[26] << 208) + (z[27] << 216) + (z[28] << 224) + (z[29] << 232) + (z[30] << 240) + (z[31] << 248) */
/* balance = [0x1ffffffffffb2e, 0xffffffffffffe, 0xffffffffffffe, 0xffffffffffffe, 0xffffffffffffe] */

#include <stdint.h>
typedef unsigned char fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1;
typedef signed char fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1;
typedef signed __int128 fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int128;
typedef unsigned __int128 fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128;

#if (-1 & 3) != 3
#error "This code only works on a two's complement system"
#endif

#if !defined(FIAT_ID_GOSTR3410_2001_CRYPTOPRO_A_PARAMSET_NO_ASM) && \
    (defined(__GNUC__) || defined(__clang__))
static __inline__ uint64_t
fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u64(uint64_t a) {
    __asm__("" : "+r"(a) : /* no inputs */);
    return a;
}
#else
#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u64(x) (x)
#endif

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u52 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^52
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^52⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xfffffffffffff]
 *   arg3: [0x0 ~> 0xfffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xfffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u52(
    uint64_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    uint64_t x1;
    uint64_t x2;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT64_C(0xfffffffffffff));
    x3 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x1 >> 52);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u52 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^52
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^52⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xfffffffffffff]
 *   arg3: [0x0 ~> 0xfffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xfffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u52(
    uint64_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    int64_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1 x2;
    uint64_t x3;
    x1 = ((int64_t)(arg2 - (int64_t)arg1) - (int64_t)arg3);
    x2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(x1 >> 52);
    x3 = (x1 & UINT64_C(0xfffffffffffff));
    *out1 = x3;
    *out2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^51
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^51⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7ffffffffffff]
 *   arg3: [0x0 ~> 0x7ffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7ffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51(
    uint64_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    uint64_t x1;
    uint64_t x2;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT64_C(0x7ffffffffffff));
    x3 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x1 >> 51);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^51
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^51⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7ffffffffffff]
 *   arg3: [0x0 ~> 0x7ffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7ffffffffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51(
    uint64_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint64_t arg2,
    uint64_t arg3) {
    int64_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1 x2;
    uint64_t x3;
    x1 = ((int64_t)(arg2 - (int64_t)arg1) - (int64_t)arg3);
    x2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(x1 >> 51);
    x3 = (x1 & UINT64_C(0x7ffffffffffff));
    *out1 = x3;
    *out2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64 is a single-word conditional move.
 * Postconditions:
 *   out1 = (if arg1 = 0 then arg2 else arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffffffffffff]
 *   arg3: [0x0 ~> 0xffffffffffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffffffffffff]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
    uint64_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1,
    uint64_t arg2, uint64_t arg3) {
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x1;
    uint64_t x2;
    uint64_t x3;
    x1 = (!(!arg1));
    x2 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(0x0 - x1) &
          UINT64_C(0xffffffffffffffff));
    x3 =
        ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u64(x2) &
          arg3) |
         (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u64((~x2)) &
          arg2));
    *out1 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul multiplies two field elements and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 *   arg2: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x2;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x3;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x4;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x5;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x6;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x7;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x8;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x9;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x10;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x11;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x12;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x13;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x14;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x15;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x16;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x17;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x18;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x19;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x20;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x21;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x22;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x23;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x24;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x25;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x26;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x27;
    uint64_t x28;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x29;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x30;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x31;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x32;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x33;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x34;
    uint64_t x35;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x36;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x37;
    uint64_t x38;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x39;
    uint64_t x40;
    uint64_t x41;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x42;
    uint64_t x43;
    uint64_t x44;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x45;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x50;
    uint64_t x51;
    uint64_t x52;
    x1 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) *
           (arg2[4])));
    x2 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) *
           (arg2[3])));
    x3 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) *
           (arg2[2])));
    x4 = (UINT16_C(0x269) *
          (((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) *
            (arg2[1])) *
           0x2));
    x5 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) *
           (arg2[4])));
    x6 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) *
           (arg2[3])));
    x7 = (UINT16_C(0x269) *
          (((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) *
            (arg2[2])) *
           0x2));
    x8 = (UINT16_C(0x269) *
          ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           (arg2[4])));
    x9 = (UINT16_C(0x269) *
          (((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
            (arg2[3])) *
           0x2));
    x10 = (UINT16_C(0x269) *
           (((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
             (arg2[4])) *
            0x2));
    x11 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) *
           (arg2[0]));
    x12 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) *
           ((arg2[1]) * 0x2));
    x13 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) *
           (arg2[0]));
    x14 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           ((arg2[2]) * 0x2));
    x15 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           ((arg2[1]) * 0x2));
    x16 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           (arg2[0]));
    x17 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           ((arg2[3]) * 0x2));
    x18 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           ((arg2[2]) * 0x2));
    x19 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           ((arg2[1]) * 0x2));
    x20 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           (arg2[0]));
    x21 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg2[4]));
    x22 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg2[3]));
    x23 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg2[2]));
    x24 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg2[1]));
    x25 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg2[0]));
    x26 = (x25 + (x10 + (x9 + (x7 + x4))));
    x27 = (x26 >> 52);
    x28 = (uint64_t)(x26 & UINT64_C(0xfffffffffffff));
    x29 = (x21 + (x17 + (x14 + (x12 + x11))));
    x30 = (x22 + (x18 + (x15 + (x13 + x1))));
    x31 = (x23 + (x19 + (x16 + (x5 + x2))));
    x32 = (x24 + (x20 + (x8 + (x6 + x3))));
    x33 = (x27 + x32);
    x34 = (x33 >> 51);
    x35 = (uint64_t)(x33 & UINT64_C(0x7ffffffffffff));
    x36 = (x34 + x31);
    x37 = (x36 >> 51);
    x38 = (uint64_t)(x36 & UINT64_C(0x7ffffffffffff));
    x39 = (x37 + x30);
    x40 = (uint64_t)(x39 >> 51);
    x41 = (uint64_t)(x39 & UINT64_C(0x7ffffffffffff));
    x42 = (x40 + x29);
    x43 = (uint64_t)(x42 >> 51);
    x44 = (uint64_t)(x42 & UINT64_C(0x7ffffffffffff));
    x45 =
        ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)UINT16_C(0x269) *
         x43);
    x46 = (x28 + x45);
    x47 = (uint64_t)(x46 >> 52);
    x48 = (uint64_t)(x46 & UINT64_C(0xfffffffffffff));
    x49 = (x47 + x35);
    x50 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x49 >> 51);
    x51 = (x49 & UINT64_C(0x7ffffffffffff));
    x52 = (x50 + x38);
    out1[0] = x48;
    out1[1] = x51;
    out1[2] = x52;
    out1[3] = x41;
    out1[4] = x44;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square squares a field element and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg1) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x9;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x10;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x11;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x12;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x13;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x14;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x15;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x16;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x17;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x18;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x19;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x20;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x21;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x22;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x23;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x24;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x25;
    uint64_t x26;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x27;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x28;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x29;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x30;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x31;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x32;
    uint64_t x33;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x34;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x35;
    uint64_t x36;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x37;
    uint64_t x38;
    uint64_t x39;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x40;
    uint64_t x41;
    uint64_t x42;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x43;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128 x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x48;
    uint64_t x49;
    uint64_t x50;
    x1 = ((arg1[4]) * UINT16_C(0x269));
    x2 = (x1 * 0x2);
    x3 = ((arg1[4]) * 0x2);
    x4 = ((arg1[3]) * UINT16_C(0x269));
    x5 = (x4 * 0x2);
    x6 = ((arg1[3]) * 0x2);
    x7 = ((arg1[2]) * 0x2);
    x8 = ((arg1[1]) * 0x2);
    x9 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[4]) * x1);
    x10 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) * x2);
    x11 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[3]) * x4);
    x12 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) * x2);
    x13 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           (x5 * 0x2));
    x14 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[2]) *
           ((arg1[2]) * 0x2));
    x15 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           (x2 * 0x2));
    x16 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           (x6 * 0x2));
    x17 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           (x7 * 0x2));
    x18 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[1]) *
           ((arg1[1]) * 0x2));
    x19 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) * x3);
    x20 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) * x6);
    x21 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) * x7);
    x22 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) * x8);
    x23 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)(arg1[0]) *
           (arg1[0]));
    x24 = (x23 + (x15 + x13));
    x25 = (x24 >> 52);
    x26 = (uint64_t)(x24 & UINT64_C(0xfffffffffffff));
    x27 = (x19 + (x16 + x14));
    x28 = (x20 + (x17 + x9));
    x29 = (x21 + (x18 + x10));
    x30 = (x22 + (x12 + x11));
    x31 = (x25 + x30);
    x32 = (x31 >> 51);
    x33 = (uint64_t)(x31 & UINT64_C(0x7ffffffffffff));
    x34 = (x32 + x29);
    x35 = (x34 >> 51);
    x36 = (uint64_t)(x34 & UINT64_C(0x7ffffffffffff));
    x37 = (x35 + x28);
    x38 = (uint64_t)(x37 >> 51);
    x39 = (uint64_t)(x37 & UINT64_C(0x7ffffffffffff));
    x40 = (x38 + x27);
    x41 = (uint64_t)(x40 >> 51);
    x42 = (uint64_t)(x40 & UINT64_C(0x7ffffffffffff));
    x43 =
        ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint128)UINT16_C(0x269) *
         x41);
    x44 = (x26 + x43);
    x45 = (uint64_t)(x44 >> 52);
    x46 = (uint64_t)(x44 & UINT64_C(0xfffffffffffff));
    x47 = (x45 + x33);
    x48 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x47 >> 51);
    x49 = (x47 & UINT64_C(0x7ffffffffffff));
    x50 = (x48 + x36);
    out1[0] = x46;
    out1[1] = x49;
    out1[2] = x50;
    out1[3] = x39;
    out1[4] = x42;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry reduces a field element.
 * Postconditions:
 *   eval out1 mod m = eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    x1 = (arg1[0]);
    x2 = ((x1 >> 52) + (arg1[1]));
    x3 = ((x2 >> 51) + (arg1[2]));
    x4 = ((x3 >> 51) + (arg1[3]));
    x5 = ((x4 >> 51) + (arg1[4]));
    x6 = ((x1 & UINT64_C(0xfffffffffffff)) + (UINT16_C(0x269) * (x5 >> 51)));
    x7 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x6 >> 52) +
          (x2 & UINT64_C(0x7ffffffffffff)));
    x8 = (x6 & UINT64_C(0xfffffffffffff));
    x9 = (x7 & UINT64_C(0x7ffffffffffff));
    x10 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x7 >> 51) +
           (x3 & UINT64_C(0x7ffffffffffff)));
    x11 = (x4 & UINT64_C(0x7ffffffffffff));
    x12 = (x5 & UINT64_C(0x7ffffffffffff));
    out1[0] = x8;
    out1[1] = x9;
    out1[2] = x10;
    out1[3] = x11;
    out1[4] = x12;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add adds two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 + eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 *   arg2: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = ((arg1[0]) + (arg2[0]));
    x2 = ((arg1[1]) + (arg2[1]));
    x3 = ((arg1[2]) + (arg2[2]));
    x4 = ((arg1[3]) + (arg2[3]));
    x5 = ((arg1[4]) + (arg2[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub subtracts two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 - eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 *   arg2: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub(
    uint64_t out1[5], const uint64_t arg1[5], const uint64_t arg2[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = ((UINT64_C(0x1ffffffffffb2e) + (arg1[0])) - (arg2[0]));
    x2 = ((UINT64_C(0xffffffffffffe) + (arg1[1])) - (arg2[1]));
    x3 = ((UINT64_C(0xffffffffffffe) + (arg1[2])) - (arg2[2]));
    x4 = ((UINT64_C(0xffffffffffffe) + (arg1[3])) - (arg2[3]));
    x5 = ((UINT64_C(0xffffffffffffe) + (arg1[4])) - (arg2[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp negates a field element.
 * Postconditions:
 *   eval out1 mod m = -eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x30000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000], [0x0 ~> 0x18000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(
    uint64_t out1[5], const uint64_t arg1[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    x1 = (UINT64_C(0x1ffffffffffb2e) - (arg1[0]));
    x2 = (UINT64_C(0xffffffffffffe) - (arg1[1]));
    x3 = (UINT64_C(0xffffffffffffe) - (arg1[2]));
    x4 = (UINT64_C(0xffffffffffffe) - (arg1[3]));
    x5 = (UINT64_C(0xffffffffffffe) - (arg1[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz is a multi-limb conditional select.
 * Postconditions:
 *   eval out1 = (if arg1 = 0 then eval arg2 else eval arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 *   arg3: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff], [0x0 ~> 0xffffffffffffffff]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
    uint64_t out1[5], fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1,
    const uint64_t arg2[5], const uint64_t arg3[5]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x1, arg1, (arg2[0]), (arg3[0]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x2, arg1, (arg2[1]), (arg3[1]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x3, arg1, (arg2[2]), (arg3[2]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x4, arg1, (arg2[3]), (arg3[3]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x5, arg1, (arg2[4]), (arg3[4]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes serializes a field element to bytes in little-endian order.
 * Postconditions:
 *   out1 = map (λ x, ⌊((eval arg1 mod m) mod 2^(8 * (x + 1))) / 2^(8 * x)⌋) [0..31]
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(
    uint8_t out1[32], const uint64_t arg1[5]) {
    uint64_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x2;
    uint64_t x3;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x4;
    uint64_t x5;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x6;
    uint64_t x7;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x8;
    uint64_t x9;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x10;
    uint64_t x11;
    uint64_t x12;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x13;
    uint64_t x14;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x15;
    uint64_t x16;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x17;
    uint64_t x18;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x19;
    uint64_t x20;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint8_t x26;
    uint64_t x27;
    uint8_t x28;
    uint64_t x29;
    uint8_t x30;
    uint64_t x31;
    uint8_t x32;
    uint64_t x33;
    uint8_t x34;
    uint64_t x35;
    uint8_t x36;
    uint8_t x37;
    uint64_t x38;
    uint8_t x39;
    uint64_t x40;
    uint8_t x41;
    uint64_t x42;
    uint8_t x43;
    uint64_t x44;
    uint8_t x45;
    uint64_t x46;
    uint8_t x47;
    uint64_t x48;
    uint8_t x49;
    uint8_t x50;
    uint64_t x51;
    uint8_t x52;
    uint64_t x53;
    uint8_t x54;
    uint64_t x55;
    uint8_t x56;
    uint64_t x57;
    uint8_t x58;
    uint64_t x59;
    uint8_t x60;
    uint64_t x61;
    uint8_t x62;
    uint64_t x63;
    uint8_t x64;
    uint8_t x65;
    uint64_t x66;
    uint8_t x67;
    uint64_t x68;
    uint8_t x69;
    uint64_t x70;
    uint8_t x71;
    uint64_t x72;
    uint8_t x73;
    uint64_t x74;
    uint8_t x75;
    uint64_t x76;
    uint8_t x77;
    uint8_t x78;
    uint64_t x79;
    uint8_t x80;
    uint64_t x81;
    uint8_t x82;
    uint64_t x83;
    uint8_t x84;
    uint64_t x85;
    uint8_t x86;
    uint64_t x87;
    uint8_t x88;
    uint64_t x89;
    uint8_t x90;
    uint8_t x91;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u52(
        &x1, &x2, 0x0, (arg1[0]), UINT64_C(0xffffffffffd97));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51(
        &x3, &x4, x2, (arg1[1]), UINT64_C(0x7ffffffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51(
        &x5, &x6, x4, (arg1[2]), UINT64_C(0x7ffffffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51(
        &x7, &x8, x6, (arg1[3]), UINT64_C(0x7ffffffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u51(
        &x9, &x10, x8, (arg1[4]), UINT64_C(0x7ffffffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u64(
        &x11, x10, 0x0, UINT64_C(0xffffffffffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u52(
        &x12, &x13, 0x0, x1, (x11 & UINT64_C(0xffffffffffd97)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51(
        &x14, &x15, x13, x3, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51(
        &x16, &x17, x15, x5, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51(
        &x18, &x19, x17, x7, (x11 & UINT64_C(0x7ffffffffffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u51(
        &x20, &x21, x19, x9, (x11 & UINT64_C(0x7ffffffffffff)));
    x22 = (x20 << 5);
    x23 = (x18 << 2);
    x24 = (x16 << 7);
    x25 = (x14 << 4);
    x26 = (uint8_t)(x12 & UINT8_C(0xff));
    x27 = (x12 >> 8);
    x28 = (uint8_t)(x27 & UINT8_C(0xff));
    x29 = (x27 >> 8);
    x30 = (uint8_t)(x29 & UINT8_C(0xff));
    x31 = (x29 >> 8);
    x32 = (uint8_t)(x31 & UINT8_C(0xff));
    x33 = (x31 >> 8);
    x34 = (uint8_t)(x33 & UINT8_C(0xff));
    x35 = (x33 >> 8);
    x36 = (uint8_t)(x35 & UINT8_C(0xff));
    x37 = (uint8_t)(x35 >> 8);
    x38 = (x25 + (uint64_t)x37);
    x39 = (uint8_t)(x38 & UINT8_C(0xff));
    x40 = (x38 >> 8);
    x41 = (uint8_t)(x40 & UINT8_C(0xff));
    x42 = (x40 >> 8);
    x43 = (uint8_t)(x42 & UINT8_C(0xff));
    x44 = (x42 >> 8);
    x45 = (uint8_t)(x44 & UINT8_C(0xff));
    x46 = (x44 >> 8);
    x47 = (uint8_t)(x46 & UINT8_C(0xff));
    x48 = (x46 >> 8);
    x49 = (uint8_t)(x48 & UINT8_C(0xff));
    x50 = (uint8_t)(x48 >> 8);
    x51 = (x24 + (uint64_t)x50);
    x52 = (uint8_t)(x51 & UINT8_C(0xff));
    x53 = (x51 >> 8);
    x54 = (uint8_t)(x53 & UINT8_C(0xff));
    x55 = (x53 >> 8);
    x56 = (uint8_t)(x55 & UINT8_C(0xff));
    x57 = (x55 >> 8);
    x58 = (uint8_t)(x57 & UINT8_C(0xff));
    x59 = (x57 >> 8);
    x60 = (uint8_t)(x59 & UINT8_C(0xff));
    x61 = (x59 >> 8);
    x62 = (uint8_t)(x61 & UINT8_C(0xff));
    x63 = (x61 >> 8);
    x64 = (uint8_t)(x63 & UINT8_C(0xff));
    x65 = (uint8_t)(x63 >> 8);
    x66 = (x23 + (uint64_t)x65);
    x67 = (uint8_t)(x66 & UINT8_C(0xff));
    x68 = (x66 >> 8);
    x69 = (uint8_t)(x68 & UINT8_C(0xff));
    x70 = (x68 >> 8);
    x71 = (uint8_t)(x70 & UINT8_C(0xff));
    x72 = (x70 >> 8);
    x73 = (uint8_t)(x72 & UINT8_C(0xff));
    x74 = (x72 >> 8);
    x75 = (uint8_t)(x74 & UINT8_C(0xff));
    x76 = (x74 >> 8);
    x77 = (uint8_t)(x76 & UINT8_C(0xff));
    x78 = (uint8_t)(x76 >> 8);
    x79 = (x22 + (uint64_t)x78);
    x80 = (uint8_t)(x79 & UINT8_C(0xff));
    x81 = (x79 >> 8);
    x82 = (uint8_t)(x81 & UINT8_C(0xff));
    x83 = (x81 >> 8);
    x84 = (uint8_t)(x83 & UINT8_C(0xff));
    x85 = (x83 >> 8);
    x86 = (uint8_t)(x85 & UINT8_C(0xff));
    x87 = (x85 >> 8);
    x88 = (uint8_t)(x87 & UINT8_C(0xff));
    x89 = (x87 >> 8);
    x90 = (uint8_t)(x89 & UINT8_C(0xff));
    x91 = (uint8_t)(x89 >> 8);
    out1[0] = x26;
    out1[1] = x28;
    out1[2] = x30;
    out1[3] = x32;
    out1[4] = x34;
    out1[5] = x36;
    out1[6] = x39;
    out1[7] = x41;
    out1[8] = x43;
    out1[9] = x45;
    out1[10] = x47;
    out1[11] = x49;
    out1[12] = x52;
    out1[13] = x54;
    out1[14] = x56;
    out1[15] = x58;
    out1[16] = x60;
    out1[17] = x62;
    out1[18] = x64;
    out1[19] = x67;
    out1[20] = x69;
    out1[21] = x71;
    out1[22] = x73;
    out1[23] = x75;
    out1[24] = x77;
    out1[25] = x80;
    out1[26] = x82;
    out1[27] = x84;
    out1[28] = x86;
    out1[29] = x88;
    out1[30] = x90;
    out1[31] = x91;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes deserializes a field element from bytes in little-endian order.
 * Postconditions:
 *   eval out1 mod m = bytes_eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x10000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000], [0x0 ~> 0x8000000000000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(
    uint64_t out1[5], const uint8_t arg1[32]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    uint64_t x13;
    uint64_t x14;
    uint64_t x15;
    uint64_t x16;
    uint64_t x17;
    uint64_t x18;
    uint64_t x19;
    uint64_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint8_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint8_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint8_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint8_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    x1 = ((uint64_t)(arg1[31]) << 43);
    x2 = ((uint64_t)(arg1[30]) << 35);
    x3 = ((uint64_t)(arg1[29]) << 27);
    x4 = ((uint64_t)(arg1[28]) << 19);
    x5 = ((uint64_t)(arg1[27]) << 11);
    x6 = ((uint64_t)(arg1[26]) << 3);
    x7 = ((uint64_t)(arg1[25]) << 46);
    x8 = ((uint64_t)(arg1[24]) << 38);
    x9 = ((uint64_t)(arg1[23]) << 30);
    x10 = ((uint64_t)(arg1[22]) << 22);
    x11 = ((uint64_t)(arg1[21]) << 14);
    x12 = ((uint64_t)(arg1[20]) << 6);
    x13 = ((uint64_t)(arg1[19]) << 49);
    x14 = ((uint64_t)(arg1[18]) << 41);
    x15 = ((uint64_t)(arg1[17]) << 33);
    x16 = ((uint64_t)(arg1[16]) << 25);
    x17 = ((uint64_t)(arg1[15]) << 17);
    x18 = ((uint64_t)(arg1[14]) << 9);
    x19 = ((uint64_t)(arg1[13]) * 0x2);
    x20 = ((uint64_t)(arg1[12]) << 44);
    x21 = ((uint64_t)(arg1[11]) << 36);
    x22 = ((uint64_t)(arg1[10]) << 28);
    x23 = ((uint64_t)(arg1[9]) << 20);
    x24 = ((uint64_t)(arg1[8]) << 12);
    x25 = ((uint64_t)(arg1[7]) << 4);
    x26 = ((uint64_t)(arg1[6]) << 48);
    x27 = ((uint64_t)(arg1[5]) << 40);
    x28 = ((uint64_t)(arg1[4]) << 32);
    x29 = ((uint64_t)(arg1[3]) << 24);
    x30 = ((uint64_t)(arg1[2]) << 16);
    x31 = ((uint64_t)(arg1[1]) << 8);
    x32 = (arg1[0]);
    x33 = (x31 + (uint64_t)x32);
    x34 = (x30 + x33);
    x35 = (x29 + x34);
    x36 = (x28 + x35);
    x37 = (x27 + x36);
    x38 = (x26 + x37);
    x39 = (x38 & UINT64_C(0xfffffffffffff));
    x40 = (uint8_t)(x38 >> 52);
    x41 = (x25 + (uint64_t)x40);
    x42 = (x24 + x41);
    x43 = (x23 + x42);
    x44 = (x22 + x43);
    x45 = (x21 + x44);
    x46 = (x20 + x45);
    x47 = (x46 & UINT64_C(0x7ffffffffffff));
    x48 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x46 >> 51);
    x49 = (x19 + (uint64_t)x48);
    x50 = (x18 + x49);
    x51 = (x17 + x50);
    x52 = (x16 + x51);
    x53 = (x15 + x52);
    x54 = (x14 + x53);
    x55 = (x13 + x54);
    x56 = (x55 & UINT64_C(0x7ffffffffffff));
    x57 = (uint8_t)(x55 >> 51);
    x58 = (x12 + (uint64_t)x57);
    x59 = (x11 + x58);
    x60 = (x10 + x59);
    x61 = (x9 + x60);
    x62 = (x8 + x61);
    x63 = (x7 + x62);
    x64 = (x63 & UINT64_C(0x7ffffffffffff));
    x65 = (uint8_t)(x63 >> 51);
    x66 = (x6 + (uint64_t)x65);
    x67 = (x5 + x66);
    x68 = (x4 + x67);
    x69 = (x3 + x68);
    x70 = (x2 + x69);
    x71 = (x1 + x70);
    out1[0] = x39;
    out1[1] = x47;
    out1[2] = x56;
    out1[3] = x64;
    out1[4] = x71;
}

/* END verbatim fiat code */

/*-
 * Finite field inversion via FLT.
 * NB: this is not a real Fiat function, just named that way for consistency.
 * Autogenerated: ecp/id_GostR3410_2001_CryptoPro_A_ParamSet/fe_inv.op3
 * custom repunit addition chain
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(fe_t output,
                                                            const fe_t t1) {
    int i;
    /* temporary variables */
    fe_t acc, t16, t164, t2, t246, t32, t4, t64, t8, t80, t82;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, acc, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t4);
    for (i = 0; i < 3; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t8, acc, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t8);
    for (i = 0; i < 7; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t16, acc, t8);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t16);
    for (i = 0; i < 15; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t32, acc, t16);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t32);
    for (i = 0; i < 31; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t64, acc, t32);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t64);
    for (i = 0; i < 15; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t80, acc, t16);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t80);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t82, acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t82);
    for (i = 0; i < 81; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t164, acc, t82);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t164);
    for (i = 0; i < 81; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t246, acc, t82);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t246);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t2);
    for (i = 0; i < 3; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(output, acc, t1);
}

/* curve coefficient constants */

static const limb_t const_one[5] = {
    UINT64_C(0x0000000000000001), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000)};

static const limb_t const_b[5] = {
    UINT64_C(0x00000000000000A6), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000), UINT64_C(0x0000000000000000),
    UINT64_C(0x0000000000000000)};

/* LUT for scalar multiplication by comb interleaving */
static const pt_aff_t lut_cmb[19][16] = {
    {
        {{UINT64_C(0x0000000000000001), UINT64_C(0x0000000000000000),
          UINT64_C(0x0000000000000000), UINT64_C(0x0000000000000000),
          UINT64_C(0x0000000000000000)},
         {UINT64_C(0x000CC99C9E9F1E14), UINT64_C(0x0002DDF23E3B122A),
          UINT64_C(0x00027E56EC6A529E), UINT64_C(0x00073689F7D41691),
          UINT64_C(0x00046C8F238F04C4)}},
        {{UINT64_C(0x0008E38E38E38D2C), UINT64_C(0x00038E38E38E38E3),
          UINT64_C(0x000471C71C71C71C), UINT64_C(0x00038E38E38E38E3),
          UINT64_C(0x000471C71C71C71C)},
         {UINT64_C(0x000117796B788A51), UINT64_C(0x0004C838768E8486),
          UINT64_C(0x00074D9B0424CF52), UINT64_C(0x00041075366EBD41),
          UINT64_C(0x0003B5E68E54D11D)}},
        {{UINT64_C(0x00001C387172C029), UINT64_C(0x0004279B488F6F02),
          UINT64_C(0x0004F05E34B15AD6), UINT64_C(0x000258F0DA65A32D),
          UINT64_C(0x000483B001F0C3C7)},
         {UINT64_C(0x0004823B068D7CC0), UINT64_C(0x0000C7FD37B58763),
          UINT64_C(0x00067ED904B1D771), UINT64_C(0x0001D38686A8A56B),
          UINT64_C(0x000484B93D728EA1)}},
        {{UINT64_C(0x0008EA01E4196EE1), UINT64_C(0x00034905BF5B297A),
          UINT64_C(0x0007447C611BEF5E), UINT64_C(0x00001829AE7D7ADB),
          UINT64_C(0x00017EEC8CA66B06)},
         {UINT64_C(0x0000C10C4D0125FB), UINT64_C(0x000020A9F3F3A624),
          UINT64_C(0x00027103CEF472D6), UINT64_C(0x0000B8216E293D29),
          UINT64_C(0x0000E3FA5A98C242)}},
        {{UINT64_C(0x0007A462275ECDA3), UINT64_C(0x00018C170DFCB702),
          UINT64_C(0x000241E756E56216), UINT64_C(0x000771F4F24A295D),
          UINT64_C(0x0005B7CB06F71A0F)},
         {UINT64_C(0x000328AD395ED26A), UINT64_C(0x0001CAF70A8E7168),
          UINT64_C(0x0000FF8BE664695F), UINT64_C(0x0007E4184137AE67),
          UINT64_C(0x00069DFB4A32D69E)}},
        {{UINT64_C(0x00022F3D1A834086), UINT64_C(0x00041ECA3623B485),
          UINT64_C(0x000674297E63115C), UINT64_C(0x000406C9C5D3F28F),
          UINT64_C(0x00031EE39C279CEE)},
         {UINT64_C(0x00053F08BBC2FF84), UINT64_C(0x000468767960B588),
          UINT64_C(0x000495E5AD5F0D51), UINT64_C(0x0007102439DB689C),
          UINT64_C(0x00064070B2980D03)}},
        {{UINT64_C(0x00062E8F3CBBCCCD), UINT64_C(0x0004BFF4EEFAA5B5),
          UINT64_C(0x0006C21A84538998), UINT64_C(0x0007A46150B4D83D),
          UINT64_C(0x00028C30C35CB4DC)},
         {UINT64_C(0x0009EBBD29C26CB2), UINT64_C(0x0007BAF9C97927A1),
          UINT64_C(0x0000F1391B4F0F65), UINT64_C(0x0001A44160EE4057),
          UINT64_C(0x0001EADEA8B087CC)}},
        {{UINT64_C(0x000BD2800F7C58E2), UINT64_C(0x0006AA0FC8C17CF8),
          UINT64_C(0x00012B72E124E103), UINT64_C(0x0007A22A20C37ACD),
          UINT64_C(0x00051583FBF07955)},
         {UINT64_C(0x000A705E55B12811), UINT64_C(0x00077F24806A4501),
          UINT64_C(0x00016CDB1FF83072), UINT64_C(0x0001FE2D523F7B40),
          UINT64_C(0x00002B561CD86E47)}},
        {{UINT64_C(0x000DD0548C39A634), UINT64_C(0x0007E5C8D222E7F3),
          UINT64_C(0x00077AFB529FB66D), UINT64_C(0x0007FE6BB0F0E041),
          UINT64_C(0x0001D44AD8E8E1BB)},
         {UINT64_C(0x000800FACA51071C), UINT64_C(0x0001B75CB6825594),
          UINT64_C(0x000524055248074A), UINT64_C(0x0001FEC05998A037),
          UINT64_C(0x000418509ED45EDE)}},
        {{UINT64_C(0x0004116786EF5595), UINT64_C(0x0002FDC9F0A70AF6),
          UINT64_C(0x0001393C9E2010EB), UINT64_C(0x000159CE63616097),
          UINT64_C(0x0002C31D8791CB8B)},
         {UINT64_C(0x0008485469328440), UINT64_C(0x00010DBB94FF5733),
          UINT64_C(0x000187E16B5054CE), UINT64_C(0x0007A1C20E552236),
          UINT64_C(0x00074B00D96EDD39)}},
        {{UINT64_C(0x00021E11C9041497), UINT64_C(0x000484A9E464D228),
          UINT64_C(0x00038D73E99FC3A5), UINT64_C(0x00037B99E592B813),
          UINT64_C(0x0005F3E5DF4C9D8E)},
         {UINT64_C(0x000369BDE19C53EC), UINT64_C(0x0006C90BB44FF031),
          UINT64_C(0x0002730C7A423157), UINT64_C(0x0004733CD7C2CEF1),
          UINT64_C(0x00001779F421C860)}},
        {{UINT64_C(0x0006444B2FC84A66), UINT64_C(0x00045CDBBBD00846),
          UINT64_C(0x0003B7859658B40F), UINT64_C(0x0006F9C075FA8F30),
          UINT64_C(0x0004C55A76F5ABEE)},
         {UINT64_C(0x0009AE065EC53CD0), UINT64_C(0x00026684080415BE),
          UINT64_C(0x0005AA8D964E4BA5), UINT64_C(0x000230B8100D807C),
          UINT64_C(0x000415674003896E)}},
        {{UINT64_C(0x000F7D10028B07C5), UINT64_C(0x0001CCB108309571),
          UINT64_C(0x00058D8CE498E384), UINT64_C(0x00054AA65A376DB0),
          UINT64_C(0x00007F4C660D79DC)},
         {UINT64_C(0x000FF278A9B6438D), UINT64_C(0x0004E340FE2BB231),
          UINT64_C(0x000699E71D615994), UINT64_C(0x000655479DBE2221),
          UINT64_C(0x0005C130AB3BD570)}},
        {{UINT64_C(0x000C6388B4B53E9C), UINT64_C(0x0000402286282147),
          UINT64_C(0x0002516CD8798759), UINT64_C(0x0007B179435AC419),
          UINT64_C(0x00018CEE31A89C76)},
         {UINT64_C(0x0000AB9AD0EC3379), UINT64_C(0x00065281151DE966),
          UINT64_C(0x00071CA903B27001), UINT64_C(0x0000E28FBCB94233),
          UINT64_C(0x00060976D76A3184)}},
        {{UINT64_C(0x0006D77EB1685C39), UINT64_C(0x0003CF8EE0115505),
          UINT64_C(0x00078D2B24AB1841), UINT64_C(0x00061B46CB0C6A81),
          UINT64_C(0x0002AF8490D4D499)},
         {UINT64_C(0x00056E3A3305760A), UINT64_C(0x00043B60E22B14EE),
          UINT64_C(0x000020C4845CFE0F), UINT64_C(0x0003D68AC0F06CA8),
          UINT64_C(0x000722A37E92E49C)}},
        {{UINT64_C(0x000A5F6FECDB7020), UINT64_C(0x00042B43E7540E44),
          UINT64_C(0x0002BD2B2059E2FE), UINT64_C(0x000631E18BEC8A26),
          UINT64_C(0x000580E22EFE7168)},
         {UINT64_C(0x0003035DF13C5516), UINT64_C(0x000682989776C95E),
          UINT64_C(0x000049780CDE914A), UINT64_C(0x000182478A356286),
          UINT64_C(0x0002945A3EE07E65)}},
    },
    {
        {{UINT64_C(0x0005A339F915A14B), UINT64_C(0x000715D733BA9825),
          UINT64_C(0x00023A52A2A78CA6), UINT64_C(0x0005B164EB92A51B),
          UINT64_C(0x00045D9A30B0E65F)},
         {UINT64_C(0x000ADD23F0E6BE0F), UINT64_C(0x0004271FA00AECFF),
          UINT64_C(0x0006F2881FB40864), UINT64_C(0x00034A01B35FC99B),
          UINT64_C(0x00058D1D8F643FFF)}},
        {{UINT64_C(0x000A0A4C877DD1B7), UINT64_C(0x00002B58C866A837),
          UINT64_C(0x0004B11FDE6972BD), UINT64_C(0x000525D5FEF644F2),
          UINT64_C(0x00028801D60D33EB)},
         {UINT64_C(0x000B74AA40171DE8), UINT64_C(0x0001482D80E7BB1C),
          UINT64_C(0x0006C7B54BF123BD), UINT64_C(0x00044EE83E8AAB32),
          UINT64_C(0x0001F0E12809FA9D)}},
        {{UINT64_C(0x00066A9CFB2131C9), UINT64_C(0x0001579B4E7D1E27),
          UINT64_C(0x0004032CBC5CCC2F), UINT64_C(0x000212394FCE5D79),
          UINT64_C(0x0002B5D4452CFFBB)},
         {UINT64_C(0x0006A4C542C01BA9), UINT64_C(0x00025184A46BB2DA),
          UINT64_C(0x00005344AC81105B), UINT64_C(0x0000FA6310182DB0),
          UINT64_C(0x00005AC302692942)}},
        {{UINT64_C(0x00019D20E0248E22), UINT64_C(0x0005CA354C3BCB85),
          UINT64_C(0x0002894DADACB4EC), UINT64_C(0x000276F52507A08E),
          UINT64_C(0x00061243F4DB4E50)},
         {UINT64_C(0x000FDF4AF08FAB34), UINT64_C(0x0004350560FCCBAC),
          UINT64_C(0x0005B2D27BD7791E), UINT64_C(0x00038BE1B282CEDF),
          UINT64_C(0x000645850A8CE169)}},
        {{UINT64_C(0x000568A7C178486C), UINT64_C(0x0001F2D08012728C),
          UINT64_C(0x0000402DE36CAB91), UINT64_C(0x000709B6DD2BAFBB),
          UINT64_C(0x00068F1171227D8D)},
         {UINT64_C(0x00068279A889EA5B), UINT64_C(0x0006E2F73FBCFF46),
          UINT64_C(0x0006DA5825DC94E7), UINT64_C(0x000250F1787A7A74),
          UINT64_C(0x0000104623421C27)}},
        {{UINT64_C(0x000B4A1DF4B52308), UINT64_C(0x00037F70383BCE58),
          UINT64_C(0x0005B4B440F91C99), UINT64_C(0x0004DFE0E63344A6),
          UINT64_C(0x0003F78ADB1149D0)},
         {UINT64_C(0x0005EE5A5CF1B775), UINT64_C(0x0006E82328650CFE),
          UINT64_C(0x00053DBD7CEC0ECD), UINT64_C(0x0006AD64013C83E3),
          UINT64_C(0x0003BAECE58084AC)}},
        {{UINT64_C(0x0003535E55FD3BB1), UINT64_C(0x00021AA38BD04D1A),
          UINT64_C(0x00030BBC74A0B7B2), UINT64_C(0x0000D651469A30EA),
          UINT64_C(0x0002BEEBF0135D98)},
         {UINT64_C(0x000BE47EA63780DD), UINT64_C(0x0007996630E63D8F),
          UINT64_C(0x0003D71B02D21DFE), UINT64_C(0x00072EE70BB7F048),
          UINT64_C(0x00003A0C52A51F7B)}},
        {{UINT64_C(0x000E522DB194B350), UINT64_C(0x0001362A59882C6C),
          UINT64_C(0x0005B09E052C570C), UINT64_C(0x0002D26AB280800D),
          UINT64_C(0x0001E221931BDDDC)},
         {UINT64_C(0x0004772E6A06F259), UINT64_C(0x0007D07B179D230C),
          UINT64_C(0x00071E03FC15549C), UINT64_C(0x0004F77A7EDB5E65),
          UINT64_C(0x000510C0C34DF5E2)}},
        {{UINT64_C(0x0004F224FF48904F), UINT64_C(0x00048434D5472DD5),
          UINT64_C(0x0004EDEE682D26D3), UINT64_C(0x000209BB373C40AF),
          UINT64_C(0x00024DAA1D2897A9)},
         {UINT64_C(0x00021D4977542F7C), UINT64_C(0x00074599CAF0ABB5),
          UINT64_C(0x00058BE88021C3ED), UINT64_C(0x0004208DC3B55B12),
          UINT64_C(0x00052C47E8541E68)}},
        {{UINT64_C(0x00040C5256AA4EC9), UINT64_C(0x000415F8F060E908),
          UINT64_C(0x0002805A8B76EBE6), UINT64_C(0x0001873A8DE991C3),
          UINT64_C(0x000386C4A0579BDC)},
         {UINT64_C(0x00017774AE596D94), UINT64_C(0x00076ED22CE59EC5),
          UINT64_C(0x00035ED29CE7AADA), UINT64_C(0x0006B0362F90D780),
          UINT64_C(0x0002F704527505EC)}},
        {{UINT64_C(0x000FCF13CEEA0CB5), UINT64_C(0x00018312ABDF7C32),
          UINT64_C(0x0000757802E622B3), UINT64_C(0x000490DC4FAFA5E7),
          UINT64_C(0x0002F574211FD5E9)},
         {UINT64_C(0x000FE0FD3DB8102B), UINT64_C(0x000250E79DF56839),
          UINT64_C(0x000279E546ADD6E1), UINT64_C(0x000145A2755E6C50),
          UINT64_C(0x0001190CE5089C0D)}},
        {{UINT64_C(0x000E3D775DED50BF), UINT64_C(0x0002D12CB77299E9),
          UINT64_C(0x0000BB582E43FB17), UINT64_C(0x00000C592B860CC1),
          UINT64_C(0x0006580719B67251)},
         {UINT64_C(0x0000FAE99B30AE7B), UINT64_C(0x00040F36F14B66A3),
          UINT64_C(0x0001B1A66B1DFF10), UINT64_C(0x0007E66D7F84E375),
          UINT64_C(0x000247DD6C1DBF6D)}},
        {{UINT64_C(0x000DE16D4416BAED), UINT64_C(0x0007F25B557A5F90),
          UINT64_C(0x000174197A990092), UINT64_C(0x00064E76B1B0E5AD),
          UINT64_C(0x0007C3B0A8276891)},
         {UINT64_C(0x000FF3F0D753BB34), UINT64_C(0x0004BC7095E070BA),
          UINT64_C(0x0005AE24B0B13E9D), UINT64_C(0x00033D3593791BC1),
          UINT64_C(0x0004A5C64A628512)}},
        {{UINT64_C(0x000679BA1D37863B), UINT64_C(0x0007E88BAD3CE7BA),
          UINT64_C(0x0007E856BFB2DCD0), UINT64_C(0x0001298B3D7E0A07),
          UINT64_C(0x000648DA87B47E6C)},
         {UINT64_C(0x0004116614706E8B), UINT64_C(0x00038083A1633124),
          UINT64_C(0x00040275662A5821), UINT64_C(0x0005AF932DE19F46),
          UINT64_C(0x000325815252F969)}},
        {{UINT64_C(0x0007431F85A6A2F2), UINT64_C(0x0003E366F4DF51AC),
          UINT64_C(0x000007E3FCF91EED), UINT64_C(0x00019A60A1F6A15A),
          UINT64_C(0x0005D281D485553A)},
         {UINT64_C(0x000AEE657D07C121), UINT64_C(0x0005B6E017CC1FF3),
          UINT64_C(0x000087D40E6AE64B), UINT64_C(0x000110B3E1403845),
          UINT64_C(0x0007B4C48BE20679)}},
        {{UINT64_C(0x0002EDBF68793170), UINT64_C(0x000433C366D3E8F9),
          UINT64_C(0x0005954AEAA0ACE5), UINT64_C(0x00017404AB74C945),
          UINT64_C(0x0005D492DD30DC05)},
         {UINT64_C(0x000664322925053F), UINT64_C(0x000443FCBBEB949A),
          UINT64_C(0x0004BC23B86D7ADE), UINT64_C(0x0002B04916F727D6),
          UINT64_C(0x000762FF85E51567)}},
    },
    {
        {{UINT64_C(0x000956A68246367E), UINT64_C(0x00021897DE9A1875),
          UINT64_C(0x0005C9CCFFBCB16F), UINT64_C(0x00070E4257F8B255),
          UINT64_C(0x00000C9A877C9B00)},
         {UINT64_C(0x000CE0B185E2541C), UINT64_C(0x0006D5D853109264),
          UINT64_C(0x00056CC68AD4E64D), UINT64_C(0x0002AF2B66927356),
          UINT64_C(0x00037EE9E95C855D)}},
        {{UINT64_C(0x000053A329418B8A), UINT64_C(0x0006646D7C973FB6),
          UINT64_C(0x0005395E69EEA7AB), UINT64_C(0x00010000B1282007),
          UINT64_C(0x00068CC15F88CFED)},
         {UINT64_C(0x000E8B128FBFD773), UINT64_C(0x0007FB20DB05AF31),
          UINT64_C(0x0007739B3F5C4BDE), UINT64_C(0x0004039AAFD5F2DC),
          UINT64_C(0x000721C85E9A29EA)}},
        {{UINT64_C(0x000B0D23A656BC62), UINT64_C(0x0005AC669CF7C7F3),
          UINT64_C(0x000209617182C48D), UINT64_C(0x000218B4CB496794),
          UINT64_C(0x0007180815D1B6B7)},
         {UINT64_C(0x000EEC1FDDEF69D9), UINT64_C(0x0003AEF0ADEF2D2B),
          UINT64_C(0x00030326BF4476DB), UINT64_C(0x000047E91D24E181),
          UINT64_C(0x000598190A0CFD77)}},
        {{UINT64_C(0x0006D10C22EF9F96), UINT64_C(0x000080EC0F94F2F7),
          UINT64_C(0x0007AA4F0AEDAE30), UINT64_C(0x0002516F4D4FE6D7),
          UINT64_C(0x00049EFAA3C8BD00)},
         {UINT64_C(0x000098EA4E6E0992), UINT64_C(0x0005BF3F949C023B),
          UINT64_C(0x000380A5EEBA5CD8), UINT64_C(0x00026413305ED75F),
          UINT64_C(0x0000F418433F8A27)}},
        {{UINT64_C(0x00089ECDDD1EBCA4), UINT64_C(0x000359E16F504E76),
          UINT64_C(0x0004CF9626CD6FBA), UINT64_C(0x0001174C74344098),
          UINT64_C(0x00057564D0F17F65)},
         {UINT64_C(0x0001195CBB8FB356), UINT64_C(0x0005A8577B21D169),
          UINT64_C(0x0006489F105E543F), UINT64_C(0x0007B3A20275D3E2),
          UINT64_C(0x0007AAB2BB22DFB3)}},
        {{UINT64_C(0x0002CA70745AE235), UINT64_C(0x00023A96DDE369C5),
          UINT64_C(0x0005012A48800C95), UINT64_C(0x00066B4F8BF4154D),
          UINT64_C(0x0006B8F21F53603C)},
         {UINT64_C(0x000F5462CCFC9D22), UINT64_C(0x00030A858C108F13),
          UINT64_C(0x000689819655D129), UINT64_C(0x0000E720A0FCEA3E),
          UINT64_C(0x00011F9DC5101E53)}},
        {{UINT64_C(0x0001DE0F018982F2), UINT64_C(0x0000EADE77ECC8D4),
          UINT64_C(0x0003B956FFEE4A79), UINT64_C(0x0004B8BBFBD7479E),
          UINT64_C(0x00005F99662FE03E)},
         {UINT64_C(0x0004D7EA312F393C), UINT64_C(0x0000BD83AE160FCB),
          UINT64_C(0x0001EC8D3FC68C3B), UINT64_C(0x000437ABD0A447B9),
          UINT64_C(0x000735FC8AD2BB1C)}},
        {{UINT64_C(0x000B9EE6D50FF37D), UINT64_C(0x00004D648636CDB2),
          UINT64_C(0x00039C45B71D2EBA), UINT64_C(0x00000F0D22EDF891),
          UINT64_C(0x00014C417247C2C5)},
         {UINT64_C(0x0006CAB42B8C5660), UINT64_C(0x00018D3158E77BC4),
          UINT64_C(0x00039A7DAB63293E), UINT64_C(0x0004410B20B68C38),
          UINT64_C(0x0005143C90B7948D)}},
        {{UINT64_C(0x0004B03CF603CB83), UINT64_C(0x0000D1179A1254F5),
          UINT64_C(0x0000FC1EF688A885), UINT64_C(0x0002CDCC95C8E925),
          UINT64_C(0x0002791D839354A1)},
         {UINT64_C(0x000642016F8D0BF2), UINT64_C(0x00066E650A9B34E8),
          UINT64_C(0x00041DBF68CBB4A7), UINT64_C(0x000512CF961BC16F),
          UINT64_C(0x0003B3E68158737A)}},
        {{UINT64_C(0x0008CF645460D714), UINT64_C(0x0007670AA8B430E8),
          UINT64_C(0x000574F16D3AC934), UINT64_C(0x00051A943821A714),
          UINT64_C(0x0006DE69E5DF9951)},
         {UINT64_C(0x0007DE4AADCCB9AA), UINT64_C(0x000135145C55A55A),
          UINT64_C(0x000238B53DDFBA15), UINT64_C(0x00068D2109040438),
          UINT64_C(0x000063397853B65B)}},
        {{UINT64_C(0x00078630F0E679A9), UINT64_C(0x000648A3915EA099),
          UINT64_C(0x0001B46B606FA986), UINT64_C(0x0005DE57BB1E25B5),
          UINT64_C(0x0004F4E27D03AF36)},
         {UINT64_C(0x000CE066A9452CC5), UINT64_C(0x00045772A10AC378),
          UINT64_C(0x000305FFC4931F9C), UINT64_C(0x0006B3AE4651216A),
          UINT64_C(0x0001EBD44A8A3E39)}},
        {{UINT64_C(0x0004B0B22DFA4A07), UINT64_C(0x00035F7907812083),
          UINT64_C(0x0006F74B674F2672), UINT64_C(0x000388F7301DAC59),
          UINT64_C(0x000159AD55DA44EC)},
         {UINT64_C(0x000DE4AFBA89C1F3), UINT64_C(0x0007E80CAB614F22),
          UINT64_C(0x00059A999E84955B), UINT64_C(0x0000FA70D936DB5E),
          UINT64_C(0x00026F2DA8417650)}},
        {{UINT64_C(0x000E186902C85447), UINT64_C(0x000464F8386F8A64),
          UINT64_C(0x00053B0F937B7CD3), UINT64_C(0x00003B7187584567),
          UINT64_C(0x000308946F3D80A4)},
         {UINT64_C(0x000D1B1C9720BFE2), UINT64_C(0x0002400C63A10347),
          UINT64_C(0x00052C5C71281FD2), UINT64_C(0x00064118DAD42634),
          UINT64_C(0x0002FB1EB2D89702)}},
        {{UINT64_C(0x00080BE5600FDDD3), UINT64_C(0x0002732F61BAFB7A),
          UINT64_C(0x0002E5DE598D6FBC), UINT64_C(0x000041E2FDE427CD),
          UINT64_C(0x000506CBA13B20AF)},
         {UINT64_C(0x000FD470BC53488D), UINT64_C(0x0005F2E4B029AB6D),
          UINT64_C(0x0000096963502BB6), UINT64_C(0x0005FD18C530903B),
          UINT64_C(0x0005F0ED95E0A6DB)}},
        {{UINT64_C(0x000A16408A941AAB), UINT64_C(0x00021F45EB4DC5BD),
          UINT64_C(0x0000F60F2631C101), UINT64_C(0x0005AE7748F7EDC4),
          UINT64_C(0x0002D03801D3B95D)},
         {UINT64_C(0x0000E8545F3E14DB), UINT64_C(0x0001650799114990),
          UINT64_C(0x000686592FD11BE6), UINT64_C(0x000434D0445B2D2A),
          UINT64_C(0x00000759D194E76A)}},
        {{UINT64_C(0x0008B92FABB27EE6), UINT64_C(0x0007CF7A4E4368A8),
          UINT64_C(0x00005ACFDBBB69ED), UINT64_C(0x0006891A54C2CC5E),
          UINT64_C(0x0007007C79A1E753)},
         {UINT64_C(0x000FB2099EDFE6F7), UINT64_C(0x0004967D75BF94F0),
          UINT64_C(0x0007F61375A45D1E), UINT64_C(0x0003B22D99BD3A83),
          UINT64_C(0x000240E6DA8B843D)}},
    },
    {
        {{UINT64_C(0x0002B047D234E1E6), UINT64_C(0x000051F630DB7224),
          UINT64_C(0x000101E259F37AB2), UINT64_C(0x0007C1F58C65113B),
          UINT64_C(0x000263DCEE305FFC)},
         {UINT64_C(0x000D1B3A99462779), UINT64_C(0x0005034BF259BEDD),
          UINT64_C(0x0006E55CD8E9F25D), UINT64_C(0x00000FF1B23C08C9),
          UINT64_C(0x000193E0F15E9D84)}},
        {{UINT64_C(0x0005E42789FDE1B6), UINT64_C(0x0001894F4E480D11),
          UINT64_C(0x0002FA3C0071679A), UINT64_C(0x0005A18A5FED0EDD),
          UINT64_C(0x00032E050C320BCA)},
         {UINT64_C(0x000129644B586CC6), UINT64_C(0x00046490AB54EE67),
          UINT64_C(0x00014EDFB7E99D26), UINT64_C(0x0005F72BBB17A488),
          UINT64_C(0x00016D460CA476AE)}},
        {{UINT64_C(0x000ED9FF353B3EBD), UINT64_C(0x0000607194AF236F),
          UINT64_C(0x0004B55C55B42B97), UINT64_C(0x00007DF4B19150C4),
          UINT64_C(0x000071DD14E1914E)},
         {UINT64_C(0x0006D2391EE57097), UINT64_C(0x0006B5C7EAFAC50A),
          UINT64_C(0x000743B60C540A6E), UINT64_C(0x0002052C67ED83B9),
          UINT64_C(0x0007218AF829BCD2)}},
        {{UINT64_C(0x000B925893F33C1F), UINT64_C(0x00067B1285BA7F1E),
          UINT64_C(0x000653712C1B3339), UINT64_C(0x0005676C8172A410),
          UINT64_C(0x00070EFC597208AE)},
         {UINT64_C(0x000960B5F604A33D), UINT64_C(0x0007921F9338E496),
          UINT64_C(0x000049FA1FFAFC93), UINT64_C(0x000161F6DCEE43A1),
          UINT64_C(0x000066EEA7D1AFAB)}},
        {{UINT64_C(0x000AF49590893929), UINT64_C(0x000070D65CE0014B),
          UINT64_C(0x000140DF3289D3DC), UINT64_C(0x000345791D5EA109),
          UINT64_C(0x00005255A323DD85)},
         {UINT64_C(0x000401F6DCF0F1DA), UINT64_C(0x000074049990B1B6),
          UINT64_C(0x000618D750975313), UINT64_C(0x000287BCB87A0714),
          UINT64_C(0x0001F72793687C35)}},
        {{UINT64_C(0x000168DAE873F26A), UINT64_C(0x0001E0AAFF1790B1),
          UINT64_C(0x000459C70C5F4D75), UINT64_C(0x0002A897AEB60F5E),
          UINT64_C(0x0004FE8D9CCEFE84)},
         {UINT64_C(0x000C5FA352BD1939), UINT64_C(0x0000D8AE757A71AF),
          UINT64_C(0x00049805C7DBD1B7), UINT64_C(0x00052A2EB32E9871),
          UINT64_C(0x0002C5844141B0C6)}},
        {{UINT64_C(0x00005696DA8028D2), UINT64_C(0x00071FFFC7289A23),
          UINT64_C(0x0006341DC5E55F0B), UINT64_C(0x00033AE49FFDF4DC),
          UINT64_C(0x00015F09ED39A715)},
         {UINT64_C(0x000674750BC4C3AD), UINT64_C(0x00041954C18CE813),
          UINT64_C(0x000694204D31CC9C), UINT64_C(0x0007E21A93A0C726),
          UINT64_C(0x000300024CEC65AE)}},
        {{UINT64_C(0x000EBD07C0D77453), UINT64_C(0x0007BF58C754CC24),
          UINT64_C(0x0000F87F9B37B295), UINT64_C(0x00024B9A92A35036),
          UINT64_C(0x000289648E3611DD)},
         {UINT64_C(0x00040BB7D1FD0ECE), UINT64_C(0x0001A131BEAD6492),
          UINT64_C(0x00069423BE245B95), UINT64_C(0x0000669C14214A82),
          UINT64_C(0x000379EBC399963B)}},
        {{UINT64_C(0x0007DDA0CDD635C8), UINT64_C(0x00035E41D7F8650E),
          UINT64_C(0x0003F70DD4244C32), UINT64_C(0x00067C3E0F50B486),
          UINT64_C(0x000247B2A722631B)},
         {UINT64_C(0x000BFFD05202D9FB), UINT64_C(0x00060A9C44DD611B),
          UINT64_C(0x00036DCF6E5FD0D6), UINT64_C(0x000054F5818F5DF8),
          UINT64_C(0x0004E905149BD33D)}},
        {{UINT64_C(0x0001A02B8DDADBAE), UINT64_C(0x0004B904A0731902),
          UINT64_C(0x0002BCFC886A4F5E), UINT64_C(0x00050E5A1340E58E),
          UINT64_C(0x000538853D4322BB)},
         {UINT64_C(0x0004FA19E1071C4B), UINT64_C(0x00020DFF018FE134),
          UINT64_C(0x000747F9F7B44E41), UINT64_C(0x0001A6855EE03199),
          UINT64_C(0x00023B78B0C2DA8E)}},
        {{UINT64_C(0x000A78F96C2693D0), UINT64_C(0x0004E852C54AB8B2),
          UINT64_C(0x00050BD1203E05C7), UINT64_C(0x0007EBCB440E392A),
          UINT64_C(0x0002902087BF3096)},
         {UINT64_C(0x0003CE5F90574625), UINT64_C(0x0003386306A925F7),
          UINT64_C(0x0006406D4F228721), UINT64_C(0x00077FA1131F3503),
          UINT64_C(0x0004E5E2E878636F)}},
        {{UINT64_C(0x000931F13967D25A), UINT64_C(0x00039563F11ADEC1),
          UINT64_C(0x000148DC8BDD5189), UINT64_C(0x000143E419A5E6C0),
          UINT64_C(0x0005DAD68377E37C)},
         {UINT64_C(0x00068A723527F5D0), UINT64_C(0x0000F1F89C72A873),
          UINT64_C(0x0000CB4FD5169F8D), UINT64_C(0x0001C570DC05123D),
          UINT64_C(0x0007DE29900ACC22)}},
        {{UINT64_C(0x0009281B46548202), UINT64_C(0x00014A3F78CE6431),
          UINT64_C(0x00075ADDA3C012A7), UINT64_C(0x0000EA845B0B8C70),
          UINT64_C(0x00079B908BEB53C6)},
         {UINT64_C(0x00019FE799C7C809), UINT64_C(0x0007B93B993A157B),
          UINT64_C(0x00077F3826FC8939), UINT64_C(0x00023CE645BC7C8A),
          UINT64_C(0x00007E8659F8E036)}},
        {{UINT64_C(0x00033DC53F98B574), UINT64_C(0x0007432F5C3CCD27),
          UINT64_C(0x0001E8355E71A3DC), UINT64_C(0x000536F3A5B8A0D5),
          UINT64_C(0x00066FBA2484854C)},
         {UINT64_C(0x000BC9ABA085FC08), UINT64_C(0x0006CD471084166A),
          UINT64_C(0x00077299B5B548AE), UINT64_C(0x0000A08F4C0976C0),
          UINT64_C(0x0007ED9BF62B1BFA)}},
        {{UINT64_C(0x000940ECF52AFA23), UINT64_C(0x000448C9B9325795),
          UINT64_C(0x0006DAFB14422BE9), UINT64_C(0x000331906DED74E7),
          UINT64_C(0x00034AA18796A795)},
         {UINT64_C(0x00017A1089CF42AB), UINT64_C(0x0004BBA8E1B7CC68),
          UINT64_C(0x00072F74CCB808A3), UINT64_C(0x00018BD75A6C68E2),
          UINT64_C(0x00001B9BDB7DACA2)}},
        {{UINT64_C(0x000018F0E8761113), UINT64_C(0x00014856FBD5FE7E),
          UINT64_C(0x00052B8E5AA39575), UINT64_C(0x00061A72375DBF30),
          UINT64_C(0x0002A1B12B4FD1A0)},
         {UINT64_C(0x000CCCFE4B5CE5E2), UINT64_C(0x00013D03A9E9851C),
          UINT64_C(0x000547736DB57F8B), UINT64_C(0x0002C281B74401A1),
          UINT64_C(0x0006AD41C9A8F14A)}},
    },
    {
        {{UINT64_C(0x000FAA572EB89E8E), UINT64_C(0x000167953742D9F0),
          UINT64_C(0x00019869F9FB4B12), UINT64_C(0x0007CBE7B69E4D6D),
          UINT64_C(0x000555F44F93253E)},
         {UINT64_C(0x000652D610CFD61D), UINT64_C(0x000450DD0FD70202),
          UINT64_C(0x0000FA8099F542AA), UINT64_C(0x00028CDD184A6382),
          UINT64_C(0x0005C334A78F9954)}},
        {{UINT64_C(0x00089F7423E24828), UINT64_C(0x0006ECC59417EC04),
          UINT64_C(0x0005923FCD743CE4), UINT64_C(0x00058E47C67B3854),
          UINT64_C(0x0001FEDFDB0EED94)},
         {UINT64_C(0x0007EEE7E104E1B6), UINT64_C(0x0002BAC9B4C39633),
          UINT64_C(0x000719482E22B7DB), UINT64_C(0x000289C779B2B427),
          UINT64_C(0x0005A26A1E0E5EC6)}},
        {{UINT64_C(0x000239DE0DF21E4A), UINT64_C(0x0003990AE0816058),
          UINT64_C(0x0007B031A49D9306), UINT64_C(0x0003AA82A7C3C63B),
          UINT64_C(0x00024190E64F1DA4)},
         {UINT64_C(0x00044CF3369C86A2), UINT64_C(0x00003ECD2B5B335C),
          UINT64_C(0x0000C07180F70933), UINT64_C(0x00065C0B6A34AA71),
          UINT64_C(0x0002462A8EC2EB00)}},
        {{UINT64_C(0x0005C2BDA6A8C3D3), UINT64_C(0x0004167C033B9C4C),
          UINT64_C(0x0001CB7DF36DB648), UINT64_C(0x000694122F8ABCB3),
          UINT64_C(0x00025F3E2C20B169)},
         {UINT64_C(0x0007E143BB0C3DB3), UINT64_C(0x00001781BBE98778),
          UINT64_C(0x00037A2A4A306DA9), UINT64_C(0x0005470297B20C04),
          UINT64_C(0x000141CF4B717BAF)}},
        {{UINT64_C(0x00070EB500B9F037), UINT64_C(0x0005727999C118FA),
          UINT64_C(0x0006027735DBD10A), UINT64_C(0x0007F78C3C18F3DD),
          UINT64_C(0x0004111344448D53)},
         {UINT64_C(0x000B19D3D6D2A8D9), UINT64_C(0x000516FF15756C60),
          UINT64_C(0x0002EE628F2DE048), UINT64_C(0x00048114031C65C1),
          UINT64_C(0x000585C81048736A)}},
        {{UINT64_C(0x000AB501B01B067A), UINT64_C(0x00067899A4477FAE),
          UINT64_C(0x000274F338EBAB9E), UINT64_C(0x00047234930DA1EF),
          UINT64_C(0x0007A996A9C60A6D)},
         {UINT64_C(0x0007B7E042371080), UINT64_C(0x00076E9E7CA96E5C),
          UINT64_C(0x00014EDEE2498639), UINT64_C(0x00020537E3A2932F),
          UINT64_C(0x0006743F9AFE0CF6)}},
        {{UINT64_C(0x0000BB8FB7939BAE), UINT64_C(0x000595F428F1564B),
          UINT64_C(0x00073AA57A8B8892), UINT64_C(0x000443C7156AD97C),
          UINT64_C(0x000394DCDE01C803)},
         {UINT64_C(0x00010277303A2295), UINT64_C(0x00036E65529E463B),
          UINT64_C(0x00002050CC0BBF19), UINT64_C(0x0007AF642321CFD0),
          UINT64_C(0x0007A578DB9DCA09)}},
        {{UINT64_C(0x000ECA0FD78BBA28), UINT64_C(0x000140DC5DBBD618),
          UINT64_C(0x0002BEF02AD1BB50), UINT64_C(0x00031A4817C3357E),
          UINT64_C(0x0000A1117B528B02)},
         {UINT64_C(0x000A242C41E7EE0C), UINT64_C(0x00024C715E851E5F),
          UINT64_C(0x000711A22BB49D60), UINT64_C(0x0002824BD92ABEC0),
          UINT64_C(0x00043A69559D6B3C)}},
        {{UINT64_C(0x000E68D7A078C305), UINT64_C(0x000734955FCFC798),
          UINT64_C(0x0000E1E3F52E8AA5), UINT64_C(0x0004E9D3FA7AD926),
          UINT64_C(0x000712628E743D52)},
         {UINT64_C(0x00073814D797A50E), UINT64_C(0x000153715FFCA99D),
          UINT64_C(0x000558A8FDE19C66), UINT64_C(0x0002204AA778B5D7),
          UINT64_C(0x0007BAA676A5FF03)}},
        {{UINT64_C(0x000DF4BDB34B5256), UINT64_C(0x0001D965B241D3BD),
          UINT64_C(0x0001271A0805AF80), UINT64_C(0x0000B84BD41CE836),
          UINT64_C(0x0007F967C3AC88CE)},
         {UINT64_C(0x000FDB1299F7351C), UINT64_C(0x00003C874EE7BCAC),
          UINT64_C(0x00039993680976FC), UINT64_C(0x00047FE73F6F2145),
          UINT64_C(0x000625098EDAEF41)}},
        {{UINT64_C(0x000D83AF4F7F1E6E), UINT64_C(0x0000FDC16634A3DE),
          UINT64_C(0x00015FB28A735369), UINT64_C(0x0007AF0B085A2FF6),
          UINT64_C(0x0001B48F17A9E049)},
         {UINT64_C(0x0003C8EA301DD7E4), UINT64_C(0x00040B69DE358F2C),
          UINT64_C(0x00050BA0AC83C141), UINT64_C(0x0000E0D532814AB2),
          UINT64_C(0x00065E5F74F8CAB4)}},
        {{UINT64_C(0x0000F47568339162), UINT64_C(0x000606AFE5A96F3B),
          UINT64_C(0x0002BA3A04C6A76C), UINT64_C(0x0002488944DE85DD),
          UINT64_C(0x000374DF90B6C521)},
         {UINT64_C(0x000382B073CF14E5), UINT64_C(0x0007C67C95A17E79),
          UINT64_C(0x00015C33E721463B), UINT64_C(0x00073EF909CF6359),
          UINT64_C(0x00050141FF3ED74B)}},
        {{UINT64_C(0x000EEEEA5F5DDB32), UINT64_C(0x0003ACDE25B0D36D),
          UINT64_C(0x0002CF889155A989), UINT64_C(0x00021FD9C628076E),
          UINT64_C(0x00056A5EB7356DC8)},
         {UINT64_C(0x0006C202D420A6E2), UINT64_C(0x00031BABD32E4E48),
          UINT64_C(0x00044C3D05F2D30C), UINT64_C(0x0007670E334C2F98),
          UINT64_C(0x0004F79A1917FFEC)}},
        {{UINT64_C(0x00051F5BDD58277B), UINT64_C(0x000003D523F21BB6),
          UINT64_C(0x00021806DD8F5937), UINT64_C(0x0000FD12EFB4623F),
          UINT64_C(0x0007A4AF6F3F8526)},
         {UINT64_C(0x0004669EB76FE0E1), UINT64_C(0x00043FDD719D5F7A),
          UINT64_C(0x00003CA28B366FB6), UINT64_C(0x000248847E6F73B9),
          UINT64_C(0x00065DA146D053AB)}},
        {{UINT64_C(0x000AC0CBD84326A2), UINT64_C(0x0004C03CAD532948),
          UINT64_C(0x0005E030FE480B61), UINT64_C(0x0003DB7F18BA70C2),
          UINT64_C(0x00035CE1592DE363)},
         {UINT64_C(0x000203476A5CD536), UINT64_C(0x00079198612CA2C7),
          UINT64_C(0x0005BB48D8AC89B9), UINT64_C(0x000295999FBC011E),
          UINT64_C(0x0003AAA59B60FE8A)}},
        {{UINT64_C(0x000DD31F74351DCE), UINT64_C(0x0005B15757FF87F4),
          UINT64_C(0x0002E2B1C4F1048F), UINT64_C(0x00069BB62674CD0F),
          UINT64_C(0x0001544612843FE9)},
         {UINT64_C(0x00039BD2752EE3C1), UINT64_C(0x00017A2616FD02E5),
          UINT64_C(0x000447AD0B0A32FC), UINT64_C(0x0005CE5C96F64A93),
          UINT64_C(0x00029642594BA9AA)}},
    },
    {
        {{UINT64_C(0x000DEE95CA3BB6FE), UINT64_C(0x00056F0ACB87E096),
          UINT64_C(0x00036182366ABC67), UINT64_C(0x0005217F2CD26751),
          UINT64_C(0x00021E6D524F4A17)},
         {UINT64_C(0x0000BCE9C99BB07D), UINT64_C(0x0000C27581F2BA83),
          UINT64_C(0x00020CF33CBD1603), UINT64_C(0x00010678FB67FFF1),
          UINT64_C(0x0005C6CD7703A25E)}},
        {{UINT64_C(0x0005BBA8048392C8), UINT64_C(0x0002542A96821BD7),
          UINT64_C(0x00075648B3E12141), UINT64_C(0x0006D7FBCF181560),
          UINT64_C(0x0002C576A6815E41)},
         {UINT64_C(0x000518AAF86E6DE1), UINT64_C(0x0001C518693DD84F),
          UINT64_C(0x0006675DCF58B296), UINT64_C(0x000644D5F602501F),
          UINT64_C(0x00077A80CA4E57C8)}},
        {{UINT64_C(0x000159EE8D243A23), UINT64_C(0x0006E29DBC72D5E0),
          UINT64_C(0x0000DA0A1E2F2981), UINT64_C(0x000651B05EAE7237),
          UINT64_C(0x000582761CC33610)},
         {UINT64_C(0x0004C0AF035DD89C), UINT64_C(0x0004D1937B5F3230),
          UINT64_C(0x00068AF1E665190F), UINT64_C(0x00076067020738A8),
          UINT64_C(0x0003815147C9AEE7)}},
        {{UINT64_C(0x000115871847A6CB), UINT64_C(0x00020F85C36D0499),
          UINT64_C(0x0004733334C55205), UINT64_C(0x0004D650B5CCA6A3),
          UINT64_C(0x0002C404C377C0C1)},
         {UINT64_C(0x000A12DF826A93D6), UINT64_C(0x0007B4188D135AA7),
          UINT64_C(0x0002D01F76CA5FE4), UINT64_C(0x000155326D392240),
          UINT64_C(0x0000E516B40BA9FD)}},
        {{UINT64_C(0x000F5546A732C66D), UINT64_C(0x00018E15DB635DC6),
          UINT64_C(0x00074C7913432090), UINT64_C(0x0005F678885345A4),
          UINT64_C(0x00061829FB56D48A)},
         {UINT64_C(0x000677B5BB743208), UINT64_C(0x000238E1E35F0380),
          UINT64_C(0x00024F21B26421F5), UINT64_C(0x000405586E33386B),
          UINT64_C(0x0000200A7DCD0CE5)}},
        {{UINT64_C(0x000E70BA7605C9B6), UINT64_C(0x00057F5CC0B99049),
          UINT64_C(0x00042F0C89079EE3), UINT64_C(0x0000AF253B6AB483),
          UINT64_C(0x00004DBD3447DEE4)},
         {UINT64_C(0x0005CDCCDF6731CF), UINT64_C(0x0003ACA58F2B6389),
          UINT64_C(0x0002874A4632D9E3), UINT64_C(0x0006EAB51A6C6508),
          UINT64_C(0x0004974444B06711)}},
        {{UINT64_C(0x000083E23DACB58A), UINT64_C(0x0003D224DBA9AFFC),
          UINT64_C(0x0000AD693FF2352E), UINT64_C(0x0000EE821C843787),
          UINT64_C(0x0006599DDD43FF17)},
         {UINT64_C(0x0001129BD5E19FA2), UINT64_C(0x0001DDF5DE008359),
          UINT64_C(0x0004F3504C03BF84), UINT64_C(0x000064EFD7D15539),
          UINT64_C(0x000427DB9DD33F27)}},
        {{UINT64_C(0x000B147505184DFD), UINT64_C(0x0005A25B9C7E076D),
          UINT64_C(0x00075CFCA22E44B6), UINT64_C(0x0002A4BF3C7632E7),
          UINT64_C(0x00032E9918544496)},
         {UINT64_C(0x000455D9704D3074), UINT64_C(0x00037C549C367FB6),
          UINT64_C(0x0004A270B27EFBB8), UINT64_C(0x0001428CFA018772),
          UINT64_C(0x0007A9637BAE976C)}},
        {{UINT64_C(0x000311ADC83DB0DD), UINT64_C(0x0000B4BCB0670913),
          UINT64_C(0x0005D30A2C0F96FC), UINT64_C(0x0001F1A1FF4D0BB0),
          UINT64_C(0x0007D2975772506E)},
         {UINT64_C(0x000EFF5E207D306E), UINT64_C(0x00068BE603FE3D2A),
          UINT64_C(0x0005C11F5D2A4998), UINT64_C(0x00006F76D4F8557F),
          UINT64_C(0x0006F3CFD87A769F)}},
        {{UINT64_C(0x00065185A90EC696), UINT64_C(0x0005E67E616805F1),
          UINT64_C(0x0000BBF738A1A711), UINT64_C(0x0005428B0D45A57C),
          UINT64_C(0x0004C8EE45621C1C)},
         {UINT64_C(0x000FAA46B5E985B8), UINT64_C(0x00029F0A5A957DDD),
          UINT64_C(0x000236B91A401AEE), UINT64_C(0x00064166423D44F1),
          UINT64_C(0x0000A4FE9BC04A82)}},
        {{UINT64_C(0x000F394FC786A4E4), UINT64_C(0x0005D5C301931568),
          UINT64_C(0x00016E5A4E0F86A1), UINT64_C(0x0003E462D1C459B7),
          UINT64_C(0x0005C99260A907B9)},
         {UINT64_C(0x00094616BF27C9BA), UINT64_C(0x0003838C1FBFC98B),
          UINT64_C(0x0001AEFD5463CE2C), UINT64_C(0x0002298F2DAA2DC1),
          UINT64_C(0x00002F8398AA1EC1)}},
        {{UINT64_C(0x000B0B54E83EB3CB), UINT64_C(0x00046C11CF2C5D85),
          UINT64_C(0x000562C687A5BD26), UINT64_C(0x00008CB654C9579D),
          UINT64_C(0x00032A6EC6175F00)},
         {UINT64_C(0x0003D94A727B903F), UINT64_C(0x00033404D23AB3CE),
          UINT64_C(0x00047CFD855EC24D), UINT64_C(0x000499392AD55820),
          UINT64_C(0x000184705D6FA8F5)}},
        {{UINT64_C(0x000FC2C43A582997), UINT64_C(0x00009B163600D952),
          UINT64_C(0x0007D1FD9FDFC13A), UINT64_C(0x0006DDB423ABD0A7),
          UINT64_C(0x0006DA6449C5CF57)},
         {UINT64_C(0x000FFEB9DF5F3E5D), UINT64_C(0x000104479DCFA27F),
          UINT64_C(0x00021D3A7738DB2D), UINT64_C(0x0004045B51B58564),
          UINT64_C(0x0002B174ED65C2FD)}},
        {{UINT64_C(0x000BD918AF2D1DE7), UINT64_C(0x000797FE89E388BD),
          UINT64_C(0x000354F756AF6F61), UINT64_C(0x0002658B66C8F102),
          UINT64_C(0x000481C3A259A692)},
         {UINT64_C(0x00028B2886FDA782), UINT64_C(0x0001ACB2E8319AE1),
          UINT64_C(0x0005E65B509E18EA), UINT64_C(0x0000602680E0D288),
          UINT64_C(0x0001F8B6C2704BE0)}},
        {{UINT64_C(0x0004DE9DC251E4F5), UINT64_C(0x000398DC76312C40),
          UINT64_C(0x00060221101717D7), UINT64_C(0x00036E5F8512F680),
          UINT64_C(0x0006AECBBC4FAED1)},
         {UINT64_C(0x0006BCF81AD0F404), UINT64_C(0x00056B0D20017CFA),
          UINT64_C(0x000051B33CAE4F06), UINT64_C(0x00079BA25FD11B60),
          UINT64_C(0x0006C66B1B94906E)}},
        {{UINT64_C(0x000F2EB0A212B2F8), UINT64_C(0x0001291549D895DF),
          UINT64_C(0x0007EA3FDC6E23D8), UINT64_C(0x0003A5FB22527356),
          UINT64_C(0x00044827B3CCBCB7)},
         {UINT64_C(0x000569B9A0749338), UINT64_C(0x00017A1416DD273B),
          UINT64_C(0x0001E3D333FDA47A), UINT64_C(0x0000F6320D57AF46),
          UINT64_C(0x0001618E7F9E3803)}},
    },
    {
        {{UINT64_C(0x0006E8A84D02A6B0), UINT64_C(0x0002A5CE4DBC212A),
          UINT64_C(0x00009BCAB85DD325), UINT64_C(0x00033808F9E31F44),
          UINT64_C(0x0000711ABE8635FB)},
         {UINT64_C(0x00073EA3098D5496), UINT64_C(0x000696F79EEC27AE),
          UINT64_C(0x0007B6463961BEF1), UINT64_C(0x00021DAC69DE5F30),
          UINT64_C(0x00033F193BEA04AC)}},
        {{UINT64_C(0x0004886B6B478D87), UINT64_C(0x00026385B4323759),
          UINT64_C(0x00013A2BB416CDED), UINT64_C(0x000717B3988CF8DE),
          UINT64_C(0x000414C09B6B85C5)},
         {UINT64_C(0x0003390639FE52FF), UINT64_C(0x00006CA38DEF7FD7),
          UINT64_C(0x000431C5BCF60FD8), UINT64_C(0x000027BCB334EEFB),
          UINT64_C(0x00028319296C6513)}},
        {{UINT64_C(0x0004CB3C06E1AB70), UINT64_C(0x00057948510101D5),
          UINT64_C(0x0001C38A22A0693D), UINT64_C(0x00002914500B3EA3),
          UINT64_C(0x000578C8D8DD9DD3)},
         {UINT64_C(0x00016C80C1DBD2FF), UINT64_C(0x00033044253FB30D),
          UINT64_C(0x0007941DD6AECD7D), UINT64_C(0x0004F200061FECF3),
          UINT64_C(0x000546D982BEE1F4)}},
        {{UINT64_C(0x000EDBC39AE757A2), UINT64_C(0x00034FE0BAD67FE3),
          UINT64_C(0x0001971E008F362D), UINT64_C(0x0006C12B96F5D1A1),
          UINT64_C(0x0001FF6E4EB688F7)},
         {UINT64_C(0x00070C4B4E036FA0), UINT64_C(0x0000611BA94D0E52),
          UINT64_C(0x0007BFC428F0E251), UINT64_C(0x00042A78CA211CAC),
          UINT64_C(0x00078AAB5035F959)}},
        {{UINT64_C(0x00039F022DA3A297), UINT64_C(0x0001DA11C9EE4589),
          UINT64_C(0x000007BC8D152059), UINT64_C(0x0005CA88E041209D),
          UINT64_C(0x000396B7E030B90E)},
         {UINT64_C(0x0006A3AFDE54F823), UINT64_C(0x0002A38ABA40380D),
          UINT64_C(0x000394F10EC351B1), UINT64_C(0x0007BFA890FF374E),
          UINT64_C(0x0003ED6D3C141924)}},
        {{UINT64_C(0x0007B3B2AB41AFA6), UINT64_C(0x0005EDB0895870A2),
          UINT64_C(0x0004AE56B08A37D7), UINT64_C(0x0002E4BCFE71C4CA),
          UINT64_C(0x0002D43D757D8A3B)},
         {UINT64_C(0x00061C756483A39E), UINT64_C(0x0003AC62981046BD),
          UINT64_C(0x0001AFB8EAD77A64), UINT64_C(0x0002EA1E8FD302B3),
          UINT64_C(0x0004DB0FB226BB28)}},
        {{UINT64_C(0x000E7B1BDB64BA6C), UINT64_C(0x00054E041D4FE699),
          UINT64_C(0x0004AC1DEB24137D), UINT64_C(0x00074A0697964817),
          UINT64_C(0x000312BDC2D7A256)},
         {UINT64_C(0x000D14A56072B4CC), UINT64_C(0x0001382886183F9C),
          UINT64_C(0x000690EC6A92CA38), UINT64_C(0x0003B6BD7E019DD0),
          UINT64_C(0x00069DAC1921760B)}},
        {{UINT64_C(0x000CEDF14C9EEC7F), UINT64_C(0x0001AD446C451FAB),
          UINT64_C(0x0000A7B94D1D1824), UINT64_C(0x0003D360209777BD),
          UINT64_C(0x0003312FC6A60827)},
         {UINT64_C(0x0006925365A89852), UINT64_C(0x000532B69C2AC275),
          UINT64_C(0x00008D2BFA9C57DF), UINT64_C(0x0006B372357986F8),
          UINT64_C(0x0000BC6738808C01)}},
        {{UINT64_C(0x000BA8848894762E), UINT64_C(0x0007C52B109B74BA),
          UINT64_C(0x00055F874E82108D), UINT64_C(0x0006BC4B1AB5D607),
          UINT64_C(0x0004F79A87A0F4BC)},
         {UINT64_C(0x00000F22EC08DE0D), UINT64_C(0x00019BBD488532BC),
          UINT64_C(0x0001F7CC6A46142B), UINT64_C(0x00047951401B8854),
          UINT64_C(0x0000EEFD5F254172)}},
        {{UINT64_C(0x00085C1F80C878D8), UINT64_C(0x0006E57903F7CB79),
          UINT64_C(0x0007C43EE3AD7648), UINT64_C(0x000397051C967FD8),
          UINT64_C(0x000117C19679F176)},
         {UINT64_C(0x00002C5D30697DD3), UINT64_C(0x0000D3ECE67318BD),
          UINT64_C(0x0001F44996A833CC), UINT64_C(0x00015C569BF7DFFB),
          UINT64_C(0x0001E2F38CD9EB4E)}},
        {{UINT64_C(0x0001E36DB36EC447), UINT64_C(0x00014967CF4A822B),
          UINT64_C(0x00000401B78E38EB), UINT64_C(0x0005DAB9A907F31E),
          UINT64_C(0x0002DACF3F0F0C42)},
         {UINT64_C(0x000D33C908C6C4F7), UINT64_C(0x00066AA9CCCC7F70),
          UINT64_C(0x000060C3FCBC4FD7), UINT64_C(0x0001581BF7C89F6C),
          UINT64_C(0x000611050B8C54AC)}},
        {{UINT64_C(0x000D068F3D4C0908), UINT64_C(0x0001A02E029F619E),
          UINT64_C(0x0002B45FEC96D2D8), UINT64_C(0x0005A2871779F131),
          UINT64_C(0x00023F7AD0C44BA1)},
         {UINT64_C(0x0004D3B86980DEAF), UINT64_C(0x000711D8166CA500),
          UINT64_C(0x00030B3ED70C5899), UINT64_C(0x0001D50F687549AF),
          UINT64_C(0x00008B4B9B39707D)}},
        {{UINT64_C(0x000466DD9C839775), UINT64_C(0x00027B5FC46C6C6B),
          UINT64_C(0x000212095C5E5D6E), UINT64_C(0x0000A30F03686D71),
          UINT64_C(0x0006E778BB41401B)},
         {UINT64_C(0x000A1B7784E9C0BD), UINT64_C(0x00028B25CB77EDC9),
          UINT64_C(0x00075141ABC6098A), UINT64_C(0x00051285E7389DBD),
          UINT64_C(0x000786BB0ADC137B)}},
        {{UINT64_C(0x0002B5755D306294), UINT64_C(0x00025D99F2BAD0F6),
          UINT64_C(0x0006E9490307FE67), UINT64_C(0x00020FBAA40EBE93),
          UINT64_C(0x000621CE633E61AE)},
         {UINT64_C(0x000063301845BDB1), UINT64_C(0x000488A25D032E0B),
          UINT64_C(0x000245B64B6BCF17), UINT64_C(0x0005288C9BE6908E),
          UINT64_C(0x00039074BE491684)}},
        {{UINT64_C(0x0009D726AEAA4BC7), UINT64_C(0x00006D9CA435F290),
          UINT64_C(0x000399BB955171B4), UINT64_C(0x000707C3166A8265),
          UINT64_C(0x0002EB12AACC834B)},
         {UINT64_C(0x0002B20ADA6782ED), UINT64_C(0x0002F055CE8BB68D),
          UINT64_C(0x0007C0EB8CC8170C), UINT64_C(0x000082AD8CCDBB94),
          UINT64_C(0x00069541FF4BC978)}},
        {{UINT64_C(0x00065D167D224872), UINT64_C(0x0002581C711887F8),
          UINT64_C(0x00074BAF45B86A47), UINT64_C(0x00005E9E6ABE67A2),
          UINT64_C(0x0007213BD0895F3B)},
         {UINT64_C(0x00072AFCA61A0254), UINT64_C(0x00011742392A0ED5),
          UINT64_C(0x000010832281B6C9), UINT64_C(0x0005A0949297A0F6),
          UINT64_C(0x0000F164D77E14A3)}},
    },
    {
        {{UINT64_C(0x000ED5760B459019), UINT64_C(0x0002228AEA32C7DB),
          UINT64_C(0x000728A39BB925A7), UINT64_C(0x000203FA2F13E567),
          UINT64_C(0x00038462A9F1BA7C)},
         {UINT64_C(0x0004BD4EC1ABD7B1), UINT64_C(0x0000B5AE953DC16B),
          UINT64_C(0x00033E07D29EA8C5), UINT64_C(0x00054599995A7CB0),
          UINT64_C(0x000156E38D3AC76E)}},
        {{UINT64_C(0x00020D2F2D61CF6A), UINT64_C(0x000773029C680360),
          UINT64_C(0x0005052970E628E8), UINT64_C(0x0001743A89EE6C31),
          UINT64_C(0x000086CD11F0DC5D)},
         {UINT64_C(0x0005C5977FD6C080), UINT64_C(0x00027FE3AE450F63),
          UINT64_C(0x00021F4ED6717373), UINT64_C(0x00022A3B606C8A66),
          UINT64_C(0x00052EAC1F03F5E2)}},
        {{UINT64_C(0x000F339A2B5CE371), UINT64_C(0x00065E19D41D0666),
          UINT64_C(0x0007287E48FAD9EA), UINT64_C(0x000471066E33BE31),
          UINT64_C(0x000679F03495F090)},
         {UINT64_C(0x00074B03158B9D31), UINT64_C(0x0005EA499F4A04EB),
          UINT64_C(0x0005FEEB553C1808), UINT64_C(0x000397D697ED49B1),
          UINT64_C(0x0000B67F077DD63D)}},
        {{UINT64_C(0x0003C03AE2687F73), UINT64_C(0x00079CC5B29E02D3),
          UINT64_C(0x00072367F9DA54CC), UINT64_C(0x0007DF9A3AD9529A),
          UINT64_C(0x0001CFAFB9504592)},
         {UINT64_C(0x00068ACC89A8EE8B), UINT64_C(0x0004AFC769D61DA5),
          UINT64_C(0x0006F0E9603608BC), UINT64_C(0x0006792F936B1E02),
          UINT64_C(0x0000F1398ED3BF93)}},
        {{UINT64_C(0x000671CA92F0E827), UINT64_C(0x0006525CA7C69927),
          UINT64_C(0x000171200FBFEAFC), UINT64_C(0x0007372EE50A7A5F),
          UINT64_C(0x0006B675F2E394EC)},
         {UINT64_C(0x00007DAA7D3D0D62), UINT64_C(0x00016FD73FAB54B1),
          UINT64_C(0x00067F158C5CC197), UINT64_C(0x000283CEF99745BD),
          UINT64_C(0x0003348A74E2E770)}},
        {{UINT64_C(0x00066F8CDCF522E3), UINT64_C(0x0006982F90C04FE6),
          UINT64_C(0x0007F81A0CED981E), UINT64_C(0x00043551059F8155),
          UINT64_C(0x0001AFA647940834)},
         {UINT64_C(0x0007CED8C2304313), UINT64_C(0x0005AE6CE56CC0C8),
          UINT64_C(0x000137FF230DA040), UINT64_C(0x000568D65C179394),
          UINT64_C(0x000283CD25568788)}},
        {{UINT64_C(0x0002AAC2440B7F74), UINT64_C(0x0001F509834690FE),
          UINT64_C(0x00045D2233CE00BE), UINT64_C(0x000222595A3D079C),
          UINT64_C(0x00027C0FF58F688E)},
         {UINT64_C(0x0001D4B3F39E60B3), UINT64_C(0x0004CC3A253B5201),
          UINT64_C(0x0002333A105146C0), UINT64_C(0x00069062190DB8DD),
          UINT64_C(0x0005D0790291AD5A)}},
        {{UINT64_C(0x000F2D0FB16FC7F9), UINT64_C(0x000619F0C8B78C4A),
          UINT64_C(0x0005A8CCA7C6916F), UINT64_C(0x0001916D4D36752A),
          UINT64_C(0x0001023EF9FD86D6)},
         {UINT64_C(0x000168AAB3670612), UINT64_C(0x0005D8B15486E18B),
          UINT64_C(0x00063DD0999E8E18), UINT64_C(0x0006162366202858),
          UINT64_C(0x0001A4A2C4870979)}},
        {{UINT64_C(0x000CEFE4409994EE), UINT64_C(0x00055DA9B44A7A13),
          UINT64_C(0x000106DF8096A1CD), UINT64_C(0x00032D3E8584E2B6),
          UINT64_C(0x0005DD046ADEB104)},
         {UINT64_C(0x000B9B4FF6F4202A), UINT64_C(0x0004ACB4DBC87A7B),
          UINT64_C(0x0004A78645C47D53), UINT64_C(0x0007903BE0C0CBED),
          UINT64_C(0x0006FAA43697CCE6)}},
        {{UINT64_C(0x0005A494E4501DAE), UINT64_C(0x0000463C338F2697),
          UINT64_C(0x000412F90982A2D3), UINT64_C(0x0004A9D3F24715D7),
          UINT64_C(0x00072482CA04194D)},
         {UINT64_C(0x00035900B4AF93E8), UINT64_C(0x000492FB85261015),
          UINT64_C(0x0002523C6C2876BB), UINT64_C(0x0000406C75A05701),
          UINT64_C(0x0000BA73DF37C857)}},
        {{UINT64_C(0x000ACB525BB4074B), UINT64_C(0x00044377012C6937),
          UINT64_C(0x0000AC6E54E5EE1E), UINT64_C(0x00075073183EEEE0),
          UINT64_C(0x0000ED70C9270A43)},
         {UINT64_C(0x0001C2882CD5040F), UINT64_C(0x0006F51F4C4C0E11),
          UINT64_C(0x0005792A7C9CEA96), UINT64_C(0x0005AE0379BAEF75),
          UINT64_C(0x0003DEBB57FC40B0)}},
        {{UINT64_C(0x00014FFB15BBCF14), UINT64_C(0x00039E25E41EAB98),
          UINT64_C(0x0007FF955A848443), UINT64_C(0x00020BC48CD89492),
          UINT64_C(0x0007196CDE655F9B)},
         {UINT64_C(0x0007849F69377955), UINT64_C(0x0003C18A2D36539D),
          UINT64_C(0x00027E1E94033F9C), UINT64_C(0x0000DE30941EF01B),
          UINT64_C(0x00008B8E3FC11EC7)}},
        {{UINT64_C(0x000B06E3EEF0F157), UINT64_C(0x0004007F2ABB1B66),
          UINT64_C(0x0007DFEC2C450329), UINT64_C(0x00064D8A2197B0C0),
          UINT64_C(0x000190C79FC2CD88)},
         {UINT64_C(0x0006B06B6D6D97E1), UINT64_C(0x0001CA489187DECE),
          UINT64_C(0x0002C0A5779B1894), UINT64_C(0x0004B2CA6FE0E365),
          UINT64_C(0x00043EC85DD748E5)}},
        {{UINT64_C(0x00066D07404CC6B4), UINT64_C(0x0000A577C566F3FB),
          UINT64_C(0x0007AB67D3AD570A), UINT64_C(0x0001F1E9C9856550),
          UINT64_C(0x00003FA767C56FD6)},
         {UINT64_C(0x0002CA0442245C81), UINT64_C(0x0005F36D42201ED8),
          UINT64_C(0x000002F30D51F27E), UINT64_C(0x0004E5D7DA6C00D3),
          UINT64_C(0x000114C4C91AB28A)}},
        {{UINT64_C(0x000CEE008612CF1A), UINT64_C(0x0002CF14346EA4C4),
          UINT64_C(0x0006F8B9D96AE467), UINT64_C(0x0003F33F3E39A186),
          UINT64_C(0x0004B642581414E2)},
         {UINT64_C(0x000A079B869D7477), UINT64_C(0x00030DA90A235CA3),
          UINT64_C(0x0006AD614FDCB2C3), UINT64_C(0x0004852A604A706D),
          UINT64_C(0x0000157A3839DED1)}},
        {{UINT64_C(0x000B59E444C4EB0D), UINT64_C(0x00011676B4ECD851),
          UINT64_C(0x0003D31CB1B12DF9), UINT64_C(0x00027C08F496EEA1),
          UINT64_C(0x0000BE08415158FA)},
         {UINT64_C(0x000A04AEF0929EB4), UINT64_C(0x0002CE3EB2563B82),
          UINT64_C(0x00020C0E6F08D318), UINT64_C(0x0006285C2F629C66),
          UINT64_C(0x00021EDBA60C1ABB)}},
    },
    {
        {{UINT64_C(0x000FBF96E6D13256), UINT64_C(0x0000B68470D1AAB9),
          UINT64_C(0x0000BC465046AA9B), UINT64_C(0x0003D5203520917C),
          UINT64_C(0x0004B417116FA14F)},
         {UINT64_C(0x0009A3647E0FAC1A), UINT64_C(0x00071A016007859E),
          UINT64_C(0x0000A7B31060BC87), UINT64_C(0x000128F617FE1085),
          UINT64_C(0x00006EB4B51971C6)}},
        {{UINT64_C(0x00056FA126BE097D), UINT64_C(0x0000CA15EBCCDD85),
          UINT64_C(0x00049ED9E3DC0A72), UINT64_C(0x0000534A6F2D34FF),
          UINT64_C(0x000727D2633CD1B3)},
         {UINT64_C(0x000077AEFB30F457), UINT64_C(0x00024BD5A728DA2D),
          UINT64_C(0x0002C9AF355CE67C), UINT64_C(0x00073999222C6A81),
          UINT64_C(0x0005ABF888781842)}},
        {{UINT64_C(0x0007E2B53855E82E), UINT64_C(0x00036F75F5B38D7D),
          UINT64_C(0x0001FC3826F69222), UINT64_C(0x000551148FF627FA),
          UINT64_C(0x0000E21BFC642276)},
         {UINT64_C(0x000CD49503E4AE88), UINT64_C(0x00047C1EF9736822),
          UINT64_C(0x0002AEC417D9DE9E), UINT64_C(0x000564FF1CD1C7C3),
          UINT64_C(0x000102F631F03439)}},
        {{UINT64_C(0x000A9896A4813696), UINT64_C(0x0003523D6EEBE38D),
          UINT64_C(0x0001F9CAD687DDF8), UINT64_C(0x00071E17B9265FA2),
          UINT64_C(0x0003DC4E4A6526C5)},
         {UINT64_C(0x000380987DFF3364), UINT64_C(0x00031775ED80285C),
          UINT64_C(0x0003F94142B2664D), UINT64_C(0x00019B13FD7656C1),
          UINT64_C(0x0003839548BAC133)}},
        {{UINT64_C(0x0009059C15C5F8C7), UINT64_C(0x0005DCE55CE2EB32),
          UINT64_C(0x00064F44509419F0), UINT64_C(0x0005813AAEBDF343),
          UINT64_C(0x00066F8F9B0F508F)},
         {UINT64_C(0x0004D3AC3084DCF0), UINT64_C(0x00046DB6042359EF),
          UINT64_C(0x0000707D3EFD6F04), UINT64_C(0x00039BE123FBD3E7),
          UINT64_C(0x000374429C7AA556)}},
        {{UINT64_C(0x0009049F51AC9B9F), UINT64_C(0x00069F045B69DBA5),
          UINT64_C(0x0003599C0A66FE14), UINT64_C(0x0007E37C062CF77A),
          UINT64_C(0x0004EE7A09B3E7EA)},
         {UINT64_C(0x000BBEBF02917C14), UINT64_C(0x00000A62E0342107),
          UINT64_C(0x00018B2A672798B2), UINT64_C(0x0005D31A994A35E2),
          UINT64_C(0x00025FE7CA47E56C)}},
        {{UINT64_C(0x000C0FA78FD24FB4), UINT64_C(0x00014B8508F3AF59),
          UINT64_C(0x0001D75539AA97B4), UINT64_C(0x00030FD522CD0AED),
          UINT64_C(0x0001AEF85FCB95C2)},
         {UINT64_C(0x000CE2C59DBEBDCB), UINT64_C(0x0002F12AEBAFF60C),
          UINT64_C(0x0004DE71444F1374), UINT64_C(0x00053A44DD8A0024),
          UINT64_C(0x000043DEE537DC07)}},
        {{UINT64_C(0x000F9CB8CD409462), UINT64_C(0x00007A5EA9DCFC77),
          UINT64_C(0x00015B9C5672DDAC), UINT64_C(0x0002DEC06E0CD7B9),
          UINT64_C(0x0003829D7DBD8C4B)},
         {UINT64_C(0x000148A317174A3E), UINT64_C(0x0006C135D451348E),
          UINT64_C(0x000614DB1EF72449), UINT64_C(0x0005EA3039A699C9),
          UINT64_C(0x0005F393C9921CFD)}},
        {{UINT64_C(0x000BD8E78399A837), UINT64_C(0x000326AC044CB6BE),
          UINT64_C(0x000421B7FF19FDFB), UINT64_C(0x0004F2CE100BD03E),
          UINT64_C(0x00064D848DAF30F8)},
         {UINT64_C(0x0009B499ED1FE2E3), UINT64_C(0x0006841BA92C3FA2),
          UINT64_C(0x00061A84C7C8363B), UINT64_C(0x00014616602672A2),
          UINT64_C(0x00056FFF0F1EB104)}},
        {{UINT64_C(0x0006C40BEAC31BB5), UINT64_C(0x00018E17F67F79D1),
          UINT64_C(0x000226CDC803E440), UINT64_C(0x0001B07C6F9AA066),
          UINT64_C(0x0000F7F08F44D7E2)},
         {UINT64_C(0x000A2B8B71A878F2), UINT64_C(0x00005451E156E406),
          UINT64_C(0x0003F11116E5324A), UINT64_C(0x0007F48FABEB6CE3),
          UINT64_C(0x00041EA4652B5893)}},
        {{UINT64_C(0x00080127877EB48E), UINT64_C(0x0004F352E0372300),
          UINT64_C(0x0004B4EA89740CB2), UINT64_C(0x0002DA8620AA1FD0),
          UINT64_C(0x0003DD845928A4A7)},
         {UINT64_C(0x0003B8912897B9CF), UINT64_C(0x00024BFED91B1D58),
          UINT64_C(0x0002574ECC3E424D), UINT64_C(0x00037D04B7723902),
          UINT64_C(0x00006D7693B13AD1)}},
        {{UINT64_C(0x00059E9FDEA9C944), UINT64_C(0x000428DEC2AC2842),
          UINT64_C(0x000497AAFFA03EFC), UINT64_C(0x0005FB9332E47452),
          UINT64_C(0x00056C69D8A0C638)},
         {UINT64_C(0x0009A23A2C6E31BC), UINT64_C(0x00003ED95E52136D),
          UINT64_C(0x00034F2DF9A2277A), UINT64_C(0x0006E2B8948FCBBB),
          UINT64_C(0x0002E56F673B687E)}},
        {{UINT64_C(0x00011F6068178EE8), UINT64_C(0x000571D59E914ACF),
          UINT64_C(0x00053B116D7B9565), UINT64_C(0x0000819A7CA2F5C6),
          UINT64_C(0x00005D54B6D9C418)},
         {UINT64_C(0x000117E6E5F4423F), UINT64_C(0x0007BB7D377A5DF8),
          UINT64_C(0x00046F1DCCE6741F), UINT64_C(0x000624C8A21373BD),
          UINT64_C(0x00023E4D22A016DF)}},
        {{UINT64_C(0x0003C0EAC69D0DE5), UINT64_C(0x0006CF31DC831402),
          UINT64_C(0x00002BAD0A426ACA), UINT64_C(0x00007864FD5F0E1A),
          UINT64_C(0x00013C70B1A51E7D)},
         {UINT64_C(0x000E7A74CF7DE7B7), UINT64_C(0x00059198080C0E1F),
          UINT64_C(0x0002341CEA45F848), UINT64_C(0x000640334A0B0BD2),
          UINT64_C(0x00073CF34A927A18)}},
        {{UINT64_C(0x00078BB4A9FDF621), UINT64_C(0x000042F21FD366CA),
          UINT64_C(0x00039E15CF3E8543), UINT64_C(0x000371C9BC5AD61E),
          UINT64_C(0x0004B624E18568CB)},
         {UINT64_C(0x00078D62B4ADE692), UINT64_C(0x0004A079DD8E7902),
          UINT64_C(0x00029FA46937F35F), UINT64_C(0x0001C7744CC75169),
          UINT64_C(0x0007C882CBF3BBAA)}},
        {{UINT64_C(0x0005C23B09112390), UINT64_C(0x0001091F52EA9318),
          UINT64_C(0x00047D031BDBD618), UINT64_C(0x00042176E850AF62),
          UINT64_C(0x000255756EBA373D)},
         {UINT64_C(0x0008E197B7FDF0D5), UINT64_C(0x0001277E4F639650),
          UINT64_C(0x00002E5B807E9012), UINT64_C(0x0005C7BCAB505A9F),
          UINT64_C(0x0001A25FDD4D5CF9)}},
    },
    {
        {{UINT64_C(0x0005828E38661C25), UINT64_C(0x00014BC3733F569B),
          UINT64_C(0x0003412CB1C71958), UINT64_C(0x00004DC731462284),
          UINT64_C(0x0001245D94326762)},
         {UINT64_C(0x000EBD2384BE5F68), UINT64_C(0x000697E3904C2BC5),
          UINT64_C(0x0005D783FCBC6030), UINT64_C(0x0004CB401C37BE4C),
          UINT64_C(0x00011FFCCA4123CC)}},
        {{UINT64_C(0x00010495DBB211E9), UINT64_C(0x00025442F7CC4E5C),
          UINT64_C(0x000561DB7856A6B7), UINT64_C(0x00052325F162CCFC),
          UINT64_C(0x00054EF209D81AB5)},
         {UINT64_C(0x000C391A115200C4), UINT64_C(0x000138919D37F23E),
          UINT64_C(0x00038909464B887E), UINT64_C(0x0005167B307E5774),
          UINT64_C(0x00032242323F9935)}},
        {{UINT64_C(0x0007ED46EC46A583), UINT64_C(0x000392288DCC4F7A),
          UINT64_C(0x0007FD2EB94B33B0), UINT64_C(0x000432CF9FE955B4),
          UINT64_C(0x0002450E4D8A038A)},
         {UINT64_C(0x0007AFF478477329), UINT64_C(0x0004D48903D3CD08),
          UINT64_C(0x000708276A425531), UINT64_C(0x000606FD71DB1D37),
          UINT64_C(0x000142BEBD75DEB9)}},
        {{UINT64_C(0x000B1FA45676391E), UINT64_C(0x00069692EEDD270E),
          UINT64_C(0x000498670EBF7399), UINT64_C(0x00045C7632B7CFC2),
          UINT64_C(0x00019A09AE2A79FB)},
         {UINT64_C(0x0002CA58A8153BB1), UINT64_C(0x00031337FF678477),
          UINT64_C(0x000418997426A417), UINT64_C(0x000655A7996C5020),
          UINT64_C(0x0001936EB94D8E93)}},
        {{UINT64_C(0x0009A2630289A595), UINT64_C(0x0000CA8CFCDBA537),
          UINT64_C(0x00056FF9F6E6FE48), UINT64_C(0x0005B53BE7E2FC07),
          UINT64_C(0x0000C8AA9E431C96)},
         {UINT64_C(0x0008F62B7EC7F8B4), UINT64_C(0x000759CD5D73D9A2),
          UINT64_C(0x00015AB8E3A25009), UINT64_C(0x0004CC8936067C91),
          UINT64_C(0x00054FC0E3CFD648)}},
        {{UINT64_C(0x0006FA9F5E920FA0), UINT64_C(0x0000B770A49D3EEC),
          UINT64_C(0x000582B079BCD64F), UINT64_C(0x0001C35560408E28),
          UINT64_C(0x000044A7950A6FFB)},
         {UINT64_C(0x000B3A87FED1B513), UINT64_C(0x00067CE537DA0A6A),
          UINT64_C(0x000707594DB6F3AF), UINT64_C(0x00057EC75ED66C23),
          UINT64_C(0x00069E23F2649E30)}},
        {{UINT64_C(0x000AA072D6126519), UINT64_C(0x0001BE00610679A4),
          UINT64_C(0x0006C412907F1A7D), UINT64_C(0x000163773E6AAA30),
          UINT64_C(0x0003BC5F3E291E74)},
         {UINT64_C(0x000809166205E3BB), UINT64_C(0x000785499CE64078),
          UINT64_C(0x0000E3D8FC7F29B8), UINT64_C(0x0007E0EF22E29934),
          UINT64_C(0x00030E9A8D252A88)}},
        {{UINT64_C(0x0003A829DCC73EE8), UINT64_C(0x000699D7B9BF4EAD),
          UINT64_C(0x0007D4B17CC3C7C2), UINT64_C(0x0002905BEE0EE901),
          UINT64_C(0x000547FF8275C891)},
         {UINT64_C(0x00052B2910F7F25A), UINT64_C(0x00068F778D2E5A27),
          UINT64_C(0x0001B85563815942), UINT64_C(0x00013DF08225311A),
          UINT64_C(0x0007609FDFF08594)}},
        {{UINT64_C(0x000B2E2EC305F2D5), UINT64_C(0x0000996BBC6E4FA9),
          UINT64_C(0x00030B4CB4168F67), UINT64_C(0x0007426FB602045D),
          UINT64_C(0x0000943C5ADFF99F)},
         {UINT64_C(0x000E48A53EA42C24), UINT64_C(0x00057678B9589CBD),
          UINT64_C(0x00052006DCF8D83F), UINT64_C(0x00053D7E245AD4FD),
          UINT64_C(0x00077A9E12D48B9D)}},
        {{UINT64_C(0x0007CAC10865F9CF), UINT64_C(0x000578B7D35B3A19),
          UINT64_C(0x000708EEEBB1E6E7), UINT64_C(0x0002ABFEA46C5555),
          UINT64_C(0x0004CA5DC5AE086E)},
         {UINT64_C(0x000A36376B7A93D2), UINT64_C(0x000188095575D2D0),
          UINT64_C(0x0000393EF6F8634C), UINT64_C(0x0007C039388938C2),
          UINT64_C(0x00031010E90AC0B1)}},
        {{UINT64_C(0x0007DF172B9EB768), UINT64_C(0x000178D003BB4489),
          UINT64_C(0x000783A956E76079), UINT64_C(0x00025ACCD6C14EC3),
          UINT64_C(0x00046178A9DC6279)},
         {UINT64_C(0x00015D4ECB7BD0F4), UINT64_C(0x0006C0DC296E2C56),
          UINT64_C(0x00053C51B36422FB), UINT64_C(0x000549E0F4E67DE5),
          UINT64_C(0x000389CCC23186D0)}},
        {{UINT64_C(0x000C532F03C27F78), UINT64_C(0x00014A6C28ABF4C3),
          UINT64_C(0x0003F90A141B9A5B), UINT64_C(0x00069B2DDF5CD3B9),
          UINT64_C(0x0002CA851CF01E00)},
         {UINT64_C(0x00059440324910F2), UINT64_C(0x000729FD333620F7),
          UINT64_C(0x00003CD70BDC8C7B), UINT64_C(0x00040C73BD640FDD),
          UINT64_C(0x0004332FAA2FFBA1)}},
        {{UINT64_C(0x0006664ABFD5681D), UINT64_C(0x000074E8C029237E),
          UINT64_C(0x0006201249B2B7BD), UINT64_C(0x000529E03D66B256),
          UINT64_C(0x000107D890CE2D92)},
         {UINT64_C(0x000F2B9FEE1231EC), UINT64_C(0x00037A3DA4060374),
          UINT64_C(0x0000D270F3481DAF), UINT64_C(0x000748DD10EC51C1),
          UINT64_C(0x000313AA5B16B013)}},
        {{UINT64_C(0x000DF0E13A34CBFC), UINT64_C(0x0003631767D8B18E),
          UINT64_C(0x00006B84D66D0E80), UINT64_C(0x0003C82463233CE9),
          UINT64_C(0x00000EC0876ED459)},
         {UINT64_C(0x000BFA9BFE16F36E), UINT64_C(0x0002B16C346CBE77),
          UINT64_C(0x0001F03779578316), UINT64_C(0x0005B6D0ECC3F5EA),
          UINT64_C(0x000392C5DC949B1E)}},
        {{UINT64_C(0x0003CA709674F552), UINT64_C(0x0004286A064869F6),
          UINT64_C(0x0000E2BB1ED6DF2A), UINT64_C(0x0004B163E6232515),
          UINT64_C(0x0007FC5BBC23EE21)},
         {UINT64_C(0x00048D96D1562488), UINT64_C(0x0004D9D561FE91F5),
          UINT64_C(0x0005807F49D14F0C), UINT64_C(0x0000A483741FDC4B),
          UINT64_C(0x0002562EC3A9714B)}},
        {{UINT64_C(0x00077D2D04C4F8B3), UINT64_C(0x0003A4D092998582),
          UINT64_C(0x00036F34A3156092), UINT64_C(0x00036043316B80D2),
          UINT64_C(0x000387899C0D0389)},
         {UINT64_C(0x0001D232B890C1FF), UINT64_C(0x0002BB8E2A3A7B1E),
          UINT64_C(0x000674044C311F30), UINT64_C(0x00042AABC2D14B2F),
          UINT64_C(0x0005FA4E41857301)}},
    },
    {
        {{UINT64_C(0x000B8C87FBE21CA1), UINT64_C(0x0004654DD4516E9E),
          UINT64_C(0x0004D4C04111E84C), UINT64_C(0x00052F54D6883A97),
          UINT64_C(0x00058FBFACC04077)},
         {UINT64_C(0x0008B1B2BD6C47E6), UINT64_C(0x000406C2B52FDDD1),
          UINT64_C(0x0005CD0AC07AFC39), UINT64_C(0x00025702B023D878),
          UINT64_C(0x0000457D9E01D39B)}},
        {{UINT64_C(0x000357AC0E5357E1), UINT64_C(0x000603BB8F446D94),
          UINT64_C(0x000526C39B423BF6), UINT64_C(0x000652EC95741E38),
          UINT64_C(0x0003AE110F98F0B7)},
         {UINT64_C(0x00062637EE945699), UINT64_C(0x00049EF673613CC9),
          UINT64_C(0x00016CE0E4F9387C), UINT64_C(0x000651CEC355D9BE),
          UINT64_C(0x000268F9AB67FA18)}},
        {{UINT64_C(0x00093F3302F7A85C), UINT64_C(0x00045C7103A537C2),
          UINT64_C(0x000019C168EFCF12), UINT64_C(0x0000FCE8AC30255C),
          UINT64_C(0x00007B9C2CD636AF)},
         {UINT64_C(0x000C7C15BA124E6A), UINT64_C(0x0006096943B520B1),
          UINT64_C(0x00062EFD361E552A), UINT64_C(0x0001B396CD943BA3),
          UINT64_C(0x000246C5ECE49CB6)}},
        {{UINT64_C(0x000C8B07844D0432), UINT64_C(0x000193AA51F64178),
          UINT64_C(0x000652032368D613), UINT64_C(0x0000584636777EBD),
          UINT64_C(0x00076EBD45AE24F8)},
         {UINT64_C(0x00083A8832371D4D), UINT64_C(0x00031C19F58C6C5D),
          UINT64_C(0x0006B31CAFC81A4C), UINT64_C(0x0000F4FB72B9C793),
          UINT64_C(0x00051077AABFF976)}},
        {{UINT64_C(0x000AFF8E199C7745), UINT64_C(0x00000B6C8E8C41D7),
          UINT64_C(0x0000E037DBA37A3B), UINT64_C(0x0005BFBB2E8C259E),
          UINT64_C(0x0003066835FD1B75)},
         {UINT64_C(0x000612167989FFA4), UINT64_C(0x0007902FB713C890),
          UINT64_C(0x0000A6D25B2214DB), UINT64_C(0x00060B1391B6BBD8),
          UINT64_C(0x0004E350B3BCA4E4)}},
        {{UINT64_C(0x000F3F02AF47CC2D), UINT64_C(0x00060388DA43D42C),
          UINT64_C(0x000632F6353FB249), UINT64_C(0x0005F31E07849F64),
          UINT64_C(0x0005CE122608D9A5)},
         {UINT64_C(0x0006007E5350D389), UINT64_C(0x0007742ACF4259FE),
          UINT64_C(0x000701EBC914081D), UINT64_C(0x000531138485C0D1),
          UINT64_C(0x00076919B7CB5969)}},
        {{UINT64_C(0x000EC0DD3F0BB75C), UINT64_C(0x000517421A5944B2),
          UINT64_C(0x00064E3D5BDAF79C), UINT64_C(0x00077DAC3841BA0D),
          UINT64_C(0x000274CFC101EC2D)},
         {UINT64_C(0x0008CD42DD1C1CFE), UINT64_C(0x0000572F81FDFD0F),
          UINT64_C(0x0007BBC498B713BC), UINT64_C(0x00034B4691EB371D),
          UINT64_C(0x00064FF50BFB9339)}},
        {{UINT64_C(0x000A74289BD66C07), UINT64_C(0x00041AE49E4EF440),
          UINT64_C(0x00016BD641D76BFB), UINT64_C(0x0006217734484D42),
          UINT64_C(0x0007801CB15FA7FF)},
         {UINT64_C(0x0006A7999951B22E), UINT64_C(0x00073B7132140BC1),
          UINT64_C(0x0001CB156A92521C), UINT64_C(0x00047DAE07D429C2),
          UINT64_C(0x00034176AE2F6DD1)}},
        {{UINT64_C(0x000F38AE8B585B7C), UINT64_C(0x0007D0F5E5336093),
          UINT64_C(0x000634B7780008CA), UINT64_C(0x0006CBB99339E01D),
          UINT64_C(0x000048FD2054F121)},
         {UINT64_C(0x000DA75057482FC7), UINT64_C(0x000659E2C0AC4ADB),
          UINT64_C(0x0002839CEE7F858A), UINT64_C(0x0005F0AC5233F317),
          UINT64_C(0x0007DB0CE3A922D5)}},
        {{UINT64_C(0x00026BED2D1D7FC7), UINT64_C(0x000040FD51645942),
          UINT64_C(0x0001C6893567FE50), UINT64_C(0x0007BEAA7D726BFC),
          UINT64_C(0x0004207CB6F8125B)},
         {UINT64_C(0x000C744046E92E4A), UINT64_C(0x0004889DCCE9C25F),
          UINT64_C(0x000298D4EBD978FD), UINT64_C(0x000538378F3B449E),
          UINT64_C(0x000103ECF3526132)}},
        {{UINT64_C(0x00070E6E240ACD3F), UINT64_C(0x0002D0E910C2C5C5),
          UINT64_C(0x00047C4B7A150503), UINT64_C(0x0007E03610CFC713),
          UINT64_C(0x000012B1D48802DF)},
         {UINT64_C(0x0005F52F4DB5F186), UINT64_C(0x000214FE444DAC5B),
          UINT64_C(0x00040B88A70B4E77), UINT64_C(0x00028DB30B535336),
          UINT64_C(0x000269074FCBDB43)}},
        {{UINT64_C(0x0007D8A7FAA53FDE), UINT64_C(0x00073B94B3141959),
          UINT64_C(0x000479FD2A22E7BB), UINT64_C(0x00023433E0E79ABC),
          UINT64_C(0x0007443BEA34A6F6)},
         {UINT64_C(0x000FEF139C8576CC), UINT64_C(0x00050FBF061F5893),
          UINT64_C(0x0004558E31BB1220), UINT64_C(0x000315C237E0227E),
          UINT64_C(0x0000B5335F077DD7)}},
        {{UINT64_C(0x00045CB3299CB9CB), UINT64_C(0x0006C0467C3B394D),
          UINT64_C(0x0004E14A26B6171B), UINT64_C(0x00038CC5A50E5AA7),
          UINT64_C(0x00005EDE90305A8E)},
         {UINT64_C(0x0007D811A19C3957), UINT64_C(0x0004D84D2FDD61E8),
          UINT64_C(0x0003F360AB0C2427), UINT64_C(0x0006D66D2AB4A909),
          UINT64_C(0x0007376DB64F64BE)}},
        {{UINT64_C(0x00051348CBF420A8), UINT64_C(0x0003D930ED834A4F),
          UINT64_C(0x000189403BF27676), UINT64_C(0x000181B04E39E3D0),
          UINT64_C(0x00005C72602F7C87)},
         {UINT64_C(0x000FF27D92F3F8A6), UINT64_C(0x00057F1C3BE718B0),
          UINT64_C(0x0007DB5FE00D8AC2), UINT64_C(0x0006508234B8F600),
          UINT64_C(0x00068F5AC550C57D)}},
        {{UINT64_C(0x000B437344372511), UINT64_C(0x0002CC307D9DF3CA),
          UINT64_C(0x000065755E585F5C), UINT64_C(0x0004484FE311448A),
          UINT64_C(0x00045D0A928F957F)},
         {UINT64_C(0x0009DC33DF29CABF), UINT64_C(0x00058AABC130136B),
          UINT64_C(0x0006DD896642EBED), UINT64_C(0x0003DD5E6A93CDB6),
          UINT64_C(0x00058BBD544A9587)}},
        {{UINT64_C(0x00023F2F46B77A44), UINT64_C(0x00022E0E5444326D),
          UINT64_C(0x0000C682DDCB6C24), UINT64_C(0x000174710E87D9FD),
          UINT64_C(0x00062440C79CB254)},
         {UINT64_C(0x000C0B15406C928C), UINT64_C(0x0004D56FB1459CB2),
          UINT64_C(0x00012D1E88446D8A), UINT64_C(0x0000D1CDDEF21AA8),
          UINT64_C(0x0002C8A130076C5D)}},
    },
    {
        {{UINT64_C(0x000A20A5A11AEE99), UINT64_C(0x000658F7C4C596BF),
          UINT64_C(0x00031596DD0E0F0C), UINT64_C(0x0007FC75CBADE15C),
          UINT64_C(0x00024BB69877DB99)},
         {UINT64_C(0x000F47EE95A31E2E), UINT64_C(0x0006090BCAD2326F),
          UINT64_C(0x0003F84B5AAC8FE6), UINT64_C(0x00028823088CE92F),
          UINT64_C(0x0002532BF86570CF)}},
        {{UINT64_C(0x0005E2861D5F12F9), UINT64_C(0x000537E8118D77D5),
          UINT64_C(0x000631B8799698AE), UINT64_C(0x0003CC1412721F76),
          UINT64_C(0x000624DB7CBC35F6)},
         {UINT64_C(0x0007E4CF1383E30F), UINT64_C(0x0000ACF29C3392AB),
          UINT64_C(0x00027424E45CBECE), UINT64_C(0x000531D9F8ABB684),
          UINT64_C(0x00076C6093C294A1)}},
        {{UINT64_C(0x000536AC6395662A), UINT64_C(0x000239188AAD2807),
          UINT64_C(0x00031EADFE0E7BEB), UINT64_C(0x00036455A85285DD),
          UINT64_C(0x0000DFA70C45530F)},
         {UINT64_C(0x000AA7B94BEC12BF), UINT64_C(0x000127AF29368714),
          UINT64_C(0x0005B0B1FF2913B2), UINT64_C(0x0005B474E2A2BCDF),
          UINT64_C(0x0005B2B977212BD9)}},
        {{UINT64_C(0x000FFD50E811BC89), UINT64_C(0x0003FA8AA1288DF8),
          UINT64_C(0x0004CE42359D496A), UINT64_C(0x0005AD22EC8B66B0),
          UINT64_C(0x00035AD636E886A1)},
         {UINT64_C(0x00036AEC1F03E773), UINT64_C(0x0004DB5A8BCA9BB7),
          UINT64_C(0x000378BD3B9E00D0), UINT64_C(0x0002F9A1B2588A18),
          UINT64_C(0x00059374C4B84DCE)}},
        {{UINT64_C(0x000A5C78C0A09501), UINT64_C(0x000438418122FEF3),
          UINT64_C(0x00036F391A493F51), UINT64_C(0x0001C1581DA10D11),
          UINT64_C(0x0004D380EFB19759)},
         {UINT64_C(0x0007AE14448E2F04), UINT64_C(0x0003E5E01CA7FD7A),
          UINT64_C(0x00014C4162795998), UINT64_C(0x0007BBA0161739BC),
          UINT64_C(0x0001CC54400F1EC4)}},
        {{UINT64_C(0x000B4104835823A8), UINT64_C(0x000049050C8FF31A),
          UINT64_C(0x00078137F3671127), UINT64_C(0x000291A0D2FD7BCD),
          UINT64_C(0x0000C5045563A528)},
         {UINT64_C(0x000216E06610C3FE), UINT64_C(0x0006D4EB6BA949C6),
          UINT64_C(0x0005980545DD7A0A), UINT64_C(0x0003C47D2617FCCA),
          UINT64_C(0x0004E4B15E594DAD)}},
        {{UINT64_C(0x000F8BABBDF00F70), UINT64_C(0x00063DF37D0B03B9),
          UINT64_C(0x0003D691146AF8DE), UINT64_C(0x00002891C5C93DC5),
          UINT64_C(0x0001CD90615B01A9)},
         {UINT64_C(0x000D5D595EBCBD12), UINT64_C(0x000179C70C411F6B),
          UINT64_C(0x000470395C25F3BB), UINT64_C(0x00005F2DBD08ADC8),
          UINT64_C(0x000177CA728981CA)}},
        {{UINT64_C(0x0000E456B1F71055), UINT64_C(0x0007362E63D73807),
          UINT64_C(0x00040975D13C6956), UINT64_C(0x00077C8546A0DDEC),
          UINT64_C(0x000255C05FCBFFB8)},
         {UINT64_C(0x0009D08BF388FA6C), UINT64_C(0x0005479123177C4E),
          UINT64_C(0x0006EF2CA17DD9CC), UINT64_C(0x0000A28B766E8543),
          UINT64_C(0x000725EF11C998DB)}},
        {{UINT64_C(0x000AFD776D46AE2B), UINT64_C(0x0003A2987F39DE71),
          UINT64_C(0x00004318A4AD86B8), UINT64_C(0x00029CB17C4E8BA2),
          UINT64_C(0x00068C1611368562)},
         {UINT64_C(0x00072BB4043C69E3), UINT64_C(0x000355B0D908C97C),
          UINT64_C(0x0001D84483454BEA), UINT64_C(0x0001086B129DA054),
          UINT64_C(0x00077E8521E8B03C)}},
        {{UINT64_C(0x000483BAE3E96C40), UINT64_C(0x0001033C465DCD03),
          UINT64_C(0x000144124D4C7602), UINT64_C(0x0000F90A0A3F2373),
          UINT64_C(0x00021D2DDA9344D1)},
         {UINT64_C(0x000BCC16AC03E55C), UINT64_C(0x0007907B7596D0A7),
          UINT64_C(0x0001FA7957776F16), UINT64_C(0x000330182BCE6E91),
          UINT64_C(0x00044B0FCEC99956)}},
        {{UINT64_C(0x00057F6C0E0F3BFB), UINT64_C(0x000162B3C9264F2C),
          UINT64_C(0x00041C4E863CD2BE), UINT64_C(0x00044D72FE16345A),
          UINT64_C(0x000666FAA55BBB69)},
         {UINT64_C(0x000E5AD6ED951F40), UINT64_C(0x000420A55FD6D09E),
          UINT64_C(0x00045DEBB67E01E4), UINT64_C(0x00057700CDA6B2E2),
          UINT64_C(0x0002385BC90BA9CD)}},
        {{UINT64_C(0x000D7FBDBC6992BF), UINT64_C(0x000326A5E14A7861),
          UINT64_C(0x000542159B191B29), UINT64_C(0x00078DF87B779043),
          UINT64_C(0x0007D91072D769FC)},
         {UINT64_C(0x000F81B25D9A6F04), UINT64_C(0x0004F3DE069B4E88),
          UINT64_C(0x0003D1D943A8CBC1), UINT64_C(0x00003F1FE40D629B),
          UINT64_C(0x0003EC7B27C110AE)}},
        {{UINT64_C(0x000A3FB03B6CC730), UINT64_C(0x00023C7A1AE1D095),
          UINT64_C(0x0004D884FC7D66EE), UINT64_C(0x0006892EDA82276A),
          UINT64_C(0x0007E14DAC6F262A)},
         {UINT64_C(0x000078183982428A), UINT64_C(0x0000F555B51769F7),
          UINT64_C(0x0002C306A05F265D), UINT64_C(0x0007359935B2E81E),
          UINT64_C(0x00017E43ECD283C0)}},
        {{UINT64_C(0x00003A82E91E17BF), UINT64_C(0x0002819AB5CB8AC3),
          UINT64_C(0x0004CCB84250F825), UINT64_C(0x0005B7B331D46BB5),
          UINT64_C(0x0001D3BA6D0D999A)},
         {UINT64_C(0x000FED56D79EE4F6), UINT64_C(0x000159B4CD69EF9F),
          UINT64_C(0x0001BFFE18B5C9B0), UINT64_C(0x0004736AD2A4D805),
          UINT64_C(0x00034F5F044BA570)}},
        {{UINT64_C(0x0001343287CDEBA0), UINT64_C(0x00013D882BADAB71),
          UINT64_C(0x0005CAD7B607FCC4), UINT64_C(0x00023E01BF809A99),
          UINT64_C(0x0003312CA1620AC9)},
         {UINT64_C(0x000282755193459D), UINT64_C(0x0004995E4E82E3E5),
          UINT64_C(0x0000CBCB85F02225), UINT64_C(0x0000D409BC19F795),
          UINT64_C(0x00002E6EDB143CE9)}},
        {{UINT64_C(0x0004F31CB2D9BCCB), UINT64_C(0x000628A63902A713),
          UINT64_C(0x0004F007BD385C5E), UINT64_C(0x0001F74B1F93394D),
          UINT64_C(0x0003086A647FCB5C)},
         {UINT64_C(0x000398B0975F65D4), UINT64_C(0x0004AA9FDC040688),
          UINT64_C(0x000332D91719B6C9), UINT64_C(0x00040BC443E8E4C7),
          UINT64_C(0x00020AA4BACC6B0F)}},
    },
    {
        {{UINT64_C(0x00082C1376036494), UINT64_C(0x000381809A5DC13F),
          UINT64_C(0x0007A768F09F3FDD), UINT64_C(0x000024F9AD059DDF),
          UINT64_C(0x000434FAA3001A00)},
         {UINT64_C(0x000F6C7E5397CE18), UINT64_C(0x000473965DA5203F),
          UINT64_C(0x000022D664316484), UINT64_C(0x00017D08DDE3B42A),
          UINT64_C(0x000032E15813D1D4)}},
        {{UINT64_C(0x000A24C7658BA0CB), UINT64_C(0x0004DAFAE0752E4D),
          UINT64_C(0x00057FEE62416FAC), UINT64_C(0x0007CE89A50A1C66),
          UINT64_C(0x0005B3A87DDA42C5)},
         {UINT64_C(0x00015A6DCD0D9750), UINT64_C(0x0000DC64FF4D1D60),
          UINT64_C(0x00062BE62D48588D), UINT64_C(0x00044CED6F7DC105),
          UINT64_C(0x00051FD1713A7272)}},
        {{UINT64_C(0x00031C0A4FBA8CD3), UINT64_C(0x00004D85D27BFFBD),
          UINT64_C(0x00073687DDD1897B), UINT64_C(0x0003C18CABEB4462),
          UINT64_C(0x000252009593038E)},
         {UINT64_C(0x0006A4B0F43B1F03), UINT64_C(0x0003596CEF096CE4),
          UINT64_C(0x000370A6BA70C9EC), UINT64_C(0x000504FA8F22F8D9),
          UINT64_C(0x00042EF23D58A32E)}},
        {{UINT64_C(0x00018C743658227A), UINT64_C(0x00059EB06D4851E7),
          UINT64_C(0x000765C0B448C135), UINT64_C(0x0002B1CDBE044E7F),
          UINT64_C(0x0003BDF03C33FC4C)},
         {UINT64_C(0x0000B8DD5FB4A505), UINT64_C(0x00042CD448FC94EE),
          UINT64_C(0x00041528FB2A0C40), UINT64_C(0x0006016CCFD12E3D),
          UINT64_C(0x00050D6616BCB42A)}},
        {{UINT64_C(0x00040B651D6D68FC), UINT64_C(0x0004F96B3665F295),
          UINT64_C(0x00032453022450B6), UINT64_C(0x00062498B1682588),
          UINT64_C(0x0000FAB72B2FCCDF)},
         {UINT64_C(0x000274624DFAF9A7), UINT64_C(0x0004DA952FBAD71D),
          UINT64_C(0x0000C06372160275), UINT64_C(0x00065F4951C1680C),
          UINT64_C(0x00020B63127A4A88)}},
        {{UINT64_C(0x000050A3603E9997), UINT64_C(0x0003A6ADCDF25D68),
          UINT64_C(0x0001ADDCEB37FF27), UINT64_C(0x0004AE1B473A39F1),
          UINT64_C(0x00008E1D46706170)},
         {UINT64_C(0x000BDEA888583D12), UINT64_C(0x00058B06E62135F6),
          UINT64_C(0x0006B07EE1CF9CF1), UINT64_C(0x0007166BD7374570),
          UINT64_C(0x0001B932B291A438)}},
        {{UINT64_C(0x0005AF929B2B37C3), UINT64_C(0x0007549202796660),
          UINT64_C(0x000513E2660DE7FC), UINT64_C(0x00006B6381885392),
          UINT64_C(0x000331EBF4C330BC)},
         {UINT64_C(0x0005604DFD132A81), UINT64_C(0x00018FFB2CF41F7A),
          UINT64_C(0x0005BE1FFDB10454), UINT64_C(0x000789C57A8EB47D),
          UINT64_C(0x0003C7003C3CC193)}},
        {{UINT64_C(0x000FF74CFBD82392), UINT64_C(0x000381F44F448A49),
          UINT64_C(0x00037DADAB5742D4), UINT64_C(0x0003385CD8013C8C),
          UINT64_C(0x00077C42B4529B16)},
         {UINT64_C(0x0009C56F0944379A), UINT64_C(0x000383BFA2B1C2B2),
          UINT64_C(0x0003E4ADF6856612), UINT64_C(0x0004D8EDC69A18AE),
          UINT64_C(0x000782A2F6102155)}},
        {{UINT64_C(0x000C8496202AE6A8), UINT64_C(0x000433B75E48A25B),
          UINT64_C(0x0005141D4B503E98), UINT64_C(0x0005226A88155633),
          UINT64_C(0x000022CA7649B49E)},
         {UINT64_C(0x000DDE29F41E7545), UINT64_C(0x00051980467DCE98),
          UINT64_C(0x0005452CD2F2E90B), UINT64_C(0x00044681E5A6B9BC),
          UINT64_C(0x0000B18699C70658)}},
        {{UINT64_C(0x000454DCDC35F98E), UINT64_C(0x0004D3A9A9B829BD),
          UINT64_C(0x0005C82A6F53D23D), UINT64_C(0x00054F2336A35986),
          UINT64_C(0x0004F050EAA88BC5)},
         {UINT64_C(0x00091AE73A0DC4CB), UINT64_C(0x0006739166A25EC5),
          UINT64_C(0x0000F8DCFD062057), UINT64_C(0x000680C6E9553F2E),
          UINT64_C(0x00016C70FF5900AB)}},
        {{UINT64_C(0x000C54EBAD62EAB1), UINT64_C(0x0006A7C01E68F92A),
          UINT64_C(0x0004C8F6FC7D7E91), UINT64_C(0x00071F7B41EA0338),
          UINT64_C(0x000441A714BEC729)},
         {UINT64_C(0x0004B4DAD4F8A724), UINT64_C(0x00033ED22AE26116),
          UINT64_C(0x00079F365BF781AB), UINT64_C(0x00002CA67E4CAEC5),
          UINT64_C(0x00055A4A1A0081CE)}},
        {{UINT64_C(0x000856B7CDEDE34B), UINT64_C(0x00046FA048E577A5),
          UINT64_C(0x00038378B91B299E), UINT64_C(0x0004C31EAC28396A),
          UINT64_C(0x0001440CEEBF654F)},
         {UINT64_C(0x000DE3FD4ACED2B3), UINT64_C(0x0007A22FF45E43F3),
          UINT64_C(0x00007CC601E3B097), UINT64_C(0x0002A04F943FBC69),
          UINT64_C(0x0005C5F64D0D7533)}},
        {{UINT64_C(0x000DBE80786B794B), UINT64_C(0x00034C0F990B92EF),
          UINT64_C(0x000330FBBBF05942), UINT64_C(0x00027E782DC51868),
          UINT64_C(0x000287A147D07324)},
         {UINT64_C(0x000213F588BDAF78), UINT64_C(0x0000435D0191F486),
          UINT64_C(0x0006F097D291A15C), UINT64_C(0x000339AFB6E98B50),
          UINT64_C(0x00055B5A4EA63E71)}},
        {{UINT64_C(0x00012AEEC81A34E7), UINT64_C(0x0003DBAAF67991EA),
          UINT64_C(0x000737806F56C38C), UINT64_C(0x00043D356C504230),
          UINT64_C(0x000311DCD3F757D5)},
         {UINT64_C(0x00073E6854BEEAE4), UINT64_C(0x0005FB95F72D5723),
          UINT64_C(0x000411187F7CE78E), UINT64_C(0x00077E7ED750A498),
          UINT64_C(0x0005993AA33720F3)}},
        {{UINT64_C(0x00028A5A432F3F67), UINT64_C(0x00075D0A5CAB6768),
          UINT64_C(0x0006396B9CAB93D8), UINT64_C(0x000546258D80855A),
          UINT64_C(0x0003C09404670F46)},
         {UINT64_C(0x00000D1A71F0C8DC), UINT64_C(0x0001CA62AD6C1C14),
          UINT64_C(0x000494E0150B4553), UINT64_C(0x0000B039392E5564),
          UINT64_C(0x0002EF65688DCB46)}},
        {{UINT64_C(0x00082CA0145F254D), UINT64_C(0x0001317C70253A1C),
          UINT64_C(0x0006973D40E8D54D), UINT64_C(0x00010E78CCE403C7),
          UINT64_C(0x000265A013FD1B69)},
         {UINT64_C(0x00000339C480223D), UINT64_C(0x00055E9E7BB6DB26),
          UINT64_C(0x00058F178B4BACE4), UINT64_C(0x00071F32575FDCD2),
          UINT64_C(0x0001C71C69B2119D)}},
    },
    {
        {{UINT64_C(0x000AF8DFD07525D6), UINT64_C(0x0005ABDDBE90D603),
          UINT64_C(0x0004A248FDF2D0CF), UINT64_C(0x0005172B1CA46E8D),
          UINT64_C(0x0007BC8CE3E6B3DD)},
         {UINT64_C(0x000F6A2BEB2B44FC), UINT64_C(0x00000D349B42C8CF),
          UINT64_C(0x0004B7CD7C88C973), UINT64_C(0x0005ECA8F4485D17),
          UINT64_C(0x0007D62C28C79894)}},
        {{UINT64_C(0x000B55207F725350), UINT64_C(0x000155DFEC40CDAB),
          UINT64_C(0x00027B783F369EDB), UINT64_C(0x00011A983C2841D6),
          UINT64_C(0x00012B6224AE4C25)},
         {UINT64_C(0x0009BF92AE68CF96), UINT64_C(0x0001147BF976A441),
          UINT64_C(0x0002CCF3203AAF22), UINT64_C(0x000069BD39DE1693),
          UINT64_C(0x00032BC9448C60DE)}},
        {{UINT64_C(0x0006D373DED15DFB), UINT64_C(0x0006BAD3E5311A3A),
          UINT64_C(0x00012CCBC4FCB6F2), UINT64_C(0x0002A5A3B3908623),
          UINT64_C(0x000542CFC756FE18)},
         {UINT64_C(0x000657D935ECDC3F), UINT64_C(0x0000A8A6A301FE80),
          UINT64_C(0x000394A934538AE2), UINT64_C(0x0001DB171CCFA8F2),
          UINT64_C(0x000490705EEEAB8C)}},
        {{UINT64_C(0x0008239926A0236C), UINT64_C(0x0004072433544306),
          UINT64_C(0x0007BB7A8ED39FBB), UINT64_C(0x0001F54925C1C4E3),
          UINT64_C(0x000664E83A948559)},
         {UINT64_C(0x000F11625DBAE781), UINT64_C(0x000692D219D5FF95),
          UINT64_C(0x00073D91B99DD9EA), UINT64_C(0x00050FBE7DF80371),
          UINT64_C(0x00057654A7F31CC1)}},
        {{UINT64_C(0x000AEE343C7C9D05), UINT64_C(0x000090117343650E),
          UINT64_C(0x0006CC575AEDA322), UINT64_C(0x0004F1E2F2B51775),
          UINT64_C(0x00076F6DB85D5F21)},
         {UINT64_C(0x000B8CF913A709D2), UINT64_C(0x0007FB116D7DC97E),
          UINT64_C(0x0001529FD6EEF6A5), UINT64_C(0x00056A6B91E47DF0),
          UINT64_C(0x0002E9B775561208)}},
        {{UINT64_C(0x000575D0571791A0), UINT64_C(0x000578E1DBFBEA77),
          UINT64_C(0x0003A703A52790E7), UINT64_C(0x0000C97F49B37153),
          UINT64_C(0x0004278132554EB7)},
         {UINT64_C(0x000157EE9EA2F134), UINT64_C(0x0003BB0E79C3342D),
          UINT64_C(0x0002C36A38226E01), UINT64_C(0x0007C88322ACB85B),
          UINT64_C(0x0004FB186883785F)}},
        {{UINT64_C(0x0002044FEB9F9253), UINT64_C(0x00022FA260775335),
          UINT64_C(0x000039F6E84F28A2), UINT64_C(0x0001836DD1BAB756),
          UINT64_C(0x00008E7A67948E3E)},
         {UINT64_C(0x00054204D45393E2), UINT64_C(0x0003CECF402108D1),
          UINT64_C(0x0000C701A3210E7D), UINT64_C(0x0000BEADB8DF7D41),
          UINT64_C(0x00017AC9DF9C5498)}},
        {{UINT64_C(0x0002F2E0A3E75974), UINT64_C(0x0002A9F168A74A87),
          UINT64_C(0x00029F4A8513C289), UINT64_C(0x00018E556097BC48),
          UINT64_C(0x000200FEB5BAD923)},
         {UINT64_C(0x0001B9CE667358CA), UINT64_C(0x00055AE551A801F4),
          UINT64_C(0x00042DE696A62306), UINT64_C(0x0002C75B9C6C564B),
          UINT64_C(0x0000164A218F9F79)}},
        {{UINT64_C(0x000F29F428A74C0C), UINT64_C(0x00063F194651B933),
          UINT64_C(0x000528FA6E64E1E3), UINT64_C(0x00068143117910EA),
          UINT64_C(0x0001B40977C6BA02)},
         {UINT64_C(0x0007F5D2BC286354), UINT64_C(0x000402F53D46EBF9),
          UINT64_C(0x00003B2C2080B52C), UINT64_C(0x00063D348A76083A),
          UINT64_C(0x0000FD0D6C7223FA)}},
        {{UINT64_C(0x000147D6E66EFE04), UINT64_C(0x0005E90EE23C1075),
          UINT64_C(0x0006E84476D5B541), UINT64_C(0x00052A8100787130),
          UINT64_C(0x0002D2188343EDC6)},
         {UINT64_C(0x0001F9FAD89722F0), UINT64_C(0x0004A9F2401F68AE),
          UINT64_C(0x000293427A23D1F0), UINT64_C(0x0001D52B70F2E2E3),
          UINT64_C(0x0003C937077DA4B5)}},
        {{UINT64_C(0x000874B69E730AAD), UINT64_C(0x000646B3E1A4C500),
          UINT64_C(0x0007077863F9D016), UINT64_C(0x00060558644A040D),
          UINT64_C(0x00036900BEB6A2F1)},
         {UINT64_C(0x0005C370223FB20A), UINT64_C(0x000447FB93D1E921),
          UINT64_C(0x00064D502A1F13B6), UINT64_C(0x0004251BECD4B787),
          UINT64_C(0x0007D125955B235D)}},
        {{UINT64_C(0x000FF3E0247628C3), UINT64_C(0x0005612C7EF51C2A),
          UINT64_C(0x0002EF572E567422), UINT64_C(0x0003F06B0469AB7B),
          UINT64_C(0x000209BA13287307)},
         {UINT64_C(0x0008994050632779), UINT64_C(0x00009A3E92F3709F),
          UINT64_C(0x00016204EE7B6320), UINT64_C(0x0002F94D7B3DCAF7),
          UINT64_C(0x0000B4460F41EE3D)}},
        {{UINT64_C(0x000C5F26F312D2C5), UINT64_C(0x00071E7C34CDFED2),
          UINT64_C(0x0002A8304ADF0D4F), UINT64_C(0x0005133E4EAA736F),
          UINT64_C(0x0004ABE07C40CFA9)},
         {UINT64_C(0x000A08F9029290FF), UINT64_C(0x0003EAF403C6D98B),
          UINT64_C(0x00060DBC0EDB37B6), UINT64_C(0x00007CEC6635832C),
          UINT64_C(0x0000C81F88E255BC)}},
        {{UINT64_C(0x000C0F4508FE693D), UINT64_C(0x00057782552C17E7),
          UINT64_C(0x000167570039C207), UINT64_C(0x0006114C8419F3C0),
          UINT64_C(0x0001712CE2C1CBEA)},
         {UINT64_C(0x0006787A0AA1DBED), UINT64_C(0x0002B18B64E45E6A),
          UINT64_C(0x0005FCE592027594), UINT64_C(0x0002E88EBA3ECFFD),
          UINT64_C(0x000476FD0C587C15)}},
        {{UINT64_C(0x000CBD80AAB36FBF), UINT64_C(0x0006ABA6598D0A0A),
          UINT64_C(0x0000D1C530FCAC66), UINT64_C(0x0002286C6B68E8F6),
          UINT64_C(0x0006AAF0D091C807)},
         {UINT64_C(0x00005BDF3CD0E8D0), UINT64_C(0x0006960A3E5D2C66),
          UINT64_C(0x000638B2EBDA0973), UINT64_C(0x0004EE54544D3339),
          UINT64_C(0x00040BDB62E2C15A)}},
        {{UINT64_C(0x000BECCC002BCD28), UINT64_C(0x0007346E8C2A60E7),
          UINT64_C(0x0003B9F742D1704B), UINT64_C(0x0001370F879A06B2),
          UINT64_C(0x0005A0A68F92C852)},
         {UINT64_C(0x000BD4FD9CD7C516), UINT64_C(0x00004D3D2354F933),
          UINT64_C(0x0003175E3418C5B6), UINT64_C(0x000415E970F01D4A),
          UINT64_C(0x0000277440ED12C8)}},
    },
    {
        {{UINT64_C(0x0004F80B29259121), UINT64_C(0x0004C7AD79241CBB),
          UINT64_C(0x00031A2024B0C285), UINT64_C(0x0002905F7DC32728),
          UINT64_C(0x00018F4173185CE8)},
         {UINT64_C(0x0008D35E043B3193), UINT64_C(0x000294DB8F82806B),
          UINT64_C(0x0006E11FA9EFB7F8), UINT64_C(0x0007EC8CC6030101),
          UINT64_C(0x0007CE956AED6CFE)}},
        {{UINT64_C(0x000CE55CC1171587), UINT64_C(0x00054B3932A64433),
          UINT64_C(0x0005BA7C98FD8209), UINT64_C(0x00056CCE0ED96A42),
          UINT64_C(0x00079534ED8A0FAF)},
         {UINT64_C(0x000AB036181F5C5D), UINT64_C(0x0002C036C8A43EDE),
          UINT64_C(0x0001D4508E4C7ED0), UINT64_C(0x0000E128F1A3971F),
          UINT64_C(0x000417208F6464E5)}},
        {{UINT64_C(0x000633C1FF9DFEFF), UINT64_C(0x0005208BBFBDE89E),
          UINT64_C(0x000256F7BBEE3FF4), UINT64_C(0x000577F9D2D9026B),
          UINT64_C(0x000745C594B0BDC9)},
         {UINT64_C(0x000021489A9DCD65), UINT64_C(0x00077418195C0224),
          UINT64_C(0x00043817466CDB80), UINT64_C(0x0001F3BC7125C1D7),
          UINT64_C(0x00022C79F6BB84F9)}},
        {{UINT64_C(0x000175240458C6F2), UINT64_C(0x0003FF9B00DB6449),
          UINT64_C(0x0001764BB4C86102), UINT64_C(0x0004BCC4DB3AF4F4),
          UINT64_C(0x0007EF78D9E386BB)},
         {UINT64_C(0x0002DFBA149190F7), UINT64_C(0x0003DA9B88D643CF),
          UINT64_C(0x0001C3155190EEEC), UINT64_C(0x0005D430C2250A24),
          UINT64_C(0x0002593ACE56A866)}},
        {{UINT64_C(0x000A92165B59C268), UINT64_C(0x0007B4FD61C8ACFF),
          UINT64_C(0x00025086FD2DA482), UINT64_C(0x000694EDA01406D5),
          UINT64_C(0x0005DDE8F63674A6)},
         {UINT64_C(0x00070F2A7161678D), UINT64_C(0x0005D1DAA454FEEB),
          UINT64_C(0x0003E403685BEC15), UINT64_C(0x0003DA80C68EC0D1),
          UINT64_C(0x000190B8FD47B2F4)}},
        {{UINT64_C(0x000D2F2403EECAB3), UINT64_C(0x0006EA952EBF1B65),
          UINT64_C(0x0007D581FECF7ECC), UINT64_C(0x000306921DE856F0),
          UINT64_C(0x0000F2F865D32B26)},
         {UINT64_C(0x0006463217B9D5F6), UINT64_C(0x0005BC460EE03A7D),
          UINT64_C(0x0002C7357E245644), UINT64_C(0x000336E4E5BEB241),
          UINT64_C(0x0004EAA60FB21263)}},
        {{UINT64_C(0x00031B08269F9703), UINT64_C(0x00005209A523B7D2),
          UINT64_C(0x000039942D5F26A4), UINT64_C(0x0002779B510C5171),
          UINT64_C(0x000554D0A38E2ED7)},
         {UINT64_C(0x0009D61A3EB63C62), UINT64_C(0x0006B3C74DDABF3E),
          UINT64_C(0x000180175FCCD722), UINT64_C(0x0005DDF5EF9271A6),
          UINT64_C(0x0005D1B2EB83F58D)}},
        {{UINT64_C(0x000269380CF53650), UINT64_C(0x000478CBA29C961B),
          UINT64_C(0x0003DCE7E460A8AF), UINT64_C(0x0005DC0399EB13A3),
          UINT64_C(0x00078777353BF27D)},
         {UINT64_C(0x000ED3B71CB3DDF7), UINT64_C(0x0004937FD2AC37C7),
          UINT64_C(0x0007A97F8A99835C), UINT64_C(0x0007E9175E349529),
          UINT64_C(0x00049AD7788A8375)}},
        {{UINT64_C(0x000AFB12685F6754), UINT64_C(0x0001CC137990650F),
          UINT64_C(0x0006EC6C0DB127EC), UINT64_C(0x000002F3F9C92B06),
          UINT64_C(0x00068ED61BED7B48)},
         {UINT64_C(0x00048477FEC95FCE), UINT64_C(0x000516ED0BB6C079),
          UINT64_C(0x00058AC37081FA80), UINT64_C(0x00052743D6E32AAF),
          UINT64_C(0x000135B867D60FA5)}},
        {{UINT64_C(0x000017A14A66889B), UINT64_C(0x0005B0AEEAE56F98),
          UINT64_C(0x0002236BACAE8AF2), UINT64_C(0x0003C2257520A874),
          UINT64_C(0x000436DD2F42BB1A)},
         {UINT64_C(0x000E73DEE8E27B51), UINT64_C(0x00026156FABFD33E),
          UINT64_C(0x0002E3A732B167EF), UINT64_C(0x000593856E3BA35D),
          UINT64_C(0x000572BAFD316337)}},
        {{UINT64_C(0x0004CCCD1BFE2F09), UINT64_C(0x0001EB388A46DB63),
          UINT64_C(0x00052EC6A89BDB63), UINT64_C(0x0003F464068D9320),
          UINT64_C(0x00066681DD5542FC)},
         {UINT64_C(0x000ADA67B76DEDEE), UINT64_C(0x00020BC1E48F374E),
          UINT64_C(0x00010A381907FB3D), UINT64_C(0x0002A188EFB7D771),
          UINT64_C(0x000204A6CB902379)}},
        {{UINT64_C(0x00017228A8F41270), UINT64_C(0x0003861FA7811FF1),
          UINT64_C(0x0005C24BAA43480B), UINT64_C(0x0000D91805B4EAEF),
          UINT64_C(0x000161DF51FF7C92)},
         {UINT64_C(0x0002E8C34073031B), UINT64_C(0x0004FC9FC002E034),
          UINT64_C(0x0000C517474BD432), UINT64_C(0x0005B028CC00B4FC),
          UINT64_C(0x00032CEE00379E56)}},
        {{UINT64_C(0x0004483DBEADB2E0), UINT64_C(0x0006513EDE13135B),
          UINT64_C(0x00016DAD5C521A87), UINT64_C(0x0005A3E8526657FC),
          UINT64_C(0x00071D76B3E6D02E)},
         {UINT64_C(0x0006473D1426BAF9), UINT64_C(0x0004AA50405810D9),
          UINT64_C(0x000775BF4EA667B2), UINT64_C(0x00028F630F480B2A),
          UINT64_C(0x000541E49B3C2921)}},
        {{UINT64_C(0x0000C2316D694346), UINT64_C(0x0006BB5D8DD7AC20),
          UINT64_C(0x000544BA02C52F67), UINT64_C(0x0001A8380E0C329B),
          UINT64_C(0x00006254291B6D56)},
         {UINT64_C(0x00023469E53DE6A0), UINT64_C(0x00067B362FC9815B),
          UINT64_C(0x00078587A6ABE2A0), UINT64_C(0x0001E2DDC24C52FB),
          UINT64_C(0x0002969D891424B8)}},
        {{UINT64_C(0x000DD3BE38C271BD), UINT64_C(0x00008AADD395B9D4),
          UINT64_C(0x00041097DCD173B3), UINT64_C(0x000377212B3119E9),
          UINT64_C(0x0000E67BF82272D5)},
         {UINT64_C(0x0006E0BB94CF58AC), UINT64_C(0x000408F1A38E35FD),
          UINT64_C(0x00029EF91D563A40), UINT64_C(0x00056766929B4B6C),
          UINT64_C(0x0005C011DCB741EA)}},
        {{UINT64_C(0x0004D1781BD13FD0), UINT64_C(0x00004CE399554755),
          UINT64_C(0x0003A691DA00B124), UINT64_C(0x0007283232CCD5CF),
          UINT64_C(0x00052A1AF5A0D851)},
         {UINT64_C(0x0007BEE24A3684D7), UINT64_C(0x0005652266B2A4B5),
          UINT64_C(0x0000289E5435AFE4), UINT64_C(0x00015FEFBEE25260),
          UINT64_C(0x0007347EAFAF74A5)}},
    },
    {
        {{UINT64_C(0x000C05E66D978880), UINT64_C(0x00025A3CB58DEACC),
          UINT64_C(0x000220AB73235F4C), UINT64_C(0x000490D8F0DF59E2),
          UINT64_C(0x0007C69489BD76E1)},
         {UINT64_C(0x000DBD924D1922F1), UINT64_C(0x0005833BE28C2F2F),
          UINT64_C(0x00035B00E5DE675F), UINT64_C(0x0002F1B35D5A7675),
          UINT64_C(0x0004858F6235F10C)}},
        {{UINT64_C(0x0002A925A417EA11), UINT64_C(0x00056E31BC4D175E),
          UINT64_C(0x00038E61D9A3B411), UINT64_C(0x0003812AC6AEFFE2),
          UINT64_C(0x000228B12A98C126)},
         {UINT64_C(0x000A7B3E73B423B7), UINT64_C(0x00066CCB2668379B),
          UINT64_C(0x00065B91202BD9C8), UINT64_C(0x00029ED3BE98ABF3),
          UINT64_C(0x000273C6405F5146)}},
        {{UINT64_C(0x0005C609D0B22D03), UINT64_C(0x0000E2D4E998729A),
          UINT64_C(0x0004FCCD77C349D1), UINT64_C(0x00061B1D24B06088),
          UINT64_C(0x0004891488BBB2D2)},
         {UINT64_C(0x000D2AE5BCA0967E), UINT64_C(0x00050335CABFCB67),
          UINT64_C(0x0000DDE04AEEADB4), UINT64_C(0x00028AEBB9AD957F),
          UINT64_C(0x0004D067C74CCBC3)}},
        {{UINT64_C(0x000DA73F6F1AECD9), UINT64_C(0x00079BFF45612FC1),
          UINT64_C(0x00051867E472C99F), UINT64_C(0x0001A560565247F0),
          UINT64_C(0x0005BED5F4CEB10A)},
         {UINT64_C(0x000C2D41FFEE613B), UINT64_C(0x00048CDF00CF0891),
          UINT64_C(0x0005E4502AA3852C), UINT64_C(0x000656DF596B0089),
          UINT64_C(0x000058D53AE7B7AC)}},
        {{UINT64_C(0x0005633B9B0B6527), UINT64_C(0x0002F32BC7DCAE54),
          UINT64_C(0x00050DF012CC4A7F), UINT64_C(0x0001DC2DC80CAF77),
          UINT64_C(0x0001CC28BCE8B4B0)},
         {UINT64_C(0x000352FCCCD0A46D), UINT64_C(0x00020A7D1A52DB7F),
          UINT64_C(0x0003E924D7CB5F73), UINT64_C(0x0005E0F520791FAC),
          UINT64_C(0x0006FBACACFAA219)}},
        {{UINT64_C(0x0007DD39E4996ED6), UINT64_C(0x000285231A44D102),
          UINT64_C(0x0002FEC0C9BAA76E), UINT64_C(0x0001D126BB5F5562),
          UINT64_C(0x000720EC42A4351F)},
         {UINT64_C(0x0002C4D634B2D745), UINT64_C(0x0007FFF3E43B229C),
          UINT64_C(0x0003A4E652046B82), UINT64_C(0x00007FDDB53AF519),
          UINT64_C(0x0003FD6A967B2866)}},
        {{UINT64_C(0x000DF55AD8667E0F), UINT64_C(0x000473081C72AAFB),
          UINT64_C(0x0002E3E9DD231D51), UINT64_C(0x0000638B70AA3F97),
          UINT64_C(0x00019AE4400E8136)},
         {UINT64_C(0x000E28055BC46447), UINT64_C(0x0000760478B05575),
          UINT64_C(0x0005AA7505F4850B), UINT64_C(0x0005BA7632BE614C),
          UINT64_C(0x0004C7DEAF3BFA9E)}},
        {{UINT64_C(0x000F0AA2A3AA87C8), UINT64_C(0x000019FBB01D0DFF),
          UINT64_C(0x0006DE474AD0CB2C), UINT64_C(0x0001B2D3AFEE81F5),
          UINT64_C(0x0004B057E9DF9F8F)},
         {UINT64_C(0x0008A62F51812859), UINT64_C(0x000096944166A90E),
          UINT64_C(0x0006E95AB6851BA6), UINT64_C(0x00055E80DE3635DA),
          UINT64_C(0x0007F910349B50D7)}},
        {{UINT64_C(0x000024BD4E9A47A8), UINT64_C(0x00044E53AC4823A9),
          UINT64_C(0x00060A0B2A2844E1), UINT64_C(0x0006CA7FA0EC1AB9),
          UINT64_C(0x00079DBC726A893E)},
         {UINT64_C(0x0002ECBD617D4BFD), UINT64_C(0x0000ED06A79CD79B),
          UINT64_C(0x0001128125B8C751), UINT64_C(0x0001A3CDAF24D9F6),
          UINT64_C(0x00076D2644374FB2)}},
        {{UINT64_C(0x000BB495334D6735), UINT64_C(0x00048E4E318D35A5),
          UINT64_C(0x00065CCC9FD612CE), UINT64_C(0x00048647BF9AD225),
          UINT64_C(0x0001F308CFFC4D92)},
         {UINT64_C(0x000993066FB5CA15), UINT64_C(0x0001024A9DBAAA76),
          UINT64_C(0x0001D8206F56BB3E), UINT64_C(0x000791789CB797FC),
          UINT64_C(0x00028DAF3708084C)}},
        {{UINT64_C(0x0005F4D6A2BB2865), UINT64_C(0x000411783ABFEAA9),
          UINT64_C(0x000285FE4D07FDEA), UINT64_C(0x00012C2888BBAB91),
          UINT64_C(0x0002F0D1613CBFEA)},
         {UINT64_C(0x000761DD1F118DE4), UINT64_C(0x00077D77CAB21417),
          UINT64_C(0x000104DF213824A8), UINT64_C(0x00019BDCA8BF3BBE),
          UINT64_C(0x000228E1468298F4)}},
        {{UINT64_C(0x000AD4D25721FB0D), UINT64_C(0x0005BBEC9D9B8FCB),
          UINT64_C(0x00006F18917A24A9), UINT64_C(0x0001AF194E249824),
          UINT64_C(0x0001C1D002C78C5F)},
         {UINT64_C(0x0005FCEA6541A6D3), UINT64_C(0x00012B0A3891C25A),
          UINT64_C(0x0001CA915EF579A4), UINT64_C(0x0006A1530D0CA296),
          UINT64_C(0x000405353E349C62)}},
        {{UINT64_C(0x0007DD87797327CF), UINT64_C(0x00049A24A1519C3D),
          UINT64_C(0x0007E2D3194C7738), UINT64_C(0x0004ADE201A4F2B4),
          UINT64_C(0x0005F03F2024D796)},
         {UINT64_C(0x0003A8A2BF24065F), UINT64_C(0x00028B98E4E34848),
          UINT64_C(0x0006CCE4BE857549), UINT64_C(0x0001BFEA602C4AB8),
          UINT64_C(0x00057C821F831EFA)}},
        {{UINT64_C(0x0000117A2CF6FB17), UINT64_C(0x0003D763A3738149),
          UINT64_C(0x00074936049CAC1E), UINT64_C(0x0007F5A417605453),
          UINT64_C(0x0003C597E1AF5EA2)},
         {UINT64_C(0x0003FBDD76DC1695), UINT64_C(0x0003C1FDAC636FA3),
          UINT64_C(0x0002045F1403CA79), UINT64_C(0x00034AEC03F7A823),
          UINT64_C(0x0000862068283073)}},
        {{UINT64_C(0x00078F961971E1BB), UINT64_C(0x0007027C711936C2),
          UINT64_C(0x00069D7EBBB0EDBC), UINT64_C(0x0002680D032F1C61),
          UINT64_C(0x000615B3503A9F8A)},
         {UINT64_C(0x000BC2A332182D40), UINT64_C(0x000706CF8E7CBE3C),
          UINT64_C(0x0004537D7373214F), UINT64_C(0x0002F1C7690B885E),
          UINT64_C(0x00022B7364CF32E1)}},
        {{UINT64_C(0x00062087435B3F31), UINT64_C(0x0007EA9C3772E730),
          UINT64_C(0x0006CBD6A025839C), UINT64_C(0x0002E4060C1FA411),
          UINT64_C(0x00013A216F147ECC)},
         {UINT64_C(0x0008E787A8F7310F), UINT64_C(0x0007C9628A16653F),
          UINT64_C(0x0004B18B674CF1D2), UINT64_C(0x00042C9C4340922B),
          UINT64_C(0x00012DD24A757E97)}},
    },
    {
        {{UINT64_C(0x000D0DAB38BAC990), UINT64_C(0x00014AB1DE778BDD),
          UINT64_C(0x00044AAC030B8EE5), UINT64_C(0x00035662B41D1CA8),
          UINT64_C(0x0003E1B41627AA05)},
         {UINT64_C(0x00093D56AB528772), UINT64_C(0x00056A1F801B8BDD),
          UINT64_C(0x00002395FB846157), UINT64_C(0x00055F0166143AAB),
          UINT64_C(0x000138392048CE38)}},
        {{UINT64_C(0x000629076955EF18), UINT64_C(0x0001F5903EDC6036),
          UINT64_C(0x0003B60DF38E1955), UINT64_C(0x0007D3533D1A0956),
          UINT64_C(0x000616E9E879B346)},
         {UINT64_C(0x000CC4D2E5A62590), UINT64_C(0x00008DF009361C78),
          UINT64_C(0x00060924BE5F5C3F), UINT64_C(0x0005698777D3EB64),
          UINT64_C(0x0000BD31C925896B)}},
        {{UINT64_C(0x000CAF98364436A6), UINT64_C(0x0001984F8F41D0B8),
          UINT64_C(0x0006839D748040E7), UINT64_C(0x000003647A88D1C1),
          UINT64_C(0x0004C9A82C439698)},
         {UINT64_C(0x0009F1BB2D35D65D), UINT64_C(0x000656D1E3913D0B),
          UINT64_C(0x00025D8A8DB01F82), UINT64_C(0x000716C168BA26FC),
          UINT64_C(0x0005F7EAE6DA00DF)}},
        {{UINT64_C(0x0006900AF557123B), UINT64_C(0x00048AD482A31C95),
          UINT64_C(0x000224E02FA0D5A3), UINT64_C(0x00049F121BC5BB9C),
          UINT64_C(0x0002819539DBCC91)},
         {UINT64_C(0x00052AA36806E6D3), UINT64_C(0x00075D00DA560416),
          UINT64_C(0x00020C8D5835B70C), UINT64_C(0x00042F7FCE2FDE3F),
          UINT64_C(0x00018D1671FEA942)}},
        {{UINT64_C(0x000126788DAF932F), UINT64_C(0x0001A2486598E1EB),
          UINT64_C(0x0001FA9390F89087), UINT64_C(0x000465B8FBA99789),
          UINT64_C(0x0001391087EBF066)},
         {UINT64_C(0x000D560B9EBE0A12), UINT64_C(0x00045DF4FD156260),
          UINT64_C(0x0004C0F5BB97F89C), UINT64_C(0x00000C02D902AD41),
          UINT64_C(0x0006D6ABD8B710CB)}},
        {{UINT64_C(0x000240C819BE997B), UINT64_C(0x00079702659A3FCC),
          UINT64_C(0x00038FF0842940FF), UINT64_C(0x0003BE2FC37F5DD7),
          UINT64_C(0x00055903D1BF4D50)},
         {UINT64_C(0x000F7788BBCF7C69), UINT64_C(0x000314F86B17508B),
          UINT64_C(0x000759BA40AAA5DE), UINT64_C(0x00008F55D89B53DA),
          UINT64_C(0x000040CD6FEF17AC)}},
        {{UINT64_C(0x000BCF2803B34AFC), UINT64_C(0x00015BCEC54183DB),
          UINT64_C(0x0000E63B583C38D3), UINT64_C(0x000543CEC0DD9AC8),
          UINT64_C(0x0006DCA0EDABDF99)},
         {UINT64_C(0x0006AD8FCA4B7A84), UINT64_C(0x00000ECF4C9B4756),
          UINT64_C(0x0002EA049D3C90F8), UINT64_C(0x000434F3AFA9C904),
          UINT64_C(0x0005BB44FDA046A9)}},
        {{UINT64_C(0x000456FFF38CE9AA), UINT64_C(0x0007628838C3FB34),
          UINT64_C(0x00047DEFE5F449AB), UINT64_C(0x0002EBE6BAC977B1),
          UINT64_C(0x00002629151FD158)},
         {UINT64_C(0x00056D1807A2F107), UINT64_C(0x00037D8DA4EE56AD),
          UINT64_C(0x00064130E6C56675), UINT64_C(0x000059823CCD4BAE),
          UINT64_C(0x000351D4561A3074)}},
        {{UINT64_C(0x000D0D5151460DBA), UINT64_C(0x00041AAD3DC6D3F7),
          UINT64_C(0x00030392B1D25432), UINT64_C(0x0000EC685A8F63C0),
          UINT64_C(0x00041E76ED18C1CB)},
         {UINT64_C(0x000DC86815DBE186), UINT64_C(0x0001744C18B22C78),
          UINT64_C(0x0007FFF9EFE95AD9), UINT64_C(0x0005EE221F3A7F26),
          UINT64_C(0x0002ED76DDD0673C)}},
        {{UINT64_C(0x000BE8E0AEDD873D), UINT64_C(0x0005C21AD1208B00),
          UINT64_C(0x00000C2B71802C36), UINT64_C(0x0003CEE802954BF7),
          UINT64_C(0x000212A13C9706C1)},
         {UINT64_C(0x000C1F2261D59FF3), UINT64_C(0x0005FE776BC7F5CB),
          UINT64_C(0x00052FBD0C5EC34E), UINT64_C(0x000431CC02ACD223),
          UINT64_C(0x00024D9DB51E1E1F)}},
        {{UINT64_C(0x0002C7CAE989C78A), UINT64_C(0x00053B83FF5723A2),
          UINT64_C(0x00008A8E6302E842), UINT64_C(0x00028299A28F9921),
          UINT64_C(0x0004216EE9364122)},
         {UINT64_C(0x000C023A772C47B9), UINT64_C(0x000773C2D287BB4D),
          UINT64_C(0x0006DA1F1AA10221), UINT64_C(0x0003A3241B2B09FC),
          UINT64_C(0x00008CDE7A649B22)}},
        {{UINT64_C(0x0007450C29EBCF74), UINT64_C(0x0004439DBEEB77A6),
          UINT64_C(0x000343C10D713C0F), UINT64_C(0x00059B68D7FEB70F),
          UINT64_C(0x00034672B08F9529)},
         {UINT64_C(0x000EB776F7FE244B), UINT64_C(0x0000A1574415007C),
          UINT64_C(0x0002E545671DDCC2), UINT64_C(0x0000C8F00787B92C),
          UINT64_C(0x0004CCFCC6C1C843)}},
        {{UINT64_C(0x000752C74FA6089E), UINT64_C(0x00043769AF9D8146),
          UINT64_C(0x00076A2097C10ACD), UINT64_C(0x00021E5FD7934DFC),
          UINT64_C(0x00044CD453C591F0)},
         {UINT64_C(0x0005D742FEFE529A), UINT64_C(0x00052812A2132236),
          UINT64_C(0x00063A0FF82BAAA3), UINT64_C(0x0003821492FA5799),
          UINT64_C(0x0006C3D3D91E5995)}},
        {{UINT64_C(0x0007F5965BB5061A), UINT64_C(0x00033DD3B61A3ACD),
          UINT64_C(0x0000ED9A94EAF477), UINT64_C(0x00005E2154735C2D),
          UINT64_C(0x000310C8E2DC1FA9)},
         {UINT64_C(0x0009C12CA0FAB3FE), UINT64_C(0x000645039E999A55),
          UINT64_C(0x00027E092D9877A3), UINT64_C(0x000234C26B632A1C),
          UINT64_C(0x0001112B3978BBB5)}},
        {{UINT64_C(0x000643EF08EEDFDA), UINT64_C(0x00041557FC7F909E),
          UINT64_C(0x0007D194BE89C579), UINT64_C(0x00013E7D0EB4169D),
          UINT64_C(0x00068C97C0B130AB)},
         {UINT64_C(0x0005670E75F30FC3), UINT64_C(0x00012ABE1F6F59DB),
          UINT64_C(0x00031C4929892E23), UINT64_C(0x00067C5926EE61D5),
          UINT64_C(0x0005EA09499EB737)}},
        {{UINT64_C(0x00068E5711D3CC63), UINT64_C(0x000355284FF4EE0B),
          UINT64_C(0x0002ED6C094527F4), UINT64_C(0x00018FD4F583BBBD),
          UINT64_C(0x0007E732542EB790)},
         {UINT64_C(0x000FBD7D6A2B1252), UINT64_C(0x0000D8B42A24E7BC),
          UINT64_C(0x00064FE14876840C), UINT64_C(0x000128C33F1AE9C6),
          UINT64_C(0x00047654D530ABA4)}},
    },
    {
        {{UINT64_C(0x0003BD937849F59D), UINT64_C(0x000063606C7698D1),
          UINT64_C(0x00054B2A0FD2FD04), UINT64_C(0x00029E53F79F11A6),
          UINT64_C(0x000555BAED07F9C6)},
         {UINT64_C(0x0002D0A2306D73CE), UINT64_C(0x00029DFF85DCB561),
          UINT64_C(0x0000C1C9000DBCD2), UINT64_C(0x00021A0F680519E0),
          UINT64_C(0x00001BC10E993DB9)}},
        {{UINT64_C(0x00010A2BB8D581B9), UINT64_C(0x00043123D8B4D328),
          UINT64_C(0x0003D3F6518C92CF), UINT64_C(0x0005EF4037756EF4),
          UINT64_C(0x00023620C43DD673)},
         {UINT64_C(0x000758B33A3A641C), UINT64_C(0x000201801C92394E),
          UINT64_C(0x0007381BD7F0EF26), UINT64_C(0x0007C1BDA98CF272),
          UINT64_C(0x000446E3CF2318B9)}},
        {{UINT64_C(0x000CAA4017FCF7B6), UINT64_C(0x0005167AEA33B820),
          UINT64_C(0x000455C15B6B0603), UINT64_C(0x0003795DAA8317AF),
          UINT64_C(0x0000A7CFFBD6A356)},
         {UINT64_C(0x000CDF21A7146CD4), UINT64_C(0x0005D9C22C0B9910),
          UINT64_C(0x00021D488F79ACFF), UINT64_C(0x0005857D2A8817A3),
          UINT64_C(0x000418B1F899B332)}},
        {{UINT64_C(0x000F3021477826E7), UINT64_C(0x0001E04BD00E11CF),
          UINT64_C(0x0006EA82EE2CF354), UINT64_C(0x000576E72ADFAF95),
          UINT64_C(0x0001D0D5496B0BCA)},
         {UINT64_C(0x000D490F0DD8482A), UINT64_C(0x00049D8CFF1AC3F4),
          UINT64_C(0x0003460BFA5563D1), UINT64_C(0x00055034532A88B0),
          UINT64_C(0x0006A47213652104)}},
        {{UINT64_C(0x00011F911810E70C), UINT64_C(0x0002D6B48D33D172),
          UINT64_C(0x00034AB16D9AF3D1), UINT64_C(0x000561679C3033DD),
          UINT64_C(0x000357CCE137A82D)},
         {UINT64_C(0x000B31F0EF17F79E), UINT64_C(0x0006EA6CD3094B9C),
          UINT64_C(0x0000B35F3599DBB1), UINT64_C(0x0007F52CCB68649E),
          UINT64_C(0x0005046AE5A46633)}},
        {{UINT64_C(0x0006B212836316B9), UINT64_C(0x00055299B1C49A9A),
          UINT64_C(0x000705EEFD2620E8), UINT64_C(0x00046DC053B6EE87),
          UINT64_C(0x0007030884C7FF32)},
         {UINT64_C(0x0006B7B8E9DC79C4), UINT64_C(0x00013A8B2116229F),
          UINT64_C(0x0004292D01383268), UINT64_C(0x000265411FE1DFD7),
          UINT64_C(0x00051B40396F7F2D)}},
        {{UINT64_C(0x00046D30B8D0B765), UINT64_C(0x000447051A08754C),
          UINT64_C(0x00002C393E136A77), UINT64_C(0x000092C51BEF8B5B),
          UINT64_C(0x00072A82D90D0C54)},
         {UINT64_C(0x000E14BC9577FA35), UINT64_C(0x0002613D84E90001),
          UINT64_C(0x0007DCD817FC6B73), UINT64_C(0x0004CCB07A61114E),
          UINT64_C(0x000128DDAE580395)}},
        {{UINT64_C(0x0000A65F62B5B469), UINT64_C(0x0001449CDF3BA068),
          UINT64_C(0x0002B322E44F8B5C), UINT64_C(0x000637CD9DAA7CD9),
          UINT64_C(0x0002826D20F6B6E0)},
         {UINT64_C(0x000935F7AE7CE72C), UINT64_C(0x000427C2F3C89114),
          UINT64_C(0x0002BC82197EEC1E), UINT64_C(0x0001D3AD81C2BB09),
          UINT64_C(0x000113864CB480E5)}},
        {{UINT64_C(0x000DF31256D0FA32), UINT64_C(0x000658162F4C433B),
          UINT64_C(0x000357078BBD48E7), UINT64_C(0x0005D6C0B50101C9),
          UINT64_C(0x0007F2F4FDFD19FA)},
         {UINT64_C(0x0008F60FFAC3108A), UINT64_C(0x000301C7448AFA71),
          UINT64_C(0x00015EB2F2A78BEF), UINT64_C(0x00017F839739C650),
          UINT64_C(0x0000C63A5C9B4465)}},
        {{UINT64_C(0x000D2864DE66EFDB), UINT64_C(0x00019367AA93908D),
          UINT64_C(0x00042D4DF0EA2C9B), UINT64_C(0x00063DABB09387B7),
          UINT64_C(0x00041D7E1B815D0C)},
         {UINT64_C(0x00094DF2A88CE7E3), UINT64_C(0x0003E4681539CE25),
          UINT64_C(0x00065386E54F328E), UINT64_C(0x00045204F7EB68FF),
          UINT64_C(0x000152316128558C)}},
        {{UINT64_C(0x00085DD706DFBF1A), UINT64_C(0x0001CD3A8A2F3283),
          UINT64_C(0x000389177C3B129F), UINT64_C(0x000266A3AF43485D),
          UINT64_C(0x0004343000537F1E)},
         {UINT64_C(0x0004EDE550CB3A82), UINT64_C(0x0007920ADCB60EE2),
          UINT64_C(0x000508D86EC186E3), UINT64_C(0x000459BB98AE049A),
          UINT64_C(0x0003C8621E0C7957)}},
        {{UINT64_C(0x0003F7B355181E78), UINT64_C(0x00034B66A3DA0C48),
          UINT64_C(0x000075B38F80B96F), UINT64_C(0x0001E51DE76B5138),
          UINT64_C(0x00024C9C3D9E4494)},
         {UINT64_C(0x0003623DEB850A90), UINT64_C(0x0004C35B7E8127CD),
          UINT64_C(0x000657A5BF58646C), UINT64_C(0x0005C440DB594283),
          UINT64_C(0x00049678D88DAA4E)}},
        {{UINT64_C(0x000F586E6CF04B0C), UINT64_C(0x000056B294784ED4),
          UINT64_C(0x0006ACFA4C41FBCA), UINT64_C(0x0000F85BA1E98BC9),
          UINT64_C(0x0007BE95FD49B8CB)},
         {UINT64_C(0x000C7F0AEF19999C), UINT64_C(0x000710B40227E43B),
          UINT64_C(0x0005D2D5948B666C), UINT64_C(0x0007C2DEF57822E5),
          UINT64_C(0x0005E9DD0BCA2B4C)}},
        {{UINT64_C(0x0004CD7C5A1F0474), UINT64_C(0x000703184E03D953),
          UINT64_C(0x00008847256A3A0A), UINT64_C(0x00023256A8F362CE),
          UINT64_C(0x0006FA0A053C19F6)},
         {UINT64_C(0x0004FDA4EC4F6E5A), UINT64_C(0x0000EAAF716D7474),
          UINT64_C(0x00031B1D9BEEA00A), UINT64_C(0x0006E77CC7B43BEA),
          UINT64_C(0x00068B7FE52A5418)}},
        {{UINT64_C(0x000182C3893C913F), UINT64_C(0x0005BD035564DDCE),
          UINT64_C(0x0000EFB789EEEA1D), UINT64_C(0x0002FD7231BCE945),
          UINT64_C(0x0001E172F821BFD8)},
         {UINT64_C(0x000B79C4B183EE97), UINT64_C(0x0005AA4E66EB8680),
          UINT64_C(0x0003F0999EA39964), UINT64_C(0x0004CCC4C10842F5),
          UINT64_C(0x0001D9639116EB1B)}},
        {{UINT64_C(0x0001F4CFF4A6524D), UINT64_C(0x00077A87CA5D36AB),
          UINT64_C(0x00079C9CFA901FD4), UINT64_C(0x0007141692444638),
          UINT64_C(0x00050A52B8F11178)},
         {UINT64_C(0x000606812D21A754), UINT64_C(0x000006CCE87ECEFD),
          UINT64_C(0x00070CF16EA53DB0), UINT64_C(0x00064231A76B8461),
          UINT64_C(0x0001CE749122C65F)}},
    },
    {
        {{UINT64_C(0x0007BB82D3C5B684), UINT64_C(0x000665B3553FDD82),
          UINT64_C(0x000622B712414BE5), UINT64_C(0x00068A66023FD004),
          UINT64_C(0x0002C4F1C507E8E9)},
         {UINT64_C(0x00067B5C49251A27), UINT64_C(0x000767A5FEFDECEA),
          UINT64_C(0x0006D5127E924F9E), UINT64_C(0x0001FAEF3DFFBBB1),
          UINT64_C(0x00043D69E32FAD1C)}},
        {{UINT64_C(0x00034E704BF54825), UINT64_C(0x00049BDB19E6DC0E),
          UINT64_C(0x0007F28264FB6501), UINT64_C(0x00038880B959C688),
          UINT64_C(0x0004F0A3D37E757E)},
         {UINT64_C(0x0003CAC81F94C40C), UINT64_C(0x00058393688FFF80),
          UINT64_C(0x000347B9E5A5C961), UINT64_C(0x0007E8D8D772B424),
          UINT64_C(0x0006C42A57B3ABF7)}},
        {{UINT64_C(0x00097FC2E68C8CCF), UINT64_C(0x000475CEE29F1E8F),
          UINT64_C(0x000298E28356667E), UINT64_C(0x0000949DE8DE0D9F),
          UINT64_C(0x00011BD78F109CE1)},
         {UINT64_C(0x0009593D9CE46006), UINT64_C(0x00057E6F9033002A),
          UINT64_C(0x0005DEF40003F782), UINT64_C(0x00018A1E90F301F2),
          UINT64_C(0x0001C9DF45910326)}},
        {{UINT64_C(0x000C11204B142486), UINT64_C(0x0001DE83CFF6A309),
          UINT64_C(0x00028E98BD4FBE39), UINT64_C(0x0007FF81C2722301),
          UINT64_C(0x0004E8EC07E39C5C)},
         {UINT64_C(0x0001EACFEACEA3A2), UINT64_C(0x000121B16B6B2243),
          UINT64_C(0x00028A37B9BD6CDB), UINT64_C(0x00021A158458C7AE),
          UINT64_C(0x000633B208185C49)}},
        {{UINT64_C(0x0000EFDE020DF8D9), UINT64_C(0x0003D774591FDE3B),
          UINT64_C(0x0007A513E2E25CA3), UINT64_C(0x0005E307A27F1A86),
          UINT64_C(0x000526839795FF69)},
         {UINT64_C(0x0005D1C8482D8549), UINT64_C(0x0006DFD24C6F2ED5),
          UINT64_C(0x0003185AD6610EDB), UINT64_C(0x00011600733CB20B),
          UINT64_C(0x0000561D0593758D)}},
        {{UINT64_C(0x000E7303812E9251), UINT64_C(0x00060FAA9640AC68),
          UINT64_C(0x0006990E9E84EAAB), UINT64_C(0x00013205597A21E0),
          UINT64_C(0x0001B36994783E3B)},
         {UINT64_C(0x0005F117AC96FFD5), UINT64_C(0x000726340518125B),
          UINT64_C(0x0000B4C646458726), UINT64_C(0x00030611FCE4A03F),
          UINT64_C(0x00024DA1C03F5846)}},
        {{UINT64_C(0x000A7E110A8A2315), UINT64_C(0x0007B5EFABE2F3DF),
          UINT64_C(0x0006DCE7CEBCDB02), UINT64_C(0x00058F4E8DC9C7E1),
          UINT64_C(0x0007EF25DE94461B)},
         {UINT64_C(0x000A9AD49C0F6E54), UINT64_C(0x0003CBF884AA9599),
          UINT64_C(0x0006A88C60115D4E), UINT64_C(0x0002C8D6CB6A4B63),
          UINT64_C(0x0005526619B4F14F)}},
        {{UINT64_C(0x000A7E10BA3F6E66), UINT64_C(0x0004F52AE281A0EC),
          UINT64_C(0x0003476B97096769), UINT64_C(0x00011AD428A9FA42),
          UINT64_C(0x0002B4544F28575F)},
         {UINT64_C(0x0006446F85BE14E9), UINT64_C(0x0001E6FBEEDFAD52),
          UINT64_C(0x000581F0641BDAA7), UINT64_C(0x0006637991AE7F02),
          UINT64_C(0x00027E73BCC450A7)}},
        {{UINT64_C(0x0004837DF05B910E), UINT64_C(0x0002779AC207231D),
          UINT64_C(0x0005394BB62F9685), UINT64_C(0x0006AC7DB912D183),
          UINT64_C(0x0006DB2D56A1A285)},
         {UINT64_C(0x00050EB1AA8E171C), UINT64_C(0x0006CB734D7871D7),
          UINT64_C(0x0003489E3A49926A), UINT64_C(0x0007A7AF3C7269B9),
          UINT64_C(0x000506FB61E5D9D0)}},
        {{UINT64_C(0x000D3E9FBCB8E76E), UINT64_C(0x0006BC0CE3973127),
          UINT64_C(0x0003FEA5962A9166), UINT64_C(0x00010E5CB8B98D3B),
          UINT64_C(0x0003D71F40DFE3FD)},
         {UINT64_C(0x00066B6980E59640), UINT64_C(0x0004772E1BF69616),
          UINT64_C(0x000382F325F1D096), UINT64_C(0x0000E79B55D3B181),
          UINT64_C(0x0000E77D96C328F4)}},
        {{UINT64_C(0x000349B94216387D), UINT64_C(0x0004B7A80220D9FD),
          UINT64_C(0x0006912F408CE0C6), UINT64_C(0x000539E83F596047),
          UINT64_C(0x0004834100723280)},
         {UINT64_C(0x000C9832C2F9A7EC), UINT64_C(0x00048534033E2CC1),
          UINT64_C(0x00052157F1129FA5), UINT64_C(0x00032B2C4930BA32),
          UINT64_C(0x0007D34399534663)}},
        {{UINT64_C(0x000C589FC644CABA), UINT64_C(0x0003362B2512BAEF),
          UINT64_C(0x0000746159FCE3F0), UINT64_C(0x00040AAF0341039E),
          UINT64_C(0x0006BAFD459922FC)},
         {UINT64_C(0x000A089C7902EF38), UINT64_C(0x0007C692F2F9CE42),
          UINT64_C(0x00026F314BA444B4), UINT64_C(0x00070534A8C26791),
          UINT64_C(0x0006A5CF6D52A467)}},
        {{UINT64_C(0x00015CB92041529B), UINT64_C(0x00049F22868AF3D0),
          UINT64_C(0x0005F3105D324FC9), UINT64_C(0x0001AB468A5313F8),
          UINT64_C(0x00073BCEBFF704D3)},
         {UINT64_C(0x000CFCBCD02F5CBC), UINT64_C(0x000766C24804981A),
          UINT64_C(0x000181E80B278C77), UINT64_C(0x0006CB8FBE114DA9),
          UINT64_C(0x00057C201DD17423)}},
        {{UINT64_C(0x00047BCA3EF2D278), UINT64_C(0x00009EAE1A7C039D),
          UINT64_C(0x0001EEDA0A098A7E), UINT64_C(0x0003B5B1C05F89EE),
          UINT64_C(0x0005D3332A3A6013)},
         {UINT64_C(0x0006490FECA06CF9), UINT64_C(0x0003EE7D19213955),
          UINT64_C(0x0000A5B59824F8E4), UINT64_C(0x0004F3FDCFB627E2),
          UINT64_C(0x00056F87225E3ED3)}},
        {{UINT64_C(0x00001B52B2B0F1BD), UINT64_C(0x0001AAD95A0BF6D7),
          UINT64_C(0x000529216F79E19E), UINT64_C(0x00014CF4A2BF207B),
          UINT64_C(0x000236F709D78288)},
         {UINT64_C(0x000EED40ABFA1D8C), UINT64_C(0x0006115A9FA586E3),
          UINT64_C(0x0005AE4248864CF2), UINT64_C(0x000343CBDCFD5D69),
          UINT64_C(0x000055F0CDAC28BE)}},
        {{UINT64_C(0x000094B3C9C54CAB), UINT64_C(0x00034BE83CF2DCAA),
          UINT64_C(0x000798965804267E), UINT64_C(0x0001968C2A055D56),
          UINT64_C(0x000729AD776CB05C)},
         {UINT64_C(0x0008022B51AE8A66), UINT64_C(0x0004CB32422B0432),
          UINT64_C(0x00069790787ABB62), UINT64_C(0x0002F124051391FC),
          UINT64_C(0x0003C38C28CBE53C)}},
    }};

/*-
 * Q := 2P, both projective, Q and P same pointers OK
 * Autogenerated: op3/dbl_proj.op3
 * https://eprint.iacr.org/2015/1060 Alg 6
 * ASSERT: a = -3
 */
static void point_double(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X = P->X;
    const limb_t *Y = P->Y;
    const limb_t *Z = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t0, X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t1, Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t2, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, X, Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, Y, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, X, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, X3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t2, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t2, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, Z3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, Z3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, t0, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t4, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t0, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
}

/*-
 * out1 = (arg1 == 0) ? 0 : nz
 * NB: this is not a "mod p equiv" 0, but literal 0
 * NB: this is not a real Fiat function, just named that way for consistency.
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_nonzero(
    limb_t *out1, const fe_t arg1) {
    limb_t x1 = 0;
    int i;

    for (i = 0; i < LIMB_CNT; i++) x1 |= arg1[i];
    *out1 = x1;
}

/*-
 * R := Q + P where R and Q are projective, P affine.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_mixed.op3
 * https://eprint.iacr.org/2015/1060 Alg 5
 * ASSERT: a = -3
 */
static void point_add_mixed(pt_prj_t *R, const pt_prj_t *Q, const pt_aff_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    fe_t X3;
    fe_t Y3;
    fe_t Z3;
    limb_t nz;

    /* check P for affine inf */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_nonzero(&nz, P->Y);

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, X1, X2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, Y1, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, X2, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, X1, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, Y2, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t4, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X2, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, X1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, X3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Z1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t1, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t4, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, t0, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, t3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t4, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t1);

    /* if P is inf, throw all that away and take Q */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->X, nz, Q->X, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->Y, nz, Q->Y, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->Z, nz, Q->Z, Z3);
}

/*-
 * R := Q + P all projective.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_proj.op3
 * https://eprint.iacr.org/2015/1060 Alg 4
 * ASSERT: a = -3
 */
static void point_add_proj(pt_prj_t *R, const pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4, t5;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *Z2 = P->Z;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, X1, X2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, Y1, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, Z1, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, X1, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, X2, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, Y1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t5, Y2, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, t4, t5);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t5, t1, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t4, t4, t5);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, X2, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, X3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t2, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t1, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t1, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t4, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, t0, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, t3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t4, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t1);
}

/* constants */
#define RADIX 5
#define DRADIX (1 << RADIX)
#define DRADIX_WNAF ((DRADIX) << 1)

/*-
 * precomp for wnaf scalar multiplication:
 * precomp[0] = 1P
 * precomp[1] = 3P
 * precomp[2] = 5P
 * precomp[3] = 7P
 * precomp[4] = 9P
 * ...
 */
static void precomp_wnaf(pt_prj_t precomp[DRADIX / 2], const pt_aff_t *P) {
    int i;

    fe_copy(precomp[0].X, P->X);
    fe_copy(precomp[0].Y, P->Y);
    fe_copy(precomp[0].Z, const_one);
    point_double(&precomp[DRADIX / 2 - 1], &precomp[0]);

    for (i = 1; i < DRADIX / 2; i++)
        point_add_proj(&precomp[i], &precomp[DRADIX / 2 - 1], &precomp[i - 1]);
}

/* fetch a scalar bit */
static int scalar_get_bit(const unsigned char in[32], int idx) {
    int widx, rshift;

    widx = idx >> 3;
    rshift = idx & 0x7;

    if (idx < 0 || widx >= 32) return 0;

    return (in[widx] >> rshift) & 0x1;
}

/*-
 * Compute "regular" wnaf representation of a scalar.
 * See "Exponent Recoding and Regular Exponentiation Algorithms",
 * Tunstall et al., AfricaCrypt 2009, Alg 6.
 * It forces an odd scalar and outputs digits in
 * {\pm 1, \pm 3, \pm 5, \pm 7, \pm 9, ...}
 * i.e. signed odd digits with _no zeroes_ -- that makes it "regular".
 */
static void scalar_rwnaf(int8_t out[52], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = (in[0] & (DRADIX_WNAF - 1)) | 1;
    for (i = 0; i < 51; i++) {
        d = (window & (DRADIX_WNAF - 1)) - DRADIX;
        out[i] = d;
        window = (window - d) >> RADIX;
        window += scalar_get_bit(in, (i + 1) * RADIX + 1) << 1;
        window += scalar_get_bit(in, (i + 1) * RADIX + 2) << 2;
        window += scalar_get_bit(in, (i + 1) * RADIX + 3) << 3;
        window += scalar_get_bit(in, (i + 1) * RADIX + 4) << 4;
        window += scalar_get_bit(in, (i + 1) * RADIX + 5) << 5;
    }
    out[i] = window;
}

/*-
 * Compute "textbook" wnaf representation of a scalar.
 * NB: not constant time
 */
static void scalar_wnaf(int8_t out[257], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = in[0] & (DRADIX_WNAF - 1);
    for (i = 0; i < 257; i++) {
        d = 0;
        if ((window & 1) && ((d = window & (DRADIX_WNAF - 1)) & DRADIX))
            d -= DRADIX_WNAF;
        out[i] = d;
        window = (window - d) >> 1;
        window += scalar_get_bit(in, i + 1 + RADIX) << RADIX;
    }
}

/*-
 * Simultaneous scalar multiplication: interleaved "textbook" wnaf.
 * NB: not constant time
 */
static void var_smul_wnaf_two(pt_aff_t *out, const unsigned char a[32],
                              const unsigned char b[32], const pt_aff_t *P) {
    int i, d, is_neg, is_inf = 1, flipped = 0;
    int8_t anaf[257] = {0};
    int8_t bnaf[257] = {0};
    pt_prj_t Q = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_wnaf(anaf, a);
    scalar_wnaf(bnaf, b);

    for (i = 256; i >= 0; i--) {
        if (!is_inf) point_double(&Q, &Q);
        if ((d = bnaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &precomp[d].X);
                fe_copy(Q.Y, &precomp[d].Y);
                fe_copy(Q.Z, &precomp[d].Z);
                is_inf = 0;
            } else
                point_add_proj(&Q, &Q, &precomp[d]);
        }
        if ((d = anaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &lut_cmb[0][d].X);
                fe_copy(Q.Y, &lut_cmb[0][d].Y);
                fe_copy(Q.Z, const_one);
                is_inf = 0;
            } else
                point_add_mixed(&Q, &Q, &lut_cmb[0][d]);
        }
    }

    if (is_inf) {
        /* initialize accumulator to inf: all-zero scalars */
        fe_set_zero(Q.X);
        fe_copy(Q.Y, const_one);
        fe_set_zero(Q.Z);
    }

    if (flipped) {
        /* correct sign */
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
    }

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Variable point scalar multiplication with "regular" wnaf.
 */
static void var_smul_rwnaf(pt_aff_t *out, const unsigned char scalar[32],
                           const pt_aff_t *P) {
    int i, j, d, diff, is_neg;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, lut = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_rwnaf(rnaf, scalar);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    /* initialize accumulator to high digit */
    d = (rnaf[51] - 1) >> 1;
    for (j = 0; j < DRADIX / 2; j++) {
        diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, diff, Q.X,
                                                              precomp[j].X);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, diff, Q.Y,
                                                              precomp[j].Y);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, diff, Q.Z,
                                                              precomp[j].Z);
    }

    for (i = 50; i >= 0; i--) {
        for (j = 0; j < RADIX; j++) point_double(&Q, &Q);
        d = rnaf[i];
        /* is_neg = (d < 0) ? 1 : 0 */
        is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
        /* d = abs(d) */
        d = (d ^ -is_neg) + is_neg;
        d = (d - 1) >> 1;
        for (j = 0; j < DRADIX / 2; j++) {
            diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.X, diff, lut.X, precomp[j].X);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Y, diff, lut.Y, precomp[j].Y);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Z, diff, lut.Z, precomp[j].Z);
        }
        /* negate lut point if digit is negative */
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(out->Y, lut.Y);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(lut.Y, is_neg,
                                                              lut.Y, out->Y);
        point_add_proj(&Q, &Q, &lut);
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fe_copy(lut.X, precomp[0].X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(lut.Y, precomp[0].Y);
    fe_copy(lut.Z, precomp[0].Z);
    point_add_proj(&lut, &lut, &Q);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, scalar[0] & 1,
                                                          lut.X, Q.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, scalar[0] & 1,
                                                          lut.Y, Q.Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, scalar[0] & 1,
                                                          lut.Z, Q.Z);

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Fixed scalar multiplication: comb with interleaving.
 */
static void fixed_smul_cmb(pt_aff_t *out, const unsigned char scalar[32]) {
    int i, j, k, d, diff, is_neg = 0;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, R = {0};
    pt_aff_t lut = {0};

    scalar_rwnaf(rnaf, scalar);

    /* initalize accumulator to inf */
    fe_set_zero(Q.X);
    fe_copy(Q.Y, const_one);
    fe_set_zero(Q.Z);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    for (i = 2; i >= 0; i--) {
        for (j = 0; i != 2 && j < RADIX; j++) point_double(&Q, &Q);
        for (j = 0; j < 19; j++) {
            if (j * 3 + i > 51) continue;
            d = rnaf[j * 3 + i];
            /* is_neg = (d < 0) ? 1 : 0 */
            is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
            /* d = abs(d) */
            d = (d ^ -is_neg) + is_neg;
            d = (d - 1) >> 1;
            for (k = 0; k < DRADIX / 2; k++) {
                diff = (1 - (-(d ^ k) >> (8 * sizeof(int) - 1))) & 1;
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                    lut.X, diff, lut.X, lut_cmb[j][k].X);
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                    lut.Y, diff, lut.Y, lut_cmb[j][k].Y);
            }
            /* negate lut point if digit is negative */
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(out->Y, lut.Y);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Y, is_neg, lut.Y, out->Y);
            point_add_mixed(&Q, &Q, &lut);
        }
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fe_copy(lut.X, lut_cmb[0][0].X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(lut.Y, lut_cmb[0][0].Y);
    point_add_mixed(&R, &Q, &lut);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, scalar[0] & 1,
                                                          R.X, Q.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, scalar[0] & 1,
                                                          R.Y, Q.Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, scalar[0] & 1,
                                                          R.Z, Q.Z);

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Wrapper: simultaneous scalar mutiplication.
 * outx, outy := a * G + b * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul_two(unsigned char outx[32], unsigned char outy[32],
                          const unsigned char a[32], const unsigned char b[32],
                          const unsigned char inx[32],
                          const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.X, inx);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.Y, iny);
    /* simultaneous scalar multiplication */
    var_smul_wnaf_two(&P, a, b, &P);

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: fixed scalar mutiplication.
 * outx, outy := scalar * G
 * Everything is LE byte ordering.
 */
static void point_mul_g(unsigned char outx[32], unsigned char outy[32],
                        const unsigned char scalar[32]) {
    pt_aff_t P;

    /* fixed scmul function */
    fixed_smul_cmb(&P, scalar);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: variable point scalar mutiplication.
 * outx, outy := scalar * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul(unsigned char outx[32], unsigned char outy[32],
                      const unsigned char scalar[32],
                      const unsigned char inx[32],
                      const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.X, inx);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.Y, iny);
    /* var scmul function */
    var_smul_rwnaf(&P, scalar, &P);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}


#include <openssl/ec.h>

/* the zero field element */
static const unsigned char const_zb[32] = {0};

/*-
 * An OpenSSL wrapper for simultaneous scalar multiplication.
 * r := n * G + m * q
 */
    int
    point_mul_two_id_GostR3410_2001_CryptoPro_A_ParamSet(
        const EC_GROUP *group, EC_POINT *r, const BIGNUM *n, const EC_POINT *q,
        const BIGNUM *m, BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(n, b_n, 32) != 32 || BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the simultaneous scalar multiplication */
    point_mul_two(b_x, b_y, b_n, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for variable point scalar multiplication.
 * r := m * q
 */
    int
    point_mul_id_GostR3410_2001_CryptoPro_A_ParamSet(const EC_GROUP *group,
                                                     EC_POINT *r,
                                                     const EC_POINT *q,
                                                     const BIGNUM *m,
                                                     BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the variable scalar multiplication */
    point_mul(b_x, b_y, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for fixed scalar multiplication.
 * r := n * G
 */
    int
    point_mul_g_id_GostR3410_2001_CryptoPro_A_ParamSet(const EC_GROUP *group,
                                                       EC_POINT *r,
                                                       const BIGNUM *n,
                                                       BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL || BN_bn2lebinpad(n, b_n, 32) != 32)
        goto err;
    /* do the fixed scalar multiplication */
    point_mul_g(b_x, b_y, b_n);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}



#else /* __SIZEOF_INT128__ */

#include <stdint.h>
#include <string.h>
#define LIMB_BITS 32
#define LIMB_CNT 11
/* Field elements */
typedef uint32_t fe_t[LIMB_CNT];
typedef uint32_t limb_t;

#ifdef OPENSSL_NO_ASM
#define FIAT_ID_GOSTR3410_2001_CRYPTOPRO_A_PARAMSET_NO_ASM
#endif

#define fe_copy(d, s) memcpy(d, s, sizeof(fe_t))
#define fe_set_zero(d) memset(d, 0, sizeof(fe_t))

#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(c, a, b) \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add(c, a, b);          \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(c, c)
#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(c, a, b) \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub(c, a, b);          \
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(c, c)

/* Projective points */
typedef struct {
    fe_t X;
    fe_t Y;
    fe_t Z;
} pt_prj_t;

/* Affine points */
typedef struct {
    fe_t X;
    fe_t Y;
} pt_aff_t;

/* BEGIN verbatim fiat code https://github.com/mit-plv/fiat-crypto */
/*-
 * MIT License
 *
 * Copyright (c) 2020 the fiat-crypto authors (see the AUTHORS file)
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/* Autogenerated: unsaturated_solinas --static --use-value-barrier id_GostR3410_2001_CryptoPro_A_ParamSet 32 '(auto)' '2^256 - 617' */
/* curve description: id_GostR3410_2001_CryptoPro_A_ParamSet */
/* machine_wordsize = 32 (from "32") */
/* requested operations: (all) */
/* n = 11 (from "(auto)") */
/* s-c = 2^256 - [(1, 617)] (from "2^256 - 617") */
/* tight_bounds_multiplier = 1 (from "") */
/*  */
/* Computed values: */
/* carry_chain = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, 1] */
/* eval z = z[0] + (z[1] << 24) + (z[2] << 47) + (z[3] << 70) + (z[4] << 94) + (z[5] << 117) + (z[6] << 140) + (z[7] << 163) + (z[8] << 187) + (z[9] << 210) + (z[10] << 233) */
/* bytes_eval z = z[0] + (z[1] << 8) + (z[2] << 16) + (z[3] << 24) + (z[4] << 32) + (z[5] << 40) + (z[6] << 48) + (z[7] << 56) + (z[8] << 64) + (z[9] << 72) + (z[10] << 80) + (z[11] << 88) + (z[12] << 96) + (z[13] << 104) + (z[14] << 112) + (z[15] << 120) + (z[16] << 128) + (z[17] << 136) + (z[18] << 144) + (z[19] << 152) + (z[20] << 160) + (z[21] << 168) + (z[22] << 176) + (z[23] << 184) + (z[24] << 192) + (z[25] << 200) + (z[26] << 208) + (z[27] << 216) + (z[28] << 224) + (z[29] << 232) + (z[30] << 240) + (z[31] << 248) */
/* balance = [0x1fffb2e, 0xfffffe, 0xfffffe, 0x1fffffe, 0xfffffe, 0xfffffe, 0xfffffe, 0x1fffffe, 0xfffffe, 0xfffffe, 0xfffffe] */

#include <stdint.h>
typedef unsigned char fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1;
typedef signed char fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1;

#if (-1 & 3) != 3
#error "This code only works on a two's complement system"
#endif

#if !defined(FIAT_ID_GOSTR3410_2001_CRYPTOPRO_A_PARAMSET_NO_ASM) && \
    (defined(__GNUC__) || defined(__clang__))
static __inline__ uint32_t
fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u32(uint32_t a) {
    __asm__("" : "+r"(a) : /* no inputs */);
    return a;
}
#else
#define fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u32(x) (x)
#endif

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u24 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^24
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^24⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffff]
 *   arg3: [0x0 ~> 0xffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u24(
    uint32_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    uint32_t x1;
    uint32_t x2;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT32_C(0xffffff));
    x3 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x1 >> 24);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u24 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^24
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^24⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffff]
 *   arg3: [0x0 ~> 0xffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u24(
    uint32_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    int32_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1 x2;
    uint32_t x3;
    x1 = ((int32_t)(arg2 - arg1) - (int32_t)arg3);
    x2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(x1 >> 24);
    x3 = (x1 & UINT32_C(0xffffff));
    *out1 = x3;
    *out2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23 is an addition with carry.
 * Postconditions:
 *   out1 = (arg1 + arg2 + arg3) mod 2^23
 *   out2 = ⌊(arg1 + arg2 + arg3) / 2^23⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7fffff]
 *   arg3: [0x0 ~> 0x7fffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7fffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
    uint32_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    uint32_t x1;
    uint32_t x2;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x3;
    x1 = ((arg1 + arg2) + arg3);
    x2 = (x1 & UINT32_C(0x7fffff));
    x3 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x1 >> 23);
    *out1 = x2;
    *out2 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23 is a subtraction with borrow.
 * Postconditions:
 *   out1 = (-arg1 + arg2 + -arg3) mod 2^23
 *   out2 = -⌊(-arg1 + arg2 + -arg3) / 2^23⌋
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0x7fffff]
 *   arg3: [0x0 ~> 0x7fffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0x7fffff]
 *   out2: [0x0 ~> 0x1]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
    uint32_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 *out2,
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1, uint32_t arg2,
    uint32_t arg3) {
    int32_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1 x2;
    uint32_t x3;
    x1 = ((int32_t)(arg2 - arg1) - (int32_t)arg3);
    x2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(x1 >> 23);
    x3 = (x1 & UINT32_C(0x7fffff));
    *out1 = x3;
    *out2 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(0x0 - x2);
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32 is a single-word conditional move.
 * Postconditions:
 *   out1 = (if arg1 = 0 then arg2 else arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [0x0 ~> 0xffffffff]
 *   arg3: [0x0 ~> 0xffffffff]
 * Output Bounds:
 *   out1: [0x0 ~> 0xffffffff]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
    uint32_t *out1, fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1,
    uint32_t arg2, uint32_t arg3) {
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x1;
    uint32_t x2;
    uint32_t x3;
    x1 = (!(!arg1));
    x2 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_int1)(0x0 - x1) &
          UINT32_C(0xffffffff));
    x3 =
        ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u32(x2) &
          arg3) |
         (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_value_barrier_u32((~x2)) &
          arg2));
    *out1 = x3;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul multiplies two field elements and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 *   arg2: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint64_t x1;
    uint64_t x2;
    uint64_t x3;
    uint64_t x4;
    uint64_t x5;
    uint64_t x6;
    uint64_t x7;
    uint64_t x8;
    uint64_t x9;
    uint64_t x10;
    uint64_t x11;
    uint64_t x12;
    uint64_t x13;
    uint64_t x14;
    uint64_t x15;
    uint64_t x16;
    uint64_t x17;
    uint64_t x18;
    uint64_t x19;
    uint64_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint64_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint64_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint64_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint64_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    uint64_t x72;
    uint64_t x73;
    uint64_t x74;
    uint64_t x75;
    uint64_t x76;
    uint64_t x77;
    uint64_t x78;
    uint64_t x79;
    uint64_t x80;
    uint64_t x81;
    uint64_t x82;
    uint64_t x83;
    uint64_t x84;
    uint64_t x85;
    uint64_t x86;
    uint64_t x87;
    uint64_t x88;
    uint64_t x89;
    uint64_t x90;
    uint64_t x91;
    uint64_t x92;
    uint64_t x93;
    uint64_t x94;
    uint64_t x95;
    uint64_t x96;
    uint64_t x97;
    uint64_t x98;
    uint64_t x99;
    uint64_t x100;
    uint64_t x101;
    uint64_t x102;
    uint64_t x103;
    uint64_t x104;
    uint64_t x105;
    uint64_t x106;
    uint64_t x107;
    uint64_t x108;
    uint64_t x109;
    uint64_t x110;
    uint64_t x111;
    uint64_t x112;
    uint64_t x113;
    uint64_t x114;
    uint64_t x115;
    uint64_t x116;
    uint64_t x117;
    uint64_t x118;
    uint64_t x119;
    uint64_t x120;
    uint64_t x121;
    uint64_t x122;
    uint64_t x123;
    uint32_t x124;
    uint64_t x125;
    uint64_t x126;
    uint64_t x127;
    uint64_t x128;
    uint64_t x129;
    uint64_t x130;
    uint64_t x131;
    uint64_t x132;
    uint64_t x133;
    uint64_t x134;
    uint64_t x135;
    uint64_t x136;
    uint32_t x137;
    uint64_t x138;
    uint64_t x139;
    uint32_t x140;
    uint64_t x141;
    uint64_t x142;
    uint32_t x143;
    uint64_t x144;
    uint64_t x145;
    uint32_t x146;
    uint64_t x147;
    uint64_t x148;
    uint32_t x149;
    uint64_t x150;
    uint64_t x151;
    uint32_t x152;
    uint64_t x153;
    uint64_t x154;
    uint32_t x155;
    uint64_t x156;
    uint64_t x157;
    uint32_t x158;
    uint64_t x159;
    uint64_t x160;
    uint32_t x161;
    uint64_t x162;
    uint32_t x163;
    uint32_t x164;
    uint64_t x165;
    uint64_t x166;
    uint32_t x167;
    uint32_t x168;
    uint32_t x169;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x170;
    uint32_t x171;
    uint32_t x172;
    x1 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[10])));
    x2 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[9])));
    x3 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[8])) * 0x2));
    x4 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[7])));
    x5 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[6])));
    x6 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[5])));
    x7 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[4])) * 0x2));
    x8 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[3])));
    x9 = (UINT16_C(0x269) * ((uint64_t)(arg1[10]) * (arg2[2])));
    x10 = (UINT16_C(0x269) * (((uint64_t)(arg1[10]) * (arg2[1])) * 0x2));
    x11 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[10])));
    x12 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[9])) * 0x2));
    x13 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[8])) * 0x2));
    x14 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[7])));
    x15 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[6])));
    x16 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[5])) * 0x2));
    x17 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[4])) * 0x2));
    x18 = (UINT16_C(0x269) * ((uint64_t)(arg1[9]) * (arg2[3])));
    x19 = (UINT16_C(0x269) * (((uint64_t)(arg1[9]) * (arg2[2])) * 0x2));
    x20 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[10])) * 0x2));
    x21 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[9])) * 0x2));
    x22 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[8])) * 0x2));
    x23 = (UINT16_C(0x269) * ((uint64_t)(arg1[8]) * (arg2[7])));
    x24 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[6])) * 0x2));
    x25 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[5])) * 0x2));
    x26 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[4])) * 0x2));
    x27 = (UINT16_C(0x269) * (((uint64_t)(arg1[8]) * (arg2[3])) * 0x2));
    x28 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[10])));
    x29 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[9])));
    x30 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[8])));
    x31 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[7])));
    x32 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[6])));
    x33 = (UINT16_C(0x269) * ((uint64_t)(arg1[7]) * (arg2[5])));
    x34 = (UINT16_C(0x269) * (((uint64_t)(arg1[7]) * (arg2[4])) * 0x2));
    x35 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[10])));
    x36 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[9])));
    x37 = (UINT16_C(0x269) * (((uint64_t)(arg1[6]) * (arg2[8])) * 0x2));
    x38 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[7])));
    x39 = (UINT16_C(0x269) * ((uint64_t)(arg1[6]) * (arg2[6])));
    x40 = (UINT16_C(0x269) * (((uint64_t)(arg1[6]) * (arg2[5])) * 0x2));
    x41 = (UINT16_C(0x269) * ((uint64_t)(arg1[5]) * (arg2[10])));
    x42 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[9])) * 0x2));
    x43 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[8])) * 0x2));
    x44 = (UINT16_C(0x269) * ((uint64_t)(arg1[5]) * (arg2[7])));
    x45 = (UINT16_C(0x269) * (((uint64_t)(arg1[5]) * (arg2[6])) * 0x2));
    x46 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[10])) * 0x2));
    x47 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[9])) * 0x2));
    x48 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[8])) * 0x2));
    x49 = (UINT16_C(0x269) * (((uint64_t)(arg1[4]) * (arg2[7])) * 0x2));
    x50 = (UINT16_C(0x269) * ((uint64_t)(arg1[3]) * (arg2[10])));
    x51 = (UINT16_C(0x269) * ((uint64_t)(arg1[3]) * (arg2[9])));
    x52 = (UINT16_C(0x269) * (((uint64_t)(arg1[3]) * (arg2[8])) * 0x2));
    x53 = (UINT16_C(0x269) * ((uint64_t)(arg1[2]) * (arg2[10])));
    x54 = (UINT16_C(0x269) * (((uint64_t)(arg1[2]) * (arg2[9])) * 0x2));
    x55 = (UINT16_C(0x269) * (((uint64_t)(arg1[1]) * (arg2[10])) * 0x2));
    x56 = ((uint64_t)(arg1[10]) * (arg2[0]));
    x57 = ((uint64_t)(arg1[9]) * ((arg2[1]) * 0x2));
    x58 = ((uint64_t)(arg1[9]) * (arg2[0]));
    x59 = ((uint64_t)(arg1[8]) * ((arg2[2]) * 0x2));
    x60 = ((uint64_t)(arg1[8]) * ((arg2[1]) * 0x2));
    x61 = ((uint64_t)(arg1[8]) * (arg2[0]));
    x62 = ((uint64_t)(arg1[7]) * (arg2[3]));
    x63 = ((uint64_t)(arg1[7]) * (arg2[2]));
    x64 = ((uint64_t)(arg1[7]) * (arg2[1]));
    x65 = ((uint64_t)(arg1[7]) * (arg2[0]));
    x66 = ((uint64_t)(arg1[6]) * ((arg2[4]) * 0x2));
    x67 = ((uint64_t)(arg1[6]) * (arg2[3]));
    x68 = ((uint64_t)(arg1[6]) * (arg2[2]));
    x69 = ((uint64_t)(arg1[6]) * ((arg2[1]) * 0x2));
    x70 = ((uint64_t)(arg1[6]) * (arg2[0]));
    x71 = ((uint64_t)(arg1[5]) * ((arg2[5]) * 0x2));
    x72 = ((uint64_t)(arg1[5]) * ((arg2[4]) * 0x2));
    x73 = ((uint64_t)(arg1[5]) * (arg2[3]));
    x74 = ((uint64_t)(arg1[5]) * ((arg2[2]) * 0x2));
    x75 = ((uint64_t)(arg1[5]) * ((arg2[1]) * 0x2));
    x76 = ((uint64_t)(arg1[5]) * (arg2[0]));
    x77 = ((uint64_t)(arg1[4]) * ((arg2[6]) * 0x2));
    x78 = ((uint64_t)(arg1[4]) * ((arg2[5]) * 0x2));
    x79 = ((uint64_t)(arg1[4]) * ((arg2[4]) * 0x2));
    x80 = ((uint64_t)(arg1[4]) * ((arg2[3]) * 0x2));
    x81 = ((uint64_t)(arg1[4]) * ((arg2[2]) * 0x2));
    x82 = ((uint64_t)(arg1[4]) * ((arg2[1]) * 0x2));
    x83 = ((uint64_t)(arg1[4]) * (arg2[0]));
    x84 = ((uint64_t)(arg1[3]) * (arg2[7]));
    x85 = ((uint64_t)(arg1[3]) * (arg2[6]));
    x86 = ((uint64_t)(arg1[3]) * (arg2[5]));
    x87 = ((uint64_t)(arg1[3]) * ((arg2[4]) * 0x2));
    x88 = ((uint64_t)(arg1[3]) * (arg2[3]));
    x89 = ((uint64_t)(arg1[3]) * (arg2[2]));
    x90 = ((uint64_t)(arg1[3]) * (arg2[1]));
    x91 = ((uint64_t)(arg1[3]) * (arg2[0]));
    x92 = ((uint64_t)(arg1[2]) * ((arg2[8]) * 0x2));
    x93 = ((uint64_t)(arg1[2]) * (arg2[7]));
    x94 = ((uint64_t)(arg1[2]) * (arg2[6]));
    x95 = ((uint64_t)(arg1[2]) * ((arg2[5]) * 0x2));
    x96 = ((uint64_t)(arg1[2]) * ((arg2[4]) * 0x2));
    x97 = ((uint64_t)(arg1[2]) * (arg2[3]));
    x98 = ((uint64_t)(arg1[2]) * (arg2[2]));
    x99 = ((uint64_t)(arg1[2]) * ((arg2[1]) * 0x2));
    x100 = ((uint64_t)(arg1[2]) * (arg2[0]));
    x101 = ((uint64_t)(arg1[1]) * ((arg2[9]) * 0x2));
    x102 = ((uint64_t)(arg1[1]) * ((arg2[8]) * 0x2));
    x103 = ((uint64_t)(arg1[1]) * (arg2[7]));
    x104 = ((uint64_t)(arg1[1]) * ((arg2[6]) * 0x2));
    x105 = ((uint64_t)(arg1[1]) * ((arg2[5]) * 0x2));
    x106 = ((uint64_t)(arg1[1]) * ((arg2[4]) * 0x2));
    x107 = ((uint64_t)(arg1[1]) * (arg2[3]));
    x108 = ((uint64_t)(arg1[1]) * ((arg2[2]) * 0x2));
    x109 = ((uint64_t)(arg1[1]) * ((arg2[1]) * 0x2));
    x110 = ((uint64_t)(arg1[1]) * (arg2[0]));
    x111 = ((uint64_t)(arg1[0]) * (arg2[10]));
    x112 = ((uint64_t)(arg1[0]) * (arg2[9]));
    x113 = ((uint64_t)(arg1[0]) * (arg2[8]));
    x114 = ((uint64_t)(arg1[0]) * (arg2[7]));
    x115 = ((uint64_t)(arg1[0]) * (arg2[6]));
    x116 = ((uint64_t)(arg1[0]) * (arg2[5]));
    x117 = ((uint64_t)(arg1[0]) * (arg2[4]));
    x118 = ((uint64_t)(arg1[0]) * (arg2[3]));
    x119 = ((uint64_t)(arg1[0]) * (arg2[2]));
    x120 = ((uint64_t)(arg1[0]) * (arg2[1]));
    x121 = ((uint64_t)(arg1[0]) * (arg2[0]));
    x122 =
        (x121 +
         (x55 +
          (x54 + (x52 + (x49 + (x45 + (x40 + (x34 + (x27 + (x19 + x10))))))))));
    x123 = (x122 >> 24);
    x124 = (uint32_t)(x122 & UINT32_C(0xffffff));
    x125 =
        (x111 +
         (x101 +
          (x92 + (x84 + (x77 + (x71 + (x66 + (x62 + (x59 + (x57 + x56))))))))));
    x126 =
        (x112 +
         (x102 +
          (x93 + (x85 + (x78 + (x72 + (x67 + (x63 + (x60 + (x58 + x1))))))))));
    x127 =
        (x113 +
         (x103 +
          (x94 + (x86 + (x79 + (x73 + (x68 + (x64 + (x61 + (x11 + x2))))))))));
    x128 =
        (x114 +
         (x104 +
          (x95 + (x87 + (x80 + (x74 + (x69 + (x65 + (x20 + (x12 + x3))))))))));
    x129 =
        (x115 +
         (x105 +
          (x96 + (x88 + (x81 + (x75 + (x70 + (x28 + (x21 + (x13 + x4))))))))));
    x130 =
        (x116 +
         (x106 +
          (x97 + (x89 + (x82 + (x76 + (x35 + (x29 + (x22 + (x14 + x5))))))))));
    x131 =
        (x117 +
         (x107 +
          (x98 + (x90 + (x83 + (x41 + (x36 + (x30 + (x23 + (x15 + x6))))))))));
    x132 =
        (x118 +
         (x108 +
          (x99 + (x91 + (x46 + (x42 + (x37 + (x31 + (x24 + (x16 + x7))))))))));
    x133 =
        (x119 +
         (x109 +
          (x100 + (x50 + (x47 + (x43 + (x38 + (x32 + (x25 + (x17 + x8))))))))));
    x134 =
        (x120 +
         (x110 +
          (x53 + (x51 + (x48 + (x44 + (x39 + (x33 + (x26 + (x18 + x9))))))))));
    x135 = (x123 + x134);
    x136 = (x135 >> 23);
    x137 = (uint32_t)(x135 & UINT32_C(0x7fffff));
    x138 = (x136 + x133);
    x139 = (x138 >> 23);
    x140 = (uint32_t)(x138 & UINT32_C(0x7fffff));
    x141 = (x139 + x132);
    x142 = (x141 >> 24);
    x143 = (uint32_t)(x141 & UINT32_C(0xffffff));
    x144 = (x142 + x131);
    x145 = (x144 >> 23);
    x146 = (uint32_t)(x144 & UINT32_C(0x7fffff));
    x147 = (x145 + x130);
    x148 = (x147 >> 23);
    x149 = (uint32_t)(x147 & UINT32_C(0x7fffff));
    x150 = (x148 + x129);
    x151 = (x150 >> 23);
    x152 = (uint32_t)(x150 & UINT32_C(0x7fffff));
    x153 = (x151 + x128);
    x154 = (x153 >> 24);
    x155 = (uint32_t)(x153 & UINT32_C(0xffffff));
    x156 = (x154 + x127);
    x157 = (x156 >> 23);
    x158 = (uint32_t)(x156 & UINT32_C(0x7fffff));
    x159 = (x157 + x126);
    x160 = (x159 >> 23);
    x161 = (uint32_t)(x159 & UINT32_C(0x7fffff));
    x162 = (x160 + x125);
    x163 = (uint32_t)(x162 >> 23);
    x164 = (uint32_t)(x162 & UINT32_C(0x7fffff));
    x165 = ((uint64_t)UINT16_C(0x269) * x163);
    x166 = (x124 + x165);
    x167 = (uint32_t)(x166 >> 24);
    x168 = (uint32_t)(x166 & UINT32_C(0xffffff));
    x169 = (x167 + x137);
    x170 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x169 >> 23);
    x171 = (x169 & UINT32_C(0x7fffff));
    x172 = (x170 + x140);
    out1[0] = x168;
    out1[1] = x171;
    out1[2] = x172;
    out1[3] = x143;
    out1[4] = x146;
    out1[5] = x149;
    out1[6] = x152;
    out1[7] = x155;
    out1[8] = x158;
    out1[9] = x161;
    out1[10] = x164;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square squares a field element and reduces the result.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 * eval arg1) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint64_t x1;
    uint64_t x2;
    uint32_t x3;
    uint64_t x4;
    uint64_t x5;
    uint32_t x6;
    uint64_t x7;
    uint64_t x8;
    uint32_t x9;
    uint64_t x10;
    uint64_t x11;
    uint32_t x12;
    uint64_t x13;
    uint64_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint64_t x21;
    uint64_t x22;
    uint64_t x23;
    uint64_t x24;
    uint64_t x25;
    uint64_t x26;
    uint64_t x27;
    uint64_t x28;
    uint64_t x29;
    uint64_t x30;
    uint64_t x31;
    uint64_t x32;
    uint64_t x33;
    uint64_t x34;
    uint64_t x35;
    uint64_t x36;
    uint64_t x37;
    uint64_t x38;
    uint64_t x39;
    uint64_t x40;
    uint64_t x41;
    uint64_t x42;
    uint64_t x43;
    uint64_t x44;
    uint64_t x45;
    uint64_t x46;
    uint64_t x47;
    uint64_t x48;
    uint64_t x49;
    uint64_t x50;
    uint64_t x51;
    uint64_t x52;
    uint64_t x53;
    uint64_t x54;
    uint64_t x55;
    uint64_t x56;
    uint64_t x57;
    uint64_t x58;
    uint64_t x59;
    uint64_t x60;
    uint64_t x61;
    uint64_t x62;
    uint64_t x63;
    uint64_t x64;
    uint64_t x65;
    uint64_t x66;
    uint64_t x67;
    uint64_t x68;
    uint64_t x69;
    uint64_t x70;
    uint64_t x71;
    uint64_t x72;
    uint64_t x73;
    uint64_t x74;
    uint64_t x75;
    uint64_t x76;
    uint64_t x77;
    uint64_t x78;
    uint64_t x79;
    uint64_t x80;
    uint64_t x81;
    uint64_t x82;
    uint64_t x83;
    uint64_t x84;
    uint64_t x85;
    uint64_t x86;
    uint64_t x87;
    uint64_t x88;
    uint32_t x89;
    uint64_t x90;
    uint64_t x91;
    uint64_t x92;
    uint64_t x93;
    uint64_t x94;
    uint64_t x95;
    uint64_t x96;
    uint64_t x97;
    uint64_t x98;
    uint64_t x99;
    uint64_t x100;
    uint64_t x101;
    uint32_t x102;
    uint64_t x103;
    uint64_t x104;
    uint32_t x105;
    uint64_t x106;
    uint64_t x107;
    uint32_t x108;
    uint64_t x109;
    uint64_t x110;
    uint32_t x111;
    uint64_t x112;
    uint64_t x113;
    uint32_t x114;
    uint64_t x115;
    uint64_t x116;
    uint32_t x117;
    uint64_t x118;
    uint64_t x119;
    uint32_t x120;
    uint64_t x121;
    uint64_t x122;
    uint32_t x123;
    uint64_t x124;
    uint64_t x125;
    uint32_t x126;
    uint64_t x127;
    uint32_t x128;
    uint32_t x129;
    uint64_t x130;
    uint64_t x131;
    uint32_t x132;
    uint32_t x133;
    uint32_t x134;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x135;
    uint32_t x136;
    uint32_t x137;
    x1 = ((uint64_t)(arg1[10]) * UINT16_C(0x269));
    x2 = (x1 * 0x2);
    x3 = ((arg1[10]) * 0x2);
    x4 = ((uint64_t)(arg1[9]) * UINT16_C(0x269));
    x5 = (x4 * 0x2);
    x6 = ((arg1[9]) * 0x2);
    x7 = ((uint64_t)(arg1[8]) * UINT16_C(0x269));
    x8 = (x7 * 0x2);
    x9 = ((arg1[8]) * 0x2);
    x10 = ((uint64_t)(arg1[7]) * UINT16_C(0x269));
    x11 = (x10 * 0x2);
    x12 = ((arg1[7]) * 0x2);
    x13 = ((uint64_t)(arg1[6]) * UINT16_C(0x269));
    x14 = (x13 * 0x2);
    x15 = ((arg1[6]) * 0x2);
    x16 = ((arg1[5]) * 0x2);
    x17 = ((arg1[4]) * 0x2);
    x18 = ((arg1[3]) * 0x2);
    x19 = ((arg1[2]) * 0x2);
    x20 = ((arg1[1]) * 0x2);
    x21 = ((arg1[10]) * x1);
    x22 = ((arg1[9]) * x2);
    x23 = ((arg1[9]) * (x4 * 0x2));
    x24 = ((arg1[8]) * (x2 * 0x2));
    x25 = ((arg1[8]) * (x5 * 0x2));
    x26 = ((arg1[8]) * (x7 * 0x2));
    x27 = ((arg1[7]) * x2);
    x28 = ((arg1[7]) * x5);
    x29 = ((arg1[7]) * x8);
    x30 = ((arg1[7]) * x10);
    x31 = ((arg1[6]) * x2);
    x32 = ((arg1[6]) * x5);
    x33 = ((arg1[6]) * (x8 * 0x2));
    x34 = ((arg1[6]) * x11);
    x35 = ((arg1[6]) * x13);
    x36 = ((arg1[5]) * x2);
    x37 = ((arg1[5]) * (x5 * 0x2));
    x38 = ((arg1[5]) * (x8 * 0x2));
    x39 = ((arg1[5]) * x11);
    x40 = ((arg1[5]) * (x14 * 0x2));
    x41 = ((uint64_t)(arg1[5]) * ((arg1[5]) * 0x2));
    x42 = ((arg1[4]) * (x2 * 0x2));
    x43 = ((arg1[4]) * (x5 * 0x2));
    x44 = ((arg1[4]) * (x8 * 0x2));
    x45 = ((arg1[4]) * (x11 * 0x2));
    x46 = ((uint64_t)(arg1[4]) * (x15 * 0x2));
    x47 = ((uint64_t)(arg1[4]) * (x16 * 0x2));
    x48 = ((uint64_t)(arg1[4]) * ((arg1[4]) * 0x2));
    x49 = ((arg1[3]) * x2);
    x50 = ((arg1[3]) * x5);
    x51 = ((arg1[3]) * (x8 * 0x2));
    x52 = ((uint64_t)(arg1[3]) * x12);
    x53 = ((uint64_t)(arg1[3]) * x15);
    x54 = ((uint64_t)(arg1[3]) * x16);
    x55 = ((uint64_t)(arg1[3]) * (x17 * 0x2));
    x56 = ((uint64_t)(arg1[3]) * (arg1[3]));
    x57 = ((arg1[2]) * x2);
    x58 = ((arg1[2]) * (x5 * 0x2));
    x59 = ((uint64_t)(arg1[2]) * (x9 * 0x2));
    x60 = ((uint64_t)(arg1[2]) * x12);
    x61 = ((uint64_t)(arg1[2]) * x15);
    x62 = ((uint64_t)(arg1[2]) * (x16 * 0x2));
    x63 = ((uint64_t)(arg1[2]) * (x17 * 0x2));
    x64 = ((uint64_t)(arg1[2]) * x18);
    x65 = ((uint64_t)(arg1[2]) * (arg1[2]));
    x66 = ((arg1[1]) * (x2 * 0x2));
    x67 = ((uint64_t)(arg1[1]) * (x6 * 0x2));
    x68 = ((uint64_t)(arg1[1]) * (x9 * 0x2));
    x69 = ((uint64_t)(arg1[1]) * x12);
    x70 = ((uint64_t)(arg1[1]) * (x15 * 0x2));
    x71 = ((uint64_t)(arg1[1]) * (x16 * 0x2));
    x72 = ((uint64_t)(arg1[1]) * (x17 * 0x2));
    x73 = ((uint64_t)(arg1[1]) * x18);
    x74 = ((uint64_t)(arg1[1]) * (x19 * 0x2));
    x75 = ((uint64_t)(arg1[1]) * ((arg1[1]) * 0x2));
    x76 = ((uint64_t)(arg1[0]) * x3);
    x77 = ((uint64_t)(arg1[0]) * x6);
    x78 = ((uint64_t)(arg1[0]) * x9);
    x79 = ((uint64_t)(arg1[0]) * x12);
    x80 = ((uint64_t)(arg1[0]) * x15);
    x81 = ((uint64_t)(arg1[0]) * x16);
    x82 = ((uint64_t)(arg1[0]) * x17);
    x83 = ((uint64_t)(arg1[0]) * x18);
    x84 = ((uint64_t)(arg1[0]) * x19);
    x85 = ((uint64_t)(arg1[0]) * x20);
    x86 = ((uint64_t)(arg1[0]) * (arg1[0]));
    x87 = (x86 + (x66 + (x58 + (x51 + (x45 + x40)))));
    x88 = (x87 >> 24);
    x89 = (uint32_t)(x87 & UINT32_C(0xffffff));
    x90 = (x76 + (x67 + (x59 + (x52 + (x46 + x41)))));
    x91 = (x77 + (x68 + (x60 + (x53 + (x47 + x21)))));
    x92 = (x78 + (x69 + (x61 + (x54 + (x48 + x22)))));
    x93 = (x79 + (x70 + (x62 + (x55 + (x24 + x23)))));
    x94 = (x80 + (x71 + (x63 + (x56 + (x27 + x25)))));
    x95 = (x81 + (x72 + (x64 + (x31 + (x28 + x26)))));
    x96 = (x82 + (x73 + (x65 + (x36 + (x32 + x29)))));
    x97 = (x83 + (x74 + (x42 + (x37 + (x33 + x30)))));
    x98 = (x84 + (x75 + (x49 + (x43 + (x38 + x34)))));
    x99 = (x85 + (x57 + (x50 + (x44 + (x39 + x35)))));
    x100 = (x88 + x99);
    x101 = (x100 >> 23);
    x102 = (uint32_t)(x100 & UINT32_C(0x7fffff));
    x103 = (x101 + x98);
    x104 = (x103 >> 23);
    x105 = (uint32_t)(x103 & UINT32_C(0x7fffff));
    x106 = (x104 + x97);
    x107 = (x106 >> 24);
    x108 = (uint32_t)(x106 & UINT32_C(0xffffff));
    x109 = (x107 + x96);
    x110 = (x109 >> 23);
    x111 = (uint32_t)(x109 & UINT32_C(0x7fffff));
    x112 = (x110 + x95);
    x113 = (x112 >> 23);
    x114 = (uint32_t)(x112 & UINT32_C(0x7fffff));
    x115 = (x113 + x94);
    x116 = (x115 >> 23);
    x117 = (uint32_t)(x115 & UINT32_C(0x7fffff));
    x118 = (x116 + x93);
    x119 = (x118 >> 24);
    x120 = (uint32_t)(x118 & UINT32_C(0xffffff));
    x121 = (x119 + x92);
    x122 = (x121 >> 23);
    x123 = (uint32_t)(x121 & UINT32_C(0x7fffff));
    x124 = (x122 + x91);
    x125 = (x124 >> 23);
    x126 = (uint32_t)(x124 & UINT32_C(0x7fffff));
    x127 = (x125 + x90);
    x128 = (uint32_t)(x127 >> 23);
    x129 = (uint32_t)(x127 & UINT32_C(0x7fffff));
    x130 = ((uint64_t)UINT16_C(0x269) * x128);
    x131 = (x89 + x130);
    x132 = (uint32_t)(x131 >> 24);
    x133 = (uint32_t)(x131 & UINT32_C(0xffffff));
    x134 = (x132 + x102);
    x135 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x134 >> 23);
    x136 = (x134 & UINT32_C(0x7fffff));
    x137 = (x135 + x105);
    out1[0] = x133;
    out1[1] = x136;
    out1[2] = x137;
    out1[3] = x108;
    out1[4] = x111;
    out1[5] = x114;
    out1[6] = x117;
    out1[7] = x120;
    out1[8] = x123;
    out1[9] = x126;
    out1[10] = x129;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry reduces a field element.
 * Postconditions:
 *   eval out1 mod m = eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    uint32_t x12;
    uint32_t x13;
    uint32_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint32_t x21;
    uint32_t x22;
    uint32_t x23;
    uint32_t x24;
    x1 = (arg1[0]);
    x2 = ((x1 >> 24) + (arg1[1]));
    x3 = ((x2 >> 23) + (arg1[2]));
    x4 = ((x3 >> 23) + (arg1[3]));
    x5 = ((x4 >> 24) + (arg1[4]));
    x6 = ((x5 >> 23) + (arg1[5]));
    x7 = ((x6 >> 23) + (arg1[6]));
    x8 = ((x7 >> 23) + (arg1[7]));
    x9 = ((x8 >> 24) + (arg1[8]));
    x10 = ((x9 >> 23) + (arg1[9]));
    x11 = ((x10 >> 23) + (arg1[10]));
    x12 = ((x1 & UINT32_C(0xffffff)) + (UINT16_C(0x269) * (x11 >> 23)));
    x13 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x12 >> 24) +
           (x2 & UINT32_C(0x7fffff)));
    x14 = (x12 & UINT32_C(0xffffff));
    x15 = (x13 & UINT32_C(0x7fffff));
    x16 = ((fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x13 >> 23) +
           (x3 & UINT32_C(0x7fffff)));
    x17 = (x4 & UINT32_C(0xffffff));
    x18 = (x5 & UINT32_C(0x7fffff));
    x19 = (x6 & UINT32_C(0x7fffff));
    x20 = (x7 & UINT32_C(0x7fffff));
    x21 = (x8 & UINT32_C(0xffffff));
    x22 = (x9 & UINT32_C(0x7fffff));
    x23 = (x10 & UINT32_C(0x7fffff));
    x24 = (x11 & UINT32_C(0x7fffff));
    out1[0] = x14;
    out1[1] = x15;
    out1[2] = x16;
    out1[3] = x17;
    out1[4] = x18;
    out1[5] = x19;
    out1[6] = x20;
    out1[7] = x21;
    out1[8] = x22;
    out1[9] = x23;
    out1[10] = x24;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add adds two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 + eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 *   arg2: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_add(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = ((arg1[0]) + (arg2[0]));
    x2 = ((arg1[1]) + (arg2[1]));
    x3 = ((arg1[2]) + (arg2[2]));
    x4 = ((arg1[3]) + (arg2[3]));
    x5 = ((arg1[4]) + (arg2[4]));
    x6 = ((arg1[5]) + (arg2[5]));
    x7 = ((arg1[6]) + (arg2[6]));
    x8 = ((arg1[7]) + (arg2[7]));
    x9 = ((arg1[8]) + (arg2[8]));
    x10 = ((arg1[9]) + (arg2[9]));
    x11 = ((arg1[10]) + (arg2[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub subtracts two field elements.
 * Postconditions:
 *   eval out1 mod m = (eval arg1 - eval arg2) mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 *   arg2: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_sub(
    uint32_t out1[11], const uint32_t arg1[11], const uint32_t arg2[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = ((UINT32_C(0x1fffb2e) + (arg1[0])) - (arg2[0]));
    x2 = ((UINT32_C(0xfffffe) + (arg1[1])) - (arg2[1]));
    x3 = ((UINT32_C(0xfffffe) + (arg1[2])) - (arg2[2]));
    x4 = ((UINT32_C(0x1fffffe) + (arg1[3])) - (arg2[3]));
    x5 = ((UINT32_C(0xfffffe) + (arg1[4])) - (arg2[4]));
    x6 = ((UINT32_C(0xfffffe) + (arg1[5])) - (arg2[5]));
    x7 = ((UINT32_C(0xfffffe) + (arg1[6])) - (arg2[6]));
    x8 = ((UINT32_C(0x1fffffe) + (arg1[7])) - (arg2[7]));
    x9 = ((UINT32_C(0xfffffe) + (arg1[8])) - (arg2[8]));
    x10 = ((UINT32_C(0xfffffe) + (arg1[9])) - (arg2[9]));
    x11 = ((UINT32_C(0xfffffe) + (arg1[10])) - (arg2[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp negates a field element.
 * Postconditions:
 *   eval out1 mod m = -eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x3000000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000], [0x0 ~> 0x1800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(
    uint32_t out1[11], const uint32_t arg1[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    x1 = (UINT32_C(0x1fffb2e) - (arg1[0]));
    x2 = (UINT32_C(0xfffffe) - (arg1[1]));
    x3 = (UINT32_C(0xfffffe) - (arg1[2]));
    x4 = (UINT32_C(0x1fffffe) - (arg1[3]));
    x5 = (UINT32_C(0xfffffe) - (arg1[4]));
    x6 = (UINT32_C(0xfffffe) - (arg1[5]));
    x7 = (UINT32_C(0xfffffe) - (arg1[6]));
    x8 = (UINT32_C(0x1fffffe) - (arg1[7]));
    x9 = (UINT32_C(0xfffffe) - (arg1[8]));
    x10 = (UINT32_C(0xfffffe) - (arg1[9]));
    x11 = (UINT32_C(0xfffffe) - (arg1[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz is a multi-limb conditional select.
 * Postconditions:
 *   eval out1 = (if arg1 = 0 then eval arg2 else eval arg3)
 *
 * Input Bounds:
 *   arg1: [0x0 ~> 0x1]
 *   arg2: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 *   arg3: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff], [0x0 ~> 0xffffffff]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
    uint32_t out1[11], fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 arg1,
    const uint32_t arg2[11], const uint32_t arg3[11]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x1, arg1, (arg2[0]), (arg3[0]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x2, arg1, (arg2[1]), (arg3[1]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x3, arg1, (arg2[2]), (arg3[2]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x4, arg1, (arg2[3]), (arg3[3]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x5, arg1, (arg2[4]), (arg3[4]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x6, arg1, (arg2[5]), (arg3[5]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x7, arg1, (arg2[6]), (arg3[6]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x8, arg1, (arg2[7]), (arg3[7]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x9, arg1, (arg2[8]), (arg3[8]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x10, arg1, (arg2[9]), (arg3[9]));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x11, arg1, (arg2[10]), (arg3[10]));
    out1[0] = x1;
    out1[1] = x2;
    out1[2] = x3;
    out1[3] = x4;
    out1[4] = x5;
    out1[5] = x6;
    out1[6] = x7;
    out1[7] = x8;
    out1[8] = x9;
    out1[9] = x10;
    out1[10] = x11;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes serializes a field element to bytes in little-endian order.
 * Postconditions:
 *   out1 = map (λ x, ⌊((eval arg1 mod m) mod 2^(8 * (x + 1))) / 2^(8 * x)⌋) [0..31]
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(
    uint8_t out1[32], const uint32_t arg1[11]) {
    uint32_t x1;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x2;
    uint32_t x3;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x4;
    uint32_t x5;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x6;
    uint32_t x7;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x8;
    uint32_t x9;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x10;
    uint32_t x11;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x12;
    uint32_t x13;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x14;
    uint32_t x15;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x16;
    uint32_t x17;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x18;
    uint32_t x19;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x20;
    uint32_t x21;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x22;
    uint32_t x23;
    uint32_t x24;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x25;
    uint32_t x26;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x27;
    uint32_t x28;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x29;
    uint32_t x30;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x31;
    uint32_t x32;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x33;
    uint32_t x34;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x35;
    uint32_t x36;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x37;
    uint32_t x38;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x39;
    uint32_t x40;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x41;
    uint32_t x42;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x43;
    uint32_t x44;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x45;
    uint32_t x46;
    uint32_t x47;
    uint32_t x48;
    uint32_t x49;
    uint32_t x50;
    uint32_t x51;
    uint32_t x52;
    uint32_t x53;
    uint32_t x54;
    uint8_t x55;
    uint32_t x56;
    uint8_t x57;
    uint8_t x58;
    uint8_t x59;
    uint32_t x60;
    uint8_t x61;
    uint8_t x62;
    uint32_t x63;
    uint8_t x64;
    uint32_t x65;
    uint8_t x66;
    uint32_t x67;
    uint8_t x68;
    uint8_t x69;
    uint32_t x70;
    uint8_t x71;
    uint32_t x72;
    uint8_t x73;
    uint32_t x74;
    uint8_t x75;
    uint8_t x76;
    uint32_t x77;
    uint8_t x78;
    uint32_t x79;
    uint8_t x80;
    uint32_t x81;
    uint8_t x82;
    uint8_t x83;
    uint32_t x84;
    uint8_t x85;
    uint32_t x86;
    uint8_t x87;
    uint32_t x88;
    uint8_t x89;
    uint8_t x90;
    uint32_t x91;
    uint8_t x92;
    uint32_t x93;
    uint8_t x94;
    uint32_t x95;
    uint8_t x96;
    uint8_t x97;
    uint32_t x98;
    uint8_t x99;
    uint32_t x100;
    uint8_t x101;
    uint32_t x102;
    uint8_t x103;
    uint8_t x104;
    uint32_t x105;
    uint8_t x106;
    uint32_t x107;
    uint8_t x108;
    uint32_t x109;
    uint8_t x110;
    uint8_t x111;
    uint32_t x112;
    uint8_t x113;
    uint32_t x114;
    uint8_t x115;
    uint32_t x116;
    uint8_t x117;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x118;
    uint32_t x119;
    uint8_t x120;
    uint32_t x121;
    uint8_t x122;
    uint8_t x123;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u24(
        &x1, &x2, 0x0, (arg1[0]), UINT32_C(0xfffd97));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x3, &x4, x2, (arg1[1]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x5, &x6, x4, (arg1[2]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u24(
        &x7, &x8, x6, (arg1[3]), UINT32_C(0xffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x9, &x10, x8, (arg1[4]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x11, &x12, x10, (arg1[5]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x13, &x14, x12, (arg1[6]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u24(
        &x15, &x16, x14, (arg1[7]), UINT32_C(0xffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x17, &x18, x16, (arg1[8]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x19, &x20, x18, (arg1[9]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_subborrowx_u23(
        &x21, &x22, x20, (arg1[10]), UINT32_C(0x7fffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_cmovznz_u32(
        &x23, x22, 0x0, UINT32_C(0xffffffff));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u24(
        &x24, &x25, 0x0, x1, (x23 & UINT32_C(0xfffd97)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x26, &x27, x25, x3, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x28, &x29, x27, x5, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u24(
        &x30, &x31, x29, x7, (x23 & UINT32_C(0xffffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x32, &x33, x31, x9, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x34, &x35, x33, x11, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x36, &x37, x35, x13, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u24(
        &x38, &x39, x37, x15, (x23 & UINT32_C(0xffffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x40, &x41, x39, x17, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x42, &x43, x41, x19, (x23 & UINT32_C(0x7fffff)));
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_addcarryx_u23(
        &x44, &x45, x43, x21, (x23 & UINT32_C(0x7fffff)));
    x46 = (x44 * (uint32_t)0x2);
    x47 = (x42 << 2);
    x48 = (x40 << 3);
    x49 = (x38 << 3);
    x50 = (x36 << 4);
    x51 = (x34 << 5);
    x52 = (x32 << 6);
    x53 = (x30 << 6);
    x54 = (x28 << 7);
    x55 = (uint8_t)(x24 & UINT8_C(0xff));
    x56 = (x24 >> 8);
    x57 = (uint8_t)(x56 & UINT8_C(0xff));
    x58 = (uint8_t)(x56 >> 8);
    x59 = (uint8_t)(x26 & UINT8_C(0xff));
    x60 = (x26 >> 8);
    x61 = (uint8_t)(x60 & UINT8_C(0xff));
    x62 = (uint8_t)(x60 >> 8);
    x63 = (x54 + (uint32_t)x62);
    x64 = (uint8_t)(x63 & UINT8_C(0xff));
    x65 = (x63 >> 8);
    x66 = (uint8_t)(x65 & UINT8_C(0xff));
    x67 = (x65 >> 8);
    x68 = (uint8_t)(x67 & UINT8_C(0xff));
    x69 = (uint8_t)(x67 >> 8);
    x70 = (x53 + (uint32_t)x69);
    x71 = (uint8_t)(x70 & UINT8_C(0xff));
    x72 = (x70 >> 8);
    x73 = (uint8_t)(x72 & UINT8_C(0xff));
    x74 = (x72 >> 8);
    x75 = (uint8_t)(x74 & UINT8_C(0xff));
    x76 = (uint8_t)(x74 >> 8);
    x77 = (x52 + (uint32_t)x76);
    x78 = (uint8_t)(x77 & UINT8_C(0xff));
    x79 = (x77 >> 8);
    x80 = (uint8_t)(x79 & UINT8_C(0xff));
    x81 = (x79 >> 8);
    x82 = (uint8_t)(x81 & UINT8_C(0xff));
    x83 = (uint8_t)(x81 >> 8);
    x84 = (x51 + (uint32_t)x83);
    x85 = (uint8_t)(x84 & UINT8_C(0xff));
    x86 = (x84 >> 8);
    x87 = (uint8_t)(x86 & UINT8_C(0xff));
    x88 = (x86 >> 8);
    x89 = (uint8_t)(x88 & UINT8_C(0xff));
    x90 = (uint8_t)(x88 >> 8);
    x91 = (x50 + (uint32_t)x90);
    x92 = (uint8_t)(x91 & UINT8_C(0xff));
    x93 = (x91 >> 8);
    x94 = (uint8_t)(x93 & UINT8_C(0xff));
    x95 = (x93 >> 8);
    x96 = (uint8_t)(x95 & UINT8_C(0xff));
    x97 = (uint8_t)(x95 >> 8);
    x98 = (x49 + (uint32_t)x97);
    x99 = (uint8_t)(x98 & UINT8_C(0xff));
    x100 = (x98 >> 8);
    x101 = (uint8_t)(x100 & UINT8_C(0xff));
    x102 = (x100 >> 8);
    x103 = (uint8_t)(x102 & UINT8_C(0xff));
    x104 = (uint8_t)(x102 >> 8);
    x105 = (x48 + (uint32_t)x104);
    x106 = (uint8_t)(x105 & UINT8_C(0xff));
    x107 = (x105 >> 8);
    x108 = (uint8_t)(x107 & UINT8_C(0xff));
    x109 = (x107 >> 8);
    x110 = (uint8_t)(x109 & UINT8_C(0xff));
    x111 = (uint8_t)(x109 >> 8);
    x112 = (x47 + (uint32_t)x111);
    x113 = (uint8_t)(x112 & UINT8_C(0xff));
    x114 = (x112 >> 8);
    x115 = (uint8_t)(x114 & UINT8_C(0xff));
    x116 = (x114 >> 8);
    x117 = (uint8_t)(x116 & UINT8_C(0xff));
    x118 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x116 >> 8);
    x119 = (x46 + (uint32_t)x118);
    x120 = (uint8_t)(x119 & UINT8_C(0xff));
    x121 = (x119 >> 8);
    x122 = (uint8_t)(x121 & UINT8_C(0xff));
    x123 = (uint8_t)(x121 >> 8);
    out1[0] = x55;
    out1[1] = x57;
    out1[2] = x58;
    out1[3] = x59;
    out1[4] = x61;
    out1[5] = x64;
    out1[6] = x66;
    out1[7] = x68;
    out1[8] = x71;
    out1[9] = x73;
    out1[10] = x75;
    out1[11] = x78;
    out1[12] = x80;
    out1[13] = x82;
    out1[14] = x85;
    out1[15] = x87;
    out1[16] = x89;
    out1[17] = x92;
    out1[18] = x94;
    out1[19] = x96;
    out1[20] = x99;
    out1[21] = x101;
    out1[22] = x103;
    out1[23] = x106;
    out1[24] = x108;
    out1[25] = x110;
    out1[26] = x113;
    out1[27] = x115;
    out1[28] = x117;
    out1[29] = x120;
    out1[30] = x122;
    out1[31] = x123;
}

/*
 * The function fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes deserializes a field element from bytes in little-endian order.
 * Postconditions:
 *   eval out1 mod m = bytes_eval arg1 mod m
 *
 * Input Bounds:
 *   arg1: [[0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff], [0x0 ~> 0xff]]
 * Output Bounds:
 *   out1: [[0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x1000000], [0x0 ~> 0x800000], [0x0 ~> 0x800000], [0x0 ~> 0x800000]]
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(
    uint32_t out1[11], const uint8_t arg1[32]) {
    uint32_t x1;
    uint32_t x2;
    uint32_t x3;
    uint32_t x4;
    uint32_t x5;
    uint32_t x6;
    uint32_t x7;
    uint32_t x8;
    uint32_t x9;
    uint32_t x10;
    uint32_t x11;
    uint32_t x12;
    uint32_t x13;
    uint32_t x14;
    uint32_t x15;
    uint32_t x16;
    uint32_t x17;
    uint32_t x18;
    uint32_t x19;
    uint32_t x20;
    uint32_t x21;
    uint32_t x22;
    uint32_t x23;
    uint32_t x24;
    uint32_t x25;
    uint32_t x26;
    uint32_t x27;
    uint32_t x28;
    uint8_t x29;
    uint32_t x30;
    uint32_t x31;
    uint8_t x32;
    uint32_t x33;
    uint32_t x34;
    uint32_t x35;
    uint32_t x36;
    uint32_t x37;
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1 x38;
    uint32_t x39;
    uint32_t x40;
    uint32_t x41;
    uint32_t x42;
    uint8_t x43;
    uint32_t x44;
    uint32_t x45;
    uint32_t x46;
    uint32_t x47;
    uint8_t x48;
    uint32_t x49;
    uint32_t x50;
    uint32_t x51;
    uint32_t x52;
    uint8_t x53;
    uint32_t x54;
    uint32_t x55;
    uint32_t x56;
    uint32_t x57;
    uint8_t x58;
    uint32_t x59;
    uint32_t x60;
    uint32_t x61;
    uint32_t x62;
    uint8_t x63;
    uint32_t x64;
    uint32_t x65;
    uint32_t x66;
    uint32_t x67;
    uint8_t x68;
    uint32_t x69;
    uint32_t x70;
    uint32_t x71;
    uint32_t x72;
    uint8_t x73;
    uint32_t x74;
    uint32_t x75;
    uint32_t x76;
    uint32_t x77;
    uint8_t x78;
    uint32_t x79;
    uint32_t x80;
    x1 = ((uint32_t)(arg1[31]) << 15);
    x2 = ((uint32_t)(arg1[30]) << 7);
    x3 = ((uint32_t)(arg1[29]) << 22);
    x4 = ((uint32_t)(arg1[28]) << 14);
    x5 = ((uint32_t)(arg1[27]) << 6);
    x6 = ((uint32_t)(arg1[26]) << 21);
    x7 = ((uint32_t)(arg1[25]) << 13);
    x8 = ((uint32_t)(arg1[24]) << 5);
    x9 = ((uint32_t)(arg1[23]) << 21);
    x10 = ((uint32_t)(arg1[22]) << 13);
    x11 = ((uint32_t)(arg1[21]) << 5);
    x12 = ((uint32_t)(arg1[20]) << 20);
    x13 = ((uint32_t)(arg1[19]) << 12);
    x14 = ((uint32_t)(arg1[18]) << 4);
    x15 = ((uint32_t)(arg1[17]) << 19);
    x16 = ((uint32_t)(arg1[16]) << 11);
    x17 = ((uint32_t)(arg1[15]) << 3);
    x18 = ((uint32_t)(arg1[14]) << 18);
    x19 = ((uint32_t)(arg1[13]) << 10);
    x20 = ((uint32_t)(arg1[12]) << 2);
    x21 = ((uint32_t)(arg1[11]) << 18);
    x22 = ((uint32_t)(arg1[10]) << 10);
    x23 = ((uint32_t)(arg1[9]) << 2);
    x24 = ((uint32_t)(arg1[8]) << 17);
    x25 = ((uint32_t)(arg1[7]) << 9);
    x26 = ((uint32_t)(arg1[6]) * 0x2);
    x27 = ((uint32_t)(arg1[5]) << 16);
    x28 = ((uint32_t)(arg1[4]) << 8);
    x29 = (arg1[3]);
    x30 = ((uint32_t)(arg1[2]) << 16);
    x31 = ((uint32_t)(arg1[1]) << 8);
    x32 = (arg1[0]);
    x33 = (x31 + (uint32_t)x32);
    x34 = (x30 + x33);
    x35 = (x28 + (uint32_t)x29);
    x36 = (x27 + x35);
    x37 = (x36 & UINT32_C(0x7fffff));
    x38 = (fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_uint1)(x36 >> 23);
    x39 = (x26 + (uint32_t)x38);
    x40 = (x25 + x39);
    x41 = (x24 + x40);
    x42 = (x41 & UINT32_C(0x7fffff));
    x43 = (uint8_t)(x41 >> 23);
    x44 = (x23 + (uint32_t)x43);
    x45 = (x22 + x44);
    x46 = (x21 + x45);
    x47 = (x46 & UINT32_C(0xffffff));
    x48 = (uint8_t)(x46 >> 24);
    x49 = (x20 + (uint32_t)x48);
    x50 = (x19 + x49);
    x51 = (x18 + x50);
    x52 = (x51 & UINT32_C(0x7fffff));
    x53 = (uint8_t)(x51 >> 23);
    x54 = (x17 + (uint32_t)x53);
    x55 = (x16 + x54);
    x56 = (x15 + x55);
    x57 = (x56 & UINT32_C(0x7fffff));
    x58 = (uint8_t)(x56 >> 23);
    x59 = (x14 + (uint32_t)x58);
    x60 = (x13 + x59);
    x61 = (x12 + x60);
    x62 = (x61 & UINT32_C(0x7fffff));
    x63 = (uint8_t)(x61 >> 23);
    x64 = (x11 + (uint32_t)x63);
    x65 = (x10 + x64);
    x66 = (x9 + x65);
    x67 = (x66 & UINT32_C(0xffffff));
    x68 = (uint8_t)(x66 >> 24);
    x69 = (x8 + (uint32_t)x68);
    x70 = (x7 + x69);
    x71 = (x6 + x70);
    x72 = (x71 & UINT32_C(0x7fffff));
    x73 = (uint8_t)(x71 >> 23);
    x74 = (x5 + (uint32_t)x73);
    x75 = (x4 + x74);
    x76 = (x3 + x75);
    x77 = (x76 & UINT32_C(0x7fffff));
    x78 = (uint8_t)(x76 >> 23);
    x79 = (x2 + (uint32_t)x78);
    x80 = (x1 + x79);
    out1[0] = x34;
    out1[1] = x37;
    out1[2] = x42;
    out1[3] = x47;
    out1[4] = x52;
    out1[5] = x57;
    out1[6] = x62;
    out1[7] = x67;
    out1[8] = x72;
    out1[9] = x77;
    out1[10] = x80;
}

/* END verbatim fiat code */

/*-
 * Finite field inversion via FLT.
 * NB: this is not a real Fiat function, just named that way for consistency.
 * Autogenerated: ecp/id_GostR3410_2001_CryptoPro_A_ParamSet/fe_inv.op3
 * custom repunit addition chain
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(fe_t output,
                                                            const fe_t t1) {
    int i;
    /* temporary variables */
    fe_t acc, t16, t164, t2, t246, t32, t4, t64, t8, t80, t82;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, acc, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t4);
    for (i = 0; i < 3; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t8, acc, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t8);
    for (i = 0; i < 7; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t16, acc, t8);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t16);
    for (i = 0; i < 15; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t32, acc, t16);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t32);
    for (i = 0; i < 31; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t64, acc, t32);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t64);
    for (i = 0; i < 15; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t80, acc, t16);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t80);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t82, acc, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t82);
    for (i = 0; i < 81; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t164, acc, t82);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t164);
    for (i = 0; i < 81; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t246, acc, t82);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, t246);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t2);
    for (i = 0; i < 3; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(acc, acc, t1);
    for (i = 0; i < 2; i++)
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(acc, acc);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(output, acc, t1);
}

/* curve coefficient constants */

static const limb_t const_one[11] = {
    UINT32_C(0x00000001), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000)};

static const limb_t const_b[11] = {
    UINT32_C(0x000000A6), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
    UINT32_C(0x00000000), UINT32_C(0x00000000)};

/* LUT for scalar multiplication by comb interleaving */
static const pt_aff_t lut_cmb[19][16] = {
    {
        {{UINT32_C(0x00000001), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000), UINT32_C(0x00000000),
          UINT32_C(0x00000000), UINT32_C(0x00000000)},
         {UINT32_C(0x009F1E14), UINT32_C(0x00499C9E), UINT32_C(0x00624559),
          UINT32_C(0x007C8F8E), UINT32_C(0x00253CB7), UINT32_C(0x005BB1A9),
          UINT32_C(0x002453F2), UINT32_C(0x00FBEA0B), UINT32_C(0x00139B44),
          UINT32_C(0x001C7826), UINT32_C(0x0046C8F2)}},
        {{UINT32_C(0x00E38D2C), UINT32_C(0x00638E38), UINT32_C(0x00471C71),
          UINT32_C(0x008E38E3), UINT32_C(0x000E38E3), UINT32_C(0x001C71C7),
          UINT32_C(0x0038E38E), UINT32_C(0x0071C71C), UINT32_C(0x0071C71C),
          UINT32_C(0x00638E38), UINT32_C(0x00471C71)},
         {UINT32_C(0x00788A51), UINT32_C(0x0017796B), UINT32_C(0x005090C2),
          UINT32_C(0x000E1DA3), UINT32_C(0x001EA532), UINT32_C(0x006C1093),
          UINT32_C(0x00507A6C), UINT32_C(0x009B375E), UINT32_C(0x0076083A),
          UINT32_C(0x0072A688), UINT32_C(0x003B5E68)}},
        {{UINT32_C(0x0072C029), UINT32_C(0x001C3871), UINT32_C(0x006DE040),
          UINT32_C(0x00E6D223), UINT32_C(0x0035AD09), UINT32_C(0x0078D2C5),
          UINT32_C(0x004B6782), UINT32_C(0x006D32D1), UINT32_C(0x001D2C78),
          UINT32_C(0x000F861E), UINT32_C(0x00483B00)},
         {UINT32_C(0x008D7CC0), UINT32_C(0x00023B06), UINT32_C(0x0030EC69),
          UINT32_C(0x00FF4DED), UINT32_C(0x002EE231), UINT32_C(0x006412C7),
          UINT32_C(0x005AF3F6), UINT32_C(0x00435452), UINT32_C(0x0004E9C3),
          UINT32_C(0x006B9475), UINT32_C(0x00484B93)}},
        {{UINT32_C(0x00196EE1), UINT32_C(0x006A01E4), UINT32_C(0x00652F51),
          UINT32_C(0x00416FD6), UINT32_C(0x005EBCD2), UINT32_C(0x0071846F),
          UINT32_C(0x0036FA23), UINT32_C(0x00D73EBD), UINT32_C(0x00180C14),
          UINT32_C(0x00653358), UINT32_C(0x0017EEC8)},
         {UINT32_C(0x000125FB), UINT32_C(0x00410C4D), UINT32_C(0x0074C481),
          UINT32_C(0x002A7CFC), UINT32_C(0x0065AC08), UINT32_C(0x000F3BD1),
          UINT32_C(0x004A5388), UINT32_C(0x00B7149E), UINT32_C(0x00085C10),
          UINT32_C(0x0054C612), UINT32_C(0x000E3FA5)}},
        {{UINT32_C(0x005ECDA3), UINT32_C(0x00246227), UINT32_C(0x0016E04F),
          UINT32_C(0x0005C37F), UINT32_C(0x00442C63), UINT32_C(0x001D5B95),
          UINT32_C(0x0057520F), UINT32_C(0x00792514), UINT32_C(0x003FB8FA),
          UINT32_C(0x0037B8D0), UINT32_C(0x005B7CB0)},
         {UINT32_C(0x005ED26A), UINT32_C(0x0028AD39), UINT32_C(0x004E2D06),
          UINT32_C(0x00BDC2A3), UINT32_C(0x0052BE72), UINT32_C(0x002F9991),
          UINT32_C(0x0019C7FC), UINT32_C(0x00209BD7), UINT32_C(0x007BF20C),
          UINT32_C(0x005196B4), UINT32_C(0x0069DFB4)}},
        {{UINT32_C(0x00834086), UINT32_C(0x002F3D1A), UINT32_C(0x007690A4),
          UINT32_C(0x00B28D88), UINT32_C(0x0022B907), UINT32_C(0x0025F98C),
          UINT32_C(0x0023F3A1), UINT32_C(0x00E2E9F9), UINT32_C(0x003A0364),
          UINT32_C(0x00613CE7), UINT32_C(0x0031EE39)},
         {UINT32_C(0x00C2FF84), UINT32_C(0x003F08BB), UINT32_C(0x0016B10A),
          UINT32_C(0x001D9E58), UINT32_C(0x001AA31A), UINT32_C(0x0016B57C),
          UINT32_C(0x002724AF), UINT32_C(0x001CEDB4), UINT32_C(0x000F8812),
          UINT32_C(0x0014C068), UINT32_C(0x0064070B)}},
        {{UINT32_C(0x00BBCCCD), UINT32_C(0x002E8F3C), UINT32_C(0x0054B6AC),
          UINT32_C(0x00FD3BBE), UINT32_C(0x0013312F), UINT32_C(0x006A114E),
          UINT32_C(0x000F7610), UINT32_C(0x00A85A6C), UINT32_C(0x0073D230),
          UINT32_C(0x001AE5A6), UINT32_C(0x0028C30C)},
         {UINT32_C(0x00C26CB2), UINT32_C(0x006BBD29), UINT32_C(0x0024F433),
          UINT32_C(0x00BE725E), UINT32_C(0x001ECBEE), UINT32_C(0x00646D3C),
          UINT32_C(0x0015C789), UINT32_C(0x00B07720), UINT32_C(0x0030D220),
          UINT32_C(0x0045843E), UINT32_C(0x001EADEA)}},
        {{UINT32_C(0x007C58E2), UINT32_C(0x0052800F), UINT32_C(0x002F9F17),
          UINT32_C(0x0083F230), UINT32_C(0x004207AA), UINT32_C(0x004B8493),
          UINT32_C(0x0033495B), UINT32_C(0x001061BD), UINT32_C(0x0057D115),
          UINT32_C(0x005F83CA), UINT32_C(0x0051583F)},
         {UINT32_C(0x00B12811), UINT32_C(0x00705E55), UINT32_C(0x0048A034),
          UINT32_C(0x00C9201A), UINT32_C(0x0060E5DF), UINT32_C(0x006C7FE0),
          UINT32_C(0x00500B66), UINT32_C(0x00A91FBD), UINT32_C(0x001CFF16),
          UINT32_C(0x0066C372), UINT32_C(0x0002B561)}},
        {{UINT32_C(0x0039A634), UINT32_C(0x0050548C), UINT32_C(0x005CFE7B),
          UINT32_C(0x00723488), UINT32_C(0x006CDBF9), UINT32_C(0x006D4A7E),
          UINT32_C(0x00107BD7), UINT32_C(0x00D87870), UINT32_C(0x006FFF35),
          UINT32_C(0x0047470D), UINT32_C(0x001D44AD)},
         {UINT32_C(0x0051071C), UINT32_C(0x0000FACA), UINT32_C(0x004AB290),
          UINT32_C(0x00D72DA0), UINT32_C(0x000E946D), UINT32_C(0x00154920),
          UINT32_C(0x000DE920), UINT32_C(0x002CCC50), UINT32_C(0x0078FF60),
          UINT32_C(0x0076A2F6), UINT32_C(0x00418509)}},
        {{UINT32_C(0x00EF5595), UINT32_C(0x00116786), UINT32_C(0x00615EC8),
          UINT32_C(0x00727C29), UINT32_C(0x0021D6BF), UINT32_C(0x00727880),
          UINT32_C(0x0025C9C9), UINT32_C(0x0031B0B0), UINT32_C(0x002CACE7),
          UINT32_C(0x003C8E5C), UINT32_C(0x002C31D8)},
         {UINT32_C(0x00328440), UINT32_C(0x00485469), UINT32_C(0x006AE670),
          UINT32_C(0x006EE53F), UINT32_C(0x00299C43), UINT32_C(0x0005AD41),
          UINT32_C(0x000D8C3F), UINT32_C(0x00072A91), UINT32_C(0x0067D0E1),
          UINT32_C(0x004B76E9), UINT32_C(0x0074B00D)}},
        {{UINT32_C(0x00041497), UINT32_C(0x001E11C9), UINT32_C(0x001A4504),
          UINT32_C(0x002A7919), UINT32_C(0x00074B21), UINT32_C(0x004FA67F),
          UINT32_C(0x0004DC6B), UINT32_C(0x00F2C95C), UINT32_C(0x0039BDCC),
          UINT32_C(0x007A64EC), UINT32_C(0x005F3E5D)},
         {UINT32_C(0x009C53EC), UINT32_C(0x0069BDE1), UINT32_C(0x007E0626),
          UINT32_C(0x0042ED13), UINT32_C(0x0062AFB2), UINT32_C(0x0031E908),
          UINT32_C(0x003C5398), UINT32_C(0x006BE167), UINT32_C(0x0002399E),
          UINT32_C(0x00210E43), UINT32_C(0x0001779F)}},
        {{UINT32_C(0x00C84A66), UINT32_C(0x00444B2F), UINT32_C(0x000108CC),
          UINT32_C(0x0036EEF4), UINT32_C(0x00681F17), UINT32_C(0x00165962),
          UINT32_C(0x004C1DBC), UINT32_C(0x003AFD47), UINT32_C(0x003B7CE0),
          UINT32_C(0x0037AD5F), UINT32_C(0x004C55A7)},
         {UINT32_C(0x00C53CD0), UINT32_C(0x002E065E), UINT32_C(0x0002B7D3),
          UINT32_C(0x00A10201), UINT32_C(0x00174A99), UINT32_C(0x00365939),
          UINT32_C(0x001F2D54), UINT32_C(0x000806C0), UINT32_C(0x0039185C),
          UINT32_C(0x00001C4B), UINT32_C(0x00415674)}},
        {{UINT32_C(0x008B07C5), UINT32_C(0x007D1002), UINT32_C(0x0012AE3E),
          UINT32_C(0x002C420C), UINT32_C(0x00470873), UINT32_C(0x00339263),
          UINT32_C(0x006C2C6C), UINT32_C(0x002D1BB6), UINT32_C(0x0072A553),
          UINT32_C(0x00306BCE), UINT32_C(0x0007F4C6)},
         {UINT32_C(0x00B6438D), UINT32_C(0x007278A9), UINT32_C(0x0076463F),
          UINT32_C(0x00D03F8A), UINT32_C(0x00332938), UINT32_C(0x001C7585),
          UINT32_C(0x000874CF), UINT32_C(0x00CEDF11), UINT32_C(0x00432AA3),
          UINT32_C(0x0059DEAB), UINT32_C(0x005C130A)}},
        {{UINT32_C(0x00B53E9C), UINT32_C(0x006388B4), UINT32_C(0x000428F8),
          UINT32_C(0x0008A18A), UINT32_C(0x000EB210), UINT32_C(0x003361E6),
          UINT32_C(0x0006528B), UINT32_C(0x00A1AD62), UINT32_C(0x005BD8BC),
          UINT32_C(0x000D44E3), UINT32_C(0x0018CEE3)},
         {UINT32_C(0x00EC3379), UINT32_C(0x002B9AD0), UINT32_C(0x003D2CC1),
          UINT32_C(0x00A04547), UINT32_C(0x00600394), UINT32_C(0x00240EC9),
          UINT32_C(0x000CF8E5), UINT32_C(0x00DE5CA1), UINT32_C(0x00107147),
          UINT32_C(0x003B518C), UINT32_C(0x0060976D)}},
        {{UINT32_C(0x00685C39), UINT32_C(0x00577EB1), UINT32_C(0x002AA0AD),
          UINT32_C(0x00E3B804), UINT32_C(0x003082F3), UINT32_C(0x002C92AC),
          UINT32_C(0x00207C69), UINT32_C(0x00658635), UINT32_C(0x00670DA3),
          UINT32_C(0x0006A6A4), UINT32_C(0x002AF849)},
         {UINT32_C(0x0005760A), UINT32_C(0x006E3A33), UINT32_C(0x00629DCA),
          UINT32_C(0x00D8388A), UINT32_C(0x007C1F0E), UINT32_C(0x00121173),
          UINT32_C(0x002A0106), UINT32_C(0x00607836), UINT32_C(0x0071EB45),
          UINT32_C(0x00749724), UINT32_C(0x00722A37)}},
        {{UINT32_C(0x00DB7020), UINT32_C(0x005F6FEC), UINT32_C(0x0001C894),
          UINT32_C(0x00D0F9D5), UINT32_C(0x0045FD0A), UINT32_C(0x002C8167),
          UINT32_C(0x000995E9), UINT32_C(0x00C5F645), UINT32_C(0x002318F0),
          UINT32_C(0x0077F38B), UINT32_C(0x00580E22)},
         {UINT32_C(0x003C5516), UINT32_C(0x00035DF1), UINT32_C(0x00592BC6),
          UINT32_C(0x00A625DD), UINT32_C(0x002295A0), UINT32_C(0x0060337A),
          UINT32_C(0x0021824B), UINT32_C(0x00C51AB1), UINT32_C(0x0014C123),
          UINT32_C(0x007703F3), UINT32_C(0x002945A3)}},
    },
    {
        {{UINT32_C(0x0015A14B), UINT32_C(0x002339F9), UINT32_C(0x005304AB),
          UINT32_C(0x0075CCEE), UINT32_C(0x00194DC5), UINT32_C(0x004A8A9E),
          UINT32_C(0x0046D1D2), UINT32_C(0x0075C952), UINT32_C(0x007ED8B2),
          UINT32_C(0x00058732), UINT32_C(0x0045D9A3)},
         {UINT32_C(0x00E6BE0F), UINT32_C(0x005D23F0), UINT32_C(0x005D9FF5),
          UINT32_C(0x00C7E802), UINT32_C(0x0010C909), UINT32_C(0x00207ED0),
          UINT32_C(0x0066F794), UINT32_C(0x00D9AFE4), UINT32_C(0x007DA500),
          UINT32_C(0x007B21FF), UINT32_C(0x0058D1D8)}},
        {{UINT32_C(0x007DD1B7), UINT32_C(0x000A4C87), UINT32_C(0x005506F4),
          UINT32_C(0x00D63219), UINT32_C(0x00657A0A), UINT32_C(0x007F79A5),
          UINT32_C(0x003CA588), UINT32_C(0x00FF7B22), UINT32_C(0x002E92EA),
          UINT32_C(0x0030699F), UINT32_C(0x0028801D)},
         {UINT32_C(0x00171DE8), UINT32_C(0x0074AA40), UINT32_C(0x00776396),
          UINT32_C(0x000B6039), UINT32_C(0x00477A52), UINT32_C(0x00552FC4),
          UINT32_C(0x004CB63D), UINT32_C(0x001F4555), UINT32_C(0x00762774),
          UINT32_C(0x00404FD4), UINT32_C(0x001F0E12)}},
        {{UINT32_C(0x002131C9), UINT32_C(0x006A9CFB), UINT32_C(0x0023C4EC),
          UINT32_C(0x00E6D39F), UINT32_C(0x00185E55), UINT32_C(0x0032F173),
          UINT32_C(0x005E6019), UINT32_C(0x00A7E72E), UINT32_C(0x006D091C),
          UINT32_C(0x002967FD), UINT32_C(0x002B5D44)},
         {UINT32_C(0x00C01BA9), UINT32_C(0x0024C542), UINT32_C(0x00765B4D),
          UINT32_C(0x0061291A), UINT32_C(0x0020B694), UINT32_C(0x0012B204),
          UINT32_C(0x006C029A), UINT32_C(0x00880C16), UINT32_C(0x00087D31),
          UINT32_C(0x0013494A), UINT32_C(0x0005AC30)}},
        {{UINT32_C(0x00248E22), UINT32_C(0x001D20E0), UINT32_C(0x007970A3),
          UINT32_C(0x008D530E), UINT32_C(0x0069D972), UINT32_C(0x0036B6B2),
          UINT32_C(0x0023944A), UINT32_C(0x009283D0), UINT32_C(0x00413B7A),
          UINT32_C(0x0026DA72), UINT32_C(0x0061243F)},
         {UINT32_C(0x008FAB34), UINT32_C(0x005F4AF0), UINT32_C(0x0019759F),
          UINT32_C(0x0041583F), UINT32_C(0x00723D0D), UINT32_C(0x0049EF5D),
          UINT32_C(0x0037ED96), UINT32_C(0x00D94167), UINT32_C(0x0025C5F0),
          UINT32_C(0x0054670B), UINT32_C(0x00645850)}},
        {{UINT32_C(0x0078486C), UINT32_C(0x0068A7C1), UINT32_C(0x004E518A),
          UINT32_C(0x00B42004), UINT32_C(0x0057227C), UINT32_C(0x00378DB2),
          UINT32_C(0x006EC201), UINT32_C(0x006E95D7), UINT32_C(0x003784DB),
          UINT32_C(0x000913EC), UINT32_C(0x0068F117)},
         {UINT32_C(0x0089EA5B), UINT32_C(0x000279A8), UINT32_C(0x001FE8CD),
          UINT32_C(0x00BDCFEF), UINT32_C(0x0029CFB8), UINT32_C(0x00609772),
          UINT32_C(0x001D36D2), UINT32_C(0x00BC3D3D), UINT32_C(0x001D2878),
          UINT32_C(0x001A10E1), UINT32_C(0x00010462)}},
        {{UINT32_C(0x00B52308), UINT32_C(0x004A1DF4), UINT32_C(0x0079CB16),
          UINT32_C(0x00DC0E0E), UINT32_C(0x003932DF), UINT32_C(0x005103E4),
          UINT32_C(0x0029ADA5), UINT32_C(0x007319A2), UINT32_C(0x00426FF0),
          UINT32_C(0x00588A4E), UINT32_C(0x003F78AD)},
         {UINT32_C(0x00F1B775), UINT32_C(0x006E5A5C), UINT32_C(0x00219FCB),
          UINT32_C(0x0008CA19), UINT32_C(0x001D9BBA), UINT32_C(0x0075F3B0),
          UINT32_C(0x0078E9ED), UINT32_C(0x00009E41), UINT32_C(0x003356B2),
          UINT32_C(0x002C0425), UINT32_C(0x003BAECE)}},
        {{UINT32_C(0x00FD3BB1), UINT32_C(0x00535E55), UINT32_C(0x0009A346),
          UINT32_C(0x00A8E2F4), UINT32_C(0x006F6486), UINT32_C(0x0071D282),
          UINT32_C(0x003A985D), UINT32_C(0x00A34D18), UINT32_C(0x00606B28),
          UINT32_C(0x00009AEC), UINT32_C(0x002BEEBF)},
         {UINT32_C(0x003780DD), UINT32_C(0x00647EA6), UINT32_C(0x0047B1F7),
          UINT32_C(0x00598C39), UINT32_C(0x003BFDE6), UINT32_C(0x006C0B48),
          UINT32_C(0x00121EB8), UINT32_C(0x0085DBF8), UINT32_C(0x006F9773),
          UINT32_C(0x001528FB), UINT32_C(0x0003A0C5)}},
        {{UINT32_C(0x0094B350), UINT32_C(0x00522DB1), UINT32_C(0x00058D9C),
          UINT32_C(0x008A9662), UINT32_C(0x002E184D), UINT32_C(0x007814B1),
          UINT32_C(0x00036D84), UINT32_C(0x00594040), UINT32_C(0x00716935),
          UINT32_C(0x0018DEEE), UINT32_C(0x001E2219)},
         {UINT32_C(0x0006F259), UINT32_C(0x00772E6A), UINT32_C(0x00246188),
          UINT32_C(0x001EC5E7), UINT32_C(0x002939F4), UINT32_C(0x000FF055),
          UINT32_C(0x001978F0), UINT32_C(0x003F6DAF), UINT32_C(0x000A7BBD),
          UINT32_C(0x001A6FAF), UINT32_C(0x00510C0C)}},
        {{UINT32_C(0x0048904F), UINT32_C(0x007224FF), UINT32_C(0x0065BAA9),
          UINT32_C(0x000D3551), UINT32_C(0x004DA721), UINT32_C(0x0039A0B4),
          UINT32_C(0x002BE76F), UINT32_C(0x009B9E20), UINT32_C(0x002504DD),
          UINT32_C(0x006944BD), UINT32_C(0x0024DAA1)},
         {UINT32_C(0x00542F7C), UINT32_C(0x001D4977), UINT32_C(0x001576A4),
          UINT32_C(0x006672BC), UINT32_C(0x0007DBD1), UINT32_C(0x00220087),
          UINT32_C(0x0044AC5F), UINT32_C(0x00E1DAAD), UINT32_C(0x00221046),
          UINT32_C(0x0042A0F3), UINT32_C(0x0052C47E)}},
        {{UINT32_C(0x00AA4EC9), UINT32_C(0x000C5256), UINT32_C(0x001D2108),
          UINT32_C(0x007E3C18), UINT32_C(0x0057CD05), UINT32_C(0x006A2DDB),
          UINT32_C(0x0070D402), UINT32_C(0x0046F4C8), UINT32_C(0x0070C39D),
          UINT32_C(0x0002BCDE), UINT32_C(0x00386C4A)},
         {UINT32_C(0x00596D94), UINT32_C(0x007774AE), UINT32_C(0x0033D8A2),
          UINT32_C(0x00B48B39), UINT32_C(0x0055B5DB), UINT32_C(0x004A739E),
          UINT32_C(0x00601AF6), UINT32_C(0x0017C86B), UINT32_C(0x0033581B),
          UINT32_C(0x0013A82F), UINT32_C(0x002F7045)}},
        {{UINT32_C(0x00EA0CB5), UINT32_C(0x004F13CE), UINT32_C(0x006F865F),
          UINT32_C(0x00C4AAF7), UINT32_C(0x00456660), UINT32_C(0x00600B98),
          UINT32_C(0x0079C3AB), UINT32_C(0x0027D7D2), UINT32_C(0x0026486E),
          UINT32_C(0x0008FEAF), UINT32_C(0x002F5742)},
         {UINT32_C(0x00B8102B), UINT32_C(0x0060FD3D), UINT32_C(0x002D073F),
          UINT32_C(0x0039E77D), UINT32_C(0x002DC294), UINT32_C(0x00151AB7),
          UINT32_C(0x001413CF), UINT32_C(0x003AAF36), UINT32_C(0x0034A2D1),
          UINT32_C(0x002844E0), UINT32_C(0x001190CE)}},
        {{UINT32_C(0x00ED50BF), UINT32_C(0x003D775D), UINT32_C(0x00533D3C),
          UINT32_C(0x004B2DDC), UINT32_C(0x00762EB4), UINT32_C(0x0060B90F),
          UINT32_C(0x003045DA), UINT32_C(0x0095C306), UINT32_C(0x0044062C),
          UINT32_C(0x004DB392), UINT32_C(0x00658071)},
         {UINT32_C(0x0030AE7B), UINT32_C(0x007AE99B), UINT32_C(0x006CD461),
          UINT32_C(0x00CDBC52), UINT32_C(0x007E2103), UINT32_C(0x0019AC77),
          UINT32_C(0x005D4D8D), UINT32_C(0x00BFC271), UINT32_C(0x0037F336),
          UINT32_C(0x0060EDFB), UINT32_C(0x00247DD6)}},
        {{UINT32_C(0x0016BAED), UINT32_C(0x00616D44), UINT32_C(0x004BF21B),
          UINT32_C(0x0096D55E), UINT32_C(0x000125FC), UINT32_C(0x0065EA64),
          UINT32_C(0x006B4BA0), UINT32_C(0x0058D872), UINT32_C(0x0047273B),
          UINT32_C(0x00413B44), UINT32_C(0x007C3B0A)},
         {UINT32_C(0x0053BB34), UINT32_C(0x0073F0D7), UINT32_C(0x000E175F),
          UINT32_C(0x001C2578), UINT32_C(0x007D3B2F), UINT32_C(0x0012C2C4),
          UINT32_C(0x00706D71), UINT32_C(0x00C9BC8D), UINT32_C(0x00499E9A),
          UINT32_C(0x00531428), UINT32_C(0x004A5C64)}},
        {{UINT32_C(0x0037863B), UINT32_C(0x0079BA1D), UINT32_C(0x001CF74C),
          UINT32_C(0x0022EB4F), UINT32_C(0x0039A1FA), UINT32_C(0x005AFECB),
          UINT32_C(0x0001FF42), UINT32_C(0x009EBF05), UINT32_C(0x003094C5),
          UINT32_C(0x003DA3F3), UINT32_C(0x00648DA8)},
         {UINT32_C(0x00706E8B), UINT32_C(0x00116614), UINT32_C(0x00662488),
          UINT32_C(0x0020E858), UINT32_C(0x003042E0), UINT32_C(0x005598A9),
          UINT32_C(0x0051A013), UINT32_C(0x0096F0CF), UINT32_C(0x0026D7C9),
          UINT32_C(0x001297CB), UINT32_C(0x00325815)}},
        {{UINT32_C(0x00A6A2F2), UINT32_C(0x00431F85), UINT32_C(0x006A358E),
          UINT32_C(0x00D9BD37), UINT32_C(0x003DDAF8), UINT32_C(0x000FF3E4),
          UINT32_C(0x0056803F), UINT32_C(0x0050FB50), UINT32_C(0x0068CD30),
          UINT32_C(0x00242AA9), UINT32_C(0x005D281D)},
         {UINT32_C(0x0007C121), UINT32_C(0x006E657D), UINT32_C(0x0003FE75),
          UINT32_C(0x00B805F3), UINT32_C(0x004C976D), UINT32_C(0x005039AB),
          UINT32_C(0x0011443E), UINT32_C(0x00F0A01C), UINT32_C(0x00648859),
          UINT32_C(0x005F1033), UINT32_C(0x007B4C48)}},
        {{UINT32_C(0x00793170), UINT32_C(0x006DBF68), UINT32_C(0x007D1F25),
          UINT32_C(0x00F0D9B4), UINT32_C(0x0059CB0C), UINT32_C(0x002BAA82),
          UINT32_C(0x00516CAA), UINT32_C(0x0055BA64), UINT32_C(0x0014BA02),
          UINT32_C(0x006986E0), UINT32_C(0x005D492D)},
         {UINT32_C(0x0025053F), UINT32_C(0x00643229), UINT32_C(0x0072934C),
          UINT32_C(0x00FF2EFA), UINT32_C(0x0075BD10), UINT32_C(0x000EE1B5),
          UINT32_C(0x0075A5E1), UINT32_C(0x008B7B93), UINT32_C(0x001D5824),
          UINT32_C(0x002F28AB), UINT32_C(0x00762FF8)}},
    },
    {
        {{UINT32_C(0x0046367E), UINT32_C(0x0056A682), UINT32_C(0x00430EB2),
          UINT32_C(0x0025F7A6), UINT32_C(0x0062DE86), UINT32_C(0x0033FEF2),
          UINT32_C(0x00156E4E), UINT32_C(0x002BFC59), UINT32_C(0x00038721),
          UINT32_C(0x003BE4D8), UINT32_C(0x0000C9A8)},
         {UINT32_C(0x00E2541C), UINT32_C(0x0060B185), UINT32_C(0x00124C99),
          UINT32_C(0x007614C4), UINT32_C(0x004C9BB5), UINT32_C(0x001A2B53),
          UINT32_C(0x0055AB66), UINT32_C(0x00B34939), UINT32_C(0x00755795),
          UINT32_C(0x004AE42A), UINT32_C(0x0037EE9E)}},
        {{UINT32_C(0x00418B8A), UINT32_C(0x0053A329), UINT32_C(0x0067F6C0),
          UINT32_C(0x001B5F25), UINT32_C(0x004F5799), UINT32_C(0x0079A7BA),
          UINT32_C(0x0001E9CA), UINT32_C(0x00589410), UINT32_C(0x00348000),
          UINT32_C(0x007C467F), UINT32_C(0x0068CC15)},
         {UINT32_C(0x00BFD773), UINT32_C(0x000B128F), UINT32_C(0x0035E63D),
          UINT32_C(0x00C836C1), UINT32_C(0x0017BDFE), UINT32_C(0x006CFD71),
          UINT32_C(0x00373B9C), UINT32_C(0x0057EAF9), UINT32_C(0x002A01CD),
          UINT32_C(0x0074D14F), UINT32_C(0x00721C85)}},
        {{UINT32_C(0x0056BC62), UINT32_C(0x000D23A6), UINT32_C(0x0078FE76),
          UINT32_C(0x0019A73D), UINT32_C(0x00091B6B), UINT32_C(0x0005C60B),
          UINT32_C(0x0065104B), UINT32_C(0x0065A4B3), UINT32_C(0x005D0C5A),
          UINT32_C(0x002E8DB5), UINT32_C(0x00718081)},
         {UINT32_C(0x00EF69D9), UINT32_C(0x006C1FDD), UINT32_C(0x0065A57D),
          UINT32_C(0x00BC2B7B), UINT32_C(0x006DB6EB), UINT32_C(0x001AFD11),
          UINT32_C(0x00605819), UINT32_C(0x008E9270), UINT32_C(0x005C23F4),
          UINT32_C(0x005067EB), UINT32_C(0x00598190)}},
        {{UINT32_C(0x00EF9F96), UINT32_C(0x00510C22), UINT32_C(0x001E5EED),
          UINT32_C(0x003B03E5), UINT32_C(0x005C6020), UINT32_C(0x003C2BB6),
          UINT32_C(0x0035FD52), UINT32_C(0x00A6A7F3), UINT32_C(0x000128B7),
          UINT32_C(0x001E45E8), UINT32_C(0x0049EFAA)},
         {UINT32_C(0x006E0992), UINT32_C(0x0018EA4E), UINT32_C(0x00004761),
          UINT32_C(0x00CFE527), UINT32_C(0x0039B16F), UINT32_C(0x0017BAE9),
          UINT32_C(0x0057DC05), UINT32_C(0x00982F6B), UINT32_C(0x001D3209),
          UINT32_C(0x0019FC51), UINT32_C(0x000F4184)}},
        {{UINT32_C(0x001EBCA4), UINT32_C(0x001ECDDD), UINT32_C(0x0009CED1),
          UINT32_C(0x00785BD4), UINT32_C(0x005F74D6), UINT32_C(0x00589B35),
          UINT32_C(0x0026267C), UINT32_C(0x003A1A20), UINT32_C(0x00148BA6),
          UINT32_C(0x00078BFB), UINT32_C(0x0057564D)},
         {UINT32_C(0x008FB356), UINT32_C(0x00195CBB), UINT32_C(0x003A2D22),
          UINT32_C(0x0015DEC8), UINT32_C(0x00287F6A), UINT32_C(0x007C4179),
          UINT32_C(0x0078B244), UINT32_C(0x00013AE9), UINT32_C(0x004FD9D1),
          UINT32_C(0x005916FD), UINT32_C(0x007AAB2B)}},
        {{UINT32_C(0x005AE235), UINT32_C(0x004A7074), UINT32_C(0x006D38A5),
          UINT32_C(0x00A5B778), UINT32_C(0x00192A8E), UINT32_C(0x00292200),
          UINT32_C(0x00536809), UINT32_C(0x00C5FA0A), UINT32_C(0x007335A7),
          UINT32_C(0x007A9B01), UINT32_C(0x006B8F21)},
         {UINT32_C(0x00FC9D22), UINT32_C(0x005462CC), UINT32_C(0x0011E27E),
          UINT32_C(0x00A16304), UINT32_C(0x002252C2), UINT32_C(0x00065957),
          UINT32_C(0x000FB44C), UINT32_C(0x00507E75), UINT32_C(0x004C7390),
          UINT32_C(0x002880F2), UINT32_C(0x0011F9DC)}},
        {{UINT32_C(0x008982F2), UINT32_C(0x005E0F01), UINT32_C(0x00191A83),
          UINT32_C(0x00B79DFB), UINT32_C(0x0014F23A), UINT32_C(0x005BFFB9),
          UINT32_C(0x00679DCA), UINT32_C(0x00FDEBA3), UINT32_C(0x007A5C5D),
          UINT32_C(0x00317F01), UINT32_C(0x0005F996)},
         {UINT32_C(0x002F393C), UINT32_C(0x0057EA31), UINT32_C(0x0041F969),
          UINT32_C(0x0060EB85), UINT32_C(0x0018762F), UINT32_C(0x0034FF1A),
          UINT32_C(0x006E4F64), UINT32_C(0x00E85223), UINT32_C(0x00721BD5),
          UINT32_C(0x005695D8), UINT32_C(0x00735FC8)}},
        {{UINT32_C(0x000FF37D), UINT32_C(0x001EE6D5), UINT32_C(0x0059B657),
          UINT32_C(0x0059218D), UINT32_C(0x005D7413), UINT32_C(0x0016DC74),
          UINT32_C(0x00245CE2), UINT32_C(0x009176FC), UINT32_C(0x00140786),
          UINT32_C(0x00123E16), UINT32_C(0x0014C417)},
         {UINT32_C(0x008C5660), UINT32_C(0x004AB42B), UINT32_C(0x006F788D),
          UINT32_C(0x004C5639), UINT32_C(0x00527C63), UINT32_C(0x0076AD8C),
          UINT32_C(0x000E1CD3), UINT32_C(0x00905B46), UINT32_C(0x00362085),
          UINT32_C(0x0005BCA4), UINT32_C(0x005143C9)}},
        {{UINT32_C(0x0003CB83), UINT32_C(0x00303CF6), UINT32_C(0x004A9EA9),
          UINT32_C(0x0045E684), UINT32_C(0x00510A34), UINT32_C(0x007BDA22),
          UINT32_C(0x004947E0), UINT32_C(0x004AE474), UINT32_C(0x000566E6),
          UINT32_C(0x001C9AA5), UINT32_C(0x002791D8)},
         {UINT32_C(0x008D0BF2), UINT32_C(0x0042016F), UINT32_C(0x00669D0C),
          UINT32_C(0x009942A6), UINT32_C(0x00694F9B), UINT32_C(0x007DA32E),
          UINT32_C(0x005BE0ED), UINT32_C(0x00CB0DE0), UINT32_C(0x006A8967),
          UINT32_C(0x000AC39B), UINT32_C(0x003B3E68)}},
        {{UINT32_C(0x0060D714), UINT32_C(0x004F6454), UINT32_C(0x00061D11),
          UINT32_C(0x00C2AA2D), UINT32_C(0x001269D9), UINT32_C(0x0045B4EB),
          UINT32_C(0x00452BA7), UINT32_C(0x001C10D3), UINT32_C(0x00468D4A),
          UINT32_C(0x002EFCCA), UINT32_C(0x006DE69E)},
         {UINT32_C(0x00CCB9AA), UINT32_C(0x005E4AAD), UINT32_C(0x0034AB4F),
          UINT32_C(0x00451715), UINT32_C(0x00742A4D), UINT32_C(0x0054F77E),
          UINT32_C(0x000E11C5), UINT32_C(0x00848202), UINT32_C(0x006F4690),
          UINT32_C(0x00429DB2), UINT32_C(0x00063397)}},
        {{UINT32_C(0x00E679A9), UINT32_C(0x000630F0), UINT32_C(0x0054132F),
          UINT32_C(0x0028E457), UINT32_C(0x00530D92), UINT32_C(0x002D81BE),
          UINT32_C(0x006D4DA3), UINT32_C(0x00DD8F12), UINT32_C(0x005AEF2B),
          UINT32_C(0x00681D79), UINT32_C(0x004F4E27)},
         {UINT32_C(0x00452CC5), UINT32_C(0x006066A9), UINT32_C(0x00586F19),
          UINT32_C(0x00DCA842), UINT32_C(0x003F3915), UINT32_C(0x007F124C),
          UINT32_C(0x005A982F), UINT32_C(0x00232890), UINT32_C(0x006759D7),
          UINT32_C(0x005451F1), UINT32_C(0x001EBD44)}},
        {{UINT32_C(0x00FA4A07), UINT32_C(0x0030B22D), UINT32_C(0x00241069),
          UINT32_C(0x00DE41E0), UINT32_C(0x004CE4D7), UINT32_C(0x002D9D3C),
          UINT32_C(0x001677BA), UINT32_C(0x00980ED6), UINT32_C(0x0031C47B),
          UINT32_C(0x002ED227), UINT32_C(0x00159AD5)},
         {UINT32_C(0x0089C1F3), UINT32_C(0x0064AFBA), UINT32_C(0x0029E45B),
          UINT32_C(0x00032AD8), UINT32_C(0x002AB7FA), UINT32_C(0x00667A12),
          UINT32_C(0x0057ACD4), UINT32_C(0x006C9B6D), UINT32_C(0x00407D38),
          UINT32_C(0x00420BB2), UINT32_C(0x0026F2DA)}},
        {{UINT32_C(0x00C85447), UINT32_C(0x00186902), UINT32_C(0x00714C9C),
          UINT32_C(0x003E0E1B), UINT32_C(0x0079A719), UINT32_C(0x003E4DED),
          UINT32_C(0x0059E9D8), UINT32_C(0x00C3AC22), UINT32_C(0x00101DB8),
          UINT32_C(0x0079EC05), UINT32_C(0x00308946)},
         {UINT32_C(0x0020BFE2), UINT32_C(0x001B1C97), UINT32_C(0x002068FA),
          UINT32_C(0x000318E8), UINT32_C(0x003FA490), UINT32_C(0x0071C4A0),
          UINT32_C(0x000D2962), UINT32_C(0x006D6A13), UINT32_C(0x000B208C),
          UINT32_C(0x0016C4B8), UINT32_C(0x002FB1EB)}},
        {{UINT32_C(0x000FDDD3), UINT32_C(0x000BE560), UINT32_C(0x005F6F50),
          UINT32_C(0x00CBD86E), UINT32_C(0x005F789C), UINT32_C(0x00796635),
          UINT32_C(0x0073572E), UINT32_C(0x007EF213), UINT32_C(0x003C20F1),
          UINT32_C(0x0009D905), UINT32_C(0x00506CBA)},
         {UINT32_C(0x0053488D), UINT32_C(0x005470BC), UINT32_C(0x00356DBF),
          UINT32_C(0x00B92C0A), UINT32_C(0x00576D7C), UINT32_C(0x00258D40),
          UINT32_C(0x000EC04B), UINT32_C(0x00629848), UINT32_C(0x006EFE8C),
          UINT32_C(0x002F0536), UINT32_C(0x005F0ED9)}},
        {{UINT32_C(0x00941AAB), UINT32_C(0x0016408A), UINT32_C(0x0038B7B4),
          UINT32_C(0x00D17AD3), UINT32_C(0x00020287), UINT32_C(0x003C98C7),
          UINT32_C(0x007107B0), UINT32_C(0x00A47BF6), UINT32_C(0x0076D73B),
          UINT32_C(0x000E9DCA), UINT32_C(0x002D0380)},
         {UINT32_C(0x003E14DB), UINT32_C(0x0068545F), UINT32_C(0x00293201),
          UINT32_C(0x0041E644), UINT32_C(0x0037CC59), UINT32_C(0x0064BF44),
          UINT32_C(0x004AB432), UINT32_C(0x00222D96), UINT32_C(0x002A1A68),
          UINT32_C(0x000CA73B), UINT32_C(0x0000759D)}},
        {{UINT32_C(0x00B27EE6), UINT32_C(0x00392FAB), UINT32_C(0x006D1511),
          UINT32_C(0x00DE9390), UINT32_C(0x0053DBF3), UINT32_C(0x003F6EED),
          UINT32_C(0x001782D6), UINT32_C(0x002A6166), UINT32_C(0x004F448D),
          UINT32_C(0x004D0F3A), UINT32_C(0x007007C7)},
         {UINT32_C(0x00DFE6F7), UINT32_C(0x0032099E), UINT32_C(0x00729E1F),
          UINT32_C(0x009F5D6F), UINT32_C(0x003A3D25), UINT32_C(0x004DD691),
          UINT32_C(0x0020FFB0), UINT32_C(0x00CCDE9D), UINT32_C(0x0075D916),
          UINT32_C(0x00545C21), UINT32_C(0x00240E6D)}},
    },
    {
        {{UINT32_C(0x0034E1E6), UINT32_C(0x003047D2), UINT32_C(0x006E4485),
          UINT32_C(0x007D8C36), UINT32_C(0x00756414), UINT32_C(0x000967CD),
          UINT32_C(0x004EC80F), UINT32_C(0x00C63288), UINT32_C(0x0073E0FA),
          UINT32_C(0x007182FF), UINT32_C(0x00263DCE)},
         {UINT32_C(0x00462779), UINT32_C(0x001B3A99), UINT32_C(0x0037DBBA),
          UINT32_C(0x00D2FC96), UINT32_C(0x0064BB40), UINT32_C(0x007363A7),
          UINT32_C(0x0032772A), UINT32_C(0x00D91E04), UINT32_C(0x001007F8),
          UINT32_C(0x000AF4EC), UINT32_C(0x00193E0F)}},
        {{UINT32_C(0x00FDE1B6), UINT32_C(0x00642789), UINT32_C(0x0001A22B),
          UINT32_C(0x0053D392), UINT32_C(0x004F3462), UINT32_C(0x007001C5),
          UINT32_C(0x003757D1), UINT32_C(0x002FF687), UINT32_C(0x002AD0C5),
          UINT32_C(0x0061905E), UINT32_C(0x0032E050)},
         {UINT32_C(0x00586CC6), UINT32_C(0x0029644B), UINT32_C(0x001DCCE2),
          UINT32_C(0x00242AD5), UINT32_C(0x003A4D19), UINT32_C(0x007EDFA6),
          UINT32_C(0x00220A76), UINT32_C(0x00DD8BD2), UINT32_C(0x003AFB95),
          UINT32_C(0x006523B5), UINT32_C(0x0016D460)}},
        {{UINT32_C(0x003B3EBD), UINT32_C(0x0059FF35), UINT32_C(0x00646DFD),
          UINT32_C(0x001C652B), UINT32_C(0x00572E18), UINT32_C(0x007156D0),
          UINT32_C(0x003125AA), UINT32_C(0x0058C8A8), UINT32_C(0x00383EFA),
          UINT32_C(0x00270C8A), UINT32_C(0x00071DD1)},
         {UINT32_C(0x00E57097), UINT32_C(0x0052391E), UINT32_C(0x0058A14D),
          UINT32_C(0x0071FABE), UINT32_C(0x0014DDAD), UINT32_C(0x00583150),
          UINT32_C(0x006E7A1D), UINT32_C(0x0033F6C1), UINT32_C(0x00490296),
          UINT32_C(0x00414DE6), UINT32_C(0x007218AF)}},
        {{UINT32_C(0x00F33C1F), UINT32_C(0x00125893), UINT32_C(0x004FE3D7),
          UINT32_C(0x00C4A16E), UINT32_C(0x0066739E), UINT32_C(0x0044B06C),
          UINT32_C(0x0004329B), UINT32_C(0x0040B952), UINT32_C(0x003AB3B6),
          UINT32_C(0x004B9045), UINT32_C(0x0070EFC5)},
         {UINT32_C(0x0004A33D), UINT32_C(0x0060B5F6), UINT32_C(0x001C92D2),
          UINT32_C(0x0087E4CE), UINT32_C(0x007927E4), UINT32_C(0x00687FEB),
          UINT32_C(0x0068424F), UINT32_C(0x006E7721), UINT32_C(0x002CB0FB),
          UINT32_C(0x003E8D7D), UINT32_C(0x00066EEA)}},
        {{UINT32_C(0x00893929), UINT32_C(0x00749590), UINT32_C(0x00002975),
          UINT32_C(0x00359738), UINT32_C(0x0027B81C), UINT32_C(0x007CCA27),
          UINT32_C(0x00424A06), UINT32_C(0x008EAF50), UINT32_C(0x0015A2BC),
          UINT32_C(0x00191EEC), UINT32_C(0x0005255A)},
         {UINT32_C(0x00F0F1DA), UINT32_C(0x0001F6DC), UINT32_C(0x001636C8),
          UINT32_C(0x00012664), UINT32_C(0x0026261D), UINT32_C(0x005D425D),
          UINT32_C(0x004530C6), UINT32_C(0x005C3D03), UINT32_C(0x005543DE),
          UINT32_C(0x001B43E1), UINT32_C(0x001F7279)}},
        {{UINT32_C(0x0073F26A), UINT32_C(0x0068DAE8), UINT32_C(0x00721622),
          UINT32_C(0x002ABFC5), UINT32_C(0x001AEA78), UINT32_C(0x001C317D),
          UINT32_C(0x0057A2CE), UINT32_C(0x00D75B07), UINT32_C(0x0011544B),
          UINT32_C(0x006677F4), UINT32_C(0x004FE8D9)},
         {UINT32_C(0x00BD1939), UINT32_C(0x005FA352), UINT32_C(0x004E35F8),
          UINT32_C(0x002B9D5E), UINT32_C(0x00236E36), UINT32_C(0x00171F6F),
          UINT32_C(0x001C64C0), UINT32_C(0x0059974C), UINT32_C(0x001A9517),
          UINT32_C(0x000A0D86), UINT32_C(0x002C5844)}},
        {{UINT32_C(0x008028D2), UINT32_C(0x005696DA), UINT32_C(0x00134460),
          UINT32_C(0x00FFF1CA), UINT32_C(0x003E17C7), UINT32_C(0x00771795),
          UINT32_C(0x003731A0), UINT32_C(0x004FFEFA), UINT32_C(0x00559D72),
          UINT32_C(0x0069CD38), UINT32_C(0x0015F09E)},
         {UINT32_C(0x00C4C3AD), UINT32_C(0x0074750B), UINT32_C(0x001D026C),
          UINT32_C(0x00553063), UINT32_C(0x00193906), UINT32_C(0x000134C7),
          UINT32_C(0x0049B4A1), UINT32_C(0x0049D063), UINT32_C(0x003BF10D),
          UINT32_C(0x0067632D), UINT32_C(0x00300024)}},
        {{UINT32_C(0x00D77453), UINT32_C(0x003D07C0), UINT32_C(0x0019849D),
          UINT32_C(0x00D631D5), UINT32_C(0x00652BEF), UINT32_C(0x007E6CDE),
          UINT32_C(0x000D87C3), UINT32_C(0x004951A8), UINT32_C(0x007525CD),
          UINT32_C(0x0071B08E), UINT32_C(0x00289648)},
         {UINT32_C(0x00FD0ECE), UINT32_C(0x000BB7D1), UINT32_C(0x002C9248),
          UINT32_C(0x004C6FAB), UINT32_C(0x00372A68), UINT32_C(0x000EF891),
          UINT32_C(0x0020B4A1), UINT32_C(0x000A10A5), UINT32_C(0x006C334E),
          UINT32_C(0x001CCCB1), UINT32_C(0x00379EBC)}},
        {{UINT32_C(0x00D635C8), UINT32_C(0x005DA0CD), UINT32_C(0x000CA1CF),
          UINT32_C(0x009075FE), UINT32_C(0x001864D7), UINT32_C(0x00375091),
          UINT32_C(0x00219FB8), UINT32_C(0x0007A85A), UINT32_C(0x006F3E1F),
          UINT32_C(0x00391318), UINT32_C(0x00247B2A)},
         {UINT32_C(0x0002D9FB), UINT32_C(0x007FD052), UINT32_C(0x002C2377),
          UINT32_C(0x00A71137), UINT32_C(0x0021AD82), UINT32_C(0x003DB97F),
          UINT32_C(0x007E1B6E), UINT32_C(0x00C0C7AE), UINT32_C(0x00742A7A),
          UINT32_C(0x0024DE99), UINT32_C(0x004E9051)}},
        {{UINT32_C(0x00DADBAE), UINT32_C(0x00202B8D), UINT32_C(0x00632043),
          UINT32_C(0x0041281C), UINT32_C(0x001EBD2E), UINT32_C(0x007221A9),
          UINT32_C(0x006395E7), UINT32_C(0x0009A072), UINT32_C(0x006E872D),
          UINT32_C(0x006A1915), UINT32_C(0x00538853)},
         {UINT32_C(0x00071C4B), UINT32_C(0x007A19E1), UINT32_C(0x007C2689),
          UINT32_C(0x007FC063), UINT32_C(0x001C8283), UINT32_C(0x0067DED1),
          UINT32_C(0x00667A3F), UINT32_C(0x00AF7018), UINT32_C(0x0038D342),
          UINT32_C(0x000616D4), UINT32_C(0x0023B78B)}},
        {{UINT32_C(0x002693D0), UINT32_C(0x0078F96C), UINT32_C(0x00571654),
          UINT32_C(0x0014B152), UINT32_C(0x000B8F3A), UINT32_C(0x004480F8),
          UINT32_C(0x004AA85E), UINT32_C(0x00A2071C), UINT32_C(0x005BF5E5),
          UINT32_C(0x003DF984), UINT32_C(0x00290208)},
         {UINT32_C(0x00574625), UINT32_C(0x004E5F90), UINT32_C(0x0024BEE7),
          UINT32_C(0x0018C1AA), UINT32_C(0x000E42CE), UINT32_C(0x00353C8A),
          UINT32_C(0x0040F203), UINT32_C(0x00898F9A), UINT32_C(0x003FBFD0),
          UINT32_C(0x0043C31B), UINT32_C(0x004E5E2E)}},
        {{UINT32_C(0x0067D25A), UINT32_C(0x0031F139), UINT32_C(0x005BD832),
          UINT32_C(0x0058FC46), UINT32_C(0x002312E5), UINT32_C(0x00722F75),
          UINT32_C(0x00300A46), UINT32_C(0x000CD2F3), UINT32_C(0x0070A1F2),
          UINT32_C(0x001BBF1B), UINT32_C(0x005DAD68)},
         {UINT32_C(0x0027F5D0), UINT32_C(0x000A7235), UINT32_C(0x00550E6D),
          UINT32_C(0x007E271C), UINT32_C(0x003F1A3C), UINT32_C(0x003F545A),
          UINT32_C(0x000F465A), UINT32_C(0x006E0289), UINT32_C(0x0008E2B8),
          UINT32_C(0x00005661), UINT32_C(0x007DE299)}},
        {{UINT32_C(0x00548202), UINT32_C(0x00281B46), UINT32_C(0x004C8632),
          UINT32_C(0x008FDE33), UINT32_C(0x00254E52), UINT32_C(0x00768F00),
          UINT32_C(0x001C3AD6), UINT32_C(0x002D85C6), UINT32_C(0x00187542),
          UINT32_C(0x005F5A9E), UINT32_C(0x0079B908)},
         {UINT32_C(0x00C7C809), UINT32_C(0x001FE799), UINT32_C(0x0042AF63),
          UINT32_C(0x004EE64E), UINT32_C(0x001273EE), UINT32_C(0x00609BF2),
          UINT32_C(0x0022BBF9), UINT32_C(0x0022DE3E), UINT32_C(0x00591E73),
          UINT32_C(0x004FC701), UINT32_C(0x0007E865)}},
        {{UINT32_C(0x0098B574), UINT32_C(0x003DC53F), UINT32_C(0x0019A4E6),
          UINT32_C(0x00CBD70F), UINT32_C(0x0047B9D0), UINT32_C(0x005579C6),
          UINT32_C(0x00354F41), UINT32_C(0x00D2DC50), UINT32_C(0x00329B79),
          UINT32_C(0x0024242A), UINT32_C(0x0066FBA2)},
         {UINT32_C(0x0085FC08), UINT32_C(0x0049ABA0), UINT32_C(0x0002CD57),
          UINT32_C(0x0051C421), UINT32_C(0x00115DB3), UINT32_C(0x0066D6D5),
          UINT32_C(0x00303B94), UINT32_C(0x00A604BB), UINT32_C(0x00685047),
          UINT32_C(0x003158DF), UINT32_C(0x007ED9BF)}},
        {{UINT32_C(0x002AFA23), UINT32_C(0x0040ECF5), UINT32_C(0x004AF2B2),
          UINT32_C(0x00326E4C), UINT32_C(0x0057D312), UINT32_C(0x006C5108),
          UINT32_C(0x0039F6D7), UINT32_C(0x0036F6BA), UINT32_C(0x005598C8),
          UINT32_C(0x003CB53C), UINT32_C(0x0034AA18)},
         {UINT32_C(0x00CF42AB), UINT32_C(0x007A1089), UINT32_C(0x00798D02),
          UINT32_C(0x00EA386D), UINT32_C(0x0011472E), UINT32_C(0x005332E0),
          UINT32_C(0x0038B97B), UINT32_C(0x00AD3634), UINT32_C(0x0008C5EB),
          UINT32_C(0x005BED65), UINT32_C(0x0001B9BD)}},
        {{UINT32_C(0x00761113), UINT32_C(0x0018F0E8), UINT32_C(0x003FCFC0),
          UINT32_C(0x0015BEF5), UINT32_C(0x002AEA52), UINT32_C(0x00396A8E),
          UINT32_C(0x004C295C), UINT32_C(0x001BAEDF), UINT32_C(0x00030D39),
          UINT32_C(0x005A7E8D), UINT32_C(0x002A1B12)},
         {UINT32_C(0x005CE5E2), UINT32_C(0x004CFE4B), UINT32_C(0x0030A399),
          UINT32_C(0x0040EA7A), UINT32_C(0x007F164F), UINT32_C(0x004DB6D5),
          UINT32_C(0x00686A3B), UINT32_C(0x00DBA200), UINT32_C(0x00296140),
          UINT32_C(0x004D478A), UINT32_C(0x006AD41C)}},
    },
    {
        {{UINT32_C(0x00B89E8E), UINT32_C(0x002A572E), UINT32_C(0x005B3E1F),
          UINT32_C(0x00E54DD0), UINT32_C(0x00162459), UINT32_C(0x0027E7ED),
          UINT32_C(0x005B4CC3), UINT32_C(0x00DB4F26), UINT32_C(0x007BE5F3),
          UINT32_C(0x007C9929), UINT32_C(0x00555F44)},
         {UINT32_C(0x00CFD61D), UINT32_C(0x0052D610), UINT32_C(0x0060404C),
          UINT32_C(0x003743F5), UINT32_C(0x00055514), UINT32_C(0x000267D5),
          UINT32_C(0x006087D4), UINT32_C(0x008C2531), UINT32_C(0x0051466E),
          UINT32_C(0x003C7CCA), UINT32_C(0x005C334A)}},
        {{UINT32_C(0x00E24828), UINT32_C(0x001F7423), UINT32_C(0x007D8091),
          UINT32_C(0x00316505), UINT32_C(0x0079C9BB), UINT32_C(0x007F35D0),
          UINT32_C(0x00152C91), UINT32_C(0x00E33D9C), UINT32_C(0x0052C723),
          UINT32_C(0x0058776C), UINT32_C(0x001FEDFD)},
         {UINT32_C(0x0004E1B6), UINT32_C(0x006EE7E1), UINT32_C(0x0072C66F),
          UINT32_C(0x00B26D30), UINT32_C(0x006FB6AE), UINT32_C(0x0020B88A),
          UINT32_C(0x0009F8CA), UINT32_C(0x00BCD95A), UINT32_C(0x001944E3),
          UINT32_C(0x007072F6), UINT32_C(0x005A26A1)}},
        {{UINT32_C(0x00F21E4A), UINT32_C(0x0039DE0D), UINT32_C(0x002C0B04),
          UINT32_C(0x0042B820), UINT32_C(0x00260CE6), UINT32_C(0x00469276),
          UINT32_C(0x000EFD81), UINT32_C(0x0053E1E3), UINT32_C(0x0011D541),
          UINT32_C(0x003278ED), UINT32_C(0x0024190E)},
         {UINT32_C(0x009C86A2), UINT32_C(0x004CF336), UINT32_C(0x00666B88),
          UINT32_C(0x00B34AD6), UINT32_C(0x0012660F), UINT32_C(0x004603DC),
          UINT32_C(0x001C4603), UINT32_C(0x00B51A55), UINT32_C(0x00032E05),
          UINT32_C(0x00761758), UINT32_C(0x002462A8)}},
        {{UINT32_C(0x00A8C3D3), UINT32_C(0x0042BDA6), UINT32_C(0x0073898B),
          UINT32_C(0x009F00CE), UINT32_C(0x006C9105), UINT32_C(0x0077CDB6),
          UINT32_C(0x002CCE5B), UINT32_C(0x0017C55E), UINT32_C(0x00274A09),
          UINT32_C(0x0061058B), UINT32_C(0x0025F3E2)},
         {UINT32_C(0x000C3DB3), UINT32_C(0x006143BB), UINT32_C(0x0030EF0F),
          UINT32_C(0x00E06EFA), UINT32_C(0x005B5205), UINT32_C(0x002928C1),
          UINT32_C(0x00011BD1), UINT32_C(0x004BD906), UINT32_C(0x003EA381),
          UINT32_C(0x005B8BDD), UINT32_C(0x00141CF4)}},
        {{UINT32_C(0x00B9F037), UINT32_C(0x000EB500), UINT32_C(0x00231F4E),
          UINT32_C(0x009E6670), UINT32_C(0x0022155C), UINT32_C(0x005CD76F),
          UINT32_C(0x00777013), UINT32_C(0x001E0C79), UINT32_C(0x004FFBC6),
          UINT32_C(0x0022246A), UINT32_C(0x00411134)},
         {UINT32_C(0x00D2A8D9), UINT32_C(0x0019D3D6), UINT32_C(0x002D8C16),
          UINT32_C(0x00BFC55D), UINT32_C(0x00409145), UINT32_C(0x000A3CB7),
          UINT32_C(0x00705773), UINT32_C(0x00018E32), UINT32_C(0x002A408A),
          UINT32_C(0x0002439B), UINT32_C(0x00585C81)}},
        {{UINT32_C(0x001B067A), UINT32_C(0x003501B0), UINT32_C(0x006FF5D5),
          UINT32_C(0x00266911), UINT32_C(0x00573D9E), UINT32_C(0x004CE3AE),
          UINT32_C(0x007BD3A7), UINT32_C(0x004986D0), UINT32_C(0x0036391A),
          UINT32_C(0x004E3053), UINT32_C(0x007A996A)},
         {UINT32_C(0x00371080), UINT32_C(0x0037E042), UINT32_C(0x002DCB8F),
          UINT32_C(0x00A79F2A), UINT32_C(0x000C73DB), UINT32_C(0x007B8926),
          UINT32_C(0x004BCA76), UINT32_C(0x00F1D149), UINT32_C(0x0059029B),
          UINT32_C(0x0057F067), UINT32_C(0x006743F9)}},
        {{UINT32_C(0x00939BAE), UINT32_C(0x003B8FB7), UINT32_C(0x002AC961),
          UINT32_C(0x007D0A3C), UINT32_C(0x00112565), UINT32_C(0x0015EA2E),
          UINT32_C(0x005F39D5), UINT32_C(0x008AB56C), UINT32_C(0x000E21E3),
          UINT32_C(0x00700E40), UINT32_C(0x00394DCD)},
         {UINT32_C(0x003A2295), UINT32_C(0x00027730), UINT32_C(0x0048C762),
          UINT32_C(0x009954A7), UINT32_C(0x007E32DB), UINT32_C(0x0043302E),
          UINT32_C(0x00740102), UINT32_C(0x001190E7), UINT32_C(0x0027D7B2),
          UINT32_C(0x005CEE50), UINT32_C(0x007A578D)}},
        {{UINT32_C(0x008BBA28), UINT32_C(0x004A0FD7), UINT32_C(0x007AC31D),
          UINT32_C(0x0037176E), UINT32_C(0x0076A050), UINT32_C(0x0040AB46),
          UINT32_C(0x005F95F7), UINT32_C(0x000BE19A), UINT32_C(0x00098D24),
          UINT32_C(0x005A9458), UINT32_C(0x000A1117)},
         {UINT32_C(0x00E7EE0C), UINT32_C(0x00242C41), UINT32_C(0x0023CBF4),
          UINT32_C(0x001C57A1), UINT32_C(0x003AC093), UINT32_C(0x0008AED2),
          UINT32_C(0x0030388D), UINT32_C(0x00EC955F), UINT32_C(0x00714125),
          UINT32_C(0x002CEB59), UINT32_C(0x0043A695)}},
        {{UINT32_C(0x0078C305), UINT32_C(0x0068D7A0), UINT32_C(0x0078F31C),
          UINT32_C(0x002557F3), UINT32_C(0x00154BCD), UINT32_C(0x000FD4BA),
          UINT32_C(0x0049870F), UINT32_C(0x00FD3D6C), UINT32_C(0x004A74E9),
          UINT32_C(0x0073A1EA), UINT32_C(0x00712628)},
         {UINT32_C(0x0097A50E), UINT32_C(0x003814D7), UINT32_C(0x001533AE),
          UINT32_C(0x00DC57FF), UINT32_C(0x0038CC54), UINT32_C(0x0023F786),
          UINT32_C(0x0075EAC5), UINT32_C(0x0053BC5A), UINT32_C(0x000D1025),
          UINT32_C(0x00352FF8), UINT32_C(0x007BAA67)}},
        {{UINT32_C(0x004B5256), UINT32_C(0x0074BDB3), UINT32_C(0x003A77BB),
          UINT32_C(0x00596C90), UINT32_C(0x005F0076), UINT32_C(0x00682016),
          UINT32_C(0x000D8938), UINT32_C(0x00EA0E74), UINT32_C(0x00385C25),
          UINT32_C(0x001D6446), UINT32_C(0x007F967C)},
         {UINT32_C(0x00F7351C), UINT32_C(0x005B1299), UINT32_C(0x0077959F),
          UINT32_C(0x0021D3B9), UINT32_C(0x006DF80F), UINT32_C(0x004DA025),
          UINT32_C(0x00515CCC), UINT32_C(0x009FB790), UINT32_C(0x00063FF3),
          UINT32_C(0x0076D77A), UINT32_C(0x00625098)}},
        {{UINT32_C(0x007F1E6E), UINT32_C(0x0003AF4F), UINT32_C(0x00147BDB),
          UINT32_C(0x0070598D), UINT32_C(0x0026D23F), UINT32_C(0x004A29CD),
          UINT32_C(0x007D8AFD), UINT32_C(0x00842D17), UINT32_C(0x0027D785),
          UINT32_C(0x003D4F02), UINT32_C(0x001B48F1)},
         {UINT32_C(0x001DD7E4), UINT32_C(0x0048EA30), UINT32_C(0x0031E587),
          UINT32_C(0x00DA778D), UINT32_C(0x00028302), UINT32_C(0x0002B20F),
          UINT32_C(0x002CA85D), UINT32_C(0x009940A5), UINT32_C(0x0050706A),
          UINT32_C(0x0027C655), UINT32_C(0x0065E5F7)}},
        {{UINT32_C(0x00339162), UINT32_C(0x00747568), UINT32_C(0x002DE761),
          UINT32_C(0x00ABF96A), UINT32_C(0x004ED981), UINT32_C(0x0068131A),
          UINT32_C(0x007755D1), UINT32_C(0x00A26F42), UINT32_C(0x00052444),
          UINT32_C(0x0005B629), UINT32_C(0x00374DF9)},
         {UINT32_C(0x00CF14E5), UINT32_C(0x0002B073), UINT32_C(0x002FCF27),
          UINT32_C(0x009F2568), UINT32_C(0x000C77F1), UINT32_C(0x004F9C85),
          UINT32_C(0x00564AE1), UINT32_C(0x0084E7B1), UINT32_C(0x002F9F7C),
          UINT32_C(0x0079F6BA), UINT32_C(0x0050141F)}},
        {{UINT32_C(0x005DDB32), UINT32_C(0x006EEA5F), UINT32_C(0x001A6DBD),
          UINT32_C(0x0037896C), UINT32_C(0x005312EB), UINT32_C(0x00224556),
          UINT32_C(0x005B967C), UINT32_C(0x00E31403), UINT32_C(0x00210FEC),
          UINT32_C(0x0039AB6E), UINT32_C(0x0056A5EB)},
         {UINT32_C(0x0020A6E2), UINT32_C(0x004202D4), UINT32_C(0x0049C90D),
          UINT32_C(0x00EAF4CB), UINT32_C(0x002618C6), UINT32_C(0x007417CB),
          UINT32_C(0x00662261), UINT32_C(0x0019A617), UINT32_C(0x0033B387),
          UINT32_C(0x0048BFFF), UINT32_C(0x004F79A1)}},
        {{UINT32_C(0x0058277B), UINT32_C(0x001F5BDD), UINT32_C(0x004376CA),
          UINT32_C(0x00F548FC), UINT32_C(0x00326E00), UINT32_C(0x001B763D),
          UINT32_C(0x000FD0C0), UINT32_C(0x0077DA31), UINT32_C(0x00187E89),
          UINT32_C(0x0079FC29), UINT32_C(0x007A4AF6)},
         {UINT32_C(0x006FE0E1), UINT32_C(0x00669EB7), UINT32_C(0x002BEF48),
          UINT32_C(0x00F75C67), UINT32_C(0x005F6D0F), UINT32_C(0x000A2CD9),
          UINT32_C(0x006E41E5), UINT32_C(0x003F37B9), UINT32_C(0x002D2442),
          UINT32_C(0x0036829D), UINT32_C(0x0065DA14)}},
        {{UINT32_C(0x004326A2), UINT32_C(0x0040CBD8), UINT32_C(0x00652915),
          UINT32_C(0x000F2B54), UINT32_C(0x0016C330), UINT32_C(0x0043F920),
          UINT32_C(0x0030AF01), UINT32_C(0x008C5D38), UINT32_C(0x000DEDBF),
          UINT32_C(0x00496F1B), UINT32_C(0x0035CE15)},
         {UINT32_C(0x005CD536), UINT32_C(0x0003476A), UINT32_C(0x001458E4),
          UINT32_C(0x0066184B), UINT32_C(0x001373E4), UINT32_C(0x002362B2),
          UINT32_C(0x0047ADDA), UINT32_C(0x00CFDE00), UINT32_C(0x00294ACC),
          UINT32_C(0x005B07F4), UINT32_C(0x003AAA59)}},
        {{UINT32_C(0x00351DCE), UINT32_C(0x00531F74), UINT32_C(0x0070FE9B),
          UINT32_C(0x0055D5FF), UINT32_C(0x00091F6C), UINT32_C(0x004713C4),
          UINT32_C(0x0043D715), UINT32_C(0x00133A66), UINT32_C(0x00274DDB),
          UINT32_C(0x001421FF), UINT32_C(0x00154461)},
         {UINT32_C(0x002EE3C1), UINT32_C(0x001BD275), UINT32_C(0x00205CA7),
          UINT32_C(0x008985BF), UINT32_C(0x0065F85E), UINT32_C(0x00342C28),
          UINT32_C(0x0024E23D), UINT32_C(0x004B7B25), UINT32_C(0x002AE72E),
          UINT32_C(0x004A5D4D), UINT32_C(0x00296425)}},
    },
    {
        {{UINT32_C(0x003BB6FE), UINT32_C(0x006E95CA), UINT32_C(0x007C12DB),
          UINT32_C(0x00C2B2E1), UINT32_C(0x0078CF5B), UINT32_C(0x0008D9AA),
          UINT32_C(0x00545B0C), UINT32_C(0x00966933), UINT32_C(0x005E90BF),
          UINT32_C(0x00127A50), UINT32_C(0x0021E6D5)},
         {UINT32_C(0x009BB07D), UINT32_C(0x003CE9C9), UINT32_C(0x00575061),
          UINT32_C(0x009D607C), UINT32_C(0x002C0630), UINT32_C(0x004CF2F4),
          UINT32_C(0x007C5067), UINT32_C(0x007DB3FF), UINT32_C(0x0078833C),
          UINT32_C(0x00381D12), UINT32_C(0x005C6CD7)}},
        {{UINT32_C(0x008392C8), UINT32_C(0x003BA804), UINT32_C(0x00437AEB),
          UINT32_C(0x000AA5A0), UINT32_C(0x00428295), UINT32_C(0x0022CF84),
          UINT32_C(0x00583AB2), UINT32_C(0x00E78C0A), UINT32_C(0x00076BFD),
          UINT32_C(0x00340AF2), UINT32_C(0x002C576A)},
         {UINT32_C(0x006E6DE1), UINT32_C(0x0018AAF8), UINT32_C(0x003B09EA),
          UINT32_C(0x00461A4F), UINT32_C(0x00652C71), UINT32_C(0x00773D62),
          UINT32_C(0x0007F33A), UINT32_C(0x00FB0128), UINT32_C(0x0023226A),
          UINT32_C(0x005272BE), UINT32_C(0x0077A80C)}},
        {{UINT32_C(0x00243A23), UINT32_C(0x0059EE8D), UINT32_C(0x005ABC02),
          UINT32_C(0x00A76F1C), UINT32_C(0x005303B8), UINT32_C(0x002878BC),
          UINT32_C(0x000DC6D0), UINT32_C(0x002F5739), UINT32_C(0x004328D8),
          UINT32_C(0x006619B0), UINT32_C(0x00582761)},
         {UINT32_C(0x005DD89C), UINT32_C(0x0040AF03), UINT32_C(0x00664609),
          UINT32_C(0x0064DED7), UINT32_C(0x00321F34), UINT32_C(0x00479994),
          UINT32_C(0x002A3457), UINT32_C(0x0081039C), UINT32_C(0x001FB033),
          UINT32_C(0x003E4D77), UINT32_C(0x00381514)}},
        {{UINT32_C(0x0047A6CB), UINT32_C(0x00158718), UINT32_C(0x00209322),
          UINT32_C(0x00E170DB), UINT32_C(0x00240A83), UINT32_C(0x004CD315),
          UINT32_C(0x0028E399), UINT32_C(0x005AE653), UINT32_C(0x00066B28),
          UINT32_C(0x001BBE06), UINT32_C(0x002C404C)},
         {UINT32_C(0x006A93D6), UINT32_C(0x0012DF82), UINT32_C(0x006B54F4),
          UINT32_C(0x00062344), UINT32_C(0x003FC9ED), UINT32_C(0x007DDB29),
          UINT32_C(0x00101680), UINT32_C(0x00369C91), UINT32_C(0x0074AA99),
          UINT32_C(0x00205D4F), UINT32_C(0x000E516B)}},
        {{UINT32_C(0x0032C66D), UINT32_C(0x005546A7), UINT32_C(0x006BB8DE),
          UINT32_C(0x008576D8), UINT32_C(0x00412063), UINT32_C(0x00644D0C),
          UINT32_C(0x00693A63), UINT32_C(0x004429A2), UINT32_C(0x002AFB3C),
          UINT32_C(0x005AB6A4), UINT32_C(0x0061829F)},
         {UINT32_C(0x00743208), UINT32_C(0x0077B5BB), UINT32_C(0x0060700C),
          UINT32_C(0x003878D7), UINT32_C(0x0043EA8E), UINT32_C(0x0006C990),
          UINT32_C(0x001AD279), UINT32_C(0x0037199C), UINT32_C(0x001602AC),
          UINT32_C(0x006E6867), UINT32_C(0x000200A7)}},
        {{UINT32_C(0x0005C9B6), UINT32_C(0x0070BA76), UINT32_C(0x0032093C),
          UINT32_C(0x00D7302E), UINT32_C(0x003DC75F), UINT32_C(0x0032241E),
          UINT32_C(0x0020E178), UINT32_C(0x009DB55A), UINT32_C(0x00105792),
          UINT32_C(0x00223EF7), UINT32_C(0x0004DBD3)},
         {UINT32_C(0x006731CF), UINT32_C(0x004DCCDF), UINT32_C(0x006C712B),
          UINT32_C(0x002963CA), UINT32_C(0x0033C6EB), UINT32_C(0x002918CB),
          UINT32_C(0x0042143A), UINT32_C(0x008D3632), UINT32_C(0x0047755A),
          UINT32_C(0x00258338), UINT32_C(0x00497444)}},
        {{UINT32_C(0x00ACB58A), UINT32_C(0x0003E23D), UINT32_C(0x0035FF81),
          UINT32_C(0x008936EA), UINT32_C(0x006A5CF4), UINT32_C(0x0024FFC8),
          UINT32_C(0x0061C56B), UINT32_C(0x000E421B), UINT32_C(0x005C7741),
          UINT32_C(0x006A1FF8), UINT32_C(0x006599DD)},
         {UINT32_C(0x00E19FA2), UINT32_C(0x00129BD5), UINT32_C(0x00106B22),
          UINT32_C(0x007D7780), UINT32_C(0x007F0877), UINT32_C(0x0041300E),
          UINT32_C(0x004E679A), UINT32_C(0x00EBE8AA), UINT32_C(0x001C3277),
          UINT32_C(0x006E99F9), UINT32_C(0x00427DB9)}},
        {{UINT32_C(0x00184DFD), UINT32_C(0x00147505), UINT32_C(0x0040EDB6),
          UINT32_C(0x0096E71F), UINT32_C(0x00096D68), UINT32_C(0x007288B9),
          UINT32_C(0x0039FAE7), UINT32_C(0x009E3B19), UINT32_C(0x0059525F),
          UINT32_C(0x0042A224), UINT32_C(0x0032E991)},
         {UINT32_C(0x004D3074), UINT32_C(0x0055D970), UINT32_C(0x004FF6C8),
          UINT32_C(0x0015270D), UINT32_C(0x007770DF), UINT32_C(0x0042C9FB),
          UINT32_C(0x005CA513), UINT32_C(0x007D00C3), UINT32_C(0x0030A146),
          UINT32_C(0x005D74BB), UINT32_C(0x007A9637)}},
        {{UINT32_C(0x003DB0DD), UINT32_C(0x0011ADC8), UINT32_C(0x00612266),
          UINT32_C(0x002F2C19), UINT32_C(0x002DF82D), UINT32_C(0x0028B03E),
          UINT32_C(0x006C2E98), UINT32_C(0x00FFA685), UINT32_C(0x0038F8D0),
          UINT32_C(0x003B9283), UINT32_C(0x007D2975)},
         {UINT32_C(0x007D306E), UINT32_C(0x007F5E20), UINT32_C(0x0047A55D),
          UINT32_C(0x00F980FF), UINT32_C(0x001331A2), UINT32_C(0x007D74A9),
          UINT32_C(0x005FEE08), UINT32_C(0x006A7C2A), UINT32_C(0x007C37BB),
          UINT32_C(0x0043D3B4), UINT32_C(0x006F3CFD)}},
        {{UINT32_C(0x000EC696), UINT32_C(0x005185A9), UINT32_C(0x0000BE2C),
          UINT32_C(0x009F985A), UINT32_C(0x004E2379), UINT32_C(0x005CE286),
          UINT32_C(0x005F05DF), UINT32_C(0x0086A2D2), UINT32_C(0x0072A145),
          UINT32_C(0x002B10E0), UINT32_C(0x004C8EE4)},
         {UINT32_C(0x00E985B8), UINT32_C(0x002A46B5), UINT32_C(0x002FBBBF),
          UINT32_C(0x00C296A5), UINT32_C(0x0035DCA7), UINT32_C(0x00646900),
          UINT32_C(0x003C51B5), UINT32_C(0x00211EA2), UINT32_C(0x000B20B3),
          UINT32_C(0x005E0254), UINT32_C(0x000A4FE9)}},
        {{UINT32_C(0x0086A4E4), UINT32_C(0x00394FC7), UINT32_C(0x0062AD1E),
          UINT32_C(0x0070C064), UINT32_C(0x000D4375), UINT32_C(0x0069383E),
          UINT32_C(0x006DCB72), UINT32_C(0x0068E22C), UINT32_C(0x0065F231),
          UINT32_C(0x0005483D), UINT32_C(0x005C9926)},
         {UINT32_C(0x0027C9BA), UINT32_C(0x004616BF), UINT32_C(0x00793172),
          UINT32_C(0x00E307EF), UINT32_C(0x001C58E0), UINT32_C(0x0075518F),
          UINT32_C(0x00704D77), UINT32_C(0x0096D516), UINT32_C(0x000514C7),
          UINT32_C(0x004550F6), UINT32_C(0x0002F839)}},
        {{UINT32_C(0x003EB3CB), UINT32_C(0x000B54E8), UINT32_C(0x000BB0B6),
          UINT32_C(0x000473CB), UINT32_C(0x007A4D1B), UINT32_C(0x001A1E96),
          UINT32_C(0x00676B16), UINT32_C(0x002A64AB), UINT32_C(0x0000465B),
          UINT32_C(0x0030BAF8), UINT32_C(0x0032A6EC)},
         {UINT32_C(0x007B903F), UINT32_C(0x00594A72), UINT32_C(0x005679C7),
          UINT32_C(0x0001348E), UINT32_C(0x00049ACD), UINT32_C(0x0076157B),
          UINT32_C(0x000823E7), UINT32_C(0x00956AAC), UINT32_C(0x00564C9C),
          UINT32_C(0x006B7D47), UINT32_C(0x00184705)}},
        {{UINT32_C(0x00582997), UINT32_C(0x0042C43A), UINT32_C(0x001B2A5F),
          UINT32_C(0x00C58D80), UINT32_C(0x00027426), UINT32_C(0x00767F7F),
          UINT32_C(0x0029FE8F), UINT32_C(0x0011D5E8), UINT32_C(0x005F6EDA),
          UINT32_C(0x004E2E7A), UINT32_C(0x006DA644)},
         {UINT32_C(0x005F3E5D), UINT32_C(0x007EB9DF), UINT32_C(0x00744FFF),
          UINT32_C(0x0011E773), UINT32_C(0x00365A41), UINT32_C(0x0069DCE3),
          UINT32_C(0x005910E9), UINT32_C(0x00A8DAC2), UINT32_C(0x0076022D),
          UINT32_C(0x006B2E17), UINT32_C(0x002B174E)}},
        {{UINT32_C(0x002D1DE7), UINT32_C(0x005918AF), UINT32_C(0x007117B7),
          UINT32_C(0x00FFA278), UINT32_C(0x005EC3E5), UINT32_C(0x005D5ABD),
          UINT32_C(0x00409AA7), UINT32_C(0x00B36478), UINT32_C(0x004932C5),
          UINT32_C(0x0012CD34), UINT32_C(0x00481C3A)},
         {UINT32_C(0x00FDA782), UINT32_C(0x000B2886), UINT32_C(0x00335C25),
          UINT32_C(0x002CBA0C), UINT32_C(0x0031D46B), UINT32_C(0x006D4278),
          UINT32_C(0x00222F32), UINT32_C(0x00407069), UINT32_C(0x00003013),
          UINT32_C(0x0013825F), UINT32_C(0x001F8B6C)}},
        {{UINT32_C(0x0051E4F5), UINT32_C(0x005E9DC2), UINT32_C(0x00258809),
          UINT32_C(0x00371D8C), UINT32_C(0x002FAEE6), UINT32_C(0x0004405C),
          UINT32_C(0x00203011), UINT32_C(0x00C2897B), UINT32_C(0x0045B72F),
          UINT32_C(0x00627D76), UINT32_C(0x006AECBB)},
         {UINT32_C(0x00D0F404), UINT32_C(0x003CF81A), UINT32_C(0x002F9F4D),
          UINT32_C(0x00C34800), UINT32_C(0x001E0D5A), UINT32_C(0x004CF2B9),
          UINT32_C(0x0058028D), UINT32_C(0x002FE88D), UINT32_C(0x003BCDD1),
          UINT32_C(0x005CA483), UINT32_C(0x006C66B1)}},
        {{UINT32_C(0x0012B2F8), UINT32_C(0x002EB0A2), UINT32_C(0x0012BBFE),
          UINT32_C(0x00455276), UINT32_C(0x0047B04A), UINT32_C(0x007F71B8),
          UINT32_C(0x0055BF51), UINT32_C(0x00912939), UINT32_C(0x005DD2FD),
          UINT32_C(0x001E65E5), UINT32_C(0x0044827B)},
         {UINT32_C(0x00749338), UINT32_C(0x0069B9A0), UINT32_C(0x0024E76A),
          UINT32_C(0x008505B7), UINT32_C(0x0048F45E), UINT32_C(0x004CCFF6),
          UINT32_C(0x00518F1E), UINT32_C(0x0006ABD7), UINT32_C(0x000C7B19),
          UINT32_C(0x007CF1C0), UINT32_C(0x001618E7)}},
    },
    {
        {{UINT32_C(0x0002A6B0), UINT32_C(0x0068A84D), UINT32_C(0x0004254D),
          UINT32_C(0x0073936F), UINT32_C(0x00264AA9), UINT32_C(0x002AE177),
          UINT32_C(0x005104DE), UINT32_C(0x007CF18F), UINT32_C(0x006D9C04),
          UINT32_C(0x007431AF), UINT32_C(0x000711AB)},
         {UINT32_C(0x008D5496), UINT32_C(0x003EA309), UINT32_C(0x0004F5CE),
          UINT32_C(0x00BDE7BB), UINT32_C(0x007DE3A5), UINT32_C(0x0018E586),
          UINT32_C(0x004C3DB2), UINT32_C(0x0034EF2F), UINT32_C(0x00310ED6),
          UINT32_C(0x005F5025), UINT32_C(0x0033F193)}},
        {{UINT32_C(0x00478D87), UINT32_C(0x00086B6B), UINT32_C(0x0046EB29),
          UINT32_C(0x00E16D0C), UINT32_C(0x001BDA98), UINT32_C(0x002ED05B),
          UINT32_C(0x003789D1), UINT32_C(0x00CC467C), UINT32_C(0x00178BD9),
          UINT32_C(0x005B5C2E), UINT32_C(0x00414C09)},
         {UINT32_C(0x00FE52FF), UINT32_C(0x00390639), UINT32_C(0x006FFAE6),
          UINT32_C(0x0028E37B), UINT32_C(0x001FB01B), UINT32_C(0x0016F3D8),
          UINT32_C(0x003EE18E), UINT32_C(0x00599A77), UINT32_C(0x004C13DE),
          UINT32_C(0x004B6328), UINT32_C(0x00283192)}},
        {{UINT32_C(0x00E1AB70), UINT32_C(0x004B3C06), UINT32_C(0x00203AA9),
          UINT32_C(0x00521440), UINT32_C(0x00527B5E), UINT32_C(0x00288A81),
          UINT32_C(0x0028CE1C), UINT32_C(0x0028059F), UINT32_C(0x004C148A),
          UINT32_C(0x0046ECEE), UINT32_C(0x00578C8D)},
         {UINT32_C(0x00DBD2FF), UINT32_C(0x006C80C1), UINT32_C(0x007661A2),
          UINT32_C(0x0011094F), UINT32_C(0x001AFACC), UINT32_C(0x00775ABB),
          UINT32_C(0x003CFCA0), UINT32_C(0x00030FF6), UINT32_C(0x00527900),
          UINT32_C(0x0015F70F), UINT32_C(0x00546D98)}},
        {{UINT32_C(0x00E757A2), UINT32_C(0x005BC39A), UINT32_C(0x004FFC7D),
          UINT32_C(0x00F82EB5), UINT32_C(0x006C5AD3), UINT32_C(0x0078023C),
          UINT32_C(0x00684CB8), UINT32_C(0x00CB7AE8), UINT32_C(0x005F6095),
          UINT32_C(0x0075B447), UINT32_C(0x001FF6E4)},
         {UINT32_C(0x00036FA0), UINT32_C(0x000C4B4E), UINT32_C(0x0021CA4E),
          UINT32_C(0x0046EA53), UINT32_C(0x0044A218), UINT32_C(0x0010A3C3),
          UINT32_C(0x002B3DFE), UINT32_C(0x0065108E), UINT32_C(0x0066153C),
          UINT32_C(0x0001AFCA), UINT32_C(0x0078AAB5)}},
        {{UINT32_C(0x00A3A297), UINT32_C(0x001F022D), UINT32_C(0x0048B127),
          UINT32_C(0x0084727B), UINT32_C(0x0040B276), UINT32_C(0x00723454),
          UINT32_C(0x0027403D), UINT32_C(0x00702090), UINT32_C(0x003AE544),
          UINT32_C(0x000185C8), UINT32_C(0x00396B7E)},
         {UINT32_C(0x0054F823), UINT32_C(0x0023AFDE), UINT32_C(0x000701AD),
          UINT32_C(0x00E2AE90), UINT32_C(0x002362A8), UINT32_C(0x00443B0D),
          UINT32_C(0x00539CA7), UINT32_C(0x00487F9B), UINT32_C(0x0013DFD4),
          UINT32_C(0x0060A0C9), UINT32_C(0x003ED6D3)}},
        {{UINT32_C(0x0041AFA6), UINT32_C(0x0033B2AB), UINT32_C(0x000E144F),
          UINT32_C(0x006C2256), UINT32_C(0x006FAF7B), UINT32_C(0x005AC228),
          UINT32_C(0x0032A572), UINT32_C(0x007F38E2), UINT32_C(0x006D725E),
          UINT32_C(0x002BEC51), UINT32_C(0x002D43D7)},
         {UINT32_C(0x0083A39E), UINT32_C(0x001C7564), UINT32_C(0x0008D7AC),
          UINT32_C(0x0018A604), UINT32_C(0x0074C8EB), UINT32_C(0x0063AB5D),
          UINT32_C(0x002CCD7D), UINT32_C(0x0047E981), UINT32_C(0x0021750F),
          UINT32_C(0x001135D9), UINT32_C(0x004DB0FB)}},
        {{UINT32_C(0x0064BA6C), UINT32_C(0x007B1BDB), UINT32_C(0x007CD33C),
          UINT32_C(0x00810753), UINT32_C(0x0026FB53), UINT32_C(0x0077AC90),
          UINT32_C(0x0005E560), UINT32_C(0x004BCB24), UINT32_C(0x005BA503),
          UINT32_C(0x0016BD12), UINT32_C(0x00312BDC)},
         {UINT32_C(0x0072B4CC), UINT32_C(0x0014A560), UINT32_C(0x0007F39A),
          UINT32_C(0x000A2186), UINT32_C(0x0014704E), UINT32_C(0x0031AA4B),
          UINT32_C(0x00743487), UINT32_C(0x00BF00CE), UINT32_C(0x002DDB5E),
          UINT32_C(0x00490BB0), UINT32_C(0x0069DAC1)}},
        {{UINT32_C(0x009EEC7F), UINT32_C(0x006DF14C), UINT32_C(0x0023F579),
          UINT32_C(0x00511B11), UINT32_C(0x0030486B), UINT32_C(0x00653474),
          UINT32_C(0x006F453D), UINT32_C(0x00104BBB), UINT32_C(0x001DE9B0),
          UINT32_C(0x00353041), UINT32_C(0x003312FC)},
         {UINT32_C(0x00A89852), UINT32_C(0x00125365), UINT32_C(0x00584EAD),
          UINT32_C(0x00ADA70A), UINT32_C(0x002FBF4C), UINT32_C(0x002FEA71),
          UINT32_C(0x003E0469), UINT32_C(0x001ABCC3), UINT32_C(0x000759B9),
          UINT32_C(0x00440460), UINT32_C(0x000BC673)}},
        {{UINT32_C(0x0094762E), UINT32_C(0x00288488), UINT32_C(0x006E9757),
          UINT32_C(0x004AC426), UINT32_C(0x00211BF1), UINT32_C(0x001D3A08),
          UINT32_C(0x0001EAFC), UINT32_C(0x008D5AEB), UINT32_C(0x00735E25),
          UINT32_C(0x003D07A5), UINT32_C(0x004F79A8)},
         {UINT32_C(0x0008DE0D), UINT32_C(0x000F22EC), UINT32_C(0x00265780),
          UINT32_C(0x00EF5221), UINT32_C(0x00285666), UINT32_C(0x0031A918),
          UINT32_C(0x00150FBE), UINT32_C(0x00A00DC4), UINT32_C(0x004A3CA8),
          UINT32_C(0x00792A0B), UINT32_C(0x000EEFD5)}},
        {{UINT32_C(0x00C878D8), UINT32_C(0x005C1F80), UINT32_C(0x00796F30),
          UINT32_C(0x005E40FD), UINT32_C(0x006C91B9), UINT32_C(0x007B8EB5),
          UINT32_C(0x00763E21), UINT32_C(0x008E4B3F), UINT32_C(0x0059CB82),
          UINT32_C(0x0033CF8B), UINT32_C(0x00117C19)},
         {UINT32_C(0x00697DD3), UINT32_C(0x002C5D30), UINT32_C(0x006317A0),
          UINT32_C(0x00FB399C), UINT32_C(0x00679834), UINT32_C(0x00265AA0),
          UINT32_C(0x007ECFA2), UINT32_C(0x004DFBEF), UINT32_C(0x0038AE2B),
          UINT32_C(0x0066CF5A), UINT32_C(0x001E2F38)}},
        {{UINT32_C(0x006EC447), UINT32_C(0x00636DB3), UINT32_C(0x00504563),
          UINT32_C(0x0059F3D2), UINT32_C(0x0071D652), UINT32_C(0x0006DE38),
          UINT32_C(0x00478020), UINT32_C(0x00D483F9), UINT32_C(0x000AED5C),
          UINT32_C(0x00787862), UINT32_C(0x002DACF3)},
         {UINT32_C(0x00C6C4F7), UINT32_C(0x0033C908), UINT32_C(0x000FEE1A),
          UINT32_C(0x00AA7333), UINT32_C(0x001FAF9A), UINT32_C(0x000FF2F1),
          UINT32_C(0x005B0306), UINT32_C(0x00FBE44F), UINT32_C(0x0030AC0D),
          UINT32_C(0x005C62A5), UINT32_C(0x00611050)}},
        {{UINT32_C(0x004C0908), UINT32_C(0x00068F3D), UINT32_C(0x006C33DA),
          UINT32_C(0x000B80A7), UINT32_C(0x0025B068), UINT32_C(0x007FB25B),
          UINT32_C(0x004C55A2), UINT32_C(0x008BBCF8), UINT32_C(0x0006D143),
          UINT32_C(0x0006225D), UINT32_C(0x0023F7AD)},
         {UINT32_C(0x0080DEAF), UINT32_C(0x0053B869), UINT32_C(0x0014A009),
          UINT32_C(0x0076059B), UINT32_C(0x003133C4), UINT32_C(0x007B5C31),
          UINT32_C(0x006BD859), UINT32_C(0x00B43AA4), UINT32_C(0x0074EA87),
          UINT32_C(0x0059CB83), UINT32_C(0x0008B4B9)}},
        {{UINT32_C(0x00839775), UINT32_C(0x0066DD9C), UINT32_C(0x000D8D68),
          UINT32_C(0x00D7F11B), UINT32_C(0x003ADC9E), UINT32_C(0x00257179),
          UINT32_C(0x005C5090), UINT32_C(0x0081B436), UINT32_C(0x006C5187),
          UINT32_C(0x005A0A00), UINT32_C(0x006E778B)},
         {UINT32_C(0x00E9C0BD), UINT32_C(0x001B7784), UINT32_C(0x007DB934),
          UINT32_C(0x00C972DD), UINT32_C(0x001314A2), UINT32_C(0x0006AF18),
          UINT32_C(0x006F7A8A), UINT32_C(0x00F39C4E), UINT32_C(0x006E8942),
          UINT32_C(0x0056E09B), UINT32_C(0x00786BB0)}},
        {{UINT32_C(0x00306294), UINT32_C(0x0035755D), UINT32_C(0x005A1EC5),
          UINT32_C(0x00667CAE), UINT32_C(0x007CCE97), UINT32_C(0x00240C1F),
          UINT32_C(0x0024F74A), UINT32_C(0x0052075F), UINT32_C(0x003907DD),
          UINT32_C(0x0019F30D), UINT32_C(0x00621CE6)},
         {UINT32_C(0x0045BDB1), UINT32_C(0x00633018), UINT32_C(0x0065C160),
          UINT32_C(0x00289740), UINT32_C(0x001E2F22), UINT32_C(0x00592DAF),
          UINT32_C(0x0023922D), UINT32_C(0x004DF348), UINT32_C(0x00129446),
          UINT32_C(0x007248B4), UINT32_C(0x0039074B)}},
        {{UINT32_C(0x00AA4BC7), UINT32_C(0x005726AE), UINT32_C(0x003E5213),
          UINT32_C(0x0067290D), UINT32_C(0x0063681B), UINT32_C(0x006E5545),
          UINT32_C(0x00195CCD), UINT32_C(0x008B3541), UINT32_C(0x002F83E1),
          UINT32_C(0x0056641A), UINT32_C(0x002EB12A)},
         {UINT32_C(0x006782ED), UINT32_C(0x00320ADA), UINT32_C(0x0076D1A5),
          UINT32_C(0x001573A2), UINT32_C(0x002E18BC), UINT32_C(0x002E3320),
          UINT32_C(0x00653E07), UINT32_C(0x00C666DD), UINT32_C(0x00604156),
          UINT32_C(0x007A5E4B), UINT32_C(0x0069541F)}},
        {{UINT32_C(0x00224872), UINT32_C(0x005D167D), UINT32_C(0x0010FF0C),
          UINT32_C(0x00071C46), UINT32_C(0x00548E96), UINT32_C(0x003D16E1),
          UINT32_C(0x0068BA5D), UINT32_C(0x00355F33), UINT32_C(0x006C2F4F),
          UINT32_C(0x00044AF9), UINT32_C(0x007213BD)},
         {UINT32_C(0x001A0254), UINT32_C(0x002AFCA6), UINT32_C(0x0041DAAE),
          UINT32_C(0x00D08E4A), UINT32_C(0x006D9245), UINT32_C(0x000C8A06),
          UINT32_C(0x003D8084), UINT32_C(0x00494BD0), UINT32_C(0x000ED04A),
          UINT32_C(0x003BF0A5), UINT32_C(0x000F164D)}},
    },
    {
        {{UINT32_C(0x00459019), UINT32_C(0x0055760B), UINT32_C(0x0058FB7D),
          UINT32_C(0x00A2BA8C), UINT32_C(0x004B4E88), UINT32_C(0x000E6EE4),
          UINT32_C(0x0059F945), UINT32_C(0x001789F2), UINT32_C(0x007101FD),
          UINT32_C(0x004F8DD3), UINT32_C(0x0038462A)},
         {UINT32_C(0x00ABD7B1), UINT32_C(0x003D4EC1), UINT32_C(0x00382D69),
          UINT32_C(0x006BA54F), UINT32_C(0x00518A2D), UINT32_C(0x001F4A7A),
          UINT32_C(0x002C19F0), UINT32_C(0x00CCAD3E), UINT32_C(0x003AA2CC),
          UINT32_C(0x0069D63B), UINT32_C(0x00156E38)}},
        {{UINT32_C(0x0061CF6A), UINT32_C(0x000D2F2D), UINT32_C(0x00006C04),
          UINT32_C(0x00C0A71A), UINT32_C(0x0051D1DC), UINT32_C(0x0025C398),
          UINT32_C(0x000C6829), UINT32_C(0x0044F736), UINT32_C(0x0074BA1D),
          UINT32_C(0x000F86E2), UINT32_C(0x00086CD1)},
         {UINT32_C(0x00D6C080), UINT32_C(0x0045977F), UINT32_C(0x0021EC6B),
          UINT32_C(0x00F8EB91), UINT32_C(0x0066E69F), UINT32_C(0x003B59C5),
          UINT32_C(0x001990FA), UINT32_C(0x00B03645), UINT32_C(0x0009151D),
          UINT32_C(0x00781FAF), UINT32_C(0x0052EAC1)}},
        {{UINT32_C(0x005CE371), UINT32_C(0x00339A2B), UINT32_C(0x0020CCDE),
          UINT32_C(0x00867507), UINT32_C(0x0033D597), UINT32_C(0x007923EB),
          UINT32_C(0x000C7943), UINT32_C(0x003719DF), UINT32_C(0x00423883),
          UINT32_C(0x0024AF84), UINT32_C(0x00679F03)},
         {UINT32_C(0x008B9D31), UINT32_C(0x004B0315), UINT32_C(0x00409D6E),
          UINT32_C(0x009267D2), UINT32_C(0x0030117A), UINT32_C(0x002D54F0),
          UINT32_C(0x006C6FF7), UINT32_C(0x004BF6A4), UINT32_C(0x0075CBEB),
          UINT32_C(0x003BEEB1), UINT32_C(0x000B67F0)}},
        {{UINT32_C(0x00687F73), UINT32_C(0x00403AE2), UINT32_C(0x00405A67),
          UINT32_C(0x00316CA7), UINT32_C(0x002999E7), UINT32_C(0x001FE769),
          UINT32_C(0x0026B91B), UINT32_C(0x001D6CA9), UINT32_C(0x004BEFCD),
          UINT32_C(0x004A822C), UINT32_C(0x001CFAFB)},
         {UINT32_C(0x00A8EE8B), UINT32_C(0x000ACC89), UINT32_C(0x0043B4AD),
          UINT32_C(0x00F1DA75), UINT32_C(0x0011792B), UINT32_C(0x002580D8),
          UINT32_C(0x0000B787), UINT32_C(0x00C9B58F), UINT32_C(0x004F3C97),
          UINT32_C(0x00769DFC), UINT32_C(0x000F1398)}},
        {{UINT32_C(0x00F0E827), UINT32_C(0x0071CA92), UINT32_C(0x005324EC),
          UINT32_C(0x009729F1), UINT32_C(0x0055F994), UINT32_C(0x00003EFF),
          UINT32_C(0x0017CB89), UINT32_C(0x0072853D), UINT32_C(0x00339B97),
          UINT32_C(0x00171CA7), UINT32_C(0x006B675F)},
         {UINT32_C(0x003D0D62), UINT32_C(0x007DAA7D), UINT32_C(0x006A9620),
          UINT32_C(0x00F5CFEA), UINT32_C(0x00032E5B), UINT32_C(0x00563173),
          UINT32_C(0x006F73F8), UINT32_C(0x007CCBA2), UINT32_C(0x004141E7),
          UINT32_C(0x0027173B), UINT32_C(0x003348A7)}},
        {{UINT32_C(0x00F522E3), UINT32_C(0x006F8CDC), UINT32_C(0x0009FCCC),
          UINT32_C(0x000BE430), UINT32_C(0x00303DA6), UINT32_C(0x006833B6),
          UINT32_C(0x00557FC0), UINT32_C(0x0082CFC0), UINT32_C(0x00521AA8),
          UINT32_C(0x003CA041), UINT32_C(0x001AFA64)},
         {UINT32_C(0x00304313), UINT32_C(0x004ED8C2), UINT32_C(0x0018190F),
          UINT32_C(0x009B395B), UINT32_C(0x0040816B), UINT32_C(0x007C8C36),
          UINT32_C(0x006509BF), UINT32_C(0x002E0BC9), UINT32_C(0x0022B46B),
          UINT32_C(0x002AB43C), UINT32_C(0x00283CD2)}},
        {{UINT32_C(0x000B7F74), UINT32_C(0x002AC244), UINT32_C(0x00521FC5),
          UINT32_C(0x004260D1), UINT32_C(0x00017C7D), UINT32_C(0x0008CF38),
          UINT32_C(0x006722E9), UINT32_C(0x00AD1E83), UINT32_C(0x0039112C),
          UINT32_C(0x002C7B44), UINT32_C(0x0027C0FF)},
         {UINT32_C(0x009E60B3), UINT32_C(0x0054B3F3), UINT32_C(0x006A4023),
          UINT32_C(0x000E894E), UINT32_C(0x000D8133), UINT32_C(0x00684145),
          UINT32_C(0x00375199), UINT32_C(0x000C86DC), UINT32_C(0x006B4831),
          UINT32_C(0x00148D6A), UINT32_C(0x005D0790)}},
        {{UINT32_C(0x006FC7F9), UINT32_C(0x002D0FB1), UINT32_C(0x0071895E),
          UINT32_C(0x007C322D), UINT32_C(0x0022DF86), UINT32_C(0x00329F1A),
          UINT32_C(0x004AAD46), UINT32_C(0x00A69B3A), UINT32_C(0x0058C8B6),
          UINT32_C(0x004FEC36), UINT32_C(0x001023EF)},
         {UINT32_C(0x00670612), UINT32_C(0x0068AAB3), UINT32_C(0x005C3162),
          UINT32_C(0x002C5521), UINT32_C(0x001C3176), UINT32_C(0x0042667A),
          UINT32_C(0x001631EE), UINT32_C(0x00B31014), UINT32_C(0x00670B11),
          UINT32_C(0x0024384B), UINT32_C(0x001A4A2C)}},
        {{UINT32_C(0x009994EE), UINT32_C(0x006FE440), UINT32_C(0x004F4279),
          UINT32_C(0x006A6D12), UINT32_C(0x00439B57), UINT32_C(0x007E025A),
          UINT32_C(0x002D8836), UINT32_C(0x0042C271), UINT32_C(0x0011969F),
          UINT32_C(0x0056F588), UINT32_C(0x005DD046)},
         {UINT32_C(0x00F4202A), UINT32_C(0x001B4FF6), UINT32_C(0x000F4F77),
          UINT32_C(0x002D36F2), UINT32_C(0x007AA72B), UINT32_C(0x00191711),
          UINT32_C(0x007B653C), UINT32_C(0x00F06065), UINT32_C(0x001BC81D),
          UINT32_C(0x0034BE67), UINT32_C(0x006FAA43)}},
        {{UINT32_C(0x00501DAE), UINT32_C(0x002494E4), UINT32_C(0x0064D2EB),
          UINT32_C(0x008F0CE3), UINT32_C(0x0045A611), UINT32_C(0x0064260A),
          UINT32_C(0x0075E097), UINT32_C(0x00F9238A), UINT32_C(0x003654E9),
          UINT32_C(0x005020CA), UINT32_C(0x0072482C)},
         {UINT32_C(0x00AF93E8), UINT32_C(0x005900B4), UINT32_C(0x004202A6),
          UINT32_C(0x00BEE149), UINT32_C(0x006D7724), UINT32_C(0x0071B0A1),
          UINT32_C(0x00405291), UINT32_C(0x003AD02B), UINT32_C(0x005C2036),
          UINT32_C(0x0079BE42), UINT32_C(0x000BA73D)}},
        {{UINT32_C(0x00B4074B), UINT32_C(0x004B525B), UINT32_C(0x000D26F5),
          UINT32_C(0x00DDC04B), UINT32_C(0x005C3D10), UINT32_C(0x00395397),
          UINT32_C(0x00380563), UINT32_C(0x008C1F77), UINT32_C(0x000FA839),
          UINT32_C(0x00493852), UINT32_C(0x000ED70C)},
         {UINT32_C(0x00D5040F), UINT32_C(0x0042882C), UINT32_C(0x0001C223),
          UINT32_C(0x0047D313), UINT32_C(0x00552DBD), UINT32_C(0x0029F273),
          UINT32_C(0x005D6BC9), UINT32_C(0x00BCDD77), UINT32_C(0x0042D701),
          UINT32_C(0x003FE205), UINT32_C(0x003DEBB5)}},
        {{UINT32_C(0x00BBCF14), UINT32_C(0x004FFB15), UINT32_C(0x00557302),
          UINT32_C(0x00897907), UINT32_C(0x000886E7), UINT32_C(0x00556A12),
          UINT32_C(0x0024BFFC), UINT32_C(0x00466C4A), UINT32_C(0x006D05E2),
          UINT32_C(0x00732AFC), UINT32_C(0x007196CD)},
         {UINT32_C(0x00377955), UINT32_C(0x00049F69), UINT32_C(0x004A73AF),
          UINT32_C(0x00628B4D), UINT32_C(0x007F38F0), UINT32_C(0x007A500C),
          UINT32_C(0x0006D3F0), UINT32_C(0x004A0F78), UINT32_C(0x001C6F18),
          UINT32_C(0x007E08F6), UINT32_C(0x0008B8E3)}},
        {{UINT32_C(0x00F0F157), UINT32_C(0x0006E3EE), UINT32_C(0x00636CD6),
          UINT32_C(0x001FCAAE), UINT32_C(0x00065300), UINT32_C(0x0030B114),
          UINT32_C(0x00303EFF), UINT32_C(0x0010CBD8), UINT32_C(0x002326C5),
          UINT32_C(0x007E166C), UINT32_C(0x00190C79)},
         {UINT32_C(0x006D97E1), UINT32_C(0x00306B6D), UINT32_C(0x007BD9CD),
          UINT32_C(0x00922461), UINT32_C(0x00312872), UINT32_C(0x0015DE6C),
          UINT32_C(0x00595605), UINT32_C(0x0037F071), UINT32_C(0x00165965),
          UINT32_C(0x006EBA47), UINT32_C(0x0043EC85)}},
        {{UINT32_C(0x004CC6B4), UINT32_C(0x006D0740), UINT32_C(0x005E7F6C),
          UINT32_C(0x005DF159), UINT32_C(0x002E1429), UINT32_C(0x001F4EB5),
          UINT32_C(0x00543D5B), UINT32_C(0x00E4C2B2), UINT32_C(0x0058F8F4),
          UINT32_C(0x003E2B7E), UINT32_C(0x0003FA76)},
         {UINT32_C(0x00245C81), UINT32_C(0x004A0442), UINT32_C(0x0003DB05),
          UINT32_C(0x00DB5088), UINT32_C(0x0064FD7C), UINT32_C(0x004C3547),
          UINT32_C(0x0034C017), UINT32_C(0x00ED3600), UINT32_C(0x002A72EB),
          UINT32_C(0x0048D594), UINT32_C(0x00114C4C)}},
        {{UINT32_C(0x0012CF1A), UINT32_C(0x006E0086), UINT32_C(0x00549899),
          UINT32_C(0x00C50D1B), UINT32_C(0x0048CEB3), UINT32_C(0x006765AB),
          UINT32_C(0x0061B7C5), UINT32_C(0x009F1CD0), UINT32_C(0x0009F99F),
          UINT32_C(0x0040A0A7), UINT32_C(0x004B6425)},
         {UINT32_C(0x009D7477), UINT32_C(0x00079B86), UINT32_C(0x006B9474),
          UINT32_C(0x006A4288), UINT32_C(0x006586C3), UINT32_C(0x00053F72),
          UINT32_C(0x001B756B), UINT32_C(0x00302538), UINT32_C(0x00464295),
          UINT32_C(0x0041CEF6), UINT32_C(0x000157A3)}},
        {{UINT32_C(0x00C4EB0D), UINT32_C(0x0059E444), UINT32_C(0x001B0A36),
          UINT32_C(0x009DAD3B), UINT32_C(0x005BF245), UINT32_C(0x0072C6C4),
          UINT32_C(0x00285E98), UINT32_C(0x007A4B77), UINT32_C(0x00693E04),
          UINT32_C(0x000A8AC7), UINT32_C(0x000BE084)},
         {UINT32_C(0x00929EB4), UINT32_C(0x0004AEF0), UINT32_C(0x00477054),
          UINT32_C(0x008FAC95), UINT32_C(0x002630B3), UINT32_C(0x0039BC23),
          UINT32_C(0x00199060), UINT32_C(0x0017B14E), UINT32_C(0x006F142E),
          UINT32_C(0x003060D5), UINT32_C(0x0021EDBA)}},
    },
    {
        {{UINT32_C(0x00D13256), UINT32_C(0x003F96E6), UINT32_C(0x0035573F),
          UINT32_C(0x00A11C34), UINT32_C(0x0055362D), UINT32_C(0x0019411A),
          UINT32_C(0x005F05E2), UINT32_C(0x001A9048), UINT32_C(0x003DEA90),
          UINT32_C(0x000B7D0A), UINT32_C(0x004B4171)},
         {UINT32_C(0x000FAC1A), UINT32_C(0x0023647E), UINT32_C(0x0070B3D3),
          UINT32_C(0x00805801), UINT32_C(0x00790FC6), UINT32_C(0x004C4182),
          UINT32_C(0x0021453D), UINT32_C(0x000BFF08), UINT32_C(0x0018947B),
          UINT32_C(0x0028CB8E), UINT32_C(0x0006EB4B)}},
        {{UINT32_C(0x00BE097D), UINT32_C(0x006FA126), UINT32_C(0x001BB0AA),
          UINT32_C(0x00857AF3), UINT32_C(0x0014E432), UINT32_C(0x00678F70),
          UINT32_C(0x003FE4F6), UINT32_C(0x0037969A), UINT32_C(0x004C29A5),
          UINT32_C(0x0019E68D), UINT32_C(0x00727D26)},
         {UINT32_C(0x0030F457), UINT32_C(0x0077AEFB), UINT32_C(0x001B45A0),
          UINT32_C(0x00F569CA), UINT32_C(0x004CF892), UINT32_C(0x003CD573),
          UINT32_C(0x0020564D), UINT32_C(0x00911635), UINT32_C(0x000B9CCC),
          UINT32_C(0x0043C0C2), UINT32_C(0x005ABF88)}},
        {{UINT32_C(0x0055E82E), UINT32_C(0x0062B538), UINT32_C(0x0071AFAF),
          UINT32_C(0x00DD7D6C), UINT32_C(0x002444DB), UINT32_C(0x00609BDA),
          UINT32_C(0x007E8FE1), UINT32_C(0x0047FB13), UINT32_C(0x005AA88A),
          UINT32_C(0x00632113), UINT32_C(0x000E21BF)},
         {UINT32_C(0x00E4AE88), UINT32_C(0x00549503), UINT32_C(0x006D0459),
          UINT32_C(0x0007BE5C), UINT32_C(0x003D3D1F), UINT32_C(0x00105F67),
          UINT32_C(0x0070D576), UINT32_C(0x008E68E3), UINT32_C(0x0066B27F),
          UINT32_C(0x000F81A1), UINT32_C(0x00102F63)}},
        {{UINT32_C(0x00813696), UINT32_C(0x001896A4), UINT32_C(0x007C71B5),
          UINT32_C(0x008F5BBA), UINT32_C(0x003BF0D4), UINT32_C(0x002B5A1F),
          UINT32_C(0x00688FCE), UINT32_C(0x00DC932F), UINT32_C(0x00178F0B),
          UINT32_C(0x00532936), UINT32_C(0x003DC4E4)},
         {UINT32_C(0x00FF3364), UINT32_C(0x0000987D), UINT32_C(0x00050B87),
          UINT32_C(0x00DD7B60), UINT32_C(0x004C9AC5), UINT32_C(0x00050AC9),
          UINT32_C(0x00305FCA), UINT32_C(0x00FEBB2B), UINT32_C(0x004CCD89),
          UINT32_C(0x0045D609), UINT32_C(0x00383954)}},
        {{UINT32_C(0x00C5F8C7), UINT32_C(0x00059C15), UINT32_C(0x005D6652),
          UINT32_C(0x00395738), UINT32_C(0x0033E177), UINT32_C(0x00114250),
          UINT32_C(0x0050F27A), UINT32_C(0x00575EF9), UINT32_C(0x003EC09D),
          UINT32_C(0x00587A84), UINT32_C(0x0066F8F9)},
         {UINT32_C(0x0084DCF0), UINT32_C(0x0053AC30), UINT32_C(0x006B3DE9),
          UINT32_C(0x006D8108), UINT32_C(0x005E091B), UINT32_C(0x0074FBF5),
          UINT32_C(0x0079C383), UINT32_C(0x0091FDE9), UINT32_C(0x0059CDF0),
          UINT32_C(0x0063D52A), UINT32_C(0x00374429)}},
        {{UINT32_C(0x00AC9B9F), UINT32_C(0x00049F51), UINT32_C(0x003B74B2),
          UINT32_C(0x00C116DA), UINT32_C(0x007C29A7), UINT32_C(0x0070299B),
          UINT32_C(0x005E9ACC), UINT32_C(0x0003167B), UINT32_C(0x002BF1BE),
          UINT32_C(0x004D9F3F), UINT32_C(0x004EE7A0)},
         {UINT32_C(0x00917C14), UINT32_C(0x003EBF02), UINT32_C(0x000420F7),
          UINT32_C(0x0098B80D), UINT32_C(0x00316402), UINT32_C(0x00299C9E),
          UINT32_C(0x00788C59), UINT32_C(0x004CA51A), UINT32_C(0x0032E98D),
          UINT32_C(0x00523F2B), UINT32_C(0x0025FE7C)}},
        {{UINT32_C(0x00D24FB4), UINT32_C(0x000FA78F), UINT32_C(0x0075EB38),
          UINT32_C(0x00E1423C), UINT32_C(0x002F6852), UINT32_C(0x0054E6AA),
          UINT32_C(0x003B4EBA), UINT32_C(0x00916685), UINT32_C(0x000987EA),
          UINT32_C(0x007E5CAE), UINT32_C(0x001AEF85)},
         {UINT32_C(0x00BEBDCB), UINT32_C(0x0062C59D), UINT32_C(0x007EC199),
          UINT32_C(0x004ABAEB), UINT32_C(0x0026E8BC), UINT32_C(0x0045113C),
          UINT32_C(0x000926F3), UINT32_C(0x006EC500), UINT32_C(0x001E9D22),
          UINT32_C(0x0029BEE0), UINT32_C(0x00043DEE)}},
        {{UINT32_C(0x00409462), UINT32_C(0x001CB8CD), UINT32_C(0x001F8EFF),
          UINT32_C(0x0097AA77), UINT32_C(0x003B581E), UINT32_C(0x007159CB),
          UINT32_C(0x006E4ADC), UINT32_C(0x0037066B), UINT32_C(0x002D6F60),
          UINT32_C(0x006DEC62), UINT32_C(0x003829D7)},
         {UINT32_C(0x00174A3E), UINT32_C(0x0048A317), UINT32_C(0x002691C2),
          UINT32_C(0x004D7514), UINT32_C(0x004893B0), UINT32_C(0x006C7BDC),
          UINT32_C(0x007270A6), UINT32_C(0x001CD34C), UINT32_C(0x0076F518),
          UINT32_C(0x004C90E7), UINT32_C(0x005F393C)}},
        {{UINT32_C(0x0099A837), UINT32_C(0x0058E783), UINT32_C(0x0016D7D7),
          UINT32_C(0x00AB0113), UINT32_C(0x007BF6C9), UINT32_C(0x005FFC67),
          UINT32_C(0x000FA10D), UINT32_C(0x000805E8), UINT32_C(0x00627967),
          UINT32_C(0x006D7987), UINT32_C(0x0064D848)},
         {UINT32_C(0x001FE2E3), UINT32_C(0x003499ED), UINT32_C(0x0007F453),
          UINT32_C(0x0006EA4B), UINT32_C(0x006C77A1), UINT32_C(0x00131F20),
          UINT32_C(0x0028B0D4), UINT32_C(0x00301339), UINT32_C(0x0010A30B),
          UINT32_C(0x0078F588), UINT32_C(0x0056FFF0)}},
        {{UINT32_C(0x00C31BB5), UINT32_C(0x00440BEA), UINT32_C(0x006F3A2D),
          UINT32_C(0x0085FD9F), UINT32_C(0x00488063), UINT32_C(0x0037200F),
          UINT32_C(0x00199136), UINT32_C(0x0037CD50), UINT32_C(0x0008D83E),
          UINT32_C(0x007A26BF), UINT32_C(0x000F7F08)},
         {UINT32_C(0x00A878F2), UINT32_C(0x002B8B71), UINT32_C(0x005C80D4),
          UINT32_C(0x00147855), UINT32_C(0x00649415), UINT32_C(0x00445B94),
          UINT32_C(0x0038DF88), UINT32_C(0x00D5F5B6), UINT32_C(0x004FFA47),
          UINT32_C(0x00295AC4), UINT32_C(0x0041EA46)}},
        {{UINT32_C(0x007EB48E), UINT32_C(0x00012787), UINT32_C(0x00646010),
          UINT32_C(0x00D4B80D), UINT32_C(0x0019653C), UINT32_C(0x002A25D0),
          UINT32_C(0x007425A7), UINT32_C(0x0010550F), UINT32_C(0x001D6D43),
          UINT32_C(0x00494525), UINT32_C(0x003DD845)},
         {UINT32_C(0x0097B9CF), UINT32_C(0x00389128), UINT32_C(0x0063AB07),
          UINT32_C(0x00FFB646), UINT32_C(0x00049A92), UINT32_C(0x003B30F9),
          UINT32_C(0x004092BA), UINT32_C(0x005BB91C), UINT32_C(0x0045BE82),
          UINT32_C(0x001D89D6), UINT32_C(0x0006D769)}},
        {{UINT32_C(0x00A9C944), UINT32_C(0x001E9FDE), UINT32_C(0x0005084B),
          UINT32_C(0x0037B0AB), UINT32_C(0x007DF90A), UINT32_C(0x002BFE80),
          UINT32_C(0x0014A4BD), UINT32_C(0x0099723A), UINT32_C(0x0062FDC9),
          UINT32_C(0x00450631), UINT32_C(0x0056C69D)},
         {UINT32_C(0x006E31BC), UINT32_C(0x00223A2C), UINT32_C(0x00426DB3),
          UINT32_C(0x00B65794), UINT32_C(0x004EF40F), UINT32_C(0x0037E688),
          UINT32_C(0x006EDA79), UINT32_C(0x004A47E5), UINT32_C(0x007B715C),
          UINT32_C(0x0039DB43), UINT32_C(0x002E56F6)}},
        {{UINT32_C(0x00178EE8), UINT32_C(0x001F6068), UINT32_C(0x002959E2),
          UINT32_C(0x007567A4), UINT32_C(0x002ACB5C), UINT32_C(0x0045B5EE),
          UINT32_C(0x0071A9D8), UINT32_C(0x003E517A), UINT32_C(0x006040CD),
          UINT32_C(0x0036CE20), UINT32_C(0x0005D54B)},
         {UINT32_C(0x00F4423F), UINT32_C(0x0017E6E5), UINT32_C(0x004BBF02),
          UINT32_C(0x00DF4DDE), UINT32_C(0x00683FEE), UINT32_C(0x00773399),
          UINT32_C(0x006F6378), UINT32_C(0x005109B9), UINT32_C(0x007F1264),
          UINT32_C(0x001500B6), UINT32_C(0x0023E4D2)}},
        {{UINT32_C(0x009D0DE5), UINT32_C(0x0040EAC6), UINT32_C(0x00628047),
          UINT32_C(0x00CC7720), UINT32_C(0x005595B3), UINT32_C(0x00342909),
          UINT32_C(0x0006815D), UINT32_C(0x007EAF87), UINT32_C(0x00743C32),
          UINT32_C(0x000D28F3), UINT32_C(0x0013C70B)},
         {UINT32_C(0x007DE7B7), UINT32_C(0x007A74CF), UINT32_C(0x0001C3FC),
          UINT32_C(0x00660203), UINT32_C(0x00709164), UINT32_C(0x0073A917),
          UINT32_C(0x007491A0), UINT32_C(0x00A50585), UINT32_C(0x00632019),
          UINT32_C(0x005493D0), UINT32_C(0x0073CF34)}},
        {{UINT32_C(0x00FDF621), UINT32_C(0x000BB4A9), UINT32_C(0x006CD94F),
          UINT32_C(0x00BC87F4), UINT32_C(0x000A8610), UINT32_C(0x00573CFA),
          UINT32_C(0x00079CF0), UINT32_C(0x00DE2D6B), UINT32_C(0x002DB8E4),
          UINT32_C(0x000C2B46), UINT32_C(0x004B624E)},
         {UINT32_C(0x00ADE692), UINT32_C(0x000D62B4), UINT32_C(0x004F204F),
          UINT32_C(0x001E7763), UINT32_C(0x0066BF28), UINT32_C(0x0011A4DF),
          UINT32_C(0x005A54FD), UINT32_C(0x002663A8), UINT32_C(0x0028E3BA),
          UINT32_C(0x005F9DDD), UINT32_C(0x007C882C)}},
        {{UINT32_C(0x00112390), UINT32_C(0x00423B09), UINT32_C(0x0052630B),
          UINT32_C(0x0047D4BA), UINT32_C(0x002C3042), UINT32_C(0x000C6F6F),
          UINT32_C(0x0058A3E8), UINT32_C(0x00742857), UINT32_C(0x007610BB),
          UINT32_C(0x0075D1B9), UINT32_C(0x00255756)},
         {UINT32_C(0x00FDF0D5), UINT32_C(0x006197B7), UINT32_C(0x0072CA11),
          UINT32_C(0x00DF93D8), UINT32_C(0x00202449), UINT32_C(0x006E01FA),
          UINT32_C(0x0027C172), UINT32_C(0x0055A82D), UINT32_C(0x0066E3DE),
          UINT32_C(0x006A6AE7), UINT32_C(0x001A25FD)}},
    },
    {
        {{UINT32_C(0x00661C25), UINT32_C(0x00028E38), UINT32_C(0x006AD36B),
          UINT32_C(0x00F0DCCF), UINT32_C(0x0032B052), UINT32_C(0x0032C71C),
          UINT32_C(0x00211A09), UINT32_C(0x0098A311), UINT32_C(0x000826E3),
          UINT32_C(0x0021933B), UINT32_C(0x001245D9)},
         {UINT32_C(0x00BE5F68), UINT32_C(0x003D2384), UINT32_C(0x000578BD),
          UINT32_C(0x00F8E413), UINT32_C(0x004061A5), UINT32_C(0x000FF2F1),
          UINT32_C(0x00132EBC), UINT32_C(0x000E1BDF), UINT32_C(0x003265A0),
          UINT32_C(0x0052091E), UINT32_C(0x0011FFCC)}},
        {{UINT32_C(0x00B211E9), UINT32_C(0x000495DB), UINT32_C(0x0009CB82),
          UINT32_C(0x0010BDF3), UINT32_C(0x004D6E95), UINT32_C(0x006DE15A),
          UINT32_C(0x003F2B0E), UINT32_C(0x00F8B166), UINT32_C(0x00569192),
          UINT32_C(0x004EC0D5), UINT32_C(0x0054EF20)},
         {UINT32_C(0x005200C4), UINT32_C(0x00391A11), UINT32_C(0x007E47D8),
          UINT32_C(0x0024674D), UINT32_C(0x0010FC4E), UINT32_C(0x0025192E),
          UINT32_C(0x005D1C48), UINT32_C(0x00983F2B), UINT32_C(0x00568B3D),
          UINT32_C(0x0011FCC9), UINT32_C(0x00322423)}},
        {{UINT32_C(0x0046A583), UINT32_C(0x006D46EC), UINT32_C(0x0009EF4F),
          UINT32_C(0x008A2373), UINT32_C(0x006760E4), UINT32_C(0x003AE52C),
          UINT32_C(0x006D3FE9), UINT32_C(0x00CFF4AA), UINT32_C(0x002A1967),
          UINT32_C(0x006C501C), UINT32_C(0x002450E4)},
         {UINT32_C(0x00477329), UINT32_C(0x002FF478), UINT32_C(0x0079A10F),
          UINT32_C(0x002240F4), UINT32_C(0x002A6335), UINT32_C(0x001DA909),
          UINT32_C(0x004DF841), UINT32_C(0x00B8ED8E), UINT32_C(0x0067037E),
          UINT32_C(0x006BAEF5), UINT32_C(0x00142BEB)}},
        {{UINT32_C(0x0076391E), UINT32_C(0x001FA456), UINT32_C(0x0024E1D6),
          UINT32_C(0x00A4BBB7), UINT32_C(0x006733A5), UINT32_C(0x001C3AFD),
          UINT32_C(0x0070A4C3), UINT32_C(0x00195BE7), UINT32_C(0x006E2E3B),
          UINT32_C(0x007153CF), UINT32_C(0x0019A09A)},
         {UINT32_C(0x00153BB1), UINT32_C(0x004A58A8), UINT32_C(0x00708EE5),
          UINT32_C(0x00CDFFD9), UINT32_C(0x00482EC4), UINT32_C(0x0065D09A),
          UINT32_C(0x000820C4), UINT32_C(0x00CCB628), UINT32_C(0x004F2AD3),
          UINT32_C(0x004A6C74), UINT32_C(0x001936EB)}},
        {{UINT32_C(0x0089A595), UINT32_C(0x00226302), UINT32_C(0x0074A6F3),
          UINT32_C(0x00A33F36), UINT32_C(0x007C9032), UINT32_C(0x0067DB9B),
          UINT32_C(0x0001EB7F), UINT32_C(0x00F3F17E), UINT32_C(0x005ADA9D),
          UINT32_C(0x007218E4), UINT32_C(0x000C8AA9)},
         {UINT32_C(0x00C7F8B4), UINT32_C(0x00762B7E), UINT32_C(0x007B3451),
          UINT32_C(0x0073575C), UINT32_C(0x002013D6), UINT32_C(0x00638E89),
          UINT32_C(0x00244AD5), UINT32_C(0x009B033E), UINT32_C(0x00226644),
          UINT32_C(0x001E7EB2), UINT32_C(0x0054FC0E)}},
        {{UINT32_C(0x00920FA0), UINT32_C(0x007A9F5E), UINT32_C(0x0027DD8D),
          UINT32_C(0x00DC2927), UINT32_C(0x002C9E2D), UINT32_C(0x0041E6F3),
          UINT32_C(0x000A2C15), UINT32_C(0x00B02047), UINT32_C(0x006CE1AA),
          UINT32_C(0x0028537F), UINT32_C(0x00044A79)},
         {UINT32_C(0x00D1B513), UINT32_C(0x003A87FE), UINT32_C(0x00414D56),
          UINT32_C(0x00394DF6), UINT32_C(0x00675F9F), UINT32_C(0x006536DB),
          UINT32_C(0x0008F83A), UINT32_C(0x00AF6B36), UINT32_C(0x0042BF63),
          UINT32_C(0x001324F1), UINT32_C(0x0069E23F)}},
        {{UINT32_C(0x00126519), UINT32_C(0x002072D6), UINT32_C(0x004F3495),
          UINT32_C(0x00801841), UINT32_C(0x0034FA6F), UINT32_C(0x004A41FC),
          UINT32_C(0x000C3620), UINT32_C(0x009F3555), UINT32_C(0x0050B1BB),
          UINT32_C(0x007148F3), UINT32_C(0x003BC5F3)},
         {UINT32_C(0x0005E3BB), UINT32_C(0x00091662), UINT32_C(0x00480F10),
          UINT32_C(0x00526739), UINT32_C(0x005371E1), UINT32_C(0x0063F1FC),
          UINT32_C(0x004D071E), UINT32_C(0x0091714C), UINT32_C(0x0023F077),
          UINT32_C(0x00692954), UINT32_C(0x0030E9A8)}},
        {{UINT32_C(0x00C73EE8), UINT32_C(0x002829DC), UINT32_C(0x0069D5A7),
          UINT32_C(0x0075EE6F), UINT32_C(0x000F85A6), UINT32_C(0x0045F30F),
          UINT32_C(0x00407EA5), UINT32_C(0x00F70774), UINT32_C(0x0045482D),
          UINT32_C(0x0013AE44), UINT32_C(0x00547FF8)},
         {UINT32_C(0x00F7F25A), UINT32_C(0x002B2910), UINT32_C(0x004B44EA),
          UINT32_C(0x00DDE34B), UINT32_C(0x003285A3), UINT32_C(0x00558E05),
          UINT32_C(0x00468DC2), UINT32_C(0x00411298), UINT32_C(0x00509EF8),
          UINT32_C(0x007F842C), UINT32_C(0x007609FD)}},
        {{UINT32_C(0x0005F2D5), UINT32_C(0x002E2EC3), UINT32_C(0x0049F536),
          UINT32_C(0x005AEF1B), UINT32_C(0x001ECE26), UINT32_C(0x0032D05A),
          UINT32_C(0x0017585A), UINT32_C(0x00DB0102), UINT32_C(0x007FA137),
          UINT32_C(0x0056FFCC), UINT32_C(0x000943C5)},
         {UINT32_C(0x00A42C24), UINT32_C(0x0048A53E), UINT32_C(0x001397BC),
          UINT32_C(0x009E2E56), UINT32_C(0x00307F5D), UINT32_C(0x001B73E3),
          UINT32_C(0x003F6900), UINT32_C(0x00122D6A), UINT32_C(0x00769EBF),
          UINT32_C(0x0016A45C), UINT32_C(0x0077A9E1)}},
        {{UINT32_C(0x0065F9CF), UINT32_C(0x004AC108), UINT32_C(0x0067432F),
          UINT32_C(0x002DF4D6), UINT32_C(0x004DCF5E), UINT32_C(0x003BAEC7),
          UINT32_C(0x00557847), UINT32_C(0x0052362A), UINT32_C(0x003955FF),
          UINT32_C(0x002D7043), UINT32_C(0x004CA5DC)},
         {UINT32_C(0x007A93D2), UINT32_C(0x0036376B), UINT32_C(0x003A5A14),
          UINT32_C(0x0002555D), UINT32_C(0x00469862), UINT32_C(0x007BDBE1),
          UINT32_C(0x003081C9), UINT32_C(0x009C449C), UINT32_C(0x0047E01C),
          UINT32_C(0x00485605), UINT32_C(0x0031010E)}},
        {{UINT32_C(0x009EB768), UINT32_C(0x005F172B), UINT32_C(0x0068912F),
          UINT32_C(0x003400EE), UINT32_C(0x0040F25E), UINT32_C(0x00255B9D),
          UINT32_C(0x0030FC1D), UINT32_C(0x006B60A7), UINT32_C(0x00652D66),
          UINT32_C(0x004EE313), UINT32_C(0x0046178A)},
         {UINT32_C(0x007BD0F4), UINT32_C(0x005D4ECB), UINT32_C(0x00458AC2),
          UINT32_C(0x00370A5B), UINT32_C(0x0045F7B0), UINT32_C(0x0046CD90),
          UINT32_C(0x007969E2), UINT32_C(0x007A733E), UINT32_C(0x0042A4F0),
          UINT32_C(0x00118C36), UINT32_C(0x00389CCC)}},
        {{UINT32_C(0x00C27F78), UINT32_C(0x00532F03), UINT32_C(0x007E9878),
          UINT32_C(0x009B0A2A), UINT32_C(0x0034B652), UINT32_C(0x0028506E),
          UINT32_C(0x006E5FC8), UINT32_C(0x00EFAE69), UINT32_C(0x00034D96),
          UINT32_C(0x006780F0), UINT32_C(0x002CA851)},
         {UINT32_C(0x004910F2), UINT32_C(0x00144032), UINT32_C(0x00441EEB),
          UINT32_C(0x007F4CCD), UINT32_C(0x0018F7CA), UINT32_C(0x005C2F72),
          UINT32_C(0x007741E6), UINT32_C(0x00DEB207), UINT32_C(0x00060639),
          UINT32_C(0x00517FDD), UINT32_C(0x004332FA)}},
        {{UINT32_C(0x00D5681D), UINT32_C(0x00664ABF), UINT32_C(0x00246FCC),
          UINT32_C(0x003A300A), UINT32_C(0x006F7A1D), UINT32_C(0x004926CA),
          UINT32_C(0x0015B100), UINT32_C(0x001EB359), UINT32_C(0x004A94F0),
          UINT32_C(0x0006716C), UINT32_C(0x00107D89)},
         {UINT32_C(0x001231EC), UINT32_C(0x002B9FEE), UINT32_C(0x00406E9E),
          UINT32_C(0x008F6901), UINT32_C(0x003B5EDE), UINT32_C(0x0043CD20),
          UINT32_C(0x00704693), UINT32_C(0x00887628), UINT32_C(0x004FA46E),
          UINT32_C(0x0058B580), UINT32_C(0x00313AA5)}},
        {{UINT32_C(0x0034CBFC), UINT32_C(0x0070E13A), UINT32_C(0x001631DB),
          UINT32_C(0x00C5D9F6), UINT32_C(0x001D00D8), UINT32_C(0x001359B4),
          UINT32_C(0x003A435C), UINT32_C(0x0031919E), UINT32_C(0x0065E412),
          UINT32_C(0x003B76A2), UINT32_C(0x0000EC08)},
         {UINT32_C(0x0016F36E), UINT32_C(0x007A9BFE), UINT32_C(0x0017CEF7),
          UINT32_C(0x005B0D1B), UINT32_C(0x00062CAC), UINT32_C(0x005DE55E),
          UINT32_C(0x007A8F81), UINT32_C(0x007661FA), UINT32_C(0x007ADB68),
          UINT32_C(0x0064A4D8), UINT32_C(0x00392C5D)}},
        {{UINT32_C(0x0074F552), UINT32_C(0x004A7096), UINT32_C(0x000D3EC7),
          UINT32_C(0x001A8192), UINT32_C(0x003E550A), UINT32_C(0x006C7B5B),
          UINT32_C(0x00454715), UINT32_C(0x00F31192), UINT32_C(0x000658B1),
          UINT32_C(0x00611F71), UINT32_C(0x007FC5BB)},
         {UINT32_C(0x00562488), UINT32_C(0x000D96D1), UINT32_C(0x00523EA9),
          UINT32_C(0x0075587F), UINT32_C(0x001E1936), UINT32_C(0x007D2745),
          UINT32_C(0x0012EC03), UINT32_C(0x00BA0FEE), UINT32_C(0x002C5241),
          UINT32_C(0x001D4B8A), UINT32_C(0x002562EC)}},
        {{UINT32_C(0x00C4F8B3), UINT32_C(0x007D2D04), UINT32_C(0x0030B04E),
          UINT32_C(0x003424A6), UINT32_C(0x004124E9), UINT32_C(0x00528C55),
          UINT32_C(0x00349B79), UINT32_C(0x0098B5C0), UINT32_C(0x0025B021),
          UINT32_C(0x0060681C), UINT32_C(0x00387899)},
         {UINT32_C(0x0090C1FF), UINT32_C(0x005232B8), UINT32_C(0x004F63C3),
          UINT32_C(0x00E38A8E), UINT32_C(0x003E60AE), UINT32_C(0x001130C4),
          UINT32_C(0x004BF3A0), UINT32_C(0x00E168A5), UINT32_C(0x00061555),
          UINT32_C(0x000C2B98), UINT32_C(0x005FA4E4)}},
    },
    {
        {{UINT32_C(0x00E21CA1), UINT32_C(0x000C87FB), UINT32_C(0x002DD3D7),
          UINT32_C(0x00537514), UINT32_C(0x00509919), UINT32_C(0x00010447),
          UINT32_C(0x0025E6A6), UINT32_C(0x006B441D), UINT32_C(0x005E97AA),
          UINT32_C(0x00660203), UINT32_C(0x0058FBFA)},
         {UINT32_C(0x006C47E6), UINT32_C(0x0031B2BD), UINT32_C(0x007BBA31),
          UINT32_C(0x00B0AD4B), UINT32_C(0x00787301), UINT32_C(0x002B01EB),
          UINT32_C(0x001E2E68), UINT32_C(0x005811EC), UINT32_C(0x006D2B81),
          UINT32_C(0x00700E9C), UINT32_C(0x000457D9)}},
        {{UINT32_C(0x005357E1), UINT32_C(0x0057AC0E), UINT32_C(0x000DB286),
          UINT32_C(0x00EEE3D1), UINT32_C(0x0077ED80), UINT32_C(0x000E6D08),
          UINT32_C(0x000E2936), UINT32_C(0x004ABA0F), UINT32_C(0x005F2976),
          UINT32_C(0x007CC785), UINT32_C(0x003AE110)},
         {UINT32_C(0x00945699), UINT32_C(0x002637EE), UINT32_C(0x0027992C),
          UINT32_C(0x00BD9CD8), UINT32_C(0x0070F927), UINT32_C(0x000393E4),
          UINT32_C(0x006F8B67), UINT32_C(0x0061AAEC), UINT32_C(0x006328E7),
          UINT32_C(0x005B3FD0), UINT32_C(0x00268F9A)}},
        {{UINT32_C(0x00F7A85C), UINT32_C(0x003F3302), UINT32_C(0x0026F852),
          UINT32_C(0x001C40E9), UINT32_C(0x001E2517), UINT32_C(0x0005A3BF),
          UINT32_C(0x005700CE), UINT32_C(0x00561812), UINT32_C(0x003C7E74),
          UINT32_C(0x0066B1B5), UINT32_C(0x0007B9C2)},
         {UINT32_C(0x00124E6A), UINT32_C(0x007C15BA), UINT32_C(0x00241638),
          UINT32_C(0x005A50ED), UINT32_C(0x002A5582), UINT32_C(0x0074D879),
          UINT32_C(0x0068F177), UINT32_C(0x0066CA1D), UINT32_C(0x0058D9CB),
          UINT32_C(0x006724E5), UINT32_C(0x00246C5E)}},
        {{UINT32_C(0x004D0432), UINT32_C(0x000B0784), UINT32_C(0x00482F19),
          UINT32_C(0x00EA947D), UINT32_C(0x002C2664), UINT32_C(0x000C8DA3),
          UINT32_C(0x002F7290), UINT32_C(0x001B3BBF), UINT32_C(0x00602C23),
          UINT32_C(0x002D7127), UINT32_C(0x0076EBD4)},
         {UINT32_C(0x00371D4D), UINT32_C(0x003A8832), UINT32_C(0x000D8BB0),
          UINT32_C(0x00067D63), UINT32_C(0x003498C7), UINT32_C(0x0072BF20),
          UINT32_C(0x0064F598), UINT32_C(0x00B95CE3), UINT32_C(0x00587A7D),
          UINT32_C(0x0055FFCB), UINT32_C(0x0051077A)}},
        {{UINT32_C(0x009C7745), UINT32_C(0x007F8E19), UINT32_C(0x00083AF5),
          UINT32_C(0x00DB23A3), UINT32_C(0x00747602), UINT32_C(0x005F6E8D),
          UINT32_C(0x00678701), UINT32_C(0x00974612), UINT32_C(0x0056DFDD),
          UINT32_C(0x002FE8DB), UINT32_C(0x00306683)},
         {UINT32_C(0x0089FFA4), UINT32_C(0x00121679), UINT32_C(0x0079120C),
          UINT32_C(0x000BEDC4), UINT32_C(0x0029B7E4), UINT32_C(0x00496C88),
          UINT32_C(0x00760536), UINT32_C(0x00C8DB5D), UINT32_C(0x00130589),
          UINT32_C(0x001DE527), UINT32_C(0x004E350B)}},
        {{UINT32_C(0x0047CC2D), UINT32_C(0x003F02AF), UINT32_C(0x007A859E),
          UINT32_C(0x00E23690), UINT32_C(0x00649380), UINT32_C(0x0058D4FE),
          UINT32_C(0x00593197), UINT32_C(0x0003C24F), UINT32_C(0x0016F98F),
          UINT32_C(0x003046CD), UINT32_C(0x005CE122)},
         {UINT32_C(0x0050D389), UINT32_C(0x00007E53), UINT32_C(0x004B3FCC),
          UINT32_C(0x000AB3D0), UINT32_C(0x00103BDD), UINT32_C(0x002F2450),
          UINT32_C(0x0034780F), UINT32_C(0x00C242E0), UINT32_C(0x00269889),
          UINT32_C(0x003E5ACB), UINT32_C(0x0076919B)}},
        {{UINT32_C(0x000BB75C), UINT32_C(0x0040DD3F), UINT32_C(0x0028965D),
          UINT32_C(0x00D08696), UINT32_C(0x006F3945), UINT32_C(0x00756F6B),
          UINT32_C(0x00037271), UINT32_C(0x001C20DD), UINT32_C(0x0037BED6),
          UINT32_C(0x00080F61), UINT32_C(0x00274CFC)},
         {UINT32_C(0x001C1CFE), UINT32_C(0x004D42DD), UINT32_C(0x003FA1F1),
          UINT32_C(0x00CBE07F), UINT32_C(0x00277815), UINT32_C(0x001262DC),
          UINT32_C(0x00477DDE), UINT32_C(0x0048F59B), UINT32_C(0x0065A5A3),
          UINT32_C(0x005FDC99), UINT32_C(0x0064FF50)}},
        {{UINT32_C(0x00D66C07), UINT32_C(0x0074289B), UINT32_C(0x005E8814),
          UINT32_C(0x00B92793), UINT32_C(0x0057F706), UINT32_C(0x0059075D),
          UINT32_C(0x00508B5E), UINT32_C(0x009A2426), UINT32_C(0x007F10BB),
          UINT32_C(0x000AFD3F), UINT32_C(0x007801CB)},
         {UINT32_C(0x0051B22E), UINT32_C(0x00279999), UINT32_C(0x0001782D),
          UINT32_C(0x00DC4C85), UINT32_C(0x002439CE), UINT32_C(0x0055AA49),
          UINT32_C(0x00708E58), UINT32_C(0x0003EA14), UINT32_C(0x00463ED7),
          UINT32_C(0x00717B6E), UINT32_C(0x0034176A)}},
        {{UINT32_C(0x00585B7C), UINT32_C(0x0038AE8B), UINT32_C(0x006C127E),
          UINT32_C(0x003D794C), UINT32_C(0x001195F4), UINT32_C(0x005DE000),
          UINT32_C(0x000771A5), UINT32_C(0x00C99CF0), UINT32_C(0x000765DC),
          UINT32_C(0x0002A789), UINT32_C(0x00048FD2)},
         {UINT32_C(0x00482FC7), UINT32_C(0x00275057), UINT32_C(0x00095B7B),
          UINT32_C(0x0078B02B), UINT32_C(0x000B1596), UINT32_C(0x0073B9FE),
          UINT32_C(0x0045D41C), UINT32_C(0x002919F9), UINT32_C(0x0056F856),
          UINT32_C(0x001D4916), UINT32_C(0x007DB0CE)}},
        {{UINT32_C(0x001D7FC7), UINT32_C(0x006BED2D), UINT32_C(0x000B2844),
          UINT32_C(0x003F5459), UINT32_C(0x007CA010), UINT32_C(0x0024D59F),
          UINT32_C(0x007F0E34), UINT32_C(0x003EB935), UINT32_C(0x006FDF55),
          UINT32_C(0x0037C092), UINT32_C(0x004207CB)},
         {UINT32_C(0x00E92E4A), UINT32_C(0x00744046), UINT32_C(0x00384BF8),
          UINT32_C(0x0027733A), UINT32_C(0x0071FB22), UINT32_C(0x0053AF65),
          UINT32_C(0x002794C6), UINT32_C(0x00C79DA2), UINT32_C(0x004A9C1B),
          UINT32_C(0x001A9309), UINT32_C(0x00103ECF)}},
        {{UINT32_C(0x000ACD3F), UINT32_C(0x000E6E24), UINT32_C(0x0058B8AE),
          UINT32_C(0x003A4430), UINT32_C(0x000A06B4), UINT32_C(0x002DE854),
          UINT32_C(0x0044E3E2), UINT32_C(0x000867E3), UINT32_C(0x007FF01B),
          UINT32_C(0x00244016), UINT32_C(0x00012B1D)},
         {UINT32_C(0x00B5F186), UINT32_C(0x00752F4D), UINT32_C(0x00358B6B),
          UINT32_C(0x003F9113), UINT32_C(0x001CEE85), UINT32_C(0x00229C2D),
          UINT32_C(0x004DA05C), UINT32_C(0x0085A9A9), UINT32_C(0x000D46D9),
          UINT32_C(0x007E5EDA), UINT32_C(0x00269074)}},
        {{UINT32_C(0x00A53FDE), UINT32_C(0x0058A7FA), UINT32_C(0x00032B2F),
          UINT32_C(0x00E52CC5), UINT32_C(0x004F77CE), UINT32_C(0x0074A88B),
          UINT32_C(0x002F23CF), UINT32_C(0x00F073CD), UINT32_C(0x00591A19),
          UINT32_C(0x0051A537), UINT32_C(0x007443BE)},
         {UINT32_C(0x008576CC), UINT32_C(0x006F139C), UINT32_C(0x006B127F),
          UINT32_C(0x00EFC187), UINT32_C(0x00244143), UINT32_C(0x0038C6EC),
          UINT32_C(0x001FA2AC), UINT32_C(0x001BF011), UINT32_C(0x005D8AE1),
          UINT32_C(0x00783BEE), UINT32_C(0x000B5335)}},
        {{UINT32_C(0x009CB9CB), UINT32_C(0x005CB329), UINT32_C(0x006729A8),
          UINT32_C(0x00119F0E), UINT32_C(0x002E37B0), UINT32_C(0x00289AD8),
          UINT32_C(0x0029E70A), UINT32_C(0x00D2872D), UINT32_C(0x0039C662),
          UINT32_C(0x000182D4), UINT32_C(0x0005EDE9)},
         {UINT32_C(0x009C3957), UINT32_C(0x005811A1), UINT32_C(0x002C3D0F),
          UINT32_C(0x00134BF7), UINT32_C(0x00484F36), UINT32_C(0x0002AC30),
          UINT32_C(0x00425F9B), UINT32_C(0x00955A54), UINT32_C(0x007B6B36),
          UINT32_C(0x00327B25), UINT32_C(0x007376DB)}},
        {{UINT32_C(0x00F420A8), UINT32_C(0x001348CB), UINT32_C(0x006949EA),
          UINT32_C(0x004C3B60), UINT32_C(0x006CECF6), UINT32_C(0x0000EFC9),
          UINT32_C(0x00740C4A), UINT32_C(0x00271CF1), UINT32_C(0x001CC0D8),
          UINT32_C(0x00017BE4), UINT32_C(0x0005C726)},
         {UINT32_C(0x00F3F8A6), UINT32_C(0x00727D92), UINT32_C(0x0063161F),
          UINT32_C(0x00C70EF9), UINT32_C(0x0015855F), UINT32_C(0x007F8036),
          UINT32_C(0x00003EDA), UINT32_C(0x001A5C7B), UINT32_C(0x00772841),
          UINT32_C(0x002A862B), UINT32_C(0x0068F5AC)}},
        {{UINT32_C(0x00372511), UINT32_C(0x00437344), UINT32_C(0x003E7956),
          UINT32_C(0x000C1F67), UINT32_C(0x003EB8B3), UINT32_C(0x00557961),
          UINT32_C(0x0022832B), UINT32_C(0x00F188A2), UINT32_C(0x007E2427),
          UINT32_C(0x00147CAB), UINT32_C(0x0045D0A9)},
         {UINT32_C(0x0029CABF), UINT32_C(0x005C33DF), UINT32_C(0x00026D73),
          UINT32_C(0x00AAF04C), UINT32_C(0x0057DB62), UINT32_C(0x0025990B),
          UINT32_C(0x006DB6EC), UINT32_C(0x003549E6), UINT32_C(0x001DEEAF),
          UINT32_C(0x002254AC), UINT32_C(0x0058BBD5)}},
        {{UINT32_C(0x00B77A44), UINT32_C(0x003F2F46), UINT32_C(0x00064DA4),
          UINT32_C(0x00839511), UINT32_C(0x0058488B), UINT32_C(0x000B772D),
          UINT32_C(0x007F4634), UINT32_C(0x008743EC), UINT32_C(0x0050BA38),
          UINT32_C(0x003CE592), UINT32_C(0x0062440C)},
         {UINT32_C(0x006C928C), UINT32_C(0x000B1540), UINT32_C(0x00339658),
          UINT32_C(0x005BEC51), UINT32_C(0x005B1535), UINT32_C(0x007A2111),
          UINT32_C(0x002A0968), UINT32_C(0x00EF790D), UINT32_C(0x007468E6),
          UINT32_C(0x00003B62), UINT32_C(0x002C8A13)}},
    },
    {
        {{UINT32_C(0x001AEE99), UINT32_C(0x0020A5A1), UINT32_C(0x0032D7F4),
          UINT32_C(0x003DF131), UINT32_C(0x001E1996), UINT32_C(0x005B7438),
          UINT32_C(0x005718AC), UINT32_C(0x00E5D6F0), UINT32_C(0x0067FE3A),
          UINT32_C(0x0043BEDC), UINT32_C(0x0024BB69)},
         {UINT32_C(0x00A31E2E), UINT32_C(0x0047EE95), UINT32_C(0x00464DFE),
          UINT32_C(0x0042F2B4), UINT32_C(0x001FCD82), UINT32_C(0x002D6AB2),
          UINT32_C(0x004BDFC2), UINT32_C(0x00844674), UINT32_C(0x003D4411),
          UINT32_C(0x00432B86), UINT32_C(0x002532BF)}},
        {{UINT32_C(0x005F12F9), UINT32_C(0x0062861D), UINT32_C(0x002EFAAB),
          UINT32_C(0x00FA0463), UINT32_C(0x00315D4D), UINT32_C(0x0061E65A),
          UINT32_C(0x005DB18D), UINT32_C(0x0009390F), UINT32_C(0x0059E60A),
          UINT32_C(0x0065E1AF), UINT32_C(0x00624DB7)},
         {UINT32_C(0x0083E30F), UINT32_C(0x0064CF13), UINT32_C(0x0072556F),
          UINT32_C(0x003CA70C), UINT32_C(0x007D9C2B), UINT32_C(0x00139172),
          UINT32_C(0x002113A1), UINT32_C(0x00FC55DB), UINT32_C(0x000698EC),
          UINT32_C(0x001E14A5), UINT32_C(0x0076C609)}},
        {{UINT32_C(0x0095662A), UINT32_C(0x0036AC63), UINT32_C(0x002500EA),
          UINT32_C(0x004622AB), UINT32_C(0x0077D68E), UINT32_C(0x0037F839),
          UINT32_C(0x007758F5), UINT32_C(0x00D42942), UINT32_C(0x003DB22A),
          UINT32_C(0x00622A98), UINT32_C(0x000DFA70)},
         {UINT32_C(0x00EC12BF), UINT32_C(0x0027B94B), UINT32_C(0x0050E295),
          UINT32_C(0x00EBCA4D), UINT32_C(0x00276449), UINT32_C(0x0047FCA4),
          UINT32_C(0x0037ED85), UINT32_C(0x0071515E), UINT32_C(0x0066DA3A),
          UINT32_C(0x0039095E), UINT32_C(0x005B2B97)}},
        {{UINT32_C(0x0011BC89), UINT32_C(0x007D50E8), UINT32_C(0x0011BF1F),
          UINT32_C(0x00A2A84A), UINT32_C(0x0012D4FE), UINT32_C(0x0008D675),
          UINT32_C(0x002C2672), UINT32_C(0x007645B3), UINT32_C(0x0006D691),
          UINT32_C(0x00374435), UINT32_C(0x0035AD63)},
         {UINT32_C(0x0003E773), UINT32_C(0x006AEC1F), UINT32_C(0x005376E6),
          UINT32_C(0x00D6A2F2), UINT32_C(0x0001A136), UINT32_C(0x0074EE78),
          UINT32_C(0x00061BC5), UINT32_C(0x00D92C45), UINT32_C(0x00397CD0),
          UINT32_C(0x0025C26E), UINT32_C(0x0059374C)}},
        {{UINT32_C(0x00A09501), UINT32_C(0x005C78C0), UINT32_C(0x005FDE74),
          UINT32_C(0x00106048), UINT32_C(0x007EA30E), UINT32_C(0x00646924),
          UINT32_C(0x00445B79), UINT32_C(0x000ED086), UINT32_C(0x0064E0AC),
          UINT32_C(0x007D8CBA), UINT32_C(0x004D380E)},
         {UINT32_C(0x008E2F04), UINT32_C(0x002E1444), UINT32_C(0x007FAF4F),
          UINT32_C(0x00780729), UINT32_C(0x003330F9), UINT32_C(0x000589E5),
          UINT32_C(0x006F0A62), UINT32_C(0x000B0B9C), UINT32_C(0x0013DDD0),
          UINT32_C(0x000078F6), UINT32_C(0x001CC544)}},
        {{UINT32_C(0x005823A8), UINT32_C(0x00410483), UINT32_C(0x007E6356),
          UINT32_C(0x00414323), UINT32_C(0x00224E12), UINT32_C(0x005FCD9C),
          UINT32_C(0x00737C09), UINT32_C(0x00697EBD), UINT32_C(0x002148D0),
          UINT32_C(0x002B1D29), UINT32_C(0x000C5045)},
         {UINT32_C(0x0010C3FE), UINT32_C(0x0016E066), UINT32_C(0x002938C4),
          UINT32_C(0x003ADAEA), UINT32_C(0x007415B5), UINT32_C(0x00151775),
          UINT32_C(0x0032ACC0), UINT32_C(0x00930BFE), UINT32_C(0x0035E23E),
          UINT32_C(0x0072CA6D), UINT32_C(0x004E4B15)}},
        {{UINT32_C(0x00F00F70), UINT32_C(0x000BABBD), UINT32_C(0x0060773F),
          UINT32_C(0x007CDF42), UINT32_C(0x0071BD8F), UINT32_C(0x004451AB),
          UINT32_C(0x00715EB4), UINT32_C(0x00E2E49E), UINT32_C(0x00241448),
          UINT32_C(0x000AD80D), UINT32_C(0x001CD906)},
         {UINT32_C(0x00BCBD12), UINT32_C(0x005D595E), UINT32_C(0x0023ED7A),
          UINT32_C(0x0071C310), UINT32_C(0x0067765E), UINT32_C(0x00657097),
          UINT32_C(0x00722381), UINT32_C(0x00DE8456), UINT32_C(0x00282F96),
          UINT32_C(0x00144C0E), UINT32_C(0x00177CA7)}},
        {{UINT32_C(0x00F71055), UINT32_C(0x006456B1), UINT32_C(0x006700E1),
          UINT32_C(0x008B98F5), UINT32_C(0x0052ADCD), UINT32_C(0x005744F1),
          UINT32_C(0x007B204B), UINT32_C(0x00A3506E), UINT32_C(0x0063BE42),
          UINT32_C(0x007E5FFD), UINT32_C(0x00255C05)},
         {UINT32_C(0x0088FA6C), UINT32_C(0x00508BF3), UINT32_C(0x006F89D3),
          UINT32_C(0x00E448C5), UINT32_C(0x00339951), UINT32_C(0x003285F7),
          UINT32_C(0x0050F779), UINT32_C(0x00BB3742), UINT32_C(0x006C5145),
          UINT32_C(0x000E4CC6), UINT32_C(0x00725EF1)}},
        {{UINT32_C(0x0046AE2B), UINT32_C(0x007D776D), UINT32_C(0x003BCE35),
          UINT32_C(0x00A61FCE), UINT32_C(0x000D70E8), UINT32_C(0x006292B6),
          UINT32_C(0x00688218), UINT32_C(0x00BE2745), UINT32_C(0x00094E58),
          UINT32_C(0x0009B42B), UINT32_C(0x0068C161)},
         {UINT32_C(0x003C69E3), UINT32_C(0x002BB404), UINT32_C(0x00192F8E),
          UINT32_C(0x006C3642), UINT32_C(0x0017D4D5), UINT32_C(0x00120D15),
          UINT32_C(0x00150EC2), UINT32_C(0x00894ED0), UINT32_C(0x00708435),
          UINT32_C(0x000F4581), UINT32_C(0x0077E852)}},
        {{UINT32_C(0x00E96C40), UINT32_C(0x0003BAE3), UINT32_C(0x0039A069),
          UINT32_C(0x00CF1197), UINT32_C(0x006C0440), UINT32_C(0x00493531),
          UINT32_C(0x005CCA20), UINT32_C(0x00051F91), UINT32_C(0x00447C85),
          UINT32_C(0x00549A26), UINT32_C(0x0021D2DD)},
         {UINT32_C(0x0003E55C), UINT32_C(0x004C16AC), UINT32_C(0x005A14F7),
          UINT32_C(0x001EDD65), UINT32_C(0x005E2DE4), UINT32_C(0x00655DDD),
          UINT32_C(0x00244FD3), UINT32_C(0x0015E737), UINT32_C(0x0059980C),
          UINT32_C(0x00764CCA), UINT32_C(0x0044B0FC)}},
        {{UINT32_C(0x000F3BFB), UINT32_C(0x007F6C0E), UINT32_C(0x0049E58A),
          UINT32_C(0x00ACF249), UINT32_C(0x00257C58), UINT32_C(0x003A18F3),
          UINT32_C(0x0016A0E2), UINT32_C(0x007F0B1A), UINT32_C(0x002626B9),
          UINT32_C(0x002ADDDB), UINT32_C(0x00666FAA)},
         {UINT32_C(0x00951F40), UINT32_C(0x005AD6ED), UINT32_C(0x005A13DC),
          UINT32_C(0x002957F5), UINT32_C(0x0003C908), UINT32_C(0x002ED9F8),
          UINT32_C(0x0038A2EF), UINT32_C(0x0066D359), UINT32_C(0x0036BB80),
          UINT32_C(0x00485D4E), UINT32_C(0x002385BC)}},
        {{UINT32_C(0x006992BF), UINT32_C(0x007FBDBC), UINT32_C(0x004F0C3A),
          UINT32_C(0x00A97852), UINT32_C(0x003652C9), UINT32_C(0x00566C64),
          UINT32_C(0x0010EA10), UINT32_C(0x003DBBC8), UINT32_C(0x0073C6FC),
          UINT32_C(0x0016BB4F), UINT32_C(0x007D9107)},
         {UINT32_C(0x009A6F04), UINT32_C(0x0001B25D), UINT32_C(0x0069D11F),
          UINT32_C(0x00F781A6), UINT32_C(0x0017833C), UINT32_C(0x00650EA3),
          UINT32_C(0x0026DE8E), UINT32_C(0x00F206B1), UINT32_C(0x00381F8F),
          UINT32_C(0x003E0885), UINT32_C(0x003EC7B2)}},
        {{UINT32_C(0x006CC730), UINT32_C(0x003FB03B), UINT32_C(0x003A12B4),
          UINT32_C(0x001E86B8), UINT32_C(0x004DDC8F), UINT32_C(0x0013F1F5),
          UINT32_C(0x005AA6C4), UINT32_C(0x006D4113), UINT32_C(0x002B4497),
          UINT32_C(0x00637931), UINT32_C(0x007E14DA)},
         {UINT32_C(0x0082428A), UINT32_C(0x00781839), UINT32_C(0x006D3EE0),
          UINT32_C(0x00556D45), UINT32_C(0x004CBA3D), UINT32_C(0x001A817C),
          UINT32_C(0x00079618), UINT32_C(0x009AD974), UINT32_C(0x00039ACC),
          UINT32_C(0x0066941E), UINT32_C(0x0017E43E)}},
        {{UINT32_C(0x001E17BF), UINT32_C(0x003A82E9), UINT32_C(0x00715860),
          UINT32_C(0x0066AD72), UINT32_C(0x00704AA0), UINT32_C(0x00610943),
          UINT32_C(0x006D6665), UINT32_C(0x0098EA35), UINT32_C(0x006ADBD9),
          UINT32_C(0x00686CCC), UINT32_C(0x001D3BA6)},
         {UINT32_C(0x009EE4F6), UINT32_C(0x006D56D7), UINT32_C(0x003DF3FF),
          UINT32_C(0x006D335A), UINT32_C(0x00136056), UINT32_C(0x007862D7),
          UINT32_C(0x00014DFF), UINT32_C(0x0069526C), UINT32_C(0x004239B5),
          UINT32_C(0x00225D2B), UINT32_C(0x0034F5F0)}},
        {{UINT32_C(0x00CDEBA0), UINT32_C(0x00343287), UINT32_C(0x00356E22),
          UINT32_C(0x00620AEB), UINT32_C(0x0079884F), UINT32_C(0x005ED81F),
          UINT32_C(0x00266E56), UINT32_C(0x00DFC04D), UINT32_C(0x00251F00),
          UINT32_C(0x000B1056), UINT32_C(0x003312CA)},
         {UINT32_C(0x0093459D), UINT32_C(0x00027551), UINT32_C(0x005C7CA5),
          UINT32_C(0x005793A0), UINT32_C(0x00444B26), UINT32_C(0x002E17C0),
          UINT32_C(0x0065465E), UINT32_C(0x00DE0CFB), UINT32_C(0x00246A04),
          UINT32_C(0x0058A1E7), UINT32_C(0x0002E6ED)}},
        {{UINT32_C(0x00D9BCCB), UINT32_C(0x00731CB2), UINT32_C(0x0054E269),
          UINT32_C(0x00298E40), UINT32_C(0x0038BD8A), UINT32_C(0x001EF4E1),
          UINT32_C(0x00536780), UINT32_C(0x008FC99C), UINT32_C(0x0070FBA5),
          UINT32_C(0x0023FE5A), UINT32_C(0x003086A6)},
         {UINT32_C(0x005F65D4), UINT32_C(0x0018B097), UINT32_C(0x0000D107),
          UINT32_C(0x00A7F701), UINT32_C(0x006D932A), UINT32_C(0x00645C66),
          UINT32_C(0x0031D996), UINT32_C(0x0021F472), UINT32_C(0x003E05E2),
          UINT32_C(0x00566358), UINT32_C(0x0020AA4B)}},
    },
    {
        {{UINT32_C(0x00036494), UINT32_C(0x002C1376), UINT32_C(0x003827F0),
          UINT32_C(0x00602697), UINT32_C(0x007FBAE0), UINT32_C(0x0023C27C),
          UINT32_C(0x0077FD3B), UINT32_C(0x00D682CE), UINT32_C(0x0000127C),
          UINT32_C(0x001800D0), UINT32_C(0x00434FAA)},
         {UINT32_C(0x0097CE18), UINT32_C(0x006C7E53), UINT32_C(0x002407FE),
          UINT32_C(0x00E59769), UINT32_C(0x0049091C), UINT32_C(0x005990C5),
          UINT32_C(0x000A8116), UINT32_C(0x006EF1DA), UINT32_C(0x0050BE84),
          UINT32_C(0x00409E8E), UINT32_C(0x00032E15)}},
        {{UINT32_C(0x008BA0CB), UINT32_C(0x0024C765), UINT32_C(0x0025C9B4),
          UINT32_C(0x00BEB81D), UINT32_C(0x005F5936), UINT32_C(0x00398905),
          UINT32_C(0x0019ABFF), UINT32_C(0x00D2850E), UINT32_C(0x0017E744),
          UINT32_C(0x006ED216), UINT32_C(0x005B3A87)},
         {UINT32_C(0x000D9750), UINT32_C(0x005A6DCD), UINT32_C(0x0023AC02),
          UINT32_C(0x00193FD3), UINT32_C(0x00311A37), UINT32_C(0x0018B521),
          UINT32_C(0x0041715F), UINT32_C(0x00B7BEE0), UINT32_C(0x004A2676),
          UINT32_C(0x0009D393), UINT32_C(0x0051FD17)}},
        {{UINT32_C(0x00BA8CD3), UINT32_C(0x001C0A4F), UINT32_C(0x007FF7A6),
          UINT32_C(0x0061749E), UINT32_C(0x0012F613), UINT32_C(0x001F7746),
          UINT32_C(0x0018B9B4), UINT32_C(0x0055F5A2), UINT32_C(0x0039E0C6),
          UINT32_C(0x002C981C), UINT32_C(0x00252009)},
         {UINT32_C(0x003B1F03), UINT32_C(0x0024B0F4), UINT32_C(0x002D9C8D),
          UINT32_C(0x005B3BC2), UINT32_C(0x0013D8D6), UINT32_C(0x001AE9C3),
          UINT32_C(0x00365B85), UINT32_C(0x0047917C), UINT32_C(0x003A827D),
          UINT32_C(0x006AC519), UINT32_C(0x0042EF23)}},
        {{UINT32_C(0x0058227A), UINT32_C(0x000C7436), UINT32_C(0x000A3CE3),
          UINT32_C(0x00AC1B52), UINT32_C(0x00026B67), UINT32_C(0x0002D123),
          UINT32_C(0x001FFB2E), UINT32_C(0x00DF0227), UINT32_C(0x003158E6),
          UINT32_C(0x00619FE2), UINT32_C(0x003BDF03)},
         {UINT32_C(0x00B4A505), UINT32_C(0x0038DD5F), UINT32_C(0x00129DC1),
          UINT32_C(0x0035123F), UINT32_C(0x0018810B), UINT32_C(0x0023ECA8),
          UINT32_C(0x000F60A9), UINT32_C(0x0067E897), UINT32_C(0x002B00B6),
          UINT32_C(0x0035E5A1), UINT32_C(0x0050D661)}},
        {{UINT32_C(0x006D68FC), UINT32_C(0x000B651D), UINT32_C(0x003E52A8),
          UINT32_C(0x005ACD99), UINT32_C(0x00216D3E), UINT32_C(0x004C0891),
          UINT32_C(0x00621922), UINT32_C(0x0058B412), UINT32_C(0x007F124C),
          UINT32_C(0x00597E66), UINT32_C(0x000FAB72)},
         {UINT32_C(0x00FAF9A7), UINT32_C(0x0074624D), UINT32_C(0x005AE3A4),
          UINT32_C(0x00A54BEE), UINT32_C(0x0004EB36), UINT32_C(0x000DC858),
          UINT32_C(0x00030603), UINT32_C(0x00A8E0B4), UINT32_C(0x00232FA4),
          UINT32_C(0x0013D254), UINT32_C(0x0020B631)}},
        {{UINT32_C(0x003E9997), UINT32_C(0x0050A360), UINT32_C(0x004BAD00),
          UINT32_C(0x00AB737C), UINT32_C(0x007E4EE9), UINT32_C(0x0073ACDF),
          UINT32_C(0x007C4D6E), UINT32_C(0x00A39D1C), UINT32_C(0x0042570D),
          UINT32_C(0x0033830B), UINT32_C(0x0008E1D4)},
         {UINT32_C(0x00583D12), UINT32_C(0x005EA888), UINT32_C(0x0026BED7),
          UINT32_C(0x00C1B988), UINT32_C(0x0039E362), UINT32_C(0x007B873E),
          UINT32_C(0x005C3583), UINT32_C(0x00EB9BA2), UINT32_C(0x00638B35),
          UINT32_C(0x00148D21), UINT32_C(0x001B932B)}},
        {{UINT32_C(0x002B37C3), UINT32_C(0x002F929B), UINT32_C(0x002CCC0B),
          UINT32_C(0x0024809E), UINT32_C(0x004FF9D5), UINT32_C(0x00099837),
          UINT32_C(0x0064A89F), UINT32_C(0x00C0C429), UINT32_C(0x007035B1),
          UINT32_C(0x00261985), UINT32_C(0x00331EBF)},
         {UINT32_C(0x00132A81), UINT32_C(0x00604DFD), UINT32_C(0x0003EF4A),
          UINT32_C(0x00FECB3D), UINT32_C(0x0008A863), UINT32_C(0x007FF6C4),
          UINT32_C(0x001F6DF0), UINT32_C(0x00BD475A), UINT32_C(0x004FC4E2),
          UINT32_C(0x0061E60C), UINT32_C(0x003C7003)}},
        {{UINT32_C(0x00D82392), UINT32_C(0x00774CFB), UINT32_C(0x0011493F),
          UINT32_C(0x007D13D1), UINT32_C(0x0005A8E0), UINT32_C(0x0036AD5D),
          UINT32_C(0x00231BED), UINT32_C(0x006C009E), UINT32_C(0x00599C2E),
          UINT32_C(0x002294D8), UINT32_C(0x0077C42B)},
         {UINT32_C(0x0044379A), UINT32_C(0x00456F09), UINT32_C(0x00385653),
          UINT32_C(0x00EFE8AC), UINT32_C(0x004C24E0), UINT32_C(0x0037DA15),
          UINT32_C(0x002B9F25), UINT32_C(0x00E34D0C), UINT32_C(0x00566C76),
          UINT32_C(0x0030810A), UINT32_C(0x00782A2F)}},
        {{UINT32_C(0x002AE6A8), UINT32_C(0x00049620), UINT32_C(0x00144B79),
          UINT32_C(0x00EDD792), UINT32_C(0x007D310C), UINT32_C(0x00752D40),
          UINT32_C(0x000CE8A0), UINT32_C(0x00440AAB), UINT32_C(0x007A9135),
          UINT32_C(0x00324DA4), UINT32_C(0x00022CA7)},
         {UINT32_C(0x001E7545), UINT32_C(0x005E29F4), UINT32_C(0x0039D31B),
          UINT32_C(0x0060119F), UINT32_C(0x00521746), UINT32_C(0x00334BCB),
          UINT32_C(0x006F2A29), UINT32_C(0x00F2D35C), UINT32_C(0x00622340),
          UINT32_C(0x004E3832), UINT32_C(0x000B1869)}},
        {{UINT32_C(0x0035F98E), UINT32_C(0x0054DCDC), UINT32_C(0x000537A8),
          UINT32_C(0x00EA6A6E), UINT32_C(0x00247B34), UINT32_C(0x0029BD4F),
          UINT32_C(0x0061AE41), UINT32_C(0x009B51AC), UINT32_C(0x0016A791),
          UINT32_C(0x0055445E), UINT32_C(0x004F050E)},
         {UINT32_C(0x000DC4CB), UINT32_C(0x001AE73A), UINT32_C(0x004BD8B2),
          UINT32_C(0x00E459A8), UINT32_C(0x0040AF9C), UINT32_C(0x0073F418),
          UINT32_C(0x004B87C6), UINT32_C(0x0074AA9F), UINT32_C(0x002F4063),
          UINT32_C(0x007AC805), UINT32_C(0x0016C70F)}},
        {{UINT32_C(0x0062EAB1), UINT32_C(0x0054EBAD), UINT32_C(0x001F2558),
          UINT32_C(0x00F0079A), UINT32_C(0x007D23A9), UINT32_C(0x005BF1F5),
          UINT32_C(0x004E2647), UINT32_C(0x00A0F501), UINT32_C(0x00278FBD),
          UINT32_C(0x0025F639), UINT32_C(0x00441A71)},
         {UINT32_C(0x00F8A724), UINT32_C(0x0034DAD4), UINT32_C(0x004C22C9),
          UINT32_C(0x00B48AB8), UINT32_C(0x000356CF), UINT32_C(0x00596FDE),
          UINT32_C(0x00317CF9), UINT32_C(0x003F2657), UINT32_C(0x00381653),
          UINT32_C(0x0050040E), UINT32_C(0x0055A4A1)}},
        {{UINT32_C(0x00EDE34B), UINT32_C(0x0056B7CD), UINT32_C(0x002EF4B0),
          UINT32_C(0x00E81239), UINT32_C(0x00533D1B), UINT32_C(0x0062E46C),
          UINT32_C(0x005A9C1B), UINT32_C(0x0056141C), UINT32_C(0x003E618F),
          UINT32_C(0x0075FB2A), UINT32_C(0x001440CE)},
         {UINT32_C(0x00CED2B3), UINT32_C(0x0063FD4A), UINT32_C(0x00487E7B),
          UINT32_C(0x008BFD17), UINT32_C(0x00612FE8), UINT32_C(0x0018078E),
          UINT32_C(0x001A43E6), UINT32_C(0x00CA1FDE), UINT32_C(0x004D5027),
          UINT32_C(0x00686BA9), UINT32_C(0x005C5F64)}},
        {{UINT32_C(0x006B794B), UINT32_C(0x003E8078), UINT32_C(0x00725DFB),
          UINT32_C(0x0003E642), UINT32_C(0x003284D3), UINT32_C(0x006EEFC1),
          UINT32_C(0x001A1987), UINT32_C(0x0016E28C), UINT32_C(0x00113F3C),
          UINT32_C(0x003E8399), UINT32_C(0x00287A14)},
         {UINT32_C(0x00BDAF78), UINT32_C(0x0013F588), UINT32_C(0x003E90C4),
          UINT32_C(0x00D74064), UINT32_C(0x0042B810), UINT32_C(0x005F4A46),
          UINT32_C(0x00543784), UINT32_C(0x00DB74C5), UINT32_C(0x00459CD7),
          UINT32_C(0x007531F3), UINT32_C(0x0055B5A4)}},
        {{UINT32_C(0x001A34E7), UINT32_C(0x002AEEC8), UINT32_C(0x00323D42),
          UINT32_C(0x00EABD9E), UINT32_C(0x000718F6), UINT32_C(0x0001BD5B),
          UINT32_C(0x000C39BC), UINT32_C(0x00B62821), UINT32_C(0x00561E9A),
          UINT32_C(0x001FBABE), UINT32_C(0x00311DCD)},
         {UINT32_C(0x00BEEAE4), UINT32_C(0x003E6854), UINT32_C(0x002AE46E),
          UINT32_C(0x00E57DCB), UINT32_C(0x004F1D7E), UINT32_C(0x0061FDF3),
          UINT32_C(0x00262088), UINT32_C(0x006BA852), UINT32_C(0x004FBF3F),
          UINT32_C(0x0019B907), UINT32_C(0x005993AA)}},
        {{UINT32_C(0x002F3F67), UINT32_C(0x000A5A43), UINT32_C(0x006CED05),
          UINT32_C(0x0042972A), UINT32_C(0x0027B1D7), UINT32_C(0x002E72AE),
          UINT32_C(0x0056B1CB), UINT32_C(0x00C6C042), UINT32_C(0x001AA312),
          UINT32_C(0x0023387A), UINT32_C(0x003C0940)},
         {UINT32_C(0x00F0C8DC), UINT32_C(0x000D1A71), UINT32_C(0x00038280),
          UINT32_C(0x0098AB5B), UINT32_C(0x000AA672), UINT32_C(0x0000542D),
          UINT32_C(0x005924A7), UINT32_C(0x009C972A), UINT32_C(0x0018581C),
          UINT32_C(0x00446E5A), UINT32_C(0x002EF656)}},
        {{UINT32_C(0x005F254D), UINT32_C(0x002CA014), UINT32_C(0x00274390),
          UINT32_C(0x005F1C09), UINT32_C(0x002A9A4C), UINT32_C(0x007503A3),
          UINT32_C(0x0071F4B9), UINT32_C(0x00667201), UINT32_C(0x0024873C),
          UINT32_C(0x001FE8DB), UINT32_C(0x00265A01)},
         {UINT32_C(0x0080223D), UINT32_C(0x000339C4), UINT32_C(0x005B64C0),
          UINT32_C(0x00A79EED), UINT32_C(0x0059C957), UINT32_C(0x005E2D2E),
          UINT32_C(0x0034AC78), UINT32_C(0x002BAFEE), UINT32_C(0x00778F99),
          UINT32_C(0x004D908C), UINT32_C(0x001C71C6)}},
    },
    {
        {{UINT32_C(0x007525D6), UINT32_C(0x0078DFD0), UINT32_C(0x001AC075),
          UINT32_C(0x00F76FA4), UINT32_C(0x00219F6A), UINT32_C(0x0023F7CB),
          UINT32_C(0x00236512), UINT32_C(0x008E5237), UINT32_C(0x00768B95),
          UINT32_C(0x001F359E), UINT32_C(0x007BC8CE)},
         {UINT32_C(0x002B44FC), UINT32_C(0x006A2BEB), UINT32_C(0x005919FE),
          UINT32_C(0x004D26D0), UINT32_C(0x0012E603), UINT32_C(0x0035F223),
          UINT32_C(0x0045E5BE), UINT32_C(0x007A242E), UINT32_C(0x0052F654),
          UINT32_C(0x00463CC4), UINT32_C(0x007D62C2)}},
        {{UINT32_C(0x00725350), UINT32_C(0x0055207F), UINT32_C(0x0019B576),
          UINT32_C(0x0077FB10), UINT32_C(0x003DB655), UINT32_C(0x0060FCDA),
          UINT32_C(0x007593DB), UINT32_C(0x001E1420), UINT32_C(0x00148D4C),
          UINT32_C(0x00257261), UINT32_C(0x0012B622)},
         {UINT32_C(0x0068CF96), UINT32_C(0x003F92AE), UINT32_C(0x00548833),
          UINT32_C(0x001EFE5D), UINT32_C(0x005E4445), UINT32_C(0x004C80EA),
          UINT32_C(0x0024D667), UINT32_C(0x009CEF0B), UINT32_C(0x007834DE),
          UINT32_C(0x00246306), UINT32_C(0x0032BC94)}},
        {{UINT32_C(0x00D15DFB), UINT32_C(0x005373DE), UINT32_C(0x0023474D),
          UINT32_C(0x00B4F94C), UINT32_C(0x006DE5AE), UINT32_C(0x002F13F2),
          UINT32_C(0x0008C966), UINT32_C(0x00D9C843), UINT32_C(0x006152D1),
          UINT32_C(0x003AB7F0), UINT32_C(0x00542CFC)},
         {UINT32_C(0x00ECDC3F), UINT32_C(0x0057D935), UINT32_C(0x003FD00C),
          UINT32_C(0x0029A8C0), UINT32_C(0x0015C42A), UINT32_C(0x0024D14E),
          UINT32_C(0x003C9CA5), UINT32_C(0x008E67D4), UINT32_C(0x0030ED8B),
          UINT32_C(0x0077755C), UINT32_C(0x00490705)}},
        {{UINT32_C(0x00A0236C), UINT32_C(0x00239926), UINT32_C(0x000860D0),
          UINT32_C(0x00C90CD5), UINT32_C(0x003F7701), UINT32_C(0x006A3B4E),
          UINT32_C(0x0038FDDB), UINT32_C(0x0092E0E2), UINT32_C(0x0064FAA4),
          UINT32_C(0x0054A42A), UINT32_C(0x00664E83)},
         {UINT32_C(0x00BAE781), UINT32_C(0x0011625D), UINT32_C(0x003FF2BE),
          UINT32_C(0x00B48675), UINT32_C(0x0033D5A4), UINT32_C(0x0046E677),
          UINT32_C(0x005C79EC), UINT32_C(0x003EFC01), UINT32_C(0x000687DF),
          UINT32_C(0x003F98E6), UINT32_C(0x0057654A)}},
        {{UINT32_C(0x007C9D05), UINT32_C(0x006E343C), UINT32_C(0x006CA1D5),
          UINT32_C(0x00045CD0), UINT32_C(0x00464424), UINT32_C(0x005D6BB6),
          UINT32_C(0x005D7662), UINT32_C(0x00795A8B), UINT32_C(0x000678F1),
          UINT32_C(0x0042EAF9), UINT32_C(0x0076F6DB)},
         {UINT32_C(0x00A709D2), UINT32_C(0x000CF913), UINT32_C(0x00392FD7),
          UINT32_C(0x00C45B5F), UINT32_C(0x006D4BFE), UINT32_C(0x007F5BBB),
          UINT32_C(0x007C0A94), UINT32_C(0x00C8F23E), UINT32_C(0x0022B535),
          UINT32_C(0x002AB090), UINT32_C(0x002E9B77)}},
        {{UINT32_C(0x001791A0), UINT32_C(0x0075D057), UINT32_C(0x007D4EEA),
          UINT32_C(0x003876FE), UINT32_C(0x0021CF5E), UINT32_C(0x000E949E),
          UINT32_C(0x0054DD38), UINT32_C(0x00A4D9B8), UINT32_C(0x005C64BF),
          UINT32_C(0x0012AA75), UINT32_C(0x00427813)},
         {UINT32_C(0x00A2F134), UINT32_C(0x0057EE9E), UINT32_C(0x006685A2),
          UINT32_C(0x00C39E70), UINT32_C(0x005C02EE), UINT32_C(0x0028E089),
          UINT32_C(0x0016D61B), UINT32_C(0x0091565C), UINT32_C(0x007FE441),
          UINT32_C(0x00441BC2), UINT32_C(0x004FB186)}},
        {{UINT32_C(0x009F9253), UINT32_C(0x00044FEB), UINT32_C(0x006A66A4),
          UINT32_C(0x00E8981D), UINT32_C(0x0051448B), UINT32_C(0x005BA13C),
          UINT32_C(0x005581CF), UINT32_C(0x00E8DD5B), UINT32_C(0x0078C1B6),
          UINT32_C(0x003CA471), UINT32_C(0x0008E7A6)},
         {UINT32_C(0x005393E2), UINT32_C(0x004204D4), UINT32_C(0x00211A2A),
          UINT32_C(0x00B3D008), UINT32_C(0x001CFAF3), UINT32_C(0x00068C84),
          UINT32_C(0x00504638), UINT32_C(0x00DC6FBE), UINT32_C(0x00605F56),
          UINT32_C(0x007CE2A4), UINT32_C(0x0017AC9D)}},
        {{UINT32_C(0x00E75974), UINT32_C(0x0072E0A3), UINT32_C(0x006950E5),
          UINT32_C(0x007C5A29), UINT32_C(0x000512AA), UINT32_C(0x002A144F),
          UINT32_C(0x001214FA), UINT32_C(0x00B04BDE), UINT32_C(0x000CC72A),
          UINT32_C(0x002DD6C9), UINT32_C(0x00200FEB)},
         {UINT32_C(0x007358CA), UINT32_C(0x0039CE66), UINT32_C(0x00003E83),
          UINT32_C(0x00B9546A), UINT32_C(0x00460D56), UINT32_C(0x001A5A98),
          UINT32_C(0x0012E16F), UINT32_C(0x00CE362B), UINT32_C(0x006563AD),
          UINT32_C(0x000C7CFB), UINT32_C(0x000164A2)}},
        {{UINT32_C(0x00A74C0C), UINT32_C(0x0029F428), UINT32_C(0x0037267E),
          UINT32_C(0x00C65194), UINT32_C(0x0043C78F), UINT32_C(0x0069B993),
          UINT32_C(0x003AA947), UINT32_C(0x0088BC88), UINT32_C(0x000B40A1),
          UINT32_C(0x003E35D0), UINT32_C(0x001B4097)},
         {UINT32_C(0x00286354), UINT32_C(0x0075D2BC), UINT32_C(0x005D7F2F),
          UINT32_C(0x00BD4F51), UINT32_C(0x006A5900), UINT32_C(0x00308202),
          UINT32_C(0x000E81D9), UINT32_C(0x00453B04), UINT32_C(0x006B1E9A),
          UINT32_C(0x0063911F), UINT32_C(0x000FD0D6)}},
        {{UINT32_C(0x006EFE04), UINT32_C(0x0047D6E6), UINT32_C(0x00020EA2),
          UINT32_C(0x0043B88F), UINT32_C(0x006A837A), UINT32_C(0x0011DB56),
          UINT32_C(0x004C3742), UINT32_C(0x00803C38), UINT32_C(0x001A9540),
          UINT32_C(0x001A1F6E), UINT32_C(0x002D2188)},
         {UINT32_C(0x009722F0), UINT32_C(0x0079FAD8), UINT32_C(0x006D15C3),
          UINT32_C(0x007C9007), UINT32_C(0x0023E12A), UINT32_C(0x0009E88F),
          UINT32_C(0x0038D49A), UINT32_C(0x00B87971), UINT32_C(0x0054EA95),
          UINT32_C(0x003BED25), UINT32_C(0x003C9370)}},
        {{UINT32_C(0x00730AAD), UINT32_C(0x0074B69E), UINT32_C(0x0018A010),
          UINT32_C(0x00ACF869), UINT32_C(0x00202D91), UINT32_C(0x00618FE7),
          UINT32_C(0x0003783B), UINT32_C(0x00322502), UINT32_C(0x004702AC),
          UINT32_C(0x0075B517), UINT32_C(0x0036900B)},
         {UINT32_C(0x003FB20A), UINT32_C(0x00437022), UINT32_C(0x003D242B),
          UINT32_C(0x00FEE4F4), UINT32_C(0x00276D11), UINT32_C(0x0040A87C),
          UINT32_C(0x0061F26A), UINT32_C(0x00F66A5B), UINT32_C(0x0076128D),
          UINT32_C(0x002AD91A), UINT32_C(0x007D1259)}},
        {{UINT32_C(0x007628C3), UINT32_C(0x0073E024), UINT32_C(0x0023855F),
          UINT32_C(0x004B1FBD), UINT32_C(0x00684558), UINT32_C(0x005CB959),
          UINT32_C(0x005ED77A), UINT32_C(0x008234D5), UINT32_C(0x001DF835),
          UINT32_C(0x00194398), UINT32_C(0x00209BA1)},
         {UINT32_C(0x00632779), UINT32_C(0x00194050), UINT32_C(0x006E13F1),
          UINT32_C(0x008FA4BC), UINT32_C(0x00464026), UINT32_C(0x0013B9ED),
          UINT32_C(0x003DCB10), UINT32_C(0x00BD9EE5), UINT32_C(0x00757CA6),
          UINT32_C(0x007A0F71), UINT32_C(0x000B4460)}},
        {{UINT32_C(0x0012D2C5), UINT32_C(0x005F26F3), UINT32_C(0x003FDA58),
          UINT32_C(0x009F0D33), UINT32_C(0x001A9FC7), UINT32_C(0x00412B7C),
          UINT32_C(0x005BD541), UINT32_C(0x00275539), UINT32_C(0x0026899F),
          UINT32_C(0x0062067D), UINT32_C(0x004ABE07)},
         {UINT32_C(0x009290FF), UINT32_C(0x0008F902), UINT32_C(0x005B3174),
          UINT32_C(0x00BD00F1), UINT32_C(0x006F6CFA), UINT32_C(0x00703B6C),
          UINT32_C(0x004B306D), UINT32_C(0x00331AC1), UINT32_C(0x00703E76),
          UINT32_C(0x004712AD), UINT32_C(0x000C81F8)}},
        {{UINT32_C(0x00FE693D), UINT32_C(0x000F4508), UINT32_C(0x0002FCF8),
          UINT32_C(0x00E0954B), UINT32_C(0x00040F5D), UINT32_C(0x005C00E7),
          UINT32_C(0x00700B3A), UINT32_C(0x00420CF9), UINT32_C(0x002B08A6),
          UINT32_C(0x00160E5F), UINT32_C(0x001712CE)},
         {UINT32_C(0x00A1DBED), UINT32_C(0x00787A0A), UINT32_C(0x000BCD4C),
          UINT32_C(0x0062D939), UINT32_C(0x006B28AC), UINT32_C(0x00164809),
          UINT32_C(0x007F6FE7), UINT32_C(0x005D1F67), UINT32_C(0x00557447),
          UINT32_C(0x0062C3E0), UINT32_C(0x00476FD0)}},
        {{UINT32_C(0x00B36FBF), UINT32_C(0x003D80AA), UINT32_C(0x00214159),
          UINT32_C(0x00E99663), UINT32_C(0x0058CDAA), UINT32_C(0x0014C3F2),
          UINT32_C(0x003D868E), UINT32_C(0x0035B474), UINT32_C(0x001D1436),
          UINT32_C(0x00048E40), UINT32_C(0x006AAF0D)},
         {UINT32_C(0x00D0E8D0), UINT32_C(0x005BDF3C), UINT32_C(0x00258CC0),
          UINT32_C(0x00828F97), UINT32_C(0x0012E7A5), UINT32_C(0x004BAF68),
          UINT32_C(0x004E71C5), UINT32_C(0x002A2699), UINT32_C(0x006A772A),
          UINT32_C(0x0017160A), UINT32_C(0x0040BDB6)}},
        {{UINT32_C(0x002BCD28), UINT32_C(0x006CCC00), UINT32_C(0x004C1CF7),
          UINT32_C(0x001BA30A), UINT32_C(0x006097CD), UINT32_C(0x005D0B45),
          UINT32_C(0x002C9DCF), UINT32_C(0x00C3CD03), UINT32_C(0x00489B87),
          UINT32_C(0x007C9642), UINT32_C(0x005A0A68)},
         {UINT32_C(0x00D7C516), UINT32_C(0x0054FD9C), UINT32_C(0x001F2677),
          UINT32_C(0x004F48D5), UINT32_C(0x000B6C13), UINT32_C(0x0078D063),
          UINT32_C(0x005298BA), UINT32_C(0x00B8780E), UINT32_C(0x00220AF4),
          UINT32_C(0x00076896), UINT32_C(0x00027744)}},
    },
    {
        {{UINT32_C(0x00259121), UINT32_C(0x00780B29), UINT32_C(0x00039769),
          UINT32_C(0x00EB5E49), UINT32_C(0x00050B31), UINT32_C(0x000092C3),
          UINT32_C(0x004A18D1), UINT32_C(0x00BEE193), UINT32_C(0x0021482F),
          UINT32_C(0x0018C2E7), UINT32_C(0x0018F417)},
         {UINT32_C(0x003B3193), UINT32_C(0x00535E04), UINT32_C(0x00500D71),
          UINT32_C(0x0036E3E0), UINT32_C(0x006FF0A5), UINT32_C(0x007EA7BE),
          UINT32_C(0x00407708), UINT32_C(0x00630180), UINT32_C(0x007BF646),
          UINT32_C(0x00576B67), UINT32_C(0x007CE956)}},
        {{UINT32_C(0x00171587), UINT32_C(0x00655CC1), UINT32_C(0x00488679),
          UINT32_C(0x00CE4CA9), UINT32_C(0x00041352), UINT32_C(0x007263F6),
          UINT32_C(0x0010ADD3), UINT32_C(0x00076CB5), UINT32_C(0x003EB667),
          UINT32_C(0x006C507D), UINT32_C(0x0079534E)},
         {UINT32_C(0x001F5C5D), UINT32_C(0x00303618), UINT32_C(0x0007DBD5),
          UINT32_C(0x000DB229), UINT32_C(0x007DA0B0), UINT32_C(0x00423931),
          UINT32_C(0x0047CEA2), UINT32_C(0x0078D1CB), UINT32_C(0x00147094),
          UINT32_C(0x007B2327), UINT32_C(0x00417208)}},
        {{UINT32_C(0x009DFEFF), UINT32_C(0x0033C1FF), UINT32_C(0x003D13CC),
          UINT32_C(0x0022EFEF), UINT32_C(0x007FE948), UINT32_C(0x005EEFB8),
          UINT32_C(0x001AD2B7), UINT32_C(0x00E96C81), UINT32_C(0x0026BBFC),
          UINT32_C(0x002585EE), UINT32_C(0x00745C59)},
         {UINT32_C(0x009DCD65), UINT32_C(0x0021489A), UINT32_C(0x00004480),
          UINT32_C(0x00060657), UINT32_C(0x003701DD), UINT32_C(0x005D19B3),
          UINT32_C(0x0075E1C0), UINT32_C(0x003892E0), UINT32_C(0x0064F9DE),
          UINT32_C(0x0035DC27), UINT32_C(0x0022C79F)}},
        {{UINT32_C(0x0058C6F2), UINT32_C(0x00752404), UINT32_C(0x006C8922),
          UINT32_C(0x00E6C036), UINT32_C(0x004204FF), UINT32_C(0x002ED321),
          UINT32_C(0x003D0BB2), UINT32_C(0x006D9D7A), UINT32_C(0x006E5E62),
          UINT32_C(0x004F1C35), UINT32_C(0x007EF78D)},
         {UINT32_C(0x009190F7), UINT32_C(0x005FBA14), UINT32_C(0x004879E5),
          UINT32_C(0x00A6E235), UINT32_C(0x005DD8F6), UINT32_C(0x00554643),
          UINT32_C(0x00090E18), UINT32_C(0x00611285), UINT32_C(0x001AEA18),
          UINT32_C(0x0072B543), UINT32_C(0x002593AC)}},
        {{UINT32_C(0x0059C268), UINT32_C(0x0012165B), UINT32_C(0x00159FF5),
          UINT32_C(0x003F5872), UINT32_C(0x004905ED), UINT32_C(0x001BF4B6),
          UINT32_C(0x00355284), UINT32_C(0x00D00A03), UINT32_C(0x001B4A76),
          UINT32_C(0x0031B3A5), UINT32_C(0x005DDE8F)},
         {UINT32_C(0x0061678D), UINT32_C(0x000F2A71), UINT32_C(0x001FDD6E),
          UINT32_C(0x0076A915), UINT32_C(0x00582B74), UINT32_C(0x000DA16F),
          UINT32_C(0x00345F20), UINT32_C(0x00634760), UINT32_C(0x0051ED40),
          UINT32_C(0x006A3D97), UINT32_C(0x00190B8F)}},
        {{UINT32_C(0x00EECAB3), UINT32_C(0x002F2403), UINT32_C(0x00636CBA),
          UINT32_C(0x00A54BAF), UINT32_C(0x007D99BA), UINT32_C(0x0007FB3D),
          UINT32_C(0x003C3EAC), UINT32_C(0x000EF42B), UINT32_C(0x00198349),
          UINT32_C(0x002E9959), UINT32_C(0x000F2F86)},
         {UINT32_C(0x00B9D5F6), UINT32_C(0x00463217), UINT32_C(0x00074FAC),
          UINT32_C(0x001183B8), UINT32_C(0x002C896F), UINT32_C(0x0055F891),
          UINT32_C(0x00105639), UINT32_C(0x0072DF59), UINT32_C(0x000D9B72),
          UINT32_C(0x007D9093), UINT32_C(0x004EAA60)}},
        {{UINT32_C(0x009F9703), UINT32_C(0x001B0826), UINT32_C(0x0076FA46),
          UINT32_C(0x00826948), UINT32_C(0x004D4814), UINT32_C(0x0050B57C),
          UINT32_C(0x005C41CC), UINT32_C(0x00A88628), UINT32_C(0x005D3BCD),
          UINT32_C(0x001C7176), UINT32_C(0x00554D0A)},
         {UINT32_C(0x00B63C62), UINT32_C(0x00561A3E), UINT32_C(0x0057E7D3),
          UINT32_C(0x00F1D376), UINT32_C(0x002E45AC), UINT32_C(0x005D7F33),
          UINT32_C(0x00698C00), UINT32_C(0x00F7C938), UINT32_C(0x0036EEFA),
          UINT32_C(0x005C1FAC), UINT32_C(0x005D1B2E)}},
        {{UINT32_C(0x00F53650), UINT32_C(0x0069380C), UINT32_C(0x0012C364),
          UINT32_C(0x0032E8A7), UINT32_C(0x00515F1E), UINT32_C(0x001F9182),
          UINT32_C(0x0068DEE7), UINT32_C(0x00CCF589), UINT32_C(0x0076EE01),
          UINT32_C(0x0029DF93), UINT32_C(0x00787773)},
         {UINT32_C(0x00B3DDF7), UINT32_C(0x0053B71C), UINT32_C(0x0006F8FD),
          UINT32_C(0x00DFF4AB), UINT32_C(0x0006B924), UINT32_C(0x007E2A66),
          UINT32_C(0x004A7D4B), UINT32_C(0x00AF1A4A), UINT32_C(0x0057F48B),
          UINT32_C(0x0044541B), UINT32_C(0x0049AD77)}},
        {{UINT32_C(0x005F6754), UINT32_C(0x007B1268), UINT32_C(0x000CA1F5),
          UINT32_C(0x0004DE64), UINT32_C(0x004FD873), UINT32_C(0x003036C4),
          UINT32_C(0x0041B763), UINT32_C(0x00FCE495), UINT32_C(0x00200179),
          UINT32_C(0x005F6BDA), UINT32_C(0x0068ED61)},
         {UINT32_C(0x00C95FCE), UINT32_C(0x000477FE), UINT32_C(0x00580F29),
          UINT32_C(0x00BB42ED), UINT32_C(0x00750145), UINT32_C(0x000DC207),
          UINT32_C(0x002BEC56), UINT32_C(0x00EB7195), UINT32_C(0x001693A1),
          UINT32_C(0x003EB07D), UINT32_C(0x00135B86)}},
        {{UINT32_C(0x0066889B), UINT32_C(0x0017A14A), UINT32_C(0x002DF300),
          UINT32_C(0x002BBAB9), UINT32_C(0x0015E56C), UINT32_C(0x002EB2BA),
          UINT32_C(0x001D111B), UINT32_C(0x00BA9054), UINT32_C(0x0069E112),
          UINT32_C(0x007A15D8), UINT32_C(0x00436DD2)},
         {UINT32_C(0x00E27B51), UINT32_C(0x0073DEE8), UINT32_C(0x007A67DC),
          UINT32_C(0x0055BEAF), UINT32_C(0x004FDE98), UINT32_C(0x001CCAC5),
          UINT32_C(0x0057571D), UINT32_C(0x00B71DD1), UINT32_C(0x005EC9C2),
          UINT32_C(0x00698B19), UINT32_C(0x00572BAF)}},
        {{UINT32_C(0x00FE2F09), UINT32_C(0x004CCD1B), UINT32_C(0x005B6C69),
          UINT32_C(0x00CE2291), UINT32_C(0x0036C67A), UINT32_C(0x001AA26F),
          UINT32_C(0x00482976), UINT32_C(0x000346C9), UINT32_C(0x0071FA32),
          UINT32_C(0x006AAA17), UINT32_C(0x0066681D)},
         {UINT32_C(0x006DEDEE), UINT32_C(0x005A67B7), UINT32_C(0x0066E9D5),
          UINT32_C(0x00F07923), UINT32_C(0x00767A82), UINT32_C(0x0060641F),
          UINT32_C(0x005C4851), UINT32_C(0x0077DBEB), UINT32_C(0x006550C4),
          UINT32_C(0x005C811B), UINT32_C(0x00204A6C)}},
        {{UINT32_C(0x00F41270), UINT32_C(0x007228A8), UINT32_C(0x0023FE22),
          UINT32_C(0x0087E9E0), UINT32_C(0x001016E1), UINT32_C(0x002EA90D),
          UINT32_C(0x003BEE12), UINT32_C(0x0002DA75), UINT32_C(0x00486C8C),
          UINT32_C(0x000FFBE4), UINT32_C(0x00161DF5)},
         {UINT32_C(0x0073031B), UINT32_C(0x0068C340), UINT32_C(0x005C0685),
          UINT32_C(0x0027F000), UINT32_C(0x0028653F), UINT32_C(0x005D1D2F),
          UINT32_C(0x003F0628), UINT32_C(0x0066005A), UINT32_C(0x005AD814),
          UINT32_C(0x0001BCF2), UINT32_C(0x0032CEE0)}},
        {{UINT32_C(0x00ADB2E0), UINT32_C(0x00483DBE), UINT32_C(0x00626B68),
          UINT32_C(0x004FB784), UINT32_C(0x00350F94), UINT32_C(0x00357148),
          UINT32_C(0x007F0B6D), UINT32_C(0x0029332B), UINT32_C(0x003AD1F4),
          UINT32_C(0x001F3681), UINT32_C(0x0071D76B)},
         {UINT32_C(0x0026BAF9), UINT32_C(0x00473D14), UINT32_C(0x00021B2C),
          UINT32_C(0x00941016), UINT32_C(0x004F652A), UINT32_C(0x007D3A99),
          UINT32_C(0x004ABBAD), UINT32_C(0x0087A405), UINT32_C(0x000547B1),
          UINT32_C(0x0059E149), UINT32_C(0x00541E49)}},
        {{UINT32_C(0x00694346), UINT32_C(0x0042316D), UINT32_C(0x00758401),
          UINT32_C(0x00D76375), UINT32_C(0x005ECFAE), UINT32_C(0x00680B14),
          UINT32_C(0x0026EA25), UINT32_C(0x00070619), UINT32_C(0x0058D41C),
          UINT32_C(0x0048DB6A), UINT32_C(0x00062542)},
         {UINT32_C(0x003DE6A0), UINT32_C(0x003469E5), UINT32_C(0x00302B64),
          UINT32_C(0x00CD8BF2), UINT32_C(0x0045419E), UINT32_C(0x001E9AAF),
          UINT32_C(0x003EFC2C), UINT32_C(0x00E12629), UINT32_C(0x0060F16E),
          UINT32_C(0x0048A125), UINT32_C(0x002969D8)}},
        {{UINT32_C(0x00C271BD), UINT32_C(0x0053BE38), UINT32_C(0x00373A9B),
          UINT32_C(0x00AB74E5), UINT32_C(0x00676622), UINT32_C(0x005F7345),
          UINT32_C(0x007A6084), UINT32_C(0x0095988C), UINT32_C(0x0055BB90),
          UINT32_C(0x00411396), UINT32_C(0x000E67BF)},
         {UINT32_C(0x00CF58AC), UINT32_C(0x0060BB94), UINT32_C(0x0046BFAD),
          UINT32_C(0x003C68E3), UINT32_C(0x00748102), UINT32_C(0x00647558),
          UINT32_C(0x005B14F7), UINT32_C(0x00494DA5), UINT32_C(0x002AB3B3),
          UINT32_C(0x0065BA0F), UINT32_C(0x005C011D)}},
        {{UINT32_C(0x00D13FD0), UINT32_C(0x0051781B), UINT32_C(0x0028EAA9),
          UINT32_C(0x0038E655), UINT32_C(0x00624813), UINT32_C(0x00476802),
          UINT32_C(0x0073DD34), UINT32_C(0x0019666A), UINT32_C(0x00479419),
          UINT32_C(0x002D06C2), UINT32_C(0x0052A1AF)},
         {UINT32_C(0x003684D7), UINT32_C(0x003EE24A), UINT32_C(0x005496AF),
          UINT32_C(0x004899AC), UINT32_C(0x005FC959), UINT32_C(0x007950D6),
          UINT32_C(0x00180144), UINT32_C(0x00DF7129), UINT32_C(0x0014AFF7),
          UINT32_C(0x007D7BA5), UINT32_C(0x007347EA)}},
    },
    {
        {{UINT32_C(0x00978880), UINT32_C(0x0005E66D), UINT32_C(0x003D5998),
          UINT32_C(0x008F2D63), UINT32_C(0x003E9896), UINT32_C(0x002DCC8D),
          UINT32_C(0x00789105), UINT32_C(0x00786FAC), UINT32_C(0x0006486C),
          UINT32_C(0x004DEBB7), UINT32_C(0x007C6948)},
         {UINT32_C(0x001922F1), UINT32_C(0x003D924D), UINT32_C(0x0005E5FB),
          UINT32_C(0x00CEF8A3), UINT32_C(0x004EBF60), UINT32_C(0x00039779),
          UINT32_C(0x001D5AD8), UINT32_C(0x00AEAD3B), UINT32_C(0x003178D9),
          UINT32_C(0x0011AF88), UINT32_C(0x004858F6)}},
        {{UINT32_C(0x0017EA11), UINT32_C(0x002925A4), UINT32_C(0x0022EBC5),
          UINT32_C(0x008C6F13), UINT32_C(0x0068235B), UINT32_C(0x0007668E),
          UINT32_C(0x00789C73), UINT32_C(0x0063577F), UINT32_C(0x0019C095),
          UINT32_C(0x0054C609), UINT32_C(0x00228B12)},
         {UINT32_C(0x00B423B7), UINT32_C(0x007B3E73), UINT32_C(0x0006F374),
          UINT32_C(0x0032C99A), UINT32_C(0x0033919B), UINT32_C(0x004480AF),
          UINT32_C(0x007CF2DC), UINT32_C(0x00DF4C55), UINT32_C(0x00194F69),
          UINT32_C(0x0002FA8A), UINT32_C(0x00273C64)}},
        {{UINT32_C(0x00B22D03), UINT32_C(0x004609D0), UINT32_C(0x000E534B),
          UINT32_C(0x00B53A66), UINT32_C(0x0013A238), UINT32_C(0x0035DF0D),
          UINT32_C(0x002227E6), UINT32_C(0x00925830), UINT32_C(0x004B0D8E),
          UINT32_C(0x0045DD96), UINT32_C(0x00489148)},
         {UINT32_C(0x00A0967E), UINT32_C(0x002AE5BC), UINT32_C(0x00796CFA),
          UINT32_C(0x00CD72AF), UINT32_C(0x005B6940), UINT32_C(0x00012BBA),
          UINT32_C(0x005FC6EF), UINT32_C(0x00DCD6CA), UINT32_C(0x000D4575),
          UINT32_C(0x003A665E), UINT32_C(0x004D067C)}},
        {{UINT32_C(0x001AECD9), UINT32_C(0x00273F6F), UINT32_C(0x0025F83B),
          UINT32_C(0x00FFD158), UINT32_C(0x00133FE6), UINT32_C(0x001F91CB),
          UINT32_C(0x007C28C3), UINT32_C(0x002B2923), UINT32_C(0x0028D2B0),
          UINT32_C(0x00267588), UINT32_C(0x005BED5F)},
         {UINT32_C(0x00EE613B), UINT32_C(0x002D41FF), UINT32_C(0x00611238),
          UINT32_C(0x0037C033), UINT32_C(0x000A5923), UINT32_C(0x0040AA8E),
          UINT32_C(0x00226F22), UINT32_C(0x00ACB580), UINT32_C(0x00332B6F),
          UINT32_C(0x00573DBD), UINT32_C(0x00058D53)}},
        {{UINT32_C(0x000B6527), UINT32_C(0x00633B9B), UINT32_C(0x0015CA8A),
          UINT32_C(0x00CAF1F7), UINT32_C(0x0014FEBC), UINT32_C(0x00404B31),
          UINT32_C(0x005DE86F), UINT32_C(0x00E40657), UINT32_C(0x0040EE16),
          UINT32_C(0x006745A5), UINT32_C(0x001CC28B)},
         {UINT32_C(0x00D0A46D), UINT32_C(0x0052FCCC), UINT32_C(0x005B6FE6),
          UINT32_C(0x009F4694), UINT32_C(0x003EE682), UINT32_C(0x00135F2D),
          UINT32_C(0x006B1F49), UINT32_C(0x00903C8F), UINT32_C(0x0066F07A),
          UINT32_C(0x0067D510), UINT32_C(0x006FBACA)}},
        {{UINT32_C(0x00996ED6), UINT32_C(0x005D39E4), UINT32_C(0x001A204F),
          UINT32_C(0x0048C691), UINT32_C(0x004EDCA1), UINT32_C(0x000326EA),
          UINT32_C(0x005897F6), UINT32_C(0x005DAFAA), UINT32_C(0x007CE893),
          UINT32_C(0x001521A8), UINT32_C(0x00720EC4)},
         {UINT32_C(0x00B2D745), UINT32_C(0x0044D634), UINT32_C(0x00645385),
          UINT32_C(0x00FCF90E), UINT32_C(0x005705FF), UINT32_C(0x00194811),
          UINT32_C(0x00465D27), UINT32_C(0x00DA9D7A), UINT32_C(0x00183FEE),
          UINT32_C(0x0033D943), UINT32_C(0x003FD6A9)}},
        {{UINT32_C(0x00667E0F), UINT32_C(0x00755AD8), UINT32_C(0x00555F7B),
          UINT32_C(0x00C2071C), UINT32_C(0x003AA31C), UINT32_C(0x0027748C),
          UINT32_C(0x0065D71F), UINT32_C(0x00B8551F), UINT32_C(0x005831C5),
          UINT32_C(0x00007409), UINT32_C(0x0019AE44)},
         {UINT32_C(0x00C46447), UINT32_C(0x0028055B), UINT32_C(0x000AAEBC),
          UINT32_C(0x00811E2C), UINT32_C(0x000A161D), UINT32_C(0x005417D2),
          UINT32_C(0x00532D53), UINT32_C(0x00195F30), UINT32_C(0x007ADD3B),
          UINT32_C(0x0079DFD4), UINT32_C(0x004C7DEA)}},
        {{UINT32_C(0x00AA87C8), UINT32_C(0x000AA2A3), UINT32_C(0x0021BFFE),
          UINT32_C(0x007EEC07), UINT32_C(0x00165806), UINT32_C(0x001D2B43),
          UINT32_C(0x007D76F2), UINT32_C(0x00D7F740), UINT32_C(0x003CD969),
          UINT32_C(0x004EFCFC), UINT32_C(0x004B057E)},
         {UINT32_C(0x00812859), UINT32_C(0x00262F51), UINT32_C(0x005521D1),
          UINT32_C(0x00A51059), UINT32_C(0x00374C25), UINT32_C(0x006ADA14),
          UINT32_C(0x0076B74A), UINT32_C(0x006F1B1A), UINT32_C(0x005EAF40),
          UINT32_C(0x0024DA86), UINT32_C(0x007F9103)}},
        {{UINT32_C(0x009A47A8), UINT32_C(0x0024BD4E), UINT32_C(0x00047520),
          UINT32_C(0x0094EB12), UINT32_C(0x0009C313), UINT32_C(0x002CA8A1),
          UINT32_C(0x002E7050), UINT32_C(0x00D0760D), UINT32_C(0x007B653F),
          UINT32_C(0x00135449), UINT32_C(0x0079DBC7)},
         {UINT32_C(0x007D4BFD), UINT32_C(0x006CBD61), UINT32_C(0x001AF365),
          UINT32_C(0x0041A9E7), UINT32_C(0x000EA23B), UINT32_C(0x000496E3),
          UINT32_C(0x007D8894), UINT32_C(0x00D7926C), UINT32_C(0x0048D1E6),
          UINT32_C(0x0021BA7D), UINT32_C(0x0076D264)}},
        {{UINT32_C(0x004D6735), UINT32_C(0x00349533), UINT32_C(0x0026B4B7),
          UINT32_C(0x00938C63), UINT32_C(0x00259D23), UINT32_C(0x00327F58),
          UINT32_C(0x000972E6), UINT32_C(0x00DFCD69), UINT32_C(0x004A4323),
          UINT32_C(0x007FE26C), UINT32_C(0x001F308C)},
         {UINT32_C(0x00B5CA15), UINT32_C(0x0013066F), UINT32_C(0x00554ED3),
          UINT32_C(0x0092A76E), UINT32_C(0x00767C40), UINT32_C(0x0001BD5A),
          UINT32_C(0x007F0EC1), UINT32_C(0x004E5BCB), UINT32_C(0x0033C8BC),
          UINT32_C(0x00384042), UINT32_C(0x0028DAF3)}},
        {{UINT32_C(0x00BB2865), UINT32_C(0x0074D6A2), UINT32_C(0x007D552B),
          UINT32_C(0x005E0EAF), UINT32_C(0x007BD504), UINT32_C(0x0079341F),
          UINT32_C(0x0064542F), UINT32_C(0x00445DD5), UINT32_C(0x00289614),
          UINT32_C(0x0009E5FF), UINT32_C(0x002F0D16)},
         {UINT32_C(0x00118DE4), UINT32_C(0x0061DD1F), UINT32_C(0x004282EE),
          UINT32_C(0x005DF2AC), UINT32_C(0x004951DF), UINT32_C(0x007C84E0),
          UINT32_C(0x006F8826), UINT32_C(0x00545F9D), UINT32_C(0x0050CDEE),
          UINT32_C(0x003414C7), UINT32_C(0x00228E14)}},
        {{UINT32_C(0x0021FB0D), UINT32_C(0x0054D257), UINT32_C(0x0071F975),
          UINT32_C(0x00FB2766), UINT32_C(0x0049536E), UINT32_C(0x006245E8),
          UINT32_C(0x00090378), UINT32_C(0x00A7124C), UINT32_C(0x007CD78C),
          UINT32_C(0x00163C62), UINT32_C(0x001C1D00)},
         {UINT32_C(0x0041A6D3), UINT32_C(0x007CEA65), UINT32_C(0x00384B4B),
          UINT32_C(0x00C28E24), UINT32_C(0x0073484A), UINT32_C(0x00457BD5),
          UINT32_C(0x00258E54), UINT32_C(0x00868651), UINT32_C(0x000B50A9),
          UINT32_C(0x0071A4E3), UINT32_C(0x00405353)}},
        {{UINT32_C(0x007327CF), UINT32_C(0x005D8779), UINT32_C(0x003387AF),
          UINT32_C(0x00892854), UINT32_C(0x006E7126), UINT32_C(0x004C6531),
          UINT32_C(0x002D3F16), UINT32_C(0x0000D279), UINT32_C(0x005A56F1),
          UINT32_C(0x000126BC), UINT32_C(0x005F03F2)},
         {UINT32_C(0x0024065F), UINT32_C(0x0028A2BF), UINT32_C(0x00690907),
          UINT32_C(0x00E63938), UINT32_C(0x006A92A2), UINT32_C(0x0012FA15),
          UINT32_C(0x002E3667), UINT32_C(0x00301625), UINT32_C(0x0068DFF5),
          UINT32_C(0x007C18F7), UINT32_C(0x0057C821)}},
        {{UINT32_C(0x00F6FB17), UINT32_C(0x00117A2C), UINT32_C(0x00702920),
          UINT32_C(0x00D8E8DC), UINT32_C(0x00583CF5), UINT32_C(0x00581272),
          UINT32_C(0x0014FA49), UINT32_C(0x000BB02A), UINT32_C(0x000BFAD2),
          UINT32_C(0x000D7AF5), UINT32_C(0x003C597E)},
         {UINT32_C(0x00DC1695), UINT32_C(0x007BDD76), UINT32_C(0x006DF467),
          UINT32_C(0x007F6B18), UINT32_C(0x0014F2F0), UINT32_C(0x007C500F),
          UINT32_C(0x0008D022), UINT32_C(0x0001FBD4), UINT32_C(0x004DA576),
          UINT32_C(0x00414183), UINT32_C(0x00086206)}},
        {{UINT32_C(0x0071E1BB), UINT32_C(0x000F9619), UINT32_C(0x0026D84F),
          UINT32_C(0x009F1C46), UINT32_C(0x005B79C0), UINT32_C(0x007AEEC3),
          UINT32_C(0x001874EB), UINT32_C(0x0081978E), UINT32_C(0x00293406),
          UINT32_C(0x0001D4FC), UINT32_C(0x00615B35)},
         {UINT32_C(0x00182D40), UINT32_C(0x0042A332), UINT32_C(0x0017C797),
          UINT32_C(0x00B3E39F), UINT32_C(0x00429FC1), UINT32_C(0x0075CDCC),
          UINT32_C(0x0017A29B), UINT32_C(0x00B485C4), UINT32_C(0x000578E3),
          UINT32_C(0x00267997), UINT32_C(0x0022B736)}},
        {{UINT32_C(0x005B3F31), UINT32_C(0x00208743), UINT32_C(0x005CE60C),
          UINT32_C(0x00A70DDC), UINT32_C(0x000739FA), UINT32_C(0x005A8096),
          UINT32_C(0x0004765E), UINT32_C(0x00060FD2), UINT32_C(0x00317203),
          UINT32_C(0x0078A3F6), UINT32_C(0x0013A216)},
         {UINT32_C(0x00F7310F), UINT32_C(0x006787A8), UINT32_C(0x004CA7F1),
          UINT32_C(0x0058A285), UINT32_C(0x0063A5F2), UINT32_C(0x002D9D33),
          UINT32_C(0x000AE58C), UINT32_C(0x0021A049), UINT32_C(0x005E164E),
          UINT32_C(0x0053ABF4), UINT32_C(0x0012DD24)}},
    },
    {
        {{UINT32_C(0x00BAC990), UINT32_C(0x000DAB38), UINT32_C(0x00717BBA),
          UINT32_C(0x00AC779D), UINT32_C(0x001DCA52), UINT32_C(0x00300C2E),
          UINT32_C(0x002A2255), UINT32_C(0x005A0E8E), UINT32_C(0x0015AB31),
          UINT32_C(0x00313D50), UINT32_C(0x003E1B41)},
         {UINT32_C(0x00528772), UINT32_C(0x003D56AB), UINT32_C(0x00717BB2),
          UINT32_C(0x0087E006), UINT32_C(0x0042AF5A), UINT32_C(0x0057EE11),
          UINT32_C(0x002AC11C), UINT32_C(0x00B30A1D), UINT32_C(0x0062AF80),
          UINT32_C(0x00024671), UINT32_C(0x00138392)}},
        {{UINT32_C(0x0055EF18), UINT32_C(0x00290769), UINT32_C(0x000C06CC),
          UINT32_C(0x00640FB7), UINT32_C(0x0032AA7D), UINT32_C(0x0037CE38),
          UINT32_C(0x00559DB0), UINT32_C(0x009E8D04), UINT32_C(0x001BE9A9),
          UINT32_C(0x0043CD9A), UINT32_C(0x00616E9E)},
         {UINT32_C(0x00A62590), UINT32_C(0x0044D2E5), UINT32_C(0x00438F19),
          UINT32_C(0x007C024D), UINT32_C(0x00387E23), UINT32_C(0x0012F97D),
          UINT32_C(0x00593049), UINT32_C(0x00BBE9F5), UINT32_C(0x002EB4C3),
          UINT32_C(0x00492C4B), UINT32_C(0x000BD31C)}},
        {{UINT32_C(0x004436A6), UINT32_C(0x002F9836), UINT32_C(0x003A1719),
          UINT32_C(0x0013E3D0), UINT32_C(0x0001CE66), UINT32_C(0x0075D201),
          UINT32_C(0x0070741C), UINT32_C(0x003D4468), UINT32_C(0x006001B2),
          UINT32_C(0x00621CB4), UINT32_C(0x004C9A82)},
         {UINT32_C(0x0035D65D), UINT32_C(0x0071BB2D), UINT32_C(0x0027A173),
          UINT32_C(0x00B478E4), UINT32_C(0x003F0595), UINT32_C(0x002A36C0),
          UINT32_C(0x003F12EC), UINT32_C(0x00B45D13), UINT32_C(0x007F8B60),
          UINT32_C(0x0036D006), UINT32_C(0x005F7EAE)}},
        {{UINT32_C(0x0057123B), UINT32_C(0x00100AF5), UINT32_C(0x006392AD),
          UINT32_C(0x00B520A8), UINT32_C(0x002B4722), UINT32_C(0x0000BE83),
          UINT32_C(0x00671127), UINT32_C(0x000DE2DD), UINT32_C(0x00464F89),
          UINT32_C(0x004EDE64), UINT32_C(0x00281953)},
         {UINT32_C(0x0006E6D3), UINT32_C(0x002AA368), UINT32_C(0x004082CA),
          UINT32_C(0x00403695), UINT32_C(0x006E19D7), UINT32_C(0x003560D6),
          UINT32_C(0x000FD064), UINT32_C(0x00E717EF), UINT32_C(0x000A17BF),
          UINT32_C(0x000FF54A), UINT32_C(0x0018D167)}},
        {{UINT32_C(0x00AF932F), UINT32_C(0x0026788D), UINT32_C(0x001C3D62),
          UINT32_C(0x00921966), UINT32_C(0x00210E68), UINT32_C(0x004E43E2),
          UINT32_C(0x00624FD4), UINT32_C(0x007DD4CB), UINT32_C(0x001A32DC),
          UINT32_C(0x003F5F83), UINT32_C(0x00139108)},
         {UINT32_C(0x00BE0A12), UINT32_C(0x00560B9E), UINT32_C(0x002C4C1A),
          UINT32_C(0x007D3F45), UINT32_C(0x00713917), UINT32_C(0x0056EE5F),
          UINT32_C(0x00506607), UINT32_C(0x006C8156), UINT32_C(0x002C0601),
          UINT32_C(0x0045B886), UINT32_C(0x006D6ABD)}},
        {{UINT32_C(0x00BE997B), UINT32_C(0x0040C819), UINT32_C(0x0047F984),
          UINT32_C(0x00C09966), UINT32_C(0x0001FFE5), UINT32_C(0x004210A5),
          UINT32_C(0x0075DC7F), UINT32_C(0x00E1BFAE), UINT32_C(0x0041DF17),
          UINT32_C(0x000DFA6A), UINT32_C(0x0055903D)},
         {UINT32_C(0x00CF7C69), UINT32_C(0x007788BB), UINT32_C(0x006A117E),
          UINT32_C(0x003E1AC5), UINT32_C(0x004BBCC5), UINT32_C(0x006902AA),
          UINT32_C(0x0076BACD), UINT32_C(0x00EC4DA9), UINT32_C(0x003047AA),
          UINT32_C(0x007F78BD), UINT32_C(0x00040CD6)}},
        {{UINT32_C(0x00B34AFC), UINT32_C(0x004F2803), UINT32_C(0x00307B77),
          UINT32_C(0x00F3B150), UINT32_C(0x0071A656), UINT32_C(0x006D60F0),
          UINT32_C(0x00320731), UINT32_C(0x00606ECD), UINT32_C(0x0066A1E7),
          UINT32_C(0x006D5EFC), UINT32_C(0x006DCA0E)},
         {UINT32_C(0x004B7A84), UINT32_C(0x002D8FCA), UINT32_C(0x0068EACD),
          UINT32_C(0x00B3D326), UINT32_C(0x0021F003), UINT32_C(0x001274F2),
          UINT32_C(0x00411750), UINT32_C(0x00D7D4E4), UINT32_C(0x00261A79),
          UINT32_C(0x006D0235), UINT32_C(0x005BB44F)}},
        {{UINT32_C(0x008CE9AA), UINT32_C(0x0056FFF3), UINT32_C(0x007F6688),
          UINT32_C(0x00A20E30), UINT32_C(0x001357D8), UINT32_C(0x003F97D1),
          UINT32_C(0x006C63EF), UINT32_C(0x005D64BB), UINT32_C(0x006175F3),
          UINT32_C(0x0028FE8A), UINT32_C(0x00026291)},
         {UINT32_C(0x00A2F107), UINT32_C(0x006D1807), UINT32_C(0x004AD5AA),
          UINT32_C(0x0063693B), UINT32_C(0x004CEADF), UINT32_C(0x00439B15),
          UINT32_C(0x006BB209), UINT32_C(0x001E66A5), UINT32_C(0x00502CC1),
          UINT32_C(0x0030D183), UINT32_C(0x00351D45)}},
        {{UINT32_C(0x00460DBA), UINT32_C(0x000D5151), UINT32_C(0x005A7EFA),
          UINT32_C(0x00AB4F71), UINT32_C(0x00286506), UINT32_C(0x004AC749),
          UINT32_C(0x0070181C), UINT32_C(0x002D47B1), UINT32_C(0x002C7634),
          UINT32_C(0x0068C60E), UINT32_C(0x0041E76E)},
         {UINT32_C(0x00DBE186), UINT32_C(0x00486815), UINT32_C(0x00458F1B),
          UINT32_C(0x0013062C), UINT32_C(0x0035B25D), UINT32_C(0x0067BFA5),
          UINT32_C(0x0049BFFF), UINT32_C(0x000F9D3F), UINT32_C(0x0072F711),
          UINT32_C(0x006E8339), UINT32_C(0x002ED76D)}},
        {{UINT32_C(0x00DD873D), UINT32_C(0x0068E0AE), UINT32_C(0x00116017),
          UINT32_C(0x0086B448), UINT32_C(0x00586D70), UINT32_C(0x002DC600),
          UINT32_C(0x007DC061), UINT32_C(0x00014AA5), UINT32_C(0x0005E774),
          UINT32_C(0x0064B836), UINT32_C(0x00212A13)},
         {UINT32_C(0x00D59FF3), UINT32_C(0x001F2261), UINT32_C(0x007EB978),
          UINT32_C(0x009DDAF1), UINT32_C(0x00069D7F), UINT32_C(0x0074317B),
          UINT32_C(0x0008E97D), UINT32_C(0x00015669), UINT32_C(0x007E18E6),
          UINT32_C(0x0028F0F0), UINT32_C(0x0024D9DB)}},
        {{UINT32_C(0x0089C78A), UINT32_C(0x0047CAE9), UINT32_C(0x00647445),
          UINT32_C(0x00E0FFD5), UINT32_C(0x0050854E), UINT32_C(0x00398C0B),
          UINT32_C(0x00484454), UINT32_C(0x00D147CC), UINT32_C(0x0009414C),
          UINT32_C(0x0049B209), UINT32_C(0x004216EE)},
         {UINT32_C(0x002C47B9), UINT32_C(0x00023A77), UINT32_C(0x007769B8),
          UINT32_C(0x00F0B4A1), UINT32_C(0x000443DC), UINT32_C(0x007C6A84),
          UINT32_C(0x007F36D0), UINT32_C(0x000D9584), UINT32_C(0x0009D192),
          UINT32_C(0x005324D9), UINT32_C(0x0008CDE7)}},
        {{UINT32_C(0x00EBCF74), UINT32_C(0x00450C29), UINT32_C(0x006EF4CE),
          UINT32_C(0x00E76FBA), UINT32_C(0x00781F10), UINT32_C(0x000435C4),
          UINT32_C(0x0043DA1E), UINT32_C(0x006BFF5B), UINT32_C(0x0026CDB4),
          UINT32_C(0x00047CA9), UINT32_C(0x0034672B)},
         {UINT32_C(0x00FE244B), UINT32_C(0x003776F7), UINT32_C(0x00200F9D),
          UINT32_C(0x0055D105), UINT32_C(0x00398428), UINT32_C(0x00159C77),
          UINT32_C(0x004B172A), UINT32_C(0x0003C3DC), UINT32_C(0x000C6478),
          UINT32_C(0x00360E42), UINT32_C(0x004CCFCC)}},
        {{UINT32_C(0x00A6089E), UINT32_C(0x0052C74F), UINT32_C(0x003028CE),
          UINT32_C(0x00DA6BE7), UINT32_C(0x00159B0D), UINT32_C(0x00025F04),
          UINT32_C(0x007F3B51), UINT32_C(0x00EBC9A6), UINT32_C(0x00410F2F),
          UINT32_C(0x001E2C8F), UINT32_C(0x0044CD45)},
         {UINT32_C(0x00FE529A), UINT32_C(0x005742FE), UINT32_C(0x006446CB),
          UINT32_C(0x0004A884), UINT32_C(0x0055474A), UINT32_C(0x003FE0AE),
          UINT32_C(0x006671D0), UINT32_C(0x00497D2B), UINT32_C(0x0055C10A),
          UINT32_C(0x0048F2CC), UINT32_C(0x006C3D3D)}},
        {{UINT32_C(0x00B5061A), UINT32_C(0x0075965B), UINT32_C(0x004759AF),
          UINT32_C(0x0074ED86), UINT32_C(0x0068EECF), UINT32_C(0x006A53AB),
          UINT32_C(0x000B476C), UINT32_C(0x00AA39AE), UINT32_C(0x00242F10),
          UINT32_C(0x0016E0FD), UINT32_C(0x00310C8E)},
         {UINT32_C(0x00FAB3FE), UINT32_C(0x00412CA0), UINT32_C(0x00334AB3),
          UINT32_C(0x0040E7A6), UINT32_C(0x006F4791), UINT32_C(0x0024B661),
          UINT32_C(0x000713F0), UINT32_C(0x0035B195), UINT32_C(0x00551A61),
          UINT32_C(0x004BC5DD), UINT32_C(0x001112B3)}},
        {{UINT32_C(0x00EEDFDA), UINT32_C(0x0043EF08), UINT32_C(0x007213CC),
          UINT32_C(0x0055FF1F), UINT32_C(0x000AF305), UINT32_C(0x0052FA27),
          UINT32_C(0x00277E8C), UINT32_C(0x00875A0B), UINT32_C(0x002C9F3E),
          UINT32_C(0x00058985), UINT32_C(0x0068C97C)},
         {UINT32_C(0x00F30FC3), UINT32_C(0x00670E75), UINT32_C(0x006B3B6A),
          UINT32_C(0x00AF87DB), UINT32_C(0x005C464A), UINT32_C(0x0024A624),
          UINT32_C(0x007558E2), UINT32_C(0x00937730), UINT32_C(0x005F3E2C),
          UINT32_C(0x004CF5B9), UINT32_C(0x005EA094)}},
        {{UINT32_C(0x00D3CC63), UINT32_C(0x000E5711), UINT32_C(0x001DC16D),
          UINT32_C(0x004A13FD), UINT32_C(0x004FE8D5), UINT32_C(0x00302514),
          UINT32_C(0x006F576B), UINT32_C(0x007AC1DD), UINT32_C(0x0040C7EA),
          UINT32_C(0x002175BC), UINT32_C(0x007E7325)},
         {UINT32_C(0x002B1252), UINT32_C(0x003D7D6A), UINT32_C(0x001CF79F),
          UINT32_C(0x002D0A89), UINT32_C(0x00081836), UINT32_C(0x000521DA),
          UINT32_C(0x0071B27F), UINT32_C(0x009F8D74), UINT32_C(0x00109461),
          UINT32_C(0x0029855D), UINT32_C(0x0047654D)}},
    },
    {
        {{UINT32_C(0x0049F59D), UINT32_C(0x003D9378), UINT32_C(0x00531A27),
          UINT32_C(0x00D81B1D), UINT32_C(0x007A0818), UINT32_C(0x00283F4B),
          UINT32_C(0x0069AA59), UINT32_C(0x00FBCF88), UINT32_C(0x00194F29),
          UINT32_C(0x00683FCE), UINT32_C(0x00555BAE)},
         {UINT32_C(0x006D73CE), UINT32_C(0x0050A230), UINT32_C(0x0016AC25),
          UINT32_C(0x007FE177), UINT32_C(0x0079A4A7), UINT32_C(0x00240036),
          UINT32_C(0x0078060E), UINT32_C(0x00B4028C), UINT32_C(0x00650D07),
          UINT32_C(0x0074C9ED), UINT32_C(0x0001BC10)}},
        {{UINT32_C(0x00D581B9), UINT32_C(0x000A2BB8), UINT32_C(0x001A6502),
          UINT32_C(0x0048F62D), UINT32_C(0x00259F0C), UINT32_C(0x00594632),
          UINT32_C(0x003D1E9F), UINT32_C(0x001BBAB7), UINT32_C(0x004EF7A0),
          UINT32_C(0x0021EEB3), UINT32_C(0x0023620C)},
         {UINT32_C(0x003A641C), UINT32_C(0x0058B33A), UINT32_C(0x004729CE),
          UINT32_C(0x00600724), UINT32_C(0x005E4C80), UINT32_C(0x006F5FC3),
          UINT32_C(0x001CB9C0), UINT32_C(0x00D4C679), UINT32_C(0x0067E0DE),
          UINT32_C(0x007918C5), UINT32_C(0x00446E3C)}},
        {{UINT32_C(0x00FCF7B6), UINT32_C(0x002A4017), UINT32_C(0x00770419),
          UINT32_C(0x009EBA8C), UINT32_C(0x000C0745), UINT32_C(0x00056DAC),
          UINT32_C(0x006BE2AE), UINT32_C(0x00D5418B), UINT32_C(0x0059BCAE),
          UINT32_C(0x005EB51A), UINT32_C(0x000A7CFF)},
         {UINT32_C(0x00146CD4), UINT32_C(0x005F21A7), UINT32_C(0x00732219),
          UINT32_C(0x00708B02), UINT32_C(0x0059FF76), UINT32_C(0x00223DE6),
          UINT32_C(0x0068D0EA), UINT32_C(0x0095440B), UINT32_C(0x004AC2BE),
          UINT32_C(0x0044CD99), UINT32_C(0x00418B1F)}},
        {{UINT32_C(0x007826E7), UINT32_C(0x00302147), UINT32_C(0x004239FE),
          UINT32_C(0x0012F403), UINT32_C(0x0066A878), UINT32_C(0x000BB8B3),
          UINT32_C(0x00657754), UINT32_C(0x00956FD7), UINT32_C(0x002ABB73),
          UINT32_C(0x004B585E), UINT32_C(0x001D0D54)},
         {UINT32_C(0x00D8482A), UINT32_C(0x00490F0D), UINT32_C(0x00587E9A),
          UINT32_C(0x00633FC6), UINT32_C(0x0047A327), UINT32_C(0x002FE955),
          UINT32_C(0x002C1A30), UINT32_C(0x00299544), UINT32_C(0x0012A81A),
          UINT32_C(0x001B2908), UINT32_C(0x006A4721)}},
        {{UINT32_C(0x0010E70C), UINT32_C(0x001F9118), UINT32_C(0x007A2E42),
          UINT32_C(0x00AD234C), UINT32_C(0x0067A2B5), UINT32_C(0x0045B66B),
          UINT32_C(0x00775A55), UINT32_C(0x00CE1819), UINT32_C(0x0036B0B3),
          UINT32_C(0x0009BD41), UINT32_C(0x00357CCE)},
         {UINT32_C(0x0017F79E), UINT32_C(0x0031F0EF), UINT32_C(0x00297396),
          UINT32_C(0x009B34C2), UINT32_C(0x003763BA), UINT32_C(0x007CD667),
          UINT32_C(0x0027859A), UINT32_C(0x0065B432), UINT32_C(0x004FFA96),
          UINT32_C(0x002D2331), UINT32_C(0x005046AE)}},
        {{UINT32_C(0x006316B9), UINT32_C(0x00321283), UINT32_C(0x0013534D),
          UINT32_C(0x00A66C71), UINT32_C(0x0041D154), UINT32_C(0x003BF498),
          UINT32_C(0x0021F82F), UINT32_C(0x0029DB77), UINT32_C(0x004A36E0),
          UINT32_C(0x00263FF9), UINT32_C(0x00703088)},
         {UINT32_C(0x00DC79C4), UINT32_C(0x0037B8E9), UINT32_C(0x004453ED),
          UINT32_C(0x00A2C845), UINT32_C(0x0064D04E), UINT32_C(0x003404E0),
          UINT32_C(0x0075E149), UINT32_C(0x008FF0EF), UINT32_C(0x003532A0),
          UINT32_C(0x004B7BF9), UINT32_C(0x0051B403)}},
        {{UINT32_C(0x00D0B765), UINT32_C(0x006D30B8), UINT32_C(0x000EA988),
          UINT32_C(0x00C14682), UINT32_C(0x0054EF11), UINT32_C(0x0064F84D),
          UINT32_C(0x0056C161), UINT32_C(0x008DF7C5), UINT32_C(0x00504962),
          UINT32_C(0x00486862), UINT32_C(0x0072A82D)},
         {UINT32_C(0x0077FA35), UINT32_C(0x0014BC95), UINT32_C(0x0020003C),
          UINT32_C(0x004F613A), UINT32_C(0x0056E698), UINT32_C(0x00605FF1),
          UINT32_C(0x0053BEE6), UINT32_C(0x003D3088), UINT32_C(0x00566658),
          UINT32_C(0x0072C01C), UINT32_C(0x00128DDA)}},
        {{UINT32_C(0x00B5B469), UINT32_C(0x00265F62), UINT32_C(0x00740D01),
          UINT32_C(0x002737CE), UINT32_C(0x0016B851), UINT32_C(0x000B913E),
          UINT32_C(0x00365599), UINT32_C(0x00CED53E), UINT32_C(0x00031BE6),
          UINT32_C(0x0007B5B7), UINT32_C(0x002826D2)},
         {UINT32_C(0x007CE72C), UINT32_C(0x0035F7AE), UINT32_C(0x00122292),
          UINT32_C(0x00F0BCF2), UINT32_C(0x00583D09), UINT32_C(0x000865FB),
          UINT32_C(0x004255E4), UINT32_C(0x00C0E15D), UINT32_C(0x0014E9D6),
          UINT32_C(0x0065A407), UINT32_C(0x00113864)}},
        {{UINT32_C(0x00D0FA32), UINT32_C(0x00731256), UINT32_C(0x0008677B),
          UINT32_C(0x00058BD3), UINT32_C(0x0011CF96), UINT32_C(0x001E2EF5),
          UINT32_C(0x00725AB8), UINT32_C(0x005A8080), UINT32_C(0x006AEB60),
          UINT32_C(0x006FE8CF), UINT32_C(0x007F2F4F)},
         {UINT32_C(0x00C3108A), UINT32_C(0x00760FFA), UINT32_C(0x005F4E31),
          UINT32_C(0x0071D122), UINT32_C(0x0017DEC0), UINT32_C(0x004BCA9E),
          UINT32_C(0x00140AF5), UINT32_C(0x00CB9CE3), UINT32_C(0x0014BFC1),
          UINT32_C(0x0064DA23), UINT32_C(0x000C63A5)}},
        {{UINT32_C(0x0066EFDB), UINT32_C(0x002864DE), UINT32_C(0x007211BA),
          UINT32_C(0x00D9EAA4), UINT32_C(0x00593664), UINT32_C(0x0037C3A8),
          UINT32_C(0x006DE16A), UINT32_C(0x00D849C3), UINT32_C(0x00331ED5),
          UINT32_C(0x005C0AE8), UINT32_C(0x0041D7E1)},
         {UINT32_C(0x008CE7E3), UINT32_C(0x004DF2A8), UINT32_C(0x0039C4B2),
          UINT32_C(0x001A054E), UINT32_C(0x00651CF9), UINT32_C(0x001B953C),
          UINT32_C(0x003FF29C), UINT32_C(0x007BF5B4), UINT32_C(0x00322902),
          UINT32_C(0x000942AC), UINT32_C(0x00152316)}},
        {{UINT32_C(0x00DFBF1A), UINT32_C(0x005DD706), UINT32_C(0x00665070),
          UINT32_C(0x004EA28B), UINT32_C(0x00253E73), UINT32_C(0x005DF0EC),
          UINT32_C(0x00175C48), UINT32_C(0x00D7A1A4), UINT32_C(0x00793351),
          UINT32_C(0x00029BF8), UINT32_C(0x00434300)},
         {UINT32_C(0x00CB3A82), UINT32_C(0x006DE550), UINT32_C(0x0041DC49),
          UINT32_C(0x0082B72D), UINT32_C(0x000DC7E4), UINT32_C(0x0061BB06),
          UINT32_C(0x0026A846), UINT32_C(0x00CC5702), UINT32_C(0x005E2CDD),
          UINT32_C(0x007063CA), UINT32_C(0x003C8621)}},
        {{UINT32_C(0x00181E78), UINT32_C(0x0077B355), UINT32_C(0x00418907),
          UINT32_C(0x00D9A8F6), UINT32_C(0x0072DED2), UINT32_C(0x004E3E02),
          UINT32_C(0x004E03AD), UINT32_C(0x00F3B5A8), UINT32_C(0x0050F28E),
          UINT32_C(0x006CF224), UINT32_C(0x0024C9C3)},
         {UINT32_C(0x00850A90), UINT32_C(0x00623DEB), UINT32_C(0x0024F9A6),
          UINT32_C(0x00D6DFA0), UINT32_C(0x0048D930), UINT32_C(0x0016FD61),
          UINT32_C(0x0020F2BD), UINT32_C(0x006DACA1), UINT32_C(0x003AE220),
          UINT32_C(0x00446D52), UINT32_C(0x0049678D)}},
        {{UINT32_C(0x00F04B0C), UINT32_C(0x00586E6C), UINT32_C(0x0009DA9E),
          UINT32_C(0x00ACA51E), UINT32_C(0x00779415), UINT32_C(0x00693107),
          UINT32_C(0x00727567), UINT32_C(0x00D0F4C5), UINT32_C(0x002C7C2D),
          UINT32_C(0x006A4DC6), UINT32_C(0x007BE95F)},
         {UINT32_C(0x0019999C), UINT32_C(0x007F0AEF), UINT32_C(0x007C8778),
          UINT32_C(0x002D0089), UINT32_C(0x004CD9C4), UINT32_C(0x0056522D),
          UINT32_C(0x00396E96), UINT32_C(0x007ABC11), UINT32_C(0x0033E16F),
          UINT32_C(0x005E515A), UINT32_C(0x005E9DD0)}},
        {{UINT32_C(0x001F0474), UINT32_C(0x004D7C5A), UINT32_C(0x007B2A69),
          UINT32_C(0x00C61380), UINT32_C(0x007415C0), UINT32_C(0x001C95A8),
          UINT32_C(0x00338442), UINT32_C(0x005479B1), UINT32_C(0x0059192B),
          UINT32_C(0x0029E0CF), UINT32_C(0x006FA0A0)},
         {UINT32_C(0x004F6E5A), UINT32_C(0x007DA4EC), UINT32_C(0x002E8E89),
          UINT32_C(0x00ABDC5B), UINT32_C(0x0040143A), UINT32_C(0x00766FBA),
          UINT32_C(0x007A98D8), UINT32_C(0x0063DA1D), UINT32_C(0x006373BE),
          UINT32_C(0x002952A0), UINT32_C(0x0068B7FE)}},
        {{UINT32_C(0x003C913F), UINT32_C(0x0002C389), UINT32_C(0x001BB9C3),
          UINT32_C(0x0040D559), UINT32_C(0x00543B6F), UINT32_C(0x005E27BB),
          UINT32_C(0x0051477D), UINT32_C(0x0018DE74), UINT32_C(0x00617EB9),
          UINT32_C(0x00410DFE), UINT32_C(0x001E172F)},
         {UINT32_C(0x0083EE97), UINT32_C(0x0079C4B1), UINT32_C(0x0070D016),
          UINT32_C(0x009399BA), UINT32_C(0x0032C96A), UINT32_C(0x00667A8E),
          UINT32_C(0x003D5F84), UINT32_C(0x00608421), UINT32_C(0x006E6662),
          UINT32_C(0x0008B758), UINT32_C(0x001D9639)}},
        {{UINT32_C(0x00A6524D), UINT32_C(0x0074CFF4), UINT32_C(0x0026D563),
          UINT32_C(0x00A1F297), UINT32_C(0x003FA9DE), UINT32_C(0x0073EA40),
          UINT32_C(0x000E3CE4), UINT32_C(0x00492223), UINT32_C(0x00638A0B),
          UINT32_C(0x0047888B), UINT32_C(0x0050A52B)},
         {UINT32_C(0x0021A754), UINT32_C(0x0006812D), UINT32_C(0x0059DFAC),
          UINT32_C(0x00B33A1F), UINT32_C(0x007B6001), UINT32_C(0x0045BA94),
          UINT32_C(0x00187867), UINT32_C(0x00D3B5C2), UINT32_C(0x007F2118),
          UINT32_C(0x00091632), UINT32_C(0x001CE749)}},
    },
    {
        {{UINT32_C(0x00C5B684), UINT32_C(0x003B82D3), UINT32_C(0x007BB04F),
          UINT32_C(0x006CD54F), UINT32_C(0x0017CB99), UINT32_C(0x005C4905),
          UINT32_C(0x00013115), UINT32_C(0x00011FE8), UINT32_C(0x00274533),
          UINT32_C(0x00283F47), UINT32_C(0x002C4F1C)},
         {UINT32_C(0x00251A27), UINT32_C(0x007B5C49), UINT32_C(0x003D9D4C),
          UINT32_C(0x00E97FBF), UINT32_C(0x001F3DD9), UINT32_C(0x0049FA49),
          UINT32_C(0x006C76A8), UINT32_C(0x009EFFDD), UINT32_C(0x0070FD77),
          UINT32_C(0x00197D68), UINT32_C(0x0043D69E)}},
        {{UINT32_C(0x00F54825), UINT32_C(0x004E704B), UINT32_C(0x005B81C6),
          UINT32_C(0x00F6C679), UINT32_C(0x004A0326), UINT32_C(0x000993ED),
          UINT32_C(0x00223F94), UINT32_C(0x005CACE3), UINT32_C(0x0079C440),
          UINT32_C(0x001BF3AB), UINT32_C(0x004F0A3D)},
         {UINT32_C(0x0094C40C), UINT32_C(0x004AC81F), UINT32_C(0x007FF007),
          UINT32_C(0x00E4DA23), UINT32_C(0x0012C360), UINT32_C(0x00679697),
          UINT32_C(0x00091A3D), UINT32_C(0x006BB95A), UINT32_C(0x005FF46C),
          UINT32_C(0x003D9D5F), UINT32_C(0x006C42A5)}},
        {{UINT32_C(0x008C8CCF), UINT32_C(0x007FC2E6), UINT32_C(0x0063D1F2),
          UINT32_C(0x0073B8A7), UINT32_C(0x004CFD1D), UINT32_C(0x000A0D59),
          UINT32_C(0x0067D4C7), UINT32_C(0x00F46F06), UINT32_C(0x00044A4E),
          UINT32_C(0x007884E7), UINT32_C(0x0011BD78)},
         {UINT32_C(0x00E46006), UINT32_C(0x00593D9C), UINT32_C(0x00600552),
          UINT32_C(0x009BE40C), UINT32_C(0x006F055F), UINT32_C(0x0050000F),
          UINT32_C(0x007CAEF7), UINT32_C(0x00487980), UINT32_C(0x0018C50F),
          UINT32_C(0x002C8819), UINT32_C(0x001C9DF4)}},
        {{UINT32_C(0x00142486), UINT32_C(0x0011204B), UINT32_C(0x00546138),
          UINT32_C(0x00A0F3FD), UINT32_C(0x007C7277), UINT32_C(0x0062F53E),
          UINT32_C(0x00405474), UINT32_C(0x00E13911), UINT32_C(0x0073FFC0),
          UINT32_C(0x003F1CE2), UINT32_C(0x004E8EC0)},
         {UINT32_C(0x00CEA3A2), UINT32_C(0x006ACFEA), UINT32_C(0x00644863),
          UINT32_C(0x006C5ADA), UINT32_C(0x0059B648), UINT32_C(0x005EE6F5),
          UINT32_C(0x006B9451), UINT32_C(0x00C22C63), UINT32_C(0x00250D0A),
          UINT32_C(0x0040C2E2), UINT32_C(0x00633B20)}},
        {{UINT32_C(0x000DF8D9), UINT32_C(0x006FDE02), UINT32_C(0x007BC761),
          UINT32_C(0x00DD1647), UINT32_C(0x003946F5), UINT32_C(0x004F8B89),
          UINT32_C(0x0021BD28), UINT32_C(0x00D13F8D), UINT32_C(0x0026F183),
          UINT32_C(0x003CAFFB), UINT32_C(0x00526839)},
         {UINT32_C(0x002D8549), UINT32_C(0x0051C848), UINT32_C(0x0065DAAB),
          UINT32_C(0x00F4931B), UINT32_C(0x001DB7B7), UINT32_C(0x006B5984),
          UINT32_C(0x0002D8C2), UINT32_C(0x00399E59), UINT32_C(0x00348B00),
          UINT32_C(0x002C9BAC), UINT32_C(0x000561D0)}},
        {{UINT32_C(0x002E9251), UINT32_C(0x00730381), UINT32_C(0x00158D1C),
          UINT32_C(0x00EAA590), UINT32_C(0x00555783), UINT32_C(0x003A7A13),
          UINT32_C(0x007834C8), UINT32_C(0x00ACBD10), UINT32_C(0x006C9902),
          UINT32_C(0x0023C1F1), UINT32_C(0x001B3699)},
         {UINT32_C(0x0096FFD5), UINT32_C(0x007117AC), UINT32_C(0x00024B6B),
          UINT32_C(0x008D0146), UINT32_C(0x000E4DC9), UINT32_C(0x00191916),
          UINT32_C(0x000FC5A6), UINT32_C(0x00FE7250), UINT32_C(0x00198308),
          UINT32_C(0x0001FAC2), UINT32_C(0x0024DA1C)}},
        {{UINT32_C(0x008A2315), UINT32_C(0x007E110A), UINT32_C(0x005E7BF4),
          UINT32_C(0x007BEAF8), UINT32_C(0x003605ED), UINT32_C(0x001F3AF3),
          UINT32_C(0x007876E7), UINT32_C(0x0046E4E3), UINT32_C(0x006EC7A7),
          UINT32_C(0x0074A230), UINT32_C(0x007EF25D)},
         {UINT32_C(0x000F6E54), UINT32_C(0x001AD49C), UINT32_C(0x0052B335),
          UINT32_C(0x00FE212A), UINT32_C(0x003A9CF2), UINT32_C(0x00318045),
          UINT32_C(0x0058F544), UINT32_C(0x0065B525), UINT32_C(0x003D646B),
          UINT32_C(0x004DA78A), UINT32_C(0x00552661)}},
        {{UINT32_C(0x003F6E66), UINT32_C(0x007E10BA), UINT32_C(0x00341D94),
          UINT32_C(0x004AB8A0), UINT32_C(0x004ED33D), UINT32_C(0x002E5C25),
          UINT32_C(0x00109A3B), UINT32_C(0x001454FD), UINT32_C(0x007C8D6A),
          UINT32_C(0x007942BA), UINT32_C(0x002B4544)},
         {UINT32_C(0x00BE14E9), UINT32_C(0x00446F85), UINT32_C(0x0075AA4C),
          UINT32_C(0x00BEFBB7), UINT32_C(0x00354E79), UINT32_C(0x0041906F),
          UINT32_C(0x0040AC0F), UINT32_C(0x00C8D73F), UINT32_C(0x001F31BC),
          UINT32_C(0x00662285), UINT32_C(0x0027E73B)}},
        {{UINT32_C(0x005B910E), UINT32_C(0x00037DF0), UINT32_C(0x006463A9),
          UINT32_C(0x00E6B081), UINT32_C(0x002D0A9D), UINT32_C(0x002ED8BE),
          UINT32_C(0x0060E9CA), UINT32_C(0x00DC8968), UINT32_C(0x0017563E),
          UINT32_C(0x00350D14), UINT32_C(0x006DB2D5)},
         {UINT32_C(0x008E171C), UINT32_C(0x000EB1AA), UINT32_C(0x000E3AEA),
          UINT32_C(0x00DCD35E), UINT32_C(0x0024D5B2), UINT32_C(0x0078E926),
          UINT32_C(0x006E5A44), UINT32_C(0x009E3934), UINT32_C(0x0043D3D7),
          UINT32_C(0x000F2ECE), UINT32_C(0x00506FB6)}},
        {{UINT32_C(0x00B8E76E), UINT32_C(0x003E9FBC), UINT32_C(0x006624FA),
          UINT32_C(0x000338E5), UINT32_C(0x0022CDAF), UINT32_C(0x001658AA),
          UINT32_C(0x004EDFF5), UINT32_C(0x005C5CC6), UINT32_C(0x0074872E),
          UINT32_C(0x0006FF1F), UINT32_C(0x003D71F4)},
         {UINT32_C(0x00E59640), UINT32_C(0x006B6980), UINT32_C(0x0052C2CC),
          UINT32_C(0x00CB86FD), UINT32_C(0x00212D1D), UINT32_C(0x004C97C7),
          UINT32_C(0x00605C17), UINT32_C(0x00AAE9D8), UINT32_C(0x005073CD),
          UINT32_C(0x00361947), UINT32_C(0x000E77D9)}},
        {{UINT32_C(0x0016387D), UINT32_C(0x0049B942), UINT32_C(0x001B3FA6),
          UINT32_C(0x00EA0088), UINT32_C(0x00418D2D), UINT32_C(0x003D0233),
          UINT32_C(0x0011F489), UINT32_C(0x001FACB0), UINT32_C(0x00029CF4),
          UINT32_C(0x00039194), UINT32_C(0x00483410)},
         {UINT32_C(0x00F9A7EC), UINT32_C(0x001832C2), UINT32_C(0x00459839),
          UINT32_C(0x004D00CF), UINT32_C(0x003F4B21), UINT32_C(0x005FC44A),
          UINT32_C(0x000CA90A), UINT32_C(0x0024985D), UINT32_C(0x000D9596),
          UINT32_C(0x004A9A33), UINT32_C(0x007D3439)}},
        {{UINT32_C(0x0044CABA), UINT32_C(0x00589FC6), UINT32_C(0x00575DF8),
          UINT32_C(0x008AC944), UINT32_C(0x0047E0CD), UINT32_C(0x000567F3),
          UINT32_C(0x006783A3), UINT32_C(0x0081A081), UINT32_C(0x00720557),
          UINT32_C(0x002CC917), UINT32_C(0x006BAFD4)},
         {UINT32_C(0x0002EF38), UINT32_C(0x00089C79), UINT32_C(0x0039C854),
          UINT32_C(0x00A4BCBE), UINT32_C(0x000969F1), UINT32_C(0x00452E91),
          UINT32_C(0x00645379), UINT32_C(0x00546133), UINT32_C(0x001F829A),
          UINT32_C(0x006A9523), UINT32_C(0x006A5CF6)}},
        {{UINT32_C(0x0041529B), UINT32_C(0x005CB920), UINT32_C(0x005E7A02),
          UINT32_C(0x00C8A1A2), UINT32_C(0x001F9327), UINT32_C(0x004174C9),
          UINT32_C(0x007E2F98), UINT32_C(0x00452989), UINT32_C(0x004CD5A3),
          UINT32_C(0x007FB826), UINT32_C(0x0073BCEB)},
         {UINT32_C(0x002F5CBC), UINT32_C(0x007CBCD0), UINT32_C(0x00130359),
          UINT32_C(0x00B09201), UINT32_C(0x0018EFD9), UINT32_C(0x00202C9E),
          UINT32_C(0x006A4C0F), UINT32_C(0x00DF08A6), UINT32_C(0x000F65C7),
          UINT32_C(0x006E8BA1), UINT32_C(0x0057C201)}},
        {{UINT32_C(0x00F2D278), UINT32_C(0x007BCA3E), UINT32_C(0x000073A8),
          UINT32_C(0x00AB869F), UINT32_C(0x0014FC27), UINT32_C(0x00682826),
          UINT32_C(0x007B8F76), UINT32_C(0x00E02FC4), UINT32_C(0x004DDAD8),
          UINT32_C(0x0051D300), UINT32_C(0x005D3332)},
         {UINT32_C(0x00A06CF9), UINT32_C(0x00490FEC), UINT32_C(0x00272AAC),
          UINT32_C(0x009F4648), UINT32_C(0x0071C8FB), UINT32_C(0x00566093),
          UINT32_C(0x0078852D), UINT32_C(0x00E7DB13), UINT32_C(0x004E79FE),
          UINT32_C(0x0012F1F6), UINT32_C(0x0056F872)}},
        {{UINT32_C(0x00B0F1BD), UINT32_C(0x001B52B2), UINT32_C(0x007EDAE0),
          UINT32_C(0x00B65682), UINT32_C(0x00433C6A), UINT32_C(0x0005BDE7),
          UINT32_C(0x001EE949), UINT32_C(0x00515F90), UINT32_C(0x0020A67A),
          UINT32_C(0x004EBC14), UINT32_C(0x00236F70)},
         {UINT32_C(0x00FA1D8C), UINT32_C(0x006D40AB), UINT32_C(0x0030DC7D),
          UINT32_C(0x0056A7E9), UINT32_C(0x0019E584), UINT32_C(0x00092219),
          UINT32_C(0x005A6D72), UINT32_C(0x00EE7EAE), UINT32_C(0x0079A1E5),
          UINT32_C(0x006D6145), UINT32_C(0x00055F0C)}},
        {{UINT32_C(0x00C54CAB), UINT32_C(0x0014B3C9), UINT32_C(0x005B9541),
          UINT32_C(0x00FA0F3C), UINT32_C(0x004CFCD2), UINT32_C(0x00596010),
          UINT32_C(0x0055BCC4), UINT32_C(0x001502AE), UINT32_C(0x0070CB46),
          UINT32_C(0x003B6582), UINT32_C(0x00729AD7)},
         {UINT32_C(0x00AE8A66), UINT32_C(0x00022B51), UINT32_C(0x00608650),
          UINT32_C(0x00CC908A), UINT32_C(0x0076C532), UINT32_C(0x0041E1EA),
          UINT32_C(0x007F34BC), UINT32_C(0x000289C8), UINT32_C(0x00717892),
          UINT32_C(0x00465F29), UINT32_C(0x003C38C2)}},
    }};

/*-
 * Q := 2P, both projective, Q and P same pointers OK
 * Autogenerated: op3/dbl_proj.op3
 * https://eprint.iacr.org/2015/1060 Alg 6
 * ASSERT: a = -3
 */
static void point_double(pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X = P->X;
    const limb_t *Y = P->Y;
    const limb_t *Z = P->Z;
    limb_t *X3 = Q->X;
    limb_t *Y3 = Q->Y;
    limb_t *Z3 = Q->Z;

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t0, X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t1, Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_square(t2, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, X, Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, Y, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, X, Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, X3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t2, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t2, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, Z3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, Z3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, t0, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t4, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t0, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, Z3);
}

/*-
 * out1 = (arg1 == 0) ? 0 : nz
 * NB: this is not a "mod p equiv" 0, but literal 0
 * NB: this is not a real Fiat function, just named that way for consistency.
 */
static void fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_nonzero(
    limb_t *out1, const fe_t arg1) {
    limb_t x1 = 0;
    int i;

    for (i = 0; i < LIMB_CNT; i++) x1 |= arg1[i];
    *out1 = x1;
}

/*-
 * R := Q + P where R and Q are projective, P affine.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_mixed.op3
 * https://eprint.iacr.org/2015/1060 Alg 5
 * ASSERT: a = -3
 */
static void point_add_mixed(pt_prj_t *R, const pt_prj_t *Q, const pt_aff_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    fe_t X3;
    fe_t Y3;
    fe_t Z3;
    limb_t nz;

    /* check P for affine inf */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_nonzero(&nz, P->Y);

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, X1, X2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, Y1, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, X2, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, X1, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, Y2, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t4, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X2, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, X1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, X3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Z1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t1, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t4, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, t0, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, t3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t4, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t1);

    /* if P is inf, throw all that away and take Q */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->X, nz, Q->X, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->Y, nz, Q->Y, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(R->Z, nz, Q->Z, Z3);
}

/*-
 * R := Q + P all projective.
 * R and Q same pointers OK
 * R and P same pointers not OK
 * Autogenerated: op3/add_proj.op3
 * https://eprint.iacr.org/2015/1060 Alg 4
 * ASSERT: a = -3
 */
static void point_add_proj(pt_prj_t *R, const pt_prj_t *Q, const pt_prj_t *P) {
    /* temporary variables */
    fe_t t0, t1, t2, t3, t4, t5;
    /* constants */
    const limb_t *b = const_b;
    /* set pointers for legacy curve arith */
    const limb_t *X1 = Q->X;
    const limb_t *Y1 = Q->Y;
    const limb_t *Z1 = Q->Z;
    const limb_t *X2 = P->X;
    const limb_t *Y2 = P->Y;
    const limb_t *Z2 = P->Z;
    limb_t *X3 = R->X;
    limb_t *Y3 = R->Y;
    limb_t *Z3 = R->Z;

    /* the curve arith formula */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t0, X1, X2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, Y1, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, Z1, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t3, X1, Y1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, X2, Y2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, t0, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t3, t3, t4);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t4, Y1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t5, Y2, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t4, t4, t5);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t5, t1, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t4, t4, t5);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X1, Z1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, X2, Z2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, X3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, b, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, Y3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, X3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Z3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(X3, t1, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, b, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t2, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t2, t1, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(Y3, Y3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, Y3, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, t1, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t1, t0, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(t0, t1, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(t0, t0, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t4, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t2, t0, Y3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Y3, X3, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Y3, Y3, t2);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(X3, t3, X3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_sub(X3, X3, t1);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(Z3, t4, Z3);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(t1, t3, t0);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_add(Z3, Z3, t1);
}

/* constants */
#define RADIX 5
#define DRADIX (1 << RADIX)
#define DRADIX_WNAF ((DRADIX) << 1)

/*-
 * precomp for wnaf scalar multiplication:
 * precomp[0] = 1P
 * precomp[1] = 3P
 * precomp[2] = 5P
 * precomp[3] = 7P
 * precomp[4] = 9P
 * ...
 */
static void precomp_wnaf(pt_prj_t precomp[DRADIX / 2], const pt_aff_t *P) {
    int i;

    fe_copy(precomp[0].X, P->X);
    fe_copy(precomp[0].Y, P->Y);
    fe_copy(precomp[0].Z, const_one);
    point_double(&precomp[DRADIX / 2 - 1], &precomp[0]);

    for (i = 1; i < DRADIX / 2; i++)
        point_add_proj(&precomp[i], &precomp[DRADIX / 2 - 1], &precomp[i - 1]);
}

/* fetch a scalar bit */
static int scalar_get_bit(const unsigned char in[32], int idx) {
    int widx, rshift;

    widx = idx >> 3;
    rshift = idx & 0x7;

    if (idx < 0 || widx >= 32) return 0;

    return (in[widx] >> rshift) & 0x1;
}

/*-
 * Compute "regular" wnaf representation of a scalar.
 * See "Exponent Recoding and Regular Exponentiation Algorithms",
 * Tunstall et al., AfricaCrypt 2009, Alg 6.
 * It forces an odd scalar and outputs digits in
 * {\pm 1, \pm 3, \pm 5, \pm 7, \pm 9, ...}
 * i.e. signed odd digits with _no zeroes_ -- that makes it "regular".
 */
static void scalar_rwnaf(int8_t out[52], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = (in[0] & (DRADIX_WNAF - 1)) | 1;
    for (i = 0; i < 51; i++) {
        d = (window & (DRADIX_WNAF - 1)) - DRADIX;
        out[i] = d;
        window = (window - d) >> RADIX;
        window += scalar_get_bit(in, (i + 1) * RADIX + 1) << 1;
        window += scalar_get_bit(in, (i + 1) * RADIX + 2) << 2;
        window += scalar_get_bit(in, (i + 1) * RADIX + 3) << 3;
        window += scalar_get_bit(in, (i + 1) * RADIX + 4) << 4;
        window += scalar_get_bit(in, (i + 1) * RADIX + 5) << 5;
    }
    out[i] = window;
}

/*-
 * Compute "textbook" wnaf representation of a scalar.
 * NB: not constant time
 */
static void scalar_wnaf(int8_t out[257], const unsigned char in[32]) {
    int i;
    int8_t window, d;

    window = in[0] & (DRADIX_WNAF - 1);
    for (i = 0; i < 257; i++) {
        d = 0;
        if ((window & 1) && ((d = window & (DRADIX_WNAF - 1)) & DRADIX))
            d -= DRADIX_WNAF;
        out[i] = d;
        window = (window - d) >> 1;
        window += scalar_get_bit(in, i + 1 + RADIX) << RADIX;
    }
}

/*-
 * Simultaneous scalar multiplication: interleaved "textbook" wnaf.
 * NB: not constant time
 */
static void var_smul_wnaf_two(pt_aff_t *out, const unsigned char a[32],
                              const unsigned char b[32], const pt_aff_t *P) {
    int i, d, is_neg, is_inf = 1, flipped = 0;
    int8_t anaf[257] = {0};
    int8_t bnaf[257] = {0};
    pt_prj_t Q = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_wnaf(anaf, a);
    scalar_wnaf(bnaf, b);

    for (i = 256; i >= 0; i--) {
        if (!is_inf) point_double(&Q, &Q);
        if ((d = bnaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &precomp[d].X);
                fe_copy(Q.Y, &precomp[d].Y);
                fe_copy(Q.Z, &precomp[d].Z);
                is_inf = 0;
            } else
                point_add_proj(&Q, &Q, &precomp[d]);
        }
        if ((d = anaf[i])) {
            if ((is_neg = d < 0) != flipped) {
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
                flipped ^= 1;
            }
            d = (is_neg) ? (-d - 1) >> 1 : (d - 1) >> 1;
            if (is_inf) {
                /* initialize accumulator */
                fe_copy(Q.X, &lut_cmb[0][d].X);
                fe_copy(Q.Y, &lut_cmb[0][d].Y);
                fe_copy(Q.Z, const_one);
                is_inf = 0;
            } else
                point_add_mixed(&Q, &Q, &lut_cmb[0][d]);
        }
    }

    if (is_inf) {
        /* initialize accumulator to inf: all-zero scalars */
        fe_set_zero(Q.X);
        fe_copy(Q.Y, const_one);
        fe_set_zero(Q.Z);
    }

    if (flipped) {
        /* correct sign */
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(Q.Y, Q.Y);
    }

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Variable point scalar multiplication with "regular" wnaf.
 */
static void var_smul_rwnaf(pt_aff_t *out, const unsigned char scalar[32],
                           const pt_aff_t *P) {
    int i, j, d, diff, is_neg;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, lut = {0};
    pt_prj_t precomp[DRADIX / 2];

    precomp_wnaf(precomp, P);
    scalar_rwnaf(rnaf, scalar);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    /* initialize accumulator to high digit */
    d = (rnaf[51] - 1) >> 1;
    for (j = 0; j < DRADIX / 2; j++) {
        diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, diff, Q.X,
                                                              precomp[j].X);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, diff, Q.Y,
                                                              precomp[j].Y);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, diff, Q.Z,
                                                              precomp[j].Z);
    }

    for (i = 50; i >= 0; i--) {
        for (j = 0; j < RADIX; j++) point_double(&Q, &Q);
        d = rnaf[i];
        /* is_neg = (d < 0) ? 1 : 0 */
        is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
        /* d = abs(d) */
        d = (d ^ -is_neg) + is_neg;
        d = (d - 1) >> 1;
        for (j = 0; j < DRADIX / 2; j++) {
            diff = (1 - (-(d ^ j) >> (8 * sizeof(int) - 1))) & 1;
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.X, diff, lut.X, precomp[j].X);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Y, diff, lut.Y, precomp[j].Y);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Z, diff, lut.Z, precomp[j].Z);
        }
        /* negate lut point if digit is negative */
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(out->Y, lut.Y);
        fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(lut.Y, is_neg,
                                                              lut.Y, out->Y);
        point_add_proj(&Q, &Q, &lut);
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fe_copy(lut.X, precomp[0].X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(lut.Y, precomp[0].Y);
    fe_copy(lut.Z, precomp[0].Z);
    point_add_proj(&lut, &lut, &Q);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, scalar[0] & 1,
                                                          lut.X, Q.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, scalar[0] & 1,
                                                          lut.Y, Q.Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, scalar[0] & 1,
                                                          lut.Z, Q.Z);

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Fixed scalar multiplication: comb with interleaving.
 */
static void fixed_smul_cmb(pt_aff_t *out, const unsigned char scalar[32]) {
    int i, j, k, d, diff, is_neg = 0;
    int8_t rnaf[52] = {0};
    pt_prj_t Q = {0}, R = {0};
    pt_aff_t lut = {0};

    scalar_rwnaf(rnaf, scalar);

    /* initalize accumulator to inf */
    fe_set_zero(Q.X);
    fe_copy(Q.Y, const_one);
    fe_set_zero(Q.Z);

#if defined(_MSC_VER)
    /* result still unsigned: yes we know */
#pragma warning(push)
#pragma warning(disable : 4146)
#endif

    for (i = 2; i >= 0; i--) {
        for (j = 0; i != 2 && j < RADIX; j++) point_double(&Q, &Q);
        for (j = 0; j < 19; j++) {
            if (j * 3 + i > 51) continue;
            d = rnaf[j * 3 + i];
            /* is_neg = (d < 0) ? 1 : 0 */
            is_neg = (d >> (8 * sizeof(int) - 1)) & 1;
            /* d = abs(d) */
            d = (d ^ -is_neg) + is_neg;
            d = (d - 1) >> 1;
            for (k = 0; k < DRADIX / 2; k++) {
                diff = (1 - (-(d ^ k) >> (8 * sizeof(int) - 1))) & 1;
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                    lut.X, diff, lut.X, lut_cmb[j][k].X);
                fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                    lut.Y, diff, lut.Y, lut_cmb[j][k].Y);
            }
            /* negate lut point if digit is negative */
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(out->Y, lut.Y);
            fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(
                lut.Y, is_neg, lut.Y, out->Y);
            point_add_mixed(&Q, &Q, &lut);
        }
    }

#if defined(_MSC_VER)
#pragma warning(pop)
#endif

    /* conditionally subtract P if the scalar was even */
    fe_copy(lut.X, lut_cmb[0][0].X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_opp(lut.Y, lut_cmb[0][0].Y);
    point_add_mixed(&R, &Q, &lut);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.X, scalar[0] & 1,
                                                          R.X, Q.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Y, scalar[0] & 1,
                                                          R.Y, Q.Y);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_selectznz(Q.Z, scalar[0] & 1,
                                                          R.Z, Q.Z);

    /* convert to affine -- NB depends on coordinate system */
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_inv(Q.Z, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->X, Q.X, Q.Z);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_carry_mul(out->Y, Q.Y, Q.Z);
}

/*-
 * Wrapper: simultaneous scalar mutiplication.
 * outx, outy := a * G + b * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul_two(unsigned char outx[32], unsigned char outy[32],
                          const unsigned char a[32], const unsigned char b[32],
                          const unsigned char inx[32],
                          const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.X, inx);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.Y, iny);
    /* simultaneous scalar multiplication */
    var_smul_wnaf_two(&P, a, b, &P);

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: fixed scalar mutiplication.
 * outx, outy := scalar * G
 * Everything is LE byte ordering.
 */
static void point_mul_g(unsigned char outx[32], unsigned char outy[32],
                        const unsigned char scalar[32]) {
    pt_aff_t P;

    /* fixed scmul function */
    fixed_smul_cmb(&P, scalar);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}

/*-
 * Wrapper: variable point scalar mutiplication.
 * outx, outy := scalar * P
 * where P = (inx, iny).
 * Everything is LE byte ordering.
 */
static void point_mul(unsigned char outx[32], unsigned char outy[32],
                      const unsigned char scalar[32],
                      const unsigned char inx[32],
                      const unsigned char iny[32]) {
    pt_aff_t P;

    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.X, inx);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_from_bytes(P.Y, iny);
    /* var scmul function */
    var_smul_rwnaf(&P, scalar, &P);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outx, P.X);
    fiat_id_GostR3410_2001_CryptoPro_A_ParamSet_to_bytes(outy, P.Y);
}


#include <openssl/ec.h>

/* the zero field element */
static const unsigned char const_zb[32] = {0};

/*-
 * An OpenSSL wrapper for simultaneous scalar multiplication.
 * r := n * G + m * q
 */
    int
    point_mul_two_id_GostR3410_2001_CryptoPro_A_ParamSet(
        const EC_GROUP *group, EC_POINT *r, const BIGNUM *n, const EC_POINT *q,
        const BIGNUM *m, BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(n, b_n, 32) != 32 || BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the simultaneous scalar multiplication */
    point_mul_two(b_x, b_y, b_n, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for variable point scalar multiplication.
 * r := m * q
 */
    int
    point_mul_id_GostR3410_2001_CryptoPro_A_ParamSet(const EC_GROUP *group,
                                                     EC_POINT *r,
                                                     const EC_POINT *q,
                                                     const BIGNUM *m,
                                                     BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_m[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL
        /* pull out coords as bytes */
        || !EC_POINT_get_affine_coordinates(group, q, x, y, ctx) ||
        BN_bn2lebinpad(x, b_x, 32) != 32 || BN_bn2lebinpad(y, b_y, 32) != 32 ||
        BN_bn2lebinpad(m, b_m, 32) != 32)
        goto err;
    /* do the variable scalar multiplication */
    point_mul(b_x, b_y, b_m, b_x, b_y);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}

/*-
 * An OpenSSL wrapper for fixed scalar multiplication.
 * r := n * G
 */
    int
    point_mul_g_id_GostR3410_2001_CryptoPro_A_ParamSet(const EC_GROUP *group,
                                                       EC_POINT *r,
                                                       const BIGNUM *n,
                                                       BN_CTX *ctx) {
    int ret = 0;
    unsigned char b_x[32];
    unsigned char b_y[32];
    unsigned char b_n[32];
    BIGNUM *x = NULL, *y = NULL;

    BN_CTX_start(ctx);
    x = BN_CTX_get(ctx);
    if ((y = BN_CTX_get(ctx)) == NULL || BN_bn2lebinpad(n, b_n, 32) != 32)
        goto err;
    /* do the fixed scalar multiplication */
    point_mul_g(b_x, b_y, b_n);
    /* check for infinity */
    if (CRYPTO_memcmp(const_zb, b_x, 32) == 0 &&
        CRYPTO_memcmp(const_zb, b_y, 32) == 0) {
        if (!EC_POINT_set_to_infinity(group, r)) goto err;
    } else {
        /* otherwise, pack the bytes into the result */
        if (BN_lebin2bn(b_x, 32, x) == NULL ||
            BN_lebin2bn(b_y, 32, y) == NULL ||
            !EC_POINT_set_affine_coordinates(group, r, x, y, ctx))
            goto err;
    }
    ret = 1;
err:
    BN_CTX_end(ctx);
    return ret;
}



#endif /* __SIZEOF_INT128__ */
