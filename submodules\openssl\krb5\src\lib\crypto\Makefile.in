mydir=lib$(S)crypto
BUILDTOP=$(REL)..$(S)..
SUBDIRS= krb $(CRYPTO_IMPL) crypto_tests

LIBBASE=k5crypto
LIBMAJOR=3
LIBMINOR=1
LIBINITFUNC=cryptoint_initialize_library
LIBFINIFUNC=cryptoint_cleanup_library
RELDIR=crypto

STOBJLISTS=$(CRYPTO_IMPL)/enc_provider/OBJS.ST				\
	$(CRYPTO_IMPL)/hash_provider/OBJS.ST				\
	$(CRYPTO_IMPL)/md4/OBJS.ST $(CRYPTO_IMPL)/md5/OBJS.ST		\
	$(CRYPTO_IMPL)/sha1/OBJS.ST $(CRYPTO_IMPL)/sha2/OBJS.ST		\
	$(CRYPTO_IMPL)/aes/OBJS.ST $(CRYPTO_IMPL)/des/OBJS.ST		\
	$(CRYPTO_IMPL)/camellia/OBJS.ST krb/OBJS.ST			\
	$(CRYPTO_IMPL)/OBJS.ST

SUBDIROBJLISTS=$(CRYPTO_IMPL)/enc_provider/OBJS.ST			\
	$(CRYPTO_IMPL)/hash_provider/OBJS.ST				\
	$(CRYPTO_IMPL)/md4/OBJS.ST $(CRYPTO_IMPL)/md5/OBJS.ST		\
	$(CRYPTO_IMPL)/sha1/OBJS.ST $(CRYPTO_IMPL)/sha2/OBJS.ST		\
	$(CRYPTO_IMPL)/aes/OBJS.ST $(CRYPTO_IMPL)/des/OBJS.ST		\
	$(CRYPTO_IMPL)/camellia/OBJS.ST krb/OBJS.ST			\
	$(CRYPTO_IMPL)/OBJS.ST

# No dependencies.  Record places to find this shared object if the target
# link editor and loader support it.
DEPLIBS=
CRYPTO_IMPL_LIBS=@CRYPTO_IMPL_LIBS@
SHLIB_EXPLIBS= $(SUPPORT_LIB) $(CRYPTO_IMPL_LIBS) $(LIBS)
SHLIB_EXPDEPLIBS= $(SUPPORT_DEPLIB)
SHLIB_LDFLAGS= $(LDFLAGS) @SHLIB_RPATH_DIRS@

##DOS##LIBNAME=$(OUTPRE)crypto.lib
##DOS##OBJFILEDEP=$(OUTPRE)krb.lst $(OUTPRE)aes.lst $(OUTPRE)enc_provider.lst $(OUTPRE)des.lst $(OUTPRE)md5.lst $(OUTPRE)camellia.lst $(OUTPRE)md4.lst $(OUTPRE)hash_provider.lst $(OUTPRE)sha2.lst $(OUTPRE)sha1.lst $(OUTPRE)builtin.lst
##DOS##OBJFILELIST=@$(OUTPRE)krb.lst @$(OUTPRE)aes.lst @$(OUTPRE)enc_provider.lst @$(OUTPRE)des.lst @$(OUTPRE)md5.lst @$(OUTPRE)camellia.lst @$(OUTPRE)md4.lst @$(OUTPRE)hash_provider.lst @$(OUTPRE)sha2.lst @$(OUTPRE)sha1.lst @$(OUTPRE)builtin.lst

all-unix: all-liblinks
install-unix: install-libs


# all-unix:
# install-unix:

libcrypto.lib:
	libdir crypto.lib

clean-unix:: clean-liblinks clean-libs clean-libobjs

@lib_frag@
@libobj_frag@

