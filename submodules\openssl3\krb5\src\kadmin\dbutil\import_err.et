error_table imp
error_code IMPORT_BAD_FILE,	    "Input not recognized as database dump"
error_code IMPORT_BAD_TOKEN,	    "Bad token in dump file."
error_code IMPORT_BAD_VERSION,	    "Bad version in dump file"
error_code IMPORT_BAD_RECORD,	    "Defective record encountered: "
error_code IMPORT_BAD_FOOTER,	    "Truncated input file detected."
error_code IMPORT_FAILED,	    "Import of dump failed"
error_code IMPORT_MISMATCH_COUNT,   "Number of records imported does not match count"
error_code IMPORT_UNK_OPTION,	    "Unknown command line option.\nUsage: ovsec_adm_import [filename]"
error_code IMPORT_WARN_DB,	    "Warning -- continuing to import will overwrite existing databases!"
error_code IMPORT_RENAME_FAILED,    "Database rename Failed!!"
error_code IMPORT_EXTRA_DATA,	    "Extra data after footer is ignored."
error_code IMPORT_CONFIRM,	    "Proceed <y|n>?"
error_code IMPORT_O<PERSON>EN_DUMP,	    "while opening input file"
error_code IMPORT_IMPORT,	    "while importing databases"
error_code IMPORT_TTY,		    "cannot open /dev/tty!!"
error_code IMPORT_RENAME_OPEN,	    "while opening databases"
error_code IMPORT_RENAME_LOCK,	    "while acquiring permanent lock"
error_code IMPORT_RENAME_UNLOCK,    "while releasing permanent lock"
error_code IMPORT_RENAME_CLOSE,	    "while closing databases"
error_code IMPORT_GET_PARAMS,	    "while retrieving configuration parameters"
end
