# `src/core/worker.c` 文档

## 一、概述
此模块主要负责协调操作的处理工作。对于给定连接的操作，由单个线程进行处理，并且连接会分布在一组可用处理器上，以实现工作的平衡。“worker” 维护着连接队列（每个连接都有一个待处理操作的队列）、无状态操作队列以及一个包含所有分配给该 worker 且有活动定时器运行的连接的定时器轮。每个连接都会被分配给一个 worker，并且在有操作需要处理时会被加入队列。

## 二、主要数据结构

### `QUIC_WORKER`
```c
typedef struct QUIC_CACHEALIGN QUIC_WORKER {
    CXPLAT_EXECUTION_CONTEXT ExecutionContext;
    QUIC_PARTITION* Partition;
    CXPLAT_EVENT Done;
    BOOLEAN IsExternal;
    BOOLEAN Enabled;
    BOOLEAN IsActive;
    uint32_t AverageQueueDelay;
    QUIC_TIMER_WHEEL TimerWheel;
    CXPLAT_EVENT Ready;
    CXPLAT_THREAD Thread;
    CXPLAT_DISPATCH_LOCK Lock;
    CXPLAT_LIST_ENTRY Connections;
    CXPLAT_LIST_ENTRY** PriorityConnectionsTail;
    CXPLAT_LIST_ENTRY Operations;
    uint32_t OperationCount;
    uint64_t DroppedOperationCount;
} QUIC_WORKER;
```
- **`ExecutionContext`**：用于执行回调和状态管理的上下文。
- **`Partition`**：该 worker 所关联的分区。
- **`Done`**：用于在执行上下文（即 worker 线程）完成时发出信号的事件。
- **`IsExternal`**：指示此工作是否由 QUIC 外部的执行上下文处理。
- **`Enabled`**：指示 worker 当前是否正在运行。
- **`IsActive`**：指示 worker 当前是否正在处理连接。
- **`AverageQueueDelay`**：连接经历的平均队列延迟，以微秒为单位。
- **`TimerWheel`**：worker 连接的定时器轮。
- **`Ready`**：用于唤醒线程的事件。
- **`Thread`**：用于从排队连接中排出操作的线程。
- **`Lock`**：用于序列化对连接和操作列表的访问。
- **`Connections`**：有待处理操作的连接队列。
- **`PriorityConnectionsTail`**：优先级连接队列的尾部指针。
- **`Operations`**：有待处理的无状态操作队列。
- **`OperationCount`**：操作队列中的操作数量。
- **`DroppedOperationCount`**：丢弃的操作数量。

### `QUIC_WORKER_POOL`
```c
typedef struct QUIC_WORKER_POOL {
    uint16_t WorkerCount;
    uint16_t LastWorker;
    _Field_size_(WorkerCount) QUIC_WORKER Workers[0];
} QUIC_WORKER_POOL;
```
- **`WorkerCount`**：池中 worker 的数量。
- **`LastWorker`**：上次负载最轻的 worker。
- **`Workers`**：所有的 worker。

## 三、主要函数

### `QuicWorkerThreadWake`
```c
void QuicWorkerThreadWake(_In_ QUIC_WORKER* Worker);
```
- **功能**：唤醒 worker 线程。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：
  - 将 `Worker->ExecutionContext.Ready` 设置为 `TRUE`。
  - 如果 `Worker->IsExternal` 为 `TRUE`，则调用 `CxPlatWakeExecutionContext` 唤醒执行上下文；否则，调用 `CxPlatEventSet` 设置 `Ready` 事件。

### `QuicWorkerInitialize`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS QuicWorkerInitialize(
    _In_ const QUIC_REGISTRATION* Registration,
    _In_ QUIC_EXECUTION_PROFILE ExecProfile,
    _In_ QUIC_PARTITION* Partition,
    _Inout_ QUIC_WORKER* Worker
);
```
- **功能**：初始化 worker。
- **参数**：
  - `Registration`：指向 `QUIC_REGISTRATION` 结构体的指针。
  - `ExecProfile`：执行配置文件。
  - `Partition`：指向 `QUIC_PARTITION` 结构体的指针。
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：
  - 初始化 `Worker` 的各项成员，包括锁、事件、列表和定时器轮。
  - 根据执行配置文件设置 `Worker` 的属性，并创建线程或添加到执行上下文池。
  - 如果初始化失败，会清理已初始化的部分并返回错误状态。

### `QuicWorkerUninitialize`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicWorkerUninitialize(_In_ QUIC_WORKER* Worker);
```
- **功能**：清理 worker。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：
  - 禁用 `Worker`，唤醒执行上下文并等待完成。
  - 清理事件、线程、锁和定时器轮。

### `QuicWorkerAssignConnection`
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicWorkerAssignConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection
);
```
- **功能**：将连接分配给 worker。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `Connection`：指向 `QUIC_CONNECTION` 结构体的指针。
- **实现细节**：
  - 将 `Connection` 的 `Worker` 和 `Partition` 字段设置为 `Worker` 和 `Worker->Partition`。
  - 记录连接分配的跟踪事件。

### `QuicWorkerIsIdle`
```c
BOOLEAN QuicWorkerIsIdle(_In_ const QUIC_WORKER* Worker);
```
- **功能**：检查 worker 是否空闲。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：
  - 检查 `Worker` 的连接列表和操作列表是否为空。

### `QuicWorkerQueueConnection`
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicWorkerQueueConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection
);
```
- **功能**：将连接加入 worker 的队列，并在必要时唤醒 worker 线程。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `Connection`：指向 `QUIC_CONNECTION` 结构体的指针。
- **实现细节**：
  - 检查 `Connection` 是否已经在处理或排队。
  - 如果没有，则将其加入连接队列，记录调度状态，增加引用计数，并在必要时唤醒 worker 线程。
  - 增加性能计数器。

### `QuicWorkerQueuePriorityConnection`
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicWorkerQueuePriorityConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection
);
```
- **功能**：将优先级连接加入 worker 的队列，并在必要时唤醒 worker 线程。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `Connection`：指向 `QUIC_CONNECTION` 结构体的指针。
- **实现细节**：
  - 检查 `Connection` 是否已经在处理或有优先级工作。
  - 如果没有，则将其加入优先级连接队列，记录调度状态，增加引用计数，并在必要时唤醒 worker 线程。
  - 增加性能计数器。

### `QuicWorkerMoveConnection`
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicWorkerMoveConnection(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_CONNECTION* Connection,
    _In_ BOOLEAN IsPriority
);
```
- **功能**：将连接移动到 worker 的队列中，并在必要时唤醒 worker 线程。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `Connection`：指向 `QUIC_CONNECTION` 结构体的指针。
  - `IsPriority`：是否为优先级连接。
- **实现细节**：
  - 根据 `IsPriority` 将 `Connection` 插入到相应的队列中。
  - 记录调度状态，增加引用计数，并在必要时唤醒 worker 线程。

### `QuicWorkerQueueOperation`
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicWorkerQueueOperation(
    _In_ QUIC_WORKER* Worker,
    _In_ QUIC_OPERATION* Operation
);
```
- **功能**：将操作加入 worker 的队列，并在必要时唤醒 worker 线程。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `Operation`：指向 `QUIC_OPERATION` 结构体的指针。
- **实现细节**：
  - 检查操作数量是否超过限制，以及是否可以增加绑定引用计数。
  - 如果可以，则将操作加入操作队列，增加操作计数和性能计数器，并在必要时唤醒 worker 线程。
  - 如果超过限制，则丢弃操作并记录日志。

### `QuicWorkerUpdateQueueDelay`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicWorkerUpdateQueueDelay(
    _In_ QUIC_WORKER* Worker,
    _In_ uint32_t TimeInQueueUs
);
```
- **功能**：更新 worker 的平均队列延迟。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
  - `TimeInQueueUs`：连接在队列中的时间（微秒）。
- **实现细节**：
  - 使用指数加权平均法更新 `Worker->AverageQueueDelay`。
  - 记录队列延迟更新的跟踪事件。

### `QuicWorkerResetQueueDelay`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicWorkerResetQueueDelay(_In_ QUIC_WORKER* Worker);
```
- **功能**：重置 worker 的平均队列延迟。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：
  - 将 `Worker->AverageQueueDelay` 设置为 0。
  - 记录队列延迟更新的跟踪事件。

### `QuicWorkerGetNextConnection`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_CONNECTION* QuicWorkerGetNextConnection(_In_ QUIC_WORKER* Worker);
```
- **功能**：获取 worker 的下一个连接。
- **参数**：
  - `Worker`：指向 `QUIC_WORKER` 结构体的指针。
- **实现细节**：该函数在提供的代码中未给出完整实现。

## 四、worker 的工作流程
文件实现了 QUIC 工作线程的核心功能，包括 worker 的初始化、清理、连接和操作的排队与处理，以及队列延迟的管理。通过这些功能，确保了 QUIC 连接操作的高效处理和负载均衡。 work 通过捕获异步事件来驱动整个工作流程。  
每个connection 会通过QuicWorkerAssignConnection 来绑定到指定的work 上， 然后需要执行什么操作的时候，就通过QuicConnQueueOper 让work 来执行这个来执行这个opreate， work 在循环调用QuicWorkerLoop 的过程中会通过QuicConnDrainOperations要执行的operator， 并进行处理。  
work 在循环调用QuicWorkerLoop 的过程 会调用QuicTimerWheelUpdateConnection 更新connection 的定时器， 并通过调用QuicWorkerProcessTimers 查询定时器是否到达了有效时间，如果是， 则会调用QuicConnQueueOper 通知work 执行一个QUIC_OPER_TYPE_TIMER_EXPIRED 事件。再下一个QuicWorkerLoop 里 通过调用QuicWorkerLoop 来执行具体的定时器任务。 时序图如下：
![](msquic/msquic_work.png)