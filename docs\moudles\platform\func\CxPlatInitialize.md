- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatInitialize(
    void
    )
{
    QUIC_STATUS Status;
    BOOLEAN CryptoInitialized = FALSE;      // 标记是否成功初始化加密模块
    BOOLEAN ProcInfoInitialized = FALSE;    // 标记是否成功初始化处理器信息

    // 增加系统引用计数。如果引用计数大于 1，说明系统已经初始化，无需重复初始化。
    if (InterlockedIncrement16(&CxPlatRef) != 1) {
        return QUIC_STATUS_SUCCESS;
    }

    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);

    // 创建全局堆，用于内存分配。
    CxPlatform.Heap = HeapCreate(0, 0, 0);
    if (CxPlatform.Heap == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        goto Error;
    }

    // 获取操作系统版本信息。
    BOOLEAN SuccessfullySetVersion = FALSE;
    HMODULE NtDllHandle = LoadLibraryA("ntdll.dll");
    if (NtDllHandle) {
        FuncRtlGetVersion VersionFunc = (FuncRtlGetVersion)GetProcAddress(NtDllHandle, "RtlGetVersion");
        if (VersionFunc) {
            RTL_OSVERSIONINFOW VersionInfo = {0};
            VersionInfo.dwOSVersionInfoSize = sizeof(VersionInfo);
            if ((*VersionFunc)(&VersionInfo) == 0) {
                CxPlatform.dwBuildNumber = VersionInfo.dwBuildNumber;
                SuccessfullySetVersion = TRUE;
            }
        }
        FreeLibrary(NtDllHandle);
    }
    CXPLAT_DBG_ASSERT(SuccessfullySetVersion); // 确保成功获取操作系统版本。

    // 初始化处理器信息。
    if (QUIC_FAILED(Status = CxPlatProcessorInfoInit())) {
        QuicTraceEvent(
            LibraryError,
            "[ lib] ERROR, %s.",
            "CxPlatProcessorInfoInit failed");
        goto Error;
    }
    ProcInfoInitialized = TRUE;

    // 获取系统内存状态。
    if (!GlobalMemoryStatusEx(&memInfo)) {
        DWORD Error = GetLastError();
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Error,
            "GlobalMemoryStatusEx failed");
        Status = HRESULT_FROM_WIN32(Error);
        goto Error;
    }

    // 保存系统总内存大小。
    CxPlatTotalMemory = memInfo.ullTotalPageFile;

#ifdef TIMERR_NOERROR
    MMRESULT mmResult;

    // 获取系统定时器的分辨率。
    if ((mmResult = timeGetDevCaps(&CxPlatTimerCapabilities, sizeof(TIMECAPS))) != TIMERR_NOERROR) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            mmResult,
            "timeGetDevCaps failed");
        Status = HRESULT_FROM_WIN32(mmResult);
        goto Error;
    }

#ifdef QUIC_HIGH_RES_TIMERS
    // 设置高分辨率定时器。
    if ((mmResult = timeBeginPeriod(CxPlatTimerCapabilities.wPeriodMin)) != TIMERR_NOERROR) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            mmResult,
            "timeBeginPeriod failed");
        Status = HRESULT_FROM_WIN32(mmResult);
        goto Error;
    }
#endif // QUIC_HIGH_RES_TIMERS
#endif // TIMERR_NOERROR

    // 初始化加密模块。
    Status = CxPlatCryptInitialize();
    if (QUIC_FAILED(Status)) {
        goto Error;
    }
    CryptoInitialized = TRUE;

#ifdef TIMERR_NOERROR
    // 记录初始化日志，包括内存和定时器分辨率信息。
    QuicTraceLogInfo(
        WindowsUserInitialized2,
        "[ dll] Initialized (AvailMem = %llu bytes, TimerResolution = [%u, %u])",
        CxPlatTotalMemory,
        CxPlatTimerCapabilities.wPeriodMin,
        CxPlatTimerCapabilities.wPeriodMax);
#else // TIMERR_NOERROR
    // 记录初始化日志，仅包括内存信息。
    QuicTraceLogInfo(
        WindowsUserInitialized,
        "[ dll] Initialized (AvailMem = %llu bytes)",
        CxPlatTotalMemory);
#endif // TIMERR_NOERROR

Error:

    // 如果初始化失败，清理已分配的资源。
    if (QUIC_FAILED(Status)) {
        if (CryptoInitialized) {
            CxPlatCryptUninitialize();
        }
        if (ProcInfoInitialized) {
            CxPlatProcessorInfoUnInit();
        }
        if (CxPlatform.Heap) {
            HeapDestroy(CxPlatform.Heap);
            CxPlatform.Heap = NULL;
        }
    }

    return Status;
}
```
```mermaid
graph TD
    A([开始]):::startend --> B{引用计数是否为 1?}:::decision
    B -- 是 --> C(创建堆):::process
    B -- 否 --> Z([返回成功状态]):::startend
    C --> D{堆创建成功?}:::decision
    D -- 否 --> Y([返回内存不足错误]):::startend
    D -- 是 --> E(加载 ntdll.dll 获取系统版本):::process
    E --> F{获取版本成功?}:::decision
    F -- 否 --> Y
    F -- 是 --> G(初始化处理器信息<br/>调用 CxPlatProcessorInfoInit):::process
    G --> H{初始化成功?}:::decision
    H -- 否 --> Y
    H -- 是 --> I(获取内存信息<br/>调用 GlobalMemoryStatusEx):::process
    I --> J{获取成功?}:::decision
    J -- 否 --> Y
    J -- 是 --> K(设置总内存):::process
    K --> L{定义 TIMERR_NOERROR?}:::decision
    L -- 是 --> M(获取定时器能力<br/>调用 timeGetDevCaps):::process
    L -- 否 --> N(跳过定时器操作):::process
    M --> O{获取成功?}:::decision
    O -- 否 --> Y
    O -- 是 --> P{定义 QUIC_HIGH_RES_TIMERS?}:::decision
    P -- 是 --> Q(设置定时器周期<br/>调用 timeBeginPeriod):::process
    P -- 否 --> N
    Q --> R{设置成功?}:::decision
    R -- 否 --> Y
    R -- 是 --> N
    N --> S(初始化加密模块<br/>调用 CxPlatCryptInitialize):::process
    S --> T{初始化成功?}:::decision
    T -- 否 --> Y
    T -- 是 --> U(记录初始化日志):::process
    U --> Z
    Y --> V(反初始化加密模块<br/>调用 CxPlatCryptUninitialize):::process
    V --> W(反初始化处理器信息<br/>调用 CxPlatProcessorInfoUnInit):::process
    W --> X(销毁堆<br/>调用 HeapDestroy):::process
    X --> Z
```

---


- Windows kernel
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatInitialize(
    void
    )
{
    SYSTEM_BASIC_INFORMATION Sbi; // 用于存储系统基本信息

    PAGED_CODE(); // 确保此函数只能在被动 IRQL（PASSIVE_LEVEL）下调用

    // 获取当前系统的处理器数量
    CxPlatProcessorCount =
        (uint32_t)KeQueryActiveProcessorCountEx(ALL_PROCESSOR_GROUPS);

    // 获取操作系统版本信息
    RTL_OSVERSIONINFOW osInfo;
    RtlZeroMemory(&osInfo, sizeof(osInfo));
    osInfo.dwOSVersionInfoSize = sizeof(osInfo);
    NTSTATUS status = RtlGetVersion(&osInfo);
    if (NT_SUCCESS(status)) {
        DWORD BuildNumber = osInfo.dwBuildNumber; // 获取系统的构建版本号
        CxPlatform.dwBuildNumber = BuildNumber;  // 保存到全局变量
    } else {
        CXPLAT_DBG_ASSERT(FALSE); // 如果获取失败，触发断言
    }

    // 打开随机数生成算法提供程序
    QUIC_STATUS Status =
        BCryptOpenAlgorithmProvider(
            &CxPlatform.RngAlgorithm,  // 输出随机数生成算法句柄
            BCRYPT_RNG_ALGORITHM,      // 使用随机数生成算法
            NULL,                      // 提供程序名称（可选）
            BCRYPT_PROV_DISPATCH);     // 使用分发模式
    if (QUIC_FAILED(Status)) {
        // 如果打开失败，记录错误并退出
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "BCryptOpenAlgorithmProvider (RNG)");
        goto Error;
    }
    CXPLAT_DBG_ASSERT(CxPlatform.RngAlgorithm != NULL); // 确保算法句柄不为空

    // 查询系统基本信息
    Status =
        ZwQuerySystemInformation(
            SystemBasicInformation, // 查询类型
            &Sbi,                   // 输出系统基本信息
            sizeof(Sbi),            // 输出缓冲区大小
            NULL);                  // 返回长度（可选）
    if (QUIC_FAILED(Status)) {
        // 如果查询失败，记录错误并退出
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "ZwQuerySystemInformation(SystemBasicInformation)");
        goto Error;
    }

    // 初始化加密模块
    Status = CxPlatCryptInitialize();
    if (QUIC_FAILED(Status)) {
        // 如果初始化失败，记录错误并退出
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "CxPlatCryptInitialize");
        goto Error;
    }

    // 计算系统的总内存大小
    // 注意：此值可能会因热插拔内存而增加，需要动态更新
    CxPlatTotalMemory = (uint64_t)Sbi.NumberOfPhysicalPages * (uint64_t)Sbi.PageSize;

    // 记录初始化日志，包括页面大小和可用内存
    QuicTraceLogInfo(
        WindowsKernelInitialized,
        "[ sys] Initialized (PageSize = %u bytes; AvailMem = %llu bytes)",
        Sbi.PageSize,
        CxPlatTotalMemory);

Error:

    // 如果初始化失败，清理已分配的资源
    if (QUIC_FAILED(Status)) {
        if (CxPlatform.RngAlgorithm != NULL) {
            BCryptCloseAlgorithmProvider(CxPlatform.RngAlgorithm, 0); // 关闭随机数生成算法提供程序
            CxPlatform.RngAlgorithm = NULL;
        }
    }

    return Status; // 返回初始化状态
}
```

```mermaid
graph TD
    A([开始]) --> B(PAGED_CODE)
    B --> C(KeQueryActiveProcessorCountEx 获取所有处理器组的活动处理器数量)
    C --> D(初始化 RTL_OSVERSIONINFOW 结构体)
    D --> E(RtlGetVersion 获取操作系统版本)
    E --> F{NT_SUCCESS status?}
    F -- 是 --> G(设置 CxPlatform.dwBuildNumber)
    F -- 否 --> H(CXPLAT_DBG_ASSERT)
    H --> O(错误处理)
    G --> I(BCryptOpenAlgorithmProvider 打开 RNG 算法提供者)
    I --> J{QUIC_FAILED Status?}
    J -- 是 --> K(记录 BCryptOpenAlgorithmProvider 错误日志)
    K --> O
    J -- 否 --> L(ZwQuerySystemInformation 查询系统基本信息)
    L --> M{QUIC_FAILED Status?}
    M -- 是 --> N(记录 ZwQuerySystemInformation 错误日志)
    N --> O
    M -- 否 --> P(CxPlatCryptInitialize 初始化加密模块)
    P --> Q{QUIC_FAILED Status?}
    Q -- 是 --> R(记录 CxPlatCryptInitialize 错误日志)
    R --> O
    Q -- 否 --> S(计算 CxPlatTotalMemory)
    S --> T(QuicTraceLogInfo 记录初始化信息)
    T --> U(返回成功状态)
    U --> V([结束])
    O --> W{是否需要关闭 RNG 算法提供者?}
    W -- 是 --> X(BCryptCloseAlgorithmProvider 关闭 RNG 算法提供者)
    X --> Y(返回错误状态)
    Y --> V
    W -- 否 --> Y
```

---

- posix
```c
QUIC_STATUS
CxPlatInitialize(
    void
    )
{
    QUIC_STATUS Status;

    // 增加系统引用计数。如果引用计数大于 1，说明系统已经初始化，无需重复初始化。
    if (InterlockedIncrement16(&CxPlatRef) != 1) {
        return QUIC_STATUS_SUCCESS;
    }

    // 打开随机数设备文件 "/dev/urandom"。
    RandomFd = open("/dev/urandom", O_RDONLY | O_CLOEXEC);
    if (RandomFd == -1) {
        // 如果打开失败，记录错误并返回。
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            errno,
            "open(/dev/urandom, O_RDONLY|O_CLOEXEC) failed");
        return (QUIC_STATUS)errno;
    }

    // 初始化加密模块。
    Status = CxPlatCryptInitialize();
    if (QUIC_FAILED(Status)) {
        // 如果初始化失败，关闭随机数设备文件并返回错误。
        if (RandomFd != -1) {
            close(RandomFd);
        }
        return Status;
    }

    // 获取系统的总内存大小。
    CxPlatTotalMemory = CGroupGetMemoryLimit();

    // 记录初始化日志，包括可用内存大小。
    QuicTraceLogInfo(
        PosixInitialized,
        "[ dso] Initialized (AvailMem = %llu bytes)",
        CxPlatTotalMemory);

    return QUIC_STATUS_SUCCESS;
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{引用计数是否为 1?}:::decision
    B -- 是 --> C(打开 /dev/urandom):::process
    B -- 否 --> Z([返回成功状态]):::startend
    C --> D{打开成功?}:::decision
    D -- 否 --> Y([返回错误状态]):::startend
    D -- 是 --> E(初始化加密模块<br/>调用 CxPlatCryptInitialize):::process
    E --> F{初始化成功?}:::decision
    F -- 否 --> G(关闭 /dev/urandom):::process
    G --> Y
    F -- 是 --> H(获取内存限制<br/>调用 CGroupGetMemoryLimit):::process
    H --> I(记录初始化日志):::process
    I --> Z
```