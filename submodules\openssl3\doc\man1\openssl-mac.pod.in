=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-mac - perform Message Authentication Code operations

=head1 SYNOPSIS

B<openssl mac>
[B<-help>]
[B<-cipher>]
[B<-digest>]
[B<-macopt>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-binary>]
{- $OpenSSL::safe::opt_provider_synopsis -}
I<mac_name>

=head1 DESCRIPTION

The message authentication code functions output the MAC of a supplied input
file.

=head1 OPTIONS

=over 4

=item B<-help>

Print a usage message.

=item B<-in> I<filename>

Input filename to calculate a MAC for, or standard input by default.
Standard input is used if the filename is '-'.
Files and standard input are expected to be in binary format.

=item B<-out> I<filename>

Filename to output to, or standard output by default.

=item B<-binary>

Output the MAC in binary form. Uses hexadecimal text format if not specified.

=item B<-cipher> I<name>

Used by CMAC and GMAC to specify the cipher algorithm.
For CMAC it must be one of AES-128-CBC, AES-192-CBC, AES-256-CBC or
DES-EDE3-CBC.
For GMAC it should be a GCM mode cipher e.g. AES-128-GCM.

=item B<-digest> I<name>

Used by HMAC as an alphanumeric string (use if the key contains printable
characters only).
The string length must conform to any restrictions of the MAC algorithm.
To see the list of supported digests, use C<openssl list -digest-commands>.

=item B<-macopt> I<nm>:I<v>

Passes options to the MAC algorithm.
A comprehensive list of controls can be found in the EVP_MAC implementation
documentation.
Common parameter names used by EVP_MAC_CTX_get_params() are:

=over 4

=item B<key:>I<string>

Specifies the MAC key as an alphanumeric string (use if the key contains
printable characters only).
The string length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.

=item B<hexkey:>I<string>

Specifies the MAC key in hexadecimal form (two hex digits per byte).
The key length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.

=item B<iv:>I<string>

Used by GMAC to specify an IV as an alphanumeric string (use if the IV contains
printable characters only).

=item B<hexiv:>I<string>

Used by GMAC to specify an IV in hexadecimal form (two hex digits per byte).

=item B<size:>I<int>

Used by KMAC128 or KMAC256 to specify an output length.
The default sizes are 32 or 64 bytes respectively.

=item B<custom:>I<string>

Used by KMAC128 or KMAC256 to specify a customization string.
The default is the empty string "".

=item B<digest:>I<string>

This option is identical to the B<-digest> option.

=item B<cipher:>I<string>

This option is identical to the B<-cipher> option.

=back

{- $OpenSSL::safe::opt_provider_item -}

=item I<mac_name>

Specifies the name of a supported MAC algorithm which will be used.
To see the list of supported MAC's use the command C<openssl list
-mac-algorithms>.

=back


=head1 EXAMPLES

To create a hex-encoded HMAC-SHA1 MAC of a file and write to stdout: \
 openssl mac -digest SHA1 \
         -macopt hexkey:000102030405060708090A0B0C0D0E0F10111213 \
         -in msg.bin HMAC

To create a SipHash MAC from a file with a binary file output: \
 openssl mac -macopt hexkey:000102030405060708090A0B0C0D0E0F \
         -in msg.bin -out out.bin -binary SipHash

To create a hex-encoded CMAC-AES-128-CBC MAC from a file:\
 openssl mac -cipher AES-128-CBC \
         -macopt hexkey:77A77FAF290C1FA30C683DF16BA7A77B \
         -in msg.bin CMAC

To create a hex-encoded KMAC128 MAC from a file with a Customisation String
'Tag' and output length of 16: \
 openssl mac -macopt custom:Tag -macopt hexkey:40414243444546 \
         -macopt size:16 -in msg.bin KMAC128

To create a hex-encoded GMAC-AES-128-GCM with a IV from a file: \
 openssl mac -cipher AES-128-GCM -macopt hexiv:E0E00F19FED7BA0136A797F3 \
         -macopt hexkey:77A77FAF290C1FA30C683DF16BA7A77B -in msg.bin GMAC

=head1 NOTES

The MAC mechanisms that are available will depend on the options
used when building OpenSSL.
Use C<openssl list -mac-algorithms> to list them.

=head1 SEE ALSO

L<openssl(1)>,
L<EVP_MAC(3)>,
L<EVP_MAC-CMAC(7)>,
L<EVP_MAC-GMAC(7)>,
L<EVP_MAC-HMAC(7)>,
L<EVP_MAC-KMAC(7)>,
L<EVP_MAC-Siphash(7)>,
L<EVP_MAC-Poly1305(7)>

=head1 COPYRIGHT

Copyright 2018-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
