=pod

=head1 NAME

EVP_MAC-Siphash - The Siphash EVP_MAC implementation

=head1 DESCRIPTION

Support for computing Siphash MACs through the B<EVP_MAC> API.

=head2 Identity

This implementation is identified with this name and properties, to be
used with EVP_MAC_fetch():

=over 4

=item "SIPHASH", "provider=default"

=back


=head2 Supported parameters

The general description of these parameters can be found in
L<EVP_MAC(3)/PARAMETERS>.

All these parameters can be set with EVP_MAC_CTX_set_params().
Furthermore, the "size" parameter can be retrieved with
EVP_MAC_CTX_get_params(), or with EVP_MAC_CTX_get_mac_size().
The length of the "size" parameter should not exceed that of a B<size_t>.

=over 4

=item "key" (B<OSSL_MAC_PARAM_KEY>) <octet string>

Sets the MAC key.
Setting this parameter is identical to passing a I<key> to L<EVP_MAC_init(3)>.

=item "size" (B<OSSL_MAC_PARAM_SIZE>) <unsigned integer>

Sets the MAC size.

=item "c-rounds" (B<OSSL_MAC_PARAM_C_ROUNDS>) <unsigned integer>

Specifies the number of rounds per message block.  By default this is I<2>.

=item "d-rounds" (B<OSSL_MAC_PARAM_D_ROUNDS>) <unsigned integer>

Specifies the number of finalisation rounds.  By default this is I<4>.

=back

=head1 SEE ALSO

L<EVP_MAC_CTX_get_params(3)>, L<EVP_MAC_CTX_set_params(3)>,
L<EVP_MAC(3)/PARAMETERS>, L<OSSL_PARAM(3)>

=head1 COPYRIGHT

Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
