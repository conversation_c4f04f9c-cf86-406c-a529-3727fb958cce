=pod

=head1 NAME

ossl_cmp_ctx_set1_caPubs,
ossl_cmp_ctx_set0_validatedSrvCert,
ossl_cmp_ctx_set_status,
ossl_cmp_ctx_set0_statusString,
ossl_cmp_ctx_set_failInfoCode,
ossl_cmp_ctx_set0_newCert,
ossl_cmp_ctx_set1_extraCertsIn,
ossl_cmp_ctx_set1_recipNonce
- internal functions for managing the CMP client context datastructure

=head1 SYNOPSIS

 #include <openssl/cmp.h>

 int ossl_cmp_ctx_set1_caPubs(OSSL_CMP_CTX *ctx, STACK_OF(X509) *caPubs);
 int ossl_cmp_ctx_set0_validatedSrvCert(OSSL_CMP_CTX *ctx, X509 *cert);
 int ossl_cmp_ctx_set_status(OSSL_CMP_CTX *ctx, int status);
 int ossl_cmp_ctx_set0_statusString(OSSL_CMP_CTX *ctx,
                                    OSSL_CMP_PKIFREETEXT *text);
 int ossl_cmp_ctx_set_failInfoCode(OSSL_CMP_CTX *ctx, int fail_info);
 int ossl_cmp_ctx_set0_newCert(OSSL_CMP_CTX *ctx, X509 *cert);
 int ossl_cmp_ctx_set1_extraCertsIn(OSSL_CMP_CTX *ctx,
                                    STACK_OF(X509) *extraCertsIn);
 int ossl_cmp_ctx_set1_recipNonce(OSSL_CMP_CTX *ctx,
                                  const ASN1_OCTET_STRING *nonce);

=head1 DESCRIPTION

ossl_cmp_ctx_set1_caPubs() copies the given stack of CA certificates
to the caPubs field of the context.
The reference counts of those certificates handled successfully are increased.

ossl_cmp_ctx_set0_validatedSrvCert() sets the validatedSrvCert of the context,
which caches any already validated server cert, or NULL if not available.

ossl_cmp_ctx_set_status() sets the status field of the context.

ossl_cmp_ctx_set0_statusString() sets the statusString field of the context.

ossl_cmp_ctx_set_failInfoCode() sets the error code bits in the failInfoCode
field of the context based on the given OSSL_CMP_PKIFAILUREINFO structure.

ossl_cmp_ctx_set0_newCert() sets the given (newly enrolled) certificate
in the context.

ossl_cmp_ctx_set1_extraCertsIn() sets the extraCertsIn field of the context.
The reference counts of those certificates handled successfully are increased.

ossl_cmp_ctx_set1_recipNonce() sets the given recipient nonce in the context.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

All functions return 1 on success, 0 on error.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
