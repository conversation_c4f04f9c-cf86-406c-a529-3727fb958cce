# Generated with generate_ssl_tests.pl

num_tests = 64

test-0 = 0-version-negotiation
test-1 = 1-version-negotiation
test-2 = 2-version-negotiation
test-3 = 3-version-negotiation
test-4 = 4-version-negotiation
test-5 = 5-version-negotiation
test-6 = 6-version-negotiation
test-7 = 7-version-negotiation
test-8 = 8-version-negotiation
test-9 = 9-version-negotiation
test-10 = 10-version-negotiation
test-11 = 11-version-negotiation
test-12 = 12-version-negotiation
test-13 = 13-version-negotiation
test-14 = 14-version-negotiation
test-15 = 15-version-negotiation
test-16 = 16-version-negotiation
test-17 = 17-version-negotiation
test-18 = 18-version-negotiation
test-19 = 19-version-negotiation
test-20 = 20-version-negotiation
test-21 = 21-version-negotiation
test-22 = 22-version-negotiation
test-23 = 23-version-negotiation
test-24 = 24-version-negotiation
test-25 = 25-version-negotiation
test-26 = 26-version-negotiation
test-27 = 27-version-negotiation
test-28 = 28-version-negotiation
test-29 = 29-version-negotiation
test-30 = 30-version-negotiation
test-31 = 31-version-negotiation
test-32 = 32-version-negotiation
test-33 = 33-version-negotiation
test-34 = 34-version-negotiation
test-35 = 35-version-negotiation
test-36 = 36-version-negotiation
test-37 = 37-version-negotiation
test-38 = 38-version-negotiation
test-39 = 39-version-negotiation
test-40 = 40-version-negotiation
test-41 = 41-version-negotiation
test-42 = 42-version-negotiation
test-43 = 43-version-negotiation
test-44 = 44-version-negotiation
test-45 = 45-version-negotiation
test-46 = 46-version-negotiation
test-47 = 47-version-negotiation
test-48 = 48-version-negotiation
test-49 = 49-version-negotiation
test-50 = 50-version-negotiation
test-51 = 51-version-negotiation
test-52 = 52-version-negotiation
test-53 = 53-version-negotiation
test-54 = 54-version-negotiation
test-55 = 55-version-negotiation
test-56 = 56-version-negotiation
test-57 = 57-version-negotiation
test-58 = 58-version-negotiation
test-59 = 59-version-negotiation
test-60 = 60-version-negotiation
test-61 = 61-version-negotiation
test-62 = 62-version-negotiation
test-63 = 63-version-negotiation
# ===========================================================

[0-version-negotiation]
ssl_conf = 0-version-negotiation-ssl

[0-version-negotiation-ssl]
server = 0-version-negotiation-server
client = 0-version-negotiation-client

[0-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[1-version-negotiation]
ssl_conf = 1-version-negotiation-ssl

[1-version-negotiation-ssl]
server = 1-version-negotiation-server
client = 1-version-negotiation-client

[1-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[2-version-negotiation]
ssl_conf = 2-version-negotiation-ssl

[2-version-negotiation-ssl]
server = 2-version-negotiation-server
client = 2-version-negotiation-client

[2-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[3-version-negotiation]
ssl_conf = 3-version-negotiation-ssl

[3-version-negotiation-ssl]
server = 3-version-negotiation-server
client = 3-version-negotiation-client

[3-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[4-version-negotiation]
ssl_conf = 4-version-negotiation-ssl

[4-version-negotiation-ssl]
server = 4-version-negotiation-server
client = 4-version-negotiation-client

[4-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[5-version-negotiation]
ssl_conf = 5-version-negotiation-ssl

[5-version-negotiation-ssl]
server = 5-version-negotiation-server
client = 5-version-negotiation-client

[5-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[6-version-negotiation]
ssl_conf = 6-version-negotiation-ssl

[6-version-negotiation-ssl]
server = 6-version-negotiation-server
client = 6-version-negotiation-client

[6-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = ServerFail
Method = DTLS


# ===========================================================

[7-version-negotiation]
ssl_conf = 7-version-negotiation-ssl

[7-version-negotiation-ssl]
server = 7-version-negotiation-server
client = 7-version-negotiation-client

[7-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = ServerFail
Method = DTLS


# ===========================================================

[8-version-negotiation]
ssl_conf = 8-version-negotiation-ssl

[8-version-negotiation-ssl]
server = 8-version-negotiation-server
client = 8-version-negotiation-client

[8-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[9-version-negotiation]
ssl_conf = 9-version-negotiation-ssl

[9-version-negotiation-ssl]
server = 9-version-negotiation-server
client = 9-version-negotiation-client

[9-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[10-version-negotiation]
ssl_conf = 10-version-negotiation-ssl

[10-version-negotiation-ssl]
server = 10-version-negotiation-server
client = 10-version-negotiation-client

[10-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[11-version-negotiation]
ssl_conf = 11-version-negotiation-ssl

[11-version-negotiation-ssl]
server = 11-version-negotiation-server
client = 11-version-negotiation-client

[11-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[12-version-negotiation]
ssl_conf = 12-version-negotiation-ssl

[12-version-negotiation-ssl]
server = 12-version-negotiation-server
client = 12-version-negotiation-client

[12-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[13-version-negotiation]
ssl_conf = 13-version-negotiation-ssl

[13-version-negotiation-ssl]
server = 13-version-negotiation-server
client = 13-version-negotiation-client

[13-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[14-version-negotiation]
ssl_conf = 14-version-negotiation-ssl

[14-version-negotiation-ssl]
server = 14-version-negotiation-server
client = 14-version-negotiation-client

[14-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[15-version-negotiation]
ssl_conf = 15-version-negotiation-ssl

[15-version-negotiation-ssl]
server = 15-version-negotiation-server
client = 15-version-negotiation-client

[15-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[16-version-negotiation]
ssl_conf = 16-version-negotiation-ssl

[16-version-negotiation-ssl]
server = 16-version-negotiation-server
client = 16-version-negotiation-client

[16-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[17-version-negotiation]
ssl_conf = 17-version-negotiation-ssl

[17-version-negotiation-ssl]
server = 17-version-negotiation-server
client = 17-version-negotiation-client

[17-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[18-version-negotiation]
ssl_conf = 18-version-negotiation-ssl

[18-version-negotiation-ssl]
server = 18-version-negotiation-server
client = 18-version-negotiation-client

[18-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[19-version-negotiation]
ssl_conf = 19-version-negotiation-ssl

[19-version-negotiation-ssl]
server = 19-version-negotiation-server
client = 19-version-negotiation-client

[19-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[20-version-negotiation]
ssl_conf = 20-version-negotiation-ssl

[20-version-negotiation-ssl]
server = 20-version-negotiation-server
client = 20-version-negotiation-client

[20-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[21-version-negotiation]
ssl_conf = 21-version-negotiation-ssl

[21-version-negotiation-ssl]
server = 21-version-negotiation-server
client = 21-version-negotiation-client

[21-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[22-version-negotiation]
ssl_conf = 22-version-negotiation-ssl

[22-version-negotiation-ssl]
server = 22-version-negotiation-server
client = 22-version-negotiation-client

[22-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[22-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[23-version-negotiation]
ssl_conf = 23-version-negotiation-ssl

[23-version-negotiation-ssl]
server = 23-version-negotiation-server
client = 23-version-negotiation-client

[23-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[24-version-negotiation]
ssl_conf = 24-version-negotiation-ssl

[24-version-negotiation-ssl]
server = 24-version-negotiation-server
client = 24-version-negotiation-client

[24-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[25-version-negotiation]
ssl_conf = 25-version-negotiation-ssl

[25-version-negotiation-ssl]
server = 25-version-negotiation-server
client = 25-version-negotiation-client

[25-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[26-version-negotiation]
ssl_conf = 26-version-negotiation-ssl

[26-version-negotiation-ssl]
server = 26-version-negotiation-server
client = 26-version-negotiation-client

[26-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[26-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[27-version-negotiation]
ssl_conf = 27-version-negotiation-ssl

[27-version-negotiation-ssl]
server = 27-version-negotiation-server
client = 27-version-negotiation-client

[27-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[27-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[28-version-negotiation]
ssl_conf = 28-version-negotiation-ssl

[28-version-negotiation-ssl]
server = 28-version-negotiation-server
client = 28-version-negotiation-client

[28-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[29-version-negotiation]
ssl_conf = 29-version-negotiation-ssl

[29-version-negotiation-ssl]
server = 29-version-negotiation-server
client = 29-version-negotiation-client

[29-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[29-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[30-version-negotiation]
ssl_conf = 30-version-negotiation-ssl

[30-version-negotiation-ssl]
server = 30-version-negotiation-server
client = 30-version-negotiation-client

[30-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[30-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedResult = ServerFail
Method = DTLS


# ===========================================================

[31-version-negotiation]
ssl_conf = 31-version-negotiation-ssl

[31-version-negotiation-ssl]
server = 31-version-negotiation-server
client = 31-version-negotiation-client

[31-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[31-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedResult = ServerFail
Method = DTLS


# ===========================================================

[32-version-negotiation]
ssl_conf = 32-version-negotiation-ssl

[32-version-negotiation-ssl]
server = 32-version-negotiation-server
client = 32-version-negotiation-client

[32-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[32-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[33-version-negotiation]
ssl_conf = 33-version-negotiation-ssl

[33-version-negotiation-ssl]
server = 33-version-negotiation-server
client = 33-version-negotiation-client

[33-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[33-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[34-version-negotiation]
ssl_conf = 34-version-negotiation-ssl

[34-version-negotiation-ssl]
server = 34-version-negotiation-server
client = 34-version-negotiation-client

[34-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[34-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[35-version-negotiation]
ssl_conf = 35-version-negotiation-ssl

[35-version-negotiation-ssl]
server = 35-version-negotiation-server
client = 35-version-negotiation-client

[35-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[35-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[36-version-negotiation]
ssl_conf = 36-version-negotiation-ssl

[36-version-negotiation-ssl]
server = 36-version-negotiation-server
client = 36-version-negotiation-client

[36-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[36-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-36]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[37-version-negotiation]
ssl_conf = 37-version-negotiation-ssl

[37-version-negotiation-ssl]
server = 37-version-negotiation-server
client = 37-version-negotiation-client

[37-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[37-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-37]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[38-version-negotiation]
ssl_conf = 38-version-negotiation-ssl

[38-version-negotiation-ssl]
server = 38-version-negotiation-server
client = 38-version-negotiation-client

[38-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[38-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-38]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[39-version-negotiation]
ssl_conf = 39-version-negotiation-ssl

[39-version-negotiation-ssl]
server = 39-version-negotiation-server
client = 39-version-negotiation-client

[39-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[39-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-39]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[40-version-negotiation]
ssl_conf = 40-version-negotiation-ssl

[40-version-negotiation-ssl]
server = 40-version-negotiation-server
client = 40-version-negotiation-client

[40-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[40-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-40]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[41-version-negotiation]
ssl_conf = 41-version-negotiation-ssl

[41-version-negotiation-ssl]
server = 41-version-negotiation-server
client = 41-version-negotiation-client

[41-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[41-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-41]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[42-version-negotiation]
ssl_conf = 42-version-negotiation-ssl

[42-version-negotiation-ssl]
server = 42-version-negotiation-server
client = 42-version-negotiation-client

[42-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[42-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-42]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[43-version-negotiation]
ssl_conf = 43-version-negotiation-ssl

[43-version-negotiation-ssl]
server = 43-version-negotiation-server
client = 43-version-negotiation-client

[43-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[43-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-43]
ExpectedProtocol = DTLSv1
ExpectedResult = Success
Method = DTLS


# ===========================================================

[44-version-negotiation]
ssl_conf = 44-version-negotiation-ssl

[44-version-negotiation-ssl]
server = 44-version-negotiation-server
client = 44-version-negotiation-client

[44-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[44-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-44]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[45-version-negotiation]
ssl_conf = 45-version-negotiation-ssl

[45-version-negotiation-ssl]
server = 45-version-negotiation-server
client = 45-version-negotiation-client

[45-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[45-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-45]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[46-version-negotiation]
ssl_conf = 46-version-negotiation-ssl

[46-version-negotiation-ssl]
server = 46-version-negotiation-server
client = 46-version-negotiation-client

[46-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[46-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-46]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[47-version-negotiation]
ssl_conf = 47-version-negotiation-ssl

[47-version-negotiation-ssl]
server = 47-version-negotiation-server
client = 47-version-negotiation-client

[47-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[47-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-47]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[48-version-negotiation]
ssl_conf = 48-version-negotiation-ssl

[48-version-negotiation-ssl]
server = 48-version-negotiation-server
client = 48-version-negotiation-client

[48-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[48-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-48]
ExpectedResult = ClientFail
Method = DTLS


# ===========================================================

[49-version-negotiation]
ssl_conf = 49-version-negotiation-ssl

[49-version-negotiation-ssl]
server = 49-version-negotiation-server
client = 49-version-negotiation-client

[49-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[49-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-49]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[50-version-negotiation]
ssl_conf = 50-version-negotiation-ssl

[50-version-negotiation-ssl]
server = 50-version-negotiation-server
client = 50-version-negotiation-client

[50-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[50-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-50]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[51-version-negotiation]
ssl_conf = 51-version-negotiation-ssl

[51-version-negotiation-ssl]
server = 51-version-negotiation-server
client = 51-version-negotiation-client

[51-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[51-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-51]
ExpectedResult = ClientFail
Method = DTLS


# ===========================================================

[52-version-negotiation]
ssl_conf = 52-version-negotiation-ssl

[52-version-negotiation-ssl]
server = 52-version-negotiation-server
client = 52-version-negotiation-client

[52-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[52-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-52]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[53-version-negotiation]
ssl_conf = 53-version-negotiation-ssl

[53-version-negotiation-ssl]
server = 53-version-negotiation-server
client = 53-version-negotiation-client

[53-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[53-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-53]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[54-version-negotiation]
ssl_conf = 54-version-negotiation-ssl

[54-version-negotiation-ssl]
server = 54-version-negotiation-server
client = 54-version-negotiation-client

[54-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[54-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-54]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[55-version-negotiation]
ssl_conf = 55-version-negotiation-ssl

[55-version-negotiation-ssl]
server = 55-version-negotiation-server
client = 55-version-negotiation-client

[55-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[55-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-55]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[56-version-negotiation]
ssl_conf = 56-version-negotiation-ssl

[56-version-negotiation-ssl]
server = 56-version-negotiation-server
client = 56-version-negotiation-client

[56-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[56-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-56]
ExpectedResult = ClientFail
Method = DTLS


# ===========================================================

[57-version-negotiation]
ssl_conf = 57-version-negotiation-ssl

[57-version-negotiation-ssl]
server = 57-version-negotiation-server
client = 57-version-negotiation-client

[57-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[57-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-57]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[58-version-negotiation]
ssl_conf = 58-version-negotiation-ssl

[58-version-negotiation-ssl]
server = 58-version-negotiation-server
client = 58-version-negotiation-client

[58-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[58-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-58]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[59-version-negotiation]
ssl_conf = 59-version-negotiation-ssl

[59-version-negotiation-ssl]
server = 59-version-negotiation-server
client = 59-version-negotiation-client

[59-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[59-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-59]
ExpectedResult = ClientFail
Method = DTLS


# ===========================================================

[60-version-negotiation]
ssl_conf = 60-version-negotiation-ssl

[60-version-negotiation-ssl]
server = 60-version-negotiation-server
client = 60-version-negotiation-client

[60-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[60-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-60]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[61-version-negotiation]
ssl_conf = 61-version-negotiation-ssl

[61-version-negotiation-ssl]
server = 61-version-negotiation-server
client = 61-version-negotiation-client

[61-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[61-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-61]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[62-version-negotiation]
ssl_conf = 62-version-negotiation-ssl

[62-version-negotiation-ssl]
server = 62-version-negotiation-server
client = 62-version-negotiation-client

[62-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[62-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-62]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


# ===========================================================

[63-version-negotiation]
ssl_conf = 63-version-negotiation-ssl

[63-version-negotiation-ssl]
server = 63-version-negotiation-server
client = 63-version-negotiation-client

[63-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[63-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-63]
ExpectedProtocol = DTLSv1.2
ExpectedResult = Success
Method = DTLS


