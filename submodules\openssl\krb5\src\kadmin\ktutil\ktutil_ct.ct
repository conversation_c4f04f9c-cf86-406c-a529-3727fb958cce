# Copyright 1995 by the Massachusetts Institute of Technology.
# All Rights Reserved.
# 
# Export of this software from the United States of America may
#   require a specific license from the United States Government.
#   It is the responsibility of any person or organization contemplating
#   export to obtain such a license before exporting.
# 
# WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
# distribute this software and its documentation for any purpose and
# without fee is hereby granted, provided that the above copyright
# notice appear in all copies and that both that copyright notice and
# this permission notice appear in supporting documentation, and that
# the name of M.I.T. not be used in advertising or publicity pertaining
# to distribution of the software without specific, written prior
# permission.  Furthermore if you modify this software you must label
# your software as modified software and not distribute it in such a
# fashion that it might be confused with the original M.I.T. software.
# M.I.T. makes no representations about the suitability of
# this software for any purpose.  It is provided "as is" without express
# or implied warranty.
# 
# 
# Command table for ktutil
#

command_table ktutil_cmds;

request ktutil_clear_list, "Clear the current keylist.",
	clear_list, clear;

request ktutil_read_v5, "Read a krb5 keytab into the current keylist.",
	read_kt, rkt;

request ktutil_read_v4, "Read a krb4 srvtab into the current keylist.",
	read_st, rst;

request ktutil_write_v5, "Write the current keylist to a krb5 keytab.",
	write_kt, wkt;

request ktutil_write_v4, "Write the current keylist to a krb4 srvtab.",
	write_st, wst;

request ktutil_add_entry, "Add an entry to the current keylist.",
	add_entry, addent;

request ktutil_delete_entry, "Delete an entry from the current keylist.",
	delete_entry, delent;

request ktutil_list, "List the current keylist.",
	list, l;

request ss_list_requests, "List available requests.",
	list_requests, lr, "?";

request ss_quit, "Exit program.",
	quit, exit, q;

end;
