# Vectors from https://gitlab.com/dkg/ocb-test-vectors/-/blob/ec0131616679f588c3be0ac7c33b7a663e1a47d4/test-vector-1-nonce112.txt
# 112-bit nonce forms of RFC 7253
# Reformatted to work with our NIST loader

COUNT = 0
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221100
AAD =
Plaintext =
Ciphertext = B22774052013981C3038DA65757A55E4

COUNT = 1
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221101
AAD = 0001020304050607
Plaintext = 0001020304050607
Ciphertext = 3791C1215BB0F2E3B008F1A9BFF0A2069BD2B3B93168AD17

COUNT = 2
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221102
AAD = 0001020304050607
Plaintext =
Ciphertext = C07EDDBB4359D5E68F7618E7D397BFAC

COUNT = 3
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221103
AAD =
Plaintext = 0001020304050607
Ciphertext = AF02506326B86248936845A600CEC91888F52C214ED1674A

COUNT = 4
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221104
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 165A060B6A9ED2F930A7D6DEC0195B5B19722619DD37749A1B97FF6C63393009

COUNT = 5
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221105
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext =
Ciphertext = E1616FC25D8A300D8C64CD2018071BFE

COUNT = 6
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221106
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = 14F2D292673A8F1DA6B80543658784F5DEE9FF8FF1B0A40FD16720E2B2970549

COUNT = 7
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221107
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = 98B9D538E13A8D12CE94C53F7C36675C77C0A8C9BDE234FDB02E92506483A49976AE5585F1777360

COUNT = 8
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221108
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext =
Ciphertext = 8B3F1EA59A0D5D7C9FD369B6CEE0F4A6

COUNT = 9
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA99887766554433221109
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = AB910BACF2F2409EE871D2818AC31ADFAA39DA637E2D1BF2D87985B4B532966CAF43B13EE754AB07

COUNT = 10
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110A
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 635623537A7D54ABB68F5B23E151FFAA77A6E0EC19DF46117C2E990154C74539393E11FBC726AACB37023175DD592667

COUNT = 11
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext =
Ciphertext = 97CC4DE80C67DD335771E498944288BF

COUNT = 12
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110C
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = A159D066AB9BF3C2B3DC4881BD4DE3A197B12548F11F91972D3B0E881A811AED6427067C99276B1CD0E4382C4C9A8709

COUNT = 13
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110D
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 259731C171E61C6FEE8E55A7B784DAB3B4B75AC8DE8C4A3CC01C96F19EAFA7F4FC49C84220893993A0B48DF8A969ACECD84BBAA996375A4F

COUNT = 14
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110E
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext =
Ciphertext = F03AF5CB012CF7228FCAEC2B2B0FFF69

COUNT = 15
Key = 000102030405060708090A0B0C0D0E0F
Nonce = DDCCBBAA9988776655443322110F
AAD =
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 45979D0ACA9A7DA537C96BCDBE1F40A288C9FD0E608148C904DA17ACAC18407E3F5D1D5A9546EA59F9F3A1D13EEF2A13676101371D0BAB20
