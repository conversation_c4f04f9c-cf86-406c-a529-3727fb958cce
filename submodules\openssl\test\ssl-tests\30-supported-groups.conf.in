# -*- mode: perl; -*-
# Copyright 2016-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (
    {
        name => "Just a sanity test case",
        server => { },
        client => { },
        test   => { "ExpectedResult" => "Success" },
    },
);

our @tests_tls1_3 = (
    {
        name => "Fail empty groups with TLS1.3",
        server => { },
        client => { "Groups" => "sect163k1" },
        test   => { "ExpectedResult" => "ClientFail" },
    },
);

our @tests_tls1_2 = (
    {
        name => "Pass with empty groups with TLS1.2",
        server => { },
        client => { "Groups" => "sect163k1",
                    "MaxProtocol" => "TLSv1.2" },
        test   => { "ExpectedResult" => "Success" },
    },
);

push @tests, @tests_tls1_3 unless disabled("tls1_3")
                                  || !disabled("ec2m") || disabled("ec");
push @tests, @tests_tls1_2 unless disabled("tls1_2") || disabled("ec");
