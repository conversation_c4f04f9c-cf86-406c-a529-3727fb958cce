
A profile file is a generic way of storing program configuration
information for applications.  An application may choose to consult
multiple configuration files; for example, a Kerberos application
might look first in ~/.krb5rc, and then in /etc/krb5.conf.  So
/etc/krb5.conf would contain the side-wide default configuration for
Kerberos, and ~/.krb5rc would contain the user's specific
configuration overrides.

Configuration information is stored in relations, which have a name
and a value.  There may be multiple relations with the same name.
Relations are always contained inside named sections.  Sections can
contain relations and other named child sections.

Top-level sections are defined by enclosing the section name in square
braces.  Child sections are defined by enclosing the contents of the
child section in curly braces.  Relations are defined by using the
format "name = value".  

An example profile file might look like this:

[libdefaults]
	default_realm = ATHENA.MIT.EDU

[realms]
	ATHENA.MIT.EDU = {
		kdc = kerberos.mit.edu
		kdc = kerberos-1.mit.edu
		kdc = kerberos-2.mit.edu
		master_kdc = kerberos.mit.edu
		admin_server = kerberos.mit.edu
	}
	CYGNUS.COM = {
		kdc = KERBEROS-1.CYGNUS.COM
		kdc = KERBEROS.CYGNUS.COM
		admin_server = KERBEROS.MIT.EDU
	}

In this example, the profile file has two top-level sections,
"libdefaults" and "realms".  The libdefaults section has a single
relation which is named "default_realm" and has the value
"ATHENA.MIT.EDU".  The realms section has two child sections,
"ATHENA.MIT.EDU" and "CYGNUS.MIT.EDU".  Each of these child has a
number of relations, "kdc", "admin_server", and (in the case of
"ATHENA.MIT.EDU"), "default_domain".  Note that there are multiple
relations with the name "kdc" in both sections; if a
profile_get_values() is called querying the "kdc" relation, both
values will be returned.

Sections may be marked as "final".  If they are marked as final, then
the contents of that section override all subsequent profile files (if
the application is searching multiple profile files for its
configuration information).  Normally, all of the profiles are
searched for a matching relation, and all of the values found in all
of the various profile files will be returned.  

Top-level sections are marked as final by adding an '*' character
following the closing square brace.  Child sections are marked as
final by adding a '*' character after the closing curly brace.  So for
example, in this example both the "libdefaults" and "ATHENA.MIT.EDU"
sections have been marked as final:

[libdefaults]*
	default_realm = ATHENA.MIT.EDU

[realms]
	ATHENA.MIT.EDU = {
		kdc = kerberos.mit.edu
		master_kdc = kerberos.mit.edu
		admin_server = kerberos.mit.edu
	}*

