=pod

=head1 NAME

X509_cmp_time, X509_cmp_current_time, X509_time_adj, X509_time_adj_ex
- X509 time functions

=head1 SYNOPSIS

 int X509_cmp_time(const ASN1_TIME *asn1_time, time_t *in_tm);
 int X509_cmp_current_time(const ASN1_TIME *asn1_time);
 ASN1_TIME *X509_time_adj(ASN1_TIME *asn1_time, long offset_sec, time_t *in_tm);
 ASN1_TIME *X509_time_adj_ex(ASN1_TIME *asn1_time, int offset_day, long
                             offset_sec, time_t *in_tm);

=head1 DESCRIPTION

X509_cmp_time() compares the ASN1_TIME in B<asn1_time> with the time
in <cmp_time>. X509_cmp_current_time() compares the ASN1_TIME in
B<asn1_time> with the current time, expressed as time_t. B<asn1_time>
must satisfy the ASN1_TIME format mandated by RFC 5280, i.e., its
format must be either YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ.

X509_time_adj_ex() sets the ASN1_TIME structure B<asn1_time> to the time
B<offset_day> and B<offset_sec> after B<in_tm>.

X509_time_adj() sets the ASN1_TIME structure B<asn1_time> to the time
B<offset_sec> after B<in_tm>. This method can only handle second
offsets up to the capacity of long, so the newer X509_time_adj_ex()
API should be preferred.

In both methods, if B<asn1_time> is NULL, a new ASN1_TIME structure
is allocated and returned.

In all methods, if B<in_tm> is NULL, the current time, expressed as
time_t, is used.

=head1 BUGS

Unlike many standard comparison functions, X509_cmp_time() and
X509_cmp_current_time() return 0 on error.

=head1 RETURN VALUES

X509_cmp_time() and X509_cmp_current_time() return -1 if B<asn1_time>
is earlier than, or equal to, B<cmp_time> (resp. current time), and 1
otherwise. These methods return 0 on error.

X509_time_adj() and X509_time_adj_ex() return a pointer to the updated
ASN1_TIME structure, and NULL on error.

=head1 COPYRIGHT

Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
