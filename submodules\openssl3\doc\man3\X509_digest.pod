=pod

=head1 NAME

X509_digest,
X509_digest_sig,
X509_CRL_digest,
X509_pubkey_digest,
X509_NAME_digest,
X509_REQ_digest,
PKCS7_ISSUER_AND_SERIAL_digest
- get digest of various objects

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_digest(const X509 *data, const EVP_MD *type, unsigned char *md,
                 unsigned int *len);
 ASN1_OCTET_STRING *X509_digest_sig(const X509 *cert,
                                    EVP_MD **md_used, int *md_is_fallback);

 int X509_CRL_digest(const X509_CRL *data, const EVP_MD *type, unsigned char *md,
                     unsigned int *len);

 int X509_pubkey_digest(const X509 *data, const EVP_MD *type,
                        unsigned char *md, unsigned int *len);

 int X509_REQ_digest(const X509_REQ *data, const EVP_MD *type,
                     unsigned char *md, unsigned int *len);

 int X509_NAME_digest(const X509_NAME *data, const EVP_MD *type,
                      unsigned char *md, unsigned int *len);

 #include <openssl/pkcs7.h>

 int PKCS7_ISSUER_AND_SERIAL_digest(PKCS7_ISSUER_AND_SERIAL *data,
                                    const EVP_MD *type, unsigned char *md,
                                    unsigned int *len);

=head1 DESCRIPTION

X509_digest_sig() calculates a digest of the given certificate I<cert>
using the same hash algorithm as in its signature, if the digest
is an integral part of the certificate signature algorithm identifier.
Otherwise, a fallback hash algorithm is determined as follows:
SHA512 if the signature algorithm is ED25519,
SHAKE256 if it is ED448, otherwise SHA256.
The output parameters are assigned as follows.
Unless I<md_used> is NULL, the hash algorithm used is provided
in I<*md_used> and must be freed by the caller (if it is not NULL).
Unless I<md_is_fallback> is NULL,
the I<*md_is_fallback> is set to 1 if the hash algorithm used is a fallback,
otherwise to 0.

X509_pubkey_digest() returns a digest of the DER representation of the public
key in the specified X509 I<data> object.

All other functions described here return a digest of the DER representation
of their entire I<data> objects.

The I<type> parameter specifies the digest to
be used, such as EVP_sha1(). The I<md> is a pointer to the buffer where the
digest will be copied and is assumed to be large enough; the constant
B<EVP_MAX_MD_SIZE> is suggested. The I<len> parameter, if not NULL, points
to a place where the digest size will be stored.

=head1 RETURN VALUES

X509_digest_sig() returns an ASN1_OCTET_STRING pointer on success, else NULL.

All other functions described here return 1 for success and 0 for failure.

=head1 SEE ALSO

L<EVP_sha1(3)>

=head1 HISTORY

The X509_digest_sig() function was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2017-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
