.\" Man page generated from reStructuredText.
.
.TH "KLIST" "1" " " "1.17.1" "MIT Kerberos"
.SH NAME
klist \- list cached Kerberos tickets
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBklist\fP
[\fB\-e\fP]
[[\fB\-c\fP] [\fB\-l\fP] [\fB\-A\fP] [\fB\-f\fP] [\fB\-s\fP] [\fB\-a\fP [\fB\-n\fP]]]
[\fB\-C\fP]
[\fB\-k\fP [\fB\-t\fP] [\fB\-K\fP]]
[\fB\-V\fP]
[\fIcache_name\fP|\fIkeytab_name\fP]
.SH DESCRIPTION
.sp
klist lists the Kerberos principal and Kerberos tickets held in a
credentials cache, or the keys held in a keytab file.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-e\fP
Displays the encryption types of the session key and the ticket
for each credential in the credential cache, or each key in the
keytab file.
.TP
\fB\-l\fP
If a cache collection is available, displays a table summarizing
the caches present in the collection.
.TP
\fB\-A\fP
If a cache collection is available, displays the contents of all
of the caches in the collection.
.TP
\fB\-c\fP
List tickets held in a credentials cache. This is the default if
neither \fB\-c\fP nor \fB\-k\fP is specified.
.TP
\fB\-f\fP
Shows the flags present in the credentials, using the following
abbreviations:
.INDENT 7.0
.INDENT 3.5
.sp
.nf
.ft C
F    Forwardable
f    forwarded
P    Proxiable
p    proxy
D    postDateable
d    postdated
R    Renewable
I    Initial
i    invalid
H    Hardware authenticated
A    preAuthenticated
T    Transit policy checked
O    Okay as delegate
a    anonymous
.ft P
.fi
.UNINDENT
.UNINDENT
.TP
\fB\-s\fP
Causes klist to run silently (produce no output).  klist will exit
with status 1 if the credentials cache cannot be read or is
expired, and with status 0 otherwise.
.TP
\fB\-a\fP
Display list of addresses in credentials.
.TP
\fB\-n\fP
Show numeric addresses instead of reverse\-resolving addresses.
.TP
\fB\-C\fP
List configuration data that has been stored in the credentials
cache when klist encounters it.  By default, configuration data
is not listed.
.TP
\fB\-k\fP
List keys held in a keytab file.
.TP
\fB\-i\fP
In combination with \fB\-k\fP, defaults to using the default client
keytab instead of the default acceptor keytab, if no name is
given.
.TP
\fB\-t\fP
Display the time entry timestamps for each keytab entry in the
keytab file.
.TP
\fB\-K\fP
Display the value of the encryption key in each keytab entry in
the keytab file.
.TP
\fB\-V\fP
Display the Kerberos version number and exit.
.UNINDENT
.sp
If \fIcache_name\fP or \fIkeytab_name\fP is not specified, klist will display
the credentials in the default credentials cache or keytab file as
appropriate.  If the \fBKRB5CCNAME\fP environment variable is set, its
value is used to locate the default ticket cache.
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH FILES
.INDENT 0.0
.TP
.B \fB@CCNAME@\fP
Default location of Kerberos 5 credentials cache
.TP
.B \fB@KTNAME@\fP
Default location for the local host\(aqs keytab file.
.UNINDENT
.SH SEE ALSO
.sp
kinit(1), kdestroy(1), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2019, MIT
.\" Generated by docutils manpage writer.
.
