=pod

=head1 NAME

EVP_sha1
- SHA-1 For EVP

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_MD *EVP_sha1(void);

=head1 DESCRIPTION

SHA-1 (Secure Hash Algorithm 1) is a cryptographic hash function standardized
in NIST FIPS 180-4. The algorithm was designed by the United States National
Security Agency and initially published in 1995.

=over 4

=item EVP_sha1()

The SHA-1 algorithm which produces a 160-bit output from a given input.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling this function multiple times and should consider using
L<EVP_MD_fetch(3)> with L<EVP_MD-SHA1(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return a B<EVP_MD> structure that contains the
implementation of the message digest. See L<EVP_MD_meth_new(3)> for
details of the B<EVP_MD> structure.

=head1 CONFORMING TO

NIST FIPS 180-4.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_DigestInit(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

