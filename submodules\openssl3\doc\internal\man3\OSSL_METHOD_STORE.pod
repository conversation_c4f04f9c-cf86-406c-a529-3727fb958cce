=pod

=head1 NAME

OSSL_METHOD_STORE, ossl_method_store_new, ossl_method_store_free,
ossl_method_store_init, ossl_method_store_cleanup,
ossl_method_store_add, ossl_method_store_fetch,
ossl_method_store_remove, ossl_method_store_remove_all_provided, 
ossl_method_store_cache_get, ossl_method_store_cache_set,
ossl_method_store_cache_flush_all
- implementation method store and query

=head1 SYNOPSIS

 #include "internal/property.h"

 typedef struct ossl_method_store_st OSSL_METHOD_STORE;

 OSSL_METHOD_STORE *ossl_method_store_new(OSSL_LIB_CTX *ctx);
 void ossl_method_store_free(OSSL_METHOD_STORE *store);
 int ossl_method_store_init(OSSL_LIB_CTX *ctx);
 void ossl_method_store_cleanup(OSSL_LIB_CTX *ctx);
 int ossl_method_store_add(OSSL_METHOD_STORE *store, const OSSL_PROVIDER *prov,
                           int nid, const char *properties, void *method,
                           int (*method_up_ref)(void *),
                           void (*method_destruct)(void *));
 int ossl_method_store_remove(OSSL_METHOD_STORE *store,
                              int nid, const void *method);
 int ossl_method_store_fetch(OSSL_METHOD_STORE *store,
                             int nid, const char *properties,
                             void **method, const OSSL_PROVIDER **prov_rw);
 int ossl_method_store_remove_all_provided(OSSL_METHOD_STORE *store,
                                           const OSSL_PROVIDER *prov);

 int ossl_method_store_cache_get(OSSL_METHOD_STORE *store, OSSL_PROVIDER *prov,
                                 int nid, const char *prop_query, void **method);
 int ossl_method_store_cache_set(OSSL_METHOD_STORE *store, OSSL_PROVIDER *prov,
                                 int nid, const char *prop_query, void *method,
                                 int (*method_up_ref)(void *),
                                 void (*method_destruct)(void *));
 void ossl_method_store_cache_flush_all(OSSL_METHOD_STORE *store);

=head1 DESCRIPTION

OSSL_METHOD_STORE stores methods that can be queried using properties and a
numeric identity (nid).

Methods are expected to be library internal structures.
It's left to the caller to define the exact contents.

Numeric identities are expected to be an algorithm identity for the methods.
It's left to the caller to define exactly what an algorithm is, and to allocate
these numeric identities accordingly.

The B<OSSL_METHOD_STORE> also holds an internal query cache, which is accessed
separately (see L</Cache Functions> below).

=head2 Store Functions

ossl_method_store_init() initialises the method store subsystem in the scope of
the library context I<ctx>.

ossl_method_store_cleanup() cleans up and shuts down the implementation method
store subsystem in the scope of the library context I<ctx>.

ossl_method_store_new() create a new empty method store using the supplied
I<ctx> to allow access to the required underlying property data.

ossl_method_store_free() frees resources allocated to I<store>.

ossl_method_store_add() adds the I<method> constructed from an implementation in
the provider I<prov> to the I<store> as an instance of an algorithm indicated by
I<nid> and the property definition I<properties>, unless the I<store> already
has a method from the same provider with the same I<nid> and I<properties>.
If the I<method_up_ref> function is given, it's called to increment the
reference count of the method.
If the I<method_destruct> function is given, it's called when this function
fails to add the method to the store, or later on when it is being released from
the I<store>.

ossl_method_store_remove() removes the I<method> identified by I<nid> from the
I<store>.

ossl_method_store_fetch() queries I<store> for a method identified by I<nid>
that matches the property query I<prop_query>.
I<*prop> may be a pointer to a provider, which will narrow the search
to methods from that provider.
The result, if any, is returned in I<*method>, and its provider in I<*prov>.

ossl_method_store_remove_all_provided() removes all methods from I<store>
that are provided by I<prov>.
When doing so, it also flushes the corresponding cache entries.

=head2 Cache Functions

ossl_method_store_cache_get() queries the cache associated with the I<store>
for a method identified by I<nid> that matches the property query
I<prop_query>.
Additionally, if I<prov> isn't NULL, it will be used to narrow the search
to only include methods from that provider.
The result, if any, is returned in I<method>.

ossl_method_store_cache_set() sets a cache entry identified by I<nid> from the
provider I<prov>, with the property query I<prop_query> in the I<store>.
Future calls to ossl_method_store_cache_get() will return the specified I<method>.
The I<method_up_ref> function is called to increment the
reference count of the method and the I<method_destruct> function is called
to decrement it.

ossl_method_store_cache_flush_all() flushes all cached entries associated with
I<store>.

=head1 NOTES

The I<prop_query> argument to ossl_method_store_cache_get() and
ossl_method_store_cache_set() is not allowed to be NULL.  Use "" for an
empty property definition or query.

=head1 RETURN VALUES

ossl_method_store_new() returns a new method store object or NULL on failure.

ossl_method_store_free(), ossl_method_store_add(),
ossl_method_store_remove(), ossl_method_store_fetch(),
ossl_method_store_cache_get(), ossl_method_store_cache_set() and
ossl_method_store_flush_cache() return B<1> on success and B<0> on error.

ossl_method_store_free() and ossl_method_store_cleanup() do not return any value.

=head1 HISTORY

This functionality was added to OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.
Copyright (c) 2019, Oracle and/or its affiliates.  All rights reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use this
file except in compliance with the License.  You can obtain a copy in the file
LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
