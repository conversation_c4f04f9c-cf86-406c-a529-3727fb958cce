mydir=plugins$(S)pwqual$(S)test
BUILDTOP=$(REL)..$(S)..$(S)..

LIBBASE=pwqual_test
LIBMAJOR=0
LIBMINOR=0
RELDIR=../plugins/pwqual/test
# Depends on libkrb5 and possibly libkrb5support
SHLIB_EXPDEPS= $(KRB5_BASE_DEPLIBS)
SHLIB_EXPLIBS= $(KRB5_BASE_LIBS)

STLIBOBJS=main.o

SRCS= $(srcdir)/main.c

all-unix: all-libs
install-unix:
clean-unix:: clean-liblinks clean-libs clean-libobjs

@libnover_frag@
@libobj_frag@
