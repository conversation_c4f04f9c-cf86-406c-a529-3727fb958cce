=pod

=head1 NAME

crypto - OpenSSL cryptographic library

=head1 SYNOPSIS

See the individual manual pages for details.

=head1 DESCRIPTION

The OpenSSL B<crypto> library implements a wide range of cryptographic
algorithms used in various Internet standards. The services provided
by this library are used by the OpenSSL implementations of SSL, TLS
and S/MIME, and they have also been used to implement SSH, OpenPGP, and
other cryptographic standards.

B<libcrypto> consists of a number of sub-libraries that implement the
individual algorithms.

The functionality includes symmetric encryption, public key
cryptography and key agreement, certificate handling, cryptographic
hash functions, cryptographic pseudo-random number generator, and
various utilities.

=head1 NOTES

Some of the newer functions follow a naming convention using the numbers
B<0> and B<1>. For example the functions:

 int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);
 int X509_add1_trust_object(X509 *x, const ASN1_OBJECT *obj);

The B<0> version uses the supplied structure pointer directly
in the parent and it will be freed up when the parent is freed.
In the above example B<crl> would be freed but B<rev> would not.

The B<1> function uses a copy of the supplied structure pointer
(or in some cases increases its link count) in the parent and
so both (B<x> and B<obj> above) should be freed up.

=head1 RETURN VALUES

See the individual manual pages for details.

=head1 SEE ALSO

L<openssl(1)>, L<ssl(7)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
