expected,description, -section,val, -recipient,val, -expect_sender,val, -srvcert,val, -trusted,val, -untrusted,val, -ignore_keyusage, -unprotected_errors, -extracertsout,val,val2, -opt1,arg1, -opt2,arg2, -opt3,arg3
,,,,,Recipient,options:,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,default test, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
0,recipient missing arg, -section,, -recipient,,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
1,unknown attribute in recipient name, -section,, -recipient,_CA_DN/ABC=123,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong syntax in recipient name: trailing double '/' after value, -section,, -recipient,_CA_DN//,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong syntax in recipient name: missing '=', -section,, -recipient,/CDE,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong syntax in recipient name: C too long, -section,, -recipient,/CN=ECC Issuing CA v10/OU=For test purpose only/O=CMPforOpenSSL/C=DEE,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,config default with expected sender, -section,, -recipient,_CA_DN, -expect_sender,_SERVER_DN,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,expected sender missing arg, -section,, -recipient,_CA_DN, -expect_sender,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong expected sender, -section,, -recipient,_CA_DN, -expect_sender,/CN=Sample Cert/OU=R&D/O=Company Ltd./L=Dublin 4/C=IE,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
1,unknown attribute in expected sender, -section,, -recipient,_CA_DN, -expect_sender,_SERVER_DN/ABC=123,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,extra attribute in expected sender, -section,, -recipient,_CA_DN, -expect_sender,_SERVER_DN/serialNumber=123,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,double attribute in expected sender, -section,, -recipient,_CA_DN, -expect_sender,/CN=ECC Issuing CA v10_SERVER_DN,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,missing attribute in expected sender, -section,, -recipient,_CA_DN, -expect_sender,/CN=ECC Issuing CA v10/OU=For test purpose only/C=DE,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,bad syntax in expected sender name: leading double '/', -section,, -recipient,_CA_DN, -expect_sender,//_CA_DN,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,bad syntax in expected sender name: trailing double '/', -section,, -recipient,_CA_DN, -expect_sender,_CA_DN//,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,bad syntax in expected sender name: missing '=', -section,, -recipient,_CA_DN, -expect_sender,/C=DE/CN=ECC Issuing CA v10/OU=For test purpose only/OCMPforOpenSSL,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,expected sender empty attributes, -section,, -recipient,_CA_DN, -expect_sender,/CN=/OU=/O=/C=,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,explicit srvcert, -section,,,,BLANK,, -srvcert,_SERVER_CERT, -trusted,"""",BLANK,,,, -unprotected_errors,BLANK,,,,,,,,
0,srvcert missing arg, -section,, -recipient,"""",BLANK,, -srvcert,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong srvcert, -section,, -recipient,"""",BLANK,, -srvcert,signer.crt, -trusted,"""",BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,srvcert is empty file, -section,, -recipient,"""",BLANK,, -srvcert,empty.txt, -trusted,"""",BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,srvcert random content, -section,, -recipient,"""",BLANK,, -srvcert,random.bin, -trusted,"""",BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,no -trusted but srvcert, -section,, -recipient,_CA_DN,BLANK,, -srvcert,_SERVER_CERT,BLANK,,BLANK,,, -unprotected_errors,BLANK,,,,,,,,
0,trusted missing arg, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,wrong trusted cert, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,signer.crt,BLANK,,BLANK, -unprotected_errors,BLANK, -secret,"""", -cert,signer.crt, -key,signer.p12, -keypass,pass:12345
0,trusted empty file, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,empty.txt,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,trusted random file, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,random.bin,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,trusted file does not exist, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,idontexist,BLANK,,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
0,untrusted missing arg, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt, -untrusted,,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,untrusted empty file, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt, -untrusted,empty.txt,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,untrusted random file, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt, -untrusted,random.bin,BLANK, -unprotected_errors,BLANK,,,,,,,,
0,untrusted file does not exist, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt, -untrusted,idontexist,BLANK, -unprotected_errors,BLANK,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,ignore key usage, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,, -ignore_keyusage, -unprotected_errors,BLANK,,,,,,,,
0,ignorekeyusage with parameter, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,, -unprotected_errors,BLANK, -ignore_keyusage,1,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,no unprotected errors - no errors, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK,BLANK,BLANK,,,,,,,,
0,unprotected_errors with parameter, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK,BLANK,BLANK,,, -unprotected_errors,123,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,
1,extracertsout, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors, -extracertsout,_RESULT_DIR/test.extracerts.pem,,,,,,,
0,extracertsout no parameter, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors, -extracertsout,,,,,,,,
0,extracertsout multiple arguments, -section,, -recipient,_CA_DN,BLANK,,BLANK,, -trusted,trusted.crt,BLANK,,BLANK, -unprotected_errors, -extracertsout,abc,def,,,,,,
