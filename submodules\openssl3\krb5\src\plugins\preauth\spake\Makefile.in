mydir=plugins$(S)preauth$(S)spake
BUILDTOP=$(REL)..$(S)..$(S)..
MODULE_INSTALL_DIR = $(KRB5_PA_MODULE_DIR)

# Like RUN_TEST, but use t_krb5.conf from this directory.
RUN_TEST_LOCAL_CONF=$(RUN_SETUP) KRB5_CONFIG=$(srcdir)/t_krb5.conf LC_ALL=C \
	$(VALGRIND)

LIBBASE=spake
LIBMAJOR=0
LIBMINOR=0
RELDIR=../plugins/preauth/spake
SHLIB_EXPDEPS=$(KRB5_BASE_DEPLIBS)
SHLIB_EXPLIBS=$(KRB5_BASE_LIBS) $(SPAKE_OPENSSL_LIBS)

WINLIBS = $(SLIB) $(KLIB) $(CLIB)

STLIBOBJS=util.o iana.o groups.o openssl.o edwards25519.o \
	spake_client.o spake_kdc.o

SRCS= \
	$(srcdir)/util.c \
	$(srcdir)/iana.c \
	$(srcdir)/groups.c \
	$(srcdir)/openssl.c \
	$(srcdir)/edwards25519.c \
	$(srcdir)/spake_client.c \
	$(srcdir)/spake_kdc.c

# Don't include spake_kdc.c in the Windows object list since we don't
# need it.
OBJS=	$(OUTPRE)util.$(OBJEXT) \
	$(OUTPRE)iana.$(OBJEXT) \
	$(OUTPRE)groups.$(OBJEXT) \
	$(OUTPRE)openssl.$(OBJEXT) \
	$(OUTPRE)edwards25519.$(OBJEXT) \
	$(OUTPRE)spake_client.$(OBJEXT)

t_vectors: t_vectors.o $(STLIBOBJS) $(SHLIB_EXPDEPS)
	$(CC_LINK) -o $@ t_vectors.o $(STLIBOBJS) $(SHLIB_EXPLIBS)

all-unix: all-liblinks
install-unix: install-libs
clean-unix:: clean-liblinks clean-libs clean-libobjs

clean:
	$(RM) t_vectors t_vectors.o $(STLIBOBJS)

check-unix: t_vectors
	$(RUN_TEST_LOCAL_CONF) ./t_vectors

all-windows: $(OUTPRE)$(SPAKELIB).dll
clean-windows::
	$(RM) $(OUTPRE)$(SPAKELIB).dll

$(OUTPRE)$(SPAKELIB).dll: spake.def $(OBJS)
	link /dll $(LOPTS) -def:spake.def -out:$*.dll $(OBJS) $(WINLIBS)

@libnover_frag@
@libobj_frag@
