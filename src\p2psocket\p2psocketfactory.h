// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once
#include "common_defines.h"
#include "socket.h"

class P2pSocketFactory {
 public:
  static SocketInterface* CreateP2pSocket(int type,
                                          SocketOptions* option = nullptr,
                                          bool isaccept = false,
                                          bool isouter = false);
  void SSLInit();
  void SSLUninit();
  enum SocketType { TCP, KCP, MUTILTCP, FSTCP, SSL, QUIC };

 private:

};
