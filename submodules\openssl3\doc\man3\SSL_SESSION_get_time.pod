=pod

=head1 NAME

SSL_SESSION_get_time, SSL_SESSION_set_time, SSL_SESSION_get_timeout,
SSL_SESSION_set_timeout,
SSL_get_time, SSL_set_time, SSL_get_timeout, SSL_set_timeout
- retrieve and manipulate session time and timeout settings

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_SESSION_get_time(const SSL_SESSION *s);
 long SSL_SESSION_set_time(SSL_SESSION *s, long tm);
 long SSL_SESSION_get_timeout(const SSL_SESSION *s);
 long SSL_SESSION_set_timeout(SSL_SESSION *s, long tm);

 long SSL_get_time(const SSL_SESSION *s);
 long SSL_set_time(SSL_SESSION *s, long tm);
 long SSL_get_timeout(const SSL_SESSION *s);
 long SSL_set_timeout(SSL_SESSION *s, long tm);

=head1 DESCRIPTION

SSL_SESSION_get_time() returns the time at which the session B<s> was
established. The time is given in seconds since the Epoch and therefore
compatible to the time delivered by the time() call.

SSL_SESSION_set_time() replaces the creation time of the session B<s> with
the chosen value B<tm>.

SSL_SESSION_get_timeout() returns the timeout value set for session B<s>
in seconds.

SSL_SESSION_set_timeout() sets the timeout value for session B<s> in seconds
to B<tm>.

The SSL_get_time(), SSL_set_time(), SSL_get_timeout(), and SSL_set_timeout()
functions are synonyms for the SSL_SESSION_*() counterparts.

=head1 NOTES

Sessions are expired by examining the creation time and the timeout value.
Both are set at creation time of the session to the actual time and the
default timeout value at creation, respectively, as set by
L<SSL_CTX_set_timeout(3)>.
Using these functions it is possible to extend or shorten the lifetime
of the session.

=head1 RETURN VALUES

SSL_SESSION_get_time() and SSL_SESSION_get_timeout() return the currently
valid values.

SSL_SESSION_set_time() and SSL_SESSION_set_timeout() return 1 on success.

If any of the function is passed the NULL pointer for the session B<s>,
0 is returned.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_timeout(3)>,
L<SSL_get_default_timeout(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
