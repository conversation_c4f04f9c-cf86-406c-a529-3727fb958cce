# Generated with generate_ssl_tests.pl

num_tests = 14

test-0 = 0-renegotiate-client-no-resume
test-1 = 1-renegotiate-client-resume
test-2 = 2-renegotiate-server-no-resume
test-3 = 3-renegotiate-server-resume
test-4 = 4-renegotiate-client-auth-require
test-5 = 5-renegotiate-client-auth-once
test-6 = 6-renegotiate-aead-to-non-aead
test-7 = 7-renegotiate-non-aead-to-aead
test-8 = 8-renegotiate-non-aead-to-non-aead
test-9 = 9-renegotiate-aead-to-aead
test-10 = 10-no-renegotiation-server-by-client
test-11 = 11-no-renegotiation-server-by-server
test-12 = 12-no-renegotiation-client-by-server
test-13 = 13-no-renegotiation-client-by-client
# ===========================================================

[0-renegotiate-client-no-resume]
ssl_conf = 0-renegotiate-client-no-resume-ssl

[0-renegotiate-client-no-resume-ssl]
server = 0-renegotiate-client-no-resume-server
client = 0-renegotiate-client-no-resume-client

[0-renegotiate-client-no-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-renegotiate-client-no-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No


# ===========================================================

[1-renegotiate-client-resume]
ssl_conf = 1-renegotiate-client-resume-ssl

[1-renegotiate-client-resume-ssl]
server = 1-renegotiate-client-resume-server
client = 1-renegotiate-client-resume-client

[1-renegotiate-client-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-renegotiate-client-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = Yes


# ===========================================================

[2-renegotiate-server-no-resume]
ssl_conf = 2-renegotiate-server-no-resume-ssl

[2-renegotiate-server-no-resume-ssl]
server = 2-renegotiate-server-no-resume-server
client = 2-renegotiate-server-no-resume-client

[2-renegotiate-server-no-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-renegotiate-server-no-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = No


# ===========================================================

[3-renegotiate-server-resume]
ssl_conf = 3-renegotiate-server-resume-ssl

[3-renegotiate-server-resume-ssl]
server = 3-renegotiate-server-resume-server
client = 3-renegotiate-server-resume-client

[3-renegotiate-server-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-renegotiate-server-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = Yes


# ===========================================================

[4-renegotiate-client-auth-require]
ssl_conf = 4-renegotiate-client-auth-require-ssl

[4-renegotiate-client-auth-require-ssl]
server = 4-renegotiate-client-auth-require-server
client = 4-renegotiate-client-auth-require-client

[4-renegotiate-client-auth-require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[4-renegotiate-client-auth-require-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = No


# ===========================================================

[5-renegotiate-client-auth-once]
ssl_conf = 5-renegotiate-client-auth-once-ssl

[5-renegotiate-client-auth-once-ssl]
server = 5-renegotiate-client-auth-once-server
client = 5-renegotiate-client-auth-once-client

[5-renegotiate-client-auth-once-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Once

[5-renegotiate-client-auth-once-client]
Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = No


# ===========================================================

[6-renegotiate-aead-to-non-aead]
ssl_conf = 6-renegotiate-aead-to-non-aead-ssl

[6-renegotiate-aead-to-non-aead-ssl]
server = 6-renegotiate-aead-to-non-aead-server
client = 6-renegotiate-aead-to-non-aead-client

[6-renegotiate-aead-to-non-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-renegotiate-aead-to-non-aead-client]
CipherString = AES128-GCM-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No
client = 6-renegotiate-aead-to-non-aead-client-extra

[6-renegotiate-aead-to-non-aead-client-extra]
RenegotiateCiphers = AES128-SHA


# ===========================================================

[7-renegotiate-non-aead-to-aead]
ssl_conf = 7-renegotiate-non-aead-to-aead-ssl

[7-renegotiate-non-aead-to-aead-ssl]
server = 7-renegotiate-non-aead-to-aead-server
client = 7-renegotiate-non-aead-to-aead-client

[7-renegotiate-non-aead-to-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-renegotiate-non-aead-to-aead-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No
client = 7-renegotiate-non-aead-to-aead-client-extra

[7-renegotiate-non-aead-to-aead-client-extra]
RenegotiateCiphers = AES128-GCM-SHA256


# ===========================================================

[8-renegotiate-non-aead-to-non-aead]
ssl_conf = 8-renegotiate-non-aead-to-non-aead-ssl

[8-renegotiate-non-aead-to-non-aead-ssl]
server = 8-renegotiate-non-aead-to-non-aead-server
client = 8-renegotiate-non-aead-to-non-aead-client

[8-renegotiate-non-aead-to-non-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-renegotiate-non-aead-to-non-aead-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No
client = 8-renegotiate-non-aead-to-non-aead-client-extra

[8-renegotiate-non-aead-to-non-aead-client-extra]
RenegotiateCiphers = AES256-SHA


# ===========================================================

[9-renegotiate-aead-to-aead]
ssl_conf = 9-renegotiate-aead-to-aead-ssl

[9-renegotiate-aead-to-aead-ssl]
server = 9-renegotiate-aead-to-aead-server
client = 9-renegotiate-aead-to-aead-client

[9-renegotiate-aead-to-aead-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = NoResumptionOnRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-renegotiate-aead-to-aead-client]
CipherString = AES128-GCM-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = Success
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No
client = 9-renegotiate-aead-to-aead-client-extra

[9-renegotiate-aead-to-aead-client-extra]
RenegotiateCiphers = AES256-GCM-SHA384


# ===========================================================

[10-no-renegotiation-server-by-client]
ssl_conf = 10-no-renegotiation-server-by-client-ssl

[10-no-renegotiation-server-by-client-ssl]
server = 10-no-renegotiation-server-by-client-server
client = 10-no-renegotiation-server-by-client-client

[10-no-renegotiation-server-by-client-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-no-renegotiation-server-by-client-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = ClientFail
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No


# ===========================================================

[11-no-renegotiation-server-by-server]
ssl_conf = 11-no-renegotiation-server-by-server-ssl

[11-no-renegotiation-server-by-server-ssl]
server = 11-no-renegotiation-server-by-server-server
client = 11-no-renegotiation-server-by-server-client

[11-no-renegotiation-server-by-server-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
Options = NoRenegotiation
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-no-renegotiation-server-by-server-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = ServerFail
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = No


# ===========================================================

[12-no-renegotiation-client-by-server]
ssl_conf = 12-no-renegotiation-client-by-server-ssl

[12-no-renegotiation-client-by-server-ssl]
server = 12-no-renegotiation-client-by-server-server
client = 12-no-renegotiation-client-by-server-client

[12-no-renegotiation-client-by-server-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-no-renegotiation-client-by-server-client]
CipherString = DEFAULT
Options = NoRenegotiation
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = ServerFail
HandshakeMode = RenegotiateServer
Method = TLS
ResumptionExpected = No


# ===========================================================

[13-no-renegotiation-client-by-client]
ssl_conf = 13-no-renegotiation-client-by-client-ssl

[13-no-renegotiation-client-by-client-ssl]
server = 13-no-renegotiation-client-by-client-server
client = 13-no-renegotiation-client-by-client-client

[13-no-renegotiation-client-by-client-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-no-renegotiation-client-by-client-client]
CipherString = DEFAULT
Options = NoRenegotiation
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = ClientFail
HandshakeMode = RenegotiateClient
Method = TLS
ResumptionExpected = No


