name: "CodeQL"

env:
  OPENSSL_BRANCH: openssl-3.0
  #RPATH: "-Wl,-rpath=${PREFIX}/lib"
  #PREFIX: ${HOME}/opt
  #PATH: ${PREFIX}/bin:${PATH}

on:
  push:
    branches: [master, ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [master]
  schedule:
    - cron: '0 2 * * 0'

jobs:
  analyse:
    name: Analyse
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2
      with:
        # We must fetch at least the immediate parents so that if this is
        # a pull request then we can checkout the head.
        fetch-depth: 2
        # gost-engine has submodules
        submodules: true

    # If this run was triggered by a pull request event, then checkout
    # the head of the pull request instead of the merge commit.
    - run: git checkout HEAD^2
      if: ${{ github.event_name == 'pull_request' }}

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v1
      # languages: cpp
      # Override language selection by uncommenting this and choosing your languages
      # with:

    # Autobuild attempts to build any compiled languages  (C/C++, C#, or Java).
    # If this step fails, then you should remove it and run the build manually (see below)
    #- name: Autobuild
    #  uses: github/codeql-action/autobuild@v1

    # ℹ️ Command-line programs to run using the OS shell.
    # 📚 https://git.io/JvXDl

    # ✏️ If the Autobuild fails above, remove it and uncomment the following three lines
    #    and modify them (or add more) to build your code if your project
    #    uses a compiled language

    - run: |
       curl -L https://cpanmin.us | sudo perl - --sudo App::cpanminus
       sudo cpanm --notest Test2::V0 > build.log 2>&1 || (cat build.log && exit 1)
       if [ "$APT_INSTALL" ]; then sudo apt-get install -y $APT_INSTALL; fi
       git clone --depth 1 -b ${OPENSSL_BRANCH} https://github.com/openssl/openssl.git
       export PREFIX=`pwd`/opt
       export RPATH="-Wl,-rpath=${PREFIX}/lib"
       cd openssl
       git describe --always --long
       ./config shared -d --prefix=${PREFIX} --openssldir=${PREFIX} --libdir=lib ${RPATH}
       make -s build_libs
       make -s build_programs
       make -s install_sw
       cd ..
       set -e
       mkdir build
       cd build
       cmake -DOPENSSL_ROOT_DIR=${PREFIX} -DOPENSSL_ENGINES_DIR=${PREFIX}/engines ${ASAN} ..
       make

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v1
