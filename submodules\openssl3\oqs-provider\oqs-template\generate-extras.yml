# These fields for 'name_group' are added in addition to the ones specified in generate.yml
kem-extras:
  frodo640aes:
    nid_ecx_hybrid: '0x2F80'
  frodo640shake:
    nid_ecx_hybrid: '0x2F81'
  frodo976aes:
    nid_ecx_hybrid: '0x2F82'
  frodo976shake:
    nid_ecx_hybrid: '0x2F83'
  kyber768:
    nid_ecx_hybrid: '0x2F90'
  ntru_hps2048509:
    nid_ecx_hybrid: '0x2F94'
  ntru_hps2048677:
    nid_ecx_hybrid: '0x2F95'
  ntru_hrss701:
    nid_ecx_hybrid: '0x2F97'
  lightsaber:
    nid_ecx_hybrid: '0x2F98'
  saber:
    nid_ecx_hybrid: '0x2F99'
  sidhp434:
    nid_ecx_hybrid: '0x2F9B'
  sidhp503:
    nid_ecx_hybrid: '0x2F9C'
  sidhp610:
    nid_ecx_hybrid: '0x2F9D'
  sikep503:
    nid_ecx_hybrid: '0x2FA0'
  sikep610:
    nid_ecx_hybrid: '0x2FA1'
  kyber90s512:
    nid_ecx_hybrid: '0x2FA9'
  kyber90s768:
    nid_ecx_hybrid: '0x2FAA'
  hqc128:
    nid_ecx_hybrid: '0x2FAC'
  hqc192:
    nid_ecx_hybrid: '0x2FAD'
  ntrulpr653:
    nid_ecx_hybrid: '0x2FAF'
  ntrulpr761:
    nid_ecx_hybrid: '0x2FB0'
  ntrulpr857:
    nid_ecx_hybrid: '0x2FB1'
  sntrup653:
    nid_ecx_hybrid: '0x2FB2'
  sntrup761:
    nid_ecx_hybrid: '0x2FB3'
  sntrup857:
    nid_ecx_hybrid: '0x2FB4'
