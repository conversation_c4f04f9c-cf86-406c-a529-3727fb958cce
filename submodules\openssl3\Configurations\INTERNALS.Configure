Configure Internals
===================

[ note: this file uses markdown for formatting ]

Intro
-----

This is a collection of notes that are hopefully of interest to those
who decide to dive into Configure and what it does.  This is a living
document and anyone is encouraged to add to it and submit changes.
There's no claim for this document to be complete at any time, but it
will hopefully reach such a point in time.


----------------------------------------------------------------------

Parsing build.info files, processing conditions
-----------------------------------------------

Processing conditions in build.info files is done with the help of a
condition stack that tell if a build.info should be processed or if it
should just be skipped over.  The possible states of the stack top are
expressed in the following comment from Configure:

    # The top item of this stack has the following values
    # -2 positive already run and we found ELSE (following ELSIF should fail)
    # -1 positive already run (skip until ENDIF)
    # 0 negatives so far (if we're at a condition, check it)
    # 1 last was positive (don't skip lines until next ELSE, ELSIF or ENDIF)
    # 2 positive ELSE (following ELSIF should fail)

Ground rule is that non-condition lines are skipped over if the
stack top is > 0.  Condition lines (IF, ELSIF, ELSE and ENDIF
statements) need to be processed either way to keep track of the skip
stack states, so they are a little more intricate.

Instead of trying to describe in words, here are some example of what
the skip stack should look like after each line is processed:

Example 1:

| IF[1]                     |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
|   IF[1]                   |  1  1     |                               |
|     ... whatever ...      |           | this line is processed        |
|   ELSIF[1]                |  1 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  1 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
| ELSIF[1]                  | -1        |                               |
|   ... whatever ...        |           | this line is skipped over     |
|   IF[1]                   | -1 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[1]                | -1 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    | -1 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   | -1        |                               |
|   ... whatever ...        |           | this line is skipped over     |
| ENDIF                     |           |                               |

Example 2:

| IF[0]                     |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
|   IF[1]                   |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[1]                |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  0 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
| ELSIF[1]                  |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
|   IF[1]                   |  1  1     |                               |
|     ... whatever ...      |           | this line is processed        |
|   ELSIF[1]                |  1 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  1 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
| ENDIF                     |           |                               |

Example 3:

| IF[0]                     |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
|   IF[0]                   |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[1]                |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  0 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
| ELSIF[1]                  |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
|   IF[0]                   |  1  0     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[1]                |  1  1     |                               |
|     ... whatever ...      |           | this line is processed        |
|   ELSE                    |  1 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
| ENDIF                     |           |                               |

Example 4:

| IF[0]                     |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
|   IF[0]                   |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[0]                |  0 -1     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  0 -2     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ENDIF                   |  0        |                               |
|   ... whatever ...        |           | this line is skipped over     |
| ELSIF[1]                  |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
|   IF[0]                   |  1  0     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSIF[0]                |  1  0     |                               |
|     ... whatever ...      |           | this line is skipped over     |
|   ELSE                    |  1  2     |                               |
|     ... whatever ...      |           | this line is processed        |
|   ENDIF                   |  1        |                               |
|   ... whatever ...        |           | this line is processed        |
| ENDIF                     |           |                               |

