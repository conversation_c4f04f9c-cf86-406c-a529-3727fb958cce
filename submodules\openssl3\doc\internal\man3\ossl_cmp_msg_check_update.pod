=pod

=head1 NAME

ossl_cmp_allow_unprotected_cb_t,
ossl_cmp_msg_check_update
- generic checks on a received CMP message, updating the context

=head1 SYNOPSIS

 #include "cmp_local.h"

 typedef int (*ossl_cmp_allow_unprotected_cb_t)(const OSSL_CMP_CTX *ctx,
                                                const OSSL_CMP_MSG *msg,
                                                int invalid_protection, int arg);

 int ossl_cmp_msg_check_update(OSSL_CMP_CTX *ctx, const OSSL_CMP_MSG *msg,
                               ossl_cmp_allow_unprotected_cb_t cb, int cb_arg);

=head1 DESCRIPTION

ossl_cmp_msg_check_update() does all generic checks on the given message B<msg>,
which may be a server response or a request by some client,
and updates the B<ctx> accordingly.

The B<msg> is checked for the following:

=over 4

=item its sender is of appropriate type (currently only B<X509_NAME>)
      and matches any expected sender or srvCert subject given in B<ctx>,

=item its protection is present and valid (or a callback function B<cb>
is present and indicates that a missing or invalid protection is acceptable),

=item its CMP protocol version is acceptable, namely B<OSSL_CMP_PVNO>,

=item its body type is valid,

=item its transaction ID matches any transaction ID given in B<ctx>, and

=item its recipNonce matches any senderNonce given in B<ctx>.

=back

In case no protection is present and B<cb> is not NULL then this callback
function is called with its B<invalid_protection> parameter being 0, while in
case an invalid protection is present the B<invalid_protection> parameter is 1.
The callback is passed also the arguments B<ctx>, B<msg>, and <cb_arg>
(which typically contains the expected message type).
The callback should return 1 on acceptance, 0 on rejection, or -1 on error.
It should not put an error on the error stack since this could be misleading.

ossl_cmp_msg_check_update() adds all extraCerts contained in the <msg> to
the list of untrusted certificates in B<ctx> such that they are already usable
for OSSL_CMP_validate_msg(), which is called internally, and for future use.
Thus they are available also to the certificate confirmation callback, and the
peer does not need to send them again (at least not in the same transaction).
Note that it does not help validating the message before storing the extraCerts
because they are not part of the protected portion of the message anyway.
For efficiency, the extraCerts are prepended to the list so they get used first.

If all checks pass then ossl_cmp_msg_check_update()
records in B<ctx> the senderNonce of the received message as the new recipNonce
and learns the transaction ID if none is currently present in B<ctx>.

Moreover, according to RFC 4210 section 5.3.2, if the message protection is
PBM-based then any certificates in the caPubs field are added to the list of
trusted certificates (if set via L<OSSL_CMP_CTX_set0_trustedStore(3)>).
This way these certs are available for validating subsequent messages in the
same context and could apply to any Polling Response (pollRep), error, or PKI
Confirmation (PKIConf) messages following in the same or future transactions.

=head1 RETURN VALUES

ossl_cmp_msg_check_update() returns 1 on success, -1 on error.

=head1 SEE ALSO

L<OSSL_CMP_validate_msg(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
