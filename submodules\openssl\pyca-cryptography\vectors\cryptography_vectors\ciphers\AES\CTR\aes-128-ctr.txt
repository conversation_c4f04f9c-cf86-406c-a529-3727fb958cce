# AES Counter test vectors from RFC 3686

[ENCRYPT]

COUNT = 0
KEY = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
PLAINTEXT = 53696E676C6520626C6F636B206D7367
CIPHERTEXT = E4095D4FB7A7B3792D6175A3261311B8

COUNT = 1
KEY = 7E24067817FAE0D743D6CE1F32539163
IV = 006CB6DBC0543B59DA48D90B00000001
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
CIPHERTEXT = 5104A106168A72D9790D41EE8EDAD388EB2E1EFC46DA57C8FCE630DF9141BE28

COUNT = 2
KEY = 7691BE035E5020A8AC6E618529F9A0DC
IV = 00E0017B27777F3F4A1786F000000001
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
CIPHERTEXT = C1CF48A89F2FFDD9CF4652E9EFDB72D74540A42BDE6D7836D59A5CEAAEF3105325B2072F
