=pod

=head1 NAME

BN_swap - exchange BIGNUMs

=head1 SYNOPSIS

 #include <openssl/bn.h>

 void BN_swap(BIGNUM *a, BIGNUM *b);

=head1 DESCRIPTION

BN_swap() exchanges the values of I<a> and I<b>.

=head1 RETURN VALUES

BN_swap() does not return a value.

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
