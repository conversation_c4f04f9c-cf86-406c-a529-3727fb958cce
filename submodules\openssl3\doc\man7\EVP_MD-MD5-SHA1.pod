=pod

=head1 NAME

EVP_MD-MD5-SHA1 - The MD5-SHA1 EVP_MD implementation

=head1 DESCRIPTION

Support for computing MD5-SHA1 digests through the B<EVP_MD> API.

MD5-SHA1 is a rather special digest that's used with SSLv3.

=head2 Identity

This implementation is only available with the default provider, and is
identified with the name "MD5-SHA1".

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head2 Settable Context Parameters

This implementation supports the following L<OSSL_PARAM(3)> entries,
settable for an B<EVP_MD_CTX> with L<EVP_MD_CTX_set_params(3)>:

=over 4

=item "ssl3-ms" (B<OSSL_DIGEST_PARAM_SSL3_MS>) <octet string>

This parameter is set by libssl in order to calculate a signature hash for an
SSLv3 CertificateVerify message as per RFC6101.
It is only set after all handshake messages have already been digested via
OP_digest_update() calls.
The parameter provides the master secret value to be added to the digest.
The digest implementation should calculate the complete digest as per RFC6101
section 5.6.8.
The next call after setting this parameter should be OP_digest_final().

=back

=head1 SEE ALSO

L<EVP_MD_CTX_set_params(3)>, L<provider-digest(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
