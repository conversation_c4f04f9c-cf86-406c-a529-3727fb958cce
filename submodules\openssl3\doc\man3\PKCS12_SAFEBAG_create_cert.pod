=pod

=head1 NAME

PKCS12_SAFEBAG_create_cert, PKCS12_SAFEBAG_create_crl,
PKCS12_SAFEBAG_create_secret, PKCS12_SAFEBAG_create0_p8inf,
PKCS12_SAFEBAG_create0_pkcs8, P<PERSON><PERSON>12_SAFEBAG_create_pkcs8_encrypt,
PKCS12_SAFEBAG_create_pkcs8_encrypt_ex - Create PKCS#12 safeBag objects

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_cert(X509 *x509);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_crl(X509_CRL *crl);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_secret(int type, int vtype,
                                              const unsigned char* value,
                                              int len);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_p8inf(PKCS8_PRIV_KEY_INFO *p8);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_pkcs8(X509_SIG *p8);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt(int pbe_nid,
                                                     const char *pass,
                                                     int passlen,
                                                     unsigned char *salt,
                                                     int saltlen, int iter,
                                                     PKCS8_PRIV_KEY_INFO *p8inf);
 PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt_ex(int pbe_nid,
                                                        const char *pass,
                                                        int passlen,
                                                        unsigned char *salt,
                                                        int saltlen, int iter,
                                                        PKCS8_PRIV_KEY_INFO *p8inf,
                                                        OSSL_LIB_CTX *ctx,
                                                        const char *propq);

=head1 DESCRIPTION

PKCS12_SAFEBAG_create_cert() creates a new B<PKCS12_SAFEBAG> of type B<NID_certBag>
containing the supplied certificate.

PKCS12_SAFEBAG_create_crl() creates a new B<PKCS12_SAFEBAG> of type B<NID_crlBag>
containing the supplied crl.

PKCS12_SAFEBAG_create_secret() creates a new B<PKCS12_SAFEBAG> of type
corresponding to a PKCS#12 B<secretBag>. The B<secretBag> contents are tagged as
I<type> with an ASN1 value of type I<vtype> constructed using the bytes in
I<value> of length I<len>.

PKCS12_SAFEBAG_create0_p8inf() creates a new B<PKCS12_SAFEBAG> of type B<NID_keyBag>
containing the supplied PKCS8 structure.

PKCS12_SAFEBAG_create0_pkcs8() creates a new B<PKCS12_SAFEBAG> of type
B<NID_pkcs8ShroudedKeyBag> containing the supplied PKCS8 structure.

PKCS12_SAFEBAG_create_pkcs8_encrypt() creates a new B<PKCS12_SAFEBAG> of type
B<NID_pkcs8ShroudedKeyBag> by encrypting the supplied PKCS8 I<p8inf>.
If I<pbe_nid> is 0, a default encryption algorithm is used. I<pass> is the
passphrase and I<iter> is the iteration count. If I<iter> is zero then a default
value of 2048 is used. If I<salt> is NULL then a salt is generated randomly.

PKCS12_SAFEBAG_create_pkcs8_encrypt_ex() is identical to PKCS12_SAFEBAG_create_pkcs8_encrypt()
but allows for a library context I<ctx> and property query I<propq> to be used to select
algorithm implementations.

=head1 NOTES

PKCS12_SAFEBAG_create_pkcs8_encrypt() makes assumptions regarding the encoding of the given pass
phrase.
See L<passphrase-encoding(7)> for more information.

PKCS12_SAFEBAG_create_secret() was added in OpenSSL 3.0.

=head1 RETURN VALUES

All of these functions return a valid B<PKCS12_SAFEBAG> structure or NULL if an error occurred.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<PKCS12_create(3)>,
L<PKCS12_add_safe(3)>,
L<PKCS12_add_safes(3)>

=head1 HISTORY

PKCS12_SAFEBAG_create_pkcs8_encrypt_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
