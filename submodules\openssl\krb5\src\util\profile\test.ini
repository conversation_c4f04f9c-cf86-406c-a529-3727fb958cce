this is a comment.  Everything up to the first square brace is ignored.

[test section 2]
	test_child = "foo\nbar"
	child_section2 = "one"
	child_section2 = {
		child = slick
		child = harry
		child = "john\tb "
	}
	child_section2 = foo

[test section 2]
	child_section2 = {
		child = ron
		chores = cleaning
	}

[realms]
ATHENA.MIT.EDU = {
	server = KERBEROS.MIT.EDU:88
	server = KERBEROS1.MIT.EDU
	server = KERBEROS2.MIT.EDU
	admin = KERBEROS.MIT.EDU
	etype = DES-MD5
}
	


[test section 1]
    foo = "bar "

[test section 2]
	quux = "bar"
	frep = bar
	kappa = alpha
	beta = epsilon

[test section 1]
    bar = foo
	foo = bar2
    quux = zap
	foo = bar3
	child_section = {
		child = slick
		child = harry
		child = john
	}
	child_section = foo




