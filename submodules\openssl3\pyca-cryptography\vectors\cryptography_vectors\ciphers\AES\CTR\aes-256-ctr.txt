# AES Counter test vectors from RFC 3686

[ENCRYPT]

COUNT = 0
KEY = 776BEFF2851DB06F4C8A0542C8696F6C6A81AF1EEC96B4D37FC1D689E6C1C104
IV = 00000060DB5672C97AA8F0B200000001
PLAINTEXT = 53696E676C6520626C6F636B206D7367
CIPHERTEXT = 145AD01DBF824EC7560863DC71E3E0C0

COUNT = 1
KEY = F6D66D6BD52D59BB0796365879EFF886C66DD51A5B6A99744B50590C87A23884
IV = 00FAAC24C1585EF15A43D87500000001
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
CIPHERTEXT = F05E231B3894612C49EE000B804EB2A9B8306B508F839D6A5530831D9344AF1C

COUNT = 2
KEY = FF7A617CE69148E4F1726E2F43581DE2AA62D9F805532EDFF1EED687FB54153D
IV = 001CC5B751A51D70A1C1114800000001
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
CIPHERTEXT = EB6C52821D0BBBF7CE7594462ACA4FAAB407DF866569FD07F48CC0B583D6071F1EC0E6B8
