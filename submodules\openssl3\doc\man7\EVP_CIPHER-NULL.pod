=pod

=head1 NAME

EVP_CIPHER-NULL - The NULL EVP_CIPHER implementation

=head1 DESCRIPTION

Support for a NULL symmetric encryption using the B<EVP_CIPHER> API.
This is used when the TLS cipher suite is TLS_NULL_WITH_NULL_NULL.
This does no encryption (just copies the data) and has a mac size of zero.

=head2 Algorithm Name

The following algorithm is available in the default provider:

=over 4

=item "NULL"

=back

=head2 Parameters

This implementation supports the following parameters:

=head3 Gettable EVP_CIPHER parameters

See L<EVP_EncryptInit(3)/Gettable EVP_CIPHER parameters>

=head3 Gettable EVP_CIPHER_CTX parameters

=over 4

=item "keylen" (B<OSSL_CIPHER_PARAM_KEYLEN>) <unsigned integer>

=item "ivlen" (B<OSSL_CIPHER_PARAM_IVLEN> and <B<OSSL_CIPHER_PARAM_AEAD_IVLEN>) <unsigned integer>

=item "tls-mac" (B<OSSL_CIPHER_PARAM_TLS_MAC>) <octet ptr>

=back

See L<EVP_EncryptInit(3)/PARAMETERS> for further information.

=head3 Settable EVP_CIPHER_CTX parameters

=over 4

=item "tls-mac-size" (B<OSSL_CIPHER_PARAM_TLS_MAC_SIZE>) <unsigned integer>

=back

See L<EVP_EncryptInit(3)/PARAMETERS> for further information.

=head1 CONFORMING TO

RFC 5246 section-*******

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
