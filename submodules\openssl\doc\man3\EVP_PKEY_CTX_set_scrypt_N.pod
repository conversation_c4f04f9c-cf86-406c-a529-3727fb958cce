=pod

=head1 NAME

EVP_PKEY_CTX_set1_scrypt_salt,
EVP_PKEY_CTX_set_scrypt_N,
EVP_PKEY_CTX_set_scrypt_r,
EVP_PKEY_CTX_set_scrypt_p,
E<PERSON>_PKEY_CTX_set_scrypt_maxmem_bytes
- EVP_PKEY scrypt KDF support functions

=head1 SYNOPSIS

 #include <openssl/kdf.h>

 int EVP_PKEY_CTX_set1_scrypt_salt(EVP_PKEY_CTX *pctx, unsigned char *salt,
                                   int saltlen);

 int EVP_PKEY_CTX_set_scrypt_N(EVP_PKEY_CTX *pctx, uint64_t N);

 int EVP_PKEY_CTX_set_scrypt_r(EVP_PKEY_CTX *pctx, uint64_t r);

 int EVP_PKEY_CTX_set_scrypt_p(EVP_PKEY_CTX *pctx, uint64_t p);

 int EVP_PKEY_CTX_set_scrypt_maxmem_bytes(EVP_PKEY_CTX *pctx,
                                          uint64_t maxmem);

=head1 DESCRIPTION

These functions are used to set up the necessary data to use the
scrypt KDF.
For more information on scrypt, see L<scrypt(7)>.

EVP_PKEY_CTX_set1_scrypt_salt() sets the B<saltlen> bytes long salt
value.

EVP_PKEY_CTX_set_scrypt_N(), EVP_PKEY_CTX_set_scrypt_r() and
EVP_PKEY_CTX_set_scrypt_p() configure the work factors N, r and p.

EVP_PKEY_CTX_set_scrypt_maxmem_bytes() sets how much RAM key
derivation may maximally use, given in bytes.
If RAM is exceeded because the load factors are chosen too high, the
key derivation will fail.

=head1 STRING CTRLS

scrypt also supports string based control operations via
L<EVP_PKEY_CTX_ctrl_str(3)>.
Similarly, the B<salt> can either be specified using the B<type>
parameter "salt" or in hex encoding by using the "hexsalt" parameter.
The work factors B<N>, B<r> and B<p> as well as B<maxmem_bytes> can be
set by using the parameters "N", "r", "p" and "maxmem_bytes",
respectively.

=head1 NOTES

The scrypt KDF also uses EVP_PKEY_CTX_set1_pbe_pass() as well as
the value from the string controls "pass" and "hexpass".
See L<EVP_PKEY_CTX_set1_pbe_pass(3)>.

All the functions described here are implemented as macros.

=head1 RETURN VALUES

All these functions return 1 for success and 0 or a negative value for
failure.
In particular a return value of -2 indicates the operation is not
supported by the public key algorithm.

=head1 SEE ALSO

L<scrypt(7)>,
L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_CTX_ctrl_str(3)>,
L<EVP_PKEY_derive(3)>

=head1 COPYRIGHT

Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
