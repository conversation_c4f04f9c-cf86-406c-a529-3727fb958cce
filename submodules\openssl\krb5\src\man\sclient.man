.\" Man page generated from reStructuredText.
.
.TH "SCLIENT" "1" " " "1.17.1" "MIT Kerberos"
.SH NAME
sclient \- sample Kerberos version 5 client
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBsclient\fP \fIremotehost\fP
.SH DESCRIPTION
.sp
sclient is a sample application, primarily useful for testing
purposes.  It contacts a sample server sserver(8) and
authenticates to it using Kerberos version 5 tickets, then displays
the server\(aqs response.
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kinit(1), sserver(8), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2019, MIT
.\" Generated by docutils manpage writer.
.
