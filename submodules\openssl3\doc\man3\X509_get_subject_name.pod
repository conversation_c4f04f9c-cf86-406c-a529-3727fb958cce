=pod

=head1 NAME

X509_NAME_hash_ex, X509_NAME_hash,
X509_get_subject_name, X509_set_subject_name, X509_subject_name_hash,
X509_get_issuer_name, X509_set_issuer_name, X509_issuer_name_hash,
X509_REQ_get_subject_name, X509_REQ_set_subject_name,
X509_CRL_get_issuer, X509_CRL_set_issuer_name -
get X509_NAME hashes or get and set issuer or subject names

=head1 SYNOPSIS

 #include <openssl/x509.h>

 unsigned long X509_NAME_hash_ex(const X509_NAME *x, OSSL_LIB_CTX *libctx,
                                 const char *propq, int *ok);

 X509_NAME *X509_get_subject_name(const X509 *x);
 int X509_set_subject_name(X509 *x, const X509_NAME *name);
 unsigned long X509_subject_name_hash(X509 *x);

 X509_NAME *X509_get_issuer_name(const X509 *x);
 int X509_set_issuer_name(X509 *x, const X509_NAME *name);
 unsigned long X509_issuer_name_hash(X509 *x);

 X509_NAME *X509_REQ_get_subject_name(const X509_REQ *req);
 int X509_REQ_set_subject_name(X509_REQ *req, const X509_NAME *name);

 X509_NAME *X509_CRL_get_issuer(const X509_CRL *crl);
 int X509_CRL_set_issuer_name(X509_CRL *x, const X509_NAME *name);

The following macro has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 #define X509_NAME_hash(x) X509_NAME_hash_ex(x, NULL, NULL, NULL)

=head1 DESCRIPTION

X509_NAME_hash_ex() returns a hash value of name I<x> or 0 on failure,
using any given library context I<libctx> and property query I<propq>.
The I<ok> result argument may be NULL
or else is used to return 1 for success and 0 for failure.
Failure may happen on malloc error or if no SHA1 implementation is available.

X509_NAME_hash() returns a hash value of name I<x> or 0 on failure,
using the default library context and default property query.

X509_get_subject_name() returns the subject name of certificate I<x>. The
returned value is an internal pointer which B<MUST NOT> be freed.

X509_set_subject_name() sets the issuer name of certificate I<x> to
I<name>. The I<name> parameter is copied internally and should be freed
up when it is no longer needed.

X509_subject_name_hash() returns a hash value of the subject name of
certificate I<x>.

X509_get_issuer_name(), X509_set_issuer_name(), and X509_issuer_name_hash()
are identical to
X509_get_subject_name(), X509_set_subject_name(), and X509_subject_name_hash()
except they relate to the issuer name of I<x>.

Similarly X509_REQ_get_subject_name(), X509_REQ_set_subject_name(),
X509_CRL_get_issuer() and X509_CRL_set_issuer_name() get or set the subject
or issuer names of certificate requests of CRLs respectively.

=head1 RETURN VALUES

X509_get_subject_name(), X509_get_issuer_name(), X509_REQ_get_subject_name()
and X509_CRL_get_issuer() return an B<X509_NAME> pointer.

X509_NAME_hash_ex(), X509_NAME_hash(),
X509_subject_name_hash() and X509_issuer_name_hash()
return the first four bytes of the SHA1 hash value,
converted to B<unsigned long> in little endian order,
or 0 on failure.

X509_set_subject_name(), X509_set_issuer_name(), X509_REQ_set_subject_name()
and X509_CRL_set_issuer_name() return 1 for success and 0 for failure.

=head1 BUGS

In case X509_NAME_hash(), X509_subject_name_hash(), or X509_issuer_name_hash()
returns 0 it remains unclear if this is the real hash value or due to failure.
Better use X509_NAME_hash_ex() instead.

=head1 SEE ALSO

L<d2i_X509(3)>,
L<ERR_get_error(3)>, L<d2i_X509(3)>
L<X509_CRL_get0_by_serial(3)>,
L<X509_get0_signature(3)>,
L<X509_get_ext_d2i(3)>,
L<X509_get_extension_flags(3)>,
L<X509_get_pubkey(3)>,
L<X509_NAME_add_entry_by_txt(3)>,
L<X509_NAME_ENTRY_get_object(3)>,
L<X509_NAME_get_index_by_NID(3)>,
L<X509_NAME_print_ex(3)>,
L<X509_new(3)>,
L<X509_sign(3)>,
L<X509V3_get_d2i(3)>,
L<X509_verify_cert(3)>

=head1 HISTORY

X509_REQ_get_subject_name() is a function in OpenSSL 1.1.0 and a macro in
earlier versions.

X509_CRL_get_issuer() is a function in OpenSSL 1.1.0. It was previously
added in OpenSSL 1.0.0 as a macro.

X509_NAME_hash() was turned into a macro and deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
