{- use File::Spec::Functions;
   our $ex_inc = $withargs{fuzzer_include} &&
       (file_name_is_absolute($withargs{fuzzer_include}) ?
        $withargs{fuzzer_include} : catdir(updir(), $withargs{fuzzer_include}));
   our $ex_lib = $withargs{fuzzer_lib} &&
       (file_name_is_absolute($withargs{fuzzer_lib}) ?
        $withargs{fuzzer_lib} : catfile(updir(), $withargs{fuzzer_lib}));
   ""
-}

IF[{- !$disabled{"fuzz-afl"} || !$disabled{"fuzz-libfuzzer"} -}]
  PROGRAMS{noinst}=asn1 asn1parse bignum bndiv client conf crl server
  PROGRAMS{noinst}=punycode

  IF[{- !$disabled{"cmp"} -}]
    PROGRAMS{noinst}=cmp
  ENDIF

  IF[{- !$disabled{"cms"} -}]
    PROGRAMS{noinst}=cms
  ENDIF

  IF[{- !$disabled{"ct"} -}]
    PROGRAMS{noinst}=ct
  ENDIF

  IF[{- !$disabled{"ocsp"} -}]
    PROGRAMS{noinst}=x509
  ENDIF

  SOURCE[asn1]=asn1.c driver.c fuzz_rand.c
  INCLUDE[asn1]=../include {- $ex_inc -}
  DEPEND[asn1]=../libcrypto ../libssl {- $ex_lib -}

  SOURCE[asn1parse]=asn1parse.c driver.c
  INCLUDE[asn1parse]=../include {- $ex_inc -}
  DEPEND[asn1parse]=../libcrypto {- $ex_lib -}

  SOURCE[bignum]=bignum.c driver.c
  INCLUDE[bignum]=../include {- $ex_inc -}
  DEPEND[bignum]=../libcrypto {- $ex_lib -}

  SOURCE[bndiv]=bndiv.c driver.c
  INCLUDE[bndiv]=../include {- $ex_inc -}
  DEPEND[bndiv]=../libcrypto {- $ex_lib -}

  SOURCE[client]=client.c driver.c fuzz_rand.c
  INCLUDE[client]=../include {- $ex_inc -}
  DEPEND[client]=../libcrypto ../libssl {- $ex_lib -}

  SOURCE[cmp]=cmp.c driver.c fuzz_rand.c
  INCLUDE[cmp]=../include {- $ex_inc -}
  DEPEND[cmp]=../libcrypto {- $ex_lib -}

  SOURCE[cms]=cms.c driver.c
  INCLUDE[cms]=../include {- $ex_inc -}
  DEPEND[cms]=../libcrypto {- $ex_lib -}

  SOURCE[conf]=conf.c driver.c
  INCLUDE[conf]=../include {- $ex_inc -}
  DEPEND[conf]=../libcrypto {- $ex_lib -}

  SOURCE[crl]=crl.c driver.c
  INCLUDE[crl]=../include {- $ex_inc -}
  DEPEND[crl]=../libcrypto {- $ex_lib -}

  SOURCE[ct]=ct.c driver.c
  INCLUDE[ct]=../include {- $ex_inc -}
  DEPEND[ct]=../libcrypto {- $ex_lib -}

  SOURCE[punycode]=punycode.c driver.c
  INCLUDE[punycode]=../include {- $ex_inc -}
  DEPEND[punycode]=../libcrypto.a {- $ex_lib -}

  SOURCE[server]=server.c driver.c fuzz_rand.c
  INCLUDE[server]=../include {- $ex_inc -}
  DEPEND[server]=../libcrypto ../libssl {- $ex_lib -}

  SOURCE[x509]=x509.c driver.c fuzz_rand.c
  INCLUDE[x509]=../include {- $ex_inc -}
  DEPEND[x509]=../libcrypto {- $ex_lib -}
ENDIF

IF[{- !$disabled{tests} -}]
  PROGRAMS{noinst}=asn1-test asn1parse-test bignum-test bndiv-test client-test conf-test crl-test server-test
  PROGRAMS{noinst}=punycode-test

  IF[{- !$disabled{"cmp"} -}]
    PROGRAMS{noinst}=cmp-test
  ENDIF

  IF[{- !$disabled{"cms"} -}]
    PROGRAMS{noinst}=cms-test
  ENDIF

  IF[{- !$disabled{"ct"} -}]
    PROGRAMS{noinst}=ct-test
  ENDIF

  IF[{- !$disabled{"ocsp"} -}]
    PROGRAMS{noinst}=x509-test
  ENDIF

  SOURCE[asn1-test]=asn1.c test-corpus.c fuzz_rand.c
  INCLUDE[asn1-test]=../include
  DEPEND[asn1-test]=../libcrypto ../libssl

  SOURCE[asn1parse-test]=asn1parse.c test-corpus.c
  INCLUDE[asn1parse-test]=../include
  DEPEND[asn1parse-test]=../libcrypto

  SOURCE[bignum-test]=bignum.c test-corpus.c
  INCLUDE[bignum-test]=../include
  DEPEND[bignum-test]=../libcrypto

  SOURCE[bndiv-test]=bndiv.c test-corpus.c
  INCLUDE[bndiv-test]=../include
  DEPEND[bndiv-test]=../libcrypto

  SOURCE[client-test]=client.c test-corpus.c fuzz_rand.c
  INCLUDE[client-test]=../include
  DEPEND[client-test]=../libcrypto ../libssl

  SOURCE[cmp-test]=cmp.c test-corpus.c fuzz_rand.c
  INCLUDE[cmp-test]=../include
  DEPEND[cmp-test]=../libcrypto.a
  # referring to static lib allows using non-exported functions

  SOURCE[cms-test]=cms.c test-corpus.c
  INCLUDE[cms-test]=../include
  DEPEND[cms-test]=../libcrypto

  SOURCE[conf-test]=conf.c test-corpus.c
  INCLUDE[conf-test]=../include
  DEPEND[conf-test]=../libcrypto

  SOURCE[crl-test]=crl.c test-corpus.c
  INCLUDE[crl-test]=../include
  DEPEND[crl-test]=../libcrypto

  SOURCE[ct-test]=ct.c test-corpus.c
  INCLUDE[ct-test]=../include
  DEPEND[ct-test]=../libcrypto

  SOURCE[punycode-test]=punycode.c test-corpus.c
  INCLUDE[punycode-test]=../include
  DEPEND[punycode-test]=../libcrypto.a

  SOURCE[server-test]=server.c test-corpus.c fuzz_rand.c
  INCLUDE[server-test]=../include
  DEPEND[server-test]=../libcrypto ../libssl

  SOURCE[x509-test]=x509.c test-corpus.c fuzz_rand.c
  INCLUDE[x509-test]=../include
  DEPEND[x509-test]=../libcrypto
ENDIF
