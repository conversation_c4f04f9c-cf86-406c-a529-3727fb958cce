# PBKDF2 SHA1 vectors from https://www.ietf.org/rfc/rfc6070.txt

COUNT = 0
PASSWORD = password
SALT = salt
ITERATIONS = 1
LENGTH = 20
DERIVED_KEY = 0c60c80f961f0e71f3a9b524af6012062fe037a6


COUNT = 1
PASSWORD = password
SALT = salt
ITERATIONS = 2
LENGTH = 20
DERIVED_KEY = ea6c014dc72d6f8ccd1ed92ace1d41f0d8de8957


COUNT = 2
PASSWORD = password
SALT = salt
ITERATIONS = 4096
LENGTH = 20
DERIVED_KEY = 4b007901b765489abead49d926f721d065a429c1


COUNT = 3
PASSWORD = password
SALT = salt
ITERATIONS = 16777216
LENGTH = 20
DERIVED_KEY = eefe3d61cd4da4e4e9945b3d6ba2158c2634e984


COUNT = 4
PASSWORD = passwordPASSWORDpassword
SALT = saltSALTsaltSALTsaltSALTsaltSALTsalt
ITERATIONS = 4096
LENGTH = 25
DERIVED_KEY = 3d2eec4fe41c849b80c8d83662c0e44a8b291a964cf2f07038


COUNT = 5
PASSWORD = pass\0word
SALT = sa\0lt
ITERATIONS = 4096
LENGTH = 16
DERIVED_KEY = 56fa6aa75548099dcc37d7f03425e0c3
