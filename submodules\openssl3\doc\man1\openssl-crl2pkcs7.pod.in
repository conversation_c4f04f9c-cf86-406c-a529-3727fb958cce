=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-crl2pkcs7 - Create a PKCS#7 structure from a CRL and certificates

=head1 SYNOPSIS

B<openssl> B<crl2pkcs7>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-certfile> I<filename>]
[B<-nocrl>]
{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command takes an optional CRL and one or more
certificates and converts them into a PKCS#7 degenerate "certificates
only" structure.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>

The input format of the CRL; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The output format of the PKCS#7 object; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-in> I<filename>

This specifies the input filename to read a CRL from or standard input if this
option is not specified.

=item B<-out> I<filename>

Specifies the output filename to write the PKCS#7 structure to or standard
output by default.

=item B<-certfile> I<filename>

Specifies a filename containing one or more certificates in B<PEM> format.
All certificates in the file will be added to the PKCS#7 structure. This
option can be used more than once to read certificates from multiple
files.

=item B<-nocrl>

Normally a CRL is included in the output file. With this option no CRL is
included in the output file and a CRL is not read from the input file.

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 EXAMPLES

Create a PKCS#7 structure from a certificate and CRL:

 openssl crl2pkcs7 -in crl.pem -certfile cert.pem -out p7.pem

Creates a PKCS#7 structure in DER format with no CRL from several
different certificates:

 openssl crl2pkcs7 -nocrl -certfile newcert.pem
        -certfile demoCA/cacert.pem -outform DER -out p7.der

=head1 NOTES

The output file is a PKCS#7 signed data structure containing no signers and
just certificates and an optional CRL.

This command can be used to send certificates and CAs to Netscape as part of
the certificate enrollment process. This involves sending the DER encoded output
as MIME type application/x-x509-user-cert.

The B<PEM> encoded form with the header and footer lines removed can be used to
install user certificates and CAs in MSIE using the Xenroll control.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkcs7(1)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
