#!/usr/bin/tclsh

lappend auto_path [file dirname [info script]]
package require ossltest
file delete -force $::test::dir/tc26_cms
file copy -force tc26_cms $::test::dir
cd $::test::dir
set plain_str [getFile ./tc26_cms/plain/text_decrypted.plain]

start_tests "CMS tests, TC26 examples"

#BUILD_AT=obj_mid.l64/ ./openssl_wrap.sh cms -verify -in ../standalone-test/tc26_cms/signed_a111.pem -inform PEM -noverify
#BUILD_AT=obj_mid.l64/ ./openssl_wrap.sh cms -verify -in ../standalone-test/tc26_cms/signed_a121.pem -inform PEM -noverify

test "Signed data, 512 bits, signed attributes" {
	grep "Verification successful" [openssl "cms -verify -in tc26_cms/signed_a111.pem -inform PEM -noverify"]
} 0 "CMS Verification successful
"

test "Signed data, 256 bits, no signed attributes" {
	grep "Verification successful" [openssl "cms -verify -in tc26_cms/signed_a121.pem -inform PEM -noverify"]
} 0 "CMS Verification successful
"

test "Digested data, 256 bits" {
	grep "Verification successful" [openssl "cms -digest_verify -in tc26_cms/hashed_a311.pem -inform PEM -out hashed_a311.out"]
} 0 "Verification successful
"

test "Digested data, 512 bits" {
	grep "Verification successful" [openssl "cms -digest_verify -in tc26_cms/hashed_a321.pem -inform PEM -out hashed_a321.out"]
} 0 "Verification successful
"

test "CMS decrypt, Kuznyechik ACPKM+OMAC, KeyAgreement" {
	grep "Enveloped" [openssl "cms -decrypt -debug_decrypt -in ./tc26_cms/encrypted_keyagree_a211.pem -inform PEM -inkey ./tc26_cms/recipient512_key.pem -recip ./tc26_cms/recipient512_cert.pem -originator ./tc26_cms/sender512_cert.pem"]
} 0 $plain_str

test "CMS decrypt, Magma ACPKM, KeyAgreement" {
	grep "Enveloped" [openssl "cms -decrypt -debug_decrypt -in ./tc26_cms/encrypted_keyagree_a221.pem -inform PEM -inkey ./tc26_cms/recipient256_key.pem -recip ./tc26_cms/recipient256_cert.pem -originator ./tc26_cms/sender256_cert.pem"]
} 0 $plain_str

test "CMS decrypt, Kuznyechik ACPKM, KeyTrans" {
	grep "Enveloped" [openssl "cms -decrypt -in ./tc26_cms/encrypted_keytrans_a231.pem -inform PEM -inkey ./tc26_cms/recipient256_key.pem"]
} 0 $plain_str

test "CMS decrypt, Magma ACPKM+OMAC, KeyTrans" {
	grep "Enveloped" [openssl "cms -decrypt -debug_decrypt -in ./tc26_cms/encrypted_keytrans_a241.pem -inform PEM -inkey ./tc26_cms/recipient512_key.pem "]
} 0 $plain_str

end_tests
