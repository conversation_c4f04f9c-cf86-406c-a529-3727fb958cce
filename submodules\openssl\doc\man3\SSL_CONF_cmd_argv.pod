=pod

=head1 NAME

SSL_CONF_cmd_argv - SSL configuration command line processing

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CONF_cmd_argv(SSL_CONF_CTX *cctx, int *pargc, char ***pargv);

=head1 DESCRIPTION

The function SSL_CONF_cmd_argv() processes at most two command line
arguments from B<pargv> and B<pargc>. The values of B<pargv> and B<pargc>
are updated to reflect the number of command options processed. The B<pargc>
argument can be set to B<NULL> if it is not used.

=head1 RETURN VALUES

SSL_CONF_cmd_argv() returns the number of command arguments processed: 0, 1, 2
or a negative error code.

If -2 is returned then an argument for a command is missing.

If -1 is returned the command is recognised but couldn't be processed due
to an error: for example a syntax error in the argument.

=head1 SEE ALSO

L<SSL_CONF_CTX_new(3)>,
L<SSL_CONF_CTX_set_flags(3)>,
L<SSL_CONF_CTX_set1_prefix(3)>,
L<SSL_CONF_CTX_set_ssl_ctx(3)>,
L<SSL_CONF_cmd(3)>

=head1 HISTORY

These functions were added in OpenSSL 1.0.2.

=head1 COPYRIGHT

Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
