# makefile: Leash executable
#

EXE_NAME=leash

SUBDIRS= htmlhelp

OBJS=   \
	$(OUTPRE)Leash.obj \
	$(OUTPRE)LeashAboutBox.obj \
	$(OUTPRE)LeashDebugWindow.obj \
	$(OUTPRE)LeashDoc.obj \
	$(OUTPRE)LeashFrame.obj \
	$(OUTPRE)LeashMessageBox.obj \
	$(OUTPRE)LeashUIApplication.obj \
	$(OUTPRE)LeashUICommandHandler.obj \
	$(OUTPRE)LeashView.obj \
	$(OUTPRE)MainFrm.obj \
	$(OUTPRE)out2con.obj \
	$(OUTPRE)StdAfx.obj \
	$(OUTPRE)KrbListTickets.obj

RESFILE = $(OUTPRE)Leash.res
XOBJS	= $(RESFILE)

##### Options
# Set NODEBUG if building release instead of debug
BUILDTOP=..\..

LOCALINCLUDES= -I$(BUILDTOP) -I$(BUILDTOP)\include -I$(BUILDTOP)\windows\include

##### RC Compiler
RFLAGS	= $(LOCALINCLUDES)
RCFLAGS	= $(RFLAGS) -D_WIN32 -DLEASH_APP

DEFINES = -DWINSOCK -DWIN32 -DWINDOWS -D_AFXDLL -DNO_STATUS_BAR -DUSE_MESSAGE_BOX
!ifdef NODEBUG
DEFINES = $(DEFINES)
!else
DEFINES = $(DEFINES) -DDBG
!endif

##### Linker
LINK	= link
LIBS	= $(GLIB) $(CLIB)
SYSLIBS	= kernel32.lib Iphlpapi.lib ws2_32.lib user32.lib gdi32.lib comdlg32.lib comctl32.lib version.lib
LFLAGS	= /nologo $(LOPTS)

all: Makefile $(OUTPRE)$(EXE_NAME).exe

$(OUTPRE)$(EXE_NAME).exe: $(OBJS) $(XOBJS) $(LIBS)
	$(LINK) $(LFLAGS) /out:$@ /ENTRY:WinMainCRTStartup $(OBJS) $(XOBJS) \
	  $(LIBS) $(SYSLIBS) ../lib/$(OUTPRE)libwin.lib
	copy $@ "$(OUTPRE)MIT Kerberos.exe"

kfwribbon.bml kfwribbon.h kfwribbon.rc: kfwribbon.xml
	uicc.exe kfwribbon.xml kfwribbon.bml /res:kfwribbon.rc /header:kfwribbon.h /name:KFW_RIBBON

$(RESFILE): Leash.rc res/Leash.rc2 kfwribbon.rc ver.rc ../version.rc ../kerberos.ver

$(OUTPRE)LeashUIApplication.obj $(OUTPRE)LeashUICommandHandler.obj: kfwribbon.h

clean:
	if exist $(OUTPRE)*.exe del $(OUTPRE)*.exe
	if exist $(OUTPRE)*.obj del $(OUTPRE)*.obj
	if exist $(OUTPRE)*.res del $(OUTPRE)*.res
	if exist $(OUTPRE)*.pdb del $(OUTPRE)*.pdb
	if exist *.err del *.err
	if exist *.bml del *.bml
	if exist kfwribbon.h del kfwribbon.h
	if exist kfwribbon.rc del kfwribbon.rc
