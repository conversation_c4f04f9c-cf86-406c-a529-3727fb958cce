.\" Man page generated from reStructuredText.
.
.TH "KPASSWD" "1" " " "1.17.1" "MIT Kerberos"
.SH NAME
kpasswd \- change a user's Kerberos password
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkpasswd\fP [\fIprincipal\fP]
.SH DESCRIPTION
.sp
The kpasswd command is used to change a Kerberos principal\(aqs password.
kpasswd first prompts for the current Kerberos password, then prompts
the user twice for the new password, and the password is changed.
.sp
If the principal is governed by a policy that specifies the length
and/or number of character classes required in the new password, the
new password must conform to the policy.  (The five character classes
are lower case, upper case, numbers, punctuation, and all other
characters.)
.SH OPTIONS
.INDENT 0.0
.TP
.B \fIprincipal\fP
Change the password for the Kerberos principal principal.
Otherwise, kpasswd uses the principal name from an existing ccache
if there is one; if not, the principal is derived from the
identity of the user invoking the kpasswd command.
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kadmin(1), kadmind(8), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2019, MIT
.\" Generated by docutils manpage writer.
.
