#
# Generated makefile dependencies follow.
#
g_accept_sec_context.so g_accept_sec_context.po $(OUTPRE)g_accept_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_accept_sec_context.c mechglue.h mglueP.h
g_acquire_cred.so g_acquire_cred.po $(OUTPRE)g_acquire_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_acquire_cred.c mechglue.h mglueP.h
g_acquire_cred_with_pw.so g_acquire_cred_with_pw.po \
  $(OUTPRE)g_acquire_cred_with_pw.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_acquire_cred_with_pw.c \
  mechglue.h mglueP.h
g_acquire_cred_imp_name.so g_acquire_cred_imp_name.po \
  $(OUTPRE)g_acquire_cred_imp_name.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_acquire_cred_imp_name.c \
  mechglue.h mglueP.h
g_authorize_localname.so g_authorize_localname.po $(OUTPRE)g_authorize_localname.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_authorize_localname.c mechglue.h mglueP.h
g_buffer_set.so g_buffer_set.po $(OUTPRE)g_buffer_set.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_buffer_set.c mechglue.h mglueP.h
g_canon_name.so g_canon_name.po $(OUTPRE)g_canon_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_canon_name.c mechglue.h mglueP.h
g_compare_name.so g_compare_name.po $(OUTPRE)g_compare_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_compare_name.c mechglue.h mglueP.h
g_complete_auth_token.so g_complete_auth_token.po $(OUTPRE)g_complete_auth_token.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_complete_auth_token.c mechglue.h mglueP.h
g_context_time.so g_context_time.po $(OUTPRE)g_context_time.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_context_time.c mechglue.h mglueP.h
g_decapsulate_token.so g_decapsulate_token.po $(OUTPRE)g_decapsulate_token.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_decapsulate_token.c mechglue.h mglueP.h
g_delete_sec_context.so g_delete_sec_context.po $(OUTPRE)g_delete_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_delete_sec_context.c mechglue.h mglueP.h
g_del_name_attr.so g_del_name_attr.po $(OUTPRE)g_del_name_attr.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_del_name_attr.c mechglue.h mglueP.h
g_dsp_name.so g_dsp_name.po $(OUTPRE)g_dsp_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_dsp_name.c mechglue.h mglueP.h
g_dsp_name_ext.so g_dsp_name_ext.po $(OUTPRE)g_dsp_name_ext.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_dsp_name_ext.c mechglue.h mglueP.h
g_dsp_status.so g_dsp_status.po $(OUTPRE)g_dsp_status.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_dsp_status.c mechglue.h mglueP.h
g_dup_name.so g_dup_name.po $(OUTPRE)g_dup_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_dup_name.c mechglue.h mglueP.h
g_encapsulate_token.so g_encapsulate_token.po $(OUTPRE)g_encapsulate_token.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_encapsulate_token.c mechglue.h mglueP.h
g_exp_sec_context.so g_exp_sec_context.po $(OUTPRE)g_exp_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_exp_sec_context.c mechglue.h mglueP.h
g_export_cred.so g_export_cred.po $(OUTPRE)g_export_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_export_cred.c mechglue.h mglueP.h
g_export_name.so g_export_name.po $(OUTPRE)g_export_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_export_name.c mechglue.h mglueP.h
g_export_name_comp.so g_export_name_comp.po $(OUTPRE)g_export_name_comp.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_export_name_comp.c mechglue.h mglueP.h
g_get_name_attr.so g_get_name_attr.po $(OUTPRE)g_get_name_attr.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_get_name_attr.c mechglue.h mglueP.h
g_glue.so g_glue.po $(OUTPRE)g_glue.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_glue.c mechglue.h \
  mglueP.h
g_imp_cred.so g_imp_cred.po $(OUTPRE)g_imp_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_imp_cred.c mechglue.h mglueP.h
g_imp_name.so g_imp_name.po $(OUTPRE)g_imp_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_imp_name.c mechglue.h mglueP.h
g_imp_sec_context.so g_imp_sec_context.po $(OUTPRE)g_imp_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_imp_sec_context.c mechglue.h mglueP.h
g_init_sec_context.so g_init_sec_context.po $(OUTPRE)g_init_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_init_sec_context.c mechglue.h mglueP.h
g_initialize.so g_initialize.po $(OUTPRE)g_initialize.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(srcdir)/../krb5/gssapiP_krb5.h $(srcdir)/../krb5/gssapi_krb5.h \
  $(srcdir)/../spnego/gssapiP_spnego.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h ../generic/gssapi_err_generic.h \
  ../krb5/gssapi_err_krb5.h g_initialize.c mechglue.h \
  mglueP.h
g_inq_context.so g_inq_context.po $(OUTPRE)g_inq_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_context.c mechglue.h mglueP.h
g_inq_context_oid.so g_inq_context_oid.po $(OUTPRE)g_inq_context_oid.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_context_oid.c mechglue.h mglueP.h
g_inq_cred.so g_inq_cred.po $(OUTPRE)g_inq_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_cred.c mechglue.h mglueP.h
g_inq_cred_oid.so g_inq_cred_oid.po $(OUTPRE)g_inq_cred_oid.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_cred_oid.c mechglue.h mglueP.h
g_inq_name.so g_inq_name.po $(OUTPRE)g_inq_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_name.c mechglue.h mglueP.h
g_inq_names.so g_inq_names.po $(OUTPRE)g_inq_names.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_inq_names.c mechglue.h mglueP.h
g_map_name_to_any.so g_map_name_to_any.po $(OUTPRE)g_map_name_to_any.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_map_name_to_any.c mechglue.h mglueP.h
g_mech_invoke.so g_mech_invoke.po $(OUTPRE)g_mech_invoke.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_mech_invoke.c mechglue.h mglueP.h
g_mechattr.so g_mechattr.po $(OUTPRE)g_mechattr.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_mechattr.c mechglue.h mglueP.h
g_mechname.so g_mechname.po $(OUTPRE)g_mechname.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_mechname.c mechglue.h mglueP.h
g_oid_ops.so g_oid_ops.po $(OUTPRE)g_oid_ops.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_oid_ops.c mechglue.h mglueP.h
g_prf.so g_prf.po $(OUTPRE)g_prf.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_prf.c mechglue.h \
  mglueP.h
g_process_context.so g_process_context.po $(OUTPRE)g_process_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_process_context.c mechglue.h mglueP.h
g_rel_buffer.so g_rel_buffer.po $(OUTPRE)g_rel_buffer.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_rel_buffer.c mechglue.h mglueP.h
g_rel_cred.so g_rel_cred.po $(OUTPRE)g_rel_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_rel_cred.c mechglue.h mglueP.h
g_rel_name.so g_rel_name.po $(OUTPRE)g_rel_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_rel_name.c mechglue.h mglueP.h
g_rel_name_mapping.so g_rel_name_mapping.po $(OUTPRE)g_rel_name_mapping.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_rel_name_mapping.c mechglue.h mglueP.h
g_rel_oid_set.so g_rel_oid_set.po $(OUTPRE)g_rel_oid_set.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_rel_oid_set.c mechglue.h mglueP.h
g_saslname.so g_saslname.po $(OUTPRE)g_saslname.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_saslname.c mechglue.h mglueP.h
g_seal.so g_seal.po $(OUTPRE)g_seal.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_seal.c mechglue.h \
  mglueP.h
g_set_context_option.so g_set_context_option.po $(OUTPRE)g_set_context_option.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_set_context_option.c mechglue.h mglueP.h
g_set_cred_option.so g_set_cred_option.po $(OUTPRE)g_set_cred_option.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_set_cred_option.c mechglue.h mglueP.h
g_set_name_attr.so g_set_name_attr.po $(OUTPRE)g_set_name_attr.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_set_name_attr.c mechglue.h mglueP.h
g_set_neg_mechs.so g_set_neg_mechs.po $(OUTPRE)g_set_neg_mechs.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_set_neg_mechs.c mechglue.h mglueP.h
g_sign.so g_sign.po $(OUTPRE)g_sign.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(COM_ERR_DEPS) \
  $(srcdir)/../generic/gssapiP_generic.h $(srcdir)/../generic/gssapi_ext.h \
  $(srcdir)/../generic/gssapi_generic.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  ../generic/gssapi_err_generic.h g_sign.c mechglue.h \
  mglueP.h
g_store_cred.so g_store_cred.po $(OUTPRE)g_store_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_store_cred.c mechglue.h mglueP.h
g_unseal.so g_unseal.po $(OUTPRE)g_unseal.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_unseal.c mechglue.h mglueP.h
g_unwrap_aead.so g_unwrap_aead.po $(OUTPRE)g_unwrap_aead.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_unwrap_aead.c mechglue.h mglueP.h
g_unwrap_iov.so g_unwrap_iov.po $(OUTPRE)g_unwrap_iov.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_unwrap_iov.c mechglue.h mglueP.h
g_verify.so g_verify.po $(OUTPRE)g_verify.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_verify.c mechglue.h mglueP.h
g_wrap_aead.so g_wrap_aead.po $(OUTPRE)g_wrap_aead.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_wrap_aead.c mechglue.h mglueP.h
g_wrap_iov.so g_wrap_iov.po $(OUTPRE)g_wrap_iov.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  g_wrap_iov.c mechglue.h mglueP.h
gssd_pname_to_uid.so gssd_pname_to_uid.po $(OUTPRE)gssd_pname_to_uid.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h ../generic/gssapi_err_generic.h \
  gssd_pname_to_uid.c mechglue.h mglueP.h
