=pod

=head1 NAME

EVP_RSA_gen,
RSA_generate_key_ex, RSA_generate_key,
RSA_generate_multi_prime_key - generate RSA key pair

=head1 SYNOPSIS

 #include <openssl/rsa.h>

 EVP_PKEY *EVP_RSA_gen(unsigned int bits);

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int RSA_generate_key_ex(RSA *rsa, int bits, BIGNUM *e, BN_GENCB *cb);
 int RSA_generate_multi_prime_key(RSA *rsa, int bits, int primes, BIGNUM *e, BN_GENCB *cb);

The following function has been deprecated since OpenSSL 0.9.8, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 RSA *RSA_generate_key(int bits, unsigned long e,
                       void (*callback)(int, int, void *), void *cb_arg);

=head1 DESCRIPTION

EVP_RSA_gen() generates a new RSA key pair with modulus size I<bits>.

All of the functions described below are deprecated.
Applications should instead use EVP_RSA_gen(), L<EVP_PKEY_Q_keygen(3)>, or
L<EVP_PKEY_keygen_init(3)> and L<EVP_PKEY_keygen(3)>.

RSA_generate_key_ex() generates a 2-prime RSA key pair and stores it in the
B<RSA> structure provided in I<rsa>.

RSA_generate_multi_prime_key() generates a multi-prime RSA key pair and stores
it in the B<RSA> structure provided in I<rsa>. The number of primes is given by
the I<primes> parameter.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see L<RAND(7)>), the operation will fail.

The modulus size will be of length I<bits>, the number of primes to form the
modulus will be I<primes>, and the public exponent will be I<e>. Key sizes
with I<num> E<lt> 1024 should be considered insecure. The exponent is an odd
number, typically 3, 17 or 65537.

In order to maintain adequate security level, the maximum number of permitted
I<primes> depends on modulus bit length:

   <1024 | >=1024 | >=4096 | >=8192
   ------+--------+--------+-------
     2   |   3    |   4    |   5

A callback function may be used to provide feedback about the
progress of the key generation. If I<cb> is not NULL, it
will be called as follows using the BN_GENCB_call() function
described on the L<BN_generate_prime(3)> page.

RSA_generate_key() is similar to RSA_generate_key_ex() but
expects an old-style callback function; see
L<BN_generate_prime(3)> for information on the old-style callback.

=over 2

=item *

While a random prime number is generated, it is called as
described in L<BN_generate_prime(3)>.

=item *

When the n-th randomly generated prime is rejected as not
suitable for the key, I<BN_GENCB_call(cb, 2, n)> is called.

=item *

When a random p has been found with p-1 relatively prime to I<e>,
it is called as I<BN_GENCB_call(cb, 3, 0)>.

=back

The process is then repeated for prime q and other primes (if any)
with I<BN_GENCB_call(cb, 3, i)> where I<i> indicates the i-th prime.

=head1 RETURN VALUES

EVP_RSA_gen() returns an I<EVP_PKEY> or NULL on failure.

RSA_generate_multi_prime_key() returns 1 on success or 0 on error.
RSA_generate_key_ex() returns 1 on success or 0 on error.
The error codes can be obtained by L<ERR_get_error(3)>.

RSA_generate_key() returns a pointer to the RSA structure or
NULL if the key generation fails.

=head1 BUGS

I<BN_GENCB_call(cb, 2, x)> is used with two different meanings.

=head1 SEE ALSO

L<EVP_PKEY_Q_keygen(3)>
L<BN_generate_prime(3)>, L<ERR_get_error(3)>,
L<RAND_bytes(3)>, L<RAND(7)>

=head1 HISTORY

EVP_RSA_gen() was added in OpenSSL 3.0.
All other functions described here were deprecated in OpenSSL 3.0.
For replacement see L<EVP_PKEY-RSA(7)>.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
