[TOC]

#msquic api 概述
msquic api 接口涉及的文件主要有msquic.h, 定义api 的函数指针， 比如设置参数， 打开连接，等等，统一定义在一个结构体里。如下  
```C++
typedef struct QUIC_API_TABLE {

    QUIC_SET_CONTEXT_FN                 SetContext;
    QUIC_GET_CONTEXT_FN                 GetContext;
    QUIC_SET_CALLBACK_HANDLER_FN        SetCallbackHandler;

    QUIC_SET_PARAM_FN                   SetParam;
    QUIC_GET_PARAM_FN                   GetParam;

    QUIC_REGISTRATION_OPEN_FN           RegistrationOpen;
    QUIC_REGISTRATION_CLOSE_FN          RegistrationClose;
    QUIC_REGISTRATION_SHUTDOWN_FN       RegistrationShutdown;

    QUIC_CONFIGURATION_OPEN_FN          ConfigurationOpen;
    QUIC_CONFIGURATION_CLOSE_FN         ConfigurationClose;
    QUIC_CONFIGURATION_LOAD_CREDENTIAL_FN
                                        ConfigurationLoadCredential;

    QUIC_LISTENER_OPEN_FN               ListenerOpen;
    QUIC_LISTENER_CLOSE_FN              ListenerClose;
    QUIC_LISTENER_START_FN              ListenerStart;
    QUIC_LISTENER_STOP_FN               ListenerStop;

    QUIC_CONNECTION_OPEN_FN             ConnectionOpen;
    QUIC_CONNECTION_CLOSE_FN            ConnectionClose;
    QUIC_CONNECTION_SHUTDOWN_FN         ConnectionShutdown;
    QUIC_CONNECTION_START_FN            ConnectionStart;
    QUIC_CONNECTION_SET_CONFIGURATION_FN
                                        ConnectionSetConfiguration;
    QUIC_CONNECTION_SEND_RESUMPTION_FN  ConnectionSendResumptionTicket;

    QUIC_STREAM_OPEN_FN                 StreamOpen;
    QUIC_STREAM_CLOSE_FN                StreamClose;
    QUIC_STREAM_START_FN                StreamStart;
    QUIC_STREAM_SHUTDOWN_FN             StreamShutdown;
    QUIC_STREAM_SEND_FN                 StreamSend;
    QUIC_STREAM_RECEIVE_COMPLETE_FN     StreamReceiveComplete;
    QUIC_STREAM_RECEIVE_SET_ENABLED_FN  StreamReceiveSetEnabled;

    QUIC_DATAGRAM_SEND_FN               DatagramSend;

    QUIC_CONNECTION_COMP_RESUMPTION_FN  ConnectionResumptionTicketValidationComplete; // Available from v2.2
    QUIC_CONNECTION_COMP_CERT_FN        ConnectionCertificateValidationComplete;      // Available from v2.2

} QUIC_API_TABLE;
```
libray.c 则是具体的实现， 里面会调用api.c 以及libray 的一些接口

# libray.c 文档介绍
## 一、文件概述
library.c 文件实现了 Microsoft QUIC 库的核心功能，包含库的加载、卸载、初始化、分区管理、性能计数器管理、设置读取和更新等功能。

## 二、主要数据结构
QUIC_LIBRARY 该结构体用于存储全局库状态，包含库的加载状态、版本信息、配置设置、锁、分区信息、性能计数器等重要信息。以下是部分关键成员的说明：  
BOOLEAN Loaded：跟踪库是否已加载。
uint32_t Version[4]：当前二进制版本。
CXPLAT_LOCK Lock：控制对库所有非数据路径内部状态的访问。
CXPLAT_DISPATCH_LOCK DatapathLock：控制对库所有数据路径内部状态的访问。
volatile short LoadRefCount：MsQuicLoadLibrary 调用的总未完成引用数。
uint16_t PartitionCount：当前正在使用的分区数量。
QUIC_PARTITION* Partitions：每个分区的存储，数量为 PartitionCount。
## 三、函数功能说明

### 1. 库的加载与卸载
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void MsQuicLibraryLoad(void);
```
功能：加载库，初始化全局变量。如果 LoadRefCount 从 0 增加到 1，则执行库的加载操作，包括系统加载、锁初始化、列表初始化等。
参数：无
返回值：无

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void MsQuicLibraryUnload(void);
```
功能：卸载库，释放全局变量。如果 LoadRefCount 减为 0，则执行库的卸载操作，包括锁的卸载和系统卸载。
参数：无
返回值：无

### 2. 分区管理

```c
void MsQuicCalculatePartitionMask(void);
```
功能：计算分区掩码，用于生成和解析分区 ID。  
参数：无  
返回值：无  

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void MsQuicLibraryFreePartitions(void);
```
功能：释放分区资源，对每个分区进行反初始化操作，并释放分区数组。  
参数：无  
返回值：无  

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS QuicLibraryInitializePartitions(void);
```
功能：初始化分区，根据配置设置分区数量和处理器列表，分配分区内存并初始化每个分区。  
参数：无  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。

### 3. 性能计数器管理

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicLibrarySumPerfCounters(_Out_writes_bytes_(BufferLength) uint8_t* Buffer, _In_ uint32_t BufferLength);
```
功能：汇总所有分区的性能计数器值到指定缓冲区。  
参数：
Buffer：输出缓冲区，用于存储汇总后的性能计数器值。  
BufferLength：缓冲区长度。  
返回值：无

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void QuicLibrarySumPerfCountersExternal(_Out_writes_bytes_(BufferLength) uint8_t* Buffer, _In_ uint32_t BufferLength);
```
功能：在加锁的情况下汇总所有分区的性能计数器值到指定缓冲区。  
参数：
Buffer：输出缓冲区，用于存储汇总后的性能计数器值。
BufferLength：缓冲区长度。  
返回值：无

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void QuicPerfCounterSnapShot(_In_ uint64_t TimeDiffUs);
```
功能：对性能计数器进行快照，记录当前的性能计数器值，并进行一些断言检查，确保计数器值在合理范围内。  
参数：
TimeDiffUs：时间差，单位为微秒。  
返回值：无

### 4. 设置管理
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void MsQuicLibraryOnSettingsChanged(_In_ BOOLEAN UpdateRegistrations);
```
功能：处理库设置更改事件，根据设置更新负载均衡和重试状态，并更新所有注册信息。
参数：
UpdateRegistrations：是否更新注册信息。  
返回值：无

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Function_class_(CXPLAT_STORAGE_CHANGE_CALLBACK)
void MsQuicLibraryReadSettings(_In_opt_ void* Context);
```
功能：读取库的设置，从存储中加载设置并更新库的状态，同时触发设置更改事件。  
参数：
Context：可选上下文指针。  
返回值：无  

### 5. 库的初始化

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS MsQuicLibraryInitialize(void);
```
功能：初始化库，包括平台初始化等操作。  
参数：无  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。  

# api.c 文档介绍

## 一、文件概述
api.c 文件提供了 QUIC_API_TABLE 中大部分公共 API 的实现。这些 API 主要用于处理 QUIC 连接、流的创建、关闭、启动等操作，是 MsQuic 库与外部应用交互的重要接口。该文件遵循 MIT 许可证，适用于 MsQuic 项目。  
## 二、宏定义
### 1. IS_REGISTRATION_HANDLE(Handle)  
功能：判断 Handle 是否为有效的注册句柄。  
参数：  
Handle：待判断的句柄。  
返回值：布尔值，若 Handle 不为空且类型为 QUIC_HANDLE_TYPE_REGISTRATION，则返回 TRUE，否则返回 FALSE。  

### 2. IS_CONN_HANDLE(Handle)
功能：判断 Handle 是否为有效的连接句柄。  
参数：
Handle：待判断的句柄。  
返回值：布尔值，若 Handle 不为空且类型为 QUIC_HANDLE_TYPE_CONNECTION_CLIENT 或 QUIC_HANDLE_TYPE_CONNECTION_SERVER，则返回 TRUE，否则返回 FALSE。

### 3. IS_STREAM_HANDLE(Handle)
功能：判断 Handle 是否为有效的流句柄。   
参数：  
Handle：待判断的句柄。  
返回值：布尔值，若 Handle 不为空且类型为 QUIC_HANDLE_TYPE_STREAM，则返回 TRUE，否则返回 FALSE。  
## 三、函数功能说明
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicConnectionOpen(
    _In_ _Pre_defensive_ HQUIC RegistrationHandle,
    _In_ _Pre_defensive_ QUIC_CONNECTION_CALLBACK_HANDLER Handler,
    _In_opt_ void* Context,
    _Outptr_ _At_(*NewConnection, __drv_allocatesMem(Mem)) _Pre_defensive_
        HQUIC *NewConnection
);
```
功能：打开一个新的 QUIC 连接。该函数调用 MsQuicConnectionOpenInPartition 并使用当前分区索引。  
参数：
RegistrationHandle：注册句柄，用于标识注册实例。  
Handler：连接回调处理函数，用于处理连接事件。  
Context：可选上下文指针，将传递给回调函数。  
NewConnection：输出参数，指向新连接的句柄。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。  

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicConnectionOpenInPartition(
    _In_ _Pre_defensive_ HQUIC RegistrationHandle,
    _In_ uint16_t PartitionIndex,
    _In_ _Pre_defensive_ QUIC_CONNECTION_CALLBACK_HANDLER Handler,
    _In_opt_ void* Context,
    _Outptr_ _At_(*NewConnection, __drv_allocatesMem(Mem)) _Pre_defensive_
        HQUIC *NewConnection
);
```
功能：在指定分区中打开一个新的 QUIC 连接。  
参数：  
RegistrationHandle：注册句柄，用于标识注册实例。  
PartitionIndex：分区索引，指定连接所在的分区。  
Handler：连接回调处理函数，用于处理连接事件。  
Context：可选上下文指针，将传递给回调函数。  
NewConnection：输出参数，指向新连接的句柄。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。  

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QUIC_API
MsQuicConnectionClose(
    _In_ _Pre_defensive_ __drv_freesMem(Mem)
        HQUIC Handle
);
```
功能：关闭一个 QUIC 连接。  
参数：  
Handle：待关闭的连接句柄。  
返回值：无  
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QUIC_API
MsQuicConnectionShutdown(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ QUIC_CONNECTION_SHUTDOWN_FLAGS Flags,
    _In_ _Pre_defensive_ QUIC_UINT62 ErrorCode
);
```
功能：关闭一个 QUIC 连接。  
参数：  
Handle：待关闭的连接句柄。  
Flags：关闭连接的标志。  
ErrorCode：关闭连接的错误码。  
返回值：无  
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicConnectionStart(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ _Pre_defensive_ HQUIC ConfigHandle,
    _In_ QUIC_ADDRESS_FAMILY Family,
    _In_reads_or_z_opt_(QUIC_MAX_SNI_LENGTH)
        const char* ServerName,
    _In_ uint16_t ServerPort // Host byte order
);
```
功能：启动一个 QUIC 连接。  
参数：  
Handle：连接句柄  
ConfigHandle：配置句柄，用于指定连接的配置  
Family：地址族，指定连接的地址类型  
ServerName：服务器名称，可选参数  
ServerPort：服务器端口，主机字节序  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_PENDING，失败返回相应的错误码。 

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicStreamOpen(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ QUIC_STREAM_OPEN_FLAGS Flags,
    _In_ _Pre_defensive_ QUIC_STREAM_CALLBACK_HANDLER Handler,
    _In_opt_ void* Context,
    _Outptr_ _At_(*NewStream, __drv_allocatesMem(Mem)) _Pre_defensive_
        HQUIC *NewStream
);
```

功能：打开一个新的 QUIC 流。  
参数：  
Handle：连接或流句柄。  
Flags：流打开标志。  
Handler：流回调处理函数，用于处理流事件。  
Context：可选上下文指针，将传递给回调函数。  
NewStream：输出参数，指向新流的句柄。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。  

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QUIC_API
MsQuicStreamClose(
    _In_ _Pre_defensive_ __drv_freesMem(Mem)
        HQUIC Handle
);
```
功能：关闭一个 QUIC 流。  
参数：  
Handle：待关闭的流句柄。  
返回值：无  

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicStreamStart(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ QUIC_STREAM_START_FLAGS Flags
);
```
功能：启动一个 QUIC 流。  
参数：  
Handle：流句柄。  
Flags：流启动标志。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_PENDING，失败返回相应的错误码。  

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicConnectionCertificateValidationComplete(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ BOOLEAN Result,
    _In_ QUIC_TLS_ALERT_CODES TlsAlert
);
```
功能：完成 QUIC 连接的证书验证。  
参数：  
Handle：连接或流句柄。  
Result：证书验证结果，TRUE 表示验证通过，FALSE 表示验证失败。  
TlsAlert：TLS 警报代码。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_PENDING，失败返回相应的错误码。  
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicGetParam(
    _When_(QUIC_PARAM_IS_GLOBAL(Param), _Reserved_)
    _When_(!QUIC_PARAM_IS_GLOBAL(Param), _In_ _Pre_defensive_)
        HQUIC Handle,
    _In_ uint32_t Param,
    _Inout_ _Pre_defensive_ uint32_t* BufferLength,
    _Out_writes_bytes_opt_(*BufferLength)
        void* Buffer
);
```
功能：获取 QUIC 对象的参数。  
参数：  
Handle：QUIC 对象句柄，对于全局参数该参数应为 NULL。  
Param：参数标识符。  
BufferLength：输入输出参数，输入时表示缓冲区长度，输出时表示实际写入的字节数。  
Buffer：输出缓冲区，用于存储参数值。  
返回值：QUIC_STATUS 类型，表示操作结果。成功返回 QUIC_STATUS_SUCCESS，失败返回相应的错误码。    

# msquic 获取api 函数
msquic.h 里定义了总的msquic 函数的接口， quic 的api 包含如下函数指针， 需要通过MsQuicOpen2 获取， 定义如下  
```C++
#define MsQuicOpen2(QuicApi) MsQuicOpenVersion(2, (const void**)QuicApi)
```
里面会调用MsQuicLibraryLoad()，初始化跟平台相关的整个全局变量。然后调用 MsQuicAddRef（）, 计数器为1时启动全局工作线程（只启动一次），最后对api 函数进行初始化
```C++
Api->SetContext = MsQuicSetContext; //设置context
Api->GetContext = MsQuicGetContext; // 获取context
Api->SetCallbackHandler = MsQuicSetCallbackHandler; //设置回掉对象，和回掉函数

Api->SetParam = MsQuicSetParam; // 设置参数
Api->GetParam = MsQuicGetParam; // 获取设置参数

Api->RegistrationOpen = MsQuicRegistrationOpen; //根据注册值打开注册器
Api->RegistrationClose = MsQuicRegistrationClose; // 关闭注册器， 释放
Api->RegistrationShutdown = MsQuicRegistrationShutdown;// 停止注册器配置（可以在RegistrationClose 之前调用）

Api->ConfigurationOpen = MsQuicConfigurationOpen; //打开配置
Api->ConfigurationClose = MsQuicConfigurationClose; //关闭配置
Api->ConfigurationLoadCredential = MsQuicConfigurationLoadCredential; // 设置tls 相关参数

Api->ListenerOpen = MsQuicListenerOpen; // 服务端设置listen 回掉
Api->ListenerClose = MsQuicListenerClose;// 关闭回掉
Api->ListenerStart = MsQuicListenerStart; // 开始监听
Api->ListenerStop = MsQuicListenerStop;// 停止监听

Api->ConnectionOpen = MsQuicConnectionOpen; //打开连接
Api->ConnectionClose = MsQuicConnectionClose;//关闭连接
Api->ConnectionShutdown = MsQuicConnectionShutdown;//停止连接
Api->ConnectionStart = MsQuicConnectionStart;//开始连接
Api->ConnectionSetConfiguration = MsQuicConnectionSetConfiguration;// 对连接设置配置
Api->ConnectionSendResumptionTicket = MsQuicConnectionSendResumptionTicket;// 设置恢复标志， 连接成功后表示可以发送数据
Api->ConnectionResumptionTicketValidationComplete = MsQuicConnectionResumptionTicketValidationComplete;// 设置握手结束标志
Api->ConnectionCertificateValidationComplete = MsQuicConnectionCertificateValidationComplete;//设置校验结束标志

Api->StreamOpen = MsQuicStreamOpen;//client在一个连接上打开流
Api->StreamClose = MsQuicStreamClose;//关闭流
Api->StreamShutdown = MsQuicStreamShutdown;//停止流
Api->StreamStart = MsQuicStreamStart;//开始流
Api->StreamSend = MsQuicStreamSend;//发送数据
Api->StreamReceiveComplete = MsQuicStreamReceiveComplete;//所有对端数据接受完毕
Api->StreamReceiveSetEnabled = MsQuicStreamReceiveSetEnabled;//设置是否接受流

Api->DatagramSend = MsQuicDatagramSend;//段发送， 无重传， 支持加密， 有拥塞控制
```

# 服务端调用api时序图
一个完整的服务端 会经历初始化， 监听端口，获取连接， 获取流，接收流数据， 发送流数据， 关闭流， 关闭连接， 关闭监听，结束的整个流程。  
### 初始化
- MsQuicOpen2 初始化全局变量，启动线程池。
- MsQuic->RegistrationOpen 可以对模式进行选择（比如是低延迟， 还是大通道模式， 实时模式）  
MsQuic->ConfigurationOpen 进行一些基本的配置（超时时间， 拥塞算法， 是否分片发送， mtu大小）  
- MsQuic->ConfigurationLoadCredential 设置加密相关配置

### 监听端口
- MsQuic->ListenerOpen 设置连接ServerListenerCallback 回掉， 有客户端连接时，或者有新的流建立时都会进行回掉  
- MsQuic->ListenerStart 开始监听， 会带上端口和地址

### 获取连接，获取流
- 客户端有连接接入时，ServerConnectionCallback 回掉带事件QUIC_CONNECTION_EVENT_CONNECTED， 在连接上开始一个流时ServerConnectionCallback 回掉事件。
- QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED。 这时候可以通过流发送和接收数据了。
### 获取流数据， 发送流
- 接收数据是通过ServerStreamCallbak回掉 QUIC_STREAM_EVENT_RECEIVE事件给上层，并带上了回掉buffer。  
- MsQuic->StreamSend 则可以通过流对象发送数据  

### 关闭流， 关闭连接
- 对端关闭流时会收到ServerStreamCallbak 回掉QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE 事件， 这时候服务端对流进行释放。  
- 对端关闭连接时会收到ServerConnectionCallback 的QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE 事件， 这时候可以对连接对象进行释放操作

### 关闭监听， 结束
- 通过 MsQuic->ListenerClose 关闭监听。
- 最后通过MsQuicClose 来释放所有资源，例如关闭线程池等

整个服务端工作时序图如下：
![](msquic/server-api-call_0.png) 
![](msquic/server-api-call_1.png) 


# 客户端调用api时序图
一个完整的客户端 会经历初始化， 打开连接， 打开流，发送流数据， 接收流数据， 关闭流， 关闭连接， 结束的整个流程。

### 初始化
- MsQuicOpen2 初始化全局变量，启动线程池， 接着- MsQuic->RegistrationOpen 可以对模式进行选择（比如是低延迟， 还是大通道模式， 实时模式）  
- MsQuic->ConfigurationOpen 进行一些基本的配置（超时时间， 拥塞算法， 是否分片发送， mtu大小）  
- MsQuic->ConfigurationLoadCredential 设置加密相关配置

### 打开连接
- MsQuic->ConnectionOpen 设置连接回掉ClientConnectionCallback， 连接成功后有回掉上来 
- MsQuic->ConnectionStart 开始连接， 会带上连接端口和地址

### 打开流
- 收到ClientConnectionCallback连接成功的QUIC_CONNECTION_EVENT_CONNECTED 回掉后， 通过MsQuic->StreamOpen 获取一个流对象， 并设置回掉函数ClientStreamCallback。
- 设置之后MsQuic->StreamStart 开始流功能

### 获取流数据， 发送流数据
- 接收数据是通过ServerStreamCallbak回掉 QUIC_STREAM_EVENT_RECEIVE事件给上层，并带上了回掉buffer。 
- MsQuic->StreamSend 则可以通过流对象发送数据 。

### 关闭流， 关闭连接
- 对端关闭流时会收到ClientStreamCallback 回掉QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE 事件， 这时候服务端对流进行释放。  
- 对端关闭连接时会收到ClientConnectionCallback 的QUIC_CONNECTION_EVENT_SHUTDOWN_COMPLETE 事件， 这时候可以对连接对象进行释放操作。

### 结束
- 通过MsQuicClose 来释放所有资源，例如关闭线程池等

整个客户端工作时序图如下：
![](msquic/client-api-call0.png) 
![](msquic/client-api-call1.png)

  


