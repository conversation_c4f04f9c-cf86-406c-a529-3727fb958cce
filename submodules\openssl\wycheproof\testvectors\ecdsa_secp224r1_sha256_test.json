{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 385, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d009e82950ebe102f37ff3645cc7d3c1bab8864e5e03a5011eeba8150bc", "result": "valid", "flags": []}, {"tcId": 2, "comment": "valid", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "************", "sig": "30813c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "************", "sig": "3082003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "************", "sig": "303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "************", "sig": "3085010000003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "************", "sig": "308901000000000000003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "************", "sig": "30847fffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "************", "sig": "3084ffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "************", "sig": "3085ffffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "************", "sig": "3088ffffffffffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "************", "sig": "30ff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "************", "sig": "303c02803ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040280617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "************", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "************", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "************", "sig": "303e0000021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "************", "sig": "3041498177303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "************", "sig": "30402500303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "************", "sig": "303e303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "************", "sig": "30412221498177021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "************", "sig": "304022202500021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "************", "sig": "3044221e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040004deadbeef021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "************", "sig": "3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042221498177021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0422202500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "************", "sig": "3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04221e021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "************", "sig": "3044aa00bb00cd00303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "************", "sig": "3042aa02aabb303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "************", "sig": "30442224aa00bb00cd00021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "************", "sig": "30422222aa02aabb021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "************", "sig": "3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042224aa00bb00cd00021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "************", "sig": "3042021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042222aa02aabb021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "************", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "************", "sig": "3080303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "************", "sig": "30402280021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042280021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "************", "sig": "3080313c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "************", "sig": "30402280031c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042280031c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "************", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "************", "sig": "2e3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "************", "sig": "2f3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "************", "sig": "313c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "************", "sig": "323c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "************", "sig": "ff3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "************", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "************", "sig": "3040300102303b1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "************", "sig": "303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "************", "sig": "303b1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": ["BER"]}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad98100", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad98105000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "************", "sig": "3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "************", "sig": "303e3000021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9813000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "************", "sig": "303f021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "************", "sig": "303e303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "************", "sig": "301e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "************", "sig": "305a021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "************", "sig": "303d02811c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402811c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "************", "sig": "303e0282001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040282001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "wrong length of integer", "msg": "************", "sig": "303c021d3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "************", "sig": "303c021b3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "30410285010000001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "************", "sig": "3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040285010000001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "3045028901000000000000001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "************", "sig": "3045021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04028901000000000000001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "304002847fffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402847fffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "30400284ffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040284ffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "30410285ffffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "************", "sig": "3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040285ffffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "30440288ffffffffffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "************", "sig": "3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040288ffffffffffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "************", "sig": "303c02ff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402ff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "************", "sig": "301e021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "************", "sig": "301f02021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "************", "sig": "301f021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "************", "sig": "303e021e3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "************", "sig": "303e021e00003ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e0000617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "************", "sig": "303e021e3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "************", "sig": "303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "************", "sig": "30200281021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "************", "sig": "3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "************", "sig": "30200500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "************", "sig": "3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "************", "sig": "303c001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "************", "sig": "303c011c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "************", "sig": "303c031c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "************", "sig": "303c041c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "************", "sig": "303cff1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04011c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04031c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04041c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04ff1c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "************", "sig": "30200200021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "************", "sig": "3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "************", "sig": "3040222002013a021bde5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "************", "sig": "3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042220020161021b7d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "************", "sig": "303c021c38de5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c637d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a84021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad901", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "************", "sig": "303b021b3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "************", "sig": "303b021bde5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "************", "sig": "303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "************", "sig": "303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b7d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "************", "sig": "303d021dff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "************", "sig": "3021090180021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "************", "sig": "3021021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "************", "sig": "3021020100021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "************", "sig": "3021021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021d013ade5c0624a5677ed7b6450d941fd283098d8a004fc718e2e7e6b441021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021dff3ade5c0624a5677ed7b6450d9421a53d481ba984280cc6582f2e5fc7021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303c021cc521a3f9db5a98812849baf26bdf441fd72b663dc4161062747575fc021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021d00c521a3f9db5a98812849baf26bde5ac2b7e4567bd7f339a7d0d1a039021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021dfec521a3f9db5a98812849baf26be02d7cf67275ffb038e71d18194bbf021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021d013ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021d00c521a3f9db5a98812849baf26bdf441fd72b663dc4161062747575fc021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d01617d6af141efd0c800c9ba3382c2119a390cfa9bed6a409bfe3703be", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dff617d6af141efd0c800c9ba3382c3e454779b1a1fc5afee11457eaf44", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c9e82950ebe102f37ff3645cc7d3d0508a7abf5a22672e8a95e25267f", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dfe9e82950ebe102f37ff3645cc7d3dee65c6f305641295bf6401c8fc42", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d01617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d009e82950ebe102f37ff3645cc7d3d0508a7abf5a22672e8a95e25267f", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "************", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3839313737", "sig": "303d021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021d0096ad91f02a3bc40c118abd416ed5c6203ed7ced0330860d7b88c10ab", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "343236343739373234", "sig": "303d021d00bcca2365cebdcf7c6cda1ee7b27c7fe79e371537b01869c715eabb1e021c3ae76f9bbfe519d778816dc8fe10635ee7576b6b7916f0c21df320c0", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "37313338363834383931", "sig": "303c021c59a9f83289ef6995d5d5592e80ab4f6a81123f69d385d3cfb152faf2021c3a97d5be190d5819241067e2be56375ab84155baab8fc7aeb7f8cb3d", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "3130333539333331363638", "sig": "303e021d00b54bac9be2beaaa09456a3968a1faf27c9d96bd5f6738fec6066d31e021d00d72c22129344a96d52fda60b264cf5e6fae45fd2c1b1b78bcba30070", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "33393439343031323135", "sig": "303d021c323dbdecd40910c6fa7a5691846fa7769113d1f2ba64ef0dc97d2ddb021d00ca9e73a4587af042f8ba924bb61829c5e24046f9803eb76ab80ef327", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "31333434323933303739", "sig": "303d021d00a55dccc27d287f15960ed79908a3edb6bb31aff07c8caa0e65fc0785021c559cb51aa5f2b9066610199dd01291a47729a6189a622ae9e7af7621", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "33373036323131373132", "sig": "303d021c137ed6105148d6f5b84e87735d57955f81c5914a6e69f55347ade074021d00dfa5d56b1a12567efacb348a133b79d48da7aac78d78ee589c2ec027", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "333433363838373132", "sig": "303e021d00856ff63d779163e78fed8c48330b48f08bf953a95266b3857eee91aa021d00f4aa917cd37f556c6df9d0960c2f7daa7ea118e5c30cc40ca1eed418", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31333531353330333730", "sig": "303d021d00a9d7716f04c5ce247f6b8c608b37db55f68e2ff94a5883863e867708021c61bc093faa6fb25cd240aea4b56fed728f7b3669b4dc84c449d38c5d", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "36353533323033313236", "sig": "303d021d00f6d088fd3b9c981ac491c62030643bbd82d4f4588e8517de5884e73d021c773eee477980763b1ea27ae998bda0244cb67b07aa6779a38cd2ba3f", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "31353634333436363033", "sig": "303e021d00eacb55588e446bbf3687089ba8ba3b05cfef7458bb81b4277f90a853021d008039e8944cc3df7f4ce5badc349975d471a81dea14e9bcae3065d410", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "34343239353339313137", "sig": "303c021c5984af8c89fb9d596a1f28fd3d41e46f7205fe12fa63437ac79e7e81021c33b16b742d45f18f88de2713078384e6150f06b8b99f36ab2ce3dd49", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "3130393533323631333531", "sig": "303d021c3cda62d84711c262f782d5c3a79b567485227b34afb821f5241b1961021d00b615cef399706ff758f072931852b717ec898e9a1e6339d0ee81b8da", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "35393837333530303431", "sig": "303d021d00e1db7304609191ea1ac91183ffb31df51b5b3fdc6b1a1129d85818d6021c441886d003ae80fbe7139e1d02845cd1bd959f0df1468f5836dd6ea5", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "33343633303036383738", "sig": "303d021c3545dc4a4ef84bbb3a526ff929c91ad234516a9e95455ac8db4012b1021d00af49926f693a7cf11f71e199f382a8d640c0c85e46d94ee26e384344", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "39383137333230323837", "sig": "303d021c0ccafdeae4582c9de6795b2d09a7fc3848c75904fa960989156cbbb9021d00af1f994da3e7d89cc8aaa44616cb77e3be7a83ccecc965775194e502", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323232303431303436", "sig": "303e021d00a3b2145d8c669027532501eea1913abb22a78a827fdd82fe9d6d3757021d009b2f1ae84f5606d68653065f74e9d089886694c739fbe3fd4a1b2b4a", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "36363636333037313034", "sig": "303e021d009aac3a7e3d142344991bf177b4f4dbfa074148ad9e20f27555b547d9021d00f830a3c7fdf251d79d41977d28e6d9a72a36df11b86e17c8dc3acae0", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "31303335393531383938", "sig": "303c021c4769fba554fd436051c285bdadfa33a443d4f7084dd598ce3b98b8fb021c0c014c87cb14113d75864f74905f75b34f9970ba58b5d0676021826d", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "31383436353937313935", "sig": "303d021d008b91fc5054a75c34a508624b85708b3d25fa74328c68741c3aeb92d9021c155e3e46b1209583135a9fef15abe325b25bd19285ee6b5b4549629f", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "33313336303436313839", "sig": "303d021d00a4a2a85fbb8bb26c4d845cfac191f89d65b00d3f1b9450d177f78890021c6605a460e60402685c7a5accd2615e9232e51937bd83dfa3065eabf7", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32363633373834323534", "sig": "303d021d00a89d333ae34187855cf7fa435ff39be6b7bb39b2d0ce682133ad9646021c483dcc89a3b43be250f5c3f78f78418e7b8341a8bcfb93dfd58e46d8", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "31363532313030353234", "sig": "303d021c2d0f99c71933c82ded544ef4faac9d669e437dea13b57186f4c20a0e021d00d9682b9f3a05d7832947bc45eadbc742d96e7ab1124832ddb7a8c65b", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "35373438303831363936", "sig": "303d021d00840208f7c41b1fbadcc701fb3a1d0f98a3e2a75235e695bfd378f8b4021c44c8daad4efc03e1753803c362b409c3ca6e0f21e538fe3a364c0e53", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "36333433393133343638", "sig": "303e021d0087cc582cb10602110566fcb10a233aede993fae5fb3f81b0bbff94ca021d00c971c05bd51d9685825b2cfc0a2596c7f80d9f9dc68c28c159aa395a", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "31353431313033353938", "sig": "303d021c50d73d949b3adcd3e8fa94dafefaf9d263ebc702128d891afac47ea7021d00f8423c378f0190574925142eb5b97c612abfa048fa3ab5375ec795a1", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "3130343738353830313238", "sig": "303e021d00d608915dfcd5d3c63ed10d0d9b614f7a866f8858a6e59dc03eb0a8ee021d008e701aa0bab491430f6e4da92244b0bb174957ee6f495bc5d15fabb1", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "3130353336323835353638", "sig": "303e021d00c87b0ab842c4769ed94b910bd7719691f9991bc5a347889608f07034021d00d083111048d6e019771fc2669c55156a3d09615a6b2d9cae52ddabee", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "393533393034313035", "sig": "303c021c0a1c2c2478e244464226c660edf724db1213f4923eb725d611d976fd021c764e55186a76f734891d05fb57af2727fab8fbea684ca4321d5de540", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "393738383438303339", "sig": "303e021d008a2747c5dd9ef5298b8aeabd2fb3a2beb16158fb2cc62be9e51b2152021d00f96251bc048bcad832e6cbc09c9c2e585ab7543dc552eaa5125be0d3", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "33363130363732343432", "sig": "303e021d00d9eac32a734f3a3e5b5a2905bed8164ef4c6cd24d5c0fc54cc83f3cc021d00a784930d16c3b753bb3ed9151d583c50ff97bc976274bde482fb9644", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "31303534323430373035", "sig": "303d021c6c40c6b15ae573f77b677cd878cc5e4da8171cf50d79974fde374e00021d00c88c9828037bf7013a1415537ca074d6c8a553bdb4b26b14a7e88d93", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "35313734343438313937", "sig": "303d021d00dca0aaa0a395393142b323edced09372760350f2ab261ce3339b114d021c0983bf6e510ce7f0a7520f2b7c60cd68a4912b78162c7ac33789e0c6", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "31393637353631323531", "sig": "303d021d00a0526ed47e2607e6bae6dcf3b8f54f4e0638023673a38cad4569c3ba021c61516f55746b379d11cbaa02cef35311d7771a47d1e127cff46dcfd6", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "33343437323533333433", "sig": "303d021c5c00db60178c8361092bdfb47fc9a47b33363d7e0d76e32520f79657021d00e1baf7ae7d81045793c73173f49d60bdfc8779942795d9d082b3ca11", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "333638323634333138", "sig": "303d021c46f69b6a99717949eee74092a0c1438a290a2cd82fe1e10d8f37e88b021d0099a5f59f09bd980a066233523397846987a8a1bfdde355062d140a4b", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33323631313938363038", "sig": "303e021d00e643d8085a22706fa0e6540f3d5e169ad8cc49b4bfe98e325321c705021d00f95bd423f9cafe0cedfec6fd97871536d71b2ac58dfb2f7ab8952d4b", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "39363738373831303934", "sig": "303e021d00e65fb9bcdd791f141ccff2b3cfbf45d84f8c6272021a68dde8c36bc8021d00df6e08c74b5e36b7772658f02515ae0ea813b64df24f3522ea15fb15", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34393538383233383233", "sig": "303e021d00a476d468221ef55611e8a724c9b4cd79c34f6940d5f665e3335f6231021d00bfddc18e7a008bc206c8e1ca6c878363e4138508e0c3a84a27eabe35", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "383234363337383337", "sig": "303c021c1b393477941879271873a8c043a77caadb9957fcdd263a6ac978e4ba021c270060d5f356ebb6d185772baa78b878af6807378e0d5c532da0a4a7", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3131303230383333373736", "sig": "303e021d00b2eda8c969d4b1bdd31867fd1f92d547b406840c257f2f80dfbdc4e3021d00e6297b059ce64ef04de9715a8f686a9f73980865066a94975b7f8117", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "313333383731363438", "sig": "303d021d00938189a18a4bff5712ac99c2b8e92c218af3e4d4e3a84b906b0f704e021c7bb3e538f0b70664dad462ab14b0ed416c86ac6e9060fe760dabb715", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "333232313434313632", "sig": "303e021d00bb7c1d8120d2aa7765b16eeac44282de605fb2a1665657dea4492935021d00e0a8adb3a143883f981ea1323fa6f1d347845be2b8dcc6cd5cc93ee5", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "3130363836363535353436", "sig": "303c021c74a4c51dd60c7118467be29652060f39af94f8c0eb7f15c64771010c021c6102ec0c9257e607af3f3ff7490b54e78111f422bec11ba01277171f", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "3632313535323436", "sig": "303d021c625da18d676f02fae9dbcb3092265909488fb95d662569d7746b9687021d00c4f1ec831e36604d604b630fd0b1999cd09960862294251d85e5873d", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "37303330383138373734", "sig": "303d021d008ee0d4a31fd1c4d854d75c14151926899dde1c7332fd4769443d213d021c4b8278b89ba4f8fbd7dcc6affe4c12156f7409909416989685dd5a39", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "35393234353233373434", "sig": "303e021d00bdde45fc9ebb3749c9fb2c25bf02e2a217ccc112f8e65499eeffb6a1021d00becd6b88ef2bee872ebc0e2b805a56066e19179fce9f0dc0df3f6378", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "31343935353836363231", "sig": "303d021c50186e023a1f5053fcb4d0473039b1b2cdeba569719a4ebabdd675c8021d00f8fb893c1b6b5b827b5f3f4bb5eab75b6212bb56a5a39bb35c127a1c", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "34303035333134343036", "sig": "303e021d00d3b454639b0fb3da93b20d55be8609e40902cb4a608f3b9064c0deb7021d00ec7aa9637fd71b543e5243faab4c7a2edc2c48e982c5ac017807f19a", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "33303936343537353132", "sig": "303d021d00c202abbd98e03809de842bdef268a1c616a7306da69a87abaf03169c021c7e7e04823af8ed6836fd2ac011e47de8e1bef91ed1da5144893fc259", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "32373834303235363230", "sig": "303d021c2e4b76638816cce057a4a27a49258dcb5437ae97739f27ebc0973c0b021d00e9f6c0b64e764ad39dd92b576e11c23e5994b02095cb2a4720c8662c", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32363138373837343138", "sig": "303c021c7e0f48761089aa4c7ecd5a7ac5380836b1e5d381d3400174d15df98b021c0c3df50060e3a6714aa565a33d784e7b16ac87bebfb3c2255cfd832c", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31363432363235323632", "sig": "303c021c4d6f7408508eb0814dcd48007f0efd9e2b91cdac4030540cc678de19021c1e74f8dc34d13613ef42462fe88981cbe2489be10e4cdae975a1b38e", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "36383234313839343336", "sig": "303d021d00967f2c5d304c7932eaaa1682197945e66cc912b703824776ef16ad7a021c73957001d9037c63d6471c809a2388383ad695137c622cd5f5584414", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "343834323435343235", "sig": "303d021c49260804bb2ceae4b9cee63b02ea60173ec3f4f90167627c0bb39888021d00c9eb022f96db3e90fe0ff617730a629f342e02fb208d6836cbbdc7d3", "result": "valid", "flags": []}, {"tcId": 285, "comment": "Signature generated without truncating the hash", "msg": "************", "sig": "303d021d00f3e712597a4b22632c5f8eb9f2845882bb03a139735f80af8826fc56021c62865bd91c0903511a481d607eb6b5fe28f6f6c89295681a3e8d55d8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0493b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92", "wx": "0093b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2", "wy": "482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000493b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEk7TCjwMtAPgOd0ke3BWDWZCe6eMKcye3\nQhnl4kgsGa41yyivyblcoe162RyBLV/M60vt2/GhbZI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 286, "comment": "k*G has a large x-coordinate", "msg": "************", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 287, "comment": "r too large", "msg": "************", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948", "wx": "00da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789", "wy": "00ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE2pJ/S6iLY5v1M0Ih0vVNjvnMwaESX60Y\nx7+3iaxRrlPebYNKnbOUe43UxqwrCEuFSWv6cthraUg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 288, "comment": "r,s are large", "msg": "************", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0420888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fcde00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1", "wx": "20888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fc", "wy": "00de00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000420888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fcde00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEIIiOHA9WlMTANjs2SCvrbh5mSbPTsm8S\nf+u2/N4AwvPY5KfooLr9QXyW0+gcl1lGovNoaqOdNfE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 289, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049545c86f032c5df255a4490bb0b83eca201181792ad74246874db229405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04", "wx": "009545c86f032c5df255a4490bb0b83eca201181792ad74246874db229", "wy": "405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049545c86f032c5df255a4490bb0b83eca201181792ad74246874db229405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAElUXIbwMsXfJVpEkLsLg+yiARgXkq10JG\nh02yKUBSZMKDBjMntw9MK+WrTS6UB7hm4SHWFF0STAQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 290, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965", "wx": "579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8", "wy": "00df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEV51T851RCb1EDj4+fv1gN0CWM0j/nHLA\nOw/muN8C8TPs1gsHKggSrcdScI8r6djJrVlT2Me/OWU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 291, "comment": "small r and s", "msg": "************", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a", "wx": "00d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628", "wy": "00acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0qFMgQbYnzU2+uva/NRoD2WrS/IkMWTK\nFGS2KKyvK+5S5iMdPJgPUvjhiaQcPjoF5ZEZXshkIXo=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 292, "comment": "small r and s", "msg": "************", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5", "wx": "00e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4", "wy": "326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6JJHkVOtE+pcpF1MMj6/H8PNDN94fDQw\naj95pDJsqWRfK1F2CNwfCLeoTPxh5v9o0U8n0gQ8fvU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 293, "comment": "small r and s", "msg": "************", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 294, "comment": "r is larger than n", "msg": "************", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103ad465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219", "wx": "2b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103a", "wy": "00d465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103ad465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEKw6sNcCylPbUNdyv+oYzsBIwBUZcMAgK\n28wQOtRlpjv7cdSu4JMoaX/hCIdTZG2DabjcEDIXwhk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "s is larger than n", "msg": "************", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7afd73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c", "wx": "00d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7af", "wy": "00d73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7afd73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0VbgHjO+zt6PT7SulSHXUef463lcoAhX\n2y/Xr9c6RQ7GDmqSGKhDGHBofglolE9txf/rMORpO3w=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "small r and s^-1", "msg": "************", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e617dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c", "wx": "00f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e6", "wy": "17dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e617dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE8pOoorSv8L7ZXGY7Nkr+aXeNON1+ejBP\nfTx05hff0J54A8RDmmwHXLV5zeZS0D91Wf9YhGMS+kw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "smallish r and s^-1", "msg": "************", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954", "wx": "00d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317", "wy": "00d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1N3wA7KYy6p9LtxYSyi0dKdhYu1LWw9i\nIsVDF9Tk/gMPF4+0qkptf2EmXs1+8TwxNga400GouVQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "100-bit r and small s^-1", "msg": "************", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce89668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0", "wx": "008a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce8", "wy": "009668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce89668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEilvwAo8ePraEHe57j4c/aLDFYOWS4xgg\ndPUc6JZowyIktltoSXE9NeOs8XhoYuZbWmZLR6CYyqA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "small r and 100 bit s^-1", "msg": "************", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a", "wx": "00b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137", "wy": "00f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtT5WmxjpNhVn5XE+5p7L55SZEbAldUai\nTD3RN/KagzNM/xxE2MDDO22tuFaMAk+h+7aUzZ5wX1o=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "100-bit r and s^-1", "msg": "************", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0477f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c978129f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7", "wx": "77f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c97812", "wy": "009f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000477f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c978129f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEd/Pr9SclyAmsuxmt8JMSaio6BlymVMIg\nmcl4Ep8ZSNI8UVjsKt/0Vesv7fEHXU7CLWYJd0JKEPc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "r and s^-1 are close to n", "msg": "************", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f31736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699", "wx": "00a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f3", "wy": "1736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f31736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEp/e5nlzcb+yJKO/3c8zfO2ixnUPNtBgJ\n4Zxg8xc2t6DBKpwtcGZxkSkVFCs+Bcie861Je9bDRpk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 302, "comment": "s == 1", "msg": "************", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 303, "comment": "s == 0", "msg": "************", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f6921c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6", "wx": "009cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f69", "wy": "21c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f6921c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEnPAAELSthmNvbMcPtYw7mVwNEuRvxY4k\nsNKPaSHIqKMgzEUMyxXr1xYX9O0l2000E/vfFX0x27Y=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "point at infinity during verify", "msg": "************", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea27427210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f", "wx": "00ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea2742", "wy": "7210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea27427210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErps2NrhUcjLfQ4VZtaEJ4COKc6dq/CXQ\ncOonQnIQpp3kStZFsbA4RQQPRvziOOksExpx5LGEwB8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "edge case for signature malleability", "msg": "************", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108", "wx": "008d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809", "wy": "00aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEjVfU/OYnV3kYiMGTgHb9dm2usuyfG9qK\n1d9ICarekk1+o65au9Bxmn1IZXWdplTPds9+wDEncQg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "edge case for signature malleability", "msg": "************", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0410518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c83f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9", "wx": "10518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c8", "wy": "3f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000410518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c83f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEEFGOt6kmtfe2W+gB7Jsqv3atziXGFS5F\nKjUSyD8yK5q1fqg1KtKb65nvNWtxNDL8xK7zH5AwRdk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "u1 == 1", "msg": "************", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3", "wx": "008a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd", "wy": "3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEil3+3J3Ry5pDnIiz3UcrLmYXP3hmhV22\nu2wS/Tut+7ikxv2A5mUQlXknx4oqoC7O9igW0DVrScM=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "u1 == n - 1", "msg": "************", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0483a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4", "wx": "0083a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff", "wy": "1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000483a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEg6Wfw98pXoTCkLMtC1UKBvmUVvwimOSm\njE8r/xs09IPbMNs6UdgohzLBB9ixqFjNVMOTbhtcEaQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "u2 == 1", "msg": "************", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0458bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a75be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977", "wx": "58bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a7", "wy": "5be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000458bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a75be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEWLraV4ogXW4XByLI7Wx3FQEf4z1+uoae\n0dRIp1vkcwwdLS74geAvAookG319Ow0LSpwFZfy0mXc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "u2 == n - 1", "msg": "************", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d", "wx": "7fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c", "wy": "0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEf8x5m5Gf6Xic4B3ZICcxy32BUVi8bLhG\nh2AkfA+dKVfg3V5MQBJL1eDdG+QcA4/OLNHcgU4K830=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0093c8c651653430cb4f1675fc86b5e82ca04ff2ab1501674476aac169", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780", "wx": "3ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674", "wy": "00fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPd1o9p0L/UetGTcPo9xy6yWCaMK183aI\nUhUWdPvg4VXZTSNzoBpecPGhBSWee4sdL99Nujz0x4A=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d009df50acc33b3625a2d5940dd13dbb97d1f7dd56afff8b7de7545127c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891", "wx": "1cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37", "wy": "00b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEHLH1ZMKev2CjQrO8M8iUXLJ5xsGgEiVc\nh04cN7dRkas7K7cwkU6/oUCAQQlwtx6vT+AeLUi+mJE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00dce8c223f235699d1f5d2dcde4809d013390b59129f783239525c08f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0444e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304", "wx": "44e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36", "wy": "477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000444e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEROMJ62huevfx4swX/VZUKziRCzt5COpU\n+wONNkd+gp1MgzLlsp80StJ6IcGNqySjHOeYW2OiEwQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "edge case for u1", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c074aae944ee7a7d544a5ad0bd06366f872d2250ba3018a63d2a7f2e6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e110129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8", "wx": "00c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e1", "wy": "10129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e110129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExygGRULLUUL17v5jgSTc16GtCzVVhCpH\n3VEI4RASndh469RzEyds7Ib1IeqVhc0QWz3EIRQZk7g=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00aae944ee7a7d544a5ad0bd0636d9455f4e83de0f186f89bca56b3c5c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d63099667220cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108", "wx": "00c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d6309966722", "wy": "0cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d63099667220cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExGwa09PQ346cD1JcIc6Nge+dZil/RC1j\nCZZnIgz6IlOqMamNiWa4WWm/nIGcAZKS72pTrB2yoQg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "edge case for u1", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c55d289dcf4faa894b5a17a0c6db3741bbc4ecbe01d01ea33ee7a4e7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33caa967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12", "wx": "00b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33ca", "wy": "00a967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33caa967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEt7LkjB5g4gkl9Nm2vmAN2DeGqTbJv6sA\nY5wzyqlny8ZQcHOaM3nagNVIQ6GNnBGimjIjSgswPBI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "edge case for u1", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c4ee7a7d544a5ad0bd0636d9e12bc561ce04faaf1312bba3a15601ebc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27ec30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0", "wx": "00f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27e", "wy": "00c30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27ec30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9KPUWYh1r38nQbvWexcztlQbxTJbO8tN\nMmfCfsML8yL1ikXGwqos7VXxddHL9yp8W/xGTXT2ZsA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "edge case for u1", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c361b9cd74d65e79a5874c501bca4973b20347ec97f6de10072d8b46a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0456d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd116cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141", "wx": "56d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd11", "wy": "6cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000456d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd116cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVtHlwdZk9s4vwfy5N6fOIxopSGq/Nsc/\nd6K9EWyygsnXxvwF85nBg+iA6jYu3wQ80o/6yflPIUE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c3739ae9acbcf34b0e98a0379492e764068fd92fedbc200e5b168d4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0430bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd", "wx": "30bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5", "wy": "00f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000430bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMLzoxrfxu7oEC40SHYXVUWesmbLizxz6\nyLAYtfHDhMNb4K4wmly1WrqYI0PSEl8tSlWdjFRTWc0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00a252d685e831b6cf095e4f0535edc5b1609d7c5c7e49a301588a1d3e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49", "wx": "00e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5", "wy": "539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6H5TipeM8YeQi+snpKJH1JaoQh2rH+ef\nh0TStVObn4/ovdz3yXxExVpPwi9NePapYUR6W2E7XEk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u1", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00ee746111f91ab4ce8fae96e6f23fd9d20a24d2e79eea563478c0f566", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f02816c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c", "wx": "113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f0281", "wy": "6c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f02816c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEETosxXyO594RvEXhRUbHKilyW5pyGBFK\nwx8CgWx2W5pGsCFTEqMpL1l5yY03s1iDuqFWKBsbrow=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0423dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74ad661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951", "wx": "23dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74a", "wy": "00d661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000423dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74ad661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEI92cPxpLR4sB+ixemX0Eggc7MpGN5Evl\ng9z3StZhpe1Xmi8J0v9W1rgPJlaNk6I3ymREsMrceVE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u2", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256dfe09e092318e39303dca03688e4ecf300300784312d617e5088c584c", "wx": "00bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256d", "wy": "00fe09e092318e39303dca03688e4ecf300300784312d617e5088c584c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256dfe09e092318e39303dca03688e4ecf300300784312d617e5088c584c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEu85LF9RdJKHIC8jsqYw1nV4eRYBYoAuV\nBkMlbf4J4JIxjjkwPcoDaI5OzzADAHhDEtYX5QiMWEw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a495347bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85", "wx": "035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a4953", "wy": "47bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a495347bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEA19YRGwb2+qlZmCol+v5ZfLRiCDHzQYw\n8EpJU0e9+upgCR9AXgmSnLLA4vbu1T4Icbf+DNWhXYU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u2", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002", "wx": "00911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279", "wy": "508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkRwAM+rEYzJpHLeSDElQ7tVzVHYeEIGh\n6p8SeVCOv3z9Pqtdq97hvhTOgpax/CCs+qwW94JMYAI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0462b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555", "wx": "62b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3", "wy": "124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000462b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEYrKrtwu5x+/ftXGB9DO2R1HxCBMNzhgN\naZLn0xJLOqilPl7t9yqmfm7cxx8Z425q0dCZpZ/9lVU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u2", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1", "wx": "0f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6", "wy": "00defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAED3WTMOeZJ1Kq5qhfe7BZl4S+pT4oj/fu\njVPV5t7+YXNiOA6S+aI8T9zDTglxOqucxEEZQY9vL9E=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138", "wx": "008f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d", "wy": "3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEjy7aQnQqsx9dTPZmiS0dYj79Oyb335qn\nApboDTvq8jXP6kH625jFM6j961hB1p7mX25xkUcR8Tg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u2", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c095b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f", "wx": "2bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c09", "wy": "5b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c095b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEK89DcbMZppHtDi4MSlWoqbmH3shrhjYh\n6XucCVuGYKdMyWSmrwMR7caxzZgPnHvzpsm3+RMqCy8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015", "wx": "00a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec", "wy": "3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEpvJSVo9vvRrgReYCNENZwMIWkRcjdI+a\nPn+t7Dt278dboDC/594t7WhpkeYYPUAkGgW0eWk8cBU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u2", "msg": "************", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe82ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a", "wx": "00a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe8", "wy": "2ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe82ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEp0wcOjHH1JOrLAr4nPXmiGIcqUZtK6HY\ndhw/6Cug0I9Mn3aFbCtxOMjx54C2lZmSsWzN/ZJfSzo=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "edge case for u2", "msg": "************", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f280562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627", "wx": "034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f2", "wy": "0080562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f280562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEA06nJ5glfzPyT2TElDj8Q+j2fdxxcP0S\nfixD8oBWKsybSfLX/MiUIdKl2y6o3QNh+0jYl9RhJic=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "point duplication during verification", "msg": "************", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c3f552f1c2b01651edf5902650fe9ab046f71999ac928edc0087bdb13", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f27fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da", "wx": "034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f2", "wy": "7fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f27fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEA06nJ5glfzPyT2TElDj8Q+j2fdxxcP0S\nfixD8n+p1TNktg0oAzdr3i1aJNBXIvyeBLcnaCue2do=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "duplication bug", "msg": "************", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c3f552f1c2b01651edf5902650fe9ab046f71999ac928edc0087bdb13", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb67ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79", "wx": "3672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb6", "wy": "7ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb67ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAENnK6lxjmDQDqtClcgZ6jZqd43W/WIfqW\nZSWctnrl6EfurqZ0vutjY3npaPeSZVAuQUodRE8Ernk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "comparison with point at infinity ", "msg": "************", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0433eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a", "wx": "33eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648", "wy": "051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000433eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEM+7vv8dyKRNuVrV1FEhj7ZC0wPip4xWB\nbW3mSAUXSd0RSAwUH7WhlGMTFjwBQSZbaKJiFry5k2o=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd61c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f", "wx": "00bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd6", "wy": "1c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd61c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvaA7JLYiQ8YeKItuoemaKIb3AJROsbjw\nRmz/1hxxKjqqzmkzGYm3B+aejeOdfNGutl2XrRgAv38=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df", "wx": "7abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b", "wy": "792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEerugy/8TTdz1TQSEb5VLiCyp+u/f6BiJ\ni/s3i3kvELV5cK5Xu0+wHAiIaEiFWusZhNPW/LK0Et8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da", "wx": "00f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16", "wy": "234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9o2Z4oZTubo+fO2zt4Fl9aVPvpDUufiE\nl5d+FiNNo+qgF4pRtbDCCO8IGN9vZXh5PBrxeHAmuNo=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f10934272004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1", "wx": "065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f1093427", "wy": "2004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f10934272004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEBl2e8TPOgcLWtm6Sg2D5Un+PNrW63TW1\n8Qk0JyAEhSdV93RAoLCLnxZUicBpbotJgdbQSihbD9E=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e83904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6", "wx": "00d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e", "wy": "0083904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e83904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1s6glHLt5XTOHgVGyazQ4c2MupsSHfKe\nidUJLoOQTr+5AuphyYfcBQjgyaflY+Jgn+r3kUCrkdY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "extreme value for k", "msg": "************", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb80e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98", "wx": "00c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb8", "wy": "0e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb80e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAExSCxgAOzVglBR+4vnfEXhXK+2De9iUQ7\nJevOuA4uk6mY+7q+ghkupMhWUc8JqVqw3C49l17nvpg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26ca141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3", "wx": "009dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26c", "wy": "00a141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26ca141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEndC5m7eoMLzH1Vq6xCkS1SWwY8UM83fK\nV3GibKFB/M8Hk8K6JGmpRsLU7SY0QFLGOm1+d5fOlsM=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521", "wx": "3dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f", "wy": "47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPaufGxnnFdF0pzYJIDddVpoYHwVeZvAT\nkYcbb0em2Hwjpbah49CpchMCzALM41813qCOImGb5SE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0456dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87", "wx": "56dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100", "wy": "00e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000456dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVt3huljqMQU7JTXGZiM0TCTHLSFK9b5p\nguiRAOdxCEgGFD6G8rMb2vYigPWzEdDSvbs4WyD8bIc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0494efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a6124414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601", "wx": "0094efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a612", "wy": "4414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000494efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a6124414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAElO/hOH/ARH19vLU3OaDk4N3sGB04LK6m\nRbGmEkQUprHHiQjQ+iBvjy3pUK1KFNHOlNnN2+MuRgE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046286803b952976ee1897013695d3ef2cbb6f977142a042b236572577722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59", "wx": "6286803b952976ee1897013695d3ef2cbb6f977142a042b236572577", "wy": "722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046286803b952976ee1897013695d3ef2cbb6f977142a042b236572577722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEYoaAO5Updu4YlwE2ldPvLLtvl3FCoEKy\nNlcld3IqbOmtPj/SjkUYM0lsY7ircFOIdyFfIElCv1k=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "extreme value for k", "msg": "************", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "testing point duplication", "msg": "************", "sig": "303d021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 349, "comment": "testing point duplication", "msg": "************", "sig": "303c021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "testing point duplication", "msg": "************", "sig": "303d021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 351, "comment": "testing point duplication", "msg": "************", "sig": "303c021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "pseudorandom signature", "msg": "", "sig": "303d021c0364e7d96832614a80216e730c353534d4bffd2c26649c0b4b0e2628021d008f40064b412fe38c5ba9cf664e6172ed48e6e79f0fe5e31a54985dfc", "result": "valid", "flags": []}, {"tcId": 353, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303d021d00f4b68df62b9238363ccc1bbee00deb3fb2693f7894178e14eeac596a021c7f51c9451adacd2bcbc721f7df0643d7cd18a6b52064b507e1912f23", "result": "valid", "flags": []}, {"tcId": 354, "comment": "pseudorandom signature", "msg": "************", "sig": "303d021d00b2970cdec29c70294a18bbc49985efa33acc0af509c326a3977a35e8021c0cea3ed8ebaaf6ee6aef6049a23cbc39f61fcf8fc6be4bab13385579", "result": "valid", "flags": []}, {"tcId": 355, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303d021c7e7b0eb7da8c68a7072b11404ee95a5c407fbfe3d69646802e28ae77021d00d409a2f6bbaae59bb60fc0a092b12fa4e67dc8d088cf19a833322fd6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c519bf185ff4635271961fa491be257231deeea9c53a6ede3b4a89ed1021c486bdad484a6a3134e1471cf56a9df0fac50f773b3e37d6f327617d7", "result": "valid", "flags": []}, {"tcId": 357, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c09fd644898b7cb5d018b52234e7b4ef2b54789afd0ce9c434e9e5515021d00f19309532164ea2053cae55df7bdcbab536c83ea7bfe6fe10d60c1ab", "result": "valid", "flags": []}, {"tcId": 358, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00ec919d4e283ccf1f71a9e3c0f781a36758d3f38b1b78a87a74288e80021c4c4663044a73c79bd88f0dc245ab1a32f89f06f40a704b31e9fabc51", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d00c51760478447217597ecc6f4001bd45088d53c90f53103608bf88aea021d00a201253aa903f9781e8992101d7171d2dd3a5d48c44d8e1d544cd6d7", "result": "valid", "flags": []}, {"tcId": 360, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c76be0112674ec29128823e1af7512e6143872fef30a64e2f1799bd56021c187e503e1a48c27b549fe0a4ce5e581e242c8663fc9efb02d6f2b193", "result": "valid", "flags": []}, {"tcId": 361, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c36245ef126b5b51e459f84eaaad5a495061f0471dc8c23f1c5f16282021c39e31d72a06ba8e14fcf95778e07bc16a2628e39449da8857d506edc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c258682975df8bca7f203f771ebeb478ef637360c860fc386cfb21745021c7663e70188047e41469a2a35c8c330dd900f2340ba82aafd22962a96", "result": "valid", "flags": []}, {"tcId": 363, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d0085c98614f36c0d66f8d87834cae978611b7b4eebf59a46bea1b89ae9021d00d1a18e378dda840e06b60f6279bf0a2231d9fa2d8d2c31e88bc1bdd7", "result": "valid", "flags": []}, {"tcId": 364, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00ca7b7432ba41ff2112e1116fffde89bbd68f5ce67fe5513d16c8e6f7021d00e421b7599e0180798acc2006451603cda2db1d582741116e6033ce5f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c19397fe5d3ecabf80fc624c1bf379564387517c185087dc97d605069021c33b5773e9aaf6c34cb612cfc81efd3bf9c22224e8c4fa1bfccf5c501", "result": "valid", "flags": []}, {"tcId": 366, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c70f24f5c164164bfbb8459aa12a981aa312dbcf00204326ebaaabdc8021d00f5cebee8caedae8662c43501665084b45d2f494fb70d603043543dc4", "result": "valid", "flags": []}, {"tcId": 367, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c0bf2d86ecaa8b56aca5e8f8ebcb45081d078a14555b75f5be8e9b132021d009a55b3ce4734849966b5034ccd9b19f76407ee0241c3f58e7b8fc89a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00bfc5dc4434cd09369610687d38d2d418b63fd475dea246a456b25a3a021d00b171dfa6cf722f20816370a868785da842b37bac31d7b78e6751fc50", "result": "valid", "flags": []}, {"tcId": 369, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d008fdbe8da646c5642d767c7dbeb3872b1edab6e37365805f0e94ce0a9021d00bcf35ab81222883dd3526cb0cf93138f4687cd0b10c2b0a126385161", "result": "valid", "flags": []}, {"tcId": 370, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00e23a11275848fd4f8b6f4ac4fc305eae981d3b7dc453e5a980c46422021c1a875693f24a03ea1614c4c3bbd0dd7221429f22b337ea7d98348ca4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c76645164ff9af3a1a9205fda2eef326d2bffc795dcc4829547fe01dd021d00b65bba503719314b27734dd06b1395d540af8396029b78b84e0149eb", "result": "valid", "flags": []}, {"tcId": 372, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c32fa0ca7e07f1f86ac350734994e1f31b6da9c82f93dced2b983c29c021c7b7891282206a45711bdfcb2a102b5d289df84ff5778548603574004", "result": "valid", "flags": []}, {"tcId": 373, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c2d5492478ca64e5111dfd8521867b6477b7e78227849ad090b855694021d00a532f5a2fa3594af81cd5928b81b4057da717be5fb42a3a86c68190d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c191eee5daf55cd499e8539cb2cff797cfec5d566d2027bf9f8d64693021d00dadfeae8131f64d96b94fd340197caa2bc04818554812feef3343070", "result": "valid", "flags": []}, {"tcId": 375, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00e0e2c08180b8a207ee9105a7d379fa112368e8370fa09dfde4a45c45021d00c717bc0860e016e7ce48f8fe6a299b36906a6055adad93b416ce8838", "result": "valid", "flags": []}, {"tcId": 376, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c1b919ef93532292743bb2e1b7b4894fd847c6e5de52a08e1b0f2dcfb021d00c2d30d6b7594d8dbd261491ae1d58779505b075b64e5564dc97a418b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00e75db49ed33ff2885ea6100cc95b8fe1b9242ea4248db07bcac2e020021c796c866142ae8eb75bb0499c668c6fe45497692fbcc66b37c2e4624f", "result": "valid", "flags": []}, {"tcId": 378, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c1f81cd924362ec825890307b9b3936e0d8f728a7c84bdb43c5cf0433021c39d3e46a03040ad41ac026b18e0629f6145e3dc8d1e6bbe200c8482b", "result": "valid", "flags": []}, {"tcId": 379, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c00fda613aa67ca42673ad4309f3f0f05b2569f3dee63f4aa9cc54cf3021c1e5a64b68a37e5b201c918303dc7a40439aaeacf019c5892a8f6d0ce", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00b932b3f7e6467e1ec7a561f31160248c7f224550a8508788634b53ce021d00a0c5312acf9e801aff6d6fc98550cfa712bbf65937165a36f2c32dc9", "result": "valid", "flags": []}, {"tcId": 381, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00e509593fb09245ee8226ce72786b0cc352be555a7486be628f4fd00c021c0b7abde0061b1e07bf13319150a4ff6a464abab636ab4e297b0d7633", "result": "valid", "flags": []}, {"tcId": 382, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c6e54f941204d4639b863c98a65b7bee318d51ab1900a8f345eac6f07021c0da5054829214ecde5e10579b36a2fe6426c24b064ed77c38590f25c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d0085ea4ab3ffdc992330c0ca8152faf991386bce82877dbb239ba654f6021c0806c6baf0ebea4c1aaa190e7d4325d46d1f7789d550632b70b5fc9b", "result": "valid", "flags": []}, {"tcId": 384, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c44d53debb646b73485402eab2d099081b97b1243c025b624f0dd67ea021d00e5de789a7d4b77eac6d7bba41658e6e4dc347dabed2f9680c04a6f55", "result": "valid", "flags": []}, {"tcId": 385, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c1526eb2f657ebea9af4ca184b975c02372c88e24e835f3f5774c0e12021c1f1ecce38ee52372cb201907794de17b6d6c1afa13c316c51cb07bc7", "result": "valid", "flags": []}]}]}