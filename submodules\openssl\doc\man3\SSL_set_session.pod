=pod

=head1 NAME

SSL_set_session - set a TLS/SSL session to be used during TLS/SSL connect

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_set_session(SSL *ssl, SSL_SESSION *session);

=head1 DESCRIPTION

SSL_set_session() sets B<session> to be used when the TLS/SSL connection
is to be established. SSL_set_session() is only useful for TLS/SSL clients.
When the session is set, the reference count of B<session> is incremented
by 1. If the session is not reused, the reference count is decremented
again during SSL_connect(). Whether the session was reused can be queried
with the L<SSL_session_reused(3)> call.

If there is already a session set inside B<ssl> (because it was set with
SSL_set_session() before or because the same B<ssl> was already used for
a connection), SSL_SESSION_free() will be called for that session. If that old
session is still B<open>, it is considered bad and will be removed from the
session cache (if used). A session is considered open, if L<SSL_shutdown(3)> was
not called for the connection (or at least L<SSL_set_shutdown(3)> was used to
set the SSL_SENT_SHUTDOWN state).

=head1 NOTES

SSL_SESSION objects keep internal link information about the session cache
list, when being inserted into one SSL_CTX object's session cache.
One SSL_SESSION object, regardless of its reference count, must therefore
only be used with one SSL_CTX object (and the SSL objects created
from this SSL_CTX object).

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item Z<>0

The operation failed; check the error stack to find out the reason.

=item Z<>1

The operation succeeded.

=back

=head1 SEE ALSO

L<ssl(7)>, L<SSL_SESSION_free(3)>,
L<SSL_get_session(3)>,
L<SSL_session_reused(3)>,
L<SSL_CTX_set_session_cache_mode(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
