k5_base64_decode
k5_base64_encode
k5_bcmp
k5_buf_init_fixed
k5_buf_init_dynamic
k5_buf_init_dynamic_zap
k5_buf_add
k5_buf_add_len
k5_buf_add_fmt
k5_buf_add_vfmt
k5_buf_get_space
k5_buf_truncate
k5_buf_status
k5_buf_free
k5_set_error
k5_vset_error
k5_get_error
k5_free_error
k5_clear_error
k5_set_error_info_callout_fn
k5_hashtab_add
k5_hashtab_create
k5_hashtab_free
k5_hashtab_get
k5_hashtab_remove
k5_hex_decode
k5_hex_encode
k5_json_array_add
k5_json_array_create
k5_json_array_fmt
k5_json_array_get
k5_json_array_length
k5_json_array_set
k5_json_bool_create
k5_json_bool_value
k5_json_decode
k5_json_encode
k5_json_get_tid
k5_json_null_create
k5_json_null_create_val
k5_json_number_create
k5_json_number_value
k5_json_object_count
k5_json_object_create
k5_json_object_get
k5_json_object_iterate
k5_json_object_set
k5_json_release
k5_json_retain
k5_json_string_create
k5_json_string_create_base64
k5_json_string_create_len
k5_json_string_unbase64
k5_json_string_utf8
k5_os_mutex_init
k5_os_mutex_destroy
k5_os_mutex_lock
k5_os_mutex_unlock
k5_once
k5_path_isabs
k5_path_join
k5_path_split
k5_strerror_r
k5_utf8_to_utf16le
k5_utf16le_to_utf8
k5_dir_filenames
k5_free_filenames
krb5int_key_register
krb5int_key_delete
krb5int_getspecific
krb5int_setspecific
krb5int_getaddrinfo
krb5int_freeaddrinfo
krb5int_gai_strerror
krb5int_getnameinfo
krb5int_in6addr_any
krb5int_pthread_loaded
krb5int_open_plugin
krb5int_close_plugin
krb5int_get_plugin_data
krb5int_get_plugin_func
krb5int_open_plugin_dirs
krb5int_close_plugin_dirs
krb5int_get_plugin_dir_data
krb5int_get_plugin_dir_func
krb5int_free_plugin_dir_data
krb5int_free_plugin_dir_func
krb5int_mutex_alloc
krb5int_mutex_free
krb5int_mutex_lock
krb5int_mutex_unlock
krb5int_gmt_mktime
krb5int_ucs4_to_utf8
krb5int_utf8_to_ucs4
krb5int_utf8_lentab
krb5int_utf8_mintab
krb5int_utf8_next
krb5int_zap
