.. _rcache_definition:

replay cache
============

A replay cache (or "rcache") keeps track of all authenticators
recently presented to a service.  If a duplicate authentication
request is detected in the replay cache, an error message is sent to
the application program.

The replay cache interface, like the credential cache and
:ref:`keytab_definition` interfaces, uses `type:residual` strings to
indicate the type of replay cache and any associated cache naming
data to use.

Background information
----------------------

Some Kerberos or GSSAPI services use a simple authentication mechanism
where a message is sent containing an authenticator, which establishes
the encryption key that the client will use for talking to the
service.  But nothing about that prevents an eavesdropper from
recording the messages sent by the client, establishing a new
connection, and re-sending or "replaying" the same messages; the
replayed authenticator will establish the same encryption key for the
new session, and the following messages will be decrypted and
processed.  The attacker may not know what the messages say, and can't
generate new messages under the same encryption key, but in some
instances it may be harmful to the user (or helpful to the attacker)
to cause the server to see the same messages again a second time.  For
example, if the legitimate client sends "delete first message in
mailbox", a replay from an attacker may delete another, different
"first" message.  (Protocol design to guard against such problems has
been discussed in :rfc:`4120#section-10`.)

Even if one protocol uses further protection to verify that the client
side of the connection actually knows the encryption keys (and thus is
presumably a legitimate user), if another service uses the same
service principal name, it may be possible to record an authenticator
used with the first protocol and "replay" it against the second.

The replay cache mitigates these attacks somewhat, by keeping track of
authenticators that have been seen until their five-minute window
expires.  Different authenticators generated by multiple connections
from the same legitimate client will generally have different
timestamps, and thus will not be considered the same.

This mechanism isn't perfect.  If a message is sent to one application
server but a man-in-the-middle attacker can prevent it from actually
arriving at that server, the attacker could then use the authenticator
(once!) against a different service on the same host.  This could be a
problem if the message from the client included something more than
authentication in the first message that could be useful to the
attacker (which is uncommon; in most protocols the server has to
indicate a successful authentication before the client sends
additional messages), or if the simple act of presenting the
authenticator triggers some interesting action in the service being
attacked.

Replay cache types
------------------

Unlike the credential cache and keytab interfaces, replay cache types
are in lowercase.  The following types are defined:

#. **none** disables the replay cache.  The residual value is ignored.

#. **file2** (new in release 1.18) uses a hash-based format to store
   replay records.  The file may grow to accommodate hash collisions.
   The residual value is the filename.

#. **dfl** is the default type if no environment variable or
   configuration specifies a different type.  It stores replay data in
   a file2 replay cache with a filename based on the effective uid.
   The residual value is ignored.

For the dfl type, the location of the replay cache file is determined
as follows:

#. The directory is taken from the **KRB5RCACHEDIR** environment
   variable, or the **TMPDIR** environment variable, or a temporary
   directory determined at configuration time such as ``/var/tmp``, in
   descending order of preference.

#. The filename is ``krb5_EUID.rcache2`` where EUID is the effective
   uid of the process.

#. The file is opened without following symbolic links, and ownership
   of the file is verified to match the effective uid.

On Windows, the directory for the dfl type is the local appdata
directory, unless overridden by the **KRB5RCACHEDIR** environment
variable.  The filename on Windows is ``krb5.rcache2``, and the file
is opened normally.

Default replay cache name
-------------------------

The default replay cache name is determined by the following, in
descending order of priority:

#. The **KRB5RCACHENAME** environment variable (new in release 1.18).

#. The **KRB5RCACHETYPE** environment variable.  If this variable is
   set, the residual value is empty.

#. The **default_rcache_name** profile variable in :ref:`libdefaults`
   (new in release 1.18).

#. If none of the above are set, the default replay cache name is
   ``dfl:``.
