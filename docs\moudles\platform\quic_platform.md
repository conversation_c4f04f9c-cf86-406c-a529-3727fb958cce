# MsQuic Platform模块源码分析

## 平台支持
MsQuic目前官方支持Windows（x64，arm64）和Linux（x64，arm64，arm32），以下为平台相关的配置
### Windows
在Windows上MsQuic默认使用内置的Schannel来支持TLS 1.3的功能。MsQuic以msquic.sys驱动的形式整合到Windows内核中了，用来支持内置的HTTP和SMB特性。用户模式程序可以使用源码编译的msquic.dll。如果需要支持Windows 10的话也可以使用OpenSSL代替Windows的Schannel。
### Linux
在Linux上MsQuic依赖TLS 1.3的功能，OpenSSL 1.1和3.1都支持。libmsquic一般依赖系统上默认安装的OpenSSL版本的libcrypto
### other
从MsQuic源码中也可以看到对Darwin(macOS/iOS)内核的支持

MsQuic 的 `platform` 模块通过抽象和实现跨平台的功能，支持多个操作系统和平台。更详细的介绍如下：

---

### **1. Windows 平台**
#### **支持功能**
- **数据路径**:
  - 使用 `datapath_win.c` 和 `datapath_winuser.c` 提供基于 Windows 网络 API 的数据路径实现。
  - 支持原始数据路径（`datapath_raw_win.c` 和 `datapath_raw_socket_win.c`）。
  - 支持 XDP（eXpress Data Path）优化（`datapath_raw_xdp_win.c`）。
- **TLS 支持**:
  - 使用 Schannel 提供 TLS 支持（`tls_schannel.c`）。
- **存储支持**:
  - 使用 Windows 注册表实现配置存储（`storage_winuser.c`）。
- **系统功能**:
  - 提供线程、锁、计时器等 Windows 特定实现（`platform_winuser.c`）。

#### **依赖库**
- `wbemuuid`
- `winmm`
- `secur32`（用于 Schannel）

---

### **2. Linux/Android 平台**
#### **支持功能**
- **数据路径**:
  - 使用 `datapath_linux.c` 和 `datapath_epoll.c` 提供基于 epoll 的数据路径实现。
  - 支持原始数据路径（`datapath_raw_linux.c` 和 `datapath_raw_socket_linux.c`）。
  - 支持 XDP 优化（`datapath_raw_xdp_linux.c` 和 `datapath_raw_xdp_linux_kern.c`）。
- **TLS 支持**:
  - 使用 OpenSSL 提供 TLS 支持（`tls_openssl.c`）。
- **存储支持**:
  - 使用 POSIX 文件系统实现配置存储（`storage_posix.c`）。
- **系统功能**:
  - 提供线程、锁、计时器等 POSIX 实现（`platform_posix.c`）。

#### **依赖库**
- `libnl-3` 和 `libnl-route-3`（用于网络配置）
- `libxdp` 和 `libbpf`（用于 XDP 支持）
- `libelf`、`libz` 和 `libzstd`（用于静态链接）

---

### **3. Darwin(macOS/iOS) 平台**
#### **支持功能**
- **数据路径**:
  - 使用 `datapath_kqueue.c` 提供基于 kqueue 的数据路径实现。
- **TLS 支持**:
  - 使用 OpenSSL 提供 TLS 支持（`tls_openssl.c`）。
- **存储支持**:
  - 使用 POSIX 文件系统实现配置存储（`storage_posix.c`）。
- **系统功能**:
  - 提供线程、锁、计时器等 POSIX 实现（`platform_posix.c`）。

#### **依赖库**
- OpenSSL
- macOS/iOS 系统框架（如 `CoreFoundation` 和 `Security`）

---

### **4. 构建逻辑**
`platform` 模块的构建逻辑在 CMakeLists.txt 中定义：
- 根据平台选择不同的源文件：
  ```cmake
  if("${CX_PLATFORM}" STREQUAL "windows")
      set(SOURCES ${SOURCES} platform_winuser.c storage_winuser.c datapath_win.c)
  elseif(CX_PLATFORM STREQUAL "linux")
      set(SOURCES ${SOURCES} platform_posix.c storage_posix.c datapath_linux.c)
  elseif(CX_PLATFORM STREQUAL "darwin")
      set(SOURCES ${SOURCES} platform_posix.c storage_posix.c datapath_kqueue.c)
  endif()
  ```
- 根据 TLS 提供程序选择不同的实现：
  ```cmake
  if (QUIC_TLS STREQUAL "schannel")
      set(SOURCES ${SOURCES} tls_schannel.c)
  elseif(QUIC_TLS STREQUAL "openssl")
      set(SOURCES ${SOURCES} tls_openssl.c)
  endif()
  ```

---

quic_platform.h 是 MsQuic 的平台抽象层核心头文件，主要用于定义跨平台的通用接口和工具。它的主要功能包括：
- 跨平台支持: 提供线程、锁、事件队列等抽象。
- 平台初始化: 定义平台的初始化和清理函数。
- 内存管理: 提供内存池和资源分配的抽象。
- 通用工具: 提供数学宏、时间转换宏和列表操作工具。

## platform对外接口
在src/inc/quic_platform.h头文件中对外提供了统一的接口，通过判断所处的平台包含对应平台相关的头文件，相关代码如下：
```c
#ifdef _KERNEL_MODE
#define CX_PLATFORM_TYPE 1
#include "quic_platform_winkernel.h"
#elif _WIN32
#define CX_PLATFORM_TYPE 2
#include "quic_platform_winuser.h"
#elif CX_PLATFORM_LINUX
#define CX_PLATFORM_TYPE 3
#define CX_PLATFORM_USES_TLS_BUILTIN_CERTIFICATE 1
#include "quic_platform_posix.h"
#elif CX_PLATFORM_DARWIN
#define CX_PLATFORM_TYPE 4
#include "quic_platform_posix.h"
#else
#define CX_PLATFORM_TYPE 0xFF
#error "Unsupported Platform"
#endif
```
这里可以看到平台对应的头文件:  
Windows kernel mode -> quic_platform_winkernel.h  
Windows user mode -> quic_platform_winuser.h  
linux、darwin -> quic_platform_posix.h  
下面我们从quic_platform.h对外提供的接口开始分析源码。
点击以下带链接的函数可进入对应函数的详细分析。

---

### **CxPlatSystemLoad**
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemLoad(
    void
    );
```
- 库的初始化，在main、DLLMain 或者 DriverEntry中调用

---

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemLoad(
    void
    )
{
    // 增加系统引用计数。如果引用计数大于 1，说明系统已经加载，无需重复初始化。
    if (InterlockedIncrement16(&CxPlatSystemRef) != 1) {
        return;
    }

#ifdef QUIC_EVENTS_MANIFEST_ETW
    // 如果启用了 QUIC_EVENTS_MANIFEST_ETW，则注册 ETW 事件提供程序。
    EventRegisterMicrosoft_Quic();
#endif

    // 查询高精度计时器的频率，并存储在全局变量 CxPlatPerfFreq 中。
    (void)QueryPerformanceFrequency((LARGE_INTEGER*)&CxPlatPerfFreq);

    // 初始化全局平台状态。
    CxPlatform.Heap = NULL;

#ifdef DEBUG
    // 在调试模式下，初始化内存分配失败的模拟参数。
    CxPlatform.AllocFailDenominator = 0;
    CxPlatform.AllocCounter = 0;
#endif

    // 记录系统加载日志。
    QuicTraceLogInfo(
        WindowsUserLoaded,
        "[ dll] Loaded");
}
```

- Windows kernel
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemLoad(
    void
    )
{
    PAGED_CODE(); // 确保此函数只能在被动 IRQL（PASSIVE_LEVEL）下调用。

#ifdef QUIC_EVENTS_MANIFEST_ETW
    // 如果启用了 QUIC_EVENTS_MANIFEST_ETW，则注册 ETW 事件提供程序。
    EventRegisterMicrosoft_Quic();
#endif

    // 查询高精度计时器的频率，并存储在全局变量 CxPlatPerfFreq 中。
    (VOID)KeQueryPerformanceCounter((LARGE_INTEGER*)&CxPlatPerfFreq);

    // 初始化全局平台状态。
    CxPlatform.RngAlgorithm = NULL; // 随机数生成算法初始化为 NULL。

#ifdef DEBUG
    // 在调试模式下，初始化内存分配失败的模拟参数。
    CxPlatform.AllocFailDenominator = 0; // 分配失败的概率分母。
    CxPlatform.AllocCounter = 0;         // 分配计数器。
#endif

    // 记录系统加载日志。
    QuicTraceLogInfo(
        WindowsKernelLoaded,
        "[ sys] Loaded");
}
```
- posix
```c
void
CxPlatSystemLoad(
    void
    )
{
    // 增加系统引用计数。如果引用计数大于 1，说明系统已经加载，无需重复初始化。
    if (InterlockedIncrement16(&CxPlatSystemRef) != 1) {
        return;
    }

#if defined(CX_PLATFORM_DARWIN)
    // 如果是 macOS 平台，处理特殊情况：
    // - arm64 macOS 无法获取当前处理器，因此假设为单核。
    // - Intel macOS 的 CPUID 返回值可能不正确，因此也假设为单核。
    CxPlatProcessorCount = 1;
#else
    // 获取当前系统的处理器数量。
    CxPlatProcessorCount = (uint32_t)sysconf(_SC_NPROCESSORS_ONLN);
#endif

#ifdef CXPLAT_NUMA_AWARE
    // 如果启用了 NUMA（非统一内存访问）支持：
    if (numa_available() >= 0) {
        // 获取 NUMA 节点数量。
        CxPlatNumaNodeCount = (uint32_t)numa_num_configured_nodes();

        // 分配每个 NUMA 节点的 CPU 集合。
        CxPlatNumaNodeMasks =
            CXPLAT_ALLOC_NONPAGED(sizeof(cpu_set_t) * CxPlatNumaNodeCount, QUIC_POOL_PLATFORM_PROC);
        CXPLAT_FRE_ASSERT(CxPlatNumaNodeMasks);

        // 初始化每个 NUMA 节点的 CPU 集合。
        for (uint32_t n = 0; n < CxPlatNumaNodeCount; ++n) {
            CPU_ZERO(&CxPlatNumaNodeMasks[n]);
            CXPLAT_FRE_ASSERT(numa_node_to_cpus_compat((int)n, CxPlatNumaNodeMasks[n].__bits, sizeof(cpu_set_t)) >= 0);
        }
    } else {
        // 如果 NUMA 不可用，设置节点数量为 0。
        CxPlatNumaNodeCount = 0;
    }
#endif // CXPLAT_NUMA_AWARE

#ifdef DEBUG
    // 在调试模式下，初始化内存分配失败的模拟参数。
    CxPlatform.AllocFailDenominator = 0; // 分配失败的概率分母。
    CxPlatform.AllocCounter = 0;         // 分配计数器。
#endif

    // 检查是否需要加载 LTTng 提供程序。
    long ShouldLoad = 1;
    char *DisableValue = getenv("QUIC_LTTng");
    if (DisableValue != NULL) {
        ShouldLoad = strtol(DisableValue, NULL, 10);
    }

    if (!ShouldLoad) {
        goto Exit; // 如果禁用了 LTTng 提供程序加载，直接退出。
    }

    // 获取当前共享库（libmsquic.so）的路径。
    Dl_info Info;
    int Succeeded = dladdr((void *)CxPlatSystemLoad, &Info);
    if (!Succeeded) {
        goto Exit; // 如果无法获取路径信息，直接退出。
    }

    size_t PathLen = strlen(Info.dli_fname);

    // 找到路径中最后一个斜杠的位置，以获取目录路径。
    int LastTrailingSlashLen = -1;
    for (int i = PathLen; i >= 0; i--) {
        if (Info.dli_fname[i] == '/') {
            LastTrailingSlashLen = i + 1;
            break;
        }
    }

    if (LastTrailingSlashLen == -1) {
        goto Exit; // 如果未找到斜杠，直接退出。
    }

    // 构造完整的 LTTng 提供程序路径。
    size_t TpLibNameLen = strlen(TpLibName);
    size_t ProviderFullPathLength = TpLibNameLen + LastTrailingSlashLen + 1;

    char* ProviderFullPath = CXPLAT_ALLOC_PAGED(ProviderFullPathLength, QUIC_POOL_PLATFORM_TMP_ALLOC);
    if (ProviderFullPath == NULL) {
        goto Exit; // 如果内存分配失败，直接退出。
    }

    // 复制路径和文件名到完整路径缓冲区。
    CxPlatCopyMemory(ProviderFullPath, Info.dli_fname, LastTrailingSlashLen);
    CxPlatCopyMemory(ProviderFullPath + LastTrailingSlashLen, TpLibName, TpLibNameLen);
    ProviderFullPath[LastTrailingSlashLen + TpLibNameLen] = '\0';

    // 加载 LTTng 提供程序。
    // 如果加载失败，不影响程序运行，只是无法使用跟踪功能。
    dlopen(ProviderFullPath, RTLD_NOW | RTLD_GLOBAL);

    // 释放分配的路径缓冲区。
    CXPLAT_FREE(ProviderFullPath, QUIC_POOL_PLATFORM_TMP_ALLOC);

Exit:
    // 记录系统加载日志。
    QuicTraceLogInfo(
        PosixLoaded,
        "[ dso] Loaded");
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{系统引用计数加 1 后是否为 1?}:::decision
    B -- 否 --> Z([结束]):::startend
    B -- 是 --> C{是否为 Darwin 平台?}:::decision
    C -- 是 --> D(设置处理器数量为 1):::process
    C -- 否 --> E(通过 sysconf 获取处理器数量):::process
    D --> F{是否启用 NUMA 感知?}:::decision
    E --> F
    F -- 是 --> G{NUMA 是否可用?}:::decision
    F -- 否 --> H(跳过 NUMA 初始化):::process
    G -- 是 --> I(获取 NUMA 节点数量):::process
    G -- 否 --> J(设置 NUMA 节点数量为 0):::process
    I --> K(分配内存存储 NUMA 节点掩码):::process
    K --> L(初始化每个 NUMA 节点的掩码):::process
    L --> M(设置调试模式下的分配失败分母和计数器):::process
    J --> M
    H --> M
    M --> N{环境变量 QUIC_LTTng 是否禁用加载?}:::decision
    N -- 是 --> O(跳过加载跟踪点提供程序):::process
    N -- 否 --> P(获取当前共享对象路径):::process
    P --> Q{获取路径是否成功?}:::decision
    Q -- 否 --> O
    Q -- 是 --> R(计算跟踪点提供程序的完整路径):::process
    R --> S(分配内存存储完整路径):::process
    S --> T{内存分配是否成功?}:::decision
    T -- 否 --> O
    T -- 是 --> U(构建完整路径字符串):::process
    U --> V(加载跟踪点提供程序):::process
    V --> W(释放存储路径的内存):::process
    W --> O
    O --> X(记录加载日志):::process
    X --> Z
```

---

### **CxPlatSystemUnload**
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemUnload(
    void
    );
```
- 库的反初始化，在main、DLLMain 或者 DriverEntry中退出函数时调用

---

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemUnload(
    void
    )
{
    // 减少系统引用计数。如果引用计数不为 0，说明系统仍在使用，直接返回。
    if (InterlockedDecrement16(&CxPlatSystemRef) != 0) {
        return;
    }

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        WindowsUserUnloaded,
        "[ dll] Unloaded");

#ifdef QUIC_EVENTS_MANIFEST_ETW
    // 如果启用了 QUIC_EVENTS_MANIFEST_ETW，则注销 ETW 事件提供程序。
    EventUnregisterMicrosoft_Quic();
#endif
}
```

- Windows kernel
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSystemUnload(
    void
    )
{
    PAGED_CODE(); // 确保此函数只能在被动 IRQL（PASSIVE_LEVEL）下调用。

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        WindowsKernelUnloaded,
        "[ sys] Unloaded");

#ifdef QUIC_EVENTS_MANIFEST_ETW
    // 如果启用了 QUIC_EVENTS_MANIFEST_ETW，则注销 ETW 事件提供程序。
    EventUnregisterMicrosoft_Quic();
#endif
}
```

- posix
```c
void
CxPlatSystemUnload(
    void
    )
{
    // 减少系统引用计数。如果引用计数不为 0，说明系统仍在使用，直接返回。
    if (InterlockedDecrement16(&CxPlatSystemRef) != 0) {
        return;
    }

#ifdef CXPLAT_NUMA_AWARE
    // 如果启用了 NUMA（非统一内存访问）支持，释放分配的 NUMA 节点相关资源。
    CXPLAT_FREE(CxPlatNumaNodeMasks, QUIC_POOL_PLATFORM_PROC);
    CxPlatNumaNodeMasks = NULL;
#endif

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        PosixUnloaded,
        "[ dso] Unloaded");
}
```

---

### [CxPlatInitialize](func/CxPlatInitialize.md ':include')
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatInitialize(
    void
    );
```
- 初始化 QUIC 平台抽象层（PAL）的必要组件，包括处理器信息、随机数生成器、加密模块等。

---

### [CxPlatUninitialize](func/CxPlatUninitialize.md ':include')
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatUninitialize(
    void
    );
```
- 该函数确保在卸载时清理所有已初始化的资源，避免资源泄漏。

---

### `CxPlatList` 系列函数是用于操作双向链表和单向链表的工具函数，提供了对链表的初始化、插入、删除、移动等操作的封装。这些函数主要用于管理内存或数据结构中的节点，确保链表操作的安全性和一致性。

---

### CxPlatListInitializeHead
```c
void CxPlatListInitializeHead(CXPLAT_LIST_ENTRY* ListHead);
```
- 初始化链表头节点，使其指向自身，表示链表为空。

---

### CxPlatListIsEmpty
```c
BOOLEAN CxPlatListIsEmpty(const CXPLAT_LIST_ENTRY* ListHead);
```
- 检查链表是否为空。

---

### CxPlatListInsertHead
```c
void CxPlatListInsertHead(CXPLAT_LIST_ENTRY* ListHead, CXPLAT_LIST_ENTRY* Entry);
```
- 将一个新节点插入到链表头部。

---

### CxPlatListInsertTail
```c
void CxPlatListInsertTail(CXPLAT_LIST_ENTRY* ListHead, CXPLAT_LIST_ENTRY* Entry);
```
- 将一个新节点插入到链表尾部。

---

### CxPlatListInsertAfter
```c
void CxPlatListInsertAfter(CXPLAT_LIST_ENTRY* ListEntry, CXPLAT_LIST_ENTRY* NewEntry);
```
- 将一个新节点插入到指定节点之后。

---

### CxPlatListRemoveHead
```c
CXPLAT_LIST_ENTRY* CxPlatListRemoveHead(CXPLAT_LIST_ENTRY* ListHead);
```
- 从链表头部移除一个节点，并返回该节点。

---

### CxPlatListEntryRemove
```c
BOOLEAN CxPlatListEntryRemove(CXPLAT_LIST_ENTRY* Entry);
```
- 从链表中移除指定节点，如果移除后链表为空，返回 `TRUE`；否则返回 `FALSE`。

---

### CxPlatListMoveItems
```c
void CxPlatListMoveItems(CXPLAT_LIST_ENTRY* Source, CXPLAT_LIST_ENTRY* Destination);
```
- 将一个链表的所有节点移动到另一个链表中。

---

### CxPlatListPushEntry
```c
void CxPlatListPushEntry(CXPLAT_SLIST_ENTRY* ListHead, CXPLAT_SLIST_ENTRY* Entry);
```
- 将一个新节点插入到单向链表头部。

---

### CxPlatListPopEntry
```c
CXPLAT_SLIST_ENTRY* CxPlatListPopEntry(CXPLAT_SLIST_ENTRY* ListHead);
```
- 从单向链表头部移除一个节点，并返回该节点。

---

### CXPLAT_WORKER_POOL
```c
typedef struct CXPLAT_WORKER_POOL {
    CXPLAT_WORKER* Workers;         // 指向工作线程数组的指针
    CXPLAT_LOCK WorkerLock;         // 用于保护线程池的锁
    CXPLAT_RUNDOWN_REF Rundown;     // 用于管理线程池的生命周期
    uint32_t WorkerCount;           // 工作线程的数量
} CXPLAT_WORKER_POOL;
```
- 管理和协调一组工作线程（`CXPLAT_WORKER`），用于执行异步任务或处理并发操作。
- CxPlatWorkerPoolInit和CxPlatWorkerPoolUninit来初始化和反初始化此结构
- 从以下代码可以知道在内核模式中不支持CxPlatWorkerPoolInit和CxPlatWorkerPoolUninit
```c
#ifdef _KERNEL_MODE // Not supported on kernel mode
#define CxPlatWorkerPoolInit(WorkerPool) UNREFERENCED_PARAMETER(WorkerPool)
#define CxPlatWorkerPoolUninit(WorkerPool) UNREFERENCED_PARAMETER(WorkerPool)
#else
void
CxPlatWorkerPoolInit(
    _In_ CXPLAT_WORKER_POOL* WorkerPool
    );

void
CxPlatWorkerPoolUninit(
    _In_ CXPLAT_WORKER_POOL* WorkerPool
    );
#endif
```
下面来看看CxPlatWorkerPoolInit和CxPlatWorkerPoolUninit的实现：

### CxPlatWorkerPoolInit
```c
void
CxPlatWorkerPoolInit(
    _In_ CXPLAT_WORKER_POOL* WorkerPool // 指向工作线程池的指针
    )
{
    // 确保传入的 WorkerPool 不为空。
    CXPLAT_DBG_ASSERT(WorkerPool);

    // 将 WorkerPool 的内存清零，初始化为默认状态。
    CxPlatZeroMemory(WorkerPool, sizeof(*WorkerPool));

    // 初始化 WorkerPool 的锁，用于保护线程池的并发访问。
    CxPlatLockInitialize(&WorkerPool->WorkerLock);
}
```

---

### CxPlatWorkerPoolUninit
```c
void
CxPlatWorkerPoolUninit(
    _In_ CXPLAT_WORKER_POOL* WorkerPool // 指向工作线程池的指针
    )
{
    // 确保传入的 WorkerPool 不为空。
    CXPLAT_DBG_ASSERT(WorkerPool);

    // 检查是否有已初始化的工作线程。
    if (WorkerPool->Workers != NULL) {
        // 等待所有线程完成其任务。
        CxPlatRundownReleaseAndWait(&WorkerPool->Rundown);

        // 遍历所有工作线程并清理资源。
        for (uint32_t i = 0; i < WorkerPool->WorkerCount; ++i) {
            CXPLAT_WORKER* Worker = &WorkerPool->Workers[i];

            // 标记线程正在停止。
            Worker->StoppingThread = TRUE;

            // 将关闭任务添加到事件队列中。
            CxPlatEventQEnqueue(&Worker->EventQ, &Worker->ShutdownSqe);

            // 等待线程完成并删除线程。
            CxPlatThreadWait(&Worker->Thread);
            CxPlatThreadDelete(&Worker->Thread);

#if DEBUG
            // 在调试模式下，确保线程已启动并完成。
            CXPLAT_DBG_ASSERT(Worker->ThreadStarted);
            CXPLAT_DBG_ASSERT(Worker->ThreadFinished);
#endif

            // 标记线程已销毁。
            Worker->DestroyedThread = TRUE;

            // 清理与线程相关的资源。
            CxPlatSqeCleanup(&Worker->EventQ, &Worker->UpdatePollSqe);
            CxPlatSqeCleanup(&Worker->EventQ, &Worker->WakeSqe);
            CxPlatSqeCleanup(&Worker->EventQ, &Worker->ShutdownSqe);
            CxPlatEventQCleanup(&Worker->EventQ);

            // 确保动态池列表为空。
            CXPLAT_DBG_ASSERT(CxPlatListIsEmpty(&Worker->DynamicPoolList));

            // 清理线程的锁。
            CxPlatLockUninitialize(&Worker->ECLock);
        }

        // 释放工作线程的内存。
        CXPLAT_FREE(WorkerPool->Workers, QUIC_POOL_PLATFORM_WORKER);
        WorkerPool->Workers = NULL;

        // 清理线程池的 Rundown 资源。
        CxPlatRundownUninitialize(&WorkerPool->Rundown);
    }

    // 清理线程池的锁。
    CxPlatLockUninitialize(&WorkerPool->WorkerLock);
}
```

```mermaid
graph TD
    A([开始]):::startend --> B(断言 WorkerPool 不为空):::process
    B --> C{Workers 是否存在}:::decision
    C -->|否| F(反初始化 WorkerLock):::process
    C -->|是| D(等待 Rundown 完成):::process
    D --> E(清理所有 Worker):::process
    E --> E1(释放 Workers 内存):::process
    E1 --> E2(反初始化 Rundown):::process
    E2 --> F
    F --> G([结束]):::startend
```

### CXPLAT_EXECUTION_STATE
```c
typedef struct CXPLAT_EXECUTION_STATE {
    uint64_t TimeNow;               // 当前时间（以微秒为单位）
    uint64_t LastWorkTime;          // 上一次执行工作的时间（以微秒为单位）
    uint64_t LastPoolProcessTime;   // 上一次处理线程池任务的时间（以微秒为单位）
    uint32_t WaitTime;              // 当前线程等待的时间（以毫秒为单位）
    uint32_t NoWorkCount;           // 连续未找到工作的次数
    CXPLAT_THREAD_ID ThreadID;      // 当前线程的唯一标识符
} CXPLAT_EXECUTION_STATE;
```
- 通用的执行上下文的抽象层，用来驱动worker循环

### 动态内存池相关操作
```c
typedef struct CXPLAT_POOL_EX {
    CXPLAT_POOL Base;              // 基础内存池
    CXPLAT_LIST_ENTRY Link;        // 链接到其他动态池的链表节点
    void* Owner;                   // 动态池的所有者
} CXPLAT_POOL_EX;

void
CxPlatAddDynamicPoolAllocator(
    _In_ CXPLAT_WORKER_POOL* WorkerPool, // 工作线程池
    _Inout_ CXPLAT_POOL_EX* Pool,        // 动态内存池
    _In_ uint16_t Index                  // 线程池中的索引
    );

void
CxPlatRemoveDynamicPoolAllocator(
    _Inout_ CXPLAT_POOL_EX* Pool         // 动态内存池
    );
```
说明:
- 内核模式不支持动态内存操作
- CXPLAT_POOL_EX 是动态内存池的扩展结构体，支持更灵活的操作。
- CxPlatAddDynamicPoolAllocator: 将动态内存池添加到工作线程池中。
- CxPlatRemoveDynamicPoolAllocator: 从工作线程池中移除动态内存池。

---

### CxPlatAddDynamicPoolAllocator
```c
void
CxPlatAddDynamicPoolAllocator(
    _In_ CXPLAT_WORKER_POOL* WorkerPool, // 工作线程池
    _Inout_ CXPLAT_POOL_EX* Pool,        // 动态内存池
    _In_ uint16_t Index                  // 工作线程池中的索引
    )
{
    // 确保传入的 WorkerPool 和 Pool 不为空。
    CXPLAT_DBG_ASSERT(WorkerPool);
    CXPLAT_FRE_ASSERT(Index < WorkerPool->WorkerCount); // 确保索引在有效范围内。

    // 获取指定索引的工作线程。
    CXPLAT_WORKER* Worker = &WorkerPool->Workers[Index];

    // 将动态内存池的所有者设置为当前工作线程。
    Pool->Owner = Worker;

    // 加锁以保护对动态池列表的访问。
    CxPlatLockAcquire(&Worker->ECLock);

    // 将动态内存池添加到工作线程的动态池列表中。
    CxPlatListInsertTail(&Worker->DynamicPoolList, &Pool->Link);

    // 释放锁。
    CxPlatLockRelease(&Worker->ECLock);
}
```

---

### CxPlatRemoveDynamicPoolAllocator
```c
void
CxPlatRemoveDynamicPoolAllocator(
    _Inout_ CXPLAT_POOL_EX* Pool // 动态内存池
    )
{
    // 获取动态内存池的所有者（工作线程）。
    CXPLAT_WORKER* Worker = (CXPLAT_WORKER*)Pool->Owner;

    // 加锁以保护对动态池列表的访问。
    CxPlatLockAcquire(&Worker->ECLock);

    // 从工作线程的动态池列表中移除该动态内存池。
    CxPlatListEntryRemove(&Pool->Link);

    // 释放锁。
    CxPlatLockRelease(&Worker->ECLock);
}
```

---

### 执行上下文相关的数据结构和接口
```c
typedef struct CXPLAT_EXECUTION_CONTEXT {
    CXPLAT_SLIST_ENTRY Entry;       // 链表节点，用于将上下文加入队列
    void* Context;                  // 用户定义的上下文数据
    void* CxPlatContext;            // 平台相关的上下文数据
    CXPLAT_EXECUTION_FN Callback;   // 执行任务的回调函数
    uint64_t NextTimeUs;            // 下次执行的时间（以微秒为单位）
    volatile BOOLEAN Ready;         // 标志上下文是否准备好执行
} CXPLAT_EXECUTION_CONTEXT;

#ifdef _KERNEL_MODE // Not supported on kernel mode
#define CxPlatAddExecutionContext(WorkerPool, Context, IdealProcessor) CXPLAT_FRE_ASSERT(FALSE)
#define CxPlatWakeExecutionContext(Context) CXPLAT_FRE_ASSERT(FALSE)
#else
void
CxPlatAddExecutionContext(
    _In_ CXPLAT_WORKER_POOL* WorkerPool,
    _Inout_ CXPLAT_EXECUTION_CONTEXT* Context,
    _In_ uint16_t Index // Into the execution config processor array
    );

void
CxPlatWakeExecutionContext(
    _In_ CXPLAT_EXECUTION_CONTEXT* Context
    );
#endif
```
- 内核模式不支持此系列函数
- 用于任务调度和执行

### CxPlatAddExecutionContext
```c
void
CxPlatAddExecutionContext(
    _In_ CXPLAT_WORKER_POOL* WorkerPool,       // 工作线程池
    _Inout_ CXPLAT_EXECUTION_CONTEXT* Context, // 执行上下文
    _In_ uint16_t Index                        // 工作线程池中的索引
    )
{
    // 确保传入的 WorkerPool 和 Context 不为空。
    CXPLAT_DBG_ASSERT(WorkerPool);
    CXPLAT_FRE_ASSERT(Index < WorkerPool->WorkerCount); // 确保索引在有效范围内。

    // 获取指定索引的工作线程。
    CXPLAT_WORKER* Worker = &WorkerPool->Workers[Index];

    // 将执行上下文的 CxPlatContext 字段设置为当前工作线程。
    Context->CxPlatContext = Worker;

    // 加锁以保护对 PendingECs 的访问。
    CxPlatLockAcquire(&Worker->ECLock);

    // 如果当前没有挂起的执行上下文，则需要触发更新事件。
    const BOOLEAN QueueEvent = Worker->PendingECs == NULL;

    // 将当前执行上下文添加到挂起的执行上下文列表中。
    Context->Entry.Next = Worker->PendingECs;
    Worker->PendingECs = &Context->Entry;

    // 释放锁。
    CxPlatLockRelease(&Worker->ECLock);

    // 如果需要触发更新事件，则将更新事件添加到事件队列中。
    if (QueueEvent) {
        CxPlatEventQEnqueue(&Worker->EventQ, &Worker->UpdatePollSqe);
    }
}
```
- 将任务分配到线程池中的某个线程

### CxPlatWakeExecutionContext
```c
void
CxPlatWakeExecutionContext(
    _In_ CXPLAT_EXECUTION_CONTEXT* Context // 执行上下文
    )
{
    // 获取执行上下文所属的工作线程。
    CXPLAT_WORKER* Worker = (CXPLAT_WORKER*)Context->CxPlatContext;

    // 检查工作线程是否已经在运行。
    if (!InterlockedFetchAndSetBoolean(&Worker->Running)) {
        // 如果工作线程未运行，则向事件队列添加唤醒事件。
        CxPlatEventQEnqueue(&Worker->EventQ, &Worker->WakeSqe);
    }
}
```
- 唤醒执行上下文，标记任务为可执行状态

---

## 接下来看看quic_datapath的相关实现
### [quic_platform](quic_datapath.md ':include')