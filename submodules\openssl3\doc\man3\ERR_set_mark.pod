=pod

=head1 NAME

ERR_set_mark, ERR_clear_last_mark, ERR_pop_to_mark
- set mark, clear mark and pop errors until mark

=head1 SYNOPSIS

 #include <openssl/err.h>

 int ERR_set_mark(void);
 int ERR_pop_to_mark(void);
 int ERR_clear_last_mark(void);

=head1 DESCRIPTION

ERR_set_mark() sets a mark on the current topmost error record if there
is one.

ERR_pop_to_mark() will pop the top of the error stack until a mark is found.
The mark is then removed.  If there is no mark, the whole stack is removed.

ERR_clear_last_mark() removes the last mark added if there is one.

=head1 RETURN VALUES

ERR_set_mark() returns 0 if the error stack is empty, otherwise 1.

ERR_clear_last_mark() and ERR_pop_to_mark() return 0 if there was no mark in the
error stack, which implies that the stack became empty, otherwise 1.

=head1 COPYRIGHT

Copyright 2003-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
