HMAC = MD5
# Note: The empty key results in passing NULL to HMAC_Init_ex, so this tests
# that HMAC_CTX and HMAC treat NULL as the empty key initially.
Key =
Input = "More text test vectors to stuff up EBCDIC machines :-)"
Output = e9139d1e6ee064ef8cf514fc7dc83e86

# HMAC tests from RFC2104
HMAC = MD5
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Input = "Hi There"
Output = 9294727a3638bb1c13f48ef8158bfc9d

HMAC = MD5
Key = "Jefe"
Input = "what do ya want for nothing?"
Output = 750c783e6ab0b503eaa86e310a5db738

HMAC = MD5
Key = AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
Input = DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD
Output = 56be34521d144c88dbb8c733f0e8b3f6

# HMAC tests from NIST test data

HMAC = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 5FD596EE78D5553C8FF4E72D266DFD192366DA29

HMAC = SHA1
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F10111213
Output = 4C99FF0CB1B31BD33F8431DBAF4D17FCD356A807

HMAC = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 2D51B2F7750E410584662E38F133435F4C4FD42A

HMAC = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = C7405E3AE058E8CD30B08B4140248581ED174CB34E1224BCC1EFC81B

HMAC = SHA224
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B
Output = E3D249A8CFB67EF8B7A169E9A0A599714A2CECBA65999A51BEB8FBBE

HMAC = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 91C52509E5AF8531601AE6230099D90BEF88AAEFB961F4080ABC014D

HMAC = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 8BB9A1DB9806F20DF7F77B82138C7914D174D59E13DC4D0169C9057B133E1D62

HMAC = SHA256
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Output = A28CF43130EE696A98F14A37678B56BCFCBDD9E5CF69717FECF5480F0EBDF790

HMAC = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = BDCCB6C72DDEADB500AE768386CB38CC41C63DBB0878DDB9C7A38A431B78378D

HMAC = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = 63C5DAA5E651847CA897C95814AB830BEDEDC7D25E83EEF9195CD45857A37F448947858F5AF50CC2B1B730DDF29671A9

HMAC = SHA384
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F
Output = 6EB242BDBB582CA17BEBFA481B1E23211464D2B7F8C20B9FF2201637B93646AF5AE9AC316E98DB45D9CAE773675EEED0

HMAC = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = 5B664436DF69B0CA22551231A3F0A3D5B4F97991713CFA84BFF4D0792EFF96C27DCCBBB6F79B65D548B40E8564CEF594

HMAC = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = FC25E240658CA785B7A811A8D3F7B4CA48CFA26A8A366BF2CD1F836B05FCB024BD36853081811D6CEA4216EBAD79DA1CFCB95EA4586B8A0CE356596A55FB1347

HMAC = SHA512
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = FD44C18BDA0BB0A6CE0E82B031BF2818F6539BD56EC00BDC10A8A2D730B3634DE2545D639B0F2CF710D0692C72A1896F1F211C2B922D1A96C392E07E7EA9FEDC

HMAC = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = D93EC8D2DE1AD2A9957CB9B83F14E76AD6B5E0CCE285079A127D3B14BCCB7AA7286D4AC0D4CE64215F2BC9E6870B33D97438BE4AAA20CDA5C5A912B48B8E27F3

# Additional HMAC tests from OpenSSL.
HMAC = SHA1
Input = "My test data"
Key =
Output = 61afdecb95429ef494d61fdee15990cabf0826fc

HMAC = SHA256
Input = "My test data"
Key =
Output = 2274b195d90ce8e03406f4b526a47e0787a88a65479938f1a5baa3ce0f079776

HMAC = SHA256
Input = "My test data"
Key = "123456"
Output = bab53058ae861a7f191abe2d0145cbb123776a6369ee3f9d79ce455667e411dd

HMAC = SHA1
Input = "My test data"
Key = "12345"
Output = 7dbe8c764c068e3bcd6e6b0fbcd5e6fc197b15bb
