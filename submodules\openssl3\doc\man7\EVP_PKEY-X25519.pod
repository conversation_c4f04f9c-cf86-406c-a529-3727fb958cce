=pod

=head1 NAME

EVP_PKEY-X25519, <PERSON><PERSON>_PKEY-X448, <PERSON><PERSON>_PKEY-ED25519, <PERSON><PERSON>_PKEY-ED448,
<PERSON><PERSON>_KEYMGMT-X25519, <PERSON><PERSON>_KEYMGMT-X448, <PERSON><PERSON>_KEYMGMT-ED25519, <PERSON><PERSON>_KEYMGMT-ED448
- EVP_PKEY X25519, X448, ED25519 and ED448 keytype and algorithm support

=head1 DESCRIPTION

The B<X25519>, B<X448>, B<ED25519> and B<ED448> keytypes are
implemented in OpenSSL's default and FIPS providers.  These implementations
support the associated key, containing the public key I<pub> and the
private key I<priv>.

No additional parameters can be set during key generation.


=head2 Common X25519, X448, ED25519 and ED448 parameters

In addition to the common parameters that all keytypes should support (see
L<provider-keymgmt(7)/Common parameters>), the implementation of these keytypes
support the following.

=over 4

=item "group" (B<OSSL_PKEY_PARAM_GROUP_NAME>) <UTF8 string>

This is only supported by X25519 and X448. The group name must be "x25519" or
"x448" respectively for those algorithms. This is only present for consistency
with other key exchange algorithms and is typically not needed.

=item "pub" (B<OSSL_PKEY_PARAM_PUB_KEY>) <octet string>

The public key value.

=item "priv" (B<OSSL_PKEY_PARAM_PRIV_KEY>) <octet string>

The private key value.

=item "encoded-pub-key" (B<OSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY>) <octet string>

Used for getting and setting the encoding of a public key for the B<X25519> and
B<X448> key types. Public keys are expected be encoded in a format as defined by
RFC7748.

=back

=head2 ED25519 and ED448 parameters

=over 4

=item "mandatory-digest" (B<OSSL_PKEY_PARAM_MANDATORY_DIGEST>) <UTF8 string>

The empty string, signifying that no digest may be specified.

=back

=head1 CONFORMING TO

=over 4

=item RFC 8032

=item RFC 8410

=back

=head1 EXAMPLES

An B<EVP_PKEY> context can be obtained by calling:

    EVP_PKEY_CTX *pctx =
        EVP_PKEY_CTX_new_from_name(NULL, "X25519", NULL);

    EVP_PKEY_CTX *pctx =
        EVP_PKEY_CTX_new_from_name(NULL, "X448", NULL);

    EVP_PKEY_CTX *pctx =
        EVP_PKEY_CTX_new_from_name(NULL, "ED25519", NULL);

    EVP_PKEY_CTX *pctx =
        EVP_PKEY_CTX_new_from_name(NULL, "ED448", NULL);

An B<X25519> key can be generated like this:

    pkey = EVP_PKEY_Q_keygen(NULL, NULL, "X25519");

An B<X448>, B<ED25519>, or B<ED448> key can be generated likewise.

=head1 SEE ALSO

L<EVP_KEYMGMT(3)>, L<EVP_PKEY(3)>, L<provider-keymgmt(7)>,
L<EVP_KEYEXCH-X25519(7)>, L<EVP_KEYEXCH-X448(7)>,
L<EVP_SIGNATURE-ED25519(7)>, L<EVP_SIGNATURE-ED448(7)>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
