=pod

=head1 NAME

ERR_print_errors, ERR_print_errors_fp, ERR_print_errors_cb
- print error messages

=head1 SYNOPSIS

 #include <openssl/err.h>

 void ERR_print_errors(BIO *bp);
 void ERR_print_errors_fp(FILE *fp);
 void ERR_print_errors_cb(int (*cb)(const char *str, size_t len, void *u), void *u)


=head1 DESCRIPTION

ERR_print_errors() is a convenience function that prints the error
strings for all errors that OpenSSL has recorded to B<bp>, thus
emptying the error queue.

ERR_print_errors_fp() is the same, except that the output goes to a
B<FILE>.

ERR_print_errors_cb() is the same, except that the callback function,
B<cb>, is called for each error line with the string, length, and userdata
B<u> as the callback parameters.

The error strings will have the following format:

 [pid]:error:[error code]:[library name]:[function name]:[reason string]:[filename]:[line]:[optional text message]

I<error code> is an 8 digit hexadecimal number. I<library name>,
I<function name> and I<reason string> are ASCII text, as is I<optional
text message> if one was set for the respective error code.

If there is no text string registered for the given error code,
the error string will contain the numeric code.

=head1 RETURN VALUES

ERR_print_errors() and ERR_print_errors_fp() return no values.

=head1 SEE ALSO

L<ERR_error_string(3)>,
L<ERR_get_error(3)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
