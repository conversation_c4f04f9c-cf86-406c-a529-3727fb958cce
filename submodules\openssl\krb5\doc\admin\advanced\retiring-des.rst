.. _retiring-des:

Retiring DES
=======================

Version 5 of the Kerberos protocol was originally implemented using
the Data Encryption Standard (DES) as a block cipher for encryption.
While it was considered secure at the time, advancements in computational
ability have rendered DES vulnerable to brute force attacks on its 56-bit
keyspace.  As such, it is now considered insecure and should not be
used (:rfc:`6649`).

History
-------

DES was used in the original Kerberos implementation, and was the
only cryptosystem in krb5 1.0.  Partial support for triple-DES (3DES) was
added in version 1.1, with full support following in version 1.2.
The Advanced Encryption Standard (AES), which supersedes DES, gained
partial support in version 1.3.0 of krb5 and full support in version 1.3.2.
However, deployments of krb5 using Kerberos databases created with older
versions of krb5 will not necessarily start using strong crypto for
ordinary operation without administrator intervention.

Types of keys
-------------

* The database master key:  This key is not exposed to user requests,
  but is used to encrypt other key material stored in the kerberos
  database.  The database master key is currently stored as ``K/M``
  by default.
* Password-derived keys:  User principals frequently have keys
  derived from a password.  When a new password is set, the KDC
  uses various string2key functions to generate keys in the database
  for that principal.
* Keytab keys:  Application server principals generally use random
  keys which are not derived from a password.  When the database
  entry is created, the KDC generates random keys of various enctypes
  to enter in the database, which are conveyed to the application server
  and stored in a keytab.
* Session keys:  These are short-term keys generated by the KDC while
  processing client requests, with an enctype selected by the KDC.

For details on the various enctypes and how enctypes are selected by the KDC
for session keys and client/server long-term keys, see :ref:`enctypes`.
When using the :ref:`kadmin(1)` interface to generate new long-term keys,
the **-e** argument can be used to force a particular set of enctypes,
overriding the KDC default values.

.. note::

    When the KDC is selecting a session key, it has no knowledge about the
    kerberos installation on the server which will receive the service ticket,
    only what keys are in the database for the service principal.
    In order to allow uninterrupted operation to
    clients while migrating away from DES, care must be taken to ensure that
    kerberos installations on application server machines are configured to
    support newer encryption types before keys of those new encryption types
    are created in the Kerberos database for those server principals.

Upgrade procedure
-----------------

This procedure assumes that the KDC software has already been upgraded
to a modern version of krb5 that supports non-DES keys, so that the
only remaining task is to update the actual keys used to service requests.
The realm used for demonstrating this procedure, ZONE.MIT.EDU,
is an example of the worst-case scenario, where all keys in the realm
are DES.  The realm was initially created with a very old version of krb5,
and **supported_enctypes** in :ref:`kdc.conf(5)` was set to a value
appropriate when the KDC was installed, but was not updated as the KDC
was upgraded:

::

    [realms]
            ZONE.MIT.EDU = {
                    [...]
                    master_key_type = des-cbc-crc
                    supported_enctypes = des-cbc-crc:normal des:normal des:v4 des:norealm des:onlyrealm des:afs3
            }

This resulted in the keys for all principals in the realm being forced
to DES-only, unless specifically requested using :ref:`kadmin(1)`.

Before starting the upgrade, all KDCs were running krb5 1.11,
and the database entries for some "high-value" principals were:

::

    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q 'getprinc krbtgt/ZONE.MIT.EDU'
    [...]
    Number of keys: 1
    Key: vno 1, des-cbc-crc:v4
    [...]
    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q 'getprinc kadmin/admin'
    [...]
    Number of keys: 1
    Key: vno 15, des-cbc-crc
    [...]
    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q 'getprinc kadmin/changepw'
    [...]
    Number of keys: 1
    Key: vno 14, des-cbc-crc
    [...]

The ``krbtgt/REALM`` key appears to have never been changed since creation
(its kvno is 1), and all three database entries have only a des-cbc-crc key.

The krbtgt key and KDC keys
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Perhaps the biggest single-step improvement in the security of the cell
is gained by strengthening the key of the ticket-granting service principal,
``krbtgt/REALM``---if this principal's key is compromised, so is the
entire realm.  Since the server that will handle service tickets
for this principal is the KDC itself, it is easy to guarantee that it
will be configured to support any encryption types which might be
selected.  However, the default KDC behavior when creating new keys is to
remove the old keys, which would invalidate all existing tickets issued
against that principal, rendering the TGTs cached by clients useless.
Instead, a new key can be created with the old key retained, so that
existing tickets will still function until their scheduled expiry
(see :ref:`changing_krbtgt_key`).

::

    [root@casio krb5kdc]# enctypes=aes256-cts-hmac-sha1-96:normal,\
    > aes128-cts-hmac-sha1-96:normal,des3-hmac-sha1:normal,des-cbc-crc:normal
    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q "cpw -e ${enctypes} -randkey \
    > -keepold krbtgt/ZONE.MIT.EDU"
    Authenticating as principal root/<EMAIL> with password.
    Key for "krbtgt/<EMAIL>" randomized.

.. note::

    The new ``krbtgt@REALM`` key should be propagated to replica KDCs
    immediately so that TGTs issued by the master KDC can be used to
    issue service tickets on replica KDCs.  Replica KDCs will refuse
    requests using the new TGT kvno until the new krbtgt entry has
    been propagated to them.

It is necessary to explicitly specify the enctypes for the new database
entry, since **supported_enctypes** has not been changed.  Leaving
**supported_enctypes** unchanged makes a potential rollback operation
easier, since all new keys of new enctypes are the result of explicit
administrator action and can be easily enumerated.
Upgrading the krbtgt key should have minimal user-visible disruption other
than that described in the note above, since only clients which list the
new enctypes as supported will use them, per the procedure
in :ref:`session_key_selection`.
Once the krbtgt key is updated, the session and ticket keys for user
TGTs will be strong keys, but subsequent requests
for service tickets will still get DES keys until the service principals
have new keys generated.  Application service
remains uninterrupted due to the key-selection procedure on the KDC.

After the change, the database entry is now:

::

    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q 'getprinc krbtgt/ZONE.MIT.EDU'
    [...]
    Number of keys: 5
    Key: vno 2, aes256-cts-hmac-sha1-96
    Key: vno 2, aes128-cts-hmac-sha1-96
    Key: vno 2, des3-cbc-sha1
    Key: vno 2, des-cbc-crc
    Key: vno 1, des-cbc-crc:v4
    [...]

Since the expected disruptions from rekeying the krbtgt principal are
minor, after a short testing period, it is
appropriate to rekey the other high-value principals, ``kadmin/admin@REALM``
and ``kadmin/changepw@REALM``. These are the service principals used for
changing user passwords and updating application keytabs.  The kadmin
and password-changing services are regular kerberized services, so the
session-key-selection algorithm described in :ref:`session_key_selection`
applies.  It is particularly important to have strong session keys for
these services, since user passwords and new long-term keys are conveyed
over the encrypted channel.

::

    [root@casio krb5kdc]# enctypes=aes256-cts-hmac-sha1-96:normal,\
    > aes128-cts-hmac-sha1-96:normal,des3-hmac-sha1:normal
    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q "cpw -e ${enctypes} -randkey \
    > kadmin/admin"
    Authenticating as principal root/<EMAIL> with password.
    Key for "kadmin/<EMAIL>" randomized.
    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q "cpw -e ${enctypes} -randkey \
    > kadmin/changepw"
    Authenticating as principal root/<EMAIL> with password.
    Key for "kadmin/<EMAIL>" randomized.

It is not necessary to retain a single-DES key for these services, since
password changes are not part of normal daily workflow, and disruption
from a client failure is likely to be minimal.  Furthermore, if a kerberos
client experiences failure changing a user password or keytab key,
this indicates that that client will become inoperative once services
are rekeyed to non-DES enctypes.  Such problems can be detected early
at this stage, giving more time for corrective action.

Adding strong keys to application servers
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Before switching the default enctypes for new keys over to strong enctypes,
it may be desired to test upgrading a handful of services with the
new configuration before flipping the switch for the defaults.  This
still requires using the **-e** argument in :ref:`kadmin(1)` to get non-default
enctypes:

::

    [root@casio krb5kdc]# enctypes=aes256-cts-hmac-sha1-96:normal,\
    > aes128-cts-hmac-sha1-96:normal,des3-cbc-sha1:normal,des-cbc-crc:normal
    [root@casio krb5kdc]# kadmin -r ZONE.MIT.EDU -p zephyr/<EMAIL> -k -t \
    > /etc/zephyr/krb5.keytab  -q "ktadd -e ${enctypes} \
    > -k /etc/zephyr/krb5.keytab zephyr/<EMAIL>"
    Authenticating as principal zephyr/<EMAIL> with keytab /etc/zephyr/krb5.keytab.
    Entry for principal zephyr/<EMAIL> with kvno 4, encryption type aes256-cts-hmac-sha1-96 added to keytab WRFILE:/etc/zephyr/krb5.keytab.
    Entry for principal zephyr/<EMAIL> with kvno 4, encryption type aes128-cts-hmac-sha1-96 added to keytab WRFILE:/etc/zephyr/krb5.keytab.
    Entry for principal zephyr/<EMAIL> with kvno 4, encryption type des3-cbc-sha1 added to keytab WRFILE:/etc/zephyr/krb5.keytab.
    Entry for principal zephyr/<EMAIL> with kvno 4, encryption type des-cbc-crc added to keytab WRFILE:/etc/zephyr/krb5.keytab.

Be sure to remove the old keys from the application keytab, per best
practice.

::

    [root@casio krb5kdc]# k5srvutil -f /etc/zephyr/krb5.keytab delold
    Authenticating as principal zephyr/<EMAIL> with keytab /etc/zephyr/krb5.keytab.
    Entry for principal zephyr/<EMAIL> with kvno 3 removed from keytab WRFILE:/etc/zephyr/krb5.keytab.

Adding strong keys by default
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Once the high-visibility services have been rekeyed, it is probably
appropriate to change :ref:`kdc.conf(5)` to generate keys with the new
encryption types by default.  This enables server administrators to generate
new enctypes with the **change** subcommand of :ref:`k5srvutil(1)`,
and causes user password
changes to add new encryption types for their entries.  It will probably
be necessary to implement administrative controls to cause all user
principal keys to be updated in a reasonable period of time, whether
by forcing password changes or a password synchronization service that
has access to the current password and can add the new keys.

::

    [realms]
            ZONE.MIT.EDU = {
                    supported_enctypes = aes256-cts-hmac-sha1-96:normal aes128-cts-hmac-sha1-96:normal des3-cbc-sha1:normal des3-hmac-sha1:normal des-cbc-crc:normal

.. note::

    The krb5kdc process must be restarted for these changes to take effect.

At this point, all service administrators can update their services and the
servers behind them to take advantage of strong cryptography.
If necessary, the server's krb5 installation should be configured and/or
upgraded to a version supporting non-DES keys.  See :ref:`enctypes` for
krb5 version and configuration settings.
Only when the service is configured to accept non-DES keys should
the key version number be incremented and new keys generated
(``k5srvutil change && k5srvutil delold``).

::

    root@dr-willy:~# k5srvutil change
    Authenticating as principal host/<EMAIL> with keytab /etc/krb5.keytab.
    Entry for principal host/<EMAIL> with kvno 3, encryption type AES-256 CTS mode with 96-bit SHA-1 HMAC added to keytab WRFILE:/etc/krb5.keytab.
    Entry for principal host/<EMAIL> with kvno 3, encryption type AES-128 CTS mode with 96-bit SHA-1 HMAC added to keytab WRFILE:/etc/krb5.keytab.
    Entry for principal host/<EMAIL> with kvno 3, encryption type Triple DES cbc mode with HMAC/sha1 added to keytab WRFILE:/etc/krb5.keytab.
    Entry for principal host/<EMAIL> with kvno 3, encryption type DES cbc mode with CRC-32 added to keytab WRFILE:/etc/krb5.keytab.
    root@dr-willy:~# klist -e -k -t /etc/krb5.keytab
    Keytab name: WRFILE:/etc/krb5.keytab
    KVNO Timestamp         Principal
    ---- ----------------- --------------------------------------------------------
       2 10/10/12 17:03:59 host/<EMAIL> (DES cbc mode with CRC-32)
       3 12/12/12 15:31:19 host/<EMAIL> (AES-256 CTS mode with 96-bit SHA-1 HMAC)
       3 12/12/12 15:31:19 host/<EMAIL> (AES-128 CTS mode with 96-bit SHA-1 HMAC)
       3 12/12/12 15:31:19 host/<EMAIL> (Triple DES cbc mode with HMAC/sha1)
       3 12/12/12 15:31:19 host/<EMAIL> (DES cbc mode with CRC-32)
    root@dr-willy:~# k5srvutil delold
    Authenticating as principal host/<EMAIL> with keytab /etc/krb5.keytab.
    Entry for principal host/<EMAIL> with kvno 2 removed from keytab WRFILE:/etc/krb5.keytab.

When a single service principal is shared by multiple backend servers in
a load-balanced environment, it may be necessary to schedule downtime
or adjust the population in the load-balanced pool in order to propagate
the updated keytab to all hosts in the pool with minimal service interruption.

Removing DES keys from usage
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This situation remains something of a testing or transitory state,
as new DES keys are still being generated, and will be used if requested
by a client.  To make more progress removing DES from the realm, the KDC
should be configured to not generate such keys by default.

.. note::

    An attacker posing as a client can implement a brute force attack against
    a DES key for any principal, if that key is in the current (highest-kvno)
    key list.  This attack is only possible if **allow_weak_crypto = true**
    is enabled on the KDC.  Setting the **+requires_preauth** flag on a
    principal forces this attack to be an online attack, much slower than
    the offline attack otherwise available to the attacker.  However, setting
    this flag on a service principal is not always advisable; see the entry in
    :ref:`add_principal` for details.

The following KDC configuration will not generate DES keys by default:

::

    [realms]
            ZONE.MIT.EDU = {
                    supported_enctypes = aes256-cts-hmac-sha1-96:normal aes128-cts-hmac-sha1-96:normal des3-cbc-sha1:normal des3-hmac-sha1:normal

.. note::

    As before, the KDC process must be restarted for this change to take
    effect.  It is best practice to update kdc.conf on all KDCs, not just the
    master, to avoid unpleasant surprises should the master fail and a
    replica need to be promoted.

It is now appropriate to remove the legacy single-DES key from the
``krbtgt/REALM`` entry:

::

    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q "cpw -randkey -keepold \
    > krbtgt/ZONE.MIT.EDU"
    Authenticating as principal host/<EMAIL> with password.
    Key for "krbtgt/<EMAIL>" randomized.

After the maximum ticket lifetime has passed, the old database entry
should be removed.

::

    [root@casio krb5kdc]# kadmin.local -r ZONE.MIT.EDU -q 'purgekeys krbtgt/ZONE.MIT.EDU'
    Authenticating as principal root/<EMAIL> with password.
    Old keys for principal "krbtgt/<EMAIL>" purged.

After the KDC is restarted with the new **supported_enctypes**,
all user password changes and application keytab updates will not
generate DES keys by default.

::

    contents-vnder-pressvre:~> kpasswd <EMAIL>
    <NAME_EMAIL>:  [enter old password]
    Enter new password:                  [enter new password]
    Enter it again:                      [enter new password]
    Password changed.
    contents-vnder-pressvre:~> kadmin -r ZONE.MIT.EDU -q 'getprinc zonetest'
    [...]
    Number of keys: 3
    Key: vno 9, aes256-cts-hmac-sha1-96
    Key: vno 9, aes128-cts-hmac-sha1-96
    Key: vno 9, des3-cbc-sha1
    [...]

    [kaduk@glossolalia ~]$ kadmin -p <EMAIL> -r ZONE.MIT.EDU -k \
    > -t kaduk-zone.keytab -q 'ktadd -k kaduk-zone.keytab <EMAIL>'
    Authenticating <NAME_EMAIL> with keytab kaduk-zone.keytab.
    Entry <NAME_EMAIL> with kvno 3, encryption type aes256-cts-hmac-sha1-96 added to keytab WRFILE:kaduk-zone.keytab.
    Entry <NAME_EMAIL> with kvno 3, encryption type aes128-cts-hmac-sha1-96 added to keytab WRFILE:kaduk-zone.keytab.
    Entry <NAME_EMAIL> with kvno 3, encryption type des3-cbc-sha1 added to keytab WRFILE:kaduk-zone.keytab.

Once all principals have been re-keyed, DES support can be disabled on the
KDC (**allow_weak_crypto = false**), and client machines can remove
**allow_weak_crypto = true** from their :ref:`krb5.conf(5)` configuration
files, completing the migration.  **allow_weak_crypto** takes precedence over
all places where DES enctypes could be explicitly configured.  DES keys will
not be used, even if they are present, when **allow_weak_crypto = false**.

Support for legacy services
~~~~~~~~~~~~~~~~~~~~~~~~~~~

If there remain legacy services which do not support non-DES enctypes
(such as older versions of AFS), **allow_weak_crypto** must remain
enabled on the KDC.  Client machines need not have this setting,
though---applications which require DES can use API calls to allow
weak crypto on a per-request basis, overriding the system krb5.conf.
However, having **allow_weak_crypto** set on the KDC means that any
principals which have a DES key in the database could still use those
keys.  To minimize the use of DES in the realm and restrict it to just
legacy services which require DES, it is necessary to remove all other
DES keys.  The realm has been configured such that at password and
keytab change, no DES keys will be generated by default.  The task
then reduces to requiring user password changes and having server
administrators update their service keytabs.  Administrative outreach
will be necessary, and if the desire to eliminate DES is sufficiently
strong, the KDC administrators may choose to randkey any principals
which have not been rekeyed after some timeout period, forcing the
user to contact the helpdesk for access.

The Database Master Key
-----------------------

This procedure does not alter ``K/M@REALM``, the key used to encrypt key
material in the Kerberos database.  (This is the key stored in the stash file
on the KDC if stash files are used.)  However, the security risk of
a single-DES key for ``K/M`` is minimal, given that access to material
encrypted in ``K/M`` (the Kerberos database) is generally tightly controlled.
If an attacker can gain access to the encrypted database, they likely
have access to the stash file as well, rendering the weak cryptography
broken by non-cryptographic means.  As such, upgrading ``K/M`` to a stronger
encryption type is unlikely to be a high-priority task.

Is is possible to upgrade the master key used for the database, if
desired.  Using :ref:`kdb5_util(8)`'s **add_mkey**, **use_mkey**, and
**update_princ_encryption** commands, a new master key can be added
and activated for use on new key material, and the existing entries
converted to the new master key.
