# -*- mode: perl; -*-
# Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test Renegotiation

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our $fips_mode;

our @tests = ();

foreach my $sctp ("No", "Yes")
{
    next if disabled("sctp") && $sctp eq "Yes";
    next if disabled("dtls1_2") && $fips_mode;

    my $suffix = ($sctp eq "No") ? "" : "-sctp";
    our @tests_basic = (
        {
            name => "renegotiate-client-no-resume".$suffix,
            server => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0',
                "Options" => "NoResumptionOnRenegotiation"
            },
            client => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-client-resume".$suffix,
            server => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            client => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "Yes",
                "ExpectedResult" => "Success"
            }
        },
        # Note: Unlike the TLS tests, we will never do resumption with server
        # initiated reneg. This is because an OpenSSL DTLS client will always do a full
        # handshake (i.e. it doesn't supply a session id) when it receives a
        # HelloRequest. This is different to the OpenSSL TLS implementation where an
        # OpenSSL client will always try an abbreviated handshake (i.e. it will supply
        # the session id). This goes all the way to commit 48ae85b6f when abbreviated
        # handshake support was first added. Neither behaviour is wrong, but the
        # discrepancy is strange. TODO: Should we harmonise the TLS and DTLS behaviour,
        # and if so, what to?
        {
            name => "renegotiate-server-resume".$suffix,
            server => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            client => {
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateServer",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-client-auth-require".$suffix,
            server => {
                "VerifyCAFile" => test_pem("root-cert.pem"),
                "VerifyMode" => "Require",
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            client => {
                "Certificate" => test_pem("ee-client-chain.pem"),
                "PrivateKey"  => test_pem("ee-key.pem"),
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateServer",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-client-auth-once".$suffix,
            server => {
                "VerifyCAFile" => test_pem("root-cert.pem"),
                "VerifyMode" => "Once",
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            client => {
                "Certificate" => test_pem("ee-client-chain.pem"),
                "PrivateKey"  => test_pem("ee-key.pem"),
                "CipherString" => 'DEFAULT:@SECLEVEL=0'
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateServer",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        }
    );
    push @tests, @tests_basic;

    next if disabled("dtls1_2");
    our @tests_dtls1_2 = (
        {
            name => "renegotiate-aead-to-non-aead".$suffix,
            server => {
                "Options" => "NoResumptionOnRenegotiation"
            },
            client => {
                "CipherString" => "AES128-GCM-SHA256",
                extra => {
                    "RenegotiateCiphers" => "AES128-SHA"
                }
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-non-aead-to-aead".$suffix,
            server => {
                "Options" => "NoResumptionOnRenegotiation"
            },
            client => {
                "CipherString" => "AES128-SHA",
                extra => {
                    "RenegotiateCiphers" => "AES128-GCM-SHA256"
                }
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-non-aead-to-non-aead".$suffix,
            server => {
                "Options" => "NoResumptionOnRenegotiation"
            },
            client => {
                "CipherString" => "AES128-SHA",
                extra => {
                    "RenegotiateCiphers" => "AES256-SHA"
                }
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
        {
            name => "renegotiate-aead-to-aead".$suffix,
            server => {
                "Options" => "NoResumptionOnRenegotiation"
            },
            client => {
                "CipherString" => "AES128-GCM-SHA256",
                extra => {
                    "RenegotiateCiphers" => "AES256-GCM-SHA384"
                }
            },
            test => {
                "Method" => "DTLS",
                "UseSCTP" => $sctp,
                "HandshakeMode" => "RenegotiateClient",
                "ResumptionExpected" => "No",
                "ExpectedResult" => "Success"
            }
        },
    );
    push @tests, @tests_dtls1_2;
}
