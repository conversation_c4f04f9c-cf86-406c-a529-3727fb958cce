﻿<?xml version='1.0' encoding='utf-8' standalone='yes'?>
<instrumentationManifest xmlns="http://schemas.microsoft.com/win/2004/08/events">
  <instrumentation
      xmlns:ut="http://manifests.microsoft.com/win/2004/08/windows/networkevents"
      xmlns:win="http://manifests.microsoft.com/win/2004/08/windows/events"
      xmlns:xs="http://www.w3.org/2001/XMLSchema"
      >
    <events xmlns="http://schemas.microsoft.com/win/2004/08/events">
      <provider
          guid="{ff15e657-4f26-570e-88ab-0796b258d11c}"
          messageFileName="%WinDir%\system32\drivers\msquic.sys"
          name="Microsoft-Quic"
          resourceFileName="%WinDir%\system32\drivers\msquic.sys"
          symbol="MICROSOFT_MSQUIC_PROVIDER"
          >
        <opcodes>
          <opcode
              name="Global"
              value="11"
              />
          <opcode
              name="Registration"
              value="12"
              />
          <opcode
              name="Configuration"
              value="13"
              />
          <opcode
              name="Worker"
              value="14"
              />
          <opcode
              name="Listener"
              value="15"
              />
          <opcode
              name="Binding"
              value="16"
              />
          <opcode
              name="Connection"
              value="17"
              />
          <opcode
              name="Stream"
              value="18"
              />
          <opcode
              name="Datapath"
              value="19"
              />
        </opcodes>
        <keywords>
          <!-- Can only use lower 32 bits of the mask. -->
          <!-- Low bits are object type. -->
          <keyword
              mask="0x0000000000000001"
              name="ut:Registration"
              />
          <keyword
              mask="0x0000000000000002"
              name="ut:Configuration"
              />
          <keyword
              mask="0x0000000000000004"
              name="ut:Listener"
              />
          <keyword
              mask="0x0000000000000008"
              name="ut:Worker"
              />
          <keyword
              mask="0x0000000000000010"
              name="ut:Binding"
              />
          <keyword
              mask="0x0000000000000020"
              name="ut:Connection"
              />
          <keyword
              mask="0x0000000000000040"
              name="ut:Stream"
              />
          <keyword
              mask="0x0000000000000080"
              name="ut:UDP"
              />
          <keyword
              mask="0x0000000000000100"
              name="ut:Packet"
              />
          <keyword
              mask="0x0000000000000200"
              name="ut:TLS"
              />
          <keyword
              mask="0x0000000000000400"
              name="ut:Platform"
              />
          <keyword
              mask="0x0000000000000800"
              name="ut:Api"
              />
          <keyword
              mask="0x0000000000001000"
              name="ut:Log"
              />
          <keyword
              mask="0x0000000000002000"
              name="ut:RPS"
              />
          <!-- High bits are operation category. -->
          <keyword
              mask="0x0000000080000000"
              name="ut:LowVolume"
              />
          <keyword
              mask="0x0000000040000000"
              name="ut:DataFlow"
              />
          <keyword
              mask="0x0000000020000000"
              name="ut:Scheduling"
              />
        </keywords>
        <maps>
          <valueMap name="map_QUIC_SCHEDULE_STATE">
            <map
                message="$(string.Enum.QUIC_SCHEDULE_STATE.IDLE)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_SCHEDULE_STATE.QUEUED)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_SCHEDULE_STATE.PROCESSING)"
                value="2"
                />
          </valueMap>
          <valueMap name="map_QUIC_OPERATION_TYPE">
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.API_CALL)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.FLUSH_RECV)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.UNREACHABLE)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.FLUSH_STREAM_RECV)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.FLUSH_SEND)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.TLS_COMPLETE)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.TIMER_EXPIRED)"
                value="6"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.TRACE_RUNDOWN)"
                value="7"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.VERSION_NEGOTIATION)"
                value="8"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.STATELESS_RESET)"
                value="9"
                />
            <map
                message="$(string.Enum.QUIC_OPERATION_TYPE.RETRY)"
                value="10"
                />
          </valueMap>
          <valueMap name="map_QUIC_API_TYPE">
            <map
                message="$(string.Enum.QUIC_API_TYPE.CONN_CLOSE)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.CONN_SHUTDOWN)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.CONN_START)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.SET_CONFIGURATION)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.SEND_RESUMPTION_TICKET)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_CLOSE)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_SHUTDOWN)"
                value="6"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_START)"
                value="7"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_SEND)"
                value="8"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_RECV_COMPLETE)"
                value="9"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.STRM_RECV_SET_ENABLED)"
                value="10"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.SET_PARAM)"
                value="11"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.GET_PARAM)"
                value="12"
                />
            <map
                message="$(string.Enum.QUIC_API_TYPE.DATAGRAM_SEND)"
                value="13"
                />
          </valueMap>
          <valueMap name="map_QUIC_CONN_TIMER_TYPE">
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.PACING)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.ACK_DELAY)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.LOSS_DETECTION)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.KEEP_ALIVE)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.IDLE)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_CONN_TIMER_TYPE.SHUTDOWN)"
                value="5"
                />
          </valueMap>
          <valueMap name="map_QUIC_LOSS_TIMER_TYPE">
            <map
                message="$(string.Enum.QUIC_LOSS_TIMER_TYPE.INITIAL)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_LOSS_TIMER_TYPE.RACK)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_LOSS_TIMER_TYPE.PROBE)"
                value="2"
                />
          </valueMap>
          <valueMap name="map_QUIC_STREAM_SEND_STATE">
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.DISABLED)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.STARTED)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.RESET)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.RESET_ACKED)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.FIN)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.FIN_ACKED)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.RELIABLE_RESET)"
                value="6"
            />
            <map
                message="$(string.Enum.QUIC_STREAM_SEND_STATE.RELIABLE_RESET_ACKED)"
                value="7"
            />
          </valueMap>
          <valueMap name="map_QUIC_STREAM_RECV_STATE">
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.DISABLED)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.STARTED)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.PAUSED)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.STOPPED)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.RESET)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.FIN)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_STREAM_RECV_STATE.RELIABLE_RESET)"
                value="6"
            />
          </valueMap>
          <valueMap name="map_QUIC_TRACE_PACKET_TYPE">
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.VERSION_NEGOTIATION)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.INITIAL)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.ZERO_RTT)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.HANDSHAKE)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.RETRY)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_TYPE.ONE_RTT)"
                value="5"
                />
          </valueMap>
          <valueMap name="map_QUIC_TRACE_PACKET_LOSS_REASON">
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_LOSS_REASON.RACK)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_LOSS_REASON.FACK)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_PACKET_LOSS_REASON.PROBE)"
                value="2"
                />
          </valueMap>
          <valueMap name="map_QUIC_TRACE_API_TYPE">
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.SET_PARAM)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.GET_PARAM)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.REGISTRATION_OPEN)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.REGISTRATION_CLOSE)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.REGISTRATION_SHUTDOWN)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_OPEN)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_CLOSE)"
                value="6"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_LOAD_CREDENTIAL)"
                value="7"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.LISTENER_OPEN)"
                value="8"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.LISTENER_CLOSE)"
                value="9"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.LISTENER_START)"
                value="10"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.LISTENER_STOP)"
                value="11"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_OPEN)"
                value="12"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_CLOSE)"
                value="13"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_SHUTDOWN)"
                value="14"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_START)"
                value="15"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_SET_CONFIGURATION)"
                value="16"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.CONNECTION_SEND_RESUMPTION_TICKET)"
                value="17"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_OPEN)"
                value="18"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_CLOSE)"
                value="19"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_START)"
                value="20"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_SHUTDOWN)"
                value="21"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_SEND)"
                value="22"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_RECEIVE_COMPLETE)"
                value="23"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.STREAM_RECEIVE_SET_ENABLED)"
                value="24"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.DATAGRAM_SEND)"
                value="25"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.COMPLETE_RESUMPTION_TICKET_VALIDATION)"
                value="26"
                />
            <map
                message="$(string.Enum.QUIC_TRACE_API_TYPE.COMPLETE_CERTIFICATE_VALIDATION)"
                value="27"
                />
          </valueMap>
          <valueMap name="map_QUIC_SEND_FLUSH_REASON">
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.CONNECTION_FLAGS)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.STREAM_FLAGS)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.PROBE)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.LOSS)"
                value="3"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.ACK)"
                value="4"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.TRANSPORT_PARAMETERS)"
                value="5"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.CONGESTION_CONTROL)"
                value="6"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.CONNECTION_FLOW_CONTROL)"
                value="7"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.NEW_KEY)"
                value="8"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.STREAM_FLOW_CONTROL)"
                value="9"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.STREAM_ID_FLOW_CONTROL)"
                value="10"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.AMPLIFICATION_PROTECTION)"
                value="11"
                />
            <map
                message="$(string.Enum.QUIC_SEND_FLUSH_REASON.SCHEDULING)"
                value="12"
                />
          </valueMap>
          <valueMap name="map_QUIC_EXECUTION_PROFILE">
            <map
                message="$(string.Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_LOW_LATENCY)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_MAX_THROUGHPUT)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_SCAVENGER)"
                value="2"
                />
            <map
                message="$(string.Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_REAL_TIME)"
                value="3"
                />
          </valueMap>
          <valueMap name="map_QUIC_CONN_CUBIC_HYSTART_STATE">
            <map
                message="$(string.Enum.QUIC_CUBIC_HYSTART_STATE.NOT_STARTED)"
                value="0"
                />
            <map
                message="$(string.Enum.QUIC_CUBIC_HYSTART_STATE.ACTIVE)"
                value="1"
                />
            <map
                message="$(string.Enum.QUIC_CUBIC_HYSTART_STATE.DONE)"
                value="2"
                />
          </valueMap>
        </maps>
        <templates>
          <template tid="tid_MESSAGE">
            <data
                inType="win:AnsiString"
                name="Message"
                />
          </template>
          <template tid="tid_BOOL">
            <data
                inType="win:UInt8"
                name="Value"
                />
          </template>
          <template tid="tid_DATAPATH_INIT">
            <data
                inType="win:UInt32"
                name="DatapathFeatures"
                />
          </template>
          <template tid="tid_LIBRARY_INIT">
            <data
                inType="win:UInt32"
                name="PartitionCount"
                />
            <data
                inType="win:UInt32"
                name="DatapathFeatures"
                />
          </template>
          <template tid="tid_LIBRARY_INIT_V2">
            <data
                inType="win:UInt32"
                name="PartitionCount"
                />
          </template>
          <template tid="tid_LIBRARY_ERROR">
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_LIBRARY_STATUS_ERROR">
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_LIBRARY_ASSERT">
            <data
                inType="win:UInt32"
                name="Line"
                />
            <data
                inType="win:AnsiString"
                name="File"
                />
            <data
                inType="win:AnsiString"
                name="Expression"
                />
          </template>
          <template tid="tid_LIBRARY_PERF_COUNTERS">
            <data
                inType="win:UInt16"
                name="PerfCountersLength"
                />
            <data
                inType="win:Binary"
                length="PerfCountersLength"
                name="PerfCounters"
                />
          </template>
          <template tid="tid_LIBRARY_VERSION">
            <data
                inType="win:UInt32"
                name="Major"
                />
            <data
                inType="win:UInt32"
                name="Minor"
                />
            <data
                inType="win:UInt32"
                name="Patch"
                />
            <data
                inType="win:UInt32"
                name="Build"
                />
          </template>
          <template tid="tid_API_ENTER">
            <data
                inType="win:UInt32"
                map="map_QUIC_TRACE_API_TYPE"
                name="Type"
                />
            <data
                inType="win:Pointer"
                name="Handle"
                />
          </template>
          <template tid="tid_API_EXIT_STATUS">
            <data
                inType="win:UInt32"
                name="Status"
                />
          </template>
          <template tid="tid_REGISTRATION">
            <data
                inType="win:Pointer"
                name="Registration"
                />
          </template>
          <template tid="tid_REGISTRATION_STRING">
            <data
                inType="win:Pointer"
                name="Registration"
                />
            <data
                inType="win:AnsiString"
                name="AppName"
                />
          </template>
          <template tid="tid_REGISTRATION_STRING_EXEC">
            <data
                inType="win:Pointer"
                name="Registration"
                />
            <data
                inType="win:AnsiString"
                name="AppName"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_EXECUTION_PROFILE"
                name="ExecProfile"
                />
          </template>
          <template tid="tid_REGISTRATION_ERROR">
            <data
                inType="win:Pointer"
                name="Registration"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_REGISTRATION_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Registration"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_REGISTRATION_FLAGS_ERRORCODE">
            <data
                inType="win:Pointer"
                name="Registration"
                />
            <data
                inType="win:UInt32"
                name="Flags"
                />
            <data
                inType="win:UInt64"
                name="ErrorCode"
                />
          </template>
          <template tid="tid_WORKER">
            <data
                inType="win:Pointer"
                name="Worker"
                />
          </template>
          <template tid="tid_WORKER_BEGIN">
            <data
                inType="win:Pointer"
                name="Worker"
                />
            <data
                inType="win:UInt16"
                name="IdealProcessor"
                />
            <data
                inType="win:Pointer"
                name="Owner"
                />
          </template>
          <template tid="tid_WORKER_ACTIVITY_STATE">
            <data
                inType="win:Pointer"
                name="Worker"
                />
            <data
                inType="win:UInt8"
                name="IsActive"
                />
            <data
                inType="win:UInt32"
                name="Arg"
                />
          </template>
          <template tid="tid_WORKER_QUEUE_DELAY">
            <data
                inType="win:Pointer"
                name="Worker"
                />
            <data
                inType="win:UInt32"
                name="QueueDelay"
                />
          </template>
          <template tid="tid_WORKER_ERROR">
            <data
                inType="win:Pointer"
                name="Worker"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_WORKER_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Worker"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_CONFIGURATION">
            <data
                inType="win:Pointer"
                name="Configuration"
                />
          </template>
          <template tid="tid_CONFIGURATION_REGISTRATION">
            <data
                inType="win:Pointer"
                name="Configuration"
                />
            <data
                inType="win:Pointer"
                name="Registration"
                />
          </template>
          <template tid="tid_CONFIGURATION_ERROR">
            <data
                inType="win:Pointer"
                name="Configuration"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_CONFIGURATION_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Configuration"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_LISTENER">
            <data
                inType="win:Pointer"
                name="Listener"
                />
          </template>
          <template tid="tid_LISTENER_REGISTRATION">
            <data
                inType="win:Pointer"
                name="Listener"
                />
            <data
                inType="win:Pointer"
                name="Registration"
                />
          </template>
          <template tid="tid_LISTENER_IP_ADDR_BINDING">
            <data
                inType="win:Pointer"
                name="Listener"
                />
            <data
                inType="win:Pointer"
                name="Binding"
                />
            <data
                inType="win:UInt8"
                name="AddrLength"
                />
            <data
                inType="win:Binary"
                length="AddrLength"
                name="Addr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="AlpnLength"
                />
            <data
                inType="win:Binary"
                length="AlpnLength"
                name="Alpn"
                />
          </template>
          <template tid="tid_LISTENER_ERROR">
            <data
                inType="win:Pointer"
                name="Listener"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_LISTENER_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Listener"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_CONN">
            <data
                inType="win:Pointer"
                name="Connection"
                />
          </template>
          <template tid="tid_CONN_MESSAGE">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:AnsiString"
                name="Message"
                />
          </template>
          <template tid="tid_CONN_UINT32">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="Value"
                />
          </template>
          <template tid="tid_CONN_BEGIN">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="IsServer"
                />
            <data
                inType="win:UInt64"
                name="CorrelationId"
                />
          </template>
          <template tid="tid_CONN_SCHEDULE_STATE">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_SCHEDULE_STATE"
                name="State"
                />
          </template>
          <template tid="tid_CONN_OPER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_OPERATION_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_CONN_API_OPER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_API_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_CONN_IP_ADDR">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="AddrLength"
                />
            <data
                inType="win:Binary"
                length="AddrLength"
                name="Addr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_CONN_CID">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="SequenceNumber"
                />
            <data
                inType="win:UInt8"
                name="CidLength"
                />
            <data
                inType="win:Binary"
                length="CidLength"
                name="Cid"
                />
          </template>
          <template tid="tid_CONN_TIMER_OPER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_CONN_TIMER_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_CONN_WORKER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:Pointer"
                name="Worker"
                />
          </template>
          <template tid="tid_CONN_REGISTRATION">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:Pointer"
                name="Registration"
                />
          </template>
          <template tid="tid_CONN_TRANSPORT_SHUTDOWN">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="ErrorCode"
                />
            <data
                inType="win:UInt8"
                name="IsRemoteShutdown"
                />
            <data
                inType="win:UInt8"
                name="IsQuicStatus"
                />
          </template>
          <template tid="tid_CONN_APP_SHUTDOWN">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="ErrorCode"
                />
            <data
                inType="win:UInt8"
                name="IsRemoteShutdown"
                />
          </template>
          <template tid="tid_CONN_OUT_FLOW_STATS">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="BytesSent"
                />
            <data
                inType="win:UInt32"
                name="BytesInFlight"
                />
            <data
                inType="win:UInt32"
                name="BytesInFlightMax"
                />
            <data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:UInt32"
                name="SlowStartThreshold"
                />
            <data
                inType="win:UInt64"
                name="ConnectionFlowControl"
                />
            <data
                inType="win:UInt64"
                name="IdealBytes"
                />
            <data
                inType="win:UInt64"
                name="PostedBytes"
                />
            <data
                inType="win:UInt32"
                name="SmoothedRtt"
                />
          </template>
          <template tid="tid_CONN_OUT_FLOW_STATS_V2">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="BytesSent"
                />
            <data
                inType="win:UInt32"
                name="BytesInFlight"
                />
            <data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:UInt64"
                name="ConnectionFlowControl"
                />
            <data
                inType="win:UInt64"
                name="IdealBytes"
                />
            <data
                inType="win:UInt64"
                name="PostedBytes"
                />
            <data
                inType="win:UInt64"
                name="SmoothedRtt"
                />
            <data
                inType="win:UInt64"
                name="OneWayDelay"
                />
          </template>
          <template tid="tid_CONN_OUT_FLOW_STREAM_STATS">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="StreamFlowControl"
                />
            <data
                inType="win:UInt64"
                name="StreamSendWindow"
                />
          </template>
          <template tid="tid_CONN_OUT_FLOW_BLOCKED">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="ReasonFlags"
                />
          </template>
          <template tid="tid_CONN_IN_FLOW_STATS">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="BytesReceived"
                />
          </template>
          <template tid="tid_CONN_CUBIC">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="SlowStartThreshold"
                />
            <data
                inType="win:UInt32"
                name="K"
                />
            <data
                inType="win:UInt32"
                name="WindowMax"
                />
            <data
                inType="win:UInt32"
                name="WindowLastMax"
                />
          </template>
          <template tid="tid_CONN_CUBIC_HYSTART">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_CONN_CUBIC_HYSTART_STATE"
                name="HyStartState"
                />
            <data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:UInt32"
                name="SlowStartThreshold"
                />
          </template>
          <template tid="tid_CONN_LOSS_TIMER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_LOSS_TIMER_TYPE"
                name="Type"
                />
            <data
                inType="win:UInt32"
                name="DelayMs"
                />
            <data
                inType="win:UInt16"
                name="ProbeCount"
                />
          </template>
          <template tid="tid_CONN_ERROR">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_CONN_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_CONN_KEY_PHASE">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="IsLocallyInitiated"
                />
          </template>
          <template tid="tid_CONN_STATISTICS">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="SmoothedRtt"
                />
            <data
                inType="win:UInt32"
                name="CongestionCount"
                />
            <data
                inType="win:UInt32"
                name="PersistentCongestionCount"
                />
            <data
                inType="win:UInt64"
                name="SendTotalBytes"
                />
            <data
                inType="win:UInt64"
                name="RecvTotalBytes"
                />
			<data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:AnsiString"
                name="Cc"
                />
          </template>
          <template tid="tid_CONN_PACKET_STATISTICS">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="SendTotalPackets"
                />
            <data
                inType="win:UInt64"
                name="SendSuspectedLostPackets"
                />
            <data
                inType="win:UInt64"
                name="SendSpuriousLostPackets"
                />
            <data
                inType="win:UInt64"
                name="RecvTotalPackets"
                />
            <data
                inType="win:UInt64"
                name="RecvReorderedPackets"
                />
            <data
                inType="win:UInt64"
                name="RecvDroppedPackets"
                />
            <data
                inType="win:UInt64"
                name="RecvDuplicatePackets"
                />
            <data
                inType="win:UInt64"
                name="RecvDecryptionFailures"
                />
          </template>
          <template tid="tid_CONN_BOOL">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="Value"
                />
          </template>
          <template tid="tid_CONN_KEY_UPDATE">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="NewValue"
                />
          </template>
          <template tid="tid_CONN_FLUSH_REASON">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_SEND_FLUSH_REASON"
                name="Type"
                />
          </template>
          <template tid="tid_CONN_SERVER_RESUME_TICKET">
            <data
                inType="win:Pointer"
                name="Connection"
                />
          </template>
          <template tid="tid_CONN_VNL">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="VnlLength"
                />
            <data
                inType="win:Binary"
                length="VnlLength"
                name="Vnl"
                />
          </template>
          <template tid="tid_CONN_ALPN">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="AlpnLength"
                />
            <data
                inType="win:Binary"
                length="AlpnLength"
                name="Alpn"
                />
          </template>
          <template tid="tid_CONN_U32">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="Value"
                />
          </template>
          <template tid="tid_CONN_TIMER">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_CONN_TIMER_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_CONN_TIMER_DELAY">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_CONN_TIMER_TYPE"
                name="Type"
                />
            <data
                inType="win:UInt64"
                name="Delay"
                />
          </template>
          <template tid="tid_CONN_BBR">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="BbrState"
                />
            <data
                inType="win:UInt32"
                name="RecoveryState"
                />
            <data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:UInt32"
                name="BytesInFlight"
                />
            <data
                inType="win:UInt32"
                name="BytesInFlightMax"
                />
            <data
                inType="win:UInt64"
                name="EstMinRtt"
                />
            <data
                inType="win:UInt64"
                name="EstBw"
                />
            <data
                inType="win:UInt32"
                name="IsAppLimited"
                />
          </template>
          <template tid="tid_STREAM">
            <data
                inType="win:Pointer"
                name="Stream"
                />
          </template>
          <template tid="tid_STREAM_BEGIN">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="ID"
                />
            <data
                inType="win:UInt8"
                name="IsLocalOwned"
                />
          </template>
          <template tid="tid_STREAM_OUT_FLOW_BLOCKED">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt8"
                name="ReasonFlags"
                />
          </template>
          <template tid="tid_STREAM_SEND_STATE">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_STREAM_SEND_STATE"
                name="State"
                />
          </template>
          <template tid="tid_STREAM_RECV_STATE">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_STREAM_RECV_STATE"
                name="State"
                />
          </template>
          <template tid="tid_STREAM_ERROR">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_STREAM_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_STREAM_MESSAGE">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:AnsiString"
                name="Message"
                />
          </template>
          <template tid="tid_STREAM_CONN">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:Pointer"
                name="Connection"
                />
          </template>
          <template tid="tid_STREAM_U64">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt64"
                name="ID"
                />
          </template>
          <template tid="tid_STREAM_RECV">
            <data
                inType="win:Pointer"
                name="Stream"
                />
            <data
                inType="win:UInt64"
                name="BufLength"
                />
            <data
                inType="win:UInt32"
                name="BufCount"
                />
            <data
                inType="win:UInt32"
                name="Flags"
                />
          </template>
          <template tid="tid_BINDING">
            <data
                inType="win:Pointer"
                name="Binding"
                />
          </template>
          <template tid="tid_BINDING_CREATED">
            <data
                inType="win:Pointer"
                name="Binding"
                />
            <data
                inType="win:Pointer"
                name="Socket"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_BINDING_ERROR">
            <data
                inType="win:Pointer"
                name="Binding"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_BINDING_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="Binding"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_BINDING_OPER">
            <data
                inType="win:Pointer"
                name="Binding"
                />
            <data
                inType="win:UInt32"
                map="map_QUIC_OPERATION_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_PACKET_DROP">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:AnsiString"
                name="Reason"
                />
          </template>
          <template tid="tid_PACKET_DROP_EX">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt64"
                name="Value"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:AnsiString"
                name="Reason"
                />
          </template>
          <template tid="tid_PACKET_SENT">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt64"
                name="Number"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_TRACE_PACKET_TYPE"
                name="Type"
                />
            <data
                inType="win:UInt16"
                name="Length"
                />
          </template>
          <template tid="tid_PACKET_RECV">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt64"
                name="Number"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_TRACE_PACKET_TYPE"
                name="Type"
                />
            <data
                inType="win:UInt16"
                name="Length"
                />
          </template>
          <template tid="tid_PACKET_LOST">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt64"
                name="Number"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_TRACE_PACKET_TYPE"
                name="Type"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_TRACE_PACKET_LOSS_REASON"
                name="Reason"
                />
          </template>
          <template tid="tid_PACKET_ACKED">
            <data
                inType="win:Pointer"
                name="Owner"
                />
            <data
                inType="win:UInt64"
                name="Number"
                />
            <data
                inType="win:UInt8"
                map="map_QUIC_TRACE_PACKET_TYPE"
                name="Type"
                />
          </template>
          <template tid="tid_DESC_COUNT">
            <data
                inType="win:AnsiString"
                name="Desc"
                />
            <data
                inType="win:UInt64"
                name="ByteCount"
                />
          </template>
          <template tid="tid_DATA_SEND">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt32"
                name="TotalSize"
                />
            <data
                inType="win:UInt8"
                name="BufferCount"
                />
            <data
                inType="win:UInt16"
                name="SegmentSize"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_DATA_SEND_TCP_CONTROL">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt32"
                name="SegmentSize"
                />
            <data
                inType="win:UInt8"
                name="TcpFlags"
                outType="win:HexInt8"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_DATA_SEND_RECV">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt32"
                name="TotalSize"
                />
            <data
                inType="win:UInt16"
                name="SegmentSize"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_UDP">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
          </template>
          <template tid="tid_UDP_ERROR">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_UDP_STATUS_ERROR">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt32"
                name="Status"
                />
            <data
                inType="win:AnsiString"
                name="ErrStr"
                />
          </template>
          <template tid="tid_UDP_CREATE">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_UDP_ADDRESSES">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
            <data
                inType="win:UInt8"
                name="RemoteAddrLength"
                />
            <data
                inType="win:Binary"
                length="RemoteAddrLength"
                name="RemoteAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_UDP_ADDRESS">
            <data
                inType="win:Pointer"
                name="UdpBinding"
                />
            <data
                inType="win:UInt8"
                name="LocalAddrLength"
                />
            <data
                inType="win:Binary"
                length="LocalAddrLength"
                name="LocalAddr"
                outType="win:SocketAddress"
                />
          </template>
          <template tid="tid_ID">
            <data
                inType="win:UInt64"
                name="ID"
                />
          </template>
          <template tid="tid_ID_ID">
            <data
                inType="win:UInt64"
                name="ID"
                />
            <data
                inType="win:UInt64"
                name="ID2"
                />
          </template>
          <template tid="tid_CONN_ECN_CAPABLE">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="IsCapable"
                />
          </template>
          <template tid="tid_CONN_ECN_FAILED">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="EncryptLevel"
                />
            <data
                inType="win:UInt64"
                name="EcnEctCounter"
                />
            <data
                inType="win:UInt64"
                name="EcnCeCounter"
                />
            <data
                inType="win:UInt64"
                name="NumPacketsSentWithEct"
                />
            <data
                inType="win:Int64"
                name="EctCeDeltaSum"
                />
            <data
                inType="win:UInt8"
                name="State"
                />
          </template>
          <template tid="tid_CONN_CONGESTION">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt8"
                name="IsEcn"
                />
          </template>
          <template tid="tid_CONN_STATISTICS_V2">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="SmoothedRtt"
                />
            <data
                inType="win:UInt32"
                name="CongestionCount"
                />
            <data
                inType="win:UInt32"
                name="PersistentCongestionCount"
                />
            <data
                inType="win:UInt64"
                name="SendTotalBytes"
                />
            <data
                inType="win:UInt64"
                name="RecvTotalBytes"
                />
			<data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:AnsiString"
                name="Cc"
                />
            <data
                inType="win:UInt32"
                name="EcnCongestionCount"
                />
          </template>
          <template tid="tid_CONN_STATISTICS_V3">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt64"
                name="SmoothedRtt"
                />
            <data
                inType="win:UInt32"
                name="CongestionCount"
                />
            <data
                inType="win:UInt32"
                name="PersistentCongestionCount"
                />
            <data
                inType="win:UInt64"
                name="SendTotalBytes"
                />
            <data
                inType="win:UInt64"
                name="RecvTotalBytes"
                />
			<data
                inType="win:UInt32"
                name="CongestionWindow"
                />
            <data
                inType="win:AnsiString"
                name="Cc"
                />
            <data
                inType="win:UInt32"
                name="EcnCongestionCount"
                />
          </template>
          <template tid="tid_CONN_COUNT_COUNT">
            <data
                inType="win:Pointer"
                name="Connection"
                />
            <data
                inType="win:UInt32"
                name="Count"
                />
            <data
                inType="win:UInt32"
                name="Count2"
                />
          </template>
        </templates>
        <events>
          <!-- Don't use value=0 as some parsers don't like it -->
          <!--    1 - 1023 | Global/Library Events -->
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryInitialized)"
              opcode="Global"
              symbol="QuicLibraryInitialized"
              template="tid_LIBRARY_INIT"
              value="1"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryUninitialized)"
              opcode="Global"
              symbol="QuicLibraryUninitialized"
              value="2"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryAddRef)"
              opcode="Global"
              symbol="QuicLibraryAddRef"
              value="3"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryRelease)"
              opcode="Global"
              symbol="QuicLibraryRelease"
              value="4"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryServerInit)"
              opcode="Global"
              symbol="QuicLibraryServerInit"
              value="5"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Warning"
              message="$(string.Etw.AllocFailure)"
              opcode="Global"
              symbol="QuicAllocFailure"
              template="tid_DESC_COUNT"
              value="6"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryRundown)"
              opcode="Global"
              symbol="QuicLibraryRundown"
              template="tid_LIBRARY_INIT"
              value="7"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.LibraryError)"
              opcode="Global"
              symbol="QuicLibraryError"
              template="tid_LIBRARY_ERROR"
              value="8"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.LibraryErrorStatus)"
              opcode="Global"
              symbol="QuicLibraryErrorStatus"
              template="tid_LIBRARY_STATUS_ERROR"
              value="9"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.LibraryAssert)"
              opcode="Global"
              symbol="QuicLibraryAssert"
              template="tid_LIBRARY_ASSERT"
              value="10"
              />
          <event
              keywords="ut:Api"
              level="win:Verbose"
              message="$(string.Etw.ApiEnter)"
              opcode="Global"
              symbol="QuicApiEnter"
              template="tid_API_ENTER"
              value="11"
              />
          <event
              keywords="ut:Api"
              level="win:Verbose"
              message="$(string.Etw.ApiExit)"
              opcode="Global"
              symbol="QuicApiExit"
              value="12"
              />
          <event
              keywords="ut:Api"
              level="win:Verbose"
              message="$(string.Etw.ApiExitStatus)"
              opcode="Global"
              symbol="QuicApiExitStatus"
              template="tid_API_EXIT_STATUS"
              value="13"
              />
          <event
              keywords="ut:Api"
              level="win:Verbose"
              message="$(string.Etw.ApiWaitOperation)"
              opcode="Global"
              symbol="QuicApiWaitOperation"
              value="14"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.PerfCountersRundown)"
              opcode="Global"
              symbol="QuicPerfCountersRundown"
              template="tid_LIBRARY_PERF_COUNTERS"
              value="15"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibrarySendRetryStateUpdated)"
              opcode="Global"
              symbol="QuicLibrarySendRetryStateUpdated"
              template="tid_BOOL"
              value="16"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryVersion)"
              opcode="Global"
              symbol="QuicLibraryVersion"
              template="tid_LIBRARY_VERSION"
              value="17"
              />
          <event
              keywords="ut:Api"
              level="win:Error"
              message="$(string.Etw.ApiError)"
              opcode="Global"
              symbol="QuicApiError"
              template="tid_API_EXIT_STATUS"
              value="18"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryInitializedV2)"
              opcode="Global"
              symbol="QuicLibraryInitializedV2"
              template="tid_LIBRARY_INIT_V2"
              value="19"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DataPathInitialized)"
              opcode="Global"
              symbol="QuicDataPathInitialized"
              template="tid_DATAPATH_INIT"
              value="20"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryRundownV2)"
              opcode="Global"
              symbol="QuicLibraryRundownV2"
              template="tid_LIBRARY_INIT_V2"
              value="21"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DataPathRundown)"
              opcode="Global"
              symbol="QuicDataPathRundown"
              template="tid_DATAPATH_INIT"
              value="22"
              />
          <event
              keywords="ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.LibraryInitializedV3)"
              opcode="Global"
              symbol="QuicLibraryInitializedV3"
              value="23"
              />
          <!-- 1024 - 2047 | Registration Events -->
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationCreated)"
              opcode="Registration"
              symbol="QuicRegistrationCreated"
              template="tid_REGISTRATION_STRING"
              value="1024"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationDestroyed)"
              opcode="Registration"
              symbol="QuicRegistrationDestroyed"
              template="tid_REGISTRATION"
              value="1025"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationCleanup)"
              opcode="Registration"
              symbol="QuicRegistrationCleanup"
              template="tid_REGISTRATION"
              value="1026"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationRundown)"
              opcode="Registration"
              symbol="QuicRegistrationRundown"
              template="tid_REGISTRATION_STRING"
              value="1027"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.RegistrationError)"
              opcode="Registration"
              symbol="QuicRegistrationError"
              template="tid_REGISTRATION_ERROR"
              value="1028"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.RegistrationErrorStatus)"
              opcode="Registration"
              symbol="QuicRegistrationErrorStatus"
              template="tid_REGISTRATION_STATUS_ERROR"
              value="1029"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationShutdown)"
              opcode="Registration"
              symbol="QuicRegistrationShutdown"
              template="tid_REGISTRATION_FLAGS_ERRORCODE"
              value="1030"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationCreatedV2)"
              opcode="Registration"
              symbol="QuicRegistrationCreatedV2"
              template="tid_REGISTRATION_STRING_EXEC"
              value="1031"
              />
          <event
              keywords="ut:Registration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.RegistrationRundownV2)"
              opcode="Registration"
              symbol="QuicRegistrationRundownV2"
              template="tid_REGISTRATION_STRING_EXEC"
              value="1032"
              />
          <!-- 2048 - 3071 | Worker Events -->
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.WorkerCreated)"
              opcode="Worker"
              symbol="QuicWorkerCreated"
              template="tid_WORKER_BEGIN"
              value="2048"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.WorkerStart)"
              opcode="Worker"
              symbol="QuicWorkerStart"
              template="tid_WORKER"
              value="2049"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.WorkerStop)"
              opcode="Worker"
              symbol="QuicWorkerStop"
              template="tid_WORKER"
              value="2050"
              />
          <event
              keywords="ut:Worker ut:Scheduling ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.WorkerActivityStateUpdated)"
              opcode="Worker"
              symbol="QuicWorkerActivityStateUpdated"
              template="tid_WORKER_ACTIVITY_STATE"
              value="2051"
              />
          <event
              keywords="ut:Worker ut:Scheduling ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.WorkerQueueDelayUpdated)"
              opcode="Worker"
              symbol="QuicWorkerQueueDelayUpdated"
              template="tid_WORKER_QUEUE_DELAY"
              value="2052"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.WorkerDestroyed)"
              opcode="Worker"
              symbol="QuicWorkerDestroyed"
              template="tid_WORKER"
              value="2053"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.WorkerCleanup)"
              opcode="Worker"
              symbol="QuicWorkerCleanup"
              template="tid_WORKER"
              value="2054"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.WorkerError)"
              opcode="Worker"
              symbol="QuicWorkerError"
              template="tid_WORKER_ERROR"
              value="2055"
              />
          <event
              keywords="ut:Worker ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.WorkerErrorStatus)"
              opcode="Worker"
              symbol="QuicWorkerErrorStatus"
              template="tid_WORKER_STATUS_ERROR"
              value="2056"
              />
          <!-- 3072 - 4095 | Configuration Events -->
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConfigurationCreated)"
              opcode="Configuration"
              symbol="QuicConfigurationCreated"
              template="tid_CONFIGURATION_REGISTRATION"
              value="3072"
              />
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConfigurationDestroyed)"
              opcode="Configuration"
              symbol="QuicConfigurationDestroyed"
              template="tid_CONFIGURATION"
              value="3073"
              />
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConfigurationCleanup)"
              opcode="Configuration"
              symbol="QuicConfigurationCleanup"
              template="tid_CONFIGURATION"
              value="3074"
              />
          <!-- missing -->
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConfigurationRundown)"
              opcode="Configuration"
              symbol="QuicConfigurationRundown"
              template="tid_CONFIGURATION_REGISTRATION"
              value="3076"
              />
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConfigurationError)"
              opcode="Configuration"
              symbol="QuicConfigurationError"
              template="tid_CONFIGURATION_ERROR"
              value="3077"
              />
          <event
              keywords="ut:Configuration ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConfigurationErrorStatus)"
              opcode="Configuration"
              symbol="QuicConfigurationErrorStatus"
              template="tid_CONFIGURATION_STATUS_ERROR"
              value="3078"
              />
          <!-- 4096 - 5119 | Listener Events -->
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ListenerCreated)"
              opcode="Listener"
              symbol="QuicListenerCreated"
              template="tid_LISTENER_REGISTRATION"
              value="4096"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ListenerDestroyed)"
              opcode="Listener"
              symbol="QuicListenerDestroyed"
              template="tid_LISTENER"
              value="4097"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ListenerStarted)"
              opcode="Listener"
              symbol="QuicListenerStarted"
              template="tid_LISTENER_IP_ADDR_BINDING"
              value="4098"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ListenerStopped)"
              opcode="Listener"
              symbol="QuicListenerStopped"
              template="tid_LISTENER"
              value="4099"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ListenerRundown)"
              opcode="Listener"
              symbol="QuicListenerRundown"
              template="tid_LISTENER_REGISTRATION"
              value="4100"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ListenerError)"
              opcode="Listener"
              symbol="QuicListenerError"
              template="tid_LISTENER_ERROR"
              value="4101"
              />
          <event
              keywords="ut:Listener ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ListenerErrorStatus)"
              opcode="Listener"
              symbol="QuicListenerErrorStatus"
              template="tid_LISTENER_STATUS_ERROR"
              value="4102"
              />
          <!-- 5120 - 6143 | Connection Events -->
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnCreated)"
              opcode="Connection"
              symbol="QuicConnCreated"
              template="tid_CONN_BEGIN"
              value="5120"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnDestroyed)"
              opcode="Connection"
              symbol="QuicConnDestroyed"
              template="tid_CONN"
              value="5121"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnHandshakeComplete)"
              opcode="Connection"
              symbol="QuicConnHandshakeComplete"
              template="tid_CONN"
              value="5122"
              />
          <event
              keywords="ut:Connection ut:Scheduling ut:RPS"
              level="win:Informational"
              message="$(string.Etw.ConnScheduleState)"
              opcode="Connection"
              symbol="QuicConnScheduleState"
              template="tid_CONN_SCHEDULE_STATE"
              value="5123"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnExecOper)"
              opcode="Connection"
              symbol="QuicConnExecOper"
              template="tid_CONN_OPER"
              value="5124"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnExecOper)"
              opcode="Connection"
              symbol="QuicConnExecApiOper"
              template="tid_CONN_API_OPER"
              value="5125"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnExecOper)"
              opcode="Connection"
              symbol="QuicConnExecTimerOper"
              template="tid_CONN_TIMER_OPER"
              value="5126"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnLocalAddrAdd)"
              opcode="Connection"
              symbol="QuicConnLocalAddrAdded"
              template="tid_CONN_IP_ADDR"
              value="5127"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnRemoteAddrAdd)"
              opcode="Connection"
              symbol="QuicConnRemoteAddrAdded"
              template="tid_CONN_IP_ADDR"
              value="5128"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnLocalAddrRemove)"
              opcode="Connection"
              symbol="QuicConnLocalAddrRemoved"
              template="tid_CONN_IP_ADDR"
              value="5129"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnRemoteAddrRemove)"
              opcode="Connection"
              symbol="QuicConnRemoteAddrRemoved"
              template="tid_CONN_IP_ADDR"
              value="5130"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Informational"
              message="$(string.Etw.ConnAssignWorker)"
              opcode="Connection"
              symbol="QuicConnAssignWorker"
              template="tid_CONN_WORKER"
              value="5131"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnHandshakeStart)"
              opcode="Connection"
              symbol="QuicConnHandshakeStart"
              template="tid_CONN"
              value="5132"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnRegistered)"
              opcode="Connection"
              symbol="QuicConnRegistered"
              template="tid_CONN_REGISTRATION"
              value="5133"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnUnregistered)"
              opcode="Connection"
              symbol="QuicConnUnregistered"
              template="tid_CONN_REGISTRATION"
              value="5134"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnTransportShutdown)"
              opcode="Connection"
              symbol="QuicConnTransportShutdown"
              template="tid_CONN_TRANSPORT_SHUTDOWN"
              value="5135"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnAppShutdown)"
              opcode="Connection"
              symbol="QuicConnAppShutdown"
              template="tid_CONN_APP_SHUTDOWN"
              value="5136"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnInitializeComplete)"
              opcode="Connection"
              symbol="QuicConnInitializeComplete"
              template="tid_CONN"
              value="5137"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnHandleClosed)"
              opcode="Connection"
              symbol="QuicConnHandleClosed"
              template="tid_CONN"
              value="5138"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnVersionSet)"
              opcode="Connection"
              symbol="QuicConnVersionSet"
              template="tid_CONN_UINT32"
              value="5139"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.ConnOutFlowStats)"
              opcode="Connection"
              symbol="QuicConnOutFlowStats"
              template="tid_CONN_OUT_FLOW_STATS"
              value="5140"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Informational"
              message="$(string.Etw.ConnOutFlowBlocked)"
              opcode="Connection"
              symbol="QuicConnOutFlowBlocked"
              template="tid_CONN_OUT_FLOW_BLOCKED"
              value="5141"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.ConnInFlowStats)"
              opcode="Connection"
              symbol="QuicConnInFlowStats"
              template="tid_CONN_IN_FLOW_STATS"
              value="5142"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.Cubic)"
              opcode="Connection"
              symbol="QuicConnCubic"
              template="tid_CONN_CUBIC"
              value="5143"
              />
          <event
              keywords="ut:Connection ut:DataFlow ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.CongestionEvent)"
              opcode="Connection"
              symbol="QuicConnCongestion"
              template="tid_CONN"
              value="5144"
              />
          <event
              keywords="ut:Connection ut:DataFlow ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.PersistentCongestionEvent)"
              opcode="Connection"
              symbol="QuicConnPersistentCongestion"
              template="tid_CONN"
              value="5145"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Informational"
              message="$(string.Etw.RecoveryExit)"
              opcode="Connection"
              symbol="QuicConnRecoveryExit"
              template="tid_CONN"
              value="5146"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnRundown)"
              opcode="Connection"
              symbol="QuicConnRundown"
              template="tid_CONN_BEGIN"
              value="5147"
              />
          <event
              keywords="ut:Connection ut:LowVolume ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.ConnSourceCidAdd)"
              opcode="Connection"
              symbol="QuicConnSourceCidAdded"
              template="tid_CONN_CID"
              value="5148"
              />
          <event
              keywords="ut:Connection ut:LowVolume ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.ConnDestCidAdd)"
              opcode="Connection"
              symbol="QuicConnDestCidAdded"
              template="tid_CONN_CID"
              value="5149"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnSourceCidRemove)"
              opcode="Connection"
              symbol="QuicConnSourceCidRemoved"
              template="tid_CONN_CID"
              value="5150"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnDestCidRemove)"
              opcode="Connection"
              symbol="QuicConnDestCidRemoved"
              template="tid_CONN_CID"
              value="5151"
              />
          <event
              keywords="ut:Connection"
              level="win:Informational"
              message="$(string.Etw.ConnLossDetectionTimerSet)"
              opcode="Connection"
              symbol="QuicConnLossDetectionTimerSet"
              template="tid_CONN_LOSS_TIMER"
              value="5152"
              />
          <event
              keywords="ut:Connection"
              level="win:Informational"
              message="$(string.Etw.ConnLossDetectionTimerCancel)"
              opcode="Connection"
              symbol="QuicConnLossDetectionTimerCancel"
              template="tid_CONN"
              value="5153"
              />
          <event
              keywords="ut:Connection ut:Packet ut:RPS ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnDropPacket)"
              opcode="Connection"
              symbol="QuicConnDropPacket"
              template="tid_PACKET_DROP"
              value="5154"
              />
          <event
              keywords="ut:Connection ut:Packet ut:RPS ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnDropPacketEx)"
              opcode="Connection"
              symbol="QuicConnDropPacketEx"
              template="tid_PACKET_DROP_EX"
              value="5155"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConnError)"
              opcode="Connection"
              symbol="QuicConnError"
              template="tid_CONN_ERROR"
              value="5156"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConnErrorStatus)"
              opcode="Connection"
              symbol="QuicConnErrorStatus"
              template="tid_CONN_STATUS_ERROR"
              value="5157"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnNewPacketKeys)"
              opcode="Connection"
              symbol="QuicConnNewPacketKeys"
              template="tid_CONN"
              value="5158"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnKeyPhaseChange)"
              opcode="Connection"
              symbol="QuicConnKeyPhaseChange"
              template="tid_CONN_KEY_PHASE"
              value="5159"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnStats)"
              opcode="Connection"
              symbol="QuicConnStats"
              template="tid_CONN_STATISTICS"
              value="5160"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnShutdownComplete)"
              opcode="Connection"
              symbol="QuicConnShutdownComplete"
              template="tid_CONN_BOOL"
              value="5161"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnReadKeyUpdated)"
              opcode="Connection"
              symbol="QuicConnReadKeyUpdated"
              template="tid_CONN_KEY_UPDATE"
              value="5162"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.ConnWriteKeyUpdated)"
              opcode="Connection"
              symbol="QuicConnWriteKeyUpdated"
              template="tid_CONN_KEY_UPDATE"
              value="5163"
              />
          <event
              keywords="ut:Connection ut:Packet"
              level="win:Verbose"
              message="$(string.Etw.ConnPacketSent)"
              opcode="Connection"
              symbol="QuicConnPacketSent"
              template="tid_PACKET_SENT"
              value="5164"
              />
          <event
              keywords="ut:Connection ut:Packet"
              level="win:Verbose"
              message="$(string.Etw.ConnPacketRecv)"
              opcode="Connection"
              symbol="QuicConnPacketRecv"
              template="tid_PACKET_RECV"
              value="5165"
              />
          <event
              keywords="ut:Connection ut:Packet"
              level="win:Informational"
              message="$(string.Etw.ConnPacketLost)"
              opcode="Connection"
              symbol="QuicConnPacketLost"
              template="tid_PACKET_LOST"
              value="5166"
              />
          <event
              keywords="ut:Connection ut:Packet"
              level="win:Verbose"
              message="$(string.Etw.ConnPacketAcked)"
              opcode="Connection"
              symbol="QuicConnPacketACKed"
              template="tid_PACKET_ACKED"
              value="5167"
              />
          <event
              keywords="ut:Connection ut:Log"
              level="win:Error"
              message="$(string.Etw.ConnMessage)"
              opcode="Connection"
              symbol="QuicConnLogError"
              template="tid_CONN_MESSAGE"
              value="5168"
              />
          <event
              keywords="ut:Connection ut:Log"
              level="win:Warning"
              message="$(string.Etw.ConnMessage)"
              opcode="Connection"
              symbol="QuicConnLogWarning"
              template="tid_CONN_MESSAGE"
              value="5169"
              />
          <event
              keywords="ut:Connection ut:Log"
              level="win:Informational"
              message="$(string.Etw.ConnMessage)"
              opcode="Connection"
              symbol="QuicConnLogInfo"
              template="tid_CONN_MESSAGE"
              value="5170"
              />
          <event
              keywords="ut:Connection ut:Log"
              level="win:Verbose"
              message="$(string.Etw.ConnMessage)"
              opcode="Connection"
              symbol="QuicConnLogVerbose"
              template="tid_CONN_MESSAGE"
              value="5171"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnQueueSendFlush)"
              opcode="Connection"
              symbol="QuicConnQueueSendFlush"
              template="tid_CONN_FLUSH_REASON"
              value="5172"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.ConnOutFlowStreamStats)"
              opcode="Connection"
              symbol="QuicConnOutFlowStreamStats"
              template="tid_CONN_OUT_FLOW_STREAM_STATS"
              value="5173"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnPacketStats)"
              opcode="Connection"
              symbol="QuicConnPacketStats"
              template="tid_CONN_PACKET_STATISTICS"
              value="5174"
              />
          <event
              keywords="ut:Connection"
              level="win:Informational"
              message="$(string.Etw.ConnServerResumeTicket)"
              opcode="Connection"
              symbol="QuicConnServerResumeTicket"
              template="tid_CONN_SERVER_RESUME_TICKET"
              value="5175"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnVNEOtherVersionList)"
              opcode="Connection"
              symbol="QuicConnVNEOtherVersionList"
              template="tid_CONN_VNL"
              value="5176"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnClientReceivedVersionList)"
              opcode="Connection"
              symbol="QuicConnClientReceivedVersionList"
              template="tid_CONN_VNL"
              value="5177"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnServerSupportedVersionList)"
              opcode="Connection"
              symbol="QuicConnServerSupportedVersionList"
              template="tid_CONN_VNL"
              value="5178"
              />
          <event
              keywords="ut:Connection ut:DataFlow ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.SpuriousCongestionEvent)"
              opcode="Connection"
              symbol="QuicConnSpuriousCongestion"
              template="tid_CONN"
              value="5179"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConnNoListenerIp)"
              opcode="Connection"
              symbol="QuicConnNoListenerIp"
              template="tid_CONN_IP_ADDR"
              value="5180"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.ConnNoListenerAlpn)"
              opcode="Connection"
              symbol="QuicConnNoListenerAlpn"
              template="tid_CONN_ALPN"
              value="5181"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.ConnFlushSend)"
              opcode="Connection"
              symbol="QuicConnFlushSend"
              template="tid_CONN_U32"
              value="5182"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnSetTimer)"
              opcode="Connection"
              symbol="QuicConnSetTimer"
              template="tid_CONN_TIMER_DELAY"
              value="5183"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnCancelTimer)"
              opcode="Connection"
              symbol="QuicConnCancelTimer"
              template="tid_CONN_TIMER"
              value="5184"
              />
          <event
              keywords="ut:Connection ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.ConnExpiredTimer)"
              opcode="Connection"
              symbol="QuicConnExpiredTimer"
              template="tid_CONN_TIMER"
              value="5185"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.Bbr)"
              opcode="Connection"
              symbol="QuicConnBbr"
              template="tid_CONN_BBR"
              value="5186"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnEcnCapable)"
              opcode="Connection"
              symbol="QuicConnEcnCapable"
              template="tid_CONN_ECN_CAPABLE"
              value="5187"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnEcnFailed)"
              opcode="Connection"
              symbol="QuicConnEcnFailed"
              template="tid_CONN_ECN_FAILED"
              value="5188"
              />
          <event
              keywords="ut:Connection ut:DataFlow ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.CongestionEventV2)"
              opcode="Connection"
              symbol="QuicConnCongestionV2"
              template="tid_CONN_CONGESTION"
              value="5189"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnStatsV2)"
              opcode="Connection"
              symbol="QuicConnStatsV2"
              template="tid_CONN_STATISTICS_V2"
              value="5190"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Informational"
              message="$(string.Etw.CubicHyStart)"
              opcode="Connection"
              symbol="QuicConnHyStartStateChange"
              template="tid_CONN_CUBIC_HYSTART"
              value="5191"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Informational"
              message="$(string.Etw.ConnRecvUdpDatagrams)"
              opcode="Connection"
              symbol="QuicConnRecvUdpDatagrams"
              template="tid_CONN_COUNT_COUNT"
              value="5192"
              />
          <event
              keywords="ut:Connection ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.ConnOutFlowStatsV2)"
              opcode="Connection"
              symbol="QuicConnOutFlowStatsV2"
              template="tid_CONN_OUT_FLOW_STATS_V2"
              value="5193"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnStatsV2)"
              opcode="Connection"
              symbol="QuicConnStatsV3"
              template="tid_CONN_STATISTICS_V3"
              value="5194"
              />
          <event
              keywords="ut:Connection ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.ConnDelayCloseApplicationError)"
              opcode="Connection"
              symbol="QuicConnDelayCloseApplicationError"
              template="tid_CONN"
              value="5195"
              />
          <!-- 6144 - 7167 | Stream Events -->
          <event
              keywords="ut:Stream ut:LowVolume ut:RPS"
              level="win:Informational"
              message="$(string.Etw.StreamCreated)"
              opcode="Stream"
              symbol="QuicStreamCreated"
              template="tid_STREAM_BEGIN"
              value="6144"
              />
          <event
              keywords="ut:Stream ut:LowVolume ut:RPS"
              level="win:Informational"
              message="$(string.Etw.StreamDestroyed)"
              opcode="Stream"
              symbol="QuicStreamDestroyed"
              template="tid_STREAM"
              value="6145"
              />
          <event
              keywords="ut:Stream ut:DataFlow"
              level="win:Informational"
              message="$(string.Etw.StreamOutFlowBlocked)"
              opcode="Stream"
              symbol="QuicStreamOutFlowBlocked"
              template="tid_STREAM_OUT_FLOW_BLOCKED"
              value="6146"
              />
          <event
              keywords="ut:Stream ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.StreamRundown)"
              opcode="Stream"
              symbol="QuicStreamRundown"
              template="tid_STREAM_BEGIN"
              value="6147"
              />
          <event
              keywords="ut:Stream ut:LowVolume ut:RPS"
              level="win:Informational"
              message="$(string.Etw.StreamSendState)"
              opcode="Stream"
              symbol="QuicStreamSendState"
              template="tid_STREAM_SEND_STATE"
              value="6148"
              />
          <event
              keywords="ut:Stream ut:LowVolume ut:RPS"
              level="win:Informational"
              message="$(string.Etw.StreamRecvState)"
              opcode="Stream"
              symbol="QuicStreamRecvState"
              template="tid_STREAM_RECV_STATE"
              value="6149"
              />
          <event
              keywords="ut:Stream ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.StreamError)"
              opcode="Stream"
              symbol="QuicStreamError"
              template="tid_STREAM_ERROR"
              value="6150"
              />
          <event
              keywords="ut:Stream ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.StreamErrorStatus)"
              opcode="Stream"
              symbol="QuicStreamErrorStatus"
              template="tid_STREAM_STATUS_ERROR"
              value="6151"
              />
          <event
              keywords="ut:Stream ut:Log"
              level="win:Error"
              message="$(string.Etw.StreamMessage)"
              opcode="Stream"
              symbol="QuicStreamLogError"
              template="tid_STREAM_MESSAGE"
              value="6152"
              />
          <event
              keywords="ut:Stream ut:Log"
              level="win:Warning"
              message="$(string.Etw.StreamMessage)"
              opcode="Stream"
              symbol="QuicStreamLogWarning"
              template="tid_STREAM_MESSAGE"
              value="6153"
              />
          <event
              keywords="ut:Stream ut:Log"
              level="win:Informational"
              message="$(string.Etw.StreamMessage)"
              opcode="Stream"
              symbol="QuicStreamLogInfo"
              template="tid_STREAM_MESSAGE"
              value="6154"
              />
          <event
              keywords="ut:Stream ut:Log"
              level="win:Verbose"
              message="$(string.Etw.StreamMessage)"
              opcode="Stream"
              symbol="QuicStreamLogVerbose"
              template="tid_STREAM_MESSAGE"
              value="6155"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamAlloc)"
              opcode="Stream"
              symbol="QuicStreamAlloc"
              template="tid_STREAM_CONN"
              value="6156"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamWriteFrames)"
              opcode="Stream"
              symbol="QuicStreamWriteFrames"
              template="tid_STREAM_U64"
              value="6157"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamReceiveFrame)"
              opcode="Stream"
              symbol="QuicStreamReceiveFrame"
              template="tid_STREAM_U64"
              value="6158"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamAppReceive)"
              opcode="Stream"
              symbol="QuicStreamAppReceive"
              template="tid_STREAM_RECV"
              value="6159"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamAppReceiveComplete)"
              opcode="Stream"
              symbol="QuicStreamAppReceiveComplete"
              template="tid_STREAM_U64"
              value="6160"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamAppSend)"
              opcode="Stream"
              symbol="QuicStreamAppSend"
              template="tid_STREAM_RECV"
              value="6161"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamReceiveFrameComplete)"
              opcode="Stream"
              symbol="QuicStreamReceiveFrameComplete"
              template="tid_STREAM"
              value="6162"
              />
          <event
              keywords="ut:Stream ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.StreamAppReceiveCompleteCall)"
              opcode="Stream"
              symbol="QuicStreamAppReceiveCompleteCall"
              template="tid_STREAM_U64"
              value="6163"
              />
          <!-- 7168 - 8191 | Binding Events -->
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.BindingCreated)"
              opcode="Binding"
              symbol="QuicBindingCreated"
              template="tid_BINDING_CREATED"
              value="7168"
              />
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.BindingRundown)"
              opcode="Binding"
              symbol="QuicBindingRundown"
              template="tid_BINDING_CREATED"
              value="7169"
              />
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.BindingDestroyed)"
              opcode="Binding"
              symbol="QuicBindingDestroyed"
              template="tid_BINDING"
              value="7170"
              />
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Verbose"
              message="$(string.Etw.BindingCleanup)"
              opcode="Binding"
              symbol="QuicBindingCleanup"
              template="tid_BINDING"
              value="7171"
              />
          <event
              keywords="ut:Binding ut:Packet ut:RPS ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.BindingDropPacket)"
              opcode="Binding"
              symbol="QuicBindingDropPacket"
              template="tid_PACKET_DROP"
              value="7172"
              />
          <event
              keywords="ut:Binding ut:Packet ut:RPS ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.BindingDropPacketEx)"
              opcode="Binding"
              symbol="QuicBindingDropPacketEx"
              template="tid_PACKET_DROP_EX"
              value="7173"
              />
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.BindingError)"
              opcode="Binding"
              symbol="QuicBindingError"
              template="tid_BINDING_ERROR"
              value="7174"
              />
          <event
              keywords="ut:Binding ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.BindingErrorStatus)"
              opcode="Binding"
              symbol="QuicBindingErrorStatus"
              template="tid_BINDING_STATUS_ERROR"
              value="7175"
              />
          <event
              keywords="ut:Binding ut:Scheduling"
              level="win:Verbose"
              message="$(string.Etw.BindingExecOper)"
              opcode="Binding"
              symbol="QuicBindingExecOper"
              template="tid_BINDING_OPER"
              value="7176"
              />
          <!-- 8192 - 9215 | Tls Events -->
          <event
              keywords="ut:TLS ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.TlsError)"
              opcode="Connection"
              symbol="QuicTlsError"
              template="tid_CONN_ERROR"
              value="8192"
              />
          <event
              keywords="ut:TLS ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.TlsErrorStatus)"
              opcode="Connection"
              symbol="QuicTlsErrorStatus"
              template="tid_CONN_STATUS_ERROR"
              value="8193"
              />
          <event
              keywords="ut:TLS"
              level="win:Verbose"
              message="$(string.Etw.TlsMessage)"
              opcode="Connection"
              symbol="QuicTlsMessage"
              template="tid_CONN_MESSAGE"
              value="8194"
              />
          <!-- 9216 - 10239 | Datapath Events -->
          <event
              keywords="ut:UDP ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.DatapathSend)"
              opcode="Datapath"
              symbol="QuicDatapathSend"
              template="tid_DATA_SEND"
              value="9217"
              />
          <event
              keywords="ut:UDP ut:DataFlow"
              level="win:Verbose"
              message="$(string.Etw.DatapathRecv)"
              opcode="Datapath"
              symbol="QuicDatapathRecv"
              template="tid_DATA_SEND_RECV"
              value="9218"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.DatapathError)"
              opcode="Datapath"
              symbol="QuicDatapathError"
              template="tid_UDP_ERROR"
              value="9219"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Error"
              message="$(string.Etw.DatapathErrorStatus)"
              opcode="Datapath"
              symbol="QuicDatapathErrorStatus"
              template="tid_UDP_STATUS_ERROR"
              value="9220"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DatapathCreated)"
              opcode="Datapath"
              symbol="QuicDatapathCreated"
              template="tid_UDP_CREATE"
              value="9221"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DatapathDestroyed)"
              opcode="Datapath"
              symbol="QuicDatapathDestroyed"
              template="tid_UDP"
              value="9222"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DatapathGetRouteStart)"
              opcode="Datapath"
              symbol="QuicDatapathGetRouteStart"
              template="tid_UDP_ADDRESSES"
              value="9223"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DatapathGetRouteComplete)"
              opcode="Datapath"
              symbol="QuicDatapathGetRouteComplete"
              template="tid_UDP_ADDRESS"
              value="9224"
              />
          <event
              keywords="ut:UDP ut:LowVolume"
              level="win:Informational"
              message="$(string.Etw.DatapathSendTcpControl)"
              opcode="Datapath"
              symbol="QuicDatapathSendTcpControl"
              template="tid_DATA_SEND_TCP_CONTROL"
              value="9225"
              />
          <!-- 10240 - 11263 | Logs (not events) -->
          <event
              keywords="ut:Log"
              level="win:Error"
              message="$(string.Etw.Message)"
              opcode="Global"
              symbol="QuicLogError"
              template="tid_MESSAGE"
              value="10240"
              />
          <event
              keywords="ut:Log"
              level="win:Warning"
              message="$(string.Etw.Message)"
              opcode="Global"
              symbol="QuicLogWarning"
              template="tid_MESSAGE"
              value="10241"
              />
          <event
              keywords="ut:Log"
              level="win:Informational"
              message="$(string.Etw.Message)"
              opcode="Global"
              symbol="QuicLogInfo"
              template="tid_MESSAGE"
              value="10242"
              />
          <event
              keywords="ut:Log"
              level="win:Verbose"
              message="$(string.Etw.Message)"
              opcode="Global"
              symbol="QuicLogVerbose"
              template="tid_MESSAGE"
              value="10243"
              />
          <!-- 11264 - 12287 | Packet Events -->
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketCreated)"
              opcode="Global"
              symbol="QuicPacketCreated"
              template="tid_ID_ID"
              value="11264"
              />
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketEncrypt)"
              opcode="Global"
              symbol="QuicPacketEncrypt"
              template="tid_ID"
              value="11265"
              />
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketFinalize)"
              opcode="Global"
              symbol="QuicPacketFinalize"
              template="tid_ID"
              value="11266"
              />
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketBatchSent)"
              opcode="Global"
              symbol="QuicPacketBatchSent"
              template="tid_ID"
              value="11267"
              />
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketReceive)"
              opcode="Global"
              symbol="QuicPacketReceive"
              template="tid_ID"
              value="11268"
              />
          <event
              keywords="ut:RPS"
              level="win:Verbose"
              message="$(string.Etw.PacketDecrypt)"
              opcode="Global"
              symbol="QuicPacketDecrypt"
              template="tid_ID"
              value="11269"
              />
        </events>
      </provider>
    </events>
  </instrumentation>
  <localization>
    <resources culture="en-US">
      <stringTable>
        <!-- Event and Log strings -->
        <string
            id="Etw.Message"
            value="%1"
            />
        <string
            id="Etw.ConnMessage"
            value="[conn][%1] %2"
            />
        <string
            id="Etw.ConnCreated"
            value="[conn][%1] Created, IsServer=%2, CorrelationId=%3"
            />
        <string
            id="Etw.ConnRundown"
            value="[conn][%1] Rundown, IsServer=%2, CorrelationId=%3"
            />
        <string
            id="Etw.ConnDestroyed"
            value="[conn][%1] Destroyed"
            />
        <string
            id="Etw.ConnHandshakeStart"
            value="[conn][%1] Handshake start"
            />
        <string
            id="Etw.ConnHandshakeComplete"
            value="[conn][%1] Handshake complete"
            />
        <string
            id="Etw.ConnScheduleState"
            value="[conn][%1] Scheduling: %2"
            />
        <string
            id="Etw.ConnExecOper"
            value="[conn][%1] Execute: %2"
            />
        <string
            id="Etw.ConnLocalAddrAdd"
            value="[conn][%1] New Local IP: %3"
            />
        <string
            id="Etw.ConnRemoteAddrAdd"
            value="[conn][%1] New Remote IP: %3"
            />
        <string
            id="Etw.ConnLocalAddrRemove"
            value="[conn][%1] Removed Local IP: %3"
            />
        <string
            id="Etw.ConnRemoteAddrRemove"
            value="[conn][%1] Removed Remote IP: %3"
            />
        <string
            id="Etw.ConnSourceCidAdd"
            value="[conn][%1] (SeqNum=%2) New Source CID: %4"
            />
        <string
            id="Etw.ConnDestCidAdd"
            value="[conn][%1] (SeqNum=%2) New Destination CID: %4"
            />
        <string
            id="Etw.ConnSourceCidRemove"
            value="[conn][%1] (SeqNum=%2) Removed Source CID: %4"
            />
        <string
            id="Etw.ConnDestCidRemove"
            value="[conn][%1] (SeqNum=%2) Removed Destination CID: %4"
            />
        <string
            id="Etw.ConnAssignWorker"
            value="[conn][%1] Assigned worker: %2"
            />
        <string
            id="Etw.ConnRegistered"
            value="[conn][%1] Registered with %2"
            />
        <string
            id="Etw.ConnUnregistered"
            value="[conn][%1] Unregistered from %2"
            />
        <string
            id="Etw.ConnTransportShutdown"
            value="[conn][%1] Transport Shutdown: %2 (Remote=%3) (QS=%4)"
            />
        <string
            id="Etw.ConnAppShutdown"
            value="[conn][%1] App Shutdown: %2 (Remote=%3)"
            />
        <string
            id="Etw.ConnInitializeComplete"
            value="[conn][%1] Initialize complete"
            />
        <string
            id="Etw.ConnHandleClosed"
            value="[conn][%1] Handle closed"
            />
        <string
            id="Etw.ConnVersionSet"
            value="[conn][%1] QUIC Version: %2"
            />
        <string
            id="Etw.ConnOutFlowStats"
            value="[conn][%1] OUT: BytesSent=%2 InFlight=%3 InFlightMax=%4 CWnd=%5 SSThresh=%6 ConnFC=%7 ISB=%8 PostedBytes=%9 SRtt=%10"
            />
        <string
            id="Etw.ConnOutFlowStatsV2"
            value="[conn][%1] OUT: BytesSent=%2 InFlight=%3 CWnd=%4 ConnFC=%5 ISB=%6 PostedBytes=%7 SRtt=%8 1Way=%9"
            />
        <string
            id="Etw.ConnOutFlowStreamStats"
            value="[conn][%1] OUT: StreamFC=%2 StreamSendWindow=%3"
            />
        <string
            id="Etw.ConnInFlowStats"
            value="[conn][%1] IN: BytesRecv=%2"
            />
        <string
            id="Etw.ConnOutFlowBlocked"
            value="[conn][%1] Send Blocked Flags: %2"
            />
        <string
            id="Etw.Cubic"
            value="[conn][%1] CUBIC: SlowStartThreshold=%2 K=%3 WindowMax=%4 WindowLastMax=%5"
            />
        <string
            id="Etw.CubicHyStart"
            value="[conn][%1] HyStart: State=%2 CongestionWindow=%3 SlowStartThreshold=%4"
            />
        <string
            id="Etw.CongestionEvent"
            value="[conn][%1] Congestion event"
            />
        <string
            id="Etw.PersistentCongestionEvent"
            value="[conn][%1] Persistent congestion event"
            />
        <string
            id="Etw.SpuriousCongestionEvent"
            value="[conn][%1] Spurious congestion event"
            />
        <string
            id="Etw.RecoveryExit"
            value="[conn][%1] Recovery complete"
            />
        <string
            id="Etw.ConnLossDetectionTimerSet"
            value="[conn][%1] Setting loss detection %2 timer for %3 us. (ProbeCount=%4)"
            />
        <string
            id="Etw.ConnLossDetectionTimerCancel"
            value="[conn][%1] Cancelling loss detection timer."
            />
        <string
            id="Etw.ConnDropPacket"
            value="[conn][%1] DROP packet Dst=%3 Src=%5 Reason=%6."
            />
        <string
            id="Etw.ConnDropPacketEx"
            value="[conn][%1] DROP packet Dst=%4 Src=%6 Reason=%7, %2."
            />
        <string
            id="Etw.ConnError"
            value="[conn][%1] ERROR, %2."
            />
        <string
            id="Etw.ConnErrorStatus"
            value="[conn][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.ConnNewPacketKeys"
            value="[conn][%1] New packet keys created successfully."
            />
        <string
            id="Etw.ConnKeyPhaseChange"
            value="[conn][%1] Key phase change (locally initiated=%2)."
            />
        <string
            id="Etw.ConnStats"
            value="[conn][%1] STATS: SRtt=%2 CongestionCount=%3 PersistentCongestionCount=%4 SendTotalBytes=%5 RecvTotalBytes=%6 CongestionWindow=%7 Cc=%8"
            />
        <string
            id="Etw.ConnPacketStats"
            value="[conn][%1] STATS: SendTotalPackets=%2 SendSuspectedLostPackets=%3 SendSpuriousLostPackets=%4 RecvTotalPackets=%5 RecvReorderedPackets=%6 RecvDroppedPackets=%7 RecvDuplicatePackets=%8 RecvDecryptionFailures=%9"
            />
        <string
            id="Etw.ConnShutdownComplete"
            value="[conn][%1] Shutdown complete, PeerFailedToAcknowledged=%2."
            />
        <string
            id="Etw.ConnReadKeyUpdated"
            value="[conn][%1] Read Key Updated, %2."
            />
        <string
            id="Etw.ConnWriteKeyUpdated"
            value="[conn][%1] Write Key Updated, %2."
            />
        <string
            id="Etw.ConnPacketSent"
            value="[conn][%1][TX][%2] %3 (%4 bytes)"
            />
        <string
            id="Etw.ConnPacketRecv"
            value="[conn][%1][RX][%2] %3 (%4 bytes)"
            />
        <string
            id="Etw.ConnPacketLost"
            value="[conn][%1][TX][%2] %3 Lost: %4"
            />
        <string
            id="Etw.ConnServerResumeTicket"
            value="[conn][%1] Server app accepted resumption ticket"
            />
        <string
            id="Etw.ConnPacketAcked"
            value="[conn][%1][TX][%2] %3 ACKed"
            />
        <string
            id="Etw.ConnQueueSendFlush"
            value="[conn][%1] Queueing send flush, reason=%2"
            />
        <string
            id="Etw.ConnVNEOtherVersionList"
            value="[conn][%1] VerInfo Available Versions List: %3"
            />
        <string
            id="Etw.ConnClientReceivedVersionList"
            value="[conn][%1] Client VI Received Version List: %3"
            />
        <string
            id="Etw.ConnServerSupportedVersionList"
            value="[conn][%1] Server VI Supported Version List: %3"
            />
        <string
            id="Etw.ConnRecvUdpDatagrams"
            value="[conn][%1] Recv %2 UDP datagrams, %3 bytes"
            />
        <string
            id="Etw.StreamCreated"
            value="[strm][%1] Created, Conn=%2 ID=%3 IsLocal=%4"
            />
        <string
            id="Etw.StreamRundown"
            value="[strm][%1] Rundown, Conn=%2 ID=%3 IsLocal=%4"
            />
        <string
            id="Etw.StreamDestroyed"
            value="[strm][%1] Destroyed"
            />
        <string
            id="Etw.StreamOutFlowBlocked"
            value="[strm][%1] Send Blocked Flags: %2"
            />
        <string
            id="Etw.StreamSendState"
            value="[strm][%1] Send State: %2"
            />
        <string
            id="Etw.StreamRecvState"
            value="[strm][%1] Recv State: %2"
            />
        <string
            id="Etw.StreamError"
            value="[strm][%1] ERROR, %2."
            />
        <string
            id="Etw.StreamErrorStatus"
            value="[strm][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.StreamMessage"
            value="[strm][%1] %2"
            />
        <string
            id="Etw.StreamAlloc"
            value="[strm][%1] Allocated, Conn=%2"
            />
        <string
            id="Etw.StreamWriteFrames"
            value="[strm][%1] Writing frames to packet %2"
            />
        <string
            id="Etw.StreamReceiveFrame"
            value="[strm][%1] Processing frame in packet %2"
            />
        <string
            id="Etw.StreamReceiveFrameComplete"
            value="[strm][%1] Done processing frame"
            />
        <string
            id="Etw.StreamAppReceive"
            value="[strm][%1] Indicating QUIC_STREAM_EVENT_RECEIVE [%2 bytes, %3 buffers, %4 flags]"
            />
        <string
            id="Etw.StreamAppReceiveComplete"
            value="[strm][%1] Receive complete [%2 bytes]"
            />
        <string
            id="Etw.StreamAppReceiveCompleteCall"
            value="[strm][%1] Receive complete call [%2 bytes]"
            />
        <string
            id="Etw.StreamAppSend"
            value="[strm][%1] App queuing send [%2 bytes, %3 buffers, %4 flags]"
            />
        <string
            id="Etw.WorkerCreated"
            value="[wrkr][%1] Created, IdealProc=%2 Owner=%3"
            />
        <string
            id="Etw.WorkerStart"
            value="[wrkr][%1] Start"
            />
        <string
            id="Etw.WorkerStop"
            value="[wrkr][%1] Stop"
            />
        <string
            id="Etw.WorkerActivityStateUpdated"
            value="[wrkr][%1] IsActive = %2, Arg = %3"
            />
        <string
            id="Etw.WorkerQueueDelayUpdated"
            value="[wrkr][%1] QueueDelay = %2"
            />
        <string
            id="Etw.WorkerDestroyed"
            value="[wrkr][%1] Destroyed"
            />
        <string
            id="Etw.WorkerCleanup"
            value="[wrkr][%1] Cleaning up"
            />
        <string
            id="Etw.WorkerError"
            value="[wrkr][%1] ERROR, %2."
            />
        <string
            id="Etw.WorkerErrorStatus"
            value="[wrkr][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.RegistrationCreated"
            value="[ reg][%1] Created, AppName=%2"
            />
        <string
            id="Etw.RegistrationCreatedV2"
            value="[ reg][%1] Created, AppName=%2, ExecProfile=%3"
            />
        <string
            id="Etw.RegistrationRundown"
            value="[ reg][%1] Rundown, AppName=%2"
            />
        <string
            id="Etw.RegistrationRundownV2"
            value="[ reg][%1] Rundown, AppName=%2, ExecProfile=%3"
            />
        <string
            id="Etw.RegistrationDestroyed"
            value="[ reg][%1] Destroyed"
            />
        <string
            id="Etw.RegistrationCleanup"
            value="[ reg][%1] Cleaning up"
            />
        <string
            id="Etw.RegistrationError"
            value="[ reg][%1] ERROR, %2."
            />
        <string
            id="Etw.RegistrationErrorStatus"
            value="[ reg][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.RegistrationShutdown"
            value="[ reg][%1] Shutting down connections, Flags=%2, ErrorCode=%3"
            />
        <string
            id="Etw.DataPathInitialized"
            value="[data] Initialized, DatapathFeatures=%1"
            />
        <string
            id="Etw.DataPathRundown"
            value="[data] Rundown, DatapathFeatures=%1"
            />
        <string
            id="Etw.LibraryInitialized"
            value="[ lib] Initialized, PartitionCount=%1 DatapathFeatures=%2"
            />
        <string
            id="Etw.LibraryInitializedV2"
            value="[ lib] Initialized, PartitionCount=%1"
            />
        <string
            id="Etw.LibraryInitializedV3"
            value="[ lib] Initialized"
            />
        <string
            id="Etw.LibraryRundown"
            value="[ lib] Rundown, PartitionCount=%1 DatapathFeatures=%2"
            />
        <string
            id="Etw.LibraryRundownV2"
            value="[ lib] Rundown, PartitionCount=%1"
            />
        <string
            id="Etw.LibraryUninitialized"
            value="[ lib] Uninitialized"
            />
        <string
            id="Etw.LibraryAddRef"
            value="[ lib] AddRef"
            />
        <string
            id="Etw.LibraryRelease"
            value="[ lib] Release"
            />
        <string
            id="Etw.LibraryServerInit"
            value="[ lib] Shared server state initializing"
            />
        <string
            id="Etw.LibraryError"
            value="[ lib] ERROR, %1."
            />
        <string
            id="Etw.LibraryErrorStatus"
            value="[ lib] ERROR, %1, %2."
            />
        <string
            id="Etw.LibraryAssert"
            value="[ lib] ASSERT, %2:%1 - %3."
            />
        <string
            id="Etw.ConfigurationCreated"
            value="[cnfg][%1] Created, Registration=%2"
            />
        <string
            id="Etw.ConfigurationRundown"
            value="[cnfg][%1] Rundown, Registration=%2"
            />
        <string
            id="Etw.ConfigurationDestroyed"
            value="[cnfg][%1] Destroyed"
            />
        <string
            id="Etw.ConfigurationCleanup"
            value="[cnfg][%1] Cleaning up"
            />
        <string
            id="Etw.ConfigurationError"
            value="[cnfg][%1] ERROR, %2."
            />
        <string
            id="Etw.ConfigurationErrorStatus"
            value="[cnfg][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.ListenerCreated"
            value="[list][%1] Created, Registration=%2"
            />
        <string
            id="Etw.ListenerRundown"
            value="[list][%1] Rundown, Registration=%2"
            />
        <string
            id="Etw.ListenerDestroyed"
            value="[list][%1] Destroyed"
            />
        <string
            id="Etw.ListenerStarted"
            value="[list][%1] Started, Binding=%2, LocalAddr=%4, ALPN=%6"
            />
        <string
            id="Etw.ListenerStopped"
            value="[list][%1] Stopped"
            />
        <string
            id="Etw.ListenerError"
            value="[list][%1] ERROR, %2."
            />
        <string
            id="Etw.ListenerErrorStatus"
            value="[list][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.BindingCreated"
            value="[bind][%1] Created, Udp=%2 LocalAddr=%4 RemoteAddr=%6"
            />
        <string
            id="Etw.BindingRundown"
            value="[bind][%1] Rundown, Udp=%2 LocalAddr=%4 RemoteAddr=%6"
            />
        <string
            id="Etw.BindingDestroyed"
            value="[bind][%1] Destroyed"
            />
        <string
            id="Etw.BindingCleanup"
            value="[bind][%1] Cleaning up"
            />
        <string
            id="Etw.BindingDropPacket"
            value="[bind][%1] DROP packet Dst=%3 Src=%5 Reason=%6."
            />
        <string
            id="Etw.BindingDropPacketEx"
            value="[bind][%1] DROP packet Dst=%4 Src=%6 Reason=%7, %2."
            />
        <string
            id="Etw.BindingError"
            value="[bind][%1] ERROR, %2."
            />
        <string
            id="Etw.BindingErrorStatus"
            value="[bind][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.BindingExecOper"
            value="[bind][%1] Execute: %2"
            />
        <string
            id="Etw.AllocFailure"
            value="Allocation of &apos;%1&apos; failed. (%2 bytes)"
            />
        <string
            id="Etw.TlsError"
            value="[ tls][%1] ERROR, %2."
            />
        <string
            id="Etw.TlsErrorStatus"
            value="[ tls][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.TlsMessage"
            value="[ tls][%1] %2"
            />
        <string
            id="Etw.DatapathSend"
            value="[data][%1] Send %2 bytes in %3 buffers (segment=%4) Dst=%6 Src=%8"
            />
        <string
            id="Etw.DatapathRecv"
            value="[data][%1] Recv %2 bytes (segment=%3) Src=%5 Dst=%7"
            />
        <string
            id="Etw.DatapathError"
            value="[data][%1] ERROR, %2."
            />
        <string
            id="Etw.DatapathErrorStatus"
            value="[data][%1] ERROR, %2, %3."
            />
        <string
            id="Etw.DatapathCreated"
            value="[data][%1] Created, local=%3, remote=%5"
            />
        <string
            id="Etw.DatapathDestroyed"
            value="[data][%1] Destroyed"
            />
        <string
            id="Etw.DatapathGetRouteStart"
            value="[data][%1] Querying route, local=%3, remote=%5"
            />
        <string
            id="Etw.DatapathGetRouteComplete"
            value="[data][%1] Query route result: %3"
            />
        <string
            id="Etw.ApiEnter"
            value="[ api] Enter %1 (%2)."
            />
        <string
            id="Etw.ApiExit"
            value="[ api] Exit"
            />
        <string
            id="Etw.ApiExitStatus"
            value="[ api] Exit %1"
            />
        <string
            id="Etw.ApiWaitOperation"
            value="[ api] Waiting on operation"
            />
        <string
            id="Etw.ApiError"
            value="[ api] Error %1"
            />
        <string
            id="Etw.PerfCountersRundown"
            value="[ lib] Perf counters Rundown"
            />
        <string
            id="Etw.LibrarySendRetryStateUpdated"
            value="[ lib] New SendRetryEnabled state, %1"
            />
        <string
            id="Etw.LibraryVersion"
            value="[ lib] Version %1.%2.%3.%4"
            />
        <string
            id="Etw.PacketCreated"
            value="[pack][%1] Created in batch %2"
            />
        <string
            id="Etw.PacketEncrypt"
            value="[pack][%1] Encrypting"
            />
        <string
            id="Etw.PacketFinalize"
            value="[pack][%1] Finalizing"
            />
        <string
            id="Etw.PacketBatchSent"
            value="[pack][%1] Batch sent"
            />
        <string
            id="Etw.PacketReceive"
            value="[pack][%1] Received"
            />
        <string
            id="Etw.PacketDecrypt"
            value="[pack][%1] Decrypting"
            />
        <!-- Map/enum strings -->
        <string
            id="Enum.QUIC_SCHEDULE_STATE.IDLE"
            value="IDLE"
            />
        <string
            id="Enum.QUIC_SCHEDULE_STATE.QUEUED"
            value="QUEUED"
            />
        <string
            id="Enum.QUIC_SCHEDULE_STATE.PROCESSING"
            value="PROCESSING"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.API_CALL"
            value="API"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.FLUSH_RECV"
            value="FLUSH_RECV"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.UNREACHABLE"
            value="UNREACHABLE"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.FLUSH_STREAM_RECV"
            value="FLUSH_STREAM_RECV"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.FLUSH_SEND"
            value="FLUSH_SEND"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.TLS_COMPLETE"
            value="TLS_COMPLETE"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.TIMER_EXPIRED"
            value="TIMER_EXPIRED"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.TRACE_RUNDOWN"
            value="TRACE_RUNDOWN"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.VERSION_NEGOTIATION"
            value="VERSION_NEGOTIATION"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.STATELESS_RESET"
            value="STATELESS_RESET"
            />
        <string
            id="Enum.QUIC_OPERATION_TYPE.RETRY"
            value="RETRY"
            />
        <string
            id="Enum.QUIC_API_TYPE.CONN_CLOSE"
            value="API.CONN_CLOSE"
            />
        <string
            id="Enum.QUIC_API_TYPE.CONN_SHUTDOWN"
            value="API.CONN_SHUTDOWN"
            />
        <string
            id="Enum.QUIC_API_TYPE.CONN_START"
            value="API.CONN_START"
            />
        <string
            id="Enum.QUIC_API_TYPE.SET_CONFIGURATION"
            value="API.SET_CONFIGURATION"
            />
        <string
            id="Enum.QUIC_API_TYPE.SEND_RESUMPTION_TICKET"
            value="API.SEND_RESUMPTION_TICKET"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_CLOSE"
            value="API.STRM_CLOSE"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_SHUTDOWN"
            value="API.STRM_SHUTDOWN"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_START"
            value="API.STRM_START"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_SEND"
            value="API.STRM_SEND"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_RECV_COMPLETE"
            value="API.STRM_RECV_COMPLETE"
            />
        <string
            id="Enum.QUIC_API_TYPE.STRM_RECV_SET_ENABLED"
            value="API.STRM_RECV_SET_ENABLED"
            />
        <string
            id="Enum.QUIC_API_TYPE.SET_PARAM"
            value="API.SET_PARAM"
            />
        <string
            id="Enum.QUIC_API_TYPE.GET_PARAM"
            value="API.GET_PARAM"
            />
        <string
            id="Enum.QUIC_API_TYPE.DATAGRAM_SEND"
            value="API.DATAGRAM_SEND"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.IDLE"
            value="TIMER.IDLE"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.LOSS_DETECTION"
            value="TIMER.LOSS_DETECTION"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.ACK_DELAY"
            value="TIMER.ACK_DELAY"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.PACING"
            value="TIMER.PACING"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.KEEP_ALIVE"
            value="TIMER.KEEP_ALIVE"
            />
        <string
            id="Enum.QUIC_CONN_TIMER_TYPE.SHUTDOWN"
            value="TIMER.SHUTDOWN"
            />
        <string
            id="Enum.QUIC_LOSS_TIMER_TYPE.INITIAL"
            value="INITIAL"
            />
        <string
            id="Enum.QUIC_LOSS_TIMER_TYPE.RACK"
            value="RACK"
            />
        <string
            id="Enum.QUIC_LOSS_TIMER_TYPE.PROBE"
            value="PROBE"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.DISABLED"
            value="DISABLED"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.STARTED"
            value="STARTED"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.RESET"
            value="RESET"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.RESET_ACKED"
            value="RESET_ACKED"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.FIN"
            value="FIN"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.FIN_ACKED"
            value="FIN_ACKED"
            />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.RELIABLE_RESET"
            value="RELIABLE_RESET"
        />
        <string
            id="Enum.QUIC_STREAM_SEND_STATE.RELIABLE_RESET_ACKED"
            value="RELIABLE_RESET_ACKED"
        />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.DISABLED"
            value="DISABLED"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.STARTED"
            value="STARTED"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.PAUSED"
            value="PAUSED"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.STOPPED"
            value="STOPPED"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.RESET"
            value="RESET"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.RELIABLE_RESET"
            value="RELIABLE_RESET"
            />
        <string
            id="Enum.QUIC_STREAM_RECV_STATE.FIN"
            value="FIN"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.VERSION_NEGOTIATION"
            value="VERSION_NEGOTIATION"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.RETRY"
            value="RETRY"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.INITIAL"
            value="INITIAL"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.HANDSHAKE"
            value="HANDSHAKE"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.ZERO_RTT"
            value="ZERO_RTT"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_TYPE.ONE_RTT"
            value="ONE_RTT"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_LOSS_REASON.RACK"
            value="RACK"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_LOSS_REASON.FACK"
            value="FACK"
            />
        <string
            id="Enum.QUIC_TRACE_PACKET_LOSS_REASON.PROBE"
            value="PROBE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.SET_PARAM"
            value="SET_PARAM"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.GET_PARAM"
            value="GET_PARAM"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.REGISTRATION_OPEN"
            value="REGISTRATION_OPEN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.REGISTRATION_CLOSE"
            value="REGISTRATION_CLOSE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.REGISTRATION_SHUTDOWN"
            value="REGISTRATION_SHUTDOWN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_OPEN"
            value="CONFIGURATION_OPEN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_CLOSE"
            value="CONFIGURATION_CLOSE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONFIGURATION_LOAD_CREDENTIAL"
            value="CONFIGURATION_LOAD_CREDENTIAL"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.LISTENER_OPEN"
            value="LISTENER_OPEN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.LISTENER_CLOSE"
            value="LISTENER_CLOSE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.LISTENER_START"
            value="LISTENER_START"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.LISTENER_STOP"
            value="LISTENER_STOP"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_OPEN"
            value="CONNECTION_OPEN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_CLOSE"
            value="CONNECTION_CLOSE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_SHUTDOWN"
            value="CONNECTION_SHUTDOWN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_START"
            value="CONNECTION_START"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_SET_CONFIGURATION"
            value="CONNECTION_SET_CONFIGURATION"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.CONNECTION_SEND_RESUMPTION_TICKET"
            value="CONNECTION_SEND_RESUMPTION_TICKET"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_OPEN"
            value="STREAM_OPEN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_CLOSE"
            value="STREAM_CLOSE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_START"
            value="STREAM_START"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_SHUTDOWN"
            value="STREAM_SHUTDOWN"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_SEND"
            value="STREAM_SEND"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_RECEIVE_COMPLETE"
            value="STREAM_RECEIVE_COMPLETE"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.STREAM_RECEIVE_SET_ENABLED"
            value="STREAM_RECEIVE_SET_ENABLED"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.DATAGRAM_SEND"
            value="DATAGRAM_SEND"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.COMPLETE_RESUMPTION_TICKET_VALIDATION"
            value="COMPLETE_RESUMPTION_TICKET_VALIDATION"
            />
        <string
            id="Enum.QUIC_TRACE_API_TYPE.COMPLETE_CERTIFICATE_VALIDATION"
            value="COMPLETE_CERTIFICATE_VALIDATION"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.CONNECTION_FLAGS"
            value="CONNECTION_FLAGS"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.STREAM_FLAGS"
            value="STREAM_FLAGS"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.PROBE"
            value="PROBE"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.LOSS"
            value="LOSS"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.ACK"
            value="ACK"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.TRANSPORT_PARAMETERS"
            value="TRANSPORT_PARAMETERS"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.CONGESTION_CONTROL"
            value="CONGESTION_CONTROL"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.CONNECTION_FLOW_CONTROL"
            value="CONNECTION_FLOW_CONTROL"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.NEW_KEY"
            value="NEW_KEY"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.STREAM_FLOW_CONTROL"
            value="STREAM_FLOW_CONTROL"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.STREAM_ID_FLOW_CONTROL"
            value="STREAM_ID_FLOW_CONTROL"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.AMPLIFICATION_PROTECTION"
            value="AMPLIFICATION_PROTECTION"
            />
        <string
            id="Enum.QUIC_SEND_FLUSH_REASON.SCHEDULING"
            value="SCHEDULING"
            />
        <string
            id="Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_LOW_LATENCY"
            value="LOW_LATENCY"
            />
        <string
            id="Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_MAX_THROUGHPUT"
            value="MAX_THROUGHPUT"
            />
        <string
            id="Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_SCAVENGER"
            value="SCAVENGER"
            />
        <string
            id="Enum.QUIC_EXECUTION_PROFILE.QUIC_EXECUTION_PROFILE_TYPE_REAL_TIME"
            value="REAL_TIME"
            />
        <string
            id="Enum.QUIC_CUBIC_HYSTART_STATE.NOT_STARTED"
            value="NotStarted"
            />
        <string
            id="Enum.QUIC_CUBIC_HYSTART_STATE.ACTIVE"
            value="Active"
            />
        <string
            id="Enum.QUIC_CUBIC_HYSTART_STATE.DONE"
            value="Done"
            />
        <string
            id="Etw.ConnNoListenerIp"
            value="[conn][%1] No Listener for IP address: %3"
            />
        <string
            id="Etw.ConnNoListenerAlpn"
            value="[conn][%1] No listener matching ALPN: %3"
            />
        <string
            id="Etw.ConnFlushSend"
            value="[conn][%1] Flushing Send. Allowance=%2 bytes"
            />
        <string
            id="Etw.ConnSetTimer"
            value="[conn][%1] Setting %2, delay=%3 us"
            />
        <string
            id="Etw.ConnCancelTimer"
            value="[conn][%1] Canceling %2"
            />
        <string
            id="Etw.ConnExpiredTimer"
            value="[conn][%1] %2 expired"
            />
        <string
            id="Etw.Bbr"
            value="[conn][%1] BBR: State=%2 RState=%3 CongestionWindow=%4 BytesInFlight=%5 BytesInFlightMax=%6 MinRttEst=%7 EstBw=%8 AppLimited=%9"
            />
        <string
            id="Etw.ConnEcnCapable"
            value="[conn][%1] Ecn: IsCapable=%2"
            />
        <string
            id="Etw.ConnEcnFailed"
            value="[conn][%1][%2] ECN failed: EctCnt %3 CeCnt %4 TxEct %5 DeltaSum %6 State %7"
            />
        <string
            id="Etw.CongestionEventV2"
            value="[conn][%1] Congestion event IsEcn=%2"
            />
        <string
            id="Etw.ConnStatsV2"
            value="[conn][%1] STATS: SRtt=%2 CongestionCount=%3 PersistentCongestionCount=%4 SendTotalBytes=%5 RecvTotalBytes=%6 CongestionWindow=%7 Cc=%8 EcnCongestionCount=%8"
            />
        <string
            id="Etw.DatapathSendTcpControl"
            value="[data][%1] Send %2 bytes TCP control packet Flags=%3 Dst=%5 Src=%7"
            />
        <string
            id="Etw.ConnDelayCloseApplicationError"
            value="[conn][%1] Received APPLICATION_ERROR error, delaying close in expectation of a 1-RTT CONNECTION_CLOSE frame."
            />
      </stringTable>
    </resources>
  </localization>
</instrumentationManifest>
