@echo off
setlocal

echo Building P2PSocket library with integrated MsQuic...

REM Create build directory
if not exist build_p2psocket mkdir build_p2psocket
cd build_p2psocket

REM Configure CMake
REM Build MsQuic as a static library to integrate into p2psocket.dll
cmake .. -DQUIC_BUILD_SHARED=OFF -DQUIC_ENABLE_LOGGING=ON -DQUIC_LOGGING_TYPE=etw -DQUIC_TLS=openssl

REM Build
cmake --build . --config Release

echo.
echo Build completed.
echo.
echo The integrated P2PSocket library can be found in:
echo %CD%\bin\Release

REM Return to original directory
cd ..

endlocal
