# Generated with generate_ssl_tests.pl

num_tests = 9

test-0 = 0-SNI-switch-context
test-1 = 1-SNI-keep-context
test-2 = 2-SNI-no-server-support
test-3 = 3-SNI-no-client-support
test-4 = 4-SNI-bad-sni-ignore-mismatch
test-5 = 5-SNI-bad-sni-reject-mismatch
test-6 = 6-SNI-bad-clienthello-sni-ignore-mismatch
test-7 = 7-SNI-bad-clienthello-sni-reject-mismatch
test-8 = 8-SNI-clienthello-disable-v12
# ===========================================================

[0-SNI-switch-context]
ssl_conf = 0-SNI-switch-context-ssl

[0-SNI-switch-context-ssl]
server = 0-SNI-switch-context-server
client = 0-SNI-switch-context-client
server2 = 0-SNI-switch-context-server

[0-SNI-switch-context-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-SNI-switch-context-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
ExpectedServerName = server2
server = 0-SNI-switch-context-server-extra
server2 = 0-SNI-switch-context-server-extra
client = 0-SNI-switch-context-client-extra

[0-SNI-switch-context-server-extra]
ServerNameCallback = IgnoreMismatch

[0-SNI-switch-context-client-extra]
ServerName = server2


# ===========================================================

[1-SNI-keep-context]
ssl_conf = 1-SNI-keep-context-ssl

[1-SNI-keep-context-ssl]
server = 1-SNI-keep-context-server
client = 1-SNI-keep-context-client
server2 = 1-SNI-keep-context-server

[1-SNI-keep-context-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-SNI-keep-context-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success
ExpectedServerName = server1
server = 1-SNI-keep-context-server-extra
server2 = 1-SNI-keep-context-server-extra
client = 1-SNI-keep-context-client-extra

[1-SNI-keep-context-server-extra]
ServerNameCallback = IgnoreMismatch

[1-SNI-keep-context-client-extra]
ServerName = server1


# ===========================================================

[2-SNI-no-server-support]
ssl_conf = 2-SNI-no-server-support-ssl

[2-SNI-no-server-support-ssl]
server = 2-SNI-no-server-support-server
client = 2-SNI-no-server-support-client

[2-SNI-no-server-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-SNI-no-server-support-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
client = 2-SNI-no-server-support-client-extra

[2-SNI-no-server-support-client-extra]
ServerName = server1


# ===========================================================

[3-SNI-no-client-support]
ssl_conf = 3-SNI-no-client-support-ssl

[3-SNI-no-client-support-ssl]
server = 3-SNI-no-client-support-server
client = 3-SNI-no-client-support-client
server2 = 3-SNI-no-client-support-server

[3-SNI-no-client-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-SNI-no-client-support-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
ExpectedServerName = server1
server = 3-SNI-no-client-support-server-extra
server2 = 3-SNI-no-client-support-server-extra

[3-SNI-no-client-support-server-extra]
ServerNameCallback = IgnoreMismatch


# ===========================================================

[4-SNI-bad-sni-ignore-mismatch]
ssl_conf = 4-SNI-bad-sni-ignore-mismatch-ssl

[4-SNI-bad-sni-ignore-mismatch-ssl]
server = 4-SNI-bad-sni-ignore-mismatch-server
client = 4-SNI-bad-sni-ignore-mismatch-client
server2 = 4-SNI-bad-sni-ignore-mismatch-server

[4-SNI-bad-sni-ignore-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-SNI-bad-sni-ignore-mismatch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success
ExpectedServerName = server1
server = 4-SNI-bad-sni-ignore-mismatch-server-extra
server2 = 4-SNI-bad-sni-ignore-mismatch-server-extra
client = 4-SNI-bad-sni-ignore-mismatch-client-extra

[4-SNI-bad-sni-ignore-mismatch-server-extra]
ServerNameCallback = IgnoreMismatch

[4-SNI-bad-sni-ignore-mismatch-client-extra]
ServerName = invalid


# ===========================================================

[5-SNI-bad-sni-reject-mismatch]
ssl_conf = 5-SNI-bad-sni-reject-mismatch-ssl

[5-SNI-bad-sni-reject-mismatch-ssl]
server = 5-SNI-bad-sni-reject-mismatch-server
client = 5-SNI-bad-sni-reject-mismatch-client
server2 = 5-SNI-bad-sni-reject-mismatch-server

[5-SNI-bad-sni-reject-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-SNI-bad-sni-reject-mismatch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = ServerFail
ExpectedServerAlert = UnrecognizedName
server = 5-SNI-bad-sni-reject-mismatch-server-extra
server2 = 5-SNI-bad-sni-reject-mismatch-server-extra
client = 5-SNI-bad-sni-reject-mismatch-client-extra

[5-SNI-bad-sni-reject-mismatch-server-extra]
ServerNameCallback = RejectMismatch

[5-SNI-bad-sni-reject-mismatch-client-extra]
ServerName = invalid


# ===========================================================

[6-SNI-bad-clienthello-sni-ignore-mismatch]
ssl_conf = 6-SNI-bad-clienthello-sni-ignore-mismatch-ssl

[6-SNI-bad-clienthello-sni-ignore-mismatch-ssl]
server = 6-SNI-bad-clienthello-sni-ignore-mismatch-server
client = 6-SNI-bad-clienthello-sni-ignore-mismatch-client
server2 = 6-SNI-bad-clienthello-sni-ignore-mismatch-server

[6-SNI-bad-clienthello-sni-ignore-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-SNI-bad-clienthello-sni-ignore-mismatch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = Success
ExpectedServerName = server1
server = 6-SNI-bad-clienthello-sni-ignore-mismatch-server-extra
server2 = 6-SNI-bad-clienthello-sni-ignore-mismatch-server-extra
client = 6-SNI-bad-clienthello-sni-ignore-mismatch-client-extra

[6-SNI-bad-clienthello-sni-ignore-mismatch-server-extra]
ServerNameCallback = ClientHelloIgnoreMismatch

[6-SNI-bad-clienthello-sni-ignore-mismatch-client-extra]
ServerName = invalid


# ===========================================================

[7-SNI-bad-clienthello-sni-reject-mismatch]
ssl_conf = 7-SNI-bad-clienthello-sni-reject-mismatch-ssl

[7-SNI-bad-clienthello-sni-reject-mismatch-ssl]
server = 7-SNI-bad-clienthello-sni-reject-mismatch-server
client = 7-SNI-bad-clienthello-sni-reject-mismatch-client
server2 = 7-SNI-bad-clienthello-sni-reject-mismatch-server

[7-SNI-bad-clienthello-sni-reject-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-SNI-bad-clienthello-sni-reject-mismatch-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = ServerFail
ExpectedServerAlert = UnrecognizedName
server = 7-SNI-bad-clienthello-sni-reject-mismatch-server-extra
server2 = 7-SNI-bad-clienthello-sni-reject-mismatch-server-extra
client = 7-SNI-bad-clienthello-sni-reject-mismatch-client-extra

[7-SNI-bad-clienthello-sni-reject-mismatch-server-extra]
ServerNameCallback = ClientHelloRejectMismatch

[7-SNI-bad-clienthello-sni-reject-mismatch-client-extra]
ServerName = invalid


# ===========================================================

[8-SNI-clienthello-disable-v12]
ssl_conf = 8-SNI-clienthello-disable-v12-ssl

[8-SNI-clienthello-disable-v12-ssl]
server = 8-SNI-clienthello-disable-v12-server
client = 8-SNI-clienthello-disable-v12-client
server2 = 8-SNI-clienthello-disable-v12-server

[8-SNI-clienthello-disable-v12-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-SNI-clienthello-disable-v12-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedProtocol = TLSv1.1
ExpectedServerName = server2
server = 8-SNI-clienthello-disable-v12-server-extra
server2 = 8-SNI-clienthello-disable-v12-server-extra
client = 8-SNI-clienthello-disable-v12-client-extra

[8-SNI-clienthello-disable-v12-server-extra]
ServerNameCallback = ClientHelloNoV12

[8-SNI-clienthello-disable-v12-client-extra]
ServerName = server2


