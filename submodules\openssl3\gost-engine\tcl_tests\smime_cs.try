#!/usr/bin/tclsh
lappend auto_path [file dirname [info script]]
package require ossltest
cd $::test::dir
set testname [file rootname [file tail $::argv0]]

start_tests "Тесты на восместимость по подписи с эталонными серверами" 

if {[info exists env(ALG_LIST)]} {
	set alg_list $env(ALG_LIST)
} else {
	switch -exact [engine_name] {
		"ccore" {set alg_list {gost2001:A gost2001:B gost2001:C}}
		"open" {set alg_list {gost2001:A gost2001:B gost2001:C}}
	}
}

foreach alg $alg_list {
	set alg_fn [string map {":" "_"} $alg]
	set username U_smime_$alg_fn
	switch -glob $alg {
		gost2012* {set ::test::ca smimeCA-2012
		           set ca_sign_alg hash_with_sign12_512
		          }
		* {set ::test::ca smimeCA
		   set ca_sign_alg hash_with_sign01_cp
		  }
	}
set hosts [list tls-ref-cp21]
foreach hstname $hosts {


test -skip {![file exists sign_$alg_fn.msg]} "Verifying a message signed with $alg without ca via cms" {
	grep Veri [openssl_remote "$username sign_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_$alg_fn.msg -out TESTPATH/verified.txt -noverify -certfile TESTPATH/$username/cert.pem" "smime"]
} 0 "Verification successful
"

test -skip {![file exists sign_$alg_fn.msg]}  "Verifying a message signed with $alg with ca via cms" {
	grep Veri [openssl_remote "$::test::ca sign_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_$alg_fn.msg -out TESTPATH/verified.txt -certfile TESTPATH/$username/cert.pem -CAfile TESTPATH/$::test::ca/cacert.pem" "smime"]
} 0 "Verification successful
"

test -skip {![file exists bad_$alg_fn.msg]} -createsfiles TESTPATH/verified.txt] "Verifying corrupted messages signed with $alg via cms" {
	grep Verification [openssl_remote "bad_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/bad_$alg_fn.msg -out TESTPATH/verified.txt -noverify -certfile TESTPATH/$username/cert.pem" "smime"]
} 1 "Verification failure"

test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert inside without ca via cms" {
	grep Veri [openssl_remote "sign_c_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_c_$alg_fn.msg -out TESTPATH/verified.txt -noverify" "smime"]
} 0 "Verification successful
"

test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert with ca via cms" {
	grep Veri [openssl_remote "sign_c_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_c_$alg_fn.msg -out TESTPATH/verified.txt -CAfile TESTPATH/$::test::ca/cacert.pem" "smime"]
} 0 "Verification successful
"

test -createsfiles TESTPATH/verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a message signed by $alg having cert inside without ca via cms" {
	grep Veri [openssl_remote "sign_op_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_op_$alg_fn.msg -out TESTPATH/verified.txt -noverify" "smime"]
} 0 "Verification successful
"

test -createsfiles TESTPATH/verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a $alg opaque  message with ca via cms" {
	grep Veri [openssl_remote "sign_op_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/sign_op_$alg_fn.msg -out TESTPATH/verified.txt -CAfile TESTPATH/$::test::ca/cacert.pem" "smime"]
} 0 "Verification successful
"

test -skip {![file exists broken_op_$alg_fn.msg]} "Verifying broken $alg opaque message" {
	grep Verification [openssl_remote "broken_op_$alg_fn.msg" "$hstname" "cms -verify -text -in TESTPATH/broken_op_$alg_fn.msg -out TESTPATH/verified.txt -CAfile TESTPATH/$::test::ca/cacert.pem" "smime"]
} 1 "Verification failure"

test -skip {![file exists sign_det_$alg_fn.pem]} "Verifying detached $alg signature via cms" {
	grep Veri [openssl_remote "sign_det_$alg_fn.pem sign.dat" "$hstname" "cms -verify -content TESTPATH/sign.dat -inform PEM -in TESTPATH/sign_det_$alg_fn.pem -out TESTPATH/verified.txt -noverify" "smime"]
} 0 "Verification successful
"

test -skip {![file exists sign_det_$alg_fn.msg] || ![file exists bad.dat]} "Verifying corrupted $alg detached signature" {
	grep Verification [openssl_remote "sign_det_$alg_fn.msg bad.dat" "$hstname" "cms -verify -content TESTPATH/bad.dat -in TESTPATH/sign_det_$alg_fn.msg -out TESTPATH/verified.txt -CAfile TESTPATH/$::test::ca/cacert.pem" "smime"]
} 1 "Verification failure"
}

}
end_tests
