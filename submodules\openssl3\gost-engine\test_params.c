/*
 * Test GOST 34.10 Verify operation with every curve parameter
 *
 * Copyright (C) 2019 <EMAIL>. All Rights Reserved.
 *
 * Contents licensed under the terms of the OpenSSL license
 * See https://www.openssl.org/source/license.html for details
 */

#ifdef _MSC_VER
# pragma warning(push, 3)
# include <openssl/applink.c>
# pragma warning(pop)
#endif
#include "e_gost_err.h"
#include "gost_lcl.h"
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <openssl/asn1.h>
#include <openssl/obj_mac.h>
#include <openssl/ec.h>
#include <openssl/bn.h>
#include <openssl/safestack.h>
#include <string.h>

#define T(e) \
    if (!(e)) { \
        ERR_print_errors_fp(stderr); \
        OpenSSLDie(__FILE__, __LINE__, #e); \
    }
#define TE(e) \
    if (!(e)) { \
        ERR_print_errors_fp(stderr); \
        fprintf(stderr, "Error at %s:%d %s\n", __FILE__, __LINE__, #e); \
        return -1; \
    }

#define cRED	"\033[1;31m"
#define cDRED	"\033[0;31m"
#define cGREEN	"\033[1;32m"
#define cDGREEN	"\033[0;32m"
#define cBLUE	"\033[1;34m"
#define cDBLUE	"\033[0;34m"
#define cNORM	"\033[m"
#define TEST_ASSERT(e) {if ((test = (e))) \
		 printf(cRED "  Test FAILED" cNORM "\n"); \
	     else \
		 printf(cGREEN "  Test passed" cNORM "\n");}

struct test_param {
    unsigned int param;		/* NID of EC parameters */
    unsigned int len;		/* length of a digest or a half of the key */
    unsigned int data_len;	/* length of @data */
    const uint8_t *data;	/* data to hash (optional) */
    const uint8_t *hash;	/* hash of data */
    const uint8_t *signature;	/* raw signature */
    const uint8_t *pub_key;	/* raw public key */
};

/*
 * Parameters provided by CryptoPro for Basealt SPO to test
 * interop with open-source software.
 */

/* 1.2.643.2.2.35.1 - szOID_GostR3410_2001_CryptoPro_A_ParamSet */
const uint8_t data_2001_256_setA[] = {
    0xCB, 0x03, 0xB7, 0x57, 0xBC, 0xA7, 0xBC, 0xB6,
    0xB0, 0x37, 0xC5, 0xD4, 0xBB, 0x51, 0x52, 0x9A,
    0xEE, 0xF3, 0x28, 0x9B, 0x14, 0x11, 0xE2, 0xCB,
    0xAB, 0x82, 0x1A, 0xDF, 0x1D, 0x2A, 0x70, 0xE6,
    0x09, 0x1B, 0x7C, 0xE9, 0x6D, 0xAE, 0xAF, 0xF9,
};

static uint8_t hash_2001_256_setA[] = {
    0x4F, 0x49, 0xB3, 0x9E, 0xA0, 0x06, 0xD0, 0xDA,
    0x4D, 0x81, 0x50, 0x61, 0x08, 0x66, 0xA1, 0x18,
    0xA6, 0x04, 0x25, 0x98, 0xB9, 0x66, 0x00, 0x32,
    0xC5, 0x40, 0xD6, 0xEB, 0x1A, 0x85, 0x70, 0xEE,
};

static uint8_t signature_2001_256_setA[] = {
    0x7E, 0xE2, 0x99, 0xB9, 0x50, 0x78, 0x1C, 0xE4,
    0xDC, 0xA6, 0x68, 0xCF, 0x6E, 0x88, 0xDC, 0x29,
    0x3F, 0x13, 0x8F, 0x12, 0x14, 0x1A, 0x03, 0x3A,
    0x09, 0x01, 0x78, 0x52, 0x82, 0x7D, 0xDC, 0x7B,
    0xF0, 0xE5, 0x49, 0x93, 0x9D, 0xBF, 0x95, 0x4B,
    0xB4, 0xB1, 0x40, 0x72, 0xDE, 0x15, 0x86, 0x11,
    0x1D, 0xF9, 0x63, 0xF2, 0xE7, 0xEF, 0xB7, 0xBD,
    0x73, 0xF7, 0xB7, 0xD1, 0x95, 0x61, 0xBA, 0x1C,
};

static uint8_t pubkey_2001_256_setA[] = {
    0xF0, 0x69, 0xAF, 0x90, 0xEE, 0xE4, 0xA3, 0x33,
    0x52, 0xA2, 0xE8, 0x0C, 0x72, 0xE6, 0x20, 0xAF,
    0xB7, 0x66, 0x03, 0xE5, 0xFF, 0x85, 0xF3, 0xAA,
    0x5F, 0x38, 0x2F, 0x8E, 0x44, 0xEF, 0x51, 0x0F,
    0x82, 0x59, 0x4A, 0x99, 0x10, 0xB8, 0x89, 0xCD,
    0x78, 0xD2, 0xBA, 0xF1, 0x97, 0xFE, 0xEB, 0xE6,
    0x74, 0xC1, 0x96, 0x90, 0x97, 0x71, 0xAD, 0x16,
    0x9F, 0x9B, 0x37, 0xDD, 0x2B, 0x44, 0xFF, 0x2D,
};

/* 1.2.643.2.2.35.2 - szOID_GostR3410_2001_CryptoPro_B_ParamSet */
const uint8_t data_2001_256_setB[] = {
    0x54, 0x7D, 0x31, 0xFE, 0x69, 0xD4, 0xB1, 0x58,
    0x7E, 0x34, 0x2D, 0xC9, 0x3D, 0xBD, 0x67, 0xAF,
    0xD8, 0x31, 0x90, 0xC4, 0xA9, 0x07, 0xCE, 0x34,
    0x3F, 0x90, 0x3A, 0xC4, 0xFC, 0xE4, 0x4E, 0xEA,
    0xF1, 0xE9, 0x04, 0xD0, 0x7E, 0x4B, 0xCF, 0x39
};

const uint8_t hash_2001_256_setB[] = {
    0x10, 0xDE, 0x3D, 0x7A, 0xEE, 0x6E, 0xC0, 0x0D,
    0x57, 0x9B, 0x4B, 0xB2, 0x92, 0xB8, 0xE5, 0x4E,
    0x75, 0x19, 0x92, 0xFE, 0x71, 0x91, 0xF7, 0xF2,
    0x72, 0xE7, 0x77, 0x47, 0x51, 0xF7, 0xEC, 0x26,
};

const uint8_t signature_2001_256_setB[] = {
    0x9C, 0x27, 0x76, 0x22, 0xB8, 0x4A, 0xB6, 0x2B,
    0xBA, 0x2E, 0xE3, 0xD5, 0x89, 0x72, 0x89, 0x53,
    0x7C, 0x2D, 0xB7, 0x70, 0x8A, 0xD5, 0x7B, 0x61,
    0xDF, 0xD1, 0xD6, 0x7F, 0x77, 0xFF, 0xDB, 0x4E,
    0xEE, 0x98, 0xFC, 0x2C, 0xDE, 0xAA, 0xC7, 0xDE,
    0x42, 0xEE, 0x40, 0x12, 0x5E, 0xC8, 0xFE, 0x0E,
    0x97, 0x80, 0xB9, 0x6A, 0xAC, 0x93, 0xAD, 0xEE,
    0x96, 0xE1, 0xDB, 0xC6, 0xF2, 0xAC, 0xF4, 0x22
};

const uint8_t pubkey_2001_256_setB[] = {
    0x6A, 0x8A, 0x5E, 0x32, 0x00, 0xED, 0xD3, 0xA7,
    0x38, 0x83, 0x58, 0x7D, 0xBD, 0xE9, 0xFD, 0xA9,
    0x00, 0xAE, 0xE8, 0x4F, 0xFF, 0x71, 0xD2, 0xA0,
    0x79, 0x14, 0xD4, 0xB4, 0xB2, 0x00, 0x9A, 0x0A,
    0x51, 0x21, 0xD5, 0x19, 0x05, 0xF1, 0xB7, 0x6C,
    0x2E, 0x3A, 0x18, 0xDD, 0x82, 0x67, 0x7F, 0x96,
    0x0B, 0x1A, 0x76, 0x93, 0xF7, 0x6A, 0xCA, 0x15,
    0xCD, 0xEE, 0xA1, 0xD2, 0xDE, 0xD5, 0x56, 0x20
};

/* 1.2.643.2.2.35.3 - szOID_GostR3410_2001_CryptoPro_C_ParamSet */
const uint8_t data_2001_256_setC[] = {
    0x30, 0x26, 0xBB, 0x7C, 0xEE, 0x71, 0x15, 0xF6,
    0x01, 0x3B, 0x8E, 0xF9, 0x04, 0xA7, 0x02, 0x39,
    0xC7, 0xF2, 0xDC, 0x15, 0x2C, 0xB4, 0x95, 0x74,
    0x1B, 0x66, 0x78, 0x5A, 0x0F, 0xF1, 0x88, 0x5A,
    0x68, 0x7F, 0xD2, 0xE8, 0xF3, 0x85, 0xE2, 0xD5
};

const uint8_t hash_2001_256_setC[] = {
    0x8C, 0xFE, 0x45, 0xBD, 0x4F, 0x9D, 0xEB, 0x80,
    0x78, 0xA7, 0xA3, 0xFB, 0xB3, 0x06, 0x2A, 0xE4,
    0xD6, 0xF4, 0x1A, 0x0B, 0x31, 0xEB, 0x82, 0xB8,
    0x13, 0x32, 0xD6, 0xA2, 0xAE, 0x80, 0xF1, 0xF7
};

const uint8_t signature_2001_256_setC[] = {
    0xD3, 0x16, 0xBB, 0x65, 0x48, 0x6D, 0x2D, 0x55,
    0x14, 0x13, 0xAE, 0x20, 0x31, 0x2B, 0xA5, 0x6B,
    0x32, 0x56, 0x0C, 0xCF, 0xB3, 0x48, 0x59, 0x63,
    0x3C, 0x8F, 0xD6, 0x98, 0x9D, 0x88, 0xB1, 0x34,
    0xAB, 0xBD, 0x04, 0x39, 0x66, 0xE5, 0x9D, 0x63,
    0xAA, 0xAB, 0x63, 0x98, 0x6C, 0x06, 0x54, 0xC2,
    0xDB, 0xD0, 0x6A, 0x6E, 0x57, 0xB3, 0x23, 0x41,
    0xAB, 0x22, 0xBB, 0x13, 0x37, 0x18, 0x3E, 0x08
};

const uint8_t pubkey_2001_256_setC[] = {
    0xBA, 0x43, 0xE0, 0xF4, 0x0D, 0x3E, 0x50, 0x60,
    0xCE, 0xC7, 0xE7, 0x0C, 0x34, 0x8F, 0x21, 0x22,
    0xF4, 0x36, 0x7E, 0x0E, 0x35, 0x49, 0x92, 0x66,
    0x89, 0x92, 0x0B, 0x62, 0x37, 0xF8, 0x69, 0x82,
    0xB7, 0x0E, 0x32, 0x29, 0x5F, 0xD6, 0x44, 0x56,
    0xBB, 0x16, 0xD0, 0x8D, 0x3B, 0xE5, 0xC2, 0xB5,
    0xCE, 0x99, 0x4D, 0xDD, 0x41, 0xF9, 0xE7, 0x98,
    0x14, 0xBD, 0xC5, 0x87, 0xAE, 0x8D, 0xF1, 0x25
};

/* 1.2.643.7.1.2.1.1.1 - szOID_tc26_gost_3410_12_256_paramSetA */
const uint8_t data_tc26_gost_3410_12_256_setA[] = {
    0xBF, 0xA5, 0x7B, 0x70, 0x8F, 0x4D, 0xDE, 0x9A,
    0x38, 0x5A, 0x4A, 0xA5, 0xD9, 0xDB, 0x84, 0x6A,
    0x23, 0xD8, 0xB4, 0x73, 0x1E, 0x9A, 0x55, 0x42,
    0x32, 0x85, 0x28, 0xE8, 0x2B, 0x0D, 0x83, 0x0E,
    0x06, 0xBF, 0x46, 0x99, 0x38, 0xDF, 0xB4, 0xFA,
    0x08, 0x0D, 0x5B, 0x20, 0xEC, 0x0D, 0xD9, 0x7F,
    0x7C, 0x69, 0x51, 0xDA, 0xA5, 0x50, 0x2A, 0x65,
    0xFD, 0xB1, 0x1F, 0x88, 0xCB, 0xA6, 0xE2, 0x61
};

const uint8_t hash_tc26_gost_3410_12_256_setA[] = {
    0xFD, 0x39, 0xEA, 0x88, 0x90, 0x89, 0xD8, 0x1E,
    0xE2, 0x49, 0x11, 0xDB, 0x51, 0x71, 0x48, 0x0A,
    0xD7, 0x27, 0xCC, 0xBA, 0xD2, 0x19, 0xF4, 0x9E,
    0x98, 0xC6, 0x3D, 0x1F, 0xB5, 0x7C, 0x24, 0x2F
};

const uint8_t signature_tc26_gost_3410_12_256_setA[] = {
    0x0D, 0xC4, 0xCA, 0x98, 0x2B, 0x15, 0x51, 0xD4,
    0x74, 0x36, 0x24, 0x10, 0xEA, 0x21, 0x2D, 0x8E,
    0xBB, 0x6C, 0xBB, 0x5E, 0xE5, 0x26, 0x76, 0x3D,
    0x88, 0x62, 0xC5, 0x2B, 0x5F, 0x93, 0xF9, 0x01,
    0x46, 0x49, 0xD6, 0x0F, 0x30, 0x44, 0x45, 0x55,
    0x0B, 0xC6, 0x63, 0x60, 0x20, 0x26, 0x09, 0x08,
    0x85, 0x2E, 0x16, 0xBE, 0x14, 0x46, 0x31, 0x89,
    0xA6, 0xD3, 0x52, 0xBA, 0xD5, 0x51, 0x69, 0x24
};

const uint8_t pubkey_tc26_gost_3410_12_256_setA[] = {
    0x3B, 0x8A, 0x6A, 0x5E, 0xFE, 0x62, 0x30, 0x31,
    0x3A, 0x34, 0x9A, 0x6A, 0xF0, 0xC4, 0x92, 0x4E,
    0xF4, 0xF8, 0x0E, 0xF6, 0xE1, 0xF2, 0x3F, 0xE1,
    0x9A, 0xA9, 0x7A, 0x77, 0x97, 0x3A, 0x11, 0xE8,
    0xD2, 0xA8, 0x5F, 0xD1, 0x49, 0xE0, 0xBD, 0xAB,
    0x28, 0xD5, 0x2B, 0x02, 0x06, 0x99, 0x8E, 0x7E,
    0xFF, 0xDB, 0x2A, 0xDE, 0x92, 0x11, 0x34, 0x5D,
    0xCF, 0x40, 0xEE, 0x0B, 0xD0, 0x61, 0x89, 0x75
};

/* 1.2.643.7.1.2.1.2.1 - szOID_tc26_gost_3410_12_512_paramSetA */
const uint8_t data_tc26_gost_3410_12_512_setA[] = {
    0xEF, 0x15, 0x1E, 0x5B, 0xE9, 0x52, 0x35, 0x84,
    0x17, 0x07, 0x4B, 0xBD, 0x10, 0xEA, 0x7D, 0x77,
    0x1E, 0xBF, 0x95, 0x55, 0xA4, 0x2A, 0x8F, 0xA7,
    0xFF, 0x3F, 0xEC, 0x8F, 0xA2, 0x3C, 0x90, 0x65,
    0x4A, 0xB8, 0x59, 0x31, 0xE1, 0x97, 0xD5, 0xC4,
    0x26, 0x49, 0xCE, 0x81, 0x53, 0xBE, 0x79, 0xF7,
    0xA1, 0xB2, 0xE0, 0x7D, 0x44, 0xA4, 0x74, 0x64,
    0xB0, 0x09, 0x62, 0x35, 0xC5, 0x50, 0x7F, 0x36
};

const uint8_t hash_tc26_gost_3410_12_512_setA[] = {
    0xE9, 0x25, 0x04, 0x72, 0x12, 0xC3, 0x82, 0x06,
    0x00, 0xB6, 0x76, 0xF4, 0x4D, 0x71, 0xE4, 0x42,
    0x49, 0x3E, 0x57, 0x23, 0xBF, 0xBD, 0xBF, 0x94,
    0x7C, 0x0E, 0x2D, 0xAA, 0x48, 0x36, 0xF5, 0x9A,
    0x4D, 0x66, 0x02, 0x42, 0x0E, 0xCC, 0x94, 0xDF,
    0x7D, 0x21, 0xF8, 0x69, 0x1D, 0xFD, 0x45, 0x56,
    0x42, 0x4C, 0x69, 0x17, 0x8E, 0x21, 0xBE, 0x4F,
    0x2C, 0xC8, 0x61, 0xDB, 0xA7, 0x24, 0xEC, 0x48
};

const uint8_t signature_tc26_gost_3410_12_512_setA[] = {
    0xC3, 0xE5, 0xFA, 0xE8, 0x5F, 0x35, 0x88, 0x13,
    0x49, 0x22, 0xC2, 0x1D, 0x5F, 0x73, 0xD4, 0x37,
    0x34, 0x1D, 0xEF, 0x56, 0x04, 0x6B, 0x17, 0x6D,
    0x00, 0x71, 0xC9, 0x14, 0xF0, 0x03, 0x3F, 0x64,
    0xCA, 0x67, 0x3A, 0x6E, 0xFE, 0x8B, 0x1D, 0x36,
    0xDD, 0x57, 0xE3, 0x28, 0x74, 0x64, 0xF1, 0xD0,
    0x89, 0x9A, 0x9B, 0xDD, 0xF6, 0xBB, 0x9B, 0x58,
    0xA4, 0x8F, 0x56, 0xB5, 0xDE, 0xF9, 0x9E, 0x70,
    0x62, 0xC8, 0xF3, 0x19, 0xE3, 0x4B, 0x73, 0x0F,
    0x95, 0x5D, 0x20, 0x97, 0x74, 0x5C, 0xAA, 0x02,
    0xB7, 0xFA, 0xFD, 0x33, 0xD5, 0xBC, 0xE4, 0xDD,
    0x9A, 0x66, 0x98, 0xEB, 0xE9, 0x51, 0x03, 0x66,
    0x25, 0x10, 0xF5, 0x8F, 0xB4, 0x45, 0x4F, 0xB5,
    0x3A, 0x61, 0x56, 0xCF, 0x8C, 0x1E, 0xD8, 0xAF,
    0x4B, 0xEC, 0x54, 0xDB, 0x43, 0x4E, 0xD6, 0x55,
    0x3F, 0xA3, 0x45, 0x15, 0x06, 0x74, 0xFA, 0x6C
};

const uint8_t pubkey_tc26_gost_3410_12_512_setA[] = {
    0xB2, 0xBF, 0x45, 0x23, 0x00, 0x57, 0x70, 0xAE,
    0xAB, 0x5B, 0x63, 0xEC, 0xA8, 0x5F, 0xCF, 0xD0,
    0xBA, 0x88, 0x64, 0x79, 0x3D, 0xB6, 0x70, 0x88,
    0xE8, 0xD8, 0xA4, 0x95, 0x9E, 0xB9, 0x78, 0x73,
    0x9F, 0x0A, 0x34, 0x74, 0xED, 0xFF, 0xB9, 0x7E,
    0x34, 0x1B, 0xE0, 0x2A, 0xE2, 0xD8, 0x07, 0xE9,
    0xC2, 0xD2, 0x84, 0x39, 0x9E, 0x36, 0x0F, 0x7A,
    0xE2, 0x56, 0x2A, 0x81, 0x6C, 0x94, 0x9D, 0x5E,
    0x6E, 0x68, 0x94, 0xFD, 0x75, 0x14, 0xE5, 0x07,
    0xED, 0x45, 0x2B, 0x07, 0xE1, 0xB2, 0x79, 0x2A,
    0x21, 0x34, 0x21, 0x95, 0x02, 0xF2, 0xAF, 0xDC,
    0x8A, 0xD7, 0xA3, 0x72, 0x4C, 0x02, 0xA2, 0xF8,
    0x59, 0xE2, 0x91, 0x58, 0x01, 0x1D, 0x55, 0xC6,
    0xEC, 0x73, 0xEA, 0x44, 0x5B, 0x35, 0x08, 0x5C,
    0xAC, 0xA0, 0xB9, 0x4B, 0x28, 0xE7, 0xBD, 0x8B,
    0xB2, 0x78, 0x9B, 0x4F, 0x46, 0xC9, 0xD6, 0x84
};

/* 1.2.643.7.1.2.1.2.2 - szOID_tc26_gost_3410_12_512_paramSetB */
const uint8_t data_tc26_gost_3410_12_512_setB[] = {
    0x84, 0x66, 0x52, 0x16, 0xB7, 0x53, 0xC0, 0xBB,
    0xAE, 0xED, 0x2F, 0x37, 0x78, 0x43, 0x03, 0xCF,
    0x21, 0x5D, 0x36, 0x97, 0x55, 0x2B, 0x3B, 0xF3,
    0xFB, 0x9C, 0x18, 0x04, 0x81, 0x9B, 0x50, 0x9E,
    0xBE, 0xC1, 0x97, 0x53, 0xBC, 0xB1, 0x55, 0xDC,
    0x0C, 0xAB, 0x7D, 0xB3, 0x88, 0xBC, 0xB2, 0x9C,
    0x86, 0x16, 0x21, 0x0A, 0x95, 0x9F, 0x3D, 0xA6,
    0x0C, 0xB4, 0x33, 0x1B, 0x7B, 0x29, 0xA3, 0x70,
    0x1A, 0x67, 0xD1, 0xC7, 0x45, 0xE7, 0xF6, 0xC0,
    0x66
};

const uint8_t hash_tc26_gost_3410_12_512_setB[] = {
    0x6A, 0x55, 0x15, 0x81, 0x50, 0x2A, 0x14, 0x22,
    0x6F, 0xD1, 0x4B, 0x50, 0xB1, 0xE2, 0x6C, 0x80,
    0xC4, 0x84, 0x21, 0xF9, 0x63, 0x46, 0xAF, 0xE8,
    0xE0, 0x2C, 0xFD, 0x41, 0x1E, 0x49, 0x01, 0x6B,
    0x00, 0x3C, 0xEB, 0x5F, 0x6B, 0x34, 0xA9, 0x93,
    0x2D, 0x86, 0x2F, 0xEA, 0x58, 0x83, 0x81, 0x51,
    0xF7, 0xA2, 0xCC, 0x0F, 0xAE, 0xAD, 0x40, 0x65,
    0x82, 0xC6, 0x53, 0x05, 0xAE, 0xEB, 0x22, 0xB8
};

const uint8_t signature_tc26_gost_3410_12_512_setB[] = {
    0x15, 0xFD, 0xD4, 0x3B, 0x57, 0x5A, 0x97, 0x4E,
    0x0D, 0xE6, 0xBC, 0xB5, 0x1F, 0x91, 0x3F, 0x8B,
    0xEE, 0xE9, 0x88, 0xF3, 0x94, 0x3D, 0xB6, 0x09,
    0x6B, 0xD6, 0xBA, 0x85, 0x42, 0xE8, 0xF4, 0xCE,
    0x0D, 0xF5, 0x8D, 0xD1, 0xAF, 0xC9, 0xC4, 0xA7,
    0x82, 0x3E, 0xBB, 0x7F, 0x72, 0x50, 0xF5, 0x36,
    0x06, 0x54, 0x10, 0x31, 0x89, 0xA9, 0x80, 0x1A,
    0x55, 0x48, 0xB7, 0xEA, 0xB3, 0xAE, 0x77, 0x4E,
    0xC1, 0x45, 0x52, 0xDD, 0xBF, 0xA9, 0x8E, 0x02,
    0x10, 0x80, 0x8F, 0x9C, 0xD1, 0x85, 0x36, 0xBA,
    0x7C, 0x20, 0x86, 0x2E, 0xDB, 0x25, 0x0C, 0x1B,
    0x53, 0xBA, 0x26, 0x39, 0xE0, 0xD4, 0xE6, 0xE7,
    0x4B, 0xA1, 0x02, 0x7D, 0xD4, 0x74, 0x6B, 0x6E,
    0x82, 0xDD, 0x92, 0xA2, 0xA4, 0xBA, 0xD4, 0xB6,
    0xF9, 0x57, 0x57, 0x67, 0xB6, 0x5A, 0xA2, 0x72,
    0x96, 0xEA, 0xE9, 0x2E, 0xA9, 0x11, 0x73, 0x27
};

const uint8_t pubkey_tc26_gost_3410_12_512_setB[] = {
    0x86, 0x97, 0xE7, 0x19, 0x03, 0x5E, 0x54, 0xA6,
    0xE8, 0x7A, 0xEE, 0xD5, 0x76, 0xC4, 0xC4, 0x72,
    0x4A, 0x59, 0x55, 0xEB, 0x72, 0xF7, 0xE0, 0x62,
    0xB1, 0x0D, 0x1B, 0x79, 0x32, 0x72, 0x83, 0x0D,
    0x1F, 0x7B, 0x74, 0x12, 0x29, 0x20, 0xFD, 0x23,
    0xAA, 0x8C, 0x77, 0xA1, 0x23, 0x38, 0x7F, 0x73,
    0x07, 0x94, 0x8A, 0x34, 0x46, 0xDB, 0x7C, 0xFB,
    0x46, 0xF0, 0x63, 0xE9, 0xD3, 0xAF, 0xC8, 0x4B,
    0x78, 0x65, 0x99, 0xAE, 0x71, 0x7F, 0x45, 0xF8,
    0x7C, 0xF3, 0x0A, 0x2F, 0x97, 0xEB, 0x85, 0x1A,
    0x22, 0x67, 0x65, 0x78, 0xA5, 0xF8, 0xF2, 0x8C,
    0xE8, 0xF5, 0x9B, 0x75, 0xA8, 0x3D, 0x81, 0xC1,
    0x7F, 0x69, 0x23, 0x94, 0xDE, 0x89, 0xFC, 0x65,
    0xB3, 0xFE, 0x18, 0x91, 0xC0, 0x03, 0xBE, 0xDA,
    0xC8, 0x7D, 0x48, 0x12, 0x4F, 0x75, 0xC5, 0xAE,
    0xB4, 0x50, 0xA0, 0xFC, 0x27, 0xC1, 0xE7, 0x00
};

/* 1.2.643.7.1.2.1.2.3 - szOID_tc26_gost_3410_12_512_paramSetC */
const uint8_t data_tc26_gost_3410_12_512_setC[] = {
    0x40, 0xE2, 0xDD, 0x43, 0xF9, 0x59, 0x3C, 0xDC,
    0x9C, 0x8F, 0x2E, 0xBF, 0xA8, 0x0B, 0x2D, 0xD4,
    0xB5, 0x00, 0x56, 0x93, 0xFC, 0xCE, 0x73, 0x5B,
    0x99, 0x66, 0x24, 0x0A, 0x76, 0x52, 0x2E, 0xBC,
    0xBE, 0xEA, 0x8A, 0x52, 0xFC, 0x95, 0x02, 0x8F,
    0xB9, 0x8E, 0x23, 0x00, 0x47, 0x40, 0x28, 0xE4,
    0x92, 0x9C, 0x19, 0x99, 0xBD, 0x98, 0xF5, 0x3A,
    0xA0, 0xBE, 0xFB, 0xC7, 0xC1, 0xE3, 0x98, 0x6B,
    0x8D, 0x2F, 0x5A, 0x85, 0xB9, 0x46, 0x90, 0x83,
    0x6D
};

const uint8_t hash_tc26_gost_3410_12_512_setC[] = {
    0xBD, 0x82, 0xF1, 0x34, 0x33, 0x74, 0x9C, 0xC2,
    0x9E, 0x95, 0x67, 0x57, 0x2C, 0x6D, 0x83, 0x3D,
    0xFD, 0xBD, 0x7C, 0xD7, 0xAA, 0xE7, 0x28, 0xF9,
    0x81, 0xB9, 0xCF, 0xAE, 0x4A, 0xAA, 0x17, 0x7D,
    0x84, 0x79, 0x25, 0xC5, 0x66, 0xAA, 0x9E, 0x28,
    0x66, 0x41, 0x95, 0xC4, 0xFF, 0xF6, 0x72, 0xEF,
    0x0E, 0x08, 0xC6, 0x0B, 0x2E, 0x0F, 0xCB, 0xC6,
    0x96, 0x4A, 0x77, 0x0D, 0x14, 0xE5, 0x35, 0xC0
};

const uint8_t signature_tc26_gost_3410_12_512_setC[] = {
    0x35, 0x1E, 0x65, 0xAE, 0x90, 0xB0, 0x05, 0x04,
    0x02, 0xF5, 0x22, 0xAC, 0xE4, 0x5D, 0x8C, 0x35,
    0x17, 0x2B, 0xFF, 0xD7, 0xCA, 0x95, 0x0E, 0xF9,
    0x85, 0x2A, 0xD0, 0xA0, 0xE1, 0x04, 0x43, 0xCA,
    0x70, 0x9A, 0xDF, 0x35, 0xD7, 0xD8, 0x8D, 0xC2,
    0x78, 0x84, 0x4C, 0xCF, 0x58, 0x25, 0x9C, 0xD2,
    0x40, 0x5A, 0xEC, 0xB3, 0x9A, 0x77, 0x6B, 0x4F,
    0x63, 0x26, 0xDD, 0xB2, 0x19, 0x8E, 0x9C, 0x39,
    0x09, 0x55, 0x72, 0xD6, 0xDD, 0x69, 0x90, 0x1E,
    0xB6, 0x1D, 0x36, 0x97, 0x49, 0x98, 0x99, 0x67,
    0x4A, 0x3B, 0xCD, 0x2D, 0xEB, 0x32, 0xCA, 0x44,
    0x4F, 0xB6, 0xC0, 0x9C, 0x4B, 0xF9, 0x18, 0x2E,
    0x42, 0xAF, 0x8B, 0xE4, 0x45, 0xE8, 0x45, 0xEF,
    0xF4, 0x10, 0x96, 0xF4, 0x16, 0xCA, 0xEB, 0xD1,
    0x72, 0xCC, 0x32, 0x28, 0xA8, 0x46, 0x03, 0x34,
    0x65, 0x3E, 0x47, 0xB2, 0xC3, 0x99, 0xE2, 0x36
};

const uint8_t pubkey_tc26_gost_3410_12_512_setC[] = {
    0xA9, 0x85, 0x17, 0xF7, 0xFE, 0x1C, 0x43, 0x8A,
    0xBE, 0xDE, 0x79, 0xE8, 0x62, 0xFE, 0x51, 0x0E,
    0xA7, 0xEA, 0x05, 0x46, 0x34, 0xD9, 0x54, 0x5B,
    0xFD, 0xB4, 0xC5, 0xCB, 0xA6, 0x9C, 0xFC, 0x90,
    0x4B, 0x59, 0x14, 0xA1, 0xE7, 0x2A, 0xDE, 0x2B,
    0xB8, 0x86, 0x9E, 0xB5, 0xDB, 0xB3, 0xD3, 0xD7,
    0x95, 0x88, 0xCB, 0xEE, 0x33, 0x20, 0x82, 0xB2,
    0xAC, 0xBF, 0x79, 0xDF, 0x2E, 0x88, 0xF0, 0x5A,
    0x62, 0x90, 0x26, 0x52, 0xAD, 0x64, 0x67, 0x36,
    0x1B, 0xE6, 0xCA, 0x57, 0x09, 0xEF, 0xF5, 0x56,
    0x0E, 0x32, 0xDF, 0xB4, 0x6C, 0xC8, 0xA8, 0xBB,
    0xCB, 0x4C, 0xB4, 0xBA, 0x63, 0x41, 0xBA, 0x1D,
    0xAB, 0xB0, 0x12, 0x82, 0xFD, 0x50, 0x37, 0xDB,
    0x69, 0x08, 0xF2, 0x7D, 0x9E, 0xC0, 0xF7, 0xA6,
    0xE9, 0x50, 0x26, 0x94, 0x88, 0x08, 0x5E, 0xDD,
    0x34, 0xC5, 0xC1, 0x9D, 0x50, 0x50, 0x4A, 0xAE
};
/* end */

static struct test_param cp_2001_256_a = {
    .param	= NID_id_GostR3410_2001_CryptoPro_A_ParamSet,
    .len	= 256 / 8,
    .data	= data_2001_256_setA,
    .data_len	= sizeof(data_2001_256_setA),
    .hash	= hash_2001_256_setA,
    .signature	= signature_2001_256_setA,
    .pub_key	= pubkey_2001_256_setA,
};

static struct test_param cp_2001_256_b = {
    .param	= NID_id_GostR3410_2001_CryptoPro_B_ParamSet,
    .len	= 256 / 8,
    .data	= data_2001_256_setB,
    .data_len	= sizeof(data_2001_256_setB),
    .hash	= hash_2001_256_setB,
    .signature	= signature_2001_256_setB,
    .pub_key	= pubkey_2001_256_setB,
};

static struct test_param cp_2001_256_c = {
    .param	= NID_id_GostR3410_2001_CryptoPro_C_ParamSet,
    .len	= 256 / 8,
    .data	= data_2001_256_setC,
    .data_len	= sizeof(data_2001_256_setC),
    .hash	= hash_2001_256_setC,
    .signature	= signature_2001_256_setC,
    .pub_key	= pubkey_2001_256_setC,
};

static struct test_param tc_2012_256_a = {
    .param	= NID_id_tc26_gost_3410_2012_256_paramSetA,
    .len	= 256 / 8,
    .data	= data_tc26_gost_3410_12_256_setA,
    .data_len	= sizeof(data_tc26_gost_3410_12_256_setA),
    .hash	= hash_tc26_gost_3410_12_256_setA,
    .signature	= signature_tc26_gost_3410_12_256_setA,
    .pub_key	= pubkey_tc26_gost_3410_12_256_setA,
};

static struct test_param tc_2012_512_a = {
    .param	= NID_id_tc26_gost_3410_2012_512_paramSetA,
    .len	= 512 / 8,
    .data	= data_tc26_gost_3410_12_512_setA,
    .data_len	= sizeof(data_tc26_gost_3410_12_512_setA),
    .hash	= hash_tc26_gost_3410_12_512_setA,
    .signature	= signature_tc26_gost_3410_12_512_setA,
    .pub_key	= pubkey_tc26_gost_3410_12_512_setA,
};

static struct test_param tc_2012_512_b = {
    .param	= NID_id_tc26_gost_3410_2012_512_paramSetB,
    .len	= 512 / 8,
    .data	= data_tc26_gost_3410_12_512_setB,
    .data_len	= sizeof(data_tc26_gost_3410_12_512_setB),
    .hash	= hash_tc26_gost_3410_12_512_setB,
    .signature	= signature_tc26_gost_3410_12_512_setB,
    .pub_key	= pubkey_tc26_gost_3410_12_512_setB,
};

static struct test_param tc_2012_512_c = {
    .param	= NID_id_tc26_gost_3410_2012_512_paramSetC,
    .len	= 512 / 8,
    .data	= data_tc26_gost_3410_12_512_setC,
    .data_len	= sizeof(data_tc26_gost_3410_12_512_setC),
    .hash	= hash_tc26_gost_3410_12_512_setC,
    .signature	= signature_tc26_gost_3410_12_512_setC,
    .pub_key	= pubkey_tc26_gost_3410_12_512_setC,
};

static struct test_param *test_params[] = {
    &cp_2001_256_a,
    &cp_2001_256_b,
    &cp_2001_256_c,
    &tc_2012_256_a,
    &tc_2012_512_a,
    &tc_2012_512_b,
    &tc_2012_512_c,
    0,
};

/*
 * Test certificates provided by Infotecs for Basealt SPO to test
 * interop with open-source software.
 */

unsigned char short_cp_a_cer[] = {
    0x30, 0x82, 0x01, 0x57, 0x30, 0x82, 0x01, 0x04, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x01, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x61, 0x30, 0x1e, 0x17, 0x0d,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x5a, 0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x61, 0x30, 0x66, 0x30, 0x1f,
    0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x13,
    0x06, 0x07, 0x2a, 0x85, 0x03, 0x02, 0x02, 0x23, 0x01, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x02, 0x02, 0x03, 0x43, 0x00, 0x04, 0x40,
    0x89, 0xb1, 0x32, 0x31, 0xd0, 0x22, 0xe1, 0x04, 0xe3, 0x47, 0xaf, 0xea,
    0x7e, 0x0c, 0x7d, 0x3b, 0xae, 0xad, 0xc9, 0xe7, 0x01, 0xb2, 0x91, 0x54,
    0x17, 0x4b, 0x24, 0xb5, 0xf9, 0x79, 0xae, 0x43, 0x77, 0xc1, 0x8d, 0xf8,
    0x78, 0x96, 0x76, 0x3c, 0xa3, 0x93, 0x6b, 0x21, 0x8e, 0x09, 0xf5, 0x92,
    0x55, 0xdd, 0x89, 0x46, 0x9b, 0x9a, 0xb5, 0x98, 0xd6, 0x73, 0x03, 0x36,
    0xa4, 0x4e, 0x35, 0x08, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x85, 0x57, 0xfc, 0x0b, 0xc5,
    0x57, 0x51, 0x41, 0xa9, 0xcd, 0xd0, 0x71, 0x92, 0x9b, 0x90, 0x57, 0x76,
    0x9f, 0x7b, 0xb9, 0x01, 0xc0, 0x13, 0x07, 0xbe, 0x40, 0x86, 0x96, 0x05,
    0xfe, 0x35, 0xc2, 0xe4, 0xa3, 0xb4, 0xe5, 0x3f, 0xff, 0x25, 0x95, 0x21,
    0x97, 0x14, 0x94, 0x03, 0x3a, 0x93, 0xdb, 0xec, 0xf1, 0xd5, 0x8b, 0xf8,
    0xcc, 0x85, 0xd4, 0xe3, 0x12, 0xea, 0x70, 0x38, 0xcf, 0x21, 0xd2
};

unsigned char short_cp_b_cer[] = {
    0x30, 0x82, 0x01, 0x57, 0x30, 0x82, 0x01, 0x04, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x02, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x62, 0x30, 0x1e, 0x17, 0x0d,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x5a, 0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x62, 0x30, 0x66, 0x30, 0x1f,
    0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x13,
    0x06, 0x07, 0x2a, 0x85, 0x03, 0x02, 0x02, 0x23, 0x02, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x02, 0x02, 0x03, 0x43, 0x00, 0x04, 0x40,
    0x07, 0x6b, 0xce, 0x22, 0x95, 0x22, 0xa4, 0x6e, 0xdd, 0x7c, 0x23, 0x24,
    0x37, 0xf5, 0x9c, 0x8c, 0x7b, 0xb0, 0x2b, 0x6f, 0xea, 0xa0, 0x2a, 0xe6,
    0x1f, 0x3d, 0x42, 0x26, 0xaa, 0xee, 0x64, 0x08, 0xb5, 0x19, 0x2b, 0xf1,
    0x70, 0xeb, 0x98, 0x98, 0x6d, 0xce, 0xcc, 0x8c, 0xc6, 0x2f, 0xfb, 0x6f,
    0xc0, 0x5c, 0x59, 0xf3, 0xcd, 0x89, 0x32, 0x80, 0xd0, 0x32, 0xfc, 0xa7,
    0x78, 0x80, 0xfd, 0x76, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x1e, 0x42, 0xcb, 0x48, 0x23,
    0x5b, 0x2b, 0xb0, 0x56, 0xd2, 0xd1, 0x58, 0xb0, 0x9c, 0xee, 0x6e, 0xb4,
    0x5f, 0x5d, 0x48, 0x24, 0x07, 0x15, 0x8a, 0xf7, 0x0d, 0xb3, 0x97, 0x86,
    0x55, 0xb3, 0xed, 0x57, 0x7b, 0xf2, 0x67, 0xef, 0x97, 0xd8, 0x8f, 0xc6,
    0xb7, 0xcd, 0x98, 0x51, 0x48, 0xc5, 0x76, 0xf1, 0x48, 0x17, 0x1e, 0xcd,
    0x48, 0x4f, 0xd8, 0xe8, 0x5d, 0x2c, 0xa8, 0xc0, 0x45, 0xdf, 0x2d
};

unsigned char short_cp_c_cer[] = {
    0x30, 0x82, 0x01, 0x57, 0x30, 0x82, 0x01, 0x04, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x03, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x63, 0x30, 0x1e, 0x17, 0x0d,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x5a, 0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14,
    0x00, 0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f,
    0x00, 0x63, 0x00, 0x70, 0x00, 0x5f, 0x00, 0x63, 0x30, 0x66, 0x30, 0x1f,
    0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x13,
    0x06, 0x07, 0x2a, 0x85, 0x03, 0x02, 0x02, 0x23, 0x03, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x02, 0x02, 0x03, 0x43, 0x00, 0x04, 0x40,
    0xe0, 0x8c, 0xa8, 0x1b, 0x04, 0x4a, 0x49, 0x17, 0x41, 0x58, 0x26, 0x78,
    0xf4, 0x0f, 0x6e, 0x1c, 0x9d, 0x7c, 0xf0, 0xc7, 0x2b, 0xcf, 0x94, 0xe4,
    0xa8, 0x15, 0x5d, 0xb1, 0xaf, 0x7a, 0x8b, 0x2e, 0x10, 0x8c, 0xe8, 0x66,
    0x8d, 0xa9, 0xc6, 0x9b, 0x74, 0xb4, 0xb6, 0x45, 0xd2, 0xaa, 0xab, 0x56,
    0xb6, 0x04, 0x22, 0x90, 0x56, 0xdf, 0xbb, 0xc2, 0xc0, 0x8a, 0x91, 0x88,
    0x4d, 0x36, 0x4e, 0x84, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x34, 0x87, 0xfa, 0x75, 0x42,
    0xbd, 0x0e, 0x1c, 0x37, 0x39, 0xef, 0x94, 0xaf, 0x6f, 0x47, 0xa9, 0x4e,
    0x2b, 0x58, 0x4e, 0x5b, 0x65, 0x34, 0xcb, 0x64, 0xc9, 0x72, 0xaf, 0xb7,
    0xbe, 0x63, 0x6a, 0x51, 0x9a, 0x0b, 0xac, 0x09, 0xc3, 0x9e, 0xb9, 0xbe,
    0x06, 0x5f, 0xe5, 0x30, 0x66, 0x20, 0xa2, 0x61, 0xe7, 0x93, 0x13, 0x6e,
    0xca, 0x0c, 0xbc, 0x3f, 0x49, 0x9f, 0x37, 0xef, 0x5d, 0xed, 0x80
};

unsigned char short_tc_a_cer[] = {
    0x30, 0x82, 0x01, 0x4e, 0x30, 0x81, 0xfc, 0xa0, 0x03, 0x02, 0x01, 0x02,
    0x02, 0x01, 0x06, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01,
    0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x61, 0x30, 0x1e, 0x17, 0x0d, 0x30,
    0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a,
    0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x61, 0x30, 0x5e, 0x30, 0x17, 0x06,
    0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x0b, 0x06,
    0x09, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x01, 0x01, 0x03, 0x43,
    0x00, 0x04, 0x40, 0xe4, 0x63, 0xf2, 0xff, 0x74, 0x1c, 0x44, 0xb2, 0xc9,
    0x3c, 0x9a, 0xc8, 0x04, 0xb6, 0xc4, 0x14, 0x43, 0x60, 0xf2, 0x42, 0x53,
    0xd3, 0x1a, 0x29, 0xb1, 0xbd, 0x03, 0xf8, 0xbc, 0x5e, 0x14, 0x8d, 0x1a,
    0x86, 0xc3, 0xb0, 0x9f, 0x4f, 0x05, 0x24, 0x20, 0xf0, 0x01, 0x9d, 0x86,
    0xa1, 0x12, 0x93, 0x9d, 0xe8, 0xb1, 0x2a, 0xc4, 0x65, 0x9f, 0xc9, 0xb8,
    0x07, 0x3a, 0x14, 0x88, 0xdc, 0xd7, 0x03, 0x30, 0x0a, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x30, 0x0f,
    0x56, 0x22, 0x1f, 0x69, 0x43, 0x54, 0x6c, 0x7c, 0x11, 0x43, 0xf8, 0x5a,
    0xa0, 0xf0, 0x33, 0x61, 0x07, 0x9b, 0x1c, 0xa5, 0xf1, 0xaa, 0x61, 0x4c,
    0xe9, 0x8b, 0x3b, 0x82, 0x0c, 0xb2, 0x2f, 0x8b, 0xb5, 0xd6, 0x38, 0xb1,
    0x92, 0xb1, 0xc3, 0x74, 0x9a, 0x7c, 0x55, 0xb9, 0x5c, 0xfb, 0x8d, 0x1b,
    0x00, 0x85, 0xad, 0x70, 0x9b, 0x6f, 0xb3, 0x32, 0x53, 0xc1, 0x85, 0x4b,
    0xd0, 0xe4
};

unsigned char short_tc_b_cer[] = {
    0x30, 0x82, 0x01, 0x4e, 0x30, 0x81, 0xfc, 0xa0, 0x03, 0x02, 0x01, 0x02,
    0x02, 0x01, 0x07, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01,
    0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x62, 0x30, 0x1e, 0x17, 0x0d, 0x30,
    0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a,
    0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x62, 0x30, 0x5e, 0x30, 0x17, 0x06,
    0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x0b, 0x06,
    0x09, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x01, 0x02, 0x03, 0x43,
    0x00, 0x04, 0x40, 0x36, 0xda, 0xe1, 0x60, 0x8c, 0x63, 0x67, 0xe1, 0xa7,
    0x36, 0x2f, 0x40, 0x1b, 0x38, 0x18, 0x59, 0xee, 0x40, 0x3b, 0xed, 0x2d,
    0x21, 0x97, 0x28, 0xa1, 0x68, 0x4e, 0xc0, 0xf2, 0x0a, 0xae, 0x67, 0x63,
    0xe2, 0x22, 0x17, 0x34, 0x44, 0xd0, 0x67, 0x3f, 0x67, 0x02, 0x61, 0x28,
    0xcb, 0x6d, 0xb0, 0x92, 0x01, 0x39, 0xba, 0xa5, 0x90, 0x66, 0x39, 0x23,
    0xcf, 0xb9, 0x37, 0x13, 0xcf, 0xfe, 0x3c, 0x30, 0x0a, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0xff, 0x80,
    0x61, 0xbf, 0x96, 0xef, 0x0e, 0x3d, 0x09, 0x15, 0xcf, 0xf3, 0x71, 0x03,
    0x8d, 0x5d, 0xa3, 0x68, 0x45, 0x10, 0x19, 0x6f, 0x0e, 0x08, 0xa0, 0xc3,
    0xc8, 0xc6, 0x97, 0x81, 0x40, 0x2f, 0xd8, 0xeb, 0xe3, 0xc0, 0x3d, 0xac,
    0xbf, 0xb7, 0x8f, 0x27, 0xc3, 0xd0, 0x57, 0x49, 0x69, 0x35, 0x3c, 0xab,
    0x49, 0xbc, 0xef, 0x3a, 0x0c, 0x0c, 0xc3, 0x92, 0xf8, 0x74, 0xba, 0xaf,
    0xa8, 0x15
};

unsigned char short_tc_c_cer[] = {
    0x30, 0x82, 0x01, 0x4e, 0x30, 0x81, 0xfc, 0xa0, 0x03, 0x02, 0x01, 0x02,
    0x02, 0x01, 0x08, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01,
    0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x63, 0x30, 0x1e, 0x17, 0x0d, 0x30,
    0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a,
    0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x63, 0x30, 0x5e, 0x30, 0x17, 0x06,
    0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x0b, 0x06,
    0x09, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x01, 0x03, 0x03, 0x43,
    0x00, 0x04, 0x40, 0xb7, 0x10, 0xef, 0x13, 0x4b, 0x97, 0x0e, 0x19, 0x9b,
    0x20, 0x3d, 0xb6, 0x7a, 0x3f, 0xa6, 0x3b, 0x70, 0xe1, 0xc1, 0x97, 0x1c,
    0xe2, 0x9f, 0xb8, 0x09, 0x1f, 0xb6, 0xd6, 0x69, 0x01, 0x4d, 0x18, 0xaf,
    0xde, 0xb4, 0xe3, 0xda, 0xab, 0x7c, 0xc8, 0x74, 0xd0, 0x59, 0x8b, 0x19,
    0xdc, 0x63, 0x04, 0x36, 0x64, 0x0f, 0xc2, 0x1e, 0xdb, 0x0f, 0xc6, 0x0b,
    0x2e, 0x3c, 0xbf, 0x5a, 0x68, 0x71, 0x5b, 0x30, 0x0a, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x0c, 0xe1,
    0xa3, 0x47, 0xe1, 0xa8, 0xfb, 0x21, 0xde, 0x98, 0x52, 0x69, 0x9e, 0x03,
    0x5f, 0x0d, 0xbc, 0x37, 0xae, 0x86, 0xcb, 0x1c, 0x36, 0x6f, 0x97, 0x23,
    0x37, 0x17, 0xac, 0x5f, 0x9a, 0x25, 0x56, 0x7c, 0xbd, 0x60, 0x60, 0xc1,
    0xcd, 0xe1, 0x58, 0xf8, 0x49, 0x9e, 0x41, 0xab, 0xe5, 0x9e, 0xcd, 0xed,
    0xf9, 0x74, 0x89, 0xc5, 0x1a, 0xc7, 0xa3, 0x88, 0x59, 0xcc, 0x79, 0x79,
    0x9a, 0x7f
};

unsigned char short_tc_d_cer[] = {
    0x30, 0x82, 0x01, 0x4e, 0x30, 0x81, 0xfc, 0xa0, 0x03, 0x02, 0x01, 0x02,
    0x02, 0x01, 0x09, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01,
    0x01, 0x03, 0x02, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x64, 0x30, 0x1e, 0x17, 0x0d, 0x30,
    0x31, 0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a,
    0x17, 0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x30, 0x5a, 0x30, 0x32, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55,
    0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73,
    0x31, 0x1d, 0x30, 0x1b, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x14, 0x00,
    0x73, 0x00, 0x68, 0x00, 0x6f, 0x00, 0x72, 0x00, 0x74, 0x00, 0x5f, 0x00,
    0x74, 0x00, 0x63, 0x00, 0x5f, 0x00, 0x64, 0x30, 0x5e, 0x30, 0x17, 0x06,
    0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x01, 0x30, 0x0b, 0x06,
    0x09, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x01, 0x04, 0x03, 0x43,
    0x00, 0x04, 0x40, 0x15, 0x8e, 0xa3, 0x12, 0x7a, 0xb2, 0xaa, 0x91, 0x45,
    0xd2, 0x85, 0xfe, 0xbb, 0xcd, 0x58, 0xce, 0xd0, 0x0a, 0x99, 0x2c, 0x5d,
    0x85, 0x88, 0x70, 0xb9, 0x3e, 0x51, 0x20, 0xca, 0x17, 0x67, 0x03, 0xa0,
    0xa6, 0x28, 0x71, 0x0d, 0xa7, 0x1f, 0x32, 0xce, 0x14, 0x56, 0xf2, 0x4e,
    0xf7, 0x66, 0x67, 0x78, 0xaf, 0x41, 0x41, 0x66, 0xf2, 0xc0, 0x61, 0xda,
    0x8a, 0x35, 0x52, 0xf0, 0x81, 0x8d, 0x4a, 0x30, 0x0a, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x02, 0x03, 0x41, 0x00, 0x12, 0xf8,
    0x5e, 0x95, 0x57, 0xce, 0xee, 0xb0, 0x32, 0xf6, 0x96, 0x3c, 0x44, 0x01,
    0x86, 0x07, 0x1f, 0x31, 0x7c, 0xcc, 0xa0, 0x30, 0x25, 0xa6, 0x69, 0x89,
    0x2c, 0xde, 0xd4, 0x32, 0x06, 0x81, 0x75, 0x43, 0xe7, 0xca, 0xce, 0x1c,
    0x3b, 0xa5, 0x43, 0xde, 0x44, 0x3e, 0x54, 0x35, 0x39, 0x3a, 0x80, 0x4b,
    0x4f, 0xdb, 0x90, 0x09, 0x31, 0xa5, 0x8f, 0xf6, 0x66, 0xb6, 0xf1, 0x84,
    0x9c, 0x82
};

unsigned char long_tc_a_cer[] = {
    0x30, 0x82, 0x01, 0xcf, 0x30, 0x82, 0x01, 0x3b, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x0a, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x03, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12,
    0x00, 0x6c, 0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74,
    0x00, 0x63, 0x00, 0x5f, 0x00, 0x61, 0x30, 0x1e, 0x17, 0x0d, 0x30, 0x31,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a, 0x17,
    0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x5a, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55, 0x04,
    0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73, 0x31,
    0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12, 0x00, 0x6c,
    0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74, 0x00, 0x63,
    0x00, 0x5f, 0x00, 0x61, 0x30, 0x81, 0xa0, 0x30, 0x17, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x02, 0x30, 0x0b, 0x06, 0x09, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x02, 0x01, 0x03, 0x81, 0x84, 0x00,
    0x04, 0x81, 0x80, 0x6b, 0xcb, 0x7e, 0xd5, 0x29, 0x12, 0x3b, 0xda, 0x9e,
    0x97, 0x11, 0x66, 0x56, 0xab, 0x76, 0x1d, 0xd8, 0x4b, 0x88, 0x96, 0x10,
    0xbf, 0x42, 0xae, 0x08, 0x9e, 0xcc, 0xcc, 0xdf, 0xc9, 0x17, 0xe8, 0x13,
    0x70, 0x38, 0x31, 0x61, 0x3f, 0xde, 0xff, 0x9a, 0x64, 0x92, 0xe1, 0xc1,
    0x80, 0xef, 0x65, 0xe8, 0xe4, 0xc2, 0xd8, 0xb1, 0xaa, 0x2a, 0xa8, 0x71,
    0xaf, 0x56, 0x07, 0xd0, 0x71, 0x21, 0x3b, 0xb8, 0x57, 0x23, 0x90, 0x0d,
    0x6d, 0x6c, 0x46, 0x1e, 0x2a, 0xa6, 0xc5, 0xb8, 0x9d, 0x49, 0xe2, 0x50,
    0x2e, 0x8d, 0xaa, 0xb8, 0x68, 0x30, 0xbd, 0x78, 0x9c, 0xa3, 0x84, 0x9a,
    0x7e, 0x77, 0xd7, 0xa9, 0xf9, 0x29, 0xd9, 0xe5, 0xc5, 0xb3, 0x10, 0xc6,
    0x0b, 0x7b, 0x23, 0x7d, 0xa6, 0x9e, 0x2b, 0xa5, 0x33, 0x46, 0xe0, 0x75,
    0x7b, 0x4b, 0xac, 0xca, 0x0f, 0x75, 0xe9, 0xc1, 0xd3, 0xff, 0xb4, 0x30,
    0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x03, 0x03,
    0x81, 0x81, 0x00, 0x19, 0x47, 0x1c, 0xb7, 0x65, 0xff, 0x15, 0x60, 0x39,
    0x8c, 0xfc, 0x8d, 0xfc, 0xaa, 0xef, 0x20, 0x0c, 0x7c, 0xf8, 0xb7, 0xfe,
    0x17, 0xcf, 0xa0, 0x3a, 0x50, 0x47, 0x71, 0x5e, 0xa8, 0x54, 0xa2, 0xec,
    0xbd, 0xc3, 0xbe, 0xdb, 0x42, 0x52, 0xe3, 0xb8, 0xb9, 0x91, 0x5d, 0x0b,
    0xbc, 0xc7, 0x91, 0xe0, 0xab, 0x41, 0x7e, 0x6c, 0x6c, 0x87, 0x1f, 0x34,
    0xb6, 0x96, 0xe2, 0xc2, 0xe3, 0x2b, 0x3b, 0xd8, 0x60, 0x82, 0x0d, 0x9b,
    0x9d, 0xf8, 0x4a, 0x06, 0x4a, 0x99, 0xc5, 0x33, 0x87, 0x1e, 0x21, 0x82,
    0xed, 0x34, 0x8e, 0xf5, 0xe7, 0x86, 0x7f, 0xec, 0x43, 0xff, 0x73, 0xd4,
    0xc2, 0x67, 0xfe, 0xd8, 0x51, 0x99, 0x60, 0x76, 0x6f, 0xab, 0x85, 0x45,
    0x92, 0xe9, 0x5c, 0xc5, 0x7d, 0x7f, 0x47, 0xf8, 0x46, 0xbe, 0x57, 0xdc,
    0x16, 0x90, 0x50, 0x90, 0x4c, 0x88, 0x94, 0x69, 0x4e, 0x07, 0x9b
};

unsigned char long_tc_b_cer[] = {
    0x30, 0x82, 0x01, 0xcf, 0x30, 0x82, 0x01, 0x3b, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x0b, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x03, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12,
    0x00, 0x6c, 0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74,
    0x00, 0x63, 0x00, 0x5f, 0x00, 0x62, 0x30, 0x1e, 0x17, 0x0d, 0x30, 0x31,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a, 0x17,
    0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x5a, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55, 0x04,
    0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73, 0x31,
    0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12, 0x00, 0x6c,
    0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74, 0x00, 0x63,
    0x00, 0x5f, 0x00, 0x62, 0x30, 0x81, 0xa0, 0x30, 0x17, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x02, 0x30, 0x0b, 0x06, 0x09, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x02, 0x02, 0x03, 0x81, 0x84, 0x00,
    0x04, 0x81, 0x80, 0x9e, 0x7e, 0xa3, 0x49, 0x56, 0xbe, 0xe3, 0xf6, 0x43,
    0x0d, 0x1d, 0xa0, 0xce, 0x43, 0x8b, 0x41, 0xec, 0x05, 0x0c, 0x06, 0x89,
    0x19, 0xc0, 0x8d, 0xe8, 0xbc, 0xb1, 0xa1, 0x32, 0xe1, 0x68, 0x69, 0x0e,
    0x1f, 0x2c, 0x77, 0x53, 0xc0, 0xc6, 0x9a, 0xca, 0x6a, 0xc5, 0x25, 0xa6,
    0xf1, 0x15, 0xab, 0x6c, 0x1f, 0x38, 0xcd, 0xb0, 0x2b, 0xb9, 0x69, 0x67,
    0x39, 0xb7, 0x90, 0x6b, 0xd6, 0x14, 0x38, 0x3e, 0x82, 0x7e, 0x53, 0xc1,
    0x89, 0xd3, 0xea, 0x46, 0xe5, 0x5f, 0x72, 0x54, 0x70, 0x02, 0x26, 0x0c,
    0x8c, 0xc9, 0xd1, 0x4c, 0xb9, 0xfd, 0xd2, 0x4d, 0x5e, 0x53, 0x6b, 0xa1,
    0x56, 0xf9, 0xb7, 0xfd, 0x0f, 0x8f, 0x0c, 0x74, 0x66, 0xa4, 0x78, 0xf8,
    0x0a, 0xcb, 0xe9, 0xc4, 0x5b, 0xf9, 0xd9, 0xb0, 0xdf, 0xc0, 0xc5, 0xa2,
    0x2c, 0xd9, 0x27, 0x86, 0x99, 0xf1, 0x58, 0xaf, 0x46, 0xe1, 0x50, 0x30,
    0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x03, 0x03,
    0x81, 0x81, 0x00, 0x05, 0x41, 0xea, 0x4e, 0x1d, 0x67, 0xc8, 0xe2, 0x91,
    0xd2, 0x0c, 0xc6, 0xa5, 0x58, 0x32, 0xd6, 0xe5, 0x3f, 0x14, 0xe6, 0x97,
    0x08, 0x7c, 0x99, 0x88, 0xf6, 0x10, 0xc7, 0xe8, 0x87, 0x83, 0x68, 0x24,
    0xc7, 0x24, 0xc1, 0x69, 0x8c, 0x69, 0xe0, 0x40, 0x61, 0xd7, 0x64, 0xea,
    0xa7, 0xa3, 0x0c, 0x18, 0x8d, 0xce, 0x48, 0x0e, 0x6e, 0x58, 0xa9, 0x9a,
    0x57, 0xa7, 0x4e, 0xe9, 0x2c, 0x6b, 0x7a, 0x1a, 0xd6, 0x72, 0x56, 0x5a,
    0x04, 0x0c, 0x57, 0xb6, 0x42, 0x72, 0x89, 0x03, 0x50, 0xd9, 0x63, 0xb7,
    0x01, 0xc4, 0x07, 0x47, 0x80, 0xe1, 0xb1, 0xdd, 0x25, 0xf4, 0x2a, 0x93,
    0xff, 0x5e, 0x07, 0x19, 0xc3, 0xe8, 0xb4, 0xd1, 0x6c, 0xad, 0x6c, 0xa3,
    0x25, 0xd4, 0x3d, 0xcb, 0x9c, 0xc7, 0x87, 0x14, 0xe0, 0x7d, 0x31, 0x9d,
    0xb6, 0x64, 0x94, 0xd1, 0x66, 0x3b, 0xb0, 0xe6, 0x3a, 0xa2, 0xa1
};

unsigned char long_tc_c_cer[] = {
    0x30, 0x82, 0x01, 0xcf, 0x30, 0x82, 0x01, 0x3b, 0xa0, 0x03, 0x02, 0x01,
    0x02, 0x02, 0x01, 0x0c, 0x30, 0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07,
    0x01, 0x01, 0x03, 0x03, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03,
    0x55, 0x04, 0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63,
    0x73, 0x31, 0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12,
    0x00, 0x6c, 0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74,
    0x00, 0x63, 0x00, 0x5f, 0x00, 0x63, 0x30, 0x1e, 0x17, 0x0d, 0x30, 0x31,
    0x30, 0x31, 0x30, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x5a, 0x17,
    0x0d, 0x34, 0x39, 0x31, 0x32, 0x33, 0x31, 0x30, 0x30, 0x30, 0x30, 0x30,
    0x30, 0x5a, 0x30, 0x30, 0x31, 0x11, 0x30, 0x0f, 0x06, 0x03, 0x55, 0x04,
    0x0a, 0x13, 0x08, 0x49, 0x6e, 0x66, 0x6f, 0x74, 0x65, 0x63, 0x73, 0x31,
    0x1b, 0x30, 0x19, 0x06, 0x03, 0x55, 0x04, 0x03, 0x1e, 0x12, 0x00, 0x6c,
    0x00, 0x6f, 0x00, 0x6e, 0x00, 0x67, 0x00, 0x5f, 0x00, 0x74, 0x00, 0x63,
    0x00, 0x5f, 0x00, 0x63, 0x30, 0x81, 0xa0, 0x30, 0x17, 0x06, 0x08, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x01, 0x01, 0x02, 0x30, 0x0b, 0x06, 0x09, 0x2a,
    0x85, 0x03, 0x07, 0x01, 0x02, 0x01, 0x02, 0x03, 0x03, 0x81, 0x84, 0x00,
    0x04, 0x81, 0x80, 0x27, 0xb1, 0xd2, 0x0d, 0x5e, 0x13, 0x65, 0x25, 0x72,
    0x48, 0xe2, 0x84, 0xc8, 0xbc, 0x49, 0xf9, 0x72, 0x97, 0x16, 0x72, 0xc1,
    0xb1, 0x38, 0xf4, 0xae, 0x6c, 0xe6, 0xb5, 0xf4, 0xa9, 0x20, 0xc8, 0xdc,
    0x7f, 0x42, 0x95, 0x45, 0xf2, 0x1c, 0xbd, 0x13, 0xcf, 0x3b, 0x46, 0xd7,
    0x1e, 0x13, 0xd2, 0xa9, 0x59, 0x08, 0x4b, 0x24, 0xc6, 0x7b, 0xe2, 0x07,
    0x66, 0x8f, 0x5b, 0xe2, 0x7e, 0x07, 0xda, 0x7e, 0xb9, 0x95, 0xfa, 0x65,
    0xd9, 0x6b, 0x1f, 0x9d, 0x19, 0x93, 0xcc, 0xba, 0x8e, 0x85, 0xb7, 0x29,
    0xd3, 0x59, 0x4e, 0x4d, 0x3e, 0xff, 0xbe, 0x48, 0x29, 0x30, 0x7b, 0x72,
    0x9f, 0xb0, 0xf2, 0xf0, 0x0d, 0xaf, 0x6d, 0x91, 0xde, 0x10, 0xed, 0x89,
    0x84, 0x2f, 0xd3, 0xce, 0x20, 0x49, 0xf8, 0x53, 0xce, 0x58, 0xe0, 0x53,
    0xa4, 0x7e, 0x13, 0xa2, 0xac, 0xb2, 0x3c, 0x7b, 0x00, 0x45, 0xad, 0x30,
    0x0a, 0x06, 0x08, 0x2a, 0x85, 0x03, 0x07, 0x01, 0x01, 0x03, 0x03, 0x03,
    0x81, 0x81, 0x00, 0x2a, 0x74, 0x42, 0xd6, 0x03, 0x58, 0x49, 0x0a, 0x80,
    0x4b, 0xaf, 0x0a, 0x1e, 0x59, 0x09, 0x00, 0x53, 0x5e, 0xba, 0x3a, 0x52,
    0x06, 0x18, 0xaf, 0x17, 0x25, 0xb0, 0x5c, 0x45, 0x39, 0xea, 0xc2, 0xa0,
    0x46, 0xb4, 0xbf, 0x20, 0xaf, 0xb2, 0xd4, 0x6c, 0x1d, 0x24, 0xd8, 0xcc,
    0x7a, 0x10, 0x46, 0x69, 0xb2, 0xe6, 0x0f, 0x55, 0xc2, 0x56, 0x4b, 0x98,
    0x65, 0xcc, 0xad, 0x81, 0x6d, 0x8f, 0x76, 0x3e, 0x7c, 0x05, 0x60, 0x4d,
    0xce, 0xec, 0xb4, 0x9c, 0xfc, 0x4e, 0x2d, 0x03, 0xc0, 0x45, 0xff, 0x4e,
    0x75, 0x12, 0x9a, 0x29, 0x3f, 0xe7, 0x5a, 0x90, 0xe6, 0x42, 0x82, 0x2a,
    0xc2, 0xbf, 0x65, 0xf5, 0xe3, 0x10, 0xc6, 0x0b, 0xf3, 0x09, 0xaa, 0x66,
    0xf9, 0xd7, 0x73, 0x8b, 0x28, 0x9c, 0x87, 0xd8, 0x35, 0x25, 0x8a, 0xd3,
    0x27, 0x08, 0x8c, 0xa9, 0x94, 0xeb, 0x34, 0xb3, 0x9b, 0x5a, 0xbe
};
/* end */

#define D(y) { .name = #y, .cert = y, .len = sizeof(y) }
static struct test_cert {
    const char *name;
    const unsigned char *cert;
    int len;
} test_certs[] = {
    D(short_cp_a_cer),
    D(short_cp_b_cer),
    D(short_cp_c_cer),
    D(short_tc_a_cer),
    D(short_tc_b_cer),
    D(short_tc_c_cer),
    D(short_tc_d_cer),
    D(long_tc_a_cer),
    D(long_tc_b_cer),
    D(long_tc_c_cer),
    {0}
};
#undef D

static void hexdump(const void *ptr, size_t len)
{
    const unsigned char *p = ptr;
    size_t i, j;

    for (i = 0; i < len; i += j) {
	for (j = 0; j < 16 && i + j < len; j++)
	    printf("%s %02x", j? "" : "\n", p[i + j]);
    }
    printf("\n");
}

static void print_test_result(int err)
{
    if (err == 1)
	printf(cGREEN "correct" cNORM "\n");
    else if (err == 0)
	printf(cRED "incorrect" cNORM "\n");
    else
	ERR_print_errors_fp(stderr);
}

/* copy-paste from crypto/crmf/crmf_lib.c */
static int X509_PUBKEY_cmp(X509_PUBKEY *a, X509_PUBKEY *b)
{
    X509_ALGOR *algA = NULL, *algB = NULL;
    int res = 0;

    if (a == b)
	return 0;
    if (a == NULL || !X509_PUBKEY_get0_param(NULL, NULL, NULL, &algA, a)
	|| algA == NULL)
	return -1;
    if (b == NULL || !X509_PUBKEY_get0_param(NULL, NULL, NULL, &algB, b)
	|| algB == NULL)
	return 1;
    if ((res = X509_ALGOR_cmp(algA, algB)) != 0)
	return res;
    return !EVP_PKEY_cmp(X509_PUBKEY_get0(a), X509_PUBKEY_get0(b));
}

static int test_cert(struct test_cert *tc)
{
    int ret = 0, err;
    X509 *x;
    const unsigned char *p;

    printf(cBLUE "Test %s (it): " cNORM, tc->name);
    p = tc->cert;
    T(x = d2i_X509(NULL, &p, tc->len));

    X509_PUBKEY *xk;
    TE(xk = X509_get_X509_PUBKEY(x));

    /* Output algo and parameters. */
    X509_ALGOR *palg;
    ASN1_OBJECT *ppkalg;
    T(X509_PUBKEY_get0_param(&ppkalg, NULL, 0, &palg, xk));
    int algo_nid = OBJ_obj2nid(ppkalg);
    printf(" (algo %s)", OBJ_nid2sn(algo_nid));
    int pptype;
    ASN1_STRING *pval = NULL;
    X509_ALGOR_get0(NULL, &pptype, (void *)&pval, palg);

    /* Low level access to parameters in case X509_get0_pubkey does not work. */
    T(pptype == V_ASN1_SEQUENCE);
    STACK_OF(ASN1_TYPE) *seq;
    p = pval->data;
    T(seq = d2i_ASN1_SEQUENCE_ANY(NULL, &p, pval->length));
    ASN1_TYPE *p1; /* First parameter is curve OID. */
    T(p1 = sk_ASN1_TYPE_value(seq, 0));
    int param_nid = OBJ_obj2nid((ASN1_OBJECT *)(p1->value.ptr));
    printf(" (curve %s)\n", OBJ_nid2sn(param_nid));
    sk_ASN1_TYPE_pop_free(seq, ASN1_TYPE_free);

    /*
     * Conversion tests.
     */
    /* Convert cert to DER and back. */
    BIO *bp;
    T(bp = BIO_new(BIO_s_mem()));
    T(i2d_X509_bio(bp, x));
    X509 *y = NULL;
    T(d2i_X509_bio(bp, &y));
    err = X509_cmp(x, y);
    printf("  d2i_X509_bio\t\t\t");
    print_test_result(!err);
    ret |= err;
    X509_free(y);

    /* Convert cert to PEM and back. */
    y = NULL;
    T(PEM_write_bio_X509(bp, x));
    T(PEM_read_bio_X509(bp, &y, 0, NULL));
    err = X509_cmp(x, y);
    printf("  PEM_read_bio_X509\t\t");
    print_test_result(!err);
    ret |= err;
    X509_free(y);

    /* Convert public key to PEM and back. */
    T(BIO_reset(bp));
    T(PEM_write_bio_X509_PUBKEY(bp, xk));
    X509_PUBKEY *tk = NULL;
    T(PEM_read_bio_X509_PUBKEY(bp, &tk, NULL, NULL));
    err = X509_PUBKEY_cmp(xk, tk);
    X509_PUBKEY_free(tk);
    printf("  PEM_read_bio_X509_PUBKEY\t");
    print_test_result(!err);
    ret |= err;

    /* Convert public key to DER and back. */
    T(BIO_reset(bp));
    T(i2d_X509_PUBKEY_bio(bp, xk));
    tk = NULL;
    T(d2i_X509_PUBKEY_bio(bp, &tk));
    err = X509_PUBKEY_cmp(xk, tk);
    X509_PUBKEY_free(tk);
    printf("  d2i_X509_PUBKEY_bio\t\t");
    print_test_result(!err);
    ret |= err;
    BIO_free(bp);

    /*
     * Verify
     */
    printf("  X509_verify API\t\t");
    fflush(stdout);
    EVP_PKEY *pk;
    TE(pk = X509_get0_pubkey(x));
    /* Similar to: openssl verify -partial_chain -check_ss_sig ... */
    /* X509_verify uses EVP_DigestVerify internally */
    err = X509_verify(x, pk);
    print_test_result(err);
    ret |= err != 1;

    /* Verify manually. */
    const ASN1_BIT_STRING *signature;
    X509_get0_signature(&signature, NULL, x);
    unsigned char *tbs = NULL; /* signed part */
    int tbs_len;
    T((tbs_len = i2d_re_X509_tbs(x, &tbs)) > 0);
    int algnid, hash_nid, pknid;
    T(algnid = X509_get_signature_nid(x));
    T(OBJ_find_sigid_algs(algnid, &hash_nid, &pknid));

    printf("  EVP_Verify API\t\t");
    EVP_MD_CTX *md_ctx;
    T(md_ctx = EVP_MD_CTX_new());
    const EVP_MD *mdtype;
    T(mdtype = EVP_get_digestbynid(hash_nid));
    T(EVP_VerifyInit(md_ctx, mdtype));
    T(EVP_VerifyUpdate(md_ctx, tbs, tbs_len));
    err = EVP_VerifyFinal(md_ctx, signature->data, signature->length, pk);
    print_test_result(err);
    EVP_MD_CTX_free(md_ctx);
    ret |= err != 1;

    X509_free(x);
    OPENSSL_free(tbs);
    return ret;
}

/* Generate EC_KEY with proper parameters using temporary PKEYs.
 * This emulates fill_GOST_EC_params() call.
 */
static int EC_KEY_create(int type, int param_nid, EC_KEY *dst)
{
    EVP_PKEY *pkey;
    T(pkey = EVP_PKEY_new());
    T(EVP_PKEY_set_type(pkey, type));
    EVP_PKEY_CTX *ctx;
    T(ctx = EVP_PKEY_CTX_new(pkey, NULL));
    T(EVP_PKEY_paramgen_init(ctx));
    T(EVP_PKEY_CTX_ctrl(ctx, type, -1, EVP_PKEY_CTRL_GOST_PARAMSET, param_nid, NULL));
    EVP_PKEY *pkey2 = NULL;
    int err;
    TE((err = EVP_PKEY_paramgen(ctx, &pkey2)) == 1);
    T(EC_KEY_copy(dst, EVP_PKEY_get0(pkey2)));
    EVP_PKEY_CTX_free(ctx);
    EVP_PKEY_free(pkey);
    EVP_PKEY_free(pkey2);
    return err;
}

static int test_param(struct test_param *t)
{
    int ret = 0, err = 0;
    int type = 0;
    int hash_nid = 0;
    const char *sn = OBJ_nid2sn(t->param);

    printf(cBLUE "Test %s (cp):" cNORM "\n", sn);

    switch (t->len) {
	case 256 / 8:
	    type = NID_id_GostR3410_2012_256;
	    break;
	case 512 / 8:
	    type = NID_id_GostR3410_2012_512;
	    break;
	default:
	    OpenSSLDie(__FILE__, __LINE__, "invalid len");
    }
    switch (type) {
	case NID_id_GostR3410_2012_256:
	    hash_nid = NID_id_GostR3411_2012_256;
	    break;
	case NID_id_GostR3410_2012_512:
	    hash_nid = NID_id_GostR3411_2012_512;
	    break;
	default:
	    OpenSSLDie(__FILE__, __LINE__, "invalid type");
    }

    /* Manually construct public key */
    EC_KEY *ec;
    T(ec = EC_KEY_new());
    T(EC_KEY_create(type, t->param, ec));
    const EC_GROUP *group;
    T(group = EC_KEY_get0_group(ec));
    unsigned char *pub_key;
    T(pub_key = OPENSSL_malloc(t->len * 2));
    BUF_reverse(pub_key, t->pub_key, t->len * 2);
    BIGNUM *x, *y;
    T(y = BN_bin2bn(pub_key, t->len, NULL));
    T(x = BN_bin2bn(pub_key + t->len, t->len, NULL));
    OPENSSL_free(pub_key);
    EC_POINT *pk;
    T(pk = EC_POINT_new(group));
    T(EC_POINT_set_affine_coordinates(group, pk, x, y, NULL));
    BN_free(x);
    BN_free(y);
    T(EC_KEY_set_public_key(ec, pk));
    EC_POINT_free(pk);

    EVP_PKEY *pkey;
    T(pkey = EVP_PKEY_new());
    T(EVP_PKEY_assign(pkey, type, ec));
    int siglen = EVP_PKEY_size(pkey);

    /*
     * Verify
     */
    EVP_PKEY_CTX *ctx;
    T(ctx = EVP_PKEY_CTX_new(pkey, NULL));
    unsigned char *sig;
    T(sig = OPENSSL_malloc(siglen));
    /* Need to reverse provided signature for unknown reason,
     * contrary to how it goes into signature. */
    BUF_reverse(sig, t->signature, siglen);

    /* Verify using EVP_PKEY_verify API */
    printf("  EVP_PKEY_verify API\t\t");
    T(EVP_PKEY_verify_init(ctx));
    err = EVP_PKEY_verify(ctx, sig, siglen, t->hash, t->len);
    EVP_PKEY_CTX_free(ctx);
    print_test_result(err);
    ret |= err != 1;

    /* Verify using EVP_Verify API */
    if (t->data) {
	printf("  EVP_Verify API\t\t");
	EVP_MD_CTX *md_ctx;
	T(md_ctx = EVP_MD_CTX_new());
	const EVP_MD *mdtype;
	T(mdtype = EVP_get_digestbynid(hash_nid));
	T(EVP_VerifyInit(md_ctx, mdtype));
	/* Feed byte-by-byte. */
	size_t i;
	for (i = 0; i < t->data_len; i++)
	    T(EVP_VerifyUpdate(md_ctx, &t->data[i], 1));
	err = EVP_VerifyFinal(md_ctx, sig, siglen, pkey);
	print_test_result(err);
	EVP_MD_CTX_free(md_ctx);
	ret |= err != 1;
    }

    /* Verify using EVP_DigestVerifyInit API */
    if (t->data) {
	printf("  EVP_DigestVerifyInit API\t");
	EVP_MD_CTX *md_ctx;
	T(md_ctx = EVP_MD_CTX_new());
	const EVP_MD *mdtype;
	T(mdtype = EVP_get_digestbynid(hash_nid));
	T(EVP_DigestVerifyInit(md_ctx, NULL, mdtype, NULL, pkey));
	/* Verify in one step. */
	err = EVP_DigestVerify(md_ctx, sig, siglen, t->data, t->data_len);
	print_test_result(err);
	EVP_MD_CTX_free(md_ctx);
	ret |= err != 1;
    }

    OPENSSL_free(sig);
    EVP_PKEY_free(pkey);
    return ret;
}

int main(int argc, char **argv)
{
    int ret = 0;

    OPENSSL_add_all_algorithms_conf();

    struct test_param **tpp;
    for (tpp = test_params; *tpp; tpp++)
	ret |= test_param(*tpp);

    struct test_cert *tc;
    for (tc = test_certs; tc->cert; tc++)
	ret |= test_cert(tc);

    if (ret)
	printf(cDRED "= Some tests FAILED!" cNORM "\n");
    else
	printf(cDGREEN "= All tests passed!" cNORM "\n");
    return ret;
}
