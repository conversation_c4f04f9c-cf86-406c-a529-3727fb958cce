LIBS=../../libcrypto

$MODESASM=
IF[{- !$disabled{asm} -}]
  $MODESASM_x86=ghash-x86.S
  $MODESDEF_x86=GHASH_ASM
  $MODESASM_x86_64=ghash-x86_64.s aesni-gcm-x86_64.s aes-gcm-avx512.s
  $MODESDEF_x86_64=GHASH_ASM

  # ghash-ia64.s doesn't work on VMS
  IF[{- $config{target} !~ /^vms-/ -}]
    $MODESASM_ia64=ghash-ia64.s
    $MODESDEF_ia64=GHASH_ASM
  ENDIF

  $MODESASM_sparcv9=ghash-sparcv9.S
  $MODESDEF_sparcv9=GHASH_ASM

  $MODESASM_alpha=ghash-alpha.S
  $MODESDEF_alpha=GHASH_ASM

  $MODESASM_s390x=ghash-s390x.S
  $MODESDEF_s390x=GHASH_ASM

  $MODESASM_armv4=ghash-armv4.S ghashv8-armx.S
  $MODESDEF_armv4=GHASH_ASM
  $MODESASM_aarch64=ghashv8-armx.S aes-gcm-armv8_64.S aes-gcm-armv8-unroll8_64.S
  $MODESDEF_aarch64=

  $MODESASM_parisc11=ghash-parisc.s
  $MODESDEF_parisc11=GHASH_ASM
  $MODESASM_parisc20_64=$MODESASM_parisc11
  $MODESDEF_parisc20_64=$MODESDEF_parisc11

  $MODESASM_ppc32=ghashp8-ppc.s
  $MODESDEF_ppc32=
  $MODESASM_ppc64=$MODESASM_ppc32
  IF[{- $target{sys_id} ne "AIX" && $target{sys_id} ne "MACOSX" -}]
    $MODESASM_ppc64=$MODESASM_ppc32 aes-gcm-ppc.s
  ENDIF
  $MODESDEF_ppc64=$MODESDEF_ppc32

  $MODESASM_c64xplus=ghash-c64xplus.s
  $MODESDEF_c64xplus=GHASH_ASM

  $MODESASM_riscv64=ghash-riscv64.s
  $MODESDEF_riscv64=GHASH_ASM

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$MODESASM_{- $target{asm_arch} -}]
    $MODESASM=$MODESASM_{- $target{asm_arch} -}
    $MODESDEF=$MODESDEF_{- $target{asm_arch} -}
  ENDIF
ENDIF

$COMMON=cbc128.c ctr128.c cfb128.c ofb128.c gcm128.c ccm128.c xts128.c \
        wrap128.c $MODESASM
SOURCE[../../libcrypto]=$COMMON \
        cts128.c ocb128.c siv128.c
SOURCE[../../providers/libfips.a]=$COMMON

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules.
DEFINE[../../libcrypto]=$MODESDEF
DEFINE[../../providers/libfips.a]=$MODESDEF


INCLUDE[gcm128.o]=..

GENERATE[ghash-ia64.s]=asm/ghash-ia64.pl
GENERATE[ghash-x86.S]=asm/ghash-x86.pl
GENERATE[ghash-x86_64.s]=asm/ghash-x86_64.pl
GENERATE[aesni-gcm-x86_64.s]=asm/aesni-gcm-x86_64.pl
GENERATE[aes-gcm-avx512.s]=asm/aes-gcm-avx512.pl
GENERATE[ghash-sparcv9.S]=asm/ghash-sparcv9.pl
INCLUDE[ghash-sparcv9.o]=..
GENERATE[ghash-alpha.S]=asm/ghash-alpha.pl
GENERATE[ghash-parisc.s]=asm/ghash-parisc.pl
GENERATE[ghashp8-ppc.s]=asm/ghashp8-ppc.pl
GENERATE[aes-gcm-ppc.s]=asm/aes-gcm-ppc.pl
GENERATE[ghash-armv4.S]=asm/ghash-armv4.pl
INCLUDE[ghash-armv4.o]=..
GENERATE[ghashv8-armx.S]=asm/ghashv8-armx.pl
INCLUDE[ghashv8-armx.o]=..
GENERATE[aes-gcm-armv8_64.S]=asm/aes-gcm-armv8_64.pl
INCLUDE[aes-gcm-armv8_64.o]=..
GENERATE[aes-gcm-armv8-unroll8_64.S]=asm/aes-gcm-armv8-unroll8_64.pl
INCLUDE[aes-gcm-armv8-unroll8_64.o]=..
GENERATE[ghash-s390x.S]=asm/ghash-s390x.pl
INCLUDE[ghash-s390x.o]=..
GENERATE[ghash-c64xplus.S]=asm/ghash-c64xplus.pl
GENERATE[ghash-riscv64.s]=asm/ghash-riscv64.pl
