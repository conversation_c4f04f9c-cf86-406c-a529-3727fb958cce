// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#include "p2psocket.h"
#include "lenlog.h"

bool LLog::isinit_ = false;
#ifdef _WIN32
RotatingLogFile LLog::file_;
#endif
log_level LLog::log_level_ = LOG_NONE;
std::mutex LLog::mutex_;
#ifdef _WIN32
RotatingLogFile::RotatingLogFile() {}

RotatingLogFile::RotatingLogFile(const std::string& filename)
    : base_filename_(filename) {
  open_next_file();
}

RotatingLogFile::~RotatingLogFile() {
  if (file_.is_open()) {
    file_.close();
  }
}
#endif
