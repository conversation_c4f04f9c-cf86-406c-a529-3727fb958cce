=pod

=head1 NAME

OSSL_CMP_STATUSINFO_new,
OSSL_CMP_snprint_PKIStatusInfo,
OSSL_CMP_CTX_snprint_PKIStatus
- function(s) for managing the CMP PKIStatus

=head1 SYNOPSIS

 #include <openssl/cmp.h>

 OSSL_CMP_PKISI *OSSL_CMP_STATUSINFO_new(int status, int fail_info,
                                         const char *text);
 char *OSSL_CMP_snprint_PKIStatusInfo(const OSSL_CMP_PKISI *statusInfo,
                                      char *buf, size_t bufsize);
 char *OSSL_CMP_CTX_snprint_PKIStatus(const OSSL_CMP_CTX *ctx, char *buf,
                                      size_t bufsize);

=head1 DESCRIPTION

This is the PKIStatus API for using CMP (Certificate Management Protocol) with
OpenSSL.

OSSL_CMP_STATUSINFO_new() creates a new PKIStatusInfo structure
and fills in the given values.
It sets the status field to I<status>,
copies I<text> (unless it is NULL) to statusString,
and interprets I<fail_info> as bit pattern for the failInfo field.

OSSL_CMP_snprint_PKIStatusInfo() places a human-readable string
representing the given statusInfo
in the given buffer, with the given maximal length.

OSSL_CMP_CTX_snprint_PKIStatus() places a human-readable string
representing the PKIStatusInfo components of the CMP context I<ctx>
in the given buffer, with the given maximal length.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

OSSL_CMP_STATUSINFO_new()
returns a pointer to the structure on success, or NULL on error.

OSSL_CMP_snprint_PKIStatusInfo() and
OSSL_CMP_CTX_snprint_PKIStatus()
return a copy of the buffer pointer containing the string or NULL on error.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
