=pod

=head1 NAME

ossl_cmp_hdr_set_pvno,
ossl_cmp_hdr_get_pvno,
ossl_cmp_hdr_get_protection_nid,
ossl_cmp_hdr_get0_sendernonce,
ossl_cmp_general_name_is_NULL_DN,
ossl_cmp_hdr_set1_sender,
ossl_cmp_hdr_set1_recipient,
ossl_cmp_hdr_update_messagetime,
ossl_cmp_hdr_set1_senderKID,
ossl_cmp_hdr_push0_freeText,
ossl_cmp_hdr_push1_freeText,
ossl_cmp_hdr_generalinfo_item_push0,
ossl_cmp_hdr_generalinfo_items_push1,
ossl_cmp_hdr_set_implicitConfirm,
ossl_cmp_hdr_has_implicitConfirm,
ossl_cmp_hdr_set_transactionID,
ossl_cmp_hdr_init
- functions handling CMP message headers

=head1 SYNOPSIS

  #include "cmp_local.h"

  int ossl_cmp_hdr_set_pvno(OSSL_CMP_PKIHEADER *hdr, int pvno);
  int ossl_cmp_hdr_get_pvno(const OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_hdr_get_protection_nid(const OSSL_CMP_PKIHEADER *hdr);
  ASN1_OCTET_STRING
     *ossl_cmp_hdr_get0_sendernonce(const OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_general_name_is_NULL_DN(GENERAL_NAME *name);

  int ossl_cmp_hdr_set1_sender(OSSL_CMP_PKIHEADER *hdr, const X509_NAME *nm);
  int ossl_cmp_hdr_set1_recipient(OSSL_CMP_PKIHEADER *hdr, const X509_NAME *nm);
  int ossl_cmp_hdr_update_messagetime(OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_hdr_set1_senderKID(OSSL_CMP_PKIHEADER *hdr,
                                  const ASN1_OCTET_STRING *senderKID);
  int ossl_cmp_hdr_generalinfo_item_push0(OSSL_CMP_PKIHEADER *hdr,
                                          OSSL_CMP_ITAV *itav);
  int ossl_cmp_hdr_generalinfo_items_push1(OSSL_CMP_PKIHEADER *hdr,
                                           STACK_OF(OSSL_CMP_ITAV) *itavs);
  int ossl_cmp_hdr_push0_freeText(OSSL_CMP_PKIHEADER *hdr,
                                  ASN1_UTF8STRING *text);
  int ossl_cmp_hdr_push1_freeText(OSSL_CMP_PKIHEADER *hdr,
                                  ASN1_UTF8STRING *text);
  int ossl_cmp_hdr_set_implicitConfirm(OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_hdr_has_implicitConfirm(OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_hdr_set_transactionID(OSSL_CMP_CTX *ctx, OSSL_CMP_PKIHEADER *hdr);
  int ossl_cmp_hdr_init(OSSL_CMP_CTX *ctx, OSSL_CMP_PKIHEADER *hdr);

=head1 DESCRIPTION

ossl_cmp_hdr_set_pvno() sets hdr->pvno to the given B<pvno>.

ossl_cmp_hdr_get_pvno() returns the pvno of the given B<hdr> or -1 on error.

ossl_cmp_hdr_get_protection_nid returns the NID of the protection algorithm
in B<hdr> or NID_undef on error.

ossl_cmp_hdr_get0_sendernonce() returns the sender nonce of the given PKIHeader.

ossl_cmp_general_name_is_NULL_DN() determines if the given GENERAL_NAME
is the NULL-DN.

ossl_cmp_hdr_set1_sender() sets the sender field in the given PKIHeader
to the given X509 Name value, without consuming the pointer.

ossl_cmp_hdr_set1_recipient() sets the recipient field in the given
PKIHeader to the given X509 Name value, without consuming the pointer.
If B<nm> is NULL, recipient is set to the NULL DN (the empty list of strings).

ossl_cmp_hdr_update_messagetime() (re-)sets the messageTime to the current
system time. As written in RFC 4210, section 5.1.1:
The messageTime field contains the time at which the sender created the message.
This may be useful to allow end entities to correct/check their local time for
consistency with the time on a central system.

ossl_cmp_hdr_set1_senderKID() Sets hdr->senderKID to the given string.
In an PBMAC-protected IR this usually is a reference number issued by the CA,
else the subject key ID of the sender's protecting certificate.

ossl_cmp_hdr_push0_freeText() pushes an ASN1_UTF8STRING to
hdr->freeText and consumes the given pointer.

ossl_cmp_hdr_push1_freeText() pushes an ASN1_UTF8STRING to
hdr->freeText and does not consume the pointer.

ossl_cmp_hdr_generalinfo_item_push0() adds the given InfoTypeAndValue
item to the hdr->generalInfo stack. Consumes the B<itav> pointer.

ossl_cmp_hdr_generalinfo_items_push1() adds a copy of the B<itavs> stack to
the generalInfo field of PKIheader of the B<hdr>. Does not consume the B<itavs>
pointer.

ossl_cmp_hdr_set_implicitConfirm() sets implicitConfirm in the generalInfo field
of the PKIMessage header.

ossl_cmp_hdr_has_implicitConfirm() returns 1 if implicitConfirm is
set int generalInfo field of the given PKIMessage header, 0 if not.

ossl_cmp_hdr_set_transactionID() sets the B<transactionID> field in C<hdr>.
In case ctx->transactionID is NULL, it starts a new transaction
by creating and storing a new random valuee with 128 bits length.

ossl_cmp_hdr_init() initializes a PKIHeader structure based on the
values in the given OSSL_CMP_CTX structure.
This starts a new transaction in case ctx->transactionID is NULL.
The sender name is copied from the subject of the client cert, if any,
or else from the subject name provided for certification requests.
As required by RFC 4210 section 5.1.1., if the sender name is not known
to the client it set to the NULL-DN. In this case for identification at least
the senderKID must be set, which we take from any referenceValue provided.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

ossl_cmp_hdr_get_pvno() returns the pvno of the given B<hdr> or -1 on error.

ossl_cmp_hdr_get_protection_nid returns the respective NID, NID_undef on error.

ossl_cmp_hdr_get0_sendernonce() returns the respective nonce, or NULL.

ossl_cmp_general_name_is_NULL_DN() returns 1 given a NULL-DN, else 0.

All other functions return 1 on success, 0 on error.

See the individual functions above.

=head1 SEE ALSO

L<ossl_cmp_msg_create(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
