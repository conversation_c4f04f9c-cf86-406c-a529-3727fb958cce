#!/bin/sh
# Run this from the TOP of the source tree!
M4=gm4
configs=`find $1 -name configure.in -print|sort|sed -e 's@/configure.in@@'`
for dir in $configs; do
	syms=""
	libs=""
	headers=""
	types=""
	funcs=""
	AC_MACRODIR=./util/autoconf
	# The following bits shamelessly stolen from autoheader.sh
	eval "`$M4 -I$AC_MACRODIR autoheader.m4 $dir/configure.in|
		sed -n -e '
		: again
		/^@@@.*@@@$/s/^@@@\(.*\)@@@$/\1/p
		/^@@@/{
			s/^@@@//p
			n
			s/^/@@@/
			b again
		}'`"
	allsyms="`for sym in $syms; do echo $sym; done | sort | uniq`"
	if test -n "$funcs"; then
		funcs="`for func in $funcs; do echo $func; done | sort | uniq`"
		funcs="`for func in $funcs; do echo $func
			done | sed 's/[^a-zA-Z0-9_]/_/g' | tr '[a-z]' '[A-Z]' | sed 's/^/HAVE_/'`"
	allsyms="$allsyms $funcs"
	fi
	if test -n "$headers"; then
		headers="`for header in $headers; do echo $header
			done | sort | uniq`"
		headers="`for header in $headers; do echo $header
			done | sed 's/[^a-zA-Z0-9_]/_/g' | tr '[a-z]' '[A-Z]' | sed 's/^/HAVE_/'`"
	allsyms="$allsyms $headers"
	fi
	if test -n "$libs"; then
		libs="`for lib in $libs; do echo $lib
			done | sort | uniq`"
		libs="`for lib in $libs; do echo $lib
			done | sed 's/[^a-zA-Z0-9_]/_/g' | tr '[a-z]' '[A-Z]' | sed 's/^/HAVE_LIB/'`"
	allsyms="$allsyms $libs"
	fi
	echo $dir/configure.in: $allsyms
	allsyms="`echo $allsyms|tr ' ' '|'`"
	files="$dir/*.[ch]"
	if test ! "`echo $files`" = "$dir/"'*.[ch]'; then
	for file in $files; do
		badsyms=""
		fsyms=`sed -f ./util/getsyms.sed $file`
		fsyms="`for sym in $fsyms; do echo $sym; done | sort | uniq`"
		for sym in $fsyms; do
			if echo $sym|egrep -s "$allsyms">/dev/null; then :
			else
				badsyms="$badsyms $sym"
			fi
		done
		if test -n "$badsyms"; then
			echo $file:$badsyms
		fi
	done
	fi
done
