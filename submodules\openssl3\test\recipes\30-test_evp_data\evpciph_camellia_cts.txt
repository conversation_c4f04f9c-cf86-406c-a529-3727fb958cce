#
# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title = Camellia CTS tests from RFC6803

# The encryption test vectors in RFC6803 specify the base_key,
# not the derived encryption key.
# The encryption key was manually derived using:
#    ke = KBKDF(mac = CMAC, mode = FEEDBACK, base_key,
#               salt = 0000000 || usage || AA,
#               seed = 00000000000000000000000000000000)
# NOTE: that the usage was not specified in the test vectors,
# but is listed here in https://www.rfc-editor.org/errata_search.php?rfc=6803
#
# e.g: openssl kdf -cipher CAMELLIA-128-CBC
#                  -keylen 16
#                  -kdfopt hexkey:1DC46A8D763F4F93742BCBA3387576C3
#                  -kdfopt hexsalt:00000000AA
#                  -kdfopt mode:FEEDBACK
#                  -kdfopt hexseed:00000000000000000000000000000000
#                  -mac CMAC
#                  KBKDF
#
# The ciphertext result also contains a MAC result so this was also manually stripped from the test data.
# The random confounder is also prepended to the plaintext.
#

# 128-bit Camellia key: 1DC46A8D763F4F93742BCBA3387576C3
# Key usage: 0
# Random confounder: B69822A19A6B09C0EBC8557D1F1B6C0A
# Plaintext: (empty)
Cipher = CAMELLIA-128-CBC-CTS
CTSMode = CS3
Key = E99B82B36C4AE8EA19E95DFA9EDE882C
IV = 00000000000000000000000000000000
Plaintext = B69822A19A6B09C0EBC8557D1F1B6C0A
Ciphertext = C466F1871069921EDB7C6FDE244A52DB

# 128-bit Camellia key: 5027BC231D0F3A9D23333F1CA6FDBE7C
# Key usage: 1
# Random confounder: 6F2FC3C2A166FD8898967A83DE9596D9
# Plaintext: 1 (31)
Cipher = CAMELLIA-128-CBC-CTS
CTSMode = CS3
Key = A7EDCD5397EA6D12B0AFF4CB8DAA57AD 
IV = 00000000000000000000000000000000
Plaintext = 6F2FC3C2A166FD8898967A83DE9596D931
Ciphertext = 842D21FD950311C0DD464A3F4BE8D6DA88

# 128-bit Camellia key: A1BB61E805F9BA6DDE8FDBDDC05CDEA0
# Key usage: 2
# Random confounder: A5B4A71E077AEEF93C8763C18FDB1F10
# Plaintext: 9 bytesss (392062797465737373)
Cipher = CAMELLIA-128-CBC-CTS
CTSMode = CS3
Key = DDE42ECA7CD9863FC3CE89CBC94362D7
IV = 00000000000000000000000000000000
Plaintext = A5B4A71E077AEEF93C8763C18FDB1F10392062797465737373
Ciphertext = 619FF072E36286FF0A28DEB3A352EC0D0EDF5C5160D663C901

# 128-bit Camellia key: 2CA27A5FAF5532244506434E1CEF6676
# Key usage: 3
# Random confounder: 19FEE40D810C524B5B22F01874C693DA
# Plaintext: 13 bytes byte (31332062797465732062797465)
Cipher = CAMELLIA-128-CBC-CTS
CTSMode = CS3
Key = C3113A258590B9AEBF721B1AF6B0CBF8
IV = 00000000000000000000000000000000
Plaintext = 19FEE40D810C524B5B22F01874C693DA31332062797465732062797465
Ciphertext = B8ECA3167AE6315512E59F98A7C500205E5F63FF3BB389AF1C41A21D64

# 128-bit Camellia key: 7824F8C16F83FF354C6BF7515B973F43
# Key usage: 4
# Random confounder: CA7A7AB4BE192DABD603506DB19C39E2
# Plaintext: 30 bytes bytes bytes bytes byt (333020627974657320627974657320627974657320627974657320627974)
Cipher = CAMELLIA-128-CBC-CTS
CTSMode = CS3
Key = 8B07EED30149916AA20DB3F5CED8AFAD
IV = 00000000000000000000000000000000
Plaintext = CA7A7AB4BE192DABD603506DB19C39E2333020627974657320627974657320627974657320627974657320627974
Ciphertext = A26A3905A4FFD5816B7B1E27380D08090C8EC1F304496E1ABDCD2BDCD1DFFC660989E117A713DDBB57A4146C1587

# 256-bit Camellia key: B61C86CC4E5D2757545AD423399FB7031ECAB913CBB900BD7A3C6DD8BF92015B
# Key usage: 0
# Random confounder: 3CBBD2B45917941067F96599BB98926C
# Plaintext: (empty)
Cipher = CAMELLIA-256-CBC-CTS
CTSMode = CS3
Key = 6CCB3F25D8AE57F4E8F6CA474BDDEFF116CE131B3F71012E756D6B1E3F70A7F1
IV = 00000000000000000000000000000000
Plaintext = 3CBBD2B45917941067F96599BB98926C
Ciphertext = 03886D03310B47A6D8F06D7B94D1DD83

# 256-bit Camellia key: 1B97FE0A190E2021EB30753E1B6E1E77B0754B1D684610355864104963463833
# Key usage: 1
# Random confounder: DEF487FCEBE6DE6346D4DA4521BBA2D2
# Plaintext: 1 (31)
Cipher = CAMELLIA-256-CBC-CTS
CTSMode = CS3
Key = E93173AA01EB3C246231DAFC7802EE32AF24851D8C7387D18CB9B2C5B7F570B8
IV = 00000000000000000000000000000000
Plaintext = DEF487FCEBE6DE6346D4DA4521BBA2D231
Ciphertext = 2C9C1570133C99BF6A34BC1B0212002FD1

# 256-bit Camellia key: 32164C5B434D1D1538E4CFD9BE8040FE8C4AC7ACC4B93D3314D2133668147A05
# Key usage: 2
# Random confounder: AD4FF904D34E555384B14100FC465F88
# Plaintext: 9 bytesss (392062797465737373)
Cipher = CAMELLIA-256-CBC-CTS
CTSMode = CS3
Key = CDA2D39A9B243FFEB56E8D5F4BD528741ECB520C62123FB040B8418B15C7D70C
IV = 00000000000000000000000000000000
Plaintext = AD4FF904D34E555384B14100FC465F88392062797465737373
Ciphertext = 9C6DE75F812DE7ED0D28B2963557A115640998275B0AF51527

# 256-bit Camellia key: B038B132CD8E06612267FAB7170066D88AECCBA0B744BFC60DC89BCA182D0715
# Key usage: 3
# Random confounder: CF9BCA6DF1144E0C0AF9B8F34C90D514
# Plaintext: 13 bytes byte (31332062797465732062797465)
Cipher = CAMELLIA-256-CBC-CTS
CTSMode = CS3
Key = CD8A10E279DADDB6901EC30BDF9873250F6EFC6A77367D74DC3EE7F74BC7774E
IV = 00000000000000000000000000000000
Plaintext = CF9BCA6DF1144E0C0AF9B8F34C90D51431332062797465732062797465
Ciphertext = EEEC85A9813CDC536772AB9B42DEFC5706F726E975DDE05A87EB5406EA

# 256-bit Camellia key: CCFCD349BF4C6677E86E4B02B8EAB924A546AC731CF9BF6989B996E7D6BFBBA7
# Key usage: 4
# Random confounder: 644DEF38DA35007275878D216855E228
# Plaintext: 30 bytes bytes bytes bytes byt (333020627974657320627974657320627974657320627974657320627974)
Cipher = CAMELLIA-256-CBC-CTS
CTSMode = CS3
Key = 1D5147F34BB001A04A68A71346E7654E0223A60D90BC2B79B4D87956D47CD42A
IV = 00000000000000000000000000000000
Plaintext = 644DEF38DA35007275878D216855E228333020627974657320627974657320627974657320627974657320627974
Ciphertext = 0E44680985855F2D1F1812529CA83BFD8E349DE6FD9ADA0BAAA048D68E265FEBF34AD1255A344999AD37146887A6
