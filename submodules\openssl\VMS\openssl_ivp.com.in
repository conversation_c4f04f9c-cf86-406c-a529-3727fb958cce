$	! OpenSSL Internal Verification Procedure
$	!
$	! This script checks the consistency of a OpenSSL installation
$	! It had better be spawned, as it creates process logicals
$
$	! Generated information
$	INSTALLTOP := {- $config{INSTALLTOP} -}
$	OPENSSLDIR := {- $config{OPENSSLDIR} -}
$
$	! Make sure that INSTALLTOP and OP<PERSON><PERSON>LD<PERSON> become something one
$	! can use to call the startup procedure
$	INSTALLTOP_ = F$PARSE("A.;",INSTALLTOP,,,"NO_CONCEAL") -
		     - ".][000000" - "[000000." - "][" - "]A.;" + "."
$	OPENSSLDIR_ = F$PARSE("A.;",OPENSSLDIR,,,"NO_CONCEAL") -
		     - ".][000000" - "[000000." - "][" - "]A.;" + "."
$
$	v    := {- sprintf "%02d%02d", split(/\./, $config{version}) -}
$	pz   := {- $config{pointer_size} -}
$	
$	@'INSTALLTOP_'SYS$STARTUP]openssl_startup'v'
$	@'INSTALLTOP_'SYS$STARTUP]openssl_utils'v'
$
$	IF F$SEARCH("OSSL$LIBCRYPTO''pz'") .EQS. "" -
           .OR. F$SEARCH("OSSL$LIBSSL''pz'") .EQS. "" {- output_off() if $config{no_shared}; "" -}-
           .OR. F$SEARCH("OSSL$LIBCRYPTO_SHR''pz'") .EQS. "" -
           .OR. F$SEARCH("OSSL$LIBSSL_SHR''pz'") .EQS. "" {- output_on() if $config{no_shared}; "" -}-
           .OR. F$SEARCH("OSSL$INCLUDE:[OPENSSL]crypto.h") .EQS. "" -
           .OR. F$SEARCH("OPENSSL:crypto.h") .EQS. "" -
           .OR. F$SEARCH("OSSL$EXE:OPENSSL''v'.EXE") .EQS. ""
$	THEN
$	    WRITE SYS$ERROR "Installation inconsistent"
$	    EXIT %x00018292 ! RMS$_FNF, file not found
$	ENDIF
$
$	ON ERROR THEN GOTO error
$
$	! If something else is wrong with the installation, we're likely
$	! to get an image activation error here
$	openssl version -a
$
$	! FUTURE ENHANCEMENT: Verify that engines are where they should be.
$	! openssl engine -c -t checker
$
$	WRITE SYS$ERROR "OpenSSL IVP passed"
$	EXIT %x10000001
$
$ error:
$	save_status = $STATUS
$	WRITE SYS$ERROR "OpenSSL IVP failed"
$	EXIT 'save_status'
