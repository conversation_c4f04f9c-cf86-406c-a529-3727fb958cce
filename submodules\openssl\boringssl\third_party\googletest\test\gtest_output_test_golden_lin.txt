The non-test part of the code is expected to have 2 failures.

gtest_output_test_.cc:#: Failure
Value of: false
  Actual: false
Expected: true
gtest_output_test_.cc:#: Failure
      Expected: 2
To be equal to: 3
[0;32m[==========] [mRunning 66 tests from 29 test cases.
[0;32m[----------] [mGlobal test environment set-up.
FooEnvironment::SetUp() called.
BarEnvironment::SetUp() called.
[0;32m[----------] [m1 test from ADeathTest
[0;32m[ RUN      ] [mADeathTest.ShouldRunFirst
[0;32m[       OK ] [mADeathTest.ShouldRunFirst
[0;32m[----------] [m1 test from ATypedDeathTest/0, where TypeParam = int
[0;32m[ RUN      ] [mATypedDeathTest/0.ShouldRunFirst
[0;32m[       OK ] [mATypedDeathTest/0.ShouldRunFirst
[0;32m[----------] [m1 test from ATypedDeathTest/1, where TypeParam = double
[0;32m[ RUN      ] [mATypedDeathTest/1.ShouldRunFirst
[0;32m[       OK ] [mATypedDeathTest/1.ShouldRunFirst
[0;32m[----------] [m1 test from My/ATypeParamDeathTest/0, where TypeParam = int
[0;32m[ RUN      ] [mMy/ATypeParamDeathTest/0.ShouldRunFirst
[0;32m[       OK ] [mMy/ATypeParamDeathTest/0.ShouldRunFirst
[0;32m[----------] [m1 test from My/ATypeParamDeathTest/1, where TypeParam = double
[0;32m[ RUN      ] [mMy/ATypeParamDeathTest/1.ShouldRunFirst
[0;32m[       OK ] [mMy/ATypeParamDeathTest/1.ShouldRunFirst
[0;32m[----------] [m2 tests from PassingTest
[0;32m[ RUN      ] [mPassingTest.PassingTest1
[0;32m[       OK ] [mPassingTest.PassingTest1
[0;32m[ RUN      ] [mPassingTest.PassingTest2
[0;32m[       OK ] [mPassingTest.PassingTest2
[0;32m[----------] [m2 tests from NonfatalFailureTest
[0;32m[ RUN      ] [mNonfatalFailureTest.EscapesStringOperands
gtest_output_test_.cc:#: Failure
      Expected: kGoldenString
      Which is: "\"Line"
To be equal to: actual
      Which is: "actual \"string\""
gtest_output_test_.cc:#: Failure
      Expected: golden
      Which is: "\"Line"
To be equal to: actual
      Which is: "actual \"string\""
[0;31m[  FAILED  ] [mNonfatalFailureTest.EscapesStringOperands
[0;32m[ RUN      ] [mNonfatalFailureTest.DiffForLongStrings
gtest_output_test_.cc:#: Failure
      Expected: golden_str
      Which is: "\"Line\0 1\"\nLine 2"
To be equal to: "Line 2"
With diff:
@@ -1,2 @@
-\"Line\0 1\"
 Line 2

[0;31m[  FAILED  ] [mNonfatalFailureTest.DiffForLongStrings
[0;32m[----------] [m3 tests from FatalFailureTest
[0;32m[ RUN      ] [mFatalFailureTest.FatalFailureInSubroutine
(expecting a failure that x should be 1)
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: x
      Which is: 2
[0;31m[  FAILED  ] [mFatalFailureTest.FatalFailureInSubroutine
[0;32m[ RUN      ] [mFatalFailureTest.FatalFailureInNestedSubroutine
(expecting a failure that x should be 1)
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: x
      Which is: 2
[0;31m[  FAILED  ] [mFatalFailureTest.FatalFailureInNestedSubroutine
[0;32m[ RUN      ] [mFatalFailureTest.NonfatalFailureInSubroutine
(expecting a failure on false)
gtest_output_test_.cc:#: Failure
Value of: false
  Actual: false
Expected: true
[0;31m[  FAILED  ] [mFatalFailureTest.NonfatalFailureInSubroutine
[0;32m[----------] [m1 test from LoggingTest
[0;32m[ RUN      ] [mLoggingTest.InterleavingLoggingAndAssertions
(expecting 2 failures on (3) >= (a[i]))
i == 0
i == 1
gtest_output_test_.cc:#: Failure
Expected: (3) >= (a[i]), actual: 3 vs 9
i == 2
i == 3
gtest_output_test_.cc:#: Failure
Expected: (3) >= (a[i]), actual: 3 vs 6
[0;31m[  FAILED  ] [mLoggingTest.InterleavingLoggingAndAssertions
[0;32m[----------] [m6 tests from SCOPED_TRACETest
[0;32m[ RUN      ] [mSCOPED_TRACETest.ObeysScopes
(expected to fail)
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and shouldn't have a trace.
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and should have a trace.
Google Test trace:
gtest_output_test_.cc:#: Expected trace
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and shouldn't have a trace.
[0;31m[  FAILED  ] [mSCOPED_TRACETest.ObeysScopes
[0;32m[ RUN      ] [mSCOPED_TRACETest.WorksInLoop
(expected to fail)
gtest_output_test_.cc:#: Failure
      Expected: 2
To be equal to: n
      Which is: 1
Google Test trace:
gtest_output_test_.cc:#: i = 1
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: n
      Which is: 2
Google Test trace:
gtest_output_test_.cc:#: i = 2
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksInLoop
[0;32m[ RUN      ] [mSCOPED_TRACETest.WorksInSubroutine
(expected to fail)
gtest_output_test_.cc:#: Failure
      Expected: 2
To be equal to: n
      Which is: 1
Google Test trace:
gtest_output_test_.cc:#: n = 1
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: n
      Which is: 2
Google Test trace:
gtest_output_test_.cc:#: n = 2
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksInSubroutine
[0;32m[ RUN      ] [mSCOPED_TRACETest.CanBeNested
(expected to fail)
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: n
      Which is: 2
Google Test trace:
gtest_output_test_.cc:#: n = 2
gtest_output_test_.cc:#: 
[0;31m[  FAILED  ] [mSCOPED_TRACETest.CanBeNested
[0;32m[ RUN      ] [mSCOPED_TRACETest.CanBeRepeated
(expected to fail)
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and should contain trace point A.
Google Test trace:
gtest_output_test_.cc:#: A
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and should contain trace point A and B.
Google Test trace:
gtest_output_test_.cc:#: B
gtest_output_test_.cc:#: A
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and should contain trace point A, B, and C.
Google Test trace:
gtest_output_test_.cc:#: C
gtest_output_test_.cc:#: B
gtest_output_test_.cc:#: A
gtest_output_test_.cc:#: Failure
Failed
This failure is expected, and should contain trace point A, B, and D.
Google Test trace:
gtest_output_test_.cc:#: D
gtest_output_test_.cc:#: B
gtest_output_test_.cc:#: A
[0;31m[  FAILED  ] [mSCOPED_TRACETest.CanBeRepeated
[0;32m[ RUN      ] [mSCOPED_TRACETest.WorksConcurrently
(expecting 6 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected failure #1 (in thread B, only trace B alive).
Google Test trace:
gtest_output_test_.cc:#: Trace B
gtest_output_test_.cc:#: Failure
Failed
Expected failure #2 (in thread A, trace A & B both alive).
Google Test trace:
gtest_output_test_.cc:#: Trace A
gtest_output_test_.cc:#: Failure
Failed
Expected failure #3 (in thread B, trace A & B both alive).
Google Test trace:
gtest_output_test_.cc:#: Trace B
gtest_output_test_.cc:#: Failure
Failed
Expected failure #4 (in thread B, only trace A alive).
gtest_output_test_.cc:#: Failure
Failed
Expected failure #5 (in thread A, only trace A alive).
Google Test trace:
gtest_output_test_.cc:#: Trace A
gtest_output_test_.cc:#: Failure
Failed
Expected failure #6 (in thread A, no trace alive).
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksConcurrently
[0;32m[----------] [m1 test from NonFatalFailureInFixtureConstructorTest
[0;32m[ RUN      ] [mNonFatalFailureInFixtureConstructorTest.FailureInConstructor
(expecting 5 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected failure #1, in the test fixture c'tor.
gtest_output_test_.cc:#: Failure
Failed
Expected failure #2, in SetUp().
gtest_output_test_.cc:#: Failure
Failed
Expected failure #3, in the test body.
gtest_output_test_.cc:#: Failure
Failed
Expected failure #4, in TearDown.
gtest_output_test_.cc:#: Failure
Failed
Expected failure #5, in the test fixture d'tor.
[0;31m[  FAILED  ] [mNonFatalFailureInFixtureConstructorTest.FailureInConstructor
[0;32m[----------] [m1 test from FatalFailureInFixtureConstructorTest
[0;32m[ RUN      ] [mFatalFailureInFixtureConstructorTest.FailureInConstructor
(expecting 2 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected failure #1, in the test fixture c'tor.
gtest_output_test_.cc:#: Failure
Failed
Expected failure #2, in the test fixture d'tor.
[0;31m[  FAILED  ] [mFatalFailureInFixtureConstructorTest.FailureInConstructor
[0;32m[----------] [m1 test from NonFatalFailureInSetUpTest
[0;32m[ RUN      ] [mNonFatalFailureInSetUpTest.FailureInSetUp
(expecting 4 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected failure #1, in SetUp().
gtest_output_test_.cc:#: Failure
Failed
Expected failure #2, in the test function.
gtest_output_test_.cc:#: Failure
Failed
Expected failure #3, in TearDown().
gtest_output_test_.cc:#: Failure
Failed
Expected failure #4, in the test fixture d'tor.
[0;31m[  FAILED  ] [mNonFatalFailureInSetUpTest.FailureInSetUp
[0;32m[----------] [m1 test from FatalFailureInSetUpTest
[0;32m[ RUN      ] [mFatalFailureInSetUpTest.FailureInSetUp
(expecting 3 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected failure #1, in SetUp().
gtest_output_test_.cc:#: Failure
Failed
Expected failure #2, in TearDown().
gtest_output_test_.cc:#: Failure
Failed
Expected failure #3, in the test fixture d'tor.
[0;31m[  FAILED  ] [mFatalFailureInSetUpTest.FailureInSetUp
[0;32m[----------] [m1 test from AddFailureAtTest
[0;32m[ RUN      ] [mAddFailureAtTest.MessageContainsSpecifiedFileAndLineNumber
foo.cc:42: Failure
Failed
Expected failure in foo.cc
[0;31m[  FAILED  ] [mAddFailureAtTest.MessageContainsSpecifiedFileAndLineNumber
[0;32m[----------] [m4 tests from MixedUpTestCaseTest
[0;32m[ RUN      ] [mMixedUpTestCaseTest.FirstTestFromNamespaceFoo
[0;32m[       OK ] [mMixedUpTestCaseTest.FirstTestFromNamespaceFoo
[0;32m[ RUN      ] [mMixedUpTestCaseTest.SecondTestFromNamespaceFoo
[0;32m[       OK ] [mMixedUpTestCaseTest.SecondTestFromNamespaceFoo
[0;32m[ RUN      ] [mMixedUpTestCaseTest.ThisShouldFail
gtest.cc:#: Failure
Failed
All tests in the same test case must use the same test fixture
class.  However, in test case MixedUpTestCaseTest,
you defined test FirstTestFromNamespaceFoo and test ThisShouldFail
using two different test fixture classes.  This can happen if
the two classes are from different namespaces or translation
units and have the same name.  You should probably rename one
of the classes to put the tests into different test cases.
[0;31m[  FAILED  ] [mMixedUpTestCaseTest.ThisShouldFail
[0;32m[ RUN      ] [mMixedUpTestCaseTest.ThisShouldFailToo
gtest.cc:#: Failure
Failed
All tests in the same test case must use the same test fixture
class.  However, in test case MixedUpTestCaseTest,
you defined test FirstTestFromNamespaceFoo and test ThisShouldFailToo
using two different test fixture classes.  This can happen if
the two classes are from different namespaces or translation
units and have the same name.  You should probably rename one
of the classes to put the tests into different test cases.
[0;31m[  FAILED  ] [mMixedUpTestCaseTest.ThisShouldFailToo
[0;32m[----------] [m2 tests from MixedUpTestCaseWithSameTestNameTest
[0;32m[ RUN      ] [mMixedUpTestCaseWithSameTestNameTest.TheSecondTestWithThisNameShouldFail
[0;32m[       OK ] [mMixedUpTestCaseWithSameTestNameTest.TheSecondTestWithThisNameShouldFail
[0;32m[ RUN      ] [mMixedUpTestCaseWithSameTestNameTest.TheSecondTestWithThisNameShouldFail
gtest.cc:#: Failure
Failed
All tests in the same test case must use the same test fixture
class.  However, in test case MixedUpTestCaseWithSameTestNameTest,
you defined test TheSecondTestWithThisNameShouldFail and test TheSecondTestWithThisNameShouldFail
using two different test fixture classes.  This can happen if
the two classes are from different namespaces or translation
units and have the same name.  You should probably rename one
of the classes to put the tests into different test cases.
[0;31m[  FAILED  ] [mMixedUpTestCaseWithSameTestNameTest.TheSecondTestWithThisNameShouldFail
[0;32m[----------] [m2 tests from TEST_F_before_TEST_in_same_test_case
[0;32m[ RUN      ] [mTEST_F_before_TEST_in_same_test_case.DefinedUsingTEST_F
[0;32m[       OK ] [mTEST_F_before_TEST_in_same_test_case.DefinedUsingTEST_F
[0;32m[ RUN      ] [mTEST_F_before_TEST_in_same_test_case.DefinedUsingTESTAndShouldFail
gtest.cc:#: Failure
Failed
All tests in the same test case must use the same test fixture
class, so mixing TEST_F and TEST in the same test case is
illegal.  In test case TEST_F_before_TEST_in_same_test_case,
test DefinedUsingTEST_F is defined using TEST_F but
test DefinedUsingTESTAndShouldFail is defined using TEST.  You probably
want to change the TEST to TEST_F or move it to another test
case.
[0;31m[  FAILED  ] [mTEST_F_before_TEST_in_same_test_case.DefinedUsingTESTAndShouldFail
[0;32m[----------] [m2 tests from TEST_before_TEST_F_in_same_test_case
[0;32m[ RUN      ] [mTEST_before_TEST_F_in_same_test_case.DefinedUsingTEST
[0;32m[       OK ] [mTEST_before_TEST_F_in_same_test_case.DefinedUsingTEST
[0;32m[ RUN      ] [mTEST_before_TEST_F_in_same_test_case.DefinedUsingTEST_FAndShouldFail
gtest.cc:#: Failure
Failed
All tests in the same test case must use the same test fixture
class, so mixing TEST_F and TEST in the same test case is
illegal.  In test case TEST_before_TEST_F_in_same_test_case,
test DefinedUsingTEST_FAndShouldFail is defined using TEST_F but
test DefinedUsingTEST is defined using TEST.  You probably
want to change the TEST to TEST_F or move it to another test
case.
[0;31m[  FAILED  ] [mTEST_before_TEST_F_in_same_test_case.DefinedUsingTEST_FAndShouldFail
[0;32m[----------] [m8 tests from ExpectNonfatalFailureTest
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.CanReferenceGlobalVariables
[0;32m[       OK ] [mExpectNonfatalFailureTest.CanReferenceGlobalVariables
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.CanReferenceLocalVariables
[0;32m[       OK ] [mExpectNonfatalFailureTest.CanReferenceLocalVariables
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.SucceedsWhenThereIsOneNonfatalFailure
[0;32m[       OK ] [mExpectNonfatalFailureTest.SucceedsWhenThereIsOneNonfatalFailure
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.FailsWhenThereIsNoNonfatalFailure
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereIsNoNonfatalFailure
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.FailsWhenThereAreTwoNonfatalFailures
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual: 2 failures
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure 1.

gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure 2.

[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereAreTwoNonfatalFailures
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.FailsWhenThereIsOneFatalFailure
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual:
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereIsOneFatalFailure
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.FailsWhenStatementReturns
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenStatementReturns
[0;32m[ RUN      ] [mExpectNonfatalFailureTest.FailsWhenStatementThrows
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenStatementThrows
[0;32m[----------] [m8 tests from ExpectFatalFailureTest
[0;32m[ RUN      ] [mExpectFatalFailureTest.CanReferenceGlobalVariables
[0;32m[       OK ] [mExpectFatalFailureTest.CanReferenceGlobalVariables
[0;32m[ RUN      ] [mExpectFatalFailureTest.CanReferenceLocalStaticVariables
[0;32m[       OK ] [mExpectFatalFailureTest.CanReferenceLocalStaticVariables
[0;32m[ RUN      ] [mExpectFatalFailureTest.SucceedsWhenThereIsOneFatalFailure
[0;32m[       OK ] [mExpectFatalFailureTest.SucceedsWhenThereIsOneFatalFailure
[0;32m[ RUN      ] [mExpectFatalFailureTest.FailsWhenThereIsNoFatalFailure
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereIsNoFatalFailure
[0;32m[ RUN      ] [mExpectFatalFailureTest.FailsWhenThereAreTwoFatalFailures
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual: 2 failures
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereAreTwoFatalFailures
[0;32m[ RUN      ] [mExpectFatalFailureTest.FailsWhenThereIsOneNonfatalFailure
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual:
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure.

[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereIsOneNonfatalFailure
[0;32m[ RUN      ] [mExpectFatalFailureTest.FailsWhenStatementReturns
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenStatementReturns
[0;32m[ RUN      ] [mExpectFatalFailureTest.FailsWhenStatementThrows
(expecting a failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenStatementThrows
[0;32m[----------] [m2 tests from TypedTest/0, where TypeParam = int
[0;32m[ RUN      ] [mTypedTest/0.Success
[0;32m[       OK ] [mTypedTest/0.Success
[0;32m[ RUN      ] [mTypedTest/0.Failure
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: TypeParam()
      Which is: 0
Expected failure
[0;31m[  FAILED  ] [mTypedTest/0.Failure, where TypeParam = int
[0;32m[----------] [m2 tests from Unsigned/TypedTestP/0, where TypeParam = unsigned char
[0;32m[ RUN      ] [mUnsigned/TypedTestP/0.Success
[0;32m[       OK ] [mUnsigned/TypedTestP/0.Success
[0;32m[ RUN      ] [mUnsigned/TypedTestP/0.Failure
gtest_output_test_.cc:#: Failure
      Expected: 1U
      Which is: 1
To be equal to: TypeParam()
      Which is: '\0'
Expected failure
[0;31m[  FAILED  ] [mUnsigned/TypedTestP/0.Failure, where TypeParam = unsigned char
[0;32m[----------] [m2 tests from Unsigned/TypedTestP/1, where TypeParam = unsigned int
[0;32m[ RUN      ] [mUnsigned/TypedTestP/1.Success
[0;32m[       OK ] [mUnsigned/TypedTestP/1.Success
[0;32m[ RUN      ] [mUnsigned/TypedTestP/1.Failure
gtest_output_test_.cc:#: Failure
      Expected: 1U
      Which is: 1
To be equal to: TypeParam()
      Which is: 0
Expected failure
[0;31m[  FAILED  ] [mUnsigned/TypedTestP/1.Failure, where TypeParam = unsigned int
[0;32m[----------] [m4 tests from ExpectFailureTest
[0;32m[ RUN      ] [mExpectFailureTest.ExpectFatalFailure
(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual:
gtest_output_test_.cc:#: Success:
Succeeded

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual:
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure.

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure containing "Some other fatal failure expected."
  Actual:
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

[0;31m[  FAILED  ] [mExpectFailureTest.ExpectFatalFailure
[0;32m[ RUN      ] [mExpectFailureTest.ExpectNonFatalFailure
(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual:
gtest_output_test_.cc:#: Success:
Succeeded

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual:
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure containing "Some other non-fatal failure."
  Actual:
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure.

[0;31m[  FAILED  ] [mExpectFailureTest.ExpectNonFatalFailure
[0;32m[ RUN      ] [mExpectFailureTest.ExpectFatalFailureOnAllThreads
(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual:
gtest_output_test_.cc:#: Success:
Succeeded

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual:
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure.

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 fatal failure containing "Some other fatal failure expected."
  Actual:
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

[0;31m[  FAILED  ] [mExpectFailureTest.ExpectFatalFailureOnAllThreads
[0;32m[ RUN      ] [mExpectFailureTest.ExpectNonFatalFailureOnAllThreads
(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual:
gtest_output_test_.cc:#: Success:
Succeeded

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual:
gtest_output_test_.cc:#: Fatal failure:
Failed
Expected fatal failure.

(expecting 1 failure)
gtest.cc:#: Failure
Expected: 1 non-fatal failure containing "Some other non-fatal failure."
  Actual:
gtest_output_test_.cc:#: Non-fatal failure:
Failed
Expected non-fatal failure.

[0;31m[  FAILED  ] [mExpectFailureTest.ExpectNonFatalFailureOnAllThreads
[0;32m[----------] [m2 tests from ExpectFailureWithThreadsTest
[0;32m[ RUN      ] [mExpectFailureWithThreadsTest.ExpectFatalFailure
(expecting 2 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected fatal failure.
gtest.cc:#: Failure
Expected: 1 fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectFailureWithThreadsTest.ExpectFatalFailure
[0;32m[ RUN      ] [mExpectFailureWithThreadsTest.ExpectNonFatalFailure
(expecting 2 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected non-fatal failure.
gtest.cc:#: Failure
Expected: 1 non-fatal failure
  Actual: 0 failures
[0;31m[  FAILED  ] [mExpectFailureWithThreadsTest.ExpectNonFatalFailure
[0;32m[----------] [m1 test from ScopedFakeTestPartResultReporterTest
[0;32m[ RUN      ] [mScopedFakeTestPartResultReporterTest.InterceptOnlyCurrentThread
(expecting 2 failures)
gtest_output_test_.cc:#: Failure
Failed
Expected fatal failure.
gtest_output_test_.cc:#: Failure
Failed
Expected non-fatal failure.
[0;31m[  FAILED  ] [mScopedFakeTestPartResultReporterTest.InterceptOnlyCurrentThread
[0;32m[----------] [m1 test from PrintingFailingParams/FailingParamTest
[0;32m[ RUN      ] [mPrintingFailingParams/FailingParamTest.Fails/0
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: GetParam()
      Which is: 2
[0;31m[  FAILED  ] [mPrintingFailingParams/FailingParamTest.Fails/0, where GetParam() = 2
[0;32m[----------] [m2 tests from PrintingStrings/ParamTest
[0;32m[ RUN      ] [mPrintingStrings/ParamTest.Success/a
[0;32m[       OK ] [mPrintingStrings/ParamTest.Success/a
[0;32m[ RUN      ] [mPrintingStrings/ParamTest.Failure/a
gtest_output_test_.cc:#: Failure
      Expected: "b"
To be equal to: GetParam()
      Which is: "a"
Expected failure
[0;31m[  FAILED  ] [mPrintingStrings/ParamTest.Failure/a, where GetParam() = "a"
[0;32m[----------] [mGlobal test environment tear-down
BarEnvironment::TearDown() called.
gtest_output_test_.cc:#: Failure
Failed
Expected non-fatal failure.
FooEnvironment::TearDown() called.
gtest_output_test_.cc:#: Failure
Failed
Expected fatal failure.
[0;32m[==========] [m66 tests from 29 test cases ran.
[0;32m[  PASSED  ] [m22 tests.
[0;31m[  FAILED  ] [m44 tests, listed below:
[0;31m[  FAILED  ] [mNonfatalFailureTest.EscapesStringOperands
[0;31m[  FAILED  ] [mNonfatalFailureTest.DiffForLongStrings
[0;31m[  FAILED  ] [mFatalFailureTest.FatalFailureInSubroutine
[0;31m[  FAILED  ] [mFatalFailureTest.FatalFailureInNestedSubroutine
[0;31m[  FAILED  ] [mFatalFailureTest.NonfatalFailureInSubroutine
[0;31m[  FAILED  ] [mLoggingTest.InterleavingLoggingAndAssertions
[0;31m[  FAILED  ] [mSCOPED_TRACETest.ObeysScopes
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksInLoop
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksInSubroutine
[0;31m[  FAILED  ] [mSCOPED_TRACETest.CanBeNested
[0;31m[  FAILED  ] [mSCOPED_TRACETest.CanBeRepeated
[0;31m[  FAILED  ] [mSCOPED_TRACETest.WorksConcurrently
[0;31m[  FAILED  ] [mNonFatalFailureInFixtureConstructorTest.FailureInConstructor
[0;31m[  FAILED  ] [mFatalFailureInFixtureConstructorTest.FailureInConstructor
[0;31m[  FAILED  ] [mNonFatalFailureInSetUpTest.FailureInSetUp
[0;31m[  FAILED  ] [mFatalFailureInSetUpTest.FailureInSetUp
[0;31m[  FAILED  ] [mAddFailureAtTest.MessageContainsSpecifiedFileAndLineNumber
[0;31m[  FAILED  ] [mMixedUpTestCaseTest.ThisShouldFail
[0;31m[  FAILED  ] [mMixedUpTestCaseTest.ThisShouldFailToo
[0;31m[  FAILED  ] [mMixedUpTestCaseWithSameTestNameTest.TheSecondTestWithThisNameShouldFail
[0;31m[  FAILED  ] [mTEST_F_before_TEST_in_same_test_case.DefinedUsingTESTAndShouldFail
[0;31m[  FAILED  ] [mTEST_before_TEST_F_in_same_test_case.DefinedUsingTEST_FAndShouldFail
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereIsNoNonfatalFailure
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereAreTwoNonfatalFailures
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenThereIsOneFatalFailure
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenStatementReturns
[0;31m[  FAILED  ] [mExpectNonfatalFailureTest.FailsWhenStatementThrows
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereIsNoFatalFailure
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereAreTwoFatalFailures
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenThereIsOneNonfatalFailure
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenStatementReturns
[0;31m[  FAILED  ] [mExpectFatalFailureTest.FailsWhenStatementThrows
[0;31m[  FAILED  ] [mTypedTest/0.Failure, where TypeParam = int
[0;31m[  FAILED  ] [mUnsigned/TypedTestP/0.Failure, where TypeParam = unsigned char
[0;31m[  FAILED  ] [mUnsigned/TypedTestP/1.Failure, where TypeParam = unsigned int
[0;31m[  FAILED  ] [mExpectFailureTest.ExpectFatalFailure
[0;31m[  FAILED  ] [mExpectFailureTest.ExpectNonFatalFailure
[0;31m[  FAILED  ] [mExpectFailureTest.ExpectFatalFailureOnAllThreads
[0;31m[  FAILED  ] [mExpectFailureTest.ExpectNonFatalFailureOnAllThreads
[0;31m[  FAILED  ] [mExpectFailureWithThreadsTest.ExpectFatalFailure
[0;31m[  FAILED  ] [mExpectFailureWithThreadsTest.ExpectNonFatalFailure
[0;31m[  FAILED  ] [mScopedFakeTestPartResultReporterTest.InterceptOnlyCurrentThread
[0;31m[  FAILED  ] [mPrintingFailingParams/FailingParamTest.Fails/0, where GetParam() = 2
[0;31m[  FAILED  ] [mPrintingStrings/ParamTest.Failure/a, where GetParam() = "a"

44 FAILED TESTS
[0;33m  YOU HAVE 1 DISABLED TEST

[mNote: Google Test filter = FatalFailureTest.*:LoggingTest.*
[==========] Running 4 tests from 2 test cases.
[----------] Global test environment set-up.
[----------] 3 tests from FatalFailureTest
[ RUN      ] FatalFailureTest.FatalFailureInSubroutine
(expecting a failure that x should be 1)
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: x
      Which is: 2
[  FAILED  ] FatalFailureTest.FatalFailureInSubroutine (? ms)
[ RUN      ] FatalFailureTest.FatalFailureInNestedSubroutine
(expecting a failure that x should be 1)
gtest_output_test_.cc:#: Failure
      Expected: 1
To be equal to: x
      Which is: 2
[  FAILED  ] FatalFailureTest.FatalFailureInNestedSubroutine (? ms)
[ RUN      ] FatalFailureTest.NonfatalFailureInSubroutine
(expecting a failure on false)
gtest_output_test_.cc:#: Failure
Value of: false
  Actual: false
Expected: true
[  FAILED  ] FatalFailureTest.NonfatalFailureInSubroutine (? ms)
[----------] 3 tests from FatalFailureTest (? ms total)

[----------] 1 test from LoggingTest
[ RUN      ] LoggingTest.InterleavingLoggingAndAssertions
(expecting 2 failures on (3) >= (a[i]))
i == 0
i == 1
gtest_output_test_.cc:#: Failure
Expected: (3) >= (a[i]), actual: 3 vs 9
i == 2
i == 3
gtest_output_test_.cc:#: Failure
Expected: (3) >= (a[i]), actual: 3 vs 6
[  FAILED  ] LoggingTest.InterleavingLoggingAndAssertions (? ms)
[----------] 1 test from LoggingTest (? ms total)

[----------] Global test environment tear-down
[==========] 4 tests from 2 test cases ran. (? ms total)
[  PASSED  ] 0 tests.
[  FAILED  ] 4 tests, listed below:
[  FAILED  ] FatalFailureTest.FatalFailureInSubroutine
[  FAILED  ] FatalFailureTest.FatalFailureInNestedSubroutine
[  FAILED  ] FatalFailureTest.NonfatalFailureInSubroutine
[  FAILED  ] LoggingTest.InterleavingLoggingAndAssertions

 4 FAILED TESTS
Note: Google Test filter = *DISABLED_*
[==========] Running 1 test from 1 test case.
[----------] Global test environment set-up.
[----------] 1 test from DisabledTestsWarningTest
[ RUN      ] DisabledTestsWarningTest.DISABLED_AlsoRunDisabledTestsFlagSuppressesWarning
[       OK ] DisabledTestsWarningTest.DISABLED_AlsoRunDisabledTestsFlagSuppressesWarning
[----------] Global test environment tear-down
[==========] 1 test from 1 test case ran.
[  PASSED  ] 1 test.
Note: Google Test filter = PassingTest.*
Note: This is test shard 2 of 2.
[==========] Running 1 test from 1 test case.
[----------] Global test environment set-up.
[----------] 1 test from PassingTest
[ RUN      ] PassingTest.PassingTest2
[       OK ] PassingTest.PassingTest2
[----------] Global test environment tear-down
[==========] 1 test from 1 test case ran.
[  PASSED  ] 1 test.
