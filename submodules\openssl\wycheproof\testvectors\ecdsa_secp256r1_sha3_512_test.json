{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 461, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "wx": "2927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838", "wy": "00c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKSexBRK64+3c/kZ4KBKLrSkDJpkZ\n9whgacjE32xzKDjHeHlk6qwA5ZIfsUmKYPRgZ2az2WhQAVWNGpdOc0FRPg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02201202069b6b5ffadede2fdc290da1badc989ba98a9a491db339bfe478450ef9cc", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "30450220dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0220edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "308146022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "30820046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3047022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "30850100000046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "3089010000000000000046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3046028000dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d028000edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30480000022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "304b4981773046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "304a25003046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30483046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304b2226498177022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304a22252500022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "304e2223022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0004deadbeef022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "304b022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2226498177022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d22252500022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "304e022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2223022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304eaa00bb00cd003046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304caa02aabb3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304e2229aa00bb00cd00022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304c2227aa02aabb022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "304e022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2229aa00bb00cd00022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "304c022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2227aa02aabb022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30803046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304a2280022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0000022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2280022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30803146022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304a2280032100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0000022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d2280032100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e46022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f46022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3146022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "3246022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff46022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "304a30010230452100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "30452100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b8500", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b8505000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30483000022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b853000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3049022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85bf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "30483046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "3023022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "3069022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "304702812100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3047022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02812100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30480282002100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0282002100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3046022200dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3046022000dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022200edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022000edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304b0285010000002100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304b022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0285010000002100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304f028901000000000000002100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304f022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d028901000000000000002100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304a02847fffffff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02847fffffff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "304a0284ffffffff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0284ffffffff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304b0285ffffffffff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304b022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0285ffffffffff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304e0288ffffffffffffffff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304e022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0288ffffffffffffffff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304602ff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02ff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "3023022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302402022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3024022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3048022300dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0000022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022300edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30480223000000dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0223000000edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0000022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3048022300dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0500022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3048022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022300edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b850500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30250281022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3025022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30250500022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3025022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046002100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046012100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046032100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046042100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046ff2100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d002100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d012100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d032100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d042100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2dff2100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30250200022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3025022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "304a22250201000220dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "304a022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d22250201000220edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3046022102dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022102edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57efad022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b05", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "3045022000dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022000edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30470222ff00dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "3047022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0222ff00edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3026090180022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3026022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3026020100022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3026022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022101dcbe02d28091fccca712175e1effda760d08c16699295b22355e5e3ae9bb147e022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450220dcbe02d48091fccaa712175e1effda76933acc0b4afa1e184deac8b4f0f4c9dc022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221ff2341fd2c7f6e033458ede8a1e1002589afde39470dee4362be5b6c8812a810d3022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502202341fd2b7f6e033558ede8a1e10025896cc533f4b505e1e7b215374b0f0b3624022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221fe2341fd2d7f6e033358ede8a1e1002589f2f73e9966d6a4ddcaa1a1c51644eb82022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022101dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502202341fd2c7f6e033458ede8a1e1002589afde39470dee4362be5b6c8812a810d3022100edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022101edfdf96294a0052321d023d6f25e4522e1324bd0b3e61f56adb3b10db3b750d6", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0220edfdf96494a0052121d023d6f25e45236764567565b6e24cc6401b87baf10634", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0221ff1202069c6b5ffaddde2fdc290da1badcdbb4aedcf3317f2e460619b548abd47b", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d0221fe1202069d6b5ffadcde2fdc290da1badd1ecdb42f4c19e0a9524c4ef24c48af2a", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3046022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d022101edfdf96394a0052221d023d6f25e4523244b51230cce80d1b9f9e64ab7542b85", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022100dcbe02d38091fccba712175e1effda765021c6b8f211bc9d41a49377ed57ef2d02201202069c6b5ffaddde2fdc290da1badcdbb4aedcf3317f2e460619b548abd47b", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff000000010000000000000000000000010000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000001000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100ffffffff00000001000000000000000000000001000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100ffffffff00000001000000000000000000000001000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3335343130", "sig": "3044022064a1aab5000d0e804f3e2fc02bdee9be8ff312334e2ba16d11547c97711c898e02203d4e6c69176bb9e30eec304a30d982cb3f6073a2273f36b67b1b284899b08163", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32373239373236343137", "sig": "3046022100f521807c1e329ac6df4df24208d1e7088b4e4de5a82ed37dbfd8d49c7406f91b022100c44f723038deea858a25d7264d1680b416ffc0d909b94def9fbda02477d69ef4", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "343331343737363137", "sig": "30450220219f842783028cab66f419241dee39da459e2d295d0ab1e56b29b38f50a8bd51022100f6f357077677e2669c1e289f65c6094c68b8e1efbe4c87468327c81d55979bd9", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "36363033343338303333", "sig": "3045022015768c2623e7093c7dcb468d430007fc7338f1cfd058fe22ab09a451b61ec34c022100a609689226f073c968fc46336cfd116edb92045f2383d5376ff88272dc444ca2", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32383239363231343535", "sig": "3045022100bb449ace6f3b900103a09357cda16e3b14e9e99beb3b8f1928f0a66ce30b0ea502206e6b65f9798cd8c32d7068270800da5d98f06d36836ccb7c30551717fae3052f", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "34333131383231373336", "sig": "30450220589e10e34c3fea59478a9301bde976cbd56ac15afa2f13f14f310e5e8d6bf1e1022100adf5198111939bd395bd3820742a68ae97f8595cfc8b7e1892fc360d13142158", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3131373730373734313735", "sig": "3046022100ad5a1daab3023a651b58e3a13ebfaefc14fb9c79ad2610be68e49bd3992e5722022100cfb91fa16a32c8724cef5714e72f2c91b1d50050b4eb272a82327604486cbbb2", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31353938353135353635", "sig": "3045022059d0df1176b277e12c097e9e00860dc4ad3bb7f2e4af2282beb13eff9d7b6afc022100d3da4f61e8adc8449885a52bf73eda66ac1b77ba05a1ab8397e42ecaeaff68cf", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32383831313031363138", "sig": "3045022100d274baceefc72a921fc4962cc9487263ec6984f0f82c0b992ae3c80ba685b423022024bd9a9f39ed02773e8ba54e3f0f99f4e806a69839b7890099be2978cd55076f", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32303034373833333332", "sig": "304502203411db9e53af62472c8afa3a4ae7d044fdbb78b1e3a8e8fd329bc72e9e1dfb5d0221008284d93b62242f6274a26a3419719dc0635e0604fa6da5c2dbfbb85541c88566", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "39353030323437373837", "sig": "3044022033b2abf92021a14fd1e5293008c8aff551fd4a0cacaf8ec6e147f40d0f521cb002204d0d3ed085477a4dc7fd86d242905637a71700ffd4c22cf962a67e9ae32b8877", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323039353030303630", "sig": "30440220717f5185242ac57215d8713f902cf9012f45dba76b8e7e51c67b71e6de10f0d8022028a9ad765a6b1248807be126b9eee01e8e2d65d8528cb28f0d45763f507d97f0", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "38313933373839323237", "sig": "304402204e57dbece14d3d279f1e831777b28401d2990c4ae477eebb997b583e82f29c5402203e41fe090c8f14b8af7fd39c2628869258cf05b0e289b6692602c10cdcc8dd83", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "33363530363033323938", "sig": "3045022009eb0b2bf6a2491eb35c3a902da3c6c90933dd374dfb1f60f8ef7bd749725755022100df8a0ee8f757dc599350bb0e2aec515451152ff05bb2e439ba9145fc9a0849fa", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "3136333833393533353233", "sig": "304402201c66a303f7f548ff4c17ce6c6038879cb9d47abbb71ad69a798d0d8a99518dcd0220485ee205c6fa3ffe5411948dff2351ab9bbbc20ca419d6182da77f5579058749", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32303931373638323035", "sig": "3045022100ae1fd2dbad7ebc2b7f19d1789e1f68ce345f40568a576a29dadcf894c51f7a3f022069e80f80c76b57e1767484485915980b5e2493e89d8824b7922ea2cbc687d2b3", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "39333634373032383235", "sig": "3044022067afdc527b0003c8400ade54df5663635f811950cb34ea77435c0ce40036524902207afa3d39b8a7e52ce768e466db6da41a354c12ea2f677bf05a24fc689e347323", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "393236383638373931", "sig": "30450221009b1a90f8116d33d674137579528f395ed13ecbc238940a1a9a8504a72568a9d7022007f34d9400af004fd657144ce97a13b76d509f7fd4c9245ce54609f749ead3cd", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35313738313334383231", "sig": "30450220263040f9babb63aec7d73fdbce988fb3ca2115ab24bba0c80832e3de34b698b9022100f7d99b7738aca745b91b65c697a83ac76ce1e61568d9271631d06af1b77ad5d6", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "34373335303130373531", "sig": "304502210082f676de71ecae7041b8788e0870a57923c71fd21db1f8864e4519ee2f65d8880220020f12d98b79d45348513696d5b4926856d953e72f267d30e7bcd57ee8147210", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "3134333533393131363839", "sig": "304502204e3c9c22a54f1ceb9336b656bf7019c375cb7f9137d692454c6d882927f16795022100b3bde8f45d5f885948b45402b9e7a722c292e88bfadb5bb2fdadfe8e1b3f5181", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31333834353439323034", "sig": "3046022100b20721bd0e8c9ee6eb790cee9d88481d17d568b4c942ba437e607460031c1df50221009f13ff6a54e6a0c5041cc3690c8f3b499f05e34f235c8adf8fd455df4a17ce97", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32373632313932373839", "sig": "304502206b1d46eeacccf7066a0fef27f498a59a9376b2544258510452787bd4a35783b502210088328ac07938b5b9cfe62a3841382cfbe9d623480cec2a3fdb73ee36dd4aef8f", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31383331363534333331", "sig": "30440220257e760a54637387b443f829f1a5802c7fc92bdf033039acf68e1acea0cbcdbe022025087f120467a80cc07efb7bb27ae68d8569a38fe2908282c3c7a310322761c5", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343336383939303330", "sig": "304402201869c821e271838453c88c4a867df2301d27662b72d9385bacb6740ab6d6b5c3022022ccc9a0493116556d9ffc7582253c581452d717a7e00618dce21f46f5b64fd0", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "323034303431323232", "sig": "304602210094a521fb3bed347b0dcbe55f2e67c0d7abc4aa32ed2e1a6fd1c209de3a25ca250221009b7631a6520ed4b14b5b2a8b57a52de31f583b8d1260eb8dc061ad965d0e9eb6", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "33333337313139393735", "sig": "3045022100ee4913a5f83dd3310e91595ef197075600ae17785e1c0b9139cbaea8def1d6d002202818f7847c48a9a38739c71653919104db8920a816000ed21e5179980efb6b6d", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "36363935363230363738", "sig": "3045022100e912b0e5436db2a50817c6424291ffdd41352916e956ccad9068c95125f3a1e9022073309aa384c56486b6a4c699fca446e6f67a1133892971be83b4e2dab49afc2a", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303933303137373437", "sig": "3046022100eb0b719879b2fd1676b3f6f913e353c2b4dd8201facb067e15da1dae9addcc8c022100a73b3a703857aa8d12263290366a19d64e0b3efccd0623f53a7370099e9dcece", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "313233343137393136", "sig": "304602210095617b71dc18a99e96a95ab5384324bff797ad704dfb1e2d6b3cc06e76652ced022100b11bc49444a2e2635d4d9b1bdedeb61593b5f3e0e16ce78da813ff64b728f8d2", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31373634333530363837", "sig": "3045022028fbebb0c142c6d200bfb173173c03bc3aa55da6d48c1b784628f2b9f59038f2022100c0f1427005a46aac00a0af43b8f54a951e673e0480c6bf7a6b5775d4a3de5e60", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "3131343137323431343431", "sig": "30440220457ff86369e4efd69592b161aea2b09153f37cf69006907a7558998ab34b38a4022012692ffc569849dc8e3b9fd2cd7c6f42207bb4c07832d490c9d35a702665ce58", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32323638323436343933", "sig": "3046022100ac70036f6058fd1eb40dc6f58dd68ab56e73dc6841eaa45a83813b6aaf75fae2022100f70f7f0563c46a07d248e80a65f38532d5759f49fc104184fa6718ca55274372", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "32373234373936373737", "sig": "3044022059baaba3992dab0b594a2e9fbc4c344cfdabe7868e4e77f47a310d94916d30ce022020b16c383085ee74402157d4f2da5f7e3e995dbbf53ee50985d4f7096882edda", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "393733333935313139", "sig": "3046022100aa567a710f7010718b1e02792bc63f9049c02f798bb319214c97bc734e54d115022100ddb7c78b45c4221db6f26e845f8c30be61c29bcf38e38abaf7cbd03e5af914f7", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "31353037303032373036", "sig": "304402206ac18c06081a51ac5fb6d0a0735af510dd0ad529206e7fc0c1b6d0ea36cec4a8022064d49524cc26ef8eda21ad5f2c371203d6eef8ddfa5da7a6c37d86f29efc1fca", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33373433353638373832", "sig": "3046022100cf59dfd67512e2d173a398cb648805fb41e3099f27a7867aa2dda50dcf2e1f86022100bd528be771fa1e52cab6d7485c5887befb378ceaf565aca0f32d2831a466959e", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "393437363731323438", "sig": "30440220455f1c4406b598fc828ddb3bc77df7ad04baad399fb4975f542b19aeedfd5a1102204e0555d9bf4211d3ab173e2e6c7edb990867f8f982f6afc1b4b6732ee96a0af0", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "373235343434333234", "sig": "3045022100e1dc7a439633b9bc91d37a954943bc94e5f56a0442a3528fba5c6070cf25e86f02200376b7ff7601978a47bbf7d11c6844520a442ceb167aec3a0ddf36e150a385c9", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "35353334303231323139", "sig": "3046022100aeeeef010cf97f324e6f89f3163656305728a573b885c52ec5973eba863a08da022100904c25f931e78e2b59fa9a138cb80a5c22341601a18501d364af361d69cd0bb8", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3132333031383133373933", "sig": "304502200e3798e441e787dae4bf84e2748abce913f362d41d59a39d58d89c889e52229f022100e3a002023296fe6d9395fc92ba31dd35c5ada42e06db01466a24f087ae70cdb4", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "39313135333137363130", "sig": "30460221009bfe9e09c40fad40bb442eb045216c20dc1282d3cc4ba77fe2e3db92d312fd800221008a152afa2164548b6edfd6610046ee001fdfb06555677c98bc505b6aef297d15", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32383934333937393632", "sig": "304502201531f9b622d3172f72096b8afc7278eb7bee49b72e1b948e48cc1add8f01e365022100edc803cdb54bd4c54b87bc69a42df3dd11ab56bb13d8b5b78642ffa9ef88d31d", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31323333353833303333", "sig": "304402207973333617680ff89798c6b64bb42f436be7771887c2d14a98dd3397e6896e0a022011b70b23a62fd9ce1b27c1b669c851187c09e9081f0aa6ae011411425f694929", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "323131323639323536", "sig": "304502204fc40e0b3dd49c4f259c5ee2ff271b703b9b7380455167e11360bf336f72c0c4022100d219cdbfefa9ced843f947191ff11bfe4880702a4504f34b28481b424f433a38", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33303035343833383634", "sig": "3046022100816c369e7b8672cce325dae1ad2f2a5330d177fe0da399c8e520f361ac770389022100858c9cdf1a2ca40ef9c1e02f883b34701dc1760a9d13ba714c57dd886282acd8", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38333536373632373235", "sig": "3044022051adf43cda1a938e1deb5fe08ab28df1e607f25fb2e98913d2579420b63056e602203b8920181a8ab6f9ea0a1814171db3eb4ff3d5fd9cbd2d9c1ec4018a7625562e", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "38333239353030373630", "sig": "3045022042ce9e0f877b7586e7c0eb2fde6f7c4b1f4cc888f54402f2b7bb99dedc2b7f07022100f0413e7636cb6605e0d7284c7804d7c9a3cc0924d37fca7d4943d9e0c9817427", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "31393237303834313635", "sig": "304402204b2e5a797d240cd8c80b424a41dc47eaad249fc642e3c5b4bc68328fc1e26fc50220117910985597a6f7482bec7632e94676c432684f16f7f3d09a90ff4ed6ad36db", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "37333032333538323838", "sig": "3044022042bf9a164723ef3f5cb59b528966ae648e80291785a4e0067283a7af59795bb70220017e1d24962a34d850d94187d269836ad437396a0ff134067b1baedef69b5951", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3133343733373337333833", "sig": "304502201ec06a62d69fca8d99473b8d5a8af3bbd701891df976e33e2f86c6084fb705f2022100b679eff436faa53c0a3c437717f574b59135d5dc49fb8524a365296db2d119e6", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32353031353533363839", "sig": "30440220183c81ab34a7d7c6742ede4198f5c75ed7fb79e8315899ef0dd26f95e3c4ba26022073f0196e459eb980b6e7ac44d868ab632d6421d1788b9f9651fb827b1a8e5dcc", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "33353335303430303239", "sig": "3045022100f5bec00e6b90ab2a8f188f8163dcdd9b8185a6bbbc623c12d583c9cf55c8e3ba02205dba085bd57b5471f8547cff0aca9e51234d85c0847d01a3aa0b3cf3cace82d9", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31343230333537323930", "sig": "304402207c18891499af92cfa1db5538c7afbd44605b17dd0ede573206db85af23d3fe9d022028ceff96833996ebababef332b05372209844ddcc4c2134fd9311397e733b10a", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "393035313735303638", "sig": "304502204ed587536ba15e6707014f5ae773a423475e5e37564ffe91aec17722894cff340221009ff59fabe5e8dcf9539ea1bf9f4b880f112a26c915c14827dad9de6b251dd65e", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31363133373734323935", "sig": "304402204fb615772d56b6d1ba60f371eb04c557e0c9414c4eec00819c7647d91e7dee0102202623c6c4a3bffc596cb5f2f70a5a381942d350df04c35978f6607ddf7d57e4e3", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "383733363733343931", "sig": "3046022100c53d6697f536b8fb0e79f3b002dc626a02948e206d96de7c62bd7f9736408ace022100c9423b8575e4afe16cfebefb03e55d628270264fad0eb67c9acf0268e514584c", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34333535313036343035", "sig": "3044022008012cfd3713068d8ba6f3fa453be9da95afa6572a893882075ed5d64d62a41702203c5d1bf341823a4cca251cd67d43a6bcd6b5e47303082b7e54c80df5acbbac82", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34353339353735383736", "sig": "30450221008ad3da767111f41558fdc51723c649a87f58d42bea3431c8fdfa1f1096e3f2dd02207d1ea6859a0c932f2298f9d21c89ebea7354bbe508d2b73469dc3efe8613bc87", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "383933363633323031", "sig": "3045022062becf5afb08835f6768882c58a26c501f277ecd61a48d1fe683b6a78c49ce8202210089d11230de6953a1eeeb6b30774fc6bfd093c1e7c0422a7253428ba17ef969ce", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "33383036303638303338", "sig": "3045022022b54382e2f2e6d7cffe3b34fbd567c8b503bfa6b5c3e5b8131e0ffe20747176022100a6d25ae5cab39cc83116d073cd1eb59d80ce2ed208c1c970b134be2ad29d6068", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "32323332383137343937", "sig": "3045022100e2d9f5c37ddb25978ed4727b63a5e17546292526877e903f11d3d29874e952c802205c28ebafa2c2d285ed7f143d49b6d47daf12ad21c36f78ed7f18cb53d9c29f76", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "34303734333232353538", "sig": "304402203b4c2fb773223c1dafa6d6de9ed2304dc25eea1fd487442a3a64ae8dc8f14927022056c21579e85fc9075c7601bd36ef8a3fa57d5bc7550c9a17bb8383e7fdbb366f", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "363130363735383237", "sig": "3044022005276f6159e2853fc6884c2939997b5a3be7ae615dad7517d6006208111dd6780220059ea3474bc6908c565dfb5bc72fd1a84231363cf78c4c317a061dbf1b03cc07", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "3137343138373339323133", "sig": "304502205f856a30a8803a2276e8e5b8475f085d14f6de0c5f64eaf9e9b81c75fa831672022100a210a74c1c682de5708b9d19e6fd2f74ea047b1352edac7e4784cc008e8b0b5a", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "35313237383432323837", "sig": "304402201c058daeb17f995cbaa4b02fc1ccf0a121fb7673d7b9b7bbaa2c9d850f9ba74102203e5400acc992698dbe9c41e25547c6ee08c841d6604de457558ee8af11d54446", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "35303338363930383739", "sig": "304402207a6c6eb198bced25223fed630dbd2956c3799a21389e007efc23a0b3968f8aa502204535354e8fb477d0be4a16f44719d94650ed4607eabe206848ba24322406c1d5", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "33383737303432333937", "sig": "30460221009d05a43f8dea2c4a3c0838e5987d899e63317a17fa5f609a4baa3764dac9899a022100d7c1833485f0a6914e5633115330a59b2ade5153b01b2a1af0a815f44d7f0aaa", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "333231373038313738", "sig": "3046022100b262a757ffcb496d880739d1937f139d39cde8e7ed29512a3be51ad470dcf5d9022100eb8dd006680530f27326f1c9c54e5c4300f1bca21bdb17245c485d7b296a2372", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37363637303434323730", "sig": "30440220480a6875b7bee4ed80f70206400df10264c38be42b07443c6ff19c0aab580444022078e3fa1c2fe11208c189a39e717b19a51f5172054891c083931861c7919d4745", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31313034373435303432", "sig": "3045022100f2be8cc615b14c289e50114ae2473b105447ec8c5311ecc3abcedcbeaeea1983022056ae20cd659370712e06bd6d5936bcaa06169737a062438c7e599bfff0099fb1", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "313533383730313534", "sig": "3045022100bed38f88fd52bf1cc4b4cc58bdd8a81af894c5a45bd822acf468eee08a68358402201c9f77ab2c821daa896e73b9f6ed4edf72ce62a6c48b5caae6a5ac9edd8d2953", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32323631333835303439", "sig": "30460221008513ed012ea7a10d2239c209e172edb565b1bfd2cdfd80269f79956a4aab5af2022100a413070c1e974643e5d5fdc56209e1421a254f47fda3312ceda244064efe69ce", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "37353538373437363632", "sig": "3045022025bc1c38b291a5f60b7b01eec8a5025c69723b183f9090150a7f0ac87464f2f5022100c986a03025cbf9bdfa4e9a0988822dae44c48624bc63a203072c9cb1b813102d", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "33343939333634313832", "sig": "3046022100b721214cff1779b8f407f4b1b2b2c5aa4e49a4a517031ff3d24b5af589b28b96022100e9c53670e94337535dda10a599de0a6da240ea813e8081f9caa6bae59cbd310a", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "32333639323733393835", "sig": "30460221008695064bbd15d76b698e4bb8c0183bc2634a5c2455d6bd2c3f8323a4268edfe0022100c706f66507b52d2c8865e3eb5959267307f51fdb0565c2320132f2ca14123452", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343933383339353635", "sig": "3045022100c91aad6b3c086073dafdb7688c6366be9127d9935cbc6e0c14b9f76c9d272c43022073c0f75156531aad36d2d14169c2b66797e8dd31d6f66b8ddb83f7522fba2176", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "32353334333739393337", "sig": "30460221008125832edd19949328b170b1067eafcc17b3b79f5c139dfb6c109a1107ac76c8022100fce7f770e2245eeab16c33a230f6dea2ce1da67d11302a8ce7dc6145c30a2bf9", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "383334383031353938", "sig": "304402205e399962385aec963cb80dab6b5f5c341ce15e437142f4275ef9c210385348c1022018a8157a0976bb0e349a02134fad0d0286c40a5e43a47b49b5a03653e3ae9e19", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32343131303537343836", "sig": "3046022100ada147d5937331d037083c0bcda59adb6125485a9ea78ef6884c1432e93e4093022100b5ea223b88f45533826a8b24fc91ed80ae3560543102fed1d82360372988dd74", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "373836343638363335", "sig": "3046022100ff56b22aca9206d8dba458507804c9f80b94e75d2b61443a1c8d72480d8680b4022100a866f620640511357b7dd3bc0eddcddcc5a59e9162204c1d85f223ec485cdee2", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33303534373733373638", "sig": "3046022100cf3555d277eb8f6fa629e8ed875df1440352e53f32f9509ceecf222c4197c5de02210084829a1286f98c299ec5c2169a14d0cbf4892a97baca8310279d9b4c98fdef47", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "31393237303137373338", "sig": "3045022100d8dd5094bc40652ddc19d04beeefee8d90fef82628edbc218a9d2de596bb023d02205c0a46804e7de7c741e5be55a7ebba092dc10a4d1691e6a04ac1690b54acf950", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "31303531333235363334", "sig": "3044022025236b00a0a67e12ea781ae53a929e13c37994ddce784f3c0c33402a43b4a6f00220117331e5b39fe2a11f5c8bbfa5bb4fdc3659ac0a0efcd03a94362081a4c0e579", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "34303139383636363832", "sig": "304402200f3564b771337bc8d494a04e4d0518f26d067d07c31689d5e27b503d3652117a022017144740db21874aa58c0de6a4cadb16c5d9230d4f4607980aabd161d21045ae", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3130343530323537333530", "sig": "3045022100bff706d27dc3c8c59951c342244e3c3552216b9225898de130c6a5a8f58eef4202200e20cb9bffd20d88fe70e5d1f909051528c55efbadbfd7cdcce67de853f64632", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "333236393538353830", "sig": "3045022100ec5c12881dae52168aa635c80dcb0031a43e7d1b76ea97231b819051a861a7ff022009149c000d1af12d800225c1ba3587a53e5aabd8a8fe78230b1b4c12ba7df008", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33303734363533323431", "sig": "3045022100caeda2ff27eef0c71a30c277dda128692e4850e589d07f84046342ddaef9842a022069d422215439150d1da00a0811d2126de5b5c5a85be18fd3c06eb638703e6031", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "37373134363833343830", "sig": "304402203977465917472d3cb67d6fc8888834f26fa47c8d3a2c8e046f70680e3037a37e0220785adced4f3d400b286ad7eb7bdf0b7c0f46d9e7268db5a34e740166caafd14a", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31373933333831333230", "sig": "30450220051b252934f1c65f39652a893e9517f049af42259936832cc3f8ff56d7f3cc54022100867bc57a96eb9d8dc3afd6db11b527f19e4ad8c031134ae6ec9b2b6ee092549b", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "34383830363235353636", "sig": "304402200162ba940a75c7a12574736d45d60bbb2c6ce7739b04429cc72352adea4666c302204c686aaafb4f3615247ff06b97bd76d49b93f65b5f66c93fc2a8a22246536c24", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "3439343337363438383537", "sig": "3045022100f74e65ddb41c6c01564b3344fcaa8db2b7b73f8fd156e500865a04826c5e4ab002206ec0376675b8c3692f397341269667eadc60696d56d8450ced85a65de9d3cfe1", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "34373038363839373836", "sig": "304502210091f3ccfb87b3667bea8c6bb07d554467fbb9a77685a6523da5cf97dfa710e6b202205559936f9c94c6e4a97c9febbcb2c1a418fbf9f2933dd64def686b3771dc3fa8", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "33303239383732393531", "sig": "304402200afdcdcf68669ecc53b019d7adbe8dc943f453b74451335a631a2a3dd5672f83022077ea833468953e14bd428663eebce4b3cdef41e9821704fb91d1515d20078e92", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "33303137313930333835", "sig": "3046022100a197d8f8fa0cccfa7eccfaea7ca4441bda9789cc3e8d22a1d1b14d6deeb2db2102210088fb12318d60b66b68fe80834172813989c24901e3adbcd4c73860fa9500bc75", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393536333633393339", "sig": "304402204b3eeeaf50e9f9bae0bfe64e2776bf2db181ab44c763393136927827f3adb24e0220317abec131eddf2117e27ea2bb4008e8fd0f4a32aa41f74eec7b1659c091fd9c", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "35333030373634333530", "sig": "30450220464bdc2db7c5a2745a5d8c96a323eb99c175e0f99baa75dbb9c6d29b98facf3b022100d9328ca00ce32934b4ed340a6e15236cb16e5364cc91ef5cd6f675e048985bac", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "31393334363634383434", "sig": "30440220270250c591cb20c7978d9f457104afa7c3484879cfdcf9dc6ac8e785e1fd20a0022009601dc597c123ed7e76bdbe2d146863bddcf357f9217713dfe1afd8f49891b6", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "3335353435303535393632", "sig": "30440220729a17311a2fbf9f2325117b2cdac18fd6bbe3db4c5a1bd4e4f4f3d8f3d68dc7022048dd0269cb216e2792f3b410a781c9b10e59abdc553aae2a00d4038f44a2849e", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "31333031373232313038", "sig": "304502205228f97e5d0dfb62cec6820029175867d7d63b179db3e8a9e7eecd1ec55e0b90022100dc547d7b7b680b40bcbe0cbf9a2d749787864158af3c0e0f8ef08dfcbbb36f7e", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "35363137363931363932", "sig": "3045022021f775d3c7f7fe68e84842b30de9fbe4ffbb4f3324b295591e36e4651c14830d022100afa24a4cd163042da5f767e7088e6fa03efe039a477fc13f6c23270cda91519f", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33353831393332353334", "sig": "3045022100dabec42e9c44f0c230d36c45956cd4a8250f3665b6f291f700e73d06fe203744022023b687c29e03bc69c5491774588e3c519fa1eafae2ff8ede2ffd4a004e233215", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3135373136363738373434", "sig": "30440220381458d6c40c888bf90aef1428bc968caace553f94287e7adaa48943dc55315d02201e0e1f75489a451fc7cc4bca61a0a7330a5b4dc4283df68e21798996d8c69bd4", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33313939373833333630", "sig": "3045022100e08881dacd7ad6441f80be1d5510b7e29da4c8504d658634f13e5f3cfcf2a16d02203792541548f0d891ddf95ee40b815fc3ac160cbfe8cd948e0786db7cc20b38d2", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "373430343735303832", "sig": "3046022100a3efb0abc9d76e6601cb23d28b0276293eb8d6f2103d0da7b06a0169db1ea6a4022100c619ab2ba23b20f90bc815d2fd4c345d0381476b164590cd932b40b2263e4054", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "343137343336353339", "sig": "3044022061746df1d5f3d30403f5281767cb6601172b26564e97c35d4c68fb7426db4ffd02207ca2b0a4fb0d58a119a8df391ad3f2de47cb3b1a8fdf04d6ec235f639fe44080", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "31323335363538383839", "sig": "3045022100ce48fc4bedd4dabacc68f71c4f5b58414e9b054b445c4781cf6d9b40d335b4b602201e73b8299579684b80801d2fd70d5c6f9a904ab0735b26d27ff44021002c6107", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "32343239323535343034", "sig": "30460221008c936b044cf6dbdbe30cffdeb9eb40b4aa48e697975805dd40455e44fa4d6dfb022100f03bd82bc73dc7a9d518012f9cab5360d6daa06dd0145765b512263b13c3d851", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "3531383033303235343636", "sig": "304502204bfaa8c78636efe63915dabaaee914deaaf4a06ae473679160eaeca88d5cc1160221008bc24546613e4811facecf605d6b1f79b442c3d8fe458c1c69334cbee31ef532", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "34343736333938323030", "sig": "30440220130fc664eccece7b71a0a9cadc5369fb5bd54ccc53fd2c515e5ef232acd1b96502204372a69ef34ec2b296dbe94c5e8a8881645b9c0f6b6c4270c19b81493eb59421", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "39303630303335323132", "sig": "304402207b675a89f3165e2ad1f3ac4950898d17e7cd869df4ef58aa54901d51193f91f302203a7ea099d7c2909ff4a5c3c286d9f7739f38b7e44605ea3f515fde117a4519ec", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31373536303533303938", "sig": "30440220393a056a5ce76dc161daf1a6a77afb72cacb809d4173b9126e636a01cf7ccea102200859e4195124e11e40db391a7bf04f3ecd7dffe2a5629aac9374468fe8f1701b", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "34393830383630303338", "sig": "304402202d9738a6e36c24a5dcde61ff7fa5ecb1e96e0077a4a183e8953745cb9461e5fa022055abb27fad179d132830f7e5dff0d26a63f0141fe2288b9ff7943c3a482ac124", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "33363231383735333335", "sig": "30440220460f528bb6f8adb55626c5166c4572a93c3825dd1a695822a81c9e75e37fe8fd02200197c8b83193110f0426a417fae7ec3b99fc669371f1e7debbbd3850f6956c14", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "36363433333334373231", "sig": "304402202d13939455192c22b1f18f7ca82ad4265ec2c4dbd124ae4a2b0017984512c9e902203d9e4fac784be61e2decb75a1f6768af21873eb49881849b936036b19187a734", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34343534393432373832", "sig": "30450221008f2523bbd4a2f4d144c0b7f7b5da84ce20d0d4f551cf5e0ad2b0cb06c6a207f0022005e46a85f61e1676def59d819220edcdf421db36efbe00bda7f9e331d56e8ce1", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "3230313834343032", "sig": "304502204f60878beb2755b35f7152a33e416e16e10f77bca2ad70b09c86eff148ca6fb3022100bcbc6fa18d8ca570b4a9a5f2a8f6ff9db3e3370ffd3e5a7e19880990f6f8e614", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "3538313332313733", "sig": "30450221009b148c63588ee8f22436a61d52ae8a60c3f35007dde8bb6e31997d99205973b302202015548563168f496c7b115bff541fd71fc926448a4554a15f97c58fdd039015", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31313833383631383131", "sig": "304402202b7d769ea682af0be42866cf3ad8865b12d4b37c3708fb2ad0dc5c87eda10154022011d8c48ba6c400f7950f9dfd9ea65617b3765db0e79778bb079fbd7db67b68f8", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31393232363032393036", "sig": "3045022100bfd3ac436e5c4d42f15fbce06d9859ad82d460d322a8c4c1d06ec318fdff4f510220335bd5d0619a5290c7fce42aaa97ccd52d80528a09a62a4ce8d1e7cf00db7a26", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "393735313433323037", "sig": "3044022056a5c2c2e803e66c5751fe8fb348822609b167478cad4030e7bf6120fd535bb002207b3fee66768794c0b362f2d40ad64fdf03e0fb0e8a47f2034167aae2ca383c0e", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "38333135313136333833", "sig": "3044022071bc399e5643a1d8e2afc317e78a2c43d76b6d87f17a5809a7b6d19f922c2e620220057e7615a2c9033ef0d1a3d3020977002395e196d3b8206c9afecfb98abd52d8", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "333236333136383132", "sig": "30450221008f94bcda036e64783935456289aedcb67232932db1ac2e8ad1ca970b14d47a1a022021a71528f9e8fe6067ca74cc0d8b4b48270aad9ade9db44a98c98182a5e8f208", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "34303239363837313336", "sig": "30450220277a0c4f34b2749fd58f55a0f373430f3c868bb1f485d477102b3e797cea5560022100bccbec7dc1e2740751b7cb8968e7d388e136d7647a29c6845bcf3759f44bf389", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case hash", "msg": "36333230383831313931", "sig": "3045022100934762967efc8c97655cb73d7168d1abb5889e55243dc67c1c5517d6298ce2c3022044c32fb9d2b5e6f9c38226538776538a55eb1bdfbac44e3e1229337415645db6", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case hash", "msg": "35323235333930373830", "sig": "30450221008f052802126f222ba3ade9cfedf59fc479a120ed8e59366b1be4f3039480004e02203e14e9209be9dc6d00ed76cddca1c2e5b85f112bffd2779f45f246cb60c319a8", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case hash", "msg": "31333439333933363934", "sig": "304402204456d6792ff7e4d27517113f309177c26ef3bf6482e131d43f9d8b80e954d61e02204ffbcc50a7a69dfce92a47410f8574f7b0abfc8b4c7f74ac0dc1c1183d393482", "result": "valid", "flags": []}, {"tcId": 357, "comment": "special case hash", "msg": "3130333937393630373631", "sig": "3045022100c0e27a8ad306f4ed4dd77edcefb61a6d8f1a9f6546a9969e794432fcb576cd49022067fdcab3395eebe53dd03b9cadd3eb62d84327de15498b60a60b023fea990436", "result": "valid", "flags": []}, {"tcId": 358, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "30450220504d070e4cf2596e560c1aadc63c573cee0bf8c4d128292c23b47a294c607703022100f181213b28b921af292ea2fbcb8af4a0e14e2c5aa603c45dab7a46f05bbd5d85", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040fbf9d131ff8a037747ac23246c5b0e0ce09a905432d3d75477af2d2993b1e0a4b14e93d4574fc2a553f85eb3c4c0658a1bd7e1cc7f08d47396e76d6d40ed772", "wx": "0fbf9d131ff8a037747ac23246c5b0e0ce09a905432d3d75477af2d2993b1e0a", "wy": "4b14e93d4574fc2a553f85eb3c4c0658a1bd7e1cc7f08d47396e76d6d40ed772"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040fbf9d131ff8a037747ac23246c5b0e0ce09a905432d3d75477af2d2993b1e0a4b14e93d4574fc2a553f85eb3c4c0658a1bd7e1cc7f08d47396e76d6d40ed772", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAED7+dEx/4oDd0esIyRsWw4M4JqQVD\nLT11R3ry0pk7HgpLFOk9RXT8KlU/hes8TAZYob1+HMfwjUc5bnbW1A7Xcg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "303502104319055358e8617b0c46353d039cdaab022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}, {"tcId": 360, "comment": "r too large", "msg": "313233343030", "sig": "3046022100ffffffff00000001000000000000000000000000fffffffffffffffffffffffc022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04920945522a364975ffc27bdddd7f01ec26ef8ac48d466a81fbb1d02fc89133e9ce7d01190e815daf1d18e5932ec8a3d83af59c7f66738b6521a9850d794a4ba3", "wx": "00920945522a364975ffc27bdddd7f01ec26ef8ac48d466a81fbb1d02fc89133e9", "wy": "00ce7d01190e815daf1d18e5932ec8a3d83af59c7f66738b6521a9850d794a4ba3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004920945522a364975ffc27bdddd7f01ec26ef8ac48d466a81fbb1d02fc89133e9ce7d01190e815daf1d18e5932ec8a3d83af59c7f66738b6521a9850d794a4ba3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkglFUio2SXX/wnvd3X8B7CbvisSN\nRmqB+7HQL8iRM+nOfQEZDoFdrx0Y5ZMuyKPYOvWcf2Zzi2UhqYUNeUpLow==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "r,s are large", "msg": "313233343030", "sig": "3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254f022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e0ce6d55dae257d10a06be4fb7333e68475407a401de84ff86a931ac022a4513802ca56c9a1d2f67e8a45703b174f0562dfb7e6a532eb1743b3ceb49f9bea420", "wx": "00e0ce6d55dae257d10a06be4fb7333e68475407a401de84ff86a931ac022a4513", "wy": "00802ca56c9a1d2f67e8a45703b174f0562dfb7e6a532eb1743b3ceb49f9bea420"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e0ce6d55dae257d10a06be4fb7333e68475407a401de84ff86a931ac022a4513802ca56c9a1d2f67e8a45703b174f0562dfb7e6a532eb1743b3ceb49f9bea420", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE4M5tVdriV9EKBr5PtzM+aEdUB6QB\n3oT/hqkxrAIqRROALKVsmh0vZ+ikVwOxdPBWLft+alMusXQ7POtJ+b6kIA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100909135bdb6799286170f5ead2de4f6511453fe50914f3df2de54a36383df8dd4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040c5baefb69764c3a55e9d3ef10a76f652ffa697794ab91169878116d058420e3ccd7b9153694151ee2d05048e40fe072d8e0f481af5d3d0a9e8cf39e1ef7e0bb", "wx": "0c5baefb69764c3a55e9d3ef10a76f652ffa697794ab91169878116d058420e3", "wy": "00ccd7b9153694151ee2d05048e40fe072d8e0f481af5d3d0a9e8cf39e1ef7e0bb"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040c5baefb69764c3a55e9d3ef10a76f652ffa697794ab91169878116d058420e3ccd7b9153694151ee2d05048e40fe072d8e0f481af5d3d0a9e8cf39e1ef7e0bb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEDFuu+2l2TDpV6dPvEKdvZS/6aXeU\nq5EWmHgRbQWEIOPM17kVNpQVHuLQUEjkD+By2OD0ga9dPQqejPOeHvfguw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022027b4577ca009376f71303fd5dd227dcef5deb773ad5f5a84360644669ca249a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041a4cb39a674bf0bec4be1a5b035ae18634f4b681a330c1f91b42366a0a7c75320b3f8018da54a8d0db30f7c2b3f04dc011a4a1c383221e52187632a565e5795c", "wx": "1a4cb39a674bf0bec4be1a5b035ae18634f4b681a330c1f91b42366a0a7c7532", "wy": "0b3f8018da54a8d0db30f7c2b3f04dc011a4a1c383221e52187632a565e5795c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041a4cb39a674bf0bec4be1a5b035ae18634f4b681a330c1f91b42366a0a7c75320b3f8018da54a8d0db30f7c2b3f04dc011a4a1c383221e52187632a565e5795c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEGkyzmmdL8L7EvhpbA1rhhjT0toGj\nMMH5G0I2agp8dTILP4AY2lSo0Nsw98Kz8E3AEaShw4MiHlIYdjKlZeV5XA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041cd72095fd856cf29fafb81c25f7ff24dee34eeaeacc0025d512091b1f1e822b427eb3bbb915209e064bfbe1a1798ff6dac8d0add6d753bff4f128fee7e00f89", "wx": "1cd72095fd856cf29fafb81c25f7ff24dee34eeaeacc0025d512091b1f1e822b", "wy": "427eb3bbb915209e064bfbe1a1798ff6dac8d0add6d753bff4f128fee7e00f89"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041cd72095fd856cf29fafb81c25f7ff24dee34eeaeacc0025d512091b1f1e822b427eb3bbb915209e064bfbe1a1798ff6dac8d0add6d753bff4f128fee7e00f89", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHNcglf2FbPKfr7gcJff/JN7jTurq\nzAAl1RIJGx8egitCfrO7uRUgngZL++GheY/22sjQrdbXU7/08Sj+5+APiQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04697cd5856c8c347fdfbca4c2cb2fc1be12f1611f190333b80a5cf4e0f7d48dab5d08740936bbc46c90b1da916d5ef39c3d9fb9092f579a43d911472022a7fa90", "wx": "697cd5856c8c347fdfbca4c2cb2fc1be12f1611f190333b80a5cf4e0f7d48dab", "wy": "5d08740936bbc46c90b1da916d5ef39c3d9fb9092f579a43d911472022a7fa90"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004697cd5856c8c347fdfbca4c2cb2fc1be12f1611f190333b80a5cf4e0f7d48dab5d08740936bbc46c90b1da916d5ef39c3d9fb9092f579a43d911472022a7fa90", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaXzVhWyMNH/fvKTCyy/BvhLxYR8Z\nAzO4Clz04PfUjatdCHQJNrvEbJCx2pFtXvOcPZ+5CS9XmkPZEUcgIqf6kA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020105", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469d31ff52dbc0935508755cd48cf3f30f0ea78fb670048983be0ebacf4de1076773c9dce9aa24b783d8688d63547dc987d4650f20c1179e6ae5d4f14f6d55cc1", "wx": "69d31ff52dbc0935508755cd48cf3f30f0ea78fb670048983be0ebacf4de1076", "wy": "773c9dce9aa24b783d8688d63547dc987d4650f20c1179e6ae5d4f14f6d55cc1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000469d31ff52dbc0935508755cd48cf3f30f0ea78fb670048983be0ebacf4de1076773c9dce9aa24b783d8688d63547dc987d4650f20c1179e6ae5d4f14f6d55cc1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEadMf9S28CTVQh1XNSM8/MPDqePtn\nAEiYO+DrrPTeEHZ3PJ3OmqJLeD2GiNY1R9yYfUZQ8gwReeauXU8U9tVcwQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "small r and s", "msg": "313233343030", "sig": "3006020105020106", "result": "valid", "flags": []}, {"tcId": 368, "comment": "r is larger than n", "msg": "313233343030", "sig": "3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632556020106", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0422554b95fcd18cb7cdf7db4a57b259f3d40f5c7cd5cc907a1d0861b3b835fb2f63b92993893f14bf17fb9bdefbcbb9404c1985e7a19699d048483702f7e25547", "wx": "22554b95fcd18cb7cdf7db4a57b259f3d40f5c7cd5cc907a1d0861b3b835fb2f", "wy": "63b92993893f14bf17fb9bdefbcbb9404c1985e7a19699d048483702f7e25547"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000422554b95fcd18cb7cdf7db4a57b259f3d40f5c7cd5cc907a1d0861b3b835fb2f63b92993893f14bf17fb9bdefbcbb9404c1985e7a19699d048483702f7e25547", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIlVLlfzRjLfN99tKV7JZ89QPXHzV\nzJB6HQhhs7g1+y9juSmTiT8Uvxf7m977y7lATBmF56GWmdBISDcC9+JVRw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "s is larger than n", "msg": "313233343030", "sig": "3026020105022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc75fbd8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042f5c464f48423a87806e88e2e6b4e6f947eea1a5f2c0717897406d97dd3c286561b62e969abf04b840be9587a2a16c0a83ff3bf6812b7257c106a26be2e71d25", "wx": "2f5c464f48423a87806e88e2e6b4e6f947eea1a5f2c0717897406d97dd3c2865", "wy": "61b62e969abf04b840be9587a2a16c0a83ff3bf6812b7257c106a26be2e71d25"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042f5c464f48423a87806e88e2e6b4e6f947eea1a5f2c0717897406d97dd3c286561b62e969abf04b840be9587a2a16c0a83ff3bf6812b7257c106a26be2e71d25", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEL1xGT0hCOoeAboji5rTm+UfuoaXy\nwHF4l0Btl908KGVhti6Wmr8EuEC+lYeioWwKg/879oErclfBBqJr4ucdJQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "small r and s^-1", "msg": "313233343030", "sig": "3027020201000221008f1e3c7862c58b16bb76eddbb76eddbb516af4f63f2d74d76e0d28c9bb75ea88", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0427527d9ba351f69285cbd9ba144b9e7c6c957d418c8bf63925fb934ab587d385216a626307e247c3d5bcf489b52ad4c1987973d7d4cc90fb5b1f488d80134656", "wx": "27527d9ba351f69285cbd9ba144b9e7c6c957d418c8bf63925fb934ab587d385", "wy": "216a626307e247c3d5bcf489b52ad4c1987973d7d4cc90fb5b1f488d80134656"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000427527d9ba351f69285cbd9ba144b9e7c6c957d418c8bf63925fb934ab587d385216a626307e247c3d5bcf489b52ad4c1987973d7d4cc90fb5b1f488d80134656", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEJ1J9m6NR9pKFy9m6FEuefGyVfUGM\ni/Y5JfuTSrWH04UhamJjB+JHw9W89Im1KtTBmHlz19TMkPtbH0iNgBNGVg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302c02072d9b4d347952d6022100ef3043e7329581dbb3974497710ab11505ee1c87ff907beebadd195a0ffe6d7a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041bfc106d79112f08439a4ffa4e06261bbab3031ff7ed32568172caaeabae2f90b894ecc8922ace776ed6d1526b7771f2cc43b0e84bac400541619e142319059f", "wx": "1bfc106d79112f08439a4ffa4e06261bbab3031ff7ed32568172caaeabae2f90", "wy": "00b894ecc8922ace776ed6d1526b7771f2cc43b0e84bac400541619e142319059f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041bfc106d79112f08439a4ffa4e06261bbab3031ff7ed32568172caaeabae2f90b894ecc8922ace776ed6d1526b7771f2cc43b0e84bac400541619e142319059f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEG/wQbXkRLwhDmk/6TgYmG7qzAx/3\n7TJWgXLKrquuL5C4lOzIkirOd27W0VJrd3HyzEOw6EusQAVBYZ4UIxkFnw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3032020d1033e67e37b32b445580bf4eff0221008b748b74000000008b748b748b748b7466e769ad4a16d3dcd87129b8e91d1b4d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0471e13191229197eefe9224be11217878635056fd8e558b74121036043cb7501709a09bc004ffc98a4051a3fb9798cb9fd5c17919ecab9ff8459a7d561f3058b2", "wx": "71e13191229197eefe9224be11217878635056fd8e558b74121036043cb75017", "wy": "09a09bc004ffc98a4051a3fb9798cb9fd5c17919ecab9ff8459a7d561f3058b2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000471e13191229197eefe9224be11217878635056fd8e558b74121036043cb7501709a09bc004ffc98a4051a3fb9798cb9fd5c17919ecab9ff8459a7d561f3058b2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEceExkSKRl+7+kiS+ESF4eGNQVv2O\nVYt0EhA2BDy3UBcJoJvABP/JikBRo/uXmMuf1cF5Geyrn/hFmn1WHzBYsg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302702020100022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0415b8867217bf4d531a5618509d9cddc6a3ff4fcc55cc94df82d9952b42ae564d7234d5c43238e591501c1fada39a60057fefb46b588d47cbedb529def080fb83", "wx": "15b8867217bf4d531a5618509d9cddc6a3ff4fcc55cc94df82d9952b42ae564d", "wy": "7234d5c43238e591501c1fada39a60057fefb46b588d47cbedb529def080fb83"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000415b8867217bf4d531a5618509d9cddc6a3ff4fcc55cc94df82d9952b42ae564d7234d5c43238e591501c1fada39a60057fefb46b588d47cbedb529def080fb83", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEFbiGche/TVMaVhhQnZzdxqP/T8xV\nzJTfgtmVK0KuVk1yNNXEMjjlkVAcH62jmmAFf++0a1iNR8vttSne8ID7gw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3032020d062522bbd3ecbe7c39e93e7c25022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e7c1fcc775b8d46770261c413291bc9d913c7785779870eb475f7437da3ee1a016ad8986f7ef63d4237a9c802e5e49471d248e4df64283e77608ee191ec61f3f", "wx": "00e7c1fcc775b8d46770261c413291bc9d913c7785779870eb475f7437da3ee1a0", "wy": "16ad8986f7ef63d4237a9c802e5e49471d248e4df64283e77608ee191ec61f3f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004e7c1fcc775b8d46770261c413291bc9d913c7785779870eb475f7437da3ee1a016ad8986f7ef63d4237a9c802e5e49471d248e4df64283e77608ee191ec61f3f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE58H8x3W41GdwJhxBMpG8nZE8d4V3\nmHDrR190N9o+4aAWrYmG9+9j1CN6nIAuXklHHSSOTfZCg+d2CO4ZHsYfPw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3045022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324d50220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cfac11bd4b7aa0a29309767eddd337d302f1f42ccab215171f9622992a671e4a1451102ad100a24e6fb5cb130c69de2a61fa50f2d07a099d73ae96f971593588", "wx": "00cfac11bd4b7aa0a29309767eddd337d302f1f42ccab215171f9622992a671e4a", "wy": "1451102ad100a24e6fb5cb130c69de2a61fa50f2d07a099d73ae96f971593588"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cfac11bd4b7aa0a29309767eddd337d302f1f42ccab215171f9622992a671e4a1451102ad100a24e6fb5cb130c69de2a61fa50f2d07a099d73ae96f971593588", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEz6wRvUt6oKKTCXZ+3dM30wLx9CzK\nshUXH5YimSpnHkoUURAq0QCiTm+1yxMMad4qYfpQ8tB6CZ1zrpb5cVk1iA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "s == 1", "msg": "313233343030", "sig": "30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020101", "result": "valid", "flags": []}, {"tcId": 377, "comment": "s == 0", "msg": "313233343030", "sig": "30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ca99e8cacb96b9b73a7ecf478617269f08971c307aba958692381696c244443b5c5df1f740db3016b0ad298997131cea685b2ba405c3b0f722c6992bdaa6fd3a", "wx": "00ca99e8cacb96b9b73a7ecf478617269f08971c307aba958692381696c244443b", "wy": "5c5df1f740db3016b0ad298997131cea685b2ba405c3b0f722c6992bdaa6fd3a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ca99e8cacb96b9b73a7ecf478617269f08971c307aba958692381696c244443b5c5df1f740db3016b0ad298997131cea685b2ba405c3b0f722c6992bdaa6fd3a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEypnoysuWubc6fs9HhhcmnwiXHDB6\nupWGkjgWlsJERDtcXfH3QNswFrCtKYmXExzqaFsrpAXDsPcixpkr2qb9Og==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a80220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043c31be015178a9c3d3cae9103bf25bc11a709316d06ab46c01f884a8eb33da2e91f32a1352712508642c4774ea67049175161cb2bafd5524b813274b140f8e99", "wx": "3c31be015178a9c3d3cae9103bf25bc11a709316d06ab46c01f884a8eb33da2e", "wy": "0091f32a1352712508642c4774ea67049175161cb2bafd5524b813274b140f8e99"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043c31be015178a9c3d3cae9103bf25bc11a709316d06ab46c01f884a8eb33da2e91f32a1352712508642c4774ea67049175161cb2bafd5524b813274b140f8e99", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPDG+AVF4qcPTyukQO/JbwRpwkxbQ\narRsAfiEqOsz2i6R8yoTUnElCGQsR3TqZwSRdRYcsrr9VSS4EydLFA+OmQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04355d9c3f97a282ac17f7bc98373f271d6a2bc02eb964f13c1e2d7debff4a02fd63282e78fb9b88f81413bcf95f16982d9f50e7f94a5d28685b41da997201db5e", "wx": "355d9c3f97a282ac17f7bc98373f271d6a2bc02eb964f13c1e2d7debff4a02fd", "wy": "63282e78fb9b88f81413bcf95f16982d9f50e7f94a5d28685b41da997201db5e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004355d9c3f97a282ac17f7bc98373f271d6a2bc02eb964f13c1e2d7debff4a02fd63282e78fb9b88f81413bcf95f16982d9f50e7f94a5d28685b41da997201db5e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENV2cP5eigqwX97yYNz8nHWorwC65\nZPE8Hi196/9KAv1jKC54+5uI+BQTvPlfFpgtn1Dn+UpdKGhbQdqZcgHbXg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041c959964cabaa5ce966b95acda1c5264847a780426c878d716d73ae6621d3084d3fedfc4f9c3b8cc8bc6539b821ad208ec08d5737aaaf1801d666ddc37e54faf", "wx": "1c959964cabaa5ce966b95acda1c5264847a780426c878d716d73ae6621d3084", "wy": "00d3fedfc4f9c3b8cc8bc6539b821ad208ec08d5737aaaf1801d666ddc37e54faf"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041c959964cabaa5ce966b95acda1c5264847a780426c878d716d73ae6621d3084d3fedfc4f9c3b8cc8bc6539b821ad208ec08d5737aaaf1801d666ddc37e54faf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHJWZZMq6pc6Wa5Ws2hxSZIR6eAQm\nyHjXFtc65mIdMITT/t/E+cO4zIvGU5uCGtII7AjVc3qq8YAdZm3cN+VPrw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "u1 == 1", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c700220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049842a82a83932d5a44daab14afb79968dd24d9ffc58e638586a90b0f25b521dd38b2c05c8d548bcf5ab2a906e2f3fbdde7f0b9bbdecb852297d55ae34257f8f3", "wx": "009842a82a83932d5a44daab14afb79968dd24d9ffc58e638586a90b0f25b521dd", "wy": "38b2c05c8d548bcf5ab2a906e2f3fbdde7f0b9bbdecb852297d55ae34257f8f3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049842a82a83932d5a44daab14afb79968dd24d9ffc58e638586a90b0f25b521dd38b2c05c8d548bcf5ab2a906e2f3fbdde7f0b9bbdecb852297d55ae34257f8f3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEmEKoKoOTLVpE2qsUr7eZaN0k2f/F\njmOFhqkLDyW1Id04ssBcjVSLz1qyqQbi8/vd5/C5u97LhSKX1VrjQlf48w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100cbd25189e59c5c367e7630cbd4c4cb1512c194cadf353d6331f9f57fa81b338e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04987a839ec570964d3a1bfc6a0334e7328c1624677c66b8fef3a5e64d1178ddae5577dfcd0fe00da9e1d8bb8bbd952aaa5bc10ecb14cae4e3f5e28c506bbc3a21", "wx": "00987a839ec570964d3a1bfc6a0334e7328c1624677c66b8fef3a5e64d1178ddae", "wy": "5577dfcd0fe00da9e1d8bb8bbd952aaa5bc10ecb14cae4e3f5e28c506bbc3a21"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004987a839ec570964d3a1bfc6a0334e7328c1624677c66b8fef3a5e64d1178ddae5577dfcd0fe00da9e1d8bb8bbd952aaa5bc10ecb14cae4e3f5e28c506bbc3a21", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEmHqDnsVwlk06G/xqAzTnMowWJGd8\nZrj+86XmTRF43a5Vd9/ND+ANqeHYu4u9lSqqW8EOyxTK5OP14oxQa7w6IQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "u2 == 1", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c700220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ab6590ad5134f8e1bf4581cf90ca0398ceb92861c2af06928cc0303d9654ca3e1d821f6a357dc173f22a3b77145c057a632bb56f31514e31d6d2ed7fddeade55", "wx": "00ab6590ad5134f8e1bf4581cf90ca0398ceb92861c2af06928cc0303d9654ca3e", "wy": "1d821f6a357dc173f22a3b77145c057a632bb56f31514e31d6d2ed7fddeade55"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ab6590ad5134f8e1bf4581cf90ca0398ceb92861c2af06928cc0303d9654ca3e1d821f6a357dc173f22a3b77145c057a632bb56f31514e31d6d2ed7fddeade55", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEq2WQrVE0+OG/RYHPkMoDmM65KGHC\nrwaSjMAwPZZUyj4dgh9qNX3Bc/IqO3cUXAV6Yyu1bzFRTjHW0u1/3ereVQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100aaaaaaaa00000000aaaaaaaaaaaaaaaa7def51c91a0fbf034d26872ca84218e1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cde41230f0cdf732ac76e5f284cb7f46915ed309b48debf245f3ff2f243e9366240da36ca5ad289bdfea83213847b14a73079705eab0a2a28aefc999e5ed1504", "wx": "00cde41230f0cdf732ac76e5f284cb7f46915ed309b48debf245f3ff2f243e9366", "wy": "240da36ca5ad289bdfea83213847b14a73079705eab0a2a28aefc999e5ed1504"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cde41230f0cdf732ac76e5f284cb7f46915ed309b48debf245f3ff2f243e9366240da36ca5ad289bdfea83213847b14a73079705eab0a2a28aefc999e5ed1504", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEzeQSMPDN9zKsduXyhMt/RpFe0wm0\njevyRfP/LyQ+k2YkDaNspa0om9/qgyE4R7FKcweXBeqwoqKK78mZ5e0VBA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bc0f3a265e2136998083451163be66f8b6a673bf5cb08a0e8dbbce4319af6977", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0436c6faa9f6dca42ea8b0e6d02d36c3941582749251bfa88c35fc8a8e9ad51cdac44ce5f20f36b4ae75cc7291678dbff188cf8c838b8963eeb8b78891795a7364", "wx": "36c6faa9f6dca42ea8b0e6d02d36c3941582749251bfa88c35fc8a8e9ad51cda", "wy": "00c44ce5f20f36b4ae75cc7291678dbff188cf8c838b8963eeb8b78891795a7364"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000436c6faa9f6dca42ea8b0e6d02d36c3941582749251bfa88c35fc8a8e9ad51cdac44ce5f20f36b4ae75cc7291678dbff188cf8c838b8963eeb8b78891795a7364", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENsb6qfbcpC6osObQLTbDlBWCdJJR\nv6iMNfyKjprVHNrETOXyDza0rnXMcpFnjb/xiM+Mg4uJY+64t4iReVpzZA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02202a06d743967a1330ca91cc46c9394512cc85b4f1b9355eb7f4bbe53a32b76cf4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04709e58a12b12034173395bf91e68255d90d8a3a1d5875c75f87787d9a85a092980e09b79f43489be726a4253ae6c05adce71bcc65389254923cbfacc0775bff6", "wx": "709e58a12b12034173395bf91e68255d90d8a3a1d5875c75f87787d9a85a0929", "wy": "0080e09b79f43489be726a4253ae6c05adce71bcc65389254923cbfacc0775bff6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004709e58a12b12034173395bf91e68255d90d8a3a1d5875c75f87787d9a85a092980e09b79f43489be726a4253ae6c05adce71bcc65389254923cbfacc0775bff6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcJ5YoSsSA0FzOVv5HmglXZDYo6HV\nh1x1+HeH2ahaCSmA4Jt59DSJvnJqQlOubAWtznG8xlOJJUkjy/rMB3W/9g==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221008a457017b290d6edb5455e167b9944f67dc5ba958c7c8108ac56e8596b9bc9a1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0486664a9e09db80dbe3bd0158cc4a64a2cded9c852b455f4443e97cca5569f7cc7454f96b52a27a43a1345b5d340902e147880adaf2fe691c168b4203ba2e1df4", "wx": "0086664a9e09db80dbe3bd0158cc4a64a2cded9c852b455f4443e97cca5569f7cc", "wy": "7454f96b52a27a43a1345b5d340902e147880adaf2fe691c168b4203ba2e1df4"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000486664a9e09db80dbe3bd0158cc4a64a2cded9c852b455f4443e97cca5569f7cc7454f96b52a27a43a1345b5d340902e147880adaf2fe691c168b4203ba2e1df4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhmZKngnbgNvjvQFYzEpkos3tnIUr\nRV9EQ+l8ylVp98x0VPlrUqJ6Q6E0W100CQLhR4gK2vL+aRwWi0IDui4d9A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100cbfaf812124638c0b98c18cac3dd8d0848ff921f26aec42a708df7b6a97571c7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04623c668c5dbae406e7b4e8e97b2bba2028586f2e3c31f694d9fe87a3de29f8433ac7eb04891898dd9077432c38cc978049cab7721630ed033095f850ade83e3e", "wx": "623c668c5dbae406e7b4e8e97b2bba2028586f2e3c31f694d9fe87a3de29f843", "wy": "3ac7eb04891898dd9077432c38cc978049cab7721630ed033095f850ade83e3e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004623c668c5dbae406e7b4e8e97b2bba2028586f2e3c31f694d9fe87a3de29f8433ac7eb04891898dd9077432c38cc978049cab7721630ed033095f850ade83e3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEYjxmjF265AbntOjpeyu6IChYby48\nMfaU2f6Ho94p+EM6x+sEiRiY3ZB3Qyw4zJeAScq3chYw7QMwlfhQreg+Pg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100af812de3638bfed9c18cac3dd8d087e7350cc7062635266a525bff486363cc91", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b034897e2a74a787e008b14c62882eaee0e6e53a1c8a58f709a54432713c20fd28e07de2ca64b5e215d25d0530d7807df11b98bb39c26a2405388ac0b163fafc", "wx": "00b034897e2a74a787e008b14c62882eaee0e6e53a1c8a58f709a54432713c20fd", "wy": "28e07de2ca64b5e215d25d0530d7807df11b98bb39c26a2405388ac0b163fafc"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b034897e2a74a787e008b14c62882eaee0e6e53a1c8a58f709a54432713c20fd28e07de2ca64b5e215d25d0530d7807df11b98bb39c26a2405388ac0b163fafc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsDSJfip0p4fgCLFMYoguruDm5Toc\nilj3CaVEMnE8IP0o4H3iymS14hXSXQUw14B98RuYuznCaiQFOIrAsWP6/A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205f025bc7c717fdb28319587bb1a10fcead32935ea552ae4fb0fe33cdca6473d1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0414c4f6210b628edd441651d3cb61a07f7ca87629a9e0add5a4fd92902a20cd255146bebdb22b4d49527ebc90ba6294784ae7ca664724d45a466c9fe8b8699bbf", "wx": "14c4f6210b628edd441651d3cb61a07f7ca87629a9e0add5a4fd92902a20cd25", "wy": "5146bebdb22b4d49527ebc90ba6294784ae7ca664724d45a466c9fe8b8699bbf"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000414c4f6210b628edd441651d3cb61a07f7ca87629a9e0add5a4fd92902a20cd255146bebdb22b4d49527ebc90ba6294784ae7ca664724d45a466c9fe8b8699bbf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEFMT2IQtijt1EFlHTy2Ggf3yodimp\n4K3VpP2SkCogzSVRRr69sitNSVJ+vJC6YpR4SufKZkck1FpGbJ/ouGmbvw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100de4130d1ed9120b8c3dd8d087e7630cb9a8402519439af08e96b342788c6e84e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b5408eefbdb7480fad4c1735f378c9a1ded28cf476835f27b6fd3b8af5e4c596a60a806e244c63a798baf2fd5a66c6c0d7f77952ba2d74b0fe3652811caac6f0", "wx": "00b5408eefbdb7480fad4c1735f378c9a1ded28cf476835f27b6fd3b8af5e4c596", "wy": "00a60a806e244c63a798baf2fd5a66c6c0d7f77952ba2d74b0fe3652811caac6f0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b5408eefbdb7480fad4c1735f378c9a1ded28cf476835f27b6fd3b8af5e4c596a60a806e244c63a798baf2fd5a66c6c0d7f77952ba2d74b0fe3652811caac6f0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtUCO7723SA+tTBc183jJod7SjPR2\ng18ntv07ivXkxZamCoBuJExjp5i68v1aZsbA1/d5UrotdLD+NlKBHKrG8A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ec618e7b1af40be0d052f5a020388fc6cafb822933c1bffab137885f52e89d3e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04de9e22bc58f4b9c913e7f7c26fcbc5d7f6bdc9fced5f520ac88e8a03290be403746a125844509d79840e35440b312dbfdf4ab8ab0011e7c36bc9e01201b1b0e0", "wx": "00de9e22bc58f4b9c913e7f7c26fcbc5d7f6bdc9fced5f520ac88e8a03290be403", "wy": "746a125844509d79840e35440b312dbfdf4ab8ab0011e7c36bc9e01201b1b0e0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004de9e22bc58f4b9c913e7f7c26fcbc5d7f6bdc9fced5f520ac88e8a03290be403746a125844509d79840e35440b312dbfdf4ab8ab0011e7c36bc9e01201b1b0e0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE3p4ivFj0uckT5/fCb8vF1/a9yfzt\nX1IKyI6KAykL5AN0ahJYRFCdeYQONUQLMS2/30q4qwAR58NryeASAbGw4A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100d39813c8102fe0f18cbfac4b0fa028e6e48c093c59ea00dc99fad50f2d614a3d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ee3bfd2e1c3d99acd88dbf6fc9a3a8f5686b50a6a3e9215f57e83a81389836a5f8ebff9a3e463d47ec36259fdde694bdbb880b0a09fe9ac649d691646ba624f6", "wx": "00ee3bfd2e1c3d99acd88dbf6fc9a3a8f5686b50a6a3e9215f57e83a81389836a5", "wy": "00f8ebff9a3e463d47ec36259fdde694bdbb880b0a09fe9ac649d691646ba624f6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004ee3bfd2e1c3d99acd88dbf6fc9a3a8f5686b50a6a3e9215f57e83a81389836a5f8ebff9a3e463d47ec36259fdde694bdbb880b0a09fe9ac649d691646ba624f6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE7jv9Lhw9mazYjb9vyaOo9WhrUKaj\n6SFfV+g6gTiYNqX46/+aPkY9R+w2JZ/d5pS9u4gLCgn+msZJ1pFka6Yk9g==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100e9fcbcad906698e02d9aba57b3c4e9eefeaf3cec7a980c422fe7f6303fc4a3ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043c0c4d0ad0848d1335b12a8919d32fafc5f2b0d5c2f3cf6494b06ba8eb17cb29dd7054095454fdb519279cde10259ec5d9e5e4b5a8c4daca649a150f2dbf21ac", "wx": "3c0c4d0ad0848d1335b12a8919d32fafc5f2b0d5c2f3cf6494b06ba8eb17cb29", "wy": "00dd7054095454fdb519279cde10259ec5d9e5e4b5a8c4daca649a150f2dbf21ac"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043c0c4d0ad0848d1335b12a8919d32fafc5f2b0d5c2f3cf6494b06ba8eb17cb29dd7054095454fdb519279cde10259ec5d9e5e4b5a8c4daca649a150f2dbf21ac", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPAxNCtCEjRM1sSqJGdMvr8XysNXC\n889klLBrqOsXyyndcFQJVFT9tRknnN4QJZ7F2eXktajE2spkmhUPLb8hrA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022021f862ec50ef64b3bfbe5d774e20cc838320437725338a3b32fefe3ff159dded", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046cf91440818ea262b9376e23f7b530b9e44edf5bc6db1a67dd60cc722975d62317c19e7d6ebc0cbdc89388242d604ae94ac4ca179ef2cd8bf33fbf738afcb39b", "wx": "6cf91440818ea262b9376e23f7b530b9e44edf5bc6db1a67dd60cc722975d623", "wy": "17c19e7d6ebc0cbdc89388242d604ae94ac4ca179ef2cd8bf33fbf738afcb39b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046cf91440818ea262b9376e23f7b530b9e44edf5bc6db1a67dd60cc722975d62317c19e7d6ebc0cbdc89388242d604ae94ac4ca179ef2cd8bf33fbf738afcb39b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEbPkUQIGOomK5N24j97UwueRO31vG\n2xpn3WDMcil11iMXwZ59brwMvciTiCQtYErpSsTKF57yzYvzP79zivyzmw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022043f0c5d8a1dec9677f7cbaee9c419907064086ee4a67147665fdfc7fe2b3bbda", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041b5afcf39888d2da7215f36421a85c4217b0ac883bf5957c2d066efd8bc89f18d8fb3b6aca0b3577a883948e016905065a3fdc13d6f433172ee0f16784e52c78", "wx": "1b5afcf39888d2da7215f36421a85c4217b0ac883bf5957c2d066efd8bc89f18", "wy": "00d8fb3b6aca0b3577a883948e016905065a3fdc13d6f433172ee0f16784e52c78"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041b5afcf39888d2da7215f36421a85c4217b0ac883bf5957c2d066efd8bc89f18d8fb3b6aca0b3577a883948e016905065a3fdc13d6f433172ee0f16784e52c78", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEG1r885iI0tpyFfNkIahcQhewrIg7\n9ZV8LQZu/YvInxjY+ztqygs1d6iDlI4BaQUGWj/cE9b0Mxcu4PFnhOUseA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022065e928c4f2ce2e1b3f3b1865ea62658a8960ca656f9a9eb198fcfabfd40d99c7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04890d36db646c27c2e6c7bc7fdc3f0cbf66fab36d13279b9ecf6e98457d8cf49207a83cfda8e9d0375404c6cbff66792eef97220239254000b4bf983e6bae26d7", "wx": "00890d36db646c27c2e6c7bc7fdc3f0cbf66fab36d13279b9ecf6e98457d8cf492", "wy": "07a83cfda8e9d0375404c6cbff66792eef97220239254000b4bf983e6bae26d7"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004890d36db646c27c2e6c7bc7fdc3f0cbf66fab36d13279b9ecf6e98457d8cf49207a83cfda8e9d0375404c6cbff66792eef97220239254000b4bf983e6bae26d7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiQ0222RsJ8Lmx7x/3D8Mv2b6s20T\nJ5uez26YRX2M9JIHqDz9qOnQN1QExsv/Znku75ciAjklQAC0v5g+a64m1w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100c522b80b59486b775aa2af0b3dcca27b1d565aa199ca0fc6d008598e33ff7779", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042a1571ed0ffe39574d030b406f638bf88eea5b4b50754e93431fe0172fdf2fbcbc77f601dc6dbc88cc2b560e8cca5e738f2c769810b2c8762dab917adfe24535", "wx": "2a1571ed0ffe39574d030b406f638bf88eea5b4b50754e93431fe0172fdf2fbc", "wy": "00bc77f601dc6dbc88cc2b560e8cca5e738f2c769810b2c8762dab917adfe24535"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042a1571ed0ffe39574d030b406f638bf88eea5b4b50754e93431fe0172fdf2fbcbc77f601dc6dbc88cc2b560e8cca5e738f2c769810b2c8762dab917adfe24535", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKhVx7Q/+OVdNAwtAb2OL+I7qW0tQ\ndU6TQx/gFy/fL7y8d/YB3G28iMwrVg6Myl5zjyx2mBCyyHYtq5F63+JFNQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffffaaaaaaaaffffffffffffffffe9a2538f37b28a2c513dee40fecbb71a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04612b4467c8b3b8ecbf5374997ec7db8cab2bda9e431982c49727f3fcefb10b47b9d1ecc026c3665425730128138c4e181c61ec28b38910ca59e5fc496ec31f08", "wx": "612b4467c8b3b8ecbf5374997ec7db8cab2bda9e431982c49727f3fcefb10b47", "wy": "00b9d1ecc026c3665425730128138c4e181c61ec28b38910ca59e5fc496ec31f08"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004612b4467c8b3b8ecbf5374997ec7db8cab2bda9e431982c49727f3fcefb10b47b9d1ecc026c3665425730128138c4e181c61ec28b38910ca59e5fc496ec31f08", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEYStEZ8izuOy/U3SZfsfbjKsr2p5D\nGYLElyfz/O+xC0e50ezAJsNmVCVzASgTjE4YHGHsKLOJEMpZ5fxJbsMfCA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100b62f26b5f2a2b26f6de86d42ad8a13da3ab3cccd0459b201de009e526adf21f2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0434191b6ce8865c2230a514b61e2b2730c94beb072e9de309872aea3743bb3e2722d202fc59984e7421a25e6a82664b5080f72ab28df9c0af4e1e300af11ae9b0", "wx": "34191b6ce8865c2230a514b61e2b2730c94beb072e9de309872aea3743bb3e27", "wy": "22d202fc59984e7421a25e6a82664b5080f72ab28df9c0af4e1e300af11ae9b0"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000434191b6ce8865c2230a514b61e2b2730c94beb072e9de309872aea3743bb3e2722d202fc59984e7421a25e6a82664b5080f72ab28df9c0af4e1e300af11ae9b0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAENBkbbOiGXCIwpRS2HisnMMlL6wcu\nneMJhyrqN0O7Pici0gL8WZhOdCGiXmqCZktQgPcqso35wK9OHjAK8RrpsA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bb1d9ac949dd748cd02bbbe749bd351cd57b38bb61403d700686aa7b4c90851e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d533c620a8fd3d5a8342caba89eadc25907d1e9b6fea48ee8f806aa772f0c80f70e09c022fa1139da32a456ec024949824477bf0bdbf603e8faccd6b205d263c", "wx": "00d533c620a8fd3d5a8342caba89eadc25907d1e9b6fea48ee8f806aa772f0c80f", "wy": "70e09c022fa1139da32a456ec024949824477bf0bdbf603e8faccd6b205d263c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004d533c620a8fd3d5a8342caba89eadc25907d1e9b6fea48ee8f806aa772f0c80f70e09c022fa1139da32a456ec024949824477bf0bdbf603e8faccd6b205d263c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE1TPGIKj9PVqDQsq6iercJZB9Hptv\n6kjuj4Bqp3LwyA9w4JwCL6ETnaMqRW7AJJSYJEd78L2/YD6PrM1rIF0mPA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022066755a00638cdaec1c732513ca0234ece52545dac11f816e818f725b4f60aaf2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0491014bea8705a022972e12b176c96e84c0b61c05eb1c6f8c5c1db731d54d67e29060ef6c764d8d47ed5ebf8f6c23835cb89f8b056cdf7e457f9273b6477ece33", "wx": "0091014bea8705a022972e12b176c96e84c0b61c05eb1c6f8c5c1db731d54d67e2", "wy": "009060ef6c764d8d47ed5ebf8f6c23835cb89f8b056cdf7e457f9273b6477ece33"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000491014bea8705a022972e12b176c96e84c0b61c05eb1c6f8c5c1db731d54d67e29060ef6c764d8d47ed5ebf8f6c23835cb89f8b056cdf7e457f9273b6477ece33", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEkQFL6ocFoCKXLhKxdsluhMC2HAXr\nHG+MXB23MdVNZ+KQYO9sdk2NR+1ev49sI4NcuJ+LBWzffkV/knO2R37OMw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022055a00c9fcdaebb6032513ca0234ecfffe98ebe492fdf02e48ca48e982beb3669", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04287a7f8edfbb55cbb56d6b7be5b96c410a85a35bc6639661a92bd653e1f59688bbd133a77828493b3e0f867f34acfcac099415399a6b1106a0f9420c06f8bf94", "wx": "287a7f8edfbb55cbb56d6b7be5b96c410a85a35bc6639661a92bd653e1f59688", "wy": "00bbd133a77828493b3e0f867f34acfcac099415399a6b1106a0f9420c06f8bf94"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004287a7f8edfbb55cbb56d6b7be5b96c410a85a35bc6639661a92bd653e1f59688bbd133a77828493b3e0f867f34acfcac099415399a6b1106a0f9420c06f8bf94", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKHp/jt+7Vcu1bWt75blsQQqFo1vG\nY5ZhqSvWU+H1loi70TOneChJOz4Phn80rPysCZQVOZprEQag+UIMBvi/lA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ab40193f9b5d76c064a27940469d9fffd31d7c925fbe05c919491d3057d66cd2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0482eca524c9ee8475ec8948721a9409b5090c6c28866d0c12669bd5cb7e685a58066b1e7135946a425ddd228076ea24d131b9bd2eae6b51c8083857628f260b80", "wx": "0082eca524c9ee8475ec8948721a9409b5090c6c28866d0c12669bd5cb7e685a58", "wy": "066b1e7135946a425ddd228076ea24d131b9bd2eae6b51c8083857628f260b80"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000482eca524c9ee8475ec8948721a9409b5090c6c28866d0c12669bd5cb7e685a58066b1e7135946a425ddd228076ea24d131b9bd2eae6b51c8083857628f260b80", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEguylJMnuhHXsiUhyGpQJtQkMbCiG\nbQwSZpvVy35oWlgGax5xNZRqQl3dIoB26iTRMbm9Lq5rUcgIOFdijyYLgA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ca0234ebb5fdcb13ca0234ecffffffffcb0dadbbc7f549f8a26b4408d0dc8600", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049cc512b58d5b921f91075441b0df61e0381459de703a84523cda31dc18549ff85647dd4bd39f6761a144d81ef39db7bc0dedbcb15bdaf084e3a10fdd10bd906a", "wx": "009cc512b58d5b921f91075441b0df61e0381459de703a84523cda31dc18549ff8", "wy": "5647dd4bd39f6761a144d81ef39db7bc0dedbcb15bdaf084e3a10fdd10bd906a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200049cc512b58d5b921f91075441b0df61e0381459de703a84523cda31dc18549ff85647dd4bd39f6761a144d81ef39db7bc0dedbcb15bdaf084e3a10fdd10bd906a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEnMUStY1bkh+RB1RBsN9h4DgUWd5w\nOoRSPNox3BhUn/hWR91L059nYaFE2B7znbe8De28sVva8ITjoQ/dEL2Qag==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff3ea3677e082b9310572620ae19933a9e65b285598711c77298815ad3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04aa9d33c76ccd011ba23047a420840374b4fa3be480c65263d543baf2ccc6141e5ed3b42ad9527869fae92914b82b952d2c31c8fcc85b4c100983096694285766", "wx": "00aa9d33c76ccd011ba23047a420840374b4fa3be480c65263d543baf2ccc6141e", "wy": "5ed3b42ad9527869fae92914b82b952d2c31c8fcc85b4c100983096694285766"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004aa9d33c76ccd011ba23047a420840374b4fa3be480c65263d543baf2ccc6141e5ed3b42ad9527869fae92914b82b952d2c31c8fcc85b4c100983096694285766", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEqp0zx2zNARuiMEekIIQDdLT6O+SA\nxlJj1UO68szGFB5e07Qq2VJ4afrpKRS4K5UtLDHI/MhbTBAJgwlmlChXZg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220266666663bbbbbbbe6666666666666665b37902e023fab7c8f055d86e5cc41f4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042812996934db1357048a1aacb07cb2a8730fbe530984a7a5166f84748ff63e4ce6e8a80e235e216e1a9e75ba3b3a321af51d2e4524ad3c8be99288463b91155a", "wx": "2812996934db1357048a1aacb07cb2a8730fbe530984a7a5166f84748ff63e4c", "wy": "00e6e8a80e235e216e1a9e75ba3b3a321af51d2e4524ad3c8be99288463b91155a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042812996934db1357048a1aacb07cb2a8730fbe530984a7a5166f84748ff63e4ce6e8a80e235e216e1a9e75ba3b3a321af51d2e4524ad3c8be99288463b91155a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKBKZaTTbE1cEihqssHyyqHMPvlMJ\nhKelFm+EdI/2Pkzm6KgOI14hbhqedbo7OjIa9R0uRSStPIvpkohGO5EVWg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff36db6db7a492492492492492146c573f4c6dfc8d08a443e258970b09", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048286d34afa1904ae756d73bf02a6b6a9db1eaa1b8351ad713a786dcfb829135bf97922dfe80cd4f0e438a8d842e7666853436d972860f715e622a1e876db4251", "wx": "008286d34afa1904ae756d73bf02a6b6a9db1eaa1b8351ad713a786dcfb829135b", "wy": "00f97922dfe80cd4f0e438a8d842e7666853436d972860f715e622a1e876db4251"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048286d34afa1904ae756d73bf02a6b6a9db1eaa1b8351ad713a786dcfb829135bf97922dfe80cd4f0e438a8d842e7666853436d972860f715e622a1e876db4251", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEgobTSvoZBK51bXO/Aqa2qdseqhuD\nUa1xOnhtz7gpE1v5eSLf6AzU8OQ4qNhC52ZoU0Ntlyhg9xXmIqHodttCUQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff2aaaaaab7fffffffffffffffc815d0e60b3e596ecb1ad3a27cfd49c4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044b7b9990a6c2c5a810107c09ee09cf3388c1a6e82aaa44c378d9886e2508c2e0867e7632fcc312fcfdc01fb6a579ce6aa6285563b1adbb3272f0e122f9de73e3", "wx": "4b7b9990a6c2c5a810107c09ee09cf3388c1a6e82aaa44c378d9886e2508c2e0", "wy": "00867e7632fcc312fcfdc01fb6a579ce6aa6285563b1adbb3272f0e122f9de73e3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044b7b9990a6c2c5a810107c09ee09cf3388c1a6e82aaa44c378d9886e2508c2e0867e7632fcc312fcfdc01fb6a579ce6aa6285563b1adbb3272f0e122f9de73e3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAES3uZkKbCxagQEHwJ7gnPM4jBpugq\nqkTDeNmIbiUIwuCGfnYy/MMS/P3AH7alec5qpihVY7GtuzJy8OEi+d5z4w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffff55555555ffffffffffffffffd344a71e6f651458a27bdc81fd976e37", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04568426540e19be6404e6cb4745fc7a943c0c059d7c7baf3bc4fc782e9aedf2deba7fb20ad12a886c53945d3cdb019afa8b0d58ed307d55f556acf79ec89012d5", "wx": "568426540e19be6404e6cb4745fc7a943c0c059d7c7baf3bc4fc782e9aedf2de", "wy": "00ba7fb20ad12a886c53945d3cdb019afa8b0d58ed307d55f556acf79ec89012d5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004568426540e19be6404e6cb4745fc7a943c0c059d7c7baf3bc4fc782e9aedf2deba7fb20ad12a886c53945d3cdb019afa8b0d58ed307d55f556acf79ec89012d5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVoQmVA4ZvmQE5stHRfx6lDwMBZ18\ne687xPx4Lprt8t66f7IK0SqIbFOUXTzbAZr6iw1Y7TB9VfVWrPeeyJAS1Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02203fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192aa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041bebc1a6823107d1263219e36d2ec1957d20b0b450b481c9de46ea9294d48b6672b7a811af9528e61199f4a2a7f30d8685f5a04767b59276e65a732e8f3950a1", "wx": "1bebc1a6823107d1263219e36d2ec1957d20b0b450b481c9de46ea9294d48b66", "wy": "72b7a811af9528e61199f4a2a7f30d8685f5a04767b59276e65a732e8f3950a1"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200041bebc1a6823107d1263219e36d2ec1957d20b0b450b481c9de46ea9294d48b6672b7a811af9528e61199f4a2a7f30d8685f5a04767b59276e65a732e8f3950a1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEG+vBpoIxB9EmMhnjbS7BlX0gsLRQ\ntIHJ3kbqkpTUi2Zyt6gRr5Uo5hGZ9KKn8w2GhfWgR2e1knbmWnMujzlQoQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205d8ecd64a4eeba466815ddf3a4de9a8e6abd9c5db0a01eb80343553da648428f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0408a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934478da818b4d94082517fa9635a8aa5be05323de604fcfa97bc3a1a57a5e80c34", "wx": "08a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934", "wy": "478da818b4d94082517fa9635a8aa5be05323de604fcfa97bc3a1a57a5e80c34"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000408a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934478da818b4d94082517fa9635a8aa5be05323de604fcfa97bc3a1a57a5e80c34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECKK3+mJfCRBqGo2Y+DHlPZGPszDW\nw4inuA35i7npyTRHjagYtNlAglF/qWNaiqW+BTI95gT8+pe8OhpXpegMNA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "point duplication during verification", "msg": "313233343030", "sig": "304502206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022100ae36701f241f6073608b5f77d9039a9aec44aa5a12a99227fd2911b001915de2", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0408a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934b87257e64b26bf7eae80569ca5755a41facdc21afb03056843c5e5a85a17f3cb", "wx": "08a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934", "wy": "00b87257e64b26bf7eae80569ca5755a41facdc21afb03056843c5e5a85a17f3cb"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000408a2b7fa625f09106a1a8d98f831e53d918fb330d6c388a7b80df98bb9e9c934b87257e64b26bf7eae80569ca5755a41facdc21afb03056843c5e5a85a17f3cb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAECKK3+mJfCRBqGo2Y+DHlPZGPszDW\nw4inuA35i7npyTS4clfmSya/fq6AVpyldVpB+s3CGvsDBWhDxeWoWhfzyw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "duplication bug", "msg": "313233343030", "sig": "304502206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022100ae36701f241f6073608b5f77d9039a9aec44aa5a12a99227fd2911b001915de2", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469aac6897b0457e54cac25f24590be255d352a20055004e7caa7cbb430b3c90f9113bffe220db9143e38514da0481df67f1717c58aab1a189fb9d4f6e53c3900", "wx": "69aac6897b0457e54cac25f24590be255d352a20055004e7caa7cbb430b3c90f", "wy": "009113bffe220db9143e38514da0481df67f1717c58aab1a189fb9d4f6e53c3900"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000469aac6897b0457e54cac25f24590be255d352a20055004e7caa7cbb430b3c90f9113bffe220db9143e38514da0481df67f1717c58aab1a189fb9d4f6e53c3900", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaarGiXsEV+VMrCXyRZC+JV01KiAF\nUATnyqfLtDCzyQ+RE7/+Ig25FD44UU2gSB32fxcXxYqrGhifudT25Tw5AA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "30250201010220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04449ff6ddbec4bf9bcc3149b8dfe480f9a677c3b8e203d272f3e0a2cf90a2cea787fcbc0799a9323da3f7fddb4818b89b1d97b32b962e1b3edad2fbed47b58d41", "wx": "449ff6ddbec4bf9bcc3149b8dfe480f9a677c3b8e203d272f3e0a2cf90a2cea7", "wy": "0087fcbc0799a9323da3f7fddb4818b89b1d97b32b962e1b3edad2fbed47b58d41"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004449ff6ddbec4bf9bcc3149b8dfe480f9a677c3b8e203d272f3e0a2cf90a2cea787fcbc0799a9323da3f7fddb4818b89b1d97b32b962e1b3edad2fbed47b58d41", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAERJ/23b7Ev5vMMUm43+SA+aZ3w7ji\nA9Jy8+Ciz5CizqeH/LwHmakyPaP3/dtIGLibHZezK5YuGz7a0vvtR7WNQQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "point with x-coordinate 0", "msg": "313233343030", "sig": "3045022101000000000000000000000000000000000000000000000000000000000000000002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b282b1b5bc00c2bb18f28ce678e1cd48c8ced8335af5d8e4abd3d7a7d3616f563d47a55ddc11e966fde2bd87b028e62fc8133def824e3e00528f2442908fe84c", "wx": "00b282b1b5bc00c2bb18f28ce678e1cd48c8ced8335af5d8e4abd3d7a7d3616f56", "wy": "3d47a55ddc11e966fde2bd87b028e62fc8133def824e3e00528f2442908fe84c"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b282b1b5bc00c2bb18f28ce678e1cd48c8ced8335af5d8e4abd3d7a7d3616f563d47a55ddc11e966fde2bd87b028e62fc8133def824e3e00528f2442908fe84c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEsoKxtbwAwrsY8ozmeOHNSMjO2DNa\n9djkq9PXp9Nhb1Y9R6Vd3BHpZv3ivYewKOYvyBM974JOPgBSjyRCkI/oTA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c7002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04facbd6f5e996284a53cb1ca41ffc4eb0a3fd73b73c730194011169b9ada14519c5491ede60614d823b491198df7bc6c6768e064e0e43b7f053ab8f279cd4f4ec", "wx": "00facbd6f5e996284a53cb1ca41ffc4eb0a3fd73b73c730194011169b9ada14519", "wy": "00c5491ede60614d823b491198df7bc6c6768e064e0e43b7f053ab8f279cd4f4ec"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004facbd6f5e996284a53cb1ca41ffc4eb0a3fd73b73c730194011169b9ada14519c5491ede60614d823b491198df7bc6c6768e064e0e43b7f053ab8f279cd4f4ec", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE+svW9emWKEpTyxykH/xOsKP9c7c8\ncwGUARFpua2hRRnFSR7eYGFNgjtJEZjfe8bGdo4GTg5Dt/BTq48nnNT07A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc476699780220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a011801d52a75f4841e40240da49dda8f38e868b4e6f941f77ca9b86665ad5a74751eabf00fc2a7a863fec366975edbcd4885693022cd755c0d8936e660d61db", "wx": "00a011801d52a75f4841e40240da49dda8f38e868b4e6f941f77ca9b86665ad5a7", "wy": "4751eabf00fc2a7a863fec366975edbcd4885693022cd755c0d8936e660d61db"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004a011801d52a75f4841e40240da49dda8f38e868b4e6f941f77ca9b86665ad5a74751eabf00fc2a7a863fec366975edbcd4885693022cd755c0d8936e660d61db", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEoBGAHVKnX0hB5AJA2kndqPOOhotO\nb5Qfd8qbhmZa1adHUeq/APwqeoY/7DZpde281IhWkwIs11XA2JNuZg1h2w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fdfa5d35e42ef91b1842c9f28aeb6c68bd7732935f748168deb718e66608b980d6f85fa678df3cabb55b5002e63b55d7cae11e89f74940b7990a3b167dfa191b", "wx": "00fdfa5d35e42ef91b1842c9f28aeb6c68bd7732935f748168deb718e66608b980", "wy": "00d6f85fa678df3cabb55b5002e63b55d7cae11e89f74940b7990a3b167dfa191b"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fdfa5d35e42ef91b1842c9f28aeb6c68bd7732935f748168deb718e66608b980d6f85fa678df3cabb55b5002e63b55d7cae11e89f74940b7990a3b167dfa191b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE/fpdNeQu+RsYQsnyiutsaL13MpNf\ndIFo3rcY5mYIuYDW+F+meN88q7VbUALmO1XXyuEeifdJQLeZCjsWffoZGw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040754de71bd21f14f4927bdab77efc57f148b36275c305a86c1d7a0dcfd53bab4989fc99bc725da84197c2f284ecc6030489eda77ef92f8680130622b631af2b3", "wx": "0754de71bd21f14f4927bdab77efc57f148b36275c305a86c1d7a0dcfd53bab4", "wy": "00989fc99bc725da84197c2f284ecc6030489eda77ef92f8680130622b631af2b3"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200040754de71bd21f14f4927bdab77efc57f148b36275c305a86c1d7a0dcfd53bab4989fc99bc725da84197c2f284ecc6030489eda77ef92f8680130622b631af2b3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEB1Tecb0h8U9JJ72rd+/FfxSLNidc\nMFqGwdeg3P1TurSYn8mbxyXahBl8LyhOzGAwSJ7ad++S+GgBMGIrYxrysw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997802203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048aad84fd5d52afd531f1068fb7a10bb365faefa89975597187470d04a8c9c8b327258e32d19cd58ddfd35bd7ea1f063c77c61b7879451bdd3e8f44cbf40241a2", "wx": "008aad84fd5d52afd531f1068fb7a10bb365faefa89975597187470d04a8c9c8b3", "wy": "27258e32d19cd58ddfd35bd7ea1f063c77c61b7879451bdd3e8f44cbf40241a2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048aad84fd5d52afd531f1068fb7a10bb365faefa89975597187470d04a8c9c8b327258e32d19cd58ddfd35bd7ea1f063c77c61b7879451bdd3e8f44cbf40241a2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEiq2E/V1Sr9Ux8QaPt6ELs2X676iZ\ndVlxh0cNBKjJyLMnJY4y0ZzVjd/TW9fqHwY8d8YbeHlFG90+j0TL9AJBog==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 422, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b4a01ec7b10acff3dace05222fae05c75368a375d8ce1faf4a1b2def1ab715ab61a6642e82ff950f373ace5c0cc298639ab9ae739e4614a93fb1122ee16d922f", "wx": "00b4a01ec7b10acff3dace05222fae05c75368a375d8ce1faf4a1b2def1ab715ab", "wy": "61a6642e82ff950f373ace5c0cc298639ab9ae739e4614a93fb1122ee16d922f"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b4a01ec7b10acff3dace05222fae05c75368a375d8ce1faf4a1b2def1ab715ab61a6642e82ff950f373ace5c0cc298639ab9ae739e4614a93fb1122ee16d922f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtKAex7EKz/PazgUiL64Fx1Noo3XY\nzh+vShst7xq3FathpmQugv+VDzc6zlwMwphjmrmuc55GFKk/sRIu4W2SLw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "extreme value for k", "msg": "313233343030", "sig": "304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044efc2bef42b11e0929edca3b025f01b1fa37aceef4cd3e8c2d4beab500856af87716828fc1788c881ae39f534c3e270ca869a578210b5dad8a8938691d0c4b73", "wx": "4efc2bef42b11e0929edca3b025f01b1fa37aceef4cd3e8c2d4beab500856af8", "wy": "7716828fc1788c881ae39f534c3e270ca869a578210b5dad8a8938691d0c4b73"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044efc2bef42b11e0929edca3b025f01b1fa37aceef4cd3e8c2d4beab500856af87716828fc1788c881ae39f534c3e270ca869a578210b5dad8a8938691d0c4b73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETvwr70KxHgkp7co7Al8Bsfo3rO70\nzT6MLUvqtQCFavh3FoKPwXiMiBrjn1NMPicMqGmleCELXa2KiThpHQxLcw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 424, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2960220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044606b9fceecc47babcc78de73ca55672f09930aacd560db0a2967a99ef8a595e612a050025785c0e7c7763db0bb53c48b6ff0ed1dfb6055df90299e295092990", "wx": "4606b9fceecc47babcc78de73ca55672f09930aacd560db0a2967a99ef8a595e", "wy": "612a050025785c0e7c7763db0bb53c48b6ff0ed1dfb6055df90299e295092990"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044606b9fceecc47babcc78de73ca55672f09930aacd560db0a2967a99ef8a595e612a050025785c0e7c7763db0bb53c48b6ff0ed1dfb6055df90299e295092990", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAERga5/O7MR7q8x43nPKVWcvCZMKrN\nVg2wopZ6me+KWV5hKgUAJXhcDnx3Y9sLtTxItv8O0d+2BV35ApnilQkpkA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b6de6de267720d16bccf71fdbe07ebe5c1149f8cfb2041b1d6cabbc9b6656d682d3e21f4025ddcacab035b5da6310361102079f1b40c1ef7c7b88427694c11c6", "wx": "00b6de6de267720d16bccf71fdbe07ebe5c1149f8cfb2041b1d6cabbc9b6656d68", "wy": "2d3e21f4025ddcacab035b5da6310361102079f1b40c1ef7c7b88427694c11c6"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b6de6de267720d16bccf71fdbe07ebe5c1149f8cfb2041b1d6cabbc9b6656d682d3e21f4025ddcacab035b5da6310361102079f1b40c1ef7c7b88427694c11c6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtt5t4mdyDRa8z3H9vgfr5cEUn4z7\nIEGx1sq7ybZlbWgtPiH0Al3crKsDW12mMQNhECB58bQMHvfHuIQnaUwRxg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 426, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04efd3bd1299d4b2bb0afbce2afd90792d8c72e1d29c6092e4681540420664e275390a4dbb20d10f7360c5b794564dcd443bb1df94bf6b1f4be5909a22a6a534ba", "wx": "00efd3bd1299d4b2bb0afbce2afd90792d8c72e1d29c6092e4681540420664e275", "wy": "390a4dbb20d10f7360c5b794564dcd443bb1df94bf6b1f4be5909a22a6a534ba"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004efd3bd1299d4b2bb0afbce2afd90792d8c72e1d29c6092e4681540420664e275390a4dbb20d10f7360c5b794564dcd443bb1df94bf6b1f4be5909a22a6a534ba", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE79O9EpnUsrsK+84q/ZB5LYxy4dKc\nYJLkaBVAQgZk4nU5Ck27INEPc2DFt5RWTc1EO7HflL9rH0vlkJoipqU0ug==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 427, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29602203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dae9cf165867da6482ede84bfc5b94375529bcb953f26d0cd68fc877088f78d951cadc9f61c55f8e2a04dbaae1251fd15cb12df9bcd072a51971127eaa6ce612", "wx": "00dae9cf165867da6482ede84bfc5b94375529bcb953f26d0cd68fc877088f78d9", "wy": "51cadc9f61c55f8e2a04dbaae1251fd15cb12df9bcd072a51971127eaa6ce612"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004dae9cf165867da6482ede84bfc5b94375529bcb953f26d0cd68fc877088f78d951cadc9f61c55f8e2a04dbaae1251fd15cb12df9bcd072a51971127eaa6ce612", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE2unPFlhn2mSC7ehL/FuUN1UpvLlT\n8m0M1o/IdwiPeNlRytyfYcVfjioE26rhJR/RXLEt+bzQcqUZcRJ+qmzmEg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 428, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048418924e94161d350c9fe8dc1cc087de4b4491d0bc694a6862df4e8a555f9afbb0b391af97b0dacbdceb7a982781d0090978efbf76b8e6914250e92ade9a6126", "wx": "008418924e94161d350c9fe8dc1cc087de4b4491d0bc694a6862df4e8a555f9afb", "wy": "00b0b391af97b0dacbdceb7a982781d0090978efbf76b8e6914250e92ade9a6126"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200048418924e94161d350c9fe8dc1cc087de4b4491d0bc694a6862df4e8a555f9afbb0b391af97b0dacbdceb7a982781d0090978efbf76b8e6914250e92ade9a6126", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhBiSTpQWHTUMn+jcHMCH3ktEkdC8\naUpoYt9OilVfmvuws5Gvl7Day9zrepgngdAJCXjvv3a45pFCUOkq3pphJg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "extreme value for k", "msg": "313233343030", "sig": "304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpZP40Li/hp/m47n60p8D54WK84zV2sxXs7LtkBoN79R9Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 430, "comment": "testing point duplication", "msg": "313233343030", "sig": "30440220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c30220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 431, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100cbd25189e59c5c367e7630cbd4c4cb1512c194cadf353d6331f9f57fa81b338e0220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "wx": "6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296", "wy": "00b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEaxfR8uEsQkf4vOblY6RA8ncDfYEt\n6zOg9KE5RdiYwpawHL0cAeWAZXEYFLWD8GHp1DHMqZTOoTE0Sb+XyECuCg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 432, "comment": "testing point duplication", "msg": "313233343030", "sig": "30440220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c30220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}, {"tcId": 433, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100cbd25189e59c5c367e7630cbd4c4cb1512c194cadf353d6331f9f57fa81b338e0220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "wx": "04aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad5", "wy": "0087d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBKrsc2NXJvIT+4qeZNo7hjLkFJWp\nRNAEW1IuunJA+tWH2TFXmKqjpboBd1eHztBeqve04J/IHW0apUboNl1SXQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 434, "comment": "pseudorandom signature", "msg": "", "sig": "3046022100ddb577670d5a9b93666df2af7f9baadd8256fca0c81deb2d5cd7301a4b39105f022100a2bcd9f6228a0aa0a4f066aa674b9b08da252b02a77fd1b2f7a2d85929e8b491", "result": "valid", "flags": []}, {"tcId": 435, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "30440220532d672c307da2891720d11422035ea25771f4fce0dc9948d754ca4f66ef36bb02204f296181799d3e6780086d6908ab8642711bd406e481b0ce3af1c44b9f098496", "result": "valid", "flags": []}, {"tcId": 436, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "304502201c58cde69ddb363e7a6d2612771b3e713be8bed07f37bcda4875f152db2ac1ad022100a66755b078262b6b0d90c74aee64522104aa58b5f82fc5ba98bcef5bdb9d43a8", "result": "valid", "flags": []}, {"tcId": 437, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "304402206637f14369d1dd112c5691969fcc867e64bafb1f5d8f917a9acf8ecf1dc957540220457b4a8bdd1c047b12826897991241700dad09666b4f410a56993023b098fd1e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "wx": "4f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000", "wy": "00ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETzN8z9Z3JqgF5PFgCuKEnfOAfsoR\nc4Ajn72BaQAAAADtneoSTMjDlkFkEemIww9CfrUEr0OjFGzV336mBmbWhQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 438, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3046022100ef9a4672e0a07a0efd5936a77f4ea0fcd69dae6fd95ccba8dcb685e7490623c3022100cf135d42f5e379f6ef94b6a493db01c740f441ff5b2475638b6b081f445b17e2", "result": "valid", "flags": []}, {"tcId": 439, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3046022100b8cb05b278c15df7c6a311b0ce7bb5598fe3a95fdb57683ac0821aa2f5d6fa18022100a9dfa1cc0c9ae0015ae2485f22ed6b0925e82d96ff695b8c2ed593f02719f30e", "result": "valid", "flags": []}, {"tcId": 440, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304502202fe6f2aaed0eb760223e8c580363630d3041614806e7fbc863405aa4d98825ee022100f5d2c396e45d9af9fe1b06b27f0db4d9ee66e1855f4846dae5db6b3d8afbc4f8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "0084fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTWE+hdNeRxyvyzjiAqJYN0qfHoTOKgvhanlnNvegAAAAA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 441, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3046022100fb9e312ced9f95c91248bae25056b7bbd84a05fc92acbd304269a1269bbf4f4c022100f31426a809ceebb369278a5182bce94835f066cab8654f8bd2339595844244db", "result": "valid", "flags": []}, {"tcId": 442, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30440220284fe5f1c86a55fcb3c27a12d82d794eab9a0d76d1fdc4f25df5a9bf52e8e76802207aaa75e5a7948be66f06ce52fc3e4916fc93514b08c51f84b73839d2c05acf58", "result": "valid", "flags": []}, {"tcId": 443, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3045022100812950449ab90351ab0f124774a2633625d0871d42a69d2e473d8ac118b506a6022050c98878c3699c34f3049e6f9496e3199fb5d860232995205f1761c000af4e07", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "wx": "3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935", "wy": "7b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEPPA9YU2JOc/UmaB4c/rCgWGPBrj/\nh+gBXD9JcmUASTV7BeixhuONQdMcd/V2nyLVg4XsyFfQelYaYyQhf////w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 444, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100eaa4f3a8805c60cff74cec78c6b7cea71ef116c333612a6e0096a4c42450936b022100fbb3508d6692b8e0854c1a1749ea065262bae7c8b831ece895a1eeda01cebe7d", "result": "valid", "flags": []}, {"tcId": 445, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "304502203d0caca44c21d34cfdcfb00098c95203e08847f4aabab5ae6ea7f365aefbbcf8022100bb99be729e51cfd167dad22182383314c9d85432309cbe36f4b1eb73e776b249", "result": "valid", "flags": []}, {"tcId": 446, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022100e4f8b27c3c2c5cb2a6cb9c2049011dd1060085c6e2a15380bcf5224063d3ae0802204b3a19f1be945cd649f34d965e7eecac0a05ac8030b7cd2d6bed84086fda567e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "wx": "2829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffff", "wy": "00a01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d030107034200042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEKCnDH6ouQA40TtlLyj/NBUWVbrz+\nitD236X/jv////+gGq+vAA5SWFhVr6dnat4oQRMJkFLfV+frO9N+vrkiLg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 447, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30440220670ce60d0fc7224c8d06f3b01c4416ee0b3ac12f0cff3e1ad214898389ebc819022045da7b6fc8f4f766f82c5132735f2ed94130780b444e2e10c1484d5e45f64332", "result": "valid", "flags": []}, {"tcId": 448, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100a22928b3022fd554670a86ed519a48d8448dc90856e9c40589076baff591e076022100b4005a89aef027e5063a89709fbc66a05eb12d8d34af84f35ea7ac93f7bbd40e", "result": "valid", "flags": []}, {"tcId": 449, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100a277181e641edafbdb359801605791c833ff3b462dac3c4d3608d479c98090ff0221008b2ad68880cede2290a996fe5b7003610c5c9f53e1dcf2cdb2ad815e2ba5c1cf", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "wx": "00fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f5", "wy": "5a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE////+UgIHmoEWN2PnnOPJmX/kFmt\naqwHCDGMTKmnpPVairy6LdqEdDEe5UFJuXPK4MD7iVV60L945lKaFmO9cw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 450, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30450220760c487841a7214e9850b5569d99fe3ff9dddc41dee7313a780921d8d3657b16022100b99d63e3cc81458801d5fd5926405d33b894e0f96a85c3c4a929a45c828d76d0", "result": "valid", "flags": []}, {"tcId": 451, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30450221009b7e72ac454505017c5377b2c379f31a7a0268b739fca4c97dfe1c9aa4bc548e02201e65cbee69a42ab0a7ae50e9acceaa0971574fb604958876ad6316c39eebdd96", "result": "valid", "flags": []}, {"tcId": 452, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502204a7e44e4a8a9fee22647923eaee0368b3fa408edaa19b406507309b13e073855022100fd8597585fa0270c8cdf855a7a69a843eb28124ecf90ac53a58a847c7b43c7a6", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "wx": "03fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e", "wy": "1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d0301070342000400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAAAAA/oV+WOUnV8DpvXH+G+eABXu\nsjrrv/EXOTe6dI4QmYcgcOjofFVfoTZZzKXX+tz8sAI+qIlUjKSK8rp+cQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 453, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402204589f0c24d1b2f89c2e806f667297c844c63330d6b9a079b2bdd2d95247e3eae022075daa1da09a3f1ed8de1aacc7039a721c46f9ebcd4672750f2fd9c2f70f1e6ed", "result": "valid", "flags": []}, {"tcId": 454, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30450220377578a81da586eae269f05c9bb2280a517ab3184e02179aca5d64dee1a930c7022100cc1735efd4b43e8bdba8c1b44f9ec3e62577e5f4f6543051467bacd9eaaa4d4a", "result": "valid", "flags": []}, {"tcId": 455, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502207f722716c279f3d14563fbd8cbf3abcd51c3305795609e04c8ed7ede12ae25180221008a996a370679c3ce6232244d64481e96e47bf6611aa5490000c0b1dffc231aa1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "1352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BUAAAAAE1K7Sg+i6kzOuatj3WhK3loRJ7zzAKaYpxk7wg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 456, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402202f11d967790dd4387a218929b3fc0f130238ec6fd9321348fe631a1e607bd74202203b52a42606d0daa135092d89ab74b3c4e72b4ed213137406d4b50fa84c4ea049", "result": "valid", "flags": []}, {"tcId": 457, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30440220192d299e96272a0c49ed1b15f9047b54b4878eaf350a7b2f5439b1e3c83f9703022026513e9cbbaca7946f3d6d90b5cc65a985cdfe089734f944ab74842a4dc4127d", "result": "valid", "flags": []}, {"tcId": 458, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3046022100ebd7a6f1450f8bb9e9abf9f66c4143d28c8c845b2e260ab0fff3f7ba5837f9440221009928afe57df6b8c90b7af7c3da981b71db6ae3a5a21a4c5a48b71628e5811a9e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "wx": "00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015", "wy": "00fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d"}, "keyDer": "3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEvLspFMefBF6qbsu8YSgWs75dLWeW\ncH2BJen4UcGK8BX////+7K1EtvBdFbMxRlScIpe1IqXu2EMM/1lnWObEPQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 459, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3044022037fd00b14ff469a8bb4d2f9c6ca228c4c24b85719389a46099653c41174e9afd02205f64dc68893cf3186df3e83af70e96e9f2103d25b8ddffecda96e8e9181619cf", "result": "valid", "flags": []}, {"tcId": 460, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502203ce339a63ea4cc1a6b12d1e66b91205e8af530eebe3208359c5327b242b2b669022100f2b1d6dae62bfe9c44b1cbd56cf0de865a1201c0486d658da5fc029ad47b917e", "result": "valid", "flags": []}, {"tcId": 461, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304502207bf225bec33ce7fb4014097e350c1504d3374028cda8f6fbbac4e0fa5319a048022100aaa45d54eba6bb3ce00ce8e63de24dc7ee19069062e8d340663adcac07f097cd", "result": "valid", "flags": []}]}]}