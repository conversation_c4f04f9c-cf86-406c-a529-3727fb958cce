# CAVS 11.1
# Config info for aes_values
# AESVS KeySbox test data for CFB8
# State : Encrypt and Decrypt
# Key Length : 128
# Generated on Fri Apr 22 15:11:46 2011

[ENCRYPT]

COUNT = 0
KEY = 10a58869d74be5a374cf867cfb473859
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 6d

COUNT = 1
KEY = caea65cdbb75e9169ecd22ebe6e54675
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 6e

COUNT = 2
KEY = a2e2fa9baf7d20822ca9f0542f764a41
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = c3

COUNT = 3
KEY = b6364ac4e1de1e285eaf144a2415f7a0
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 5d

COUNT = 4
KEY = 64cf9c7abc50b888af65f49d521944b2
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = f7

COUNT = 5
KEY = 47d6742eefcc0465dc96355e851b64d9
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 03

COUNT = 6
KEY = 3eb39790678c56bee34bbcdeccf6cdb5
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 85

COUNT = 7
KEY = 64110a924f0743d500ccadae72c13427
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 35

COUNT = 8
KEY = 18d8126516f8a12ab1a36d9f04d68e51
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 6c

COUNT = 9
KEY = f530357968578480b398a3c251cd1093
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = f5

COUNT = 10
KEY = da84367f325d42d601b4326964802e8e
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = bb

COUNT = 11
KEY = e37b1c6aa2846f6fdb413f238b089f23
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 43

COUNT = 12
KEY = 6c002b682483e0cabcc731c253be5674
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 35

COUNT = 13
KEY = 143ae8ed6555aba96110ab58893a8ae1
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 80

COUNT = 14
KEY = b69418a85332240dc82492353956ae0c
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = a3

COUNT = 15
KEY = 71b5c08a1993e1362e4d0ce9b22b78d5
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = c2

COUNT = 16
KEY = e234cdca2606b81f29408d5f6da21206
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = ff

COUNT = 17
KEY = 13237c49074a3da078dc1d828bb78c6f
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 81

COUNT = 18
KEY = 3071a2a48fe6cbd04f1a129098e308f8
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 4b

COUNT = 19
KEY = 90f42ec0f68385f2ffc5dfc03a654dce
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = 7a

COUNT = 20
KEY = febd9a24d8b65c1c787d50a4ed3619a9
IV = 00000000000000000000000000000000
PLAINTEXT = 00
CIPHERTEXT = f4

[DECRYPT]

COUNT = 0
KEY = 10a58869d74be5a374cf867cfb473859
IV = 00000000000000000000000000000000
CIPHERTEXT = 6d
PLAINTEXT = 00

COUNT = 1
KEY = caea65cdbb75e9169ecd22ebe6e54675
IV = 00000000000000000000000000000000
CIPHERTEXT = 6e
PLAINTEXT = 00

COUNT = 2
KEY = a2e2fa9baf7d20822ca9f0542f764a41
IV = 00000000000000000000000000000000
CIPHERTEXT = c3
PLAINTEXT = 00

COUNT = 3
KEY = b6364ac4e1de1e285eaf144a2415f7a0
IV = 00000000000000000000000000000000
CIPHERTEXT = 5d
PLAINTEXT = 00

COUNT = 4
KEY = 64cf9c7abc50b888af65f49d521944b2
IV = 00000000000000000000000000000000
CIPHERTEXT = f7
PLAINTEXT = 00

COUNT = 5
KEY = 47d6742eefcc0465dc96355e851b64d9
IV = 00000000000000000000000000000000
CIPHERTEXT = 03
PLAINTEXT = 00

COUNT = 6
KEY = 3eb39790678c56bee34bbcdeccf6cdb5
IV = 00000000000000000000000000000000
CIPHERTEXT = 85
PLAINTEXT = 00

COUNT = 7
KEY = 64110a924f0743d500ccadae72c13427
IV = 00000000000000000000000000000000
CIPHERTEXT = 35
PLAINTEXT = 00

COUNT = 8
KEY = 18d8126516f8a12ab1a36d9f04d68e51
IV = 00000000000000000000000000000000
CIPHERTEXT = 6c
PLAINTEXT = 00

COUNT = 9
KEY = f530357968578480b398a3c251cd1093
IV = 00000000000000000000000000000000
CIPHERTEXT = f5
PLAINTEXT = 00

COUNT = 10
KEY = da84367f325d42d601b4326964802e8e
IV = 00000000000000000000000000000000
CIPHERTEXT = bb
PLAINTEXT = 00

COUNT = 11
KEY = e37b1c6aa2846f6fdb413f238b089f23
IV = 00000000000000000000000000000000
CIPHERTEXT = 43
PLAINTEXT = 00

COUNT = 12
KEY = 6c002b682483e0cabcc731c253be5674
IV = 00000000000000000000000000000000
CIPHERTEXT = 35
PLAINTEXT = 00

COUNT = 13
KEY = 143ae8ed6555aba96110ab58893a8ae1
IV = 00000000000000000000000000000000
CIPHERTEXT = 80
PLAINTEXT = 00

COUNT = 14
KEY = b69418a85332240dc82492353956ae0c
IV = 00000000000000000000000000000000
CIPHERTEXT = a3
PLAINTEXT = 00

COUNT = 15
KEY = 71b5c08a1993e1362e4d0ce9b22b78d5
IV = 00000000000000000000000000000000
CIPHERTEXT = c2
PLAINTEXT = 00

COUNT = 16
KEY = e234cdca2606b81f29408d5f6da21206
IV = 00000000000000000000000000000000
CIPHERTEXT = ff
PLAINTEXT = 00

COUNT = 17
KEY = 13237c49074a3da078dc1d828bb78c6f
IV = 00000000000000000000000000000000
CIPHERTEXT = 81
PLAINTEXT = 00

COUNT = 18
KEY = 3071a2a48fe6cbd04f1a129098e308f8
IV = 00000000000000000000000000000000
CIPHERTEXT = 4b
PLAINTEXT = 00

COUNT = 19
KEY = 90f42ec0f68385f2ffc5dfc03a654dce
IV = 00000000000000000000000000000000
CIPHERTEXT = 7a
PLAINTEXT = 00

COUNT = 20
KEY = febd9a24d8b65c1c787d50a4ed3619a9
IV = 00000000000000000000000000000000
CIPHERTEXT = f4
PLAINTEXT = 00

