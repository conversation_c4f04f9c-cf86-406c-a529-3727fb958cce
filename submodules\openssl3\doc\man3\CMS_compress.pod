=pod

=head1 NAME

CMS_compress - create a CMS CompressedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ContentInfo *CMS_compress(BIO *in, int comp_nid, unsigned int flags);

=head1 DESCRIPTION

CMS_compress() creates and returns a CMS CompressedData structure. B<comp_nid>
is the compression algorithm to use or B<NID_undef> to use the default
algorithm (zlib compression). B<in> is the content to be compressed.
B<flags> is an optional set of flags.

The only currently supported compression algorithm is zlib using the NID
NID_zlib_compression.

If zlib support is not compiled into OpenSSL then CMS_compress() will return
an error.

If the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are
prepended to the data.

Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if B<CMS_BINARY> is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If B<CMS_BINARY> is set then
B<CMS_TEXT> is ignored.

If the B<CMS_STREAM> flag is set a partial B<CMS_ContentInfo> structure is
returned suitable for streaming I/O: no data is read from the BIO B<in>.

The compressed data is included in the CMS_ContentInfo structure, unless
B<CMS_DETACHED> is set in which case it is omitted. This is rarely used in
practice and is not supported by SMIME_write_CMS().

If the flag B<CMS_STREAM> is set the returned B<CMS_ContentInfo> structure is
B<not> complete and outputting its contents via a function that does not
properly finalize the B<CMS_ContentInfo> structure will give unpredictable
results.

Several functions including SMIME_write_CMS(), i2d_CMS_bio_stream(),
PEM_write_bio_CMS_stream() finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 B<BIO> directly using
BIO_new_CMS().

Additional compression parameters such as the zlib compression level cannot
currently be set.

=head1 RETURN VALUES

CMS_compress() returns either a CMS_ContentInfo structure or NULL if an error
occurred. The error can be obtained from ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_uncompress(3)>

=head1 HISTORY

The B<CMS_STREAM> flag was added in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
