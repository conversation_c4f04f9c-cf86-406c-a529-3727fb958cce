/* -*- mode: c; c-basic-offset: 4; indent-tabs-mode: nil -*- */
/*
 * Copyright (C) 2019 by the Massachusetts Institute of Technology.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * * Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * * Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 * Usage: s4u2self user self out_cache [ad-type ad-contents]
 *
 * The default ccache contains a TGT for the intermediate service self.  An
 * S4U2Self request is made to self.  The resulting cred is stored in
 * out_cache.
 */

#include <k5-int.h>

static krb5_context ctx;

static void
check(krb5_error_code code)
{
    const char *errmsg;

    if (code) {
        errmsg = krb5_get_error_message(ctx, code);
        fprintf(stderr, "%s\n", errmsg);
        krb5_free_error_message(ctx, errmsg);
        exit(1);
    }
}

static krb5_authdata **
make_request_authdata(int type, const char *contents)
{
    krb5_authdata *ad;
    krb5_authdata **req_authdata;

    ad = malloc(sizeof(*ad));
    assert(ad != NULL);
    ad->magic = KV5M_AUTHDATA;
    ad->ad_type = type;
    ad->length = strlen(contents);
    ad->contents = (unsigned char *)strdup(contents);
    assert(ad->contents != NULL);

    req_authdata = malloc(2 * sizeof(*req_authdata));
    assert(req_authdata != NULL);
    req_authdata[0] = ad;
    req_authdata[1] = NULL;

    return req_authdata;
}

int
main(int argc, char **argv)
{
    krb5_context context;
    krb5_ccache defcc, ocache;
    krb5_principal client, self;
    krb5_creds mcred, *new_cred;
    krb5_authdata **req_authdata = NULL;

    if (argc == 6) {
        req_authdata = make_request_authdata(atoi(argv[4]), argv[5]);
        argc -= 2;
    }

    assert(argc == 4);
    check(krb5_init_context(&context));

    /* Open the default ccache. */
    check(krb5_cc_default(context, &defcc));

    check(krb5_parse_name(context, argv[1], &client));
    check(krb5_parse_name(context, argv[2], &self));

    memset(&mcred, 0, sizeof(mcred));
    mcred.client = client;
    mcred.server = self;
    mcred.authdata = req_authdata;
    check(krb5_get_credentials_for_user(context, KRB5_GC_NO_STORE |
                                        KRB5_GC_CANONICALIZE, defcc,
                                        &mcred, NULL, &new_cred));

    if (strcmp(argv[3], "-") == 0) {
        check(krb5_cc_store_cred(context, defcc, new_cred));
    } else {
        check(krb5_cc_resolve(context, argv[3], &ocache));
        check(krb5_cc_initialize(context, ocache, new_cred->client));
        check(krb5_cc_store_cred(context, ocache, new_cred));
        krb5_cc_close(context, ocache);
    }

    assert(req_authdata == NULL || new_cred->authdata != NULL);

    krb5_cc_close(context, defcc);
    krb5_free_principal(context, client);
    krb5_free_principal(context, self);
    krb5_free_creds(context, new_cred);
    krb5_free_authdata(context, req_authdata);
    krb5_free_context(context);
    return 0;
}
