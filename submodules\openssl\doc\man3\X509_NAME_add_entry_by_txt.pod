=pod

=head1 NAME

X509_NAME_add_entry_by_txt, X509_NAME_add_entry_by_OBJ, X509_NAME_add_entry_by_NID,
X509_NAME_add_entry, X509_NAME_delete_entry - X509_NAME modification functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_NAME_add_entry_by_txt(X509_NAME *name, const char *field, int type,
                                const unsigned char *bytes, int len, int loc, int set);

 int X509_NAME_add_entry_by_OBJ(X509_NAME *name, const ASN1_OBJECT *obj, int type,
                                const unsigned char *bytes, int len, int loc, int set);

 int X509_NAME_add_entry_by_NID(X509_NAME *name, int nid, int type,
                                const unsigned char *bytes, int len, int loc, int set);

 int X509_NAME_add_entry(X509_NAME *name, const X509_NAME_ENTRY *ne, int loc, int set);

 X509_NAME_ENTRY *X509_NAME_delete_entry(X509_NAME *name, int loc);

=head1 DESCRIPTION

X509_NAME_add_entry_by_txt(), X509_NAME_add_entry_by_OBJ() and
X509_NAME_add_entry_by_NID() add a field whose name is defined
by a string B<field>, an object B<obj> or a NID B<nid> respectively.
The field value to be added is in B<bytes> of length B<len>. If
B<len> is -1 then the field length is calculated internally using
strlen(bytes).

The type of field is determined by B<type> which can either be a
definition of the type of B<bytes> (such as B<MBSTRING_ASC>) or a
standard ASN1 type (such as B<V_ASN1_IA5STRING>). The new entry is
added to a position determined by B<loc> and B<set>.

X509_NAME_add_entry() adds a copy of B<X509_NAME_ENTRY> structure B<ne>
to B<name>. The new entry is added to a position determined by B<loc>
and B<set>. Since a copy of B<ne> is added B<ne> must be freed up after
the call.

X509_NAME_delete_entry() deletes an entry from B<name> at position
B<loc>. The deleted entry is returned and must be freed up.

=head1 NOTES

The use of string types such as B<MBSTRING_ASC> or B<MBSTRING_UTF8>
is strongly recommended for the B<type> parameter. This allows the
internal code to correctly determine the type of the field and to
apply length checks according to the relevant standards. This is
done using ASN1_STRING_set_by_NID().

If instead an ASN1 type is used no checks are performed and the
supplied data in B<bytes> is used directly.

In X509_NAME_add_entry_by_txt() the B<field> string represents
the field name using OBJ_txt2obj(field, 0).

The B<loc> and B<set> parameters determine where a new entry should
be added. For almost all applications B<loc> can be set to -1 and B<set>
to 0. This adds a new entry to the end of B<name> as a single valued
RelativeDistinguishedName (RDN).

B<loc> actually determines the index where the new entry is inserted:
if it is -1 it is appended.

B<set> determines how the new type is added. If it is zero a
new RDN is created.

If B<set> is -1 or 1 it is added to the previous or next RDN
structure respectively. This will then be a multivalued RDN:
since multivalues RDNs are very seldom used B<set> is almost
always set to zero.

=head1 RETURN VALUES

X509_NAME_add_entry_by_txt(), X509_NAME_add_entry_by_OBJ(),
X509_NAME_add_entry_by_NID() and X509_NAME_add_entry() return 1 for
success of 0 if an error occurred.

X509_NAME_delete_entry() returns either the deleted B<X509_NAME_ENTRY>
structure of B<NULL> if an error occurred.

=head1 EXAMPLES

Create an B<X509_NAME> structure:

"C=UK, O=Disorganized Organization, CN=Joe Bloggs"

 X509_NAME *nm;

 nm = X509_NAME_new();
 if (nm == NULL)
     /* Some error */
 if (!X509_NAME_add_entry_by_txt(nm, "C", MBSTRING_ASC,
                                 "UK", -1, -1, 0))
     /* Error */
 if (!X509_NAME_add_entry_by_txt(nm, "O", MBSTRING_ASC,
                                 "Disorganized Organization", -1, -1, 0))
     /* Error */
 if (!X509_NAME_add_entry_by_txt(nm, "CN", MBSTRING_ASC,
                                 "Joe Bloggs", -1, -1, 0))
     /* Error */

=head1 BUGS

B<type> can still be set to B<V_ASN1_APP_CHOOSE> to use a
different algorithm to determine field types. Since this form does
not understand multicharacter types, performs no length checks and
can result in invalid field types its use is strongly discouraged.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<d2i_X509_NAME(3)>

=head1 COPYRIGHT

Copyright 2002-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
