=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-rsa - RSA key processing command

=head1 SYNOPSIS

B<openssl> B<rsa>
[B<-help>]
[B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>|I<uri>]
[B<-passin> I<arg>]
[B<-out> I<filename>]
[B<-passout> I<arg>]
[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-text>]
[B<-noout>]
[B<-modulus>]
[B<-traditional>]
[B<-check>]
[B<-pubin>]
[B<-pubout>]
[B<-RSAPublicKey_in>]
[B<-RSAPublicKey_out>]
[B<-pvk-strong>]
[B<-pvk-weak>]
[B<-pvk-none>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command processes RSA keys. They can be converted between
various forms and their components printed out.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The key output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-traditional>

When writing a private key, use the traditional PKCS#1 format
instead of the PKCS#8 format.

=item B<-in> I<filename>|I<uri>

This specifies the input to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.

=item B<-passin> I<arg>, B<-passout> I<arg>

The password source for the input and output file.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-out> I<filename>

This specifies the output filename to write a key to or standard output if this
option is not specified. If any encryption options are set then a pass phrase
will be prompted for. The output filename should B<not> be the same as the input
filename.

=item B<-aes128>, B<-aes192>, B<-aes256>, B<-aria128>, B<-aria192>, B<-aria256>, B<-camellia128>, B<-camellia192>, B<-camellia256>, B<-des>, B<-des3>, B<-idea>

These options encrypt the private key with the specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified the key is written in plain text. This
means that this command can be used to remove the pass phrase from a key
by not giving any encryption option is given, or to add or change the pass
phrase by setting them.
These options can only be used with PEM format output files.

=item B<-text>

Prints out the various public or private key components in
plain text in addition to the encoded version.

=item B<-noout>

This option prevents output of the encoded version of the key.

=item B<-modulus>

This option prints out the value of the modulus of the key.

=item B<-check>

This option checks the consistency of an RSA private key.

=item B<-pubin>

By default a private key is read from the input file: with this
option a public key is read instead.

=item B<-pubout>

By default a private key is output: with this option a public
key will be output instead. This option is automatically set if
the input is a public key.

=item B<-RSAPublicKey_in>, B<-RSAPublicKey_out>

Like B<-pubin> and B<-pubout> except B<RSAPublicKey> format is used instead.

=item B<-pvk-strong>

Enable 'Strong' PVK encoding level (default).

=item B<-pvk-weak>

Enable 'Weak' PVK encoding level.

=item B<-pvk-none>

Don't enforce PVK encoding.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 NOTES

The L<openssl-pkey(1)> command is capable of performing all the operations
this command can, as well as supporting other public key types.

=head1 EXAMPLES

The documentation for the L<openssl-pkey(1)> command contains examples
equivalent to the ones listed here.

To remove the pass phrase on an RSA private key:

 openssl rsa -in key.pem -out keyout.pem

To encrypt a private key using triple DES:

 openssl rsa -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl rsa -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl rsa -in key.pem -text -noout

To just output the public part of a private key:

 openssl rsa -in key.pem -pubout -out pubkey.pem

Output the public part of a private key in B<RSAPublicKey> format:

 openssl rsa -in key.pem -RSAPublicKey_out -out pubkey.pem

=head1 BUGS

There should be an option that automatically handles F<.key> files,
without having to manually edit them.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkey(1)>,
L<openssl-pkcs8(1)>,
L<openssl-dsa(1)>,
L<openssl-genrsa(1)>,
L<openssl-gendsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
