=pod

=head1 NAME

EVP_PKEY_print_public, E<PERSON>_PKEY_print_private, EVP_PKEY_print_params - public key algorithm printing routines

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_print_public(BIO *out, const EVP_PKEY *pkey,
                           int indent, ASN1_PCTX *pctx);
 int EVP_PKEY_print_private(BIO *out, const EVP_PKEY *pkey,
                            int indent, ASN1_PCTX *pctx);
 int EVP_PKEY_print_params(BIO *out, const EVP_PKEY *pkey,
                           int indent, ASN1_PCTX *pctx);

=head1 DESCRIPTION

The functions EVP_PKEY_print_public(), EVP_PKEY_print_private() and
EVP_PKEY_print_params() print out the public, private or parameter components
of key B<pkey> respectively. The key is sent to BIO B<out> in human readable
form. The parameter B<indent> indicated how far the printout should be indented.

The B<pctx> parameter allows the print output to be finely tuned by using
ASN1 printing options. If B<pctx> is set to NULL then default values will
be used.

=head1 NOTES

Currently no public key algorithms include any options in the B<pctx> parameter.

If the key does not include all the components indicated by the function then
only those contained in the key will be printed. For example passing a public
key to EVP_PKEY_print_private() will only print the public components.

=head1 RETURN VALUES

These functions all return 1 for success and 0 or a negative value for failure.
In particular a return value of -2 indicates the operation is not supported by
the public key algorithm.

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_keygen(3)>

=head1 HISTORY

These functions were added in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2006-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
