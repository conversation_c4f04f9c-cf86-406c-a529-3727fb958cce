/*
 * Maxim Tishkov 2016
 * This file is distributed under the same license as OpenSSL
 */

#ifndef GOST_GRASSHOPPER_PRECOMPILED_H
#define GOST_GRASSHOPPER_PRECOMPILED_H

#include "gost_grasshopper_defines.h"
#include "gost_grasshopper_math.h"

extern const grasshopper_w128_t grasshopper_pil_enc128[GRASSHOPPER_MAX_BIT_PARTS][256];

extern const grasshopper_w128_t grasshopper_l_dec128[GRASSHOPPER_MAX_BIT_PARTS][256];

extern const grasshopper_w128_t grasshopper_pil_dec128[GRASSHOPPER_MAX_BIT_PARTS][256];

#endif
