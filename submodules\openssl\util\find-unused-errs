#! /bin/bash
# Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Find unused error function-names and reason-codes, and edit them
# out of the source.  Doesn't handle line-wrapping, might have to do
# some manual cleanups to fix compile errors.

export X1=/tmp/f.1.$$
export X2=/tmp/f.2.$$

case "$1" in
    -f)
        PAT='_F_'
        echo Functions only
        ;;
    -[er])
        PAT='_R_'
        echo Reason codes only
        ;;
    "")
        PAT='_[FR]_'
        echo Function and reasons
        ;;
    *)
        echo "Usage error; one of -[efr] required."
        exit 1;
        ;;
esac

cd include/openssl || exit 1
grep "$PAT" *  | grep -v ERR_FATAL_ERROR | awk '{print $3;}' | sort -u >$X1
cd ../..

for F in `cat $X1` ; do
    git grep -l --full-name -F $F >$X2
    NUM=`wc -l <$X2`
    test $NUM -gt 2 && continue
    if grep -q $F crypto/err/openssl.ec ; then
        echo Possibly unused $F found in openssl.ec
        continue
    fi
    echo $F
    for FILE in `cat $X2` ; do
        grep -v -w $F <$FILE >$FILE.new
        mv $FILE.new $FILE
    done
done

rm $X1 $X2
