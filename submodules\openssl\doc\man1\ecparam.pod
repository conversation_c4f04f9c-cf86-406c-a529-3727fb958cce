=pod

=head1 NAME

openssl-ecparam,
ecparam - EC parameter manipulation and generation

=head1 SYNOPSIS

B<openssl ecparam>
[B<-help>]
[B<-inform DER|PEM>]
[B<-outform DER|PEM>]
[B<-in filename>]
[B<-out filename>]
[B<-noout>]
[B<-text>]
[B<-C>]
[B<-check>]
[B<-name arg>]
[B<-list_curves>]
[B<-conv_form arg>]
[B<-param_enc arg>]
[B<-no_seed>]
[B<-rand file...>]
[B<-writerand file>]
[B<-genkey>]
[B<-engine id>]

=head1 DESCRIPTION

This command is used to manipulate or generate EC parameter files.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform DER|PEM>

This specifies the input format. The B<DER> option uses an ASN.1 DER encoded
form compatible with RFC 3279 EcpkParameters. The PEM form is the default
format: it consists of the B<DER> format base64 encoded with additional
header and footer lines.

=item B<-outform DER|PEM>

This specifies the output format, the options have the same meaning and default
as the B<-inform> option.

=item B<-in filename>

This specifies the input filename to read parameters from or standard input if
this option is not specified.

=item B<-out filename>

This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should B<not> be the same
as the input filename.

=item B<-noout>

This option inhibits the output of the encoded version of the parameters.

=item B<-text>

This option prints out the EC parameters in human readable form.

=item B<-C>

This option converts the EC parameters into C code. The parameters can then
be loaded by calling the get_ec_group_XXX() function.

=item B<-check>

Validate the elliptic curve parameters.

=item B<-name arg>

Use the EC parameters with the specified 'short' name. Use B<-list_curves>
to get a list of all currently implemented EC parameters.

=item B<-list_curves>

If this options is specified B<ecparam> will print out a list of all
currently implemented EC parameters names and exit.

=item B<-conv_form>

This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: B<compressed>, B<uncompressed> (the
default value) and B<hybrid>. For more information regarding
the point conversion forms please read the X9.62 standard.
B<Note> Due to patent issues the B<compressed> option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro B<OPENSSL_EC_BIN_PT_COMP> at compile time.

=item B<-param_enc arg>

This specifies how the elliptic curve parameters are encoded.
Possible value are: B<named_curve>, i.e. the ec parameters are
specified by an OID, or B<explicit> where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is B<named_curve>.
B<Note> the B<implicitlyCA> alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.

=item B<-no_seed>

This option inhibits that the 'seed' for the parameter generation
is included in the ECParameters structure (see RFC 3279).

=item B<-genkey>

This option will generate an EC private key using the specified parameters.

=item B<-rand file...>

A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is B<;> for MS-Windows, B<,> for OpenVMS, and B<:> for
all others.

=item [B<-writerand file>]

Writes random data to the specified I<file> upon exit.
This can be used with a subsequent B<-rand> flag.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<ecparam>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=back

=head1 NOTES

PEM format EC parameters use the header and footer lines:

 -----BEGIN EC PARAMETERS-----
 -----END EC PARAMETERS-----

OpenSSL is currently not able to generate new groups and therefore
B<ecparam> can only create EC parameters from known (named) curves.

=head1 EXAMPLES

To create EC parameters with the group 'prime192v1':

  openssl ecparam -out ec_param.pem -name prime192v1

To create EC parameters with explicit parameters:

  openssl ecparam -out ec_param.pem -name prime192v1 -param_enc explicit

To validate given EC parameters:

  openssl ecparam -in ec_param.pem -check

To create EC parameters and a private key:

  openssl ecparam -out ec_key.pem -name prime192v1 -genkey

To change the point encoding to 'compressed':

  openssl ecparam -in ec_in.pem -out ec_out.pem -conv_form compressed

To print out the EC parameters to standard output:

  openssl ecparam -in ec_param.pem -noout -text

=head1 SEE ALSO

L<ec(1)>, L<dsaparam(1)>

=head1 COPYRIGHT

Copyright 2003-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
