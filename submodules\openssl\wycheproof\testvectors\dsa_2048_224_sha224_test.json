{"algorithm": "DSA", "generatorVersion": "0.8rc18", "numberOfTests": 330, "header": ["Test vectors of test DsaVerify are intended for checking the signature", "verification of DSA signatures."], "notes": {"EdgeCase": "Some implementations of DSA do not properly check for boundaries. In some cases the modular inverse of 0 is simply 0. As a result there are implementations where values such as r=1, s=0 lead to forgeries.", "NoLeadingZero": "ASN encoded integers with a leading hex-digit in the range 8 .. F are negative. If the first hex-digit of a positive integer is 8 .. F then a leading 0 must be added. Some libraries forgot to do this an therefore generated invalid DSA signatures. Some providers, accept such legacy signatures for compatibility."}, "schema": "dsa_verify_schema.json", "testGroups": [{"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAHnf4QrGuD82ZKdOUFh1B4UYU/3UHqaMfSh8U0i4qYnofTllmJIg/GlsW\njpQlFG8i1fbuKHV0FHFLuZS6ESnwFdbgSnF+35tTCl1cq5TxRjHotM95rrNYzHQY\nRVU4QeisRhYw6ASmL0Nna6Z5SvZomcN3uGnqYSp7n+ZhGqlr5S64tiyXkRe7vMqK\nfsHh/6scffz8cEhwDTrjhYE26JdwHXwpIbXf7x0fiX9Q2WyhtcLtxYytoYkZ41ZC\n8IB+6/oAyZoy9NCVwxiPeO1UcRvgMlxLUyrszWVApWfDJyJUQOoVMZveBlEEeaGG\nF5niW1fezHPANtdaBwK9NzyiMTSZMQ==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 1, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303c021ca545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d028000a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0280068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "303f0000021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "3042498177303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "30412500303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "303f303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422222498177021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "304122212500021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "3045221f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0004deadbeef021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2221498177021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b22202500021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b221e021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045aa00bb00cd00303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043aa02aabb303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "30452225aa00bb00cd00021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "30432223aa02aabb021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2224aa00bb00cd00021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2222aa02aabb021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30412280021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2280021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30412280031d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2280031c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3041300102303c1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a800", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a805000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "303f3000021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a83000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3040021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "303f303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305b021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e02811d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02811c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f0282001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0282001c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021e00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021b068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30420285010000001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0285010000001c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046028901000000000000001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b028901000000000000001c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304102847fffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02847fffffff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30410284ffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0284ffffffff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30420285ffffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0285ffffffffff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30450288ffffffffffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0288ffffffffffffffff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d02ff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02ff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "301e021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "301f02021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021f00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021f000000a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e0000068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021f00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0500021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a80500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30200281021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30200500021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d011d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d031d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d041d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303dff1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b001c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b011c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b031c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b041c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7bff1c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30200200021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "30412221020100021ca545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2220020106021b8259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021d02a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c048259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1bfb021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea428", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021c00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021b068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021b8259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021eff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021dff068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3021090180021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b090180", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3021020100021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b020100", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d01603c6cd3f3ac5f55da5295ec5ee9ddcc947e8af9d2254162e62f84d8021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021cea4f3f86e8ba6f961c82a11ccbfa4ec0b61926925c5a3fe16c84b21e021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021dff5aba29d291cc988a0495647b6a8de9b95ab42739e8c03f5dd6a5e485021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021c15b0c07917459069e37d5ee33405b13f49e6d96da3a5c01e937b4de2021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021dfe9fc3932c0c53a0aa25ad6a13a11622336b8175062ddabe9d19d07b28021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d01a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021c5aba29d291cc988a0495647b6a8de9b95ab42739e8c03f5dd6a5e485021c068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d00c178f07615a75535ca0ee2274e824a59fef7f79ef575a73a1e040e05", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021dff4b8bc3290ab565760c3eed57bb92bb4e209293377faaa5b8a4593b4b", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021cf97da6306fd1a2aa14d918407af57d2bf03aba94c56fd9869ed15b58", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d00b4743cd6f54a9a89f3c112a8446d44b1df6d6cc880555a475ba6c4b5", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021dff3e870f89ea58aaca35f11dd8b17db5a6010808610a8a58c5e1fbf1fb", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d01068259cf902e5d55eb26e7bf850a82d40fc5456b3a902679612ea4a8", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d00f97da6306fd1a2aa14d918407af57d2bf03aba94c56fd9869ed15b58", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 225, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 226, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 227, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 228, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 229, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 230, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 231, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 232, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 233, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 234, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 235, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 236, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 237, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 238, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 239, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 240, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 241, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 242, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 243, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 244, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 245, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 246, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 247, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 248, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 249, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 250, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 251, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d01000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 252, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 253, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 254, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 255, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 256, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 257, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 258, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 259, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d0100000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 260, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 261, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 262, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 263, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 264, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 265, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 266, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 267, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 268, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 269, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 270, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 271, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 272, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 273, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 274, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 275, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 276, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 277, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 278, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 279, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 280, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 281, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 282, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 283, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 284, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 285, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 286, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 287, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 288, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 289, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 290, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 291, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 292, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 293, "comment": "random signatures", "msg": "313233343030", "sig": "303d021d00a939df97ddbe605a925e2456acc196ceea94410d54eed9d501befb90021c2f50ce7bac70f84f265794a3ada363f9afacf7b88c5273e23cac5e55", "result": "valid", "flags": []}, {"tcId": 294, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c3b98fa1f5ea18af8e2878571152f257accf243342582a757535f4a46021c1d86c74f77cde3fdeb48efa02ca06a5264981310fa5922339d52dfff", "result": "valid", "flags": []}, {"tcId": 295, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c6413ccb5d0de22129ab5f861f571d9d9419e057101f990cebb2a52e5021c3e332c78c75288f96057795872cbe64f343100b5df2353f60ed257f5", "result": "valid", "flags": []}, {"tcId": 296, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c19dc7c18a0ca1e947b095782aa5ab1e6c3f2ca329d6070959833d88c021c183eefdcfd75ff8dcdb2c17c184529b8accfa7cb2a5c94d214fa459c", "result": "valid", "flags": []}, {"tcId": 297, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c720931df5201f87af025960a55f815e841d827b85f047b789bb026f1021c25541b566f22f776996699acd28248c9c3c3313d1508c8a09cc582e6", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "32323534363238393834", "sig": "303d021d00ae0bf0ce838e2edb37604efa49b6cd1d5f900c3c64e5736c673339b8021c18ef9233f76217a6ad249b1f08c59457918a60ea86e8c2a277938c89", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "31383237383738363130", "sig": "303d021d00863925f1ecb1dbf5a42d756df18161339f89034584f3642fe5fa43c8021c2f0a360afd74ead00fca0e66c4564e2ceda8870e61059e8ca380f98a", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31353138383631373039", "sig": "303c021c7bdc97bca7f3013fc6b6fc0034d723de4cf7c7039d09af4ef5f2f4fa021c2650562418258e080bf50e7b81d95b4ec77b92991a26d0386833ea74", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "32313239323333343232", "sig": "303c021c051b0d01cfc2a31d139a670eb6f091e9436cc525a9b0242e49428b63021c718785905b3499e7934112656136da4809529f068c50f96e8e834b76", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "31323231393739303539", "sig": "303c021c099ce9fa75f5fff9cf3ca5781378269376d9faa205711a6b8aa15690021c240586c456884c48b71a2e5b0a1dd0bb4471d5c9bc11c06e6618f84f", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "32333032353637363131", "sig": "303c021c52a9967eb246ed578e094b3fd0722864015f065e419fe86dbc965626021c4b1a9a97c54387f2a4a6ed53c7280450151ec94979e8648bf0307db7", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "31313035383638343732", "sig": "303d021d00a5d86a6109be1f7fc13bfa24f3988ab773dd31fc48c078bcf1810d80021c6db2be3b6ae263111b540ab708acdf1d72408fd073e8032634d2dfcc", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "31343636363134343432", "sig": "303d021c6fe4095c131b39ec9e568d2079701707f68f8fc2adbaa110b6b25023021d009f8b5e066750f5e9f2afe77a3a377367288637c6d045d53900368a66", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343431393536343230", "sig": "303c021c255b9c7a2e453c0e737c6e8cdb2d0a70ff83286381636c8e257acfde021c4da758b8cc6b44beb5334530d0f18aa435f1f13624f57408e5256a3d", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "31393639343836303634", "sig": "303c021c1cfdc22c4198e7c2e8c5f4d444a66af35f4fc050ab5cd79d53e4c2fe021c08fa67477ae8bd723537f3f67bdc07691341cab13c60ac6619e4fd0e", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "32323335363732383833", "sig": "303d021c07e4399a8efea40467291eadd4b72603f5239585dcdaa9af0886b4f1021d00aa6fc9c29a83b42cc38177643363381764b7bcb0ee1b36e9dcc1305c", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32323537383433373033", "sig": "303d021d008325d2277a644be50232ac0d5d8ef9672c3512bcb63518e57f9da7f8021c6cdcfa9c16a84114784db6a775802937cfbe796df1d1883b330c4d5d", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "393434353030393436", "sig": "303d021d00b56f2fc3a14310f4f7139e4b87c63b2603094c270ba9ead2b3689dab021c1188a91b903413844e241fe4f7118635c7c085a465dfa89fe81cd072", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "31363837373839343130", "sig": "303d021d008d6dd789d09df289b7344a0868d9d93c61ef71ba2aa60f57a9044962021c31da5aea906b4c5618def7f89d275ce6d3947bd00cd77044d213f587", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "333933323631323238", "sig": "303d021d009a489a5bf29ac49e700ed1c7a61eaf422242e9a9f706f30938353494021c06b93abe7678e8bb9e6a3744581ba11e657021299f76b7803ce15fcb", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "31303733353731303935", "sig": "303d021d0089eef41c40f43032e2c17f80e3bfa8125ba4bef9c5590ea7704067c1021c5a61599ed31031a0d5a9ae6fb1338b0002c7e647a4a76738c7f7ffdb", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "3630383837343734", "sig": "303e021d00b1b4bd210c230ad71527f0afdf31872d583978e7b44a4c8b0846f4fe021d00912a4e8750aeaa073dfca5c206f645a8e8f498067331ec037cbbd0e2", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "31313932353639393632", "sig": "303d021c4ad46013a0286073fbbfed901623f6838ccd6c81e3e18570b2f5c13c021d00933c2e37d7f559f905fa14277144e2b64d20bf03c41069286139d8cc", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "3930303736303933", "sig": "303d021c159879b8e270418199fb051152897126c80286bd38343fe914737b36021d008a31fa7a9aac01e3df0770d6eaa9476ea36f47295c194a50d62b5eab", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "31363032383336313337", "sig": "303c021c269cc182e0bbd081be7d135ff72355c67a58336657767abf4b9b6405021c1684f1019a21d80feb1bdb4554933e8067feb6f0bf17437d2e63aaa6", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "32303830323435363734", "sig": "303c021c1fd07fe19939130250715186cc77196dcbe20e8e6d67c47b0a6dcf28021c776f5809de588faddee252de1755a95f7b1f8a073fec81bad1972024", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31373938323734363539", "sig": "303c021c5b2d08ff9d8314a064b9bf7e65efc880c41b37dab090c5a33405664b021c6f790ac01d38c8adf3e9012f3f0e5bc38e17778cdcddf2cddabc75b0", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "31313535353737373230", "sig": "303c021c6e6d35062b651c452c25327b4d99544fc2262be58c57bc8ff75f193a021c710c4ab62dfa6fb982505b9c4415ffb117808ec1c4b8a5aee5d92722", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "32343332343734363634", "sig": "303c021c416b0d4f56fb6e2d1075e910c4cb349e8bff99f60aaaafa6a7398f45021c212d7473330f9bc0e22758072195982cc50d6f4146c87ff72be0bbf8", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "32343137323832323737", "sig": "303c021c5f123d0b2862885918d2a67afc9743657bc6c603abfc0ff7c1d11a27021c2ce308192c6ff01dd1eeb1709be7bfeab115a0edca1bc7b8de901f5a", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "3737383734373731", "sig": "303c021c6aaad7b9f862239389d833ab039e7d63b84401bb05155a228848cad4021c79a27fe2e493a1d29020cbb16c2921b1a87bd01bf8ef33e2d9882a37", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "6978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201006978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAaXi2jTEzTuW8ez6Rq2wjNvq0XGSDa9kstTN7c0256ORPiJ+IaYKfT+F0\n3JNEwWSgulsBJiWbqKQ/YHVk+kodDUlkXh1YhqH8SF4v6R5W6uMw2gXhews9AYwp\nAoWySbxAnnr1QwD8fD6zSRFFfiNxkxrZMC6EUM2V3z1WHqCtlNCi6ryv4N1nKPso\nACm1Vtn0+nwPRqeAQymTZwjpfhH8IrKlB2GokMZbX+oqGkFy9r6eqmDnOM32DAFR\nQuLlYrtioR6BDM3wv2MzBzgvLZqXabEV382rS6yuc/7KKJ2yCdzjTL4Sbox/nZ5P\nj3ETSaYI1We0jAUOnfsyvBhOyqTw8A==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 324, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 325, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 326, "comment": "r = 1, u2 small", "msg": "54657374", "sig": "3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}, {"tcId": 327, "comment": "r = 1, s = q-1", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "2a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201002a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAKmSVO95AeJ+A7YInGSKGEVuS0J1d6WkE6APsTs+9c+DwjoKRD+vxn6PN\nxV/yDrlw2ccS9EeFwP1ZLBf7Q/RiU1ekrIoaYo9yBArlNgg5x8H2shTnoVUw/iKI\ncTnqDwWp2vnZW9a3Rnq/kQfJ++MeNjMCdu7Mzj1ZY1IG1gyiVvmvYGJ2JrBZSYS1\noHXELEIGf6jDMPJYvPFF3yepfajuQZtU46spbHzp72oBEzibPKx4hbRLNyLSfK1g\n5OWpJKHtA0LOqemSVva8EwjUrywK+TebHPIRnOETwIVwX1UZzMG6hWKiI2GQ0/DA\noQ8BRmrXmkgSfChDP2s04kpTmvYPPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 328, "comment": "s = 1", "msg": "54657374", "sig": "3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "1d8f9992387355ff9381199d883e099d798bb730a7ad074236eb863b14b6aafe487ff8e7276232e938537f18d0fbea02c9ad305b31ef80ca5bfa2560a128b7c6342c692f3ecaf31e16866fe6395f10e6669caed8aa827708d890241f0011f5c2f390804b462e93ea319de7a44bbaf21ff98743611a0a4dd6def604c0bd6ff9d673db27b3ef6b16ac6b32f9fac975562b15908acdf909636b8622467c4b08b812485270f2797f9421dd9998b60b83e738cd359767da3c69a1ec1c4848d1f8dd4bf07282dec668b0fefa480336b0cc428a546620075dfcb488e14076ec76d20bba7111eba9e74e7846502f2b33b9ace80dfd92077c4bc9f396ffa90947de3eb604"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001d8f9992387355ff9381199d883e099d798bb730a7ad074236eb863b14b6aafe487ff8e7276232e938537f18d0fbea02c9ad305b31ef80ca5bfa2560a128b7c6342c692f3ecaf31e16866fe6395f10e6669caed8aa827708d890241f0011f5c2f390804b462e93ea319de7a44bbaf21ff98743611a0a4dd6def604c0bd6ff9d673db27b3ef6b16ac6b32f9fac975562b15908acdf909636b8622467c4b08b812485270f2797f9421dd9998b60b83e738cd359767da3c69a1ec1c4848d1f8dd4bf07282dec668b0fefa480336b0cc428a546620075dfcb488e14076ec76d20bba7111eba9e74e7846502f2b33b9ace80dfd92077c4bc9f396ffa90947de3eb604", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAHY+ZkjhzVf+TgRmdiD4JnXmLtzCnrQdCNuuGOxS2qv5If/jnJ2Iy6ThT\nfxjQ++oCya0wWzHvgMpb+iVgoSi3xjQsaS8+yvMeFoZv5jlfEOZmnK7YqoJ3CNiQ\nJB8AEfXC85CAS0Yuk+oxneekS7ryH/mHQ2EaCk3W3vYEwL1v+dZz2yez72sWrGsy\n+frJdVYrFZCKzfkJY2uGIkZ8Swi4EkhScPJ5f5Qh3ZmYtguD5zjNNZdn2jxpoewc\nSEjR+N1L8HKC3sZosP76SAM2sMxCilRmIAdd/LSI4UB27HbSC7pxEeup5054RlAv\nKzO5rOgN/ZIHfEvJ85b/qQlH3j62BA==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 329, "comment": "u2 small", "msg": "54657374", "sig": "303d021c2b5a9e2ff5f7aa2ed6ff534908262d0ae5d070377f67704103a5a7c2021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "516b4a830ae8f21865f7467fcfe264ae975e08ea174bfa27dbea118f1998c473dcd30ec099580fd63dfe601ec2df835eef25597bebe18bac31c6bde258f6543d5703d8c3dfba6e26552ab773f557b6d9a429dd65e96e2473ee5b9383761eda17ac96fd9d239226ad14cd6647f2962b2d2297060dc7f2558d84e6da8ee4bdccb24c74acd7267551e434c7e54052af99dba6121f5efc86db6cdf6b8f9ce1ac77396187894c1420fbe90ec0845528a7b3212df13a3ff8eff84341271240623601fd41f3af8466aeffc0ff1f2eac1878d5fc604e9f19446cdf79865a4c2451cbbca978a254a0fe8fe19fb667e35787bc200b00452f42052bfc55e6fcf6ea9dd8f715"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201050002820100516b4a830ae8f21865f7467fcfe264ae975e08ea174bfa27dbea118f1998c473dcd30ec099580fd63dfe601ec2df835eef25597bebe18bac31c6bde258f6543d5703d8c3dfba6e26552ab773f557b6d9a429dd65e96e2473ee5b9383761eda17ac96fd9d239226ad14cd6647f2962b2d2297060dc7f2558d84e6da8ee4bdccb24c74acd7267551e434c7e54052af99dba6121f5efc86db6cdf6b8f9ce1ac77396187894c1420fbe90ec0845528a7b3212df13a3ff8eff84341271240623601fd41f3af8466aeffc0ff1f2eac1878d5fc604e9f19446cdf79865a4c2451cbbca978a254a0fe8fe19fb667e35787bc200b00452f42052bfc55e6fcf6ea9dd8f715", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAUWtKgwro8hhl90Z/z+JkrpdeCOoXS/on2+oRjxmYxHPc0w7AmVgP1j3+\nYB7C34Ne7yVZe+vhi6wxxr3iWPZUPVcD2MPfum4mVSq3c/VXttmkKd1l6W4kc+5b\nk4N2HtoXrJb9nSOSJq0UzWZH8pYrLSKXBg3H8lWNhObajuS9zLJMdKzXJnVR5DTH\n5UBSr5nbphIfXvyG22zfa4+c4ax3OWGHiUwUIPvpDsCEVSinsyEt8To/+O/4Q0En\nEkBiNgH9QfOvhGau/8D/Hy6sGHjV/GBOnxlEbN95hlpMJFHLvKl4olSg/o/hn7Zn\n41eHvCALAEUvQgUr/FXm/Pbqndj3FQ==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 330, "comment": "s = q - 1", "msg": "54657374", "sig": "303d021c2b5a9e2ff5f7aa2ed6ff534908262d0ae5d070377f67704103a5a7c2021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}]}