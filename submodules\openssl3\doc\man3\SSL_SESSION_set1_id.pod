=pod

=head1 NAME

SSL_SESSION_get_id,
SSL_SESSION_set1_id
- get and set the SSL session ID

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 const unsigned char *SSL_SESSION_get_id(const SSL_SESSION *s,
                                         unsigned int *len);
 int SSL_SESSION_set1_id(SSL_SESSION *s, const unsigned char *sid,
                         unsigned int sid_len);

=head1 DESCRIPTION

SSL_SESSION_get_id() returns a pointer to the internal session id value for the
session B<s>. The length of the id in bytes is stored in B<*len>. The length may
be 0. The caller should not free the returned pointer directly.

SSL_SESSION_set1_id() sets the session ID for the B<ssl> SSL/TLS session
to B<sid> of length B<sid_len>.

=head1 RETURN VALUES

SSL_SESSION_get_id() returns a pointer to the session id value.
SSL_SESSION_set1_id() returns 1 for success and 0 for failure, for example
if the supplied session ID length exceeds B<SSL_MAX_SSL_SESSION_ID_LENGTH>.

=head1 SEE ALSO

L<ssl(7)>

=head1 HISTORY

The SSL_SESSION_set1_id() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
