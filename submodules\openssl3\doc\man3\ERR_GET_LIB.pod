=pod

=head1 NAME

ERR_GET_LIB, ERR_GET_REASON, ERR_FATAL_ERROR
- get information from error codes

=head1 SYNOPSIS

 #include <openssl/err.h>

 int ERR_GET_LIB(unsigned long e);

 int ERR_GET_REASON(unsigned long e);

 int ERR_FATAL_ERROR(unsigned long e);

=head1 DESCRIPTION

The error code returned by ERR_get_error() consists of a library
number and reason code. ERR_GET_LIB()
and ERR_GET_REASON() can be used to extract these.

ERR_FATAL_ERROR() indicates whether a given error code is a fatal error.

The library number describes where the error
occurred, the reason code is the information about what went wrong.

Each sub-library of OpenSSL has a unique library number; the
reason code is unique within each sub-library.  Note that different
libraries may use the same value to signal different reasons.

B<ERR_R_...> reason codes such as B<ERR_R_MALLOC_FAILURE> are globally
unique. However, when checking for sub-library specific reason codes,
be sure to also compare the library number.

ERR_GET_LIB(), ERR_GET_REASON(), and ERR_FATAL_ERROR() are macros.

=head1 RETURN VALUES

The library number, reason code, and whether the error
is fatal, respectively.
Starting with OpenSSL 3.0.0, the function code is always set to zero.

=head1 NOTES

Applications should not make control flow decisions based on specific error
codes. Error codes are subject to change at any time (even in patch releases of
OpenSSL). A particular error code can only be considered meaningful for control
flow decisions if it is explicitly documented as such. New failure codes may
still appear at any time.

=head1 SEE ALSO

L<ERR_get_error(3)>

=head1 HISTORY

ERR_GET_LIB() and ERR_GET_REASON() are available in all versions of OpenSSL.

ERR_GET_FUNC() was removed in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
