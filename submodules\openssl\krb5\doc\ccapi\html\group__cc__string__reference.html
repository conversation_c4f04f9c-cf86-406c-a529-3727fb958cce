<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_string_t Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_string_t Overview</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_string_t represents a C string returned by the API. It has a pointer to the string data and a release() function. This type is used for both principal names and ccache names returned by the API. Principal names may contain UTF-8 encoded strings for internationalization purposes.<p>
For API function documentation see <a class="el" href="structcc__string__f.html">cc_string_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__string__d.html">cc_string_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__string__f.html">cc_string_f</a> <a class="el" href="group__cc__string__reference.html#g3d01e5c05378e9e78f126156137d51ce">cc_string_f</a>
<li>typedef <a class="el" href="structcc__string__d.html">cc_string_d</a> <a class="el" href="group__cc__string__reference.html#g80f57852683632a4c587af5b0f545dea">cc_string_d</a>
<li>typedef <a class="el" href="structcc__string__d.html">cc_string_d</a> * <a class="el" href="group__cc__string__reference.html#ge6bb6a8c574096b66b5def65eb87a40e">cc_string_t</a>
</ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g3d01e5c05378e9e78f126156137d51ce"></a><!-- doxytag: member="CredentialsCache.h::cc_string_f" ref="g3d01e5c05378e9e78f126156137d51ce" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__string__f.html">cc_string_f</a> <a class="el" href="structcc__string__f.html">cc_string_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g80f57852683632a4c587af5b0f545dea"></a><!-- doxytag: member="CredentialsCache.h::cc_string_d" ref="g80f57852683632a4c587af5b0f545dea" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__string__d.html">cc_string_d</a> <a class="el" href="structcc__string__d.html">cc_string_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="ge6bb6a8c574096b66b5def65eb87a40e"></a><!-- doxytag: member="CredentialsCache.h::cc_string_t" ref="ge6bb6a8c574096b66b5def65eb87a40e" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__string__d.html">cc_string_d</a>* <a class="el" href="structcc__string__d.html">cc_string_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
