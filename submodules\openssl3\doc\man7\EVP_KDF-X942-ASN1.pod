=pod

=head1 NAME

EVP_KDF-X942-ASN1 - The X9.42-2003 asn1 EVP_KDF implementation

=head1 DESCRIPTION

The EVP_KDF-X942-ASN1 algorithm implements the key derivation function
X942KDF-ASN1. It is used by DH KeyAgreement, to derive a key using input such as
a shared secret key and other info. The other info is DER encoded data that
contains a 32 bit counter as well as optional fields for "partyu-info",
"partyv-info", "supp-pubinfo" and "supp-privinfo".
This kdf is used by Cryptographic Message Syntax (CMS).

=head2 Identity

"X942KDF-ASN1" or "X942KDF" is the name for this implementation; it
can be used with the EVP_KDF_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "properties" (B<OSSL_KDF_PARAM_PROPERTIES>) <UTF8 string>

=item "digest" (B<OSSL_KDF_PARAM_DIGEST>) <UTF8 string>

These parameters work as described in L<EVP_KDF(3)/PARAMETERS>.

=item "secret" (B<OSSL_KDF_PARAM_SECRET>) <octet string>

The shared secret used for key derivation.  This parameter sets the secret.

=item "acvp-info" (B<OSSL_KDF_PARAM_X942_ACVPINFO>) <octet string>

This value should not be used in production and should only be used for ACVP
testing. It is an optional octet string containing a combined DER encoded blob
of any of the optional fields related to "partyu-info", "partyv-info",
"supp-pubinfo" and "supp-privinfo". If it is specified then none of these other
fields should be used.

=item "partyu-info" (B<OSSL_KDF_PARAM_X942_PARTYUINFO>) <octet string>

An optional octet string containing public info contributed by the initiator.

=item "ukm" (B<OSSL_KDF_PARAM_UKM>) <octet string>

An alias for "partyu-info".
In CMS this is the user keying material.

=item "partyv-info" (B<OSSL_KDF_PARAM_X942_PARTYVINFO>) <octet string>

An optional octet string containing public info contributed by the responder.

=item "supp-pubinfo" (B<OSSL_KDF_PARAM_X942_SUPP_PUBINFO>) <octet string>

An optional octet string containing some additional, mutually-known public
information. Setting this value also sets "use-keybits" to 0.

=item "use-keybits" (B<OSSL_KDF_PARAM_X942_USE_KEYBITS>) <integer>

The default value of 1 will use the KEK key length (in bits) as the
"supp-pubinfo". A value of 0 disables setting the "supp-pubinfo".

=item "supp-privinfo" (B<OSSL_KDF_PARAM_X942_SUPP_PRIVINFO>) <octet string>

An optional octet string containing some additional, mutually-known private
information.

=item "cekalg" (B<OSSL_KDF_PARAM_CEK_ALG>) <UTF8 string>

This parameter sets the CEK wrapping algorithm name.
Valid values are "AES-128-WRAP", "AES-192-WRAP", "AES-256-WRAP" and "DES3-WRAP".

=back

=head1 NOTES

A context for X942KDF can be obtained by calling:

 EVP_KDF *kdf = EVP_KDF_fetch(NULL, "X942KDF", NULL);
 EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);

The output length of an X942KDF is specified via the I<keylen>
parameter to the L<EVP_KDF_derive(3)> function.

=head1 EXAMPLES

This example derives 24 bytes, with the secret key "secret" and random user
keying material:

  EVP_KDF_CTX *kctx;
  EVP_KDF_CTX *kctx;
  unsigned char out[192/8];
  unsignred char ukm[64];
  OSSL_PARAM params[5], *p = params;

  if (RAND_bytes(ukm, sizeof(ukm)) <= 0)
      error("RAND_bytes");

  kdf = EVP_KDF_fetch(NULL, "X942KDF", NULL);
  if (kctx == NULL)
      error("EVP_KDF_fetch");
  kctx = EVP_KDF_CTX_new(kdf);
  EVP_KDF_free(kdf);
  if (kctx == NULL)
      error("EVP_KDF_CTX_new");

  *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST, "SHA256", 0);
  *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
                                           "secret", (size_t)6);
  *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_UKM, ukm, sizeof(ukm));
  *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_CEK_ALG, "AES-256-WRAP, 0);
  *p = OSSL_PARAM_construct_end();
  if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
      error("EVP_KDF_derive");

  EVP_KDF_CTX_free(kctx);

=head1 CONFORMING TO

ANS1 X9.42-2003
RFC 2631

=head1 SEE ALSO

L<EVP_KDF(3)>,
L<EVP_KDF_CTX_new(3)>,
L<EVP_KDF_CTX_free(3)>,
L<EVP_KDF_CTX_set_params(3)>,
L<EVP_KDF_CTX_get_kdf_size(3)>,
L<EVP_KDF_derive(3)>,
L<EVP_KDF(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
