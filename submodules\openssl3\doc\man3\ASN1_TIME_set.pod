=pod

=head1 NAME

ASN1_TIME_set, ASN1_UTCTIME_set, ASN1_GENERALIZEDTIME_set,
ASN1_TIME_adj, ASN1_UTCTIME_adj, ASN1_GENERALIZEDTIME_adj,
ASN1_TIME_check, ASN1_UTCTIME_check, ASN1_GENERALIZEDTIME_check,
ASN1_TIME_set_string, ASN1_UTCTIME_set_string, ASN1_GENERALIZEDTIME_set_string,
ASN1_TIME_set_string_X509,
ASN1_TIME_normalize,
ASN1_TIME_to_tm,
ASN1_TIME_print, ASN1_TIME_print_ex, ASN1_UTCTIME_print, ASN1_GENERALIZEDTIME_print,
ASN1_TIME_diff,
ASN1_TIME_cmp_time_t, ASN1_UTCTIME_cmp_time_t,
ASN1_TIME_compare,
ASN1_TIME_to_generalizedtime,
ASN1_TIME_dup, ASN1_UTCTIME_dup, ASN1_GENERALIZEDTIME_dup - ASN.1 Time functions

=head1 SYNOPSIS

 ASN1_TIME *ASN1_TIME_set(ASN1_TIME *s, time_t t);
 ASN1_UTCTIME *ASN1_UTCTIME_set(ASN1_UTCTIME *s, time_t t);
 ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_set(ASN1_GENERALIZEDTIME *s,
                                                time_t t);

 ASN1_TIME *ASN1_TIME_adj(ASN1_TIME *s, time_t t, int offset_day,
                          long offset_sec);
 ASN1_UTCTIME *ASN1_UTCTIME_adj(ASN1_UTCTIME *s, time_t t,
                                int offset_day, long offset_sec);
 ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_adj(ASN1_GENERALIZEDTIME *s,
                                                time_t t, int offset_day,
                                                long offset_sec);

 int ASN1_TIME_set_string(ASN1_TIME *s, const char *str);
 int ASN1_TIME_set_string_X509(ASN1_TIME *s, const char *str);
 int ASN1_UTCTIME_set_string(ASN1_UTCTIME *s, const char *str);
 int ASN1_GENERALIZEDTIME_set_string(ASN1_GENERALIZEDTIME *s,
                                     const char *str);

 int ASN1_TIME_normalize(ASN1_TIME *s);

 int ASN1_TIME_check(const ASN1_TIME *t);
 int ASN1_UTCTIME_check(const ASN1_UTCTIME *t);
 int ASN1_GENERALIZEDTIME_check(const ASN1_GENERALIZEDTIME *t);

 int ASN1_TIME_print(BIO *b, const ASN1_TIME *s);
 int ASN1_TIME_print_ex(BIO *bp, const ASN1_TIME *tm, unsigned long flags);
 int ASN1_UTCTIME_print(BIO *b, const ASN1_UTCTIME *s);
 int ASN1_GENERALIZEDTIME_print(BIO *b, const ASN1_GENERALIZEDTIME *s);

 int ASN1_TIME_to_tm(const ASN1_TIME *s, struct tm *tm);
 int ASN1_TIME_diff(int *pday, int *psec, const ASN1_TIME *from,
                    const ASN1_TIME *to);

 int ASN1_TIME_cmp_time_t(const ASN1_TIME *s, time_t t);
 int ASN1_UTCTIME_cmp_time_t(const ASN1_UTCTIME *s, time_t t);

 int ASN1_TIME_compare(const ASN1_TIME *a, const ASN1_TIME *b);

 ASN1_GENERALIZEDTIME *ASN1_TIME_to_generalizedtime(ASN1_TIME *t,
                                                    ASN1_GENERALIZEDTIME **out);

 ASN1_TIME *ASN1_TIME_dup(const ASN1_TIME *t);
 ASN1_UTCTIME *ASN1_UTCTIME_dup(const ASN1_UTCTIME *t);
 ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_dup(const ASN1_GENERALIZEDTIME *t);

=head1 DESCRIPTION

The ASN1_TIME_set(), ASN1_UTCTIME_set() and ASN1_GENERALIZEDTIME_set()
functions set the structure I<s> to the time represented by the time_t
value I<t>. If I<s> is NULL a new time structure is allocated and returned.

The ASN1_TIME_adj(), ASN1_UTCTIME_adj() and ASN1_GENERALIZEDTIME_adj()
functions set the time structure I<s> to the time represented
by the time I<offset_day> and I<offset_sec> after the time_t value I<t>.
The values of I<offset_day> or I<offset_sec> can be negative to set a
time before I<t>. The I<offset_sec> value can also exceed the number of
seconds in a day. If I<s> is NULL a new structure is allocated
and returned.

The ASN1_TIME_set_string(), ASN1_UTCTIME_set_string() and
ASN1_GENERALIZEDTIME_set_string() functions set the time structure I<s>
to the time represented by string I<str> which must be in appropriate ASN.1
time format (for example YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ). If I<s> is NULL
this function performs a format check on I<str> only. The string I<str>
is copied into I<s>.

ASN1_TIME_set_string_X509() sets B<ASN1_TIME> structure I<s> to the time
represented by string I<str> which must be in appropriate time format
that RFC 5280 requires, which means it only allows YYMMDDHHMMSSZ and
YYYYMMDDHHMMSSZ (leap second is rejected), all other ASN.1 time format
are not allowed. If I<s> is NULL this function performs a format check
on I<str> only.

The ASN1_TIME_normalize() function converts an B<ASN1_GENERALIZEDTIME> or
B<ASN1_UTCTIME> into a time value that can be used in a certificate. It
should be used after the ASN1_TIME_set_string() functions and before
ASN1_TIME_print() functions to get consistent (i.e. GMT) results.

The ASN1_TIME_check(), ASN1_UTCTIME_check() and ASN1_GENERALIZEDTIME_check()
functions check the syntax of the time structure I<s>.

The ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print()
functions print the time structure I<s> to BIO I<b> in human readable
format. It will be of the format MMM DD HH:MM:SS YYYY [GMT], for example
"Feb  3 00:55:52 2015 GMT", which does not include a newline.
If the time structure has invalid format it prints out "Bad time value" and
returns an error. The output for generalized time may include a fractional part
following the second.

ASN1_TIME_print_ex() provides I<flags> to specify the output format of the
datetime. This can be either B<ASN1_DTFLGS_RFC822> or B<ASN1_DTFLGS_ISO8601>.

ASN1_TIME_to_tm() converts the time I<s> to the standard I<tm> structure.
If I<s> is NULL, then the current time is converted. The output time is GMT.
The I<tm_sec>, I<tm_min>, I<tm_hour>, I<tm_mday>, I<tm_wday>, I<tm_yday>,
I<tm_mon> and I<tm_year> fields of I<tm> structure are set to proper values,
whereas all other fields are set to 0. If I<tm> is NULL this function performs
a format check on I<s> only. If I<s> is in Generalized format with fractional
seconds, e.g. YYYYMMDDHHMMSS.SSSZ, the fractional seconds will be lost while
converting I<s> to I<tm> structure.

ASN1_TIME_diff() sets I<*pday> and I<*psec> to the time difference between
I<from> and I<to>. If I<to> represents a time later than I<from> then
one or both (depending on the time difference) of I<*pday> and I<*psec>
will be positive. If I<to> represents a time earlier than I<from> then
one or both of I<*pday> and I<*psec> will be negative. If I<to> and I<from>
represent the same time then I<*pday> and I<*psec> will both be zero.
If both I<*pday> and I<*psec> are nonzero they will always have the same
sign. The value of I<*psec> will always be less than the number of seconds
in a day. If I<from> or I<to> is NULL the current time is used.

The ASN1_TIME_cmp_time_t() and ASN1_UTCTIME_cmp_time_t() functions compare
the two times represented by the time structure I<s> and the time_t I<t>.

The ASN1_TIME_compare() function compares the two times represented by the
time structures I<a> and I<b>.

The ASN1_TIME_to_generalizedtime() function converts an B<ASN1_TIME> to an
B<ASN1_GENERALIZEDTIME>, regardless of year. If either I<out> or
I<*out> are NULL, then a new object is allocated and must be freed after use.

The ASN1_TIME_dup(), ASN1_UTCTIME_dup() and ASN1_GENERALIZEDTIME_dup() functions
duplicate the time structure I<t> and return the duplicated result
correspondingly.

=head1 NOTES

The B<ASN1_TIME> structure corresponds to the ASN.1 structure B<Time>
defined in RFC5280 et al. The time setting functions obey the rules outlined
in RFC5280: if the date can be represented by UTCTime it is used, else
GeneralizedTime is used.

The B<ASN1_TIME>, B<ASN1_UTCTIME> and B<ASN1_GENERALIZEDTIME> structures are
represented as an B<ASN1_STRING> internally and can be freed up using
ASN1_STRING_free().

The B<ASN1_TIME> structure can represent years from 0000 to 9999 but no attempt
is made to correct ancient calendar changes (for example from Julian to
Gregorian calendars).

B<ASN1_UTCTIME> is limited to a year range of 1950 through 2049.

Some applications add offset times directly to a time_t value and pass the
results to ASN1_TIME_set() (or equivalent). This can cause problems as the
time_t value can overflow on some systems resulting in unexpected results.
New applications should use ASN1_TIME_adj() instead and pass the offset value
in the I<offset_sec> and I<offset_day> parameters instead of directly
manipulating a time_t value.

ASN1_TIME_adj() may change the type from B<ASN1_GENERALIZEDTIME> to
B<ASN1_UTCTIME>, or vice versa, based on the resulting year.
ASN1_GENERALIZEDTIME_adj() and ASN1_UTCTIME_adj() will not modify the type
of the return structure.

It is recommended that functions starting with B<ASN1_TIME> be used instead of
those starting with B<ASN1_UTCTIME> or B<ASN1_GENERALIZEDTIME>. The functions
starting with B<ASN1_UTCTIME> and B<ASN1_GENERALIZEDTIME> act only on that
specific time format. The functions starting with B<ASN1_TIME> will operate on
either format.

=head1 BUGS

ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print() do
not print out the timezone: it either prints out "GMT" or nothing. But all
certificates complying with RFC5280 et al use GMT anyway.

ASN1_TIME_print(), ASN1_TIME_print_ex(), ASN1_UTCTIME_print() and
ASN1_GENERALIZEDTIME_print() do not distinguish if they fail because
of an I/O error or invalid time format.

Use the ASN1_TIME_normalize() function to normalize the time value before
printing to get GMT results.

=head1 RETURN VALUES

ASN1_TIME_set(), ASN1_UTCTIME_set(), ASN1_GENERALIZEDTIME_set(),
ASN1_TIME_adj(), ASN1_UTCTIME_adj() and ASN1_GENERALIZEDTIME_set() return
a pointer to a time structure or NULL if an error occurred.

ASN1_TIME_set_string(), ASN1_UTCTIME_set_string(),
ASN1_GENERALIZEDTIME_set_string() and ASN1_TIME_set_string_X509() return
1 if the time value is successfully set and 0 otherwise.

ASN1_TIME_normalize() returns 1 on success, and 0 on error.

ASN1_TIME_check(), ASN1_UTCTIME_check and ASN1_GENERALIZEDTIME_check() return 1
if the structure is syntactically correct and 0 otherwise.

ASN1_TIME_print(), ASN1_UTCTIME_print() and ASN1_GENERALIZEDTIME_print()
return 1 if the time is successfully printed out and
0 if an I/O error occurred an error occurred (I/O error or invalid time format).

ASN1_TIME_to_tm() returns 1 if the time is successfully parsed and 0 if an
error occurred (invalid time format).

ASN1_TIME_diff() returns 1 for success and 0 for failure. It can fail if the
passed-in time structure has invalid syntax, for example.

ASN1_TIME_cmp_time_t() and ASN1_UTCTIME_cmp_time_t() return -1 if I<s> is
before I<t>, 0 if I<s> equals I<t>, or 1 if I<s> is after I<t>. -2 is returned
on error.

ASN1_TIME_compare() returns -1 if I<a> is before I<b>, 0 if I<a> equals I<b>,
or 1 if I<a> is after I<b>. -2 is returned on error.

ASN1_TIME_to_generalizedtime() returns a pointer to the appropriate time
structure on success or NULL if an error occurred.

ASN1_TIME_dup(), ASN1_UTCTIME_dup() and ASN1_GENERALIZEDTIME_dup() return a
pointer to a time structure or NULL if an error occurred.

=head1 EXAMPLES

Set a time structure to one hour after the current time and print it out:

 #include <time.h>
 #include <openssl/asn1.h>

 ASN1_TIME *tm;
 time_t t;
 BIO *b;

 t = time(NULL);
 tm = ASN1_TIME_adj(NULL, t, 0, 60 * 60);
 b = BIO_new_fp(stdout, BIO_NOCLOSE);
 ASN1_TIME_print(b, tm);
 ASN1_STRING_free(tm);
 BIO_free(b);

Determine if one time is later or sooner than the current time:

 int day, sec;

 if (!ASN1_TIME_diff(&day, &sec, NULL, to))
     /* Invalid time format */

 if (day > 0 || sec > 0)
     printf("Later\n");
 else if (day < 0 || sec < 0)
     printf("Sooner\n");
 else
     printf("Same\n");

=head1 HISTORY

The ASN1_TIME_to_tm() function was added in OpenSSL 1.1.1.
The ASN1_TIME_set_string_X509() function was added in OpenSSL 1.1.1.
The ASN1_TIME_normalize() function was added in OpenSSL 1.1.1.
The ASN1_TIME_cmp_time_t() function was added in OpenSSL 1.1.1.
The ASN1_TIME_compare() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
