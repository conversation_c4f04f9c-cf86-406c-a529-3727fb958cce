version: 2.1

commands:
  docker-pull:
    parameters:
      image:
        type: string
    steps:
      - run: docker pull <<parameters.image>> || docker pull <<parameters.image>>
  docker-run:
    parameters:
      image:
        type: string
      command:
        type: string
    steps:
      - run: docker run -e PLATFORM -e PYTHON -v $(pwd):/test <<parameters.image>>  /bin/bash -c 'cd /test;<<parameters.command>>'

jobs:
  linux-arm64:
    machine:
      image: ubuntu-2004:current
    resource_class: arm.medium
    parameters:
      image:
        type: string
      toxenv:
        type: string
    steps:
      - checkout
      - docker-pull:
          image: <<parameters.image>>
      - docker-run:
          image: <<parameters.image>>
          command: /venv/bin/tox -e <<parameters.toxenv>>
  linux-arm64-wheel:
    machine:
      image: ubuntu-2004:current
    resource_class: arm.medium
    parameters:
      image:
        type: string
      platform:
        type: string
      python:
        type: string
    environment:
        PLATFORM: <<parameters.platform>>
        PYTHON: <<parameters.python>>
    steps:
      - checkout
      - docker-pull:
          image: <<parameters.image>>
      - docker-run:
          image: <<parameters.image>>
          command: /test/.circleci/build-wheel.sh
      - store_artifacts:
          path: wheelhouse.final

workflows:
  ci:
    jobs:
      - linux-arm64:
          # Changing this name should only be done in conjunction with updating
          # the required checks on GH
          name: linux-arm64-ci
          image: ghcr.io/pyca/cryptography-runner-ubuntu-focal:aarch64
          toxenv: py38
          # This makes sure it runs on all tags in addition to PRs/branches.
          # By default CircleCI ignores tags.
          filters:
            tags:
              only: /.*/
      - linux-arm64:
          name: linux-arm64-alpine-ci
          image: ghcr.io/pyca/cryptography-runner-alpine:aarch64
          toxenv: py310
          filters:
            tags:
              only: /.*/
      - linux-arm64-wheel:
          name: manylinux2014_aarch64-wheel
          image: ghcr.io/pyca/cryptography-manylinux2014_aarch64:latest
          python: cp36-cp36m
          platform: manylinux2014_aarch64
          filters:
            tags:
              only: /.*/
      - linux-arm64-wheel:
          name: manylinux_2_24_aarch64-wheel
          image: ghcr.io/pyca/cryptography-manylinux_2_24:aarch64
          python: cp36-cp36m
          platform: manylinux_2_24_aarch64
          filters:
            tags:
              only: /.*/
      - linux-arm64-wheel:
          name: manylinux_2_28_aarch64-wheel
          image: ghcr.io/pyca/cryptography-manylinux_2_28:aarch64
          python: cp36-cp36m
          platform: manylinux_2_28_aarch64
          filters:
            tags:
              only: /.*/
      - linux-arm64-wheel:
          name: musllinux_1_1_aarch64-wheel
          image: ghcr.io/pyca/cryptography-musllinux_1_1:aarch64
          python: cp36-cp36m
          platform: musllinux_1_1_aarch64
          filters:
            tags:
              only: /.*/
