<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_iterator_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_iterator_f Struct Reference</h1><!-- doxytag: class="cc_credentials_iterator_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_credentials_iterator_t. For more information see <a class="el" href="group__cc__credentials__iterator__reference.html">cc_credentials_iterator_t</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__iterator__f.html#16c385de50a458e4223af5680488c95c">release</a> )(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> io_credentials_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g79f914583e8076ac24c0d5dde4ddb712">cc_credentials_iterator_release()</a></b>: Release memory associated with a cc_credentials_iterator_t object.  <a href="#16c385de50a458e4223af5680488c95c"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__iterator__f.html#8ba419513434ba0b03a1be0c17da1478">next</a> )(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> in_credentials_iterator, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> *out_credentials)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next()</a></b>: Get the next credentials in the ccache.  <a href="#8ba419513434ba0b03a1be0c17da1478"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="group__cc__credentials__iterator__reference.html#g7d765e583b5994785e214df663e8959c">clone</a> )(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> in_credentials_iterator, <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> *out_credentials_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g59a9f96a6c00b64c0ab971f7e9b5aae2">cc_credentials_iterator_clone()</a></b>: Make a copy of a credentials iterator.  <a href="group__cc__credentials__iterator__reference.html#g7d765e583b5994785e214df663e8959c"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="16c385de50a458e4223af5680488c95c"></a><!-- doxytag: member="cc_credentials_iterator_f::release" ref="16c385de50a458e4223af5680488c95c" args=")(cc_credentials_iterator_t io_credentials_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__iterator__f.html#16c385de50a458e4223af5680488c95c">release</a>)(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> io_credentials_iterator)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g79f914583e8076ac24c0d5dde4ddb712">cc_credentials_iterator_release()</a></b>: Release memory associated with a cc_credentials_iterator_t object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_credentials_iterator</em>&nbsp;</td><td>the credentials iterator object to release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="8ba419513434ba0b03a1be0c17da1478"></a><!-- doxytag: member="cc_credentials_iterator_f::next" ref="8ba419513434ba0b03a1be0c17da1478" args=")(cc_credentials_iterator_t in_credentials_iterator, cc_credentials_t *out_credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__credentials__iterator__f.html#8ba419513434ba0b03a1be0c17da1478">next</a>)(<a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> in_credentials_iterator, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> *out_credentials)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next()</a></b>: Get the next credentials in the ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_iterator</em>&nbsp;</td><td>a credentials iterator object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_credentials</em>&nbsp;</td><td>on exit, the next credentials in the ccache. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a> if the next credential in the ccache was obtained or <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b748d5a55ed773e002ccc271beb4512c0a">ccIteratorEnd</a> if there are no more credentials. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
