objectclass ( 1.2.840.113533.7.67.14
    NAME 'entrustDNQualifierUser' SUP top AUXILIARY MAY dnQualifier )

objectclass ( *******.4.1.18227.2.1.2 NAME 'opencaEmailAddress' SUP top AUXILIARY
        MAY ( mail $ emailAddress )
        )

# attributetype ( ******** NAME 'pseudonym'
#	DESC 'RFC3039: pseudonum'
#	SUP name )

objectclass ( 1.2.840.113533.7.67.15
	NAME 'entrustNamedObject'
	SUP top AUXILIARY
	MAY ( dc $ cn $ sn $ c $ l $ st $ o $ ou $ title $ name $ givenName $
		initials $ generationQualifier $ dmdName )
	)

objectclass ( 1.2.840.113549.********
	NAME 'naturalPerson'
	DESC 'RFC 2985'
	SUP top AUXILIARY
	MAY ( emailAddress $ unstructuredName $ unstructuredAddress $
		dateOfBirth & placeOfBirth & gender & countryOfCitizenship &
		countryOfResidence & pseudonym & serialNumber )
	)
