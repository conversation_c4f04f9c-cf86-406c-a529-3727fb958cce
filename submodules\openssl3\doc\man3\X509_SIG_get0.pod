=pod

=head1 NAME

X509_SIG_get0, X509_SIG_getm - DigestInfo functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 void X509_SIG_get0(const X509_SIG *sig, const X509_ALGOR **palg,
                    const ASN1_OCTET_STRING **pdigest);
 void X509_SIG_getm(X509_SIG *sig, X509_ALGOR **palg,
                    ASN1_OCTET_STRING **pdigest);

=head1 DESCRIPTION

X509_SIG_get0() returns pointers to the algorithm identifier and digest
value in B<sig>. X509_SIG_getm() is identical to X509_SIG_get0()
except the pointers returned are not constant and can be modified:
for example to initialise them.

=head1 RETURN VALUES

X509_SIG_get0() and X509_SIG_getm() return no values.

=head1 SEE ALSO

L<d2i_X509(3)>

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
