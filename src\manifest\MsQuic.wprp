<?xml version="1.0" encoding="utf-8"?>
<WindowsPerformanceRecorder Version="1.0" Author="Microsoft Corporation" Copyright="Microsoft Corporation" Company="Microsoft Corporation">
  <Profiles>
   <SystemCollector Id="SC_HighVolume" Realtime="false">
      <BufferSize Value="1024"/>
      <Buffers Value="60"/>
    </SystemCollector>
    <EventCollector Id="EC_LowVolume" Name="LowVolume">
      <BufferSize Value="128"/>
      <Buffers Value="32"/>
    </EventCollector>
    <EventCollector Id="EC_HighVolume" Name="High Volume">
      <BufferSize Value="1024"/>
      <Buffers Value="64"/>
    </EventCollector>

    <SystemProvider Id="SP_SystemThreadExecution">
      <Keywords>
        <Keyword Value="CpuConfig"/>
        <Keyword Value="Loader"/>
        <Keyword Value="ProcessThread"/>
        <Keyword Value="SampledProfile"/>
        <Keyword Value="DPC"/>
        <Keyword Value="Interrupt"/>
      </Keywords>
      <Stacks>
        <Stack Value="SampledProfile"/>
      </Stacks>
    </SystemProvider>

    <SystemProvider Id="SP_SystemThreadExecutionDetail">
      <Keywords>
          <Keyword Value="ProcessThread"/>
          <Keyword Value="Loader"/>
          <Keyword Value="CSwitch"/>
          <Keyword Value="ReadyThread"/>
          <Keyword Value="SampledProfile"/>
          <Keyword Value="DPC"/>
          <Keyword Value="Interrupt"/>
          <Keyword Value="IdleStates"/>
      </Keywords>
      <Stacks>
          <Stack Value="CSwitch"/>
          <Stack Value="ReadyThread"/>
          <Stack Value="SampledProfile"/>
      </Stacks>
    </SystemProvider>

    <EventProvider Id="EP_MsQuicEtw" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true" />
    <EventProvider Id="EP_MsQuicEtw_LowVolume" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true" Level="4">
      <Keywords>
        <Keyword Value="0x80000000"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_LowVolume_Scheduling" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0xA0000000"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_LowVolume_DataPath" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0xC0000000"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_LowVolume_Api" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0x80000800"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_LowVolume_Warnings" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true" Level="3">
      <Keywords>
        <Keyword Value="0x80000000"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_NoLogs" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0xE0000800"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_MsQuicEtw_RPS" Name="ff15e657-4f26-570e-88ab-0796b258d11c" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0x00002000"/>
      </Keywords>
    </EventProvider>

    <EventProvider Id="EP_TcpIpEtw" Name="Microsoft-Windows-TCPIP" NonPagedMemory="true" />
    <EventProvider Id="EP_TcpIpEtw_Drops" Name="Microsoft-Windows-TCPIP" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0x10000000000"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_WinSockEtw" Name="Microsoft-Windows-Winsock-AFD" NonPagedMemory="true" />
    <EventProvider Id="EP_SChannelWpp" Name="37D2C3CD-C5D4-4587-8531-4696C44244C8" Level="5">
      <Keywords>
        <Keyword Value="0x7FFFFF"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_NCryptWpp" Name="A74EFE00-14BE-4ef9-9DA9-1484D5473301" Level="5">
      <Keywords>
        <Keyword Value="0x7FFFFF"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_BCryptWpp" Name="A74EFE00-14BE-4ef9-9DA9-1484D5473302" Level="5">
      <Keywords>
        <Keyword Value="0xFFFFFFFF"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_TaefEtw" Name="40c4df8b-00a9-5159-62bc-9bbc5ee78a29" Level="4">
      <Keywords>
        <Keyword Value="0xFFFF"/>
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_XdpWpp" Name="D6143B5C-9FD6-44BA-BA02-FAD9EA0C263D" Level="5" NonPagedMemory="true">
      <Keywords>
        <Keyword Value="0x7FFFFFFFFFFFFFFF" />
      </Keywords>
    </EventProvider>
    <EventProvider Id="EP_XdpEtwPerFrame" Name="Microsoft-XDP" Level="25" />

    <Profile Id="Stacks.Light.File" Name="Stacks" Description="CPU Stacks" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecution" />
        </SystemCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Stacks.Light.Memory" Base="Stacks.Light.File" Name="Stacks" Description="CPU Stacks" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="Stacks.Verbose.File" Name="Stacks" Description="Detailed CPU Stacks" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecutionDetail" />
        </SystemCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Stacks.Verbose.Memory" Base="Stacks.Light.File" Name="Stacks" Description="CPU Stacks" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="RPS.Light.File" Name="RPS" Description="RPS related MsQuic ETW Events" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_RPS" />
            <EventProviderId Value="EP_XdpEtwPerFrame" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="RPS.Light.Memory" Base="RPS.Light.File" Name="RPS" Description="RPS related MsQuic ETW Events" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="RPS.Verbose.File" Name="RPS" Description="RPS related MsQuic ETW Events" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecutionDetail" />
        </SystemCollectorId>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_RPS" />
            <EventProviderId Value="EP_XdpEtwPerFrame" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="RPS.Verbose.Memory" Base="RPS.Verbose.File" Name="RPS" Description="RPS related MsQuic ETW Events" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="Performance.Light.File" Name="Performance" Description="Perf related MsQuic ETW Events" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_NoLogs" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Performance.Light.Memory" Base="Performance.Light.File" Name="Performance" Description="Perf related MsQuic ETW Events" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="Performance.Verbose.File" Name="Performance" Description="Perf related MsQuic ETW and System Events" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecution" />
        </SystemCollectorId>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_NoLogs" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Performance.Verbose.Memory" Base="Performance.Verbose.File" Name="Performance" Description="Perf related MsQuic ETW and System" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="Basic.Light.File" Name="Basic" Description="Low Volume MsQuic ETW Events" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_LowVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume" />
            <EventProviderId Value="EP_TcpIpEtw_Drops" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Basic.Light.Memory" Base="Basic.Light.File" Name="Basic" Description="Low Volume MsQuic ETW Events" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="Basic.Verbose.File" Name="Basic" Description="All MsQuic ETW Events" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecution" />
        </SystemCollectorId>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw" />
            <EventProviderId Value="EP_TcpIpEtw_Drops" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Basic.Verbose.Memory" Base="Basic.Verbose.File" Name="Basic" Description="All MsQuic ETW Events" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="Scheduling.Verbose.File" Name="Scheduling" Description="Used for debugging scheduling" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume_Scheduling" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Scheduling.Verbose.Memory" Base="Scheduling.Verbose.File" Name="Scheduling" Description="Used for debugging scheduling" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="Datapath.Light.File" Name="Datapath" Description="Used for debugging datapath" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume_DataPath" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Datapath.Light.Memory" Base="Datapath.Light.File" Name="Datapath" Description="Used for debugging datapath" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="Datapath.Verbose.File" Name="Datapath" Description="Used for debugging datapath" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <SystemCollectorId Value="SC_HighVolume">
          <SystemProviderId Value="SP_SystemThreadExecution" />
        </SystemCollectorId>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume_DataPath" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Datapath.Verbose.Memory" Base="Datapath.Verbose.File" Name="Datapath" Description="Used for debugging datapath" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="Full.Light.File" Name="Full" Description="All MsQuic and TAEF Events" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_LowVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw" />
            <EventProviderId Value="EP_TcpIpEtw_Drops" />
            <EventProviderId Value="EP_TaefEtw" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Full.Light.Memory" Base="Full.Light.File" Name="Full" Description="All MsQuic and TAEF Events" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="Full.Verbose.File" Name="Full" Description="Everything" LoggingMode="File" DetailLevel="Verbose">
      <Collectors>
        <EventCollectorId Value="EC_LowVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw" />
            <EventProviderId Value="EP_TaefEtw" />
          </EventProviders>
        </EventCollectorId>
        <EventCollectorId Value="EC_HighVolume">
          <EventProviders>
            <EventProviderId Value="EP_TcpIpEtw" />
            <EventProviderId Value="EP_WinSockEtw" />
            <EventProviderId Value="EP_SChannelWpp" />
            <EventProviderId Value="EP_NCryptWpp" />
            <EventProviderId Value="EP_XdpEtwPerFrame" />
            <EventProviderId Value="EP_XdpWpp" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="Full.Verbose.Memory" Base="Full.Verbose.File" Name="Full" Description="Everything" LoggingMode="Memory" DetailLevel="Verbose"/>

    <Profile Id="SpinQuic.Light.File" Name="SpinQuic" Description="Used for SpinQuic Testing" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_LowVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume_Api" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="SpinQuic.Light.Memory" Base="SpinQuic.Light.File" Name="SpinQuic" Description="Used for SpinQuic Testing" LoggingMode="Memory" DetailLevel="Light"/>

    <Profile Id="SpinQuicWarnings.Light.File" Name="SpinQuicWarnings" Description="Used for SpinQuic Testing" LoggingMode="File" DetailLevel="Light">
      <Collectors>
        <EventCollectorId Value="EC_LowVolume">
          <EventProviders>
            <EventProviderId Value="EP_MsQuicEtw_LowVolume_Warnings" />
          </EventProviders>
        </EventCollectorId>
      </Collectors>
    </Profile>
    <Profile Id="SpinQuicWarnings.Light.Memory" Base="SpinQuicWarnings.Light.File" Name="SpinQuicWarnings" Description="Used for SpinQuic Testing" LoggingMode="Memory" DetailLevel="Light"/>

  </Profiles>
</WindowsPerformanceRecorder>
