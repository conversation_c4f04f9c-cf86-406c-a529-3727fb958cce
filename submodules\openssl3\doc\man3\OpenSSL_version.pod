=pod

=head1 NAME

OPENSSL_VERSION_MAJOR, OPENSSL_VERSION_MINOR, OPENSSL_VERSION_PATCH,
OPENSSL_VERSION_PRE_RELEASE, OPENSSL_VERSION_BUILD_METADATA,
OPENSSL_VERSION_TEXT, OPENSSL_VERSION_PREREQ, OPENSSL_version_major,
OPENSSL_version_minor, OPENSSL_version_patch, OPENSSL_version_pre_release,
OPENSSL_version_build_metadata, OpenSSL_version, OPENSSL_VERSION_NUMBER,
OpenSSL_version_num, OPENSSL_info
- get OpenSSL version number and other information

=head1 SYNOPSIS

 #include <openssl/opensslv.h>

 #define OPENSSL_VERSION_MAJOR  x
 #define OPENSSL_VERSION_MINOR  y
 #define OPENSSL_VERSION_PATCH  z

 /* The definitions here are typical release values */
 #define OPENSSL_VERSION_PRE_RELEASE ""
 #define OPENSSL_VERSION_BUILD_METADATA ""

 #define OPENSSL_VERSION_TEXT "OpenSSL x.y.z xx XXX xxxx"

 #define OPENSSL_VERSION_PREREQ(maj,min)

 #include <openssl/crypto.h>

 unsigned int OPENSSL_version_major(void);
 unsigned int OPENSSL_version_minor(void);
 unsigned int OPENSSL_version_patch(void);
 const char *OPENSSL_version_pre_release(void);
 const char *OPENSSL_version_build_metadata(void);

 const char *OpenSSL_version(int t);

 const char *OPENSSL_info(int t);

 /* from openssl/opensslv.h */
 #define OPENSSL_VERSION_NUMBER 0xnnnnnnnnL

 /* from openssl/crypto.h */
 unsigned long OpenSSL_version_num();

=head1 DESCRIPTION

=head2 Macros

The three macros B<OPENSSL_VERSION_MAJOR>, B<OPENSSL_VERSION_MINOR> and
B<OPENSSL_VERSION_PATCH> represent the three parts of a version
identifier, B<I<MAJOR>.I<MINOR>.I<PATCH>>.

The macro B<OPENSSL_VERSION_PRE_RELEASE> is an added bit of text that
indicates that this is a pre-release version, such as C<"-dev"> for an
ongoing development snapshot or C<"-alpha3"> for an alpha release.
The value must be a string.

The macro B<OPENSSL_VERSION_BUILD_METADATA> is extra information, reserved
for other parties, such as C<"+fips">, or C<"+vendor.1">).
The OpenSSL project will not touch this macro (will leave it an empty string).
The value must be a string.

B<OPENSSL_VERSION_STR> is a convenience macro to get the short version
identifier string, C<"I<MAJOR>.I<MINOR>.I<PATCH>">.

B<OPENSSL_FULL_VERSION_STR> is a convenience macro to get the longer
version identifier string, which combines B<OPENSSL_VERSION_STR>,
B<OPENSSL_VERSION_PRE_RELEASE> and B<OPENSSL_VERSION_BUILD_METADATA>.

B<OPENSSL_VERSION_TEXT> is a convenience macro to get a full descriptive
version text, which includes B<OPENSSL_FULL_VERSION_STR> and the release
date.

B<OPENSSL_VERSION_PREREQ> is a useful macro for checking whether the OpenSSL
version for the headers in use is at least at the given pre-requisite major
(B<maj>) and minor (B<min>) number or not. It will evaluate to true if the
header version number (B<OPENSSL_VERSION_MAJOR>.B<OPENSSL_VERSION_MINOR>) is
greater than or equal to B<maj>.B<min>.

B<OPENSSL_VERSION_NUMBER> is a combination of the major, minor and
patch version into a single integer 0xMNN00PP0L, where:

=over 4

=item M

is the number from B<OPENSSL_VERSION_MAJOR>, in hexadecimal notation

=item NN

is the number from B<OPENSSL_VERSION_MINOR>, in hexadecimal notation

=item PP

is the number from B<OPENSSL_VERSION_PATCH>, in hexadecimal notation

=back

=head2 Functions

OPENSSL_version_major(), OPENSSL_version_minor(), OPENSSL_version_patch(),
OPENSSL_version_pre_release(), and OPENSSL_version_build_metadata() return
the values of the macros above for the build of the library, respectively.

OpenSSL_version() returns different strings depending on I<t>:

=over 4

=item OPENSSL_VERSION

The value of B<OPENSSL_VERSION_TEXT>

=item OPENSSL_VERSION_STRING

The value of B<OPENSSL_VERSION_STR>

=item OPENSSL_FULL_VERSION_STRING

The value of B<OPENSSL_FULL_VERSION_STR>

=item OPENSSL_CFLAGS

The compiler flags set for the compilation process in the form
C<compiler: ...>  if available, or C<compiler: information not available>
otherwise.

=item OPENSSL_BUILT_ON

The date of the build process in the form C<built on: ...> if available
or C<built on: date not available> otherwise.
The date would not be available in a reproducible build, for example.

=item OPENSSL_PLATFORM

The "Configure" target of the library build in the form C<platform: ...>
if available, or C<platform: information not available> otherwise.

=item OPENSSL_DIR

The B<OPENSSLDIR> setting of the library build in the form C<OPENSSLDIR: "...">
if available, or C<OPENSSLDIR: N/A> otherwise.

=item OPENSSL_ENGINES_DIR

The B<ENGINESDIR> setting of the library build in the form C<ENGINESDIR: "...">
if available, or C<ENGINESDIR: N/A> otherwise. This option is deprecated in
OpenSSL 3.0.

=item OPENSSL_MODULES_DIR

The B<MODULESDIR> setting of the library build in the form C<MODULESDIR: "...">
if available, or C<MODULESDIR: N/A> otherwise.

=item OPENSSL_CPU_INFO

The current OpenSSL cpu settings.
This is the current setting of the cpu capability flags. It is usually
automatically configured but may be set via an environment variable.
The value has the same syntax as the environment variable.
For x86 the string looks like C<CPUINFO: OPENSSL_ia32cap=0x123:0x456>
or C<CPUINFO: N/A> if not available.

=back

For an unknown I<t>, the text C<not available> is returned.

OPENSSL_info() also returns different strings depending on I<t>:

=over 4

=item OPENSSL_INFO_CONFIG_DIR

The configured C<OPENSSLDIR>, which is the default location for
OpenSSL configuration files.

=item OPENSSL_INFO_ENGINES_DIR

The configured C<ENGINESDIR>, which is the default location for
OpenSSL engines.

=item OPENSSL_INFO_MODULES_DIR

The configured C<MODULESDIR>, which is the default location for
dynamically loadable OpenSSL modules other than engines.

=item OPENSSL_INFO_DSO_EXTENSION

The configured dynamically loadable module extension.

=item OPENSSL_INFO_DIR_FILENAME_SEPARATOR

The separator between a directory specification and a filename.
Note that on some operating systems, this is not the same as the
separator between directory elements.

=item OPENSSL_INFO_LIST_SEPARATOR

The OpenSSL list separator.
This is typically used in strings that are lists of items, such as the
value of the environment variable C<$PATH> on Unix (where the
separator is C<:>) or C<%PATH%> on Windows (where the separator is
C<;>).

=item OPENSSL_INFO_CPU_SETTINGS

The current OpenSSL cpu settings.
This is the current setting of the cpu capability flags. It is usually
automatically configured but may be set via an environment variable.
The value has the same syntax as the environment variable.
For x86 the string looks like C<OPENSSL_ia32cap=0x123:0x456>.

=item OPENSSL_INFO_QUIC

This is only defined when compiling with a QUIC-enabled version of
OpenSSL. At run time, this will return "QUIC" if QUIC is supported.

This can be used as a build time flag to determine if OpenSSL has
QUIC enabled.

=back

For an unknown I<t>, NULL is returned.

OpenSSL_version_num() returns the value of B<OPENSSL_VERSION_NUMBER>.

=head1 RETURN VALUES

OPENSSL_version_major(), OPENSSL_version_minor() and OPENSSL_version_patch()
return the version number parts as integers.

OPENSSL_version_pre_release() and OPENSSL_version_build_metadata() return
the values of B<OPENSSL_VERSION_PRE_RELEASE> and
B<OPENSSL_VERSION_BUILD_METADATA> respectively as constant strings.
For any of them that is undefined, the empty string is returned.

OpenSSL_version() returns constant strings.

=head1 SEE ALSO

L<crypto(7)>

=head1 HISTORY

The macros and functions described here were added in OpenSSL 3.0,
except for OPENSSL_VERSION_NUMBER and OpenSSL_version_num().

=head1 COPYRIGHT

Copyright 2018-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
