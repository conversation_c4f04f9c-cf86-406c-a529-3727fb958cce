=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-pkeyutl - public key algorithm command

=head1 SYNOPSIS

B<openssl> B<pkeyutl>
[B<-help>]
[B<-in> I<file>]
[B<-rawin>]
[B<-digest> I<algorithm>]
[B<-out> I<file>]
[B<-sigfile> I<file>]
[B<-inkey> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-passin> I<arg>]
[B<-peerkey> I<file>]
[B<-peerform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-pubin>]
[B<-certin>]
[B<-rev>]
[B<-sign>]
[B<-verify>]
[B<-verifyrecover>]
[B<-encrypt>]
[B<-decrypt>]
[B<-derive>]
[B<-kdf> I<algorithm>]
[B<-kdflen> I<length>]
[B<-pkeyopt> I<opt>:I<value>]
[B<-pkeyopt_passin> I<opt>[:I<passarg>]]
[B<-hexdump>]
[B<-asn1parse>]
{- $OpenSSL::safe::opt_engine_synopsis -}[B<-engine_impl>]
{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
{- $OpenSSL::safe::opt_config_synopsis -}

=head1 DESCRIPTION

This command can be used to perform low-level public key
operations using any supported algorithm.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-in> I<filename>

This specifies the input filename to read data from or standard input
if this option is not specified.

=item B<-rawin>

This indicates that the input data is raw data, which is not hashed by any
message digest algorithm. The user can specify a digest algorithm by using
the B<-digest> option. This option can only be used with B<-sign> and
B<-verify> and must be used with the Ed25519 and Ed448 algorithms.

=item B<-digest> I<algorithm>

This specifies the digest algorithm which is used to hash the input data before
signing or verifying it with the input key. This option could be omitted if the
signature algorithm does not require one (for instance, EdDSA). If this option
is omitted but the signature algorithm requires one, a default value will be
used. For signature algorithms like RSA, DSA and ECDSA, SHA-256 will be the
default digest algorithm. For SM2, it will be SM3. If this option is present,
then the B<-rawin> option must be also specified.

=item B<-out> I<filename>

Specifies the output filename to write to or standard output by
default.

=item B<-sigfile> I<file>

Signature file, required for B<-verify> operations only

=item B<-inkey> I<filename>|I<uri>

The input key, by default it should be a private key.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-passin> I<arg>

The input key password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-peerkey> I<file>

The peer key file, used by key derivation (agreement) operations.

=item B<-peerform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The peer key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-pubin>

The input file is a public key.

=item B<-certin>

The input is a certificate containing a public key.

=item B<-rev>

Reverse the order of the input buffer. This is useful for some libraries
(such as CryptoAPI) which represent the buffer in little endian format.

=item B<-sign>

Sign the input data (which must be a hash) and output the signed result. This
requires a private key.

=item B<-verify>

Verify the input data (which must be a hash) against the signature file and
indicate if the verification succeeded or failed.

=item B<-verifyrecover>

Verify the input data (which must be a hash) and output the recovered data.

=item B<-encrypt>

Encrypt the input data using a public key.

=item B<-decrypt>

Decrypt the input data using a private key.

=item B<-derive>

Derive a shared secret using the peer key.

=item B<-kdf> I<algorithm>

Use key derivation function I<algorithm>.  The supported algorithms are
at present B<TLS1-PRF> and B<HKDF>.
Note: additional parameters and the KDF output length will normally have to be
set for this to work.
See L<EVP_PKEY_CTX_set_hkdf_md(3)> and L<EVP_PKEY_CTX_set_tls1_prf_md(3)>
for the supported string parameters of each algorithm.

=item B<-kdflen> I<length>

Set the output length for KDF.

=item B<-pkeyopt> I<opt>:I<value>

Public key options specified as opt:value. See NOTES below for more details.

=item B<-pkeyopt_passin> I<opt>[:I<passarg>]

Allows reading a public key option I<opt> from stdin or a password source.
If only I<opt> is specified, the user will be prompted to enter a password on
stdin.  Alternatively, I<passarg> can be specified which can be any value
supported by L<openssl-passphrase-options(1)>.

=item B<-hexdump>

hex dump the output data.

=item B<-asn1parse>

Parse the ASN.1 output data, this is useful when combined with the
B<-verifyrecover> option when an ASN1 structure is signed.

{- $OpenSSL::safe::opt_engine_item -}

{- output_off() if $disabled{"deprecated-3.0"}; "" -}
=item B<-engine_impl>

When used with the B<-engine> option, it specifies to also use
engine I<id> for crypto operations.
{- output_on() if $disabled{"deprecated-3.0"}; "" -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_config_item -}

=back

=head1 NOTES

The operations and options supported vary according to the key algorithm
and its implementation. The OpenSSL operations and options are indicated below.

Unless otherwise mentioned all algorithms support the B<digest:>I<alg> option
which specifies the digest in use for sign, verify and verifyrecover operations.
The value I<alg> should represent a digest name as used in the
EVP_get_digestbyname() function for example B<sha1>. This value is not used to
hash the input data. It is used (by some algorithms) for sanity-checking the
lengths of data passed in and for creating the structures that make up the
signature (e.g. B<DigestInfo> in RSASSA PKCS#1 v1.5 signatures).

This command does not hash the input data (except where -rawin is used) but
rather it will use the data directly as input to the signature algorithm.
Depending on the key type, signature type, and mode of padding, the maximum
acceptable lengths of input data differ. The signed data can't be longer than
the key modulus with RSA. In case of ECDSA and DSA the data shouldn't be longer
than the field size, otherwise it will be silently truncated to the field size.
In any event the input size must not be larger than the largest supported digest
size.

In other words, if the value of digest is B<sha1> the input should be the 20
bytes long binary encoding of the SHA-1 hash function output.

=head1 RSA ALGORITHM

The RSA algorithm generally supports the encrypt, decrypt, sign,
verify and verifyrecover operations. However, some padding modes
support only a subset of these operations. The following additional
B<pkeyopt> values are supported:

=over 4

=item B<rsa_padding_mode:>I<mode>

This sets the RSA padding mode. Acceptable values for I<mode> are B<pkcs1> for
PKCS#1 padding, B<none> for no padding, B<oaep>
for B<OAEP> mode, B<x931> for X9.31 mode and B<pss> for PSS.

In PKCS#1 padding if the message digest is not set then the supplied data is
signed or verified directly instead of using a B<DigestInfo> structure. If a
digest is set then the a B<DigestInfo> structure is used and its the length
must correspond to the digest type.

For B<oaep> mode only encryption and decryption is supported.

For B<x931> if the digest type is set it is used to format the block data
otherwise the first byte is used to specify the X9.31 digest ID. Sign,
verify and verifyrecover are can be performed in this mode.

For B<pss> mode only sign and verify are supported and the digest type must be
specified.

=item B<rsa_pss_saltlen:>I<len>

For B<pss> mode only this option specifies the salt length. Three special
values are supported: B<digest> sets the salt length to the digest length,
B<max> sets the salt length to the maximum permissible value. When verifying
B<auto> causes the salt length to be automatically determined based on the
B<PSS> block structure.

=item B<rsa_mgf1_md:>I<digest>

For PSS and OAEP padding sets the MGF1 digest. If the MGF1 digest is not
explicitly set in PSS mode then the signing digest is used.

=item B<rsa_oaep_md:>I<digest>

Sets the digest used for the OAEP hash function. If not explicitly set then
SHA1 is used.

=back

=head1 RSA-PSS ALGORITHM

The RSA-PSS algorithm is a restricted version of the RSA algorithm which only
supports the sign and verify operations with PSS padding. The following
additional B<-pkeyopt> values are supported:

=over 4

=item B<rsa_padding_mode:>I<mode>, B<rsa_pss_saltlen:>I<len>,
B<rsa_mgf1_md:>I<digest>

These have the same meaning as the B<RSA> algorithm with some additional
restrictions. The padding mode can only be set to B<pss> which is the
default value.

If the key has parameter restrictions than the digest, MGF1
digest and salt length are set to the values specified in the parameters.
The digest and MG cannot be changed and the salt length cannot be set to a
value less than the minimum restriction.

=back

=head1 DSA ALGORITHM

The DSA algorithm supports signing and verification operations only. Currently
there are no additional B<-pkeyopt> options other than B<digest>. The SHA1
digest is assumed by default.

=head1 DH ALGORITHM

The DH algorithm only supports the derivation operation and no additional
B<-pkeyopt> options.

=head1 EC ALGORITHM

The EC algorithm supports sign, verify and derive operations. The sign and
verify operations use ECDSA and derive uses ECDH. SHA1 is assumed by default for
the B<-pkeyopt> B<digest> option.

=head1 X25519 AND X448 ALGORITHMS

The X25519 and X448 algorithms support key derivation only. Currently there are
no additional options.

=head1 ED25519 AND ED448 ALGORITHMS

These algorithms only support signing and verifying. OpenSSL only implements the
"pure" variants of these algorithms so raw data can be passed directly to them
without hashing them first. The option B<-rawin> must be used with these
algorithms with no B<-digest> specified. Additionally OpenSSL only supports
"oneshot" operation with these algorithms. This means that the entire file to
be signed/verified must be read into memory before processing it. Signing or
Verifying very large files should be avoided. Additionally the size of the file
must be known for this to work. If the size of the file cannot be determined
(for example if the input is stdin) then the sign or verify operation will fail.

=head1 SM2

The SM2 algorithm supports sign, verify, encrypt and decrypt operations. For
the sign and verify operations, SM2 requires an Distinguishing ID string to
be passed in. The following B<-pkeyopt> value is supported:

=over 4

=item B<distid:>I<string>

This sets the ID string used in SM2 sign or verify operations. While verifying
an SM2 signature, the ID string must be the same one used when signing the data.
Otherwise the verification will fail.

=item B<hexdistid:>I<hex_string>

This sets the ID string used in SM2 sign or verify operations. While verifying
an SM2 signature, the ID string must be the same one used when signing the data.
Otherwise the verification will fail. The ID string provided with this option
should be a valid hexadecimal value.

=back

=head1 EXAMPLES

Sign some data using a private key:

 openssl pkeyutl -sign -in file -inkey key.pem -out sig

Recover the signed data (e.g. if an RSA key is used):

 openssl pkeyutl -verifyrecover -in sig -inkey key.pem

Verify the signature (e.g. a DSA key):

 openssl pkeyutl -verify -in file -sigfile sig -inkey key.pem

Sign data using a message digest value (this is currently only valid for RSA):

 openssl pkeyutl -sign -in file -inkey key.pem -out sig -pkeyopt digest:sha256

Derive a shared secret value:

 openssl pkeyutl -derive -inkey key.pem -peerkey pubkey.pem -out secret

Hexdump 48 bytes of TLS1 PRF using digest B<SHA256> and shared secret and
seed consisting of the single byte 0xFF:

 openssl pkeyutl -kdf TLS1-PRF -kdflen 48 -pkeyopt md:SHA256 \
    -pkeyopt hexsecret:ff -pkeyopt hexseed:ff -hexdump

Derive a key using B<scrypt> where the password is read from command line:

 openssl pkeyutl -kdf scrypt -kdflen 16 -pkeyopt_passin pass \
    -pkeyopt hexsalt:aabbcc -pkeyopt N:16384 -pkeyopt r:8 -pkeyopt p:1

Derive using the same algorithm, but read key from environment variable MYPASS:

 openssl pkeyutl -kdf scrypt -kdflen 16 -pkeyopt_passin pass:env:MYPASS \
    -pkeyopt hexsalt:aabbcc -pkeyopt N:16384 -pkeyopt r:8 -pkeyopt p:1

Sign some data using an L<SM2(7)> private key and a specific ID:

 openssl pkeyutl -sign -in file -inkey sm2.key -out sig -rawin -digest sm3 \
    -pkeyopt distid:someid

Verify some data using an L<SM2(7)> certificate and a specific ID:

 openssl pkeyutl -verify -certin -in file -inkey sm2.cert -sigfile sig \
    -rawin -digest sm3 -pkeyopt distid:someid

Decrypt some data using a private key with OAEP padding using SHA256:

 openssl pkeyutl -decrypt -in file -inkey key.pem -out secret \
    -pkeyopt rsa_padding_mode:oaep -pkeyopt rsa_oaep_md:sha256

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-genpkey(1)>,
L<openssl-pkey(1)>,
L<openssl-rsautl(1)>
L<openssl-dgst(1)>,
L<openssl-rsa(1)>,
L<openssl-genrsa(1)>,
L<openssl-kdf(1)>
L<EVP_PKEY_CTX_set_hkdf_md(3)>,
L<EVP_PKEY_CTX_set_tls1_prf_md(3)>,

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
