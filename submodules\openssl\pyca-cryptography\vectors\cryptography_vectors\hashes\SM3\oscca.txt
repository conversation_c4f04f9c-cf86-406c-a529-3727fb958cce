# Vectors from https://raw.githubusercontent.com/torvalds/linux/master/crypto/testmgr.h,
# originally from http://www.oscca.gov.cn/UpFile/20101222141857786.pdf and
# https://github.com/adamws/oscca-sm3
# Reformatted to work with the NIST loader
# SM3

Len = 0
Msg = 00
MD = 1ab21d8355cfa17f8e61194831e81a8f22bec8c728fefb747ed035eb5082aa2b

Len = 8
Msg = 61
MD = 623476ac18f65a2909e43c7fec61b49c7e764a91a18ccb82f1917a29c86c5e88

# A.1. Example 1
Len = 24
Msg = 616263
MD = 66c7f0f462eeedd9d1f2d46bdc10e4e24167c4875cf2f7a2297da02b8f4ba8e0

# A.1. Example 2
Len = 208
Msg = 6162636465666768696a6b6c6d6e6f707172737475767778797a
MD = b80fe97a4da24afc277564f66a359ef440462ad28dcc6d63adb24d5c20a61595

Len = 512
Msg = 61626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364
MD = debe9ff92275b8a138604889c18e5a4d6fdb70e5387e5765293dcba39c0c5732

Len = 2048
Msg = 61626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364616263646162636461626364
MD = b965764c8bebb091c7602b74afd34eefb531dccb4e0076d9b7cd813199b45971
