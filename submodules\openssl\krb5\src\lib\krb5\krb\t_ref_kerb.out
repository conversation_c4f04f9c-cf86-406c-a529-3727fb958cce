parsed (and unparsed) principal(tytso): '<EMAIL>'
parsed (and unparsed) principal(tytso@SHAZAAM): MATCH
parsed (and unparsed) principal(tytso/<EMAIL>): MATCH
parsed (and unparsed) principal(tytso/tuber/<EMAIL>): MATCH
parsed (and unparsed) principal(tytso/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q/r/s/t): 'tytso/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q/r/s/<EMAIL>'
parsed (and unparsed) principal(tytso/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q/r/s/t@FOO): MATCH
parsed (and unparsed) principal(tytso\\0/\0@B\n\t\\GAG): MATCH
parsed (and unparsed) principal(tytso/\n/\b\t@B\0hacky-test): MATCH
parsed (and unparsed) principal(\/slash/\@atsign/octa\/thorpe@\/slash\@at\/sign): MATCH
425_converted principal(rcmd, e40-po, ATHENA.MIT.EDU): 'host/<EMAIL>'
425_converted principal(rcmd, mit, ATHENA.MIT.EDU): 'host/<EMAIL>'
425_converted principal(rcmd, lithium, ATHENA.MIT.EDU): 'host/<EMAIL>'
425_converted principal(rcmd, tweedledumb, CYGNUS.COM): 'host/<EMAIL>'
425_converted principal(rcmd, uunet, UU.NET): 'host/<EMAIL>'
425_converted principal(zephyr, zephyr, ATHENA.MIT.EDU): 'zephyr/<EMAIL>'
425_converted principal(kadmin, ATHENA.MIT.EDU, ATHENA.MIT.EDU): 'kadmin/<EMAIL>'
524_converted_principal(host/<EMAIL>): 'rcmd' 'e40-po' 'ATHENA.MIT.EDU'
524_converted_principal(host/<EMAIL>): 'rcmd' 'foobar' 'IR.STANFORD.EDU'
old principal: <EMAIL>, modified principal: <EMAIL>
