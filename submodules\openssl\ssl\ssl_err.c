/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include <openssl/err.h>
#include <openssl/sslerr.h>

#ifndef OPENSSL_NO_ERR

static const ERR_STRING_DATA SSL_str_functs[] = {
    {ERR_PACK(ERR_LIB_SSL, SSL_F_ADD_CLIENT_KEY_SHARE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_ADD_KEY_SHARE, 0), "add_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_BYTES_TO_CIPHER_LIST, 0),
     "bytes_to_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CHECK_SUITEB_CIPHER_LIST, 0),
     "check_suiteb_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CIPHERSUITE_CB, 0), "ciphersuite_cb"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CONSTRUCT_CA_NAMES, 0), "construct_ca_names"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CONSTRUCT_KEY_EXCHANGE_TBS, 0),
     "construct_key_exchange_tbs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CONSTRUCT_STATEFUL_TICKET, 0),
     "construct_stateful_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CONSTRUCT_STATELESS_TICKET, 0),
     "construct_stateless_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CREATE_SYNTHETIC_MESSAGE_HASH, 0),
     "create_synthetic_message_hash"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CREATE_TICKET_PREQUEL, 0),
     "create_ticket_prequel"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CT_MOVE_SCTS, 0), "ct_move_scts"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CT_STRICT, 0), "ct_strict"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CUSTOM_EXT_ADD, 0), "custom_ext_add"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_CUSTOM_EXT_PARSE, 0), "custom_ext_parse"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_D2I_SSL_SESSION, 0), "d2i_SSL_SESSION"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DANE_CTX_ENABLE, 0), "dane_ctx_enable"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DANE_MTYPE_SET, 0), "dane_mtype_set"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DANE_TLSA_ADD, 0), "dane_tlsa_add"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DERIVE_SECRET_KEY_AND_IV, 0),
     "derive_secret_key_and_iv"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DO_DTLS1_WRITE, 0), "do_dtls1_write"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DO_SSL3_WRITE, 0), "do_ssl3_write"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_BUFFER_RECORD, 0),
     "dtls1_buffer_record"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_CHECK_TIMEOUT_NUM, 0),
     "dtls1_check_timeout_num"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_HEARTBEAT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_HM_FRAGMENT_NEW, 0),
     "dtls1_hm_fragment_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_PREPROCESS_FRAGMENT, 0),
     "dtls1_preprocess_fragment"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_PROCESS_BUFFERED_RECORDS, 0),
     "dtls1_process_buffered_records"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_PROCESS_RECORD, 0),
     "dtls1_process_record"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_READ_BYTES, 0), "dtls1_read_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_READ_FAILED, 0), "dtls1_read_failed"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_RETRANSMIT_MESSAGE, 0),
     "dtls1_retransmit_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_WRITE_APP_DATA_BYTES, 0),
     "dtls1_write_app_data_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS1_WRITE_BYTES, 0), "dtls1_write_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLSV1_LISTEN, 0), "DTLSv1_listen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_CONSTRUCT_CHANGE_CIPHER_SPEC, 0),
     "dtls_construct_change_cipher_spec"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_CONSTRUCT_HELLO_VERIFY_REQUEST, 0),
     "dtls_construct_hello_verify_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_GET_REASSEMBLED_MESSAGE, 0),
     "dtls_get_reassembled_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_PROCESS_HELLO_VERIFY, 0),
     "dtls_process_hello_verify"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_RECORD_LAYER_NEW, 0),
     "DTLS_RECORD_LAYER_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_DTLS_WAIT_FOR_DRY, 0), "dtls_wait_for_dry"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_EARLY_DATA_COUNT_OK, 0),
     "early_data_count_ok"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_EARLY_DATA, 0), "final_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_EC_PT_FORMATS, 0),
     "final_ec_pt_formats"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_EMS, 0), "final_ems"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_KEY_SHARE, 0), "final_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_MAXFRAGMENTLEN, 0),
     "final_maxfragmentlen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_PSK, 0), "final_psk"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_QUIC_TRANSPORT_PARAMS, 0),
     "final_quic_transport_params"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_RENEGOTIATE, 0), "final_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_SERVER_NAME, 0), "final_server_name"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_FINAL_SIG_ALGS, 0), "final_sig_algs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_GET_CERT_VERIFY_TBS_DATA, 0),
     "get_cert_verify_tbs_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_NSS_KEYLOG_INT, 0), "nss_keylog_int"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OPENSSL_INIT_SSL, 0), "OPENSSL_init_ssl"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT13_READ_TRANSITION, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT13_WRITE_TRANSITION, 0),
     "ossl_statem_client13_write_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT_CONSTRUCT_MESSAGE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT_POST_PROCESS_MESSAGE, 0),
     "ossl_statem_client_post_process_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT_PROCESS_MESSAGE, 0),
     "ossl_statem_client_process_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT_READ_TRANSITION, 0),
     "ossl_statem_client_read_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_CLIENT_WRITE_TRANSITION, 0),
     "ossl_statem_client_write_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER13_READ_TRANSITION, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER13_WRITE_TRANSITION, 0),
     "ossl_statem_server13_write_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_CONSTRUCT_MESSAGE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_POST_PROCESS_MESSAGE, 0),
     "ossl_statem_server_post_process_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_POST_WORK, 0),
     "ossl_statem_server_post_work"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_PRE_WORK, 0),
     "ossl_statem_server_pre_work"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_PROCESS_MESSAGE, 0),
     "ossl_statem_server_process_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_READ_TRANSITION, 0),
     "ossl_statem_server_read_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_OSSL_STATEM_SERVER_WRITE_TRANSITION, 0),
     "ossl_statem_server_write_transition"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_PARSE_CA_NAMES, 0), "parse_ca_names"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_PITEM_NEW, 0), "pitem_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_PQUEUE_NEW, 0), "pqueue_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_PROCESS_KEY_SHARE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_QUIC_CHANGE_CIPHER_STATE, 0),
     "quic_change_cipher_state"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_QUIC_GET_MESSAGE, 0), "quic_get_message"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_QUIC_SET_ENCRYPTION_SECRETS, 0),
     "quic_set_encryption_secrets"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_READ_STATE_MACHINE, 0), "read_state_machine"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SET_CLIENT_CIPHERSUITE, 0),
     "set_client_ciphersuite"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SRP_GENERATE_CLIENT_MASTER_SECRET, 0),
     "srp_generate_client_master_secret"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SRP_GENERATE_SERVER_MASTER_SECRET, 0),
     "srp_generate_server_master_secret"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SRP_VERIFY_SERVER_PARAM, 0),
     "srp_verify_server_param"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_CHANGE_CIPHER_STATE, 0),
     "ssl3_change_cipher_state"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_CHECK_CERT_AND_ALGORITHM, 0),
     "ssl3_check_cert_and_algorithm"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_CTRL, 0), "ssl3_ctrl"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_CTX_CTRL, 0), "ssl3_ctx_ctrl"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_DIGEST_CACHED_RECORDS, 0),
     "ssl3_digest_cached_records"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_DISPATCH_ALERT, 0),
     "ssl3_dispatch_alert"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_DO_CHANGE_CIPHER_SPEC, 0),
     "ssl3_do_change_cipher_spec"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_DO_WRITE, 0), "ssl3_do_write"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_ENC, 0), "ssl3_enc"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_FINAL_FINISH_MAC, 0),
     "ssl3_final_finish_mac"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_FINISH_MAC, 0), "ssl3_finish_mac"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_GENERATE_KEY_BLOCK, 0),
     "ssl3_generate_key_block"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_GENERATE_MASTER_SECRET, 0),
     "ssl3_generate_master_secret"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_GET_RECORD, 0), "ssl3_get_record"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_INIT_FINISHED_MAC, 0),
     "ssl3_init_finished_mac"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_OUTPUT_CERT_CHAIN, 0),
     "ssl3_output_cert_chain"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_READ_BYTES, 0), "ssl3_read_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_READ_N, 0), "ssl3_read_n"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_SETUP_KEY_BLOCK, 0),
     "ssl3_setup_key_block"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_SETUP_READ_BUFFER, 0),
     "ssl3_setup_read_buffer"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_SETUP_WRITE_BUFFER, 0),
     "ssl3_setup_write_buffer"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_WRITE_BYTES, 0), "ssl3_write_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL3_WRITE_PENDING, 0), "ssl3_write_pending"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CERT_CHAIN, 0), "ssl_add_cert_chain"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CERT_TO_BUF, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CERT_TO_WPACKET, 0),
     "ssl_add_cert_to_wpacket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CLIENTHELLO_RENEGOTIATE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CLIENTHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_CLIENTHELLO_USE_SRTP_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_DIR_CERT_SUBJECTS_TO_STACK, 0),
     "SSL_add_dir_cert_subjects_to_stack"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_FILE_CERT_SUBJECTS_TO_STACK, 0),
     "SSL_add_file_cert_subjects_to_stack"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_SERVERHELLO_RENEGOTIATE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_SERVERHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ADD_SERVERHELLO_USE_SRTP_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_BAD_METHOD, 0), "ssl_bad_method"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_BUILD_CERT_CHAIN, 0),
     "ssl_build_cert_chain"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_BYTES_TO_CIPHER_LIST, 0),
     "SSL_bytes_to_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CACHE_CIPHERLIST, 0),
     "ssl_cache_cipherlist"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CERT_ADD0_CHAIN_CERT, 0),
     "ssl_cert_add0_chain_cert"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CERT_DUP, 0), "ssl_cert_dup"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CERT_NEW, 0), "ssl_cert_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CERT_SET0_CHAIN, 0),
     "ssl_cert_set0_chain"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CHECK_PRIVATE_KEY, 0),
     "SSL_check_private_key"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CHECK_SERVERHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CHECK_SRP_EXT_CLIENTHELLO, 0),
     "ssl_check_srp_ext_ClientHello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CHECK_SRVR_ECC_CERT_AND_ALG, 0),
     "ssl_check_srvr_ecc_cert_and_alg"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CHOOSE_CLIENT_VERSION, 0),
     "ssl_choose_client_version"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CIPHER_DESCRIPTION, 0),
     "SSL_CIPHER_description"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CIPHER_LIST_TO_BYTES, 0),
     "ssl_cipher_list_to_bytes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CIPHER_PROCESS_RULESTR, 0),
     "ssl_cipher_process_rulestr"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CIPHER_STRENGTH_SORT, 0),
     "ssl_cipher_strength_sort"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CLEAR, 0), "SSL_clear"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CLIENT_HELLO_GET1_EXTENSIONS_PRESENT, 0),
     "SSL_client_hello_get1_extensions_present"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_COMP_ADD_COMPRESSION_METHOD, 0),
     "SSL_COMP_add_compression_method"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CONF_CMD, 0), "SSL_CONF_cmd"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CREATE_CIPHER_LIST, 0),
     "ssl_create_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTRL, 0), "SSL_ctrl"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_CHECK_PRIVATE_KEY, 0),
     "SSL_CTX_check_private_key"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_ENABLE_CT, 0), "SSL_CTX_enable_ct"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_MAKE_PROFILES, 0),
     "ssl_ctx_make_profiles"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_NEW, 0), "SSL_CTX_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_ALPN_PROTOS, 0),
     "SSL_CTX_set_alpn_protos"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_CIPHER_LIST, 0),
     "SSL_CTX_set_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_CLIENT_CERT_ENGINE, 0),
     "SSL_CTX_set_client_cert_engine"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_CT_VALIDATION_CALLBACK, 0),
     "SSL_CTX_set_ct_validation_callback"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_SESSION_ID_CONTEXT, 0),
     "SSL_CTX_set_session_id_context"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_SSL_VERSION, 0),
     "SSL_CTX_set_ssl_version"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_SET_TLSEXT_MAX_FRAGMENT_LENGTH, 0),
     "SSL_CTX_set_tlsext_max_fragment_length"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_CERTIFICATE, 0),
     "SSL_CTX_use_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_CERTIFICATE_ASN1, 0),
     "SSL_CTX_use_certificate_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_CERTIFICATE_FILE, 0),
     "SSL_CTX_use_certificate_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_PRIVATEKEY, 0),
     "SSL_CTX_use_PrivateKey"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_PRIVATEKEY_ASN1, 0),
     "SSL_CTX_use_PrivateKey_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_PRIVATEKEY_FILE, 0),
     "SSL_CTX_use_PrivateKey_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_PSK_IDENTITY_HINT, 0),
     "SSL_CTX_use_psk_identity_hint"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_RSAPRIVATEKEY, 0),
     "SSL_CTX_use_RSAPrivateKey"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_RSAPRIVATEKEY_ASN1, 0),
     "SSL_CTX_use_RSAPrivateKey_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_RSAPRIVATEKEY_FILE, 0),
     "SSL_CTX_use_RSAPrivateKey_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_SERVERINFO, 0),
     "SSL_CTX_use_serverinfo"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_SERVERINFO_EX, 0),
     "SSL_CTX_use_serverinfo_ex"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_CTX_USE_SERVERINFO_FILE, 0),
     "SSL_CTX_use_serverinfo_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DANE_DUP, 0), "ssl_dane_dup"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DANE_ENABLE, 0), "SSL_dane_enable"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DERIVE, 0), "ssl_derive"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DO_CONFIG, 0), "ssl_do_config"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DO_HANDSHAKE, 0), "SSL_do_handshake"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_DUP_CA_LIST, 0), "SSL_dup_CA_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_ENABLE_CT, 0), "SSL_enable_ct"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GENERATE_PKEY_GROUP, 0),
     "ssl_generate_pkey_group"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GENERATE_SESSION_ID, 0),
     "ssl_generate_session_id"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GET_NEW_SESSION, 0),
     "ssl_get_new_session"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GET_PREV_SESSION, 0),
     "ssl_get_prev_session"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GET_SERVER_CERT_INDEX, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_GET_SIGN_PKEY, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_HANDSHAKE_HASH, 0), "ssl_handshake_hash"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_INIT_WBIO_BUFFER, 0),
     "ssl_init_wbio_buffer"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_KEY_UPDATE, 0), "SSL_key_update"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_LOAD_CLIENT_CA_FILE, 0),
     "SSL_load_client_CA_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_LOG_MASTER_SECRET, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_LOG_RSA_CLIENT_KEY_EXCHANGE, 0),
     "ssl_log_rsa_client_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_MODULE_INIT, 0), "ssl_module_init"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_NEW, 0), "SSL_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_NEXT_PROTO_VALIDATE, 0),
     "ssl_next_proto_validate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_CLIENTHELLO_RENEGOTIATE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_CLIENTHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_CLIENTHELLO_USE_SRTP_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_SERVERHELLO_RENEGOTIATE_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_SERVERHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PARSE_SERVERHELLO_USE_SRTP_EXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PEEK, 0), "SSL_peek"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PEEK_EX, 0), "SSL_peek_ex"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PEEK_INTERNAL, 0), "ssl_peek_internal"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PROCESS_QUIC_POST_HANDSHAKE, 0),
     "SSL_process_quic_post_handshake"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_PROVIDE_QUIC_DATA, 0),
     "SSL_provide_quic_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_READ, 0), "SSL_read"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_READ_EARLY_DATA, 0),
     "SSL_read_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_READ_EX, 0), "SSL_read_ex"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_READ_INTERNAL, 0), "ssl_read_internal"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_RENEGOTIATE, 0), "SSL_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_RENEGOTIATE_ABBREVIATED, 0),
     "SSL_renegotiate_abbreviated"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SCAN_CLIENTHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SCAN_SERVERHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SESSION_DUP, 0), "ssl_session_dup"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SESSION_NEW, 0), "SSL_SESSION_new"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SESSION_PRINT_FP, 0),
     "SSL_SESSION_print_fp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SESSION_SET1_ID, 0),
     "SSL_SESSION_set1_id"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SESSION_SET1_ID_CONTEXT, 0),
     "SSL_SESSION_set1_id_context"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_ALPN_PROTOS, 0),
     "SSL_set_alpn_protos"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_CERT, 0), "ssl_set_cert"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_CERT_AND_KEY, 0),
     "ssl_set_cert_and_key"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_CIPHER_LIST, 0),
     "SSL_set_cipher_list"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_CT_VALIDATION_CALLBACK, 0),
     "SSL_set_ct_validation_callback"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_FD, 0), "SSL_set_fd"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_PKEY, 0), "ssl_set_pkey"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_RFD, 0), "SSL_set_rfd"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_SESSION, 0), "SSL_set_session"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_SESSION_ID_CONTEXT, 0),
     "SSL_set_session_id_context"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_SESSION_TICKET_EXT, 0),
     "SSL_set_session_ticket_ext"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_TLSEXT_MAX_FRAGMENT_LENGTH, 0),
     "SSL_set_tlsext_max_fragment_length"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SET_WFD, 0), "SSL_set_wfd"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SHUTDOWN, 0), "SSL_shutdown"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_SRP_CTX_INIT, 0), "SSL_SRP_CTX_init"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_START_ASYNC_JOB, 0),
     "ssl_start_async_job"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_UNDEFINED_FUNCTION, 0),
     "ssl_undefined_function"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_UNDEFINED_VOID_FUNCTION, 0),
     "ssl_undefined_void_function"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_CERTIFICATE, 0),
     "SSL_use_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_CERTIFICATE_ASN1, 0),
     "SSL_use_certificate_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_CERTIFICATE_FILE, 0),
     "SSL_use_certificate_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_PRIVATEKEY, 0), "SSL_use_PrivateKey"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_PRIVATEKEY_ASN1, 0),
     "SSL_use_PrivateKey_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_PRIVATEKEY_FILE, 0),
     "SSL_use_PrivateKey_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_PSK_IDENTITY_HINT, 0),
     "SSL_use_psk_identity_hint"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_RSAPRIVATEKEY, 0),
     "SSL_use_RSAPrivateKey"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_RSAPRIVATEKEY_ASN1, 0),
     "SSL_use_RSAPrivateKey_ASN1"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_USE_RSAPRIVATEKEY_FILE, 0),
     "SSL_use_RSAPrivateKey_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_VALIDATE_CT, 0), "ssl_validate_ct"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_VERIFY_CERT_CHAIN, 0),
     "ssl_verify_cert_chain"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_VERIFY_CLIENT_POST_HANDSHAKE, 0),
     "SSL_verify_client_post_handshake"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_WRITE, 0), "SSL_write"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_WRITE_EARLY_DATA, 0),
     "SSL_write_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_WRITE_EARLY_FINISH, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_WRITE_EX, 0), "SSL_write_ex"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_SSL_WRITE_INTERNAL, 0), "ssl_write_internal"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_STATEM_FLUSH, 0), "statem_flush"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_STATE_MACHINE, 0), "state_machine"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS12_CHECK_PEER_SIGALG, 0),
     "tls12_check_peer_sigalg"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS12_COPY_SIGALGS, 0), "tls12_copy_sigalgs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_CHANGE_CIPHER_STATE, 0),
     "tls13_change_cipher_state"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_ENC, 0), "tls13_enc"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_FINAL_FINISH_MAC, 0),
     "tls13_final_finish_mac"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_GENERATE_SECRET, 0),
     "tls13_generate_secret"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_HKDF_EXPAND, 0), "tls13_hkdf_expand"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_RESTORE_HANDSHAKE_DIGEST_FOR_PHA, 0),
     "tls13_restore_handshake_digest_for_pha"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_SAVE_HANDSHAKE_DIGEST_FOR_PHA, 0),
     "tls13_save_handshake_digest_for_pha"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS13_SETUP_KEY_BLOCK, 0),
     "tls13_setup_key_block"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_CHANGE_CIPHER_STATE, 0),
     "tls1_change_cipher_state"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_CHECK_DUPLICATE_EXTENSIONS, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_ENC, 0), "tls1_enc"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_EXPORT_KEYING_MATERIAL, 0),
     "tls1_export_keying_material"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_GET_CURVELIST, 0), "tls1_get_curvelist"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_PRF, 0), "tls1_PRF"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SAVE_U16, 0), "tls1_save_u16"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SETUP_KEY_BLOCK, 0),
     "tls1_setup_key_block"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SET_GROUPS, 0), "tls1_set_groups"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SET_RAW_SIGALGS, 0),
     "tls1_set_raw_sigalgs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SET_SERVER_SIGALGS, 0),
     "tls1_set_server_sigalgs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SET_SHARED_SIGALGS, 0),
     "tls1_set_shared_sigalgs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS1_SET_SIGALGS, 0), "tls1_set_sigalgs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CHOOSE_SIGALG, 0), "tls_choose_sigalg"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CLIENT_KEY_EXCHANGE_POST_WORK, 0),
     "tls_client_key_exchange_post_work"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_COLLECT_EXTENSIONS, 0),
     "tls_collect_extensions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CERTIFICATE_AUTHORITIES, 0),
     "tls_construct_certificate_authorities"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CERTIFICATE_REQUEST, 0),
     "tls_construct_certificate_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CERT_STATUS, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CERT_STATUS_BODY, 0),
     "tls_construct_cert_status_body"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CERT_VERIFY, 0),
     "tls_construct_cert_verify"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CHANGE_CIPHER_SPEC, 0),
     "tls_construct_change_cipher_spec"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_DHE, 0),
     "tls_construct_cke_dhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_ECDHE, 0),
     "tls_construct_cke_ecdhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_GOST, 0),
     "tls_construct_cke_gost"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_PSK_PREAMBLE, 0),
     "tls_construct_cke_psk_preamble"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_RSA, 0),
     "tls_construct_cke_rsa"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CKE_SRP, 0),
     "tls_construct_cke_srp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CLIENT_CERTIFICATE, 0),
     "tls_construct_client_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CLIENT_HELLO, 0),
     "tls_construct_client_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CLIENT_KEY_EXCHANGE, 0),
     "tls_construct_client_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CLIENT_VERIFY, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_ALPN, 0),
     "tls_construct_ctos_alpn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_CERTIFICATE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_COOKIE, 0),
     "tls_construct_ctos_cookie"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_EARLY_DATA, 0),
     "tls_construct_ctos_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_EC_PT_FORMATS, 0),
     "tls_construct_ctos_ec_pt_formats"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_EMS, 0),
     "tls_construct_ctos_ems"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_ETM, 0),
     "tls_construct_ctos_etm"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_HELLO, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_KEY_EXCHANGE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_KEY_SHARE, 0),
     "tls_construct_ctos_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_MAXFRAGMENTLEN, 0),
     "tls_construct_ctos_maxfragmentlen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_NPN, 0),
     "tls_construct_ctos_npn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_PADDING, 0),
     "tls_construct_ctos_padding"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_POST_HANDSHAKE_AUTH, 0),
     "tls_construct_ctos_post_handshake_auth"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_PSK, 0),
     "tls_construct_ctos_psk"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_PSK_KEX_MODES, 0),
     "tls_construct_ctos_psk_kex_modes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_QUIC_TRANSPORT_PARAMS, 0),
     "tls_construct_ctos_quic_transport_params"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_QUIC_TRANSPORT_PARAMS_DRAFT, 0),
     "tls_construct_ctos_quic_transport_params_draft"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_RENEGOTIATE, 0),
     "tls_construct_ctos_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SCT, 0),
     "tls_construct_ctos_sct"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SERVER_NAME, 0),
     "tls_construct_ctos_server_name"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SESSION_TICKET, 0),
     "tls_construct_ctos_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SIG_ALGS, 0),
     "tls_construct_ctos_sig_algs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SRP, 0),
     "tls_construct_ctos_srp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_STATUS_REQUEST, 0),
     "tls_construct_ctos_status_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SUPPORTED_GROUPS, 0),
     "tls_construct_ctos_supported_groups"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_SUPPORTED_VERSIONS, 0),
     "tls_construct_ctos_supported_versions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_USE_SRTP, 0),
     "tls_construct_ctos_use_srtp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_CTOS_VERIFY, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_ENCRYPTED_EXTENSIONS, 0),
     "tls_construct_encrypted_extensions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_END_OF_EARLY_DATA, 0),
     "tls_construct_end_of_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_EXTENSIONS, 0),
     "tls_construct_extensions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_FINISHED, 0),
     "tls_construct_finished"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_HELLO_REQUEST, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_HELLO_RETRY_REQUEST, 0),
     "tls_construct_hello_retry_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_KEY_UPDATE, 0),
     "tls_construct_key_update"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_NEW_SESSION_TICKET, 0),
     "tls_construct_new_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_NEXT_PROTO, 0),
     "tls_construct_next_proto"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_SERVER_CERTIFICATE, 0),
     "tls_construct_server_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_SERVER_HELLO, 0),
     "tls_construct_server_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_SERVER_KEY_EXCHANGE, 0),
     "tls_construct_server_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_ALPN, 0),
     "tls_construct_stoc_alpn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_CERTIFICATE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_COOKIE, 0),
     "tls_construct_stoc_cookie"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_CRYPTOPRO_BUG, 0),
     "tls_construct_stoc_cryptopro_bug"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_DONE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_EARLY_DATA, 0),
     "tls_construct_stoc_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_EARLY_DATA_INFO, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_EC_PT_FORMATS, 0),
     "tls_construct_stoc_ec_pt_formats"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_EMS, 0),
     "tls_construct_stoc_ems"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_ETM, 0),
     "tls_construct_stoc_etm"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_HELLO, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_KEY_EXCHANGE, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_KEY_SHARE, 0),
     "tls_construct_stoc_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_MAXFRAGMENTLEN, 0),
     "tls_construct_stoc_maxfragmentlen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_NEXT_PROTO_NEG, 0),
     "tls_construct_stoc_next_proto_neg"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_PSK, 0),
     "tls_construct_stoc_psk"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_QUIC_TRANSPORT_PARAMS, 0),
     "tls_construct_stoc_quic_transport_params"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_QUIC_TRANSPORT_PARAMS_DRAFT, 0),
     "tls_construct_stoc_quic_transport_params_draft"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_RENEGOTIATE, 0),
     "tls_construct_stoc_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_SERVER_NAME, 0),
     "tls_construct_stoc_server_name"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_SESSION_TICKET, 0),
     "tls_construct_stoc_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_STATUS_REQUEST, 0),
     "tls_construct_stoc_status_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_SUPPORTED_GROUPS, 0),
     "tls_construct_stoc_supported_groups"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_SUPPORTED_VERSIONS, 0),
     "tls_construct_stoc_supported_versions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_CONSTRUCT_STOC_USE_SRTP, 0),
     "tls_construct_stoc_use_srtp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_EARLY_POST_PROCESS_CLIENT_HELLO, 0),
     "tls_early_post_process_client_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_FINISH_HANDSHAKE, 0),
     "tls_finish_handshake"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_GET_MESSAGE_BODY, 0),
     "tls_get_message_body"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_GET_MESSAGE_HEADER, 0),
     "tls_get_message_header"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_HANDLE_ALPN, 0), "tls_handle_alpn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_HANDLE_STATUS_REQUEST, 0),
     "tls_handle_status_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CERTIFICATE_AUTHORITIES, 0),
     "tls_parse_certificate_authorities"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CLIENTHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_ALPN, 0),
     "tls_parse_ctos_alpn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_COOKIE, 0),
     "tls_parse_ctos_cookie"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_EARLY_DATA, 0),
     "tls_parse_ctos_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_EC_PT_FORMATS, 0),
     "tls_parse_ctos_ec_pt_formats"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_EMS, 0), "tls_parse_ctos_ems"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_KEY_SHARE, 0),
     "tls_parse_ctos_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_MAXFRAGMENTLEN, 0),
     "tls_parse_ctos_maxfragmentlen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_POST_HANDSHAKE_AUTH, 0),
     "tls_parse_ctos_post_handshake_auth"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_PSK, 0), "tls_parse_ctos_psk"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_PSK_KEX_MODES, 0),
     "tls_parse_ctos_psk_kex_modes"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_QUIC_TRANSPORT_PARAMS, 0),
     "tls_parse_ctos_quic_transport_params"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_QUIC_TRANSPORT_PARAMS_DRAFT, 0),
     "tls_parse_ctos_quic_transport_params_draft"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_RENEGOTIATE, 0),
     "tls_parse_ctos_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SERVER_NAME, 0),
     "tls_parse_ctos_server_name"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SESSION_TICKET, 0),
     "tls_parse_ctos_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SIG_ALGS, 0),
     "tls_parse_ctos_sig_algs"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SIG_ALGS_CERT, 0),
     "tls_parse_ctos_sig_algs_cert"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SRP, 0), "tls_parse_ctos_srp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_STATUS_REQUEST, 0),
     "tls_parse_ctos_status_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_SUPPORTED_GROUPS, 0),
     "tls_parse_ctos_supported_groups"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_CTOS_USE_SRTP, 0),
     "tls_parse_ctos_use_srtp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_ALPN, 0),
     "tls_parse_stoc_alpn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_COOKIE, 0),
     "tls_parse_stoc_cookie"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_EARLY_DATA, 0),
     "tls_parse_stoc_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_EARLY_DATA_INFO, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_EC_PT_FORMATS, 0),
     "tls_parse_stoc_ec_pt_formats"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_KEY_SHARE, 0),
     "tls_parse_stoc_key_share"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_MAXFRAGMENTLEN, 0),
     "tls_parse_stoc_maxfragmentlen"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_NPN, 0), "tls_parse_stoc_npn"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_PSK, 0), "tls_parse_stoc_psk"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_QUIC_TRANSPORT_PARAMS, 0),
     "tls_parse_stoc_quic_transport_params"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_QUIC_TRANSPORT_PARAMS_DRAFT, 0),
     "tls_parse_stoc_quic_transport_params_draft"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_RENEGOTIATE, 0),
     "tls_parse_stoc_renegotiate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_SCT, 0), "tls_parse_stoc_sct"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_SERVER_NAME, 0),
     "tls_parse_stoc_server_name"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_SESSION_TICKET, 0),
     "tls_parse_stoc_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_STATUS_REQUEST, 0),
     "tls_parse_stoc_status_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_SUPPORTED_VERSIONS, 0),
     "tls_parse_stoc_supported_versions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PARSE_STOC_USE_SRTP, 0),
     "tls_parse_stoc_use_srtp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_POST_PROCESS_CLIENT_HELLO, 0),
     "tls_post_process_client_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_POST_PROCESS_CLIENT_KEY_EXCHANGE, 0),
     "tls_post_process_client_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PREPARE_CLIENT_CERTIFICATE, 0),
     "tls_prepare_client_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_AS_HELLO_RETRY_REQUEST, 0),
     "tls_process_as_hello_retry_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CERTIFICATE_REQUEST, 0),
     "tls_process_certificate_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CERT_STATUS, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CERT_STATUS_BODY, 0),
     "tls_process_cert_status_body"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CERT_VERIFY, 0),
     "tls_process_cert_verify"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CHANGE_CIPHER_SPEC, 0),
     "tls_process_change_cipher_spec"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_DHE, 0),
     "tls_process_cke_dhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_ECDHE, 0),
     "tls_process_cke_ecdhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_GOST, 0),
     "tls_process_cke_gost"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_PSK_PREAMBLE, 0),
     "tls_process_cke_psk_preamble"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_RSA, 0),
     "tls_process_cke_rsa"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CKE_SRP, 0),
     "tls_process_cke_srp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CLIENT_CERTIFICATE, 0),
     "tls_process_client_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CLIENT_HELLO, 0),
     "tls_process_client_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_CLIENT_KEY_EXCHANGE, 0),
     "tls_process_client_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_ENCRYPTED_EXTENSIONS, 0),
     "tls_process_encrypted_extensions"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_END_OF_EARLY_DATA, 0),
     "tls_process_end_of_early_data"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_FINISHED, 0),
     "tls_process_finished"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_HELLO_REQ, 0),
     "tls_process_hello_req"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_HELLO_RETRY_REQUEST, 0),
     "tls_process_hello_retry_request"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_INITIAL_SERVER_FLIGHT, 0),
     "tls_process_initial_server_flight"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_KEY_EXCHANGE, 0),
     "tls_process_key_exchange"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_KEY_UPDATE, 0),
     "tls_process_key_update"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_NEW_SESSION_TICKET, 0),
     "tls_process_new_session_ticket"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_NEXT_PROTO, 0),
     "tls_process_next_proto"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SERVER_CERTIFICATE, 0),
     "tls_process_server_certificate"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SERVER_DONE, 0),
     "tls_process_server_done"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SERVER_HELLO, 0),
     "tls_process_server_hello"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SKE_DHE, 0),
     "tls_process_ske_dhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SKE_ECDHE, 0),
     "tls_process_ske_ecdhe"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SKE_PSK_PREAMBLE, 0),
     "tls_process_ske_psk_preamble"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PROCESS_SKE_SRP, 0),
     "tls_process_ske_srp"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_PSK_DO_BINDER, 0), "tls_psk_do_binder"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_SCAN_CLIENTHELLO_TLSEXT, 0), ""},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_TLS_SETUP_HANDSHAKE, 0),
     "tls_setup_handshake"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_USE_CERTIFICATE_CHAIN_FILE, 0),
     "use_certificate_chain_file"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_WPACKET_INTERN_INIT_LEN, 0),
     "wpacket_intern_init_len"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_WPACKET_START_SUB_PACKET_LEN__, 0),
     "WPACKET_start_sub_packet_len__"},
    {ERR_PACK(ERR_LIB_SSL, SSL_F_WRITE_STATE_MACHINE, 0),
     "write_state_machine"},
    {0, NULL}
};

static const ERR_STRING_DATA SSL_str_reasons[] = {
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_APPLICATION_DATA_AFTER_CLOSE_NOTIFY),
    "application data after close notify"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_APP_DATA_IN_HANDSHAKE),
    "app data in handshake"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT),
    "attempt to reuse session in different context"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_AT_LEAST_TLS_1_0_NEEDED_IN_FIPS_MODE),
    "at least TLS 1.0 needed in FIPS mode"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE),
    "at least (D)TLS 1.2 needed in Suite B mode"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_CHANGE_CIPHER_SPEC),
    "bad change cipher spec"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_CIPHER), "bad cipher"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_DATA), "bad data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_DATA_RETURNED_BY_CALLBACK),
    "bad data returned by callback"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_DECOMPRESSION), "bad decompression"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_DH_VALUE), "bad dh value"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_DIGEST_LENGTH), "bad digest length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_EARLY_DATA), "bad early data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_ECC_CERT), "bad ecc cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_ECPOINT), "bad ecpoint"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_EXTENSION), "bad extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_HANDSHAKE_LENGTH),
    "bad handshake length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_HANDSHAKE_STATE),
    "bad handshake state"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_HELLO_REQUEST), "bad hello request"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_HRR_VERSION), "bad hrr version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_KEY_SHARE), "bad key share"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_KEY_UPDATE), "bad key update"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_LEGACY_VERSION), "bad legacy version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_LENGTH), "bad length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_PACKET), "bad packet"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_PACKET_LENGTH), "bad packet length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_PROTOCOL_VERSION_NUMBER),
    "bad protocol version number"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_PSK), "bad psk"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_PSK_IDENTITY), "bad psk identity"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_RECORD_TYPE), "bad record type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_RSA_ENCRYPT), "bad rsa encrypt"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SIGNATURE), "bad signature"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SRP_A_LENGTH), "bad srp a length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SRP_PARAMETERS), "bad srp parameters"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SRTP_MKI_VALUE), "bad srtp mki value"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SRTP_PROTECTION_PROFILE_LIST),
    "bad srtp protection profile list"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_SSL_FILETYPE), "bad ssl filetype"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_VALUE), "bad value"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BAD_WRITE_RETRY), "bad write retry"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BINDER_DOES_NOT_VERIFY),
    "binder does not verify"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BIO_NOT_SET), "bio not set"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BLOCK_CIPHER_PAD_IS_WRONG),
    "block cipher pad is wrong"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_BN_LIB), "bn lib"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CALLBACK_FAILED), "callback failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CANNOT_CHANGE_CIPHER),
    "cannot change cipher"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CA_DN_LENGTH_MISMATCH),
    "ca dn length mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CA_KEY_TOO_SMALL), "ca key too small"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CA_MD_TOO_WEAK), "ca md too weak"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CCS_RECEIVED_EARLY), "ccs received early"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CERTIFICATE_VERIFY_FAILED),
    "certificate verify failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CERT_CB_ERROR), "cert cb error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CERT_LENGTH_MISMATCH),
    "cert length mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CIPHERSUITE_DIGEST_HAS_CHANGED),
    "ciphersuite digest has changed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CIPHER_CODE_WRONG_LENGTH),
    "cipher code wrong length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CIPHER_OR_HASH_UNAVAILABLE),
    "cipher or hash unavailable"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CLIENTHELLO_TLSEXT), "clienthello tlsext"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COMPRESSED_LENGTH_TOO_LONG),
    "compressed length too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COMPRESSION_DISABLED),
    "compression disabled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COMPRESSION_FAILURE),
    "compression failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE),
    "compression id not within private range"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COMPRESSION_LIBRARY_ERROR),
    "compression library error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CONNECTION_TYPE_NOT_SET),
    "connection type not set"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CONTEXT_NOT_DANE_ENABLED),
    "context not dane enabled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COOKIE_GEN_CALLBACK_FAILURE),
    "cookie gen callback failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_COOKIE_MISMATCH), "cookie mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_CUSTOM_EXT_HANDLER_ALREADY_INSTALLED),
    "custom ext handler already installed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_ALREADY_ENABLED),
    "dane already enabled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_CANNOT_OVERRIDE_MTYPE_FULL),
    "dane cannot override mtype full"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_NOT_ENABLED), "dane not enabled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_CERTIFICATE),
    "dane tlsa bad certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_CERTIFICATE_USAGE),
    "dane tlsa bad certificate usage"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_DATA_LENGTH),
    "dane tlsa bad data length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_DIGEST_LENGTH),
    "dane tlsa bad digest length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_MATCHING_TYPE),
    "dane tlsa bad matching type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_PUBLIC_KEY),
    "dane tlsa bad public key"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_BAD_SELECTOR),
    "dane tlsa bad selector"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DANE_TLSA_NULL_DATA),
    "dane tlsa null data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DATA_BETWEEN_CCS_AND_FINISHED),
    "data between ccs and finished"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DATA_LENGTH_TOO_LONG),
    "data length too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DECRYPTION_FAILED), "decryption failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DECRYPTION_FAILED_OR_BAD_RECORD_MAC),
    "decryption failed or bad record mac"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DH_KEY_TOO_SMALL), "dh key too small"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DH_PUBLIC_VALUE_LENGTH_IS_WRONG),
    "dh public value length is wrong"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DIGEST_CHECK_FAILED),
    "digest check failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DTLS_MESSAGE_TOO_BIG),
    "dtls message too big"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_DUPLICATE_COMPRESSION_ID),
    "duplicate compression id"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ECC_CERT_NOT_FOR_SIGNING),
    "ecc cert not for signing"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ECDH_REQUIRED_FOR_SUITEB_MODE),
    "ecdh required for suiteb mode"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EE_KEY_TOO_SMALL), "ee key too small"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EMPTY_SRTP_PROTECTION_PROFILE_LIST),
    "empty srtp protection profile list"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ENCRYPTED_LENGTH_TOO_LONG),
    "encrypted length too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ERROR_IN_RECEIVED_CIPHER_LIST),
    "error in received cipher list"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ERROR_SETTING_TLSA_BASE_DOMAIN),
    "error setting tlsa base domain"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EXCEEDS_MAX_FRAGMENT_SIZE),
    "exceeds max fragment size"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EXCESSIVE_MESSAGE_SIZE),
    "excessive message size"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EXTENSION_NOT_RECEIVED),
    "extension not received"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EXTRA_DATA_IN_MESSAGE),
    "extra data in message"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_EXT_LENGTH_MISMATCH),
    "ext length mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_FAILED_TO_INIT_ASYNC),
    "failed to init async"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_FRAGMENTED_CLIENT_HELLO),
    "fragmented client hello"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_GOT_A_FIN_BEFORE_A_CCS),
    "got a fin before a ccs"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_HTTPS_PROXY_REQUEST),
    "https proxy request"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_HTTP_REQUEST), "http request"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ILLEGAL_POINT_COMPRESSION),
    "illegal point compression"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_ILLEGAL_SUITEB_DIGEST),
    "illegal Suite B digest"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INAPPROPRIATE_FALLBACK),
    "inappropriate fallback"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INCONSISTENT_COMPRESSION),
    "inconsistent compression"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INCONSISTENT_EARLY_DATA_ALPN),
    "inconsistent early data alpn"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INCONSISTENT_EARLY_DATA_SNI),
    "inconsistent early data sni"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INCONSISTENT_EXTMS), "inconsistent extms"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INSUFFICIENT_SECURITY),
    "insufficient security"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_ALERT), "invalid alert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CCS_MESSAGE),
    "invalid ccs message"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CERTIFICATE_OR_ALG),
    "invalid certificate or alg"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_COMMAND), "invalid command"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_COMPRESSION_ALGORITHM),
    "invalid compression algorithm"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CONFIG), "invalid config"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CONFIGURATION_NAME),
    "invalid configuration name"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CONTEXT), "invalid context"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_CT_VALIDATION_TYPE),
    "invalid ct validation type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_KEY_UPDATE_TYPE),
    "invalid key update type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_MAX_EARLY_DATA),
    "invalid max early data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_NULL_CMD_NAME),
    "invalid null cmd name"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_SEQUENCE_NUMBER),
    "invalid sequence number"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_SERVERINFO_DATA),
    "invalid serverinfo data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_SESSION_ID), "invalid session id"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_SRP_USERNAME),
    "invalid srp username"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_STATUS_RESPONSE),
    "invalid status response"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_INVALID_TICKET_KEYS_LENGTH),
    "invalid ticket keys length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_LENGTH_MISMATCH), "length mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_LENGTH_TOO_LONG), "length too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_LENGTH_TOO_SHORT), "length too short"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_LIBRARY_BUG), "library bug"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_LIBRARY_HAS_NO_CIPHERS),
    "library has no ciphers"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_DSA_SIGNING_CERT),
    "missing dsa signing cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_ECDSA_SIGNING_CERT),
    "missing ecdsa signing cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_FATAL), "missing fatal"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_PARAMETERS), "missing parameters"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_PSK_KEX_MODES_EXTENSION),
    "missing psk kex modes extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_QUIC_TRANSPORT_PARAMETERS_EXTENSION),
    "missing quic transport parameters extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_RSA_CERTIFICATE),
    "missing rsa certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_RSA_ENCRYPTING_CERT),
    "missing rsa encrypting cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_RSA_SIGNING_CERT),
    "missing rsa signing cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_SIGALGS_EXTENSION),
    "missing sigalgs extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_SIGNING_CERT),
    "missing signing cert"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_SRP_PARAM),
    "can't find SRP server param"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_SUPPORTED_GROUPS_EXTENSION),
    "missing supported groups extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_TMP_DH_KEY), "missing tmp dh key"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MISSING_TMP_ECDH_KEY),
    "missing tmp ecdh key"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA),
    "mixed handshake and non handshake data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NOT_ON_RECORD_BOUNDARY),
    "not on record boundary"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NOT_REPLACING_CERTIFICATE),
    "not replacing certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NOT_SERVER), "not server"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_APPLICATION_PROTOCOL),
    "no application protocol"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CERTIFICATES_RETURNED),
    "no certificates returned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CERTIFICATE_ASSIGNED),
    "no certificate assigned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CERTIFICATE_SET), "no certificate set"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CHANGE_FOLLOWING_HRR),
    "no change following hrr"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CIPHERS_AVAILABLE),
    "no ciphers available"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CIPHERS_SPECIFIED),
    "no ciphers specified"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CIPHER_MATCH), "no cipher match"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_CLIENT_CERT_METHOD),
    "no client cert method"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_COMPRESSION_SPECIFIED),
    "no compression specified"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_COOKIE_CALLBACK_SET),
    "no cookie callback set"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_GOST_CERTIFICATE_SENT_BY_PEER),
    "Peer haven't sent GOST certificate, required for selected ciphersuite"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_METHOD_SPECIFIED),
    "no method specified"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_PEM_EXTENSIONS), "no pem extensions"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_PRIVATE_KEY_ASSIGNED),
    "no private key assigned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_PROTOCOLS_AVAILABLE),
    "no protocols available"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_RENEGOTIATION), "no renegotiation"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_REQUIRED_DIGEST), "no required digest"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SHARED_CIPHER), "no shared cipher"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SHARED_GROUPS), "no shared groups"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SHARED_SIGNATURE_ALGORITHMS),
    "no shared signature algorithms"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SRTP_PROFILES), "no srtp profiles"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SUITABLE_KEY_SHARE),
    "no suitable key share"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_SUITABLE_SIGNATURE_ALGORITHM),
    "no suitable signature algorithm"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_VALID_SCTS), "no valid scts"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NO_VERIFY_COOKIE_CALLBACK),
    "no verify cookie callback"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NULL_SSL_CTX), "null ssl ctx"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_NULL_SSL_METHOD_PASSED),
    "null ssl method passed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_OCSP_CALLBACK_FAILURE),
    "ocsp callback failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_OLD_SESSION_CIPHER_NOT_RETURNED),
    "old session cipher not returned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED),
    "old session compression algorithm not returned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_OVERFLOW_ERROR), "overflow error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PACKET_LENGTH_TOO_LONG),
    "packet length too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PARSE_TLSEXT), "parse tlsext"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PATH_TOO_LONG), "path too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PEER_DID_NOT_RETURN_A_CERTIFICATE),
    "peer did not return a certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PEM_NAME_BAD_PREFIX),
    "pem name bad prefix"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PEM_NAME_TOO_SHORT), "pem name too short"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PIPELINE_FAILURE), "pipeline failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_POST_HANDSHAKE_AUTH_ENCODING_ERR),
    "post handshake auth encoding err"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PRIVATE_KEY_MISMATCH),
    "private key mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PROTOCOL_IS_SHUTDOWN),
    "protocol is shutdown"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PSK_IDENTITY_NOT_FOUND),
    "psk identity not found"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PSK_NO_CLIENT_CB), "psk no client cb"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_PSK_NO_SERVER_CB), "psk no server cb"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_READ_BIO_NOT_SET), "read bio not set"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_READ_TIMEOUT_EXPIRED),
    "read timeout expired"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_RECORD_LENGTH_MISMATCH),
    "record length mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_RECORD_TOO_SMALL), "record too small"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_RENEGOTIATE_EXT_TOO_LONG),
    "renegotiate ext too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_RENEGOTIATION_ENCODING_ERR),
    "renegotiation encoding err"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_RENEGOTIATION_MISMATCH),
    "renegotiation mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_REQUEST_PENDING), "request pending"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_REQUEST_SENT), "request sent"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_REQUIRED_CIPHER_MISSING),
    "required cipher missing"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_REQUIRED_COMPRESSION_ALGORITHM_MISSING),
    "required compression algorithm missing"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SCSV_RECEIVED_WHEN_RENEGOTIATING),
    "scsv received when renegotiating"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SCT_VERIFICATION_FAILED),
    "sct verification failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SERVERHELLO_TLSEXT), "serverhello tlsext"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SESSION_ID_CONTEXT_UNINITIALIZED),
    "session id context uninitialized"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SHUTDOWN_WHILE_IN_INIT),
    "shutdown while in init"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SIGNATURE_ALGORITHMS_ERROR),
    "signature algorithms error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SIGNATURE_FOR_NON_SIGNING_CERTIFICATE),
    "signature for non signing certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SRP_A_CALC), "error with the srp params"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SRTP_COULD_NOT_ALLOCATE_PROFILES),
    "srtp could not allocate profiles"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SRTP_PROTECTION_PROFILE_LIST_TOO_LONG),
    "srtp protection profile list too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SRTP_UNKNOWN_PROTECTION_PROFILE),
    "srtp unknown protection profile"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH),
    "ssl3 ext invalid max fragment length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL3_EXT_INVALID_SERVERNAME),
    "ssl3 ext invalid servername"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL3_EXT_INVALID_SERVERNAME_TYPE),
    "ssl3 ext invalid servername type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL3_SESSION_ID_TOO_LONG),
    "ssl3 session id too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_BAD_CERTIFICATE),
    "sslv3 alert bad certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_BAD_RECORD_MAC),
    "sslv3 alert bad record mac"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_CERTIFICATE_EXPIRED),
    "sslv3 alert certificate expired"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_CERTIFICATE_REVOKED),
    "sslv3 alert certificate revoked"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_CERTIFICATE_UNKNOWN),
    "sslv3 alert certificate unknown"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_DECOMPRESSION_FAILURE),
    "sslv3 alert decompression failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_HANDSHAKE_FAILURE),
    "sslv3 alert handshake failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_ILLEGAL_PARAMETER),
    "sslv3 alert illegal parameter"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_NO_CERTIFICATE),
    "sslv3 alert no certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_UNEXPECTED_MESSAGE),
    "sslv3 alert unexpected message"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSLV3_ALERT_UNSUPPORTED_CERTIFICATE),
    "sslv3 alert unsupported certificate"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_COMMAND_SECTION_EMPTY),
    "ssl command section empty"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_COMMAND_SECTION_NOT_FOUND),
    "ssl command section not found"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION),
    "ssl ctx has no default ssl version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_HANDSHAKE_FAILURE),
    "ssl handshake failure"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_LIBRARY_HAS_NO_CIPHERS),
    "ssl library has no ciphers"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_NEGATIVE_LENGTH),
    "ssl negative length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SECTION_EMPTY), "ssl section empty"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SECTION_NOT_FOUND),
    "ssl section not found"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_ID_CALLBACK_FAILED),
    "ssl session id callback failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_ID_CONFLICT),
    "ssl session id conflict"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_ID_CONTEXT_TOO_LONG),
    "ssl session id context too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_ID_HAS_BAD_LENGTH),
    "ssl session id has bad length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_ID_TOO_LONG),
    "ssl session id too long"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_SSL_SESSION_VERSION_MISMATCH),
    "ssl session version mismatch"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_STILL_IN_INIT), "still in init"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV13_ALERT_CERTIFICATE_REQUIRED),
    "tlsv13 alert certificate required"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV13_ALERT_MISSING_EXTENSION),
    "tlsv13 alert missing extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_ACCESS_DENIED),
    "tlsv1 alert access denied"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_DECODE_ERROR),
    "tlsv1 alert decode error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_DECRYPTION_FAILED),
    "tlsv1 alert decryption failed"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_DECRYPT_ERROR),
    "tlsv1 alert decrypt error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_EXPORT_RESTRICTION),
    "tlsv1 alert export restriction"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_INAPPROPRIATE_FALLBACK),
    "tlsv1 alert inappropriate fallback"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_INSUFFICIENT_SECURITY),
    "tlsv1 alert insufficient security"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_INTERNAL_ERROR),
    "tlsv1 alert internal error"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_NO_RENEGOTIATION),
    "tlsv1 alert no renegotiation"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_PROTOCOL_VERSION),
    "tlsv1 alert protocol version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_RECORD_OVERFLOW),
    "tlsv1 alert record overflow"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_UNKNOWN_CA),
    "tlsv1 alert unknown ca"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_ALERT_USER_CANCELLED),
    "tlsv1 alert user cancelled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_BAD_CERTIFICATE_HASH_VALUE),
    "tlsv1 bad certificate hash value"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_BAD_CERTIFICATE_STATUS_RESPONSE),
    "tlsv1 bad certificate status response"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_CERTIFICATE_UNOBTAINABLE),
    "tlsv1 certificate unobtainable"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_UNRECOGNIZED_NAME),
    "tlsv1 unrecognized name"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLSV1_UNSUPPORTED_EXTENSION),
    "tlsv1 unsupported extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLS_HEARTBEAT_PEER_DOESNT_ACCEPT),
    "peer does not accept heartbeats"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLS_HEARTBEAT_PENDING),
    "heartbeat request already pending"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLS_ILLEGAL_EXPORTER_LABEL),
    "tls illegal exporter label"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TLS_INVALID_ECPOINTFORMAT_LIST),
    "tls invalid ecpointformat list"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TOO_MANY_KEY_UPDATES),
    "too many key updates"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TOO_MANY_WARN_ALERTS),
    "too many warn alerts"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_TOO_MUCH_EARLY_DATA),
    "too much early data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNABLE_TO_FIND_ECDH_PARAMETERS),
    "unable to find ecdh parameters"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS),
    "unable to find public key parameters"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNABLE_TO_LOAD_SSL3_MD5_ROUTINES),
    "unable to load ssl3 md5 routines"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES),
    "unable to load ssl3 sha1 routines"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNEXPECTED_CCS_MESSAGE),
    "unexpected ccs message"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNEXPECTED_END_OF_EARLY_DATA),
    "unexpected end of early data"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNEXPECTED_MESSAGE), "unexpected message"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNEXPECTED_RECORD), "unexpected record"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNINITIALIZED), "uninitialized"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_ALERT_TYPE), "unknown alert type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_CERTIFICATE_TYPE),
    "unknown certificate type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_CIPHER_RETURNED),
    "unknown cipher returned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_CIPHER_TYPE),
    "unknown cipher type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_CMD_NAME), "unknown cmd name"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_COMMAND), "unknown command"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_DIGEST), "unknown digest"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_KEY_EXCHANGE_TYPE),
    "unknown key exchange type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_PKEY_TYPE), "unknown pkey type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_PROTOCOL), "unknown protocol"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_SSL_VERSION),
    "unknown ssl version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNKNOWN_STATE), "unknown state"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSAFE_LEGACY_RENEGOTIATION_DISABLED),
    "unsafe legacy renegotiation disabled"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSOLICITED_EXTENSION),
    "unsolicited extension"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSUPPORTED_COMPRESSION_ALGORITHM),
    "unsupported compression algorithm"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSUPPORTED_ELLIPTIC_CURVE),
    "unsupported elliptic curve"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSUPPORTED_PROTOCOL),
    "unsupported protocol"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSUPPORTED_SSL_VERSION),
    "unsupported ssl version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_UNSUPPORTED_STATUS_TYPE),
    "unsupported status type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_USE_SRTP_NOT_NEGOTIATED),
    "use srtp not negotiated"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_VERSION_TOO_HIGH), "version too high"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_VERSION_TOO_LOW), "version too low"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_CERTIFICATE_TYPE),
    "wrong certificate type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_CIPHER_RETURNED),
    "wrong cipher returned"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_CURVE), "wrong curve"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_ENCRYPTION_LEVEL_RECEIVED),
    "wrong encryption level received"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_SIGNATURE_LENGTH),
    "wrong signature length"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_SIGNATURE_SIZE),
    "wrong signature size"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_SIGNATURE_TYPE),
    "wrong signature type"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_SSL_VERSION), "wrong ssl version"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_WRONG_VERSION_NUMBER),
    "wrong version number"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_X509_LIB), "x509 lib"},
    {ERR_PACK(ERR_LIB_SSL, 0, SSL_R_X509_VERIFICATION_SETUP_PROBLEMS),
    "x509 verification setup problems"},
    {0, NULL}
};

#endif

int ERR_load_SSL_strings(void)
{
#ifndef OPENSSL_NO_ERR
    if (ERR_func_error_string(SSL_str_functs[0].error) == NULL) {
        ERR_load_strings_const(SSL_str_functs);
        ERR_load_strings_const(SSL_str_reasons);
    }
#endif
    return 1;
}
