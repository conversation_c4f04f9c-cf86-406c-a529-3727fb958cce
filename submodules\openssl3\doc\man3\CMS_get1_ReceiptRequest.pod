=pod

=head1 NAME

CMS_ReceiptRequest_create0_ex, CMS_ReceiptRequest_create0,
CMS_add1_ReceiptRequest, CMS_get1_ReceiptRequest, CMS_ReceiptRequest_get0_values
- CMS signed receipt request functions

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ReceiptRequest *CMS_ReceiptRequest_create0_ex(
     unsigned char *id, int idlen, int allorfirst,
     STACK_OF(GENERAL_NAMES) *receiptList, STACK_OF(GENERAL_NAMES) *receiptsTo,
     OSSL_LIB_CTX *libctx);
 CMS_ReceiptRequest *CMS_ReceiptRequest_create0(
     unsigned char *id, int idlen, int allorfirst,
     STACK_OF(GENERAL_NAMES) *receiptList, STACK_OF(GENERAL_NAMES) *receiptsTo);
 int CMS_add1_ReceiptRequest(CMS_SignerInfo *si, CMS_ReceiptRequest *rr);
 int CMS_get1_ReceiptRequest(CMS_SignerInfo *si, CMS_ReceiptRequest **prr);
 void CMS_ReceiptRequest_get0_values(CMS_ReceiptRequest *rr, ASN1_STRING **pcid,
                                     int *pallorfirst,
                                     STACK_OF(GENERAL_NAMES) **plist,
                                     STACK_OF(GENERAL_NAMES) **prto);

=head1 DESCRIPTION

CMS_ReceiptRequest_create0_ex() creates a signed receipt request
structure. The B<signedContentIdentifier> field is set using I<id> and I<idlen>,
or it is set to 32 bytes of pseudo random data if I<id> is NULL.
If I<receiptList> is NULL the allOrFirstTier option in I<receiptsFrom> is used
and set to the value of the I<allorfirst> parameter. If I<receiptList> is not
NULL the I<receiptList> option in I<receiptsFrom> is used. The I<receiptsTo>
parameter specifies the I<receiptsTo> field value. The library context I<libctx>
is used to find the public random generator.

CMS_ReceiptRequest_create0() is similar to
CMS_ReceiptRequest_create0_ex() but uses default values of NULL for the
library context I<libctx>.

The CMS_add1_ReceiptRequest() function adds a signed receipt request B<rr>
to SignerInfo structure B<si>.

int CMS_get1_ReceiptRequest() looks for a signed receipt request in B<si>, if
any is found it is decoded and written to B<prr>.

CMS_ReceiptRequest_get0_values() retrieves the values of a receipt request.
The signedContentIdentifier is copied to B<pcid>. If the B<allOrFirstTier>
option of B<receiptsFrom> is used its value is copied to B<pallorfirst>
otherwise the B<receiptList> field is copied to B<plist>. The B<receiptsTo>
parameter is copied to B<prto>.

=head1 NOTES

For more details of the meaning of the fields see RFC2634.

The contents of a signed receipt should only be considered meaningful if the
corresponding CMS_ContentInfo structure can be successfully verified using
CMS_verify().

=head1 RETURN VALUES

CMS_ReceiptRequest_create0_ex() and CMS_ReceiptRequest_create0() return
a signed receipt request structure or NULL if an error occurred.

CMS_add1_ReceiptRequest() returns 1 for success or 0 if an error occurred.

CMS_get1_ReceiptRequest() returns 1 is a signed receipt request is found and
decoded. It returns 0 if a signed receipt request is not present and -1 if
it is present but malformed.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_sign(3)>,
L<CMS_sign_receipt(3)>, L<CMS_verify(3)>
L<CMS_verify_receipt(3)>

=head1 HISTORY

The function CMS_ReceiptRequest_create0_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2008-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
