.\" Copyright (c) 1988 Massachusetts Institute of Technology,
.\" Student Information Processing Board.  All rights reserved.
.\"
.\" util/et/com_err.3
.\"
.TH COM_ERR 3 "22 Nov 1988" SIPB
.SH NAME
com_err \- common error display routine
.SH SYNOPSIS
.nf
 #include "com_err.h"
.PP
void com_err (whoami, code, format, ...);
	const char *whoami;
	long code;
	const char *format;
.PP
proc = set_com_err_hook (proc);
.fi
void (*
.I proc
) (const char *, long, const char *, va_list);
.nf
.PP
proc = reset_com_err_hook ();
.PP
void initialize_XXXX_error_table ();
.fi
.SH DESCRIPTION
.I Com_err
displays an error message on the standard error stream
.I stderr
(see
.IR stdio (3S))
composed of the
.I whoami
string, which should specify the program name or some subportion of
a program, followed by an error message generated from the
.I code
value (derived from
.IR compile_et (1)),
and a string produced using the
.I format
string and any following arguments, in the same style as
.IR fprintf (3).

The behavior of
.I com_err
can be modified using
.I set_com_err_hook;
this defines a procedure which is called with the arguments passed to
.I com_err,
instead of the default internal procedure which sends the formatted
text to error output.  Thus the error messages from a program can all
easily be diverted to another form of diagnostic logging, such as
.IR syslog (3).
.I Reset_com_err_hook
may be used to restore the behavior of
.I com_err
to its default form.  Both procedures return the previous ``hook''
value.  These ``hook'' procedures must have the declaration given for
.I proc
above in the synopsis.

The
.I initialize_XXXX_error_table
routine is generated mechanically by
.IR compile_et (1)
from a source file containing names and associated strings.  Each
table has a name of up to four characters, which is used in place of
the
.B XXXX
in the name of the routine.  These routines should be called before
any of the corresponding error codes are used, so that the
.I com_err
library will recognize error codes from these tables when they are
used.

The
.B com_err.h
header file should be included in any source file that uses routines
from the
.I com_err
library; executable files must be linked using
.I ``-lcom_err''
in order to cause the
.I com_err
library to be included.

.\" .IR for manual entries
.\" .PP for paragraph breaks

.SH "SEE ALSO"
compile_et (1), syslog (3).

Ken Raeburn, "A Common Error Description Library for UNIX".
