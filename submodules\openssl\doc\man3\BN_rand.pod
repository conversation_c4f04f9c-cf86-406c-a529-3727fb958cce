=pod

=head1 NAME

BN_rand, BN_priv_rand, BN_pseudo_rand,
BN_rand_range, BN_priv_rand_range, BN_pseudo_rand_range
- generate pseudo-random number

=head1 SYNOPSIS

 #include <openssl/bn.h>

 int BN_rand(BIGNUM *rnd, int bits, int top, int bottom);

 int BN_priv_rand(BIGNUM *rnd, int bits, int top, int bottom);

 int BN_pseudo_rand(BIGNUM *rnd, int bits, int top, int bottom);

 int BN_rand_range(BIGNUM *rnd, BIGNUM *range);

 int BN_priv_rand_range(BIGNUM *rnd, BIGNUM *range);

 int BN_pseudo_rand_range(BIGNUM *rnd, BIGNUM *range);

=head1 DESCRIPTION

BN_rand() generates a cryptographically strong pseudo-random number of
B<bits> in length and stores it in B<rnd>.
If B<bits> is less than zero, or too small to
accommodate the requirements specified by the B<top> and B<bottom>
parameters, an error is returned.
The B<top> parameters specifies
requirements on the most significant bit of the generated number.
If it is B<BN_RAND_TOP_ANY>, there is no constraint.
If it is B<BN_RAND_TOP_ONE>, the top bit must be one.
If it is B<BN_RAND_TOP_TWO>, the two most significant bits of
the number will be set to 1, so that the product of two such random
numbers will always have 2*B<bits> length.
If B<bottom> is B<BN_RAND_BOTTOM_ODD>, the number will be odd; if it
is B<BN_RAND_BOTTOM_ANY> it can be odd or even.
If B<bits> is 1 then B<top> cannot also be B<BN_RAND_TOP_TWO>.

BN_rand_range() generates a cryptographically strong pseudo-random
number B<rnd> in the range 0 E<lt>= B<rnd> E<lt> B<range>.

BN_priv_rand() and BN_priv_rand_range() have the same semantics as
BN_rand() and BN_rand_range() respectively.  They are intended to be
used for generating values that should remain private, and mirror the
same difference between L<RAND_bytes(3)> and L<RAND_priv_bytes(3)>.

=head1 NOTES

Always check the error return value of these functions and do not take
randomness for granted: an error occurs if the CSPRNG has not been
seeded with enough randomness to ensure an unpredictable byte sequence.

=head1 RETURN VALUES

The functions return 1 on success, 0 on error.
The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<RAND_add(3)>,
L<RAND_bytes(3)>,
L<RAND_priv_bytes(3)>,
L<RAND(7)>,
L<RAND_DRBG(7)>

=head1 HISTORY

=over 2

=item *

Starting with OpenSSL release 1.1.0, BN_pseudo_rand() has been identical
to BN_rand() and BN_pseudo_rand_range() has been identical to
BN_rand_range().
The "pseudo" functions should not be used and may be deprecated in
a future release.

=item *

The
BN_priv_rand() and BN_priv_rand_range() functions were added in OpenSSL 1.1.1.

=back

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
