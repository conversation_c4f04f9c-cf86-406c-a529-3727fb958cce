# MsQuic quic_datapath模块源码分析

##  quic_datapath
quic_datapath.h是 MsQuic 的数据路径模块的核心头文件，定义了与数据路径相关的结构、枚举、宏和函数接口。数据路径模块是 MsQuic 的关键部分，他的主要功能包括：

- 数据路径抽象: 提供跨平台的网络通信支持。
- 网络通信: 支持 UDP 和 TCP 的发送和接收。
- 路由管理: 提供路由解析和更新功能。
- 事件回调: 支持异步事件处理。
- 高级功能: 支持 RSS、QTIP 和加密卸载等功能。

下面具体介绍quic_datapath提供的数据结构和接口

---

### **IPv4 和 IPv6 的头部大小**
```c
#define CXPLAT_MIN_IPV4_HEADER_SIZE 20 // IPv4 最小头部大小
#define CXPLAT_MIN_IPV6_HEADER_SIZE 40 // IPv6 最小头部大小
```

---

#### **UDP 和 TCP 的头部大小**
```c
#define CXPLAT_UDP_HEADER_SIZE 8  // UDP 头部大小
#define CXPLAT_TCP_HEADER_SIZE 20 // TCP 头部大小
```

---

#### **临时端口范围（根据 RFC 6335）**
```c
#define QUIC_ADDR_EPHEMERAL_PORT_MIN 49152 // 最小临时端口
#define QUIC_ADDR_EPHEMERAL_PORT_MAX 65535 // 最大临时端口
```

---

#### **ECN（显式拥塞通知）类型**
```c
typedef enum CXPLAT_ECN_TYPE {
    CXPLAT_ECN_NON_ECT = 0x0, // 非 ECN 传输
    CXPLAT_ECN_ECT_1   = 0x1, // ECN 能力传输，ECT(1)
    CXPLAT_ECN_ECT_0   = 0x2, // ECN 能力传输，ECT(0)
    CXPLAT_ECN_CE      = 0x3  // 拥塞遇到，CE
} CXPLAT_ECN_TYPE;
```
- 定义了不同的 ECN 类型，用于标记网络拥塞状态。

---

#### **DSCP（差分服务代码点）类型**
```c
typedef enum CXPLAT_DSCP_TYPE {
    CXPLAT_DSCP_CS0 = 0,  // 默认流量
    CXPLAT_DSCP_LE  = 1,  // 最低努力
    CXPLAT_DSCP_CS1 = 8,  // 优先级 1
    CXPLAT_DSCP_CS2 = 16, // 优先级 2
    CXPLAT_DSCP_CS3 = 24, // 优先级 3
    CXPLAT_DSCP_CS4 = 32, // 优先级 4
    CXPLAT_DSCP_CS5 = 40, // 优先级 5
    CXPLAT_DSCP_EF  = 46  // 紧急流量
} CXPLAT_DSCP_TYPE;
```
- 定义了不同的 DSCP 值，用于区分网络流量的优先级。

---

#### **数据路径的最大 MTU 和 UDP 负载大小**
```c
#define CXPLAT_MAX_MTU 1500 // 数据路径支持的最大 MTU
#define MAX_UDP_PAYLOAD_LENGTH (CXPLAT_MAX_MTU - CXPLAT_MIN_IPV4_HEADER_SIZE - CXPLAT_UDP_HEADER_SIZE)
```
- 定义了数据路径支持的最大 MTU 和最大 UDP 负载大小。

---

### MaxUdpPayloadSizeFromMTU
```c
inline
uint16_t
MaxUdpPayloadSizeFromMTU(
    _In_ uint16_t Mtu
    )
{
    return  Mtu - CXPLAT_MIN_IPV4_HEADER_SIZE - CXPLAT_UDP_HEADER_SIZE;
}
```
- 根据给定的最大传输单元（MTU）计算可以容纳的最大 UDP 负载大小。它通过减去 IPv4 和 UDP 协议头的大小，得出实际可用的 UDP 数据负载空间。

---

### MaxUdpPayloadSizeForFamily
```c
inline
uint16_t
MaxUdpPayloadSizeForFamily(
    _In_ QUIC_ADDRESS_FAMILY Family,
    _In_ uint16_t Mtu
    )
{
    return Family == QUIC_ADDRESS_FAMILY_INET ?
        Mtu - CXPLAT_MIN_IPV4_HEADER_SIZE - CXPLAT_UDP_HEADER_SIZE :
        Mtu - CXPLAT_MIN_IPV6_HEADER_SIZE - CXPLAT_UDP_HEADER_SIZE;
}
```
- 根据指定的地址族（IPv4 或 IPv6）和最大传输单元（MTU）计算可以容纳的最大 UDP 负载大小。它通过减去对应协议头（IPv4 或 IPv6）和 UDP 协议头的大小，得出实际可用的 UDP 数据负载空间。

---

### PacketSizeFromUdpPayloadSize
```c
inline
uint16_t
PacketSizeFromUdpPayloadSize(
    _In_ QUIC_ADDRESS_FAMILY Family,
    _In_ uint16_t UdpPayloadSize
    )
{
    uint32_t PayloadSize = Family == QUIC_ADDRESS_FAMILY_INET ?
        UdpPayloadSize + CXPLAT_MIN_IPV4_HEADER_SIZE + CXPLAT_UDP_HEADER_SIZE :
        UdpPayloadSize + CXPLAT_MIN_IPV6_HEADER_SIZE + CXPLAT_UDP_HEADER_SIZE;
    if (PayloadSize > UINT16_MAX) {
        PayloadSize = UINT16_MAX;
    }
    return (uint16_t)PayloadSize;
}
```
- 根据给定的 UDP 负载大小和地址族（IPv4 或 IPv6），计算完整的网络数据包大小（包括 IP 头部和 UDP 头部）。它通过将协议头的大小加到 UDP 负载大小上，得出最终的数据包大小。

---

### 来自其他文件定义的数据结构
```c
// 顶层datapath句柄类型。
typedef struct CXPLAT_DATAPATH CXPLAT_DATAPATH;
typedef struct CXPLAT_DATAPATH_RAW CXPLAT_DATAPATH_RAW;

// 表示一个 UDP 或 TCP 的抽象。
typedef struct CXPLAT_SOCKET CXPLAT_SOCKET;

// 维护“每次发送”的上下文。
typedef struct CXPLAT_SEND_DATA CXPLAT_SEND_DATA;

// 包含指针和长度。
typedef struct QUIC_BUFFER QUIC_BUFFER;
```

---

### CXPLAT_ROUTE_STATE
```c
typedef enum CXPLAT_ROUTE_STATE {
    RouteUnresolved,  // 路由未解析
    RouteResolving,   // 路由正在解析
    RouteSuspected,   // 路由可疑
    RouteResolved,    // 路由已解析
} CXPLAT_ROUTE_STATE;
```
- 表示网络路由的状态

---

### **CXPLAT_RAW_TCP_STATE**
```c
// 表示原始 TCP 状态的结构体。
typedef struct CXPLAT_RAW_TCP_STATE {
    BOOLEAN Syncd;             // 是否同步
    uint32_t AckNumber;         // 确认号（主机字节序）
    uint32_t SequenceNumber;    // 序列号（主机字节序）
} CXPLAT_RAW_TCP_STATE;
```
- `CXPLAT_RAW_TCP_STATE` 用于存储 TCP 连接的确认号和序列号等状态信息。

---

### **CXPLAT_ROUTE**
```c
typedef struct CXPLAT_ROUTE {

    // 与此路由主要关联的 (RSS) 队列。
    void* Queue;

    QUIC_ADDR RemoteAddress; // 远程地址
    QUIC_ADDR LocalAddress;  // 本地地址

    uint8_t LocalLinkLayerAddress[6];   // 本地链路层地址
    uint8_t NextHopLinkLayerAddress[6]; // 下一跳链路层地址

    uint16_t DatapathType: 15; // 数据路径类型 (CXPLAT_DATAPATH_TYPE)
    uint8_t UseQTIP: 1;        // 如果路由使用 QTIP，则为 TRUE

    // QuicCopyRouteInfo 会复制到此处的内存（不包括 State）。
    CXPLAT_ROUTE_STATE State;       // 路由状态
    CXPLAT_RAW_TCP_STATE TcpState;  // TCP 状态

} CXPLAT_ROUTE;
```
- `CXPLAT_ROUTE` 表示一个网络路由，包含路由的地址信息、链路层地址、数据路径类型以及状态信息。

---

### **CXPLAT_RECV_DATA**
```c
// 表示接收到的 UDP 数据报或 TCP 数据的结构体。
typedef struct CXPLAT_RECV_DATA {

    // 链中的下一个接收数据。
    struct CXPLAT_RECV_DATA* Next;

    // 包含网络路由。
    CXPLAT_ROUTE* Route;

    // 包含接收到的字节的数据缓冲区。
    _Field_size_(BufferLength)
    uint8_t* Buffer;

    // 缓冲区中有效数据的长度。
    uint16_t BufferLength;

    // 接收到的数据的分区 ID。
    uint16_t PartitionIndex;

    // IPv4 头的服务类型字段或 IPv6 头的流量类别字段。
    uint8_t TypeOfService;

    // 接收到的数据包的 IP 头中的 TTL 或跳数限制字段。
    uint8_t HopLimitTTL;

    // 标志。
    uint16_t Allocated : 1;          // 用于调试。释放时设置为 FALSE。
    uint16_t QueuedOnConnection : 1; // 用于调试。
    uint16_t DatapathType : 2;       // 数据路径类型 (CXPLAT_DATAPATH_TYPE)
    uint16_t Reserved : 4;           // 保留字段
    uint16_t ReservedEx : 8;         // 头部长度

    // 可变长度数据（大小为 `ClientRecvContextLength`，在 CxPlatDataPathInitialize 中传递）直接跟随。
} CXPLAT_RECV_DATA;
```
- `CXPLAT_RECV_DATA` 表示接收到的网络数据，包含数据缓冲区、路由信息和相关元

---

### 以下是QUIC Encryption Offload (QEO) 相关的数据结构定义：
---

```c
typedef enum CXPLAT_QEO_OPERATION {
    CXPLAT_QEO_OPERATION_ADD,     // 添加（或修改）一个 QUIC连接的offload
    CXPLAT_QEO_OPERATION_REMOVE,  // 移除一个 QUIC连接的offload
} CXPLAT_QEO_OPERATION;

typedef enum CXPLAT_QEO_DIRECTION {
    CXPLAT_QEO_DIRECTION_TRANSMIT, // 用于发送路径的offload
    CXPLAT_QEO_DIRECTION_RECEIVE,  // 用于接收路径的offload
} CXPLAT_QEO_DIRECTION;

typedef enum CXPLAT_QEO_DECRYPT_FAILURE_ACTION {
    CXPLAT_QEO_DECRYPT_FAILURE_ACTION_DROP,     // 解密失败时丢弃数据包
    CXPLAT_QEO_DECRYPT_FAILURE_ACTION_CONTINUE, // 解密失败时继续并将数据包上传
} CXPLAT_QEO_DECRYPT_FAILURE_ACTION;
```

### **CXPLAT_QEO_CIPHER_TYPE**
```c
typedef enum CXPLAT_QEO_CIPHER_TYPE {
    CXPLAT_QEO_CIPHER_TYPE_AEAD_AES_128_GCM,
    CXPLAT_QEO_CIPHER_TYPE_AEAD_AES_256_GCM,
    CXPLAT_QEO_CIPHER_TYPE_AEAD_CHACHA20_POLY1305,
    CXPLAT_QEO_CIPHER_TYPE_AEAD_AES_128_CCM,
} CXPLAT_QEO_CIPHER_TYPE;
```
-  `CXPLAT_QEO_CIPHER_TYPE` 枚举定义了支持的加密算法类型。

### **CXPLAT_QEO_CONNECTION**
```c
typedef struct CXPLAT_QEO_CONNECTION {
    uint32_t Operation            : 1;  // CXPLAT_QEO_OPERATION
    uint32_t Direction            : 1;  // CXPLAT_QEO_DIRECTION
    uint32_t DecryptFailureAction : 1;  // CXPLAT_QEO_DECRYPT_FAILURE_ACTION
    uint32_t KeyPhase             : 1;
    uint32_t RESERVED             : 12; // 必须设置为 0，不要读取。
    uint32_t CipherType           : 16; // CXPLAT_QEO_CIPHER_TYPE
    uint64_t NextPacketNumber;
    QUIC_ADDR Address;
    uint8_t ConnectionIdLength;
    uint8_t ConnectionId[20]; // QUIC v1 和 v2 的最大连接 ID 大小
    uint8_t PayloadKey[32];   // 长度由 CipherType 决定
    uint8_t HeaderKey[32];    // 长度由 CipherType 决定
    uint8_t PayloadIv[12];
} CXPLAT_QEO_CONNECTION;
```
- `CXPLAT_QEO_CONNECTION` 表示 QUIC 加密卸载（QEO）连接的相关信息。

以上代码片段定义了 QUIC 加密卸载（QEO）相关的枚举和结构体，包括：
1. **`CXPLAT_QEO_OPERATION`**: 定义了 QEO 的操作类型（添加或移除）。
2. **`CXPLAT_QEO_DIRECTION`**: 定义了 QEO 的方向（发送或接收）。
3. **`CXPLAT_QEO_DECRYPT_FAILURE_ACTION`**: 定义了解密失败时的处理方式。
4. **`CXPLAT_QEO_CIPHER_TYPE`**: 定义了支持的加密算法类型。
5. **`CXPLAT_QEO_CONNECTION`**: 表示 QEO 连接的详细信息，包括地址、密钥和加密算法。

这些定义为 MsQuic 提供了灵活的加密卸载支持，涵盖了连接管理、加密算法选择和错误处理等功能。

---

### 下面再来看看datapath定义的udp和tcp相关的回调函数

---

### **CXPLAT_UDP_DATAPATH_CALLBACKS**
```c
//UDP datapath回调
typedef struct CXPLAT_UDP_DATAPATH_CALLBACKS {
    CXPLAT_DATAPATH_RECEIVE_CALLBACK_HANDLER Receive;
    CXPLAT_DATAPATH_UNREACHABLE_CALLBACK_HANDLER Unreachable;
} CXPLAT_UDP_DATAPATH_CALLBACKS;
```
- `Receive`: 数据接收回调。
- `Unreachable`: 端口不可达回调。

### **CXPLAT_TCP_DATAPATH_CALLBACKS**
```c
//TCP datapath回调
typedef struct CXPLAT_TCP_DATAPATH_CALLBACKS {
    CXPLAT_DATAPATH_ACCEPT_CALLBACK_HANDLER Accept;
    CXPLAT_DATAPATH_CONNECT_CALLBACK_HANDLER Connect;
    CXPLAT_DATAPATH_RECEIVE_CALLBACK_HANDLER Receive;
    CXPLAT_DATAPATH_SEND_COMPLETE_CALLBACK_HANDLER SendComplete;
} CXPLAT_TCP_DATAPATH_CALLBACKS;
```
- `Accept`: 连接接受回调。
- `Connect`: 连接或断开连接回调。
- `Receive`: 数据接收回调。
- `SendComplete`: 发送完成回调。

---

### **CxPlatDataPathInitialize**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathInitialize(
    _In_ uint32_t ClientRecvContextLength,
    _In_opt_ const CXPLAT_UDP_DATAPATH_CALLBACKS* UdpCallbacks,
    _In_opt_ const CXPLAT_TCP_DATAPATH_CALLBACKS* TcpCallbacks,
    _In_ CXPLAT_WORKER_POOL* WorkerPool,
    _In_opt_ QUIC_EXECUTION_CONFIG* Config,
    _Out_ CXPLAT_DATAPATH** NewDataPath
    )
```
#### **参数说明**
1. **`ClientRecvContextLength`**
   - 描述: 每个接收上下文的大小。
   - 用途: 用于分配接收数据的上下文。

2. **`UdpCallbacks`**
   - 描述: UDP 数据路径的回调函数集合。
   - 用途: 配置 UDP 数据接收和不可达事件的回调。

3. **`TcpCallbacks`**
   - 描述: TCP 数据路径的回调函数集合。
   - 用途: 配置 TCP 数据接收、连接和发送完成的回调。

4. **`WorkerPool`**
   - 描述: 工作线程池。
   - 用途: 用于处理数据路径的异步操作。

5. **`Config`**
   - 描述: 数据路径的执行配置。
   - 用途: 包含特定功能（如 XDP 支持）的配置标志。

6. **`NewDataPath`**
   - 描述: 输出参数，指向新创建的 `CXPLAT_DATAPATH` 实例。
   - 用途: 返回初始化后的数据路径对象。

- 分别在datapath_xplat.c和datapath_kqueue.c中实现了此函数，前者支持windows、linux平台，后者支持darwin平台

- datapath_xplat.c中的CxPlatDataPathInitialize函数详细实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathInitialize(
    _In_ uint32_t ClientRecvContextLength,                 // 每个接收上下文的长度
    _In_opt_ const CXPLAT_UDP_DATAPATH_CALLBACKS* UdpCallbacks, // UDP 数据路径的回调函数
    _In_opt_ const CXPLAT_TCP_DATAPATH_CALLBACKS* TcpCallbacks, // TCP 数据路径的回调函数
    _In_ CXPLAT_WORKER_POOL* WorkerPool,                   // 工作线程池
    _In_opt_ QUIC_EXECUTION_CONFIG* Config,                // 执行配置（可选）
    _Out_ CXPLAT_DATAPATH** NewDataPath                    // 输出参数，返回数据路径句柄
    )
{
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;

    // 检查输出参数是否为空。
    if (NewDataPath == NULL) {
        Status = QUIC_STATUS_INVALID_PARAMETER;
        goto Error;
    }

    // 初始化基础数据路径。
    Status =
        DataPathInitialize(
            ClientRecvContextLength,
            UdpCallbacks,
            TcpCallbacks,
            WorkerPool,
            Config,
            NewDataPath);
    if (QUIC_FAILED(Status)) {
        // 如果初始化失败，记录错误并退出。
        QuicTraceLogVerbose(
            DatapathInitFail,
            "[  dp] Failed to initialize datapath, status:%d", Status);
        goto Error;
    }

    // 如果配置启用了 XDP（eXpress Data Path），初始化原始数据路径。
    if (Config && Config->Flags & QUIC_EXECUTION_CONFIG_FLAG_XDP) {
        Status =
            RawDataPathInitialize(
                ClientRecvContextLength,
                Config,
                (*NewDataPath),
                WorkerPool,
                &((*NewDataPath)->RawDataPath));
        if (QUIC_FAILED(Status)) {
            // 如果原始数据路径初始化失败，记录错误并清理资源。
            QuicTraceLogVerbose(
                RawDatapathInitFail,
                "[ raw] Failed to initialize raw datapath, status:%d", Status);
            (*NewDataPath)->RawDataPath = NULL;
            CxPlatDataPathUninitialize(*NewDataPath);
            *NewDataPath = NULL;
        }
    }

Error:

    // 返回初始化状态。
    return Status;
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{NewDataPath 是否为空}:::decision
    B -->|是| C(设置状态为无效参数):::process
    B -->|否| D(调用 DataPathInitialize):::process
    C --> E(跳转至错误处理):::process
    D --> F{DataPathInitialize 是否失败}:::decision
    F -->|是| G(记录初始化失败日志):::process
    F -->|否| H{Config 是否存在且包含 XDP 标志}:::decision
    G --> E
    H -->|是| I(调用 RawDataPathInitialize):::process
    H -->|否| J(返回成功状态):::process
    I --> K{RawDataPathInitialize 是否失败}:::decision
    K -->|是| L(记录原始数据路径初始化失败日志):::process
    K -->|否| J
    L --> M(将 RawDataPath 置为 NULL):::process
    M --> N(调用 CxPlatDataPathUninitialize):::process
    N --> O(将 NewDataPath 置为 NULL):::process
    O --> E
    E --> P(返回状态):::process
    P --> Q([结束]):::startend
```

---

- datapath_kqueue.c中的CxPlatDataPathInitialize函数详细实现
```c
QUIC_STATUS
CxPlatDataPathInitialize(
    _In_ uint32_t ClientRecvDataLength,                 // 每个接收上下文的长度
    _In_opt_ const CXPLAT_UDP_DATAPATH_CALLBACKS* UdpCallbacks, // UDP 数据路径的回调函数
    _In_opt_ const CXPLAT_TCP_DATAPATH_CALLBACKS* TcpCallbacks, // TCP 数据路径的回调函数
    _In_ CXPLAT_WORKER_POOL* WorkerPool,               // 工作线程池
    _In_opt_ QUIC_EXECUTION_CONFIG* Config,            // 执行配置（可选）
    _Out_ CXPLAT_DATAPATH** NewDataPath                // 输出参数，返回数据路径句柄
    )
{
    UNREFERENCED_PARAMETER(TcpCallbacks); // 当前未支持 TCP 功能

    // 检查输出参数是否为空
    if (NewDataPath == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    // 检查 UDP 回调函数是否有效
    if (UdpCallbacks != NULL) {
        if (UdpCallbacks->Receive == NULL || UdpCallbacks->Unreachable == NULL) {
            return QUIC_STATUS_INVALID_PARAMETER;
        }
    }

    // 检查工作线程池是否为空
    if (WorkerPool == NULL) {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    // 启动工作线程池
    if (!CxPlatWorkerPoolLazyStart(WorkerPool, Config)) {
        return QUIC_STATUS_OUT_OF_MEMORY;
    }

    // 确定分区数量
    uint32_t PartitionCount;
    if (Config && Config->ProcessorCount) {
        PartitionCount = Config->ProcessorCount;
    } else {
        PartitionCount = CxPlatProcCount();
    }

    // 计算数据路径对象的大小
    const size_t DatapathLength =
        sizeof(CXPLAT_DATAPATH) + PartitionCount * sizeof(CXPLAT_DATAPATH_PARTITION);

    // 分配数据路径对象
    CXPLAT_DATAPATH* Datapath = (CXPLAT_DATAPATH*)CXPLAT_ALLOC_PAGED(DatapathLength, QUIC_POOL_DATAPATH);
    if (Datapath == NULL) {
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "CXPLAT_DATAPATH",
            DatapathLength);
        return QUIC_STATUS_OUT_OF_MEMORY;
    }

    // 初始化数据路径对象
    CxPlatZeroMemory(Datapath, DatapathLength);
    if (UdpCallbacks) {
        Datapath->UdpHandlers = *UdpCallbacks;
    }
    Datapath->WorkerPool = WorkerPool;
    Datapath->PartitionCount = 1; // Darwin 仅支持单个接收器
    CxPlatRefInitializeEx(&Datapath->RefCount, Datapath->PartitionCount);

    // 初始化每个处理器的分区上下文
    for (uint32_t i = 0; i < Datapath->PartitionCount; i++) {
        CxPlatProcessorContextInitialize(
            Datapath,
            i,
            ClientRecvDataLength,
            &Datapath->Partitions[i]);
    }

    // 增加工作线程池的引用计数
    CXPLAT_FRE_ASSERT(CxPlatRundownAcquire(&WorkerPool->Rundown));
    *NewDataPath = Datapath;

    return QUIC_STATUS_SUCCESS;
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{NewDataPath 是否为空}:::decision
    B -->|是| C(返回无效参数错误):::process
    B -->|否| D{UdpCallbacks 是否为空}:::decision
    D -->|否| E{UdpCallbacks 的 Receive 和 Unreachable 是否为空}:::decision
    E -->|是| C
    D -->|是| F{WorkerPool 是否为空}:::decision
    E -->|否| F
    F -->|是| C
    F -->|否| G{WorkerPool 懒启动是否成功}:::decision
    G -->|否| H(返回内存不足错误):::process
    G -->|是| I{Config 是否存在且 ProcessorCount 不为 0}:::decision
    I -->|是| J(分区数设为 Config 的 ProcessorCount):::process
    I -->|否| K(分区数设为系统处理器数量):::process
    J --> L(计算数据路径结构体长度):::process
    K --> L
    L --> M(分配数据路径内存):::process
    M --> N{内存分配是否成功}:::decision
    N -->|否| O(记录分配失败日志并返回内存不足错误):::process
    N -->|是| P(初始化数据路径结构体):::process
    P --> Q(初始化每个分区上下文):::process
    Q --> R(获取 WorkerPool 的 Rundown):::process
    R --> S(设置 NewDataPath 并返回成功):::process
    C --> T([结束]):::startend
    H --> T
    O --> T
    S --> T
```

---

### **`CxPlatDataPathUninitialize` **
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathUninitialize(
    _In_ CXPLAT_DATAPATH* Datapath
    );
```
- 清理和释放 MsQuic 数据路径（Datapath）相关的资源。

- datapath_xplat.c中的CxPlatDataPathUninitialize函数详细实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathUninitialize(
    _In_ CXPLAT_DATAPATH* Datapath // 数据路径对象
    )
{
    // 检查是否存在原始数据路径（RawDataPath）。
    if (Datapath->RawDataPath) {
        // 如果存在，调用 RawDataPathUninitialize 清理原始数据路径资源。
        RawDataPathUninitialize(Datapath->RawDataPath);
    }

    // 调用 DataPathUninitialize 清理基础数据路径资源。
    DataPathUninitialize(Datapath);
}
```

- datapath_kqueue.c中的CxPlatDataPathUninitialize函数详细实现
```c
void
CxPlatDataPathUninitialize(
    _In_ CXPLAT_DATAPATH* Datapath // 数据路径对象
    )
{
    // 检查传入的 Datapath 是否为空。
    if (Datapath != NULL) {
#if DEBUG
        // 在调试模式下，确保数据路径未被标记为未初始化。
        CXPLAT_DBG_ASSERT(!Datapath->Uninitialized);
        Datapath->Uninitialized = TRUE; // 标记为已卸载。
#endif

        // 获取数据路径的分区数量。
        const uint16_t PartitionCount = Datapath->PartitionCount;

        // 遍历每个分区并释放其资源。
        for (uint32_t i = 0; i < PartitionCount; i++) {
            CxPlatProcessorContextRelease(&Datapath->Partitions[i]);
        }
    }
}
```

---

### **`CxPlatDataPathUpdateConfig` 函数**
```c
//
// 更新数据路径的执行配置。
//
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathUpdateConfig(
    _In_ CXPLAT_DATAPATH* Datapath, // 数据路径实例
    _In_ QUIC_EXECUTION_CONFIG* Config // 新的执行配置
    );
```
- 更新datapath的执行配置，允许动态调整data的行为。
- QUIC_EXECUTION_CONFIG结构
```c
// 用于 QUIC 线程执行的自定义配置。
typedef struct QUIC_EXECUTION_CONFIG {
    // 执行配置标志，用于指定线程执行的各种选项。
    QUIC_EXECUTION_CONFIG_FLAGS Flags;
    // 轮询空闲超时时间。
    uint32_t PollingIdleTimeoutUs;
    // 要使用的处理器数量。
    uint32_t ProcessorCount;
    // 用于线程的处理器列表，数组大小为 ProcessorCount。
    _Field_size_(ProcessorCount)
    uint16_t ProcessorList[1];
} QUIC_EXECUTION_CONFIG;
```

```c
typedef enum QUIC_EXECUTION_CONFIG_FLAGS {
    QUIC_EXECUTION_CONFIG_FLAG_NONE             = 0x0000,
#ifdef QUIC_API_ENABLE_PREVIEW_FEATURES
    QUIC_EXECUTION_CONFIG_FLAG_RIO              = 0x0002,
    QUIC_EXECUTION_CONFIG_FLAG_XDP              = 0x0004,
    QUIC_EXECUTION_CONFIG_FLAG_NO_IDEAL_PROC    = 0x0008,
    QUIC_EXECUTION_CONFIG_FLAG_HIGH_PRIORITY    = 0x0010,
    QUIC_EXECUTION_CONFIG_FLAG_AFFINITIZE       = 0x0020,
#endif
} QUIC_EXECUTION_CONFIG_FLAGS;
```
- 部分对应worker以下设置
```c
typedef enum CXPLAT_THREAD_FLAGS {
    CXPLAT_THREAD_FLAG_NONE               = 0x0000,
    CXPLAT_THREAD_FLAG_SET_IDEAL_PROC     = 0x0001,
    CXPLAT_THREAD_FLAG_SET_AFFINITIZE     = 0x0002,
    CXPLAT_THREAD_FLAG_HIGH_PRIORITY      = 0x0004
} CXPLAT_THREAD_FLAGS;
```

- datapath_xplat.c中的CxPlatDataPathUpdateConfig函数详细实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathUpdateConfig(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_ QUIC_EXECUTION_CONFIG* Config
    )
{
    DataPathUpdateConfig(Datapath, Config);
    if (Datapath->RawDataPath) {
        RawDataPathUpdateConfig(Datapath->RawDataPath, Config);
    }
}
```
DataPathUpdateConfig是空函数，没实现具体功能
RawDataPathUpdateConfig在xdp有实现

- datapath_kqueue.c中的CxPlatDataPathUpdateConfig函数详细实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathUpdateConfig(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_ QUIC_EXECUTION_CONFIG* Config
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    UNREFERENCED_PARAMETER(Config);
}
```

---

### **datapath功能标志**
```c
#define CXPLAT_DATAPATH_FEATURE_RECV_SIDE_SCALING     0x0001 // 接收端缩放（RSS）
#define CXPLAT_DATAPATH_FEATURE_RECV_COALESCING       0x0002 // 数据包合并
#define CXPLAT_DATAPATH_FEATURE_SEND_SEGMENTATION     0x0004 // 发送分段
#define CXPLAT_DATAPATH_FEATURE_LOCAL_PORT_SHARING    0x0008 // 本地端口共享
#define CXPLAT_DATAPATH_FEATURE_PORT_RESERVATIONS     0x0010 // 端口保留
#define CXPLAT_DATAPATH_FEATURE_TCP                   0x0020 // TCP 支持
#define CXPLAT_DATAPATH_FEATURE_RAW                   0x0040 // 原始数据路径支持
#define CXPLAT_DATAPATH_FEATURE_TTL                   0x0080 // TTL 支持
#define CXPLAT_DATAPATH_FEATURE_SEND_DSCP             0x0100 // 发送 DSCP 支持
```
- 定义了数据路径支持的功能标志，用于配置和查询数据路径的功能。
- CxPlatDataPathGetSupportedFeatures函数返回值中使用

`CXPLAT_DATAPATH_FEATURE_RECV_SIDE_SCALING`
- RSS（Receive Side Scaling）是一种网络性能优化技术，它允许网络适配器将传入的网络数据包分发到多个 CPU 核心，从而提高系统的网络处理能力。

`CXPLAT_DATAPATH_FEATURE_RECV_COALESCING`
- 对应UDP分段的功能

`CXPLAT_DATAPATH_FEATURE_SEND_SEGMENTATION`
- UDP发送分段,减少系统调用

`CXPLAT_DATAPATH_FEATURE_TTL`
-控制数据包的生存时间（Time To Live, TTL）

`CXPLAT_DATAPATH_FEATURE_SEND_DSCP`
- 用来设置报文优先级

---

### **`CxPlatDataPathGetSupportedFeatures` **
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
uint32_t
CxPlatDataPathGetSupportedFeatures(
    _In_ CXPLAT_DATAPATH* Datapath // 数据路径实例
    );
```
- 返回当前数据路径支持的功能标志。

- datapath_xplat.c中的CxPlatDataPathGetSupportedFeatures函数详细实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
uint32_t
CxPlatDataPathGetSupportedFeatures(
    _In_ CXPLAT_DATAPATH* Datapath
    )
{
    if (Datapath->RawDataPath) {
        return DataPathGetSupportedFeatures(Datapath) |
               RawDataPathGetSupportedFeatures(Datapath->RawDataPath);
    }
    return DataPathGetSupportedFeatures(Datapath);
}
```

- datapath_kqueue.c中的CxPlatDataPathGetSupportedFeatures函数详细实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
uint32_t
CxPlatDataPathGetSupportedFeatures(
    _In_ CXPLAT_DATAPATH* Datapath
    )
{
    return Datapath->Features;
}
```

---

### **`CxPlatDataPathIsPaddingPreferred`**
```c
BOOLEAN
CxPlatDataPathIsPaddingPreferred(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_ CXPLAT_SEND_DATA* SendData
    )
{
    UNREFERENCED_PARAMETER(SendData);
    return !!(Datapath->Features & CXPLAT_DATAPATH_FEATURE_SEND_SEGMENTATION);
}
```
- 查询datapath是否支持CXPLAT_DATAPATH_FEATURE_SEND_SEGMENTATION特性。

---

### **`CxPlatDataPathResolveAddress` 函数**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathResolveAddress(
    _In_ CXPLAT_DATAPATH* Datapath, // 数据路径实例
    _In_z_ const char* HostName, // 主机名
    _Inout_ QUIC_ADDR* Address // 输出解析后的地址
    );
```
- 将主机名解析为 IP 地址。

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathResolveAddress(
    _In_ CXPLAT_DATAPATH* Datapath,  // 数据路径对象
    _In_z_ const char* HostName,     // 主机名（字符串形式）
    _Inout_ QUIC_ADDR* Address       // 输出参数，用于存储解析后的地址
    )
{
    QUIC_STATUS Status;
    PWSTR HostNameW = NULL;          // 用于存储宽字符形式的主机名
    ADDRINFOW Hints = { 0 };         // 地址信息查询的提示结构
    ADDRINFOW* Ai;

    // 将主机名从 UTF-8 转换为宽字符形式
    Status =
        CxPlatUtf8ToWideChar(
            HostName,
            QUIC_POOL_PLATFORM_TMP_ALLOC,
            &HostNameW);
    if (QUIC_FAILED(Status)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "Convert HostName to unicode");
        goto Exit;
    }

    // 使用输入地址的地址族作为提示
    Hints.ai_family = Address->si_family;

    // 尝试将主机名解析为数字地址
    Hints.ai_flags = AI_NUMERICHOST;
    if (GetAddrInfoW(HostNameW, NULL, &Hints, &Ai) == 0) {
        CxPlatDataPathPopulateTargetAddress((ADDRESS_FAMILY)Hints.ai_family, Ai, Address);
        FreeAddrInfoW(Ai);
        Status = QUIC_STATUS_SUCCESS;
        goto Exit;
    }

    // 如果主机名不是数字地址，则尝试解析为规范主机名
    Hints.ai_flags = AI_CANONNAME;
    if (GetAddrInfoW(HostNameW, NULL, &Hints, &Ai) == 0) {
        CxPlatDataPathPopulateTargetAddress((ADDRESS_FAMILY)Hints.ai_family, Ai, Address);
        FreeAddrInfoW(Ai);
        Status = QUIC_STATUS_SUCCESS;
        goto Exit;
    }

    // 如果解析失败，记录错误并返回状态
    QuicTraceEvent(
        LibraryError,
        "[ lib] ERROR, %s.",
        "Resolving hostname to IP");
    QuicTraceLogError(
        DatapathResolveHostNameFailed,
        "[%p] Couldn't resolve hostname '%s' to an IP address",
        Datapath,
        HostName);
    Status = HRESULT_FROM_WIN32(WSAHOST_NOT_FOUND);

Exit:

    // 释放分配的宽字符主机名内存
    if (HostNameW != NULL) {
        CXPLAT_FREE(HostNameW, QUIC_POOL_PLATFORM_TMP_ALLOC);
    }

    return Status;
}
```

- Windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathResolveAddress(
    _In_ CXPLAT_DATAPATH* Datapath,  // 数据路径对象
    _In_z_ const char* HostName,     // 主机名（字符串形式）
    _Inout_ QUIC_ADDR* Address       // 输出参数，用于存储解析后的地址
    )
{
    QUIC_STATUS Status = STATUS_SUCCESS;
    UNICODE_STRING UniHostName = { 0 }; // 用于存储 Unicode 格式的主机名
    ADDRINFOEXW Hints = { 0 };          // 地址解析的提示信息
    ADDRINFOEXW* Ai = NULL;             // 地址信息结果

    // 检查主机名长度是否超过限制
    size_t HostNameLength = strnlen(HostName, 1024);
    if (HostNameLength >= 1024) {
        Status = QUIC_STATUS_INVALID_PARAMETER;
        goto Error;
    }

    // 分配用于存储 Unicode 主机名的缓冲区
    UniHostName.MaximumLength = (USHORT)(sizeof(WCHAR) * HostNameLength);
    UniHostName.Buffer = CXPLAT_ALLOC_PAGED(UniHostName.MaximumLength, QUIC_POOL_PLATFORM_TMP_ALLOC);
    if (UniHostName.Buffer == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "Unicode Hostname",
            UniHostName.MaximumLength);
        goto Error;
    }

    // 将主机名从 UTF-8 转换为 Unicode 格式
    ULONG UniHostNameLength = 0;
    Status =
        RtlUTF8ToUnicodeN(
            UniHostName.Buffer,
            UniHostName.MaximumLength,
            &UniHostNameLength,
            HostName,
            (ULONG)HostNameLength);
    if (QUIC_FAILED(Status)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "Convert hostname to unicode");
        goto Error;
    }
    UniHostName.Length = (USHORT)UniHostNameLength;

    // 尝试将主机名解析为数字地址
    Hints.ai_family = Address->si_family; // 使用输入地址的地址族作为提示
    Hints.ai_flags = AI_NUMERICHOST;
    Status =
        CxPlatDataPathResolveAddressWithHint(
            Datapath,
            &UniHostName,
            &Hints,
            &Ai);
    if (NT_SUCCESS(Status)) {
        memcpy(Address, Ai->ai_addr, Ai->ai_addrlen);
        goto Error;
    }

    // 尝试将主机名解析为规范主机名
    Hints.ai_flags = AI_CANONNAME;
    Status =
        CxPlatDataPathResolveAddressWithHint(
            Datapath,
            &UniHostName,
            &Hints,
            &Ai);
    if (NT_SUCCESS(Status)) {
        memcpy(Address, Ai->ai_addr, Ai->ai_addrlen);
        goto Error;
    }

    // 如果解析失败，记录错误并返回状态
    QuicTraceEvent(
        LibraryError,
        "[ lib] ERROR, %s.",
        "Resolving hostname to IP");
    QuicTraceLogError(
        DatapathResolveHostNameFailed,
        "[%p] Couldn't resolve hostname '%s' to an IP address",
        Datapath,
        HostName);
    Status = STATUS_NOT_FOUND;

Error:

    // 释放地址信息结果
    if (Ai != NULL) {
        Datapath->WskProviderNpi.Dispatch->WskFreeAddressInfo(
            Datapath->WskProviderNpi.Client,
            Ai);
    }

    // 释放分配的 Unicode 主机名缓冲区
    if (UniHostName.Buffer != NULL) {
        CXPLAT_FREE(UniHostName.Buffer, QUIC_POOL_PLATFORM_TMP_ALLOC);
    }

    return Status;
}
```

- unix
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathResolveAddress(
    _In_ CXPLAT_DATAPATH* Datapath,  // 数据路径对象
    _In_z_ const char* HostName,     // 主机名（字符串形式）
    _Inout_ QUIC_ADDR* Address       // 输出参数，用于存储解析后的地址
    )
{
    UNREFERENCED_PARAMETER(Datapath); // 数据路径对象未使用
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS; // 初始化返回状态
    ADDRINFO Hints = {0};           // 地址解析的提示信息
    ADDRINFO* AddrInfo = NULL;      // 地址信息结果
    int Result = 0;                 // getaddrinfo 的返回值

    //
    // 使用输入地址的地址族作为提示。可能是未指定的地址族。
    //
    Hints.ai_family = Address->Ip.sa_family;
    if (Hints.ai_family == QUIC_ADDRESS_FAMILY_INET6) {
        Hints.ai_family = AF_INET6; // 将 MsQuic 的地址族转换为系统的地址族
    }

    //
    // 尝试将主机名解析为数字地址。
    //
    Hints.ai_flags = AI_NUMERICHOST; // 指定解析为数字地址
    Result = getaddrinfo(HostName, NULL, &Hints, &AddrInfo);
    if (Result == 0) {
        // 如果解析成功，将结果填充到 Address 中
        CxPlatDataPathPopulateTargetAddress(Hints.ai_family, AddrInfo, Address);
        freeaddrinfo(AddrInfo); // 释放地址信息结果
        AddrInfo = NULL;
        goto Exit; // 解析成功，退出函数
    }

    //
    // 尝试将主机名解析为规范主机名。
    //
    Hints.ai_flags = AI_CANONNAME; // 指定解析为规范主机名
    Result = getaddrinfo(HostName, NULL, &Hints, &AddrInfo);
    if (Result == 0) {
        // 如果解析成功，将结果填充到 Address 中
        CxPlatDataPathPopulateTargetAddress(Hints.ai_family, AddrInfo, Address);
        freeaddrinfo(AddrInfo); // 释放地址信息结果
        AddrInfo = NULL;
        goto Exit; // 解析成功，退出函数
    }

    //
    // 如果解析失败，记录错误并返回状态。
    //
    QuicTraceEvent(
        LibraryErrorStatus,
        "[ lib] ERROR, %u, %s.",
        (uint32_t)Result,
        "Resolving hostname to IP");
    QuicTraceLogError(
        DatapathResolveHostNameFailed,
        "[%p] Couldn't resolve hostname '%s' to an IP address",
        Datapath,
        HostName);
    Status = (QUIC_STATUS)Result; // 将错误代码转换为 QUIC_STATUS

Exit:

    return Status; // 返回解析状态
}
```

---

### **`CXPLAT_OPERATION_STATUS` 枚举**
```c
//
// 来自 RFC 2863 的状态值。
//
typedef enum CXPLAT_OPERATION_STATUS {
    CXPLAT_OPERATION_STATUS_UP = 1,          // 接口已启用
    CXPLAT_OPERATION_STATUS_DOWN,            // 接口已禁用
    CXPLAT_OPERATION_STATUS_TESTING,         // 接口正在测试
    CXPLAT_OPERATION_STATUS_UNKNOWN,         // 接口状态未知
    CXPLAT_OPERATION_STATUS_DORMANT,         // 接口处于休眠状态
    CXPLAT_OPERATION_STATUS_NOT_PRESENT,     // 接口不存在
    CXPLAT_OPERATION_STATUS_LOWER_LAYER_DOWN // 下层接口已禁用
} CXPLAT_OPERATION_STATUS;
```

---

### **`CXPLAT_ADAPTER_ADDRESS` 结构体**
```c
typedef struct CXPLAT_ADAPTER_ADDRESS {
    QUIC_ADDR Address;                     // 接口的 IP 地址
    uint32_t InterfaceIndex;               // 接口索引
    uint16_t InterfaceType;                // 接口类型
    CXPLAT_OPERATION_STATUS OperationStatus; // 接口操作状态
} CXPLAT_ADAPTER_ADDRESS;
```
- 表示网络适配器的地址信息。

---

### **`CXPLAT_IF_TYPE_SOFTWARE_LOOPBACK` 宏**
```c
#define CXPLAT_IF_TYPE_SOFTWARE_LOOPBACK 24
```
- 定义软件回环接口的类型值。

---

### **`CxPlatDataPathGetLocalAddresses`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetLocalAddresses(
    _In_ CXPLAT_DATAPATH* Datapath, // 数据路径实例
    _Outptr_ _At_(*Addresses, __drv_allocatesMem(Mem))
        CXPLAT_ADAPTER_ADDRESS** Addresses, // 输出本地地址列表
    _Out_ uint32_t* AddressesCount          // 输出地址数量
    );
```
- 获取本地网络适配器的 IP 地址列表。
- 仅支持windows平台,其他平台返回QUIC_STATUS_NOT_SUPPORTED

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetLocalAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,                      // 数据路径对象
    _Outptr_ _At_(*Addresses, __drv_allocatesMem(Mem))
        CXPLAT_ADAPTER_ADDRESS** Addresses,             // 输出参数，存储本地地址列表
    _Out_ uint32_t* AddressesCount                      // 输出参数，存储本地地址的数量
    )
{
    const ULONG Flags =
        GAA_FLAG_INCLUDE_ALL_INTERFACES |               // 包括所有接口
        GAA_FLAG_SKIP_ANYCAST |                         // 跳过任播地址
        GAA_FLAG_SKIP_MULTICAST |                       // 跳过多播地址
        GAA_FLAG_SKIP_DNS_SERVER |                      // 跳过 DNS 服务器地址
        GAA_FLAG_SKIP_FRIENDLY_NAME |                   // 跳过友好名称
        GAA_FLAG_SKIP_DNS_INFO;                         // 跳过 DNS 信息

    UNREFERENCED_PARAMETER(Datapath);

    ULONG AdapterAddressesSize = 0;
    PIP_ADAPTER_ADDRESSES AdapterAddresses = NULL;
    uint32_t Index = 0;

    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;
    ULONG Error;

    // 循环尝试获取适配器地址信息
    do {
        Error =
            GetAdaptersAddresses(
                AF_UNSPEC,                              // 获取 IPv4 和 IPv6 地址
                Flags,                                  // 使用指定的标志
                NULL,                                   // 没有用户数据
                AdapterAddresses,                       // 适配器地址结构
                &AdapterAddressesSize);                 // 适配器地址结构的大小
        if (Error == ERROR_BUFFER_OVERFLOW) {
            if (AdapterAddresses) {
                CXPLAT_FREE(AdapterAddresses, QUIC_POOL_DATAPATH_ADDRESSES);
            }
            AdapterAddresses = CXPLAT_ALLOC_NONPAGED(AdapterAddressesSize, QUIC_POOL_DATAPATH_ADDRESSES);
            if (!AdapterAddresses) {
                Error = ERROR_NOT_ENOUGH_MEMORY;
                QuicTraceEvent(
                    AllocFailure,
                    "Allocation of '%s' failed. (%llu bytes)",
                    "PIP_ADAPTER_ADDRESSES",
                    AdapterAddressesSize);
            }
        }
    } while (Error == ERROR_BUFFER_OVERFLOW);

    if (Error != ERROR_SUCCESS) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Error,
            "GetAdaptersAddresses");
        Status = HRESULT_FROM_WIN32(Error);
        goto Exit;
    }

    // 遍历适配器地址，统计本地单播地址的数量
    for (PIP_ADAPTER_ADDRESSES Iter = AdapterAddresses; Iter != NULL; Iter = Iter->Next) {
        for (PIP_ADAPTER_UNICAST_ADDRESS_LH Iter2 = Iter->FirstUnicastAddress; Iter2 != NULL; Iter2 = Iter2->Next) {
            Index++;
        }
    }

    if (Index == 0) {
        QuicTraceEvent(
            LibraryError,
            "[ lib] ERROR, %s.",
            "No local unicast addresses found");
        Status = QUIC_STATUS_NOT_FOUND;
        goto Exit;
    }

    // 分配内存以存储本地地址列表
    *Addresses = CXPLAT_ALLOC_NONPAGED(Index * sizeof(CXPLAT_ADAPTER_ADDRESS), QUIC_POOL_DATAPATH_ADDRESSES);
    if (*Addresses == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "Addresses",
            Index * sizeof(CXPLAT_ADAPTER_ADDRESS));
        goto Exit;
    }

    CxPlatZeroMemory(*Addresses, Index * sizeof(CXPLAT_ADAPTER_ADDRESS));
    *AddressesCount = Index;
    Index = 0;

    // 填充本地地址列表
    for (PIP_ADAPTER_ADDRESSES Iter = AdapterAddresses; Iter != NULL; Iter = Iter->Next) {
        for (PIP_ADAPTER_UNICAST_ADDRESS_LH Iter2 = Iter->FirstUnicastAddress; Iter2 != NULL; Iter2 = Iter2->Next) {
            CxPlatCopyMemory(
                &(*Addresses)[Index].Address,
                Iter2->Address.lpSockaddr,
                sizeof(QUIC_ADDR));
            (*Addresses)[Index].InterfaceIndex =
                Iter2->Address.lpSockaddr->sa_family == AF_INET ?
                    (uint32_t)Iter->IfIndex : (uint32_t)Iter->Ipv6IfIndex;
            (*Addresses)[Index].InterfaceType = (uint16_t)Iter->IfType;
            (*Addresses)[Index].OperationStatus = (CXPLAT_OPERATION_STATUS)Iter->OperStatus;
            Index++;
        }
    }

Exit:

    // 释放适配器地址结构的内存
    if (AdapterAddresses) {
        CXPLAT_FREE(AdapterAddresses, QUIC_POOL_DATAPATH_ADDRESSES);
    }

    return Status;
}
```

- Windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetLocalAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,                      // 数据路径对象
    _Outptr_ _At_(*Addresses, __drv_allocatesMem(Mem))
        CXPLAT_ADAPTER_ADDRESS** Addresses,             // 输出参数，存储本地地址列表
    _Out_ uint32_t* AddressesCount                      // 输出参数，存储本地地址的数量
    )
{
    UNREFERENCED_PARAMETER(Datapath); // 数据路径对象未使用

    MIB_IPINTERFACE_TABLE* InterfaceTable = NULL;       // 存储接口表
    MIB_UNICASTIPADDRESS_TABLE* AddressTable = NULL;    // 存储单播地址表

    QUIC_STATUS Status = GetIpInterfaceTable(AF_UNSPEC, &InterfaceTable);
    if (QUIC_FAILED(Status)) {
        // 如果获取接口表失败，记录错误并返回状态
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "GetIpInterfaceTable");
        goto Error;
    }

    Status = GetUnicastIpAddressTable(AF_UNSPEC, &AddressTable);
    if (QUIC_FAILED(Status)) {
        // 如果获取单播地址表失败，记录错误并返回状态
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "GetUnicastIpAddressTable");
        goto Error;
    }

    // 分配内存以存储本地地址列表
    *Addresses = CXPLAT_ALLOC_NONPAGED(AddressTable->NumEntries * sizeof(CXPLAT_ADAPTER_ADDRESS), QUIC_POOL_DATAPATH_ADDRESSES);
    if (*Addresses == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "Addresses",
            AddressTable->NumEntries * sizeof(CXPLAT_ADAPTER_ADDRESS));
        goto Error;
    }
    *AddressesCount = (uint32_t)AddressTable->NumEntries;

    // 遍历单播地址表并填充本地地址列表
    for (ULONG i = 0; i < AddressTable->NumEntries; ++i) {
        MIB_IPINTERFACE_ROW* Interface = NULL;
        for (ULONG j = 0; j < InterfaceTable->NumEntries; ++j) {
            if (InterfaceTable->Table[j].InterfaceIndex == AddressTable->Table[i].InterfaceIndex) {
                Interface = &InterfaceTable->Table[j];
                break;
            }
        }

        CXPLAT_ADAPTER_ADDRESS* AdapterAddress = &(*Addresses)[i];
        memcpy(&AdapterAddress->Address, &AddressTable->Table[i].Address, sizeof(QUIC_ADDR));
        AdapterAddress->InterfaceIndex = (uint32_t)AddressTable->Table[i].InterfaceIndex;
        AdapterAddress->InterfaceType = (uint16_t)AddressTable->Table[i].InterfaceLuid.Info.IfType;
        AdapterAddress->OperationStatus = Interface && Interface->Connected ? CXPLAT_OPERATION_STATUS_UP : CXPLAT_OPERATION_STATUS_DOWN;
    }

Error:

    // 释放单播地址表的内存
    if (AddressTable) {
        FreeMibTable(AddressTable);
    }

    // 释放接口表的内存
    if (InterfaceTable) {
        FreeMibTable(InterfaceTable);
    }

    return Status;
}
```

- unix
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetLocalAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,
    _Outptr_ _At_(*Addresses, __drv_allocatesMem(Mem))
        CXPLAT_ADAPTER_ADDRESS** Addresses,
    _Out_ uint32_t* AddressesCount
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    *Addresses = NULL;
    *AddressesCount = 0;
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

---

### **`CxPlatDataPathGetGatewayAddresses`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetGatewayAddresses(
    _In_ CXPLAT_DATAPATH* Datapath, // 数据路径实例
    _Outptr_ _At_(*GatewayAddresses, __drv_allocatesMem(Mem))
        QUIC_ADDR** GatewayAddresses, // 输出网关地址列表
    _Out_ uint32_t* GatewayAddressesCount // 输出网关地址数量
    );
```
- 获取当前网络环境中可用的网关服务器地址列表。
- 仅支持Windows用户态，其他平台返回QUIC_STATUS_NOT_SUPPORTED

-Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetGatewayAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,
    _Outptr_ _At_(*GatewayAddresses, __drv_allocatesMem(Mem))
        QUIC_ADDR** GatewayAddresses,
    _Out_ uint32_t* GatewayAddressesCount
    )
{
    const ULONG Flags =
        GAA_FLAG_INCLUDE_GATEWAYS |                     // 包括网关地址
        GAA_FLAG_INCLUDE_ALL_INTERFACES |               // 包括所有接口
        GAA_FLAG_SKIP_DNS_SERVER |                      // 跳过 DNS 服务器地址
        GAA_FLAG_SKIP_MULTICAST;                        // 跳过多播地址

    UNREFERENCED_PARAMETER(Datapath);

    ULONG AdapterAddressesSize = 0;
    PIP_ADAPTER_ADDRESSES AdapterAddresses = NULL;
    uint32_t Index = 0;

    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;
    ULONG Error;

    // 循环尝试获取适配器地址信息
    do {
        Error =
            GetAdaptersAddresses(
                AF_UNSPEC,                              // 获取 IPv4 和 IPv6 地址
                Flags,                                  // 使用指定的标志
                NULL,                                   // 没有用户数据
                AdapterAddresses,                       // 适配器地址结构
                &AdapterAddressesSize);                 // 适配器地址结构的大小
        if (Error == ERROR_BUFFER_OVERFLOW) {
            if (AdapterAddresses) {
                CXPLAT_FREE(AdapterAddresses, QUIC_POOL_DATAPATH_ADDRESSES);
            }
            AdapterAddresses = CXPLAT_ALLOC_NONPAGED(AdapterAddressesSize, QUIC_POOL_DATAPATH_ADDRESSES);
            if (!AdapterAddresses) {
                Error = ERROR_NOT_ENOUGH_MEMORY;
                QuicTraceEvent(
                    AllocFailure,
                    "Allocation of '%s' failed. (%llu bytes)",
                    "PIP_ADAPTER_ADDRESSES",
                    AdapterAddressesSize);
            }
        }
    } while (Error == ERROR_BUFFER_OVERFLOW);

    if (Error != ERROR_SUCCESS) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Error,
            "GetAdaptersAddresses");
        Status = HRESULT_FROM_WIN32(Error);
        goto Exit;
    }

    // 遍历适配器地址，统计网关地址的数量
    for (PIP_ADAPTER_ADDRESSES Iter = AdapterAddresses; Iter != NULL; Iter = Iter->Next) {
        for (PIP_ADAPTER_GATEWAY_ADDRESS_LH Iter2 = Iter->FirstGatewayAddress; Iter2 != NULL; Iter2 = Iter2->Next) {
            Index++;
        }
    }

    if (Index == 0) {
        QuicTraceEvent(
            LibraryError,
            "[ lib] ERROR, %s.",
            "No gateway server addresses found");
        Status = QUIC_STATUS_NOT_FOUND;
        goto Exit;
    }

    // 分配内存以存储网关地址列表
    *GatewayAddresses = CXPLAT_ALLOC_NONPAGED(Index * sizeof(QUIC_ADDR), QUIC_POOL_DATAPATH_ADDRESSES);
    if (*GatewayAddresses == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "GatewayAddresses",
            Index * sizeof(QUIC_ADDR));
        goto Exit;
    }

    CxPlatZeroMemory(*GatewayAddresses, Index * sizeof(QUIC_ADDR));
    *GatewayAddressesCount = Index;
    Index = 0;

    // 填充网关地址列表
    for (PIP_ADAPTER_ADDRESSES Iter = AdapterAddresses; Iter != NULL; Iter = Iter->Next) {
        for (PIP_ADAPTER_GATEWAY_ADDRESS_LH Iter2 = Iter->FirstGatewayAddress; Iter2 != NULL; Iter2 = Iter2->Next) {
            CxPlatCopyMemory(
                &(*GatewayAddresses)[Index],
                Iter2->Address.lpSockaddr,
                sizeof(QUIC_ADDR));
            Index++;
        }
    }

Exit:

    // 释放适配器地址结构的内存
    if (AdapterAddresses) {
        CXPLAT_FREE(AdapterAddresses, QUIC_POOL_DATAPATH_ADDRESSES);
    }

    return Status;
}
```

- Windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
_Success_(QUIC_SUCCEEDED(return))
QUIC_STATUS
CxPlatDataPathGetGatewayAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,
    _Outptr_ _At_(*GatewayAddresses, __drv_allocatesMem(Mem))
        QUIC_ADDR** GatewayAddresses,
    _Out_ uint32_t* GatewayAddressesCount
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    *GatewayAddresses = NULL;
    *GatewayAddressesCount = 0;
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

- unix
```c
QUIC_STATUS
CxPlatDataPathGetGatewayAddresses(
    _In_ CXPLAT_DATAPATH* Datapath,
    _Outptr_ _At_(*GatewayAddresses, __drv_allocatesMem(Mem))
        QUIC_ADDR** GatewayAddresses,
    _Out_ uint32_t* GatewayAddressesCount
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    *GatewayAddresses = NULL;
    *GatewayAddressesCount = 0;
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

---

### **用于CXPLAT_SOCKET的标志**
```c
#define CXPLAT_SOCKET_FLAG_PCP      0x00000001  // 套接字用于内部 PCP 支持
#define CXPLAT_SOCKET_FLAG_SHARE    0x00000002  // 强制共享地址和端口
#define CXPLAT_SOCKET_SERVER_OWNED  0x00000004  // 表示套接字是监听套接字
#define CXPLAT_SOCKET_FLAG_QTIP     0x00000008  // 套接字支持 QTIP
```
- 定义了套接字的标志，用于指定套接字的行为。

---

### **`CXPLAT_UDP_CONFIG` 结构体**
```c
typedef struct CXPLAT_UDP_CONFIG {
    const QUIC_ADDR* LocalAddress;      // 本地地址（可选）
    const QUIC_ADDR* RemoteAddress;     // 远程地址（可选）
    uint32_t Flags;                     // 套接字标志（CXPLAT_SOCKET_FLAG_*）
    uint32_t InterfaceIndex;            // 接口索引，0 表示任意/全部
    uint16_t PartitionIndex;            // 分区索引，仅客户端使用
    void* CallbackContext;              // 回调上下文（可选）
#ifdef QUIC_COMPARTMENT_ID
    QUIC_COMPARTMENT_ID CompartmentId;  // 隔离 ID（可选）
#endif
#ifdef QUIC_OWNING_PROCESS
    QUIC_PROCESS OwningProcess;         // 所属进程，仅内核客户端使用
#endif

    // 用于raw datapath
    uint8_t CibirIdLength;              // CIBIR ID 长度，值为 0 表示未使用 CIBIR
    uint8_t CibirIdOffsetSrc;           // CIBIR ID 在源 CID 中的偏移量
    uint8_t CibirIdOffsetDst;           // CIBIR ID 在目标 CID 中的偏移量
    uint8_t CibirId[6];                 // CIBIR ID 数据
} CXPLAT_UDP_CONFIG;
```
- 定义了创建 UDP 套接字时的配置参数。

---

### **`CxPlatSocketCreateUdp`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateUdp(
    _In_ CXPLAT_DATAPATH* Datapath, // 数据路径实例
    _In_ const CXPLAT_UDP_CONFIG* Config, // UDP 配置
    _Out_ CXPLAT_SOCKET** Socket // 输出参数，返回创建的套接字
    );
```
- 为指定的（可选）本地地址和/或（可选）远程地址创建一个 UDP 套接字
- 此函数会立即注册接收回调。

- xplat中CxPlatSocketCreateUdp的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateUdp(
    _In_ CXPLAT_DATAPATH* Datapath,       // 数据路径对象
    _In_ const CXPLAT_UDP_CONFIG* Config, // UDP 套接字的配置
    _Out_ CXPLAT_SOCKET** NewSocket       // 输出参数，返回创建的套接字
    )
{
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;

    //
    // 在生产环境中（如 XDP/QTIP+XDP 场景），通常只需要尝试一次即可绑定到指定端口。
    // 但在测试环境中，为了避免测试不稳定性，可能需要多次尝试绑定到随机的 UDP 端口。
    //
    for (uint32_t TryCount = 0; TryCount < 1000; TryCount++) {
        // 调用底层函数创建 UDP 套接字
        Status = SocketCreateUdp(Datapath, Config, NewSocket);
        if (QUIC_FAILED(Status)) {
            QuicTraceLogVerbose(
                SockCreateFail,
                "[sock] Failed to create socket, status:%d", Status);
            goto Error;
        }

        // 初始化 RawSocketAvailable 标志
        (*NewSocket)->RawSocketAvailable = 0;

        // 如果启用了 RawDataPath，则尝试创建原始套接字
        if (Datapath->RawDataPath) {
            Status = RawSocketCreateUdp(
                Datapath->RawDataPath,
                Config,
                CxPlatSocketToRaw(*NewSocket));
            (*NewSocket)->RawSocketAvailable = QUIC_SUCCEEDED(Status);

            if (QUIC_FAILED(Status)) {
                QuicTraceLogVerbose(
                    RawSockCreateFail,
                    "[sock] Failed to create raw socket, status:%d", Status);

                // 检查是否使用通配符地址以及是否启用了 QTIP 标志
                BOOLEAN IsWildcardAddr = Config->LocalAddress == NULL || QuicAddrIsWildCard(Config->LocalAddress);
                if (IsWildcardAddr && (Config->Flags & CXPLAT_SOCKET_FLAG_QTIP)) {
                    CxPlatSocketDelete(*NewSocket);
                    continue; // 继续尝试绑定到其他端口
                }

                // 如果未启用 QTIP 标志，则忽略错误
                if (!(Config->Flags & CXPLAT_SOCKET_FLAG_QTIP)) {
                    Status = QUIC_STATUS_SUCCESS;
                } else {
                    CxPlatSocketDelete(*NewSocket);
                }
                goto Error;
            }
        }
        break; // 成功创建套接字，退出循环
    }

Error:
    return Status;
}
```

- kqueue中CxPlatSocketCreateUdp的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateUdp(
    _In_ CXPLAT_DATAPATH* Datapath,       // 数据路径对象
    _In_ const CXPLAT_UDP_CONFIG* Config, // UDP 套接字的配置
    _Out_ CXPLAT_SOCKET** NewBinding      // 输出参数，返回创建的套接字
    )
{
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;
    BOOLEAN IsServerSocket = Config->RemoteAddress == NULL; // 判断是否为服务器套接字

    // 确保 UDP 回调函数已注册，或者套接字用于 PCP
    CXPLAT_DBG_ASSERT(Datapath->UdpHandlers.Receive != NULL || Config->Flags & CXPLAT_SOCKET_FLAG_PCP);

    uint32_t SocketCount = IsServerSocket ? Datapath->PartitionCount : 1; // 套接字数量
    CXPLAT_FRE_ASSERT(SocketCount > 0);

    // 计算套接字对象所需的内存大小
    size_t BindingLength =
        sizeof(CXPLAT_SOCKET) +
        SocketCount * sizeof(CXPLAT_SOCKET_CONTEXT);

    // 分配内存以创建套接字对象
    CXPLAT_SOCKET* Binding =
        (CXPLAT_SOCKET*)CXPLAT_ALLOC_PAGED(BindingLength, QUIC_POOL_SOCKET);
    if (Binding == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "CXPLAT_SOCKET",
            BindingLength);
        goto Exit;
    }

    // 初始化套接字对象
    QuicTraceEvent(
        DatapathCreated,
        "[data][%p] Created, local=%!ADDR!, remote=%!ADDR!",
        Binding,
        CASTED_CLOG_BYTEARRAY(Config->LocalAddress ? sizeof(*Config->LocalAddress) : 0, Config->LocalAddress),
        CASTED_CLOG_BYTEARRAY(Config->RemoteAddress ? sizeof(*Config->RemoteAddress) : 0, Config->RemoteAddress));

    CxPlatZeroMemory(Binding, BindingLength);
    Binding->Datapath = Datapath;
    Binding->ClientContext = Config->CallbackContext;
    Binding->HasFixedRemoteAddress = (Config->RemoteAddress != NULL);
    Binding->Mtu = CXPLAT_MAX_MTU;
    CxPlatRefInitializeEx(&Binding->RefCount, SocketCount);

    // 设置本地地址
    if (Config->LocalAddress) {
        CxPlatConvertToMappedV6(Config->LocalAddress, &Binding->LocalAddress);
    } else {
        Binding->LocalAddress.Ip.sa_family = QUIC_ADDRESS_FAMILY_INET6;
    }

    // 初始化每个套接字上下文
    for (uint32_t i = 0; i < SocketCount; i++) {
        Binding->SocketContexts[i].Binding = Binding;
        Binding->SocketContexts[i].SocketFd = INVALID_SOCKET;
        Binding->SocketContexts[i].RecvIov.iov_len =
            Binding->Mtu - CXPLAT_MIN_IPV4_HEADER_SIZE - CXPLAT_UDP_HEADER_SIZE;
        Binding->SocketContexts[i].DatapathPartition =
            IsServerSocket ?
                &Datapath->Partitions[i % Datapath->PartitionCount] :
                &Datapath->Partitions[Config->PartitionIndex];
        CxPlatRefIncrement(&Binding->SocketContexts[i].DatapathPartition->RefCount);
        CxPlatListInitializeHead(&Binding->SocketContexts[i].PendingSendDataHead);
        CxPlatLockInitialize(&Binding->SocketContexts[i].PendingSendDataLock);
        CxPlatRundownInitialize(&Binding->SocketContexts[i].UpcallRundown);
    }

    // 如果启用了 PCP 标志，则标记为 PCP 套接字
    if (Config->Flags & CXPLAT_SOCKET_FLAG_PCP) {
        Binding->PcpBinding = TRUE;
    }

    // 初始化每个套接字上下文
    for (uint32_t i = 0; i < SocketCount; i++) {
        Status =
            CxPlatSocketContextInitialize(
                &Binding->SocketContexts[i],
                Config->LocalAddress,
                Config->RemoteAddress);
        if (QUIC_FAILED(Status)) {
            goto Exit;
        }
    }

    // 转换本地地址为标准格式
    CxPlatConvertFromMappedV6(&Binding->LocalAddress, &Binding->LocalAddress);
    Binding->LocalAddress.Ipv6.sin6_scope_id = 0;

    // 设置远程地址
    if (Config->RemoteAddress != NULL) {
        Binding->RemoteAddress = *Config->RemoteAddress;
    } else {
        Binding->RemoteAddress.Ipv4.sin_port = 0;
    }

    // 设置输出指针
    *NewBinding = Binding;

    // 启动接收路径
    for (uint32_t i = 0; i < SocketCount; i++) {
        Status = CxPlatSocketContextStartReceive(&Binding->SocketContexts[i]);
        if (QUIC_FAILED(Status)) {
            goto Exit;
        }
        Binding->SocketContexts[i].IoStarted = TRUE;
    }

    Binding = NULL;

Exit:

    // 如果发生错误，清理已分配的资源
    if (Binding != NULL) {
        CxPlatSocketDelete(Binding);
    }

    return Status;
}
```

---

### **`CxPlatSocketCreateTcp`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcp(
    _In_ CXPLAT_DATAPATH* Datapath,       // 数据路径实例
    _In_opt_ const QUIC_ADDR* LocalAddress, // 本地地址（可选）
    _In_ const QUIC_ADDR* RemoteAddress,  // 远程地址（必需）
    _In_opt_ void* CallbackContext,       // 回调上下文（可选）
    _Out_ CXPLAT_SOCKET** Socket          // 输出参数，返回创建的套接字
    );
```
- 为指定的（可选）本地地址和（必需）远程地址创建一个 TCP 套接字。
- 此函数会立即注册底层的回调。
- macOS/iOS不支持此函数

- xplat中CxPlatSocketCreateTcp的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcp(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_opt_ const QUIC_ADDR* LocalAddress,
    _In_ const QUIC_ADDR* RemoteAddress,
    _In_opt_ void* CallbackContext,
    _Out_ CXPLAT_SOCKET** NewSocket
    )
{
    return SocketCreateTcp(
        Datapath,
        LocalAddress,
        RemoteAddress,
        CallbackContext,
        NewSocket);
}
```

- kqueue中CxPlatSocketCreateTcp的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcp(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_opt_ const QUIC_ADDR* LocalAddress,
    _In_ const QUIC_ADDR* RemoteAddress,
    _In_opt_ void* CallbackContext,
    _Out_ CXPLAT_SOCKET** Socket
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    UNREFERENCED_PARAMETER(LocalAddress);
    UNREFERENCED_PARAMETER(RemoteAddress);
    UNREFERENCED_PARAMETER(CallbackContext);
    UNREFERENCED_PARAMETER(Socket);
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

---

### **`CxPlatSocketCreateTcpListener`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcpListener(
    _In_ CXPLAT_DATAPATH* Datapath,       // 数据路径实例
    _In_opt_ const QUIC_ADDR* LocalAddress, // 本地地址（可选）
    _In_opt_ void* CallbackContext,       // 回调上下文（可选）
    _Out_ CXPLAT_SOCKET** Socket          // 输出参数，返回创建的监听套接字
    );
```
- 为指定的（可选）本地地址创建一个 TCP 监听套接字。
- 此函数会立即注册底层的接受回调。
- macOS/iOS不支持此函数

- xplat中CxPlatSocketCreateTcpListener的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcpListener(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_opt_ const QUIC_ADDR* LocalAddress,
    _In_opt_ void* RecvCallbackContext,
    _Out_ CXPLAT_SOCKET** NewSocket
    )
{
    return SocketCreateTcpListener(
        Datapath,
        LocalAddress,
        RecvCallbackContext,
        NewSocket);
}
```

- kqueue中CxPlatSocketCreateTcpListener的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatSocketCreateTcpListener(
    _In_ CXPLAT_DATAPATH* Datapath,
    _In_opt_ const QUIC_ADDR* LocalAddress,
    _In_opt_ void* CallbackContext,
    _Out_ CXPLAT_SOCKET** Socket
    )
{
    UNREFERENCED_PARAMETER(Datapath);
    UNREFERENCED_PARAMETER(LocalAddress);
    UNREFERENCED_PARAMETER(CallbackContext);
    UNREFERENCED_PARAMETER(Socket);
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

---

### **`CxPlatSocketDelete`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSocketDelete(
    _In_ CXPLAT_SOCKET* Socket // 要删除的套接字
    );
```
- 删除一个套接字并释放相关资源。
- 此函数会阻塞所有未完成的回调，并在返回时保证不会再触发任何回调。
- 不要在回调中调用此函数！

- xplat中CxPlatSocketDelete的实现
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatSocketDelete(
    _In_ CXPLAT_SOCKET* Socket
    )
{
    if (Socket->RawSocketAvailable) {
        RawSocketDelete(CxPlatSocketToRaw(Socket));
    }
    SocketDelete(Socket);
}
```

- kqueue中CxPlatSocketDelete的实现
```c
void
CxPlatSocketDelete(
    _Inout_ CXPLAT_SOCKET* Socket // 要删除的套接字对象
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL); // 确保套接字不为空

    // 记录套接字销毁日志
    QuicTraceEvent(
        DatapathDestroyed,
        "[data][%p] Destroyed",
        Socket);

#if DEBUG
    // 在调试模式下，标记套接字为已卸载
    CXPLAT_DBG_ASSERT(!Socket->Uninitialized);
    Socket->Uninitialized = TRUE;
#endif

    // 获取套接字上下文数量
    const uint32_t SocketCount =
        Socket->HasFixedRemoteAddress ? 1 : Socket->Datapath->PartitionCount;

    // 遍历并清理每个套接字上下文
    for (uint32_t i = 0; i < SocketCount; ++i) {
        CxPlatSocketContextUninitialize(&Socket->SocketContexts[i]);
    }
}
```

---

### **`CxPlatSocketGetLocalMtu`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
uint16_t
CxPlatSocketGetLocalMtu(
    _In_ CXPLAT_SOCKET* Socket, // 套接字实例
    _In_ CXPLAT_ROUTE* Route    // 网络路由
    );
```
- 查询与本地绑定接口相关的 MTU 值。

- xplat中CxPlatSocketGetLocalMtu的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
uint16_t
CxPlatSocketGetLocalMtu(
    _In_ CXPLAT_SOCKET* Socket, // 套接字对象
    _In_ CXPLAT_ROUTE* Route    // 路由信息
    )
{
    // 确保套接字对象不为空
    CXPLAT_DBG_ASSERT(Socket != NULL);

    // 如果路由使用 QTIP 或者原始套接字可用且远程地址不是回环地址
    if (Route->UseQTIP || (Socket->RawSocketAvailable &&
        !IS_LOOPBACK(Socket->RemoteAddress))) {
        // 调用原始套接字的函数获取本地 MTU
        return RawSocketGetLocalMtu(Route);
    }

    // 否则，返回套接字的默认 MTU 值
    return Socket->Mtu;
}
```

- kqueue中CxPlatSocketGetLocalMtu的实现
```c
uint16_t
CxPlatSocketGetLocalMtu(
    _In_ CXPLAT_SOCKET* Socket,
    _In_ CXPLAT_ROUTE* Route
    )
{
    UNREFERENCED_PARAMETER(Route);
    CXPLAT_DBG_ASSERT(Socket != NULL);
    return Socket->Mtu;
}
```

---

### **`CxPlatSocketGetLocalAddress` 函数**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketGetLocalAddress(
    _In_ CXPLAT_SOCKET* Socket, // 套接字实例
    _Out_ QUIC_ADDR* Address    // 输出参数，返回本地地址
    );
```
- 获取与套接字绑定的本地 IP 地址。

- xplat中CxPlatSocketGetLocalAddress的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketGetLocalAddress(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ QUIC_ADDR* Address
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL);
    *Address = Socket->LocalAddress;
}
```

- kqueue中CxPlatSocketGetLocalAddress的实现
```c
void
CxPlatSocketGetLocalAddress(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ QUIC_ADDR* Address
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL);
    *Address = Socket->LocalAddress;
}
```

---

### **`CxPlatSocketGetRemoteAddress`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketGetRemoteAddress(
    _In_ CXPLAT_SOCKET* Socket, // 套接字实例
    _Out_ QUIC_ADDR* Address    // 输出参数，返回远程地址
    );
```
- 获取与套接字连接的远程 IP 地址。

- xplat中CxPlatSocketGetRemoteAddress的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketGetRemoteAddress(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ QUIC_ADDR* Address
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL);
    *Address = Socket->RemoteAddress;
}
```

- kqueue中CxPlatSocketGetRemoteAddress的实现
```c
void
CxPlatSocketGetRemoteAddress(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ QUIC_ADDR* Address
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL);
    *Address = Socket->RemoteAddress;
}
```

---

### **`CxPlatSocketRawSocketAvailable`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSocketRawSocketAvailable(
    _In_ CXPLAT_SOCKET* Socket
    );
```
- 查询raw socket是否可用

- xplat中CxPlatSocketRawSocketAvailable的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSocketRawSocketAvailable(
    _In_ CXPLAT_SOCKET* Socket
    )
{
    return Socket->RawSocketAvailable;
}
```

- kqueue中CxPlatSocketRawSocketAvailable的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSocketRawSocketAvailable(
    _In_ CXPLAT_SOCKET* Socket
    )
{
    UNREFERENCED_PARAMETER(Socket);
    return FALSE;
}
```

---

### **`CxPlatRecvDataReturn`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatRecvDataReturn(
    _In_opt_ CXPLAT_RECV_DATA* RecvDataChain // 接收到的数据链
    );
```
- 释放接收到的数据链。

- xplat中CxPlatRecvDataReturn的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatRecvDataReturn(
    _In_opt_ CXPLAT_RECV_DATA* RecvDataChain // 接收数据链表
    )
{
    // 如果接收数据链表为空，则直接返回
    if (RecvDataChain == NULL) {
        return;
    }

    // 确保接收数据的类型是正常数据路径或原始数据路径
    CXPLAT_DBG_ASSERT(
        RecvDataChain->DatapathType == CXPLAT_DATAPATH_TYPE_NORMAL ||
        RecvDataChain->DatapathType == CXPLAT_DATAPATH_TYPE_RAW);

    // 根据接收数据的类型调用相应的释放函数
    RecvDataChain->DatapathType == CXPLAT_DATAPATH_TYPE_NORMAL ?
        RecvDataReturn(RecvDataChain) : // 正常数据路径的释放函数
        RawRecvDataReturn(RecvDataChain); // 原始数据路径的释放函数
}
```

- kqueue中CxPlatRecvDataReturn的实现
```c
void
CxPlatRecvDataReturn(
    _In_opt_ CXPLAT_RECV_DATA* RecvDataChain // 接收数据链表
    )
{
    // 遍历接收数据链表并释放每个数据包的内存
    CXPLAT_RECV_DATA* Datagram;
    while ((Datagram = RecvDataChain) != NULL) {
        // 将当前数据包从链表中移除
        RecvDataChain = RecvDataChain->Next;

        // 获取包含接收数据的 IO 块
        DATAPATH_RX_IO_BLOCK* IoBlock =
            CXPLAT_CONTAINING_RECORD(Datagram, DATAPATH_RX_IO_BLOCK, RecvPacket);

        // 释放 IO 块的内存
        CxPlatPoolFree(IoBlock);
    }
}
```

---

### **`CXPLAT_SEND_FLAGS` 枚举**
```c
typedef enum CXPLAT_SEND_FLAGS {
    CXPLAT_SEND_FLAGS_NONE = 0,            // 无特殊标志
    CXPLAT_SEND_FLAGS_MAX_THROUGHPUT = 1,  // 优化为最大吞吐量
} CXPLAT_SEND_FLAGS;
```
- 定义发送操作的标志，用于指定发送行为。

---

### **`CXPLAT_SEND_CONFIG` 结构体**
```c
typedef struct CXPLAT_SEND_CONFIG {
    CXPLAT_ROUTE* Route;          // 网络路由
    uint16_t MaxPacketSize;       // 最大数据包大小
    uint8_t ECN;                  // 显式拥塞通知类型（CXPLAT_ECN_TYPE）
    uint8_t Flags;                // 发送标志（CXPLAT_SEND_FLAGS）
    uint8_t DSCP;                 // 差分服务代码点（CXPLAT_DSCP_TYPE）
} CXPLAT_SEND_CONFIG;
```
- 定义发送操作的配置参数。

---

### **`CxPlatSendDataAlloc` 函数**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
CXPLAT_SEND_DATA*
CxPlatSendDataAlloc(
    _In_ CXPLAT_SOCKET* Socket,          // 套接字实例
    _Inout_ CXPLAT_SEND_CONFIG* Config   // 发送配置
    );
```
- 分配一个新的发送上下文，用于发送数据。
- 分配一个新的发送上下文，用于调用 `QuicSocketSend`。
- 可以通过 `QuicSendDataFree` 释放。

- xplat中CxPlatSendDataAlloc的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
CXPLAT_SEND_DATA*
CxPlatSendDataAlloc(
    _In_ CXPLAT_SOCKET* Socket,         // 套接字对象
    _Inout_ CXPLAT_SEND_CONFIG* Config  // 发送配置
    )
{
    CXPLAT_SEND_DATA* SendData = NULL;

    // 检查路由的类型是否为原始数据路径或未知数据路径
    if (Config->Route->DatapathType == CXPLAT_DATAPATH_TYPE_RAW ||
        (Config->Route->DatapathType == CXPLAT_DATAPATH_TYPE_UNKNOWN &&
        Socket->RawSocketAvailable && !IS_LOOPBACK(Config->Route->RemoteAddress))) {
        // 如果是原始数据路径或满足条件的未知数据路径，调用原始发送数据分配函数
        SendData = RawSendDataAlloc(Config);
    } else {
        // 否则，调用正常发送数据分配函数
        SendData = SendDataAlloc(Socket, Config);
    }

    // 返回分配的发送数据对象
    return SendData;
}
```

- kqueue中CxPlatSendDataAlloc的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
CXPLAT_SEND_DATA*
CxPlatSendDataAlloc(
    _In_ CXPLAT_SOCKET* Socket,         // 套接字对象
    _Inout_ CXPLAT_SEND_CONFIG* Config  // 发送配置
    )
{
    CXPLAT_DBG_ASSERT(Socket != NULL); // 确保套接字对象不为空

    // 如果路由队列尚未设置，则将其设置为套接字的第一个上下文
    if (Config->Route->Queue == NULL) {
        Config->Route->Queue = &Socket->SocketContexts[0];
    }

    // 获取路由队列对应的套接字上下文
    CXPLAT_SOCKET_CONTEXT* SocketContext = Config->Route->Queue;

    // 从发送数据池中分配发送数据对象
    CXPLAT_SEND_DATA* SendData = CxPlatPoolAlloc(&SocketContext->DatapathPartition->SendDataPool);
    if (SendData != NULL) {
        // 初始化发送数据对象
        CxPlatZeroMemory(SendData, sizeof(*SendData));
        SendData->Owner = SocketContext->DatapathPartition; // 设置所属的分区
        SendData->ECN = Config->ECN;                       // 设置显式拥塞通知（ECN）
        SendData->DSCP = Config->DSCP;                     // 设置差分服务代码点（DSCP）
        SendData->SegmentSize =
            (Socket->Datapath->Features & CXPLAT_DATAPATH_FEATURE_SEND_SEGMENTATION)
                ? Config->MaxPacketSize : 0;               // 设置分段大小（如果支持分段发送）
    }

    // 返回分配的发送数据对象
    return SendData;
}
```

---

### **`CxPlatSendDataFree`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFree(
    _In_ CXPLAT_SEND_DATA* SendData // 要释放的发送上下文
    );
```
- 释放从之前调用 `QuicSendDataAlloc` 返回的发送上下文。

- xplat中CxPlatSendDataFree的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFree(
    _In_ CXPLAT_SEND_DATA* SendData // 要释放的发送数据对象
    )
{
    // 确保发送数据的类型是正常数据路径或原始数据路径
    CXPLAT_DBG_ASSERT(
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ||
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_RAW);

    // 根据数据路径类型调用相应的释放函数
    DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ?
        SendDataFree(SendData) : // 正常数据路径的释放函数
        RawSendDataFree(SendData); // 原始数据路径的释放函数
}
```

- kqueue中CxPlatSendDataFree的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFree(
    _In_ CXPLAT_SEND_DATA* SendData // 要释放的发送数据对象
    )
{
    // 遍历发送数据的所有缓冲区并释放内存
    for (size_t i = 0; i < SendData->BufferCount; ++i) {
        // 从发送缓冲区池中释放每个缓冲区
        CxPlatPoolFree(SendData->Buffers[i].Buffer);
    }

    // 释放发送数据对象本身的内存
    CxPlatPoolFree(SendData);
}
```

---

### **`CxPlatSendDataAllocBuffer`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
QUIC_BUFFER*
CxPlatSendDataAllocBuffer(
    _In_ CXPLAT_SEND_DATA* SendData,     // 发送上下文
    _In_ uint16_t MaxBufferLength        // 缓冲区的最大长度
    );
```
- 为发送操作分配一个新的数据缓冲区。

- xplat中CxPlatSendDataAllocBuffer的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
QUIC_BUFFER*
CxPlatSendDataAllocBuffer(
    _In_ CXPLAT_SEND_DATA* SendData,   // 发送数据对象
    _In_ uint16_t MaxBufferLength      // 要分配的缓冲区的最大长度
    )
{
    // 确保发送数据的类型是正常数据路径或原始数据路径
    CXPLAT_DBG_ASSERT(
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ||
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_RAW);

    // 根据数据路径类型调用相应的缓冲区分配函数
    return
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ?
        SendDataAllocBuffer(SendData, MaxBufferLength) : // 正常数据路径的缓冲区分配
        RawSendDataAllocBuffer(SendData, MaxBufferLength); // 原始数据路径的缓冲区分配
}
```

- kqueue中CxPlatSendDataAllocBuffer的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
_Success_(return != NULL)
QUIC_BUFFER*
CxPlatSendDataAllocBuffer(
    _In_ CXPLAT_SEND_DATA* SendData,   // 发送数据对象
    _In_ uint16_t MaxBufferLength      // 要分配的缓冲区的最大长度
    )
{
    CXPLAT_DBG_ASSERT(SendData != NULL); // 确保发送数据对象不为空
    CXPLAT_DBG_ASSERT(MaxBufferLength > 0); // 确保缓冲区长度大于 0

    // 确保当前缓冲区已完成分配
    CxPlatSendDataFinalizeSendBuffer(SendData);

    // 检查是否可以分配新的发送缓冲区
    if (!CxPlatSendDataCanAllocSend(SendData, MaxBufferLength)) {
        return NULL; // 如果无法分配，返回 NULL
    }

    // 如果未启用分段发送，则分配单个数据包缓冲区
    if (SendData->SegmentSize == 0) {
        return CxPlatSendDataAllocPacketBuffer(SendData, MaxBufferLength);
    }

    // 如果启用了分段发送，则分配分段缓冲区
    return CxPlatSendDataAllocSegmentBuffer(SendData, MaxBufferLength);
}
```

---

### **`CxPlatSendDataFreeBuffer` 函数**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFreeBuffer(
    _In_ CXPLAT_SEND_DATA* SendData, // 发送上下文
    _In_ QUIC_BUFFER* Buffer         // 要释放的数据缓冲区
    );
```
- 释放从之前调用 `QuicSendDataAllocBuffer` 返回的数据缓冲区。

- xplat中CxPlatSendDataFreeBuffer的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFreeBuffer(
    _In_ CXPLAT_SEND_DATA* SendData, // 发送数据对象
    _In_ QUIC_BUFFER* Buffer         // 要释放的缓冲区
    )
{
    // 确保发送数据的类型是正常数据路径或原始数据路径
    CXPLAT_DBG_ASSERT(
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ||
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_RAW);

    // 根据数据路径类型调用相应的缓冲区释放函数
    DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ?
        SendDataFreeBuffer(SendData, Buffer) : // 正常数据路径的缓冲区释放
        RawSendDataFreeBuffer(SendData, Buffer); // 原始数据路径的缓冲区释放
}
```

- kqueue中CxPlatSendDataFreeBuffer的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSendDataFreeBuffer(
    _In_ CXPLAT_SEND_DATA* SendData, // 发送数据对象
    _In_ QUIC_BUFFER* Buffer         // 要释放的缓冲区
    )
{
    //
    // 确保发送数据对象和缓冲区不为空
    //
    CXPLAT_DBG_ASSERT(SendData != NULL);
    CXPLAT_DBG_ASSERT(Buffer != NULL);

    //
    // 确保释放的缓冲区是最后一个缓冲区；中间缓冲区不能被释放
    //
#ifdef DEBUG
    uint8_t* TailBuffer = SendData->Buffers[SendData->BufferCount - 1].Buffer;
#endif

    //
    // 如果未启用分段发送
    //
    if (SendData->SegmentSize == 0) {
#ifdef DEBUG
        CXPLAT_DBG_ASSERT(Buffer->Buffer == (uint8_t*)TailBuffer);
#endif
        //
        // 释放缓冲区内存并减少缓冲区计数
        //
        CxPlatPoolFree(Buffer->Buffer);
        --SendData->BufferCount;
    } else {
        //
        // 如果启用了分段发送
        //
#ifdef DEBUG
        TailBuffer += SendData->Buffers[SendData->BufferCount - 1].Length;
        CXPLAT_DBG_ASSERT(Buffer->Buffer == (uint8_t*)TailBuffer);
#endif

        //
        // 如果最后一个缓冲区的长度为 0，则释放缓冲区内存并减少缓冲区计数
        //
        if (SendData->Buffers[SendData->BufferCount - 1].Length == 0) {
            CxPlatPoolFree(Buffer->Buffer);
            --SendData->BufferCount;
        }

        //
        // 清空客户端缓冲区的引用
        //
        SendData->ClientBuffer.Buffer = NULL;
        SendData->ClientBuffer.Length = 0;
    }
}
```

---

### **`CxPlatSendDataIsFull`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSendDataIsFull(
    _In_ CXPLAT_SEND_DATA* SendData // 发送上下文
    );
```
- 检查发送上下文的缓冲区是否已满。

- xplat中CxPlatSendDataIsFull的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSendDataIsFull(
    _In_ CXPLAT_SEND_DATA* SendData // 发送数据对象
    )
{
    // 确保发送数据的类型是正常数据路径或原始数据路径
    CXPLAT_DBG_ASSERT(
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ||
        DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_RAW);

    // 根据数据路径类型调用相应的检查函数
    return DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL ?
        SendDataIsFull(SendData) : // 正常数据路径的检查函数
        RawSendDataIsFull(SendData); // 原始数据路径的检查函数
}
```

- kqueue中CxPlatSendDataIsFull的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
BOOLEAN
CxPlatSendDataIsFull(
    _In_ CXPLAT_SEND_DATA* SendData // 发送数据对象
    )
{
    //
    // 检查发送数据对象是否已满，无法再分配新的缓冲区。
    // 调用内部函数 `CxPlatSendDataCanAllocSend` 来判断是否可以分配更多数据。
    //
    return !CxPlatSendDataCanAllocSend(SendData, SendData->SegmentSize);
}
```

---

### **`CxPlatSocketSend`**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketSend(
    _In_ CXPLAT_SOCKET* Socket,          // 套接字实例
    _In_ const CXPLAT_ROUTE* Route,      // 路由信息
    _In_ CXPLAT_SEND_DATA* SendData      // 发送上下文
    );
```
- 通过指定的套接字发送数据。

- xplat中CxPlatSendDataIsFull的实现
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
CxPlatSocketSend(
    _In_ CXPLAT_SOCKET* Socket,         // 套接字对象
    _In_ const CXPLAT_ROUTE* Route,     // 路由信息
    _In_ CXPLAT_SEND_DATA* SendData     // 发送数据对象
    )
{
    //
    // 根据发送数据的类型调用相应的发送函数。
    //
    if (DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_NORMAL) {
        //
        // 如果是正常数据路径，调用 SocketSend 函数发送数据。
        //
        SocketSend(Socket, Route, SendData);
    } else {
        //
        // 如果是原始数据路径，调用 RawSocketSend 函数发送数据。
        //
        CXPLAT_DBG_ASSERT(DatapathType(SendData) == CXPLAT_DATAPATH_TYPE_RAW);
        RawSocketSend(CxPlatSocketToRaw(Socket), Route, SendData);
    }
}
```

- kqueue中CxPlatSendDataIsFull的实现
```c
void
CxPlatSocketSend(
    _In_ CXPLAT_SOCKET* Socket,         // 套接字对象
    _In_ const CXPLAT_ROUTE* Route,     // 路由信息
    _In_ CXPLAT_SEND_DATA* SendData     // 发送数据对象
    )
{
    //
    // 确保路由队列已初始化。
    //
    CXPLAT_DBG_ASSERT(Route->Queue);

    //
    // 调用内部发送函数执行数据发送操作。
    //
    CxPlatSocketSendInternal(
        Route->Queue,                   // 路由队列（套接字上下文）
        &Route->LocalAddress,           // 本地地址
        &Route->RemoteAddress,          // 远程地址
        SendData,                       // 发送数据对象
        FALSE                           // 指定发送操作未挂起
    );
}
```

---

### **`CXPLAT_TCP_STATISTICS` 结构体**
```c
typedef struct CXPLAT_TCP_STATISTICS {
    uint32_t Mss;                     // 最大段大小（Maximum Segment Size）
    uint64_t ConnectionTimeMs;        // 连接时间（毫秒）
    BOOLEAN TimestampsEnabled;        // 是否启用了时间戳
    uint32_t RttUs;                   // 往返时间（微秒）
    uint32_t MinRttUs;                // 最小往返时间（微秒）
    uint32_t BytesInFlight;           // 正在传输中的字节数
    uint32_t Cwnd;                    // 拥塞窗口大小
    uint32_t SndWnd;                  // 发送窗口大小
    uint32_t RcvWnd;                  // 接收窗口大小
    uint32_t RcvBuf;                  // 接收缓冲区大小
    uint64_t BytesOut;                // 已发送的字节数
    uint64_t BytesIn;                 // 已接收的字节数
    uint32_t BytesReordered;          // 重排序的字节数
    uint32_t BytesRetrans;            // 重传的字节数
    uint32_t FastRetrans;             // 快速重传的次数
    uint32_t DupAcksIn;               // 收到的重复 ACK 数量
    uint32_t TimeoutEpisodes;         // 超时事件的次数
    uint8_t SynRetrans;               // SYN 重传的次数
    uint32_t SndLimTransRwin;         // 发送受接收窗口限制的次数
    uint32_t SndLimTimeRwin;          // 发送受接收窗口限制的时间
    uint64_t SndLimBytesRwin;         // 发送受接收窗口限制的字节数
    uint32_t SndLimTransCwnd;         // 发送受拥塞窗口限制的次数
    uint32_t SndLimTimeCwnd;          // 发送受拥塞窗口限制的时间
    uint64_t SndLimBytesCwnd;         // 发送受拥塞窗口限制的字节数
    uint32_t SndLimTransSnd;          // 发送受发送缓冲区限制的次数
    uint32_t SndLimTimeSnd;           // 发送受发送缓冲区限制的时间
    uint64_t SndLimBytesSnd;          // 发送受发送缓冲区限制的字节数
} CXPLAT_TCP_STATISTICS;
```
- 该结构体用于存储 TCP 连接的统计信息。
- TCP 统计信息结构体（目前大部分从 TCP_INFO_v1 复制）。
- 仅Windows用户模式下支持，其他平台返回QUIC_STATUS_NOT_SUPPORTED

---

### **`CxPlatSocketGetTcpStatistics` 函数**
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
CxPlatSocketGetTcpStatistics(
    _In_ CXPLAT_SOCKET* Socket,               // 套接字实例
    _Out_ CXPLAT_TCP_STATISTICS* Statistics   // 输出参数，返回 TCP 统计信息
    );
```
- 获取指定套接字的 TCP 统计信息。

- windows user
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
CxPlatSocketGetTcpStatistics(
    _In_ CXPLAT_SOCKET* Socket,             // 套接字对象
    _Out_ CXPLAT_TCP_STATISTICS* Statistics // 输出参数，用于存储 TCP 统计信息
    )
{
#if (NTDDI_VERSION >= NTDDI_WIN10_RS5) // 检查是否支持 TCP_INFO_v1
    CXPLAT_SOCKET_PROC* SocketProc = &Socket->PerProcSockets[0]; // 获取套接字的处理器上下文
    DWORD Version = 1; // 使用 TCP_INFO_v1 版本
    TCP_INFO_v1 Info = { 0 }; // 用于存储 TCP 信息的结构
    DWORD InfoSize = sizeof(Info); // TCP 信息结构的大小

    // 调用 WSAIoctl 获取 TCP 统计信息
    int Result =
        WSAIoctl(
            SocketProc->Socket,          // 套接字句柄
            SIO_TCP_INFO,                // IOCTL 操作码，用于获取 TCP 信息
            &Version,                    // 输入参数：版本号
            sizeof(Version),             // 输入参数大小
            &Info,                       // 输出参数：TCP 信息
            InfoSize,                    // 输出参数大小
            &InfoSize,                   // 实际返回的大小
            NULL,                        // 未使用
            NULL);                       // 未使用

    // 检查是否调用成功
    if (Result == SOCKET_ERROR) {
        int WsaError = WSAGetLastError(); // 获取错误代码
        QuicTraceEvent(
            DatapathErrorStatus,
            "[data][%p] ERROR, %u, %s.",
            SocketProc->Parent,
            WsaError,
            "WSAIoctl TCP_INFO_v1");
        return HRESULT_FROM_WIN32(WsaError); // 返回错误状态
    }

    // 填充输出的 TCP 统计信息
    Statistics->Mss = Info.Mss;
    Statistics->ConnectionTimeMs = Info.ConnectionTimeMs;
    Statistics->TimestampsEnabled = Info.TimestampsEnabled;
    Statistics->RttUs = Info.RttUs;
    Statistics->MinRttUs = Info.MinRttUs;
    Statistics->BytesInFlight = Info.BytesInFlight;
    Statistics->Cwnd = Info.Cwnd;
    Statistics->SndWnd = Info.SndWnd;
    Statistics->RcvWnd = Info.RcvWnd;
    Statistics->RcvBuf = Info.RcvBuf;
    Statistics->BytesOut = Info.BytesOut;
    Statistics->BytesIn = Info.BytesIn;
    Statistics->BytesReordered = Info.BytesReordered;
    Statistics->BytesRetrans = Info.BytesRetrans;
    Statistics->FastRetrans = Info.FastRetrans;
    Statistics->DupAcksIn = Info.DupAcksIn;
    Statistics->TimeoutEpisodes = Info.TimeoutEpisodes;
    Statistics->SynRetrans = Info.SynRetrans;
    Statistics->SndLimTransRwin = Info.SndLimTransRwin;
    Statistics->SndLimTimeRwin = Info.SndLimTimeRwin;
    Statistics->SndLimTransCwnd = Info.SndLimTransCwnd;
    Statistics->SndLimTimeCwnd = Info.SndLimTimeCwnd;
    Statistics->SndLimTransSnd = Info.SndLimTransSnd;
    Statistics->SndLimTimeSnd = Info.SndLimTimeSnd;
    Statistics->SndLimBytesRwin = Info.SndLimBytesRwin;
    Statistics->SndLimBytesCwnd = Info.SndLimBytesCwnd;
    Statistics->SndLimBytesSnd = Info.SndLimBytesSnd;

    return QUIC_STATUS_SUCCESS; // 返回成功状态

#else
    // 如果不支持 TCP_INFO_v1，则返回不支持的状态
    UNREFERENCED_PARAMETER(Socket);
    UNREFERENCED_PARAMETER(Statistics);
    return QUIC_STATUS_NOT_SUPPORTED;
#endif
}
```

- windows kernel
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
CxPlatSocketGetTcpStatistics(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ CXPLAT_TCP_STATISTICS* Statistics
    )
{
    UNREFERENCED_PARAMETER(Socket);
    UNREFERENCED_PARAMETER(Statistics);
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

- epoll
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
CxPlatSocketGetTcpStatistics(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ CXPLAT_TCP_STATISTICS* Statistics
    )
{
    UNREFERENCED_PARAMETER(Socket);
    UNREFERENCED_PARAMETER(Statistics);
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

- kqueue
```c
_IRQL_requires_max_(DISPATCH_LEVEL)
QUIC_STATUS
CxPlatSocketGetTcpStatistics(
    _In_ CXPLAT_SOCKET* Socket,
    _Out_ CXPLAT_TCP_STATISTICS* Statistics
    )
{
    UNREFERENCED_PARAMETER(Socket);
    UNREFERENCED_PARAMETER(Statistics);
    return QUIC_STATUS_NOT_SUPPORTED;
}
```

---

### **`CXPLAT_ROUTE_RESOLUTION_CALLBACK` 函数指针类型**
```c
typedef
_IRQL_requires_max_(DISPATCH_LEVEL)
_Function_class_(CXPLAT_ROUTE_RESOLUTION_CALLBACK)
void
(CXPLAT_ROUTE_RESOLUTION_CALLBACK)(
    _Inout_ void* Context,                              // 上下文
    _When_(Succeeded == FALSE, _Reserved_)              // 如果解析失败，保留
    _When_(Succeeded == TRUE, _In_reads_bytes_(6))      // 如果解析成功，返回物理地址
        const uint8_t* PhysicalAddress,                // 物理地址（MAC 地址）
    _In_ uint8_t PathId,                                // 路径 ID
    _In_ BOOLEAN Succeeded                              // 是否解析成功
    );
```
- 定义了路由解析完成时的回调函数类型。

---

### **`CxPlatResolveRouteComplete`**
```c
void
CxPlatResolveRouteComplete(
    _In_ void* Context,                              // 上下文
    _Inout_ CXPLAT_ROUTE* Route,                    // 路由对象
    _In_reads_bytes_(6) const uint8_t* PhysicalAddress, // 物理地址（MAC 地址）
    _In_ uint8_t PathId                             // 路径 ID
    );
```
- 在路由解析完成后，将物理地址复制到路由对象中，并更新路由状态为已解析。
- 将 L2 地址复制到路由对象中，并将路由状态设置为已解析。

---

### **`CxPlatResolveRoute`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatResolveRoute(
    _In_ CXPLAT_SOCKET* Socket,                     // 套接字实例
    _Inout_ CXPLAT_ROUTE* Route,                    // 路由对象
    _In_ uint8_t PathId,                            // 路径 ID
    _In_ void* Context,                             // 上下文
    _In_ CXPLAT_ROUTE_RESOLUTION_CALLBACK_HANDLER Callback // 路由解析回调
    );
```
- 尝试解析目标地址的路由，并在解析完成后调用回调函数。

---

### **`CxPlatUpdateRoute`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatUpdateRoute(
    _Inout_ CXPLAT_ROUTE* DstRoute, // 目标路由对象
    _In_ CXPLAT_ROUTE* SrcRoute     // 源路由对象
    );
```
- 将源路由对象的信息复制到目标路由对象中。

---

**RSS Configuration** 指的是 **Receive Side Scaling（接收端缩放）配置**。  
它是一种网络性能优化技术，用于在多核处理器系统中分配网络流量，以提高网络吞吐量和降低延迟。
目前只在xdp中支持
---

### **RSS 的作用**
1. **多核处理器优化**:
   - RSS 允许网络适配器将接收到的网络流量分配到不同的 CPU 核心进行处理，从而避免单个核心成为瓶颈。

2. **流量分配**:
   - RSS 使用哈希算法（基于 IP 地址、端口等）将网络流量分配到特定的队列，每个队列对应一个 CPU 核心。

3. **性能提升**:
   - 通过并行处理网络流量，RSS 提高了网络吞吐量，降低了延迟，并优化了多核系统的资源利用率。

---

### **`CXPLAT_RSS_HASH_TYPE` 枚举**
```c
//
// 获取接口的 RSS 配置。
//
typedef enum CXPLAT_RSS_HASH_TYPE {
    CXPLAT_RSS_HASH_TYPE_IPV4        = 0x001, // IPv4 流量
    CXPLAT_RSS_HASH_TYPE_TCP_IPV4    = 0x002, // IPv4 上的 TCP 流量
    CXPLAT_RSS_HASH_TYPE_UDP_IPV4    = 0x004, // IPv4 上的 UDP 流量
    CXPLAT_RSS_HASH_TYPE_IPV6        = 0x008, // IPv6 流量
    CXPLAT_RSS_HASH_TYPE_TCP_IPV6    = 0x010, // IPv6 上的 TCP 流量
    CXPLAT_RSS_HASH_TYPE_UDP_IPV6    = 0x020, // IPv6 上的 UDP 流量
    CXPLAT_RSS_HASH_TYPE_IPV6_EX     = 0x040, // 扩展的 IPv6 流量
    CXPLAT_RSS_HASH_TYPE_TCP_IPV6_EX = 0x080, // 扩展的 IPv6 上的 TCP 流量
    CXPLAT_RSS_HASH_TYPE_UDP_IPV6_EX = 0x100  // 扩展的 IPv6 上的 UDP 流量
} CXPLAT_RSS_HASH_TYPE;
```
- **作用**: 定义了接收端缩放（RSS）支持的哈希类型，用于指定不同类型的网络流量。
- **枚举值**:
  - `CXPLAT_RSS_HASH_TYPE_IPV4`: IPv4 流量。
  - `CXPLAT_RSS_HASH_TYPE_TCP_IPV4`: IPv4 上的 TCP 流量。
  - `CXPLAT_RSS_HASH_TYPE_UDP_IPV4`: IPv4 上的 UDP 流量。
  - `CXPLAT_RSS_HASH_TYPE_IPV6`: IPv6 流量。
  - `CXPLAT_RSS_HASH_TYPE_TCP_IPV6`: IPv6 上的 TCP 流量。
  - `CXPLAT_RSS_HASH_TYPE_UDP_IPV6`: IPv6 上的 UDP 流量。
  - `CXPLAT_RSS_HASH_TYPE_IPV6_EX`: 扩展的 IPv6 流量。
  - `CXPLAT_RSS_HASH_TYPE_TCP_IPV6_EX`: 扩展的 IPv6 上的 TCP 流量。
  - `CXPLAT_RSS_HASH_TYPE_UDP_IPV6_EX`: 扩展的 IPv6 上的 UDP 流量。
 
---

### **`CXPLAT_RSS_CONFIG` 结构体**
```c
typedef struct CXPLAT_RSS_CONFIG {
    CXPLAT_RSS_HASH_TYPE HashTypes;          // 支持的哈希类型
    uint32_t RssSecretKeyLength;             // RSS 密钥的长度
    uint32_t RssIndirectionTableCount;       // RSS 重定向表的条目数
    _Field_size_bytes_(RssSecretKeyLength)
    uint8_t* RssSecretKey;                   // RSS 密钥
    _Field_size_(RssIndirectionTableCount)
    uint32_t* RssIndirectionTable;           // RSS 重定向表（转换为处理器索引）
} CXPLAT_RSS_CONFIG;
```
- 表示网络接口的 RSS 配置，包括哈希类型、密钥和重定向表

---

### **`CxPlatDataPathRssConfigGet`**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatDataPathRssConfigGet(
    _In_ uint32_t InterfaceIndex, // 接口索引
    _Outptr_ _At_(*RssConfig, __drv_allocatesMem(Mem))
        CXPLAT_RSS_CONFIG** RssConfig // 输出参数，返回 RSS 配置
    );
```
- 获取指定网络接口的 RSS 配置信息。

---

### **`CxPlatDataPathRssConfigFree` 函数**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatDataPathRssConfigFree(
    _In_ CXPLAT_RSS_CONFIG* RssConfig // 要释放的 RSS 配置
    );
```
- 释放通过 `CxPlatDataPathRssConfigGet` 获取的 RSS 配置

---

