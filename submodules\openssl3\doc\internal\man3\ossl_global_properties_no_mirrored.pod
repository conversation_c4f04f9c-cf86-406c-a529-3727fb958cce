=pod

=head1 NAME

ossl_property_list_to_string, ossl_global_properties_no_mirrored
- internal property routines

=head1 SYNOPSIS

 #include "internal/property.h"

 size_t ossl_property_list_to_string(OSSL_LIB_CTX *ctx,
                                     const OSSL_PROPERTY_LIST *list, char *buf,
                                     size_t bufsize);

 int ossl_global_properties_no_mirrored(OSSL_LIB_CTX *libctx);
 void ossl_global_properties_no_mirrored(OSSL_LIB_CTX *libctx);


=head1 DESCRIPTION

ossl_property_list_to_string() takes a given OSSL_PROPERTY_LIST in I<list> and
converts it to a string. If I<buf> is non NULL then the string will be stored
in I<buf>. The size of the buffer is provided in I<bufsize>. If I<bufsize> is
too short then the string will be truncated. If I<buf> is NULL then the length
of the string is still calculated and returned. If the property list has no
properties in it then the empty string will be stored in I<buf>.

ossl_global_properties_no_mirrored() checks whether mirroring of global
properties from a parent library context is allowed for the current library
context.

ossl_global_properties_no_mirrored() prevents future mirroring of global
properties from a parent library context for the current library context.

=head1 RETURN VALUES

ossl_property_list_to_string() returns the length of the string, or 0 on error.

ossl_global_properties_no_mirrored() returns 1 if mirroring of global properties
is not allowed, or 0 otherwise.

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
