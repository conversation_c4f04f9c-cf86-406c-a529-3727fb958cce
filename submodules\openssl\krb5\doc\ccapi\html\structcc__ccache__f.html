<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_ccache_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_ccache_f Struct Reference</h1><!-- doxytag: class="cc_ccache_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_ccache_t. For more information see <a class="el" href="group__cc__ccache__reference.html">cc_ccache_t Overview</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#ef46c1c04ceb1a0a479db84ddba7160c">release</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ge517135d87d8775d77b426d57a491ef0">cc_ccache_release()</a></b>: Release memory associated with a cc_ccache_t object.  <a href="#ef46c1c04ceb1a0a479db84ddba7160c"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#86872ea5155e7ff57f4674a8f8c1d6c5">destroy</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ge05b68d91bece2f99b531e96cde8d457">cc_ccache_destroy()</a></b>: Destroy a ccache.  <a href="#86872ea5155e7ff57f4674a8f8c1d6c5"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#93c7e154d7472227b02d240a13dd53da">set_default</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g535b92993b85d92b67fa622447afbe13">cc_ccache_set_default()</a></b>: Make a ccache the default ccache.  <a href="#93c7e154d7472227b02d240a13dd53da"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#934cc9927404ee42460008b49e572158">get_credentials_version</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_credentials_version)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g934f93499765bdd179bb2342ae0f0fa6">cc_ccache_get_credentials_version()</a></b>: Get the credentials version of a ccache.  <a href="#934cc9927404ee42460008b49e572158"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#e60fec8fe34019df7108e8deed0f52ac">get_name</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_name)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g042bea6044879ec03996b190792e3ae9">cc_ccache_get_name()</a></b>: Get the name of a ccache.  <a href="#e60fec8fe34019df7108e8deed0f52ac"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#ef23df23bb71b1f01138b791bc8b6255">get_principal</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_principal)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g464aa49a2e8054c9c3c2a3410eaf5c54">cc_ccache_get_principal()</a></b>: Get the principal of a ccache.  <a href="#ef23df23bb71b1f01138b791bc8b6255"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#0d72ae907e8357633fe4ff2248818f42">set_principal</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, const char *in_principal)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a></b>: Set the principal of a ccache.  <a href="#0d72ae907e8357633fe4ff2248818f42"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#c4ac7d0cb5e15309978d8c4990f769a3">store_credentials</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, const <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> *in_credentials_union)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g35c1548dbacb8907da7b8c3124eabf39">cc_ccache_store_credentials()</a></b>: Store credentials in a ccache.  <a href="#c4ac7d0cb5e15309978d8c4990f769a3"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#6cc6d19ff6044fafc3cd9f42c338341c">remove_credentials</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_credentials)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ga1bbc05414ad4c17cea9cd5e5c50c7cc">cc_ccache_remove_credentials()</a></b>: Remove credentials from a ccache.  <a href="#6cc6d19ff6044fafc3cd9f42c338341c"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#4efc19a7cd2ac6695da44cb7f7e9be14">new_credentials_iterator</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> *out_credentials_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g893b31c419e71c2f528781d3036fa3ff">cc_ccache_new_credentials_iterator()</a></b>: Iterate over credentials in a ccache.  <a href="#4efc19a7cd2ac6695da44cb7f7e9be14"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#d4e0dc020d293643405a07396b6f5942">move</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_source_ccache, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_destination_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move()</a></b>: Move the contents of one ccache into another, destroying the source.  <a href="#d4e0dc020d293643405a07396b6f5942"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#731d262d2aa179451f824d320c460f58">lock</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_lock_type, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_block)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gb8c2624719ee1c4be5f1b1bc4844f0cc">cc_ccache_lock()</a></b>: Lock a ccache.  <a href="#731d262d2aa179451f824d320c460f58"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#bffbfe60a3e8da64224623df5235159a">unlock</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ge9b13c950cb6ee636c4a73d6c569a811">cc_ccache_unlock()</a></b>: Unlock a ccache.  <a href="#bffbfe60a3e8da64224623df5235159a"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#c65301a0ef050524286130185c3ec06d">get_last_default_time</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_last_default_time)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a></b>: Get the last time a ccache was the default ccache.  <a href="#c65301a0ef050524286130185c3ec06d"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#a73ffc6e33ca8155cd644aa5d702c36f">get_change_time</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_change_time)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a></b>: Get the last time a ccache changed.  <a href="#a73ffc6e33ca8155cd644aa5d702c36f"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#30719ac8b49a62bf73cb2841e397a81d">compare</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_compare_to_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g197ff60fac986634fbef8ca102ec54a5">cc_ccache_compare()</a></b>: Compare two ccache objects.  <a href="#30719ac8b49a62bf73cb2841e397a81d"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#106ee10feffc1681c7583d6aac4d33b4">get_kdc_time_offset</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_time_offset)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g1fa36a89752da4a491d2ecdad17f8b0e">cc_ccache_get_kdc_time_offset()</a></b>: Get the KDC time offset for credentials in a ccache.  <a href="#106ee10feffc1681c7583d6aac4d33b4"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#d537ad02da9b4eae3f5e51df0c58ee2e">set_kdc_time_offset</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> in_time_offset)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g519bf0ab152e5a3d2beee8a76a27d16e">cc_ccache_set_kdc_time_offset()</a></b>: Set the KDC time offset for credentials in a ccache.  <a href="#d537ad02da9b4eae3f5e51df0c58ee2e"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#bc092bd23b9081d12e695faa55913257">clear_kdc_time_offset</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g803c35f92992dc0b73e8809d13ebabab">cc_ccache_clear_kdc_time_offset()</a></b>: Clear the KDC time offset for credentials in a ccache.  <a href="#bc092bd23b9081d12e695faa55913257"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#4c815231f7e071a1dedd9aef3cedb0ef">wait_for_change</a> )(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a></b>: Wait for the next change to a ccache.  <a href="#4c815231f7e071a1dedd9aef3cedb0ef"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="ef46c1c04ceb1a0a479db84ddba7160c"></a><!-- doxytag: member="cc_ccache_f::release" ref="ef46c1c04ceb1a0a479db84ddba7160c" args=")(cc_ccache_t io_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#ef46c1c04ceb1a0a479db84ddba7160c">release</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ge517135d87d8775d77b426d57a491ef0">cc_ccache_release()</a></b>: Release memory associated with a cc_ccache_t object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>the ccache object to release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
<dl compact><dt><b>Note:</b></dt><dd>Does not modify the ccache. If you wish to remove the ccache see <a class="el" href="group__helper__macros.html#ge05b68d91bece2f99b531e96cde8d457">cc_ccache_destroy()</a>. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="86872ea5155e7ff57f4674a8f8c1d6c5"></a><!-- doxytag: member="cc_ccache_f::destroy" ref="86872ea5155e7ff57f4674a8f8c1d6c5" args=")(cc_ccache_t io_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#86872ea5155e7ff57f4674a8f8c1d6c5">destroy</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ge05b68d91bece2f99b531e96cde8d457">cc_ccache_destroy()</a></b>: Destroy a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>the ccache object to destroy and release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Destroy the ccache referred to by <em>io_ccache</em> and releases memory associated with the <em>io_ccache</em> object. After this call <em>io_ccache</em> becomes invalid. If <em>io_ccache</em> was the default ccache, the next ccache in the cache collection (if any) becomes the new default.     </td>
  </tr>
</table>
<a class="anchor" name="93c7e154d7472227b02d240a13dd53da"></a><!-- doxytag: member="cc_ccache_f::set_default" ref="93c7e154d7472227b02d240a13dd53da" args=")(cc_ccache_t io_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#93c7e154d7472227b02d240a13dd53da">set_default</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g535b92993b85d92b67fa622447afbe13">cc_ccache_set_default()</a></b>: Make a ccache the default ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>a ccache object to make the new default ccache. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="934cc9927404ee42460008b49e572158"></a><!-- doxytag: member="cc_ccache_f::get_credentials_version" ref="934cc9927404ee42460008b49e572158" args=")(cc_ccache_t in_ccache, cc_uint32 *out_credentials_version)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#934cc9927404ee42460008b49e572158">get_credentials_version</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_credentials_version)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g934f93499765bdd179bb2342ae0f0fa6">cc_ccache_get_credentials_version()</a></b>: Get the credentials version of a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_credentials_version</em>&nbsp;</td><td>on exit, the credentials version of <em>in_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
<a class="el" href="group__helper__macros.html#g934f93499765bdd179bb2342ae0f0fa6">cc_ccache_get_credentials_version()</a> returns one value of the enumerated type cc_credentials_vers. The possible return values are <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa">cc_credentials_v4</a> (if ccache's v4 principal has been set), <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668">cc_credentials_v5</a> (if ccache's v5 principal has been set), or <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35ca49ae6c35599f5860241601dcb0c9e0d">cc_credentials_v4_v5</a> (if both ccache's v4 and v5 principals have been set). A ccache's principal is set with one of <a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a>, <a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a>, <a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a>, or <a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a>.     </td>
  </tr>
</table>
<a class="anchor" name="e60fec8fe34019df7108e8deed0f52ac"></a><!-- doxytag: member="cc_ccache_f::get_name" ref="e60fec8fe34019df7108e8deed0f52ac" args=")(cc_ccache_t in_ccache, cc_string_t *out_name)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#e60fec8fe34019df7108e8deed0f52ac">get_name</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_name)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g042bea6044879ec03996b190792e3ae9">cc_ccache_get_name()</a></b>: Get the name of a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_name</em>&nbsp;</td><td>on exit, a cc_string_t representing the name of <em>in_ccache</em>. <em>out_name</em> must be released with <a class="el" href="group__helper__macros.html#ge9bebfed2d574e69f29dd341bc8a63d9">cc_string_release()</a>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="ef23df23bb71b1f01138b791bc8b6255"></a><!-- doxytag: member="cc_ccache_f::get_principal" ref="ef23df23bb71b1f01138b791bc8b6255" args=")(cc_ccache_t in_ccache, cc_uint32 in_credentials_version, cc_string_t *out_principal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#ef23df23bb71b1f01138b791bc8b6255">get_principal</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_principal)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g464aa49a2e8054c9c3c2a3410eaf5c54">cc_ccache_get_principal()</a></b>: Get the principal of a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_version</em>&nbsp;</td><td>the credentials version to get the principal for. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_principal</em>&nbsp;</td><td>on exit, a cc_string_t representing the principal of <em>in_ccache</em>. <em>out_principal</em> must be released with <a class="el" href="group__helper__macros.html#ge9bebfed2d574e69f29dd341bc8a63d9">cc_string_release()</a>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Return the principal for the ccache that was set via <a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a>, <a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a>, <a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a>, or <a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a>. Principals for v4 and v5 are separate, but should be kept synchronized for each ccache; they can be retrieved by passing cc_credentials_v4 or cc_credentials_v5 in cred_vers. Passing cc_credentials_v4_v5 will result in the error ccErrBadCredentialsVersion.     </td>
  </tr>
</table>
<a class="anchor" name="0d72ae907e8357633fe4ff2248818f42"></a><!-- doxytag: member="cc_ccache_f::set_principal" ref="0d72ae907e8357633fe4ff2248818f42" args=")(cc_ccache_t io_ccache, cc_uint32 in_credentials_version, const char *in_principal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#0d72ae907e8357633fe4ff2248818f42">set_principal</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, const char *in_principal)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a></b>: Set the principal of a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_version</em>&nbsp;</td><td>the credentials version to set the principal for. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_principal</em>&nbsp;</td><td>a C string representing the new principal of <em>in_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Set the a principal for ccache. The v4 and v5 principals can be set independently, but they should always be kept equal, up to differences in string representation between v4 and v5. Passing cc_credentials_v4_v5 in cred_vers will result in the error ccErrBadCredentialsVersion.     </td>
  </tr>
</table>
<a class="anchor" name="c4ac7d0cb5e15309978d8c4990f769a3"></a><!-- doxytag: member="cc_ccache_f::store_credentials" ref="c4ac7d0cb5e15309978d8c4990f769a3" args=")(cc_ccache_t io_ccache, const cc_credentials_union *in_credentials_union)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#c4ac7d0cb5e15309978d8c4990f769a3">store_credentials</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, const <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> *in_credentials_union)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g35c1548dbacb8907da7b8c3124eabf39">cc_ccache_store_credentials()</a></b>: Store credentials in a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_union</em>&nbsp;</td><td>the credentials to store in <em>io_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Store a copy of credentials in the ccache.<p>
See the description of the credentials types for the meaning of <a class="el" href="structcc__credentials__union.html">cc_credentials_union</a> fields.<p>
Before credentials of a specific credential type can be stored in a ccache, the corresponding principal version has to be set. For example, before you can store Kerberos v4 credentials in a ccache, the Kerberos v4 principal has to be set either by <a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a>, <a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a>, <a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a>, or <a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a>; likewise for Kerberos v5. Otherwise, ccErrBadCredentialsVersion is returned.     </td>
  </tr>
</table>
<a class="anchor" name="6cc6d19ff6044fafc3cd9f42c338341c"></a><!-- doxytag: member="cc_ccache_f::remove_credentials" ref="6cc6d19ff6044fafc3cd9f42c338341c" args=")(cc_ccache_t io_ccache, cc_credentials_t in_credentials)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#6cc6d19ff6044fafc3cd9f42c338341c">remove_credentials</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="structcc__credentials__d.html">cc_credentials_t</a> in_credentials)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ga1bbc05414ad4c17cea9cd5e5c50c7cc">cc_ccache_remove_credentials()</a></b>: Remove credentials from a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials</em>&nbsp;</td><td>the credentials to remove from <em>io_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Removes credentials from a ccache. Note that credentials must be previously acquired from the CCache API; only exactly matching credentials will be removed. (This places the burden of determining exactly which credentials to remove on the caller, but ensures there is no ambigity about which credentials will be removed.) cc_credentials_t objects can be obtained by iterating over the ccache's credentials with <a class="el" href="group__helper__macros.html#g893b31c419e71c2f528781d3036fa3ff">cc_ccache_new_credentials_iterator()</a>.<p>
If found, the credentials are removed from the ccache. The credentials parameter is not modified and should be freed by the caller. It is legitimate to call this function while an iterator is traversing the ccache, and the deletion of a credential already returned by <a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next()</a> will not disturb sequence of credentials returned by <a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next()</a>.     </td>
  </tr>
</table>
<a class="anchor" name="4efc19a7cd2ac6695da44cb7f7e9be14"></a><!-- doxytag: member="cc_ccache_f::new_credentials_iterator" ref="4efc19a7cd2ac6695da44cb7f7e9be14" args=")(cc_ccache_t in_ccache, cc_credentials_iterator_t *out_credentials_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#4efc19a7cd2ac6695da44cb7f7e9be14">new_credentials_iterator</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__credentials__iterator__d.html">cc_credentials_iterator_t</a> *out_credentials_iterator)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g893b31c419e71c2f528781d3036fa3ff">cc_ccache_new_credentials_iterator()</a></b>: Iterate over credentials in a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_credentials_iterator</em>&nbsp;</td><td>a credentials iterator for <em>io_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Allocates memory for iterator and initializes it. Successive calls to <a class="el" href="group__helper__macros.html#g0c2f41d90f478b2415b699085f8fcaa4">cc_credentials_iterator_next()</a> will return credentials from the ccache.<p>
If changes are made to the ccache while an iterator is being used on it, the iterator must return at least the intersection, and at most the union, of the set of credentials that were in the ccache when the iteration began and the set of credentials that are in the ccache when it ends.     </td>
  </tr>
</table>
<a class="anchor" name="d4e0dc020d293643405a07396b6f5942"></a><!-- doxytag: member="cc_ccache_f::move" ref="d4e0dc020d293643405a07396b6f5942" args=")(cc_ccache_t io_source_ccache, cc_ccache_t io_destination_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#d4e0dc020d293643405a07396b6f5942">move</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_source_ccache, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_destination_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move()</a></b>: Move the contents of one ccache into another, destroying the source. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_source_ccache</em>&nbsp;</td><td>a ccache object to move. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>io_destination_ccache</em>&nbsp;</td><td>a ccache object replace with the contents of <em>io_source_ccache</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
<a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move()</a> atomically copies the credentials, credential versions and principals from one ccache to another. On successful completion <em>io_source_ccache</em> will be released and the ccache it points to will be destroyed. Any credentials previously in <em>io_destination_ccache</em> will be replaced with credentials from <em>io_source_ccache</em>. The only part of <em>io_destination_ccache</em> which remains constant is the name. Any other callers referring to <em>io_destination_ccache</em> will suddenly see new data in it.<p>
Typically <a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move()</a> is used when the caller wishes to safely overwrite the contents of a ccache with new data which requires several steps to generate. <a class="el" href="group__helper__macros.html#ge1238f80c37ae89486f2ba29bcbcae38">cc_ccache_move()</a> allows the caller to create a temporary ccache (which can be destroyed if any intermediate step fails) and the atomically copy the temporary cache into the destination.     </td>
  </tr>
</table>
<a class="anchor" name="731d262d2aa179451f824d320c460f58"></a><!-- doxytag: member="cc_ccache_f::lock" ref="731d262d2aa179451f824d320c460f58" args=")(cc_ccache_t io_ccache, cc_uint32 in_lock_type, cc_uint32 in_block)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#731d262d2aa179451f824d320c460f58">lock</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_lock_type, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_block)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gb8c2624719ee1c4be5f1b1bc4844f0cc">cc_ccache_lock()</a></b>: Lock a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>the ccache object for the ccache you wish to lock. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_lock_type</em>&nbsp;</td><td>the type of lock to obtain. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_block</em>&nbsp;</td><td>whether or not the function should block if the lock cannot be obtained immediately. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Attempts to acquire an advisory lock for a ccache. Allowed values for lock_type are:<p>
<ul>
<li>cc_lock_read: a read lock. </li>
<li>cc_lock_write: a write lock </li>
<li>cc_lock_upgrade: upgrade an already-obtained read lock to a write lock </li>
<li>cc_lock_downgrade: downgrade an already-obtained write lock to a read lock</li>
</ul>
If block is cc_lock_block, <a class="el" href="structcc__ccache__f.html#731d262d2aa179451f824d320c460f58">lock()</a> will not return until the lock is acquired. If block is cc_lock_noblock, <a class="el" href="structcc__ccache__f.html#731d262d2aa179451f824d320c460f58">lock()</a> will return immediately, either acquiring the lock and returning ccNoError, or failing to acquire the lock and returning an error explaining why.<p>
To avoid having to deal with differences between thread semantics on different platforms, locks are granted per ccache, rather than per thread or per process. That means that different threads of execution have to acquire separate contexts in order to be able to synchronize with each other.<p>
The lock should be unlocked by using <a class="el" href="group__helper__macros.html#ge9b13c950cb6ee636c4a73d6c569a811">cc_ccache_unlock()</a>.<p>
<dl compact><dt><b>Note:</b></dt><dd>All locks are advisory. For example, callers which do not call <a class="el" href="group__helper__macros.html#gb8c2624719ee1c4be5f1b1bc4844f0cc">cc_ccache_lock()</a> and <a class="el" href="group__helper__macros.html#ge9b13c950cb6ee636c4a73d6c569a811">cc_ccache_unlock()</a> will not be prevented from writing to the ccache when you have a read lock. This is because the CCAPI locking was added after the first release and thus adding mandatory locks would have changed the user experience and performance of existing applications. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="bffbfe60a3e8da64224623df5235159a"></a><!-- doxytag: member="cc_ccache_f::unlock" ref="bffbfe60a3e8da64224623df5235159a" args=")(cc_ccache_t io_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#bffbfe60a3e8da64224623df5235159a">unlock</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ge9b13c950cb6ee636c4a73d6c569a811">cc_ccache_unlock()</a></b>: Unlock a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="c65301a0ef050524286130185c3ec06d"></a><!-- doxytag: member="cc_ccache_f::get_last_default_time" ref="c65301a0ef050524286130185c3ec06d" args=")(cc_ccache_t in_ccache, cc_time_t *out_last_default_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#c65301a0ef050524286130185c3ec06d">get_last_default_time</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_last_default_time)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a></b>: Get the last time a ccache was the default ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a cache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_last_default_time</em>&nbsp;</td><td>on exit, the last time the ccache was default. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
This function returns the last time when the ccache was made the default ccache. This allows clients to sort the ccaches by how recently they were default, which is useful for user listing of ccaches. If the ccache was never default, ccErrNeverDefault is returned.     </td>
  </tr>
</table>
<a class="anchor" name="a73ffc6e33ca8155cd644aa5d702c36f"></a><!-- doxytag: member="cc_ccache_f::get_change_time" ref="a73ffc6e33ca8155cd644aa5d702c36f" args=")(cc_ccache_t in_ccache, cc_time_t *out_change_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#a73ffc6e33ca8155cd644aa5d702c36f">get_change_time</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_change_time)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a></b>: Get the last time a ccache changed. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a cache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_change_time</em>&nbsp;</td><td>on exit, the last time the ccache changed. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. If the ccache was never the default ccache, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7604f23ab0c8c3e1d97f8b32c4501a895">ccErrNeverDefault</a>. Otherwise, an error code representing the failure.</dd></dl>
This function returns the time of the most recent change made to a ccache. By maintaining a local copy the caller can deduce whether or not the ccache has been modified since the previous call to <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a>.<p>
The time returned by <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a> increases whenever:<p>
<ul>
<li>a credential is stored </li>
<li>a credential is removed </li>
<li>a ccache principal is changed </li>
<li>the ccache becomes the default ccache </li>
<li>the ccache is no longer the default ccache</li>
</ul>
<dl compact><dt><b>Note:</b></dt><dd>In order to be able to compare two values returned by <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a>, the caller must use the same ccache object to acquire them. Callers should maintain a single ccache object in memory for <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a> calls rather than creating a new ccache object for every call.</dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__ccache__f.html#4c815231f7e071a1dedd9aef3cedb0ef">wait_for_change</a> </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="30719ac8b49a62bf73cb2841e397a81d"></a><!-- doxytag: member="cc_ccache_f::compare" ref="30719ac8b49a62bf73cb2841e397a81d" args=")(cc_ccache_t in_ccache, cc_ccache_t in_compare_to_ccache, cc_uint32 *out_equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#30719ac8b49a62bf73cb2841e397a81d">compare</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_compare_to_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g197ff60fac986634fbef8ca102ec54a5">cc_ccache_compare()</a></b>: Compare two ccache objects. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_compare_to_ccache</em>&nbsp;</td><td>a ccache object to compare with <em>in_ccache</em>. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_equal</em>&nbsp;</td><td>on exit, whether or not the two ccaches refer to the same ccache. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="106ee10feffc1681c7583d6aac4d33b4"></a><!-- doxytag: member="cc_ccache_f::get_kdc_time_offset" ref="106ee10feffc1681c7583d6aac4d33b4" args=")(cc_ccache_t in_ccache, cc_uint32 in_credentials_version, cc_time_t *out_time_offset)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#106ee10feffc1681c7583d6aac4d33b4">get_kdc_time_offset</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_time_offset)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g1fa36a89752da4a491d2ecdad17f8b0e">cc_ccache_get_kdc_time_offset()</a></b>: Get the KDC time offset for credentials in a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_version</em>&nbsp;</td><td>the credentials version to get the time offset for. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_time_offset</em>&nbsp;</td><td>on exit, the KDC time offset for <em>in_ccache</em> for credentials version <em>in_credentials_version</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a> if a time offset was obtained or <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7d6825aa88394eb52df80bef870d986db">ccErrTimeOffsetNotSet</a> if a time offset has not been set. On failure, an error code representing the failure. </dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__ccache__f.html#d537ad02da9b4eae3f5e51df0c58ee2e">set_kdc_time_offset</a>, <a class="el" href="structcc__ccache__f.html#bc092bd23b9081d12e695faa55913257">clear_kdc_time_offset</a></dd></dl>
Sometimes the KDC and client's clocks get out of sync. <a class="el" href="group__helper__macros.html#g1fa36a89752da4a491d2ecdad17f8b0e">cc_ccache_get_kdc_time_offset()</a> returns the difference between the KDC and client's clocks at the time credentials were acquired. This offset allows callers to figure out how much time is left on a given credential even though the end_time is based on the KDC's clock not the client's clock.     </td>
  </tr>
</table>
<a class="anchor" name="d537ad02da9b4eae3f5e51df0c58ee2e"></a><!-- doxytag: member="cc_ccache_f::set_kdc_time_offset" ref="d537ad02da9b4eae3f5e51df0c58ee2e" args=")(cc_ccache_t io_ccache, cc_uint32 in_credentials_version, cc_time_t in_time_offset)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#d537ad02da9b4eae3f5e51df0c58ee2e">set_kdc_time_offset</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> in_time_offset)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g519bf0ab152e5a3d2beee8a76a27d16e">cc_ccache_set_kdc_time_offset()</a></b>: Set the KDC time offset for credentials in a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_version</em>&nbsp;</td><td>the credentials version to get the time offset for. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_time_offset</em>&nbsp;</td><td>the new KDC time offset for <em>in_ccache</em> for credentials version <em>in_credentials_version</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__ccache__f.html#106ee10feffc1681c7583d6aac4d33b4">get_kdc_time_offset</a>, <a class="el" href="structcc__ccache__f.html#bc092bd23b9081d12e695faa55913257">clear_kdc_time_offset</a></dd></dl>
Sometimes the KDC and client's clocks get out of sync. <a class="el" href="group__helper__macros.html#g519bf0ab152e5a3d2beee8a76a27d16e">cc_ccache_set_kdc_time_offset()</a> sets the difference between the KDC and client's clocks at the time credentials were acquired. This offset allows callers to figure out how much time is left on a given credential even though the end_time is based on the KDC's clock not the client's clock.     </td>
  </tr>
</table>
<a class="anchor" name="bc092bd23b9081d12e695faa55913257"></a><!-- doxytag: member="cc_ccache_f::clear_kdc_time_offset" ref="bc092bd23b9081d12e695faa55913257" args=")(cc_ccache_t io_ccache, cc_uint32 in_credentials_version)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#bc092bd23b9081d12e695faa55913257">clear_kdc_time_offset</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> io_ccache, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_credentials_version)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g803c35f92992dc0b73e8809d13ebabab">cc_ccache_clear_kdc_time_offset()</a></b>: Clear the KDC time offset for credentials in a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_credentials_version</em>&nbsp;</td><td>the credentials version to get the time offset for. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__ccache__f.html#106ee10feffc1681c7583d6aac4d33b4">get_kdc_time_offset</a>, <a class="el" href="structcc__ccache__f.html#d537ad02da9b4eae3f5e51df0c58ee2e">set_kdc_time_offset</a></dd></dl>
Sometimes the KDC and client's clocks get out of sync. <a class="el" href="group__helper__macros.html#g803c35f92992dc0b73e8809d13ebabab">cc_ccache_clear_kdc_time_offset()</a> clears the difference between the KDC and client's clocks at the time credentials were acquired. This offset allows callers to figure out how much time is left on a given credential even though the end_time is based on the KDC's clock not the client's clock.     </td>
  </tr>
</table>
<a class="anchor" name="4c815231f7e071a1dedd9aef3cedb0ef"></a><!-- doxytag: member="cc_ccache_f::wait_for_change" ref="4c815231f7e071a1dedd9aef3cedb0ef" args=")(cc_ccache_t in_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__f.html#4c815231f7e071a1dedd9aef3cedb0ef">wait_for_change</a>)(<a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> in_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a></b>: Wait for the next change to a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache</em>&nbsp;</td><td>a ccache object. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
This function blocks until the next change is made to the ccache referenced by <em>in_ccache</em>. By repeatedly calling <a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a> from a worker thread the caller can effectively receive callbacks whenever the ccache changes. This is considerably more efficient than polling with <a class="el" href="group__helper__macros.html#gb19ef7d2b1bcfb474e18e157fb3bc9c6">cc_ccache_get_change_time()</a>.<p>
<a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a> will return whenever:<p>
<ul>
<li>a credential is stored </li>
<li>a credential is removed </li>
<li>the ccache principal is changed </li>
<li>the ccache becomes the default ccache </li>
<li>the ccache is no longer the default ccache</li>
</ul>
<dl compact><dt><b>Note:</b></dt><dd>In order to make sure that the caller doesn't miss any changes, <a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a> always returns immediately after the first time it is called on a new ccache object. Callers must use the same ccache object for successive calls to <a class="el" href="group__helper__macros.html#gc508ad0c010c88ad8ff0739b43a2b199">cc_ccache_wait_for_change()</a> rather than creating a new ccache object for every call.</dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__ccache__f.html#a73ffc6e33ca8155cd644aa5d702c36f">get_change_time</a> </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:05 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
