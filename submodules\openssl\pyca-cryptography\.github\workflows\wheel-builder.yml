name: Wheel Builder
on:
  workflow_dispatch:
    inputs:
      version:
        description: The version to build
        required: true
  push:
    tags:
      - '*.*'
      - '*.*.*'

jobs:
  manylinux:
    runs-on: ubuntu-latest
    container: ghcr.io/pyca/${{ matrix.MANYLINUX.CONTAINER }}
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - { VERSION: "cp36-cp36m", PATH: "/opt/python/cp36-cp36m/bin/python", ABI_VERSION: 'cp36' }
          - { VERSION: "pypy3.6", PATH: "/opt/pypy3.6/bin/pypy" }
          - { VERSION: "pypy3.7", PATH: "/opt/pypy3.7/bin/pypy" }
        MANYLINUX:
          - { NAME: "manylinux2010_x86_64", CONTAINER: "cryptography-manylinux2010:x86_64" }
          - { NAME: "manylinux2014_x86_64", CONTAINER: "cryptography-manylinux2014:x86_64" }
          - { name: "manylinux_2_24_x86_64", CONTAINER: "cryptography-manylinux_2_24:x86_64"}
          - { name: "musllinux_1_1_x86_64", CONTAINER: "cryptography-musllinux_1_1:x86_64"}
        exclude:
          # There are no readily available musllinux PyPy distributions
          - PYTHON: { VERSION: "pypy3.6", PATH: "/opt/pypy3.6/bin/pypy" }
            MANYLINUX: { name: "musllinux_1_1_x86_64", CONTAINER: "cryptography-musllinux_1_1:x86_64" }
          - PYTHON: { VERSION: "pypy3.7", PATH: "/opt/pypy3.7/bin/pypy" }
            MANYLINUX: { name: "musllinux_1_1_x86_64", CONTAINER: "cryptography-musllinux_1_1:x86_64"}
    name: "${{ matrix.PYTHON.VERSION }} for ${{ matrix.MANYLINUX.NAME }}"
    steps:
      - uses: actions/checkout@v1 # Need v1 because manylinux2010 can't run node from v2
        with:
          # The tag to build or the tag received by the tag event
          ref: ${{ github.event.inputs.version || github.ref }}
      - run: ${{ matrix.PYTHON.PATH }} -m venv .venv
      - name: Install Python dependencies
        run: .venv/bin/pip install -U pip wheel cffi setuptools-rust
      - name: Make sdist
        run: .venv/bin/python setup.py sdist
      - run: tar zxvf dist/cryptography*.tar.gz && mkdir tmpwheelhouse
      - name: Build the wheel
        run: |
          if [ -n "${{ matrix.PYTHON.ABI_VERSION }}" ]; then
              PY_LIMITED_API="--py-limited-api=${{ matrix.PYTHON.ABI_VERSION }}"
          fi
          cd cryptography*
          LDFLAGS="-L/opt/pyca/cryptography/openssl/lib" \
              CFLAGS="-I/opt/pyca/cryptography/openssl/include -Wl,--exclude-libs,ALL" \
              ../.venv/bin/python setup.py bdist_wheel $PY_LIMITED_API && mv dist/cryptography*.whl ../tmpwheelhouse
        env:
          RUSTUP_HOME: /root/.rustup
      - run: auditwheel repair --plat ${{ matrix.MANYLINUX.NAME }} tmpwheelhouse/cryptograph*.whl -w wheelhouse/
      - run: unzip wheelhouse/*.whl -d execstack.check
      - run: |
          results=$(readelf -lW execstack.check/cryptography/hazmat/bindings/*.so)
          count=$(echo "$results" | grep -c 'GNU_STACK.*[R ][W ]E' || true)
          if [ "$count" -ne 0 ]; then
            exit 1
          else
            exit 0
          fi
      - run: .venv/bin/pip install cryptography --no-index -f wheelhouse/
      - run: |
          .venv/bin/python -c "from cryptography.hazmat.backends.openssl.backend import backend;print('Loaded: ' + backend.openssl_version_text());print('Linked Against: ' + backend._ffi.string(backend._lib.OPENSSL_VERSION_TEXT).decode('ascii'))"
      - run: mkdir cryptography-wheelhouse
      - run: mv wheelhouse/cryptography*.whl cryptography-wheelhouse/
      - uses: actions/upload-artifact@v1
        with:
          name: "cryptography-${{ github.event.inputs.version }}-${{ matrix.MANYLINUX.NAME }}-${{ matrix.PYTHON.VERSION }}"
          path: cryptography-wheelhouse/

  macos:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        PYTHON:
          - VERSION: '3.8'
            ABI_VERSION: 'cp36'
            DOWNLOAD_URL: 'https://www.python.org/ftp/python/3.8.10/python-3.8.10-macosx10.9.pkg'
            BIN_PATH: '/Library/Frameworks/Python.framework/Versions/3.8/bin/python3'
          - VERSION: 'pypy-3.7'
            BIN_PATH: 'pypy3'
    name: "${{ matrix.PYTHON.VERSION }} ABI ${{ matrix.PYTHON.ABI_VERSION }} macOS"
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          # The tag to build or the tag received by the tag event
          ref: ${{ github.event.inputs.version || github.ref }}
          persist-credentials: false
      - name: Setup python
        run: |
          curl "$PYTHON_DOWNLOAD_URL" -o python.pkg
          sudo installer -pkg python.pkg -target /
        env:
          PYTHON_DOWNLOAD_URL: ${{ matrix.PYTHON.DOWNLOAD_URL }}
        if: contains(matrix.PYTHON.VERSION, 'pypy') == false
      - name: Setup pypy
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
        if: contains(matrix.PYTHON.VERSION, 'pypy')
      - run: ${{ matrix.PYTHON.BIN_PATH }} -m pip install -U requests
      - name: Download OpenSSL
        run: |
            ${{ matrix.PYTHON.BIN_PATH }} .github/workflows/download_openssl.py macos openssl-macos-x86-64
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: stable
          override: true
          default: true

      - run: ${{ matrix.PYTHON.BIN_PATH }} -m venv venv
      - run: venv/bin/pip install -U pip wheel cffi setuptools-rust
      - run: venv/bin/python setup.py sdist
      - run: tar zxvf dist/cryptography*.tar.gz && mkdir wheelhouse
      - name: Build the wheel
        run: |
          cd cryptography*
          CRYPTOGRAPHY_SUPPRESS_LINK_FLAGS="1" \
              LDFLAGS="${HOME}/openssl-macos-x86-64/lib/libcrypto.a ${HOME}/openssl-macos-x86-64/lib/libssl.a" \
              CFLAGS="-I${HOME}/openssl-macos-x86-64/include -mmacosx-version-min=10.10 -march=core2" \
              ../venv/bin/python setup.py bdist_wheel --py-limited-api=${{ matrix.PYTHON.ABI_VERSION }} && mv dist/cryptography*.whl ../wheelhouse
        env:
          MACOSX_DEPLOYMENT_TARGET:  "10.10"
      - run: venv/bin/pip install -f wheelhouse --no-index cryptography
      - run: |
          venv/bin/python -c "from cryptography.hazmat.backends.openssl.backend import backend;print('Loaded: ' + backend.openssl_version_text());print('Linked Against: ' + backend._ffi.string(backend._lib.OPENSSL_VERSION_TEXT).decode('ascii'))"

      - run: mkdir cryptography-wheelhouse
      - run: mv wheelhouse/cryptography*.whl cryptography-wheelhouse/
      - uses: actions/upload-artifact@v2.2.0
        with:
          name: "cryptography-${{ github.event.inputs.version }}-macOS-${{ matrix.PYTHON.ABI_VERSION }}"
          path: cryptography-wheelhouse/

  windows:
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        WINDOWS:
          - {ARCH: 'x86', WINDOWS: 'win32', RUST_TRIPLE: 'i686-pc-windows-msvc'}
          - {ARCH: 'x64', WINDOWS: 'win64', RUST_TRIPLE: 'x86_64-pc-windows-msvc'}
        PYTHON:
          - {VERSION: "3.8", MSVC_VERSION: "2019", "ABI_VERSION": "cp36"}
          - {VERSION: "pypy-3.7", MSVC_VERSION: "2019"}
        exclude:
          # We need to exclude the below configuration because there is no 32-bit pypy3
          - WINDOWS: {ARCH: 'x86', WINDOWS: 'win32', RUST_TRIPLE: 'i686-pc-windows-msvc'}
            PYTHON: {VERSION: "pypy-3.7", MSVC_VERSION: "2019"}
    name: "${{ matrix.PYTHON.VERSION }} ${{ matrix.WINDOWS.WINDOWS }} ${{ matrix.PYTHON.ABI_VERSION }}"
    steps:
      - uses: actions/checkout@v2.3.4
        with:
          # The tag to build or the tag received by the tag event
          ref: ${{ github.event.inputs.version || github.ref }}
          persist-credentials: false
      - name: Setup python
        uses: actions/setup-python@v2.2.2
        with:
          python-version: ${{ matrix.PYTHON.VERSION }}
          architecture: ${{ matrix.WINDOWS.ARCH }}
      - uses: actions-rs/toolchain@v1.0.7
        with:
          profile: minimal
          toolchain: stable
          override: true
          default: true
          target: ${{ matrix.WINDOWS.RUST_TRIPLE }}

      - run: pip install requests
      - name: Download OpenSSL
        run: |
            python .github/workflows/download_openssl.py windows openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}
            echo "INCLUDE=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}/include;$INCLUDE" >> $GITHUB_ENV
            echo "LIB=C:/openssl-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.MSVC_VERSION }}/lib;$LIB" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash

      - run: python -m pip install -U pip wheel cffi setuptools-rust
      - run: python setup.py sdist
      - run: tar zxvf dist/cryptography*.tar.gz && mkdir wheelhouse
        shell: bash
      - run: cd cryptography* && python setup.py bdist_wheel --py-limited-api=${{ matrix.PYTHON.ABI_VERSION }} && mv dist/cryptography*.whl ../wheelhouse
      - run: pip install -f wheelhouse --no-index cryptography
      - name: Print the OpenSSL we built and linked against
        run: |
            python -c "from cryptography.hazmat.backends.openssl.backend import backend;print('Loaded: ' + backend.openssl_version_text());print('Linked Against: ' + backend._ffi.string(backend._lib.OPENSSL_VERSION_TEXT).decode('ascii'))"

      - run: mkdir cryptography-wheelhouse
      - run: move wheelhouse\cryptography*.whl cryptography-wheelhouse\
      - uses: actions/upload-artifact@v2.2.0
        with:
          name: "cryptography-${{ github.event.inputs.version }}-${{ matrix.WINDOWS.WINDOWS }}-${{ matrix.PYTHON.VERSION }}-${{ matrix.PYTHON.ABI_VERSION}}"
          path: cryptography-wheelhouse\
