openssl_conf = openssl_init

# Comment out the next line to ignore configuration errors
config_diagnostics = 1

.include fipsmodule.cnf

[openssl_init]
providers = provider_sect
alg_section = evp_properties

[evp_properties]
# Ensure FIPS non-approved algorithms in the FIPS module are suppressed (e.g.
# TEST-RAND). This also means that EVP_default_properties_is_fips_enabled()
# returns the expected value
default_properties = "fips=yes"

[provider_sect]
fips = fips_sect
