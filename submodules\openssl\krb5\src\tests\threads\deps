#
# Generated makefile dependencies follow.
#
$(OUTPRE)t_rcache.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h t_rcache.c
$(OUTPRE)gss-perf.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h \
  gss-perf.c
$(OUTPRE)init_ctx.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/krb5.h \
  init_ctx.c
$(OUTPRE)profread.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/krb5.h \
  profread.c
$(OUTPRE)prof1.$(OBJEXT): $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) prof1.c
