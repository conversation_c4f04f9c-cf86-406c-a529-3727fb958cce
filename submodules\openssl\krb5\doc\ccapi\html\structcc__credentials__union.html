<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_union Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_union Struct Reference<br>
<small>
[<a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a>]</small>
</h1><!-- doxytag: class="cc_credentials_union" --><h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__union.html#2d41fe5eaeafcfae38d60dae26985ac2">version</a>
<li>union {
<ul>
<li>&nbsp;&nbsp;&nbsp;<a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a> * &nbsp;&nbsp;&nbsp;<a class="el" href="structcc__credentials__union.html#5cd1c69704fe9706f69fdde1d954bba5">credentials_v4</a>
<li>&nbsp;&nbsp;&nbsp;<a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a> * &nbsp;&nbsp;&nbsp;<a class="el" href="structcc__credentials__union.html#9d28c534b1b7c41da162f26620e92ded">credentials_v5</a>
</ul>
<li>} <a class="el" href="structcc__credentials__union.html#9e7108eff62e2df10a768cec653fe9c3">credentials</a>
</ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="2d41fe5eaeafcfae38d60dae26985ac2"></a><!-- doxytag: member="cc_credentials_union::version" ref="2d41fe5eaeafcfae38d60dae26985ac2" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__union.html#2d41fe5eaeafcfae38d60dae26985ac2">version</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The credentials version of this credentials object.     </td>
  </tr>
</table>
<a class="anchor" name="5cd1c69704fe9706f69fdde1d954bba5"></a><!-- doxytag: member="cc_credentials_union::credentials_v4" ref="5cd1c69704fe9706f69fdde1d954bba5" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a>* <a class="el" href="structcc__credentials__union.html#5cd1c69704fe9706f69fdde1d954bba5">credentials_v4</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
If <em>version</em> is <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa">cc_credentials_v4</a>, a pointer to a <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a>.     </td>
  </tr>
</table>
<a class="anchor" name="9d28c534b1b7c41da162f26620e92ded"></a><!-- doxytag: member="cc_credentials_union::credentials_v5" ref="9d28c534b1b7c41da162f26620e92ded" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a>* <a class="el" href="structcc__credentials__union.html#9d28c534b1b7c41da162f26620e92ded">credentials_v5</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
If <em>version</em> is <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668">cc_credentials_v5</a>, a pointer to a <a class="el" href="structcc__credentials__v5__t.html">cc_credentials_v5_t</a>.     </td>
  </tr>
</table>
<a class="anchor" name="9e7108eff62e2df10a768cec653fe9c3"></a><!-- doxytag: member="cc_credentials_union::credentials" ref="9e7108eff62e2df10a768cec653fe9c3" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">union { ... } 				 <a class="el" href="structcc__credentials__union.html#9e7108eff62e2df10a768cec653fe9c3">credentials</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The credentials.     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
