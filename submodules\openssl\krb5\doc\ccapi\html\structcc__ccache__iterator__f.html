<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_ccache_iterator_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_ccache_iterator_f Struct Reference</h1><!-- doxytag: class="cc_ccache_iterator_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_ccache_iterator_t. For more information see <a class="el" href="group__cc__ccache__iterator__reference.html">cc_ccache_iterator_t Overview</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#4df0298826e5004ca873b005d6d3b9d0">release</a> )(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> io_ccache_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g34f37496fb8bc414aafb0b265afecb1b">cc_ccache_iterator_release()</a></b>: Release memory associated with a cc_ccache_iterator_t object.  <a href="#4df0298826e5004ca873b005d6d3b9d0"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#6f1c5bf2a8c3ca2fb1761a039fbf30cb">next</a> )(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> in_ccache_iterator, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gcff0b3e247a2adc95442324fec6c5651">cc_ccache_iterator_next()</a></b>: Get the next ccache in the cache collection.  <a href="#6f1c5bf2a8c3ca2fb1761a039fbf30cb"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#3a2fc1000215e7e8a2ef5b29eb4af890">clone</a> )(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> in_ccache_iterator, <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> *out_ccache_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g904d7757fd7ac40f4ee9b448a389f2dd">cc_ccache_iterator_clone()</a></b>: Make a copy of a ccache iterator.  <a href="#3a2fc1000215e7e8a2ef5b29eb4af890"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="4df0298826e5004ca873b005d6d3b9d0"></a><!-- doxytag: member="cc_ccache_iterator_f::release" ref="4df0298826e5004ca873b005d6d3b9d0" args=")(cc_ccache_iterator_t io_ccache_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#4df0298826e5004ca873b005d6d3b9d0">release</a>)(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> io_ccache_iterator)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g34f37496fb8bc414aafb0b265afecb1b">cc_ccache_iterator_release()</a></b>: Release memory associated with a cc_ccache_iterator_t object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_ccache_iterator</em>&nbsp;</td><td>the ccache iterator object to release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="6f1c5bf2a8c3ca2fb1761a039fbf30cb"></a><!-- doxytag: member="cc_ccache_iterator_f::next" ref="6f1c5bf2a8c3ca2fb1761a039fbf30cb" args=")(cc_ccache_iterator_t in_ccache_iterator, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#6f1c5bf2a8c3ca2fb1761a039fbf30cb">next</a>)(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> in_ccache_iterator, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gcff0b3e247a2adc95442324fec6c5651">cc_ccache_iterator_next()</a></b>: Get the next ccache in the cache collection. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache_iterator</em>&nbsp;</td><td>a ccache iterator object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, the next ccache in the cache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a> if the next ccache in the cache collection was obtained or <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b748d5a55ed773e002ccc271beb4512c0a">ccIteratorEnd</a> if there are no more ccaches. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="3a2fc1000215e7e8a2ef5b29eb4af890"></a><!-- doxytag: member="cc_ccache_iterator_f::clone" ref="3a2fc1000215e7e8a2ef5b29eb4af890" args=")(cc_ccache_iterator_t in_ccache_iterator, cc_ccache_iterator_t *out_ccache_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__ccache__iterator__f.html#3a2fc1000215e7e8a2ef5b29eb4af890">clone</a>)(<a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> in_ccache_iterator, <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> *out_ccache_iterator)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g904d7757fd7ac40f4ee9b448a389f2dd">cc_ccache_iterator_clone()</a></b>: Make a copy of a ccache iterator. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_ccache_iterator</em>&nbsp;</td><td>a ccache iterator object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache_iterator</em>&nbsp;</td><td>on exit, a copy of <em>in_ccache_iterator</em>. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:05 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
