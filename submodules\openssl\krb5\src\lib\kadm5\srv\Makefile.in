mydir=lib$(S)kadm5$(S)srv
BUILDTOP=$(REL)..$(S)..$(S)..
LOCALINCLUDES = -I$(BUILDTOP)/include/kadm5 \
	-I$(top_srcdir)/lib/gssapi/krb5 -I$(top_srcdir)/lib/gssapi/generic \
	-I$(BUILDTOP)/lib/gssapi/krb5 -I$(BUILDTOP)/lib/gssapi/generic
DEFINES = @HESIOD_DEFS@

##DOSBUILDTOP = ..\..\..
##DOSLIBNAME = libkadm5srv.lib

LIBBASE=kadm5srv_mit
LIBMAJOR=11
LIBMINOR=0
STOBJLISTS=../OBJS.ST OBJS.ST

SHLIB_EXPDEPS=\
	$(TOPLIBD)/libgssrpc$(SHLIBEXT) \
	$(TOPLIBD)/libgssapi_krb5$(SHLIBEXT) \
	$(TOPLIBD)/libkdb5$(SHLIBEXT) \
	$(TOPLIBD)/libkrb5$(SHLIBEXT) \
	$(TOPLIBD)/libk5crypto$(SHLIBEXT) \
	$(COM_ERR_DEPLIB) $(SUPPORT_LIBDEP)
SHLIB_EXPLIBS =	-lgssrpc -lgssapi_krb5 -lkdb5 $(KDB5_DB_LIB) \
		-lkrb5 -lk5crypto $(SUPPORT_LIB) -lcom_err @GEN_LIB@ $(LIBS)
RELDIR=kadm5/srv

SRCS =	$(srcdir)/pwqual.c \
	$(srcdir)/kadm5_hook.c \
	$(srcdir)/pwqual_dict.c \
	$(srcdir)/pwqual_empty.c \
	$(srcdir)/pwqual_hesiod.c \
	$(srcdir)/pwqual_princ.c \
	$(srcdir)/svr_policy.c \
	$(srcdir)/svr_principal.c \
	$(srcdir)/server_kdb.c \
	$(srcdir)/server_misc.c \
	$(srcdir)/server_init.c \
	$(srcdir)/svr_iters.c \
	$(srcdir)/svr_chpass_util.c \
	$(srcdir)/adb_xdr.c 

OBJS =	pwqual.$(OBJEXT) \
	pwqual_dict.$(OBJEXT) \
	pwqual_empty.$(OBJEXT) \
	pwqual_hesiod.$(OBJEXT) \
	pwqual_princ.$(OBJEXT) \
	kadm5_hook.$(OBJEXT) \
	svr_policy.$(OBJEXT) \
	svr_principal.$(OBJEXT) \
	server_kdb.$(OBJEXT) \
	server_misc.$(OBJEXT) \
	server_init.$(OBJEXT) \
	svr_iters.$(OBJEXT) \
	svr_chpass_util.$(OBJEXT) \
	adb_xdr.$(OBJEXT) 

STLIBOBJS = \
	pwqual.o \
	pwqual_dict.o \
	pwqual_empty.o \
	pwqual_hesiod.o \
	pwqual_princ.o \
	kadm5_hook.o \
	svr_policy.o \
	svr_principal.o \
	server_kdb.o \
	server_misc.o \
	server_init.o \
	svr_iters.o \
	svr_chpass_util.o \
	adb_xdr.o

all-unix: all-liblinks
all-windows: $(OBJS)

generate-files-mac: darwin.exports

check-windows:

clean-windows::

clean-unix:: clean-liblinks clean-libs clean-libobjs

install: install-libs

install-unix:
	$(RM) $(DESTDIR)$(KRB5_LIBDIR)/libkadm5srv$(DEPLIBEXT)
	(cd $(DESTDIR)$(KRB5_LIBDIR) && $(LN_S) lib$(LIBBASE)$(DEPLIBEXT) \
		libkadm5srv$(DEPLIBEXT))

@lib_frag@
@libobj_frag@

