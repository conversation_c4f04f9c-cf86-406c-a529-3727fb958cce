=pod

=head1 NAME

d2i_DHparams, i2d_DHparams - PKCS#3 DH parameter functions

=head1 SYNOPSIS

 #include <openssl/dh.h>

 DH *d2i_DHparams(DH **a, const unsigned char **pp, long length);
 int i2d_DHparams(DH *a, unsigned char **pp);

=head1 DESCRIPTION

These functions decode and encode PKCS#3 DH parameters using the
DHparameter structure described in PKCS#3.

Otherwise these behave in a similar way to d2i_X509() and i2d_X509()
described in the L<d2i_X509(3)> manual page.

=head1 RETURN VALUES

d2i_DHparams() returns a valid B<DH> structure or NULL if an error occurred.

i2d_DHparams() returns the length of encoded data on success or a value which
is less than or equal to 0 on error.

=head1 SEE ALSO

L<d2i_X509(3)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
