<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : Basic Types</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>Basic Types</h1>
<p>
<h2>Typedefs</h2>
<ul>
<li>typedef uint32_t <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a>
<li>typedef int32_t <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>
<li>typedef int64_t <a class="el" href="group__ccapi__types__reference.html#gb6fb75be8c6af177e86f1b3bc562f031">cc_int64</a>
<li>typedef uint64_t <a class="el" href="group__ccapi__types__reference.html#g44f60a4b4db805be9d55b476dd949a66">cc_uint64</a>
<li>typedef <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a>
</ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="ga00783c3f4aa70580d0900b1a79aab9d"></a><!-- doxytag: member="CredentialsCache.h::cc_uint32" ref="ga00783c3f4aa70580d0900b1a79aab9d" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef uint32_t <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Unsigned 32-bit integer type     </td>
  </tr>
</table>
<a class="anchor" name="g0ce639c8d65dc6367fb361d5bbcea874"></a><!-- doxytag: member="CredentialsCache.h::cc_int32" ref="g0ce639c8d65dc6367fb361d5bbcea874" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef int32_t <a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Signed 32-bit integer type     </td>
  </tr>
</table>
<a class="anchor" name="gb6fb75be8c6af177e86f1b3bc562f031"></a><!-- doxytag: member="CredentialsCache.h::cc_int64" ref="gb6fb75be8c6af177e86f1b3bc562f031" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef int64_t <a class="el" href="group__ccapi__types__reference.html#gb6fb75be8c6af177e86f1b3bc562f031">cc_int64</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Unsigned 64-bit integer type     </td>
  </tr>
</table>
<a class="anchor" name="g44f60a4b4db805be9d55b476dd949a66"></a><!-- doxytag: member="CredentialsCache.h::cc_uint64" ref="g44f60a4b4db805be9d55b476dd949a66" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef uint64_t <a class="el" href="group__ccapi__types__reference.html#g44f60a4b4db805be9d55b476dd949a66">cc_uint64</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Signed 64-bit integer type     </td>
  </tr>
</table>
<a class="anchor" name="ge7a754cfe5664beadddaa100646c9742"></a><!-- doxytag: member="CredentialsCache.h::cc_time_t" ref="ge7a754cfe5664beadddaa100646c9742" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The cc_time_t type is used to represent a time in seconds. The time must be stored as the number of seconds since midnight GMT on January 1, 1970.     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
