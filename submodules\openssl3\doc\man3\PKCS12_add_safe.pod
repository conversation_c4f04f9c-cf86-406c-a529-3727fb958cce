=pod

=head1 NAME

PKCS12_add_safe, PKCS12_add_safe_ex,
PKCS12_add_safes, PKCS12_add_safes_ex - Create and add objects to a PKCS#12 structure

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 int PKCS12_add_safe(STACK_OF(PKCS7) **psafes, STACK_OF(PKCS12_SAFEBAG) *bags,
                    int safe_nid, int iter, const char *pass);
 int PKCS12_add_safe_ex(STACK_OF(PKCS7) **psafes, STACK_OF(PKCS12_SAFEBAG) *bags,
                        int safe_nid, int iter, const char *pass,
                        OSSL_LIB_CTX *ctx, const char *propq);

 PKCS12 *PKCS12_add_safes(STACK_OF(PKCS7) *safes, int p7_nid);
 PKCS12 *PKCS12_add_safes_ex(STACK_OF(PKCS7) *safes, int p7_nid,
                             OSSL_LIB_CTX *ctx, const char *propq);

=head1 DESCRIPTION

PKCS12_add_safe() creates a new PKCS7 contentInfo containing the supplied
B<PKCS12_SAFEBAG>s and adds this to a set of PKCS7 contentInfos. Its type
depends on the value of B<safe_nid>:

=over 4

=item * If I<safe_nid> is -1, a plain PKCS7 I<data> contentInfo is created.

=item * If I<safe_nid> is a valid PBE algorithm NID, a PKCS7 B<encryptedData>
contentInfo is created. The algorithm uses I<pass> as the passphrase and I<iter>
as the iteration count. If I<iter> is zero then a default value for iteration
count of 2048 is used.

=item * If I<safe_nid> is 0, a PKCS7 B<encryptedData> contentInfo is created using
a default encryption algorithm, currently B<NID_pbe_WithSHA1And3_Key_TripleDES_CBC>.

=back

PKCS12_add_safe_ex() is identical to PKCS12_add_safe() but allows for a library
context I<ctx> and property query I<propq> to be used to select algorithm
implementations.

PKCS12_add_safes() creates a B<PKCS12> structure containing the supplied set of
PKCS7 contentInfos. The I<safes> are enclosed first within a PKCS7 contentInfo
of type I<p7_nid>. Currently the only supported type is B<NID_pkcs7_data>.

PKCS12_add_safes_ex() is identical to PKCS12_add_safes() but allows for a
library context I<ctx> and property query I<propq> to be used to select
algorithm implementations.

=head1 NOTES

PKCS12_add_safe() makes assumptions regarding the encoding of the given pass
phrase.
See L<passphrase-encoding(7)> for more information.

=head1 RETURN VALUES

PKCS12_add_safe() returns a value of 1 indicating success or 0 for failure.

PKCS12_add_safes() returns a valid B<PKCS12> structure or NULL if an error occurred.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<PKCS12_create(3)>

=head1 HISTORY

PKCS12_add_safe_ex() and PKCS12_add_safes_ex() were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
