#!/bin/sh

# args: srcdir srctop-from-srcdir header-path

d=`pwd`
head -1 $1/configure.ac > config-in.tmp
echo "AC_CONFIG_HEADER(fooconfig.h:$d/fooconfig-h.tmp)" >> config-in.tmp
tail +2 $1/configure.ac | grep -v AC_CONFIG_HEADER >> config-in.tmp
mv -f config-in.tmp config-in.ac~

if (cd $1 && autoheader --include=$2 $d/config-in.ac~) > /dev/null; then
  rm -rf $1/autom4te.cache config-in.ac~
else
  rm -rf $1/autom4te.cache
# config-in.ac~ fooconfig-h.tmp
  echo autoheader failed, eek
  exit 1
fi

awk '/^#undef/ { print $2; }' < fooconfig-h.tmp | sort > acsyms.here
rm -f fooconfig-h.tmp
awk '/^#undef/ { print $2; }' < $3 | sort > acsyms.there

comm -23 acsyms.here acsyms.there > acsyms.extra
rm -f acsyms.here acsyms.there

if test -s acsyms.extra; then
  echo ERROR: Symbol or symbols defined here but not in `basename $3`: `cat acsyms.extra`
  rm -f acsyms.extra
  exit 1
fi
rm -f acsyms.extra
exit 0
