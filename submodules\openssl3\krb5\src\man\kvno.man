.\" Man page generated from reStructuredText.
.
.TH "KVNO" "1" " " "1.20" "MIT Kerberos"
.SH NAME
kvno \- print key version numbers of Kerberos principals
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkvno\fP
[\fB\-c\fP \fIccache\fP]
[\fB\-e\fP \fIetype\fP]
[\fB\-k\fP \fIkeytab\fP]
[\fB\-q\fP]
[\fB\-u\fP | \fB\-S\fP \fIsname\fP]
[\fB\-P\fP]
[\fB\-\-cached\-only\fP]
[\fB\-\-no\-store\fP]
[\fB\-\-out\-cache\fP \fIcache\fP]
[[{\fB\-F\fP \fIcert_file\fP | {\fB\-I\fP | \fB\-U\fP} \fIfor_user\fP} [\fB\-P\fP]] | \fB\-\-u2u\fP \fIccache\fP]
\fIservice1 service2\fP ...
.SH DESCRIPTION
.sp
kvno acquires a service ticket for the specified Kerberos principals
and prints out the key version numbers of each.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-c\fP \fIccache\fP
Specifies the name of a credentials cache to use (if not the
default)
.TP
\fB\-e\fP \fIetype\fP
Specifies the enctype which will be requested for the session key
of all the services named on the command line.  This is useful in
certain backward compatibility situations.
.TP
\fB\-k\fP \fIkeytab\fP
Decrypt the acquired tickets using \fIkeytab\fP to confirm their
validity.
.TP
\fB\-q\fP
Suppress printing output when successful.  If a service ticket
cannot be obtained, an error message will still be printed and
kvno will exit with nonzero status.
.TP
\fB\-u\fP
Use the unknown name type in requested service principal names.
This option Cannot be used with \fI\-S\fP\&.
.TP
\fB\-P\fP
Specifies that the \fIservice1 service2\fP ...  arguments are to be
treated as services for which credentials should be acquired using
constrained delegation.  This option is only valid when used in
conjunction with protocol transition.
.TP
\fB\-S\fP \fIsname\fP
Specifies that the \fIservice1 service2\fP ... arguments are
interpreted as hostnames, and the service principals are to be
constructed from those hostnames and the service name \fIsname\fP\&.
The service hostnames will be canonicalized according to the usual
rules for constructing service principals.
.TP
\fB\-I\fP \fIfor_user\fP
Specifies that protocol transition (S4U2Self) is to be used to
acquire a ticket on behalf of \fIfor_user\fP\&.  If constrained
delegation is not requested, the service name must match the
credentials cache client principal.
.TP
\fB\-U\fP \fIfor_user\fP
Same as \-I, but treats \fIfor_user\fP as an enterprise name.
.TP
\fB\-F\fP \fIcert_file\fP
Specifies that protocol transition is to be used, identifying the
client principal with the X.509 certificate in \fIcert_file\fP\&.  The
certificate file must be in PEM format.
.TP
\fB\-\-cached\-only\fP
Only retrieve credentials already present in the cache, not from
the KDC.  (Added in release 1.19.)
.TP
\fB\-\-no\-store\fP
Do not store retrieved credentials in the cache.  If
\fB\-\-out\-cache\fP is also specified, credentials will still be
stored into the output credential cache.  (Added in release 1.19.)
.TP
\fB\-\-out\-cache\fP \fIccache\fP
Initialize \fIccache\fP and store all retrieved credentials into it.
Do not store acquired credentials in the input cache.  (Added in
release 1.19.)
.TP
\fB\-\-u2u\fP \fIccache\fP
Requests a user\-to\-user ticket.  \fIccache\fP must contain a local
krbtgt ticket for the server principal.  The reported version
number will typically be 0, as the resulting ticket is not
encrypted in the server\(aqs long\-term key.
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH FILES
.INDENT 0.0
.TP
.B \fB@CCNAME@\fP
Default location of the credentials cache
.UNINDENT
.SH SEE ALSO
.sp
kinit(1), kdestroy(1), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
