=pod

=head1 NAME

EVP_PKEY,
EVP_PKEY_new,
EVP_PKEY_up_ref,
EVP_PKEY_dup,
EVP_PKEY_free,
EVP_PKEY_new_raw_private_key_ex,
EVP_PKEY_new_raw_private_key,
EVP_PKEY_new_raw_public_key_ex,
EVP_PKEY_new_raw_public_key,
EVP_PKEY_new_CMAC_key,
EVP_PKEY_new_mac_key,
EVP_PKEY_get_raw_private_key,
EVP_PKEY_get_raw_public_key
- public/private key allocation and raw key handling functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 typedef evp_pkey_st EVP_PKEY;

 EVP_PKEY *EVP_PKEY_new(void);
 int EVP_PKEY_up_ref(EVP_PKEY *key);
 EVP_PKEY *EVP_PKEY_dup(EVP_PKEY *key);
 void EVP_PKEY_free(<PERSON>VP_PKEY *key);

 E<PERSON>_PKEY *EVP_PKEY_new_raw_private_key_ex(OSSL_LIB_CTX *libctx,
                                           const char *keytype,
                                           const char *propq,
                                           const unsigned char *key,
                                           size_t keylen);
 EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
                                        const unsigned char *key, size_t keylen);
 EVP_PKEY *EVP_PKEY_new_raw_public_key_ex(OSSL_LIB_CTX *libctx,
                                          const char *keytype,
                                          const char *propq,
                                          const unsigned char *key,
                                          size_t keylen);
 EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
                                       const unsigned char *key, size_t keylen);
 EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
                                int keylen);

 int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
                                  size_t *len);
 int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
                                 size_t *len);

The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 EVP_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
                                 size_t len, const EVP_CIPHER *cipher);

=head1 DESCRIPTION

B<EVP_PKEY> is a generic structure to hold diverse types of asymmetric keys
(also known as "key pairs"), and can be used for diverse operations, like
signing, verifying signatures, key derivation, etc.  The asymmetric keys
themselves are often referred to as the "internal key", and are handled by
backends, such as providers (through L<EVP_KEYMGMT(3)>) or B<ENGINE>s.

Conceptually, an B<EVP_PKEY> internal key may hold a private key, a public
key, or both (a keypair), and along with those, key parameters if the key type
requires them.  The presence of these components determine what operations can
be made; for example, signing normally requires the presence of a private key,
and verifying normally requires the presence of a public key.

=for comment ED signature require both the private and public key...

B<EVP_PKEY> has also been used for MAC algorithm that were conceived as
producing signatures, although not being public key algorithms; "POLY1305",
"SIPHASH", "HMAC", "CMAC".  This usage is considered legacy and is discouraged
in favor of the L<EVP_MAC(3)> API.

The EVP_PKEY_new() function allocates an empty B<EVP_PKEY> structure which is
used by OpenSSL to store public and private keys. The reference count is set to
B<1>.

EVP_PKEY_up_ref() increments the reference count of I<key>.

EVP_PKEY_dup() duplicates the I<key>. The I<key> must not be ENGINE based or
a raw key, otherwise the duplication will fail.

EVP_PKEY_free() decrements the reference count of I<key> and, if the reference
count is zero, frees it up. If I<key> is NULL, nothing is done.

EVP_PKEY_new_raw_private_key_ex() allocates a new B<EVP_PKEY>. Unless an
engine should be used for the key type, a provider for the key is found using
the library context I<libctx> and the property query string I<propq>. The
I<keytype> argument indicates what kind of key this is. The value should be a
string for a public key algorithm that supports raw private keys, i.e one of
"X25519", "ED25519", "X448" or "ED448". I<key> points to the raw private key
data for this B<EVP_PKEY> which should be of length I<keylen>. The length
should be appropriate for the type of the key. The public key data will be
automatically derived from the given private key data (if appropriate for the
algorithm type).

EVP_PKEY_new_raw_private_key() does the same as
EVP_PKEY_new_raw_private_key_ex() except that the default library context and
default property query are used instead. If I<e> is non-NULL then the new
B<EVP_PKEY> structure is associated with the engine I<e>. The I<type> argument
indicates what kind of key this is. The value should be a NID for a public key
algorithm that supports raw private keys, i.e. one of B<EVP_PKEY_X25519>,
B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>.

EVP_PKEY_new_raw_private_key_ex() and EVP_PKEY_new_raw_private_key() may also
be used with most MACs implemented as public key algorithms, so key types such
as "HMAC", "POLY1305", "SIPHASH", or their NID form B<EVP_PKEY_POLY1305>,
B<EVP_PKEY_SIPHASH>, B<EVP_PKEY_HMAC> are also accepted.  This usage is,
as mentioned above, discouraged in favor of the L<EVP_MAC(3)> API.

EVP_PKEY_new_raw_public_key_ex() works in the same way as
EVP_PKEY_new_raw_private_key_ex() except that I<key> points to the raw
public key data. The B<EVP_PKEY> structure will be initialised without any
private key information. Algorithm types that support raw public keys are
"X25519", "ED25519", "X448" or "ED448".

EVP_PKEY_new_raw_public_key() works in the same way as
EVP_PKEY_new_raw_private_key() except that I<key> points to the raw public key
data. The B<EVP_PKEY> structure will be initialised without any private key
information. Algorithm types that support raw public keys are
B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>.

EVP_PKEY_new_mac_key() works in the same way as EVP_PKEY_new_raw_private_key().
New applications should use EVP_PKEY_new_raw_private_key() instead.

EVP_PKEY_get_raw_private_key() fills the buffer provided by I<priv> with raw
private key data. The size of the I<priv> buffer should be in I<*len> on entry
to the function, and on exit I<*len> is updated with the number of bytes
actually written. If the buffer I<priv> is NULL then I<*len> is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the private
key data. This function only works for algorithms that support raw private keys.
Currently this is: B<EVP_PKEY_HMAC>, B<EVP_PKEY_POLY1305>, B<EVP_PKEY_SIPHASH>,
B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or B<EVP_PKEY_ED448>.

EVP_PKEY_get_raw_public_key() fills the buffer provided by I<pub> with raw
public key data. The size of the I<pub> buffer should be in I<*len> on entry
to the function, and on exit I<*len> is updated with the number of bytes
actually written. If the buffer I<pub> is NULL then I<*len> is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the public
key data. This function only works for algorithms that support raw public  keys.
Currently this is: B<EVP_PKEY_X25519>, B<EVP_PKEY_ED25519>, B<EVP_PKEY_X448> or
B<EVP_PKEY_ED448>.

EVP_PKEY_new_CMAC_key() works in the same way as EVP_PKEY_new_raw_private_key()
except it is only for the B<EVP_PKEY_CMAC> algorithm type. In addition to the
raw private key data, it also takes a cipher algorithm to be used during
creation of a CMAC in the B<cipher> argument. The cipher should be a standard
encryption-only cipher. For example AEAD and XTS ciphers should not be used.

Applications should use the L<EVP_MAC(3)> API instead
and set the B<OSSL_MAC_PARAM_CIPHER> parameter on the B<EVP_MAC_CTX> object
with the name of the cipher being used.

=head1 NOTES

The B<EVP_PKEY> structure is used by various OpenSSL functions which require a
general private key without reference to any particular algorithm.

The structure returned by EVP_PKEY_new() is empty. To add a private or public
key to this empty structure use the appropriate functions described in
L<EVP_PKEY_set1_RSA(3)>, L<EVP_PKEY_set1_DSA(3)>, L<EVP_PKEY_set1_DH(3)> or
L<EVP_PKEY_set1_EC_KEY(3)>.

=head1 RETURN VALUES

EVP_PKEY_new(), EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(),
EVP_PKEY_new_CMAC_key() and EVP_PKEY_new_mac_key() return either the newly
allocated B<EVP_PKEY> structure or NULL if an error occurred.

EVP_PKEY_dup() returns the key duplicate or NULL if an error occurred.

EVP_PKEY_up_ref(), EVP_PKEY_get_raw_private_key() and
EVP_PKEY_get_raw_public_key() return 1 for success and 0 for failure.

=head1 SEE ALSO

L<EVP_PKEY_set1_RSA(3)>, L<EVP_PKEY_set1_DSA(3)>, L<EVP_PKEY_set1_DH(3)> or
L<EVP_PKEY_set1_EC_KEY(3)>

=head1 HISTORY

The
EVP_PKEY_new() and EVP_PKEY_free() functions exist in all versions of OpenSSL.

The EVP_PKEY_up_ref() function was added in OpenSSL 1.1.0.

The
EVP_PKEY_new_raw_private_key(), EVP_PKEY_new_raw_public_key(),
EVP_PKEY_new_CMAC_key(), EVP_PKEY_new_raw_private_key() and
EVP_PKEY_get_raw_public_key() functions were added in OpenSSL 1.1.1.

The EVP_PKEY_dup(), EVP_PKEY_new_raw_private_key_ex(), and
EVP_PKEY_new_raw_public_key_ex()
functions were added in OpenSSL 3.0.

The EVP_PKEY_new_CMAC_key() was deprecated in OpenSSL 3.0.

The documentation of B<EVP_PKEY> was amended in OpenSSL 3.0 to allow there to
be the private part of the keypair without the public part, where this was
previously implied to be disallowed.

=head1 COPYRIGHT

Copyright 2002-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
