=pod

=head1 NAME

DTLS_get_data_mtu - Get maximum data payload size

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 size_t DTLS_get_data_mtu(const SSL *ssl);

=head1 DESCRIPTION

This function obtains the maximum data payload size for the established
DTLS connection B<ssl>, based on the DTLS record MTU and the overhead
of the DTLS record header, encryption and authentication currently in use.

=head1 RETURN VALUES

Returns the maximum data payload size on success, or 0 on failure.

=head1 HISTORY

The DTLS_get_data_mtu() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
