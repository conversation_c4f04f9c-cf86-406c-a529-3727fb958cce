${-
  use File::Spec::Functions qw(rel2abs);

  my $bldtop = rel2abs($config{builddir});
  our %names = ( map { $_ => $bldtop.$_.".EXE" }
                 map { $unified_info{sharednames}->{$_} || () }
                 @{$unified_info{libraries}} );
  "" -}
$       ! Remove the local environment created by local_shlib.com
$
$       OPENSSL_NAMES := OPENSSL_NAMES_'F$GETJPI("","PID")'
$       IF F$TRNLNM("OSSL_FLAG",OPENSSL_NAMES) .EQS. "" THEN EXIT 0
$
$       NAMES := {- join(",", keys %names); -}
$       I = 0
$       LOOP:
$           E = F$ELEMENT(I,",",NAMES)
$           I = I + 1
$           IF E .EQS. "," THEN GOTO ENDLOOP
$           OLDV = F$TRNLNM(E,OPENSSL_NAMES)
$           DEASSIGN 'E'
$           IF OLDV .NES. "" THEN DEFINE 'E' 'OLDV'
$           GOTO LOOP
$       ENDLOOP:
$
$       DEASSIGN 'OPENSSL_NAMES' /TABLE=LNM$PROCESS_DIRECTORY
