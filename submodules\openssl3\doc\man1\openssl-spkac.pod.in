=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-spkac - SPKAC printing and generating command

=head1 SYNOPSIS

B<openssl> B<spkac>
[B<-help>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-digest> I<digest>]
[B<-key> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-passin> I<arg>]
[B<-challenge> I<string>]
[B<-pubkey>]
[B<-spkac> I<spkacname>]
[B<-spksect> I<section>]
[B<-noout>]
[B<-verify>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command processes Netscape signed public key and challenge
(SPKAC) files. It can print out their contents, verify the signature and
produce its own SPKACs from a supplied private key.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-in> I<filename>

This specifies the input filename to read from or standard input if this
option is not specified. Ignored if the B<-key> option is used.

=item B<-out> I<filename>

Specifies the output filename to write to or standard output by
default.

=item B<-digest> I<digest>

Use the specified I<digest> to sign a created SPKAC file.
The default digest algorithm is MD5.

=item B<-key> I<filename>|I<uri>

Create an SPKAC file using the private key specified by I<filename> or I<uri>.
The B<-in>, B<-noout>, B<-spksect> and B<-verify> options are ignored if
present.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-passin> I<arg>

The input file password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-challenge> I<string>

Specifies the challenge string if an SPKAC is being created.

=item B<-spkac> I<spkacname>

Allows an alternative name form the variable containing the
SPKAC. The default is "SPKAC". This option affects both
generated and input SPKAC files.

=item B<-spksect> I<section>

Allows an alternative name form the section containing the
SPKAC. The default is the default section.

=item B<-noout>

Don't output the text version of the SPKAC (not used if an
SPKAC is being created).

=item B<-pubkey>

Output the public key of an SPKAC (not used if an SPKAC is
being created).

=item B<-verify>

Verifies the digital signature on the supplied SPKAC.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 EXAMPLES

Print out the contents of an SPKAC:

 openssl spkac -in spkac.cnf

Verify the signature of an SPKAC:

 openssl spkac -in spkac.cnf -noout -verify

Create an SPKAC using the challenge string "hello":

 openssl spkac -key key.pem -challenge hello -out spkac.cnf

Example of an SPKAC, (long lines split up for clarity):

 SPKAC=MIG5MGUwXDANBgkqhkiG9w0BAQEFAANLADBIAkEA\
 1cCoq2Wa3Ixs47uI7FPVwHVIPDx5yso105Y6zpozam135a\
 8R0CpoRvkkigIyXfcCjiVi5oWk+6FfPaD03uPFoQIDAQAB\
 FgVoZWxsbzANBgkqhkiG9w0BAQQFAANBAFpQtY/FojdwkJ\
 h1bEIYuc2EeM2KHTWPEepWYeawvHD0gQ3DngSC75YCWnnD\
 dq+NQ3F+X4deMx9AaEglZtULwV4=

=head1 NOTES

A created SPKAC with suitable DN components appended can be fed to
L<openssl-ca(1)>.

SPKACs are typically generated by Netscape when a form is submitted
containing the B<KEYGEN> tag as part of the certificate enrollment
process.

The challenge string permits a primitive form of proof of possession
of private key. By checking the SPKAC signature and a random challenge
string some guarantee is given that the user knows the private key
corresponding to the public key being certified. This is important in
some applications. Without this it is possible for a previous SPKAC
to be used in a "replay attack".

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-ca(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

The B<-digest> option was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
