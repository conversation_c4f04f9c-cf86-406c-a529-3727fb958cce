<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_string_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_string_f Struct Reference</h1><!-- doxytag: class="cc_string_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_string_t. For more information see <a class="el" href="group__cc__string__reference.html">cc_string_t Overview</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__string__f.html#ba3623018f7ad67de1f29f4cf1a9c66f">release</a> )(<a class="el" href="structcc__string__d.html">cc_string_t</a> io_string)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#ge9bebfed2d574e69f29dd341bc8a63d9">cc_string_release()</a></b>: Release memory associated with a cc_string_t object.  <a href="#ba3623018f7ad67de1f29f4cf1a9c66f"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="ba3623018f7ad67de1f29f4cf1a9c66f"></a><!-- doxytag: member="cc_string_f::release" ref="ba3623018f7ad67de1f29f4cf1a9c66f" args=")(cc_string_t io_string)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__string__f.html#ba3623018f7ad67de1f29f4cf1a9c66f">release</a>)(<a class="el" href="structcc__string__d.html">cc_string_t</a> io_string)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#ge9bebfed2d574e69f29dd341bc8a63d9">cc_string_release()</a></b>: Release memory associated with a cc_string_t object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_string</em>&nbsp;</td><td>the string object to release. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
