/* -*- mode: c; c-file-style: "bsd"; indent-tabs-mode: t -*- */
/*
 * Copyright 2004 Sun Microsystems, Inc.  All rights reserved.
 * Use is subject to license terms.
 */

/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */

#include <memory.h> /* for memset */
#include "iprop.h"

/* Default timeout can be changed using clnt_control() */
static struct timeval TIMEOUT = { 25, 0 };

void *
iprop_null_1(void *argp, CLIENT *clnt)
{
	static char clnt_res;

	memset(&clnt_res, 0, sizeof(clnt_res));
	if (clnt_call (clnt, IPROP_NULL,
		(xdrproc_t) xdr_void, (caddr_t) argp,
		(xdrproc_t) xdr_void, (caddr_t) &clnt_res,
		TIMEOUT) != RPC_SUCCESS) {
		return (NULL);
	}
	return ((void *)&clnt_res);
}

kdb_incr_result_t *
iprop_get_updates_1(kdb_last_t *argp, CLIENT *clnt)
{
	static kdb_incr_result_t clnt_res;

	memset(&clnt_res, 0, sizeof(clnt_res));
	if (clnt_call (clnt, IPROP_GET_UPDATES,
		(xdrproc_t) xdr_kdb_last_t, (caddr_t) argp,
		(xdrproc_t) xdr_kdb_incr_result_t, (caddr_t) &clnt_res,
		TIMEOUT) != RPC_SUCCESS) {
		return (NULL);
	}
	return (&clnt_res);
}

kdb_fullresync_result_t *
iprop_full_resync_1(void *argp, CLIENT *clnt)
{
	static kdb_fullresync_result_t clnt_res;

	memset(&clnt_res, 0, sizeof(clnt_res));
	if (clnt_call (clnt, IPROP_FULL_RESYNC,
		(xdrproc_t) xdr_void, (caddr_t) argp,
		(xdrproc_t) xdr_kdb_fullresync_result_t, (caddr_t) &clnt_res,
		TIMEOUT) != RPC_SUCCESS) {
		return (NULL);
	}
	return (&clnt_res);
}
