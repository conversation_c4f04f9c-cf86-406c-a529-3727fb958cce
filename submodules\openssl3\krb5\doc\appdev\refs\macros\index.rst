krb5 simple macros
=========================

Public
-------

.. toctree::
   :maxdepth: 1

   ADDRTYPE_ADDRPORT.rst
   ADDRTYPE_CHAOS.rst
   ADDRTYPE_DDP.rst
   ADDRTYPE_INET.rst
   ADDRTYPE_INET6.rst
   ADDRTYPE_IPPORT.rst
   ADDRTYPE_ISO.rst
   ADDRTYPE_IS_LOCAL.rst
   ADDRTYPE_NETBIOS.rst
   ADDRTYPE_XNS.rst
   AD_TYPE_EXTERNAL.rst
   AD_TYPE_FIELD_TYPE_MASK.rst
   AD_TYPE_REGISTERED.rst
   AD_TYPE_RESERVED.rst
   AP_OPTS_ETYPE_NEGOTIATION.rst
   AP_OPTS_MUTUAL_REQUIRED.rst
   AP_OPTS_RESERVED.rst
   AP_OPTS_USE_SESSION_KEY.rst
   AP_OPTS_USE_SUBKEY.rst
   AP_OPTS_WIRE_MASK.rst
   CKSUMTYPE_CMAC_CAMELLIA128.rst
   CKSUMTYPE_CMAC_CAMELLIA256.rst
   CKSUMTYPE_CRC32.rst
   CKSUMTYPE_DESCBC.rst
   CKSUMTYPE_HMAC_MD5_ARCFOUR.rst
   CKSUMTYPE_HMAC_SHA1_96_AES128.rst
   CKSUMTYPE_HMAC_SHA1_96_AES256.rst
   CKSUMTYPE_HMAC_SHA256_128_AES128.rst
   CKSUMTYPE_HMAC_SHA384_192_AES256.rst
   CKSUMTYPE_HMAC_SHA1_DES3.rst
   CKSUMTYPE_MD5_HMAC_ARCFOUR.rst
   CKSUMTYPE_NIST_SHA.rst
   CKSUMTYPE_RSA_MD4.rst
   CKSUMTYPE_RSA_MD4_DES.rst
   CKSUMTYPE_RSA_MD5.rst
   CKSUMTYPE_RSA_MD5_DES.rst
   ENCTYPE_AES128_CTS_HMAC_SHA1_96.rst
   ENCTYPE_AES128_CTS_HMAC_SHA256_128.rst
   ENCTYPE_AES256_CTS_HMAC_SHA1_96.rst
   ENCTYPE_AES256_CTS_HMAC_SHA384_192.rst
   ENCTYPE_ARCFOUR_HMAC.rst
   ENCTYPE_ARCFOUR_HMAC_EXP.rst
   ENCTYPE_CAMELLIA128_CTS_CMAC.rst
   ENCTYPE_CAMELLIA256_CTS_CMAC.rst
   ENCTYPE_DES3_CBC_ENV.rst
   ENCTYPE_DES3_CBC_RAW.rst
   ENCTYPE_DES3_CBC_SHA.rst
   ENCTYPE_DES3_CBC_SHA1.rst
   ENCTYPE_DES_CBC_CRC.rst
   ENCTYPE_DES_CBC_MD4.rst
   ENCTYPE_DES_CBC_MD5.rst
   ENCTYPE_DES_CBC_RAW.rst
   ENCTYPE_DES_HMAC_SHA1.rst
   ENCTYPE_DSA_SHA1_CMS.rst
   ENCTYPE_MD5_RSA_CMS.rst
   ENCTYPE_NULL.rst
   ENCTYPE_RC2_CBC_ENV.rst
   ENCTYPE_RSA_ENV.rst
   ENCTYPE_RSA_ES_OAEP_ENV.rst
   ENCTYPE_SHA1_RSA_CMS.rst
   ENCTYPE_UNKNOWN.rst
   KDC_OPT_ALLOW_POSTDATE.rst
   KDC_OPT_CANONICALIZE.rst
   KDC_OPT_CNAME_IN_ADDL_TKT.rst
   KDC_OPT_DISABLE_TRANSITED_CHECK.rst
   KDC_OPT_ENC_TKT_IN_SKEY.rst
   KDC_OPT_FORWARDABLE.rst
   KDC_OPT_FORWARDED.rst
   KDC_OPT_POSTDATED.rst
   KDC_OPT_PROXIABLE.rst
   KDC_OPT_PROXY.rst
   KDC_OPT_RENEW.rst
   KDC_OPT_RENEWABLE.rst
   KDC_OPT_RENEWABLE_OK.rst
   KDC_OPT_REQUEST_ANONYMOUS.rst
   KDC_OPT_VALIDATE.rst
   KDC_TKT_COMMON_MASK.rst
   KRB5_ALTAUTH_ATT_CHALLENGE_RESPONSE.rst
   KRB5_ANONYMOUS_PRINCSTR.rst
   KRB5_ANONYMOUS_REALMSTR.rst
   KRB5_AP_REP.rst
   KRB5_AP_REQ.rst
   KRB5_AS_REP.rst
   KRB5_AS_REQ.rst
   KRB5_AUTHDATA_AND_OR.rst
   KRB5_AUTHDATA_AP_OPTIONS.rst
   KRB5_AUTHDATA_AUTH_INDICATOR.rst
   KRB5_AUTHDATA_CAMMAC.rst
   KRB5_AUTHDATA_ETYPE_NEGOTIATION.rst
   KRB5_AUTHDATA_FX_ARMOR.rst
   KRB5_AUTHDATA_IF_RELEVANT.rst
   KRB5_AUTHDATA_INITIAL_VERIFIED_CAS.rst
   KRB5_AUTHDATA_KDC_ISSUED.rst
   KRB5_AUTHDATA_MANDATORY_FOR_KDC.rst
   KRB5_AUTHDATA_OSF_DCE.rst
   KRB5_AUTHDATA_SESAME.rst
   KRB5_AUTHDATA_SIGNTICKET.rst
   KRB5_AUTHDATA_WIN2K_PAC.rst
   KRB5_AUTH_CONTEXT_DO_SEQUENCE.rst
   KRB5_AUTH_CONTEXT_DO_TIME.rst
   KRB5_AUTH_CONTEXT_GENERATE_LOCAL_ADDR.rst
   KRB5_AUTH_CONTEXT_GENERATE_LOCAL_FULL_ADDR.rst
   KRB5_AUTH_CONTEXT_GENERATE_REMOTE_ADDR.rst
   KRB5_AUTH_CONTEXT_GENERATE_REMOTE_FULL_ADDR.rst
   KRB5_AUTH_CONTEXT_PERMIT_ALL.rst
   KRB5_AUTH_CONTEXT_RET_SEQUENCE.rst
   KRB5_AUTH_CONTEXT_RET_TIME.rst
   KRB5_AUTH_CONTEXT_USE_SUBKEY.rst
   KRB5_CRED.rst
   KRB5_CRYPTO_TYPE_CHECKSUM.rst
   KRB5_CRYPTO_TYPE_DATA.rst
   KRB5_CRYPTO_TYPE_EMPTY.rst
   KRB5_CRYPTO_TYPE_HEADER.rst
   KRB5_CRYPTO_TYPE_PADDING.rst
   KRB5_CRYPTO_TYPE_SIGN_ONLY.rst
   KRB5_CRYPTO_TYPE_STREAM.rst
   KRB5_CRYPTO_TYPE_TRAILER.rst
   KRB5_CYBERSAFE_SECUREID.rst
   KRB5_DOMAIN_X500_COMPRESS.rst
   KRB5_ENCPADATA_REQ_ENC_PA_REP.rst
   KRB5_ERROR.rst
   KRB5_FAST_REQUIRED.rst
   KRB5_GC_CACHED.rst
   KRB5_GC_CANONICALIZE.rst
   KRB5_GC_CONSTRAINED_DELEGATION.rst
   KRB5_GC_FORWARDABLE.rst
   KRB5_GC_NO_STORE.rst
   KRB5_GC_NO_TRANSIT_CHECK.rst
   KRB5_GC_USER_USER.rst
   KRB5_GET_INIT_CREDS_OPT_ADDRESS_LIST.rst
   KRB5_GET_INIT_CREDS_OPT_ANONYMOUS.rst
   KRB5_GET_INIT_CREDS_OPT_CANONICALIZE.rst
   KRB5_GET_INIT_CREDS_OPT_CHG_PWD_PRMPT.rst
   KRB5_GET_INIT_CREDS_OPT_ETYPE_LIST.rst
   KRB5_GET_INIT_CREDS_OPT_FORWARDABLE.rst
   KRB5_GET_INIT_CREDS_OPT_PREAUTH_LIST.rst
   KRB5_GET_INIT_CREDS_OPT_PROXIABLE.rst
   KRB5_GET_INIT_CREDS_OPT_RENEW_LIFE.rst
   KRB5_GET_INIT_CREDS_OPT_SALT.rst
   KRB5_GET_INIT_CREDS_OPT_TKT_LIFE.rst
   KRB5_INIT_CONTEXT_SECURE.rst
   KRB5_INIT_CONTEXT_KDC.rst
   KRB5_INIT_CREDS_STEP_FLAG_CONTINUE.rst
   KRB5_INT16_MAX.rst
   KRB5_INT16_MIN.rst
   KRB5_INT32_MAX.rst
   KRB5_INT32_MIN.rst
   KRB5_KEYUSAGE_AD_ITE.rst
   KRB5_KEYUSAGE_AD_KDCISSUED_CKSUM.rst
   KRB5_KEYUSAGE_AD_MTE.rst
   KRB5_KEYUSAGE_AD_SIGNEDPATH.rst
   KRB5_KEYUSAGE_APP_DATA_CKSUM.rst
   KRB5_KEYUSAGE_APP_DATA_ENCRYPT.rst
   KRB5_KEYUSAGE_AP_REP_ENCPART.rst
   KRB5_KEYUSAGE_AP_REQ_AUTH.rst
   KRB5_KEYUSAGE_AP_REQ_AUTH_CKSUM.rst
   KRB5_KEYUSAGE_AS_REP_ENCPART.rst
   KRB5_KEYUSAGE_AS_REQ.rst
   KRB5_KEYUSAGE_AS_REQ_PA_ENC_TS.rst
   KRB5_KEYUSAGE_CAMMAC.rst
   KRB5_KEYUSAGE_ENC_CHALLENGE_CLIENT.rst
   KRB5_KEYUSAGE_ENC_CHALLENGE_KDC.rst
   KRB5_KEYUSAGE_FAST_ENC.rst
   KRB5_KEYUSAGE_FAST_FINISHED.rst
   KRB5_KEYUSAGE_FAST_REP.rst
   KRB5_KEYUSAGE_FAST_REQ_CHKSUM.rst
   KRB5_KEYUSAGE_GSS_TOK_MIC.rst
   KRB5_KEYUSAGE_GSS_TOK_WRAP_INTEG.rst
   KRB5_KEYUSAGE_GSS_TOK_WRAP_PRIV.rst
   KRB5_KEYUSAGE_IAKERB_FINISHED.rst
   KRB5_KEYUSAGE_KDC_REP_TICKET.rst
   KRB5_KEYUSAGE_KRB_CRED_ENCPART.rst
   KRB5_KEYUSAGE_KRB_ERROR_CKSUM.rst
   KRB5_KEYUSAGE_KRB_PRIV_ENCPART.rst
   KRB5_KEYUSAGE_KRB_SAFE_CKSUM.rst
   KRB5_KEYUSAGE_PA_AS_FRESHNESS.rst
   KRB5_KEYUSAGE_PA_FX_COOKIE.rst
   KRB5_KEYUSAGE_PA_OTP_REQUEST.rst
   KRB5_KEYUSAGE_PA_PKINIT_KX.rst
   KRB5_KEYUSAGE_PA_S4U_X509_USER_REPLY.rst
   KRB5_KEYUSAGE_PA_S4U_X509_USER_REQUEST.rst
   KRB5_KEYUSAGE_PA_SAM_CHALLENGE_CKSUM.rst
   KRB5_KEYUSAGE_PA_SAM_CHALLENGE_TRACKID.rst
   KRB5_KEYUSAGE_PA_SAM_RESPONSE.rst
   KRB5_KEYUSAGE_SPAKE.rst
   KRB5_KEYUSAGE_TGS_REP_ENCPART_SESSKEY.rst
   KRB5_KEYUSAGE_TGS_REP_ENCPART_SUBKEY.rst
   KRB5_KEYUSAGE_TGS_REQ_AD_SESSKEY.rst
   KRB5_KEYUSAGE_TGS_REQ_AD_SUBKEY.rst
   KRB5_KEYUSAGE_TGS_REQ_AUTH.rst
   KRB5_KEYUSAGE_TGS_REQ_AUTH_CKSUM.rst
   KRB5_KPASSWD_ACCESSDENIED.rst
   KRB5_KPASSWD_AUTHERROR.rst
   KRB5_KPASSWD_BAD_VERSION.rst
   KRB5_KPASSWD_HARDERROR.rst
   KRB5_KPASSWD_INITIAL_FLAG_NEEDED.rst
   KRB5_KPASSWD_MALFORMED.rst
   KRB5_KPASSWD_SOFTERROR.rst
   KRB5_KPASSWD_SUCCESS.rst
   KRB5_LRQ_ALL_ACCT_EXPTIME.rst
   KRB5_LRQ_ALL_LAST_INITIAL.rst
   KRB5_LRQ_ALL_LAST_RENEWAL.rst
   KRB5_LRQ_ALL_LAST_REQ.rst
   KRB5_LRQ_ALL_LAST_TGT.rst
   KRB5_LRQ_ALL_LAST_TGT_ISSUED.rst
   KRB5_LRQ_ALL_PW_EXPTIME.rst
   KRB5_LRQ_NONE.rst
   KRB5_LRQ_ONE_ACCT_EXPTIME.rst
   KRB5_LRQ_ONE_LAST_INITIAL.rst
   KRB5_LRQ_ONE_LAST_RENEWAL.rst
   KRB5_LRQ_ONE_LAST_REQ.rst
   KRB5_LRQ_ONE_LAST_TGT.rst
   KRB5_LRQ_ONE_LAST_TGT_ISSUED.rst
   KRB5_LRQ_ONE_PW_EXPTIME.rst
   KRB5_NT_ENTERPRISE_PRINCIPAL.rst
   KRB5_NT_ENT_PRINCIPAL_AND_ID.rst
   KRB5_NT_MS_PRINCIPAL.rst
   KRB5_NT_MS_PRINCIPAL_AND_ID.rst
   KRB5_NT_PRINCIPAL.rst
   KRB5_NT_SMTP_NAME.rst
   KRB5_NT_SRV_HST.rst
   KRB5_NT_SRV_INST.rst
   KRB5_NT_SRV_XHST.rst
   KRB5_NT_UID.rst
   KRB5_NT_UNKNOWN.rst
   KRB5_NT_WELLKNOWN.rst
   KRB5_NT_X500_PRINCIPAL.rst
   KRB5_PAC_CLIENT_INFO.rst
   KRB5_PAC_CREDENTIALS_INFO.rst
   KRB5_PAC_DELEGATION_INFO.rst
   KRB5_PAC_LOGON_INFO.rst
   KRB5_PAC_PRIVSVR_CHECKSUM.rst
   KRB5_PAC_SERVER_CHECKSUM.rst
   KRB5_PAC_UPN_DNS_INFO.rst
   KRB5_PADATA_AFS3_SALT.rst
   KRB5_PADATA_AP_REQ.rst
   KRB5_PADATA_AS_CHECKSUM.rst
   KRB5_PADATA_AS_FRESHNESS.rst
   KRB5_PADATA_ENCRYPTED_CHALLENGE.rst
   KRB5_PADATA_ENC_SANDIA_SECURID.rst
   KRB5_PADATA_ENC_TIMESTAMP.rst
   KRB5_PADATA_ENC_UNIX_TIME.rst
   KRB5_PADATA_ETYPE_INFO.rst
   KRB5_PADATA_ETYPE_INFO2.rst
   KRB5_PADATA_FOR_USER.rst
   KRB5_PADATA_FX_COOKIE.rst
   KRB5_PADATA_FX_ERROR.rst
   KRB5_PADATA_FX_FAST.rst
   KRB5_PADATA_GET_FROM_TYPED_DATA.rst
   KRB5_PADATA_NONE.rst
   KRB5_PADATA_OSF_DCE.rst
   KRB5_PADATA_OTP_CHALLENGE.rst
   KRB5_PADATA_OTP_PIN_CHANGE.rst
   KRB5_PADATA_OTP_REQUEST.rst
   KRB5_PADATA_PAC_OPTIONS.rst
   KRB5_PADATA_PAC_REQUEST.rst
   KRB5_PADATA_PKINIT_KX.rst
   KRB5_PADATA_PK_AS_REP.rst
   KRB5_PADATA_PK_AS_REP_OLD.rst
   KRB5_PADATA_PK_AS_REQ.rst
   KRB5_PADATA_PK_AS_REQ_OLD.rst
   KRB5_PADATA_PW_SALT.rst
   KRB5_PADATA_REFERRAL.rst
   KRB5_PADATA_S4U_X509_USER.rst
   KRB5_PADATA_SAM_CHALLENGE.rst
   KRB5_PADATA_SAM_CHALLENGE_2.rst
   KRB5_PADATA_SAM_REDIRECT.rst
   KRB5_PADATA_SAM_RESPONSE.rst
   KRB5_PADATA_SAM_RESPONSE_2.rst
   KRB5_PADATA_SESAME.rst
   KRB5_PADATA_SPAKE.rst
   KRB5_PADATA_SVR_REFERRAL_INFO.rst
   KRB5_PADATA_TGS_REQ.rst
   KRB5_PADATA_USE_SPECIFIED_KVNO.rst
   KRB5_PRINCIPAL_COMPARE_CASEFOLD.rst
   KRB5_PRINCIPAL_COMPARE_ENTERPRISE.rst
   KRB5_PRINCIPAL_COMPARE_IGNORE_REALM.rst
   KRB5_PRINCIPAL_COMPARE_UTF8.rst
   KRB5_PRINCIPAL_PARSE_ENTERPRISE.rst
   KRB5_PRINCIPAL_PARSE_IGNORE_REALM.rst
   KRB5_PRINCIPAL_PARSE_NO_DEF_REALM.rst
   KRB5_PRINCIPAL_PARSE_NO_REALM.rst
   KRB5_PRINCIPAL_PARSE_REQUIRE_REALM.rst
   KRB5_PRINCIPAL_UNPARSE_DISPLAY.rst
   KRB5_PRINCIPAL_UNPARSE_NO_REALM.rst
   KRB5_PRINCIPAL_UNPARSE_SHORT.rst
   KRB5_PRIV.rst
   KRB5_PROMPT_TYPE_NEW_PASSWORD.rst
   KRB5_PROMPT_TYPE_NEW_PASSWORD_AGAIN.rst
   KRB5_PROMPT_TYPE_PASSWORD.rst
   KRB5_PROMPT_TYPE_PREAUTH.rst
   KRB5_PVNO.rst
   KRB5_REALM_BRANCH_CHAR.rst
   KRB5_RECVAUTH_BADAUTHVERS.rst
   KRB5_RECVAUTH_SKIP_VERSION.rst
   KRB5_REFERRAL_REALM.rst
   KRB5_RESPONDER_PKINIT_FLAGS_TOKEN_USER_PIN_COUNT_LOW.rst
   KRB5_RESPONDER_PKINIT_FLAGS_TOKEN_USER_PIN_FINAL_TRY.rst
   KRB5_RESPONDER_PKINIT_FLAGS_TOKEN_USER_PIN_LOCKED.rst
   KRB5_RESPONDER_QUESTION_PKINIT.rst
   KRB5_RESPONDER_OTP_FLAGS_COLLECT_PIN.rst
   KRB5_RESPONDER_OTP_FLAGS_COLLECT_TOKEN.rst
   KRB5_RESPONDER_OTP_FLAGS_NEXTOTP.rst
   KRB5_RESPONDER_OTP_FLAGS_SEPARATE_PIN.rst
   KRB5_RESPONDER_OTP_FORMAT_ALPHANUMERIC.rst
   KRB5_RESPONDER_OTP_FORMAT_DECIMAL.rst
   KRB5_RESPONDER_OTP_FORMAT_HEXADECIMAL.rst
   KRB5_RESPONDER_QUESTION_OTP.rst
   KRB5_RESPONDER_QUESTION_PASSWORD.rst
   KRB5_SAFE.rst
   KRB5_SAM_MUST_PK_ENCRYPT_SAD.rst
   KRB5_SAM_SEND_ENCRYPTED_SAD.rst
   KRB5_SAM_USE_SAD_AS_KEY.rst
   KRB5_TC_MATCH_2ND_TKT.rst
   KRB5_TC_MATCH_AUTHDATA.rst
   KRB5_TC_MATCH_FLAGS.rst
   KRB5_TC_MATCH_FLAGS_EXACT.rst
   KRB5_TC_MATCH_IS_SKEY.rst
   KRB5_TC_MATCH_KTYPE.rst
   KRB5_TC_MATCH_SRV_NAMEONLY.rst
   KRB5_TC_MATCH_TIMES.rst
   KRB5_TC_MATCH_TIMES_EXACT.rst
   KRB5_TC_NOTICKET.rst
   KRB5_TC_OPENCLOSE.rst
   KRB5_TC_SUPPORTED_KTYPES.rst
   KRB5_TGS_NAME.rst
   KRB5_TGS_NAME_SIZE.rst
   KRB5_TGS_REP.rst
   KRB5_TGS_REQ.rst
   KRB5_TKT_CREDS_STEP_FLAG_CONTINUE.rst
   KRB5_VERIFY_INIT_CREDS_OPT_AP_REQ_NOFAIL.rst
   KRB5_WELLKNOWN_NAMESTR.rst
   LR_TYPE_INTERPRETATION_MASK.rst
   LR_TYPE_THIS_SERVER_ONLY.rst
   MAX_KEYTAB_NAME_LEN.rst
   MSEC_DIRBIT.rst
   MSEC_VAL_MASK.rst
   SALT_TYPE_AFS_LENGTH.rst
   SALT_TYPE_NO_LENGTH.rst
   THREEPARAMOPEN.rst
   TKT_FLG_ANONYMOUS.rst
   TKT_FLG_ENC_PA_REP.rst
   TKT_FLG_FORWARDABLE.rst
   TKT_FLG_FORWARDED.rst
   TKT_FLG_HW_AUTH.rst
   TKT_FLG_INITIAL.rst
   TKT_FLG_INVALID.rst
   TKT_FLG_MAY_POSTDATE.rst
   TKT_FLG_OK_AS_DELEGATE.rst
   TKT_FLG_POSTDATED.rst
   TKT_FLG_PRE_AUTH.rst
   TKT_FLG_PROXIABLE.rst
   TKT_FLG_PROXY.rst
   TKT_FLG_RENEWABLE.rst
   TKT_FLG_TRANSIT_POLICY_CHECKED.rst
   VALID_INT_BITS.rst
   VALID_UINT_BITS.rst
   krb5_const.rst
   krb5_princ_component.rst
   krb5_princ_name.rst
   krb5_princ_realm.rst
   krb5_princ_set_realm.rst
   krb5_princ_set_realm_data.rst
   krb5_princ_set_realm_length.rst
   krb5_princ_size.rst
   krb5_princ_type.rst
   krb5_roundup.rst
   krb5_x.rst
   krb5_xc.rst

Deprecated macros
------------------------------

.. toctree::
   :maxdepth: 1

   krb524_convert_creds_kdc.rst
   krb524_init_ets.rst
