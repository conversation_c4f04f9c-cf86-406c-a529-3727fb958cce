=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-pkey - public or private key processing command

=head1 SYNOPSIS

B<openssl> B<pkey>
[B<-help>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}
[B<-check>]
[B<-pubcheck>]
[B<-in> I<filename>|I<uri>]
[B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-passin> I<arg>]
[B<-pubin>]
[B<-out> I<filename>]
[B<-outform> B<DER>|B<PEM>]
[B<-I<cipher>>]
[B<-passout> I<arg>]
[B<-traditional>]
[B<-pubout>]
[B<-noout>]
[B<-text>]
[B<-text_pub>]
[B<-ec_conv_form> I<arg>]
[B<-ec_param_enc> I<arg>]

=head1 DESCRIPTION

This command processes public or private keys. They can be
converted between various forms and their components printed.

=head1 OPTIONS

=head2 General options

=over 4

=item B<-help>

Print out a usage message.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=item B<-check>

This option checks the consistency of a key pair for both public and private
components.

=item B<-pubcheck>

This option checks the correctness of either a public key
or the public component of a key pair.

=back

=head2 Input options

=over 4

=item B<-in> I<filename>|I<uri>

This specifies the input to read a key from
or standard input if this option is not specified.
If the key input is encrypted and B<-passin> is not given
a pass phrase will be prompted for.

=item B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-passin> I<arg>

The password source for the key input.

For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-pubin>

By default a private key is read from the input.
With this option only the public components are read.

=back

=head2 Output options

=over 4

=item B<-out> I<filename>

This specifies the output filename to save the encoded and/or text output of key
or standard output if this option is not specified.
If any cipher option is set but no B<-passout> is given
then a pass phrase will be prompted for.
The output filename should B<not> be the same as the input filename.

=item B<-outform> B<DER>|B<PEM>

The key output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-I<cipher>>

Encrypt the PEM encoded private key with the supplied cipher. Any algorithm
name accepted by EVP_get_cipherbyname() is acceptable such as B<aes128>.
Encryption is not supported for DER output.

=item B<-passout> I<arg>

The password source for the output file.

For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-traditional>

Normally a private key is written using standard format: this is PKCS#8 form
with the appropriate encryption algorithm (if any). If the B<-traditional>
option is specified then the older "traditional" format is used instead.

=item B<-pubout>

By default the private and public key is output;
this option restricts the output to the public components.
This option is automatically set if the input is a public key.

When combined with B<-text>, this is equivalent to B<-text_pub>.

=item B<-noout>

Do not output the key in encoded form.

=item B<-text>

Output the various key components in plain text
(possibly in addition to the PEM encoded form).
This cannot be combined with encoded output in DER format.

=item B<-text_pub>

Output in text form only the public key components (also for private keys).
This cannot be combined with encoded output in DER format.

=item B<-ec_conv_form> I<arg>

This option only applies to elliptic-curve based keys.

This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: B<compressed> (the default
value), B<uncompressed> and B<hybrid>. For more information regarding
the point conversion forms please read the X9.62 standard.
B<Note> Due to patent issues the B<compressed> option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro B<OPENSSL_EC_BIN_PT_COMP> at compile time.

=item B<-ec_param_enc> I<arg>

This option only applies to elliptic curve based public and private keys.

This specifies how the elliptic curve parameters are encoded.
Possible value are: B<named_curve>, i.e. the ec parameters are
specified by an OID, or B<explicit> where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is B<named_curve>.
B<Note> the B<implicitlyCA> alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.

=back

=head1 EXAMPLES

To remove the pass phrase on a private key:

 openssl pkey -in key.pem -out keyout.pem

To encrypt a private key using triple DES:

 openssl pkey -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl pkey -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl pkey -in key.pem -text -noout

To print out the public components of a private key to standard output:

 openssl pkey -in key.pem -text_pub -noout

To just output the public part of a private key:

 openssl pkey -in key.pem -pubout -out pubkey.pem

To change the EC parameters encoding to B<explicit>:

 openssl pkey -in key.pem -ec_param_enc explicit -out keyout.pem

To change the EC point conversion form to B<compressed>:

 openssl pkey -in key.pem -ec_conv_form compressed -out keyout.pem

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-genpkey(1)>,
L<openssl-rsa(1)>,
L<openssl-pkcs8(1)>,
L<openssl-dsa(1)>,
L<openssl-genrsa(1)>,
L<openssl-gendsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
