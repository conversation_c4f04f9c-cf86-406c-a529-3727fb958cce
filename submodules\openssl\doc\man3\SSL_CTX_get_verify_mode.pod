=pod

=head1 NAME

SSL_CTX_get_verify_mode, SSL_get_verify_mode, SSL_CTX_get_verify_depth, SSL_get_verify_depth, SSL_get_verify_callback, SSL_CTX_get_verify_callback - get currently set verification parameters

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_get_verify_mode(const SSL_CTX *ctx);
 int SSL_get_verify_mode(const SSL *ssl);
 int SSL_CTX_get_verify_depth(const SSL_CTX *ctx);
 int SSL_get_verify_depth(const SSL *ssl);
 int (*SSL_CTX_get_verify_callback(const SSL_CTX *ctx))(int, X509_STORE_CTX *);
 int (*SSL_get_verify_callback(const SSL *ssl))(int, X509_STORE_CTX *);

=head1 DESCRIPTION

SSL_CTX_get_verify_mode() returns the verification mode currently set in
B<ctx>.

SSL_get_verify_mode() returns the verification mode currently set in
B<ssl>.

SSL_CTX_get_verify_depth() returns the verification depth limit currently set
in B<ctx>. If no limit has been explicitly set, -1 is returned and the
default value will be used.

SSL_get_verify_depth() returns the verification depth limit currently set
in B<ssl>. If no limit has been explicitly set, -1 is returned and the
default value will be used.

SSL_CTX_get_verify_callback() returns a function pointer to the verification
callback currently set in B<ctx>. If no callback was explicitly set, the
NULL pointer is returned and the default callback will be used.

SSL_get_verify_callback() returns a function pointer to the verification
callback currently set in B<ssl>. If no callback was explicitly set, the
NULL pointer is returned and the default callback will be used.

=head1 RETURN VALUES

See DESCRIPTION

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set_verify(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
