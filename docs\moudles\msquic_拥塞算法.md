


          
# MsQuic 拥塞控制算法分析

## 1. 拥塞控制概述

拥塞控制是网络传输协议中的关键组件，用于避免网络拥塞并确保网络资源的高效利用。在 MsQuic 中，拥塞控制算法负责调整发送速率，以适应网络条件的变化，防止网络拥塞，并确保数据的可靠传输。

MsQuic 实现了两种主要的拥塞控制算法：
- **CUBIC** - 基于 RFC8312bis 的拥塞控制算法
- **BBR** (Bottleneck Bandwidth and RTT) - 谷歌开发的拥塞控制算法

## 2. 拥塞控制数据结构

### 2.1 主要结构体

```c
typedef struct QUIC_CONGESTION_CONTROL {
    union {
        QUIC_CONGESTION_CONTROL_CUBIC Cubic;
        QUIC_CONGESTION_CONTROL_BBR Bbr;
    };
} QUIC_CONGESTION_CONTROL;
```

MsQuic 使用统一的 `QUIC_CONGESTION_CONTROL` 结构体来管理不同的拥塞控制算法，通过联合体方式支持 CUBIC 和 BBR 算法。

### 2.2 算法选择枚举

```c
typedef enum QUIC_CONGESTION_CONTROL_ALGORITHM {
    QUIC_CONGESTION_CONTROL_ALGORITHM_CUBIC,
    QUIC_CONGESTION_CONTROL_ALGORITHM_BBR,
    QUIC_CONGESTION_CONTROL_ALGORITHM_MAX
} QUIC_CONGESTION_CONTROL_ALGORITHM;
```

## 3. CUBIC 拥塞控制算法

### 3.1 CUBIC 状态定义

CUBIC 算法主要包含以下状态：
- 慢启动 (Slow Start)
- 拥塞避免 (Congestion Avoidance)
- 恢复 (Recovery)
- 持续拥塞 (Persistent Congestion)

### 3.2 CUBIC 关键参数

```c
// BETA 和 C 来自 RFC8312，使用 10 倍整数算术
#define TEN_TIMES_BETA_CUBIC 7  // 0.7
#define TEN_TIMES_C_CUBIC 4     // 0.4
```

### 3.3 CUBIC 主要功能

1. **慢启动阶段**：
   - 指数增长拥塞窗口，直到达到慢启动阈值
   - 支持 HyStart++ 算法提前检测拥塞

2. **拥塞避免阶段**：
   - 使用三次方根函数调整拥塞窗口
   - 实现了快速收敛机制

3. **恢复机制**：
   - 检测丢包事件并相应减小拥塞窗口
   - 支持 ECN (显式拥塞通知)

4. **持续拥塞处理**：
   - 当检测到持续拥塞时，大幅减小拥塞窗口

### 3.4 CUBIC 核心函数

```c
// 初始化 CUBIC 拥塞控制
CubicCongestionControlInitialize(QUIC_CONGESTION_CONTROL* Cc, const QUIC_SETTINGS_INTERNAL* Settings)

// 处理拥塞事件
CubicCongestionControlOnCongestionEvent(QUIC_CONGESTION_CONTROL* Cc, BOOLEAN IsPersistentCongestion, BOOLEAN Ecn)

// 处理显示拥塞事件
CubicCongestionControlOnEcn(
    _In_ QUIC_CONGESTION_CONTROL* Cc,
    _In_ const QUIC_ECN_EVENT* EcnEvent
    )

// 处理数据确认
CubicCongestionControlOnDataAcknowledged(QUIC_CONGESTION_CONTROL* Cc, const QUIC_ACK_EVENT* AckEvent)

// 处理丢包
void
CubicCongestionControlOnDataLost(
    _In_ QUIC_CONGESTION_CONTROL* Cc,
    _In_ const QUIC_LOSS_EVENT* LossEvent
    )

// 获取发送限额
CubicCongestionControlGetSendAllowance(QUIC_CONGESTION_CONTROL* Cc, uint64_t TimeSinceLastSend, BOOLEAN TimeSinceLastSendValid)

// 可以发送数据
BOOLEAN
CubicCongestionControlCanSend(
    _In_ QUIC_CONGESTION_CONTROL* Cc
    )

```

## 4. BBR 拥塞控制算法

### 4.1 BBR 状态定义

```c
typedef enum BBR_STATE {
    BBR_STATE_STARTUP,    // 启动阶段
    BBR_STATE_DRAIN,      // 排空阶段
    BBR_STATE_PROBE_BW,   // 探测带宽阶段
    BBR_STATE_PROBE_RTT   // 探测RTT阶段
} BBR_STATE;

typedef enum RECOVERY_STATE {
    RECOVERY_STATE_NOT_RECOVERY = 0,  // 非恢复状态
    RECOVERY_STATE_CONSERVATIVE = 1,  // 保守恢复状态
    RECOVERY_STATE_GROWTH = 2,        // 增长恢复状态
} RECOVERY_STATE;
```

### 4.2 BBR 关键参数

```c
// 带宽单位
#define BW_UNIT 8 // 1 << 3

// 增益单位
#define GAIN_UNIT 256 // 1 << 8

// 增益周期长度
#define GAIN_CYCLE_LENGTH 8

// 启动阶段增益
const uint32_t kHighGain = GAIN_UNIT * 2885 / 1000 + 1; // 2/ln(2)

// 排空阶段增益
const uint32_t kDrainGain = GAIN_UNIT * 1000 / 2885; // 1/kHighGain
```

### 4.3 BBR 主要功能

1. **启动阶段 (STARTUP)**：
   - 快速增加发送速率以探测可用带宽
   - 使用高增益因子 (kHighGain)

2. **排空阶段 (DRAIN)**：
   - 排空启动阶段积累的队列
   - 使用低增益因子 (kDrainGain)

3. **探测带宽阶段 (PROBE_BW)**：
   - 周期性地探测更高的带宽
   - 使用增益周期 (kPacingGain)

4. **探测RTT阶段 (PROBE_RTT)**：
   - 周期性地降低发送速率以测量最小RTT
   - 保持低队列状态至少 kProbeRttTimeInUs 时间

### 4.4 BBR 核心函数

```c
// 初始化 BBR 拥塞控制
BbrCongestionControlInitialize(QUIC_CONGESTION_CONTROL* Cc, const QUIC_SETTINGS_INTERNAL* Settings)

// 获取带宽估计
BbrCongestionControlGetBandwidth(const QUIC_CONGESTION_CONTROL* Cc)

// 获取拥塞窗口
BbrCongestionControlGetCongestionWindow(const QUIC_CONGESTION_CONTROL* Cc)

// 处理数据确认
BbrCongestionControlOnDataAcknowledged(QUIC_CONGESTION_CONTROL* Cc, const QUIC_ACK_EVENT* AckEvent)
```

## 5. 拥塞控制初始化与选择

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QuicCongestionControlInitialize(
    _In_ QUIC_CONGESTION_CONTROL* Cc,
    _In_ const QUIC_SETTINGS_INTERNAL* Settings
    )
{
    CXPLAT_DBG_ASSERT(Settings->CongestionControlAlgorithm < QUIC_CONGESTION_CONTROL_ALGORITHM_MAX);

    switch (Settings->CongestionControlAlgorithm) {
    default:
        QuicTraceLogConnWarning(
            InvalidCongestionControlAlgorithm,
            QuicCongestionControlGetConnection(Cc),
            "Unknown congestion control algorithm: %hu, fallback to Cubic",
            Settings->CongestionControlAlgorithm);
        __fallthrough;
    case QUIC_CONGESTION_CONTROL_ALGORITHM_CUBIC:
        CubicCongestionControlInitialize(Cc, Settings);
        break;
    case QUIC_CONGESTION_CONTROL_ALGORITHM_BBR:
        BbrCongestionControlInitialize(Cc, Settings);
        break;
    }
}
```

拥塞控制算法的选择在连接初始化时通过 `Settings->CongestionControlAlgorithm` 参数指定，默认情况下使用 CUBIC 算法。

## 6. 拥塞控制与定时器的关系

拥塞控制算法与 MsQuic 的定时器机制紧密相关，特别是以下定时器：

1. **QUIC_CONN_TIMER_LOSS_DETECTION** - 丢包检测定时器
   - 用于检测数据包丢失并触发重传
   - 拥塞控制算法根据丢包事件调整拥塞窗口

2. **QUIC_CONN_TIMER_PACING** - 流量控制节奏定时器
   - 控制数据包发送的时间间隔
   - 实现平滑的数据发送，避免突发流量

## 7. 拥塞控制状态图

### 7.1 CUBIC 状态图

```mermaid
stateDiagram-v2
    [*] --> 慢启动
    慢启动 --> 拥塞避免: 达到慢启动阈值
    慢启动 --> 恢复: 检测到丢包
    拥塞避免 --> 恢复: 检测到丢包
    恢复 --> 拥塞避免: 恢复完成
    恢复 --> 持续拥塞: 检测到持续拥塞
    持续拥塞 --> 恢复: 恢复完成
    持续拥塞 --> 慢启动: 重置连接
```

### 7.2 BBR 状态图

```mermaid
stateDiagram-v2
    [*] --> STARTUP
    STARTUP --> DRAIN: 检测到带宽饱和
    DRAIN --> PROBE_BW: 队列排空
    PROBE_BW --> PROBE_RTT: 需要更新最小RTT
    PROBE_RTT --> PROBE_BW: 完成RTT测量
    PROBE_BW --> PROBE_BW: 周期性探测带宽
```

## 8. 拥塞控制流程图

### 8.1 数据发送流程

```mermaid
flowchart TD
    A[准备发送数据] --> B[检查拥塞窗口]
    B --> C{BytesInFlight < CongestionWindow?}
    C -->|是| D[计算发送限额]
    C -->|否| E[暂停发送]
    D --> F[发送数据]
    F --> G[更新BytesInFlight]
    G --> H[更新拥塞控制状态]
```

### 8.2 数据确认流程

```mermaid
flowchart TD
    A[收到ACK] --> B[更新BytesInFlight]
    B --> C{是否在恢复状态?}
    C -->|是| D[检查是否完成恢复]
    C -->|否| E{是否在慢启动?}
    E -->|是| F[增加拥塞窗口]
    E -->|否| G[拥塞避免算法调整窗口]
    F --> H[检查是否达到慢启动阈值]
    G --> I[更新拥塞控制状态]
    H --> I
    D --> I
```

### 8.3 拥塞事件处理流程

```mermaid
flowchart TD
    A[检测到拥塞事件] --> B[进入恢复状态]
    B --> C{是否为持续拥塞?}
    C -->|是| D[大幅减小拥塞窗口]
    C -->|否| E[减小拥塞窗口]
    D --> F[更新拥塞控制参数]
    E --> F
```

## 9. 拥塞控制算法使用场景

### 9.1 CUBIC 适用场景

- 高带宽长延迟网络 (Long Fat Networks)
- 需要与现有 TCP 流量公平竞争的场景
- 对丢包敏感的应用

### 9.2 BBR 适用场景

- 高丢包率网络环境
- 需要更高吞吐量的应用
- 卫星链路等特殊网络环境
- 移动网络等带宽波动较大的场景

## 10. 总结

MsQuic 实现了两种先进的拥塞控制算法：CUBIC 和 BBR，为不同的网络环境和应用场景提供了优化选择。这些算法通过动态调整发送速率，实现了高效的网络资源利用，同时避免了网络拥塞。

拥塞控制算法与 MsQuic 的定时器机制、丢包检测、流量控制等组件紧密协作，共同保证了 QUIC 协议的高性能和可靠性。通过选择合适的拥塞控制算法，应用程序可以在不同的网络条件下获得最佳的传输性能。
