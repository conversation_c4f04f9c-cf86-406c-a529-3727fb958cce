=pod

=head1 NAME

BIO_s_core, BIO_new_from_core_bio - OSSL_CORE_BIO functions

=head1 SYNOPSIS

 #include <openssl/bio.h>

 const BIO_METHOD *BIO_s_core(void);

 BIO *BIO_new_from_core_bio(OSSL_LIB_CTX *libctx, OSSL_CORE_BIO *corebio);

=head1 DESCRIPTION

BIO_s_core() returns the core BIO method function.

A core BIO is treated as source/sink BIO which communicates to some external
BIO. This is primarily useful to provider authors. A number of calls from
libcrypto into a provider supply an OSSL_CORE_BIO parameter. This represents
a BIO within libcrypto, but cannot be used directly by a provider. Instead it
should be wrapped using a BIO_s_core().

Once a BIO is constructed based on BIO_s_core(), the associated OSSL_CORE_BIO
object should be set on it using BIO_set_data(3). Note that the BIO will only
operate correctly if it is associated with a library context constructed using
OSSL_LIB_CTX_new_from_dispatch(3). To associate the BIO with a library context
construct it using BIO_new_ex(3).

BIO_new_from_core_bio() is a convenience function that constructs a new BIO
based on BIO_s_core() and that is associated with the given library context. It
then also sets the OSSL_CORE_BIO object on the BIO using BIO_set_data(3).

=head1 RETURN VALUES

BIO_s_core() return a core BIO B<BIO_METHOD> structure.

BIO_new_from_core_bio() returns a BIO structure on success or NULL on failure.
A failure will most commonly be because the library context was not constructed
using OSSL_LIB_CTX_new_from_dispatch(3).

=head1 HISTORY

BIO_s_core() and BIO_new_from_core_bio() were added in OpenSSL 3.0.

=head1 EXAMPLES

Create a core BIO and write some data to it:

 int some_function(OSSL_LIB_CTX *libctx, OSSL_CORE_BIO *corebio) {
     BIO *cbio = BIO_new_from_core_bio(libctx, corebio);

     if (cbio == NULL)
         return 0;

     BIO_puts(cbio, "Hello World\n");

     BIO_free(cbio);
     return 1;
 }

=head1 COPYRIGHT

Copyright 2021-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
