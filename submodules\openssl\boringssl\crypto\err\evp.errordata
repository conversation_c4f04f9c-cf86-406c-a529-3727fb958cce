<PERSON><PERSON>,100,B<PERSON><PERSON><PERSON>_TOO_SMALL
EVP,101,COMMAND_NOT_SUPPORTED
<PERSON><PERSON>,102,DECODE_ERROR
EVP,103,DIFFERENT_KEY_TYPES
EVP,104,<PERSON><PERSON><PERSON><PERSON>ENT_PARAMETERS
EVP,105,<PERSON><PERSON><PERSON><PERSON>_ERROR
EVP,106,EXPECTING_AN_EC_KEY_KEY
<PERSON>,107,EXPECTING_AN_RSA_KEY
<PERSON>,108,EXPECTING_A_DSA_KEY
<PERSON>,109,ILLEGAL_OR_UNSUPPORTED_PADDING_MODE
EVP,110,INVALID_DIGEST_LENGTH
EVP,111,INVALID_DIGEST_TYPE
E<PERSON>,112,INVALID_<PERSON>EYBITS
E<PERSON>,113,INVALID_MGF1_MD
EVP,114,INVALID_OPERATION
EVP,115,INVALID_PADDING_MODE
<PERSON>,116,INVALID_PSS_SALTLEN
EVP,117,KEYS_NOT_SET
<PERSON>,118,MI<PERSON>ING_PARAMETERS
EVP,119,NO_DEFAULT_DIGEST
<PERSON>,120,NO_KEY_SET
<PERSON>,121,NO_MDC2_SUPPORT
EVP,122,NO_<PERSON>ID_FOR_CURVE
EVP,123,NO_OPERATION_SET
EVP,124,NO_PARAMETERS_SET
EVP,125,OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE
EVP,126,OPERATON_NOT_INITIALIZED
EVP,127,UNKNOWN_PUBLIC_KEY_TYPE
EVP,128,UNSUPPORTED_ALGORITHM
EVP,129,UNSUPPORTED_PUBLIC_KEY_TYPE
