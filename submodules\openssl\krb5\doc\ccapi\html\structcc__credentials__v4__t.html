<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_credentials_v4_t Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_credentials_v4_t Struct Reference<br>
<small>
[<a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a>]</small>
</h1><!-- doxytag: class="cc_credentials_v4_t" --><hr><a name="_details"></a><h2>Detailed Description</h2>
If a cc_credentials_t variable is used to store Kerberos v4 credentials, then credentials.credentials_v4 points to a v4 credentials structure. This structure is similar to a krb4 API CREDENTIALS structure. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v4__t.html#2d41fe5eaeafcfae38d60dae26985ac2">version</a>
<li>char <a class="el" href="structcc__credentials__v4__t.html#9f819063dece13be2211723c071cc05a">principal</a> [cc_v4_name_size]
<li>char <a class="el" href="structcc__credentials__v4__t.html#76949f2eaa30043f779dd32a617b36b2">principal_instance</a> [cc_v4_instance_size]
<li>char <a class="el" href="structcc__credentials__v4__t.html#2034c72f7997740d1bd0526fde941f36">service</a> [cc_v4_name_size]
<li>char <a class="el" href="structcc__credentials__v4__t.html#188bf95cfe0ec75c60e5df82a65ce4f1">service_instance</a> [cc_v4_instance_size]
<li>char <a class="el" href="structcc__credentials__v4__t.html#1965bd82f992c9448d2600d241c11143">realm</a> [cc_v4_realm_size]
<li>unsigned char <a class="el" href="structcc__credentials__v4__t.html#5833b04b0672722de1dc40148eac67ca">session_key</a> [cc_v4_key_size]
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#cee6149add6477c273b3318d6497ca0b">kvno</a>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#dbfece338488ae1e84f642e1675a2248">string_to_key_type</a>
<li><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v4__t.html#02d649915754b7903b7a60ef9fb9f036">issue_date</a>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#a546cc61e206f01e8657cc4d22e9e4cd">lifetime</a>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v4__t.html#99252d53c89be046c8ce4d12e8bb2fe4">address</a>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#fbf8c355fc354f976f66db1e51034d9e">ticket_size</a>
<li>unsigned char <a class="el" href="structcc__credentials__v4__t.html#c4f3ef871699e35563771cfe9889c8e5">ticket</a> [cc_v4_ticket_size]
</ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="2d41fe5eaeafcfae38d60dae26985ac2"></a><!-- doxytag: member="cc_credentials_v4_t::version" ref="2d41fe5eaeafcfae38d60dae26985ac2" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v4__t.html#2d41fe5eaeafcfae38d60dae26985ac2">version</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="9f819063dece13be2211723c071cc05a"></a><!-- doxytag: member="cc_credentials_v4_t::principal" ref="9f819063dece13be2211723c071cc05a" args="[cc_v4_name_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char <a class="el" href="structcc__credentials__v4__t.html#9f819063dece13be2211723c071cc05a">principal</a>[cc_v4_name_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the first component of the client principal     </td>
  </tr>
</table>
<a class="anchor" name="76949f2eaa30043f779dd32a617b36b2"></a><!-- doxytag: member="cc_credentials_v4_t::principal_instance" ref="76949f2eaa30043f779dd32a617b36b2" args="[cc_v4_instance_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char <a class="el" href="structcc__credentials__v4__t.html#76949f2eaa30043f779dd32a617b36b2">principal_instance</a>[cc_v4_instance_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the second component of the client principal     </td>
  </tr>
</table>
<a class="anchor" name="2034c72f7997740d1bd0526fde941f36"></a><!-- doxytag: member="cc_credentials_v4_t::service" ref="2034c72f7997740d1bd0526fde941f36" args="[cc_v4_name_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char <a class="el" href="structcc__credentials__v4__t.html#2034c72f7997740d1bd0526fde941f36">service</a>[cc_v4_name_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the first component of the service principal     </td>
  </tr>
</table>
<a class="anchor" name="188bf95cfe0ec75c60e5df82a65ce4f1"></a><!-- doxytag: member="cc_credentials_v4_t::service_instance" ref="188bf95cfe0ec75c60e5df82a65ce4f1" args="[cc_v4_instance_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char <a class="el" href="structcc__credentials__v4__t.html#188bf95cfe0ec75c60e5df82a65ce4f1">service_instance</a>[cc_v4_instance_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the second component of the service principal     </td>
  </tr>
</table>
<a class="anchor" name="1965bd82f992c9448d2600d241c11143"></a><!-- doxytag: member="cc_credentials_v4_t::realm" ref="1965bd82f992c9448d2600d241c11143" args="[cc_v4_realm_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">char <a class="el" href="structcc__credentials__v4__t.html#1965bd82f992c9448d2600d241c11143">realm</a>[cc_v4_realm_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
A properly quoted string representation of the realm     </td>
  </tr>
</table>
<a class="anchor" name="5833b04b0672722de1dc40148eac67ca"></a><!-- doxytag: member="cc_credentials_v4_t::session_key" ref="5833b04b0672722de1dc40148eac67ca" args="[cc_v4_key_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">unsigned char <a class="el" href="structcc__credentials__v4__t.html#5833b04b0672722de1dc40148eac67ca">session_key</a>[cc_v4_key_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket session key     </td>
  </tr>
</table>
<a class="anchor" name="cee6149add6477c273b3318d6497ca0b"></a><!-- doxytag: member="cc_credentials_v4_t::kvno" ref="cee6149add6477c273b3318d6497ca0b" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#cee6149add6477c273b3318d6497ca0b">kvno</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Key version number     </td>
  </tr>
</table>
<a class="anchor" name="dbfece338488ae1e84f642e1675a2248"></a><!-- doxytag: member="cc_credentials_v4_t::string_to_key_type" ref="dbfece338488ae1e84f642e1675a2248" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#dbfece338488ae1e84f642e1675a2248">string_to_key_type</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
String to key type used. See cc_string_to_key_type for valid values     </td>
  </tr>
</table>
<a class="anchor" name="02d649915754b7903b7a60ef9fb9f036"></a><!-- doxytag: member="cc_credentials_v4_t::issue_date" ref="02d649915754b7903b7a60ef9fb9f036" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> <a class="el" href="structcc__credentials__v4__t.html#02d649915754b7903b7a60ef9fb9f036">issue_date</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Time when the ticket was issued     </td>
  </tr>
</table>
<a class="anchor" name="a546cc61e206f01e8657cc4d22e9e4cd"></a><!-- doxytag: member="cc_credentials_v4_t::lifetime" ref="a546cc61e206f01e8657cc4d22e9e4cd" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#a546cc61e206f01e8657cc4d22e9e4cd">lifetime</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket lifetime in 5 minute units     </td>
  </tr>
</table>
<a class="anchor" name="99252d53c89be046c8ce4d12e8bb2fe4"></a><!-- doxytag: member="cc_credentials_v4_t::address" ref="99252d53c89be046c8ce4d12e8bb2fe4" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__credentials__v4__t.html#99252d53c89be046c8ce4d12e8bb2fe4">address</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
IPv4 address of the client the ticket was issued for     </td>
  </tr>
</table>
<a class="anchor" name="fbf8c355fc354f976f66db1e51034d9e"></a><!-- doxytag: member="cc_credentials_v4_t::ticket_size" ref="fbf8c355fc354f976f66db1e51034d9e" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a> <a class="el" href="structcc__credentials__v4__t.html#fbf8c355fc354f976f66db1e51034d9e">ticket_size</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket size (no greater than cc_v4_ticket_size)     </td>
  </tr>
</table>
<a class="anchor" name="c4f3ef871699e35563771cfe9889c8e5"></a><!-- doxytag: member="cc_credentials_v4_t::ticket" ref="c4f3ef871699e35563771cfe9889c8e5" args="[cc_v4_ticket_size]" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">unsigned char <a class="el" href="structcc__credentials__v4__t.html#c4f3ef871699e35563771cfe9889c8e5">ticket</a>[cc_v4_ticket_size]          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Ticket data     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
