#
# Generated makefile dependencies follow.
#
$(OUTPRE)adata.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h adata.c
$(OUTPRE)etinfo.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h etinfo.c
$(OUTPRE)forward.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h forward.c
$(OUTPRE)gcred.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h gcred.c
$(OUTPRE)hist.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssrpc/types.h \
  $(BUILDTOP)/include/kadm5/admin.h $(BUILDTOP)/include/kadm5/chpass_util_strings.h \
  $(BUILDTOP)/include/kadm5/kadm_err.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/kdb.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  hist.c
$(OUTPRE)hooks.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h hooks.c
$(OUTPRE)hrealm.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h hrealm.c
$(OUTPRE)icinterleave.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h icinterleave.c
$(OUTPRE)icred.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/krb5.h \
  icred.c
$(OUTPRE)kdbtest.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssrpc/types.h $(BUILDTOP)/include/kadm5/admin.h \
  $(BUILDTOP)/include/kadm5/chpass_util_strings.h $(BUILDTOP)/include/kadm5/kadm_err.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/kdb.h \
  $(top_srcdir)/include/krb5.h kdbtest.c
$(OUTPRE)localauth.$(OBJEXT): $(BUILDTOP)/include/krb5/krb5.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h localauth.c
$(OUTPRE)plugorder.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssrpc/types.h \
  $(BUILDTOP)/include/kadm5/admin.h $(BUILDTOP)/include/kadm5/chpass_util_strings.h \
  $(BUILDTOP)/include/kadm5/kadm_err.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/kdb.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/krb5/pwqual_plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h plugorder.c
$(OUTPRE)rdreq.$(OBJEXT): $(BUILDTOP)/include/krb5/krb5.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h rdreq.c
$(OUTPRE)replay.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h replay.c
$(OUTPRE)responder.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-json.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/krb5.h responder.c
$(OUTPRE)s2p.$(OBJEXT): $(BUILDTOP)/include/krb5/krb5.h \
  $(COM_ERR_DEPS) $(top_srcdir)/include/krb5.h s2p.c
$(OUTPRE)s4u2self.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h s4u2self.c
$(OUTPRE)s4u2proxy.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h s4u2proxy.c
$(OUTPRE)t_inetd.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(COM_ERR_DEPS) t_inetd.c
$(OUTPRE)unlockiter.$(OBJEXT): $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssrpc/types.h $(BUILDTOP)/include/kadm5/admin.h \
  $(BUILDTOP)/include/kadm5/chpass_util_strings.h $(BUILDTOP)/include/kadm5/kadm_err.h \
  $(BUILDTOP)/include/krb5/krb5.h $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/kdb.h \
  $(top_srcdir)/include/krb5.h unlockiter.c
