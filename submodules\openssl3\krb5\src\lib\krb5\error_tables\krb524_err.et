# Copyright 1994 by OpenVision Technologies, Inc.
# 
# Permission to use, copy, modify, distribute, and sell this software
# and its documentation for any purpose is hereby granted without fee,
# provided that the above copyright notice appears in all copies and
# that both that copyright notice and this permission notice appear in
# supporting documentation, and that the name of OpenVision not be used
# in advertising or publicity pertaining to distribution of the software
# without specific, written prior permission. OpenVision makes no
# representations about the suitability of this software for any
# purpose.  It is provided "as is" without express or implied warranty.
# 
# OPENVISION DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL OPENVISION BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF
# USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
# OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.
# 

error_table k524

error_code KRB524_BADKEY, "Cannot convert V5 keyblock"
error_code KRB524_BADADDR, "Cannot convert V5 address information"
error_code KRB524_BADPRINC, "Cannot convert V5 principal"
error_code KRB524_BADREALM, "V5 realm name longer than V4 maximum"
error_code KRB524_V4ERR, "Kerberos V4 error"
error_code KRB524_ENCFULL, "Encoding too large"
error_code KRB524_DECEMPTY, "Decoding out of data"
error_code KRB524_NOTRESP, "Service not responding"
error_code KRB524_KRB4_DISABLED,	"Kerberos version 4 support is disabled"

end
