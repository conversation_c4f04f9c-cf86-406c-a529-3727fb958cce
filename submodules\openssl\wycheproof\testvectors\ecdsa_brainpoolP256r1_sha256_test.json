{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 389, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "GroupIsomorphism": "Some EC groups have isomorphic groups that allow an efficient implementation. This is a test vector that contains values that are edge cases on such an isomorphic group.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04019a2d9637743a63ddaefdbca0ee229a163b809b9b145e5313bbeb8defeab9d6548caf89bf5ba49499404145651234336401b9b2843a579ed152e090f11b9e59", "wx": "019a2d9637743a63ddaefdbca0ee229a163b809b9b145e5313bbeb8defeab9d6", "wy": "548caf89bf5ba49499404145651234336401b9b2843a579ed152e090f11b9e59"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004019a2d9637743a63ddaefdbca0ee229a163b809b9b145e5313bbeb8defeab9d6548caf89bf5ba49499404145651234336401b9b2843a579ed152e090f11b9e59", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABAGaLZY3dDpj3a79vKDuIpoWO4Cb\nmxReUxO7643v6rnWVIyvib9bpJSZQEFFZRI0M2QBubKEOlee0VLgkPEbnlk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220426e857aad3ff7aa96e4d200c03b45f1846a36d089ee3917768ca1a0d6d4da6e", "result": "valid", "flags": []}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30814402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082004402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "304302200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000004402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000004402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "304402800a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0280678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "3046000002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "3049498177304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "30482500304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3046304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "3049222549817702200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30482224250002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304c222202200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0004deadbeef0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304902200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f22254981770220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f222425000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "304c02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f22220220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "304caa00bb00cd00304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "304aaa02aabb304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304c2228aa00bb00cd0002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304a2226aa02aabb02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304c02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f2228aa00bb00cd000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304a02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f2226aa02aabb0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3048228002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f00000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f22800220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080314402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3048228003200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f00000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f22800320678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e4402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f4402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "314402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "324402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff4402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "30483001023043200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "304302200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "3043200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": ["BER"]}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c3900", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c3905000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "308002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "3046300002200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c393000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "304702200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3046304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "302202200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "306602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30450281200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f028120678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3046028200200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02820020678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304402210a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3044021f0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0221678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f021f678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3049028501000000200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304902200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02850100000020678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304d02890100000000000000200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304d02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0289010000000000000020678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304802847fffffff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02847fffffff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30480284ffffffff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0284ffffffff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30490285ffffffffff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304902200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0285ffffffffff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304c0288ffffffffffffffff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304c02200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0288ffffffffffffffff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304402ff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02ff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "30220220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3023020220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302302200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "304602220a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f00000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0222678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3046022200000a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02220000678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f00000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "304602220a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f05000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "304602200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0222678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c390500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "302402810220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "302402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "302405000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "302402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304400200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304401200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304403200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304404200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3044ff200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0020678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0120678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0320678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0420678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111fff20678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "302402000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "302402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "3048222402010a021f5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "304802200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f2224020167021f8cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "30440220085f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220658cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d119f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737cb9", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "3043021f0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d110220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "3043021f5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "304302200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f021f678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "304302200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f021f8cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30450221ff0a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0221ff678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "30250901800220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "302502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "30250201000220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "302502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022100b45ae44c5c1bb1ff143702d28ee43a86a590e44bcafda560b652e5bfdde567c60220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221ff60643495183e5e86976aedb153dd1fa38d1def04603a57719616c8baaf54ba780220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30440220f5a0738f45d2f7bd2a2f07be0e9f52eae6a89657ea640196d9cb28c2b962eee10220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221009f9bcb6ae7c1a1796895124eac22e05c72e210fb9fc5a88e69e9374550ab45880220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221ff4ba51bb3a3e44e00ebc8fd2d711bc5795a6f1bb435025a9f49ad1a40221a983a0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30450221010a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022100f5a0738f45d2f7bd2a2f07be0e9f52eae6a89657ea640196d9cb28c2b962eee10220678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02210111882a3c969d5bcde5e743207acbd4f19408be76e0d514d7a9af7b6457bbd2e0", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0220bd917a8552c00855691b2dff3fc4ba0e7b95c92f7611c6e889735e5f292b2592", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f022098732d9f0b514dee587ec77022b7b87ff830bc2cd48c921fe66e931e3f8c83c7", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f0221feee77d5c36962a4321a18bcdf85342b0e6bf741891f2aeb285650849ba8442d20", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f022101678cd260f4aeb211a781388fdd48478007cf43d32b736de019916ce1c0737c39", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502200a5f8c70ba2d0842d5d0f841f160ad15195769a8159bfe692634d73d469d111f02210098732d9f0b514dee587ec77022b7b87ff830bc2cd48c921fe66e931e3f8c83c7", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a70201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a60201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a80201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53770201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53780201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a6", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5378090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3638393434", "sig": "304402200c939a92486c6d0d619510b4a94162b9221be2eb15faf878bff75e6cdf4e370702203977619b43e6b4ea1870d861206483b306560e3c4a3ef82b11a802ff8892dc1d", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "343236343739373234", "sig": "304402205583dd480964bd2332885fbb50b7475ebd428399e7166fd9bd529611534b9f3402200ed035a02c4b665cacb70de8e822facd71645a15f93fee661324f850b847b51d", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "37313338363834383931", "sig": "30440220300e26027ce7d3f21c8571dc690b1bb990e8fc49ad3e95374bd543b2e22badc6022022bc8f2445cd4956bc0db553966a0718aeb5ead65bc66ddb21fea0e571a87ee1", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "3130333539333331363638", "sig": "304402202907cb01a82a88046640a523f9b9854d95b7ec2ddd67c20723d05829e8438a77022038ca08e58623560f724a3e3f9ba0e9ec7974976dd34e6940c0fe6168d540e39b", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "33393439343031323135", "sig": "304402200c35840f7b7319f19fd72f29fea4cf937aba2c3fe1dc01aec63c21094c5d354802207bf699868c2b694547aebe9b98c01c5efbe982a84150390894563d4e2cb240b6", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "31333434323933303739", "sig": "304402204272ff20b8c3d19e8c84141fbe4d1681fa71b51f6c10360db7affac989274d2302206772ff768ee6a3edaf0dbdd7b5c6962c2acc8cb14e6347631e25940189729468", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "33373036323131373132", "sig": "304502200a1e072c48a62a583bf94fe63809e95f3202176bfa6d28de8f75a4a3256ca21f0221009514a6e5b235c29152561cc9492cf47477a0fe23f56040d7206bfb4eb3e18798", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "333433363838373132", "sig": "3044022002bcbd38a3e3113445ad2ee42faeaee9fed00277e0b15521329f4c27c963af01022006cf399deb1f6fd692075d236272b99c3336aea2cfac34d904646cc1daf54de6", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "31333531353330333730", "sig": "3044022062f3a6a9c9f457211b46b1ca3a782f11f44cb9360bb30702e67136036ccba39e022022f02e5f647ceb3d0c49f2e7ac9bbb31b7e3ae29a5ed670c96cad6d0f45df389", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "36353533323033313236", "sig": "3045022023d679aed9066b611820a8e02b3daa922b10d5596c8ceb7bd4e4fcd6e5e1dca70221009626e1d2205d60e39b633852f623f0f8b35e44797e08c6fad196c33be69b5ac7", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "31353634333436363033", "sig": "304402200e4c5c077f14a4db197654f8081f10ac2229e6f2084405aea525679e592539a902201355d43667402b9f01959140c414f18d908e2559e57adf35ce794dbc8e222006", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "34343239353339313137", "sig": "304402203246b33954cf1dd4a216218d49b14e39db82004ba0556fb591357aff76a1ea6302205b5fcb726ebf18c9151a26a5b0800cbf95b5edc084b42dc6dc7fbb9a0aed8425", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "3130393533323631333531", "sig": "30440220361a8def874057c715423843bd7bf0775ba6366fa48ca83e1cdce206bf94c2bf0220365e97493d3382681f1d94657e9888245c9b0762ee7f4ca02e738afdbba274d6", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "35393837333530303431", "sig": "304402202c5f51bc91969fd5b804e751323fc80294b0b5b1e20e195ec9bdc6a7806da13f02204c246c949bce43d303201fa0d989e70674766555e8d3a99c26babb658d1f7db8", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "33343633303036383738", "sig": "3045022100a9e3f1e83108be78668d4bac7ffb2918d38100ba01f37de5b923eeca07cc05e302203f0d81bcc08802a435599759f51c89f816742710885b4137758130e8acf707d4", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "39383137333230323837", "sig": "3045022100959e9811bb18b4865fde6d5f9c246d67e48d7a5c7ce46d7afb6f5ec0b26d506002200091a097618f2517ad6dcf49bcc208e94cb81af87f65b7880580f99858a9a915", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323232303431303436", "sig": "304502204f9231b1cbaea183ed9d8591ae3e9f0439201e1067ff00535a415396b77811d60221009851c799a311abaefa08c412f6f679a000a6edaa005d05f550a62ff9a6a1a507", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "36363636333037313034", "sig": "304402205479acb76c38d47f21940855f1800978a52fb10b7cc9b07caf88af67f26971430220244f3cdd683555b88a45e975073735d38713da4bdea340b5fb87d3c443adb0ee", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "31303335393531383938", "sig": "3045022014990149d3a8f3c96e9c62952f90ef21cbcc0d03da802f72432a041da54db5be02210087427b96d28499707a6788705cd8a5ee9fd42e2d1f1273752337efcd06aa88a9", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "31383436353937313935", "sig": "304502205bb244b511a9828fbe7a041af341a93b242b513310de9f4bc366e18b93a3ce34022100978be5d58ce70c92dea75ce2f8e88f093f5e4675e750fd088777a7411526c1f7", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "33313336303436313839", "sig": "30450220070e64f4d19f9fd9a8d3f0a64f951c41db2f0e13490e7ac0b3f6066bc1e540a7022100835b25029a2ced8df57b0343a2c718db72c2d31f7ef66b230c97d20281d49a33", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "32363633373834323534", "sig": "304402202b5a6dc14e98d2e6c0b627568a748bda04c09500bc63bd744f5dee967db0f0b102203452b13ef8dc01a0b785fbb4fcd057a5880c418427283abc7aa7fa07d507eed0", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "31363532313030353234", "sig": "304402201d91bda90d0831be058f610fe3e6451791e09689c52bd466ef74dd85b3cbd12102204ba37a9341e5923ea93e357344fe7b73446e207a7e449607b1482c510e93b630", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "35373438303831363936", "sig": "304402206a32e1625c1eb7d40b3145f894c7138d6232a6116d50f1270a0e971e2b7a8e75022061b6aae56819272813319f7c214f83ce5fccdb58878d592ab0f4479a52d970e1", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "36333433393133343638", "sig": "304402205b7526f09dab248551ed8b1229c2447a4521d2d6e22902acbc176c501f5f5f7e02203186552f700d9e6b551c893ed2aed9556b3f0ac2a5e2772f8fb1a184949262cb", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "31353431313033353938", "sig": "3044022030d0ffa9c2be042ccd2c9adbcbbba22cc044d69abf37eff2bcab91d45be9b0bd0220482dd72aa3b3f3f2e6dd4a075fa962b8f6fc25e9d32d0dccbd80831acf7595e0", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "3130343738353830313238", "sig": "30450220200d3b5f915863ada8c84ef5eb50ecf0ab43e2bac10a4c42cf3719121a8d37cd0221009d137e11a050bfbba746c19ad5f7195c86f24115d1fadfb19ad2cb5624126cda", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "3130353336323835353638", "sig": "304402204ac55470789095e9e250332f3790f865fbcc58934588c774babf22de6a8a695802202cfefb0e2be0542c97eb61914f23fb37b58fb17d0d6b766a8f63c8d0dc79e52b", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "393533393034313035", "sig": "3045022100908c1e6da625879fc116ddb65173b9355fa8eb038063de2cec1934e8fb2bfc2702206e084ff7c043edfb161aea2605a111cf43d58388e061e8019e99526376e4c71e", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "393738383438303339", "sig": "304502210097328e1050fc2d44ec89836a7eaae360d6f9d996855e8b144d0c273c4866d7fe02203919d7ced9f3e3284978546394fbb277f84d26598dbe83da4ba7c1de372b3340", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "33363130363732343432", "sig": "30450221009881e8f75db8163d2be1fc11491926c4125374440da94750a19ecaf8a83b71fc02204a9a191a9da8fa3d5641cbb5a88cac5b3780fbbef8ef1a445782394925efc5b1", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "31303534323430373035", "sig": "3044022070cb8bc7d5c372c73cf36fe69aa1a509fe0cf2be642e085ac979d6eefddaa9e10220500402f496dc8d904c709695ff02714e607c4bee9d064cd4654b6c466f4010e8", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "35313734343438313937", "sig": "3044022039c8f870185f87957e009d01e52fbf6c7ae50d734d39ec4113b37b7bd1b68066022073d6da2b777ce0c43d49080857c6ec58546fddf17d2676f10f88ddc900ca1891", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "31393637353631323531", "sig": "304402202e95b702ed138f42614f07a1b21548ea1d247a4a7fd765628bab68551129ad3802202e9a6af078b51812ed71b0eab65350cd081f7999a24a56e96af9d5c5f6bdaf0f", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "33343437323533333433", "sig": "304402205837b687f2128063dc67f512cb6670f122b611257f536d45e3984f5ebbc3cd4d02206a6c0c41b9cc37ae02c2218d3b8cd80cd3c4fc25771c0caab3b8ed2c611cf7cb", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "333638323634333138", "sig": "304502205a73c75d2b5c48af17b7847244262bb9b2c3f2697a9d8c605758a2d33cccd18f02210097f12aa04b2582373f9bea646bce1b129030ea5f35c9dc2a149e90aa3b56345c", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33323631313938363038", "sig": "3045022032b603132a96c5b957b08c88532e49fcb73cd7c5f71a1e6ed14a5cc1776d2da702210093be0e4c9844bec9d2b62b424e618a845a98537b2356c1f473bba13b08458eea", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "39363738373831303934", "sig": "30450221009b76b7aac0a13bf217f24d335bc04694ecdbd5acfe4ec23c065efeb7936a1c620220432cde74fdbb4f5437cdeca53cb7ab79f692694f91ed3735fbc4e08a3f527881", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "34393538383233383233", "sig": "30450221008f2565b517f62a3b1e19b0917ab2b223fc8193cc0fdf3ab9692bc42cf40910e802201dccfbed8b90ee5391ea743e35b60ed31d19edfbd94504badca4aa4cf2a7bb31", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "383234363337383337", "sig": "3044022031c627fd791f734421e5502618aec447c67029b2794ee12b08eeb6c59aedb3ee022008f91f3789bd01e5b9d93941cf46698d5e1a2708e70ee9a226e81e7f4a414e9e", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3131303230383333373736", "sig": "304402206005293132d7eac0e72b9b218d03212675d5aae0da97bccdf1a5ff784de5cde6022013a155c74a9ab27cbdf6cae18d4d1f18b8212d8018551e2baec91979ea5b4c49", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "313333383731363438", "sig": "304402203a6dab51ed9027f5cae192e0586a32c8ef2276ceba3b796059dca135e361795d02204bf16b0e62e32a945088f55fb428159af78296dd4f8dfd9713bdb2f677cbcd12", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "333232313434313632", "sig": "3046022100961de77ed9cf6170d925c233bd3e20eef9bbe6d6c8dac28acde46011f99f8bff022100977de04779ffe3afe708d81ce8a1ed6c7d2a9a25ef9959c7a951a0555a6d3792", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "3130363836363535353436", "sig": "30440220778581b3d4030031141e555fa1dbebaef0eed019e0b897b5076544ab80498b9c02207132c8d109c1f1a6c10f81e9fc11adea4b9cff599208b6d9cb4e4b27f1972846", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "3632313535323436", "sig": "30450221009eeac8f07c40cc8ee3cba107af49d526731d8b7c70130cbb6efa3c61505d6337022062db38226b71f64a5b598ab7c4e3f89880fe0d0749dfd5c7a38a3eec3c793876", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "37303330383138373734", "sig": "30430220673d41c17e727f0125175b2a9f0561ecc5cf9cd49035828ba7c47545a0b338f4021f459ef978e7b03468c80fd4533a334755a0826bf5a30df919129e352d347562", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "35393234353233373434", "sig": "30460221008a97f19b0809042cdafe9c32bc0b0b01218a49867a6882d64d5b7bc255eb773d022100904662b5dfd8cd94eaefd57e5d4f2d14268e1b8c4fbd4ac4e5080f79d53fd24c", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "31343935353836363231", "sig": "30440220287a88bdb561fa2785ca258663f86d3b07aac949f647ee572621b0b70eb3e9ca02204a6d7916418443deb4c43f5c69f6490952cf53ee69eec1ac69e144b8f9e26307", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "34303035333134343036", "sig": "30440220250db6a8b3813b13b6fb7bf19896f13a502be453c204e6a813a164dbdd9c66ca02201d96683ac97f5874ac9538b57bf1eaa50a11a33e9abb825d6b7a7546a698606e", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "33303936343537353132", "sig": "3045022100a29b2c4be50f1724a1ce9acd4c5129b391b4b9009abb582397a522c771d54abd02200ec1d7aedbfe4e743d7627ea8d207c2460ae4c9f2134b0f84a0255205ac23482", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "32373834303235363230", "sig": "30440220149f0508aef9fbccf32e1bd3199d630240bb6577593e87566b0a14a5b6f2099902205d37b409c01fb9b6cf4ea14432c35631694402d2875a301d761d81811469628d", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32363138373837343138", "sig": "304402206c9b110d8e4453d82ec51a5a691b152edf9fb1a9947bd001beb24d56f3bf27af02202a80bbd2f827cc23157526df6ea4e0e324b765a50be77f7e9667558a165eb692", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "31363432363235323632", "sig": "30450221009086a5c93823b1df21f63951ed6e707fba0d899eef711100e32f2d6017da659002201f831ed30c129dab4266272e01283210ed823c55907ac5ecda85d70bd80279c3", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "36383234313839343336", "sig": "304402202af63547dc5ffc8ba4d168d368d9228132a0efa20e3255c332219feced80039502203642f53ce9521fab754be7711f00af7888222bf2bbf1ed8995e03b55c98a6022", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "343834323435343235", "sig": "304502210091e9acef9bc28c910891b80320af3603c4306174f17e97059267fc817814ff1f02207a9c833beb73bdd62df64952b4c848d2180fae385f8084f1fc5b1b1c64575007", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044b402a9ae18fc1a87cda337483900499fe729e471607671651a263fbf0d93f781ef9b0f98fb73bcb605a7823a427ea5f0d98788c7dae42a04536202022c021cd", "wx": "4b402a9ae18fc1a87cda337483900499fe729e471607671651a263fbf0d93f78", "wy": "1ef9b0f98fb73bcb605a7823a427ea5f0d98788c7dae42a04536202022c021cd"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200044b402a9ae18fc1a87cda337483900499fe729e471607671651a263fbf0d93f781ef9b0f98fb73bcb605a7823a427ea5f0d98788c7dae42a04536202022c021cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABEtAKprhj8GofNozdIOQBJn+cp5H\nFgdnFlGiY/vw2T94Hvmw+Y+3O8tgWngjpCfqXw2YeIx9rkKgRTYgICLAIc0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 285, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3036021100e2027b801fc479308ff5399a8825fccf022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a4", "result": "valid", "flags": []}, {"tcId": 286, "comment": "r too large", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a4", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0440a2df0f17c7873459d5e9ac11fff84deb5f40ff9a52df8745bb4770f6dbf58199c2bf4920e9c8f758c2de69e42c1cb77c58425a9dafa41d7b0873efa894cedc", "wx": "40a2df0f17c7873459d5e9ac11fff84deb5f40ff9a52df8745bb4770f6dbf581", "wy": "0099c2bf4920e9c8f758c2de69e42c1cb77c58425a9dafa41d7b0873efa894cedc"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000440a2df0f17c7873459d5e9ac11fff84deb5f40ff9a52df8745bb4770f6dbf58199c2bf4920e9c8f758c2de69e42c1cb77c58425a9dafa41d7b0873efa894cedc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABECi3w8Xx4c0WdXprBH/+E3rX0D/\nmlLfh0W7R3D22/WBmcK/SSDpyPdYwt5p5Cwct3xYQlqdr6Qdewhz76iUztw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 287, "comment": "r,s are large", "msg": "313233343030", "sig": "3046022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a3022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049de669f9444da82e429f842f31c64418d4d7b05e93f41daddd09fc181ac227c61c86210e8291fc5ae30c72e2013ec22bb97d88bf376d4a85dd1bb71b22526d1f", "wx": "009de669f9444da82e429f842f31c64418d4d7b05e93f41daddd09fc181ac227c6", "wy": "1c86210e8291fc5ae30c72e2013ec22bb97d88bf376d4a85dd1bb71b22526d1f"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200049de669f9444da82e429f842f31c64418d4d7b05e93f41daddd09fc181ac227c61c86210e8291fc5ae30c72e2013ec22bb97d88bf376d4a85dd1bb71b22526d1f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABJ3maflETaguQp+ELzHGRBjU17Be\nk/Qdrd0J/BgawifGHIYhDoKR/FrjDHLiAT7CK7l9iL83bUqF3Ru3GyJSbR8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 288, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff02204ab8de0a51481bc45794b924518f2dd6ac5cce31f3228d624c5a896f79a2d6a2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0430345b6451377b78a54ac6e110f50c7de71c2c760278373607722c53f586790759acc40014c93d4ad44778bc1a44ebaebe1a97c88ad11c1025057b6bc4377f2d", "wx": "30345b6451377b78a54ac6e110f50c7de71c2c760278373607722c53f5867907", "wy": "59acc40014c93d4ad44778bc1a44ebaebe1a97c88ad11c1025057b6bc4377f2d"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000430345b6451377b78a54ac6e110f50c7de71c2c760278373607722c53f586790759acc40014c93d4ad44778bc1a44ebaebe1a97c88ad11c1025057b6bc4377f2d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDA0W2RRN3t4pUrG4RD1DH3nHCx2\nAng3NgdyLFP1hnkHWazEABTJPUrUR3i8GkTrrr4al8iK0RwQJQV7a8Q3fy0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 289, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304502207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0221008b1b5f89f5bb74caa42d36e601a9f3c20b4e6c91ceb98a52fbfa9f81781b8a17", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04149d893f1306fb253bbf3a8691aba8a50002b0a27693aa97435a7b5cb33a55ee24b075fcdbc1a739f2f492dbe4799474ee3ad3804447e7d584e3430ce15a968a", "wx": "149d893f1306fb253bbf3a8691aba8a50002b0a27693aa97435a7b5cb33a55ee", "wy": "24b075fcdbc1a739f2f492dbe4799474ee3ad3804447e7d584e3430ce15a968a"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004149d893f1306fb253bbf3a8691aba8a50002b0a27693aa97435a7b5cb33a55ee24b075fcdbc1a739f2f492dbe4799474ee3ad3804447e7d584e3430ce15a968a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBSdiT8TBvslO786hpGrqKUAArCi\ndpOql0Nae1yzOlXuJLB1/NvBpzny9JLb5HmUdO4604BER+fVhONDDOFaloo=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 290, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04156e626649ce8236982201a24935fb3d36b0d73041b6fdca97990a8d152efb8b326f4b20a0cc4623b02a6bb17114901a01de0df1716d669d253de440cc8f9cdd", "wx": "156e626649ce8236982201a24935fb3d36b0d73041b6fdca97990a8d152efb8b", "wy": "326f4b20a0cc4623b02a6bb17114901a01de0df1716d669d253de440cc8f9cdd"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004156e626649ce8236982201a24935fb3d36b0d73041b6fdca97990a8d152efb8b326f4b20a0cc4623b02a6bb17114901a01de0df1716d669d253de440cc8f9cdd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBVuYmZJzoI2mCIBokk1+z02sNcw\nQbb9ypeZCo0VLvuLMm9LIKDMRiOwKmuxcRSQGgHeDfFxbWadJT3kQMyPnN0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 291, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0410cc7992ede28c7b4dda5c35cbd71174918e83adab0342cc3d556a413b4ce93b3f9c3b38aef0a0e687d7ee6afde70d47d6900ff0ce62156e8645b8103fc66cad", "wx": "10cc7992ede28c7b4dda5c35cbd71174918e83adab0342cc3d556a413b4ce93b", "wy": "3f9c3b38aef0a0e687d7ee6afde70d47d6900ff0ce62156e8645b8103fc66cad"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000410cc7992ede28c7b4dda5c35cbd71174918e83adab0342cc3d556a413b4ce93b3f9c3b38aef0a0e687d7ee6afde70d47d6900ff0ce62156e8645b8103fc66cad", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBDMeZLt4ox7TdpcNcvXEXSRjoOt\nqwNCzD1VakE7TOk7P5w7OK7woOaH1+5q/ecNR9aQD/DOYhVuhkW4ED/GbK0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 292, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 293, "comment": "r is larger than n", "msg": "313233343030", "sig": "3026022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a8020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042ba28f6236c5a774cd104b036d2e016711cb4a83fa078b5150f69e5098de7b4ca7c13ef8c57fcbe684ceff312ef53af1b14397d4154ba6106a3383aaed16ecb1", "wx": "2ba28f6236c5a774cd104b036d2e016711cb4a83fa078b5150f69e5098de7b4c", "wy": "00a7c13ef8c57fcbe684ceff312ef53af1b14397d4154ba6106a3383aaed16ecb1"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200042ba28f6236c5a774cd104b036d2e016711cb4a83fa078b5150f69e5098de7b4ca7c13ef8c57fcbe684ceff312ef53af1b14397d4154ba6106a3383aaed16ecb1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCuij2I2xad0zRBLA20uAWcRy0qD\n+geLUVD2nlCY3ntMp8E++MV/y+aEzv8xLvU68bFDl9QVS6YQajODqu0W7LE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 294, "comment": "s is larger than n", "msg": "313233343030", "sig": "3026020101022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82975b2d2e", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04113489555bdc112352b08b7ffebcf05090f94da62367646b2e03a3478863914b4b4a0a435462a122f6d9ac801319bbc6d2c59228861a3414b500e5cf5943c964", "wx": "113489555bdc112352b08b7ffebcf05090f94da62367646b2e03a3478863914b", "wy": "4b4a0a435462a122f6d9ac801319bbc6d2c59228861a3414b500e5cf5943c964"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004113489555bdc112352b08b7ffebcf05090f94da62367646b2e03a3478863914b4b4a0a435462a122f6d9ac801319bbc6d2c59228861a3414b500e5cf5943c964", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBE0iVVb3BEjUrCLf/688FCQ+U2m\nI2dkay4Do0eIY5FLS0oKQ1RioSL22ayAExm7xtLFkiiGGjQUtQDlz1lDyWQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "small r and s^-1", "msg": "313233343030", "sig": "30260202010102202827370584fdeb9f5d5a9fb9579a09390efb6f9d99b64fc188d8bce05c2d4eed", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0434224746efa8c5d4f4c6b82de4d76d3e7150c1b69e23339f098ff769bcac94bf94618e3624a57d48d19e72867dbc191a0fd05cf6f4b5ec497b797626a57baa22", "wx": "34224746efa8c5d4f4c6b82de4d76d3e7150c1b69e23339f098ff769bcac94bf", "wy": "0094618e3624a57d48d19e72867dbc191a0fd05cf6f4b5ec497b797626a57baa22"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000434224746efa8c5d4f4c6b82de4d76d3e7150c1b69e23339f098ff769bcac94bf94618e3624a57d48d19e72867dbc191a0fd05cf6f4b5ec497b797626a57baa22", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDQiR0bvqMXU9Ma4LeTXbT5xUMG2\nniMznwmP92m8rJS/lGGONiSlfUjRnnKGfbwZGg/QXPb0texJe3l2JqV7qiI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302b02072d9b4d347952ce02204937a087731df4febc2c3a81ddfbab5dc3af950817f41b590d156ed409ad2869", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046fb0cdf3b08dc5d8b7e5259c7d1bbd31a2235345b7b445631e894b567d23c07953243207df5c446011c1cfedde6e5351958affa8f274fe5af435759de87db343", "wx": "6fb0cdf3b08dc5d8b7e5259c7d1bbd31a2235345b7b445631e894b567d23c079", "wy": "53243207df5c446011c1cfedde6e5351958affa8f274fe5af435759de87db343"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046fb0cdf3b08dc5d8b7e5259c7d1bbd31a2235345b7b445631e894b567d23c07953243207df5c446011c1cfedde6e5351958affa8f274fe5af435759de87db343", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG+wzfOwjcXYt+UlnH0bvTGiI1NF\nt7RFYx6JS1Z9I8B5UyQyB99cRGARwc/t3m5TUZWK/6jydP5a9DV1neh9s0M=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3032020d1033e67e37b32b445580bf4efb02210091827d03bb6dac31940ba56ed88489048ff173f0bf20cab20dcc086fca37f285", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040b8d3bef12ebab43f2f6f6618f0843d5f45d97874f26f9a36b788cb7a69ecf5f855588c99b3839ca9361ddc77645f7592ad371438ee3e186c74081c481dd5295", "wx": "0b8d3bef12ebab43f2f6f6618f0843d5f45d97874f26f9a36b788cb7a69ecf5f", "wy": "00855588c99b3839ca9361ddc77645f7592ad371438ee3e186c74081c481dd5295"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200040b8d3bef12ebab43f2f6f6618f0843d5f45d97874f26f9a36b788cb7a69ecf5f855588c99b3839ca9361ddc77645f7592ad371438ee3e186c74081c481dd5295", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABAuNO+8S66tD8vb2YY8IQ9X0XZeH\nTyb5o2t4jLemns9fhVWIyZs4OcqTYd3HdkX3WSrTcUOO4+GGx0CBxIHdUpU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "30260202010102203eb35fe7e8331f71e4c63b45f349a99d47a5e781798e579f2386195d3827bb15", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d24985342a45a55fd99e47521fe3e991b8a1d376fa73899d3bacc067c12ee0d6542f148599fccb99b1ba28d3805814292a99bffe371df277b09e8ada1253dcd", "wx": "6d24985342a45a55fd99e47521fe3e991b8a1d376fa73899d3bacc067c12ee0d", "wy": "6542f148599fccb99b1ba28d3805814292a99bffe371df277b09e8ada1253dcd"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046d24985342a45a55fd99e47521fe3e991b8a1d376fa73899d3bacc067c12ee0d6542f148599fccb99b1ba28d3805814292a99bffe371df277b09e8ada1253dcd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG0kmFNCpFpV/ZnkdSH+Ppkbih03\nb6c4mdO6zAZ8Eu4NZULxSFmfzLmbG6KNOAWBQpKpm//jcd8newnoraElPc0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3031020d062522bbd3ecbe7c39e93e7c2502203eb35fe7e8331f71e4c63b45f349a99d47a5e781798e579f2386195d3827bb15", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044125e46820f41206b670882a9d8d51b6bac39091150c9cb33b6d009e0cff522365749240622b40d70a63407952c1b8761c9f8e85aba6f03bbc7219e24e6fb276", "wx": "4125e46820f41206b670882a9d8d51b6bac39091150c9cb33b6d009e0cff5223", "wy": "65749240622b40d70a63407952c1b8761c9f8e85aba6f03bbc7219e24e6fb276"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200044125e46820f41206b670882a9d8d51b6bac39091150c9cb33b6d009e0cff522365749240622b40d70a63407952c1b8761c9f8e85aba6f03bbc7219e24e6fb276", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABEEl5Ggg9BIGtnCIKp2NUba6w5CR\nFQycszttAJ4M/1IjZXSSQGIrQNcKY0B5UsG4dhyfjoWrpvA7vHIZ4k5vsnY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3045022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e8297485628022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0491ba1706a19ce58faca26366dced293399450efa488f2c4baa95693b974d075d5e8401565a37b05b9351e408af542bf0f7957e5eed182afeabeafa2bf7bbbb47", "wx": "0091ba1706a19ce58faca26366dced293399450efa488f2c4baa95693b974d075d", "wy": "5e8401565a37b05b9351e408af542bf0f7957e5eed182afeabeafa2bf7bbbb47"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000491ba1706a19ce58faca26366dced293399450efa488f2c4baa95693b974d075d5e8401565a37b05b9351e408af542bf0f7957e5eed182afeabeafa2bf7bbbb47", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABJG6FwahnOWPrKJjZtztKTOZRQ76\nSI8sS6qVaTuXTQddXoQBVlo3sFuTUeQIr1Qr8PeVfl7tGCr+q+r6K/e7u0c=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "s == 1", "msg": "313233343030", "sig": "3025022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2020101", "result": "valid", "flags": []}, {"tcId": 302, "comment": "s == 0", "msg": "313233343030", "sig": "3025022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0425d17570e4bae1e468e6dd0975b382368061e0c704241c1d18fd5baa8ca8dc135acadcd13992f6665b469c9f9ab7797e3c4b881c6d7f4d2601c96a1536f76d05", "wx": "25d17570e4bae1e468e6dd0975b382368061e0c704241c1d18fd5baa8ca8dc13", "wy": "5acadcd13992f6665b469c9f9ab7797e3c4b881c6d7f4d2601c96a1536f76d05"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000425d17570e4bae1e468e6dd0975b382368061e0c704241c1d18fd5baa8ca8dc135acadcd13992f6665b469c9f9ab7797e3c4b881c6d7f4d2601c96a1536f76d05", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCXRdXDkuuHkaObdCXWzgjaAYeDH\nBCQcHRj9W6qMqNwTWsrc0TmS9mZbRpyfmrd5fjxLiBxtf00mAclqFTb3bQU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "3044022054fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b53022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041dc3325ffa55e179e2171a66b2e7534ae12cfc292af2e0fbf1c3fcce5558fc6a2420abcdb7df8cf38634648264a681d5ed195bf16a970ffa68ab250b34a93514", "wx": "1dc3325ffa55e179e2171a66b2e7534ae12cfc292af2e0fbf1c3fcce5558fc6a", "wy": "2420abcdb7df8cf38634648264a681d5ed195bf16a970ffa68ab250b34a93514"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200041dc3325ffa55e179e2171a66b2e7534ae12cfc292af2e0fbf1c3fcce5558fc6a2420abcdb7df8cf38634648264a681d5ed195bf16a970ffa68ab250b34a93514", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABB3DMl/6VeF54hcaZrLnU0rhLPwp\nKvLg+/HD/M5VWPxqJCCrzbffjPOGNGSCZKaB1e0ZW/Fqlw/6aKslCzSpNRQ=\n-----E<PERSON> PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "3044022054fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b53022054fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b53", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043fffa6dbaf667b0a94e5f86b3774b975971a4d2439607def4e5de1d17820a3b21bf36613b50b925264551815c5da783bd158aaa1c6244b40a9fa31a2a433f8e8", "wx": "3fffa6dbaf667b0a94e5f86b3774b975971a4d2439607def4e5de1d17820a3b2", "wy": "1bf36613b50b925264551815c5da783bd158aaa1c6244b40a9fa31a2a433f8e8"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200043fffa6dbaf667b0a94e5f86b3774b975971a4d2439607def4e5de1d17820a3b21bf36613b50b925264551815c5da783bd158aaa1c6244b40a9fa31a2a433f8e8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABD//ptuvZnsKlOX4azd0uXWXGk0k\nOWB9705d4dF4IKOyG/NmE7ULklJkVRgVxdp4O9FYqqHGJEtAqfoxoqQz+Og=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "3044022054fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b53022054fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b54", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048afd47eb0667860bec98d5dcd2f60da9eac1ae99620569892f14e094d635872a5e8f0bc67b98a233ade715c04d9daab11a27517a92cf2651c9e5f2fde4e2db98", "wx": "008afd47eb0667860bec98d5dcd2f60da9eac1ae99620569892f14e094d635872a", "wy": "5e8f0bc67b98a233ade715c04d9daab11a27517a92cf2651c9e5f2fde4e2db98"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048afd47eb0667860bec98d5dcd2f60da9eac1ae99620569892f14e094d635872a5e8f0bc67b98a233ade715c04d9daab11a27517a92cf2651c9e5f2fde4e2db98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIr9R+sGZ4YL7JjV3NL2Danqwa6Z\nYgVpiS8U4JTWNYcqXo8LxnuYojOt5xXATZ2qsRonUXqSzyZRyeXy/eTi25g=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "u1 == 1", "msg": "313233343030", "sig": "3045022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2022100bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040876616636a8dbc82160ac01af2941353ba0eea4a3b8fe31696b47317d4972c923180073061d27984ecf491f394004c3a4846d773f58dc2ab5e43dcbf968d027", "wx": "0876616636a8dbc82160ac01af2941353ba0eea4a3b8fe31696b47317d4972c9", "wy": "23180073061d27984ecf491f394004c3a4846d773f58dc2ab5e43dcbf968d027"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200040876616636a8dbc82160ac01af2941353ba0eea4a3b8fe31696b47317d4972c923180073061d27984ecf491f394004c3a4846d773f58dc2ab5e43dcbf968d027", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABAh2YWY2qNvIIWCsAa8pQTU7oO6k\no7j+MWlrRzF9SXLJIxgAcwYdJ5hOz0kfOUAEw6SEbXc/WNwqteQ9y/lo0Cc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "3045022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2022100989c5cc31440c1168f88b32ba6e47900183c0d843f9c41671898030664305d2b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040fabb052217eae8e63fea4eea09953d51862427f341307d819ff6e933bf72ba94b897f2c4a4cf57054c363c720da3d242471cc8e493becb0de022251d2ee4c8c", "wx": "0fabb052217eae8e63fea4eea09953d51862427f341307d819ff6e933bf72ba9", "wy": "4b897f2c4a4cf57054c363c720da3d242471cc8e493becb0de022251d2ee4c8c"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200040fabb052217eae8e63fea4eea09953d51862427f341307d819ff6e933bf72ba94b897f2c4a4cf57054c363c720da3d242471cc8e493becb0de022251d2ee4c8c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABA+rsFIhfq6OY/6k7qCZU9UYYkJ/\nNBMH2Bn/bpM79yupS4l/LEpM9XBUw2PHINo9JCRxzI5JO+yw3gIiUdLuTIw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "u2 == 1", "msg": "313233343030", "sig": "3044022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0472ebad749b504c874d21bc5e4bba545dd42eb5fbf78af42043f5cef10aeb3ad745227464e1e9cef662f43fc80d4ce7eb7eb615a23699d48e89b278abd46ccc46", "wx": "72ebad749b504c874d21bc5e4bba545dd42eb5fbf78af42043f5cef10aeb3ad7", "wy": "45227464e1e9cef662f43fc80d4ce7eb7eb615a23699d48e89b278abd46ccc46"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000472ebad749b504c874d21bc5e4bba545dd42eb5fbf78af42043f5cef10aeb3ad745227464e1e9cef662f43fc80d4ce7eb7eb615a23699d48e89b278abd46ccc46", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABHLrrXSbUEyHTSG8Xku6VF3ULrX7\n94r0IEP1zvEK6zrXRSJ0ZOHpzvZi9D/IDUzn6362FaI2mdSOibJ4q9RszEY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "3044022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2022071523a926bf4712829995c6069025e4bb2d0fc6d23966f4fb5695f01ba3039c5", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04744e218a04b31471b05e679c9481446bcd72a4d0fca7a7af1a1fe2f574d9362f60c0c52843d8d72cd636153f0f510a09089fc4478372dfc50e5b91d5301ba75e", "wx": "744e218a04b31471b05e679c9481446bcd72a4d0fca7a7af1a1fe2f574d9362f", "wy": "60c0c52843d8d72cd636153f0f510a09089fc4478372dfc50e5b91d5301ba75e"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004744e218a04b31471b05e679c9481446bcd72a4d0fca7a7af1a1fe2f574d9362f60c0c52843d8d72cd636153f0f510a09089fc4478372dfc50e5b91d5301ba75e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABHROIYoEsxRxsF5nnJSBRGvNcqTQ\n/Kenrxof4vV02TYvYMDFKEPY1yzWNhU/D1EKCQifxEeDct/FDluR1TAbp14=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022005ca53b2d9e4a2e1e4f47276fcdfb17b26a9cf0a7c9721dad28203d41107fdd4", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047ea53d3c4635a4d5b60d79aac79d974c759263363472146a4605280d935ffc7559790403c96459b20477eaa437b3c7decd5e690faa940c0891de0cd07d41813c", "wx": "7ea53d3c4635a4d5b60d79aac79d974c759263363472146a4605280d935ffc75", "wy": "59790403c96459b20477eaa437b3c7decd5e690faa940c0891de0cd07d41813c"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200047ea53d3c4635a4d5b60d79aac79d974c759263363472146a4605280d935ffc7559790403c96459b20477eaa437b3c7decd5e690faa940c0891de0cd07d41813c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABH6lPTxGNaTVtg15qsedl0x1kmM2\nNHIUakYFKA2TX/x1WXkEA8lkWbIEd+qkN7PH3s1eaQ+qlAwIkd4M0H1BgTw=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205448aa82fc57740b2e1ebdf989baa145b018b423b3761feb055959eb6a01f1a1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04750462a163655746af66ba3eb48009a490d970799280586cfe59316365dc4ef0a2f1567257bd9aa1dcca3cd276ffaeb1dd85cea28d888a98642bf09a98f69f11", "wx": "750462a163655746af66ba3eb48009a490d970799280586cfe59316365dc4ef0", "wy": "00a2f1567257bd9aa1dcca3cd276ffaeb1dd85cea28d888a98642bf09a98f69f11"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004750462a163655746af66ba3eb48009a490d970799280586cfe59316365dc4ef0a2f1567257bd9aa1dcca3cd276ffaeb1dd85cea28d888a98642bf09a98f69f11", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABHUEYqFjZVdGr2a6PrSACaSQ2XB5\nkoBYbP5ZMWNl3E7wovFWcle9mqHcyjzSdv+usd2FzqKNiIqYZCvwmpj2nxE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022065bfcce69b89eff545fb0a67d2581a5f253484ef538b9b55fa862dfd2d488d52", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04323ae5754b417552cf968f5f3eea7187f7b1726e8c2e510f98d26430ac5849bc327101d82adf87c932e8eaa6a57e1d11bd65dc8f404c113f65abaa6eeaf5c7c4", "wx": "323ae5754b417552cf968f5f3eea7187f7b1726e8c2e510f98d26430ac5849bc", "wy": "327101d82adf87c932e8eaa6a57e1d11bd65dc8f404c113f65abaa6eeaf5c7c4"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004323ae5754b417552cf968f5f3eea7187f7b1726e8c2e510f98d26430ac5849bc327101d82adf87c932e8eaa6a57e1d11bd65dc8f404c113f65abaa6eeaf5c7c4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDI65XVLQXVSz5aPXz7qcYf3sXJu\njC5RD5jSZDCsWEm8MnEB2Crfh8ky6OqmpX4dEb1l3I9ATBE/Zauqbur1x8Q=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207a459e047395d81d3b00f4b8d5ad34442b35dec5e6c1b45a0678e65a1fe9e9e6", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0437a105e3ce3fb636733032d1ca56b4c659b451f64f4ba7378b087987e7a544d2782bad9b1654f2770d7a3ee35b672a366f685bc7191889ff2fa5c6b94ebe7ab8", "wx": "37a105e3ce3fb636733032d1ca56b4c659b451f64f4ba7378b087987e7a544d2", "wy": "782bad9b1654f2770d7a3ee35b672a366f685bc7191889ff2fa5c6b94ebe7ab8"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000437a105e3ce3fb636733032d1ca56b4c659b451f64f4ba7378b087987e7a544d2782bad9b1654f2770d7a3ee35b672a366f685bc7191889ff2fa5c6b94ebe7ab8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDehBePOP7Y2czAy0cpWtMZZtFH2\nT0unN4sIeYfnpUTSeCutmxZU8ncNej7jW2cqNm9oW8cZGIn/L6XGuU6+erg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02203b7739bbe1048b69fd05f9262f628e03b0770e7ecd82337f1482a72db0293232", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0413dd59454f6af3e9db115b7ec8c3a1c8d308fdcb4963c3b8ea1264e4afda652c5d260b7fc9bfd200896d229f3c8daab9df2f55aa9ad95d4ea76aed8d74c5494d", "wx": "13dd59454f6af3e9db115b7ec8c3a1c8d308fdcb4963c3b8ea1264e4afda652c", "wy": "5d260b7fc9bfd200896d229f3c8daab9df2f55aa9ad95d4ea76aed8d74c5494d"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000413dd59454f6af3e9db115b7ec8c3a1c8d308fdcb4963c3b8ea1264e4afda652c5d260b7fc9bfd200896d229f3c8daab9df2f55aa9ad95d4ea76aed8d74c5494d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBPdWUVPavPp2xFbfsjDocjTCP3L\nSWPDuOoSZOSv2mUsXSYLf8m/0gCJbSKfPI2qud8vVaqa2V1Op2rtjXTFSU0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022076ee7377c20916d3fa0bf24c5ec51c0760ee1cfd9b0466fe29054e5b60526464", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042c1dc56459bf09df50fb2d962f5989f3643021c5c360363e10e695a70b5942e86216d3ca0cca31dbd92a4d28bf951437f6f45db41e8e41fdf72414a293f53087", "wx": "2c1dc56459bf09df50fb2d962f5989f3643021c5c360363e10e695a70b5942e8", "wy": "6216d3ca0cca31dbd92a4d28bf951437f6f45db41e8e41fdf72414a293f53087"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200042c1dc56459bf09df50fb2d962f5989f3643021c5c360363e10e695a70b5942e86216d3ca0cca31dbd92a4d28bf951437f6f45db41e8e41fdf72414a293f53087", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCwdxWRZvwnfUPstli9ZifNkMCHF\nw2A2PhDmlacLWULoYhbTygzKMdvZKk0ov5UUN/b0XbQejkH99yQUopP1MIc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02204a992824c737b00f02d23d2f2e3decf090b28ffa0e90e6d1e5dd157070719f65", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04137d6fdf836b1824378c08b35fa7ebe4e807d8a20105ce9cb3cd281f0a47c9c307d6475d4958c16d950f0439d3dbf86c2d7e2b12e8b137efc62dd1c723b83a62", "wx": "137d6fdf836b1824378c08b35fa7ebe4e807d8a20105ce9cb3cd281f0a47c9c3", "wy": "07d6475d4958c16d950f0439d3dbf86c2d7e2b12e8b137efc62dd1c723b83a62"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004137d6fdf836b1824378c08b35fa7ebe4e807d8a20105ce9cb3cd281f0a47c9c307d6475d4958c16d950f0439d3dbf86c2d7e2b12e8b137efc62dd1c723b83a62", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBN9b9+DaxgkN4wIs1+n6+ToB9ii\nAQXOnLPNKB8KR8nDB9ZHXUlYwW2VDwQ509v4bC1+KxLosTfvxi3RxyO4OmI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221009c092d165ef1b11a82b59c73aab3496631e3032038feda236db7b0f5a8e0cabb", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04640213be1698b166f0c54e588e1b57a64826bf848adabfef60681d77747d2ca8646e45d961419d4ad1338c361228e1c6b6615398582c0e3e97f7ebc85a504423", "wx": "640213be1698b166f0c54e588e1b57a64826bf848adabfef60681d77747d2ca8", "wy": "646e45d961419d4ad1338c361228e1c6b6615398582c0e3e97f7ebc85a504423"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004640213be1698b166f0c54e588e1b57a64826bf848adabfef60681d77747d2ca8646e45d961419d4ad1338c361228e1c6b6615398582c0e3e97f7ebc85a504423", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGQCE74WmLFm8MVOWI4bV6ZIJr+E\nitq/72BoHXd0fSyoZG5F2WFBnUrRM4w2EijhxrZhU5hYLA4+l/fryFpQRCM=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100a90449e87d9de3ebed92a227735e45325b1d2d774b4876a86d0863349471ac59", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04269154ca58317552c655d2a9b3804dd94c2711145b9cd93c360f2dfe34cc197198046cc90cc6a8ac48ef7bacc5cb7e57334fa91facbadb48952c9fee543d1bb5", "wx": "269154ca58317552c655d2a9b3804dd94c2711145b9cd93c360f2dfe34cc1971", "wy": "0098046cc90cc6a8ac48ef7bacc5cb7e57334fa91facbadb48952c9fee543d1bb5"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004269154ca58317552c655d2a9b3804dd94c2711145b9cd93c360f2dfe34cc197198046cc90cc6a8ac48ef7bacc5cb7e57334fa91facbadb48952c9fee543d1bb5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCaRVMpYMXVSxlXSqbOATdlMJxEU\nW5zZPDYPLf40zBlxmARsyQzGqKxI73usxct+VzNPqR+suttIlSyf7lQ9G7U=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100a4310428c80a06da59719819a0a3dbf6658fab9938ca851cbd9c0aae864058d3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04240e0b64cee2e0b8890c2fa82de5848a5642ef0f7b2414f88f585281df7a1ff53a5990f860da3053f821bea914059ced85c9c2390b0d860532dbccca7ff66692", "wx": "240e0b64cee2e0b8890c2fa82de5848a5642ef0f7b2414f88f585281df7a1ff5", "wy": "3a5990f860da3053f821bea914059ced85c9c2390b0d860532dbccca7ff66692"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004240e0b64cee2e0b8890c2fa82de5848a5642ef0f7b2414f88f585281df7a1ff53a5990f860da3053f821bea914059ced85c9c2390b0d860532dbccca7ff66692", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCQOC2TO4uC4iQwvqC3lhIpWQu8P\neyQU+I9YUoHfeh/1OlmQ+GDaMFP4Ib6pFAWc7YXJwjkLDYYFMtvMyn/2ZpI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100a14bda4f5b17b56966f75ede22340338d23ac413fa7ef42f545b08c47dbc59e9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0496f3cb5eb0c33be205ec058a22093d739fe80a7ecc874399c14f7f6c38cfcc5147b3eccaecc9add2b1dffc988f13dcab15b7e910d0250e70a1d79b3b931c32ed", "wx": "0096f3cb5eb0c33be205ec058a22093d739fe80a7ecc874399c14f7f6c38cfcc51", "wy": "47b3eccaecc9add2b1dffc988f13dcab15b7e910d0250e70a1d79b3b931c32ed"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000496f3cb5eb0c33be205ec058a22093d739fe80a7ecc874399c14f7f6c38cfcc5147b3eccaecc9add2b1dffc988f13dcab15b7e910d0250e70a1d79b3b931c32ed", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABJbzy16wwzviBewFiiIJPXOf6Ap+\nzIdDmcFPf2w4z8xRR7PsyuzJrdKx3/yYjxPcqxW36RDQJQ5wodebO5McMu0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022032dfe6734dc4f7faa2fd8533e92c0d2f929a4277a9c5cdaafd4316fe96a446a9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04103b1bf6343d57260f652d272aaeff6cfa439f1583335eba66fa72d00eff7f8520f2bb035bd056c67ca22ca952abb5e1bcb68d67ca81790d24097f13d45209a1", "wx": "103b1bf6343d57260f652d272aaeff6cfa439f1583335eba66fa72d00eff7f85", "wy": "20f2bb035bd056c67ca22ca952abb5e1bcb68d67ca81790d24097f13d45209a1"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004103b1bf6343d57260f652d272aaeff6cfa439f1583335eba66fa72d00eff7f8520f2bb035bd056c67ca22ca952abb5e1bcb68d67ca81790d24097f13d45209a1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABBA7G/Y0PVcmD2UtJyqu/2z6Q58V\ngzNeumb6ctAO/3+FIPK7A1vQVsZ8oiypUqu14by2jWfKgXkNJAl/E9RSCaE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02206353c7f3e0a4e33ebf7758dadf2bd9d0841328e13c75e252855f5a2b87c2c78c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04959b3bf372301993b37e20b4344f13c06d5c1c53c7737f166efb94832c3b9bbb40d35ef46e4cfad475ddd1a1d9609feca7069712d30bdf4638d4c88bc9a12100", "wx": "00959b3bf372301993b37e20b4344f13c06d5c1c53c7737f166efb94832c3b9bbb", "wy": "40d35ef46e4cfad475ddd1a1d9609feca7069712d30bdf4638d4c88bc9a12100"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004959b3bf372301993b37e20b4344f13c06d5c1c53c7737f166efb94832c3b9bbb40d35ef46e4cfad475ddd1a1d9609feca7069712d30bdf4638d4c88bc9a12100", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABJWbO/NyMBmTs34gtDRPE8BtXBxT\nx3N/Fm77lIMsO5u7QNNe9G5M+tR13dGh2WCf7KcGlxLTC99GONTIi8mhIQA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022014899bc6ff5e72338f3c9847fa6531c4337fde3fcf1c8c32f768fba3a402a964", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e69b17d83894e2e71ffce351b53459c0bb29bec379ff435f23c01a9b37df49e3ba1053ad84236d82cf7c762362b37b24e3b0ee1f8ea6c543a2591dcb6681a8f", "wx": "6e69b17d83894e2e71ffce351b53459c0bb29bec379ff435f23c01a9b37df49e", "wy": "3ba1053ad84236d82cf7c762362b37b24e3b0ee1f8ea6c543a2591dcb6681a8f"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046e69b17d83894e2e71ffce351b53459c0bb29bec379ff435f23c01a9b37df49e3ba1053ad84236d82cf7c762362b37b24e3b0ee1f8ea6c543a2591dcb6681a8f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG5psX2DiU4ucf/ONRtTRZwLspvs\nN5/0NfI8AamzffSeO6EFOthCNtgs98diNis3sk47DuH46mxUOiWR3LZoGo8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022072655c5e4f1cefca22f413a612e5bfdd7ba9ae71053f68b0c74d9a73590013c3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04336fc28e1f250485276747dfc34859b4741667b3ac46a0f6384decc1ac790304401206b5508aa06601a2246e7381dfecca6adb2b197ae14549a24c355cd53be1", "wx": "336fc28e1f250485276747dfc34859b4741667b3ac46a0f6384decc1ac790304", "wy": "401206b5508aa06601a2246e7381dfecca6adb2b197ae14549a24c355cd53be1"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004336fc28e1f250485276747dfc34859b4741667b3ac46a0f6384decc1ac790304401206b5508aa06601a2246e7381dfecca6adb2b197ae14549a24c355cd53be1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDNvwo4fJQSFJ2dH38NIWbR0Fmez\nrEag9jhN7MGseQMEQBIGtVCKoGYBoiRuc4Hf7Mpq2ysZeuFFSaJMNVzVO+E=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100a2030dbf01d8c9de2900dc3845fda4e4c6bc049c4cd5717a9c629b9ed29d1859", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04913d9ce35b9c73203578e255d4dd35ff20212d357227d26b8a959180665b542ba503d922d3fd65a07eca18c0a4e2d3f2cf7c05928b406458cb286e11dc62dcb6", "wx": "00913d9ce35b9c73203578e255d4dd35ff20212d357227d26b8a959180665b542b", "wy": "00a503d922d3fd65a07eca18c0a4e2d3f2cf7c05928b406458cb286e11dc62dcb6"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004913d9ce35b9c73203578e255d4dd35ff20212d357227d26b8a959180665b542ba503d922d3fd65a07eca18c0a4e2d3f2cf7c05928b406458cb286e11dc62dcb6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABJE9nONbnHMgNXjiVdTdNf8gIS01\ncifSa4qVkYBmW1QrpQPZItP9ZaB+yhjApOLT8s98BZKLQGRYyyhuEdxi3LY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100a1db7293b6f01594b808718e61a4d642dff4fee2fb471167ef7ab42959a473e7", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046b76915cc1c854744a78dac9baecd59845b90ad9cd308f5a887dccc909dacd4a7260456f8f8d31760d81bf85348d9f50c99d9918b480b1ec25f4e2e34de03769", "wx": "6b76915cc1c854744a78dac9baecd59845b90ad9cd308f5a887dccc909dacd4a", "wy": "7260456f8f8d31760d81bf85348d9f50c99d9918b480b1ec25f4e2e34de03769"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046b76915cc1c854744a78dac9baecd59845b90ad9cd308f5a887dccc909dacd4a7260456f8f8d31760d81bf85348d9f50c99d9918b480b1ec25f4e2e34de03769", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGt2kVzByFR0Snjaybrs1ZhFuQrZ\nzTCPWoh9zMkJ2s1KcmBFb4+NMXYNgb+FNI2fUMmdmRi0gLHsJfTi403gN2k=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02210099bb8d4bcbf1816d31aad88c25c61f1433b08322412c7bd84ed759d01c009127", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04647b37b731d3ead759762751995483469031084cd709887c9b6bafba462cbf84888c5b171f2b2fb7bb2b9d88200d79ac94d7d4025f79348e2283511c047891bf", "wx": "647b37b731d3ead759762751995483469031084cd709887c9b6bafba462cbf84", "wy": "00888c5b171f2b2fb7bb2b9d88200d79ac94d7d4025f79348e2283511c047891bf"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004647b37b731d3ead759762751995483469031084cd709887c9b6bafba462cbf84888c5b171f2b2fb7bb2b9d88200d79ac94d7d4025f79348e2283511c047891bf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGR7N7cx0+rXWXYnUZlUg0aQMQhM\n1wmIfJtrr7pGLL+EiIxbFx8rL7e7K52IIA15rJTX1AJfeTSOIoNRHAR4kb8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022041cee82a6957ef02ab3aa07a3315accc0d0d66c2081d530246d6e681873c90d1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041f761a1ae1e82e4af277b399da0a523e85644ce971c7b90236d03115aed9855b55cdb3e104361fd2e0979863f29a3b0bf5542c5105c91dfc7c94643b78a2b7f2", "wx": "1f761a1ae1e82e4af277b399da0a523e85644ce971c7b90236d03115aed9855b", "wy": "55cdb3e104361fd2e0979863f29a3b0bf5542c5105c91dfc7c94643b78a2b7f2"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200041f761a1ae1e82e4af277b399da0a523e85644ce971c7b90236d03115aed9855b55cdb3e104361fd2e0979863f29a3b0bf5542c5105c91dfc7c94643b78a2b7f2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABB92Ghrh6C5K8nezmdoKUj6FZEzp\ncce5AjbQMRWu2YVbVc2z4QQ2H9Lgl5hj8po7C/VULFEFyR38fJRkO3iit/I=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220746d61572ecae774691e7809121986d9b93279b00934ff1def1f4798da89ad4c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0463d303162574962899fd9a323c5fe24a09188fa20d47a8d92ba502d4f886f5b372cd0d82b3fd4f54fedc5d8618b142f63553e438cc1269719dee3abd3316fa21", "wx": "63d303162574962899fd9a323c5fe24a09188fa20d47a8d92ba502d4f886f5b3", "wy": "72cd0d82b3fd4f54fedc5d8618b142f63553e438cc1269719dee3abd3316fa21"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000463d303162574962899fd9a323c5fe24a09188fa20d47a8d92ba502d4f886f5b372cd0d82b3fd4f54fedc5d8618b142f63553e438cc1269719dee3abd3316fa21", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGPTAxYldJYomf2aMjxf4koJGI+i\nDUeo2SulAtT4hvWzcs0NgrP9T1T+3F2GGLFC9jVT5DjMEmlxne46vTMW+iE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220281b26a0908968099f8e1f610f4f358318baa21107b791ef6f24cb244677a64b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043e1d966e05f04c44e162133d97730f6408a88ad990a2c6efb7e3e73a886f7ed4a40e3b3fd8b005fc417437f21011d9fbe38b329a2e7959ed9b040c8e1eb677fd", "wx": "3e1d966e05f04c44e162133d97730f6408a88ad990a2c6efb7e3e73a886f7ed4", "wy": "00a40e3b3fd8b005fc417437f21011d9fbe38b329a2e7959ed9b040c8e1eb677fd"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200043e1d966e05f04c44e162133d97730f6408a88ad990a2c6efb7e3e73a886f7ed4a40e3b3fd8b005fc417437f21011d9fbe38b329a2e7959ed9b040c8e1eb677fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABD4dlm4F8ExE4WITPZdzD2QIqIrZ\nkKLG77fj5zqIb37UpA47P9iwBfxBdDfyEBHZ++OLMpoueVntmwQMjh62d/0=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022046a78fe7c149c67d7eeeb1b5be57b3a1082651c278ebc4a50abeb4570f858f1b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0456ac8e49b319d5d041ae3d3f91de229c0a820d7ffd97ea06196eee7507363f42787fc05eba606f77b984e57cabf911209700b5d39147a14c5d1a95f56cd5feb4", "wx": "56ac8e49b319d5d041ae3d3f91de229c0a820d7ffd97ea06196eee7507363f42", "wy": "787fc05eba606f77b984e57cabf911209700b5d39147a14c5d1a95f56cd5feb4"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000456ac8e49b319d5d041ae3d3f91de229c0a820d7ffd97ea06196eee7507363f42787fc05eba606f77b984e57cabf911209700b5d39147a14c5d1a95f56cd5feb4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABFasjkmzGdXQQa49P5HeIpwKgg1/\n/ZfqBhlu7nUHNj9CeH/AXrpgb3e5hOV8q/kRIJcAtdORR6FMXRqV9WzV/rQ=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022014fdabedd0f754de1f3305484ec1c6b8c61cbd51dab0d37bc80f07414ba42b55", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045e2f228631ee7f00ceaf936278f2e2681b429fcfb8cb2c019b31f188839884f530e1079a6b889393cc83fabbd524f21bb486c65b83ab0afafb17265d971bae91", "wx": "5e2f228631ee7f00ceaf936278f2e2681b429fcfb8cb2c019b31f188839884f5", "wy": "30e1079a6b889393cc83fabbd524f21bb486c65b83ab0afafb17265d971bae91"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200045e2f228631ee7f00ceaf936278f2e2681b429fcfb8cb2c019b31f188839884f530e1079a6b889393cc83fabbd524f21bb486c65b83ab0afafb17265d971bae91", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABF4vIoYx7n8Azq+TYnjy4mgbQp/P\nuMssAZsx8YiDmIT1MOEHmmuIk5PMg/q71STyG7SGxluDqwr6+xcmXZcbrpE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221008e305a1cf885ccc330ad0f1b5834a6a783f1948a5d5087d42bb5d47af8243535", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc51288fb2ca09ee336ef316b73919a7f3b329fca2f5c365cc427425fecf64f7bf3", "wx": "6dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc5", "wy": "1288fb2ca09ee336ef316b73919a7f3b329fca2f5c365cc427425fecf64f7bf3"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc51288fb2ca09ee336ef316b73919a7f3b329fca2f5c365cc427425fecf64f7bf3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG28VgW04ROTL+3ntHQ/Tfxi/eyu\nFnNbUWU9ee4Ajy/FEoj7LKCe4zbvMWtzkZp/OzKfyi9cNlzEJ0Jf7PZPe/M=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "point duplication during verification", "msg": "313233343030", "sig": "30440220074c035603e1eb49ab5382819bf82af82929b500c6e78841c1b2c3ff54a615dd02202035ac9ea7119e30e54f369cd22aa27af38b566ae6093f1df35b612de6f07598", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc597725caf014fc6854f349f1d0be90e373b9c2bf478efc363f8d0e830291ed784", "wx": "6dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc5", "wy": "0097725caf014fc6854f349f1d0be90e373b9c2bf478efc363f8d0e830291ed784"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046dbc5605b4e113932fede7b4743f4dfc62fdecae16735b51653d79ee008f2fc597725caf014fc6854f349f1d0be90e373b9c2bf478efc363f8d0e830291ed784", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG28VgW04ROTL+3ntHQ/Tfxi/eyu\nFnNbUWU9ee4Ajy/Fl3JcrwFPxoVPNJ8dC+kONzucK/R478Nj+NDoMCke14Q=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "duplication bug", "msg": "313233343030", "sig": "30440220074c035603e1eb49ab5382819bf82af82929b500c6e78841c1b2c3ff54a615dd02202035ac9ea7119e30e54f369cd22aa27af38b566ae6093f1df35b612de6f07598", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048c5635eeaf7e994ff163ebdc9aacfdad1d50f9929a8035c36cf1c1e16d5b28f13de48431f3eb823a384c940b2b0a01512da98b8f72bd9545d179d6f1cd5a2a63", "wx": "008c5635eeaf7e994ff163ebdc9aacfdad1d50f9929a8035c36cf1c1e16d5b28f1", "wy": "3de48431f3eb823a384c940b2b0a01512da98b8f72bd9545d179d6f1cd5a2a63"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048c5635eeaf7e994ff163ebdc9aacfdad1d50f9929a8035c36cf1c1e16d5b28f13de48431f3eb823a384c940b2b0a01512da98b8f72bd9545d179d6f1cd5a2a63", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIxWNe6vfplP8WPr3Jqs/a0dUPmS\nmoA1w2zxweFtWyjxPeSEMfPrgjo4TJQLKwoBUS2pi49yvZVF0XnW8c1aKmM=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "3044022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2022021ff1192539621f272e135501f80b5e38271e553f11387cb1cd2cfb3b7db4487", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042b9999cf86f15a7471ff8d212ca3f9a99225851b6d9608034ce0af55fd539b5a25d1d06449a6a9f4db833ab69d1170b4f0f07d2e5f74a9b56212563a0356e0b6", "wx": "2b9999cf86f15a7471ff8d212ca3f9a99225851b6d9608034ce0af55fd539b5a", "wy": "25d1d06449a6a9f4db833ab69d1170b4f0f07d2e5f74a9b56212563a0356e0b6"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200042b9999cf86f15a7471ff8d212ca3f9a99225851b6d9608034ce0af55fd539b5a25d1d06449a6a9f4db833ab69d1170b4f0f07d2e5f74a9b56212563a0356e0b6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCuZmc+G8Vp0cf+NISyj+amSJYUb\nbZYIA0zgr1X9U5taJdHQZEmmqfTbgzq2nRFwtPDwfS5fdKm1YhJWOgNW4LY=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "30440220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048d40dbb264923c02a484fdc7f06108c727e5d18172c909f79a3845485c939f4594dd7b7c67653a712074d94890a8eb56a7d4b975024d3c82a1151669a6b83821", "wx": "008d40dbb264923c02a484fdc7f06108c727e5d18172c909f79a3845485c939f45", "wy": "0094dd7b7c67653a712074d94890a8eb56a7d4b975024d3c82a1151669a6b83821"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048d40dbb264923c02a484fdc7f06108c727e5d18172c909f79a3845485c939f4594dd7b7c67653a712074d94890a8eb56a7d4b975024d3c82a1151669a6b83821", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABI1A27JkkjwCpIT9x/BhCMcn5dGB\ncskJ95o4RUhck59FlN17fGdlOnEgdNlIkKjrVqfUuXUCTTyCoRUWaaa4OCE=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30440220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e0220796a6353bccf0b8675b699d502cbae2c88bb5799818ee4f9f93a0a5d477cd02e", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041fb44c46fcdcfe8e37f047bccf57ba1890643f0033d492c4b197ca7057c86067763f1041f8c38be3ad20945a6f0fad6f530af96fed289b4e8f02abd80b2f2d83", "wx": "1fb44c46fcdcfe8e37f047bccf57ba1890643f0033d492c4b197ca7057c86067", "wy": "763f1041f8c38be3ad20945a6f0fad6f530af96fed289b4e8f02abd80b2f2d83"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200041fb44c46fcdcfe8e37f047bccf57ba1890643f0033d492c4b197ca7057c86067763f1041f8c38be3ad20945a6f0fad6f530af96fed289b4e8f02abd80b2f2d83", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABB+0TEb83P6ON/BHvM9XuhiQZD8A\nM9SSxLGXynBXyGBndj8QQfjDi+OtIJRabw+tb1MK+W/tKJtOjwKr2AsvLYM=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30450220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e02210087fc46494e5887c9cb84d5407e02d78e09c7954fc44e1f2c734b3ecedf6d121f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048a42aef32568d8451e187a9441a6e886342d0033b04aaa4ddbd4d600c6a5c86a855fbb0861c7a642333f3723c6c3dd961f279d9943779d4c237deec94bff846e", "wx": "008a42aef32568d8451e187a9441a6e886342d0033b04aaa4ddbd4d600c6a5c86a", "wy": "00855fbb0861c7a642333f3723c6c3dd961f279d9943779d4c237deec94bff846e"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048a42aef32568d8451e187a9441a6e886342d0033b04aaa4ddbd4d600c6a5c86a855fbb0861c7a642333f3723c6c3dd961f279d9943779d4c237deec94bff846e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIpCrvMlaNhFHhh6lEGm6IY0LQAz\nsEqqTdvU1gDGpchqhV+7CGHHpkIzPzcjxsPdlh8nnZlDd51MI33uyUv/hG4=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30440220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e022021ff1192539621f272e135501f80b5e38271e553f11387cb1cd2cfb3b7db4488", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0467f999eb1a40fdda28044d2af23357aac045172ef1e89c6430a68deb0a5e2c21550d93565dfc6a0c5b5cf4e7d9111bf4e31a0d0f94b8adfd9b800c5b38cc22b0", "wx": "67f999eb1a40fdda28044d2af23357aac045172ef1e89c6430a68deb0a5e2c21", "wy": "550d93565dfc6a0c5b5cf4e7d9111bf4e31a0d0f94b8adfd9b800c5b38cc22b0"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000467f999eb1a40fdda28044d2af23357aac045172ef1e89c6430a68deb0a5e2c21550d93565dfc6a0c5b5cf4e7d9111bf4e31a0d0f94b8adfd9b800c5b38cc22b0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGf5mesaQP3aKARNKvIzV6rARRcu\n8eicZDCmjesKXiwhVQ2TVl38agxbXPTn2REb9OMaDQ+UuK39m4AMWzjMIrA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30440220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e02203090f487e51f9e35c8af70bb9ab7df45037e230a33d2c1fd96e404254fcb8679", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047cbf2bd2c89069d23ef7417cb783dec50089b3c45573ad00e1214b0c6f51ced56ef5cbc578da2f35cd8a43cf01a7078841fffef2bfaa4b931920ada792019b29", "wx": "7cbf2bd2c89069d23ef7417cb783dec50089b3c45573ad00e1214b0c6f51ced5", "wy": "6ef5cbc578da2f35cd8a43cf01a7078841fffef2bfaa4b931920ada792019b29"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200047cbf2bd2c89069d23ef7417cb783dec50089b3c45573ad00e1214b0c6f51ced56ef5cbc578da2f35cd8a43cf01a7078841fffef2bfaa4b931920ada792019b29", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABHy/K9LIkGnSPvdBfLeD3sUAibPE\nVXOtAOEhSwxvUc7VbvXLxXjaLzXNikPPAacHiEH//vK/qkuTGSCtp5IBmyk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "extreme value for k", "msg": "313233343030", "sig": "30440220743cf1b8b5cd4f2eb55f8aa369593ac436ef044166699e37d51a14c2ce13ea0e022077472d9a28b4ece71cf413a68eac0eb423a16fb462b1f48706fed48ca437bd2d", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0433d4259f3ac0ce8a534e7655f2068f80f401c742ec04084784d269c49ef0701f3e1dd6fc7c206d4d759c80e3612da4d0fcd4200afe7a68300e9c13f4ef23f880", "wx": "33d4259f3ac0ce8a534e7655f2068f80f401c742ec04084784d269c49ef0701f", "wy": "3e1dd6fc7c206d4d759c80e3612da4d0fcd4200afe7a68300e9c13f4ef23f880"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000433d4259f3ac0ce8a534e7655f2068f80f401c742ec04084784d269c49ef0701f3e1dd6fc7c206d4d759c80e3612da4d0fcd4200afe7a68300e9c13f4ef23f880", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDPUJZ86wM6KU052VfIGj4D0AcdC\n7AQIR4TSacSe8HAfPh3W/HwgbU11nIDjYS2k0PzUIAr+emgwDpwT9O8j+IA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "30450221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262022038a91d4935fa389414ccae3034812f25d9687e3691cb37a7dab4af80dd181ce2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046eacd3ac7f7be63942b897b75d2826210553e1973a5b38487531e0db4a8418cc6b781f1ec2302bf27f8c4a46c9179185b92a53a28b85b3c64171139dede35a05", "wx": "6eacd3ac7f7be63942b897b75d2826210553e1973a5b38487531e0db4a8418cc", "wy": "6b781f1ec2302bf27f8c4a46c9179185b92a53a28b85b3c64171139dede35a05"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046eacd3ac7f7be63942b897b75d2826210553e1973a5b38487531e0db4a8418cc6b781f1ec2302bf27f8c4a46c9179185b92a53a28b85b3c64171139dede35a05", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG6s06x/e+Y5QriXt10oJiEFU+GX\nOls4SHUx4NtKhBjMa3gfHsIwK/J/jEpGyReRhbkqU6KLhbPGQXETne3jWgU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30450221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace32620220796a6353bccf0b8675b699d502cbae2c88bb5799818ee4f9f93a0a5d477cd02e", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0449680c57a9644af8a3cb5d60f33bbeb54c910bd40dab3fdb8daa09182e4d791880fca5d924092c316ae8266b2a32b74f186f6cf22c29520871fb2ad2c44ee71a", "wx": "49680c57a9644af8a3cb5d60f33bbeb54c910bd40dab3fdb8daa09182e4d7918", "wy": "0080fca5d924092c316ae8266b2a32b74f186f6cf22c29520871fb2ad2c44ee71a"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000449680c57a9644af8a3cb5d60f33bbeb54c910bd40dab3fdb8daa09182e4d791880fca5d924092c316ae8266b2a32b74f186f6cf22c29520871fb2ad2c44ee71a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABEloDFepZEr4o8tdYPM7vrVMkQvU\nDas/242qCRguTXkYgPyl2SQJLDFq6CZrKjK3TxhvbPIsKVIIcfsq0sRO5xo=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30460221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace326202210087fc46494e5887c9cb84d5407e02d78e09c7954fc44e1f2c734b3ecedf6d121f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469566f1e4619346bf81d4b7e76705781ae6a3e8470806ae4f73d53bb03c207a1396a54d57b45951ebce9987f6adb457d7ce77c6c3820d657f9a8882cdfad66cf", "wx": "69566f1e4619346bf81d4b7e76705781ae6a3e8470806ae4f73d53bb03c207a1", "wy": "396a54d57b45951ebce9987f6adb457d7ce77c6c3820d657f9a8882cdfad66cf"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000469566f1e4619346bf81d4b7e76705781ae6a3e8470806ae4f73d53bb03c207a1396a54d57b45951ebce9987f6adb457d7ce77c6c3820d657f9a8882cdfad66cf", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGlWbx5GGTRr+B1LfnZwV4Guaj6E\ncIBq5Pc9U7sDwgehOWpU1XtFlR686Zh/attFfXznfGw4INZX+aiILN+tZs8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30450221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262022021ff1192539621f272e135501f80b5e38271e553f11387cb1cd2cfb3b7db4488", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0446868fbfc7150d0bdb1c8e9976d845dc4b8840f4d921299b6d8f989d4dce865783921b9a729e51d2deb5955f4d87cc2b299c7f01372ae82cd63f529a266d4b52", "wx": "46868fbfc7150d0bdb1c8e9976d845dc4b8840f4d921299b6d8f989d4dce8657", "wy": "0083921b9a729e51d2deb5955f4d87cc2b299c7f01372ae82cd63f529a266d4b52"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000446868fbfc7150d0bdb1c8e9976d845dc4b8840f4d921299b6d8f989d4dce865783921b9a729e51d2deb5955f4d87cc2b299c7f01372ae82cd63f529a266d4b52", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABEaGj7/HFQ0L2xyOmXbYRdxLiED0\n2SEpm22PmJ1NzoZXg5IbmnKeUdLetZVfTYfMKymcfwE3Kugs1j9SmiZtS1I=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "30450221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace326202203090f487e51f9e35c8af70bb9ab7df45037e230a33d2c1fd96e404254fcb8679", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044ba9ffbad26f909e59ff58118fb25d05e1fd2722cf1b9d88abfeb716c9f5461f76b2f395fdacb89f3b85fdf4cd733630403068559ba12c0f438f856286773f9b", "wx": "4ba9ffbad26f909e59ff58118fb25d05e1fd2722cf1b9d88abfeb716c9f5461f", "wy": "76b2f395fdacb89f3b85fdf4cd733630403068559ba12c0f438f856286773f9b"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200044ba9ffbad26f909e59ff58118fb25d05e1fd2722cf1b9d88abfeb716c9f5461f76b2f395fdacb89f3b85fdf4cd733630403068559ba12c0f438f856286773f9b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABEup/7rSb5CeWf9YEY+yXQXh/Sci\nzxudiKv+txbJ9UYfdrLzlf2suJ87hf30zXM2MEAwaFWboSwPQ4+FYoZ3P5s=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "extreme value for k", "msg": "313233343030", "sig": "30450221008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262022077472d9a28b4ece71cf413a68eac0eb423a16fb462b1f48706fed48ca437bd2d", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997", "wx": "008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262", "wy": "547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIvSrrnLflfLLEtIL/yBt6+53ifh\n470jwjpEU72azjJiVH74NcPaxP2X+EYaFGEdycJ3RRMt7Y5UXB1Uxy8EaZc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023022018487a43f28fcf1ae457b85dcd5befa281bf118519e960fecb720212a7e5c33c", "result": "invalid", "flags": []}, {"tcId": 350, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100989c5cc31440c1168f88b32ba6e47900183c0d843f9c41671898030664305d2b022018487a43f28fcf1ae457b85dcd5befa281bf118519e960fecb720212a7e5c33c", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262557c5fa5de13e4bea66dc47689226fa8abc4b110a73891d3c3f5f355f069e9e0", "wx": "008bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262", "wy": "557c5fa5de13e4bea66dc47689226fa8abc4b110a73891d3c3f5f355f069e9e0"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262557c5fa5de13e4bea66dc47689226fa8abc4b110a73891d3c3f5f355f069e9e0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIvSrrnLflfLLEtIL/yBt6+53ifh\n470jwjpEU72azjJiVXxfpd4T5L6mbcR2iSJvqKvEsRCnOJHTw/XzVfBp6eA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419feca605023022018487a43f28fcf1ae457b85dcd5befa281bf118519e960fecb720212a7e5c33c", "result": "invalid", "flags": []}, {"tcId": 352, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100989c5cc31440c1168f88b32ba6e47900183c0d843f9c41671898030664305d2b022018487a43f28fcf1ae457b85dcd5befa281bf118519e960fecb720212a7e5c33c", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042676bd1e3fd83f3328d1af941442c036760f09587729419053083eb61d1ed22c2cf769688a5ffd67da1899d243e66bcabe21f9e78335263bf5308b8e41a71b39", "wx": "2676bd1e3fd83f3328d1af941442c036760f09587729419053083eb61d1ed22c", "wy": "2cf769688a5ffd67da1899d243e66bcabe21f9e78335263bf5308b8e41a71b39"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200042676bd1e3fd83f3328d1af941442c036760f09587729419053083eb61d1ed22c2cf769688a5ffd67da1899d243e66bcabe21f9e78335263bf5308b8e41a71b39", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABCZ2vR4/2D8zKNGvlBRCwDZ2DwlY\ndylBkFMIPrYdHtIsLPdpaIpf/WfaGJnSQ+Zryr4h+eeDNSY79TCLjkGnGzk=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "pseudorandom signature", "msg": "", "sig": "30440220745be1da902d19c76c8f57d4a1f3362b4b20ed7c8de8fc0463d566795f979cea02205916c317a1e325b53735216a0fa37737f08b32245c88084817b468a41f5afee9", "result": "valid", "flags": []}, {"tcId": 354, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "304502200ff9279a0775740b7db8bec07f9a0401b7903886cb198c1b18c46de0673b31c30221008b3c8686bd1a1508b5b785e762fece8c6cf19b6156983e5c36b2bbe724d6c23e", "result": "valid", "flags": []}, {"tcId": 355, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "30450220351e727003896ec02949a3cf752223bcc6c2b611b30391edd60dc0c83dc9c98f022100924ad9dc00364d4aa2091416d173862f9b02965ff176e880ea62a673e16db98e", "result": "valid", "flags": []}, {"tcId": 356, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "3044022044a811b2321acbc65cacf80d2dbe848946f1dac528f3e1ae38b0e54d083c258f022055d7edfaecdda3bbc062d5074e3c3719d32761159d027ca27c1725ddbd62f688", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a9fb57db62501389594f0ee9fc1652fa83377fa302e19cef64252fc0b147f7749507acf5b04339ed102b9ca60db98c165b94ebe855d2202e46dce15ba1e028be", "wx": "00a9fb57db62501389594f0ee9fc1652fa83377fa302e19cef64252fc0b147f774", "wy": "009507acf5b04339ed102b9ca60db98c165b94ebe855d2202e46dce15ba1e028be"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57db62501389594f0ee9fc1652fa83377fa302e19cef64252fc0b147f7749507acf5b04339ed102b9ca60db98c165b94ebe855d2202e46dce15ba1e028be", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABKn7V9tiUBOJWU8O6fwWUvqDN3+j\nAuGc72QlL8CxR/d0lQes9bBDOe0QK5ymDbmMFluU6+hV0iAuRtzhW6HgKL4=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022062aab40a36d6a0d25644719ce31dc629ec684f6f0da32f9dd034ccc421dbd0ed022100a1fa6b0dfd9558da29374fb77505ee8ab3572161711f821d11807c7fff910c1c", "result": "valid", "flags": []}, {"tcId": 358, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30450220740cd3d3a9cd9dbe05ead4e39e54db27c0f1579da68e3aa5c9245b047aebc3b80221008ae78c12233d378fe2ce3c0fb2b769f8463830a71a5e5187c11b20fdd7e50445", "result": "valid", "flags": []}, {"tcId": 359, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022100a28f30245c5fb0c225fdec23924dc2cd4c2da888d1ee1bc5445858c646015ca802200ee364c1491c4551ef3509be8f88db0e04d0afb36528aeda1301b14948cc9cd6", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04351a45fb920f2c9f1b178438fa3bf272ff9328b881c477a1f56a8c0e884652761270f806fe40ad97ebf76c6825384b780ae6afccc792b05f2fb3eb7b7fffffff", "wx": "351a45fb920f2c9f1b178438fa3bf272ff9328b881c477a1f56a8c0e88465276", "wy": "1270f806fe40ad97ebf76c6825384b780ae6afccc792b05f2fb3eb7b7fffffff"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004351a45fb920f2c9f1b178438fa3bf272ff9328b881c477a1f56a8c0e884652761270f806fe40ad97ebf76c6825384b780ae6afccc792b05f2fb3eb7b7fffffff", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABDUaRfuSDyyfGxeEOPo78nL/kyi4\ngcR3ofVqjA6IRlJ2EnD4Bv5ArZfr92xoJThLeArmr8zHkrBfL7Pre3////8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "304402207f202f54f591b51105b227ee6d6da3adddfc4b5e819efc04befcdcbf7484f78302204360ea04503955fc3f025928b2dce50ff2d58b9060b34bbedfc3c219b3b4355b", "result": "valid", "flags": []}, {"tcId": 361, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3044022062e218dca32e4ef35692e9315e1e036bef1766073b846e38de20d2d29349f9fe0220519d4d4c6158d95474d793a0ee9c260a0c5469c5aab79510971b41fb4fae4baf", "result": "valid", "flags": []}, {"tcId": 362, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022100a3902295f6f743ac754db7b3fcd823be917b1191a5705728f5682492784da7f1022043def636660eff72e6435edb850c9126c7067938668f249998a0e4006b8ee7db", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040000000129b2146e36fc055545bf8f2cc70f8e73e8b25e539365ad7577cc35354a2b8c0319bc4ccd3e60da119477c23faf8fc2dcefc42d3af75827aeb42f6f0f", "wx": "0129b2146e36fc055545bf8f2cc70f8e73e8b25e539365ad7577cc3535", "wy": "4a2b8c0319bc4ccd3e60da119477c23faf8fc2dcefc42d3af75827aeb42f6f0f"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200040000000129b2146e36fc055545bf8f2cc70f8e73e8b25e539365ad7577cc35354a2b8c0319bc4ccd3e60da119477c23faf8fc2dcefc42d3af75827aeb42f6f0f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABAAAAAEpshRuNvwFVUW/jyzHD45z\n6LJeU5NlrXV3zDU1SiuMAxm8TM0+YNoRlHfCP6+PwtzvxC0691gnrrQvbw8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502210086d05b26a9ca7e10ae0681bb4c35a06d7a4e918f8625e3dfa7ac2d5aeda91c05022008c5f475a95888769da4a0e1b635c2292f654f934a5c5010fe0c729f3d11e1b1", "result": "valid", "flags": []}, {"tcId": 364, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3045022043c4474710d25094a2e21a9cc08585c26015f9f94012b100e72c0763aa9e0cff0221008345c46fd5592cefbd5ebb258965c05d964e6e6a278198ddc1e388cf1e75867c", "result": "valid", "flags": []}, {"tcId": 365, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402206d2724167e816528491cce574f0526209de52cd0f2af0085284fd050163d37c5022076dd1dd50ff9b553b0e142b7e6c6be8edf3708dd292f03f3e9bf157d21daa9eb", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04680becabe7d7df4fadfe5ae01fba5ea51b76759606a2e30612e667419b885d0508541dcb0723785c3c766581a7514a1ff42e4437d63f878271cb860f00000000", "wx": "680becabe7d7df4fadfe5ae01fba5ea51b76759606a2e30612e667419b885d05", "wy": "08541dcb0723785c3c766581a7514a1ff42e4437d63f878271cb860f00000000"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004680becabe7d7df4fadfe5ae01fba5ea51b76759606a2e30612e667419b885d0508541dcb0723785c3c766581a7514a1ff42e4437d63f878271cb860f00000000", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABGgL7Kvn199Prf5a4B+6XqUbdnWW\nBqLjBhLmZ0GbiF0FCFQdywcjeFw8dmWBp1FKH/QuRDfWP4eCccuGDwAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30440220321009a06c759c54cd66baafa0cbfd07eedb19f12a1ed654dd52b56f9c4fac7c02201956310a7e4757ec83ddb92d2763607354678149f1ad92387928cf887b4bed0f", "result": "valid", "flags": []}, {"tcId": 367, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "30450221009bdd359881c239e2415ca2af3d18463bb24be53f6f636cbd20360b6b333bc34502200ff03bc36cc1975bdc8680c44fbf2aefddf67c118c304b8b3d360eb10203c3a4", "result": "valid", "flags": []}, {"tcId": 368, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3044022048565eb7e7820d40754b5f264a4ceafa62bf75084241514b491995e7971e699502203da6df3d354f48daef6d078cf1124295fc8c3211f2757967c781dc2e9c62ed1a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee906100000001469fb456ca6a1720ca8db25d567e121cf921ce13e34000f8c12f5272", "wx": "7d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee9061", "wy": "01469fb456ca6a1720ca8db25d567e121cf921ce13e34000f8c12f5272"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200047d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee906100000001469fb456ca6a1720ca8db25d567e121cf921ce13e34000f8c12f5272", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABH0W/WVqnms05F2MjDtFjq57vCh5\n+LT2EXGpb2ZO7pBhAAAAAUaftFbKahcgyo2yXVZ+Ehz5Ic4T40AA+MEvUnI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30450220518e885def022eb5020fc90f4024d87122dc0f3ed7f869ed7720ff74a009fb7b0221008a3e26a8cd426d21eba5cd7a5614f3644395cfcecb24fe760a68a7a9e8f09c02", "result": "valid", "flags": []}, {"tcId": 370, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3044022004b01e5cc3ce9bf10844bc1cb21deeff6ebc9e2a7010cfbb3af0811354599c8102202e65fb8db62f255910ea4d5235bb21aa67aa59ffd519911ecd9893000ab67bb4", "result": "valid", "flags": []}, {"tcId": 371, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304502210094bb0601198c4ce266b0932426ffd00132d7d4e2de65ef47f56360825f26243802202734327d1989c9580f5458f04aac6fd5752a1ee5e236e9ed1a7c0b2d9b36db10", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee9061a9fb57da5b4ef56573fbf36fd2f5db1517bde406dc0452143cd347245e3f0105", "wx": "7d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee9061", "wy": "00a9fb57da5b4ef56573fbf36fd2f5db1517bde406dc0452143cd347245e3f0105"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200047d16fd656a9e6b34e45d8c8c3b458eae7bbc2879f8b4f61171a96f664eee9061a9fb57da5b4ef56573fbf36fd2f5db1517bde406dc0452143cd347245e3f0105", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABH0W/WVqnms05F2MjDtFjq57vCh5\n+LT2EXGpb2ZO7pBhqftX2ltO9WVz+/Nv0vXbFRe95AbcBFIUPNNHJF4/AQU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304402204dde197f962c63a7799c862e897b3bb1e7a7ddfb9ab77c2a17a54151ce604ad60220017e7aef86e533086425a2c4b32082f118913ef3667c8437672e0bbc7c2b8d7e", "result": "valid", "flags": []}, {"tcId": 373, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "304402207c53ed1d504ad4ba53d39792012a34d007250a2b8d1ca189c0d9f75ccc9a9957022009b97dcc5c67487114231d601374a8364cafa39581291762202b9215d51135fd", "result": "valid", "flags": []}, {"tcId": 374, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30450220513245ab2b6a4206bb0f6970c8ad040a94725ddc9a08db0fd9def93866ffbba1022100a53a7ab37decedae18dd5b5c48eb642b7a9c927e6bcf6bdac3a757e6d2c169c5", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0481528b7adbbebf1b6b3c7fa1d61284b07759b9a98d31a5702707b018fdecff1175bbfccb545381bf8601031731841829401b08dcdc68cc34e06a64e412038512", "wx": "0081528b7adbbebf1b6b3c7fa1d61284b07759b9a98d31a5702707b018fdecff11", "wy": "75bbfccb545381bf8601031731841829401b08dcdc68cc34e06a64e412038512"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000481528b7adbbebf1b6b3c7fa1d61284b07759b9a98d31a5702707b018fdecff1175bbfccb545381bf8601031731841829401b08dcdc68cc34e06a64e412038512", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABIFSi3rbvr8bazx/odYShLB3Wbmp\njTGlcCcHsBj97P8Rdbv8y1RTgb+GAQMXMYQYKUAbCNzcaMw04Gpk5BIDhRI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "3045022100a50318c3066a4966ad18ae8f85253fbb5835a34b2f9187daac71ee28d3d5d0eb02200890ef0fc93df222d11197cb221483ce897b0cf1acf4a909c306c5a485776abc", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 376, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "30440220041e0389dda2cf2ae3a9562a0fb5d41c1f7533e6cc84a896e99af781e21097700220366b5d88c36f1227df522fdab65e12347d68eb64f2de82c648115fd565bd37b7", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 377, "comment": "x-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "304502202a76394a04ae19b25c54291e28bcd42a7edeb20981b8a3b838f9dd0e29b574c10221009ce89980ae432c4fa6a68025da554bf900cc2eb0c66906420d322c14b453049c", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e16ea8e03c20977f20aed58106b6d9d1085b4475f75b5469c5f426cb27ec6d872", "wx": "00a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e", "wy": "16ea8e03c20977f20aed58106b6d9d1085b4475f75b5469c5f426cb27ec6d872"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e16ea8e03c20977f20aed58106b6d9d1085b4475f75b5469c5f426cb27ec6d872", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABKOiWjU8qpSsTu03APfVa0VqD8Zw\n1WoWbVIZt8l/MO8+FuqOA8IJd/IK7VgQa22dEIW0R191tUacX0Jssn7G2HI=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "y-coordinate of the public key is small on brainpoolP256t1", "msg": "4d657373616765", "sig": "3045022066958be3379405826a00daf5495b1657698126a5ff449f9649af26ca96df96670221009b4100816e2741f86c5c0b0dcf82e579f4281d2b8e70c234808d84c1a495079f", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 379, "comment": "y-coordinate of the public key is small on brainpoolP256t1", "msg": "4d657373616765", "sig": "3044022053ed0f4b8fb33ef277cdd1060435ed3dec518a225659f71f67f9a1f07f85c1ca0220124d5f94ddf12bb4cbe3c5cea6d2686d4480dabb8ffbb05e5238c877fe20383e", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 380, "comment": "y-coordinate of the public key is small on brainpoolP256t1", "msg": "4d657373616765", "sig": "3044022046643c7fe0f308b8af4ce2978d797e8c46a7e1f8bfee0b5cdbaecde1f59be41d02201bd11a814d1fbd9ae97a49df99beca7fec2512563c0031c5aad5b9fc2fb0a507", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e9310c9d7dfe531ca3378b2803215f061e887aec45f70d98bc0d0db6aa0a77b05", "wx": "00a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e", "wy": "009310c9d7dfe531ca3378b2803215f061e887aec45f70d98bc0d0db6aa0a77b05"}, "keyDer": "305a301406072a8648ce3d020106092b240303020801010703420004a3a25a353caa94ac4eed3700f7d56b456a0fc670d56a166d5219b7c97f30ef3e9310c9d7dfe531ca3378b2803215f061e887aec45f70d98bc0d0db6aa0a77b05", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABKOiWjU8qpSsTu03APfVa0VqD8Zw\n1WoWbVIZt8l/MO8+kxDJ19/lMcozeLKAMhXwYeiHrsRfcNmLwNDbaqCnewU=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "y-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "304402204f833bec9c80185beacbb73b5f984e2c03d922359be7468ce37584f53d1aea4a02206636744ab7fecaa53541bcf5f37c6cbe828a8efbc4d00f6469ba390a86708a26", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 382, "comment": "y-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "3045022100a2869da416523aad2b8fa8aad5c3b31c5a535fdd413b71af4dffb90c6f96a669022029ff3e8d499cabc3cc4cccd0fa811cc3b04770aa71f0d052185210b14d31993d", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 383, "comment": "y-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "3044022063dbfe29249a506b89fbd2cb1fafc254a9582dfc4b08d143b6d25bf2ab49d55e022044cad80c00460905e103f26da84cefd71af4bc7a71962a3bce321bc3b5842736", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d499b077ab6d77b244320a2cacab91a764595dd67a7a8dfcf84da7d38b2d8f45994c07b833ff4909c1a92cc9f24dea88be8603b407b00d228faf2158db2354f", "wx": "6d499b077ab6d77b244320a2cacab91a764595dd67a7a8dfcf84da7d38b2d8f4", "wy": "5994c07b833ff4909c1a92cc9f24dea88be8603b407b00d228faf2158db2354f"}, "keyDer": "305a301406072a8648ce3d020106092b2403030208010107034200046d499b077ab6d77b244320a2cacab91a764595dd67a7a8dfcf84da7d38b2d8f45994c07b833ff4909c1a92cc9f24dea88be8603b407b00d228faf2158db2354f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFowFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABG1Jmwd6ttd7JEMgosrKuRp2RZXd\nZ6eo38+E2n04stj0WZTAe4M/9JCcGpLMnyTeqIvoYDtAewDSKPryFY2yNU8=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "30450221009d907cf88e10d60c3f23892498fe43ddb02f824fb18e6be313e02d94f2c8e09002200c16b9e0db4dc8606c023b001f69b3c886080794fc9d7fe31b00c1cf0935e421", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 385, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "304402207395ce0ef652848a86b61097cc9543998d39dae88a1fc9e4dfdd69642949548902207de29e256e8202382f91c116a667a8b946f210447a57369ba61ae4fae73dd136", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 386, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP256t1", "msg": "4d657373616765", "sig": "304402207baf1fde87ccb1bea0f893b3bfb2549c04bca18835d8eb5a31b8d20506ff88c30220289ebe829fefb9ad009d7cdd622874aef5fa088f0508a4b43d5895d61645cecf", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP256r1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0408c2f95ffedde1d55e3f2c9dcf5884347f6904c6492273ad760eb7b9b35f036b2bcf7a048caa2c726ae8808dc95312eb2350275a8f4fbeea7c0f32f3839c7b93", "wx": "08c2f95ffedde1d55e3f2c9dcf5884347f6904c6492273ad760eb7b9b35f036b", "wy": "2bcf7a048caa2c726ae8808dc95312eb2350275a8f4fbeea7c0f32f3839c7b93"}, "keyDer": "305a301406072a8648ce3d020106092b24030302080101070342000408c2f95ffedde1d55e3f2c9dcf5884347f6904c6492273ad760eb7b9b35f036b2bcf7a048caa2c726ae8808dc95312eb2350275a8f4fbeea7c0f32f3839c7b93", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQEHA0IABAjC+V/+3eHVXj8snc9YhDR/aQTG\nSSJzrXYOt7mzXwNrK896BIyqLHJq6ICNyVMS6yNQJ1qPT77qfA8y84Oce5M=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "x-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "3044022033e37c3b66acabee3d68cbbb9c55cd52b586de51647723fa84e532a3ec5953ef02203b8a9ee707d1bc5f83e17ea072adc2ecda92e637d7c06060f1af79b929a850b3", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 388, "comment": "x-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "304402201f8ebdc94ecddd84f90960cc55d0ca02e33d70535fc1c7322b3c2783b9dc92380220205aa8626c3a5da214e5485b11154a378d70b0d3323ab868528ae8048d17b696", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 389, "comment": "x-coordinate of the public key is large on brainpoolP256t1", "msg": "4d657373616765", "sig": "304402206b0d70e09ba1642adac06dff9b52e22a3e4aab4180e372665691412241e743a002204d7d30ff8a210de69e3e6d1ecf7175f89f481a4d9ed06beaf7148da47f4af9e9", "result": "valid", "flags": ["GroupIsomorphism"]}]}]}