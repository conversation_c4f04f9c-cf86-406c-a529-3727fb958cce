#!/bin/bash
# Unit test runner for p2psocket interface
# This script builds and runs the testp2psocket unit tests

echo "========================================"
echo "P2P Socket Interface Unit Test Runner"
echo "========================================"

# Check if we're in the correct directory
if [ ! -f "testp2psocket.cpp" ]; then
    echo "Error: testp2psocket.cpp not found in current directory"
    echo "Please run this script from the src/p2psocket directory"
    exit 1
fi

# Build the project
echo "Building p2psocket library and tests..."
cd ../..

# Check if CMakeLists.txt exists in the root
if [ ! -f "CMakeLists.txt" ]; then
    echo "Error: CMakeLists.txt not found in project root"
    echo "Please ensure you're running from the correct directory"
    exit 1
fi

# Create build directory if it doesn't exist
mkdir -p build
cd build

# Configure and build
echo "Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    exit 1
fi

echo "Building..."
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

# Go to the bin directory where executables are built
cd bin

# Check if testp2psocket exists
if [ ! -f "testp2psocket" ]; then
    echo "Error: testp2psocket not found in bin directory"
    echo "Make sure the build completed successfully"
    exit 1
fi

echo ""
echo "========================================"
echo "Running Unit Tests"
echo "========================================"

# Run the unit tests with default settings (QUIC)
echo "Running tests with QUIC socket type..."
./testp2psocket -type 3
QUIC_RESULT=$?

echo ""
echo "========================================"
echo "Running tests with SSL socket type..."
./testp2psocket -type 2
SSL_RESULT=$?

echo ""
echo "========================================"
echo "Running tests with MULTITCP socket type..."
./testp2psocket -type 1
TCP_RESULT=$?

echo ""
echo "========================================"
echo "Test Results Summary"
echo "========================================"
echo "QUIC tests:     $QUIC_RESULT"
echo "SSL tests:      $SSL_RESULT"
echo "MULTITCP tests: $TCP_RESULT"

if [ $QUIC_RESULT -eq 0 ]; then
    echo "QUIC tests: PASSED"
else
    echo "QUIC tests: FAILED"
fi

if [ $SSL_RESULT -eq 0 ]; then
    echo "SSL tests: PASSED"
else
    echo "SSL tests: FAILED"
fi

if [ $TCP_RESULT -eq 0 ]; then
    echo "MULTITCP tests: PASSED"
else
    echo "MULTITCP tests: FAILED"
fi

# Calculate overall result
TOTAL_FAILURES=$((QUIC_RESULT + SSL_RESULT + TCP_RESULT))

if [ $TOTAL_FAILURES -eq 0 ]; then
    echo ""
    echo "*** ALL TESTS PASSED! ***"
    echo "Unit tests completed successfully."
else
    echo ""
    echo "*** SOME TESTS FAILED ***"
    echo "Please check the output above for details."
fi

exit $TOTAL_FAILURES
