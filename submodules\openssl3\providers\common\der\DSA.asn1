-- Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
--
-- Licensed under the Apache License 2.0 (the "License").  You may not use
-- this file except in compliance with the License.  You can obtain a copy
-- in the file LICENSE in the source distribution or at
-- https://www.openssl.org/source/license.html

-- -------------------------------------------------------------------
-- Taken from RFC 3279, 3  ASN.1 Module
-- (https://www.rfc-editor.org/rfc/rfc3279.html#section-3)

-- OID for DSA public key

id-dsa OBJECT IDENTIFIER ::= {
     iso(1) member-body(2) us(840) x9-57(10040) x9algorithm(4) 1 }

-- OID for DSA signature generated with SHA-1 hash

id-dsa-with-sha1 OBJECT IDENTIFIER ::=  {
     iso(1) member-body(2) us(840) x9-57 (10040) x9algorithm(4) 3 }


-- -------------------------------------------------------------------
-- Taken from https://csrc.nist.gov/projects/computer-security-objects-register/algorithm-registration

sigAlgs OBJECT IDENTIFIER ::= { 2 16 840 1 101 3 4 3 }

id-dsa-with-sha224 OBJECT IDENTIFIER ::= { sigAlgs 1 }
id-dsa-with-sha256 OBJECT IDENTIFIER ::= { sigAlgs 2 }
id-dsa-with-sha384 OBJECT IDENTIFIER ::= { sigAlgs 3 }
id-dsa-with-sha512 OBJECT IDENTIFIER ::= { sigAlgs 4 }

id-dsa-with-sha3-224 OBJECT IDENTIFIER ::= { sigAlgs 5 }
id-dsa-with-sha3-256 OBJECT IDENTIFIER ::= { sigAlgs 6 }
id-dsa-with-sha3-384 OBJECT IDENTIFIER ::= { sigAlgs 7 }
id-dsa-with-sha3-512 OBJECT IDENTIFIER ::= { sigAlgs 8 }
