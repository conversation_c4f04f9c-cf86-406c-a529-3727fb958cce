.. hazmat::

Constant time functions
=======================

.. currentmodule:: cryptography.hazmat.primitives.constant_time

This module contains functions for operating with secret data in a way that
does not leak information about that data through how long it takes to perform
the operation. These functions should be used whenever operating on secret data
along with data that is user supplied.

An example would be comparing a HMAC signature received from a client to the
one generated by the server code for authentication purposes.

For more information about this sort of issue, see `<PERSON><PERSON> Hale's blog post`_
about the timing attacks on KeyCzar and Java's ``MessageDigest.isEqual()``.


.. function:: bytes_eq(a, b)

    Compares ``a`` and ``b`` with one another. If ``a`` and ``b`` have
    different lengths, this returns ``False`` immediately. Otherwise it
    compares them in a way that takes the same amount of time, regardless of
    how many characters are the same between the two.

    .. doctest::

        >>> from cryptography.hazmat.primitives import constant_time
        >>> constant_time.bytes_eq(b"foo", b"foo")
        True
        >>> constant_time.bytes_eq(b"foo", b"bar")
        False

    :param bytes a: The left-hand side.
    :param bytes b: The right-hand side.
    :returns bool: ``True`` if ``a`` has the same bytes as ``b``, otherwise
                   ``False``.
    :raises TypeError: This exception is raised if ``a`` or ``b`` is not
                       ``bytes``.


.. _`Coda Hale's blog post`: https://codahale.com/a-lesson-in-timing-attacks/
