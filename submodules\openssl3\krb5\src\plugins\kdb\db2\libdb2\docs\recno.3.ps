%!PS-Adobe-3.0
%%Creator: groff version 1.08
%%DocumentNeededResources: font Times-Roman
%%+ font Times-Bold
%%+ font Times-Italic
%%DocumentSuppliedResources: procset grops 1.08 0
%%Pages: 2
%%PageOrder: Ascend
%%Orientation: Portrait
%%EndComments
%%BeginProlog
%%BeginResource: procset grops 1.08 0
/setpacking where{
pop
currentpacking
true setpacking
}if
/grops 120 dict dup begin
/SC 32 def
/A/show load def
/B{0 SC 3 -1 roll widthshow}bind def
/C{0 exch ashow}bind def
/D{0 exch 0 SC 5 2 roll awidthshow}bind def
/E{0 rmoveto show}bind def
/F{0 rmoveto 0 SC 3 -1 roll widthshow}bind def
/G{0 rmoveto 0 exch ashow}bind def
/H{0 rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/I{0 exch rmoveto show}bind def
/J{0 exch rmoveto 0 SC 3 -1 roll widthshow}bind def
/K{0 exch rmoveto 0 exch ashow}bind def
/L{0 exch rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/M{rmoveto show}bind def
/N{rmoveto 0 SC 3 -1 roll widthshow}bind def
/O{rmoveto 0 exch ashow}bind def
/P{rmoveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/Q{moveto show}bind def
/R{moveto 0 SC 3 -1 roll widthshow}bind def
/S{moveto 0 exch ashow}bind def
/T{moveto 0 exch 0 SC 5 2 roll awidthshow}bind def
/SF{
findfont exch
[exch dup 0 exch 0 exch neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/MF{
findfont
[5 2 roll
0 3 1 roll 
neg 0 0]makefont
dup setfont
[exch/setfont cvx]cvx bind def
}bind def
/level0 0 def
/RES 0 def
/PL 0 def
/LS 0 def
/PLG{
gsave newpath clippath pathbbox grestore
exch pop add exch pop
}bind def
/BP{
/level0 save def
1 setlinecap
1 setlinejoin
72 RES div dup scale
LS{
90 rotate
}{
0 PL translate
}ifelse
1 -1 scale
}bind def
/EP{
level0 restore
showpage
}bind def
/DA{
newpath arcn stroke
}bind def
/SN{
transform
.25 sub exch .25 sub exch
round .25 add exch round .25 add exch
itransform
}bind def
/DL{
SN
moveto
SN
lineto stroke
}bind def
/DC{
newpath 0 360 arc closepath
}bind def
/TM matrix def
/DE{
TM currentmatrix pop
translate scale newpath 0 0 .5 0 360 arc closepath
TM setmatrix
}bind def
/RC/rcurveto load def
/RL/rlineto load def
/ST/stroke load def
/MT/moveto load def
/CL/closepath load def
/FL{
currentgray exch setgray fill setgray
}bind def
/BL/fill load def
/LW/setlinewidth load def
/RE{
findfont
dup maxlength 1 index/FontName known not{1 add}if dict begin
{
1 index/FID ne{def}{pop pop}ifelse
}forall
/Encoding exch def
dup/FontName exch def
currentdict end definefont pop
}bind def
/DEFS 0 def
/EBEGIN{
moveto
DEFS begin
}bind def
/EEND/end load def
/CNT 0 def
/level1 0 def
/PBEGIN{
/level1 save def
translate
div 3 1 roll div exch scale
neg exch neg exch translate
0 setgray
0 setlinecap
1 setlinewidth
0 setlinejoin
10 setmiterlimit
[]0 setdash
/setstrokeadjust where{
pop
false setstrokeadjust
}if
/setoverprint where{
pop
false setoverprint
}if
newpath
/CNT countdictstack def
userdict begin
/showpage{}def
}bind def
/PEND{
clear
countdictstack CNT sub{end}repeat
level1 restore
}bind def
end def
/setpacking where{
pop
setpacking
}if
%%EndResource
%%IncludeResource: font Times-Roman
%%IncludeResource: font Times-Bold
%%IncludeResource: font Times-Italic
grops begin/DEFS 1 dict def DEFS begin/u{.001 mul}bind def end/RES 72 def/PL
792 def/LS false def/ENC0[/asciicircum/asciitilde/Scaron/Zcaron/scaron/zcaron
/Ydieresis/trademark/quotesingle/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/space
/exclam/quotedbl/numbersign/dollar/percent/ampersand/quoteright/parenleft
/parenright/asterisk/plus/comma/hyphen/period/slash/zero/one/two/three/four
/five/six/seven/eight/nine/colon/semicolon/less/equal/greater/question/at/A/B/C
/D/E/F/G/H/I/J/K/L/M/N/O/P/Q/R/S/T/U/V/W/X/Y/Z/bracketleft/backslash
/bracketright/circumflex/underscore/quoteleft/a/b/c/d/e/f/g/h/i/j/k/l/m/n/o/p/q
/r/s/t/u/v/w/x/y/z/braceleft/bar/braceright/tilde/.notdef/quotesinglbase
/guillemotleft/guillemotright/bullet/florin/fraction/perthousand/dagger
/daggerdbl/endash/emdash/ff/fi/fl/ffi/ffl/dotlessi/dotlessj/grave/hungarumlaut
/dotaccent/breve/caron/ring/ogonek/quotedblleft/quotedblright/oe/lslash
/quotedblbase/OE/Lslash/.notdef/exclamdown/cent/sterling/currency/yen/brokenbar
/section/dieresis/copyright/ordfeminine/guilsinglleft/logicalnot/minus
/registered/macron/degree/plusminus/twosuperior/threesuperior/acute/mu
/paragraph/periodcentered/cedilla/onesuperior/ordmasculine/guilsinglright
/onequarter/onehalf/threequarters/questiondown/Agrave/Aacute/Acircumflex/Atilde
/Adieresis/Aring/AE/Ccedilla/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute
/Icircumflex/Idieresis/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis
/multiply/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla/egrave/eacute
/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis/eth/ntilde/ograve
/oacute/ocircumflex/otilde/odieresis/divide/oslash/ugrave/uacute/ucircumflex
/udieresis/yacute/thorn/ydieresis]def/Times-Italic@0 ENC0/Times-Italic RE
/Times-Bold@0 ENC0/Times-Bold RE/Times-Roman@0 ENC0/Times-Roman RE
%%EndProlog
%%Page: 1 1
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 130.12(RECNO\(3\) BSD)72 48 R(Programmer')2.5 E 2.5(sM)
-.55 G 130.12(anual RECNO\(3\))340.17 48 R/F1 9/Times-Bold@0 SF -.18(NA)72 84 S
(ME).18 E F0(recno \255 record number database access method)108 96 Q F1
(SYNOPSIS)72 112.8 Q/F2 10/Times-Bold@0 SF(#include <sys/types.h>)108 124.8 Q
(#include <db)108 136.8 Q(.h>)-.4 E F1(DESCRIPTION)72 153.6 Q F0 1.158
(The routine)108 165.6 R/F3 10/Times-Italic@0 SF(dbopen)3.658 E F0 1.158
(is the library interf)3.658 F 1.158(ace to database \214les.)-.1 F 1.157
(One of the supported \214le formats is record)6.158 F 1.159(number \214les.)
108 177.6 R 1.159(The general description of the database access methods is in)
6.159 F F3(dbopen)3.66 E F0 1.16(\(3\), this manual page).24 F
(describes only the recno speci\214c information.)108 189.6 Q 1.944
(The record number data structure is either v)108 206.4 R 1.944
(ariable or \214x)-.25 F 1.944
(ed-length records stored in a \215at-\214le format,)-.15 F 2.04
(accessed by the logical record number)108 218.4 R 7.04(.T)-.55 G 2.04(he e)
286.31 218.4 R 2.04(xistence of record number \214v)-.15 F 4.54(ei)-.15 G 2.04
(mplies the e)442.1 218.4 R 2.04(xistence of)-.15 F .876
(records one through four)108 230.4 R 3.376(,a)-.4 G .875
(nd the deletion of record number one causes record number \214v)219.684 230.4
R 3.375(et)-.15 G 3.375(ob)489.93 230.4 S 3.375(er)503.305 230.4 S(enum-)514.45
230.4 Q .282(bered to record number four)108 242.4 R 2.782(,a)-.4 G 2.782(sw)
231.19 242.4 S .283(ell as the cursor)245.082 242.4 R 2.783(,i)-.4 G 2.783(fp)
316.633 242.4 S .283(ositioned after record number one, to shift do)327.746
242.4 R .283(wn one)-.25 F(record.)108 254.4 Q .373
(The recno access method speci\214c data structure pro)108 271.2 R .373
(vided to)-.15 F F3(dbopen)2.873 E F0 .373(is de\214ned in the <db)2.873 F .373
(.h> include \214le as)-.4 F(follo)108 283.2 Q(ws:)-.25 E(typedef struct {)108
300 Q(u_long \215ags;)144 312 Q(u_int cachesize;)144 324 Q(u_int psize;)144 336
Q(int lorder;)144 348 Q(size_t reclen;)144 360 Q(u_char b)144 372 Q -.25(va)
-.15 G(l;).25 E(char *bfname;)144 384 Q 2.5(}R)108 396 S(ECNOINFO;)121.97 396 Q
(The elements of this structure are de\214ned as follo)108 412.8 Q(ws:)-.25 E
14.61(\215ags The)108 429.6 R(\215ag v)2.5 E(alue is speci\214ed by)-.25 E F3
(or)2.5 E F0('ing an).73 E 2.5(yo)-.15 G 2.5(ft)313.2 429.6 S(he follo)321.81
429.6 Q(wing v)-.25 E(alues:)-.25 E(R_FIXEDLEN)144 446.4 Q .962
(The records are \214x)180 458.4 R .963(ed-length, not byte delimited.)-.15 F
.963(The structure element)5.963 F F3 -.37(re)3.463 G(clen).37 E F0
(speci\214es)3.463 E .345(the length of the record, and the structure element)
180 470.4 R F3(bval)2.844 E F0 .344(is used as the pad character)2.844 F 5.344
(.A)-.55 G -.15(ny)530.15 470.4 S .739
(records, inserted into the database, that are less than)180 482.4 R F3 -.37
(re)3.239 G(clen).37 E F0 .74(bytes long are automatically)3.239 F(padded.)180
494.4 Q(R_NOKEY)144 511.2 Q 2.34(In the interf)180 523.2 R 2.34
(ace speci\214ed by)-.1 F F3(dbopen)4.84 E F0 4.84(,t).24 G 2.34
(he sequential record retrie)344.98 523.2 R -.25(va)-.25 G 4.84<6c8c>.25 G 2.34
(lls in both the)478.25 523.2 R(caller')180 535.2 Q 3.556(sk)-.55 G 1.357 -.15
(ey a)217.336 535.2 T 1.057(nd data structures.).15 F 1.057
(If the R_NOKEY \215ag is speci\214ed, the)6.057 F F3(cur)3.557 E(sor)-.1 E F0
(routines)3.557 E .029(are not required to \214ll in the k)180 547.2 R .329
-.15(ey s)-.1 H 2.529(tructure. This).15 F .028(permits applications to retrie)
2.529 F .328 -.15(ve r)-.25 H .028(ecords at).15 F
(the end of \214les without reading all of the interv)180 559.2 Q
(ening records.)-.15 E(R_SN)144 576 Q(APSHO)-.35 E(T)-.4 E .964
(This \215ag requires that a snapshot of the \214le be tak)180 588 R .965
(en when)-.1 F F3(dbopen)3.465 E F0 .965(is called, instead of)3.465 F
(permitting an)180 600 Q 2.5(yu)-.15 G
(nmodi\214ed records to be read from the original \214le.)245.96 600 Q
(cachesize)108 616.8 Q 3.16(As)144 628.8 S .66
(uggested maximum size, in bytes, of the memory cache.)158.27 628.8 R .659
(This v)5.659 F .659(alue is)-.25 F F2(only)3.159 E F0(advisory)3.159 E 3.159
(,a)-.65 G .659(nd the)514.621 628.8 R .046
(access method will allocate more memory rather than f)144 640.8 R 2.546
(ail. If)-.1 F F3(cac)2.546 E(hesize)-.15 E F0 2.546(is 0)2.546 F .046
(\(no size is speci\214ed\) a)2.546 F(def)144 652.8 Q(ault cache is used.)-.1 E
12.95(psize The)108 669.6 R .715
(recno access method stores the in-memory copies of its records in a btree.)
3.216 F .715(This v)5.715 F .715(alue is the)-.25 F .805
(size \(in bytes\) of the pages used for nodes in that tree.)144 681.6 R(If)
5.805 E F3(psize)3.305 E F0 .806(is 0 \(no page size is speci\214ed\) a)3.305 F
1.468
(page size is chosen based on the underlying \214le system I/O block size.)144
693.6 R(See)6.467 E F3(btr)3.967 E(ee)-.37 E F0 1.467(\(3\) for more).18 F
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 96.815
(ution August)-.2 F(18, 1994)2.5 E(1)535 732 Q EP
%%Page: 2 2
%%BeginPageSetup
BP
%%EndPageSetup
/F0 10/Times-Roman@0 SF 130.12(RECNO\(3\) BSD)72 48 R(Programmer')2.5 E 2.5(sM)
-.55 G 130.12(anual RECNO\(3\))340.17 48 R(information.)144 84 Q 9.62
(lorder The)108 100.8 R 1.596(byte order for inte)4.096 F 1.596
(gers in the stored database metadata.)-.15 F 1.597
(The number should represent the)6.597 F .689(order as an inte)144 112.8 R .689
(ger; for e)-.15 F .689(xample, big endian order w)-.15 F .689
(ould be the number 4,321.)-.1 F(If)5.689 E/F1 10/Times-Italic@0 SF(lor)3.189 E
(der)-.37 E F0 .688(is 0 \(no)3.189 F
(order is speci\214ed\) the current host order is used.)144 124.8 Q 9.07
(reclen The)108 141.6 R(length of a \214x)2.5 E(ed-length record.)-.15 E -.15
(bv)108 158.4 S 16.68(al The)-.1 F .182
(delimiting byte to be used to mark the end of a record for v)2.682 F .183
(ariable-length records, and the pad)-.25 F .809(character for \214x)144 170.4
R .809(ed-length records.)-.15 F .809(If no v)5.809 F .809
(alue is speci\214ed, ne)-.25 F .809(wlines \(`)-.25 F(`\\n')-.74 E .808
('\) are used to mark the)-.74 F(end of v)144 182.4 Q
(ariable-length records and \214x)-.25 E
(ed-length records are padded with spaces.)-.15 E 3.51(bfname The)108 199.2 R
.505
(recno access method stores the in-memory copies of its records in a btree.)
3.005 F .506(If bfname is non-)5.506 F .065(NULL, it speci\214es the name of t\
he btree \214le, as if speci\214ed as the \214le name for a dbopen of a btree)
144 211.2 R(\214le.)144 223.2 Q .971(The data part of the k)108 240 R -.15(ey)
-.1 G .972(/data pair used by the recno access method is the same as other acc\
ess methods.).15 F .199(The k)108 252 R .499 -.15(ey i)-.1 H 2.699(sd).15 G(if)
157.507 252 Q 2.699(ferent. The)-.25 F F1(data)2.699 E F0 .199
(\214eld of the k)2.699 F .499 -.15(ey s)-.1 H .198
(hould be a pointer to a memory location of type).15 F F1 -.37(re)2.698 G
(cno_t).37 E F0 2.698(,a).68 G(s)536.11 252 Q .505(de\214ned in the <db)108 264
R .506(.h> include \214le.)-.4 F .506(This type is normally the lar)5.506 F
.506(gest unsigned inte)-.18 F .506(gral type a)-.15 F -.25(va)-.2 G .506
(ilable to the).25 F 2.5(implementation. The)108 276 R F1(size)2.5 E F0
(\214eld of the k)2.5 E .3 -.15(ey s)-.1 H(hould be the size of that type.).15
E .706(Because there can be no meta-data associated with the underlying recno \
access method \214les, an)108 292.8 R 3.206(yc)-.15 G(hanges)512.23 292.8 Q
1.262(made to the def)108 304.8 R 1.262(ault v)-.1 F 1.262(alues \(e.g. \214x)
-.25 F 1.263(ed record length or byte separator v)-.15 F 1.263
(alue\) must be e)-.25 F 1.263(xplicitly speci\214ed)-.15 F
(each time the \214le is opened.)108 316.8 Q .065(In the interf)108 333.6 R
.065(ace speci\214ed by)-.1 F F1(dbopen)2.564 E F0 2.564(,u).24 G .064
(sing the)261.548 333.6 R F1(put)2.564 E F0(interf)2.564 E .064
(ace to create a ne)-.1 F 2.564(wr)-.25 G .064
(ecord will cause the creation of)414.44 333.6 R .755(multiple, empty records \
if the record number is more than one greater than the lar)108 345.6 R .755
(gest record currently in)-.18 F(the database.)108 357.6 Q/F2 9/Times-Bold@0 SF
(ERR)72 374.4 Q(ORS)-.27 E F0(The)108 386.4 Q F1 -.37(re)2.922 G(cno).37 E F0
.421(access method routines may f)2.921 F .421(ail and set)-.1 F F1(errno)2.921
E F0 .421(for an)2.921 F 2.921(yo)-.15 G 2.921(ft)377.933 386.4 S .421
(he errors speci\214ed for the library rou-)386.964 386.4 R(tine)108 398.4 Q F1
(dbopen)2.5 E F0(\(3\) or the follo).24 E(wing:)-.25 E([EINV)108 415.2 Q(AL])
-1.35 E(An attempt w)144 427.2 Q(as made to add a record to a \214x)-.1 E
(ed-length database that w)-.15 E(as too lar)-.1 E(ge to \214t.)-.18 E F2
(SEE ALSO)72 444 Q F1(btr)108 456 Q(ee)-.37 E F0(\(3\)).18 E F1(dbopen)2.5 E F0
(\(3\),).24 E F1(hash)2.5 E F0(\(3\),).28 E F1(mpool)2.5 E F0(\(3\),).51 E F1
2.754(Document Pr)108 480 R 2.754(ocessing in a Relational Database System)-.45
F F0 5.255(,M).32 G 2.755(ichael Stonebrak)362.13 480 R(er)-.1 E 5.255(,H)-.4 G
2.755(eidi Stettner)454.06 480 R 5.255(,J)-.4 G(oseph)516.67 480 Q
(Kalash, Antonin Guttman, Nadene L)108 492 Q
(ynn, Memorandum No. UCB/ERL M82/32, May 1982.)-.55 E F2 -.09(BU)72 508.8 S(GS)
.09 E F0(Only big and little endian byte order is supported.)108 520.8 Q
(4.4 Berk)72 732 Q(ele)-.1 E 2.5(yD)-.15 G(istrib)132.57 732 Q 96.815
(ution August)-.2 F(18, 1994)2.5 E(2)535 732 Q EP
%%Trailer
end
%%EOF
