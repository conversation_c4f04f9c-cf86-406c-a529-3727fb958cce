=pod

=head1 NAME

ossl_cmp_certreq_new,
ossl_cmp_certrep_new,
ossl_cmp_rr_new,
ossl_cmp_rp_new,
ossl_cmp_certConf_new,
ossl_cmp_pkiconf_new,
ossl_cmp_pollReq_new,
ossl_cmp_pollRep_new,
ossl_cmp_genm_new,
ossl_cmp_genp_new,
ossl_cmp_error_new
- functions for generating CMP messages

=head1 SYNOPSIS

 #include "cmp_local.h"

 OSSL_ossl_cmp_MSG *ossl_cmp_certreq_new(OSSL_CMP_CTX *ctx, int bodytype,
                                         const OSSL_CRMF_MSG *crm);
 OSSL_CMP_MSG *ossl_cmp_certrep_new(OSSL_CMP_CTX *ctx, int bodytype,
                                    int certReqId, const OSSL_CMP_PKISI *si,
                                    X509 *cert, const X509 *encryption_recip,
                                    STACK_OF(X509) *chain, STACK_OF(X509) *caPubs,
                                    int unprotectedErrors);
 OSSL_CMP_MSG *ossl_cmp_rr_new(OSSL_CMP_CTX *ctx);
 OSSL_CMP_MSG *ossl_cmp_rp_new(OSSL_CMP_CTX *ctx, const OSSL_CMP_PKISI *si,
                               const OSSL_CRMF_CERTID *cid,
                               int unprotectedErrors);
 OSSL_CMP_MSG *ossl_cmp_certConf_new(OSSL_CMP_CTX *ctx, int certReqId,
                                     int fail_info, const char *text);
 OSSL_CMP_MSG *ossl_cmp_pkiconf_new(OSSL_CMP_CTX *ctx);
 OSSL_CMP_MSG *ossl_cmp_pollReq_new(OSSL_CMP_CTX *ctx, int crid);
 OSSL_CMP_MSG *ossl_cmp_pollRep_new(OSSL_CMP_CTX *ctx, int crid, int poll_after);
 OSSL_CMP_MSG *ossl_cmp_genm_new(OSSL_CMP_CTX *ctx);
 OSSL_CMP_MSG *ossl_cmp_genp_new(OSSL_CMP_CTX *ctx);
 OSSL_CMP_MSG *ossl_cmp_error_new(OSSL_CMP_CTX *ctx, const OSSL_CMP_PKISI *si,
                                  int64_t errorCode, const char *details,
                                  int unprotected);

=head1 DESCRIPTION

This is the internal API for creating various CMP PKIMESSAGES.
All functions are based on L<ossl_cmp_msg_create(3)>.
The allocate a new message, fill it with the relevant data derived from
the given B<OSSL_CMP_CTX>, and create the applicable protection.

ossl_cmp_certreq_new() creates a PKIMessage for requesting a certificate,
which can be either of IR/CR/KUR/P10CR, depending on the given I<bodytype>.
The CRMF message to use may be given explicitly via a non-NULL I<crm> argument,
otherwise it is created from the information in the I<ctx>.

Available CMP certificate request PKIMessage I<bodytype>s are:

=over 4

=item * B<OSSL_CMP_PKIBODY_IR>    - Initialization Request

=item * B<OSSL_CMP_PKIBODY_CR>    - Certification Request

=item * B<OSSL_CMP_PKIBODY_P10CR> - PKCS#10 Certification Request

=item * B<OSSL_CMP_PKIBODY_KUR>   - Key Update Request

=back

ossl_cmp_certrep_new() creates a PKIMessage for certificate response,
which can be either of IP/CP/KUP, depending on the given I<bodytype>,
with the given I<certReqId> and I<si> values and optionally with I<cert>,
I<chain>, and I<caPubs>. The I<cert>, I<chain>, and I<caPubs> arguments
are not consumed if present but their internal reference counter is increased.
The I<encryption_recip> is currently unsupported.
The function does not protect the message if the B<status> value in I<si>
is B<rejected> and I<unprotectedErrors> is nonzero.

Available CMP certificate response PKIMessage I<bodytype>s are:

=over 4

=item * B<OSSL_CMP_PKIBODY_IP>    - Initialization Response

=item * B<OSSL_CMP_PKIBODY_CP>    - Certification Response

=item * B<OSSL_CMP_PKIBODY_KUP>   - Key Update Response

=back

The list of all CMP PKIMessage I<bodytype>s is:

 #define OSSL_CMP_PKIBODY_IR        0
 #define OSSL_CMP_PKIBODY_IP        1
 #define OSSL_CMP_PKIBODY_CR        2
 #define OSSL_CMP_PKIBODY_CP        3
 #define OSSL_CMP_PKIBODY_P10CR     4
 #define OSSL_CMP_PKIBODY_POPDECC   5
 #define OSSL_CMP_PKIBODY_POPDECR   6
 #define OSSL_CMP_PKIBODY_KRR       9
 #define OSSL_CMP_PKIBODY_KRP      10
 #define OSSL_CMP_PKIBODY_RR       11
 #define OSSL_CMP_PKIBODY_RP       12
 #define OSSL_CMP_PKIBODY_CCR      13
 #define OSSL_CMP_PKIBODY_CCP      14
 #define OSSL_CMP_PKIBODY_CKUANN   15
 #define OSSL_CMP_PKIBODY_CANN     16
 #define OSSL_CMP_PKIBODY_RANN     17
 #define OSSL_CMP_PKIBODY_CRLANN   18
 #define OSSL_CMP_PKIBODY_PKICONF  19
 #define OSSL_CMP_PKIBODY_NESTED   20
 #define OSSL_CMP_PKIBODY_GENM     21
 #define OSSL_CMP_PKIBODY_GENP     22
 #define OSSL_CMP_PKIBODY_ERROR    23
 #define OSSL_CMP_PKIBODY_CERTCONF 24
 #define OSSL_CMP_PKIBODY_POLLREQ  25
 #define OSSL_CMP_PKIBODY_POLLREP  26

ossl_cmp_rr_new() creates a Revocation Request message from the
information set via OSSL_CMP_CTX_set1_oldClCert().

ossl_cmp_rp_new() creates a Revocation Response message with I<si> and I<cid>.
It does not protect the message if the B<status> value in I<si> is B<rejected>
and I<unprotectedErrors> is nonzero.

ossl_cmp_certConf_new() creates a Certificate Confirmation message for the last
received certificate with the given I<certReqId>.
The PKIStatus defaults to B<accepted> if the I<fail_info> bit field is 0.
Otherwise it is taken as the failInfo of the PKIStatusInfo, PKIStatus is
set to B<rejected>, and I<text> is copied to statusString unless it is NULL.

ossl_cmp_pkiconf_new() creates a PKI Confirmation message.

ossl_cmp_pollReq_new() creates a Polling Request message with certReqId set to
I<crid>.

ossl_cmp_pollRep_new() creates a Polling Response message with certReqId set to
I<crid> and pollAfter to I<poll_after>.

ossl_cmp_genm_new() creates a new General Message with an empty ITAV stack.

ossl_cmp_genp_new() creates a new General Response with an empty ITAV stack.

ossl_cmp_error_new() creates a new Error Message with the given contents
I<si>, I<errorCode>, and optional I<details>.
If I<errorCode> is positive and in the range of an OpenSSL error code,
the library and reason strings are included in the B<errorDetails> field.
If given, the I<details> are added to the contents of the B<errorDetails> field.
The function does not protect the message if I<unprotectedErrors> is nonzero.

=head1 NOTES

CMP is specified in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

All of the functions return a new OSSL_CMP_MSG structure containing
the generated message on success, or NULL on error.

=head1 SEE ALSO

L<ossl_cmp_msg_create(3)>,
L<OSSL_CMP_CTX_new(3)>, L<ERR_load_strings(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
