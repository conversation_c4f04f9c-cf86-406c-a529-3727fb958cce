=pod

=head1 NAME

SSL_CTX_get_ex_data, SSL_CTX_set_ex_data,
SSL_get_ex_data, SSL_set_ex_data
- Store and retrieve extra data from the SSL_CTX, SSL or SSL_SESSION

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void *SSL_CTX_get_ex_data(const SSL_CTX *s, int idx);

 int SSL_CTX_set_ex_data(SSL_CTX *s, int idx, void *arg);

 void *SSL_get_ex_data(const SSL *s, int idx);

 int SSL_set_ex_data(SSL *s, int idx, void *arg);

=head1 DESCRIPTION

SSL*_set_ex_data() functions can be used to store arbitrary user data into the
B<SSL_CTX>, or B<SSL> object. The user must supply a unique index
which they can subsequently use to retrieve the data using SSL*_get_ex_data().

For more detailed information see L<CRYPTO_get_ex_data(3)> and
L<CRYPTO_set_ex_data(3)> which implement these functions and
L<CRYPTO_get_ex_new_index(3)> for generating a unique index.

=head1 RETURN VALUES

The SSL*_set_ex_data() functions return 1 if the item is successfully stored
and 0 if it is not.
The SSL*_get_ex_data() functions return the ex_data pointer if successful,
otherwise NULL.

=head1 SEE ALSO

L<CRYPTO_get_ex_data(3)>, L<CRYPTO_set_ex_data(3)>,
L<CRYPTO_get_ex_new_index(3)>

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
