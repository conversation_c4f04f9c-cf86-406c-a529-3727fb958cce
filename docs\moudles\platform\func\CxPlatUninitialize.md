- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatUninitialize(
    void
    )
{
    // 减少系统引用计数。如果引用计数不为 0，说明系统仍在使用，直接返回。
    if (InterlockedDecrement16(&CxPlatRef) != 0) {
        return;
    }

    // 清理加密模块。
    CxPlatCryptUninitialize();

    // 确保全局堆已被初始化。
    CXPLAT_DBG_ASSERT(CxPlatform.Heap);

#ifdef TIMERR_NOERROR
#ifdef QUIC_HIGH_RES_TIMERS
    // 如果启用了高分辨率定时器，恢复系统默认的定时器分辨率。
    timeEndPeriod(CxPlatTimerCapabilities.wPeriodMin);
#endif
#endif // TIMERR_NOERROR

    // 清理处理器信息。
    CxPlatProcessorInfoUnInit();

    // 销毁全局堆。
    HeapDestroy(CxPlatform.Heap);
    CxPlatform.Heap = NULL;

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        WindowsUserUninitialized,
        "[ dll] Uninitialized");
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{引用计数减 1 后是否为 0?}:::decision
    B -- 否 --> Z([结束]):::startend
    B -- 是 --> C(反初始化加密模块<br/>调用 CxPlatCryptUninitialize):::process
    C --> D(检查堆是否存在):::process
    D --> E{定义 TIMERR_NOERROR?}:::decision
    E -- 是 --> F{定义 QUIC_HIGH_RES_TIMERS?}:::decision
    F -- 是 --> G(结束定时器周期<br/>调用 timeEndPeriod):::process
    F -- 否 --> H(反初始化处理器信息<br/>调用 CxPlatProcessorInfoUnInit):::process
    E -- 否 --> H
    G --> H
    H --> I(销毁堆<br/>调用 HeapDestroy):::process
    I --> J(记录卸载日志):::process
    J --> Z
```

---

- Windows kernel
```c
PAGEDX
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatUninitialize(
    void
    )
{
    PAGED_CODE(); // 确保此函数只能在被动 IRQL（PASSIVE_LEVEL）下调用。

    // 清理加密模块。
    CxPlatCryptUninitialize();

    // 关闭随机数生成算法提供程序。
    BCryptCloseAlgorithmProvider(CxPlatform.RngAlgorithm, 0);
    CxPlatform.RngAlgorithm = NULL;

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        WindowsKernelUninitialized,
        "[ sys] Uninitialized");
}
```

```mermaid
graph TD
    A([开始]):::startend --> B(执行 PAGED_CODE 检查):::process
    B --> C(反初始化加密模块<br/>调用 CxPlatCryptUninitialize):::process
    C --> D(关闭 RNG 算法句柄<br/>调用 BCryptCloseAlgorithmProvider):::process
    D --> E(将 RNG 算法句柄置为 NULL):::process
    E --> F(记录卸载日志):::process
    F --> G([结束]):::startend
```

---

- posix
```c
void
CxPlatUninitialize(
    void
    )
{
    // 减少系统引用计数。如果引用计数不为 0，说明系统仍在使用，直接返回。
    if (InterlockedDecrement16(&CxPlatRef) != 0) {
        return;
    }

    // 清理加密模块，释放相关资源。
    CxPlatCryptUninitialize();

    // 关闭随机数设备文件。
    close(RandomFd);

    // 记录系统卸载日志，便于调试和跟踪。
    QuicTraceLogInfo(
        PosixUninitialized,
        "[ dso] Uninitialized");
}
```

```mermaid
graph TD
    A([开始]):::startend --> B{引用计数减 1 后是否为 0?}:::decision
    B -- 否 --> Z([结束]):::startend
    B -- 是 --> C(反初始化加密模块<br/>调用 CxPlatCryptUninitialize):::process
    C --> D(关闭 /dev/urandom 文件描述符):::process
    D --> E(记录反初始化日志):::process
    E --> Z
```