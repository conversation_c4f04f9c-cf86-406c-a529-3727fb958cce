#
# Generated makefile dependencies follow.
#
accept_sec_context.so accept_sec_context.po $(OUTPRE)accept_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-input.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h ../generic/gssapi_err_generic.h \
  accept_sec_context.c gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h
acquire_cred.so acquire_cred.po $(OUTPRE)acquire_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h acquire_cred.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
canon_name.so canon_name.po $(OUTPRE)canon_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h canon_name.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
compare_name.so compare_name.po $(OUTPRE)compare_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h compare_name.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
context_time.so context_time.po $(OUTPRE)context_time.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h context_time.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
copy_ccache.so copy_ccache.po $(OUTPRE)copy_ccache.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h copy_ccache.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
cred_store.so cred_store.po $(OUTPRE)cred_store.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h cred_store.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
delete_sec_context.so delete_sec_context.po $(OUTPRE)delete_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h delete_sec_context.c \
  gssapiP_krb5.h gssapi_err_krb5.h gssapi_krb5.h
disp_name.so disp_name.po $(OUTPRE)disp_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h disp_name.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
disp_status.so disp_status.po $(OUTPRE)disp_status.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h disp_status.c error_map.h \
  gssapiP_krb5.h gssapi_err_krb5.h gssapi_krb5.h
duplicate_name.so duplicate_name.po $(OUTPRE)duplicate_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h duplicate_name.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
export_cred.so export_cred.po $(OUTPRE)export_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-json.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h ../generic/gssapi_err_generic.h \
  export_cred.c gssapiP_krb5.h gssapi_err_krb5.h gssapi_krb5.h
export_name.so export_name.po $(OUTPRE)export_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h export_name.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
export_sec_context.so export_sec_context.po $(OUTPRE)export_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h export_sec_context.c \
  gssapiP_krb5.h gssapi_err_krb5.h gssapi_krb5.h
get_tkt_flags.so get_tkt_flags.po $(OUTPRE)get_tkt_flags.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h get_tkt_flags.c gssapiP_krb5.h \
  gssapi_err_krb5.h gssapi_krb5.h
gssapi_krb5.so gssapi_krb5.po $(OUTPRE)gssapi_krb5.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(srcdir)/../mechglue/mechglue.h $(srcdir)/../mechglue/mglueP.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.c gssapi_krb5.h
iakerb.so iakerb.po $(OUTPRE)iakerb.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h iakerb.c
import_cred.so import_cred.po $(OUTPRE)import_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-json.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h ../generic/gssapi_err_generic.h \
  gssapiP_krb5.h gssapi_err_krb5.h gssapi_krb5.h import_cred.c
import_name.so import_name.po $(OUTPRE)import_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h import_name.c
import_sec_context.so import_sec_context.po $(OUTPRE)import_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h import_sec_context.c
indicate_mechs.so indicate_mechs.po $(OUTPRE)indicate_mechs.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(srcdir)/../mechglue/mechglue.h $(srcdir)/../mechglue/mglueP.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h indicate_mechs.c
init_sec_context.so init_sec_context.po $(OUTPRE)init_sec_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h init_sec_context.c
inq_context.so inq_context.po $(OUTPRE)inq_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h inq_context.c
inq_cred.so inq_cred.po $(OUTPRE)inq_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h inq_cred.c
inq_names.so inq_names.po $(OUTPRE)inq_names.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h inq_names.c
k5seal.so k5seal.po $(OUTPRE)k5seal.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5seal.c
k5sealiov.so k5sealiov.po $(OUTPRE)k5sealiov.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5sealiov.c
k5sealv3.so k5sealv3.po $(OUTPRE)k5sealv3.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5sealv3.c
k5sealv3iov.so k5sealv3iov.po $(OUTPRE)k5sealv3iov.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5sealv3iov.c
k5unseal.so k5unseal.po $(OUTPRE)k5unseal.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5unseal.c
k5unsealiov.so k5unsealiov.po $(OUTPRE)k5unsealiov.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h k5unsealiov.c
krb5_gss_glue.so krb5_gss_glue.po $(OUTPRE)krb5_gss_glue.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h krb5_gss_glue.c
lucid_context.so lucid_context.po $(OUTPRE)lucid_context.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h lucid_context.c
naming_exts.so naming_exts.po $(OUTPRE)naming_exts.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h naming_exts.c
prf.so prf.po $(OUTPRE)prf.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h prf.c
process_context_token.so process_context_token.po $(OUTPRE)process_context_token.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h process_context_token.c
rel_cred.so rel_cred.po $(OUTPRE)rel_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h rel_cred.c
rel_oid.so rel_oid.po $(OUTPRE)rel_oid.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h rel_oid.c
rel_name.so rel_name.po $(OUTPRE)rel_name.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h rel_name.c
s4u_gss_glue.so s4u_gss_glue.po $(OUTPRE)s4u_gss_glue.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h s4u_gss_glue.c
set_allowable_enctypes.so set_allowable_enctypes.po \
  $(OUTPRE)set_allowable_enctypes.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssapi/gssapi_alloc.h \
  $(BUILDTOP)/include/gssapi/gssapi_ext.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h set_allowable_enctypes.c
ser_sctx.so ser_sctx.po $(OUTPRE)ser_sctx.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h ser_sctx.c
set_ccache.so set_ccache.po $(OUTPRE)set_ccache.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h set_ccache.c
store_cred.so store_cred.po $(OUTPRE)store_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h store_cred.c
util_cksum.so util_cksum.po $(OUTPRE)util_cksum.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h util_cksum.c
util_crypt.so util_crypt.po $(OUTPRE)util_crypt.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h util_crypt.c
util_seed.so util_seed.po $(OUTPRE)util_seed.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h util_seed.c
util_seqnum.so util_seqnum.po $(OUTPRE)util_seqnum.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h util_seqnum.c
val_cred.so val_cred.po $(OUTPRE)val_cred.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h val_cred.c
wrap_size_limit.so wrap_size_limit.po $(OUTPRE)wrap_size_limit.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/gssapi/gssapi.h \
  $(BUILDTOP)/include/gssapi/gssapi_alloc.h $(BUILDTOP)/include/gssapi/gssapi_ext.h \
  $(BUILDTOP)/include/krb5/krb5.h $(BUILDTOP)/include/osconf.h \
  $(BUILDTOP)/include/profile.h $(COM_ERR_DEPS) $(srcdir)/../generic/gssapiP_generic.h \
  $(srcdir)/../generic/gssapi_ext.h $(srcdir)/../generic/gssapi_generic.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  ../generic/gssapi_err_generic.h gssapiP_krb5.h gssapi_err_krb5.h \
  gssapi_krb5.h wrap_size_limit.c
