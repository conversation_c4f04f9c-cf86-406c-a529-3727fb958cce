digraph mac {
    bgcolor="transparent";

    begin [label=start, color="#deeaee", style="filled"];
    newed [fontcolor="#c94c4c", style="solid"];
    initialised [fontcolor="#c94c4c"];
    updated [fontcolor="#c94c4c"];
    finaled [fontcolor="#c94c4c"];
    end [label=freed, color="#deeaee", style="filled"];

    begin -> newed [label="EVP_MAC_CTX_new"];
    newed -> initialised [label="EVP_MAC_init"];
    initialised -> updated [label="EVP_MAC_update"];
    updated -> updated [label="EVP_MAC_update"];
    updated -> finaled [label="EVP_MAC_final"];
    updated -> finaled [label="EVP_MAC_finalXOF",
                        fontcolor="#808080", color="#808080"];
    /* Once this works it should go back in:
    finaled -> finaled [label="EVP_MAC_final_XOF",
                        fontcolor="#808080", color="#808080"];
    */
    finaled -> end [label="EVP_MAC_CTX_free"];
    updated -> initialised [label="EVP_MAC_init", style=dashed,
                            color="#034f84", fontcolor="#034f84"];
    finaled -> initialised [label="EVP_MAC_init", style=dashed,
                            color="#034f84", fontcolor="#034f84"];
}

