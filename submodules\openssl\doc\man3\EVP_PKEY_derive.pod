=pod

=head1 NAME

EVP_PKEY_derive_init, E<PERSON>_PKEY_derive_set_peer, EVP_PKEY_derive - derive public key algorithm shared secret

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_derive_init(EVP_PKEY_CTX *ctx);
 int EVP_PKEY_derive_set_peer(EVP_PKEY_CTX *ctx, EVP_PKEY *peer);
 int EVP_PKEY_derive(EVP_PKEY_CTX *ctx, unsigned char *key, size_t *keylen);

=head1 DESCRIPTION

The EVP_PKEY_derive_init() function initializes a public key algorithm
context using key B<pkey> for shared secret derivation.

The EVP_PKEY_derive_set_peer() function sets the peer key: this will normally
be a public key.

The EVP_PKEY_derive() derives a shared secret using B<ctx>.
If B<key> is B<NULL> then the maximum size of the output buffer is written to
the B<keylen> parameter. If B<key> is not B<NULL> then before the call the
B<keylen> parameter should contain the length of the B<key> buffer, if the call
is successful the shared secret is written to B<key> and the amount of data
written to B<keylen>.

=head1 NOTES

After the call to EVP_PKEY_derive_init() algorithm specific control
operations can be performed to set any appropriate parameters for the
operation.

The function EVP_PKEY_derive() can be called more than once on the same
context if several operations are performed using the same parameters.

=head1 RETURN VALUES

EVP_PKEY_derive_init() and EVP_PKEY_derive() return 1 for success and 0
or a negative value for failure. In particular a return value of -2
indicates the operation is not supported by the public key algorithm.

=head1 EXAMPLES

Derive shared secret (for example DH or EC keys):

 #include <openssl/evp.h>
 #include <openssl/rsa.h>

 EVP_PKEY_CTX *ctx;
 ENGINE *eng;
 unsigned char *skey;
 size_t skeylen;
 EVP_PKEY *pkey, *peerkey;
 /* NB: assumes pkey, eng, peerkey have been already set up */

 ctx = EVP_PKEY_CTX_new(pkey, eng);
 if (!ctx)
     /* Error occurred */
 if (EVP_PKEY_derive_init(ctx) <= 0)
     /* Error */
 if (EVP_PKEY_derive_set_peer(ctx, peerkey) <= 0)
     /* Error */

 /* Determine buffer length */
 if (EVP_PKEY_derive(ctx, NULL, &skeylen) <= 0)
     /* Error */

 skey = OPENSSL_malloc(skeylen);

 if (!skey)
     /* malloc failure */

 if (EVP_PKEY_derive(ctx, skey, &skeylen) <= 0)
     /* Error */

 /* Shared secret is skey bytes written to buffer skey */

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_encrypt(3)>,
L<EVP_PKEY_decrypt(3)>,
L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify(3)>,
L<EVP_PKEY_verify_recover(3)>,

=head1 HISTORY

These functions were added in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2006-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
