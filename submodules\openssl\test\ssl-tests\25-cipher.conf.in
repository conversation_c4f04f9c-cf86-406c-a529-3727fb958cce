# -*- mode: perl; -*-
# Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test version negotiation

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (
    {
        name => "cipher-server-1",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
    },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384"
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES256-SHA384",
        },
    },
    {
        name => "cipher-server-2",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES128-SHA256"
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES128-SHA256",
        },
    },
    {
        name => "cipher-server-client-list",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384",
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES128-SHA256",
        },
    },
    {
        name => "cipher-server-pref-1",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
            "Options" => "ServerPreference",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384"
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES256-SHA384",
        },
    },
    {
        name => "cipher-server-pref-2",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
            "Options" => "ServerPreference",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES128-SHA256"
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES128-SHA256",
        },
    },
    {
        name => "cipher-server-pref-client-list",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256",
            "Options" => "ServerPreference",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384",
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES256-SHA384",
        },
    },
    {
        name => "cipher-server-pref-not-mobile",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-CHACHA20-POLY1305",
            "Options" => "ServerPreference",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-CHACHA20-POLY1305:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384",
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES256-SHA384",
        },
    },
    {
        name => "cipher-server-pref-mobile",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-CHACHA20-POLY1305",
            "Options" => "ServerPreference,PrioritizeChaCha",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-CHACHA20-POLY1305",
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-AES256-SHA384",
        },
    },
);

my @tests_poly1305 = (
    {
        name => "cipher-server-pref-mobile2",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-CHACHA20-POLY1305",
            "Options" => "ServerPreference,PrioritizeChaCha",
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            "CipherString" => "ECDHE-RSA-CHACHA20-POLY1305:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384",
        },
        test => {
            "ExpectedCipher" => "ECDHE-RSA-CHACHA20-POLY1305",
        },
    },
);

push @tests, @tests_poly1305 unless disabled("poly1305") || disabled("chacha");
