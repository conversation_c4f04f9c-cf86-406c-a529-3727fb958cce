OBJ_rsadsi="\x2A\x86\x48\x86\xF7\x0D"
OBJ_pkcs="\x2A\x86\x48\x86\xF7\x0D\x01"
OBJ_md2="\x2A\x86\x48\x86\xF7\x0D\x02\x02"
OBJ_md5="\x2A\x86\x48\x86\xF7\x0D\x02\x05"
OBJ_rc4="\x2A\x86\x48\x86\xF7\x0D\x03\x04"
OBJ_rsaEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x01"
OBJ_md2WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x02"
OBJ_md5WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x04"
OBJ_pbeWithMD2AndDES_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x01"
OBJ_pbeWithMD5AndDES_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x03"
OBJ_X500="\x55"
OBJ_X509="\x55\x04"
OBJ_commonName="\x55\x04\x03"
OBJ_countryName="\x55\x04\x06"
OBJ_localityName="\x55\x04\x07"
OBJ_stateOrProvinceName="\x55\x04\x08"
OBJ_organizationName="\x55\x04\x0A"
OBJ_organizationalUnitName="\x55\x04\x0B"
OBJ_rsa="\x55\x08\x01\x01"
OBJ_pkcs7="\x2A\x86\x48\x86\xF7\x0D\x01\x07"
OBJ_pkcs7_data="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x01"
OBJ_pkcs7_signed="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x02"
OBJ_pkcs7_enveloped="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x03"
OBJ_pkcs7_signedAndEnveloped="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x04"
OBJ_pkcs7_digest="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x05"
OBJ_pkcs7_encrypted="\x2A\x86\x48\x86\xF7\x0D\x01\x07\x06"
OBJ_pkcs3="\x2A\x86\x48\x86\xF7\x0D\x01\x03"
OBJ_dhKeyAgreement="\x2A\x86\x48\x86\xF7\x0D\x01\x03\x01"
OBJ_des_ecb="\x2B\x0E\x03\x02\x06"
OBJ_des_cfb64="\x2B\x0E\x03\x02\x09"
OBJ_des_cbc="\x2B\x0E\x03\x02\x07"
OBJ_des_ede_ecb="\x2B\x0E\x03\x02\x11"
OBJ_idea_cbc="\x2B\x06\x01\x04\x01\x81\x3C\x07\x01\x01\x02"
OBJ_rc2_cbc="\x2A\x86\x48\x86\xF7\x0D\x03\x02"
OBJ_sha="\x2B\x0E\x03\x02\x12"
OBJ_shaWithRSAEncryption="\x2B\x0E\x03\x02\x0F"
OBJ_des_ede3_cbc="\x2A\x86\x48\x86\xF7\x0D\x03\x07"
OBJ_des_ofb64="\x2B\x0E\x03\x02\x08"
OBJ_pkcs9="\x2A\x86\x48\x86\xF7\x0D\x01\x09"
OBJ_pkcs9_emailAddress="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x01"
OBJ_pkcs9_unstructuredName="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x02"
OBJ_pkcs9_contentType="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x03"
OBJ_pkcs9_messageDigest="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x04"
OBJ_pkcs9_signingTime="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x05"
OBJ_pkcs9_countersignature="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x06"
OBJ_pkcs9_challengePassword="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x07"
OBJ_pkcs9_unstructuredAddress="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x08"
OBJ_pkcs9_extCertAttributes="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x09"
OBJ_netscape="\x60\x86\x48\x01\x86\xF8\x42"
OBJ_netscape_cert_extension="\x60\x86\x48\x01\x86\xF8\x42\x01"
OBJ_netscape_data_type="\x60\x86\x48\x01\x86\xF8\x42\x02"
OBJ_sha1="\x2B\x0E\x03\x02\x1A"
OBJ_sha1WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x05"
OBJ_dsaWithSHA="\x2B\x0E\x03\x02\x0D"
OBJ_dsa_2="\x2B\x0E\x03\x02\x0C"
OBJ_pbeWithSHA1AndRC2_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x0B"
OBJ_id_pbkdf2="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x0C"
OBJ_dsaWithSHA1_2="\x2B\x0E\x03\x02\x1B"
OBJ_netscape_cert_type="\x60\x86\x48\x01\x86\xF8\x42\x01\x01"
OBJ_netscape_base_url="\x60\x86\x48\x01\x86\xF8\x42\x01\x02"
OBJ_netscape_revocation_url="\x60\x86\x48\x01\x86\xF8\x42\x01\x03"
OBJ_netscape_ca_revocation_url="\x60\x86\x48\x01\x86\xF8\x42\x01\x04"
OBJ_netscape_renewal_url="\x60\x86\x48\x01\x86\xF8\x42\x01\x07"
OBJ_netscape_ca_policy_url="\x60\x86\x48\x01\x86\xF8\x42\x01\x08"
OBJ_netscape_ssl_server_name="\x60\x86\x48\x01\x86\xF8\x42\x01\x0C"
OBJ_netscape_comment="\x60\x86\x48\x01\x86\xF8\x42\x01\x0D"
OBJ_netscape_cert_sequence="\x60\x86\x48\x01\x86\xF8\x42\x02\x05"
OBJ_id_ce="\x55\x1D"
OBJ_subject_key_identifier="\x55\x1D\x0E"
OBJ_key_usage="\x55\x1D\x0F"
OBJ_private_key_usage_period="\x55\x1D\x10"
OBJ_subject_alt_name="\x55\x1D\x11"
OBJ_issuer_alt_name="\x55\x1D\x12"
OBJ_basic_constraints="\x55\x1D\x13"
OBJ_crl_number="\x55\x1D\x14"
OBJ_certificate_policies="\x55\x1D\x20"
OBJ_authority_key_identifier="\x55\x1D\x23"
OBJ_bf_cbc="\x2B\x06\x01\x04\x01\x97\x55\x01\x02"
OBJ_mdc2="\x55\x08\x03\x65"
OBJ_mdc2WithRSA="\x55\x08\x03\x64"
OBJ_givenName="\x55\x04\x2A"
OBJ_surname="\x55\x04\x04"
OBJ_initials="\x55\x04\x2B"
OBJ_uniqueIdentifier="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2C"
OBJ_crl_distribution_points="\x55\x1D\x1F"
OBJ_md5WithRSA="\x2B\x0E\x03\x02\x03"
OBJ_serialNumber="\x55\x04\x05"
OBJ_title="\x55\x04\x0C"
OBJ_description="\x55\x04\x0D"
OBJ_cast5_cbc="\x2A\x86\x48\x86\xF6\x7D\x07\x42\x0A"
OBJ_pbeWithMD5AndCast5_CBC="\x2A\x86\x48\x86\xF6\x7D\x07\x42\x0C"
OBJ_dsaWithSHA1="\x2A\x86\x48\xCE\x38\x04\x03"
OBJ_sha1WithRSA="\x2B\x0E\x03\x02\x1D"
OBJ_dsa="\x2A\x86\x48\xCE\x38\x04\x01"
OBJ_ripemd160="\x2B\x24\x03\x02\x01"
OBJ_ripemd160WithRSA="\x2B\x24\x03\x03\x01\x02"
OBJ_rc5_cbc="\x2A\x86\x48\x86\xF7\x0D\x03\x08"
OBJ_zlib_compression="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x08"
OBJ_ext_key_usage="\x55\x1D\x25"
OBJ_id_pkix="\x2B\x06\x01\x05\x05\x07"
OBJ_id_kp="\x2B\x06\x01\x05\x05\x07\x03"
OBJ_server_auth="\x2B\x06\x01\x05\x05\x07\x03\x01"
OBJ_client_auth="\x2B\x06\x01\x05\x05\x07\x03\x02"
OBJ_code_sign="\x2B\x06\x01\x05\x05\x07\x03\x03"
OBJ_email_protect="\x2B\x06\x01\x05\x05\x07\x03\x04"
OBJ_time_stamp="\x2B\x06\x01\x05\x05\x07\x03\x08"
OBJ_ms_code_ind="\x2B\x06\x01\x04\x01\x82\x37\x02\x01\x15"
OBJ_ms_code_com="\x2B\x06\x01\x04\x01\x82\x37\x02\x01\x16"
OBJ_ms_ctl_sign="\x2B\x06\x01\x04\x01\x82\x37\x0A\x03\x01"
OBJ_ms_sgc="\x2B\x06\x01\x04\x01\x82\x37\x0A\x03\x03"
OBJ_ms_efs="\x2B\x06\x01\x04\x01\x82\x37\x0A\x03\x04"
OBJ_ns_sgc="\x60\x86\x48\x01\x86\xF8\x42\x04\x01"
OBJ_delta_crl="\x55\x1D\x1B"
OBJ_crl_reason="\x55\x1D\x15"
OBJ_invalidity_date="\x55\x1D\x18"
OBJ_sxnet="\x2B\x65\x01\x04\x01"
OBJ_pbe_WithSHA1And128BitRC4="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x01"
OBJ_pbe_WithSHA1And40BitRC4="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x02"
OBJ_pbe_WithSHA1And3_Key_TripleDES_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x03"
OBJ_pbe_WithSHA1And2_Key_TripleDES_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x04"
OBJ_pbe_WithSHA1And128BitRC2_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x05"
OBJ_pbe_WithSHA1And40BitRC2_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x01\x06"
OBJ_keyBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x01"
OBJ_pkcs8ShroudedKeyBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x02"
OBJ_certBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x03"
OBJ_crlBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x04"
OBJ_secretBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x05"
OBJ_safeContentsBag="\x2A\x86\x48\x86\xF7\x0D\x01\x0C\x0A\x01\x06"
OBJ_friendlyName="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x14"
OBJ_localKeyID="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x15"
OBJ_x509Certificate="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x16\x01"
OBJ_sdsiCertificate="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x16\x02"
OBJ_x509Crl="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x17\x01"
OBJ_pbes2="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x0D"
OBJ_pbmac1="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x0E"
OBJ_hmacWithSHA1="\x2A\x86\x48\x86\xF7\x0D\x02\x07"
OBJ_id_qt_cps="\x2B\x06\x01\x05\x05\x07\x02\x01"
OBJ_id_qt_unotice="\x2B\x06\x01\x05\x05\x07\x02\x02"
OBJ_SMIMECapabilities="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x0F"
OBJ_pbeWithMD2AndRC2_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x04"
OBJ_pbeWithMD5AndRC2_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x06"
OBJ_pbeWithSHA1AndDES_CBC="\x2A\x86\x48\x86\xF7\x0D\x01\x05\x0A"
OBJ_ms_ext_req="\x2B\x06\x01\x04\x01\x82\x37\x02\x01\x0E"
OBJ_ext_req="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x0E"
OBJ_name="\x55\x04\x29"
OBJ_dnQualifier="\x55\x04\x2E"
OBJ_id_pe="\x2B\x06\x01\x05\x05\x07\x01"
OBJ_id_ad="\x2B\x06\x01\x05\x05\x07\x30"
OBJ_info_access="\x2B\x06\x01\x05\x05\x07\x01\x01"
OBJ_ad_OCSP="\x2B\x06\x01\x05\x05\x07\x30\x01"
OBJ_ad_ca_issuers="\x2B\x06\x01\x05\x05\x07\x30\x02"
OBJ_OCSP_sign="\x2B\x06\x01\x05\x05\x07\x03\x09"
OBJ_member_body="\x2A"
OBJ_ISO_US="\x2A\x86\x48"
OBJ_X9_57="\x2A\x86\x48\xCE\x38"
OBJ_X9cm="\x2A\x86\x48\xCE\x38\x04"
OBJ_pkcs1="\x2A\x86\x48\x86\xF7\x0D\x01\x01"
OBJ_pkcs5="\x2A\x86\x48\x86\xF7\x0D\x01\x05"
OBJ_SMIME="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10"
OBJ_id_smime_mod="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00"
OBJ_id_smime_ct="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01"
OBJ_id_smime_aa="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02"
OBJ_id_smime_alg="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03"
OBJ_id_smime_cd="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x04"
OBJ_id_smime_spq="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x05"
OBJ_id_smime_cti="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06"
OBJ_id_smime_mod_cms="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x01"
OBJ_id_smime_mod_ess="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x02"
OBJ_id_smime_mod_oid="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x03"
OBJ_id_smime_mod_msg_v3="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x04"
OBJ_id_smime_mod_ets_eSignature_88="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x05"
OBJ_id_smime_mod_ets_eSignature_97="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x06"
OBJ_id_smime_mod_ets_eSigPolicy_88="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x07"
OBJ_id_smime_mod_ets_eSigPolicy_97="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x00\x08"
OBJ_id_smime_ct_receipt="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x01"
OBJ_id_smime_ct_authData="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x02"
OBJ_id_smime_ct_publishCert="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x03"
OBJ_id_smime_ct_TSTInfo="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x04"
OBJ_id_smime_ct_TDTInfo="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x05"
OBJ_id_smime_ct_contentInfo="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x06"
OBJ_id_smime_ct_DVCSRequestData="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x07"
OBJ_id_smime_ct_DVCSResponseData="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x08"
OBJ_id_smime_aa_receiptRequest="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x01"
OBJ_id_smime_aa_securityLabel="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x02"
OBJ_id_smime_aa_mlExpandHistory="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x03"
OBJ_id_smime_aa_contentHint="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x04"
OBJ_id_smime_aa_msgSigDigest="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x05"
OBJ_id_smime_aa_encapContentType="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x06"
OBJ_id_smime_aa_contentIdentifier="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x07"
OBJ_id_smime_aa_macValue="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x08"
OBJ_id_smime_aa_equivalentLabels="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x09"
OBJ_id_smime_aa_contentReference="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0A"
OBJ_id_smime_aa_encrypKeyPref="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0B"
OBJ_id_smime_aa_signingCertificate="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0C"
OBJ_id_smime_aa_smimeEncryptCerts="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0D"
OBJ_id_smime_aa_timeStampToken="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0E"
OBJ_id_smime_aa_ets_sigPolicyId="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x0F"
OBJ_id_smime_aa_ets_commitmentType="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x10"
OBJ_id_smime_aa_ets_signerLocation="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x11"
OBJ_id_smime_aa_ets_signerAttr="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x12"
OBJ_id_smime_aa_ets_otherSigCert="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x13"
OBJ_id_smime_aa_ets_contentTimestamp="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x14"
OBJ_id_smime_aa_ets_CertificateRefs="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x15"
OBJ_id_smime_aa_ets_RevocationRefs="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x16"
OBJ_id_smime_aa_ets_certValues="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x17"
OBJ_id_smime_aa_ets_revocationValues="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x18"
OBJ_id_smime_aa_ets_escTimeStamp="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x19"
OBJ_id_smime_aa_ets_certCRLTimestamp="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x1A"
OBJ_id_smime_aa_ets_archiveTimeStamp="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x1B"
OBJ_id_smime_aa_signatureType="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x1C"
OBJ_id_smime_aa_dvcs_dvc="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x1D"
OBJ_id_smime_alg_ESDHwith3DES="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x01"
OBJ_id_smime_alg_ESDHwithRC2="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x02"
OBJ_id_smime_alg_3DESwrap="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x03"
OBJ_id_smime_alg_RC2wrap="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x04"
OBJ_id_smime_alg_ESDH="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x05"
OBJ_id_smime_alg_CMS3DESwrap="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x06"
OBJ_id_smime_alg_CMSRC2wrap="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x07"
OBJ_id_smime_cd_ldap="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x04\x01"
OBJ_id_smime_spq_ets_sqt_uri="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x05\x01"
OBJ_id_smime_spq_ets_sqt_unotice="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x05\x02"
OBJ_id_smime_cti_ets_proofOfOrigin="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x01"
OBJ_id_smime_cti_ets_proofOfReceipt="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x02"
OBJ_id_smime_cti_ets_proofOfDelivery="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x03"
OBJ_id_smime_cti_ets_proofOfSender="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x04"
OBJ_id_smime_cti_ets_proofOfApproval="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x05"
OBJ_id_smime_cti_ets_proofOfCreation="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x06\x06"
OBJ_md4="\x2A\x86\x48\x86\xF7\x0D\x02\x04"
OBJ_id_pkix_mod="\x2B\x06\x01\x05\x05\x07\x00"
OBJ_id_qt="\x2B\x06\x01\x05\x05\x07\x02"
OBJ_id_it="\x2B\x06\x01\x05\x05\x07\x04"
OBJ_id_pkip="\x2B\x06\x01\x05\x05\x07\x05"
OBJ_id_alg="\x2B\x06\x01\x05\x05\x07\x06"
OBJ_id_cmc="\x2B\x06\x01\x05\x05\x07\x07"
OBJ_id_on="\x2B\x06\x01\x05\x05\x07\x08"
OBJ_id_pda="\x2B\x06\x01\x05\x05\x07\x09"
OBJ_id_aca="\x2B\x06\x01\x05\x05\x07\x0A"
OBJ_id_qcs="\x2B\x06\x01\x05\x05\x07\x0B"
OBJ_id_cct="\x2B\x06\x01\x05\x05\x07\x0C"
OBJ_id_pkix1_explicit_88="\x2B\x06\x01\x05\x05\x07\x00\x01"
OBJ_id_pkix1_implicit_88="\x2B\x06\x01\x05\x05\x07\x00\x02"
OBJ_id_pkix1_explicit_93="\x2B\x06\x01\x05\x05\x07\x00\x03"
OBJ_id_pkix1_implicit_93="\x2B\x06\x01\x05\x05\x07\x00\x04"
OBJ_id_mod_crmf="\x2B\x06\x01\x05\x05\x07\x00\x05"
OBJ_id_mod_cmc="\x2B\x06\x01\x05\x05\x07\x00\x06"
OBJ_id_mod_kea_profile_88="\x2B\x06\x01\x05\x05\x07\x00\x07"
OBJ_id_mod_kea_profile_93="\x2B\x06\x01\x05\x05\x07\x00\x08"
OBJ_id_mod_cmp="\x2B\x06\x01\x05\x05\x07\x00\x09"
OBJ_id_mod_qualified_cert_88="\x2B\x06\x01\x05\x05\x07\x00\x0A"
OBJ_id_mod_qualified_cert_93="\x2B\x06\x01\x05\x05\x07\x00\x0B"
OBJ_id_mod_attribute_cert="\x2B\x06\x01\x05\x05\x07\x00\x0C"
OBJ_id_mod_timestamp_protocol="\x2B\x06\x01\x05\x05\x07\x00\x0D"
OBJ_id_mod_ocsp="\x2B\x06\x01\x05\x05\x07\x00\x0E"
OBJ_id_mod_dvcs="\x2B\x06\x01\x05\x05\x07\x00\x0F"
OBJ_id_mod_cmp2000="\x2B\x06\x01\x05\x05\x07\x00\x10"
OBJ_biometricInfo="\x2B\x06\x01\x05\x05\x07\x01\x02"
OBJ_qcStatements="\x2B\x06\x01\x05\x05\x07\x01\x03"
OBJ_ac_auditEntity="\x2B\x06\x01\x05\x05\x07\x01\x04"
OBJ_ac_targeting="\x2B\x06\x01\x05\x05\x07\x01\x05"
OBJ_aaControls="\x2B\x06\x01\x05\x05\x07\x01\x06"
OBJ_sbgp_ipAddrBlock="\x2B\x06\x01\x05\x05\x07\x01\x07"
OBJ_sbgp_autonomousSysNum="\x2B\x06\x01\x05\x05\x07\x01\x08"
OBJ_sbgp_routerIdentifier="\x2B\x06\x01\x05\x05\x07\x01\x09"
OBJ_textNotice="\x2B\x06\x01\x05\x05\x07\x02\x03"
OBJ_ipsecEndSystem="\x2B\x06\x01\x05\x05\x07\x03\x05"
OBJ_ipsecTunnel="\x2B\x06\x01\x05\x05\x07\x03\x06"
OBJ_ipsecUser="\x2B\x06\x01\x05\x05\x07\x03\x07"
OBJ_dvcs="\x2B\x06\x01\x05\x05\x07\x03\x0A"
OBJ_id_it_caProtEncCert="\x2B\x06\x01\x05\x05\x07\x04\x01"
OBJ_id_it_signKeyPairTypes="\x2B\x06\x01\x05\x05\x07\x04\x02"
OBJ_id_it_encKeyPairTypes="\x2B\x06\x01\x05\x05\x07\x04\x03"
OBJ_id_it_preferredSymmAlg="\x2B\x06\x01\x05\x05\x07\x04\x04"
OBJ_id_it_caKeyUpdateInfo="\x2B\x06\x01\x05\x05\x07\x04\x05"
OBJ_id_it_currentCRL="\x2B\x06\x01\x05\x05\x07\x04\x06"
OBJ_id_it_unsupportedOIDs="\x2B\x06\x01\x05\x05\x07\x04\x07"
OBJ_id_it_subscriptionRequest="\x2B\x06\x01\x05\x05\x07\x04\x08"
OBJ_id_it_subscriptionResponse="\x2B\x06\x01\x05\x05\x07\x04\x09"
OBJ_id_it_keyPairParamReq="\x2B\x06\x01\x05\x05\x07\x04\x0A"
OBJ_id_it_keyPairParamRep="\x2B\x06\x01\x05\x05\x07\x04\x0B"
OBJ_id_it_revPassphrase="\x2B\x06\x01\x05\x05\x07\x04\x0C"
OBJ_id_it_implicitConfirm="\x2B\x06\x01\x05\x05\x07\x04\x0D"
OBJ_id_it_confirmWaitTime="\x2B\x06\x01\x05\x05\x07\x04\x0E"
OBJ_id_it_origPKIMessage="\x2B\x06\x01\x05\x05\x07\x04\x0F"
OBJ_id_regCtrl="\x2B\x06\x01\x05\x05\x07\x05\x01"
OBJ_id_regInfo="\x2B\x06\x01\x05\x05\x07\x05\x02"
OBJ_id_regCtrl_regToken="\x2B\x06\x01\x05\x05\x07\x05\x01\x01"
OBJ_id_regCtrl_authenticator="\x2B\x06\x01\x05\x05\x07\x05\x01\x02"
OBJ_id_regCtrl_pkiPublicationInfo="\x2B\x06\x01\x05\x05\x07\x05\x01\x03"
OBJ_id_regCtrl_pkiArchiveOptions="\x2B\x06\x01\x05\x05\x07\x05\x01\x04"
OBJ_id_regCtrl_oldCertID="\x2B\x06\x01\x05\x05\x07\x05\x01\x05"
OBJ_id_regCtrl_protocolEncrKey="\x2B\x06\x01\x05\x05\x07\x05\x01\x06"
OBJ_id_regInfo_utf8Pairs="\x2B\x06\x01\x05\x05\x07\x05\x02\x01"
OBJ_id_regInfo_certReq="\x2B\x06\x01\x05\x05\x07\x05\x02\x02"
OBJ_id_alg_des40="\x2B\x06\x01\x05\x05\x07\x06\x01"
OBJ_id_alg_noSignature="\x2B\x06\x01\x05\x05\x07\x06\x02"
OBJ_id_alg_dh_sig_hmac_sha1="\x2B\x06\x01\x05\x05\x07\x06\x03"
OBJ_id_alg_dh_pop="\x2B\x06\x01\x05\x05\x07\x06\x04"
OBJ_id_cmc_statusInfo="\x2B\x06\x01\x05\x05\x07\x07\x01"
OBJ_id_cmc_identification="\x2B\x06\x01\x05\x05\x07\x07\x02"
OBJ_id_cmc_identityProof="\x2B\x06\x01\x05\x05\x07\x07\x03"
OBJ_id_cmc_dataReturn="\x2B\x06\x01\x05\x05\x07\x07\x04"
OBJ_id_cmc_transactionId="\x2B\x06\x01\x05\x05\x07\x07\x05"
OBJ_id_cmc_senderNonce="\x2B\x06\x01\x05\x05\x07\x07\x06"
OBJ_id_cmc_recipientNonce="\x2B\x06\x01\x05\x05\x07\x07\x07"
OBJ_id_cmc_addExtensions="\x2B\x06\x01\x05\x05\x07\x07\x08"
OBJ_id_cmc_encryptedPOP="\x2B\x06\x01\x05\x05\x07\x07\x09"
OBJ_id_cmc_decryptedPOP="\x2B\x06\x01\x05\x05\x07\x07\x0A"
OBJ_id_cmc_lraPOPWitness="\x2B\x06\x01\x05\x05\x07\x07\x0B"
OBJ_id_cmc_getCert="\x2B\x06\x01\x05\x05\x07\x07\x0F"
OBJ_id_cmc_getCRL="\x2B\x06\x01\x05\x05\x07\x07\x10"
OBJ_id_cmc_revokeRequest="\x2B\x06\x01\x05\x05\x07\x07\x11"
OBJ_id_cmc_regInfo="\x2B\x06\x01\x05\x05\x07\x07\x12"
OBJ_id_cmc_responseInfo="\x2B\x06\x01\x05\x05\x07\x07\x13"
OBJ_id_cmc_queryPending="\x2B\x06\x01\x05\x05\x07\x07\x15"
OBJ_id_cmc_popLinkRandom="\x2B\x06\x01\x05\x05\x07\x07\x16"
OBJ_id_cmc_popLinkWitness="\x2B\x06\x01\x05\x05\x07\x07\x17"
OBJ_id_cmc_confirmCertAcceptance="\x2B\x06\x01\x05\x05\x07\x07\x18"
OBJ_id_on_personalData="\x2B\x06\x01\x05\x05\x07\x08\x01"
OBJ_id_pda_dateOfBirth="\x2B\x06\x01\x05\x05\x07\x09\x01"
OBJ_id_pda_placeOfBirth="\x2B\x06\x01\x05\x05\x07\x09\x02"
OBJ_id_pda_gender="\x2B\x06\x01\x05\x05\x07\x09\x03"
OBJ_id_pda_countryOfCitizenship="\x2B\x06\x01\x05\x05\x07\x09\x04"
OBJ_id_pda_countryOfResidence="\x2B\x06\x01\x05\x05\x07\x09\x05"
OBJ_id_aca_authenticationInfo="\x2B\x06\x01\x05\x05\x07\x0A\x01"
OBJ_id_aca_accessIdentity="\x2B\x06\x01\x05\x05\x07\x0A\x02"
OBJ_id_aca_chargingIdentity="\x2B\x06\x01\x05\x05\x07\x0A\x03"
OBJ_id_aca_group="\x2B\x06\x01\x05\x05\x07\x0A\x04"
OBJ_id_aca_role="\x2B\x06\x01\x05\x05\x07\x0A\x05"
OBJ_id_qcs_pkixQCSyntax_v1="\x2B\x06\x01\x05\x05\x07\x0B\x01"
OBJ_id_cct_crs="\x2B\x06\x01\x05\x05\x07\x0C\x01"
OBJ_id_cct_PKIData="\x2B\x06\x01\x05\x05\x07\x0C\x02"
OBJ_id_cct_PKIResponse="\x2B\x06\x01\x05\x05\x07\x0C\x03"
OBJ_ad_timeStamping="\x2B\x06\x01\x05\x05\x07\x30\x03"
OBJ_ad_dvcs="\x2B\x06\x01\x05\x05\x07\x30\x04"
OBJ_id_pkix_OCSP_basic="\x2B\x06\x01\x05\x05\x07\x30\x01\x01"
OBJ_id_pkix_OCSP_Nonce="\x2B\x06\x01\x05\x05\x07\x30\x01\x02"
OBJ_id_pkix_OCSP_CrlID="\x2B\x06\x01\x05\x05\x07\x30\x01\x03"
OBJ_id_pkix_OCSP_acceptableResponses="\x2B\x06\x01\x05\x05\x07\x30\x01\x04"
OBJ_id_pkix_OCSP_noCheck="\x2B\x06\x01\x05\x05\x07\x30\x01\x05"
OBJ_id_pkix_OCSP_archiveCutoff="\x2B\x06\x01\x05\x05\x07\x30\x01\x06"
OBJ_id_pkix_OCSP_serviceLocator="\x2B\x06\x01\x05\x05\x07\x30\x01\x07"
OBJ_id_pkix_OCSP_extendedStatus="\x2B\x06\x01\x05\x05\x07\x30\x01\x08"
OBJ_id_pkix_OCSP_valid="\x2B\x06\x01\x05\x05\x07\x30\x01\x09"
OBJ_id_pkix_OCSP_path="\x2B\x06\x01\x05\x05\x07\x30\x01\x0A"
OBJ_id_pkix_OCSP_trustRoot="\x2B\x06\x01\x05\x05\x07\x30\x01\x0B"
OBJ_algorithm="\x2B\x0E\x03\x02"
OBJ_rsaSignature="\x2B\x0E\x03\x02\x0B"
OBJ_X500algorithms="\x55\x08"
OBJ_org="\x2B"
OBJ_dod="\x2B\x06"
OBJ_iana="\x2B\x06\x01"
OBJ_Directory="\x2B\x06\x01\x01"
OBJ_Management="\x2B\x06\x01\x02"
OBJ_Experimental="\x2B\x06\x01\x03"
OBJ_Private="\x2B\x06\x01\x04"
OBJ_Security="\x2B\x06\x01\x05"
OBJ_SNMPv2="\x2B\x06\x01\x06"
OBJ_Mail="\x2B\x06\x01\x07"
OBJ_Enterprises="\x2B\x06\x01\x04\x01"
OBJ_dcObject="\x2B\x06\x01\x04\x01\x8B\x3A\x82\x58"
OBJ_domainComponent="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x19"
OBJ_Domain="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x0D"
OBJ_selected_attribute_types="\x55\x01\x05"
OBJ_clearance="\x55\x01\x05\x37"
OBJ_md4WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x03"
OBJ_ac_proxying="\x2B\x06\x01\x05\x05\x07\x01\x0A"
OBJ_sinfo_access="\x2B\x06\x01\x05\x05\x07\x01\x0B"
OBJ_id_aca_encAttrs="\x2B\x06\x01\x05\x05\x07\x0A\x06"
OBJ_role="\x55\x04\x48"
OBJ_policy_constraints="\x55\x1D\x24"
OBJ_target_information="\x55\x1D\x37"
OBJ_no_rev_avail="\x55\x1D\x38"
OBJ_ansi_X9_62="\x2A\x86\x48\xCE\x3D"
OBJ_X9_62_prime_field="\x2A\x86\x48\xCE\x3D\x01\x01"
OBJ_X9_62_characteristic_two_field="\x2A\x86\x48\xCE\x3D\x01\x02"
OBJ_X9_62_id_ecPublicKey="\x2A\x86\x48\xCE\x3D\x02\x01"
OBJ_X9_62_prime192v1="\x2A\x86\x48\xCE\x3D\x03\x01\x01"
OBJ_X9_62_prime192v2="\x2A\x86\x48\xCE\x3D\x03\x01\x02"
OBJ_X9_62_prime192v3="\x2A\x86\x48\xCE\x3D\x03\x01\x03"
OBJ_X9_62_prime239v1="\x2A\x86\x48\xCE\x3D\x03\x01\x04"
OBJ_X9_62_prime239v2="\x2A\x86\x48\xCE\x3D\x03\x01\x05"
OBJ_X9_62_prime239v3="\x2A\x86\x48\xCE\x3D\x03\x01\x06"
OBJ_X9_62_prime256v1="\x2A\x86\x48\xCE\x3D\x03\x01\x07"
OBJ_ecdsa_with_SHA1="\x2A\x86\x48\xCE\x3D\x04\x01"
OBJ_ms_csp_name="\x2B\x06\x01\x04\x01\x82\x37\x11\x01"
OBJ_aes_128_ecb="\x60\x86\x48\x01\x65\x03\x04\x01\x01"
OBJ_aes_128_cbc="\x60\x86\x48\x01\x65\x03\x04\x01\x02"
OBJ_aes_128_ofb128="\x60\x86\x48\x01\x65\x03\x04\x01\x03"
OBJ_aes_128_cfb128="\x60\x86\x48\x01\x65\x03\x04\x01\x04"
OBJ_aes_192_ecb="\x60\x86\x48\x01\x65\x03\x04\x01\x15"
OBJ_aes_192_cbc="\x60\x86\x48\x01\x65\x03\x04\x01\x16"
OBJ_aes_192_ofb128="\x60\x86\x48\x01\x65\x03\x04\x01\x17"
OBJ_aes_192_cfb128="\x60\x86\x48\x01\x65\x03\x04\x01\x18"
OBJ_aes_256_ecb="\x60\x86\x48\x01\x65\x03\x04\x01\x29"
OBJ_aes_256_cbc="\x60\x86\x48\x01\x65\x03\x04\x01\x2A"
OBJ_aes_256_ofb128="\x60\x86\x48\x01\x65\x03\x04\x01\x2B"
OBJ_aes_256_cfb128="\x60\x86\x48\x01\x65\x03\x04\x01\x2C"
OBJ_hold_instruction_code="\x55\x1D\x17"
OBJ_hold_instruction_none="\x2A\x86\x48\xCE\x38\x02\x01"
OBJ_hold_instruction_call_issuer="\x2A\x86\x48\xCE\x38\x02\x02"
OBJ_hold_instruction_reject="\x2A\x86\x48\xCE\x38\x02\x03"
OBJ_data="\x09"
OBJ_pss="\x09\x92\x26"
OBJ_ucl="\x09\x92\x26\x89\x93\xF2\x2C"
OBJ_pilot="\x09\x92\x26\x89\x93\xF2\x2C\x64"
OBJ_pilotAttributeType="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01"
OBJ_pilotAttributeSyntax="\x09\x92\x26\x89\x93\xF2\x2C\x64\x03"
OBJ_pilotObjectClass="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04"
OBJ_pilotGroups="\x09\x92\x26\x89\x93\xF2\x2C\x64\x0A"
OBJ_iA5StringSyntax="\x09\x92\x26\x89\x93\xF2\x2C\x64\x03\x04"
OBJ_caseIgnoreIA5StringSyntax="\x09\x92\x26\x89\x93\xF2\x2C\x64\x03\x05"
OBJ_pilotObject="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x03"
OBJ_pilotPerson="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x04"
OBJ_account="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x05"
OBJ_document="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x06"
OBJ_room="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x07"
OBJ_documentSeries="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x09"
OBJ_rFC822localPart="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x0E"
OBJ_dNSDomain="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x0F"
OBJ_domainRelatedObject="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x11"
OBJ_friendlyCountry="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x12"
OBJ_simpleSecurityObject="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x13"
OBJ_pilotOrganization="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x14"
OBJ_pilotDSA="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x15"
OBJ_qualityLabelledData="\x09\x92\x26\x89\x93\xF2\x2C\x64\x04\x16"
OBJ_userId="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x01"
OBJ_textEncodedORAddress="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x02"
OBJ_rfc822Mailbox="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x03"
OBJ_info="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x04"
OBJ_favouriteDrink="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x05"
OBJ_roomNumber="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x06"
OBJ_photo="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x07"
OBJ_userClass="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x08"
OBJ_host="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x09"
OBJ_manager="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0A"
OBJ_documentIdentifier="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0B"
OBJ_documentTitle="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0C"
OBJ_documentVersion="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0D"
OBJ_documentAuthor="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0E"
OBJ_documentLocation="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x0F"
OBJ_homeTelephoneNumber="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x14"
OBJ_secretary="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x15"
OBJ_otherMailbox="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x16"
OBJ_lastModifiedTime="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x17"
OBJ_lastModifiedBy="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x18"
OBJ_aRecord="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1A"
OBJ_pilotAttributeType27="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1B"
OBJ_mXRecord="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1C"
OBJ_nSRecord="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1D"
OBJ_sOARecord="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1E"
OBJ_cNAMERecord="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x1F"
OBJ_associatedDomain="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x25"
OBJ_associatedName="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x26"
OBJ_homePostalAddress="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x27"
OBJ_personalTitle="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x28"
OBJ_mobileTelephoneNumber="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x29"
OBJ_pagerTelephoneNumber="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2A"
OBJ_friendlyCountryName="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2B"
OBJ_organizationalStatus="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2D"
OBJ_janetMailbox="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2E"
OBJ_mailPreferenceOption="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x2F"
OBJ_buildingName="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x30"
OBJ_dSAQuality="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x31"
OBJ_singleLevelQuality="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x32"
OBJ_subtreeMinimumQuality="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x33"
OBJ_subtreeMaximumQuality="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x34"
OBJ_personalSignature="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x35"
OBJ_dITRedirect="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x36"
OBJ_audio="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x37"
OBJ_documentPublisher="\x09\x92\x26\x89\x93\xF2\x2C\x64\x01\x38"
OBJ_x500UniqueIdentifier="\x55\x04\x2D"
OBJ_mime_mhs="\x2B\x06\x01\x07\x01"
OBJ_mime_mhs_headings="\x2B\x06\x01\x07\x01\x01"
OBJ_mime_mhs_bodies="\x2B\x06\x01\x07\x01\x02"
OBJ_id_hex_partial_message="\x2B\x06\x01\x07\x01\x01\x01"
OBJ_id_hex_multipart_message="\x2B\x06\x01\x07\x01\x01\x02"
OBJ_generationQualifier="\x55\x04\x2C"
OBJ_pseudonym="\x55\x04\x41"
OBJ_id_set="\x67\x2A"
OBJ_set_ctype="\x67\x2A\x00"
OBJ_set_msgExt="\x67\x2A\x01"
OBJ_set_attr="\x67\x2A\x03"
OBJ_set_policy="\x67\x2A\x05"
OBJ_set_certExt="\x67\x2A\x07"
OBJ_set_brand="\x67\x2A\x08"
OBJ_setct_PANData="\x67\x2A\x00\x00"
OBJ_setct_PANToken="\x67\x2A\x00\x01"
OBJ_setct_PANOnly="\x67\x2A\x00\x02"
OBJ_setct_OIData="\x67\x2A\x00\x03"
OBJ_setct_PI="\x67\x2A\x00\x04"
OBJ_setct_PIData="\x67\x2A\x00\x05"
OBJ_setct_PIDataUnsigned="\x67\x2A\x00\x06"
OBJ_setct_HODInput="\x67\x2A\x00\x07"
OBJ_setct_AuthResBaggage="\x67\x2A\x00\x08"
OBJ_setct_AuthRevReqBaggage="\x67\x2A\x00\x09"
OBJ_setct_AuthRevResBaggage="\x67\x2A\x00\x0A"
OBJ_setct_CapTokenSeq="\x67\x2A\x00\x0B"
OBJ_setct_PInitResData="\x67\x2A\x00\x0C"
OBJ_setct_PI_TBS="\x67\x2A\x00\x0D"
OBJ_setct_PResData="\x67\x2A\x00\x0E"
OBJ_setct_AuthReqTBS="\x67\x2A\x00\x10"
OBJ_setct_AuthResTBS="\x67\x2A\x00\x11"
OBJ_setct_AuthResTBSX="\x67\x2A\x00\x12"
OBJ_setct_AuthTokenTBS="\x67\x2A\x00\x13"
OBJ_setct_CapTokenData="\x67\x2A\x00\x14"
OBJ_setct_CapTokenTBS="\x67\x2A\x00\x15"
OBJ_setct_AcqCardCodeMsg="\x67\x2A\x00\x16"
OBJ_setct_AuthRevReqTBS="\x67\x2A\x00\x17"
OBJ_setct_AuthRevResData="\x67\x2A\x00\x18"
OBJ_setct_AuthRevResTBS="\x67\x2A\x00\x19"
OBJ_setct_CapReqTBS="\x67\x2A\x00\x1A"
OBJ_setct_CapReqTBSX="\x67\x2A\x00\x1B"
OBJ_setct_CapResData="\x67\x2A\x00\x1C"
OBJ_setct_CapRevReqTBS="\x67\x2A\x00\x1D"
OBJ_setct_CapRevReqTBSX="\x67\x2A\x00\x1E"
OBJ_setct_CapRevResData="\x67\x2A\x00\x1F"
OBJ_setct_CredReqTBS="\x67\x2A\x00\x20"
OBJ_setct_CredReqTBSX="\x67\x2A\x00\x21"
OBJ_setct_CredResData="\x67\x2A\x00\x22"
OBJ_setct_CredRevReqTBS="\x67\x2A\x00\x23"
OBJ_setct_CredRevReqTBSX="\x67\x2A\x00\x24"
OBJ_setct_CredRevResData="\x67\x2A\x00\x25"
OBJ_setct_PCertReqData="\x67\x2A\x00\x26"
OBJ_setct_PCertResTBS="\x67\x2A\x00\x27"
OBJ_setct_BatchAdminReqData="\x67\x2A\x00\x28"
OBJ_setct_BatchAdminResData="\x67\x2A\x00\x29"
OBJ_setct_CardCInitResTBS="\x67\x2A\x00\x2A"
OBJ_setct_MeAqCInitResTBS="\x67\x2A\x00\x2B"
OBJ_setct_RegFormResTBS="\x67\x2A\x00\x2C"
OBJ_setct_CertReqData="\x67\x2A\x00\x2D"
OBJ_setct_CertReqTBS="\x67\x2A\x00\x2E"
OBJ_setct_CertResData="\x67\x2A\x00\x2F"
OBJ_setct_CertInqReqTBS="\x67\x2A\x00\x30"
OBJ_setct_ErrorTBS="\x67\x2A\x00\x31"
OBJ_setct_PIDualSignedTBE="\x67\x2A\x00\x32"
OBJ_setct_PIUnsignedTBE="\x67\x2A\x00\x33"
OBJ_setct_AuthReqTBE="\x67\x2A\x00\x34"
OBJ_setct_AuthResTBE="\x67\x2A\x00\x35"
OBJ_setct_AuthResTBEX="\x67\x2A\x00\x36"
OBJ_setct_AuthTokenTBE="\x67\x2A\x00\x37"
OBJ_setct_CapTokenTBE="\x67\x2A\x00\x38"
OBJ_setct_CapTokenTBEX="\x67\x2A\x00\x39"
OBJ_setct_AcqCardCodeMsgTBE="\x67\x2A\x00\x3A"
OBJ_setct_AuthRevReqTBE="\x67\x2A\x00\x3B"
OBJ_setct_AuthRevResTBE="\x67\x2A\x00\x3C"
OBJ_setct_AuthRevResTBEB="\x67\x2A\x00\x3D"
OBJ_setct_CapReqTBE="\x67\x2A\x00\x3E"
OBJ_setct_CapReqTBEX="\x67\x2A\x00\x3F"
OBJ_setct_CapResTBE="\x67\x2A\x00\x40"
OBJ_setct_CapRevReqTBE="\x67\x2A\x00\x41"
OBJ_setct_CapRevReqTBEX="\x67\x2A\x00\x42"
OBJ_setct_CapRevResTBE="\x67\x2A\x00\x43"
OBJ_setct_CredReqTBE="\x67\x2A\x00\x44"
OBJ_setct_CredReqTBEX="\x67\x2A\x00\x45"
OBJ_setct_CredResTBE="\x67\x2A\x00\x46"
OBJ_setct_CredRevReqTBE="\x67\x2A\x00\x47"
OBJ_setct_CredRevReqTBEX="\x67\x2A\x00\x48"
OBJ_setct_CredRevResTBE="\x67\x2A\x00\x49"
OBJ_setct_BatchAdminReqTBE="\x67\x2A\x00\x4A"
OBJ_setct_BatchAdminResTBE="\x67\x2A\x00\x4B"
OBJ_setct_RegFormReqTBE="\x67\x2A\x00\x4C"
OBJ_setct_CertReqTBE="\x67\x2A\x00\x4D"
OBJ_setct_CertReqTBEX="\x67\x2A\x00\x4E"
OBJ_setct_CertResTBE="\x67\x2A\x00\x4F"
OBJ_setct_CRLNotificationTBS="\x67\x2A\x00\x50"
OBJ_setct_CRLNotificationResTBS="\x67\x2A\x00\x51"
OBJ_setct_BCIDistributionTBS="\x67\x2A\x00\x52"
OBJ_setext_genCrypt="\x67\x2A\x01\x01"
OBJ_setext_miAuth="\x67\x2A\x01\x03"
OBJ_setext_pinSecure="\x67\x2A\x01\x04"
OBJ_setext_pinAny="\x67\x2A\x01\x05"
OBJ_setext_track2="\x67\x2A\x01\x07"
OBJ_setext_cv="\x67\x2A\x01\x08"
OBJ_set_policy_root="\x67\x2A\x05\x00"
OBJ_setCext_hashedRoot="\x67\x2A\x07\x00"
OBJ_setCext_certType="\x67\x2A\x07\x01"
OBJ_setCext_merchData="\x67\x2A\x07\x02"
OBJ_setCext_cCertRequired="\x67\x2A\x07\x03"
OBJ_setCext_tunneling="\x67\x2A\x07\x04"
OBJ_setCext_setExt="\x67\x2A\x07\x05"
OBJ_setCext_setQualf="\x67\x2A\x07\x06"
OBJ_setCext_PGWYcapabilities="\x67\x2A\x07\x07"
OBJ_setCext_TokenIdentifier="\x67\x2A\x07\x08"
OBJ_setCext_Track2Data="\x67\x2A\x07\x09"
OBJ_setCext_TokenType="\x67\x2A\x07\x0A"
OBJ_setCext_IssuerCapabilities="\x67\x2A\x07\x0B"
OBJ_setAttr_Cert="\x67\x2A\x03\x00"
OBJ_setAttr_PGWYcap="\x67\x2A\x03\x01"
OBJ_setAttr_TokenType="\x67\x2A\x03\x02"
OBJ_setAttr_IssCap="\x67\x2A\x03\x03"
OBJ_set_rootKeyThumb="\x67\x2A\x03\x00\x00"
OBJ_set_addPolicy="\x67\x2A\x03\x00\x01"
OBJ_setAttr_Token_EMV="\x67\x2A\x03\x02\x01"
OBJ_setAttr_Token_B0Prime="\x67\x2A\x03\x02\x02"
OBJ_setAttr_IssCap_CVM="\x67\x2A\x03\x03\x03"
OBJ_setAttr_IssCap_T2="\x67\x2A\x03\x03\x04"
OBJ_setAttr_IssCap_Sig="\x67\x2A\x03\x03\x05"
OBJ_setAttr_GenCryptgrm="\x67\x2A\x03\x03\x03\x01"
OBJ_setAttr_T2Enc="\x67\x2A\x03\x03\x04\x01"
OBJ_setAttr_T2cleartxt="\x67\x2A\x03\x03\x04\x02"
OBJ_setAttr_TokICCsig="\x67\x2A\x03\x03\x05\x01"
OBJ_setAttr_SecDevSig="\x67\x2A\x03\x03\x05\x02"
OBJ_set_brand_IATA_ATA="\x67\x2A\x08\x01"
OBJ_set_brand_Diners="\x67\x2A\x08\x1E"
OBJ_set_brand_AmericanExpress="\x67\x2A\x08\x22"
OBJ_set_brand_JCB="\x67\x2A\x08\x23"
OBJ_set_brand_Visa="\x67\x2A\x08\x04"
OBJ_set_brand_MasterCard="\x67\x2A\x08\x05"
OBJ_set_brand_Novus="\x67\x2A\x08\xAE\x7B"
OBJ_des_cdmf="\x2A\x86\x48\x86\xF7\x0D\x03\x0A"
OBJ_rsaOAEPEncryptionSET="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x06"
OBJ_international_organizations="\x67"
OBJ_ms_smartcard_login="\x2B\x06\x01\x04\x01\x82\x37\x14\x02\x02"
OBJ_ms_upn="\x2B\x06\x01\x04\x01\x82\x37\x14\x02\x03"
OBJ_streetAddress="\x55\x04\x09"
OBJ_postalCode="\x55\x04\x11"
OBJ_id_ppl="\x2B\x06\x01\x05\x05\x07\x15"
OBJ_proxyCertInfo="\x2B\x06\x01\x05\x05\x07\x01\x0E"
OBJ_id_ppl_anyLanguage="\x2B\x06\x01\x05\x05\x07\x15\x00"
OBJ_id_ppl_inheritAll="\x2B\x06\x01\x05\x05\x07\x15\x01"
OBJ_name_constraints="\x55\x1D\x1E"
OBJ_Independent="\x2B\x06\x01\x05\x05\x07\x15\x02"
OBJ_sha256WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0B"
OBJ_sha384WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0C"
OBJ_sha512WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0D"
OBJ_sha224WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0E"
OBJ_sha256="\x60\x86\x48\x01\x65\x03\x04\x02\x01"
OBJ_sha384="\x60\x86\x48\x01\x65\x03\x04\x02\x02"
OBJ_sha512="\x60\x86\x48\x01\x65\x03\x04\x02\x03"
OBJ_sha224="\x60\x86\x48\x01\x65\x03\x04\x02\x04"
OBJ_identified_organization="\x2B"
OBJ_certicom_arc="\x2B\x81\x04"
OBJ_wap="\x67\x2B"
OBJ_wap_wsg="\x67\x2B\x01"
OBJ_X9_62_id_characteristic_two_basis="\x2A\x86\x48\xCE\x3D\x01\x02\x03"
OBJ_X9_62_onBasis="\x2A\x86\x48\xCE\x3D\x01\x02\x03\x01"
OBJ_X9_62_tpBasis="\x2A\x86\x48\xCE\x3D\x01\x02\x03\x02"
OBJ_X9_62_ppBasis="\x2A\x86\x48\xCE\x3D\x01\x02\x03\x03"
OBJ_X9_62_c2pnb163v1="\x2A\x86\x48\xCE\x3D\x03\x00\x01"
OBJ_X9_62_c2pnb163v2="\x2A\x86\x48\xCE\x3D\x03\x00\x02"
OBJ_X9_62_c2pnb163v3="\x2A\x86\x48\xCE\x3D\x03\x00\x03"
OBJ_X9_62_c2pnb176v1="\x2A\x86\x48\xCE\x3D\x03\x00\x04"
OBJ_X9_62_c2tnb191v1="\x2A\x86\x48\xCE\x3D\x03\x00\x05"
OBJ_X9_62_c2tnb191v2="\x2A\x86\x48\xCE\x3D\x03\x00\x06"
OBJ_X9_62_c2tnb191v3="\x2A\x86\x48\xCE\x3D\x03\x00\x07"
OBJ_X9_62_c2onb191v4="\x2A\x86\x48\xCE\x3D\x03\x00\x08"
OBJ_X9_62_c2onb191v5="\x2A\x86\x48\xCE\x3D\x03\x00\x09"
OBJ_X9_62_c2pnb208w1="\x2A\x86\x48\xCE\x3D\x03\x00\x0A"
OBJ_X9_62_c2tnb239v1="\x2A\x86\x48\xCE\x3D\x03\x00\x0B"
OBJ_X9_62_c2tnb239v2="\x2A\x86\x48\xCE\x3D\x03\x00\x0C"
OBJ_X9_62_c2tnb239v3="\x2A\x86\x48\xCE\x3D\x03\x00\x0D"
OBJ_X9_62_c2onb239v4="\x2A\x86\x48\xCE\x3D\x03\x00\x0E"
OBJ_X9_62_c2onb239v5="\x2A\x86\x48\xCE\x3D\x03\x00\x0F"
OBJ_X9_62_c2pnb272w1="\x2A\x86\x48\xCE\x3D\x03\x00\x10"
OBJ_X9_62_c2pnb304w1="\x2A\x86\x48\xCE\x3D\x03\x00\x11"
OBJ_X9_62_c2tnb359v1="\x2A\x86\x48\xCE\x3D\x03\x00\x12"
OBJ_X9_62_c2pnb368w1="\x2A\x86\x48\xCE\x3D\x03\x00\x13"
OBJ_X9_62_c2tnb431r1="\x2A\x86\x48\xCE\x3D\x03\x00\x14"
OBJ_secp112r1="\x2B\x81\x04\x00\x06"
OBJ_secp112r2="\x2B\x81\x04\x00\x07"
OBJ_secp128r1="\x2B\x81\x04\x00\x1C"
OBJ_secp128r2="\x2B\x81\x04\x00\x1D"
OBJ_secp160k1="\x2B\x81\x04\x00\x09"
OBJ_secp160r1="\x2B\x81\x04\x00\x08"
OBJ_secp160r2="\x2B\x81\x04\x00\x1E"
OBJ_secp192k1="\x2B\x81\x04\x00\x1F"
OBJ_secp224k1="\x2B\x81\x04\x00\x20"
OBJ_secp224r1="\x2B\x81\x04\x00\x21"
OBJ_secp256k1="\x2B\x81\x04\x00\x0A"
OBJ_secp384r1="\x2B\x81\x04\x00\x22"
OBJ_secp521r1="\x2B\x81\x04\x00\x23"
OBJ_sect113r1="\x2B\x81\x04\x00\x04"
OBJ_sect113r2="\x2B\x81\x04\x00\x05"
OBJ_sect131r1="\x2B\x81\x04\x00\x16"
OBJ_sect131r2="\x2B\x81\x04\x00\x17"
OBJ_sect163k1="\x2B\x81\x04\x00\x01"
OBJ_sect163r1="\x2B\x81\x04\x00\x02"
OBJ_sect163r2="\x2B\x81\x04\x00\x0F"
OBJ_sect193r1="\x2B\x81\x04\x00\x18"
OBJ_sect193r2="\x2B\x81\x04\x00\x19"
OBJ_sect233k1="\x2B\x81\x04\x00\x1A"
OBJ_sect233r1="\x2B\x81\x04\x00\x1B"
OBJ_sect239k1="\x2B\x81\x04\x00\x03"
OBJ_sect283k1="\x2B\x81\x04\x00\x10"
OBJ_sect283r1="\x2B\x81\x04\x00\x11"
OBJ_sect409k1="\x2B\x81\x04\x00\x24"
OBJ_sect409r1="\x2B\x81\x04\x00\x25"
OBJ_sect571k1="\x2B\x81\x04\x00\x26"
OBJ_sect571r1="\x2B\x81\x04\x00\x27"
OBJ_wap_wsg_idm_ecid_wtls1="\x67\x2B\x01\x04\x01"
OBJ_wap_wsg_idm_ecid_wtls3="\x67\x2B\x01\x04\x03"
OBJ_wap_wsg_idm_ecid_wtls4="\x67\x2B\x01\x04\x04"
OBJ_wap_wsg_idm_ecid_wtls5="\x67\x2B\x01\x04\x05"
OBJ_wap_wsg_idm_ecid_wtls6="\x67\x2B\x01\x04\x06"
OBJ_wap_wsg_idm_ecid_wtls7="\x67\x2B\x01\x04\x07"
OBJ_wap_wsg_idm_ecid_wtls8="\x67\x2B\x01\x04\x08"
OBJ_wap_wsg_idm_ecid_wtls9="\x67\x2B\x01\x04\x09"
OBJ_wap_wsg_idm_ecid_wtls10="\x67\x2B\x01\x04\x0A"
OBJ_wap_wsg_idm_ecid_wtls11="\x67\x2B\x01\x04\x0B"
OBJ_wap_wsg_idm_ecid_wtls12="\x67\x2B\x01\x04\x0C"
OBJ_any_policy="\x55\x1D\x20\x00"
OBJ_policy_mappings="\x55\x1D\x21"
OBJ_inhibit_any_policy="\x55\x1D\x36"
OBJ_camellia_128_cbc="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x01\x02"
OBJ_camellia_192_cbc="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x01\x03"
OBJ_camellia_256_cbc="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x01\x04"
OBJ_camellia_128_ecb="\x03\xA2\x31\x05\x03\x01\x09\x01"
OBJ_camellia_192_ecb="\x03\xA2\x31\x05\x03\x01\x09\x15"
OBJ_camellia_256_ecb="\x03\xA2\x31\x05\x03\x01\x09\x29"
OBJ_camellia_128_cfb128="\x03\xA2\x31\x05\x03\x01\x09\x04"
OBJ_camellia_192_cfb128="\x03\xA2\x31\x05\x03\x01\x09\x18"
OBJ_camellia_256_cfb128="\x03\xA2\x31\x05\x03\x01\x09\x2C"
OBJ_camellia_128_ofb128="\x03\xA2\x31\x05\x03\x01\x09\x03"
OBJ_camellia_192_ofb128="\x03\xA2\x31\x05\x03\x01\x09\x17"
OBJ_camellia_256_ofb128="\x03\xA2\x31\x05\x03\x01\x09\x2B"
OBJ_subject_directory_attributes="\x55\x1D\x09"
OBJ_issuing_distribution_point="\x55\x1D\x1C"
OBJ_certificate_issuer="\x55\x1D\x1D"
OBJ_kisa="\x2A\x83\x1A\x8C\x9A\x44"
OBJ_seed_ecb="\x2A\x83\x1A\x8C\x9A\x44\x01\x03"
OBJ_seed_cbc="\x2A\x83\x1A\x8C\x9A\x44\x01\x04"
OBJ_seed_ofb128="\x2A\x83\x1A\x8C\x9A\x44\x01\x06"
OBJ_seed_cfb128="\x2A\x83\x1A\x8C\x9A\x44\x01\x05"
OBJ_hmac_md5="\x2B\x06\x01\x05\x05\x08\x01\x01"
OBJ_hmac_sha1="\x2B\x06\x01\x05\x05\x08\x01\x02"
OBJ_id_PasswordBasedMAC="\x2A\x86\x48\x86\xF6\x7D\x07\x42\x0D"
OBJ_id_DHBasedMac="\x2A\x86\x48\x86\xF6\x7D\x07\x42\x1E"
OBJ_id_it_suppLangTags="\x2B\x06\x01\x05\x05\x07\x04\x10"
OBJ_caRepository="\x2B\x06\x01\x05\x05\x07\x30\x05"
OBJ_id_smime_ct_compressedData="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x09"
OBJ_id_ct_asciiTextWithCRLF="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x1B"
OBJ_id_aes128_wrap="\x60\x86\x48\x01\x65\x03\x04\x01\x05"
OBJ_id_aes192_wrap="\x60\x86\x48\x01\x65\x03\x04\x01\x19"
OBJ_id_aes256_wrap="\x60\x86\x48\x01\x65\x03\x04\x01\x2D"
OBJ_ecdsa_with_Recommended="\x2A\x86\x48\xCE\x3D\x04\x02"
OBJ_ecdsa_with_Specified="\x2A\x86\x48\xCE\x3D\x04\x03"
OBJ_ecdsa_with_SHA224="\x2A\x86\x48\xCE\x3D\x04\x03\x01"
OBJ_ecdsa_with_SHA256="\x2A\x86\x48\xCE\x3D\x04\x03\x02"
OBJ_ecdsa_with_SHA384="\x2A\x86\x48\xCE\x3D\x04\x03\x03"
OBJ_ecdsa_with_SHA512="\x2A\x86\x48\xCE\x3D\x04\x03\x04"
OBJ_hmacWithMD5="\x2A\x86\x48\x86\xF7\x0D\x02\x06"
OBJ_hmacWithSHA224="\x2A\x86\x48\x86\xF7\x0D\x02\x08"
OBJ_hmacWithSHA256="\x2A\x86\x48\x86\xF7\x0D\x02\x09"
OBJ_hmacWithSHA384="\x2A\x86\x48\x86\xF7\x0D\x02\x0A"
OBJ_hmacWithSHA512="\x2A\x86\x48\x86\xF7\x0D\x02\x0B"
OBJ_dsa_with_SHA224="\x60\x86\x48\x01\x65\x03\x04\x03\x01"
OBJ_dsa_with_SHA256="\x60\x86\x48\x01\x65\x03\x04\x03\x02"
OBJ_whirlpool="\x28\xCF\x06\x03\x00\x37"
OBJ_cryptopro="\x2A\x85\x03\x02\x02"
OBJ_cryptocom="\x2A\x85\x03\x02\x09"
OBJ_id_GostR3411_94_with_GostR3410_2001="\x2A\x85\x03\x02\x02\x03"
OBJ_id_GostR3411_94_with_GostR3410_94="\x2A\x85\x03\x02\x02\x04"
OBJ_id_GostR3411_94="\x2A\x85\x03\x02\x02\x09"
OBJ_id_HMACGostR3411_94="\x2A\x85\x03\x02\x02\x0A"
OBJ_id_GostR3410_2001="\x2A\x85\x03\x02\x02\x13"
OBJ_id_GostR3410_94="\x2A\x85\x03\x02\x02\x14"
OBJ_id_Gost28147_89="\x2A\x85\x03\x02\x02\x15"
OBJ_id_Gost28147_89_MAC="\x2A\x85\x03\x02\x02\x16"
OBJ_id_GostR3411_94_prf="\x2A\x85\x03\x02\x02\x17"
OBJ_id_GostR3410_2001DH="\x2A\x85\x03\x02\x02\x62"
OBJ_id_GostR3410_94DH="\x2A\x85\x03\x02\x02\x63"
OBJ_id_Gost28147_89_CryptoPro_KeyMeshing="\x2A\x85\x03\x02\x02\x0E\x01"
OBJ_id_Gost28147_89_None_KeyMeshing="\x2A\x85\x03\x02\x02\x0E\x00"
OBJ_id_GostR3411_94_TestParamSet="\x2A\x85\x03\x02\x02\x1E\x00"
OBJ_id_GostR3411_94_CryptoProParamSet="\x2A\x85\x03\x02\x02\x1E\x01"
OBJ_id_Gost28147_89_TestParamSet="\x2A\x85\x03\x02\x02\x1F\x00"
OBJ_id_Gost28147_89_CryptoPro_A_ParamSet="\x2A\x85\x03\x02\x02\x1F\x01"
OBJ_id_Gost28147_89_CryptoPro_B_ParamSet="\x2A\x85\x03\x02\x02\x1F\x02"
OBJ_id_Gost28147_89_CryptoPro_C_ParamSet="\x2A\x85\x03\x02\x02\x1F\x03"
OBJ_id_Gost28147_89_CryptoPro_D_ParamSet="\x2A\x85\x03\x02\x02\x1F\x04"
OBJ_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet="\x2A\x85\x03\x02\x02\x1F\x05"
OBJ_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet="\x2A\x85\x03\x02\x02\x1F\x06"
OBJ_id_Gost28147_89_CryptoPro_RIC_1_ParamSet="\x2A\x85\x03\x02\x02\x1F\x07"
OBJ_id_GostR3410_94_TestParamSet="\x2A\x85\x03\x02\x02\x20\x00"
OBJ_id_GostR3410_94_CryptoPro_A_ParamSet="\x2A\x85\x03\x02\x02\x20\x02"
OBJ_id_GostR3410_94_CryptoPro_B_ParamSet="\x2A\x85\x03\x02\x02\x20\x03"
OBJ_id_GostR3410_94_CryptoPro_C_ParamSet="\x2A\x85\x03\x02\x02\x20\x04"
OBJ_id_GostR3410_94_CryptoPro_D_ParamSet="\x2A\x85\x03\x02\x02\x20\x05"
OBJ_id_GostR3410_94_CryptoPro_XchA_ParamSet="\x2A\x85\x03\x02\x02\x21\x01"
OBJ_id_GostR3410_94_CryptoPro_XchB_ParamSet="\x2A\x85\x03\x02\x02\x21\x02"
OBJ_id_GostR3410_94_CryptoPro_XchC_ParamSet="\x2A\x85\x03\x02\x02\x21\x03"
OBJ_id_GostR3410_2001_TestParamSet="\x2A\x85\x03\x02\x02\x23\x00"
OBJ_id_GostR3410_2001_CryptoPro_A_ParamSet="\x2A\x85\x03\x02\x02\x23\x01"
OBJ_id_GostR3410_2001_CryptoPro_B_ParamSet="\x2A\x85\x03\x02\x02\x23\x02"
OBJ_id_GostR3410_2001_CryptoPro_C_ParamSet="\x2A\x85\x03\x02\x02\x23\x03"
OBJ_id_GostR3410_2001_CryptoPro_XchA_ParamSet="\x2A\x85\x03\x02\x02\x24\x00"
OBJ_id_GostR3410_2001_CryptoPro_XchB_ParamSet="\x2A\x85\x03\x02\x02\x24\x01"
OBJ_id_GostR3410_94_a="\x2A\x85\x03\x02\x02\x14\x01"
OBJ_id_GostR3410_94_aBis="\x2A\x85\x03\x02\x02\x14\x02"
OBJ_id_GostR3410_94_b="\x2A\x85\x03\x02\x02\x14\x03"
OBJ_id_GostR3410_94_bBis="\x2A\x85\x03\x02\x02\x14\x04"
OBJ_id_Gost28147_89_cc="\x2A\x85\x03\x02\x09\x01\x06\x01"
OBJ_id_GostR3410_94_cc="\x2A\x85\x03\x02\x09\x01\x05\x03"
OBJ_id_GostR3410_2001_cc="\x2A\x85\x03\x02\x09\x01\x05\x04"
OBJ_id_GostR3411_94_with_GostR3410_94_cc="\x2A\x85\x03\x02\x09\x01\x03\x03"
OBJ_id_GostR3411_94_with_GostR3410_2001_cc="\x2A\x85\x03\x02\x09\x01\x03\x04"
OBJ_id_GostR3410_2001_ParamSet_cc="\x2A\x85\x03\x02\x09\x01\x08\x01"
OBJ_LocalKeySet="\x2B\x06\x01\x04\x01\x82\x37\x11\x02"
OBJ_freshest_crl="\x55\x1D\x2E"
OBJ_id_on_permanentIdentifier="\x2B\x06\x01\x05\x05\x07\x08\x03"
OBJ_searchGuide="\x55\x04\x0E"
OBJ_businessCategory="\x55\x04\x0F"
OBJ_postalAddress="\x55\x04\x10"
OBJ_postOfficeBox="\x55\x04\x12"
OBJ_physicalDeliveryOfficeName="\x55\x04\x13"
OBJ_telephoneNumber="\x55\x04\x14"
OBJ_telexNumber="\x55\x04\x15"
OBJ_teletexTerminalIdentifier="\x55\x04\x16"
OBJ_facsimileTelephoneNumber="\x55\x04\x17"
OBJ_x121Address="\x55\x04\x18"
OBJ_internationaliSDNNumber="\x55\x04\x19"
OBJ_registeredAddress="\x55\x04\x1A"
OBJ_destinationIndicator="\x55\x04\x1B"
OBJ_preferredDeliveryMethod="\x55\x04\x1C"
OBJ_presentationAddress="\x55\x04\x1D"
OBJ_supportedApplicationContext="\x55\x04\x1E"
OBJ_member="\x55\x04\x1F"
OBJ_owner="\x55\x04\x20"
OBJ_roleOccupant="\x55\x04\x21"
OBJ_seeAlso="\x55\x04\x22"
OBJ_userPassword="\x55\x04\x23"
OBJ_userCertificate="\x55\x04\x24"
OBJ_cACertificate="\x55\x04\x25"
OBJ_authorityRevocationList="\x55\x04\x26"
OBJ_certificateRevocationList="\x55\x04\x27"
OBJ_crossCertificatePair="\x55\x04\x28"
OBJ_enhancedSearchGuide="\x55\x04\x2F"
OBJ_protocolInformation="\x55\x04\x30"
OBJ_distinguishedName="\x55\x04\x31"
OBJ_uniqueMember="\x55\x04\x32"
OBJ_houseIdentifier="\x55\x04\x33"
OBJ_supportedAlgorithms="\x55\x04\x34"
OBJ_deltaRevocationList="\x55\x04\x35"
OBJ_dmdName="\x55\x04\x36"
OBJ_id_alg_PWRI_KEK="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x03\x09"
OBJ_aes_128_gcm="\x60\x86\x48\x01\x65\x03\x04\x01\x06"
OBJ_aes_128_ccm="\x60\x86\x48\x01\x65\x03\x04\x01\x07"
OBJ_id_aes128_wrap_pad="\x60\x86\x48\x01\x65\x03\x04\x01\x08"
OBJ_aes_192_gcm="\x60\x86\x48\x01\x65\x03\x04\x01\x1A"
OBJ_aes_192_ccm="\x60\x86\x48\x01\x65\x03\x04\x01\x1B"
OBJ_id_aes192_wrap_pad="\x60\x86\x48\x01\x65\x03\x04\x01\x1C"
OBJ_aes_256_gcm="\x60\x86\x48\x01\x65\x03\x04\x01\x2E"
OBJ_aes_256_ccm="\x60\x86\x48\x01\x65\x03\x04\x01\x2F"
OBJ_id_aes256_wrap_pad="\x60\x86\x48\x01\x65\x03\x04\x01\x30"
OBJ_id_camellia128_wrap="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x03\x02"
OBJ_id_camellia192_wrap="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x03\x03"
OBJ_id_camellia256_wrap="\x2A\x83\x08\x8C\x9A\x4B\x3D\x01\x01\x03\x04"
OBJ_anyExtendedKeyUsage="\x55\x1D\x25\x00"
OBJ_mgf1="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x08"
OBJ_rsassaPss="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0A"
OBJ_aes_128_xts="\x2B\x6F\x02\x8C\x53\x00\x01\x01"
OBJ_aes_256_xts="\x2B\x6F\x02\x8C\x53\x00\x01\x02"
OBJ_rsaesOaep="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x07"
OBJ_dhpublicnumber="\x2A\x86\x48\xCE\x3E\x02\x01"
OBJ_brainpoolP160r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x01"
OBJ_brainpoolP160t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x02"
OBJ_brainpoolP192r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x03"
OBJ_brainpoolP192t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x04"
OBJ_brainpoolP224r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x05"
OBJ_brainpoolP224t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x06"
OBJ_brainpoolP256r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x07"
OBJ_brainpoolP256t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x08"
OBJ_brainpoolP320r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x09"
OBJ_brainpoolP320t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x0A"
OBJ_brainpoolP384r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x0B"
OBJ_brainpoolP384t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x0C"
OBJ_brainpoolP512r1="\x2B\x24\x03\x03\x02\x08\x01\x01\x0D"
OBJ_brainpoolP512t1="\x2B\x24\x03\x03\x02\x08\x01\x01\x0E"
OBJ_pSpecified="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x09"
OBJ_dhSinglePass_stdDH_sha1kdf_scheme="\x2B\x81\x05\x10\x86\x48\x3F\x00\x02"
OBJ_dhSinglePass_stdDH_sha224kdf_scheme="\x2B\x81\x04\x01\x0B\x00"
OBJ_dhSinglePass_stdDH_sha256kdf_scheme="\x2B\x81\x04\x01\x0B\x01"
OBJ_dhSinglePass_stdDH_sha384kdf_scheme="\x2B\x81\x04\x01\x0B\x02"
OBJ_dhSinglePass_stdDH_sha512kdf_scheme="\x2B\x81\x04\x01\x0B\x03"
OBJ_dhSinglePass_cofactorDH_sha1kdf_scheme="\x2B\x81\x05\x10\x86\x48\x3F\x00\x03"
OBJ_dhSinglePass_cofactorDH_sha224kdf_scheme="\x2B\x81\x04\x01\x0E\x00"
OBJ_dhSinglePass_cofactorDH_sha256kdf_scheme="\x2B\x81\x04\x01\x0E\x01"
OBJ_dhSinglePass_cofactorDH_sha384kdf_scheme="\x2B\x81\x04\x01\x0E\x02"
OBJ_dhSinglePass_cofactorDH_sha512kdf_scheme="\x2B\x81\x04\x01\x0E\x03"
OBJ_ct_precert_scts="\x2B\x06\x01\x04\x01\xD6\x79\x02\x04\x02"
OBJ_ct_precert_poison="\x2B\x06\x01\x04\x01\xD6\x79\x02\x04\x03"
OBJ_ct_precert_signer="\x2B\x06\x01\x04\x01\xD6\x79\x02\x04\x04"
OBJ_ct_cert_scts="\x2B\x06\x01\x04\x01\xD6\x79\x02\x04\x05"
OBJ_jurisdictionLocalityName="\x2B\x06\x01\x04\x01\x82\x37\x3C\x02\x01\x01"
OBJ_jurisdictionStateOrProvinceName="\x2B\x06\x01\x04\x01\x82\x37\x3C\x02\x01\x02"
OBJ_jurisdictionCountryName="\x2B\x06\x01\x04\x01\x82\x37\x3C\x02\x01\x03"
OBJ_camellia_128_gcm="\x03\xA2\x31\x05\x03\x01\x09\x06"
OBJ_camellia_128_ccm="\x03\xA2\x31\x05\x03\x01\x09\x07"
OBJ_camellia_128_ctr="\x03\xA2\x31\x05\x03\x01\x09\x09"
OBJ_camellia_128_cmac="\x03\xA2\x31\x05\x03\x01\x09\x0A"
OBJ_camellia_192_gcm="\x03\xA2\x31\x05\x03\x01\x09\x1A"
OBJ_camellia_192_ccm="\x03\xA2\x31\x05\x03\x01\x09\x1B"
OBJ_camellia_192_ctr="\x03\xA2\x31\x05\x03\x01\x09\x1D"
OBJ_camellia_192_cmac="\x03\xA2\x31\x05\x03\x01\x09\x1E"
OBJ_camellia_256_gcm="\x03\xA2\x31\x05\x03\x01\x09\x2E"
OBJ_camellia_256_ccm="\x03\xA2\x31\x05\x03\x01\x09\x2F"
OBJ_camellia_256_ctr="\x03\xA2\x31\x05\x03\x01\x09\x31"
OBJ_camellia_256_cmac="\x03\xA2\x31\x05\x03\x01\x09\x32"
OBJ_id_scrypt="\x2B\x06\x01\x04\x01\xDA\x47\x04\x0B"
OBJ_id_tc26="\x2A\x85\x03\x07\x01"
OBJ_id_tc26_algorithms="\x2A\x85\x03\x07\x01\x01"
OBJ_id_tc26_sign="\x2A\x85\x03\x07\x01\x01\x01"
OBJ_id_GostR3410_2012_256="\x2A\x85\x03\x07\x01\x01\x01\x01"
OBJ_id_GostR3410_2012_512="\x2A\x85\x03\x07\x01\x01\x01\x02"
OBJ_id_tc26_digest="\x2A\x85\x03\x07\x01\x01\x02"
OBJ_id_GostR3411_2012_256="\x2A\x85\x03\x07\x01\x01\x02\x02"
OBJ_id_GostR3411_2012_512="\x2A\x85\x03\x07\x01\x01\x02\x03"
OBJ_id_tc26_signwithdigest="\x2A\x85\x03\x07\x01\x01\x03"
OBJ_id_tc26_signwithdigest_gost3410_2012_256="\x2A\x85\x03\x07\x01\x01\x03\x02"
OBJ_id_tc26_signwithdigest_gost3410_2012_512="\x2A\x85\x03\x07\x01\x01\x03\x03"
OBJ_id_tc26_mac="\x2A\x85\x03\x07\x01\x01\x04"
OBJ_id_tc26_hmac_gost_3411_2012_256="\x2A\x85\x03\x07\x01\x01\x04\x01"
OBJ_id_tc26_hmac_gost_3411_2012_512="\x2A\x85\x03\x07\x01\x01\x04\x02"
OBJ_id_tc26_cipher="\x2A\x85\x03\x07\x01\x01\x05"
OBJ_id_tc26_agreement="\x2A\x85\x03\x07\x01\x01\x06"
OBJ_id_tc26_agreement_gost_3410_2012_256="\x2A\x85\x03\x07\x01\x01\x06\x01"
OBJ_id_tc26_agreement_gost_3410_2012_512="\x2A\x85\x03\x07\x01\x01\x06\x02"
OBJ_id_tc26_constants="\x2A\x85\x03\x07\x01\x02"
OBJ_id_tc26_sign_constants="\x2A\x85\x03\x07\x01\x02\x01"
OBJ_id_tc26_gost_3410_2012_512_constants="\x2A\x85\x03\x07\x01\x02\x01\x02"
OBJ_id_tc26_gost_3410_2012_512_paramSetTest="\x2A\x85\x03\x07\x01\x02\x01\x02\x00"
OBJ_id_tc26_gost_3410_2012_512_paramSetA="\x2A\x85\x03\x07\x01\x02\x01\x02\x01"
OBJ_id_tc26_gost_3410_2012_512_paramSetB="\x2A\x85\x03\x07\x01\x02\x01\x02\x02"
OBJ_id_tc26_digest_constants="\x2A\x85\x03\x07\x01\x02\x02"
OBJ_id_tc26_cipher_constants="\x2A\x85\x03\x07\x01\x02\x05"
OBJ_id_tc26_gost_28147_constants="\x2A\x85\x03\x07\x01\x02\x05\x01"
OBJ_id_tc26_gost_28147_param_Z="\x2A\x85\x03\x07\x01\x02\x05\x01\x01"
OBJ_INN="\x2A\x85\x03\x03\x81\x03\x01\x01"
OBJ_OGRN="\x2A\x85\x03\x64\x01"
OBJ_SNILS="\x2A\x85\x03\x64\x03"
OBJ_subjectSignTool="\x2A\x85\x03\x64\x6F"
OBJ_issuerSignTool="\x2A\x85\x03\x64\x70"
OBJ_tlsfeature="\x2B\x06\x01\x05\x05\x07\x01\x18"
OBJ_ipsec_IKE="\x2B\x06\x01\x05\x05\x07\x03\x11"
OBJ_capwapAC="\x2B\x06\x01\x05\x05\x07\x03\x12"
OBJ_capwapWTP="\x2B\x06\x01\x05\x05\x07\x03\x13"
OBJ_sshClient="\x2B\x06\x01\x05\x05\x07\x03\x15"
OBJ_sshServer="\x2B\x06\x01\x05\x05\x07\x03\x16"
OBJ_sendRouter="\x2B\x06\x01\x05\x05\x07\x03\x17"
OBJ_sendProxiedRouter="\x2B\x06\x01\x05\x05\x07\x03\x18"
OBJ_sendOwner="\x2B\x06\x01\x05\x05\x07\x03\x19"
OBJ_sendProxiedOwner="\x2B\x06\x01\x05\x05\x07\x03\x1A"
OBJ_id_pkinit="\x2B\x06\x01\x05\x02\x03"
OBJ_pkInitClientAuth="\x2B\x06\x01\x05\x02\x03\x04"
OBJ_pkInitKDC="\x2B\x06\x01\x05\x02\x03\x05"
OBJ_X25519="\x2B\x65\x6E"
OBJ_X448="\x2B\x65\x6F"
OBJ_blake2b512="\x2B\x06\x01\x04\x01\x8D\x3A\x0C\x02\x01\x10"
OBJ_blake2s256="\x2B\x06\x01\x04\x01\x8D\x3A\x0C\x02\x02\x08"
OBJ_id_smime_ct_contentCollection="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x13"
OBJ_id_smime_ct_authEnvelopedData="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x17"
OBJ_id_ct_xml="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x01\x1C"
OBJ_aria_128_ecb="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x01"
OBJ_aria_128_cbc="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x02"
OBJ_aria_128_cfb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x03"
OBJ_aria_128_ofb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x04"
OBJ_aria_128_ctr="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x05"
OBJ_aria_192_ecb="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x06"
OBJ_aria_192_cbc="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x07"
OBJ_aria_192_cfb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x08"
OBJ_aria_192_ofb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x09"
OBJ_aria_192_ctr="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0A"
OBJ_aria_256_ecb="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0B"
OBJ_aria_256_cbc="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0C"
OBJ_aria_256_cfb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0D"
OBJ_aria_256_ofb128="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0E"
OBJ_aria_256_ctr="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x0F"
OBJ_id_smime_aa_signingCertificateV2="\x2A\x86\x48\x86\xF7\x0D\x01\x09\x10\x02\x2F"
OBJ_ED25519="\x2B\x65\x70"
OBJ_ED448="\x2B\x65\x71"
OBJ_organizationIdentifier="\x55\x04\x61"
OBJ_countryCode3c="\x55\x04\x62"
OBJ_countryCode3n="\x55\x04\x63"
OBJ_dnsName="\x55\x04\x64"
OBJ_x509ExtAdmission="\x2B\x24\x08\x03\x03"
OBJ_sha512_224="\x60\x86\x48\x01\x65\x03\x04\x02\x05"
OBJ_sha512_256="\x60\x86\x48\x01\x65\x03\x04\x02\x06"
OBJ_sha3_224="\x60\x86\x48\x01\x65\x03\x04\x02\x07"
OBJ_sha3_256="\x60\x86\x48\x01\x65\x03\x04\x02\x08"
OBJ_sha3_384="\x60\x86\x48\x01\x65\x03\x04\x02\x09"
OBJ_sha3_512="\x60\x86\x48\x01\x65\x03\x04\x02\x0A"
OBJ_shake128="\x60\x86\x48\x01\x65\x03\x04\x02\x0B"
OBJ_shake256="\x60\x86\x48\x01\x65\x03\x04\x02\x0C"
OBJ_hmac_sha3_224="\x60\x86\x48\x01\x65\x03\x04\x02\x0D"
OBJ_hmac_sha3_256="\x60\x86\x48\x01\x65\x03\x04\x02\x0E"
OBJ_hmac_sha3_384="\x60\x86\x48\x01\x65\x03\x04\x02\x0F"
OBJ_hmac_sha3_512="\x60\x86\x48\x01\x65\x03\x04\x02\x10"
OBJ_dsa_with_SHA384="\x60\x86\x48\x01\x65\x03\x04\x03\x03"
OBJ_dsa_with_SHA512="\x60\x86\x48\x01\x65\x03\x04\x03\x04"
OBJ_dsa_with_SHA3_224="\x60\x86\x48\x01\x65\x03\x04\x03\x05"
OBJ_dsa_with_SHA3_256="\x60\x86\x48\x01\x65\x03\x04\x03\x06"
OBJ_dsa_with_SHA3_384="\x60\x86\x48\x01\x65\x03\x04\x03\x07"
OBJ_dsa_with_SHA3_512="\x60\x86\x48\x01\x65\x03\x04\x03\x08"
OBJ_ecdsa_with_SHA3_224="\x60\x86\x48\x01\x65\x03\x04\x03\x09"
OBJ_ecdsa_with_SHA3_256="\x60\x86\x48\x01\x65\x03\x04\x03\x0A"
OBJ_ecdsa_with_SHA3_384="\x60\x86\x48\x01\x65\x03\x04\x03\x0B"
OBJ_ecdsa_with_SHA3_512="\x60\x86\x48\x01\x65\x03\x04\x03\x0C"
OBJ_RSA_SHA3_224="\x60\x86\x48\x01\x65\x03\x04\x03\x0D"
OBJ_RSA_SHA3_256="\x60\x86\x48\x01\x65\x03\x04\x03\x0E"
OBJ_RSA_SHA3_384="\x60\x86\x48\x01\x65\x03\x04\x03\x0F"
OBJ_RSA_SHA3_512="\x60\x86\x48\x01\x65\x03\x04\x03\x10"
OBJ_aria_128_ccm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x25"
OBJ_aria_192_ccm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x26"
OBJ_aria_256_ccm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x27"
OBJ_aria_128_gcm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x22"
OBJ_aria_192_gcm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x23"
OBJ_aria_256_gcm="\x2A\x83\x1A\x8C\x9A\x6E\x01\x01\x24"
OBJ_cmcCA="\x2B\x06\x01\x05\x05\x07\x03\x1B"
OBJ_cmcRA="\x2B\x06\x01\x05\x05\x07\x03\x1C"
OBJ_sm4_ecb="\x2A\x81\x1C\xCF\x55\x01\x68\x01"
OBJ_sm4_cbc="\x2A\x81\x1C\xCF\x55\x01\x68\x02"
OBJ_sm4_ofb128="\x2A\x81\x1C\xCF\x55\x01\x68\x03"
OBJ_sm4_cfb1="\x2A\x81\x1C\xCF\x55\x01\x68\x05"
OBJ_sm4_cfb128="\x2A\x81\x1C\xCF\x55\x01\x68\x04"
OBJ_sm4_cfb8="\x2A\x81\x1C\xCF\x55\x01\x68\x06"
OBJ_sm4_ctr="\x2A\x81\x1C\xCF\x55\x01\x68\x07"
OBJ_ISO_CN="\x2A\x81\x1C"
OBJ_oscca="\x2A\x81\x1C\xCF\x55"
OBJ_sm_scheme="\x2A\x81\x1C\xCF\x55\x01"
OBJ_sm3="\x2A\x81\x1C\xCF\x55\x01\x83\x11"
OBJ_sm3WithRSAEncryption="\x2A\x81\x1C\xCF\x55\x01\x83\x78"
OBJ_sha512_224WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x0F"
OBJ_sha512_256WithRSAEncryption="\x2A\x86\x48\x86\xF7\x0D\x01\x01\x10"
OBJ_id_tc26_gost_3410_2012_256_constants="\x2A\x85\x03\x07\x01\x02\x01\x01"
OBJ_id_tc26_gost_3410_2012_256_paramSetA="\x2A\x85\x03\x07\x01\x02\x01\x01\x01"
OBJ_id_tc26_gost_3410_2012_512_paramSetC="\x2A\x85\x03\x07\x01\x02\x01\x02\x03"
OBJ_ISO_UA="\x2A\x86\x24"
OBJ_ua_pki="\x2A\x86\x24\x02\x01\x01\x01"
OBJ_dstu28147="\x2A\x86\x24\x02\x01\x01\x01\x01\x01\x01"
OBJ_dstu28147_ofb="\x2A\x86\x24\x02\x01\x01\x01\x01\x01\x01\x02"
OBJ_dstu28147_cfb="\x2A\x86\x24\x02\x01\x01\x01\x01\x01\x01\x03"
OBJ_dstu28147_wrap="\x2A\x86\x24\x02\x01\x01\x01\x01\x01\x01\x05"
OBJ_hmacWithDstu34311="\x2A\x86\x24\x02\x01\x01\x01\x01\x01\x02"
OBJ_dstu34311="\x2A\x86\x24\x02\x01\x01\x01\x01\x02\x01"
OBJ_dstu4145le="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01"
OBJ_dstu4145be="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x01\x01"
OBJ_uacurve0="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x00"
OBJ_uacurve1="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x01"
OBJ_uacurve2="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x02"
OBJ_uacurve3="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x03"
OBJ_uacurve4="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x04"
OBJ_uacurve5="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x05"
OBJ_uacurve6="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x06"
OBJ_uacurve7="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x07"
OBJ_uacurve8="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x08"
OBJ_uacurve9="\x2A\x86\x24\x02\x01\x01\x01\x01\x03\x01\x01\x02\x09"
OBJ_ieee="\x2B\x6F"
OBJ_ieee_siswg="\x2B\x6F\x02\x8C\x53"
OBJ_sm2="\x2A\x81\x1C\xCF\x55\x01\x82\x2D"
OBJ_id_tc26_cipher_gostr3412_2015_magma="\x2A\x85\x03\x07\x01\x01\x05\x01"
OBJ_id_tc26_cipher_gostr3412_2015_magma_ctracpkm="\x2A\x85\x03\x07\x01\x01\x05\x01\x01"
OBJ_id_tc26_cipher_gostr3412_2015_magma_ctracpkm_omac="\x2A\x85\x03\x07\x01\x01\x05\x01\x02"
OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik="\x2A\x85\x03\x07\x01\x01\x05\x02"
OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm="\x2A\x85\x03\x07\x01\x01\x05\x02\x01"
OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm_omac="\x2A\x85\x03\x07\x01\x01\x05\x02\x02"
OBJ_id_tc26_wrap="\x2A\x85\x03\x07\x01\x01\x07"
OBJ_id_tc26_wrap_gostr3412_2015_magma="\x2A\x85\x03\x07\x01\x01\x07\x01"
OBJ_id_tc26_wrap_gostr3412_2015_magma_kexp15="\x2A\x85\x03\x07\x01\x01\x07\x01\x01"
OBJ_id_tc26_wrap_gostr3412_2015_kuznyechik="\x2A\x85\x03\x07\x01\x01\x07\x02"
OBJ_id_tc26_wrap_gostr3412_2015_kuznyechik_kexp15="\x2A\x85\x03\x07\x01\x01\x07\x02\x01"
OBJ_id_tc26_gost_3410_2012_256_paramSetB="\x2A\x85\x03\x07\x01\x02\x01\x01\x02"
OBJ_id_tc26_gost_3410_2012_256_paramSetC="\x2A\x85\x03\x07\x01\x02\x01\x01\x03"
OBJ_id_tc26_gost_3410_2012_256_paramSetD="\x2A\x85\x03\x07\x01\x02\x01\x01\x04"
OBJ_hmacWithSHA512_224="\x2A\x86\x48\x86\xF7\x0D\x02\x0C"
OBJ_hmacWithSHA512_256="\x2A\x86\x48\x86\xF7\x0D\x02\x0D"
