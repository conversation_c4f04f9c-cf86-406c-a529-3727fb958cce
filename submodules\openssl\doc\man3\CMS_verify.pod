=pod

=head1 NAME

CMS_verify, CMS_get0_signers - verify a CMS SignedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int CMS_verify(CMS_ContentInfo *cms, STACK_OF(X509) *certs, X509_STORE *store,
                BIO *indata, BIO *out, unsigned int flags);

 STACK_OF(X509) *CMS_get0_signers(CMS_ContentInfo *cms);

=head1 DESCRIPTION

CMS_verify() is very similar to L<PKCS7_verify(3)>. It verifies a
B<CMS SignedData> structure contained in a structure of type B<CMS_ContentInfo>.
I<cms> points to the B<CMS_ContentInfo> structure to verify.
The optional I<certs> parameter refers to a set of certificates
in which to search for signing certificates.
I<cms> may contain extra untrusted CA certificates that may be used for
chain building as well as CRLs that may be used for certificate validation.
I<store> may be NULL or point to
the trusted certificate store to use for chain verification.
I<indata> refers to the signed data if the content is detached from I<cms>.
Otherwise I<indata> should be NULL and the signed data must be in I<cms>.
The content is written to the BIO I<out> unless it is NULL.
I<flags> is an optional set of flags, which can be used to modify the operation.

CMS_get0_signers() retrieves the signing certificate(s) from I<cms>, it may only
be called after a successful CMS_verify() operation.

=head1 VERIFY PROCESS

Normally the verify process proceeds as follows.

Initially some sanity checks are performed on I<cms>. The type of I<cms> must
be SignedData. There must be at least one signature on the data and if
the content is detached I<indata> cannot be NULL.

An attempt is made to locate all the signing certificate(s), first looking in
the I<certs> parameter (if it is not NULL) and then looking in any
certificates contained in the I<cms> structure unless B<CMS_NOINTERN> is set.
If any signing certificate cannot be located the operation fails.

Each signing certificate is chain verified using the I<smimesign> purpose and
using the trusted certificate store I<store> if supplied.
Any internal certificates in the message, which may have been added using
L<CMS_add1_cert(3)>, are used as untrusted CAs.
If CRL checking is enabled in I<store> and B<CMS_NOCRL> is not set,
any internal CRLs, which may have been added using L<CMS_add1_crl(3)>,
are used in addition to attempting to look them up in I<store>.
If I<store> is not NULL and any chain verify fails an error code is returned.

Finally the signed content is read (and written to I<out> unless it is NULL)
and the signature is checked.

If all signatures verify correctly then the function is successful.

Any of the following flags (ored together) can be passed in the I<flags>
parameter to change the default verify behaviour.

If B<CMS_NOINTERN> is set the certificates in the message itself are not
searched when locating the signing certificate(s).
This means that all the signing certificates must be in the I<certs> parameter.

If B<CMS_NOCRL> is set and CRL checking is enabled in I<store> then any
CRLs in the message itself are ignored.

If the B<CMS_TEXT> flag is set MIME headers for type B<text/plain> are deleted
from the content. If the content is not of type B<text/plain> then an error is
returned.

If B<CMS_NO_SIGNER_CERT_VERIFY> is set the signing certificates are not
chain verified.

If B<CMS_NO_ATTR_VERIFY> is set the signed attributes signature is not
verified.

If B<CMS_NO_CONTENT_VERIFY> is set then the content digest is not checked.

=head1 NOTES

One application of B<CMS_NOINTERN> is to only accept messages signed by
a small number of certificates. The acceptable certificates would be passed
in the I<certs> parameter. In this case if the signer certificate is not one
of the certificates supplied in I<certs> then the verify will fail because the
signer cannot be found.

In some cases the standard techniques for looking up and validating
certificates are not appropriate: for example an application may wish to
lookup certificates in a database or perform customised verification. This
can be achieved by setting and verifying the signer certificates manually
using the signed data utility functions.

Care should be taken when modifying the default verify behaviour, for example
setting B<CMS_NO_CONTENT_VERIFY> will totally disable all content verification
and any modified content will be considered valid. This combination is however
useful if one merely wishes to write the content to I<out> and its validity
is not considered important.

Chain verification should arguably be performed using the signing time rather
than the current time. However, since the signing time is supplied by the
signer it cannot be trusted without additional evidence (such as a trusted
timestamp).

=head1 RETURN VALUES

CMS_verify() returns 1 for a successful verification and 0 if an error occurred.

CMS_get0_signers() returns all signers or NULL if an error occurred.

The error can be obtained from L<ERR_get_error(3)>

=head1 BUGS

The trusted certificate store is not searched for the signing certificate.
This is primarily due to the inadequacies of the current B<X509_STORE>
functionality.

The lack of single pass processing means that the signed content must all
be held in memory if it is not detached.

=head1 SEE ALSO

L<PKCS7_verify(3)>, L<CMS_add1_cert(3)>, L<CMS_add1_crl(3)>,
L<OSSL_ESS_check_signing_certs(3)>,
L<ERR_get_error(3)>, L<CMS_sign(3)>

=head1 COPYRIGHT

Copyright 2008-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
