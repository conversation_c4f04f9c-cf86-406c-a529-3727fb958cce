# Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: FIPS Checksums
on: [pull_request]

permissions:
  contents: read

jobs:
  compute-checksums:
    runs-on: ubuntu-latest
    steps:
      - name: install unifdef
        run: |
            sudo apt-get update
            sudo apt-get -yq --no-install-suggests --no-install-recommends --force-yes install unifdef
      - name: create build dirs
        run: |
          mkdir ./build-pristine
          mkdir ./source-pristine
          mkdir ./build
          mkdir ./source
          mkdir ./artifact
      - uses: actions/checkout@v4
        with:
          repository: ${{ github.event.pull_request.base.repo.full_name }}
          ref: ${{ github.event.pull_request.base.ref }}
          path: source-pristine
      - name: config pristine
        run: ../source-pristine/config enable-fips
        working-directory: ./build-pristine
      - name: config pristine dump
        run: ./configdata.pm --dump
        working-directory: ./build-pristine
      - name: make build_generated pristine
        run: make -s build_generated
        working-directory: ./build-pristine
      - name: make fips-checksums pristine
        run: make fips-checksums
        working-directory: ./build-pristine
      - uses: actions/checkout@v4
        with:
          path: source
      - name: config
        run: ../source/config enable-fips
        working-directory: ./build
      - name: config dump
        run: ./configdata.pm --dump
        working-directory: ./build
      - name: make build_generated
        run: make -s build_generated
        working-directory: ./build
      - name: make fips-checksums
        run: make fips-checksums
        working-directory: ./build
      - name: update checksums
        run: |
          cp -a build-pristine/providers/fips.module.sources.new source/providers/fips.module.sources
          cp -a build-pristine/providers/fips-sources.checksums.new source/providers/fips-sources.checksums
          cp -a build-pristine/providers/fips.checksum.new source/providers/fips.checksum
      - name: make diff-fips-checksums
        run: make diff-fips-checksums && touch ../artifact/fips_unchanged || ( touch ../artifact/fips_changed ; echo FIPS CHANGED )
        working-directory: ./build
      - name: save PR number
        run: echo ${{ github.event.number }} > ./artifact/pr_num
      - name: save artifact
        uses: actions/upload-artifact@v3
        with:
          name: fips_checksum
          path: artifact/
  verify-checksums:
    runs-on: ubuntu-latest
    steps:
      - name: install unifdef
        run: |
            sudo apt-get update
            sudo apt-get -yq --no-install-suggests --no-install-recommends --force-yes install unifdef
      - uses: actions/checkout@v2
      - name: create build dirs
        run: |
          mkdir ./build
      - name: config
        run: ../config enable-fips && perl configdata.pm --dump
        working-directory: ./build
      - name: make build_generated
        run: make -s build_generated
        working-directory: ./build
      - name: make fips-checksums
        run: make fips-checksums
        working-directory: ./build
      - name: make fips-checksums
        run: make fips-checksums
        working-directory: ./build
      - name: make diff-fips-checksums
        run: make diff-fips-checksums
        working-directory: ./build
