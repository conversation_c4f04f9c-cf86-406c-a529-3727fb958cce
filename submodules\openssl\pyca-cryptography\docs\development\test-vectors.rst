Test vectors
============

Testing the correctness of the primitives implemented in each ``cryptography``
backend requires trusted test vectors. Where possible these vectors are
obtained from official sources such as `NIST`_ or `IETF`_ RFCs. When this is
not possible ``cryptography`` has chosen to create a set of custom vectors
using an official vector file as input to verify consistency between
implemented backends.

Vectors are kept in the ``cryptography_vectors`` package rather than within our
main test suite.

Sources
-------

Project Wycheproof
~~~~~~~~~~~~~~~~~~

We run vectors from `Project Wycheproof`_ -- a collection of known edge-cases
for various cryptographic algorithms. These are not included in the repository
(or ``cryptography_vectors`` package), but rather cloned from Git in our
continuous integration environments.

We have ensured all test vectors are used as of commit
``2196000605e45d91097147c9c71f26b72af58003``.

Asymmetric ciphers
~~~~~~~~~~~~~~~~~~

* RSA PKCS #1 from the RSA FTP site (ftp://ftp.rsasecurity.com/pub/pkcs/pkcs-1/
  and ftp://ftp.rsa.com/pub/rsalabs/tmp/).
* RSA FIPS 186-2 and PKCS1 v1.5 vulnerability test vectors from `NIST CAVP`_.
* FIPS 186-2 and FIPS 186-3 DSA test vectors from `NIST CAVP`_.
* FIPS 186-2 and FIPS 186-3 ECDSA test vectors from `NIST CAVP`_.
* DH and ECDH and ECDH+KDF(17.4) test vectors from `NIST CAVP`_.
* Ed25519 test vectors from the `Ed25519 website_`.
* OpenSSL PEM RSA serialization vectors from the `OpenSSL example key`_ and
  `GnuTLS key parsing tests`_.
* OpenSSL PEM DSA serialization vectors from the `GnuTLS example keys`_.
* PKCS #8 PEM serialization vectors from

  * GnuTLS: `enc-rsa-pkcs8.pem`_, `enc2-rsa-pkcs8.pem`_,
    `unenc-rsa-pkcs8.pem`_, `pkcs12_s2k_pem.c`_. The encoding error in
    `unenc-rsa-pkcs8.pem`_ was fixed, and the contents of `enc-rsa-pkcs8.pem`_
    was re-encrypted to include it. The contents of `enc2-rsa-pkcs8.pem`_
    was re-encrypted using a stronger PKCS#8 cipher.
  * `Botan's ECC private keys`_.
* `asymmetric/public/PKCS1/dsa.pub.pem`_ is a PKCS1 DSA public key from the
  Ruby test suite.
* X25519 and X448 test vectors from :rfc:`7748`.
* RSA OAEP with custom label from the `BoringSSL evp tests`_.
* Ed448 test vectors from :rfc:`8032`.


Custom asymmetric vectors
~~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
    :maxdepth: 1

    custom-vectors/secp256k1
    custom-vectors/rsa-oaep-sha2

* ``asymmetric/PEM_Serialization/ec_private_key.pem`` and
  ``asymmetric/DER_Serialization/ec_private_key.der`` - Contains an Elliptic
  Curve key generated by OpenSSL from the curve ``secp256r1``.
* ``asymmetric/PEM_Serialization/ec_private_key_encrypted.pem`` and
  ``asymmetric/DER_Serialization/ec_private_key_encrypted.der``- Contains the
  same Elliptic Curve key as ``ec_private_key.pem``, except that it is
  encrypted with AES-128 with the password "123456".
* ``asymmetric/PEM_Serialization/ec_public_key.pem`` and
  ``asymmetric/DER_Serialization/ec_public_key.der``- Contains the public key
  corresponding to ``ec_private_key.pem``, generated using OpenSSL.
* ``asymmetric/PEM_Serialization/rsa_private_key.pem`` - Contains an RSA 2048
  bit key generated using OpenSSL, protected by the secret "123456" with DES3
  encryption.
* ``asymmetric/PEM_Serialization/rsa_public_key.pem`` and
  ``asymmetric/DER_Serialization/rsa_public_key.der``- Contains an RSA 2048
  bit public generated using OpenSSL from ``rsa_private_key.pem``.
* ``asymmetric/PEM_Serialization/dsa_4096.pem`` - Contains a 4096-bit DSA
  private key generated using OpenSSL.
* ``asymmetric/PEM_Serialization/dsaparam.pem`` - Contains 2048-bit DSA
  parameters generated using OpenSSL; contains no keys.
* ``asymmetric/PEM_Serialization/dsa_private_key.pem`` - Contains a DSA 2048
  bit key generated using OpenSSL from the parameters in ``dsaparam.pem``,
  protected by the secret "123456" with DES3 encryption.
* ``asymmetric/PEM_Serialization/dsa_public_key.pem`` and
  ``asymmetric/DER_Serialization/dsa_public_key.der`` - Contains a DSA 2048 bit
  key generated using OpenSSL from ``dsa_private_key.pem``.
* ``asymmetric/DER_Serialization/dsa_public_key_no_params.der`` - Contains a
  DSA public key with the optional parameters removed.
* ``asymmetric/DER_Serialization/dsa_public_key_invalid_bit_string.der`` -
  Contains a DSA public key with the bit string padding value set to 2 rather
  than the required 0.
* ``asymmetric/PKCS8/unenc-dsa-pkcs8.pem`` and
  ``asymmetric/DER_Serialization/unenc-dsa-pkcs8.der`` - Contains a DSA 1024
  bit key generated using OpenSSL.
* ``asymmetric/PKCS8/unenc-dsa-pkcs8.pub.pem`` and
  ``asymmetric/DER_Serialization/unenc-dsa-pkcs8.pub.der`` - Contains a DSA
  2048 bit public key generated using OpenSSL from ``unenc-dsa-pkcs8.pem``.
* DER conversions of the `GnuTLS example keys`_ for DSA as well as the
  `OpenSSL example key`_ for RSA.
* DER conversions of `enc-rsa-pkcs8.pem`_, `enc2-rsa-pkcs8.pem`_, and
  `unenc-rsa-pkcs8.pem`_.
* ``asymmetric/public/PKCS1/rsa.pub.pem`` and
  ``asymmetric/public/PKCS1/rsa.pub.der`` are PKCS1 conversions of the public
  key from ``asymmetric/PKCS8/unenc-rsa-pkcs8.pem`` using PEM and DER encoding.
* ``x509/custom/ca/ca_key.pem`` - An unencrypted PCKS8 ``secp256r1`` key. It is
  the private key for the certificate ``x509/custom/ca/ca.pem``. This key is
  encoded in several of the PKCS12 custom vectors.
* ``x509/custom/ca/rsa_key.pem`` - An unencrypted PCKS8 4096 bit RSA key. It is
  the private key for the certificate ``x509/custom/ca/rsa_ca.pem``.
* ``asymmetric/EC/compressed_points.txt`` - Contains compressed public points
  generated using OpenSSL.
* ``asymmetric/X448/x448-pkcs8-enc.pem`` and
  ``asymmetric/X448/x448-pkcs8-enc.der`` contain an X448 key encrypted with
  AES 256 CBC with the password ``password``.
* ``asymmetric/X448/x448-pkcs8.pem`` and ``asymmetric/X448/x448-pkcs8.der``
  contain an unencrypted X448 key.
* ``asymmetric/X448/x448-pub.pem`` and ``asymmetric/X448/x448-pub.der`` contain
  an X448 public key.
* ``asymmetric/Ed25519/ed25519-pkcs8-enc.pem`` and
  ``asymmetric/Ed25519/ed25519-pkcs8-enc.der`` contain an Ed25519 key encrypted
  with AES 256 CBC with the password ``password``.
* ``asymmetric/Ed25519/ed25519-pkcs8.pem`` and
  ``asymmetric/Ed25519/ed25519-pkcs8.der`` contain an unencrypted Ed25519 key.
* ``asymmetric/Ed25519/ed25519-pub.pem`` and
  ``asymmetric/Ed25519/ed25519-pub.der`` contain an Ed25519 public key.
* ``asymmetric/X25519/x25519-pkcs8-enc.pem`` and
  ``asymmetric/X25519/x25519-pkcs8-enc.der`` contain an X25519 key encrypted
  with AES 256 CBC with the password ``password``.
* ``asymmetric/X25519/x25519-pkcs8.pem`` and
  ``asymmetric/X25519/x25519-pkcs8.der`` contain an unencrypted X25519 key.
* ``asymmetric/X25519/x25519-pub.pem`` and ``asymmetric/X25519/x25519-pub.der``
  contain an X25519 public key.
* ``asymmetric/Ed448/ed448-pkcs8-enc.pem`` and
  ``asymmetric/Ed448/ed448-pkcs8-enc.der`` contain an Ed448 key encrypted
  with AES 256 CBC with the password ``password``.
* ``asymmetric/Ed448/ed448-pkcs8.pem`` and
  ``asymmetric/Ed448/ed448-pkcs8.der`` contain an unencrypted Ed448 key.
* ``asymmetric/Ed448/ed448-pub.pem`` and ``asymmetric/Ed448/ed448-pub.der``
  contain an Ed448 public key.


Key exchange
~~~~~~~~~~~~

* ``vectors/cryptography_vectors/asymmetric/DH/rfc3526.txt`` contains
  several standardized Diffie-Hellman groups from :rfc:`3526`.

* ``vectors/cryptography_vectors/asymmetric/DH/RFC5114.txt`` contains
  Diffie-Hellman examples from appendix A.1, A.2 and A.3 of :rfc:`5114`.

* ``vectors/cryptography_vectors/asymmetric/DH/vec.txt`` contains
  Diffie-Hellman examples from `botan`_.

* ``vectors/cryptography_vectors/asymmetric/DH/bad_exchange.txt`` contains
  Diffie-Hellman vector pairs that were generated using OpenSSL
  ``DH_generate_parameters_ex`` and ``DH_generate_key``.

* ``vectors/cryptography_vectors/asymmetric/DH/dhp.pem``,
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey.pem`` and
  ``vectors/cryptography_vectors/asymmetric/DH/dhpub.pem`` contains
  Diffie-Hellman parameters and key respectively. The keys were
  generated using OpenSSL following `DHKE`_ guide.
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey.txt`` contains
  all parameter in text.
  ``vectors/cryptography_vectors/asymmetric/DH/dhp.der``,
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey.der`` and
  ``vectors/cryptography_vectors/asymmetric/DH/dhpub.der`` contains
  are the above parameters and keys in DER format.

* ``vectors/cryptography_vectors/asymmetric/DH/dhp_rfc5114_2.pem``,
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey_rfc5114_2.pem`` and
  ``vectors/cryptography_vectors/asymmetric/DH/dhpub_rfc5114_2.pem`` contains
  Diffie-Hellman parameters and key respectively. The keys were
  generated using OpenSSL following `DHKE`_ guide. When creating the
  parameters we added the `-pkeyopt dh_rfc5114:2` option to use
  :rfc:`5114` 2048 bit DH parameters with 224 bit subgroup.
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey_rfc5114_2.txt`` contains
  all parameter in text.
  ``vectors/cryptography_vectors/asymmetric/DH/dhp_rfc5114_2.der``,
  ``vectors/cryptography_vectors/asymmetric/DH/dhkey_rfc5114_2.der`` and
  ``vectors/cryptography_vectors/asymmetric/DH/dhpub_rfc5114_2.der`` contains
  are the above parameters and keys in DER format.
* ``vectors/cryptography_vectors/asymmetric/DH/dh_key_256.pem`` contains
  a PEM PKCS8 encoded DH key with a 256-bit key size.

* ``vectors/cryptoraphy_vectors/asymmetric/ECDH/brainpool.txt`` contains
  Brainpool vectors from :rfc:`7027`.

X.509
~~~~~

* PKITS test suite from `NIST PKI Testing`_.
* ``v1_cert.pem`` from the OpenSSL source tree (`testx509.pem`_).
* ``ecdsa_root.pem`` - `DigiCert Global Root G3`_, a ``secp384r1`` ECDSA root
  certificate.
* ``verisign-md2-root.pem`` - A legacy Verisign public root signed using the
  MD2 algorithm. This is a PEM conversion of the `root data`_ in the NSS source
  tree.
* ``cryptography.io.pem`` - A leaf certificate issued by RapidSSL for the
  cryptography website.
* ``cryptography.io.old_header.pem`` - A leaf certificate issued by RapidSSL
  for the cryptography website. This certificate uses the ``X509 CERTIFICATE``
  legacy PEM header format.
* ``cryptography.io.repeated_twice.pem`` - The same as ``cryptography.io.pem``,
  but the certificate is repeated twice.
* ``cryptography.io.with_garbage.pem`` - The same as ``cryptography.io.pem``,
  but with other sections and text around it.
* ``rapidssl_sha256_ca_g3.pem`` - The intermediate CA that issued the
  ``cryptography.io.pem`` certificate.
* ``cryptography.io.precert.pem`` - A pre-certificate with the CT poison
  extension for the cryptography website.
* ``cryptography-scts.io.pem`` - A leaf certificate issued by Let's Encrypt for
  the cryptography website which contains signed certificate timestamps.
* ``wildcard_san.pem`` - A leaf certificate issued by a public CA for
  ``langui.sh`` that contains wildcard entries in the SAN extension.
* ``san_edipartyname.der`` - A DSA certificate from a `Mozilla bug`_
  containing a SAN extension with an ``ediPartyName`` general name.
* ``san_x400address.der`` - A DSA certificate from a `Mozilla bug`_ containing
  a SAN extension with an ``x400Address`` general name.
* ``department-of-state-root.pem`` - The intermediary CA for the Department of
  State, issued by the United States Federal Government's Common Policy CA.
  Notably has a ``critical`` policy constraints extensions.
* ``e-trust.ru.der`` - A certificate from a `Russian CA`_ signed using the GOST
  cipher and containing numerous unusual encodings such as NUMERICSTRING in
  the subject DN.
* ``alternate-rsa-sha1-oid.der`` - A certificate that uses an alternate
  signature OID for RSA with SHA1. This certificate has an invalid signature.
* ``badssl-sct.pem`` - A certificate with the certificate transparency signed
  certificate timestamp extension.
* ``bigoid.pem`` - A certificate with a rather long OID in the
  Certificate Policies extension.  We need to make sure we can parse
  long OIDs.
* ``wosign-bc-invalid.pem`` - A certificate issued by WoSign that contains
  a basic constraints extension with CA set to false and a path length of zero
  in violation of :rfc:`5280`.
* ``tls-feature-ocsp-staple.pem`` - A certificate issued by Let's Encrypt that
  contains a TLS Feature extension with the ``status_request`` feature
  (commonly known as OCSP Must-Staple).
* ``unique-identifier.pem`` - A certificate containing
  a distinguished name with an ``x500UniqueIdentifier``.
* ``utf8-dnsname.pem`` - A certificate containing non-ASCII characters in the
  DNS name entries of the SAN extension.
* ``badasn1time.pem`` - A certificate containing an incorrectly specified
  UTCTime in its validity->not_after.
* ``letsencryptx3.pem`` - A subordinate certificate used by Let's Encrypt to
  issue end entity certificates.
* ``ed25519-rfc8410.pem`` - A certificate containing an X25519 public key with
  an ``ed25519`` signature taken from :rfc:`8410`.
* ``root-ed25519.pem`` - An ``ed25519`` root certificate (``ed25519`` signature
  with ``ed25519`` public key) from the OpenSSL test suite.
  (`root-ed25519.pem`_)
* ``server-ed25519-cert.pem`` - An ``ed25519`` server certificate (RSA
  signature with ``ed25519`` public key) from the OpenSSL test suite.
  (`server-ed25519-cert.pem`_)
* ``server-ed448-cert.pem`` - An ``ed448`` server certificate (RSA
  signature with ``ed448`` public key) from the OpenSSL test suite.
  (`server-ed448-cert.pem`_)

Custom X.509 Vectors
~~~~~~~~~~~~~~~~~~~~

* ``invalid_version.pem`` - Contains an RSA 2048 bit certificate with the
  X.509 version field set to ``0x7``.
* ``post2000utctime.pem`` - Contains an RSA 2048 bit certificate with the
  ``notBefore`` and ``notAfter`` fields encoded as post-2000 ``UTCTime``.
* ``dsa_selfsigned_ca.pem`` - Contains a DSA self-signed CA certificate
  generated using OpenSSL.
* ``ec_no_named_curve.pem`` - Contains an ECDSA certificate that does not have
  an embedded OID defining the curve.
* ``all_supported_names.pem`` - An RSA 2048 bit certificate generated using
  OpenSSL that contains a subject and issuer that have two of each supported
  attribute type from :rfc:`5280`.
* ``unsupported_subject_name.pem`` - An RSA 2048 bit self-signed CA certificate
  generated using OpenSSL that contains the unsupported "initials" name.
* ``utf8_common_name.pem`` - An RSA 2048 bit self-signed CA certificate
  generated using OpenSSL that contains a UTF8String common name with the value
  "We heart UTF8!™".
* ``two_basic_constraints.pem`` - An RSA 2048 bit self-signed certificate
  containing two basic constraints extensions.
* ``basic_constraints_not_critical.pem`` - An RSA 2048 bit self-signed
  certificate containing a basic constraints extension that is not marked as
  critical.
* ``bc_path_length_zero.pem`` - An RSA 2048 bit self-signed
  certificate containing a basic constraints extension with a path length of
  zero.
* ``unsupported_extension.pem`` - An RSA 2048 bit self-signed certificate
  containing an unsupported extension type. The OID was encoded as
  "*******" with an ``extnValue`` of "value".
* ``unsupported_extension_2.pem`` - A ``secp256r1`` certificate
  containing two unsupported extensions. The OIDs are ``*******.4.1.41482.2``
  with an ``extnValue`` of ``*******.4.1.41482.1.2`` and
  ``*******.4.1.45724.2.1.1`` with an ``extnValue`` of ``\x03\x02\x040``
* ``unsupported_extension_critical.pem`` - An RSA 2048 bit self-signed
  certificate containing an unsupported extension type marked critical. The OID
  was encoded as "*******" with an ``extnValue`` of "value".
* ``san_email_dns_ip_dirname_uri.pem`` - An RSA 2048 bit self-signed
  certificate containing a subject alternative name extension with the
  following general names: ``rfc822Name``, ``dNSName``, ``iPAddress``,
  ``directoryName``, and ``uniformResourceIdentifier``.
* ``san_empty_hostname.pem`` - An RSA 2048 bit self-signed certificate
  containing a subject alternative extension with an empty ``dNSName``
  general name.
* ``san_other_name.pem`` - An RSA 2048 bit self-signed certificate containing
  a subject alternative name extension with the ``otherName`` general name.
* ``san_registered_id.pem`` - An RSA 1024 bit certificate containing a
  subject alternative name extension with the ``registeredID`` general name.
* ``all_key_usages.pem`` - An RSA 2048 bit self-signed certificate containing
  a key usage extension with all nine purposes set to true.
* ``extended_key_usage.pem`` - An RSA 2048 bit self-signed certificate
  containing an extended key usage extension with eight usages.
* ``san_idna_names.pem`` - An RSA 2048 bit self-signed certificate containing
  a subject alternative name extension with ``rfc822Name``, ``dNSName``, and
  ``uniformResourceIdentifier`` general names with IDNA (:rfc:`5895`) encoding.
* ``san_wildcard_idna.pem`` - An RSA 2048 bit self-signed certificate
  containing a subject alternative name extension with a ``dNSName`` general
  name with a wildcard IDNA (:rfc:`5895`) domain.
* ``san_idna2003_dnsname.pem`` - An RSA 2048 bit self-signed certificate
  containing a subject alternative name extension with an IDNA 2003
  (:rfc:`3490`) ``dNSName``.
* ``san_rfc822_names.pem`` - An RSA 2048 bit self-signed certificate containing
  a subject alternative name extension with various ``rfc822Name`` values.
* ``san_rfc822_idna.pem`` - An RSA 2048 bit self-signed certificate containing
  a subject alternative name extension with an IDNA ``rfc822Name``.
* ``san_uri_with_port.pem`` - An RSA 2048 bit self-signed certificate
  containing a subject alternative name extension with various
  ``uniformResourceIdentifier`` values.
* ``san_ipaddr.pem`` - An RSA 2048 bit self-signed certificate containing a
  subject alternative name extension with an ``iPAddress`` value.
* ``san_dirname.pem`` - An RSA 2048 bit self-signed certificate containing a
  subject alternative name extension with a ``directoryName`` value.
* ``inhibit_any_policy_5.pem`` - An RSA 2048 bit self-signed certificate
  containing an inhibit any policy extension with the value 5.
* ``inhibit_any_policy_negative.pem`` - An RSA 2048 bit self-signed certificate
  containing an inhibit any policy extension with the value -1.
* ``authority_key_identifier.pem`` - An RSA 2048 bit self-signed certificate
  containing an authority key identifier extension with key identifier,
  authority certificate issuer, and authority certificate serial number fields.
* ``authority_key_identifier_no_keyid.pem`` - An RSA 2048 bit self-signed
  certificate containing an authority key identifier extension with authority
  certificate issuer and authority certificate serial number fields.
* ``aia_ocsp_ca_issuers.pem`` - An RSA 2048 bit self-signed certificate
  containing an authority information access extension with two OCSP and one
  CA issuers entry.
* ``aia_ocsp.pem`` - An RSA 2048 bit self-signed certificate
  containing an authority information access extension with an OCSP entry.
* ``aia_ca_issuers.pem`` - An RSA 2048 bit self-signed certificate
  containing an authority information access extension with a CA issuers entry.
* ``cdp_empty_hostname.pem`` - An RSA 2048 bit self-signed certificate
  containing a CRL distribution point extension with ``fullName`` URI without
  a hostname.
* ``cdp_fullname_reasons_crl_issuer.pem`` - An RSA 1024 bit certificate
  containing a CRL distribution points extension with ``fullName``,
  ``cRLIssuer``, and ``reasons`` data.
* ``cdp_crl_issuer.pem`` - An RSA 1024 bit certificate containing a CRL
  distribution points extension with ``cRLIssuer`` data.
* ``cdp_all_reasons.pem`` - An RSA 1024 bit certificate containing a CRL
  distribution points extension with all ``reasons`` bits set.
* ``cdp_reason_aa_compromise.pem`` - An RSA 1024 bit certificate containing a
  CRL distribution points extension with the ``AACompromise`` ``reasons`` bit
  set.
* ``nc_permitted_excluded.pem`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with both permitted and excluded
  elements. Contains ``IPv4`` and ``IPv6`` addresses with network mask as well
  as ``dNSName`` with a leading period.
* ``nc_permitted_excluded_2.pem`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with both permitted and excluded
  elements. Unlike ``nc_permitted_excluded.pem``, the general names do not
  contain any name constraints specific values.
* ``nc_permitted.pem`` - An RSA 2048 bit self-signed certificate containing a
  name constraints extension with permitted elements.
* ``nc_permitted_2.pem`` - An RSA 2048 bit self-signed certificate containing a
  name constraints extension with permitted elements that do not contain any
  name constraints specific values.
* ``nc_excluded.pem`` - An RSA 2048 bit self-signed certificate containing a
  name constraints extension with excluded elements.
* ``nc_invalid_ip_netmask.pem`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with a permitted element that has an
  ``IPv6`` IP and an invalid network mask.
* ``nc_invalid_ip4_netmask.der`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with a permitted element that has an
  ``IPv4`` IP and an invalid network mask. The signature on this certificate
  is invalid.
* ``nc_single_ip_netmask.pem`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with a permitted element that has two
  IPs with ``/32`` and ``/128`` network masks.
* ``nc_ip_invalid_length.pem`` - An RSA 2048 bit self-signed certificate
  containing a name constraints extension with a permitted element that has an
  invalid length (33 bytes instead of 32) for an ``IPv6`` address with
  network mask. The signature on this certificate is invalid.
* ``cp_user_notice_with_notice_reference.pem`` - An RSA 2048 bit self-signed
  certificate containing a certificate policies extension with a
  notice reference in the user notice.
* ``cp_user_notice_with_explicit_text.pem`` - An RSA 2048 bit self-signed
  certificate containing a certificate policies extension with explicit
  text and no notice reference.
* ``cp_cps_uri.pem`` - An RSA 2048 bit self-signed certificate containing a
  certificate policies extension with a CPS URI and no user notice.
* ``cp_user_notice_no_explicit_text.pem`` - An RSA 2048 bit self-signed
  certificate containing a certificate policies extension with a user notice
  with no explicit text.
* ``cp_invalid.pem`` - An RSA 2048 bit self-signed certificate containing a
  certificate policies extension with invalid data. The ``policyQualifierId``
  is for ``id-qt-unotice`` but the value is an ``id-qt-cps`` ASN.1 structure.
* ``cp_invalid2.der`` - An RSA 2048 bit self-signed certificate containing a
  certificate policies extension with invalid data. The ``policyQualifierId``
  is for ``id-qt-cps`` but the value is an ``id-qt-unotice`` ASN.1 structure.
  The signature on this certificate is invalid.
* ``ian_uri.pem`` - An RSA 2048 bit certificate containing an issuer
  alternative name extension with a ``URI`` general name.
* ``ocsp_nocheck.pem`` - An RSA 2048 bit self-signed certificate containing
  an ``OCSPNoCheck`` extension.
* ``pc_inhibit_require.pem`` - An RSA 2048 bit self-signed certificate
  containing a policy constraints extension with both inhibit policy mapping
  and require explicit policy elements.
* ``pc_inhibit.pem`` - An RSA 2048 bit self-signed certificate containing a
  policy constraints extension with an inhibit policy mapping element.
* ``pc_require.pem`` - An RSA 2048 bit self-signed certificate containing a
  policy constraints extension with a require explicit policy element.
* ``unsupported_subject_public_key_info.pem`` - A certificate whose public key
  is an unknown OID (``*******.4.1.8432.1.1.2``).
* ``policy_constraints_explicit.pem`` - A self-signed certificate containing
  a ``policyConstraints`` extension with a ``requireExplicitPolicy`` value.
* ``freshestcrl.pem`` - A self-signed certificate containing a ``freshestCRL``
  extension.
* ``sia.pem`` - An RSA 2048 bit self-signed certificate containing a subject
  information access extension with both a CA repository entry and a custom
  OID entry.
* ``ca/ca.pem`` - A self-signed certificate with ``basicConstraints`` set to
  true. Its private key is ``ca/ca_key.pem``. This certificate is encoded in
  several of the PKCS12 custom vectors.
* ``negative_serial.pem`` - A certificate with a serial number that is a
  negative number.
* ``rsa_pss.pem`` - A certificate with an RSA PSS signature.
* ``root-ed448.pem`` - An ``ed448`` self-signed CA certificate
  using ``ed448-pkcs8.pem`` as key.
* ``ca/rsa_ca.pem`` - A self-signed RSA certificate with ``basicConstraints``
  set to true. Its private key is ``ca/rsa_key.pem``.
* ``invalid-sct-version.der`` - A certificate with an SCT with an unknown
  version.
* ``invalid-sct-length.der`` - A certificate with an SCT with an internal
  length greater than the amount of data.

Custom X.509 Request Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* ``dsa_sha1.pem`` and ``dsa_sha1.der`` - Contain a certificate request using
  1024-bit DSA parameters and SHA1 generated using OpenSSL.
* ``rsa_md4.pem`` and ``rsa_md4.der`` - Contain a certificate request using
  2048 bit RSA and MD4 generated using OpenSSL.
* ``rsa_sha1.pem`` and ``rsa_sha1.der`` - Contain a certificate request using
  2048 bit RSA and SHA1 generated using OpenSSL.
* ``rsa_sha256.pem`` and ``rsa_sha256.der`` - Contain a certificate request
  using 2048 bit RSA and SHA256 generated using OpenSSL.
* ``ec_sha256.pem`` and ``ec_sha256.der`` - Contain a certificate request
  using EC (``secp384r1``) and SHA256 generated using OpenSSL.
* ``ec_sha256_old_header.pem`` - Identical to ``ec_sha256.pem``, but uses
  the ``-----BEGIN NEW CERTIFICATE REQUEST-----`` legacy PEM header format.
* ``san_rsa_sha1.pem`` and ``san_rsa_sha1.der`` - Contain a certificate
  request using RSA and SHA1 with a subject alternative name extension
  generated using OpenSSL.
* ``two_basic_constraints.pem`` - A certificate signing request
  for an RSA 2048 bit key containing two basic constraints extensions.
* ``unsupported_extension.pem`` - A certificate signing request
  for an RSA 2048 bit key containing containing an unsupported
  extension type. The OID was encoded as "*******" with an
  ``extnValue`` of "value".
* ``unsupported_extension_critical.pem`` - A certificate signing
  request for an RSA 2048 bit key containing containing an unsupported
  extension type marked critical. The OID was encoded as "*******"
  with an ``extnValue`` of "value".
* ``basic_constraints.pem`` - A certificate signing request for an RSA
  2048 bit key containing a basic constraints extension marked as
  critical.
* ``invalid_signature.pem`` - A certificate signing request for an RSA
  1024 bit key containing an invalid signature with correct padding.
* ``challenge.pem`` - A certificate signing request for an RSA 2048 bit key
  containing a challenge password.
* ``challenge-invalid.der`` - A certificate signing request for an RSA 2048 bit
  key containing a challenge password attribute that has been encoded as an
  ASN.1 integer rather than a string.
* ``challenge-unstructured.pem`` - A certificate signing request for an RSA
  2048 bit key containing a challenge password attribute and an unstructured
  name attribute.
* ``challenge-multi-valued.der`` - A certificate signing request for an RSA
  2048 bit key containing a challenge password attribute with two values
  inside the ASN.1 set. The signature on this request is invalid.

Custom X.509 Certificate Revocation List Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* ``crl_all_reasons.pem`` - Contains a CRL with 12 revoked certificates, whose
  serials match their list position. It includes one revocation without
  any entry extensions, 10 revocations with every supported reason code and one
  revocation with an unsupported, non-critical entry extension with the OID
  value set to "*******".
* ``crl_dup_entry_ext.pem`` - Contains a CRL with one revocation which has a
  duplicate entry extension.
* ``crl_md2_unknown_crit_entry_ext.pem`` - Contains a CRL with one revocation
  which contains an unsupported critical entry extension with the OID value set
  to "*******". The CRL uses an unsupported MD2 signature algorithm.
* ``crl_unsupported_reason.pem`` - Contains a CRL with one revocation which has
  an unsupported reason code.
* ``crl_inval_cert_issuer_entry_ext.pem`` - Contains a CRL with one revocation
  which has one entry extension for certificate issuer with an empty value.
* ``crl_empty.pem`` - Contains a CRL with no revoked certificates.
* ``crl_empty_no_sequence.der`` - Contains a CRL with no revoked certificates
  and the optional ASN.1 sequence for revoked certificates is omitted.
* ``crl_ian_aia_aki.pem`` - Contains a CRL with ``IssuerAlternativeName``,
  ``AuthorityInformationAccess``, ``AuthorityKeyIdentifier`` and ``CRLNumber``
  extensions.
* ``valid_signature_crl.pem`` - Contains a CRL with a valid signature.
* ``valid_signature_cert.pem`` - Contains a cert whose public key corresponds
  to the private key that produced the signature for
  ``valid_signature_crl.pem``.
* ``invalid_signature_crl.pem`` - Contains a CRL with the last signature byte
  incremented by 1 to produce an invalid signature.
* ``invalid_signature_cert.pem`` - Contains a cert whose public key corresponds
  to the private key that  produced the signature for
  ``invalid_signature_crl.pem``.
* ``crl_delta_crl_indicator.pem`` - Contains a CRL with the
  ``DeltaCRLIndicator`` extension.
* ``crl_idp_fullname_only.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension with only a ``fullname`` for the
  distribution point.
* ``crl_idp_only_ca.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that is only valid for CA certificate
  revocation.
* ``crl_idp_fullname_only_aa.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that sets a ``fullname`` and is only
  valid for attribute certificate revocation.
* ``crl_idp_fullname_only_user.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that sets a ``fullname`` and is only
  valid for user certificate revocation.
* ``crl_idp_fullname_indirect_crl.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that sets a ``fullname`` and the
  indirect CRL flag.
* ``crl_idp_reasons_only.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that is only valid for revocations
  with the ``keyCompromise`` reason.
* ``crl_idp_relative_user_all_reasons.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension that sets all revocation reasons as
  allowed.
* ``crl_idp_relativename_only.pem`` - Contains a CRL with an
  ``IssuingDistributionPoints`` extension with only a ``relativename`` for
  the distribution point.
* ``crl_unrecognized_extension.der`` - Contains a CRL containing an
  unsupported extension type. The OID was encoded as "*******.5" with an
  ``extnValue`` of ``abcdef``.
* ``crl_invalid_time.der`` - Contains a CRL with an invalid ``UTCTime``
  value in ``thisUpdate``. The signature on this CRL is invalid.
* ``crl_no_next_time.pem`` - Contains a CRL with no ``nextUpdate`` value. The
  signature on this CRL is invalid.

X.509 OCSP Test Vectors
~~~~~~~~~~~~~~~~~~~~~~~
* ``x509/ocsp/resp-sha256.der`` - An OCSP response for ``cryptography.io`` with
  a SHA256 signature.
* ``x509/ocsp/resp-unauthorized.der`` - An OCSP response with an unauthorized
  status.
* ``x509/ocsp/resp-revoked.der`` - An OCSP response for ``revoked.badssl.com``
  with a revoked status.
* ``x509/ocsp/resp-delegate-unknown-cert.der`` - An OCSP response for an
  unknown cert from ``AC Camerafirma``. This response also contains a delegate
  certificate.
* ``x509/ocsp/resp-responder-key-hash.der`` - An OCSP response from the
  ``DigiCert`` OCSP responder that uses a key hash for the responder ID.
* ``x509/ocsp/resp-revoked-reason.der`` - An OCSP response from the
  ``QuoVadis`` OCSP responder that contains a revoked certificate with a
  revocation reason.
* ``x509/ocsp/resp-revoked-no-next-update.der`` - An OCSP response that
  contains a revoked certificate and no ``nextUpdate`` value.
* ``x509/ocsp/resp-invalid-signature-oid.der`` - An OCSP response that was
  modified to contain an MD2 signature algorithm object identifier.
* ``x509/ocsp/resp-single-extension-reason.der`` - An OCSP response that
  contains a ``CRLReason`` single extension.
* ``x509/ocsp/resp-sct-extension.der`` - An OCSP response containing a
  ``CT Certificate SCTs`` single extension, from the SwissSign OCSP responder.
* ``x509/ocsp/ocsp-army.deps.mil-resp.der`` - An OCSP response containing
  multiple ``SINGLERESP`` values.
* ``x509/ocsp/resp-response-type-unknown-oid.der`` - An OCSP response with
  an unknown OID for response type. The signature on this response is invalid.
* ``x509/ocsp/resp-successful-no-response-bytes.der`` - An OCSP request with
  a successful response type but the response bytes are missing.
* ``x509/ocsp/resp-unknown-response-status.der`` - An OCSP response with an
  unknown response status.

Custom X.509 OCSP Test Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
* ``x509/ocsp/req-sha1.der`` - An OCSP request containing a single request and
  using SHA1 as the hash algorithm.
* ``x509/ocsp/req-multi-sha1.der`` - An OCSP request containing multiple
  requests.
* ``x509/ocsp/req-invalid-hash-alg.der`` - An OCSP request containing an
  invalid hash algorithm OID.
* ``x509/ocsp/req-ext-nonce.der`` - An OCSP request containing a nonce
  extension.
* ``x509/ocsp/req-ext-unknown-oid.der`` - An OCSP request containing an
  extension with an unknown OID.
* ``x509/ocsp/req-duplicate-ext.der`` - An OCSP request with duplicate
  extensions.
* ``x509/ocsp/resp-unknown-extension.der`` - An OCSP response containing an
  extension with an unknown OID.
* ``x509/ocsp/resp-unknown-hash-alg.der`` - AN OCSP response containing an
  invalid hash algorithm OID.

Custom PKCS12 Test Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~~
* ``pkcs12/cert-key-aes256cbc.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  both encrypted with AES 256 CBC with the password ``cryptography``.
* ``pkcs12/cert-none-key-none.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with no encryption. The password (used for integrity checking only) is
  ``cryptography``.
* ``pkcs12/cert-rc2-key-3des.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) encrypted with RC2 and key
  (``x509/custom/ca/ca_key.pem``) encrypted via 3DES with the password
  ``cryptography``.
* ``pkcs12/no-password.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``) with no
  encryption and no password.
* ``pkcs12/no-cert-key-aes256cbc.p12`` - A PKCS12 file containing a key
  (``x509/custom/ca/ca_key.pem``) encrypted via AES 256 CBC with the
  password ``cryptography`` and no certificate.
* ``pkcs12/cert-aes256cbc-no-key.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) encrypted via AES 256 CBC with the
  password ``cryptography`` and no private key.
* ``pkcs12/no-name-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``).
* ``pkcs12/name-all-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``name``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``name2`` and ``name3``, respectively.
* ``pkcs12/name-1-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``name``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``).
* ``pkcs12/name-2-3-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``) with friendly names ``name2`` and
  ``name3``, respectively.
* ``pkcs12/name-2-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``), the first having friendly name ``name2``.
* ``pkcs12/name-3-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``), the latter having friendly name ``name3``.
* ``pkcs12/name-unicode-no-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``☺``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``ä`` and ``ç``, respectively.
* ``pkcs12/no-name-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``),
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/name-all-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``name``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``name2`` and ``name3`` respectively,
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/name-1-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``name``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/name-2-3-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``) with friendly names ``name2` and
  ``name3`` respectively, encrypted via AES 256 CBC with the password
  ``cryptography``.
* ``pkcs12/name-2-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``), the first having friendly name ``name2``,
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/name-3-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``),
  as well as two additional certificates (``x509/cryptography.io.pem``
  and ``x509/letsencryptx3.pem``), the latter having friendly name ``name2``,
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/name-unicode-pwd.p12`` - A PKCS12 file containing a cert
  (``x509/custom/ca/ca.pem``) and key (``x509/custom/ca/ca_key.pem``)
  with friendly name ``☺``, as well as two additional certificates
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``ä`` and ``ç`` respectively, encrypted via
  AES 256 CBC with the password ``cryptography``.
* ``pkcs12/no-cert-no-name-no-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``).
* ``pkcs12/no-cert-name-all-no-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``name2`` and ``name3``, respectively.
* ``pkcs12/no-cert-name-2-no-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  the first having friendly name ``name2``.
* ``pkcs12/no-cert-name-3-no-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  the second having friendly name ``name3``.
* ``pkcs12/no-cert-name-unicode-no-pwd.p12`` - A PKCS12 file containing two
  certs (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``☹`` and ``ï``, respectively.
* ``pkcs12/no-cert-no-name-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/no-cert-name-all-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``name2`` and ``name3``, respectively,
  encrypted via AES 256 CBC with the password ``cryptography``.
* ``pkcs12/no-cert-name-2-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  the first with friendly name ``name2``, encrypted via AES 256 CBC with
  the password ``cryptography``.
* ``pkcs12/no-cert-name-3-pwd.p12`` - A PKCS12 file containing two certs
  (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``),
  the second with friendly name ``name3``, encrypted via AES 256 CBC with
  the password ``cryptography``.
* ``pkcs12/no-cert-name-unicode-pwd.p12`` - A PKCS12 file containing two
  certs (``x509/cryptography.io.pem`` and ``x509/letsencryptx3.pem``)
  with friendly names ``☹`` and ``ï``, respectively, encrypted via
  AES 256 CBC with the password ``cryptography``.

Custom PKCS7 Test Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~
* ``pkcs7/isrg.pem`` - A PEM encoded PKCS7 file containing the ISRG X1 root
  CA.
* ``pkcs7/amazon-roots.p7b`` - A DER encoded PCKS7 file containing Amazon Root
  CA 2 and 3.
* ``pkcs7/enveloped.pem`` - A PEM encoded PKCS7 file with enveloped data.

Custom OpenSSH Test Vectors
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Generated by
``asymmetric/OpenSSH/gen.sh``
using command-line tools from OpenSSH_7.6p1 package.

* ``dsa-nopsw.key``, ``dsa-nopsw.key.pub``, ``dsa-nopsw.key-cert.pub`` -
  DSA-1024 private key; and corresponding public key in plain format
  and with self-signed certificate.
* ``dsa-psw.key``, ``dsa-psw.key.pub`` -
  Password-protected DSA-1024 private key and corresponding public key.
  Password is "password".
* ``ecdsa-nopsw.key``, ``ecdsa-nopsw.key.pub``,
  ``ecdsa-nopsw.key-cert.pub`` -
  SECP256R1 private key; and corresponding public key in plain format
  and with self-signed certificate.
* ``ecdsa-psw.key``, ``ecdsa-psw.key.pub`` -
  Password-protected SECP384R1 private key and corresponding public key.
  Password is "password".
* ``ed25519-nopsw.key``, ``ed25519-nopsw.key.pub``,
  ``ed25519-nopsw.key-cert.pub`` -
  Ed25519 private key; and corresponding public key in plain format
  and with self-signed certificate.
* ``ed25519-psw.key``, ``ed25519-psw.key.pub`` -
  Password-protected Ed25519 private key and corresponding public key.
  Password is "password".
* ``rsa-nopsw.key``, ``rsa-nopsw.key.pub``,
  ``rsa-nopsw.key-cert.pub`` -
  RSA-2048 private key; and corresponding public key in plain format
  and with self-signed certificate.
* ``rsa-psw.key``, ``rsa-psw.key.pub`` -
  Password-protected RSA-2048 private key and corresponding public key.
  Password is "password".

Hashes
~~~~~~

* MD5 from :rfc:`1321`.
* RIPEMD160 from the `RIPEMD website`_.
* SHA1 from `NIST CAVP`_.
* SHA2 (224, 256, 384, 512, 512/224, 512/256) from `NIST CAVP`_.
* SHA3 (224, 256, 384, 512) from `NIST CAVP`_.
* SHAKE (128, 256) from `NIST CAVP`_.
* Blake2s and Blake2b from OpenSSL `test/evptests.txt`_.

HMAC
~~~~

* HMAC-MD5 from :rfc:`2202`.
* HMAC-SHA1 from :rfc:`2202`.
* HMAC-RIPEMD160 from :rfc:`2286`.
* HMAC-SHA2 (224, 256, 384, 512) from :rfc:`4231`.

Key derivation functions
~~~~~~~~~~~~~~~~~~~~~~~~

* HKDF (SHA1, SHA256) from :rfc:`5869`.
* PBKDF2 (HMAC-SHA1) from :rfc:`6070`.
* scrypt from the `draft RFC`_.
* X9.63 KDF from `NIST CAVP`_.
* SP 800-108 Counter Mode KDF (HMAC-SHA1, HMAC-SHA224, HMAC-SHA256,
  HMAC-SHA384, HMAC-SHA512) from `NIST CAVP`_.

Key wrapping
~~~~~~~~~~~~

* AES key wrap (AESKW) and 3DES key wrap test vectors from `NIST CAVP`_.
* AES key wrap with padding vectors from `Botan's key wrap vectors`_.

Recipes
~~~~~~~

* Fernet from its `specification repository`_.

Symmetric ciphers
~~~~~~~~~~~~~~~~~

* AES (CBC, CFB, ECB, GCM, OFB, CCM) from `NIST CAVP`_.
* AES CTR from :rfc:`3686`.
* 3DES (CBC, CFB, ECB, OFB) from `NIST CAVP`_.
* ARC4 (KEY-LENGTH: 40, 56, 64, 80, 128, 192, 256) from :rfc:`6229`.
* ARC4 (KEY-LENGTH: 160) generated by this project.
  See: :doc:`/development/custom-vectors/arc4`
* Blowfish (CBC, CFB, ECB, OFB) from `Bruce Schneier's vectors`_.
* Camellia (ECB) from NTT's `Camellia page`_ as linked by `CRYPTREC`_.
* Camellia (CBC, CFB, OFB) from `OpenSSL's test vectors`_.
* CAST5 (ECB) from :rfc:`2144`.
* CAST5 (CBC, CFB, OFB) generated by this project.
  See: :doc:`/development/custom-vectors/cast5`
* ChaCha20 from :rfc:`7539`.
* ChaCha20Poly1305 from :rfc:`7539`, `OpenSSL's evpciph.txt`_, and the
  `BoringSSL ChaCha20Poly1305 tests`_.
* IDEA (ECB) from the `NESSIE IDEA vectors`_ created by `NESSIE`_.
* IDEA (CBC, CFB, OFB) generated by this project.
  See: :doc:`/development/custom-vectors/idea`
* SEED (ECB) from :rfc:`4269`.
* SEED (CBC) from :rfc:`4196`.
* SEED (CFB, OFB) generated by this project.
  See: :doc:`/development/custom-vectors/seed`

Two factor authentication
~~~~~~~~~~~~~~~~~~~~~~~~~

* HOTP from :rfc:`4226`
* TOTP from :rfc:`6238` (Note that an `errata`_ for the test vectors in RFC
  6238 exists)

CMAC
~~~~

* AES-128, AES-192, AES-256, 3DES from `NIST SP-800-38B`_

Poly1305
~~~~~~~~

* Test vectors from :rfc:`7539`.

Creating test vectors
---------------------

When official vectors are unavailable ``cryptography`` may choose to build
its own using existing vectors as source material.

Created Vectors
~~~~~~~~~~~~~~~

.. toctree::
    :maxdepth: 1

    custom-vectors/arc4
    custom-vectors/cast5
    custom-vectors/idea
    custom-vectors/seed
    custom-vectors/hkdf


If official test vectors appear in the future the custom generated vectors
should be discarded.

Any vectors generated by this method must also be prefixed with the following
header format (substituting the correct information):

.. code-block:: python

    # CAST5 CBC vectors built for https://github.com/pyca/cryptography
    # Derived from the AESVS MMT test data for CBC
    # Verified against the CommonCrypto and Go crypto packages
    # Key Length : 128

.. _`NIST`: https://www.nist.gov/
.. _`IETF`: https://www.ietf.org/
.. _`Project Wycheproof`: https://github.com/google/wycheproof
.. _`NIST CAVP`: https://csrc.nist.gov/projects/cryptographic-algorithm-validation-program
.. _`Bruce Schneier's vectors`: https://www.schneier.com/wp-content/uploads/2015/12/vectors-2.txt
.. _`Camellia page`: https://info.isl.ntt.co.jp/crypt/eng/camellia/
.. _`CRYPTREC`: https://www.cryptrec.go.jp
.. _`OpenSSL's test vectors`: https://github.com/openssl/openssl/blob/97cf1f6c2854a3a955fd7dd3a1f113deba00c9ef/crypto/evp/evptests.txt#L232
.. _`OpenSSL's evpciph.txt`: https://github.com/openssl/openssl/blob/5a7bc0be97dee9ac715897fe8180a08e211bc6ea/test/evpciph.txt#L2362
.. _`BoringSSL ChaCha20Poly1305 tests`: https://boringssl.googlesource.com/boringssl/+/2e2a226ac9201ac411a84b5e79ac3a7333d8e1c9/crypto/cipher_extra/test/chacha20_poly1305_tests.txt
.. _`BoringSSL evp tests`: https://boringssl.googlesource.com/boringssl/+/ce3773f9fe25c3b54390bc51d72572f251c7d7e6/crypto/evp/evp_tests.txt
.. _`RIPEMD website`: https://homes.esat.kuleuven.be/~bosselae/ripemd160.html
.. _`draft RFC`: https://tools.ietf.org/html/draft-josefsson-scrypt-kdf-01
.. _`Specification repository`: https://github.com/fernet/spec
.. _`errata`: https://www.rfc-editor.org/errata_search.php?rfc=6238
.. _`OpenSSL example key`: https://github.com/openssl/openssl/blob/d02b48c63a58ea4367a0e905979f140b7d090f86/test/testrsa.pem
.. _`GnuTLS key parsing tests`: https://gitlab.com/gnutls/gnutls/commit/f16ef39ef0303b02d7fa590a37820440c466ce8d
.. _`enc-rsa-pkcs8.pem`: https://gitlab.com/gnutls/gnutls/blob/f8d943b38bf74eaaa11d396112daf43cb8aa82ae/tests/pkcs8-decode/encpkcs8.pem
.. _`enc2-rsa-pkcs8.pem`: https://gitlab.com/gnutls/gnutls/blob/f8d943b38bf74eaaa11d396112daf43cb8aa82ae/tests/pkcs8-decode/enc2pkcs8.pem
.. _`unenc-rsa-pkcs8.pem`: https://gitlab.com/gnutls/gnutls/blob/f8d943b38bf74eaaa11d396112daf43cb8aa82ae/tests/pkcs8-decode/unencpkcs8.pem
.. _`pkcs12_s2k_pem.c`: https://gitlab.com/gnutls/gnutls/blob/f8d943b38bf74eaaa11d396112daf43cb8aa82ae/tests/pkcs12_s2k_pem.c
.. _`Botan's ECC private keys`: https://github.com/randombit/botan/tree/4917f26a2b154e841cd27c1bcecdd41d2bdeb6ce/src/tests/data/ecc
.. _`GnuTLS example keys`: https://gitlab.com/gnutls/gnutls/commit/ad2061deafdd7db78fd405f9d143b0a7c579da7b
.. _`NESSIE IDEA vectors`: https://www.cosic.esat.kuleuven.be/nessie/testvectors/bc/idea/Idea-128-64.verified.test-vectors
.. _`NESSIE`: https://en.wikipedia.org/wiki/NESSIE
.. _`Ed25519 website`: https://ed25519.cr.yp.to/software.html
.. _`NIST SP-800-38B`: https://csrc.nist.gov/publications/detail/sp/800-38b/archive/2005-05-01
.. _`NIST PKI Testing`: https://csrc.nist.gov/Projects/PKI-Testing
.. _`testx509.pem`: https://github.com/openssl/openssl/blob/master/test/testx509.pem
.. _`DigiCert Global Root G3`: http://cacerts.digicert.com/DigiCertGlobalRootG3.crt
.. _`root data`: https://hg.mozilla.org/projects/nss/file/25b2922cc564/security/nss/lib/ckfw/builtins/certdata.txt#l2053
.. _`asymmetric/public/PKCS1/dsa.pub.pem`: https://github.com/ruby/ruby/blob/4ccb387f3bc436a08fc6d72c4931994f5de95110/test/openssl/test_pkey_dsa.rb#L53
.. _`Mozilla bug`: https://bugzilla.mozilla.org/show_bug.cgi?id=233586
.. _`Russian CA`: https://e-trust.gosuslugi.ru/
.. _`test/evptests.txt`: https://github.com/openssl/openssl/blob/2d0b44126763f989a4cbffbffe9d0c7518158bb7/test/evptests.txt
.. _`unknown signature OID`: https://bugzilla.mozilla.org/show_bug.cgi?id=405966
.. _`botan`: https://github.com/randombit/botan/blob/57789bdfc55061002b2727d0b32587612829a37c/src/tests/data/pubkey/dh.vec
.. _`DHKE`: https://sandilands.info/sgordon/diffie-hellman-secret-key-exchange-with-openssl
.. _`Botan's key wrap vectors`: https://github.com/randombit/botan/blob/737f33c09a18500e044dca3e2ae13bd2c08bafdd/src/tests/data/keywrap/nist_key_wrap.vec
.. _`root-ed25519.pem`: https://github.com/openssl/openssl/blob/2a1e2fe145c6eb8e75aa2e1b3a8c3a49384b2852/test/certs/root-ed25519.pem
.. _`server-ed25519-cert.pem`: https://github.com/openssl/openssl/blob/2a1e2fe145c6eb8e75aa2e1b3a8c3a49384b2852/test/certs/server-ed25519-cert.pem
.. _`server-ed448-cert.pem`: https://github.com/openssl/openssl/blob/2a1e2fe145c6eb8e75aa2e1b3a8c3a49384b2852/test/certs/server-ed448-cert.pem
