=pod

=head1 NAME

SSL_CTX_set_timeout, SSL_CTX_get_timeout - manipulate timeout values for session caching

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_CTX_set_timeout(SSL_CTX *ctx, long t);
 long SSL_CTX_get_timeout(SSL_CTX *ctx);

=head1 DESCRIPTION

SSL_CTX_set_timeout() sets the timeout for newly created sessions for
B<ctx> to B<t>. The timeout value B<t> must be given in seconds.

SSL_CTX_get_timeout() returns the currently set timeout value for B<ctx>.

=head1 NOTES

Whenever a new session is created, it is assigned a maximum lifetime. This
lifetime is specified by storing the creation time of the session and the
timeout value valid at this time. If the actual time is later than creation
time plus timeout, the session is not reused.

Due to this realization, all sessions behave according to the timeout value
valid at the time of the session negotiation. Changes of the timeout value
do not affect already established sessions.

The expiration time of a single session can be modified using the
L<SSL_SESSION_get_time(3)> family of functions.

Expired sessions are removed from the internal session cache, whenever
L<SSL_CTX_flush_sessions(3)> is called, either
directly by the application or automatically (see
L<SSL_CTX_set_session_cache_mode(3)>)

The default value for session timeout is decided on a per protocol
basis, see L<SSL_get_default_timeout(3)>.
All currently supported protocols have the same default timeout value
of 300 seconds.

This timeout value is used as the ticket lifetime hint for stateless session
tickets. It is also used as the timeout value within the ticket itself.

For TLSv1.3, RFC8446 limits transmission of this value to 1 week (604800
seconds).

For TLSv1.2, tickets generated during an initial handshake use the value
as specified. Tickets generated during a resumed handshake have a value
of 0 for the ticket lifetime hint.

=head1 RETURN VALUES

SSL_CTX_set_timeout() returns the previously set timeout value.

SSL_CTX_get_timeout() returns the currently set timeout value.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_session_cache_mode(3)>,
L<SSL_SESSION_get_time(3)>,
L<SSL_CTX_flush_sessions(3)>,
L<SSL_get_default_timeout(3)>

=head1 COPYRIGHT

Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
