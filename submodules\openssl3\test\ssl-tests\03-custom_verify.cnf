# Generated with generate_ssl_tests.pl

num_tests = 10

test-0 = 0-verify-success
test-1 = 1-verify-custom-reject
test-2 = 2-verify-custom-allow
test-3 = 3-verify-custom-retry
test-4 = 4-noverify-success
test-5 = 5-noverify-ignore-custom-reject
test-6 = 6-noverify-accept-custom-allow
test-7 = 7-verify-fail-no-root
test-8 = 8-verify-custom-success-no-root
test-9 = 9-verify-custom-fail-no-root
# ===========================================================

[0-verify-success]
ssl_conf = 0-verify-success-ssl

[0-verify-success-ssl]
server = 0-verify-success-server
client = 0-verify-success-client

[0-verify-success-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-verify-success-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-verify-custom-reject]
ssl_conf = 1-verify-custom-reject-ssl

[1-verify-custom-reject-ssl]
server = 1-verify-custom-reject-server
client = 1-verify-custom-reject-client

[1-verify-custom-reject-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-verify-custom-reject-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedClientAlert = HandshakeFailure
ExpectedResult = ClientFail
client = 1-verify-custom-reject-client-extra

[1-verify-custom-reject-client-extra]
VerifyCallback = RejectAll


# ===========================================================

[2-verify-custom-allow]
ssl_conf = 2-verify-custom-allow-ssl

[2-verify-custom-allow-ssl]
server = 2-verify-custom-allow-server
client = 2-verify-custom-allow-client

[2-verify-custom-allow-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-verify-custom-allow-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success
client = 2-verify-custom-allow-client-extra

[2-verify-custom-allow-client-extra]
VerifyCallback = AcceptAll


# ===========================================================

[3-verify-custom-retry]
ssl_conf = 3-verify-custom-retry-ssl

[3-verify-custom-retry-ssl]
server = 3-verify-custom-retry-server
client = 3-verify-custom-retry-client

[3-verify-custom-retry-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-verify-custom-retry-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success
client = 3-verify-custom-retry-client-extra

[3-verify-custom-retry-client-extra]
VerifyCallback = RetryOnce


# ===========================================================

[4-noverify-success]
ssl_conf = 4-noverify-success-ssl

[4-noverify-success-ssl]
server = 4-noverify-success-server
client = 4-noverify-success-client

[4-noverify-success-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-noverify-success-client]
CipherString = DEFAULT

[test-4]
ExpectedResult = Success


# ===========================================================

[5-noverify-ignore-custom-reject]
ssl_conf = 5-noverify-ignore-custom-reject-ssl

[5-noverify-ignore-custom-reject-ssl]
server = 5-noverify-ignore-custom-reject-server
client = 5-noverify-ignore-custom-reject-client

[5-noverify-ignore-custom-reject-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-noverify-ignore-custom-reject-client]
CipherString = DEFAULT

[test-5]
ExpectedResult = Success
client = 5-noverify-ignore-custom-reject-client-extra

[5-noverify-ignore-custom-reject-client-extra]
VerifyCallback = RejectAll


# ===========================================================

[6-noverify-accept-custom-allow]
ssl_conf = 6-noverify-accept-custom-allow-ssl

[6-noverify-accept-custom-allow-ssl]
server = 6-noverify-accept-custom-allow-server
client = 6-noverify-accept-custom-allow-client

[6-noverify-accept-custom-allow-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-noverify-accept-custom-allow-client]
CipherString = DEFAULT

[test-6]
ExpectedResult = Success
client = 6-noverify-accept-custom-allow-client-extra

[6-noverify-accept-custom-allow-client-extra]
VerifyCallback = AcceptAll


# ===========================================================

[7-verify-fail-no-root]
ssl_conf = 7-verify-fail-no-root-ssl

[7-verify-fail-no-root-ssl]
server = 7-verify-fail-no-root-server
client = 7-verify-fail-no-root-client

[7-verify-fail-no-root-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-verify-fail-no-root-client]
CipherString = DEFAULT
VerifyMode = Peer

[test-7]
ExpectedClientAlert = UnknownCA
ExpectedResult = ClientFail


# ===========================================================

[8-verify-custom-success-no-root]
ssl_conf = 8-verify-custom-success-no-root-ssl

[8-verify-custom-success-no-root-ssl]
server = 8-verify-custom-success-no-root-server
client = 8-verify-custom-success-no-root-client

[8-verify-custom-success-no-root-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-verify-custom-success-no-root-client]
CipherString = DEFAULT
VerifyMode = Peer

[test-8]
ExpectedResult = Success
client = 8-verify-custom-success-no-root-client-extra

[8-verify-custom-success-no-root-client-extra]
VerifyCallback = AcceptAll


# ===========================================================

[9-verify-custom-fail-no-root]
ssl_conf = 9-verify-custom-fail-no-root-ssl

[9-verify-custom-fail-no-root-ssl]
server = 9-verify-custom-fail-no-root-server
client = 9-verify-custom-fail-no-root-client

[9-verify-custom-fail-no-root-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-verify-custom-fail-no-root-client]
CipherString = DEFAULT
VerifyMode = Peer

[test-9]
ExpectedClientAlert = HandshakeFailure
ExpectedResult = ClientFail
client = 9-verify-custom-fail-no-root-client-extra

[9-verify-custom-fail-no-root-client-extra]
VerifyCallback = RejectAll


