=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-prime - compute prime numbers

=head1 SYNOPSIS

B<openssl prime>
[B<-help>]
[B<-hex>]
[B<-generate>]
[B<-bits> I<num>]
[B<-safe>]
{- $OpenSSL::safe::opt_provider_synopsis -}
[B<-checks> I<num>]
[I<number> ...]

=head1 DESCRIPTION

This command checks if the specified numbers are prime.

If no numbers are given on the command line, the B<-generate> flag should
be used to generate primes according to the requirements specified by the
rest of the flags.

=head1 OPTIONS

=over 4

=item B<-help>

Display an option summary.

=item B<-hex>

Generate hex output.

=item B<-generate>

Generate a prime number.

=item B<-bits> I<num>

Generate a prime with I<num> bits.

=item B<-safe>

When used with B<-generate>, generates a "safe" prime. If the number
generated is I<n>, then check that C<(I<n>-1)/2> is also prime.

{- $OpenSSL::safe::opt_provider_item -}

=item B<-checks> I<num>

This parameter is ignored.

=back

=head1 COPYRIGHT

Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
