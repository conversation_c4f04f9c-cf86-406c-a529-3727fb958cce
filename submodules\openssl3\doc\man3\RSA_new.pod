=pod

=head1 NAME

RSA_new, RSA_free - allocate and free RSA objects

=head1 SYNOPSIS

 #include <openssl/rsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 RSA *RSA_new(void);

 void RSA_free(RSA *rsa);

=head1 DESCRIPTION

RSA_new() allocates and initializes an B<RSA> structure. It is equivalent to
calling RSA_new_method(NULL).

RSA_free() frees the B<RSA> structure and its components. The key is
erased before the memory is returned to the system.
If B<rsa> is NULL nothing is done.

=head1 RETURN VALUES

If the allocation fails, RSA_new() returns B<NULL> and sets an error
code that can be obtained by L<ERR_get_error(3)>. Otherwise it returns
a pointer to the newly allocated structure.

RSA_free() returns no value.

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<RSA_generate_key(3)>,
L<RSA_new_method(3)>

=head1 HISTORY

All functions described here were deprecated in OpenSSL 3.0.
For replacement see EVP_PKEY-RSA(7).

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
