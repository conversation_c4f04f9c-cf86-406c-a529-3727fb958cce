{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 476, "header": ["Test vectors of type EcdhTest are intended for", "testing an ECDH implementations using X509 encoded", "public keys and integers for private keys.", "Test vectors of this format are useful for testing", "Java providers."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format.", "InvalidAsn": "The public key in this test uses an invalid ASN encoding. Some cases where the ASN parser is not strictly checking the ASN format are benign as long as the ECDH computation still returns the correct shared value.", "InvalidPublic": "The public key has been modified and is invalid. An implementation should always check whether the public key is valid and on the same curve as the private key. The test vector includes the shared secret computed with the original public key if the public point is on the curve of the private key. Generating a shared secret other than the one with the original key likely indicates that the bug is exploitable.", "IsomorphicPublicKey": "The public key in this test vector uses an isomorphic curve. Such isomorphisms are sometimes used to speed up implementations. For example the brainpool curves are using this.", "ModifiedPrime": "The modulus of the public key has been modified. The public point of the public key has been chosen so that it is both a point on both the curve of the modified public key and the private key.", "UnnamedCurve": "The public key does not use a named curve. RFC 3279 allows to encode such curves by explicitly encoding, the parameters of the curve equation, modulus, generator, order and cofactor. However, many crypto libraries only support named curves. Modifying some of the EC parameters and encoding the corresponding public key as an unnamed curve is a potential attack vector.", "UnusedParam": "A parameter that is typically not used for ECDH has been modified. Sometimes libraries ignore small differences between public and private key. For example, a library might ignore an incorrect cofactor in the public key. We consider ignoring such changes as acceptable as long as these differences do not change the outcome of the ECDH computation, i.e. as long as the computation is done on the curve from the private key.", "WeakPublicKey": "The vector contains a weak public key. The curve is not a named curve, the public key point has order 3 and has been chosen to be on the same curve as the private key. This test vector is used to check ECC implementations for missing steps in the verification of the public key.", "WrongOrder": "The order of the public key has been modified. If this order is used in a cryptographic primitive instead of the correct order then private keys may leak. E.g. ECDHC in BC 1.52 suffered from this."}, "schema": "ecdh_test_schema.json", "testGroups": [{"curve": "brainpoolP224r1", "encoding": "asn", "type": "EcdhTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000447a927daafd7282afd721c141c268be12312066c1d62fe047dcef272958e02a2b81c89d7b81004eda65cb6e1df8da330be2d563221862b81", "private": "008cff3b0b5c1b3220043759320c6cc6392e19e554f180f3df1e44c7fc", "shared": "4f7afb302c699bd56db0bdac550172bac94b73b0f2b60eee91b6a420", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "3036301406072a8648ce3d020106092b2403030208010105031e000347a927daafd7282afd721c141c268be12312066c1d62fe047dcef272", "private": "008cff3b0b5c1b3220043759320c6cc6392e19e554f180f3df1e44c7fc", "shared": "4f7afb302c699bd56db0bdac550172bac94b73b0f2b60eee91b6a420", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045f9a3dd8daebed9583d91dd75c6ef089d325ffafb3639fb97a7a2dea04e811b5cb44e171a5ded25c22f37fd10e1860416e4edb8aa8081c0e", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "00000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045c753c63b9cb9b79ebbd8d4e3fc3ae8de3ed6a16d12e38b1fc23e9e5aa6d815ecdf607246745e02982919a91a4cf8f1443fa90dd8f70a57b", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "00000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045d8de5d817d49856a9ae870b5c84824f1a19745e963c820fdebb553c06f87a56e4d0019f1192f904285f5538c1d3eedc059e77344e7d4b8b", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "000000000000000000000000ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004539b0566588e16505e2d39323c07799a6aa288e7d2aa03db8a392a388aad13ac01879d1cde648dc8ad70c2dec49cb67961b2493ddad8724e", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "00000000000000ffffffffffffff0000000000000100000000000000", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000426287f17f2554aaf1e49c5594fbc35059a5c7c037994bfff9cb437f33d0e5fa3400163c4a0ec513e7049edeac43f7463b99f1c9144d454d9", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "00000000ffffffffffffffff0000000000000000ffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043ac67c437f5cd2b4bc78218b01f64130c8b217070f6e1eef5ad4b600242505741417f79fd28596192538a9438126a49f44f98ed2642aab1b", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff00010000", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048ab3fa0ccf52a661ea3a1d50219c0cbf809bcbf63745f4e0b94a890c01c166c6392c3690f7ec28684857ea05f5fd4b8b4e7b8b05a9e94265", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "0003fffffff00000003fffffff00000003fffffff000000040000000", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045a6ecb1ba881bf4df559980cc940e66e430bb22b6dd0e6ea1842e90caa0c8cd725f93c45985695fc3a78c42ef72c190b083db57ff85c3db2", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "01fffffffc00000007fffffff00000001fffffffc00000007fffffff", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041076cc0201b3399137184b1731028ba2bebdcba1548ad5ed1af1df9cd2f4abb6d76591efe7712bc3544a76ef53bed9f186a2c2d12105e7ad", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "30975f626ebd46b09e413513a897f6b74de5707bbbeb7a3d8c60d0b9", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045216d31c450635af6d6c7327d3fe0514a8abb972b16d3084164c9ac761817518c5f4e78e277e009c7028ad7e59a3a87e13295ef1777119cc", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "373bbcf4231b53083d36adb3f56485603b19b4b77251abd9b8683c12", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b1121e4cafa5ee5cca2a9b661dadb37a819c0bb32dc0f01680c8192913981ac1c68e6d410cda305b0be46ef1422a70354199d0f544176195", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "41478d4c207e3fe1aef7fbcc34223f39b86f634d978921ecb40a5136", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046e22eb98da25e639954eb2987a1fc71605b287abe4a9e7fe4cd79c4bc759b111ce37a17fda313b17393c08c750ecf1d38a9e0f72c0c1705f", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "5394eaa3dbc30feac5eb7d88e69cf068315ccb6dedf787dbf933f823", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004a60d25227568b2486b402af66d9212fe923a37873a8a5613ef813d91c88aa84b15b7cae6ab6478f0bf42c464f3f50ec27d339e85a4908841", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "7ffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000499b409afecf5e544d0477a8a43d6f4805ca19d0b24b89d6a372556060695d60a6c4c321043cb40a501a2781c77fb9c8f370c8af801069e85", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "7fff0000003ffffff0000003ffffff0000003ffffff0000004000000", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004772b186ddd36192f7a73f10372c86d6a7e05200e0dabffe07e02bc9ea13a2429f0a30ab26de39bfafd44e37f14c79acee2ca7fbf2bd8f55c", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "7ffff0000007fffffe000000ffffffc000001ffffff8000004000001", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d54fbb07c8c0482600908178438fd572eca1ebafdcd101071afcb247a0561e4e2201447d30db350510f0f8afffa228515de9289516855d2f", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "7fffffff00000000ffffffff00000000ffffffff0000000100000000", "result": "valid", "flags": []}, {"tcId": 19, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004baeaa482e45fb4b932c945eeade8644f980e50f4150fc4ce1d02ecf6a883c9da5dc24c149b9b4510da27db8df3b3afbeaa629e19d99be628", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 20, "comment": "edge case for shared secret", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d63331a28bd6f82dcbe3de0ea807431012e954fd9febcc7a3687dbabca532329351b6a7a0648ab34c6aac123f8dd4ec8a44fb88df0ec45ae", "private": "133ba3cdfdd2b6f8e12c4efb844dd1ba60212b3c4afe6476f1efa12c", "shared": "d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe", "result": "valid", "flags": []}, {"tcId": 21, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004000000000000000000000000000000000000000000000000000000014ebc9078ad8ad07562cd41b374827192aa88ce3c718a014405eed475", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "609042ac307c7d75ee58690d36d1f0ba2cb70f1a77ca43bc7bb70067", "result": "valid", "flags": []}, {"tcId": 22, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004000000000000000000000000000000000000000000000000000000034f735bbff1a54db7995b03c4e43c17bc3e0f30bda4b2eaf85189615f", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "9d5cff406488bae680f1747d8f5ef070f3a7d451205a1ba9a6cd19fc", "result": "valid", "flags": []}, {"tcId": 23, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004000000000000000000000000ffffffffffffffffffffffffffffffff541d4608f6b28dcc77f02d94b1e25428a22e64fa9b3354beed97af99", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "a55b6ca4b1e135cf1df413a50861917b3d10ffef98a29a874ba9563f", "result": "valid", "flags": []}, {"tcId": 24, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000000000ffffffffffffff00000000000001000000000000004946136fcdd34a5225b09a844d9f482e56a13c46b4ef294e9c049f5b", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "9240e268b133e25b39b36cb7bc9e91a535ce622b48d3b0701d31b2c6", "result": "valid", "flags": []}, {"tcId": 25, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000ffffffffffffffff0000000000000000ffffffffffffffff4bdc280b52c98b4bd52bfd4fd2676a97610c4946a66abef9bad0adaa", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "b552615bd4ea7eef4fef3e39aa4fc511d8cbaf40da099e47a611e512", "result": "valid", "flags": []}, {"tcId": 26, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0001000060916626da518ee8f6bad287e697307084e191ca79398148141e57db", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "5701c9c1c1e59e93a0e57addf676c0d7646ff8407de630e5ec7255cc", "result": "valid", "flags": []}, {"tcId": 27, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040003fffffff00000003fffffff00000003fffffff0000000400000005b5f99e5ee14baa1c4fa21adf0a6557416c603f933a592dd8dd16d66", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "07c0ae0f2e52e4657fcd5dcf95c5d925b0534da4d6a113e1406580ad", "result": "valid", "flags": []}, {"tcId": 28, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000401fffffffc00000007fffffff00000001fffffffc00000007fffffff572544766fb80fb67365c9292c5f70bc285388d705155b361eee3a31", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "2d4f54d23c12ee51456f8047bc9c9bf3829192beef85bc6160029915", "result": "valid", "flags": []}, {"tcId": 29, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000430975f626ebd46b09e413513a897f6b74de5707bbbeb7a3d8c60d0b95fb0ba1f08e4e15dfe79c1b2f6614b9df6bbdd57112f52ada01d4f3e", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "466d58ed8630d222335899a419deb03b9f3ddacb314f1ea8ad0ce2c6", "result": "valid", "flags": []}, {"tcId": 30, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004373bbcf4231b53083d36adb3f56485603b19b4b77251abd9b8683c1230c5f383bea0bf35359a47ebd97417b93e24f3d2e78badd6561893aa", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "c0c682a0745b197dcedf8362decf70c0b5135e16c4f7c63b10468501", "result": "valid", "flags": []}, {"tcId": 31, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000441478d4c207e3fe1aef7fbcc34223f39b86f634d978921ecb40a51361e9f6a7174b801cedaa4d4bcd772a2884d0a2bd27356295c88573203", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "72e9c06decee8133bb05c483c97a5144351d3a7fde2fc88be6eefd7d", "result": "valid", "flags": []}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045394eaa3dbc30feac5eb7d88e69cf068315ccb6dedf787dbf933f823661267e8aefb70da15181fbf7390e72a4680b78883bea3a09087ac50", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "42e46a0df978b3362a4e546ea65f889f101b4e0270cb99b5ed046554", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047ffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff65d425acdceb1b04862ea9df634fcad6f1af3b70ca0000015b943550", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "4d51293480c1208a093504c4c96a4c33466c918d300a8566e69d4afb", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047fff0000003ffffff0000003ffffff0000003ffffff000000400000002ab72fec57251d40ab8f358a291933878b14635671f9e0a8088dc73", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "7bedf280c9880edf26fe8dbd7afa356a54a67a93a2bf7043d2f497ee", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047ffff0000007fffffe000000ffffffc000001ffffff80000040000011f4ecfad07f9a9656935f536a8b06e0db0acf5d54a939436ea844480", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "c56360d832e7d3854f60e0f3d362e58eb8026e27b6161fb2217ce57c", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047fffffff00000000ffffffff00000000ffffffff00000001000000003532d972042e05e13fe9d8f44800502161c9c5ffa30f5caf151d600d", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "5a0e6ab7b41c61b1cb098449c707f74f55548b92c78af27d3f33c084", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047fffffffffffffffffffffffffffffffffffffffffffffffffffffff515853ec16a985fbde8da662e47299e86ee30ad464acb849f5132922", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "b4e21f2403d826439e4d94985cebbc724a85a3e405aeb8f610892ec3", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge cases for ephemeral key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe51bd78396f681cbf535b3951d0b7edf91abd3b10e1cb5cdeb7ba8f9a", "private": "00ae3ad02b042445f5edd485a9370286498b83b82ca6705fb09425dd35", "shared": "92d2a63b7788e0d704ecda5dc59321dc30264445763da792f7035832", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040c603a056fc7c39b5cb098ec919e8688603bdaa27ae20071ab1c5d9a0c01846a7c8ea8b3def886d5f4209f9cf3c45a7614e441e6f82ee0c1", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "a0caf5935ab3e9d5a1fbd2e95b2438bd82056ee326d58d9c13bf6cce", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000467cf644504fff409b432279d6d19905981b64b036390b03ac512e04f1232af9c5b4e0c73a0682c2be3fd340a445fe144c3dcb855ae4a544e", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "659f942e949f831c010e9858b92e53ebe77ed40fbca352e0b8c48a71", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048f5f0b76cabec293413084bc7e4302d63aee2e7ed8bb4d65f44dc17654efbb1a154b7e9514749b711a7f856bdd05863a6a4a796a922f1283", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "38cc293e07ff307dfc3b6bacc76e02334b32c55e81d7794081fa7c6f", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004cb60faa4b67ba2eacd679738e43350ff50632cb51cf88983d3ac636519a18fc1134676a47b4c963a8be628468077987b987777d58a5c59d5", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "2460ad7b31893e444df618af075306b9c2d8499aa5e94055dca77f79", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043f7107e2825dcbe6e9aad75c05ab580e13aa3747c298b35fef4eb52f48720e075ac77768d83fa8c2a2a38d51cbdb8e0d6a1587c694e0fddf", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a0b62d36a5c9b6821276b9338e038bddb5d3b5832ed8e1f9f2d83dc3", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge case for Jacobian and projective coordinates", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000410499af4aa2a686a8605abb5b3bf007d9bd3ef1e6f246f4033802eff9d65d9d527fd91a9aec32b7d92fd5c6d7367d16fbfda0a2dc7d29488", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4f801599242655102d0a22f87d2f78aa948bc812ac34856b80698e9b", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041eb82f61b03bd6bebdddc43a2f651a0541e2576501bf5f962b858dbacf141078da890b0e53cbbb6ac463afb97782fbd684b144aedd83bd31", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "17af73cad5e3702089d1250044bf416be7735e62dd486b85310d224a", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047f902c017f9d2ebc84e1705a0506436db1b31cd45c0445cd1d1d0d21913279765bfa0cace74f87763e858ca81292ec4b9d4c3dc414f809c5", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "60aff74834a1224f2d35110bc62609e4d063df54bf5feb2286373cff", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c67de0f4dd078191a1c391d1e76b93b760f2e8b12a74cdb2a03da29cc77f400d21ce7b135980ba918763481d823fe9808107e425655ef292", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "a77bdf8e3900c30abfae7edcaf1d26d43288fb35d9bcbb5a9227a0c2", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000433b006d4d5654fc570368ab604f54620f9a56bd4a56b2e87ea208669d7a38779bd1505eafd26f887583ecd1db31515221b21d3e87f69f10b", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "8d6590ea4e4ffb6c29fe61b37b2f127d9d79d314a365543288b3cf17", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000493be47422cceeaa1d6a24205b0ae3ee70831f4768af5807e0d86a77e6b1e03263a716bf38a5febea4dddda6c05ba7c2e25775feac8ccd5c5", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "c252b59718c8f2f49992f4469ff391b1e27aae07208c83bcb8bb3627", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043b44947836047e3db6a4d990f75d547c7902ae1faae67608ea38812331e016cad0dbc3598592a6594191b87e7f1880e5be532a09057d95bc", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "d34a8ece568b3dadc9757990cd74210584c703f8bb9fc76cc29caf5e", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b58e2e426d9fb1b04838bcae55ad81c18e9374f52d000f2d7ea186cb6f3015b083e3cddbce3a28cdfe5ad367ec6f5028633ba9fe8a8ad6e5", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "319ac4f1f5afc65ae1db812e9cc3c861e0f43c77925104372856f364", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b2f891d51114b1da3ff3952df2060a68744bf6340129a3985c22ead40ab8bc2fe667cbe346d7e04556e13bab132da493310fa81d48fbe5d1", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "6ce8ac2032b7248b39eb220cc7af234cf53725f462af530662a95125", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042bf43bf37242e397e8e78633a30ca2c24bb4ba5fdfd1878e2b40629d54b1896c2e95afc5ce6a951f63f8597e66b21cc06a9599ebf66f6542", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "acdc7945d54ebd8c00d22fb39e0e9ef8495b9cae642a34d79dd92c07", "result": "valid", "flags": []}, {"tcId": 54, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b92faea4a924440c2c0ee4342f45210035f83aee1e43228907be1bd69e3f62be11caa4525a194c94918f555b0a507d2b307ee5fab35b8dde", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "4b2f6258b4419dd73f275e8fded5df56d3a1c14cae2f7983d1bd9fe9", "result": "valid", "flags": []}, {"tcId": 55, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041c1301c85150acacdb428c854d214f5a1cca4c2e1f4468dbc4d3e0414f4f50d73dbd1dd7cc6180e101d5c9e265ac7f545844076adccdc6c1", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "8e209397bbee6ded3fb01eab811e998f314f8d4eb3c417143f59eede", "result": "valid", "flags": []}, {"tcId": 56, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c8d5cb1cd175845dbf35b330bef247726f4bdac9396ea3a4020d31364adbb5ec1c541b56869ffb7e9a088c39b40cb126e00b37a7e123cd06", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "0af9b8de7f20705ca38b8a0b31468971d120bc6c22a65bc5ebe1a28e", "result": "valid", "flags": []}, {"tcId": 57, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b3796cea497c6585d0b7c7e3c71d42aa18481074a29ef25fb51c4694c6c930986598f48a7c04a275c44952e53a94dba35884bcc9e4f41e56", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "77ce5baa58506ddae3164b4404541f3d57dc1600d2320b143c299659", "result": "valid", "flags": []}, {"tcId": 58, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043a738da406fb3557c2abe2f7f4cece40e471492774dd8db568ede64cc0f11b9ecb3538d3999128fc548d5d65487563ee5f607bebe30aa498", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "9efbd6be9966e25834225c5b1a72c5458670bb4704aed55f98626711", "result": "valid", "flags": []}, {"tcId": 59, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bdb86f0806dc7151b0fbc1b1100000cb8a0e3812f9c1bf702bccfbeb33ede8b94056646db37d38c83d4013c8c09ac09ea982be0cff2eb335", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "40ab16bc2e607e10a79ce297f5ee699135699fd55afe55430aa32e42", "result": "valid", "flags": []}, {"tcId": 60, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004868ec4eb5deef517ab053cd91eb9e5cfb4990f0d8be2b78181072a5d0ffcda3ca1138729adea01fe653c06e4fd078c635e50a5162d49607d", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "a1f73b33cfea917747286794f358918a242c0b9e6aea7468d56b49bc", "result": "valid", "flags": []}, {"tcId": 61, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043011101a41a674f3cf53bf1401884fcaaf7fb82228297379bc7924ba0a0956819aad7472957e90c33e819cfae0ccbd4e0c49f6bcacbea9df", "private": "00a8404675dee81d0be705d90db8f1b2b9ba1d6b212b09ccd318121f21", "shared": "3ea3bd6d302be28d537d9a346c05fe4d91b4ab3c839e9a6f53baeb9c", "result": "valid", "flags": []}, {"tcId": 62, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000421ca1e971b37a1700b664f441cef0f0699b2fd1b83494f1bc25c0212a01e371b8824466e76cc434dbdd909c0075b67b3c318d287c72ccf59", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "42be912835c34e45b9cffb6b88a964b730bed932c1546c6bec36fd6b", "result": "valid", "flags": []}, {"tcId": 63, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bc4c9c1eebb04175c5d69e9e7ed1df90749d924006ed3b9aa3270abbac9ca334a4dfbb7902782a6f6acbd3b9ee842a528e189a2be823960e", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "8f827c779cf952d17e9257452864021dd1fa8855b590f619665ee4e7", "result": "valid", "flags": []}, {"tcId": 64, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004acf835ddf60ed01777ad8a19e324da3477d881f625cd0ae19c4f81e2bbc0e57373d7e764b32d168cfda2eb32d1327d6157551a10eb79fdca", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "855887d8a26614efa81e511dbdd12d1a80391fbd967bb8aa65cfdcee", "result": "valid", "flags": []}, {"tcId": 65, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045edb9333abdd33dfccab64dcc68fc357b84a644cca45db6d71a761a7d1c8c015a84076e6127cc6bacdb1f782109bb7d0ec4fac802d5b8f1e", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "775ba36b9a756f406bf3958dfd1bcb77fe7212eecedc30d940d28a33", "result": "valid", "flags": []}, {"tcId": 66, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046f31003b2069b5051e2922534454f9435081a6e50b107672334da0d838c89bb6b9a3c0d12295ec7a0eee475f7f5f457017a03334e8558b63", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9deaac0d2f1b895a839e90cf20c88056840d6511a97412d1a9c59b90", "result": "valid", "flags": []}, {"tcId": 67, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046d84b5f321d7a96adc27a225abf66875ee29e703fb04f9e8bf3c0d1ea2facbae188e28dd45c6967871dbc8dd1c612c6d5ab52a19747062a6", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "862ad2e6061c96f3ebc71642be4b17eb97fa2ac7677d91b2202d1277", "result": "valid", "flags": []}, {"tcId": 68, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000410462ce1b183643744e1b3bb104d492ab581443193de278a2d086e0694ae45f5b3c2073e48dd023971b2e23d03660f5f137f4086aef50420", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "b57376784247802da7f054fd07ba81faa5d61f59ea2356c655446ab6", "result": "valid", "flags": []}, {"tcId": 69, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045670777feca5d826e72c3073ea1835a0b484d62eea4a4081659a8eb64e75ad7f58d8e58cf9e00e957dbbb77752e4e9f9eb1c3a7612275002", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "96d05f4a550eeba049589e44da192431c66d8f86645e4de213edbb47", "result": "valid", "flags": []}, {"tcId": 70, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042c16080a1aeee210c8de17d6c22d357d1a4623147e18417d68d6d071423d8015179c0264d0f2e3c076e0cf1f40d8197da9e326a7042fe6aa", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3fd17f96729653c9f5cc3bac0742b78a6e344647bdcb5626d297ff78", "result": "valid", "flags": []}, {"tcId": 71, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041430398d1f976394d4e296d0099a0286874d611e4b64883be68565bb05c29faa8e34e4d67535bc944c7231c59529ac3d2d7f3ddb243d0ba0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "6b13728ef0b76a8ee525e2ac95fb3da277c6717efc7c094e0c7afc33", "result": "valid", "flags": []}, {"tcId": 72, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044f873d7360c5248cc10675e03b4980adfc5e6b8a7d21bd5f9ffdaecaba0090620510679906d34928a2fd0385f8cb33601fa114ab1aef2997", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d3f03a598b7d2c43f1e8ba952f2486c3c5b891b5b153f65fcc1983cb", "result": "valid", "flags": []}, {"tcId": 73, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000424a382b60976d1dd7f16d2e49557774a56a3d5dda3dfb3916dd830de1db332927b328f8e812cbbf40f68604138438322fd68b062e3d02340", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "122900ad2d884417dce5e2430f09004aee9679df7e265812b57a199f", "result": "valid", "flags": []}, {"tcId": 74, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004484233e91dfefe7bb9ce45c3fd4ddfbd482e745510b9a44a7c75da6ec96e3bc4b7fa3331a42e5371d6a5ec5489b47d2195809f9fa92b5da2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "774a895653301703834d3107d3e4ab0185de667631781cbdc1126cac", "result": "valid", "flags": []}, {"tcId": 75, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004257f5ff6a98904fba31584672fdb05e972bfba5490c5e370b055fff51dfd3cad994b92a2f41f1b5c2b594f8a0e7fd929ccae5ad187b44d6f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "2557bc44e681e4aecf5756183c6143901fa7c9fb366442b556dc9388", "result": "valid", "flags": []}, {"tcId": 76, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000423ec2d535843139755eac1b6fbe4eec772cff6935bea57c53159889b469f31e284254b06583ba0fcd75cc3178e502e24eab54832e5e94d52", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "19a979b7787b227c4df1dc71bfcccd5747043bdb6110c8ed6991d7ee", "result": "valid", "flags": []}, {"tcId": 77, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042383f640dd90b15cb89c4401d24a82dd67f16d12ba9a96a220ed574312249707c4f687bdc02aefd828b5154a58573239e618c7a8201626d0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "c5a679539a38a8d44b91e216ebbf2c280646823419eae4eaa3b55d74", "result": "valid", "flags": []}, {"tcId": 78, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004020e3711fcde85e4ed95d2eaf4b09dc9bd98be60c3f80a865430ea61c9029f67c4b4d1116b96e43f2e5b05ed0b41e116ab2eb65e917ff5ae", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d438bdf88a081101cb6d1209f72af52aec1d41ae7c4a268e4fd722f6", "result": "valid", "flags": []}, {"tcId": 79, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c29d38549df337366d2f0512515d726b4878aa0505dd04e39dfd27e5875bb28f326f342723112d86dc8626efe29b4eef6e3e665463a9ad5f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a3eb0ca5f9371d11a5144b5ae21046b8513db9ca274c0ed755a3c57e", "result": "valid", "flags": []}, {"tcId": 80, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004789617298b6399556a16f8d37b9f2d605a29ee5f8b486708ed02c61814b2459bb29e4b9e261cfa4e33bd1a919682fc1be3aa37d66b6a217d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "96f0aacf032b067061aa809c8f9eca2a6cb1e2b517214f3edf41b13c", "result": "valid", "flags": []}, {"tcId": 81, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b9ac60fa3353dae51a4aecd37c404aff1dda95d7774c7c6d9b1b0164b2efc2019c26011b992894b8440d47d1cf66e2272d30e628b5b813d8", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "b99be70f5da71de573cfdb0347e08ed9f49ace97e94428405abbe47d", "result": "valid", "flags": []}, {"tcId": 82, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000477818ff0dd804f640b8b204f31820139b144d14a9a551dc7675e2f79d5ad42188ae82647364e7a1118e5b0b16c24b73a02bcc2dc56c3125a", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a3126484797c491204f8625b8d05da88cb9e064d981e9a1fe22b54f1", "result": "valid", "flags": []}, {"tcId": 83, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047c423d1eea0388eed7e83cfa351c6702f501c19b1b657d44af487c93a0d10e7ebecd315e346d7423398cc627d1f07a1da4b3a30b47427976", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "82e9487b1dfd2fcd9560b27f8d51c561a6da4b3c3910633cb1523269", "result": "valid", "flags": []}, {"tcId": 84, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004a8a8758dedca0cf58dc2ace02160ee7a167bcc022bda3cb6994a2ad12f01247293552b37bf8c4cc047c2def1a1f243a43811c78a07f38964", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "978a2974670265f3d3e887659f255aaab3bc378d32a704486b5f731e", "result": "valid", "flags": []}, {"tcId": 85, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004a9bdee88108db625525e7f5b13d9fa4636c3201e7e613c2a99de7dfc7f282161652d6942a728e5adffb73d209f78eb2c707d43a71b2d1cbf", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5ebbd01a878042b746184a689b12c78e5e819cc9ed3eacdbb3342ed0", "result": "valid", "flags": []}, {"tcId": 86, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047c1d15d7a7b6d78798368a09f671bec21d295a7ffef01e85a396d01e35519d7c3f137e0775ce8c6bc892d00a6c33d8f4440f4a5b73b702d2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "525ca8f0a8d594c3aeb58091d895cfea89da0fb11bb467d90ea6b555", "result": "valid", "flags": []}, {"tcId": 87, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004356c86e52ae7dbb9de8e5cca75d22f2b8c425003d3e8b742049415fd4e572b6ec47fdd498b3be0f68c8a5ab891f6d56059f628d84bbd6a32", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d095338e29079016219a88fe9a254a68e3aa20beb7414dc148e25ace", "result": "valid", "flags": []}, {"tcId": 88, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000472a204e4c45be398f2e30b6e48811b4cbe18356f107286c15c3cd916cabbe9ba621f8bc04e9ddbc1788aed01dbca1a429d30215e9dd51283", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d6bcc7f523c1f03765ea6b874ecfa6bc7c9216524ff1238562b121a3", "result": "valid", "flags": []}, {"tcId": 89, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00049abfd0558a82299e9359bfb0f83382c6491534b4ec41682c4a862bac29dca17976181a72f6d6f6f87bf76152911e905d2182587eca65651b", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "0a19c034534d61d0b40e7b7f35d02986fbb9de657bb5ed0c3bc05468", "result": "valid", "flags": []}, {"tcId": 90, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b2ea1fedcac6d9627c9d9badb81bdfaa5b8b8d026737bf1d55411abf6d04e1fbbafd3c9a4c3935b83aee6dce8806868818753d5635e39162", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "ce86fc96338cd0b2b7349d9a972c01d68b6f3e7cbfb706e72faae107", "result": "valid", "flags": []}, {"tcId": 91, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044f3d27d07618df2e7ba2430b0310bbd21ebfd9c74549f51a6f2f390c4d378a8f92296dea64f1325be426f429ccc319a5874220e4d73be6bc", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "25bcdfec78296e0e676a17ddad9055bfc204350cb84b8a0f4d055059", "result": "valid", "flags": []}, {"tcId": 92, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000491e22400f1a18e23618b66896acc788b42d18f75e6d9db7e45183fa03a45c619e4dae7deee68b421ba1c0d89094e5b5b3527914db08e2d5d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "869857ec3fa648476d7aacdfe200185ef3d58e8c7f7395be69d5ffb6", "result": "valid", "flags": []}, {"tcId": 93, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000439638854f00d0853bdfdcefc2f4762dc6cce4c59a9936d4e5a2b21994cebf8f5c9c58db71c48490748d14a57fe20712f5e83c37ad58e6f7f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "256bb8520650e7400ea918157593bf8be561557949b84ea18b883ba8", "result": "valid", "flags": []}, {"tcId": 94, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048c462a2da9224b6161f60da3113a4dbd5f3510492d7a8a43f3b2521552f07b22216533a54f715f4fb51205e5093fae72398b2016db484583", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9eca45c88ccd1b5685479a95325ae21287ba5500d726e733a0c2742f", "result": "valid", "flags": []}, {"tcId": 95, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042394c1ee038bc5d695bfe3219a9c9033370eeaea9b614138e6117747c7a70e4f17583ed92a307ec0c6354be3e5d0d33f09f7cb134624ffc3", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "12a3e33f4916369182fbc96afe38837d58875b625175da53a0d155f7", "result": "valid", "flags": []}, {"tcId": 96, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000411740949356f15da6707a792741d804ced72e61c877b823b945ac0c010d4e8311ac745b4fc53ef8931ff3588bafab69896d7a6456a4a14e5", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "87f6d48bc3841aac6310ae65dcdf61977c9592efc9f99549197ffe89", "result": "valid", "flags": []}, {"tcId": 97, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048016e079b0eb140dfffe0e70d4416f04d6dc1728f65944e4331f63c5c5df4d435ccbb80cbe9f80202a3fe0d205c38f0bd3d9039f72bdbdf3", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "26e10662612fb8076629df7d2327c9233c32080f2c6c7e0afba2ed44", "result": "valid", "flags": []}, {"tcId": 98, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b5f30cb6e7ad215f4768a21c26f58734b1cbceb0a8e2791e99160edb780e259fb5f7ad1b9f4b090db029cb64ef831d5da4a6d54d13499a0f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "ac2fba737d3fa2fe4e0eb838b5d7c40af45d9aeb069764cfa6f3b727", "result": "valid", "flags": []}, {"tcId": 99, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043429145492400f8db41a49e52686982cbc9b0ad1058ad8b92b2345fb24ae53d260d51a3cc5efad1f9e2545a1512183c27b79a5d427dd09ac", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "16f461ffd6c2db45038e6afece890725a07a64fabef67e0c6b965e80", "result": "valid", "flags": []}, {"tcId": 100, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042ae5bab7289cdb31887ea5741a1d9999dc80c3bd56f1eee5d1f0cf34af72d85abd35801235cf182c74a259cb507f340b8f0f4d72fc3270ec", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3ca21fabb91dabdb09df339369f7382df66170ebceb0a5bff7f2730e", "result": "valid", "flags": []}, {"tcId": 101, "comment": "point with coordinate x = -1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe51bd78396f681cbf535b3951d0b7edf91abd3b10e1cb5cdeb7ba8f9a", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5fad1d3769f9d2e879d3b2c0f02eea85282b01bb0c902947aae51784", "result": "valid", "flags": []}, {"tcId": 102, "comment": "point with coordinate x = -1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004499288ec0e6f18f47ef53878806d818d94f93fbee09ea29a628de327a3ba98a47ee04408e650c889c2f31c344449f77b0906a2d6a436e33c", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "7b4f7e6dcd2aab2cfe10422fec61dcd2f328054d89979964fd821061", "result": "valid", "flags": []}, {"tcId": 103, "comment": "point with coordinate x = -1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040bf8765d0fa9d718236db06140991e1f4fa7cef3a96fdc2c60896cf5ca3e97a0ca3c3ab49e4f35a115669bffb0dc792470448082698e098d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d1199a490b5d664080b16edb6ccb4f51163e4a1dd7652cb7c0f012b9", "result": "valid", "flags": []}, {"tcId": 104, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bf8c23d2fdf67aff951eac5c822023402d350ab0a1a1b1f21b6b1d5e0e45bf5d6e430afec86697c743730e5fb93428d8ea37da1052e06127", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a04f2980084bec2db9b2eb5ddd09f0c3e0a4f767227b41c810fd3733", "result": "valid", "flags": []}, {"tcId": 105, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043223532a3e83948cc5aa188e161df354e4566093bd9e6ef70e1667fe493deaa9010058c94464204b032ded6874bec6ee343e215022452eb9", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "59d782a68eac86e422041567d2ade6cda2c5e3ce9b014619981f301b", "result": "valid", "flags": []}, {"tcId": 106, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045c2c8855ab283b19d4f4092ffed6a87f08766c9080eb692c5a62f29b5fbd386a00bb1928a046c5a26b45ea498aff295bca2210921dd01fbd", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d3793f509337d1ae39233313b9cc1cf9407dd61883f7c2bd5eb1db45", "result": "valid", "flags": []}, {"tcId": 107, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aa71ef47f92e7981175f89ac3b40b783927f307220cd793c68f37b58a8f85afbc86a0e60f88d4e7ac76ab07d233fe6f2e10e1175528008de", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "2a1979bf8f7e65c6469709efc1430dbcfece5efa8851ad03d11afbcd", "result": "valid", "flags": []}, {"tcId": 108, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045e145b797375f6c479415f23947b9a18a8a4baea6de05389baf0e4e158cdd128750e120caf61b31d5dbf00073afe7bd0a2681054961b75e0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a1fedca1961776bbb747667f0b123f99dd613ca6ccc7ef5036b4bc09", "result": "valid", "flags": []}, {"tcId": 109, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b4819bb73daea5103d7eb21f0bd94f3eee81b882af442611e1f7cfb1185807dbc266e5714ab0969821e242911f074f5e6804fb8f81efbf9f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "868e6d7950f2cf55a7e14bad667c677935cc0fc80d47d71f5abfc3f1", "result": "valid", "flags": []}, {"tcId": 110, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00049bdc2da490310e2e741f960f9c00268b20eb03042833293656bb3040d118fd17138c97c57f653143314b1d9f13df95a372936e654bdb0e50", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "34e22b7e1850a1d8f652f9b73f9093b56694f7c4b270b174ea8b22b9", "result": "valid", "flags": []}, {"tcId": 111, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00049ea4b5ea76d52de3ee13931f8aa51887976140c3cb67488de079b5f40ca0ce6862ee0baa554dfa419cd156d30e298cdfdcc4f87d4b2b64a4", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "179bb99d01fbec460bed504058c7edb0aece18aef25f6bd65da452c9", "result": "valid", "flags": []}, {"tcId": 112, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042e406a6239b493641e1ae280793032b93e673d8e1df3e176a587f9ccc9671a01ce377483e095c8af4b577b0e1da720fa01982d0ae5fb6a49", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "52666968841777db9243762d8ff25b9c7084942f15cc10b158d79817", "result": "valid", "flags": []}, {"tcId": 113, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048ffa1026de44e7af8d5a6e2270c2ef5d72b88349a32c0771e5d5c0ed67f2da5ef35599b4ca732a06a6a382960eb978793da250e97957fa7d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "30441e342dc1274bbb97d1033801beb7a0e264af9a51dc9e36de1b45", "result": "valid", "flags": []}, {"tcId": 114, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004108098184d30871b64b2cae0a768c95a8bb4b8339379e15736d4ad2d0d2ee5928ed89cbbafec81f79d3326e4fdb40039a0aede68ed8c85f0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "28e9f72c0f4939f162feb3dec52d48d1ff3a7f72c0badfef54b3b20a", "result": "valid", "flags": []}, {"tcId": 115, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b3fa92cbea39bc6974a8a7ff72fe31a91180b81941e4aed8a5a34123b2b7d6dc43d280078e158976ef51123b648ac83005eabc6330d570d8", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "c4c1901e5f136b9b020a1c1b47d34354d49f278d130754c52c7a1a1b", "result": "valid", "flags": []}, {"tcId": 116, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000433d809be0ffae538b4709bf8168803e76d8e19ff4d31992fdb0f0903193330608abef4a31723c61ce1abd4e61501ef8e67909fe6fb8f8a40", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "75eda60bddff285fffc585de0be97a23d88370d71450b92092eb6bd2", "result": "valid", "flags": []}, {"tcId": 117, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000424a277cdb25830b086c53a95de3101885674d8289329821dfc36546a51ba18b46a3971e7e04afddbe9198b5f3718c273950fa8ce398457b3", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4fd3a3fc7580b0f2361f79dfdc5e2ffdc02f42a937d3950402a73b69", "result": "valid", "flags": []}, {"tcId": 118, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045e5669b31c0ee19fa72444cd974f68201fe805a50f05edab49d4523590f748eb5e2f25e52dea590aab8be8b283776d1b379e0d63ee94ae63", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5f3ae760bc6d4eff26c307f1b7af6abd583634b3060450f0fedaf1aa", "result": "valid", "flags": []}, {"tcId": 119, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004595184d2e1a9daee2596b4522b747735b39c4701481ad5480a96b3e30ae2170edb1c0b743eccbd590036a1957430b6ce842383bb236cc80b", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "81e2752988e47a78ad9db5c5aa75b2c05650e96cf6eb241af25a6bc2", "result": "valid", "flags": []}, {"tcId": 120, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000433163ba5c826379ef4d5a959b316b154870f7abab89503f0444040e324ba3cb6096ee75279d3d12bb1e397d9543404bfce174d57d3d562fd", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "309371c137631b9e370c08ae96a145164313e79ab9b6e3eae39dc155", "result": "valid", "flags": []}, {"tcId": 121, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bd064e6658832df8a967a93bcfd0a394436b2d775303f43de6b58509be698d9ac412c9b4cb044e252313f6316770c3a9740400add98f91a2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a662af2c9415d9904d6df8d4f3b2c98541ca4199aedf6f0e881f711a", "result": "valid", "flags": []}, {"tcId": 122, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044a3cf5ae891bd9a8846535b3c4699f96d6393cfd1b6bec2211131e75093b7bc2923e9a4fc10273133b8fd3c8138e9bacee553bee92afac76", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "24a886773197a7b702c15bbe027cc7a5c398ab26df366a0333e7b0d8", "result": "valid", "flags": []}, {"tcId": 123, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040e10a80146998b4fb096c3b04516bf395fe13035170f0495fa1c1bcb3742d463d5d2ddc0b4438fddd15b9f0c81f6a98397629f91883f3412", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "0b699bc79a35eee79252e97d252f603c6c01c81591effc6de65914a0", "result": "valid", "flags": []}, {"tcId": 124, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004cea7fc146d257ec4f2c8da0c7a98f620c2af3f7736a7ee6e34ef2853322e6d4edb0d52ccdc16948da4f6ac408f6ef0174756038975ca6a40", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5d2a2a0f715b297a7136ecf31bd697e554c322599a111bc3bfbeecb8", "result": "valid", "flags": []}, {"tcId": 125, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004abcf057042e5ef97bfc7ccce9d52dafbb6da5e86b460de1c98af705c422158d9aaa3a6741ae10a1748a28addb4aec0d3e07eb31c2a50050b", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "adc1fa19f6f1b70a010324203793ae95f2e7d8cb61dcff4d3cc42c38", "result": "valid", "flags": []}, {"tcId": 126, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004be0e130a3b483e7873599e8c5ffe9aeeb7d1556a125932457666bf8d71588f8da778ad874255f5fb20765d8b1938fd965f9a568167d72c93", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "6b30282b495af0090b5f1035bf731bd6d1311d5ae739fa5b4326de8a", "result": "valid", "flags": []}, {"tcId": 127, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004863bc16dbc20b4a6dc58e1815fc6d1d14fa7df6d7a1f5912402e4494754ba7b0200802e26b0d4d265b0637fa8a5156aa3346c00e2c986358", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "6c3b3ba98b50eb191839a8268f026cd34ddf3ded12a899905247b4a0", "result": "valid", "flags": []}, {"tcId": 128, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004cc82a99447f492a2ef0ec488cc670b870227b55eadd4108bc8fbaa54c4543251952f71181f9e0eca09809a34f04d117eb5d40bd89b44c557", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "8e62c1a0bc6c2330b254f49d36a4b7a55578b82a11978a357c983024", "result": "valid", "flags": []}, {"tcId": 129, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042de07658698e3d055f44b29f00bf63af71b94c142939a941a6a091264a99a2d062359b846266ca7fb90b449bcfb26444601b3bd0aab81398", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4e489f49e2c2e6a477d9b3cb724f033564acc78ebfccc21bacd49b4f", "result": "valid", "flags": []}, {"tcId": 130, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d031c303d0b72cce50f266b304de88720f168262ff40255e8e0556f95e58ca62cba65cc2feb7856c1aa8b43b64012a71b15dcef43908f417", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "7ecd7ff36609de1bf7aada4b45e85e7592481674fab5f2ad1bb5040e", "result": "valid", "flags": []}, {"tcId": 131, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b362cd22bbf80b3384a2208c9d72fcc092b2b23a708e8d4a6fa55a2a9f1783b2ed2b3f546e29fedb2f230b85dff2171dfabc62b9fc1e2113", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "277ac6f2110e4605ca28fad60e959da91d66a2bf3d6ff5f396369fb1", "result": "valid", "flags": []}, {"tcId": 132, "comment": "point with coordinate x = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004000000000000000000000000000000000000000000000000000000014ebc9078ad8ad07562cd41b374827192aa88ce3c718a014405eed475", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "c6b51b2b01ff43f8cc36813f3dfc2d1190c3563ea7305d82a46037c7", "result": "valid", "flags": []}, {"tcId": 133, "comment": "point with coordinate x = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043cdf091f078400c7b3290c774c2c98f6cac49d69934e4c55358d0c4d9cacd4833f379ca5e587629cf6936e130b91c7f614349fd0282c2e65", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "48b0aa9b8602993f862ed947b845e3eb78209e9c39568f78f4a8c909", "result": "valid", "flags": []}, {"tcId": 134, "comment": "point with coordinate x = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c4eee1e529ddddbb69b8a26154b3467ae377fea8b52b1e73eeda35c0b73c160db946e31bceeb0a6b8c226491cc7749b3e968237ecf92e71f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "bcbe1d55fc19ad7b237dac1e2a8dc57a7c109194c770dc93ffda079d", "result": "valid", "flags": []}, {"tcId": 135, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b7c4fca76afd0135f79cba7dc4257fefaa1429bb6b162710962cb341ca9e6961e66370dd62b77e3018c67f7e524db906ee18ca1bebe9a6c0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "ace355e30c6e9db3b3c345554539f6b0d4aa437eac1d7da381c58e3f", "result": "valid", "flags": []}, {"tcId": 136, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004a2a704839ccde87449bb8e87c630dc2ed520f9ad7067d6ea460a5754b5a9f29c93f92a43e53a18159c3b996a5b589ea1fa64ff6fdb758fc1", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "bf8137883eb67e6c77050b9329aa1d3996167775bfee3209fb728f16", "result": "valid", "flags": []}, {"tcId": 137, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044e1c8b127d0b92f8f11e69b2721a8c0f1d32fc2ab88aaa36a5a495f8563f190677275d6c3b0ac32fc87871d235a573dff7f81f0ca06b523d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "bb2b15b293e6ea65887e8da86f1e05a54a61ac6efd828a4bb866b80c", "result": "valid", "flags": []}, {"tcId": 138, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004121a60b2b61d6d1dcfc2bc9008a34e81f89ee4a8a1741e51ca180cb29ee444f10fe6a4e3e13f5f1d09e648510355c562844cbf41ec01c94f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "b7f0eb18185456382cf1c1e0ea06ac2579debcdb7112761a95889773", "result": "valid", "flags": []}, {"tcId": 139, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004817de4233975a002fb3270fdac02157977a7efc2fb4cbdd75f41ed2c7454c105b30d1780db0ad9e94ef4e37804e3ea224cee93996d1b8ada", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "14e90223f8d4f8af982659f765fd43f23b5b6b8f9f7be9d3247c3a6a", "result": "valid", "flags": []}, {"tcId": 140, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c4230116de584c9406fb847330e72b0aaee2f79ac7da56d4f8d5dc054a16d4634af4be99ec85ec74bcc709fe928355201d386b54a0cfe229", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "96530f95ab2454c413c497344756e790fac6c996e4e69ab017c96843", "result": "valid", "flags": []}, {"tcId": 141, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043504c31b825853d28f7126fe34111a6e74ee9e181058a0ebe344bd35972d37d3d0684e9e1f7e450deacb4303c72c56830196f3b371c84d3a", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "61d10139e421299235282b47b00ef77bc01d19e79e5c3348c9056d26", "result": "valid", "flags": []}, {"tcId": 142, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004cac2ef7efb9a1f42158ba6bcf4d2dee11820aef5fd32054176f133c4a13d59e6e68c2e33b40b19f83e213475f70ee4366a5aabccfe069a85", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3294cf02189251f9e2999eae3259ed559b948014ad6f86367a8075ea", "result": "valid", "flags": []}, {"tcId": 143, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048c0bc275d927d0b90033edacf71df4ec361f2d5a8a16603dd689633731ffb900dffa6e424a3d71e30d91f58d108d14dd4bc8c5a3e17606df", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1496b6800e081b958397bdbf9902ede585df3066d3feccb4f4ee5f7c", "result": "valid", "flags": []}, {"tcId": 144, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044409475cefb7d31579184ffbf7ca913d7a731cb9d88eb3c358ca872221d11adde5d04dab31ba7b375d2d502dbf936190a0468ab3b8598f8a", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "90ebeb1ee03474c8de672d80088de5c902a1305038a7976d63f1bc47", "result": "valid", "flags": []}, {"tcId": 145, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000405d62055ef1a6e08d09cf0cc2954560fe08f047c41b5b9c8d6ea7bca686a163a539d2f64db5c91f1f5742e2fd4c285b068fba15dd5e6bbb5", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9c6c7a976da50be1e8b4121b7cee068e189f43c58c21659aaa9fc8bf", "result": "valid", "flags": []}, {"tcId": 146, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046135e5292b9ef76b2d2810b3110791ecaa34c33dc665a25449957330a65b87ed28f4bd122c99428df5f7b2df2d6e7db966939bd758d50945", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "d78ee9ba10627a4673df93b44af9226c09b2fb6de0fd82412bbe7e03", "result": "valid", "flags": []}, {"tcId": 147, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004063d4a3578cfb5790872a155125de0ccab62e51369e47fe408855d4a66e0ecf1dd41a8426714e6e4fa084977a3eb50f80096dd3b992fb05f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "00e20ede1bb64fe946619bf61d36a31b18810ace2b2be5ca34b0d074", "result": "valid", "flags": []}, {"tcId": 148, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041f711a5cab72af597d0fa079d3adcc9dd733bfb27cbfb994533c288c751283b7d9ac357904030bbcd0a274c3aca7b83e749b82bfb330e16d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "206ffdb59a6026b1ea27a0b02d152c5dcfaaf51e14947c77dc3c6dc2", "result": "valid", "flags": []}, {"tcId": 149, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043e4f47dd3eed360bfbfe6a6a52e4221e99e63fb4f11497f4764a34e0243885d3767cd729b4817d34bffa7c2cca09cd53a61680d22a507680", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "aa298c470c056986eb047b5b39e56771b0fc756e447204b02a073162", "result": "valid", "flags": []}, {"tcId": 150, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b55750b5f0e98431b00219e22bdf79ef76813c0cb182526332273ea0ace963afacd27edc2973e7921b4af68239269def74753f4a93352961", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3a58314a6ca7965b05ed725a9c5cb7c72bc848e8078bff1bfbfbe166", "result": "valid", "flags": []}, {"tcId": 151, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043ed67a4c6be5098bed6b3bb490d03fbea5f4564f49d9024dd1595048afae7066bdc8913db757144fa794532cf42709897afffe68038169f2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4787e3411589932b672454e8b2f704e34060b4e56ec1e5993c423a8c", "result": "valid", "flags": []}, {"tcId": 152, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004767295cf24e6eebb17a934b9def1b7937c77cfcc39ad4dddfea1c51d5bd4b17037f9b2630b9b625caa900e7cb1eb5e85a91e674eeab4bfe7", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "0127f89d48b05e5cc8bdae1b526768919dec7943c3384c82c1e73cea", "result": "valid", "flags": []}, {"tcId": 153, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000486d33668c592f042ea587c325748bdd12e9344b348a887c7f0ffb0074954afabe65b567842a566ba0a74cdc2fb17439417d4c228f44ff7ea", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4b17d44fb95139d4a744458c3409753cc6767d19c670598497d8baa1", "result": "valid", "flags": []}, {"tcId": 154, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048375d8cf64e4152174ca0efc9caa89a431739357ca62c9d22b19e096475ebb6e6aea5568589d867826a657b91f75030bc85fcfdd6ddcac1d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4ea8ba24a6b772dcc6b39aeb140634df9c9108dc464b071a95060e17", "result": "valid", "flags": []}, {"tcId": 155, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047f19d5c44f9ef788ef017167fe2c4584ecf30a42a4d69bb5d1ebc5a7526642688d66867e95e93973284313dda2aa01e58ebbf4d0ba6f90ac", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5a1a19ea3f747ed6bfcfb6cc1188ced5b640baf0bfbe9d370edd99c2", "result": "valid", "flags": []}, {"tcId": 156, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004099b2ed157e8427688ae4a31211626a31b69fc1b54cc2875406082d87d3cbc085a1d59870f97f9c5fef04801d9dbd125a6184c2062bd7b74", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "58aa990c3cbf80ca08725bc71b7e826f0340a2b11e0d3360c7d7c1d2", "result": "valid", "flags": []}, {"tcId": 157, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048468f615a3d091540180f64f52886f9f489fbb5bbb4ce45ad3359b7c8426be2af863e20a6d691bca5a12f6c44cbe52e27f0a62d1c094a660", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "463b4798278317eb51b7acf05b27070642696f9f21e064398ffb121b", "result": "valid", "flags": []}, {"tcId": 158, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046717f27c90f9e0895904260e5875ceeafea9c3249f1823e5443a89100678e7917056210634eab191a8de65deb764b8f5711d71c1da48d086", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "20bfc6a86fdbcbf2fce60d1841334efe993bd5866f387ce886c16146", "result": "valid", "flags": []}, {"tcId": 159, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040f3117f5ffd8e6e45ee0b26b36514ebcc138ef457b69ecd3cd574f1a5662f5b8507f70cb20f96fcb0e4c4ee9518214502943cad2b8a7ae6f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4ddf20c34e6c420c5344364dc2bb8d760167fe5b0b30a028747b8458", "result": "valid", "flags": []}, {"tcId": 160, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048f0f70fbacf33093d3b03fbf805a03a2a30dba2bdb124ed8b7f145e018ad204e4605579111b73c42d780296f16271d9748d12cda5cbd0d3c", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "04caf7d7805786f81a3ebde17df828400a9699c780315b3df5790bcf", "result": "valid", "flags": []}, {"tcId": 161, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b6c35a76cb5e830230e8986cba5fadb8cc2858d3bfd882baa7ea90056e30bdbd4c1704584262ef66a6842f5e6555e80f488f3b419eddde5d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "5ce5fade15341828a708389c39cda6fec22fcb533dd678d40268a12c", "result": "valid", "flags": []}, {"tcId": 162, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041529516a4b8d56bbe22e15d9c87d58fe6e4d89d9b5ad0d0c0c803cd30fe70f949c6aad01d2e55ab8dd46617dbae884a77a469f71e5840191", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3850aa204af04ec9c071ff53d6550021ba55b9db69a9227cd0ddbd3d", "result": "valid", "flags": []}, {"tcId": 163, "comment": "point with coordinate y = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00041acb03e1fc180533c5ec614110033d5013cbb281267a2ee5d86f97c700000000000000000000000000000000000000000000000000000001", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "2a7b5c942eca0cf9b53592adfb04e28c0253d17739456b8340a4a5e7", "result": "valid", "flags": []}, {"tcId": 164, "comment": "point with coordinate y = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004740aca1cfd94175d2e4a3740024b2456b1046f4dc6cd1c558d4e044035504fb29bbf39f268d674ecbc0de91705143a980ada0348bcfeebce", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9d58a8916b54400244f2481ec496053c3c317a39b41ab183042c4ccd", "result": "valid", "flags": []}, {"tcId": 165, "comment": "point with coordinate y = 1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004ab71fef80be9212acc31ba6445f0ccf187b451001074204d0eb4e4899e2cf46c2abc9112f7828e3d9db7615d47500d17ce780602f081cfb0", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "ac908fdef845c224b3d9d3a355ee62772630e72de9785a6f0d7f6c71", "result": "valid", "flags": []}, {"tcId": 166, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004ca2561a85e51b1bd762a1745af5067fefc9327d22f5c0bc3417ce73250267f158595e9163bb43ca7db5152ab3d637b8856be1805c50a1456", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "31badaa6d26e46b6d0ee473709b6014486a905f5bd9ae3333fd42b1c", "result": "valid", "flags": []}, {"tcId": 167, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040c97a6dc6bf95b28be23ab76df791207a0c73e16f807b58690e9b7d3ca957c82c3af898229f9454aba3f09ccbdab9cf5354a195f987d725d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "3d7944c173c21b7373c75a7f0269fc859373ca3d4c2b66789226b863", "result": "valid", "flags": []}, {"tcId": 168, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00049bdbe285b7b78081e4fbc5bc945e9107dabf41f8a1809946e6bc0d601d0bbc8ceac853d077c74b4e5f51ff1344cfc09f99a2109e0ca4b02e", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1a7c131ade48d1f4728f9b32ca0064e2d0e312ec891ba5e6f27b5b7f", "result": "valid", "flags": []}, {"tcId": 169, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045ed576b5b8dcbebb8b4a7a7bdd915dd627db9754da0f6ca726d0329fa3c1d932bc1b7b93f6f9f5fe90abaaf813021042871375b1f9a3da9a", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "0bf562ae39eccddf8c39ebfb574b4b9f337d7aadc6ec2150879bf237", "result": "valid", "flags": []}, {"tcId": 170, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000405c1d0ead6c230fbb129168a4903e0e3e09531fe3a0dd5aebf4c5cd9c5d3dc258da865b8c83f1d12708a195a3fd6dcd7f7de112f4567c172", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1dc541a572c2399a88e299ac275be160904667daec2be42ae07be061", "result": "valid", "flags": []}, {"tcId": 171, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000408aa9e3501d9f7e434c7e4780f51c9c3b57f5987d88671b6594aa04a5ec033d63702669a3d82234677bd016848ef3f709212f1fa3a5f853f", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "8cb62e424b1a0076f0a5f2f10f8169c4e41240d9c73862938e2c471d", "result": "valid", "flags": []}, {"tcId": 172, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00045c423803043c029b9ed8c5adfa21bd1a5d1f1bb1fb636abefa8428d4cc776b7d30a69da3bc525dc4c1daf142d3a0ec83866f9f44945342d2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1f96515e5779872b3c6cb96cf63984eab355f49a5932da492e79da3c", "result": "valid", "flags": []}, {"tcId": 173, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b4893124f9b4e5427c281427424cadf19c6c3fd4adb098bb4c1d879a674529243a317d7759f07d57fe59fd771672df55b99c02f57e206581", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "b1f8f1233fdb86dbb5fa265824ba22d3fae7ebd0924ed5290e07083d", "result": "valid", "flags": []}, {"tcId": 174, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00042e4e596b55a26034f03185ae5bc9d24463c23da0b1e3f7d9c3714e9b4ae287c93407ed830b28fb7c3528ad4f4db03f63c45d2b6724de417b", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "b096681efa8a6ac6c974481f7af9cee198b125519ce32cca11710476", "result": "valid", "flags": []}, {"tcId": 175, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bf5135875367ecf52791422d554514dc91149fc920a9b90eeaabd0fb025ccfcd7623d7c143caee1a39b8826ae53deed7cd9a3899bfc142bf", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "c0b69eb28b0b94036a11c0e8831c20a00c85879414d4030e1692af2c", "result": "valid", "flags": []}, {"tcId": 176, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00049a804a2fe4ebff17ba1b031380d7dcda567078bd64efdd8cf78584dea1dbca1bd96a9635332d731dbc79b614ed1a732559a9d38517dc4adc", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "4073d9e0c23657c2090c598b563901aff87d68435e2ef3bfd532e83c", "result": "valid", "flags": []}, {"tcId": 177, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00043005ca1fb7be5fc97af437fb02a1ec894a1d3dfa6280d4aa292dbdcb3cbd6b5fae91264de57f6b52e6f28f8858b8332e709b02f2cccfa776", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "57d38fcd4adab459f8400766771949ef048e75c53d94f5ada35debd0", "result": "valid", "flags": []}, {"tcId": 178, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00044d49e38e22d862275f3945492f7bf2577b86a4ac31fc0cb2ca455e679ae1239ef751ac0f3a2feef28cd752e133fdde2c29b95b8c5e625707", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "51b58b59782679999977344a4df8f2514b7c80e15a8190f295336a92", "result": "valid", "flags": []}, {"tcId": 179, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004119074f3bbd0da37147a91d65dd25bfdc81d65b3b1857e81277c81b1a7c8b917d3923fdd1fd24e07aa793ae8006cbc11281a3505c4505543", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "8b8818456c965ae74826b21654ac2c11bf71b8e12126f4a3fe1e65ac", "result": "valid", "flags": []}, {"tcId": 180, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004ba78a1d6ca79c60054d9d22954139440ff818ddbcc6e456e81342c190e79a87af7fd44fde9036ea80ca81d454cf1522a5e206e3758ff98d1", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9c4ee8a04a168496338cac9c9eaf5b89b1ab58dca4717dacf9730513", "result": "valid", "flags": []}, {"tcId": 181, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00046417ebfeaeb93520fb7221c9e5e615ea1490703468a73b25e59eedeaa5d32091c7839c71fdb56a56ffbc82f6606b5b430a28b0ccfccde221", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "773f1ec8811e4f3b5066959dd1cb00805173b53121db92e2da129665", "result": "valid", "flags": []}, {"tcId": 182, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000498e9a61e78e0f455d94c8d2ca0808a901e7d3d5d1c0d4cf75167886bd14d9cb5da090888cbcc6881264947c9ec498c21694edd9aa5030d17", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "61cc1f8f65f673acb0fef656ccfdfbb394851143be51b11cd8f87be4", "result": "valid", "flags": []}, {"tcId": 183, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bfd29ace14fe8a8c33d6e08d64e201390178ebf29202335cf2a6c2443ec55ad04e39cc371a7d040858ebefffa91fc5c9e1469ed1af5001da", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "a0c6f00fbf273266d88247cb2ed8e20eb7b478c7130fd5c150475ef6", "result": "valid", "flags": []}, {"tcId": 184, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aa395332c5ea3a42417269fe56c9664ef53540910fca0f101e7b0c1781f366ea70f3d69389471fa5088587465bc4075d9615f5a26fab79e2", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "2bba1130341162d642d71ac1e8f19ec73e9576c0743e716d6d8ecf4c", "result": "valid", "flags": []}, {"tcId": 185, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00048e2d4ab2199c6877cfb816280725d4f752a96923e82499ceb1f4641cae4a30334471c1d2c9abe929c7264a85b4a564cafc5ed88a7870d902", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "0de03d60404a29ba58d9cf07990b0d976f364213252229d36f894c3c", "result": "valid", "flags": []}, {"tcId": 186, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000484296cb5812ab220a2e0027fadce536cb4bc89cdbaa487ae7fac2f211cb9d2c37968db3329af5b932d1c0a16fd274ae988ffeca1b47c9bc4", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "944ed63056f7913eb0b1759777bceb7afe56ac7f9d50726570fc1f7c", "result": "valid", "flags": []}, {"tcId": 187, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004c49d69861a4b47d3e7ed4d2371df30cf4812a9e6b4e0ff4886668a0a9364c06494e42445fb6eeb7cb900bbe36e1068c04f6710f079de9604", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9983be0a5dbb6d03bae2162d6c0a11c70fabc1389b4d96b02dbb53ff", "result": "valid", "flags": []}, {"tcId": 188, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00047135dd6d57bb9eee9010c0d4f03715761118bf782443cff543df91e942608975ecb6abdd6fe2dadc1f3bd089fd3301816689aab9f16c2db1", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "bbade7032014c4373725bbcb8a02dd042930d3d682db97449eca6737", "result": "valid", "flags": []}, {"tcId": 189, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400e3ee2595af45425d3467d051b0afe0c9941e46ceaec4865a74790ad01ea4e7a290b331a13c9bf41ca15e944a0d64333cfa9985198e4f74", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "9d405a6623edf4c77393bed7c70dd05d545517b10322f30f2c5ada37", "result": "valid", "flags": []}, {"tcId": 190, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000480faf91b920239fd4d52b8c614b8413c4b2bee9e33a1476c88da73851528bdba580d7d9f76ab2fdd2d0b2b26ac7ee5ec5f11fe2810d1760d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1be7327314da157036432c308564662eb1c789c2ea7de8d19753456e", "result": "valid", "flags": []}, {"tcId": 191, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000423b90de543db88a7d37218edafa483e674b1da7a50937585d878d278bcc19a897ef41d31b1fd8a306a0c582402c0e297ff7de16316414a62", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "1458c1dd00ffe98e7c046af877c50cef45dc838bf2774c9d8ec17a54", "result": "valid", "flags": []}, {"tcId": 192, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004b29c4665e550b551ea808cc4ce9c01ca68565b01b652a73a579c96432d83111687c393ddd82a5967a4f614297988483529ebf3e6dfdc091d", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "cf7b1acc2edb86f303107f030e02eaa8d85f021f18b914592383c659", "result": "valid", "flags": []}, {"tcId": 193, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004946819d70fd67688b814b7d4e5d522fba6db0c6b84c72c63c1036ec2d46a974dcd8f88472490142ec720cccde520906939f8ed7d6f58cc61", "private": "009f99cbb61f4460f97e04bdd7b5b45c75fb5a3ec09e4dae36395921eb", "shared": "cad413470e797da588202ccac3c916dedf7ca0732049fc7ed7f2f0cf", "result": "valid", "flags": []}, {"tcId": 194, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "03", "shared": "0c52cf1e5e3f3da21b6040644e0e9eeca2e020f5872c430cccef8b98", "result": "valid", "flags": []}, {"tcId": 195, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "ba92ad99f14a0ee915aecf776c4fc5fdecbbc8fd8e13a7610a44d2b9", "result": "valid", "flags": []}, {"tcId": 196, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "01000000000000000000000000000000000000000000000000000000", "shared": "2b8dfc433327f665f04f2929084412000f1b2249f98b277784b334dd", "result": "valid", "flags": []}, {"tcId": 197, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "b70058644ff94dea13a4f3fea5bb80579152095ba9d3cf637f937737", "result": "valid", "flags": []}, {"tcId": 198, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "0080000000000000000000000000000000000000000000000000000000", "shared": "27bdac60e05ba7ac0f904050247e653c896bafd45b50d0938589718a", "result": "valid", "flags": []}, {"tcId": 199, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4a6ddebca3a5a7939f", "shared": "00b6b04347120ddc5228530ef7075824fbb745945edd75e73f637a40", "result": "valid", "flags": []}, {"tcId": 200, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6dcebca3a5a7939f", "shared": "d5b319d0f3143095c6bd1aa880c9df6a492caa6d6d275eb104a7aba3", "result": "valid", "flags": []}, {"tcId": 201, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6dd6bca3a5a7939f", "shared": "a0e1f7a142aafb0856616d46b5f76c2244010c9d46cec07ab33bb4a8", "result": "valid", "flags": []}, {"tcId": 202, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca2a5a7939f", "shared": "6f1baf2ed42e35d71b705f5dbfb7f51a8ab661be91d8b2614769fa01", "result": "valid", "flags": []}, {"tcId": 203, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a792dd", "shared": "1be0d59d1f0f3a743ae19c5246099391098f71444223831e16cfa0c5", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 204, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7935d", "shared": "7e00a9267243cea4ba7617860b6fcf404e0357d1202d8c85dc5e07d3", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 205, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939c", "shared": "0c52cf1e5e3f3da21b6040644e0e9eeca2e020f5872c430cccef8b98", "result": "valid", "flags": []}, {"tcId": 206, "comment": "edge case private key", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004aaf040d6cad2c18b953de46420b387fa83474d74c6767ed708b9d1268c82a09310bc35b5caf2d9b46318b895e4c097ed501d2dcb14d30a66", "private": "00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939d", "shared": "aac6a805f4ce1b6dcc13ec4ed16a889dc4d708f7f6f1e23471338324", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 207, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 208, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 209, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000000000000000000000000000000000000000000000000000d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 210, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000000000000000000000000000000000000000000000000000d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 211, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 212, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000001", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 213, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000000000000000000000000000000000000000000000000001d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 214, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000400000000000000000000000000000000000000000000000000000001d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 215, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe00000000000000000000000000000000000000000000000000000000", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 216, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe00000000000000000000000000000000000000000000000000000001", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 217, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fed7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 218, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fed7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 219, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff00000000000000000000000000000000000000000000000000000000", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 220, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff00000000000000000000000000000000000000000000000000000001", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 221, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ffd7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0fe", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 222, "comment": "point is not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ffd7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 223, "comment": "", "public": "3019301406072a8648ce3d020106092b2403030208010105030100", "private": "00c7e1dc95d8877ff3745ed3af688dc63b22cdc34d5b213c6a5a5e3244", "shared": "", "result": "invalid", "flags": []}, {"tcId": 224, "comment": "public point not on curve", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b3", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 225, "comment": "public point = (0,0)", "public": "3052301406072a8648ce3d020106092b2403030208010105033a00040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 226, "comment": "order = -22721622932454352787552537995910923612567546342330757191396560966559", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021dff283ecb55d9bc9979d5e7cfda8a2f04672ee943b49221435c5a586c61020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 227, "comment": "order = 0", "public": "3081f73081b806072a8648ce3d02013081ac020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd020100020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 228, "comment": "order = 1", "public": "3081f73081b806072a8648ce3d02013081ac020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd020101020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 229, "comment": "order = 5290290092223871682666367384584369978999613398297400491171", "public": "3082010f3081d006072a8648ce3d02013081c4020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021900d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 230, "comment": "generator = (0,0)", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 231, "comment": "generator not on curve", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cf021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 232, "comment": "cofactor = -1", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f0201ff033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 233, "comment": "cofactor = 0", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020100033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 234, "comment": "cofactor = 2", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020102033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 235, "comment": "cofactor = 22721622932454352787552537995910923612567546342330757191396560966559", "public": "3082012f3081f006072a8648ce3d02013081e4020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 236, "comment": "cofactor = None", "public": "308201103081d106072a8648ce3d02013081c5020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b0439040d9029ad2c7e5cf4340823b2a87dc68c9e4ce3174c1e6efdee12c07d58aa56f772c0726f24c6b89e4ecdac24354b9e99caa3f6d3761402cd021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 237, "comment": "modified prime", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00b352e92e2b56ca58f5e37acdc34af65ec6145e27c14bce42bef26235303c041c68a5e62ca9ce6c1c299803a6c1530b514e182ad8b0042a59cad29f43041c2580f63ccfe44138870713b1a92369e33e2135d266dbb372386c400b04390400000000000000000023f905836e050000000000000000000000024058a847e339e15fdfa24f6f1876ce8b7a763a02e23cc17016e2c89c20021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101033a000400000000000000000023f905836e050000000000000000000000024058a847e339e15fdfa24f6f1876ce8b7a763a02e23cc17016e2c89c20", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "74aa41dd243d700a0e5fd27262a98927bad607b8a5f43f9994a5540f", "result": "invalid", "flags": ["ModifiedPrime", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 238, "comment": "using secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004074f56dc2ea648ef89c3b72e23bbd2da36f60243e4d2067b70604af1c2165cec2f86603d60c8a611d5b84ba3d91dfe1a480825bcc4af3bcf", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 239, "comment": "using secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cbf6606595a3ee50f9fceaa2798c2740c82540516b4e5a7d361ff24e9dd15364e5408b2e679f9d5310d1f6893b36ce16b4a507509175fcb52aea53b781556b39", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 240, "comment": "using secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a03420004a1263e75b87ae0937060ff1472f330ee55cdf8f4329d6284a9ebfbcc856c11684225e72cbebff41e54fb6f00e11afe53a17937bedbf2df787f8ef9584f775838", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 241, "comment": "a = 0", "public": "3081f83081b906072a8648ce3d02013081ad020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff3021040100041c7ca82b1a7918992ef42c3deef04c4862d9e15b11e4c036e5c7389c94043904a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101033a0004a99e0508ee1d6fa2c3e46900b7130f4906896b1edb6ebc8e539a693826d7c3265cbd14cce5bbeaa7930c2b53ee2022f43ad74401b5fef1b1", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "7e1b3baf50c3b70324164df397146f3f6b5fcf9652699b2347ecbafe", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 242, "comment": "public key of order 3", "public": "308201133081d406072a8648ce3d02013081c8020101302806072a8648ce3d0101021d00d7c134aa264366862a18302575d1d787b09f075797da89f57ec8c0ff303c041c2f4559be967740e87c937f68650257b400c607237850d15c1c7038ef041c6c6d762d5ef01f9eeeeb593e08b92aad9b346a3fcb012716f2015b9904390434197d7b0473e033e001341b8ad983f299c5bcf14b87a72a08f9bb5512fa8a08e75d6552e0b8ff9de1025f1afabe6f93d936da7b5e5afdfc021d00d7c134aa264366862a18302575d0fb98d116bc4b6ddebca3a5a7939f020101033a000434197d7b0473e033e001341b8ad983f299c5bcf14b87a72a08f9bb55c4c6aaa13ee60133495f308794cf786cb5e097c3bea3af7a206dc303", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["WeakPublicKey", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 243, "comment": "public key on isomorphic curve brainpoolP224t1", "public": "3052301406072a8648ce3d020106092b2403030208010106033a0004002718942d4d3f883dffbdbea18a5b9af73ac85e648076b2c0b333de4cc8c1d9c6127ab6880b903ac44e50730c5ce1d4b52b3b02689c7f0b", "private": "009589ebd788c54b1002d7b60ee3c6daa2cad255882df77b6108dd6b58", "shared": "", "result": "invalid", "flags": ["IsomorphicPublicKey", "InvalidPublic"]}, {"tcId": 244, "comment": "Public key uses wrong curve: secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004992f4a20b0e54d674737b79da8e31ca6c6b3f86d5fed22a8861cfc1a3a57f7a9592ec7b70afa981399d30b9cc7f7dd31b6200f33a7d4b696", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 245, "comment": "Public key uses wrong curve: secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d0301070342000415cc8782ffaca34a954d055b906afdfffe1e3e2c08202cab9f2b31a18e6545cb52509e9a3ce64208d2cae9af9f366bc0053a210ef117376a4df1c43d54944f90", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 246, "comment": "Public key uses wrong curve: secp384r1", "public": "3076301006072a8648ce3d020106052b81040022036200044d5a019d890fb4a512d6e84c1738ce2ec66b942b17f28bba75341a6dda2ae2acf94ff30bad8205d013382bec105ce2a2287482fb50a9e273d934d5302a74845c21f75c493deaf571ee4df5619d7bc77638a530bebd6bcacf31bd550241b9b81e", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 247, "comment": "Public key uses wrong curve: secp521r1", "public": "30819b301006072a8648ce3d020106052b810400230381860004009cccaf2f3737187608ee4cd4bca434909fa84976e95bbf7e7517c07739625f5b2d52431c7e744930daac43a8997048ac82d082d37161a3b2a0c4d238d9140d6cf90159320fff6a710b506881b3b772747678dfd73bf7e870b516039c6008d44491e23789b57ad0c43b3fd27565ed703019aa036b60ff84ac866b653e368b47374c557f", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 248, "comment": "Public key uses wrong curve: secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a0342000422ae2126ae12a38cc771c1c8576a9b983aadcdd65f48f9d201192a488b0ff4a1d6d14ad981aac3fcfa2156b6340012197db2a4f6b6f7324ac8a7c4015861a1eb", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 249, "comment": "Public key uses wrong curve: secp224k1", "public": "304e301006072a8648ce3d020106052b81040020033a000451d0879ccbdc697e40e6b604392610f69a667feebb998309bc71fa6334189447b750d31937461ab880811cd9a83d697b333c2a28f385ced8", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 250, "comment": "Public key uses wrong curve: brainpoolP256r1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000467de48ceec36eccd05eb47bf815b784a51723667fe1008e700cd197cf3d50fc577444217f2a1976fb3faf6db0d08e8c6d645785ab66f3e5686c5572093bb6646", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 251, "comment": "Public key uses wrong curve: brainpoolP320r1", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044ece661f5ba97078659744dde9c8b2ad87ddf952fa76fa31ad3d04ee898aff363690885479efc69d0ddd21a35e0de809b95a183944e43c9653450ee4a03d5c3b714476482f0a84248b14efada7b6a5d9", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 252, "comment": "Public key uses wrong curve: brainpoolP384r1", "public": "307a301406072a8648ce3d020106092b240303020801010b036200047aeb413f2efd160ecca59106870bd37ebaaf5e3ff576a6b5125de002dc3031f6000889ec211d8f40ee4c0a085b6011868319cd075c7a5d6dbf24a24eec40ed34ff03618f5548fc3a10d06f168a190723632e5d7979261f048e7da8e8dd564d2e", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 253, "comment": "Public key uses wrong curve: brainpoolP512r1", "public": "30819b301406072a8648ce3d020106092b240303020801010d038182000496f2336f70fc7b831650e40834c0a425cd343b98ddb15d25c849f5951addcdd12dc956ddd6f90bb8ac29e185bcc2098974016dd5e760024bcba4f35adee681fd158c98fc462b2538595157538b299ac638ba6709d968a0e37a4ede0939ba6c1c54675c49c9f0d23c1576fd2c89727f7e50e63406d47612a0c5dc20f7688c1fa5", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 254, "comment": "Public key uses wrong curve: brainpoolP224t1", "public": "3052301406072a8648ce3d020106092b2403030208010106033a000474ec4011cf976c7151f8ccaa8b753b34c08e31cabf3efcc5bb997cd262c3273750abfcb8f76bc785c056a924e760d3d155bc5a7281a5b345", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 255, "comment": "Public key uses wrong curve: brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b2403030208010108034200040b42aeb62fc7e1295f9ce8f5bc5a66aecc4de289f73d05b672e7dd3ca85f34785e2e11f7cb6c7e4b48d458c2e2a613a81af11334ebd2627b0666f0d3628edbaa", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 256, "comment": "Public key uses wrong curve: brainpoolP320t1", "public": "306a301406072a8648ce3d020106092b240303020801010a0352000472e9530f37de4b1d19d53789b02a5bda6e2124f71c58e8cd0e080047f0ca66aa69d4ff2e31b14901746487c71dfffb8a1368e45a56f482fcb5c635a80a1ad2ef56573ffd5fad242c823516eac42f1c7c", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 257, "comment": "Public key uses wrong curve: brainpoolP384t1", "public": "307a301406072a8648ce3d020106092b240303020801010c0362000427cd77274564e4cc0b8284eaac5373fe2cbc731d95c01a781a6b6c4c94983a88df69f47cfe38cc04895673b56a4739377cac72600ac72743a72c025fbbc2c20e7b82474dcc3e98ba671fcb10e8aaff3d4791309f7a5d36d0b68c2d0fb703cced", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 258, "comment": "Public key uses wrong curve: brainpoolP512t1", "public": "30819b301406072a8648ce3d020106092b240303020801010e038182000466d53b70bcc33f779eddc473774f0a01f762f88df36199b80ef23123551bfe411a639600466da455b77317aa2f43d130258e7a1e86cd65e19a7e684c61896bcb40a0996fb5a897341f7c53a3486fb0aaf9e3281d31d366929eb608b5ed9e13693384dccb9f51f73a2d195949f1453e063e90287f5cb478322c5f03e9a05ab538", "private": "00a45d3c181ab18bbeb697c24c199854d4e450107dc4e33183bda31617", "shared": "", "result": "invalid", "flags": []}, {"tcId": 259, "comment": "invalid public key", "public": "3036301406072a8648ce3d020106092b2403030208010105031e0002cfa2455a3fc3491d24291de3588e0eb46063c04430d7975ba71df01d", "private": "545f59601297fcc8d734f43bebc2d73866b16a3adb885211ae1a608f", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 260, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e00039f98bba2a331e3a334c5ea7f8937a85580e557b353e06f3ed8dcd195", "private": "7abde5e6ce42081afc6194930710bfd108b91a7fc34d2caf52cdc54f", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 261, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e00029f98bba2a331e3a334c5ea7f8937a85580e557b353e06f3ed8dcd195", "private": "7abde5e6ce42081afc6194930710bfd108b91a7fc34d2caf52cdc550", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 262, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e0002128849d9e5731a42465e35aebd7b6db3f24e1366f0ade29e821e1c71", "private": "720c41bbb15e5247ed81e269b421771e6be5134b11f3542ee8059176", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 263, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e0003abefd2cfd3faf77666c7e1ad81414d6336fea6a6b45179eccffcc95a", "private": "72adf44f808419549de69d89d37b7508ff528c439580e5d0031e842b", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 264, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e0002abefd2cfd3faf77666c7e1ad81414d6336fea6a6b45179eccffcc95a", "private": "72adf44f808419549de69d89d37b7508ff528c439580e5d0031e842c", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 265, "comment": "public key is a low order point on twist", "public": "3036301406072a8648ce3d020106092b2403030208010105031e0003128849d9e5731a42465e35aebd7b6db3f24e1366f0ade29e821e1c71", "private": "720c41bbb15e5247ed81e269b421771e6be5134b11f3542ee8059175", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 266, "comment": "long form encoding of length of sequence", "public": "308152301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 267, "comment": "long form encoding of length of sequence", "public": "305330811406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 268, "comment": "length of sequence contains leading 0", "public": "30820052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 269, "comment": "length of sequence contains leading 0", "public": "30543082001406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 270, "comment": "wrong length of sequence", "public": "3053301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 271, "comment": "wrong length of sequence", "public": "3051301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 272, "comment": "wrong length of sequence", "public": "3052301506072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 273, "comment": "wrong length of sequence", "public": "3052301306072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 274, "comment": "uint32 overflow in length of sequence", "public": "30850100000052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 275, "comment": "uint32 overflow in length of sequence", "public": "30573085010000001406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 276, "comment": "uint64 overflow in length of sequence", "public": "3089010000000000000052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 277, "comment": "uint64 overflow in length of sequence", "public": "305b308901000000000000001406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 278, "comment": "length of sequence = 2**31 - 1", "public": "30847fffffff301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 279, "comment": "length of sequence = 2**31 - 1", "public": "305630847fffffff06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 280, "comment": "length of sequence = 2**32 - 1", "public": "3084ffffffff301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 281, "comment": "length of sequence = 2**32 - 1", "public": "30563084ffffffff06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 282, "comment": "length of sequence = 2**40 - 1", "public": "3085ffffffffff301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 283, "comment": "length of sequence = 2**40 - 1", "public": "30573085ffffffffff06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 284, "comment": "length of sequence = 2**64 - 1", "public": "3088ffffffffffffffff301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 285, "comment": "length of sequence = 2**64 - 1", "public": "305a3088ffffffffffffffff06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 286, "comment": "incorrect length of sequence", "public": "30ff301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 287, "comment": "incorrect length of sequence", "public": "305230ff06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 288, "comment": "indefinite length without termination", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 289, "comment": "indefinite length without termination", "public": "3052308006072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 290, "comment": "indefinite length without termination", "public": "3052301406802a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 291, "comment": "indefinite length without termination", "public": "3052301406072a8648ce3d020106802b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 292, "comment": "indefinite length without termination", "public": "3052301406072a8648ce3d020106092b240303020801010503800004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 293, "comment": "removing sequence", "public": "", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 294, "comment": "removing sequence", "public": "303c033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 295, "comment": "lonely sequence tag", "public": "30", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 296, "comment": "lonely sequence tag", "public": "303d30033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 297, "comment": "appending 0's to sequence", "public": "3054301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 298, "comment": "appending 0's to sequence", "public": "3054301606072a8648ce3d020106092b24030302080101050000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 299, "comment": "prepending 0's to sequence", "public": "30540000301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 300, "comment": "prepending 0's to sequence", "public": "30543016000006072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 301, "comment": "appending unused 0's to sequence", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 302, "comment": "appending unused 0's to sequence", "public": "3054301406072a8648ce3d020106092b24030302080101050000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 303, "comment": "appending null value to sequence", "public": "3054301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710500", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 304, "comment": "appending null value to sequence", "public": "3054301606072a8648ce3d020106092b24030302080101050500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 305, "comment": "including garbage", "public": "30574981773052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 306, "comment": "including garbage", "public": "305625003052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 307, "comment": "including garbage", "public": "30543052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710004deadbeef", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 308, "comment": "including garbage", "public": "30573019498177301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 309, "comment": "including garbage", "public": "305630182500301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 310, "comment": "including garbage", "public": "305a3016301406072a8648ce3d020106092b24030302080101050004deadbeef033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 311, "comment": "including garbage", "public": "30573019260c49817706072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 312, "comment": "including garbage", "public": "30563018260b250006072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 313, "comment": "including garbage", "public": "305a301c260906072a8648ce3d02010004deadbeef06092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 314, "comment": "including garbage", "public": "3057301906072a8648ce3d0201260e49817706092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 315, "comment": "including garbage", "public": "3056301806072a8648ce3d0201260d250006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 316, "comment": "including garbage", "public": "305a301c06072a8648ce3d0201260b06092b24030302080101050004deadbeef033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 317, "comment": "including garbage", "public": "3057301406072a8648ce3d020106092b2403030208010105233f498177033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 318, "comment": "including garbage", "public": "3056301406072a8648ce3d020106092b2403030208010105233e2500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 319, "comment": "including garbage", "public": "305a301406072a8648ce3d020106092b2403030208010105233c033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710004deadbeef", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 320, "comment": "including undefined tags", "public": "305aaa00bb00cd003052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 321, "comment": "including undefined tags", "public": "3058aa02aabb3052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 322, "comment": "including undefined tags", "public": "305a301caa00bb00cd00301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 323, "comment": "including undefined tags", "public": "3058301aaa02aabb301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 324, "comment": "including undefined tags", "public": "305a301c260faa00bb00cd0006072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 325, "comment": "including undefined tags", "public": "3058301a260daa02aabb06072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 326, "comment": "including undefined tags", "public": "305a301c06072a8648ce3d02012611aa00bb00cd0006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 327, "comment": "including undefined tags", "public": "3058301a06072a8648ce3d0201260faa02aabb06092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 328, "comment": "including undefined tags", "public": "305a301406072a8648ce3d020106092b24030302080101052342aa00bb00cd00033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 329, "comment": "including undefined tags", "public": "3058301406072a8648ce3d020106092b24030302080101052340aa02aabb033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 330, "comment": "truncated length of sequence", "public": "3081", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 331, "comment": "truncated length of sequence", "public": "303e3081033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 332, "comment": "Replacing sequence with NULL", "public": "0500", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 333, "comment": "Replacing sequence with NULL", "public": "303e0500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 334, "comment": "changing tag value of sequence", "public": "2e52301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 335, "comment": "changing tag value of sequence", "public": "2f52301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 336, "comment": "changing tag value of sequence", "public": "3152301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 337, "comment": "changing tag value of sequence", "public": "3252301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 338, "comment": "changing tag value of sequence", "public": "ff52301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 339, "comment": "changing tag value of sequence", "public": "30522e1406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 340, "comment": "changing tag value of sequence", "public": "30522f1406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 341, "comment": "changing tag value of sequence", "public": "3052311406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 342, "comment": "changing tag value of sequence", "public": "3052321406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 343, "comment": "changing tag value of sequence", "public": "3052ff1406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 344, "comment": "dropping value of sequence", "public": "3000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 345, "comment": "dropping value of sequence", "public": "303e3000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 346, "comment": "truncated sequence", "public": "3051301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 347, "comment": "truncated sequence", "public": "30511406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 348, "comment": "truncated sequence", "public": "3051301306072a8648ce3d020106092b24030302080101033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 349, "comment": "truncated sequence", "public": "30513013072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 350, "comment": "indefinite length", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 351, "comment": "indefinite length", "public": "3054308006072a8648ce3d020106092b24030302080101050000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 352, "comment": "indefinite length with truncated delimiter", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b7100", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 353, "comment": "indefinite length with truncated delimiter", "public": "3053308006072a8648ce3d020106092b240303020801010500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 354, "comment": "indefinite length with additional element", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b7105000000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 355, "comment": "indefinite length with additional element", "public": "3056308006072a8648ce3d020106092b240303020801010505000000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 356, "comment": "indefinite length with truncated element", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71060811220000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 357, "comment": "indefinite length with truncated element", "public": "3058308006072a8648ce3d020106092b2403030208010105060811220000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 358, "comment": "indefinite length with garbage", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710000fe02beef", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 359, "comment": "indefinite length with garbage", "public": "3058308006072a8648ce3d020106092b24030302080101050000fe02beef033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 360, "comment": "indefinite length with nonempty EOC", "public": "3080301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710002beef", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 361, "comment": "indefinite length with nonempty EOC", "public": "3056308006072a8648ce3d020106092b24030302080101050002beef033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 362, "comment": "prepend empty sequence", "public": "30543000301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 363, "comment": "prepend empty sequence", "public": "30543016300006072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 364, "comment": "append empty sequence", "public": "3054301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b713000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 365, "comment": "append empty sequence", "public": "3054301606072a8648ce3d020106092b24030302080101053000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 366, "comment": "append garbage with high tag number", "public": "3055301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71bf7f00", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 367, "comment": "append garbage with high tag number", "public": "3055301706072a8648ce3d020106092b2403030208010105bf7f00033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 368, "comment": "sequence of sequence", "public": "30543052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 369, "comment": "sequence of sequence", "public": "30543016301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 370, "comment": "truncated sequence: removed last 1 elements", "public": "3016301406072a8648ce3d020106092b2403030208010105", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 371, "comment": "truncated sequence: removed last 1 elements", "public": "3047300906072a8648ce3d0201033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 372, "comment": "repeating element in sequence", "public": "30818e301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 373, "comment": "repeating element in sequence", "public": "305d301f06072a8648ce3d020106092b240303020801010506092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 374, "comment": "long form encoding of length of oid", "public": "305330150681072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 375, "comment": "long form encoding of length of oid", "public": "3053301506072a8648ce3d02010681092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 376, "comment": "length of oid contains leading 0", "public": "30543016068200072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 377, "comment": "length of oid contains leading 0", "public": "3054301606072a8648ce3d0201068200092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 378, "comment": "wrong length of oid", "public": "3052301406082a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 379, "comment": "wrong length of oid", "public": "3052301406062a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 380, "comment": "wrong length of oid", "public": "3052301406072a8648ce3d0201060a2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 381, "comment": "wrong length of oid", "public": "3052301406072a8648ce3d020106082b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 382, "comment": "uint32 overflow in length of oid", "public": "30573019068501000000072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 383, "comment": "uint32 overflow in length of oid", "public": "3057301906072a8648ce3d0201068501000000092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 384, "comment": "uint64 overflow in length of oid", "public": "305b301d06890100000000000000072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 385, "comment": "uint64 overflow in length of oid", "public": "305b301d06072a8648ce3d020106890100000000000000092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 386, "comment": "length of oid = 2**31 - 1", "public": "3056301806847fffffff2a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 387, "comment": "length of oid = 2**31 - 1", "public": "3056301806072a8648ce3d020106847fffffff2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 388, "comment": "length of oid = 2**32 - 1", "public": "305630180684ffffffff2a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 389, "comment": "length of oid = 2**32 - 1", "public": "3056301806072a8648ce3d02010684ffffffff2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 390, "comment": "length of oid = 2**40 - 1", "public": "305730190685ffffffffff2a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 391, "comment": "length of oid = 2**40 - 1", "public": "3057301906072a8648ce3d02010685ffffffffff2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 392, "comment": "length of oid = 2**64 - 1", "public": "305a301c0688ffffffffffffffff2a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 393, "comment": "length of oid = 2**64 - 1", "public": "305a301c06072a8648ce3d02010688ffffffffffffffff2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 394, "comment": "incorrect length of oid", "public": "3052301406ff2a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 395, "comment": "incorrect length of oid", "public": "3052301406072a8648ce3d020106ff2b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 396, "comment": "removing oid", "public": "3049300b06092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 397, "comment": "lonely oid tag", "public": "304a300c0606092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 398, "comment": "lonely oid tag", "public": "3048300a06072a8648ce3d020106033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 399, "comment": "appending 0's to oid", "public": "3054301606092a8648ce3d0201000006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 400, "comment": "appending 0's to oid", "public": "3054301606072a8648ce3d0201060b2b24030302080101050000033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 401, "comment": "prepending 0's to oid", "public": "30543016060900002a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 402, "comment": "prepending 0's to oid", "public": "3054301606072a8648ce3d0201060b00002b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 403, "comment": "appending unused 0's to oid", "public": "3054301606072a8648ce3d0201000006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 404, "comment": "appending null value to oid", "public": "3054301606092a8648ce3d0201050006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 405, "comment": "appending null value to oid", "public": "3054301606072a8648ce3d0201060b2b24030302080101050500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 406, "comment": "truncated length of oid", "public": "304b300d068106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 407, "comment": "truncated length of oid", "public": "3049300b06072a8648ce3d02010681033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 408, "comment": "Replacing oid with NULL", "public": "304b300d050006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 409, "comment": "Replacing oid with NULL", "public": "3049300b06072a8648ce3d02010500033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 410, "comment": "changing tag value of oid", "public": "3052301404072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 411, "comment": "changing tag value of oid", "public": "3052301405072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 412, "comment": "changing tag value of oid", "public": "3052301407072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 413, "comment": "changing tag value of oid", "public": "3052301408072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 414, "comment": "changing tag value of oid", "public": "30523014ff072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 415, "comment": "changing tag value of oid", "public": "3052301406072a8648ce3d020104092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 416, "comment": "changing tag value of oid", "public": "3052301406072a8648ce3d020105092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 417, "comment": "changing tag value of oid", "public": "3052301406072a8648ce3d020107092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 418, "comment": "changing tag value of oid", "public": "3052301406072a8648ce3d020108092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 419, "comment": "changing tag value of oid", "public": "3052301406072a8648ce3d0201ff092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 420, "comment": "dropping value of oid", "public": "304b300d060006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 421, "comment": "dropping value of oid", "public": "3049300b06072a8648ce3d02010600033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 422, "comment": "modify first byte of oid", "public": "305230140607288648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 423, "comment": "modify first byte of oid", "public": "3052301406072a8648ce3d02010609292403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 424, "comment": "modify last byte of oid", "public": "3052301406072a8648ce3d028106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 425, "comment": "modify last byte of oid", "public": "3052301406072a8648ce3d020106092b2403030208010185033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 426, "comment": "truncated oid", "public": "3051301306062a8648ce3d0206092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 427, "comment": "truncated oid", "public": "3051301306068648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 428, "comment": "truncated oid", "public": "3051301306072a8648ce3d020106082b24030302080101033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 429, "comment": "truncated oid", "public": "3051301306072a8648ce3d020106082403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 430, "comment": "wrong oid", "public": "3050301206052b0e03021a06092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 431, "comment": "wrong oid", "public": "30543016060960864801650304020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 432, "comment": "wrong oid", "public": "304e301006072a8648ce3d020106052b0e03021a033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 433, "comment": "wrong oid", "public": "3052301406072a8648ce3d02010609608648016503040201033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 434, "comment": "longer oid", "public": "3053301506082a8648ce3d02010106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 435, "comment": "longer oid", "public": "3053301506072a8648ce3d0201060a2b240303020801010501033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 436, "comment": "oid with modified node", "public": "3052301406072a8648ce3d021106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 437, "comment": "oid with modified node", "public": "30563018060b2a8648ce3d02888080800106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 438, "comment": "oid with modified node", "public": "3052301406072a8648ce3d020106092b2403030208010115033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 439, "comment": "oid with modified node", "public": "3056301806072a8648ce3d0201060d2b240303020801018880808005033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 440, "comment": "large integer in oid", "public": "305b301d06102a8648ce3d028280808080808080800106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 441, "comment": "large integer in oid", "public": "305b301d06072a8648ce3d020106122b2403030208010182808080808080808005033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 442, "comment": "oid with invalid node", "public": "3053301506082a8648ce3d0201e006092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 443, "comment": "oid with invalid node", "public": "3053301506082a808648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 444, "comment": "oid with invalid node", "public": "3053301506072a8648ce3d0201060a2b2403030208010105e0033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 445, "comment": "oid with invalid node", "public": "3053301506072a8648ce3d0201060a2b802403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 446, "comment": "long form encoding of length of bit string", "public": "3053301406072a8648ce3d020106092b240303020801010503813a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 447, "comment": "length of bit string contains leading 0", "public": "3054301406072a8648ce3d020106092b24030302080101050382003a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 448, "comment": "wrong length of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105033b0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 449, "comment": "wrong length of bit string", "public": "3052301406072a8648ce3d020106092b240303020801010503390004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 450, "comment": "uint32 overflow in length of bit string", "public": "3057301406072a8648ce3d020106092b24030302080101050385010000003a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 451, "comment": "uint64 overflow in length of bit string", "public": "305b301406072a8648ce3d020106092b2403030208010105038901000000000000003a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 452, "comment": "length of bit string = 2**31 - 1", "public": "3056301406072a8648ce3d020106092b240303020801010503847fffffff0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 453, "comment": "length of bit string = 2**32 - 1", "public": "3056301406072a8648ce3d020106092b24030302080101050384ffffffff0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 454, "comment": "length of bit string = 2**40 - 1", "public": "3057301406072a8648ce3d020106092b24030302080101050385ffffffffff0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 455, "comment": "length of bit string = 2**64 - 1", "public": "305a301406072a8648ce3d020106092b24030302080101050388ffffffffffffffff0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 456, "comment": "incorrect length of bit string", "public": "3052301406072a8648ce3d020106092b240303020801010503ff0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 457, "comment": "lonely bit string tag", "public": "3017301406072a8648ce3d020106092b240303020801010503", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 458, "comment": "appending 0's to bit string", "public": "3054301406072a8648ce3d020106092b2403030208010105033c0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710000", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 459, "comment": "prepending 0's to bit string", "public": "3054301406072a8648ce3d020106092b2403030208010105033c00000004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 460, "comment": "appending null value to bit string", "public": "3054301406072a8648ce3d020106092b2403030208010105033c0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b710500", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 461, "comment": "truncated length of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101050381", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 462, "comment": "Replacing bit string with NULL", "public": "3018301406072a8648ce3d020106092b24030302080101050500", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 463, "comment": "changing tag value of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105013a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 464, "comment": "changing tag value of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105023a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 465, "comment": "changing tag value of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105043a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 466, "comment": "changing tag value of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105053a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 467, "comment": "changing tag value of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105ff3a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 468, "comment": "dropping value of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101050300", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 469, "comment": "modify first byte of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0204d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 470, "comment": "modify last byte of bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27bf1", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 471, "comment": "truncated bit string", "public": "3051301406072a8648ce3d020106092b240303020801010503390004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 472, "comment": "truncated bit string", "public": "3051301406072a8648ce3d020106092b2403030208010105033904d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 473, "comment": "declaring bits as unused in bit string", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0104d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 474, "comment": "unused bits in bit string", "public": "3056301406072a8648ce3d020106092b2403030208010105033e2004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b7101020304", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 475, "comment": "unused bits in empty bit-string", "public": "3019301406072a8648ce3d020106092b2403030208010105030103", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 476, "comment": "128 unused bits", "public": "3052301406072a8648ce3d020106092b2403030208010105033a8004d7ba2df581b454d83c6bb8650fc3a1f13521881fa2ea52061d9e3af54e820e75fb8622183b699d868789bf3682889d7021798a3e4ba27b71", "private": "4dee4d6c6c9a8bf8b5e3c2fc001fe4918da586aaceb2a1ee78a293c4", "shared": "cd46b85ec2e2f06ecce31fefc71aa29256476b2ef0dd04ac0eaed597", "result": "acceptable", "flags": ["InvalidAsn"]}]}]}