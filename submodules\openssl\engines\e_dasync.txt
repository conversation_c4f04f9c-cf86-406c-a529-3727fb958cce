# Copyright 1999-2017 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Function codes
DASYNC_F_BIND_DASYNC:107:bind_dasync
DASYNC_F_CIPHER_AES_128_CBC_CODE:100:*
DASYNC_F_DASYNC_AES128_CBC_HMAC_SHA1_INIT_KEY:109:*
DASYNC_F_DASYNC_AES128_INIT_KEY:108:*
DASYNC_F_DASYNC_BN_MOD_EXP:101:*
DASYNC_F_DASYNC_CIPHER_INIT_KEY_HELPER:110:dasync_cipher_init_key_helper
DASYNC_F_DASYNC_MOD_EXP:102:*
DASYNC_F_DASYNC_PRIVATE_DECRYPT:103:*
DASY<PERSON>_F_DASYNC_PRIVATE_ENCRYPT:104:*
DASY<PERSON>_F_DASYNC_PUBLIC_DECRYPT:105:*
DASYNC_F_DASYNC_PUBLIC_ENCRYPT:106:*

#Reason codes
DASYNC_R_INIT_FAILED:100:init failed
