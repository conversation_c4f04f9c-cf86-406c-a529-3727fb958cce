=pod

=head1 NAME

EVP_PKEY_CTX_set_tls1_prf_md,
EVP_PKEY_CTX_set1_tls1_prf_secret, EVP_PKEY_CTX_add1_tls1_prf_seed -
TLS PRF key derivation algorithm

=head1 SYNOPSIS

 #include <openssl/kdf.h>

 int EVP_PKEY_CTX_set_tls1_prf_md(EVP_PKEY_CTX *pctx, const EVP_MD *md);
 int EVP_PKEY_CTX_set1_tls1_prf_secret(EVP_PKEY_CTX *pctx,
                                       unsigned char *sec, int seclen);
 int EVP_PKEY_CTX_add1_tls1_prf_seed(EVP_PKEY_CTX *pctx,
                                     unsigned char *seed, int seedlen);

=head1 DESCRIPTION

The B<EVP_PKEY_TLS1_PRF> algorithm implements the PRF key derivation function for
TLS. It has no associated private key and only implements key derivation
using L<EVP_PKEY_derive(3)>.

E<PERSON>_PKEY_set_tls1_prf_md() sets the message digest associated with the
TLS PRF. EVP_md5_sha1() is treated as a special case which uses the PRF
algorithm using both B<MD5> and B<SHA1> as used in TLS 1.0 and 1.1.

EVP_PKEY_CTX_set_tls1_prf_secret() sets the secret value of the TLS PRF
to B<seclen> bytes of the buffer B<sec>. Any existing secret value is replaced
and any seed is reset.

EVP_PKEY_CTX_add1_tls1_prf_seed() sets the seed to B<seedlen> bytes of B<seed>.
If a seed is already set it is appended to the existing value.

=head1 STRING CTRLS

The TLS PRF also supports string based control operations using
L<EVP_PKEY_CTX_ctrl_str(3)>.
The B<type> parameter "md" uses the supplied B<value> as the name of the digest
algorithm to use.
The B<type> parameters "secret" and "seed" use the supplied B<value> parameter
as a secret or seed value.
The names "hexsecret" and "hexseed" are similar except they take a hex string
which is converted to binary.

=head1 NOTES

All these functions are implemented as macros.

A context for the TLS PRF can be obtained by calling:

 EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);

The digest, secret value and seed must be set before a key is derived or an
error occurs.

The total length of all seeds cannot exceed 1024 bytes in length: this should
be more than enough for any normal use of the TLS PRF.

The output length of the PRF is specified by the length parameter in the
EVP_PKEY_derive() function. Since the output length is variable, setting
the buffer to B<NULL> is not meaningful for the TLS PRF.

Optimised versions of the TLS PRF can be implemented in an ENGINE.

=head1 RETURN VALUES

All these functions return 1 for success and 0 or a negative value for failure.
In particular a return value of -2 indicates the operation is not supported by
the public key algorithm.

=head1 EXAMPLES

This example derives 10 bytes using SHA-256 with the secret key "secret"
and seed value "seed":

 EVP_PKEY_CTX *pctx;
 unsigned char out[10];
 size_t outlen = sizeof(out);

 pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_TLS1_PRF, NULL);
 if (EVP_PKEY_derive_init(pctx) <= 0)
     /* Error */
 if (EVP_PKEY_CTX_set_tls1_prf_md(pctx, EVP_sha256()) <= 0)
     /* Error */
 if (EVP_PKEY_CTX_set1_tls1_prf_secret(pctx, "secret", 6) <= 0)
     /* Error */
 if (EVP_PKEY_CTX_add1_tls1_prf_seed(pctx, "seed", 4) <= 0)
     /* Error */
 if (EVP_PKEY_derive(pctx, out, &outlen) <= 0)
     /* Error */

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_CTX_ctrl_str(3)>,
L<EVP_PKEY_derive(3)>

=head1 COPYRIGHT

Copyright 2016-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
