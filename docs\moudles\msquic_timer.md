[TOC]

# `timer_wheel.c` 文档

## 一、文件概述
`timer_wheel.c` 文件实现了一个定时器轮算法，该算法用于管理和调度 QUIC 连接的定时器。利用每个连接由单个工作线程“拥有”的特性，工作线程可以串行驱动这些连接的执行，同时也能驱动所有连接定时器的过期，避免了依赖平台提供的定时器实现，从而提高了定时器管理的效率。

## 二、宏定义
| 宏定义 | 值 | 功能 |
| --- | --- | --- |
| `QUIC_TIMER_WHEEL_INITIAL_SLOT_COUNT` | 32 | 定义定时器轮的初始槽数量。 |
| `QUIC_TIMER_WHEEL_MAX_LOAD_FACTOR` | 32 | 定义每个槽平均允许的最大连接数。 |
| `TIME_TO_SLOT_INDEX(TimerWheel, TimeUs)` | `(US_TO_MS(TimeUs) / 1000) % (TimerWheel)->SlotCount` | 根据给定的时间（微秒）计算对应的槽索引。 |

## 三、结构体和枚举

### `QUIC_TIMER_WHEEL` 结构体
此结构体用于表示定时器轮，其成员如下：
| 成员 | 初始值 | 说明 |
| --- | --- | --- |
| `NextExpirationTime` | `UINT64_MAX` | 下一个过期时间。 |
| `ConnectionCount` | 0 | 连接计数。 |
| `NextConnection` | `NULL` | 下一个过期的连接。 |
| `SlotCount` | - | 槽的数量。 |
| `Slots` | - | 槽数组，每个槽是一个双向链表头。 |

## 四、函数说明

### `QuicTimerWheelInitialize`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
QuicTimerWheelInitialize(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel
);
```
- **功能**：对定时器轮进行初始化操作。
- **参数**：
  - `TimerWheel`：指向待初始化的定时器轮的指针。
- **返回值**：
  - `QUIC_STATUS_SUCCESS`：初始化成功。
  - `QUIC_STATUS_OUT_OF_MEMORY`：内存分配失败。
- **实现步骤**：
  1. 把 `NextExpirationTime` 初始化为 `UINT64_MAX`，`ConnectionCount` 初始化为 0，`NextConnection` 初始化为 `NULL`。
  2. 设定 `SlotCount` 为 `QUIC_TIMER_WHEEL_INITIAL_SLOT_COUNT`。
  3. 为槽数组分配内存。
  4. 若内存分配失败，记录错误日志并返回 `QUIC_STATUS_OUT_OF_MEMORY`。
  5. 对每个槽的双向链表头进行初始化。

### `QuicTimerWheelUninitialize`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicTimerWheelUninitialize(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel
);
```
- **功能**：对定时器轮执行反初始化操作。
- **参数**：
  - `TimerWheel`：指向待反初始化的定时器轮的指针。
- **实现步骤**：
  1. 检查 `Slots` 是否为空，若不为空：
     - 遍历每个槽，检查是否有连接未移除，若有则记录警告日志。
     - 确保 `ConnectionCount` 为 0，`NextConnection` 为 `NULL`，`NextExpirationTime` 为 `UINT64_MAX`。
     - 释放槽数组的内存。

### `QuicTimerWheelResize`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicTimerWheelResize(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel
);
```
- **功能**：调整定时器轮的大小。
- **参数**：
  - `TimerWheel`：指向待调整大小的定时器轮的指针。
- **实现步骤**：
  1. 计算新的槽数量（原槽数量的 2 倍），若达到最大大小则返回。
  2. 为新的槽数组分配内存。
  3. 若内存分配失败，记录错误日志并返回。
  4. 初始化新槽的双向链表头。
  5. 遍历旧槽，将所有连接从旧槽移除并插入到新槽中。
  6. 释放旧槽数组的内存。

### `QuicTimerWheelUpdate`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicTimerWheelUpdate(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel
);
```
- **功能**：更新定时器轮的下一个过期时间和连接。
- **参数**：
  - `TimerWheel`：指向待更新的定时器轮的指针。
- **实现步骤**：
  1. 把 `NextExpirationTime` 初始化为 `UINT64_MAX`，`NextConnection` 初始化为 `NULL`。
  2. 遍历所有槽，找出最早过期的连接。
  3. 若找到最早过期的连接，更新 `NextExpirationTime` 和 `NextConnection`。
  4. 根据是否找到最早过期的连接，记录相应的日志。

### `QuicTimerWheelRemoveConnection`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicTimerWheelRemoveConnection(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel,
    _Inout_ QUIC_CONNECTION* Connection
);
```
- **功能**：从定时器轮中移除指定的连接。
- **参数**：
  - `TimerWheel`：指向定时器轮的指针。
  - `Connection`：指向要移除的连接的指针。
- **实现步骤**：
  1. 检查连接是否在定时器轮中，若在则：
     - 从双向链表中移除该连接。
     - 减少连接计数。
     - 若移除的连接是下一个要过期的连接，更新定时器轮的下一个过期时间和连接。
     - 释放连接的定时器轮引用。

### `QuicTimerWheelUpdateConnection`
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QuicTimerWheelUpdateConnection(
    _Inout_ QUIC_TIMER_WHEEL* TimerWheel,
    _Inout_ QUIC_CONNECTION* Connection
);
```
- **功能**：更新定时器轮中指定连接的信息。
- **参数**：
  - `TimerWheel`：指向定时器轮的指针。
  - `Connection`：指向要更新的连接的指针。
- **实现步骤**：
  1. 获取连接的过期时间。
  2. 若连接已在定时器轮中：
     - 从双向链表中移除该连接。
     - 若过期时间为 `UINT64_MAX` 或连接已关闭，移除该连接并更新定时器轮。
  3. 若连接不在定时器轮中且过期时间不为 `UINT64_MAX` 且连接未关闭，增加连接计数并增加连接的定时器轮引用。
  4. 计算连接应插入的槽索引。
  5. 将连接插入到相应槽的双向链表中，按过期时间排序。
  6. 更新定时器轮的下一个过期时间和连接。
  7. 若连接计数超过槽数量乘以最大负载因子，调整定时器轮的大小。




  


