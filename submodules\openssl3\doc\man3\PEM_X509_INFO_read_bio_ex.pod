=pod

=head1 NAME

PEM_X509_INFO_read_ex, PEM_X509_INFO_read, PEM_X509_INFO_read_bio_ex, PEM_X509_INFO_read_bio
- read PEM-encoded data structures into one or more B<X509_INFO> objects

=head1 SYNOPSIS

 #include <openssl/pem.h>

 STACK_OF(X509_INFO) *PEM_X509_INFO_read_ex(FILE *fp, STACK_OF(X509_INFO) *sk,
                                            pem_password_cb *cb, void *u,
                                            OSSL_LIB_CTX *libctx,
                                            const char *propq);
 STACK_OF(X509_INFO) *PEM_X509_INFO_read(FILE *fp, STACK_OF(X509_INFO) *sk,
                                         pem_password_cb *cb, void *u);
 STACK_OF(X509_INFO) *PEM_X509_INFO_read_bio_ex(<PERSON><PERSON> *bio,
                                                STACK_OF(X509_INFO) *sk,
                                                pem_password_cb *cb, void *u,
                                                OSSL_LIB_CTX *libctx,
                                                const char *propq);
 STACK_OF(X509_INFO) *PEM_X509_INFO_read_bio(BIO *bp, STACK_OF(X509_INFO) *sk,
                                             pem_password_cb *cb, void *u);

=head1 DESCRIPTION

PEM_X509_INFO_read_ex() loads the B<X509_INFO> objects from a file I<fp>.

PEM_X509_INFO_read() is similar to PEM_X509_INFO_read_ex()
but uses the default (NULL) library context I<libctx>
and empty property query I<propq>.

PEM_X509_INFO_read_bio_ex() loads the B<X509_INFO> objects using a bio I<bp>.

PEM_X509_INFO_read_bio() is similar to PEM_X509_INFO_read_bio_ex()
but uses the default (NULL) library context I<libctx>
and empty property query I<propq>.

Each of the loaded B<X509_INFO> objects can contain a CRL, a certificate,
and/or a private key.
The elements are read sequentially, and as far as they are of different type than
the elements read before, they are combined into the same B<X509_INFO> object.
The idea behind this is that if, for instance, a certificate is followed by
a private key, the private key is supposed to correspond to the certificate.

If the input stack I<sk> is NULL a new stack is allocated,
else the given stack is extended.

The optional I<cb> and I<u> parameters can be used for providing a pass phrase
needed for decrypting encrypted PEM structures (normally only private keys).
See L<PEM_read_bio_PrivateKey(3)> and L<passphrase-encoding(7)> for details.

The library context I<libctx> and property query I<propq> are used for fetching
algorithms from providers.

=head1 RETURN VALUES

PEM_X509_INFO_read_ex(), PEM_X509_INFO_read(),
PEM_X509_INFO_read_bio_ex() and PEM_X509_INFO_read_bio() return
a stack of B<X509_INFO> objects or NULL on failure.

=head1 SEE ALSO

L<PEM_read_bio_ex(3)>,
L<PEM_read_bio_PrivateKey(3)>,
L<passphrase-encoding(7)>

=head1 HISTORY

The functions PEM_X509_INFO_read_ex() and
PEM_X509_INFO_read_bio_ex() were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
