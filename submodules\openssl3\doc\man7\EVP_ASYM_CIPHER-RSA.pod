=pod

=head1 NAME

EVP_ASYM_CIPHER-RSA
- RSA Asymmetric Cipher algorithm support

=head1 DESCRIPTION

Asymmetric Cipher support for the B<RSA> key type.

=head2 RSA Asymmetric Cipher parameters

=over 4

=item "pad-mode" (B<OSSL_ASYM_CIPHER_PARAM_PAD_MODE>) <UTF8 string>

The default provider understands these RSA padding modes in string form:

=over 4

=item "none" (B<OSSL_PKEY_RSA_PAD_MODE_NONE>)

=item "oaep" (B<OSSL_PKEY_RSA_PAD_MODE_OAEP>)

=item "pkcs1" (B<OSSL_PKEY_RSA_PAD_MODE_PKCSV15>)

=item "x931" (B<OSSL_PKEY_RSA_PAD_MODE_X931>)

=back

=item "pad-mode" (B<OSSL_ASYM_CIPHER_PARAM_PAD_MODE>) <integer>

The default provider understands these RSA padding modes in integer form:

=over 4

=item 1 (B<RSA_PKCS1_PADDING>)

=item 3 (B<RSA_NO_PADDING>)

=item 4 (B<RSA_PKCS1_OAEP_PADDING>)

=item 5 (B<RSA_X931_PADDING>)

=back

See L<EVP_PKEY_CTX_set_rsa_padding(3)> for further details.

=item "digest" (B<OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST>) <UTF8 string>

=item "digest-props" (B<OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS>) <UTF8 string>

=item "mgf1-digest" (B<OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST>) <UTF8 string>

=item "mgf1-digest-props" (B<OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST_PROPS>) <UTF8 string>

=item "oaep-label" (B<OSSL_ASYM_CIPHER_PARAM_OAEP_LABEL>) <octet string>

=item "tls-client-version" (B<OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION>) <unsigned integer>

See B<RSA_PKCS1_WITH_TLS_PADDING> on the page L<EVP_PKEY_CTX_set_rsa_padding(3)>.

=item "tls-negotiated-version" (B<OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION>) <unsigned integer>

See B<RSA_PKCS1_WITH_TLS_PADDING> on the page L<EVP_PKEY_CTX_set_rsa_padding(3)>.

See L<provider-asym_cipher(7)/Asymmetric Cipher Parameters> for more information.

=back

=head1 SEE ALSO

L<EVP_PKEY-RSA(7)>,
L<EVP_PKEY(3)>,
L<provider-asym_cipher(7)>,
L<provider-keymgmt(7)>,
L<OSSL_PROVIDER-default(7)>
L<OSSL_PROVIDER-FIPS(7)>

=head1 COPYRIGHT

Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
