engines:
  radon:
    enabled: true
    config:
      threshold: "C"
  pep8:
    enabled: true
  duplication:
    enabled: true
    exclude_fingerprints:
    # duplication in tlslite/utils/codec.py Writer.addTwo() .addFour()
    - 2e783666ce368f4223c1e7f5b162e2d9
    - 2c398389f33ea2572edefc5370ed49c0
    config:
      languages:
        python:
          python_version: 3
          mass_threshold: 35
  fixme:
    enabled: true
    config:
      strings:
        - TODO
        - FIXME
        - HACK
        - BUG
    checks:
      bug:
        enabled: true
  markdownlint:
    enabled: true
ratings:
  paths:
  - "tlslite/**"
  - "**.py"
exclude_paths:
 - "tests/**/*"
 - "scripts/**/*"
 - "unit_tests/**/*"
