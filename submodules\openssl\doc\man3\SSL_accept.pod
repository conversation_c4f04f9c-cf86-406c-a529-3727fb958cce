=pod

=head1 NAME

SSL_accept - wait for a TLS/SSL client to initiate a TLS/SSL handshake

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_accept(SSL *ssl);

=head1 DESCRIPTION

SSL_accept() waits for a TLS/SSL client to initiate the TLS/SSL handshake.
The communication channel must already have been set and assigned to the
B<ssl> by setting an underlying B<BIO>.

=head1 NOTES

The behaviour of SSL_accept() depends on the underlying BIO.

If the underlying BIO is B<blocking>, SSL_accept() will only return once the
handshake has been finished or an error occurred.

If the underlying BIO is B<nonblocking>, SSL_accept() will also return
when the underlying BIO could not satisfy the needs of SSL_accept()
to continue the handshake, indicating the problem by the return value -1.
In this case a call to SSL_get_error() with the
return value of SSL_accept() will yield B<SSL_ERROR_WANT_READ> or
B<SSL_ERROR_WANT_WRITE>. The calling process then must repeat the call after
taking appropriate action to satisfy the needs of <PERSON><PERSON>_accept().
The action depends on the underlying BIO. When using a nonblocking socket,
nothing is to be done, but select() can be used to check for the required
condition. When using a buffering BIO, like a BIO pair, data must be written
into or retrieved out of the BIO before being able to continue.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item Z<>0

The TLS/SSL handshake was not successful but was shut down controlled and
by the specifications of the TLS/SSL protocol. Call SSL_get_error() with the
return value B<ret> to find out the reason.

=item Z<>1

The TLS/SSL handshake was successfully completed, a TLS/SSL connection has been
established.

=item E<lt>0

The TLS/SSL handshake was not successful because a fatal error occurred either
at the protocol level or a connection failure occurred. The shutdown was
not clean. It can also occur if action is needed to continue the operation
for nonblocking BIOs. Call SSL_get_error() with the return value B<ret>
to find out the reason.

=back

=head1 SEE ALSO

L<SSL_get_error(3)>, L<SSL_connect(3)>,
L<SSL_shutdown(3)>, L<ssl(7)>, L<bio(7)>,
L<SSL_set_connect_state(3)>,
L<SSL_do_handshake(3)>,
L<SSL_CTX_new(3)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
