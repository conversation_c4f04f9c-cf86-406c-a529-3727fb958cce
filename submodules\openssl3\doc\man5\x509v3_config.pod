=pod

=head1 NAME

x509v3_config - X509 V3 certificate extension configuration format

=head1 DESCRIPTION

Several OpenSSL commands can add extensions to a certificate or
certificate request based on the contents of a configuration file
and CLI options such as B<-addext>.
The syntax of configuration files is described in L<config(5)>.
The commands typically have an option to specify the name of the configuration
file, and a section within that file; see the documentation of the
individual command for details.

This page uses B<extensions> as the name of the section, when needed
in examples.

Each entry in the extension section takes the form:

 name = [critical, ]value(s)

If B<critical> is present then the extension will be marked as critical.

If multiple entries are processed for the same extension name,
later entries override earlier ones with the same name.

The format of B<values> depends on the value of B<name>, many have a
type-value pairing where the type and value are separated by a colon.
There are four main types of extension:

 string
 multi-valued
 raw
 arbitrary

Each is described in the following paragraphs.

String extensions simply have a string which contains either the value itself
or how it is obtained.

Multi-valued extensions have a short form and a long form. The short form
is a comma-separated list of names and values:

 basicConstraints = critical, CA:true, pathlen:1

The long form allows the values to be placed in a separate section:

 [extensions]
 basicConstraints = critical, @basic_constraints

 [basic_constraints]
 CA = true
 pathlen = 1

Both forms are equivalent.

If an extension is multi-value and a field value must contain a comma the long
form must be used otherwise the comma would be misinterpreted as a field
separator. For example:

 subjectAltName = URI:ldap://somehost.com/CN=foo,OU=bar

will produce an error but the equivalent form:

 [extensions]
 subjectAltName = @subject_alt_section

 [subject_alt_section]
 subjectAltName = URI:ldap://somehost.com/CN=foo,OU=bar

is valid.

OpenSSL does not support multiple occurrences of the same field within a
section. In this example:

 [extensions]
 subjectAltName = @alt_section

 [alt_section]
 email = <EMAIL>
 email = <EMAIL>

will only recognize the last value.  To specify multiple values append a
numeric identifier, as shown here:

 [extensions]
 subjectAltName = @alt_section

 [alt_section]
 email.1 = <EMAIL>
 email.2 = <EMAIL>

The syntax of raw extensions is defined by the source code that parses
the extension but should be documented.
See L</Certificate Policies> for an example of a raw extension.

If an extension type is unsupported, then the I<arbitrary> extension syntax
must be used, see the L</ARBITRARY EXTENSIONS> section for more details.

=head1 STANDARD EXTENSIONS

The following sections describe the syntax of each supported extension.
They do not define the semantics of the extension.

=head2 Basic Constraints

This is a multi-valued extension which indicates whether a certificate is
a CA certificate. The first value is B<CA> followed by B<TRUE> or
B<FALSE>. If B<CA> is B<TRUE> then an optional B<pathlen> name followed by a
nonnegative value can be included.

For example:

 basicConstraints = CA:TRUE

 basicConstraints = CA:FALSE

 basicConstraints = critical, CA:TRUE, pathlen:1

A CA certificate I<must> include the B<basicConstraints> name with the B<CA>
parameter set to B<TRUE>. An end-user certificate must either have B<CA:FALSE>
or omit the extension entirely.
The B<pathlen> parameter specifies the maximum number of CAs that can appear
below this one in a chain. A B<pathlen> of zero means the CA cannot sign
any sub-CA's, and can only sign end-entity certificates.

=head2 Key Usage

Key usage is a multi-valued extension consisting of a list of names of
the permitted key usages.  The defined values are: C<digitalSignature>,
C<nonRepudiation>, C<keyEncipherment>, C<dataEncipherment>, C<keyAgreement>,
C<keyCertSign>, C<cRLSign>, C<encipherOnly>, and C<decipherOnly>.

Examples:

 keyUsage = digitalSignature, nonRepudiation

 keyUsage = critical, keyCertSign

=head2 Extended Key Usage

This extension consists of a list of values indicating purposes for which
the certificate public key can be used.
Each value can be either a short text name or an OID.
The following text names, and their intended meaning, are known:

 Value                  Meaning according to RFC 5280 etc.
 -----                  ----------------------------------
 serverAuth             SSL/TLS WWW Server Authentication
 clientAuth             SSL/TLS WWW Client Authentication
 codeSigning            Code Signing
 emailProtection        E-mail Protection (S/MIME)
 timeStamping           Trusted Timestamping
 OCSPSigning            OCSP Signing
 ipsecIKE               ipsec Internet Key Exchange
 msCodeInd              Microsoft Individual Code Signing (authenticode)
 msCodeCom              Microsoft Commercial Code Signing (authenticode)
 msCTLSign              Microsoft Trust List Signing
 msEFS                  Microsoft Encrypted File System

While IETF RFC 5280 says that B<id-kp-serverAuth> and B<id-kp-clientAuth>
are only for WWW use, in practice they are used for all kinds of TLS clients
and servers, and this is what OpenSSL assumes as well.

Examples:

 extendedKeyUsage = critical, codeSigning, *******

 extendedKeyUsage = serverAuth, clientAuth

=head2 Subject Key Identifier

The SKID extension specification has a value with three choices.
If the value is the word B<none> then no SKID extension will be included.
If the value is the word B<hash>, or by default for the B<x509>, B<req>, and
B<ca> apps, the process specified in RFC 5280 section *******. (1) is followed:
The keyIdentifier is composed of the 160-bit SHA-1 hash of the value of the BIT
STRING subjectPublicKey (excluding the tag, length, and number of unused bits).

Otherwise, the value must be a hex string (possibly with C<:> separating bytes)
to output directly, however, this is strongly discouraged.

Example:

 subjectKeyIdentifier = hash

=head2 Authority Key Identifier

The AKID extension specification may have the value B<none>
indicating that no AKID shall be included.
Otherwise it may have the value B<keyid> or B<issuer>
or both of them, separated by C<,>.
Either or both can have the option B<always>,
indicated by putting a colon C<:> between the value and this option.
For self-signed certificates the AKID is suppressed unless B<always> is present.
By default the B<x509>, B<req>, and B<ca> apps behave as if
"none" was given for self-signed certificates and "keyid, issuer" otherwise.

If B<keyid> is present, an attempt is made to
copy the subject key identifier (SKID) from the issuer certificate except if
the issuer certificate is the same as the current one and it is not self-signed.
The hash of the public key related to the signing key is taken as fallback
if the issuer certificate is the same as the current certificate.
If B<always> is present but no value can be obtained, an error is returned.

If B<issuer> is present, and in addition it has the option B<always> specified
or B<keyid> is not present,
then the issuer DN and serial number are copied from the issuer certificate.

Examples:

 authorityKeyIdentifier = keyid, issuer

 authorityKeyIdentifier = keyid, issuer:always

=head2 Subject Alternative Name

This is a multi-valued extension that supports several types of name
identifier, including
B<email> (an email address),
B<URI> (a uniform resource indicator),
B<DNS> (a DNS domain name),
B<RID> (a registered ID: OBJECT IDENTIFIER),
B<IP> (an IP address),
B<dirName> (a distinguished name),
and B<otherName>.
The syntax of each is described in the following paragraphs.

The B<email> option has two special values.
C<copy> will automatically include any email addresses
contained in the certificate subject name in the extension.
C<move> will automatically move any email addresses
from the certificate subject name to the extension.

The IP address used in the B<IP> option can be in either IPv4 or IPv6 format.

The value of B<dirName> is specifies the configuration section containing
the distinguished name to use, as a set of name-value pairs.
Multi-valued AVAs can be formed by prefacing the name with a B<+> character.

The value of B<otherName> can include arbitrary data associated with an OID;
the value should be the OID followed by a semicolon and the content in specified
using the syntax in L<ASN1_generate_nconf(3)>.

Examples:

 subjectAltName = email:copy, email:<EMAIL>, URI:http://my.example.com/

 subjectAltName = IP:***********

 subjectAltName = IP:13::17

 subjectAltName = email:<EMAIL>, RID:*******

 subjectAltName = otherName:*******;UTF8:some other identifier

 [extensions]
 subjectAltName = dirName:dir_sect

 [dir_sect]
 C = UK
 O = My Organization
 OU = My Unit
 CN = My Name

Non-ASCII Email Address conforming the syntax defined in Section 3.3 of RFC 6531
are provided as otherName.SmtpUTF8Mailbox. According to RFC 8398, the email
address should be provided as UTF8String. To enforce the valid representation in
the certificate, the SmtpUTF8Mailbox should be provided as follows

 subjectAltName=@alts
 [alts]
 otherName = *******.*******.9;FORMAT:UTF8,UTF8String:nonasciiname.example.com

=head2 Issuer Alternative Name

This extension supports most of the options of subject alternative name;
it does not support B<email:copy>.
It also adds B<issuer:copy> as an allowed value, which copies any subject
alternative names from the issuer certificate, if possible.

Example:

 issuerAltName = issuer:copy

=head2 Authority Info Access

This extension gives details about how to retrieve information that
related to the certificate that the CA makes available. The syntax is
B<access_id;location>, where B<access_id> is an object identifier
(although only a few values are well-known) and B<location> has the same
syntax as subject alternative name (except that B<email:copy> is not supported).

Possible values for access_id include B<OCSP> (OCSP responder),
B<caIssuers> (CA Issuers),
B<ad_timestamping> (AD Time Stamping),
B<AD_DVCS> (ad dvcs),
B<caRepository> (CA Repository).

Examples:

 authorityInfoAccess = OCSP;URI:http://ocsp.example.com/,caIssuers;URI:http://myca.example.com/ca.cer

 authorityInfoAccess = OCSP;URI:http://ocsp.example.com/

=head2 CRL distribution points

This is a multi-valued extension whose values can be either a name-value
pair using the same form as subject alternative name or a single value
specifying the section name containing all the distribution point values.

When a name-value pair is used, a DistributionPoint extension will
be set with the given value as the fullName field as the distributionPoint
value, and the reasons and cRLIssuer fields will be omitted.

When a single option is used, the value specifies the section, and that
section can have the following items:

=over 4

=item fullname

The full name of the distribution point, in the same format as the subject
alternative name.

=item relativename

The value is taken as a distinguished name fragment that is set as the
value of the nameRelativeToCRLIssuer field.

=item CRLIssuer

The value must in the same format as the subject alternative name.

=item reasons

A multi-value field that contains the reasons for revocation. The recognized
values are: C<keyCompromise>, C<CACompromise>, C<affiliationChanged>,
C<superseded>, C<cessationOfOperation>, C<certificateHold>,
C<privilegeWithdrawn>, and C<AACompromise>.

=back

Only one of B<fullname> or B<relativename> should be specified.

Simple examples:

 crlDistributionPoints = URI:http://example.com/myca.crl

 crlDistributionPoints = URI:http://example.com/myca.crl, URI:http://example.org/my.crl

Full distribution point example:

 [extensions]
 crlDistributionPoints = crldp1_section

 [crldp1_section]
 fullname = URI:http://example.com/myca.crl
 CRLissuer = dirName:issuer_sect
 reasons = keyCompromise, CACompromise

 [issuer_sect]
 C = UK
 O = Organisation
 CN = Some Name

=head2 Issuing Distribution Point

This extension should only appear in CRLs. It is a multi-valued extension
whose syntax is similar to the "section" pointed to by the CRL distribution
points extension. The following names have meaning:

=over 4

=item fullname

The full name of the distribution point, in the same format as the subject
alternative name.

=item relativename

The value is taken as a distinguished name fragment that is set as the
value of the nameRelativeToCRLIssuer field.

=item onlysomereasons

A multi-value field that contains the reasons for revocation. The recognized
values are: C<keyCompromise>, C<CACompromise>, C<affiliationChanged>,
C<superseded>, C<cessationOfOperation>, C<certificateHold>,
C<privilegeWithdrawn>, and C<AACompromise>.

=item onlyuser, onlyCA, onlyAA, indirectCRL

The value for each of these names is a boolean.

=back

Example:

 [extensions]
 issuingDistributionPoint = critical, @idp_section

 [idp_section]
 fullname = URI:http://example.com/myca.crl
 indirectCRL = TRUE
 onlysomereasons = keyCompromise, CACompromise

=head2 Certificate Policies

This is a I<raw> extension that supports all of the defined fields of the
certificate extension.

Policies without qualifiers are specified by giving the OID.
Multiple policies are comma-separated. For example:

 certificatePolicies = *******, *******

To include policy qualifiers, use the "@section" syntax to point to a
section that specifies all the information.

The section referred to must include the policy OID using the name
B<policyIdentifier>. cPSuri qualifiers can be included using the syntax:

 CPS.nnn = value

where C<nnn> is a number.

userNotice qualifiers can be set using the syntax:

 userNotice.nnn = @notice

The value of the userNotice qualifier is specified in the relevant section.
This section can include B<explicitText>, B<organization>, and B<noticeNumbers>
options. explicitText and organization are text strings, noticeNumbers is a
comma separated list of numbers. The organization and noticeNumbers options
(if included) must BOTH be present. Some software might require
the B<ia5org> option at the top level; this changes the encoding from
Displaytext to IA5String.

Example:

 [extensions]
 certificatePolicies = ia5org, *******, *******.8, @polsect

 [polsect]
 policyIdentifier = *******
 CPS.1 = "http://my.host.example.com/"
 CPS.2 = "http://my.your.example.com/"
 userNotice.1 = @notice

 [notice]
 explicitText = "Explicit Text Here"
 organization = "Organisation Name"
 noticeNumbers = 1, 2, 3, 4

The character encoding of explicitText can be specified by prefixing the
value with B<UTF8>, B<BMP>, or B<VISIBLE> followed by colon. For example:

 [notice]
 explicitText = "UTF8:Explicit Text Here"

=head2 Policy Constraints

This is a multi-valued extension which consisting of the names
B<requireExplicitPolicy> or B<inhibitPolicyMapping> and a non negative integer
value. At least one component must be present.

Example:

 policyConstraints = requireExplicitPolicy:3

=head2 Inhibit Any Policy

This is a string extension whose value must be a non negative integer.

Example:

 inhibitAnyPolicy = 2

=head2 Name Constraints

This is a multi-valued extension. The name should
begin with the word B<permitted> or B<excluded> followed by a B<;>. The rest of
the name and the value follows the syntax of subjectAltName except
B<email:copy>
is not supported and the B<IP> form should consist of an IP addresses and
subnet mask separated by a B</>.

Examples:

 nameConstraints = permitted;IP:***********/***********

 nameConstraints = permitted;email:.example.com

 nameConstraints = excluded;email:.com

=head2 OCSP No Check

This is a string extension. It is parsed, but ignored.

Example:

 noCheck = ignored

=head2 TLS Feature (aka Must Staple)

This is a multi-valued extension consisting of a list of TLS extension
identifiers. Each identifier may be a number (0..65535) or a supported name.
When a TLS client sends a listed extension, the TLS server is expected to
include that extension in its reply.

The supported names are: B<status_request> and B<status_request_v2>.

Example:

 tlsfeature = status_request

=head1 DEPRECATED EXTENSIONS

The following extensions are non standard, Netscape specific and largely
obsolete. Their use in new applications is discouraged.

=head2 Netscape String extensions

Netscape Comment (B<nsComment>) is a string extension containing a comment
which will be displayed when the certificate is viewed in some browsers.
Other extensions of this type are: B<nsBaseUrl>,
B<nsRevocationUrl>, B<nsCaRevocationUrl>, B<nsRenewalUrl>, B<nsCaPolicyUrl>
and B<nsSslServerName>.

=head2 Netscape Certificate Type

This is a multi-valued extensions which consists of a list of flags to be
included. It was used to indicate the purposes for which a certificate could
be used. The basicConstraints, keyUsage and extended key usage extensions are
now used instead.

Acceptable values for nsCertType are: B<client>, B<server>, B<email>,
B<objsign>, B<reserved>, B<sslCA>, B<emailCA>, B<objCA>.

=head1 ARBITRARY EXTENSIONS

If an extension is not supported by the OpenSSL code then it must be encoded
using the arbitrary extension format. It is also possible to use the arbitrary
format for supported extensions. Extreme care should be taken to ensure that
the data is formatted correctly for the given extension type.

There are two ways to encode arbitrary extensions.

The first way is to use the word ASN1 followed by the extension content
using the same syntax as L<ASN1_generate_nconf(3)>.
For example:

 [extensions]
 ******* = critical, ASN1:UTF8String:Some random data
 *******.1 = ASN1:SEQUENCE:seq_sect

 [seq_sect]
 field1 = UTF8:field1
 field2 = UTF8:field2

It is also possible to use the word DER to include the raw encoded data in any
extension.

 ******* = critical, DER:01:02:03:04
 *******.1 = DER:01020304

The value following DER is a hex dump of the DER encoding of the extension
Any extension can be placed in this form to override the default behaviour.
For example:

 basicConstraints = critical, DER:00:01:02:03

=head1 WARNINGS

There is no guarantee that a specific implementation will process a given
extension. It may therefore be sometimes possible to use certificates for
purposes prohibited by their extensions because a specific application does
not recognize or honour the values of the relevant extensions.

The DER and ASN1 options should be used with caution. It is possible to create
invalid extensions if they are not used carefully.

=head1 SEE ALSO

L<openssl-req(1)>, L<openssl-ca(1)>, L<openssl-x509(1)>,
L<ASN1_generate_nconf(3)>

=head1 COPYRIGHT

Copyright 2004-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
