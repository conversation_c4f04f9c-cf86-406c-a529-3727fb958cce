=pod

=head1 NAME

OSSL_SELF_TEST_set_callback,
OSSL_SELF_TEST_get_callback - specify a callback for processing self tests

=head1 SYNOPSIS

 #include <openssl/self_test.h>

 void OSSL_SELF_TEST_set_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK *cb, void *cbarg);
 void OSSL_SELF_TEST_get_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK **cb, void **cbarg);

=head1 DESCRIPTION

Set or gets the optional application callback (and the callback argument) that
is called during self testing.
The application callback L<OSSL_CALLBACK(3)> is associated with a B<OSSL_LIB_CTX>.
The application callback function receives information about a running self test,
and may return a result to the calling self test.
See L<openssl-core.h(7)> for further information on the callback.

=head1 RETURN VALUES

OSSL_SELF_TEST_get_callback() returns the callback and callback argument that
has been set via OSSL_SELF_TEST_set_callback() for the given library context
I<ctx>.
These returned parameters will be NULL if OSSL_SELF_TEST_set_callback() has
not been called.

=head1 SEE ALSO

L<openssl-core.h(7)>,
L<OSSL_PROVIDER-FIPS(7)>
L<OSSL_SELF_TEST_new(3)>
L<OSSL_LIB_CTX(3)>

=head1 HISTORY

The functions described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
