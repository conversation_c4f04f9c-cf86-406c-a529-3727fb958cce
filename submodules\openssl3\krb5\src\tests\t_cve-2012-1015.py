import base64
import socket
from k5test import *

realm = K5Realm()

# CVE-2012-1015 KDC frees uninitialized pointer

# Force a failure in krb5_c_make_checksum(), which causes the cleanup
# code in kdc_handle_protected_negotiation() to free an uninitialized
# pointer in an unpatched KDC.

s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
a = (hostname, realm.portbase)

x1 = base64.b16decode('6A81A030819DA103020105A20302010A' +
                      'A30E300C300AA10402020095A2020400' +
                      'A48180307EA00703050000000000A120' +
                      '301EA003020101A11730151B066B7262' +
                      '7467741B0B4B5242544553542E434F4D' +
                      'A20D1B0B4B5242544553542E434F4DA3' +
                      '20301EA003020101A11730151B066B72' +
                      '627467741B0B4B5242544553542E434F' +
                      '4DA511180F3139393430363130303630' +
                      '3331375AA7030201')

x2 = base64.b16decode('A8083006020106020112')

for x in range(0, 128):
    s.sendto(x1 + bytes([x]) + x2, a)

# Make sure kinit still works.

realm.kinit(realm.user_princ, password('user'))

success('CVE-2012-1015 regression test')
