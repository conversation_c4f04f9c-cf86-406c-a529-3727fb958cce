[{"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--cipherlist", "chacha20-poly1305", "--cipherlist", "aes256gcm", "--cipherlist", "aes128gcm", "--cipherlist", "aes256ccm", "--cipherlist", "aes128ccm", "--cipherlist", "aes256", "--cipherlist", "aes128", "--cipherlist", "3des", "--cipherlist", "aes256ccm_8", "--cipherlist", "aes128ccm_8", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-aesccm.py"}, {"name": "test-aesccm.py", "arguments": ["-d"]}, {"name": "test-aes-gcm-nonces.py"}, {"name": "test-alpn-negotiation.py", "arguments": ["-e", "renegotiation 2nd handshake alpn", "-e", "renegotiation with protocol change", "-e", "renegotiation without protocol change", "-e", "resumption with alpn change"], "comment": "tlslite-ng doesn't support renegotiation, only session id based resumption is supported"}, {"name": "test-alpn-negotiation.py", "arguments": ["-d", "-x", "renegotiation 2nd handshake alpn", "-x", "renegotiation with protocol change", "-x", "renegotiation without protocol change", "-x", "resumption with alpn change"], "comment": "tlslite-ng doesn't support renegotiation, only session id based resumption is supported"}, {"name": "test-atypical-padding.py"}, {"name": "test-b<PERSON><PERSON><PERSON>bacher-timing.py"}, {"name": "test-b<PERSON><PERSON><PERSON>bacher-timing.py", "arguments": ["--static-enc"]}, {"name": "test-b<PERSON><PERSON><PERSON><PERSON>er-workaround.py", "arguments": ["-t", "0.01"], "comment": "test over loopback, so 10ms RTT is entirely reasonable"}, {"name": "test-chacha20.py"}, {"name": "test-chacha20.py", "arguments": ["--extra-exts", "-x", "Chacha20 in TLS1.1", "-X", "\"handshake_failure\" does not match received \"insufficient_security\""], "comment": "as we're sending FFDHE groups and no ciphers valid for TLS 1.1, the server complains correctly about too weak parameters"}, {"name": "test-clienthello-md5.py"}, {"name": "test-client-hello-max-size.py"}, {"name": "test-client-hello-max-size.py", "arguments": ["-d"]}, {"name": "test-client-compatibility.py", "arguments": ["-x", "18: IE 6 on XP", "-x", "52: YandexBot 3.0 on unknown", "-x", "100: IE 6 on XP"], "comment": "SSLv3 is not supported by tlslite-ng by default"}, {"name": "test-client-compatibility.py", "arguments": ["-d", "sanity"], "comment": "non-sanity probes use specific ciphers, we cannot alter them with -d option"}, {"name": "test-conversation.py"}, {"name": "test-conversation.py", "arguments": ["-d"]}, {"name": "test-cve-2016-2107.py"}, {"name": "test-cve-2016-6309.py"}, {"name": "test-cve-2016-7054.py"}, {"name": "test-dhe-key-share-random.py", "arguments": ["--repeat=15", "-e", "Protocol (3, 0)", "-e", "Protocol (3, 0) in SSLv2 compatible ClientHello"], "comment": "SSLv3 is not supported by tlslite-ng by default"}, {"name": "test-dhe-no-shared-secret-padding.py", "arguments": ["-e", "Protocol (3, 0)", "-e", "Protocol (3, 0) in SSLv2 compatible ClientHello"], "comment": "SSLv3 is not supported by tlslite-ng by default"}, {"name": "test-dhe-rsa-key-exchange.py"}, {"name": "test-dhe-rsa-key-exchange-signatures.py"}, {"name": "test-dhe-rsa-key-exchange-with-bad-messages.py"}, {"name": "test-downgrade-protection.py", "comments": "expects server to support TLSv1.3 by default", "arguments": ["--server-max-protocol=TLSv1.3"]}, {"name": "test-early-application-data.py"}, {"name": "test-ecdhe-padded-shared-secret.py", "arguments": ["Protocol (3, 3) with secp256r1 group"], "comment": "the test is very slow and non-deterministic, run just one instance"}, {"name": "test-ecdhe-rsa-key-exchange.py"}, {"name": "test-ecdhe-rsa-key-exchange-with-bad-messages.py"}, {"name": "test-ecdhe-rsa-key-share-random.py", "arguments": ["--repeat=2", "-e", "Protocol (3, 0)", "-e", "Protocol (3, 0) in SSLv2 compatible ClientHello"], "comment": "SSLv3 is not supported by tlslite-ng by default"}, {"name": "test-empty-extensions.py"}, {"name": "test-encrypt-then-mac.py"}, {"name": "test-encrypt-then-mac.py", "arguments": ["-d"]}, {"name": "test-encrypt-then-mac-renegotiation.py", "arguments": ["-x", "Encrypt-then-MAC renegotiation crash"], "comment": "Test requires renegotiation support"}, {"name": "test-encrypt-then-mac-renegotiation.py", "arguments": ["-d", "-x", "Encrypt-then-MAC renegotiation crash"], "comment": "Test requires renegotiation support"}, {"name": "test-export-ciphers-rejected.py", "comment": "tlslite-ng doesn't support SSLv3 by default", "arguments": ["--min-ver", "TLSv1.0"]}, {"name": "test-export-ciphers-rejected.py", "arguments": ["-d", "--min-ver", "TLSv1.0"]}, {"name": "test-extensions.py"}, {"name": "test-extended-master-secret-extension.py", "arguments": ["-x", "extended master secret with renegotiation", "-x", "renegotiate with EMS in session without EMS", "-x", "renegotiate without EMS in session with EMS"], "comment": "Test requires renegotiation support"}, {"name": "test-extended-master-secret-extension.py", "arguments": ["-d", "-x", "extended master secret with renegotiation", "-x", "renegotiate with EMS in session without EMS", "-x", "renegotiate without EMS in session with EMS"], "comment": "Test requires renegotiation support"}, {"name": "test-fallback-scsv.py", "arguments": ["--tls-1.3"]}, {"name": "test-fallback-scsv.py", "arguments": ["--tls-1.3", "-d"]}, {"name": "test-ffdhe-expected-params.py", "arguments": ["--named-ffdh", "RFC5054 group 3"]}, {"name": "test-ffdhe-negotiation.py"}, {"name": "test-fuzzed-ciphertext.py"}, {"name": "test-fuzzed-ciphertext.py", "arguments": ["-d"]}, {"name": "test-fuzzed-finished.py"}, {"name": "test-fuzzed-MAC.py"}, {"name": "test-fuzzed-padding.py"}, {"name": "test-fuzzed-padding.py", "arguments": ["-d"]}, {"name": "test-fuzzed-plaintext.py"}, {"name": "test-fuzzed-plaintext.py", "arguments": ["-d"], "comment": "-d to test DHE key exchange"}, {"name": "test-fuzzed-plaintext.py", "arguments": ["-C", "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"]}, {"name": "test-heartbeat.py"}, {"name": "test-heartbeat.py", "arguments": ["-d"]}, {"name": "test-hello-request-by-client.py"}, {"name": "test-interleaved-application-data-and-fragmented-handshakes-in-renegotiation.py", "comment": "test requires renegotiation support", "exp_pass": false}, {"name": "test-interleaved-application-data-in-renegotiation.py", "comment": "test requires renegotiation support", "exp_pass": false}, {"name": "test-invalid-cipher-suites.py"}, {"name": "test-invalid-client-hello.py"}, {"name": "test-invalid-client-hello-w-record-overflow.py"}, {"name": "test-invalid-compression-methods.py"}, {"name": "test-invalid-content-type.py"}, {"name": "test-invalid-rsa-key-exchange-messages.py"}, {"name": "test-invalid-server-name-extension.py"}, {"name": "test-invalid-server-name-extension-resumption.py", "comment": "test requires support for multiple virtual hosts on server side", "exp_pass": false}, {"name": "test-invalid-session-id.py", "comment": "session id is not verified correctly by tlslite-ng", "exp_pass": false}, {"name": "test-invalid-version.py"}, {"name": "test-large-hello.py"}, {"name": "test-large-number-of-extensions.py"}, {"name": "test-large-number-of-extensions.py", "arguments": ["-d"]}, {"name": "test-legacy-renegotiation.py", "comment": "tlslite-ng does not support renegotiation", "exp_pass": false}, {"name": "test-legacy-renegotiation.py", "arguments": ["-d"], "comment": "tlslite-ng does not support renegotiation", "exp_pass": false}, {"name": "test-lucky13.py", "arguments": ["--quick"]}, {"name": "test-lucky13.py", "arguments": ["--quick", "-C", "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"]}, {"name": "test-message-duplication.py"}, {"name": "test-message-skipping.py"}, {"name": "test-message-skipping.py", "arguments": ["-d"]}, {"name": "test-no-heartbeat.py", "arguments": ["sanity"], "comment": "No support to disable heartbeats in tls.py"}, {"name": "test-no-heartbeat.py", "comment": "No support to disable heartbeats in tls.py", "exp_pass": false}, {"name": "test-ocsp-stapling.py", "comment": "test requires OCSP setup", "exp_pass": false}, {"name": "test-ocsp-stapling.py", "arguments": ["--no-status", "-e", "renegotiate", "-e", "renegotiate with large responder_id_list"], "comment": "test without OCSP setup, renegotiation unsupported"}, {"name": "test-ocsp-stapling.py", "arguments": ["-d", "--no-status", "-e", "renegotiate", "-e", "renegotiate with large responder_id_list"], "comment": "test without OCSP setup, renegotiation unsupported"}, {"name": "test-openssl-3712.py", "comment": "test requires renegotiation support", "exp_pass": false}, {"name": "test-record-layer-fragmentation.py"}, {"name": "test-record-size-limit.py", "comment": "tlslite-ng does not implement renegotiation", "arguments": ["--reply-AD-size", "{expected_size}", "--request", "GET /tests/test_file.txt HTTP/1.0\r\n\r\n", "--cookie", "--hrr-supported-groups", "-e", "renegotiation with changed limit", "-e", "renegotiation with dropped extension"]}, {"name": "test-renegotiation-changed-clienthello.py", "comment": "tlslite-ng does not implement renegotiation, see tlslite-ng#66", "exp_pass": false}, {"name": "test-renegotiation-disabled.py"}, {"name": "test-renegotiation-disabled-client-cert.py", "comment": "tlslite-ng does not implement renegotiation. tlslite-ng#66. The test requires client certs, but since we exclude the runs with them, we don't really use them, so can run it against any server", "arguments": ["-e", "try insecure (legacy) renegotiation", "-e", "try secure renegotiation", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem"]}, {"name": "test-resumption-with-wrong-ciphers.py"}, {"name": "test-resumption-with-wrong-ciphers.py", "arguments": ["-d"]}, {"name": "test-serverhello-random.py", "arguments": ["-x", "Protocol (3, 0)", "-X", "protocol_version", "-x", "Protocol (3, 0) in SSLv2 compatible ClientHello", "-X", "protocol_version"], "comment": "tlslite-ng does not support SSLv3 by default"}, {"name": "test-sessionID-resumption.py"}, {"name": "test-sig-algs.py", "comment": "server has just one certificate configured", "arguments": ["-x", "rsa_pss_pss_sha256 only", "-x", "rsa_pss_pss_sha384 only", "-x", "rsa_pss_pss_sha512 only"]}, {"name": "test-sig-algs-renegotiation-resumption.py", "comment": "tlslite-ng doesn't support renegotiation: #66", "arguments": ["--no-renego", "--sig-algs-drop-ok"]}, {"name": "test-signature-algorithms.py"}, {"name": "test-ssl-death-alert.py", "arguments": ["--alerts", "0", "--alert-level", "warning", "--alert-description", "close_notify"], "comment": "tlslite-ng does not allow client to send warning alerts, thus the number of allowed alerts (--alerts) is 0"}, {"name": "test-ssl-death-alert.py", "arguments": ["-d", "--alerts", "0", "--alert-level", "warning", "--alert-description", "close_notify"], "comment": "tlslite-ng does not allow client to send warning alerts, thus the number of allowed alerts (--alerts) is 0"}, {"name": "test-sslv2-connection.py"}, {"name": "test-sslv2-force-cipher-3des.py"}, {"name": "test-sslv2-force-cipher-non3des.py"}, {"name": "test-sslv2-force-cipher.py"}, {"name": "test-sslv2-force-export-cipher.py"}, {"name": "test-sslv2hello-protocol.py"}, {"name": "test-sslv2hello-protocol.py", "arguments": ["-d"]}, {"name": "test-tls13-0rtt-garbage.py", "arguments": ["--cookie"]}, {"name": "test-tls13-ccs.py"}, {"name": "test-tls13-conversation.py"}, {"name": "test-tls13-count-tickets.py", "arguments": ["-t", "2"]}, {"name": "test-tls13-crfg-curves.py"}, {"name": "test-tls13-dhe-shared-secret-padding.py"}, {"name": "test-tls13-ecdhe-curves.py"}, {"name": "test-tls13-empty-alert.py"}, {"name": "test-tls13-ffdhe-groups.py"}, {"name": "test-tls13-ffdhe-sanity.py"}, {"name": "test-tls13-finished.py"}, {"name": "test-tls13-finished-plaintext.py"}, {"name": "test-tls13-hrr.py", "arguments": ["--cookie"]}, {"name": "test-tls13-invalid-ciphers.py"}, {"name": "test-tls13-keyshare-omitted.py"}, {"name": "test-tls13-keyupdate.py", "comment": "bug tlslite-ng #342", "arguments": ["-e", "1/4 fragmented keyupdate msg, appdata between", "-e", "2/3 fragmented keyupdate msg, appdata between", "-e", "3/2 fragmented keyupdate msg, appdata between", "-e", "4/1 fragmented keyupdate msg, appdata between"]}, {"name": "test-tls13-keyupdate-from-server.py"}, {"name": "test-tls13-large-number-of-extensions.py", "arguments": ["--exc", "9", "--exc", "11", "--exc", "12", "--exc", "23", "--exc", "13172"], "comment": "several tls12 extensions must be excluded, tlslite-ng issue #314"}, {"name": "test-tls13-large-number-of-extensions.py", "arguments": ["--separate", "12000", "empty unassigned extension id 12000"], "comment": "just checks if the --separate option works"}, {"name": "test-tls13-legacy-version.py"}, {"name": "test-tls13-multiple-ccs-messages.py", "arguments": ["-x", "second CCS before client finished", "-X", "Handshake(new_session_ticket)", "-x", "multiple CCS Messages in one TCP packet", "-X", "Timeout", "-x", "CH and multiple CCS Messages in one TCP packet", "-X", "Timeout", "-x", "multiple CCS Messages in one TLS record", "-X", "Timeout"], "comment": "tlslite actually allows multiple CCS messages"}, {"name": "test-tls13-nociphers.py"}, {"name": "test-tls13-obsolete-curves.py", "arguments": ["--cookie"]}, {"name": "test-tls13-pkcs-signature.py"}, {"name": "test-tls13-post-handshake-auth.py", "arguments": ["-k", "tests/clientX509Key.pem", "-c", "tests/clientX509Cert.pem", "--pha-as-reply", "--query", "GET /secret HTTP/1.0\r\n\r\n"]}, {"name": "test-tls13-record-layer-limits.py"}, {"name": "test-tls13-record-padding.py"}, {"name": "test-tls13-rsa-signatures.py"}, {"name": "test-tls13-serverhello-random.py", "arguments": ["--repeat", "5"]}, {"name": "test-tls13-session-resumption.py"}, {"name": "test-tls13-shuffled-extentions.py", "arguments": ["--cookie"]}, {"name": "test-tls13-signature-algorithms.py"}, {"name": "test-tls13-symetric-ciphers.py"}, {"name": "test-tls13-unrecognised-groups.py", "arguments": ["--cookie"]}, {"name": "test-tls13-version-negotiation.py"}, {"name": "test-tls13-version-negotiation.py", "arguments": ["-d"]}, {"name": "test-tls13-zero-content-type.py"}, {"name": "test-tls13-zero-length-data.py"}, {"name": "test-TLSv1_2-rejected-without-TLSv1_2.py"}, {"name": "test-truncating-of-client-hello.py"}, {"name": "test-truncating-of-finished.py"}, {"name": "test-truncating-of-kRSA-client-key-exchange.py"}, {"name": "test-unsupported-curve-fallback.py"}, {"name": "test-version-numbers.py"}, {"name": "test-x25519.py"}, {"name": "test-zero-length-data.py"}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--reqcert", "localhost:4433"], "environment": {"PYTHONPATH": "."}, "common_arguments": ["-k", "tests/clientX509Key.pem", "-c", "tests/clientX509Cert.pem"], "tests": [{"name": "test-certificate-malformed.py"}, {"name": "test-certificate-request.py"}, {"name": "test-certificate-request.py", "arguments": ["-k", "tests/clientRSAPSSKey.pem", "-c", "tests/clientRSAPSSCert.pem"]}, {"name": "test-certificate-verify-malformed-sig.py"}, {"name": "test-certificate-verify-malformed-sig.py", "arguments": ["-d"]}, {"name": "test-certificate-verify-malformed.py"}, {"name": "test-certificate-verify.py"}, {"name": "test-certificate-verify.py", "arguments": ["-d"]}, {"name": "test-ecdsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverECKey.pem", "-c", "tests/serverECCert.pem"]}, {"name": "test-eddsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverEd25519Key.pem", "-c", "tests/serverEd25519Cert.pem"]}, {"name": "test-eddsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverEd448Key.pem", "-c", "tests/serverEd448Cert.pem"]}, {"name": "test-rsa-pss-sigs-on-certificate-verify.py", "arguments": ["--illeg<PERSON>"]}, {"name": "test-rsa-pss-sigs-on-certificate-verify.py", "arguments": ["-k", "tests/clientRSAPSSKey.pem", "-c", "tests/clientRSAPSSCert.pem", "--illeg<PERSON>"]}, {"name": "test-extended-master-secret-extension-with-client-cert.py"}, {"name": "test-rsa-sigs-on-certificate-verify.py"}, {"name": "test-rsa-sigs-on-certificate-verify.py", "arguments": ["-d"]}, {"name": "test-tls13-certificate-request.py"}, {"name": "test-tls13-certificate-request.py", "comment": "Test with additional extensions", "arguments": ["-E", "status_request:CH:CT key_share:SH supported_versions:SH"]}, {"name": "test-tls13-certificate-verify.py", "arguments": ["-k", "tests/clientRSAPSSKey.pem", "-c", "tests/clientRSAPSSCert.pem"]}, {"name": "test-tls13-certificate-verify.py"}, {"name": "test-tls13-ecdsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverECKey.pem", "-c", "tests/serverECCert.pem"]}, {"name": "test-tls13-eddsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverEd25519Key.pem", "-c", "tests/serverEd25519Cert.pem"]}, {"name": "test-tls13-eddsa-in-certificate-verify.py", "arguments": ["-k", "tests/serverEd448Key.pem", "-c", "tests/serverEd448Cert.pem"]}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--ssl3", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "comment": "Tests of the SSLv3 implementation", "tests": [{"name": "test-dhe-key-share-random.py", "arguments": ["--repeat=15"]}, {"name": "test-ecdhe-rsa-key-share-random.py", "arguments": ["--repeat=2"]}, {"name": "test-serverhello-random.py"}, {"name": "test-SSLv3-padding.py"}]}, {"server_command": ["{python}", "-u", "{command}", "server", "--psk", "aa", "--psk-ident", "test", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "comment": "Tests of the TLSv1.3 PSK key exchanges with SHA256 PRF", "tests": [{"name": "test-tls13-psk_ke.py"}, {"name": "test-tls13-psk_dhe_ke.py"}]}, {"server_command": ["{python}", "-u", "{command}", "server", "--psk", "aa", "--psk-ident", "test", "--psk-sha384", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "common_arguments": ["--psk-sha384"], "comment": "Tests of the TLSv1.3 PSK key exchanges with SHA384 PRF", "tests": [{"name": "test-tls13-psk_ke.py"}, {"name": "test-tls13-psk_dhe_ke.py"}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverRSAPSSKey.pem", "-c", "tests/serverRSAPSSCert.pem", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-tls13-rsapss-signatures.py"}, {"name": "test-sig-algs.py", "comment": "the server has just one certificate installed", "arguments": ["-x", "rsa_pss_rsae_sha256 only", "-x", "rsa_pss_rsae_sha384 only", "-x", "rsa_pss_rsae_sha512 only"]}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--max-ver", "tls1.2", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-downgrade-protection.py", "comments": "expects server to support TLSv1.2 by default", "arguments": ["--server-max-protocol=TLSv1.2"]}, {"name": "test-fallback-scsv.py"}, {"name": "test-fallback-scsv.py", "arguments": ["-d"]}, {"name": "test-tls13-non-support.py"}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--request-pha", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-tls13-post-handshake-auth.py", "arguments": ["-k", "tests/clientX509Key.pem", "-c", "tests/clientX509Cert.pem"]}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-k", "tests/serverX509Key.pem", "-c", "tests/serverX509Cert.pem", "--require-pha", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-tls13-post-handshake-auth.py", "arguments": ["-k", "tests/clientX509Key.pem", "-c", "tests/clientX509Cert.pem", "--pha-as-reply", "--cert-required", "--min-tickets=2"]}]}, {"server_command": ["{python}", "-u", "{command}", "server", "-c", "tests/serverECCert.pem", "-k", "tests/serverECKey.pem", "-c", "tests/serverP384ECCert.pem", "-k", "tests/serverP384ECKey.pem", "-c", "tests/serverP521ECCert.pem", "-k", "tests/serverP521ECKey.pem", "--cipherlist", "chacha20-poly1305", "--cipherlist", "aes256gcm", "--cipherlist", "aes128gcm", "--cipherlist", "aes256ccm", "--cipherlist", "aes128ccm", "--cipherlist", "aes256", "--cipherlist", "aes128", "--cipherlist", "3des", "--cipherlist", "aes256ccm_8", "--cipherlist", "aes128ccm_8", "localhost:4433"], "server_hostname": "localhost", "server_port": 4433, "environment": {"PYTHONPATH": "."}, "tests": [{"name": "test-aesccm.py", "comments": "script by default uses kRSA, use (EC)DHE instead", "arguments": ["-d"]}, {"name": "test-conversation.py", "comments": "script by default uses kRSA, use (EC)DHE instead", "arguments": ["-d"]}, {"name": "test-ecdsa-sig-flexibility.py"}, {"name": "test-fuzzed-plaintext.py", "arguments": ["-C", "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"]}, {"name": "test-renegotiation-disabled-client-cert.py", "comment": "tlslite-ng does not implement renegotiation. tlslite-ng#66. The test requires client certs, but since we exclude the runs with them, we don't really use them, so can run it against any server", "arguments": ["-e", "try insecure (legacy) renegotiation", "-e", "try secure renegotiation", "-k", "tests/serverECKey.pem", "-c", "tests/serverECCert.pem", "-d"]}, {"name": "test-signature-algorithms.py", "comments": "use ECDSA signatures instead of RSA", "arguments": ["--ecdsa"]}, {"name": "test-tls13-conversation.py"}, {"name": "test-tls13-ecdsa-support.py"}]}]