﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="$(WindowsSdkDir)\Debuggers\inc\engextcpp.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="analyze.cpp" />
    <ClCompile Include="binding.cpp" />
    <ClCompile Include="configuration.cpp" />
    <ClCompile Include="connection.cpp" />
    <ClCompile Include="dump.cpp" />
    <ClCompile Include="handle.cpp" />
    <ClCompile Include="library.cpp" />
    <ClCompile Include="listener.cpp" />
    <ClCompile Include="packet.cpp" />
    <ClCompile Include="quicdbg.cpp" />
    <ClCompile Include="registration.cpp" />
    <ClCompile Include="stream.cpp" />
    <ClCompile Include="worker.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="quicdbg.h" />
    <ClInclude Include="quictypes.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="quic.def" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{3EE62742-8A44-4D09-AD78-5EDB2FBDCE18}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup>
    <TargetName>quic</TargetName>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <OutDir>$(SolutionDir)..\..\artifacts\bin\windbg\$(Platform)_$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)..\..\build\windbg\$(Platform)_$(Configuration)\obj\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ItemDefinitionGroup>
    <ClCompile>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(WindowsSdkDir)Debuggers\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>dbgeng.lib;ntdll.lib;ws2_32.lib</AdditionalDependencies>
      <ModuleDefinitionFile>quic.def</ModuleDefinitionFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>