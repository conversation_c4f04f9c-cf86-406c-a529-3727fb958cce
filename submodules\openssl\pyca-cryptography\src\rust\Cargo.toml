[package]
name = "cryptography-rust"
version = "0.1.0"
authors = ["The cryptography developers <<EMAIL>>"]
edition = "2018"
publish = false

[dependencies]
lazy_static = "1"
pyo3 = { version = "0.14.5" }
asn1 = { version = "0.8.2", default-features = false, features = ["derive"] }
pem = "1.0"
chrono = { version = "0.4", default-features = false, features = ["alloc"] }
ouroboros = "0.13"

[features]
extension-module = ["pyo3/extension-module"]
default = ["extension-module"]

[lib]
name = "cryptography_rust"
crate-type = ["cdylib"]

[profile.release]
lto = "thin"
overflow-checks = true
