/* -*- mode: c; c-basic-offset: 4; indent-tabs-mode: nil -*- */
/*  plugins/audit/kdc_j_encode.h - Declarations for KDC audit json encoders */
/*
 * Copyright 2013 by the Massachusetts Institute of Technology.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * * Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * * Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef KRB5_KDC_J_ENCODE_H_INCLUDED
#define KRB5_KDC_J_ENCODE_H_INCLUDED

#include <krb5/audit_plugin.h>

/* Maximum length of the name of preauth type. */
#define MAX_PATYPE_NAME_LEN 32

krb5_error_code
kau_j_kdc_stop(const krb5_boolean ev_success, char **jout);

krb5_error_code
kau_j_kdc_start(const krb5_boolean ev_success, char **jout);

krb5_error_code
kau_j_as_req(const krb5_boolean ev_success, krb5_audit_state *state,
             char **jout);

krb5_error_code
kau_j_tgs_req(const krb5_boolean ev_success, krb5_audit_state *state,
              char **jout);

krb5_error_code
kau_j_tgs_s4u2self(const krb5_boolean ev_success, krb5_audit_state *state,
                   char **jout);

krb5_error_code
kau_j_tgs_s4u2proxy(const krb5_boolean ev_success, krb5_audit_state *state,
                    char **jout);

krb5_error_code
kau_j_tgs_u2u(const krb5_boolean ev_success, krb5_audit_state *state,
              char **jout);

#endif /* KRB5_KDC_J_ENCODE_H_INCLUDED */
