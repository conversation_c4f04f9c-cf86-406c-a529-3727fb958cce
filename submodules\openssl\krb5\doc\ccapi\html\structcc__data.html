<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_data Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_data Struct Reference<br>
<small>
[<a class="el" href="group__cc__credentials__reference.html">cc_credentials_t Overview</a>]</small>
</h1><!-- doxytag: class="cc_data" --><hr><a name="_details"></a><h2>Detailed Description</h2>
The CCAPI data structure. This structure is similar to a krb5_data structure. In a v5 credentials structure, <a class="el" href="structcc__data.html">cc_data</a> structures are used to store tagged variable-length binary data. Specifically, for cc_credentials_v5.ticket and cc_credentials_v5.second_ticket, the <a class="el" href="structcc__data.html#1cfc8b2545d7999b7a760b47bfbbf6e7">cc_data.type</a> field must be zero. For the cc_credentials_v5.addresses, cc_credentials_v5.authdata, and cc_credentials_v5.keyblock, the <a class="el" href="structcc__data.html#1cfc8b2545d7999b7a760b47bfbbf6e7">cc_data.type</a> field should be the address type, authorization data type, and encryption type, as defined by the Kerberos v5 protocol definition. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__data.html#1cfc8b2545d7999b7a760b47bfbbf6e7">type</a>
<li><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__data.html#61dffab9209fdc97d53c4cb31f746aa5">length</a>
<li>void * <a class="el" href="structcc__data.html#735984d41155bc1032e09bece8f8d66d">data</a>
</ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="1cfc8b2545d7999b7a760b47bfbbf6e7"></a><!-- doxytag: member="cc_data::type" ref="1cfc8b2545d7999b7a760b47bfbbf6e7" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__data.html#1cfc8b2545d7999b7a760b47bfbbf6e7">type</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The type of the data as defined by the krb5_data structure.     </td>
  </tr>
</table>
<a class="anchor" name="61dffab9209fdc97d53c4cb31f746aa5"></a><!-- doxytag: member="cc_data::length" ref="61dffab9209fdc97d53c4cb31f746aa5" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> <a class="el" href="structcc__data.html#61dffab9209fdc97d53c4cb31f746aa5">length</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The length of <em>data</em>.     </td>
  </tr>
</table>
<a class="anchor" name="735984d41155bc1032e09bece8f8d66d"></a><!-- doxytag: member="cc_data::data" ref="735984d41155bc1032e09bece8f8d66d" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">void* <a class="el" href="structcc__data.html#735984d41155bc1032e09bece8f8d66d">data</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
The data buffer.     </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
