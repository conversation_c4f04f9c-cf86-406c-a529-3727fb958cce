#
# Copyright 2001-2019 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line.  Lines starting with a pound sign,
# like this prolog, are ignored.

# SIPHASH tests - default values: 2,4 rounds, 16-byte mac
# There are no official test vectors, they are simple vectors 1, 2, 3, etc

Title = SIPHASH tests

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input =
Output = a3817f04ba25a8e66df67214c7550293

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00
Output = da87c1d86b99af44347659119b22fc45

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001
Output = 8177228da4a45dc7fca38bdef60affe4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102
Output = 9c70b60c5267a94e5f33b6b02985ed51

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203
Output = f88164c12d9c8faf7d0f6e7c7bcd5579

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304
Output = 1368875980776f8854527a07690e9627

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405
Output = 14eeca338b208613485ea0308fd7a15e

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203040506
Output = a1f1ebbed8dbc153c0b84aa61ff08239

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304050607
Output = 3b62a9ba6258f5610f83e264f31497b4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708
Output = 264499060ad9baabc47f8b02bb6d71ed

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c


MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input =
Output = a3817f04ba25a8e66df67214c7550293

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00
Output = da87c1d86b99af44347659119b22fc45

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001
Output = 8177228da4a45dc7fca38bdef60affe4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102
Output = 9c70b60c5267a94e5f33b6b02985ed51

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203
Output = f88164c12d9c8faf7d0f6e7c7bcd5579

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304
Output = 1368875980776f8854527a07690e9627

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405
Output = 14eeca338b208613485ea0308fd7a15e

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203040506
Output = a1f1ebbed8dbc153c0b84aa61ff08239

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304050607
Output = 3b62a9ba6258f5610f83e264f31497b4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708
Output = 264499060ad9baabc47f8b02bb6d71ed

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 8-byte mac

MAC = SipHash
Ctrl = digestsize:8
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 724506EB4C328A95

# SIPHASH - default values: 2,4 rounds, explicit 16-byte mac

MAC = SipHash
Ctrl = digestsize:16
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 16-byte mac (set as 0)

MAC = SipHash
Ctrl = digestsize:0
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 13-byte mac (invalid size)

MAC = SipHash
Ctrl = digestsize:13
Key = 000102030405060708090A0B0C0D0E0F
Result = EVPPKEYCTXCTRL_ERROR

Title = HMAC tests (from RFC2104 and others)

MAC = HMAC
Algorithm = MD5
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Input = "Hi There"
Output = 9294727a3638bb1c13f48ef8158bfc9d

MAC = HMAC
Algorithm = MD5
Key = "Jefe"
Input = "what do ya want for nothing?"
Output = 750c783e6ab0b503eaa86e310a5db738

MAC = HMAC
Algorithm = MD5
Key = AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
Input = DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD
Output = 56be34521d144c88dbb8c733f0e8b3f6

Title = SHA1

# HMAC tests from NIST test data

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 5FD596EE78D5553C8FF4E72D266DFD192366DA29

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F10111213
Output = 4C99FF0CB1B31BD33F8431DBAF4D17FCD356A807

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 2D51B2F7750E410584662E38F133435F4C4FD42A

Title = SHA2

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = C7405E3AE058E8CD30B08B4140248581ED174CB34E1224BCC1EFC81B

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B
Output = E3D249A8CFB67EF8B7A169E9A0A599714A2CECBA65999A51BEB8FBBE

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 91C52509E5AF8531601AE6230099D90BEF88AAEFB961F4080ABC014D

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 8BB9A1DB9806F20DF7F77B82138C7914D174D59E13DC4D0169C9057B133E1D62

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Output = A28CF43130EE696A98F14A37678B56BCFCBDD9E5CF69717FECF5480F0EBDF790

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = BDCCB6C72DDEADB500AE768386CB38CC41C63DBB0878DDB9C7A38A431B78378D

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = 63C5DAA5E651847CA897C95814AB830BEDEDC7D25E83EEF9195CD45857A37F448947858F5AF50CC2B1B730DDF29671A9

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F
Output = 6EB242BDBB582CA17BEBFA481B1E23211464D2B7F8C20B9FF2201637B93646AF5AE9AC316E98DB45D9CAE773675EEED0

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = 5B664436DF69B0CA22551231A3F0A3D5B4F97991713CFA84BFF4D0792EFF96C27DCCBBB6F79B65D548B40E8564CEF594

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = FC25E240658CA785B7A811A8D3F7B4CA48CFA26A8A366BF2CD1F836B05FCB024BD36853081811D6CEA4216EBAD79DA1CFCB95EA4586B8A0CE356596A55FB1347

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = FD44C18BDA0BB0A6CE0E82B031BF2818F6539BD56EC00BDC10A8A2D730B3634DE2545D639B0F2CF710D0692C72A1896F1F211C2B922D1A96C392E07E7EA9FEDC

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = D93EC8D2DE1AD2A9957CB9B83F14E76AD6B5E0CCE285079A127D3B14BCCB7AA7286D4AC0D4CE64215F2BC9E6870B33D97438BE4AAA20CDA5C5A912B48B8E27F3

Title = SHA3

# NIST's test vectors

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b
Output = 332cfd59347fdb8e576e77260be4aba2d6dc53117b3bfb52c6d18c04

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f
Output = d8b733bcf66c644a12323d564e24dcf3fc75f231f3b67968359100c7

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaab
Output = 078695eecc227c636ad31d063a15dd05a7e819a66ec6d8de1e193e59

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Output = 4fe8e202c4f058e8dddc23d8c34e467343e23555e24fc2f025d598f558f67205

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687
Output = 68b94e2e538a9be4103bebb5aa016d47961d4d1aa906061313b557f8af2c3faa

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7
Output = 9bcf2c238e235c3ce88404e813bd2f3a97185ac6f238c63d6229a00b07974258

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f
Output = d588a3c51f3f2d906e8298c1199aa8ff6296218127f6b38a90b6afe2c5617725bc99987f79b22a557b6520db710b7f42

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061626364656667
Output = a27d24b592e8c8cbf6d4ce6fc5bf62d8fc98bf2d486640d9eb8099e24047837f5f3bffbe92dcce90b4ed5b1e7e44fa90

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091929394959697
Output = e5ae4c739f455279368ebf36d4f5354c95aa184c899d3870e460ebc288ef1f9470053f73f7c6da2a71bcaec38ce7d6ac

MAC = HMAC
Algorithm = SHA3-512
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Output = 4efd629d6c71bf86162658f29943b1c308ce27cdfa6db0d9c3ce81763f9cbce5f7ebe9868031db1a8f8eb7b6b95e5c5e3f657a8996c86a2f6527e307f0213196

MAC = HMAC
Algorithm = SHA3-512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f4041424344454647
Output = 544e257ea2a3e5ea19a590e6a24b724ce6327757723fe2751b75bf007d80f6b360744bf1b7a88ea585f9765b47911976d3191cf83c039f5ffab0d29cc9d9b6da

MAC = HMAC
Algorithm = SHA3-512
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687
Output = 5f464f5e5b7848e3885e49b2c385f0694985d0e38966242dc4a5fe3fea4b37d46b65ceced5dcf59438dd840bab22269f0ba7febdb9fcf74602a35666b2a32915

Title = HMAC self generated tests

MAC = HMAC
Algorithm = SHAKE128
Input = "Test that SHAKE128 fails"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Result = DIGESTSIGNINIT_ERROR


Title = CMAC tests (from FIPS module)

MAC = CMAC
Algorithm = AES-128-CBC
Key = 77A77FAF290C1FA30C683DF16BA7A77B
Input = 020683E1F0392F4CAC54318B6029259E9C553DBC4B6AD998E64D58E4E7DC2E13
Output = FBFEA41BF9740CB501F1292C21CEBB40

MAC = CMAC
Algorithm = AES-192-CBC
Key = 7B32391369AA4CA97558095BE3C3EC862BD057CEF1E32D62
Input =
Output = E4D9340B03E67DEFD4969CC1ED3735E6

MAC = CMAC
Algorithm = AES-256-CBC
Key = 0B122AC8F34ED1FE082A3625D157561454167AC145A10BBF77C6A70596D574F1
Input = 498B53FDEC87EDCBF07097DCCDE93A084BAD7501A224E388DF349CE18959FE8485F8AD1537F0D896EA73BEDC7214713F
Output = F62C46329B41085625669BAF51DEA66A

MAC = CMAC
Algorithm = DES-EDE3-CBC
Key = 89BCD952A8C8AB371AF48AC7D07085D5EFF702E6D62CDC23
Input = FA620C1BBE97319E9A0CF0492121F7A20EB08A6A709DCBD00AAF38E4F99E754E
Output = 8F49A1B7D6AA2258

Title = Poly1305 Tests (from RFC 7539 and others)

MAC = Poly1305
Key = 0000000000000000000000000000000000000000000000000000000000000000
Input = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Output = 00000000000000000000000000000000

MAC = Poly1305
Key = 0000000000000000000000000000000036e5f6b5c5e06070f0efca96227a863e
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = 36e5f6b5c5e06070f0efca96227a863e

MAC = Poly1305
Key = 36e5f6b5c5e06070f0efca96227a863e00000000000000000000000000000000
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = f3477e7cd95417af89a6b8794c310cf0

MAC = Poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
Input = 2754776173206272696c6c69672c20616e642074686520736c6974687920746f7665730a446964206779726520616e642067696d626c6520696e2074686520776162653a0a416c6c206d696d737920776572652074686520626f726f676f7665732c0a416e6420746865206d6f6d65207261746873206f757467726162652e
Output = 4541669a7eaaee61e708dc7cbcc5eb62

# If one uses 130-bit partial reduction, does the code handle the case where partially reduced final result is not fully reduced?
MAC = Poly1305
Key = 0200000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Output = 03000000000000000000000000000000

# What happens if addition of s overflows modulo 2^128?
MAC = Poly1305
Key = 02000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Input = 02000000000000000000000000000000
Output = 03000000000000000000000000000000

# What happens if data limb is all ones and there is carry from lower limb?
MAC = Poly1305
Key = 0100000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFF11000000000000000000000000000000
Output = 05000000000000000000000000000000

# What happens if final result from polynomial part is exactly 2^130-5?
MAC = Poly1305
Key = 0100000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBFEFEFEFEFEFEFEFEFEFEFEFEFEFEFE01010101010101010101010101010101
Output = 00000000000000000000000000000000

# What happens if final result from polynomial part is exactly 2^130-6?
MAC = Poly1305
Key = 0200000000000000000000000000000000000000000000000000000000000000
Input = FDFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Output = FAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF

# Taken from poly1305_internal_test.c
# More RFC7539

MAC = Poly1305
Input = 43727970746f6772617068696320466f72756d2052657365617263682047726f7570
Key = 85d6be7857556d337f4452fe42d506a80103808afb0db2fd4abff6af4149f51b
Output = a8061dc1305136c6c22b8baf0c0127a9

# test vectors from "The Poly1305-AES message-authentication code"

MAC = Poly1305
Input = f3f6
Key = 851fc40c3467ac0be05cc20404f3f700580b3b0f9447bb1e69d095b5928b6dbc
Output = f4c633c3044fc145f84f335cb81953de

# No input?
# MAC = Poly1305
# Input =
# Key = a0f3080000f46400d0c7e9076c834403dd3fab2251f11ac759f0887129cc2ee7
# Output = dd3fab2251f11ac759f0887129cc2ee7

MAC = Poly1305
Input = 663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef
Output = 0ee1c16bb73f0f4fd19881753c01cdbe

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 5154ad0d2cb26e01274fc51148491f1b

# self-generated vectors exercise "significant" length such that* are handled by different code paths

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 812059a5da198637cac7c4a631bee466

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 5b88d7f6228b11e2e28579a5c0c1f761

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = bbb613b2b6d753ba07395b916aaece15

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = c794d7057d1778c4bbee0a39b3d97342

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = ffbcb9b371423152d7fca5ad042fbaa9

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee466
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 069ed6b8ef0f207b3e243bb1019fe632

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = cca339d9a45fa2368c2c68b3a4179133

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 53f6e828a2f0fe0ee815bf0bd5841a34

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = b846d44e9bbd53cedffbfbb6b7fa4933

# 4th power of the key spills to 131th bit in SIMD key setup

MAC = Poly1305
Input = ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
Key = ad628107e8351d0f2c231a05dc4a410600000000000000000000000000000000
Output = 07145a4c02fe5fa32036de68fabe9066

# poly1305_ieee754.c failed this in final stage

MAC = Poly1305
Input = 842364e156336c0998b933a6237726180d9e3fdcbde4cd5d17080fc3beb49614d7122c037463ff104d73f19c12704628d417c4c54a3fe30d3c3d7714382d43b0382a50a5dee54be844b076e8df88201a1cd43b90eb21643fa96f39b518aa8340c942ff3c31baf7c9bdbf0f31ae3fa096bf8c63030609829fe72e179824890bc8e08c315c1cce2a83144dbbff09f74e3efc770b54d0984a8f19b14719e63635641d6b1eedf63efbf080e1783d32445412114c20de0b837a0dfa33d6b82825fff44c9a70ea54ce47f07df698e6b03323b53079364a5fc3e9dd034392bdde86dccdda94321c5e44060489336cb65bf3989c36f7282c2f5d2b882c171e74
Key = 95d5c005503e510d8cd0aa072c4a4d066eabc52d11653df47fbf63ab198bcc26
Output = f248312e578d9d58f8b7bb4d19105431

# AVX2 in poly1305-x86.pl failed this with 176+32 split

MAC = Poly1305
Input = 248ac31085b6c2adaaa38259a0d7192c5c35d1bb4ef39ad94c38d1c82479e2dd2159a077024b0589bc8a20101b506f0a1ad0bbab76e83a83f1b94be6beae74e874cab692c5963a75436b776121ec9f62399a3e66b2d22707dae81933b6277f3c8516bcbe26dbbd86f373103d7cf4cad1888c952118fbfbd0d7b4bedc4ae4936aff91157e7aa47c54442ea78d6ac251d324a0fbe49d89cc3521b66d16e9c66a3709894e4eb0a4eedc4ae19468e66b81f271351b1d921ea551047abcc6b87a901fde7db79fa1818c11336dbc07244a40eb
Key = 000102030405060708090a0b0c0d0e0f00000000000000000000000000000000
Output = bc939bc5281480fa99c6d68c258ec42f

# test vectors from Google

# No input?
# MAC = Poly1305
# Input =
# Key = c8afaac331ee372cd6082de134943b174710130e9f6fea8d72293850a667d86c
# Output = 4710130e9f6fea8d72293850a667d86c

MAC = Poly1305
Input = 48656c6c6f20776f726c6421
Key = 746869732069732033322d62797465206b657920666f7220506f6c7931333035
Output = a6f745008f81c916a20dcc74eef2b2f0

MAC = Poly1305
Input = 0000000000000000000000000000000000000000000000000000000000000000
Key = 746869732069732033322d62797465206b657920666f7220506f6c7931333035
Output = 49ec78090e481ec6c26b33b91ccc0307

MAC = Poly1305
Input = 89dab80b7717c1db5db437860a3f70218e93e1b8f461fb677f16f35f6f87e2a91c99bc3a47ace47640cc95c345be5ecca5a3523c35cc01893af0b64a620334270372ec12482d1b1e363561698a578b359803495bb4e2ef1930b17a5190b580f141300df30adbeca28f6427a8bc1a999fd51c554a017d095d8c3e3127daf9f595
Key = 2d773be37adb1e4d683bf0075e79c4ee037918535a7f99ccb7040fb5f5f43aea
Output = c85d15ed44c378d6b00e23064c7bcd51

MAC = Poly1305
Input = 000000000000000b170303020000000006db1f1f368d696a810a349c0c714c9a5e7850c2407d721acded95e018d7a85266a6e1289cdb4aeb18da5ac8a2b0026d24a59ad485227f3eaedbb2e7e35e1c66cd60f9abf716dcc9ac42682dd7dab287a7024c4eefc321cc0574e16793e37cec03c5bda42b54c114a80b57af26416c7be742005e20855c73e21dc8e2edc9d435cb6f6059280011c270b71570051c1c9b3052126620bc1e2730fa066c7a509d53c60e5ae1b40aa6e39e49669228c90eecb4a50db32a50bc49e90b4f4b359a1dfd11749cd3867fcf2fb7bb6cd4738f6a4ad6f7ca5058f7618845af9f020f6c3b967b8f4cd4a91e2813b507ae66f2d35c18284f7292186062e10fd5510d18775351ef334e7634ab4743f5b68f49adcab384d3fd75f7390f4006ef2a295c8c7a076ad54546cd25d2107fbe1436c840924aaebe5b370893cd63d1325b8616fc4810886bc152c53221b6df373119393255ee72bcaa880174f1717f9184fa91646f17a24ac55d16bfddca9581a92eda479201f0edbf633600d6066d1ab36d5d2415d71351bbcd608a25108d25641992c1f26c531cf9f90203bc4cc19f5927d834b0a47116d3884bbb164b8ec883d1ac832e56b3918a98601a08d171881541d594db399c6ae6151221745aec814c45b0b05b565436fd6f137aa10a0c0b643761dbd6f9a9dcb99b1a6e690854ce0769cde39761d82fcdec15f0d92d7d8e94ade8eb83fbe0
Key = 99e5822dd4173c995e3dae0ddefb97743fde3b080134b39f76e9bf8d0e88d546
Output = 2637408fe13086ea73f971e3425e2820

# test vectors from Hanno Bock

MAC = Poly1305
Input = cccccccccccccccccccccccccccccccccccccccccccccccccc80ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccceccccccccccccccccccccccccccccccccccccc5cccccccccccccccccccccccccccccccccccccccccce3ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccaccccccccccccccccccccce6cccccccccc000000afccccccccccccccccccfffffff5000000000000000000000000000000000000000000000000000000ffffffe70000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000719205a8521dfc
Key = 7f1b02640000000000000000000000000000000000000000cccccccccccccccc
Output = 8559b876eceed66eb37798c0457baff9

MAC = Poly1305
Input = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa000000000000000000800264
Key = e00016000000000000000000000000000000aaaaaaaaaaaaaaaaaaaaaaaaaaaa
Output = 00bd1258978e205444c9aaaa82006fed

MAC = Poly1305
Input = 02fc
Key = 0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Output = 06120c0c0c0c0c0c0c0c0c0c0c0c0c0c

MAC = Poly1305
Input = 7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7a7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b5c7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b6e7b007b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7a7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b5c7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b6e7b001300000000b300000000000000000000000000000000000000000000f20000000000000000000000000000000000002000efff0009000000000000000000000000100000000009000000640000000000000000000000001300000000b300000000000000000000000000000000000000000000f20000000000000000000000000000000000002000efff00090000000000000000007a000010000000000900000064000000000000000000000000000000000000000000000000fc
Key = 00ff000000000000000000000000000000000000001e00000000000000007b7b
Output = 33205bbf9e9f8f7212ab9e2ab9b7e4a5

MAC = Poly1305
Input = 77777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777ffffffe9e9acacacacacacacacacacac0000acacec0100acacac2caca2acacacacacacacacacacac64f2
Key = 0000007f0000007f01000020000000000000cf77777777777777777777777777
Output = 02ee7c8c546ddeb1a467e4c3981158b9

# test vectors from Andrew Moon - nacl

MAC = Poly1305
Input = 8e993b9f48681273c29650ba32fc76ce48332ea7164d96a4476fb8c531a1186ac0dfc17c98dce87b4da7f011ec48c97271d2c20f9b928fe2270d6fb863d51738b48eeee314a7cc8ab932164548e526ae90224368517acfeabd6bb3732bc0e9da99832b61ca01b6de56244a9e88d5f9b37973f622a43d14a6599b1f654cb45a74e355a5
Key = eea6a7251c1e72916d11c2cb214d3c252539121d8e234e652d651fa4c8cff880
Output = f3ffc7703f9400e52a7dfb4b3d3305d9

# wrap 2^130-5
MAC = Poly1305
Input = ffffffffffffffffffffffffffffffff
Key = 0200000000000000000000000000000000000000000000000000000000000000
Output = 03000000000000000000000000000000

# wrap 2^128
MAC = Poly1305
Input = 02000000000000000000000000000000
Key = 02000000000000000000000000000000ffffffffffffffffffffffffffffffff
Output = 03000000000000000000000000000000

# limb carry
MAC = Poly1305
Input = fffffffffffffffffffffffffffffffff0ffffffffffffffffffffffffffffff11000000000000000000000000000000
Key = 0100000000000000000000000000000000000000000000000000000000000000
Output = 05000000000000000000000000000000

# 2^130-5
MAC = Poly1305
Input = fffffffffffffffffffffffffffffffffbfefefefefefefefefefefefefefefe01010101010101010101010101010101
Key = 0100000000000000000000000000000000000000000000000000000000000000
Output = 00000000000000000000000000000000

# 2^130-6
MAC = Poly1305
Input = fdffffffffffffffffffffffffffffff
Key = 0200000000000000000000000000000000000000000000000000000000000000
Output = faffffffffffffffffffffffffffffff

# 5*H+L reduction intermediate
MAC = Poly1305
Input = e33594d7505e43b900000000000000003394d7505e4379cd01000000000000000000000000000000000000000000000001000000000000000000000000000000
Key = 0100000000000000040000000000000000000000000000000000000000000000
Output = 14000000000000005500000000000000

# 5*H+L reduction final
MAC = Poly1305
Input = e33594d7505e43b900000000000000003394d7505e4379cd010000000000000000000000000000000000000000000000
Key = 0100000000000000040000000000000000000000000000000000000000000000
Output = 13000000000000000000000000000000

