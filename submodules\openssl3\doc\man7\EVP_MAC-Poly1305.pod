=pod

=head1 NAME

EVP_MAC-Poly1305 - The Poly1305 EVP_MAC implementation

=head1 DESCRIPTION

Support for computing Poly1305 MACs through the B<EVP_MAC> API.

=head2 Identity

This implementation is identified with this name and properties, to be
used with EVP_MAC_fetch():

=over 4

=item "POLY1305", "provider=default"

=back

=head2 Supported parameters

The general description of these parameters can be found in
L<EVP_MAC(3)/PARAMETERS>.

The following parameter can be set with EVP_MAC_CTX_set_params():

=over 4

=item "key" (B<OSSL_MAC_PARAM_KEY>) <octet string>

Sets the MAC key.
Setting this parameter is identical to passing a I<key> to L<EVP_MAC_init(3)>.

=back

The following parameters can be retrieved with
EVP_MAC_CTX_get_params():

=over 4

=item "size" (B<OSSL_MAC_PARAM_SIZE>) <unsigned integer>

Gets the MAC size.

=back

The "size" parameter can also be retrieved with with EVP_MAC_CTX_get_mac_size().
The length of the "size" parameter should not exceed that of an B<unsigned int>.

=head1 NOTES

The OpenSSL implementation of the Poly 1305 MAC corresponds to RFC 7539.

It is critical to never reuse the key.  The security implication noted in
RFC 8439 applies equally to the OpenSSL implementation.

=head1 SEE ALSO

L<EVP_MAC_CTX_get_params(3)>, L<EVP_MAC_CTX_set_params(3)>,
L<EVP_MAC(3)/PARAMETERS>, L<OSSL_PARAM(3)>

=head1 COPYRIGHT

Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
