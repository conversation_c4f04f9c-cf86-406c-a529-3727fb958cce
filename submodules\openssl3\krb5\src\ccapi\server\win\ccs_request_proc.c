/* ccapi/server/win/ccs_request_proc.c */
/*
 * Copyright 2008 Massachusetts Institute of Technology.
 * All Rights Reserved.
 *
 * Export of this software from the United States of America may
 * require a specific license from the United States Government.
 * It is the responsibility of any person or organization contemplating
 * export to obtain such a license before exporting.
 *
 * WITHIN THAT CONSTRAINT, permission to use, copy, modify, and
 * distribute this software and its documentation for any purpose and
 * without fee is hereby granted, provided that the above copyright
 * notice appear in all copies and that both that copyright notice and
 * this permission notice appear in supporting documentation, and that
 * the name of M.I.T. not be used in advertising or publicity pertaining
 * to distribution of the software without specific, written prior
 * permission.  Furthermore if you modify this software you must label
 * your software as modified software and not distribute it in such a
 * fashion that it might be confused with the original M.I.T. software.
 * M.I.T. makes no representations about the suitability of
 * this software for any purpose.  It is provided "as is" without express
 * or implied warranty.
 */

#include <stdlib.h>
#include <stdio.h>

#include "ccs_request.h"    // header file generated by MIDL compiler
#include "cci_debugging.h"
#include "WorkQueue.h"
#include "win-utils.h"
#include "ccs_win_pipe.h"

void ccs_rpc_request(
    const long  rpcmsg,             /* Message type */
    const char  tspHandle[],        /* Client's tspdata* */
    const char* pszUUID,            /* Where client will listen for the reply */
    const long  lenRequest,         /* Length of buffer */
    const char  pbRequest[],        /* Data buffer */
    const long  serverStartTime,    /* Which server session we're talking to */
    long*       return_status ) {   /* Return code */

    cc_int32        status  = 0;
    k5_ipc_stream   stream;
    UINT64*         p       = (UINT64*)(tspHandle);
    WIN_PIPE*       pipe    = NULL;

    status = (rpcmsg != CCMSG_REQUEST) && (rpcmsg != CCMSG_PING);

    if (!status) {
        status = krb5int_ipc_stream_new (&stream);  /* Create a stream for the request data */
        }

    if (!status) {                          /* Put the data into the stream */
        status = krb5int_ipc_stream_write (stream, pbRequest, lenRequest);
        }

    pipe = ccs_win_pipe_new(pszUUID, *p);
    worklist_add(rpcmsg, pipe, stream, serverStartTime);
    *return_status = status;
    }


void ccs_rpc_connect(
    const long  rpcmsg,             /* Message type */
    const char  tspHandle[],        /* Client's tspdata* */
    const char* pszUUID,            /* Data buffer */
    long*       return_status ) {   /* Return code */

    UINT64*     p       = (UINT64*)(tspHandle);
    WIN_PIPE*   pipe    = ccs_win_pipe_new(pszUUID, *p);

    worklist_add(   rpcmsg,
                    pipe,
                    NULL,               /* No payload with connect request */
                    (const time_t)0 );  /* No server session number with connect request */
    }


// 'Authentication' is client setting a value in a file and the server
//   returning that value plus one.
CC_UINT32 ccs_authenticate(const CC_CHAR* name) {
    HANDLE      hMap    = 0;
    PDWORD      pvalue  = 0;
    CC_UINT32   result  = 0;
    DWORD       status  = 0;

    hMap = OpenFileMapping(FILE_MAP_ALL_ACCESS, FALSE, (LPSTR)name);
    status  = !hMap;

    if (!status) {
        pvalue = (PDWORD)MapViewOfFile(hMap, FILE_MAP_WRITE, 0, 0, 0);
        status = !pvalue;
        }

    if (!status) {
        *pvalue += 1;
        result = *pvalue;
        }

    if (pvalue) {
        UnmapViewOfFile(pvalue);
        }

    if (hMap) CloseHandle(hMap);
    return result;
    }
