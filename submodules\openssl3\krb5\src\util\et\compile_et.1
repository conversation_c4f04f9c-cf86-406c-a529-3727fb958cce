.\" Copyright (c) 1988 Massachusetts Institute of Technology,
.\" Student Information Processing Board.  All rights reserved.
.\"
.\" util/et/compile_et.1
.\"
.TH COMPILE_ET 1 "22 Nov 1988" SIPB
.SH NAME
compile_et \- error table compiler
.SH SYNOPSIS
.B compile_et
[
.B \-\-textdomain
.I domain
[
.B \-\-localedir
.I dir
] ]
file
.SH DESCRIPTION
.B Compile_et
converts a table listing error-code names and associated messages into
a C source file suitable for use with the
.IR com_err (3)
library.

The source file name must end with a suffix of ``.et''; the file
consists of a declaration supplying the name (up to four characters
long) of the error-code table:

.B error_table
.I name

followed by up to 256 entries of the form:

.B error_code
.I name,
"
.I string
"

and a final

.B end

to indicate the end of the table.

The name of the table is used to construct the name of a subroutine
.I initialize_XXXX_error_table
which must be called in order for the
.I com_err
library to recognize the error table.

The various error codes defined are assigned sequentially increasing
numbers (starting with a large number computed as a hash function of
the name of the table); thus for compatibility it is suggested that
new codes be added only to the end of an existing table, and that no
codes be removed from tables.

The names defined in the table are placed into a C header file with
preprocessor directives defining them as integer constants of up to
32 bits in magnitude.

A C source file is also generated which should be compiled and linked
with the object files which reference these error codes; it contains
the text of the messages and the initialization subroutine.  Both C
files have names derived from that of the original source file, with
the ``.et'' suffix replaced by ``.c'' and ``.h''.

A ``#'' in the source file is treated as a comment character, and all
remaining text to the end of the source line will be ignored.

If a text domain is provided with the
.B \-\-textdomain
option, error messages will be looked up in the specified domain with
.BR gettext .
If a locale directory is also provided with the
.B \-\-localedir
option, the text domain will be bound to the specified locale
directory with
.B bindtextdomain
when the error table is initialized.

.SH BUGS

Since
.B compile_et
uses a very simple parser based on
.IR yacc (1),
its error recovery leaves much to be desired.

.\" .IR for manual entries
.\" .PP for paragraph breaks

.SH "SEE ALSO"
com_err (3).

Ken Raeburn, "A Common Error Description Library for UNIX".
