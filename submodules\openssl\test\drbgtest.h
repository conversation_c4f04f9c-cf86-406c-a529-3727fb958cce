/*
 * Copyright 2011-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/*
 * Known answer tests for SP800-90 DRBG CTR mode.
 */


/*
 * AES-128 use df PR
 */
static const unsigned char aes_128_use_df_pr_entropyinput[] = {
    0x61, 0x52, 0x7c, 0xe3, 0x23, 0x7d, 0x0a, 0x07, 0x10, 0x0c, 0x50, 0x33,
    0xc8, 0xdb, 0xff, 0x12
};
static const unsigned char aes_128_use_df_pr_nonce[] = {
    0x51, 0x0d, 0x85, 0x77, 0xed, 0x22, 0x97, 0x28
};
static const unsigned char aes_128_use_df_pr_personalizationstring[] = {
    0x59, 0x9f, 0xbb, 0xcd, 0xd5, 0x25, 0x69, 0xb5, 0xcb, 0xb5, 0x03, 0xfe,
    0xd7, 0xd7, 0x01, 0x67
};
static const unsigned char aes_128_use_df_pr_additionalinput[] = {
    0xef, 0x88, 0x76, 0x01, 0xaf, 0x3c, 0xfe, 0x8b, 0xaf, 0x26, 0x06, 0x9e,
    0x9a, 0x47, 0x08, 0x76
};
static const unsigned char aes_128_use_df_pr_entropyinputpr[] = {
    0xe2, 0x76, 0xf9, 0xf6, 0x3a, 0xba, 0x10, 0x9f, 0xbf, 0x47, 0x0e, 0x51,
    0x09, 0xfb, 0xa3, 0xb6
};
static const unsigned char aes_128_use_df_pr_int_returnedbits[] = {
    0xd4, 0x98, 0x8a, 0x46, 0x80, 0x4c, 0xdb, 0xa3, 0x59, 0x02, 0x57, 0x52,
    0x66, 0x1c, 0xea, 0x5b
};
static const unsigned char aes_128_use_df_pr_additionalinput2[] = {
    0x88, 0x8c, 0x91, 0xd6, 0xbe, 0x56, 0x6e, 0x08, 0x9a, 0x62, 0x2b, 0x11,
    0x3f, 0x5e, 0x31, 0x06
};
static const unsigned char aes_128_use_df_pr_entropyinputpr2[] = {
    0xc0, 0x5c, 0x6b, 0x98, 0x01, 0x0d, 0x58, 0x18, 0x51, 0x18, 0x96, 0xae,
    0xa7, 0xe3, 0xa8, 0x67
};
static const unsigned char aes_128_use_df_pr_returnedbits[] = {
    0xcf, 0x01, 0xac, 0x22, 0x31, 0x06, 0x8e, 0xfc, 0xce, 0x56, 0xea, 0x24,
    0x0f, 0x38, 0x43, 0xc6
};


/*
 * AES-128 use df no PR
 */
static const unsigned char aes_128_use_df_entropyinput[] = {
    0x1f, 0x8e, 0x34, 0x82, 0x0c, 0xb7, 0xbe, 0xc5, 0x01, 0x3e, 0xd0, 0xa3,
    0x9d, 0x7d, 0x1c, 0x9b
};
static const unsigned char aes_128_use_df_nonce[] = {
    0xd5, 0x4d, 0xbd, 0x4a, 0x93, 0x7f, 0xb8, 0x96,
};
static const unsigned char aes_128_use_df_personalizationstring[] = {
    0xab, 0xd6, 0x3f, 0x04, 0xfe, 0x27, 0x6b, 0x2d, 0xd7, 0xc3, 0x1c, 0xf3,
    0x38, 0x66, 0xba, 0x1b
};
static const unsigned char aes_128_use_df_additionalinput[] = {
    0xfe, 0xf4, 0x09, 0xa8, 0xb7, 0x73, 0x27, 0x9c, 0x5f, 0xa7, 0xea, 0x46,
    0xb5, 0xe2, 0xb2, 0x41
};
static const unsigned char aes_128_use_df_int_returnedbits[] = {
    0x42, 0xe4, 0x4e, 0x7b, 0x27, 0xdd, 0xcb, 0xbc, 0x0a, 0xcf, 0xa6, 0x67,
    0xe7, 0x57, 0x11, 0xb4
};
static const unsigned char aes_128_use_df_entropyinputreseed[] = {
    0x14, 0x26, 0x69, 0xd9, 0xf3, 0x65, 0x03, 0xd6, 0x6b, 0xb9, 0x44, 0x0b,
    0xc7, 0xc4, 0x9e, 0x39
};
static const unsigned char aes_128_use_df_additionalinputreseed[] = {
    0x55, 0x2e, 0x60, 0x9a, 0x05, 0x72, 0x8a, 0xa8, 0xef, 0x22, 0x81, 0x5a,
    0xc8, 0x93, 0xfa, 0x84
};
static const unsigned char aes_128_use_df_additionalinput2[] = {
    0x3c, 0x40, 0xc8, 0xc4, 0x16, 0x0c, 0x21, 0xa4, 0x37, 0x2c, 0x8f, 0xa5,
    0x06, 0x0c, 0x15, 0x2c
};
static const unsigned char aes_128_use_df_returnedbits[] = {
    0xe1, 0x3e, 0x99, 0x98, 0x86, 0x67, 0x0b, 0x63, 0x7b, 0xbe, 0x3f, 0x88,
    0x46, 0x81, 0xc7, 0x19
};


/*
 * AES-192 use df PR
 */
static const unsigned char aes_192_use_df_pr_entropyinput[] = {
    0x2b, 0x4e, 0x8b, 0xe1, 0xf1, 0x34, 0x80, 0x56, 0x81, 0xf9, 0x74, 0xec,
    0x17, 0x44, 0x2a, 0xf1, 0x14, 0xb0, 0xbf, 0x97, 0x39, 0xb7, 0x04, 0x7d
};
static const unsigned char aes_192_use_df_pr_nonce[] = {
    0xd6, 0x9d, 0xeb, 0x14, 0x4e, 0x6c, 0x30, 0x1e, 0x39, 0x55, 0x73, 0xd0,
    0xd1, 0x80, 0x78, 0xfa
};
static const unsigned char aes_192_use_df_pr_personalizationstring[] = {
    0xfc, 0x43, 0x4a, 0xf8, 0x9a, 0x55, 0xb3, 0x53, 0x83, 0xe2, 0x18, 0x16,
    0x0c, 0xdc, 0xcd, 0x5e, 0x4f, 0xa0, 0x03, 0x01, 0x2b, 0x9f, 0xe4, 0xd5,
    0x7d, 0x49, 0xf0, 0x41, 0x9e, 0x3d, 0x99, 0x04
};
static const unsigned char aes_192_use_df_pr_additionalinput[] = {
    0x5e, 0x9f, 0x49, 0x6f, 0x21, 0x8b, 0x1d, 0x32, 0xd5, 0x84, 0x5c, 0xac,
    0xaf, 0xdf, 0xe4, 0x79, 0x9e, 0xaf, 0xa9, 0x82, 0xd0, 0xf8, 0x4f, 0xcb,
    0x69, 0x10, 0x0a, 0x7e, 0x81, 0x57, 0xb5, 0x36
};
static const unsigned char aes_192_use_df_pr_entropyinputpr[] = {
    0xd4, 0x81, 0x0c, 0xd7, 0x66, 0x39, 0xec, 0x42, 0x53, 0x87, 0x41, 0xa5,
    0x1e, 0x7d, 0x80, 0x91, 0x8e, 0xbb, 0xed, 0xac, 0x14, 0x02, 0x1a, 0xd5,
};
static const unsigned char aes_192_use_df_pr_int_returnedbits[] = {
    0xdf, 0x1d, 0x39, 0x45, 0x7c, 0x9b, 0xc6, 0x2b, 0x7d, 0x8c, 0x93, 0xe9,
    0x19, 0x30, 0x6b, 0x67
};
static const unsigned char aes_192_use_df_pr_additionalinput2[] = {
    0x00, 0x71, 0x27, 0x4e, 0xd3, 0x14, 0xf1, 0x20, 0x7f, 0x4a, 0x41, 0x32,
    0x2a, 0x97, 0x11, 0x43, 0x8f, 0x4a, 0x15, 0x7b, 0x9b, 0x51, 0x79, 0xda,
    0x49, 0x3d, 0xde, 0xe8, 0xbc, 0x93, 0x91, 0x99
};
static const unsigned char aes_192_use_df_pr_entropyinputpr2[] = {
    0x90, 0xee, 0x76, 0xa1, 0x45, 0x8d, 0xb7, 0x40, 0xb0, 0x11, 0xbf, 0xd0,
    0x65, 0xd7, 0x3c, 0x7c, 0x4f, 0x20, 0x3f, 0x4e, 0x11, 0x9d, 0xb3, 0x5e,
};
static const unsigned char aes_192_use_df_pr_returnedbits[] = {
    0x24, 0x3b, 0x20, 0xa4, 0x37, 0x66, 0xba, 0x72, 0x39, 0x3f, 0xcf, 0x3c,
    0x7e, 0x1a, 0x2b, 0x83
};


/*
 * AES-192 use df no PR
 */
static const unsigned char aes_192_use_df_entropyinput[] = {
    0x8d, 0x74, 0xa4, 0x50, 0x1a, 0x02, 0x68, 0x0c, 0x2a, 0x69, 0xc4, 0x82,
    0x3b, 0xbb, 0xda, 0x0e, 0x7f, 0x77, 0xa3, 0x17, 0x78, 0x57, 0xb2, 0x7b,
};
static const unsigned char aes_192_use_df_nonce[] = {
    0x75, 0xd5, 0x1f, 0xac, 0xa4, 0x8d, 0x42, 0x78, 0xd7, 0x69, 0x86, 0x9d,
    0x77, 0xd7, 0x41, 0x0e
};
static const unsigned char aes_192_use_df_personalizationstring[] = {
    0x4e, 0x33, 0x41, 0x3c, 0x9c, 0xc2, 0xd2, 0x53, 0xaf, 0x90, 0xea, 0xcf,
    0x19, 0x50, 0x1e, 0xe6, 0x6f, 0x63, 0xc8, 0x32, 0x22, 0xdc, 0x07, 0x65,
    0x9c, 0xd3, 0xf8, 0x30, 0x9e, 0xed, 0x35, 0x70
};
static const unsigned char aes_192_use_df_additionalinput[] = {
    0x5d, 0x8b, 0x8c, 0xc1, 0xdf, 0x0e, 0x02, 0x78, 0xfb, 0x19, 0xb8, 0x69,
    0x78, 0x4e, 0x9c, 0x52, 0xbc, 0xc7, 0x20, 0xc9, 0xe6, 0x5e, 0x77, 0x22,
    0x28, 0x3d, 0x0c, 0x9e, 0x68, 0xa8, 0x45, 0xd7
};
static const unsigned char aes_192_use_df_int_returnedbits[] = {
    0xd5, 0xe7, 0x08, 0xc5, 0x19, 0x99, 0xd5, 0x31, 0x03, 0x0a, 0x74, 0xb6,
    0xb7, 0xed, 0xe9, 0xea
};
static const unsigned char aes_192_use_df_entropyinputreseed[] = {
    0x9c, 0x26, 0xda, 0xf1, 0xac, 0xd9, 0x5a, 0xd6, 0xa8, 0x65, 0xf5, 0x02,
    0x8f, 0xdc, 0xa2, 0x09, 0x54, 0xa6, 0xe2, 0xa4, 0xde, 0x32, 0xe0, 0x01,
};
static const unsigned char aes_192_use_df_additionalinputreseed[] = {
    0x9b, 0x90, 0xb0, 0x3a, 0x0e, 0x3a, 0x80, 0x07, 0x4a, 0xf4, 0xda, 0x76,
    0x28, 0x30, 0x3c, 0xee, 0x54, 0x1b, 0x94, 0x59, 0x51, 0x43, 0x56, 0x77,
    0xaf, 0x88, 0xdd, 0x63, 0x89, 0x47, 0x06, 0x65
};
static const unsigned char aes_192_use_df_additionalinput2[] = {
    0x3c, 0x11, 0x64, 0x7a, 0x96, 0xf5, 0xd8, 0xb8, 0xae, 0xd6, 0x70, 0x4e,
    0x16, 0x96, 0xde, 0xe9, 0x62, 0xbc, 0xee, 0x28, 0x2f, 0x26, 0xa6, 0xf0,
    0x56, 0xef, 0xa3, 0xf1, 0x6b, 0xa1, 0xb1, 0x77
};
static const unsigned char aes_192_use_df_returnedbits[] = {
    0x0b, 0xe2, 0x56, 0x03, 0x1e, 0xdb, 0x2c, 0x6d, 0x7f, 0x1b, 0x15, 0x58,
    0x1a, 0xf9, 0x13, 0x28
};


/*
 * AES-256 use df PR
 */
static const unsigned char aes_256_use_df_pr_entropyinput[] = {
    0x61, 0x68, 0xfc, 0x1a, 0xf0, 0xb5, 0x95, 0x6b, 0x85, 0x09, 0x9b, 0x74,
    0x3f, 0x13, 0x78, 0x49, 0x3b, 0x85, 0xec, 0x93, 0x13, 0x3b, 0xa9, 0x4f,
    0x96, 0xab, 0x2c, 0xe4, 0xc8, 0x8f, 0xdd, 0x6a
};
static const unsigned char aes_256_use_df_pr_nonce[] = {
    0xad, 0xd2, 0xbb, 0xba, 0xb7, 0x65, 0x89, 0xc3, 0x21, 0x6c, 0x55, 0x33,
    0x2b, 0x36, 0xff, 0xa4
};
static const unsigned char aes_256_use_df_pr_personalizationstring[] = {
    0x6e, 0xca, 0xe7, 0x20, 0x72, 0xd3, 0x84, 0x5a, 0x32, 0xd3, 0x4b, 0x24,
    0x72, 0xc4, 0x63, 0x2b, 0x9d, 0x12, 0x24, 0x0c, 0x23, 0x26, 0x8e, 0x83,
    0x16, 0x37, 0x0b, 0xd1, 0x06, 0x4f, 0x68, 0x6d
};
static const unsigned char aes_256_use_df_pr_additionalinput[] = {
    0x7e, 0x08, 0x4a, 0xbb, 0xe3, 0x21, 0x7c, 0xc9, 0x23, 0xd2, 0xf8, 0xb0,
    0x73, 0x98, 0xba, 0x84, 0x74, 0x23, 0xab, 0x06, 0x8a, 0xe2, 0x22, 0xd3,
    0x7b, 0xce, 0x9b, 0xd2, 0x4a, 0x76, 0xb8, 0xde
};
static const unsigned char aes_256_use_df_pr_entropyinputpr[] = {
    0x0b, 0x23, 0xaf, 0xdf, 0xf1, 0x62, 0xd7, 0xd3, 0x43, 0x97, 0xf8, 0x77,
    0x04, 0xa8, 0x42, 0x20, 0xbd, 0xf6, 0x0f, 0xc1, 0x17, 0x2f, 0x9f, 0x54,
    0xbb, 0x56, 0x17, 0x86, 0x68, 0x0e, 0xba, 0xa9
};
static const unsigned char aes_256_use_df_pr_int_returnedbits[] = {
    0x31, 0x8e, 0xad, 0xaf, 0x40, 0xeb, 0x6b, 0x74, 0x31, 0x46, 0x80, 0xc7,
    0x17, 0xab, 0x3c, 0x7a
};
static const unsigned char aes_256_use_df_pr_additionalinput2[] = {
    0x94, 0x6b, 0xc9, 0x9f, 0xab, 0x8d, 0xc5, 0xec, 0x71, 0x88, 0x1d, 0x00,
    0x8c, 0x89, 0x68, 0xe4, 0xc8, 0x07, 0x77, 0x36, 0x17, 0x6d, 0x79, 0x78,
    0xc7, 0x06, 0x4e, 0x99, 0x04, 0x28, 0x29, 0xc3
};
static const unsigned char aes_256_use_df_pr_entropyinputpr2[] = {
    0xbf, 0x6c, 0x59, 0x2a, 0x0d, 0x44, 0x0f, 0xae, 0x9a, 0x5e, 0x03, 0x73,
    0xd8, 0xa6, 0xe1, 0xcf, 0x25, 0x61, 0x38, 0x24, 0x86, 0x9e, 0x53, 0xe8,
    0xa4, 0xdf, 0x56, 0xf4, 0x06, 0x07, 0x9c, 0x0f
};
static const unsigned char aes_256_use_df_pr_returnedbits[] = {
    0x22, 0x4a, 0xb4, 0xb8, 0xb6, 0xee, 0x7d, 0xb1, 0x9e, 0xc9, 0xf9, 0xa0,
    0xd9, 0xe2, 0x97, 0x00
};


/*
 * AES-256 use df no PR
 */
static const unsigned char aes_256_use_df_entropyinput[] = {
    0xa5, 0x3e, 0x37, 0x10, 0x17, 0x43, 0x91, 0x93, 0x59, 0x1e, 0x47, 0x50,
    0x87, 0xaa, 0xdd, 0xd5, 0xc1, 0xc3, 0x86, 0xcd, 0xca, 0x0d, 0xdb, 0x68,
    0xe0, 0x02, 0xd8, 0x0f, 0xdc, 0x40, 0x1a, 0x47
};
static const unsigned char aes_256_use_df_nonce[] = {
    0xa9, 0x4d, 0xa5, 0x5a, 0xfd, 0xc5, 0x0c, 0xe5, 0x1c, 0x9a, 0x3b, 0x8a,
    0x4c, 0x44, 0x84, 0x40
};
static const unsigned char aes_256_use_df_personalizationstring[] = {
    0x8b, 0x52, 0xa2, 0x4a, 0x93, 0xc3, 0x4e, 0xa7, 0x1e, 0x1c, 0xa7, 0x05,
    0xeb, 0x82, 0x9b, 0xa6, 0x5d, 0xe4, 0xd4, 0xe0, 0x7f, 0xa3, 0xd8, 0x6b,
    0x37, 0x84, 0x5f, 0xf1, 0xc7, 0xd5, 0xf6, 0xd2
};
static const unsigned char aes_256_use_df_additionalinput[] = {
    0x20, 0xf4, 0x22, 0xed, 0xf8, 0x5c, 0xa1, 0x6a, 0x01, 0xcf, 0xbe, 0x5f,
    0x8d, 0x6c, 0x94, 0x7f, 0xae, 0x12, 0xa8, 0x57, 0xdb, 0x2a, 0xa9, 0xbf,
    0xc7, 0xb3, 0x65, 0x81, 0x80, 0x8d, 0x0d, 0x46
};
static const unsigned char aes_256_use_df_int_returnedbits[] = {
    0x4e, 0x44, 0xfd, 0xf3, 0x9e, 0x29, 0xa2, 0xb8, 0x0f, 0x5d, 0x6c, 0xe1,
    0x28, 0x0c, 0x3b, 0xc1
};
static const unsigned char aes_256_use_df_entropyinputreseed[] = {
    0xdd, 0x40, 0xe5, 0x98, 0x7b, 0x27, 0x16, 0x73, 0x15, 0x68, 0xd2, 0x76,
    0xbf, 0x0c, 0x67, 0x15, 0x75, 0x79, 0x03, 0xd3, 0xde, 0xde, 0x91, 0x46,
    0x42, 0xdd, 0xd4, 0x67, 0xc8, 0x79, 0xc8, 0x1e
};
static const unsigned char aes_256_use_df_additionalinputreseed[] = {
    0x7f, 0xd8, 0x1f, 0xbd, 0x2a, 0xb5, 0x1c, 0x11, 0x5d, 0x83, 0x4e, 0x99,
    0xf6, 0x5c, 0xa5, 0x40, 0x20, 0xed, 0x38, 0x8e, 0xd5, 0x9e, 0xe0, 0x75,
    0x93, 0xfe, 0x12, 0x5e, 0x5d, 0x73, 0xfb, 0x75
};
static const unsigned char aes_256_use_df_additionalinput2[] = {
    0xcd, 0x2c, 0xff, 0x14, 0x69, 0x3e, 0x4c, 0x9e, 0xfd, 0xfe, 0x26, 0x0d,
    0xe9, 0x86, 0x00, 0x49, 0x30, 0xba, 0xb1, 0xc6, 0x50, 0x57, 0x77, 0x2a,
    0x62, 0x39, 0x2c, 0x3b, 0x74, 0xeb, 0xc9, 0x0d
};
static const unsigned char aes_256_use_df_returnedbits[] = {
    0x4f, 0x78, 0xbe, 0xb9, 0x4d, 0x97, 0x8c, 0xe9, 0xd0, 0x97, 0xfe, 0xad,
    0xfa, 0xfd, 0x35, 0x5e
};


/*
 * AES-128 no df PR
 */
static const unsigned char aes_128_no_df_pr_entropyinput[] = {
    0x9a, 0x25, 0x65, 0x10, 0x67, 0xd5, 0xb6, 0x6b, 0x70, 0xa1, 0xb3, 0xa4,
    0x43, 0x95, 0x80, 0xc0, 0x84, 0x0a, 0x79, 0xb0, 0x88, 0x74, 0xf2, 0xbf,
    0x31, 0x6c, 0x33, 0x38, 0x0b, 0x00, 0xb2, 0x5a
};
static const unsigned char aes_128_no_df_pr_nonce[] = {
    0x78, 0x47, 0x6b, 0xf7, 0x90, 0x8e, 0x87, 0xf1,
};
static const unsigned char aes_128_no_df_pr_personalizationstring[] = {
    0xf7, 0x22, 0x1d, 0x3a, 0xbe, 0x1d, 0xca, 0x32, 0x1b, 0xbd, 0x87, 0x0c,
    0x51, 0x24, 0x19, 0xee, 0xa3, 0x23, 0x09, 0x63, 0x33, 0x3d, 0xa8, 0x0c,
    0x1c, 0xfa, 0x42, 0x89, 0xcc, 0x6f, 0xa0, 0xa8
};
static const unsigned char aes_128_no_df_pr_additionalinput[] = {
    0xc9, 0xe0, 0x80, 0xbf, 0x8c, 0x45, 0x58, 0x39, 0xff, 0x00, 0xab, 0x02,
    0x4c, 0x3e, 0x3a, 0x95, 0x9b, 0x80, 0xa8, 0x21, 0x2a, 0xee, 0xba, 0x73,
    0xb1, 0xd9, 0xcf, 0x28, 0xf6, 0x8f, 0x9b, 0x12
};
static const unsigned char aes_128_no_df_pr_entropyinputpr[] = {
    0x4c, 0xa8, 0xc5, 0xf0, 0x59, 0x9e, 0xa6, 0x8d, 0x26, 0x53, 0xd7, 0x8a,
    0xa9, 0xd8, 0xf7, 0xed, 0xb2, 0xf9, 0x12, 0x42, 0xe1, 0xe5, 0xbd, 0xe7,
    0xe7, 0x1d, 0x74, 0x99, 0x00, 0x9d, 0x31, 0x3e
};
static const unsigned char aes_128_no_df_pr_int_returnedbits[] = {
    0xe2, 0xac, 0x20, 0xf0, 0x80, 0xe7, 0xbc, 0x7e, 0x9c, 0x7b, 0x65, 0x71,
    0xaf, 0x19, 0x32, 0x16
};
static const unsigned char aes_128_no_df_pr_additionalinput2[] = {
    0x32, 0x7f, 0x38, 0x8b, 0x73, 0x0a, 0x78, 0x83, 0xdc, 0x30, 0xbe, 0x9f,
    0x10, 0x1f, 0xf5, 0x1f, 0xca, 0x00, 0xb5, 0x0d, 0xd6, 0x9d, 0x60, 0x83,
    0x51, 0x54, 0x7d, 0x38, 0x23, 0x3a, 0x52, 0x50
};
static const unsigned char aes_128_no_df_pr_entropyinputpr2[] = {
    0x18, 0x61, 0x53, 0x56, 0xed, 0xed, 0xd7, 0x20, 0xfb, 0x71, 0x04, 0x7a,
    0xb2, 0xac, 0xc1, 0x28, 0xcd, 0xf2, 0xc2, 0xfc, 0xaa, 0xb1, 0x06, 0x07,
    0xe9, 0x46, 0x95, 0x02, 0x48, 0x01, 0x78, 0xf9
};
static const unsigned char aes_128_no_df_pr_returnedbits[] = {
    0x29, 0xc8, 0x1b, 0x15, 0xb1, 0xd1, 0xc2, 0xf6, 0x71, 0x86, 0x68, 0x33,
    0x57, 0x82, 0x33, 0xaf
};


/*
 * AES-128 no df no PR
 */
static const unsigned char aes_128_no_df_entropyinput[] = {
    0xc9, 0xc5, 0x79, 0xbc, 0xe8, 0xc5, 0x19, 0xd8, 0xbc, 0x66, 0x73, 0x67,
    0xf6, 0xd3, 0x72, 0xaa, 0xa6, 0x16, 0xb8, 0x50, 0xb7, 0x47, 0x3a, 0x42,
    0xab, 0xf4, 0x16, 0xb2, 0x96, 0xd2, 0xb6, 0x60
};
static const unsigned char aes_128_no_df_nonce[] = {
    0x5f, 0xbf, 0x97, 0x0c, 0x4b, 0xa4, 0x87, 0x13,
};
static const unsigned char aes_128_no_df_personalizationstring[] = {
    0xce, 0xfb, 0x7b, 0x3f, 0xd4, 0x6b, 0x29, 0x0d, 0x69, 0x06, 0xff, 0xbb,
    0xf2, 0xe5, 0xc6, 0x6c, 0x0a, 0x10, 0xa0, 0xcf, 0x1a, 0x48, 0xc7, 0x8b,
    0x3c, 0x16, 0x88, 0xed, 0x50, 0x13, 0x81, 0xce
};
static const unsigned char aes_128_no_df_additionalinput[] = {
    0x4b, 0x22, 0x46, 0x18, 0x02, 0x7b, 0xd2, 0x1b, 0x22, 0x42, 0x7c, 0x37,
    0xd9, 0xf6, 0xe8, 0x9b, 0x12, 0x30, 0x5f, 0xe9, 0x90, 0xe8, 0x08, 0x24,
    0x4f, 0x06, 0x66, 0xdb, 0x19, 0x2b, 0x13, 0x95
};
static const unsigned char aes_128_no_df_int_returnedbits[] = {
    0x2e, 0x96, 0x70, 0x64, 0xfa, 0xdf, 0xdf, 0x57, 0xb5, 0x82, 0xee, 0xd6,
    0xed, 0x3e, 0x65, 0xc2
};
static const unsigned char aes_128_no_df_entropyinputreseed[] = {
    0x26, 0xc0, 0x72, 0x16, 0x3a, 0x4b, 0xb7, 0x99, 0xd4, 0x07, 0xaf, 0x66,
    0x62, 0x36, 0x96, 0xa4, 0x51, 0x17, 0xfa, 0x07, 0x8b, 0x17, 0x5e, 0xa1,
    0x2f, 0x3c, 0x10, 0xe7, 0x90, 0xd0, 0x46, 0x00
};
static const unsigned char aes_128_no_df_additionalinputreseed[] = {
    0x83, 0x39, 0x37, 0x7b, 0x02, 0x06, 0xd2, 0x12, 0x13, 0x8d, 0x8b, 0xf2,
    0xf0, 0xf6, 0x26, 0xeb, 0xa4, 0x22, 0x7b, 0xc2, 0xe7, 0xba, 0x79, 0xe4,
    0x3b, 0x77, 0x5d, 0x4d, 0x47, 0xb2, 0x2d, 0xb4
};
static const unsigned char aes_128_no_df_additionalinput2[] = {
    0x0b, 0xb9, 0x67, 0x37, 0xdb, 0x83, 0xdf, 0xca, 0x81, 0x8b, 0xf9, 0x3f,
    0xf1, 0x11, 0x1b, 0x2f, 0xf0, 0x61, 0xa6, 0xdf, 0xba, 0xa3, 0xb1, 0xac,
    0xd3, 0xe6, 0x09, 0xb8, 0x2c, 0x6a, 0x67, 0xd6
};
static const unsigned char aes_128_no_df_returnedbits[] = {
    0x1e, 0xa7, 0xa4, 0xe4, 0xe1, 0xa6, 0x7c, 0x69, 0x9a, 0x44, 0x6c, 0x36,
    0x81, 0x37, 0x19, 0xd4
};


/*
 * AES-192 no df PR
 */
static const unsigned char aes_192_no_df_pr_entropyinput[] = {
    0x9d, 0x2c, 0xd2, 0x55, 0x66, 0xea, 0xe0, 0xbe, 0x18, 0xb7, 0x76, 0xe7,
    0x73, 0x35, 0xd8, 0x1f, 0xad, 0x3a, 0xe3, 0x81, 0x0e, 0x92, 0xd0, 0x61,
    0xc9, 0x12, 0x26, 0xf6, 0x1c, 0xdf, 0xfe, 0x47, 0xaa, 0xfe, 0x7d, 0x5a,
    0x17, 0x1f, 0x8d, 0x9a
};
static const unsigned char aes_192_no_df_pr_nonce[] = {
    0x44, 0x82, 0xed, 0xe8, 0x4c, 0x28, 0x5a, 0x14, 0xff, 0x88, 0x8d, 0x19,
    0x61, 0x5c, 0xee, 0x0f
};
static const unsigned char aes_192_no_df_pr_personalizationstring[] = {
    0x47, 0xd7, 0x9b, 0x99, 0xaa, 0xcb, 0xe7, 0xd2, 0x57, 0x66, 0x2c, 0xe1,
    0x78, 0xd6, 0x2c, 0xea, 0xa3, 0x23, 0x5f, 0x2a, 0xc1, 0x3a, 0xf0, 0xa4,
    0x20, 0x3b, 0xfa, 0x07, 0xd5, 0x05, 0x02, 0xe4, 0x57, 0x01, 0xb6, 0x10,
    0x57, 0x2e, 0xe7, 0x55
};
static const unsigned char aes_192_no_df_pr_additionalinput[] = {
    0x4b, 0x74, 0x0b, 0x40, 0xce, 0x6b, 0xc2, 0x6a, 0x24, 0xb4, 0xf3, 0xad,
    0x7a, 0xa5, 0x7a, 0xa2, 0x15, 0xe2, 0xc8, 0x61, 0x15, 0xc6, 0xb7, 0x85,
    0x69, 0x11, 0xad, 0x7b, 0x14, 0xd2, 0xf6, 0x12, 0xa1, 0x95, 0x5d, 0x3f,
    0xe2, 0xd0, 0x0c, 0x2f
};
static const unsigned char aes_192_no_df_pr_entropyinputpr[] = {
    0x0c, 0x9c, 0xad, 0x05, 0xee, 0xae, 0x48, 0x23, 0x89, 0x59, 0xa1, 0x94,
    0xd7, 0xd8, 0x75, 0xd5, 0x54, 0x93, 0xc7, 0x4a, 0xd9, 0x26, 0xde, 0xeb,
    0xba, 0xb0, 0x7e, 0x30, 0x1d, 0x5f, 0x69, 0x40, 0x9c, 0x3b, 0x17, 0x58,
    0x1d, 0x30, 0xb3, 0x78
};
static const unsigned char aes_192_no_df_pr_int_returnedbits[] = {
    0xf7, 0x93, 0xb0, 0x6d, 0x77, 0x83, 0xd5, 0x38, 0x01, 0xe1, 0x52, 0x40,
    0x7e, 0x3e, 0x0c, 0x26
};
static const unsigned char aes_192_no_df_pr_additionalinput2[] = {
    0xbc, 0x4b, 0x37, 0x44, 0x1c, 0xc5, 0x45, 0x5f, 0x8f, 0x51, 0x62, 0x8a,
    0x85, 0x30, 0x1d, 0x7c, 0xe4, 0xcf, 0xf7, 0x44, 0xce, 0x32, 0x3e, 0x57,
    0x95, 0xa4, 0x2a, 0xdf, 0xfd, 0x9e, 0x38, 0x41, 0xb3, 0xf6, 0xc5, 0xee,
    0x0c, 0x4b, 0xee, 0x6e
};
static const unsigned char aes_192_no_df_pr_entropyinputpr2[] = {
    0xec, 0xaf, 0xf6, 0x4f, 0xb1, 0xa0, 0x54, 0xb5, 0x5b, 0xe3, 0x46, 0xb0,
    0x76, 0x5a, 0x7c, 0x3f, 0x7b, 0x94, 0x69, 0x21, 0x51, 0x02, 0xe5, 0x9f,
    0x04, 0x59, 0x02, 0x98, 0xc6, 0x43, 0x2c, 0xcc, 0x26, 0x4c, 0x87, 0x6b,
    0x8e, 0x0a, 0x83, 0xdf
};
static const unsigned char aes_192_no_df_pr_returnedbits[] = {
    0x74, 0x45, 0xfb, 0x53, 0x84, 0x96, 0xbe, 0xff, 0x15, 0xcc, 0x41, 0x91,
    0xb9, 0xa1, 0x21, 0x68
};


/*
 * AES-192 no df no PR
 */
static const unsigned char aes_192_no_df_entropyinput[] = {
    0x3c, 0x7d, 0xb5, 0xe0, 0x54, 0xd9, 0x6e, 0x8c, 0xa9, 0x86, 0xce, 0x4e,
    0x6b, 0xaf, 0xeb, 0x2f, 0xe7, 0x75, 0xe0, 0x8b, 0xa4, 0x3b, 0x07, 0xfe,
    0xbe, 0x33, 0x75, 0x93, 0x80, 0x27, 0xb5, 0x29, 0x47, 0x8b, 0xc7, 0x28,
    0x94, 0xc3, 0x59, 0x63
};
static const unsigned char aes_192_no_df_nonce[] = {
    0x43, 0xf1, 0x7d, 0xb8, 0xc3, 0xfe, 0xd0, 0x23, 0x6b, 0xb4, 0x92, 0xdb,
    0x29, 0xfd, 0x45, 0x71
};
static const unsigned char aes_192_no_df_personalizationstring[] = {
    0x9f, 0x24, 0x29, 0x99, 0x9e, 0x01, 0xab, 0xe9, 0x19, 0xd8, 0x23, 0x08,
    0xb7, 0xd6, 0x7e, 0x8c, 0xc0, 0x9e, 0x7f, 0x6e, 0x5b, 0x33, 0x20, 0x96,
    0x0b, 0x23, 0x2c, 0xa5, 0x6a, 0xf8, 0x1b, 0x04, 0x26, 0xdb, 0x2e, 0x2b,
    0x3b, 0x88, 0xce, 0x35
};
static const unsigned char aes_192_no_df_additionalinput[] = {
    0x94, 0xe9, 0x7c, 0x3d, 0xa7, 0xdb, 0x60, 0x83, 0x1f, 0x98, 0x3f, 0x0b,
    0x88, 0x59, 0x57, 0x51, 0x88, 0x9f, 0x76, 0x49, 0x9f, 0xa6, 0xda, 0x71,
    0x1d, 0x0d, 0x47, 0x16, 0x63, 0xc5, 0x68, 0xe4, 0x5d, 0x39, 0x69, 0xb3,
    0x3e, 0xbe, 0xd4, 0x8e
};
static const unsigned char aes_192_no_df_int_returnedbits[] = {
    0xf9, 0xd7, 0xad, 0x69, 0xab, 0x8f, 0x23, 0x56, 0x70, 0x17, 0x4f, 0x2a,
    0x45, 0xe7, 0x4a, 0xc5
};
static const unsigned char aes_192_no_df_entropyinputreseed[] = {
    0xa6, 0x71, 0x6a, 0x3d, 0xba, 0xd1, 0xe8, 0x66, 0xa6, 0xef, 0xb2, 0x0e,
    0xa8, 0x9c, 0xaa, 0x4e, 0xaf, 0x17, 0x89, 0x50, 0x00, 0xda, 0xa1, 0xb1,
    0x0b, 0xa4, 0xd9, 0x35, 0x89, 0xc8, 0xe5, 0xb0, 0xd9, 0xb7, 0xc4, 0x33,
    0x9b, 0xcb, 0x7e, 0x75
};
static const unsigned char aes_192_no_df_additionalinputreseed[] = {
    0x27, 0x21, 0xfc, 0xc2, 0xbd, 0xf3, 0x3c, 0xce, 0xc3, 0xca, 0xc1, 0x01,
    0xe0, 0xff, 0x93, 0x12, 0x7d, 0x54, 0x42, 0xe3, 0x9f, 0x03, 0xdf, 0x27,
    0x04, 0x07, 0x3c, 0x53, 0x7f, 0xa8, 0x66, 0xc8, 0x97, 0x4b, 0x61, 0x40,
    0x5d, 0x7a, 0x25, 0x79
};
static const unsigned char aes_192_no_df_additionalinput2[] = {
    0x2d, 0x8e, 0x16, 0x5d, 0x0b, 0x9f, 0xeb, 0xaa, 0xd6, 0xec, 0x28, 0x71,
    0x7c, 0x0b, 0xc1, 0x1d, 0xd4, 0x44, 0x19, 0x47, 0xfd, 0x1d, 0x7c, 0xe5,
    0xf3, 0x27, 0xe1, 0xb6, 0x72, 0x0a, 0xe0, 0xec, 0x0e, 0xcd, 0xef, 0x1a,
    0x91, 0x6a, 0xe3, 0x5f
};
static const unsigned char aes_192_no_df_returnedbits[] = {
    0xe5, 0xda, 0xb8, 0xe0, 0x63, 0x59, 0x5a, 0xcc, 0x3d, 0xdc, 0x9f, 0xe8,
    0x66, 0x67, 0x2c, 0x92
};


/*
 * AES-256 no df PR
 */
static const unsigned char aes_256_no_df_pr_entropyinput[] = {
    0x15, 0xc7, 0x5d, 0xcb, 0x41, 0x4b, 0x16, 0x01, 0x3a, 0xd1, 0x44, 0xe8,
    0x22, 0x32, 0xc6, 0x9c, 0x3f, 0xe7, 0x43, 0xf5, 0x9a, 0xd3, 0xea, 0xf2,
    0xd7, 0x4e, 0x6e, 0x6a, 0x55, 0x73, 0x40, 0xef, 0x89, 0xad, 0x0d, 0x03,
    0x96, 0x7e, 0x78, 0x81, 0x2f, 0x91, 0x1b, 0x44, 0xb0, 0x02, 0xba, 0x1c,
};
static const unsigned char aes_256_no_df_pr_nonce[] = {
    0xdc, 0xe4, 0xd4, 0x27, 0x7a, 0x90, 0xd7, 0x99, 0x43, 0xa1, 0x3c, 0x30,
    0xcc, 0x4b, 0xee, 0x2e
};
static const unsigned char aes_256_no_df_pr_personalizationstring[] = {
    0xe3, 0xe6, 0xb9, 0x11, 0xe4, 0x7a, 0xa4, 0x40, 0x6b, 0xf8, 0x73, 0xf7,
    0x7e, 0xec, 0xc7, 0xb9, 0x97, 0xbf, 0xf8, 0x25, 0x7b, 0xbe, 0x11, 0x9b,
    0x5b, 0x6a, 0x0c, 0x2e, 0x2b, 0x01, 0x51, 0xcd, 0x41, 0x4b, 0x6b, 0xac,
    0x31, 0xa8, 0x0b, 0xf7, 0xe6, 0x59, 0x42, 0xb8, 0x03, 0x0c, 0xf8, 0x06,
};
static const unsigned char aes_256_no_df_pr_additionalinput[] = {
    0x6a, 0x9f, 0x00, 0x91, 0xae, 0xfe, 0xcf, 0x84, 0x99, 0xce, 0xb1, 0x40,
    0x6d, 0x5d, 0x33, 0x28, 0x84, 0xf4, 0x8c, 0x63, 0x4c, 0x7e, 0xbd, 0x2c,
    0x80, 0x76, 0xee, 0x5a, 0xaa, 0x15, 0x07, 0x31, 0xd8, 0xbb, 0x8c, 0x69,
    0x9d, 0x9d, 0xbc, 0x7e, 0x49, 0xae, 0xec, 0x39, 0x6b, 0xd1, 0x1f, 0x7e,
};
static const unsigned char aes_256_no_df_pr_entropyinputpr[] = {
    0xf3, 0xb9, 0x75, 0x9c, 0xbd, 0x88, 0xea, 0xa2, 0x50, 0xad, 0xd6, 0x16,
    0x1a, 0x12, 0x3c, 0x86, 0x68, 0xaf, 0x6f, 0xbe, 0x19, 0xf2, 0xee, 0xcc,
    0xa5, 0x70, 0x84, 0x53, 0x50, 0xcb, 0x9f, 0x14, 0xa9, 0xe5, 0xee, 0xb9,
    0x48, 0x45, 0x40, 0xe2, 0xc7, 0xc9, 0x9a, 0x74, 0xff, 0x8c, 0x99, 0x1f,
};
static const unsigned char aes_256_no_df_pr_int_returnedbits[] = {
    0x2e, 0xf2, 0x45, 0x4c, 0x62, 0x2e, 0x0a, 0xb9, 0x6b, 0xa2, 0xfd, 0x56,
    0x79, 0x60, 0x93, 0xcf
};
static const unsigned char aes_256_no_df_pr_additionalinput2[] = {
    0xaf, 0x69, 0x20, 0xe9, 0x3b, 0x37, 0x9d, 0x3f, 0xb4, 0x80, 0x02, 0x7a,
    0x25, 0x7d, 0xb8, 0xde, 0x71, 0xc5, 0x06, 0x0c, 0xb4, 0xe2, 0x8f, 0x35,
    0xd8, 0x14, 0x0d, 0x7f, 0x76, 0x63, 0x4e, 0xb5, 0xee, 0xe9, 0x6f, 0x34,
    0xc7, 0x5f, 0x56, 0x14, 0x4a, 0xe8, 0x73, 0x95, 0x5b, 0x1c, 0xb9, 0xcb,
};
static const unsigned char aes_256_no_df_pr_entropyinputpr2[] = {
    0xe5, 0xb0, 0x2e, 0x7e, 0x52, 0x30, 0xe3, 0x63, 0x82, 0xb6, 0x44, 0xd3,
    0x25, 0x19, 0x05, 0x24, 0x9a, 0x9f, 0x5f, 0x27, 0x6a, 0x29, 0xab, 0xfa,
    0x07, 0xa2, 0x42, 0x0f, 0xc5, 0xa8, 0x94, 0x7c, 0x17, 0x7b, 0x85, 0x83,
    0x0c, 0x25, 0x0e, 0x63, 0x0b, 0xe9, 0x12, 0x60, 0xcd, 0xef, 0x80, 0x0f,
};
static const unsigned char aes_256_no_df_pr_returnedbits[] = {
    0x5e, 0xf2, 0x26, 0xef, 0x9f, 0x58, 0x5d, 0xd5, 0x4a, 0x10, 0xfe, 0xa7,
    0x2d, 0x5f, 0x4a, 0x46
};


/*
 * AES-256 no df no PR
 */
static const unsigned char aes_256_no_df_entropyinput[] = {
    0xfb, 0xcf, 0x1b, 0x61, 0x16, 0x89, 0x78, 0x23, 0xf5, 0xd8, 0x96, 0xe3,
    0x4e, 0x64, 0x0b, 0x29, 0x9a, 0x3f, 0xf8, 0xa5, 0xed, 0xf2, 0xfe, 0xdb,
    0x16, 0xca, 0x7f, 0x10, 0xfa, 0x5e, 0x18, 0x76, 0x2c, 0x63, 0x5e, 0x96,
    0xcf, 0xb3, 0xd6, 0xfc, 0xaf, 0x99, 0x39, 0x28, 0x9c, 0x61, 0xe8, 0xb3,
};
static const unsigned char aes_256_no_df_nonce[] = {
    0x12, 0x96, 0xf0, 0x52, 0xf3, 0x8d, 0x81, 0xcf, 0xde, 0x86, 0xf2, 0x99,
    0x43, 0x96, 0xb9, 0xf0
};
static const unsigned char aes_256_no_df_personalizationstring[] = {
    0x63, 0x0d, 0x78, 0xf5, 0x90, 0x8e, 0x32, 0x47, 0xb0, 0x4d, 0x37, 0x60,
    0x09, 0x96, 0xbc, 0xbf, 0x97, 0x7a, 0x62, 0x14, 0x45, 0xbd, 0x8d, 0xcc,
    0x69, 0xfb, 0x03, 0xe1, 0x80, 0x1c, 0xc7, 0xe2, 0x2a, 0xf9, 0x37, 0x3f,
    0x66, 0x4d, 0x62, 0xd9, 0x10, 0xe0, 0xad, 0xc8, 0x9a, 0xf0, 0xa8, 0x6d,
};
static const unsigned char aes_256_no_df_additionalinput[] = {
    0x36, 0xc6, 0x13, 0x60, 0xbb, 0x14, 0xad, 0x22, 0xb0, 0x38, 0xac, 0xa6,
    0x18, 0x16, 0x93, 0x25, 0x86, 0xb7, 0xdc, 0xdc, 0x36, 0x98, 0x2b, 0xf9,
    0x68, 0x33, 0xd3, 0xc6, 0xff, 0xce, 0x8d, 0x15, 0x59, 0x82, 0x76, 0xed,
    0x6f, 0x8d, 0x49, 0x74, 0x2f, 0xda, 0xdc, 0x1f, 0x17, 0xd0, 0xde, 0x17,
};
static const unsigned char aes_256_no_df_int_returnedbits[] = {
    0x16, 0x2f, 0x8e, 0x3f, 0x21, 0x7a, 0x1c, 0x20, 0x56, 0xd1, 0x92, 0xf6,
    0xd2, 0x25, 0x75, 0x0e
};
static const unsigned char aes_256_no_df_entropyinputreseed[] = {
    0x91, 0x79, 0x76, 0xee, 0xe0, 0xcf, 0x9e, 0xc2, 0xd5, 0xd4, 0x23, 0x9b,
    0x12, 0x8c, 0x7e, 0x0a, 0xb7, 0xd2, 0x8b, 0xd6, 0x7c, 0xa3, 0xc6, 0xe5,
    0x0e, 0xaa, 0xc7, 0x6b, 0xae, 0x0d, 0xfa, 0x53, 0x06, 0x79, 0xa1, 0xed,
    0x4d, 0x6a, 0x0e, 0xd8, 0x9d, 0xbe, 0x1b, 0x31, 0x93, 0x7b, 0xec, 0xfb,
};
static const unsigned char aes_256_no_df_additionalinputreseed[] = {
    0xd2, 0x46, 0x50, 0x22, 0x10, 0x14, 0x63, 0xf7, 0xea, 0x0f, 0xb9, 0x7e,
    0x0d, 0xe1, 0x94, 0x07, 0xaf, 0x09, 0x44, 0x31, 0xea, 0x64, 0xa4, 0x18,
    0x5b, 0xf9, 0xd8, 0xc2, 0xfa, 0x03, 0x47, 0xc5, 0x39, 0x43, 0xd5, 0x3b,
    0x62, 0x86, 0x64, 0xea, 0x2c, 0x73, 0x8c, 0xae, 0x9d, 0x98, 0x98, 0x29,
};
static const unsigned char aes_256_no_df_additionalinput2[] = {
    0x8c, 0xab, 0x18, 0xf8, 0xc3, 0xec, 0x18, 0x5c, 0xb3, 0x1e, 0x9d, 0xbe,
    0x3f, 0x03, 0xb4, 0x00, 0x98, 0x9d, 0xae, 0xeb, 0xf4, 0x94, 0xf8, 0x42,
    0x8f, 0xe3, 0x39, 0x07, 0xe1, 0xc9, 0xad, 0x0b, 0x1f, 0xed, 0xc0, 0xba,
    0xf6, 0xd1, 0xec, 0x27, 0x86, 0x7b, 0xd6, 0x55, 0x9b, 0x60, 0xa5, 0xc6,
};
static const unsigned char aes_256_no_df_returnedbits[] = {
    0xef, 0xd2, 0xd8, 0x5c, 0xdc, 0x62, 0x25, 0x9f, 0xaa, 0x1e, 0x2c, 0x67,
    0xf6, 0x02, 0x32, 0xe2
};
