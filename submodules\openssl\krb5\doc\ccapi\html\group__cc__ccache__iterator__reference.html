<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_ccache_iterator_t Overview</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_ccache_iterator_t Overview</h1><hr><a name="_details"></a><h2>Detailed Description</h2>
The cc_ccache_iterator_t type represents an iterator that iterates over a set of ccaches and returns them in all in some order. A new instance of this type can be obtained by calling <a class="el" href="group__helper__macros.html#g6957bc9570e4769a5b1213d2a1d90cd7">cc_context_new_ccache_iterator()</a>.<p>
For API function documentation see <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a>. 
<p>
<h2>Data Structures</h2>
<ul>
<li>struct <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a>
</ul>
<h2>Typedefs</h2>
<ul>
<li>typedef <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a> <a class="el" href="group__cc__ccache__iterator__reference.html#g55a6d891b6840466cc956bdd327dc314">cc_ccache_iterator_f</a>
<li>typedef <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a> <a class="el" href="group__cc__ccache__iterator__reference.html#g0ab4121cf78b7e0f92a90e9d4c15f0cf">cc_ccache_iterator_d</a>
<li>typedef <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a> * <a class="el" href="group__cc__ccache__iterator__reference.html#gce800a50d1fe0dcb05b6be0884232318">cc_ccache_iterator_t</a>
</ul>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="g55a6d891b6840466cc956bdd327dc314"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_f" ref="g55a6d891b6840466cc956bdd327dc314" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a> <a class="el" href="structcc__ccache__iterator__f.html">cc_ccache_iterator_f</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="g0ab4121cf78b7e0f92a90e9d4c15f0cf"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_d" ref="g0ab4121cf78b7e0f92a90e9d4c15f0cf" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef struct <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a> <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<a class="anchor" name="gce800a50d1fe0dcb05b6be0884232318"></a><!-- doxytag: member="CredentialsCache.h::cc_ccache_iterator_t" ref="gce800a50d1fe0dcb05b6be0884232318" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">typedef <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_d</a>* <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
