{% for sig in config['sigs'] %}
   {%- for variant in sig['variants'] %}
extern const OSSL_DISPATCH oqs_{{ variant['name'] }}_keymgmt_functions[];
     {%- for classical_alg in variant['mix_with'] -%}
extern const OSSL_DISPATCH oqs_{{ classical_alg['name'] }}_{{ variant['name'] }}_keymgmt_functions[];
     {%- endfor -%}
   {%- endfor %}
{%- endfor %}
{% for kem in config['kems'] %}
extern const OSSL_DISPATCH oqs_{{ kem['name_group'] }}_keymgmt_functions[];
{%- endfor %}
{% for kem in config['kems'] %}
extern const OSSL_DISPATCH oqs_ecp_{{ kem['name_group'] }}_keymgmt_functions[];
{%- endfor %}
{% for kem in config['kems'] %}
extern const OSSL_DISPATCH oqs_ecx_{{ kem['name_group'] }}_keymgmt_functions[];
{%- endfor %}

