# See http://luci-config.appspot.com/schemas/projects/refs:cq.cfg for the
# documentation of this file format.
version: 1
cq_name: "boringssl"
git_repo_url: "https://boringssl.googlesource.com/boringssl"
cq_status_url: "https://chromium-cq-status.appspot.com"
gerrit {
	cq_verified_label: "CQ-Verified"
}
verifiers {
  try_job {
    buckets {
      name: "client.boringssl"

			builders {
        name: "android_aarch64"
        experiment_percentage: 1
      }
			builders {
        name: "android_aarch64_rel"
        experiment_percentage: 1
      }
			builders {
        name: "android_arm"
        experiment_percentage: 1
      }
			builders {
        name: "android_arm_rel"
        experiment_percentage: 1
      }

			builders { name: "linux" }
			builders { name: "linux32" }
			builders { name: "linux32_rel" }
			builders { name: "linux_clang_rel" }
			builders { name: "linux_noasm_asan" }
			builders { name: "linux_nothreads" }
			builders { name: "linux_rel" }
			builders { name: "linux_shared" }
			builders { name: "linux_small" }

			builders { name: "mac" }
			builders { name: "mac_rel" }
			builders { name: "mac_small" }

			builders { name: "win32" }
			builders { name: "win32_rel" }
			builders { name: "win32_small" }
			builders { name: "win64" }
			builders { name: "win64_rel" }
			builders { name: "win64_small" }

			builders { name: "docs" }
    }
  }
}
