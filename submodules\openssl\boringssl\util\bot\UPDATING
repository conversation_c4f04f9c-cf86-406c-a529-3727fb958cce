This directory consumes tools from other repositories for use on the
bots. To update to newer revisions, follow these instructions:

DEPS: Set all revisions to those used in Chromium, found at
   https://chromium.googlesource.com/chromium/src/+/master/DEPS (Search for the
   corresponding repository name.)

go/bootstrap.py: Set TOOLSET_VERSION to the latest release of Go, found at
    https://golang.org/dl/.

update_clang.py: Set CLANG_REVISION and CLANG_SUB_REVISION to the values used in
    Chromium, found at
    https://chromium.googlesource.com/chromium/src/+/master/tools/clang/scripts/update.py

vs_toolchain.py: Set the hash in TOOLCHAIN_HASH to the toolchain
    used in Chromium, found at _GetDesiredVsToolchainHashes
    https://chromium.googlesource.com/chromium/src/+/master/build/vs_toolchain.py
    This may require taking other updates to that file. If updating MSVS
    version, also update TOOLCHAIN_VERSION accordingly.

The .sha1 files correspond to files downloaded from Google Cloud Storage. To
update, place the updated files in their intended location and run:

    upload_to_google_storage.py -b chromium-tools FILE

cmake-linux64.tar.gz: Download the latest CMake source tarball, found at
    https://cmake.org/download/. Build it with:

        ./bootstrap --prefix=$PWD/cmake-linux64 && make && make install
        tar -czf cmake-linux64.tar.gz cmake-linux64/

cmake-mac.tar.gz: Follow the same instructions as above on a Mac, but replace
    cmake-linux64 with cmake-mac.

cmake-win32.zip: Update to the latest prebuilt release of CMake, found at
    https://cmake.org/download/. Use the file labeled "Windows ZIP". The
    download will be named cmake-VERSION-win32-x86.zip.

perl-win32.zip: Update to the latest 32-bit prebuilt "PortableZip" edition of
    Strawberry Perl, found at http://strawberryperl.com/releases.html. The
    download will be named strawberry-perl-VERSION-32bit-portable.zip.

yasm-win32.exe: Update to the appropriate release of Yasm. Use the same version
    as Chromium, found at
    https://chromium.googlesource.com/chromium/src/+/master/third_party/yasm/README.chromium
    Use the release at http://yasm.tortall.net/Download.html labeled
    "Win32 .exe". The download will be named yasm-VERSION-win32.exe.
