=pod

=head1 NAME

ECPKParameters_print, ECPKParameters_print_fp - Functions for decoding and
encoding ASN1 representations of elliptic curve entities

=head1 SYNOPSIS

 #include <openssl/ec.h>

 int ECPKParameters_print(BIO *bp, const EC_GROUP *x, int off);
 int ECPKParameters_print_fp(FILE *fp, const EC_GROUP *x, int off);

=head1 DESCRIPTION

The ECPKParameters represent the public parameters for an
B<EC_GROUP> structure, which represents a curve.

The ECPKParameters_print() and ECPKParameters_print_fp() functions print
a human-readable output of the public parameters of the EC_GROUP to B<bp>
or B<fp>. The output lines are indented by B<off> spaces.

=head1 RETURN VALUES

ECPKParameters_print() and ECPKParameters_print_fp()
return 1 for success and 0 if an error occurs.

=head1 SEE ALSO

L<crypto(7)>, L<EC_GROUP_new(3)>, L<EC_GROUP_copy(3)>,
L<EC_POINT_new(3)>, L<EC_POINT_add(3)>, L<EC_KEY_new(3)>,
L<EC_GFp_simple_method(3)>,

=head1 COPYRIGHT

Copyright 2013-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
