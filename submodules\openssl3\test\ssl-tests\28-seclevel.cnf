# Generated with generate_ssl_tests.pl

num_tests = 6

test-0 = 0-SECLEVEL 3 with default key
test-1 = 1-SECLEVEL 4 with ED448 key
test-2 = 2-SECLEVEL 5 server with ED448 key
test-3 = 3-SECLEVEL 5 client with ED448 key
test-4 = 4-SECLEVEL 3 with P-384 key, X25519 ECDHE
test-5 = 5-SECLEVEL 3 with ED448 key, TLSv1.2
# ===========================================================

[0-SECLEVEL 3 with default key]
ssl_conf = 0-SECLEVEL 3 with default key-ssl

[0-SECLEVEL 3 with default key-ssl]
server = 0-SECLEVEL 3 with default key-server
client = 0-SECLEVEL 3 with default key-client

[0-SECLEVEL 3 with default key-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-SECLEVEL 3 with default key-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = ServerFail


# ===========================================================

[1-SECLEVEL 4 with ED448 key]
ssl_conf = 1-SECLEVEL 4 with ED448 key-ssl

[1-SECLEVEL 4 with ED448 key-ssl]
server = 1-SECLEVEL 4 with ED448 key-server
client = 1-SECLEVEL 4 with ED448 key-client

[1-SECLEVEL 4 with ED448 key-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
CipherString = DEFAULT:@SECLEVEL=4
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem

[1-SECLEVEL 4 with ED448 key-client]
CipherString = DEFAULT:@SECLEVEL=4
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


# ===========================================================

[2-SECLEVEL 5 server with ED448 key]
ssl_conf = 2-SECLEVEL 5 server with ED448 key-ssl

[2-SECLEVEL 5 server with ED448 key-ssl]
server = 2-SECLEVEL 5 server with ED448 key-server
client = 2-SECLEVEL 5 server with ED448 key-client

[2-SECLEVEL 5 server with ED448 key-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
CipherString = DEFAULT:@SECLEVEL=5
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem

[2-SECLEVEL 5 server with ED448 key-client]
CipherString = DEFAULT:@SECLEVEL=4
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = ServerFail


# ===========================================================

[3-SECLEVEL 5 client with ED448 key]
ssl_conf = 3-SECLEVEL 5 client with ED448 key-ssl

[3-SECLEVEL 5 client with ED448 key-ssl]
server = 3-SECLEVEL 5 client with ED448 key-server
client = 3-SECLEVEL 5 client with ED448 key-client

[3-SECLEVEL 5 client with ED448 key-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
CipherString = DEFAULT:@SECLEVEL=4
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem

[3-SECLEVEL 5 client with ED448 key-client]
CipherString = DEFAULT:@SECLEVEL=5
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = ServerFail


# ===========================================================

[4-SECLEVEL 3 with P-384 key, X25519 ECDHE]
ssl_conf = 4-SECLEVEL 3 with P-384 key, X25519 ECDHE-ssl

[4-SECLEVEL 3 with P-384 key, X25519 ECDHE-ssl]
server = 4-SECLEVEL 3 with P-384 key, X25519 ECDHE-server
client = 4-SECLEVEL 3 with P-384 key, X25519 ECDHE-client

[4-SECLEVEL 3 with P-384 key, X25519 ECDHE-server]
Certificate = ${ENV::TEST_CERTS_DIR}/p384-server-cert.pem
CipherString = DEFAULT:@SECLEVEL=3
Groups = X25519
PrivateKey = ${ENV::TEST_CERTS_DIR}/p384-server-key.pem

[4-SECLEVEL 3 with P-384 key, X25519 ECDHE-client]
CipherString = ECDHE:@SECLEVEL=3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/p384-root.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success


# ===========================================================

[5-SECLEVEL 3 with ED448 key, TLSv1.2]
ssl_conf = 5-SECLEVEL 3 with ED448 key, TLSv1.2-ssl

[5-SECLEVEL 3 with ED448 key, TLSv1.2-ssl]
server = 5-SECLEVEL 3 with ED448 key, TLSv1.2-server
client = 5-SECLEVEL 3 with ED448 key, TLSv1.2-client

[5-SECLEVEL 3 with ED448 key, TLSv1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ed448-cert.pem
CipherString = DEFAULT:@SECLEVEL=3
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ed448-key.pem

[5-SECLEVEL 3 with ED448 key, TLSv1.2-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-ed448-cert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success


