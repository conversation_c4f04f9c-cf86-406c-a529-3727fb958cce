=pod

=head1 NAME

EVP_PKEY_copy_parameters, EVP_PKEY_missing_parameters, EVP_PKEY_cmp_parameters,
EVP_PKEY_cmp - public key parameter and comparison functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_missing_parameters(const EVP_PKEY *pkey);
 int EVP_PKEY_copy_parameters(EVP_PKEY *to, const EVP_PKEY *from);

 int EVP_PKEY_cmp_parameters(const EVP_PKEY *a, const EVP_PKEY *b);
 int EVP_PKEY_cmp(const EVP_PKEY *a, const EVP_PKEY *b);

=head1 DESCRIPTION

The function EVP_PKEY_missing_parameters() returns 1 if the public key
parameters of B<pkey> are missing and 0 if they are present or the algorithm
doesn't use parameters.

The function EVP_PKEY_copy_parameters() copies the parameters from key
B<from> to key B<to>. An error is returned if the parameters are missing in
B<from> or present in both B<from> and B<to> and mismatch. If the parameters
in B<from> and B<to> are both present and match this function has no effect.

The function EVP_PKEY_cmp_parameters() compares the parameters of keys
B<a> and B<b>.

The function EVP_PKEY_cmp() compares the public key components and parameters
(if present) of keys B<a> and B<b>.

=head1 NOTES

The main purpose of the functions EVP_PKEY_missing_parameters() and
EVP_PKEY_copy_parameters() is to handle public keys in certificates where the
parameters are sometimes omitted from a public key if they are inherited from
the CA that signed it.

Since OpenSSL private keys contain public key components too the function
EVP_PKEY_cmp() can also be used to determine if a private key matches
a public key.

=head1 RETURN VALUES

The function EVP_PKEY_missing_parameters() returns 1 if the public key
parameters of B<pkey> are missing and 0 if they are present or the algorithm
doesn't use parameters.

These functions EVP_PKEY_copy_parameters() returns 1 for success and 0 for
failure.

The function EVP_PKEY_cmp_parameters() and EVP_PKEY_cmp() return 1 if the
keys match, 0 if they don't match, -1 if the key types are different and
-2 if the operation is not supported.

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_keygen(3)>

=head1 COPYRIGHT

Copyright 2006-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
