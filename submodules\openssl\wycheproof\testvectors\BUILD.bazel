package(default_visibility = ["//visibility:public"])

licenses(["notice"])  # Apache 2.0

exports_files([
    "aes_cbc_pkcs5_test.json",
    "aes_cmac_test.json",
    "aes_gcm_siv_test.json",
    "aes_gcm_test.json",
    "chacha20_poly1305_test.json",
    "dsa_test.json",
    "ecdh_secp224r1_test.json",
    "ecdh_secp256r1_test.json",
    "ecdh_secp384r1_test.json",
    "ecdh_secp521r1_test.json",
    "ecdsa_secp224r1_sha224_test.json",
    "ecdsa_secp224r1_sha256_test.json",
    "ecdsa_secp224r1_sha512_test.json",
    "ecdsa_secp256r1_sha256_test.json",
    "ecdsa_secp256r1_sha512_test.json",
    "ecdsa_secp384r1_sha384_test.json",
    "ecdsa_secp384r1_sha512_test.json",
    "ecdsa_secp521r1_sha512_test.json",
    "eddsa_test.json",
    "kw_test.json",
    "kwp_test.json",
    "rsa_pss_2048_sha1_mgf1_20_test.json",
    "rsa_pss_2048_sha256_mgf1_0_test.json",
    "rsa_pss_2048_sha256_mgf1_32_test.json",
    "rsa_pss_3072_sha256_mgf1_32_test.json",
    "rsa_pss_4096_sha256_mgf1_32_test.json",
    "rsa_pss_4096_sha512_mgf1_32_test.json",
    "rsa_pss_misc_test.json",
    "rsa_signature_test.json",
    "x25519_test.json",
])

filegroup(
    name = "all",
    srcs = glob(["**"]),
)

filegroup(
    name = "aegis",
    srcs = [
        "aegis128L_test.json",
        "aegis128_test.json",
        "aegis256_test.json",
    ],
)

filegroup(
    name = "aes_cbc_pkcs5",
    srcs = ["aes_cbc_pkcs5_test.json"],
)

filegroup(
    name = "aes_ccm",
    srcs = ["aes_ccm_test.json"],
)

filegroup(
    name = "aes_cmac",
    srcs = ["aes_cmac_test.json"],
)

filegroup(
    name = "aes_gcm_siv",
    srcs = ["aes_gcm_siv_test.json"],
)

filegroup(
    name = "aes_eax",
    srcs = ["aes_eax_test.json"],
)

filegroup(
    name = "aes_gcm",
    srcs = ["aes_gcm_test.json"],
)

filegroup(
    name = "aes_siv_cmac",
    srcs = [
        "aead_aes_siv_cmac_test.json",
        "aes_siv_cmac_test.json",
    ],
)

filegroup(
    name = "vmac",
    srcs = [
        "vmac_128_test.json",
        "vmac_64_test.json",
    ],
)

filegroup(
    name = "gmac",
    srcs = [
        "gmac_test.json",
    ],
)

filegroup(
    name = "hmac",
    srcs = [
        "hmac_sha1_test.json",
        "hmac_sha224_test.json",
        "hmac_sha256_test.json",
        "hmac_sha384_test.json",
        "hmac_sha3_224_test.json",
        "hmac_sha3_256_test.json",
        "hmac_sha3_384_test.json",
        "hmac_sha3_512_test.json",
        "hmac_sha512_test.json",
    ],
)

filegroup(
    name = "chacha20_poly1305",
    srcs = [
        "chacha20_poly1305_test.json",
        "xchacha20_poly1305_test.json",
    ],
)

filegroup(
    name = "dsa",
    srcs = [
        "dsa_2048_224_sha224_p1363_test.json",
        "dsa_2048_224_sha224_test.json",
        "dsa_2048_224_sha256_p1363_test.json",
        "dsa_2048_224_sha256_test.json",
        "dsa_2048_256_sha256_p1363_test.json",
        "dsa_2048_256_sha256_test.json",
        "dsa_3072_256_sha256_p1363_test.json",
        "dsa_3072_256_sha256_test.json",
        "dsa_test.json",  # deprecated: use the files above
    ],
)

filegroup(
    name = "ecdsa",
    srcs = [
        "ecdsa_brainpoolP224r1_sha224_test.json",
        "ecdsa_brainpoolP256r1_sha256_test.json",
        "ecdsa_brainpoolP320r1_sha384_test.json",
        "ecdsa_brainpoolP384r1_sha384_test.json",
        "ecdsa_brainpoolP512r1_sha512_test.json",
        "ecdsa_secp224r1_sha224_test.json",
        "ecdsa_secp224r1_sha256_test.json",
        "ecdsa_secp224r1_sha3_224_test.json",
        "ecdsa_secp224r1_sha3_256_test.json",
        "ecdsa_secp224r1_sha3_512_test.json",
        "ecdsa_secp224r1_sha512_test.json",
        "ecdsa_secp256k1_sha256_test.json",
        "ecdsa_secp256k1_sha3_256_test.json",
        "ecdsa_secp256k1_sha3_512_test.json",
        "ecdsa_secp256k1_sha512_test.json",
        "ecdsa_secp256r1_sha256_test.json",
        "ecdsa_secp256r1_sha3_256_test.json",
        "ecdsa_secp256r1_sha3_512_test.json",
        "ecdsa_secp256r1_sha512_test.json",
        "ecdsa_secp384r1_sha384_test.json",
        "ecdsa_secp384r1_sha3_384_test.json",
        "ecdsa_secp384r1_sha3_512_test.json",
        "ecdsa_secp384r1_sha512_test.json",
        "ecdsa_secp521r1_sha3_512_test.json",
        "ecdsa_secp521r1_sha512_test.json",
        "ecdsa_test.json",  # deprecated: use the files above
    ],
)

# Test vectors for ECDSA signatures in P1363 format
filegroup(
    name = "ecdsa_p1363",
    srcs = [
        "ecdsa_brainpoolP224r1_sha224_p1363_test.json",
        "ecdsa_brainpoolP256r1_sha256_p1363_test.json",
        "ecdsa_brainpoolP320r1_sha384_p1363_test.json",
        "ecdsa_brainpoolP384r1_sha384_p1363_test.json",
        "ecdsa_brainpoolP512r1_sha512_p1363_test.json",
        "ecdsa_secp224r1_sha224_p1363_test.json",
        "ecdsa_secp224r1_sha256_p1363_test.json",
        "ecdsa_secp224r1_sha512_p1363_test.json",
        "ecdsa_secp256k1_sha256_p1363_test.json",
        "ecdsa_secp256k1_sha512_p1363_test.json",
        "ecdsa_secp256r1_sha256_p1363_test.json",
        "ecdsa_secp256r1_sha512_p1363_test.json",
        "ecdsa_secp384r1_sha384_p1363_test.json",
        "ecdsa_secp384r1_sha512_p1363_test.json",
        "ecdsa_secp521r1_sha512_p1363_test.json",
    ],
)

filegroup(
    name = "ecdh",
    srcs = [
        "ecdh_brainpoolP224r1_test.json",
        "ecdh_brainpoolP256r1_test.json",
        "ecdh_brainpoolP320r1_test.json",
        "ecdh_brainpoolP384r1_test.json",
        "ecdh_brainpoolP512r1_test.json",
        "ecdh_secp224r1_test.json",
        "ecdh_secp256k1_test.json",
        "ecdh_secp256r1_test.json",
        "ecdh_secp384r1_test.json",
        "ecdh_secp521r1_test.json",
        "ecdh_test.json",  # deprecated use the files above
    ],
)

filegroup(
    name = "ecdh_ecpoint",
    srcs = [
        "ecdh_secp224r1_ecpoint_test.json",
        "ecdh_secp256r1_ecpoint_test.json",
        "ecdh_secp384r1_ecpoint_test.json",
        "ecdh_secp521r1_ecpoint_test.json",
    ],
)

filegroup(
    name = "eddsa",
    srcs = [
        "ed448_test.json",
        "eddsa_test.json",
    ],
)

filegroup(
    name = "keywrap",
    srcs = [
        "kw_test.json",
        "kwp_test.json",
    ],
)

filegroup(
    name = "kdf",
    srcs = [
        "hkdf_sha1_test.json",
        "hkdf_sha256_test.json",
        "hkdf_sha384_test.json",
        "hkdf_sha512_test.json",
    ],
)

# RSA PKCS #1 v.1.5 signatures
filegroup(
    name = "rsa_signature",
    srcs = [
        # Signature verification
        "rsa_signature_2048_sha224_test.json",
        "rsa_signature_2048_sha256_test.json",
        "rsa_signature_2048_sha512_test.json",
        "rsa_signature_3072_sha256_test.json",
        "rsa_signature_3072_sha384_test.json",
        "rsa_signature_3072_sha512_test.json",
        "rsa_signature_4096_sha384_test.json",
        "rsa_signature_4096_sha512_test.json",
        "rsa_signature_2048_sha3_224_test.json",
        "rsa_signature_2048_sha3_256_test.json",
        "rsa_signature_2048_sha3_384_test.json",
        "rsa_signature_2048_sha3_512_test.json",
        "rsa_signature_3072_sha3_256_test.json",
        "rsa_signature_3072_sha3_384_test.json",
        "rsa_signature_3072_sha3_512_test.json",
        "rsa_signature_test.json",
        # Signature generation
        "rsa_sig_gen_misc_test.json",
    ],
)

filegroup(
    name = "rsaes_pkcs1",
    srcs = [
        "rsa_pkcs1_2048_test.json",
        "rsa_pkcs1_3072_test.json",
        "rsa_pkcs1_4096_test.json",
    ],
)

# A small list of test vectors for RSA-PSS.
# The list will be adjusted once we know what is actually used.
filegroup(
    name = "rsa_pss",
    srcs = [
        "rsa_pss_2048_sha1_mgf1_20_test.json",
        "rsa_pss_2048_sha256_mgf1_0_test.json",
        "rsa_pss_2048_sha256_mgf1_32_test.json",
        "rsa_pss_3072_sha256_mgf1_32_test.json",
        "rsa_pss_4096_sha256_mgf1_32_test.json",
        "rsa_pss_4096_sha512_mgf1_32_test.json",
        "rsa_pss_misc_test.json",
    ],
)

# A list with RSA OAEP test vectors.
# The focus of this list are test vectors for parameter sets
# where the hash for the mgf is either the same as the hash for the label
# or where mgf1sha1 is used as mask generation function.
# The reason is that JCE algorithm names such as
# "RSA/None/OAEPPaddingWithSHA256AndMGF1" are ambiguous and interpreted
# differently by various providers.
filegroup(
    name = "rsa_oaep",
    srcs = [
        "rsa_oaep_2048_sha1_mgf1sha1_test.json",
        "rsa_oaep_2048_sha224_mgf1sha1_test.json",
        "rsa_oaep_2048_sha224_mgf1sha224_test.json",
        "rsa_oaep_2048_sha256_mgf1sha1_test.json",
        "rsa_oaep_2048_sha256_mgf1sha256_test.json",
        "rsa_oaep_2048_sha384_mgf1sha1_test.json",
        "rsa_oaep_2048_sha384_mgf1sha384_test.json",
        "rsa_oaep_2048_sha512_mgf1sha1_test.json",
        "rsa_oaep_2048_sha512_mgf1sha512_test.json",
        "rsa_oaep_3072_sha256_mgf1sha1_test.json",
        "rsa_oaep_3072_sha256_mgf1sha256_test.json",
        "rsa_oaep_3072_sha512_mgf1sha1_test.json",
        "rsa_oaep_3072_sha512_mgf1sha512_test.json",
        "rsa_oaep_4096_sha256_mgf1sha1_test.json",
        "rsa_oaep_4096_sha256_mgf1sha256_test.json",
        "rsa_oaep_4096_sha512_mgf1sha1_test.json",
        "rsa_oaep_4096_sha512_mgf1sha512_test.json",
        "rsa_oaep_misc_test.json",
    ],
)

# Webcrypto uses ECDSA signatures with P1363 format.
# The test vectors in "ecdsa_p1363" that use a curve supported by webcrypto
# can be used as additional test vectors.
filegroup(
    name = "ecdsa_webcrypto",
    srcs = ["ecdsa_webcrypto_test.json"],
)

filegroup(
    name = "ecdh_webcrypto",
    srcs = ["ecdh_webcrypto_test.json"],
)

filegroup(
    name = "xdh",
    srcs = [
        "x25519_asn_test.json",
        "x25519_jwk_test.json",
        "x25519_pem_test.json",
        "x25519_test.json",
        "x448_asn_test.json",
        "x448_jwk_test.json",
        "x448_pem_test.json",
        "x448_test.json",
    ],
)
