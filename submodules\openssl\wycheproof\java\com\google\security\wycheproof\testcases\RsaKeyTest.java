/**
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.security.wycheproof;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import com.google.security.wycheproof.WycheproofRunner.NoPresubmitTest;
import com.google.security.wycheproof.WycheproofRunner.ProviderType;
import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.interfaces.RSAPrivateCrtKey;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

/**
 * Tests RSA keys. Signatures and encryption are tested in different tests.
 *
 * <AUTHOR> (Daniel Bleichenbacher)
 */
// TODO(bleichen):
// - Expect keys with e=1 to be rejected
// - Expect keys with e=0 to be rejected
// - Document stuff
// - Check encodings of private keys.
// - Test multi prime RSA
// - Tests for alternative representations:
//    many libraries sort the primes as: p > q (but not all)
//    some libraries compute d mod lambda(n)
//    paramaters p,q,... are not really required
// - Checks for bad random number generation.
// - A X509 encoded public key contains a pkcs1algorithm.
//   This may for example be used to include RSA-PSS parameters into key,
//   but requires new interfaces.
// - Convert test vectors into JSON format to test other languages.
// - Add checks for malformed private keys.
@RunWith(JUnit4.class)
public class RsaKeyTest {

  public static final String ENCODED_PUBLIC_KEY =
      "30819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
          + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
          + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
          + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
          + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
          + "0001";

  /**
   * Encodings of the public key from ENCODED_PUBLIC_KEY with modifications. This list so far has
   * just a simple purpose: it is used to check whether parsing the key leads to some unexpected
   * exceptions. I.e. it should not be possible to crash an application with an modified public key.
   */
  public static final String[] MODIFIED_PUBLIC_KEY = {
    // length contains leading 0
    "3082009f300d06092a864886f70d010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "3081a13082000d06092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f068200092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f06092a864886f70d0101010582000003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d01010105000382008d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "30819f300d06092a864886f70d010101050003818d3082008902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101050003818d30818a0282008100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02820003"
        + "010001",
    // wrong length
    "30a0300d06092a864886f70d010101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "309e300d06092a864886f70d010101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819f300e06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300c06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d060a2a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06082a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101050103818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819e300d06092a864886f70d0101010500038e0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d0101010500038c0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819d300d06092a864886f70d010101050003818b308a02818100ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819d300d06092a864886f70d010101050003818b308802818100ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819d300d06092a864886f70d010101050003818b308188028200ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819d300d06092a864886f70d010101050003818b308188028000ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02040100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02020100"
        + "01",
    // uint32 overflow in length
    "3085010000009f300d06092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a43085010000000d06092a864886f70d010101050003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a43012068501000000092a864886f70d010101050003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a4301206092a864886f70d0101010585010000000003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a3300d06092a864886f70d01010105000385010000008d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a2300d06092a864886f70d01010105000381903085010000008902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a2300d06092a864886f70d010101050003819030818d0285010000008100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02850100"
        + "000003010001",
    // uint64 overflow in length
    "308901000000000000009f300d06092a864886f70d010101050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a8308901000000000000000d06092a864886f70d010101050003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    "3081a8301606890100000000000000092a864886f70d010101050003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    "3081a8301606092a864886f70d010101058901000000000000000003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    "3081a7300d06092a864886f70d0101010500038901000000000000008d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a6300d06092a864886f70d01010105000381943089010000000000000089"
        + "02818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a6300d06092a864886f70d01010105000381943081910289010000000000"
        + "00008100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a7300d06092a864886f70d010101050003819530819202818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02890100"
        + "00000000000003010001",
    // length = 2**32 - 1
    "3084ffffffff300d06092a864886f70d010101050003818d0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a33084ffffffff06092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a330110684ffffffff2a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3301106092a864886f70d0101010584ffffffff03818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a2300d06092a864886f70d01010105000384ffffffff0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a1300d06092a864886f70d010101050003818f3084ffffffff02818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300d06092a864886f70d010101050003818f30818c0284ffffffff00ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a2300d06092a864886f70d010101050003819030818d02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0284ffff"
        + "ffff010001",
    // length = 2**64 - 1
    "3088ffffffffffffffff300d06092a864886f70d010101050003818d00308189"
        + "02818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a73088ffffffffffffffff06092a864886f70d010101050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a730150688ffffffffffffffff2a864886f70d010101050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a7301506092a864886f70d0101010588ffffffffffffffff03818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a6300d06092a864886f70d01010105000388ffffffffffffffff00308189"
        + "02818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a5300d06092a864886f70d01010105000381933088ffffffffffffffff02"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a5300d06092a864886f70d01010105000381933081900288ffffffffffff"
        + "ffff00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a6300d06092a864886f70d010101050003819430819102818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0288ffff"
        + "ffffffffffff010001",
    // removing sequence
    "",
    "30819003818d0030818902818100ab9014dc47d44b6d260fc1fef9ab022042fd"
        + "9566e9d7b60c54100cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb1"
        + "67d8a44ab93d73c4d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e"
        + "043b6fec81f9d984b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31"
        + "dad83563f3a315acf9a0b351a23f0203010001",
    // appending 0's to sequence
    "3081a1300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00010000",
    "3081a1300f06092a864886f70d0101010500000003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "010000",
    // prepending 0's to sequence
    "3081a10000300d06092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f000006092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d010101050003818e30818b000002818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    // appending unused 0's
    "30819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00010000",
    "3081a1300d06092a864886f70d0101010500000003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f06092a864886f70d0101010000050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d010101050003818e30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "010000",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f00000203"
        + "010001",
    // appending null value
    "3081a1300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00010500",
    "3081a1300f06092a864886f70d0101010500050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f060b2a864886f70d0101010500050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f06092a864886f70d0101010502050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300d06092a864886f70d010101050003818f0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00010500",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "010500",
    "3081a0300d06092a864886f70d010101050003818e30818b02818300ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f05000203"
        + "010001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02050100"
        + "010500",
    // including garbage
    "3081a4498030819f300d06092a864886f70d010101050003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a4250030819f300d06092a864886f70d010101050003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a230819f300d06092a864886f70d010101050003818d0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "02030100010004deadbeef",
    "3081a330114980300d06092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a330112500300d06092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a7300f300d06092a864886f70d01010105000004deadbeef03818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a33011260d498006092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a33011260d250006092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a73015260b06092a864886f70d0101010004deadbeef050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a3301106092a864886f70d01010125044980050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3301106092a864886f70d01010125042500050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a7301506092a864886f70d010101250205000004deadbeef03818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a4300d06092a864886f70d0101010500238192498003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a4300d06092a864886f70d0101010500238192250003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a8300d06092a864886f70d010101050023819003818d0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "02030100010004deadbeef",
    "3081a3300d06092a864886f70d010101050003819130818e4980308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e2500308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a7300d06092a864886f70d010101050003819530818c30818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "030100010004deadbeef",
    "3081a3300d06092a864886f70d010101050003819130818e2281864980028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e2281862500028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a7300d06092a864886f70d010101050003819530819222818402818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f00"
        + "04deadbeef0203010001",
    "3081a2300d06092a864886f70d010101050003819030818d02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f22074980"
        + "0203010001",
    "3081a2300d06092a864886f70d010101050003819030818d02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f22072500"
        + "0203010001",
    "3081a6300d06092a864886f70d010101050003819430819102818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f22050203"
        + "0100010004deadbeef",
    // including undefined tags
    "3081a8aa00bb00cd0030819f300d06092a864886f70d010101050003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    "3081a6aa02aabb30819f300d06092a864886f70d010101050003818d00308189"
        + "02818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a73015aa00bb00cd00300d06092a864886f70d010101050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a53013aa02aabb300d06092a864886f70d010101050003818d0030818902"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a730152611aa00bb00cd0006092a864886f70d010101050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a53013260faa02aabb06092a864886f70d010101050003818d0030818902"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a7301506092a864886f70d0101012508aa00bb00cd00050003818d003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a5301306092a864886f70d0101012506aa02aabb050003818d0030818902"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a8300d06092a864886f70d0101010500238196aa00bb00cd0003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    "3081a6300d06092a864886f70d0101010500238194aa02aabb03818d00308189"
        + "02818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6"
        + "e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f1"
        + "09fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562"
        + "517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0"
        + "b351a23f0203010001",
    "3081a7300d06092a864886f70d0101010500038195308192aa00bb00cd003081"
        + "8902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a5300d06092a864886f70d0101010500038193308190aa02aabb30818902"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a7300d06092a864886f70d010101050003819530819222818aaa00bb00cd"
        + "0002818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100c"
        + "b6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0"
        + "f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b5"
        + "62517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9"
        + "a0b351a23f0203010001",
    "3081a5300d06092a864886f70d0101010500038193308190228188aa02aabb02"
        + "818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1"
        + "d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109"
        + "fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b56251"
        + "7e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b3"
        + "51a23f0203010001",
    "3081a6300d06092a864886f70d010101050003819430819102818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f220baa00"
        + "bb00cd000203010001",
    "3081a4300d06092a864886f70d010101050003819230818f02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f2209aa02"
        + "aabb0203010001",
    // changing tag value
    "2e819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "32819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "ff819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f2e0d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f320d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819fff0d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d04092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d08092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300dff092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101030003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101070003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101ff0003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101050001818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101050005818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d0101010500ff818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819e300d06092a864886f70d010101050003818c2e818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c32818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818cff818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818900818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818904818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c308189ff818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f00030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f04030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23fff030100"
        + "01",
    // dropping value of sequence
    "3000",
    "308192300003818d0030818902818100ab9014dc47d44b6d260fc1fef9ab0220"
        + "42fd9566e9d7b60c54100cb6e1d4edc98590467d0502c17fce69d00ac5efb40b"
        + "2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236ff517cf84412e173679cfa"
        + "e42e043b6fec81f9d984b562517e6febe1f72295dbc3fdfc19d3240aa7551556"
        + "3f31dad83563f3a315acf9a0b351a23f0203010001",
    // using composition
    "3081a430013030819e0d06092a864886f70d010101050003818d003081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a33011300106300c092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a33011260d06012a0608864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a4300d06092a864886f70d010101050023819203010003818c3081890281"
        + "8100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4"
        + "edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb"
        + "5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e"
        + "6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351"
        + "a23f0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e3001023081888181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e2281860201000281"
        + "80ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a2300d06092a864886f70d010101050003819030818d02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f22070201"
        + "0102020001",
    // truncate sequence
    "30819e300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00",
    "30819e0d06092a864886f70d010101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300c06092a864886f70d0101010503818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300c092a864886f70d010101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819d300d06092a864886f70d010101050003818b30818802818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100",
    "30819d300d06092a864886f70d010101050003818b308188818100ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    // prepend empty sequence
    "3081a13000300d06092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300f300006092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d010101050003818e30818b300002818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    // append empty sequence
    "3081a1300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00013000",
    "3081a1300f06092a864886f70d0101010500300003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "013000",
    // sequence of sequence
    "3081a230819f300d06092a864886f70d010101050003818d0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a1300f300d06092a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300d06092a864886f70d010101050003818f30818c30818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    // truncated sequence
    "300f300d06092a864886f70d0101010500",
    "30819d300b06092a864886f70d01010103818d0030818902818100ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "308199300d06092a864886f70d010101050003818730818402818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f",
    // repeat element in sequence
    "3082012f300d06092a864886f70d010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "01000103818d0030818902818100ab9014dc47d44b6d260fc1fef9ab022042fd"
        + "9566e9d7b60c54100cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb1"
        + "67d8a44ab93d73c4d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e"
        + "043b6fec81f9d984b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31"
        + "dad83563f3a315acf9a0b351a23f0203010001",
    "3081a3300d06092a864886f70d010101050003819130818e02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "010203010001",
    // long form encoding of length
    "3081a030810d06092a864886f70d010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "3081a0300e0681092a864886f70d010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "3081a0300e06092a864886f70d01010105810003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "30819f300d06092a864886f70d010101050003818d30818a02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02810301"
        + "0001",
    // removing oid
    "3081943002050003818d0030818902818100ab9014dc47d44b6d260fc1fef9ab"
        + "022042fd9566e9d7b60c54100cb6e1d4edc98590467d0502c17fce69d00ac5ef"
        + "b40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236ff517cf84412e17367"
        + "9cfae42e043b6fec81f9d984b562517e6febe1f72295dbc3fdfc19d3240aa755"
        + "15563f31dad83563f3a315acf9a0b351a23f0203010001",
    // appending 0's to oid
    "3081a1300f060b2a864886f70d0101010000050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    // prepending 0's to oid
    "3081a1300f060b00002a864886f70d010101050003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    // dropping value of oid
    "30819630040600050003818d0030818902818100ab9014dc47d44b6d260fc1fe"
        + "f9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d0502c17fce69d00a"
        + "c5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236ff517cf84412e1"
        + "73679cfae42e043b6fec81f9d984b562517e6febe1f72295dbc3fdfc19d3240a"
        + "a75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    // modify first byte of oid
    "30819f300d06092b864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    // modify last byte of oid
    "30819f300d06092a864886f70d010100050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    // truncate oid
    "30819e300c06082a864886f70d0101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300c0608864886f70d010101050003818d0030818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    // wrong oid
    "30819b300906052b0e03021a050003818d0030818902818100ab9014dc47d44b"
        + "6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d0502c1"
        + "7fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236ff51"
        + "7cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295dbc3fd"
        + "fc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819f300d0609608648016503040201050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    // longer oid
    "3081a0300e060a2a864886f70d01010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    // oid with modified node
    "30819f300d06092a864886f70d010111050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "3081a33011060d2a864886f70d01018880808001050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    // large integer in oid
    "3081a8301606122a864886f70d010182808080808080808001050003818d0030"
        + "818902818100ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c5410"
        + "0cb6e1d4edc98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4"
        + "d0f109fb5a26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984"
        + "b562517e6febe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315ac"
        + "f9a0b351a23f0203010001",
    // oid with invalid node
    "3081a0300e060a2a864886f70d010101e0050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "3081a0300e060a2a80864886f70d010101050003818d0030818902818100ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    // appending 0's to null
    "3081a1300f06092a864886f70d0101010502000003818d0030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    // appending 0's to bit string
    "3081a1300d06092a864886f70d010101050003818f0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00010000",
    // prepending 0's to bit string
    "3081a1300d06092a864886f70d010101050003818f00000030818902818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    // modify first byte of bit string
    "30819f300d06092a864886f70d010101050003818d0130818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    // modify last byte of bit string
    "30819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0000",
    // truncate bit string
    "30819e300d06092a864886f70d010101050003818c0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "00",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    // removing integer
    "3018300d06092a864886f70d0101010500030730050203010001",
    // appending 0's to integer
    "3081a0300d06092a864886f70d010101050003818e30818b02818300ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f00000203"
        + "010001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02050100"
        + "010000",
    // prepending 0's to integer
    "3081a0300d06092a864886f70d010101050003818e30818b028183000000ab90"
        + "14dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590"
        + "467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8"
        + "823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f7"
        + "2295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203"
        + "010001",
    "3081a0300d06092a864886f70d010101050003818e30818b02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02050000"
        + "010001",
    // dropping value of integer
    "301a300d06092a864886f70d01010105000309300702000203010001",
    "30819b300d06092a864886f70d010101050003818930818602818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0200",
    // modify first byte of integer
    "30819e300d06092a864886f70d010101050003818c30818902818101ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030000"
        + "01",
    // modify last byte of integer
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23e02030100"
        + "01",
    "30819e300d06092a864886f70d010101050003818c30818902818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02030100"
        + "00",
    // truncate integer
    "30819d300d06092a864886f70d010101050003818b30818802818000ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a20203010001",
    "30819d300d06092a864886f70d010101050003818b308188028180ab9014dc47"
        + "d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d05"
        + "02c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f8823236"
        + "ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295db"
        + "c3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203010001",
    "30819d300d06092a864886f70d010101050003818b30818802818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02020100",
    "30819d300d06092a864886f70d010101050003818b30818802818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02020001",
    // leading ff in integer
    "30819f300d06092a864886f70d010101050003818d30818a028182ff00ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020301"
        + "0001",
    "30819f300d06092a864886f70d010101050003818d30818a02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0204ff01"
        + "0001",
    // infinity
    "301b300d06092a864886f70d0101010500030a30080901800203010001",
    "30819c300d06092a864886f70d010101050003818a30818702818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f090180",
    // n=0
    "301c300d06092a864886f70d0101010500030b0030080201000203010001",
    // negative n
    "30819f300d06092a864886f70d010101050003818d00308189028181ff546feb"
        + "23b82bb492d9f03e010654fddfbd026a99162849f3abeff3491e2b12367a6fb9"
        + "82fafd3e8031962ff53a104bf4d34e98275bb546c28c3b2f0ef604a5d93d077d"
        + "cdc900ae8307bbed1e8c9863051bd1fbc490137e06267b4a9dae8190141e08dd"
        + "6a243c0203e62cdbf558aaeaa9c0ce2527ca9c0c5cea53065f4cae5dc1020301"
        + "0001",
    // e=0
    "30819d300d06092a864886f70d010101050003818b0030818702818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020100",
    // e=1
    "30819d300d06092a864886f70d010101050003818b0030818702818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020101",
    // e=2
    "30819d300d06092a864886f70d010101050003818b0030818702818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f020102",
    // negative e
    "30819f300d06092a864886f70d010101050003818d0030818902818100ab9014"
        + "dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9859046"
        + "7d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f882"
        + "3236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f722"
        + "95dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f0203fe"
        + "ffff",
    // Some encodings that were problematic in jdk before the fix in Jan 2017.
    "3013300d06092a864886f70d010101050003023000",
    // dropping value of bit string
    "3011300d06092a864886f70d01010105000300",
    // length = 2**31 - 1
    "30847fffffff300d06092a864886f70d010101050003818d0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a330847fffffff06092a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3301106847fffffff2a864886f70d010101050003818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a3301106092a864886f70d01010105847fffffff03818d00308189028181"
        + "00ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4ed"
        + "c98590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a"
        + "26c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6f"
        + "ebe1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a2"
        + "3f0203010001",
    "3081a2300d06092a864886f70d010101050003847fffffff0030818902818100"
        + "ab9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc9"
        + "8590467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26"
        + "c2f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6feb"
        + "e1f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f"
        + "0203010001",
    "3081a1300d06092a864886f70d010101050003818f30847fffffff02818100ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a1300d06092a864886f70d010101050003818f30818c02847fffffff00ab"
        + "9014dc47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc985"
        + "90467d0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2"
        + "f8823236ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1"
        + "f72295dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02"
        + "03010001",
    "3081a2300d06092a864886f70d010101050003819030818d02818100ab9014dc"
        + "47d44b6d260fc1fef9ab022042fd9566e9d7b60c54100cb6e1d4edc98590467d"
        + "0502c17fce69d00ac5efb40b2cb167d8a44ab93d73c4d0f109fb5a26c2f88232"
        + "36ff517cf84412e173679cfae42e043b6fec81f9d984b562517e6febe1f72295"
        + "dbc3fdfc19d3240aa75515563f31dad83563f3a315acf9a0b351a23f02847fff"
        + "ffff010001",
  };

  /**
   * Parses a list of modified encodings of an RSA public key. Expects that any modification either
   * results in an InvalidKeySpecException or an altered PublicKey. This test has mostly "defense in
   * depth" characteristic, since applications should never accept unauthenticated public keys.
   */
  @Test
  public void testModifiedPublicKeyDecoding() throws Exception {
    KeyFactory kf = KeyFactory.getInstance("RSA");
    int cnt = 0;
    for (String encoded : MODIFIED_PUBLIC_KEY) {
      X509EncodedKeySpec spec = new X509EncodedKeySpec(TestUtil.hexToBytes(encoded));
      try {
        RSAPublicKey unusedKey = (RSAPublicKey) kf.generatePublic(spec);
        // TODO(bleichen): check the keys that are accepted.
        //   To decide whether a key should have been rejected or not the test vectors must be
        //   divided into several categories:
        //   - keys that use BER encoding: these are OK
        //   - keys that include additional fields or garbage: might be OK
        //   - keys where the modification does not change the values: are probably OK
        //   - keys with missing parameters: should be rejected
        //   - keys with impossible values (negative n, negative e): should be rejected.
      } catch (InvalidKeySpecException ex) {
        // expected
      } catch (Exception ex) {
        System.out.println("generatePublic throws:" + ex.getMessage() + " for " + encoded);
        cnt++;
      }
    }
    assertEquals(0, cnt);
  }

  private void checkPrivateCrtKey(RSAPrivateCrtKey key, int expectedKeySize) throws Exception {
    BigInteger p = key.getPrimeP();
    BigInteger q = key.getPrimeQ();
    BigInteger n = key.getModulus();
    BigInteger e = key.getPublicExponent();
    BigInteger d = key.getPrivateExponent();
    BigInteger dp = key.getPrimeExponentP();
    BigInteger dq = key.getPrimeExponentQ();
    BigInteger crtCoeff = key.getCrtCoefficient();

    // Simple test that (n,d,e) is a valid RSA key.
    assertEquals(n, p.multiply(q));
    assertEquals(expectedKeySize, n.bitLength());
    int certainty = 80;
    assertTrue(p.isProbablePrime(certainty));
    assertTrue(q.isProbablePrime(certainty));
    // Very simple checks for weak random number generators.
    RandomUtil.checkPrime(p);
    RandomUtil.checkPrime(q);
    // TODO(bleichen): Keys that are very imbalanced can be broken with elliptic curve factoring.
    //   Add other checks. E.g. for the size of dp and dq
    assertTrue(p.bitLength() > 256);
    assertTrue(q.bitLength() > 256);
    BigInteger p1 = p.subtract(BigInteger.ONE);
    BigInteger q1 = q.subtract(BigInteger.ONE);
    BigInteger phi = p1.multiply(q1);
    BigInteger order = phi.divide(p1.gcd(q1)); // maximal order of elements
    // RFC 8017 Section 3.2 specifies that d is a positive integer smaller than n satisfying
    //    e * d == 1 (mod lcm(p-1, q-1)).
    // FIPS-PUB 186-4 specifies that d is the smallest positive integer satisfying
    // the equation above and further specifies that key with d < 2^(n.bitlenght()/2) are not
    // allowed. The second condition is very unlikely to hold if keys are chosen at random.
    // Hence seeing a small d indicates with high probability a faulty key generation, such
    // as switching e and d, or selecting the primes p and q incorretly.
    // Such keys can likely be broken easily. I.e. since lcm(p - 1, q - 1) divides d * e - 1,
    // it follows that (p - 1) * (q - 1) divides (d * e - 1) * gcd(p - 1, q - 1).
    // Hence if d * e - 1 is small then p - 1 and q - 1 must have a large common factor g.
    assertEquals(1, d.compareTo(BigInteger.ONE));
    assertEquals(-1, d.compareTo(n)); // This is the requirement of RFC 8017
    // The following would be the stricter requirement of FIPS-PUB 186-4.
    // assertEquals(-1, d.compareTo(order));
    assertTrue(d.bitLength() > expectedKeySize / 2);
    assertEquals(BigInteger.ONE, d.multiply(e).mod(order));
    assertEquals(d.mod(p1), dp.mod(p1));
    assertEquals(d.mod(q1), dq.mod(q1));
    assertEquals(q.multiply(crtCoeff).mod(p), BigInteger.ONE);
    // Checks that p - 1 and q - 1 do not have a large common factor g. Since large common
    // factors are very unlikely to occur at random one has to assume that such an event is caused
    // by a faulty generation and that g is in fact known. Coppersmith showed how to factor an RSA
    // modulus if about 1/4 of the low order bits of a factor is known.
    assertTrue(p1.gcd(q1).bitLength() < expectedKeySize / 4);
  }

  private void checkPublicKey(RSAPublicKey pub) {
    BigInteger e = pub.getPublicExponent();
    BigInteger n = pub.getModulus();
    // Checks that e > 1. [CVE-1999-1444]
    assertEquals(1, e.compareTo(BigInteger.ONE));
    // TODO(bleichen): Try to generalize and test private keys once the paper is available.
    // Test for CVE-2017-15361. Public keys generated by the broken generator can be identified
    // heuristically by testing if n is equivalent to a power of 65537 modulo the following primes:
    int[] primes = {11, 13, 17, 19, 37, 53, 61, 71, 73, 79, 97, 103, 107, 109, 127, 151, 157};
    boolean hasPattern = true;
    for (int prime : primes) {
      int residue = n.mod(BigInteger.valueOf(prime)).intValue();
      int exp = 1;
      do {
        exp = exp * 65537 % prime;
      } while (exp != 1 && exp != residue);
      if (exp != residue) {
        hasPattern = false;
        break;
      }
    }
    assertFalse("Public key has pattern from CVE-2017-15361. n = " + n.toString(), hasPattern);
  }

  private void checkKeyPair(KeyPair keypair, int keySizeInBits) throws Exception {
    RSAPublicKey pub = (RSAPublicKey) keypair.getPublic();
    RSAPrivateKey priv = (RSAPrivateKey) keypair.getPrivate();
    if (priv instanceof RSAPrivateCrtKey) {
      checkPrivateCrtKey((RSAPrivateCrtKey) priv, keySizeInBits);
    } else {
      // Using a CRT key leads to 6-7 times better performance than not using the CRT.
      // Such a perfomance loss makes a library almost useless. Thus we consider this
      // a bug.
      fail("Expecting an RSAPrivateCrtKey instead of " + priv.getClass().getName());
    }
    checkPublicKey(pub);
    assertEquals(pub.getModulus(), priv.getModulus());
  }

  /**
   * Checks the default key size used for RSA key generation.
   *
   * <p>This test fails if the default key size for RSA is below the minimum recommendation of NIST
   * SP 800-57 part1 revision 4, Table 2, page 53 in
   * http://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-57pt1r4.pdf . NIST recommends
   * a minimal security strength of 112 bits for keys used until 2030. This translates to a minimal
   * key size of 2048 bits.
   *
   * <p>Enisa, Algorithms, key size and parameters report – 2014, Section 3.6
   * https://www.enisa.europa.eu/publications/algorithms-key-size-and-parameters-report-2014 Enisa,
   * also suggests that 2048-bit RSA keys provide a security strength of about 112 bits. However,
   * the recommendation for near term systems is more conservative than NIST. Enisa recommends a
   * minimal key size of 3072 bits.
   *
   * <p>ECRYPT II Yearly Report on Algorithms and Keysizes (2011-2012), Section 13.3
   * http://www.ecrypt.eu.org/ecrypt2/documents/D.SPA.20.pdf Suggests at least 2432 bits for new
   * keys and at least 1024 bits for legacy keys.
   *
   * <p>All the references above clearly state that keys smaller than 2048 bits should only be used
   * in legacy cases. Therefore, it seems wrong to use a default key size smaller than 2048 bits. If
   * a user really wants a small RSA key then such a choice should be made by explicitly providing
   * the desired key length during the initalization of the KeyPairGenerator.
   *
   * <p>According to https://docs.oracle.com/javase/7/docs/api/javax/crypto/Cipher.html every
   * implementation of the Java platform is required to implement RSA with both 1024 and 2048 bit
   * key sizes. Hence a 2048 bit default should not lead to compatibility problems.
   */
  @NoPresubmitTest(
    providers = {ProviderType.OPENJDK},
    bugs = {"b/33190530"}
  )
  @Test
  public void testDefaultKeySize() throws Exception {
    KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
    KeyPair keypair = keyGen.genKeyPair();
    RSAPublicKey pub = (RSAPublicKey) keypair.getPublic();
    int keySizeInBits = pub.getModulus().bitLength();
    System.out.println("testDefaultSize: keysize=" + keySizeInBits);
    checkKeyPair(keypair, keySizeInBits);
    if (keySizeInBits < 2048) {
      fail("RSA default key size too small:" + keySizeInBits);
    }
  }

  private void testKeyGenerationSize(int keySizeInBits) throws Exception {
    KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
    keyGen.initialize(keySizeInBits);
    KeyPair keypair = keyGen.genKeyPair();
    checkKeyPair(keypair, keySizeInBits);
  }

  @Test
  public void testKeyGeneration() throws Exception {
    testKeyGenerationSize(1024);
    testKeyGenerationSize(2048);
  }

  /**
   * Checks whether decoding and again encoding an RSA public key results in the same encoding. This
   * is a regression test. Failing this test implies that the encoding has changed. Such a failure
   * does not need to be a bug, since several encoding for the same key are possible.
   */
  @Test
  public void testEncodeDecodePublic() throws Exception {
    KeyFactory kf = KeyFactory.getInstance("RSA");
    byte[] encoded = TestUtil.hexToBytes(ENCODED_PUBLIC_KEY);
    X509EncodedKeySpec spec = new X509EncodedKeySpec(encoded);
    RSAPublicKey pub = (RSAPublicKey) kf.generatePublic(spec);
    assertEquals(
        "The test assumes that the public key is in X.509 format", "X.509", pub.getFormat());
    assertEquals(ENCODED_PUBLIC_KEY, TestUtil.bytesToHex(pub.getEncoded()));
  }

  @NoPresubmitTest(
    providers = {ProviderType.CONSCRYPT},
    bugs = {"b/145113402"}
  )
  @Test
  public void testEncodeDecodePrivate() throws Exception {
    int keySizeInBits = 2048;
    KeyFactory kf = KeyFactory.getInstance("RSA");
    KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
    keyGen.initialize(keySizeInBits);
    KeyPair keypair = keyGen.genKeyPair();
    RSAPrivateKey priv = (RSAPrivateKey) keypair.getPrivate();
    assertTrue("Expecting an RSA private key with CRT parameters",
               priv instanceof RSAPrivateCrtKey);
    byte[] encoded = priv.getEncoded();
    PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(encoded);
    RSAPrivateKey priv2 = (RSAPrivateKey) kf.generatePrivate(spec);
    // Checks that priv2 equivalent to priv1.
    assertEquals(priv2.getModulus(), priv.getModulus());
    assertEquals(priv2.getPrivateExponent(), priv.getPrivateExponent());
    // Some implementations are not subclasses of RSAPrivateCtrKey.
    // E.g. org.conscrypt.OpenSSLRSAPrivateKey.
    // But there is OpenSSLRSAPrivateCrtKey
    if (priv instanceof RSAPrivateCrtKey) {
      checkPrivateCrtKey((RSAPrivateCrtKey) priv, keySizeInBits);
    } else {
      // Using a CRT key leads to 6-7 times better performance than not using the CRT.
      fail("Expecting an RSAPrivateCrtKey instead of " + priv.getClass().getName());
    }
  }

}
