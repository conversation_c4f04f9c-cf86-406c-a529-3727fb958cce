/*
 * Copyright 1995-2016 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_SSL2_H
# define OPENSSL_SSL2_H
# pragma once

# include <openssl/macros.h>
# ifndef OPENSSL_NO_DEPRECATED_3_0
#  define HEADER_SSL2_H
# endif

#ifdef  __cplusplus
extern "C" {
#endif

# define SSL2_VERSION            0x0002

# define SSL2_MT_CLIENT_HELLO            1

#ifdef  __cplusplus
}
#endif
#endif
