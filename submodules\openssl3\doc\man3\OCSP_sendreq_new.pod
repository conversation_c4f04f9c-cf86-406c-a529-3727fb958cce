=pod

=head1 NAME

OCSP_REQ_CTX,
OCSP_sendreq_new,
OCSP_sendreq_nbio,
OCSP_sendreq_bio,
OCSP_REQ_CTX_i2d,
OCSP_REQ_CTX_add1_header,
OCSP_REQ_CTX_free,
OCSP_set_max_response_length,
OCSP_REQ_CTX_set1_req
- OCSP responder query functions

=head1 SYNOPSIS

 #include <openssl/ocsp.h>

 OSSL_HTTP_REQ_CTX *OCSP_sendreq_new(BIO *io, const char *path,
                                     const OCSP_REQUEST *req, int buf_size);
 OCSP_RESPONSE *OCSP_sendreq_bio(BIO *io, const char *path, OCSP_REQUEST *req);

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 typedef OSSL_HTTP_REQ_CTX OCSP_REQ_CTX;
 int OCSP_sendreq_nbio(OCSP_RESPONSE **presp, OSSL_HTTP_REQ_CTX *rctx);
 int OCSP_REQ_CTX_i2d(OCSP_REQ_CT *rctx, const ASN1_ITEM *it, ASN1_VALUE *req);
 int OCSP_REQ_CTX_add1_header(OCSP_REQ_CT *rctx,
                              const char *name, const char *value);
 void OCSP_REQ_CTX_free(OCSP_REQ_CTX *rctx);
 void OCSP_set_max_response_length(OCSP_REQ_CT *rctx, unsigned long len);
 int OCSP_REQ_CTX_set1_req(OCSP_REQ_CTX *rctx, const OCSP_REQUEST *req);

=head1 DESCRIPTION

These functions perform an OCSP POST request / response transfer over HTTP,
using the HTTP request functions described in L<OSSL_HTTP_REQ_CTX(3)>.

The function OCSP_sendreq_new() builds a complete B<OSSL_HTTP_REQ_CTX> structure
with the B<BIO> I<io> to be used for requests and response, the URL path I<path>,
optionally the OCSP request I<req>, and a response header maximum line length
of I<buf_size>. If I<buf_size> is zero a default value of 4KiB is used.
The I<req> may be set to NULL and provided later using OCSP_REQ_CTX_set1_req()
or L<OSSL_HTTP_REQ_CTX_set1_req(3)>.
The I<io> and I<path> arguments to OCSP_sendreq_new() correspond to the
components of the URL.
For example if the responder URL is C<http://example.com/ocspreq> the BIO
I<io> should haven been connected to host C<example.com> on port 80 and I<path>
should be set to C</ocspreq>.

OCSP_sendreq_nbio() attempts to send the request prepared in I<rctx>
and to gather the response via HTTP, using the BIO I<io> and I<path>
that were given when calling OCSP_sendreq_new().
If the operation gets completed it assigns the response,
a pointer to a B<OCSP_RESPONSE> structure, in I<*presp>.
The function may need to be called again if its result is -1, which indicates
L<BIO_should_retry(3)>.  In such a case it is advisable to sleep a little in
between, using L<BIO_wait(3)> on the read BIO to prevent a busy loop.

OCSP_sendreq_bio() combines OCSP_sendreq_new() with as many calls of
OCSP_sendreq_nbio() as needed and then OCSP_REQ_CTX_free(), with a
response header maximum line length 4k. It waits indefinitely on a response.
It does not support setting a timeout or adding headers and is retained
for compatibility; use L<OSSL_HTTP_transfer(3)> instead.

OCSP_REQ_CTX_i2d(rctx, it, req) is equivalent to the following:

  OSSL_HTTP_REQ_CTX_set1_req(rctx, "application/ocsp-request", it, req)

OCSP_REQ_CTX_set1_req(rctx, req) is equivalent to the following:

 OSSL_HTTP_REQ_CTX_set1_req(rctx, "application/ocsp-request",
                            ASN1_ITEM_rptr(OCSP_REQUEST),
                            (const ASN1_VALUE *)req)

The deprecated type and the remaining deprecated functions
have been superseded by the following equivalents:
B<OCSP_REQ_CTX> by L<OSSL_HTTP_REQ_CTX(3)>,
OCSP_REQ_CTX_add1_header() by L<OSSL_HTTP_REQ_CTX_add1_header(3)>,
OCSP_REQ_CTX_free() by L<OSSL_HTTP_REQ_CTX_free(3)>, and
OCSP_set_max_response_length() by
L<OSSL_HTTP_REQ_CTX_set_max_response_length(3)>.

=head1 RETURN VALUES

OCSP_sendreq_new() returns a valid B<OSSL_HTTP_REQ_CTX> structure or NULL
if an error occurred.

OCSP_sendreq_nbio() returns 1 for success, 0 on error, -1 if retry is needed.

OCSP_sendreq_bio() returns the B<OCSP_RESPONSE> structure sent by the
responder or NULL if an error occurred.

=head1 SEE ALSO

L<OSSL_HTTP_REQ_CTX(3)>, L<OSSL_HTTP_transfer(3)>,
L<OCSP_cert_to_id(3)>,
L<OCSP_request_add1_nonce(3)>,
L<OCSP_REQUEST_new(3)>,
L<OCSP_resp_find_status(3)>,
L<OCSP_response_status(3)>

=head1 HISTORY

B<OCSP_REQ_CTX>,
OCSP_REQ_CTX_i2d(),
OCSP_REQ_CTX_add1_header(),
OCSP_REQ_CTX_free(),
OCSP_set_max_response_length(),
and OCSP_REQ_CTX_set1_req()
were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2015-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
