=pod

=head1 NAME

BIO_set_data, BIO_get_data, BIO_set_init, BIO_get_init, BIO_set_shutdown,
BIO_get_shutdown - functions for managing BIO state information

=head1 SYNOPSIS

 #include <openssl/bio.h>

 void BIO_set_data(BIO *a, void *ptr);
 void *BIO_get_data(BIO *a);
 void BIO_set_init(BIO *a, int init);
 int BIO_get_init(BIO *a);
 void BIO_set_shutdown(BIO *a, int shut);
 int BIO_get_shutdown(BIO *a);

=head1 DESCRIPTION

These functions are mainly useful when implementing a custom BIO.

The BIO_set_data() function associates the custom data pointed to by B<ptr> with
the BIO. This data can subsequently be retrieved via a call to BIO_get_data().
This can be used by custom BIOs for storing implementation specific information.

The BIO_set_init() function sets the value of the BIO's "init" flag to indicate
whether initialisation has been completed for this BIO or not. A nonzero value
indicates that initialisation is complete, whilst zero indicates that it is not.
Often initialisation will complete during initial construction of the BIO. For
some BIOs however, initialisation may not complete until after additional steps
have occurred (for example through calling custom ctrls). The BIO_get_init()
function returns the value of the "init" flag.

The BIO_set_shutdown() and BIO_get_shutdown() functions set and get the state of
this BIO's shutdown (i.e. BIO_CLOSE) flag. If set then the underlying resource
is also closed when the BIO is freed.

=head1 RETURN VALUES

BIO_get_data() returns a pointer to the implementation specific custom data
associated with this BIO, or NULL if none has been set.

BIO_get_init() returns the state of the BIO's init flag.

BIO_get_shutdown() returns the stat of the BIO's shutdown (i.e. BIO_CLOSE) flag.

=head1 SEE ALSO

L<bio(7)>, L<BIO_meth_new(3)>

=head1 HISTORY

The functions described here were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
