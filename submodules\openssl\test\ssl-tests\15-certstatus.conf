# Generated with generate_ssl_tests.pl

num_tests = 2

test-0 = 0-certstatus-good
test-1 = 1-certstatus-bad
# ===========================================================

[0-certstatus-good]
ssl_conf = 0-certstatus-good-ssl

[0-certstatus-good-ssl]
server = 0-certstatus-good-server
client = 0-certstatus-good-client

[0-certstatus-good-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-certstatus-good-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success
Method = TLS
server = 0-certstatus-good-server-extra

[0-certstatus-good-server-extra]
CertStatus = GoodResponse


# ===========================================================

[1-certstatus-bad]
ssl_conf = 1-certstatus-bad-ssl

[1-certstatus-bad-ssl]
server = 1-certstatus-bad-server
client = 1-certstatus-bad-client

[1-certstatus-bad-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-certstatus-bad-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = ClientFail
Method = TLS
server = 1-certstatus-bad-server-extra

[1-certstatus-bad-server-extra]
CertStatus = BadResponse


