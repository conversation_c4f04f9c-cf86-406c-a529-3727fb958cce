<?xml version="1.0" encoding="UTF-8"?>
<XPD:PROJECT xmlns:XPD="http://www.staruml.com" version="1">
<XPD:HEADER>
<XPD:SUBUNITS>
</XPD:SUBUNITS>
<XPD:PROFILES>
<XPD:PROFILE>UMLStandard</XPD:PROFILE>
</XPD:PROFILES>
</XPD:HEADER>
<XPD:BODY>
<XPD:OBJ name="DocumentElement" type="UMLProject" guid="eMTM5siBPkmesgn1ExGJ4wAA">
<XPD:ATTR name="Title" type="string">Untitled</XPD:ATTR>
<XPD:ATTR name="#OwnedElements" type="integer">5</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLModel" guid="Y8LpwWcNukWtaRfUBI5ZLgAA">
<XPD:ATTR name="Name" type="string">Use Case Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">useCaseModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLUseCaseDiagram" guid="01F2MJANekqll84kbF5YNQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">Y8LpwWcNukWtaRfUBI5ZLgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLUseCaseDiagramView" guid="Wa78Fn/Bb0SrcCepzNe+hwAA">
<XPD:REF name="Diagram">01F2MJANekqll84kbF5YNQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLModel" guid="IFqLwgQrJUOrRvknlfHP7gAA">
<XPD:ATTR name="Name" type="string">Analysis Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">analysisModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="qf/BpIoQNE2iR+jW/1YKGQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:ATTR name="DiagramType" type="string">RobustnessDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="Nj8POkxtUk2Ybm9uGd6QNwAA">
<XPD:REF name="Diagram">qf/BpIoQNE2iR+jW/1YKGQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedCollaborationInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedCollaborationInstanceSets[0]" type="UMLCollaborationInstanceSet" guid="RJjbkGXgxkyHu7/7jQk7WgAA">
<XPD:ATTR name="Name" type="string">CollaborationInstanceSet1</XPD:ATTR>
<XPD:REF name="RepresentedClassifier">IFqLwgQrJUOrRvknlfHP7gAA</XPD:REF>
<XPD:ATTR name="#InteractionInstanceSets" type="integer">1</XPD:ATTR>
<XPD:OBJ name="InteractionInstanceSets[0]" type="UMLInteractionInstanceSet" guid="z5Jdy9Y9AkuwxNfYTGq/jgAA">
<XPD:ATTR name="Name" type="string">InteractionInstanceSet1</XPD:ATTR>
<XPD:REF name="Context">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLSequenceDiagram" guid="PrarycmgpEmAgPsPTXgTdAAA">
<XPD:ATTR name="Name" type="string">SequenceDiagram</XPD:ATTR>
<XPD:REF name="DiagramOwner">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLSequenceDiagramView" guid="o16wrrjNA0qa+l2Fh/S4lgAA">
<XPD:REF name="Diagram">PrarycmgpEmAgPsPTXgTdAAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">21</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLSeqObjectView" guid="hPmViqUBXkyp20XJo5hMdgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">164</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1283</XPD:ATTR>
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="fAggghDBvEq/ulCEeW7p1wAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="J8Tym9syak6jMb998m2XnAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">app</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="7tw+uaP9y0KlpsxVOPDIWwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="L08Ynzs4RUePB4dOSv8aiQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="+2RX1PWkxkmtGMjAQcMkiwAA">
<XPD:REF name="Model">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[1]" type="UMLSeqObjectView" guid="QJ/ol6Ii20Cm5p+rCS0bnQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">408</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">124</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1315</XPD:ATTR>
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="T6OBOo60Q0W4cqZXEbC1RgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="pY6xEFcwOE+9xyDGKflCfAAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">worker</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="RGPC07sgskGBwg2p/ygfqwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="ktdSB1Q4R0mcUr92wegLdAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="PpgPcPMDNk2aqxVrk8rFfAAA">
<XPD:REF name="Model">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[2]" type="UMLSeqObjectView" guid="lDUJf8C6fEyt4j0+bqluBgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">632</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">70</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1319</XPD:ATTR>
<XPD:REF name="Model">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="8iotUIgixE62ZDVmD3Z3fQAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="Kb2lMHEsEk2Cuq5svpZgwQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">timer</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="aEV4eOOZU0mHkw75pExuOgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="uGzZliJVtkG5NM6OsR+hEQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="ZEuU64rQXku57sf2f119/wAA">
<XPD:REF name="Model">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[3]" type="UMLSeqObjectView" guid="PnRdfgtnekueMRegs48acQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">844</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">102</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1277</XPD:ATTR>
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="wa79nq7jfkyCxJ9WKgFXjgAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="ADa8z/SUOUquP1daSz7gqQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">platform</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kJ/gDpz3j0ee3eR61+pEaQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="OMFhMMF220KlJGqxz1ec2QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="utuP5e+xFEmMGXpPHYF8ogAA">
<XPD:REF name="Model">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[4]" type="UMLSeqObjectView" guid="33q5j2m07U66Vqancw44YQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">1048</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">48</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">75</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">1269</XPD:ATTR>
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="3uXfUgftWE2YaJbZSLhjRwAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="6hCBIMsdDkKV2jSutLPPxQAA">
<XPD:ATTR name="FontStyle" type="integer">4</XPD:ATTR>
<XPD:ATTR name="Text" type="string">connection</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="kyEWL0SI2EqTC4V3i20LggAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="qr6WtEdGD0erGJ2d4gw6swAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="LifeLine" type="UMLLifeLineView" guid="5qlq1+nrd0+/KejphUw97gAA">
<XPD:REF name="Model">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[5]" type="UMLSeqStimulusView" guid="z/BF72dr4kuVb3dOKjH8ZgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,106;463,106</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="mJgFFVIbl0Kb4O4Re+5yUAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">1 : QuicWorkerPoolInitializepool()</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="GbIXvH8SkUqsZolSq95kowAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="S90I5wQEs0aBXCatMG/TkwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="HostEdge">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="S9pKVUTolkyAH+KCx8eGtQAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">106</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[6]" type="UMLSeqStimulusView" guid="wDVcYA9+x0WLoJPnxPXPkgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,146;888,146</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="dD9eiSncVEmuhy4t5tJ6KwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">2 : CxPlatWorkerPoolInit()</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="kVlo/+eDg02oTd+nY1yxqAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="BslqtKAHZUmD1gQXEPMWCAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="HostEdge">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="ECuN9eY+ukGrE26n49Dw3gAA">
<XPD:ATTR name="Left" type="integer">888</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">146</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[7]" type="UMLSeqStimulusView" guid="OyRt5dyo8EW9ttjl4R31XQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,196;660,196</XPD:ATTR>
<XPD:REF name="Model">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="PZK0Fr7sGUGeubud7SR0ywAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">3 : QuicTimerWheelInitialize()</XPD:ATTR>
<XPD:REF name="Model">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="HostEdge">OyRt5dyo8EW9ttjl4R31XQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="DD8poTq6vUuAfUCMrVf0wAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="HostEdge">OyRt5dyo8EW9ttjl4R31XQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="C8nIoc95Z0GPFcMBJ4MjigAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="HostEdge">OyRt5dyo8EW9ttjl4R31XQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="eTte8thLZ0ac8H7SCNS6eAAA">
<XPD:ATTR name="Left" type="integer">660</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">196</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[8]" type="UMLSeqStimulusView" guid="hzFWW/e1zEKEq5yQxGfdswAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,222;463,222</XPD:ATTR>
<XPD:REF name="Model">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="7nV7gqBlnEKuwy308NSqwwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">4 : QuicWorkerAssignConnection()</XPD:ATTR>
<XPD:REF name="Model">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="HostEdge">hzFWW/e1zEKEq5yQxGfdswAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="DfJlbNE/2Eyqwksm/UkW8gAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="HostEdge">hzFWW/e1zEKEq5yQxGfdswAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="o3chl0GN4kWaJQRoH4XI0AAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="HostEdge">hzFWW/e1zEKEq5yQxGfdswAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="tQNmBMfUN0iOeY+nuSX1sAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">222</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[9]" type="UMLSeqStimulusView" guid="T+zyidx5dUOoQTbQegqgIgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,284;463,284</XPD:ATTR>
<XPD:REF name="Model">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="CJ65IuOlFUa00KnyZJuJDQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">5 : QuicConnQueueOper()</XPD:ATTR>
<XPD:REF name="Model">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="HostEdge">T+zyidx5dUOoQTbQegqgIgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="c1jR/LXx7UGvfhzbCoiK9gAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="HostEdge">T+zyidx5dUOoQTbQegqgIgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="h2WWSD06jE+6WtdSQkHMvwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="HostEdge">T+zyidx5dUOoQTbQegqgIgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="DUJDF8Z2z02RUl006pmghAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">284</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[10]" type="UMLSeqStimulusView" guid="/7H97BiWPEimjQYp8AwQggAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,328;500,328;500,348;476,348</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="WJqHlqRE0UCmK5TESEI9fQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">6 : QuicWorkerLoop()</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="6ABOwJaydkeMEGyOfuP4DwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="TLzxye8lR0ywxzgHukxEfwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="HostEdge">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="kDGxe6QFDEyocFGYNatesAAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">348</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[11]" type="UMLSeqStimulusView" guid="FiOZxZQg0UyS/Q6IpIeLqgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,404;660,404</XPD:ATTR>
<XPD:REF name="Model">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="Iqn24syWvEuND5PFmI8f2wAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">7 : QuicTimerWheelUpdateConnection()</XPD:ATTR>
<XPD:REF name="Model">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="HostEdge">FiOZxZQg0UyS/Q6IpIeLqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="EBFv/yncykGkr8ECgB7/qQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="HostEdge">FiOZxZQg0UyS/Q6IpIeLqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="NXag5hue6kqUaxQiF5VeDQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="HostEdge">FiOZxZQg0UyS/Q6IpIeLqgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="eRZ2j/UP70WqMLMICAkMNwAA">
<XPD:ATTR name="Left" type="integer">660</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">404</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[12]" type="UMLSeqStimulusView" guid="PUG0qINYvkqbN21bv1vuEwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,461;1078,461</XPD:ATTR>
<XPD:REF name="Model">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="2+0XEMx5PECY0nk50eCDLwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">8 : QuicConnDrainOperations()</XPD:ATTR>
<XPD:REF name="Model">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="HostEdge">PUG0qINYvkqbN21bv1vuEwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="TDNjLL0hA0Oo1l7vGTitiAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="HostEdge">PUG0qINYvkqbN21bv1vuEwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="Clx98WqtVkyFrqIIe4lXrgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="HostEdge">PUG0qINYvkqbN21bv1vuEwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="c4tlDObWhkul2sq9hTCmSAAA">
<XPD:ATTR name="Left" type="integer">1078</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">461</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[13]" type="UMLSeqStimulusView" guid="lsdUv5gxI0GnPr8A65TKqAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,486;500,486;500,506;476,506</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="B6lJgjnH+UmL+ypZjbtGDAAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">9 : QuicWorkerLoop()</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="YOf9RPv5f0WGx1XsPe5ZaQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="QWMFLpCu2E6eOqWYlJqTDgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="HostEdge">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="ZwuiHcjQPUun/x84OfHYvwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">506</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[14]" type="UMLSeqStimulusView" guid="yLywg+JEfkWgNYTOXU/V7wAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,556;660,556</XPD:ATTR>
<XPD:REF name="Model">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="4S8gWka9vke7vTwskfwy9wAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">10 : QuicWorkerProcessTimers()</XPD:ATTR>
<XPD:REF name="Model">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="HostEdge">yLywg+JEfkWgNYTOXU/V7wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="cASL9vEIOUKHZoi6d8B1PQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="HostEdge">yLywg+JEfkWgNYTOXU/V7wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="xLDUJ5yuykKookVPa+3h+wAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="HostEdge">yLywg+JEfkWgNYTOXU/V7wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="ipzBq2/pKUGp7Ajf4+t0sgAA">
<XPD:ATTR name="Left" type="integer">660</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">556</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[15]" type="UMLSeqStimulusView" guid="iGSpP4MFoEW508/4DQbUHQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">667,604;476,604</XPD:ATTR>
<XPD:REF name="Model">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="Uk9zQMAT1EWh1FpnnspJoQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">11 : QuicConnQueueOper:QUIC_OPER_TYPE_TIMER_EXPIRED()</XPD:ATTR>
<XPD:REF name="Model">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:REF name="HostEdge">iGSpP4MFoEW508/4DQbUHQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="ndvyiIiw8EWSdywyCv5JGAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:REF name="HostEdge">iGSpP4MFoEW508/4DQbUHQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="GuxStQ/Y9UeubE6iQJ4GwAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:REF name="HostEdge">iGSpP4MFoEW508/4DQbUHQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="mIoTSZG4TEeNDAa0BLDb1QAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">604</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[16]" type="UMLSeqStimulusView" guid="yFN/J8EdIEKS0PitReVACAAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,668;1078,668</XPD:ATTR>
<XPD:REF name="Model">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
<XPD:REF name="Head">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="yDxtALW5Akqc3zYPjZ7oUgAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">12 : QuicConnProcessExpiredTimer()</XPD:ATTR>
<XPD:REF name="Model">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
<XPD:REF name="HostEdge">yFN/J8EdIEKS0PitReVACAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="FnvkgGhpHUSBYGMCheX7hwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
<XPD:REF name="HostEdge">yFN/J8EdIEKS0PitReVACAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="iwc5uuEs7UGD9dXCYeEteQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
<XPD:REF name="HostEdge">yFN/J8EdIEKS0PitReVACAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="WNDFhDyiakilcWGN5GGdBwAA">
<XPD:ATTR name="Left" type="integer">1078</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">668</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[17]" type="UMLSeqStimulusView" guid="yXBtsB5bUE6Qi4pqj1dpEQAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">199,708;463,708</XPD:ATTR>
<XPD:REF name="Model">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="JiDQmT792ke6A6884Jms7QAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">13 : QuicWorkerPoolUninitialize()</XPD:ATTR>
<XPD:REF name="Model">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
<XPD:REF name="HostEdge">yXBtsB5bUE6Qi4pqj1dpEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="fLLjoPDWNUqbhSoyu5p1TAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
<XPD:REF name="HostEdge">yXBtsB5bUE6Qi4pqj1dpEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="oZHEWmTRkU+gjtGeRBORrAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
<XPD:REF name="HostEdge">yXBtsB5bUE6Qi4pqj1dpEQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="l7l6RUZ8lkeVL1CuyAjgVwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">708</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[18]" type="UMLSeqStimulusView" guid="1q9WKnroFkyBiwFF9JhIfwAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,744;500,744;500,764;476,764</XPD:ATTR>
<XPD:REF name="Model">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
<XPD:REF name="Head">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="5aXXUh44iUa95jSy96TVOwAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">14 : QuicWorkerUninitialize()</XPD:ATTR>
<XPD:REF name="Model">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
<XPD:REF name="HostEdge">1q9WKnroFkyBiwFF9JhIfwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="cD15TEVMtUOClRrqrj1JsQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
<XPD:REF name="HostEdge">1q9WKnroFkyBiwFF9JhIfwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="a/FFNFDnxk+KuGGIpAdoyAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
<XPD:REF name="HostEdge">1q9WKnroFkyBiwFF9JhIfwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="HcWTBkMj+0yr8s+qizKHQwAA">
<XPD:ATTR name="Left" type="integer">463</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">764</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[19]" type="UMLSeqStimulusView" guid="sGCp5Hv/FEK5E/yt68Lk+wAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,808;888,808</XPD:ATTR>
<XPD:REF name="Model">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
<XPD:REF name="Head">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="UwRD+FGwDUq6FUhscVED5gAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">15 : CxPlatPoolUninitialize()</XPD:ATTR>
<XPD:REF name="Model">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
<XPD:REF name="HostEdge">sGCp5Hv/FEK5E/yt68Lk+wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="F8iYMr+z0UGsJN/hOYrL8QAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
<XPD:REF name="HostEdge">sGCp5Hv/FEK5E/yt68Lk+wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="dsojbZJmKECFB4ykwrdDdAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
<XPD:REF name="HostEdge">sGCp5Hv/FEK5E/yt68Lk+wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="DH55+6atT0OWgknwOpauKgAA">
<XPD:ATTR name="Left" type="integer">888</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">808</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedViews[20]" type="UMLSeqStimulusView" guid="E5jSZzuiLUS3CjFqKSiV8gAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="LineStyle" type="LineStyleKind">lsRectilinear</XPD:ATTR>
<XPD:ATTR name="Points" type="Points">470,844;660,844</XPD:ATTR>
<XPD:REF name="Model">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
<XPD:REF name="Head">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:REF name="Tail">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:OBJ name="NameLabel" type="EdgeLabelView" guid="pnIK5QuafUyD9l47YgbLLQAA">
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:ATTR name="Text" type="string">16 : QuicTimerWheelUninitialize()</XPD:ATTR>
<XPD:REF name="Model">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
<XPD:REF name="HostEdge">E5jSZzuiLUS3CjFqKSiV8gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="EdgeLabelView" guid="4dkwp+3o+0uOuevtxo6mDwAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">25</XPD:ATTR>
<XPD:REF name="Model">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
<XPD:REF name="HostEdge">E5jSZzuiLUS3CjFqKSiV8gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="EdgeLabelView" guid="kvHeLOhfbEa7YxPiKCMkJAAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:ATTR name="Alpha" type="real">-1.5707963267949</XPD:ATTR>
<XPD:ATTR name="Distance" type="real">10</XPD:ATTR>
<XPD:REF name="Model">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
<XPD:REF name="HostEdge">E5jSZzuiLUS3CjFqKSiV8gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Activation" type="UMLActivationView" guid="1n7XP1hWAk2+UxsQMHXarwAA">
<XPD:ATTR name="Left" type="integer">660</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">844</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">14</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">29</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingStimuli" type="integer">16</XPD:ATTR>
<XPD:OBJ name="ParticipatingStimuli[0]" type="UMLStimulus" guid="2STyiL6wBkSrtaDfatb64wAA">
<XPD:ATTR name="Name" type="string">QuicWorkerPoolInitializepool</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="FCqbeum8pkKP4j3ksd+iwwAA">
<XPD:REF name="Stimulus">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">z/BF72dr4kuVb3dOKjH8ZgAA</XPD:REF>
<XPD:REF name="Views[1]">mJgFFVIbl0Kb4O4Re+5yUAAA</XPD:REF>
<XPD:REF name="Views[2]">GbIXvH8SkUqsZolSq95kowAA</XPD:REF>
<XPD:REF name="Views[3]">S90I5wQEs0aBXCatMG/TkwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[1]" type="UMLStimulus" guid="VHIVhFa2PkGbARAlXReRbgAA">
<XPD:ATTR name="Name" type="string">CxPlatWorkerPoolInit</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="RjkQp/Xygke/OUxbcoTWsAAA">
<XPD:REF name="Stimulus">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">wDVcYA9+x0WLoJPnxPXPkgAA</XPD:REF>
<XPD:REF name="Views[1]">dD9eiSncVEmuhy4t5tJ6KwAA</XPD:REF>
<XPD:REF name="Views[2]">kVlo/+eDg02oTd+nY1yxqAAA</XPD:REF>
<XPD:REF name="Views[3]">BslqtKAHZUmD1gQXEPMWCAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[2]" type="UMLStimulus" guid="qYFE+gwSeE2E5lBjDoLpsgAA">
<XPD:ATTR name="Name" type="string">QuicTimerWheelInitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="7l4s/3Fey0ygkz3ZISJCyAAA">
<XPD:REF name="Stimulus">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">OyRt5dyo8EW9ttjl4R31XQAA</XPD:REF>
<XPD:REF name="Views[1]">PZK0Fr7sGUGeubud7SR0ywAA</XPD:REF>
<XPD:REF name="Views[2]">DD8poTq6vUuAfUCMrVf0wAAA</XPD:REF>
<XPD:REF name="Views[3]">C8nIoc95Z0GPFcMBJ4MjigAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[3]" type="UMLStimulus" guid="kW1+5dXRYEm6Lqf2RDmN1gAA">
<XPD:ATTR name="Name" type="string">QuicWorkerAssignConnection</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="uxVVjRT10U2NcEzXOOI8agAA">
<XPD:REF name="Stimulus">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">hzFWW/e1zEKEq5yQxGfdswAA</XPD:REF>
<XPD:REF name="Views[1]">7nV7gqBlnEKuwy308NSqwwAA</XPD:REF>
<XPD:REF name="Views[2]">DfJlbNE/2Eyqwksm/UkW8gAA</XPD:REF>
<XPD:REF name="Views[3]">o3chl0GN4kWaJQRoH4XI0AAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[4]" type="UMLStimulus" guid="ej+VtdGLwUaUdQZ7mXmRvQAA">
<XPD:ATTR name="Name" type="string">QuicConnQueueOper</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="kJK8fXC13EKSpGyVZknPyAAA">
<XPD:REF name="Stimulus">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">T+zyidx5dUOoQTbQegqgIgAA</XPD:REF>
<XPD:REF name="Views[1]">CJ65IuOlFUa00KnyZJuJDQAA</XPD:REF>
<XPD:REF name="Views[2]">c1jR/LXx7UGvfhzbCoiK9gAA</XPD:REF>
<XPD:REF name="Views[3]">h2WWSD06jE+6WtdSQkHMvwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[5]" type="UMLStimulus" guid="hL965ZJIfk2bvrDdeWudywAA">
<XPD:ATTR name="Name" type="string">QuicWorkerLoop</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="GqbR4o+WNU2g1I2plHvR2QAA">
<XPD:REF name="Stimulus">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">/7H97BiWPEimjQYp8AwQggAA</XPD:REF>
<XPD:REF name="Views[1]">WJqHlqRE0UCmK5TESEI9fQAA</XPD:REF>
<XPD:REF name="Views[2]">6ABOwJaydkeMEGyOfuP4DwAA</XPD:REF>
<XPD:REF name="Views[3]">TLzxye8lR0ywxzgHukxEfwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[6]" type="UMLStimulus" guid="3ogauuK8A0q0xqbfJKnyfAAA">
<XPD:ATTR name="Name" type="string">QuicTimerWheelUpdateConnection</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="seanX9IvaE6mOniR3vXVXwAA">
<XPD:REF name="Stimulus">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">FiOZxZQg0UyS/Q6IpIeLqgAA</XPD:REF>
<XPD:REF name="Views[1]">Iqn24syWvEuND5PFmI8f2wAA</XPD:REF>
<XPD:REF name="Views[2]">EBFv/yncykGkr8ECgB7/qQAA</XPD:REF>
<XPD:REF name="Views[3]">NXag5hue6kqUaxQiF5VeDQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[7]" type="UMLStimulus" guid="dBF8ildlP02NhwJwNhJ3PwAA">
<XPD:ATTR name="Name" type="string">QuicConnDrainOperations</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="u+DwMHhQJkuI3BTFu8aw2AAA">
<XPD:REF name="Stimulus">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">PUG0qINYvkqbN21bv1vuEwAA</XPD:REF>
<XPD:REF name="Views[1]">2+0XEMx5PECY0nk50eCDLwAA</XPD:REF>
<XPD:REF name="Views[2]">TDNjLL0hA0Oo1l7vGTitiAAA</XPD:REF>
<XPD:REF name="Views[3]">Clx98WqtVkyFrqIIe4lXrgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[8]" type="UMLStimulus" guid="FobcVtHgj0KB0KW/lmhxhwAA">
<XPD:ATTR name="Name" type="string">QuicWorkerLoop</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="xnGCScOsA0yKWRKyezzT9wAA">
<XPD:REF name="Stimulus">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">lsdUv5gxI0GnPr8A65TKqAAA</XPD:REF>
<XPD:REF name="Views[1]">B6lJgjnH+UmL+ypZjbtGDAAA</XPD:REF>
<XPD:REF name="Views[2]">YOf9RPv5f0WGx1XsPe5ZaQAA</XPD:REF>
<XPD:REF name="Views[3]">QWMFLpCu2E6eOqWYlJqTDgAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[9]" type="UMLStimulus" guid="wfpWf+MJRUaxdW8UaVdTFgAA">
<XPD:ATTR name="Name" type="string">QuicWorkerProcessTimers</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="+nI4QyHSf0SUHMW6I1LuFgAA">
<XPD:REF name="Stimulus">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">yLywg+JEfkWgNYTOXU/V7wAA</XPD:REF>
<XPD:REF name="Views[1]">4S8gWka9vke7vTwskfwy9wAA</XPD:REF>
<XPD:REF name="Views[2]">cASL9vEIOUKHZoi6d8B1PQAA</XPD:REF>
<XPD:REF name="Views[3]">xLDUJ5yuykKookVPa+3h+wAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[10]" type="UMLStimulus" guid="8BKWmImsnUieXCc9p8+C5QAA">
<XPD:ATTR name="Name" type="string">QuicConnQueueOper:QUIC_OPER_TYPE_TIMER_EXPIRED</XPD:ATTR>
<XPD:REF name="Sender">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="dAJt/3bfIkix7atkiSA9RAAA">
<XPD:REF name="Stimulus">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">iGSpP4MFoEW508/4DQbUHQAA</XPD:REF>
<XPD:REF name="Views[1]">Uk9zQMAT1EWh1FpnnspJoQAA</XPD:REF>
<XPD:REF name="Views[2]">ndvyiIiw8EWSdywyCv5JGAAA</XPD:REF>
<XPD:REF name="Views[3]">GuxStQ/Y9UeubE6iQJ4GwAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[11]" type="UMLStimulus" guid="u9I/IZE+xU2JRh72QkaQagAA">
<XPD:ATTR name="Name" type="string">QuicConnProcessExpiredTimer</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">x4GPIF5LNU6b9TXPe2occgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="uSsSkJ5EdkCzME3FOgR2ggAA">
<XPD:REF name="Stimulus">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">yFN/J8EdIEKS0PitReVACAAA</XPD:REF>
<XPD:REF name="Views[1]">yDxtALW5Akqc3zYPjZ7oUgAA</XPD:REF>
<XPD:REF name="Views[2]">FnvkgGhpHUSBYGMCheX7hwAA</XPD:REF>
<XPD:REF name="Views[3]">iwc5uuEs7UGD9dXCYeEteQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[12]" type="UMLStimulus" guid="OGp3fHryYU+jxnAfb8Sp2AAA">
<XPD:ATTR name="Name" type="string">QuicWorkerPoolUninitialize</XPD:ATTR>
<XPD:REF name="Sender">B2op3V8XQ0eesPz/zKkoBQAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="ysp3uZ79DkCvpTg3aTL3JAAA">
<XPD:REF name="Stimulus">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">yXBtsB5bUE6Qi4pqj1dpEQAA</XPD:REF>
<XPD:REF name="Views[1]">JiDQmT792ke6A6884Jms7QAA</XPD:REF>
<XPD:REF name="Views[2]">fLLjoPDWNUqbhSoyu5p1TAAA</XPD:REF>
<XPD:REF name="Views[3]">oZHEWmTRkU+gjtGeRBORrAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[13]" type="UMLStimulus" guid="I4o4cNUWrUGohgQgAnToLwAA">
<XPD:ATTR name="Name" type="string">QuicWorkerUninitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="JuI3RVE1Skeip5DODAnZAQAA">
<XPD:REF name="Stimulus">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">1q9WKnroFkyBiwFF9JhIfwAA</XPD:REF>
<XPD:REF name="Views[1]">5aXXUh44iUa95jSy96TVOwAA</XPD:REF>
<XPD:REF name="Views[2]">cD15TEVMtUOClRrqrj1JsQAA</XPD:REF>
<XPD:REF name="Views[3]">a/FFNFDnxk+KuGGIpAdoyAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[14]" type="UMLStimulus" guid="jyB7r5EEl06Dy2hUwjjFyQAA">
<XPD:ATTR name="Name" type="string">CxPlatPoolUninitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">xMzoSDbqnEK0EOuKrFyoJQAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="5vszgOvMbkyn0xNNblh+xQAA">
<XPD:REF name="Stimulus">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">sGCp5Hv/FEK5E/yt68Lk+wAA</XPD:REF>
<XPD:REF name="Views[1]">UwRD+FGwDUq6FUhscVED5gAA</XPD:REF>
<XPD:REF name="Views[2]">F8iYMr+z0UGsJN/hOYrL8QAA</XPD:REF>
<XPD:REF name="Views[3]">dsojbZJmKECFB4ykwrdDdAAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingStimuli[15]" type="UMLStimulus" guid="Ba6WGJ5w0ku26s13rS4oGQAA">
<XPD:ATTR name="Name" type="string">QuicTimerWheelUninitialize</XPD:ATTR>
<XPD:REF name="Sender">aRekjOsrRkGxXeUMaRdEOgAA</XPD:REF>
<XPD:REF name="Receiver">An+R1x1Jqk6mV8IzyMPWvwAA</XPD:REF>
<XPD:OBJ name="Action" type="UMLCallAction" guid="XQNk8PNCR0CncT/xw73lBwAA">
<XPD:REF name="Stimulus">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
</XPD:OBJ>
<XPD:REF name="InteractionInstanceSet">z5Jdy9Y9AkuwxNfYTGq/jgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">E5jSZzuiLUS3CjFqKSiV8gAA</XPD:REF>
<XPD:REF name="Views[1]">pnIK5QuafUyD9l47YgbLLQAA</XPD:REF>
<XPD:REF name="Views[2]">4dkwp+3o+0uOuevtxo6mDwAA</XPD:REF>
<XPD:REF name="Views[3]">kvHeLOhfbEa7YxPiKCMkJAAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#ParticipatingInstances" type="integer">6</XPD:ATTR>
<XPD:OBJ name="ParticipatingInstances[0]" type="UMLObject" guid="B2op3V8XQ0eesPz/zKkoBQAA">
<XPD:ATTR name="Name" type="string">app</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">hPmViqUBXkyp20XJo5hMdgAA</XPD:REF>
<XPD:REF name="Views[1]">+2RX1PWkxkmtGMjAQcMkiwAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[1]" type="UMLObject" guid="aRekjOsrRkGxXeUMaRdEOgAA">
<XPD:ATTR name="Name" type="string">worker</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">QJ/ol6Ii20Cm5p+rCS0bnQAA</XPD:REF>
<XPD:REF name="Views[1]">PpgPcPMDNk2aqxVrk8rFfAAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">11</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="SendingStimuli[1]">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="SendingStimuli[2]">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="SendingStimuli[3]">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="SendingStimuli[4]">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="SendingStimuli[5]">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="SendingStimuli[6]">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="SendingStimuli[7]">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
<XPD:REF name="SendingStimuli[8]">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
<XPD:REF name="SendingStimuli[9]">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
<XPD:REF name="SendingStimuli[10]">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">8</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">2STyiL6wBkSrtaDfatb64wAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">kW1+5dXRYEm6Lqf2RDmN1gAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">ej+VtdGLwUaUdQZ7mXmRvQAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">hL965ZJIfk2bvrDdeWudywAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[4]">FobcVtHgj0KB0KW/lmhxhwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[5]">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[6]">OGp3fHryYU+jxnAfb8Sp2AAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[7]">I4o4cNUWrUGohgQgAnToLwAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[2]" type="UMLObject" guid="An+R1x1Jqk6mV8IzyMPWvwAA">
<XPD:ATTR name="Name" type="string">timer</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">lDUJf8C6fEyt4j0+bqluBgAA</XPD:REF>
<XPD:REF name="Views[1]">ZEuU64rQXku57sf2f119/wAA</XPD:REF>
<XPD:ATTR name="#SendingStimuli" type="integer">1</XPD:ATTR>
<XPD:REF name="SendingStimuli[0]">8BKWmImsnUieXCc9p8+C5QAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">4</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">qYFE+gwSeE2E5lBjDoLpsgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">3ogauuK8A0q0xqbfJKnyfAAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[2]">wfpWf+MJRUaxdW8UaVdTFgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[3]">Ba6WGJ5w0ku26s13rS4oGQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[3]" type="UMLObject" guid="xMzoSDbqnEK0EOuKrFyoJQAA">
<XPD:ATTR name="Name" type="string">platform</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">PnRdfgtnekueMRegs48acQAA</XPD:REF>
<XPD:REF name="Views[1]">utuP5e+xFEmMGXpPHYF8ogAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">2</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">VHIVhFa2PkGbARAlXReRbgAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">jyB7r5EEl06Dy2hUwjjFyQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[4]" type="UMLObject" guid="x4GPIF5LNU6b9TXPe2occgAA">
<XPD:ATTR name="Name" type="string">connection</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">2</XPD:ATTR>
<XPD:REF name="Views[0]">33q5j2m07U66Vqancw44YQAA</XPD:REF>
<XPD:REF name="Views[1]">5qlq1+nrd0+/KejphUw97gAA</XPD:REF>
<XPD:ATTR name="#ReceivingStimuli" type="integer">2</XPD:ATTR>
<XPD:REF name="ReceivingStimuli[0]">dBF8ildlP02NhwJwNhJ3PwAA</XPD:REF>
<XPD:REF name="ReceivingStimuli[1]">u9I/IZE+xU2JRh72QkaQagAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="ParticipatingInstances[5]" type="UMLObject" guid="YHPCGABMdUOkz/1EAH7BiAAA">
<XPD:ATTR name="Name" type="string">stream</XPD:ATTR>
<XPD:REF name="CollaborationInstanceSet">RJjbkGXgxkyHu7/7jQk7WgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLModel" guid="md+fvOlUm0OVXhlV9hGlZQAA">
<XPD:ATTR name="Name" type="string">Design Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">designModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLClassDiagram" guid="m4ZRdEIg30uEQ1AKDhRiwwAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:ATTR name="DefaultDiagram" type="boolean">True</XPD:ATTR>
<XPD:REF name="DiagramOwner">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLClassDiagramView" guid="+WcODUTEa0yzWzWZozxWSgAA">
<XPD:REF name="Diagram">m4ZRdEIg30uEQ1AKDhRiwwAA</XPD:REF>
<XPD:ATTR name="#OwnedViews" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedViews[0]" type="UMLClassView" guid="Z/m+NTtYakCizo+ZJ6PRwgAA">
<XPD:ATTR name="LineColor" type="string">clMaroon</XPD:ATTR>
<XPD:ATTR name="FillColor" type="string">$00B9FFFF</XPD:ATTR>
<XPD:ATTR name="Left" type="integer">356</XPD:ATTR>
<XPD:ATTR name="Top" type="integer">120</XPD:ATTR>
<XPD:ATTR name="Width" type="integer">213</XPD:ATTR>
<XPD:ATTR name="Height" type="integer">95</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:OBJ name="NameCompartment" type="UMLNameCompartmentView" guid="18l4ty/7JkqfwF849znu2AAA">
<XPD:OBJ name="NameLabel" type="LabelView" guid="EvRVNDul20Ol+Qv55pYvzwAA">
<XPD:ATTR name="FontStyle" type="integer">1</XPD:ATTR>
<XPD:ATTR name="Text" type="string">library.c</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="StereotypeLabel" type="LabelView" guid="lx49Ske3uEW4bZVaL9fufgAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
<XPD:OBJ name="PropertyLabel" type="LabelView" guid="cG52dQe9wEmS0zCw7F/UXQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="AttributeCompartment" type="UMLAttributeCompartmentView" guid="gKdcDpRpOkWcTxL/ZidPdQAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OperationCompartment" type="UMLOperationCompartmentView" guid="YXSBrfWAQUug1BGXKiQxtgAA">
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="TemplateParameterCompartment" type="UMLTemplateParameterCompartmentView" guid="URf16QRAeUSOmCYkUK1naQAA">
<XPD:ATTR name="Visible" type="boolean">False</XPD:ATTR>
<XPD:REF name="Model">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:ATTR name="#OwnedElements" type="integer">3</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLClass" guid="JBFMD0tAmUOBm1nrDpCX0gAA">
<XPD:ATTR name="Name" type="string">library.c</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
<XPD:ATTR name="#Views" type="integer">4</XPD:ATTR>
<XPD:REF name="Views[0]">Z/m+NTtYakCizo+ZJ6PRwgAA</XPD:REF>
<XPD:REF name="Views[1]">gKdcDpRpOkWcTxL/ZidPdQAA</XPD:REF>
<XPD:REF name="Views[2]">YXSBrfWAQUug1BGXKiQxtgAA</XPD:REF>
<XPD:REF name="Views[3]">URf16QRAeUSOmCYkUK1naQAA</XPD:REF>
<XPD:ATTR name="#OwnedElements" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedElements[0]" type="UMLInterface" guid="JT2S8CoRJU2JIea3BkEKiQAA">
<XPD:ATTR name="Name" type="string">Kim, Jeongil</XPD:ATTR>
<XPD:REF name="Namespace">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:ATTR name="#Operations" type="integer">4</XPD:ATTR>
<XPD:OBJ name="Operations[0]" type="UMLOperation" guid="99SPX+XZhEeX5Yg+VJEl/QAA">
<XPD:ATTR name="Name" type="string">MsQuicOpen2</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="npZ9vV5aB0yTW0bFO/W0WgAA">
<XPD:ATTR name="Name" type="string">const void** QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">99SPX+XZhEeX5Yg+VJEl/QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[1]" type="UMLOperation" guid="S/AXFe+Ro0mWFJthXDGG0QAA">
<XPD:ATTR name="Name" type="string">MsQuicClose</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
<XPD:ATTR name="#Parameters" type="integer">1</XPD:ATTR>
<XPD:OBJ name="Parameters[0]" type="UMLParameter" guid="9l+2IKGa3kqRetrsHcZ5EAAA">
<XPD:ATTR name="Name" type="string">const void* QuicApi</XPD:ATTR>
<XPD:REF name="BehavioralFeature">S/AXFe+Ro0mWFJthXDGG0QAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="Operations[2]" type="UMLOperation" guid="FQAUYxQlNkCWUkFLWggmrgAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryLoad</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="Operations[3]" type="UMLOperation" guid="XdHB1mkRzkqLeSO9Vtc35QAA">
<XPD:ATTR name="Name" type="string">MsQuicLibraryUninitialize</XPD:ATTR>
<XPD:REF name="Owner">JBFMD0tAmUOBm1nrDpCX0gAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[1]" type="UMLClass" guid="22zjv9K1y0aFzy8lJRR/KAAA">
<XPD:ATTR name="Name" type="string">Kum, Deukkyu</XPD:ATTR>
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[2]" type="UMLClass" guid="5oI76i2ZiUeFVSPnXc+4OwAA">
<XPD:REF name="Namespace">md+fvOlUm0OVXhlV9hGlZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[3]" type="UMLModel" guid="VFDhfGg3iE6vmYXwAyC6fAAA">
<XPD:ATTR name="Name" type="string">Implementation Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">implementationModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLComponentDiagram" guid="O/DoalvzX0Skw2gFKWB5ZQAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">VFDhfGg3iE6vmYXwAyC6fAAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLComponentDiagramView" guid="4Ny79qmdTESrEmvxndV+BAAA">
<XPD:REF name="Diagram">O/DoalvzX0Skw2gFKWB5ZQAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
<XPD:OBJ name="OwnedElements[4]" type="UMLModel" guid="xmqKmxOf5EmOkrtUe6SHogAA">
<XPD:ATTR name="Name" type="string">Deployment Model</XPD:ATTR>
<XPD:ATTR name="StereotypeProfile" type="string">UMLStandard</XPD:ATTR>
<XPD:ATTR name="StereotypeName" type="string">deploymentModel</XPD:ATTR>
<XPD:REF name="Namespace">eMTM5siBPkmesgn1ExGJ4wAA</XPD:REF>
<XPD:ATTR name="#OwnedDiagrams" type="integer">1</XPD:ATTR>
<XPD:OBJ name="OwnedDiagrams[0]" type="UMLDeploymentDiagram" guid="kW6AlkIWSUW41ygNV3gWHgAA">
<XPD:ATTR name="Name" type="string">Main</XPD:ATTR>
<XPD:REF name="DiagramOwner">xmqKmxOf5EmOkrtUe6SHogAA</XPD:REF>
<XPD:OBJ name="DiagramView" type="UMLDeploymentDiagramView" guid="Hpb7DSjYcUCpDIAkjRJJLAAA">
<XPD:REF name="Diagram">kW6AlkIWSUW41ygNV3gWHgAA</XPD:REF>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:OBJ>
</XPD:BODY>
</XPD:PROJECT>
