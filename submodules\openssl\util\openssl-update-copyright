#!/usr/bin/env bash
#
# Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the OpenSSL license (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


myname="$(basename $0)"

this_year="$(date '+%Y')"
some_year="[12][0-9][0-9][0-9]"
year_range="(${some_year})(-${some_year})?"

copyright_owner="The OpenSSL Project"
copyright="Copyright .*${year_range} .*${copyright_owner}"

# sed_script:
#   for all lines that contain ${copyright} : {
#     replace years yyyy-zzzz (or year yyyy) by yyyy-${this_year}
#     replace repeated years yyyy-yyyy by yyyy
#   }
sed_script="/${copyright}/{ s/${year_range}/\1-${this_year}/ ; s/(${some_year})-\1/\1/ }"

function usage() {
	cat >&2 <<EOF
usage: $myname [-h|--help] [file|directory] ...

Updates the year ranges of all OpenSSL copyright statements in the given
files or directories. (Directories are traversed recursively.)
EOF
}

if [ $# -eq 0 ]; then
	usage
	exit 0
fi


for arg in "$@"; do
	case $arg in
		-h|--help)
			usage
			exit 0
			;;
		-*)
			echo -e "illegal option: $arg\n" >& 2
			usage
			exit 1
			;;
		*)
			if [ -f "$arg" ]; then
				sed -E -i "${sed_script}" "$arg"
			elif [ -d "$arg" ]; then
				find "$arg" -name '.[a-z]*' -prune -o -type f -exec sed -E -i "${sed_script}" {} +
			else
				echo "$arg: no such file or directory" >&2
			fi
			;;
	esac
done
