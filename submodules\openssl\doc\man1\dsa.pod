=pod

=head1 NAME

openssl-dsa,
dsa - DSA key processing

=head1 SYNOPSIS

B<openssl> B<dsa>
[B<-help>]
[B<-inform PEM|DER>]
[B<-outform PEM|DER>]
[B<-in filename>]
[B<-passin arg>]
[B<-out filename>]
[B<-passout arg>]
[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-text>]
[B<-noout>]
[B<-modulus>]
[B<-pubin>]
[B<-pubout>]
[B<-engine id>]

=head1 DESCRIPTION

The B<dsa> command processes DSA keys. They can be converted between various
forms and their components printed out. B<Note> This command uses the
traditional SSLeay compatible format for private key encryption: newer
applications should use the more secure PKCS#8 format using the B<pkcs8>

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform DER|PEM>

This specifies the input format. The B<DER> option with a private key uses
an ASN1 DER encoded form of an ASN.1 SEQUENCE consisting of the values of
version (currently zero), p, q, g, the public and private key components
respectively as ASN.1 INTEGERs. When used with a public key it uses a
SubjectPublicKeyInfo structure: it is an error if the key is not DSA.

The B<PEM> form is the default format: it consists of the B<DER> format base64
encoded with additional header and footer lines. In the case of a private key
PKCS#8 format is also accepted.

=item B<-outform DER|PEM>

This specifies the output format, the options have the same meaning and default
as the B<-inform> option.

=item B<-in filename>

This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.

=item B<-passin arg>

The input file password source. For more information about the format of B<arg>
see L<openssl(1)/Pass Phrase Options>.

=item B<-out filename>

This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should B<not> be the same as the input
filename.

=item B<-passout arg>

The output file password source. For more information about the format of B<arg>
see L<openssl(1)/Pass Phrase Options>.

=item B<-aes128>, B<-aes192>, B<-aes256>, B<-aria128>, B<-aria192>, B<-aria256>, B<-camellia128>, B<-camellia192>, B<-camellia256>, B<-des>, B<-des3>, B<-idea>

These options encrypt the private key with the specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified the key is written in plain text. This
means that using the B<dsa> utility to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.

=item B<-text>

Prints out the public, private key components and parameters.

=item B<-noout>

This option prevents output of the encoded version of the key.

=item B<-modulus>

This option prints out the value of the public key component of the key.

=item B<-pubin>

By default, a private key is read from the input file. With this option a
public key is read instead.

=item B<-pubout>

By default, a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<dsa>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=back

=head1 NOTES

The PEM private key format uses the header and footer lines:

 -----BEGIN DSA PRIVATE KEY-----
 -----END DSA PRIVATE KEY-----

The PEM public key format uses the header and footer lines:

 -----BEGIN PUBLIC KEY-----
 -----END PUBLIC KEY-----

=head1 EXAMPLES

To remove the pass phrase on a DSA private key:

 openssl dsa -in key.pem -out keyout.pem

To encrypt a private key using triple DES:

 openssl dsa -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl dsa -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl dsa -in key.pem -text -noout

To just output the public part of a private key:

 openssl dsa -in key.pem -pubout -out pubkey.pem

=head1 SEE ALSO

L<dsaparam(1)>, L<gendsa(1)>, L<rsa(1)>,
L<genrsa(1)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
