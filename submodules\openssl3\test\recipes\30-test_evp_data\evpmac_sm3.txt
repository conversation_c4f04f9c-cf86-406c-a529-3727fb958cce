#
# Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding MAC
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.

Title = HMAC-SM3 from GM/T 0042-2015 Appendix D.3

MAC = HMAC
Algorithm = SM3
Input = "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopqabcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
Key = 0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f20
Output = ca05e144ed05d1857840d1f318a4a8669e559fc8391f414485bfdf7bb408963a

MAC = HMAC
Algorithm = SM3
Input = cdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcdcd
Key = 0102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425
Output = 220bf579ded555393f0159f66c99877822a3ecf610d1552154b41d44b94db3ae

MAC = HMAC
Algorithm = SM3
Input = "Hi There"
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Output = c0ba18c68b90c88bc07de794bfc7d2c8d19ec31ed8773bc2b390c9604e0be11e

MAC = HMAC
Algorithm = SM3
Input = "what do ya want for nothing?"
Key = "Jefe"
Output = 2e87f1d16862e6d964b50a5200bf2b10b764faa9680a296a2405f24bec39f882
