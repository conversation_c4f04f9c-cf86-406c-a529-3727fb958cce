=pod

=head1 NAME

openssl-pkcs7,
pkcs7 - PKCS#7 utility

=head1 SYNOPSIS

B<openssl> B<pkcs7>
[B<-help>]
[B<-inform PEM|DER>]
[B<-outform PEM|DER>]
[B<-in filename>]
[B<-out filename>]
[B<-print_certs>]
[B<-text>]
[B<-noout>]
[B<-engine id>]

=head1 DESCRIPTION

The B<pkcs7> command processes PKCS#7 files in DER or PEM format.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform DER|PEM>

This specifies the input format. B<DER> format is DER encoded PKCS#7
v1.5 structure.B<PEM> (the default) is a base64 encoded version of
the DER form with header and footer lines.

=item B<-outform DER|PEM>

This specifies the output format, the options have the same meaning and default
as the B<-inform> option.

=item B<-in filename>

This specifies the input filename to read from or standard input if this
option is not specified.

=item B<-out filename>

Specifies the output filename to write to or standard output by
default.

=item B<-print_certs>

Prints out any certificates or CRLs contained in the file. They are
preceded by their subject and issuer names in one line format.

=item B<-text>

Prints out certificates details in full rather than just subject and
issuer names.

=item B<-noout>

Don't output the encoded version of the PKCS#7 structure (or certificates
is B<-print_certs> is set).

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<pkcs7>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=back

=head1 EXAMPLES

Convert a PKCS#7 file from PEM to DER:

 openssl pkcs7 -in file.pem -outform DER -out file.der

Output all certificates in a file:

 openssl pkcs7 -in file.pem -print_certs -out certs.pem

=head1 NOTES

The PEM PKCS#7 format uses the header and footer lines:

 -----BEGIN PKCS7-----
 -----END PKCS7-----

For compatibility with some CAs it will also accept:

 -----BEGIN CERTIFICATE-----
 -----END CERTIFICATE-----

=head1 RESTRICTIONS

There is no option to print out all the fields of a PKCS#7 file.

This PKCS#7 routines only understand PKCS#7 v 1.5 as specified in RFC2315 they
cannot currently parse, for example, the new CMS as described in RFC2630.

=head1 SEE ALSO

L<crl2pkcs7(1)>

=head1 COPYRIGHT

Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
