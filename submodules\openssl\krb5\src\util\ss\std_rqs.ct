	command_table	ss_std_requests;

	request	ss_self_identify, "Identify the subsystem.",
		".",
		(dont_list, dont_summarize);

	request	ss_help, "Display info on command or topic.",
		help;

	unimplemented
		ss_list_help,
		"List topics for which help is available.",
		list_help, lh;

	request	ss_list_requests, "List available commands.",
		list_requests, lr, "?";

	request	ss_quit, "Leave the subsystem.",
		quit, q;

	unimplemented
		ss_abbrev,
		"Enable/disable abbreviation processing of request lines.",
		abbrev, ab;

	unimplemented
		ss_execute,
		"Execute a UNIX command line.",
		execute, e;

	unimplemented
		ss_summarize_requests,
		"Produce a list of the most commonly used requests.",
		"?";
		
	request	ss_subsystem_name,
		"Return the name of this subsystem.",
		subsystem_name,
		(dont_list);

	request	ss_subsystem_version,
		"Return the version of this subsystem.",
		subsystem_version,
		(dont_list);

	end;
