<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
<HEAD>
<meta name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1">
<link rel="stylesheet" type="text/css" href="Leash.css" />
<Title>Make Default</Title>
</HEAD>
<BODY>
<H1> Make  Default Principal </H1>
<p>
Your <em>default</em> principal is the one whose tickets are used when an application or service asks for tickets without specifying which principal is being authenticated. If you plan to use an application, service, or network that only one of your principals has access to, first set that principal to be the default. </p>
<p>
If you have only one principal, that principal is automatically the default.</p>

<H3>Set Default Principal</H3>
<ol>
<li>Select the principal by clicking it in the main window (if the principal is not listed, first get tickets for it). </li>
<li>Click the Make Default button. </li>
</ol>
<p>
The default principal appears in bold font in the main window.</p>

<h3>Related help</h3>
<ul id="helpul">
<li><a href="HTML/Principals.htm">About principals</a></li>
<li><a href="HTML/Manage_Multiple_Principals.htm">Manage multiple principals</a></li>
</ul>



<SCRIPT Language=JavaScript>
popfont="Arial,.725,,plain "
popupRealm="The Kerberos realm is the group of network resources that you gain access to when you log on with a Kerberos username and password. Often it is named after the DNS domain it corrosponds to. In Windows, realms are called 'domains.' "
</SCRIPT>

<OBJECT id=popup type="application/x-oleobject"
classid="clsid:adb880a6-d8ff-11cf-9377-00aa003b7a11">
</OBJECT>
</BODY>
</HTML>
