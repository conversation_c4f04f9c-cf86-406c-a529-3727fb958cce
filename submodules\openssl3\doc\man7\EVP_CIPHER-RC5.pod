=pod

=head1 NAME

EVP_CIPHER-RC5 - The RC5 EVP_CIPHER implementations

=head1 DESCRIPTION

Support for RC5 symmetric encryption using the B<EVP_CIPHER> API.

Disabled by default. Use the I<enable-rc5> configuration option to enable.

=head2 Algorithm Names

The following algorithms are available in the legacy provider:

=over 4

=item "RC5-CBC" or "RC5"

=item "RC5-ECB"

=item "RC5-OFB"

=item "RC5-CFB"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-legacy(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
