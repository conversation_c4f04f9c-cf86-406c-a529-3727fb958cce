=pod

=head1 NAME

EVP_PKEY_set_type, EVP_PKEY_set_type_str, EVP_PKEY_set_type_by_keymgmt
- functions to change the EVP_PKEY type

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_set_type(EVP_PKEY *pkey, int type);
 int EVP_PKEY_set_type_str(EVP_PKEY *pkey, const char *str, int len);
 int EVP_PKEY_set_type_by_keymgmt(EVP_PKEY *pkey, EVP_KEYMGMT *keymgmt);

=head1 DESCRIPTION

All the functions described here behave the same in so far that they
clear all the previous key data and methods from I<pkey>, and reset it
to be of the type of key given by the different arguments.  If
I<pkey> is NULL, these functions will still return the same return
values as if it wasn't.

EVP_PKEY_set_type() initialises I<pkey> to contain an internal legacy
key.  When doing this, it finds a L<EVP_PKEY_ASN1_METHOD(3)>
corresponding to I<type>, and associates I<pkey> with the findings.
It is an error if no L<EVP_PKEY_ASN1_METHOD(3)> could be found for
I<type>.

EVP_PKEY_set_type_str() initialises I<pkey> to contain an internal legacy
key. When doing this, it finds a L<EVP_PKEY_ASN1_METHOD(3)>
corresponding to I<str> that has then length I<len>, and associates
I<pkey> with the findings.
It is an error if no L<EVP_PKEY_ASN1_METHOD(3)> could be found for
I<type>.

For both EVP_PKEY_set_type() and EVP_PKEY_set_type_str(), I<pkey> gets
a numeric type, which can be retrieved with L<EVP_PKEY_get_id(3)>.  This
numeric type is taken from the L<EVP_PKEY_ASN1_METHOD(3)> that was
found, and is equal to or closely related to I<type> in the case of
EVP_PKEY_set_type(), or related to I<str> in the case of
EVP_PKEY_set_type_str().

EVP_PKEY_set_type_by_keymgmt() initialises I<pkey> to contain an
internal provider side key.  When doing this, it associates I<pkey>
with I<keymgmt>.  For keys initialised like this, the numeric type
retrieved with L<EVP_PKEY_get_id(3)> will always be B<EVP_PKEY_NONE>.

=head1 RETURN VALUES

All functions described here return 1 if successful, or 0 on error.

=head1 SEE ALSO

L<EVP_PKEY_assign(3)>, L<EVP_PKEY_get_id(3)>, L<EVP_PKEY_get0_RSA(3)>,
L<EVP_PKEY_copy_parameters(3)>, L<EVP_PKEY_ASN1_METHOD(3)>,
L<EVP_KEYMGMT(3)>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
