=pod

=head1 NAME

ASN1_INTEGER_new, ASN1_INTEGER_free - ASN1_INTEGER allocation functions

=head1 SYNOPSIS

=for openssl generic

 #include <openssl/asn1.h>

 ASN1_INTEGER *ASN1_INTEGER_new(void);
 void ASN1_INTEGER_free(ASN1_INTEGER *a);

=head1 DESCRIPTION

ASN1_INTEGER_new() returns an allocated B<ASN1_INTEGER> structure.

ASN1_INTEGER_free() frees up a single B<ASN1_INTEGER> object.

B<ASN1_INTEGER> structure representing the ASN.1 INTEGER type

=head1 RETURN VALUES

ASN1_INTEGER_new() return a valid B<ASN1_INTEGER> structure or NULL
if an error occurred.

ASN1_INTEGER_free() does not return a value.

=head1 SEE ALSO

L<ERR_get_error(3)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
