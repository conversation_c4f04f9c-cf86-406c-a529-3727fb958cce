[TOC]

# nfs 线程模型

## nfsserver线程模型
- nfsserver 线程模型比较简单， 单线程， 读请求， 处理请求， 发送响应
  p2pread --> process --> p2pwrite

## nfsclient windows 线程模型
- nfsclient 有多个线程发送请求，并等待响应, 如果请求的线程比较多， 还会把请求入队列   
  thread1： p2pwrite  
  thread2： -p2pwrite  
  thread3： --p2pwrite
- nfsclient 有一个专门的读线程，读取响应并返回结果给thread1， thread2， thread3  
  读线程thread：  
  p2pread-->process->callback(thread1)  -->  
  p2pread-->process->callback(thread2)  -->  
  p2pread-->process->callback(thread3)

## nfsclient android  线程模型
- nfsclient 有多个线程同时发送请求， 发送完请求后会排队再读取响应
  thread1：p2pwrite---------p2pread  
  thread2: --p2pwrite---------p2pread  
  thread3: ---p2pwrite---------p2pread
   


# api 接口

## 内部结构体
设计一个如下的内部结构P2P_SOCKET_CONTEXT， 用来对每个socket 保存相应的成员变量，以便和msquic 进行交互
```C++
typedef struct P2P_SOCKET_CONTEXT {
    enum SOCKET_MODE mode;
    enum SOCKET_TYPE type;
    HQUIC registration;
    HQUIC configuration;
    HQUIC listener;      // For server mode
    HQUIC connection;    // For client mode or accepted connections
    HQUIC stream;        // Current active stream  ，探索多stream
    char* localIp;
    int localPort;
    char* remoteIp;
    int remotePort;
    int isConnected;
    int connTimeout;     // Connection timeout in milliseconds
    void* userData;

    // Buffer for receiving data
    char* recvBuffer;
    int recvBufferSize;
    int recvDataSize;
    int recvDataOffset;

    // Direct read mode (no copy)
    int useDirectReadMode;       // Flag to indicate if using direct read mode

    // Queue for direct read mode
    RECV_BUFFER_ENTRY* recvDirectBufferQueueHead;  // Head of the buffer queue
    RECV_BUFFER_ENTRY* recvDirectBufferQueueTail;  // Tail of the buffer queue
    int recvDirectBufferQueueSize;                 // Number of entries in the queue
    CXPLAT_LOCK recvDirectBufferQueueLock;         // Lock to protect the queue

    // Current buffer being processed (for backward compatibility)
    const QUIC_BUFFER* recvDirectBuffers;          // Direct reference to MsQuic buffers
    uint32_t recvDirectBufferCount;                // Number of direct buffers
    uint64_t recvDirectTotalLength;                // Total length of direct buffers
    uint64_t recvDirectConsumedLength;             // Amount of data consumed from direct buffers

    // Send mode
    int useSendBufferMode;       // Flag to indicate if using send buffer mode (1) or API copy mode (0)
    SEND_RING_BUFFER sendRingBuffer;  // Ring buffer for API copy mode
    QUIC_BUFFER sendQuicBuffer;       // QUIC buffer for sending in API copy mode
    uint64_t workerThreadID;          // ID of the worker thread for SendInline optimization
    int sendInProgress;          // Flag to indicate if a send operation is in progress
    CXPLAT_LOCK sendBufferLock;  // Lock to protect ring buffer access

    // Event handling
    int lastError;       // Last error code
    int dataAvailable;   // Flag indicating data is available to read
    int sendComplete;    // Flag indicating send operation is complete
    int sendCompleteBytes; // Number of bytes sent in last operation

    // SSL/TLS related
    const char* certPath;        // Path to certificate file (if loading from file)
    const char* privateKeyPath;  // Path to private key file (if loading from file)
    const char* certData;        // Certificate data in PEM format (if loading from memory)
    const char* privateKeyData;  // Private key data in PEM format (if loading from memory)
    int useMemoryCert;           // Flag to indicate if using memory-based certificates
    CertVerifyCallback certVerifyCallback;

    // Connection queue for server mode
    struct P2P_CONNECTION_QUEUE_ENTRY* connectionQueueHead;
    struct P2P_CONNECTION_QUEUE_ENTRY* connectionQueueTail;
    int connectionQueueSize;

} P2P_SOCKET_CONTEXT;
```
## 三、函数功能说明

### P2pCreate
```c
P2P_SOCKET P2pCreate(SocketOptions* option)
```
功能：初始化socket对象。  
参数：option 参数  
返回值：P2P_SOCKET  
实现：返回一个P2P_SOCKET_CONTEXT 对象
```c
P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)calloc(1, sizeof(P2P_SOCKET_CONTEXT));
if (context == NULL) {
    return NULL;
}
MsQuicOpen2(&MsQuic);

// Initialize context with options
context->mode = option->mode;
context->type = option->type;
MsQuic->RegistrationOpen(&RegConfig, &context->registration);
return (P2P_SOCKET)context;
```

### P2pSetConnTimeout
```c
int P2pSetConnTimeout(P2P_SOCKET soc, int timeout)
```
功能：设置超时时间  
参数：超时时长  
返回值：P2P_SOCKET  
实现：保存超时时长到context
```c
P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
context->connTimeout = timeout;
return 0;
```

### P2pBind
```c
int P2pBind(P2P_SOCKET soc, const char* ipaddr, int port)
```
功能：绑定ip 和端口  
参数：ip， 端口  
返回值：  
实现：拷贝 ip 和端口信息到context
```c
    P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    memcpy_s(context->localIp, sizeof(context->localIp), ipaddr, strlen(ipaddr));
    context->localPort = port;
    return 0;
```


### P2pListen
```c
int P2pListen(P2P_SOCKET soc)
```
功能：监听  
参数：socket  
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
    // Create listener
   MsQuic->ListenerOpen(
    context->registration,
    ListenerCallback,
    context,
    &context->listener);

    MsQuic->ListenerStart(
        context->listener,
        &AlpnBuffer,
        1,
        &Address);

    return 0;
```

### P2pConnect
```c
int P2pConnect(P2P_SOCKET soc, const char* ipaddr, int port)
```
功能：连接  
参数：socket ， ip ,端口 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
   memcpy_s(context->remoteIp, sizeof(context->remoteIp), ipaddr, strlen(ipaddr));
   context->remotePort = port;
   MsQuic->ConnectionOpen(
        context->registration,
        ClientConnectionCallback,
        context,
        &context->connection);

    MsQuic->ConnectionStart(
        context->connection,
        context->configuration,
        QUIC_ADDRESS_FAMILY_UNSPEC,
        context->remoteIp,
        (uint16_t)context->remotePort);
   // wait ClientConnectionCallback to notify success 
    return context->isConnected ? 0 : -1;
```

### P2pWrite
```c
int P2pWrite(P2P_SOCKET soc, const char* buffer, int len)
```
功能：发送数据  
参数：socket ， 数据 ,长度 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
  if (context->useSendBufferMode) {
    // Mode 2: Zero Copy Mode
    // In this mode, we rely on MsQuic's SendBufferingEnabled setting being
    // FALSE The application buffer is directly passed to MsQuic
    QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;

    // Create buffer for sending
    QUIC_BUFFER Buffer = {.Buffer = (uint8_t *)buffer, .Length = (uint32_t)len};

    // Send data
    MsQuic->StreamSend(context->stream, &Buffer, 1,
                                            sendFlags, (void *)(uintptr_t)len);

    if (QUIC_FAILED(status)) {
      return -1;
    }

    // Wait for send completion in blocking mode
    context->sendComplete = 0;
    context->sendCompleteBytes = 0;

    // Wait for send completion or timeout

      // Check if connection is closed or an error occurred
      if (!context->isConnected || context->lastError != 0) {
        return -1;
      }


    // If send completed, return the number of bytes sent
    if (context->sendComplete) {
      return context->sendCompleteBytes;
    }

    // Timeout but request was successfully submitted, return the requested
    // length
    return len;
  } else {

    // Mode 1: API Copy Mode with Ring Buffer
    // Copy the data to our ring buffer
    result = RingBufferWrite(&context->sendRingBuffer, buffer, len);


      if (SendDataFromRingBuffer(context) < 0) {
        return -1;
      }

    // In this mode, we return immediately after copying to the buffer
    return len;
  }

```

### P2pWritev
```c
int P2pWritev(P2P_SOCKET soc, struct p2p_iovec* iov, int count)
```
功能：发送数据  
参数：socket ， iov， count 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
  if (context->useSendBufferMode) {
    // Mode 2: Zero Copy Mode
    // In this mode, we rely on MsQuic's SendBufferingEnabled setting being
    // FALSE The application buffer is directly passed to MsQuic
    QUIC_SEND_FLAGS sendFlags = QUIC_SEND_FLAG_NONE;

    QUIC_BUFFER *Buffers = (QUIC_BUFFER *)malloc(count * sizeof(QUIC_BUFFER));
    for (int i = 0; i < count; i++) {
          Buffers[i].Buffer = (uint8_t *)iov[i].iov_base;
            Buffers[i].Length = (uint32_t)iov[i].iov_len;
    }

    // Send data
    MsQuic->StreamSend(context->stream, &Buffers, count,
                                            sendFlags, (void *)(uintptr_t)len);

    if (QUIC_FAILED(status)) {
      return -1;
    }

    // Wait for send completion in blocking mode
    context->sendComplete = 0;
    context->sendCompleteBytes = 0;

    // Wait for send completion or timeout

      // Check if connection is closed or an error occurred
      if (!context->isConnected || context->lastError != 0) {
        return -1;
      }


    // If send completed, return the number of bytes sent
    if (context->sendComplete) {
      return context->sendCompleteBytes;
    }

    // Timeout but request was successfully submitted, return the requested
    // length
    return len;
  } else {

    // Mode 1: API Copy Mode with Ring Buffer
    // Copy the data to our ring buffer
    for (int i = 0; i < count; i++) {
            RingBufferWrite(&context->sendRingBuffer,  (uint8_t *)iov[i].iov_base, (uint32_t)iov[i].iov_len);
    }


      if (SendDataFromRingBuffer(context) < 0) {
        return -1;
      }

    // In this mode, we return immediately after copying to the buffer
    return len;
  }
```


### P2pRead
```c
int P2pRead(P2P_SOCKET soc, char* buffer, int len) 
```
功能：接收数据  
参数：socket ， buffer， len 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
     if (context->useDirectReadMode) {
        // Direct read mode - read directly from MsQuic buffers

        // Copy data from buffers
        memcpy(buffer ,
         context->recvDirectBuffers[bufferIndex].Buffer 
         len);
        // notify msquic we read len bytes
        MsQuic->StreamReceiveComplete(context->stream,
                               len);
    }else {
        // Copy mode - read from our internal buffer
        // If there's data in the receive buffer, return it
        memcpy(buffer, context->recvBuffer + context->recvDataOffset, len);
    }

    return len;
```

### P2pAccept
```c
 P2P_SOCKET P2pAccept(P2P_SOCKET soc, char* ipaddr, int ipaddr_len, int* port) 
```
功能：accept socket  
参数：socket ， buffer， len 
返回值：  
实现：当有连接进来时， 返回一份复制的context 内容， 并把connection 填入到里面
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
   P2P_SOCKET_CONTEXT* newContext = (P2P_SOCKET_CONTEXT*)calloc(1, sizeof(P2P_SOCKET_CONTEXT));
   memcpy(acceptContext, context, sizeof(P2P_SOCKET_CONTEXT));
  //wait callback to notify connection is coming 
    
    return acceptContext;
```

### P2pGetLocalPort
```c
int P2pGetLocalPort(P2P_SOCKET soc) 
```
功能：获取本地端口  
参数：socket 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
   return context->localPort;
```

### P2pSetReadMode
```c
int P2pSetReadMode(P2P_SOCKET soc, int directMode)
```
功能：设置读模式， directMode 为1是直接使用msquic 内部buffer，为0 模式下会copy 到接收缓存 。
参数：socket 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT *context = (P2P_SOCKET_CONTEXT *)soc;
   context->useDirectReadMode = directMode;
```


### P2pSetSendMode
```c
int P2pSetSendMode(P2P_SOCKET soc, int sendBufferMode)
```
功能：设置写模式， sendBufferMode 为1， copy 要写的内容到ringbuffer，然后设置msqic 为直接发送Settings.SendBufferingEnabled = 0；（msquic 为直接发送， 收到ack 之后才返回完成）。 sendBufferMode 为0， 不需要copy 要写的内容到ringbuffer，然后设置msqic 为直接发送Settings.SendBufferingEnabled = 1；（msquic 为缓存发送， 不需要等到ack 就返回完成 ）。
参数：socket 
返回值：  
实现：
```c
   P2P_SOCKET_CONTEXT *context = (P2P_SOCKET_CONTEXT *)soc;
   context->useSendBufferMode = sendBufferMode;
```


### P2pPoll
```c
 int P2pPoll(P2P_SOCKET soc, PollEvent* events, int timeout)
```
功能：poll 读写  
参数：socket ， 事件，超时时间  
返回值：  
实现：对于写请求，直接返回1，永远可写。 对于读请求， 判断读缓存buffer 里是否有数据， 没有则在timeout 事件内等待callback 返回接收到的数据
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;
   // Check for readable events
   if (events->events & P2PPOLLIN) {
    if (context->recvDataSize > 0) {
        events->revents |= P2PPOLLIN;
        return 1;
    }
   }

   // Check for writable events
   if (events->events & P2PPOLLOUT) {
    if (context->isConnected) {
        events->revents |= P2PPOLLOUT;
        // 判断下缓存长度
        return 1;
    }
   }   
  //wait callback to notify connection is coming 
    判断多个stream 有没有数据回掉上来
    
    return acceptContext;
```

### P2pClose
```c
int P2pClose(P2P_SOCKET soc) 
```
功能：关闭socket  
参数：socket 
返回值：  
实现： 关闭连接， 释放资源
```c
   P2P_SOCKET_CONTEXT* context = (P2P_SOCKET_CONTEXT*)soc;

   MsQuic->StreamClose(context->stream);

   MsQuic->ConnectionClose(context->connection);

   free(context); 
    
    return acceptContext;
```




  


