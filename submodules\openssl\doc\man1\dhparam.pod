=pod

=head1 NAME

openssl-dhparam,
dhparam - DH parameter manipulation and generation

=head1 SYNOPSIS

B<openssl dhparam>
[B<-help>]
[B<-inform DER|PEM>]
[B<-outform DER|PEM>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-dsaparam>]
[B<-check>]
[B<-noout>]
[B<-text>]
[B<-C>]
[B<-2>]
[B<-5>]
[B<-rand file...>]
[B<-writerand file>]
[B<-engine id>]
[I<numbits>]

=head1 DESCRIPTION

This command is used to manipulate DH parameter files.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform DER|PEM>

This specifies the input format. The B<DER> option uses an ASN1 DER encoded
form compatible with the PKCS#3 DHparameter structure. The PEM form is the
default format: it consists of the B<DER> format base64 encoded with
additional header and footer lines.

=item B<-outform DER|PEM>

This specifies the output format, the options have the same meaning and default
as the B<-inform> option.

=item B<-in> I<filename>

This specifies the input filename to read parameters from or standard input if
this option is not specified.

=item B<-out> I<filename>

This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should B<not> be the same
as the input filename.

=item B<-dsaparam>

If this option is used, DSA rather than DH parameters are read or created;
they are converted to DH format.  Otherwise, "strong" primes (such
that (p-1)/2 is also prime) will be used for DH parameter generation.

DH parameter generation with the B<-dsaparam> option is much faster,
and the recommended exponent length is shorter, which makes DH key
exchange more efficient.  Beware that with such DSA-style DH
parameters, a fresh DH key should be created for each use to
avoid small-subgroup attacks that may be possible otherwise.

=item B<-check>

Performs numerous checks to see if the supplied parameters are valid and
displays a warning if not.

=item B<-2>, B<-5>

The generator to use, either 2 or 5. If present then the
input file is ignored and parameters are generated instead. If not
present but B<numbits> is present, parameters are generated with the
default generator 2.

=item B<-rand file...>

A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is B<;> for MS-Windows, B<,> for OpenVMS, and B<:> for
all others.

=item [B<-writerand file>]

Writes random data to the specified I<file> upon exit.
This can be used with a subsequent B<-rand> flag.

=item I<numbits>

This option specifies that a parameter set should be generated of size
I<numbits>. It must be the last option. If this option is present then
the input file is ignored and parameters are generated instead. If
this option is not present but a generator (B<-2> or B<-5>) is
present, parameters are generated with a default length of 2048 bits.

=item B<-noout>

This option inhibits the output of the encoded version of the parameters.

=item B<-text>

This option prints out the DH parameters in human readable form.

=item B<-C>

This option converts the parameters into C code. The parameters can then
be loaded by calling the get_dhNNNN() function.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<dhparam>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=back

=head1 WARNINGS

The program B<dhparam> combines the functionality of the programs B<dh> and
B<gendh> in previous versions of OpenSSL. The B<dh> and B<gendh>
programs are retained for now but may have different purposes in future
versions of OpenSSL.

=head1 NOTES

PEM format DH parameters use the header and footer lines:

 -----BEGIN DH PARAMETERS-----
 -----END DH PARAMETERS-----

OpenSSL currently only supports the older PKCS#3 DH, not the newer X9.42
DH.

This program manipulates DH parameters not keys.

=head1 BUGS

There should be a way to generate and manipulate DH keys.

=head1 SEE ALSO

L<dsaparam(1)>

=head1 COPYRIGHT

Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
