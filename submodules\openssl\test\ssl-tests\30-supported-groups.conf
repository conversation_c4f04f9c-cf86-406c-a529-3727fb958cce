# Generated with generate_ssl_tests.pl

num_tests = 2

test-0 = 0-Just a sanity test case
test-1 = 1-Pass with empty groups with TLS1.2
# ===========================================================

[0-Just a sanity test case]
ssl_conf = 0-Just a sanity test case-ssl

[0-Just a sanity test case-ssl]
server = 0-Just a sanity test case-server
client = 0-Just a sanity test case-client

[0-Just a sanity test case-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-Just a sanity test case-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-Pass with empty groups with TLS1.2]
ssl_conf = 1-Pass with empty groups with TLS1.2-ssl

[1-Pass with empty groups with TLS1.2-ssl]
server = 1-Pass with empty groups with TLS1.2-server
client = 1-Pass with empty groups with TLS1.2-client

[1-Pass with empty groups with TLS1.2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-Pass with empty groups with TLS1.2-client]
CipherString = DEFAULT
Groups = sect163k1
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


