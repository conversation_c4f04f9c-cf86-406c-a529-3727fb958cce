


          
# MsQuic Stream 实现分析

## 1. Stream 概述

QUIC Stream 是 QUIC 协议中的一个核心概念，它提供了在单个 QUIC 连接上多路复用的、有序的、可靠的数据传输通道。MsQuic 作为 QUIC 协议的实现，提供了完整的 Stream 功能支持。

Stream 在 QUIC 中具有以下特点：
- 多路复用：单个连接可以同时支持多个流
- 有序传输：保证数据按顺序交付
- 流量控制：每个流独立的流量控制机制
- 优先级：支持流的优先级调度
- 双向/单向：支持双向和单向流

## 2. Stream 核心数据结构

### 2.1 QUIC_STREAM 结构

`QUIC_STREAM` 是 MsQuic 中表示 QUIC 流的核心数据结构，定义在 `stream.h` 中。该结构包含了流的所有状态信息和管理逻辑。主要组成部分包括：

- **基本信息**：引用计数、流ID、流类型、流状态等
- **连接引用**：指向所属连接的指针
- **发送管理**：发送队列、发送标志、发送状态等
- **接收管理**：接收队列、接收标志、接收状态等
- **流量控制**：发送和接收窗口大小、已接收/发送的字节数等
- **应用层回调**：应用程序注册的回调函数和上下文

### 2.2 QUIC_STREAM_FLAGS 枚举

定义了流的各种状态标志，包括：

```c
//
// Different flags of a stream.
// Note - Keep quictypes.h's copy up to date.
//
typedef union QUIC_STREAM_FLAGS {
    uint64_t AllFlags;
    struct {
        BOOLEAN Allocated               : 1;    // Allocated by Connection. Used for Debugging.
        BOOLEAN Initialized             : 1;    // Initialized successfully. Used for Debugging.
        BOOLEAN Started                 : 1;    // The app has started the stream.
        BOOLEAN StartedIndicated        : 1;    // The app received a start complete event.
        BOOLEAN PeerStreamStartEventActive : 1; // The app is processing QUIC_CONNECTION_EVENT_PEER_STREAM_STARTED
        BOOLEAN Unidirectional          : 1;    // Sends/receives in 1 direction only.
        BOOLEAN Opened0Rtt              : 1;    // A 0-RTT packet opened the stream.
        BOOLEAN IndicatePeerAccepted    : 1;    // The app requested the PEER_ACCEPTED event.

        BOOLEAN SendOpen                : 1;    // Send a STREAM frame immediately on start.
        BOOLEAN SendOpenAcked           : 1;    // A STREAM frame has been acknowledged.

        BOOLEAN LocalNotAllowed         : 1;    // Peer's unidirectional stream.
        BOOLEAN LocalCloseFin           : 1;    // Locally closed (graceful).
        BOOLEAN LocalCloseReset         : 1;    // Locally closed (locally aborted).
        BOOLEAN LocalCloseResetReliable : 1;    // Indicates that we should shutdown the send path once we sent/ACK'd ReliableOffsetSend bytes.
        BOOLEAN LocalCloseResetReliableAcked : 1; // Indicates the peer has acknowledged we will stop sending once we sent/ACK'd ReliableOffsetSend bytes.
        BOOLEAN RemoteCloseResetReliable : 1;   // Indicates that the peer initiated a reliable reset. Keep Recv path available for RecvMaxLength bytes.
        BOOLEAN ReceivedStopSending     : 1;    // Peer sent STOP_SENDING frame.
        BOOLEAN LocalCloseAcked         : 1;    // Any close acknowledged.
        BOOLEAN FinAcked                : 1;    // Our FIN was acknowledged.
        BOOLEAN InRecovery              : 1;    // Lost data is being retransmitted and is
                                                // unacknowledged.

        BOOLEAN RemoteNotAllowed        : 1;    // Our unidirectional stream.
        BOOLEAN RemoteCloseFin          : 1;    // Remotely closed.
        BOOLEAN RemoteCloseReset        : 1;    // Remotely closed (remotely aborted).
        BOOLEAN SentStopSending         : 1;    // We sent STOP_SENDING frame.
        BOOLEAN RemoteCloseAcked        : 1;    // Any close acknowledged.

        BOOLEAN SendEnabled             : 1;    // Application is allowed to send data.
        BOOLEAN ReceiveEnabled          : 1;    // Application is ready for receive callbacks.
        BOOLEAN ReceiveMultiple         : 1;    // The app supports multiple parallel receive indications.
        BOOLEAN UseAppOwnedRecvBuffers  : 1;    // The stream is using app provided receive buffers.
        BOOLEAN ReceiveFlushQueued      : 1;    // The receive flush operation is queued.
        BOOLEAN ReceiveDataPending      : 1;    // Data (or FIN) is queued and ready for delivery.
        BOOLEAN ReceiveCallActive       : 1;    // There is an active receive to the app.
        BOOLEAN SendDelayed             : 1;    // A delayed send is currently queued.
        BOOLEAN CancelOnLoss            : 1;    // Indicates that the stream is to be canceled
                                                // if loss is detected.

        BOOLEAN HandleSendShutdown      : 1;    // Send shutdown complete callback delivered.
        BOOLEAN HandleShutdown          : 1;    // Shutdown callback delivered.
        BOOLEAN HandleClosed            : 1;    // Handle closed by application layer.

        BOOLEAN ShutdownComplete        : 1;    // Both directions have been shutdown and acknowledged.
        BOOLEAN Uninitialized           : 1;    // Uninitialize started/completed. Used for Debugging.
        BOOLEAN Freed                   : 1;    // Freed after last ref count released. Used for Debugging.

        BOOLEAN InStreamTable           : 1;    // The stream is currently in the connection's table.
        BOOLEAN InWaitingList           : 1;    // The stream is currently in the waiting list for stream id FC.
        BOOLEAN DelayIdFcUpdate         : 1;    // Delay stream ID FC updates to StreamClose.
    };
} QUIC_STREAM_FLAGS;
```

### 2.3 QUIC_SEND_REQUEST 结构
```c
//
// Tracks the data queued up for sending by an application.
//
typedef struct QUIC_SEND_REQUEST {

    //
    // The pointer to the next item in the list.
    //
    struct QUIC_SEND_REQUEST* Next;

    //
    // Array of buffers to send.
    //
    _Field_size_bytes_(BufferCount)
    const QUIC_BUFFER* Buffers;

    //
    // The size of the Buffers array.
    //
    uint32_t BufferCount;

    //
    // A set of flags.
    //
    QUIC_SEND_FLAGS Flags;

    //
    // The starting stream offset.
    //
    uint64_t StreamOffset;

    //
    // The length of all the Buffers.
    //
    uint64_t TotalLength;

    //
    // Data descriptor for buffered requests.
    //
    QUIC_BUFFER InternalBuffer;

    //
    // API Client completion context.
    //
    void* ClientContext;

} QUIC_SEND_REQUEST;
```

表示应用程序的发送请求，包含要发送的数据缓冲区和相关标志。

### 2.4 QUIC_RECV_BUFFER 结构

```c
typedef struct QUIC_RECV_BUFFER {

    //
    // A list of chunks that make up the buffer.
    //
    CXPLAT_LIST_ENTRY Chunks;

    //
    // Optional, preallocated initial chunk.
    //
    QUIC_RECV_CHUNK* PreallocatedChunk;

    //
    // Ranges of stream offsets that have been written to the buffer,
    // starting from 0 (not only what is currently in the buffer).
    // The first sub-range includes [0, BaseOffset + ReadLength),
    // and potentially more if ReadLength is constrained by the chunk size.
    //
    QUIC_RANGE WrittenRanges;

    //
    // The length of all pending reads to the app.
    //
    uint64_t ReadPendingLength;

    //
    // The stream offset of the byte at ReadStart.
    //
    uint64_t BaseOffset;

    //
    // Position of the reading head in the first chunk.
    //
    uint32_t ReadStart;

    //
    // The length of data available to read in the first "active" chunk,
    // starting at ReadStart.
    // ("Active" means a chunk that can targetted by a read or write.
    // In SINGLE and CIRCULAR modes, only the last chunk is "active".)
    //
    uint32_t ReadLength;

    //
    // Length of the buffer indicated to peers.
    // Invariant: BaseOffset + VirtualBufferLength must never decrease.
    //
    uint32_t VirtualBufferLength;

    //
    // Basically same as Chunk->AllocLength of first chunk, but start shrinking
    // by drain operation after next chunk is allocated.
    //
    uint32_t Capacity;

    //
    // Controls the behavior of the buffer, which changes the logic for
    // writing, reading and draining.
    //
    QUIC_RECV_BUF_MODE RecvMode;

} QUIC_RECV_BUFFER;

```

管理接收到的数据缓冲区，支持乱序接收和有序交付。

### 2.5 流发送相关数据结构

```c
// 流发送状态
typedef struct QUIC_STREAM_SEND {
    // 发送队列
    QUIC_SEND_BUFFER SendBuffer;
    
    // 发送标志
    uint32_t SendFlags;
    
    // 发送优先级
    uint16_t SendPriority;
    
    // 发送状态标志
    BOOLEAN SendDelayed;
    BOOLEAN InRecovery;
    BOOLEAN ResetAcked;
    BOOLEAN FinAcked;
    
    // 最大允许发送的数据量
    uint64_t MaxAllowedSendOffset;
    
    // 已发送的最大偏移量
    uint64_t NextSendOffset;
} QUIC_STREAM_SEND;
```

## 3. Stream 状态管理

### 3.1 Stream 状态图

```mermaid
stateDiagram-v2
    Idle --> Open: 创建流
    Open --> LocalClose: 本地关闭(FIN)
    Open --> RemoteClose: 远程关闭(FIN)
    LocalClose --> Closed: 远程关闭
    RemoteClose --> Closed: 本地关闭
    Closed --> Shutdown: 完成清理
```

### 3.2 Stream 状态转换

- **Idle → Open**：流创建并开始发送/接收数据
- **Open → Local Close**：本地应用程序关闭发送方向
- **Open → Remote Close**：远程对端关闭发送方向
- **Local Close + Remote Close → Closed**：双向都关闭
- **Closed → Shutdown**：完成所有清理工作

## 4. Stream 创建流程

### 4.1 客户端创建流

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant API as MsQuic API
    participant Stream as QUIC_STREAM
    participant Conn as QUIC_CONNECTION
    
    App->>API: MsQuicStreamOpen
    API->>Stream: QuicStreamInitialize
    Stream->>Stream: 初始化流结构
    Stream->>Conn: 添加到流集合
    API->>App: 返回流句柄
    App->>API: MsQuicStreamStart
    API->>Stream: QuicStreamStart
    Stream->>Conn: 分配流ID
    Stream->>App: 通知流启动完成
```

1. 应用程序通过 `MsQuicStreamOpen` 创建流
2. 内部调用 `QuicStreamInitialize` 分配流结构体
3. 根据流类型（单向/双向）设置流ID和流标志
4. 将流添加到连接的流集合中
5. 返回流句柄给应用程序

### 4.2 服务端接收流

```mermaid
sequenceDiagram
    participant Remote as 远程端点
    participant Conn as QUIC_CONNECTION
    participant Stream as QUIC_STREAM
    participant App as 应用程序
    
    Remote->>Conn: STREAM帧
    Conn->>Conn: 检测新流ID
    Conn->>Stream: QuicStreamInitialize
    Stream->>Stream: 初始化流结构
    Conn->>Conn: 添加到流集合
    Conn->>App: 通知新流到达
```

当服务端接收到新流的数据包时：

1. 连接处理数据包，发现新的流ID
2. 调用 `QuicStreamInitialize` 创建新的流结构
3. 将流添加到连接的流集合中
4. 通过连接回调通知应用程序新流到达

## 5. Stream 数据发送流程

### 5.1 发送数据

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Stream as QUIC_STREAM
    participant Send as 发送队列
    participant Conn as QUIC_CONNECTION
    participant Remote as 远程端点
    
    App->>Stream: MsQuicStreamSend
    Stream->>Stream: QuicStreamSend
    Stream->>Send: 添加到发送队列
    Stream->>Stream: 设置发送标志
    Stream->>Conn: 添加到发送流列表
    Conn->>Conn: QuicSendQueueFlush
    Conn->>Remote: 发送STREAM帧
```

1. 应用程序通过 `MsQuicStreamSend` 发送数据
2. 内部调用 `QuicStreamSend` 处理发送请求
3. 将数据添加到流的发送队列
4. 设置流的发送标志 `QUIC_STREAM_SEND_FLAG_DATA`
5. 将流添加到连接的发送流列表
6. 触发连接的发送操作

### 5.2 发送控制帧

流可以发送以下控制帧：

- `STREAM_FRAME`：包含流数据
- `RESET_STREAM_FRAME`：重置流
- `STOP_SENDING_FRAME`：请求对端停止发送
- `MAX_STREAM_DATA_FRAME`：更新流接收窗口

## 6. Stream 数据接收流程

### 6.1 接收数据

```mermaid
sequenceDiagram
    participant Remote as 远程端点
    participant Conn as QUIC_CONNECTION
    participant Stream as QUIC_STREAM
    participant Recv as 接收缓冲区
    participant App as 应用程序
    
    Remote->>Conn: STREAM帧
    Conn->>Stream: QuicStreamRecv
    Stream->>Recv: 添加到接收缓冲区
    Stream->>Stream: 更新接收窗口
    Stream->>App: 通知数据可用
    App->>Stream: MsQuicStreamReceiveComplete
    Stream->>Stream: 更新已消费字节数
    Stream->>Remote: 发送MAX_STREAM_DATA帧
```

1. 连接接收到数据包后，解析出流帧
2. 调用 `QuicStreamRecv` 处理流数据
3. 将数据添加到流的接收缓冲区
4. 更新流的接收窗口
5. 如果应用程序已启用接收回调，则通知应用程序

### 6.2 流量控制

流量控制通过以下机制实现：

- 接收窗口：限制对端可以发送的数据量
- `MAX_STREAM_DATA` 帧：通知对端更新的接收窗口
- `STREAM_DATA_BLOCKED` 帧：当发送方被接收窗口阻塞时发送

## 7. Stream 关闭流程

### 7.1 本地关闭

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant Stream as QUIC_STREAM
    participant Conn as QUIC_CONNECTION
    participant Remote as 远程端点
    
    App->>Stream: MsQuicStreamShutdown
    Stream->>Stream: QuicStreamShutdown
    alt 优雅关闭
        Stream->>Remote: 发送带FIN的STREAM帧
    else 中止关闭
        Stream->>Remote: 发送RESET_STREAM帧
    end
    Stream->>Stream: 更新流状态
    Stream->>App: 通知关闭完成
```

1. 应用程序通过 `MsQuicStreamShutdown` 关闭流
2. 内部调用 `QuicStreamShutdown` 处理关闭请求
3. 根据关闭标志设置流状态
4. 如果需要，发送 `RESET_STREAM` 帧或 `STOP_SENDING` 帧
5. 检查流是否可以完全关闭

### 7.2 远程关闭

```mermaid
sequenceDiagram
    participant Remote as 远程端点
    participant Conn as QUIC_CONNECTION
    participant Stream as QUIC_STREAM
    participant App as 应用程序
    
    alt 优雅关闭
        Remote->>Conn: 带FIN的STREAM帧
    else 中止关闭
        Remote->>Conn: RESET_STREAM帧
    end
    Conn->>Stream: 处理关闭帧
    Stream->>Stream: 更新流状态
    Stream->>App: 通知流关闭
```

1. 远程端点发送带有 FIN 标志的 STREAM 帧或 RESET_STREAM 帧
2. 连接处理帧并更新流状态
3. 通知应用程序流已关闭
4. 如果双向都已关闭，则进入完全关闭状态

## 8. Stream 关键函数

### 8.1 流生命周期管理

- `QuicStreamInitialize`：初始化流结构
- `QuicStreamStart`：启动流
- `QuicStreamShutdown`：关闭流
- `QuicStreamClose`：关闭流句柄
- `QuicStreamFree`：释放流资源

### 8.2 流数据处理

- `QuicStreamSend`：发送流数据
- `QuicStreamRecv`：接收流数据
- `QuicStreamReceiveComplete`：完成数据接收处理
- `QuicStreamSendFlush`：刷新发送队列

### 8.3 流状态查询

- `QuicStreamSendGetState`：获取发送状态
- `QuicStreamRecvGetState`：获取接收状态
- `QuicStreamAddRef`：增加引用计数
- `QuicStreamRelease`：减少引用计数

## 9. Stream 流量控制机制

```mermaid
flowchart TD
    A[流量控制] --> B[接收窗口控制]
    A --> C[发送窗口控制]
    B --> D[MaxAllowedRecvOffset]
    B --> E[发送MAX_STREAM_DATA帧]
    C --> F[MaxAllowedSendOffset]
    C --> G[接收MAX_STREAM_DATA帧]
    F --> H{是否可发送?}
    H -->|是| I[发送数据]
    H -->|否| J[发送STREAM_DATA_BLOCKED帧]
```

流量控制是 QUIC 协议的重要特性，MsQuic 实现了完整的流量控制机制：

1. **接收窗口控制**：
   - 每个流维护一个 `MaxAllowedRecvOffset` 值，表示允许接收的最大数据量
   - 当接收数据接近窗口上限时，发送 `MAX_STREAM_DATA` 帧更新窗口

2. **发送窗口控制**：
   - 每个流维护一个 `MaxAllowedSendOffset` 值，表示允许发送的最大数据量
   - 当接收到 `MAX_STREAM_DATA` 帧时，更新发送窗口
   - 当发送数据超过窗口时，发送 `STREAM_DATA_BLOCKED` 帧

## 10. 应用程序接口

### 10.1 流创建

```c
QUIC_STATUS
QUIC_API
MsQuicStreamOpen(
    _In_ _Pre_defensive_ HQUIC Connection,
    _In_ QUIC_STREAM_OPEN_FLAGS Flags,
    _In_ _Pre_defensive_ QUIC_STREAM_CALLBACK_HANDLER Handler,
    _In_opt_ void* Context,
    _Outptr_ _At_(*Stream, __drv_allocatesMem(Mem)) _Pre_defensive_
        HQUIC* Stream
    );
```

### 10.2 流启动

```c
QUIC_STATUS
QUIC_API
MsQuicStreamStart(
    _In_ _Pre_defensive_ HQUIC Stream,
    _In_ QUIC_STREAM_START_FLAGS Flags
    );
```

### 10.3 流发送

```c
QUIC_STATUS
QUIC_API
MsQuicStreamSend(
    _In_ _Pre_defensive_ HQUIC Stream,
    _In_reads_(BufferCount) _Pre_defensive_
        const QUIC_BUFFER* const Buffers,
    _In_ uint32_t BufferCount,
    _In_ QUIC_SEND_FLAGS Flags,
    _In_opt_ void* ClientSendContext
    );
```

### 10.4 流接收完成

```c
QUIC_STATUS
QUIC_API
MsQuicStreamReceiveComplete(
    _In_ _Pre_defensive_ HQUIC Stream,
    _In_ uint64_t BufferLength
    );
```

### 10.5 流关闭

```c
QUIC_STATUS
QUIC_API
MsQuicStreamShutdown(
    _In_ _Pre_defensive_ HQUIC Stream,
    _In_ QUIC_STREAM_SHUTDOWN_FLAGS Flags,
    _In_ _Pre_defensive_ QUIC_UINT62 ErrorCode
    );
```

### 10.6 流回调事件

应用程序通过注册回调函数接收流事件：

- `QUIC_STREAM_EVENT_START_COMPLETE`：流启动完成
- `QUIC_STREAM_EVENT_RECEIVE`：接收到数据
- `QUIC_STREAM_EVENT_SEND_COMPLETE`：发送完成
- `QUIC_STREAM_EVENT_PEER_SEND_SHUTDOWN`：对端关闭发送
- `QUIC_STREAM_EVENT_PEER_SEND_ABORTED`：对端中止发送
- `QUIC_STREAM_EVENT_SHUTDOWN_COMPLETE`：流关闭完成
- `QUIC_STREAM_EVENT_IDEAL_SEND_BUFFER_SIZE`：理想发送缓冲区大小

## 11. 总结

MsQuic 的 Stream 实现提供了完整的 QUIC 流功能，支持多路复用、有序传输、流量控制和优先级调度等特性。通过合理的状态管理和事件跟踪，确保了流的可靠性和高性能。应用程序可以通过简洁的 API 接口使用流功能，实现复杂的数据传输需求。

流的实现核心在于状态管理和数据缓冲区管理，通过精心设计的状态转换和缓冲区操作，实现了高效的数据传输和流量控制。同时，流的生命周期管理也确保了资源的合理分配和释放，避免了内存泄漏和资源浪费。

        