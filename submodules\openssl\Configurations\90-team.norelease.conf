## -*- mode: perl; -*-
## Build configuration targets for openssl-team members

my %targets = (
    "purify" => {
        inherit_from     => [ 'BASE_unix' ],
        cc               => "purify gcc",
        CFLAGS           => "-g -Wall",
        thread_scheme    => "(unknown)",
        ex_libs          => add(" ","-lsocket -lnsl"),
    },
    "debug" => {
        inherit_from     => [ 'BASE_unix' ],
        cc               => "gcc",
        cflags           => "-DBN_DEBUG -DREF_DEBUG -DCONF_DEBUG -DBN_CTX_DEBUG -DOPENSSL_NO_ASM -ggdb -g2 -Wformat -Wshadow -Wmissing-prototypes -Wmissing-declarations -Werror",
        thread_scheme    => "(unknown)",
    },
    "debug-erbridge" => {
        inherit_from     => [ 'BASE_unix', "x86_64_asm" ],
        cc               => "gcc",
        cflags           => combine(join(' ', @gcc_devteam_warn),
                                    "-DBN_DEBUG -DCONF_DEBUG -m64 -DL_ENDIAN -DTERMIO -g",
                                    threads("-D_REENTRANT")),
        ex_libs          => add(" ","-ldl"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        thread_scheme    => "pthreads",
        perlasm_scheme   => "elf",
        dso_scheme       => "dlfcn",
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
        shared_ldflag    => "-m64",
        shared_extension => ".so.\$(SHLIB_VERSION_NUMBER)",
        multilib         => "64",
    },
    "debug-linux-pentium" => {
        inherit_from     => [ 'BASE_unix', "x86_elf_asm" ],
        cc               => "gcc",
        cflags           => combine("-DBN_DEBUG -DREF_DEBUG -DCONF_DEBUG -DBN_CTX_DEBUG -DL_ENDIAN -g -mcpu=pentium -Wall",
                                    threads("-D_REENTRANT")),
        ex_libs          => add(" ","-ldl"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
    },
    "debug-linux-ppro" => {
        inherit_from     => [ 'BASE_unix', "x86_elf_asm" ],
        cc               => "gcc",
        cflags           => combine("-DBN_DEBUG -DREF_DEBUG -DCONF_DEBUG -DBN_CTX_DEBUG -DL_ENDIAN -g -mcpu=pentiumpro -Wall",
                                    threads("-D_REENTRANT")),
        ex_libs          => add(" ","-ldl"),
        bn_ops           => "BN_LLONG",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
    },
    "debug-linux-ia32-aes" => {
        inherit_from     => [ 'BASE_unix' ],
        cc               => "gcc",
        cflags           => combine("-DL_ENDIAN -O3 -fomit-frame-pointer -Wall",
                                    threads("-D_REENTRANT")),
        ex_libs          => add(" ","-ldl"),
        bn_ops           => "BN_LLONG",
        cpuid_asm_src    => "x86cpuid.s",
        bn_asm_src       => "bn-586.s co-586.s x86-mont.s",
        des_asm_src      => "des-586.s crypt586.s",
        aes_asm_src      => "aes_x86core.s aes_cbc.s aesni-x86.s",
        bf_asm_src       => "bf-586.s",
        md5_asm_src      => "md5-586.s",
        sha1_asm_src     => "sha1-586.s sha256-586.s sha512-586.s",
        cast_asm_src     => "cast-586.s",
        rc4_asm_src      => "rc4-586.s",
        rmd160_asm_src   => "rmd-586.s",
        rc5_asm_src      => "rc5-586.s",
        wp_asm_src       => "wp_block.s wp-mmx.s",
        modes_asm_src    => "ghash-x86.s",
        padlock_asm_src  => "e_padlock-x86.s",
        thread_scheme    => "pthreads",
        perlasm_scheme   => "elf",
        dso_scheme       => "dlfcn",
        shared_target    => "linux-shared",
        shared_cflag     => "-fPIC",
        shared_extension => ".so.\$(SHLIB_VERSION_NUMBER)",
    },
    "debug-test-64-clang" => {
        inherit_from     => [ 'BASE_unix', "x86_64_asm" ],
        cc               => "clang",
        cflags           => combine(join(' ', @gcc_devteam_warn),
                                    "-Wno-error=overlength-strings -Wno-error=extended-offsetof -Wno-error=language-extension-token -Wno-error=unused-const-variable -Wstrict-overflow -Qunused-arguments -DBN_DEBUG -DCONF_DEBUG -DDEBUG_UNUSED -g3 -O3 -pipe",
                                    threads("${BSDthreads}")),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        thread_scheme    => "pthreads",
        perlasm_scheme   => "elf",
        dso_scheme       => "dlfcn",
        shared_target    => "bsd-gcc-shared",
        shared_cflag     => "-fPIC",
        shared_extension => ".so.\$(SHLIB_VERSION_NUMBER)",
    },
    "darwin64-debug-test-64-clang" => {
        inherit_from     => [ 'BASE_unix', "x86_64_asm" ],
        cc               => "clang",
        cflags           => combine("-arch x86_64 -DL_ENDIAN",
                                    join(' ', @gcc_devteam_warn),
                                    "-Wno-error=overlength-strings -Wno-error=extended-offsetof -Wno-error=language-extension-token -Wno-error=unused-const-variable -Wstrict-overflow -Qunused-arguments -DBN_DEBUG -DCONF_DEBUG -DDEBUG_UNUSED -g3 -O3 -pipe",
                                    threads("${BSDthreads}")),
        sys_id           => "MACOSX",
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
        thread_scheme    => "pthreads",
        perlasm_scheme   => "macosx",
        dso_scheme       => "dlfcn",
        shared_target    => "darwin-shared",
        shared_cflag     => "-fPIC -fno-common",
        shared_ldflag    => "-arch x86_64 -dynamiclib",
        shared_extension => ".\$(SHLIB_VERSION_NUMBER).dylib",
    },
);
