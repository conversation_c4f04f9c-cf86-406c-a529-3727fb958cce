#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = PBKDF2 tests

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha1
Output = 0c60c80f961f0e71f3a9b524af6012062fe037a6

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha256
Output = 120fb6cffcf8b32c43e7225256c4f837a86548c92ccc35480805987cb70be17b

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha512
Output = 867f70cf1ade02cff3752599a3a53dc4af34c7a669815ae5d513554e1c8cf252c02d470a285a0501bad999bfe943c08f050235d7d68b1da55e63f73b60a57fce

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:2
Ctrl.digest = digest:sha1
Output = ea6c014dc72d6f8ccd1ed92ace1d41f0d8de8957

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:2
Ctrl.digest = digest:sha256
Output = ae4d0c95af6b46d32d0adff928f06dd02a303f8ef3c251dfd6e2d85a95474c43

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:2
Ctrl.digest = digest:sha512
Output = e1d9c16aa681708a45f5c7c4e215ceb66e011a2e9f0040713f18aefdb866d53cf76cab2868a39b9f7840edce4fef5a82be67335c77a6068e04112754f27ccf4e

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha1
Output = 4b007901b765489abead49d926f721d065a429c1

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha256
Output = c5e478d59288c841aa530db6845c4c8d962893a001ce4e11a4963873aa98134a

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha512
Output = d197b1b33db0143e018b12f3d1d1479e6cdebdcc97c5c0f87f6902e072f457b5143f30602641b3d55cd335988cb36b84376060ecd532e039b742a239434af2d5

KDF = PBKDF2
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALTsaltSALTsaltSALTsaltSALTsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha1
Output = 3d2eec4fe41c849b80c8d83662c0e44a8b291a964cf2f07038

KDF = PBKDF2
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALTsaltSALTsaltSALTsaltSALTsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha256
Output = 348c89dbcbd32b2f32d814b8116e84cf2b17347ebc1800181c4e2a1fb8dd53e1c635518c7dac47e9

KDF = PBKDF2
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALTsaltSALTsaltSALTsaltSALTsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha512
Output = 8c0511f4c6e597c6ac6315d8f0362e225f3c501495ba23b868c005174dc4ee71115b59f9e60cd9532fa33e0f75aefe30225c583a186cd82bd4daea9724a3d3b8

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.hexpass = hexpass:7061737300776f7264
Ctrl.hexsalt = hexsalt:7361006c74
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha1
Output = 56fa6aa75548099dcc37d7f03425e0c3

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.hexpass = hexpass:7061737300776f7264
Ctrl.hexsalt = hexsalt:7361006c74
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha256
Output = 89b69d0516f829893c696226650a8687

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.hexpass = hexpass:7061737300776f7264
Ctrl.hexsalt = hexsalt:7361006c74
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha512
Output = 9d9e9c4cd21fe4be24d5b8244c759665

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha3-224
Output = 691292bc3683d7d41ea2910f5b3eed23

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha3-256
Output = 778b6e237a0f49621549ff70d218d208

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha3-384
Output = 9a5f1e45e8b83f1b259ba72d11c59087

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:password
Ctrl.salt = salt:salt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha3-512
Output = 2bfaf2d5ceb6d10f5e262cd902488cfd

Title = PBKDF2 tests for empty inputs

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha1
Output = a33dddc30478185515311f8752895d36ea4363a2

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha256
Output = f135c27993baf98773c5cdb40a5706ce6a345cde

KDF = PBKDF2
Ctrl.pkcs5 = pkcs5:1
Ctrl.pass = pass:
Ctrl.salt = salt:salt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha512
Output = 00ef42cdbfc98d29db20976608e455567fdddf14
