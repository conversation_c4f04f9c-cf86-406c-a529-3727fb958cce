name: Bump BoringSSL version
permissions:
  contents: read

on:
  workflow_dispatch:
  schedule:
    # Run daily
    - cron: "0 0 * * *"

jobs:
  bump:
    if: github.repository_owner == 'pyca'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3.0.2
      - id: check-sha
        run: |
          SHA=$(git ls-remote https://boringssl.googlesource.com/boringssl refs/heads/master | cut -f1)
          if ! grep -q "$SHA" .github/workflows/ci.yml; then
            echo "::set-output name=BORING_SHA::$SHA"
          fi
          LAST_COMMIT=$(grep boringssl .github/workflows/ci.yml | grep TYPE | grep -oE '[a-z0-9]{40}')
          echo "::set-output name=LAST_COMMIT::$LAST_COMMIT"
      - name: Update boring
        run: |
          set -xe
          CURRENT_DATE=$(date "+%b %d, %Y")
          sed -E -i "s/Latest commit on the master branch.*/Latest commit on the master branch, as of ${CURRENT_DATE}./" .github/workflows/ci.yml
          sed -E -i "s/TYPE: \"boringssl\", VERSION: \"[0-9a-f]{40}\"/TYPE: \"boringssl\", VERSION: \"${{ steps.check-sha.outputs.BORING_SHA }}\"/" .github/workflows/ci.yml
          git status
        if: steps.check-sha.outputs.BORING_SHA
      - uses: tibdex/github-app-token@f717b5ecd4534d3c4df4ce9b5c1c2214f0f7cd06
        id: generate-token
        with:
          app_id: ${{ secrets.BORINGBOT_APP_ID }}
          private_key: ${{ secrets.BORINGBOT_PRIVATE_KEY }}
        if: steps.check-sha.outputs.BORING_SHA
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@18f90432bedd2afd6a825469ffd38aa24712a91d
        with:
          commit-message: "Bump BoringSSL version to ${{ steps.check-sha.outputs.BORING_SHA }}"
          title: "Bump BoringSSL version to ${{ steps.check-sha.outputs.BORING_SHA }}"
          author: "BoringSSL Bot <<EMAIL>>"
          body: |
            [Commit: ${{ steps.check-sha.outputs.BORING_SHA }}](https://boringssl.googlesource.com/boringssl/+/${{ steps.check-sha.outputs.BORING_SHA }})

            [Diff](https://boringssl.googlesource.com/boringssl/+/${{ steps.check-sha.outputs.LAST_COMMIT }}..${{ steps.check-sha.outputs.BORING_SHA }}) between the last commit hash merged to this repository and the new commit.
          token: ${{ steps.generate-token.outputs.token }}
        if: steps.check-sha.outputs.BORING_SHA
