[tox]
minversion = 2.4
isolated_build = True

[testenv]
extras =
    test
    ssh: ssh
deps =
    -e ./vectors
    pytest-shard>=0.1.2
    randomorder: pytest-randomly
passenv = ARCHFLAGS LDFLAGS CFLAGS INCLUDE LIB LD_LIBRARY_PATH USERNAME PYTHONIOENCODING RUSTFLAGS CARGO_TARGET_DIR LLVM_PROFILE_FILE OPENSSL_FORCE_FIPS_MODE
commands =
    pip list
    pytest -n auto --cov=cryptography --cov=tests --capture=no --strict-markers --durations=10 {posargs}

# This target disables coverage on pypy because of performance problems with
# coverage.py on pypy.
[testenv:pypy3-nocoverage]
basepython = pypy3
commands =
    pip list
    pytest -n auto --capture=no --strict-markers --durations=10 {posargs}

[testenv:docs]
extras =
    docs
    docstest
    sdist
basepython = python3
commands =
    sphinx-build -T -W -b html -d {envtmpdir}/doctrees docs docs/_build/html
    sphinx-build -T -W -b latex -d {envtmpdir}/doctrees docs docs/_build/latex
    sphinx-build -T -W -b doctest -d {envtmpdir}/doctrees docs docs/_build/html
    sphinx-build -T -W -b spelling docs docs/_build/html
    doc8 --allow-long-titles README.rst CHANGELOG.rst docs/ --ignore-path docs/_build/
    python setup.py sdist
    twine check dist/*

[testenv:docs-linkcheck]
extras =
    docs
basepython = python3
commands =
    sphinx-build -W -b linkcheck docs docs/_build/html

[testenv:flake]
basepython = python3
extras =
    pep8test
    test
    ssh
deps =
    mypy
    types-pytz
    check-manifest
commands =
    flake8 .
    black --check .
    check-manifest
    mypy src/cryptography/ vectors/cryptography_vectors/ tests/

[testenv:rust]
basepython = python3
changedir = src/rust/
allowlist_externals =
    cargo
commands =
    cargo fmt --all -- --check
    cargo clippy -- -D warnings
    cargo test --no-default-features

[flake8]
ignore = E203,E211,W503,W504,N818
exclude = .tox,*.egg,.git,_build,.hypothesis
select = E,W,F,N,I
application-import-names = cryptography,cryptography_vectors,tests

[doc8]
extensions = rst
