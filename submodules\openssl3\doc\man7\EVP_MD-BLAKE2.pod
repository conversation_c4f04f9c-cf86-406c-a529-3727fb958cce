=pod

=head1 NAME

EVP_MD-BLAKE2 - The BLAKE2 EVP_MD implementation

=head1 DESCRIPTION

Support for computing BLAKE2 digests through the B<EVP_MD> API.

=head2 Identities

This implementation is only available with the default provider, and
includes the following varieties:

=over 4

=item BLAKE2S-256

Known names are "<PERSON><PERSON>KE2S-256" and "BLAKE2s256".

=item BLAKE2B-512

Known names are "BLAKE2B-512" and "BLAKE2b512".

=back

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<provider-digest(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
