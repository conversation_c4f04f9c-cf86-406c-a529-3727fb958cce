# 多客户端连接测试设计文档

## 概述

多客户端连接测试 (`TestMultipleClientConnections`) 是针对 p2psocket.h 接口中服务器处理多个并发客户端连接能力的专门测试。

## 测试架构

### 整体设计
```
服务器线程 (MultiClientServerFunction)
    ├── 监听端口
    ├── 接受多个客户端连接 (P2pAccept)
    └── 为每个客户端创建处理线程 (HandleClientConnection)

客户端线程组 (ClientConnectionFunction × N)
    ├── 客户端 0: 连接 → 发送消息 → 验证回显
    ├── 客户端 1: 连接 → 发送消息 → 验证回显
    └── 客户端 N: 连接 → 发送消息 → 验证回显
```

## 核心组件

### 1. 全局状态管理
```cpp
// 多客户端测试专用变量
std::atomic<int> g_connected_clients(0);      // 已连接客户端数量
std::atomic<int> g_expected_clients(0);       // 期望客户端数量
std::vector<P2P_SOCKET> g_client_sockets;     // 客户端socket列表
std::mutex g_client_sockets_mutex;            // 保护socket列表的互斥锁
```

### 2. 服务器端实现 (MultiClientServerFunction)

#### 关键特性
- **非阻塞接受**: 使用 `P2pPoll` 设置超时，避免无限阻塞
- **并发处理**: 为每个客户端连接创建独立的处理线程
- **资源管理**: 维护客户端socket列表，确保正确清理

#### 核心逻辑
```cpp
// 使用轮询避免阻塞
PollEvent poll_event;
poll_event.events = P2PPOLLIN;
int poll_result = P2pPoll(g_server_socket, &poll_event, 2000); // 2秒超时

if (poll_result > 0 && (poll_event.revents & P2PPOLLIN)) {
    P2P_SOCKET client_socket = P2pAccept(g_server_socket, client_ip, sizeof(client_ip), &client_port);
    // 为客户端创建处理线程
    client_handlers.emplace_back(HandleClientConnection, client_socket, accepted_count);
}
```

### 3. 客户端处理 (HandleClientConnection)

#### 功能
- 独立处理每个客户端的数据交换
- 实现 echo 服务器功能
- 为每个客户端添加唯一标识

#### Echo 格式
```
客户端发送: "Message0FromClient1"
服务器回显: "Echo-Client1:Message0FromClient1"
```

### 4. 客户端连接 (ClientConnectionFunction)

#### 测试流程
1. 创建客户端socket
2. 设置连接超时
3. 连接到服务器
4. 发送3条测试消息
5. 验证每条消息的回显
6. 记录测试结果

#### 数据验证
- 每个客户端发送唯一的消息
- 验证回显消息的格式和内容
- 确保数据不会在客户端间混淆

## 同步机制

### 1. 启动同步
```cpp
// 等待服务器启动
while (!g_server_running && wait_count < 50) {
    SleepMs(100);
    wait_count++;
}
```

### 2. 连接错开
```cpp
// 客户端连接错开，避免同时连接
for (int i = 0; i < NUM_CLIENTS; i++) {
    client_threads.emplace_back(ClientConnectionFunction, ...);
    SleepMs(200); // 200ms延迟
}
```

### 3. 线程同步
```cpp
// 等待所有客户端完成
for (auto& client_thread : client_threads) {
    if (client_thread.joinable()) {
        client_thread.join();
    }
}
```

## 测试验证点

### 1. 连接验证
- ✅ 服务器成功启动并监听
- ✅ 所有客户端成功连接
- ✅ 服务器接受预期数量的连接

### 2. 数据隔离验证
- ✅ 每个客户端的消息独立处理
- ✅ 回显消息包含正确的客户端标识
- ✅ 客户端间数据不会混淆

### 3. 并发处理验证
- ✅ 服务器同时处理多个客户端
- ✅ 每个客户端独立的处理线程
- ✅ 并发读写操作正确执行

### 4. 资源管理验证
- ✅ 所有socket正确关闭
- ✅ 线程正确退出
- ✅ 内存资源正确释放

## 配置参数

### 默认设置
```cpp
const int NUM_CLIENTS = 3;           // 客户端数量
const int MESSAGES_PER_CLIENT = 3;   // 每客户端消息数
const int CONNECTION_TIMEOUT = 5000; // 连接超时(ms)
const int POLL_TIMEOUT = 2000;       // 轮询超时(ms)
const int STAGGER_DELAY = 200;       // 连接错开延迟(ms)
```

### 端口分配
```cpp
int test_port = g_config.test_port + 30; // 避免端口冲突
```

## 错误处理

### 1. 连接失败
- 客户端连接超时
- 服务器绑定失败
- 网络不可达

### 2. 数据传输错误
- 发送失败
- 接收超时
- 数据损坏

### 3. 资源错误
- Socket创建失败
- 线程创建失败
- 内存不足

## 性能考虑

### 1. 可扩展性
- 客户端数量可配置
- 消息数量可调整
- 超时时间可设置

### 2. 资源使用
- 每个客户端一个处理线程
- 合理的超时设置
- 及时的资源清理

### 3. 稳定性
- 错开连接避免竞争
- 超时机制防止死锁
- 异常处理确保清理

## 日志输出示例

```
[TEST] === Testing Multiple Client Connections ===
[TEST] Starting multi-client test with 3 clients on port 4463
[TEST] Multi-client server listening on port 4463
[TEST] Client 0 thread starting...
[TEST] Client 0 connected successfully
[TEST] Multi-client server accepted connection 0 from 127.0.0.1:xxxxx
[TEST] Client handler 0 started
[TEST] Client 0 sent: Message0FromClient0
[TEST] Client handler 0 received: Message0FromClient0
[TEST] Client handler 0 echoed: Echo-Client0:Message0FromClient0
[TEST] Client 0 received correct echo: Echo-Client0:Message0FromClient0
[INFO] PASS: Client 0 connection and data exchange
[INFO] PASS: All clients connected and exchanged data successfully
[TEST] Multi-client test cleanup completed
```

## 总结

多客户端连接测试全面验证了 p2psocket.h 接口在并发场景下的正确性，包括：

1. **并发连接处理**: 服务器能同时处理多个客户端连接
2. **数据隔离**: 每个客户端的数据独立处理，不会混淆
3. **资源管理**: 正确的socket和线程生命周期管理
4. **错误处理**: 优雅处理各种异常情况
5. **性能验证**: 在合理负载下的稳定运行

这个测试为 p2psocket 库在实际多客户端应用场景中的可靠性提供了重要保障。
