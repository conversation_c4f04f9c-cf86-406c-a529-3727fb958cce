=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-errstr - lookup error codes

=head1 SYNOPSIS

B<openssl errstr>
[B<-help>]
I<error_code...>

=head1 DESCRIPTION

Sometimes an application will not load error message texts and only
numerical forms will be available. This command can be
used to display the meaning of the hex code. The hex code is the hex digits
after the second colon.

=head1 OPTIONS

=over 4

=item B<-help>

Display a usage message.

=back

=head1 EXAMPLES

The error code:

 27594:error:2006D080:lib(32)::reason(128)::107:

can be displayed with:

 openssl errstr 2006D080

to produce the error message:

 error:2006D080:BIO routines::no such file

=head1 COPYRIGHT

Copyright 2004-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
