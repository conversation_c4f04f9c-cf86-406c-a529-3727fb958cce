<!-- Copyright 2017 Google Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
// -->

<!DOCTYPE html>
<title>Unit Tests of Web Crypto API</title>
<script src="../../../../javascript/closure/base.js"></script>
<script src="../../webcrypto_test_deps-runfiles.js"></script>
<script>
  goog.require('wycheproof.webcryptoapi');
  goog.require('wycheproof.webcryptoapi.AES-GCM');
  goog.require('wycheproof.webcryptoapi.ECDSA');
  goog.require('wycheproof.webcryptoapi.ECDH');
  goog.require('wycheproof.webcryptoapi.RSA-OAED');
  goog.require('wycheproof.webcryptoapi.RSASSA-PKCS1-V1_5');
</script>
<script>
function setUpPage(){
  wycheproof.webcryptoapi.setupPresubmitTests();
}
</script>

