=pod

=head1 NAME

X509_STORE_CTX_new, X509_STORE_CTX_cleanup, X509_STORE_CTX_free,
X509_STORE_CTX_init, X509_STORE_CTX_set0_trusted_stack, X509_STORE_CTX_set_cert,
X509_STORE_CTX_set0_crls,
X509_STORE_CTX_get0_chain, X509_STORE_CTX_set0_verified_chain,
X509_STORE_CTX_get0_param, X509_STORE_CTX_set0_param,
X509_STORE_CTX_get0_untrusted, X509_STORE_CTX_set0_untrusted,
X509_STORE_CTX_get_num_untrusted,
X509_STORE_CTX_set_default,
X509_STORE_CTX_set_verify,
X509_STORE_CTX_verify_fn,
X509_STORE_CTX_set_purpose,
X509_STORE_CTX_set_trust,
X509_STORE_CTX_purpose_inherit
- X509_STORE_CTX initialisation

=head1 SYNOPSIS

 #include <openssl/x509_vfy.h>

 X509_STORE_CTX *X509_STORE_CTX_new(void);
 void X509_STORE_CTX_cleanup(X509_STORE_CTX *ctx);
 void X509_STORE_CTX_free(X509_STORE_CTX *ctx);

 int X509_STORE_CTX_init(X509_STORE_CTX *ctx, X509_STORE *store,
                         X509 *x509, STACK_OF(X509) *chain);

 void X509_STORE_CTX_set0_trusted_stack(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);

 void X509_STORE_CTX_set_cert(X509_STORE_CTX *ctx, X509 *x);
 STACK_OF(X509) *X509_STORE_CTX_get0_chain(X509_STORE_CTX *ctx);
 void X509_STORE_CTX_set0_verified_chain(X509_STORE_CTX *ctx, STACK_OF(X509) *chain);
 void X509_STORE_CTX_set0_crls(X509_STORE_CTX *ctx, STACK_OF(X509_CRL) *sk);

 X509_VERIFY_PARAM *X509_STORE_CTX_get0_param(X509_STORE_CTX *ctx);
 void X509_STORE_CTX_set0_param(X509_STORE_CTX *ctx, X509_VERIFY_PARAM *param);
 int X509_STORE_CTX_set_default(X509_STORE_CTX *ctx, const char *name);

 STACK_OF(X509)* X509_STORE_CTX_get0_untrusted(X509_STORE_CTX *ctx);
 void X509_STORE_CTX_set0_untrusted(X509_STORE_CTX *ctx, STACK_OF(X509) *sk);

 int X509_STORE_CTX_get_num_untrusted(X509_STORE_CTX *ctx);

 typedef int (*X509_STORE_CTX_verify_fn)(X509_STORE_CTX *);
 void X509_STORE_CTX_set_verify(X509_STORE_CTX *ctx, X509_STORE_CTX_verify_fn verify);

 int X509_STORE_CTX_set_purpose(X509_STORE_CTX *ctx, int purpose);
 int X509_STORE_CTX_set_trust(X509_STORE_CTX *ctx, int trust);
 int X509_STORE_CTX_purpose_inherit(X509_STORE_CTX *ctx, int def_purpose,
                                    int purpose, int trust);

=head1 DESCRIPTION

These functions initialise an B<X509_STORE_CTX> structure for subsequent use
by X509_verify_cert().

X509_STORE_CTX_new() returns a newly initialised B<X509_STORE_CTX> structure.

X509_STORE_CTX_cleanup() internally cleans up an B<X509_STORE_CTX> structure.
The context can then be reused with a new call to X509_STORE_CTX_init().

X509_STORE_CTX_free() completely frees up B<ctx>. After this call B<ctx>
is no longer valid.
If B<ctx> is NULL nothing is done.

X509_STORE_CTX_init() sets up B<ctx> for a subsequent verification operation.
It must be called before each call to X509_verify_cert(), i.e. a B<ctx> is only
good for one call to X509_verify_cert(); if you want to verify a second
certificate with the same B<ctx> then you must call X509_STORE_CTX_cleanup()
and then X509_STORE_CTX_init() again before the second call to
X509_verify_cert(). The trusted certificate store is set to B<store>, the end
entity certificate to be verified is set to B<x509> and a set of additional
certificates (which will be untrusted but may be used to build the chain) in
B<chain>. Any or all of the B<store>, B<x509> and B<chain> parameters can be
B<NULL>.

X509_STORE_CTX_set0_trusted_stack() sets the set of trusted certificates of
B<ctx> to B<sk>. This is an alternative way of specifying trusted certificates
instead of using an B<X509_STORE>.

X509_STORE_CTX_set_cert() sets the certificate to be verified in B<ctx> to
B<x>.

X509_STORE_CTX_set0_verified_chain() sets the validated chain used
by B<ctx> to be B<chain>.
Ownership of the chain is transferred to B<ctx> and should not be
free'd by the caller.
X509_STORE_CTX_get0_chain() returns the internal pointer used by the
B<ctx> that contains the validated chain.

X509_STORE_CTX_set0_crls() sets a set of CRLs to use to aid certificate
verification to B<sk>. These CRLs will only be used if CRL verification is
enabled in the associated B<X509_VERIFY_PARAM> structure. This might be
used where additional "useful" CRLs are supplied as part of a protocol,
for example in a PKCS#7 structure.

X509_STORE_CTX_get0_param() retrieves an internal pointer
to the verification parameters associated with B<ctx>.

X509_STORE_CTX_get0_untrusted() retrieves an internal pointer to the
stack of untrusted certificates associated with B<ctx>.

X509_STORE_CTX_set0_untrusted() sets the internal point to the stack
of untrusted certificates associated with B<ctx> to B<sk>.

X509_STORE_CTX_set0_param() sets the internal verification parameter pointer
to B<param>. After this call B<param> should not be used.

X509_STORE_CTX_set_default() looks up and sets the default verification
method to B<name>. This uses the function X509_VERIFY_PARAM_lookup() to
find an appropriate set of parameters from B<name>.

X509_STORE_CTX_get_num_untrusted() returns the number of untrusted certificates
that were used in building the chain following a call to X509_verify_cert().

X509_STORE_CTX_set_verify() provides the capability for overriding the default
verify function. This function is responsible for verifying chain signatures and
expiration times.

A verify function is defined as an X509_STORE_CTX_verify type which has the
following signature:

 int (*verify)(X509_STORE_CTX *);

This function should receive the current X509_STORE_CTX as a parameter and
return 1 on success or 0 on failure.

X509 certificates may contain information about what purposes keys contained
within them can be used for. For example "TLS WWW Server Authentication" or
"Email Protection". This "key usage" information is held internally to the
certificate itself. In addition the trust store containing trusted certificates
can declare what purposes we trust different certificates for. This "trust"
information is not held within the certificate itself but is "meta" information
held alongside it. This "meta" information is associated with the certificate
after it is issued and could be determined by a system administrator. For
example a certificate might declare that it is suitable for use for both
"TLS WWW Server Authentication" and "TLS Client Authentication", but a system
administrator might only trust it for the former. An X.509 certificate extension
exists that can record extended key usage information to supplement the purpose
information described above. This extended mechanism is arbitrarily extensible
and not well suited for a generic library API; applications that need to
validate extended key usage information in certifiates will need to define a
custom "purpose" (see below) or supply a nondefault verification callback
(L<X509_STORE_set_verify_cb_func(3)>).

X509_STORE_CTX_set_purpose() sets the purpose for the target certificate being
verified in the I<ctx>. Built-in available values for the I<purpose> argument
are B<X509_PURPOSE_SSL_CLIENT>, B<X509_PURPOSE_SSL_SERVER>,
B<X509_PURPOSE_NS_SSL_SERVER>, B<X509_PURPOSE_SMIME_SIGN>,
B<X509_PURPOSE_SMIME_ENCRYPT>, B<X509_PURPOSE_CRL_SIGN>, B<X509_PURPOSE_ANY>,
B<X509_PURPOSE_OCSP_HELPER> and B<X509_PURPOSE_TIMESTAMP_SIGN>. It is also
possible to create a custom purpose value. Setting a purpose will ensure that
the key usage declared within certificates in the chain being verified is
consistent with that purpose as well as, potentially, other checks. Every
purpose also has an associated default trust value which will also be set at the
same time. During verification this trust setting will be verified to check it
is consistent with the trust set by the system administrator for certificates in
the chain.

X509_STORE_CTX_set_trust() sets the trust value for the target certificate
being verified in the I<ctx>. Built-in available values for the I<trust>
argument are B<X509_TRUST_COMPAT>, B<X509_TRUST_SSL_CLIENT>,
B<X509_TRUST_SSL_SERVER>, B<X509_TRUST_EMAIL>, B<X509_TRUST_OBJECT_SIGN>,
B<X509_TRUST_OCSP_SIGN>, B<X509_TRUST_OCSP_REQUEST> and B<X509_TRUST_TSA>. It is
also possible to create a custom trust value. Since X509_STORE_CTX_set_purpose()
also sets the trust value it is normally sufficient to only call that function.
If both are called then X509_STORE_CTX_set_trust() should be called after
X509_STORE_CTX_set_purpose() since the trust setting of the last call will be
used.

It should not normally be necessary for end user applications to call
X509_STORE_CTX_purpose_inherit() directly. Typically applications should call
X509_STORE_CTX_set_purpose() or X509_STORE_CTX_set_trust() instead. Using this
function it is possible to set the purpose and trust values for the I<ctx> at
the same time.
Both I<ctx> and its internal verification parameter pointer must not be NULL.
The I<def_purpose> and I<purpose> arguments can have the same
purpose values as described for X509_STORE_CTX_set_purpose() above. The I<trust>
argument can have the same trust values as described in
X509_STORE_CTX_set_trust() above. Any of the I<def_purpose>, I<purpose> or
I<trust> values may also have the value 0 to indicate that the supplied
parameter should be ignored. After calling this function the purpose to be used
for verification is set from the I<purpose> argument unless the purpose was
already set in I<ctx> before, and the trust is set from the I<trust> argument
unless the trust was already set in I<ctx> before.
If I<trust> is 0 then the trust value will be set from
the default trust value for I<purpose>. If the default trust value for the
purpose is I<X509_TRUST_DEFAULT> and I<trust> is 0 then the default trust value
associated with the I<def_purpose> value is used for the trust setting instead.

=head1 NOTES

The certificates and CRLs in a store are used internally and should B<not>
be freed up until after the associated B<X509_STORE_CTX> is freed.

=head1 BUGS

The certificates and CRLs in a context are used internally and should B<not>
be freed up until after the associated B<X509_STORE_CTX> is freed. Copies
should be made or reference counts increased instead.

=head1 RETURN VALUES

X509_STORE_CTX_new() returns a newly allocated context or B<NULL> if an
error occurred.

X509_STORE_CTX_init() returns 1 for success or 0 if an error occurred.

X509_STORE_CTX_get0_param() returns a pointer to an B<X509_VERIFY_PARAM>
structure or B<NULL> if an error occurred.

X509_STORE_CTX_cleanup(), X509_STORE_CTX_free(),
X509_STORE_CTX_set0_trusted_stack(),
X509_STORE_CTX_set_cert(),
X509_STORE_CTX_set0_crls() and X509_STORE_CTX_set0_param() do not return
values.

X509_STORE_CTX_set_default() returns 1 for success or 0 if an error occurred.

X509_STORE_CTX_get_num_untrusted() returns the number of untrusted certificates
used.

=head1 SEE ALSO

L<X509_verify_cert(3)>
L<X509_VERIFY_PARAM_set_flags(3)>

=head1 HISTORY

The X509_STORE_CTX_set0_crls() function was added in OpenSSL 1.0.0.
The X509_STORE_CTX_get_num_untrusted() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2009-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
