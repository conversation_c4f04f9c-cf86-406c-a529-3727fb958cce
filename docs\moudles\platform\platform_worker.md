platform_worker.c 是 MsQuic 平台抽象层的一部分，主要用于实现 **每处理器工作线程（Worker Threads）** 的管理和调度。它提供了跨平台的工作线程支持，负责处理异步任务、事件队列以及动态资源管理。

---

### **文件的主要作用**

1. **工作线程管理**:
   - 定义和管理每个处理器的工作线程（`CXPLAT_WORKER`）。
   - 提供线程的创建、运行、停止和销毁逻辑。

2. **事件队列处理**:
   - 使用事件队列（`CXPLAT_EVENTQ`）驱动工作线程的执行。
   - 支持事件的提交、处理和完成回调。

3. **执行上下文管理**:
   - 管理执行上下文（`CXPLAT_EXECUTION_CONTEXT`），包括动态添加、更新和运行。
   - 支持多任务调度和优先级管理。

4. **动态资源管理**:
   - 管理动态资源池（`CXPLAT_POOL_EX`），包括资源的分配、释放和定期清理。

5. **跨平台支持**:
   - 提供与平台无关的工作线程和事件队列抽象，适配不同操作系统的线程和事件机制。

---

### **文件中的关键内容**

#### **1. 核心结构体**

- **`CXPLAT_WORKER`**:
  - 表示单个工作线程的核心数据结构。
  - 包含线程、事件队列、锁、动态资源池等信息。
  - **关键字段**:
    - `CXPLAT_THREAD Thread`: 工作线程实例。
    - `CXPLAT_EVENTQ EventQ`: 事件队列，用于驱动线程执行。
    - `CXPLAT_LOCK ECLock`: 用于保护执行上下文的锁。
    - `CXPLAT_SLIST_ENTRY* ExecutionContexts`: 当前活跃的执行上下文列表。
    - `BOOLEAN Running`: 指示线程是否正在运行。

- **`CXPLAT_WORKER_POOL`**:
  - 表示工作线程池的抽象。
  - 管理多个工作线程及其事件队列。

---

#### **2. 核心函数**

##### **工作线程管理**

- **`CxPlatWorkerPoolInit`**:
  - 初始化工作线程池。
  - **主要逻辑**:
    - 初始化线程池的锁和其他必要资源。

- **`CxPlatWorkerPoolLazyStart`**:
  - 延迟启动工作线程池。
  - **主要逻辑**:
    - 根据配置动态创建线程。
    - 初始化每个线程的事件队列和相关资源。

- **`CxPlatWorkerPoolUninit`**:
  - 停止并销毁工作线程池。
  - **主要逻辑**:
    - 停止所有工作线程。
    - 清理线程池中的资源。

- **`CxPlatWorkerThread`**:
  - 工作线程的主函数。
  - **主要逻辑**:
    - 循环处理事件队列中的任务。
    - 调用 `CxPlatRunExecutionContexts` 运行执行上下文。
    - 定期清理动态资源池。

---

##### **事件队列处理**

- **`CxPlatEventQInitialize`**:
  - 初始化事件队列。
  - **主要逻辑**:
    - 创建事件队列实例。
    - 配置事件队列的提交条目。

- **`CxPlatProcessEvents`**:
  - 处理事件队列中的事件。
  - **主要逻辑**:
    - 从事件队列中取出事件。
    - 调用事件的完成回调函数。

---

##### **执行上下文管理**

- **`CxPlatAddExecutionContext`**:
  - 添加执行上下文到工作线程。
  - **主要逻辑**:
    - 将上下文加入到线程的待处理队列中。
    - 触发线程更新其执行上下文。

- **`CxPlatUpdateExecutionContexts`**:
  - 更新工作线程的执行上下文。
  - **主要逻辑**:
    - 将待处理队列中的上下文移动到活跃队列中。

- **`CxPlatRunExecutionContexts`**:
  - 运行工作线程的执行上下文。
  - **主要逻辑**:
    - 遍历执行上下文列表，调用回调函数处理任务。
    - 根据上下文的状态决定是否继续运行或移除。

---

##### **动态资源管理**

- **`CxPlatAddDynamicPoolAllocator`**:
  - 将动态资源池分配器添加到工作线程。
  - **主要逻辑**:
    - 将资源池加入到线程的动态资源池列表中。

- **`CxPlatProcessDynamicPoolAllocators`**:
  - 定期清理动态资源池。
  - **主要逻辑**:
    - 遍历动态资源池列表，释放不再需要的资源。

---

### **文件的跨平台特性**

- **线程管理**:
  - 使用 `CXPLAT_THREAD` 抽象，适配不同平台的线程实现。
- **事件队列**:
  - 使用 `CXPLAT_EVENTQ` 抽象，适配不同平台的事件机制（如 Windows 的 IOCP，Linux 的 epoll）。
- **锁和同步**:
  - 使用 `CXPLAT_LOCK` 提供跨平台的锁实现。

---

### **文件的核心功能模块**

#### **1. 工作线程管理**
- 提供跨平台的工作线程支持。
- 每个工作线程绑定到一个理想处理器（`IdealProcessor`），以优化性能。

#### **2. 事件队列驱动**
- 使用事件队列（`CXPLAT_EVENTQ`）驱动线程的执行。
- 支持事件的异步提交和回调处理。

#### **3. 执行上下文调度**
- 管理多个执行上下文（`CXPLAT_EXECUTION_CONTEXT`）。
- 支持动态添加、更新和运行上下文。

#### **4. 动态资源管理**
- 管理动态资源池（`CXPLAT_POOL_EX`），支持资源的分配和定期清理。

#### **5. 调试支持**
- 提供调试统计信息（如线程循环次数、事件处理次数等）。
- 在调试模式下验证线程的启动和停止状态。

---

### **总结**

platform_worker.c 是 MsQuic 平台抽象层的重要组成部分，负责实现跨平台的工作线程和事件队列管理。它的主要功能包括：
1. **工作线程的创建、运行和销毁**。
2. **事件队列的驱动和任务调度**。
3. **执行上下文的动态管理**。
4. **动态资源池的分配和清理**。

通过这些功能，`platform_worker.c` 为 MsQuic 提供了高效的多线程任务调度和资源管理支持，确保其在多种操作系统上的高性能运行。

### CXPLAT_WORKER
CXPLAT_WORKER 是 MsQuic 平台抽象层中的一个核心结构体，表示 每处理器工作线程（Worker Thread） 的抽象。以下是对 CXPLAT_WORKER 结构体的详细内容：

```c
typedef struct QUIC_CACHEALIGN CXPLAT_WORKER {

    //
    // 用于驱动工作的线程。
    //
    CXPLAT_THREAD Thread;

    //
    // 用于驱动执行的事件队列。
    //
    CXPLAT_EVENTQ EventQ;

    //
    // 用于关闭工作线程的提交队列条目。
    //
    CXPLAT_SQE ShutdownSqe;

    //
    // 用于唤醒线程以进行轮询的提交队列条目。
    //
    CXPLAT_SQE WakeSqe;

    //
    // 用于更新轮询集的提交队列条目。
    //
    CXPLAT_SQE UpdatePollSqe;

    //
    // 用于序列化对执行上下文的访问的锁。
    //
    CXPLAT_LOCK ECLock;

    //
    // 管理的动态资源池列表。
    //
    CXPLAT_LIST_ENTRY DynamicPoolList;

    //
    // 等待添加到 CXPLAT_WORKER::ExecutionContexts 的执行上下文列表。
    //
    CXPLAT_SLIST_ENTRY* PendingECs;

    //
    // 当前活跃的执行上下文集合。
    //
    CXPLAT_SLIST_ENTRY* ExecutionContexts;

#if DEBUG // 调试统计信息
    uint64_t LoopCount;     // 线程循环次数
    uint64_t EcPollCount;   // 执行上下文轮询次数
    uint64_t EcRunCount;    // 执行上下文运行次数
    uint64_t CqeCount;      // 处理的完成队列条目数量
#endif

    //
    // 工作线程的理想处理器。
    //
    uint16_t IdealProcessor;

    //
    // 标志，用于指示已初始化的内容。
    //
    BOOLEAN InitializedEventQ : 1;        // 是否初始化了事件队列
    BOOLEAN InitializedShutdownSqe : 1;  // 是否初始化了关闭提交队列条目
    BOOLEAN InitializedWakeSqe : 1;      // 是否初始化了唤醒提交队列条目
    BOOLEAN InitializedUpdatePollSqe : 1;// 是否初始化了更新轮询提交队列条目
    BOOLEAN InitializedThread : 1;       // 是否初始化了线程
    BOOLEAN InitializedECLock : 1;       // 是否初始化了执行上下文锁
    BOOLEAN StoppingThread : 1;          // 是否正在停止线程
    BOOLEAN StoppedThread : 1;           // 是否已停止线程
    BOOLEAN DestroyedThread : 1;         // 是否已销毁线程
#if DEBUG // 调试标志 - 不应放入位字段中
    BOOLEAN ThreadStarted;               // 线程是否已启动
    BOOLEAN ThreadFinished;              // 线程是否已完成
#endif

    BOOLEAN Running;                     // 线程是否正在运行

} CXPLAT_WORKER;
```

下面看看worker中核心函数在各平台下的实现，主要关注不同平台的实现：

---

### **CxPlatWorkerPoolInit**
```c
void
CxPlatWorkerPoolInit(
    _In_ CXPLAT_WORKER_POOL* WorkerPool
    )
{
    CXPLAT_DBG_ASSERT(WorkerPool); // 检查输入参数
    CxPlatZeroMemory(WorkerPool, sizeof(*WorkerPool)); // 清零线程池结构体
    CxPlatLockInitialize(&WorkerPool->WorkerLock); // 初始化线程池锁
}
```
#### CxPlatLockInitialize和相关函数各平台实现
- Windows user
```c
typedef CRITICAL_SECTION CXPLAT_LOCK;

#define CxPlatLockInitialize(Lock) InitializeCriticalSection(Lock)
#define CxPlatLockUninitialize(Lock) DeleteCriticalSection(Lock)
#define CxPlatLockAcquire(Lock) EnterCriticalSection(Lock)
#define CxPlatLockRelease(Lock) LeaveCriticalSection(Lock)
```
- Windows kernel
```c
typedef EX_PUSH_LOCK CXPLAT_LOCK;

#define CxPlatLockInitialize(Lock) ExInitializePushLock(Lock)
#define CxPlatLockUninitialize(Lock)
#define CxPlatLockAcquire(Lock) KeEnterCriticalRegion(); ExAcquirePushLockExclusive(Lock)
#define CxPlatLockRelease(Lock) ExReleasePushLockExclusive(Lock); KeLeaveCriticalRegion()
```
- posix
```c
typedef struct CXPLAT_LOCK {

    alignas(16) pthread_mutex_t Mutex;

} CXPLAT_LOCK;

#define CxPlatLockInitialize(Lock) { \
    pthread_mutexattr_t Attr; \
    CXPLAT_FRE_ASSERT(pthread_mutexattr_init(&Attr) == 0); \
    CXPLAT_FRE_ASSERT(pthread_mutexattr_settype(&Attr, PTHREAD_MUTEX_RECURSIVE) == 0); \
    CXPLAT_FRE_ASSERT(pthread_mutex_init(&(Lock)->Mutex, &Attr) == 0); \
    CXPLAT_FRE_ASSERT(pthread_mutexattr_destroy(&Attr) == 0); \
}

#define CxPlatLockUninitialize(Lock) \
        CXPLAT_FRE_ASSERT(pthread_mutex_destroy(&(Lock)->Mutex) == 0);

#define CxPlatLockAcquire(Lock) \
    CXPLAT_FRE_ASSERT(pthread_mutex_lock(&(Lock)->Mutex) == 0);

#define CxPlatLockRelease(Lock) \
    CXPLAT_FRE_ASSERT(pthread_mutex_unlock(&(Lock)->Mutex) == 0);
```

---

### CxPlatWorkerPoolLazyStart
```c
BOOLEAN
CxPlatWorkerPoolLazyStart(
    _In_ CXPLAT_WORKER_POOL* WorkerPool,
    _In_opt_ QUIC_EXECUTION_CONFIG* Config
    )
{
    //给WorkerPool加锁访问
    CxPlatLockAcquire(&WorkerPool->WorkerLock);
    if (WorkerPool->Workers != NULL) {
        CxPlatLockRelease(&WorkerPool->WorkerLock);
        return TRUE;
    }

    // 设置处理器列表
    if (Config && Config->ProcessorCount) {
        WorkerPool->WorkerCount = Config->ProcessorCount;
    } else {
        //如果Config->ProcessorCount没有设置，那么使用CxPlatProcCount()获取
        WorkerPool->WorkerCount = CxPlatProcCount();
    }

    // 分配工作线程数组
    WorkerPool->Workers = (CXPLAT_WORKER*)CXPLAT_ALLOC_PAGED(
        sizeof(CXPLAT_WORKER) * WorkerPool->WorkerCount, QUIC_POOL_PLATFORM_WORKER);
    if (WorkerPool->Workers == NULL) {
        goto Error;
    }

    // 初始化每个工作线程
    for (uint32_t i = 0; i < WorkerPool->WorkerCount; ++i) {
        CXPLAT_WORKER* Worker = &WorkerPool->Workers[i];
        CxPlatLockInitialize(&Worker->ECLock);
        if (!CxPlatEventQInitialize(&Worker->EventQ)) {
            goto Error;
        }
        if (QUIC_FAILED(CxPlatThreadCreate(&ThreadConfig, &Worker->Thread))) {
            goto Error;
        }
    }

    //创建工作线程
    CxPlatThreadCreate(&ThreadConfig, &Worker->Thread)

    //初始化rundown，相关函数CxPlatRundownReleaseAndWait
    CxPlatRundownInitialize(&WorkerPool->Rundown);
    CxPlatLockRelease(&WorkerPool->WorkerLock);
    return TRUE;

Error:
    // 清理逻辑
    CxPlatLockRelease(&WorkerPool->WorkerLock);
    return FALSE;
}
```
![CxPlatWorkerPoolLazyStart](./pic/CxPlatWorkerPoolLazyStart.png)

### CxPlatProcCount()在各平台获取的值不同
- Windows user  
```c
    CxPlatProcessorCount = 0;
    for (WORD i = 0; i < Info->Group.ActiveGroupCount; ++i) {
        CxPlatProcessorGroupInfo[i].Mask = Info->Group.GroupInfo[i].ActiveProcessorMask;
        CxPlatProcessorGroupInfo[i].Count = Info->Group.GroupInfo[i].ActiveProcessorCount;
        CxPlatProcessorGroupInfo[i].Offset = CxPlatProcessorCount;
        CxPlatProcessorCount += Info->Group.GroupInfo[i].ActiveProcessorCount;
```
- windows kernel
```c
    CxPlatProcessorCount =
        (uint32_t)KeQueryActiveProcessorCountEx(ALL_PROCESSOR_GROUPS);
```
- darwin
```c
    CxPlatProcessorCount = 1;
```
- other
```c
    CxPlatProcessorCount = (uint32_t)sysconf(_SC_NPROCESSORS_ONLN);
```

### CxPlatEventQInitialize在各平台下的实现
- windows user
```c
BOOLEAN
CxPlatEventQInitialize(
    _Out_ CXPLAT_EVENTQ* queue
    )
{
    return (*queue = CreateIoCompletionPort(INVALID_HANDLE_VALUE, NULL, 0, 1)) != NULL;
}
```
- windows kernel
```c
BOOLEAN
CxPlatEventQInitialize(
    _Out_ CXPLAT_EVENTQ* queue
    )
{
    KeInitializeEvent(queue, SynchronizationEvent, FALSE);
    return TRUE;
}
```
- linux  
根据宏定义`CXPLAT_USE_IO_URING`可以使用io_uring或者epoll  

io_uring
```c
BOOLEAN
CxPlatEventQInitialize(
    _Out_ CXPLAT_EVENTQ* queue
    )
{
    return 0 == io_uring_queue_init(256, queue, 0); // TODO - make size configurable
}
```
epoll
```c
BOOLEAN
CxPlatEventQInitialize(
    _Out_ CXPLAT_EVENTQ* queue
    )
{
    return (*queue = epoll_create1(EPOLL_CLOEXEC)) != -1;
}
```

- darwin
```c
BOOLEAN
CxPlatEventQInitialize(
    _Out_ CXPLAT_EVENTQ* queue
    )
{
    return (*queue = kqueue()) != -1;
}
```

---

### CxPlatThreadCreate在各平台下的实现
- windows user
```c
QUIC_STATUS
CxPlatThreadCreate(
    _In_ CXPLAT_THREAD_CONFIG* Config,
    _Out_ CXPLAT_THREAD* Thread
    )
{
  //通过CXPLAT_USE_CUSTOM_THREAD_CONTEXT宏来决定是否使用自定义的thread和context，
  //如果使用，可以在工作线程创建时执行额外的自定义逻辑
  //自定义函数名为CxPlatThreadCustomStart
#ifdef CXPLAT_USE_CUSTOM_THREAD_CONTEXT

        //创建工作线程
        CreateThread(
            NULL,
            0,
            CxPlatThreadCustomStart,
            CustomContext,
            0,
            NULL);
#else // CXPLAT_USE_CUSTOM_THREAD_CONTEXT

        //创建工作线程
        CreateThread(
            NULL,
            0,
            Config->Callback,
            Config->Context,
            0,
            NULL);
    }
#endif // CXPLAT_USE_CUSTOM_THREAD_CONTEXT

    //CXPLAT_THREAD_FLAG_SET_AFFINITIZE
    //绑定线程到指定处理器或处理器组
    CXPLAT_DBG_ASSERT(Config->IdealProcessor < CxPlatProcCount());
    const CXPLAT_PROCESSOR_INFO* ProcInfo = &CxPlatProcessorInfo[Config->IdealProcessor];
    GROUP_AFFINITY Group = {0};
    if (Config->Flags & CXPLAT_THREAD_FLAG_SET_AFFINITIZE) {
        Group.Mask = (KAFFINITY)(1ull << ProcInfo->Index);          // Fixed processor
    } else {
        Group.Mask = CxPlatProcessorGroupInfo[ProcInfo->Group].Mask;
    }
    Group.Group = ProcInfo->Group;
    if (!SetThreadGroupAffinity(*Thread, &Group, NULL)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            GetLastError(),
            "SetThreadGroupAffinity");
    }

    //CXPLAT_THREAD_FLAG_SET_IDEAL_PROC
    //设置线程的 理想处理器（Ideal Processor）。
    //理想处理器是操作系统在调度线程时优先选择的处理器，
    //但线程并不被严格限制在该处理器上运行。
    if (Config->Flags & CXPLAT_THREAD_FLAG_SET_IDEAL_PROC &&
        !SetThreadIdealProcessorEx(*Thread, (PROCESSOR_NUMBER*)ProcInfo, NULL)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            GetLastError(),
            "SetThreadIdealProcessorEx");
    }

    //CXPLAT_THREAD_FLAG_HIGH_PRIORITY
    //设置线程优先级为最高
    if (Config->Flags & CXPLAT_THREAD_FLAG_HIGH_PRIORITY &&
        !SetThreadPriority(*Thread, THREAD_PRIORITY_HIGHEST)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            GetLastError(),
            "SetThreadPriority");
    }
    if (Config->Name) {
        WCHAR WideName[64] = L"";
        size_t WideNameLength;
        mbstowcs_s(
            &WideNameLength,
            WideName,
            ARRAYSIZE(WideName) - 1,
            Config->Name,
            _TRUNCATE);

//设置线程描述信息
#if defined(QUIC_RESTRICTED_BUILD)
        SetThreadDescription(*Thread, WideName);
#else
        THREAD_NAME_INFORMATION_PRIVATE ThreadNameInfo;
        RtlInitUnicodeString(&ThreadNameInfo.ThreadName, WideName);
        NtSetInformationThread(
            *Thread,
            ThreadNameInformationPrivate,
            &ThreadNameInfo,
            sizeof(ThreadNameInfo));
#endif
    }
    return QUIC_STATUS_SUCCESS;
}
```

- windows kernel
```c
inline
QUIC_STATUS
CxPlatThreadCreate(
    _In_ CXPLAT_THREAD_CONFIG* Config,
    _Out_ CXPLAT_THREAD* Thread
    )
{
    QUIC_STATUS Status;
    HANDLE ThreadHandle;
    Status =
        PsCreateSystemThread(
            &ThreadHandle,
            THREAD_ALL_ACCESS,
            NULL,
            NULL,
            NULL,
            Config->Callback,
            Config->Context);
    CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
    if (QUIC_FAILED(Status)) {
        *Thread = NULL;
        goto Error;
    }
    Status =
        ObReferenceObjectByHandle(
            ThreadHandle,
            THREAD_ALL_ACCESS,
            *PsThreadType,
            KernelMode,
            (void**)Thread,
            NULL);
    CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
    if (QUIC_FAILED(Status)) {
        *Thread = NULL;
        goto Cleanup;
    }
    PROCESSOR_NUMBER Processor, IdealProcessor;
    Status =
        KeGetProcessorNumberFromIndex(
            Config->IdealProcessor,
            &Processor);
    if (QUIC_FAILED(Status)) {
        Status = QUIC_STATUS_SUCCESS; // Currently we don't treat this as fatal
        goto SetPriority;             // TODO: Improve this logic.
    }
    IdealProcessor = Processor;
    if (Config->Flags & CXPLAT_THREAD_FLAG_SET_AFFINITIZE) {
        GROUP_AFFINITY Affinity;
        CxPlatZeroMemory(&Affinity, sizeof(Affinity));
        Affinity.Group = Processor.Group;
        Affinity.Mask = (1ull << Processor.Number);
        Status =
            ZwSetInformationThread(
                ThreadHandle,
                ThreadGroupInformation,
                &Affinity,
                sizeof(Affinity));
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        if (QUIC_FAILED(Status)) {
            goto Cleanup;
        }
    } else { // NUMA Node Affinity
        SYSTEM_LOGICAL_PROCESSOR_INFORMATION_EX Info;
        ULONG InfoLength = sizeof(Info);
        Status =
            KeQueryLogicalProcessorRelationship(
                &Processor,
                RelationNumaNode,
                &Info,
                &InfoLength);
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        if (QUIC_FAILED(Status)) {
            goto Cleanup;
        }
        Status =
            ZwSetInformationThread(
                ThreadHandle,
                ThreadGroupInformation,
                &Info.NumaNode.GroupMask,
                sizeof(GROUP_AFFINITY));
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        if (QUIC_FAILED(Status)) {
            goto Cleanup;
        }
    }
    if (Config->Flags & CXPLAT_THREAD_FLAG_SET_IDEAL_PROC) {
        Status =
            ZwSetInformationThread(
                ThreadHandle,
                ThreadIdealProcessorEx,
                &IdealProcessor, // Don't pass in Processor because this overwrites on output.
                sizeof(IdealProcessor));
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        if (QUIC_FAILED(Status)) {
            goto Cleanup;
        }
    }
SetPriority:
    if (Config->Flags & CXPLAT_THREAD_FLAG_HIGH_PRIORITY) {
        KeSetBasePriorityThread(
            (PKTHREAD)(*Thread),
            IO_NETWORK_INCREMENT + 1);
    }
    if (Config->Name) {
        DECLARE_UNICODE_STRING_SIZE(UnicodeName, 64);
        ULONG UnicodeNameLength = 0;
        Status =
            RtlUTF8ToUnicodeN(
                UnicodeName.Buffer,
                UnicodeName.MaximumLength,
                &UnicodeNameLength,
                Config->Name,
                (ULONG)strnlen(Config->Name, 64));
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        UnicodeName.Length = (USHORT)UnicodeNameLength;
#define ThreadNameInformation ((THREADINFOCLASS)38)
        Status =
            ZwSetInformationThread(
                ThreadHandle,
                ThreadNameInformation,
                &UnicodeName,
                sizeof(UNICODE_STRING));
        CXPLAT_DBG_ASSERT(QUIC_SUCCEEDED(Status));
        Status = QUIC_STATUS_SUCCESS;
    }
Cleanup:
    ZwClose(ThreadHandle);
Error:
    return Status;
}
```

- linux
```c
QUIC_STATUS
CxPlatThreadCreate(
    _In_ CXPLAT_THREAD_CONFIG* Config,
    _Out_ CXPLAT_THREAD* Thread
    )
{
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;

    pthread_attr_t Attr;
    if (pthread_attr_init(&Attr)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            errno,
            "pthread_attr_init failed");
        return errno;
    }

//glibc下才支持CXPLAT_THREAD_FLAG_SET_AFFINITIZE标志，android下不支持
#ifdef __GLIBC__
    if (Config->Flags & CXPLAT_THREAD_FLAG_SET_AFFINITIZE) {
        cpu_set_t CpuSet;
        CPU_ZERO(&CpuSet);
        CPU_SET(Config->IdealProcessor, &CpuSet);
        if (pthread_attr_setaffinity_np(&Attr, sizeof(CpuSet), &CpuSet)) {
            QuicTraceEvent(
                LibraryError,
                "[ lib] ERROR, %s.",
                "pthread_attr_setaffinity_np failed");
        }
    } else {
        // TODO - Set Linux equivalent of NUMA affinity.
    }
    //Config->Flags不支持CXPLAT_THREAD_FLAG_SET_IDEAL_PROC标志
    // There is no way to set an ideal processor in Linux.
#endif
    //支持CXPLAT_THREAD_FLAG_HIGH_PRIORITY标志
    if (Config->Flags & CXPLAT_THREAD_FLAG_HIGH_PRIORITY) {
        struct sched_param Params;
        Params.sched_priority = sched_get_priority_max(SCHED_FIFO);
        if (pthread_attr_setschedparam(&Attr, &Params)) {
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                errno,
                "pthread_attr_setschedparam failed");
        }
    }

//支持自定义的工作线程函数CxPlatThreadCustomStart
#ifdef CXPLAT_USE_CUSTOM_THREAD_CONTEXT

    CXPLAT_THREAD_CUSTOM_CONTEXT* CustomContext =
        CXPLAT_ALLOC_NONPAGED(sizeof(CXPLAT_THREAD_CUSTOM_CONTEXT), QUIC_POOL_CUSTOM_THREAD);
    if (CustomContext == NULL) {
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "Custom thread context",
            sizeof(CXPLAT_THREAD_CUSTOM_CONTEXT));
    }
    CustomContext->Callback = Config->Callback;
    CustomContext->Context = Config->Context;

    if (pthread_create(Thread, &Attr, CxPlatThreadCustomStart, CustomContext)) {
        Status = errno;
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "pthread_create failed");
        CXPLAT_FREE(CustomContext, QUIC_POOL_CUSTOM_THREAD);
    }

#else // CXPLAT_USE_CUSTOM_THREAD_CONTEXT

    //
    // If pthread_create fails with an error code, then try again without the attribute
    // because the CPU might be offline.
    //
    if (pthread_create(Thread, &Attr, Config->Callback, Config->Context)) {
        QuicTraceLogWarning(
            PlatformThreadCreateFailed,
            "[ lib] pthread_create failed, retrying without affinitization");
        if (pthread_create(Thread, NULL, Config->Callback, Config->Context)) {
            Status = errno;
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                Status,
                "pthread_create failed");
        }
    }

#endif // !CXPLAT_USE_CUSTOM_THREAD_CONTEXT

#if !defined(__ANDROID__)
    if (Status == QUIC_STATUS_SUCCESS) {
      //android下不支持CXPLAT_THREAD_FLAG_SET_AFFINITIZE
        if (Config->Flags & CXPLAT_THREAD_FLAG_SET_AFFINITIZE) {
            cpu_set_t CpuSet;
            CPU_ZERO(&CpuSet);
            CPU_SET(Config->IdealProcessor, &CpuSet);
            if (pthread_setaffinity_np(*Thread, sizeof(CpuSet), &CpuSet)) {
                QuicTraceEvent(
                    LibraryError,
                    "[ lib] ERROR, %s.",
                    "pthread_setaffinity_np failed");
            }
#ifdef CXPLAT_NUMA_AWARE
        } else if (CxPlatNumaNodeCount != 0) {
          //numa下默认处置ideal processor
            int IdealNumaNode = numa_node_of_cpu((int)Config->IdealProcessor);
            if (pthread_setaffinity_np(*Thread, sizeof(cpu_set_t), &CxPlatNumaNodeMasks[IdealNumaNode])) {
                QuicTraceEvent(
                    LibraryError,
                    "[ lib] ERROR, %s.",
                    "pthread_setaffinity_np failed");
            }
#endif
        }
    }
#endif

    pthread_attr_destroy(&Attr);

    return Status;
}
```

- darwin
```c
QUIC_STATUS
CxPlatThreadCreate(
    _In_ CXPLAT_THREAD_CONFIG* Config,
    _Out_ CXPLAT_THREAD* Thread
    )
{
    QUIC_STATUS Status = QUIC_STATUS_SUCCESS;
    pthread_attr_t Attr;
    if (pthread_attr_init(&Attr)) {
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            errno,
            "pthread_attr_init failed");
        return errno;
    }

    // XXX: Set processor affinity

    if (Config->Flags & CXPLAT_THREAD_FLAG_HIGH_PRIORITY) {
        struct sched_param Params;
        Params.sched_priority = sched_get_priority_max(SCHED_FIFO);
        if (!pthread_attr_setschedparam(&Attr, &Params)) {
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                errno,
                "pthread_attr_setschedparam failed");
        }
    }

    if (pthread_create(Thread, &Attr, Config->Callback, Config->Context)) {
        Status = errno;
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "pthread_create failed");
    }

    pthread_attr_destroy(&Attr);

    return Status;
}
```

### **CxPlatWorkerThread**
```c
CXPLAT_THREAD_CALLBACK(CxPlatWorkerThread, Context)
```
下面看看默认工作线程的流程
1. 初始化线程状态。
2. 进入主循环，运行执行上下文和处理事件队列。
3. 检查空闲状态，必要时让出 CPU。
4. 定期清理动态资源池。
5. 在停止条件满足时退出循环并清理状态。

```c
#define CXPLAT_WORKER_IDLE_WORK_THRESHOLD_COUNT 10

CXPLAT_THREAD_CALLBACK(CxPlatWorkerThread, Context)
{
    CXPLAT_WORKER* Worker = (CXPLAT_WORKER*)Context;
    CXPLAT_DBG_ASSERT(Worker != NULL);

    QuicTraceLogInfo(
        PlatformWorkerThreadStart,
        "[ lib][%p] Worker start",
        Worker);
#if DEBUG
    Worker->ThreadStarted = TRUE;
#endif

    //初始化线程状态。
    CXPLAT_EXECUTION_STATE State = { 0, 0, 0, UINT32_MAX, 0, CxPlatCurThreadID() };

    Worker->Running = TRUE;

    //进入主循环，运行执行上下文和处理事件队列。
    while (!Worker->StoppedThread) {

        ++State.NoWorkCount;
#if DEBUG // Debug statistics
        ++Worker->LoopCount;
#endif
        State.TimeNow = CxPlatTimeUs64();

        //运行执行上下文
        CxPlatRunExecutionContexts(Worker, &State);
        if (State.WaitTime && InterlockedFetchAndClearBoolean(&Worker->Running)) {
          //在上面的CxPlatRunExecutionContexts中的执行期间
          //可能增加了新的context，确保这些新添加的上下文能够被及时调度
            State.TimeNow = CxPlatTimeUs64();
            CxPlatRunExecutionContexts(Worker, &State); // Run once more to handle race conditions
        }

        //处理事件队列
        CxPlatProcessEvents(Worker, &State);

        if (State.NoWorkCount == 0) {
            State.LastWorkTime = State.TimeNow;
        } else if (State.NoWorkCount > CXPLAT_WORKER_IDLE_WORK_THRESHOLD_COUNT) {
            //检查空闲状态，必要时让出 CPU。
            CxPlatSchedulerYield();
            State.NoWorkCount = 0;
        }

        //定期清理动态资源池。
        if (State.TimeNow - State.LastPoolProcessTime > DYNAMIC_POOL_PROCESSING_PERIOD) {
            CxPlatProcessDynamicPoolAllocators(Worker);
            State.LastPoolProcessTime = State.TimeNow;
        }
    }

    //循环结束时，更新线程状态
    Worker->Running = FALSE;

#if DEBUG
    Worker->ThreadFinished = TRUE;
#endif

    QuicTraceLogInfo(
        PlatformWorkerThreadStop,
        "[ lib][%p] Worker stop",
        Worker);

    CXPLAT_THREAD_RETURN(0);
}
```
![CxPlatWorkerThread流程图](./pic/CxPlatWorkerThread.png)

### **CxPlatWorkerThread中的CxPlatRunExecutionContexts**
```c
void
CxPlatRunExecutionContexts(
    _In_ CXPLAT_WORKER* Worker,
    _Inout_ CXPLAT_EXECUTION_STATE* State
    )
```
流程：
1. 检查哪些上下文需要运行。
2. 调用上下文的回调函数执行任务。
3. 动态调整上下文列表。
4. 计算线程的下一次等待时间。
```c
void
CxPlatRunExecutionContexts(
    _In_ CXPLAT_WORKER* Worker,
    _Inout_ CXPLAT_EXECUTION_STATE* State
    )
{
    if (Worker->ExecutionContexts == NULL) {
        State->WaitTime = UINT32_MAX;
        return;
    }

#if DEBUG // Debug statistics
    ++Worker->EcPollCount;
#endif

    uint64_t NextTime = UINT64_MAX;
    CXPLAT_SLIST_ENTRY** EC = &Worker->ExecutionContexts;
    //循环获取CXPLAT_EXECUTION_CONTEXT处理
    do {
        CXPLAT_EXECUTION_CONTEXT* Context =
            CXPLAT_CONTAINING_RECORD(*EC, CXPLAT_EXECUTION_CONTEXT, Entry);

        //获取是否就绪的状态，并将原始就绪字段清零
        BOOLEAN Ready = InterlockedFetchAndClearBoolean(&Context->Ready);

        //如果context处于就绪状态，或者执行时间已到，则调用context回调
        if (Ready || Context->NextTimeUs <= State->TimeNow) {
#if DEBUG // Debug statistics
            ++Worker->EcRunCount;
#endif
            CXPLAT_SLIST_ENTRY* Next = Context->Entry.Next;
            //回调返回FALSE，则表示本次执行完毕需要移除context
            if (!Context->Callback(Context->Context, State)) {
                *EC = Next; // Remove Context from the list.
                continue;
            }
            //如果context已经就绪
            //可能是其他线程或者自己修改了Ready的就绪状态
            if (Context->Ready) {
                //下次执行时间设置为0，表示下次无需等待立即执行
                NextTime = 0;
            }
        }
        //NextTime取最近一次的下次执行时间
        if (Context->NextTimeUs < NextTime) {
            NextTime = Context->NextTimeUs;
        }
        EC = &Context->Entry.Next;
    } while (*EC != NULL);

    //根据下一次执行时间NextTime，计算WaitTime需要等待的时间
    if (NextTime == 0) {
        State->WaitTime = 0;
    } else if (NextTime != UINT64_MAX) {
        uint64_t Diff = NextTime - State->TimeNow;
        Diff = US_TO_MS(Diff);
        if (Diff == 0) {
            State->WaitTime = 1;
        } else if (Diff < UINT32_MAX) {
            State->WaitTime = (uint32_t)Diff;
        } else {
            State->WaitTime = UINT32_MAX-1;
        }
    } else {
        State->WaitTime = UINT32_MAX;
    }
}
```

### **CxPlatProcessEvents**
```c
void
CxPlatProcessEvents(
    _In_ CXPLAT_WORKER* Worker,
    _Inout_ CXPLAT_EXECUTION_STATE* State
    )
```
1. 它的主要功能是：
2. 从事件队列中获取事件。
3. 调用事件的完成回调函数。
4. 更新线程的执行状态。
```c
void
CxPlatProcessEvents(
    _In_ CXPLAT_WORKER* Worker,
    _Inout_ CXPLAT_EXECUTION_STATE* State
    )
{
  //调用 CxPlatEventQDequeue 从事件队列中提取最多 16 个事件。
  //提取的事件存储在 Cqes 数组中。
  //提取的事件数量存储在 CqeCount 中。
    CXPLAT_CQE Cqes[16];
    uint32_t CqeCount = CxPlatEventQDequeue(&Worker->EventQ, Cqes, ARRAYSIZE(Cqes), State->WaitTime);

    //设置work线程运行状态为TRUE。
    InterlockedFetchAndSetBoolean(&Worker->Running);

    //如果 CqeCount 为 0，说明没有事件需要处理，直接返回。
    //如果有事件，重置 State->NoWorkCount，表示线程有工作要做
    if (CqeCount != 0) {
#if DEBUG // Debug statistics
        Worker->CqeCount += CqeCount;
#endif
        State->NoWorkCount = 0;

        //遍历提取的事件数组 Cqes。
        for (uint32_t i = 0; i < CqeCount; ++i) {
            CXPLAT_SQE* Sqe = CxPlatCqeGetSqe(&Cqes[i]);

            //对于每个事件，调用其完成回调函数（Sqe->Completion）。
            Sqe->Completion(&Cqes[i]);
        }
        CxPlatEventQReturn(&Worker->EventQ, CqeCount);
    }
}
```
![CxPlatProcessEvents流程图](./pic/CxPlatProcessEvents.png)

### **CxPlatProcessEvents中的CxPlatEventQDequeue**
再看看上面用到的函数CxPlatEventQDequeue在各平台中的实现

- windows user
```c
inline
uint32_t
CxPlatEventQDequeue(
    _In_ CXPLAT_EVENTQ* queue,       // 事件队列句柄
    _Out_ CXPLAT_CQE* events,        // 输出参数，用于存储获取的事件
    _In_ uint32_t count,             // 要获取的最大事件数量
    _In_ uint32_t wait_time          // 等待时间（毫秒）
    )
{
    ULONG out_count = 0;

    // 调用 GetQueuedCompletionStatusEx 从队列中获取事件
    if (!GetQueuedCompletionStatusEx(*queue, events, count, &out_count, wait_time, FALSE)) {
        return 0; // 如果获取失败，返回 0
    }

    // 确保获取的事件数量不为 0
    CXPLAT_DBG_ASSERT(out_count != 0);

    // 确保第一个事件的 Overlapped 字段不为空，或者只有一个事件
    CXPLAT_DBG_ASSERT(events[0].lpOverlapped != NULL || out_count == 1);

#if DEBUG
    // 在调试模式下，更新事件的调试标志
    if (events[0].lpOverlapped) {
        for (uint32_t i = 0; i < (uint32_t)out_count; ++i) {
            CXPLAT_CONTAINING_RECORD(events[i].lpOverlapped, CXPLAT_SQE, Overlapped)->IsQueued = FALSE;
        }
    }
#endif

    // 如果第一个事件的 Overlapped 字段为空，返回 0；否则返回事件数量
    return events[0].lpOverlapped == NULL ? 0 : (uint32_t)out_count;
}
```

- windows kernel
```c
inline
uint32_t
CxPlatEventQDequeue(
    _In_ CXPLAT_EVENTQ* queue,   // 事件队列
    _Out_ CXPLAT_CQE* events,    // 输出参数，用于存储获取的事件
    _In_ uint32_t count,         // 要获取的最大事件数量（未使用）
    _In_ uint32_t wait_time      // 等待时间（以毫秒为单位）
    )
{
    // 忽略 count 参数，因为在内核模式下不支持批量事件处理
    UNREFERENCED_PARAMETER(count);

    // 初始化输出事件为 NULL，表示尚未获取到事件
    *events = NULL;

    // 调用 _CxPlatEventWaitWithTimeout 函数等待事件
    // 如果等待成功（返回 STATUS_SUCCESS），则返回事件数量为 1
    // 如果等待超时或失败，则返回事件数量为 0
    return STATUS_SUCCESS == _CxPlatEventWaitWithTimeout(queue, wait_time) ? 1 : 0;
}
```

- linux io_uring
```c
inline
uint32_t
CxPlatEventQDequeue(
    _In_ CXPLAT_EVENTQ* queue,   // 事件队列句柄
    _Out_ CXPLAT_CQE* events,    // 输出参数，用于存储获取的事件
    _In_ uint32_t count,         // 要获取的最大事件数量
    _In_ uint32_t wait_time      // 等待时间（以毫秒为单位）
    )
{
    // 调用 io_uring_peek_batch_cqe 尝试批量获取事件
    int result = io_uring_peek_batch_cqe(queue, events, count);

    // 如果成功获取事件或等待时间为 0，则直接返回结果
    if (result > 0 || wait_time == 0) {
        return result;
    }

    // 如果等待时间不是无限等待，则设置超时时间
    if (wait_time != UINT32_MAX) {
        struct __kernel_timespec timeout;
        timeout.tv_sec = (wait_time / 1000);               // 秒部分
        timeout.tv_nsec = ((wait_time % 1000) * 1000000);  // 纳秒部分

        // 调用 io_uring_wait_cqe_timeout 等待事件
        (void)io_uring_wait_cqe_timeout(queue, events, &timeout);
    } else {
        // 如果是无限等待，则调用 io_uring_wait_cqe
        (void)io_uring_wait_cqe(queue, events);
    }

    // 再次调用 io_uring_peek_batch_cqe 获取事件
    return io_uring_peek_batch_cqe(queue, events, count);
}
```

- linux epoll
```c
inline
uint32_t
CxPlatEventQDequeue(
    _In_ CXPLAT_EVENTQ* queue,   // 事件队列句柄
    _Out_ CXPLAT_CQE* events,    // 输出参数，用于存储获取的事件
    _In_ uint32_t count,         // 要获取的最大事件数量
    _In_ uint32_t wait_time      // 等待时间（以毫秒为单位）
    )
{
    // 定义超时时间
    const int timeout = wait_time == UINT32_MAX ? -1 : (int)wait_time; // 如果等待时间为 UINT32_MAX，则设置为无限等待
    int result;

    // 调用 epoll_wait 或 kqueue 等平台特定的事件等待函数
    do {
        result = epoll_wait(*queue, events, count, timeout); // POSIX 平台使用 epoll_wait
    } while ((result == -1L) && (errno == EINTR)); // 如果被信号中断（errno == EINTR），则重试

    // 返回获取的事件数量
    return (uint32_t)result;
}
```

- darwin
```c
inline
uint32_t
CxPlatEventQDequeue(
    _In_ CXPLAT_EVENTQ* queue,   // 事件队列句柄
    _Out_ CXPLAT_CQE* events,    // 输出参数，用于存储获取的事件
    _In_ uint32_t count,         // 要获取的最大事件数量
    _In_ uint32_t wait_time      // 等待时间（以毫秒为单位）
    )
{
    // 定义超时时间结构体
    struct timespec timeout = {0, 0};
    if (wait_time != UINT32_MAX) { // 如果等待时间不是无限等待
        timeout.tv_sec = (wait_time / 1000);               // 秒部分
        timeout.tv_nsec = ((wait_time % 1000) * 1000000);  // 纳秒部分
    }

    int result;
    do {
        // 调用 kevent 函数从事件队列中获取事件
        // 如果 wait_time 为 UINT32_MAX，则不设置超时时间
        result = kevent(*queue, NULL, 0, events, count, wait_time == UINT32_MAX ? NULL : &timeout);
    } while ((result == -1L) && (errno == EINTR)); // 如果被信号中断（errno == EINTR），则重试

    // 返回获取的事件数量
    return (uint32_t)result;
} 
```

---

```mermaid
graph TD;
    A[开始] --> B[检查套接字是否有效];
    B -- 无效 --> C[返回错误];
    B -- 有效 --> D[准备发送数据];
    D --> E[检查数据长度是否合法];
    E -- 不合法 --> C;
    E -- 合法 --> F[调用系统发送函数];
    F --> G[检查发送结果];
    G -- 失败 --> C;
    G -- 成功 --> H[返回发送字节数];
    C --> I[结束];
    H --> I;
```
