mydir=kdc
BUILDTOP=$(REL)..
DEFINES=-DLIBDIR=\"$(KRB5_LIBDIR)\"

all: krb5kdc rtest

# DEFINES = -DBACKWARD_COMPAT $(KRB4DEF)

LOCALINCLUDES = -I.
SRCS= \
	kdc5_err.c \
	$(srcdir)/authind.c \
	$(srcdir)/cammac.c \
	$(srcdir)/dispatch.c \
	$(srcdir)/do_as_req.c \
	$(srcdir)/do_tgs_req.c \
	$(srcdir)/fast_util.c \
	$(srcdir)/kdc_util.c \
	$(srcdir)/kdc_preauth.c \
	$(srcdir)/kdc_preauth_ec.c \
	$(srcdir)/kdc_preauth_encts.c \
	$(srcdir)/main.c \
	$(srcdir)/policy.c \
	$(srcdir)/extern.c \
	$(srcdir)/replay.c \
	$(srcdir)/kdc_authdata.c \
	$(srcdir)/kdc_audit.c \
	$(srcdir)/kdc_transit.c \
	$(srcdir)/tgs_policy.c \
	$(srcdir)/kdc_log.c \
	$(srcdir)/t_replay.c

OBJS= \
	kdc5_err.o \
	authind.o \
	cammac.o \
	dispatch.o \
	do_as_req.o \
	do_tgs_req.o \
	fast_util.o \
	kdc_util.o \
	kdc_preauth.o \
	kdc_preauth_ec.o \
	kdc_preauth_encts.o \
	main.o \
	policy.o \
	extern.o \
	replay.o \
	kdc_authdata.o \
	kdc_audit.o \
	kdc_transit.o \
	tgs_policy.o \
	kdc_log.o

RT_OBJS= rtest.o \
	kdc_transit.o

depend: kdc5_err.c kdc5_err.h

kdc5_err.c: kdc5_err.et

kdc5_err.h: kdc5_err.et

kdc5_err.o: kdc5_err.h

krb5kdc: $(OBJS) $(KADMSRV_DEPLIBS) $(KRB5_BASE_DEPLIBS) $(APPUTILS_DEPLIB) $(VERTO_DEPLIB)
	$(CC_LINK) -o krb5kdc $(OBJS) $(APPUTILS_LIB) $(KADMSRV_LIBS) $(KRB5_BASE_LIBS) $(VERTO_LIBS)

rtest: $(RT_OBJS) $(KDB5_DEPLIBS) $(KADM_COMM_DEPLIBS) $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o rtest $(RT_OBJS) $(KDB5_LIBS) $(KADM_COMM_LIBS) $(KRB5_BASE_LIBS)

check-unix: rtest runenv.sh
	$(RUN_TEST) $(srcdir)/rtscript > test.out
	cmp test.out $(srcdir)/rtest.good
	$(RM) test.out

T_REPLAY_OBJS=t_replay.o

t_replay: $(T_REPLAY_OBJS) replay.o $(KRB5_BASE_DEPLIBS)
	$(CC_LINK) -o $@ $(T_REPLAY_OBJS) $(CMOCKA_LIBS) $(KRB5_BASE_LIBS)

check-cmocka: t_replay
	$(RUN_TEST) ./t_replay > /dev/null

check-pytests:
	$(RUNPYTEST) $(srcdir)/t_workers.py $(PYTESTFLAGS)
	$(RUNPYTEST) $(srcdir)/t_emptytgt.py $(PYTESTFLAGS)
	$(RUNPYTEST) $(srcdir)/t_bigreply.py $(PYTESTFLAGS)

install:
	$(INSTALL_PROGRAM) krb5kdc ${DESTDIR}$(SERVER_BINDIR)/krb5kdc

clean:
	$(RM) kdc5_err.h kdc5_err.c krb5kdc rtest.o rtest t_replay.o t_replay

