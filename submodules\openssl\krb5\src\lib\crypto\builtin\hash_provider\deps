#
# Generated makefile dependencies follow.
#
hash_crc32.so hash_crc32.po $(OUTPRE)hash_crc32.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../../krb/crypto_int.h $(srcdir)/../aes/aes.h \
  $(srcdir)/../crypto_mod.h $(srcdir)/../sha2/sha2.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  hash_crc32.c
hash_md4.so hash_md4.po $(OUTPRE)hash_md4.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../../krb/crypto_int.h $(srcdir)/../aes/aes.h \
  $(srcdir)/../crypto_mod.h $(srcdir)/../md4/rsa-md4.h \
  $(srcdir)/../sha2/sha2.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h hash_md4.c
hash_md5.so hash_md5.po $(OUTPRE)hash_md5.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../../krb/crypto_int.h $(srcdir)/../aes/aes.h \
  $(srcdir)/../crypto_mod.h $(srcdir)/../md5/rsa-md5.h \
  $(srcdir)/../sha2/sha2.h $(top_srcdir)/include/k5-buf.h \
  $(top_srcdir)/include/k5-err.h $(top_srcdir)/include/k5-gmt_mktime.h \
  $(top_srcdir)/include/k5-int-pkinit.h $(top_srcdir)/include/k5-int.h \
  $(top_srcdir)/include/k5-platform.h $(top_srcdir)/include/k5-plugin.h \
  $(top_srcdir)/include/k5-thread.h $(top_srcdir)/include/k5-trace.h \
  $(top_srcdir)/include/krb5.h $(top_srcdir)/include/krb5/authdata_plugin.h \
  $(top_srcdir)/include/krb5/plugin.h $(top_srcdir)/include/port-sockets.h \
  $(top_srcdir)/include/socket-utils.h hash_md5.c
hash_sha1.so hash_sha1.po $(OUTPRE)hash_sha1.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../../krb/crypto_int.h $(srcdir)/../aes/aes.h \
  $(srcdir)/../crypto_mod.h $(srcdir)/../sha1/shs.h $(srcdir)/../sha2/sha2.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  hash_sha1.c
hash_sha2.so hash_sha2.po $(OUTPRE)hash_sha2.$(OBJEXT): \
  $(BUILDTOP)/include/autoconf.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/include/osconf.h $(BUILDTOP)/include/profile.h \
  $(COM_ERR_DEPS) $(srcdir)/../../krb/crypto_int.h $(srcdir)/../aes/aes.h \
  $(srcdir)/../crypto_mod.h $(srcdir)/../sha2/sha2.h \
  $(top_srcdir)/include/k5-buf.h $(top_srcdir)/include/k5-err.h \
  $(top_srcdir)/include/k5-gmt_mktime.h $(top_srcdir)/include/k5-int-pkinit.h \
  $(top_srcdir)/include/k5-int.h $(top_srcdir)/include/k5-platform.h \
  $(top_srcdir)/include/k5-plugin.h $(top_srcdir)/include/k5-thread.h \
  $(top_srcdir)/include/k5-trace.h $(top_srcdir)/include/krb5.h \
  $(top_srcdir)/include/krb5/authdata_plugin.h $(top_srcdir)/include/krb5/plugin.h \
  $(top_srcdir)/include/port-sockets.h $(top_srcdir)/include/socket-utils.h \
  hash_sha2.c
