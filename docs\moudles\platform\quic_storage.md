## 持久化存储接口。
持久化存储是一个树形结构，支持键值对的存储和读取，  
每个键都有一组名称/值对，
对于每个键，该键下的所有名称都是唯一的，  
名称是 UTF8 字符串，长度必须小于 65536 字节，  
值以二进制数据块的形式读取。
从源码可以看到storage相关功能只在windows上有实现，其他平台暂不支持。

### **CxPlatStorageOpen**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageOpen(
    _In_opt_z_ const char * Path,
    _In_ CXPLAT_STORAGE_CHANGE_CALLBACK_HANDLER Callback,
    _In_opt_ void* CallbackContext,
    _Out_ CXPLAT_STORAGE** NewStorage
    );
```
  - 打开一个存储上下文，注册存储变更回调函数，并返回一个句柄。
  - **参数说明**:
    - `Path`: 存储路径（可选）。
    - `Callback`: 存储变动时的回调函数。
    - `CallbackContext`: 回调函数的上下文数据（可选）。
    - `NewStorage`: 输出参数，返回新的存储上下文句柄。
  - **返回值**:
    - 返回 `QUIC_STATUS`，表示操作的结果。

- windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageOpen(
    _In_opt_z_ const char * Path,                          // 可选的存储路径（UTF-8 字符串）
    _In_ CXPLAT_STORAGE_CHANGE_CALLBACK_HANDLER Callback,  // 存储变更回调函数
    _In_opt_ void* CallbackContext,                        // 回调函数的上下文数据
    _Out_ CXPLAT_STORAGE** NewStorage                      // 输出参数，返回存储上下文句柄
    )
{
    QUIC_STATUS Status;
    CXPLAT_STORAGE* Storage = NULL; // 存储上下文结构体

    // 初始化注册表路径，默认使用 CXPLAT_BASE_REG_PATH。
    char FullKeyName[256] = CXPLAT_BASE_REG_PATH;

    // 如果提供了路径，将其拼接到 FullKeyName。
    if (Path != NULL) {
        size_t PathLength = strlen(Path);
        if (PathLength + sizeof(CXPLAT_BASE_REG_PATH) > sizeof(FullKeyName)) {
            // 如果路径长度超过缓冲区大小，返回错误。
            Status = QUIC_STATUS_INVALID_PARAMETER;
            goto Exit;
        }

        // 拼接路径到 FullKeyName。
        memcpy(
            FullKeyName + sizeof(CXPLAT_BASE_REG_PATH) - 1,
            Path,
            PathLength + 1);
    }

    // 分配 CXPLAT_STORAGE 结构体。
    Storage = CXPLAT_ALLOC_PAGED(sizeof(CXPLAT_STORAGE), QUIC_POOL_STORAGE);
    if (Storage == NULL) {
        // 如果分配失败，返回内存不足错误。
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        goto Exit;
    }

    // 初始化 CXPLAT_STORAGE 字段。
    CxPlatZeroMemory(Storage, sizeof(CXPLAT_STORAGE));
    Storage->Callback = Callback;            // 设置回调函数。
    Storage->CallbackContext = CallbackContext; // 设置回调上下文。

    // 创建通知事件，用于监听注册表变更。
    Storage->NotifyEvent = CreateEvent(NULL, FALSE, FALSE, NULL);
    if (Storage->NotifyEvent == NULL) {
        // 如果事件创建失败，返回内存不足错误。
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        goto Exit;
    }

    // 创建线程池等待对象，用于异步处理注册表变更通知。
    Storage->ThreadPoolWait =
        CreateThreadpoolWait(
            CxPlatStorageRegKeyChangeCallback, // 注册回调函数。
            Storage,                           // 传递存储上下文。
            NULL);
    if (Storage->ThreadPoolWait == NULL) {
        // 如果线程池等待对象创建失败，返回内存不足错误。
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        goto Exit;
    }

    // 记录打开注册表键的日志。
    QuicTraceLogVerbose(
        StorageOpenKey,
        "[ reg] Opening %s",
        FullKeyName);

    // 打开注册表键。
#pragma prefast(suppress:6001, "SAL can't track FullKeyName")
    Status =
        HRESULT_FROM_WIN32(
        RegOpenKeyExA(
            HKEY_LOCAL_MACHINE,       // 根键。
            FullKeyName,              // 完整路径。
            0,                        // 保留字段。
            KEY_READ | KEY_NOTIFY,    // 访问权限。
            &Storage->RegKey));       // 输出注册表键句柄。
    if (QUIC_FAILED(Status)) {
        // 如果打开失败，记录错误并退出。
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "RegOpenKeyExA failed");
        goto Exit;
    }

    // 注册注册表变更通知。
    Status =
        HRESULT_FROM_WIN32(
        RegNotifyChangeKeyValue(
            Storage->RegKey,
```
![CxPlatStorageOpen流程图](./pic/user_CxPlatStorageOpen.png)

- windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageOpen(
    _In_opt_z_ const char * Path,                          // 可选的存储路径（UTF-8 字符串）
    _In_ CXPLAT_STORAGE_CHANGE_CALLBACK_HANDLER Callback,  // 存储变更回调函数
    _In_opt_ void* CallbackContext,                        // 回调函数的上下文数据
    _Out_ CXPLAT_STORAGE** NewStorage                      // 输出参数，返回存储上下文句柄
    )
{
    QUIC_STATUS Status;
    OBJECT_ATTRIBUTES Attributes;       // 注册表对象属性
    PUNICODE_STRING PathUnicode = NULL; // 转换后的路径（UNICODE_STRING）
    CXPLAT_STORAGE* Storage = NULL;     // 存储上下文结构体

    // 如果提供了路径，将其转换为 UNICODE_STRING
    if (Path != NULL) {
        Status = CxPlatStorageCreateAppKey(Path, &PathUnicode);
        if (QUIC_FAILED(Status)) {
            // 如果路径转换失败，记录错误并退出
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                Status,
                "CxPlatStorageCreateAppKey failed");
            goto Exit;
        }

        // 初始化 OBJECT_ATTRIBUTES，用于打开注册表键
        InitializeObjectAttributes(
            &Attributes,
            PathUnicode,
            OBJ_CASE_INSENSITIVE | OBJ_KERNEL_HANDLE,
            NULL,
            NULL);
    } else {
        // 如果路径为空，使用默认路径 BaseKeyPath
        InitializeObjectAttributes(
            &Attributes,
            (PUNICODE_STRING)&BaseKeyPath,
            OBJ_CASE_INSENSITIVE | OBJ_KERNEL_HANDLE,
            NULL,
            NULL);
    }

    // 分配 CXPLAT_STORAGE 结构体
    Storage = CXPLAT_ALLOC_NONPAGED(sizeof(CXPLAT_STORAGE), QUIC_POOL_STORAGE);
    if (Storage == NULL) {
        // 如果分配失败，记录错误并退出
        QuicTraceEvent(
            AllocFailure,
            "Allocation of '%s' failed. (%llu bytes)",
            "CXPLAT_STORAGE",
            sizeof(CXPLAT_STORAGE));
        Status = QUIC_STATUS_OUT_OF_MEMORY;
        goto Exit;
    }

    // 初始化 CXPLAT_STORAGE 字段
    CxPlatZeroMemory(Storage, sizeof(CXPLAT_STORAGE));
    CxPlatLockInitialize(&Storage->Lock); // 初始化锁
    Storage->Callback = Callback;        // 设置回调函数
    Storage->CallbackContext = CallbackContext; // 设置回调上下文

#pragma warning(push)
#pragma warning(disable: 4996)
    // 初始化工作项，用于处理注册表变更通知
    ExInitializeWorkItem(
        &Storage->WorkItem,
        CxPlatStorageRegKeyChangeCallback,
        Storage);
#pragma warning(pop)

    // 打开注册表键
    Status =
        ZwOpenKey(
            &Storage->RegKey,
            KEY_READ | KEY_NOTIFY, // 读取和通知权限
            &Attributes);
    if (QUIC_FAILED(Status)) {
        // 如果打开失败，记录错误并退出
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "ZwOpenKey failed");
        goto Exit;
    }

    // 注册注册表变更通知
    Status =
        ZwNotifyChangeKey(
            Storage->RegKey,
            NULL,
            (PIO_APC_ROUTINE)(ULONG_PTR)&Storage->WorkItem,
            (PVOID)(UINT_PTR)(unsigned int)DelayedWorkQueue,
            &Storage->IoStatusBlock,
            REG_NOTIFY_CHANGE_LAST_SET, // 监听键值变更
            FALSE,
            NULL,
            0,
            TRUE);
    if (QUIC_FAILED(Status)) {
        // 如果注册失败，记录错误并退出
        QuicTraceEvent(
            LibraryErrorStatus,
            "[ lib] ERROR, %u, %s.",
            Status,
            "ZwNotifyChangeKey failed");
        goto Exit;
    }

    // 成功，返回存储句柄
    *NewStorage = Storage;
    Storage = NULL;

Exit:

    // 清理路径的 UNICODE_STRING
    if (PathUnicode != NULL) {
        CXPLAT_FREE(PathUnicode, QUIC_POOL_PLATFORM_TMP_ALLOC);
    }

    // 如果失败，清理已分配的资源
    if (Storage != NULL) {
        if (Storage->RegKey != NULL) {
            ZwClose(Storage->RegKey); // 关闭注册表键
        }
        CxPlatLockUninitialize(&Storage->Lock); // 清理锁
        CXPLAT_FREE(Storage, QUIC_POOL_STORAGE); // 释放存储上下文
    }

    return Status; // 返回操作结果
}
```
![CxPlatStorageOpen流程图](./pic/kernel_CxPlatStorageOpen.png)

### **CxPlatStorageClose**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatStorageClose(
    _In_opt_ CXPLAT_STORAGE* Storage
    );
```
- 清理和释放存储上下文（CXPLAT_STORAGE）的资源
#### **参数**
- **`Storage`**:
  - 指向需要关闭的存储上下文（`CXPLAT_STORAGE`）。
  - 如果为 `NULL`，函数直接返回，不执行任何操作。

---

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatStorageClose(
    _In_opt_ CXPLAT_STORAGE* Storage
    )
{
    if (Storage != NULL) {
        //等待未完成的等待回调完成，并取消尚未开始执行的挂起回调。
        WaitForThreadpoolWaitCallbacks(Storage->ThreadPoolWait, TRUE);

        //关闭存储上下文中的注册表键句柄。
        RegCloseKey(Storage->RegKey);

        //关闭变更通知的事件。
        CloseThreadpoolWait(Storage->ThreadPoolWait);
        CxPlatCloseHandle(Storage->NotifyEvent);

        //释放存储上下文的内存和其他资源。
        CXPLAT_FREE(Storage, QUIC_POOL_STORAGE);
    }
}
```

- Windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
CxPlatStorageClose(
    _In_opt_ CXPLAT_STORAGE* Storage
    )
{
    if (Storage != NULL) {
        // 初始化 CleanupEvent
        CXPLAT_EVENT CleanupEvent;
        CxPlatEventInitialize(&CleanupEvent, TRUE, FALSE);

        // 关闭注册表键句柄
        CxPlatLockAcquire(&Storage->Lock);
        ZwClose(Storage->RegKey); // 触发最后一次变更通知回调
        Storage->RegKey = NULL;
        Storage->CleanupEvent = &CleanupEvent;
        CxPlatLockRelease(&Storage->Lock);

        // 等待清理完成
        CxPlatEventWaitForever(CleanupEvent);

        // 清理资源
        CxPlatEventUninitialize(CleanupEvent);
        CxPlatLockUninitialize(&Storage->Lock);
        CXPLAT_FREE(Storage, QUIC_POOL_STORAGE);
    }
}
```

### **CxPlatStorageReadValue**
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageReadValue(
    _In_ CXPLAT_STORAGE* Storage,
    _In_opt_z_ const char * Name,
    _Out_writes_bytes_to_opt_(*BufferLength, *BufferLength)
        uint8_t * Buffer,
    _Inout_ uint32_t * BufferLength
    );
```
- 从存储上下文（CXPLAT_STORAGE）中读取指定键的值
#### **参数**
1. **`Storage`**:
   - 存储上下文（`CXPLAT_STORAGE`），表示打开的注册表键句柄。
   - 必须是通过 `CxPlatStorageOpen` 打开的有效存储上下文。

2. **`Name`**:
   - 要读取的注册表值的名称（UTF-8 字符串）。
   - 如果为 `NULL`，表示读取默认值。

3. **`Buffer`**:
   - 输出缓冲区，用于存储读取的值。
   - 如果为 `NULL`，函数只返回值的大小。

4. **`BufferLength`**:
   - 输入/输出参数：
     - 输入时，表示缓冲区的大小（以字节为单位）。
     - 输出时，表示实际读取的字节数。

#### **返回值**
- 返回 `QUIC_STATUS`，表示操作的结果：
  - **`QUIC_STATUS_SUCCESS`**: 读取成功。
  - **其他错误代码**: 表示读取失败，例如缓冲区不足或键不存在。

---

- Windows user
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageReadValue(
    _In_ CXPLAT_STORAGE* Storage,
    _In_opt_z_ const char * Name,
    _Out_writes_bytes_to_opt_(*BufferLength, *BufferLength)
        UINT8 * Buffer,
    _Inout_ uint32_t * BufferLength
    )
{
    DWORD Type; // 用于存储值的类型
    return
        HRESULT_FROM_WIN32(
            RegQueryValueExA(
                Storage->RegKey,       // 注册表键句柄
                Name,                  // 要读取的值的名称
                NULL,                  // 保留字段，必须为 NULL
                &Type,                 // 输出值的类型
                Buffer,                // 输出缓冲区
                (PDWORD)BufferLength   // 输入/输出缓冲区大小
            ));
}
```

- Windows kernel
```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
CxPlatStorageReadValue(
    _In_ CXPLAT_STORAGE* Storage,
    _In_opt_z_ const char * Name,
    _Out_writes_bytes_to_opt_(*BufferLength, *BufferLength)
        UINT8 * Buffer,
    _Inout_ uint32_t * BufferLength
    )
{
    QUIC_STATUS Status;
    PUNICODE_STRING NameUnicode;

    if (Name != NULL) {
        Status = CxPlatConvertUtf8ToUnicode(Name, &NameUnicode);
        if (QUIC_FAILED(Status)) {
            return Status;
        }
    } else {
        return QUIC_STATUS_INVALID_PARAMETER;
    }

    if (Buffer == NULL) {

        ULONG InfoLength;

        //获取要读取键值的大小
        Status =
            ZwQueryValueKey(
                Storage->RegKey,
                NameUnicode,
                KeyValuePartialInformation,
                NULL,
                0,
                &InfoLength);
        if (Status == STATUS_BUFFER_OVERFLOW ||
            Status == STATUS_BUFFER_TOO_SMALL) {
            Status = QUIC_STATUS_SUCCESS;
        } else if (QUIC_FAILED(Status)) {
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                Status,
                "ZwQueryValueKey (length) failed");
            goto Exit;
        }

        *BufferLength = InfoLength - BASE_KEY_INFO_LENGTH;

    } else {

        ULONG InfoLength = BASE_KEY_INFO_LENGTH + *BufferLength;
        PKEY_VALUE_PARTIAL_INFORMATION Info = CXPLAT_ALLOC_PAGED(InfoLength, QUIC_POOL_PLATFORM_TMP_ALLOC);
        if (Info == NULL) {
            Status = QUIC_STATUS_OUT_OF_MEMORY;
            goto Exit;
        }

        //读取键值
        Status =
            ZwQueryValueKey(
                Storage->RegKey,
                NameUnicode,
                KeyValuePartialInformation,
                Info,
                InfoLength,
                &InfoLength);
        if (QUIC_SUCCEEDED(Status)) {
            CXPLAT_DBG_ASSERT(*BufferLength == Info->DataLength);
            memcpy(Buffer, Info->Data, Info->DataLength);
        } else if (Status != STATUS_OBJECT_NAME_NOT_FOUND) {
            QuicTraceEvent(
                LibraryErrorStatus,
                "[ lib] ERROR, %u, %s.",
                Status,
                "ZwQueryValueKey failed");
        }

        CXPLAT_FREE(Info, QUIC_POOL_PLATFORM_TMP_ALLOC);
    }

Exit:

    if (NameUnicode != NULL) {
        CXPLAT_FREE(NameUnicode, QUIC_POOL_PLATFORM_TMP_ALLOC);
    }

    return Status;
}
```