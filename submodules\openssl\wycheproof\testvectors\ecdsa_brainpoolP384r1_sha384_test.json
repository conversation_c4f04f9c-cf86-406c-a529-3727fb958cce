{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 420, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "GroupIsomorphism": "Some EC groups have isomorphic groups that allow an efficient implementation. This is a test vector that contains values that are edge cases on such an isomorphic group.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04192ed5ce547d2336911d3f6cecba227f08df077f6242a9147a914e854e6e32d325fd23ccc42921dc4a7e4c2eb71defd3631e69079ba982e7a1cad0a39eff47fc6d6e3a280d081286b624886ba1f3069671ec1a29986d84fb79736d2799e6fc21", "wx": "192ed5ce547d2336911d3f6cecba227f08df077f6242a9147a914e854e6e32d325fd23ccc42921dc4a7e4c2eb71defd3", "wy": "631e69079ba982e7a1cad0a39eff47fc6d6e3a280d081286b624886ba1f3069671ec1a29986d84fb79736d2799e6fc21"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004192ed5ce547d2336911d3f6cecba227f08df077f6242a9147a914e854e6e32d325fd23ccc42921dc4a7e4c2eb71defd3631e69079ba982e7a1cad0a39eff47fc6d6e3a280d081286b624886ba1f3069671ec1a29986d84fb79736d2799e6fc21", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABBku1c5UfSM2kR0/bOy6In8I3wd/\nYkKpFHqRToVObjLTJf0jzMQpIdxKfkwutx3v02MeaQebqYLnocrQo57/R/xtbjoo\nDQgShrYkiGuh8waWcewaKZhthPt5c20nmeb8IQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102310083aa7ba485dc060df9922f9ccc5da29adb75d44671d18bad0636d2e09c5e2f95e892a79b9fd3b37e1f798b157b567a24", "result": "valid", "flags": []}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30816402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082006402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "306302300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000006402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000006402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "306402800e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910280090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "3066000002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "3069498177306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "30682500306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3066306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "3069223549817702300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "30682234250002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "306c223202300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910004deadbeef0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "306902300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9122354981770230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91223425000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "306c02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9122320230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "306caa00bb00cd00306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "306aaa02aabb306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c2238aa00bb00cd0002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "306a2236aa02aabb02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "306c02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b912238aa00bb00cd000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "306a02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b912236aa02aabb0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3068228002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9100000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9122800230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080316402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3068228003300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9100000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9122800330090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e6402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f6402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "316402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "326402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff6402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "30683001023063300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "306302300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "3063300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": ["BER"]}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb4100", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb4105000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "308002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "3066300002300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb413000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "306702300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3066306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "303202300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "30819602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30650281300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91028130090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3066028200300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102820030090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "306402310e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3064022f0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910231090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91022f090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3069028501000000300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "306902300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102850100000030090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306d02890100000000000000300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "306d02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910289010000000000000030090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "306802847fffffff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102847fffffff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30680284ffffffff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910284ffffffff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30690285ffffffffff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "306902300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910285ffffffffff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306c0288ffffffffffffffff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "306c02300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910288ffffffffffffffff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "306402ff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102ff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "30320230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3033020230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "303302300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "306602320e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9100000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910232090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3066023200000e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102320000090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9100000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "306602320e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9105000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "306602300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910232090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb410500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "303402810230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "303402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "303405000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "303402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306400300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306401300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306403300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306404300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3064ff300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910030090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910130090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910330090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910430090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91ff30090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "303402000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "303402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "3068223402010e022f8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "306802300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b912234020109022f0ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "306402300c8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102300b0ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b110230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadebc1", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "3063022f0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "3063022f8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "306302300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91022f090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "306302300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91022f0ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30650231ff0e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910231ff090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "30350901800230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "303502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "30350201000230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "303502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650231009b472fccbf6d812d65696852cbfcadddaa9ff8b1ed8f8be752cc82bc2aebfc9f6bc887fe5da9bd8a6aafb4c21c74f0f60230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3064023081d4f2c778fca6dd46ae89562a302a1f8041169e12e6de81149fa5e2d2e3b14fcd531a9f86aa3769f39f50bc4a6c262c0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30640230f171eeb5e3caebfaa9f4072b84e994016a8f7857ffc4cacbcc49ebb08118290863722eb10dd60585d0d87d40cc8f746f0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306402307e2b0d3887035922b95176a9d5cfd5e07fbee961ed19217eeb605a1d2d1c4eb032ace5607955c8960c60af43b593d9d40230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650231ff64b8d03340927ed29a9697ad340352225560074e12707418ad337d43d514036094377801a256427595504b3de38b0f0a0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30650231010e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3065023100f171eeb5e3caebfaa9f4072b84e994016a8f7857ffc4cacbcc49ebb08118290863722eb10dd60585d0d87d40cc8f746f0230090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b9102310095c7c160c094d4422528af5fd56ee1234ee90dcd68d721b937f609f8bbaa1bb9b5e2c5c3372bd2a25796d8f056b250a6", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910231ff7c55845b7a23f9f2066dd06333a25d65248a2bb98e2e7452f9c92d1f63a1d06a176d5864602c4c81e08674ea84a985dc", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306402300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910230f6f15d21e2a398e5ea34c01e7b7760bbc646633c847d34f9e7206473f05a09ee1957f0ec3453f06de3f15912925214bf", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b910231ff6a383e9f3f6b2bbddad750a02a911edcb116f2329728de46c809f6074455e4464a1d3a3cc8d42d5da869270fa94daf5a", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91023101090ea2de1d5c671a15cb3fe184889f4439b99cc37b82cb0618df9b8c0fa5f611e6a80f13cbac0f921c0ea6ed6dadeb41", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "306502300e8e114a1c351405560bf8d47b166bfe957087a8003b353433b6144f7ee7d6f79c8dd14ef229fa7a2f2782bf33708b91023100f6f15d21e2a398e5ea34c01e7b7760bbc646633c847d34f9e7206473f05a09ee1957f0ec3453f06de3f15912925214bf", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201000231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201000231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201000231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201000231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201000231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360201ff0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30380231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30380231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30380231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec530231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30380231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046565", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046564", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec53", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec540231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30380231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec54090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3335373632", "sig": "30640230705c790f8f50061c508c15fc9aabc1f58193ab15b394ab2195e358cb620a5bf4b65449afb9c417bd1a3105e53a9742ce02306dd7abda4001bc416982ab4326b5d27b1280f02b142f040ce2497f9e153e4e1e3a35c5ffaef72694e677872eb19ddf36", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "31373530353531383135", "sig": "3064023079df11f0221de0473ccf844ca702b0d3981b8a97eb8f6884f4efeb84715d2c6ede43208c7e98db8e091e6c917fd9f0bb02301da9881957bffe209d61dde87ecd9c9d8c5cdad0e4cfb6e08ce2e06a431c3eeb2d141d3b13b5baac30ebfd622cbf5ed6", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "3130333633303731", "sig": "306402301cfce0ce5fbf6178abb6c27db2d4a48ba5797dc9b99cdfe52f749d079c789ecbe1bd8e7de10e2ac7b83d0381ba0c611f023024c37f70691e443b1b70293100c98cf5494e0d6e0b14e4400eef72cd0aa10fb4a689f6b88ae0f0abc3af7d09eb1b0cf9", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "32333632343231333231", "sig": "30650231008c297529b9ce5401f51e5eaeb53115f4b07066c79c4b54a9fac00638fcd16cfaaa9626dc6da6598833d924b0b92867a60230787762678f96858f222505f110b97a24987338d5e5dc0c290624c243904f65c0b5780517838a7ba217fac9ff59b6de4e", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "34353838303134363536", "sig": "3064023069fcb752545d6576b0ce45f8903651831e79ef0e173ad1c8fdad99d6b380aa7ce4a588d14aaf0a307e5bb05b81945d1002303fc4151f72c111cd2b0a38fec138083f7d058b7389a266f7030fc55b7d69e490aee05f931c55b769cae93229e7af5e69", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "32313436363035363432", "sig": "306402301ff39aa7f866347b6c5a0b62bbc9329483245d524e93dbae9fc350197143460ba6bff2a12401ac12c575fc331d89042a02301591933f0e33894abcb72c0e53de6889a00ebc0ab5974d3ab8613a493b168db33da5118f3f3477a73df49af27ed80d05", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "333335333030383230", "sig": "3064023061d322d16ca80620bb093333ac1f7b5f38ad5d1bf39b686471b3838d194a4337d3d0ca300125d4b724dc6c7cd1b0aa000230595b3d2e24354810c5d20dc81b2ba3d719036c7d4073b170d31d210f58f3b5f7ca0f03007e33702be149517f8ed69ab2", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "36333936363033363331", "sig": "306402307c8cd6b9ab6068297f8bd2f4fb5cb86182843b80dd7582317e817899c612bf13bcdf520dcd61353f27a4356dbd16707002304331c14c7f033c5f6e5d9d2de76a9020b426357d5ddbaf125765b8ed476a18ebe1bafaf9417bbe0f8f9672fbf20a5cde", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "33333931363630373935", "sig": "30640230771117177496a9118f2883e57f63a58998d57879166659314c508b6028094d4e16090f277acfd47e097f5bef3dc6593902303ec4bc040aaf47f9acba6093c82c3e07c1e607ee238bebb5db96596964bc3af7e57b808c2f6be04128467a56577b40e4", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "31343436393735393634", "sig": "306402306a05488b75acec4718d7164ec19dcd6d351a5fb2852afc4915e9ebcd8073897a5d559dc9ec56a5aae400dd0cdeefc71602302e511d8bf60ebe468f5e045333d43d4be59b4393c8e650e3e6fabcbf10da7ae5f2318bff047413df4dc17fb372d98d8a", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "35313539343738363431", "sig": "30660231008542c58427f1deb1dbd36227eb96fbff153edbd23ebd13e786a52e4863c888a2dd50941654e551a4fca91b5bf351978902310082b68b14b608032945bde3d7061d5f687458ede1b302af8842449788f8314b108579f6c528bdc800afe6b2c8b185fb6e", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "35323431373932333331", "sig": "3064023058fb974bef2acaf2547cacb6f0cc934b5991c66eb7a223755209acaf5b9e4b0fed712c76606c59c1014ba2c2eb1bb32202307d9e265dc09e031014182b369e15b4a34dba3901062d627cffab561e73d38bbea907272346fbb247d3ec63564fe1cbef", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "31313437323930323034", "sig": "30640230065ed5994d4b498af7f5ab7d4c08810cb76d242b5d8b7b5537cb8afa6ea852ab714f66b144a486d05b2a56f2056baa11023037e676a8d535d0a818dcecccaa4783db6d254925a00dcf6a035a7d9e0d677dc78195a7eccfc7beee8e8eea7456c3699a", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "3130383738373235363435", "sig": "3064023025c147aa99a615a34a6bd152d17184c48d28bf79fa6fba928e678ef638a064da79d5f253f7feb8915a40d6437b7bdfa502300cf7e14c03cf67895721cc2fbdd62d6a0f89aec43dd123d51f813d9b5c82850c07d089e7aea0df2f597e6a1c8e2bfd29", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "37333433333036353633", "sig": "3065023100853df8d619f3aa7dd0bdd24c34d387cc59abff4a0585e2e9c82066e4d2e957b0437031bc1284ba3d39545d5e850e27a302300435982cefe2cd1581f378c6be16ea77284a178b3f0dc14c169c9ed863cc4a8d8f78651380609df5e05b65041dd7a201", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "393734343630393738", "sig": "306402301b29b1e60895a4920d6861836faa227765404602f4bba3b4faa888a4b1693a7c8b585b59b942487122a9889f4f1454ef02307d9fcfbc2ee71fbe32a4262e4777daa38f9722b0a67500b950aae4b469bff9525ae1de389cc17ae719e24ecd19728441", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323237303836383339", "sig": "30650231008937dd05004f6e782a2c91c8d79f40795d169fab6af385f91f5cee928c2a22869f10938ee2edb3ed0e0a0e38144d5064023048c692b4b88776b0158b99e15e99de3955ab9d884477418cb740ff917a704c7707f39954186a03977cbedf34bac02715", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "323332393736343130", "sig": "306402301e42fae83460bb8f30d7d6bedf984622a55035d502fc2d7f9ce52c56515fd66d1d593094d4167f4ae051f2b12d0e67ab0230284d00f98f29202f03b37971978eebf2fbfb94bef2b4d63fbed88c7d29d18b61ca409882aeeea97e30a0b156dce2bb06", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3934303437333831", "sig": "306402302c07e185941b20628df84808ff9a010e5e112c0632cb3231266e8418ab06f6f18eb41f2f98a5a0ca1a462339228fad9a023029051e9231d68ab462ba7aaee39edef69c05f81ba7eab161454bcf4969ba293463e6de2e784677e8d2a92953400fe957", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "33323230353639313233", "sig": "30640230030687253ad2ccb94342d325a8ad19278ff2ac8cfe00209ab030c7997b3008d4e9588ba2922d62e75a5e6cb842324f720230752ae1bdbd94e35bc57815d2758b1fdfee706f410c0ed966be8792eeb54cae8631baa0c095e0742d6dd7d1e0419bc588", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "343134303533393934", "sig": "3064023039abdc1943608ef4f5c46bac7ae1e23d2e3252e6fcc2b0ce8f41501df024b7d297362401be87b122bb9ccd98daa533ce023058f8d8088faf75fa06d76e8cc10a1d7bcfc225d58b75d8a204e6a5ce4d6d95146e853b6818746cebf7864facb44a2189", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31393531353638363439", "sig": "30640230510f5602dbf3a095276e99a67a65249217c6e6c168a6caa64f5aad806b57d29002e60786c6f3ed274690583d18cde72b0230687568eb41af3f5ccf7f2b16e67a1f4fbcb3bf683d86e49a61fff0c28fc03d797a722af9b02c391a49f5669c7968db1b", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "35393539303731363335", "sig": "306602310084bedafb46873274ef91de67b20375c7698afbe37f3d5bac1bbcabcbb4aa6616b345267fc9d5285baacca6f1b694619f02310089b39165949cc435503f4a6ac5754d2afddb99b55a3ba840040d51624a0985251f2c9787b5cb266a218143db5b041879", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "323135333436393533", "sig": "30650230251b50b63fe1cbae210431bd1e76563f078454f7c2b2e475abc1b7758920f03b971112c62ca6132a480738768edc35d30231008b8c1646900601de4fc9c9dbea228ce9c9edbbce7c31a42d3cba580e191c92d123e11c0634b87bc094cff16e209b6954", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "34383037313039383330", "sig": "3064023034b3c6afc0fa7a75385e3d0dbfb237b5c76afe16f0f69e44533b7ac3abf4233799201504ebec0310b2fd7e867f9fdd0102302f831f5955c2e4fa5b298bef8f09732d0b15ea7ce141a6dcdbbc60378fd9c969339e826def5681e96f0a1dbc36adaf5e", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "343932393339363930", "sig": "30640230335bf6b152647bdf8a3c5de8de3c69832101902679bc802612d2f4bff8c7ed7df225a080eff6deaa5dacc74016c5ce3d02307f1b116f8d27d894ffe6ab57546851baa5513d110e6960d713263afd542e97f01f4df4f7b64d49496d22c2f6c56050d1", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "32313132333535393630", "sig": "30640230329c5d35adef357339f901657af55da7b041e8c18374c79ab44449b27a83022800f0c938503bdd85b7884a32df9057fe023074f56101c7f7b36d634c2175a0d17cec0546b6cdf18e86ef9abb6d6d0bccdd0442af1255e02a5dde707840db48543170", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "31323339323735373034", "sig": "306402301ac3d463df221945adbd28a746817ba83d5957d95657c724f1ad68b98bde6bf7959f7363253ece174d7aed346410dc2102302a5a30a8191a4883babf69ba883af7f5067bc990f8dac4a35bc6ef387102fad85d268564c61246dff17510634168a1ac", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303831313838373638", "sig": "306402305cc9a074e10c41724d55502d72430d707ba755c41d568d79987dc3cde45cf6f70341f91fa2a20e3ba5b57febde05b5c402306d8025162af30cfab2cd16a1342b4174ae171dc3c75bc1fe994ec6c0359295f0390e65856aec5ebd894a15c68577ab0c", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "343534363038393633", "sig": "306402300c13ddef4e7e83163090559fa592edba35a92d66e54a34aae2905a10b03a316ffd0c013f7b061909f3a924ac25f1c90a023040ab2d40b4007fec32a647784ae4a2d5cb4f7042cce8c374298c345180d0e38aaa5d73875eb859b082d0a17cd496d20f", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31333837363837313131", "sig": "306402300af74a3ea3c4121711d10f2e4d725031b1b08cf8eff22834748402453b8eaa00b1578611ee45220753bcbd20a391402e023015eb2daf4fb9321283f69157e7c747d6376759d0130e790552b4fd89577139a28daed43ba976a76bec1c7d53a49c9822", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "32303331333831383735", "sig": "30640230674b6ac9890dcbaba4d744ce9992e9dc698e524b0d1cf4d76d355372631d6f7dce6ff5a607273c0c1469d8e5b12ab60e02307cf8f98328f920d29475d5cb38bc35fe71ffd87f1be788d202908eb939c76b7694cecfc21dae50f433773d75e279e303", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "323535333538333333", "sig": "306502310089ab51357a7db36d1c26b1e906088b9aa3e3d59658e2bed55dd03deb56908677a59a4b24cd65eae6351b03a9300ae5180230395e10a6accc3c6e566844c4fac4caa2a8ceda4751df5aab5b3275f825c5940b1db60886f1395318110ca53c69328352", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "34363138383431343732", "sig": "306402305b3f30c83acaa088a372d5588229a3555dba14fbed8cbc2935f6f6eabd8077c853dbc7b2e354683d41dce5b5d4c9de580230767024280e5e131b4a46d66b35f2b304a55e6481f094b355e873a7f861029602097a4d300136ea005bf5fbc10843ba95", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "31303039323435383534", "sig": "3064023032e8abc36623fcb2034662105066afd71fae4d75b8300e32bef4632fac65ecbd285c4061ca64f6813edd2abfbcc213e802300b0013e2a56c36de1ba19a9c304869f3d69806ece6f4a801c27a3d4f1c20af5eb175e95e734ef637653a6cdb2a9ecb44", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "32373536343636353238", "sig": "3064023045df529d2531d48cab412b680cadb532cd6225304fb742841c89545959b79e198c3b1297dc5c4bd9aa7549193e0780d102305c8f62fc4852069d35232aab7725715e9157d1aa688050f896d690dcd4e41baa66ea6f9b34deea5a607cc391ff097d7d", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "313139363937313032", "sig": "306402301020078f6e5717538fef879c5635d4d7d52721be1529585b0a77083c5f347f21b1316d0399a8bc17b367336475a6d97e02301ade87ed2e2bdb2481a027dd3fa5b93a81f4ffdc33d4a908d97b40f841821c02929b036135f419752c88d57509d17bef", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "323333313432313732", "sig": "306402300d93d5c63741447fdbed17a738a41efdbb7093333797499fe70d5c54bc86b6bb650424bbd64907375ef92efd13ee25ec023066192ac1fb22db75881df7ae890da4953a74fa954e0b5e6b692eca23c3bcfd5fb3228d092d9991071baa4b6e8fa206ea", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "31363733343831383938", "sig": "30640230049334bf43f7d9c213443c96c4ba119b335757a3e69ba873bdc4ef642139b8687a8a5782b6a900211d6fc1ecf14c2cf90230182990bee4787267b6d63b7ea67a25852951d145cf5a39d201babe9f3f1120924e5b283eeb636a8fbbb0c2fc66ddf755", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "31343630313539383237", "sig": "306402306b0c248fa39317621af5344f978bd765ec6125cce2f40cdddfa40f7e8c7f4fe9216354bdafc2067288c56794eb5d17d202307584c077ad35b58fb29403b9c2c641271794e26b241dfc8d74d4daa7de3f076c9c4c6d3909e2c0ab9b9a702c0812eead", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "38393930383539393239", "sig": "30650231008c048a8eaba1b654c3f687df01714df3b61b98e578205c804a72bde32daae87b37fd2f9f5f82b3c5f7b4a007eaa1986a023030b79f44c83bd52e537ccf9a35772fab5ba9faf0decbe34763b5ae280984ac7ff27fb8dbad57218c364b39dc2a03b5af", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "34333236343430393831", "sig": "306402302ebcd94d17122442d3e7bd12c4b609dbceef69b3246092e4ad0c83d602c2516e09169b592fdf61a7e881e262bf495714023070392cd4e5e17606608c2e4ffff7a9c0e9171198915cf7e50633263d7e071954f12ebb1a4f4acc7683a160d64dda3b88", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32333736343337353537", "sig": "306402304d7191596f50b8309e1490895e62b16c415c1f7b50d2a4260904bc5b7bffde4f92687b029f326f4b48e6fd8d1f19ee5002300a54515fad47bb08e586697f28e2bbf98d7575c7bb911bd74db3d9aa848475bbddac66181efd63a24918dec2dd01a2d9", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "383630333937373230", "sig": "306402306fdefa7d912cd2c30cf12bae608ab87de12d49ee084d239081e89246e4939d6071dfd11f7401894aee9c13d11013ec7502307937495dc0a3a3d66c43945d99cd98dc842ae8677f14d649b22c1e7ec14857a05639ec1fe08be228112832b5e32fcf15", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "35383037373733393837", "sig": "3064023005867e5f8abd704007b98c1a8f2c69f4eea14cb4a4210262b474c4eba9073374cab5dd1bb5c781df040df32bf7943187023068afdc70aaca5f1b36ef32593d889e377d3f83b329386c982acf9b401b7cd26b75a5389395c15d507d7d67023d6d07b1", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "353731383636383537", "sig": "306502310088d5f4069be31fc58a38fd8de9dbc7fec64065de4268d41c8db799d0a20ae10492c7e80b30034b7f321cd49b2b9c3f33023009912b63c4f88be77211ab533cf13f2b63472006aab5e70df21b87301fe5139aaed4845a421b0f076f968ae4b32490d2", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38363737333039333632", "sig": "30640230750e5baeef6934b36512572dd330fa88353e321a521363bda889fd257e4ea4024fb5f92e39f265d789d2a949dd91843b023018b8467c63892514847c3b98ee279e3f41b391a47975d7f4d6669385ac0bd2e322f88608870310b635ad28256d8dcab5", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "32343735353135303630", "sig": "30650231008bb6569737c5e01d2596d3ba5d01890f231136c69c6c9f42a944f06602296b1159f29fc1d98b68be06f3052c5fa8619b02302d1d4ccd79b00998acf03d3412888f27d274b8788742be27d798dd7db654d964fa4cde3384d88c2a50247792e8820ad3", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "393733313736383734", "sig": "30640230277b8fe00651998cdcf8b4f40795f454e4dca2fdc2c10dce1fff8f0c852aac0bf6183b1ac3c3826706c3e659854198a002300d71f3f3f681fc391c3cfdb81b61eba0155cb4a8e9ce8049cdf9b459aaf264525fbb893eaa71593a9618c0f117efd90c", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "33363938303935313438", "sig": "30640230269d14d9867ed23410caf24f5f9f171bc0e52f006d8d16c02c81f1b4edba222de7351ad72943ed09a2e7ac176a1b215602304af93b800fbdca45ac74cfd22cba9a508739f8fcf3ce14e55c39bc770a143f809970e836447a4542d0bb367de6612c89", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3130373530323638353736", "sig": "3064023040731dbe64636cd5ef5d2560a23f3891accf0a530a446c6ad213b4ba7ff9cb505aba9331836ab8a98fe565e866c87979023019eb3cf6b5faf11e9717d6d0449624a509358936dd0067ffc18f22e6bcbc6aa1df3a45f15ae732197790cc6fdb92c3f0", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "383639313439353538", "sig": "306502310084335a1c93d996e36c22aee4f33ceff7c6ff088cd5604db8275098600666144607bcfac7e695f2f79a775628a1ab6f82023028ca8cdc6bd772cc9f24c14ef71332f192fefd52d03b8df99a257f315e0f6f3296e4a45fd182f06a3d2ba2779c10a40c", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "32313734363535343335", "sig": "30640230520b74d5d33ba289ffccf5187a6000380c31304b1d6f8fb54d880c1908fbd8df5e0857ffa8ca344ff7a4fa9bb6ed5f38023003ae877bc1f0bc8e7c9039381f0b66a52047163cb78eabd7a3dbfc538b424fef31d1e0af76c0e1bed7936a88338f1bb6", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "363434353530373932", "sig": "3065023059492f3e58b5bad9412105b4824dbe939d7cb2864a27680620ac107285505c42ebfaeb154c4eb6d9e6783a12abaa35aa0231008b4114caf3260e509658243a5f4190e40c268d012578df86866a4e9503c8490804882d0812aa105e245c8c46fb36480d", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "353332383138333338", "sig": "30640230421dc854479aa611887aa3689b63276fbdc16ec7d3dca449b482dd27b1403c911ef6265ad625279e9d83ce7534f4ac3f02302852e16b4276215a62ebcbcffaddbdb2358dcea7084948bc948f9b3d0f91693aba66362d4a2cec70f7952e374886211b", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31313932303736333832", "sig": "3064023049110e413aa3e02fc05937d100ae4db14cf3f0038b38679a4aa297b11f9c47f7df538df8cee30efda4ddab2cc51a6b0f0230018a09a18e1e7983e52b8e6cc8da9c6d7155c5409082f69587420906b75cb5157d3758e992b223eb7e9c274fbff4a973", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "31353332383432323230", "sig": "3065023100884cf64ce726d5758efb9f2f35c92dcc6063b01b7432faffd0f8186ac177e31129633a648a1a6986148384a7d1c4d3f5023001850718d7a2d41eb9892f5440ef4b9fc8b996d3b6742eaec3d40b10c5caa890b9a853e1d211f7fd1178116a9e7c5f4b", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "313536373137373339", "sig": "306402300d9dbf8ebcf80f256ee2d1f4fb45cd543381f6275c4c10afa85208828e27e6fb3df3ca7491899c68307db66a505c7a9002301f0db26dc691680832b3e359905e5e632bc9eaefd1d5eb4f058a0d88f8f2df0d1a60c2f77172caf6554b2d256cce8c67", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34333033303931313230", "sig": "306402306fa1802ac4c2e79b8b5a477b0a5febf630c29c089841484c1d569daedbf13c0bdf793d0a8f6915bdc67dd1480824a1ce023028b8063258111e32aa10af0b2068c7f54f0d5e9f02ad39a415c91743d6444c20156c3592d2bcc234f265b46a35864e57", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "37373335393135353831", "sig": "306402303dc1363dd0119a5054afd99484026a2692567d2fbeeb4c6d80a30d22f166b6304544246a35ba854f854601397ce45bd502302b020a770901108ce6ddf69117a2e80734788171604a8f0571db61a4a8c3c4dae33af841afe4a9892306b4f9ecb19b49", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "323433393636373430", "sig": "306402307b984bfa807a8e9b0eef94ed28c24b04d42e0664fbfc0ee1c1b5945c8f0e97fdc515fe09edd6cdaf7fef3151ca4044df02304e878741529d7a90125deb8fa5fdab8e9f7d254b8aa48a59a2f335c7d43402f2590f1082c76b2263582c9dd98ca686cb", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "34333237363032383233", "sig": "306502310087731525e787239a232ba3c24b9caeff3ce591c168227b8e2864140b1d7c0c50a7d5fa9f4f6468bca817458c171aa4470230670598b6e5dfbaab3b622bad9b5b6ae42c9d27bd45b1b0b892af9fd9739dd50414e8eede3c6dc10fc224463b44c8c23b", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "32393332303032353932", "sig": "306402301901d0d861205cc3e3f4a189b879ee246486f0cfdc481d63727384feedc46c8baddf891a6e6eab6bede4e46bbff1649602304017c9eddaea3112f26f7c6ee472ee1983d7a296a7402295794fddd9e267fe62d85b07b99e81ea513eca8d1a67e705a0", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "36343039383737323834", "sig": "3064023004143d73f140febac8fd4d6762b9a55bc93264cc3372bf1661b35a4b11be9af7910d3aa8e4f5cb5eafe1de3a9d96957702305966b4e1e9ef78e523916dbea37e03ecc356f466441dc45b9b98fe6d09af83e7d57a861c5d2cf94bf0b87f62752b2824", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "36303735363930343132", "sig": "306402305d24a6cb0a9f7f8b9f8d72da989fcbe85c9448b425a368207fce8421e5a60f029184f18611b9a5a1eb66d054d36057da023032b8a4d4aca17e8335d84f2a68d74f38d8cce5297efe9e6d0e1a8e5bed1b5759bcb73cff28062963a28bbc1c571e3e1c", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "32333231363233313335", "sig": "306402301d10c1ec203fa05deed62644b809150cd5e3fa15c9129156fbc0477742328d8d35b1c4ca8baa6138e2e12936a0f4c33202304a029bb52ddfb171e4b125d3326deec488cc9f6f2b36d051a35d75c1de4b7abd178c7d4390e511f14d58f49baef62dfe", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "36343130313532313731", "sig": "306402302dcda31189d8d179f7deff208aea8bdfe0cb08a6da46f663c5ca649c84d8fec9c4495921c7791d32aca42557c3bf658b023067536e336428bddfb0862bff5bf5d5b1694b82c1e1485498e14fe5c88f75a9d7f520115a35703cc30ba0ce973815189e", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "383337323835373438", "sig": "306402302cb81c03c3cefc417fc60f19b740e230982e0b1c68ced12121300d533f485597d1c532d87b235d136be3a43dd85882ca023048a04c5d8d867e8849bd3b981f010691f0e7422882573bd5bfcc33d6f069a622d159ca71bd562502ec001bd2b453712f", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "33333234373034353235", "sig": "30640230152464668cefda80dea5232d36bf240ac325e3ca279d8c8e0458306b18fb12ac1ed16586d2d07562691c3205ebb4c77402303c385567269279e9bc5a2d076ae9a09e790d1d8d08978871dfc586298f56121b4bc84f7891e91c3d7612249d320e363f", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "31343033393636383732", "sig": "30640230052595a16d03a138d656dec75a5540b80f7efe63b193250de3bf811fb2799d7eb9a6ae274ac953a8fbee741dc1f52100023055f0594ffa8d32b91eea8bf079b8f5a9f6b60888500225016095b3e71181ff32dbabcaa5e992b43409f55467bbb65125", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31323237363035313238", "sig": "30640230296f6b3851553203822d7417c176eb3ec5e556e352d24f2834bbdf7089a168e637f3e80999e7a8611466dec370590029023064f796945f53fe0b27bbfbc5b5e957d4132c24c8b462075e821bca24983e8b8f850531617a42ed3157dbe20eab31cb28", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "34393531343838333632", "sig": "30640230619380558cd14ac909b565e049afa224f7e720af1b22e18b59cb055fdc0f191deff46a6050d1642c5636c032e9a7b46b02303c3fd2f278f07954936c6183da8aafc0f61319d9a90b7d3dd11abe13e55e2afa81512f384c13331af1d6fb4d7c6929b3", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32343532313237303139", "sig": "30640230354ee949bfaca35b1934e88e036b7f376006b79886133d07ff4aea3e057eeeeec0b93f629961c9ac1ee188c1c87e2cd202301d02624a9110f7bad63ef70e134a7ff572d772aae30b4de679494d00ba9cd835d4ec840d772af9f7c0b0dae8cad3837a", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "31373331353530373036", "sig": "3065023100881e4eaeff3d3ed173daa166223d9583ac8dd28a310765e7261b0ff52b2b3fafee805d613258add6d056157ccc40da73023053bc28cffdedab6452161e05517194e66afcc14d107d1e5ded729f661cd6630d8b6581a8c25251ed7c4b5c364129b58a", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "31363637303639383738", "sig": "306402301734f666ae2d87271d9ede0da53ba8fc6cc2f83edc15dd26b9349c9e4cc5380434fc37b4ffcfd9b781f07125d85dd6600230531daed4b855e9117a1ebafa232f06b47f50be4386db27ffa5ceca3e247be95497565c0c97b437d32d7694974b22f2ba", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "343431353437363137", "sig": "30640230296917dd85475f8e7b6f858e84740bfa967996e173d63d85aa08f1b6d3683097395e4a7648d14e0bedbbe3667d3db4f502304d3e0279e93bb192f24418a0a05bf17238dcf78dbe8343e55a663418106d7ae22845943459b2641f45ef4ed606c53437", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343233393434393938", "sig": "3064023047f3150db2f4fa598ec25dfe864e11f92bbdd5d6046aef744b794f56704e323cde1eb6eabdc3f72f8940d6a6fb30a1c802304e41a74f6f6cd1950df41133c58608fbd8fb92b17bd3bfbeb1c1cc778489a4fcf884e8006546cb69fa9d3492652d1255", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "34383037363230373132", "sig": "3064023041b903c255e90cb719b74684ed9700a924362ecd1cc8ce35b44daa1d41e3ae2ad3df2d01b9100337efbab68c53c6c76b0230471f451c324025ddbfbe359f1d3ac5e40f712b4e8ae0bb316f54a1ae1def08c95f53ecc51afc375de4368d03d5095964", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "32313634363636323839", "sig": "3064023061b9b042fd654ca9c685aaab03ed0544e39e1848370537a0d0ac42ea7453d6853795695dda8f4c349b897c2f61f950360230634d5a0a8c8571f7685c6c4b68de8b2916d8af2233693a17399acb6048a4d1416ed3b2f91b7853868def58a0eaddce51", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "31393432383533383635", "sig": "306402301ac6ec442cbcea48a8c404ac27e42e2122a121ef8484bd91c04aa259438fed1ee8a80ba59464b8a4351089923bb01e92023078c0ed4eea7fedda04dad0d3f0d228bc54148b1238c63428fc39a772146947798965caf7ec9276a05a972ca1e4218f84", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "32323139333833353231", "sig": "306402306df0b36456bdcf91f6657d05b93738771f183912e27b9fd888af4850b3d979c4b7cb042f27e38615f054d5175938131802306a638be5f77cbec37a9766036efbc900498ee4fc850ac983e5b602c9483038da987374d755aa089cfd50bf2cc2de3b95", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "393236393333343139", "sig": "3064023031c868d4f028e318c6913a8d1af08abb1f8ad961a85338f8baed7cbd8d79e1337f35be3b03f1f62c033f1388d62b701e023077a8d63de68f69ca299aa3ebab0836d1e717285bf403683e03210bd2a333d7b61984d1e13918913c8d1e7d6a93ab7f69", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "373639333836333634", "sig": "306402307d45c7dfa59f13830e9dd79a283e2129d5829be4cb84e04369ed3bfaa08fdb38ea643cceda163fdbe5016623d3f1e41e02305b51f7b0ca780125dd6f60d3b40923fcafe6e0d49b84b3dbe508bb169459495a8420028a3e4484412048429e67ca6037", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "32373335393330353733", "sig": "30660231008732951e64e15ec1203332b9ca9a1dfbeddfb0d1231d0e2898c099f75efd4de9a46db9a780539bb0e28435d5b297007802310086b21184542ce50d046d49ec539d33569a5cc45c6432d965fb4c455c63d448194355771d7ddc47af74fd2827e1d72e0d", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "38333030353634303635", "sig": "306402302631c759a6a86914160766b94a4e4f474a0ad679b5afed8a7237eac773c6b0d67ff3ec36df3730e81adeba58d6e29517023016209dcc9237a9ae32d862b33153943f1eaf2a92146af773cf3e5bba48a8551d9c2fa12a01dff3b005426cdaff05a8c0", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "34333037363535373338", "sig": "3064023059b12f6220b046b08f892d69baefc81c510cc25ad090616b350606084216e6c40e1d8cd96a1b315e64ce1d84986d89ac02303994a6852b2377dcc80935e2ea1eaf7889ed694cd321bbda342dbd57ede1a47c2b30de46bb05cac66a6235c4bb290c5c", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "39363537303138313735", "sig": "306402304f69180fb660597f8e70334b7b6fa97e5928a6c175de912905261f3e1f4df1752d3415370e6272710c7bd4bd42edadec0230445b0b78099bd99fa78a9945d7bd2058a900b94138d67abd37fdfcf2e9fab6644cb1a8c376163ecb69955e954ce8c320", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048a94164dc7654fda3cd4301d3e972024c2daba71d442128c7f3faecdb9e375a85aa80c4ac28889f258e6cba886d47636548b3bf1b675f2318c3d8ab7a1c281a33241c121b3590bfdf703c7cd4bae8f451886d989234c1b8c589614554d429392", "wx": "008a94164dc7654fda3cd4301d3e972024c2daba71d442128c7f3faecdb9e375a85aa80c4ac28889f258e6cba886d47636", "wy": "548b3bf1b675f2318c3d8ab7a1c281a33241c121b3590bfdf703c7cd4bae8f451886d989234c1b8c589614554d429392"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200048a94164dc7654fda3cd4301d3e972024c2daba71d442128c7f3faecdb9e375a85aa80c4ac28889f258e6cba886d47636548b3bf1b675f2318c3d8ab7a1c281a33241c121b3590bfdf703c7cd4bae8f451886d989234c1b8c589614554d429392", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIqUFk3HZU/aPNQwHT6XICTC2rpx\n1EISjH8/rs2543WoWqgMSsKIifJY5suohtR2NlSLO/G2dfIxjD2Kt6HCgaMyQcEh\ns1kL/fcDx81Lro9FGIbZiSNMG4xYlhRVTUKTkg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "304e021900f39b6bacd3b2eb7bdd98f07a249d57614bbece10480386e80231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046562", "result": "valid", "flags": []}, {"tcId": 318, "comment": "r too large", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b412b1da197fb71123acd3a729901d1a71874700133107ec4d0231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046562", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0473f84ab63789301e88b4cb82cb935decffb8f42b2c9784c7544615b9076ec7a7ab94702ca7f1d9aacfb90537b5d368dc502cb7c8c18285994c7b19fa3e2401fdc26de54ffe006bb79bdd7852c666d730bdf76a16c0792a6c6681ed6b647fc81b", "wx": "73f84ab63789301e88b4cb82cb935decffb8f42b2c9784c7544615b9076ec7a7ab94702ca7f1d9aacfb90537b5d368dc", "wy": "502cb7c8c18285994c7b19fa3e2401fdc26de54ffe006bb79bdd7852c666d730bdf76a16c0792a6c6681ed6b647fc81b"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000473f84ab63789301e88b4cb82cb935decffb8f42b2c9784c7544615b9076ec7a7ab94702ca7f1d9aacfb90537b5d368dc502cb7c8c18285994c7b19fa3e2401fdc26de54ffe006bb79bdd7852c666d730bdf76a16c0792a6c6681ed6b647fc81b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHP4SrY3iTAeiLTLgsuTXez/uPQr\nLJeEx1RGFbkHbsenq5RwLKfx2arPuQU3tdNo3FAst8jBgoWZTHsZ+j4kAf3CbeVP\n/gBrt5vdeFLGZtcwvfdqFsB5Kmxmge1rZH/IGw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "r,s are large", "msg": "313233343030", "sig": "30660231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90465640231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046563", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04895e8461eddbe21367a95b25cd85cd31e80ecf1f95539056fb7e10b4aa49900b2194d919b29cd9bf373a1d53ef571174767c02e36b935a65e5a9cbb35589a2a018482065c5e33da8ce483dc7f7fe441574f9e7ab0614bdcfc61022c780a30009", "wx": "00895e8461eddbe21367a95b25cd85cd31e80ecf1f95539056fb7e10b4aa49900b2194d919b29cd9bf373a1d53ef571174", "wy": "767c02e36b935a65e5a9cbb35589a2a018482065c5e33da8ce483dc7f7fe441574f9e7ab0614bdcfc61022c780a30009"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004895e8461eddbe21367a95b25cd85cd31e80ecf1f95539056fb7e10b4aa49900b2194d919b29cd9bf373a1d53ef571174767c02e36b935a65e5a9cbb35589a2a018482065c5e33da8ce483dc7f7fe441574f9e7ab0614bdcfc61022c780a30009", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIlehGHt2+ITZ6lbJc2FzTHoDs8f\nlVOQVvt+ELSqSZALIZTZGbKc2b83Oh1T71cRdHZ8AuNrk1pl5anLs1WJoqAYSCBl\nxeM9qM5IPcf3/kQVdPnnqwYUvc/GECLHgKMACQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0230480eca2874aa6ba71e7fb5711339ac0a7bf84065b3c7d59c64a2c6015e6f794e7dfa2b1fec73a72adb32bdb7dd55cd04", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04618ad81912e4c31f23eab2f0c693b3ef8404074ab1dce01dc82a768151c9fa0393b4d6aeaeec6858d3f419957a5b997f31fa809b1b44677cc5aef1894846142c3e44bba6c471123fa14feb8f3aa9e92f769be549cef9c1d55bc6f1f4f841813d", "wx": "618ad81912e4c31f23eab2f0c693b3ef8404074ab1dce01dc82a768151c9fa0393b4d6aeaeec6858d3f419957a5b997f", "wy": "31fa809b1b44677cc5aef1894846142c3e44bba6c471123fa14feb8f3aa9e92f769be549cef9c1d55bc6f1f4f841813d"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004618ad81912e4c31f23eab2f0c693b3ef8404074ab1dce01dc82a768151c9fa0393b4d6aeaeec6858d3f419957a5b997f31fa809b1b44677cc5aef1894846142c3e44bba6c471123fa14feb8f3aa9e92f769be549cef9c1d55bc6f1f4f841813d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGGK2BkS5MMfI+qy8MaTs++EBAdK\nsdzgHcgqdoFRyfoDk7TWrq7saFjT9BmVeluZfzH6gJsbRGd8xa7xiUhGFCw+RLum\nxHESP6FP6486qekvdpvlSc75wdVbxvH0+EGBPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe02301629ef2d7182d67b6bd9cf6842251fe09c96bfe022b8ad9a0e546fdc8ecf5dc8636fa13059d7e9d83fde50e0d2b392c8", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0479583b4968b576811b567e1620e00b0aab8aa223c5e655b27b1ebeaf83bcd35f4205a5a0e51a2052fffe9fd23785c98f77357c8a1008fcb7a3579614c2ff47980fa9e44b6b5ea3f8a33c919dd2aea5dad0ca1a01a9e2106518b1642906e4f275", "wx": "79583b4968b576811b567e1620e00b0aab8aa223c5e655b27b1ebeaf83bcd35f4205a5a0e51a2052fffe9fd23785c98f", "wy": "77357c8a1008fcb7a3579614c2ff47980fa9e44b6b5ea3f8a33c919dd2aea5dad0ca1a01a9e2106518b1642906e4f275"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000479583b4968b576811b567e1620e00b0aab8aa223c5e655b27b1ebeaf83bcd35f4205a5a0e51a2052fffe9fd23785c98f77357c8a1008fcb7a3579614c2ff47980fa9e44b6b5ea3f8a33c919dd2aea5dad0ca1a01a9e2106518b1642906e4f275", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHlYO0lotXaBG1Z+FiDgCwqriqIj\nxeZVsnsevq+DvNNfQgWloOUaIFL//p/SN4XJj3c1fIoQCPy3o1eWFML/R5gPqeRL\na16j+KM8kZ3SrqXa0MoaAaniEGUYsWQpBuTydQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0489657bac216c3ac4a3a2d5afd342ad24a4eb103d4dbe2e4461e03c7011826513fe82bd06e17e3ae8eb5811da0bec88bb33ee1eddd5d49dd86e785fbfebb9288661964e6fbe0c07af9a4ba3145fc4be11e5484b650c97096db82ebb0ca2bb84ed", "wx": "0089657bac216c3ac4a3a2d5afd342ad24a4eb103d4dbe2e4461e03c7011826513fe82bd06e17e3ae8eb5811da0bec88bb", "wy": "33ee1eddd5d49dd86e785fbfebb9288661964e6fbe0c07af9a4ba3145fc4be11e5484b650c97096db82ebb0ca2bb84ed"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000489657bac216c3ac4a3a2d5afd342ad24a4eb103d4dbe2e4461e03c7011826513fe82bd06e17e3ae8eb5811da0bec88bb33ee1eddd5d49dd86e785fbfebb9288661964e6fbe0c07af9a4ba3145fc4be11e5484b650c97096db82ebb0ca2bb84ed", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIlle6whbDrEo6LVr9NCrSSk6xA9\nTb4uRGHgPHARgmUT/oK9BuF+OujrWBHaC+yIuzPuHt3V1J3Ybnhfv+u5KIZhlk5v\nvgwHr5pLoxRfxL4R5UhLZQyXCW24LrsMoruE7Q==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045876f414fa385b403a2d10da5d89b110344ad005bfaf8c759ab1e3561a39ff0db9ff91ec6040316e2fca3654a48c0e890dcb77f896ea475cb97672a8400329554c941b61b4a84bde1f8c8fc5250c29161fc3ca50458a41c77a48bb336882f2ea", "wx": "5876f414fa385b403a2d10da5d89b110344ad005bfaf8c759ab1e3561a39ff0db9ff91ec6040316e2fca3654a48c0e89", "wy": "0dcb77f896ea475cb97672a8400329554c941b61b4a84bde1f8c8fc5250c29161fc3ca50458a41c77a48bb336882f2ea"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200045876f414fa385b403a2d10da5d89b110344ad005bfaf8c759ab1e3561a39ff0db9ff91ec6040316e2fca3654a48c0e890dcb77f896ea475cb97672a8400329554c941b61b4a84bde1f8c8fc5250c29161fc3ca50458a41c77a48bb336882f2ea", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABFh29BT6OFtAOi0Q2l2JsRA0StAF\nv6+MdZqx41YaOf8Nuf+R7GBAMW4vyjZUpIwOiQ3Ld/iW6kdcuXZyqEADKVVMlBth\ntKhL3h+Mj8UlDCkWH8PKUEWKQcd6SLszaILy6g==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 325, "comment": "r is larger than n", "msg": "313233343030", "sig": "30360231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046566020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041bee741fa192a9bd0535d00627737079e98f00076394c978a96a0f9fba64e9e21decff6b4b8fe11f60b18d5d758684de06d19321eab7e8601f8f4606fe93fd3b2f02986a58ca56413282c66dd36ba6724a3cbceee79948ba2d55c756586b58e2", "wx": "1bee741fa192a9bd0535d00627737079e98f00076394c978a96a0f9fba64e9e21decff6b4b8fe11f60b18d5d758684de", "wy": "06d19321eab7e8601f8f4606fe93fd3b2f02986a58ca56413282c66dd36ba6724a3cbceee79948ba2d55c756586b58e2"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041bee741fa192a9bd0535d00627737079e98f00076394c978a96a0f9fba64e9e21decff6b4b8fe11f60b18d5d758684de06d19321eab7e8601f8f4606fe93fd3b2f02986a58ca56413282c66dd36ba6724a3cbceee79948ba2d55c756586b58e2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABBvudB+hkqm9BTXQBidzcHnpjwAH\nY5TJeKlqD5+6ZOniHez/a0uP4R9gsY1ddYaE3gbRkyHqt+hgH49GBv6T/TsvAphq\nWMpWQTKCxm3Ta6ZySjy87ueZSLotVcdWWGtY4g==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "s is larger than n", "msg": "313233343030", "sig": "30360201010231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9173bec", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046b25f8c1629f7579e3c7ee4b029cc029b4bdbed88b9b399303e4a14352d1f3f6048ecdd062d37cba7b70bcbd587231e7621313f93d310f144bd3322582804639dd2960969a993a9f2a3609f856e1415a0a4dcf58a7864e41e2a8c80dfc158a30", "wx": "6b25f8c1629f7579e3c7ee4b029cc029b4bdbed88b9b399303e4a14352d1f3f6048ecdd062d37cba7b70bcbd587231e7", "wy": "621313f93d310f144bd3322582804639dd2960969a993a9f2a3609f856e1415a0a4dcf58a7864e41e2a8c80dfc158a30"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200046b25f8c1629f7579e3c7ee4b029cc029b4bdbed88b9b399303e4a14352d1f3f6048ecdd062d37cba7b70bcbd587231e7621313f93d310f144bd3322582804639dd2960969a993a9f2a3609f856e1415a0a4dcf58a7864e41e2a8c80dfc158a30", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGsl+MFin3V548fuSwKcwCm0vb7Y\ni5s5kwPkoUNS0fP2BI7N0GLTfLp7cLy9WHIx52ITE/k9MQ8US9MyJYKARjndKWCW\nmpk6nyo2CfhW4UFaCk3PWKeGTkHiqMgN/BWKMA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "small r and s^-1", "msg": "313233343030", "sig": "303702020101023100896621d23283b12111048d1c978e2c286d60b6ef7ce37af36cf7aa4de268d626de7ddcb356d167c7483c69455c752c93", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045d082cde6086f8ea6994f46e9dc06c1c1d2c3a3c2dc5c97bf137653d9b2ed21101bad843d46e4b7925b9af7034c6d02112c7f56e65d233104063391fb3828b3990e6893d77746e42305e6a5ba111d976d693f595af858f19fac7234f7484c489", "wx": "5d082cde6086f8ea6994f46e9dc06c1c1d2c3a3c2dc5c97bf137653d9b2ed21101bad843d46e4b7925b9af7034c6d021", "wy": "12c7f56e65d233104063391fb3828b3990e6893d77746e42305e6a5ba111d976d693f595af858f19fac7234f7484c489"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200045d082cde6086f8ea6994f46e9dc06c1c1d2c3a3c2dc5c97bf137653d9b2ed21101bad843d46e4b7925b9af7034c6d02112c7f56e65d233104063391fb3828b3990e6893d77746e42305e6a5ba111d976d693f595af858f19fac7234f7484c489", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABF0ILN5ghvjqaZT0bp3AbBwdLDo8\nLcXJe/E3ZT2bLtIRAbrYQ9RuS3klua9wNMbQIRLH9W5l0jMQQGM5H7OCizmQ5ok9\nd3RuQjBealuhEdl21pP1la+Fjxn6xyNPdITEiQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "303b02072d9b4d347952cc023038e8dae216c63f06b3edbd0f9ba7a5e4a332ec187251e3d627839d1baac667d7caad2ab0a1ea9fbb12dc5a71e3b49bc9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047407ca6c2a183f9ca1376609e9c78a8d080effad15a4f63cbb7a168e3c789b8b59ce4d3122ca08a86907ba487f717fbc3e2c56a9b3460a5136b213be8d48cb3dc9c7ad945b1dcecbf93fa6cfaaf8dbd70f1040b97ad8e3ac30f2e64fd7cc76d6", "wx": "7407ca6c2a183f9ca1376609e9c78a8d080effad15a4f63cbb7a168e3c789b8b59ce4d3122ca08a86907ba487f717fbc", "wy": "3e2c56a9b3460a5136b213be8d48cb3dc9c7ad945b1dcecbf93fa6cfaaf8dbd70f1040b97ad8e3ac30f2e64fd7cc76d6"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200047407ca6c2a183f9ca1376609e9c78a8d080effad15a4f63cbb7a168e3c789b8b59ce4d3122ca08a86907ba487f717fbc3e2c56a9b3460a5136b213be8d48cb3dc9c7ad945b1dcecbf93fa6cfaaf8dbd70f1040b97ad8e3ac30f2e64fd7cc76d6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHQHymwqGD+coTdmCenHio0IDv+t\nFaT2PLt6Fo48eJuLWc5NMSLKCKhpB7pIf3F/vD4sVqmzRgpRNrITvo1Iyz3Jx62U\nWx3Oy/k/ps+q+NvXDxBAuXrY46ww8uZP18x21g==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3041020d1033e67e37b32b445580bf4efc02300d2436a599b396a51c546e05d1c3d25a8f6d05935ae5031dad3cdd7cb36cf6912a433de28f8475d3b1e2e1ce77610879", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044fc32a5226820ec9c3fff2c74e0b36d7de028e59fc005f3807a3bd59892c9ad20dba7168ef9ed9bf99b25ed01bcfc6ca6a13da2e852777a6f99d04322a1b9fb4227684bf7c40d4d3ef92798003a3bf2da158d5686457c33d0e24be5c265fc473", "wx": "4fc32a5226820ec9c3fff2c74e0b36d7de028e59fc005f3807a3bd59892c9ad20dba7168ef9ed9bf99b25ed01bcfc6ca", "wy": "6a13da2e852777a6f99d04322a1b9fb4227684bf7c40d4d3ef92798003a3bf2da158d5686457c33d0e24be5c265fc473"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044fc32a5226820ec9c3fff2c74e0b36d7de028e59fc005f3807a3bd59892c9ad20dba7168ef9ed9bf99b25ed01bcfc6ca6a13da2e852777a6f99d04322a1b9fb4227684bf7c40d4d3ef92798003a3bf2da158d5686457c33d0e24be5c265fc473", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABE/DKlImgg7Jw//yx04LNtfeAo5Z\n/ABfOAejvVmJLJrSDbpxaO+e2b+Zsl7QG8/GymoT2i6FJ3em+Z0EMiobn7QidoS/\nfEDU0++SeYADo78toVjVaGRXwz0OJL5cJl/Ecw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "30360202010102304a289adad7ceec67ae99ef5da797b6bb17d9c168428ab30ea9a68b89652c4b9e9bae876ab3d7fbdf1eb92ed422bd3b93", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047350a7d00d7719a318522ef4c5e6be24b3b2cb300c596f79e8dd31a4688fe65a54b2d7497a06821eecbaf31b2fa7cdcb4bd72fc7f05e32457fda0cc3f321157744f1841c30bd086e6ddd5bf415eb71ecbe36f0f3fd23d3c41487fb283e0e9794", "wx": "7350a7d00d7719a318522ef4c5e6be24b3b2cb300c596f79e8dd31a4688fe65a54b2d7497a06821eecbaf31b2fa7cdcb", "wy": "4bd72fc7f05e32457fda0cc3f321157744f1841c30bd086e6ddd5bf415eb71ecbe36f0f3fd23d3c41487fb283e0e9794"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200047350a7d00d7719a318522ef4c5e6be24b3b2cb300c596f79e8dd31a4688fe65a54b2d7497a06821eecbaf31b2fa7cdcb4bd72fc7f05e32457fda0cc3f321157744f1841c30bd086e6ddd5bf415eb71ecbe36f0f3fd23d3c41487fb283e0e9794", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHNQp9ANdxmjGFIu9MXmviSzsssw\nDFlveejdMaRoj+ZaVLLXSXoGgh7suvMbL6fNy0vXL8fwXjJFf9oMw/MhFXdE8YQc\nML0Ibm3dW/QV63Hsvjbw8/0j08QUh/soPg6XlA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3041020d062522bbd3ecbe7c39e93e7c2402304a289adad7ceec67ae99ef5da797b6bb17d9c168428ab30ea9a68b89652c4b9e9bae876ab3d7fbdf1eb92ed422bd3b93", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0461498ad31a84eed102ba2712eb8a7bd92320bda4ac6d07b4326a30869d19eb1b96229d21efd711dcf73048bf166800e30cfcc13a0914132284dbeab6fcf5d70b34ca86a681157e4874abffaeebb69b8b71f69d332306567823dde5407ce739e8", "wx": "61498ad31a84eed102ba2712eb8a7bd92320bda4ac6d07b4326a30869d19eb1b96229d21efd711dcf73048bf166800e3", "wy": "0cfcc13a0914132284dbeab6fcf5d70b34ca86a681157e4874abffaeebb69b8b71f69d332306567823dde5407ce739e8"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000461498ad31a84eed102ba2712eb8a7bd92320bda4ac6d07b4326a30869d19eb1b96229d21efd711dcf73048bf166800e30cfcc13a0914132284dbeab6fcf5d70b34ca86a681157e4874abffaeebb69b8b71f69d332306567823dde5407ce739e8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHowFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGFJitMahO7RAronEuuKe9kjIL2k\nrG0HtDJqMIadGesbliKdIe/XEdz3MEi/FmgA4wz8wToJFBMihNvqtvz11ws0yoam\ngRV+SHSr/67rtpuLcfadMyMGVngj3eVAfOc56A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "30650231008cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e90464e502305dd0bf01c2259e1ab4e8f4fee099813f6374f6069e3839ccbf64499dc802c3c534d1cf1f9cffd76027b021574602ee43", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04050592f34db0263df4c669b8991941be18237a1045bfd165ea4af385376564edf6654a0dff7b5d84474090f265c46b511545918cd8f22260ce21a584edfa0b1644488c997d956529262aef400cc0320ed27ddcec3bde6b9fd79b374af688fa9f", "wx": "050592f34db0263df4c669b8991941be18237a1045bfd165ea4af385376564edf6654a0dff7b5d84474090f265c46b51", "wy": "1545918cd8f22260ce21a584edfa0b1644488c997d956529262aef400cc0320ed27ddcec3bde6b9fd79b374af688fa9f"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004050592f34db0263df4c669b8991941be18237a1045bfd165ea4af385376564edf6654a0dff7b5d84474090f265c46b511545918cd8f22260ce21a584edfa0b1644488c997d956529262aef400cc0320ed27ddcec3bde6b9fd79b374af688fa9f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABAUFkvNNsCY99MZpuJkZQb4YI3oQ\nRb/RZepK84U3ZWTt9mVKDf97XYRHQJDyZcRrURVFkYzY8iJgziGlhO36CxZESIyZ\nfZVlKSYq70AMwDIO0n3c7Dvea5/XmzdK9oj6nw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "s == 1", "msg": "313233343030", "sig": "303502302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721020101", "result": "valid", "flags": []}, {"tcId": 334, "comment": "s == 0", "msg": "313233343030", "sig": "303502302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044df898544c2b10dc3c4d3249fca5130e753d26e08320bd823926acb050d8b6a4feadf29bef07ecdb00e85b341f22069a003343695d1e0ac0a78b38490d97c1e90e4ff4ca0d2140b9101f1b63f29ca4f2bf9176e1600483916216bd35abce6741", "wx": "4df898544c2b10dc3c4d3249fca5130e753d26e08320bd823926acb050d8b6a4feadf29bef07ecdb00e85b341f22069a", "wy": "3343695d1e0ac0a78b38490d97c1e90e4ff4ca0d2140b9101f1b63f29ca4f2bf9176e1600483916216bd35abce6741"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044df898544c2b10dc3c4d3249fca5130e753d26e08320bd823926acb050d8b6a4feadf29bef07ecdb00e85b341f22069a003343695d1e0ac0a78b38490d97c1e90e4ff4ca0d2140b9101f1b63f29ca4f2bf9176e1600483916216bd35abce6741", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABE34mFRMKxDcPE0ySfylEw51PSbg\ngyC9gjkmrLBQ2Lak/q3ym+8H7NsA6Fs0HyIGmgAzQ2ldHgrAp4s4SQ2XwekOT/TK\nDSFAuRAfG2PynKTyv5F24WAEg5FiFr01q85nQQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "30640230465c8f41519c369407aeb7bf287320ef8a97b884f6aa2b598f8b3736560212d3e79d5b57b5bfe1881dc41901748232b202302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043e955d284589775837c9b83dbcb49186d695d6b53f5771689f6458e40a2b6dad6254cbb227de4675849d11e0fdf321143c4f0ae8803367716186174f91b7035b35bf8490e49f9c46147b6d3b71d96f74abfa5e40f33c100f79d459624191cee0", "wx": "3e955d284589775837c9b83dbcb49186d695d6b53f5771689f6458e40a2b6dad6254cbb227de4675849d11e0fdf32114", "wy": "3c4f0ae8803367716186174f91b7035b35bf8490e49f9c46147b6d3b71d96f74abfa5e40f33c100f79d459624191cee0"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200043e955d284589775837c9b83dbcb49186d695d6b53f5771689f6458e40a2b6dad6254cbb227de4675849d11e0fdf321143c4f0ae8803367716186174f91b7035b35bf8490e49f9c46147b6d3b71d96f74abfa5e40f33c100f79d459624191cee0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABD6VXShFiXdYN8m4Pby0kYbWlda1\nP1dxaJ9kWOQKK22tYlTLsifeRnWEnRHg/fMhFDxPCuiAM2dxYYYXT5G3A1s1v4SQ\n5J+cRhR7bTtx2W90q/peQPM8EA951FliQZHO4A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "30640230465c8f41519c369407aeb7bf287320ef8a97b884f6aa2b598f8b3736560212d3e79d5b57b5bfe1881dc41901748232b70230465c8f41519c369407aeb7bf287320ef8a97b884f6aa2b598f8b3736560212d3e79d5b57b5bfe1881dc41901748232b2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0439c44873ccab023c4b366a646decb4beb5672b6d2140fa0fd200374aa01301008c0419c3392c589000816e1f18059a4c2b6104be5e26c657aa1f6fa4addf3ff52a45679800dd28cd628711f2d1c11153a36c6c42fba6954cd37fd252112de1a4", "wx": "39c44873ccab023c4b366a646decb4beb5672b6d2140fa0fd200374aa01301008c0419c3392c589000816e1f18059a4c", "wy": "2b6104be5e26c657aa1f6fa4addf3ff52a45679800dd28cd628711f2d1c11153a36c6c42fba6954cd37fd252112de1a4"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000439c44873ccab023c4b366a646decb4beb5672b6d2140fa0fd200374aa01301008c0419c3392c589000816e1f18059a4c2b6104be5e26c657aa1f6fa4addf3ff52a45679800dd28cd628711f2d1c11153a36c6c42fba6954cd37fd252112de1a4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDnESHPMqwI8SzZqZG3stL61Zytt\nIUD6D9IAN0qgEwEAjAQZwzksWJAAgW4fGAWaTCthBL5eJsZXqh9vpK3fP/UqRWeY\nAN0ozWKHEfLRwRFTo2xsQvumlUzTf9JSES3hpA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "30640230465c8f41519c369407aeb7bf287320ef8a97b884f6aa2b598f8b3736560212d3e79d5b57b5bfe1881dc41901748232b70230465c8f41519c369407aeb7bf287320ef8a97b884f6aa2b598f8b3736560212d3e79d5b57b5bfe1881dc41901748232b3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045d77134e890ac72f9c69fcc3f181ae746fefffdafc1dfc791cf33a22fb0f8e586188cf2d5d060ddb04004baf56191c9f0e7401ddcc47a09b5ecf2719cc936010a9371a7f7624e63e7a00550a13d035cf586d3b522c7fd06251adbb0f0aad3dd7", "wx": "5d77134e890ac72f9c69fcc3f181ae746fefffdafc1dfc791cf33a22fb0f8e586188cf2d5d060ddb04004baf56191c9f", "wy": "0e7401ddcc47a09b5ecf2719cc936010a9371a7f7624e63e7a00550a13d035cf586d3b522c7fd06251adbb0f0aad3dd7"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200045d77134e890ac72f9c69fcc3f181ae746fefffdafc1dfc791cf33a22fb0f8e586188cf2d5d060ddb04004baf56191c9f0e7401ddcc47a09b5ecf2719cc936010a9371a7f7624e63e7a00550a13d035cf586d3b522c7fd06251adbb0f0aad3dd7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABF13E06JCscvnGn8w/GBrnRv7//a\n/B38eRzzOiL7D45YYYjPLV0GDdsEAEuvVhkcnw50Ad3MR6CbXs8nGcyTYBCpNxp/\ndiTmPnoAVQoT0DXPWG07Uix/0GJRrbsPCq091w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "u1 == 1", "msg": "313233343030", "sig": "306502302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721023100f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04607cd94c42f5bbfcf857a708ac163f0afc0a65c8d88725f18c4bf7eb7cf5d34aca6008a27b4e5fd9476134ed85fcd32c89f248290c59b8fb963e90bab9b0b3e313d3b8e0a6c8901455a22b7b74a108152c5b814ba575de8de07cdb8d67ba2b50", "wx": "607cd94c42f5bbfcf857a708ac163f0afc0a65c8d88725f18c4bf7eb7cf5d34aca6008a27b4e5fd9476134ed85fcd32c", "wy": "0089f248290c59b8fb963e90bab9b0b3e313d3b8e0a6c8901455a22b7b74a108152c5b814ba575de8de07cdb8d67ba2b50"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004607cd94c42f5bbfcf857a708ac163f0afc0a65c8d88725f18c4bf7eb7cf5d34aca6008a27b4e5fd9476134ed85fcd32c89f248290c59b8fb963e90bab9b0b3e313d3b8e0a6c8901455a22b7b74a108152c5b814ba575de8de07cdb8d67ba2b50", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGB82UxC9bv8+FenCKwWPwr8CmXI\n2Icl8YxL9+t89dNKymAIontOX9lHYTTthfzTLInySCkMWbj7lj6Qurmws+MT07jg\npsiQFFWiK3t0oQgVLFuBS6V13o3gfNuNZ7orUA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "306402302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba301772102301fc115146e521d7ea33f3e128eb01db0f653dc45852c2b50301d639b778b13380e51d9366552cf2049156605d57adffc", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044b4afbd91746b1a4df6d0d717afc7528fa4a9dda9a62afee19a72fc0019aa2ea89a125bea7675506230656caaff52c735f5c3575bf669637efdb672477500f1fe37b45dcf879487ad6ca36c4147329fb741706ce9b928ce47bf6dc0f9e44017f", "wx": "4b4afbd91746b1a4df6d0d717afc7528fa4a9dda9a62afee19a72fc0019aa2ea89a125bea7675506230656caaff52c73", "wy": "5f5c3575bf669637efdb672477500f1fe37b45dcf879487ad6ca36c4147329fb741706ce9b928ce47bf6dc0f9e44017f"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044b4afbd91746b1a4df6d0d717afc7528fa4a9dda9a62afee19a72fc0019aa2ea89a125bea7675506230656caaff52c735f5c3575bf669637efdb672477500f1fe37b45dcf879487ad6ca36c4147329fb741706ce9b928ce47bf6dc0f9e44017f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEtK+9kXRrGk320NcXr8dSj6Sp3a\nmmKv7hmnL8ABmqLqiaElvqdnVQYjBlbKr/Usc19cNXW/ZpY379tnJHdQDx/je0Xc\n+HlIetbKNsQUcyn7dBcGzpuSjOR79twPnkQBfw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "u2 == 1", "msg": "313233343030", "sig": "306402302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba301772102302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040d8b246c623188b7455716ac189b9af441676a1c41cd575754bd02ae4d6825304b961ddf0826bb161e3d63e9bc71f1d46edbeddc2d40dafdccac90ae85cd616a0ea1e4a08ae8fc3358ce7d5142eee8f3bebdc14591c4c9b15bff12b8cf08334a", "wx": "0d8b246c623188b7455716ac189b9af441676a1c41cd575754bd02ae4d6825304b961ddf0826bb161e3d63e9bc71f1d4", "wy": "6edbeddc2d40dafdccac90ae85cd616a0ea1e4a08ae8fc3358ce7d5142eee8f3bebdc14591c4c9b15bff12b8cf08334a"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200040d8b246c623188b7455716ac189b9af441676a1c41cd575754bd02ae4d6825304b961ddf0826bb161e3d63e9bc71f1d46edbeddc2d40dafdccac90ae85cd616a0ea1e4a08ae8fc3358ce7d5142eee8f3bebdc14591c4c9b15bff12b8cf08334a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABA2LJGxiMYi3RVcWrBibmvRBZ2oc\nQc1XV1S9Aq5NaCUwS5Yd3wgmuxYePWPpvHHx1G7b7dwtQNr9zKyQroXNYWoOoeSg\niuj8M1jOfVFC7ujzvr3BRZHEybFb/xK4zwgzSg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "306402302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba301772102305dd0bf01c2259e1ab4e8f4fee099813f6374f6069e3839ccbf64499dc802c3c534d1cf1f9cffd76027b021574602ee44", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0404d9d4a62d6eb02073e738b1e439cecd5440031911f45190eb6062a33535fc5269bcfc25d4afc1dae0ebad948d7732d8029af37e89a3cea7df38b020f624906fca6d944e1486853fe8e5ba9cfba2d74a852ec587d46fe49917c364418ef7eca5", "wx": "04d9d4a62d6eb02073e738b1e439cecd5440031911f45190eb6062a33535fc5269bcfc25d4afc1dae0ebad948d7732d8", "wy": "029af37e89a3cea7df38b020f624906fca6d944e1486853fe8e5ba9cfba2d74a852ec587d46fe49917c364418ef7eca5"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000404d9d4a62d6eb02073e738b1e439cecd5440031911f45190eb6062a33535fc5269bcfc25d4afc1dae0ebad948d7732d8029af37e89a3cea7df38b020f624906fca6d944e1486853fe8e5ba9cfba2d74a852ec587d46fe49917c364418ef7eca5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABATZ1KYtbrAgc+c4seQ5zs1UQAMZ\nEfRRkOtgYqM1NfxSabz8JdSvwdrg662UjXcy2AKa836Jo86n3ziwIPYkkG/KbZRO\nFIaFP+jlupz7otdKhS7Fh9Rv5JkXw2RBjvfspQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230533b0d50480a3ef07e7e8af8b1097759bc03ac9a1c7ed6075a052869f57f12b285613162d08ee7aab9fe54aaa984a39a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041a4a55c9b0ce43d7ed78a98d9bf6459ccf349466fccc457598fc15a1d6956d8ce8348b2332fffb3d516b078d28d329dd73f45a4ce1f5dc772f3c3283af6564e6e410f9d5064b6484065966936693f62ac9940eb28914a091d2964cd843b41028", "wx": "1a4a55c9b0ce43d7ed78a98d9bf6459ccf349466fccc457598fc15a1d6956d8ce8348b2332fffb3d516b078d28d329dd", "wy": "73f45a4ce1f5dc772f3c3283af6564e6e410f9d5064b6484065966936693f62ac9940eb28914a091d2964cd843b41028"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041a4a55c9b0ce43d7ed78a98d9bf6459ccf349466fccc457598fc15a1d6956d8ce8348b2332fffb3d516b078d28d329dd73f45a4ce1f5dc772f3c3283af6564e6e410f9d5064b6484065966936693f62ac9940eb28914a091d2964cd843b41028", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABBpKVcmwzkPX7XipjZv2RZzPNJRm\n/MxFdZj8FaHWlW2M6DSLIzL/+z1RaweNKNMp3XP0Wkzh9dx3Lzwyg69lZObkEPnV\nBktkhAZZZpNmk/YqyZQOsokUoJHSlkzYQ7QQKA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023070a8e817f4ea82b831ba5e671830b4312846b23da14ff7d43baf3a7ee7aa061c86422aaf27ffc5c655406868b5bf19bf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04373ac98f088268a86d136de4fa0ce2c41042cd33ed2d07250f53cd4ed43fa1da425da597bd5b413d56cfff954267104f069e0453bbbd79280316f8c1c161a846af379a941ed286e593e7f289ba4fff42458b273a3ba499574e134e7fb4a7dc19", "wx": "373ac98f088268a86d136de4fa0ce2c41042cd33ed2d07250f53cd4ed43fa1da425da597bd5b413d56cfff954267104f", "wy": "069e0453bbbd79280316f8c1c161a846af379a941ed286e593e7f289ba4fff42458b273a3ba499574e134e7fb4a7dc19"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004373ac98f088268a86d136de4fa0ce2c41042cd33ed2d07250f53cd4ed43fa1da425da597bd5b413d56cfff954267104f069e0453bbbd79280316f8c1c161a846af379a941ed286e593e7f289ba4fff42458b273a3ba499574e134e7fb4a7dc19", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDc6yY8IgmiobRNt5PoM4sQQQs0z\n7S0HJQ9TzU7UP6HaQl2ll71bQT1Wz/+VQmcQTwaeBFO7vXkoAxb4wcFhqEavN5qU\nHtKG5ZPn8om6T/9CRYsnOjukmVdOE05/tKfcGQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230326c0872a7224e7a104087acf4c4b4e3e5aba4ffe4625fc3955ce9647bf71fb596b83971ad2b52473a2821991c808905", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047e6ab429b9e33a974f6ab9a49eb152c484575fad5d9bcddcb87edce16e79333a937276f36aec9121de450384cb20bb2e8595f6c2880d89198e1b625e65056d0a19a58d1d1c551bcc5dd39d281d726dad4108488c8f941ac983169cace3ecc71b", "wx": "7e6ab429b9e33a974f6ab9a49eb152c484575fad5d9bcddcb87edce16e79333a937276f36aec9121de450384cb20bb2e", "wy": "008595f6c2880d89198e1b625e65056d0a19a58d1d1c551bcc5dd39d281d726dad4108488c8f941ac983169cace3ecc71b"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200047e6ab429b9e33a974f6ab9a49eb152c484575fad5d9bcddcb87edce16e79333a937276f36aec9121de450384cb20bb2e8595f6c2880d89198e1b625e65056d0a19a58d1d1c551bcc5dd39d281d726dad4108488c8f941ac983169cace3ecc71b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABH5qtCm54zqXT2q5pJ6xUsSEV1+t\nXZvN3Lh+3OFueTM6k3J282rskSHeRQOEyyC7LoWV9sKIDYkZjhtiXmUFbQoZpY0d\nHFUbzF3TnSgdcm2tQQhIjI+UGsmDFpys4+zHGw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023065cf0a5bce70af078af6d5a14545ca619e47d6eb0fd0531ecc743a7685530284a83289c2d09e024384ae5e778799e414", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041fbb37f75195c3f2de3afcc88ad7eb32108144608943face3a890005ff2a3e0b558079c5842620f44adc0c38dd88aac551734f8eb827df929d7317714a29cf8ba432caf689094d00eb9d63cbc908ba76ca5b1f93d229477c960842940f4224d3", "wx": "1fbb37f75195c3f2de3afcc88ad7eb32108144608943face3a890005ff2a3e0b558079c5842620f44adc0c38dd88aac5", "wy": "51734f8eb827df929d7317714a29cf8ba432caf689094d00eb9d63cbc908ba76ca5b1f93d229477c960842940f4224d3"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041fbb37f75195c3f2de3afcc88ad7eb32108144608943face3a890005ff2a3e0b558079c5842620f44adc0c38dd88aac551734f8eb827df929d7317714a29cf8ba432caf689094d00eb9d63cbc908ba76ca5b1f93d229477c960842940f4224d3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABB+7N/dRlcPy3jr8yIrX6zIQgURg\niUP6zjqJAAX/Kj4LVYB5xYQmIPRK3Aw43YiqxVFzT464J9+SnXMXcUopz4ukMsr2\niQlNAOudY8vJCLp2ylsfk9IpR3yWCEKUD0Ik0w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02302e099adfe4d9120596e8a1520399b0e249555b171e0a71967307548a3c28753fa40bbcb0a8658369dc8ca0caa05fb001", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0407fa30c837c8ad029326a1d448bd27521b5d26aad4d8244b7242493df70172e6dd1daf5c7e07f4fa102f5c415a4ec61f0904527df877527f7d0f5a7f71b6d9c03f2de1df8804868e7337da35c9b1ffc9bf2e279c3af8a0786e6f39832cc6ed1b", "wx": "07fa30c837c8ad029326a1d448bd27521b5d26aad4d8244b7242493df70172e6dd1daf5c7e07f4fa102f5c415a4ec61f", "wy": "0904527df877527f7d0f5a7f71b6d9c03f2de1df8804868e7337da35c9b1ffc9bf2e279c3af8a0786e6f39832cc6ed1b"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000407fa30c837c8ad029326a1d448bd27521b5d26aad4d8244b7242493df70172e6dd1daf5c7e07f4fa102f5c415a4ec61f0904527df877527f7d0f5a7f71b6d9c03f2de1df8804868e7337da35c9b1ffc9bf2e279c3af8a0786e6f39832cc6ed1b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABAf6MMg3yK0Ckyah1Ei9J1IbXSaq\n1NgkS3JCST33AXLm3R2vXH4H9PoQL1xBWk7GHwkEUn34d1J/fQ9af3G22cA/LeHf\niASGjnM32jXJsf/Jvy4nnDr4oHhubzmDLMbtGw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02305c1335bfc9b2240b2dd142a4073361c492aab62e3c14e32ce60ea9147850ea7f4817796150cb06d3b919419540bf6002", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0461397ae7fe8e7e894bfa689e5813514293a0f1b9f1090c0d9696379b61287a752a3f7d1d2480fe4127498d0eeda84c630c2fadd37ea36bfe532b5d3a0f101ddd3ac59458399648f3efaf5833dec1c8c8ece05515893553ef4d58120d37ce2ecd", "wx": "61397ae7fe8e7e894bfa689e5813514293a0f1b9f1090c0d9696379b61287a752a3f7d1d2480fe4127498d0eeda84c63", "wy": "0c2fadd37ea36bfe532b5d3a0f101ddd3ac59458399648f3efaf5833dec1c8c8ece05515893553ef4d58120d37ce2ecd"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000461397ae7fe8e7e894bfa689e5813514293a0f1b9f1090c0d9696379b61287a752a3f7d1d2480fe4127498d0eeda84c630c2fadd37ea36bfe532b5d3a0f101ddd3ac59458399648f3efaf5833dec1c8c8ece05515893553ef4d58120d37ce2ecd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGE5euf+jn6JS/ponlgTUUKToPG5\n8QkMDZaWN5thKHp1Kj99HSSA/kEnSY0O7ahMYwwvrdN+o2v+UytdOg8QHd06xZRY\nOZZI8++vWDPewcjI7OBVFYk1U+9NWBINN84uzQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230259160b321c350f4f2299aa77c72a09248927957b6414308bf8c7fb4f2dbba5ca79198f80a150e1ceb5a9845144eee9b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047f166efa8d8416d922f57673a2180cfbb49e8d160d60ba5ec90ba547f3eccd22ce6afd99a0fb292cfd16b0692b9cab03418579e67c87b359912f6cb4158bdd7ea130b5007726df2fce319915deedc4f7e89ee23f786e25373c9937498bab81b4", "wx": "7f166efa8d8416d922f57673a2180cfbb49e8d160d60ba5ec90ba547f3eccd22ce6afd99a0fb292cfd16b0692b9cab03", "wy": "418579e67c87b359912f6cb4158bdd7ea130b5007726df2fce319915deedc4f7e89ee23f786e25373c9937498bab81b4"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200047f166efa8d8416d922f57673a2180cfbb49e8d160d60ba5ec90ba547f3eccd22ce6afd99a0fb292cfd16b0692b9cab03418579e67c87b359912f6cb4158bdd7ea130b5007726df2fce319915deedc4f7e89ee23f786e25373c9937498bab81b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABH8WbvqNhBbZIvV2c6IYDPu0no0W\nDWC6XskLpUfz7M0izmr9maD7KSz9FrBpK5yrA0GFeeZ8h7NZkS9stBWL3X6hMLUA\ndybfL84xmRXe7cT36J7iP3huJTc8mTdJi6uBtA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "edge case for u1", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023100881964e1bba9a28c7a1d84379c65bb3da72f3cc879f7f579d2f9b34a574432d6c7d1c229ee227d4ddbdd9f15df9978c0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0477c9c2e658b004ab6840d7c33a5e7eb5f93ba3a7c5b32f7275fd75b07c1c92f5ae31576b9cbca046337e6d6ea76c145e67c56010dd9749e2d90b3eb57ef1c4c73741233a32a6a4355b8c4e3a24bcf5986627c7480783161db1d2a5332bd75fef", "wx": "77c9c2e658b004ab6840d7c33a5e7eb5f93ba3a7c5b32f7275fd75b07c1c92f5ae31576b9cbca046337e6d6ea76c145e", "wy": "67c56010dd9749e2d90b3eb57ef1c4c73741233a32a6a4355b8c4e3a24bcf5986627c7480783161db1d2a5332bd75fef"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000477c9c2e658b004ab6840d7c33a5e7eb5f93ba3a7c5b32f7275fd75b07c1c92f5ae31576b9cbca046337e6d6ea76c145e67c56010dd9749e2d90b3eb57ef1c4c73741233a32a6a4355b8c4e3a24bcf5986627c7480783161db1d2a5332bd75fef", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHfJwuZYsASraEDXwzpefrX5O6On\nxbMvcnX9dbB8HJL1rjFXa5y8oEYzfm1up2wUXmfFYBDdl0ni2Qs+tX7xxMc3QSM6\nMqakNVuMTjokvPWYZifHSAeDFh2x0qUzK9df7w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02300e3c83bb59abc57220170152251cf010b0081fecca2c957ca7ec1a33dae3ca1d7094b1c0f71b03e008bbe64659119f09", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0464d9a317d5b41af30fdfc7389460f357fa9978304d026b312aa5ca04a19bdc0c56440cfd14a0b060c3b8f4ee8d4a5a3777299b2280ab4c857ed2531e8db027f8c7238028bd7f7ba59bc80547d4f10da6f2e613580553406f0427ecbd7b75916e", "wx": "64d9a317d5b41af30fdfc7389460f357fa9978304d026b312aa5ca04a19bdc0c56440cfd14a0b060c3b8f4ee8d4a5a37", "wy": "77299b2280ab4c857ed2531e8db027f8c7238028bd7f7ba59bc80547d4f10da6f2e613580553406f0427ecbd7b75916e"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000464d9a317d5b41af30fdfc7389460f357fa9978304d026b312aa5ca04a19bdc0c56440cfd14a0b060c3b8f4ee8d4a5a3777299b2280ab4c857ed2531e8db027f8c7238028bd7f7ba59bc80547d4f10da6f2e613580553406f0427ecbd7b75916e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGTZoxfVtBrzD9/HOJRg81f6mXgw\nTQJrMSqlygShm9wMVkQM/RSgsGDDuPTujUpaN3cpmyKAq0yFftJTHo2wJ/jHI4Ao\nvX97pZvIBUfU8Q2m8uYTWAVTQG8EJ+y9e3WRbg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230631b97da7f334dafd01e2a01f8618632372d9abcdf14ebaf7213da37b1449c4e8c8a1dfe03384f3ade8907ad94421398", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04264ba447f80d721bf1e79877f27a23ee58565e88c49f6b9cd6448c024b6ff53aebb2b08cec22eb2eb38e30fd54727f01801887f9f94dce625ed1d56350a4b252e0dcfc0984928f25ad22a13135baf996bfa82809fbe79c0979670fddc9fba9e6", "wx": "264ba447f80d721bf1e79877f27a23ee58565e88c49f6b9cd6448c024b6ff53aebb2b08cec22eb2eb38e30fd54727f01", "wy": "00801887f9f94dce625ed1d56350a4b252e0dcfc0984928f25ad22a13135baf996bfa82809fbe79c0979670fddc9fba9e6"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004264ba447f80d721bf1e79877f27a23ee58565e88c49f6b9cd6448c024b6ff53aebb2b08cec22eb2eb38e30fd54727f01801887f9f94dce625ed1d56350a4b252e0dcfc0984928f25ad22a13135baf996bfa82809fbe79c0979670fddc9fba9e6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCZLpEf4DXIb8eeYd/J6I+5YVl6I\nxJ9rnNZEjAJLb/U667KwjOwi6y6zjjD9VHJ/AYAYh/n5Tc5iXtHVY1CkslLg3PwJ\nhJKPJa0ioTE1uvmWv6goCfvnnAl5Zw/dyfup5g==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230397e11325b2e2e3790dee4859fdcca85592bc46fd0d580abc5114602b68512f549d9854c9af0db658189dd583f7fc1cb", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0404918040a58dc477a7245561273df2d8bd977e5fd39c40d3011536cb2b9cfee82e2ab5f539e5908dcbf3ff24c645db4e5969a9d8df5cdaafe3490caa4946acf5ebe3e93aab28a8d4a6f61e2c8e5c02dc605c75806dddddebe23915631159c1f7", "wx": "04918040a58dc477a7245561273df2d8bd977e5fd39c40d3011536cb2b9cfee82e2ab5f539e5908dcbf3ff24c645db4e", "wy": "5969a9d8df5cdaafe3490caa4946acf5ebe3e93aab28a8d4a6f61e2c8e5c02dc605c75806dddddebe23915631159c1f7"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000404918040a58dc477a7245561273df2d8bd977e5fd39c40d3011536cb2b9cfee82e2ab5f539e5908dcbf3ff24c645db4e5969a9d8df5cdaafe3490caa4946acf5ebe3e93aab28a8d4a6f61e2c8e5c02dc605c75806dddddebe23915631159c1f7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABASRgECljcR3pyRVYSc98ti9l35f\n05xA0wEVNssrnP7oLiq19TnlkI3L8/8kxkXbTllpqdjfXNqv40kMqklGrPXr4+k6\nqyio1Kb2HiyOXALcYFx1gG3d3eviORVjEVnB9w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02300fe08a8a37290ebf519f9f0947580ed87b29ee22c29615a8180eb1cdbbc5899c0728ec9b32a96790248ab302eabd6ffe", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0422e44ebe0a351e4c91f7bdfc0c0c3c6e1c679da84a32539c2dbb41ea31061b0825e3f34d7b0ad525261eb9e457c408196089e33034731ba8e9f95f5a234bf8d3539c8381f4d95510d5e0f145fd48205e5c60218c3f84b189c8e4fd5608b49778", "wx": "22e44ebe0a351e4c91f7bdfc0c0c3c6e1c679da84a32539c2dbb41ea31061b0825e3f34d7b0ad525261eb9e457c40819", "wy": "6089e33034731ba8e9f95f5a234bf8d3539c8381f4d95510d5e0f145fd48205e5c60218c3f84b189c8e4fd5608b49778"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000422e44ebe0a351e4c91f7bdfc0c0c3c6e1c679da84a32539c2dbb41ea31061b0825e3f34d7b0ad525261eb9e457c408196089e33034731ba8e9f95f5a234bf8d3539c8381f4d95510d5e0f145fd48205e5c60218c3f84b189c8e4fd5608b49778", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCLkTr4KNR5Mkfe9/AwMPG4cZ52o\nSjJTnC27QeoxBhsIJePzTXsK1SUmHrnkV8QIGWCJ4zA0cxuo6flfWiNL+NNTnIOB\n9NlVENXg8UX9SCBeXGAhjD+EsYnI5P1WCLSXeA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "edge case for u1", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02305f92937aa52d5dd10fcefb95a2d57b617d6d8b04e8db5b3b5a39abe893fda2aeb2f978108c558aabbad829ce02c27735", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0466ed49779ed6a7b10c812bc7ee7b47a5d11c5ea50277273da140bc1b0cf5b8210a6a737f7e9d92eee6d845137e5c44a28accb8f637385cf6519bfae3ed3ae4d0acaa19a260a01bd8cb53ad24dacab1954b20d1472cf3975e87cc733f329ab6bd", "wx": "66ed49779ed6a7b10c812bc7ee7b47a5d11c5ea50277273da140bc1b0cf5b8210a6a737f7e9d92eee6d845137e5c44a2", "wy": "008accb8f637385cf6519bfae3ed3ae4d0acaa19a260a01bd8cb53ad24dacab1954b20d1472cf3975e87cc733f329ab6bd"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000466ed49779ed6a7b10c812bc7ee7b47a5d11c5ea50277273da140bc1b0cf5b8210a6a737f7e9d92eee6d845137e5c44a28accb8f637385cf6519bfae3ed3ae4d0acaa19a260a01bd8cb53ad24dacab1954b20d1472cf3975e87cc733f329ab6bd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGbtSXee1qexDIErx+57R6XRHF6l\nAncnPaFAvBsM9bghCmpzf36dku7m2EUTflxEoorMuPY3OFz2UZv64+065NCsqhmi\nYKAb2MtTrSTayrGVSyDRRyzzl16HzHM/Mpq2vQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023059930a2b8bbd79b8051f252a1af76b4a5c6525adf9c6c7910a5ccf798eac0c8d4513923a792a965abe82bb564dac21cb", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043024912041bc989a936fb4dcdd178b15e03a0aa94abafb4465b4a89d4416b7a8b029d47c17e69a25962ff3aefe862dcb249ee9252b5713e747a2da8aac2b961ee2b6aca157a44888748648fbcdc5661cd4a169bb92c9c1ce50a79a63735002a1", "wx": "3024912041bc989a936fb4dcdd178b15e03a0aa94abafb4465b4a89d4416b7a8b029d47c17e69a25962ff3aefe862dcb", "wy": "249ee9252b5713e747a2da8aac2b961ee2b6aca157a44888748648fbcdc5661cd4a169bb92c9c1ce50a79a63735002a1"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200043024912041bc989a936fb4dcdd178b15e03a0aa94abafb4465b4a89d4416b7a8b029d47c17e69a25962ff3aefe862dcb249ee9252b5713e747a2da8aac2b961ee2b6aca157a44888748648fbcdc5661cd4a169bb92c9c1ce50a79a63735002a1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDAkkSBBvJiak2+03N0XixXgOgqp\nSrr7RGW0qJ1EFreosCnUfBfmmiWWL/Ou/oYtyySe6SUrVxPnR6Laiqwrlh7itqyh\nV6RIiHSGSPvNxWYc1KFpu5LJwc5Qp5pjc1ACoQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02301449901ce4b00f0e3a5ff84cff8c134854b808e504d1b8f027ace9591234e3f62ce70c35a8aa8e60cafe1e0df3ed80e7", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046c9393b00e9a62ce0b83674cdcca59b18d5b34246348e37c1d78898a522d813c49d08efc5f3f7ef33f3dc9dd1bc2e5c2000b9410ce04a64cd095ae1194bc1f514c7009a4e06871b557154cf492e7c57749487ecfcd04cb31426ab785ffa95e2f", "wx": "6c9393b00e9a62ce0b83674cdcca59b18d5b34246348e37c1d78898a522d813c49d08efc5f3f7ef33f3dc9dd1bc2e5c2", "wy": "0b9410ce04a64cd095ae1194bc1f514c7009a4e06871b557154cf492e7c57749487ecfcd04cb31426ab785ffa95e2f"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200046c9393b00e9a62ce0b83674cdcca59b18d5b34246348e37c1d78898a522d813c49d08efc5f3f7ef33f3dc9dd1bc2e5c2000b9410ce04a64cd095ae1194bc1f514c7009a4e06871b557154cf492e7c57749487ecfcd04cb31426ab785ffa95e2f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGyTk7AOmmLOC4NnTNzKWbGNWzQk\nY0jjfB14iYpSLYE8SdCO/F8/fvM/PcndG8LlwgALlBDOBKZM0JWuEZS8H1FMcAmk\n4GhxtVcVTPSS58V3SUh+z80EyzFCareF/6leLw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02310089ae6f8e215bcf35c7e2afed1a6b9855171687d9edbea8af5bf8e9ddc667aac4e166f05097385fa9ea3a6245fc07b4ad", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042c58277aaa61c400d7036183af49c99a97fea5a8d5f8608c4c6ac7a282757e4dc4b6f92d82a10272f2a19696a48fa79f5a8adb770740669d6010e55f6625b141be469fe1779f4adfe64eab2e4a9ac5bf1c25b3de0b74b8f9644fc216010d9659", "wx": "2c58277aaa61c400d7036183af49c99a97fea5a8d5f8608c4c6ac7a282757e4dc4b6f92d82a10272f2a19696a48fa79f", "wy": "5a8adb770740669d6010e55f6625b141be469fe1779f4adfe64eab2e4a9ac5bf1c25b3de0b74b8f9644fc216010d9659"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200042c58277aaa61c400d7036183af49c99a97fea5a8d5f8608c4c6ac7a282757e4dc4b6f92d82a10272f2a19696a48fa79f5a8adb770740669d6010e55f6625b141be469fe1779f4adfe64eab2e4a9ac5bf1c25b3de0b74b8f9644fc216010d9659", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCxYJ3qqYcQA1wNhg69JyZqX/qWo\n1fhgjExqx6KCdX5NxLb5LYKhAnLyoZaWpI+nn1qK23cHQGadYBDlX2YlsUG+Rp/h\nd59K3+ZOqy5KmsW/HCWz3gt0uPlkT8IWAQ2WWQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02303fc16256a0914ce2661a54688af4b2546b1b59b043667da6abb5b1a1e0e2e6ab862fe8bb749f7251572bc160567530a7", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046e5f827e1aa225c4b95db52655f67d654bdc69a4bf8f49c19d1e65dcf12ca511505aa1726ca2f5cdf8ab376f94a0c5bd5daec6f35f1dfbc68fba024cc8c5f79ce9baa86adfd8d2ba53a798cdcc9025eb9797d3be207bc694abb338e43778ffdd", "wx": "6e5f827e1aa225c4b95db52655f67d654bdc69a4bf8f49c19d1e65dcf12ca511505aa1726ca2f5cdf8ab376f94a0c5bd", "wy": "5daec6f35f1dfbc68fba024cc8c5f79ce9baa86adfd8d2ba53a798cdcc9025eb9797d3be207bc694abb338e43778ffdd"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200046e5f827e1aa225c4b95db52655f67d654bdc69a4bf8f49c19d1e65dcf12ca511505aa1726ca2f5cdf8ab376f94a0c5bd5daec6f35f1dfbc68fba024cc8c5f79ce9baa86adfd8d2ba53a798cdcc9025eb9797d3be207bc694abb338e43778ffdd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABG5fgn4aoiXEuV21JlX2fWVL3Gmk\nv49JwZ0eZdzxLKURUFqhcmyi9c34qzdvlKDFvV2uxvNfHfvGj7oCTMjF95zpuqhq\n39jSulOnmM3MkCXrl5fTviB7xpSrszjkN3j/3Q==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023064c1556c5eef311a4f3ba46316adf73732d6ed47b1ba2ecd178ff89bbc5ddd6c6419f62e045ea2d35c33a250dc2fb925", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "047fe852a7612a673df351f05afeafcbb16ce4cadf85681b2b5f46cc31ef33d6b695378e7325e9cb3185d7137b2b1700465cbd4c810076d135316887e94b14b4b0108db1c944794c398938d42176c32575b6428b3e37b602211c574acafef0911e", "wx": "7fe852a7612a673df351f05afeafcbb16ce4cadf85681b2b5f46cc31ef33d6b695378e7325e9cb3185d7137b2b170046", "wy": "5cbd4c810076d135316887e94b14b4b0108db1c944794c398938d42176c32575b6428b3e37b602211c574acafef0911e"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200047fe852a7612a673df351f05afeafcbb16ce4cadf85681b2b5f46cc31ef33d6b695378e7325e9cb3185d7137b2b1700465cbd4c810076d135316887e94b14b4b0108db1c944794c398938d42176c32575b6428b3e37b602211c574acafef0911e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABH/oUqdhKmc981HwWv6vy7Fs5Mrf\nhWgbK19GzDHvM9a2lTeOcyXpyzGF1xN7KxcARly9TIEAdtE1MWiH6UsUtLAQjbHJ\nRHlMOYk41CF2wyV1tkKLPje2AiEcV0rK/vCRHg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02303cc98c561aa5f50c8f19d947dc75ac8f507e6985762006e7100982caccb79530f8f935ac9d3d82967cdf129ecf5b0ce5", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040a49dc359ed4fef683e462dfe685442cea77b733fd95633216794d9a61f7e1022d942a36e781a484a2b479a643469af4512ebd0966b68bfecf7a47021bcd9e6aa2703dcc556a9a443d16195aa145738fa36a4dff3d09481f4a86550a8d1f3545", "wx": "0a49dc359ed4fef683e462dfe685442cea77b733fd95633216794d9a61f7e1022d942a36e781a484a2b479a643469af4", "wy": "512ebd0966b68bfecf7a47021bcd9e6aa2703dcc556a9a443d16195aa145738fa36a4dff3d09481f4a86550a8d1f3545"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200040a49dc359ed4fef683e462dfe685442cea77b733fd95633216794d9a61f7e1022d942a36e781a484a2b479a643469af4512ebd0966b68bfecf7a47021bcd9e6aa2703dcc556a9a443d16195aa145738fa36a4dff3d09481f4a86550a8d1f3545", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABApJ3DWe1P72g+Ri3+aFRCzqd7cz\n/ZVjMhZ5TZph9+ECLZQqNueBpISitHmmQ0aa9FEuvQlmtov+z3pHAhvNnmqicD3M\nVWqaRD0WGVqhRXOPo2pN/z0JSB9KhlUKjR81RQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02303f2095530f36144e009019eee102b2867d83c9eb4f28bcb31b383e00c8c3746b20cc90e8efc813aefb5b6a4965204c53", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04276715087495d52c4160d15446ebb4d758291bf5bc9ca87b56c3f00adc41fa452d66684152d3e19d2fc3ad5d289787ad367385d3c3f5c3c2c6c3166adcfafc3d204453cab8797d56e955fbf1cf421763a6653e40efd9035df8128135546b6261", "wx": "276715087495d52c4160d15446ebb4d758291bf5bc9ca87b56c3f00adc41fa452d66684152d3e19d2fc3ad5d289787ad", "wy": "367385d3c3f5c3c2c6c3166adcfafc3d204453cab8797d56e955fbf1cf421763a6653e40efd9035df8128135546b6261"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004276715087495d52c4160d15446ebb4d758291bf5bc9ca87b56c3f00adc41fa452d66684152d3e19d2fc3ad5d289787ad367385d3c3f5c3c2c6c3166adcfafc3d204453cab8797d56e955fbf1cf421763a6653e40efd9035df8128135546b6261", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCdnFQh0ldUsQWDRVEbrtNdYKRv1\nvJyoe1bD8ArcQfpFLWZoQVLT4Z0vw61dKJeHrTZzhdPD9cPCxsMWatz6/D0gRFPK\nuHl9VulV+/HPQhdjpmU+QO/ZA134EoE1VGtiYQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0230704afc6a72080d1728f6cc9fd023e9d2373023377f02599b6ea9fb2923dd7403fe2fd73999f65316b53f910bda4f6f10", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045943dbd66c79fcb882936eccdd6d860c42e20727a2cdb29165c8426c9d192990b71d9a3c7f240e46acab2741b7ee9c7a461e5ab1db3eb9b51b3238d3ada33567d251d8fd0fbaf59aa1cfb40fe7b22e0277f166a32edb81ab6a8580f9b1fb3e39", "wx": "5943dbd66c79fcb882936eccdd6d860c42e20727a2cdb29165c8426c9d192990b71d9a3c7f240e46acab2741b7ee9c7a", "wy": "461e5ab1db3eb9b51b3238d3ada33567d251d8fd0fbaf59aa1cfb40fe7b22e0277f166a32edb81ab6a8580f9b1fb3e39"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200045943dbd66c79fcb882936eccdd6d860c42e20727a2cdb29165c8426c9d192990b71d9a3c7f240e46acab2741b7ee9c7a461e5ab1db3eb9b51b3238d3ada33567d251d8fd0fbaf59aa1cfb40fe7b22e0277f166a32edb81ab6a8580f9b1fb3e39", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABFlD29Zsefy4gpNuzN1thgxC4gcn\nos2ykWXIQmydGSmQtx2aPH8kDkasqydBt+6cekYeWrHbPrm1GzI4062jNWfSUdj9\nD7r1mqHPtA/nsi4Cd/Fmoy7bgatqhYD5sfs+OQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023046f09c4741c1afe74e32f6ff14daaf90f4486c33f5d0e978f9af24f5751988e72b374c5faeffdec309330401965f7d20", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "045285d72925c87c75b6ad9955064182bf2debcb25c88d0606f6672863de413e549688a4fcfbe6689bb23dba2b757bcda64ef6b01766c95b66ff10496d5deebac4b4bf8c3bb4232c019f80b69d8ab0214ceaf5813027ecec133a5a5b971948822e", "wx": "5285d72925c87c75b6ad9955064182bf2debcb25c88d0606f6672863de413e549688a4fcfbe6689bb23dba2b757bcda6", "wy": "4ef6b01766c95b66ff10496d5deebac4b4bf8c3bb4232c019f80b69d8ab0214ceaf5813027ecec133a5a5b971948822e"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200045285d72925c87c75b6ad9955064182bf2debcb25c88d0606f6672863de413e549688a4fcfbe6689bb23dba2b757bcda64ef6b01766c95b66ff10496d5deebac4b4bf8c3bb4232c019f80b69d8ab0214ceaf5813027ecec133a5a5b971948822e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABFKF1yklyHx1tq2ZVQZBgr8t68sl\nyI0GBvZnKGPeQT5Uloik/PvmaJuyPbordXvNpk72sBdmyVtm/xBJbV3uusS0v4w7\ntCMsAZ+Atp2KsCFM6vWBMCfs7BM6WluXGUiCLg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023019930a2b8bbd79b8051f252a1af76b4a5c6525adf9c6c7910a5ccf798eac0c8d4513923a792a965abe82bb564dac21cd", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040786afb03dd791dbfc371ab51ffa288b7cedd90d6a35a3c3a92566f895f38cb18536137e010f1cfba2fbed70568d77b84eec840cca8b6f3f612304b602ffad8dcbae1786b2c2216e9a1e59a6b69628b52a408b6a083d727f3ccd0e706f9aeef8", "wx": "0786afb03dd791dbfc371ab51ffa288b7cedd90d6a35a3c3a92566f895f38cb18536137e010f1cfba2fbed70568d77b8", "wy": "4eec840cca8b6f3f612304b602ffad8dcbae1786b2c2216e9a1e59a6b69628b52a408b6a083d727f3ccd0e706f9aeef8"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200040786afb03dd791dbfc371ab51ffa288b7cedd90d6a35a3c3a92566f895f38cb18536137e010f1cfba2fbed70568d77b84eec840cca8b6f3f612304b602ffad8dcbae1786b2c2216e9a1e59a6b69628b52a408b6a083d727f3ccd0e706f9aeef8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABAeGr7A915Hb/DcatR/6KIt87dkN\najWjw6klZviV84yxhTYTfgEPHPui++1wVo13uE7shAzKi28/YSMEtgL/rY3LrheG\nssIhbpoeWaa2lii1KkCLagg9cn88zQ5wb5ru+A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc023033261457177af3700a3e4a5435eed694b8ca4b5bf38d8f2214b99ef31d58191a8a272474f2552cb57d0576ac9b58439a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0446690db403904228e4f736b1344791596628e85669d4dd01374b21274280b421e42f5ba3f3f2fadad27d4469be7d9bdb7e883b43c27217f606e0a5ba6c9df781c145776c0e5a8993f0ed65c6ded65a43bddd0fe7611485e8e8d9e7decdf2d8b5", "wx": "46690db403904228e4f736b1344791596628e85669d4dd01374b21274280b421e42f5ba3f3f2fadad27d4469be7d9bdb", "wy": "7e883b43c27217f606e0a5ba6c9df781c145776c0e5a8993f0ed65c6ded65a43bddd0fe7611485e8e8d9e7decdf2d8b5"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000446690db403904228e4f736b1344791596628e85669d4dd01374b21274280b421e42f5ba3f3f2fadad27d4469be7d9bdb7e883b43c27217f606e0a5ba6c9df781c145776c0e5a8993f0ed65c6ded65a43bddd0fe7611485e8e8d9e7decdf2d8b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEZpDbQDkEIo5Pc2sTRHkVlmKOhW\nadTdATdLISdCgLQh5C9bo/Py+trSfURpvn2b236IO0PCchf2BuClumyd94HBRXds\nDlqJk/DtZcbe1lpDvd0P52EUhejo2efezfLYtQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "edge case for u2", "msg": "313233343030", "sig": "306402307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02304cb91e82a3386d280f5d6f7e50e641df152f7109ed5456b31f166e6cac0425a7cf3ab6af6b7fc3103b883202e9046567", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048be6928acad44c9571b5c4015fa3ffae5e639e4130a1a66b473e5dfdfe93b68a8de89583666d4d699e8885469f9b1a4d83b1d5312310e445ae57c85ab1a3df8dbbb706a598fbc007efb602a14a5952fd7e7df0464d533e062ea211285c2f5c27", "wx": "008be6928acad44c9571b5c4015fa3ffae5e639e4130a1a66b473e5dfdfe93b68a8de89583666d4d699e8885469f9b1a4d", "wy": "0083b1d5312310e445ae57c85ab1a3df8dbbb706a598fbc007efb602a14a5952fd7e7df0464d533e062ea211285c2f5c27"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200048be6928acad44c9571b5c4015fa3ffae5e639e4130a1a66b473e5dfdfe93b68a8de89583666d4d699e8885469f9b1a4d83b1d5312310e445ae57c85ab1a3df8dbbb706a598fbc007efb602a14a5952fd7e7df0464d533e062ea211285c2f5c27", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIvmkorK1EyVcbXEAV+j/65eY55B\nMKGma0c+Xf3+k7aKjeiVg2ZtTWmeiIVGn5saTYOx1TEjEORFrlfIWrGj3427twal\nmPvAB++2AqFKWVL9fn3wRk1TPgYuohEoXC9cJw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "edge case for u2", "msg": "313233343030", "sig": "306502307ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0231008b33c708624a1e2eeba00fb5b5a8ed1a1622fc71ed897fb13d87ac253935e8365850d380015c115d12e14a2472860d09", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e856a0f0ed8d7f28f916a4e727e55bf0818dcc84ed1132bd7da9f98ff95fb2aec238f4df9185b0982a6682c06c85e6a895e", "wx": "1886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e85", "wy": "6a0f0ed8d7f28f916a4e727e55bf0818dcc84ed1132bd7da9f98ff95fb2aec238f4df9185b0982a6682c06c85e6a895e"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e856a0f0ed8d7f28f916a4e727e55bf0818dcc84ed1132bd7da9f98ff95fb2aec238f4df9185b0982a6682c06c85e6a895e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABBiG3dKCsCMISVPvfZ6FOmrcE2DO\n9/Vt99oMp73PTzpdInpzD58g+UNLVl3E+oGehWoPDtjX8o+Rak5yflW/CBjcyE7R\nEyvX2p+Y/5X7Kuwjj035GFsJgqZoLAbIXmqJXg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "point duplication during verification", "msg": "313233343030", "sig": "30650231008729cbb906f69d8d43f94cb8c4b9572c958272f5c6ff759ba9113f340b9f9aa598837aa37a4311717faf4cf66747a5b4023028a9b8c55eb6f5f1cf5c233aff640f48211cd2b9cf0593e8b9ffff67c7e69703f8a6c5382a36769d3cca57711ab63c65", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e8522aa0fa9cb45dd96a50efcfffb2739c638672238da287ed97318da83848c25001d85ae11351397cb1f1af94ad29d62f5", "wx": "1886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e85", "wy": "22aa0fa9cb45dd96a50efcfffb2739c638672238da287ed97318da83848c25001d85ae11351397cb1f1af94ad29d62f5"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041886ddd282b023084953ef7d9e853a6adc1360cef7f56df7da0ca7bdcf4f3a5d227a730f9f20f9434b565dc4fa819e8522aa0fa9cb45dd96a50efcfffb2739c638672238da287ed97318da83848c25001d85ae11351397cb1f1af94ad29d62f5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABBiG3dKCsCMISVPvfZ6FOmrcE2DO\n9/Vt99oMp73PTzpdInpzD58g+UNLVl3E+oGehSKqD6nLRd2WpQ78//snOcY4ZyI4\n2ih+2XMY2oOEjCUAHYWuETUTl8sfGvlK0p1i9Q==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "duplication bug", "msg": "313233343030", "sig": "30650231008729cbb906f69d8d43f94cb8c4b9572c958272f5c6ff759ba9113f340b9f9aa598837aa37a4311717faf4cf66747a5b4023028a9b8c55eb6f5f1cf5c233aff640f48211cd2b9cf0593e8b9ffff67c7e69703f8a6c5382a36769d3cca57711ab63c65", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0489dd738efcb0f79811df6bec873485169450ada18e602721e61768be0d81e5d41381f24668276f32bfe31ff1c16bcb6b1f7a4d2823bcd73f236d90b6ea61d892026190e14317b5d110526e9e2675f03d5ef3fce87b5827a37e0cf19b4d3988c0", "wx": "0089dd738efcb0f79811df6bec873485169450ada18e602721e61768be0d81e5d41381f24668276f32bfe31ff1c16bcb6b", "wy": "1f7a4d2823bcd73f236d90b6ea61d892026190e14317b5d110526e9e2675f03d5ef3fce87b5827a37e0cf19b4d3988c0"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000489dd738efcb0f79811df6bec873485169450ada18e602721e61768be0d81e5d41381f24668276f32bfe31ff1c16bcb6b1f7a4d2823bcd73f236d90b6ea61d892026190e14317b5d110526e9e2675f03d5ef3fce87b5827a37e0cf19b4d3988c0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIndc478sPeYEd9r7Ic0hRaUUK2h\njmAnIeYXaL4NgeXUE4HyRmgnbzK/4x/xwWvLax96TSgjvNc/I22Qtuph2JICYZDh\nQxe10RBSbp4mdfA9XvP86HtYJ6N+DPGbTTmIwA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "306402302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba301772102301c25061a20a4e2a19cac497fa9c7a6c6376fe36862aa77bd6c9e1615bc00d454c30bbe23157ff3d00be80a009500e114", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04795592a673e82dff3d77450194e5308d64f45f11f759f34f7c7b5b7cc6ad73f9bff8f6633cc20378cff2e53fb7a5303085b5cd4621665aac8435d8ce85b26d444508b77b282e91cd5315c701d2e5b66ba4c00bf7e1eb0859a13cc351d00041a1", "wx": "795592a673e82dff3d77450194e5308d64f45f11f759f34f7c7b5b7cc6ad73f9bff8f6633cc20378cff2e53fb7a53030", "wy": "0085b5cd4621665aac8435d8ce85b26d444508b77b282e91cd5315c701d2e5b66ba4c00bf7e1eb0859a13cc351d00041a1"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004795592a673e82dff3d77450194e5308d64f45f11f759f34f7c7b5b7cc6ad73f9bff8f6633cc20378cff2e53fb7a5303085b5cd4621665aac8435d8ce85b26d444508b77b282e91cd5315c701d2e5b66ba4c00bf7e1eb0859a13cc351d00041a1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABHlVkqZz6C3/PXdFAZTlMI1k9F8R\n91nzT3x7W3zGrXP5v/j2YzzCA3jP8uU/t6UwMIW1zUYhZlqshDXYzoWybURFCLd7\nKC6RzVMVxwHS5bZrpMAL9+HrCFmhPMNR0ABBoQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca02302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0439d94ff8757dcdb67480cbc48e9679423e57de5a23232df0db1e0ff7e908614401e6cd8d615008ea8be51299d9e22de9438126d70d14e75ce41ea2f409be88e2806f7f73bd513731696bc59e7a2c1d44d5683d3bdc92baba1c2ada58809f8bef", "wx": "39d94ff8757dcdb67480cbc48e9679423e57de5a23232df0db1e0ff7e908614401e6cd8d615008ea8be51299d9e22de9", "wy": "438126d70d14e75ce41ea2f409be88e2806f7f73bd513731696bc59e7a2c1d44d5683d3bdc92baba1c2ada58809f8bef"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000439d94ff8757dcdb67480cbc48e9679423e57de5a23232df0db1e0ff7e908614401e6cd8d615008ea8be51299d9e22de9438126d70d14e75ce41ea2f409be88e2806f7f73bd513731696bc59e7a2c1d44d5683d3bdc92baba1c2ada58809f8bef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDnZT/h1fc22dIDLxI6WeUI+V95a\nIyMt8NseD/fpCGFEAebNjWFQCOqL5RKZ2eIt6UOBJtcNFOdc5B6i9Am+iOKAb39z\nvVE3MWlrxZ56LB1E1Wg9O9ySurocKtpYgJ+L7w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0420b1fc8e2480a973e097337343490b12ae40652e4180dd4ae56df521daa9e391777c0d466f018af55519038dead355017232882bca3ccd6b375591f5b5096538ca5778355307e603148fde31f5acffeb4c6863541ad233de3f281ea0d235b6f3", "wx": "20b1fc8e2480a973e097337343490b12ae40652e4180dd4ae56df521daa9e391777c0d466f018af55519038dead35501", "wy": "7232882bca3ccd6b375591f5b5096538ca5778355307e603148fde31f5acffeb4c6863541ad233de3f281ea0d235b6f3"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000420b1fc8e2480a973e097337343490b12ae40652e4180dd4ae56df521daa9e391777c0d466f018af55519038dead355017232882bca3ccd6b375591f5b5096538ca5778355307e603148fde31f5acffeb4c6863541ad233de3f281ea0d235b6f3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCCx/I4kgKlz4Jczc0NJCxKuQGUu\nQYDdSuVt9SHaqeORd3wNRm8BivVVGQON6tNVAXIyiCvKPM1rN1WR9bUJZTjKV3g1\nUwfmAxSP3jH1rP/rTGhjVBrSM94/KB6g0jW28w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca02301c25061a20a4e2a19cac497fa9c7a6c6376fe36862aa77bd6c9e1615bc00d454c30bbe23157ff3d00be80a009500e114", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044a391d79cfa82b943123d69ee2d1bc0e0b7e1e6f93c69123bfce0bd4f31a5e3434062dd0e1aa8b886ceba362c4d6720c7a2b0543a156f1934e02d31e81d5d2785a71d541cc7e1e6e6132ebee42111f52a844937260719056ae7b10f751606c41", "wx": "4a391d79cfa82b943123d69ee2d1bc0e0b7e1e6f93c69123bfce0bd4f31a5e3434062dd0e1aa8b886ceba362c4d6720c", "wy": "7a2b0543a156f1934e02d31e81d5d2785a71d541cc7e1e6e6132ebee42111f52a844937260719056ae7b10f751606c41"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044a391d79cfa82b943123d69ee2d1bc0e0b7e1e6f93c69123bfce0bd4f31a5e3434062dd0e1aa8b886ceba362c4d6720c7a2b0543a156f1934e02d31e81d5d2785a71d541cc7e1e6e6132ebee42111f52a844937260719056ae7b10f751606c41", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEo5HXnPqCuUMSPWnuLRvA4Lfh5v\nk8aRI7/OC9TzGl40NAYt0OGqi4hs66NixNZyDHorBUOhVvGTTgLTHoHV0nhacdVB\nzH4ebmEy6+5CER9SqESTcmBxkFauexD3UWBsQQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca02307094186882938a8672b125fea71e9b18ddbf8da18aa9def5b2785856f00351530c2ef88c55ffcf402fa0280254038451", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0436854adacf83ce5f0e4422406d7b6f7db63d73d4c892a01e975ef6ee6b71a9334c9d57ce6ffcdb1a2e4174ddba799e127d619672035db4fd73e5e4b4ea920b74f2e70fd24ebca49d22fdb11e96b7867fa1838ca5babcd9dd096ab85e2f97b5ae", "wx": "36854adacf83ce5f0e4422406d7b6f7db63d73d4c892a01e975ef6ee6b71a9334c9d57ce6ffcdb1a2e4174ddba799e12", "wy": "7d619672035db4fd73e5e4b4ea920b74f2e70fd24ebca49d22fdb11e96b7867fa1838ca5babcd9dd096ab85e2f97b5ae"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000436854adacf83ce5f0e4422406d7b6f7db63d73d4c892a01e975ef6ee6b71a9334c9d57ce6ffcdb1a2e4174ddba799e127d619672035db4fd73e5e4b4ea920b74f2e70fd24ebca49d22fdb11e96b7867fa1838ca5babcd9dd096ab85e2f97b5ae", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDaFStrPg85fDkQiQG17b322PXPU\nyJKgHpde9u5rcakzTJ1Xzm/82xouQXTdunmeEn1hlnIDXbT9c+XktOqSC3Ty5w/S\nTryknSL9sR6Wt4Z/oYOMpbq82d0JarheL5e1rg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca0230789eac6ff99e146b7ae25f90d7a0cabf36baf32d14916ee2aceea7cadc95d7221f56e5baee6d82a03307064ba32856e9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04804e6c71e493b783ecd375a4edcf86c77b1c2df551bbc73bed8516e4d11ce51a1dd081e19aa6f51c656818b853962178580bd6b2c4eabcf5b3741e6b7d59b0e7f2bddb247f5f9d6751cf09e3c6c9d1f7c27c0bb8d21e77a80ebadaf90af8b0d0", "wx": "00804e6c71e493b783ecd375a4edcf86c77b1c2df551bbc73bed8516e4d11ce51a1dd081e19aa6f51c656818b853962178", "wy": "580bd6b2c4eabcf5b3741e6b7d59b0e7f2bddb247f5f9d6751cf09e3c6c9d1f7c27c0bb8d21e77a80ebadaf90af8b0d0"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004804e6c71e493b783ecd375a4edcf86c77b1c2df551bbc73bed8516e4d11ce51a1dd081e19aa6f51c656818b853962178580bd6b2c4eabcf5b3741e6b7d59b0e7f2bddb247f5f9d6751cf09e3c6c9d1f7c27c0bb8d21e77a80ebadaf90af8b0d0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIBObHHkk7eD7NN1pO3Phsd7HC31\nUbvHO+2FFuTRHOUaHdCB4Zqm9RxlaBi4U5YheFgL1rLE6rz1s3Qea31ZsOfyvdsk\nf1+dZ1HPCePGydH3wnwLuNIed6gOutr5Cviw0A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "extreme value for k", "msg": "313233343030", "sig": "306402302282bc382a2f4dfcb95c3495d7b4fd590ad520b3eb6be4d6ec2f80c4e0f70df87c4ba74a09b553ebb427b58df9d59fca023064dc78d112cd6ed67d4323b302650a606ed41415bd8cfc40ec7438a70ee3d8680420e5f602aed591a324760c58140642", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042c115772dd298612197a1c59df9c25a86ac16fa4f27adf74bcc673bb4a6a4bb5d0b5b64470d5d26e0300922ab723732442f6ec209e27ce0b127d334745272643d3666bff54927419764de52322ee1696e620d15e0eea62fed0f20efe6c91e1e3", "wx": "2c115772dd298612197a1c59df9c25a86ac16fa4f27adf74bcc673bb4a6a4bb5d0b5b64470d5d26e0300922ab7237324", "wy": "42f6ec209e27ce0b127d334745272643d3666bff54927419764de52322ee1696e620d15e0eea62fed0f20efe6c91e1e3"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200042c115772dd298612197a1c59df9c25a86ac16fa4f27adf74bcc673bb4a6a4bb5d0b5b64470d5d26e0300922ab723732442f6ec209e27ce0b127d334745272643d3666bff54927419764de52322ee1696e620d15e0eea62fed0f20efe6c91e1e3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCwRV3LdKYYSGXocWd+cJahqwW+k\n8nrfdLzGc7tKaku10LW2RHDV0m4DAJIqtyNzJEL27CCeJ84LEn0zR0UnJkPTZmv/\nVJJ0GXZN5SMi7haW5iDRXg7qYv7Q8g7+bJHh4w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e02302ee85f80e112cf0d5a747a7f704cc09fb1ba7b034f1c1ce65fb224cee40161e29a68e78fce7febb013d810aba3017721", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04295778c9a3be2b373450f57daf10de66d32441750ac4289f6751ff61405ce0237f64e28ac5281a81d13fba81a8454e584c9f3991d615512faf0dc9107193b1b6f5cd684356ca51504d15c1ca4ba00b21c7c68eb4683222a8211e4ffd56da0e06", "wx": "295778c9a3be2b373450f57daf10de66d32441750ac4289f6751ff61405ce0237f64e28ac5281a81d13fba81a8454e58", "wy": "4c9f3991d615512faf0dc9107193b1b6f5cd684356ca51504d15c1ca4ba00b21c7c68eb4683222a8211e4ffd56da0e06"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004295778c9a3be2b373450f57daf10de66d32441750ac4289f6751ff61405ce0237f64e28ac5281a81d13fba81a8454e584c9f3991d615512faf0dc9107193b1b6f5cd684356ca51504d15c1ca4ba00b21c7c68eb4683222a8211e4ffd56da0e06", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABClXeMmjvis3NFD1fa8Q3mbTJEF1\nCsQon2dR/2FAXOAjf2TiisUoGoHRP7qBqEVOWEyfOZHWFVEvrw3JEHGTsbb1zWhD\nVspRUE0VwcpLoAshx8aOtGgyIqghHk/9VtoOBg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0465a340bc68f3fcead4f04277ee8675f9c17bc8c88426c5ba0313b8ce7da58d92ca9a0ffa32c7eee195857d860ba1eebe4dcd5be3a6778008b36ea19d902d93dd488f6fb65dc0719521553b39cb3c524b12681d2e07a8ef720cdc15011c23ba9d", "wx": "65a340bc68f3fcead4f04277ee8675f9c17bc8c88426c5ba0313b8ce7da58d92ca9a0ffa32c7eee195857d860ba1eebe", "wy": "4dcd5be3a6778008b36ea19d902d93dd488f6fb65dc0719521553b39cb3c524b12681d2e07a8ef720cdc15011c23ba9d"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000465a340bc68f3fcead4f04277ee8675f9c17bc8c88426c5ba0313b8ce7da58d92ca9a0ffa32c7eee195857d860ba1eebe4dcd5be3a6778008b36ea19d902d93dd488f6fb65dc0719521553b39cb3c524b12681d2e07a8ef720cdc15011c23ba9d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGWjQLxo8/zq1PBCd+6GdfnBe8jI\nhCbFugMTuM59pY2SypoP+jLH7uGVhX2GC6Huvk3NW+Omd4AIs26hnZAtk91Ij2+2\nXcBxlSFVOznLPFJLEmgdLgeo73IM3BUBHCO6nQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e02301c25061a20a4e2a19cac497fa9c7a6c6376fe36862aa77bd6c9e1615bc00d454c30bbe23157ff3d00be80a009500e114", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0454a03902656bfaf4d6a54ff3429d9f9719bb61e6caf000e100992b31700e780e0f73f51614954acdddcaaa8b2311195b04ad3b19b01e150a39dc0cfaecc6498b18138ce612c492795687a488522644b3ddf7462c3c359bd091b7d39469571879", "wx": "54a03902656bfaf4d6a54ff3429d9f9719bb61e6caf000e100992b31700e780e0f73f51614954acdddcaaa8b2311195b", "wy": "04ad3b19b01e150a39dc0cfaecc6498b18138ce612c492795687a488522644b3ddf7462c3c359bd091b7d39469571879"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000454a03902656bfaf4d6a54ff3429d9f9719bb61e6caf000e100992b31700e780e0f73f51614954acdddcaaa8b2311195b04ad3b19b01e150a39dc0cfaecc6498b18138ce612c492795687a488522644b3ddf7462c3c359bd091b7d39469571879", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABFSgOQJla/r01qVP80Kdn5cZu2Hm\nyvAA4QCZKzFwDngOD3P1FhSVSs3dyqqLIxEZWwStOxmwHhUKOdwM+uzGSYsYE4zm\nEsSSeVaHpIhSJkSz3fdGLDw1m9CRt9OUaVcYeQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e02307094186882938a8672b125fea71e9b18ddbf8da18aa9def5b2785856f00351530c2ef88c55ffcf402fa0280254038451", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0446d10d749a47a4d3f25b6f28951a11f01a54c2413957a477162dabe0d08d8ae9b6a9f44b68ef341fb820b0c24c7a1c0e671ff166cd35d2f3cc821d58fa18e35d25e6033b9e790fce4818f9e570921c0034b381cc9ad254eeaf1b386e511b7c89", "wx": "46d10d749a47a4d3f25b6f28951a11f01a54c2413957a477162dabe0d08d8ae9b6a9f44b68ef341fb820b0c24c7a1c0e", "wy": "671ff166cd35d2f3cc821d58fa18e35d25e6033b9e790fce4818f9e570921c0034b381cc9ad254eeaf1b386e511b7c89"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000446d10d749a47a4d3f25b6f28951a11f01a54c2413957a477162dabe0d08d8ae9b6a9f44b68ef341fb820b0c24c7a1c0e671ff166cd35d2f3cc821d58fa18e35d25e6033b9e790fce4818f9e570921c0034b381cc9ad254eeaf1b386e511b7c89", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEbRDXSaR6TT8ltvKJUaEfAaVMJB\nOVekdxYtq+DQjYrptqn0S2jvNB+4ILDCTHocDmcf8WbNNdLzzIIdWPoY410l5gM7\nnnkPzkgY+eVwkhwANLOBzJrSVO6vGzhuURt8iQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e0230789eac6ff99e146b7ae25f90d7a0cabf36baf32d14916ee2aceea7cadc95d7221f56e5baee6d82a03307064ba32856e9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048ae92de10b244ac7f0deb6b102d075951d8c13b2960c2e98d7fb42b8abe90fd07a4a21b86eb4c77efe9adb6725676d1736063f3407c71627acaa83be9029c7a40e8aa896cb68a9c2fa2aaa1079035a283181cd3f2723b221d5a8747ad392a0f9", "wx": "008ae92de10b244ac7f0deb6b102d075951d8c13b2960c2e98d7fb42b8abe90fd07a4a21b86eb4c77efe9adb6725676d17", "wy": "36063f3407c71627acaa83be9029c7a40e8aa896cb68a9c2fa2aaa1079035a283181cd3f2723b221d5a8747ad392a0f9"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200048ae92de10b244ac7f0deb6b102d075951d8c13b2960c2e98d7fb42b8abe90fd07a4a21b86eb4c77efe9adb6725676d1736063f3407c71627acaa83be9029c7a40e8aa896cb68a9c2fa2aaa1079035a283181cd3f2723b221d5a8747ad392a0f9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIrpLeELJErH8N62sQLQdZUdjBOy\nlgwumNf7Qrir6Q/QekohuG60x37+mttnJWdtFzYGPzQHxxYnrKqDvpApx6QOiqiW\ny2ipwvoqqhB5A1ooMYHNPycjsiHVqHR605Kg+Q==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "extreme value for k", "msg": "313233343030", "sig": "306402301d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e023064dc78d112cd6ed67d4323b302650a606ed41415bd8cfc40ec7438a70ee3d8680420e5f602aed591a324760c58140642", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e8abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315", "wx": "1d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e", "wy": "008abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e8abe1d7520f9c2a45cb1eb8e95cfd55262b70b29feec5864e19c054ff99129280e4646217791811142820341263c5315", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABB0cZPBoz0X/oqY6gbfBP2uIR6Pn\nfvFP49t/yv4MvRDo6CbgNDbWRqrvh7LiR9SvHoq+HXUg+cKkXLHrjpXP1VJitwsp\n/uxYZOGcBU/5kSkoDkZGIXeRgRFCggNBJjxTFQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "invalid", "flags": []}, {"tcId": 384, "comment": "testing point duplication", "msg": "313233343030", "sig": "306402301fc115146e521d7ea33f3e128eb01db0f653dc45852c2b50301d639b778b13380e51d9366552cf2049156605d57adffc0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e01fb010d823eaa83b2ab83efbb166c8cb27865dfee67fe4f3115d4c98625e7fb9e8d6108188b996044c4fcd20acb993e", "wx": "1d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e", "wy": "01fb010d823eaa83b2ab83efbb166c8cb27865dfee67fe4f3115d4c98625e7fb9e8d6108188b996044c4fcd20acb993e"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200041d1c64f068cf45ffa2a63a81b7c13f6b8847a3e77ef14fe3db7fcafe0cbd10e8e826e03436d646aaef87b2e247d4af1e01fb010d823eaa83b2ab83efbb166c8cb27865dfee67fe4f3115d4c98625e7fb9e8d6108188b996044c4fcd20acb993e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABB0cZPBoz0X/oqY6gbfBP2uIR6Pn\nfvFP49t/yv4MvRDo6CbgNDbWRqrvh7LiR9SvHgH7AQ2CPqqDsquD77sWbIyyeGXf\n7mf+TzEV1MmGJef7no1hCBiLmWBExPzSCsuZPg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "testing point duplication", "msg": "313233343030", "sig": "3065023100f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "invalid", "flags": []}, {"tcId": 386, "comment": "testing point duplication", "msg": "313233343030", "sig": "306402301fc115146e521d7ea33f3e128eb01db0f653dc45852c2b50301d639b778b13380e51d9366552cf2049156605d57adffc0230141a7212a99a58bc947b0fed7945771fde747ddcd8c2e7d07227c6a1cf6e4e85afe3d0f47d12407008812bb745dc0e7c", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046c9aaba343cb2faf098319cc4d15ea218786f55c8cf0a8b668091170a6422f6c2498945a8164a4b6f27cdd11e800da501be961b37b09804610ce0df40dd8236c75a12d0c8014b163464a4aeba7cb18d20d3222083ec4a941852f24aa3d5d84e3", "wx": "6c9aaba343cb2faf098319cc4d15ea218786f55c8cf0a8b668091170a6422f6c2498945a8164a4b6f27cdd11e800da50", "wy": "1be961b37b09804610ce0df40dd8236c75a12d0c8014b163464a4aeba7cb18d20d3222083ec4a941852f24aa3d5d84e3"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200046c9aaba343cb2faf098319cc4d15ea218786f55c8cf0a8b668091170a6422f6c2498945a8164a4b6f27cdd11e800da501be961b37b09804610ce0df40dd8236c75a12d0c8014b163464a4aeba7cb18d20d3222083ec4a941852f24aa3d5d84e3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGyaq6NDyy+vCYMZzE0V6iGHhvVc\njPCotmgJEXCmQi9sJJiUWoFkpLbyfN0R6ADaUBvpYbN7CYBGEM4N9A3YI2x1oS0M\ngBSxY0ZKSuunyxjSDTIiCD7EqUGFLySqPV2E4w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "pseudorandom signature", "msg": "", "sig": "3064023065fd456814371d60883ffda5f74f36dc2d45886121770e29ed3163754716d12c1cab03a2cb6a6e3376fc96d8727bd1bf02301aa65e57932d05788413219b7ab23e5337f63fb2dcb0f89b4227d284a3fcbdf3c54c021a6c0ca42445bf802213121654", "result": "valid", "flags": []}, {"tcId": 388, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3064023001057e36ad00f79e7c1cfcf4dea301e4e2350644d5eff4d4c7f23cdd2f4f236093ff27e33eb44fd804b2f0daf5c327a402302a9b2b910dd23b994cac12f322828461094c8790481b392569c6674ac2eca74dd74957d94456548546b65bd50558f4a6", "result": "valid", "flags": []}, {"tcId": 389, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "306402306dd9d4e98c9c388240e95c49b2100afbe0d722f8a152651c61d7ef9bf46150e3cdf9bf6330e75e4bf2c294cd66e48d0602301282d33b5b79d4eaafa03a77bb8ba2c318291f6ea09d548b7704bb00910856dd360557e609add891c6435d7a80afddfb", "result": "valid", "flags": []}, {"tcId": 390, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "3065023046cb43798bc06dbe788a4f4b2b98130e3aae917f1d2a005656bd70a3288caf7c37d1dee0c9108828a69d2a1eeae113c60231008180d0c5ba1bed4f2b0d4d8ed7ea17916b63400397e7b6d70e7312c5ff0f4524a49abf7071c8ba470de64fb668570380", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04462117d2e33a7db1b95c8a6a3c7982f83da96817e749718caee7b6aa9c9da4e8f2ff7951674eed2b569ab846f59002a850e6606a9726a9209c9e945fbf6cbbc9a487c4a4d81c52ac3684c26c3392b9bd24f7184821be06f6448b24a8ffffffff", "wx": "462117d2e33a7db1b95c8a6a3c7982f83da96817e749718caee7b6aa9c9da4e8f2ff7951674eed2b569ab846f59002a8", "wy": "50e6606a9726a9209c9e945fbf6cbbc9a487c4a4d81c52ac3684c26c3392b9bd24f7184821be06f6448b24a8ffffffff"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004462117d2e33a7db1b95c8a6a3c7982f83da96817e749718caee7b6aa9c9da4e8f2ff7951674eed2b569ab846f59002a850e6606a9726a9209c9e945fbf6cbbc9a487c4a4d81c52ac3684c26c3392b9bd24f7184821be06f6448b24a8ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEYhF9LjOn2xuVyKajx5gvg9qWgX\n50lxjK7ntqqcnaTo8v95UWdO7StWmrhG9ZACqFDmYGqXJqkgnJ6UX79su8mkh8Sk\n2BxSrDaEwmwzkrm9JPcYSCG+BvZEiySo/////w==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3064023043a3ac2f3d2b4d3723a97930b023ee73010a7cf8d2a99372f3132bd7d9c83574de3ab86525efc4ee2c59799d5ff7efb4023034f59a3ea9f5267f8458afdaa3873e2336e0ab8a40ca1b797cbd977d192f2024f9eb8d39b37b9a238f208d66bacd27bf", "result": "valid", "flags": []}, {"tcId": 392, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "306402303531ada25b8d9af9b87e5224cd6a6d956c17dc323ef8980f497a6e7e44c83d69b74de791d62bceacaff7378863dd725b0230459d15539399409380af99d560c561217daa5c539729453067dd1aa4bd9df2b534920f0d6213261ecea16f0ed68536b1", "result": "valid", "flags": []}, {"tcId": 393, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30640230438a0cff9fcfcf587f8c40775ad44ea4b0ed69f2d547befe295d1fb9c24ddcb97f228027df552a06bf657b4c2027261502305e157630bb744fc8e7f75901de498e5af0b5511dfeee0c4c1f2e5c4aa0129de57b87a2a13ea59d187d51cbeb6ef22407", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "048cb91e81ee5901b71a59a4f7c8174ae05fe3ba00f699dcbc3c9233265c640587b3c165593c2d76b5ffc4b8dcbcb0e6553a0e5d14f2d0e8efe2bd8aa260d8ace06bf964c51bab8207070a2d30410bb6b87aeecb7fff802f2d4ea3caf6e0e7e726", "wx": "008cb91e81ee5901b71a59a4f7c8174ae05fe3ba00f699dcbc3c9233265c640587b3c165593c2d76b5ffc4b8dcbcb0e655", "wy": "3a0e5d14f2d0e8efe2bd8aa260d8ace06bf964c51bab8207070a2d30410bb6b87aeecb7fff802f2d4ea3caf6e0e7e726"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200048cb91e81ee5901b71a59a4f7c8174ae05fe3ba00f699dcbc3c9233265c640587b3c165593c2d76b5ffc4b8dcbcb0e6553a0e5d14f2d0e8efe2bd8aa260d8ace06bf964c51bab8207070a2d30410bb6b87aeecb7fff802f2d4ea3caf6e0e7e726", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIy5HoHuWQG3Glmk98gXSuBf47oA\n9pncvDySMyZcZAWHs8FlWTwtdrX/xLjcvLDmVToOXRTy0Ojv4r2KomDYrOBr+WTF\nG6uCBwcKLTBBC7a4eu7Lf/+ALy1Oo8r24OfnJg==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3064023016496c08c3076773fcd841a5e25e1a87108e0ba90f9727f539034bd2cf688e01a955686a15112e0590fc91e3995ff5f8023031b1b7338f74adba33712a83a7c685e7cd5f3be84ef951ecad50facb7c6ec393a3bac52ea7b1212bd92f4f45a9f8514c", "result": "valid", "flags": []}, {"tcId": 395, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "306602310087f3090292e79b722cde5aedafa4244f6eb460a280e2e050399b9d802391ad502108704a3c0bb9f9ae571c3f7dec6c0b02310089ae0043de38a585a1632c7211b78303afa3f8936154a6e65a6f729c3b1ec66a1775aa465af8eed6dfeaa5ba98cedb41", "result": "valid", "flags": []}, {"tcId": 396, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "30640230720822abefa91265a7b8d446ec3bc405fd192178aa1b85dd663396a896a32c119e64b1a20843f81edd43c03709b8dbc60230206ae95bb18d2d3844a39340872edba1611e3ea0e84cea7cb6cff282af414d8b5aa0be8aabc1b51b7121d426916b01b5", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0469ebf332e1eb2455324a7572a17977a4e2955108ee8bd81bd6d1f555d608687f5bbb39858ebee304985baa7d09c830bb672b9c96684dfc007f015e39cdada9fe16db5022bfd173348caafc528684621f97fba24f2c30e3dc728772e800000000", "wx": "69ebf332e1eb2455324a7572a17977a4e2955108ee8bd81bd6d1f555d608687f5bbb39858ebee304985baa7d09c830bb", "wy": "672b9c96684dfc007f015e39cdada9fe16db5022bfd173348caafc528684621f97fba24f2c30e3dc728772e800000000"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000469ebf332e1eb2455324a7572a17977a4e2955108ee8bd81bd6d1f555d608687f5bbb39858ebee304985baa7d09c830bb672b9c96684dfc007f015e39cdada9fe16db5022bfd173348caafc528684621f97fba24f2c30e3dc728772e800000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGnr8zLh6yRVMkp1cqF5d6TilVEI\n7ovYG9bR9VXWCGh/W7s5hY6+4wSYW6p9Ccgwu2crnJZoTfwAfwFeOc2tqf4W21Ai\nv9FzNIyq/FKGhGIfl/uiTyww49xyh3LoAAAAAA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402301e5027fcc630aa08750a4725919dd9072422a21aca9d3326bec3e6ac040ba9784951b1fda6f588e60dcb550b75793a4e02300df3224641f6804f4d1bf951051e087ce1fa7365c43bd27878626833f09190cc0a7fa29b16bc2ca0d34fd0660d24718f", "result": "valid", "flags": []}, {"tcId": 398, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402304e61e34740a9f6db0854faf205719a3d98ef644b86241b858fa22959c04395578bef7be35036ae7a9ffeb9a2173311f402301e967c3b6071d37560fd64a4fe0921b1d600f60d883fdec816836176c5e67ad05182aa080c7e2184c0710050d523f0e2", "result": "valid", "flags": []}, {"tcId": 399, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "306402302c3090c581e575da58a8f659f74c5eee566400eb1d91de0a950e787542e6572f73b9f6d4f81f1c8e42f9e460dac3c1dc0230756b1b693e7fe06686708c2a609854accd21e3195d84b72c11c873908d175dfc00c00ebbdf8e2bb6970f2f19785303cc", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044fb5688666673f104287428b5bae6bd82a5c69b523762aa739b24594a9a81297318df613f6b7379af47979ae7fffffff7e2d325b41fe831a23cb694cb80a30119c196143536ee334416ba437a419054c180a945154596b83d7f7c3a6b6059645", "wx": "4fb5688666673f104287428b5bae6bd82a5c69b523762aa739b24594a9a81297318df613f6b7379af47979ae7fffffff", "wy": "7e2d325b41fe831a23cb694cb80a30119c196143536ee334416ba437a419054c180a945154596b83d7f7c3a6b6059645"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044fb5688666673f104287428b5bae6bd82a5c69b523762aa739b24594a9a81297318df613f6b7379af47979ae7fffffff7e2d325b41fe831a23cb694cb80a30119c196143536ee334416ba437a419054c180a945154596b83d7f7c3a6b6059645", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABE+1aIZmZz8QQodCi1uua9gqXGm1\nI3YqpzmyRZSpqBKXMY32E/a3N5r0eXmuf////34tMltB/oMaI8tpTLgKMBGcGWFD\nU27jNEFrpDekGQVMGAqUUVRZa4PX98OmtgWWRQ==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30640230092f0ee1feeb79c054ae36235f8717e9ee72b466b1704d4fa78addfcd13518a64db2b2fdb06439acbc4c045fb2c23c3a02302371ca6d36f4266162ee5c657c71cea35dcec3632c5b220a6f23ace1ba6562a841aeeeefe87a7998adfaf185b8558e4a", "result": "valid", "flags": []}, {"tcId": 401, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "306402306c8f4be641afaf5bf91ce08974d284ece6aec74792247229fa86c6597eed3fb507b712bb77af0226e1bbb3bad632b0d80230775954fe8bf936157b7ab7a683f6dc1838a8718200621bc8bf2f32b778f6c8e8c656532b50de39ac22d22b37dccfd1f9", "result": "valid", "flags": []}, {"tcId": 402, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3064023076e5c07582177400df453114fed746f40704197897b4ca21b72e5b44d4ca40cfcaa55e4446355c91ea9767f38c8172df02300c6dd73eefbb4c06e823224d8efaa3ee934e4a97eed2833513b4d735ed06eb550b2a5fa7f86613d627d9db466afa6646", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0434770c73a7e42ce7a57d1de6e54f35f1752047f6513584c7b14bca17d7abc499f8ab037c70fd2e13a8b97b2ae263688622421615ba363f1ffe9a8f2fe0f6e246fda11462a3ec000c685e09a90dbcdc2af6467f9ee69b5e7bead9b8461f4a4be0", "wx": "34770c73a7e42ce7a57d1de6e54f35f1752047f6513584c7b14bca17d7abc499f8ab037c70fd2e13a8b97b2ae2636886", "wy": "22421615ba363f1ffe9a8f2fe0f6e246fda11462a3ec000c685e09a90dbcdc2af6467f9ee69b5e7bead9b8461f4a4be0"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000434770c73a7e42ce7a57d1de6e54f35f1752047f6513584c7b14bca17d7abc499f8ab037c70fd2e13a8b97b2ae263688622421615ba363f1ffe9a8f2fe0f6e246fda11462a3ec000c685e09a90dbcdc2af6467f9ee69b5e7bead9b8461f4a4be0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABDR3DHOn5CznpX0d5uVPNfF1IEf2\nUTWEx7FLyhfXq8SZ+KsDfHD9LhOouXsq4mNohiJCFhW6Nj8f/pqPL+D24kb9oRRi\no+wADGheCakNvNwq9kZ/nuabXnvq2bhGH0pL4A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "x-coordinate of the public key is large on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402300e44fdc33aed0c320e371e2a78e9f18fde83434e681afb05a5bdb0f43cac70e83ede56bf8c56acf70e054e2ffef549cf02301324b4cfe684d401eac15b0940f5835436d3a1028e27c1966dbf69fefef82748a05b4443c77c870789135755d0d184cf", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 404, "comment": "x-coordinate of the public key is large on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402305966acd8a7714f2015e36fd4fdb3452258ce0aaefb3972091b496bd530bbaf1ec67d7e37e50031b3eea44a8bb8f62c2002302a5f309d2fad55b93a7a3012cbda2845efaa4ea0d187d3824f4a6a9227730d3ab15246d8d0952c7ee8c0b9eb83d1c2a2", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 405, "comment": "x-coordinate of the public key is large on brainpoolP384t1", "msg": "4d657373616765", "sig": "30640230266eace657e1ec88a2adbb38a5afb4f750274ca614d1fde9ea39dff6f2a2aa69923e9a7489f06bf9d84c518cee57e55b02303d19027684ef221216f63a591d8e793524e4c1234a56ce415bb9ad9e2ebf25ac94a99261b9157d19daa5aa876291f308", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0486f0fc89b7861ec3bd582161aecfc95789ae402459eb7f3015b7dd24e20fc9b005c635fc290a0e2a9ff35863b7b82e3e01ebba489e923dad88146077914e3ae5c575e1bececec710962a18ffd91005776c4d9e4bd952c793587a70291ce478b4", "wx": "0086f0fc89b7861ec3bd582161aecfc95789ae402459eb7f3015b7dd24e20fc9b005c635fc290a0e2a9ff35863b7b82e3e", "wy": "01ebba489e923dad88146077914e3ae5c575e1bececec710962a18ffd91005776c4d9e4bd952c793587a70291ce478b4"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b0362000486f0fc89b7861ec3bd582161aecfc95789ae402459eb7f3015b7dd24e20fc9b005c635fc290a0e2a9ff35863b7b82e3e01ebba489e923dad88146077914e3ae5c575e1bececec710962a18ffd91005776c4d9e4bd952c793587a70291ce478b4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABIbw/Im3hh7DvVghYa7PyVeJrkAk\nWet/MBW33STiD8mwBcY1/CkKDiqf81hjt7guPgHrukiekj2tiBRgd5FOOuXFdeG+\nzs7HEJYqGP/ZEAV3bE2eS9lSx5NYenApHOR4tA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "x-coordinate of the public key is small on brainpoolP384t1", "msg": "4d657373616765", "sig": "3064023013de6eb532321c023092aa78c199f9ee4dce7a18df158c3e799461af9d96c2d38765a78fdb14404d199365de05bd44c502302514a0359bcb66122bf48c186a4bb2edccf305b06414b11f470d2512cadda129366f6072de715bc2babb8a3a5f260d9b", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 407, "comment": "x-coordinate of the public key is small on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402301308d3d9edfe3ad07e215a975b2b067e9f0b803371b3029f4388a3471f4db23f358aea5c03db62d77115c56c4962633b02304b8b1fe44b32cc669114a1ce0ba0555446d0c96a32cb602185e8fba414d3a831cbf5b519b0f90647dc45e30a1f23ef90", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 408, "comment": "x-coordinate of the public key is small on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402305da3df094155b8f8812d0c6345344e41c3b591b65b95fedbbcbd3c3a3bb1c1dbfc4d4c5b841b8f8874d59b07cf2288fc02304a1e4a8399abbdf246929b2559bb0fa404772755fc74523626aeef432fe4764df1e1f5c9b0f897ed8f1ffd7a88167f0e", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04082f7dceb585c5ba4894b0faf6604da888a311ad9f41731a1d3937168a10b0795a1fae496cb9a90739e1c0a6e531e8072c3b8568eaa1c6f541a665ce7a66f78ea2d5893103e6028add62356492d8b5ac6ab8901d59621c33416c33981bd594ec", "wx": "082f7dceb585c5ba4894b0faf6604da888a311ad9f41731a1d3937168a10b0795a1fae496cb9a90739e1c0a6e531e807", "wy": "2c3b8568eaa1c6f541a665ce7a66f78ea2d5893103e6028add62356492d8b5ac6ab8901d59621c33416c33981bd594ec"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b03620004082f7dceb585c5ba4894b0faf6604da888a311ad9f41731a1d3937168a10b0795a1fae496cb9a90739e1c0a6e531e8072c3b8568eaa1c6f541a665ce7a66f78ea2d5893103e6028add62356492d8b5ac6ab8901d59621c33416c33981bd594ec", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABAgvfc61hcW6SJSw+vZgTaiIoxGt\nn0FzGh05NxaKELB5Wh+uSWy5qQc54cCm5THoByw7hWjqocb1QaZlznpm946i1Ykx\nA+YCit1iNWSS2LWsariQHVliHDNBbDOYG9WU7A==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "x-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402300bf6fec0a5be27cddb0e7669ae06d15dfa75837f8ee72b47443ac845ffcd427b0893e10c85c20c7aa576fb70e87761ab02307418b6f374936adca8b07dc51545ee34ed2e9f56f3267033e30ea09a0acd31b6ce83503ee7e098627f8ba8b4c584341e", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 410, "comment": "x-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3064023003e306a86f6b2cb248fcb68d1d317a6042b7089e96d74c2f5b934e2e122831268a45e2185b7c21270e8b906cd372e6d702304c82ab6de6bc0194ac1a2e3480a0c80466af7d2a329d20b03151d1806a0bc0720f55d3781a7db9febe7d8bbd0a719bfa", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 411, "comment": "x-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3064023014d1df9b3db55ecc8d1e126625bdf5b6316bba1e7f4ea5ec77418c754a597563dc5dc291b7dd047782d518fe74e0be83023033ef701c440f280edf81a9632dde9dc17de5f438dcc19e9ca5919b4b73e62905e5f7e0bc9db0b14bc53327f79f70c6da", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046afe4ea7705492bda308b789d70da49457dde825d5258960a7a366e4665af9d326392c2672165ea4bbdc33374d88e7498475e6937a10a6f6a50f23de9126ba04e5650a1cd06a8066ca423339fc2ce53d91482744a4cdf2f937f76f12aae3f630", "wx": "6afe4ea7705492bda308b789d70da49457dde825d5258960a7a366e4665af9d326392c2672165ea4bbdc33374d88e749", "wy": "008475e6937a10a6f6a50f23de9126ba04e5650a1cd06a8066ca423339fc2ce53d91482744a4cdf2f937f76f12aae3f630"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200046afe4ea7705492bda308b789d70da49457dde825d5258960a7a366e4665af9d326392c2672165ea4bbdc33374d88e7498475e6937a10a6f6a50f23de9126ba04e5650a1cd06a8066ca423339fc2ce53d91482744a4cdf2f937f76f12aae3f630", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABGr+TqdwVJK9owi3idcNpJRX3egl\n1SWJYKejZuRmWvnTJjksJnIWXqS73DM3TYjnSYR15pN6EKb2pQ8j3pEmugTlZQoc\n0GqAZspCMzn8LOU9kUgnRKTN8vk3928SquP2MA==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402306a3a18400686635ae279c385b640d4fa080d9c44a5d421fe4be5a5ec7a8ae31b00bfa406e919e57e39c11360e670d8690230729c0b9ff77f88f810548d6db1835312a448114a3bd93cf59422faa2ea026f5d47627f0c11fb859112246d879c859568", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 413, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306502301ab8d6c31d4577f59ca5714c9eada979fdb9ec0cad32d8cb915dbd70492947187f5a52718e19982f7a2d4cb48b227723023100872e3ce7d1fd5ae180faf1990b11937558aa44ccdab631492b8925be84fbcb452148edad5bbfe48c06b8c9908ca252fd", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 414, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3066023100803ffc58f8150a9c4c229a7b522357f49f9a5f48f82d8bb982954395836e09eb5f8cf1f345ce284674bc369d046d5c8a0231008a9feb64c410cf3ae6261ad35f7e3e8da13129daf94944f8e08e9649cd006622c3d5c91ec5b9798a1be3a31533a0a851", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044bc65262c22d322ea89146ccb5c60c4287b65a35228743a5b9dcd15493bd8642478987c421637dd0715079ec90fb8cd47a45557ef653d0773dbe2630f8e000629ed8293e1aa4a96f3b159a245aa35ad92a1019c7e09a9ab75ba43c0786928237", "wx": "4bc65262c22d322ea89146ccb5c60c4287b65a35228743a5b9dcd15493bd8642478987c421637dd0715079ec90fb8cd4", "wy": "7a45557ef653d0773dbe2630f8e000629ed8293e1aa4a96f3b159a245aa35ad92a1019c7e09a9ab75ba43c0786928237"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200044bc65262c22d322ea89146ccb5c60c4287b65a35228743a5b9dcd15493bd8642478987c421637dd0715079ec90fb8cd47a45557ef653d0773dbe2630f8e000629ed8293e1aa4a96f3b159a245aa35ad92a1019c7e09a9ab75ba43c0786928237", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABEvGUmLCLTIuqJFGzLXGDEKHtlo1\nIodDpbnc0VSTvYZCR4mHxCFjfdBxUHnskPuM1HpFVX72U9B3Pb4mMPjgAGKe2Ck+\nGqSpbzsVmiRao1rZKhAZx+CamrdbpDwHhpKCNw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306502302ed569f12dbe30a2abf02190bb9e4de7e218e9fd705dc71cbe1480022781b2a2213c3ef2f91052e90840a18f74e375ae0231008872b566f387c2bcb639df9c2d866f7631df290c5f66c264d4949e256383b1b4b2098c120f13449d9d7bff6891919c88", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 416, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402304b7e5651b035959295092e2efe548da52206c8d0e48ba43e2b8ecd98ece25dc08955b6e7b05e38c4e22829d1658711b5023044a973b75528400cef3f63f55f2154d48bb0b826214200d3f33c7bc31155242d4e24f07ed19606fdb2c8ecaeb6981eb7", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 417, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "306402301ecadaceaa479fa4e9aabac4210b1ab77fc1d13a9c4cb022826bb1806575115834a6ecb9dec3e668b8c91d4aca283dc902302de8965a66d56545ad84fdaee16fffa0eb31022186a5b6be2a2475958b9ad72f483ebd4b255748a811806bcd428acfd7", "result": "valid", "flags": ["GroupIsomorphism"]}]}, {"key": {"curve": "brainpoolP384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042ac393f20c110e3f97065304397eae0e23187b2b6163dc66083e82aff568426843056aff8dc23eebce297f747830e21734c935671391c6efa8b46c5c37b3f84a82e429a7580feb9a1383b55c83a9398e8ecc7b15d699e63962329102a1576f2b", "wx": "2ac393f20c110e3f97065304397eae0e23187b2b6163dc66083e82aff568426843056aff8dc23eebce297f747830e217", "wy": "34c935671391c6efa8b46c5c37b3f84a82e429a7580feb9a1383b55c83a9398e8ecc7b15d699e63962329102a1576f2b"}, "keyDer": "307a301406072a8648ce3d020106092b240303020801010b036200042ac393f20c110e3f97065304397eae0e23187b2b6163dc66083e82aff568426843056aff8dc23eebce297f747830e21734c935671391c6efa8b46c5c37b3f84a82e429a7580feb9a1383b55c83a9398e8ecc7b15d699e63962329102a1576f2b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nM<PERSON>owFAYHKoZIzj0CAQYJKyQDAwIIAQELA2IABCrDk/IMEQ4/lwZTBDl+rg4jGHsr\nYWPcZgg+gq/1aEJoQwVq/43CPuvOKX90eDDiFzTJNWcTkcbvqLRsXDez+EqC5Cmn\nWA/rmhODtVyDqTmOjsx7FdaZ5jliMpECoVdvKw==\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3064023037e256872340da9dc884fd00daa14628372b4bedc0a8a09f9d7513521d3b803a78dc0edbab3c7dc2b2014baf7a9d210e02301ba4b4087973070cca9b957650177eeb41c557731596a966b0b7f68717d8e7b554afd07c2937c95403a90c3a05fa964b", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 419, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3063022f128c199dc27677f23feae28a9b28813cbc3b02fca493005a67c3126a705c49b982cb5817ee2c81161e80b738bbb512023073cb6d4547771d254be74348955bee979071358aa3afd62a5838179a0965465aec79bd6cbd9b8b2aa2c79bb88ab21592", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 420, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP384t1", "msg": "4d657373616765", "sig": "3065023100818b0fd6ca0978a59cad3fa15e84db2896f39b2aa462f0583834fa4444d153fe61e0c93071ba96c5ffa7193f77b806f302301d2d6144172385f857db4b7e7e863962eacacdec034b4b4a9dd1af272604403f39f45a21948b30976e738e9e98fd9cee", "result": "valid", "flags": ["GroupIsomorphism"]}]}]}