# Generated with generate_ssl_tests.pl

num_tests = 6

test-0 = 0-disable-encrypt-then-mac-server-sha
test-1 = 1-disable-encrypt-then-mac-client-sha
test-2 = 2-disable-encrypt-then-mac-both-sha
test-3 = 3-disable-encrypt-then-mac-server-sha2
test-4 = 4-disable-encrypt-then-mac-client-sha2
test-5 = 5-disable-encrypt-then-mac-both-sha2
# ===========================================================

[0-disable-encrypt-then-mac-server-sha]
ssl_conf = 0-disable-encrypt-then-mac-server-sha-ssl

[0-disable-encrypt-then-mac-server-sha-ssl]
server = 0-disable-encrypt-then-mac-server-sha-server
client = 0-disable-encrypt-then-mac-server-sha-client

[0-disable-encrypt-then-mac-server-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -EncryptThenMac
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-disable-encrypt-then-mac-server-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = Success


# ===========================================================

[1-disable-encrypt-then-mac-client-sha]
ssl_conf = 1-disable-encrypt-then-mac-client-sha-ssl

[1-disable-encrypt-then-mac-client-sha-ssl]
server = 1-disable-encrypt-then-mac-client-sha-server
client = 1-disable-encrypt-then-mac-client-sha-client

[1-disable-encrypt-then-mac-client-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-disable-encrypt-then-mac-client-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
Options = -EncryptThenMac
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = Success


# ===========================================================

[2-disable-encrypt-then-mac-both-sha]
ssl_conf = 2-disable-encrypt-then-mac-both-sha-ssl

[2-disable-encrypt-then-mac-both-sha-ssl]
server = 2-disable-encrypt-then-mac-both-sha-server
client = 2-disable-encrypt-then-mac-both-sha-client

[2-disable-encrypt-then-mac-both-sha-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -EncryptThenMac
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-disable-encrypt-then-mac-both-sha-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
Options = -EncryptThenMac
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = Success


# ===========================================================

[3-disable-encrypt-then-mac-server-sha2]
ssl_conf = 3-disable-encrypt-then-mac-server-sha2-ssl

[3-disable-encrypt-then-mac-server-sha2-ssl]
server = 3-disable-encrypt-then-mac-server-sha2-server
client = 3-disable-encrypt-then-mac-server-sha2-client

[3-disable-encrypt-then-mac-server-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -EncryptThenMac
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-disable-encrypt-then-mac-server-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = Success


# ===========================================================

[4-disable-encrypt-then-mac-client-sha2]
ssl_conf = 4-disable-encrypt-then-mac-client-sha2-ssl

[4-disable-encrypt-then-mac-client-sha2-ssl]
server = 4-disable-encrypt-then-mac-client-sha2-server
client = 4-disable-encrypt-then-mac-client-sha2-client

[4-disable-encrypt-then-mac-client-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-disable-encrypt-then-mac-client-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
Options = -EncryptThenMac
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = Success


# ===========================================================

[5-disable-encrypt-then-mac-both-sha2]
ssl_conf = 5-disable-encrypt-then-mac-both-sha2-ssl

[5-disable-encrypt-then-mac-both-sha2-ssl]
server = 5-disable-encrypt-then-mac-both-sha2-server
client = 5-disable-encrypt-then-mac-both-sha2-client

[5-disable-encrypt-then-mac-both-sha2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Options = -EncryptThenMac
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-disable-encrypt-then-mac-both-sha2-client]
CipherString = AES128-SHA256
MaxProtocol = TLSv1.2
Options = -EncryptThenMac
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = Success


