krb5 API
========


Frequently used public interfaces
----------------------------------

.. toctree::
   :maxdepth: 1

   krb5_build_principal.rst
   krb5_build_principal_alloc_va.rst
   krb5_build_principal_ext.rst
   krb5_cc_close.rst
   krb5_cc_default.rst
   krb5_cc_default_name.rst
   krb5_cc_destroy.rst
   krb5_cc_dup.rst
   krb5_cc_get_name.rst
   krb5_cc_get_principal.rst
   krb5_cc_get_type.rst
   krb5_cc_initialize.rst
   krb5_cc_new_unique.rst
   krb5_cc_resolve.rst
   krb5_change_password.rst
   krb5_chpw_message.rst
   krb5_expand_hostname.rst
   krb5_free_context.rst
   krb5_free_error_message.rst
   krb5_free_principal.rst
   krb5_fwd_tgt_creds.rst
   krb5_get_default_realm.rst
   krb5_get_error_message.rst
   krb5_get_host_realm.rst
   krb5_get_credentials.rst
   krb5_get_fallback_host_realm.rst
   krb5_get_init_creds_keytab.rst
   krb5_get_init_creds_opt_alloc.rst
   krb5_get_init_creds_opt_free.rst
   krb5_get_init_creds_opt_get_fast_flags.rst
   krb5_get_init_creds_opt_set_address_list.rst
   krb5_get_init_creds_opt_set_anonymous.rst
   krb5_get_init_creds_opt_set_canonicalize.rst
   krb5_get_init_creds_opt_set_change_password_prompt.rst
   krb5_get_init_creds_opt_set_etype_list.rst
   krb5_get_init_creds_opt_set_expire_callback.rst
   krb5_get_init_creds_opt_set_fast_ccache.rst
   krb5_get_init_creds_opt_set_fast_ccache_name.rst
   krb5_get_init_creds_opt_set_fast_flags.rst
   krb5_get_init_creds_opt_set_forwardable.rst
   krb5_get_init_creds_opt_set_in_ccache.rst
   krb5_get_init_creds_opt_set_out_ccache.rst
   krb5_get_init_creds_opt_set_pa.rst
   krb5_get_init_creds_opt_set_pac_request.rst
   krb5_get_init_creds_opt_set_preauth_list.rst
   krb5_get_init_creds_opt_set_proxiable.rst
   krb5_get_init_creds_opt_set_renew_life.rst
   krb5_get_init_creds_opt_set_responder.rst
   krb5_get_init_creds_opt_set_salt.rst
   krb5_get_init_creds_opt_set_tkt_life.rst
   krb5_get_init_creds_password.rst
   krb5_get_profile.rst
   krb5_get_prompt_types.rst
   krb5_get_renewed_creds.rst
   krb5_get_validated_creds.rst
   krb5_init_context.rst
   krb5_init_secure_context.rst
   krb5_is_config_principal.rst
   krb5_is_thread_safe.rst
   krb5_kt_close.rst
   krb5_kt_client_default.rst
   krb5_kt_default.rst
   krb5_kt_default_name.rst
   krb5_kt_dup.rst
   krb5_kt_get_name.rst
   krb5_kt_get_type.rst
   krb5_kt_resolve.rst
   krb5_kuserok.rst
   krb5_parse_name.rst
   krb5_parse_name_flags.rst
   krb5_principal_compare.rst
   krb5_principal_compare_any_realm.rst
   krb5_principal_compare_flags.rst
   krb5_prompter_posix.rst
   krb5_realm_compare.rst
   krb5_responder_get_challenge.rst
   krb5_responder_list_questions.rst
   krb5_responder_set_answer.rst
   krb5_responder_otp_get_challenge.rst
   krb5_responder_otp_set_answer.rst
   krb5_responder_otp_challenge_free.rst
   krb5_responder_pkinit_get_challenge.rst
   krb5_responder_pkinit_set_answer.rst
   krb5_responder_pkinit_challenge_free.rst
   krb5_set_default_realm.rst
   krb5_set_password.rst
   krb5_set_password_using_ccache.rst
   krb5_set_principal_realm.rst
   krb5_set_trace_callback.rst
   krb5_set_trace_filename.rst
   krb5_sname_match.rst
   krb5_sname_to_principal.rst
   krb5_unparse_name.rst
   krb5_unparse_name_ext.rst
   krb5_unparse_name_flags.rst
   krb5_unparse_name_flags_ext.rst
   krb5_us_timeofday.rst
   krb5_verify_authdata_kdc_issued.rst

Rarely used public interfaces
--------------------------------

.. toctree::
   :maxdepth: 1

   krb5_425_conv_principal.rst
   krb5_524_conv_principal.rst
   krb5_address_compare.rst
   krb5_address_order.rst
   krb5_address_search.rst
   krb5_allow_weak_crypto.rst
   krb5_aname_to_localname.rst
   krb5_anonymous_principal.rst
   krb5_anonymous_realm.rst
   krb5_appdefault_boolean.rst
   krb5_appdefault_string.rst
   krb5_auth_con_free.rst
   krb5_auth_con_genaddrs.rst
   krb5_auth_con_get_checksum_func.rst
   krb5_auth_con_getaddrs.rst
   krb5_auth_con_getauthenticator.rst
   krb5_auth_con_getflags.rst
   krb5_auth_con_getkey.rst
   krb5_auth_con_getkey_k.rst
   krb5_auth_con_getlocalseqnumber.rst
   krb5_auth_con_getrcache.rst
   krb5_auth_con_getrecvsubkey.rst
   krb5_auth_con_getrecvsubkey_k.rst
   krb5_auth_con_getremoteseqnumber.rst
   krb5_auth_con_getsendsubkey.rst
   krb5_auth_con_getsendsubkey_k.rst
   krb5_auth_con_init.rst
   krb5_auth_con_set_checksum_func.rst
   krb5_auth_con_set_req_cksumtype.rst
   krb5_auth_con_setaddrs.rst
   krb5_auth_con_setflags.rst
   krb5_auth_con_setports.rst
   krb5_auth_con_setrcache.rst
   krb5_auth_con_setrecvsubkey.rst
   krb5_auth_con_setrecvsubkey_k.rst
   krb5_auth_con_setsendsubkey.rst
   krb5_auth_con_setsendsubkey_k.rst
   krb5_auth_con_setuseruserkey.rst
   krb5_cc_cache_match.rst
   krb5_cc_copy_creds.rst
   krb5_cc_end_seq_get.rst
   krb5_cc_get_config.rst
   krb5_cc_get_flags.rst
   krb5_cc_get_full_name.rst
   krb5_cc_move.rst
   krb5_cc_next_cred.rst
   krb5_cc_remove_cred.rst
   krb5_cc_retrieve_cred.rst
   krb5_cc_select.rst
   krb5_cc_set_config.rst
   krb5_cc_set_default_name.rst
   krb5_cc_set_flags.rst
   krb5_cc_start_seq_get.rst
   krb5_cc_store_cred.rst
   krb5_cc_support_switch.rst
   krb5_cc_switch.rst
   krb5_cccol_cursor_free.rst
   krb5_cccol_cursor_new.rst
   krb5_cccol_cursor_next.rst
   krb5_cccol_have_content.rst
   krb5_clear_error_message.rst
   krb5_check_clockskew.rst
   krb5_copy_addresses.rst
   krb5_copy_authdata.rst
   krb5_copy_authenticator.rst
   krb5_copy_checksum.rst
   krb5_copy_context.rst
   krb5_copy_creds.rst
   krb5_copy_data.rst
   krb5_copy_error_message.rst
   krb5_copy_keyblock.rst
   krb5_copy_keyblock_contents.rst
   krb5_copy_principal.rst
   krb5_copy_ticket.rst
   krb5_find_authdata.rst
   krb5_free_addresses.rst
   krb5_free_ap_rep_enc_part.rst
   krb5_free_authdata.rst
   krb5_free_authenticator.rst
   krb5_free_cred_contents.rst
   krb5_free_creds.rst
   krb5_free_data.rst
   krb5_free_data_contents.rst
   krb5_free_default_realm.rst
   krb5_free_enctypes.rst
   krb5_free_error.rst
   krb5_free_host_realm.rst
   krb5_free_keyblock.rst
   krb5_free_keyblock_contents.rst
   krb5_free_keytab_entry_contents.rst
   krb5_free_string.rst
   krb5_free_ticket.rst
   krb5_free_unparsed_name.rst
   krb5_get_etype_info.rst
   krb5_get_permitted_enctypes.rst
   krb5_get_server_rcache.rst
   krb5_get_time_offsets.rst
   krb5_init_context_profile.rst
   krb5_init_creds_free.rst
   krb5_init_creds_get.rst
   krb5_init_creds_get_creds.rst
   krb5_init_creds_get_error.rst
   krb5_init_creds_get_times.rst
   krb5_init_creds_init.rst
   krb5_init_creds_set_keytab.rst
   krb5_init_creds_set_password.rst
   krb5_init_creds_set_service.rst
   krb5_init_creds_step.rst
   krb5_init_keyblock.rst
   krb5_is_referral_realm.rst
   krb5_kt_add_entry.rst
   krb5_kt_end_seq_get.rst
   krb5_kt_get_entry.rst
   krb5_kt_have_content.rst
   krb5_kt_next_entry.rst
   krb5_kt_read_service_key.rst
   krb5_kt_remove_entry.rst
   krb5_kt_start_seq_get.rst
   krb5_make_authdata_kdc_issued.rst
   krb5_marshal_credentials.rst
   krb5_merge_authdata.rst
   krb5_mk_1cred.rst
   krb5_mk_error.rst
   krb5_mk_ncred.rst
   krb5_mk_priv.rst
   krb5_mk_rep.rst
   krb5_mk_rep_dce.rst
   krb5_mk_req.rst
   krb5_mk_req_extended.rst
   krb5_mk_safe.rst
   krb5_os_localaddr.rst
   krb5_pac_add_buffer.rst
   krb5_pac_free.rst
   krb5_pac_get_buffer.rst
   krb5_pac_get_types.rst
   krb5_pac_init.rst
   krb5_pac_parse.rst
   krb5_pac_sign.rst
   krb5_pac_sign_ext.rst
   krb5_pac_verify.rst
   krb5_pac_verify_ext.rst
   krb5_pac_get_client_info.rst
   krb5_prepend_error_message.rst
   krb5_principal2salt.rst
   krb5_rd_cred.rst
   krb5_rd_error.rst
   krb5_rd_priv.rst
   krb5_rd_rep.rst
   krb5_rd_rep_dce.rst
   krb5_rd_req.rst
   krb5_rd_safe.rst
   krb5_read_password.rst
   krb5_salttype_to_string.rst
   krb5_server_decrypt_ticket_keytab.rst
   krb5_set_default_tgs_enctypes.rst
   krb5_set_error_message.rst
   krb5_set_kdc_recv_hook.rst
   krb5_set_kdc_send_hook.rst
   krb5_set_real_time.rst
   krb5_string_to_cksumtype.rst
   krb5_string_to_deltat.rst
   krb5_string_to_enctype.rst
   krb5_string_to_salttype.rst
   krb5_string_to_timestamp.rst
   krb5_timeofday.rst
   krb5_timestamp_to_sfstring.rst
   krb5_timestamp_to_string.rst
   krb5_tkt_creds_free.rst
   krb5_tkt_creds_get.rst
   krb5_tkt_creds_get_creds.rst
   krb5_tkt_creds_get_times.rst
   krb5_tkt_creds_init.rst
   krb5_tkt_creds_step.rst
   krb5_unmarshal_credentials.rst
   krb5_verify_init_creds.rst
   krb5_verify_init_creds_opt_init.rst
   krb5_verify_init_creds_opt_set_ap_req_nofail.rst
   krb5_vprepend_error_message.rst
   krb5_vset_error_message.rst
   krb5_vwrap_error_message.rst
   krb5_wrap_error_message.rst


Public interfaces that should not be called directly
-------------------------------------------------------

.. toctree::
   :maxdepth: 1

   krb5_c_block_size.rst
   krb5_c_checksum_length.rst
   krb5_c_crypto_length.rst
   krb5_c_crypto_length_iov.rst
   krb5_c_decrypt.rst
   krb5_c_decrypt_iov.rst
   krb5_c_derive_prfplus.rst
   krb5_c_encrypt.rst
   krb5_c_encrypt_iov.rst
   krb5_c_encrypt_length.rst
   krb5_c_enctype_compare.rst
   krb5_c_free_state.rst
   krb5_c_fx_cf2_simple.rst
   krb5_c_init_state.rst
   krb5_c_is_coll_proof_cksum.rst
   krb5_c_is_keyed_cksum.rst
   krb5_c_keyed_checksum_types.rst
   krb5_c_keylengths.rst
   krb5_c_make_checksum.rst
   krb5_c_make_checksum_iov.rst
   krb5_c_make_random_key.rst
   krb5_c_padding_length.rst
   krb5_c_prf.rst
   krb5_c_prfplus.rst
   krb5_c_prf_length.rst
   krb5_c_random_add_entropy.rst
   krb5_c_random_make_octets.rst
   krb5_c_random_os_entropy.rst
   krb5_c_random_to_key.rst
   krb5_c_string_to_key.rst
   krb5_c_string_to_key_with_params.rst
   krb5_c_valid_cksumtype.rst
   krb5_c_valid_enctype.rst
   krb5_c_verify_checksum.rst
   krb5_c_verify_checksum_iov.rst
   krb5_cksumtype_to_string.rst
   krb5_decode_authdata_container.rst
   krb5_decode_ticket.rst
   krb5_deltat_to_string.rst
   krb5_encode_authdata_container.rst
   krb5_enctype_to_name.rst
   krb5_enctype_to_string.rst
   krb5_free_checksum.rst
   krb5_free_checksum_contents.rst
   krb5_free_cksumtypes.rst
   krb5_free_tgt_creds.rst
   krb5_k_create_key.rst
   krb5_k_decrypt.rst
   krb5_k_decrypt_iov.rst
   krb5_k_encrypt.rst
   krb5_k_encrypt_iov.rst
   krb5_k_free_key.rst
   krb5_k_key_enctype.rst
   krb5_k_key_keyblock.rst
   krb5_k_make_checksum.rst
   krb5_k_make_checksum_iov.rst
   krb5_k_prf.rst
   krb5_k_reference_key.rst
   krb5_k_verify_checksum.rst
   krb5_k_verify_checksum_iov.rst


Legacy convenience interfaces
------------------------------

.. toctree::
   :maxdepth: 1

   krb5_recvauth.rst
   krb5_recvauth_version.rst
   krb5_sendauth.rst


Deprecated public interfaces
------------------------------

.. toctree::
   :maxdepth: 1

   krb5_524_convert_creds.rst
   krb5_auth_con_getlocalsubkey.rst
   krb5_auth_con_getremotesubkey.rst
   krb5_auth_con_initivector.rst
   krb5_build_principal_va.rst
   krb5_c_random_seed.rst
   krb5_calculate_checksum.rst
   krb5_checksum_size.rst
   krb5_encrypt.rst
   krb5_decrypt.rst
   krb5_eblock_enctype.rst
   krb5_encrypt_size.rst
   krb5_finish_key.rst
   krb5_finish_random_key.rst
   krb5_cc_gen_new.rst
   krb5_get_credentials_renew.rst
   krb5_get_credentials_validate.rst
   krb5_get_in_tkt_with_password.rst
   krb5_get_in_tkt_with_skey.rst
   krb5_get_in_tkt_with_keytab.rst
   krb5_get_init_creds_opt_init.rst
   krb5_init_random_key.rst
   krb5_kt_free_entry.rst
   krb5_random_key.rst
   krb5_process_key.rst
   krb5_string_to_key.rst
   krb5_use_enctype.rst
   krb5_verify_checksum.rst
