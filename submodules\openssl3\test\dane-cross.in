# Copyright 2016-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html
#
# Blank and comment lines ignored.
#
# The first line in each block takes the form:
#
#       <TLSA-count> <cert-count> <nonamechecks> <desired-verify-result> <desired-match-depth>
#
# It is followed by <TLSA-count> lines of the form:
#
#       <usage> <selector> <mtype> <hex-data>
#
# and finally, by <cert-count> certificates.

# 1
# Ensure TLSA with direct root works when peer chain provides a
# cross-cert.
1 4 0 0 2
2 0 0 308202f1308201d9a003020102020101300d06092a864886f70d01010b050030123110300e06035504030c07526f6f742043413020170d3136303131353038313934395a180f32313136303131363038313934395a30123110300e06035504030c07526f6f7420434130820122300d06092a864886f70d01010105000382010f003082010a0282010100e1e600f506bca04638790f3f1e7119136d02df2b9b766bfcacb021edd69108423ba56335ec525ba0a24ac5d6009403975a3db96728d27de3215ca4edc63c8d8e84d19d54c33dd974a296766771f9c61a5077b3fd78ee5be0e38c8923c29a22ec3ed4379e07cdc02b5511173a34421c690d3a18b0ed15945ec6fd9e87c61ce7941a92c05f05c17343e503de0991c924ced88e8b7e2ee03148d1862a6a55267ae80b364e97b1e0a4c9da5c694b0380085683a31fb22ab8c14cfec5b14dd4c001aaedbece25bdee69adf607cd6e2ba18954f568eb9809edc96066cb33624a03b068d60eab5fc135557efd46eebaebdcb6a2a3ed857f77dfb56822437e662423afc50203010001a350304e301d0603551d0e041604148ef525af1e9b6b843d7c809ac9539c4d72f7a352301f0603551d230418301680148ef525af1e9b6b843d7c809ac9539c4d72f7a352300c0603551d13040530030101ff300d06092a864886f70d01010b05000382010100c91449c76ed660ea203d76693df00cb7ca6d6a9affba02d618b9706f32b24a8c8ba68576fd8340bd300607dd2216aeb1fee8e3acae35fc44b4a77bf7f3f41fbb1a36e2071981cfe860b57652a47eb860b1ebca763962d872d06c011b5858e1203e11c56fd695c5c3902b2647b62bc35f4c0b197fa7a99a075fd21899cd2c6e944144ccf146c0a16f30f9adef6467936b8248c0e8327b8d88761a2b4e33aa085370ddf7ea64ddb084905520472f6a37f93e0327aa1f541c6f92d4f8c4e6970f1b9b2ce630e05981d7a0b4ee07b2170130ed39e0a481dd649f04f0ce6c4859d2f9bf970eb74c68bcf3220cb65926714da0d112a979023de86e907aa1f2285de9f0
subject=CN = server.example
issuer=CN = CA
notBefore=Jan 15 08:19:49 2016 GMT
notAfter=Jan 16 08:19:49 2116 GMT
-----BEGIN CERTIFICATE-----
MIIDIDCCAgigAwIBAgIBAjANBgkqhkiG9w0BAQsFADANMQswCQYDVQQDDAJDQTAg
Fw0xNjAxMTUwODE5NDlaGA8yMTE2MDExNjA4MTk0OVowGTEXMBUGA1UEAwwOc2Vy
dmVyLmV4YW1wbGUwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCo/4lY
YYWu3tssD9Vz++K3qBt6dWAr1H08c3a1rt6TL38kkG3JHPSKOM2fooAWVsu0LLuT
5Rcf/w3GQ/4xNPgo2HXpo7uIgu+jcuJTYgVFTeAxl++qnRDSWA2eBp4yuxsIVl1l
Dz9mjsI2oBH/wFk1/Ukc3RxCMwZ4rgQ4I+XndWfTlK1aqUAfrFkQ9QzBZK1KxMY1
U7OWaoIbFYvRmavknm+UqtKW5Vf7jJFkijwkFsbSGb6CYBM7YrDtPh2zyvlr3zG5
ep5LR2inKcc/SuIiJ7TvkGPX79ByST5brbkb1Ctvhmjd1XMSuEPJ3EEPoqNGT4tn
iIQPYf55NB9KiR+3AgMBAAGjfTB7MB0GA1UdDgQWBBTnm+IqrYpsOst2UeWOB5gi
l+FzojAfBgNVHSMEGDAWgBS0ETPx1+Je91OeICIQT4YGvx/JXjAJBgNVHRMEAjAA
MBMGA1UdJQQMMAoGCCsGAQUFBwMBMBkGA1UdEQQSMBCCDnNlcnZlci5leGFtcGxl
MA0GCSqGSIb3DQEBCwUAA4IBAQBBtDxPYULl5b7VFC7/U0NgV8vTJk4zpPnUMMQ4
QF2AWDFAek8oLKrz18KQ8M/DEhDxgkaoeXEMLT6BJUEVNYuFEYHEDGarl0nMDRXL
xOgAExfz3Tf/pjsLaha5aWH7NyCSKWC+lYkIOJ/Kb/m/6QsDJoXsEC8AhrPfqJhz
UzsCoxIlaDWqawH4+S8bdeX0tvs2VtJk/WOJHxMqXra6kgI4fAgyvr2kIZHinQ3y
cgX40uAC38bwpE95kJ7FhSfQlE1Rt7sOspUj098Dd0RNDn2uKyOTxEqIELHfw4AX
O3XAzt8qDyho8nEd/xiQ6qgsQnvXa+hSRJw42g3/czVskxRx
-----END CERTIFICATE-----
subject=CN = CA
issuer=CN = Root CA
notBefore=Jan 15 08:19:49 2016 GMT
notAfter=Jan 16 08:19:49 2116 GMT
-----BEGIN CERTIFICATE-----
MIIC7DCCAdSgAwIBAgIBAjANBgkqhkiG9w0BAQsFADASMRAwDgYDVQQDDAdSb290
IENBMCAXDTE2MDExNTA4MTk0OVoYDzIxMTYwMTE2MDgxOTQ5WjANMQswCQYDVQQD
DAJDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJadpD0ASxxfxsvd
j9IxsogVzMSGLFziaYuE9KejU9+R479RifvwfBANO62sNWJ19X//9G5UjwWmkiOz
n1k50DkYsBBA3mJzik6wjt/c58lBIlSEgAgpvDU8ht8w3t20JP9+YqXAeugqFj/W
l9rFQtsvaWSRywjXVlp5fxuEQelNnXcJEKhsKTNExsBUZebo4/J1BWpklWzA9P0l
YW5INvDAAwcF1nzlEf0Y6Eot03IMNyg2MTE4hehxjdgCSci8GYnFirE/ojXqqpAc
ZGh7r2dqWgZUD1Dh+bT2vjrUzj8eTH3GdzI+oljt29102JIUaqj3yzRYkah8FLF9
CLNNsUcCAwEAAaNQME4wHQYDVR0OBBYEFLQRM/HX4l73U54gIhBPhga/H8leMB8G
A1UdIwQYMBaAFI71Ja8em2uEPXyAmslTnE1y96NSMAwGA1UdEwQFMAMBAf8wDQYJ
KoZIhvcNAQELBQADggEBADnZ9uXGAdwfNC3xuERIlBwgLROeBRGgcfHWdXZB/tWk
IM9ox88wYKWynanPbra4n0zhepooKt+naeY2HLR8UgwT6sTi0Yfld9mjytA8/DP6
AcqtIDDf60vNI00sgxjgZqofVayA9KShzIPzjBec4zI1sg5YzoSNyH28VXFstEpi
8CVtmRYQHhc2gDI9MGge4sHRYwaIFkegzpwcEUnp6tTVe9ZvHawgsXF/rCGfH4M6
uNO0D+9Md1bdW7382yOtWbkyibsugqnfBYCUH6hAhDlfYzpba2Smb0roc6Crq7HR
5HpEYY6qEir9wFMkD5MZsWrNRGRuzd5am82J+aaHz/4=
-----END CERTIFICATE-----
subject=CN = Root CA
issuer=CN = Cross Root
notBefore=Aug 30 18:36:38 2021 GMT
notAfter=Aug 31 18:36:38 2121 GMT
-----BEGIN CERTIFICATE-----
MIIC9zCCAd+gAwIBAgIBAjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApDcm9z
cyBSb290MCAXDTIxMDgzMDE4MzYzOFoYDzIxMjEwODMxMTgzNjM4WjASMRAwDgYD
VQQDDAdSb290IENBMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4eYA
9Qa8oEY4eQ8/HnEZE20C3yubdmv8rLAh7daRCEI7pWM17FJboKJKxdYAlAOXWj25
ZyjSfeMhXKTtxjyNjoTRnVTDPdl0opZ2Z3H5xhpQd7P9eO5b4OOMiSPCmiLsPtQ3
ngfNwCtVERc6NEIcaQ06GLDtFZRexv2eh8Yc55QaksBfBcFzQ+UD3gmRySTO2I6L
fi7gMUjRhipqVSZ66As2Tpex4KTJ2lxpSwOACFaDox+yKrjBTP7FsU3UwAGq7b7O
Jb3uaa32B81uK6GJVPVo65gJ7clgZsszYkoDsGjWDqtfwTVVfv1G7rrr3Laio+2F
f3fftWgiQ35mJCOvxQIDAQABo1MwUTAdBgNVHQ4EFgQUjvUlrx6ba4Q9fICayVOc
TXL3o1IwHwYDVR0jBBgwFoAUL16/ihJvr2w9I5k63jjZ13SPW20wDwYDVR0TAQH/
BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAHi+qdZF/jJrR/F3L60JVLOOUhTpi
LxFFBksZPVaiVf+6R8pSMy0WtDEkzGT430ji6V4i8O/70HXIG9n9pCye8sLsOl6D
exXj/MkwwSd3J0Y58zd8ZwMrK9m/jyFrk9TlWokfIFL/eC8VFsu7qmSSRLIjMuxc
YPPisgR5+WPcus7Jf8auqcYw8eW0GPc1ugJobwucs5e/TinksMfwQrzEydmOPoWI
Pfur7MjPr5IQXROtQv+CihMigPIHvi73YzSe5zdPCw8JcuZ5vBi2pwquvzvGLtMM
Btln/SwonyQMks5WV4dOk6NOB73mCMywCir4ybp9ElJMaUGEF9nLO+h8Fg==
-----END CERTIFICATE-----
subject=CN = Cross Root
issuer=CN = Cross Root
notBefore=Aug 30 18:33:26 2021 GMT
notAfter=Aug 31 18:33:26 2121 GMT
-----BEGIN CERTIFICATE-----
MIIC+jCCAeKgAwIBAgIBATANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApDcm9z
cyBSb290MCAXDTIxMDgzMDE4MzMyNloYDzIxMjEwODMxMTgzMzI2WjAVMRMwEQYD
VQQDDApDcm9zcyBSb290MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
kpH8JGF0yRS+CCAkUBV44unl/SNTSPexgbtmJPHo8HLTLbXCU5QS2KWERUJ2YLC8
FRIQNuSipUjT8zz6xFYqU+S5eSeroVhARixxE4fEMeLCOdPAds1D/trZw2qWLZOT
DUlXUGV1besCbrF2PL3efJxzy6OM6+tTE8K49y3dJVNfGB7cZxkjDGDz08XnBNJo
2FNN/BC8WTHZU/lUgtCjgSXXY+uPpF0XYg7UBlVhOfDLV+f9fC4EmNyf4bzYZnFr
mOtdaYF3JsAz60p9Xw3kahvO0LPCp4ktXbVhwwlVv+SmTkHi3CulU2hHZ8HT2EOw
YiaePt8qSphK3U/U2AbPFwIDAQABo1MwUTAdBgNVHQ4EFgQUL16/ihJvr2w9I5k6
3jjZ13SPW20wHwYDVR0jBBgwFoAUL16/ihJvr2w9I5k63jjZ13SPW20wDwYDVR0T
AQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAUiqf8oQaPX3aW6I+dcRhsq5g
bpYF0X5jePk6UqWu86YcmpoRtGLH7e5aHGJYqrVrkOoo0q4eTL3Pm1/sB3omPRMb
ey/i7Z70wwd5yI8iz/WBmQDahYxq5wSDsUSdZDL0kSyoU2jCwXUPtuC6F1kMZBFI
uUeaFcF8oKVGuOHvZgj/FMBpT7tyjdPpDG4uo6AT04AKGhf5xO5UY2N+uqmEsXHK
HsKAEMrVhdeU5mbrfifvSkMYcYgJOX1KFP+t4U+ogqCHy1/Nfhq+WG1XN5GwhtuO
ze25NqI6ZvA2og4AoeIzvJ/+Nfl5PNtClm0IjbGvR77oOBMs71lO4GjUYj9eiw==
-----END CERTIFICATE-----
