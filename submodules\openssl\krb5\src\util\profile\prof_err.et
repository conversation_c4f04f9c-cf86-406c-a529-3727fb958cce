error_table prof

error_code	PROF_VERSION,	"Profile version 0.0"

#
# generated by prof_tree.c
#
error_code	PROF_MAGIC_NODE,	"Bad magic value in profile_node"
error_code	PROF_NO_SECTION,	"Profile section not found"
error_code	PROF_NO_RELATION,	"Profile relation not found"
error_code	PROF_ADD_NOT_SECTION, 
	"Attempt to add a relation to node which is not a section"
error_code	PROF_SECTION_WITH_VALUE, 
	"A profile section header has a non-zero value"
error_code	PROF_BAD_LINK_LIST, 	"Bad linked list in profile structures"
error_code	PROF_BAD_GROUP_LVL, 	"Bad group level in profile strctures"
error_code	PROF_BAD_PARENT_PTR, 	
	"Bad parent pointer in profile strctures"
error_code	PROF_MAGIC_ITERATOR,	"Bad magic value in profile iterator"
error_code	PROF_SET_SECTION_VALUE,	"Can't set value on section node"
error_code	PROF_EINVAL,		"Invalid argument passed to profile library"
error_code	PROF_READ_ONLY,		"Attempt to modify read-only profile"

#
# generated by prof_parse.c
#

error_code	PROF_SECTION_NOTOP, "Profile section header not at top level"
error_code	PROF_SECTION_SYNTAX, "Syntax error in profile section header"
error_code	PROF_RELATION_SYNTAX, "Syntax error in profile relation"
error_code	PROF_EXTRA_CBRACE, "Extra closing brace in profile"
error_code	PROF_MISSING_OBRACE, "Missing open brace in profile"

#
# generated by prof_init.c
# 
error_code	PROF_MAGIC_PROFILE,	"Bad magic value in profile_t"
error_code	PROF_MAGIC_SECTION,	"Bad magic value in profile_section_t"
error_code	PROF_TOPSECTION_ITER_NOSUPP,
	"Iteration through all top level section not supported"
error_code	PROF_INVALID_SECTION,	"Invalid profile_section object"
error_code	PROF_END_OF_SECTIONS,	"No more sections"
error_code	PROF_BAD_NAMESET,	"Bad nameset passed to query routine"
error_code	PROF_NO_PROFILE,	"No profile file open"

#
# generated by prof_file.c
#
error_code      PROF_MAGIC_FILE,	"Bad magic value in profile_file_t"
error_code	PROF_FAIL_OPEN,		"Couldn't open profile file"

#
# generated by prof_set.c
#
error_code	PROF_EXISTS,		"Section already exists"

#
# generated by prof_get.c
#
error_code	PROF_BAD_BOOLEAN,		"Invalid boolean value"
error_code	PROF_BAD_INTEGER,		"Invalid integer value"

#
# new error codes added at end to avoid changing values
#
error_code	PROF_MAGIC_FILE_DATA, "Bad magic value in profile_file_data_t"
error_code	PROF_FAIL_INCLUDE_FILE,
	"Included profile file could not be read"
error_code	PROF_FAIL_INCLUDE_DIR,
	"Included profile directory could not be read"
error_code	PROF_UNSUPPORTED, "Operation not supported on this profile"
error_code	PROF_MAGIC_NODE_ITERATOR, "Bad magic value in profile iterator"
error_code	PROF_MODULE, "Unexpected module declaration in profile"
error_code	PROF_MODULE_SYNTAX,
	"Invalid syntax of module declaration in profile"
error_code	PROF_MODULE_INVALID, "Invalid profile module"

end
