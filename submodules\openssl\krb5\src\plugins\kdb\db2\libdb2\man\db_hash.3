.\" Copyright (c) 1990, 1993, 1994, 1995
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in the
.\"    documentation and/or other materials provided with the distribution.
.\" 3. All advertising materials mentioning features or use of this software
.\"    must display the following acknowledgement:
.\"	This product includes software developed by the University of
.\"	California, Berkeley and its contributors.
.\" 4. Neither the name of the University nor the names of its contributors
.\"    may be used to endorse or promote products derived from this software
.\"    without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
.\" ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
.\" IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
.\" ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
.\" FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
.\" DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
.\" OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
.\" HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
.\" LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
.\" OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"	@(#)db_hash.3	8.13 (Berkeley) 8/1/95
.\"
.TH DB_HASH 3 "August 1, 1995"
.UC 7
.SH NAME
db_hash \- hash database access method
.SH DESCRIPTION
.so db.so
.GN
specific details of the hashing access method.
.PP
The hash data structure is an extensible, dynamic hashing scheme.
Backward compatible interfaces to the functions described in
.IR dbm (3),
and
.IR ndbm (3)
are provided, however these interfaces are not compatible with
previous file formats.
.SH "ACCESS METHOD SPECIFIC INFORMATION"
The hash access method specific data structure provided to
.I db_open
is typedef'd and named HASHINFO.
A HASHINFO structure has at least the following fields,
which may be initialized before calling
.IR db_open :
.TP 5
u_int bsize;
.I Bsize
defines the hash table bucket size, and is, by default, 256 bytes.
It may be preferable to increase the page size for disk-resident tables
and tables with large data items.
.TP 5
u_int cachesize;
A suggested maximum size, in bytes, of the memory cache.
This value is
.B only
advisory, and the access method will allocate more memory rather
than fail.
.TP 5
u_int ffactor;
.I Ffactor
indicates a desired density within the hash table.
It is an approximation of the number of keys allowed to accumulate in any
one bucket, determining when the hash table grows or shrinks.
The default value is 8.
.TP 5
u_int32_t (*hash)(const void *, size_t);
.I Hash
is a user defined hash function.
Since no hash function performs equally well on all possible data, the
user may find that the built-in hash function does poorly on a particular
data set.
User specified hash functions must take two arguments (a pointer to a byte
string and a length) and return a 32-bit quantity to be used as the hash
value.
.IP
If a hash function is specified,
.I hash_open
will attempt to determine if the hash function specified is the same as
the one with which the database was created, and will fail if it is not.
.TP 5
int lorder;
The byte order for integers in the stored database metadata.
The number should represent the order as an integer; for example, 
big endian order would be the number 4,321.
If
.I lorder
is 0 (no order is specified) the current host order is used.
If the  file already exists, the specified value is ignored and the
value specified when the tree was created is used.
.TP 5
u_int nelem;
.I Nelem
is an estimate of the final size of the hash table.
If not set or set too low, hash tables will expand gracefully as keys
are entered, although a slight performance degradation may be noticed.
The default value is 1.
.PP
If the file already exists (and the O_TRUNC flag is not specified), the
values specified for the parameters bsize, ffactor, lorder and nelem are
ignored and the values specified when the tree was created are used.
.SH "DB OPERATIONS"
The functions returned by
.I db_open
for the hash access method are as described in
.IR db_open (3).
.SH ERRORS
The
.I hash
access method functions may fail and set
.I errno
for any of the errors specified for the library function
.IR db_open (3).
.SH "SEE ALSO"
.IR db_btree (3),
.IR db_lock (3),
.IR db_log (3),
.IR db_mpool (3),
.IR db_open (3),
.IR db_recno (3),
.IR db_txn (3)
.sp
.IR "Dynamic Hash Tables" ,
Per-Ake Larson, Communications of the ACM, April 1988.
.sp
.IR "A New Hash Package for UNIX" ,
Margo Seltzer, USENIX Proceedings, Winter 1991.
