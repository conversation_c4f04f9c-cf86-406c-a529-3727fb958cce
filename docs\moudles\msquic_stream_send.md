[TOC]

# msquic 发送过程
本文主要讲解msquic stream send 的发送过程， 对非可靠的Datagram 发送暂时不予关注， stream send 类似tcp， 只要流不断开， 类似tcp 重传机制可以保证数据完整的发送到对方

几个概念：  
‌DPLPMTUD（Datagram Packetization Layer Path MTU Discovery）‌，一种用于数据报分组层（PL）的路径MTU发现技术，旨在避免IP分片并提高网络效率。DPLPMTUD通过发送探测包来发现网络路径上能支持的最大不分片数据报大小，并处理可能的黑洞和不一致的路径信息；  
ECN，显式拥塞通知；  
Spin bits，运行网络监控者计算链接的RTT；  
Path migration, 网络路径迁移（变更）  
NAT rebing，对端地址因为NAT等中间网络设备而变更了地址   
Push promises，PUSH_PROMISE帧，用于在发送者打算初始化流之前通知对端   
Extensions，包括http3的能力，Datagrams，丢包率计算， ACK确认延迟（用于提高吞吐率）


## ‌QUIC stream 外部发送数据的接口
‌
遵循打开stream，开始stream， 发送数据三步
 ```C++
  MsQuic->StreamOpen(Connection, QUIC_STREAM_OPEN_FLAG_NONE, ClientStreamCallback, NULL, &Stream)；
```
 ```C++
  MsQuic->StreamStart(Stream, QUIC_STREAM_START_FLAG_NONE)；
```
msquic发送的数据格式如下， Length 为数据长度， Buffer 为数据指针 
 ```C++
typedef struct QUIC_BUFFER {
    uint32_t Length;
    _Field_size_bytes_(Length)
    uint8_t* Buffer;
} QUIC_BUFFER;
 ``` 
 ```C++
 MsQuic->StreamSend(Stream, SendBuffer, 1, QUIC_SEND_FLAG_START, SendBuffer)；
 其中SendBuffer 类型为QUIC_BUFFER， 有一点要特别注意的，由于quic 发送为异步调用， 调用send 接口之后会里面返回， 发送成功之后会在streamcallback 里回调发送成功的消息， 回调会带上最后StreamSend 函数里最后一个参数， 可以在这里对SendBuffer 进行释放，或者重新放进pool
  ``` 

## ‌QUIC 调用streamsend 内部发送过程
先从connection的pool 里申请一个发送请求， 然后把发送的buffer指针放到发送请求里
```C++
SendRequest = CxPlatPoolAlloc(&Connection->Worker->SendRequestPool);
SendRequest->Next = NULL;
SendRequest->Buffers = Buffers;  //发送的buffers 指针
SendRequest->BufferCount = BufferCount; //发送的buffers 个数
SendRequest->Flags = Flags & ~QUIC_SEND_FLAGS_INTERNAL;
SendRequest->TotalLength = TotalLength; // 总长度
SendRequest->ClientContext = ClientSendContext; // 发送完成后回调的指针
 ``` 
 把sendrequset放到发送队列里， 然后计数器加1 就完成了
 ```C++
 QUIC_SEND_REQUEST** ApiSendRequestsTail = &Stream->ApiSendRequests;
while (*ApiSendRequestsTail != NULL) {
    ApiSendRequestsTail = &((*ApiSendRequestsTail)->Next);
    QueueOper = FALSE; // Not necessary if the previous send hasn't been flushed yet.
}
*ApiSendRequestsTail = SendRequest; //放到发送队列
QuicStreamAddRef(Stream, QUIC_STREAM_REF_OPERATION); //计数器加1
 ``` 
申请一个Oper， 真正的操作会放到Connection->Worker 这个work 线程上去执行streamsend操作
```C++
Oper = QuicOperationAlloc(Connection->Worker, QUIC_OPER_TYPE_API_CALL)；
Oper->API_CALL.Context->Type = QUIC_API_TYPE_STRM_SEND;
Oper->API_CALL.Context->STRM_SEND.Stream = Stream;
QuicConnQueueOper(Connection, Oper);
```

## 工作线程处理QUIC_API_TYPE_STRM_SEND 操作
工作线程会一直轮询是否有Oper 需要执行， 需要执行就会一次性把所有oper 执行完， 当检测到QUIC_API_TYPE_STRM_SEND 类型的Oper时， 会调用
```C++
QuicStreamSendFlush（ApiCtx->STRM_SEND.Stream）； 
```   
进行处理， stream 为Oper 带过来的参数，QuicStreamSendFlush 里首先会通过QuicSendSetStreamSendFlag 生成一个发送数据的Oper：QUIC_OPER_TYPE_FLUSH_SEND
```C++
        QuicSendSetStreamSendFlag(
            &Stream->Connection->Send,
            Stream,
            QUIC_STREAM_SEND_FLAG_DATA,
            !!(SendRequest->Flags & QUIC_SEND_FLAG_DELAY_SEND));
``` 
堆栈如下，
![](msquic/send_flush.png) 
然后再调用QuicSendBufferFill 函数把要发送的buffer 内容copy 到quic 发送的内部缓存， 最后回调上层通知释放buffer，堆栈如下
![](msquic/send_callback.png) 


## 工作线程处理 QUIC_OPER_TYPE_FLUSH_SEND 操作
 真正的发送操作是通过工作线程轮询Oper 队列处理QUIC_OPER_TYPE_FLUSH_SEND 完成的， 调用的函数为QuicSendFlush(&Connection->Send)；
 调用QuicPacketBuilderInitialize（&Builder, Connection, Path），里面会初始化sourceid 和发送窗的大小
```C++
 Builder->SourceCid =
    CXPLAT_CONTAINING_RECORD(
        Connection->SourceCids.Next,
        QUIC_CID_HASH_ENTRY,
        Link);

uint64_t TimeNow = CxPlatTimeUs64();
uint64_t TimeSinceLastSend;
if (Connection->Send.LastFlushTimeValid) {
    TimeSinceLastSend =
        CxPlatTimeDiff64(Connection->Send.LastFlushTime, TimeNow);
} else {
    TimeSinceLastSend = 0;
} // 获取发送时间
Builder->SendAllowance =
    QuicCongestionControlGetSendAllowance(
        &Connection->CongestionControl,
        TimeSinceLastSend,
        Connection->Send.LastFlushTimeValid);
        // 获取发送窗口大小，涉及quic 拥塞控制算法bbr，cubic
if (Builder->SendAllowance > Path->Allowance) {
    Builder->SendAllowance = Path->Allowance;
}
``` 
 初始化完成之后， 开始发送数据， 按照下面的优先顺序进行发送：    
 1. 连接控制指令（Connection wide control data）    
 2. 路径探测包 （Path MTU discovery packets）
 3. 流数据 （Stream (control and application) data）
数据类型由Send->SendFlags 进行控制， 代码如下：

```C++
if ((SendFlags & ~QUIC_CONN_SEND_FLAG_DPLPMTUD) != 0) {
    //发送连接控制指令
    CXPLAT_DBG_ASSERT(QuicSendCanSendFlagsNow(Send));
    if (!QuicPacketBuilderPrepareForControlFrames(
            &Builder,
            Send->TailLossProbeNeeded,
            SendFlags & ~QUIC_CONN_SEND_FLAG_DPLPMTUD)) {
        break;
    }
    WrotePacketFrames = QuicSendWriteFrames(Send, &Builder);
} else if ((SendFlags & QUIC_CONN_SEND_FLAG_DPLPMTUD) != 0) {
    //发送路径探测相关包
    if (!QuicPacketBuilderPrepareForPathMtuDiscovery(&Builder)) {
        break;
    }
    FlushBatchedDatagrams = TRUE;
    Send->SendFlags &= ~QUIC_CONN_SEND_FLAG_DPLPMTUD;
    if (Builder.Metadata->FrameCount < QUIC_MAX_FRAMES_PER_PACKET &&
        Builder.DatagramLength < Builder.Datagram->Length - Builder.EncryptionOverhead) {

        Builder.Datagram->Buffer[Builder.DatagramLength++] = QUIC_FRAME_PING;
        Builder.Metadata->Frames[Builder.Metadata->FrameCount++].Type = QUIC_FRAME_PING;
        WrotePacketFrames = TRUE;
    } else {
        WrotePacketFrames = FALSE;
    }
} else if (Stream != NULL ||
    (Stream = QuicSendGetNextStream(Send, &StreamPacketCount)) != NULL){
        //发送流数据
        
    // 写ack

     QUIC_PACKET_SPACE* Packets = Connection->Packets[Builder.EncryptLevel];
     uint8_t ZeroRttPacketType =
    Connection->Stats.QuicVersion == QUIC_VERSION_2 ?
        QUIC_0_RTT_PROTECTED_V2 : QUIC_0_RTT_PROTECTED_V1;
     WrotePacketFrames =
    Builder.PacketType != ZeroRttPacketType &&
    QuicAckTrackerHasPacketsToAck(&Packets->AckTracker) &&
    QuicAckTrackerAckFrameEncode(&Packets->AckTracker, &Builder);
    }

    // 写数据流
    WrotePacketFrames |= QuicStreamSendWrite(Stream, &Builder);

```
写数据的过程中会先从stream 的写请求的队列buffer 里读取数据，堆栈如下：    
![](msquic/copydata.png) 
copy 数据的时候如果数据足够多，每个包会copy 满一个mtu 单元， 一般为1000多个字节。    
最后会调用QuicPacketBuilderFinalize 完成数据的加密和发送


```C++
// !WrotePacketFrames 没有更多的数据了，所有数据都打包完了
// Builder.Metadata->FrameCount == QUIC_MAX_FRAMES_PER_PACKET 打包了QUIC_MAX_FRAMES_PER_PACKET 个frame
//Builder.Datagram->Length - Builder.DatagramLength < QUIC_MIN_PACKET_SPARE_SPACE  数据段满， 包长度接近mtu极限
if (!WrotePacketFrames ||
    Builder.Metadata->FrameCount == QUIC_MAX_FRAMES_PER_PACKET ||
    Builder.Datagram->Length - Builder.DatagramLength < QUIC_MIN_PACKET_SPARE_SPACE) {


    if (!QuicPacketBuilderFinalize(&Builder, !WrotePacketFrames || FlushBatchedDatagrams)) {
        break;
    }
}
```
调用发送udp 的堆栈如下：
![](msquic/udpsend.png) 
最终在CxPlatSocketSendInline里调用Datapath->WSASendMsg 发送udp包