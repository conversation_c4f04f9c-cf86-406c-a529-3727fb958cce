#!/bin/bash
# Selective test runner for p2psocket interface
# This script runs all individual tests in sequence

echo "========================================"
echo "P2P Socket Selective Test Runner"
echo "========================================"

# Check if we're in the correct directory
if [ ! -f "testp2psocket.cpp" ]; then
    echo "Error: testp2psocket.cpp not found in current directory"
    echo "Please run this script from the src/p2psocket directory"
    exit 1
fi

# Build the project first
echo "Building p2psocket library and tests..."
cd ../..

# Check if CMakeLists.txt exists in the root
if [ ! -f "CMakeLists.txt" ]; then
    echo "Error: CMakeLists.txt not found in project root"
    echo "Please ensure you're running from the correct directory"
    exit 1
fi

# Create build directory if it doesn't exist
mkdir -p build
cd build

# Configure and build
echo "Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    exit 1
fi

echo "Building..."
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

# Go to the bin directory where executables are built
cd bin

# Check if testp2psocket exists
if [ ! -f "testp2psocket" ]; then
    echo "Error: testp2psocket not found in bin directory"
    echo "Make sure the build completed successfully"
    exit 1
fi

echo ""
echo "========================================"
echo "Running Individual Tests"
echo "========================================"

echo ""
echo "1. Testing Basic Operations..."
./testp2psocket -test basic
BASIC_RESULT=$?
if [ $BASIC_RESULT -eq 0 ]; then
    echo "[OK] Basic operations test PASSED"
else
    echo "[FAIL] Basic operations test FAILED"
    echo "Stopping here - fix basic operations first"
    exit $BASIC_RESULT
fi

echo ""
echo "2. Testing Invalid Parameters..."
./testp2psocket -test invalid
INVALID_RESULT=$?
if [ $INVALID_RESULT -eq 0 ]; then
    echo "[OK] Invalid parameters test PASSED"
else
    echo "[FAIL] Invalid parameters test FAILED"
fi

echo ""
echo "3. Testing Socket Binding..."
./testp2psocket -test binding
BINDING_RESULT=$?
if [ $BINDING_RESULT -eq 0 ]; then
    echo "[OK] Socket binding test PASSED"
else
    echo "[FAIL] Socket binding test FAILED"
fi

echo ""
echo "4. Testing Socket Settings..."
./testp2psocket -test settings
SETTINGS_RESULT=$?
if [ $SETTINGS_RESULT -eq 0 ]; then
    echo "[OK] Socket settings test PASSED"
else
    echo "[FAIL] Socket settings test FAILED"
fi

echo ""
echo "5. Testing Polling Operations..."
./testp2psocket -test polling
POLLING_RESULT=$?
if [ $POLLING_RESULT -eq 0 ]; then
    echo "[OK] Polling operations test PASSED"
else
    echo "[FAIL] Polling operations test FAILED"
fi

echo ""
echo "========================================"
echo "Basic Tests Summary"
echo "========================================"
echo "Basic Operations: $BASIC_RESULT"
echo "Invalid Parameters: $INVALID_RESULT"
echo "Socket Binding: $BINDING_RESULT"
echo "Socket Settings: $SETTINGS_RESULT"
echo "Polling Operations: $POLLING_RESULT"

# Calculate basic tests result
BASIC_FAILURES=$((BASIC_RESULT + INVALID_RESULT + BINDING_RESULT + SETTINGS_RESULT + POLLING_RESULT))

if [ $BASIC_FAILURES -eq 0 ]; then
    echo ""
    echo "*** ALL BASIC TESTS PASSED! ***"
    echo "Proceeding to advanced tests..."
    
    echo ""
    echo "6. Testing Server-Client Connection..."
    ./testp2psocket -test connection
    CONNECTION_RESULT=$?
    if [ $CONNECTION_RESULT -eq 0 ]; then
        echo "[OK] Connection test PASSED"
    else
        echo "[FAIL] Connection test FAILED"
    fi
    
    echo ""
    echo "7. Testing Vectored I/O..."
    ./testp2psocket -test vectored
    VECTORED_RESULT=$?
    if [ $VECTORED_RESULT -eq 0 ]; then
        echo "[OK] Vectored I/O test PASSED"
    else
        echo "[FAIL] Vectored I/O test FAILED"
    fi
    
    echo ""
    echo "8. Testing Multi-Client Connections..."
    ./testp2psocket -test multiclient
    MULTICLIENT_RESULT=$?
    if [ $MULTICLIENT_RESULT -eq 0 ]; then
        echo "[OK] Multi-client test PASSED"
    else
        echo "[FAIL] Multi-client test FAILED"
    fi
    
    echo ""
    echo "9. Testing Edge Cases..."
    ./testp2psocket -test edge
    EDGE_RESULT=$?
    if [ $EDGE_RESULT -eq 0 ]; then
        echo "[OK] Edge cases test PASSED"
    else
        echo "[FAIL] Edge cases test FAILED"
    fi
    
    echo ""
    echo "========================================"
    echo "Complete Test Results Summary"
    echo "========================================"
    echo "Basic Operations: $BASIC_RESULT"
    echo "Invalid Parameters: $INVALID_RESULT"
    echo "Socket Binding: $BINDING_RESULT"
    echo "Socket Settings: $SETTINGS_RESULT"
    echo "Polling Operations: $POLLING_RESULT"
    echo "Connection Test: $CONNECTION_RESULT"
    echo "Vectored I/O: $VECTORED_RESULT"
    echo "Multi-Client: $MULTICLIENT_RESULT"
    echo "Edge Cases: $EDGE_RESULT"
    
    # Calculate total failures
    TOTAL_FAILURES=$((BASIC_RESULT + INVALID_RESULT + BINDING_RESULT + SETTINGS_RESULT + POLLING_RESULT + CONNECTION_RESULT + VECTORED_RESULT + MULTICLIENT_RESULT + EDGE_RESULT))
    
    if [ $TOTAL_FAILURES -eq 0 ]; then
        echo ""
        echo "*** ALL TESTS PASSED! ***"
        echo "The p2psocket interface is working correctly."
    else
        echo ""
        echo "*** SOME TESTS FAILED ***"
        echo "Total failed tests: $TOTAL_FAILURES"
        echo "Please check the output above for details."
    fi
    
else
    echo ""
    echo "*** SOME BASIC TESTS FAILED ***"
    echo "Please fix the basic functionality before proceeding to advanced tests."
    echo "Skipping advanced tests..."
    TOTAL_FAILURES=$BASIC_FAILURES
fi

echo ""
echo "To run a specific test manually:"
echo "  ./testp2psocket -test <testname>"
echo ""
echo "Available tests: basic, invalid, binding, settings, polling,"
echo "                 connection, vectored, multiclient, edge, all"

exit $TOTAL_FAILURES
