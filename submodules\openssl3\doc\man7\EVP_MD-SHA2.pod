=pod

=head1 NAME

EVP_MD-SHA2 - The SHA2 EVP_MD implementation

=head1 DESCRIPTION

Support for computing SHA2 digests through the B<EVP_MD> API.

=head2 Identities

This implementation includes the following varieties:

=over 4

=item *

Available with the FIPS provider as well as the default provider:

=over 4

=item SHA2-224

Known names are "SHA2-224", "SHA-224" and "SHA224".

=item SHA2-256

Known names are "SHA2-256", "SHA-256" and "SHA256".

=item SHA2-384

Known names are "SHA2-384", "SHA-384" and "SHA384".

=item SHA2-512

Known names are "SHA2-512", "SHA-512" and "SHA512".

=back

=item *

Available with the default provider:

=over 4

=item SHA2-512/224

Known names are "SHA2-512/224", "SHA-512/224" and "SHA512-224".

=item SHA2-512/256

Known names are "SHA2-512/256", "SHA-512/256" and "SHA512-256".

=back

=back

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<provider-digest(7)>, L<OSSL_PROVIDER-FIPS(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
