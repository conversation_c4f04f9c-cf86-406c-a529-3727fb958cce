# German translation of mit-krb5.
# This file is distributed under the same license as the mit-krb5 package.
# Copyright (C) 1985-2013 by the Massachusetts Institute of Technology.
# Copyright (C) of this file 2014-2016 <PERSON> <<EMAIL>>.
#
msgid ""
msgstr ""
"Project-Id-Version: mit-krb5 13.2\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2015-05-06 14:59-0400\n"
"PO-Revision-Date: 2016-04-07 08:15+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

#: ../../src/clients/kdestroy/kdestroy.c:62
#, c-format
msgid "Usage: %s [-A] [-q] [-c cache_name]\n"
msgstr "Aufruf: %s [-A] [-q] [-c Zwischenspeichername]\n"

#: ../../src/clients/kdestroy/kdestroy.c:63
#, c-format
msgid "\t-A destroy all credential caches in collection\n"
msgstr "\t-A vernichtet alle Anmeldedatenzwischenspeicher in der Sammlung.\n"

#: ../../src/clients/kdestroy/kdestroy.c:64
#, c-format
msgid "\t-q quiet mode\n"
msgstr "\t-q stiller Modus\n"

#: ../../src/clients/kdestroy/kdestroy.c:65
#: ../../src/clients/kswitch/kswitch.c:45
#, c-format
msgid "\t-c specify name of credentials cache\n"
msgstr "\t-c gibt den Namen des Zwischenspeichers für Anmeldedaten an.\n"

#: ../../src/clients/kdestroy/kdestroy.c:98
#: ../../src/clients/kinit/kinit.c:383 ../../src/clients/ksu/main.c:284
#, c-format
msgid "Only one -c option allowed\n"
msgstr "Nur eine »-c«-Option ist erlaubt.\n"

#: ../../src/clients/kdestroy/kdestroy.c:105
#: ../../src/clients/kinit/kinit.c:412 ../../src/clients/klist/klist.c:182
#, c-format
msgid "Kerberos 4 is no longer supported\n"
msgstr "Kerberos 4 wird nicht mehr unterstützt.\n"

#: ../../src/clients/kdestroy/kdestroy.c:126
#: ../../src/clients/klist/klist.c:253 ../../src/clients/ksu/main.c:131
#: ../../src/clients/ksu/main.c:137 ../../src/clients/kswitch/kswitch.c:97
#: ../../src/kadmin/ktutil/ktutil.c:52 ../../src/kdc/main.c:926
#: ../../src/slave/kprop.c:102 ../../src/slave/kpropd.c:1052
msgid "while initializing krb5"
msgstr "beim Initialisieren von Krb5"

#: ../../src/clients/kdestroy/kdestroy.c:133
msgid "while listing credential caches"
msgstr "beim Auflisten der Anmeldedatenzwischenspeicher"

#: ../../src/clients/kdestroy/kdestroy.c:140
msgid "composing ccache name"
msgstr "Ccache-Name wird zusammengesetzt."

#: ../../src/clients/kdestroy/kdestroy.c:145
#, c-format
msgid "while destroying cache %s"
msgstr "beim Zerstören des Zwischenspeichers %s"

#: ../../src/clients/kdestroy/kdestroy.c:157
#: ../../src/clients/kswitch/kswitch.c:104
#, c-format
msgid "while resolving %s"
msgstr "beim Auflösen von %s"

#: ../../src/clients/kdestroy/kdestroy.c:163
#: ../../src/clients/kinit/kinit.c:501 ../../src/clients/klist/klist.c:460
msgid "while getting default ccache"
msgstr "beim Holen des Standard-Ccaches"

#: ../../src/clients/kdestroy/kdestroy.c:170 ../../src/clients/ksu/main.c:986
msgid "while destroying cache"
msgstr "beim Zerstören des Zwischenspeichers"

#: ../../src/clients/kdestroy/kdestroy.c:173
#, c-format
msgid "Ticket cache NOT destroyed!\n"
msgstr "Ticketzwischenspeicher NICHT vernichtet!\n"

#: ../../src/clients/kdestroy/kdestroy.c:175
#, c-format
msgid "Ticket cache %cNOT%c destroyed!\n"
msgstr "Ticketzwischenspeicher %cNICHT%c vernichtet!\n"

#: ../../src/clients/kinit/kinit.c:213
#, c-format
msgid "\t-V verbose\n"
msgstr "\t-V detaillierte Ausgabe\n"

#: ../../src/clients/kinit/kinit.c:214
#, c-format
msgid "\t-l lifetime\n"
msgstr "\t-l Lebensdauer\n"

#: ../../src/clients/kinit/kinit.c:215
#, c-format
msgid "\t-s start time\n"
msgstr "\t-s Startzeit\n"

#: ../../src/clients/kinit/kinit.c:216
#, c-format
msgid "\t-r renewable lifetime\n"
msgstr "\t-r verlängerbare Lebensdauer\n"

#: ../../src/clients/kinit/kinit.c:217
#, c-format
msgid "\t-f forwardable\n"
msgstr "\t-f weiterleitbar\n"

#: ../../src/clients/kinit/kinit.c:218
#, c-format
msgid "\t-F not forwardable\n"
msgstr "\t-F nicht weiterleitbar\n"

#: ../../src/clients/kinit/kinit.c:219
#, c-format
msgid "\t-p proxiable\n"
msgstr "\t-p Proxy nutzbar\n"

#: ../../src/clients/kinit/kinit.c:220
#, c-format
msgid "\t-P not proxiable\n"
msgstr "\t-P Proxy nicht nutzbar\n"

#: ../../src/clients/kinit/kinit.c:221
#, c-format
msgid "\t-n anonymous\n"
msgstr "\t-n anonym\n"

#: ../../src/clients/kinit/kinit.c:222
#, c-format
msgid "\t-a include addresses\n"
msgstr "\t-a bezieht Adressen ein.\n"

#: ../../src/clients/kinit/kinit.c:223
#, c-format
msgid "\t-A do not include addresses\n"
msgstr "\t-a bezieht Adressen nicht ein.\n"

#: ../../src/clients/kinit/kinit.c:224
#, c-format
msgid "\t-v validate\n"
msgstr "\t-v überprüft\n"

#: ../../src/clients/kinit/kinit.c:225
#, c-format
msgid "\t-R renew\n"
msgstr "\t-R erneuert\n"

#: ../../src/clients/kinit/kinit.c:226
#, c-format
msgid "\t-C canonicalize\n"
msgstr "\t-C bringt in Normalform\n"

#: ../../src/clients/kinit/kinit.c:227
#, c-format
msgid "\t-E client is enterprise principal name\n"
msgstr "\t-E Client ist der Principal-Name des Unternehmens\n"

#: ../../src/clients/kinit/kinit.c:228
#, c-format
msgid "\t-k use keytab\n"
msgstr "\t-k verwendet Schlüsseltabelle\n"

#: ../../src/clients/kinit/kinit.c:229
#, c-format
msgid "\t-i use default client keytab (with -k)\n"
msgstr "\t-i verwendet die Standardschlüsseltabelle des Clients (mit -k).\n"

#: ../../src/clients/kinit/kinit.c:230
#, c-format
msgid "\t-t filename of keytab to use\n"
msgstr "\t-t Dateiname der zu verwendenden Schlüsseltabelle\n"

#: ../../src/clients/kinit/kinit.c:231
#, c-format
msgid "\t-c Kerberos 5 cache name\n"
msgstr "\t-c Kerberos-5-Zwischenspeichername\n"

#: ../../src/clients/kinit/kinit.c:232
#, c-format
msgid "\t-S service\n"
msgstr "\t-S Dienst\n"

#: ../../src/clients/kinit/kinit.c:233
#, c-format
msgid "\t-T armor credential cache\n"
msgstr "\t-T gehärteter Anmeldedatenzwischenspeicher\n"

#: ../../src/clients/kinit/kinit.c:234
#, c-format
msgid "\t-X <attribute>[=<value>]\n"
msgstr "\t-X <Attribut>[=<Wert>]\n"

#: ../../src/clients/kinit/kinit.c:301 ../../src/clients/kinit/kinit.c:309
#, c-format
msgid "Bad lifetime value %s\n"
msgstr "falscher Wert für die Lebensdauer %s\n"

#: ../../src/clients/kinit/kinit.c:343
#, c-format
msgid "Bad start time value %s\n"
msgstr "falscher Wert für die Startzeit %s\n"

#: ../../src/clients/kinit/kinit.c:362
#, c-format
msgid "Only one -t option allowed.\n"
msgstr "Nur eine -t-Option ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:370
#, c-format
msgid "Only one armor_ccache\n"
msgstr "nur ein gehärteter Ccache\n"

#: ../../src/clients/kinit/kinit.c:391
#, c-format
msgid "Only one -I option allowed\n"
msgstr "Nur eine -I-Option ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:401
msgid "while adding preauth option"
msgstr "beim Hinzufügen der Option »preauth«"

#: ../../src/clients/kinit/kinit.c:425
#, c-format
msgid "Only one of -f and -F allowed\n"
msgstr "Nur eine der Optionen -f und -F ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:430
#, c-format
msgid "Only one of -p and -P allowed\n"
msgstr "Nur eine der Optionen -p und -P ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:435
#, c-format
msgid "Only one of -a and -A allowed\n"
msgstr "Nur eine der Optionen -a und -A ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:440
#, c-format
msgid "Only one of -t and -i allowed\n"
msgstr "Nur eine der Optionen -t und-i ist erlaubt.\n"

#: ../../src/clients/kinit/kinit.c:447
#, c-format
msgid "keytab specified, forcing -k\n"
msgstr "Schlüsseltabelle angegeben, -k wird erzwungen\n"

#: ../../src/clients/kinit/kinit.c:451 ../../src/clients/klist/klist.c:221
#, c-format
msgid "Extra arguments (starting with \"%s\").\n"
msgstr "zusätzliche Argumente (beginnend mit »%s«)\n"

#: ../../src/clients/kinit/kinit.c:480
msgid "while initializing Kerberos 5 library"
msgstr "beim Initialisieren der Kerberos-5-Bibliothek"

#: ../../src/clients/kinit/kinit.c:488 ../../src/clients/kinit/kinit.c:644
#, c-format
msgid "resolving ccache %s"
msgstr "Ccache %s wird ermittelt"

#: ../../src/clients/kinit/kinit.c:493
#, c-format
msgid "Using specified cache: %s\n"
msgstr "Angegebener Zwischenspeicher wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:515 ../../src/clients/kinit/kinit.c:595
#: ../../src/clients/kpasswd/kpasswd.c:28 ../../src/clients/ksu/main.c:238
#, c-format
msgid "when parsing name %s"
msgstr "wenn der Name %s ausgewertet wird"

#: ../../src/clients/kinit/kinit.c:523 ../../src/kadmin/dbutil/kdb5_util.c:307
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:391
#: ../../src/slave/kprop.c:203
msgid "while getting default realm"
msgstr "beim Holen des Standard-Realms"

#: ../../src/clients/kinit/kinit.c:535
msgid "while building principal"
msgstr "beim Erstellen des Principals"

#: ../../src/clients/kinit/kinit.c:543
msgid "When resolving the default client keytab"
msgstr "beim Auflösen der Standardschlüsseltabelle des Clients"

#: ../../src/clients/kinit/kinit.c:550
msgid "When determining client principal name from keytab"
msgstr "beim Bestimmen des Dienst-Principal-Namens anhand der Schlüsseltabelle"

#: ../../src/clients/kinit/kinit.c:559
msgid "when creating default server principal name"
msgstr "wenn der Standard-Principal-Name des Servers erstellt wird"

#: ../../src/clients/kinit/kinit.c:566
#, c-format
msgid "(principal %s)"
msgstr "(Principal %s)"

#: ../../src/clients/kinit/kinit.c:569
msgid "for local services"
msgstr "für lokale Dienste"

#: ../../src/clients/kinit/kinit.c:590 ../../src/clients/kpasswd/kpasswd.c:42
#, c-format
msgid "Unable to identify user\n"
msgstr "Benutzer kann nicht identifiziert werden\n"

#: ../../src/clients/kinit/kinit.c:605 ../../src/clients/kswitch/kswitch.c:116
#, c-format
msgid "while searching for ccache for %s"
msgstr "beim Suchen nach Ccache für %s"

#: ../../src/clients/kinit/kinit.c:611
#, c-format
msgid "Using existing cache: %s\n"
msgstr "Existierender Zwischenspeicher wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:620
msgid "while generating new ccache"
msgstr "beim Erstellen von neuem Ccache"

#: ../../src/clients/kinit/kinit.c:624
#, c-format
msgid "Using new cache: %s\n"
msgstr "Neuer Zwischenspeicher wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:636
#, c-format
msgid "Using default cache: %s\n"
msgstr "Standardzwischenspeicher wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:649
#, c-format
msgid "Using specified input cache: %s\n"
msgstr "Angegebener Eingabezwischenspeicher wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:657 ../../src/clients/ksu/krb_auth_su.c:160
msgid "when unparsing name"
msgstr "beim Rückgängigmachen der Auswertung des Namens"

#: ../../src/clients/kinit/kinit.c:661
#, c-format
msgid "Using principal: %s\n"
msgstr "verwendeter Principal: %s\n"

#: ../../src/clients/kinit/kinit.c:752
msgid "getting local addresses"
msgstr "Lokale Adressen werden geholt."

#: ../../src/clients/kinit/kinit.c:771
#, c-format
msgid "while setting up KDB keytab for realm %s"
msgstr "beim Einrichten der KDB-Schlüsseltabelle für Realm %s"

#: ../../src/clients/kinit/kinit.c:780 ../../src/clients/kvno/kvno.c:201
#, c-format
msgid "resolving keytab %s"
msgstr "Schlüsseltabelle wird ermittelt: %s"

#: ../../src/clients/kinit/kinit.c:785
#, c-format
msgid "Using keytab: %s\n"
msgstr "Schlüsseltabelle wird verwendet: %s\n"

#: ../../src/clients/kinit/kinit.c:789
msgid "resolving default client keytab"
msgstr "Standardschlüsseltabelle des Clients wird ermittelt."

#: ../../src/clients/kinit/kinit.c:799
#, c-format
msgid "while setting '%s'='%s'"
msgstr "beim Setzen von »%s«=»%s«"

#: ../../src/clients/kinit/kinit.c:804
#, c-format
msgid "PA Option %s = %s\n"
msgstr "PA-Option %s = %s\n"

#: ../../src/clients/kinit/kinit.c:849
msgid "getting initial credentials"
msgstr "Anfängliche Anmeldedaten werden geholt."

#: ../../src/clients/kinit/kinit.c:852
msgid "validating credentials"
msgstr "Anmeldedaten werden geprüft."

#: ../../src/clients/kinit/kinit.c:855
msgid "renewing credentials"
msgstr "Anmeldedaten werden erneuert."

#: ../../src/clients/kinit/kinit.c:860
#, c-format
msgid "%s: Password incorrect while %s\n"
msgstr "%s: Passwort bei %s falsch\n"

#: ../../src/clients/kinit/kinit.c:863
#, c-format
msgid "while %s"
msgstr "bei %s"

#: ../../src/clients/kinit/kinit.c:871 ../../src/slave/kprop.c:224
#, c-format
msgid "when initializing cache %s"
msgstr "beim Initialisieren des Zwischenspeichers %s"

#: ../../src/clients/kinit/kinit.c:876
#, c-format
msgid "Initialized cache\n"
msgstr "initialisierter Zwischenspeicher\n"

#: ../../src/clients/kinit/kinit.c:880
msgid "while storing credentials"
msgstr "beim Speichern der Anmeldedaten"

#: ../../src/clients/kinit/kinit.c:884
#, c-format
msgid "Stored credentials\n"
msgstr "gespeicherte Anmeldedaten\n"

#: ../../src/clients/kinit/kinit.c:891
msgid "while switching to new ccache"
msgstr "beim Wechsel zum neuen Ccache"

#: ../../src/clients/kinit/kinit.c:946
#, c-format
msgid "Authenticated to Kerberos v5\n"
msgstr "Authentifiziert für Kerberos v5\n"

#: ../../src/clients/klist/klist.c:91
#, c-format
msgid ""
"Usage: %s [-e] [-V] [[-c] [-l] [-A] [-d] [-f] [-s] [-a [-n]]] [-k [-t] [-K]] "
"[name]\n"
msgstr ""
"Aufruf: %s [-e] [-V] [[-c] [-l] [-A] [-d] [-f] [-s] [-a [-n]]] [-k [-t] [-"
"K]] [Name]\n"

#: ../../src/clients/klist/klist.c:93
#, c-format
msgid "\t-c specifies credentials cache\n"
msgstr "\t-c gibt den Anmeldedatenzwischenspeicher an\n"

#: ../../src/clients/klist/klist.c:94
#, c-format
msgid "\t-k specifies keytab\n"
msgstr "\t-k gibt die Schlüsseltabelle an.\n"

#: ../../src/clients/klist/klist.c:95
#, c-format
msgid "\t   (Default is credentials cache)\n"
msgstr "\t   (Voreinstellung ist Anmeldedatenzwischenspeicher)\n"

#: ../../src/clients/klist/klist.c:96
#, c-format
msgid "\t-i uses default client keytab if no name given\n"
msgstr ""
"\t-i verwendet die Standardschlüsseltabelle des Clients, falls kein Name "
"angegeben wurde.\n"

#: ../../src/clients/klist/klist.c:97
#, c-format
msgid "\t-l lists credential caches in collection\n"
msgstr "\t-l listet gesammelte Anmeldedatenzwischenspeicher auf.\n"

#: ../../src/clients/klist/klist.c:98
#, c-format
msgid "\t-A shows content of all credential caches\n"
msgstr "\t-A zeigt den Inhalt aller Anmeldedatenzwischenspeicher an.\n"

#: ../../src/clients/klist/klist.c:99
#, c-format
msgid "\t-e shows the encryption type\n"
msgstr "\t-e zeigt den Verschlüsselungstyp.\n"

#: ../../src/clients/klist/klist.c:100
#, c-format
msgid "\t-V shows the Kerberos version and exits\n"
msgstr "\t-V zeigt die Kerberos-Version und wird beendet.\n"

#: ../../src/clients/klist/klist.c:101
#, c-format
msgid "\toptions for credential caches:\n"
msgstr "\tOptionen für Anmeldedatenzwischenspeicher:\n"

#: ../../src/clients/klist/klist.c:102
#, c-format
msgid "\t\t-d shows the submitted authorization data types\n"
msgstr "\t\t-d zeigt die übertragenen Autorisierungsdatentypen.\n"

#: ../../src/clients/klist/klist.c:104
#, c-format
msgid "\t\t-f shows credentials flags\n"
msgstr "t\t-f zeigt die Anmeldedatenschalter.\n"

#: ../../src/clients/klist/klist.c:105
#, c-format
msgid "\t\t-s sets exit status based on valid tgt existence\n"
msgstr ""
"\t\t-s setzt den Exit-Status auf Basis der Existenz eines gültigen TGTs.\n"

#: ../../src/clients/klist/klist.c:107
#, c-format
msgid "\t\t-a displays the address list\n"
msgstr "\t\t-a zeigt die Adressliste.\n"

#: ../../src/clients/klist/klist.c:108
#, c-format
msgid "\t\t\t-n do not reverse-resolve\n"
msgstr "\t\t\t-n löst nicht rückwärts auf.\n"

#: ../../src/clients/klist/klist.c:109
#, c-format
msgid "\toptions for keytabs:\n"
msgstr "\tOptionen für Schlüsseltabellen:\n"

#: ../../src/clients/klist/klist.c:110
#, c-format
msgid "\t\t-t shows keytab entry timestamps\n"
msgstr "\t\t-t zeigt die Zeitstempel der Schlüsseltabelleneinträge.\n"

#: ../../src/clients/klist/klist.c:111
#, c-format
msgid "\t\t-K shows keytab entry keys\n"
msgstr "\t\t-K zeigt die Schlüssel der Schlüsseltabelleneinträge.\n"

#: ../../src/clients/klist/klist.c:230
#, c-format
msgid "%s version %s\n"
msgstr "%s Version %s\n"

#: ../../src/clients/klist/klist.c:282
msgid "while getting default client keytab"
msgstr "beim Holen der Standardschlüsseltabelle des Clients"

#: ../../src/clients/klist/klist.c:287
msgid "while getting default keytab"
msgstr "beim Holen der Standardschlüsseltabelle"

#: ../../src/clients/klist/klist.c:292 ../../src/kadmin/cli/keytab.c:108
#, c-format
msgid "while resolving keytab %s"
msgstr "beim Ermitteln der Schlüsseltabelle %s"

#: ../../src/clients/klist/klist.c:298 ../../src/kadmin/cli/keytab.c:92
msgid "while getting keytab name"
msgstr "beim Holen des Schlüsseltabellennamens"

#: ../../src/clients/klist/klist.c:305 ../../src/kadmin/cli/keytab.c:399
msgid "while starting keytab scan"
msgstr "beim Start des Schlüsseltabellen-Scans"

#: ../../src/clients/klist/klist.c:326 ../../src/clients/klist/klist.c:500
#: ../../src/clients/ksu/ccache.c:465 ../../src/kadmin/dbutil/dump.c:550
msgid "while unparsing principal name"
msgstr "beim Rückgängigmachen des Auswertens des Principal-Namens"

#: ../../src/clients/klist/klist.c:350 ../../src/kadmin/cli/keytab.c:443
msgid "while scanning keytab"
msgstr "beim Scannen der Schlüsseltabelle"

#: ../../src/clients/klist/klist.c:354 ../../src/kadmin/cli/keytab.c:448
msgid "while ending keytab scan"
msgstr "beim Beenden des Schlüsseltabellen-Scans"

#: ../../src/clients/klist/klist.c:371 ../../src/clients/klist/klist.c:434
msgid "while listing ccache collection"
msgstr "beim Aufführen der Ccache-Sammlung"

#: ../../src/clients/klist/klist.c:411
msgid "(Expired)"
msgstr "(abgelaufen)"

#: ../../src/clients/klist/klist.c:466
#, c-format
msgid "while resolving ccache %s"
msgstr "beim Ermitteln des Ccaches %s"

#: ../../src/clients/klist/klist.c:504
#, c-format
msgid ""
"Ticket cache: %s:%s\n"
"Default principal: %s\n"
"\n"
msgstr ""
"Ticketzwischenspeicher: %s:%s\n"
"Standard-Principal: %s\n"
"\n"

#: ../../src/clients/klist/klist.c:518
msgid "while starting to retrieve tickets"
msgstr "während das Abfragen der Tickets beginnt"

#: ../../src/clients/klist/klist.c:539
msgid "while finishing ticket retrieval"
msgstr "während das Abfragem der Tickets endet"

#: ../../src/clients/klist/klist.c:545
msgid "while closing ccache"
msgstr "beim Schließen des Ccaches"

#: ../../src/clients/klist/klist.c:555
msgid "while retrieving a ticket"
msgstr "beim Abfragen eines Tickets"

#: ../../src/clients/klist/klist.c:667 ../../src/clients/ksu/ccache.c:450
#: ../../src/slave/kpropd.c:1225 ../../src/slave/kpropd.c:1285
msgid "while unparsing client name"
msgstr "beim Rückgängigmachen des Auswertens des Client-Namens"

#: ../../src/clients/klist/klist.c:672 ../../src/clients/ksu/ccache.c:455
#: ../../src/slave/kprop.c:240
msgid "while unparsing server name"
msgstr "beim Rückgängigmachen des Auswertens des Server-Namens"

#: ../../src/clients/klist/klist.c:701 ../../src/clients/ksu/ccache.c:480
#, c-format
msgid "\tfor client %s"
msgstr "\tfür Client %s"

#: ../../src/clients/klist/klist.c:713 ../../src/clients/ksu/ccache.c:489
msgid "renew until "
msgstr "erneuern bis "

#: ../../src/clients/klist/klist.c:730 ../../src/clients/ksu/ccache.c:499
#, c-format
msgid "Flags: %s"
msgstr "Schalter: %s"

#: ../../src/clients/klist/klist.c:749
#, c-format
msgid "Etype (skey, tkt): %s, "
msgstr "Etype (Skey, TKT): %s, "

#: ../../src/clients/klist/klist.c:766
#, c-format
msgid "AD types: "
msgstr "AD-Typen"

#: ../../src/clients/klist/klist.c:783
#, c-format
msgid "\tAddresses: (none)\n"
msgstr "\tAdressen: (keine)\n"

#: ../../src/clients/klist/klist.c:785
#, c-format
msgid "\tAddresses: "
msgstr "\tAdressen: "

#: ../../src/clients/klist/klist.c:818
#, c-format
msgid "broken address (type %d length %d)"
msgstr "kaputte Adresse (Typ %d Länge %d)"

#: ../../src/clients/klist/klist.c:838
#, c-format
msgid "unknown addrtype %d"
msgstr "unbekannter »addrtype« %d"

#: ../../src/clients/klist/klist.c:847
#, c-format
msgid "unprintable address (type %d, error %d %s)"
msgstr "nicht druckbare Adresse (Typ %d Fehler %d %s)"

#: ../../src/clients/kpasswd/kpasswd.c:12 ../../src/lib/krb5/krb/gic_pwd.c:396
msgid "Enter new password"
msgstr "Geben Sie ein neues Passwort ein."

#: ../../src/clients/kpasswd/kpasswd.c:13 ../../src/lib/krb5/krb/gic_pwd.c:404
msgid "Enter it again"
msgstr "Geben Sie es erneut ein."

#: ../../src/clients/kpasswd/kpasswd.c:33
#, c-format
msgid "Unable to identify user from password file\n"
msgstr ""
"Der Benutzer kann nicht anhand der Passwortdatei identifiziert werden.\n"

#: ../../src/clients/kpasswd/kpasswd.c:65
#, c-format
msgid "usage: %s [principal]\n"
msgstr "Aufruf: %s [Principal]\n"

#: ../../src/clients/kpasswd/kpasswd.c:73
msgid "initializing kerberos library"
msgstr "Kerberos-Bibliothek wird initialisiert."

#: ../../src/clients/kpasswd/kpasswd.c:77
msgid "allocating krb5_get_init_creds_opt"
msgstr "krb5_get_init_creds_opt wird reserviert."

#: ../../src/clients/kpasswd/kpasswd.c:92
msgid "opening default ccache"
msgstr "Standard-Ccache wird geöffnet."

#: ../../src/clients/kpasswd/kpasswd.c:97
msgid "getting principal from ccache"
msgstr "Principal wird vom Ccache geholt."

#: ../../src/clients/kpasswd/kpasswd.c:104
msgid "while setting FAST ccache"
msgstr "beim Setzen des FAST-Ccaches"

#: ../../src/clients/kpasswd/kpasswd.c:111
msgid "closing ccache"
msgstr "Ccache wird geschlossen."

#: ../../src/clients/kpasswd/kpasswd.c:118
msgid "parsing client name"
msgstr "Client-Name wird ausgewertet."

#: ../../src/clients/kpasswd/kpasswd.c:135
msgid "Password incorrect while getting initial ticket"
msgstr "Passwort beim Holen des anfänglichen Tickets falsch"

#: ../../src/clients/kpasswd/kpasswd.c:137
msgid "getting initial ticket"
msgstr "Anfängliches Ticket wird geholt."

#: ../../src/clients/kpasswd/kpasswd.c:144
msgid "while reading password"
msgstr "beim Lesen des Passworts"

#: ../../src/clients/kpasswd/kpasswd.c:152
msgid "changing password"
msgstr "Passwort wird geändert."

#: ../../src/clients/kpasswd/kpasswd.c:174
#: ../lib/kadm5/chpass_util_strings.c:30
#, c-format
msgid "Password changed.\n"
msgstr "Passwort geändert\n"

#: ../../src/clients/ksu/authorization.c:369
#, c-format
msgid ""
"Error: bad entry - %s in %s file, must be either full path or just the cmd "
"name\n"
msgstr ""
"Fehler: falscher Eintrag – %s in Datei %s muss entweder ein vollständiger "
"Pfad oder nur ein Befehlsname sein.\n"

#: ../../src/clients/ksu/authorization.c:377
#, c-format
msgid ""
"Error: bad entry - %s in %s file, since %s is just the cmd name, CMD_PATH "
"must be defined \n"
msgstr ""
"Fehler: falscher Eintrag – %s in Datei %s. Da %s nur ein Befehlsname ist, "
"muss CMD_PATH definiert sein.\n"

#: ../../src/clients/ksu/authorization.c:392
#, c-format
msgid "Error: bad entry - %s in %s file, CMD_PATH contains no paths \n"
msgstr ""
"Fehler: falscher Eintrag – %s in Datei %s. CMD_PATH enthält keine Pfade.\n"

#: ../../src/clients/ksu/authorization.c:401
#, c-format
msgid "Error: bad path %s in CMD_PATH for %s must start with '/' \n"
msgstr "Fehler: falscher Pfad %s in CMD_PATH für %s muss mit »/« beginnen\n"

#: ../../src/clients/ksu/authorization.c:517
msgid "Error: not found -> "
msgstr "Fehler: nicht gefunden -> "

#: ../../src/clients/ksu/authorization.c:723
#, c-format
msgid "home directory name `%s' too long, can't search for .k5login\n"
msgstr ""
"Name des Home-Verzeichnisses »%s« ist zu lang, Suche nach .k5login nicht "
"möglich\n"

#: ../../src/clients/ksu/ccache.c:368
#, c-format
msgid "home directory path for %s too long\n"
msgstr "Home-Verzeichnispfad für %s zu lang\n"

#: ../../src/clients/ksu/ccache.c:461
msgid "while retrieving principal name"
msgstr "beim Abfragen des Principal-Namens"

#: ../../src/clients/ksu/krb_auth_su.c:57
#: ../../src/clients/ksu/krb_auth_su.c:62 ../../src/slave/kprop.c:247
msgid "while copying client principal"
msgstr "beim Kopieren des Client-Principals"

#: ../../src/clients/ksu/krb_auth_su.c:69
msgid "while creating tgt for local realm"
msgstr "beim Erstellen des TGTs für lokalen Realm"

#: ../../src/clients/ksu/krb_auth_su.c:84
msgid "while retrieving creds from cache"
msgstr "beim Abfragen der Anmeldedaten aus dem Zwischenspeicher"

#: ../../src/clients/ksu/krb_auth_su.c:95
msgid "while switching to target uid"
msgstr "beim Umschalten auf die Ziel-UID"

#: ../../src/clients/ksu/krb_auth_su.c:100
#, c-format
msgid ""
"WARNING: Your password may be exposed if you enter it here and are logged \n"
msgstr ""
"WARNUNG: Ihr Passwort könnte offengelegt werden, falls Sie es hier eingeben "
"und\n"

#: ../../src/clients/ksu/krb_auth_su.c:102
#, c-format
msgid "         in remotely using an unsecure (non-encrypted) channel. \n"
msgstr ""
"         in der Ferne mittels eines unsicheren (unverschlüsselten) Kanals\n"
"         angemeldet sind.\n"

#: ../../src/clients/ksu/krb_auth_su.c:114 ../../src/clients/ksu/main.c:464
msgid "while reclaiming root uid"
msgstr "beim erneuten Beanspruchen der Root-UID"

#: ../../src/clients/ksu/krb_auth_su.c:121
#, c-format
msgid "does not have any appropriate tickets in the cache.\n"
msgstr "hat keine geeigneten Tickets im Zwischenspeicher.\n"

#: ../../src/clients/ksu/krb_auth_su.c:133
msgid "while verifying ticket for server"
msgstr "beim Prüfen des Tickets für Server"

#: ../../src/clients/ksu/krb_auth_su.c:167
msgid "while getting time of day"
msgstr "beim Holen der Tageszeit"

#: ../../src/clients/ksu/krb_auth_su.c:171
#, c-format
msgid "Kerberos password for %s: "
msgstr "Kerberos-Passwort für %s: "

#: ../../src/clients/ksu/krb_auth_su.c:175
#, c-format
msgid "principal name %s too long for internal buffer space\n"
msgstr "Principal-Name %s für den internen Pufferbereich zu groß\n"

#: ../../src/clients/ksu/krb_auth_su.c:184
#, c-format
msgid "while reading password for '%s'\n"
msgstr "beim Lesen des Passworts für »%s«\n"

#: ../../src/clients/ksu/krb_auth_su.c:191
#, c-format
msgid "No password given\n"
msgstr "kein Passwort angegeben\n"

#: ../../src/clients/ksu/krb_auth_su.c:204
#, c-format
msgid "%s: Password incorrect\n"
msgstr "%s: Passwort falsch\n"

#: ../../src/clients/ksu/krb_auth_su.c:206
msgid "while getting initial credentials"
msgstr "beim Holen der Anfangsanmeldedaten"

#: ../../src/clients/ksu/krb_auth_su.c:226
#: ../../src/clients/ksu/krb_auth_su.c:240
#, c-format
msgid " %s while unparsing name\n"
msgstr "%s beim Rückgängigmachen der Namensauswertung\n"

#: ../../src/clients/ksu/main.c:68
#, c-format
msgid ""
"Usage: %s [target user] [-n principal] [-c source cachename] [-k] [-D] [-r "
"time] [-pf] [-l lifetime] [-zZ] [-q] [-e command [args... ] ] [-a "
"[args... ] ]\n"
msgstr ""
"Aufruf: %s [Zielbenutzer] [-n Principal] [-c Quellenzwischenspeichername] [-"
"k] [-D] [-r Zeit] [-pf] [-l Lebensdauer] [-zZ] [-q] [-e Befehl [Argumente "
"…] ] [-a [Argumente …] ]\n"

#: ../../src/clients/ksu/main.c:147
msgid ""
"program name too long - quitting to avoid triggering system logging bugs"
msgstr ""
"Programmname zu lang – wird beendet, um das Auslösen von "
"Systemprotokollierungsfehlern zu vermeiden"

#: ../../src/clients/ksu/main.c:173
msgid "while allocating memory"
msgstr "bei Reservieren von Speicher"

#: ../../src/clients/ksu/main.c:186
msgid "while setting euid to source user"
msgstr "beim Setzen der EUID auf dem Quellbenutzer"

#: ../../src/clients/ksu/main.c:196 ../../src/clients/ksu/main.c:231
#, c-format
msgid "Bad lifetime value (%s hours?)\n"
msgstr "falscher Wert für Lebensdauer (%s Stunden?)\n"

#: ../../src/clients/ksu/main.c:208 ../../src/clients/ksu/main.c:292
msgid "when gathering parameters"
msgstr "beim Zusammenstellen der Parameter"

#: ../../src/clients/ksu/main.c:251
#, c-format
msgid "-z option is mutually exclusive with -Z.\n"
msgstr "Die Optionen -z und -Z schließen sich gegenseitig aus.\n"

#: ../../src/clients/ksu/main.c:259
#, c-format
msgid "-Z option is mutually exclusive with -z.\n"
msgstr "Die Optionen -Z und -z schließen sich gegenseitig aus.\n"

#: ../../src/clients/ksu/main.c:272
#, c-format
msgid "while looking for credentials cache %s"
msgstr "beim Suchen nach dem Anmeldedatenzwischenspeicher %s"

#: ../../src/clients/ksu/main.c:278
#, c-format
msgid "malformed credential cache name %s\n"
msgstr "falsch gebildeter Anmeldedatenzwischenspeichername %s\n"

# ksu ist eine Kerberos-Variante von su
#: ../../src/clients/ksu/main.c:336
#, c-format
msgid "ksu: who are you?\n"
msgstr "ksu: Wer sind Sie?\n"

#: ../../src/clients/ksu/main.c:340
#, c-format
msgid "Your uid doesn't match your passwd entry?!\n"
msgstr "Ihre UID passt nicht zu Ihrem Passworteintrag.\n"

#: ../../src/clients/ksu/main.c:355
#, c-format
msgid "ksu: unknown login %s\n"
msgstr "ksu: unbekannter Anmeldename %s\n"

#: ../../src/clients/ksu/main.c:375
msgid "while getting source cache"
msgstr "beim Holen des Quellenzwischenspeichers"

#: ../../src/clients/ksu/main.c:381 ../../src/clients/kvno/kvno.c:194
msgid "while opening ccache"
msgstr "beim Öffnen des Ccaches"

#: ../../src/clients/ksu/main.c:389
msgid "while selecting the best principal"
msgstr "beim Auswählen des besten Principals"

#: ../../src/clients/ksu/main.c:397
msgid "while returning to source uid after finding best principal"
msgstr ""
"bei der Rückkehr zur Quell-UID, nachdem der beste Principal gefunden wurde"

#: ../../src/clients/ksu/main.c:417
#, c-format
msgid "account %s: authorization failed\n"
msgstr "Konto %s: Autorisierung fehlgeschlagen\n"

#: ../../src/clients/ksu/main.c:442
msgid "while parsing temporary name"
msgstr "beim Auswertens des temporären Namens"

#: ../../src/clients/ksu/main.c:447
msgid "while creating temporary cache"
msgstr "bei Erstellen des temporären Zwischenspeichers"

#: ../../src/clients/ksu/main.c:453 ../../src/clients/ksu/main.c:693
#, c-format
msgid "while copying cache %s to %s"
msgstr "beim Kopieren des Zwischenspeichers %s nach %s"

#: ../../src/clients/ksu/main.c:471
#, c-format
msgid ""
"WARNING: Your password may be exposed if you enter it here and are logged\n"
msgstr ""
"WARNUNG: Ihr Passwort könnte offengelegt werden, falls Sie es hier eingeben "
"und\n"

#: ../../src/clients/ksu/main.c:473
#, c-format
msgid "         in remotely using an unsecure (non-encrypted) channel.\n"
msgstr ""
"         in der Ferne über einen unsicheren (unverschlüsselten) Kanal "
"angemeldet\n"
"sind.\n"

#: ../../src/clients/ksu/main.c:479
#, c-format
msgid "Goodbye\n"
msgstr "Auf Wiedersehen\n"

#: ../../src/clients/ksu/main.c:483
#, c-format
msgid "Could not get a tgt for "
msgstr "Es konnte kein TGT geholt werden für "

#: ../../src/clients/ksu/main.c:505
#, c-format
msgid "Authentication failed.\n"
msgstr "Authentifizierung fehlgeschlagen.\n"

#: ../../src/clients/ksu/main.c:513
msgid "When unparsing name"
msgstr "beim Rückgängigmachen der Namensauswertung"

#: ../../src/clients/ksu/main.c:517
#, c-format
msgid "Authenticated %s\n"
msgstr "Authentifiziert %s\n"

#: ../../src/clients/ksu/main.c:524
msgid "while switching to target for authorization check"
msgstr "beim Wechsel des Ziels der Autorisierungsprüfung"

#: ../../src/clients/ksu/main.c:531
msgid "while checking authorization"
msgstr "beim Prüfen der Autorisierung"

#: ../../src/clients/ksu/main.c:537
msgid "while switching back from target after authorization check"
msgstr "beim Zurückwechsel vom Ziel nach der Autorisierungsprüfung"

#: ../../src/clients/ksu/main.c:544
#, c-format
msgid "Account %s: authorization for %s for execution of\n"
msgstr "Konto %s: Autorisierung für %s zum Ausführen von\n"

#: ../../src/clients/ksu/main.c:546
#, c-format
msgid "               %s successful\n"
msgstr "               %s erfolgreich\n"

#: ../../src/clients/ksu/main.c:552
#, c-format
msgid "Account %s: authorization for %s successful\n"
msgstr "Konto %s: Autorisierung für %s erfolgreich\n"

#: ../../src/clients/ksu/main.c:564
#, c-format
msgid "Account %s: authorization for %s for execution of %s failed\n"
msgstr "Konto %s: Autorisierung für %s zum Ausführen von %s fehlgeschlagen\n"

#: ../../src/clients/ksu/main.c:572
#, c-format
msgid "Account %s: authorization of %s failed\n"
msgstr "Konto %s: Autorisierung von %s fehlgeschlagen\n"

#: ../../src/clients/ksu/main.c:587
msgid "while calling cc_filter"
msgstr "beim Aufruf von »cc_filter«"

#: ../../src/clients/ksu/main.c:595
msgid "while erasing target cache"
msgstr "bei Löschen des Zielzwischenspeichers"

#: ../../src/clients/ksu/main.c:615
#, c-format
msgid "ksu: permission denied (shell).\n"
msgstr "ksu: Zugriff verweigert (Shell)\n"

#: ../../src/clients/ksu/main.c:624
#, c-format
msgid "ksu: couldn't set environment variable USER\n"
msgstr "ksu: Umgebungsvariable USER kann nicht gesetzt werden\n"

#: ../../src/clients/ksu/main.c:630
#, c-format
msgid "ksu: couldn't set environment variable HOME\n"
msgstr "ksu: Umgebungsvariable HOME kann nicht gesetzt werden\n"

#: ../../src/clients/ksu/main.c:635
#, c-format
msgid "ksu: couldn't set environment variable SHELL\n"
msgstr "ksu: Umgebungsvariable SHELL kann nicht gesetzt werden\n"

#: ../../src/clients/ksu/main.c:646
#, c-format
msgid "ksu: initgroups failed.\n"
msgstr "ksu: »initgroups« fehlgeschlagen\n"

#: ../../src/clients/ksu/main.c:651
#, c-format
msgid "Leaving uid as %s (%ld)\n"
msgstr "UID bleibt %s (%ld)\n"

#: ../../src/clients/ksu/main.c:654
#, c-format
msgid "Changing uid to %s (%ld)\n"
msgstr "UID wird zu %s (%ld) geändert\n"

#: ../../src/clients/ksu/main.c:680
msgid "while getting name of target ccache"
msgstr "beim Holen des Ziel-Ccache-Namens"

#: ../../src/clients/ksu/main.c:700
#, c-format
msgid "%s does not have correct permissions for %s, %s aborted"
msgstr "%s hat nicht die korrekten Rechte für %s, %s wird abgebrochen."

#: ../../src/clients/ksu/main.c:721
#, c-format
msgid "Internal error: command %s did not get resolved\n"
msgstr "Interner Fehler: Befehl %s wurde nicht aufgelöst\n"

#: ../../src/clients/ksu/main.c:738 ../../src/clients/ksu/main.c:774
#, c-format
msgid "while trying to execv %s"
msgstr "beim Versuch von »execv %s«"

#: ../../src/clients/ksu/main.c:764
msgid "while calling waitpid"
msgstr "beim Aufruf von »waitpid«"

#: ../../src/clients/ksu/main.c:769
msgid "while trying to fork."
msgstr "beim Versuch zu verzweigen."

#: ../../src/clients/ksu/main.c:791
msgid "while reading cache name from ccache"
msgstr "beim Lesen des Zwischenspeichernamens aus dem Ccache"

#: ../../src/clients/ksu/main.c:797
#, c-format
msgid "ksu: couldn't set environment variable %s\n"
msgstr "ksu: Umgebungsvariable %s kann nicht gesetzt werden\n"

#: ../../src/clients/ksu/main.c:820
#, c-format
msgid "while clearing the value of %s"
msgstr "beim Leeren des Werts von %s"

#: ../../src/clients/ksu/main.c:828
msgid "while resetting target ccache name"
msgstr "beim Zurücksetzen des Ziel-Ccache-Namens"

#: ../../src/clients/ksu/main.c:842
msgid "while determining target ccache name"
msgstr "beim Bestimmen des Ziel-Ccache-Namens"

#: ../../src/clients/ksu/main.c:881
msgid "while generating part of the target ccache name"
msgstr "beim Erzeugen eines Teils des Ziel-Ccache-Namens"

#: ../../src/clients/ksu/main.c:887
msgid "while allocating memory for the target ccache name"
msgstr "beim Reservieren von Speicher für den Ziel-Ccache-Namen"

#: ../../src/clients/ksu/main.c:906
msgid "while creating new target ccache"
msgstr "bei Erstellen von neuem Ziel-Ccache"

#: ../../src/clients/ksu/main.c:912
msgid "while initializing target cache"
msgstr "beim Initialisieren des Zielzwischenspeichers"

#: ../../src/clients/ksu/main.c:952
#, c-format
msgid "terminal name %s too long\n"
msgstr "Terminal-Name %s ist zu lang.\n"

#: ../../src/clients/ksu/main.c:980
msgid "while changing to target uid for destroying ccache"
msgstr "beim Ändern der Ziel-UID für das Zerstören von Ccache"

#: ../../src/clients/kswitch/kswitch.c:44
#, c-format
msgid "Usage: %s {-c cache_name | -p principal}\n"
msgstr "Aufruf: %s {-c Zwischenspeichername | -p Principal}\n"

#: ../../src/clients/kswitch/kswitch.c:46
#, c-format
msgid "\t-p specify name of principal\n"
msgstr "\t-p gibt den Namen des Principals an.\n"

#: ../../src/clients/kswitch/kswitch.c:69
#, c-format
msgid "Only one -c or -p option allowed\n"
msgstr "Nur eine der Optionen -c oder -p ist erlaubt.\n"

#: ../../src/clients/kswitch/kswitch.c:88
#, c-format
msgid "One of -c or -p must be specified\n"
msgstr "Entweder -c oder -p muss angegeben werden.\n"

#: ../../src/clients/kswitch/kswitch.c:110 ../../src/clients/kvno/kvno.c:211
#: ../../src/clients/kvno/kvno.c:245 ../../src/kadmin/cli/keytab.c:350
#: ../../src/kadmin/dbutil/kdb5_util.c:576
#, c-format
msgid "while parsing principal name %s"
msgstr "beim Auswerten des Principal-Namens %s"

#: ../../src/clients/kswitch/kswitch.c:124
msgid "while switching to credential cache"
msgstr "beim Wechsel auf den Anmeldedatenzwischenspeicher"

#: ../../src/clients/kvno/kvno.c:46
#, c-format
msgid "usage: %s [-C] [-u] [-c ccache] [-e etype]\n"
msgstr "Aufruf: %s [-C] [-u] [-c Ccache] [-e Etype]\n"

#: ../../src/clients/kvno/kvno.c:47
#, c-format
msgid "\t[-k keytab] [-S sname] [-U for_user [-P]]\n"
msgstr "\t[-k Schlüsseltabelle] [-S Sname] [-U für_Benutzer [-P]]\n"

#: ../../src/clients/kvno/kvno.c:48
#, c-format
msgid "\tservice1 service2 ...\n"
msgstr "\tDienst1 Dienst2 …\n"

#: ../../src/clients/kvno/kvno.c:103 ../../src/clients/kvno/kvno.c:111
#, c-format
msgid "Options -u and -S are mutually exclusive\n"
msgstr "Die Optionen -u und -S schließen sich gegenseitig aus.\n"

#: ../../src/clients/kvno/kvno.c:126
#, c-format
msgid "Option -P (constrained delegation) requires keytab to be specified\n"
msgstr ""
"Die Option -P (eingeschränkte Abtretung) erfordert zur Angabe eine "
"Schlüsseltabelle.\n"

#: ../../src/clients/kvno/kvno.c:130
#, c-format
msgid ""
"Option -P (constrained delegation) requires option -U (protocol transition)\n"
msgstr ""
"Die Option -P (eingeschränkte Abtretung) erfordert die Option -U "
"(Protokollübergang)\n"

#: ../../src/clients/kvno/kvno.c:175 ../../src/kadmin/cli/kadmin.c:280
msgid "while initializing krb5 library"
msgstr "beim Initialisieren der Krb5-Bibliothek"

#: ../../src/clients/kvno/kvno.c:182
msgid "while converting etype"
msgstr "bei der Etype-Umwandlung"

#: ../../src/clients/kvno/kvno.c:218
msgid "while getting client principal name"
msgstr "beim Holen des Client-Principal-Namens"

#: ../../src/clients/kvno/kvno.c:256
#, c-format
msgid "while formatting parsed principal name for '%s'"
msgstr "beim Formatieren des ausgewerteten Principal-Namens für »%s«"

#: ../../src/clients/kvno/kvno.c:267
msgid "client and server principal names must match"
msgstr "Die Principal-Namen von Client und Server müssen übereinstimmen."

#: ../../src/clients/kvno/kvno.c:284
#, c-format
msgid "while getting credentials for %s"
msgstr "beim Holen der Anmeldedaten für %s"

#: ../../src/clients/kvno/kvno.c:291
#, c-format
msgid "while decoding ticket for %s"
msgstr "beim Dekodieren des Tickets für %s"

#: ../../src/clients/kvno/kvno.c:302
#, c-format
msgid "while decrypting ticket for %s"
msgstr "beim Entschlüsseln des Tickets für %s"

#: ../../src/clients/kvno/kvno.c:306
#, c-format
msgid "%s: kvno = %d, keytab entry valid\n"
msgstr "%s: KVNO = %d, Schlüsseltabelleneintrag gültig\n"

#: ../../src/clients/kvno/kvno.c:324
#, c-format
msgid "%s: constrained delegation failed"
msgstr "%s: eingeschränkte Abtretung fehlgeschlagen"

#: ../../src/clients/kvno/kvno.c:330
#, c-format
msgid "%s: kvno = %d\n"
msgstr "%s: KVNO = %d\n"

#: ../../src/kadmin/cli/kadmin.c:118
#, c-format
msgid ""
"Usage: %s [-r realm] [-p principal] [-q query] [clnt|local args]\n"
"\tclnt args: [-s admin_server[:port]] [[-c ccache]|[-k [-t keytab]]]|[-n]\n"
"\tlocal args: [-x db_args]* [-d dbname] [-e \"enc:salt ...\"] [-m]\n"
"where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""
"Aufruf: %s [-r Realm] [-p Principal] [-q Abfrage] [clnt|lokale Argumente]\n"
"\tclnt Argumente: [-s Admin-Server[:Port]] [[-c Ccache]|\n"
"\t[-k [-t Schlüsseltabelle]]]|[-n] lokale Argumente: [-x DB-Argumente]*\n"
"\t[-d Datenbankname] [-e \"enc:Salt …\"] [-m]\n"
"wobei\n"
"\t[-x DB-Argumente]* - eine beliebige Anzahl datenbankspezifischer "
"Argumente\n"
"\tist. Die unterstützten Argumente finden Sie in den jeweiligen "
"\tDatenbankdokumentationen\n"

#: ../../src/kadmin/cli/kadmin.c:292 ../../src/kadmin/cli/kadmin.c:333
#, c-format
msgid "%s: Cannot initialize. Not enough memory\n"
msgstr "%s: Zu wenig Speicher zum Initialisieren\n"

#: ../../src/kadmin/cli/kadmin.c:353 ../../src/kadmin/cli/kadmin.c:804
#: ../../src/kadmin/cli/kadmin.c:1084 ../../src/kadmin/cli/kadmin.c:1634
#: ../../src/kadmin/cli/keytab.c:159 ../../src/kadmin/dbutil/kdb5_util.c:591
#, c-format
msgid "while parsing keysalts %s"
msgstr "beim Auswerten der Schlüssel-Salts %s"

#: ../../src/kadmin/cli/kadmin.c:376
#, c-format
msgid "%s: unable to get default realm\n"
msgstr "%s: Standard-Realm kann nicht geholt werden\n"

#: ../../src/kadmin/cli/kadmin.c:396
msgid "while opening default credentials cache"
msgstr "beim Öffnen des Standardanmeldedatenzwischenspeichers"

#: ../../src/kadmin/cli/kadmin.c:402
#, c-format
msgid "while opening credentials cache %s"
msgstr "beim Öffnen des Anmeldedatenzwischenspeichers %s"

#: ../../src/kadmin/cli/kadmin.c:424 ../../src/kadmin/cli/kadmin.c:479
#: ../../src/kadmin/cli/kadmin.c:487 ../../src/kadmin/cli/kadmin.c:494
#, c-format
msgid "%s: out of memory\n"
msgstr "%s: Speicherplatz reicht nicht aus\n"

#: ../../src/kadmin/cli/kadmin.c:433 ../../src/kadmin/cli/kadmin.c:448
#: ../../src/slave/kpropd.c:681
msgid "while canonicalizing principal name"
msgstr "während der Principal-Name in die normale Form gebracht wird"

#: ../../src/kadmin/cli/kadmin.c:442
msgid "creating host service principal"
msgstr "Principal des Rechnerdienstes wird erstellt"

#: ../../src/kadmin/cli/kadmin.c:455
#, c-format
msgid "%s: unable to canonicalize principal\n"
msgstr "%s: Principal kann nicht in die normale Form gebracht werden\n"

#: ../../src/kadmin/cli/kadmin.c:499
#, c-format
msgid "%s: unable to figure out a principal name\n"
msgstr "%s: Es kann kein Principal-Name herausgefunden werden.\n"

#: ../../src/kadmin/cli/kadmin.c:507
msgid "while setting up logging"
msgstr "beim Einrichten der Protokollierung"

#: ../../src/kadmin/cli/kadmin.c:516
#, c-format
msgid "Authenticating as principal %s with existing credentials.\n"
msgstr "Authentifizierung als Principal %s mit existierenden Anmeldedaten\n"

#: ../../src/kadmin/cli/kadmin.c:522
#, c-format
msgid "Authenticating as principal %s with password; anonymous requested.\n"
msgstr ""
"Authentifizierung als Principal %s mit Passwort; Anonymität erwünscht\n"

#: ../../src/kadmin/cli/kadmin.c:529
#, c-format
msgid "Authenticating as principal %s with keytab %s.\n"
msgstr "Authentifizierung als Principal %s mit Schlüsseltabelle %s\n"

#: ../../src/kadmin/cli/kadmin.c:532
#, c-format
msgid "Authenticating as principal %s with default keytab.\n"
msgstr "Authentifizierung als Principal %s mit Standardschlüsseltabelle\n"

#: ../../src/kadmin/cli/kadmin.c:538
#, c-format
msgid "Authenticating as principal %s with password.\n"
msgstr "Authentifizierung als Principal %s mit Passwort\n"

#: ../../src/kadmin/cli/kadmin.c:546 ../../src/slave/kpropd.c:728
#, c-format
msgid "while initializing %s interface"
msgstr "beim Initialisieren der Schnittstelle %s"

#: ../../src/kadmin/cli/kadmin.c:560
#, c-format
msgid "while closing ccache %s"
msgstr "beim Schließen von Ccache %s"

#: ../../src/kadmin/cli/kadmin.c:566
msgid "while mapping update log"
msgstr "beim Abbilden des Aktualisierungsprotokolls"

#: ../../src/kadmin/cli/kadmin.c:581
msgid "while unlocking locked database"
msgstr "beim Entsperren der Datenbank"

#: ../../src/kadmin/cli/kadmin.c:590
msgid "Administration credentials NOT DESTROYED.\n"
msgstr "Verwaltungsanmeldedaten NICHT VERNICHTET\n"

#: ../../src/kadmin/cli/kadmin.c:639
#, c-format
msgid "usage: delete_principal [-force] principal\n"
msgstr "Aufruf: delete_principal [-force] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:644 ../../src/kadmin/cli/kadmin.c:819
msgid "while parsing principal name"
msgstr "beim Auswerten des Principal-Namens"

#: ../../src/kadmin/cli/kadmin.c:650 ../../src/kadmin/cli/kadmin.c:825
#: ../../src/kadmin/cli/kadmin.c:1217 ../../src/kadmin/cli/kadmin.c:1339
#: ../../src/kadmin/cli/kadmin.c:1409 ../../src/kadmin/cli/kadmin.c:1858
#: ../../src/kadmin/cli/kadmin.c:1902 ../../src/kadmin/cli/kadmin.c:1948
#: ../../src/kadmin/cli/kadmin.c:1988
msgid "while canonicalizing principal"
msgstr "während der Principal in die normale Form gebracht wird"

#: ../../src/kadmin/cli/kadmin.c:654
#, c-format
msgid "Are you sure you want to delete the principal \"%s\"? (yes/no): "
msgstr ""
"Sind Sie sicher, dass Sie den Principal »%s« löschen möchten? (yes/no): "

#: ../../src/kadmin/cli/kadmin.c:658
#, c-format
msgid "Principal \"%s\" not deleted\n"
msgstr "Principal »%s« nicht gelöscht\n"

#: ../../src/kadmin/cli/kadmin.c:665
#, c-format
msgid "while deleting principal \"%s\""
msgstr "beim Löschen von Principal »%s«"

#: ../../src/kadmin/cli/kadmin.c:668
#, c-format
msgid "Principal \"%s\" deleted.\n"
msgstr "Principal »%s« gelöscht\n"

#: ../../src/kadmin/cli/kadmin.c:669
#, c-format
msgid ""
"Make sure that you have removed this principal from all ACLs before "
"reusing.\n"
msgstr ""
"Stellen Sie sicher, dass Sie diesen Principal aus allen ACLs entfernt haben, "
"bevor Sie ihn erneut benutzen.\n"

#: ../../src/kadmin/cli/kadmin.c:686
#, c-format
msgid "usage: rename_principal [-force] old_principal new_principal\n"
msgstr "Aufruf: rename_principal [-force] alter_Principal neuer_Principal\n"

#: ../../src/kadmin/cli/kadmin.c:693
msgid "while parsing old principal name"
msgstr "beim Auswerten des alten Principal-Namens"

#: ../../src/kadmin/cli/kadmin.c:699
msgid "while parsing new principal name"
msgstr "beim Auswerten des neuen Principal-Namens"

#: ../../src/kadmin/cli/kadmin.c:705
msgid "while canonicalizing old principal"
msgstr "während der alte Principal in die normale Form gebracht wird"

#: ../../src/kadmin/cli/kadmin.c:711
msgid "while canonicalizing new principal"
msgstr "während der neue Principal in die normale Form gebracht wird"

#: ../../src/kadmin/cli/kadmin.c:715
#, c-format
msgid ""
"Are you sure you want to rename the principal \"%s\" to \"%s\"? (yes/no): "
msgstr ""
"Sind Sie sicher, dass Sie den Principal »%s« in »%s« umbenennen möchten? "
"(yes/no): "

#: ../../src/kadmin/cli/kadmin.c:719
#, c-format
msgid "Principal \"%s\" not renamed\n"
msgstr "Principal »%s« wurde nicht umbenannt.\n"

#: ../../src/kadmin/cli/kadmin.c:726
#, c-format
msgid "while renaming principal \"%s\" to \"%s\""
msgstr "beim Umbenennen von Principal »%s« in »%s«"

#: ../../src/kadmin/cli/kadmin.c:730
#, c-format
msgid "Principal \"%s\" renamed to \"%s\".\n"
msgstr "Principal »%s« wurde in »%s« umbenannt.\n"

#: ../../src/kadmin/cli/kadmin.c:731
#, c-format
msgid ""
"Make sure that you have removed the old principal from all ACLs before "
"reusing.\n"
msgstr ""
"Stellen Sie sicher, dass Sie den alten Principal aus allen ACLs entfernt "
"haben, bevor Sie ihn erneut benutzen.\n"

#: ../../src/kadmin/cli/kadmin.c:746
#, c-format
msgid ""
"usage: change_password [-randkey] [-keepold] [-e keysaltlist] [-pw password] "
"principal\n"
msgstr ""
"Aufruf: change_password [-randkey] [-keepold] [-e Schlüssel-Salt-Liste] [-pw "
"Passwort] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:772
msgid "change_password: missing db argument"
msgstr "change_password: fehlendes Datenbankargument"

#: ../../src/kadmin/cli/kadmin.c:778
#, c-format
msgid "change_password: Not enough memory\n"
msgstr "change_password: zu wenig Speicher\n"

#: ../../src/kadmin/cli/kadmin.c:786
msgid "change_password: missing password arg"
msgstr "change_password: fehlendes Passwortargument"

#: ../../src/kadmin/cli/kadmin.c:797
msgid "change_password: missing keysaltlist arg"
msgstr "change_password: fehlendes Schlüssel-Salt-Listenargument"

#: ../../src/kadmin/cli/kadmin.c:813
msgid "missing principal name"
msgstr "fehlender Principal-Name"

#: ../../src/kadmin/cli/kadmin.c:837 ../../src/kadmin/cli/kadmin.c:874
#, c-format
msgid "while changing password for \"%s\"."
msgstr "beim Ändern des Passworts von »%s«."

#: ../../src/kadmin/cli/kadmin.c:840 ../../src/kadmin/cli/kadmin.c:877
#, c-format
msgid "Password for \"%s\" changed.\n"
msgstr "Passwort von »%s« geändert\n"

#: ../../src/kadmin/cli/kadmin.c:846 ../../src/kadmin/cli/kadmin.c:1290
#, c-format
msgid "while randomizing key for \"%s\"."
msgstr "beim Erzeugen eines zufälligen Schlüssels für »%s«."

#: ../../src/kadmin/cli/kadmin.c:849
#, c-format
msgid "Key for \"%s\" randomized.\n"
msgstr "Es wurde ein zufälliger Schlüssel für %s erzeugt\n"

#: ../../src/kadmin/cli/kadmin.c:854 ../../src/kadmin/cli/kadmin.c:1250
#, c-format
msgid "Enter password for principal \"%s\""
msgstr "Geben Sie das Passwort für Principal »%s« ein."

#: ../../src/kadmin/cli/kadmin.c:856 ../../src/kadmin/cli/kadmin.c:1252
#, c-format
msgid "Re-enter password for principal \"%s\""
msgstr "Geben Sie das Passwort für Principal »%s« erneut ein."

#: ../../src/kadmin/cli/kadmin.c:861 ../../src/kadmin/cli/kadmin.c:1256
#, c-format
msgid "while reading password for \"%s\"."
msgstr "beim Lesen des Passworts von »%s«."

#: ../../src/kadmin/cli/kadmin.c:915
#, c-format
msgid "Not enough memory\n"
msgstr "Speicher reicht nicht aus\n"

#: ../../src/kadmin/cli/kadmin.c:945 ../../src/kadmin/dbutil/kdb5_util.c:623
msgid "while getting time"
msgstr "beim Holen der Zeit"

#: ../../src/kadmin/cli/kadmin.c:994 ../../src/kadmin/cli/kadmin.c:1007
#: ../../src/kadmin/cli/kadmin.c:1020 ../../src/kadmin/cli/kadmin.c:1033
#: ../../src/kadmin/cli/kadmin.c:1546 ../../src/kadmin/cli/kadmin.c:1558
#: ../../src/kadmin/cli/kadmin.c:1601 ../../src/kadmin/cli/kadmin.c:1618
#, c-format
msgid "Invalid date specification \"%s\".\n"
msgstr "ungültige Datumsangabe »%s«\n"

#: ../../src/kadmin/cli/kadmin.c:1118 ../../src/kadmin/cli/kadmin.c:1333
#: ../../src/kadmin/cli/kadmin.c:1404 ../../src/kadmin/cli/kadmin.c:1852
#: ../../src/kadmin/cli/kadmin.c:1896 ../../src/kadmin/cli/kadmin.c:1942
#: ../../src/kadmin/cli/kadmin.c:1982
msgid "while parsing principal"
msgstr "beim Auswerten des Principals"

#: ../../src/kadmin/cli/kadmin.c:1127
#, c-format
msgid "usage: add_principal [options] principal\n"
msgstr "Aufruf: add_principal [Optionen] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:1128 ../../src/kadmin/cli/kadmin.c:1155
#: ../../src/kadmin/cli/kadmin.c:1657
#, c-format
msgid "\toptions are:\n"
msgstr "\tEs gibt folgende Optionen:\n"

#: ../../src/kadmin/cli/kadmin.c:1130
#, c-format
msgid ""
"\t\t[-randkey|-nokey] [-x db_princ_args]* [-expire expdate] [-pwexpire "
"pwexpdate] [-maxlife maxtixlife]\n"
"\t\t[-kvno kvno] [-policy policy] [-clearpolicy]\n"
"\t\t[-pw password] [-maxrenewlife maxrenewlife]\n"
"\t\t[-e keysaltlist]\n"
"\t\t[{+|-}attribute]\n"
msgstr ""
"\t\t[-randkey|-nokey] [-x DB-Principal-Argumente]* [-expire Ablaufdatum] [-"
"pwexpire Passwortablaufdatum] [-maxlife maximale_Ticketlebensdauer]\n"
"\t\t[-kvno KVNO] [-policy Richtlinie] [-clearpolicy]\n"
"\t\t[-pw Passwort] [-maxrenewlife maximale_Dauer_bis_zum_Erneuern]\n"
"\t\t[-e Schlüssel-Salt-Liste]\n"
"\t\t[{+|-}Attribut]\n"

#: ../../src/kadmin/cli/kadmin.c:1136
#, c-format
msgid "\tattributes are:\n"
msgstr "\tEs gibt folgende Attribute:\n"

#: ../../src/kadmin/cli/kadmin.c:1138 ../../src/kadmin/cli/kadmin.c:1164
#, c-format
msgid ""
"\t\tallow_postdated allow_forwardable allow_tgs_req allow_renewable\n"
"\t\tallow_proxiable allow_dup_skey allow_tix requires_preauth\n"
"\t\trequires_hwauth needchange allow_svr password_changing_service\n"
"\t\tok_as_delegate ok_to_auth_as_delegate no_auth_data_required\n"
"\n"
"where,\n"
"\t[-x db_princ_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""
"\t\tallow_postdated allow_forwardable allow_tgs_req allow_renewable\n"
"\t\tallow_proxiable allow_dup_skey allow_tix requires_preauth\n"
"\t\trequires_hwauth needchange allow_svr password_changing_service\n"
"\t\tok_as_delegate ok_to_auth_as_delegate no_auth_data_required\n"
"\n"
"wobei\n"
"\t[-x DB-Principal-Argumente]* - eine beliebige Zahl\n"
"\tdatenbankspezifischer Argumente ist.\n"
"\t\t\tDie unterstützten Argumente finden Sie in der jeweiligen\n"
"Datenbankdokumentation.\n"

#: ../../src/kadmin/cli/kadmin.c:1154
#, c-format
msgid "usage: modify_principal [options] principal\n"
msgstr "Aufruf: modify_principal [Optionen] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:1157
#, c-format
msgid ""
"\t\t[-x db_princ_args]* [-expire expdate] [-pwexpire pwexpdate] [-maxlife "
"maxtixlife]\n"
"\t\t[-kvno kvno] [-policy policy] [-clearpolicy]\n"
"\t\t[-maxrenewlife maxrenewlife] [-unlock] [{+|-}attribute]\n"
msgstr ""
"\t\t[-x DB-Principal-Argumente]* [-expire Ablaufdatum] [-pwexpire "
"Passwortablaufdatum] [-maxlife maximale_Ticketlebensdauer]\n"
"\t\t[-kvno KVNO] [-policy Richtlinie] [-clearpolicy]\n"
"\t\t[-maxrenewlife maximale_Dauer_bis_zum_Erneuern] [-unlock] [{+|-}"
"Attribut]\n"

#: ../../src/kadmin/cli/kadmin.c:1224 ../../src/kadmin/cli/kadmin.c:1362
#, c-format
msgid "WARNING: policy \"%s\" does not exist\n"
msgstr "WARNUNG: Richtlinie »%s« existiert nicht.\n"

#: ../../src/kadmin/cli/kadmin.c:1230
#, c-format
msgid "No policy specified for %s; assigning \"default\"\n"
msgstr ""
"Für %s wurde keine Richtlinie angegeben, es wird »default« "
"zugewiesen\n"

#: ../../src/kadmin/cli/kadmin.c:1235
#, c-format
msgid "No policy specified for %s; defaulting to no policy\n"
msgstr ""
"Für %s wurde keine Richtlinie angegeben, es wird die Vorgabe "
"»keine\n"
"Richtlinie« verwandt.\n"

#: ../../src/kadmin/cli/kadmin.c:1276
#, c-format
msgid "Admin server does not support -nokey while creating \"%s\"\n"
msgstr ""
"Der Administrationsrechner unterstützt beim Erstellen von »%s« kein -nokey\n"

#: ../../src/kadmin/cli/kadmin.c:1298
#, c-format
msgid "while clearing DISALLOW_ALL_TIX for \"%s\"."
msgstr "beim Löschen von DISALLOW_ALL_TIX für »%s«."

#: ../../src/kadmin/cli/kadmin.c:1345
#, c-format
msgid "while getting \"%s\"."
msgstr "beim Holen von »%s«."

#: ../../src/kadmin/cli/kadmin.c:1371
#, c-format
msgid "while modifying \"%s\"."
msgstr "beim Ändern von »%s«."

#: ../../src/kadmin/cli/kadmin.c:1375
#, c-format
msgid "Principal \"%s\" modified.\n"
msgstr "Principal »%s« wurde geändert.\n"

#: ../../src/kadmin/cli/kadmin.c:1396
#, c-format
msgid "usage: get_principal [-terse] principal\n"
msgstr "Aufruf: get_principal [-terse] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:1415
#, c-format
msgid "while retrieving \"%s\"."
msgstr "beim Abfragen von »%s«."

#: ../../src/kadmin/cli/kadmin.c:1420 ../../src/kadmin/cli/kadmin.c:1425
msgid "while unparsing principal"
msgstr "beim Rückgängigmachen der Auswertung des Principals"

#: ../../src/kadmin/cli/kadmin.c:1429
#, c-format
msgid "Principal: %s\n"
msgstr "Principal: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1430
#, c-format
msgid "Expiration date: %s\n"
msgstr "Ablaufdatum: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1431 ../../src/kadmin/cli/kadmin.c:1433
#: ../../src/kadmin/cli/kadmin.c:1444
msgid "[never]"
msgstr "[niemals]"

#: ../../src/kadmin/cli/kadmin.c:1432
#, c-format
msgid "Last password change: %s\n"
msgstr "Letzte Passwortänderung: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1434
#, c-format
msgid "Password expiration date: %s\n"
msgstr "Passwortablaufdatum: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1436 ../../src/kadmin/cli/kadmin.c:1478
msgid "[none]"
msgstr "[keins]"

#: ../../src/kadmin/cli/kadmin.c:1437
#, c-format
msgid "Maximum ticket life: %s\n"
msgstr "maximale Ticketlebensdauer: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1438
#, c-format
msgid "Maximum renewable life: %s\n"
msgstr "maximale verlängerbare Lebensdauer: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1440
#, c-format
msgid "Last modified: %s (%s)\n"
msgstr "zuletzt geändert: %s (%s)\n"

#: ../../src/kadmin/cli/kadmin.c:1442
#, c-format
msgid "Last successful authentication: %s\n"
msgstr "letzte erfolgreiche Authentifizierung: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1448
#, c-format
msgid "Failed password attempts: %d\n"
msgstr "Fehlgeschlagene Anmeldeversuche: %d\n"

#: ../../src/kadmin/cli/kadmin.c:1450
#, c-format
msgid "Number of keys: %d\n"
msgstr "Anzahl der Schlüssel: %d\n"

#: ../../src/kadmin/cli/kadmin.c:1457
#, c-format
msgid "<Encryption type 0x%x>"
msgstr "<Verschlüsselungstyp 0x%x>"

#: ../../src/kadmin/cli/kadmin.c:1464
#, c-format
msgid "<Salt type 0x%x>"
msgstr "<Salt-Typ 0x%x>"

#: ../../src/kadmin/cli/kadmin.c:1470
#, c-format
msgid "MKey: vno %d\n"
msgstr "MKey: vno %d\n"

#: ../../src/kadmin/cli/kadmin.c:1472
#, c-format
msgid "Attributes:"
msgstr "Attribute:"

#: ../../src/kadmin/cli/kadmin.c:1480
msgid " [does not exist]"
msgstr " [existiert nicht]"

#: ../../src/kadmin/cli/kadmin.c:1481
#, c-format
msgid "Policy: %s%s\n"
msgstr "Richtlinie: %s%s\n"

#: ../../src/kadmin/cli/kadmin.c:1517
#, c-format
msgid "usage: get_principals [expression]\n"
msgstr "Aufruf: get_principals [Ausdruck]\n"

#: ../../src/kadmin/cli/kadmin.c:1522 ../../src/kadmin/cli/kadmin.c:1794
msgid "while retrieving list."
msgstr "beim Abfragen der Liste."

#: ../../src/kadmin/cli/kadmin.c:1647
#, c-format
msgid "%s: parser lost count!\n"
msgstr "%s: Auswertungsprogramm verlor Anzahl!\n"

#: ../../src/kadmin/cli/kadmin.c:1656
#, c-format
msgid "usage; %s [options] policy\n"
msgstr "Aufruf: %s [Optionen] Richtlinie\n"

#: ../../src/kadmin/cli/kadmin.c:1659
#, c-format
msgid ""
"\t\t[-maxlife time] [-minlife time] [-minlength length]\n"
"\t\t[-minclasses number] [-history number]\n"
"\t\t[-maxfailure number] [-failurecountinterval time]\n"
"\t\t[-allowedkeysalts keysalts]\n"
msgstr ""
"\t\t[-maxlife Zeit] [-minlife Zeit] [-minlength Länge]\n"
"\t\t[-minclasses Anzahl] [-history Nummer]\n"
"\t\t[-maxfailure Anzahl] [-failurecountinterval Zeit]\n"
"\t\t[-allowedkeysalts Schlüssel-Salts]\n"

#: ../../src/kadmin/cli/kadmin.c:1663
#, c-format
msgid "\t\t[-lockoutduration time]\n"
msgstr "\t\t[-lockoutduration Dauer]\n"

#: ../../src/kadmin/cli/kadmin.c:1682
#, c-format
msgid "while creating policy \"%s\"."
msgstr "beim Erstellen der Richtlinie »%s«"

#: ../../src/kadmin/cli/kadmin.c:1703
#, c-format
msgid "while modifying policy \"%s\"."
msgstr "beim Ändern der Richtlinie »%s«"

#: ../../src/kadmin/cli/kadmin.c:1715
#, c-format
msgid "usage: delete_policy [-force] policy\n"
msgstr "Aufruf: delete_policy [-force] Richtlinie\n"

#: ../../src/kadmin/cli/kadmin.c:1719
#, c-format
msgid "Are you sure you want to delete the policy \"%s\"? (yes/no): "
msgstr ""
"Sind Sie sicher, dass Sie die Richtlinie »%s« löschen möchten? (yes/no): "

#: ../../src/kadmin/cli/kadmin.c:1723
#, c-format
msgid "Policy \"%s\" not deleted.\n"
msgstr "Richtlinie »%s« nicht gelöscht\n"

#: ../../src/kadmin/cli/kadmin.c:1729
#, c-format
msgid "while deleting policy \"%s\""
msgstr "bei Löschen der Richtlinie »%s«"

#: ../../src/kadmin/cli/kadmin.c:1741
#, c-format
msgid "usage: get_policy [-terse] policy\n"
msgstr "Aufruf: get_policy [-terse] Richtlinie\n"

#: ../../src/kadmin/cli/kadmin.c:1746
#, c-format
msgid "while retrieving policy \"%s\"."
msgstr "beim Abfragen der Richtlinie »%s«."

#: ../../src/kadmin/cli/kadmin.c:1751
#, c-format
msgid "Policy: %s\n"
msgstr "Richtlinie: »%s«\n"

#: ../../src/kadmin/cli/kadmin.c:1752
#, c-format
msgid "Maximum password life: %ld\n"
msgstr "maximale Passwortlebensdauer: %ld\n"

#: ../../src/kadmin/cli/kadmin.c:1753
#, c-format
msgid "Minimum password life: %ld\n"
msgstr "minimale Passwortlebensdauer: %ld\n"

#: ../../src/kadmin/cli/kadmin.c:1754
#, c-format
msgid "Minimum password length: %ld\n"
msgstr "minimale Passwortlänge: %ld\n"

#: ../../src/kadmin/cli/kadmin.c:1755
#, c-format
msgid "Minimum number of password character classes: %ld\n"
msgstr "minimale Anzahl von Passwortzeichenklassen: %ld\n"

#: ../../src/kadmin/cli/kadmin.c:1757
#, c-format
msgid "Number of old keys kept: %ld\n"
msgstr "Anzahl aufbewahrter alter Schlüssel: %ld\n"

#: ../../src/kadmin/cli/kadmin.c:1758
#, c-format
msgid "Maximum password failures before lockout: %lu\n"
msgstr "maximale Anzahl falscher Passworteingaben vor dem Sperren: %lu\n"

#: ../../src/kadmin/cli/kadmin.c:1760
#, c-format
msgid "Password failure count reset interval: %s\n"
msgstr "Rücksetzintervall für zu viele falsch eingebene Passwörter: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1762
#, c-format
msgid "Password lockout duration: %s\n"
msgstr "Passwortsperrdauer: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1765
#, c-format
msgid "Allowed key/salt types: %s\n"
msgstr "erlaubte Schlüssel-/Salt-Typen: %s\n"

#: ../../src/kadmin/cli/kadmin.c:1789
#, c-format
msgid "usage: get_policies [expression]\n"
msgstr "Aufruf: get_policies [Ausdruck]\n"

#: ../../src/kadmin/cli/kadmin.c:1811
#, c-format
msgid "usage: get_privs\n"
msgstr "Aufruf: get_privs\n"

#: ../../src/kadmin/cli/kadmin.c:1816
msgid "while retrieving privileges"
msgstr "beim Abfragen von Rechten"

#: ../../src/kadmin/cli/kadmin.c:1819
#, c-format
msgid "current privileges:"
msgstr "aktuelle Rechte:"

#: ../../src/kadmin/cli/kadmin.c:1845
#, c-format
msgid "usage: purgekeys [-all|-keepkvno oldest_kvno_to_keep] principal\n"
msgstr ""
"Aufruf: purgekeys [-all|-keepkvno älteste_KVNO_die_behalten_wird] Principal\n"

#: ../../src/kadmin/cli/kadmin.c:1865
#, c-format
msgid "while purging keys for principal \"%s\""
msgstr "beim vollständigen Löschen der Schlüssel für Principal »%s«"

#: ../../src/kadmin/cli/kadmin.c:1870
#, c-format
msgid "All keys for principal \"%s\" removed.\n"
msgstr "Alle Schlüssel für Principal »%s« wurden entfernt.\n"

#: ../../src/kadmin/cli/kadmin.c:1872
#, c-format
msgid "Old keys for principal \"%s\" purged.\n"
msgstr "Alte Schlüssel für Principal »%s« wurden entfernt.\n"

#: ../../src/kadmin/cli/kadmin.c:1889
#, c-format
msgid "usage: get_strings principal\n"
msgstr "Aufruf: get_strings Principal\n"

#: ../../src/kadmin/cli/kadmin.c:1909
#, c-format
msgid "while getting attributes for principal \"%s\""
msgstr "beim Holen von Attributen für Principal »%s«"

#: ../../src/kadmin/cli/kadmin.c:1914
#, c-format
msgid "(No string attributes.)\n"
msgstr "(keine Zeichenkettenattribute)\n"

#: ../../src/kadmin/cli/kadmin.c:1933
#, c-format
msgid "usage: set_string principal key value\n"
msgstr "Aufruf: set_string Principal Schlüssel Wert\n"

#: ../../src/kadmin/cli/kadmin.c:1955
#, c-format
msgid "while setting attribute on principal \"%s\""
msgstr "beim Setzen eines Attributes für Principal »%s«"

#: ../../src/kadmin/cli/kadmin.c:1959
#, c-format
msgid "Attribute set for principal \"%s\".\n"
msgstr "Attribute für Principal »%s« wurden gesetzt.\n"

#: ../../src/kadmin/cli/kadmin.c:1974
#, c-format
msgid "usage: del_string principal key\n"
msgstr "Aufruf: del_string Principal Schlüssel\n"

#: ../../src/kadmin/cli/kadmin.c:1995
#, c-format
msgid "while deleting attribute from principal \"%s\""
msgstr "beim Löschen eines Attributs von Principal »%s«"

#: ../../src/kadmin/cli/kadmin.c:1999
#, c-format
msgid "Attribute removed from principal \"%s\".\n"
msgstr "Attribut von Principal »%s« wurde gelöscht.\n"

#: ../../src/kadmin/cli/keytab.c:56
#, c-format
msgid ""
"Usage: ktadd [-k[eytab] keytab] [-q] [-e keysaltlist] [-norandkey] "
"[principal | -glob princ-exp] [...]\n"
msgstr ""
"Aufruf: ktadd [-k[eytab] Schlüsseltabelle] [-q] [-e Schlüssel-Salt-Liste] [-"
"norandkey] [Principal | -glob Principal-Ausdruck] […]\n"

#: ../../src/kadmin/cli/keytab.c:59
#, c-format
msgid ""
"Usage: ktadd [-k[eytab] keytab] [-q] [-e keysaltlist] [principal | -glob "
"princ-exp] [...]\n"
msgstr ""
"Aufruf: ktadd [-k[eytab] Schlüsseltabelle] [-q] [-e Schlüssel-Salt-Liste] "
"[Principal | -glob Principal-Ausdruck] […]\n"

#: ../../src/kadmin/cli/keytab.c:67
#, c-format
msgid ""
"Usage: ktremove [-k[eytab] keytab] [-q] principal [kvno|\"all\"|\"old\"]\n"
msgstr ""
"Aufruf: ktremove [-k[eytab] Schlüsseltabelle] [-q] Principal "
"[kvno|»all«|»old«]\n"

#: ../../src/kadmin/cli/keytab.c:81 ../../src/kadmin/cli/keytab.c:102
msgid "while creating keytab name"
msgstr "beim Erstellen des Schlüsseltabellennamens"

#: ../../src/kadmin/cli/keytab.c:86
msgid "while opening default keytab"
msgstr "beim Öffnen der Standardschlüsseltabelle"

#: ../../src/kadmin/cli/keytab.c:147
#, c-format
msgid "-norandkey option only valid for kadmin.local\n"
msgstr "Die Option »-norandkey« ist nur für »kadmin.local« gültig.\n"

#: ../../src/kadmin/cli/keytab.c:176
#, c-format
msgid "cannot specify keysaltlist when not changing key\n"
msgstr ""
"Schlüssel-Salt-Liste kann nicht angegeben werden, wenn der Schlüssel nicht "
"geändert wird\n"

#: ../../src/kadmin/cli/keytab.c:192
#, c-format
msgid "while expanding expression \"%s\"."
msgstr "beim Expandieren des Ausdrucks »%s«."

#: ../../src/kadmin/cli/keytab.c:211 ../../src/kadmin/cli/keytab.c:251
msgid "while closing keytab"
msgstr "beim Schließen der Schlüsseltabelle"

#: ../../src/kadmin/cli/keytab.c:275
#, c-format
msgid "while parsing -add principal name %s"
msgstr "beim Auswerten von »-add Principal-Name %s«"

#: ../../src/kadmin/cli/keytab.c:289
#, c-format
msgid "%s: Principal %s does not exist.\n"
msgstr "%s: Principal %s existiert nicht.\n"

#: ../../src/kadmin/cli/keytab.c:292
#, c-format
msgid "while changing %s's key"
msgstr "beim Ändern des Schlüssels von %s"

#: ../../src/kadmin/cli/keytab.c:299
msgid "while retrieving principal"
msgstr "beim Abfragen des Principals"

#: ../../src/kadmin/cli/keytab.c:311
msgid "while adding key to keytab"
msgstr "beim Hinzufügen des Schlüssels zur Schlüsseltabelle"

#: ../../src/kadmin/cli/keytab.c:317
#, c-format
msgid ""
"Entry for principal %s with kvno %d, encryption type %s added to keytab %s.\n"
msgstr ""
"Der Eintrag für Principal %s mit KVNO %d und Verschlüsselungstyp %s wurde "
"der Schlüsseltabelle %s hinzugefügt.\n"

#: ../../src/kadmin/cli/keytab.c:326
msgid "while freeing principal entry"
msgstr "beim Freigeben des Principal-Eintrags"

#: ../../src/kadmin/cli/keytab.c:373
#, c-format
msgid "%s: Keytab %s does not exist.\n"
msgstr "%s: Schlüsseltabelle %s existiert nicht.\n"

#: ../../src/kadmin/cli/keytab.c:377
#, c-format
msgid "%s: No entry for principal %s exists in keytab %s\n"
msgstr ""
"%s: Für Principal %s existiert kein Eintrag in der Schlüsseltabelle %s.\n"

#: ../../src/kadmin/cli/keytab.c:381
#, c-format
msgid "%s: No entry for principal %s with kvno %d exists in keytab %s\n"
msgstr ""
"%s: Für den Principal %s mit der KVNO %d existiert kein Eintrag in der "
"Schlüsseltabelle %s.\n"

#: ../../src/kadmin/cli/keytab.c:387
msgid "while retrieving highest kvno from keytab"
msgstr "beim Abfragen der höchsten KVNO der Schlüsseltabelle"

#: ../../src/kadmin/cli/keytab.c:420
msgid "while temporarily ending keytab scan"
msgstr "beim Unterbrechen des Schlüsseltabellen-Scans"

#: ../../src/kadmin/cli/keytab.c:425
msgid "while deleting entry from keytab"
msgstr "beim Löschen eines Eintrags aus der Schlüsseltabelle"

#: ../../src/kadmin/cli/keytab.c:430
msgid "while restarting keytab scan"
msgstr "bei der Wiederaufnahme des Schlüsseltabellen-Scans"

#: ../../src/kadmin/cli/keytab.c:436
#, c-format
msgid "Entry for principal %s with kvno %d removed from keytab %s.\n"
msgstr ""
"Der Eintrag für Principal %s mit KVNO %d wurde aus der Schlüsseltabelle %s "
"entfernt.\n"

#: ../../src/kadmin/cli/keytab.c:458
#, c-format
msgid "%s: There is only one entry for principal %s in keytab %s\n"
msgstr ""
"%s: Es gibt nur einen Eintrag für Principal %s in der Schlüsseltabelle %s.\n"

#: ../../src/kadmin/cli/ss_wrapper.c:49 ../../src/kadmin/ktutil/ktutil.c:58
msgid "creating invocation"
msgstr "Aufruf wird erstellt"

#: ../../src/kadmin/dbutil/dump.c:165
msgid "while allocating temporary filename dump"
msgstr "beim Reservieren des temporären Dateinamenspeicherauszugs"

#: ../../src/kadmin/dbutil/dump.c:176
msgid "while renaming dump file into place"
msgstr "während das Umbenennen der Auszugsdateien Gestalt annimmt"

#: ../../src/kadmin/dbutil/dump.c:192
msgid "while allocating dump_ok filename"
msgstr "beim Reservieren des »dump_ok«-Dateinamens"

#: ../../src/kadmin/dbutil/dump.c:199
#, c-format
msgid "while creating 'ok' file, '%s'"
msgstr "beim Erstellen der Datei »ok«, »%s«"

#: ../../src/kadmin/dbutil/dump.c:206
#, c-format
msgid "while locking 'ok' file, '%s'"
msgstr "beim Sperren der Datei »ok«, »%s«"

#: ../../src/kadmin/dbutil/dump.c:248 ../../src/kadmin/dbutil/dump.c:277
#, c-format
msgid "%s: regular expression error: %s\n"
msgstr "%s: Fehler im regulären Ausdruck: %s\n"

#: ../../src/kadmin/dbutil/dump.c:260
#, c-format
msgid "%s: regular expression match error: %s\n"
msgstr "%s: Fehler beim Abgleich mit regulärem Ausdruck: %s\n"

#: ../../src/kadmin/dbutil/dump.c:361
#, c-format
msgid "%s: tagged data list inconsistency for %s (counted %d, stored %d)\n"
msgstr ""
"%s: Unstimmigkeit in der markierten Datenliste für %s (%d gezählt, %d "
"gespeichert)\n"

#: ../../src/kadmin/dbutil/dump.c:519
#, c-format
msgid ""
"Warning!  Multiple DES-CBC-CRC keys for principal %s; skipping duplicates.\n"
msgstr ""
"Warnung! Mehrere DES-CBC-CRC-Schlüssel für Principal %s, Duplikate werden "
"übersprungen.\n"

#: ../../src/kadmin/dbutil/dump.c:530
#, c-format
msgid ""
"Warning!  No DES-CBC-CRC key for principal %s, cannot generate OV-compatible "
"record; skipping\n"
msgstr ""
"Warnung! Kein DES-CBC-CRC-Schlüssel für Principal %s, es kann kein OV-"
"kompatibler Datensatz erzeugt werden, wird übersprungen\n"

#: ../../src/kadmin/dbutil/dump.c:558
#, c-format
msgid "while converting %s to new master key"
msgstr "beim Umwandeln von %s in den neuen Hauptschlüssel"

#: ../../src/kadmin/dbutil/dump.c:579
#, c-format
msgid "%s(%d): %s\n"
msgstr "%s(%d): %s\n"

#: ../../src/kadmin/dbutil/dump.c:622
#, c-format
msgid "%s(%d): ignoring trash at end of line: "
msgstr "%s(%d): Müll am Zeilenende wird ignoriert: "

#: ../../src/kadmin/dbutil/dump.c:685
msgid "cannot read tagged data type and length"
msgstr "Markierter Datentyp und Länge können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:692
msgid "cannot read tagged data contents"
msgstr "Inhalt der markierten Daten kann nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:726
msgid "cannot match size tokens"
msgstr "Größenmerkmale können nicht zugeordnet werden."

#: ../../src/kadmin/dbutil/dump.c:755
msgid "cannot read name string"
msgstr "Namenszeichenkette kann nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:760
#, c-format
msgid "while parsing name %s"
msgstr "beim Auswerten des Namens %s"

#: ../../src/kadmin/dbutil/dump.c:768
msgid "cannot read principal attributes"
msgstr "Principal-Attribute können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:821
msgid "cannot read key size and version"
msgstr "Schlüssellänge und -version können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:832
msgid "cannot read key type and length"
msgstr "Schlüsseltyp und -länge können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:838
msgid "cannot read key data"
msgstr "Schlüsseldaten können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:848
msgid "cannot read extra data"
msgstr "Zusätzliche Daten können nicht gelesen werden."

#: ../../src/kadmin/dbutil/dump.c:857
#, c-format
msgid "while storing %s"
msgstr "beim Speichern von %s"

#: ../../src/kadmin/dbutil/dump.c:896 ../../src/kadmin/dbutil/dump.c:935
#: ../../src/kadmin/dbutil/dump.c:981
#, c-format
msgid "cannot parse policy (%d read)\n"
msgstr "Richtlinie kann nicht ausgewertet werden (%d gelesen)\n"

#: ../../src/kadmin/dbutil/dump.c:904 ../../src/kadmin/dbutil/dump.c:943
#: ../../src/kadmin/dbutil/dump.c:1001
msgid "while creating policy"
msgstr "beim Erstellen der Richtlinie"

#: ../../src/kadmin/dbutil/dump.c:908
#, c-format
msgid "created policy %s\n"
msgstr "erstellte Richtlinie %s\n"

#: ../../src/kadmin/dbutil/dump.c:1038
#, c-format
msgid "unknown record type \"%s\"\n"
msgstr "unbekannter Datensatztyp »%s«\n"

#: ../../src/kadmin/dbutil/dump.c:1167
#, c-format
msgid "%s: Unknown iprop dump version %d\n"
msgstr "%s: unbekannte Iprop-Auszugsversion %d\n"

#: ../../src/kadmin/dbutil/dump.c:1270 ../../src/kadmin/dbutil/dump.c:1498
#, c-format
msgid "Iprop not enabled\n"
msgstr "Iprop nicht aktiviert\n"

#: ../../src/kadmin/dbutil/dump.c:1308
msgid "Conditional dump is an undocumented option for use only for iprop dumps"
msgstr ""
"Bedingter Auszug ist eine nicht dokumentierte Option, die nur für Iprop-"
"Auszüge benutzt wird."

#: ../../src/kadmin/dbutil/dump.c:1321
msgid "Database not currently opened!"
msgstr "Die Datenbank ist zur Zeit nicht geöffnet!"

#: ../../src/kadmin/dbutil/dump.c:1335
#: ../../src/kadmin/dbutil/kdb5_stash.c:116
#: ../../src/kadmin/dbutil/kdb5_util.c:479
msgid "while reading master key"
msgstr "beim Lesen des Hauptschlüssels"

#: ../../src/kadmin/dbutil/dump.c:1341
msgid "while verifying master key"
msgstr "beim Prüfen des Hauptschlüssels"

#: ../../src/kadmin/dbutil/dump.c:1360 ../../src/kadmin/dbutil/dump.c:1370
msgid "while reading new master key"
msgstr "beim Lesen des neuen Hauptschlüssels"

#: ../../src/kadmin/dbutil/dump.c:1364
#, c-format
msgid "Please enter new master key....\n"
msgstr "Bitte geben Sie den neuen Hauptschlüssel ein …\n"

#: ../../src/kadmin/dbutil/dump.c:1388
#, c-format
msgid "while opening %s for writing"
msgstr "beim Öffnen von %s zum Schreiben"

#: ../../src/kadmin/dbutil/dump.c:1403
msgid "while reading update log header"
msgstr "beim Lesen der Aktualisierungsprotokollkopfzeilen"

#: ../../src/kadmin/dbutil/dump.c:1418 ../../src/kadmin/dbutil/dump.c:1425
#, c-format
msgid "performing %s dump"
msgstr "Auszug von %s wird durchgeführt"

#: ../../src/kadmin/dbutil/dump.c:1455
#, c-format
msgid "%s: error processing line %d of %s\n"
msgstr "%s: Fehler beim Verarbeiten von Zeile %d von %s\n"

#: ../../src/kadmin/dbutil/dump.c:1507
msgid "while parsing options"
msgstr "beim Auswerten der Optionen"

#: ../../src/kadmin/dbutil/dump.c:1522
#, c-format
msgid "while opening %s"
msgstr "beim Öffnen von %s"

#: ../../src/kadmin/dbutil/dump.c:1527 ../../src/kadmin/dbutil/dump.c:1626
msgid "standard input"
msgstr "Standardeingabe"

#: ../../src/kadmin/dbutil/dump.c:1532
#, c-format
msgid "%s: can't read dump header in %s\n"
msgstr "%s: Kopfzeilen des Auszugs in %s können nicht gelesen werden.\n"

#: ../../src/kadmin/dbutil/dump.c:1540 ../../src/kadmin/dbutil/dump.c:1557
#, c-format
msgid "%s: dump header bad in %s\n"
msgstr "%s: falsche Kopfzeilen des Auszugs in %s\n"

#: ../../src/kadmin/dbutil/dump.c:1566
#, c-format
msgid "Could not open iprop ulog\n"
msgstr "Iprop-Ulog kann nicht geöffnet werden.\n"

#: ../../src/kadmin/dbutil/dump.c:1571
#, c-format
msgid "%s: dump version %s can only be loaded with the -update flag\n"
msgstr ""
"%s: Die Auszugsversion %s kann nur mit dem Schalter -update geladen werden.\n"

#: ../../src/kadmin/dbutil/dump.c:1580 ../../src/kadmin/dbutil/dump.c:1585
msgid "computing parameters for database"
msgstr "Parameter für die Datenbank werden berechnet."

#: ../../src/kadmin/dbutil/dump.c:1591
msgid "while creating database"
msgstr "beim Erstellen der Datenbank"

#: ../../src/kadmin/dbutil/dump.c:1600
msgid "while opening database"
msgstr "beim Öffnen der Datenbank"

#: ../../src/kadmin/dbutil/dump.c:1610
msgid "while permanently locking database"
msgstr "beim dauerhaften Sperren der Datenbank"

#: ../../src/kadmin/dbutil/dump.c:1628
#, c-format
msgid "%s: %s restore failed\n"
msgstr "%s: Wiederherstellen von %s fehlgeschlagen\n"

#: ../../src/kadmin/dbutil/dump.c:1633
msgid "while unlocking database"
msgstr "beim Aufheben der Datenbanksperre"

#: ../../src/kadmin/dbutil/dump.c:1643 ../../src/kadmin/dbutil/dump.c:1662
msgid "while reinitializing update log"
msgstr "beim erneuten Initialisieren des Aktualisierungsprotokolls"

#: ../../src/kadmin/dbutil/dump.c:1653
msgid "while making newly loaded database live"
msgstr "beim Aktivieren der neu geladenen Datenbank"

#: ../../src/kadmin/dbutil/dump.c:1669
msgid "while writing update log header"
msgstr "beim Schreiben der Aktualisierungsprotokollkopfzeilen"

#: ../../src/kadmin/dbutil/dump.c:1683
#, c-format
msgid "while deleting bad database %s"
msgstr "beim Löschen der falschen Datenbank %s"

#: ../../src/kadmin/dbutil/kadm5_create.c:84
msgid "while looking up the Kerberos configuration"
msgstr "beim Nachschlagen der Kerberos-Konfiguration"

#: ../../src/kadmin/dbutil/kadm5_create.c:111
msgid "while initializing the Kerberos admin interface"
msgstr "beim Initialisieren der Kerberos-Administrationsoberfläche"

#: ../../src/kadmin/dbutil/kadm5_create.c:169
#, c-format
msgid "getaddrinfo(%s): Cannot determine canonical hostname.\n"
msgstr ""
"getaddrinfo(%s): Die Normalform des Rechnernamens kann nicht bestimmt "
"werden.\n"

#: ../../src/kadmin/dbutil/kadm5_create.c:190
#: ../../src/kadmin/dbutil/kadm5_create.c:196
#, c-format
msgid "Out of memory\n"
msgstr "Speicherplatz reicht nicht aus.\n"

#: ../../src/kadmin/dbutil/kadm5_create.c:270
msgid "while appending realm to principal"
msgstr "beim Anhängen des Realms an den Principal"

#: ../../src/kadmin/dbutil/kadm5_create.c:275
msgid "while parsing admin principal name"
msgstr "beim Auswerten des Principal-Namens des Administrators"

#: ../../src/kadmin/dbutil/kadm5_create.c:286
#, c-format
msgid "while creating principal %s"
msgstr "beim Erstellen des Principals %s"

#: ../../src/kadmin/dbutil/kdb5_create.c:175
#: ../../src/kadmin/dbutil/kdb5_util.c:241
#: ../../src/kadmin/dbutil/kdb5_util.c:248
msgid "while parsing command arguments\n"
msgstr "beim Auswerten der Befehlsargumente\n"

#: ../../src/kadmin/dbutil/kdb5_create.c:198
#, c-format
msgid "Loading random data\n"
msgstr "Zufällige Daten werden geladen.\n"

#: ../../src/kadmin/dbutil/kdb5_create.c:201
msgid "Loading random data"
msgstr "Zufällige Daten werden geladen."

#: ../../src/kadmin/dbutil/kdb5_create.c:211
#: ../../src/kadmin/dbutil/kdb5_mkey.c:242
#: ../../src/kadmin/dbutil/kdb5_mkey.c:435
#: ../../src/kadmin/dbutil/kdb5_mkey.c:591
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1149
#: ../../src/kadmin/dbutil/kdb5_util.c:423
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:606
msgid "while setting up master key name"
msgstr "beim Einrichten des Hauptschlüsselnamens"

#: ../../src/kadmin/dbutil/kdb5_create.c:222
#, c-format
msgid ""
"Initializing database '%s' for realm '%s',\n"
"master key name '%s'\n"
msgstr ""
"Datenbank »%s« für Realm »%s« wird initialisiert,\n"
"Hauptschlüsselname »%s«\n"

#: ../../src/kadmin/dbutil/kdb5_create.c:227
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:516
#, c-format
msgid "You will be prompted for the database Master Password.\n"
msgstr "Sie werden nach dem Master-Passwort der Datenbank gefragt.\n"

#: ../../src/kadmin/dbutil/kdb5_create.c:228
#: ../../src/kadmin/dbutil/kdb5_mkey.c:260
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:517
#, c-format
msgid "It is important that you NOT FORGET this password.\n"
msgstr "Es ist wichtig, dass Sie dieses Passwort NICHT VERGESSEN.\n"

#: ../../src/kadmin/dbutil/kdb5_create.c:234
#: ../../src/kadmin/dbutil/kdb5_mkey.c:266
msgid "while creating new master key"
msgstr "beim Erstellen des neuen Hauptschlüssels"

#: ../../src/kadmin/dbutil/kdb5_create.c:242
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:527
msgid "while reading master key from keyboard"
msgstr "beim Lesen des Hauptschlüssels von der Tastatur"

#: ../../src/kadmin/dbutil/kdb5_create.c:252
#: ../../src/kadmin/dbutil/kdb5_mkey.c:285
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:618
msgid "while calculating master key salt"
msgstr "beim Berechnen des Hauptschlüssel-Salts"

#: ../../src/kadmin/dbutil/kdb5_create.c:260
#: ../../src/kadmin/dbutil/kdb5_mkey.c:294
#: ../../src/kadmin/dbutil/kdb5_util.c:465
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:630
msgid "while transforming master key from password"
msgstr "beim Umwandeln des Hauptschlüssels vom Passwort"

#: ../../src/kadmin/dbutil/kdb5_create.c:270
msgid "while initializing random key generator"
msgstr "beim Initialisieren des Zufallsschlüsselgenerators"

#: ../../src/kadmin/dbutil/kdb5_create.c:275
#, c-format
msgid "while creating database '%s'"
msgstr "beim Erstellen der Datenbank »%s«"

#: ../../src/kadmin/dbutil/kdb5_create.c:293
msgid "while creating update log"
msgstr "beim Erstellen des Aktualisierungsprotokolls"

#: ../../src/kadmin/dbutil/kdb5_create.c:304
msgid "while initializing update log"
msgstr "beim Initialisieren des Aktualisierungsprotokolls"

#: ../../src/kadmin/dbutil/kdb5_create.c:320
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:642
msgid "while adding entries to the database"
msgstr "beim Hinzufügen von Einträgen in die Datenbank"

#: ../../src/kadmin/dbutil/kdb5_create.c:348
#: ../../src/kadmin/dbutil/kdb5_mkey.c:339
#: ../../src/kadmin/dbutil/kdb5_stash.c:133
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:667
msgid "while storing key"
msgstr "beim Speichern des Schlüssels"

#: ../../src/kadmin/dbutil/kdb5_create.c:349
#: ../../src/kadmin/dbutil/kdb5_mkey.c:340
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:668
#, c-format
msgid "Warning: couldn't stash master key.\n"
msgstr "Warnung: Hauptschlüssel kann nicht gelagert werden.\n"

#: ../../src/kadmin/dbutil/kdb5_destroy.c:57
msgid "while initializing krb5_context"
msgstr "beim Initialisieren von »krb5_context«"

#: ../../src/kadmin/dbutil/kdb5_destroy.c:63
#: ../../src/kadmin/dbutil/kdb5_util.c:259
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:291
msgid "while setting default realm name"
msgstr "beim Einstellen des Standard-Realm-Namens"

#: ../../src/kadmin/dbutil/kdb5_destroy.c:83
#, c-format
msgid "Deleting KDC database stored in '%s', are you sure?\n"
msgstr ""
"Die in »%s« gespeicherte KDC-Datenbank wird gelöscht. Sind Sie sicher?\n"

#: ../../src/kadmin/dbutil/kdb5_destroy.c:85
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1166
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:360
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1482
#, c-format
msgid "(type 'yes' to confirm)? "
msgstr "(Geben Sie als Bestätigung »yes« ein)? "

#: ../../src/kadmin/dbutil/kdb5_destroy.c:92
#, c-format
msgid "OK, deleting database '%s'...\n"
msgstr "OK, Datenbank »%s« wird gelöscht …\n"

#: ../../src/kadmin/dbutil/kdb5_destroy.c:97
#, c-format
msgid "deleting database '%s'"
msgstr "Datenbank »%s« wird gelöscht."

#: ../../src/kadmin/dbutil/kdb5_destroy.c:106
#, c-format
msgid "** Database '%s' destroyed.\n"
msgstr "** Datenbank »%s« vernichtet\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:218
#, c-format
msgid "%s is an invalid enctype"
msgstr "%s ist ein ungültiger Verschlüsselungstyp"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:250
#: ../../src/kadmin/dbutil/kdb5_mkey.c:443
#: ../../src/kadmin/dbutil/kdb5_mkey.c:599
#: ../../src/kadmin/dbutil/kdb5_mkey.c:986
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1157
#, c-format
msgid "while getting master key principal %s"
msgstr "beim Holen des Hauptschlüssels von Principal %s"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:256
#, c-format
msgid "Creating new master key for master key principal '%s'\n"
msgstr ""
"Es wird ein neuer Hauptschlüssel für den Hauptschlüssel-Principal »%s« "
"erstellt.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:259
#, c-format
msgid "You will be prompted for a new database Master Password.\n"
msgstr "Sie werden nach einem neuen Datenbank-Master-Passwort gefragt.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:275
msgid "while reading new master key from keyboard"
msgstr "beim Lesen des neuen Hauptschlüssels von der Tastatur"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:304
msgid "adding new master key to master principal"
msgstr "dem Haupt-Principal wird ein neuer Hauptschlüssel hinzugefügt"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:310
#: ../../src/kadmin/dbutil/kdb5_mkey.c:402
#: ../../src/kadmin/dbutil/kdb5_mkey.c:843
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1356
msgid "while getting current time"
msgstr "beim Holen der aktuellen Zeit"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:317
#: ../../src/kadmin/dbutil/kdb5_mkey.c:544
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1363
msgid "while updating the master key principal modification time"
msgstr "beim Aktulisieren der Änderungszeit des Hauptschlüssel-Principals"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:325
#: ../../src/kadmin/dbutil/kdb5_mkey.c:553
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1374
msgid "while adding master key entry to the database"
msgstr "beim Hinzufügen des Hauptschlüsseleintrags zur Datenbank"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:383
msgid "0 is an invalid KVNO value"
msgstr "0 ist kein gültiger KVNO-Wert"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:394
#, c-format
msgid "%d is an invalid KVNO value"
msgstr "%d ist kein gültiger KVNO-Wert"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:410
#, c-format
msgid "could not parse date-time string '%s'"
msgstr "»date-time«-Zeichenkette »%s« konnte nicht ausgewertet werden"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:452
msgid "while looking up active version of master key"
msgstr "beim Nachschlagen der aktiven Version des Hauptschlüssels"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:491
msgid "while adding new master key"
msgstr "beim Hinzufügen eines neuen Hauptschlüssels"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:529
msgid "there must be one master key currently active"
msgstr "ein Hauptschlüssel muss derzeit aktiv sein"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:537
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1342
msgid "while updating actkvno data for master principal entry"
msgstr "beim Aktualisieren der Actkvno-Daten für den Haupt-Principal-Eintrag"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:581
#: ../../src/kadmin/dbutil/kdb5_mkey.c:948
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1116
msgid "master keylist not initialized"
msgstr "Hauptschlüsselliste ist nicht initialisiert"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:607
#: ../../src/kadmin/dbutil/kdb5_mkey.c:994
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1254
msgid "while looking up active kvno list"
msgstr "beim Nachschlagen der Liste aktiver KVNOs"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:615
#: ../../src/kadmin/dbutil/kdb5_mkey.c:1002
msgid "while looking up active master key"
msgstr "beim Nachschlagen des aktiven Hauptschlüssels"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:627
msgid "while getting enctype description"
msgstr "beim Holen des Verschlüsselungsbeschreibung"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:644
#, c-format
msgid "KVNO: %d, Enctype: %s, Active on: %s *\n"
msgstr "KVNO: %d, Verschlüsselungstyp: %s, aktiviert auf: %s *\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:649
#, c-format
msgid "KVNO: %d, Enctype: %s, Active on: %s\n"
msgstr "KVNO: %d, Verschlüsselungstyp: %s, aktiviert auf: %s\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:653
#, c-format
msgid "KVNO: %d, Enctype: %s, No activate time set\n"
msgstr "KVNO: %d, Verschlüsselungstyp: %s, keine Aktivierungszeit gesetzt\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:658
msgid "asprintf could not allocate enough memory to hold output"
msgstr ""
"Asprintf konnte nicht genug Speicher reservieren, um die Ausgabe "
"bereitzuhalten"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:793
msgid "getting string representation of principal name"
msgstr "Principal-Name wird im Klartext geholt"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:817
#, c-format
msgid "determining master key used for principal '%s'"
msgstr "Hauptschlüssel, der für Principal »%s« benutzt wird, wird bestimmt"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:823
#, c-format
msgid "would skip:   %s\n"
msgstr "würde übersprungen:   %s\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:825
#, c-format
msgid "skipping: %s\n"
msgstr "wird übersprungen: %s\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:831
#, c-format
msgid "would update: %s\n"
msgstr "würde aktualisiert: %s\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:835
#, c-format
msgid "updating: %s\n"
msgstr "wird aktualisiert: %s\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:839
#, c-format
msgid "error re-encrypting key for principal '%s'"
msgstr "Fehler beim erneuten Verschlüsseln des Schlüssels für Principal »%s«"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:850
#, c-format
msgid "while updating principal '%s' modification time"
msgstr "beim Aktualisieren der Änderungszeit von Principal »%s«"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:857
#, c-format
msgid "while updating principal '%s' key data in the database"
msgstr ""
"beim Aktualisieren der Schlüsseldaten von Principal »%s« in der Datenbank"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:889
#, c-format
msgid ""
"\n"
"(type 'yes' to confirm)? "
msgstr ""
"\n"
"(Geben Sie als Bestätigung »yes« ein) "

#: ../../src/kadmin/dbutil/kdb5_mkey.c:942
msgid "while formatting master principal name"
msgstr "beim Formatieren des Haupt-Principal-Namens"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:959
#, c-format
msgid "converting glob pattern '%s' to regular expression"
msgstr "Platzhalter »%s« wird in einen regulären Ausdruck umgewandelt"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:977
#, c-format
msgid "error compiling converted regexp '%s'"
msgstr "Fehler beim Kompilieren des umgewandelten regulären Ausdrucks »%s«"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1010
#, c-format
msgid "Re-encrypt all keys not using master key vno %u?"
msgstr ""
"Sollen alle Schlüssel neu verschlüsselt werden, die nicht die Hauptschlüssel-"
"VNO %u verwenden?"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1012
#, c-format
msgid "OK, doing nothing.\n"
msgstr "Ok, es wird nichts getan.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1018
#, c-format
msgid "Principals whose keys WOULD BE re-encrypted to master key vno %u:\n"
msgstr ""
"Principals, deren Schlüssel mit dem Hauptschlüssel VNO %u neu verschlüsselt "
"WÜRDEN:\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1021
#, c-format
msgid ""
"Principals whose keys are being re-encrypted to master key vno %u if "
"necessary:\n"
msgstr ""
"Principals, deren Schlüssel mit dem Hauptschlüssel VNO %u neu verschlüsselt "
"werden, falls nötig:\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1037
msgid "trying to process principal database"
msgstr "es wird versucht, die Principal-Datenbank zu verarbeiten"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1042
#, c-format
msgid "%u principals processed: %u would be updated, %u already current\n"
msgstr ""
"%u Principals verarbeitet: %u würden aktualisiert, %u bereits aktuell\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1046
#, c-format
msgid "%u principals processed: %u updated, %u already current\n"
msgstr "%u Principals verarbeitet: %u aktualisiert, %u bereits aktuell\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1164
#, c-format
msgid ""
"Will purge all unused master keys stored in the '%s' principal, are you "
"sure?\n"
msgstr ""
"Sind Sie sicher, dass alle nicht verwendeten Hauptschlüssel, die für "
"Principal »%s« gespeichert sind, vollständig entfernt werden sollen?\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1175
#, c-format
msgid "OK, purging unused master keys from '%s'...\n"
msgstr ""
"Ok, die nicht verwendeten Hauptschlüssel von »%s« werden vollständig "
"entfernt …\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1183
#, c-format
msgid "There is only one master key which can not be purged.\n"
msgstr ""
"Es gibt nur einen einzigen Hauptschlüssel, der nicht vollständig entfernt "
"werden kann.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1192
msgid "while allocating args.kvnos"
msgstr "beim Reservieren von »args.kvnos«"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1208
msgid "while finding master keys in use"
msgstr "bei der Suche nach den gerade verwendeten Hauptschlüsseln"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1217
#, c-format
msgid "Would purge the following master key(s) from %s:\n"
msgstr ""
"Der/Die folgende(n) Hauptschlüssel würden/würde von %s vollständig "
"entfernt:\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1220
#, c-format
msgid "Purging the following master key(s) from %s:\n"
msgstr ""
"Der/Die folgende(n) Hauptschlüssel werden/wird von %s vollständig entfernt:\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1232
msgid "master key stash file needs updating, command aborting"
msgstr ""
"Ablagedatei des Hauptschlüssels erfordert Aktualisierung, Befehl abgebrochen"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1238
#, c-format
msgid "KVNO: %d\n"
msgstr "KVNO: %d\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1243
#, c-format
msgid "All keys in use, nothing purged.\n"
msgstr "Alle Schlüssel sind in Gebrauch, keiner wurde vollständig entfernt.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1248
#, c-format
msgid "%d key(s) would be purged.\n"
msgstr "%d Schlüssel würde(n) vollständig entfernt.\n"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1261
msgid "while looking up mkey aux data list"
msgstr "beim Nachschlagen der Mkey-Aux-Datenliste"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1269
msgid "while allocating key_data"
msgstr "beim Reservieren von »key_data«"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1350
msgid "while updating mkey_aux data for master principal entry"
msgstr "beim Aktualisieren der Mkey-Aux-Daten für den Haupt-Principal-Eintrag"

#: ../../src/kadmin/dbutil/kdb5_mkey.c:1378
#, c-format
msgid "%d key(s) purged.\n"
msgstr "%d Schlüssel vollständig entfernt\n"

#: ../../src/kadmin/dbutil/kdb5_stash.c:97
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:538
#, c-format
msgid "while setting up enctype %d"
msgstr "beim Einrichten des Verschlüsselungstyps %d"

#: ../../src/kadmin/dbutil/kdb5_stash.c:123
msgid "while getting master key list"
msgstr "beim Holen der Hauptschlüsselliste"

#: ../../src/kadmin/dbutil/kdb5_stash.c:127
#, c-format
msgid "Using existing stashed keys to update stash file.\n"
msgstr ""
"Zur Aktualisierung der Ablagedatei werden existierende gelagert Schlüssel "
"verwendet.\n"

#: ../../src/kadmin/dbutil/kdb5_util.c:80
#, c-format
msgid ""
"Usage: kdb5_util [-x db_args]* [-r realm] [-d dbname] [-k mkeytype] [-M "
"mkeyname]\n"
"\t        [-kv mkeyVNO] [-sf stashfilename] [-m] cmd [cmd_options]\n"
"\tcreate  [-s]\n"
"\tdestroy [-f]\n"
"\tstash   [-f keyfile]\n"
"\tdump    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose]\n"
"\t        [-mkey_convert] [-new_mkey_file mkey_file]\n"
"\t        [-rev] [-recurse] [filename [princs...]]\n"
"\tload    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose] [-update] filename\n"
"\tark     [-e etype_list] principal\n"
"\tadd_mkey [-e etype] [-s]\n"
"\tuse_mkey kvno [time]\n"
"\tlist_mkeys\n"
msgstr ""
"Aufruf: kdb5_util [-x Datenbankargumente]* [-r Realm] [-d Datenbankname] [-k "
"Mkeytype] [-M Mkeyname]\n"
"\t        [-kv MkeyVNO] [-sf Ablagedateiname] [-m] Befehl [Befehlsoptionen]\n"
"\tcreate  [-s]\n"
"\tdestroy [-f]\n"
"\tstash   [-f Schlüsseldatei]\n"
"\tdump    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose]\n"
"\t        [-mkey_convert] [-new_mkey_file mkey-Datei]\n"
"\t        [-rev] [-recurse] [Dateiname [Principals …]]\n"
"\tload    [-old|-ov|-b6|-b7|-r13|-r18] [-verbose] [-update] Dateiname\n"
"\tark     [-e Etype-Liste] Principal\n"
"\tadd_mkey [-e Etype] [-s]\n"
"\tuse_mkey kvno [Zeit]\n"
"\tlist_mkeys\n"

#: ../../src/kadmin/dbutil/kdb5_util.c:98
#, c-format
msgid ""
"\tupdate_princ_encryption [-f] [-n] [-v] [princ-pattern]\n"
"\tpurge_mkeys [-f] [-n] [-v]\n"
"\n"
"where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""
"\tupdate_princ_encryption [-f] [-n] [-v] [Principal-Muster]\n"
"\tpurge_mkeys [-f] [-n] [-v]\n"
"\n"
"dabei sind\n"
"\t[-x Datenbankargumente]* - eine beliebige Anzahl datenbankspezifischer "
"Argumente.\n"
"\t\t\tWelche Argumente unterstützt werden, finden Sie in der Dokumentation "
"der jeweiligen Datenbank.\n"

#: ../../src/kadmin/dbutil/kdb5_util.c:211
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:260
msgid "while initializing Kerberos code"
msgstr "beim Initialisieren von Kerberos-Code"

#: ../../src/kadmin/dbutil/kdb5_util.c:217
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:267
msgid "while creating sub-command arguments"
msgstr "beim Erstellen von Unterbefehlsargumenten"

#: ../../src/kadmin/dbutil/kdb5_util.c:235
msgid "while parsing command arguments"
msgstr "beim Auswerten von Befehlsargumenten"

#: ../../src/kadmin/dbutil/kdb5_util.c:264
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:298
#, c-format
msgid ": %s is an invalid enctype"
msgstr ": %s ist kein gültiger Verschlüsselungstyp"

#: ../../src/kadmin/dbutil/kdb5_util.c:272
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:307
#, c-format
msgid ": %s is an invalid mkeyVNO"
msgstr ": %s ist kein gültiger MkeyVNO"

# FIXME s/retreiving/retrieving/
#: ../../src/kadmin/dbutil/kdb5_util.c:317
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:431
msgid "while retreiving configuration parameters"
msgstr "beim Abfragen der Konfigurationsparameter"

#: ../../src/kadmin/dbutil/kdb5_util.c:368
msgid "Too few arguments"
msgstr "zu wenige Argumente"

#: ../../src/kadmin/dbutil/kdb5_util.c:369
#, c-format
msgid "Usage: %s dbpathname realmname"
msgstr "Aufruf: %s Datenbankpfadname Realm-Name"

#: ../../src/kadmin/dbutil/kdb5_util.c:375
msgid "while closing previous database"
msgstr "beim Schließen der vorherigen Datenbank"

#: ../../src/kadmin/dbutil/kdb5_util.c:412
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:877
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1497
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:564
msgid "while initializing database"
msgstr "beim Initialisieren der Datenbank"

#: ../../src/kadmin/dbutil/kdb5_util.c:429
msgid "while retrieving master entry"
msgstr "beim Abfragen des Haupteintrags"

#: ../../src/kadmin/dbutil/kdb5_util.c:448
msgid "while calculated master key salt"
msgstr "beim Berechnen des Hauptschlüssel-Salts"

#: ../../src/kadmin/dbutil/kdb5_util.c:480
msgid "Warning: proceeding without master key"
msgstr "Warnung: Es wird ohne Hauptschlüssel fortgefahren"

#: ../../src/kadmin/dbutil/kdb5_util.c:498
msgid "while seeding random number generator"
msgstr "beim Erzeugen des Startwerts des Zufallszahlengenerators"

#: ../../src/kadmin/dbutil/kdb5_util.c:508
#, c-format
msgid "%s: Could not map log\n"
msgstr "%s: Protokolldatei konnte nicht abgebildet werden\n"

#: ../../src/kadmin/dbutil/kdb5_util.c:535
msgid "while closing database"
msgstr "beim Schließen der Datenbank"

#: ../../src/kadmin/dbutil/kdb5_util.c:582
#, c-format
msgid "while fetching principal %s"
msgstr "beim Abrufen von Principal %s"

#: ../../src/kadmin/dbutil/kdb5_util.c:605
msgid "while finding mkey"
msgstr "beim Suchen nach Mkey"

#: ../../src/kadmin/dbutil/kdb5_util.c:630
msgid "while setting changetime"
msgstr "beim Setzen der Änderungszeit der Datei"

#: ../../src/kadmin/dbutil/kdb5_util.c:638
#, c-format
msgid "while saving principal %s"
msgstr "beim Speichern von Principal %s"

#: ../../src/kadmin/dbutil/kdb5_util.c:642
#, c-format
msgid "%s changed\n"
msgstr "%s geändert\n"

#: ../../src/kadmin/ktutil/ktutil.c:73
#, c-format
msgid "%s: invalid arguments\n"
msgstr "%s: ungültige Argumente\n"

#: ../../src/kadmin/ktutil/ktutil.c:78
msgid "while freeing ktlist"
msgstr "beim Freigeben von »ktlist«"

#: ../../src/kadmin/ktutil/ktutil.c:89
#, c-format
msgid "%s: must specify keytab to read\n"
msgstr ""
"%s: Die Schlüsseltabelle, die gelesen werden soll, muss angegeben werden.\n"

#: ../../src/kadmin/ktutil/ktutil.c:94
#, c-format
msgid "while reading keytab \"%s\""
msgstr "beim Lesen der Schlüsseltabelle »%s«"

#: ../../src/kadmin/ktutil/ktutil.c:104
#, c-format
msgid "%s: must specify the srvtab to read\n"
msgstr "%s: Die zu lesende Dienstschlüsseltabelle muss angegeben werden.\n"

#: ../../src/kadmin/ktutil/ktutil.c:109
#, c-format
msgid "while reading srvtab \"%s\""
msgstr "beim Lesen der Dienstschlüsseltabelle »%s«"

#: ../../src/kadmin/ktutil/ktutil.c:119
#, c-format
msgid "%s: must specify keytab to write\n"
msgstr "%s: Die zu schreibende Schlüsseltabelle muss angegeben werden.\n"

#: ../../src/kadmin/ktutil/ktutil.c:124
#, c-format
msgid "while writing keytab \"%s\""
msgstr "beim Schreiben der Schlüsseltabelle »%s«"

#: ../../src/kadmin/ktutil/ktutil.c:131
#, c-format
msgid "%s: writing srvtabs is no longer supported\n"
msgstr ""
"%s: Schreiben der Dienstschlüsseltabelle wird nicht länger unterstützt\n"

#: ../../src/kadmin/ktutil/ktutil.c:169
#, c-format
msgid "usage: %s (-key | -password) -p principal -k kvno -e enctype\n"
msgstr ""
"Aufruf: %s (-key | -password) -p Principal -k KVNO -e Verschlüsselungstyp\n"

#: ../../src/kadmin/ktutil/ktutil.c:176
msgid "while adding new entry"
msgstr "beim Hinzufügen eines neuen Eintrags"

#: ../../src/kadmin/ktutil/ktutil.c:186
#, c-format
msgid "%s: must specify entry to delete\n"
msgstr "%s: zu löschender Eintrag muss angegeben werden\n"

#: ../../src/kadmin/ktutil/ktutil.c:191
#, c-format
msgid "while deleting entry %d"
msgstr "beim Löschen von Eintrag %d"

#: ../../src/kadmin/ktutil/ktutil.c:219
#, c-format
msgid "%s: usage: %s [-t] [-k] [-e]\n"
msgstr "%s: Aufruf: %s [-t] [-k] [-e]\n"

#: ../../src/kadmin/ktutil/ktutil.c:259
msgid "While converting enctype to string"
msgstr "beim Umwandeln des Verschlüsselungstyps in eine Zeichenkette"

#: ../../src/kadmin/ktutil/ktutil_funcs.c:162
#, c-format
msgid "Password for %.1000s"
msgstr "Passwort für %.1000s"

#: ../../src/kadmin/ktutil/ktutil_funcs.c:179
#, c-format
msgid "Key for %s (hex): "
msgstr "Schlüssel für %s (hexadezimal): "

#: ../../src/kadmin/ktutil/ktutil_funcs.c:191
#, c-format
msgid "addent: Error reading key.\n"
msgstr "addent: Fehler beim Lesen des Schlüssels\n"

#: ../../src/kadmin/ktutil/ktutil_funcs.c:206
#, c-format
msgid "addent: Illegal character in key.\n"
msgstr "addent: unerlaubtes Zeichen im Schlüssel\n"

#: ../../src/kadmin/server/ipropd_svc.c:48
#, c-format
msgid "Unauthorized request: %s, client=%s, service=%s, addr=%s"
msgstr "unberechtigte Anfrage: %s, Client=%s, Dienst=%s, Adresse=%s"

#: ../../src/kadmin/server/ipropd_svc.c:49
#: ../../src/kadmin/server/ipropd_svc.c:212
#, c-format
msgid "Request: %s, %s, %s, client=%s, service=%s, addr=%s"
msgstr "Anfrage: %s, %s, %s, Client=%s, Dienst=%s, Adresse=%s"

#: ../../src/kadmin/server/ipropd_svc.c:146
#: ../../src/kadmin/server/ipropd_svc.c:271
#, c-format
msgid "%s: server handle is NULL"
msgstr "%s: Server-Identifikator ist NULL"

#: ../../src/kadmin/server/ipropd_svc.c:156
#: ../../src/kadmin/server/ipropd_svc.c:284
#, c-format
msgid "%s: setup_gss_names failed"
msgstr "%s: setup_gss_names fehlgeschlagen"

#: ../../src/kadmin/server/ipropd_svc.c:166
#: ../../src/kadmin/server/ipropd_svc.c:295
#, c-format
msgid "%s: out of memory recording principal names"
msgstr "%s: Speicher reicht nicht zur Aufzeichnung der Principal-Namen aus"

#: ../../src/kadmin/server/ipropd_svc.c:195
#, c-format
msgid "%s; Incoming SerialNo=%lu; Outgoing SerialNo=%lu"
msgstr "%s; eingehende Seriennummer=%lu; ausgehende Seriennummer=%lu"

#: ../../src/kadmin/server/ipropd_svc.c:201
#, c-format
msgid "%s; Incoming SerialNo=%lu; Outgoing SerialNo=N/A"
msgstr "%s; eingehende Seriennummer=%lu; ausgehende Seriennummer=N/A"

#: ../../src/kadmin/server/ipropd_svc.c:320
#, c-format
msgid "%s: getclhoststr failed"
msgstr "%s: getclhoststr fehlgeschlagen"

#: ../../src/kadmin/server/ipropd_svc.c:342
#, c-format
msgid "%s: cannot construct kdb5 util dump string too long; out of memory"
msgstr ""
"Ausgabenzeichenkette des KDB5-Hilfswerkzeugs nicht konstruierbar, da zu "
"lang; Speicher reicht nicht aus.%s: Die Ausgabezeichenkette des KDB5-"
"Hilfswerkzeugs kann nicht erstellt werden, weil sie zu lang ist. Der "
"Speicherplatz reicht nicht aus."

#: ../../src/kadmin/server/ipropd_svc.c:362
#, c-format
msgid "%s: fork failed: %s"
msgstr "%s: Verzweigen fehlgeschlagen: %s"

#: ../../src/kadmin/server/ipropd_svc.c:374
#, c-format
msgid "%s: popen failed: %s"
msgstr "%s: popen fehlgeschlagen: %s"

#: ../../src/kadmin/server/ipropd_svc.c:388
#, c-format
msgid "%s: pclose(popen) failed: %s"
msgstr "%s: pclose(popen) fehlgeschlagen: %s"

#: ../../src/kadmin/server/ipropd_svc.c:405
#, c-format
msgid "%s: exec failed: %s"
msgstr "%s: exec fehlgeschlagen: %s"

#: ../../src/kadmin/server/ipropd_svc.c:421
#, c-format
msgid "Request: %s, spawned resync process %d, client=%s, service=%s, addr=%s"
msgstr ""
"Anfrage: %s, hervorgebrachter Neusynchronisationsprozess %d, Client=%s, "
"Dienst=%s, Adresse=%s"

#: ../../src/kadmin/server/ipropd_svc.c:485
#: ../../src/kadmin/server/kadm_rpc_svc.c:275
#, c-format
msgid "check_rpcsec_auth: failed inquire_context, stat=%u"
msgstr "check_rpcsec_auth: inquire_context fehlgeschlagen, Stat=%u"

#: ../../src/kadmin/server/ipropd_svc.c:515
#: ../../src/kadmin/server/kadm_rpc_svc.c:304
#, c-format
msgid "bad service principal %.*s%s"
msgstr "falscher Dienst-Principal %.*s%s"

#: ../../src/kadmin/server/ipropd_svc.c:538
#, c-format
msgid "authentication attempt failed: %s, RPC authentication flavor %d"
msgstr ""
"Authentifizierungsversuche gescheitert: %s, PRC-Authentifizierungsvariante %d"

#: ../../src/kadmin/server/ipropd_svc.c:572
#, c-format
msgid "RPC unknown request: %d (%s)"
msgstr "unbekannte PRC-Anfrage: %d (%s)"

#: ../../src/kadmin/server/ipropd_svc.c:580
#, c-format
msgid "RPC svc_getargs failed (%s)"
msgstr "RPC-»svc_getargs« fehlgeschlagen (%s)"

#: ../../src/kadmin/server/ipropd_svc.c:590
#, c-format
msgid "RPC svc_sendreply failed (%s)"
msgstr "RPC-»svc_sendreply« fehlgeschlagen (%s)"

#: ../../src/kadmin/server/ipropd_svc.c:596
#, c-format
msgid "RPC svc_freeargs failed (%s)"
msgstr "RPC-»svc_freeargs« fehlgeschlagen (%s)"

#: ../../src/kadmin/server/kadm_rpc_svc.c:325
#, c-format
msgid "gss_to_krb5_name: failed display_name status %d"
msgstr "gss_to_krb5_name: display_name fehlgeschlagen, Status %d"

#: ../../src/kadmin/server/ovsec_kadmd.c:86
#, c-format
msgid ""
"Usage: kadmind [-x db_args]* [-r realm] [-m] [-nofork] [-port port-number]\n"
"\t\t[-proponly] [-p path-to-kdb5_util] [-F dump-file]\n"
"\t\t[-K path-to-kprop] [-P pid_file]\n"
"\n"
"where,\n"
"\t[-x db_args]* - any number of database specific arguments.\n"
"\t\t\tLook at each database documentation for supported arguments\n"
msgstr ""
"Aufruf: kadmind [-x Datenbankargumente]* [-r Realm] [-m] [-nofork]\n"
"\t\t[-port Portummer] [-p Pfad_zum_KDB5-Hilfswerkzeug] [-F Auszugsdatei]\n"
"\t\t[-K Pfad_zu_Kprop] [-P PID-Datei]\n"
"\n"
"dabei sind\n"
"\t[-x Datenbankargumente]* - eine beliebige Anzahl datenbankspezifischer "
"Argumente.\n"
"\t\t\tWelche Argumente unterstützt werden, finden Sie in der Dokumentation "
"der jeweiligen Datenbank.\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:111
#, c-format
msgid "%s: %s while %s, aborting\n"
msgstr "%s: %s bei %s, wird abgebrochen\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:113
#, c-format
msgid "%s while %s, aborting\n"
msgstr "%s bei %s, wird abgebrochen\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:115
#, c-format
msgid "%s: %s, aborting\n"
msgstr "%s: %s, wird abgebrochen\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:116
#, c-format
msgid "%s, aborting"
msgstr "%s, wird abgebrochen"

#: ../../src/kadmin/server/ovsec_kadmd.c:282
#, c-format
msgid ""
"WARNING! Forged/garbled request: %s, claimed client = %.*s%s, server = %.*s"
"%s, addr = %s"
msgstr ""
"WARNUNG! Gefälschte/verstümmelte Anfrage: %s, geforderter Client = %.*s%s, "
"Server = %.*s%s, Adresse = %s"

#: ../../src/kadmin/server/ovsec_kadmd.c:288
#, c-format
msgid ""
"WARNING! Forged/garbled request: %d, claimed client = %.*s%s, server = %.*s"
"%s, addr = %s"
msgstr ""
"WARNUNG! Gefälschte/verstümmelte Anfrage: %d,   Client = %.*s%s, Server = "
"%.*s%s, Adresse = %s"

#: ../../src/kadmin/server/ovsec_kadmd.c:302
#, c-format
msgid "Miscellaneous RPC error: %s, %s"
msgstr "sonstiger PRC-Fehler: %s, %s"

#: ../../src/kadmin/server/ovsec_kadmd.c:318
#, c-format
msgid "%s Cannot decode status %d"
msgstr "%s: Status %d kann nicht dekodiert werden"

#: ../../src/kadmin/server/ovsec_kadmd.c:336
#, c-format
msgid "Authentication attempt failed: %s, GSS-API error strings are:"
msgstr "Authentifizierungsversuch fehlgeschlagen: %s, GSS-API-Fehlermeldungen:"

#: ../../src/kadmin/server/ovsec_kadmd.c:341
msgid "   GSS-API error strings complete."
msgstr "   GSS-API-Fehlermeldungen vollständig"

#: ../../src/kadmin/server/ovsec_kadmd.c:378
#, c-format
msgid "%s: cannot initialize. Not enough memory\n"
msgstr "%s: kann nicht initialisiert werden: Speicher reicht nicht aus.\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:445
#, c-format
msgid "%s: %s while initializing context, aborting\n"
msgstr "%s: %s beim Initialisieren des Kontextes, wird abgebrochen\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:456
msgid "initializing"
msgstr "wird initialisiert"

#: ../../src/kadmin/server/ovsec_kadmd.c:460
msgid "getting config parameters"
msgstr "beim Holen der Konfigurationsparameter"

#: ../../src/kadmin/server/ovsec_kadmd.c:462
msgid "Missing required realm configuration"
msgstr "erforderliche Realm-Konfiguration fehlt"

#: ../../src/kadmin/server/ovsec_kadmd.c:464
msgid "Missing required ACL file configuration"
msgstr "erforderliche ACL-Dateikonfiguration fehlt"

#: ../../src/kadmin/server/ovsec_kadmd.c:468
msgid "initializing network"
msgstr "Netzwerk wird initialisiert"

#: ../../src/kadmin/server/ovsec_kadmd.c:473
msgid "Cannot build GSSAPI auth names"
msgstr "GSS-API-Authentifizierungsnamen können nicht gebildet werden."

#: ../../src/kadmin/server/ovsec_kadmd.c:477
msgid "Cannot set up KDB keytab"
msgstr "Die KDB-Schlüsseltabelle kann nicht eingerichtet werden."

#: ../../src/kadmin/server/ovsec_kadmd.c:480
msgid "Cannot set GSSAPI authentication names"
msgstr "GSS-API-Authentifizierungsnamen können nicht gesetzt werden."

#: ../../src/kadmin/server/ovsec_kadmd.c:497
msgid "Cannot initialize GSSAPI service name"
msgstr "GSSAPI-Dienstname kann nicht initialisiert werden"

#: ../../src/kadmin/server/ovsec_kadmd.c:501
msgid "initializing ACL file"
msgstr "ACL-Datei wird initialisiert"

#: ../../src/kadmin/server/ovsec_kadmd.c:504
msgid "spawning daemon process"
msgstr "Daemon-Prozess wird erzeugt"

#: ../../src/kadmin/server/ovsec_kadmd.c:508
msgid "creating PID file"
msgstr "PID-Datei wird erstellt"

#: ../../src/kadmin/server/ovsec_kadmd.c:511
msgid "Seeding random number generator"
msgstr "Startwert des Zufallszahlengenerators wird erzeugt"

#: ../../src/kadmin/server/ovsec_kadmd.c:514
msgid "getting random seed"
msgstr "Zufallsstartwert wird geholt"

#: ../../src/kadmin/server/ovsec_kadmd.c:521
msgid "mapping update log"
msgstr "Aktualisierungsprotokoll wird abgebildet"

#: ../../src/kadmin/server/ovsec_kadmd.c:525
#, c-format
msgid "%s: create IPROP svc (PROG=%d, VERS=%d)\n"
msgstr "%s: IPROP-Dienst wird erstellt (PROG=%d, VERS=%d)\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:530
msgid "starting"
msgstr "startet"

#: ../../src/kadmin/server/ovsec_kadmd.c:532 ../../src/kdc/main.c:1061
#, c-format
msgid "%s: starting...\n"
msgstr "%s: startet …\n"

#: ../../src/kadmin/server/ovsec_kadmd.c:535
msgid "finished, exiting"
msgstr "fertig, wird beendet"

#: ../../src/kadmin/server/schpw.c:282
#, c-format
msgid "setpw request from %s by %.*s%s for %.*s%s: %s"
msgstr "»setpw«-Anfrage von %s durch %.*s%s für %.*s%s: %s"

#: ../../src/kadmin/server/schpw.c:287
#, c-format
msgid "chpw request from %s for %.*s%s: %s"
msgstr "»chpw«-Anfrage von %s für %.*s%s: %s"

#: ../../src/kadmin/server/schpw.c:464
#, c-format
msgid "chpw: Couldn't open admin keytab %s"
msgstr "chpw«: Administratorschlüsseltabelle %s konnte nicht geöffnet werden"

#: ../../src/kadmin/server/server_stubs.c:293
#, c-format
msgid ""
"Unauthorized request: %s, %.*s%s, client=%.*s%s, service=%.*s%s, addr=%s"
msgstr ""
"Unauthorisierte Anfrage: %s, %.*s%s, Client=%.*s%s, Dienst=%.*s%s, Adresse=%s"

#: ../../src/kadmin/server/server_stubs.c:314
#: ../../src/kadmin/server/server_stubs.c:649
#: ../../src/kadmin/server/server_stubs.c:1792
msgid "success"
msgstr "erfolgreich"

#: ../../src/kadmin/server/server_stubs.c:324
#, c-format
msgid "Request: %s, %.*s%s, %s, client=%.*s%s, service=%.*s%s, addr=%s"
msgstr "Anfrage: %s, %.*s%s, %s, Client=%.*s%s, Dienst=%.*s%s, Adresse=%s"

#: ../../src/kadmin/server/server_stubs.c:628
#, c-format
msgid ""
"Unauthorized request: kadm5_rename_principal, %.*s%s to %.*s%s, client=%.*s"
"%s, service=%.*s%s, addr=%s"
msgstr ""
"Unauthorisierte Anfrage: kadm5_rename_principal, %.*s%s bis %.*s%s, Client="
"%.*s%s, Dienst=%.*s%s, Adresse=%s"

#: ../../src/kadmin/server/server_stubs.c:644
#, c-format
msgid ""
"Request: kadm5_rename_principal, %.*s%s to %.*s%s, %s, client=%.*s%s, "
"service=%.*s%s, addr=%s"
msgstr ""
"Anfrage: kadm5_rename_principal, %.*s%s bis %.*s%s, %s, Client=%.*s%s, "
"Dienst=%.*s%s, Adresse=%s"

#: ../../src/kadmin/server/server_stubs.c:1788
#, c-format
msgid ""
"Request: kadm5_init, %.*s%s, %s, client=%.*s%s, service=%.*s%s, addr=%s, "
"vers=%d, flavor=%d"
msgstr ""
"Anfrage: kadm5_init, %.*s%s, %s, Client=%.*s%s, Dienst=%.*s%s, Adresse=%s, "
"Version=%d, Variante=%d"

#: ../../src/kdc/do_as_req.c:273
#, c-format
msgid "AS_REQ : handle_authdata (%d)"
msgstr "AS_REQ: handle_authdata (%d)"

#: ../../src/kdc/do_tgs_req.c:593
#, c-format
msgid "TGS_REQ : handle_authdata (%d)"
msgstr "TGS_REQ: handle_authdata (%d)"

#: ../../src/kdc/do_tgs_req.c:655
msgid "not checking transit path"
msgstr "Übergangspfad wird nicht geprüft"

#: ../../src/kdc/fast_util.c:62
#, c-format
msgid "%s while handling ap-request armor"
msgstr "%s bei der Handhabung des »ap-request«-Schutzes"

#: ../../src/kdc/fast_util.c:71
msgid "ap-request armor for something other than the local TGS"
msgstr "»ap-request«-Schutz für etwas anderes als den lokalen TGS"

#: ../../src/kdc/fast_util.c:80
msgid "ap-request armor without subkey"
msgstr "»ap-request«-Schutz ohne Unterschlüssel"

#: ../../src/kdc/fast_util.c:162
msgid "Ap-request armor not permitted with TGS"
msgstr "»ap-request«-Schutz nicht mit TGS gestattet"

#: ../../src/kdc/fast_util.c:169
#, c-format
msgid "Unknown FAST armor type %d"
msgstr "unbekanntet FAST-Schutztyp %d"

#: ../../src/kdc/fast_util.c:183
msgid "No armor key but FAST armored request present"
msgstr "Es gibt keinen Schutzschlüssel aber eine FAST-geschützte Anfrage"

#: ../../src/kdc/fast_util.c:219
msgid "FAST req_checksum invalid; request modified"
msgstr "FAST-»req_checksum« ungültig; Anfrage geändert"

#: ../../src/kdc/fast_util.c:225
msgid "Unkeyed checksum used in fast_req"
msgstr "in fast_req wurde eine Prüfsumme ohne Schlüssel benutzt"

#: ../../src/kdc/kdc_audit.c:110
#, c-format
msgid "audit plugin %s failed to open. error=%i"
msgstr "Öffnen der Audit-Erweiterung %s fehlgeschlagen. Fehler=%i"

#: ../../src/kdc/kdc_authdata.c:292 ../../src/kdc/kdc_authdata.c:328
#, c-format
msgid "authdata %s failed to initialize: %s"
msgstr "Initialisieren von »authdata« %s fehlgeschlagen: %s"

#: ../../src/kdc/kdc_authdata.c:779
#, c-format
msgid "authdata (%s) handling failure: %s"
msgstr "Handhabung von »authdata« %s fehlgeschlagen: %s"

#: ../../src/kdc/kdc_log.c:82
#, c-format
msgid "AS_REQ (%s) %s: ISSUE: authtime %d, %s, %s for %s"
msgstr "AS_REQ (%s) %s: PROBLEM: Authentifizierungszeit %d, %s, %s für %s"

#: ../../src/kdc/kdc_log.c:88
#, c-format
msgid "AS_REQ (%s) %s: %s: %s for %s%s%s"
msgstr "AS_REQ (%s) %s: %s: %s für %s%s%s"

#: ../../src/kdc/kdc_log.c:159
#, c-format
msgid "TGS_REQ (%s) %s: %s: authtime %d, %s%s %s for %s%s%s"
msgstr "TGS_REQ (%s) %s: %s: Authentifizierungszeit %d, %s%s %s für %s%s%s"

#: ../../src/kdc/kdc_log.c:166
#, c-format
msgid "... PROTOCOL-TRANSITION s4u-client=%s"
msgstr "… PROTOKOLLÜBERGANG s4u-client=%s"

#: ../../src/kdc/kdc_log.c:170
#, c-format
msgid "... CONSTRAINED-DELEGATION s4u-client=%s"
msgstr "…  EINHESCHRÄNKTE DELEGIERUNG s4u-client=%s"

#: ../../src/kdc/kdc_log.c:174
#, c-format
msgid "TGS_REQ %s: %s: authtime %d, %s for %s, 2nd tkt client %s"
msgstr "TGS_REQ %s: %s: Authentifizierungszeit %d, %s für %s, 2. TKT-Client %s"

#: ../../src/kdc/kdc_log.c:208
#, c-format
msgid "bad realm transit path from '%s' to '%s' via '%.*s%s'"
msgstr "falscher Realm-Übergangspfad von »%s« zu »%s« über »%.*s%s«"

#: ../../src/kdc/kdc_log.c:214
#, c-format
msgid "unexpected error checking transit from '%s' to '%s' via '%.*s%s': %s"
msgstr ""
"unerwarteter Fehler bei der Prüfung des Übergangs von »%s« zu »%s« über »%.*s"
"%s«: %s"

#: ../../src/kdc/kdc_log.c:232
msgid "TGS_REQ: issuing alternate <un-unparseable> TGT"
msgstr "TGS_REQ: alternativer <nicht nicht auswertbarer> TGT wird erstellt"

#: ../../src/kdc/kdc_log.c:235
#, c-format
msgid "TGS_REQ: issuing TGT %s"
msgstr "TGS_REQ: TGT %s wird erstellt"

#: ../../src/kdc/kdc_preauth.c:328
#, c-format
msgid "preauth %s failed to initialize: %s"
msgstr "Initialisieren von »preauth« %s fehlgeschlagen: %s"

#: ../../src/kdc/kdc_preauth.c:339
#, c-format
msgid "preauth %s failed to setup loop: %s"
msgstr "Einrichten der Schleife von »preauth« %s fehlgeschlagen: %s"

#: ../../src/kdc/kdc_preauth.c:760
#, c-format
msgid "%spreauth required but hint list is empty"
msgstr "%spreauth benötigt, aber Hinweisliste ist leer"

#: ../../src/kdc/kdc_preauth_ec.c:75
msgid "Encrypted Challenge used outside of FAST tunnel"
msgstr "verschlüsselte Aufforderung wurde außerhalb des FAST-Tunnels verwendet"

#: ../../src/kdc/kdc_preauth_ec.c:110
msgid "Incorrect password in encrypted challenge"
msgstr "falsches Passwort in verschlüsselter Aufforderung"

#: ../../src/kdc/kdc_util.c:236
msgid "TGS_REQ: SESSION KEY or MUTUAL"
msgstr "TGS_REQ: SITZUNGSSCHLÜSSEL oder BEIDERSEITIG"

#: ../../src/kdc/kdc_util.c:314
msgid "PROCESS_TGS: failed lineage check"
msgstr "PROCESS_TGS: Abstammungsprüfung fehlgeschlagen"

#: ../../src/kdc/kdc_util.c:468
#, c-format
msgid "TGS_REQ: UNKNOWN SERVER: server='%s'"
msgstr "TGS_REQ: UNBEKANNTER SERVER: Server=»%s«"

#: ../../src/kdc/main.c:231
#, c-format
msgid "while getting context for realm %s"
msgstr "beim Holen des Kontextes für Realm %s"

#: ../../src/kdc/main.c:329
#, c-format
msgid "while setting default realm to %s"
msgstr "beim Setzen des Standard-Realms auf %s"

#: ../../src/kdc/main.c:337
#, c-format
msgid "while initializing database for realm %s"
msgstr "beim Initialisieren der Datenbank für Realm %s"

#: ../../src/kdc/main.c:346
#, c-format
msgid "while setting up master key name %s for realm %s"
msgstr "beim Einrichten des Hauptschlüsselnamens %s für Realm %s"

#: ../../src/kdc/main.c:359
#, c-format
msgid "while fetching master key %s for realm %s"
msgstr "beim Abholen des Hauptschlüssels %s für Realm %s"

#: ../../src/kdc/main.c:367
#, c-format
msgid "while fetching master keys list for realm %s"
msgstr "beim Abholen der Hauptschlüsselliste für Realm %s"

#: ../../src/kdc/main.c:376
#, c-format
msgid "while resolving kdb keytab for realm %s"
msgstr "beim Ermitteln der KDB-Schlüsseltabelle für Realm %s"

#: ../../src/kdc/main.c:385
#, c-format
msgid "while building TGS name for realm %s"
msgstr "beim Bilden des TGS-Namens für Realm %s"

#: ../../src/kdc/main.c:503
#, c-format
msgid "creating %d worker processes"
msgstr "%d Arbeitsprozesse werden erzeugt"

#: ../../src/kdc/main.c:513
msgid "Unable to reinitialize main loop"
msgstr "Hauptschleife konnte nicht neu initialisiert werden"

#: ../../src/kdc/main.c:518
#, c-format
msgid "Unable to initialize signal handlers in pid %d"
msgstr ""
"Signalbehandlungsprogramme in PID %d konnten nicht initialisiert werden"

#: ../../src/kdc/main.c:548
#, c-format
msgid "worker %ld exited with status %d"
msgstr "Arbeitsprozess %ld endete mit Status %d"

#: ../../src/kdc/main.c:572
#, c-format
msgid "signal %d received in supervisor"
msgstr "Überwachungsprogramm empfing Signal %d"

#: ../../src/kdc/main.c:591
#, c-format
msgid ""
"usage: %s [-x db_args]* [-d dbpathname] [-r dbrealmname]\n"
"\t\t[-R replaycachename] [-m] [-k masterenctype]\n"
"\t\t[-M masterkeyname] [-p port] [-P pid_file]\n"
"\t\t[-n] [-w numworkers] [/]\n"
"\n"
"where,\n"
"\t[-x db_args]* - Any number of database specific arguments.\n"
"\t\t\tLook at each database module documentation for \t\t\tsupported "
"arguments\n"
msgstr ""
"Aufruf: %s [-x Datenbankargumente]* [-d Datenbankpfadname]\n"
"\t\t[-r Datenbank-Realm-Name] [-m] [-k Hauptverschlüsselungstyp]\n"
"\t\t[-M Hauptschlüsselname] [-p Port] [-P PID-Datei]\n"
"\t\t[-n] [-w Arbeitsprozessanzahl] [/]\n"
"\n"
"dabei sind\n"
"\t[-x Datenbankargumente]* - eine beliebige Anzahl datenbankspezifischer "
"Argumente.\n"
"\t\t\tWelche Argumente unterstützt werden, finden Sie in der Dokumentation "
"der jeweiligen Datenbank.\n"

#: ../../src/kdc/main.c:653 ../../src/kdc/main.c:660 ../../src/kdc/main.c:774
#, c-format
msgid " KDC cannot initialize. Not enough memory\n"
msgstr "KDC kann nicht initialisiert werden. Speicher reicht nicht aus\n"

#: ../../src/kdc/main.c:679 ../../src/kdc/main.c:722 ../../src/kdc/main.c:733
#, c-format
msgid "%s: KDC cannot initialize. Not enough memory\n"
msgstr "%s: KDC kann nicht initialisiert werden. Speicher reicht nicht aus\n"

#: ../../src/kdc/main.c:699 ../../src/kdc/main.c:816
#, c-format
msgid "%s: cannot initialize realm %s - see log file for details\n"
msgstr ""
"%s: Realm %s kann nicht initialisiert werden - Einzelheiten finden Sie in "
"der Protokolldatei\n"

#: ../../src/kdc/main.c:710
#, c-format
msgid "%s: cannot initialize realm %s. Not enough memory\n"
msgstr ""
"%s: Realm %s kann nicht initialisiert werden. Speicher reicht nicht aus\n"

#: ../../src/kdc/main.c:761
#, c-format
msgid "invalid enctype %s"
msgstr "ungültiger Verschlüsselungstyp %s"

#: ../../src/kdc/main.c:804
msgid "while attempting to retrieve default realm"
msgstr "beim Versuch, den Standard-Realm abzufragen"

#: ../../src/kdc/main.c:806
#, c-format
msgid "%s: %s, attempting to retrieve default realm\n"
msgstr "%s: %s, es wird versucht, den Standard-Realm abzufragen\n"

#: ../../src/kdc/main.c:912
#, c-format
msgid "%s: cannot get memory for realm list\n"
msgstr "%s: Speicher für die Realm-Liste kann nicht erlangt werden\n"

#: ../../src/kdc/main.c:947
msgid "while initializing lookaside cache"
msgstr "beim Initialisieren des Lookaside-Zwischenspeichers"

#: ../../src/kdc/main.c:955
msgid "while creating main loop"
msgstr "beim Erzeugen der Hauptschleife"

# SAM=Security Accounts Manager
#: ../../src/kdc/main.c:965
msgid "while initializing SAM"
msgstr "beim Initialisieren des SAMs"

#: ../../src/kdc/main.c:1011
msgid "while initializing routing socket"
msgstr "beim Initialisieren des Routing-Sockets"

#: ../../src/kdc/main.c:1017
msgid "while initializing signal handlers"
msgstr "beim Initialisieren des Signalbehandlungsprogramms"

#: ../../src/kdc/main.c:1024
msgid "while initializing network"
msgstr "beim Initialisieren des Netzwerks"

#: ../../src/kdc/main.c:1029
msgid "while detaching from tty"
msgstr "beim Lösen vom Terminal"

#: ../../src/kdc/main.c:1036
msgid "while creating PID file"
msgstr "beim Erstellen der PID-Datei"

#: ../../src/kdc/main.c:1045
msgid "creating worker processes"
msgstr "Arbeitsprozesse werden erzeugt"

#: ../../src/kdc/main.c:1055
msgid "while loading audit plugin module(s)"
msgstr "beim Laden des/der Auditerweiterungsmoduls/Auditerweiterungsmodule"

#: ../../src/kdc/main.c:1059
msgid "commencing operation"
msgstr "Aktion wird begonnen"

#: ../../src/kdc/main.c:1067
msgid "shutting down"
msgstr "wird heruntergefahren"

#: ../../src/lib/apputils/net-server.c:258
msgid "Got signal to request exit"
msgstr "Signal zur Anfrage des Beendens empfangen"

#: ../../src/lib/apputils/net-server.c:272
msgid "Got signal to reset"
msgstr "Signal zum Zurücksetzen empfangen"

#: ../../src/lib/apputils/net-server.c:429
#, c-format
msgid "closing down fd %d"
msgstr "Dateideskriptor %d wird geschlossen"

#: ../../src/lib/apputils/net-server.c:443
#, c-format
msgid "descriptor %d closed but still in svc_fdset"
msgstr "Deskriptor %d geschlossen, aber immer noch in »svc_fdset«"

#: ../../src/lib/apputils/net-server.c:469
msgid "cannot create io event"
msgstr "E/A-Ereignis kann nicht erzeugt werden"

#: ../../src/lib/apputils/net-server.c:475
msgid "cannot save event"
msgstr "Ereignis kann nicht gesichert werden"

#: ../../src/lib/apputils/net-server.c:495
#, c-format
msgid "file descriptor number %d too high"
msgstr "Dateideskriptornummer %d zu hoch"

#: ../../src/lib/apputils/net-server.c:503
msgid "cannot allocate storage for connection info"
msgstr "Speicher für Verbindungsinformation kann nicht reserviert werden"

#: ../../src/lib/apputils/net-server.c:562
#, c-format
msgid "Cannot create TCP server socket on %s"
msgstr "Auf %s kann kein TCP-Server-Socket erstellt werden."

#: ../../src/lib/apputils/net-server.c:571
#, c-format
msgid "TCP socket fd number %d (for %s) too high"
msgstr "TCP-Socket-Deskriptornummer %d (für %s) zu hoch"

#: ../../src/lib/apputils/net-server.c:579
#, c-format
msgid "Cannot enable SO_REUSEADDR on fd %d"
msgstr "SO_REUSEADDR kann nicht für Dateideskriptor %d aktiviert werden"

#: ../../src/lib/apputils/net-server.c:586
#, c-format
msgid "setsockopt(%d,IPV6_V6ONLY,1) failed"
msgstr "setsockopt(%d,IPV6_V6ONLY,1) fehlgeschlagen"

#: ../../src/lib/apputils/net-server.c:588
#, c-format
msgid "setsockopt(%d,IPV6_V6ONLY,1) worked"
msgstr "setsockopt(%d,IPV6_V6ONLY,1) funktioniert"

#: ../../src/lib/apputils/net-server.c:591
msgid "no IPV6_V6ONLY socket option support"
msgstr "keine Socket-Option für IPV6_V6ONLY unterstützt"

#: ../../src/lib/apputils/net-server.c:597
#, c-format
msgid "Cannot bind server socket on %s"
msgstr "Server-Socket kann nicht an %s gebunden werden"

#: ../../src/lib/apputils/net-server.c:624
#, c-format
msgid "Cannot create RPC service: %s; continuing"
msgstr "RPC-Dienst kann nicht erstellt werden: %s; es wird fortgefahren"

#: ../../src/lib/apputils/net-server.c:633
#, c-format
msgid "Cannot register RPC service: %s; continuing"
msgstr "RPC-Dienst kann nicht registriert werden: %s; es wird fortgefahren"

#: ../../src/lib/apputils/net-server.c:682
#, c-format
msgid "Cannot listen on TCP server socket on %s"
msgstr ""
"Auf dem TCP-Server-Socket kann nicht auf eine Verbindung gewartet werden auf "
"%s."

#: ../../src/lib/apputils/net-server.c:688
#, c-format
msgid "cannot set listening tcp socket on %s non-blocking"
msgstr ""
"Das auf eine Verbindung wartende TCP-Socket kann nicht auf nicht-"
"blockierendes %s gesetzt werden."

#: ../../src/lib/apputils/net-server.c:695
#, c-format
msgid "disabling SO_LINGER on TCP socket on %s"
msgstr "SO_LINGER auf dem TCP-Socket auf %s wird deaktiviert"

#: ../../src/lib/apputils/net-server.c:743
#: ../../src/lib/apputils/net-server.c:752
#, c-format
msgid "listening on fd %d: tcp %s"
msgstr "auf Dateideskriptor %d wird auf eine Verbindung gewartet: TCP %s"

#: ../../src/lib/apputils/net-server.c:757
msgid "assuming IPv6 socket accepts IPv4"
msgstr "es wird davon ausgegangen, dass das IPv6-Socket IPv4 akzeptiert"

#: ../../src/lib/apputils/net-server.c:791
#: ../../src/lib/apputils/net-server.c:804
#, c-format
msgid "listening on fd %d: rpc %s"
msgstr "auf Dateideskriptor %d wird auf eine Verbindung gewartet: RPC %s"

#: ../../src/lib/apputils/net-server.c:883
#, c-format
msgid "Cannot request packet info for udp socket address %s port %d"
msgstr ""
"Paketinformation für UDP-Socket-Adresse %s, Port %d, kann nicht abgefragt "
"werden"

#: ../../src/lib/apputils/net-server.c:889
#, c-format
msgid "listening on fd %d: udp %s%s"
msgstr "auf Dateideskriptor %d wird auf eine Verbindung gewartet: UDP %s%s"

#: ../../src/lib/apputils/net-server.c:918
msgid "Failed to reconfigure network, exiting"
msgstr "Neukonfiguration des Netzwerks fehlgeschlagen, wird beendet"

#: ../../src/lib/apputils/net-server.c:979
#, c-format
msgid ""
"unhandled routing message type %d, will reconfigure just for the fun of it"
msgstr ""
"nicht behandelter Routing-Meldungstyp %d, es wird es nur zum Spaß neu "
"konfiguriert"

#: ../../src/lib/apputils/net-server.c:1013
#, c-format
msgid "short read (%d/%d) from routing socket"
msgstr "ungenügende Daten (%d/%d) vom Routing-Socket gelesen"

#: ../../src/lib/apputils/net-server.c:1023
#, c-format
msgid "read %d from routing socket but msglen is %d"
msgstr "%d vom Routing-Socket gelesen, Nachrichtenlänge ist jedoch %d"

#: ../../src/lib/apputils/net-server.c:1055
#, c-format
msgid "couldn't set up routing socket: %s"
msgstr "Routing-Socket konnte nicht eingerichtet werden: %s"

#: ../../src/lib/apputils/net-server.c:1058
#, c-format
msgid "routing socket is fd %d"
msgstr "Das Routing-Socket hat den Dateideskriptor %d."

#: ../../src/lib/apputils/net-server.c:1084
msgid "setting up network..."
msgstr "Netzwerk wird eingerichtet …"

#: ../../src/lib/apputils/net-server.c:1101
#, c-format
msgid "set up %d sockets"
msgstr "%d Sockets werden eingerichtet"

#: ../../src/lib/apputils/net-server.c:1103
msgid "no sockets set up?"
msgstr "keine Sockets eingerichtet?"

#: ../../src/lib/apputils/net-server.c:1351
#: ../../src/lib/apputils/net-server.c:1405
msgid "while dispatching (udp)"
msgstr "beim Versenden (UDP)"

#: ../../src/lib/apputils/net-server.c:1380
#, c-format
msgid "while sending reply to %s/%s from %s"
msgstr "beim Senden der Antwort zu %s/%s von %s"

#: ../../src/lib/apputils/net-server.c:1385
#, c-format
msgid "short reply write %d vs %d\n"
msgstr "ungenügende Ausgabe der Antwort %d gegenüber %d\n"

#: ../../src/lib/apputils/net-server.c:1430
msgid "while receiving from network"
msgstr "beim Empfangen vom Netzwerk"

#: ../../src/lib/apputils/net-server.c:1446
#, c-format
msgid "pktinfo says local addr is %s"
msgstr "Pktinfo sagt, die lokale Adresse sei %s"

#: ../../src/lib/apputils/net-server.c:1479
msgid "too many connections"
msgstr "zu viele Verbindungen"

#: ../../src/lib/apputils/net-server.c:1502
#, c-format
msgid "dropping %s fd %d from %s"
msgstr "%s Dateideskriptor %d von %s wird verworfen"

#: ../../src/lib/apputils/net-server.c:1580
#, c-format
msgid "allocating buffer for new TCP session from %s"
msgstr "Puffer für neue TCP-Sitzung von %s wird reserviert"

#: ../../src/lib/apputils/net-server.c:1610
msgid "while dispatching (tcp)"
msgstr "beim Versenden (TCP)"

#: ../../src/lib/apputils/net-server.c:1642
msgid "error allocating tcp dispatch private!"
msgstr "Fehler beim Reservieren zum nicht öffentlichen TCP-Versand!"

#: ../../src/lib/apputils/net-server.c:1689
#, c-format
msgid "TCP client %s wants %lu bytes, cap is %lu"
msgstr "TCP-Client %s will %lu Byte, Cap ist %lu"

#: ../../src/lib/apputils/net-server.c:1697
#, c-format
msgid "error constructing KRB_ERR_FIELD_TOOLONG error! %s"
msgstr "Fehler beim Erzeugen des KRB_ERR_FIELD_TOOLONG-Fehlers! %s"

#: ../../src/lib/apputils/net-server.c:1876
#, c-format
msgid "accepted RPC connection on socket %d from %s"
msgstr "akzeptierte PRC-Verbindung auf Socket %d von %s"

# pseudo random function
#: ../../src/lib/crypto/krb/cf2.c:114
#, c-format
msgid "Enctype %d has no PRF"
msgstr "Verschlüsselungstyp %d hat keine PRF"

#: ../../src/lib/crypto/krb/prng_fortuna.c:428
msgid "Random number generator could not be seeded"
msgstr "Zufallszahlengenerator konnte kein Startwert zugewiesen werden"

#: ../../src/lib/gssapi/generic/disp_major_status.c:43
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:165
msgid "A required input parameter could not be read"
msgstr "Ein benötigter Eingabeparameter konnte nicht gelesen werden."

#: ../../src/lib/gssapi/generic/disp_major_status.c:44
msgid "A required input parameter could not be written"
msgstr "Ein benötigter Eingabeparameter konnte nicht geschrieben werden."

#: ../../src/lib/gssapi/generic/disp_major_status.c:45
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:175
msgid "A parameter was malformed"
msgstr "Ein Parameter hatte eine falsche Form"

#: ../../src/lib/gssapi/generic/disp_major_status.c:48
msgid "calling error"
msgstr "Aufruffehler"

#: ../../src/lib/gssapi/generic/disp_major_status.c:59
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:195
msgid "An unsupported mechanism was requested"
msgstr "Ein nicht unterstützter Mechanismus wurde angefordert."

#: ../../src/lib/gssapi/generic/disp_major_status.c:60
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:199
msgid "An invalid name was supplied"
msgstr "Ein ungültiger Name wurde übergeben."

#: ../../src/lib/gssapi/generic/disp_major_status.c:61
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:203
msgid "A supplied name was of an unsupported type"
msgstr "Ein übergebener Name hatte einen nicht unterstützten Typ."

#: ../../src/lib/gssapi/generic/disp_major_status.c:62
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:208
msgid "Incorrect channel bindings were supplied"
msgstr "Falsche Kanalbindungen wurden übergeben."

#: ../../src/lib/gssapi/generic/disp_major_status.c:63
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:179
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:274
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:334
msgid "An invalid status code was supplied"
msgstr "Ein ungültiger Statuscode wurde übergeben."

#: ../../src/lib/gssapi/generic/disp_major_status.c:64
msgid "A token had an invalid signature"
msgstr "Ein Merkmal hatte eine ungültige Signatur."

#: ../../src/lib/gssapi/generic/disp_major_status.c:65
msgid "No credentials were supplied"
msgstr "Es wurden keine Anmeldedaten übergeben."

#: ../../src/lib/gssapi/generic/disp_major_status.c:66
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:223
msgid "No context has been established"
msgstr "Es wurde keine Kontext etabliert."

#: ../../src/lib/gssapi/generic/disp_major_status.c:67
msgid "A token was invalid"
msgstr "Ein Merkmal war ungültig."

#: ../../src/lib/gssapi/generic/disp_major_status.c:68
msgid "A credential was invalid"
msgstr "Eine der Anmeldedaten war ungültig."

#: ../../src/lib/gssapi/generic/disp_major_status.c:69
msgid "The referenced credentials have expired"
msgstr "Die referenzierten Anmeldedaten sind abgelaufen."

#: ../../src/lib/gssapi/generic/disp_major_status.c:70
msgid "The context has expired"
msgstr "Der Kontext ist abgelaufen."

#: ../../src/lib/gssapi/generic/disp_major_status.c:71
msgid "Miscellaneous failure"
msgstr "sonstiger Fehlschlag"

#: ../../src/lib/gssapi/generic/disp_major_status.c:72
msgid "The quality-of-protection requested could not be provided"
msgstr ""
"Die angeforderte Qualität des Schutzes konnte nicht bereitgestellt werden."

#: ../../src/lib/gssapi/generic/disp_major_status.c:73
msgid "The operation is forbidden by the local security policy"
msgstr "Die Aktion wird durch die lokale Sicherheitsrichtinie verboten."

#: ../../src/lib/gssapi/generic/disp_major_status.c:74
msgid "The operation or option is not available"
msgstr "Die Aktion oder Option ist nicht verfügbar."

#: ../../src/lib/gssapi/generic/disp_major_status.c:77
msgid "routine error"
msgstr "Fehler in einer Routine"

#: ../../src/lib/gssapi/generic/disp_major_status.c:89
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:311
msgid "The routine must be called again to complete its function"
msgstr ""
"Die Routine muss erneut aufgerufen werden, um ihre Funktion zu "
"vervollständigen."

#: ../../src/lib/gssapi/generic/disp_major_status.c:90
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:316
msgid "The token was a duplicate of an earlier token"
msgstr "Das Merkmal war ein Zweitexemplar eines früheren Merkmals."

#: ../../src/lib/gssapi/generic/disp_major_status.c:91
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:321
msgid "The token's validity period has expired"
msgstr "Die Gültigkeitsperiode des Merkmals ist abgelaufen."

#: ../../src/lib/gssapi/generic/disp_major_status.c:92
#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:325
msgid "A later token has already been processed"
msgstr "Es wurde bereits ein neueres Merkmal verarbeitet."

#: ../../src/lib/gssapi/generic/disp_major_status.c:95
msgid "supplementary info code"
msgstr "zusätzlicher Informationscode"

#: ../../src/lib/gssapi/generic/disp_major_status.c:106
#: ../lib/krb5/error_tables/krb5_err.c:23
msgid "No error"
msgstr "kein Fehler"

#: ../../src/lib/gssapi/generic/disp_major_status.c:107
#, c-format
msgid "Unknown %s (field = %d)"
msgstr "%s unbekannt (Feld = %d)"

#: ../../src/lib/gssapi/krb5/acquire_cred.c:165
#, c-format
msgid "No key table entry found matching %s"
msgstr "Es wurde kein zu %s passender Schlüsseltabelleneintrag gefunden."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:161
msgid "The routine completed successfully"
msgstr "Die Routine wurde erfolgreich abgeschlossen"

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:170
msgid "A required output parameter could not be written"
msgstr "Ein erforderlicher Ausgabeparameter konnte nicht geschrieben werden."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:212
msgid "A token had an invalid Message Integrity Check (MIC)"
msgstr ""
"Ein Merkmal hatte eine ungültige Meldungsintegritätsprüfung (Message "
"Integrity Check/MIC)."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:217
msgid ""
"No credentials were supplied, or the credentials were unavailable or "
"inaccessible"
msgstr ""
"Es wurden keine Anmeldedaten übergeben oder die Anmeldedaten waren nicht "
"verfügbar bzw. ein Zugriff darauf nicht möglich."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:227
msgid "Invalid token was supplied"
msgstr "Es wurde ein ungültiges Token übergeben."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:231
msgid "Invalid credential was supplied"
msgstr "ungültige Anmeldedaten wurden übergeben"

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:235
msgid "The referenced credential has expired"
msgstr "Die referenzierten Anmeldedaten sind abgelaufen."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:239
msgid "The referenced context has expired"
msgstr "Der referenzierte Kontext ist abgelaufen."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:243
msgid "Unspecified GSS failure.  Minor code may provide more information"
msgstr ""
"nicht spezifizierter GSS-Fehlschlag. Möglicherweise stellt der "
"untergeordnete Code weitere Informationen bereit."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:248
msgid "The quality-of-protection (QOP) requested could not be provided"
msgstr ""
"Die Qualität des Schutzes (quality-of-protection/QOP) konnte nicht "
"bereitgestellt werden."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:253
msgid "The operation is forbidden by local  security policy"
msgstr "Die Aktion wird durch die lokale Sicherheitsrichtinie verboten."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:258
msgid "The operation or option is not available or unsupported"
msgstr ""
"Die Aktion oder Option ist nicht verfügbar oder wird nicht unterstützt."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:263
msgid "The requested credential element already exists"
msgstr "Das angeforderte Anmeldedatenelement existiert bereits."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:268
msgid "The provided name was not mechanism specific (MN)"
msgstr "Der bereitgestellte Name war nicht mechanismusspezifisch (MN)."

#: ../../src/lib/gssapi/mechglue/g_dsp_status.c:329
msgid "An expected per-message token was not received"
msgstr "Ein erwartetes nachrichtenspezifisches Token wurde nicht empfangen."

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1860
msgid "SPNEGO cannot find mechanisms to negotiate"
msgstr "SPNEGO kann keine Mechanismen zum Aushandeln finden."

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1865
msgid "SPNEGO failed to acquire creds"
msgstr "SPNEGO ist beim Beschaffen von Anmeldedaten gescheitert"

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1870
msgid "SPNEGO acceptor did not select a mechanism"
msgstr "SPNEGO-Abnehmer hat keinen Mechanismus ausgewählt"

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1875
msgid "SPNEGO failed to negotiate a mechanism"
msgstr "SPNEGO ist beim Aushandeln eines Mechanismus gescheitert."

#: ../../src/lib/gssapi/spnego/spnego_mech.c:1880
msgid "SPNEGO acceptor did not return a valid token"
msgstr "SPNEGO-Abnehmer hat kein gültiges Token zurückgeliefert"

#: ../../src/lib/kadm5/alt_prof.c:854
#, c-format
msgid "Cannot resolve address of admin server \"%s\" for realm \"%s\""
msgstr ""
"Adresse des Admin-Servers »%s« für Realm »%s« kann nicht ermittelt werden"

#: ../../src/lib/kadm5/logger.c:56
#, c-format
msgid "%s: cannot parse <%s>\n"
msgstr "%s: <%s> kann nicht ausgewertet werden\n"

#: ../../src/lib/kadm5/logger.c:57
#, c-format
msgid "%s: warning - logging entry syntax error\n"
msgstr "%s: Warnung – Syntaxfehler bei Protokolleintrag\n"

#: ../../src/lib/kadm5/logger.c:58
#, c-format
msgid "%s: error writing to %s\n"
msgstr "%s: Fehler beim Schreiben auf %s\n"

#: ../../src/lib/kadm5/logger.c:59
#, c-format
msgid "%s: error writing to %s device\n"
msgstr "%s: Fehler beim Schreiben auf Gerät %s\n"

#: ../../src/lib/kadm5/logger.c:61
msgid "EMERGENCY"
msgstr "NOTFALL"

#: ../../src/lib/kadm5/logger.c:62
msgid "ALERT"
msgstr "ALARM"

#: ../../src/lib/kadm5/logger.c:63
msgid "CRITICAL"
msgstr "KRITISCH"

#: ../../src/lib/kadm5/logger.c:64
msgid "Error"
msgstr "Fehler"

#: ../../src/lib/kadm5/logger.c:65
msgid "Warning"
msgstr "Warnung"

#: ../../src/lib/kadm5/logger.c:66
msgid "Notice"
msgstr "Hinweis"

#: ../../src/lib/kadm5/logger.c:67
msgid "info"
msgstr "Information"

#: ../../src/lib/kadm5/logger.c:68
msgid "debug"
msgstr "Fehlersuchmeldung"

#: ../../src/lib/kadm5/logger.c:967
#, c-format
msgid "Couldn't open log file %s: %s\n"
msgstr "Protokolldatei %s konnte nicht geöffnet werden: %s\n"

#: ../../src/lib/kadm5/srv/kadm5_hook.c:119
#, c-format
msgid "kadm5_hook %s failed postcommit %s: %s"
msgstr "»kadm5_hook« %s ist beim Nach-Commit %s gescheitert: %s"

#: ../../src/lib/kadm5/srv/pwqual_dict.c:106
msgid "No dictionary file specified, continuing without one."
msgstr "keine Wörterbuchdatei angegeben, es wird ohne fortgefahren"

#: ../../src/lib/kadm5/srv/pwqual_dict.c:113
#, c-format
msgid "WARNING!  Cannot find dictionary file %s, continuing without one."
msgstr ""
"WARNUNG! Wörterbuchdatei %s kann nicht gefunden werden, es wird ohne "
"fortgefahren"

#: ../../src/lib/kadm5/srv/pwqual_empty.c:42
msgid "Empty passwords are not allowed"
msgstr "Leere Passwörter sind nicht erlaubt."

#: ../../src/lib/kadm5/srv/pwqual_hesiod.c:114
msgid "Password may not match user information."
msgstr "Das Passwort darf keinen Anwenderdaten entsprechen."

#: ../../src/lib/kadm5/srv/pwqual_princ.c:54
msgid "Password may not match principal name"
msgstr "Das Passwort darf nicht mit dem Principal-Namen übereinstimmen."

#: ../../src/lib/kadm5/srv/server_acl.c:89
#, c-format
msgid "%s: line %d too long, truncated"
msgstr "%s: Zeile %d zu lang, wurde gekürzt"

#: ../../src/lib/kadm5/srv/server_acl.c:90
#, c-format
msgid "Unrecognized ACL operation '%c' in %s"
msgstr "unbekannte ACL-Aktion »%c« in %s"

#: ../../src/lib/kadm5/srv/server_acl.c:92
#, c-format
msgid "%s: syntax error at line %d <%10s...>"
msgstr "%s: Syntaxfehler in Zeile %d <%10s …>"

#: ../../src/lib/kadm5/srv/server_acl.c:94
#, c-format
msgid "%s while opening ACL file %s"
msgstr "%s beim Öffnen der ACL-Datei %s"

#: ../../src/lib/kadm5/srv/server_acl.c:353
#, c-format
msgid "%s: invalid restrictions: %s"
msgstr "%s: ungültige Beschränkung: %s"

#: ../../src/lib/kadm5/srv/server_kdb.c:192
msgid "History entry contains no key data"
msgstr "Chronikeintrag enthält keine Schlüsseldaten"

#: ../../src/lib/kadm5/srv/server_misc.c:128
#, c-format
msgid "password quality module %s rejected password for %s: %s"
msgstr ""
"Das Modul %s für Passwortqualität hat das Passwort für %s abgelehnt: %s"

#: ../../src/lib/kadm5/str_conv.c:80
msgid "Not Postdateable"
msgstr "nicht vordatierbar"

#: ../../src/lib/kadm5/str_conv.c:81
msgid "Not Forwardable"
msgstr "nicht weiterleitbar"

#: ../../src/lib/kadm5/str_conv.c:82
msgid "No TGT-based requests"
msgstr "keine TGT-basierten Anfragen"

#: ../../src/lib/kadm5/str_conv.c:83
msgid "Not renewable"
msgstr "nicht erneuerbar"

#: ../../src/lib/kadm5/str_conv.c:84
msgid "Not proxiable"
msgstr "Proxy nicht nutzbar"

#: ../../src/lib/kadm5/str_conv.c:85
msgid "No DUP_SKEY requests"
msgstr "keine DUP_SKEY-Anfragen"

#: ../../src/lib/kadm5/str_conv.c:86
msgid "All Tickets Disallowed"
msgstr "keine Tickets erlaubt"

#: ../../src/lib/kadm5/str_conv.c:87
msgid "Preauthentication required"
msgstr "Vorauthentifizierung erforderlich"

#: ../../src/lib/kadm5/str_conv.c:88
msgid "HW authentication required"
msgstr "HW-Authentifizierung erforderlich"

#: ../../src/lib/kadm5/str_conv.c:89
msgid "OK as Delegate"
msgstr "OK als Vertreter"

#: ../../src/lib/kadm5/str_conv.c:90
msgid "Password Change required"
msgstr "Passwortänderung erforderlich"

#: ../../src/lib/kadm5/str_conv.c:91
msgid "Service Disabled"
msgstr "Dienst deaktiviert"

#: ../../src/lib/kadm5/str_conv.c:92
msgid "Password Changing Service"
msgstr "Passwortänderungsdienst"

#: ../../src/lib/kadm5/str_conv.c:93
msgid "RSA-MD5 supported"
msgstr "RSA-MD5 unterstützt"

#: ../../src/lib/kadm5/str_conv.c:94
msgid "Protocol transition with delegation allowed"
msgstr "Protokollübergang mit Vertretung erlaubt"

#: ../../src/lib/kadm5/str_conv.c:95
msgid "No authorization data required"
msgstr "keine Autorisierungsdaten erforderlich"

#: ../../src/lib/kdb/kdb5.c:219
msgid "No default realm set; cannot initialize KDB"
msgstr "kein Standard-Realm gesetzt; KDB kann nicht initialisiert werden"

#: ../../src/lib/kdb/kdb5.c:324 ../../src/lib/kdb/kdb5.c:406
#, c-format
msgid "Unable to find requested database type: %s"
msgstr "angeforderter Datenbanktyp kann nicht gefunden werden. %s"

#: ../../src/lib/kdb/kdb5.c:416
#, c-format
msgid "plugin symbol 'kdb_function_table' lookup failed: %s"
msgstr ""
"Nachschlagen des Erweiterungssymbols »kdb_function_table« fehlgeschlagen: %s"

#: ../../src/lib/kdb/kdb5.c:426
#, c-format
msgid ""
"Unable to load requested database module '%s': plugin symbol "
"'kdb_function_table' not found"
msgstr ""
"angefordertes Datenbankmodul »%s« kann nicht geladen werden: "
"Erweiterungssymbol »kdb_function_table« nicht gefunden"

#: ../../src/lib/kdb/kdb5.c:1650
#, c-format
msgid "Illegal version number for KRB5_TL_MKEY_AUX %d\n"
msgstr "Ungültige Versionsnummer für KRB5_TL_MKEY_AUX %d\n"

#: ../../src/lib/kdb/kdb5.c:1819
#, c-format
msgid "Illegal version number for KRB5_TL_ACTKVNO %d\n"
msgstr "Ungültige Versionsnummer für KRB5_TL_ACTKVNO %d\n"

#: ../../src/lib/kdb/kdb_default.c:164
#, c-format
msgid "keyfile (%s) is not a regular file: %s"
msgstr "Schlüsseldatei (%s) ist keine normale Datei: %s"

#: ../../src/lib/kdb/kdb_default.c:177
msgid "Could not create temp keytab file name."
msgstr "Temporärer Schlüsseltabellendateiname konnte nicht erstellt werden."

#: ../../src/lib/kdb/kdb_default.c:202
#, c-format
msgid "Temporary stash file already exists: %s."
msgstr "Temporäre Ablagedatei existiert bereits: %s."

#: ../../src/lib/kdb/kdb_default.c:230
#, c-format
msgid "rename of temporary keyfile (%s) to (%s) failed: %s"
msgstr ""
"Umbenennen von temporärer Schlüsseldatei (%s) in (%s) fehlgeschlagen: %s"

#: ../../src/lib/kdb/kdb_default.c:419
#, c-format
msgid "Can not fetch master key (error: %s)."
msgstr "Hauptschlüssel kann nicht abgeholt werden (Fehler: %s)"

#: ../../src/lib/kdb/kdb_default.c:482
msgid "Unable to decrypt latest master key with the provided master key\n"
msgstr ""
"Letzter Hauptschlüssel kann nicht mit dem bereitgestellten Hauptschlüssel "
"entschlüsselt werden.\n"

#: ../../src/lib/kdb/kdb_log.c:83
msgid "could not sync ulog header to disk"
msgstr "Ulog-Kopfzeilen konnten nicht auf die Platte synchronisiert werden"

#: ../../src/lib/krb5/ccache/cc_dir.c:122
#, c-format
msgid "Subsidiary cache path %s has no parent directory"
msgstr ""
"Ergänzender Zwischenspeicherpfad %s hat kein übergeordnetes Verzeichnis."

#: ../../src/lib/krb5/ccache/cc_dir.c:128
#, c-format
msgid "Subsidiary cache path %s filename does not begin with \"tkt\""
msgstr ""
"Dateiname des ergänzenden Zwischenspeicherpfads %s beginnt nicht mit »tkt«"

#: ../../src/lib/krb5/ccache/cc_dir.c:169
#, c-format
msgid "%s contains invalid filename"
msgstr "%s enthält einen ungültigen Dateinamen."

#: ../../src/lib/krb5/ccache/cc_dir.c:229
#, c-format
msgid "Credential cache directory %s does not exist"
msgstr "Anmeldedatenzwischenspeicherverzeichnis %s existiert nicht."

#: ../../src/lib/krb5/ccache/cc_dir.c:235
#, c-format
msgid "Credential cache directory %s exists but is not a directory"
msgstr ""
"Anmeldedatenzwischenspeicherverzeichnis %s existiert, ist jedoch kein "
"Verzeichnis"

#: ../../src/lib/krb5/ccache/cc_dir.c:400
msgid ""
"Can't create new subsidiary cache because default cache is not a directory "
"collection"
msgstr ""
"Der neue ergänzende Zwischenspeicher kann nicht erstellt werden, da der "
"Standardzwischenspeicher keine Ansammlung von Verzeichnissen ist."

#: ../../src/lib/krb5/ccache/cc_file.c:569
#, c-format
msgid "Credentials cache file '%s' not found"
msgstr "Anmeldedatenzwischenspeicherdatei »%s« nicht gefunden"

#: ../../src/lib/krb5/ccache/cc_file.c:1575
#, c-format
msgid "Credentials cache I/O operation failed (%s)"
msgstr "Anmeldedatenzwischenspeicher-E/A-Aktion fehlgeschlagen (%s)"

#: ../../src/lib/krb5/ccache/cc_keyring.c:1151
msgid ""
"Can't create new subsidiary cache because default cache is already a "
"subsidiary"
msgstr ""
"Der neue ergänzende Zwischenspeicher kann nicht erstellt werden, da der "
"Standardzwischenspeicher bereits eine Ergänzung ist."

#: ../../src/lib/krb5/ccache/cc_keyring.c:1219
#, c-format
msgid "Credentials cache keyring '%s' not found"
msgstr "Schlüsselbund %s des Anmeldedatenzwischenspeichers nicht gefunden"

#: ../../src/lib/krb5/ccache/cccursor.c:212
#, c-format
msgid "Can't find client principal %s in cache collection"
msgstr ""
"Client-Principal %s kann nicht in der Zwischenspeicheransammlung gefunden "
"werden"

#: ../../src/lib/krb5/ccache/cccursor.c:253
msgid "No Kerberos credentials available"
msgstr "keine Kerberos-Anmeldedaten verfügbar"

#: ../../src/lib/krb5/keytab/kt_file.c:398
#, c-format
msgid "No key table entry found for %s"
msgstr "Für %s wurde kein Schlüsseltabelleneintrag gefunden."

#: ../../src/lib/krb5/keytab/kt_file.c:815
#: ../../src/lib/krb5/keytab/kt_file.c:848
msgid "Cannot change keytab with keytab iterators active"
msgstr ""
"Schlüsseltabelle mit aktiven Schlüsseltabelleniteratoren kann nicht geändert "
"werden"

#: ../../src/lib/krb5/keytab/kt_file.c:1047
#, c-format
msgid "Key table file '%s' not found"
msgstr "Schlüsseltabellendatei »%s« nicht gefunden"

#: ../../src/lib/krb5/keytab/ktfns.c:127
#, c-format
msgid "Keytab %s is nonexistent or empty"
msgstr "Schlüsseltabelle %s existiert nicht oder ist leer"

#: ../../src/lib/krb5/krb/chpw.c:251
msgid "Malformed request error"
msgstr "Fehler wegen Anfrage in falscher Form"

#: ../../src/lib/krb5/krb/chpw.c:254 ../lib/krb5/error_tables/kdb5_err.c:58
msgid "Server error"
msgstr "Serverfehler"

#: ../../src/lib/krb5/krb/chpw.c:257
msgid "Authentication error"
msgstr "Authentifizierungsfehler"

#: ../../src/lib/krb5/krb/chpw.c:260
msgid "Password change rejected"
msgstr "Passwortänderung abgelehnt"

#: ../../src/lib/krb5/krb/chpw.c:263
msgid "Access denied"
msgstr "Zugriff verweigert"

#: ../../src/lib/krb5/krb/chpw.c:266
msgid "Wrong protocol version"
msgstr "falsche Protokollversion"

#: ../../src/lib/krb5/krb/chpw.c:269
msgid "Initial password required"
msgstr "Erstpasswort erforderlich"

#: ../../src/lib/krb5/krb/chpw.c:272
msgid "Success"
msgstr "Erfolg"

#: ../../src/lib/krb5/krb/chpw.c:275 ../lib/krb5/error_tables/krb5_err.c:257
msgid "Password change failed"
msgstr "Ändern des Passworts fehlgeschlagen"

#: ../../src/lib/krb5/krb/chpw.c:433
msgid ""
"The password must include numbers or symbols.  Don't include any part of "
"your name in the password."
msgstr ""
"Das Passwort muss Zahlen oder Symbole enthalten. Fügen Sie keinen Teil Ihres "
"Namens in das Passwort ein."

#: ../../src/lib/krb5/krb/chpw.c:439
#, c-format
msgid "The password must contain at least %d character."
msgid_plural "The password must contain at least %d characters."
msgstr[0] "Das Passwort muss mindestens %d Zeichen enthalten."
msgstr[1] "Das Passwort muss mindestens %d Zeichen enthalten."

#: ../../src/lib/krb5/krb/chpw.c:448
#, c-format
msgid "The password must be different from the previous password."
msgid_plural "The password must be different from the previous %d passwords."
msgstr[0] "Das Passwort muss sich vom vorhergehenden Passwort unterscheiden."
msgstr[1] ""
"Das Passwort muss sich von den vorhergehenden %d Passwörtern unterscheiden."

#: ../../src/lib/krb5/krb/chpw.c:460
#, c-format
msgid "The password can only be changed once a day."
msgid_plural "The password can only be changed every %d days."
msgstr[0] "Das Passwort kann nur einmal täglich geändert werden."
msgstr[1] "Das Passwort kann nur alle %d Tage geändert werden."

#: ../../src/lib/krb5/krb/chpw.c:506
msgid "Try a more complex password, or contact your administrator."
msgstr ""
"Versuchen Sie es mit einem etwas komplexeren Passwort oder wenden Sie sich "
"an Ihren Administrator."

#: ../../src/lib/krb5/krb/fast.c:217
#, c-format
msgid "%s constructing AP-REQ armor"
msgstr "%s-Konstruktion von AP-REQ-Schutz"

#: ../../src/lib/krb5/krb/fast.c:399
#, c-format
msgid "%s while decrypting FAST reply"
msgstr "%s beim Entschlüsseln der FAST-Antwort"

#: ../../src/lib/krb5/krb/fast.c:408
msgid "nonce modified in FAST response: KDC response modified"
msgstr ""
"Nummer für einmaligen Gebrauch in der FAST-Anwort geändert: KDC-Anwort "
"geändert"

#: ../../src/lib/krb5/krb/fast.c:474
msgid "Expecting FX_ERROR pa-data inside FAST container"
msgstr "Innerhalb des FAST-Containers wird »FX_ERROR pa-data« erwartet."

#: ../../src/lib/krb5/krb/fast.c:545
msgid "FAST response missing finish message in KDC reply"
msgstr "Der FAST-Anwort fehlt die Beendigungsnachricht in der KDC-Anwort"

#: ../../src/lib/krb5/krb/fast.c:558
msgid "Ticket modified in KDC reply"
msgstr "Ticket in der KDC-Antwort verändert"

#: ../../src/lib/krb5/krb/gc_via_tkt.c:208
#, c-format
msgid "KDC returned error string: %.*s"
msgstr "KDC gab eine Fehlermeldung zurück: %.*s"

#: ../../src/lib/krb5/krb/gc_via_tkt.c:217
#, c-format
msgid "Server %s not found in Kerberos database"
msgstr "Server %s wurde nicht in der Kerberos-Datenbank gefunden"

#: ../../src/lib/krb5/krb/get_in_tkt.c:133
msgid "Reply has wrong form of session key for anonymous request"
msgstr ""
"Antwort hat die falsche Form des Sitzungschlüssels für eine anonyme Anfrage"

#: ../../src/lib/krb5/krb/get_in_tkt.c:1628
#, c-format
msgid "%s while storing credentials"
msgstr "%s beim Speichern der Anmeldedaten"

#: ../../src/lib/krb5/krb/get_in_tkt.c:1715
#, c-format
msgid "Client '%s' not found in Kerberos database"
msgstr "Client »%s« wurde nicht in der Kerberos-Datenbank gefunden"

#: ../../src/lib/krb5/krb/gic_keytab.c:207
#, c-format
msgid "Keytab contains no suitable keys for %s"
msgstr "Schlüsseltabelle enthält keine passenden Schlüssel für %s"

#: ../../src/lib/krb5/krb/gic_pwd.c:75
#, c-format
msgid "Password for %s"
msgstr "Passwort für %s"

#: ../../src/lib/krb5/krb/gic_pwd.c:227
#, c-format
msgid "Warning: Your password will expire in less than one hour on %s"
msgstr ""
"Warnung: Ihr Passwort auf %s wird in weniger als einer Stunde ablaufen."

# FIXME in German impossible; plural without »s«
#: ../../src/lib/krb5/krb/gic_pwd.c:231
#, c-format
msgid "Warning: Your password will expire in %d hour%s on %s"
msgstr "Warnung: Ihr Passwort wird in %d Stunden%s am %s ablaufen."

#: ../../src/lib/krb5/krb/gic_pwd.c:235
#, c-format
msgid "Warning: Your password will expire in %d days on %s"
msgstr "Warnung: Ihr Passwort wird in %d Tagen am %s ablaufen."

#: ../../src/lib/krb5/krb/gic_pwd.c:409
msgid "Password expired.  You must change it now."
msgstr "Passwort abgelaufen. Sie müssen es nun ändern."

#: ../../src/lib/krb5/krb/gic_pwd.c:428 ../../src/lib/krb5/krb/gic_pwd.c:432
#, c-format
msgid "%s.  Please try again."
msgstr "%s. Bitte versuchen Sie es erneut."

#: ../../src/lib/krb5/krb/gic_pwd.c:471
#, c-format
msgid "%.*s%s%s.  Please try again.\n"
msgstr "%.*s%s%s. Bitte versuchen Sie es erneut.\n"

#: ../../src/lib/krb5/krb/parse.c:203
#, c-format
msgid "Principal %s is missing required realm"
msgstr "Principal %s fehlt erforderlicher Realm"

#: ../../src/lib/krb5/krb/parse.c:215
#, c-format
msgid "Principal %s has realm present"
msgstr "Für Principal %s ist Realm vorhanden"

#: ../../src/lib/krb5/krb/plugin.c:165
#, c-format
msgid "Invalid module specifier %s"
msgstr "ungültiger Modulbezeichner %s"

#: ../../src/lib/krb5/krb/plugin.c:402
#, c-format
msgid "Could not find %s plugin module named '%s'"
msgstr "Das Erweiterungsmodul %s namens »%s« konnte nicht gefunden werden."

#: ../../src/lib/krb5/krb/preauth2.c:1018
msgid "Unable to initialize preauth context"
msgstr "Vorauthentifizierungskontext konnte nicht initialisiert werden."

#: ../../src/lib/krb5/krb/preauth2.c:1032
#, c-format
msgid "Preauth module %s: %s"
msgstr "Vorauthentifizierungsmodul %s: %s"

#: ../../src/lib/krb5/krb/preauth_otp.c:510
msgid "Please choose from the following:\n"
msgstr "Bitte wählen Sie aus dem Folgenden aus:\n"

#: ../../src/lib/krb5/krb/preauth_otp.c:511
msgid "Vendor:"
msgstr "Anbieter:"

#: ../../src/lib/krb5/krb/preauth_otp.c:523
msgid "Enter #"
msgstr "Geben Sie # ein"

#: ../../src/lib/krb5/krb/preauth_otp.c:559
msgid "OTP Challenge:"
msgstr "Anforderung des Einwegpassworts:"

#: ../../src/lib/krb5/krb/preauth_otp.c:588
msgid "OTP Token PIN"
msgstr "Einwegpasswort-Token-PIN"

#: ../../src/lib/krb5/krb/preauth_otp.c:702
msgid "OTP value doesn't match any token formats"
msgstr "Wert des Einwegpassworts entspricht keinem Token-Format"

#: ../../src/lib/krb5/krb/preauth_otp.c:769
msgid "Enter OTP Token Value"
msgstr "Geben Sie den Wert des Einwegpasswort-Tokens an"

#: ../../src/lib/krb5/krb/preauth_otp.c:914
msgid "No supported tokens"
msgstr "keine unterstützten Token"

#: ../../src/lib/krb5/krb/preauth_sam2.c:49
msgid "Challenge for Enigma Logic mechanism"
msgstr "Anforderung für Enigma-Logic-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:53
msgid "Challenge for Digital Pathways mechanism"
msgstr "Anforderung für Digital-Pathway-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:57
msgid "Challenge for Activcard mechanism"
msgstr "Anforderung für Activcard-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:60
msgid "Challenge for Enhanced S/Key mechanism"
msgstr "Anforderung für erweiterten S/Key-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:63
msgid "Challenge for Traditional S/Key mechanism"
msgstr "Anforderung für traditionellen S/Key-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:66
#: ../../src/lib/krb5/krb/preauth_sam2.c:69
msgid "Challenge for Security Dynamics mechanism"
msgstr "Anforderung für Security-Dynamics-Mechanismus"

#: ../../src/lib/krb5/krb/preauth_sam2.c:72
msgid "Challenge from authentication server"
msgstr "Anforderung vom Authentifizierungsserver"

#: ../../src/lib/krb5/krb/preauth_sam2.c:166
msgid "SAM Authentication"
msgstr "SAM-Authentifizierung"

#: ../../src/lib/krb5/krb/rd_req_dec.c:145
#, c-format
msgid "Cannot find key for %s kvno %d in keytab"
msgstr ""
"Schlüssel für %s-KNVO %d kann nicht in der Schlüsseltabelle gefunden werden"

#: ../../src/lib/krb5/krb/rd_req_dec.c:150
#, c-format
msgid "Cannot find key for %s kvno %d in keytab (request ticket server %s)"
msgstr ""
"Schlüssel für %s-KNVO %d kann nicht in der Schlüsseltabelle gefunden werden "
"(angefragter Ticketserver %s)"

#: ../../src/lib/krb5/krb/rd_req_dec.c:175
#, c-format
msgid "Cannot decrypt ticket for %s using keytab key for %s"
msgstr ""
"Ticket für %s kann nicht mittels des Schlüsseltabellenschlüssels für %s "
"entschlüsselt werden"

#: ../../src/lib/krb5/krb/rd_req_dec.c:197
#, c-format
msgid "Server principal %s does not match request ticket server %s"
msgstr "Server-Principal %s passt nicht zum abgefragten Ticketserver %s"

#: ../../src/lib/krb5/krb/rd_req_dec.c:226
msgid "No keys in keytab"
msgstr "keine Schlüssel in der Schlüsseltabelle"

#: ../../src/lib/krb5/krb/rd_req_dec.c:229
#, c-format
msgid "Server principal %s does not match any keys in keytab"
msgstr ""
"Server-Principal %s hat keinen passenden Schlüssel in der Schlüsseltabelle"

#: ../../src/lib/krb5/krb/rd_req_dec.c:236
#, c-format
msgid ""
"Request ticket server %s found in keytab but does not match server principal "
"%s"
msgstr ""
"abgefragter Ticketserver %s wurde in der Schlüsseltabelle gefunden, er passte "
"jedoch nicht zu Server-Principal %s"

#: ../../src/lib/krb5/krb/rd_req_dec.c:241
#, c-format
msgid "Request ticket server %s not found in keytab (ticket kvno %d)"
msgstr ""
"Abgefragter Ticketserver %s wurde nicht in der Schlüsseltabelle gefunden "
"(Ticket KVNO %d)."

#: ../../src/lib/krb5/krb/rd_req_dec.c:247
#, c-format
msgid ""
"Request ticket server %s kvno %d not found in keytab; ticket is likely out "
"of date"
msgstr ""
"Abgefragter Ticketserver %s KVNO %d wurde nicht in der Schlüsseltabelle "
"gefunden; Ticket ist wahrscheinlich abgelaufen."

#: ../../src/lib/krb5/krb/rd_req_dec.c:252
#, c-format
msgid ""
"Request ticket server %s kvno %d not found in keytab; keytab is likely out "
"of date"
msgstr ""
"Abgefragter Ticketserver %s KVNO %d wurde nicht in der Schlüsseltabelle "
"gefunden; Schlüsseltabelle ist wahrscheinlich nicht mehr aktuell."

#: ../../src/lib/krb5/krb/rd_req_dec.c:261
#, c-format
msgid ""
"Request ticket server %s kvno %d found in keytab but not with enctype %s"
msgstr ""
"Abgefragter Ticketserver %s KVNO %d wurde in der Schlüsseltabelle gefunden, "
"jedoch nicht mit Verschlüsselungstyp %s."

#: ../../src/lib/krb5/krb/rd_req_dec.c:266
#, c-format
msgid ""
"Request ticket server %s kvno %d enctype %s found in keytab but cannot "
"decrypt ticket"
msgstr ""
"Abgefragter Ticketserver %s KVNO %d mit Verschlüsselungstyp %s in der "
"Schlüsseltabelle gefunden, Ticket kann jedoch nicht entschlüsselt werden."

#: ../../src/lib/krb5/krb/rd_req_dec.c:897
#, c-format
msgid "Encryption type %s not permitted"
msgstr "Verschlüsselungstyp %s nicht erlaubt"

#: ../../src/lib/krb5/os/expand_path.c:316
#, c-format
msgid "Can't find username for uid %lu"
msgstr "Zu UID %lu kann kein Benutzername gefunden werden."

#: ../../src/lib/krb5/os/expand_path.c:405
#: ../../src/lib/krb5/os/expand_path.c:421
msgid "Invalid token"
msgstr "ungültiges Token"

#: ../../src/lib/krb5/os/expand_path.c:506
msgid "variable missing }"
msgstr "Variable fehlt }"

#: ../../src/lib/krb5/os/locate_kdc.c:660
#, c-format
msgid "Cannot find KDC for realm \"%.*s\""
msgstr "KDC für Realm »%.*s« kann nicht gefunden werden"

#: ../../src/lib/krb5/os/sendto_kdc.c:475
#, c-format
msgid "Cannot contact any KDC for realm '%.*s'"
msgstr "für Realm »%.*s« kann nicht KDC kontaktiert werden"

#: ../../src/lib/krb5/rcache/rc_io.c:106
#, c-format
msgid "Cannot fstat replay cache file %s: %s"
msgstr "»fstat« für Antwortzwischenspeicherdatei %s nicht möglich: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:112
#, c-format
msgid ""
"Insecure mkstemp() file mode for replay cache file %s; try running this "
"program with umask 077"
msgstr ""
"unsicherer mkstemp()-Dateimodus für Antwortzwischenspeicherdatei %s; "
"versuchen Sie, dieses Programm mit der Umask 077 auszuführen"

#: ../../src/lib/krb5/rcache/rc_io.c:144
#, c-format
msgid "Cannot %s replay cache file %s: %s"
msgstr "%s der Wiederholungszwischenspeicherdatei %s nicht möglich: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:149
#, c-format
msgid "Cannot %s replay cache: %s"
msgstr "%s des Wiederholungszwischenspeichers nicht möglich: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:272
#, c-format
msgid "Insecure file mode for replay cache file %s"
msgstr "unsicherer Dateimodus für Wiederholungszwischenspeicherdatei %s"

#: ../../src/lib/krb5/rcache/rc_io.c:278
#, c-format
msgid "rcache not owned by %d"
msgstr "Rcache gehört nicht %d"

#: ../../src/lib/krb5/rcache/rc_io.c:402 ../../src/lib/krb5/rcache/rc_io.c:406
#: ../../src/lib/krb5/rcache/rc_io.c:411
#, c-format
msgid "Can't write to replay cache: %s"
msgstr ""
"in Wiederholungszwischenspeicherdatei kann nicht geschrieben werden: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:432
#, c-format
msgid "Cannot sync replay cache file: %s"
msgstr ""
"Wiederholungszwischenspeicherdatei kann nicht synchronisiert werden: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:451
#, c-format
msgid "Can't read from replay cache: %s"
msgstr "aus dem Wiederholungszwischenspeicher kann nicht gelesen werden: %s"

#: ../../src/lib/krb5/rcache/rc_io.c:482 ../../src/lib/krb5/rcache/rc_io.c:488
#: ../../src/lib/krb5/rcache/rc_io.c:493
#, c-format
msgid "Can't destroy replay cache: %s"
msgstr "Wiederholungszwischenspeicher kann nicht vernichtet werden: %s"

#: ../../src/plugins/kdb/db2/kdb_db2.c:245
#: ../../src/plugins/kdb/db2/kdb_db2.c:830
#, c-format
msgid "Unsupported argument \"%s\" for db2"
msgstr "nicht unterstütztes Argument »%s« für DB2"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:69
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:887
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1088
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1507
msgid "while reading kerberos container information"
msgstr "beim Lesen der Kerberos-Container-Information"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:129
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:143
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:504
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:518
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:151
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:166
msgid "while providing time specification"
msgstr "beim Bereitstellen der Zeitspezifikation"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:268
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:304
msgid "while creating policy object"
msgstr "beim Erstellen des Richtlinienobjekts"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:279
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1515
msgid "while reading realm information"
msgstr "beim Lesen der Realm-Information"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:348
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:407
msgid "while destroying policy object"
msgstr "beim Zerstören des Richtlinienobjekts"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:358
#, c-format
msgid "This will delete the policy object '%s', are you sure?\n"
msgstr "Dies wird das Richtlinienobjekt »%s« löschen, sind Sie sicher?\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:473
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:663
msgid "while modifying policy object"
msgstr "beim Ändern des Richtlinienobjekts"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:487
#, c-format
msgid "while reading information of policy '%s'"
msgstr "beim Lesen der Information der Richtlinie »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:692
msgid "while viewing policy"
msgstr "beim Betrachten der Richtlinie"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:701
#, c-format
msgid "while viewing policy '%s'"
msgstr "beim Betrachten der Richtlinie »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_policy.c:835
msgid "while listing policy objects"
msgstr "beim Auflisten der Richtlinienobjekte"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:453
#, c-format
msgid "for subtree while creating realm '%s'"
msgstr "für einen Teilbaum beim Erstellen von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:465
#, c-format
msgid "for container reference while creating realm '%s'"
msgstr "für Container-Bezug beim Erstellen von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:489
#, c-format
msgid "invalid search scope while creating realm '%s'"
msgstr "ungültiger Suchbereich beim Erstellen von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:504
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:823
#, c-format
msgid "'%s' is an invalid option\n"
msgstr "»%s« ist keine gültige Option\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:512
#, c-format
msgid "Initializing database for realm '%s'\n"
msgstr "Datenbank für Realm »%s« wird initialisiert\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:536
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:696
#, c-format
msgid "while creating realm '%s'"
msgstr "beim Erstellen von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:556
#, c-format
msgid "Enter DN of Kerberos container: "
msgstr "Geben Sie die den DN des Kerberos-Containers ein: "

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:591
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:894
#, c-format
msgid "while reading information of realm '%s'"
msgstr "beim Lesen der Information von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:733
msgid "while reading Kerberos container information"
msgstr "beim Lesen der Kerberos-Container-Information"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:774
#, c-format
msgid "for subtree while modifying realm '%s'"
msgstr "für einen Teilbaum beim Ändern von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:785
#, c-format
msgid "for container reference while modifying realm '%s'"
msgstr "für Container-Bezug beim Ändern von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:812
#, c-format
msgid "specified for search scope while modifying information of realm '%s'"
msgstr ""
"angegeben für Suchbereich, während die Information für Realm »%s« geändert "
"wird"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:851
#, c-format
msgid "while modifying information of realm '%s'"
msgstr "beim Ändern der Information von Realm »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:940
msgid "Realm Name"
msgstr "Realm-Name"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:943
msgid "Subtree"
msgstr "Teilbaum"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:946
msgid "Principal Container Reference"
msgstr "Principal-Container-Bezug"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:951
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:953
msgid "SearchScope"
msgstr "Suchbereich"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:951
msgid "Invalid !"
msgstr "ungültig!"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:958
msgid "KDC Services"
msgstr "KDC-Dienste"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:973
msgid "Admin Services"
msgstr "Administratordienste"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:988
msgid "Passwd Services"
msgstr "Passwortdienste"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1004
msgid "Maximum Ticket Life"
msgstr "maximale Ticketlebensdauer"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1009
msgid "Maximum Renewable Life"
msgstr "maximale verlängerbare Lebensdauer"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1016
msgid "Ticket flags"
msgstr "Ticket-Flags"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1095
msgid "while listing realms"
msgstr "beim Auflisten der Realms"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1439
msgid "while adding entries to database"
msgstr "beim Hinzufügen von Einträgen zur Datenbank"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1480
#, c-format
msgid "Deleting KDC database of '%s', are you sure?\n"
msgstr ""
"Sind Sie sicher, dass die KDC-Datenbank von »%s« gelöscht werden soll?\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1491
#, c-format
msgid "OK, deleting database of '%s'...\n"
msgstr "OK, die Datenbank von »%s« wird gelöscht …\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1524
#, c-format
msgid "deleting database of '%s'"
msgstr "Die Datenbank von »%s« wird gelöscht."

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_realm.c:1529
#, c-format
msgid "** Database of '%s' destroyed.\n"
msgstr "** Datenbank von »%s« vernichtet\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:81
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:88
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:96
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:104
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:120
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:148
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:227
msgid "while setting service object password"
msgstr "beim Setzen des Passworts für das Dienstobjekt"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:140
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:477
#, c-format
msgid "Password for \"%s\""
msgstr "Passwort für »%s«"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:143
#, c-format
msgid "Re-enter password for \"%s\""
msgstr "Geben Sie das Passwort für »%s« erneut ein."

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:154
#, c-format
msgid "%s: Invalid password\n"
msgstr "%s: ungültiges Passwort\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:170
msgid "Failed to convert the password to hexadecimal"
msgstr "Das Umwandeln des Passworts in Dezimalschreibweise ist fehlgeschlagen."

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:183
#, c-format
msgid "Failed to open file %s: %s"
msgstr "Datei %s konnte nicht geöffnet werden: %s"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:205
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:247
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:256
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:283
msgid "Failed to write service object password to file"
msgstr ""
"Schreiben des Passworts für das Dienstobjekt in eine Datei fehlgeschlagen"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:211
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:268
msgid "Error reading service object password file"
msgstr "Fehler beim Lesen der Passwortdatei für das Dienstobjekt"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_services.c:236
#, c-format
msgid "Error creating file %s"
msgstr "Fehler beim Erstellen der Datei %s"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:105
#, c-format
msgid ""
"Usage: kdb5_ldap_util [-D user_dn [-w passwd]] [-H ldapuri]\n"
"\tcmd [cmd_options]\n"
"create          [-subtrees subtree_dn_list] [-sscope search_scope] [-"
"containerref container_reference_dn]\n"
"\t\t[-m|-P password|-sf stashfilename] [-k mkeytype] [-kv mkeyVNO] [-s]\n"
"\t\t[-maxtktlife max_ticket_life] [-maxrenewlife max_renewable_ticket_life]\n"
"\t\t[ticket_flags] [-r realm]\n"
"modify          [-subtrees subtree_dn_list] [-sscope search_scope] [-"
"containerref container_reference_dn]\n"
"\t\t[-maxtktlife max_ticket_life] [-maxrenewlife max_renewable_ticket_life]\n"
"\t\t[ticket_flags] [-r realm]\n"
"view            [-r realm]\n"
"destroy                [-f] [-r realm]\n"
"list\n"
"stashsrvpw      [-f filename] service_dn\n"
"create_policy   [-r realm] [-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags] policy\n"
"modify_policy   [-r realm] [-maxtktlife max_ticket_life]\n"
"\t\t[-maxrenewlife max_renewable_ticket_life] [ticket_flags] policy\n"
"view_policy     [-r realm] policy\n"
"destroy_policy  [-r realm] [-force] policy\n"
"list_policy     [-r realm]\n"
msgstr ""
"Aufruf: kdb5_ldap_util [-D Benutzer-DN [-w Passwort]] [-H LDAP-URI]\n"
"\tcmd [Befehlsoptionen]\n"
"create          [-subtrees DN-Liste_Teilbäume] [-sscope Suchbereich] [-"
"containerref Container-Bezug-DN]\n"
"\t\t[-m|-P Passwort|-sf Ablagedateiname] [-k mkeytype] [-kv mkeyVNO] [-s]\n"
"\t\t[-maxtktlife maximale_Ticketlebensdauer]\n"
"\t\t[-maxrenewlife maximale_Dauer_bis_zum_Erneuern_des_Tickets]\n"
"\t\t[Ticket_Flags] [-r Realm]\n"
"modify          [-subtrees DN-Liste_Teilbäume] [-sscope Suchbereich] [-"
"containerref Container-Bezug-DN]\n"
"\t\t[-maxtktlife maximale_Ticketlebensdauer]\n"
"\t\t[-maxrenewlife maximale_Dauer_bis_zum_Erneuern_des_Tickets]\n"
"\t\t[Ticket_Flags] [-r Realm]\n"
"view            [-r Realm]\n"
"destroy                [-f] [-r Realm]\n"
"list\n"
"stashsrvpw      [-f Dateiname] Dienst-DN\n"
"create_policy   [-r Realm] [-maxtktlife maximale_Ticketlebensdauer]\n"
"\t\t[-maxrenewlife maximale_Dauer_bis_zum_Erneuern_des_Tickets]\n"
"\t\t[Ticket_Flags] Richtlinie\n"
"modify_policy   [-r Realm] [-maxtktlife maximale_Ticketlebensdauer]\n"
"\t\t[-maxrenewlife maximale_Dauer_bis_zum_Erneuern_des_Tickets]\n"
"\t\t[Ticket_Flags] Richtlinie\n"
"view_policy     [-r Realm] Richtlinie\n"
"destroy_policy  [-r Realm] [-force] Richtlinie\n"
"list_policy     [-r Realm]\n"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:325
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:333
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:341
msgid "while reading ldap parameters"
msgstr "beim Lesen der LDAP-Parameter"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:439
msgid "while initializing error handling"
msgstr "beim Initialisieren der Fehlerbehandlung"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:447
msgid "while initializing ldap handle"
msgstr "beim Initialisieren des LDAP-Identifikators"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:461
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:470
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:483
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:525
msgid "while retrieving ldap configuration"
msgstr "beim Abfragen der LDAP-Konfiguration"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:500
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:507
#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:516
msgid "while initializing server list"
msgstr "beim Initialisieren der Serverliste"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:547
msgid "while setting up lib handle"
msgstr "ein Einrichten der BibliotheksIdentifikators"

#: ../../src/plugins/kdb/ldap/ldap_util/kdb5_ldap_util.c:556
msgid "while reading ldap configuration"
msgstr "beim Lesen der LDAP-Konfiguration"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:68
msgid "Unable to read Kerberos container"
msgstr "Kerberos-Container kann nicht gelesen werden"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:74
msgid "Unable to read Realm"
msgstr "Realm kann nicht gelesen werden"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:215
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:73
msgid "Error processing LDAP DB params:"
msgstr "Fehler beim Verarbeiten der LDAP-Datenbankparameter:"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap.c:222
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:80
msgid "Error reading LDAP server params:"
msgstr "Fehler beim Lesen der LDAP-Server-Parameters:"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:64
msgid "LDAP bind dn value missing"
msgstr "LDAP-Bindungs-DN-Wert fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:69
msgid "LDAP bind password value missing"
msgstr "LDAP-Bindungs-Passwortwert fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:77
msgid "Error reading password from stash: "
msgstr "Fehler beim Lesen des Passworts aus der Ablage: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:85
msgid "Service password length is zero"
msgstr "Länge des Dienstpassworts ist Null"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:145
#, c-format
msgid "Cannot bind to LDAP server '%s' with SASL mechanism '%s': %s"
msgstr ""
"mit LDAP-Server »%s« kann keine Verbindung mit SASL-Mechanismus »%s« "
"hergestellt werden: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:158
#, c-format
msgid "Cannot bind to LDAP server '%s' as '%s': %s"
msgstr ""
"mit LDAP-Server »%s« kann keine Verbindung als »%s« hergestellt werden: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/kdb_ldap_conn.c:183
#, c-format
msgid "Cannot create LDAP handle for '%s': %s"
msgstr "LDAP-Identifikator für »%s« kann nicht erstellt werden: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_create.c:131
msgid "could not complete roll-back, error deleting Kerberos Container"
msgstr ""
"Zurücksetzen kann nicht abgeschlossen werden, Fehler beim Löschen des "
"Kerberos-Containers"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:56
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:67
msgid "Error reading kerberos container location from krb5.conf"
msgstr ""
"Fehler beim Lesen des Kerberos-Container-Speicherorts aus der »krb5.conf«."

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_krbcontainer.c:75
msgid "Kerberos container location not specified"
msgstr "Kerberos-Container-Speicherort nicht angegeben"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:55
#, c-format
msgid "Error reading '%s' attribute: %s"
msgstr "Fehler beim Lesen des Attributs »%s«: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:218
msgid "KDB module requires -update argument"
msgstr "KDB-Modul benötigt Argument »-update«"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:224
#, c-format
msgid "'%s' value missing"
msgstr "Wert »%s« fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:282
#, c-format
msgid "unknown option '%s'"
msgstr "unbekannte Option »%s«"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_misc.c:342
msgid "Minimum connections required per server is 2"
msgstr "Die benötigte Mindestanzahl von Verbindungen pro Server ist zwei"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal.c:159
msgid "Default realm not set"
msgstr "Standard-Realm nicht gesetzt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal.c:262
msgid "DN information missing"
msgstr "DN-Information fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:108
msgid "Principal does not belong to realm"
msgstr "Principal gehört nicht zum Realm"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:278
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:287
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:295
#, c-format
msgid "%s option not supported"
msgstr "Option %s wird nicht unterstützt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:302
#, c-format
msgid "unknown option: %s"
msgstr "unbekannte Option: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:309
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:316
#, c-format
msgid "%s option value missing"
msgstr "Wert der Option %s fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:542
msgid "Principal does not belong to the default realm"
msgstr "Principal gehört nicht zum Standard-Realm"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:610
#, c-format
msgid ""
"operation can not continue, more than one entry with principal name \"%s\" "
"found"
msgstr ""
"Die Aktion kann nicht fortfahren, da mehr als ein Principal namens »%s« "
"gefunden wurde."

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:673
#, c-format
msgid "'%s' not found: "
msgstr "»%s« nicht gefunden: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:751
msgid "DN is out of the realm subtree"
msgstr "DN liegt außerhalb ders Teilbaums des Realms"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:807
#, c-format
msgid "ldap object is already kerberized"
msgstr "LDAP-Objekt ist bereits an Kerberos angepasst"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:827
#, c-format
msgid ""
"link information can not be set/updated as the kerberos principal belongs to "
"an ldap object"
msgstr ""
"Verweisinformation kann nicht eingerichtet/aktualisiert werden, da der "
"Kerberos-Principal zu einem LDAP-Objekt gehört."

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:842
#, c-format
msgid "Failed getting object references"
msgstr "Holen von Objektbezügen fehlgeschlagen"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:849
#, c-format
msgid "kerberos principal is already linked to a ldap object"
msgstr "Kerberos-Principal ist bereits mit einem LDAP-Objekt verknüpft"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1167
msgid "ticket policy object value: "
msgstr "Wert des Ticket-Richtlinienobjekts: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1215
#, c-format
msgid "Principal delete failed (trying to replace entry): %s"
msgstr ""
"Löschen des Principals fehlgeschlagen (es wird versucht, den Eintrag zu "
"ersetzen): %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1225
#, c-format
msgid "Principal add failed: %s"
msgstr "Hinzufügen des Principals fehlgeschlagen: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1263
#, c-format
msgid "User modification failed: %s"
msgstr "Änderung des Benutzers fehlgeschlagen: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1336
msgid "Error reading ticket policy. "
msgstr "Fehler beim Lesen der Ticket-Richtlinie"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_principal2.c:1402
#, c-format
msgid "unable to decode stored principal key data (%s)"
msgstr ""
"Die gespeicherten Schlüsseldaten des Principals (%s) konnten nicht "
"dekodiert werden."

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:223
msgid "Realm information not available"
msgstr "Realm-Information nicht verfügbar"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:294
msgid "Error reading ticket policy: "
msgstr "Fehler beim Lesen der Ticket-Richtlinie:"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:307
#, c-format
msgid "Realm Delete FAILED: %s"
msgstr "Löschen des Realms FEHLGESCHLAGEN: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:387
msgid "subtree value: "
msgstr "Wert des Teilbaums: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:404
msgid "container reference value: "
msgstr "Wert des Container-Bezugs: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:487
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:550
msgid "Kerberos Container information is missing"
msgstr "Kerberos-Container-Information fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:499
msgid "Invalid Kerberos container DN"
msgstr "ungültiger Kerberos-Container-DN"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:515
#, c-format
msgid "Kerberos Container create FAILED: %s"
msgstr "Erstellen des Kerberos-Containers FEHLGESCHLAGEN: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:558
#, c-format
msgid "Kerberos Container delete FAILED: %s"
msgstr "Löschen des Kerberos-Containers FEHLGESCHLAGEN: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_realm.c:634
msgid "realm object value: "
msgstr "Wert des Realm-Objekts: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:48
msgid "Not a hexadecimal password"
msgstr "kein hexadezimales Passwort"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:55
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:66
msgid "Password corrupt"
msgstr "Passwort beschädigt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:93
#, c-format
msgid "Cannot open LDAP password file '%s': %s"
msgstr "LDAP-Passwortdatei »%s« kann nicht geöffnet werden: %s"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_service_stash.c:123
#, c-format
msgid "Bind DN entry '%s' missing in LDAP password file '%s'"
msgstr "Bind-DN-Eintrag »%s« fehlt in der LDAP-Passwortdatei »%s«"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:56
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:132
msgid "Ticket Policy Name missing"
msgstr "Ticket-Richtlinienname fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:144
#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:221
msgid "ticket policy object: "
msgstr "Ticket-Richtlinienobjekt: "

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:209
msgid "Ticket Policy Object information missing"
msgstr "Ticket-Richtlinienobjekt-Information fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:300
msgid "Ticket Policy Object DN missing"
msgstr "DN des Ticket-Richtlinienobjekts fehlt"

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:327
msgid "Delete Failed: One or more Principals associated with the Ticket Policy"
msgstr ""
"Löschen fehlgeschlagen: Ein oder mehrere Principals gehören zur Ticket-"
"Richtlinie."

#: ../../src/plugins/kdb/ldap/libkdb_ldap/ldap_tkt_policy.c:435
msgid "Error reading container object: "
msgstr "Fehler beim Lesen des Container-Objekts: "

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_nss.c:667
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:652
#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:4153
msgid "Pass phrase for"
msgstr "Passphrase für"

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1081
#, c-format
msgid "Cannot create cert chain: %s"
msgstr "Zertifikatskette kann nicht erstellt werden: %s"

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1408
msgid "Invalid pkinit packet: octet string expected"
msgstr "ungültiges Pkinit-Paket: Achtbit-Zeichenkette erwartet"

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:1427
msgid "wrong oid\n"
msgstr "falsche OID\n"

#: ../../src/plugins/preauth/pkinit/pkinit_crypto_openssl.c:5994
#, c-format
msgid "unknown code 0x%x"
msgstr "unbekannter Code 0x%x"

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:424
#, c-format
msgid "Unsupported type while processing '%s'\n"
msgstr "nicht unterstützter Typ bei der Verarbeitung von »%s«\n"

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:465
msgid "Internal error parsing X509_user_identity\n"
msgstr "interner Fehler beim Auswerten von »X509_user_identity«\n"

#: ../../src/plugins/preauth/pkinit/pkinit_identity.c:560
msgid "No user identity options specified"
msgstr "keine Optionen der Nutzeridentität angegeben"

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:414
msgid "Pkinit request not signed, but client not anonymous."
msgstr "Pkinit-Anfrage nicht signiert, Client ist jedoch nicht anonym"

# DH = Diffie-Hellman
#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:447
msgid "Anonymous pkinit without DH public value not supported."
msgstr "Anonymes Pkinit wird nicht ohne öffentlichen DH-Wert unterstützt."

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1147
#, c-format
msgid "No pkinit_identity supplied for realm %s"
msgstr "Für Realm %s wird keine »pkinit_identity« bereitgestellt."

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1158
#, c-format
msgid "No pkinit_anchors supplied for realm %s"
msgstr "Für Realm %s werden keine »pkinit_anchors« bereitgestellt."

#: ../../src/plugins/preauth/pkinit/pkinit_srv.c:1346
msgid "No realms configured correctly for pkinit support"
msgstr "Für Pkinit-Unterstützung wurden keine Realms korrekt konfiguriert."

#: ../../src/slave/kprop.c:85
#, c-format
msgid ""
"\n"
"Usage: %s [-r realm] [-f file] [-d] [-P port] [-s srvtab] slave_host\n"
"\n"
msgstr ""
"\n"
"Aufruf: %s [-r Realm] [-f Datei] [-d] [-P Port] [-s Dienstschlüsseltabelle] "
"untergeordneter_Rechner\n"
"\n"

#: ../../src/slave/kprop.c:114
#, c-format
msgid "Database propagation to %s: SUCCEEDED\n"
msgstr "Datenbankverbreitung auf %s: ERFOLGREICH\n"

#: ../../src/slave/kprop.c:187
msgid "while setting client principal name"
msgstr "beim Setzen des Client-Principal-Namens"

#: ../../src/slave/kprop.c:194 ../../src/slave/kprop.c:209
msgid "while setting client principal realm"
msgstr "beim Setzen des Client-Principal-Realms"

#: ../../src/slave/kprop.c:217
#, c-format
msgid "while opening credential cache %s"
msgstr "beim Öffnen des Anmeldedatenzwischenspeichers %s"

#: ../../src/slave/kprop.c:233
msgid "while setting server principal name"
msgstr "beim Setzen des Server-Principal-Namens"

#: ../../src/slave/kprop.c:255
msgid "while resolving keytab"
msgstr "beim Ermitteln der Schlüsseltabelle"

#: ../../src/slave/kprop.c:264
msgid "while getting initial credentials\n"
msgstr "beim Holen der Anfangsanmeldedaten\n"

#: ../../src/slave/kprop.c:301
msgid "while creating socket"
msgstr "beim Erstellen eines Sockets"

#: ../../src/slave/kprop.c:317
msgid "while converting server address"
msgstr "beim Umwandeln der Server-Adresse"

#: ../../src/slave/kprop.c:327
msgid "while connecting to server"
msgstr "beim Verbinden mit dem Server"

#: ../../src/slave/kprop.c:334 ../../src/slave/kpropd.c:1215
msgid "while getting local socket address"
msgstr "beim Holen der lokalen Socket-Adresse"

#: ../../src/slave/kprop.c:339
msgid "while converting local address"
msgstr "beim Umwandeln der lokalen Socket-Adresse"

#: ../../src/slave/kprop.c:362
msgid "in krb5_auth_con_setaddrs"
msgstr "in »krb5_auth_con_setaddrs«"

#: ../../src/slave/kprop.c:370
msgid "while authenticating to server"
msgstr "beim Authentifizieren am Server"

#: ../../src/slave/kprop.c:374 ../../src/slave/kprop.c:573
#: ../../src/slave/kpropd.c:1521
#, c-format
msgid "Generic remote error: %s\n"
msgstr "allgemeiner ferner Fehler: %s\n"

#: ../../src/slave/kprop.c:380 ../../src/slave/kprop.c:579
msgid "signalled from server"
msgstr "signalisiert vom Server"

#: ../../src/slave/kprop.c:382 ../../src/slave/kprop.c:581
#, c-format
msgid "Error text from server: %s\n"
msgstr "Fehlermeldung vom Server: %s\n"

#: ../../src/slave/kprop.c:410
#, c-format
msgid "allocating database file name '%s'"
msgstr "Datenbankdateiname »%s« wird reserviert"

#: ../../src/slave/kprop.c:416
#, c-format
msgid "while trying to open %s"
msgstr "beim Versuch, %s zu öffnen"

#: ../../src/slave/kprop.c:423
msgid "database locked"
msgstr "Datenbank gesperrt"

#: ../../src/slave/kprop.c:426 ../../src/slave/kpropd.c:525
#, c-format
msgid "while trying to lock '%s'"
msgstr "beim Versuch, »%s« zu sperren"

#: ../../src/slave/kprop.c:430 ../../src/slave/kprop.c:438
#, c-format
msgid "while trying to stat %s"
msgstr "beim Versuch, »stat« für %s auszuführen"

#: ../../src/slave/kprop.c:434
msgid "while trying to malloc data_ok_fn"
msgstr "beim Versuch, Speicher für »data_ok_fn« zu reservieren"

#: ../../src/slave/kprop.c:443
#, c-format
msgid "'%s' more recent than '%s'."
msgstr "»%s« ist aktueller als »%s«."

#: ../../src/slave/kprop.c:459
#, c-format
msgid "while unlocking database '%s'"
msgstr "beim Entsperren von Datenbank »%s«"

#: ../../src/slave/kprop.c:492 ../../src/slave/kprop.c:493
msgid "while encoding database size"
msgstr "beim Aufbereiten der Datenbankgröße"

#: ../../src/slave/kprop.c:501
msgid "while sending database size"
msgstr "beim Senden der Datenbankgröße"

#: ../../src/slave/kprop.c:511
msgid "while allocating i_vector"
msgstr "beim Reservieren von »i_vector«"

#: ../../src/slave/kprop.c:534
#, c-format
msgid "while sending database block starting at %d"
msgstr "beim Senden des Datenbankblocks, der bei %d beginnt"

#: ../../src/slave/kprop.c:544
msgid "Premature EOF found for database file!"
msgstr "vorzeitiges EOF für Datenbankdatei gefunden!"

#: ../../src/slave/kprop.c:557
msgid "while reading response from server"
msgstr "beim Lesen der Antwort vom Servers"

#: ../../src/slave/kprop.c:568
msgid "while decoding error response from server"
msgstr "beim Aufschlüsseln der Fehlerantwort vom Server"

#: ../../src/slave/kprop.c:599
#, c-format
msgid "Kpropd sent database size %d, expecting %d"
msgstr "Kpropd sendet Datenbankgröße %d, erwartet wurde %d"

#: ../../src/slave/kprop.c:643
msgid "while allocating filename for update_last_prop_file"
msgstr "beim Reservieren des Dateinamens für »update_last_prop_file«"

#: ../../src/slave/kprop.c:648
#, c-format
msgid "while creating 'last_prop' file, '%s'"
msgstr "beim Erstellen der Datei »last_prop«, »%s«"

#: ../../src/slave/kpropd.c:170
#, c-format
msgid ""
"\n"
"Usage: %s [-r realm] [-s srvtab] [-dS] [-f slave_file]\n"
msgstr ""
"\n"
"Aufruf: %s [-r Realm] [-s Dienstschlüsseltabelle] [-dS] [-f "
"untergeordnete_Datei]\n"

#: ../../src/slave/kpropd.c:172
#, c-format
msgid "\t[-F kerberos_db_file ] [-p kdb5_util_pathname]\n"
msgstr "\t[-F Kerberos-Datenbankdatei ] [-p KDB5-Hilfswerkzeugpfadname]\n"

#: ../../src/slave/kpropd.c:173
#, c-format
msgid "\t[-x db_args]* [-P port] [-a acl_file]\n"
msgstr "\t[-x Datenbankargumente]* [-P Port] [-a ACL-Datei]\n"

#: ../../src/slave/kpropd.c:174
#, c-format
msgid "\t[-A admin_server]\n"
msgstr "\t[-A Serveradministrator]\n"

#: ../../src/slave/kpropd.c:215
#, c-format
msgid "Killing fullprop child (%d)\n"
msgstr "Beenden des Fullprop-Kindprozesses (%d) wird erzwungen\n"

#: ../../src/slave/kpropd.c:244
msgid "while checking if stdin is a socket"
msgstr "beim Prüfen, ob die Standardeingabe ein Socket ist"

#: ../../src/slave/kpropd.c:262
#, c-format
msgid "ready\n"
msgstr "bereit\n"

#: ../../src/slave/kpropd.c:272
#, c-format
msgid "Could not open /dev/null: %s"
msgstr "/dev/null konnte nicht geöffnet werden: %s"

#: ../../src/slave/kpropd.c:279
#, c-format
msgid "Could not dup the inetd socket: %s"
msgstr "Das Inetd-Socket konnte nicht dupliziert werden: %s"

#: ../../src/slave/kpropd.c:314 ../../src/slave/kpropd.c:327
msgid "do_iprop failed.\n"
msgstr "»do_iprop« fehlgeschlagen\n"

#: ../../src/slave/kpropd.c:366
#, c-format
msgid "getaddrinfo: %s\n"
msgstr "getaddrinfo: %s\n"

#: ../../src/slave/kpropd.c:372
msgid "while obtaining socket"
msgstr "beim Erlangen des Sockets"

#: ../../src/slave/kpropd.c:378
msgid "while setting SO_REUSEADDR option"
msgstr "beim Setzen der Option SO_REUSEADDR"

#: ../../src/slave/kpropd.c:386
msgid "while unsetting IPV6_V6ONLY option"
msgstr "beim Entfernen der Option IPV6_V6ONLY"

#: ../../src/slave/kpropd.c:391
msgid "while binding listener socket"
msgstr "beim Anbinden an das auf Verbindung wartende Socket"

#: ../../src/slave/kpropd.c:402
#, c-format
msgid "waiting for a kprop connection\n"
msgstr "warten auf Kprop-Verbindung\n"

#: ../../src/slave/kpropd.c:408
msgid "while accepting connection"
msgstr "beim Akzeptieren der Verbindung"

#: ../../src/slave/kpropd.c:414
msgid "while forking"
msgstr "beim Erzeugen eines Kindprozesses"

#: ../../src/slave/kpropd.c:429
#, c-format
msgid "waitpid() failed to wait for doit() (%d %s)\n"
msgstr "waitpid() schlug beim Warten auf doit() fehl (%d %s)\n"

#: ../../src/slave/kpropd.c:433
msgid "while waiting to receive database"
msgstr "beim Warten auf den Erhalt der Datenbank"

#: ../../src/slave/kpropd.c:437
#, c-format
msgid "Database load process for full propagation completed.\n"
msgstr ""
"Der Datenbankladeprozess für eine vollständige Verbreitung ist "
"abgeschlossen.\n"

#: ../../src/slave/kpropd.c:471
#, c-format
msgid ""
"%s: Standard input does not appear to be a network socket.\n"
"\t(Not run from inetd, and missing the -S option?)\n"
msgstr ""
"%s: Bei der Standardeingabe scheint es sich nicht um ein Netzwerk-Socket zu\n"
"\thandeln (läuft nicht aus Inetd und die Option -S fehlt?).\n"

#: ../../src/slave/kpropd.c:485
msgid "while attempting setsockopt (SO_KEEPALIVE)"
msgstr "beim Versuch, »setsockopt« auszuführen (SO_KEEPALIVE)"

#: ../../src/slave/kpropd.c:490
#, c-format
msgid "Connection from %s"
msgstr "Verbindung von %s"

#: ../../src/slave/kpropd.c:510
#, c-format
msgid "Rejected connection from unauthorized principal %s\n"
msgstr "Zurückgewiesene Verbindung von nicht autorisiertem Principal %s\n"

#: ../../src/slave/kpropd.c:514
#, c-format
msgid "Rejected connection from unauthorized principal %s"
msgstr "Zurückgewiesene Verbindung von nicht authorisiertem Principal %s"

#: ../../src/slave/kpropd.c:531
#, c-format
msgid "while opening database file, '%s'"
msgstr "beim Öffnen der Datenbankdatei, »%s«"

#: ../../src/slave/kpropd.c:537
#, c-format
msgid "while renaming %s to %s"
msgstr "beim Umbenennen von %s in %s"

#: ../../src/slave/kpropd.c:543
#, c-format
msgid "while downgrading lock on '%s'"
msgstr "beim Downgrade der Sperre auf »%s«"

#: ../../src/slave/kpropd.c:550
#, c-format
msgid "while unlocking '%s'"
msgstr "beim Aufheben der Sperre »%s«"

#: ../../src/slave/kpropd.c:562
msgid "while sending # of received bytes"
msgstr "beim Senden n empfangener Byte"

#: ../../src/slave/kpropd.c:568
msgid "while trying to close database file"
msgstr "beim Versuch, die Datenbankdatei zu schließen"

#: ../../src/slave/kpropd.c:624
#, c-format
msgid "Incremental propagation enabled\n"
msgstr "inkrementelle Verbreitung aktiviert\n"

#: ../../src/slave/kpropd.c:634
msgid "Unable to get default realm"
msgstr "Standard-Realm kann nicht geholt werden"

#: ../../src/slave/kpropd.c:647
#, c-format
msgid "%s: unable to get kiprop host based service name for realm %s\n"
msgstr ""
"%s: Kiprop-rechnerbasierter Dienstname für Realm %s kann nicht geholt "
"werden\n"

#: ../../src/slave/kpropd.c:658
msgid "while trying to construct host service principal"
msgstr "beim Versuch, den Rechnerdienst-Principal zu erstellen"

#: ../../src/slave/kpropd.c:672
msgid "while determining local service principal name"
msgstr "beim Bestimmen des lokalen Dienst-Principal-Namens"

#: ../../src/slave/kpropd.c:692
#, c-format
msgid "Initializing kadm5 as client %s\n"
msgstr "Kadm5 wird als Client %s initialisiert\n"

#: ../../src/slave/kpropd.c:706
#, c-format
msgid "kadm5 initialization failed!\n"
msgstr "Initialisierung von Kadm5 fehlgeschlagen!\n"

#: ../../src/slave/kpropd.c:715
msgid "while attempting to connect to master KDC ... retrying"
msgstr ""
"beim Versuch, eine Verbindung zum Master-KDC aufzubauen … wird erneut "
"versucht"

#: ../../src/slave/kpropd.c:719
#, c-format
msgid "Sleeping %d seconds to re-initialize kadm5 (RPC ERROR)\n"
msgstr ""
"Um Kadm5 neu zu initialisieren, wird %d Sekunden gewartet (RPC-FEHLER).\n"

#: ../../src/slave/kpropd.c:735
#, c-format
msgid "while initializing %s interface, retrying"
msgstr "beim Initialisieren der Schnittstelle %s, wird erneut versucht"

#: ../../src/slave/kpropd.c:739
#, c-format
msgid "Sleeping %d seconds to re-initialize kadm5 (krb5kdc not running?)\n"
msgstr ""
"Um Kadm5 neu zu initialisieren, wird %d Sekunden gewartet (läuft Krb5kdc "
"nicht?).\n"

#: ../../src/slave/kpropd.c:749
#, c-format
msgid "kadm5 initialization succeeded\n"
msgstr "Initialisieren von Kadm5 erfolgreich\n"

#: ../../src/slave/kpropd.c:771
msgid "reading update log header"
msgstr "Aktualisierungsprotokollkopfzeilen werden gelesen"

#: ../../src/slave/kpropd.c:782
#, c-format
msgid "Calling iprop_get_updates_1 (sno=%u sec=%u usec=%u)\n"
msgstr "»iprop_get_updates_1()« wird aufgerufen (sno=%u sec=%u usec=%u)\n"

#: ../../src/slave/kpropd.c:792
msgid "iprop_get_updates call failed"
msgstr "Aufruf von »iprop_get_updates« fehlgeschlagen"

#: ../../src/slave/kpropd.c:798
#, c-format
msgid "Reinitializing iprop because get updates failed\n"
msgstr ""
"Iprop wird neu initialisiert, da Aktualisierungen fehlgeschlagen sind\n"

#: ../../src/slave/kpropd.c:819
#, c-format
msgid "Still waiting for full resync\n"
msgstr ""
"Es wird immer noch auf das vollständige erneute Synchronisieren gewartet.\n"

#: ../../src/slave/kpropd.c:824
#, c-format
msgid "Full resync needed\n"
msgstr "erneutes vollständiges Synchronisieren erforderlich\n"

#: ../../src/slave/kpropd.c:825
msgid "kpropd: Full resync needed."
msgstr "Kpropd: erneutes vollständiges Synchronisieren erforderlich"

#: ../../src/slave/kpropd.c:830
msgid "iprop_full_resync call failed"
msgstr "Aufruf von »iprop_full_resync« fehlgeschlagen"

#: ../../src/slave/kpropd.c:841
#, c-format
msgid "Full resync request granted\n"
msgstr "Anfrage nach vollständigem erneuten Synchronisieren genehmigt\n"

#: ../../src/slave/kpropd.c:842
msgid "Full resync request granted."
msgstr "Anfrage nach vollständigem erneuten Synchronisieren genehmigt"

# FIXME s/backoff/back-off/
#: ../../src/slave/kpropd.c:851
#, c-format
msgid "Exponential backoff\n"
msgstr "exponentieller Wartezyklus\n"

#: ../../src/slave/kpropd.c:857
#, c-format
msgid "Full resync permission denied\n"
msgstr "vollständiges erneutes Synchronisieren nicht gestattet\n"

#: ../../src/slave/kpropd.c:858
msgid "Full resync, permission denied."
msgstr "vollständiges erneutes Synchronisieren, nicht gestattet"

#: ../../src/slave/kpropd.c:863
#, c-format
msgid "Full resync error from master\n"
msgstr "Fehler beim vollständigen erneuten Synchronisieren vom Master\n"

#: ../../src/slave/kpropd.c:864
msgid " Full resync, error returned from master KDC."
msgstr ""
"vollständiges erneutes Synchronisieren, das Master-KDC gab einen Fehler "
"zurück"

#: ../../src/slave/kpropd.c:872
#, c-format
msgid "Full resync invalid result from master\n"
msgstr ""
"Beim vollständigen erneuten Synchronisieren gab der Master ein ungültiges "
"Ergebnis zurück.\n"

#: ../../src/slave/kpropd.c:874
msgid "Full resync, invalid return from master KDC."
msgstr ""
"vollständiges erneutes Synchronisieren, ungültiger Rückgabewert vom Master-"
"KDC"

#: ../../src/slave/kpropd.c:890
#, c-format
msgid "Got incremental updates (sno=%u sec=%u usec=%u)\n"
msgstr ""
"inkrementelle Aktualisierungen erhalten (sno=%u sec=%u usec=%u)\n"

#: ../../src/slave/kpropd.c:902
#, c-format
msgid "ulog_replay failed (%s), updates not registered\n"
msgstr ""
"»ulog_replay« fehlgeschlagen (%s), Aktualisierungen nicht registriert\n"

#: ../../src/slave/kpropd.c:905
#, c-format
msgid "ulog_replay failed (%s), updates not registered."
msgstr "»ulog_replay« fehlgeschlagen (%s), Aktualisierungen nicht registriert"

#: ../../src/slave/kpropd.c:914
#, c-format
msgid "Incremental updates: %d updates / %lu us"
msgstr "inkrementelle Aktualisierungen: %d Aktualisierungen / %lu us"

#: ../../src/slave/kpropd.c:917
#, c-format
msgid "Incremental updates: %d updates / %lu us\n"
msgstr "inkrementelle Aktualisierungen: %d Aktualisierungen / %lu us\n"

#: ../../src/slave/kpropd.c:925
#, c-format
msgid "get_updates permission denied\n"
msgstr "Zugriff bei »get_updates« verweigert\n"

#: ../../src/slave/kpropd.c:926
msgid "get_updates, permission denied."
msgstr "»get_updates«, Zugriff verweigert"

#: ../../src/slave/kpropd.c:931
#, c-format
msgid "get_updates error from master\n"
msgstr "»get_updates«-Fehler vom Master\n"

#: ../../src/slave/kpropd.c:932
msgid "get_updates, error returned from master KDC."
msgstr "Vom Master-KDC wurde ein »get_updates«-Fehler zurückgegeben."

# FIXME s/backoff/back-off/
#: ../../src/slave/kpropd.c:940
#, c-format
msgid "get_updates master busy; backoff\n"
msgstr "»get_updates«-Master ausgelastet; hält sich zurück\n"

#: ../../src/slave/kpropd.c:949
#, c-format
msgid "KDC is synchronized with master.\n"
msgstr "KDC wurde mit dem Master synchronisiert.\n"

#: ../../src/slave/kpropd.c:957
#, c-format
msgid "get_updates invalid result from master\n"
msgstr "ungültiges »get_updates«-Ergebnis vom Master\n"

#: ../../src/slave/kpropd.c:958
msgid "get_updates, invalid return from master KDC."
msgstr "»get_updates«, ungültiger Rückgabewert vom Master-KDC"

# FIXME s/backoff/back-off/
#: ../../src/slave/kpropd.c:973
#, c-format
msgid "Busy signal received from master, backoff for %d secs\n"
msgstr ""
"Vom Master wurde ein Signal empfangen, dass er ausgelastet ist, "
"Zurückhaltung für %d Sekunden\n"

#: ../../src/slave/kpropd.c:980
#, c-format
msgid "Waiting for %d seconds before checking for updates again\n"
msgstr ""
"vor der erneuten Prufung auf Aktualisierungen wird %d Sekunden gewartet\n"

#: ../../src/slave/kpropd.c:991
#, c-format
msgid "ERROR returned by master, bailing\n"
msgstr "FEHLER vom Master zurückgegeben, Ausstieg\n"

#: ../../src/slave/kpropd.c:992
msgid "ERROR returned by master KDC, bailing.\n"
msgstr "FEHLER vom Master-KDC zurückgegeben, Ausstieg\n"

#: ../../src/slave/kpropd.c:1134
msgid "copying db args"
msgstr "Datenbankargumente werden kopiert"

#: ../../src/slave/kpropd.c:1161
msgid "while trying to construct my service name"
msgstr "beim Versuch, meinen Dienstnamen zu erstellen"

#: ../../src/slave/kpropd.c:1167
msgid "while constructing my service realm"
msgstr "beim Erstellen meines Dienst-Realms"

#: ../../src/slave/kpropd.c:1175
msgid "while allocating filename for temp file"
msgstr "beim Reservieren des Dateinamens für die temporäre Datei"

#: ../../src/slave/kpropd.c:1181
msgid "while initializing"
msgstr "bei der Initialisierung"

#: ../../src/slave/kpropd.c:1189
msgid "Unable to map log!\n"
msgstr "Protokoll kann nicht abgebildet werden!\n"

#: ../../src/slave/kpropd.c:1235
#, c-format
msgid "Error in krb5_auth_con_ini: %s"
msgstr "Fehler in »krb5_auth_con_ini«: %s"

#: ../../src/slave/kpropd.c:1243
#, c-format
msgid "Error in krb5_auth_con_setflags: %s"
msgstr "Fehler in »krb5_auth_con_setflags«: %s"

#: ../../src/slave/kpropd.c:1251
#, c-format
msgid "Error in krb5_auth_con_setaddrs: %s"
msgstr "Fehler in »krb5_auth_con_setaddrs«: %s"

#: ../../src/slave/kpropd.c:1259
#, c-format
msgid "Error in krb5_kt_resolve: %s"
msgstr "Fehler in »krb5_kt_resolve«: %s"

#: ../../src/slave/kpropd.c:1268
#, c-format
msgid "Error in krb5_recvauth: %s"
msgstr "Fehler in »krb5_recvauth«: %s"

#: ../../src/slave/kpropd.c:1275
#, c-format
msgid "Error in krb5_copy_prinicpal: %s"
msgstr "Fehler in »krb5_copy_prinicpal«: %s"

#: ../../src/slave/kpropd.c:1291
msgid "while unparsing ticket etype"
msgstr "beim Rückgängigmachen der Auswertung des »etype«s des Tickets"

#: ../../src/slave/kpropd.c:1295
#, c-format
msgid "authenticated client: %s (etype == %s)\n"
msgstr "Authentifizierter Client: %s (etype == %s)\n"

#: ../../src/slave/kpropd.c:1374
msgid "while reading size of database from client"
msgstr "beim Lesen der Datenbankgröße vom Client"

#: ../../src/slave/kpropd.c:1384
msgid "while decoding database size from client"
msgstr "beim Dekodieren der Datenbankgröße vom Client"

#: ../../src/slave/kpropd.c:1397
msgid "while initializing i_vector"
msgstr "beim Initialisieren von »i_vector«"

#: ../../src/slave/kpropd.c:1402
#, c-format
msgid "Full propagation transfer started.\n"
msgstr "vollständige Verbreitungsübertragung gestartet\n"

#: ../../src/slave/kpropd.c:1455
#, c-format
msgid "Full propagation transfer finished.\n"
msgstr "vollständige Verbreitungsübertragung beendet\n"

#: ../../src/slave/kpropd.c:1516
msgid "while decoding error packet from client"
msgstr "beim Dekodieren des Fehlerpakets vom Client"

#: ../../src/slave/kpropd.c:1525
msgid "signaled from server"
msgstr "signalisiert vom Server"

#: ../../src/slave/kpropd.c:1527
#, c-format
msgid "Error text from client: %s\n"
msgstr "Fehlermeldung vom Client: %s\n"

#: ../../src/slave/kpropd.c:1576
#, c-format
msgid "while trying to fork %s"
msgstr "beim Versuch, einen Kindprozess von %s zu erzeugen"

#: ../../src/slave/kpropd.c:1580
#, c-format
msgid "while trying to exec %s"
msgstr "beim Versuch, %s auszuführen"

#: ../../src/slave/kpropd.c:1587
#, c-format
msgid "while waiting for %s"
msgstr "beim Warten auf %s"

#: ../../src/slave/kpropd.c:1593
#, c-format
msgid "%s load terminated"
msgstr "Laden von %s beendet"

#: ../../src/slave/kpropd.c:1599
#, c-format
msgid "%s returned a bad exit status (%d)"
msgstr "%s gab einen falschen Exit-Status (%d) zurück"

#: ../../src/slave/kproplog.c:27
#, c-format
msgid ""
"\n"
"Usage: %s [-h] [-v] [-v] [-e num]\n"
"\t%s -R\n"
"\n"
msgstr ""
"\n"
"Aufruf: %s [-h] [-v] [-v] [-e Zahl]\n"
"\t%s -R\n"
"\n"

#: ../../src/slave/kproplog.c:129
#, c-format
msgid ""
"\n"
"Couldn't allocate memory"
msgstr ""
"\n"
"Speicher konnte nicht reserviert werden"

#: ../../src/slave/kproplog.c:223
#, c-format
msgid "\t\tAttribute flags\n"
msgstr "\t\tAttributschalter\n"

#: ../../src/slave/kproplog.c:228
#, c-format
msgid "\t\tMaximum ticket life\n"
msgstr "\t\tmaximale Ticketlebensdauer\n"

#: ../../src/slave/kproplog.c:233
#, c-format
msgid "\t\tMaximum renewable life\n"
msgstr "\t\tmaximale verlängerbare Lebensdauer\n"

#: ../../src/slave/kproplog.c:238
#, c-format
msgid "\t\tPrincipal expiration\n"
msgstr "\t\tAblauf des Principals\n"

#: ../../src/slave/kproplog.c:243
#, c-format
msgid "\t\tPassword expiration\n"
msgstr "\t\tAblauf des Passworts\n"

#: ../../src/slave/kproplog.c:248
#, c-format
msgid "\t\tLast successful auth\n"
msgstr "\t\tletzte erfolgreiche Authentifizierung\n"

#: ../../src/slave/kproplog.c:253
#, c-format
msgid "\t\tLast failed auth\n"
msgstr "\t\tletzte fehlgeschlagene Authentifizierung\n"

#: ../../src/slave/kproplog.c:258
#, c-format
msgid "\t\tFailed passwd attempt\n"
msgstr "\t\tfehlgeschlagener Passwortversuch\n"

#: ../../src/slave/kproplog.c:263
#, c-format
msgid "\t\tPrincipal\n"
msgstr "\t\tPrincipal\n"

#: ../../src/slave/kproplog.c:268
#, c-format
msgid "\t\tKey data\n"
msgstr "\t\tSchlüsseldaten\n"

#: ../../src/slave/kproplog.c:275
#, c-format
msgid "\t\tTL data\n"
msgstr "\t\tTL-Daten\n"

#: ../../src/slave/kproplog.c:282
#, c-format
msgid "\t\tLength\n"
msgstr "\t\tLänge\n"

#: ../../src/slave/kproplog.c:287
#, c-format
msgid "\t\tPassword last changed\n"
msgstr "\t\tletzte Passwortänderung\n"

#: ../../src/slave/kproplog.c:292
#, c-format
msgid "\t\tModifying principal\n"
msgstr "\t\ttPrincipal wird geändert\n"

#: ../../src/slave/kproplog.c:297
#, c-format
msgid "\t\tModification time\n"
msgstr "\t\tÄnderungszeit\n"

#: ../../src/slave/kproplog.c:302
#, c-format
msgid "\t\tModified where\n"
msgstr "\t\tGeändert wobei\n"

#: ../../src/slave/kproplog.c:307
#, c-format
msgid "\t\tPassword policy\n"
msgstr "\t\tPasswortrichtlinie\n"

#: ../../src/slave/kproplog.c:312
#, c-format
msgid "\t\tPassword policy switch\n"
msgstr "\t\tPasswortrichtlinienumschalter\n"

#: ../../src/slave/kproplog.c:317
#, c-format
msgid "\t\tPassword history KVNO\n"
msgstr "\t\tPasswortchronik KVNO\n"

#: ../../src/slave/kproplog.c:322
#, c-format
msgid "\t\tPassword history\n"
msgstr "\t\tPasswortchronik\n"

#: ../../src/slave/kproplog.c:356
#, c-format
msgid ""
"Corrupt update entry\n"
"\n"
msgstr ""
"beschädigter Aktualisierungseintrag\n"
"\n"

#: ../../src/slave/kproplog.c:364
#, c-format
msgid ""
"Entry data decode failure\n"
"\n"
msgstr ""
"Dekodieren der eingetragenen Daten fehlgeschlagen\n"
"\n"

#: ../../src/slave/kproplog.c:369
#, c-format
msgid "Update Entry\n"
msgstr "Aktualisierungseintrag\n"

#: ../../src/slave/kproplog.c:371
#, c-format
msgid "\tUpdate serial # : %u\n"
msgstr "\tAktualisierung der Seriennummer: %u\n"

#: ../../src/slave/kproplog.c:373
#, c-format
msgid "\tUpdate operation : "
msgstr "\tAktualisierungsaktion: "

#: ../../src/slave/kproplog.c:375
#, c-format
msgid "Delete\n"
msgstr "Löschen\n"

#: ../../src/slave/kproplog.c:377
#, c-format
msgid "Add\n"
msgstr "Hinzufügen\n"

#: ../../src/slave/kproplog.c:381
#, c-format
msgid ""
"Could not allocate principal name\n"
"\n"
msgstr ""
"Der Principal-Name konnte nicht reserviert werden.\n"
"\n"

#: ../../src/slave/kproplog.c:387
#, c-format
msgid "\tUpdate principal : %s\n"
msgstr "\tAktualisierung des Principals: %s\n"

#: ../../src/slave/kproplog.c:389
#, c-format
msgid "\tUpdate size : %u\n"
msgstr "\tGröße der Aktualisierung: %u\n"

#: ../../src/slave/kproplog.c:390
#, c-format
msgid "\tUpdate committed : %s\n"
msgstr "\tAktualisierung übergeben: %s\n"

#: ../../src/slave/kproplog.c:394
#, c-format
msgid "\tUpdate time stamp : None\n"
msgstr "\tZeitstempel der Aktualisierung: keiner\n"

#: ../../src/slave/kproplog.c:396
#, c-format
msgid "\tUpdate time stamp : %s"
msgstr "\tZeitstempel der Aktualisierung: %s"

#: ../../src/slave/kproplog.c:400
#, c-format
msgid "\tAttributes changed : %d\n"
msgstr "\tgeänderte Attribute: %d\n"

#: ../../src/slave/kproplog.c:465
#, c-format
msgid ""
"Unable to initialize Kerberos\n"
"\n"
msgstr ""
"Kerberos kann nicht initialisiert werden\n"
"\n"

#: ../../src/slave/kproplog.c:472
#, c-format
msgid ""
"Couldn't read database_name\n"
"\n"
msgstr ""
"»database_name« kann nicht gelesen werden\n"
"\n"

#: ../../src/slave/kproplog.c:476
#, c-format
msgid ""
"\n"
"Kerberos update log (%s)\n"
msgstr ""
"\n"
"Kerberos-Aktualisierungsprotokoll (%s)\n"

#: ../../src/slave/kproplog.c:480 ../../src/slave/kproplog.c:495
#, c-format
msgid ""
"Unable to map log file %s\n"
"\n"
msgstr ""
"Protokolldatei %s kann nicht abgebildet werden\n"
"\n"

#: ../../src/slave/kproplog.c:485
#, c-format
msgid ""
"Couldn't reinitialize ulog file %s\n"
"\n"
msgstr ""
"Ulog-Datei %s konnte nicht neu initialisiert werden\n"
"\n"

#: ../../src/slave/kproplog.c:489
#, c-format
msgid "Reinitialized the ulog.\n"
msgstr "Das Ulog wurde neu initialisiert.\n"

#: ../../src/slave/kproplog.c:501
#, c-format
msgid ""
"Corrupt header log, exiting\n"
"\n"
msgstr ""
"beschädigtes Kopfzeilenprotokoll, wird beendet\n"
"\n"

#: ../../src/slave/kproplog.c:505
#, c-format
msgid "Update log dump :\n"
msgstr "Aktualisierungsprotokollauszug :\n"

#: ../../src/slave/kproplog.c:506
#, c-format
msgid "\tLog version # : %u\n"
msgstr "\tProtokollversion #:  %u\n"

#: ../../src/slave/kproplog.c:507
#, c-format
msgid "\tLog state : "
msgstr "\tProtokollstatus: "

#: ../../src/slave/kproplog.c:510
#, c-format
msgid "Stable\n"
msgstr "stabil\n"

#: ../../src/slave/kproplog.c:513
#, c-format
msgid "Unstable\n"
msgstr "instabil\n"

#: ../../src/slave/kproplog.c:516
#, c-format
msgid "Corrupt\n"
msgstr "beschädigt\n"

#: ../../src/slave/kproplog.c:519
#, c-format
msgid "Unknown state: %d\n"
msgstr "unbekannter Status: %d\n"

#: ../../src/slave/kproplog.c:522
#, c-format
msgid "\tEntry block size : %u\n"
msgstr "\tBlockgrößeneintrag: %u\n"

#: ../../src/slave/kproplog.c:523
#, c-format
msgid "\tNumber of entries : %u\n"
msgstr "\tAnzahl der Einträge: %u\n"

#: ../../src/slave/kproplog.c:526
#, c-format
msgid "\tLast serial # : None\n"
msgstr "\tletzte Seriennummer: keine\n"

#: ../../src/slave/kproplog.c:529
#, c-format
msgid "\tFirst serial # : None\n"
msgstr "\terste Seriennummer: keine\n"

#: ../../src/slave/kproplog.c:531
#, c-format
msgid "\tFirst serial # : "
msgstr "\terste Seriennummer: "

#: ../../src/slave/kproplog.c:535
#, c-format
msgid "\tLast serial # : "
msgstr "\tletzte Seriennummer: "

#: ../../src/slave/kproplog.c:540
#, c-format
msgid "\tLast time stamp : None\n"
msgstr "\tletzter Zeitstempel: keiner\n"

#: ../../src/slave/kproplog.c:543
#, c-format
msgid "\tFirst time stamp : None\n"
msgstr "\terster Zeitstempel: keiner\n"

#: ../../src/slave/kproplog.c:545
#, c-format
msgid "\tFirst time stamp : %s"
msgstr "\terster Zeitstempel: %s"

#: ../../src/slave/kproplog.c:549
#, c-format
msgid "\tLast time stamp : %s\n"
msgstr "\tletzter Zeitstempel: %s\n"

#: ../../src/util/support/errors.c:77
msgid "Kerberos library initialization failure"
msgstr "Initialisieren der Kerberos-Bibliothek fehlgeschlagen"

#: ../../src/util/support/errors.c:93
#, c-format
msgid "error %ld"
msgstr "Fehler %ld"

#: ../../src/util/support/plugins.c:186
#, c-format
msgid "unable to find plugin [%s]: %s"
msgstr "Erweiterung [%s] konnte nicht gefunden werden: %s"

#: ../../src/util/support/plugins.c:274
msgid "unknown failure"
msgstr "unbekannter Fehlschlag"

#: ../../src/util/support/plugins.c:277
#, c-format
msgid "unable to load plugin [%s]: %s"
msgstr "Erweiterung [%s] konnte nicht geladen werden: %s"

#: ../../src/util/support/plugins.c:300
#, c-format
msgid "unable to load DLL [%s]"
msgstr "DLL [%s] konnte nicht geladen werden"

#: ../../src/util/support/plugins.c:316
#, c-format
msgid "plugin unavailable: %s"
msgstr "Erweiterung nicht verfügbar: %s"

#: ../lib/gssapi/generic/gssapi_err_generic.c:23
msgid "No @ in SERVICE-NAME name string"
msgstr "keine @ in der Namenszeichenkette SERVICE-NAME"

#: ../lib/gssapi/generic/gssapi_err_generic.c:24
msgid "STRING-UID-NAME contains nondigits"
msgstr "STRING-UID-NAME enthält etwas anderes als Ziffern"

#: ../lib/gssapi/generic/gssapi_err_generic.c:25
msgid "UID does not resolve to username"
msgstr "UID lässt sich nicht zu Benutzernamen ermitteln"

#: ../lib/gssapi/generic/gssapi_err_generic.c:26
msgid "Validation error"
msgstr "Überprüfungsfehler"

#: ../lib/gssapi/generic/gssapi_err_generic.c:27
msgid "Couldn't allocate gss_buffer_t data"
msgstr "»gss_buffer_t«-Daten konnten reserviert werden"

#: ../lib/gssapi/generic/gssapi_err_generic.c:28
msgid "Message context invalid"
msgstr "Nachrichtenkontext ungültig"

#: ../lib/gssapi/generic/gssapi_err_generic.c:29
msgid "Buffer is the wrong size"
msgstr "Puffer hat die falsche Größe"

#: ../lib/gssapi/generic/gssapi_err_generic.c:30
msgid "Credential usage type is unknown"
msgstr "Typ des Anmeldedatenaufrufs ist unbekannt"

#: ../lib/gssapi/generic/gssapi_err_generic.c:31
msgid "Unknown quality of protection specified"
msgstr "unbekannte Schutzqualität angegeben"

#: ../lib/gssapi/generic/gssapi_err_generic.c:32
msgid "Local host name could not be determined"
msgstr "lokaler Rechnername konnte nicht bestimmt werden"

#: ../lib/gssapi/generic/gssapi_err_generic.c:33
msgid "Hostname in SERVICE-NAME string could not be canonicalized"
msgstr ""
"Rechnername in der Zeichenkette »SERVICE-NAME« konnte nicht in Normalform "
"gebracht werden"

#: ../lib/gssapi/generic/gssapi_err_generic.c:34
msgid "Mechanism is incorrect"
msgstr "Mechanismus ist nicht korrekt"

#: ../lib/gssapi/generic/gssapi_err_generic.c:35
msgid "Token header is malformed or corrupt"
msgstr "Token-Kopfzeilen haben die falsche Form oder sind beschädigt"

#: ../lib/gssapi/generic/gssapi_err_generic.c:36
msgid "Packet was replayed in wrong direction"
msgstr "Paket wurde in falscher Richtung erneut abgespielt"

#: ../lib/gssapi/generic/gssapi_err_generic.c:37
msgid "Token is missing data"
msgstr "dem Token fehlen Daten"

#: ../lib/gssapi/generic/gssapi_err_generic.c:38
msgid "Token was reflected"
msgstr "Token wurde zurückgeworfen"

#: ../lib/gssapi/generic/gssapi_err_generic.c:39
msgid "Received token ID does not match expected token ID"
msgstr "Die empfangene Token-Kennung passt nicht zur erwarteten Token-Kennung."

#: ../lib/gssapi/generic/gssapi_err_generic.c:40
msgid "The given credential's usage does not match the requested usage"
msgstr ""
"Die Verwendung der angegebenen Anmeldedaten passt nicht zur angeforderten "
"Verwendung."

#: ../lib/gssapi/generic/gssapi_err_generic.c:41
msgid "Storing of acceptor credentials is not supported by the mechanism"
msgstr ""
"Das Speichern von Abnehmeranmeldedaten wird nicht durch den Mechanismus "
"unterstützt."

#: ../lib/gssapi/generic/gssapi_err_generic.c:42
msgid "Storing of non-default credentials is not supported by the mechanism"
msgstr ""
"Das Speichern von Nichtstandardanmeldedaten wird nicht durch den Mechanismus "
"unterstützt."

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:23
msgid "Principal in credential cache does not match desired name"
msgstr ""
"Principal im Anmeldedatenzwischenspeicher entspricht nicht dem gewünschten "
"Namen"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:24
msgid "No principal in keytab matches desired name"
msgstr "Kein Principal in der Schlüsseltabelle passt zum gewünschten Namen."

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:25
msgid "Credential cache has no TGT"
msgstr "Anmeldedatenzwischenspeicher hat kein TGT"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:26
msgid "Authenticator has no subkey"
msgstr "Schlüsselziffer hat keinen Unterschlüssel"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:27
msgid "Context is already fully established"
msgstr "Kontext wurde bereits vollständig eingerichtet"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:28
msgid "Unknown signature type in token"
msgstr "unbekannter Signaturtyp im Token"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:29
msgid "Invalid field length in token"
msgstr "falsche Feldlänge im Token"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:30
msgid "Attempt to use incomplete security context"
msgstr ""
"Es wurde versucht, einen unvollständigen Sicherheitskontext zu verwenden."

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:31
msgid "Bad magic number for krb5_gss_ctx_id_t"
msgstr "falsche magische Zahl für »krb5_gss_ctx_id_t«"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:32
msgid "Bad magic number for krb5_gss_cred_id_t"
msgstr "falsche magische Zahl für »krb5_gss_cred_id_t«"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:33
msgid "Bad magic number for krb5_gss_enc_desc"
msgstr "falsche magische Zahl für »krb5_gss_enc_desc«"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:34
msgid "Sequence number in token is corrupt"
msgstr "Sequnznummer im Token ist beschädigt"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:35
msgid "Credential cache is empty"
msgstr "Anmeldedatenzwischenspeicher ist leer"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:36
msgid "Acceptor and Initiator share no checksum types"
msgstr "Abnehmer und Initiator haben keinen gemeinsamen Prüfsummentyp"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:37
msgid "Requested lucid context version not supported"
msgstr "angeforderte »lucid«-Kontextversion nicht unterstützt"

# PRF = Pseudo Random Function
#: ../lib/gssapi/krb5/gssapi_err_krb5.c:38
msgid "PRF input too long"
msgstr "PRF-Eingabe zu lang"

#: ../lib/gssapi/krb5/gssapi_err_krb5.c:39
msgid "Bad magic number for iakerb_ctx_id_t"
msgstr "falsche magische Zahl für »iakerb_ctx_id_t«"

#: ../lib/kadm5/chpass_util_strings.c:23
msgid "while getting policy info."
msgstr "beim Holen der Richtlinieninformation."

#: ../lib/kadm5/chpass_util_strings.c:24
msgid "while getting principal info."
msgstr "beim Holen der Principal-Information."

#: ../lib/kadm5/chpass_util_strings.c:25
msgid "New passwords do not match - password not changed.\n"
msgstr "neue Passwörter stimmen nicht überein – Passwort nicht geändert\n"

#: ../lib/kadm5/chpass_util_strings.c:26
msgid "New password"
msgstr "neues Passwort"

#: ../lib/kadm5/chpass_util_strings.c:27
msgid "New password (again)"
msgstr "neues Passwort (erneut)"

#: ../lib/kadm5/chpass_util_strings.c:28
msgid ""
"You must type a password. Passwords must be at least one character long.\n"
msgstr ""
"Sie müssen ein Passwort eingeben. Passwörter müssen mindestens ein Zeichen "
"lang sein.\n"

#: ../lib/kadm5/chpass_util_strings.c:29
msgid "yet no policy set!  Contact your system security administrator."
msgstr ""
"noch keine Richtlinie gesetzt! Kontaktieren Sie Ihren "
"Systemsicherheitsadministrator"

#: ../lib/kadm5/chpass_util_strings.c:31
msgid ""
"New password was found in a dictionary of possible passwords and\n"
"therefore may be easily guessed. Please choose another password.\n"
"See the kpasswd man page for help in choosing a good password."
msgstr ""
"Das neue Passwort wurde in einem Wörterbuch mit möglichen Passwörtern "
"gefunden\n"
"und kann daher leicht erraten werden. Bitte wählen Sie ein anderes "
"Passwort.\n"
"Hilfe bei der Wahl guter Passwörter finden Sie in der Handbuchseite von\n"
"»kpasswd«."

#: ../lib/kadm5/chpass_util_strings.c:32
msgid "Password not changed."
msgstr "Passwort nicht geändert"

#: ../lib/kadm5/chpass_util_strings.c:33
#, c-format
msgid ""
"New password is too short.\n"
"Please choose a password which is at least %d characters long."
msgstr ""
"Das neue Passwort ist zu kurz.\n"
"Bitte wählen Sie ein Passwort, das mindestens %d Zeichen lang ist."

#: ../lib/kadm5/chpass_util_strings.c:34
#, c-format
msgid ""
"New password does not have enough character classes.\n"
"The character classes are:\n"
"\t- lower-case letters,\n"
"\t- upper-case letters,\n"
"\t- digits,\n"
"\t- punctuation, and\n"
"\t- all other characters (e.g., control characters).\n"
"Please choose a password with at least %d character classes."
msgstr ""
"Das neue Passwort besteht aus zu wenigen Zeichenklassen.\n"
"Die Zeichenklassen sind:\n"
"\t- Kleinbuchstaben,\n"
"\t- Großbuchstaben,\n"
"\t- Ziffern,\n"
"\t- Satzzeichen und\n"
"\t- alle anderen Zeichen (z.B. Steuerzeichen).\n"
"Bitte wählen Sie ein Passwort mit mindestens %d Zeichenklassen."

#: ../lib/kadm5/chpass_util_strings.c:35
#, c-format
msgid ""
"Password cannot be changed because it was changed too recently.\n"
"Please wait until %s before you change it.\n"
"If you need to change your password before then, contact your system\n"
"security administrator."
msgstr ""
"Das Passwort kann nicht geändert werden, da es erst vor kurzem geändert "
"wurde.\n"
"Bitte warten Sie bis %s, ehe Sie es ändern.\n"
"Falls Sie es vorher ändern müssen, kontaktieren Sie Ihren\n"
"Systemsicherheitsadministrator."

#: ../lib/kadm5/chpass_util_strings.c:36
msgid "New password was used previously. Please choose a different password."
msgstr ""
"Das neue Passwort wurde zuvor schon benutzt. Bitte wählen Sie ein anderes "
"Passwort."

#: ../lib/kadm5/chpass_util_strings.c:37
msgid "while trying to change password."
msgstr "beim Versuch, das Passwort zu ändern."

#: ../lib/kadm5/chpass_util_strings.c:38
msgid "while reading new password."
msgstr "beim Lesen des neuen Passworts."

#: ../lib/kadm5/kadm_err.c:23
msgid "Operation failed for unspecified reason"
msgstr "Aktion aus nicht näher beschriebenem Grund fehlgeschlagen"

#: ../lib/kadm5/kadm_err.c:24
msgid "Operation requires ``get'' privilege"
msgstr "Aktion erfordert »get«-Recht"

#: ../lib/kadm5/kadm_err.c:25
msgid "Operation requires ``add'' privilege"
msgstr "Aktion erfordert »add«-Recht"

#: ../lib/kadm5/kadm_err.c:26
msgid "Operation requires ``modify'' privilege"
msgstr "Aktion erfordert »modify«-Recht"

#: ../lib/kadm5/kadm_err.c:27
msgid "Operation requires ``delete'' privilege"
msgstr "Aktion erfordert »delete«-Recht"

#: ../lib/kadm5/kadm_err.c:28
msgid "Insufficient authorization for operation"
msgstr "unzureichende Berechtigung für diese Aktion"

#: ../lib/kadm5/kadm_err.c:29 ../lib/kdb/adb_err.c:29
msgid "Database inconsistency detected"
msgstr "Datenbankinkonsistenz entdeckt"

#: ../lib/kadm5/kadm_err.c:30 ../lib/kdb/adb_err.c:24
msgid "Principal or policy already exists"
msgstr "Principal oder Richtlinie existiert bereits"

#: ../lib/kadm5/kadm_err.c:31
msgid "Communication failure with server"
msgstr "Kommunikation mit dem Server fehlgeschlagen"

#: ../lib/kadm5/kadm_err.c:32
msgid "No administration server found for realm"
msgstr "kein Administrationsserver für den Realm gefunden"

#: ../lib/kadm5/kadm_err.c:33
msgid "Password history principal key version mismatch"
msgstr "Die Passwortchronikschlüssel des Principals passen nicht zusammen."

#: ../lib/kadm5/kadm_err.c:34
msgid "Connection to server not initialized"
msgstr "Verbindung zum Server nicht initialisiert"

#: ../lib/kadm5/kadm_err.c:35
msgid "Principal does not exist"
msgstr "Principal existiert nicht"

#: ../lib/kadm5/kadm_err.c:36
msgid "Policy does not exist"
msgstr "Richtlinie existiert nicht"

#: ../lib/kadm5/kadm_err.c:37
msgid "Invalid field mask for operation"
msgstr "ungültige Feldmaske für Aktion"

#: ../lib/kadm5/kadm_err.c:38
msgid "Invalid number of character classes"
msgstr "ungültige Anzahl von Zeichenklassen"

#: ../lib/kadm5/kadm_err.c:39
msgid "Invalid password length"
msgstr "ungültige Passwortlänge"

#: ../lib/kadm5/kadm_err.c:40
msgid "Illegal policy name"
msgstr "unzulässiger Richtlinienname"

#: ../lib/kadm5/kadm_err.c:41
msgid "Illegal principal name"
msgstr "unzulässiger Principal-Name"

# FIXME s/auxillary/auxilary/
#: ../lib/kadm5/kadm_err.c:42
msgid "Invalid auxillary attributes"
msgstr "ungültige Zusatzattribute"

#: ../lib/kadm5/kadm_err.c:43
msgid "Invalid password history count"
msgstr "ungültige Passwortchronikanzahl"

#: ../lib/kadm5/kadm_err.c:44
msgid "Password minimum life is greater than password maximum life"
msgstr "Die minimale Lebensdauer des Passworts ist größer als die maximale."

#: ../lib/kadm5/kadm_err.c:45
msgid "Password is too short"
msgstr "Das Passwort ist zu kurz."

#: ../lib/kadm5/kadm_err.c:46
msgid "Password does not contain enough character classes"
msgstr "Das Passwort enthält nicht genug Zeichenklassen."

#: ../lib/kadm5/kadm_err.c:47
msgid "Password is in the password dictionary"
msgstr "Das Passwort steht im Passwortwörterbuch."

#: ../lib/kadm5/kadm_err.c:48
msgid "Cannot reuse password"
msgstr "Das Passwort kann nicht erneut verwendet werden."

#: ../lib/kadm5/kadm_err.c:49
msgid "Current password's minimum life has not expired"
msgstr "Die aktuell minimale Lebensdauer des Passworts ist nicht abgelaufen."

#: ../lib/kadm5/kadm_err.c:50 ../lib/krb5/error_tables/kdb5_err.c:67
msgid "Policy is in use"
msgstr "Richtlinie ist in Benutzung"

#: ../lib/kadm5/kadm_err.c:51
msgid "Connection to server already initialized"
msgstr "Verbindung zum Server ist bereits initialisiert"

#: ../lib/kadm5/kadm_err.c:52
msgid "Incorrect password"
msgstr "falsches Passwort"

#: ../lib/kadm5/kadm_err.c:53
msgid "Cannot change protected principal"
msgstr "geschützter Principal kann nicht geändert werden"

#: ../lib/kadm5/kadm_err.c:54
msgid "Programmer error! Bad Admin server handle"
msgstr "Fehler des Programmierers! Falscher Admin-Server-Identifikator"

#: ../lib/kadm5/kadm_err.c:55
msgid "Programmer error! Bad API structure version"
msgstr "Fehler des Programmierers! Falsche API-Strukturversion"

#: ../lib/kadm5/kadm_err.c:56
msgid ""
"API structure version specified by application is no longer supported (to "
"fix, recompile application against current KADM5 API header files and "
"libraries)"
msgstr ""
"Die von der Anwendung angegebene Version der API-Struktur wird nicht länger "
"unterstützt. (Kompilieren Sie die Anwendung mit den aktuellen KADM5-API-"
"Header-Dateien und -Bibliotheken, um dies zu beheben.)"

#: ../lib/kadm5/kadm_err.c:57
msgid ""
"API structure version specified by application is unknown to libraries (to "
"fix, obtain current KADM5 API header files and libraries and recompile "
"application)"
msgstr ""
"Die von der Anwendung angegebene Version der API-Struktur ist den "
"Bibliotheken unbekannt. (Besorgen Sie sich die aktuellen KADM5-API-Header-"
"Dateien und -Bibliotheken und kompilieren Sie die Anwendung neu, um dies zu "
"beheben.)"

#: ../lib/kadm5/kadm_err.c:58
msgid "Programmer error! Bad API version"
msgstr "Fehler des Programmierers! Falsche API-Version"

#: ../lib/kadm5/kadm_err.c:59
msgid ""
"API version specified by application is no longer supported by libraries (to "
"fix, update application to adhere to current API version and recompile)"
msgstr ""
"Die von der Anwendung angegebene Version der API-Struktur wird nicht länger "
"von den Bibliotheken unterstützt. (Aktualisieren Sie die Anwendung, dass sie "
"zu der aktuellen API-Version passt, und kompilieren Sie sie, um dies zu "
"beheben.)"

#: ../lib/kadm5/kadm_err.c:60
msgid ""
"API version specified by application is no longer supported by server (to "
"fix, update application to adhere to current API version and recompile)"
msgstr ""
"Die von der Anwendung angegebene Version der API-Struktur wird nicht länger "
"vom Server unterstützt. (Aktualisieren Sie die Anwendung, dass sie zu der "
"aktuellen API-Version passt, und kompilieren Sie sie, um dies zu beheben.)"

#: ../lib/kadm5/kadm_err.c:61
msgid ""
"API version specified by application is unknown to libraries (to fix, obtain "
"current KADM5 API header files and libraries and recompile application)"
msgstr ""
"Die von der Anwendung angegebenene API-Version ist den Bibliotheken "
"unbekannt. (Besorgen Sie sich die aktuellen KADM5-API-Header-Dateien und -"
"Bibliotheken und kompilieren Sie die Anwendung neu, um dies zu beheben.)"

#: ../lib/kadm5/kadm_err.c:62
msgid ""
"API version specified by application is unknown to server (to fix, obtain "
"and install newest KADM5 Admin Server)"
msgstr ""
"Die von der Anwendung angegebene API-Version ist dem Server unbekannt. "
"(Besorgen und installieren Sie sich den neuesten KADM5-Admin-Server, um dies "
"zu beheben.)"

#: ../lib/kadm5/kadm_err.c:63
msgid "Database error! Required KADM5 principal missing"
msgstr "Datenbankfehler! Erforderlicher KADM5-Principal fehlt"

#: ../lib/kadm5/kadm_err.c:64
msgid "The salt type of the specified principal does not support renaming"
msgstr "Der Salt-Typ des angegebenen Principals unterstützt kein Umbenennen."

#: ../lib/kadm5/kadm_err.c:65
msgid "Illegal configuration parameter for remote KADM5 client"
msgstr "widerrechtlicher Konfigurationsparameter für fernen KADM5-Client"

#: ../lib/kadm5/kadm_err.c:66
msgid "Illegal configuration parameter for local KADM5 client"
msgstr "widerrechtlicher Konfigurationsparameter für lokalen KADM5-Client"

#: ../lib/kadm5/kadm_err.c:67
msgid "Operation requires ``list'' privilege"
msgstr "Aktion erfordert das »list«-Recht"

#: ../lib/kadm5/kadm_err.c:68
msgid "Operation requires ``change-password'' privilege"
msgstr "Aktion erfordert das »change-password«-Recht"

#: ../lib/kadm5/kadm_err.c:69
msgid "GSS-API (or Kerberos) error"
msgstr "GSS-API- (oder Kerberos-) Fehler"

#: ../lib/kadm5/kadm_err.c:70
msgid "Programmer error! Illegal tagged data list type"
msgstr ""
"Fehler des Programmierers! Widerrechlicher Listentyp für gekennzeichnete "
"Daten"

#: ../lib/kadm5/kadm_err.c:71
msgid "Required parameters in kdc.conf missing"
msgstr "erforderliche Parameter in »kdc.conf« fehlen"

#: ../lib/kadm5/kadm_err.c:72
msgid "Bad krb5 admin server hostname"
msgstr "falscher Rechnername des KRB5-Admin-Servers"

#: ../lib/kadm5/kadm_err.c:73
msgid "Operation requires ``set-key'' privilege"
msgstr "Aktion erfordert das »set-key«-Recht"

#: ../lib/kadm5/kadm_err.c:74
msgid "Multiple values for single or folded enctype"
msgstr ""
"mehrere Werte für einzelnen Verschlüsselungstyp oder Verschlüsselungstyp mit "
"Salt"

#: ../lib/kadm5/kadm_err.c:75
msgid "Invalid enctype for setv4key"
msgstr "widerrechtlicher Verschlüsselungstyp für Setv4key"

#: ../lib/kadm5/kadm_err.c:76
msgid "Mismatched enctypes for setkey3"
msgstr "nicht zusammenpassende Verschlüsselungstypen für Setkey3"

#: ../lib/kadm5/kadm_err.c:77
msgid "Missing parameters in krb5.conf required for kadmin client"
msgstr "für Kadmin-Client benötigte Parameter fehlen in »krb5.conf«"

#: ../lib/kadm5/kadm_err.c:78 ../lib/kdb/adb_err.c:30
msgid "XDR encoding error"
msgstr "XDR-Verschlüsselungsfehler"

#: ../lib/kadm5/kadm_err.c:79
msgid "Cannot resolve network address for admin server in requested realm"
msgstr ""
"Die Netzwerkadresse für den Admin-Server im angeforderten Realm kann nicht "
"aufgelöst werden."

#: ../lib/kadm5/kadm_err.c:80
msgid "Unspecified password quality failure"
msgstr "nicht näher angegebener Passwortqualitätsfehlschlag"

#: ../lib/kadm5/kadm_err.c:81
msgid "Invalid key/salt tuples"
msgstr "ungültige Schlüssel-/Salt-Tupel"

#: ../lib/kdb/adb_err.c:23
msgid "No Error"
msgstr "kein Fehler"

#: ../lib/kdb/adb_err.c:25
msgid "Principal or policy does not exist"
msgstr "Principal oder Richtlinie existiert nicht"

#: ../lib/kdb/adb_err.c:26
msgid "Database not initialized"
msgstr "Datenbank nicht initialisiert"

#: ../lib/kdb/adb_err.c:27
msgid "Invalid policy name"
msgstr "ungültiger Richtlinienname"

#: ../lib/kdb/adb_err.c:28
msgid "Invalid principal name"
msgstr "ungültiger Principal-Name"

#: ../lib/kdb/adb_err.c:31
msgid "Failure!"
msgstr "Fehlschlag!"

#: ../lib/kdb/adb_err.c:32
msgid "Bad lock mode"
msgstr "falscher Sperrmodus"

#: ../lib/kdb/adb_err.c:33
msgid "Cannot lock database"
msgstr "Datenbank kann nicht gesperrt werden"

#: ../lib/kdb/adb_err.c:34
msgid "Database not locked"
msgstr "Datenbank nicht gesperrt"

#: ../lib/kdb/adb_err.c:35
msgid "KADM5 administration database lock file missing"
msgstr "Sperrdatei der KADM5-Verwaltungsdatenbank fehlt"

#: ../lib/kdb/adb_err.c:36
msgid "Insufficient permission to lock file"
msgstr "keine ausreichenden Rechte zum Sperren der Datei"

#: ../lib/krb5/error_tables/k5e1_err.c:23
msgid "Plugin does not support interface version"
msgstr "Erweiterung unterstützt nicht die Schnittstellenversion"

#: ../lib/krb5/error_tables/k5e1_err.c:24
msgid "Invalid module specifier"
msgstr "ungültige Modulangabe"

#: ../lib/krb5/error_tables/k5e1_err.c:25
msgid "Plugin module name not found"
msgstr "Erweiterungsmodulname nicht gefunden"

#: ../lib/krb5/error_tables/k5e1_err.c:26
msgid "The KDC should discard this request"
msgstr "Das KDC sollte diese Anfrage verwerfen"

#: ../lib/krb5/error_tables/k5e1_err.c:27
msgid "Can't create new subsidiary cache"
msgstr "Der neue ergänzende Zwischenspeicher kann nicht erzeugt werden"

#: ../lib/krb5/error_tables/k5e1_err.c:28
msgid "Invalid keyring anchor name"
msgstr "ungültiger Schlüsselbundverankerungsname"

#: ../lib/krb5/error_tables/k5e1_err.c:29
msgid "Unknown keyring collection version"
msgstr "unbekannte Schlüsselbundsammlungsversion"

#: ../lib/krb5/error_tables/k5e1_err.c:30
msgid "Invalid UID in persistent keyring name"
msgstr "ungültige UID im beständigen Schlüsselbundnamen"

#: ../lib/krb5/error_tables/k5e1_err.c:31
msgid "Malformed reply from KCM daemon"
msgstr "Antwort des KCM-Daemons hat die falsche Form"

#: ../lib/krb5/error_tables/k5e1_err.c:32
msgid "Mach RPC error communicating with KCM daemon"
msgstr "Mach-RPC-Fehler beim der Kommunikation mit dem KCM-Daemon"

#: ../lib/krb5/error_tables/k5e1_err.c:33
msgid "KCM daemon reply too big"
msgstr "Antwort des KCM-Daemons zu groß"

#: ../lib/krb5/error_tables/k5e1_err.c:34
msgid "No KCM server found"
msgstr "Kein KCM-Server gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:24
msgid "Client's entry in database has expired"
msgstr "Eintrag des Clients in der Datenbank ist abgelaufen"

#: ../lib/krb5/error_tables/krb5_err.c:25
msgid "Server's entry in database has expired"
msgstr "Eintrag des Servers in der Datenbank ist abgelaufen"

#: ../lib/krb5/error_tables/krb5_err.c:26
msgid "Requested protocol version not supported"
msgstr "angeforderte Protokollversion nicht unterstützt"

#: ../lib/krb5/error_tables/krb5_err.c:27
msgid "Client's key is encrypted in an old master key"
msgstr ""
"Der Schlüssel des Clients wurde mit einem alten Hauptschlüssel verschlüsselt."

#: ../lib/krb5/error_tables/krb5_err.c:28
msgid "Server's key is encrypted in an old master key"
msgstr ""
"Der Schlüssel des Servers wurde mit einem alten Hauptschlüssel verschlüsselt."

#: ../lib/krb5/error_tables/krb5_err.c:29
msgid "Client not found in Kerberos database"
msgstr "Client nicht in der Kerberos-Datenbank gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:30
msgid "Server not found in Kerberos database"
msgstr "Server nicht in der Kerberos-Datenbank gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:31
msgid "Principal has multiple entries in Kerberos database"
msgstr "Principal hat in der Kerberos-Datenbank mehrere Einträge"

#: ../lib/krb5/error_tables/krb5_err.c:32
msgid "Client or server has a null key"
msgstr "Client oder Server hat einen Nullschlüssel"

#: ../lib/krb5/error_tables/krb5_err.c:33
msgid "Ticket is ineligible for postdating"
msgstr "Ticket ist zum Vordatieren ungeeignet"

#: ../lib/krb5/error_tables/krb5_err.c:34
msgid "Requested effective lifetime is negative or too short"
msgstr "Die angeforderte effektive Lebensdauer ist negativ oder zu kurz."

#: ../lib/krb5/error_tables/krb5_err.c:35
msgid "KDC policy rejects request"
msgstr "KDC-Richtlinie weist die Anfrage zurück"

#: ../lib/krb5/error_tables/krb5_err.c:36
msgid "KDC can't fulfill requested option"
msgstr "KDC kann erforderliche Option nicht erfüllen"

#: ../lib/krb5/error_tables/krb5_err.c:37
msgid "KDC has no support for encryption type"
msgstr "KDC unterstützt diesen Verschlüsselungstyp nicht"

#: ../lib/krb5/error_tables/krb5_err.c:38
msgid "KDC has no support for checksum type"
msgstr "KDC unterstützt diesen Prüfsummentyp nicht"

#: ../lib/krb5/error_tables/krb5_err.c:39
msgid "KDC has no support for padata type"
msgstr "KDC unterstützt diesen Padata-Typ nicht"

#: ../lib/krb5/error_tables/krb5_err.c:40
msgid "KDC has no support for transited type"
msgstr "KDC unterstützt diesen Übergangstyp nicht"

#: ../lib/krb5/error_tables/krb5_err.c:41
msgid "Clients credentials have been revoked"
msgstr "Anmeldedaten des Clients wurden widerrufen"

#: ../lib/krb5/error_tables/krb5_err.c:42
msgid "Credentials for server have been revoked"
msgstr "Anmeldedaten für den Server wurden widerrufen"

#: ../lib/krb5/error_tables/krb5_err.c:43
msgid "TGT has been revoked"
msgstr "TGT wurde widerrufen"

#: ../lib/krb5/error_tables/krb5_err.c:44
msgid "Client not yet valid - try again later"
msgstr "Client noch nicht gültig – versuchen Sie es später noch einmal"

#: ../lib/krb5/error_tables/krb5_err.c:45
msgid "Server not yet valid - try again later"
msgstr "Server noch nicht gültig – versuchen Sie es später noch einmal"

#: ../lib/krb5/error_tables/krb5_err.c:46
msgid "Password has expired"
msgstr "Passwort ist abgelaufen"

#: ../lib/krb5/error_tables/krb5_err.c:47
msgid "Preauthentication failed"
msgstr "Vorauthentifizierung fehlgeschlagen"

#: ../lib/krb5/error_tables/krb5_err.c:48
msgid "Additional pre-authentication required"
msgstr "zusätzlich Vorauthentifizierung erforderlich"

#: ../lib/krb5/error_tables/krb5_err.c:49
msgid "Requested server and ticket don't match"
msgstr "abgefragter Server und Ticket passen nicht zusammen"

#: ../lib/krb5/error_tables/krb5_err.c:50
msgid "Server principal valid for user2user only"
msgstr "Der Server-Principal ist nur für »user2user« gültig"

#: ../lib/krb5/error_tables/krb5_err.c:51
msgid "KDC policy rejects transited path"
msgstr "KDC-Richtlinie verwirft durchgereichten Pfad"

#: ../lib/krb5/error_tables/krb5_err.c:52
msgid "A service is not available that is required to process the request"
msgstr ""
"Ein Dienst, der zum Verarbeiten der Abfrage erforderlich ist, ist nicht "
"verfügbar."

#: ../lib/krb5/error_tables/krb5_err.c:53
msgid "KRB5 error code 30"
msgstr "KRB5-Fehlercode 30"

#: ../lib/krb5/error_tables/krb5_err.c:54
msgid "Decrypt integrity check failed"
msgstr "Entschlüsselungsintegritätsprüfung fehlgeschlagen"

#: ../lib/krb5/error_tables/krb5_err.c:55
msgid "Ticket expired"
msgstr "Ticket abgelaufen"

#: ../lib/krb5/error_tables/krb5_err.c:56
msgid "Ticket not yet valid"
msgstr "Ticket noch nicht gültig"

#: ../lib/krb5/error_tables/krb5_err.c:57
msgid "Request is a replay"
msgstr "Anfrage ist eine Wiederholung"

#: ../lib/krb5/error_tables/krb5_err.c:58
msgid "The ticket isn't for us"
msgstr "Das Ticket ist nicht für uns."

#: ../lib/krb5/error_tables/krb5_err.c:59
msgid "Ticket/authenticator don't match"
msgstr "Ticket/Schlüsselziffer passen nicht zueinander"

#: ../lib/krb5/error_tables/krb5_err.c:60
msgid "Clock skew too great"
msgstr "Uhrzeitabweichung zu groß"

#: ../lib/krb5/error_tables/krb5_err.c:61
msgid "Incorrect net address"
msgstr "falsche Netzwerkadresse"

#: ../lib/krb5/error_tables/krb5_err.c:62
msgid "Protocol version mismatch"
msgstr "Protokollversion passt nicht"

#: ../lib/krb5/error_tables/krb5_err.c:63
msgid "Invalid message type"
msgstr "ungültiger Nachrichtentyp"

#: ../lib/krb5/error_tables/krb5_err.c:64
msgid "Message stream modified"
msgstr "Nachrichtendatenstrom geändert"

#: ../lib/krb5/error_tables/krb5_err.c:65
msgid "Message out of order"
msgstr "Nachricht nicht in Ordnung"

#: ../lib/krb5/error_tables/krb5_err.c:66
msgid "Illegal cross-realm ticket"
msgstr "Widerrechliches Realm-übergreifendes Ticket"

#: ../lib/krb5/error_tables/krb5_err.c:67
msgid "Key version is not available"
msgstr "Schlüsselversion ist nicht verfügbar"

#: ../lib/krb5/error_tables/krb5_err.c:68
msgid "Service key not available"
msgstr "Dienstschlüssel nicht verfügbar"

#: ../lib/krb5/error_tables/krb5_err.c:69
#: ../lib/krb5/error_tables/krb5_err.c:181
msgid "Mutual authentication failed"
msgstr "gegenseitige Authentifizierung fehlgeschlagen"

#: ../lib/krb5/error_tables/krb5_err.c:70
msgid "Incorrect message direction"
msgstr "falsche Nachrichtenrichtung"

#: ../lib/krb5/error_tables/krb5_err.c:71
msgid "Alternative authentication method required"
msgstr "alternative Authentifizierungsmethode erforderlich"

#: ../lib/krb5/error_tables/krb5_err.c:72
msgid "Incorrect sequence number in message"
msgstr "falsche Sequenznummer in der Nachricht"

#: ../lib/krb5/error_tables/krb5_err.c:73
msgid "Inappropriate type of checksum in message"
msgstr "ungeeigneter Prüfsummentyp in der Nachricht"

#: ../lib/krb5/error_tables/krb5_err.c:74
msgid "Policy rejects transited path"
msgstr "Richtlinie verwirft durchgereichten Pfad"

#: ../lib/krb5/error_tables/krb5_err.c:75
msgid "Response too big for UDP, retry with TCP"
msgstr "Antwort für UDP zu groß, erneuter Versuch mit TCP"

#: ../lib/krb5/error_tables/krb5_err.c:76
msgid "KRB5 error code 53"
msgstr "KRB5-Fehlercode 53"

#: ../lib/krb5/error_tables/krb5_err.c:77
msgid "KRB5 error code 54"
msgstr "KRB5-Fehlercode 54"

#: ../lib/krb5/error_tables/krb5_err.c:78
msgid "KRB5 error code 55"
msgstr "KRB5-Fehlercode 55"

#: ../lib/krb5/error_tables/krb5_err.c:79
msgid "KRB5 error code 56"
msgstr "KRB5-Fehlercode 56"

#: ../lib/krb5/error_tables/krb5_err.c:80
msgid "KRB5 error code 57"
msgstr "KRB5-Fehlercode 57"

#: ../lib/krb5/error_tables/krb5_err.c:81
msgid "KRB5 error code 58"
msgstr "KRB5-Fehlercode 58"

#: ../lib/krb5/error_tables/krb5_err.c:82
msgid "KRB5 error code 59"
msgstr "KRB5-Fehlercode 59"

#: ../lib/krb5/error_tables/krb5_err.c:83
msgid "Generic error (see e-text)"
msgstr "allgemeiner Fehler (siehe E-Text)"

#: ../lib/krb5/error_tables/krb5_err.c:84
msgid "Field is too long for this implementation"
msgstr "Feld ist für diese Implementierung zu lang"

#: ../lib/krb5/error_tables/krb5_err.c:85
msgid "Client not trusted"
msgstr "Client nicht vertrauenswürdig"

#: ../lib/krb5/error_tables/krb5_err.c:86
msgid "KDC not trusted"
msgstr "KDC nicht vertrauenswürdig"

#: ../lib/krb5/error_tables/krb5_err.c:87
msgid "Invalid signature"
msgstr "ungültige Signatur"

#: ../lib/krb5/error_tables/krb5_err.c:88
msgid "Key parameters not accepted"
msgstr "Schlüsselparameter nicht akzeptiert"

#: ../lib/krb5/error_tables/krb5_err.c:89
msgid "Certificate mismatch"
msgstr "Zertifikat passt nicht"

#: ../lib/krb5/error_tables/krb5_err.c:90
msgid "No ticket granting ticket"
msgstr "kein ticketgewährendes Ticket"

#: ../lib/krb5/error_tables/krb5_err.c:91
msgid "Realm not local to KDC"
msgstr "Realm für KDC nicht lokal"

#: ../lib/krb5/error_tables/krb5_err.c:92
msgid "User to user required"
msgstr "Benutzer-zu-Benutzer erforderlich"

#: ../lib/krb5/error_tables/krb5_err.c:93
msgid "Can't verify certificate"
msgstr "Zertifikat kann nicht überprüft werden"

#: ../lib/krb5/error_tables/krb5_err.c:94
msgid "Invalid certificate"
msgstr "ungültiges Zertifikat"

#: ../lib/krb5/error_tables/krb5_err.c:95
msgid "Revoked certificate"
msgstr "widerrufenes Zertifikat"

#: ../lib/krb5/error_tables/krb5_err.c:96
msgid "Revocation status unknown"
msgstr "Widerrufsstatus unbekannt"

#: ../lib/krb5/error_tables/krb5_err.c:97
msgid "Revocation status unavailable"
msgstr "Widerrufsstatus nicht verfügbar"

#: ../lib/krb5/error_tables/krb5_err.c:98
msgid "Client name mismatch"
msgstr "Client-Name passt nicht"

#: ../lib/krb5/error_tables/krb5_err.c:99
msgid "KDC name mismatch"
msgstr "KDC-Name passt nicht"

#: ../lib/krb5/error_tables/krb5_err.c:100
msgid "Inconsistent key purpose"
msgstr "inkonstistenter Schlüsselzweck"

#: ../lib/krb5/error_tables/krb5_err.c:101
msgid "Digest in certificate not accepted"
msgstr "Kurzfassung im Zertifikat nicht akzeptiert"

#: ../lib/krb5/error_tables/krb5_err.c:102
msgid "Checksum must be included"
msgstr "Prüfsumme muss enthalten sein"

#: ../lib/krb5/error_tables/krb5_err.c:103
msgid "Digest in signed-data not accepted"
msgstr "Kurzfassung in signierten Daten nicht akzeptiert"

#: ../lib/krb5/error_tables/krb5_err.c:104
msgid "Public key encryption not supported"
msgstr "Asymetrische Verschlüsselung nicht unterstützt"

#: ../lib/krb5/error_tables/krb5_err.c:105
msgid "KRB5 error code 82"
msgstr "KRB5-Fehlercode 82"

#: ../lib/krb5/error_tables/krb5_err.c:106
msgid "KRB5 error code 83"
msgstr "KRB5-Fehlercode 83"

#: ../lib/krb5/error_tables/krb5_err.c:107
msgid "KRB5 error code 84"
msgstr "KRB5-Fehlercode 84"

#: ../lib/krb5/error_tables/krb5_err.c:108
msgid "The IAKERB proxy could not find a KDC"
msgstr "Der IAKERB-Proxy konnte kein KDC finden."

#: ../lib/krb5/error_tables/krb5_err.c:109
msgid "The KDC did not respond to the IAKERB proxy"
msgstr "Das KDC anwortete dem IAKERB-Proxy nicht."

#: ../lib/krb5/error_tables/krb5_err.c:110
msgid "KRB5 error code 87"
msgstr "KRB5-Fehlercode 87"

#: ../lib/krb5/error_tables/krb5_err.c:111
msgid "KRB5 error code 88"
msgstr "KRB5-Fehlercode 88"

#: ../lib/krb5/error_tables/krb5_err.c:112
msgid "KRB5 error code 89"
msgstr "KRB5-Fehlercode 89"

#: ../lib/krb5/error_tables/krb5_err.c:113
msgid "KRB5 error code 90"
msgstr "KRB5-Fehlercode 90"

#: ../lib/krb5/error_tables/krb5_err.c:114
msgid "KRB5 error code 91"
msgstr "KRB5-Fehlercode 91"

#: ../lib/krb5/error_tables/krb5_err.c:115
msgid "KRB5 error code 92"
msgstr "KRB5-Fehlercode 92"

#: ../lib/krb5/error_tables/krb5_err.c:116
msgid "An unsupported critical FAST option was requested"
msgstr "Es wurde eine nicht unterstützte kritische FAST-Aktion angefordert."

#: ../lib/krb5/error_tables/krb5_err.c:117
msgid "KRB5 error code 94"
msgstr "KRB5-Fehlercode 94"

#: ../lib/krb5/error_tables/krb5_err.c:118
msgid "KRB5 error code 95"
msgstr "KRB5-Fehlercode 95"

#: ../lib/krb5/error_tables/krb5_err.c:119
msgid "KRB5 error code 96"
msgstr "KRB5-Fehlercode 96"

#: ../lib/krb5/error_tables/krb5_err.c:120
msgid "KRB5 error code 97"
msgstr "KRB5-Fehlercode 97"

#: ../lib/krb5/error_tables/krb5_err.c:121
msgid "KRB5 error code 98"
msgstr "KRB5-Fehlercode 98"

#: ../lib/krb5/error_tables/krb5_err.c:122
msgid "KRB5 error code 99"
msgstr "KRB5-Fehlercode 99"

#: ../lib/krb5/error_tables/krb5_err.c:123
msgid "No acceptable KDF offered"
msgstr "kein akzeptables KDF angeboten"

#: ../lib/krb5/error_tables/krb5_err.c:124
msgid "KRB5 error code 101"
msgstr "KRB5-Fehlercode 101"

#: ../lib/krb5/error_tables/krb5_err.c:125
msgid "KRB5 error code 102"
msgstr "KRB5-Fehlercode 102"

#: ../lib/krb5/error_tables/krb5_err.c:126
msgid "KRB5 error code 103"
msgstr "KRB5-Fehlercode 103"

#: ../lib/krb5/error_tables/krb5_err.c:127
msgid "KRB5 error code 104"
msgstr "KRB5-Fehlercode 104"

#: ../lib/krb5/error_tables/krb5_err.c:128
msgid "KRB5 error code 105"
msgstr "KRB5-Fehlercode 105"

#: ../lib/krb5/error_tables/krb5_err.c:129
msgid "KRB5 error code 106"
msgstr "KRB5-Fehlercode 106"

#: ../lib/krb5/error_tables/krb5_err.c:130
msgid "KRB5 error code 107"
msgstr "KRB5-Fehlercode 107"

#: ../lib/krb5/error_tables/krb5_err.c:131
msgid "KRB5 error code 108"
msgstr "KRB5-Fehlercode 108"

#: ../lib/krb5/error_tables/krb5_err.c:132
msgid "KRB5 error code 109"
msgstr "KRB5-Fehlercode 109"

#: ../lib/krb5/error_tables/krb5_err.c:133
msgid "KRB5 error code 110"
msgstr "KRB5-Fehlercode 110"

#: ../lib/krb5/error_tables/krb5_err.c:134
msgid "KRB5 error code 111"
msgstr "KRB5-Fehlercode 111"

#: ../lib/krb5/error_tables/krb5_err.c:135
msgid "KRB5 error code 112"
msgstr "KRB5-Fehlercode 112"

#: ../lib/krb5/error_tables/krb5_err.c:136
msgid "KRB5 error code 113"
msgstr "KRB5-Fehlercode 113"

#: ../lib/krb5/error_tables/krb5_err.c:137
msgid "KRB5 error code 114"
msgstr "KRB5-Fehlercode 114"

#: ../lib/krb5/error_tables/krb5_err.c:138
msgid "KRB5 error code 115"
msgstr "KRB5-Fehlercode 115"

#: ../lib/krb5/error_tables/krb5_err.c:139
msgid "KRB5 error code 116"
msgstr "KRB5-Fehlercode 116"

#: ../lib/krb5/error_tables/krb5_err.c:140
msgid "KRB5 error code 117"
msgstr "KRB5-Fehlercode 117"

#: ../lib/krb5/error_tables/krb5_err.c:141
msgid "KRB5 error code 118"
msgstr "KRB5-Fehlercode 118"

#: ../lib/krb5/error_tables/krb5_err.c:142
msgid "KRB5 error code 119"
msgstr "KRB5-Fehlercode 119"

#: ../lib/krb5/error_tables/krb5_err.c:143
msgid "KRB5 error code 120"
msgstr "KRB5-Fehlercode 120"

#: ../lib/krb5/error_tables/krb5_err.c:144
msgid "KRB5 error code 121"
msgstr "KRB5-Fehlercode 121"

#: ../lib/krb5/error_tables/krb5_err.c:145
msgid "KRB5 error code 122"
msgstr "KRB5-Fehlercode 122"

#: ../lib/krb5/error_tables/krb5_err.c:146
msgid "KRB5 error code 123"
msgstr "KRB5-Fehlercode 123"

#: ../lib/krb5/error_tables/krb5_err.c:147
msgid "KRB5 error code 124"
msgstr "KRB5-Fehlercode 124"

#: ../lib/krb5/error_tables/krb5_err.c:148
msgid "KRB5 error code 125"
msgstr "KRB5-Fehlercode 125"

#: ../lib/krb5/error_tables/krb5_err.c:149
msgid "KRB5 error code 126"
msgstr "KRB5-Fehlercode 126"

#: ../lib/krb5/error_tables/krb5_err.c:150
msgid "KRB5 error code 127"
msgstr "KRB5-Fehlercode 127"

#: ../lib/krb5/error_tables/krb5_err.c:151
#: ../lib/krb5/error_tables/kdb5_err.c:23
msgid "$Id$"
msgstr "$Id$"

#: ../lib/krb5/error_tables/krb5_err.c:152
msgid "Invalid flag for file lock mode"
msgstr "ungültiger Schalter für den Datei-Sperrmodus"

#: ../lib/krb5/error_tables/krb5_err.c:153
msgid "Cannot read password"
msgstr "Passwort kann nicht gelesen werden"

#: ../lib/krb5/error_tables/krb5_err.c:154
msgid "Password mismatch"
msgstr "Passwort stimmt nicht überein"

#: ../lib/krb5/error_tables/krb5_err.c:155
msgid "Password read interrupted"
msgstr "Lesen des Passworts unterbrochen"

#: ../lib/krb5/error_tables/krb5_err.c:156
msgid "Illegal character in component name"
msgstr "ungültiges Zeichen in Komponentenname"

#: ../lib/krb5/error_tables/krb5_err.c:157
msgid "Malformed representation of principal"
msgstr "Darstellung des Principals in falscher Form"

#: ../lib/krb5/error_tables/krb5_err.c:158
msgid "Can't open/find Kerberos configuration file"
msgstr "Kerberos-Konfigurationsdatei kann nicht geöffnet/gefunden werden"

#: ../lib/krb5/error_tables/krb5_err.c:159
msgid "Improper format of Kerberos configuration file"
msgstr "Format der Kerberos-Konfigurationsdatei ist ungeeignet"

#: ../lib/krb5/error_tables/krb5_err.c:160
msgid "Insufficient space to return complete information"
msgstr "Platz reicht nicht zur Rückgabe aller Informationen aus"

#: ../lib/krb5/error_tables/krb5_err.c:161
msgid "Invalid message type specified for encoding"
msgstr "der zum Kodieren angegebene Nachrichtentyp ist ungültig"

#: ../lib/krb5/error_tables/krb5_err.c:162
msgid "Credential cache name malformed"
msgstr "falsche Form des Anmeldedatenzwischenspeichernamens"

#: ../lib/krb5/error_tables/krb5_err.c:163
msgid "Unknown credential cache type"
msgstr "unbekannter Anmeldedatenzwischenspeichertyp"

#: ../lib/krb5/error_tables/krb5_err.c:164
msgid "Matching credential not found"
msgstr "keine passenden Anmeldedaten gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:165
msgid "End of credential cache reached"
msgstr "Ende des Anmeldedatenzwischenspeichers erreicht"

#: ../lib/krb5/error_tables/krb5_err.c:166
msgid "Request did not supply a ticket"
msgstr "Anfrage lieferte kein Ticket"

#: ../lib/krb5/error_tables/krb5_err.c:167
msgid "Wrong principal in request"
msgstr "falscher Principal in der Anfrage"

#: ../lib/krb5/error_tables/krb5_err.c:168
msgid "Ticket has invalid flag set"
msgstr "Das Ticket hat einen falsch gesetzten Schalter."

#: ../lib/krb5/error_tables/krb5_err.c:169
msgid "Requested principal and ticket don't match"
msgstr "angeforderter Principal und Ticket passen nicht zusammen"

#: ../lib/krb5/error_tables/krb5_err.c:170
msgid "KDC reply did not match expectations"
msgstr "KDC-Antwort entsprach nicht den Erwartungen"

#: ../lib/krb5/error_tables/krb5_err.c:171
msgid "Clock skew too great in KDC reply"
msgstr "Zeitversatz in der KDC-Antwort zu groß"

#: ../lib/krb5/error_tables/krb5_err.c:172
msgid "Client/server realm mismatch in initial ticket request"
msgstr ""
"Client-/Server-Realm passen in der anfänglichen Ticketanfrage nicht zusammen."

#: ../lib/krb5/error_tables/krb5_err.c:173
msgid "Program lacks support for encryption type"
msgstr ""
"Dem Programm fehlt es an der Unterstützung für den Verschlüsselungstyp."

#: ../lib/krb5/error_tables/krb5_err.c:174
msgid "Program lacks support for key type"
msgstr "Dem Programm fehlt es an der Unterstützung für den Schlüsseltyp."

#: ../lib/krb5/error_tables/krb5_err.c:175
msgid "Requested encryption type not used in message"
msgstr ""
"Der angeforderte Verschlüsselungstyp wird in der Nachricht nicht verwendet."

#: ../lib/krb5/error_tables/krb5_err.c:176
msgid "Program lacks support for checksum type"
msgstr "Dem Programm fehlt es an der Unterstützung für den Prüfsummentyp."

#: ../lib/krb5/error_tables/krb5_err.c:177
msgid "Cannot find KDC for requested realm"
msgstr "KDC für angeforderten Realm kann nicht gefunden werden"

#: ../lib/krb5/error_tables/krb5_err.c:178
msgid "Kerberos service unknown"
msgstr "Kerberos-Dienst unbekannt"

#: ../lib/krb5/error_tables/krb5_err.c:179
msgid "Cannot contact any KDC for requested realm"
msgstr "Für den angeforderten Realm kann kein KDC kontaktiert werden."

#: ../lib/krb5/error_tables/krb5_err.c:180
msgid "No local name found for principal name"
msgstr "Für den Principal-Namen wurde kein lokaler Name gefunden."

#: ../lib/krb5/error_tables/krb5_err.c:182
msgid "Replay cache type is already registered"
msgstr "Wiederholungszwischenspeichertyp ist bereits registriert"

#: ../lib/krb5/error_tables/krb5_err.c:183
msgid "No more memory to allocate (in replay cache code)"
msgstr ""
"kein Speicher mehr zu reservieren (im Wiederholungszwischenspeichercode)"

#: ../lib/krb5/error_tables/krb5_err.c:184
msgid "Replay cache type is unknown"
msgstr "Wiederholungszwischenspeichertyp ist unbekannt"

#: ../lib/krb5/error_tables/krb5_err.c:185
msgid "Generic unknown RC error"
msgstr "allgemeiner unbekannter Wiederholungszwischenspeicherfehler"

#: ../lib/krb5/error_tables/krb5_err.c:186
msgid "Message is a replay"
msgstr "Nachricht ist eine Wiederholung"

#: ../lib/krb5/error_tables/krb5_err.c:187
msgid "Replay cache I/O operation failed"
msgstr "Wiederholungszwischenspeicher-E/A-Aktion fehlgeschlagen"

#: ../lib/krb5/error_tables/krb5_err.c:188
msgid "Replay cache type does not support non-volatile storage"
msgstr ""
"Wiederholungszwischenspeichertyp unterstützt keinen beständigen Speicher"

#: ../lib/krb5/error_tables/krb5_err.c:189
msgid "Replay cache name parse/format error"
msgstr "Auswerte-/Formatfehler im Wiederholungszwischenspeichernamens"

#: ../lib/krb5/error_tables/krb5_err.c:190
msgid "End-of-file on replay cache I/O"
msgstr "Dateiende bei der E/A des Wiederholungszwischenspeichers"

#: ../lib/krb5/error_tables/krb5_err.c:191
msgid "No more memory to allocate (in replay cache I/O code)"
msgstr ""
"kein weiterer Speicher reservierbar (im Wiederholungszwischenspeicher-E/A-"
"Code)"

#: ../lib/krb5/error_tables/krb5_err.c:192
msgid "Permission denied in replay cache code"
msgstr "Zugriff im Wiederholungszwischenspeichercode verweigert"

#: ../lib/krb5/error_tables/krb5_err.c:193
msgid "I/O error in replay cache i/o code"
msgstr "E/A-Fehler im Wiederholungszwischenspeicher-E/A-Code"

#: ../lib/krb5/error_tables/krb5_err.c:194
msgid "Generic unknown RC/IO error"
msgstr "allgemeiner unbekannter Wiederholungszwischenspeicher-/E/A-Fehler"

#: ../lib/krb5/error_tables/krb5_err.c:195
msgid "Insufficient system space to store replay information"
msgstr ""
"Platz im System reicht nicht zum Speichern der Wiederholungsinformationen"

#: ../lib/krb5/error_tables/krb5_err.c:196
msgid "Can't open/find realm translation file"
msgstr "Realm-Übersetzungsdatei kann nicht geöffnet/gefunden werden"

#: ../lib/krb5/error_tables/krb5_err.c:197
msgid "Improper format of realm translation file"
msgstr "Format der Realm-Übersetzungsdatei ist ungeeignet"

#: ../lib/krb5/error_tables/krb5_err.c:198
msgid "Can't open/find lname translation database"
msgstr "die Lname-Übersetzungsdatenbank kann nicht geöffnet/gefunden werden"

#: ../lib/krb5/error_tables/krb5_err.c:199
msgid "No translation available for requested principal"
msgstr "Für den angeforderten Principal ist keine Übersetzung verfügbar."

#: ../lib/krb5/error_tables/krb5_err.c:200
msgid "Improper format of translation database entry"
msgstr "Format des Eintrags der Übersetzungsdatenbank ist ungeeignet"

#: ../lib/krb5/error_tables/krb5_err.c:201
msgid "Cryptosystem internal error"
msgstr "interner Fehler des Verschlüsselungssystems"

#: ../lib/krb5/error_tables/krb5_err.c:202
msgid "Key table name malformed"
msgstr "falsche Form des Schlüsseltabellennamens"

#: ../lib/krb5/error_tables/krb5_err.c:203
msgid "Unknown Key table type"
msgstr "unbekannter Schlüsseltabellentyp"

#: ../lib/krb5/error_tables/krb5_err.c:204
msgid "Key table entry not found"
msgstr "Schlüsseltabelleneintrag nicht gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:205
msgid "End of key table reached"
msgstr "Ende der Schlüsseltabelle erreicht"

#: ../lib/krb5/error_tables/krb5_err.c:206
msgid "Cannot write to specified key table"
msgstr "in angegebene Schlüsseltabelle kann nicht geschrieben werden"

#: ../lib/krb5/error_tables/krb5_err.c:207
msgid "Error writing to key table"
msgstr "Fehler beim Schreiben in Schlüsseltabelle"

#: ../lib/krb5/error_tables/krb5_err.c:208
msgid "Cannot find ticket for requested realm"
msgstr "Ticket für angeforderten Realm kann nicht gefunden werden"

#: ../lib/krb5/error_tables/krb5_err.c:209
msgid "DES key has bad parity"
msgstr "DES-Schlüssel hat falsche Parität"

#: ../lib/krb5/error_tables/krb5_err.c:210
msgid "DES key is a weak key"
msgstr "DES-Schlüssel ist schwach"

#: ../lib/krb5/error_tables/krb5_err.c:211
msgid "Bad encryption type"
msgstr "falscher Verschlüsselungstyp"

#: ../lib/krb5/error_tables/krb5_err.c:212
msgid "Key size is incompatible with encryption type"
msgstr "Schlüssellänge ist nicht mit dem Verschlüsselungstyp kompatibel"

#: ../lib/krb5/error_tables/krb5_err.c:213
msgid "Message size is incompatible with encryption type"
msgstr "Nachrichtengröße ist nicht mit Verschlüsselungstyp kompatibel"

#: ../lib/krb5/error_tables/krb5_err.c:214
msgid "Credentials cache type is already registered."
msgstr "Anmeldedatenzwischenspeichertyp ist bereits registriert"

#: ../lib/krb5/error_tables/krb5_err.c:215
msgid "Key table type is already registered."
msgstr "Schlüsseltabellentyp ist bereits registriert"

#: ../lib/krb5/error_tables/krb5_err.c:216
msgid "Credentials cache I/O operation failed XXX"
msgstr "E/A-Aktion für Anmeldedatenzwischenspeicher fehlgeschlagen XXX"

#: ../lib/krb5/error_tables/krb5_err.c:217
msgid "Credentials cache permissions incorrect"
msgstr "Anmeldedatenzwischenspeicherrechte nicht korrekt"

#: ../lib/krb5/error_tables/krb5_err.c:218
msgid "No credentials cache found"
msgstr "kein Anmeldedatenzwischenspeicher gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:219
msgid "Internal credentials cache error"
msgstr "interner Anmeldedatenzwischenspeicherfehler"

#: ../lib/krb5/error_tables/krb5_err.c:220
msgid "Error writing to credentials cache"
msgstr "Fehler beim Schreiben in den Anmeldedatenzwischenspeicher"

#: ../lib/krb5/error_tables/krb5_err.c:221
msgid "No more memory to allocate (in credentials cache code)"
msgstr ""
"kein weiterer Speicher zu reservieren (im Anmeldedatenzwischenspeichercode)"

#: ../lib/krb5/error_tables/krb5_err.c:222
msgid "Bad format in credentials cache"
msgstr "falsches Format im Anmeldedatenzwischenspeicher"

#: ../lib/krb5/error_tables/krb5_err.c:223
msgid "No credentials found with supported encryption types"
msgstr "keine Anmeldedaten mit unterstützten Verschlüsselungstypen gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:224
msgid "Invalid KDC option combination (library internal error)"
msgstr "ungültige Kombination von KDC-Optionen (interner Bibliotheksfehler)"

#: ../lib/krb5/error_tables/krb5_err.c:225
msgid "Request missing second ticket"
msgstr "Der Anfrage fehlt das zweite Ticket."

#: ../lib/krb5/error_tables/krb5_err.c:226
msgid "No credentials supplied to library routine"
msgstr "der Bibliotheks-Routine wurden keine Anmeldedaten geliefert"

#: ../lib/krb5/error_tables/krb5_err.c:227
msgid "Bad sendauth version was sent"
msgstr "Es wurde eine falsche Sendauth-Version verschickt"

#: ../lib/krb5/error_tables/krb5_err.c:228
msgid "Bad application version was sent (via sendauth)"
msgstr "Es wurde eine falsche Anwendungsversion (über Sendauth) verschickt"

#: ../lib/krb5/error_tables/krb5_err.c:229
msgid "Bad response (during sendauth exchange)"
msgstr "falsche Antwort (beim Sendauth-Austausch)"

#: ../lib/krb5/error_tables/krb5_err.c:230
msgid "Server rejected authentication (during sendauth exchange)"
msgstr "Server wies Authentifizierung (beim Sendauth-Austausch) zurück"

#: ../lib/krb5/error_tables/krb5_err.c:231
msgid "Unsupported preauthentication type"
msgstr "nicht unterstützter Vorauthentifizierungstyp"

#: ../lib/krb5/error_tables/krb5_err.c:232
msgid "Required preauthentication key not supplied"
msgstr "erforderlicher Vorauthentifizierungsschlüssel nicht bereitgestellt"

#: ../lib/krb5/error_tables/krb5_err.c:233
msgid "Generic preauthentication failure"
msgstr "allgemeiner Fehlschlag der Vorauthentifizierung"

#: ../lib/krb5/error_tables/krb5_err.c:234
msgid "Unsupported replay cache format version number"
msgstr ""
"nicht unterstütztes Versionsnummernformat des Wiederholungszwischenspeichers"

#: ../lib/krb5/error_tables/krb5_err.c:235
msgid "Unsupported credentials cache format version number"
msgstr ""
"nicht unterstütztes Versionsnummernformat des Anmeldedatenzwischenspeichers"

#: ../lib/krb5/error_tables/krb5_err.c:236
msgid "Unsupported key table format version number"
msgstr "nicht unterstütztes Versionsnummernformat der Schlüsseltabelle"

#: ../lib/krb5/error_tables/krb5_err.c:237
msgid "Program lacks support for address type"
msgstr "Dem Programm fehlt es an der Unterstützung des Adresstyps."

#: ../lib/krb5/error_tables/krb5_err.c:238
msgid "Message replay detection requires rcache parameter"
msgstr "Erkennung der Antwortnachricht erfordert den Parameter »rcache«"

#: ../lib/krb5/error_tables/krb5_err.c:239
msgid "Hostname cannot be canonicalized"
msgstr "Rechnername kann nicht in Normalform gebracht werden"

#: ../lib/krb5/error_tables/krb5_err.c:240
msgid "Cannot determine realm for host"
msgstr "Realm für Rechner kann nicht bestimmt werden"

#: ../lib/krb5/error_tables/krb5_err.c:241
msgid "Conversion to service principal undefined for name type"
msgstr "Umwandlung in Dienst-Principal für Namenstyp nicht definiert"

#: ../lib/krb5/error_tables/krb5_err.c:242
msgid "Initial Ticket response appears to be Version 4 error"
msgstr "anfängliche Ticket-Antwort scheint ein Fehler der Version 4 zu sein"

#: ../lib/krb5/error_tables/krb5_err.c:243
msgid "Cannot resolve network address for KDC in requested realm"
msgstr ""
"Netzwerkadresse für KDC im angeforderten Realm kann nicht aufgelöst werden"

#: ../lib/krb5/error_tables/krb5_err.c:244
msgid "Requesting ticket can't get forwardable tickets"
msgstr "anforderndes Ticket kann keine weiterleitbaren Tickets holen"

#: ../lib/krb5/error_tables/krb5_err.c:245
msgid "Bad principal name while trying to forward credentials"
msgstr "falscher Principal beim Versuch, Anmeldedaten weiterzuleiten"

#: ../lib/krb5/error_tables/krb5_err.c:246
msgid "Looping detected inside krb5_get_in_tkt"
msgstr "Schleife innerhalb von »krb5_get_in_tkt« entdeckt"

#: ../lib/krb5/error_tables/krb5_err.c:247
msgid "Configuration file does not specify default realm"
msgstr "Konfigurationsdatei gibt keinen Standard-Realm an"

#: ../lib/krb5/error_tables/krb5_err.c:248
msgid "Bad SAM flags in obtain_sam_padata"
msgstr "falsche SAM-Schalter in »obtain_sam_padata«"

#: ../lib/krb5/error_tables/krb5_err.c:249
msgid "Invalid encryption type in SAM challenge"
msgstr "ungültiger Verschlüsselungstyp in der SAM-Aufforderung"

#: ../lib/krb5/error_tables/krb5_err.c:250
msgid "Missing checksum in SAM challenge"
msgstr "fehlende Prüfsumme in der SAM-Aufforderung"

#: ../lib/krb5/error_tables/krb5_err.c:251
msgid "Bad checksum in SAM challenge"
msgstr "falsche Prüfsumme in der SAM-Aufforderung"

#: ../lib/krb5/error_tables/krb5_err.c:252
msgid "Keytab name too long"
msgstr "Schlüsseltabellennamen zu lang"

#: ../lib/krb5/error_tables/krb5_err.c:253
msgid "Key version number for principal in key table is incorrect"
msgstr ""
"Schlüsselversionsnummer des Principals in der Schlüsseltabelle ist nicht "
"korrekt"

#: ../lib/krb5/error_tables/krb5_err.c:254
msgid "This application has expired"
msgstr "Diese Anwendung ist abgelaufen."

#: ../lib/krb5/error_tables/krb5_err.c:255
msgid "This Krb5 library has expired"
msgstr "Diese Krb5-Bibliothek ist abgelaufen."

#: ../lib/krb5/error_tables/krb5_err.c:256
msgid "New password cannot be zero length"
msgstr "Das neue Passwort kann nicht die Länge Null haben."

#: ../lib/krb5/error_tables/krb5_err.c:258
msgid "Bad format in keytab"
msgstr "falsches Format in der Schlüsseltabelle"

#: ../lib/krb5/error_tables/krb5_err.c:259
msgid "Encryption type not permitted"
msgstr "Verschlüsselungstyp nicht erlaubt"

#: ../lib/krb5/error_tables/krb5_err.c:260
msgid "No supported encryption types (config file error?)"
msgstr ""
"keine unterstützten Verschlüsselungstypen (Fehler in der "
"Konfigurationsdatei?)"

#: ../lib/krb5/error_tables/krb5_err.c:261
msgid "Program called an obsolete, deleted function"
msgstr "Das Programm rief eine veraltete, gelöschte Funktion auf."

#: ../lib/krb5/error_tables/krb5_err.c:262
msgid "unknown getaddrinfo failure"
msgstr "unbekannter Getaddrinfo-Fehlschlag"

#: ../lib/krb5/error_tables/krb5_err.c:263
msgid "no data available for host/domain name"
msgstr "keine Daten für Rechner/Domain-Namen verfügbar"

#: ../lib/krb5/error_tables/krb5_err.c:264
msgid "host/domain name not found"
msgstr "Rechner/Domain-Name nicht gefunden"

#: ../lib/krb5/error_tables/krb5_err.c:265
msgid "service name unknown"
msgstr "Dienstname unbekannt"

#: ../lib/krb5/error_tables/krb5_err.c:266
msgid "Cannot determine realm for numeric host address"
msgstr "Realm für numerische Rechneradresse kann nicht bestimmt werden"

#: ../lib/krb5/error_tables/krb5_err.c:267
msgid "Invalid key generation parameters from KDC"
msgstr "ungültige Parameter zum Erzeugen von Schlüsseln vom KDC"

#: ../lib/krb5/error_tables/krb5_err.c:268
msgid "service not available"
msgstr "Dienst nicht verfügbar"

#: ../lib/krb5/error_tables/krb5_err.c:269
msgid "Ccache function not supported: read-only ccache type"
msgstr "Ccache-Funktion nicht unterstützt: Ccache-Typ nur lesbar"

#: ../lib/krb5/error_tables/krb5_err.c:270
msgid "Ccache function not supported: not implemented"
msgstr "Ccache-Funktion nicht unterstützt: nicht implementiert"

#: ../lib/krb5/error_tables/krb5_err.c:271
msgid "Invalid format of Kerberos lifetime or clock skew string"
msgstr ""
"ungültiges Format der Kerberos-Lebensdauer oder der Zeitversatzzeichenkette"

#: ../lib/krb5/error_tables/krb5_err.c:272
msgid "Supplied data not handled by this plugin"
msgstr ""
"Die bereitgestellten Daten werden nicht von dieser Erweiterung behandelt."

#: ../lib/krb5/error_tables/krb5_err.c:273
msgid "Plugin does not support the operation"
msgstr "Erweiterung unterstützt diese Aktion nicht"

#: ../lib/krb5/error_tables/krb5_err.c:274
msgid "Invalid UTF-8 string"
msgstr "ungültige UTF-8-Zeichenkette"

#: ../lib/krb5/error_tables/krb5_err.c:275
msgid "FAST protected pre-authentication required but not supported by KDC"
msgstr ""
"FAST-geschützte Vorauthentifizierung erforderlich, aber nicht vom KDC "
"unterstützt"

#: ../lib/krb5/error_tables/krb5_err.c:276
msgid "Auth context must contain local address"
msgstr "Authentifizierungskontext muss lokale Adresse enthalten"

#: ../lib/krb5/error_tables/krb5_err.c:277
msgid "Auth context must contain remote address"
msgstr "Authentifizierungskontext muss ferne Adresse enthalten"

#: ../lib/krb5/error_tables/krb5_err.c:278
msgid "Tracing unsupported"
msgstr "Verfolgung nicht unterstützt"

#: ../lib/krb5/error_tables/kdb5_err.c:24
msgid "Entry already exists in database"
msgstr "Eintrag existiert bereits in der Datenbank"

#: ../lib/krb5/error_tables/kdb5_err.c:25
msgid "Database store error"
msgstr "Datenbank-Speicherfehler"

#: ../lib/krb5/error_tables/kdb5_err.c:26
msgid "Database read error"
msgstr "Datenbank-Lesefehler"

#: ../lib/krb5/error_tables/kdb5_err.c:27
msgid "Insufficient access to perform requested operation"
msgstr "Zugriffsrechte reichen nicht zur Durchführung der angeforderten Aktion"

#: ../lib/krb5/error_tables/kdb5_err.c:28
msgid "No such entry in the database"
msgstr "kein derartiger Eintrag in der Datenbank"

#: ../lib/krb5/error_tables/kdb5_err.c:29
msgid "Illegal use of wildcard"
msgstr "ungültige Verwendung eines Platzhalters"

#: ../lib/krb5/error_tables/kdb5_err.c:30
msgid "Database is locked or in use--try again later"
msgstr ""
"Datenbank ist gesperrt oder wird gerade benutzt – versuchen Sie es später "
"wieder"

#: ../lib/krb5/error_tables/kdb5_err.c:31
msgid "Database was modified during read"
msgstr "Datenbank wurde während des Lesens geändert"

#: ../lib/krb5/error_tables/kdb5_err.c:32
msgid "Database record is incomplete or corrupted"
msgstr "Datensatz ist unvollständig oder beschädigt"

#: ../lib/krb5/error_tables/kdb5_err.c:33
msgid "Attempt to lock database twice"
msgstr "Es wurde zweimal versucht, die Datenbank zu sperren."

#: ../lib/krb5/error_tables/kdb5_err.c:34
msgid "Attempt to unlock database when not locked"
msgstr ""
"Es wurde versucht, die Datenbank zu entsperren, obwohl sie nicht gesperrt "
"ist."

#: ../lib/krb5/error_tables/kdb5_err.c:35
msgid "Invalid kdb lock mode"
msgstr "ungültiger KDB-Sperrmodus"

#: ../lib/krb5/error_tables/kdb5_err.c:36
msgid "Database has not been initialized"
msgstr "Datenbank wurde nicht initialisiert"

#: ../lib/krb5/error_tables/kdb5_err.c:37
msgid "Database has already been initialized"
msgstr "Datenbank wurde bereits initialisiert"

#: ../lib/krb5/error_tables/kdb5_err.c:38
msgid "Bad direction for converting keys"
msgstr "falsche Richtung zum Umwandeln von Schlüsseln"

#: ../lib/krb5/error_tables/kdb5_err.c:39
msgid "Cannot find master key record in database"
msgstr "Hauptschlüsseldatensatz kann nicht in der Datenbank gefunden werden"

#: ../lib/krb5/error_tables/kdb5_err.c:40
msgid "Master key does not match database"
msgstr "Hauptschlüssel passt nicht zur Datenbank"

#: ../lib/krb5/error_tables/kdb5_err.c:41
msgid "Key size in database is invalid"
msgstr "Die Schlüssellänge in der Datenbank ist ungültig,"

#: ../lib/krb5/error_tables/kdb5_err.c:42
msgid "Cannot find/read stored master key"
msgstr "Der gespeicherte Hauptschlüssel kann nicht gefunden/gelesen werden."

#: ../lib/krb5/error_tables/kdb5_err.c:43
msgid "Stored master key is corrupted"
msgstr "Der gespeicherte Hauptschlüssel ist beschädigt."

#: ../lib/krb5/error_tables/kdb5_err.c:44
msgid "Cannot find active master key"
msgstr "Der aktive Hauptschlüssel kann nicht gefunden werden."

#: ../lib/krb5/error_tables/kdb5_err.c:45
msgid "KVNO of new master key does not match expected value"
msgstr "KVNO des neuen Hauptschlüssels passt nicht zum erwarteten Wert"

#: ../lib/krb5/error_tables/kdb5_err.c:46
msgid "Stored master key is not current"
msgstr "gespeicherter Hauptschlüssel ist nicht aktuell"

#: ../lib/krb5/error_tables/kdb5_err.c:47
msgid "Insufficient access to lock database"
msgstr "keine ausreichenden Zugriffsrechte zum Sperren der Datenbank"

#: ../lib/krb5/error_tables/kdb5_err.c:48
msgid "Database format error"
msgstr "fehlerhaftes Datenbankformat"

#: ../lib/krb5/error_tables/kdb5_err.c:49
msgid "Unsupported version in database entry"
msgstr "nicht unterstützte Version im Datenbankeintrag"

#: ../lib/krb5/error_tables/kdb5_err.c:50
msgid "Unsupported salt type"
msgstr "nicht unterstützter Salt-Typ"

#: ../lib/krb5/error_tables/kdb5_err.c:51
msgid "Unsupported encryption type"
msgstr "nicht unterstützter Verschlüsselungstyp"

#: ../lib/krb5/error_tables/kdb5_err.c:52
msgid "Bad database creation flags"
msgstr "falsche Schalter zum Erstellen der Datenbank"

#: ../lib/krb5/error_tables/kdb5_err.c:53
msgid "No matching key in entry having a permitted enctype"
msgstr ""
"kein passender Schlüssel in einem Eintrag mit erlaubtem Verschlüsselungstyp"

#: ../lib/krb5/error_tables/kdb5_err.c:54
msgid "No matching key in entry"
msgstr "kein passender Schlüssel im Eintrag"

#: ../lib/krb5/error_tables/kdb5_err.c:55
msgid "Unable to find requested database type"
msgstr "angeforderter Datenbanktyp kann nicht gefunden werden"

#: ../lib/krb5/error_tables/kdb5_err.c:56
msgid "Database type not supported"
msgstr "Datenbanktyp nicht unterstützt"

#: ../lib/krb5/error_tables/kdb5_err.c:57
msgid "Database library failed to initialize"
msgstr "Initialisieren der Datenbankbibliothek fehlgeschlagen"

#: ../lib/krb5/error_tables/kdb5_err.c:59
msgid "Unable to access Kerberos database"
msgstr "auf die Kerberos-Datenbank kann nicht zugegriffen werden"

#: ../lib/krb5/error_tables/kdb5_err.c:60
msgid "Kerberos database internal error"
msgstr "interner Kerberos-Datenbankfehler"

#: ../lib/krb5/error_tables/kdb5_err.c:61
msgid "Kerberos database constraints violated"
msgstr "Kerberos-Datenbankbeschränkungen verletzt"

#: ../lib/krb5/error_tables/kdb5_err.c:62
msgid "Update log conversion error"
msgstr "Fehler beim Umwandeln des Aktualisierungsprotokolls"

#: ../lib/krb5/error_tables/kdb5_err.c:63
msgid "Update log is unstable"
msgstr "Aktualisierungsprotokoll ist instabil"

#: ../lib/krb5/error_tables/kdb5_err.c:64
msgid "Update log is corrupt"
msgstr "Aktualisierungsprotokoll ist beschädigt"

#: ../lib/krb5/error_tables/kdb5_err.c:65
msgid "Generic update log error"
msgstr "allgemeiner Aktualisierungsprotokollfehler"

#: ../lib/krb5/error_tables/kdb5_err.c:66
msgid "Database module does not match KDC version"
msgstr "Datenbankmodul passt nicht zur KDC-Version"

#: ../lib/krb5/error_tables/kdb5_err.c:68
msgid "Too much string mapping data"
msgstr "zu viele zeichenkettenabbildenden Daten"

#: ../lib/krb5/error_tables/asn1_err.c:23
msgid "ASN.1 failed call to system time library"
msgstr "ASN.1 beim Aufruf der Systemzeitbibliothek gescheitert"

#: ../lib/krb5/error_tables/asn1_err.c:24
msgid "ASN.1 structure is missing a required field"
msgstr "ein erforderliches Feld fehlt in der ASN.1-Struktur"

#: ../lib/krb5/error_tables/asn1_err.c:25
msgid "ASN.1 unexpected field number"
msgstr "ASN.1 unerwartete Feldnummer"

#: ../lib/krb5/error_tables/asn1_err.c:26
msgid "ASN.1 type numbers are inconsistent"
msgstr "ASN.1-Typnummern sind inkonsistent"

#: ../lib/krb5/error_tables/asn1_err.c:27
msgid "ASN.1 value too large"
msgstr "ASN.1-Wert zu groß"

#: ../lib/krb5/error_tables/asn1_err.c:28
msgid "ASN.1 encoding ended unexpectedly"
msgstr "ASN.1-Kodierung endete unerwartet"

#: ../lib/krb5/error_tables/asn1_err.c:29
msgid "ASN.1 identifier doesn't match expected value"
msgstr "ASN.1-Bezeichner passt nicht zum erwarteten Wert"

#: ../lib/krb5/error_tables/asn1_err.c:30
msgid "ASN.1 length doesn't match expected value"
msgstr "Länge von ASN.1 passt nicht zum erwarteten Wert"

#: ../lib/krb5/error_tables/asn1_err.c:31
msgid "ASN.1 badly-formatted encoding"
msgstr "fehlerhaft formatierte ASN.1-Kodierung"

#: ../lib/krb5/error_tables/asn1_err.c:32
msgid "ASN.1 parse error"
msgstr "ASN.1-Auswertungsfehler"

#: ../lib/krb5/error_tables/asn1_err.c:33
msgid "ASN.1 bad return from gmtime"
msgstr "ASN.1 falscher Rückgabewert von Gmtime"

#: ../lib/krb5/error_tables/asn1_err.c:34
msgid "ASN.1 non-constructed indefinite encoding"
msgstr "nicht konstruierte unbestimmte ASN.1-Kodierung"

#: ../lib/krb5/error_tables/asn1_err.c:35
msgid "ASN.1 missing expected EOC"
msgstr "ASN.1 fehlt erwartetes EOC"

#: ../lib/krb5/error_tables/asn1_err.c:36
msgid "ASN.1 object omitted in sequence"
msgstr "ASN.1-Objekt in Sequenz ausgelassen"

#: ../lib/krb5/error_tables/kv5m_err.c:23
msgid "Kerberos V5 magic number table"
msgstr "Tabelle magischer Zahlen von Kerberos V5"

#: ../lib/krb5/error_tables/kv5m_err.c:24
msgid "Bad magic number for krb5_principal structure"
msgstr "falsche magische Zahl für Krb5_principal-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:25
msgid "Bad magic number for krb5_data structure"
msgstr "falsche magische Zahl für Krb5_data-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:26
msgid "Bad magic number for krb5_keyblock structure"
msgstr "falsche magische Zahl für Krb5_krb5_keyblock-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:27
msgid "Bad magic number for krb5_checksum structure"
msgstr "falsche magische Zahl für Krb5_krb5_checksum-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:28
msgid "Bad magic number for krb5_encrypt_block structure"
msgstr "falsche magische Zahl für Krb5_encrypt_bloc-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:29
msgid "Bad magic number for krb5_enc_data structure"
msgstr "falsche magische Zahl für Krb5_enc_data-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:30
msgid "Bad magic number for krb5_cryptosystem_entry structure"
msgstr "falsche magische Zahl für Krb5_cryptosystem_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:31
msgid "Bad magic number for krb5_cs_table_entry structure"
msgstr "falsche magische Zahl für Krb5_cs_table_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:32
msgid "Bad magic number for krb5_checksum_entry structure"
msgstr "falsche magische Zahl für Krb5_checksum_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:33
msgid "Bad magic number for krb5_authdata structure"
msgstr "falsche magische Zahl für Krb5_authdata-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:34
msgid "Bad magic number for krb5_transited structure"
msgstr "falsche magische Zahl für Krb5_transited-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:35
msgid "Bad magic number for krb5_enc_tkt_part structure"
msgstr "falsche magische Zahl für Krb5_enc_tkt_part-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:36
msgid "Bad magic number for krb5_ticket structure"
msgstr "falsche magische Zahl für Krb5_ticket-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:37
msgid "Bad magic number for krb5_authenticator structure"
msgstr "falsche magische Zahl für Krb5_authenticator-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:38
msgid "Bad magic number for krb5_tkt_authent structure"
msgstr "falsche magische Zahl für Krb5_tkt_authent-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:39
msgid "Bad magic number for krb5_creds structure"
msgstr "falsche magische Zahl für Krb5_creds-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:40
msgid "Bad magic number for krb5_last_req_entry structure"
msgstr "falsche magische Zahl für Krb5_last_req_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:41
msgid "Bad magic number for krb5_pa_data structure"
msgstr "falsche magische Zahl für Krb5_pa_data-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:42
msgid "Bad magic number for krb5_kdc_req structure"
msgstr "falsche magische Zahl für Krb5_kdc_req-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:43
msgid "Bad magic number for krb5_enc_kdc_rep_part structure"
msgstr "falsche magische Zahl für Krb5_enc_kdc_rep_part-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:44
msgid "Bad magic number for krb5_kdc_rep structure"
msgstr "falsche magische Zahl für Krb5_kdc_rep-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:45
msgid "Bad magic number for krb5_error structure"
msgstr "falsche magische Zahl für Krb5_error-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:46
msgid "Bad magic number for krb5_ap_req structure"
msgstr "falsche magische Zahl für Krb5_ap_req-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:47
msgid "Bad magic number for krb5_ap_rep structure"
msgstr "falsche magische Zahl für Krb5_ap_rep-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:48
msgid "Bad magic number for krb5_ap_rep_enc_part structure"
msgstr "falsche magische Zahl für Krb5_ap_rep_enc_part-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:49
msgid "Bad magic number for krb5_response structure"
msgstr "falsche magische Zahl für Krb5_response-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:50
msgid "Bad magic number for krb5_safe structure"
msgstr "falsche magische Zahl für Krb5_safe-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:51
msgid "Bad magic number for krb5_priv structure"
msgstr "falsche magische Zahl für Krb5_priv-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:52
msgid "Bad magic number for krb5_priv_enc_part structure"
msgstr "falsche magische Zahl für Krb5_priv_enc_part-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:53
msgid "Bad magic number for krb5_cred structure"
msgstr "falsche magische Zahl für Krb5_cred-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:54
msgid "Bad magic number for krb5_cred_info structure"
msgstr "falsche magische Zahl für Krb5_cred_info-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:55
msgid "Bad magic number for krb5_cred_enc_part structure"
msgstr "falsche magische Zahl für Krb5_cred_enc_part-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:56
msgid "Bad magic number for krb5_pwd_data structure"
msgstr "falsche magische Zahl für Krb5_pwd_data-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:57
msgid "Bad magic number for krb5_address structure"
msgstr "falsche magische Zahl für Krb5_address-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:58
msgid "Bad magic number for krb5_keytab_entry structure"
msgstr "falsche magische Zahl für Krb5_keytab_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:59
msgid "Bad magic number for krb5_context structure"
msgstr "falsche magische Zahl für Krb5_context-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:60
msgid "Bad magic number for krb5_os_context structure"
msgstr "falsche magische Zahl für Krb5_os_context-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:61
msgid "Bad magic number for krb5_alt_method structure"
msgstr "falsche magische Zahl für Krb5_alt_method-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:62
msgid "Bad magic number for krb5_etype_info_entry structure"
msgstr "falsche magische Zahl für Krb5_etype_info_entry-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:63
msgid "Bad magic number for krb5_db_context structure"
msgstr "falsche magische Zahl für Krb5_db_context-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:64
msgid "Bad magic number for krb5_auth_context structure"
msgstr "falsche magische Zahl für Krb5_auth_context-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:65
msgid "Bad magic number for krb5_keytab structure"
msgstr "falsche magische Zahl für Krb5_keytab-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:66
msgid "Bad magic number for krb5_rcache structure"
msgstr "falsche magische Zahl für Krb5_rcache-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:67
msgid "Bad magic number for krb5_ccache structure"
msgstr "falsche magische Zahl für Krb5_ccache-Struktur"

#: ../lib/krb5/error_tables/kv5m_err.c:68
msgid "Bad magic number for krb5_preauth_ops"
msgstr "falsche magische Zahl für Krb5_preauth_ops"

#: ../lib/krb5/error_tables/kv5m_err.c:69
msgid "Bad magic number for krb5_sam_challenge"
msgstr "falsche magische Zahl für Krb5_sam_challenge"

#: ../lib/krb5/error_tables/kv5m_err.c:70
msgid "Bad magic number for krb5_sam_challenge_2"
msgstr "falsche magische Zahl für Krb5_sam_challenge_2"

#: ../lib/krb5/error_tables/kv5m_err.c:71
msgid "Bad magic number for krb5_sam_key"
msgstr "falsche magische Zahl für Krb5_sam_key"

#: ../lib/krb5/error_tables/kv5m_err.c:72
#: ../lib/krb5/error_tables/kv5m_err.c:73
msgid "Bad magic number for krb5_enc_sam_response_enc"
msgstr "falsche magische Zahl für Krb5_enc_sam_response_enc"

#: ../lib/krb5/error_tables/kv5m_err.c:74
msgid "Bad magic number for krb5_sam_response"
msgstr "falsche magische Zahl für Krb5_sam_response"

#: ../lib/krb5/error_tables/kv5m_err.c:75
msgid "Bad magic number for krb5_sam_response 2"
msgstr "falsche magische Zahl für Krb5_sam_response 2"

#: ../lib/krb5/error_tables/kv5m_err.c:76
msgid "Bad magic number for krb5_predicted_sam_response"
msgstr "falsche magische Zahl für Krb5_predicted_sam_response"

#: ../lib/krb5/error_tables/kv5m_err.c:77
msgid "Bad magic number for passwd_phrase_element"
msgstr "falsche magische Zahl für Passwd_phrase_element"

#: ../lib/krb5/error_tables/kv5m_err.c:78
msgid "Bad magic number for GSSAPI OID"
msgstr "falsche magische Zahl für GSSAPI OID"

#: ../lib/krb5/error_tables/kv5m_err.c:79
msgid "Bad magic number for GSSAPI QUEUE"
msgstr "falsche magische Zahl für GSSAPI QUEUE"

#: ../lib/krb5/error_tables/kv5m_err.c:80
msgid "Bad magic number for fast armored request"
msgstr "falsche magische Zahl für per FAST geschützte Anfrage"

#: ../lib/krb5/error_tables/kv5m_err.c:81
msgid "Bad magic number for FAST request"
msgstr "falsche magische Zahl für FAST-Anfrage"

#: ../lib/krb5/error_tables/kv5m_err.c:82
msgid "Bad magic number for FAST response"
msgstr "falsche magische Zahl für FAST-Antwort"

#: ../lib/krb5/error_tables/kv5m_err.c:83
msgid "Bad magic number for krb5_authdata_context"
msgstr "falsche magische Zahl für Krb5_authdata_context"

#: ../lib/krb5/error_tables/krb524_err.c:23
msgid "Cannot convert V5 keyblock"
msgstr "V5-Schlüsselblock kann nicht umgewandelt werden"

#: ../lib/krb5/error_tables/krb524_err.c:24
msgid "Cannot convert V5 address information"
msgstr "V5-Adressinformationen können nicht umgewandelt werden"

#: ../lib/krb5/error_tables/krb524_err.c:25
msgid "Cannot convert V5 principal"
msgstr "V5-Principal kann nicht umgewandelt werden"

#: ../lib/krb5/error_tables/krb524_err.c:26
msgid "V5 realm name longer than V4 maximum"
msgstr "V5-Realm-Name ist länger als die V4-Maximallänge"

#: ../lib/krb5/error_tables/krb524_err.c:27
msgid "Kerberos V4 error"
msgstr "Kerberos-V4-Fehler"

#: ../lib/krb5/error_tables/krb524_err.c:28
msgid "Encoding too large"
msgstr "Kodierung zu lang"

#: ../lib/krb5/error_tables/krb524_err.c:29
msgid "Decoding out of data"
msgstr "Dekodieren außerhalb der Daten"

#: ../lib/krb5/error_tables/krb524_err.c:30
msgid "Service not responding"
msgstr "Dienst antwortet nicht"

#: ../lib/krb5/error_tables/krb524_err.c:31
msgid "Kerberos version 4 support is disabled"
msgstr "Kerberos 4 Unterstützung ist deaktiviert"

#~ msgid "while creating server %s principal name"
#~ msgstr "beim Erstellen des Principal-Namens für Server %s"

# KDC = Key Distribution Center
#~ msgid "while getting credentials from kdc"
#~ msgstr "beim Holen der Anmeldedaten vom KDC"

# FIXME s/Retrieving/retrieving/
#~ msgid "while Retrieving credentials"
#~ msgstr "beim Abfragen der Anmeldedaten"

#~ msgid "while copying principal"
#~ msgstr "beim Kopieren des Principals"

#~ msgid "%s does not have correct permissions for %s\n"
#~ msgstr "%s hat nicht die erforderlichen Zugriffsrechte für %s\n"

#~ msgid "no salt\n"
#~ msgstr "kein Salt\n"

#~ msgid "%s: Couldn't grab lock\n"
#~ msgstr "%s: Es konnte keine Sperre erlangt werden.\n"

#~ msgid "%s: Loads disallowed when iprop is enabled and a ulog is present\n"
#~ msgstr ""
#~ "%s: Wenn Iprop aktiviert und Ulog vorhanden ist, ist Laden nicht "
#~ "möglich.\n"

#~ msgid "trying to lock database"
#~ msgstr "es wird versucht, die Datenbank zu sperren"

#~ msgid "GSS-API error %s: %s\n"
#~ msgstr "GSS-API-Fehler %s: %s\n"

#~ msgid "Couldn't create KRB5 Name NameType OID\n"
#~ msgstr "KRB5 Name NameType OID konnte nicht erstellt werden.\n"

#~ msgid "%s: %s while initializing, aborting"
#~ msgstr "%s: %s beim Initialisieren, wird abgebrochen"

#~ msgid ""
#~ "%s: Missing required configuration values (%lx) while initializing, "
#~ "aborting"
#~ msgstr ""
#~ "%s: Beim Initialisieren fehlen die erforderlichen Konfigurationswerte "
#~ "(%lx), wird abgebrochen"

#~ msgid ""
#~ "%s: Missing required configuration values (%lx) while initializing, "
#~ "aborting\n"
#~ msgstr ""
#~ "%s: Beim Initialisieren fehlen die erforderlichen Konfigurationswerte "
#~ "(%lx), wird abgebrochen\n"

#~ msgid "%s: could not initialize loop, aborting"
#~ msgstr "%s: Schleife konnte nicht initialisiert werden, wird abgebrochen"

#~ msgid "%s: could not initialize loop, aborting\n"
#~ msgstr "%s: Schleife konnte nicht initialisiert werden, wird abgebrochen\n"

#~ msgid "%s: %s while initializing signal handlers, aborting"
#~ msgstr ""
#~ "%s: %s beim Initialisieren des Signalbehandlungsprogramms, wird "
#~ "abgebrochen"

#~ msgid "%s: %s while initializing signal handlers, aborting\n"
#~ msgstr ""
#~ "%s: %s beim Initialisieren des Signalbehandlungsprogramms, wird "
#~ "abgebrochen\n"

#~ msgid "%s: %s while initializing network, aborting"
#~ msgstr "%s: %s beim Initialisieren des Netzwerks, wird abgebrochen"

#~ msgid "%s: %s while initializing network, aborting\n"
#~ msgstr "%s: %s beim Initialisieren des Netzwerks, wird abgebrochen\n"

#~ msgid "Cannot build GSS-API authentication names, failing."
#~ msgstr ""
#~ "GSS-API-Authentifizierungsnamen können nicht gebildet werden, "
#~ "fehlgeschlagen"

#~ msgid "Can't set kdb keytab's internal context."
#~ msgstr ""
#~ "Der interne Kontext von KDBs Schlüsseltabelle kann nicht gesetzt werden."

#~ msgid "Can't register kdb keytab."
#~ msgstr "Die KDB-Schlüsseltabelle kann nicht registriert werden."

#~ msgid "Can't register acceptor keytab."
#~ msgstr "Die Empfängerschlüsseltabelle kann nicht registriert werden."

#~ msgid ""
#~ "Cannot set GSS-API authentication names (keytab not present?), failing."
#~ msgstr ""
#~ "GSS-API-Authentifizierungsnamen können nicht gesetzt werden "
#~ "(Schlüsseltabelle nicht vorhanden?), fehlgeschlagen"

#~ msgid "Cannot initialize acl file: %s"
#~ msgstr "ACL-Datei kann nicht initialisiert werden: %s"

#~ msgid "%s: Cannot initialize acl file: %s\n"
#~ msgstr "%s: ACL-Datei kann nicht initialisiert werden: %s\n"

#~ msgid "Cannot detach from tty: %s"
#~ msgstr "kann nicht vom Terminal gelöst werden: %s"

#~ msgid "Cannot create PID file %s: %s"
#~ msgstr "PID-Datei %s kann nicht erstellt werden: %s"

#~ msgid "%s: %s while mapping update log (`%s.ulog')\n"
#~ msgstr "%s: %s beim Abbilden des Aktualisierungsprotokolls (»%s.ulog«)\n"

#~ msgid "%s while mapping update log (`%s.ulog')"
#~ msgstr "%s beim Abbilden des Aktualisierungsprotokolls (»%s.ulog«)"

#~ msgid "%s: Cannot create IProp RPC service (PROG=%d, VERS=%d)\n"
#~ msgstr ""
#~ "%s: IProp-RPC-Dienst kann nicht erstellt werden (PROG=%d, VERS=%d)\n"

#~ msgid "Cannot create IProp RPC service (PROG=%d, VERS=%d), failing."
#~ msgstr ""
#~ "IProp-RPC-Dienst kann nicht erstellt werden (PROG=%d, VERS=%d), "
#~ "fehlgeschlagen"

#~ msgid "%s while getting IProp svc name, failing"
#~ msgstr "%s beim Holen des IProp-Dienstnamens, fehlgeschlagen"

#~ msgid "%s: %s while getting IProp svc name, failing\n"
#~ msgstr "%s: %s beim Holen des IProp-Dienstnamens, fehlgeschlagen\n"

#~ msgid "Unable to set RPCSEC_GSS service name (`%s'), failing."
#~ msgstr ""
#~ "der RPCSEC_GSS-Dienstname (»%s«) kann nicht gesetzt werden, fehlgeschlagen"

#~ msgid "%s: Unable to set RPCSEC_GSS service name (`%s'), failing.\n"
#~ msgstr ""
#~ "%s: der RPCSEC_GSS-Dienstname (»%s«) kann nicht gesetzt werden, "
#~ "fehlgeschlagen\n"

#~ msgid "GSS-API authentication error %.*s: recursive failure!"
#~ msgstr "GSS-API-Authentifizierungsfehler %.*s: rekursiver Fehlschlag!"

#~ msgid "skipping unrecognized local address family %d"
#~ msgstr "nicht erkannte lokale Adressfamilie %d wird übersprungen"

#~ msgid "got routing msg type %d(%s) v%d"
#~ msgstr "Routing-Meldungstyp %d(%s) v%d erhalten"

#~ msgid "Could not create temp stash file: %s"
#~ msgstr "Temporäre Ablagedatei konnte nicht erstellt werden: %s"

#~ msgid "ulog_sync_header: could not sync to disk"
#~ msgstr "ulog_sync_header: kann nicht auf Platte sychronisiert werden"

#~ msgid "%s: attempt to convert non-extended krb5_get_init_creds_opt"
#~ msgstr ""
#~ "%s: Es wird versucht, nicht erweiterte »krb5_get_init_creds_opt« "
#~ "umzuwandeln"

#~ msgid "krb5_sname_to_principal, while adding entries to the database"
#~ msgstr ""
#~ "»krb5_sname_to_principal« beim Hinzufügen von Einträgen zur Datenbank"

#~ msgid "krb5_copy_principal, while adding entries to the database"
#~ msgstr "»krb5_copy_principal« beim Hinzufügen von Einträgen zur Datenbank"

#~ msgid ""
#~ "Unable to check if SASL EXTERNAL mechanism is supported by LDAP server. "
#~ "Proceeding anyway ..."
#~ msgstr ""
#~ "Es konnte nicht geprüft werden, ob der Mechanismus SASL EXTERNAL vom LDAP-"
#~ "Server unterstützt wird. Es wird trotzdem fortgesetzt …"

#~ msgid ""
#~ "SASL EXTERNAL mechanism not supported by LDAP server. Can't perform "
#~ "certificate-based bind."
#~ msgstr ""
#~ "Der Mechanismus SASL EXTERNAL wird nicht vom LDAP-Server unterstützt. Es "
#~ "kann keine zertifikatbasierte Verbindung hergestellt werden."

#~ msgid "Error reading 'ldap_servers' attribute"
#~ msgstr "Fehler beim Lesen des Attributs »ldap_servers«"

#~ msgid "Stash file entry corrupt"
#~ msgstr "Eintrag in der Ablagedatei beschädigt"

#~ msgid "while setting server principal realm"
#~ msgstr "beim Setzen des Server-Principal-Realms"

#~ msgid "while getting initial ticket\n"
#~ msgstr "beim Holen eines Anfangs-Tickets\n"

#~ msgid "while destroying ticket cache"
#~ msgstr "beim Zerstören des Ticket-Zwischenspeichers"

#~ msgid "while closing default ccache"
#~ msgstr "beim Schließen des Standard-Ccaches"
