digraph cipher {
    bgcolor="transparent";

    begin [label=start, color="#deeaee", style="filled"];
    newed [fontcolor="#c94c4c", style="solid"];

    initialised [fontcolor="#c94c4c"];
    updated [fontcolor="#c94c4c"];
    finaled [fontcolor="#c94c4c"];
    end [label="freed", color="#deeaee", style="filled"];

    d_initialised [label="initialised\n(decryption)", fontcolor="#c94c4c"];
    d_updated [label="updated\n(decryption)", fontcolor="#c94c4c"];
    e_initialised [label="initialised\n(encryption)", fontcolor="#c94c4c"];
    e_updated [label="updated\n(encryption)", fontcolor="#c94c4c"];

    begin -> newed [label="EVP_CIPHER_CTX_new"];
    newed -> initialised [label="EVP_CipherInit"];
    initialised -> initialised [label="EVP_CipherInit\n(not required but allowed)",
                                style=dashed];
    initialised -> updated [label="EVP_CipherUpdate", weight=2];
    updated -> updated [label="EVP_CipherUpdate"];
    updated -> finaled [label="EVP_CipherFinal"];
    finaled -> finaled [label="EVP_CIPHER_CTX_get_params\n(AEAD encryption)",
                        style=dashed];
    finaled -> end [label="EVP_CIPHER_CTX_free"];
    newed -> d_initialised [label="EVP_DecryptInit"];
    d_initialised -> d_initialised [label="EVP_DecryptInit\n(not required but allowed)",
                                style=dashed];
    d_initialised -> d_updated [label="EVP_DecryptUpdate", weight=2];
    d_updated -> d_updated [label="EVP_DecryptUpdate"];
    d_updated -> finaled [label="EVP_DecryptFinal"];
    newed -> e_initialised [label="EVP_EncryptInit"];
    e_initialised -> e_initialised [label="EVP_EncryptInit\n(not required but allowed)",
                                style=dashed];
    e_initialised -> e_updated [label="EVP_EncryptUpdate", weight=2];
    e_updated -> e_updated [label="EVP_EncryptUpdate"];
    e_updated -> finaled [label="EVP_EncryptFinal"];
    most -> newed [label="EVP_CIPHER_CTX_reset", style=dashed,
                      color="#034f84", fontcolor="#034f84"];
    most [label="any of the initialised\nupdated or finaled states", style=dashed,
                      color="#034f84", fontcolor="#034f84"];
}

/* This is a version with a single flavour which is easier to comprehend
digraph cipher {
    bgcolor="transparent";

    begin [label=start, color="#deeaee", style="filled"];
    newed [fontcolor="#c94c4c", style="solid"];
    initialised [fontcolor="#c94c4c"];
    updated [fontcolor="#c94c4c"];
    finaled [fontcolor="#c94c4c"];
    end [label="freed", color="#deeaee", style="filled"];

    begin -> newed [label="EVP_CIPHER_CTX_new"];
    newed -> initialised [label="EVP_CipherInit"];
    initialised -> initialised [label="EVP_CipherInit\n(not required but allowed)",
                                style=dashed];
    initialised -> updated [label="EVP_CipherUpdate", weight=2];
    updated -> updated [label="EVP_CipherUpdate"];
    updated -> finaled [label="EVP_CipherFinal"];
    finaled -> finaled [label="EVP_CIPHER_CTX_get_params\n(AEAD encryption)",
                        style=dashed];
    finaled -> end [label="EVP_CIPHER_CTX_free"];
    finaled -> newed [label="EVP_CIPHER_CTX_reset", style=dashed,
                      color="#034f84", fontcolor="#034f84"];
    updated -> newed [label="EVP_CIPHER_CTX_reset", style=dashed,
                      color="#034f84", fontcolor="#034f84"];
}
*/

