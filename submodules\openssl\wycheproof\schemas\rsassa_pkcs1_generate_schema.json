{"type": "object", "definitions": {"RsassaPkcs1GenTestGroup": {"type": "object", "properties": {"type": {"enum": ["RsassaPkcs1Generate"]}, "d": {"type": "string", "format": "BigInt", "description": "The private exponent"}, "e": {"type": "string", "format": "BigInt", "description": "The public exponent"}, "keyAsn": {"type": "string", "format": "Der", "description": "DER encoding of the sequence [n, e]"}, "keyDer": {"type": "string", "format": "Der", "description": "DER encoding of the public key"}, "keyJwk": {"type": "object", "description": "[Optional] Private key in JWK format"}, "keyPem": {"type": "string", "format": "Pem", "description": "Pem encoded public key"}, "keySize": {"type": "integer", "description": "the size of the modulus in bits"}, "n": {"type": "string", "format": "BigInt", "description": "The modulus of the key"}, "privateKeyDer": {"type": "string", "format": "Der", "description": "DER encoding of the private key"}, "privateKeyJwk": {"type": "object", "description": "[Optional] Private key in JWK format"}, "privateKeyPem": {"type": "string", "format": "Pem", "description": "Pem encoded private key"}, "sha": {"type": "string", "description": "the hash function used for the message"}, "tests": {"type": "array", "items": {"$ref": "#/definitions/SignatureTestVector"}}}}, "SignatureTestVector": {"type": "object", "properties": {"tcId": {"type": "integer", "description": "Identifier of the test case"}, "comment": {"type": "string", "description": "A brief description of the test case"}, "msg": {"type": "string", "format": "HexBytes", "description": "The message to sign"}, "sig": {"type": "string", "format": "HexBytes", "description": "A signature for msg"}, "result": {"type": "string", "description": "Test result", "enum": ["valid", "invalid", "acceptable"]}, "flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags"}}}}, "properties": {"algorithm": {"type": "string", "description": "the primitive tested in the test file"}, "generatorVersion": {"type": "string", "description": "the version of the test vectors."}, "header": {"type": "array", "items": {"type": "string"}, "description": "additional documentation"}, "notes": {"type": "object", "description": "a description of the labels used in the test vectors"}, "numberOfTests": {"type": "integer", "description": "the number of test vectors in this test"}, "schema": {"enum": ["rsassa_pkcs1_generate_schema.json"]}, "testGroups": {"type": "array", "items": {"$ref": "#/definitions/RsassaPkcs1GenTestGroup"}}}}