# CAST5 CTR vectors built for https://github.com/pyca/cryptography
# Derived from the RFC 3686 test vectors for AES CTR
# Verified against the Go crypto packages
# Key Length : 128

COUNT = 0
KEY = AE6852F8121067CC4BF7A5765577F39E
IV = 0000003000000000
PLAINTEXT = 53696E676C6520626C6F636B206D7367
CIPHERTEXT = 1f5483baa07a68abccfed68179e84ac0

COUNT = 1
KEY = 7E24067817FAE0D743D6CE1F32539163
IV = 006CB6DBC0543B59
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
CIPHERTEXT = 07c52bc5f858c5b1a21ee4e27f7b1ac90c17087d1e6ae0fd81c72e248cce329b

COUNT = 2
KEY = 7691BE035E5020A8AC6E618529F9A0DC
IV = 00E0017B27777F3F
PLAINTEXT = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
CIPHERTEXT = 612aec9151e753cc1f8b086ece5343737697cda52122e1d9d2294cb43e7547bb81b50939
