=pod

=head1 NAME

openssl-gendsa,
gendsa - generate a DSA private key from a set of parameters

=head1 SYNOPSIS

B<openssl> B<gendsa>
[B<-help>]
[B<-out filename>]
[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-rand file...>]
[B<-writerand file>]
[B<-engine id>]
[B<paramfile>]

=head1 DESCRIPTION

The B<gendsa> command generates a DSA private key from a DSA parameter file
(which will be typically generated by the B<openssl dsaparam> command).

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out filename>

Output the key to the specified file. If this argument is not specified then
standard output is used.

=item B<-aes128>, B<-aes192>, B<-aes256>, B<-aria128>, B<-aria192>, B<-aria256>, B<-camellia128>, B<-camellia192>, B<-camellia256>, B<-des>, B<-des3>, B<-idea>

These options encrypt the private key with specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified no encryption is used.

=item B<-rand file...>

A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is B<;> for MS-Windows, B<,> for OpenVMS, and B<:> for
all others.

=item [B<-writerand file>]

Writes random data to the specified I<file> upon exit.
This can be used with a subsequent B<-rand> flag.

=item B<-engine id>

Specifying an engine (by its unique B<id> string) will cause B<gendsa>
to attempt to obtain a functional reference to the specified engine,
thus initialising it if needed. The engine will then be set as the default
for all available algorithms.

=item B<paramfile>

This option specifies the DSA parameter file to use. The parameters in this
file determine the size of the private key. DSA parameters can be generated
and examined using the B<openssl dsaparam> command.

=back

=head1 NOTES

DSA key generation is little more than random number generation so it is
much quicker that RSA key generation for example.

=head1 SEE ALSO

L<dsaparam(1)>, L<dsa(1)>, L<genrsa(1)>,
L<rsa(1)>

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
