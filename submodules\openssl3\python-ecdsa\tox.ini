
[tox]
envlist = py26, py27, py33, py34, py35, py36, py37, py38, py39, py, pypy, pypy3, gmpy2py27, gmpy2py39, gmpypy27, gmpypy39, codechecks

[testenv]
deps =
     py{33}: py<1.5
     py{33}: pytest<3.3
     py{33}: enum34
     py{33}: hypothesis<3.44
     py{26}: unittest2
     py{26}: hypothesis<3
     py{34}: attrs<21
     py{26,27,34,35,36,37,38,39,py,py3}: pytest
     py{27,34,35,36,37,38,39,py,py3}: hypothesis
     gmpy2py{27,39}: gmpy2
     gmpypy{27,39}: gmpy
     gmpy{2py27,2py39,py27,py39}: pytest
     gmpy{2py27,2py39,py27,py39}: hypothesis
# six==1.9.0 comes from setup.py install_requires
     py27_old_six: six==1.9.0
     py27_old_six: pytest
     py27_old_six: hypothesis
# those are the oldest versions of gmpy and gmpy2 on PyPI (i.e. oldest we can
# actually test), older versions may work, but are not easy to test
     py27_old_gmpy: gmpy==1.15
     py27_old_gmpy: pytest
     py27_old_gmpy: hypothesis
     py27_old_gmpy2: gmpy2==2.0.1
     py27_old_gmpy2: pytest
     py27_old_gmpy2: hypothesis
     py: pytest
     py: hypothesis
     py{33}: wheel<0.30
     coverage
commands = coverage run --branch -m pytest {posargs:src/ecdsa}

[testenv:py27_old_gmpy]
basepython = python2.7

[testenv:py27_old_gmpy2]
basepython = python2.7

[testenv:py27_old_six]
basepython = python2.7

[testenv:gmpypy27]
basepython=python2.7

[testenv:gmpypy39]
basepython=python3.9

[testenv:gmpy2py27]
basepython=python2.7

[testenv:gmpy2py39]
basepython=python3.9

[testenv:instrumental]
basepython = python2.7
deps =
     gmpy2
     instrumental
     hypothesis
     pytest>=4.6.0
     coverage
     six
commands =
         instrumental -t ecdsa -i '.*test_.*|.*_version|.*_compat|.*_sha3' {envbindir}/pytest {posargs:src/ecdsa}
         instrumental -f .instrumental.cov -sr

[testenv:coverage]
sitepackages=True
whitelist_externals=coverage
commands =
         coverage run --branch -m pytest --hypothesis-show-statistics {posargs:src/ecdsa}
         coverage html
         coverage report -m

[testenv:speed]
commands = {envpython} speed.py

[testenv:speedgmpy]
deps = gmpy
commands = {envpython} speed.py

[testenv:speedgmpy2]
deps = gmpy2
commands = {envpython} speed.py

[testenv:codechecks]
basepython = python3
deps =
     black==19.10b0
     flake8
commands =
         flake8 setup.py speed.py src
         black --check --line-length 79 .

[testenv:codeformat]
basepython = python3
deps =
     black==19.10b0
commands =
         black --line-length 79 .

[flake8]
exclude = src/ecdsa/test*.py
# We're just getting started. For now, ignore the following problems:
# E203: whitespace before ':' (this needs to be ignored for black compatibility)
# E741: ambiguous variable name
extend-ignore = E203,E741
