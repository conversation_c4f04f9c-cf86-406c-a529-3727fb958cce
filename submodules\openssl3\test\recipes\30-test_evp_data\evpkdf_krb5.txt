#
# Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = KRB5KDF tests (from RFC 3961 test vectors and krb5 sources)

#RFC3961
KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:dce06b1f64c857a11c3db57c51899b2cc1791008ce973b92
Ctrl.hexconstant = hexconstant:**********
Output = 925179d04591a79b5d3192c4a7e9c289b049c71f6ee604cd

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:5e13d31c70ef765746578531cb51c15bf11ca82c97cee9f2
Ctrl.hexconstant = hexconstant:00000001aa
Output = 9e58e5a146d9942a101c469845d67a20e3c4259ed913f207

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:98e6fd8a04a4b6859b75a176540b9752bad3ecd610a252bc
Ctrl.hexconstant = hexconstant:**********
Output = 13fef80d763e94ec6d13fd2ca1d085070249dad39808eabf

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:622aec25a2fe2cad7094680b7c64940280084c1a7cec92b5
Ctrl.hexconstant = hexconstant:00000001aa
Output = f8dfbf04b097e6d9dc0702686bcb3489d91fd9a4516b703e

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:d3f8298ccb166438dcb9b93ee5a7629286a491f838f802fb
Ctrl.hexconstant = hexconstant:6b65726265726f73
Output = 2370da575d2a3da864cebfdc5204d56df779a7df43d9da43

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:c1081649ada74362e6a1459d01dfd30d67c2234c940704da
Ctrl.hexconstant = hexconstant:**********
Output = 348057ec98fdc48016161c2a4c7a943e92ae492c989175f7

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:5d154af238f46713155719d55e2f1f790dd661f279a7917c
Ctrl.hexconstant = hexconstant:00000001aa
Output = a8808ac267dada3dcbe9a7c84626fbc761c294b01315e5c1

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:798562e049852f57dc8c343ba17f2ca1d97394efc8adc443
Ctrl.hexconstant = hexconstant:**********
Output = c813f88a3be3b334f75425ce9175fbe3c8493b89c8703b49

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:26dce334b545292f2feab9a8701a89a4b99eb9942cecd016
Ctrl.hexconstant = hexconstant:00000001aa
Output = f48ffd6e83f83e7354e694fd252cf83bfe58f7d5ba37ec5d

#Krb5 sources
KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:850BB51358548CD05E86768C313E3BFEF7511937DCF72C3E
Ctrl.hexconstant = hexconstant:0000000299
Output = F78C496D16E6C2DAE0E0B6C24057A84C0426AEEF26FD6DCE

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:850BB51358548CD05E86768C313E3BFEF7511937DCF72C3E
Ctrl.hexconstant = hexconstant:00000002AA
Output = 5B5723D0B634CB684C3EBA5264E9A70D52E683231AD3C4CE

KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:850BB51358548CD05E86768C313E3BFEF7511937DCF72C3E
Ctrl.hexconstant = hexconstant:0000000255
Output = A77C94980E9B7345A81525C423A737CE67F4CD91B6B3DA45

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-128-CBC
Ctrl.hexkey = hexkey:42263C6E89F4FC28B8DF68EE09799F15
Ctrl.hexconstant = hexconstant:0000000299
Output = 34280A382BC92769B2DA2F9EF066854B

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-128-CBC
Ctrl.hexkey = hexkey:42263C6E89F4FC28B8DF68EE09799F15
Ctrl.hexconstant = hexconstant:00000002AA
Output = 5B14FC4E250E14DDF9DCCF1AF6674F53

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-128-CBC
Ctrl.hexkey = hexkey:42263C6E89F4FC28B8DF68EE09799F15
Ctrl.hexconstant = hexconstant:0000000255
Output = 4ED31063621684F09AE8D89991AF3E8F

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-256-CBC
Ctrl.hexkey = hexkey:FE697B52BC0D3CE14432BA036A92E65BBB52280990A2FA27883998D72AF30161
Ctrl.hexconstant = hexconstant:0000000299
Output = BFAB388BDCB238E9F9C98D6A878304F04D30C82556375AC507A7A852790F4674

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-256-CBC
Ctrl.hexkey = hexkey:FE697B52BC0D3CE14432BA036A92E65BBB52280990A2FA27883998D72AF30161
Ctrl.hexconstant = hexconstant:00000002AA
Output = C7CFD9CD75FE793A586A542D87E0D1396F1134A104BB1A9190B8C90ADA3DDF37

KDF = KRB5KDF
Ctrl.cipher = cipher:AES-256-CBC
Ctrl.hexkey = hexkey:FE697B52BC0D3CE14432BA036A92E65BBB52280990A2FA27883998D72AF30161
Ctrl.hexconstant = hexconstant:0000000255
Output = 97151B4C76945063E2EB0529DC067D97D7BBA90776D8126D91F34F3101AEA8BA

#Same as the first but with no "fixup"
KDF = KRB5KDF
Ctrl.cipher = cipher:DES-EDE3-CBC
Ctrl.hexkey = hexkey:dce06b1f64c857a11c3db57c51899b2cc1791008ce973b92
Ctrl.hexconstant = hexconstant:**********
Output = 935079d14490a75c3093c4a6e8c3b049c71e6ee705
