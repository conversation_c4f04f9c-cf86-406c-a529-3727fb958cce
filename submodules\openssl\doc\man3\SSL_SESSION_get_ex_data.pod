=pod

=head1 NAME

SSL_SESSION_set_ex_data,
SSL_SESSION_get_ex_data
- get and set application specific data on a session

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_SESSION_set_ex_data(SSL_SESSION *ss, int idx, void *data);
 void *SSL_SESSION_get_ex_data(const SSL_SESSION *s, int idx);

=head1 DESCRIPTION

SSL_SESSION_set_ex_data() enables an application to store arbitrary application
specific data B<data> in an SSL_SESSION structure B<ss>. The index B<idx> should
be a value previously returned from a call to L<CRYPTO_get_ex_new_index(3)>.

SSL_SESSION_get_ex_data() retrieves application specific data previously stored
in an SSL_SESSION structure B<s>. The B<idx> value should be the same as that
used when originally storing the data.

=head1 RETURN VALUES

SSL_SESSION_set_ex_data() returns 1 for success or 0 for failure.

SSL_SESSION_get_ex_data() returns the previously stored value or NULL on
failure. NULL may also be a valid value.

=head1 SEE ALSO

L<ssl(7)>,
L<CRYPTO_get_ex_new_index(3)>

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
