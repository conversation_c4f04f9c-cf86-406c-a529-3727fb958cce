


/*----------------------------------------------------------
// Decoder Ring for TestIgnoreConnectionTimeout
// [test] Ignoring timeout unexpected status because of random loss
// QuicTraceLogInfo(
                    TestIgnoreConnectionTimeout,
                    "[test] Ignoring timeout unexpected status because of random loss");
----------------------------------------------------------*/
TRACEPOINT_EVENT(CLOG_TESTCONNECTION_CPP, TestIgnoreConnectionTimeout,
    TP_ARGS(
), 
    TP_FIELDS(
    )
)
