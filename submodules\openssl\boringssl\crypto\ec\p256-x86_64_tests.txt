# Negation tests.
#
# The following tests satisfy A = -B (mod P).

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000000
B = 0000000000000000000000000000000000000000000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000001
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffffe

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000003
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffffc

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000007
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffff8

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000000f
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffff0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000001f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffffe0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000003f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffffc0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000007f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffff80

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000000ff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffff00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000001ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffe00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000003ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffc00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000007ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffff800

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000fff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffff000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000001fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffe000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000003fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffc000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000007fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffff8000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000ffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffff0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000001ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffe0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000003ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffc0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000007ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffff80000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000fffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffff00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000001fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffe00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000003fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffc00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000007fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffff800000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000ffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffff000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000001ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffe000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000003ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffc000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000007ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffff8000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000fffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffff0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000001fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffe0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000003fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffc0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000007fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffff80000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000ffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffff00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000001ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffe00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000003ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffc00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000007ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffff800000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000fffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffff000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000001fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffe000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000003fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffc000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000007fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffff8000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000ffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffff0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000001ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffe0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000003ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffc0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000007ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffff80000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000fffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffff00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000001fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffe00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000003fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffc00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000007fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffff800000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000ffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffff000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000001ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffe000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000003ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffc000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000007ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffff8000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000fffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffff0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000001fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffe0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000003fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffc0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000007fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffff80000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000ffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffff00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000001ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffe00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000003ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffc00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000007ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffff800000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000fffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffff000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000001fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffe000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000003fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffc000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000007fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffff8000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000ffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffff0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000001ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffe0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000003ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffc0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000007ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffff80000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000fffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffff00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000001fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffe00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000003fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffc00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000007fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffff800000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000ffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffff000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000001ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffe000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000003ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffc000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000007ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffff8000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000fffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffff0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000001fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffe0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000003fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffc0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000007fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffff80000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffff00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000001ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffe00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000003ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffc00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000007ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fff800000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fff000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000001fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffe000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000003fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffc000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000007fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ff8000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ff0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000001ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fe0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000003ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fc0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000007ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000f80000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000f00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000001fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000e00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000003fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000c00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000007fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000800000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000ffffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000001ffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffff000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000003ffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffffd000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000007ffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffff9000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000fffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffff1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000001fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffe1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000003fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffc1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000007fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffff81000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000ffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffff01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000001ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffe01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000003ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffc01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000007ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffff801000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000fffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffff001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000001fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffe001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000003fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffc001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000007fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffff8001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000ffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffff0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000001ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffe0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000003ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffc0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000007ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffff80001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000fffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffff00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000001fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffe00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000003fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffc00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000007fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffff800001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000ffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffff000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000001ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffe000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000003ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffc000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000007ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffff8000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000fffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffff0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000001fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffe0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000003fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffc0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000007fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffff80000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000ffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffff00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000001ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffe00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000003ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffc00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000007ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffff800000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000fffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffff000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000001fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffe000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000003fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffc000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000007fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffff8000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000ffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffff0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000001ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffe0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000003ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffc0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000007ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffff80000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000fffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffff00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000001fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffe00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000003fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffc00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000007fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffff800000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffff000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000001ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffe000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000003ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffc000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000007ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffff8000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffff0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000001fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffe0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000003fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffc0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000007fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffff80000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffff00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000001ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffe00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000003ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffc00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000007ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffff800000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffff000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000001fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffe000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000003fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffc000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000007fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffff8000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffff0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000001ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffe0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000003ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffc0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000007ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffff80000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffff00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000001fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffe00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000003fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffc00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000007fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffff800000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffff000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000001ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffe000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000003ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffc000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000007ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffff8000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffff0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000001fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffe0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000003fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffc0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000007fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffff80000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffff00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000001ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffe00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000003ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffc00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000007ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fff800000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fff000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000001fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffe000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000003fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffc000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000007fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ff8000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ff0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000001ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fe0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000003ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fc0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000007ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000f80000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000f00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000001fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000e00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000003fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000c00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000007fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000800000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000001ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffff000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000003ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffffd000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000007ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffff9000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffff1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000001fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffe1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000003fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffc1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000007fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffff81000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffff01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000001ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffe01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000003ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffc01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000007ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffff801000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffff001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000001fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffe001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000003fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffc001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000007fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffff8001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffff0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000001ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffe0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000003ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffc0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000007ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefff80001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefff00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000001fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffe00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000003fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffc00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000007fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeff800001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeff000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000001ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefe000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000003ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefc000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000007ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffef8000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffef0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffee0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffec0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000007fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffe80000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffe00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000001ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffd00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000003ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffb00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000007ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffff700000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffef00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffdf00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000003fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffbf00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000007fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffff7f00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffeff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000001ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffdff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000003ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffbff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000007ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffff7ff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffefff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffdfff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00003fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffbfff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffff7fff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffeffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0001ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffdffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0003ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffbffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0007ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fff7ffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffefffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffdfffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 003fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffbfffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ff7fffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = feffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fdffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 03ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fbffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 07ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = f7ffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = efffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = dfffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 3fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = bfffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = 7fffffff00000001000000000000000000000001000000000000000000000000


# Montgomery multiplication tests.
#
# The following tests satisfy A * B * 2^-256 = Result (mod P).

Test = MulMont
A = e762f095431b732ce33c4f4a6f41068ff7f78e37aad940166667d193bfc58039
B = a43df383dd5df14d7c16737b781261473f9ffb76ee29562fbb5e5d390b882fb5
Result = cf637a47dc5fb82aed80ed4c66b682a94bf0b76a2878acf483aad86c0db7cc19

Test = MulMont
A = 2e519e860cb3f8f32fc351861b022e9fc7bb073ca8767efb3d1027dd32a38bcb
B = 466d035e4238d6a30613dd227b0daeacd6a8634fa60f5150d42dd20601794be4
Result = 486e1abe0f79e107f8beca6e4653872f63a24dedb005def6aae75a2a51e73c76

Test = MulMont
A = 1763859541b5e2edee019c66699d0e12e349e6ee586d618ac20c679d2fa8cadd
B = 56125872de888c5656dec17fbf9678d915ff9815da897df81f03fd9aa4f93654
Result = 71ce584135a0aba8222ca0021bcefac5811d19100627f96726cf195ff2ac4aad

Test = MulMont
A = ea3be6295098e4995b93267dbd58f24fd9c18f7e89e9e5aeafdc34ca54e8ec4e
B = 2735658978d620a4f76bffe94d9cd7d683b3bfd533aa6bb2b94f52122e83f7fc
Result = 362f7ab3a12effe9bad069b84a7df5f108503c2457f83ddb05b57f19e6457989

Test = MulMont
A = f607f087ec5015b533df8802771dc60ef1487d86ce405e5bb18f8f06ca483f13
B = 73ac532eb3f2356a96e668f167a1626a0f7b1fd2cd84ba6deeebd01af1d3897d
Result = ce7045e69da157e62fb42508880f5734531c36948c704aedec42afa75cb9c2eb

Test = MulMont
A = 80ce8eb07601fd8e19ba08a9d21081b0324fd459f9c489ac7c871d406133c813
B = 7ad28cef45b137ecc5426a44b6bce6d4329f5bd2b5e55d46edd5fbb295678a1b
Result = 04068f8461d17b34c8d9c3eecf61dbaef9cd5a952bbcd9f84bb2044f2439da60

Test = MulMont
A = 17429caf63689e143c8ca77df69a11cbc02c272daadd75a66f3fa5f88828367e
B = 5725bedc56a4b16e0f0ae55fa0beb1fdf3ff132ccb9803bab678d4ac7915d88c
Result = a1da0fa68947e906287ea164b213bc7e80649b2ac3f97f203549d3b270de05a1

Test = MulMont
A = e7da43c0e0fa7adeb972901bef3160c848e9651bfc810968afdb0cd598830370
B = 08f03060cac1d3c15eea69623d5fb01da465b209e3e5e90fbb51053a1c5700eb
Result = cda4ffaf8b1c3ac0d44bae6ea5154de11e14931747a65396531302c0cb1ed537

Test = MulMont
A = c7375c2b6666713cb33cfb741268fd3ccf703bcaa0b9b27f84a8cb970655da9c
B = b0796ee4bb88b9bad895d9c25c34f43a3941e9585bda8e86ff4fa0bbb391ac61
Result = fd1d557a9fb0031e462121bf7ca31804acfcfce822bb6ee6631b54c575380617

Test = MulMont
A = 72a87b13eb4a2e248214aa591c586df65790f9f750a1641b47581a4ee09be7e9
B = 38e602844b9aaf737e8b1261110b86ba22806ccbbbfdc5305075429d7ce4f002
Result = cb2d63ee829de8801759f0229d4c07139bacd804f0c815d35004747c65bffdf2

# Test cases where A == B to test squaring.

Test = MulMont
A = 0000000000000000000000000000000000000000000000000000000000000000
B = 0000000000000000000000000000000000000000000000000000000000000000
Result = 0000000000000000000000000000000000000000000000000000000000000000

Test = MulMont
A = 579e9ce1ad00639b8b64d49546ff4f9c30ad12eaebe9e2ed91e97d55c3c5d847
B = 579e9ce1ad00639b8b64d49546ff4f9c30ad12eaebe9e2ed91e97d55c3c5d847
Result = 10c5e60c2d480d5d53f50c24fb771fd2dec208db04624dfd05d2847ca173a9aa

Test = MulMont
A = 501947209b121bcdedce8c895ee2ba310f2e561e97998eb8f3b99d1f924f36c1
B = 501947209b121bcdedce8c895ee2ba310f2e561e97998eb8f3b99d1f924f36c1
Result = 54d6d64566619b215910f1b9e467b22ef205ca3aaad37a00fcbd906357f9c179

Test = MulMont
A = e84ab9202722498baa2c9158f40d47b1f03df4d13976b0aec916a937e99f3a89
B = e84ab9202722498baa2c9158f40d47b1f03df4d13976b0aec916a937e99f3a89
Result = 9af01fa6947a60679b6f87efe9b6fba97baf5d55a19d5e91dd5da1da10caeebf

Test = MulMont
A = add67c61d8479570f45a59e9b04974f970b0c4c6c046056fea1bdf3f0e7d3152
B = add67c61d8479570f45a59e9b04974f970b0c4c6c046056fea1bdf3f0e7d3152
Result = c0c68b4327e3fe7e0522167a54b25aaa6f76085ce4f6550479c89f3f1c39dd18

Test = MulMont
A = 434ef0db5640a3ea63125f815bc3cb3c92d06dbc3b5cb484e01b5247b3b4bfe5
B = 434ef0db5640a3ea63125f815bc3cb3c92d06dbc3b5cb484e01b5247b3b4bfe5
Result = b5105d16b858279247ed31362a90260978d64e0492e84bffa7a0e13ee1541544

Test = MulMont
A = b1db42aa4b259d9c6104599aff622114f10c327d02c5640b74cf1742adff332d
B = b1db42aa4b259d9c6104599aff622114f10c327d02c5640b74cf1742adff332d
Result = 0c175e7f96fc62059864c561d99a8d90978c72757ba305cd8862ed6a5fadad59

Test = MulMont
A = 7610271796be25416b652badd3119938974b20d4fc92244aea76d23b80d178f0
B = 7610271796be25416b652badd3119938974b20d4fc92244aea76d23b80d178f0
Result = 67d76e4a7c8355bb362481a76a63b365ad79767cc672b174130e833d41ca5709

Test = MulMont
A = 3480d60b0ccafca89c86f22f78380cead81310241f27a815e6fd21c2060caed8
B = 3480d60b0ccafca89c86f22f78380cead81310241f27a815e6fd21c2060caed8
Result = 68bfb2652d3bf03d17b20b2c52c68e847b0006047ba4ea81d4b85af2e0a21f72

Test = MulMont
A = 8ad6fa8bf3fe56ece1d0970636c1429ed5dfc2441c3194928a6348b69490b537
B = 8ad6fa8bf3fe56ece1d0970636c1429ed5dfc2441c3194928a6348b69490b537
Result = f5cdccf29e09928722137fb5a5ec035d7f39580838e19b892a7a972866330318

Test = MulMont
A = 71c328ce472ae74b5028b21f9d1997e0f7dbcee979a8f9fdecfa5d37d359c835
B = 71c328ce472ae74b5028b21f9d1997e0f7dbcee979a8f9fdecfa5d37d359c835
Result = c3472fafd01fc3ed93a91ab65411cb852bd5839603a02ca6cdfbadcb9ac474a0


# Montgomery conversion tests.
#
# The following tests satisfy A * 2^-256 = Result (mod P).

Test = FromMont
A = 0585a3dada9bb283fd8db4fc46c106d28f95b8cf159a405891196dbb9ce0b5cf
Result = d198d054d25a069c40cdeeb968a5562a67c3ef659297169e4be872f234897dc0

Test = FromMont
A = 9ff49a4a3f810fd34ca6f37fb1b3c40e61bc0492227e91e41cbe06bd58ba65b8
Result = 326a061b2047d9ba4eddaba9b1fe253d5b2a24e268e3f8810767bef8cda07643

Test = FromMont
A = 05a69f8f646494be65affbd44d0536ca098d6f3640e80b5e48764ab78928cf58
Result = 5a6f9c7025d4063480c400fe6f271cf3a3d2c43f9e1ceac21a88208c28329731

Test = FromMont
A = 256481a9e52d692719330a6f1208d9eca4ddd919aee06e234cbbde77d245501b
Result = fe9fc86a2ff61a0c981d5e86c5472248e071e9639521c5be43947bfffc7d5858

Test = FromMont
A = 2062ef333cadefc36ced52a2ea7e4215b1fca29283baa1e3be76e321f1b213f0
Result = 961ce39c3bf1d699b4b61ded8a5beae6eb6185d21f1df435b079b1f6a79dc738

Test = FromMont
A = 97241c3651a8f9d2fc02730f15c3e09e48d2e645cfe927385cb81d3f454414fb
Result = 2114225803efe7b6c7fbb290cb946da4e78697aad5624c2d3fe9fb568460b93c

Test = FromMont
A = 1aae0ad2c8ac988e11beda32ca7257f4d4de41f4b74452fa46f0a3bafb39262a
Result = 77c884131c34a2c3acce8a69dc5cf55987b7999c70586a9ef3c0dfb634900296

Test = FromMont
A = 034de033e2d38cf8bec8a994414b64a2fce7c83c5d81efc3d21448225071e85d
Result = 984fecbde84f393133fb602777b4395c56449d2cbbd7d8ae428b2ee6f82a2956

Test = FromMont
A = d2b296c2004b2761b6781311c924cbf5ff56dcc0900ed5cd24f5dd2e07f32633
Result = ddcff6e031b859a814ce8f37b71c10cd5fb642af54af72deabb95adcb99307b1

Test = FromMont
A = 8f525e6af50a62fc176dec75bdf48f70ba8ab97323ba78c643ef07f6457ba070
Result = 8fa95d57aae2fff79045654501478f7a394b27b8b54113a25ac74662606f767c


# Point adding tests.
#
# The following tests satisfy Result = A + B, where Result is in affine
# coordinates and A and B are in Jacobian coordinates in the Montgomery domain.

# ∞ + ∞ = ∞.
Test = PointAdd
A.X = 0000000000000000000000000000000000000000000000000000000000000000
A.Y = 0000000000000000000000000000000000000000000000000000000000000000
A.Z = 0000000000000000000000000000000000000000000000000000000000000000
B.X = 0000000000000000000000000000000000000000000000000000000000000000
B.Y = 0000000000000000000000000000000000000000000000000000000000000000
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

# ∞ + ∞ = ∞, with an alternate representation of ∞.
Test = PointAdd
A.X = 2b11cb945c8cf152ffa4c9c2b1c965b019b35d0b7626919ef0ae6cb9d232f8af
A.Y = 6d333da42e30f7011245b6281015ded14e0f100968e758a1b6c3c083afc14ea0
A.Z = 0000000000000000000000000000000000000000000000000000000000000000
B.X = 2b11cb945c8cf152ffa4c9c2b1c965b019b35d0b7626919ef0ae6cb9d232f8af
B.Y = 6d333da42e30f7011245b6281015ded14e0f100968e758a1b6c3c083afc14ea0
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

# g + ∞ = g.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 0000000000000000000000000000000000000000000000000000000000000000
B.Y = 0000000000000000000000000000000000000000000000000000000000000000
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
Result.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a

# g + ∞ = g, with an alternate representation of ∞.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 2b11cb945c8cf152ffa4c9c2b1c965b019b35d0b7626919ef0ae6cb9d232f8af
B.Y = 6d333da42e30f7011245b6281015ded14e0f100968e758a1b6c3c083afc14ea0
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
Result.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a

# g + -g = ∞.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
B.Y = 7a8e00e6da77a27b2d17797722de0cda74b5471c45e61ba3220daca8316aa9f5
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

Test = PointAdd
A.X = bcba3eebf2b0af1174a4b874b155b4dc74bd5fb57c70214561aaabb105635580
A.Y = 1dc33ce74f651305dd89263c1d314edd2773ef6dd043742a6f47f29542b9eb07
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = f9e0b98b1a87b6c49c4cc5fc47efd157e5f12cf5543d71cfa38187a3793d6791
B.Y = 3b2de94df438554381037c9f9d2c21991c6975d83c0acd42ef1a8419a040436f
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 6bd7b4e06d7862f749901a398417e941618c11c48dffcce719e4026220b77477
Result.Y = 1e2ffd71e8c206acc19032d26a53ea275fefea51a2c90e4dd3c8b7c6acc51ab6

Test = PointAdd
A.X = d71c6da129f6e867bf525563e1d8bdbd2f90a9bac7de867a6ea2317a5d6cb507
A.Y = 125e0cc1ba0c93caa19edb419a764f88d955289c4c6e77d02d90e4e31d47c9a2
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 334c2200ec08896808ab12a76820ff674fcdccff6d85afa2e586b31fc944de33
B.Y = b5ee8cfa25896d4075588c60926a2582a099c7a5acbcfec78fba457c4886301c
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 93e9d4e6f7736f80da1b00d221024ccfd17f2927d6b505a5bcefe0801fe6f0a9
Result.Y = 4824eeb2d5da27d57e1d50c2dae000acdcddcbaf534d8b7e7d97854ed3dc939e

Test = PointAdd
A.X = 0daba41be2b418e7d160a363e6cbdcbff5d433f96b0d5be3812c0a7adfab8ed4
A.Y = 3ae4dd97c4d2987a63df16c5fb8c494164e14b93eeebd5585d74bd26e2201499
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 87135fb06383ec8b282fdc028eb38fd447ac1ecc76922e37f0cc454febb11aee
B.Y = 98ab966087531eb3eea1e5e36189271a02f7ee8e381f9c78d6f346a301f96f81
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 2e096c2fabf06a5b838c7e07fda436d068dd1c4e3ff4f5704f89ab9df6b4be5b
Result.Y = 59ca6304321ae1e41bfa30f52e7ef27fceeade8507f20837654383d70e8a41df

Test = PointAdd
A.X = 356db98c21c2169899b9b296edcacb7d531524f2572913b75edb7b73196f5682
A.Y = 47a26c52b1b2f229109e8aca7f5b4af768baf053a15ff8f58051c7e4e1b7f818
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 56956f6d3bbbd4aece299f29bb4c537355f312f391c207c6ec6efe646362b288
B.Y = a69fc73c0636c9928764cc9d6e1482577b6ca06f277c098f571108356a858cab
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = ca0ddd995a77173a1438473bf82734cb3a09fafe7050bda9bd592a1cf078fa38
Result.Y = 379da87952d36c5396b934a2ce8b003ee8fc4155b3b488f2f550734e2a82ce7d

Test = PointAdd
A.X = 13764cccab4addf5cf4ef5fb4af60a93e08fa3a0a72653abf013e3427abbf82c
A.Y = c3dc524745368a0dc4948f897402f4b5a280acbf74f5ea9180d038a483d4090a
A.Z = 2903a04d6615ec23cd63ba46287be2e7a8eeee030bed49e7a94769386a46f209
B.X = a5c5921f9a8c569f661693bfae1b167937987c2fe951956ef0e34c426965c648
B.Y = f8a299605e690a78e583371e59cf2b848d475afc35bb1448981c53ad8c0a6581
B.Z = 9c3fde73f1899a76eb40f055fce02ab9c1b1ce7d43b54c54f93ffe56830e3f83
Result.X = 4073318e85bc2d7637fd0129fa8eb86b6ca20334542795f3bb1de54b90a16b69
Result.Y = 9a1b1e7435d98287b244d2337f8bf0e9c87b40677bf1ea2a9dedbd07c5241ee0

Test = PointAdd
A.X = f72706b81fca2b1530238bdc2c0c454b5116ee54fdf156bc62bffea73f0645af
A.Y = c6e66d9ae8fc5e164e6a985f866aae41f3c4e4281a0eea9173e4e77cb29e4bc7
A.Z = 6a84f9c37634b8aefdae477e9efec66f20d2f6159575f40c7b21a1e0732e8c49
B.X = bcf21b020cb8fb4b2ef7f639240d221dd96fc08d7fa575c2e7037fc84d8f03b2
B.Y = abc500f82f06f0d69a920c8d80eef9dd2310cd09e0d89d80fc7397aa4e361dd1
B.Z = 5031c46be15f9d4fa9a347be998c07f9cc7f754999fe0f9c3c8b38e0d85dda9f
Result.X = 401b010df4dd21ed96f7c8babb401db74b3b6ee7f55c498803203855b5911de9
Result.Y = 05e585cca569bc22855f7df32b20a4a45315a1ca5d98d2b94792eb748ec8744b

Test = PointAdd
A.X = 7b44b52e9fb1bc58c81a2adc9bfedcc42bba3cb34ec666e51cba8050d48fdb37
A.Y = 2b7e629fef7b4e175f5eb30c421e60f26fefdf5f9fed743cad4a8e638c18696a
A.Z = 68f31acd92bed56a4556e954b0c51f9f8f3b797bc853d1b2b01b228657bd317f
B.X = 3d293c36fd065d1f054eb218932d60feb00d1bd4bee0236cb9788d9723df9571
B.Y = c8b893b8e9ff935f2e060227334e32ba144f4046b1bd4961f4479ad3fef1c7d2
B.Z = 9c072deacfe5c025c763efebb4feab79e954c47d3e86ef4abfbd1901f50d8495
Result.X = 245582d32415c77a2e3abbf844cf1a40c31466c1418cd279747e5394744509be
Result.Y = 5c2f80f947d2df7fb1f829d05c6175f6fce7cd2d7f79fd7aa865f930e910e9fd

Test = PointAdd
A.X = 75ab91b8a46a5a1abf827cb209373b28cbb8f83a06adf6a9b10ac76e22493ecc
A.Y = abd989a78d1bcee7e63920d7e637f9763901da408a9d8c731e4e65a6fc52e1a1
A.Z = 188a24145243ca066c35870e5a8835532ad512fbdcf5f5ae4033b262fa9aa6b8
B.X = 5d6e885ec19069b2aa51a2723c98da1f03e8dbc344fe1de0bdb42910ba8bfe96
B.Y = a1f86e66eacc38db7e47154a324a16031705b4803addf074037d3320b50dbef8
B.Z = 5cff900a783687049a7d497b1f8cd837c479a61f3fef4b7ced180ea82770bc75
Result.X = a4029333b9b9db434eea002bd6d4e0d9f3e5317c685511a30ecae351fc60d164
Result.Y = 8e9302c77bc6f560c9bec473ef1ffb76b357c0d4794192696bda8e99651798ee

Test = PointAdd
A.X = 8d1867f890abaa26b634d5d5cdeb0f4abc7ebd16d807479f837fcece592dc0eb
A.Y = fc68c801999c12070eddeb3169219c491f9e8fe29cdc4e3cb698ee8471934076
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 8d1867f890abaa26b634d5d5cdeb0f4abc7ebd16d807479f837fcece592dc0eb
B.Y = fc68c801999c12070eddeb3169219c491f9e8fe29cdc4e3cb698ee8471934076
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 8da53dc540c1450c73082ad3b799d0d18a69a747fcd81f847e9e60484dcf579a
Result.Y = c20c398e99e0513a452b5e9b6331863d1ac3eee6fcf73021f505a0b62daf6f80

Test = PointAdd
A.X = 328b983f6490312e37e8eeb2121cd622cf85dbcf78af93df74fbca961ce3bfa2
A.Y = 1c8a0aea2f2e540770644f48c41810bf7f9e1a782b2f6397712b17c88109fbce
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 328b983f6490312e37e8eeb2121cd622cf85dbcf78af93df74fbca961ce3bfa2
B.Y = 1c8a0aea2f2e540770644f48c41810bf7f9e1a782b2f6397712b17c88109fbce
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = b6f3c548944862dfdea2314ca6d6a88780b08da41becf58384af80544aca4966
Result.Y = 95afecb4ad3195485a2aad3cd14008c9a7c1e0c02656c3c2b7cd5f2e7f3a4474

Test = PointAdd
A.X = 3ae6b24cadd6a14612d24a1c094a35c6be56db8f53a6d526e0ede03923918443
A.Y = de8a23105c5f5c88b77dbde74e30a56f8865d78a5ce9060cff9f2927dbd196b6
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 3ae6b24cadd6a14612d24a1c094a35c6be56db8f53a6d526e0ede03923918443
B.Y = de8a23105c5f5c88b77dbde74e30a56f8865d78a5ce9060cff9f2927dbd196b6
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 6f125b512c3c736f39781fcd89adb653e515b4ce1e1204505f08d0a8480052ef
Result.Y = e1acfccf1b9950067adf0f06e0d9703a8b1ac1bbdbb35b08df28cd56c24ae5a0

Test = PointAdd
A.X = f317c6c02d9a6ff0799b3b4a22f83c95324831baad336ecd0c631ea04a5e11c8
A.Y = b624e8057d411031f41b30cd02f56c24e89262e885007b7a1ed1861feb7ffcda
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = f317c6c02d9a6ff0799b3b4a22f83c95324831baad336ecd0c631ea04a5e11c8
B.Y = b624e8057d411031f41b30cd02f56c24e89262e885007b7a1ed1861feb7ffcda
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = e805208c74602e54482d113f16fcf6e4600436f8af49705cdd05ecfb0e6d45fd
Result.Y = baded898bfead1b4eb3ab3bbd0129837efc85823dabe82718a975bd603f96d9e

Test = PointAdd
A.X = 3a6802aeaebc67046a1e75152822fa8bab04c11ae2b816f42c073daee3f13274
A.Y = d6522c882d18e32bc5ea1fa59efbce8ce2369f2154dcc00e6fb17500f50f8ebf
A.Z = bea747d5bb1c6ee865249d7a22378f3c760916e163497f4b6ef4da8adcb5dfab
B.X = 3a6802aeaebc67046a1e75152822fa8bab04c11ae2b816f42c073daee3f13274
B.Y = d6522c882d18e32bc5ea1fa59efbce8ce2369f2154dcc00e6fb17500f50f8ebf
B.Z = bea747d5bb1c6ee865249d7a22378f3c760916e163497f4b6ef4da8adcb5dfab
Result.X = 5a2891dca746889d413d8dc1a69b715954baf692689fc32d9aa10b7431a5c149
Result.Y = 91db7288536b4f6d78e5a787ecbb5094f6834515038cb070a7fa4870af8045f0

Test = PointAdd
A.X = c76ddbcb15bc63f82807804536a0d25fd7a639c71adf953ad6cc8f68d915f485
A.Y = e3a4f830809f5e91b68699c05fa9faa7c3d1f9d1b1c982c282508fa18d695537
A.Z = eb372f19c7b9466a116363ad9114a89ad287523da318d915f59ed5e558bd824e
B.X = c76ddbcb15bc63f82807804536a0d25fd7a639c71adf953ad6cc8f68d915f485
B.Y = e3a4f830809f5e91b68699c05fa9faa7c3d1f9d1b1c982c282508fa18d695537
B.Z = eb372f19c7b9466a116363ad9114a89ad287523da318d915f59ed5e558bd824e
Result.X = c5485a3509f55c7cc33d098fb0bfe1b198a9f26ce0ebc29bec5baa29ef6f74a2
Result.Y = 60e949a551aa94afc9a3efe411a3c63ecb851ef1738ed24c88f86cf85ec01020

Test = PointAdd
A.X = ca72936509631f09d2a3ac14fb786daabb15520ef01de4298c7fd71653e89194
A.Y = 02aeb6b6f04cd8125887baa18e6e79ba2b0acfa9a2443e9eea36ca7715eb8eb3
A.Z = 8b4ef1a52fa42c711445e0463003f2ed38ace6583bf08198e9a0b938b4589479
B.X = ca72936509631f09d2a3ac14fb786daabb15520ef01de4298c7fd71653e89194
B.Y = 02aeb6b6f04cd8125887baa18e6e79ba2b0acfa9a2443e9eea36ca7715eb8eb3
B.Z = 8b4ef1a52fa42c711445e0463003f2ed38ace6583bf08198e9a0b938b4589479
Result.X = 8d3b35c5661faafa83510ab9b3f1642bb121e7686ed4ae61323ddee2c7247f93
Result.Y = 1a22ef5df156ca80235fe3cd1ca3152e21a3e17b2a34dd93b2003e3274a8a2fb

Test = PointAdd
A.X = db7b023fbe056819027fa09c5a2a0d777a53fb78c00bf4f31f46b63a7494bbfe
A.Y = 59affcbf4628d572ee56b95087d30e765bb518b123e879b25df9960dab706a32
A.Z = 1f7c7226d78e51478c683bbb6afe01abc2225dbfc773d0806d30ff5f827b76c8
B.X = db7b023fbe056819027fa09c5a2a0d777a53fb78c00bf4f31f46b63a7494bbfe
B.Y = 59affcbf4628d572ee56b95087d30e765bb518b123e879b25df9960dab706a32
B.Z = 1f7c7226d78e51478c683bbb6afe01abc2225dbfc773d0806d30ff5f827b76c8
Result.X = fba400ae656ec3103c5c5f531d2a0f7368031e01a48a91f1a4f3138d294b13be
Result.Y = 160e358ad1f059eb62722df01a7440048a1db21ecaea8698efa9677db6e9ff97
