#!/bin/sh

# Test the walk_rtree.c code.
#

#error: wanted
#got tgt list:
check='echo Running walk_rtree test $1 $2 ... ; ans=`./t_walk_rtree $1 $2 | sed -e s,krbtgt/,,g`; ans=`echo $ans`; echo Got TGT list: "$ans" ; if test "$3" != "$ans" ; then err=1; echo ERROR: wanted "$3"; fi; echo ""'

err=0

set ATHENA.MIT.EDU HACK.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> COM@EDU EXAMPLE.COM@COM <EMAIL>"
eval $check

set ATHENA.MIT.EDU CSAIL.MIT.EDU "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set FOO.EXAMPLE.COM BAR.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set FOZ.EXAMPLE.COM BAZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set FOZ.EXAMPLE.COM BOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set FOZ.EXAMPLE.COM OZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set OZ.EXAMPLE.COM FOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL>"
eval $check

set A.FOZ.EXAMPLE.COM A.OZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

set A.OZ.EXAMPLE.COM A.FOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

set A.FOZ.EXAMPLE.COM A.BOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

set A.BOZ.EXAMPLE.COM A.FOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

set A.FOZ.EXAMPLE.COM OZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

set OZ.EXAMPLE.COM A.FOZ.EXAMPLE.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

#set EXAMPLE.COM EXAMPLE.COM "<EMAIL>"
set EXAMPLE.COM EXAMPLE.COM ""
echo Next test should return a cannot-find-ticket error...
eval $check

set A.B B.B "A.B@A.B B@A.B B.B@B"
eval $check

set AB.B B.B "AB.B@AB.B B@AB.B B.B@B"
eval $check

set A.B BA.B "A.B@A.B B@A.B BA.B@B"
eval $check

set EXAMPLE.COM A.EXAMPLE.COM "<EMAIL> <EMAIL>"
eval $check

set A.EXAMPLE.COM EXAMPLE.COM "<EMAIL> <EMAIL>"
eval $check

echo CAPATH test
set ATHENA.MIT.EDU KERBEROS.COM "<EMAIL> <EMAIL>"
eval $check

echo CAPATH test
set LCS.MIT.EDU KABLOOEY.KERBEROS.COM "<EMAIL> <EMAIL> <EMAIL> <EMAIL>"
eval $check

exit $err
