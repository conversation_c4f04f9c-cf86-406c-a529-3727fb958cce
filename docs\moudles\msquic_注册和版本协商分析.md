


          
# MsQuic 注册和版本协商功能分析

## 1. 注册功能

### 1.1 注册功能概述

注册(Registration)是MsQuic对象层次结构中的最高层，它维护应用程序的所有执行上下文。主要包括一组工作线程，用于驱动特定于应用程序上下文的所有连接。

### 1.2 注册数据结构

```c
typedef struct QUIC_REGISTRATION {
    QUIC_HANDLE_TYPE Type;                     // 句柄类型
    BOOLEAN NoPartitioning;                    // 是否禁用分区
    BOOLEAN ShuttingDown;                      // 是否正在关闭
    QUIC_EXECUTION_PROFILE ExecProfile;        // 执行配置文件
    CXPLAT_LOCK ConfigLock;                    // 配置锁
    CXPLAT_LIST_ENTRY Configurations;          // 配置列表
    CXPLAT_DISPATCH_LOCK ConnectionLock;       // 连接锁
    CXPLAT_LIST_ENTRY Connections;             // 连接列表
    CXPLAT_LIST_ENTRY Listeners;               // 监听器列表
    CXPLAT_RUNDOWN_REF Rundown;                // 运行引用计数
    CXPLAT_LIST_ENTRY Link;                    // 链接到全局注册列表
    QUIC_WORKER_POOL* WorkerPool;              // 工作线程池
    QUIC_CONNECTION_SHUTDOWN_FLAGS ShutdownFlags; // 关闭标志
    QUIC_UINT62 ShutdownErrorCode;             // 关闭错误码
    uint8_t AppNameLength;                     // 应用名称长度
    BOOLEAN IsVerifying;                       // 是否正在验证
    char AppName[0];                           // 应用名称
} QUIC_REGISTRATION;
```

### 1.3 注册功能使用流程

#### 1.3.1 创建注册

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant API as MsQuic API
    participant Reg as 注册对象
    participant Lib as MsQuic库
    
    App->>API: MsQuicRegistrationOpen(Config, &Registration)
    API->>Lib: QuicLibraryLazyInitialize()
    Lib-->>API: 初始化库
    API->>Reg: 分配注册对象
    API->>Reg: 初始化注册对象字段
    API->>Reg: QuicWorkerPoolInitialize()
    Reg-->>API: 初始化工作线程池
    API->>Lib: 添加到全局注册列表
    API-->>App: 返回注册句柄
```

#### 1.3.2 关闭注册

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant API as MsQuic API
    participant Reg as 注册对象
    participant Lib as MsQuic库
    
    App->>API: MsQuicRegistrationClose(Registration)
    API->>Lib: 从全局注册列表移除
    API->>Reg: CxPlatRundownReleaseAndWait()
    Reg-->>API: 等待所有操作完成
    API->>Reg: QuicWorkerPoolUninitialize()
    Reg-->>API: 清理工作线程池
    API->>Reg: 释放资源
    API-->>App: 返回
```

#### 1.3.3 注册状态图

```mermaid
stateDiagram-v2
    [*] --> 初始化: MsQuicRegistrationOpen
    初始化 --> 活动: 初始化成功
    活动 --> 关闭中: MsQuicRegistrationShutdown
    活动 --> 销毁中: MsQuicRegistrationClose
    关闭中 --> 销毁中: MsQuicRegistrationClose
    销毁中 --> [*]: 资源释放完成
```

### 1.4 关键函数

#### 1.4.1 MsQuicRegistrationOpen

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
QUIC_STATUS
QUIC_API
MsQuicRegistrationOpen(
    _In_opt_ const QUIC_REGISTRATION_CONFIG* Config,
    _Outptr_ _At_(*NewRegistration, __drv_allocatesMem(Mem)) _Pre_defensive_
        HQUIC* NewRegistration
    )
```

功能：创建一个新的注册对象，用于管理应用程序的执行上下文。

参数：
- `Config`：注册配置，包含应用名称和执行配置文件
- `NewRegistration`：输出参数，返回创建的注册句柄

返回值：操作状态码

#### 1.4.2 MsQuicRegistrationClose

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
void
QUIC_API
MsQuicRegistrationClose(
    _In_ _Pre_defensive_ __drv_freesMem(Mem)
        HQUIC Handle
    )
```

功能：关闭注册对象，释放相关资源。

参数：
- `Handle`：要关闭的注册句柄

#### 1.4.3 MsQuicRegistrationShutdown

```c
_IRQL_requires_max_(DISPATCH_LEVEL)
void
QUIC_API
MsQuicRegistrationShutdown(
    _In_ _Pre_defensive_ HQUIC Handle,
    _In_ QUIC_CONNECTION_SHUTDOWN_FLAGS Flags,
    _In_ _Pre_defensive_ QUIC_UINT62 ErrorCode
    )
```

功能：关闭注册对象下的所有连接和监听器。

参数：
- `Handle`：要关闭的注册句柄
- `Flags`：关闭标志
- `ErrorCode`：关闭错误码

### 1.5 工作线程池

注册对象管理一个工作线程池，用于处理连接的操作。工作线程数量取决于系统处理器的总数。注册管理的连接集尽可能平均地分配给这些工作线程。

每个工作线程负责调用`QuicConnDrainOperations`，允许连接处理当前排队的工作。

## 2. 版本协商功能

### 2.1 版本协商概述

QUIC协议支持版本协商，允许客户端和服务器协商使用的QUIC版本。MsQuic实现了QUIC版本协商扩展(Version Negotiation Extension)，提供了更安全的版本协商机制。

### 2.2 版本协商相关数据结构

```c
// 兼容版本映射
typedef struct QUIC_COMPATIBLE_VERSION_MAP {
    const uint32_t OriginalVersion;    // 原始版本
    const uint32_t CompatibleVersion;  // 兼容版本
} QUIC_COMPATIBLE_VERSION_MAP;

// 版本信息结构
typedef struct QUIC_VERSION_INFORMATION_V1 {
    uint32_t ChosenVersion;            // 选择的版本
    uint32_t AvailableVersionsCount;   // 可用版本数量
    const uint32_t* AvailableVersions; // 可用版本列表
} QUIC_VERSION_INFORMATION_V1;
```

### 2.3 版本协商流程

#### 2.3.1 客户端版本协商流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    
    Client->>Server: 初始包(包含版本信息)
    Server->>Client: 版本协商包(如果不支持客户端版本)
    Client->>Client: 选择支持的版本
    Client->>Server: 新的初始包(使用协商的版本)
    Server->>Client: 初始包响应(确认版本)
```

#### 2.3.2 版本协商扩展流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    
    Client->>Server: 初始包(包含版本信息扩展)
    Note over Client,Server: 版本信息包含客户端支持的所有版本
    Server->>Client: 初始包响应(包含版本信息扩展)
    Note over Client,Server: 版本信息包含服务器选择的版本和支持的版本
    Client->>Client: 验证服务器选择的版本是否兼容
```

#### 2.3.3 版本协商状态图

```mermaid
stateDiagram-v2
    [*] --> 初始化: 连接创建
    初始化 --> 发送版本信息: 客户端发送初始包
    发送版本信息 --> 等待服务器响应: 等待服务器版本信息
    等待服务器响应 --> 版本协商: 收到不兼容版本
    等待服务器响应 --> 版本确认: 收到兼容版本
    版本协商 --> 发送新版本: 选择新版本
    发送新版本 --> 等待服务器响应: 发送新初始包
    版本确认 --> [*]: 版本协商完成
```

### 2.4 关键函数

#### 2.4.1 QuicVersionNegotiationExtIsVersionServerSupported

```c
BOOLEAN
QuicVersionNegotiationExtIsVersionServerSupported(
    _In_ uint32_t Version
    )
```

功能：检查服务器是否支持指定的QUIC版本。

参数：
- `Version`：要检查的QUIC版本

返回值：如果支持返回TRUE，否则返回FALSE

#### 2.4.2 QuicVersionNegotiationExtIsVersionClientSupported

```c
BOOLEAN
QuicVersionNegotiationExtIsVersionClientSupported(
    _In_ QUIC_CONNECTION* Connection,
    _In_ uint32_t Version
    )
```

功能：检查客户端是否支持指定的QUIC版本。

参数：
- `Connection`：连接对象
- `Version`：要检查的QUIC版本

返回值：如果支持返回TRUE，否则返回FALSE

#### 2.4.3 QuicVersionNegotiationExtAreVersionsCompatible

```c
BOOLEAN
QuicVersionNegotiationExtAreVersionsCompatible(
    _In_ uint32_t OriginalVersion,
    _In_ uint32_t UpgradedVersion
    )
```

功能：检查两个QUIC版本是否兼容。

参数：
- `OriginalVersion`：原始版本
- `UpgradedVersion`：升级版本

返回值：如果兼容返回TRUE，否则返回FALSE

#### 2.4.4 QuicVersionNegotiationExtEncodeVersionInfo

```c
_IRQL_requires_max_(PASSIVE_LEVEL)
__drv_allocatesMem(Mem)
_Must_inspect_result_
_Success_(return != NULL)
_Ret_writes_bytes_(*VerInfoLength)
const uint8_t*
QuicVersionNegotiationExtEncodeVersionInfo(
    _In_ QUIC_CONNECTION* Connection,
    _Out_ uint32_t* VerInfoLength
    )
```

功能：编码连接的版本信息。

参数：
- `Connection`：连接对象
- `VerInfoLength`：输出参数，返回版本信息的长度

返回值：编码后的版本信息缓冲区

#### 2.4.5 QuicVersionNegotiationExtParseVersionInfo

```c
QUIC_STATUS
QuicVersionNegotiationExtParseVersionInfo(
    _In_ QUIC_CONNECTION* Connection,
    _In_reads_bytes_(BufferLength)
        const uint8_t* const Buffer,
    _In_ uint16_t BufferLength,
    _Out_ QUIC_VERSION_INFORMATION_V1* VersionInfo
    )
```

功能：解析版本信息。

参数：
- `Connection`：连接对象
- `Buffer`：包含版本信息的缓冲区
- `BufferLength`：缓冲区长度
- `VersionInfo`：输出参数，返回解析后的版本信息

返回值：操作状态码

### 2.5 版本兼容性

MsQuic定义了一个版本兼容性映射表，用于确定不同QUIC版本之间的兼容性：

```c
const QUIC_COMPATIBLE_VERSION_MAP CompatibleVersionsMap[] = {
    {QUIC_VERSION_MS_1, QUIC_VERSION_1},
    {QUIC_VERSION_1, QUIC_VERSION_MS_1},
    {QUIC_VERSION_1, QUIC_VERSION_2}
};
```

这表示：
- Microsoft QUIC版本1与IETF QUIC版本1兼容
- IETF QUIC版本1与Microsoft QUIC版本1兼容
- IETF QUIC版本1与IETF QUIC版本2兼容

## 3. 注册与版本协商的关系

注册是MsQuic对象层次结构的顶层，它管理连接的执行上下文。版本协商是QUIC协议的一个特性，发生在连接建立阶段。

```mermaid
graph TD
    A[注册Registration] --> B[配置Configuration]
    B --> C[连接Connection]
    C --> D[流Stream]
    C --> E[版本协商]
    E --> F[加密握手]
    F --> G[数据传输]
```

注册对象通过工作线程池处理连接的操作，包括版本协商过程中的数据包处理和状态转换。

## 4. 总结

### 4.1 注册功能

注册是MsQuic中的顶层对象，负责：
- 管理应用程序的执行上下文
- 维护工作线程池
- 协调连接和监听器的生命周期
- 提供资源隔离和管理

### 4.2 版本协商功能

版本协商是QUIC协议的核心特性，MsQuic实现了：
- 基本的版本协商机制
- 版本协商扩展(VNE)
- 版本兼容性检查
- 安全的版本降级防护

这两个功能共同确保了MsQuic能够高效、安全地建立和管理QUIC连接，支持不同版本的QUIC协议，并提供良好的性能和可靠性。