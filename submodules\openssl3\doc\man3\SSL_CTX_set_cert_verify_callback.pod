=pod

=head1 NAME

SSL_CTX_set_cert_verify_callback - set peer certificate verification procedure

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_CTX_set_cert_verify_callback(SSL_CTX *ctx,
                                       int (*callback)(X509_STORE_CTX *, void *),
                                       void *arg);

=head1 DESCRIPTION

SSL_CTX_set_cert_verify_callback() sets the verification callback function for
I<ctx>. SSL objects that are created from I<ctx> inherit the setting valid at
the time when L<SSL_new(3)> is called.

=head1 NOTES

When a peer certificate has been received during a SSL/TLS handshake,
a verification function is called regardless of the verification mode.
If the application does not explicitly specify a verification callback function,
the built-in verification function is used.
If a verification callback I<callback> is specified via
SSL_CTX_set_cert_verify_callback(), the supplied callback function is called
instead with the arguments callback(X509_STORE_CTX *x509_store_ctx, void *arg).
The argument I<arg> is specified by the application when setting I<callback>.
By setting I<callback> to NULL, the default behaviour is restored.

I<callback> should return 1 to indicate verification success
and 0 to indicate verification failure.
In server mode, a return value of 0 leads to handshake failure.
In client mode, the behaviour is as follows.
All values, including 0, are ignored
if the verification mode is B<SSL_VERIFY_NONE>.
Otherwise, when the return value is less than or equal to 0, the handshake will
fail.

In client mode I<callback> may also call the L<SSL_set_retry_verify(3)>
function on the B<SSL> object set in the I<x509_store_ctx> ex data (see
L<SSL_get_ex_data_X509_STORE_CTX_idx(3)>) and return 1. This would be
typically done in case the certificate verification was not yet able
to succeed. This makes the handshake suspend and return control to the
calling application with B<SSL_ERROR_WANT_RETRY_VERIFY>. The app can for
instance fetch further certificates or cert status information needed for
the verification. Calling L<SSL_connect(3)> again resumes the connection
attempt by retrying the server certificate verification step.
This process may even be repeated if need be.

In any case a viable verification result value must be reflected
in the B<error> member of I<x509_store_ctx>,
which can be done using L<X509_STORE_CTX_set_error(3)>.
This is particularly important in case
the I<callback> allows the connection to continue (by returning 1).
Note that the verification status in the store context is a possibly durable
indication of the chain's validity!
This gets recorded in the SSL session (and thus also in session tickets)
and the validity of the originally presented chain is then visible
on resumption, even though no chain is presented int that case.
Moreover, the calling application will be informed about the detailed result of
the verification procedure and may elect to base further decisions on it.

Within I<x509_store_ctx>, I<callback> has access to the I<verify_callback>
function set using L<SSL_CTX_set_verify(3)>.

=head1 RETURN VALUES

SSL_CTX_set_cert_verify_callback() does not return a value.

=head1 WARNINGS

Do not mix the verification callback described in this function with the
B<verify_callback> function called during the verification process. The
latter is set using the L<SSL_CTX_set_verify(3)>
family of functions.

Providing a complete verification procedure including certificate purpose
settings etc is a complex task. The built-in procedure is quite powerful
and in most cases it should be sufficient to modify its behaviour using
the B<verify_callback> function.

=head1 BUGS

SSL_CTX_set_cert_verify_callback() does not provide diagnostic information.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set_verify(3)>,
L<X509_STORE_CTX_set_error(3)>,
L<SSL_get_verify_result(3)>,
L<SSL_set_retry_verify(3)>,
L<SSL_CTX_load_verify_locations(3)>

=head1 COPYRIGHT

Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
