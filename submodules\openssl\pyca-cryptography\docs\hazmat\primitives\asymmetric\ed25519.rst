.. hazmat::

Ed25519 signing
===============

.. currentmodule:: cryptography.hazmat.primitives.asymmetric.ed25519


Ed25519 is an elliptic curve signing algorithm using `EdDSA`_ and
`Curve25519`_. If you do not have legacy interoperability concerns then you
should strongly consider using this signature algorithm.


Signing & Verification
~~~~~~~~~~~~~~~~~~~~~~

.. doctest::

    >>> from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
    >>> private_key = Ed25519PrivateKey.generate()
    >>> signature = private_key.sign(b"my authenticated message")
    >>> public_key = private_key.public_key()
    >>> # Raises InvalidSignature if verification fails
    >>> public_key.verify(signature, b"my authenticated message")

Key interfaces
~~~~~~~~~~~~~~

.. class:: Ed25519PrivateKey

    .. versionadded:: 2.6

    .. classmethod:: generate()

        Generate an Ed25519 private key.

        :returns: :class:`Ed25519PrivateKey`

    .. classmethod:: from_private_bytes(data)

        :param data: 32 byte private key.
        :type data: :term:`bytes-like`

        :returns: :class:`Ed25519PrivateKey`

        :raises ValueError: This is raised if the private key is not 32 bytes long.

        :raises cryptography.exceptions.UnsupportedAlgorithm: If the private key
            is of a type that is not supported by the backend.

        .. doctest::

            >>> from cryptography.hazmat.primitives import serialization
            >>> from cryptography.hazmat.primitives.asymmetric import ed25519
            >>> private_key = ed25519.Ed25519PrivateKey.generate()
            >>> private_bytes = private_key.private_bytes(
            ...     encoding=serialization.Encoding.Raw,
            ...     format=serialization.PrivateFormat.Raw,
            ...     encryption_algorithm=serialization.NoEncryption()
            ... )
            >>> loaded_private_key = ed25519.Ed25519PrivateKey.from_private_bytes(private_bytes)


    .. method:: public_key()

        :returns: :class:`Ed25519PublicKey`

    .. method:: sign(data)

        :param bytes data: The data to sign.

        :returns bytes: The 64 byte signature.

    .. method:: private_bytes(encoding, format, encryption_algorithm)

        Allows serialization of the key to bytes. Encoding (
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.PEM`,
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.DER`, or
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.Raw`) and
        format (
        :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.PKCS8`,
        :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.OpenSSH`
        or
        :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.Raw`
        ) are chosen to define the exact serialization.

        :param encoding: A value from the
            :class:`~cryptography.hazmat.primitives.serialization.Encoding` enum.

        :param format: A value from the
            :class:`~cryptography.hazmat.primitives.serialization.PrivateFormat`
            enum. If the ``encoding`` is
            :attr:`~cryptography.hazmat.primitives.serialization.Encoding.Raw`
            then ``format`` must be
            :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.Raw`
            , otherwise it must be
            :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.PKCS8` or
            :attr:`~cryptography.hazmat.primitives.serialization.PrivateFormat.OpenSSH`.

        :param encryption_algorithm: An instance of an object conforming to the
            :class:`~cryptography.hazmat.primitives.serialization.KeySerializationEncryption`
            interface.

        :return bytes: Serialized key.

.. class:: Ed25519PublicKey

    .. versionadded:: 2.6

    .. classmethod:: from_public_bytes(data)

        :param bytes data: 32 byte public key.

        :returns: :class:`Ed25519PublicKey`

        :raises ValueError: This is raised if the public key is not 32 bytes long.

        :raises cryptography.exceptions.UnsupportedAlgorithm: If the public key
            is of a type that is not supported by the backend.

        .. doctest::

            >>> from cryptography.hazmat.primitives import serialization
            >>> from cryptography.hazmat.primitives.asymmetric import ed25519
            >>> private_key = ed25519.Ed25519PrivateKey.generate()
            >>> public_key = private_key.public_key()
            >>> public_bytes = public_key.public_bytes(
            ...     encoding=serialization.Encoding.Raw,
            ...     format=serialization.PublicFormat.Raw
            ... )
            >>> loaded_public_key = ed25519.Ed25519PublicKey.from_public_bytes(public_bytes)

    .. method:: public_bytes(encoding, format)

        Allows serialization of the key to bytes. Encoding (
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.PEM`,
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.DER`,
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.OpenSSH`,
        or
        :attr:`~cryptography.hazmat.primitives.serialization.Encoding.Raw`) and
        format (
        :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.SubjectPublicKeyInfo`,
        :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.OpenSSH`
        , or
        :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.Raw`
        ) are chosen to define the exact serialization.

        :param encoding: A value from the
            :class:`~cryptography.hazmat.primitives.serialization.Encoding` enum.

        :param format: A value from the
            :class:`~cryptography.hazmat.primitives.serialization.PublicFormat`
            enum. If the ``encoding`` is
            :attr:`~cryptography.hazmat.primitives.serialization.Encoding.Raw`
            then ``format`` must be
            :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.Raw`.
            If ``encoding`` is
            :attr:`~cryptography.hazmat.primitives.serialization.Encoding.OpenSSH`
            then ``format`` must be
            :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.OpenSSH`.
            In all other cases ``format`` must be
            :attr:`~cryptography.hazmat.primitives.serialization.PublicFormat.SubjectPublicKeyInfo`.

        :returns bytes: The public key bytes.

    .. method:: verify(signature, data)

        :param bytes signature: The signature to verify.

        :param bytes data: The data to verify.

        :raises cryptography.exceptions.InvalidSignature: Raised when the
            signature cannot be verified.



.. _`EdDSA`: https://en.wikipedia.org/wiki/EdDSA
.. _`Curve25519`: https://en.wikipedia.org/wiki/Curve25519
