<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : Constants</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>Constants</h1>
<p>
<h2>Enumerations</h2>
<ul>
<li>enum { <br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55bafee271af4e43ec6c9bb2e3e849cc1f9">ccapi_version_2</a> =  2, 
<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55b26f201de4113dda3b4ec78dcda95d5a1">ccapi_version_3</a> =  3, 
<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55b833d16677b6eb5531fb1285b216c8f3b">ccapi_version_4</a> =  4, 
<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55bc3ad534e2499e8e48c86f852c39c8415">ccapi_version_5</a> =  5, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55bc462c2766a9fe8fa84d8fccea65b8ab3">ccapi_version_6</a> =  6, 
<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55b9c325640a04bb548b92d49df89381bc8">ccapi_version_7</a> =  7, 
<a class="el" href="group__ccapi__constants__reference.html#gg06fc87d81c62e9abb8790b6e5713c55bc33d8c3e3c172be1515cc0d2df8e3d71">ccapi_version_max</a> =  ccapi_version_7
<br>
 }
<li>enum { <br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a> =  0, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b748d5a55ed773e002ccc271beb4512c0a">ccIteratorEnd</a> =  201, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b775735bfd0c621b1c5c2a0067f60cfa9d">ccErrBadParam</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b74f31ee78c32ed1bea75d364ccbf1e690">ccErrNoMem</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7d9bf97d9c57a6a14ac5b6a7a06e008f7">ccErrInvalidContext</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b708791e8ed735ecc02c1e6b222f913c71">ccErrInvalidCCache</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b742b40a71ad0c84171c8d982f6cfdeec9">ccErrInvalidString</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b78705dd3f7b52ce8e23e9a25f552b4a84">ccErrInvalidCredentials</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7fbc118bc7860198fc6a0b8e4a161363a">ccErrInvalidCCacheIterator</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b751c0c94d8b4dfb834b27fa7090f7b4f2">ccErrInvalidCredentialsIterator</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b73e2425cf044691597d10bbc14b3c90ba">ccErrInvalidLock</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7b508b53d9628aa7e0fca3253d619e78e">ccErrBadName</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7d2f35f60002115819c8c443e67191ea4">ccErrBadCredentialsVersion</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b759ae48c63ae4aaaa3cd7bc5504847660">ccErrBadAPIVersion</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b78df04862b2e05986499f0d93268fa7b8">ccErrContextLocked</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b71606d0e0540ead205a400306e5933c04">ccErrContextUnlocked</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7a453f3f6d7e578f47efbb1734cb46002">ccErrCCacheLocked</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7d3ebfa156acf72a3a86f14760d315e24">ccErrCCacheUnlocked</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7b0fa85b2c59d51c3ec205e40191e7619">ccErrBadLockType</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7604f23ab0c8c3e1d97f8b32c4501a895">ccErrNeverDefault</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b74c8c5082e236270c5dc55e998dfb9288">ccErrCredentialsNotFound</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b73098feac66058e6ebd02c5e44fa20a9c">ccErrCCacheNotFound</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b793bd0c54010e2a36d2e2af1e8aaef06f">ccErrContextNotFound</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7267b21d05e1f4005392c52e439de03a4">ccErrServerUnavailable</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7a18e1555d7af1a60ce1978069e95fe6f">ccErrServerInsecure</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7fd9fdcb22b761cb3e53e1d6d6b545884">ccErrServerCantBecomeUID</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7d6825aa88394eb52df80bef870d986db">ccErrTimeOffsetNotSet</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b75f8cef73e4ac4c8894c4318ef921350e">ccErrBadInternalMessage</a>, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7a35748a788a849ff09dd453ac66f8314">ccErrNotImplemented</a>, 
<a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b79cab6af44e2358aa12f176a60e245d67">ccErrClientNotFound</a>
<br>
 }
<li>enum <a class="el" href="group__ccapi__constants__reference.html#gae76da96fff95c157c3b28c4455dc35c">cc_credential_versions</a> { <a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa">cc_credentials_v4</a> =  1, 
<a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668">cc_credentials_v5</a> =  2, 
<a class="el" href="group__ccapi__constants__reference.html#ggae76da96fff95c157c3b28c4455dc35ca49ae6c35599f5860241601dcb0c9e0d">cc_credentials_v4_v5</a> =  3
 }
<li>enum <a class="el" href="group__ccapi__constants__reference.html#g2081cbacd4ec3d5a64c8729fd78fc611">cc_lock_types</a> { <a class="el" href="group__ccapi__constants__reference.html#gg2081cbacd4ec3d5a64c8729fd78fc6110a9a6083623e4c7c8340c0146f032154">cc_lock_read</a> =  0, 
<a class="el" href="group__ccapi__constants__reference.html#gg2081cbacd4ec3d5a64c8729fd78fc611bc97eae3b375c4cee90087597b7f375d">cc_lock_write</a> =  1, 
<a class="el" href="group__ccapi__constants__reference.html#gg2081cbacd4ec3d5a64c8729fd78fc61184bf8feb65a88dc09aa52c6d955111a3">cc_lock_upgrade</a> =  2, 
<a class="el" href="group__ccapi__constants__reference.html#gg2081cbacd4ec3d5a64c8729fd78fc611426c331fb09e249b6944d8ba28893eba">cc_lock_downgrade</a> =  3
 }
<li>enum <a class="el" href="group__ccapi__constants__reference.html#g0eff5be22e263d0bd9e4bb6fb0a8e948">cc_lock_modes</a> { <a class="el" href="group__ccapi__constants__reference.html#gg0eff5be22e263d0bd9e4bb6fb0a8e9485fe1eddb0bba9df16dbcc63aeeb79aa3">cc_lock_noblock</a> =  0, 
<a class="el" href="group__ccapi__constants__reference.html#gg0eff5be22e263d0bd9e4bb6fb0a8e94827e385feb864d167880744d0874834d8">cc_lock_block</a> =  1
 }
<li>enum { <br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gg99fb83031ce9923c84392b4e92f956b5ae3147869a486588adf6bc588ee8cc30">cc_v4_name_size</a> =  40, 
<a class="el" href="group__ccapi__constants__reference.html#gg99fb83031ce9923c84392b4e92f956b55abe44162300eabb9a9f65b324cad493">cc_v4_instance_size</a> =  40, 
<a class="el" href="group__ccapi__constants__reference.html#gg99fb83031ce9923c84392b4e92f956b5714d28ea3d6c6807817d7b377afc22f8">cc_v4_realm_size</a> =  40, 
<a class="el" href="group__ccapi__constants__reference.html#gg99fb83031ce9923c84392b4e92f956b5b52bd33d187632efd597f282540b081a">cc_v4_ticket_size</a> =  1254, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gg99fb83031ce9923c84392b4e92f956b57532f28276c3a759e487560ee4666a29">cc_v4_key_size</a> =  8
<br>
 }
<li>enum <a class="el" href="group__ccapi__constants__reference.html#ge78c8fbb79f8ff963ec7c88c431721c3">cc_string_to_key_type</a> { <br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gge78c8fbb79f8ff963ec7c88c431721c3c0beda03d5c9e36ce9a199c98573d39a">cc_v4_stk_afs</a> =  0, 
<a class="el" href="group__ccapi__constants__reference.html#gge78c8fbb79f8ff963ec7c88c431721c31b71e069604cd1cebc2b694ef04aedd1">cc_v4_stk_des</a> =  1, 
<a class="el" href="group__ccapi__constants__reference.html#gge78c8fbb79f8ff963ec7c88c431721c3db66296923c38aa2d7c7420da28d7014">cc_v4_stk_columbia_special</a> =  2, 
<a class="el" href="group__ccapi__constants__reference.html#gge78c8fbb79f8ff963ec7c88c431721c3816b6bb45f51557bc3de5e13d38dc310">cc_v4_stk_krb5</a> =  3, 
<br>
&nbsp;&nbsp;<a class="el" href="group__ccapi__constants__reference.html#gge78c8fbb79f8ff963ec7c88c431721c3e19e1a52a4289172671e91d87ab027bb">cc_v4_stk_unknown</a> =  4
<br>
 }
</ul>
<hr><h2>Enumeration Type Documentation</h2>
<a class="anchor" name="g06fc87d81c62e9abb8790b6e5713c55b"></a><!-- doxytag: member="CredentialsCache.h::@0" ref="g06fc87d81c62e9abb8790b6e5713c55b" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">anonymous enum          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
API version numbers<p>
These constants are passed into <a class="el" href="group__cc__context__reference.html#ge4174587d8bb261e32194bbb9585fb82">cc_initialize()</a> to indicate the version of the API the caller wants to use.<p>
CCAPI v1 and v2 are deprecated and should not be used. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55bafee271af4e43ec6c9bb2e3e849cc1f9"></a><!-- doxytag: member="ccapi_version_2" ref="gg06fc87d81c62e9abb8790b6e5713c55bafee271af4e43ec6c9bb2e3e849cc1f9" args="" -->ccapi_version_2</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55b26f201de4113dda3b4ec78dcda95d5a1"></a><!-- doxytag: member="ccapi_version_3" ref="gg06fc87d81c62e9abb8790b6e5713c55b26f201de4113dda3b4ec78dcda95d5a1" args="" -->ccapi_version_3</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55b833d16677b6eb5531fb1285b216c8f3b"></a><!-- doxytag: member="ccapi_version_4" ref="gg06fc87d81c62e9abb8790b6e5713c55b833d16677b6eb5531fb1285b216c8f3b" args="" -->ccapi_version_4</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55bc3ad534e2499e8e48c86f852c39c8415"></a><!-- doxytag: member="ccapi_version_5" ref="gg06fc87d81c62e9abb8790b6e5713c55bc3ad534e2499e8e48c86f852c39c8415" args="" -->ccapi_version_5</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55bc462c2766a9fe8fa84d8fccea65b8ab3"></a><!-- doxytag: member="ccapi_version_6" ref="gg06fc87d81c62e9abb8790b6e5713c55bc462c2766a9fe8fa84d8fccea65b8ab3" args="" -->ccapi_version_6</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55b9c325640a04bb548b92d49df89381bc8"></a><!-- doxytag: member="ccapi_version_7" ref="gg06fc87d81c62e9abb8790b6e5713c55b9c325640a04bb548b92d49df89381bc8" args="" -->ccapi_version_7</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg06fc87d81c62e9abb8790b6e5713c55bc33d8c3e3c172be1515cc0d2df8e3d71"></a><!-- doxytag: member="ccapi_version_max" ref="gg06fc87d81c62e9abb8790b6e5713c55bc33d8c3e3c172be1515cc0d2df8e3d71" args="" -->ccapi_version_max</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="gdf764cbdea00d65edcd07bb9953ad2b7"></a><!-- doxytag: member="CredentialsCache.h::@1" ref="gdf764cbdea00d65edcd07bb9953ad2b7" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">anonymous enum          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Error codes <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26"></a><!-- doxytag: member="ccNoError" ref="ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26" args="" -->ccNoError</em>&nbsp;</td><td>
Success. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b748d5a55ed773e002ccc271beb4512c0a"></a><!-- doxytag: member="ccIteratorEnd" ref="ggdf764cbdea00d65edcd07bb9953ad2b748d5a55ed773e002ccc271beb4512c0a" args="" -->ccIteratorEnd</em>&nbsp;</td><td>
Iterator is done iterating. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b775735bfd0c621b1c5c2a0067f60cfa9d"></a><!-- doxytag: member="ccErrBadParam" ref="ggdf764cbdea00d65edcd07bb9953ad2b775735bfd0c621b1c5c2a0067f60cfa9d" args="" -->ccErrBadParam</em>&nbsp;</td><td>
Bad parameter (NULL or invalid pointer where valid pointer expected). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b74f31ee78c32ed1bea75d364ccbf1e690"></a><!-- doxytag: member="ccErrNoMem" ref="ggdf764cbdea00d65edcd07bb9953ad2b74f31ee78c32ed1bea75d364ccbf1e690" args="" -->ccErrNoMem</em>&nbsp;</td><td>
Not enough memory to complete the operation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7d9bf97d9c57a6a14ac5b6a7a06e008f7"></a><!-- doxytag: member="ccErrInvalidContext" ref="ggdf764cbdea00d65edcd07bb9953ad2b7d9bf97d9c57a6a14ac5b6a7a06e008f7" args="" -->ccErrInvalidContext</em>&nbsp;</td><td>
Context is invalid (e.g., it was released). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b708791e8ed735ecc02c1e6b222f913c71"></a><!-- doxytag: member="ccErrInvalidCCache" ref="ggdf764cbdea00d65edcd07bb9953ad2b708791e8ed735ecc02c1e6b222f913c71" args="" -->ccErrInvalidCCache</em>&nbsp;</td><td>
CCache is invalid (e.g., it was released or destroyed). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b742b40a71ad0c84171c8d982f6cfdeec9"></a><!-- doxytag: member="ccErrInvalidString" ref="ggdf764cbdea00d65edcd07bb9953ad2b742b40a71ad0c84171c8d982f6cfdeec9" args="" -->ccErrInvalidString</em>&nbsp;</td><td>
String is invalid (e.g., it was released). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b78705dd3f7b52ce8e23e9a25f552b4a84"></a><!-- doxytag: member="ccErrInvalidCredentials" ref="ggdf764cbdea00d65edcd07bb9953ad2b78705dd3f7b52ce8e23e9a25f552b4a84" args="" -->ccErrInvalidCredentials</em>&nbsp;</td><td>
Credentials are invalid (e.g., they were released), or they have a bad version. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7fbc118bc7860198fc6a0b8e4a161363a"></a><!-- doxytag: member="ccErrInvalidCCacheIterator" ref="ggdf764cbdea00d65edcd07bb9953ad2b7fbc118bc7860198fc6a0b8e4a161363a" args="" -->ccErrInvalidCCacheIterator</em>&nbsp;</td><td>
CCache iterator is invalid (e.g., it was released). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b751c0c94d8b4dfb834b27fa7090f7b4f2"></a><!-- doxytag: member="ccErrInvalidCredentialsIterator" ref="ggdf764cbdea00d65edcd07bb9953ad2b751c0c94d8b4dfb834b27fa7090f7b4f2" args="" -->ccErrInvalidCredentialsIterator</em>&nbsp;</td><td>
Credentials iterator is invalid (e.g., it was released). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b73e2425cf044691597d10bbc14b3c90ba"></a><!-- doxytag: member="ccErrInvalidLock" ref="ggdf764cbdea00d65edcd07bb9953ad2b73e2425cf044691597d10bbc14b3c90ba" args="" -->ccErrInvalidLock</em>&nbsp;</td><td>
Lock is invalid (e.g., it was released). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7b508b53d9628aa7e0fca3253d619e78e"></a><!-- doxytag: member="ccErrBadName" ref="ggdf764cbdea00d65edcd07bb9953ad2b7b508b53d9628aa7e0fca3253d619e78e" args="" -->ccErrBadName</em>&nbsp;</td><td>
Bad credential cache name format. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7d2f35f60002115819c8c443e67191ea4"></a><!-- doxytag: member="ccErrBadCredentialsVersion" ref="ggdf764cbdea00d65edcd07bb9953ad2b7d2f35f60002115819c8c443e67191ea4" args="" -->ccErrBadCredentialsVersion</em>&nbsp;</td><td>
Credentials version is invalid. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b759ae48c63ae4aaaa3cd7bc5504847660"></a><!-- doxytag: member="ccErrBadAPIVersion" ref="ggdf764cbdea00d65edcd07bb9953ad2b759ae48c63ae4aaaa3cd7bc5504847660" args="" -->ccErrBadAPIVersion</em>&nbsp;</td><td>
Unsupported API version. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b78df04862b2e05986499f0d93268fa7b8"></a><!-- doxytag: member="ccErrContextLocked" ref="ggdf764cbdea00d65edcd07bb9953ad2b78df04862b2e05986499f0d93268fa7b8" args="" -->ccErrContextLocked</em>&nbsp;</td><td>
Context is already locked. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b71606d0e0540ead205a400306e5933c04"></a><!-- doxytag: member="ccErrContextUnlocked" ref="ggdf764cbdea00d65edcd07bb9953ad2b71606d0e0540ead205a400306e5933c04" args="" -->ccErrContextUnlocked</em>&nbsp;</td><td>
Context is not locked by the caller. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7a453f3f6d7e578f47efbb1734cb46002"></a><!-- doxytag: member="ccErrCCacheLocked" ref="ggdf764cbdea00d65edcd07bb9953ad2b7a453f3f6d7e578f47efbb1734cb46002" args="" -->ccErrCCacheLocked</em>&nbsp;</td><td>
CCache is already locked. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7d3ebfa156acf72a3a86f14760d315e24"></a><!-- doxytag: member="ccErrCCacheUnlocked" ref="ggdf764cbdea00d65edcd07bb9953ad2b7d3ebfa156acf72a3a86f14760d315e24" args="" -->ccErrCCacheUnlocked</em>&nbsp;</td><td>
CCache is not locked by the caller. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7b0fa85b2c59d51c3ec205e40191e7619"></a><!-- doxytag: member="ccErrBadLockType" ref="ggdf764cbdea00d65edcd07bb9953ad2b7b0fa85b2c59d51c3ec205e40191e7619" args="" -->ccErrBadLockType</em>&nbsp;</td><td>
Bad lock type. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7604f23ab0c8c3e1d97f8b32c4501a895"></a><!-- doxytag: member="ccErrNeverDefault" ref="ggdf764cbdea00d65edcd07bb9953ad2b7604f23ab0c8c3e1d97f8b32c4501a895" args="" -->ccErrNeverDefault</em>&nbsp;</td><td>
CCache was never default. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b74c8c5082e236270c5dc55e998dfb9288"></a><!-- doxytag: member="ccErrCredentialsNotFound" ref="ggdf764cbdea00d65edcd07bb9953ad2b74c8c5082e236270c5dc55e998dfb9288" args="" -->ccErrCredentialsNotFound</em>&nbsp;</td><td>
Matching credentials not found in the ccache. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b73098feac66058e6ebd02c5e44fa20a9c"></a><!-- doxytag: member="ccErrCCacheNotFound" ref="ggdf764cbdea00d65edcd07bb9953ad2b73098feac66058e6ebd02c5e44fa20a9c" args="" -->ccErrCCacheNotFound</em>&nbsp;</td><td>
Matching ccache not found in the collection. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b793bd0c54010e2a36d2e2af1e8aaef06f"></a><!-- doxytag: member="ccErrContextNotFound" ref="ggdf764cbdea00d65edcd07bb9953ad2b793bd0c54010e2a36d2e2af1e8aaef06f" args="" -->ccErrContextNotFound</em>&nbsp;</td><td>
Matching cache collection not found. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7267b21d05e1f4005392c52e439de03a4"></a><!-- doxytag: member="ccErrServerUnavailable" ref="ggdf764cbdea00d65edcd07bb9953ad2b7267b21d05e1f4005392c52e439de03a4" args="" -->ccErrServerUnavailable</em>&nbsp;</td><td>
CCacheServer is unavailable. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7a18e1555d7af1a60ce1978069e95fe6f"></a><!-- doxytag: member="ccErrServerInsecure" ref="ggdf764cbdea00d65edcd07bb9953ad2b7a18e1555d7af1a60ce1978069e95fe6f" args="" -->ccErrServerInsecure</em>&nbsp;</td><td>
CCacheServer has detected that it is running as the wrong user. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7fd9fdcb22b761cb3e53e1d6d6b545884"></a><!-- doxytag: member="ccErrServerCantBecomeUID" ref="ggdf764cbdea00d65edcd07bb9953ad2b7fd9fdcb22b761cb3e53e1d6d6b545884" args="" -->ccErrServerCantBecomeUID</em>&nbsp;</td><td>
CCacheServer failed to start running as the user. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7d6825aa88394eb52df80bef870d986db"></a><!-- doxytag: member="ccErrTimeOffsetNotSet" ref="ggdf764cbdea00d65edcd07bb9953ad2b7d6825aa88394eb52df80bef870d986db" args="" -->ccErrTimeOffsetNotSet</em>&nbsp;</td><td>
KDC time offset not set for this ccache. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b75f8cef73e4ac4c8894c4318ef921350e"></a><!-- doxytag: member="ccErrBadInternalMessage" ref="ggdf764cbdea00d65edcd07bb9953ad2b75f8cef73e4ac4c8894c4318ef921350e" args="" -->ccErrBadInternalMessage</em>&nbsp;</td><td>
The client and CCacheServer can't communicate (e.g., a version mismatch). </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b7a35748a788a849ff09dd453ac66f8314"></a><!-- doxytag: member="ccErrNotImplemented" ref="ggdf764cbdea00d65edcd07bb9953ad2b7a35748a788a849ff09dd453ac66f8314" args="" -->ccErrNotImplemented</em>&nbsp;</td><td>
API function not supported by this implementation. </td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggdf764cbdea00d65edcd07bb9953ad2b79cab6af44e2358aa12f176a60e245d67"></a><!-- doxytag: member="ccErrClientNotFound" ref="ggdf764cbdea00d65edcd07bb9953ad2b79cab6af44e2358aa12f176a60e245d67" args="" -->ccErrClientNotFound</em>&nbsp;</td><td>
CCacheServer has no record of the caller's process (e.g., the server crashed). </td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="gae76da96fff95c157c3b28c4455dc35c"></a><!-- doxytag: member="CredentialsCache.h::cc_credential_versions" ref="gae76da96fff95c157c3b28c4455dc35c" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">enum <a class="el" href="group__ccapi__constants__reference.html#gae76da96fff95c157c3b28c4455dc35c">cc_credential_versions</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Credentials versions<p>
These constants are used in several places in the API to discern between Kerberos v4 and Kerberos v5. Not all values are valid inputs and outputs for all functions; function specifications below detail the allowed values.<p>
Kerberos version constants will always be a bit-field, and can be tested as such; for example the following test will tell you if a ccacheVersion includes v5 credentials:<p>
if ((ccacheVersion &amp; cc_credentials_v5) != 0) <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa"></a><!-- doxytag: member="cc_credentials_v4" ref="ggae76da96fff95c157c3b28c4455dc35c017c26531bad42f92f7f3e1f697b58fa" args="" -->cc_credentials_v4</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668"></a><!-- doxytag: member="cc_credentials_v5" ref="ggae76da96fff95c157c3b28c4455dc35c98335a31ad81a10632568375dcc10668" args="" -->cc_credentials_v5</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="ggae76da96fff95c157c3b28c4455dc35ca49ae6c35599f5860241601dcb0c9e0d"></a><!-- doxytag: member="cc_credentials_v4_v5" ref="ggae76da96fff95c157c3b28c4455dc35ca49ae6c35599f5860241601dcb0c9e0d" args="" -->cc_credentials_v4_v5</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="g2081cbacd4ec3d5a64c8729fd78fc611"></a><!-- doxytag: member="CredentialsCache.h::cc_lock_types" ref="g2081cbacd4ec3d5a64c8729fd78fc611" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">enum <a class="el" href="group__ccapi__constants__reference.html#g2081cbacd4ec3d5a64c8729fd78fc611">cc_lock_types</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Lock types<p>
These constants are used in the locking functions to describe the type of lock requested. Note that all CCAPI locks are advisory so only callers using the lock calls will be blocked by each other. This is because locking functions were introduced after the CCAPI came into common use and we did not want to break existing callers. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg2081cbacd4ec3d5a64c8729fd78fc6110a9a6083623e4c7c8340c0146f032154"></a><!-- doxytag: member="cc_lock_read" ref="gg2081cbacd4ec3d5a64c8729fd78fc6110a9a6083623e4c7c8340c0146f032154" args="" -->cc_lock_read</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2081cbacd4ec3d5a64c8729fd78fc611bc97eae3b375c4cee90087597b7f375d"></a><!-- doxytag: member="cc_lock_write" ref="gg2081cbacd4ec3d5a64c8729fd78fc611bc97eae3b375c4cee90087597b7f375d" args="" -->cc_lock_write</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2081cbacd4ec3d5a64c8729fd78fc61184bf8feb65a88dc09aa52c6d955111a3"></a><!-- doxytag: member="cc_lock_upgrade" ref="gg2081cbacd4ec3d5a64c8729fd78fc61184bf8feb65a88dc09aa52c6d955111a3" args="" -->cc_lock_upgrade</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg2081cbacd4ec3d5a64c8729fd78fc611426c331fb09e249b6944d8ba28893eba"></a><!-- doxytag: member="cc_lock_downgrade" ref="gg2081cbacd4ec3d5a64c8729fd78fc611426c331fb09e249b6944d8ba28893eba" args="" -->cc_lock_downgrade</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="g0eff5be22e263d0bd9e4bb6fb0a8e948"></a><!-- doxytag: member="CredentialsCache.h::cc_lock_modes" ref="g0eff5be22e263d0bd9e4bb6fb0a8e948" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">enum <a class="el" href="group__ccapi__constants__reference.html#g0eff5be22e263d0bd9e4bb6fb0a8e948">cc_lock_modes</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Locking Modes<p>
These constants are used in the advisory locking functions to describe whether or not the lock function should block waiting for a lock or return an error immediately. For example, attempting to acquire a lock with a non-blocking call will result in an error if the lock cannot be acquired; otherwise, the call will block until the lock can be acquired. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg0eff5be22e263d0bd9e4bb6fb0a8e9485fe1eddb0bba9df16dbcc63aeeb79aa3"></a><!-- doxytag: member="cc_lock_noblock" ref="gg0eff5be22e263d0bd9e4bb6fb0a8e9485fe1eddb0bba9df16dbcc63aeeb79aa3" args="" -->cc_lock_noblock</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg0eff5be22e263d0bd9e4bb6fb0a8e94827e385feb864d167880744d0874834d8"></a><!-- doxytag: member="cc_lock_block" ref="gg0eff5be22e263d0bd9e4bb6fb0a8e94827e385feb864d167880744d0874834d8" args="" -->cc_lock_block</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="g99fb83031ce9923c84392b4e92f956b5"></a><!-- doxytag: member="CredentialsCache.h::@2" ref="g99fb83031ce9923c84392b4e92f956b5" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">anonymous enum          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
Sizes of fields in <a class="el" href="structcc__credentials__v4__t.html">cc_credentials_v4_t</a>. <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gg99fb83031ce9923c84392b4e92f956b5ae3147869a486588adf6bc588ee8cc30"></a><!-- doxytag: member="cc_v4_name_size" ref="gg99fb83031ce9923c84392b4e92f956b5ae3147869a486588adf6bc588ee8cc30" args="" -->cc_v4_name_size</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg99fb83031ce9923c84392b4e92f956b55abe44162300eabb9a9f65b324cad493"></a><!-- doxytag: member="cc_v4_instance_size" ref="gg99fb83031ce9923c84392b4e92f956b55abe44162300eabb9a9f65b324cad493" args="" -->cc_v4_instance_size</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg99fb83031ce9923c84392b4e92f956b5714d28ea3d6c6807817d7b377afc22f8"></a><!-- doxytag: member="cc_v4_realm_size" ref="gg99fb83031ce9923c84392b4e92f956b5714d28ea3d6c6807817d7b377afc22f8" args="" -->cc_v4_realm_size</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg99fb83031ce9923c84392b4e92f956b5b52bd33d187632efd597f282540b081a"></a><!-- doxytag: member="cc_v4_ticket_size" ref="gg99fb83031ce9923c84392b4e92f956b5b52bd33d187632efd597f282540b081a" args="" -->cc_v4_ticket_size</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gg99fb83031ce9923c84392b4e92f956b57532f28276c3a759e487560ee4666a29"></a><!-- doxytag: member="cc_v4_key_size" ref="gg99fb83031ce9923c84392b4e92f956b57532f28276c3a759e487560ee4666a29" args="" -->cc_v4_key_size</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<a class="anchor" name="ge78c8fbb79f8ff963ec7c88c431721c3"></a><!-- doxytag: member="CredentialsCache.h::cc_string_to_key_type" ref="ge78c8fbb79f8ff963ec7c88c431721c3" args="" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top">enum <a class="el" href="group__ccapi__constants__reference.html#ge78c8fbb79f8ff963ec7c88c431721c3">cc_string_to_key_type</a>          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
String to key type (Kerberos v4 only) <dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="gge78c8fbb79f8ff963ec7c88c431721c3c0beda03d5c9e36ce9a199c98573d39a"></a><!-- doxytag: member="cc_v4_stk_afs" ref="gge78c8fbb79f8ff963ec7c88c431721c3c0beda03d5c9e36ce9a199c98573d39a" args="" -->cc_v4_stk_afs</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge78c8fbb79f8ff963ec7c88c431721c31b71e069604cd1cebc2b694ef04aedd1"></a><!-- doxytag: member="cc_v4_stk_des" ref="gge78c8fbb79f8ff963ec7c88c431721c31b71e069604cd1cebc2b694ef04aedd1" args="" -->cc_v4_stk_des</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge78c8fbb79f8ff963ec7c88c431721c3db66296923c38aa2d7c7420da28d7014"></a><!-- doxytag: member="cc_v4_stk_columbia_special" ref="gge78c8fbb79f8ff963ec7c88c431721c3db66296923c38aa2d7c7420da28d7014" args="" -->cc_v4_stk_columbia_special</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge78c8fbb79f8ff963ec7c88c431721c3816b6bb45f51557bc3de5e13d38dc310"></a><!-- doxytag: member="cc_v4_stk_krb5" ref="gge78c8fbb79f8ff963ec7c88c431721c3816b6bb45f51557bc3de5e13d38dc310" args="" -->cc_v4_stk_krb5</em>&nbsp;</td><td>
</td></tr>
<tr><td valign="top"><em><a class="anchor" name="gge78c8fbb79f8ff963ec7c88c431721c3e19e1a52a4289172671e91d87ab027bb"></a><!-- doxytag: member="cc_v4_stk_unknown" ref="gge78c8fbb79f8ff963ec7c88c431721c3e19e1a52a4289172671e91d87ab027bb" args="" -->cc_v4_stk_unknown</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:06 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
