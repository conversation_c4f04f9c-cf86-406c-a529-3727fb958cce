.\" Man page generated from reStructuredText.
.
.TH "K5IDENTITY" "5" " " "1.20" "MIT Kerberos"
.SH NAME
k5identity \- Kerberos V5 client principal selection rules
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH DESCRIPTION
.sp
The .k5identity file, which resides in a user\(aqs home directory,
contains a list of rules for selecting a client principals based on
the server being accessed.  These rules are used to choose a
credential cache within the cache collection when possible.
.sp
Blank lines and lines beginning with \fB#\fP are ignored.  Each line has
the form:
.INDENT 0.0
.INDENT 3.5
\fIprincipal\fP \fIfield\fP=\fIvalue\fP ...
.UNINDENT
.UNINDENT
.sp
If the server principal meets all of the field constraints, then
principal is chosen as the client principal.  The following fields are
recognized:
.INDENT 0.0
.TP
\fBrealm\fP
If the realm of the server principal is known, it is matched
against \fIvalue\fP, which may be a pattern using shell wildcards.
For host\-based server principals, the realm will generally only be
known if there is a domain_realm section in
krb5.conf(5) with a mapping for the hostname.
.TP
\fBservice\fP
If the server principal is a host\-based principal, its service
component is matched against \fIvalue\fP, which may be a pattern using
shell wildcards.
.TP
\fBhost\fP
If the server principal is a host\-based principal, its hostname
component is converted to lower case and matched against \fIvalue\fP,
which may be a pattern using shell wildcards.
.sp
If the server principal matches the constraints of multiple lines
in the .k5identity file, the principal from the first matching
line is used.  If no line matches, credentials will be selected
some other way, such as the realm heuristic or the current primary
cache.
.UNINDENT
.SH EXAMPLE
.sp
The following example .k5identity file selects the client principal
\<EMAIL>\fP if the server principal is within that realm,
the principal \fBalice/<EMAIL>\fP if the server host is within
a servers subdomain, and the principal \fBalice/<EMAIL>\fP when
accessing the IMAP service on \fBmail.example.com\fP:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
<EMAIL>       realm=KRBTEST.COM
alice/<EMAIL>  host=*.servers.example.com
alice/<EMAIL>  host=mail.example.com service=imap
.ft P
.fi
.UNINDENT
.UNINDENT
.SH SEE ALSO
.sp
kerberos(1), krb5.conf(5)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
