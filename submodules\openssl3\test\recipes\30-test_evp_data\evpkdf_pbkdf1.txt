#
# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = PBKDF1 tests

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:md2
Output = 2C5DAEBD49984F34642ACC09BAD696D7

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:md5
Output = FDBDF3419FFF98BDB0241390F62A9DB3

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha1
Output = CAB86DD6261710891E8CB56EE3625691

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:2
Ctrl.digest = digest:md2
Output = FD7999A1AB54B01B4FC39389A5FE820D

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:2
Ctrl.digest = digest:md5
Output = 3D4A8D4FB4C6E8686B21D36142902966

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:2
Ctrl.digest = digest:sha1
Output = E3A8DFCF2EEA6DC81D2AD154274FAAE9

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:md2
Output = 94E4671F438BD6C441C5B120C6CC79CA

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:md5
Output = 3283ED8F8D037045157DA055BFF84A02

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:password
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:4096
Ctrl.digest = digest:sha1
Output = 3CB0C21E81127F5BFF2EEA2B5DC3F31D

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALT
Ctrl.iter = iter:65537
Ctrl.digest = digest:md2
Output = 36DAA8DEB8B471B26AA8CE064A81E54F

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALT
Ctrl.iter = iter:65537
Ctrl.digest = digest:md5
Output = 763F3BA457E3F9ED088B04B5361D7CCA

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:passwordPASSWORDpassword
Ctrl.salt = salt:saltSALT
Ctrl.iter = iter:65537
Ctrl.digest = digest:sha1
Output = B2B4635718AAAD9FEF23FE328EB83ECF

Title = PBKDF1 tests for empty inputs

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:md2
Output = 8ECD1C4C1D57C415295784CCD4686905

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:md5
Output = F3D07DE5EFB5E2C3EAFC16B0CF7E07FA

Availablein = legacy
KDF = PBKDF1
Ctrl.pass = pass:
Ctrl.salt = salt:saltsalt
Ctrl.iter = iter:1
Ctrl.digest = digest:sha1
Output = 2C2ABACE4BD8BB19F67113DA146DBB8C
