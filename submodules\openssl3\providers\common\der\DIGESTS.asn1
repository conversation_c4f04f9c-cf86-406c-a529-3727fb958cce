-- Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
--
-- Licensed under the Apache License 2.0 (the "License").  You may not use
-- this file except in compliance with the License.  You can obtain a copy
-- in the file LICENSE in the source distribution or at
-- https://www.openssl.org/source/license.html

-- -------------------------------------------------------------------
-- From https://tools.ietf.org/html/rfc4055#section-2.1

id-sha1  OBJECT IDENTIFIER  ::=  { iso(1)
                     identified-organization(3) oiw(14)
                     secsig(3) algorithms(2) 26 }

-- -------------------------------------------------------------------
-- From https://tools.ietf.org/html/rfc5480#appendix-A
-- (OIDs for MD2 and MD5 are allowed only in EMSA-PKCS1-v1_5)

id-md2  OBJECT IDENTIFIER ::= {
  iso(1) member-body(2) us(840) rsadsi(113549) digestAlgorithm(2) 2 }

id-md5  OBJECT IDENTIFIER ::= {
  iso(1) member-body(2) us(840) rsadsi(113549) digestAlgorithm(2) 5 }

-- -------------------------------------------------------------------
-- From https://csrc.nist.gov/projects/computer-security-objects-register/algorithm-registration

id-sha256 OBJECT IDENTIFIER ::= { hashAlgs 1 }
id-sha384 OBJECT IDENTIFIER ::= { hashAlgs 2 }
id-sha512 OBJECT IDENTIFIER ::= { hashAlgs 3 }
id-sha224 OBJECT IDENTIFIER ::= { hashAlgs 4 }
id-sha512-224 OBJECT IDENTIFIER ::= { hashAlgs 5 }
id-sha512-256 OBJECT IDENTIFIER ::= { hashAlgs 6 }
id-sha3-224 OBJECT IDENTIFIER ::= { hashAlgs 7 }
id-sha3-256 OBJECT IDENTIFIER ::= { hashAlgs 8 }
id-sha3-384 OBJECT IDENTIFIER ::= { hashAlgs 9 }
id-sha3-512 OBJECT IDENTIFIER ::= { hashAlgs 10 }
id-shake128 OBJECT IDENTIFIER ::= { hashAlgs 11 }
id-shake256 OBJECT IDENTIFIER ::= { hashAlgs 12 }
id-shake128-len OBJECT IDENTIFIER ::= { hashAlgs 17 }
id-shake256-len OBJECT IDENTIFIER ::= { hashAlgs 18 }
id-KMACWithSHAKE128 OBJECT IDENTIFIER ::={hashAlgs 19}
id-KMACWithSHAKE256 OBJECT IDENTIFIER ::={ hashAlgs 20}
