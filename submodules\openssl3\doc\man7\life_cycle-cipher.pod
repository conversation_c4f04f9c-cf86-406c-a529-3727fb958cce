=pod

=head1 NAME

life_cycle-cipher - The cipher algorithm life-cycle

=head1 DESCRIPTION

All symmetric ciphers (CIPHERs) go through a number of stages in their
life-cycle:

=over 4

=item start

This state represents the CIPHER before it has been allocated.  It is the
starting state for any life-cycle transitions.

=item newed

This state represents the CIPHER after it has been allocated.

=item initialised

These states represent the CIPHER when it is set up and capable of processing
input.  There are three possible initialised states:

=over 4

=item initialised using EVP_CipherInit

=item initialised for decryption using EVP_DecryptInit

=item initialised for encryption using EVP_EncryptInit

=back

=item updated

These states represent the CIPHER when it is set up and capable of processing
additional input or generating output.  The three possible states directly
correspond to those for initialised above.  The three different streams should
not be mixed.

=item finaled

This state represents the CIPHER when it has generated output.

=item freed

This state is entered when the CIPHER is freed.  It is the terminal state
for all life-cycle transitions.

=back

=head2 State Transition Diagram

The usual life-cycle of a CIPHER is illustrated:

=begin man

                                 +---------------------------+
                                 |                           |
                                 |           start           |
                                 |                           |
                                 +---------------------------+   + - - - - - - - - - - - - - +
                                       |                         '  any of the initialised   '
                                       | EVP_CIPHER_CTX_new      ' updated or finaled states '
                                       v                         '                           '
                                 +---------------------------+   + - - - - - - - - - - - - - +
                                 |                           |      |
                                 |           newed           |      | EVP_CIPHER_CTX_reset
                                 |                           | <----+
                                 +---------------------------+
                                    |   |                 |
                          +---------+   |                 +---------+
          EVP_DecryptInit |             | EVP_CipherInit            | EVP_EncryptInit
                          v             v                           v
 +---------------------------+   +---------------------------+   +---------------------------+
 |                           |   |                           |   |                           |
 |        initialised        |   |        initialised        |   |        initialised        |
 |       for decryption      |   |                           |   |       for encryption      |
 +---------------------------+   +---------------------------+   +---------------------------+
   |                                   |                                                   |
   | EVP_DecryptUpdate                 | EVP_CipherUpdate                EVP_EncryptUpdate |
   |                                   v                                                   |
   |                             +---------------------------+                             |
   |                             |                           |--------------------+        |
   |                             |          updated          |   EVP_CipherUpdate |        |
   |                             |                           | <------------------+        |
   v                             +---------------------------+                             v
 +---------------------------+                         |         +---------------------------+
 |                           |---------------------+   |         |                           |
 |          updated          |   EVP_DecryptUpdate |   |         |          updated          |------+
 |       for decryption      | <-------------------+   |         |       for encryption      |      |
 +---------------------------+                         |         +---------------------------+      |
                          |            EVP_CipherFinal |            |           ^                   |
                          +-------+                    |   +--------+           |                   |
                 EVP_DecryptFinal |                    |   | EVP_EncryptFinal   +-------------------+
                                  v                    v   v                      EVP_EncryptUpdate
                                 +---------------------------+
                                 |                           |-----------------------------+
                                 |          finaled          |                             |
                                 |                           | <---------------------------+
                                 +---------------------------+   EVP_CIPHER_CTX_get_params
                                       |                             (AEAD encryption)
                                       | EVP_CIPHER_CTX_free
                                       v
                                 +---------------------------+
                                 |                           |
                                 |           freed           |
                                 |                           |
                                 +---------------------------+

=end man

=for html <img src="img/cipher.png">

=head2 Formal State Transitions

This section defines all of the legal state transitions.
This is the canonical list.

=begin man

 Function Call                ---------------------------------------------- Current State -----------------------------------------------
                              start   newed    initialised   updated     finaled   initialised   updated    initialised   updated    freed
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_new           newed
 EVP_CipherInit                    initialised initialised initialised initialised initialised initialised  initialised initialised
 EVP_DecryptInit                   initialised initialised initialised initialised initialised initialised  initialised initialised
                                    decryption  decryption  decryption  decryption  decryption  decryption  decryption  decryption
 EVP_EncryptInit                   initialised initialised initialised initialised initialised initialised  initialised initialised
                                    encryption  encryption  encryption  encryption  encryption  encryption  encryption  encryption
 EVP_CipherUpdate                                updated     updated
 EVP_DecryptUpdate                                                                   updated     updated
                                                                                    decryption  decryption
 EVP_EncryptUpdate                                                                                            updated     updated
                                                                                                             encryption  encryption
 EVP_CipherFinal                                             finaled
 EVP_DecryptFinal                                                                                finaled
 EVP_EncryptFinal                                                                                                         finaled
 EVP_CIPHER_CTX_free          freed   freed       freed       freed       freed       freed       freed        freed       freed
 EVP_CIPHER_CTX_reset                 newed       newed       newed       newed       newed       newed        newed       newed
 EVP_CIPHER_CTX_get_params            newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_set_params            newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_gettable_params       newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_settable_params       newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption

=end man

=begin html

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="10">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">initialised</th>
    <th style="border:1px solid" align="center">updated</th>
    <th style="border:1px solid" align="center">finaled</th>
    <th style="border:1px solid" align="center">initialised<br>decryption</th>
    <th style="border:1px solid" align="center">updated<br>decryption</th>
    <th style="border:1px solid" align="center">initialised<br>encryption</th>
    <th style="border:1px solid" align="center">updated<br>encryption</th>
    <th style="border:1px solid" align="center">freed</th></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CipherInit</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DecryptInit</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_EncryptInit</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CipherUpdate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DecryptUpdate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_EncryptUpdate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CipherFinal</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">finaled</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DecryptFinal</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">finaled<br>decryption</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_EncryptFinal</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">finaled<br>decryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_reset</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_CIPHER_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised<br>decryption</td>
    <td style="border:1px solid" align="center">updated<br>decryption</td>
    <td style="border:1px solid" align="center">initialised<br>encryption</td>
    <td style="border:1px solid" align="center">updated<br>encryption</td>
    <td style="border:1px solid" align="center"></td></tr>
</table>

=end html

=head1 NOTES

At some point the EVP layer will begin enforcing the transitions described
herein.

=head1 SEE ALSO

L<provider-cipher(7)>, L<EVP_EncryptInit(3)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
