UNIX Application Servers
========================

An application server is a host that provides one or more services
over the network.  Application servers can be "secure" or "insecure."
A "secure" host is set up to require authentication from every client
connecting to it.  An "insecure" host will still provide Kerberos
authentication, but will also allow unauthenticated clients to
connect.

If you have Kerberos V5 installed on all of your client machines, MIT
recommends that you make your hosts secure, to take advantage of the
security that Kerberos authentication affords.  However, if you have
some clients that do not have Kerberos V5 installed, you can run an
insecure server, and still take advantage of Kerberos V5's single
sign-on capability.


.. _keytab_file:

The keytab file
---------------

All Kerberos server machines need a keytab file to authenticate to the
KDC.  By default on UNIX-like systems this file is named |keytab|.
The keytab file is an local copy of the host's key.  The keytab file
is a potential point of entry for a break-in, and if compromised,
would allow unrestricted access to its host.  The keytab file should
be readable only by root, and should exist only on the machine's local
disk.  The file should not be part of any backup of the machine,
unless access to the backup data is secured as tightly as access to
the machine's root password.

In order to generate a keytab for a host, the host must have a
principal in the Kerberos database.  The procedure for adding hosts to
the database is described fully in :ref:`add_mod_del_princs`.  (See
:ref:`replica_host_key` for a brief description.)  The keytab is
generated by running :ref:`kadmin(1)` and issuing the :ref:`ktadd`
command.

For example, to generate a keytab file to allow the host
``trillium.mit.edu`` to authenticate for the services host, ftp, and
pop, the administrator ``joeadmin`` would issue the command (on
``trillium.mit.edu``)::

    trillium% kadmin
    kadmin5: ktadd host/trillium.mit.edu ftp/trillium.mit.edu
        pop/trillium.mit.edu
    kadmin: Entry for principal host/<EMAIL> with
        kvno 3, encryption type DES-CBC-CRC added to keytab
        FILE:/etc/krb5.keytab.
    kadmin: Entry for principal ftp/<EMAIL> with
        kvno 3, encryption type DES-CBC-CRC added to keytab
        FILE:/etc/krb5.keytab.
    kadmin: Entry for principal pop/<EMAIL> with
        kvno 3, encryption type DES-CBC-CRC added to keytab
        FILE:/etc/krb5.keytab.
    kadmin5: quit
    trillium%

If you generate the keytab file on another host, you need to get a
copy of the keytab file onto the destination host (``trillium``, in
the above example) without sending it unencrypted over the network.


Some advice about secure hosts
------------------------------

Kerberos V5 can protect your host from certain types of break-ins, but
it is possible to install Kerberos V5 and still leave your host
vulnerable to attack.  Obviously an installation guide is not the
place to try to include an exhaustive list of countermeasures for
every possible attack, but it is worth noting some of the larger holes
and how to close them.

We recommend that backups of secure machines exclude the keytab file
(|keytab|).  If this is not possible, the backups should at least be
done locally, rather than over a network, and the backup tapes should
be physically secured.

The keytab file and any programs run by root, including the Kerberos
V5 binaries, should be kept on local disk.  The keytab file should be
readable only by root.
