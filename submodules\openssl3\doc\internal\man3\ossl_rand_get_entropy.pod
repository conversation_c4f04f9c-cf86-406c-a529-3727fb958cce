=pod

=head1 NAME

ossl_rand_get_entropy, ossl_rand_get_user_entropy,
ossl_rand_cleanup_entropy, ossl_rand_cleanup_user_entropy,
ossl_rand_get_nonce, ossl_rand_get_user_nonce,
ossl_rand_cleanup_nonce, ossl_rand_cleanup_user_nonce
- get seed material from the operating system

=head1 SYNOPSIS

 #include "crypto/rand.h"

 size_t ossl_rand_get_entropy(OSSL_CORE_HANDLE *handle,
                              unsigned char **pout, int entropy,
                              size_t min_len, size_t max_len);
 size_t ossl_rand_get_user_entropy(OSSL_CORE_HANDLE *handle,
                                   unsigned char **pout, int entropy,
                                   size_t min_len, size_t max_len);
 void ossl_rand_cleanup_entropy(OSSL_CORE_HANDLE *handle,
                                unsigned char *buf, size_t len);
 void ossl_rand_cleanup_user_entropy(OSSL_CORE_HANDLE *handle,
                                     unsigned char *buf, size_t len);
 size_t ossl_rand_get_nonce(OSSL_CORE_HANDLE *handle,
                            unsigned char **pout, size_t min_len,
                            size_t max_len, const void *salt, size_t salt_len);
 size_t ossl_rand_get_user_nonce(OSSL_CORE_HANDLE *handle, unsigned char **pout,
                                 size_t min_len, size_t max_len,
                                 const void *salt, size_t salt_len);
 void ossl_rand_cleanup_nonce(OSSL_CORE_HANDLE *handle,
                              unsigned char *buf, size_t len);
 void ossl_rand_cleanup_user_nonce(OSSL_CORE_HANDLE *handle,
                                   unsigned char *buf, size_t len);

=head1 DESCRIPTION

ossl_rand_get_entropy() retrieves seeding material from the operating system.
The seeding material will have at least I<entropy> bytes of randomness and is
stored in a buffer which contains at least I<min_len> and at most I<max_len>
bytes.  The buffer address is stored in I<*pout> and the buffer length is
returned to the caller.

ossl_rand_get_user_entropy() is the same as ossl_rand_get_entropy()
except that it retrieves the seeding material from the library context's
DRBG seed source.  By default this is the operating system but it can
be changed by calling L<RAND_set_seed_source_type(3)>.

ossl_rand_cleanup_entropy() cleanses and frees any storage allocated by
ossl_rand_get_entropy().  The entropy buffer is pointed to by I<buf>
and is of length I<len> bytes.

ossl_rand_cleanup_user_entropy() cleanses and frees any storage allocated by
ossl_rand_get_user_entropy().  The entropy buffer is pointed to by I<buf>
and is of length I<len> bytes.

ossl_rand_get_nonce() retrieves a nonce using the passed I<salt> parameter
of length I<salt_len> and operating system specific information.
The I<salt> should contain uniquely identifying information and this is
included, in an unspecified manner, as part of the output.
The output is stored in a buffer which contains at least I<min_len> and at
most I<max_len> bytes.  The buffer address is stored in I<*pout> and the
buffer length returned to the caller.

ossl_rand_get_user_nonce() is the same as ossl_rand_get_nonce() except
that it retrieves the seeding material from the library context's DRBG
seed source.  By default this is the operating system but it can be
changed by calling L<RAND_set_seed_source_type(3)>.

ossl_rand_cleanup_nonce() cleanses and frees any storage allocated by
ossl_rand_get_nonce() or ossl_rand_get_user_nonce().  The nonce buffer
is pointed to by I<buf> and is of length I<len> bytes.

=head1 NOTES

FIPS providers 3.0.0, 3.0.8 and 3.0.9 incorrectly pass a provider
internal pointer to ossl_rand_get_entropy(), ossl_rand_cleanup_entropy(),
ossl_rand_get_nonce() and ossl_rand_cleanup_nonce().  This pointer cannot
be safely dereferenced.

=head1 RETURN VALUES

ossl_rand_get_entropy(), ossl_rand_get_user_entropy(),
ossl_rand_get_nonce() and ossl_rand_get_user_nonce() return the number
of bytes in I<*pout> or 0 on error.

=head1 HISTORY

The functions ossl_rand_get_user_entropy(), ossl_rand_get_user_nonce(),
ossl_rand_cleanup_user_entropy(), and ossl_rand_cleanup_user_nonce()
were added in OpenSSL 3.1.4 and 3.2.0.

The remaining functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
