.\" Man page generated from reStructuredText.
.
.TH "KSWITCH" "1" " " "1.20" "MIT Kerberos"
.SH NAME
kswitch \- switch primary ticket cache
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkswitch\fP
{\fB\-c\fP \fIcachename\fP|\fB\-p\fP \fIprincipal\fP}
.SH DESCRIPTION
.sp
kswitch makes the specified credential cache the primary cache for the
collection, if a cache collection is available.
.SH OPTIONS
.INDENT 0.0
.TP
\fB\-c\fP \fIcachename\fP
Directly specifies the credential cache to be made primary.
.TP
\fB\-p\fP \fIprincipal\fP
Causes the cache collection to be searched for a cache containing
credentials for \fIprincipal\fP\&.  If one is found, that collection is
made primary.
.UNINDENT
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH FILES
.INDENT 0.0
.TP
.B \fB@CCNAME@\fP
Default location of Kerberos 5 credentials cache
.UNINDENT
.SH SEE ALSO
.sp
kinit(1), kdestroy(1), klist(1),
kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
