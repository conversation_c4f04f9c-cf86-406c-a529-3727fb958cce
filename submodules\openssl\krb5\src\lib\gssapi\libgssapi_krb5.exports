GSS_C_ATTR_LOCAL_LOGIN_USER
GSS_C_INQ_SSPI_SESSION_KEY
GSS_C_NT_ANONYMOUS
GSS_C_NT_COMPOSITE_EXPORT
GSS_C_NT_EXPORT_NAME
GSS_C_NT_HOSTBASED_SERVICE
GSS_C_NT_HOSTBASED_SERVICE_X
GSS_C_NT_MACHINE_UID_NAME
GSS_C_NT_STRING_UID_NAME
GSS_C_NT_USER_NAME
GSS_KRB5_NT_PRINCIPAL_NAME
GSS_KRB5_NT_ENTERPRISE_NAME
GSS_KRB5_CRED_NO_CI_FLAGS_X
GSS_KRB5_GET_CRED_IMPERSONATOR
GSS_C_MA_MECH_CONCRETE
GSS_C_MA_MECH_PSEUDO
GSS_C_MA_MECH_COMPOSITE
GSS_C_MA_MECH_NEGO
GSS_C_MA_MECH_GLUE
GSS_C_MA_NOT_MECH
GSS_C_MA_DEPRECATED
GSS_C_MA_NOT_DFLT_MECH
GSS_C_MA_ITOK_FRAMED
GSS_C_MA_AUTH_INIT
GSS_C_MA_AUTH_TARG
GSS_C_MA_AUTH_INIT_INIT
GSS_C_MA_AUTH_TARG_INIT
GSS_C_MA_AUTH_INIT_ANON
GSS_C_MA_AUTH_TARG_ANON
GSS_C_MA_DELEG_CRED
GSS_C_MA_INTEG_PROT
GSS_C_MA_CONF_PROT
GSS_C_MA_MIC
GSS_C_MA_WRAP
GSS_C_MA_PROT_READY
GSS_C_MA_REPLAY_DET
GSS_C_MA_OOS_DET
GSS_C_MA_CBINDINGS
GSS_C_MA_PFS
GSS_C_MA_COMPRESS
GSS_C_MA_CTX_TRANS
GSS_C_SEC_CONTEXT_SASL_SSF
gss_accept_sec_context
gss_acquire_cred
gss_acquire_cred_with_password
gss_acquire_cred_impersonate_name
gss_add_buffer_set_member
gss_add_cred
gss_add_cred_impersonate_name
gss_add_cred_with_password
gss_add_oid_set_member
gss_authorize_localname
gss_canonicalize_name
gss_compare_name
gss_complete_auth_token
gss_context_time
gss_create_empty_buffer_set
gss_create_empty_oid_set
gss_decapsulate_token
gss_delete_name_attribute
gss_delete_sec_context
gss_display_mech_attr
gss_display_name
gss_display_name_ext
gss_display_status
gss_duplicate_name
gss_encapsulate_token
gss_export_cred
gss_export_name
gss_export_name_composite
gss_export_sec_context
gss_get_mic
gss_get_mic_iov
gss_get_mic_iov_length
gss_get_name_attribute
gss_import_cred
gss_import_name
gss_import_sec_context
gss_indicate_mechs
gss_init_sec_context
gss_indicate_mechs_by_attrs
gss_inquire_attrs_for_mech
gss_inquire_context
gss_inquire_cred
gss_inquire_cred_by_mech
gss_inquire_cred_by_oid
gss_inquire_mech_for_saslname
gss_inquire_mechs_for_name
gss_inquire_names_for_mech
gss_inquire_saslname_for_mech
gss_inquire_sec_context_by_oid
gss_krb5_ccache_name
gss_krb5_copy_ccache
gss_krb5_export_lucid_sec_context
gss_krb5_free_lucid_sec_context
gss_krb5_get_tkt_flags
gss_krb5_import_cred
gss_krb5_set_allowable_enctypes
gss_krb5_set_cred_rcache
gss_krb5int_make_seal_token_v3
gss_krb5int_unseal_token_v3
gsskrb5_extract_authtime_from_sec_context
gsskrb5_extract_authz_data_from_sec_context
gss_localname
gss_map_name_to_any
gss_mech_iakerb
gss_mech_krb5
gss_mech_krb5_old
gss_mech_krb5_wrong
gss_mech_set_krb5
gss_mech_set_krb5_both
gss_mech_set_krb5_old
gss_nt_exported_name
gss_nt_krb5_name
gss_nt_krb5_principal
gss_nt_machine_uid_name
gss_nt_service_name
gss_nt_service_name_v2
gss_nt_string_uid_name
gss_nt_user_name
gss_oid_equal
gss_oid_to_str
gss_pname_to_uid
gss_pseudo_random
gss_process_context_token
gss_release_any_name_mapping
gss_release_buffer_set
gss_release_buffer
gss_release_cred
gss_release_iov_buffer
gss_release_name
gss_release_oid
gss_release_oid_set
gss_seal
gss_set_name_attribute
gss_set_neg_mechs
gss_set_sec_context_option
gss_sign
gss_store_cred
gss_str_to_oid
gss_test_oid_set_member
gss_unseal
gss_unwrap
gss_unwrap_aead
gss_unwrap_iov
gss_userok
gss_verify
gss_verify_mic
gss_verify_mic_iov
gss_wrap
gss_wrap_aead
gss_wrap_iov
gss_wrap_iov_length
gss_wrap_size_limit
gss_set_cred_option
gssspi_set_cred_option
gssspi_mech_invoke
krb5_gss_dbg_client_expcreds
krb5_gss_register_acceptor_identity
krb5_gss_use_kdc_context
gss_inquire_name
gss_acquire_cred_from
gss_add_cred_from
gss_store_cred_into
gssint_g_seqstate_init
