
try:
    import unittest2 as unittest
except ImportError:
    import unittest

try:
    import mock
    from mock import call
except ImportError:
    import unittest.mock as mock
    from unittest.mock import call

from tlslite.utils.python_key import Python_Key
from tlslite.utils.python_rsakey import Python_RSAK<PERSON>
from tlslite.utils.python_ecdsakey import Python_ECDSAKey
from tlslite.utils.python_dsakey import Python_DSAKey

class TestKey(unittest.TestCase):
    def test_rsa_key(self):
        key = (
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

        parsed_key = Python_Key.parsePEM(key)

        self.assertIsInstance(parsed_key, Python_RSAKey)

        exp_n = int("29060443439214279856616714317441381282994349643640084870"
                    "42194472422505198384747878467307665661184232728624861572"
                    "46118030874616185167217887082030330066913757629456433183"
                    "57727014263595982166729996386221650476766003639153689499"
                    "85761113451052281630236293941677142748838601564606627814"
                    "78871504321887555454323655057925411605057705083616507918"
                    "02130319371355483088627276339169052633563469569700890323"
                    "45345689545843561543977465544801728579255200638380126710"
                    "78271693450544506178122783381759966742683127796190767251"
                    "31801425088592558384516012482302720815493207137857605058"
                    "06980478584101642143302393556465736571436454903701271051"
                    "7")

        self.assertEqual(parsed_key.n, exp_n)

    def test_ecdsa_key_pkcs8(self):
        key = (
*********************************************************************************************************************************************************************************************************************************************************************************************************************************

        parsed_key = Python_Key.parsePEM(key)

        self.assertIsInstance(parsed_key, Python_ECDSAKey)
        self.assertEqual(parsed_key.private_key.privkey.secret_multiplier,
                         int("40256217329389834316473379676481509423"
                             "54978248437138490984956489316429083942"))
        self.assertIsNotNone(parsed_key.public_key)

    def test_ecdsa_key_ssleay(self):
        key = (
*******************************************************************************************************************************************************************************************************************************************************************************************************************

        parsed_key = Python_Key.parsePEM(key)

        self.assertIsInstance(parsed_key, Python_ECDSAKey)
        self.assertEqual(parsed_key.private_key.privkey.secret_multiplier,
                         int("40256217329389834316473379676481509423"
                             "54978248437138490984956489316429083942"))
        self.assertIsNotNone(parsed_key.public_key)

    def test_ecdsa_p224(self):
        key = (
*************************************************************************************************************************************************************************************************************************************************************************************************************

        # secp224r1 is not supported by tlslite-ng
        with self.assertRaises(SyntaxError) as e:
            Python_Key.parsePEM(key)

        self.assertIn("Unknown curve", str(e.exception))

    def test_ecdsa_p384(self):
        key = (
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

        parsed_key = Python_Key.parsePEM(key)

        self.assertIsInstance(parsed_key, Python_ECDSAKey)
        self.assertEqual(len(parsed_key), 384)

    def test_ecdsa_p521(self):
        key = (
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

        parsed_key = Python_Key.parsePEM(key)

        self.assertIsInstance(parsed_key, Python_ECDSAKey)
        self.assertEqual(len(parsed_key), 521)

    def test_dsa_key_pkcs8(self):
        key_PKCS8 = (
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        parsed_key = Python_Key.parsePEM(key_PKCS8)
        self.assertIsInstance(parsed_key, Python_DSAKey)
        self.assertTrue(parsed_key.hasPrivateKey())
        self.assertEqual(parsed_key.private_key,    \
                54605271259585079176392566431938393409383029096)
