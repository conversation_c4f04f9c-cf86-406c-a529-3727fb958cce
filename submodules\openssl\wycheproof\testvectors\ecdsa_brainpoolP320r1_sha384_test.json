{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 393, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "040fcc8860cb26e262ca8b4ecb9c52f78d82a10a1d30dd0c8ecd7584ce80dbb75c488a062b643755001f27e676c26cd3488c1ef4ec3edd88cf8af78daf9036724b57e66da02cf7c676a53664becdfedc3b", "wx": "0fcc8860cb26e262ca8b4ecb9c52f78d82a10a1d30dd0c8ecd7584ce80dbb75c488a062b64375500", "wy": "1f27e676c26cd3488c1ef4ec3edd88cf8af78daf9036724b57e66da02cf7c676a53664becdfedc3b"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200040fcc8860cb26e262ca8b4ecb9c52f78d82a10a1d30dd0c8ecd7584ce80dbb75c488a062b643755001f27e676c26cd3488c1ef4ec3edd88cf8af78daf9036724b57e66da02cf7c676a53664becdfedc3b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABA/MiGDLJuJiyotOy5xS942CoQod\nMN0Mjs11hM6A27dcSIoGK2Q3VQAfJ+Z2wmzTSIwe9Ow+3YjPiveNr5A2cktX5m2g\nLPfGdqU2ZL7N/tw7\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022900833d6661b0576d61a80ffe4d3271c43b2a56c14b3bd90305923ccdcf7b3d988c07ebb1c4cc67381c", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "3054022885b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30815502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082005502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "305402290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000005502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000005502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "305502800085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02805020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "3057000002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "305a498177305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "30592500305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "3057305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "305a222e49817702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "3059222d250002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "305d222b02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0004deadbeef02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "305a02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e222d49817702285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e222c250002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "305d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e222a02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "305daa00bb00cd00305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "305baa02aabb305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "305d2231aa00bb00cd0002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "305b222faa02aabb02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "305d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e2230aa00bb00cd0002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "305b02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e222eaa02aabb02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3059228002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e000002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e228002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080315502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3059228003290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e000002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e228003285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e5502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f5502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "315502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "325502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff5502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "30593001023054290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "305402290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5a", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "3054290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af500", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af505000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "308002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "3057300002290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af53000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "305802290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5bf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3057305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "302b02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "307f02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af502285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30560281290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0281285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3057028200290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e028200285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3055022a0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "305502280085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02295020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02275020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "305a028501000000290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "305a02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e028501000000285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "305e02890100000000000000290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "305e02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02890100000000000000285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "305902847fffffff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02847fffffff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30590284ffffffff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0284ffffffff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "305a0285ffffffffff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "305a02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0285ffffffffff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "305d0288ffffffffffffffff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "305d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0288ffffffffffffffff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "305502ff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02ff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "302a02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302b0202285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302c02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3057022b0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e000002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022a5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3057022b00000085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022a00005020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e000002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3057022b0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e050002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "305702290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022a5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af50500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "302c028102285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "302d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "302c050002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "302d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305500290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305501290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305503290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305504290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3055ff290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e00285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e01285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e03285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e04285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6eff285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "302c020002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "302d02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3059222d020100022885b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "305902290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e222c020150022720e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "305502290285b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285220e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34aee02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5a75", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "305402280085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "305402290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02275020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5a", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "305402290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022720e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "3056022aff0085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0229ff5020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "302d09018002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "302e02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "302d02010002285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "302e02290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305502290159100378a2b190377dcb3bd531e20c378d106931fc183f707dc9d08576f8fb566185594220b8dd7f02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30540228b25375383538f0c7bb524b178dde4b6b99f0c9e68efa1a2a233972f599ec49835462ae8b972db75d02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30550229ff7a4e43a7940abf8063713c89a01fd42e6c7f6673ba76d332af7e5e42778d5d93250bfc19240cb59202285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305402284dac8ac7cac70f3844adb4e87221b494660f36197105e5d5dcc68d0a6613b67cab9d517468d248a302285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30550229fea6effc875d4e6fc88234c42ace1df3c872ef96ce03e7c08f82362f7a890704a99e7aa6bddf47228102285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305502290185b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e02285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305402287a4e43a7940abf8063713c89a01fd42e6c7f6673ba76d332af7e5e42778d5d93250bfc19240cb59202285020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022901237f27debd21320e1a68f2707191fc90c8c8de0031452240c8538fc061cf19470536f8f1bd23ee06", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0229ff7cc2999e4fa8929e57f001b2cd8e3bc4d5a93eb4c426fcfa6dc3323084c26773f8144e3b3398c7e4", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305502290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0228afdf1f41799b1da9c6d385ee606fe3d530c6f1a58549f06264f49f078cb73fa2815a5c6987a1a50b", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0229fedc80d82142decdf1e5970d8f8e6e036f373721ffcebaddbf37ac703f9e30e6b8fac9070e42dc11fa", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e0229015020e0be8664e256392c7a119f901c2acf390e5a7ab60f9d9b0b60f87348c05d7ea5a396785e5af5", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "305602290085b1bc586bf5407f9c8ec3765fe02bd19380998c45892ccd5081a1bd8872a26cdaf403e6dbf34a6e022900afdf1f41799b1da9c6d385ee606fe3d530c6f1a58549f06264f49f078cb73fa2815a5c6987a1a50b", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020100022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020100022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020100022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020100022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020100022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e0201ff022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e0201ff022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e0201ff022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e0201ff022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e0201ff022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593110201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3030022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593100201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3030022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593120201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3030022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e270201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3030022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e280201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59310", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3030022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e28090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3730373135", "sig": "3054022825166f47ac99c6bec3b038849ab4ead3b251f18afb0da1da5caa604a92a909c8561817684abffb9202283107ffd1aadce5b58a2a1b9517ccedda090433ac6344b027f36fc6b358ef4a8e436df3fd05521668", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "31373530353531383135", "sig": "30540228103c3ef2b43a8f57d01e2da67edfa003a0d342d7fbde0541332b0b24deea76afff4e2cd0572d73bb02280a0a680ebe3644c46b58d67ed8ee94f3aaee2839bc270d6b939bcb7657eeebbb6cccf2bc54af9781", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "3130333633303731", "sig": "30550229008c70216094feda4e721a72d8a91c51dd17392cf4c4481d7cd94be56da994e5baaa561085cecfe80d02281b19f7e89525601820bc17bd595a7dbdef76e5b352fcb16c3a8a1c332ff6a5308ff47a7e54e0b1cb", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "32333632343231333231", "sig": "3055022900b4a4a035dbeaee126d09c7b15816b04bc717cb71bb5fe7649ac026269b7fe6d593fe1ff8fc5278a10228635516de531104e72176e89a845032b3096e3269e41431c1854fbc4337ba6fb5ea91defd33729d83", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "34353838303134363536", "sig": "305402282a3103cdaad1dd28ecf897491051dd0a9c9da9483753c93490b4a05f1c42e1642925a3a0154d40620228672903243b6858a5e09148e403461f31c1ff0e126c365942e0680d314c1a7a7c57e2f0528c8cabbf", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "32313436363035363432", "sig": "3056022900b880b6be2a1295af3840a5f374fe77cde1ffdd6df3bb86097d5ce14852f73a1925fa6d192a27b74c022900c2cb211303aea030a5b92be98fc36770822f8195ad73eee5a9bb87c5717ba4345cb60b099e4d4deb", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "333335333030383230", "sig": "30540228677f84a0653725b94e4eeddbe0b70aefdf594f5ef9e484b4060567a8365c43a783d81548d1f2740802284cb24e15375bdae0b44b336fc7e6c11856d4c6f9dd7e83148dc387c4a8869b11538b7ee94f053f4f", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "36333936363033363331", "sig": "30550229009d5d984c7544ef737a1001d67f9dbbed521b46f74bc468c03881c2ab5944635af5465c3fa01cf51e02280c706dcfe11a4e30d623870fb0f2b979d0fd9daa970d86f64bb48f49aa484d924e9b93bcaf406924", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "33333931363630373935", "sig": "30540228448464becc13522c90708aa5204930e676fedfbaefe8fe02509a4fe822cc88fd6d92a958438ded7c02285de659e080a61c50b5b7489f4677ec4c6931faaf171f2a69756e2f2d1214235bdb1ea3d2a4a75359", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "31343436393735393634", "sig": "3054022859c0c95941e1a52390e00c2d7796c685dcc4d73d6d6967590aa1767c972e199de3c6dbfca77dcac80228507f27ab5ac05ad23cb25fc48ffc766dcb6dc0cd25606505a2d270066c3a74842768b54af2c84751", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "35313539343738363431", "sig": "3054022859902d7763e7f1875a4252d133eb6114cfc1972b683adfd767a71ca80c3f78057cea759ea195d31e0228397deaf96e2903a207f68e5330c9f2c6276a45d0fdba961a04c275fa203678176201ed370999a32d", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "35323431373932333331", "sig": "3055022900d142217584e852a499efa734a10a436a397ba7e068ad70f3eefc4d6731e76a481b260eac1d2147f0022817c8482639df8d20fcb835bfe0f3ecd27317eb8315c69b656ebf137dde6582f3409d7c44a8b6e085", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "31313437323930323034", "sig": "30560229008a55fa49224592f7e403a4b3e647bf382a26499b37ec2cff51a2be2a1357807fe875359ec8654f87022900b9506e74af8f552d4abb2c472b8508ce24814e20b27d192e24d36d5ac751922b0c807bf97a7b1ad7", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "3130383738373235363435", "sig": "3055022868ad2da643d66fc5d27b63d7222f835d09fe0b328fc4da4684b86d9c12b3992626f610e3395e4ed00229009662f74d52712a2af54f601c4488934fe2826d50e1ee868022437c9b620c93d43fc750f03312897c", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "37333433333036353633", "sig": "30550228578bdd50de9986fad341a954e51126ff0cb540026abb3d42b3c208e4ad187f7ba2d99b3efe495c92022900b95afd2d12cdee68c3572a5fe126334ed0ed7ba82d3097eaa6d9d737c09b830b6cd3e878f470e7e0", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "393734343630393738", "sig": "30550229009d612663497c484084d3d15d8e799e1fe38b7b5922955fc5a7fea4ecfc41954ca707525c1e0dc010022859e80cf69be6876b95357ded13ca61a494fac7355ac2e80a89be0219552d916852632617c0946bc2", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "33323237303836383339", "sig": "3056022900b4c00f589dbb51ea68270f4b02eff48a4b123c0167bbd24daf2a837903e734339b8a2542041f87aa02290094c32634baea4452c054295d7aebe23be7e80abbf53789651674182263ee5c2902fbfb3df7da7425", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "323332393736343130", "sig": "3056022900b9201e49950ce0d1df644356405f0a785a8b8470f83e78a6f35e6234daa92a7685877a59d8c91a970229008bee9077443eedde34a2fc2c266f188e844eab2904c84204c816ba3cb1c4b9b253d4a78ce4e81114", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3934303437333831", "sig": "305402280d42a1899f73ac70c9192558b478db8803c68337d415faebab76858c32a37e399f1727fd599a18170228363f1346c0227ec54da1659165ee7b07e06610d36b1ce6226f608bf6cef2144248de37562be8537a", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "33323230353639313233", "sig": "3056022900c5d65effab2ec3e4435c258121c493e24ae92005ac80136f21f2f42946fc3745841dbc2a3eb9969502290085fec2a9080a1ece18896970c9a2e1b32240eaf187d65f6f9e91d27111c4033d471eda67eb8986ed", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "343134303533393934", "sig": "3055022900a240adea61f748998df2cde95be9d7df30f454bde5b907ec7de6dcdb121bea41bd42c4392476c4f902283ef991d642bd0265b4a7b521b20a42fb2c687ca2f0694b239a113a83575b5727dcb632482a572649", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31393531353638363439", "sig": "3055022900cad52bdb35af7ee0e8f687c81f2edd8efc2d6ee317f3c6a82121048ef7a3ff3b69187aaea53f4926022858f84e186616544af494900241d2b802df2eae3e3f1410865e4cd16e221f277e7b5093ff186e4d76", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "35393539303731363335", "sig": "305602290093013ff84151a04ea146a36d2a3b9f497ba5d9323019b730be322bc519e2701e3f0ec1b6c8015e8f022900872669f33b9b4b93384d9ac3f7c3092560b9af7e6738221e3b289421813601fe569b2c49afec8bb4", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "323135333436393533", "sig": "3055022826228cdd2c08fbf8dd4ad5d2f80ed15129868e892d33cd892503207e91114c868d0064c60f1bb612022900afdedc05f0b27e9363c34d9bd1bc64ec0142fcd9f40f3584605bbccf12b0e279e4b3e3d0927a4852", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "34383037313039383330", "sig": "30540228731d597c3694a4932f0f14fc3132d2ba9f5b7d833ae91cbe9a450352f4240d5bb712f65b0eea041202281b8a6fc9bc1ecf8c09b1ba27c4c8dcebaf1e669a89036b34fa8ff57280e5741959e6c05e05880a37", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "343932393339363930", "sig": "3056022900d2ab69ae6110bb67ed99ffbdad8036dfabc46de8ae1fc7e799986b91ba7d454672ebe4896cf72011022900d29d67bf2b882770d46dbd06a6fbaad583c2ceedcbd772200b7532e354f86eaf9a9418191eafc5b8", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "32313132333535393630", "sig": "305602290081da10c5fef4bfb58b4a73b4cf3fd4f0f028b448b3463dabe0d6f1e101af570fa64116731ea5b9c2022900ceac01ebad706ef43c80caa1d8962c655bfd810396b94d2bbea299bd5cbcced75562b0fab446ff85", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "31323339323735373034", "sig": "3055022838358ba2c46f61f39bef22873a5bf26464f2b05e4874bbb62a2323385f8e87a5b118a0079078b44d02290093f84f06290f48161922552577482a973404f47c84c6e1a94643c3832fb2912fc4b38529e2f13e9a", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303831313838373638", "sig": "3055022900afd1fba700fc703fb772f701597adddcce4ff9f530c830dc8c8cbd4b3070f4a22b80516b0b820970022876c2e890860c36bbb5f6a1053401f1b51aa83cdfd96a3c15e1a183fdf8357e49d2984e4fcf19c25d", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "343534363038393633", "sig": "30550229008c90d4c8bf021906d3d577bd16b3e139bbc35d7692a828f0ef5cdf9d51a8442265f815849fe793b802280bfe16492abb58a1d8064767546d29aaf6138c5842c7f7002fbac34b78b324b84426510c1b7b0d89", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31333837363837313131", "sig": "305402286d07da92bd405912b22a993ed30e06149c78743fa195fcd3baf05803fddd5a6408201e68faee622a02285ad3c8b4c1c68080279f20ba15548343fca9ec52fe23bdf59619738dd1bd418414ac53ad7ce16c2b", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "32303331333831383735", "sig": "3055022900be43df617f79ef83404e7c7393ed3c38c815d06e3c0debf9ba37f36c419a6c3ea690822f88011ff102282f0ad4ee5fe7ad128f58a520a4fbad3f0a502a4a4412639b3dbc206edfb2a03d564010d78d2228b9", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "323535333538333333", "sig": "3055022900844ca29340112d0eb9dd15f62655a0cfa993415e570511a3f7273623b82d892d136c6e8bc57db84e022871114ae579d053b5cb3d77d2e9faf1c06cc263ab8fd845a0378f4a75da86ddc23ab4d07946832a77", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "34363138383431343732", "sig": "3056022900cda8531222502a62368fddc724798870038333b9fa77141d4bc5ef758f7e973e5ab8b4cfae90eaa10229009ead50a2533287abe5504efd8db57f8b96a7fe039cc95d1690ea0c1e2c9df5fc29cddd7b01edb99b", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "31303039323435383534", "sig": "305502285c2c6a661338df365af8ba1080b994e59989f002fa4fff42fc8994ca6395620152f3971300aff6f90229008681ea1793bd3e069426127a6b665725ddba4a8f1945851743477a1cbdc7356713ae70fc138531a3", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "32373536343636353238", "sig": "305402280e061b48b5f7836e0caf73c9770c3fee1f67ec18ded9e1d339ab56d05b9adde369504fedff1d66810228009dbc4ad8edc1896fa041ca60ac64b7fa148e3f02b0f697ae22d923f526fd4936e5f584b23ddbc3", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "313139363937313032", "sig": "3055022900a8462605c79437c068523d93d2e1529ccae1b5248e8f9a90f2ef08d9d5e5025b3639f82b70f1e7ed022867aa5fe4e79e7c54a8ce389b90e1ce1556aa689b44814a6cb5c2f0fe4569c5cfcee34cbb4a086219", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "323333313432313732", "sig": "305502290093b01029421ca30d8abbd06f134ee9dcbe81790d012722ae65214f0aaa34bba642f43949d5ec51a50228110e4fce36e0c2acd898122fba756e711ad082087c36b125084f67b22e37a02bd68628cdb164ece0", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "31363733343831383938", "sig": "305502286b6d893c3055da5d5a2ff6ce038a1bec59b04950bfd8012648d6063186ff861d7aa91a5185aa3c0c022900a602ed34ee41e4811251a51bd67010f8eb3355b8691dad66035e723d971346f57c8a0f479fef666c", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "31343630313539383237", "sig": "305402286fc42cf23aa42c36675ff126412c757dde74ef73cadb0425e23cb76230a58b3d002370b4166bfd2902284e61b9b10a13fe0dd2758733f6b178af98e0079867837d55f8e5e90b577de90ce0d8dc345ed16b38", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "38393930383539393239", "sig": "3055022900b529a563a2f4065d333c812b0b6134de9735bb269fea763f01cf7d1c6a0ec70d7223c7e6c1b040e80228347fcb8f3971b3d968ba709b1bd4d31b550cceed232268ca0df1e00502a56aa42dde7330bd919d5f", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "34333236343430393831", "sig": "3055022900b91b2a73449560522112fa4fd1b57b8c24754791af247a8c9de423e0bd536289a4fe850f7e3c43ae02282eb3f874f2e5ec56356bf6baf10b64b7c54b13447ccca1ffc66a1fc89dc8e977801748f8f0ce5a4b", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32333736343337353537", "sig": "30550228314338683f23b1110d1840732f672254f38079bf11eeafff1ec2e53a5373f74c98887b11ebb78c86022900b7e3a6b459dd10cfb5df6d2ea7afb15efeb1e5e917e5aa44fa54743689d7daf163f998e05719127a", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "383630333937373230", "sig": "3054022822d1881a94e1e11cf10620f37d708ebed847f1459129a0d42cb457da8051f81a0f9da846281a68f70228404652ebd261f5f6b185de4a16980dc3662fc4573e245577e7ef6e3cbfdd47bc1e487ba206ccf760", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "35383037373733393837", "sig": "3055022900863970caec6c6ff0a1c70f60859576e3583e6b529de3f928136e848c56b6c6715045fdfe526377470228667f525a88be891ec6b9f96d1a68c4f06b3b3d2ad1c15f063d110ec9fb60ae7463dd568a69ce452c", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "353731383636383537", "sig": "3054022869cf0600384ceb832e237b6b453dea81a3c19cb8fae48ba186d28e3b118464b27af9100f181b738a02282780269a8fa40790ed726372ee0956265e72896d9e84f713f883a3bc0548e8d3a2b357333dda4c0c", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38363737333039333632", "sig": "3054022830f38139883f71c0c64d63adcb731f1c385df87db7ce7326bf50410acc50f0babf5017f92a1e1a6e0228597734222482724643f60e48b3b589deaa37b86e1de1cf0b129b286ec67686574f16cfb5f2cc6f45", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "32343735353135303630", "sig": "30540228174ae98cc25dbed747cedf8697266d16e96bb4f8071c905990f4e72c728c94aaa32dce24a52166fb0228578099835a3dcb3da2fd42750180079da407c7a142cbccb699fed7af89dd703ba0cad94cdf2c051c", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "393733313736383734", "sig": "3054022858cfb1511a223c06b3e974b660bed827bee38c59c4523068e9c9faa330c970e6271ea387db6b40b00228763594ced6e8413bfd90d5ceca18d6774a3da87473cc4dee726b0325e2df8b257d9e01318ae7e022", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "33363938303935313438", "sig": "30560229008ddba4f7aa8cc5e00a3ffd2a082710d42d3a88f9f947ba51f09466c2a9295caa131b8ae9ef51b35c022900bc3410033a5798d9e4c6a817da1759c00c0d38e3c1ff22f0a41e5ad0d1e914e71c907da8c245ca2c", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3130373530323638353736", "sig": "305502282f7e50ee96706d597d8bf0103fa46786746a2c65c021fac7fa378d244c418a42b7908f0fab2dffca022900b7b94f2a883ba1b49858329cc78fc2a992109809b470b878cc38e1bdd4df3ee00a0ae7e228e466bc", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "383639313439353538", "sig": "305402284591491eee04e10a40a1e8eb82148195123348ec1a7cba06044e8f226ed30910b693ebbbaee0685f02281b8b3733cc5ca15dad84809df8499788b4899cba307f93b49ea6a63b9e77487c3c98b803fce69cb9", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "32313734363535343335", "sig": "30550229009d22b864e2706fa43a67ea62f39e4d3f402767d9d038c78e2844d699f41eaf1a641cfdd9d6a63fb502281eaacdad8294ecc2ca0c55e6e73df03d832beafe048fc690895beca5cead9d01d37b3fed5741cb94", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "363434353530373932", "sig": "30550229008f1502143eff372fb9bdd956ece35d232403fed7f77eb4f4552d4fc84e4a2e5d6c8ccf06f5a6ad6102284621c9fbc37466f7c757f66d171da8ee0a0dd5f24113e2f517a082b7ab5d4123c3eec9eeadf69952", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "353332383138333338", "sig": "30560229008dcdcc74a25fcd92e4372e11364a361c7a2d7f3dd74c5d6bc3761c07fe78f084765de8ce70e448fa022900b3cfdb35c20b1e30d3c216dcef3c73cd44ef1973c8695c8ab439f7e26628574e0f349d81c598d1e6", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31313932303736333832", "sig": "3055022900bc2a5b098f724830a190480fff5fafa0f2fe85bf17176af8d4eb670e1d55533f820d690e76b3bccd0228516c576444be0250e33823302adb708b6622f17e2438f01800c58edcf907e505b419f6c0dff11afc", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "31353332383432323230", "sig": "3055022900b3208e3c49a8ce5fb89effb2480e8ca6b9e11b16a049aecf0ce35450e1e53509909b02ab33e663e20228362affc091fa46d71ae84e27979ab575c60f115845fc521e0a81591ab233bd32e6ab0e8b08809801", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "313536373137373339", "sig": "305502290091cd133d74c701c936758054753591d31f1d854213b4067e033880116d4653cf257015445c5563ea022843185b31dbee46dbb62d1cdaadf479aa4f4b0b32dbc49ef5ccad43c9f0fcd94f06bdf6315e67c785", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34333033303931313230", "sig": "3056022900c63ac9ada6e0a00dbdc171f4ced1c16294881cd7b8cc91e67d0f97f5a61909ac6a694ab0b0d37a6c022900b33d6876f4464e7ad8e27a195f63b49fad3be8f4684f4c3d42f58913944abc60173e5113581d94b7", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "37373335393135353831", "sig": "30550228489db46f6ab87624a332376735aaf8e6c4c43affdb9b93c78682d3f90c1e01caadbcac4c975a22130229008e10c64116c4042b71bd9872c0506a7b34b6fffa9c3e24f843ce18270e3f163659ddc4a2460a4382", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "323433393636373430", "sig": "3054022809f7e103550337e36ea09f9ab3b83580ddc6259fb9daf38b424175d64134d14cae3112bddd7b21af0228253719f8ce1161959841b06ae31dc9d84cf0df90dffc101f0442c8e98c040e4d53f8ecd709b62049", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "34333237363032383233", "sig": "3054022841310739de7f1e06169f375d73e06550a50850be6aef432ef2143d7addfaf218f68836375921006e022843c4594e625f7aaa757eea847451d0155bd6b820883306b921184ec8141ca2b8c23b1dd64b980f97", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "32393332303032353932", "sig": "3055022900cf0672d18f93bd9c8c1c5e17d90a918c5cc8fdb967f9d2ad727dd72ec992116741b175d35393885d0228029e747ea2c1d66f1b4c5be492bd3b0ce01e8420a626a8a8e125c3b58c614cab12e6edbfc13f8a4f", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "36343039383737323834", "sig": "305402282a247a37c489974ed8946e5e8bd5a9d298d7afcc416b5efc3a9c69ba61d6538ad5294775d1c2a479022848f5716ab8b409f284351051dcd222cc5ec4f1b8c71708b1e85ed4db1ecec73888bd8f78c1e74d05", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "36303735363930343132", "sig": "305402287a2d71ce074eaaa4dff6d104fb6e7b070cb09c235deb697f5334918e181ce9bbe547b79ab37969f102286dc14d2bfa01e0cb36878d1aef216df992b5c3f058fab8ce922249b59cb72556364138389561af5a", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "32333231363233313335", "sig": "3055022900851f1d1483719cd183940cf4e9eb30eadde699d8ba8090e216123ff61e41d166505a591a75dac6ce0228292a45f31ef34bf34c3ccfd4a22adc4cc19c416151f70e95ad19999f9c59685e5fd9079a27b86fce", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "36343130313532313731", "sig": "30540228061df326b43ad7aa7bc4af8f3a5830bb0e63297cf690ec60a7259a942dce631e6676742ad12830cf02280e88675228b7fa4743ab53d24865193742cd6c5db218dde0044b301654bbab639abc775aae69064d", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "383337323835373438", "sig": "30550228560e6e5414414dbbd6e9d40522c9f3fce665c4455eae07eafa3724d708689315f6c7515cfe1aedbf022900a3e619bf5f9d776a591ff74a9252e43bd04ae1f1c34fe5b84f04c3d9c972a80e187888bade5aa9d4", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "33333234373034353235", "sig": "3055022834f9def6f21f5328d1c5e349857f3a7008f0afa5bbcb896ff6247b21a4abac7aedada64fa23f956e022900cb44dbc53b0b0b43d6b158d90247209c2c74152c4e19a1c703577cf407ada14b198bde1ba79a344b", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "31343033393636383732", "sig": "30550229009721d7991f2968c23856a075e86b70a39f4aa30fd68777dbbf46c1d37cc3be4497cb4714b2f1656a02280aa34e858175fef3c0734c5c7c4ccd0459927b0f722e86af6c4045752b4ba154e725946319dc4274", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31323237363035313238", "sig": "3056022900aec92108751afc4a0c62a9da2163bb52bdabb9e7a8b566ca8d30fac389c68a3817d21a33df2f68da022900befbacfb03957c9378903cf9b432093f78954e5224303611e9e96c92a76704e3a6432a24413bd277", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "34393531343838333632", "sig": "305402286e852689b90451a1592fad585ea57c71eb5b446196c07dcf450972ad13fdcd8117319eec75d0781f02283608d11f0b8eb773b35878cc43ea95fb4d354ab0ffec9f785a41a17ebcf8f7d957f793479ae89999", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32343532313237303139", "sig": "3055022900a34ebb41336c252a056097983d56dedd1eda042c7378b8b56d905aa9be1d7f6afb605466c0026c440228414c12e2a97f8e427ee9bd95e15bd5c10c16c1eea6011f01f271fe75869a6000fbfbc25a6d1f8541", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "31373331353530373036", "sig": "305502281cf27fa96f137e0fbce4da476bdb5c3b875b7fb455fcfe7efd863cb8ed61090f6cf6a2b927fabbdb022900945bc7e4f319004b2ef4ce2fa2ef270c4abc360e21d8f8b21074080ee8a3422137c96f82e26cfa3c", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "31363637303639383738", "sig": "3055022900cdb4f0b7f7de92ec59a2e0a1900318bdd649155bcd3e0914136a7c46c5e4bfd84231c04d64cc5c5302287b0ee0d7b7773c3bcd5921239f36b093bd232859d685920eaff53a91ff7188344a3075fe7f342c7c", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "343431353437363137", "sig": "3055022900aa8ef33f59508ea9617a41084a3532e1179d0f5755e08277a568192272cec63f910377a871021c7e0228708751c6284f7c3cfc57a598c1199c1cfcb3d26e4546484de55228c292bca978ea7698d3f7d806c6", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343233393434393938", "sig": "305402287d57fe006a0563d5570acbcabdf94ed515029f52e02298c79c9891181bd8b4974dde5765433d89cb02283be83d7dc7a5dc1a151f2b0957f678b9efca3d0818ec359202d9a4cfe792e95924be9e36e20ef970", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "34383037363230373132", "sig": "3056022900998c251f0c1f9623980c1cff0e369c7b0a383fc74079113121f982bfe7b87f17b48e5cdd29b0e1c0022900d0d33d8be8c37fb49c4f49ceee321186ff30b9950a706c6fddd1054772af3c3266fb286677592d69", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "32313634363636323839", "sig": "305402282e2384e2621b0f248ad34922f721f434527d354a7ef876defb8e80f6864867ae60c76ce24896a40f022808ecea054800ec201f0d1ac893f3bd79ae48533529345e1d3310fdc747d765970fa55ee0a47bdcd1", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "31393432383533383635", "sig": "3055022900d290cf0f9fbd691743b9dcdc00c0816fb99b5ad3b89a23f1313e0bf00cb3e12c95648dce175e34470228587707db7e9ddf613ff7d979ba9ca411b4bb7826862a380ce7cebecd52f52ff885f8ad536fbf1123", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "32323139333833353231", "sig": "3054022834b252604ebd8675104266be4df84b51942687a212957dedd4ab6dff4355a0027b7122aeb1e9f449022815082c5764df742c40193a8bf1e38d43a5fa4c77416cd8753057521c765062c676f99be659fcd00e", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "393236393333343139", "sig": "3055022900b45baf5ee188cef5413ef3e6f0d917a90cf263d2be0a7039a6064e6e4053b6c960f44de15932855a022847602defee00b2fdea095346deae00b46069c95c09e43594889b8d3aa3d75350377aa3431ef63c76", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "373639333836333634", "sig": "305502286d8267065d60dcd375ce041813d6085fa90246cddbbaba12643c736cc9b7e6d619178f12c6fd2d3a0229009b72611a5b4f8763e30c11aa791eff6b74c34d05e65451736e2a2b728f2e5ef485dc4e2e5c3daa37", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "32373335393330353733", "sig": "3055022900a1a302aa27a32cd25c336c9ed7adaf5caf33dc3485a813718fce395ec0c93eef4cb34a6518a61cc80228258acd1b450082fddc05433e2dd66b0321114395a33bd9827d4c44d486c82c2d229869f3762012fe", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "38333030353634303635", "sig": "3054022863fad28570b4203502a1d105903244dfe2b09530a93d8ea429a197c7121337a99d4c73516ec6613502287b4a08b7cdeaf6eedeec6306a0c410b092718c25590190030c5255bf837393d8293890c84909f436", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "34333037363535373338", "sig": "3056022900c1e6232f06a44de1d7213b20eaa1b89c31dd2f5bb033245e8171524cc9110876d778a04834ac88ee022900cb4aa20d158c3b8d115ec62bb51545bd58b63f1f0176ada04869dfee84019737cb8072f7a8b940f6", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "39363537303138313735", "sig": "3055022847b6baa1a053eab8b38ed6c9862f2ffed3811449e49ff2e18a588512129d1dea0a6c4fb811dc5eb0022900a6cceef5a1e12f9b049f72f53732d42903a733ca6f3fae9596e17d9c757ed4ef04fcc37302ad1f45", "result": "valid", "flags": []}, {"tcId": 317, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "3054022867bea53478364ad2cc40eab42535a4bf8e41583c941cb04ef37f11f035654c331d3bb0ddfd74031d022876a9eba43713ed8892a627ed3bcbf7d87f7991d128580a057c1b6388b604954d340e92f41827674b", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04b21ecd48cc46fb306ed54e88adb615208457bd257e7e9d81db5bd1f56100a7aebb1386465507bbf386224cb383815e1babe561dcb6f49af0073e1bfda366066ef62440fc81dec7eca021cb0c05091dfb", "wx": "00b21ecd48cc46fb306ed54e88adb615208457bd257e7e9d81db5bd1f56100a7aebb1386465507bbf3", "wy": "0086224cb383815e1babe561dcb6f49af0073e1bfda366066ef62440fc81dec7eca021cb0c05091dfb"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004b21ecd48cc46fb306ed54e88adb615208457bd257e7e9d81db5bd1f56100a7aebb1386465507bbf386224cb383815e1babe561dcb6f49af0073e1bfda366066ef62440fc81dec7eca021cb0c05091dfb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABLIezUjMRvswbtVOiK22FSCEV70l\nfn6dgdtb0fVhAKeuuxOGRlUHu/OGIkyzg4FeG6vlYdy29JrwBz4b/aNmBm72JED8\ngd7H7KAhywwFCR37\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "30420215014064fb4c224a8b248a0d933f7642bd56aced9b12022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930e", "result": "valid", "flags": []}, {"tcId": 319, "comment": "r too large", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e23022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930e", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "049c9701de2ffdb296e6d56a5f3c189ecbb0e4448e38ed65da46eeaa51a7b34e650a91da95faf179001e0a98a598523a34c4918d4180f87d641e4626ce11fa3a244abfb2450736693d38652309240ebda9", "wx": "009c9701de2ffdb296e6d56a5f3c189ecbb0e4448e38ed65da46eeaa51a7b34e650a91da95faf17900", "wy": "1e0a98a598523a34c4918d4180f87d641e4626ce11fa3a244abfb2450736693d38652309240ebda9"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200049c9701de2ffdb296e6d56a5f3c189ecbb0e4448e38ed65da46eeaa51a7b34e650a91da95faf179001e0a98a598523a34c4918d4180f87d641e4626ce11fa3a244abfb2450736693d38652309240ebda9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABJyXAd4v/bKW5tVqXzwYnsuw5ESO\nOO1l2kbuqlGns05lCpHalfrxeQAeCpilmFI6NMSRjUGA+H1kHkYmzhH6OiRKv7JF\nBzZpPThlIwkkDr2p\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "r,s are large", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930f022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930e", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04b6f0ddc834ef8a67903681ea02b788fcff82d12307c8c3f4a44b30d7c5f614dafcc9a839991f8ee427538e30ae5102b2043957dd6124fba3a1b601c04bddaf6c929ffdf2f7796fd7098c387dbc0b26fb", "wx": "00b6f0ddc834ef8a67903681ea02b788fcff82d12307c8c3f4a44b30d7c5f614dafcc9a839991f8ee4", "wy": "27538e30ae5102b2043957dd6124fba3a1b601c04bddaf6c929ffdf2f7796fd7098c387dbc0b26fb"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004b6f0ddc834ef8a67903681ea02b788fcff82d12307c8c3f4a44b30d7c5f614dafcc9a839991f8ee427538e30ae5102b2043957dd6124fba3a1b601c04bddaf6c929ffdf2f7796fd7098c387dbc0b26fb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABLbw3cg074pnkDaB6gK3iPz/gtEj\nB8jD9KRLMNfF9hTa/MmoOZkfjuQnU44wrlECsgQ5V91hJPujobYBwEvdr2ySn/3y\n93lv1wmMOH28Cyb7\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "305502287fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0229008c0736554dbc446063e8e15f297fd4b66fa8879945bbb5c22714a9645f4fa4ef9d710eafa6b226d8", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "046df44321d4a5f6af63e01b79bb608ea04ac6f35f795044a04ff400f547fd34d9b78c12c45978f96fb52901cece48aab432c3dbdcbc0e270b2cc9b9915cc1ffb69a365d84c39186c48177387aa9ee0a48", "wx": "6df44321d4a5f6af63e01b79bb608ea04ac6f35f795044a04ff400f547fd34d9b78c12c45978f96f", "wy": "00b52901cece48aab432c3dbdcbc0e270b2cc9b9915cc1ffb69a365d84c39186c48177387aa9ee0a48"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200046df44321d4a5f6af63e01b79bb608ea04ac6f35f795044a04ff400f547fd34d9b78c12c45978f96fb52901cece48aab432c3dbdcbc0e270b2cc9b9915cc1ffb69a365d84c39186c48177387aa9ee0a48", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABG30QyHUpfavY+AbebtgjqBKxvNf\neVBEoE/0APVH/TTZt4wSxFl4+W+1KQHOzkiqtDLD29y8DicLLMm5kVzB/7aaNl2E\nw5GGxIF3OHqp7gpI\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "305402287fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff02285407cdd593acb501fc2848351f9d2e6b5457d3de43c3130e3b74e6a9242b3cce1c24f094f118bcd6", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "044e496f056ab5d07f96562c683643440e31fea26d35e6c5b69eefaa4107d345c807bf279f2ea26b60288539766fc726cb9e841db5dcfbbb792cade3c1ef64b69dcbda7f5e497b455a911ce2f0ebcacaad", "wx": "4e496f056ab5d07f96562c683643440e31fea26d35e6c5b69eefaa4107d345c807bf279f2ea26b60", "wy": "288539766fc726cb9e841db5dcfbbb792cade3c1ef64b69dcbda7f5e497b455a911ce2f0ebcacaad"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200044e496f056ab5d07f96562c683643440e31fea26d35e6c5b69eefaa4107d345c807bf279f2ea26b60288539766fc726cb9e841db5dcfbbb792cade3c1ef64b69dcbda7f5e497b455a911ce2f0ebcacaad", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABE5JbwVqtdB/llYsaDZDRA4x/qJt\nNebFtp7vqkEH00XIB78nny6ia2AohTl2b8cmy56EHbXc+7t5LK3jwe9ktp3L2n9e\nSXtFWpEc4vDrysqt\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0411e094f470948e4eaa6aa13fab4e063386e91a638fa226d988d0693dea719ca95f61e493e9835af43f533e89aa2085a9f8121086a2597f1060f73c8d75d66940e50eead73dfd03c476ea1947cdd4dd3f", "wx": "11e094f470948e4eaa6aa13fab4e063386e91a638fa226d988d0693dea719ca95f61e493e9835af4", "wy": "3f533e89aa2085a9f8121086a2597f1060f73c8d75d66940e50eead73dfd03c476ea1947cdd4dd3f"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000411e094f470948e4eaa6aa13fab4e063386e91a638fa226d988d0693dea719ca95f61e493e9835af43f533e89aa2085a9f8121086a2597f1060f73c8d75d66940e50eead73dfd03c476ea1947cdd4dd3f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBHglPRwlI5OqmqhP6tOBjOG6Rpj\nj6Im2YjQaT3qcZypX2Hkk+mDWvQ/Uz6JqiCFqfgSEIaiWX8QYPc8jXXWaUDlDurX\nPf0DxHbqGUfN1N0/\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0416517a7d7beab6472ea8f6bc20412a3cd96d242c246ce9f983b2ef08b284cfad1ac28563b56edafb9f56fe2df78c239aa16c3c318bc9191a16ec407a700354173f8b862d9a0aa10d67397f26e7c9c0be", "wx": "16517a7d7beab6472ea8f6bc20412a3cd96d242c246ce9f983b2ef08b284cfad1ac28563b56edafb", "wy": "009f56fe2df78c239aa16c3c318bc9191a16ec407a700354173f8b862d9a0aa10d67397f26e7c9c0be"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000416517a7d7beab6472ea8f6bc20412a3cd96d242c246ce9f983b2ef08b284cfad1ac28563b56edafb9f56fe2df78c239aa16c3c318bc9191a16ec407a700354173f8b862d9a0aa10d67397f26e7c9c0be", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBZRen176rZHLqj2vCBBKjzZbSQs\nJGzp+YOy7wiyhM+tGsKFY7Vu2vufVv4t94wjmqFsPDGLyRkaFuxAenADVBc/i4Yt\nmgqhDWc5fybnycC+\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 326, "comment": "r is larger than n", "msg": "313233343030", "sig": "302e022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59312020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "043a2668bc09614d2638ed58f1c421bb61f2d499a86fe7d573bd1392acef9e296b1ef2b10d7f4ec524d1b78eb2716ce668054d29677c6f4d3235f27d3a9295ecef9ddfd2f658ba002052d0e1e671721e2e", "wx": "3a2668bc09614d2638ed58f1c421bb61f2d499a86fe7d573bd1392acef9e296b1ef2b10d7f4ec524", "wy": "00d1b78eb2716ce668054d29677c6f4d3235f27d3a9295ecef9ddfd2f658ba002052d0e1e671721e2e"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200043a2668bc09614d2638ed58f1c421bb61f2d499a86fe7d573bd1392acef9e296b1ef2b10d7f4ec524d1b78eb2716ce668054d29677c6f4d3235f27d3a9295ecef9ddfd2f658ba002052d0e1e671721e2e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABDomaLwJYU0mOO1Y8cQhu2Hy1Jmo\nb+fVc70TkqzvnilrHvKxDX9OxSTRt46ycWzmaAVNKWd8b00yNfJ9OpKV7O+d39L2\nWLoAIFLQ4eZxch4u\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "s is larger than n", "msg": "313233343030", "sig": "302e020101022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44d86998", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04cd1697c6e107f3d90b8df462eb368b75eba585635950177e0a64b1ad4be527c90957fbdf203d67c84b003f20e81659099b7e466618f2610c6f1df315b2011db07b90f3662b51561fffdf3ebb5d443440", "wx": "00cd1697c6e107f3d90b8df462eb368b75eba585635950177e0a64b1ad4be527c90957fbdf203d67c8", "wy": "4b003f20e81659099b7e466618f2610c6f1df315b2011db07b90f3662b51561fffdf3ebb5d443440"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004cd1697c6e107f3d90b8df462eb368b75eba585635950177e0a64b1ad4be527c90957fbdf203d67c84b003f20e81659099b7e466618f2610c6f1df315b2011db07b90f3662b51561fffdf3ebb5d443440", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABM0Wl8bhB/PZC430Yus2i3XrpYVj\nWVAXfgpksa1L5SfJCVf73yA9Z8hLAD8g6BZZCZt+RmYY8mEMbx3zFbIBHbB7kPNm\nK1FWH//fPrtdRDRA\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302e02020100022821494db879806d4f59e53d4963977a03f6ec51140c9f9a1dba8857ff3bbc76d2214947e60edc982b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04b1e3619d9e35873e959bb7de7740e927e3cb7fcf4413bfdbbed72ecc9a86a50d7029cae08ec285ab486b5d2f7c9b9314420bc864cfe29b4064bf7b922bbb5bbcd16f3a81ea7d0a61b0a09a62959b7690", "wx": "00b1e3619d9e35873e959bb7de7740e927e3cb7fcf4413bfdbbed72ecc9a86a50d7029cae08ec285ab", "wy": "486b5d2f7c9b9314420bc864cfe29b4064bf7b922bbb5bbcd16f3a81ea7d0a61b0a09a62959b7690"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004b1e3619d9e35873e959bb7de7740e927e3cb7fcf4413bfdbbed72ecc9a86a50d7029cae08ec285ab486b5d2f7c9b9314420bc864cfe29b4064bf7b922bbb5bbcd16f3a81ea7d0a61b0a09a62959b7690", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABLHjYZ2eNYc+lZu33ndA6Sfjy3/P\nRBO/277XLsyahqUNcCnK4I7ChatIa10vfJuTFEILyGTP4ptAZL97kiu7W7zRbzqB\n6n0KYbCgmmKVm3aQ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "303302072d9b4d347952cc022843becc876a63564b458280199e382cbad8ef68d406665bbf307ffea45845a9ac69345a84a5a72b87", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0497cfebab588a54242a4d962ef803376c3f43079aa50a8871d6e776f7a0b33aea46ab9a2da63a33d8c81af34af2e9a0c571effb501c4a27fd2aedc13623447af2bc8b6d5e7208c23e87e2d797cc3cf57e", "wx": "0097cfebab588a54242a4d962ef803376c3f43079aa50a8871d6e776f7a0b33aea46ab9a2da63a33d8", "wy": "00c81af34af2e9a0c571effb501c4a27fd2aedc13623447af2bc8b6d5e7208c23e87e2d797cc3cf57e"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000497cfebab588a54242a4d962ef803376c3f43079aa50a8871d6e776f7a0b33aea46ab9a2da63a33d8c81af34af2e9a0c571effb501c4a27fd2aedc13623447af2bc8b6d5e7208c23e87e2d797cc3cf57e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABJfP66tYilQkKk2WLvgDN2w/Qwea\npQqIcdbndvegszrqRquaLaY6M9jIGvNK8umgxXHv+1AcSif9Ku3BNiNEevK8i21e\ncgjCPofi15fMPPV+\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3039020d1033e67e37b32b445580bf4efc02283992353d916617b49303856488e39fbc26173b8bc426f8207de3d8f1b97f3d12c803b99d57768fa7", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04296e0067947efc07a06ae218fb00164d1ebebcd3787f793481407e2796248e8b65eac57db0c14606729e8094b9a54eeac23d98d51d662eff2df33a8693008fd02a0429ef6851ecbdcd93aac67c2fbdb6", "wx": "296e0067947efc07a06ae218fb00164d1ebebcd3787f793481407e2796248e8b65eac57db0c14606", "wy": "729e8094b9a54eeac23d98d51d662eff2df33a8693008fd02a0429ef6851ecbdcd93aac67c2fbdb6"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004296e0067947efc07a06ae218fb00164d1ebebcd3787f793481407e2796248e8b65eac57db0c14606729e8094b9a54eeac23d98d51d662eff2df33a8693008fd02a0429ef6851ecbdcd93aac67c2fbdb6", "keyPem": "-----BEGIN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABCluAGeUfvwHoGriGPsAFk0evrzT\neH95NIFAfieWJI6LZerFfbDBRgZynoCUuaVO6sI9mNUdZi7/LfM6hpMAj9AqBCnv\naFHsvc2TqsZ8L722\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302f0202010002290084380881b243236967227191398a3a4909000425576c79465bdaaa0a03267b9e48f68fa0a68b29e3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04a3783b01455d92080f520d171f92abeaf48c7238e168b2931f2b322f9c0faa69a24097836cb0a6851cbf1a22bac2437551244605682dabcdd4cf39ff9d08443921c99448cbcea5deb85ad952dbb2b967", "wx": "00a3783b01455d92080f520d171f92abeaf48c7238e168b2931f2b322f9c0faa69a24097836cb0a685", "wy": "1cbf1a22bac2437551244605682dabcdd4cf39ff9d08443921c99448cbcea5deb85ad952dbb2b967"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004a3783b01455d92080f520d171f92abeaf48c7238e168b2931f2b322f9c0faa69a24097836cb0a6851cbf1a22bac2437551244605682dabcdd4cf39ff9d08443921c99448cbcea5deb85ad952dbb2b967", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABKN4OwFFXZIID1INFx+Sq+r0jHI4\n4Wiykx8rMi+cD6ppokCXg2ywpoUcvxoiusJDdVEkRgVoLavN1M85/50IRDkhyZRI\ny86l3rha2VLbsrln\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "303a020d062522bbd3ecbe7c39e93e7c2402290084380881b243236967227191398a3a4909000425576c79465bdaaa0a03267b9e48f68fa0a68b29e3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0470d5fd41c416d5b7cdbcb944205bd69ff00ed6354aa502757e089cb19af6f777beb0f6921c0fafac22ae7cc65e0e7b617423750b8493a58512e379c00de626c17f7c82bfc907f26610a3f1e4d132c575", "wx": "70d5fd41c416d5b7cdbcb944205bd69ff00ed6354aa502757e089cb19af6f777beb0f6921c0fafac", "wy": "22ae7cc65e0e7b617423750b8493a58512e379c00de626c17f7c82bfc907f26610a3f1e4d132c575"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000470d5fd41c416d5b7cdbcb944205bd69ff00ed6354aa502757e089cb19af6f777beb0f6921c0fafac22ae7cc65e0e7b617423750b8493a58512e379c00de626c17f7c82bfc907f26610a3f1e4d132c575", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABHDV/UHEFtW3zby5RCBb1p/wDtY1\nSqUCdX4InLGa9vd3vrD2khwPr6wirnzGXg57YXQjdQuEk6WFEuN5wA3mJsF/fIK/\nyQfyZhCj8eTRMsV1\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3056022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c592940229008ce984c0247d8a7a9628503f36abeaeea65fdfc3cf0a0c6cc8dac9da9f043b4659b638e7832e620b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0406828ce63f3b0d694ce2999d06947fa9e2d1c18ab8032652fa7a98c678cf6bb2c52e7369085e4ef7c56df69128962fbefc2aef1b3f6c467b72fc305acf51b339643ca2ed6bde56317c4cf59895923ded", "wx": "06828ce63f3b0d694ce2999d06947fa9e2d1c18ab8032652fa7a98c678cf6bb2c52e7369085e4ef7", "wy": "00c56df69128962fbefc2aef1b3f6c467b72fc305acf51b339643ca2ed6bde56317c4cf59895923ded"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000406828ce63f3b0d694ce2999d06947fa9e2d1c18ab8032652fa7a98c678cf6bb2c52e7369085e4ef7c56df69128962fbefc2aef1b3f6c467b72fc305acf51b339643ca2ed6bde56317c4cf59895923ded", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABAaCjOY/Ow1pTOKZnQaUf6ni0cGK\nuAMmUvp6mMZ4z2uyxS5zaQheTvfFbfaRKJYvvvwq7xs/bEZ7cvwwWs9RszlkPKLt\na95WMXxM9ZiVkj3t\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "s == 1", "msg": "313233343030", "sig": "302d02284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105020101", "result": "valid", "flags": []}, {"tcId": 335, "comment": "s == 0", "msg": "313233343030", "sig": "302d02284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04202516ad663775f12155521079037f3fca50c64faa4afd886add4daab927f3f62aa2dae684a635d6632aedd530e61dab35916962ee8f23ed688198afd5ad6b0705e2ef9d0ba3c5333b15bdab432ee342", "wx": "202516ad663775f12155521079037f3fca50c64faa4afd886add4daab927f3f62aa2dae684a635d6", "wy": "632aedd530e61dab35916962ee8f23ed688198afd5ad6b0705e2ef9d0ba3c5333b15bdab432ee342"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004202516ad663775f12155521079037f3fca50c64faa4afd886add4daab927f3f62aa2dae684a635d6632aedd530e61dab35916962ee8f23ed688198afd5ad6b0705e2ef9d0ba3c5333b15bdab432ee342", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABCAlFq1mN3XxIVVSEHkDfz/KUMZP\nqkr9iGrdTaq5J/P2KqLa5oSmNdZjKu3VMOYdqzWRaWLujyPtaIGYr9WtawcF4u+d\nC6PFMzsVvatDLuNC\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "3054022869af23901b5e27dbf09e3c2f6900f032fcc7e7d2db47895196a41763f7432c74c348aaada262c98802284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04823a830c58d9dd370f687ff819142b644ac9dc18a94681e2245eb22f27e333e62fece397231769da36a7e237ea2f3e2472de147e166ce4bd8248208df538ac00f5b2299e2d729b0dd80e3e106c060844", "wx": "00823a830c58d9dd370f687ff819142b644ac9dc18a94681e2245eb22f27e333e62fece397231769da", "wy": "36a7e237ea2f3e2472de147e166ce4bd8248208df538ac00f5b2299e2d729b0dd80e3e106c060844"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004823a830c58d9dd370f687ff819142b644ac9dc18a94681e2245eb22f27e333e62fece397231769da36a7e237ea2f3e2472de147e166ce4bd8248208df538ac00f5b2299e2d729b0dd80e3e106c060844", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABII6gwxY2d03D2h/+BkUK2RKydwY\nqUaB4iResi8n4zPmL+zjlyMXado2p+I36i8+JHLeFH4WbOS9gkggjfU4rAD1sime\nLXKbDdgOPhBsBghE\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "3054022869af23901b5e27dbf09e3c2f6900f032fcc7e7d2db47895196a41763f7432c74c348aaada262c988022869af23901b5e27dbf09e3c2f6900f032fcc7e7d2db47895196a41763f7432c74c348aaada262c988", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04387a759284f65d2d93f541f2766f846abdec239190e8cddba9b7564a83d58162a489f25f0d43b4f8424625a6c1e1589474c30e6383c925b363239d1a87b9634fd8aac2eb0ce39e3763873de77358bd4b", "wx": "387a759284f65d2d93f541f2766f846abdec239190e8cddba9b7564a83d58162a489f25f0d43b4f8", "wy": "424625a6c1e1589474c30e6383c925b363239d1a87b9634fd8aac2eb0ce39e3763873de77358bd4b"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004387a759284f65d2d93f541f2766f846abdec239190e8cddba9b7564a83d58162a489f25f0d43b4f8424625a6c1e1589474c30e6383c925b363239d1a87b9634fd8aac2eb0ce39e3763873de77358bd4b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABDh6dZKE9l0tk/VB8nZvhGq97COR\nkOjN26m3VkqD1YFipInyXw1DtPhCRiWmweFYlHTDDmODySWzYyOdGoe5Y0/YqsLr\nDOOeN2OHPedzWL1L\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "3054022869af23901b5e27dbf09e3c2f6900f032fcc7e7d2db47895196a41763f7432c74c348aaada262c988022869af23901b5e27dbf09e3c2f6900f032fcc7e7d2db47895196a41763f7432c74c348aaada262c989", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "046b4327117e9c04d7a58259c5207a36e8d278e873b92b5b3a70a3c4742cc583b41408aaab23a12a9c9b0b26160c548abacd7f0e37276f917c09721b3844d0b26e9ed5c76c99787992259bf0f7b02445d3", "wx": "6b4327117e9c04d7a58259c5207a36e8d278e873b92b5b3a70a3c4742cc583b41408aaab23a12a9c", "wy": "009b0b26160c548abacd7f0e37276f917c09721b3844d0b26e9ed5c76c99787992259bf0f7b02445d3"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200046b4327117e9c04d7a58259c5207a36e8d278e873b92b5b3a70a3c4742cc583b41408aaab23a12a9c9b0b26160c548abacd7f0e37276f917c09721b3844d0b26e9ed5c76c99787992259bf0f7b02445d3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABGtDJxF+nATXpYJZxSB6NujSeOhz\nuStbOnCjxHQsxYO0FAiqqyOhKpybCyYWDFSKus1/Djcnb5F8CXIbOETQsm6e1cds\nmXh5kiWb8PewJEXT\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "u1 == 1", "msg": "313233343030", "sig": "305502284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105022900f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb700", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04277f487faf77a65dbb791536e863b8c70f904fcdcaf52740d4bd5c469731e58ea6bd53e8d77202282d346f2b4ca7bacb882fef749c2713f1a75f00827e8b9b9f744a0e1e34bcf80799a120950de95d99", "wx": "277f487faf77a65dbb791536e863b8c70f904fcdcaf52740d4bd5c469731e58ea6bd53e8d7720228", "wy": "2d346f2b4ca7bacb882fef749c2713f1a75f00827e8b9b9f744a0e1e34bcf80799a120950de95d99"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004277f487faf77a65dbb791536e863b8c70f904fcdcaf52740d4bd5c469731e58ea6bd53e8d77202282d346f2b4ca7bacb882fef749c2713f1a75f00827e8b9b9f744a0e1e34bcf80799a120950de95d99", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABCd/SH+vd6Zdu3kVNuhjuMcPkE/N\nyvUnQNS9XEaXMeWOpr1T6NdyAigtNG8rTKe6y4gv73ScJxPxp18Agn6Lm590Sg4e\nNLz4B5mhIJUN6V2Z\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "305502284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105022900ad0b664f9559e29e46fd4fd390e75abebf14997d17a1a3304c80e451fc8f79bb7cff168e17de6f22", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0473bd62f3bb329d65092b5d13c5746d462401d2600994d8fe2ec5ef5b9f3399084b1ddc64cb334baec1d1ac4f9a0c2a79ef7ccc4ae9165ddfa76138235718cf24032c33f9db4a26b2b03692a56f5202eb", "wx": "73bd62f3bb329d65092b5d13c5746d462401d2600994d8fe2ec5ef5b9f3399084b1ddc64cb334bae", "wy": "00c1d1ac4f9a0c2a79ef7ccc4ae9165ddfa76138235718cf24032c33f9db4a26b2b03692a56f5202eb"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000473bd62f3bb329d65092b5d13c5746d462401d2600994d8fe2ec5ef5b9f3399084b1ddc64cb334baec1d1ac4f9a0c2a79ef7ccc4ae9165ddfa76138235718cf24032c33f9db4a26b2b03692a56f5202eb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABHO9YvO7Mp1lCStdE8V0bUYkAdJg\nCZTY/i7F71ufM5kISx3cZMszS67B0axPmgwqee98zErpFl3fp2E4I1cYzyQDLDP5\n20omsrA2kqVvUgLr\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "u2 == 1", "msg": "313233343030", "sig": "305402284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c197310502284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0420a75551035db95d7a1a673d464d276da0861008e4644c582bc10a1beeaeb070823fd064a2625ebb5d47f0c77fc57e3bb0e153bbc7e9bbde8db98b0c46c58154af5b9786b10ba12ab3ba8533a3992883", "wx": "20a75551035db95d7a1a673d464d276da0861008e4644c582bc10a1beeaeb070823fd064a2625ebb", "wy": "5d47f0c77fc57e3bb0e153bbc7e9bbde8db98b0c46c58154af5b9786b10ba12ab3ba8533a3992883"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000420a75551035db95d7a1a673d464d276da0861008e4644c582bc10a1beeaeb070823fd064a2625ebb5d47f0c77fc57e3bb0e153bbc7e9bbde8db98b0c46c58154af5b9786b10ba12ab3ba8533a3992883", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABCCnVVEDXbldehpnPUZNJ22ghhAI\n5GRMWCvBChvurrBwgj/QZKJiXrtdR/DHf8V+O7DhU7vH6bvejbmLDEbFgVSvW5eG\nsQuhKrO6hTOjmSiD\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "305502284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c19731050229008ce984c0247d8a7a9628503f36abeaeea65fdfc3cf0a0c6cc8dac9da9f043b4659b638e7832e620c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "043f436d07cb0264b13f92fd696334a4e51b7d6619e2d043b2d0d278963f2516200ef905ebf671666340e642b6c966072b79278003651128879f19dee01273b66bead8045194277c9284093348d90569b1", "wx": "3f436d07cb0264b13f92fd696334a4e51b7d6619e2d043b2d0d278963f2516200ef905ebf6716663", "wy": "40e642b6c966072b79278003651128879f19dee01273b66bead8045194277c9284093348d90569b1"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200043f436d07cb0264b13f92fd696334a4e51b7d6619e2d043b2d0d278963f2516200ef905ebf671666340e642b6c966072b79278003651128879f19dee01273b66bead8045194277c9284093348d90569b1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABD9DbQfLAmSxP5L9aWM0pOUbfWYZ\n4tBDstDSeJY/JRYgDvkF6/ZxZmNA5kK2yWYHK3kngANlESiHnxne4BJztmvq2ARR\nlCd8koQJM0jZBWmx\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02280cc64af035cb79b3336a62d915b381e268d3bcb834f9cfd0f597c37ca5fcf50f588614ef0ef7b6a5", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04c16fbe6d0d77327cf9a65f987c2fe7ee1807851c0e1c8bc4f0622807dcd4a88b3b912eb0475471e575421c40540050507a163f23cc7cb90acc52822d01d245ab70dcaac06e2ea644327a85f595d026ef", "wx": "00c16fbe6d0d77327cf9a65f987c2fe7ee1807851c0e1c8bc4f0622807dcd4a88b3b912eb0475471e5", "wy": "75421c40540050507a163f23cc7cb90acc52822d01d245ab70dcaac06e2ea644327a85f595d026ef"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004c16fbe6d0d77327cf9a65f987c2fe7ee1807851c0e1c8bc4f0622807dcd4a88b3b912eb0475471e575421c40540050507a163f23cc7cb90acc52822d01d245ab70dcaac06e2ea644327a85f595d026ef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABMFvvm0NdzJ8+aZfmHwv5+4YB4Uc\nDhyLxPBiKAfc1KiLO5EusEdUceV1QhxAVABQUHoWPyPMfLkKzFKCLQHSRatw3KrA\nbi6mRDJ6hfWV0Cbv\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0229008e765d0d1cf9539f682a4155b6d60eb6aa6862b2af9e9d3f94c9ad46d332f0e029775522815c0e5a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04be924007d6e22b944ac76da7fc2660d1aefab69471bd835bd78edd2c10621e76f718bfd0a5e2307ec62583d5ba5cc1c547630476b399866e7ed953b538f76c86afe9cfd0854b57e33691c77e444ccab8", "wx": "00be924007d6e22b944ac76da7fc2660d1aefab69471bd835bd78edd2c10621e76f718bfd0a5e2307e", "wy": "00c62583d5ba5cc1c547630476b399866e7ed953b538f76c86afe9cfd0854b57e33691c77e444ccab8"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004be924007d6e22b944ac76da7fc2660d1aefab69471bd835bd78edd2c10621e76f718bfd0a5e2307ec62583d5ba5cc1c547630476b399866e7ed953b538f76c86afe9cfd0854b57e33691c77e444ccab8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABL6SQAfW4iuUSsdtp/wmYNGu+raU\ncb2DW9eO3SwQYh529xi/0KXiMH7GJYPVulzBxUdjBHazmYZuftlTtTj3bIav6c/Q\nhUtX4zaRx35ETMq4\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02287225a960d967cfe52ac126a50fd79fa85a586397c0b298c8adfaf138317b0f794b24f53bd920c1cf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04acf240130d47d4a57d606595f989129fea7e9744b1e53f5ce679c244c85af35c618607e2ecce1a431b696a7959fe30d049100dd54258181b08a2fe442e41ff29523c11a3e01028eb64b321c2b702579c", "wx": "00acf240130d47d4a57d606595f989129fea7e9744b1e53f5ce679c244c85af35c618607e2ecce1a43", "wy": "1b696a7959fe30d049100dd54258181b08a2fe442e41ff29523c11a3e01028eb64b321c2b702579c"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004acf240130d47d4a57d606595f989129fea7e9744b1e53f5ce679c244c85af35c618607e2ecce1a431b696a7959fe30d049100dd54258181b08a2fe442e41ff29523c11a3e01028eb64b321c2b702579c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABKzyQBMNR9SlfWBllfmJEp/qfpdE\nseU/XOZ5wkTIWvNcYYYH4uzOGkMbaWp5Wf4w0EkQDdVCWBgbCKL+RC5B/ylSPBGj\n4BAo62SzIcK3Alec\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900874f311b6b9ac74fc34c60c0941873651b3c0ec1d097a7861e0c7fbec3226f23a5e2c929d856ecb3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0446243b39e77639ac19e9be53669317d9598e03ec30a0cf6930f800009833826a59ade5321933ff2f69d770b978ccc36c90b748e5010636e7004ddc19885da7bb90dbfad479fc52dce4b9281405f1c6bd", "wx": "46243b39e77639ac19e9be53669317d9598e03ec30a0cf6930f800009833826a59ade5321933ff2f", "wy": "69d770b978ccc36c90b748e5010636e7004ddc19885da7bb90dbfad479fc52dce4b9281405f1c6bd"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000446243b39e77639ac19e9be53669317d9598e03ec30a0cf6930f800009833826a59ade5321933ff2f69d770b978ccc36c90b748e5010636e7004ddc19885da7bb90dbfad479fc52dce4b9281405f1c6bd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABEYkOznndjmsGem+U2aTF9lZjgPs\nMKDPaTD4AACYM4JqWa3lMhkz/y9p13C5eMzDbJC3SOUBBjbnAE3cGYhdp7uQ2/rU\nefxS3OS5KBQF8ca9\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022813753ac90fbc7edfdcb32e1697fdfd41b1fb59c5ad177e96feacc87522ef928de80a60bb0f32e7e2", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04b4b9b6ba3c0e7509c275894e84e818d71de14577bdb4bf0b8e5e1332d1087f3c333b73e8ab75f2c94f33d0e2ab342d2e1968ce3e1c47be87e39ee88273ae4cf777869d3a1703b63a983d2d43c59303e5", "wx": "00b4b9b6ba3c0e7509c275894e84e818d71de14577bdb4bf0b8e5e1332d1087f3c333b73e8ab75f2c9", "wy": "4f33d0e2ab342d2e1968ce3e1c47be87e39ee88273ae4cf777869d3a1703b63a983d2d43c59303e5"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004b4b9b6ba3c0e7509c275894e84e818d71de14577bdb4bf0b8e5e1332d1087f3c333b73e8ab75f2c94f33d0e2ab342d2e1968ce3e1c47be87e39ee88273ae4cf777869d3a1703b63a983d2d43c59303e5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABLS5tro8DnUJwnWJToToGNcd4UV3\nvbS/C45eEzLRCH88Mztz6Kt18slPM9DiqzQtLhlozj4cR76H457ognOuTPd3hp06\nFwO2Opg9LUPFkwPl\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 348, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022826ea75921f78fdbfb9665c2d2ffbfa8363f6b38b5a2efd2dfd5990ea45df251bd014c1761e65cfc4", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0403015b3ca67683467c79446d4b93d10978330856eee40d6d58683ac73500ae315c5b582351c4226b18d89561d3ffa0f9311aa616547f7eb1d36e73a6cc4bd230df34a1f319be66bcb2fb0e1f68cc192e", "wx": "03015b3ca67683467c79446d4b93d10978330856eee40d6d58683ac73500ae315c5b582351c4226b", "wy": "18d89561d3ffa0f9311aa616547f7eb1d36e73a6cc4bd230df34a1f319be66bcb2fb0e1f68cc192e"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000403015b3ca67683467c79446d4b93d10978330856eee40d6d58683ac73500ae315c5b582351c4226b18d89561d3ffa0f9311aa616547f7eb1d36e73a6cc4bd230df34a1f319be66bcb2fb0e1f68cc192e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABAMBWzymdoNGfHlEbUuT0Ql4MwhW\n7uQNbVhoOsc1AK4xXFtYI1HEImsY2JVh0/+g+TEaphZUf36x025zpsxL0jDfNKHz\nGb5mvLL7Dh9ozBku\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0229009fee192930d30502c05e56adf086ecd13a92cd43ce0c72ea65ead43667890ae19be835333c32c5f0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04187d93f84a0e6043f097d0a87f8dca07739cf44548a7d3403e039e49c4c51285482975af54ec056c0623c57538fefb7231d619bbefd4cab373a54b361354e586b1d9981a8835e9c6beab082cb93e13b6", "wx": "187d93f84a0e6043f097d0a87f8dca07739cf44548a7d3403e039e49c4c51285482975af54ec056c", "wy": "0623c57538fefb7231d619bbefd4cab373a54b361354e586b1d9981a8835e9c6beab082cb93e13b6"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004187d93f84a0e6043f097d0a87f8dca07739cf44548a7d3403e039e49c4c51285482975af54ec056c0623c57538fefb7231d619bbefd4cab373a54b361354e586b1d9981a8835e9c6beab082cb93e13b6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBh9k/hKDmBD8JfQqH+NygdznPRF\nSKfTQD4DnknExRKFSCl1r1TsBWwGI8V1OP77cjHWGbvv1Mqzc6VLNhNU5Yax2Zga\niDXpxr6rCCy5PhO2\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 350, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900963f97cb35a321df62fc219eb2f3703949c483165d06db13c403080a86c1e5d9b43d2e8dd9643cde", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "040855cc20351126b38f934fbb56c302f62a360e62493c2d529fb87caea0d71bfdaf5fcc3368d495fd1ce7578610cbec465398b2c1238b3e23b9e29b476196106430d76316aaf29937ace658b69c8bfb99", "wx": "0855cc20351126b38f934fbb56c302f62a360e62493c2d529fb87caea0d71bfdaf5fcc3368d495fd", "wy": "1ce7578610cbec465398b2c1238b3e23b9e29b476196106430d76316aaf29937ace658b69c8bfb99"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200040855cc20351126b38f934fbb56c302f62a360e62493c2d529fb87caea0d71bfdaf5fcc3368d495fd1ce7578610cbec465398b2c1238b3e23b9e29b476196106430d76316aaf29937ace658b69c8bfb99", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABAhVzCA1ESazj5NPu1bDAvYqNg5i\nSTwtUp+4fK6g1xv9r1/MM2jUlf0c51eGEMvsRlOYssEjiz4jueKbR2GWEGQw12MW\nqvKZN6zmWLaci/uZ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022813dbff9e667e7bcd44950226f93b09738e793517c02886ae9f2b3dededa756c9049ab9a46bc7c93e", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0494c54919004079be0db4c92dc1fc947d79eb0f8e869d94813886ada4254f1dadb4d87a6112a5833686d8b5beac00fafd647ef8b631e899a6a8b72a511d4f50ce156648ad9cb708fb2fb2c638fdb9f332", "wx": "0094c54919004079be0db4c92dc1fc947d79eb0f8e869d94813886ada4254f1dadb4d87a6112a58336", "wy": "0086d8b5beac00fafd647ef8b631e899a6a8b72a511d4f50ce156648ad9cb708fb2fb2c638fdb9f332"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000494c54919004079be0db4c92dc1fc947d79eb0f8e869d94813886ada4254f1dadb4d87a6112a5833686d8b5beac00fafd647ef8b631e899a6a8b72a511d4f50ce156648ad9cb708fb2fb2c638fdb9f332", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABJTFSRkAQHm+DbTJLcH8lH156w+O\nhp2UgTiGraQlTx2ttNh6YRKlgzaG2LW+rAD6/WR++LYx6JmmqLcqUR1PUM4VZkit\nnLcI+y+yxjj9ufMy\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 352, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900852466cef316992b3ca25cc54b7f4fda2e8a819e7c4b040543e94f9caca02937681c2019bb49ee43", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "042bca76043728b5eeefde89d25acdf2e0b160c5ae0ccdab6bd3baa479f17753c3c000ccf8ba8623de92f0c2d68a1bd405e449823fe63b21402aef3e9a017dcbc30af18bcc79a85264834398c72fa2bb16", "wx": "2bca76043728b5eeefde89d25acdf2e0b160c5ae0ccdab6bd3baa479f17753c3c000ccf8ba8623de", "wy": "0092f0c2d68a1bd405e449823fe63b21402aef3e9a017dcbc30af18bcc79a85264834398c72fa2bb16"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200042bca76043728b5eeefde89d25acdf2e0b160c5ae0ccdab6bd3baa479f17753c3c000ccf8ba8623de92f0c2d68a1bd405e449823fe63b21402aef3e9a017dcbc30af18bcc79a85264834398c72fa2bb16", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABCvKdgQ3KLXu796J0lrN8uCxYMWu\nDM2ra9O6pHnxd1PDwADM+LqGI96S8MLWihvUBeRJgj/mOyFAKu8+mgF9y8MK8YvM\neahSZINDmMcvorsW\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 353, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0228634bfe1800786b0256e90ac2de272f41c85e0976c0caa1691bd835a5a444b1ed1705a0361ae6ee36", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "041c013b3a3260ccfb53e3f6ce93e6984865dc8e1293e92301f4cb3a554bd5da8a53ee101b3e1a300997d2901e26729303e1cb93a8b72dc2afc90ff5b44fd5b6624455487974ed71c7833eff03cc128d0c", "wx": "1c013b3a3260ccfb53e3f6ce93e6984865dc8e1293e92301f4cb3a554bd5da8a53ee101b3e1a3009", "wy": "0097d2901e26729303e1cb93a8b72dc2afc90ff5b44fd5b6624455487974ed71c7833eff03cc128d0c"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200041c013b3a3260ccfb53e3f6ce93e6984865dc8e1293e92301f4cb3a554bd5da8a53ee101b3e1a300997d2901e26729303e1cb93a8b72dc2afc90ff5b44fd5b6624455487974ed71c7833eff03cc128d0c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBwBOzoyYMz7U+P2zpPmmEhl3I4S\nk+kjAfTLOlVL1dqKU+4QGz4aMAmX0pAeJnKTA+HLk6i3LcKvyQ/1tE/VtmJEVUh5\ndO1xx4M+/wPMEo0M\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 354, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900c697fc3000f0d604add21585bc4e5e8390bc12ed819542d237b06b4b488963da2e0b406c35cddc6c", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04624bec4520e6044abed1eee4964668775181464c5d6bf5a8b539f1156f3248c02271bf9425b966b547f406bcc143226d814cdb988d76412ad186bdeeb869ad78a32fe87c76f2545447ddf8fbd0430811", "wx": "624bec4520e6044abed1eee4964668775181464c5d6bf5a8b539f1156f3248c02271bf9425b966b5", "wy": "47f406bcc143226d814cdb988d76412ad186bdeeb869ad78a32fe87c76f2545447ddf8fbd0430811"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004624bec4520e6044abed1eee4964668775181464c5d6bf5a8b539f1156f3248c02271bf9425b966b547f406bcc143226d814cdb988d76412ad186bdeeb869ad78a32fe87c76f2545447ddf8fbd0430811", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABGJL7EUg5gRKvtHu5JZGaHdRgUZM\nXWv1qLU58RVvMkjAInG/lCW5ZrVH9Aa8wUMibYFM25iNdkEq0Ya97rhprXijL+h8\ndvJUVEfd+PvQQwgR\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "edge case for u1", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02285685b327caacf14f237ea7e9c873ad5f5f8a4cbe8bd0d19826407228fe47bcddbe7f8b470bef3791", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "041b2b2738e3055d1596f64176cf0ac381b3a8178a2f021403350218fa18f9f860c1bba39fc524bc8209fbafca1afc5af7598b878d69cb875be0d39f41ff01b09388693eb310adc9d4836e226c23677e51", "wx": "1b2b2738e3055d1596f64176cf0ac381b3a8178a2f021403350218fa18f9f860c1bba39fc524bc82", "wy": "09fbafca1afc5af7598b878d69cb875be0d39f41ff01b09388693eb310adc9d4836e226c23677e51"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200041b2b2738e3055d1596f64176cf0ac381b3a8178a2f021403350218fa18f9f860c1bba39fc524bc8209fbafca1afc5af7598b878d69cb875be0d39f41ff01b09388693eb310adc9d4836e226c23677e51", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBsrJzjjBV0VlvZBds8Kw4GzqBeK\nLwIUAzUCGPoY+fhgwbujn8UkvIIJ+6/KGvxa91mLh41py4db4NOfQf8BsJOIaT6z\nEK3J1INuImwjZ35R\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 356, "comment": "edge case for u1", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900a2c1f84088120fce85fecf81f0ecc00729f4199ebba0d5b5eda190001000b43168db254b8ef32a70", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0440902bf6b239d2f3588260e9d7f512253fa44f308a0ab81dff05b8fa2e25814d65c2018d49390aae016f8ae5691938402adc0ffa29bb87ef0af0ecf3cd446d97c3e8d12b3b09eb78909c1b91b1b8785f", "wx": "40902bf6b239d2f3588260e9d7f512253fa44f308a0ab81dff05b8fa2e25814d65c2018d49390aae", "wy": "016f8ae5691938402adc0ffa29bb87ef0af0ecf3cd446d97c3e8d12b3b09eb78909c1b91b1b8785f"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000440902bf6b239d2f3588260e9d7f512253fa44f308a0ab81dff05b8fa2e25814d65c2018d49390aae016f8ae5691938402adc0ffa29bb87ef0af0ecf3cd446d97c3e8d12b3b09eb78909c1b91b1b8785f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABECQK/ayOdLzWIJg6df1EiU/pE8w\nigq4Hf8FuPouJYFNZcIBjUk5Cq4Bb4rlaRk4QCrcD/opu4fvCvDs881EbZfD6NEr\nOwnreJCcG5GxuHhf\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 357, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0228711f6d0abce96fe7f5bed2ca4600a021fdda9a8c922fb0e10f180f97fa2cc84dd785c71e6c41dbaf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04726533e26773ac720a115b02de89ac15966677e239b7c577a1c15b81027b1feb73e673601e211aa92accb585bc06cc274b61c9e614746edd248d1cccf8d8b1ab4bc15cc58cdf116065ce9767f2a3223d", "wx": "726533e26773ac720a115b02de89ac15966677e239b7c577a1c15b81027b1feb73e673601e211aa9", "wy": "2accb585bc06cc274b61c9e614746edd248d1cccf8d8b1ab4bc15cc58cdf116065ce9767f2a3223d"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004726533e26773ac720a115b02de89ac15966677e239b7c577a1c15b81027b1feb73e673601e211aa92accb585bc06cc274b61c9e614746edd248d1cccf8d8b1ab4bc15cc58cdf116065ce9767f2a3223d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABHJlM+Jnc6xyChFbAt6JrBWWZnfi\nObfFd6HBW4ECex/rc+ZzYB4hGqkqzLWFvAbMJ0thyeYUdG7dJI0czPjYsatLwVzF\njN8RYGXOl2fyoyI9\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022802a6eb408443d24e96be4ca0278442a8a426087f9beb03ffe5526162bf1dc30434cf7ea79574b19b", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0453c3da4de14f7d35775f9beca6d53ee78dac73cd3f18c6fbf709b4ffa7dd3e70b436409b9b285d1c2a5b60e457e58422c959142b5ecff236dfd76c99c3018cea904058099a13647db08898cfd0509e84", "wx": "53c3da4de14f7d35775f9beca6d53ee78dac73cd3f18c6fbf709b4ffa7dd3e70b436409b9b285d1c", "wy": "2a5b60e457e58422c959142b5ecff236dfd76c99c3018cea904058099a13647db08898cfd0509e84"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000453c3da4de14f7d35775f9beca6d53ee78dac73cd3f18c6fbf709b4ffa7dd3e70b436409b9b285d1c2a5b60e457e58422c959142b5ecff236dfd76c99c3018cea904058099a13647db08898cfd0509e84", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABFPD2k3hT301d1+b7KbVPueNrHPN\nPxjG+/cJtP+n3T5wtDZAm5soXRwqW2DkV+WEIslZFCtez/I239dsmcMBjOqQQFgJ\nmhNkfbCImM/QUJ6E\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900b6ba1aecd240debe77213a4228b125603671c9d5147b6c0b36dd23e42b7cb5078a1b8fdf1b98b93a", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04cd24ae7f7523adf859db92e51d48746b8b2f868620898a9c42f8bae8173e3646f586fd818712430e55b12d59f7344168f796fe59c026eaaa139745a8ace97df1d5c6bcc21f0cfa6860f9c8c75f391629", "wx": "00cd24ae7f7523adf859db92e51d48746b8b2f868620898a9c42f8bae8173e3646f586fd818712430e", "wy": "55b12d59f7344168f796fe59c026eaaa139745a8ace97df1d5c6bcc21f0cfa6860f9c8c75f391629"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004cd24ae7f7523adf859db92e51d48746b8b2f868620898a9c42f8bae8173e3646f586fd818712430e55b12d59f7344168f796fe59c026eaaa139745a8ace97df1d5c6bcc21f0cfa6860f9c8c75f391629", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABM0krn91I634WduS5R1IdGuLL4aG\nIImKnEL4uugXPjZG9Yb9gYcSQw5VsS1Z9zRBaPeW/lnAJuqqE5dFqKzpffHVxrzC\nHwz6aGD5yMdfORYp\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 360, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900957b383ba1cebf5ca579ef6ed10027988f8424f42ffbea2e51b3340df9f8c3c60b558d6dc2df10f3", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "044db460209972c8e9c365119546ac457add157f0c4d2b3cd65c635dcaeca617029cabf75c06101bb69ef8b7626e6b2f9845b0086d2a964018b9b25eb8db426bc90694cc614b7602b1fd6087a9a71cbf1f", "wx": "4db460209972c8e9c365119546ac457add157f0c4d2b3cd65c635dcaeca617029cabf75c06101bb6", "wy": "009ef8b7626e6b2f9845b0086d2a964018b9b25eb8db426bc90694cc614b7602b1fd6087a9a71cbf1f"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200044db460209972c8e9c365119546ac457add157f0c4d2b3cd65c635dcaeca617029cabf75c06101bb69ef8b7626e6b2f9845b0086d2a964018b9b25eb8db426bc90694cc614b7602b1fd6087a9a71cbf1f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABE20YCCZcsjpw2URlUasRXrdFX8M\nTSs81lxjXcrsphcCnKv3XAYQG7ae+LdibmsvmEWwCG0qlkAYubJeuNtCa8kGlMxh\nS3YCsf1gh6mnHL8f\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02290098ff1db1b9affa33a2e53c684d3f07611772405e8c200f2af2afa9e53c6e8ef30cc143b3f5ff7fb0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "043e7ab850840d75987d33837ead46499ce433f3fce67383b2e325dd2fc7e0f500769cbb67b4550a28c30314487a87094750334499dbfbeb2d5cb976ee2d47997321597a41124a038fe867be0ef668c4ce", "wx": "3e7ab850840d75987d33837ead46499ce433f3fce67383b2e325dd2fc7e0f500769cbb67b4550a28", "wy": "00c30314487a87094750334499dbfbeb2d5cb976ee2d47997321597a41124a038fe867be0ef668c4ce"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200043e7ab850840d75987d33837ead46499ce433f3fce67383b2e325dd2fc7e0f500769cbb67b4550a28c30314487a87094750334499dbfbeb2d5cb976ee2d47997321597a41124a038fe867be0ef668c4ce", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABD56uFCEDXWYfTODfq1GSZzkM/P8\n5nODsuMl3S/H4PUAdpy7Z7RVCijDAxRIeocJR1AzRJnb++stXLl27i1HmXMhWXpB\nEkoDj+hnvg72aMTO\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02285e9ff4433ca3a4af648e0071c87c2e5c3554b11761b10bb2b81725028a56c4fc92f1320ca7396c4f", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "047acc919934b0fd90011cd96f59ddba52e12094dac18a2cadcb03a0f31ac72d3fd5984a11e9220f8c0629bc5f3f0dabbd3fdd30f47a0a5bea3052892f8e50a4033be4795b32c6671d141b473080e57911", "wx": "7acc919934b0fd90011cd96f59ddba52e12094dac18a2cadcb03a0f31ac72d3fd5984a11e9220f8c", "wy": "0629bc5f3f0dabbd3fdd30f47a0a5bea3052892f8e50a4033be4795b32c6671d141b473080e57911"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200047acc919934b0fd90011cd96f59ddba52e12094dac18a2cadcb03a0f31ac72d3fd5984a11e9220f8c0629bc5f3f0dabbd3fdd30f47a0a5bea3052892f8e50a4033be4795b32c6671d141b473080e57911", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABHrMkZk0sP2QARzZb1ndulLhIJTa\nwYosrcsDoPMaxy0/1ZhKEekiD4wGKbxfPw2rvT/dMPR6ClvqMFKJL45QpAM75Hlb\nMsZnHRQbRzCA5XkR\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900c76ce53560163f157b11e4d05c61540a5df6b8241cbd3ba7d911a7541eec55e986ebf811ae50a8b9", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0412c163fe25cb79ad59c76b5280dc6706a42c58596230bf7ba7206e6ce2b467e1b7a7063e59b0bed6ccbeaf22accb1ac41ed43ac775b97aea3a688e2f096c3a5e59f868bc919da5ce252cf5d712e7de40", "wx": "12c163fe25cb79ad59c76b5280dc6706a42c58596230bf7ba7206e6ce2b467e1b7a7063e59b0bed6", "wy": "00ccbeaf22accb1ac41ed43ac775b97aea3a688e2f096c3a5e59f868bc919da5ce252cf5d712e7de40"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000412c163fe25cb79ad59c76b5280dc6706a42c58596230bf7ba7206e6ce2b467e1b7a7063e59b0bed6ccbeaf22accb1ac41ed43ac775b97aea3a688e2f096c3a5e59f868bc919da5ce252cf5d712e7de40", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABBLBY/4ly3mtWcdrUoDcZwakLFhZ\nYjC/e6cgbmzitGfht6cGPlmwvtbMvq8irMsaxB7UOsd1uXrqOmiOLwlsOl5Z+Gi8\nkZ2lziUs9dcS595A\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02284218a45116ea65b283cc7d90a510f077b1b09eddbcfca3e7d2896b869dd3ba556c4f10590b0e08cf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "046960bfcddd0021fcb8a3d7aa85f96cf360a7113e3824508525021f83e3085989c35e0c57726503305c1275b9d8b5199d461fcb9d34f8857b65a140462fd5cdc7a33e5cf7f4e2d08a5a34d9ae00b2939a", "wx": "6960bfcddd0021fcb8a3d7aa85f96cf360a7113e3824508525021f83e3085989c35e0c5772650330", "wy": "5c1275b9d8b5199d461fcb9d34f8857b65a140462fd5cdc7a33e5cf7f4e2d08a5a34d9ae00b2939a"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200046960bfcddd0021fcb8a3d7aa85f96cf360a7113e3824508525021f83e3085989c35e0c57726503305c1275b9d8b5199d461fcb9d34f8857b65a140462fd5cdc7a33e5cf7f4e2d08a5a34d9ae00b2939a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABGlgv83dACH8uKPXqoX5bPNgpxE+\nOCRQhSUCH4PjCFmJw14MV3JlAzBcEnW52LUZnUYfy500+IV7ZaFARi/VzcejPlz3\n9OLQilo02a4AspOa\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02285e5f32423bad3644be718d8195341362c9cba52b330f913b1521af6e5e3eb2069421b05dcac299f7", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0466ad2c26012388c8b9046a466b87bd71b64ab81b54cffc5a611f4b7581ad8365edd08e6afd4a52f61a3066c0b3b703ddce746239a4d3dbf1938945f15ea9497bbfc45b389e130350b9945922b87ce374", "wx": "66ad2c26012388c8b9046a466b87bd71b64ab81b54cffc5a611f4b7581ad8365edd08e6afd4a52f6", "wy": "1a3066c0b3b703ddce746239a4d3dbf1938945f15ea9497bbfc45b389e130350b9945922b87ce374"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000466ad2c26012388c8b9046a466b87bd71b64ab81b54cffc5a611f4b7581ad8365edd08e6afd4a52f61a3066c0b3b703ddce746239a4d3dbf1938945f15ea9497bbfc45b389e130350b9945922b87ce374", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABGatLCYBI4jIuQRqRmuHvXG2Srgb\nVM/8WmEfS3WBrYNl7dCOav1KUvYaMGbAs7cD3c50Yjmk09vxk4lF8V6pSXu/xFs4\nnhMDULmUWSK4fON0\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900ac07aaade5c5fb2fe5a18bbefd262e0e439fd68e0a317db06ff4ba623a2a03114ec5b6e084171058", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "040cfa6e3838d8113a24d87db97417d68f00c426e9b8550d8a951fed531572e7cca66ffe0ae176ff0e312fa02e5cc77c21f4a6630e25bcb987dc1eef14aec80c15b9b292e3acfb30bc2c0438f0a9831c07", "wx": "0cfa6e3838d8113a24d87db97417d68f00c426e9b8550d8a951fed531572e7cca66ffe0ae176ff0e", "wy": "312fa02e5cc77c21f4a6630e25bcb987dc1eef14aec80c15b9b292e3acfb30bc2c0438f0a9831c07"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200040cfa6e3838d8113a24d87db97417d68f00c426e9b8550d8a951fed531572e7cca66ffe0ae176ff0e312fa02e5cc77c21f4a6630e25bcb987dc1eef14aec80c15b9b292e3acfb30bc2c0438f0a9831c07", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABAz6bjg42BE6JNh9uXQX1o8AxCbp\nuFUNipUf7VMVcufMpm/+CuF2/w4xL6AuXMd8IfSmYw4lvLmH3B7vFK7IDBW5spLj\nrPswvCwEOPCpgxwH\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0228311f6d0abce96fe7f5bed2ca4600a021fdda9a8c922fb0e10f180f97fa2cc84dd785c71e6c41dbb1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "043dabbc36a455ba07432da1aa7239aefdefb72ac09313c3a7f3439850f602543eb4affc5d8225b5eece48e2f67e82d448b3d8b9b0fc200832a3d1ac88058872762fcbf027e9f5705d8f5812e507dae125", "wx": "3dabbc36a455ba07432da1aa7239aefdefb72ac09313c3a7f3439850f602543eb4affc5d8225b5ee", "wy": "00ce48e2f67e82d448b3d8b9b0fc200832a3d1ac88058872762fcbf027e9f5705d8f5812e507dae125"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200043dabbc36a455ba07432da1aa7239aefdefb72ac09313c3a7f3439850f602543eb4affc5d8225b5eece48e2f67e82d448b3d8b9b0fc200832a3d1ac88058872762fcbf027e9f5705d8f5812e507dae125", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABD2rvDakVboHQy2hqnI5rv3vtyrA\nkxPDp/NDmFD2AlQ+tK/8XYIlte7OSOL2foLUSLPYubD8IAgyo9GsiAWIcnYvy/An\n6fVwXY9YEuUH2uEl\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0228623eda1579d2dfcfeb7da5948c014043fbb53519245f61c21e301f2ff459909baf0b8e3cd883b762", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "048a9658dc5f91aa577706f1d91d2252cb0d09f2053e561129105c7f37ddb2f972b3224f12cf9e43fe08782ec6105f4c06587eb1ececb2f4f4a04e236304dc75eb2efff0be66b977fa804af73bfcbac78e", "wx": "008a9658dc5f91aa577706f1d91d2252cb0d09f2053e561129105c7f37ddb2f972b3224f12cf9e43fe", "wy": "08782ec6105f4c06587eb1ececb2f4f4a04e236304dc75eb2efff0be66b977fa804af73bfcbac78e"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200048a9658dc5f91aa577706f1d91d2252cb0d09f2053e561129105c7f37ddb2f972b3224f12cf9e43fe08782ec6105f4c06587eb1ececb2f4f4a04e236304dc75eb2efff0be66b977fa804af73bfcbac78e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABIqWWNxfkapXdwbx2R0iUssNCfIF\nPlYRKRBcfzfdsvlysyJPEs+eQ/4IeC7GEF9MBlh+sezssvT0oE4jYwTcdesu//C+\nZrl3+oBK9zv8useO\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "edge case for u2", "msg": "313233343030", "sig": "305502287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022900935e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59313", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0406b43bb9587ee158ad5752d1ad11f6f0f5e316ad21937cdd9253f3844857f0a25e7b677bbf9994449705362334bdceb68ae6a584640c95cb10789b19953f5e119973eed735177aabfcb263fc8ef5ef97", "wx": "06b43bb9587ee158ad5752d1ad11f6f0f5e316ad21937cdd9253f3844857f0a25e7b677bbf999444", "wy": "009705362334bdceb68ae6a584640c95cb10789b19953f5e119973eed735177aabfcb263fc8ef5ef97"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000406b43bb9587ee158ad5752d1ad11f6f0f5e316ad21937cdd9253f3844857f0a25e7b677bbf9994449705362334bdceb68ae6a584640c95cb10789b19953f5e119973eed735177aabfcb263fc8ef5ef97", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABAa0O7lYfuFYrVdS0a0R9vD14xat\nIZN83ZJT84RIV/CiXntne7+ZlESXBTYjNL3OtormpYRkDJXLEHibGZU/XhGZc+7X\nNRd6q/yyY/yO9e+X\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "edge case for u2", "msg": "313233343030", "sig": "305402287ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02285b5d0d7669206f5f3b909d21145892b01b38e4ea8a3db6059b6e91f215be5a83c50dc7ef8dcc5c9d", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f423999c08367945495d933f206927a2b7f5b74b22f973a898355aa2f7e295e06ef3a4f561546db97f79afa", "wx": "568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f423999", "wy": "00c08367945495d933f206927a2b7f5b74b22f973a898355aa2f7e295e06ef3a4f561546db97f79afa"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f423999c08367945495d933f206927a2b7f5b74b22f973a898355aa2f7e295e06ef3a4f561546db97f79afa", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABFaIA9oHHmufQ4DjmVTysPwPW7WK\nD2i10aQsfpBS7OKg/HrK3A9COZnAg2eUVJXZM/IGknorf1t0si+XOomDVaovfile\nBu86T1YVRtuX95r6\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "point duplication during verification", "msg": "313233343030", "sig": "30560229009563bd68545ccd185ae724d8efcd4cc23234934eef10f280792b2f930c97a6c1e00829a8b975b9ee022900c5e79c49abb135129f0636e18e2e73bced30855deeba1477d9521b33a32865155177d946e1babcb4", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f42399912dadf8be2267683ef35e5e4a68284f14760386c6d70b8452014908e71a4b1d9a6becbd659bb932d", "wx": "568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f423999", "wy": "12dadf8be2267683ef35e5e4a68284f14760386c6d70b8452014908e71a4b1d9a6becbd659bb932d"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004568803da071e6b9f4380e39954f2b0fc0f5bb58a0f68b5d1a42c7e9052ece2a0fc7acadc0f42399912dadf8be2267683ef35e5e4a68284f14760386c6d70b8452014908e71a4b1d9a6becbd659bb932d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABFaIA9oHHmufQ4DjmVTysPwPW7WK\nD2i10aQsfpBS7OKg/HrK3A9COZkS2t+L4iZ2g+815eSmgoTxR2A4bG1wuEUgFJCO\ncaSx2aa+y9ZZu5Mt\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "duplication bug", "msg": "313233343030", "sig": "30560229009563bd68545ccd185ae724d8efcd4cc23234934eef10f280792b2f930c97a6c1e00829a8b975b9ee022900c5e79c49abb135129f0636e18e2e73bced30855deeba1477d9521b33a32865155177d946e1babcb4", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "045d1a100118bd3610f10e13b5adcc7a90a37f4f988cfa4e22cca77e88444b00216dcfe5f68418d3425d5b88c9b8c92b3dec7f7bcc688a6d18e6cdeb9176150d4b1062a832c8a3bc377f8d7e98b1db0b9d", "wx": "5d1a100118bd3610f10e13b5adcc7a90a37f4f988cfa4e22cca77e88444b00216dcfe5f68418d342", "wy": "5d5b88c9b8c92b3dec7f7bcc688a6d18e6cdeb9176150d4b1062a832c8a3bc377f8d7e98b1db0b9d"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200045d1a100118bd3610f10e13b5adcc7a90a37f4f988cfa4e22cca77e88444b00216dcfe5f68418d3425d5b88c9b8c92b3dec7f7bcc688a6d18e6cdeb9176150d4b1062a832c8a3bc377f8d7e98b1db0b9d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABF0aEAEYvTYQ8Q4Tta3MepCjf0+Y\njPpOIsynfohESwAhbc/l9oQY00JdW4jJuMkrPex/e8xoim0Y5s3rkXYVDUsQYqgy\nyKO8N3+Nfpix2wud\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "305402284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c197310502282a460e39a48c0ff193727e795d339347984ff65457b636ed6f74d627fc8144fb81504445742783d0", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04cca9ac38de5b36cf79d8e415cb729e685e0bbdafe161c5e7ecfa4177e826e815d66526aa5daf32279b7799bcefc6b5d8d09ff1a0739fd423188126f80af703314da0d26ba6714aa197a6582c36b0f05d", "wx": "00cca9ac38de5b36cf79d8e415cb729e685e0bbdafe161c5e7ecfa4177e826e815d66526aa5daf3227", "wy": "009b7799bcefc6b5d8d09ff1a0739fd423188126f80af703314da0d26ba6714aa197a6582c36b0f05d"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004cca9ac38de5b36cf79d8e415cb729e685e0bbdafe161c5e7ecfa4177e826e815d66526aa5daf32279b7799bcefc6b5d8d09ff1a0739fd423188126f80af703314da0d26ba6714aa197a6582c36b0f05d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABMyprDjeWzbPedjkFctynmheC72v\n4WHF5+z6QXfoJugV1mUmql2vMiebd5m878a12NCf8aBzn9QjGIEm+Ar3AzFNoNJr\npnFKoZemWCw2sPBd\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "305402282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de802284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04cceaa1203bdcbe15e20434d624f0ed9aca81d4c82f840bba3a86c6756262aa37efed62f5f1d097f7457057b98d2b9ea6bd28581d40ac20fcc9d536a117769203447bf41e10ce4da1ad794ca20f8ee146", "wx": "00cceaa1203bdcbe15e20434d624f0ed9aca81d4c82f840bba3a86c6756262aa37efed62f5f1d097f7", "wy": "457057b98d2b9ea6bd28581d40ac20fcc9d536a117769203447bf41e10ce4da1ad794ca20f8ee146"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004cceaa1203bdcbe15e20434d624f0ed9aca81d4c82f840bba3a86c6756262aa37efed62f5f1d097f7457057b98d2b9ea6bd28581d40ac20fcc9d536a117769203447bf41e10ce4da1ad794ca20f8ee146", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABMzqoSA73L4V4gQ01iTw7ZrKgdTI\nL4QLujqGxnViYqo37+1i9fHQl/dFcFe5jSuepr0oWB1ArCD8ydU2oRd2kgNEe/Qe\nEM5Noa15TKIPjuFG\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "305402282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de802281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04cc9ed25f13e94a6ebd531f3b142fabc4ed522dc6127861528830c6787d6ecfd4b704e1774e9118ed68e4e172f93f1d5b8d7860fae2c115f4aa0daaf6df5ca3809d79acfdb9ed2be19995658d2f44d235", "wx": "00cc9ed25f13e94a6ebd531f3b142fabc4ed522dc6127861528830c6787d6ecfd4b704e1774e9118ed", "wy": "68e4e172f93f1d5b8d7860fae2c115f4aa0daaf6df5ca3809d79acfdb9ed2be19995658d2f44d235"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004cc9ed25f13e94a6ebd531f3b142fabc4ed522dc6127861528830c6787d6ecfd4b704e1774e9118ed68e4e172f93f1d5b8d7860fae2c115f4aa0daaf6df5ca3809d79acfdb9ed2be19995658d2f44d235", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABMye0l8T6UpuvVMfOxQvq8TtUi3G\nEnhhUogwxnh9bs/UtwThd06RGO1o5OFy+T8dW414YPriwRX0qg2q9t9co4Cdeaz9\nue0r4ZmVZY0vRNI1\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "305402282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de802282a460e39a48c0ff193727e795d339347984ff65457b636ed6f74d627fc8144fb81504445742783d0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "046a3cae0edc8455ae16b5eeb6569603bdaeb5793699e85d372857f1319c70dd525b1ea30a0f5c7b44075537cd822d9ee2d0e7a49c4c3141445d01b789bbcad02ec4249c2e2355d61db5581dbdb342c993", "wx": "6a3cae0edc8455ae16b5eeb6569603bdaeb5793699e85d372857f1319c70dd525b1ea30a0f5c7b44", "wy": "075537cd822d9ee2d0e7a49c4c3141445d01b789bbcad02ec4249c2e2355d61db5581dbdb342c993"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200046a3cae0edc8455ae16b5eeb6569603bdaeb5793699e85d372857f1319c70dd525b1ea30a0f5c7b44075537cd822d9ee2d0e7a49c4c3141445d01b789bbcad02ec4249c2e2355d61db5581dbdb342c993", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABGo8rg7chFWuFrXutlaWA72utXk2\nmehdNyhX8TGccN1SWx6jCg9ce0QHVTfNgi2e4tDnpJxMMUFEXQG3ibvK0C7EJJwu\nI1XWHbVYHb2zQsmT\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "305502282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de8022900a91838e692303fc64dc9f9e574ce4d1e613fd9515ed8dbb5bdd3589ff20513ee05411115d09e0f41", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "044efb5161ca1a1eeb59a5fc39cd521d40bd3e034512fa2a1eaf3b7e92bb9e95c06a4c726ceccdf9bc6bfa801b067137f1b6b4506041130b4d402d90087ad005e3f652e1d91c9d344cd1eeffff61d3a306", "wx": "4efb5161ca1a1eeb59a5fc39cd521d40bd3e034512fa2a1eaf3b7e92bb9e95c06a4c726ceccdf9bc", "wy": "6bfa801b067137f1b6b4506041130b4d402d90087ad005e3f652e1d91c9d344cd1eeffff61d3a306"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200044efb5161ca1a1eeb59a5fc39cd521d40bd3e034512fa2a1eaf3b7e92bb9e95c06a4c726ceccdf9bc6bfa801b067137f1b6b4506041130b4d402d90087ad005e3f652e1d91c9d344cd1eeffff61d3a306", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABE77UWHKGh7rWaX8Oc1SHUC9PgNF\nEvoqHq87fpK7npXAakxybOzN+bxr+oAbBnE38ba0UGBBEwtNQC2QCHrQBeP2UuHZ\nHJ00TNHu//9h06MG\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "305502282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de8022900b52c3cf70a58445477eab051464ac05768321fb29c7aa242b9194cab5ebc4c35e10edb72cd3ba2a1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "045f658687e6a542a91d893b48776a86d528fd399781bbb9305be0797e3a6f36118ae19e68dc1673f6676e536c7897a0002f9664929631f418c4537d23749220c50a32121c434dcad2a6cdc203cd035a32", "wx": "5f658687e6a542a91d893b48776a86d528fd399781bbb9305be0797e3a6f36118ae19e68dc1673f6", "wy": "676e536c7897a0002f9664929631f418c4537d23749220c50a32121c434dcad2a6cdc203cd035a32"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200045f658687e6a542a91d893b48776a86d528fd399781bbb9305be0797e3a6f36118ae19e68dc1673f6676e536c7897a0002f9664929631f418c4537d23749220c50a32121c434dcad2a6cdc203cd035a32", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABF9lhofmpUKpHYk7SHdqhtUo/TmX\ngbu5MFvgeX46bzYRiuGeaNwWc/ZnblNseJegAC+WZJKWMfQYxFN9I3SSIMUKMhIc\nQ03K0qbNwgPNA1oy\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "extreme value for k", "msg": "313233343030", "sig": "305402282fb412f03e6debdfbfa3a3092f21c4619e04279be0931694ab99c6503e5a894def8377ed059a6de8022853bf06e43fcc4236b2ec0d88471379053f1f3437207c5a75b09036b1c40fa8f3128277894a4c96cf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0490537a6827a25060273d44d4846aea596682df0a59d0ffe79be2a1ebe918703cabfac64da5e591003309180d9da5e78237b95403c52f3ceee503067b672715e97d8b6369342684a72f467698741b1a1f", "wx": "0090537a6827a25060273d44d4846aea596682df0a59d0ffe79be2a1ebe918703cabfac64da5e59100", "wy": "3309180d9da5e78237b95403c52f3ceee503067b672715e97d8b6369342684a72f467698741b1a1f"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000490537a6827a25060273d44d4846aea596682df0a59d0ffe79be2a1ebe918703cabfac64da5e591003309180d9da5e78237b95403c52f3ceee503067b672715e97d8b6369342684a72f467698741b1a1f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABJBTemgnolBgJz1E1IRq6llmgt8K\nWdD/55vioevpGHA8q/rGTaXlkQAzCRgNnaXngje5VAPFLzzu5QMGe2cnFel9i2Np\nNCaEpy9Gdph0Gxof\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3054022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061102284674c260123ec53d4b14281f9b55f577532fefe1e7850636646d64ed4f821da32cdb1c73c1973105", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04a81ccbf4fc4457033bd49ceac8fa52e459400730b877305be0418153d278d30b5973777a7dd1c2c17544ff1b76208e841053ecaef7a5869e92da08c5c4c3d0a167d5685eb721d620339cc9b00149838e", "wx": "00a81ccbf4fc4457033bd49ceac8fa52e459400730b877305be0418153d278d30b5973777a7dd1c2c1", "wy": "7544ff1b76208e841053ecaef7a5869e92da08c5c4c3d0a167d5685eb721d620339cc9b00149838e"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004a81ccbf4fc4457033bd49ceac8fa52e459400730b877305be0418153d278d30b5973777a7dd1c2c17544ff1b76208e841053ecaef7a5869e92da08c5c4c3d0a167d5685eb721d620339cc9b00149838e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABKgcy/T8RFcDO9Sc6sj6UuRZQAcw\nuHcwW+BBgVPSeNMLWXN3en3RwsF1RP8bdiCOhBBT7K73pYaektoIxcTD0KFn1Whe\ntyHWIDOcybABSYOO\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3054022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061102281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04ba160089327cf8ba163eefa476a4eafd0f6ce9d55292f6724d020f0efac54bf684f9d5f5695f89c2b4de70dc4ab265761827323da3b2b055ac1187fc5341e4555ebc6f6993b4c3fdd89863fc55ea38b4", "wx": "00ba160089327cf8ba163eefa476a4eafd0f6ce9d55292f6724d020f0efac54bf684f9d5f5695f89c2", "wy": "00b4de70dc4ab265761827323da3b2b055ac1187fc5341e4555ebc6f6993b4c3fdd89863fc55ea38b4"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004ba160089327cf8ba163eefa476a4eafd0f6ce9d55292f6724d020f0efac54bf684f9d5f5695f89c2b4de70dc4ab265761827323da3b2b055ac1187fc5341e4555ebc6f6993b4c3fdd89863fc55ea38b4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABLoWAIkyfPi6Fj7vpHak6v0PbOnV\nUpL2ck0CDw76xUv2hPnV9WlficK03nDcSrJldhgnMj2jsrBVrBGH/FNB5FVevG9p\nk7TD/diYY/xV6ji0\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3054022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061102282a460e39a48c0ff193727e795d339347984ff65457b636ed6f74d627fc8144fb81504445742783d0", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "044a5cf447550f0ff2efa193c3e185db604fcfd7de5c47a59a392da0c7572f061038c6af5afcfa9bd530b7682b82010c39334ba2edecf0a23bca09e810d745bdf73e445e80ace0e5399fa26102cb3faee6", "wx": "4a5cf447550f0ff2efa193c3e185db604fcfd7de5c47a59a392da0c7572f061038c6af5afcfa9bd5", "wy": "30b7682b82010c39334ba2edecf0a23bca09e810d745bdf73e445e80ace0e5399fa26102cb3faee6"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200044a5cf447550f0ff2efa193c3e185db604fcfd7de5c47a59a392da0c7572f061038c6af5afcfa9bd530b7682b82010c39334ba2edecf0a23bca09e810d745bdf73e445e80ace0e5399fa26102cb3faee6", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABEpc9EdVDw/y76GTw+GF22BPz9fe\nXEelmjktoMdXLwYQOMavWvz6m9Uwt2grggEMOTNLou3s8KI7ygnoENdFvfc+RF6A\nrODlOZ+iYQLLP67m\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3055022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611022900a91838e692303fc64dc9f9e574ce4d1e613fd9515ed8dbb5bdd3589ff20513ee05411115d09e0f41", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "045d3fef1b96dbc8ca9330508ad4ced491e627eb67cba8c6b1537937498ee3021b45ca6759117d89c4ad2b699e3ef9516fff2ed2e134931c96d28d3e14dd51c5b87589a8fa88af2529b8caa0f785ce2033", "wx": "5d3fef1b96dbc8ca9330508ad4ced491e627eb67cba8c6b1537937498ee3021b45ca6759117d89c4", "wy": "00ad2b699e3ef9516fff2ed2e134931c96d28d3e14dd51c5b87589a8fa88af2529b8caa0f785ce2033"}, "keyDer": "306a301406072a8648ce3d020106092b2403030208010109035200045d3fef1b96dbc8ca9330508ad4ced491e627eb67cba8c6b1537937498ee3021b45ca6759117d89c4ad2b699e3ef9516fff2ed2e134931c96d28d3e14dd51c5b87589a8fa88af2529b8caa0f785ce2033", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABF0/7xuW28jKkzBQitTO1JHmJ+tn\ny6jGsVN5N0mO4wIbRcpnWRF9icStK2mePvlRb/8u0uE0kxyW0o0+FN1Rxbh1iaj6\niK8lKbjKoPeFziAz\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3055022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611022900b52c3cf70a58445477eab051464ac05768321fb29c7aa242b9194cab5ebc4c35e10edb72cd3ba2a1", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "04a8336702c158dcae495f1c9cd720c39f15c123a67750dcd74520c34cf67907e49220bcd020cc3a60151a432ee3e23a74c8b8a98d8e7c672216df48d8a60d3f592f6673830ac9ecfbcd00550db7ad5c62", "wx": "00a8336702c158dcae495f1c9cd720c39f15c123a67750dcd74520c34cf67907e49220bcd020cc3a60", "wy": "151a432ee3e23a74c8b8a98d8e7c672216df48d8a60d3f592f6673830ac9ecfbcd00550db7ad5c62"}, "keyDer": "306a301406072a8648ce3d020106092b240303020801010903520004a8336702c158dcae495f1c9cd720c39f15c123a67750dcd74520c34cf67907e49220bcd020cc3a60151a432ee3e23a74c8b8a98d8e7c672216df48d8a60d3f592f6673830ac9ecfbcd00550db7ad5c62", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABKgzZwLBWNyuSV8cnNcgw58VwSOm\nd1Dc10Ugw0z2eQfkkiC80CDMOmAVGkMu4+I6dMi4qY2OfGciFt9I2KYNP1kvZnOD\nCsns+80AVQ23rVxi\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "extreme value for k", "msg": "313233343030", "sig": "3054022843bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611022853bf06e43fcc4236b2ec0d88471379053f1f3437207c5a75b09036b1c40fa8f3128277894a4c96cf", "result": "valid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1", "wx": "43bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611", "wy": "14fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABEO9fpr7U9i4Uom8xI7lv+byATfR\nCgh+tueHHioQpZnHEK+NDTniBhEU/dBVRewcyKtAkyR/dydeB0P/7RFxguqpx3h3\nqqxqx9NSRdFpLo7h\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "testing point duplication", "msg": "313233343030", "sig": "3055022900f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb70002281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "invalid", "flags": []}, {"tcId": 387, "comment": "testing point duplication", "msg": "313233343030", "sig": "3055022900ad0b664f9559e29e46fd4fd390e75abebf14997d17a1a3304c80e451fc8f79bb7cff168e17de6f2202281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611be6076caf0d032ef35fbe53a528ab907f24bcfb9e5828b04a5cb4174cde781612981cce088849f46", "wx": "43bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611", "wy": "00be6076caf0d032ef35fbe53a528ab907f24bcfb9e5828b04a5cb4174cde781612981cce088849f46"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e20611be6076caf0d032ef35fbe53a528ab907f24bcfb9e5828b04a5cb4174cde781612981cce088849f46", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABEO9fpr7U9i4Uom8xI7lv+byATfR\nCgh+tueHHioQpZnHEK+NDTniBhG+YHbK8NAy7zX75TpSirkH8kvPueWCiwSly0F0\nzeeBYSmBzOCIhJ9G\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "testing point duplication", "msg": "313233343030", "sig": "3055022900f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb70002281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "invalid", "flags": []}, {"tcId": 389, "comment": "testing point duplication", "msg": "313233343030", "sig": "3055022900ad0b664f9559e29e46fd4fd390e75abebf14997d17a1a3304c80e451fc8f79bb7cff168e17de6f2202281e320a292c640b636951c80d8bb7200e915daff31a147060742ee21c8fca0cb3a58279e87789f070", "result": "invalid", "flags": []}]}, {"key": {"curve": "brainpoolP320r1", "keySize": 320, "type": "EcPublicKey", "uncompressed": "0444ab2320c2297b66114428df33fe641956f82033893398af3b49b0023179201c27d26dd65121c06e0c59524c938f19daffc2a9a4679dba7cf1991ced4700592bb75e98cf77dbf6c584c2f72735152921", "wx": "44ab2320c2297b66114428df33fe641956f82033893398af3b49b0023179201c27d26dd65121c06e", "wy": "0c59524c938f19daffc2a9a4679dba7cf1991ced4700592bb75e98cf77dbf6c584c2f72735152921"}, "keyDer": "306a301406072a8648ce3d020106092b24030302080101090352000444ab2320c2297b66114428df33fe641956f82033893398af3b49b0023179201c27d26dd65121c06e0c59524c938f19daffc2a9a4679dba7cf1991ced4700592bb75e98cf77dbf6c584c2f72735152921", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMGowFAYHKoZIzj0CAQYJKyQDAwIIAQEJA1IABESrIyDCKXtmEUQo3zP+ZBlW+CAz\niTOYrztJsAIxeSAcJ9Jt1lEhwG4MWVJMk48Z2v/CqaRnnbp88Zkc7UcAWSu3XpjP\nd9v2xYTC9yc1FSkh\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "pseudorandom signature", "msg": "", "sig": "30540229009cf7f0d60cc1fb2d4b3e78d5f83b374e17a4aebccc6e723f1ad35babb2acfb2b75530389189395f802271110c5b8b8e5fa8dc7952a7bf6200bddae6c1d66639a07a4b6046e00bfa7a2bd9d5777b80c3a92", "result": "valid", "flags": []}, {"tcId": 391, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3055022826fd695ee1cc50c2661c2434f8699577af181304bceb7690c538b03463df24334395e791f6750ff6022900b322618cd50c6a7cffcb419ec05b67ec6a117088c78d57cecdd224902d391892ca03e4bc1bd0467b", "result": "valid", "flags": []}, {"tcId": 392, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "305402287a31b7375f924369ec12bc33b834726c95444a4c263557344afa732cf48a155e71a6ee7de42e91ce022824d3d72861f4d2b551c10f0294d16a3bf1d4ee3e484439b804d097dea2d7cace76ade14af1663322", "result": "valid", "flags": []}, {"tcId": 393, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "305502282417eb10a538921621066608243fd6574de84ef1281520f01ebe0444b46a607ab9eda8f3721779a60229008f1e2ea294028baeb738181e128c86ad55cb1945436cf69e090c2f6159f6f22011d731733b4433ba", "result": "valid", "flags": []}]}]}