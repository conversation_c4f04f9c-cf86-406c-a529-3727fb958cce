=pod

=head1 NAME

EVP_PKEY_get_group_name - get group name of a key

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_get_group_name(EVP_PKEY *pkey, char *gname, size_t gname_sz,
                             size_t *gname_len);

=head1 DESCRIPTION

EVP_PKEY_get_group_name() fills in the group name of the I<pkey> into
I<gname>, up to at most I<gname_sz> bytes including the ending NUL byte
and assigns I<*gname_len> the actual length of the name not including
the NUL byte, if I<pkey>'s key type supports it.
I<gname> as well as I<gname_len> may individually be NULL, and won't be
filled in or assigned in that case.

=head1 NOTES

Among the standard OpenSSL key types, this is only supported for DH, EC and
SM2 keys.  Other providers may support this for additional key types.

=head1 RETURN VALUES

EVP_PKEY_get_group_name() returns 1 if the group name could be filled in,
otherwise 0.

=head1 HISTORY

This function was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
