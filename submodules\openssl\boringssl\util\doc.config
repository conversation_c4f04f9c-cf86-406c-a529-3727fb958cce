{"BaseDirectory": "..", "Sections": [{"Name": "Low-level infrastructure", "Headers": ["include/openssl/base64.h", "include/openssl/bio.h", "include/openssl/buf.h", "include/openssl/bytestring.h", "include/openssl/err.h", "include/openssl/cpu.h", "include/openssl/crypto.h", "include/openssl/ex_data.h", "include/openssl/lhash.h", "include/openssl/mem.h", "include/openssl/obj.h", "include/openssl/pool.h", "include/openssl/rand.h", "include/openssl/stack.h"]}, {"Name": "Low-level crypto primitives", "Headers": ["include/openssl/aes.h", "include/openssl/bn.h", "include/openssl/cmac.h", "include/openssl/curve25519.h", "include/openssl/des.h", "include/openssl/dh.h", "include/openssl/dsa.h", "include/openssl/ec.h", "include/openssl/ec_key.h", "include/openssl/ecdh.h", "include/openssl/ecdsa.h", "include/openssl/engine.h", "include/openssl/hkdf.h", "include/openssl/hmac.h", "include/openssl/md5.h", "include/openssl/rc4.h", "include/openssl/rsa.h", "include/openssl/sha.h"]}, {"Name": "Crypto interfaces", "Headers": ["include/openssl/digest.h", "include/openssl/cipher.h", "include/openssl/aead.h", "include/openssl/evp.h"]}, {"Name": "SSL implementation", "Headers": ["include/openssl/ssl.h"]}]}