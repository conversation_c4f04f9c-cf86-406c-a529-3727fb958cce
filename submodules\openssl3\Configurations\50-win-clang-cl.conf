## -*- mode: perl; -*-
# Windows on Arm clang-cl targets.
#

my %targets = (
    "VC-WIN64-CLANGASM-ARM" => {
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "SIXTY_FOUR_BIT RC4_CHAR",
        multilib        => "-arm64",
        asm_arch        => "aarch64",
        AS        => "clang-cl.exe",
        ASFLAGS   => "/nologo /Zi",
        asflags   => "/c",
        asoutflag => "/Fo",
        perlasm_scheme => "win64",
        uplink_arch      => 'armv8',
    },
    "VC-CLANG-WIN64-CLANGASM-ARM" => {
        CC => "clang-cl",
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "SIXTY_FOUR_BIT RC4_CHAR",
        multilib        => "-arm64",
        asm_arch        => "aarch64",
        AS        => "clang-cl.exe",
        ASFLAGS   => "/nologo /Zi",
        asflags   => "/c",
        asoutflag => "/Fo",
        perlasm_scheme => "win64",
        uplink_arch      => 'armv8',
    },
);
