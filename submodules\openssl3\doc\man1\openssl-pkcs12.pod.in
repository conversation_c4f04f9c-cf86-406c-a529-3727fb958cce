=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-pkcs12 - PKCS#12 file command

=head1 SYNOPSIS

B<openssl> B<pkcs12>
[B<-help>]
[B<-passin> I<arg>]
[B<-passout> I<arg>]
[B<-password> I<arg>]
[B<-twopass>]
[B<-in> I<filename>|I<uri>]
[B<-out> I<filename>]
[B<-nokeys>]
[B<-nocerts>]
[B<-noout>]
[B<-legacy>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}
{- $OpenSSL::safe::opt_r_synopsis -}

PKCS#12 input (parsing) options:
[B<-info>]
[B<-nomacver>]
[B<-clcerts>]
[B<-cacerts>]

[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-noenc>]
[B<-nodes>]

PKCS#12 output (export) options:

[B<-export>]
[B<-inkey> I<filename>|I<uri>]
[B<-certfile> I<filename>]
[B<-passcerts> I<arg>]
[B<-chain>]
[B<-untrusted> I<filename>]
{- $OpenSSL::safe::opt_trust_synopsis -}
[B<-name> I<name>]
[B<-caname> I<name>]
[B<-CSP> I<name>]
[B<-LMK>]
[B<-keyex>]
[B<-keysig>]
[B<-keypbe> I<cipher>]
[B<-certpbe> I<cipher>]
[B<-descert>]
[B<-macalg> I<digest>]
[B<-iter> I<count>]
[B<-noiter>]
[B<-nomaciter>]
[B<-maciter>]
[B<-nomac>]

=head1 DESCRIPTION

This command allows PKCS#12 files (sometimes referred to as
PFX files) to be created and parsed. PKCS#12 files are used by several
programs including Netscape, MSIE and MS Outlook.

=head1 OPTIONS

There are a lot of options the meaning of some depends of whether a PKCS#12 file
is being created or parsed. By default a PKCS#12 file is parsed.
A PKCS#12 file can be created by using the B<-export> option (see below).
The PKCS#12 export encryption and MAC options such as B<-certpbe> and B<-iter>
and many further options such as B<-chain> are relevant only with B<-export>.
Conversely, the options regarding encryption of private keys when outputting
PKCS#12 input are relevant only when the B<-export> option is not given.

The default encryption algorithm is AES-256-CBC with PBKDF2 for key derivation.

When encountering problems loading legacy PKCS#12 files that involve,
for example, RC2-40-CBC,
try using the B<-legacy> option and, if needed, the B<-provider-path> option.

=over 4

=item B<-help>

Print out a usage message.

=item B<-passin> I<arg>

The password source for the input, and for encrypting any private keys that
are output.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-passout> I<arg>

The password source for output files.

=item B<-password> I<arg>

With B<-export>, B<-password> is equivalent to B<-passout>,
otherwise it is equivalent to B<-passin>.

=item B<-twopass>

Prompt for separate integrity and encryption passwords: most software
always assumes these are the same so this option will render such
PKCS#12 files unreadable. Cannot be used in combination with the options
B<-password>, B<-passin> if importing from PKCS#12, or B<-passout> if exporting.

=item B<-nokeys>

No private keys will be output.

=item B<-nocerts>

No certificates will be output.

=item B<-noout>

This option inhibits all credentials output,
and so the input is just verified.

=item B<-legacy>

Use legacy mode of operation and automatically load the legacy provider.
If OpenSSL is not installed system-wide,
it is necessary to also use, for example, C<-provider-path ./providers>
or to set the environment variable B<OPENSSL_MODULES>
to point to the directory where the providers can be found.

In the legacy mode, the default algorithm for certificate encryption
is RC2_CBC or 3DES_CBC depending on whether the RC2 cipher is enabled
in the build. The default algorithm for private key encryption is 3DES_CBC.
If the legacy option is not specified, then the legacy provider is not loaded
and the default encryption algorithm for both certificates and private keys is
AES_256_CBC with PBKDF2 for key derivation.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_r_item -}

=back

=head2 PKCS#12 input (parsing) options

=over 4

=item B<-in> I<filename>|I<uri>

This specifies the input filename or URI.
Standard input is used by default.
Without the B<-export> option this must be PKCS#12 file to be parsed.
For use with the B<-export> option
see the L</PKCS#12 output (export) options> section.

=item B<-out> I<filename>

The filename to write certificates and private keys to, standard output by
default.  They are all written in PEM format.

=item B<-info>

Output additional information about the PKCS#12 file structure, algorithms
used and iteration counts.

=item B<-nomacver>

Don't attempt to verify the integrity MAC.

=item B<-clcerts>

Only output client certificates (not CA certificates).

=item B<-cacerts>

Only output CA certificates (not client certificates).

=item B<-aes128>, B<-aes192>, B<-aes256>

Use AES to encrypt private keys before outputting.

=item B<-aria128>, B<-aria192>, B<-aria256>

Use ARIA to encrypt private keys before outputting.

=item B<-camellia128>, B<-camellia192>, B<-camellia256>

Use Camellia to encrypt private keys before outputting.

=item B<-des>

Use DES to encrypt private keys before outputting.

=item B<-des3>

Use triple DES to encrypt private keys before outputting.

=item B<-idea>

Use IDEA to encrypt private keys before outputting.

=item B<-noenc>

Don't encrypt private keys at all.

=item B<-nodes>

This option is deprecated since OpenSSL 3.0; use B<-noenc> instead.

=back

=head2 PKCS#12 output (export) options

=over 4

=item B<-export>

This option specifies that a PKCS#12 file will be created rather than
parsed.

=item B<-out> I<filename>

This specifies filename to write the PKCS#12 file to. Standard output is used
by default.

=item B<-in> I<filename>|I<uri>

This specifies the input filename or URI.
Standard input is used by default.
With the B<-export> option this is a file with certificates and a key,
or a URI that refers to a key accessed via an engine.
The order of credentials in a file doesn't matter but one private key and
its corresponding certificate should be present. If additional
certificates are present they will also be included in the PKCS#12 output file.

=item B<-inkey> I<filename>|I<uri>

The private key input for PKCS12 output.
If this option is not specified then the input file (B<-in> argument) must
contain a private key.
If no engine is used, the argument is taken as a file.
If the B<-engine> option is used or the URI has prefix C<org.openssl.engine:>
then the rest of the URI is taken as key identifier for the given engine.

=item B<-certfile> I<filename>

An input file with extra certificates to be added to the PKCS#12 output
if the B<-export> option is given.

=item B<-passcerts> I<arg>

The password source for certificate input such as B<-certfile>
and B<-untrusted>.
For more information about the format of B<arg> see
L<openssl-passphrase-options(1)>.

=item B<-chain>

If this option is present then the certificate chain of the end entity
certificate is built and included in the PKCS#12 output file.
The end entity certificate is the first one read from the B<-in> file
if no key is given, else the first certificate matching the given key.
The standard CA trust store is used for chain building,
as well as any untrusted CA certificates given with the B<-untrusted> option.

=item B<-untrusted> I<filename>

An input file of untrusted certificates that may be used
for chain building, which is relevant only when a PKCS#12 file is created
with the B<-export> option and the B<-chain> option is given as well.
Any certificates that are actually part of the chain are added to the output.

{- $OpenSSL::safe::opt_trust_item -}

=item B<-name> I<friendlyname>

This specifies the "friendly name" for the certificates and private key. This
name is typically displayed in list boxes by software importing the file.

=item B<-caname> I<friendlyname>

This specifies the "friendly name" for other certificates. This option may be
used multiple times to specify names for all certificates in the order they
appear. Netscape ignores friendly names on other certificates whereas MSIE
displays them.

=item B<-CSP> I<name>

Write I<name> as a Microsoft CSP name.
The password source for the input, and for encrypting any private keys that
are output.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-LMK>

Add the "Local Key Set" identifier to the attributes.

=item B<-keyex>|B<-keysig>

Specifies that the private key is to be used for key exchange or just signing.
This option is only interpreted by MSIE and similar MS software. Normally
"export grade" software will only allow 512 bit RSA keys to be used for
encryption purposes but arbitrary length keys for signing. The B<-keysig>
option marks the key for signing only. Signing only keys can be used for
S/MIME signing, authenticode (ActiveX control signing)  and SSL client
authentication, however, due to a bug only MSIE 5.0 and later support
the use of signing only keys for SSL client authentication.

=item B<-keypbe> I<alg>, B<-certpbe> I<alg>

These options allow the algorithm used to encrypt the private key and
certificates to be selected. Any PKCS#5 v1.5 or PKCS#12 PBE algorithm name
can be used (see L</NOTES> section for more information). If a cipher name
(as output by C<openssl list -cipher-algorithms>) is specified then it
is used with PKCS#5 v2.0. For interoperability reasons it is advisable to only
use PKCS#12 algorithms.

Special value C<NONE> disables encryption of the private key and certificates.

=item B<-descert>

Encrypt the certificates using triple DES. By default the private
key and the certificates are encrypted using AES-256-CBC unless
the '-legacy' option is used. If '-descert' is used with the '-legacy'
then both, the private key and the certificates are encrypted using triple DES.

=item B<-macalg> I<digest>

Specify the MAC digest algorithm. If not included SHA256 will be used.

=item B<-iter> I<count>

This option specifies the iteration count for the encryption key and MAC. The
default value is 2048.

To discourage attacks by using large dictionaries of common passwords the
algorithm that derives keys from passwords can have an iteration count applied
to it: this causes a certain part of the algorithm to be repeated and slows it
down. The MAC is used to check the file integrity but since it will normally
have the same password as the keys and certificates it could also be attacked.

=item B<-noiter>, B<-nomaciter>

By default both encryption and MAC iteration counts are set to 2048, using
these options the MAC and encryption iteration counts can be set to 1, since
this reduces the file security you should not use these options unless you
really have to. Most software supports both MAC and encryption iteration counts.
MSIE 4.0 doesn't support MAC iteration counts so it needs the B<-nomaciter>
option.

=item B<-maciter>

This option is included for compatibility with previous versions, it used
to be needed to use MAC iterations counts but they are now used by default.

=item B<-nomac>

Do not attempt to provide the MAC integrity. This can be useful with the FIPS
provider as the PKCS12 MAC requires PKCS12KDF which is not an approved FIPS
algorithm and cannot be supported by the FIPS provider.

=back

=head1 NOTES

Although there are a large number of options most of them are very rarely
used. For PKCS#12 file parsing only B<-in> and B<-out> need to be used
for PKCS#12 file creation B<-export> and B<-name> are also used.

If none of the B<-clcerts>, B<-cacerts> or B<-nocerts> options are present
then all certificates will be output in the order they appear in the input
PKCS#12 files. There is no guarantee that the first certificate present is
the one corresponding to the private key.
Certain software which tries to get a private key and the corresponding
certificate might assume that the first certificate in the file is the one
corresponding to the private key, but that may not always be the case.
Using the B<-clcerts> option will solve this problem by only
outputting the certificate corresponding to the private key. If the CA
certificates are required then they can be output to a separate file using
the B<-nokeys> B<-cacerts> options to just output CA certificates.

The B<-keypbe> and B<-certpbe> algorithms allow the precise encryption
algorithms for private keys and certificates to be specified. Normally
the defaults are fine but occasionally software can't handle triple DES
encrypted private keys, then the option B<-keypbe> I<PBE-SHA1-RC2-40> can
be used to reduce the private key encryption to 40 bit RC2. A complete
description of all algorithms is contained in L<openssl-pkcs8(1)>.

Prior 1.1 release passwords containing non-ASCII characters were encoded
in non-compliant manner, which limited interoperability, in first hand
with Windows. But switching to standard-compliant password encoding
poses problem accessing old data protected with broken encoding. For
this reason even legacy encodings is attempted when reading the
data. If you use PKCS#12 files in production application you are advised
to convert the data, because implemented heuristic approach is not
MT-safe, its sole goal is to facilitate the data upgrade with this
command.

=head1 EXAMPLES

Parse a PKCS#12 file and output it to a PEM file:

 openssl pkcs12 -in file.p12 -out file.pem

Output only client certificates to a file:

 openssl pkcs12 -in file.p12 -clcerts -out file.pem

Don't encrypt the private key:

 openssl pkcs12 -in file.p12 -out file.pem -noenc

Print some info about a PKCS#12 file:

 openssl pkcs12 -in file.p12 -info -noout

Print some info about a PKCS#12 file in legacy mode:

 openssl pkcs12 -in file.p12 -info -noout -legacy

Create a PKCS#12 file from a PEM file that may contain a key and certificates:

 openssl pkcs12 -export -in file.pem -out file.p12 -name "My PSE"

Include some extra certificates:

 openssl pkcs12 -export -in file.pem -out file.p12 -name "My PSE" \
  -certfile othercerts.pem

Export a PKCS#12 file with data from a certificate PEM file and from a further
PEM file containing a key, with default algorithms as in the legacy provider:

 openssl pkcs12 -export -in cert.pem -inkey key.pem -out file.p12 -legacy

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkcs8(1)>,
L<ossl_store-file(7)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.
The B<-nodes> option was deprecated in OpenSSL 3.0, too; use B<-noenc> instead.

=head1 COPYRIGHT

Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
