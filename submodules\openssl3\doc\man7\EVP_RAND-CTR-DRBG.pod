=pod

=head1 NAME

EVP_RAND-CTR-DRBG - The CTR DRBG EVP_RAND implementation

=head1 DESCRIPTION

Support for the counter deterministic random bit generator through the
B<EVP_RAND> API.

=head2 Identity

"CTR-DRBG" is the name for this implementation; it can be used with the
EVP_RAND_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "state" (B<OSSL_RAND_PARAM_STATE>) <integer>

=item "strength" (B<OSSL_RAND_PARAM_STRENGTH>) <unsigned integer>

=item "max_request" (B<OSSL_RAND_PARAM_MAX_REQUEST>) <unsigned integer>

=item "reseed_requests" (B<OSSL_DRBG_PARAM_RESEED_REQUESTS>) <unsigned integer>

=item "reseed_time_interval" (B<OSSL_DRBG_PARAM_RESEED_TIME_INTERVAL>) <integer>

=item "min_entropylen" (B<OSSL_DRBG_PARAM_MIN_ENTROPYLEN>) <unsigned integer>

=item "max_entropylen" (B<OSSL_DRBG_PARAM_MAX_ENTROPYLEN>) <unsigned integer>

=item "min_noncelen" (B<OSSL_DRBG_PARAM_MIN_NONCELEN>) <unsigned integer>

=item "max_noncelen" (B<OSSL_DRBG_PARAM_MAX_NONCELEN>) <unsigned integer>

=item "max_perslen" (B<OSSL_DRBG_PARAM_MAX_PERSLEN>) <unsigned integer>

=item "max_adinlen" (B<OSSL_DRBG_PARAM_MAX_ADINLEN>) <unsigned integer>

=item "reseed_counter" (B<OSSL_DRBG_PARAM_RESEED_COUNTER>) <unsigned integer>

=item "properties" (B<OSSL_DRBG_PARAM_PROPERTIES>) <UTF8 string>

=item "cipher" (B<OSSL_DRBG_PARAM_CIPHER>) <UTF8 string>

These parameters work as described in L<EVP_RAND(3)/PARAMETERS>.

=item "use_derivation_function" (B<OSSL_DRBG_PARAM_USE_DF>) <integer>

This Boolean indicates if a derivation function should be used or not.
A nonzero value (the default) uses the derivation function.  A zero value
does not.

=back

=head1 NOTES

A context for CTR DRBG can be obtained by calling:

 EVP_RAND *rand = EVP_RAND_fetch(NULL, "CTR-DRBG", NULL);
 EVP_RAND_CTX *rctx = EVP_RAND_CTX_new(rand);

=head1 EXAMPLES

 EVP_RAND *rand;
 EVP_RAND_CTX *rctx;
 unsigned char bytes[100];
 OSSL_PARAM params[2], *p = params;
 unsigned int strength = 128;

 rand = EVP_RAND_fetch(NULL, "CTR-DRBG", NULL);
 rctx = EVP_RAND_CTX_new(rand, NULL);
 EVP_RAND_free(rand);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_DRBG_PARAM_CIPHER,
                                         SN_aes_256_ctr, 0);
 *p = OSSL_PARAM_construct_end();
 EVP_RAND_instantiate(rctx, strength, 0, NULL, 0, params);

 EVP_RAND_generate(rctx, bytes, sizeof(bytes), strength, 0, NULL, 0);

 EVP_RAND_CTX_free(rctx);

=head1 CONFORMING TO

NIST SP 800-90A and SP 800-90B

=head1 SEE ALSO

L<EVP_RAND(3)>,
L<EVP_RAND(3)/PARAMETERS>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
