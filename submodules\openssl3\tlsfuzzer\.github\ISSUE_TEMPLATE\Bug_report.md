---
name: Bug report
about: Create a report to help us improve

---

# Bug Report

## System Information

| Type                     | Version/Name |
| ------------------------ | ------------ |
| Operating System Name    | <!-- provide name of OS you're using here, e.g.: Windows, RHEL --> |
| Operating System Version | <!-- provide the version of the OS you're using here, e.g.: 6.10, 7 --> |
| Python version           | <!-- provide the output of `python -V` you're using --> |
| tlslite-ng version       | <!-- provide the version or commit ID of tlslite-ng version you're using(use `git rev-parse HEAD` in tlslite-ng repo copy you're using or if you're using a packages version, run `python -c 'import tlslite; print(tlslite.__version__)'`, also how it was installed --> |
| tlsfuzzer revision       | <!-- provide the revision of tlsfuzzer that you are using (use `git rev-parse HEAD` in the checked-out tlsfuzzer directory --> |

## Problem description

A clear and concise description of what the bug is.

## Describe how to reproduce the problem

(if the issue is interaction with some other system/library/application,
please provide relevant version numbers or commit IDs of it too.)

Steps to reproduce the behaviour:

1. First ...
1. Then ...
1. Finally ...

## Expected behaviour

A concise description of what you expected to happen.

## Include errors or backtraces

<!-- please insert logs between

```
```

lines (standard MarkDown preformatted text markers).

If the log is very long (full screen or longer), please include it inside a
<details> HTML tag, see https://stackoverflow.com/a/39920717/462370 for details
-->

## Additional context

Add any other context about the problem here.
