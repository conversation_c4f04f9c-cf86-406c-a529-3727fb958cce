krb5 types and structures
=========================

Public
-------

.. toctree::
   :maxdepth: 1

   krb5_address.rst
   krb5_addrtype.rst
   krb5_ap_req.rst
   krb5_ap_rep.rst
   krb5_ap_rep_enc_part.rst
   krb5_authdata.rst
   krb5_authdatatype.rst
   krb5_authenticator.rst
   krb5_boolean.rst
   krb5_checksum.rst
   krb5_const_pointer.rst
   krb5_const_principal.rst
   krb5_cred.rst
   krb5_cred_enc_part.rst
   krb5_cred_info.rst
   krb5_creds.rst
   krb5_crypto_iov.rst
   krb5_cryptotype.rst
   krb5_data.rst
   krb5_deltat.rst
   krb5_enc_data.rst
   krb5_enc_kdc_rep_part.rst
   krb5_enc_tkt_part.rst
   krb5_encrypt_block.rst
   krb5_enctype.rst
   krb5_error.rst
   krb5_error_code.rst
   krb5_expire_callback_func.rst
   krb5_flags.rst
   krb5_get_init_creds_opt.rst
   krb5_gic_opt_pa_data.rst
   krb5_int16.rst
   krb5_int32.rst
   krb5_kdc_rep.rst
   krb5_kdc_req.rst
   krb5_keyblock.rst
   krb5_keytab_entry.rst
   krb5_keyusage.rst
   krb5_kt_cursor.rst
   krb5_kvno.rst
   krb5_last_req_entry.rst
   krb5_magic.rst
   krb5_mk_req_checksum_func.rst
   krb5_msgtype.rst
   krb5_octet.rst
   krb5_pa_pac_req.rst
   krb5_pa_server_referral_data.rst
   krb5_pa_svr_referral_data.rst
   krb5_pa_data.rst
   krb5_pointer.rst
   krb5_post_recv_fn.rst
   krb5_pre_send_fn.rst
   krb5_preauthtype.rst
   krb5_principal.rst
   krb5_principal_data.rst
   krb5_const_principal.rst
   krb5_prompt.rst
   krb5_prompt_type.rst
   krb5_prompter_fct.rst
   krb5_pwd_data.rst
   krb5_responder_context.rst
   krb5_responder_fn.rst
   krb5_responder_otp_challenge.rst
   krb5_responder_otp_tokeninfo.rst
   krb5_responder_pkinit_challenge.rst
   krb5_responder_pkinit_identity.rst
   krb5_response.rst
   krb5_replay_data.rst
   krb5_ticket.rst
   krb5_ticket_times.rst
   krb5_timestamp.rst
   krb5_tkt_authent.rst
   krb5_trace_callback.rst
   krb5_trace_info.rst
   krb5_transited.rst
   krb5_typed_data.rst
   krb5_ui_2.rst
   krb5_ui_4.rst
   krb5_verify_init_creds_opt.rst
   passwd_phrase_element.rst


Internal
---------

.. toctree::
   :maxdepth: 1

   krb5_auth_context.rst
   krb5_cksumtype
   krb5_context.rst
   krb5_cc_cursor.rst
   krb5_ccache.rst
   krb5_cccol_cursor.rst
   krb5_init_creds_context.rst
   krb5_key.rst
   krb5_keytab.rst
   krb5_pac.rst
   krb5_rcache.rst
   krb5_tkt_creds_context.rst
