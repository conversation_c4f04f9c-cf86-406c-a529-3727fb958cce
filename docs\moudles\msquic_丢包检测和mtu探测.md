


          
# MsQuic 丢包检测和MTU探测机制分析

## 1. 丢包检测机制

### 1.1 概述

MsQuic的丢包检测机制结合了两种类似的丢包检测算法：

1. **RACK (基于时间的重排序阈值)算法**：
   - 如果一个未确认的数据包在一个已确认的数据包之前发送，且发送时间超过了`QUIC_TIME_REORDER_THRESHOLD`，则认为该数据包已丢失。

2. **FACK (基于包号的重排序阈值)算法**：
   - 如果一个未确认的数据包在一个已确认的数据包之前发送，且发送的数据包数量超过了`QUIC_PACKET_REORDER_THRESHOLD`，则认为该数据包已丢失。

### 1.2 丢包检测中的定时器

丢包检测模块中有三个逻辑定时器：

1. **断开连接定时器**：
   - 如果一个数据包在`DisconnectTimeoutUs`时间内未被确认或确定为丢失，则终止连接。
   - 这是最后的"放弃"定时器，在有未确认数据包时启用。

2. **RACK定时器**：
   - 当有未确认的数据包且有更晚发送的数据包已被确认时启用。
   - 用于触发上述RACK丢包检测算法。
   - 当此定时器启用时，探测定时器不启用。

3. **探测定时器**：
   - 目的是确保RACK算法在所有情况下都能发现丢失的数据包。
   - 当RACK定时器未启用且有未确认数据包时启用。
   - 周期是RTT的函数，每次连续触发时翻倍。
   - 当探测定时器触发时，会发送两个探测数据包。

### 1.3 数据结构

```c
typedef struct QUIC_LOSS_DETECTION {
    QUIC_SENT_PACKET_METADATA* SentPackets;        // 已发送数据包链表
    QUIC_SENT_PACKET_METADATA** SentPacketsTail;   // 已发送数据包链表尾指针
    QUIC_SENT_PACKET_METADATA* LostPackets;        // 丢失数据包链表
    QUIC_SENT_PACKET_METADATA** LostPacketsTail;   // 丢失数据包链表尾指针
    
    uint32_t PacketsInFlight;                      // 在途数据包数量
    uint64_t TimeOfLastPacketSent;                 // 最后一个数据包发送时间
    uint64_t TotalBytesSent;                       // 总发送字节数
    uint64_t TotalBytesAcked;                      // 总确认字节数
    uint64_t TotalBytesSentAtLastAck;              // 上次确认时的总发送字节数
    uint64_t TimeOfLastPacketAcked;                // 最后一个数据包确认时间
    uint64_t TimeOfLastAckedPacketSent;            // 最后一个被确认的数据包的发送时间
    uint64_t AdjustedLastAckedTime;                // 调整后的最后确认时间
    uint16_t ProbeCount;                           // 探测计数
    
    // 其他字段...
} QUIC_LOSS_DETECTION;
```

### 1.4 主要函数

1. **初始化与清理**：
   - `QuicLossDetectionInitialize`：初始化丢包检测模块
   - `QuicLossDetectionUninitialize`：清理丢包检测模块
   - `QuicLossDetectionReset`：重置丢包检测状态

2. **数据包处理**：
   - `QuicLossDetectionOnPacketSent`：处理数据包发送事件
   - `QuicLossDetectionOnPacketAcknowledged`：处理数据包确认事件
   - `QuicLossDetectionOnPacketDiscarded`：处理数据包丢弃事件

3. **定时器管理**：
   - `QuicLossDetectionUpdateTimer`：更新丢包检测定时器
   - `QuicLossDetectionProcessTimerOperation`：处理定时器触发事件
   - `QuicLossDetectionComputeProbeTimeout`：计算探测超时时间

4. **丢包处理**：
   - `QuicLossDetectionRetransmitFrames`：重传帧
   - `QuicLossDetectionDetectAndHandleLostPackets`：检测并处理丢失的数据包

### 1.5 丢包检测流程

1. **数据包发送**：
   - 当发送数据包时，调用`QuicLossDetectionOnPacketSent`
   - 创建数据包元数据副本并添加到已发送数据包队列
   - 更新相关统计信息和状态

2. **数据包确认**：
   - 当收到ACK时，调用`QuicLossDetectionOnPacketAcknowledged`
   - 处理确认的数据包中的每个帧
   - 更新RTT和拥塞控制状态

3. **丢包检测**：
   - 基于RACK和FACK算法检测丢失的数据包
   - 将丢失的数据包从已发送队列移到丢失队列
   - 触发重传机制

4. **定时器触发**：
   - 当丢包检测定时器触发时，执行相应的操作
   - RACK定时器：检测并处理丢失的数据包
   - 探测定时器：发送探测数据包

### 1.6 状态图

```mermaid
stateDiagram-v2
    [*] --> 初始化: QuicLossDetectionInitialize
    初始化 --> 空闲: 无数据包发送
    空闲 --> 数据包发送: QuicLossDetectionOnPacketSent
    数据包发送 --> RACK定时器: 有已确认的更晚数据包
    数据包发送 --> 探测定时器: 无已确认的更晚数据包
    RACK定时器 --> 丢包处理: 定时器触发
    探测定时器 --> 发送探测: 定时器触发
    丢包处理 --> 重传: QuicLossDetectionRetransmitFrames
    发送探测 --> 数据包发送: 发送探测数据包
    数据包发送 --> 数据包确认: 收到ACK
    数据包确认 --> 更新RTT: 更新RTT和拥塞控制
    更新RTT --> 空闲: 所有数据包已确认
    更新RTT --> 数据包发送: 仍有未确认数据包
    初始化 --> [*]: QuicLossDetectionUninitialize
```

## 2. MTU探测机制

### 2.1 概述

MTU（最大传输单元）探测是QUIC协议中的一个重要机制，用于发现路径上支持的最大数据包大小。MsQuic实现了DPLPMTUD（Datagram Packetization Layer Path MTU Discovery）算法：

- 当新路径被验证后，在该路径上启动MTU探测
- 发送比当前MTU更大的探测数据包
- 如果探测数据包被确认，则将其设为当前MTU并发送新的探测数据包
- 如果探测数据包未被确认，则重试相同大小的探测
- 如果连续失败`QUIC_DPLPMTUD_MAX_PROBES`次，则认为已找到最大MTU并停止搜索

### 2.2 数据结构

```c
typedef struct QUIC_MTU_DISCOVERY {
    BOOLEAN IsSearchComplete;                // 搜索是否完成
    BOOLEAN HasProbed1500;                   // 是否已探测1500字节
    uint16_t ProbeSize;                      // 探测大小
    uint16_t MaxMtu;                         // 最大MTU
    int16_t ProbeCount;                      // 探测计数
    uint64_t SearchCompleteEnterTimeUs;      // 进入搜索完成状态的时间
} QUIC_MTU_DISCOVERY;
```

### 2.3 主要函数

1. **初始化与状态转换**：
   - `QuicMtuDiscoveryPeerValidated`：当对等方验证后初始化MTU探测
   - `QuicMtuDiscoveryMoveToSearching`：转移到搜索状态
   - `QuicMtuDiscoveryMoveToSearchComplete`：转移到搜索完成状态

2. **探测处理**：
   - `QuicMtuDiscoverySendProbePacket`：发送探测数据包
   - `QuicMtuDiscoveryOnAckedPacket`：处理已确认的探测数据包
   - `QuicMtuDiscoveryProbePacketDiscarded`：处理被丢弃的探测数据包

3. **辅助函数**：
   - `QuicGetNextProbeSize`：获取下一个探测大小

### 2.4 MTU探测算法

MsQuic使用的MTU探测算法相对简单：

1. 每次增加80字节（`QUIC_DPLPMTUD_INCREMENT`）
2. 特殊处理1500字节的探测，因为1500通常是互联网上允许的最大值
3. 如果当前MTU小于1280，则直接跳到1280（IPv6最小MTU）

### 2.5 MTU探测流程

1. **初始化**：
   - 当路径被验证后，调用`QuicMtuDiscoveryPeerValidated`
   - 设置最大MTU和初始状态

2. **搜索阶段**：
   - 计算下一个探测大小
   - 发送探测数据包
   - 等待确认或丢弃

3. **处理结果**：
   - 如果探测数据包被确认，更新当前MTU并尝试更大的探测
   - 如果探测数据包被丢弃，增加探测计数并重试或完成搜索

4. **搜索完成**：
   - 当达到最大MTU或多次探测失败时，进入搜索完成状态
   - 在`QUIC_DPLPMTUD_RAISE_TIMER_TIMEOUT`时间后可能重新开始探测

### 2.6 状态图

```mermaid
stateDiagram-v2
    [*] --> 初始化: 路径验证
    初始化 --> 搜索: QuicMtuDiscoveryMoveToSearching
    搜索 --> 发送探测: QuicMtuDiscoverySendProbePacket
    发送探测 --> 等待结果: 等待ACK或超时
    等待结果 --> 探测成功: 收到ACK
    等待结果 --> 探测失败: 未收到ACK
    探测成功 --> 更新MTU: 设置新MTU
    更新MTU --> 搜索: 尝试更大MTU
    更新MTU --> 搜索完成: 达到最大MTU
    探测失败 --> 重试探测: ProbeCount < MAX_PROBES
    探测失败 --> 搜索完成: ProbeCount >= MAX_PROBES
    重试探测 --> 发送探测: 相同大小
    搜索完成 --> 等待超时: 等待RAISE_TIMER_TIMEOUT
    等待超时 --> 搜索: 超时后重新开始
    搜索完成 --> [*]: 连接关闭
```

## 3. 协议交互图

### 3.1 丢包检测协议交互

```mermaid
sequenceDiagram
    participant A as 客户端
    participant B as 服务器
    
    Note over A,B: 正常数据传输
    A->>B: 数据包 1
    A->>B: 数据包 2
    A->>B: 数据包 3
    B-->>A: ACK(1,2)
    
    Note over A,B: 丢包检测 - RACK算法
    A->>B: 数据包 4
    A->>B: 数据包 5
    Note over B: 数据包 4 丢失
    B-->>A: ACK(5)
    Note over A: 检测到数据包 4 可能丢失
    Note over A: 启动RACK定时器
    Note over A: RACK定时器触发
    Note over A: 确认数据包 4 丢失
    A->>B: 重传数据包 4 的内容
    
    Note over A,B: 丢包检测 - 探测定时器
    A->>B: 数据包 6 (最后一个数据包)
    Note over B: 数据包 6 丢失
    Note over A: 无法通过RACK检测丢失
    Note over A: 启动探测定时器
    Note over A: 探测定时器触发
    A->>B: 探测数据包 1
    A->>B: 探测数据包 2
    B-->>A: ACK(探测数据包)
    Note over A: 检测到数据包 6 丢失
    A->>B: 重传数据包 6 的内容
```

### 3.2 MTU探测协议交互

```mermaid
sequenceDiagram
    participant A as 客户端
    participant B as 服务器
    
    Note over A,B: 路径验证完成
    Note over A: 初始MTU = 1280
    
    Note over A,B: 成功的MTU探测
    A->>B: MTU探测数据包 (1360字节)
    B-->>A: ACK(MTU探测)
    Note over A: 更新MTU = 1360
    
    A->>B: MTU探测数据包 (1440字节)
    B-->>A: ACK(MTU探测)
    Note over A: 更新MTU = 1440
    
    A->>B: MTU探测数据包 (1500字节)
    B-->>A: ACK(MTU探测)
    Note over A: 更新MTU = 1500
    
    Note over A,B: 失败的MTU探测
    A->>B: MTU探测数据包 (1580字节)
    Note over B: 数据包太大，被网络丢弃
    Note over A: 探测超时
    
    A->>B: MTU探测数据包 (1580字节) - 重试1
    Note over B: 数据包太大，被网络丢弃
    Note over A: 探测超时
    
    A->>B: MTU探测数据包 (1580字节) - 重试2
    Note over B: 数据包太大，被网络丢弃
    Note over A: 探测超时
    
    Note over A: 达到最大重试次数
    Note over A: 确定最大MTU = 1500
    Note over A: 进入搜索完成状态
```
## 4. 总结

MsQuic的丢包检测和MTU探测机制是QUIC协议实现中的关键组件，它们共同确保了数据传输的可靠性和效率：

1. **丢包检测**通过RACK和FACK算法检测丢失的数据包，并通过定时器机制确保及时重传，保证了数据传输的可靠性。

2. **MTU探测**通过逐步增加数据包大小并验证其可达性，找到网络路径上支持的最大MTU，优化了数据传输效率。

这两个机制的实现充分利用了MsQuic的定时器轮架构，与连接管理和工作线程紧密集成，提供了高效的事件驱动模型，使得协议栈能够及时响应各种网络事件和超时情况，保证了QUIC协议的正确实现和高性能运行。

        