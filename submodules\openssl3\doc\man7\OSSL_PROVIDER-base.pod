=pod

=head1 NAME

OSSL_PROVIDER-base - OpenSSL base provider

=head1 DESCRIPTION

The OpenSSL base provider supplies the encoding for OpenSSL's
asymmetric cryptography.

=head2 Properties

The implementations in this provider specifically have this property
defined:

=over 4

=item "provider=base"

=back

It may be used in a property query string with fetching functions.

It isn't mandatory to query for this property, except to make sure to get
implementations of this provider and none other.

=over 4

=item "type=parameters"

=item "type=private"

=item "type=public"

=back

These may be used in a property query string with fetching functions to select
which data are to be encoded.  Either the private key material, the public
key material or the domain parameters can be selected.

=over 4

=item "format=der"

=item "format=pem"

=item "format=text"

=back

These may be used in a property query string with fetching functions to select
the encoding output format.  Either the DER, PEM and plaintext are
currently permitted.

=head1 OPERATIONS AND ALGORITHMS

The OpenSSL base provider supports these operations and algorithms:

=head2 Asymmetric Key Encoder

In addition to "provider=base", some of these encoders define the
property "fips=yes", to allow them to be used together with the FIPS
provider.

=over 4

=item RSA, see L<OSSL_ENCODER-RSA(7)>

=item DH, see L<OSSL_ENCODER-DH(7)>

=item DSA, see L<OSSL_ENCODER-DSA(7)>

=item EC, see L<OSSL_ENCODER-EC(7)>

=item X25519, see L<OSSL_ENCODER-X25519(7)>

=item X448, see L<OSSL_ENCODER-X448(7)>

=back

=head1 SEE ALSO

L<OSSL_PROVIDER-default(7)>, L<openssl-core.h(7)>,
L<openssl-core_dispatch.h(7)>, L<provider(7)>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
