.\" Man page generated from reStructuredText.
.
.TH "KRB5.CONF" "5" " " "1.17.1" "MIT Kerberos"
.SH NAME
krb5.conf \- Kerberos configuration file
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.sp
The krb5.conf file contains Kerberos configuration information,
including the locations of KDCs and admin servers for the Kerberos
realms of interest, defaults for the current realm and for Kerberos
applications, and mappings of hostnames onto Kerberos realms.
Normally, you should install your krb5.conf file in the directory
\fB/etc\fP\&.  You can override the default location by setting the
environment variable \fBKRB5_CONFIG\fP\&.  Multiple colon\-separated
filenames may be specified in \fBKRB5_CONFIG\fP; all files which are
present will be read.  Starting in release 1.14, directory names can
also be specified in \fBKRB5_CONFIG\fP; all files within the directory
whose names consist solely of alphanumeric characters, dashes, or
underscores will be read.
.SH STRUCTURE
.sp
The krb5.conf file is set up in the style of a Windows INI file.
Lines beginning with \(aq#\(aq or \(aq;\(aq (possibly after initial whitespace)
are ignored as comments.  Sections are headed by the section name, in
square brackets.  Each section may contain zero or more relations, of
the form:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
foo = bar
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
or:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
fubar = {
    foo = bar
    baz = quux
}
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
Placing a \(aq*\(aq after the closing bracket of a section name indicates
that the section is \fIfinal\fP, meaning that if the same section appears
within a later file specified in \fBKRB5_CONFIG\fP, it will be ignored.
A subsection can be marked as final by placing a \(aq*\(aq after either the
tag name or the closing brace.
.sp
The krb5.conf file can include other files using either of the
following directives at the beginning of a line:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
include FILENAME
includedir DIRNAME
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
\fIFILENAME\fP or \fIDIRNAME\fP should be an absolute path. The named file or
directory must exist and be readable.  Including a directory includes
all files within the directory whose names consist solely of
alphanumeric characters, dashes, or underscores.  Starting in release
1.15, files with names ending in ".conf" are also included, unless the
name begins with ".".  Included profile files are syntactically
independent of their parents, so each included file must begin with a
section header.  Starting in release 1.17, files are read in
alphanumeric order; in previous releases, they may be read in any
order.
.sp
The krb5.conf file can specify that configuration should be obtained
from a loadable module, rather than the file itself, using the
following directive at the beginning of a line before any section
headers:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
module MODULEPATH:RESIDUAL
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
\fIMODULEPATH\fP may be relative to the library path of the krb5
installation, or it may be an absolute path.  \fIRESIDUAL\fP is provided
to the module at initialization time.  If krb5.conf uses a module
directive, kdc.conf(5) should also use one if it exists.
.SH SECTIONS
.sp
The krb5.conf file may contain the following sections:
.TS
center;
|l|l|.
_
T{
\fI\%[libdefaults]\fP
T}	T{
Settings used by the Kerberos V5 library
T}
_
T{
\fI\%[realms]\fP
T}	T{
Realm\-specific contact information and settings
T}
_
T{
\fI\%[domain_realm]\fP
T}	T{
Maps server hostnames to Kerberos realms
T}
_
T{
\fI\%[capaths]\fP
T}	T{
Authentication paths for non\-hierarchical cross\-realm
T}
_
T{
\fI\%[appdefaults]\fP
T}	T{
Settings used by some Kerberos V5 applications
T}
_
T{
\fI\%[plugins]\fP
T}	T{
Controls plugin module registration
T}
_
.TE
.sp
Additionally, krb5.conf may include any of the relations described in
kdc.conf(5), but it is not a recommended practice.
.SS [libdefaults]
.sp
The libdefaults section may contain any of the following relations:
.INDENT 0.0
.TP
\fBallow_weak_crypto\fP
If this flag is set to false, then weak encryption types (as noted
in Encryption_types in kdc.conf(5)) will be filtered
out of the lists \fBdefault_tgs_enctypes\fP,
\fBdefault_tkt_enctypes\fP, and \fBpermitted_enctypes\fP\&.  The default
value for this tag is false, which may cause authentication
failures in existing Kerberos infrastructures that do not support
strong crypto.  Users in affected environments should set this tag
to true until their infrastructure adopts stronger ciphers.
.TP
\fBap_req_checksum_type\fP
An integer which specifies the type of AP\-REQ checksum to use in
authenticators.  This variable should be unset so the appropriate
checksum for the encryption key in use will be used.  This can be
set if backward compatibility requires a specific checksum type.
See the \fBkdc_req_checksum_type\fP configuration option for the
possible values and their meanings.
.TP
\fBcanonicalize\fP
If this flag is set to true, initial ticket requests to the KDC
will request canonicalization of the client principal name, and
answers with different client principals than the requested
principal will be accepted.  The default value is false.
.TP
\fBccache_type\fP
This parameter determines the format of credential cache types
created by kinit(1) or other programs.  The default value
is 4, which represents the most current format.  Smaller values
can be used for compatibility with very old implementations of
Kerberos which interact with credential caches on the same host.
.TP
\fBclockskew\fP
Sets the maximum allowable amount of clockskew in seconds that the
library will tolerate before assuming that a Kerberos message is
invalid.  The default value is 300 seconds, or five minutes.
.sp
The clockskew setting is also used when evaluating ticket start
and expiration times.  For example, tickets that have reached
their expiration time can still be used (and renewed if they are
renewable tickets) if they have been expired for a shorter
duration than the \fBclockskew\fP setting.
.TP
\fBdefault_ccache_name\fP
This relation specifies the name of the default credential cache.
The default is \fB@CCNAME@\fP\&.  This relation is subject to parameter
expansion (see below).  New in release 1.11.
.TP
\fBdefault_client_keytab_name\fP
This relation specifies the name of the default keytab for
obtaining client credentials.  The default is \fB@CKTNAME@\fP\&.  This
relation is subject to parameter expansion (see below).
New in release 1.11.
.TP
\fBdefault_keytab_name\fP
This relation specifies the default keytab name to be used by
application servers such as sshd.  The default is \fB@KTNAME@\fP\&.  This
relation is subject to parameter expansion (see below).
.TP
\fBdefault_realm\fP
Identifies the default Kerberos realm for the client.  Set its
value to your Kerberos realm.  If this value is not set, then a
realm must be specified with every Kerberos principal when
invoking programs such as kinit(1)\&.
.TP
\fBdefault_tgs_enctypes\fP
Identifies the supported list of session key encryption types that
the client should request when making a TGS\-REQ, in order of
preference from highest to lowest.  The list may be delimited with
commas or whitespace.  See Encryption_types in
kdc.conf(5) for a list of the accepted values for this tag.
The default value is \fBaes256\-cts\-hmac\-sha1\-96 aes128\-cts\-hmac\-sha1\-96 aes256\-cts\-hmac\-sha384\-192 aes128\-cts\-hmac\-sha256\-128 des3\-cbc\-sha1 arcfour\-hmac\-md5 camellia256\-cts\-cmac camellia128\-cts\-cmac des\-cbc\-crc des\-cbc\-md5 des\-cbc\-md4\fP, but single\-DES encryption types
will be implicitly removed from this list if the value of
\fBallow_weak_crypto\fP is false.
.sp
Do not set this unless required for specific backward
compatibility purposes; stale values of this setting can prevent
clients from taking advantage of new stronger enctypes when the
libraries are upgraded.
.TP
\fBdefault_tkt_enctypes\fP
Identifies the supported list of session key encryption types that
the client should request when making an AS\-REQ, in order of
preference from highest to lowest.  The format is the same as for
default_tgs_enctypes.  The default value for this tag is
\fBaes256\-cts\-hmac\-sha1\-96 aes128\-cts\-hmac\-sha1\-96 aes256\-cts\-hmac\-sha384\-192 aes128\-cts\-hmac\-sha256\-128 des3\-cbc\-sha1 arcfour\-hmac\-md5 camellia256\-cts\-cmac camellia128\-cts\-cmac des\-cbc\-crc des\-cbc\-md5 des\-cbc\-md4\fP, but single\-DES encryption types will be implicitly
removed from this list if the value of \fBallow_weak_crypto\fP is
false.
.sp
Do not set this unless required for specific backward
compatibility purposes; stale values of this setting can prevent
clients from taking advantage of new stronger enctypes when the
libraries are upgraded.
.TP
\fBdns_canonicalize_hostname\fP
Indicate whether name lookups will be used to canonicalize
hostnames for use in service principal names.  Setting this flag
to false can improve security by reducing reliance on DNS, but
means that short hostnames will not be canonicalized to
fully\-qualified hostnames.  The default value is true.
.TP
\fBdns_lookup_kdc\fP
Indicate whether DNS SRV records should be used to locate the KDCs
and other servers for a realm, if they are not listed in the
krb5.conf information for the realm.  (Note that the admin_server
entry must be in the krb5.conf realm information in order to
contact kadmind, because the DNS implementation for kadmin is
incomplete.)
.sp
Enabling this option does open up a type of denial\-of\-service
attack, if someone spoofs the DNS records and redirects you to
another server.  However, it\(aqs no worse than a denial of service,
because that fake KDC will be unable to decode anything you send
it (besides the initial ticket request, which has no encrypted
data), and anything the fake KDC sends will not be trusted without
verification using some secret that it won\(aqt know.
.TP
\fBdns_uri_lookup\fP
Indicate whether DNS URI records should be used to locate the KDCs
and other servers for a realm, if they are not listed in the
krb5.conf information for the realm.  SRV records are used as a
fallback if no URI records were found.  The default value is true.
New in release 1.15.
.TP
\fBerr_fmt\fP
This relation allows for custom error message formatting.  If a
value is set, error messages will be formatted by substituting a
normal error message for %M and an error code for %C in the value.
.TP
\fBextra_addresses\fP
This allows a computer to use multiple local addresses, in order
to allow Kerberos to work in a network that uses NATs while still
using address\-restricted tickets.  The addresses should be in a
comma\-separated list.  This option has no effect if
\fBnoaddresses\fP is true.
.TP
\fBforwardable\fP
If this flag is true, initial tickets will be forwardable by
default, if allowed by the KDC.  The default value is false.
.TP
\fBignore_acceptor_hostname\fP
When accepting GSSAPI or krb5 security contexts for host\-based
service principals, ignore any hostname passed by the calling
application, and allow clients to authenticate to any service
principal in the keytab matching the service name and realm name
(if given).  This option can improve the administrative
flexibility of server applications on multihomed hosts, but could
compromise the security of virtual hosting environments.  The
default value is false.  New in release 1.10.
.TP
\fBk5login_authoritative\fP
If this flag is true, principals must be listed in a local user\(aqs
k5login file to be granted login access, if a \&.k5login(5)
file exists.  If this flag is false, a principal may still be
granted login access through other mechanisms even if a k5login
file exists but does not list the principal.  The default value is
true.
.TP
\fBk5login_directory\fP
If set, the library will look for a local user\(aqs k5login file
within the named directory, with a filename corresponding to the
local username.  If not set, the library will look for k5login
files in the user\(aqs home directory, with the filename .k5login.
For security reasons, .k5login files must be owned by
the local user or by root.
.TP
\fBkcm_mach_service\fP
On macOS only, determines the name of the bootstrap service used to
contact the KCM daemon for the KCM credential cache type.  If the
value is \fB\-\fP, Mach RPC will not be used to contact the KCM
daemon.  The default value is \fBorg.h5l.kcm\fP\&.
.TP
\fBkcm_socket\fP
Determines the path to the Unix domain socket used to access the
KCM daemon for the KCM credential cache type.  If the value is
\fB\-\fP, Unix domain sockets will not be used to contact the KCM
daemon.  The default value is
\fB/var/run/.heim_org.h5l.kcm\-socket\fP\&.
.TP
\fBkdc_default_options\fP
Default KDC options (Xored for multiple values) when requesting
initial tickets.  By default it is set to 0x00000010
(KDC_OPT_RENEWABLE_OK).
.TP
\fBkdc_timesync\fP
Accepted values for this relation are 1 or 0.  If it is nonzero,
client machines will compute the difference between their time and
the time returned by the KDC in the timestamps in the tickets and
use this value to correct for an inaccurate system clock when
requesting service tickets or authenticating to services.  This
corrective factor is only used by the Kerberos library; it is not
used to change the system clock.  The default value is 1.
.TP
\fBkdc_req_checksum_type\fP
An integer which specifies the type of checksum to use for the KDC
requests, for compatibility with very old KDC implementations.
This value is only used for DES keys; other keys use the preferred
checksum type for those keys.
.sp
The possible values and their meanings are as follows.
.TS
center;
|l|l|.
_
T{
1
T}	T{
CRC32
T}
_
T{
2
T}	T{
RSA MD4
T}
_
T{
3
T}	T{
RSA MD4 DES
T}
_
T{
4
T}	T{
DES CBC
T}
_
T{
7
T}	T{
RSA MD5
T}
_
T{
8
T}	T{
RSA MD5 DES
T}
_
T{
9
T}	T{
NIST SHA
T}
_
T{
12
T}	T{
HMAC SHA1 DES3
T}
_
T{
\-138
T}	T{
Microsoft MD5 HMAC checksum type
T}
_
.TE
.TP
\fBnoaddresses\fP
If this flag is true, requests for initial tickets will not be
made with address restrictions set, allowing the tickets to be
used across NATs.  The default value is true.
.TP
\fBpermitted_enctypes\fP
Identifies all encryption types that are permitted for use in
session key encryption.  The default value for this tag is
\fBaes256\-cts\-hmac\-sha1\-96 aes128\-cts\-hmac\-sha1\-96 aes256\-cts\-hmac\-sha384\-192 aes128\-cts\-hmac\-sha256\-128 des3\-cbc\-sha1 arcfour\-hmac\-md5 camellia256\-cts\-cmac camellia128\-cts\-cmac des\-cbc\-crc des\-cbc\-md5 des\-cbc\-md4\fP, but single\-DES encryption types will be implicitly
removed from this list if the value of \fBallow_weak_crypto\fP is
false.
.TP
\fBplugin_base_dir\fP
If set, determines the base directory where krb5 plugins are
located.  The default value is the \fBkrb5/plugins\fP subdirectory
of the krb5 library directory.  This relation is subject to
parameter expansion (see below) in release 1.17 and later.
.TP
\fBpreferred_preauth_types\fP
This allows you to set the preferred preauthentication types which
the client will attempt before others which may be advertised by a
KDC.  The default value for this setting is "17, 16, 15, 14",
which forces libkrb5 to attempt to use PKINIT if it is supported.
.TP
\fBproxiable\fP
If this flag is true, initial tickets will be proxiable by
default, if allowed by the KDC.  The default value is false.
.TP
\fBrdns\fP
If this flag is true, reverse name lookup will be used in addition
to forward name lookup to canonicalizing hostnames for use in
service principal names.  If \fBdns_canonicalize_hostname\fP is set
to false, this flag has no effect.  The default value is true.
.TP
\fBrealm_try_domains\fP
Indicate whether a host\(aqs domain components should be used to
determine the Kerberos realm of the host.  The value of this
variable is an integer: \-1 means not to search, 0 means to try the
host\(aqs domain itself, 1 means to also try the domain\(aqs immediate
parent, and so forth.  The library\(aqs usual mechanism for locating
Kerberos realms is used to determine whether a domain is a valid
realm, which may involve consulting DNS if \fBdns_lookup_kdc\fP is
set.  The default is not to search domain components.
.TP
\fBrenew_lifetime\fP
(duration string.)  Sets the default renewable lifetime
for initial ticket requests.  The default value is 0.
.TP
\fBsafe_checksum_type\fP
An integer which specifies the type of checksum to use for the
KRB\-SAFE requests.  By default it is set to 8 (RSA MD5 DES).  For
compatibility with applications linked against DCE version 1.1 or
earlier Kerberos libraries, use a value of 3 to use the RSA MD4
DES instead.  This field is ignored when its value is incompatible
with the session key type.  See the \fBkdc_req_checksum_type\fP
configuration option for the possible values and their meanings.
.TP
\fBspake_preauth_groups\fP
A whitespace or comma\-separated list of words which specifies the
groups allowed for SPAKE preauthentication.  The possible values
are:
.TS
center;
|l|l|.
_
T{
edwards25519
T}	T{
Edwards25519 curve (\fI\%RFC 7748\fP)
T}
_
T{
P\-256
T}	T{
NIST P\-256 curve (\fI\%RFC 5480\fP)
T}
_
T{
P\-384
T}	T{
NIST P\-384 curve (\fI\%RFC 5480\fP)
T}
_
T{
P\-521
T}	T{
NIST P\-521 curve (\fI\%RFC 5480\fP)
T}
_
.TE
.sp
The default value for the client is \fBedwards25519\fP\&.  The default
value for the KDC is empty.  New in release 1.17.
.TP
\fBticket_lifetime\fP
(duration string.)  Sets the default lifetime for initial
ticket requests.  The default value is 1 day.
.TP
\fBudp_preference_limit\fP
When sending a message to the KDC, the library will try using TCP
before UDP if the size of the message is above
\fBudp_preference_limit\fP\&.  If the message is smaller than
\fBudp_preference_limit\fP, then UDP will be tried before TCP.
Regardless of the size, both protocols will be tried if the first
attempt fails.
.TP
\fBverify_ap_req_nofail\fP
If this flag is true, then an attempt to verify initial
credentials will fail if the client machine does not have a
keytab.  The default value is false.
.UNINDENT
.SS [realms]
.sp
Each tag in the [realms] section of the file is the name of a Kerberos
realm.  The value of the tag is a subsection with relations that
define the properties of that particular realm.  For each realm, the
following tags may be specified in the realm\(aqs subsection:
.INDENT 0.0
.TP
\fBadmin_server\fP
Identifies the host where the administration server is running.
Typically, this is the master Kerberos server.  This tag must be
given a value in order to communicate with the kadmind(8)
server for the realm.
.TP
\fBauth_to_local\fP
This tag allows you to set a general rule for mapping principal
names to local user names.  It will be used if there is not an
explicit mapping for the principal name that is being
translated. The possible values are:
.INDENT 7.0
.TP
\fBRULE:\fP\fIexp\fP
The local name will be formulated from \fIexp\fP\&.
.sp
The format for \fIexp\fP is \fB[\fP\fIn\fP\fB:\fP\fIstring\fP\fB](\fP\fIregexp\fP\fB)s/\fP\fIpattern\fP\fB/\fP\fIreplacement\fP\fB/g\fP\&.
The integer \fIn\fP indicates how many components the target
principal should have.  If this matches, then a string will be
formed from \fIstring\fP, substituting the realm of the principal
for \fB$0\fP and the \fIn\fP\(aqth component of the principal for
\fB$n\fP (e.g., if the principal was \fBjohndoe/admin\fP then
\fB[2:$2$1foo]\fP would result in the string
\fBadminjohndoefoo\fP).  If this string matches \fIregexp\fP, then
the \fBs//[g]\fP substitution command will be run over the
string.  The optional \fBg\fP will cause the substitution to be
global over the \fIstring\fP, instead of replacing only the first
match in the \fIstring\fP\&.
.TP
\fBDEFAULT\fP
The principal name will be used as the local user name.  If
the principal has more than one component or is not in the
default realm, this rule is not applicable and the conversion
will fail.
.UNINDENT
.sp
For example:
.INDENT 7.0
.INDENT 3.5
.sp
.nf
.ft C
[realms]
    ATHENA.MIT.EDU = {
        auth_to_local = RULE:[2:$1](johndoe)s/^.*$/guest/
        auth_to_local = RULE:[2:$1;$2](^.*;admin$)s/;admin$//
        auth_to_local = RULE:[2:$2](^.*;root)s/^.*$/root/
        auth_to_local = DEFAULT
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
would result in any principal without \fBroot\fP or \fBadmin\fP as the
second component to be translated with the default rule.  A
principal with a second component of \fBadmin\fP will become its
first component.  \fBroot\fP will be used as the local name for any
principal with a second component of \fBroot\fP\&.  The exception to
these two rules are any principals \fBjohndoe/*\fP, which will
always get the local name \fBguest\fP\&.
.TP
\fBauth_to_local_names\fP
This subsection allows you to set explicit mappings from principal
names to local user names.  The tag is the mapping name, and the
value is the corresponding local user name.
.TP
\fBdefault_domain\fP
This tag specifies the domain used to expand hostnames when
translating Kerberos 4 service principals to Kerberos 5 principals
(for example, when converting \fBrcmd.hostname\fP to
\fBhost/hostname.domain\fP).
.TP
\fBdisable_encrypted_timestamp\fP
If this flag is true, the client will not perform encrypted
timestamp preauthentication if requested by the KDC.  Setting this
flag can help to prevent dictionary attacks by active attackers,
if the realm\(aqs KDCs support SPAKE preauthentication or if initial
authentication always uses another mechanism or always uses FAST.
This flag persists across client referrals during initial
authentication.  This flag does not prevent the KDC from offering
encrypted timestamp.  New in release 1.17.
.TP
\fBhttp_anchors\fP
When KDCs and kpasswd servers are accessed through HTTPS proxies, this tag
can be used to specify the location of the CA certificate which should be
trusted to issue the certificate for a proxy server.  If left unspecified,
the system\-wide default set of CA certificates is used.
.sp
The syntax for values is similar to that of values for the
\fBpkinit_anchors\fP tag:
.sp
\fBFILE:\fP \fIfilename\fP
.sp
\fIfilename\fP is assumed to be the name of an OpenSSL\-style ca\-bundle file.
.sp
\fBDIR:\fP \fIdirname\fP
.sp
\fIdirname\fP is assumed to be an directory which contains CA certificates.
All files in the directory will be examined; if they contain certificates
(in PEM format), they will be used.
.sp
\fBENV:\fP \fIenvvar\fP
.sp
\fIenvvar\fP specifies the name of an environment variable which has been set
to a value conforming to one of the previous values.  For example,
\fBENV:X509_PROXY_CA\fP, where environment variable \fBX509_PROXY_CA\fP has
been set to \fBFILE:/tmp/my_proxy.pem\fP\&.
.TP
\fBkdc\fP
The name or address of a host running a KDC for that realm.  An
optional port number, separated from the hostname by a colon, may
be included.  If the name or address contains colons (for example,
if it is an IPv6 address), enclose it in square brackets to
distinguish the colon from a port separator.  For your computer to
be able to communicate with the KDC for each realm, this tag must
be given a value in each realm subsection in the configuration
file, or there must be DNS SRV records specifying the KDCs.
.TP
\fBkpasswd_server\fP
Points to the server where all the password changes are performed.
If there is no such entry, DNS will be queried (unless forbidden
by \fBdns_lookup_kdc\fP).  Finally, port 464 on the \fBadmin_server\fP
host will be tried.
.TP
\fBmaster_kdc\fP
Identifies the master KDC(s).  Currently, this tag is used in only
one case: If an attempt to get credentials fails because of an
invalid password, the client software will attempt to contact the
master KDC, in case the user\(aqs password has just been changed, and
the updated database has not been propagated to the replica
servers yet.
.TP
\fBv4_instance_convert\fP
This subsection allows the administrator to configure exceptions
to the \fBdefault_domain\fP mapping rule.  It contains V4 instances
(the tag name) which should be translated to some specific
hostname (the tag value) as the second component in a Kerberos V5
principal name.
.TP
\fBv4_realm\fP
This relation is used by the krb524 library routines when
converting a V5 principal name to a V4 principal name.  It is used
when the V4 realm name and the V5 realm name are not the same, but
still share the same principal names and passwords. The tag value
is the Kerberos V4 realm name.
.UNINDENT
.SS [domain_realm]
.sp
The [domain_realm] section provides a translation from a domain name
or hostname to a Kerberos realm name.  The tag name can be a host name
or domain name, where domain names are indicated by a prefix of a
period (\fB\&.\fP).  The value of the relation is the Kerberos realm name
for that particular host or domain.  A host name relation implicitly
provides the corresponding domain name relation, unless an explicit domain
name relation is provided.  The Kerberos realm may be
identified either in the \fI\%realms\fP section or using DNS SRV records.
Host names and domain names should be in lower case.  For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
[domain_realm]
    crash.mit.edu = TEST.ATHENA.MIT.EDU
    .dev.mit.edu = TEST.ATHENA.MIT.EDU
    mit.edu = ATHENA.MIT.EDU
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
maps the host with the name \fBcrash.mit.edu\fP into the
\fBTEST.ATHENA.MIT.EDU\fP realm.  The second entry maps all hosts under the
domain \fBdev.mit.edu\fP into the \fBTEST.ATHENA.MIT.EDU\fP realm, but not
the host with the name \fBdev.mit.edu\fP\&.  That host is matched
by the third entry, which maps the host \fBmit.edu\fP and all hosts
under the domain \fBmit.edu\fP that do not match a preceding rule
into the realm \fBATHENA.MIT.EDU\fP\&.
.sp
If no translation entry applies to a hostname used for a service
principal for a service ticket request, the library will try to get a
referral to the appropriate realm from the client realm\(aqs KDC.  If
that does not succeed, the host\(aqs realm is considered to be the
hostname\(aqs domain portion converted to uppercase, unless the
\fBrealm_try_domains\fP setting in [libdefaults] causes a different
parent domain to be used.
.SS [capaths]
.sp
In order to perform direct (non\-hierarchical) cross\-realm
authentication, configuration is needed to determine the
authentication paths between realms.
.sp
A client will use this section to find the authentication path between
its realm and the realm of the server.  The server will use this
section to verify the authentication path used by the client, by
checking the transited field of the received ticket.
.sp
There is a tag for each participating client realm, and each tag has
subtags for each of the server realms.  The value of the subtags is an
intermediate realm which may participate in the cross\-realm
authentication.  The subtags may be repeated if there is more then one
intermediate realm.  A value of "." means that the two realms share
keys directly, and no intermediate realms should be allowed to
participate.
.sp
Only those entries which will be needed on the client or the server
need to be present.  A client needs a tag for its local realm with
subtags for all the realms of servers it will need to authenticate to.
A server needs a tag for each realm of the clients it will serve, with
a subtag of the server realm.
.sp
For example, \fBANL.GOV\fP, \fBPNL.GOV\fP, and \fBNERSC.GOV\fP all wish to
use the \fBES.NET\fP realm as an intermediate realm.  ANL has a sub
realm of \fBTEST.ANL.GOV\fP which will authenticate with \fBNERSC.GOV\fP
but not \fBPNL.GOV\fP\&.  The [capaths] section for \fBANL.GOV\fP systems
would look like this:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
[capaths]
    ANL.GOV = {
        TEST.ANL.GOV = .
        PNL.GOV = ES.NET
        NERSC.GOV = ES.NET
        ES.NET = .
    }
    TEST.ANL.GOV = {
        ANL.GOV = .
    }
    PNL.GOV = {
        ANL.GOV = ES.NET
    }
    NERSC.GOV = {
        ANL.GOV = ES.NET
    }
    ES.NET = {
        ANL.GOV = .
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
The [capaths] section of the configuration file used on \fBNERSC.GOV\fP
systems would look like this:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
[capaths]
    NERSC.GOV = {
        ANL.GOV = ES.NET
        TEST.ANL.GOV = ES.NET
        TEST.ANL.GOV = ANL.GOV
        PNL.GOV = ES.NET
        ES.NET = .
    }
    ANL.GOV = {
        NERSC.GOV = ES.NET
    }
    PNL.GOV = {
        NERSC.GOV = ES.NET
    }
    ES.NET = {
        NERSC.GOV = .
    }
    TEST.ANL.GOV = {
        NERSC.GOV = ANL.GOV
        NERSC.GOV = ES.NET
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
When a subtag is used more than once within a tag, clients will use
the order of values to determine the path.  The order of values is not
important to servers.
.SS [appdefaults]
.sp
Each tag in the [appdefaults] section names a Kerberos V5 application
or an option that is used by some Kerberos V5 application[s].  The
value of the tag defines the default behaviors for that application.
.sp
For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
[appdefaults]
    telnet = {
        ATHENA.MIT.EDU = {
            option1 = false
        }
    }
    telnet = {
        option1 = true
        option2 = true
    }
    ATHENA.MIT.EDU = {
        option2 = false
    }
    option2 = true
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
The above four ways of specifying the value of an option are shown in
order of decreasing precedence. In this example, if telnet is running
in the realm EXAMPLE.COM, it should, by default, have option1 and
option2 set to true.  However, a telnet program in the realm
\fBATHENA.MIT.EDU\fP should have \fBoption1\fP set to false and
\fBoption2\fP set to true.  Any other programs in ATHENA.MIT.EDU should
have \fBoption2\fP set to false by default.  Any programs running in
other realms should have \fBoption2\fP set to true.
.sp
The list of specifiable options for each application may be found in
that application\(aqs man pages.  The application defaults specified here
are overridden by those specified in the \fI\%realms\fP section.
.SS [plugins]
.INDENT 0.0
.INDENT 3.5
.INDENT 0.0
.IP \(bu 2
\fI\%pwqual\fP interface
.IP \(bu 2
\fI\%kadm5_hook\fP interface
.IP \(bu 2
\fI\%clpreauth\fP and \fI\%kdcpreauth\fP interfaces
.UNINDENT
.UNINDENT
.UNINDENT
.sp
Tags in the [plugins] section can be used to register dynamic plugin
modules and to turn modules on and off.  Not every krb5 pluggable
interface uses the [plugins] section; the ones that do are documented
here.
.sp
New in release 1.9.
.sp
Each pluggable interface corresponds to a subsection of [plugins].
All subsections support the same tags:
.INDENT 0.0
.TP
\fBdisable\fP
This tag may have multiple values. If there are values for this
tag, then the named modules will be disabled for the pluggable
interface.
.TP
\fBenable_only\fP
This tag may have multiple values. If there are values for this
tag, then only the named modules will be enabled for the pluggable
interface.
.TP
\fBmodule\fP
This tag may have multiple values.  Each value is a string of the
form \fBmodulename:pathname\fP, which causes the shared object
located at \fIpathname\fP to be registered as a dynamic module named
\fImodulename\fP for the pluggable interface.  If \fIpathname\fP is not an
absolute path, it will be treated as relative to the
\fBplugin_base_dir\fP value from \fI\%[libdefaults]\fP\&.
.UNINDENT
.sp
For pluggable interfaces where module order matters, modules
registered with a \fBmodule\fP tag normally come first, in the order
they are registered, followed by built\-in modules in the order they
are documented below.  If \fBenable_only\fP tags are used, then the
order of those tags overrides the normal module order.
.sp
The following subsections are currently supported within the [plugins]
section:
.SS ccselect interface
.sp
The ccselect subsection controls modules for credential cache
selection within a cache collection.  In addition to any registered
dynamic modules, the following built\-in modules exist (and may be
disabled with the disable tag):
.INDENT 0.0
.TP
\fBk5identity\fP
Uses a .k5identity file in the user\(aqs home directory to select a
client principal
.TP
\fBrealm\fP
Uses the service realm to guess an appropriate cache from the
collection
.TP
\fBhostname\fP
If the service principal is host\-based, uses the service hostname
to guess an appropriate cache from the collection
.UNINDENT
.SS pwqual interface
.sp
The pwqual subsection controls modules for the password quality
interface, which is used to reject weak passwords when passwords are
changed.  The following built\-in modules exist for this interface:
.INDENT 0.0
.TP
\fBdict\fP
Checks against the realm dictionary file
.TP
\fBempty\fP
Rejects empty passwords
.TP
\fBhesiod\fP
Checks against user information stored in Hesiod (only if Kerberos
was built with Hesiod support)
.TP
\fBprinc\fP
Checks against components of the principal name
.UNINDENT
.SS kadm5_hook interface
.sp
The kadm5_hook interface provides plugins with information on
principal creation, modification, password changes and deletion.  This
interface can be used to write a plugin to synchronize MIT Kerberos
with another database such as Active Directory.  No plugins are built
in for this interface.
.SS kadm5_auth interface
.sp
The kadm5_auth section (introduced in release 1.16) controls modules
for the kadmin authorization interface, which determines whether a
client principal is allowed to perform a kadmin operation.  The
following built\-in modules exist for this interface:
.INDENT 0.0
.TP
\fBacl\fP
This module reads the kadm5.acl(5) file, and authorizes
operations which are allowed according to the rules in the file.
.TP
\fBself\fP
This module authorizes self\-service operations including password
changes, creation of new random keys, fetching the client\(aqs
principal record or string attributes, and fetching the policy
record associated with the client principal.
.UNINDENT
.SS clpreauth and kdcpreauth interfaces
.sp
The clpreauth and kdcpreauth interfaces allow plugin modules to
provide client and KDC preauthentication mechanisms.  The following
built\-in modules exist for these interfaces:
.INDENT 0.0
.TP
\fBpkinit\fP
This module implements the PKINIT preauthentication mechanism.
.TP
\fBencrypted_challenge\fP
This module implements the encrypted challenge FAST factor.
.TP
\fBencrypted_timestamp\fP
This module implements the encrypted timestamp mechanism.
.UNINDENT
.SS hostrealm interface
.sp
The hostrealm section (introduced in release 1.12) controls modules
for the host\-to\-realm interface, which affects the local mapping of
hostnames to realm names and the choice of default realm.  The following
built\-in modules exist for this interface:
.INDENT 0.0
.TP
\fBprofile\fP
This module consults the [domain_realm] section of the profile for
authoritative host\-to\-realm mappings, and the \fBdefault_realm\fP
variable for the default realm.
.TP
\fBdns\fP
This module looks for DNS records for fallback host\-to\-realm
mappings and the default realm.  It only operates if the
\fBdns_lookup_realm\fP variable is set to true.
.TP
\fBdomain\fP
This module applies heuristics for fallback host\-to\-realm
mappings.  It implements the \fBrealm_try_domains\fP variable, and
uses the uppercased parent domain of the hostname if that does not
produce a result.
.UNINDENT
.SS localauth interface
.sp
The localauth section (introduced in release 1.12) controls modules
for the local authorization interface, which affects the relationship
between Kerberos principals and local system accounts.  The following
built\-in modules exist for this interface:
.INDENT 0.0
.TP
\fBdefault\fP
This module implements the \fBDEFAULT\fP type for \fBauth_to_local\fP
values.
.TP
\fBrule\fP
This module implements the \fBRULE\fP type for \fBauth_to_local\fP
values.
.TP
\fBnames\fP
This module looks for an \fBauth_to_local_names\fP mapping for the
principal name.
.TP
\fBauth_to_local\fP
This module processes \fBauth_to_local\fP values in the default
realm\(aqs section, and applies the default method if no
\fBauth_to_local\fP values exist.
.TP
\fBk5login\fP
This module authorizes a principal to a local account according to
the account\(aqs \&.k5login(5) file.
.TP
\fBan2ln\fP
This module authorizes a principal to a local account if the
principal name maps to the local account name.
.UNINDENT
.SS certauth interface
.sp
The certauth section (introduced in release 1.16) controls modules for
the certificate authorization interface, which determines whether a
certificate is allowed to preauthenticate a user via PKINIT.  The
following built\-in modules exist for this interface:
.INDENT 0.0
.TP
\fBpkinit_san\fP
This module authorizes the certificate if it contains a PKINIT
Subject Alternative Name for the requested client principal, or a
Microsoft UPN SAN matching the principal if \fBpkinit_allow_upn\fP
is set to true for the realm.
.TP
\fBpkinit_eku\fP
This module rejects the certificate if it does not contain an
Extended Key Usage attribute consistent with the
\fBpkinit_eku_checking\fP value for the realm.
.TP
\fBdbmatch\fP
This module authorizes or rejects the certificate according to
whether it matches the \fBpkinit_cert_match\fP string attribute on
the client principal, if that attribute is present.
.UNINDENT
.SH PKINIT OPTIONS
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
The following are PKINIT\-specific options.  These values may
be specified in [libdefaults] as global defaults, or within
a realm\-specific subsection of [libdefaults], or may be
specified as realm\-specific values in the [realms] section.
A realm\-specific value overrides, not adds to, a generic
[libdefaults] specification.  The search order is:
.UNINDENT
.UNINDENT
.INDENT 0.0
.IP 1. 3
realm\-specific subsection of [libdefaults]:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
[libdefaults]
    EXAMPLE.COM = {
        pkinit_anchors = FILE:/usr/local/example.com.crt
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.IP 2. 3
realm\-specific value in the [realms] section:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
[realms]
    OTHERREALM.ORG = {
        pkinit_anchors = FILE:/usr/local/otherrealm.org.crt
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.IP 3. 3
generic value in the [libdefaults] section:
.INDENT 3.0
.INDENT 3.5
.sp
.nf
.ft C
[libdefaults]
    pkinit_anchors = DIR:/usr/local/generic_trusted_cas/
.ft P
.fi
.UNINDENT
.UNINDENT
.UNINDENT
.SS Specifying PKINIT identity information
.sp
The syntax for specifying Public Key identity, trust, and revocation
information for PKINIT is as follows:
.INDENT 0.0
.TP
\fBFILE:\fP\fIfilename\fP[\fB,\fP\fIkeyfilename\fP]
This option has context\-specific behavior.
.sp
In \fBpkinit_identity\fP or \fBpkinit_identities\fP, \fIfilename\fP
specifies the name of a PEM\-format file containing the user\(aqs
certificate.  If \fIkeyfilename\fP is not specified, the user\(aqs
private key is expected to be in \fIfilename\fP as well.  Otherwise,
\fIkeyfilename\fP is the name of the file containing the private key.
.sp
In \fBpkinit_anchors\fP or \fBpkinit_pool\fP, \fIfilename\fP is assumed to
be the name of an OpenSSL\-style ca\-bundle file.
.TP
\fBDIR:\fP\fIdirname\fP
This option has context\-specific behavior.
.sp
In \fBpkinit_identity\fP or \fBpkinit_identities\fP, \fIdirname\fP
specifies a directory with files named \fB*.crt\fP and \fB*.key\fP
where the first part of the file name is the same for matching
pairs of certificate and private key files.  When a file with a
name ending with \fB\&.crt\fP is found, a matching file ending with
\fB\&.key\fP is assumed to contain the private key.  If no such file
is found, then the certificate in the \fB\&.crt\fP is not used.
.sp
In \fBpkinit_anchors\fP or \fBpkinit_pool\fP, \fIdirname\fP is assumed to
be an OpenSSL\-style hashed CA directory where each CA cert is
stored in a file named \fBhash\-of\-ca\-cert.#\fP\&.  This infrastructure
is encouraged, but all files in the directory will be examined and
if they contain certificates (in PEM format), they will be used.
.sp
In \fBpkinit_revoke\fP, \fIdirname\fP is assumed to be an OpenSSL\-style
hashed CA directory where each revocation list is stored in a file
named \fBhash\-of\-ca\-cert.r#\fP\&.  This infrastructure is encouraged,
but all files in the directory will be examined and if they
contain a revocation list (in PEM format), they will be used.
.TP
\fBPKCS12:\fP\fIfilename\fP
\fIfilename\fP is the name of a PKCS #12 format file, containing the
user\(aqs certificate and private key.
.TP
\fBPKCS11:\fP[\fBmodule_name=\fP]\fImodname\fP[\fB:slotid=\fP\fIslot\-id\fP][\fB:token=\fP\fItoken\-label\fP][\fB:certid=\fP\fIcert\-id\fP][\fB:certlabel=\fP\fIcert\-label\fP]
All keyword/values are optional.  \fImodname\fP specifies the location
of a library implementing PKCS #11.  If a value is encountered
with no keyword, it is assumed to be the \fImodname\fP\&.  If no
module\-name is specified, the default is \fBopensc\-pkcs11.so\fP\&.
\fBslotid=\fP and/or \fBtoken=\fP may be specified to force the use of
a particular smard card reader or token if there is more than one
available.  \fBcertid=\fP and/or \fBcertlabel=\fP may be specified to
force the selection of a particular certificate on the device.
See the \fBpkinit_cert_match\fP configuration option for more ways
to select a particular certificate to use for PKINIT.
.TP
\fBENV:\fP\fIenvvar\fP
\fIenvvar\fP specifies the name of an environment variable which has
been set to a value conforming to one of the previous values.  For
example, \fBENV:X509_PROXY\fP, where environment variable
\fBX509_PROXY\fP has been set to \fBFILE:/tmp/my_proxy.pem\fP\&.
.UNINDENT
.SS PKINIT krb5.conf options
.INDENT 0.0
.TP
\fBpkinit_anchors\fP
Specifies the location of trusted anchor (root) certificates which
the client trusts to sign KDC certificates.  This option may be
specified multiple times.  These values from the config file are
not used if the user specifies X509_anchors on the command line.
.TP
\fBpkinit_cert_match\fP
Specifies matching rules that the client certificate must match
before it is used to attempt PKINIT authentication.  If a user has
multiple certificates available (on a smart card, or via other
media), there must be exactly one certificate chosen before
attempting PKINIT authentication.  This option may be specified
multiple times.  All the available certificates are checked
against each rule in order until there is a match of exactly one
certificate.
.sp
The Subject and Issuer comparison strings are the \fI\%RFC 2253\fP
string representations from the certificate Subject DN and Issuer
DN values.
.sp
The syntax of the matching rules is:
.INDENT 7.0
.INDENT 3.5
[\fIrelation\-operator\fP]\fIcomponent\-rule\fP ...
.UNINDENT
.UNINDENT
.sp
where:
.INDENT 7.0
.TP
.B \fIrelation\-operator\fP
can be either \fB&&\fP, meaning all component rules must match,
or \fB||\fP, meaning only one component rule must match.  The
default is \fB&&\fP\&.
.TP
.B \fIcomponent\-rule\fP
can be one of the following.  Note that there is no
punctuation or whitespace between component rules.
.INDENT 7.0
.INDENT 3.5
.nf
\fB<SUBJECT>\fP\fIregular\-expression\fP
\fB<ISSUER>\fP\fIregular\-expression\fP
\fB<SAN>\fP\fIregular\-expression\fP
\fB<EKU>\fP\fIextended\-key\-usage\-list\fP
\fB<KU>\fP\fIkey\-usage\-list\fP
.fi
.sp
.UNINDENT
.UNINDENT
.sp
\fIextended\-key\-usage\-list\fP is a comma\-separated list of
required Extended Key Usage values.  All values in the list
must be present in the certificate.  Extended Key Usage values
can be:
.INDENT 7.0
.IP \(bu 2
pkinit
.IP \(bu 2
msScLogin
.IP \(bu 2
clientAuth
.IP \(bu 2
emailProtection
.UNINDENT
.sp
\fIkey\-usage\-list\fP is a comma\-separated list of required Key
Usage values.  All values in the list must be present in the
certificate.  Key Usage values can be:
.INDENT 7.0
.IP \(bu 2
digitalSignature
.IP \(bu 2
keyEncipherment
.UNINDENT
.UNINDENT
.sp
Examples:
.INDENT 7.0
.INDENT 3.5
.sp
.nf
.ft C
pkinit_cert_match = ||<SUBJECT>.*DoE.*<SAN>.*@EXAMPLE.COM
pkinit_cert_match = &&<EKU>msScLogin,clientAuth<ISSUER>.*DoE.*
pkinit_cert_match = <EKU>msScLogin,clientAuth<KU>digitalSignature
.ft P
.fi
.UNINDENT
.UNINDENT
.TP
\fBpkinit_eku_checking\fP
This option specifies what Extended Key Usage value the KDC
certificate presented to the client must contain.  (Note that if
the KDC certificate has the pkinit SubjectAlternativeName encoded
as the Kerberos TGS name, EKU checking is not necessary since the
issuing CA has certified this as a KDC certificate.)  The values
recognized in the krb5.conf file are:
.INDENT 7.0
.TP
\fBkpKDC\fP
This is the default value and specifies that the KDC must have
the id\-pkinit\-KPKdc EKU as defined in \fI\%RFC 4556\fP\&.
.TP
\fBkpServerAuth\fP
If \fBkpServerAuth\fP is specified, a KDC certificate with the
id\-kp\-serverAuth EKU will be accepted.  This key usage value
is used in most commercially issued server certificates.
.TP
\fBnone\fP
If \fBnone\fP is specified, then the KDC certificate will not be
checked to verify it has an acceptable EKU.  The use of this
option is not recommended.
.UNINDENT
.TP
\fBpkinit_dh_min_bits\fP
Specifies the size of the Diffie\-Hellman key the client will
attempt to use.  The acceptable values are 1024, 2048, and 4096.
The default is 2048.
.TP
\fBpkinit_identities\fP
Specifies the location(s) to be used to find the user\(aqs X.509
identity information.  If this option is specified multiple times,
the first valid value is used; this can be used to specify an
environment variable (with \fBENV:\fP\fIenvvar\fP) followed by a
default value.  Note that these values are not used if the user
specifies \fBX509_user_identity\fP on the command line.
.TP
\fBpkinit_kdc_hostname\fP
The presense of this option indicates that the client is willing
to accept a KDC certificate with a dNSName SAN (Subject
Alternative Name) rather than requiring the id\-pkinit\-san as
defined in \fI\%RFC 4556\fP\&.  This option may be specified multiple
times.  Its value should contain the acceptable hostname for the
KDC (as contained in its certificate).
.TP
\fBpkinit_pool\fP
Specifies the location of intermediate certificates which may be
used by the client to complete the trust chain between a KDC
certificate and a trusted anchor.  This option may be specified
multiple times.
.TP
\fBpkinit_require_crl_checking\fP
The default certificate verification process will always check the
available revocation information to see if a certificate has been
revoked.  If a match is found for the certificate in a CRL,
verification fails.  If the certificate being verified is not
listed in a CRL, or there is no CRL present for its issuing CA,
and \fBpkinit_require_crl_checking\fP is false, then verification
succeeds.
.sp
However, if \fBpkinit_require_crl_checking\fP is true and there is
no CRL information available for the issuing CA, then verification
fails.
.sp
\fBpkinit_require_crl_checking\fP should be set to true if the
policy is such that up\-to\-date CRLs must be present for every CA.
.TP
\fBpkinit_revoke\fP
Specifies the location of Certificate Revocation List (CRL)
information to be used by the client when verifying the validity
of the KDC certificate presented.  This option may be specified
multiple times.
.UNINDENT
.SH PARAMETER EXPANSION
.sp
Starting with release 1.11, several variables, such as
\fBdefault_keytab_name\fP, allow parameters to be expanded.
Valid parameters are:
.INDENT 0.0
.INDENT 3.5
.TS
center;
|l|l|.
_
T{
%{TEMP}
T}	T{
Temporary directory
T}
_
T{
%{uid}
T}	T{
Unix real UID or Windows SID
T}
_
T{
%{euid}
T}	T{
Unix effective user ID or Windows SID
T}
_
T{
%{USERID}
T}	T{
Same as %{uid}
T}
_
T{
%{null}
T}	T{
Empty string
T}
_
T{
%{LIBDIR}
T}	T{
Installation library directory
T}
_
T{
%{BINDIR}
T}	T{
Installation binary directory
T}
_
T{
%{SBINDIR}
T}	T{
Installation admin binary directory
T}
_
T{
%{username}
T}	T{
(Unix) Username of effective user ID
T}
_
T{
%{APPDATA}
T}	T{
(Windows) Roaming application data for current user
T}
_
T{
%{COMMON_APPDATA}
T}	T{
(Windows) Application data for all users
T}
_
T{
%{LOCAL_APPDATA}
T}	T{
(Windows) Local application data for current user
T}
_
T{
%{SYSTEM}
T}	T{
(Windows) Windows system folder
T}
_
T{
%{WINDOWS}
T}	T{
(Windows) Windows folder
T}
_
T{
%{USERCONFIG}
T}	T{
(Windows) Per\-user MIT krb5 config file directory
T}
_
T{
%{COMMONCONFIG}
T}	T{
(Windows) Common MIT krb5 config file directory
T}
_
.TE
.UNINDENT
.UNINDENT
.SH SAMPLE KRB5.CONF FILE
.sp
Here is an example of a generic krb5.conf file:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
[libdefaults]
    default_realm = ATHENA.MIT.EDU
    dns_lookup_kdc = true
    dns_lookup_realm = false

[realms]
    ATHENA.MIT.EDU = {
        kdc = kerberos.mit.edu
        kdc = kerberos\-1.mit.edu
        kdc = kerberos\-2.mit.edu
        admin_server = kerberos.mit.edu
        master_kdc = kerberos.mit.edu
    }
    EXAMPLE.COM = {
        kdc = kerberos.example.com
        kdc = kerberos\-1.example.com
        admin_server = kerberos.example.com
    }

[domain_realm]
    mit.edu = ATHENA.MIT.EDU

[capaths]
    ATHENA.MIT.EDU = {
           EXAMPLE.COM = .
    }
    EXAMPLE.COM = {
           ATHENA.MIT.EDU = .
    }
.ft P
.fi
.UNINDENT
.UNINDENT
.SH FILES
.sp
\fB/etc/krb5.conf\fP
.SH SEE ALSO
.sp
syslog(3)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2019, MIT
.\" Generated by docutils manpage writer.
.
