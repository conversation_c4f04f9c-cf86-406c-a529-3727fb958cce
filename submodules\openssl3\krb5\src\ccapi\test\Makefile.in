mydir=ccapi$(S)test
BUILDTOP=..$(S)..
CCAPI=$(BUILDTOP)$(S)CCAPI

!if defined(KRB5_KFW_COMPILE)
KFWINC= /I$(BUILDTOP)\..\..\krbcc\include
!endif

# Because all the sources are in .,
#  the only includes we need are to directories outside of ccapi.
LOCALINCLUDES = /I$(BUILDTOP) /I$(BUILDTOP)$(S)include /I$(BUILDTOP)$(S)include$(S)krb5 $(KFWINC) \
    -I$(BUILDTOP)$(S)util$(S)et /I. -I$(CCAPI)$(S)COMMON -I$(CCAPI)$(S)LIB

# run with "make all" to create CCAPI tests in "/tmp/ccapi_test"
# run resulting tests with "sh test_ccapi.sh"

##DOS##CPPFLAGS = $(CPPFLAGS) /EHsc -D_CRTAPI1=_cdecl -D_CRTAPI2=_cdecl -DWINVER=0x0501 \
##DOS##    -D_WIN32_WINNT=0x0501 -D_CRT_SECURE_NO_WARNINGS

##DOS##WINH = cci_debugging.h \
##DOS##       ccs_reply.h \
##DOS##       ccs_request.h \
##DOS##       ccs_request_c.c \
##DOS##       cci_types.h \
##DOS##       win-utils.h

##DOS##LIBSRC=ccapi_ccache.c \
##DOS##       ccapi_ccache_iterator.c \
##DOS##       ccapi_context.c \
##DOS##       ccapi_context_change_time.c \
##DOS##       ccapi_err.c \
##DOS##       ccapi_ipc.c \
##DOS##       ccapi_credentials.c \
##DOS##       ccapi_credentials_iterator.c \
##DOS##       ccapi_string.c \
##DOS##       ccapi_v2.c

##DOS##COMSRC=cci_cred_union.c \
##DOS##       cci_identifier.c \
##DOS##       cci_message.c

##DOS##COWSRC=cci_os_identifier.c

SRCDIR  = .
DSTROOT = $(SRCDIR)
OBJDIR  = $(DSTROOT)$(S)ccapi_intermediates
DSTDIR  = $(DSTROOT)$(S)ccapi_test
TESTDIR = $(DSTDIR)$(S)tests
SRCTMP  = $(SRCDIR)\srctmp

SCRIPT_NAME = test_ccapi.sh

OBJECTS =   $(OUTPRE)test_ccapi_ccache.$(OBJEXT) \
            $(OUTPRE)test_ccapi_check.$(OBJEXT) \
            $(OUTPRE)test_ccapi_constants.$(OBJEXT) \
            $(OUTPRE)test_ccapi_context.$(OBJEXT) \
            $(OUTPRE)test_ccapi_v2.$(OBJEXT) \
            $(OUTPRE)test_ccapi_globals.$(OBJEXT) \
            $(OUTPRE)test_ccapi_iterators.$(OBJEXT) \
            $(OUTPRE)test_ccapi_log.$(OBJEXT) \
            $(OUTPRE)test_ccapi_util.$(OBJEXT)

PINGOBJS =  $(OUTPRE)ccapi_ccache.$(OBJEXT) \
            $(OUTPRE)ccapi_ccache_iterator.$(OBJEXT) \
            $(OUTPRE)ccapi_context.$(OBJEXT) \
            $(OUTPRE)ccapi_context_change_time.$(OBJEXT) \
            $(OUTPRE)ccapi_err.$(OBJEXT) \
            $(OUTPRE)ccapi_ipc.$(OBJEXT) \
            $(OUTPRE)ccapi_credentials.$(OBJEXT) \
            $(OUTPRE)ccapi_credentials_iterator.$(OBJEXT) \
            $(OUTPRE)ccapi_string.$(OBJEXT) \
            $(OUTPRE)ccapi_v2.$(OBJEXT) \
            $(OUTPRE)cci_cred_union.$(OBJEXT) \
            $(OUTPRE)cci_identifier.$(OBJEXT) \
            $(OUTPRE)cci_os_identifier.$(OBJEXT) \
            $(OUTPRE)cci_message.$(OBJEXT) \
            $(OUTPRE)ccs_request_c.$(OBJEXT) \
            $(OUTPRE)pingtest.$(OBJEXT) \
            $(OBJECTS)

TESTALLOBJS=$(OUTPRE)main.$(OBJEXT) \
            $(OBJECTS)

TEST_NAMES =    test_cc_ccache_iterator_next \
                test_constants \
                test_cc_initialize \
                test_cc_credentials_iterator_next

MORE_TESTS =    test_cc_context_release \
                test_cc_context_get_change_time \
                test_cc_context_get_default_ccache_name \
                test_cc_context_open_ccache \
                test_cc_context_open_default_ccache \
                test_cc_context_create_ccache \
                test_cc_context_create_default_ccache \
                test_cc_context_create_new_ccache \
                test_cc_context_new_ccache_iterator \
                test_cc_context_compare \
                test_cc_ccache_release \
                test_cc_ccache_destroy \
                test_cc_ccache_set_default \
                test_cc_ccache_get_credentials_version \
                test_cc_ccache_get_name \
                test_cc_ccache_get_principal \
                test_cc_ccache_set_principal \
                test_cc_ccache_store_credentials \
                test_cc_ccache_remove_credentials \
                test_cc_ccache_new_credentials_iterator \
                test_cc_ccache_get_change_time \
                test_cc_ccache_get_last_default_time \
                test_cc_ccache_move \
                test_cc_ccache_compare \
                test_cc_ccache_get_kdc_time_offset \
                test_cc_ccache_set_kdc_time_offset \
                test_cc_ccache_clear_kdc_time_offset \
                test_cc_shutdown \
                test_cc_get_change_time \
                test_cc_open \
                test_cc_create \
                test_cc_close \
                test_cc_destroy \
                test_cc_get_cred_version \
                test_cc_get_name \
                test_cc_get_principal \
                test_cc_set_principal \
                test_cc_store \
                test_cc_remove_cred \
                test_cc_seq_fetch_NCs_begin \
                test_cc_seq_fetch_NCs_next \
                test_cc_seq_fetch_creds_begin \
                test_cc_seq_fetch_creds_next \
                test_cc_get_NC_info


##### Linker
LINK	= link
LIBS    = -lkrb5
##DOS##LIBS = $(CLIB) $(SLIB) advapi32.lib rpcrt4.lib user32.lib ws2_32.lib $(CCLIB).lib
LFLAGS	= /nologo $(LOPTS)

all-mac:     setup-test-dir pingtest simple_lock_test build-base build-tests link-tests copy-script success-message
all-windows: setup-windows build-base $(OUTPRE)pingtest.exe build-tests build-testall copy-script success-message

# compile base files used by all tests
build-base: $(PINGOBJS)

##++ These two rules build each element of the list:
# compile each test
build-tests: $(TEST_NAMES)
    @echo build-tests complete.

$(TEST_NAMES):
    @echo DBG: $@
    $(CC) $(ALL_CFLAGS) -Fe$(TESTDIR)$(S)$@.exe -Fd$(OBJDIR)$(S)$@.pdb $@.c $(OBJECTS) $(LIBS)
# Clean .obj from .:
    $(RM) $@.$(OBJEXT)
##-- These two rules build each element of the list.

# Make a build directory
setup-test-dir:
	@echo "Removing old destination directory... $(DSTDIR)"
	if [ -d "$(DSTDIR)" ]; then chmod -R u+w "$(DSTDIR)" && rm -rf "$(DSTDIR)"; fi
	mkdir -p "$(TESTDIR)"
	if [ -d "$(OBJDIR)" ]; then chmod -R u+w "$(OBJDIR)" && rm -rf "$(OBJDIR)"; fi
	mkdir -p "$(OBJDIR)"

## The same trick as used in TEST_NAMES to run an action on each element ofthe list WINH:
setup-windows: $(WINH) $(LIBSRC) $(COMSRC) $(COWSRC)
	if NOT exist $(TESTDIR) mkdir $(TESTDIR)
	if NOT exist $(OBJDIR)  mkdir $(OBJDIR)
	set LINK = link

# This rule assumes that nmake in ..\lib\win has already run.
#   That is how ..\Makefile.in is set up.
$(WINH):
    copy ..\lib\win\srctmp\$@ .

$(LIBSRC):
    copy ..\lib\$@ .

$(COMSRC):
    copy ..\common\$@ .

$(COWSRC):
    copy ..\common\win\$@ .

# This rule assumes that nmake in ..\lib\win\ has already run.
$(OUTPRE)pingtest.exe: $(OBJECTS) $(PINGOBJS)
# There doesn't appear to be any way to examine a variable and return a value
#  indicating whether a string is present in it.  We use a perl script to
#  check the LIB variable.  If the path to $(CCLIB).lib isn't present, the script
#  deletes a.tmp and the following nmake actions correct LIB.
	echo %%PATH%% > a.tmp
    perl setlib.pl
    if not exist a.tmp (
        @echo Adding ..\lib\win\srctmp to LIB
        set LIB=%%LIB%%;..\lib\win\srctmp
        )
    $(LINK) $(linkdebug) /map:$(@B)1.map -out:$(*B)1.exe $(conflags) $(PINGOBJS) $(LIBS)
	$(LINK) $(LFLAGS)    /map:$(@B)2.map /out:$(*B)2.exe $(conflags) $(PINGOBJS) $(LIBS) $(conlibsdll)

link-tests: $(TEST_NAMES)

build-testall: $(TEST_NAMES) $(OBJECTS) $(TESTALLOBJS) testall.exe

testall.exe:
    $(LINK) $(linkdebug) /map:$(@B)1.map -out:$(*B)1.exe $(conflags) $(TESTALLOBJS) $(LIBS) $(conslibdll)


simple_lock_test:
	$(CC) -o $(TESTDIR)/simple_lock_test simple_lock_test.c $(LIBS)

copy-script:
	$(CP) $(SCRIPT_NAME) $(DSTDIR)$(S)$(SCRIPT_NAME)

success-message:
	@echo
	@echo "CCAPI tests created in $(DSTDIR)"

.PHONY: clean

clean:
	-rm -rf "$(OBJDIR)"
	-rm -rf $(LIBSRC)
	-rm -rf $(WINH)
