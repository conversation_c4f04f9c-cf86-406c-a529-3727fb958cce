#!/bin/sh

DUMMY=${TESTDIR=$TOP/testing}
DUMMY=${KRB5RCACHEDIR=$TESTDIR}

while [ $# -gt 0 ] ; do
	case $1 in
		-start_servers)
			start_servers=$1
			;;
		*) 
			TOP=$1
			export TOP
			;;
	esac
	shift
done

# kill any running servers.

if $VERBOSE; then echo "Killing servers:"; fi

for pid in xxx \
	`$PS_ALL | grep krb5kdc | grep -v grep | awk '{print $2}'` \
	`$PS_ALL | grep kadmind | grep -v grep | awk '{print $2}'` \
	; do
	case "$pid" in
		xxx)
			;;
		*)
			if $VERBOSE; then $PS_PID$pid | grep -v COMMAND; fi
			kill $pid
			;;
	esac
done

# Destroy the kdc replay cache so we don't lose if we try to run the
# KDC as another unix user.
if test "x$USER" = x ; then
  USER=$LOGNAME
fi
rm -f $KRB5RCACHEDIR/krb5kdc_rcache.$USER

exit 0
