=pod

=head1 NAME

life_cycle-digest - The digest algorithm life-cycle

=head1 DESCRIPTION

All message digests (MDs) go through a number of stages in their life-cycle:

=over 4

=item start

This state represents the MD before it has been allocated.  It is the
starting state for any life-cycle transitions.

=item newed

This state represents the MD after it has been allocated.

=item initialised

This state represents the MD when it is set up and capable of processing
input.

=item updated

This state represents the MD when it is set up and capable of processing
additional input or generating output.

=item finaled

This state represents the MD when it has generated output.

=item freed

This state is entered when the MD is freed.  It is the terminal state
for all life-cycle transitions.

=back

=head2 State Transition Diagram

The usual life-cycle of a MD is illustrated:

=begin man

                     +-------------------+
                     |       start       |
                     +-------------------+
                       |
                       | EVP_MD_CTX_new
                       v
                     +-------------------+         EVP_MD_CTX_reset
                     |       newed       | <------------------------------+
                     +-------------------+                                |
                       |                                                  |
                       | EVP_DigestInit                                   |
                       v                                                  |
                     +-------------------+                                |
                +--> |    initialised    | <+ EVP_DigestInit              |
                |    +-------------------+  |                             |
                |      |                    |      EVP_DigestUpdate       |
                |      | EVP_DigestUpdate   |    +------------------+     |
                |      v                    |    v                  |     |
                |    +------------------------------------------------+   |
 EVP_DigestInit |    |                    updated                     | --+
                |    +------------------------------------------------+   |
                |      |                    |                             |
                |      | EVP_DigestFinal    | EVP_DigestFinalXOF          |
                |      v                    v                             |
                |    +------------------------------------------------+   |
                +--- |                    finaled                     | --+
                     +------------------------------------------------+
                       |
                       | EVP_MD_CTX_free
                       v
                     +-------------------+
                     |       freed       |
                     +-------------------+

=end man

=for html <img src="img/digest.png">

=head2 Formal State Transitions

This section defines all of the legal state transitions.
This is the canonical list.

=begin man

 Function Call                --------------------- Current State ----------------------
                              start   newed    initialised   updated     finaled   freed
 EVP_MD_CTX_new               newed
 EVP_DigestInit                    initialised initialised initialised initialised
 EVP_DigestUpdate                                updated     updated
 EVP_DigestFinal                                             finaled
 EVP_DigestFinalXOF                                          finaled
 EVP_MD_CTX_free              freed   freed       freed       freed       freed
 EVP_MD_CTX_reset                     newed       newed       newed       newed
 EVP_MD_CTX_get_params                newed    initialised   updated
 EVP_MD_CTX_set_params                newed    initialised   updated
 EVP_MD_CTX_gettable_params           newed    initialised   updated
 EVP_MD_CTX_settable_params           newed    initialised   updated

=end man

=begin html

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="6">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">initialised</th>
    <th style="border:1px solid" align="center">updated</th>
    <th style="border:1px solid" align="center">finaled</th>
    <th style="border:1px solid" align="center">freed</th></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DigestInit</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DigestUpdate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DigestFinal</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">finaled</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_DigestFinalXOF</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">finaled</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_reset</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_MD_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">initialised</td>
    <td style="border:1px solid" align="center">updated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
</table>

=end html

=head1 NOTES

At some point the EVP layer will begin enforcing the transitions described
herein.

=head1 SEE ALSO

L<provider-digest(7)>, L<EVP_DigestInit(3)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
