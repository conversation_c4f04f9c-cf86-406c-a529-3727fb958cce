# MsQuic 工具集详细介绍

MsQuic 项目中的 tools 目录包含了13个用于测试、调试和演示 QUIC 协议功能的工具。这些工具对于开发者理解 QUIC 协议、测试 MsQuic 实现以及进行性能评估非常有价值。以下是对这些工具的详细介绍。

## 1. attack - QUIC 攻击模拟工具

### 功能介绍
attack 工具用于模拟对 QUIC 连接的各种攻击场景，帮助测试 MsQuic 实现的安全性和鲁棒性。

### 主要文件
- `attack.cpp` - 主程序实现
- `packet_writer.cpp/h` - 数据包构造和发送功能

### 参数说明
- `-list` - 列出支持的攻击类型
- `-type:<number>` - 指定攻击类型，可选值：
  - `0` - 随机 TCP SYN 包攻击
  - `1` - 随机 1 字节 UDP 包攻击
  - `2` - 随机完整长度 UDP 包攻击
  - `3` - 随机 QUIC 初始包攻击
  - `4` - 有效 QUIC 初始包攻击
- `-ip:<ip_address_and_port>` - 指定目标服务器 IP 地址和端口
- `-alpn:<protocol_name>` - 指定应用层协议协商值，默认为 h3
- `-sni:<host_name>` - 指定服务器名称指示
- `-timeout:<ms>` - 指定攻击持续时间（毫秒），默认为 60000
- `-threads:<count>` - 指定线程数，默认为处理器核心数
- `-rate:<packet_rate>` - 指定数据包发送速率，默认为 1000000

### 使用示例
```bash
# 列出支持的攻击类型
attack.exe -list

# 执行随机 TCP SYN 包攻击
attack.exe -type:0 -ip:************:443 -timeout:30000

# 执行有效 QUIC 初始包攻击，使用自定义 ALPN 和 SNI
attack.exe -type:4 -ip:************:443 -alpn:h3 -sni:example.com -threads:8 -rate:500000
```

## 2. etw - ETW 跟踪工具

### 功能介绍
etw 工具用于收集和分析 MsQuic 生成的 ETW (Event Tracing for Windows) 事件，帮助开发者调试和分析 QUIC 连接的行为。

### 主要文件
- `main.c` - 主程序入口
- `binding.c` - 绑定相关事件处理
- `cxn.c` - 连接相关事件处理
- `listener.c` - 监听器相关事件处理
- `stream.c` - 流相关事件处理
- `worker.c` - 工作线程相关事件处理
- `library.c` - 库相关事件处理
- `session.c` - 会话相关事件处理
- `trace.c` - 跟踪相关功能

### 参数说明
根据 `main.c` 文件，etw 工具支持以下命令行参数：

#### 基本用法
```
quicetw <f.etl> [options] [command]
quicetw --local [options] [command]
```

#### 全局选项
- `--man` - 指定跟踪清单文件路径，或使用 'sdxroot' 从 %SDXROOT% 加载
- `--csv` - 以逗号分隔值格式输出结果

#### 通用命令
- `--help [command]` - 显示帮助文本
- `--summary` - 显示一般事件/文件信息
- `--report` - 生成跟踪中系统的报告
- `--trace` - 将所有 ETW 日志转换为文本

#### 连接命令
- `--conn [--sort <type>|--filter <type>|--id <num>|--cid <bytes>]` - 显示连接详细信息
- `--conn_list [--sort <type>|--filter <type>|--cid <bytes>] [--top <num>]` - 列出所有连接
- `--conn_tput [--sort <type>|--filter <type>|--id <num>|--cid <bytes>] [--reso <ms>] [--top <num>]` - 显示连接吞吐量
- `--conn_trace [--sort <type>|--filter <type>|--id <num>|--cid <bytes>] [--top <num>]` - 显示连接跟踪
- `--conn_qlog [--sort <type>|--filter <type>|--id <num>|--cid <bytes>]` - 生成连接的 QLOG 文件

#### 流命令
- `--stream_trace [--id <num>] [--top <num>]` - 显示流跟踪

#### 工作线程命令
- `--worker [--sort <type>] [--id <num>]` - 显示工作线程详细信息
- `--worker_list [--sort <type>] [--top <num>]` - 列出所有工作线程
- `--worker_queue [--sort <type>] [--id <num>] [--reso <ms>]` - 显示工作线程队列信息
- `--worker_trace [--sort <type>|--id <num>] [--top <num>]` - 显示工作线程跟踪

#### 命令选项
- `--sort <type>` - 指定排序顺序，可选值：{age,cpu_active,cpu_queued,cpu_idle,tx,rx,conn_count,shutdown}
- `--filter <type>` - 指定要查找的过滤器，可选值：{disconnect}
- `--id <num>` - 从 --conn_list 或 --worker_list 输出中获取的编号
- `--cid <bytes>` - 要搜索的连接 ID
- `--top <num>` - 限制输出行数
- `--reso <ms>` - 事件分辨率（毫秒）
- `--verbose` - 包含更详细的输出

### 使用示例
```bash
# 显示 ETL 文件的摘要信息
etw.exe trace.etl --summary

# 生成系统报告
etw.exe trace.etl --report

# 列出所有连接并排序
etw.exe trace.etl --conn_list --sort age

# 显示特定连接 ID 的详细信息
etw.exe trace.etl --conn --cid 0123456789abcdef

# 分析连接吞吐量，每 500 毫秒一个数据点
etw.exe trace.etl --conn_tput --id 1 --reso 500

# 生成连接的 QLOG 文件
etw.exe trace.etl --conn_qlog --id 1
```
## 3. forwarder - QUIC 流量转发工具

### 功能介绍
forwarder 工具是一个终端 QUIC 代理，用于将所有传入的 QUIC 流量转发到指定的目标服务器。它创建了一个透明的 QUIC 代理，可以用于测试、调试和分析 QUIC 连接。该工具在前端接收 QUIC 连接，然后在后端创建对应的连接，并在两者之间转发所有流量。

### 主要文件
- `forwarder.cpp` - 主程序实现，包含代理逻辑和命令行参数处理

### 参数说明
根据源代码中的 `USAGE` 定义和 `ParseArgs` 函数，forwarder 工具支持以下命令行参数：

- `<alpn>` - 应用层协议协商值（必需参数）
- `<local-port>` - 本地监听端口（必需参数）
- `<target-name/ip>:<target-port>` - 目标服务器地址和端口（必需参数）
- `<thumbprint>` - 证书指纹（必需参数）
- `[0/1-buffered-mode]` - 缓冲模式设置（可选参数，默认为 1 启用缓冲模式）
- `[fc-window]` - 流控窗口大小（可选参数）

### 工作原理
forwarder 工具通过以下方式工作：

1. 在指定的本地端口上创建 QUIC 监听器
2. 当接收到新连接时，创建一个到目标服务器的后端连接
3. 为每个前端流创建对应的后端流
4. 在前端和后端流之间转发所有数据
5. 支持两种数据转发模式：
   - 缓冲模式：接收到的数据被复制到新的缓冲区再转发
   - 非缓冲模式：直接使用原始缓冲区转发数据

### 使用示例
```bash
# 基本用法，将本地 4433 端口的 QUIC 流量转发到 example.com 的 443 端口
quicforward.exe h3 4433 example.com:443 0123456789abcdef0123456789abcdef01234567

# 禁用缓冲模式
quicforward.exe h3 4433 example.com:443 0123456789abcdef0123456789abcdef01234567 0

# 设置自定义流控窗口大小
quicforward.exe h3 4433 example.com:443 0123456789abcdef0123456789abcdef01234567 1 1048576
```

forwarder 工具对于以下场景特别有用：
- 测试 QUIC 协议实现的互操作性
- 调试 QUIC 连接问题
- 分析 QUIC 流量模式
- 在不修改客户端或服务器的情况下拦截和检查 QUIC 流量

## 4. interop - QUIC 互操作性测试工具
### 功能介绍
interop 工具是一个 QUIC 互操作性测试客户端，用于测试各种公共 QUIC 端点的主要 QUIC 功能。它可以连接到已知的公共 QUIC 服务器或自定义服务器，并测试多种 QUIC 协议特性，如版本协商、握手、流数据传输、连接关闭、会话恢复、0-RTT、无状态重试、后量子加密、密钥更新、CID 更新、NAT 重绑定、数据报、ChaCha20 加密、QUIC 版本 2 和 QUIC 比特位修饰等。

### 主要文件
- interop.cpp - 主程序实现，包含测试逻辑和命令行参数处理
- interop.h - 头文件，定义了测试特性和常量
### 参数说明
根据源代码中的 PrintUsage 函数和参数处理逻辑，interop 工具支持以下命令行参数：

- -help 或 -? - 显示帮助信息
- -list - 列出所有已知的 QUIC 实现和服务器
- -target:<implementation> - 指定要测试的目标实现，如 msquic、quiche 等
- -custom:<hostname> - 指定自定义服务器主机名
- -port:<####> - 指定自定义端口号
- -test:<test case> - 指定要运行的测试用例，可以是以下字符的组合或数字值：
  - V - 版本协商 (VersionNegotiation)
  - H - 握手 (Handshake)
  - D - 流数据传输 (StreamData)
  - C - 连接关闭 (ConnectionClose)
  - R - 会话恢复 (Resumption)
  - Z - 0-RTT (ZeroRtt)
  - S - 无状态重试 (StatelessRetry)
  - Q - 后量子加密 (PostQuantum)
  - U - 密钥更新 (KeyUpdate)
  - M - CID 更新 (CidUpdate)
  - B - NAT 重绑定 (NatRebinding)
  - G - 数据报 (Datagram)
  - A - ChaCha20 加密 (ChaCha20)
  - 2 - QUIC 版本 2 (Version2)
  - E - QUIC 比特位修饰 (GreaseQuicBit)
- -timeout:<milliseconds> - 指定操作超时时间（毫秒）
- -version:<####> - 指定初始 QUIC 版本
- -serial - 串行运行测试（而非并行）
- -urls:<url1> <url2> ... - 指定要请求的自定义 URL 路径
- -sslkeylogfile:<file> - 指定 SSL 密钥日志文件路径
### 工作原理
interop 工具通过以下方式工作：

1. 连接到指定的 QUIC 服务器（已知公共服务器或自定义服务器）
2. 根据指定的测试用例，执行一系列 QUIC 功能测试
3. 对于每个测试，验证服务器是否正确实现了相应的 QUIC 功能
4. 收集测试结果并生成摘要报告
5. 支持测试多种 QUIC 协议特性，包括基本功能和高级功能
### 使用示例
```bash
# 显示帮助信息
quicinterop.exe -help

# 列出所有已知的 QUIC 实现和服务器
quicinterop.exe -list

# 测试所有 QUIC 功能（默认行为）
quicinterop.exe

# 仅测试握手功能
quicinterop.exe -test:H

# 测试 msquic 实现的所有功能
quicinterop.exe -target:msquic

# 测试自定义服务器的特定功能（流数据传输，十六进制值 0x0004）
quicinterop.exe -custom:localhost -test:16

# 测试多个功能：版本协商、握手和流数据传输
quicinterop.exe -test:VHD

# 使用自定义超时和端口
quicinterop.exe -target:quiche -timeout:20000 -port:4433

# 请求自定义 URL 路径
quicinterop.exe -target:msquic -urls:/index.html /style.css /script.js
```
interop 工具对于以下场景特别有用：

- 验证不同 QUIC 实现之间的互操作性
- 测试 QUIC 服务器对各种协议特性的支持情况
- 调试 QUIC 协议实现问题
- 评估 QUIC 服务器的合规性和功能完整性
- 比较不同 QUIC 实现的性能和特性支持

## 5. interopserver - QUIC 互操作性测试服务器

### 功能介绍
interopserver 是一个简单的 QUIC HTTP 0.9/1.1 服务器，用于测试 QUIC 协议的互操作性。它支持基本的 HTTP GET 和 POST 请求，可以提供静态文件服务，并支持多种 QUIC 协议特性，如会话恢复、0-RTT、强制重试和版本协商扩展等。此外，它还实现了 "siduck" 协议，用于测试 QUIC 数据报功能。

### 主要文件
- `InteropServer.cpp` - 主程序实现，包含服务器逻辑和命令行参数处理
- `InteropServer.h` - 头文件，定义了服务器的类和数据结构

### 参数说明
interopserver 工具支持以下命令行参数：

- `-listen:<addr or *>` - 指定监听地址，可以是具体 IP 地址或 * 表示所有地址（必需参数）
- `-root:<path>` - 指定网站根目录路径（必需参数）
- `-thumbprint:<cert_thumbprint>` - 指定证书指纹（与 -file/-key 二选一）
- `-file:<cert_filepath>` - 指定证书文件路径（与 -thumbprint 二选一，必须与 -key 一起使用）
- `-key:<cert_key_filepath>` - 指定证书私钥文件路径（与 -file 一起使用）
- `-port:<####>` - 指定监听端口，默认为 4433
- `-retry:<0/1>` - 是否启用强制重试功能，默认为 0（禁用）
- `-upload:<path>` - 指定上传文件保存目录，启用 POST 请求支持
- `-enableVNE:<0/1>` - 是否启用版本协商扩展，默认为 0（禁用）
- `-sslkeylogfile:<file>` - 指定 SSL 密钥日志文件路径，用于调试加密流量
- `-noexit` - 服务器启动后不等待按键退出，而是永久运行

### 支持的 ALPN 协议
interopserver 支持以下应用层协议协商 (ALPN) 值：
- `hq-interop` - HTTP/QUIC 互操作性测试协议
- `hq-29` - HTTP/QUIC 草案-29 版本
- `siduck` - 简单数据报测试协议
- `siduck-00` - 简单数据报测试协议版本 00

### 工作原理
interopserver 工具通过以下方式工作：

1. 在指定的地址和端口上创建 QUIC 监听器
2. 根据协商的 ALPN 协议，处理不同类型的连接：
   - 对于 HTTP 相关协议（hq-interop、hq-29），处理 HTTP 请求
   - 对于 siduck 协议，处理数据报请求
3. 对于 HTTP 连接：
   - 支持 GET 请求，从指定的根目录提供文件
   - 如果配置了上传目录，还支持 POST 请求上传文件
   - 支持 HTTP 0.9 和 HTTP 1.1 响应格式
4. 对于 siduck 协议连接：
   - 接收 "quack" 数据报并回复 "quack-ack" 数据报
   - 用于测试 QUIC 数据报功能

### 使用示例
```bash
# 基本用法，监听本地 4433 端口，使用指定证书
quicinteropserver.exe -listen:127.0.0.1 -port:4433 -root:c:\website -thumbprint:175342733b39d81c997817296c9b691172ca6b6e

# 监听所有地址，启用强制重试功能
quicinteropserver.exe -listen:* -port:4433 -root:c:\website -thumbprint:175342733b39d81c997817296c9b691172ca6b6e -retry:1

# 使用证书文件和私钥文件
quicinteropserver.exe -listen:* -port:4433 -root:c:\website -file:cert.pem -key:key.pem

# 启用文件上传功能
quicinteropserver.exe -listen:* -port:4433 -root:c:\website -thumbprint:175342733b39d81c997817296c9b691172ca6b6e -upload:c:\uploads

# 启用版本协商扩展
quicinteropserver.exe -listen:* -port:4433 -root:c:\website -thumbprint:175342733b39d81c997817296c9b691172ca6b6e -enableVNE:1

# 记录 SSL 密钥日志，用于 Wireshark 等工具解密分析 QUIC 流量
quicinteropserver.exe -listen:* -port:4433 -root:c:\website -thumbprint:175342733b39d81c997817296c9b691172ca6b6e -sslkeylogfile:ssl_key.log
```

interopserver 工具对于以下场景特别有用：
- 测试 QUIC 客户端与服务器的互操作性
- 验证 QUIC 协议实现的合规性
- 调试 QUIC 连接问题
- 测试 QUIC 数据报功能
- 评估 QUIC 协议的性能和特性

## 6. ip - QUIC 公共 IP 地址查询工具

### 功能介绍
ip 工具是一个用于查询和显示 QUIC 连接的公共 IP 地址的工具集，包含客户端和服务器两个组件。客户端（quicipclient）可以连接到服务器（quicipserver）查询自己的公共 IP 地址，服务器则负责向客户端返回其公共 IP 地址信息。这对于在 NAT 或代理环境后面的 QUIC 应用程序特别有用，可以帮助确定外部可见的 IP 地址。

### 主要文件
- `quicip.h` - 头文件，定义了公共 IP 查询的数据结构和回调函数
- `client/quicipclient.cpp` - 客户端实现，用于查询公共 IP 地址
- `server/quicipserver.cpp` - 服务器实现，用于响应客户端的 IP 地址查询请求

### 客户端参数说明
quicipclient 工具支持以下命令行参数：

- `-target:<hostname>` - 指定目标服务器主机名，默认为 "quic.westus.cloudapp.azure.com"
- `-local:<ip_address>` - 指定本地 IP 地址，默认为 "*"（所有地址）
- `-unsecure` - 使用不安全模式（不验证服务器证书）
- `-help` 或 `-?` - 显示帮助信息

### 服务器参数说明
quicipserver 工具支持以下命令行参数：

- `-selfsign:1` - 使用自签名证书
- `-cert_hash:<hash>` - 指定证书哈希值
- `-cert_store:<store>` - 指定证书存储位置
- `-machine` - 使用机器证书存储
- `-cert_file:<path>` - 指定证书文件路径（与 -key_file 一起使用）
- `-key_file:<path>` - 指定证书私钥文件路径（与 -cert_file 一起使用）

### 工作原理

#### 客户端 (quicipclient)
1. 连接到指定的 IP 查询服务器
2. 建立 QUIC 连接并使用 "ip" ALPN
3. 接收服务器发送的公共 IP 地址信息
4. 显示本地 IP 地址和公共 IP 地址

#### 服务器 (quicipserver)
1. 在端口 4444 上监听 QUIC 连接
2. 当客户端连接时，获取客户端的远程地址信息
3. 创建单向流并将客户端的公共 IP 地址发送回客户端
4. 支持会话恢复以加快重连速度

### 使用示例

#### 客户端示例
```bash
# 基本用法，连接默认服务器查询公共 IP
quicipclient.exe

# 连接指定服务器查询公共 IP
quicipclient.exe -target:example.com

# 指定本地地址并使用不安全模式
quicipclient.exe -local:************ -unsecure
```
服务器示例
```bash
# 使用自签名证书启动服务器
quicipserver.exe -selfsign:1

# 使用指定证书哈希值启动服务器
quicipserver.exe -cert_hash:0123456789abcdef0123456789abcdef01234567

# 使用证书文件和私钥文件启动服务器
quicipserver.exe -cert_file:cert.pem -key_file:key.pem
```
ip 工具对于以下场景特别有用：
- 在 NAT 或代理环境下获取 QUIC 应用程序的公共 IP 地址
- 测试 QUIC 协议实现的互操作性
- 验证 QUIC 连接的安全性
- 调试 QUIC 连接问题
- 评估 QUIC 协议的性能和特性

## 7. lb - QUIC 负载均衡器工具

### 功能介绍
lb 工具是一个简单的 QUIC 负载均衡器，用于将来自公共地址的 QUIC 流量分发到一组私有地址。它通过 NAT（网络地址转换）技术实现流量转发，为 QUIC 连接提供基本的负载均衡功能。该工具主要用于测试和开发环境，不建议在生产环境中使用。

### 主要文件
- `loadbalancer.cpp` - 主程序实现，包含负载均衡逻辑和命令行参数处理

### 参数说明
根据源代码中的参数处理逻辑，lb 工具支持以下命令行参数：

- `-pub:<address>` - 指定公共监听地址和端口（必需参数）
- `-priv:<address>,<address>,...` - 指定一组私有目标服务器地址和端口，用逗号分隔（必需参数）
- `-v` 或 `-verbose` - 启用详细日志输出模式

### 工作原理
lb 工具通过以下方式工作：

1. 在指定的公共地址上创建 UDP 监听器
2. 接收来自客户端的 QUIC 数据包
3. 使用 Toeplitz 哈希算法根据客户端地址和本地地址的组合选择目标私有服务器
4. 将数据包转发到选定的私有服务器
5. 接收私有服务器的响应并将其发送回原始客户端
6. 维护客户端连接和私有服务器之间的映射关系，确保来自同一客户端的数据包始终转发到同一私有服务器

负载均衡器使用轮询（Round-Robin）方式为新连接分配私有服务器，并使用哈希表存储连接映射关系，以确保会话一致性。

### 使用示例
```bash
# 基本用法，将公共地址 *************:4433 的流量负载均衡到两个私有服务器
quiclb.exe -pub:*************:4433 -priv:10.0.0.1:4433,10.0.0.2:4433

# 使用详细日志模式
quiclb.exe -pub:*************:4433 -priv:10.0.0.1:4433,10.0.0.2:4433 -v

# 负载均衡到多个私有服务器
quiclb.exe -pub:*************:4433 -priv:10.0.0.1:4433,10.0.0.2:4433,10.0.0.3:4433,10.0.0.4:4433
```
lb 工具对于以下场景特别有用：

- 测试 QUIC 协议的负载均衡功能
- 验证 QUIC 连接的安全性
- 调试 QUIC 连接问题
- 评估 QUIC 协议的性能和特性
- 注意：lb 工具仅用于测试和开发环境，不建议在生产环境中使用。
          
## 8. load - QUIC 连接负载测试工具

### 功能介绍
load 工具是一个用于测试 QUIC 服务器性能和稳定性的连接负载生成器。它可以创建大量并发的 QUIC 连接到指定服务器，模拟高负载场景，帮助开发者测试 QUIC 服务器在高并发连接下的表现。该工具会跟踪连接状态并显示实时统计信息，包括已连接数量和活动连接数量。

### 主要文件
- `load.cpp` - 主程序实现，包含连接创建和状态跟踪逻辑

### 参数说明
load 工具支持以下命令行参数：

- `<server_name>` - 目标服务器名称或 IP 地址（必需参数）
- `[conn_count]` - 要创建的连接数量，默认为 100
- `[keep_alive_ms]` - 连接保活时间（毫秒），默认为 60000（60秒）
- `[poll_ms]` - 状态轮询间隔（毫秒），默认为 10000（10秒）
- `[share_udp]` - 是否共享 UDP 绑定，1 表示启用（默认），0 表示禁用

### 工作原理
load 工具通过以下方式工作：

1. 解析命令行参数并解析目标服务器地址
2. 创建指定数量的 QUIC 连接到目标服务器
3. 配置连接参数，包括 ALPN（"h3"和"h3-29"）、保活时间和空闲超时
4. 可选择是否共享 UDP 绑定（多个连接共用同一个本地 UDP 端口）
5. 定期显示连接状态统计信息，包括已连接数量和活动连接数量
6. 等待所有连接完成或关闭

该工具使用 MsQuic API 创建和管理 QUIC 连接，并使用回调函数跟踪连接状态变化。它支持 HTTP/3 协议（通过 ALPN "h3"和"h3-29"），并可以处理服务器发起的单向流。

### 使用示例
```bash
# 基本用法，创建 100 个连接到指定服务器
quicload.exe example.com

# 创建 500 个连接，自定义保活时间为 30 秒
quicload.exe example.com 500 30000

# 创建 1000 个连接，保活时间 60 秒，状态更新间隔 5 秒
quicload.exe example.com 1000 60000 5000

# 创建 200 个连接，禁用 UDP 绑定共享
quicload.exe example.com 200 60000 10000 0
```
load 工具对于以下场景特别有用：
- 测试 QUIC 服务器在高并发连接下的性能和稳定性
- 模拟大量客户端同时连接的场景
- 评估服务器资源使用情况（CPU、内存、网络）
- 测试 QUIC 实现的连接管理能力
- 验证服务器在高负载下的正确行为

 ## 9. pcp - QUIC 端口控制协议工具
### 功能介绍
pcp 工具是一个用于测试和演示 PCP（端口控制协议，Port Control Protocol）功能的实用工具。PCP 是一种允许客户端管理 NAT 或防火墙设备上的端口映射的网络协议，是 NAT-PMP 的后继者。该工具可以发送 PCP MAP 请求，用于创建和删除端口映射，帮助 QUIC 应用程序在 NAT 环境中实现更好的连接性。

### 主要文件
- pcp.cpp - 主程序实现，包含 PCP 请求发送和回调处理逻辑
### 工作原理
pcp 工具通过以下方式工作：

1. 初始化 QUIC 平台和数据路径
2. 生成随机 PCP Nonce 值（用于验证 PCP 响应的真实性）
3. 初始化 PCP 客户端上下文
4. 发送 PCP MAP 请求，尝试在 NAT 设备上创建端口映射
   - 默认使用内部端口 1234
   - 请求映射生存时间为 360000 秒（约 100 小时）
5. 等待并处理 PCP 响应
6. 发送删除请求（生存时间为 0 的 MAP 请求）
7. 清理资源并退出
工具通过回调函数处理三种类型的 PCP 事件：

- CXPLAT_PCP_EVENT_FAILURE - 处理 PCP 请求失败事件
- CXPLAT_PCP_EVENT_MAP - 处理 MAP 请求的响应
- CXPLAT_PCP_EVENT_PEER - 处理 PEER 请求的响应（虽然当前实现中未发送 PEER 请求）
### 使用示例
```bash
# 基本用法，发送 MAP 请求并随后删除映射
quicpcp.exe
```
当工具运行时，它会输出类似以下内容：
```plaintext
Sending MAP request...
Response: 203.0.113.5:1234 maps to :1234 for 3600 seconds
Sending (delete) MAP request...
Response: 203.0.113.5:1234 maps to :1234 for 0 seconds
```
其中：

- 第一行表示正在发送 MAP 请求
- 第二行显示外部地址映射到内部端口的信息及映射的生存时间
- 第三行表示正在发送删除请求
- 第四行显示映射已被删除（生存时间为 0）
pcp 工具对于以下场景特别有用：

- 测试 NAT 设备的 PCP 支持情况
- 调试 QUIC 应用程序在 NAT 环境中的连接问题
- 验证端口映射的创建和删除功能
- 了解 NAT 设备分配的外部地址和端口
- 测试 QUIC 协议与 PCP 的集成       

## 10. post - QUIC HTTP 文件上传工具
### 功能介绍
post 工具是一个简单的 QUIC HTTP 0.9 POST 客户端，用于通过 QUIC 协议将文件上传到支持 HTTP 0.9 的服务器。该工具创建单向 QUIC 流，使用 HTTP 0.9 格式发送 POST 请求，并跟踪上传性能指标，如传输速率和总传输时间。

### 主要文件
- post.cpp - 主程序实现，包含文件上传逻辑和命令行参数处理
### 参数说明
post 工具支持以下命令行参数：

- -file:<path> - 指定要上传的文件路径（必需参数）
- -server:<name> - 指定目标服务器主机名，默认为 "localhost"
- -port:<number> - 指定目标服务器端口，默认为 4433
### 工作原理
post 工具通过以下方式工作：

1. 解析命令行参数并打开指定的文件
2. 建立到目标服务器的 QUIC 连接
3. 创建单向 QUIC 流用于发送数据
4. 构造简单的 HTTP 0.9 POST 请求头（格式为 "POST 文件名\r\n"）
5. 将文件内容分块读取并通过 QUIC 流发送
6. 发送完成后，计算并显示传输统计信息，包括：
   - 总传输字节数
   - 传输耗时（毫秒）
   - 传输速率（kbps 或 mbps）
工具使用 "hq-interop" 和 "hq-29" 作为 ALPN（应用层协议协商）值，以确保与支持 HTTP/QUIC 的服务器兼容。

### 使用示例
```bash
# 基本用法，上传文件到本地服务器
quicpost.exe -file:c:\data\example.txt

# 上传文件到指定服务器和端口
quicpost.exe -server:example.com -port:4433 -file:c:\data\example.txt
```
post 工具对于以下场景特别有用：

- 测试 QUIC 服务器的文件上传功能
- 评估 QUIC 协议的上传性能
- 调试 QUIC 连接的上传问题
- 与支持 HTTP/QUIC 的服务器（如 interopserver）进行互操作性测试

## 11. recvfuzz - QUIC 接收路径模糊测试工具
### 功能介绍
recvfuzz 工具是一个用于测试 QUIC 协议接收路径的数据包模糊测试工具。它通过生成随机的 QUIC 数据包并发送到目标服务器，然后监控服务器的响应，帮助开发者发现 QUIC 协议实现中可能存在的漏洞和稳定性问题。该工具特别关注 QUIC 握手过程中的数据包处理，可以模拟各种异常情况，如畸形数据包、无效加密内容等。

### 主要文件
- recvfuzz.cpp - 主程序实现，包含数据包生成、发送和响应处理逻辑
### 参数说明
recvfuzz 工具支持以下命令行参数：

- timeout:<milliseconds> - 指定测试运行时间（毫秒），默认为 60000（60秒）
- seed:<number> - 指定随机数生成器的种子值，用于重现特定的测试场景。如果不指定，将使用随机生成的种子值
### 工作原理
recvfuzz 工具通过以下方式工作：

1. 创建一个本地 QUIC 服务器用于接收响应
2. 生成随机的 QUIC 初始数据包（Initial Packets）和握手数据包（Handshake Packets）
3. 对数据包内容进行随机修改（模糊测试）
4. 发送这些数据包到目标服务器
5. 监听并处理服务器的响应
6. 跟踪发送的数据包数量和总字节数
7. 支持两种模式：
   - 纯随机模式：生成完全随机的数据包
   - 半有效模式：生成部分有效的数据包，包含正确的加密信息
工具使用 TLS 上下文来处理握手过程，并实现了 QUIC 数据包的加密和解密功能，可以测试服务器对各种 QUIC 帧类型（如 CRYPTO 帧和 ACK 帧）的处理能力。

### 使用示例
```bash
# 基本用法，使用默认参数运行测试
recvfuzz.exe

# 指定测试运行时间为 2 分钟
recvfuzz.exe timeout:120000

# 使用特定的种子值运行测试，以重现特定的测试场景
recvfuzz.exe seed:12345

# 同时指定测试时间和种子值
recvfuzz.exe timeout:300000 seed:54321
```
recvfuzz 工具对于以下场景特别有用：

- 测试 QUIC 服务器实现的鲁棒性和安全性
- 发现 QUIC 协议实现中的潜在漏洞
- 验证 QUIC 服务器对异常数据包的处理能力
- 测试 QUIC 握手过程中的边缘情况
- 评估 QUIC 服务器在面对恶意或损坏数据包时的行为
工具会在测试结束时输出统计信息，包括发送的初始数据包数量、握手数据包数量和总字节数，帮助开发者评估测试的覆盖范围和强度

## 12. sample - QUIC 简单协议示例工具

### 功能介绍
sample 工具是一个简单的 MsQuic API 示例应用程序，实现了一个基本的 QUIC 客户端和服务器。它展示了 MsQuic API 的基本用法，包括连接建立、流数据传输和连接关闭等核心功能。该工具实现了一个简单的协议（ALPN "sample"），其中客户端连接到服务器，打开一个双向流，发送数据并关闭发送方向。服务器接受所有连接、流和数据，在客户端流关闭后，服务器发送自己的数据并关闭其发送方向。连接在 1 秒钟的空闲超时后自动关闭。

### 主要文件
- `sample.c` - 主程序实现，包含客户端和服务器的完整实现

### 参数说明
sample 工具支持以下命令行参数：

#### 客户端模式
- `-client` - 以客户端模式运行
- `-unsecure` - 禁用服务器证书验证
- `-target:{IPAddress|Hostname}` - 指定目标服务器的 IP 地址或主机名
- `-ticket:<ticket>` - 可选参数，指定会话恢复票据

#### 服务器模式
- `-server` - 以服务器模式运行
- `-cert_hash:<hash>` - 指定证书哈希值（从证书存储中加载）
- `-cert_file:<path>` - 指定证书文件路径（与 -key_file 一起使用）
- `-key_file:<path>` - 指定证书私钥文件路径（与 -cert_file 一起使用）
- `-password:<password>` - 可选参数，指定证书私钥的密码

#### 通用参数
- `-help` 或 `-?` - 显示帮助信息

### 工作原理
sample 工具通过以下方式工作：

#### 服务器端
1. 加载服务器配置，包括 TLS 证书和 QUIC 设置
2. 创建 QUIC 监听器并开始监听指定端口（默认 4567）
3. 当接收到新连接时，设置连接回调并提供配置
4. 接受客户端创建的流并处理接收到的数据
5. 当客户端关闭其发送方向时，服务器发送响应数据
6. 连接在空闲超时（默认 1 秒）后自动关闭

#### 客户端端
1. 加载客户端配置，可选择禁用服务器证书验证
2. 创建 QUIC 连接并连接到指定的服务器
3. 连接成功后，创建双向流并发送数据
4. 发送完成后关闭流的发送方向
5. 接收服务器的响应数据
6. 连接在空闲超时后自动关闭

### 使用示例

#### 服务器示例
```bash
# 使用证书哈希值运行服务器
quicsample.exe -server -cert_hash:175342733b39d81c997817296c9b691172ca6b6e

# 使用证书文件和私钥文件运行服务器
quicsample.exe -server -cert_file:server.cert -key_file:server.key

# 使用带密码保护的证书文件运行服务器
quicsample.exe -server -cert_file:server.cert -key_file:server.key -password:SecurePassword
```

#### 客户端示例
```bash
# 连接到本地服务器，验证服务器证书
quicsample.exe -client -target:localhost

# 连接到远程服务器，不验证服务器证书
quicsample.exe -client -unsecure -target:example.com

# 使用会话恢复票据连接到服务器
quicsample.exe -client -target:localhost -ticket:0123456789abcdef0123456789abcdef
```

sample 工具对于以下场景特别有用：
- 学习 QUIC 协议和 MsQuic API 的基本用法
- 测试 QUIC 连接的建立和数据传输
- 验证 TLS 证书配置和会话恢复功能
- 作为开发自定义 QUIC 应用程序的起点
- 调试 QUIC 协议实现问题

注意：使用服务器模式需要有可用的 TLS 证书。在 Windows 上，可以使用 PowerShell 命令生成自签名证书；在 Linux 上，可以使用 OpenSSL 命令生成证书文件。

## 13. spin - QUIC 协议模糊测试工具

### 功能介绍
spin 工具是一个用于对 QUIC 协议实现进行模糊测试和压力测试的工具。它通过随机生成和操作 QUIC 连接、流和数据包，以测试 MsQuic 实现的稳定性、健壮性和安全性。该工具可以同时运行客户端和服务器模式，创建大量随机的 QUIC 连接和流，并在这些连接上执行各种随机操作，如发送数据、关闭流、更新连接参数等。

### 主要文件
- `spinquic.cpp` - 主程序实现，包含模糊测试逻辑和命令行参数处理

### 参数说明
spin 工具支持以下命令行参数：

- 第一个参数必须是以下之一：
  - `server` - 仅运行服务器模式
  - `client` - 仅运行客户端模式
  - `both` - 同时运行服务器和客户端模式

- `timeout:<ms>` - 指定测试运行时间（毫秒），默认为 60000（60秒）
- `max_ops:<count>` - 指定最大操作次数，默认为无限制
- `loss:<percent>` - 指定数据包丢失百分比，默认为 5%
- `repeat_count:<count>` - 指定测试重复次数，默认为 1
- `alloc_fail:<denominator>` - 设置内存分配失败概率分母，默认为 0（不模拟分配失败）
- `seed:<number>` - 指定随机数生成器的种子值，默认为随机生成

#### 客户端模式特有参数
- `dstport:<port>` - 指定目标服务器端口，默认为 9998 和 9999
- `target:<hostname>` - 指定目标服务器主机名，默认为 "127.0.0.1"
- `alpn:<protocol>` - 指定应用层协议协商值，默认为 "spin"
- `sessions:<count>` - 指定会话数量，默认为 4

### 工作原理
spin 工具通过以下方式工作：

1. 初始化 QUIC 平台和 MsQuic API
2. 根据命令行参数配置测试环境，包括运行模式、超时时间、数据包丢失率等
3. 创建指定数量的工作线程执行测试
4. 在每个线程中：
   - 创建 QUIC 注册和配置
   - 如果是服务器模式，创建监听器接受连接
   - 如果是客户端模式，创建多个连接到目标服务器
   - 在连接上随机执行各种 API 调用，如创建流、发送数据、设置参数等
   - 处理连接和流事件，并随机响应这些事件
5. 收集测试结果和性能计数器

该工具支持两种测试模式：
- 正常模式：使用随机生成的参数进行测试
- 模糊测试模式：使用预定义的输入数据进行确定性测试，适用于与模糊测试引擎（如 LibFuzzer）集成

### 使用示例
```bash
# 同时运行服务器和客户端模式，测试 30 秒
spinquic.exe both timeout:30000

# 仅运行客户端模式，连接到指定服务器，使用自定义 ALPN
spinquic.exe client target:quic-server.example.com dstport:4433 alpn:h3

# 仅运行服务器模式，设置 10% 的数据包丢失率
spinquic.exe server loss:10

# 使用指定的随机种子运行测试，重复 5 次
spinquic.exe both seed:12345 repeat_count:5

# 运行高强度测试，模拟内存分配失败
spinquic.exe both timeout:300000 alloc_fail:100
```

spin 工具对于以下场景特别有用：
- 测试 QUIC 协议实现的稳定性和健壮性
- 发现 QUIC 实现中的潜在漏洞和错误
- 验证 QUIC 实现对异常情况的处理能力
- 评估 QUIC 协议在高压力和随机操作下的性能
- 与模糊测试引擎集成，进行自动化安全测试
        

       