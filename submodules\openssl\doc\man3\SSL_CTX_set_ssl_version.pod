=pod

=head1 NAME

SSL_CTX_set_ssl_version, SSL_set_ssl_method, SSL_get_ssl_method
- choose a new TLS/SSL method

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_set_ssl_version(SSL_CTX *ctx, const SSL_METHOD *method);
 int SSL_set_ssl_method(SSL *s, const SSL_METHOD *method);
 const SSL_METHOD *SSL_get_ssl_method(const SSL *ssl);

=head1 DESCRIPTION

SSL_CTX_set_ssl_version() sets a new default TLS/SSL B<method> for SSL objects
newly created from this B<ctx>. SSL objects already created with
L<SSL_new(3)> are not affected, except when
L<SSL_clear(3)> is being called.

SSL_set_ssl_method() sets a new TLS/SSL B<method> for a particular B<ssl>
object. It may be reset, when SSL_clear() is called.

SSL_get_ssl_method() returns a function pointer to the TLS/SSL method
set in B<ssl>.

=head1 NOTES

The available B<method> choices are described in
L<SSL_CTX_new(3)>.

When L<SSL_clear(3)> is called and no session is connected to
an SSL object, the method of the SSL object is reset to the method currently
set in the corresponding SSL_CTX object.

=head1 RETURN VALUES

The following return values can occur for SSL_CTX_set_ssl_version()
and SSL_set_ssl_method():

=over 4

=item Z<>0

The new choice failed, check the error stack to find out the reason.

=item Z<>1

The operation succeeded.

=back

=head1 SEE ALSO

L<SSL_CTX_new(3)>, L<SSL_new(3)>,
L<SSL_clear(3)>, L<ssl(7)>,
L<SSL_set_connect_state(3)>

=head1 COPYRIGHT

Copyright 2000-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
