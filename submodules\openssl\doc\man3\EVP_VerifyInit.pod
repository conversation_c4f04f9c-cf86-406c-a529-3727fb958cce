=pod

=head1 NAME

EVP_VerifyInit_ex,
EVP_VerifyInit, EVP_VerifyUpdate, EVP_VerifyFinal
- EVP signature verification functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_VerifyInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
 int EVP_VerifyUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
 int EVP_VerifyFinal(EVP_MD_CTX *ctx, unsigned char *sigbuf, unsigned int siglen,
                     EVP_PKEY *pkey);

 int EVP_VerifyInit(EVP_MD_CTX *ctx, const EVP_MD *type);

=head1 DESCRIPTION

The EVP signature verification routines are a high-level interface to digital
signatures.

EVP_VerifyInit_ex() sets up verification context B<ctx> to use digest
B<type> from ENGINE B<impl>. B<ctx> must be created by calling
EVP_MD_CTX_new() before calling this function.

EVP_VerifyUpdate() hashes B<cnt> bytes of data at B<d> into the
verification context B<ctx>. This function can be called several times on the
same B<ctx> to include additional data.

EVP_VerifyFinal() verifies the data in B<ctx> using the public key B<pkey>
and against the B<siglen> bytes at B<sigbuf>.

EVP_VerifyInit() initializes verification context B<ctx> to use the default
implementation of digest B<type>.

=head1 RETURN VALUES

EVP_VerifyInit_ex() and EVP_VerifyUpdate() return 1 for success and 0 for
failure.

EVP_VerifyFinal() returns 1 for a correct signature, 0 for failure and -1 if some
other error occurred.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 NOTES

The B<EVP> interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.

The call to EVP_VerifyFinal() internally finalizes a copy of the digest context.
This means that calls to EVP_VerifyUpdate() and EVP_VerifyFinal() can be called
later to digest and verify additional data.

Since only a copy of the digest context is ever finalized the context must
be cleaned up after use by calling EVP_MD_CTX_free() or a memory leak
will occur.

=head1 BUGS

Older versions of this documentation wrongly stated that calls to
EVP_VerifyUpdate() could not be made after calling EVP_VerifyFinal().

Since the public key is passed in the call to EVP_SignFinal() any error
relating to the private key (for example an unsuitable key and digest
combination) will not be indicated until after potentially large amounts of
data have been passed through EVP_SignUpdate().

It is not possible to change the signing parameters using these function.

The previous two bugs are fixed in the newer EVP_DigestVerify*() function.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_SignInit(3)>,
L<EVP_DigestInit(3)>,
L<evp(7)>, L<HMAC(3)>, L<MD2(3)>,
L<MD5(3)>, L<MDC2(3)>, L<RIPEMD160(3)>,
L<SHA1(3)>, L<dgst(1)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
