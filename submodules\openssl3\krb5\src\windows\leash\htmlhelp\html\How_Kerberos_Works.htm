<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<HTML>
<HEAD>
<meta name="GENERATOR" content="Microsoft&reg; HTML Help Workshop 4.1">
<link rel="stylesheet" type="text/css" href="Leash.css" />
<Title>How Kerberos Works</Title>
</HEAD>
<BODY>
<H1> How Does Kerberos Work?</H1>
<p>
The Kerberos protocol uses secret-key cryptography to allow the user and the service the user is accessing to prove their identities to each other and then to encrypt the rest of their communications. This mutual authentication and subsequent encryption maintain privacy and data integrity for both user and service. </p>
<p>
A basic understanding of Kerberos can be gained by reading the <a href="HTML/Kerberos_Terminology.htm">Kerberos terminology</a> page. You do not need to know the inner workings of the encryption and authentication to use Kerberos. However, if you are curious to know more, the MIT Kerberos Consortium has an excellent website with links to several varieties of documentation, including a tutorial of the Kerberos protocol. <a href="https://www.kerberos.org/docs/index.html" target="new">MIT Kerberos Consortium documentation page</a>

<H2>Related Help</H2>
<ul id="helpul">
<li><a href="HTML/Kerberos.htm">What is Kerberos?</a></li>
<li><a href="HTML/Kerberos_Terminology.htm">Kerberos terminology</a></li>
<li><a href="HTML/Encryption_Types.htm">Encryption types</a></li>
</ul>


</BODY>
</HTML>
