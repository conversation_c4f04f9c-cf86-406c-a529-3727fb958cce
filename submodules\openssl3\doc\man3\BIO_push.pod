=pod

=head1 NAME

BIO_push, BIO_pop, BIO_set_next - add and remove BIOs from a chain

=head1 SYNOPSIS

 #include <openssl/bio.h>

 BIO *BIO_push(BIO *b, BIO *next);
 BIO *BIO_pop(BIO *b);
 void BIO_set_next(BIO *b, <PERSON><PERSON> *next);

=head1 DESCRIPTION

BIO_push() pushes I<b> on I<next>.
If I<b> is NULL the function does nothing and returns I<next>.
Otherwise it prepends I<b>, which may be a single BIO or a chain of BIOs,
to I<next> (unless I<next> is NULL).
It then makes a control call on I<b> and returns I<b>.

BIO_pop() removes the BIO I<b> from any chain is is part of.
If I<b> is NULL the function does nothing and returns NULL.
Otherwise it makes a control call on I<b> and
returns the next BIO in the chain, or NULL if there is no next BIO.
The removed BIO becomes a single BIO with no association with
the original chain, it can thus be freed or be made part of a different chain.

BIO_set_next() replaces the existing next BIO in a chain with the BIO pointed to
by I<next>. The new chain may include some of the same BIOs from the old chain
or it may be completely different.

=head1 NOTES

The names of these functions are perhaps a little misleading. BIO_push()
joins two BIO chains whereas BIO_pop() deletes a single BIO from a chain,
the deleted BIO does not need to be at the end of a chain.

The process of calling BIO_push() and BIO_pop() on a BIO may have additional
consequences (a control call is made to the affected BIOs).
Any effects will be noted in the descriptions of individual BIOs.

=head1 RETURN VALUES

BIO_push() returns the head of the chain,
which usually is I<b>, or I<next> if I<b> is NULL.

BIO_pop() returns the next BIO in the chain,
or NULL if there is no next BIO.

=head1 EXAMPLES

For these examples suppose I<md1> and I<md2> are digest BIOs,
I<b64> is a base64 BIO and I<f> is a file BIO.

If the call:

 BIO_push(b64, f);

is made then the new chain will be I<b64-f>. After making the calls

 BIO_push(md2, b64);
 BIO_push(md1, md2);

the new chain is I<md1-md2-b64-f>. Data written to I<md1> will be digested
by I<md1> and I<md2>, base64 encoded, and finally written to I<f>.

It should be noted that reading causes data to pass in the reverse
direction, that is data is read from I<f>, base64 decoded,
and digested by I<md2> and then I<md1>.

The call:

 BIO_pop(md2);

will return I<b64> and the new chain will be I<md1-b64-f>.
Data can be written to and read from I<md1> as before,
except that I<md2> will no more be applied.

=head1 SEE ALSO

L<bio(7)>

=head1 HISTORY

The BIO_set_next() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
