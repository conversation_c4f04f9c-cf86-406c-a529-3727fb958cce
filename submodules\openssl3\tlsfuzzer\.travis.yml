# workaround for 3.7 and 3.8 not available in default configuration
# travis-ci/travis-ci#9815
dist: trusty
sudo: false
cache: pip
language: python

addons:
  apt_packages:
    # needed for M2Crypto
    - swig
    - libssl-dev
    # needed for gmpy2
    - libgmp-dev
    - libmpfr-dev
    - libmpc-dev
    # needed for scipy on 3.9
    - gcc
    - gfortran
    - libopenblas-dev
    - liblapack-dev
    - cython
    # needed for test coverage
    - tcpdump

before_cache:
  - rm -f $HOME/.cache/pip/log/debug.log

jobs:
 include:
# ANALYSIS_DEP=EXTRACT_DEP=false
   - python: 2.6
   - python: 2.7
   - python: 3.3
   - python: 3.4
   - python: 3.5
   - python: 3.6
# workaround for 3.7 and 3.8 not available in default configuration
# travis-ci/travis-ci#9815
   - python: 3.7
     dist: xenial
     sudo: true
   - python: 3.8
     dist: xenial
     sudo: true
   - python: 3.9
     dist: xenial
     sudo: true
   - python: nightly
     dist: xenial
     sudo: true
# ANALYSIS_DEP=false EXTRACT_DEP=true
   - python: 2.6
     env: EXTRACT_DEP=true
   - python: 2.7
     env: EXTRACT_DEP=true
   - python: 3.3
     env: EXTRACT_DEP=true
   - python: 3.4
     env: EXTRACT_DEP=true
   - python: 3.5
     env: EXTRACT_DEP=true
   - python: 3.6
     env: EXTRACT_DEP=true
   - python: 3.7
     dist: xenial
     sudo: true
     env: EXTRACT_DEP=true
   - python: 3.8
     dist: xenial
     sudo: true
     env: EXTRACT_DEP=true
   - python: 3.9
     dist: xenial
     sudo: true
     env: EXTRACT_DEP=true
   - python: nightly
     dist: xenial
     sudo: true
     env: EXTRACT_DEP=true
# ANALYSIS_DEP=true EXTRACT_DEP=false
   - python: 3.6
     env: ANALYSIS_DEP=true
   - python: 3.7
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true
   - python: 3.8
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true
   - python: 3.9
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true
   - python: nightly
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true
# ANALYSIS_DEP=EXTRACT_DEP=true
   - python: 3.6
     env: ANALYSIS_DEP=true EXTRACT_DEP=true
   - python: 3.7
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true EXTRACT_DEP=true
   - python: 3.8
     dist: xenial
     sudo: true
     # Note: CC_REPORT=true can be set in only one environment
     env: ANALYSIS_DEP=true EXTRACT_DEP=true CC_REPORT=true
   - python: 3.9
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true EXTRACT_DEP=true
   - python: nightly
     dist: xenial
     sudo: true
     env: ANALYSIS_DEP=true EXTRACT_DEP=true
 allow_failures:
   # lazy-object-proxy fails to build
   - python: nightly
   # scipy fails to build
   - python: 3.9
     env: ANALYSIS_DEP=true
   - python: 3.9
     env: ANALYSIS_DEP=true EXTRACT_DEP=true

branches:
  only:
    - master

before_install:
  - |
       echo -e "TRAVIS_PULL_REQUEST=$TRAVIS_PULL_REQUEST\n" \
               "TRAVIS_REPO_SLUG=$TRAVIS_REPO_SLUG\n" \
               "TRAVIS_PULL_REQUEST=$TRAVIS_PULL_REQUEST\n" \
               "TRAVIS_COMMIT=$TRAVIS_COMMIT\n" \
               "TRAVIS_PYTHON_VERSION=$TRAVIS_PYTHON_VERSION"
  - |
       # workaround https://github.com/travis-ci/travis-ci/issues/2666
       if [ "$TRAVIS_PULL_REQUEST" != "false" ]; then
         URL="https://github.com/${TRAVIS_REPO_SLUG}/pull/${TRAVIS_PULL_REQUEST}.patch"
         # `--location` makes curl follow redirects
         PR_FIRST=$(curl --silent --show-error --location $URL | head -1 | grep -o -E '\b[0-9a-f]{40}\b' | tr -d '\n')
         TRAVIS_COMMIT_RANGE=$PR_FIRST^..$TRAVIS_COMMIT
       fi
  # sanity check current commit
  - git rev-parse HEAD
  - echo "TRAVIS_COMMIT_RANGE=$TRAVIS_COMMIT_RANGE"
  - git fetch origin master:refs/remotes/origin/master
  - curl -L https://codeclimate.com/downloads/test-reporter/test-reporter-latest-linux-amd64 > ./cc-test-reporter
  - chmod +x ./cc-test-reporter

install:
  - pip list
  - if [[ $TRAVIS_PYTHON_VERSION == '2.6' ]]; then
        curl -o get-pip.py https://bootstrap.pypa.io/2.6/get-pip.py;
        python get-pip.py;
    fi
  - if [[ -e build-requirements-${TRAVIS_PYTHON_VERSION}.txt ]]; then travis_retry pip install -r build-requirements-${TRAVIS_PYTHON_VERSION}.txt; else travis_retry pip install -r build-requirements.txt; fi
  - |
      if [[ $EXTRACT_DEP == 'true' ]]; then
          if [[ $TRAVIS_PYTHON_VERSION == '2.6' ]]; then
              travis_retry pip install "dpkt==1.9.2";
          else
              travis_retry pip install "dpkt>=1.9.2";
          fi
      fi
  # kiwisolver requires new pip for its wheel files so ensure we're running the newest one
  - if [[ $ANALYSIS_DEP == 'true' ]]; then travis_retry pip install --upgrade pip; travis_retry pip install -r build-requirements-analysis.txt; fi
  - travis_retry pip install -r requirements.txt
  # install optional tlslite-ng requirementes to speed up testing
  - |
    if [[ $TRAVIS_PYTHON_VERSION == 3.7 ]] || [[ $TRAVIS_PYTHON_VERSION == 3.8 ]] || [[ $TRAVIS_PYTHON_VERSION == 3.9 ]] ; then
      travis_retry pip install m2crypto
    elif [[ $TRAVIS_PYTHON_VERSION != '3.3' ]]; then
      # M2Crypto 0.37.0 broke compatibility with old OpenSSL
      travis_retry pip install 'm2crypto<0.37'
    fi
  - travis_retry pip install gmpy2
  # codeclimate supports natively just one set of results, so use the most recent python for that
  - if [[ $CC_REPORT == 'true' ]]; then ./cc-test-reporter before-build; fi
  - pip list

script:
  - |
      if [[ $TRAVIS_PYTHON_VERSION == '2.6' ]]; then
          coverage run --branch --source tlsfuzzer -m unittest2 discover;
      else
          coverage run --branch --source tlsfuzzer -m unittest discover;
      fi
  - coverage report -m
  # check if all scripts are included in the json files
  - python tests/verify-scripts-json.py tests/tlslite-ng-random-subset.json tests/tlslite-ng.json
  # the HTTP header includes python version, since "2.7.15" is longer than "2.6.9", the reply itself is longer too
  # more generally, it should be `1845 + len(sys.version.split()[0])`,
  # as http.server uses `sys_version = "Python/" + sys.version.split()[0]`
  # https://github.com/python/cpython/blob/2c050e52f1ccf5db03819e4ed70690521d67e9fa/Lib/http/server.py#L253
  - |
      if [[ $TRAVIS_PYTHON_VERSION == '2.7' ]]; then
        export REPLY_SIZE=1850
      elif [[ $TRAVIS_PYTHON_VERSION == 'nightly' ]]; then
        export REPLY_SIZE=1852  # Server: SimpleHTTP/0.6 Python/3.10.0a0
      else
        export REPLY_SIZE=1849  # Server: SimpleHTTP/0.6 Python/3.7.6
      fi
  - travis_wait 30 python tests/scripts_retention.py tests/tlslite-ng-random-subset.json `which tls.py` $REPLY_SIZE
  # pylint doesn't work on 2.6: https://bitbucket.org/logilab/pylint/issue/390/py26-compatiblity-broken
  - |
      if [[ $TRAVIS_PYTHON_VERSION != '2.6' ]]; then
        pylint --msg-template="{path}:{line}: [{msg_id}({symbol}), {obj}] {msg}" tlsfuzzer > pylint_report.txt || :
        diff-quality --violations=pylint --fail-under=90 pylint_report.txt
      fi
  - |
      if [[ $TRAVIS_PULL_REQUEST != "false" ]]; then
        echo "Will test commits between $TRAVIS_COMMIT_RANGE:"
        git log --oneline --reverse $TRAVIS_COMMIT_RANGE

        for i in $(git log --pretty=format:%H --reverse $TRAVIS_COMMIT_RANGE); do
          git checkout $i
          make clean
          if [[ $TRAVIS_PYTHON_VERSION == '2.6' ]]; then
            unit2 discover || exit 1
          else
            python -m unittest discover || exit 1
          fi
          cd $TRAVIS_BUILD_DIR
        done
      fi

after_success:
 - travis_retry coveralls
 - if [[ $CC_REPORT == 'true' ]]; then coverage xml; ./cc-test-reporter after-build --exit-code $TRAVIS_TEST_RESULT; fi
