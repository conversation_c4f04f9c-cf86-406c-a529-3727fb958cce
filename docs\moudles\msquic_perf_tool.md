# MsQuic Perf 工具详细介绍

## 功能介绍

SecNetPerf 是 MsQuic 项目中用于性能测试的标准跨平台工具。它实现了 [QUIC 性能测试协议](https://tools.ietf.org/html/draft-banks-quic-performance)，提供了一个通用接口，允许进行客户端驱动的性能测试。该工具可以测试 QUIC 协议的吞吐量、延迟、连接建立速率等性能指标，同时也支持 TCP 协议的性能测试以进行比较。

### 主要文件

- `SecNetPerf.h` - 主要头文件，定义了性能测试的核心数据结构和函数
- `SecNetPerfMain.cpp` - 主程序入口，处理命令行参数和初始化
- `PerfClient.h/cpp` - 客户端实现，负责创建连接、发送数据和收集统计信息
- `PerfServer.h/cpp` - 服务器实现，负责接收连接和处理请求
- `Tcp.h/cpp` - TCP 协议支持，用于与 QUIC 进行性能比较

## 服务器模式

服务器模式通常以最少的参数运行，主要用于响应客户端的性能测试请求。

### 服务器参数说明

| 参数 | 用法 | 含义 |
| --- | --- | --- |
| bind | `-bind:<address>` | 绑定到指定的本地地址 |
| port | `-port:<####>` | 服务器的 UDP 端口，默认为 4433 |
| serverid | `-serverid:<####>` | 服务器 ID（用于负载均衡） |
| cibir | `-cibir:<hex_bytes>` | CIBIR 标识符（十六进制字节） |
| cc | `-cc:<cubic,bbr>` | 使用的拥塞控制算法 |
| cipher | `-cipher:<value>` | `QUIC_ALLOWED_CIPHER_SUITE_FLAGS` 的十进制值 |
| cpu | `-cpu:<cpu_indexes>` | 要运行的 CPU 索引列表（逗号分隔） |
| ecn | `-ecn:<0,1>` | 启用/禁用发送方 ECN 支持 |
| exec | `-exec:<lowlat,maxtput,scavenger,realtime>` | 应用程序使用的执行配置文件 |
| pollidle | `-pollidle:<time_us>` | 空闲时轮询的时间（微秒），超时后进入睡眠状态 |
| stats | `-stats:<0,1>` | 在每个连接结束时打印统计信息 |

### 服务器使用示例

```bash
# 基本用法
> secnetperf

# 高吞吐量测试配置
> secnetperf -exec:maxtput

# 指定绑定地址和端口
> secnetperf -bind:************ -port:5000

# 启用统计信息打印
> secnetperf -stats:1
```

## 客户端模式

由于测试是客户端驱动的，secnetperf 的客户端模式通常需要指定多个参数来配置测试场景。

### 客户端参数说明

#### 远程选项

| 别名 | 用法 | 含义 |
| --- | --- | --- |
| target, server, to, remote, peer | `-target:<hostname>` | 目标服务器主机名（必需参数） |
| ip, af | `-ip:<0,4,6>` | 解析主机名为 IP 地址的地址族提示 |
| port | `-port:<value>` | 远程对等方的 UDP 端口 |
| cibir | `-cibir:<hex_bytes>` | CIBIR 标识符（十六进制字节） |
| inctarget | `-inctarget:<0,1>` | 是否为每个工作线程向目标主机名附加唯一 ID |

#### 本地选项

| 别名 | 用法 | 含义 |
| --- | --- | --- |
| threads | `-threads:<value>` | 要使用的最大工作线程数 |
| affinitize | `-affinitize:<0,1>` | 将工作线程绑定到核心 |
| comp | `-comp:<value>` | 要运行的网络隔离 ID（仅限 Windows） |
| bind | `-bind:<addr(s)>` | 要绑定的本地 IP 地址/端口 |
| share | `-share:<0,1>` | 是否共享相同的本地绑定 |

#### 通用配置选项

| 别名 | 用法 | 含义 |
| --- | --- | --- |
| tcp | `-tcp:<0,1>` | 禁用/启用 TCP 使用（而不是 QUIC） |
| encrypt | `-encrypt:<0,1>` | 禁用/启用加密 |
| pacing | `-pacing:<0,1>` | 禁用/启用发送调度 |
| sendbuf | `-sendbuf:<0,1>` | 禁用/启用发送缓冲 |
| ptput | `-ptput:<0,1>` | 打印吞吐量信息 |
| pconnection, pconn | `-pconn:<0,1>` | 打印连接统计信息 |
| pstream | `-pstream:<0,1>` | 打印流统计信息 |
| platency, plat | `-platency:<0,1>` | 打印延迟统计信息 |
| praw | `-praw:<0,1>` | 打印原始信息 |

#### 场景选项

| 别名 | 用法 | 含义 |
| --- | --- | --- |
| conns | `-conns:<value>` | 要使用的连接数 |
| streams, requests | `-streams:<value>` | 同时发送的流数量 |
| upload, up, request | `-upload:<value>[units]` | 在每个流上发送的字节长度（或可选的时间/长度单位） |
| download, down, response | `-download:<value>[units]` | 在每个流上接收的字节长度（或可选的时间/长度单位） |
| iosize | `-iosize:<value>` | 每个发送请求的大小 |
| rconn, rc | `-rconn:<0,1>` | 在连接级别重复场景 |
| rstream, rs | `-rstream:<0,1>` | 在流级别重复场景 |
| runtime, time, run | `-runtime:<value>[units]` | 总运行时间，带可选单位（默认单位为微秒） |

### 客户端使用示例

```bash
# 简单的下载测试（下载 1GB 数据）
> secnetperf -target:perf-server -down:1gb

# 重复的请求/响应交换测试
> secnetperf -target:perf-server -rstream:1 -run:10s -up:500 -down:4000

# 多连接并行下载测试
> secnetperf -target:perf-server -conns:10 -down:100mb

# 禁用加密的高吞吐量测试
> secnetperf -target:perf-server -encrypt:0 -down:10gb -exec:maxtput

# 使用 TCP 而不是 QUIC 进行比较测试
> secnetperf -target:perf-server -tcp:1 -down:1gb
```

## 连接统计信息（pconn）功能

通过设置 `-pconn:1` 参数，SecNetPerf 工具会在测试完成后输出详细的连接级别统计信息。这些统计信息对于分析 QUIC 协议的性能和行为非常有用。

### 连接统计信息包括：

- **RTT** - 往返时间（微秒）
- **MinRTT** - 最小往返时间（微秒）
- **EcnCapable** - ECN（显式拥塞通知）能力状态
- **SendTotalPackets** - 发送的总数据包数
- **SendSuspectedLostPackets** - 疑似丢失的发送数据包数
- **SendSpuriousLostPackets** - 误报为丢失的发送数据包数
- **SendCongestionCount** - 拥塞事件计数
- **SendEcnCongestionCount** - ECN 拥塞事件计数
- **RecvTotalPackets** - 接收的总数据包数
- **RecvReorderedPackets** - 重新排序的接收数据包数
- **RecvDroppedPackets** - 丢弃的接收数据包数
- **RecvDuplicatePackets** - 重复的接收数据包数
- **RecvDecryptionFailures** - 解密失败的接收数据包数

### 连接统计信息输出示例：

```
Connection Statistics:
  RTT                       15000 us
  MinRTT                    12000 us
  EcnCapable                0
  SendTotalPackets          12345
  SendSuspectedLostPackets  123
  SendSpuriousLostPackets   12
  SendCongestionCount       5
  SendEcnCongestionCount    0
  RecvTotalPackets          54321
  RecvReorderedPackets      234
  RecvDroppedPackets        45
  RecvDuplicatePackets      67
  RecvDecryptionFailures    0
```

## 流统计信息（pstream）功能

通过设置 `-pstream:1` 参数，SecNetPerf 工具会在测试完成后输出详细的流级别统计信息。这些统计信息对于分析 QUIC 流的性能和行为非常有用。

### 流统计信息包括：

- **StreamID** - 流的唯一标识符
- **BytesSent** - 发送的总字节数
- **BytesReceived** - 接收的总字节数
- **SendStartTime** - 发送开始时间
- **SendEndTime** - 发送结束时间
- **SendDuration** - 发送持续时间（微秒）
- **RecvStartTime** - 接收开始时间
- **RecvEndTime** - 接收结束时间
- **RecvDuration** - 接收持续时间（微秒）
- **SendThroughput** - 发送吞吐量（Mbps）
- **RecvThroughput** - 接收吞吐量（Mbps）

### 流统计信息输出示例：

```
Stream Statistics:
  StreamID                  1
  BytesSent                 104857600
  BytesReceived             0
  SendStartTime             1234567890
  SendEndTime               1234569890
  SendDuration              2000000 us
  RecvStartTime             0
  RecvEndTime               0
  RecvDuration              0 us
  SendThroughput            419.43 Mbps
  RecvThroughput            0.00 Mbps
```

## 延迟统计信息（platency）功能

通过设置 `-platency:1` 或 `-plat:1` 参数，SecNetPerf 工具会收集并输出详细的延迟统计信息。这对于分析 QUIC 协议的延迟性能特别有用。

### 延迟统计信息包括：

- **Min** - 最小延迟（微秒）
- **Max** - 最大延迟（微秒）
- **Average** - 平均延迟（微秒）
- **90th Percentile** - 90% 百分位延迟（微秒）
- **99th Percentile** - 99% 百分位延迟（微秒）
- **99.9th Percentile** - 99.9% 百分位延迟（微秒）
- **99.99th Percentile** - 99.99% 百分位延迟（微秒）
- **99.999th Percentile** - 99.999% 百分位延迟（微秒）
- **99.9999th Percentile** - 99.9999% 百分位延迟（微秒）

### 延迟统计信息输出示例：

```
Latency Statistics (us):
  Min                       1200
  Max                       25000
  Average                   3500
  90th Percentile           5000
  99th Percentile           10000
  99.9th Percentile         15000
  99.99th Percentile        20000
  99.999th Percentile       22000
  99.9999th Percentile      24000
```

## 吞吐量信息（ptput）功能

通过设置 `-ptput:1` 参数，SecNetPerf 工具会在测试过程中和完成后输出吞吐量信息。这对于评估 QUIC 连接的数据传输效率非常有用。

### 吞吐量信息包括：

- **Total Bytes** - 传输的总字节数
- **Duration** - 传输持续时间（秒）
- **Throughput** - 吞吐量（Mbps 或 Gbps）

### 吞吐量信息输出示例：

```
Throughput:
  Total Bytes               1073741824
  Duration                  5.234 s
  Throughput                1.64 Gbps
```

## 总结

SecNetPerf 是一个功能强大的性能测试工具，可以全面评估 QUIC 协议的各种性能指标。通过合理配置客户端和服务器参数，可以模拟各种网络条件和应用场景，获取详细的性能数据。特别是通过 `-pconn`、`-pstream` 和 `-platency` 参数，可以获取连接、流和延迟的详细统计信息，帮助开发者深入分析 QUIC 协议的性能特性和优化方向。
        