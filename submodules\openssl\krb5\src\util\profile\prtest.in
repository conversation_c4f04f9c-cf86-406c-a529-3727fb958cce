#!/bin/sh
SRCDIR=@srcdir@
SCRIPT=$SRCDIR/prtest.script
REPORT=prtest.report
TESTPROG=./test_profile

rm -f $REPORT

if test -f $SCRIPT ; then 
	: 
else
	echo "$SCRIPT not found!"
	exit 1
fi

grep -v ^\# < $SCRIPT | while read filespec cmd args
do
	case $filespec in
	krb5)
		file=$SRCDIR/krb5.conf
		;;
	test)
		file=$SRCDIR/test.ini
		;;
	*)
		echo "Unknown file specifer $file!"
		exit 1
		;;
	esac
	if test $cmd = parse ; then
	    echo \# test_parse $filespec   >> $REPORT 2>&1
	    $TESTPARSE $file >> $REPORT    >> $REPORT 2>&1
	fi
	echo \# $filespec $cmd $args  >> $REPORT 2>&1
	$TESTPROG $file $cmd $args    >> $REPORT 2>&1 
done 
