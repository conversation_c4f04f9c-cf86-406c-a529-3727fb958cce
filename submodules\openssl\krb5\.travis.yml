language: c++

sudo: required

matrix:
  include:
  - compiler: clang
    env: MAKEVARS=CPPFLAGS=-Werror
  - compiler: gcc

before_install:
  - sudo apt-get update -qq
  - sudo apt-get install -y bison dejagnu gettext keyutils ldap-utils libldap2-dev libkeyutils-dev libssl-dev python3-paste slapd tcl-dev tcsh
  - mkdir -p cmocka/build
  - cd cmocka
  - wget https://cmocka.org/files/1.1/cmocka-1.1.1.tar.xz
  - tar -xvf cmocka-1.1.1.tar.xz
  - cd build
  - cmake ../cmocka-1.1.1 -DCMAKE_INSTALL_PREFIX=/usr
  - make
  - sudo make install
  - cd ../..

script: sh -ex .travis-ci.sh
