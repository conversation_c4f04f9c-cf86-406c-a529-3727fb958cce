#!/usr/bin/perl
#
# This program converts the old-style krb.conf and krb.realms files into the
# new-format krb5.conf file.  It takes two arguments; the first is the krb.conf
# file, and the second is the krb.realms file.  The krb5.conf file is output
# to stdout.
#
# Written by <PERSON>'o, 4/25/95
#

if ($#ARGV >= 0) {
	$krb_conf_file = $ARGV[0];
} else {
	$krb_conf_file = "/etc/krb.conf";
}

if ($#ARGV >= 1) {
	$krb_realms_file = $ARGV[1];
} else {
	$krb_realms_file = "/etc/krb.realms";
}

open(FILE, "<$krb_conf_file") || die "Couldn't open the krb.conf file\n";

$_ = <FILE>;
strip;
$default_realm = $_;

while(<FILE>) {
	strip;
	($realm, $host, $admin) = split;
	if (!defined($realmpt{$realm})) {
		$realmpt{$realm} = 1;
	}
	$realmkdc{$realm . "##" . $realmpt{$realm}} = $host;
	$realmpt{$realm}++;
	if ($admin eq "admin") {	
		$realmadmin{$realm} = $host;
	}
}

close(FILE);

open(FILE, "<$krb_realms_file") || die "Couldn't open krb.realms file";

while (<FILE>) {
	strip;
	($domain, $realm) = split;
	$domain =~ s/\.$//;
	$domain =~ tr/[A-Z]/[a-z]/;
	$dom_realm{$domain} = $realm;
	if ($domain =~ /^\./) {
		$domain =~ s/^\.//;
		$def_realm{$realm} = $domain;
	}
}

print "[libdefaults]\n\tdefault_realm = $default_realm\n";

print "[realms]\n";

foreach $realm (sort(keys(%realmpt))) {
	print "\t$realm = {\n";
	for ($i = 1; $i < $realmpt{$realm}; $i++) {
		printf("\t\tkdc = %s\n", $realmkdc{$realm . "##" . $i});
		
	}
	if (defined($realmadmin{$realm})) {
		print "\t\tadmin_server = $realmadmin{$realm}\n";
	}
	if (defined($def_realm{$realm})) {
		print "\t\tdefault_domain = $def_realm{$realm}\n";
	}
	print "\t}\n";
}

print "\n[domain_realm]\n";

foreach $domain (keys(%dom_realm)) {
	print "\t$domain = $dom_realm{$domain}\n";
}



