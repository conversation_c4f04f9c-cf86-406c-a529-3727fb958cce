


          
# MsQuic ETW 实现分析

## 1. ETW 概述

ETW (Event Tracing for Windows) 是 Windows 操作系统提供的一种高效、低开销的跟踪机制，用于收集系统和应用程序的运行时信息。MsQuic 项目利用 ETW 来实现其跟踪和诊断功能，帮助开发人员监控和调试 QUIC 协议的实现。

## 2. MsQuic ETW 工具实现

### 2.1 目录结构

MsQuic ETW 工具的源代码位于 `src/tools/etw/` 目录下，主要包含以下文件：

- `main.c` - 主程序入口和命令行处理
- `quicetw.h` - ETW 工具的头文件
- `trace.c` - 跟踪事件处理
- `cxn.c` - 连接相关事件处理
- `worker.c` - 工作线程相关事件处理
- `binding.c` - 绑定相关事件处理
- `listener.c` - 监听器相关事件处理
- `session.c` - 会话相关事件处理
- `stream.c` - 流相关事件处理
- `library.c` - 库相关事件处理
- `object_set.h` - 对象集合管理
- `qjson.h` - JSON 输出支持

etw trace 支持主要代码在 `src/plugins/trace` 下 etw相关跟踪点定义：

```c#
    public enum QuicObjectType
    {
        Global,
        Registration,
        Configuration,
        Worker,
        Listener,
        Binding,
        Connection,
        Stream,
        Datapath
    }

     public enum QuicEventId : ushort
    {
        LibraryInitialized = 1,
        LibraryUninitialized,
        LibraryAddRef,
        LibraryRelease,
        LibraryServerInit,
        AllocFailure,
        LibraryRundown,
        LibraryError,
        LibraryErrorStatus,
        LibraryAssert,
        ApiEnter,
        ApiExit,
        ApiExitStatus,
        ApiWaitOperation,
        PerfCountersRundown,
        LibrarySendRetryStateUpdated,
        LibraryVersion,
        LibraryInitializedV2,
        DataPathInitialized,
        LibraryRundownV2,
        DataPathRundown,

        RegistrationCreated = 1024,
        RegistrationDestroyed,
        RegistrationCleanup,
        RegistrationRundown,
        RegistrationError,
        RegistrationErrorStatus,
        RegistrationShutdown,
        RegistrationCreatedV2,
        RegistrationRundownV2,

        WorkerCreated = 2048,
        WorkerStart,
        WorkerStop,
        WorkerActivityStateUpdated,
        WorkerQueueDelayUpdated,
        WorkerDestroyed,
        WorkerCleanup,
        WorkerError,
        WorkerErrorStatus,

        ConfigurationCreated = 3072,
        ConfigurationDestroyed,
        ConfigurationCleanup,
        QuicConfigurationRundown = 3076,
        ConfigurationError,
        QuicConfigurationErrorStatus,

        ListenerCreated = 4096,
        ListenerDestroyed,
        ListenerStarted,
        ListenerStopped,
        ListenerRundown,
        ListenerError,
        ListenerErrorStatus,

        ConnCreated = 5120,
        ConnDestroyed,
        ConnHandshakeComplete,
        ConnScheduleState,
        ConnExecOper,
        ConnExecApiOper,
        ConnExecTimerOper,
        ConnLocalAddrAdded,
        ConnRemoteAddrAdded,
        ConnLocalAddrRemoved,
        ConnRemoteAddrRemoved,
        ConnAssignWorker,
        ConnHandshakeStart,
        ConnRegisterSession,
        ConnUnregisterSession,
        ConnTransportShutdown,
        ConnAppShutdown,
        ConnInitializeComplete,
        ConnHandleClosed,
        ConnVersionSet,
        ConnOutFlowStats,
        ConnOutFlowBlocked,
        ConnInFlowStats,
        ConnCubic,
        ConnCongestion,
        ConnPersistentCongestion,
        ConnRecoveryExit,
        ConnRundown,
        ConnSourceCidAdded,
        ConnDestCidAdded,
        ConnSourceCidRemoved,
        ConnDestCidRemoved,
        ConnLossDetectionTimerSet,
        ConnLossDetectionTimerCancel,
        ConnDropPacket,
        ConnDropPacketEx,
        ConnError,
        ConnErrorStatus,
        ConnNewPacketKeys,
        ConnKeyPhaseChange,
        ConnStats,
        ConnShutdownComplete,
        ConnReadKeyUpdated,
        ConnWriteKeyUpdated,
        ConnPacketSent,
        ConnPacketRecv,
        ConnPacketLost,
        ConnPacketACKed,
        ConnLogError,
        ConnLogWarning,
        ConnLogInfo,
        ConnLogVerbose,
        ConnQueueSendFlush,
        ConnOutFlowStreamStats,
        ConnPacketStats,
        ConnServerResumeTicket,
        ConnVNEOtherVersionList,
        ConnClientReceivedVersionList,
        ConnServerSupportedVersionList,
        ConnSpuriousCongestion,
        ConnNoListenerIp,
        ConnNoListenerAlpn,
        ConnFlushSend,
        ConnTimerSet,
        ConnTimerCancel,
        ConnTimerExpire,
        ConnBbr,
        ConnEcnCapable,
        ConnEcnFailed,
        ConnCongestionV2,
        ConnStatsV2,
        ConnCubicHyStart,
        ConnRecvUdpDatagrams,
        ConnOutFlowStatsV2,
        ConnStatsV3,

        StreamCreated = 6144,
        StreamDestroyed,
        StreamOutFlowBlocked,
        StreamRundown,
        StreamSendState,
        StreamRecvState,
        StreamError,
        StreamErrorStatus,
        StreamLogError,
        StreamLogWarning,
        StreamLogInfo,
        StreamLogVerbose,
        StreamAlloc,
        StreamWriteFrames,
        StreamReceiveFrame,
        StreamAppReceive,
        StreamAppReceiveComplete,
        StreamAppSend,
        StreamReceiveFrameComplete,
        StreamAppReceiveCompleteCall,

        BindingCreated = 7168,
        BindingRundown,
        BindingDestroyed,
        BindingCleanup,
        BindingDropPacket,
        BindingDropPacketEx,
        BindingError,
        BindingErrorStatus,
        BindingExecOper,

        TlsError = 8192,
        TlsErrorStatus,
        TlsMessage,

        Temporal = 9216, // Temporary, while there are still builds out there generating this old event
        DatapathSend,
        DatapathRecv,
        DatapathError,
        DatapathErrorStatus,
        DatapathCreated,
        DatapathDestroyed,

        LogError = 10240,
        LogWarning,
        LogInfo,
        LogVerbose,

        PacketCreated = 11264,
        PacketEncrypt,
        PacketFinalize,
        PacketBatchSent,
        PacketReceive,
        PacketDecrypt
    }
```
### 2.2 核心数据结构

```c
// 命令参数结构
typedef struct CMD_ARGS {
    COMMAND Command;
    BOOLEAN FormatCSV;
    SORT_TYPE Sort;
    FILTER_TYPE Filter;
    ULONG SelectedId;
    ULONG64 OutputResolution;
    ULONG MaxOutputLines;
    UCHAR Cid[256];
    ULONG CidLength;
    BOOLEAN Verbose;
} CMD_ARGS;

// 跟踪状态结构
typedef struct TRACE_STATE {
    TRACEHANDLE Handle;
    BOOLEAN Processed;
    ULONG64 EventCount;
    ULONG64 ApiCallCount;
    ULONG64 EventTypeCount[EventType_Count];
    ULONG64 StartTimestamp;
    ULONG64 StopTimestamp;
    ULONG64 ProcessedMs;
    ULONG OutputLineCount;
    BOOLEAN HasSchedulingEvents;
    BOOLEAN HasDatapathEvents;
} TRACE_STATE;
```

### 2.3 ETW 提供程序 ID

MsQuic 定义了自己的 ETW 提供程序 ID，用于标识来自 MsQuic 的事件：

```c
const GUID QuicEtwProviderId = { // {ff15e657-4f26-570e-88ab-0796b258d11c}
    0xff15e657,0x4f26,0x570e,0x88,0xab,0x07,0x96,0xb2,0x58,0xd1,0x1c};
```

## 3. ETW 事件类型

MsQuic 定义了多种事件类型，用于跟踪不同组件的行为：
```c
typedef enum QUIC_EVENT_TYPE {
    EventType_Global,
    EventType_Registration,
    EventType_Worker,
    EventType_Session,
    EventType_Listener,
    EventType_Connection,
    EventType_Stream,
    EventType_Binding,
    EventType_Tls,
    EventType_Datapath,
    EventType_Log,

    EventType_Count
} QUIC_EVENT_TYPE;
```
1. **库事件** - 跟踪 MsQuic 库的初始化和清理
2. **注册事件** - 跟踪应用程序注册
3. **工作线程事件** - 跟踪工作线程的创建、调度和处理
4. **会话事件** - 跟踪 QUIC 会话的生命周期
5. **监听器事件** - 跟踪监听器的创建和连接接受
6. **连接事件** - 跟踪连接的建立、维护和关闭
7. **流事件** - 跟踪流的创建、数据传输和关闭
8. **绑定事件** - 跟踪网络绑定
9. **TLS 事件** - 跟踪 TLS 握手和加密操作
10. **数据路径事件** - 跟踪数据包的发送和接收
11. **日志事件** - 跟踪应用程序日志

## 4. ETW 工具使用方法

### 4.1 命令行选项

MsQuic ETW 工具提供了丰富的命令行选项，用于收集和分析 ETW 跟踪数据：

```
quicetw <f.etl> [options] [command]
quicetw --local [options] [command]
```

主要命令包括：

- `--summary` - 显示一般事件/文件信息
- `--report` - 生成系统报告
- `--trace` - 将所有 ETW 日志转换为文本
- `--conn` - 显示连接信息
- `--conn_list` - 列出所有连接
- `--conn_tput` - 显示连接吞吐量
- `--conn_trace` - 显示连接跟踪
- `--conn_qlog` - 生成连接 qlog
- `--worker` - 显示工作线程信息
- `--worker_list` - 列出所有工作线程
- `--worker_queue` - 显示工作线程队列
- `--worker_trace` - 显示工作线程跟踪
- `--stream_trace` - 显示流跟踪

### 4.2 收集 ETW 跟踪

ETW 工具可以通过两种方式收集跟踪数据：

1. **分析现有 ETL 文件**：
   ```
   quicetw <f.etl> [options] [command]
   ```

2. **实时收集跟踪**：
   ```
   quicetw --local [options] [command]
   ```

实时收集跟踪时，工具会创建一个 ETW 会话，启用 MsQuic 提供程序，收集一段时间的事件，然后停止会话并分析收集到的数据。

## 5. ETW 跟踪点示例

MsQuic 核心代码中定义了大量的跟踪点，用于记录关键操作和状态变化。这些跟踪点通过宏定义，在编译时可以选择启用或禁用。
MsQuic 在代码中定义了多种跟踪点，用于记录关键操作和状态变化。以下是一些常见的跟踪点示例：

### 5.1 连接跟踪点
```c
QuicTraceEvent(
    ConnCreated,
    "[conn][%p] Created, IdleTimeout=%llu ms",
    Connection,
    Connection->Settings.IdleTimeoutMs);

QuicTraceEvent(
    ConnDestroyed,
    "[conn][%p] Destroyed",
    Connection);

QuicTraceEvent(
    ConnHandshakeComplete,
    "[conn][%p] Handshake complete",
    Connection);
 ```

### 5.2 流跟踪点
```c
QuicTraceEvent(
    StreamCreated,
    "[strm][%p] Created, Conn=%p, ID=%llu, IsLocal=%hhu",
    Stream,
    Stream->Connection,
    Stream->ID,
    Stream->IsLocallyInitiated);

QuicTraceEvent(
    StreamDestroyed,
    "[strm][%p] Destroyed",
    Stream);
 ```

### 5.3 数据包跟踪点
```c
QuicTraceEvent(
    PacketSent,
    "[conn][%p] Sent [%llu] %hhu bytes (Flags=%hhu) Dst=%!ADDR!",
    Connection,
    PacketNumber,
    (uint8_t)PacketLength,
    (uint8_t)Flags,
    CASTED_CLOG_BYTEARRAY(sizeof(Connection->Paths[0].RemoteAddress), &Connection->Paths[0].RemoteAddress));
```

## 6. 自定义 ETW 跟踪点
要在自己的应用程序中添加 MsQuic ETW 跟踪点，可以使用以下步骤：

### 6.1 包含必要的头文件
```c
#include "msquic.h"
#include "quic_trace.h"
 ```

### 6.2 定义跟踪点
在应用程序中，可以使用 QuicTraceEvent 宏添加跟踪点：

```c
QuicTraceEvent(
    AppCustomEvent,
    "[app][%p] Custom event: %s",
    AppContext,
    "Important information");
 ```

### 6.3 完整示例
以下是一个简单的示例，展示如何在应用程序中使用 MsQuic ETW 跟踪点：

```c
#include "msquic.h"
#include "quic_trace.h"

void MyQuicFunction(HQUIC Connection) {
    // 记录函数开始
    QuicTraceEvent(
        AppFunctionEntry,
        "[app][%p] MyQuicFunction entry",
        Connection);
    
    // 执行一些操作
    // ...
    
    // 记录重要事件
    QuicTraceEvent(
        AppImportantEvent,
        "[app][%p] Important event occurred: %s",
        Connection,
        "Connection established successfully");
    
    // 记录函数退出
    QuicTraceEvent(
        AppFunctionExit,
        "[app][%p] MyQuicFunction exit",
        Connection);
}
 ```

## 7. 跟踪事件回调
当 ETW 事件发生时，ETW 工具通过 `EventCallback` 函数处理这些事件：

```c
void WINAPI EventCallback(_In_ PEVENT_RECORD ev)
{
    if (!IsEqualGUID(&ev->EventHeader.ProviderId, &QuicEtwProviderId)) {
        return;
    }

    QUIC_EVENT_TYPE EventType = GetEventType(ev->EventHeader.EventDescriptor.Id);
    // 处理不同类型的事件...
}
```

不同类型的事件由相应的处理函数处理，如 `ConnEventCallback`、`WorkerEventCallback` 等。
MsQuic 使用 McGenControlCallbackV2 函数作为 ETW 控制回调，用于处理 ETW 会话的启用和禁用通知：

```c
VOID
__stdcall
McGenControlCallbackV2(
    _In_ LPCGUID SourceId,
    _In_ ULONG ControlCode,
    _In_ UCHAR Level,
    _In_ ULONGLONG MatchAnyKeyword,
    _In_ ULONGLONG MatchAllKeyword,
    _In_opt_ PEVENT_FILTER_DESCRIPTOR FilterData,
    _Inout_opt_ PVOID CallbackContext
    )
 ```

当 ETW 会话启用或禁用 MsQuic 提供程序时，此回调函数会被调用，用于更新跟踪上下文的状态。

## 8. ETW 工具运行流程

### 8.1 主程序流程

```mermaid
flowchart TD
    A[程序入口] --> B{解析命令行参数}
    B --> C{是否使用现有ETL文件?}
    C -->|是| D[打开ETL文件]
    C -->|否| E[创建ETW会话]
    E --> F[启用MsQuic提供程序]
    F --> G[收集事件]
    G --> H[停止ETW会话]
    H --> D
    D --> I[处理ETW事件]
    I --> J{执行指定命令}
    J --> K[输出结果]
    K --> L[程序结束]
```

### 8.2 事件处理流程

```mermaid
flowchart TD
    A[接收ETW事件] --> B{检查提供程序ID}
    B -->|不匹配| C[忽略事件]
    B -->|匹配| D[确定事件类型]
    D --> E[更新事件计数]
    E --> F{调用相应的事件处理函数}
    F --> G[ConnEventCallback]
    F --> H[WorkerEventCallback]
    F --> I[StreamEventCallback]
    F --> J[其他事件回调...]
    G --> K{是否需要跟踪事件?}
    H --> K
    I --> K
    J --> K
    K -->|是| L[调用QuicTraceEvent]
    K -->|否| M[完成事件处理]
    L --> M
```

## 9. ETW 与 MsQuic 核心组件的交互

### 9.1 连接跟踪

MsQuic 核心中的连接组件（`connection.c`）在关键点插入了 ETW 跟踪点，记录连接的创建、状态变化、数据传输和关闭等事件。ETW 工具通过 `ConnEventCallback` 函数处理这些事件，构建连接对象的状态模型。

### 10.2 工作线程跟踪

工作线程组件（`worker.c`）记录线程创建、调度和处理连接的事件。ETW 工具通过 `WorkerEventCallback` 函数处理这些事件，跟踪工作线程的性能和行为。

### 10.3 流跟踪

流组件（`stream.c`、`stream_send.c`、`stream_recv.c`）记录流的创建、数据发送/接收和关闭等事件。ETW 工具通过 `StreamEventCallback` 函数处理这些事件，分析流的行为和性能。

## 11. ETW 交互流程图

### 11.1 应用程序与 MsQuic 的 ETW 交互

```mermaid
sequenceDiagram
    participant App as 应用程序
    participant MsQuic as MsQuic库
    participant ETW as ETW基础设施
    participant Tool as ETW工具

    App->>MsQuic: 调用MsQuic API
    MsQuic->>ETW: 记录ETW事件
    ETW->>Tool: 传递事件
    Tool->>Tool: 处理事件
    Tool->>App: 显示分析结果
```

### 11.2 连接建立的 ETW 跟踪

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Server as 服务器
    participant ETW as ETW基础设施
    participant Tool as ETW工具

    Client->>ETW: 记录连接开始事件
    Client->>Server: 发送初始数据包
    ETW->>Tool: 传递连接开始事件
    Server->>ETW: 记录连接接受事件
    Server->>Client: 发送响应数据包
    ETW->>Tool: 传递连接接受事件
    Client->>ETW: 记录握手完成事件
    ETW->>Tool: 传递握手完成事件
    Tool->>Tool: 更新连接状态模型
```

### 11.3 数据传输的 ETW 跟踪

```mermaid
sequenceDiagram
    participant Sender as 发送方
    participant Receiver as 接收方
    participant ETW as ETW基础设施
    participant Tool as ETW工具

    Sender->>ETW: 记录发送开始事件
    Sender->>Receiver: 发送数据包
    ETW->>Tool: 传递发送事件
    Receiver->>ETW: 记录接收事件
    ETW->>Tool: 传递接收事件
    Receiver->>Sender: 发送ACK
    Receiver->>ETW: 记录ACK发送事件
    ETW->>Tool: 传递ACK发送事件
    Sender->>ETW: 记录ACK接收事件
    ETW->>Tool: 传递ACK接收事件
    Tool->>Tool: 计算吞吐量和延迟
```

## 12. ETW 工具输出示例

### 12.1 连接列表输出

```
ID  State      Age(us)   Active(us) Queued(us) Idle(us)   TX    RX    LocalIp         RemoteIp        SourceCid       DestinationCid
1   Connected  12345678  1234567    123456     1234567    1234  5678  ***********:443  ***********:12345  0123456789abcdef  fedcba9876543210
```

### 12.2 连接吞吐量输出

```
ms    TxMbps  RxMbps  RttMs  CongEvents  InFlight  Cwnd   TxBufBytes  FlowAvailStrm  FlowAvailConn  SsThresh  CubicK  CubicWindowMax  StrmSndWnd
0     123.45  234.56  12.34  0           12345     65535  123456      65535          65535          65535     12345   65535           65535
100   234.56  345.67  12.34  0           23456     65535  234567      65535          65535          65535     23456   65535           65535
```

### 12.3 工作线程列表输出

```
ID  Thread  IdealProc  CxnCount  Age(us)   Active(us)
1   1234    0          12        12345678  1234567
2   2345    1          23        23456789  2345678
```

## 13. 总结

MsQuic ETW 工具是一个强大的诊断和分析工具，它利用 Windows ETW 基础设施收集和分析 MsQuic 库的运行时行为。通过分析 ETW 事件，开发人员可以深入了解 QUIC 连接的建立、数据传输、拥塞控制和错误处理等方面的性能和行为。

ETW 工具与 MsQuic 核心代码紧密集成，核心组件在关键点插入跟踪点，记录重要事件和状态变化。ETW 工具通过处理这些事件，构建对象模型，计算性能指标，并提供丰富的命令行选项来分析和可视化这些数据。

通过使用 MsQuic ETW 工具，开发人员可以更有效地调试和优化基于 QUIC 协议的应用程序，确保它们在各种网络条件下都能高效可靠地运行。     