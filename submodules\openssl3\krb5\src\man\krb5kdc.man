.\" Man page generated from reStructuredText.
.
.TH "KRB5KDC" "8" " " "1.20" "MIT Kerberos"
.SH NAME
krb5kdc \- Kerberos V5 KDC
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBkrb5kdc\fP
[\fB\-x\fP \fIdb_args\fP]
[\fB\-d\fP \fIdbname\fP]
[\fB\-k\fP \fIkeytype\fP]
[\fB\-M\fP \fImkeyname\fP]
[\fB\-p\fP \fIportnum\fP]
[\fB\-m\fP]
[\fB\-r\fP \fIrealm\fP]
[\fB\-n\fP]
[\fB\-w\fP \fInumworkers\fP]
[\fB\-P\fP \fIpid_file\fP]
[\fB\-T\fP \fItime_offset\fP]
.SH DESCRIPTION
.sp
krb5kdc is the Kerberos version 5 Authentication Service and Key
Distribution Center (AS/KDC).
.SH OPTIONS
.sp
The \fB\-r\fP \fIrealm\fP option specifies the realm for which the server
should provide service.  This option may be specified multiple times
to serve multiple realms.  If no \fB\-r\fP option is given, the default
realm (as specified in krb5.conf(5)) will be served.
.sp
The \fB\-d\fP \fIdbname\fP option specifies the name under which the
principal database can be found.  This option does not apply to the
LDAP database.
.sp
The \fB\-k\fP \fIkeytype\fP option specifies the key type of the master key
to be entered manually as a password when \fB\-m\fP is given; the default
is \fBaes256\-cts\-hmac\-sha1\-96\fP\&.
.sp
The \fB\-M\fP \fImkeyname\fP option specifies the principal name for the
master key in the database (usually \fBK/M\fP in the KDC\(aqs realm).
.sp
The \fB\-m\fP option specifies that the master database password should
be fetched from the keyboard rather than from a stash file.
.sp
The \fB\-n\fP option specifies that the KDC does not put itself in the
background and does not disassociate itself from the terminal.
.sp
The \fB\-P\fP \fIpid_file\fP option tells the KDC to write its PID into
\fIpid_file\fP after it starts up.  This can be used to identify whether
the KDC is still running and to allow init scripts to stop the correct
process.
.sp
The \fB\-p\fP \fIportnum\fP option specifies the default UDP and TCP port
numbers which the KDC should listen on for Kerberos version 5
requests, as a comma\-separated list.  This value overrides the port
numbers specified in the kdcdefaults section of
kdc.conf(5), but may be overridden by realm\-specific values.
If no value is given from any source, the default port is 88.
.sp
The \fB\-w\fP \fInumworkers\fP option tells the KDC to fork \fInumworkers\fP
processes to listen to the KDC ports and process requests in parallel.
The top level KDC process (whose pid is recorded in the pid file if
the \fB\-P\fP option is also given) acts as a supervisor.  The supervisor
will relay SIGHUP signals to the worker subprocesses, and will
terminate the worker subprocess if the it is itself terminated or if
any other worker process exits.
.sp
The \fB\-x\fP \fIdb_args\fP option specifies database\-specific arguments.
See Database Options in kadmin(1) for
supported arguments.
.sp
The \fB\-T\fP \fIoffset\fP option specifies a time offset, in seconds, which
the KDC will operate under.  It is intended only for testing purposes.
.SH EXAMPLE
.sp
The KDC may service requests for multiple realms (maximum 32 realms).
The realms are listed on the command line.  Per\-realm options that can
be specified on the command line pertain for each realm that follows
it and are superseded by subsequent definitions of the same option.
.sp
For example:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
krb5kdc \-p 2001 \-r REALM1 \-p 2002 \-r REALM2 \-r REALM3
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
specifies that the KDC listen on port 2001 for REALM1 and on port 2002
for REALM2 and REALM3.  Additionally, per\-realm parameters may be
specified in the kdc.conf(5) file.  The location of this file
may be specified by the \fBKRB5_KDC_PROFILE\fP environment variable.
Per\-realm parameters specified in this file take precedence over
options specified on the command line.  See the kdc.conf(5)
description for further details.
.SH ENVIRONMENT
.sp
See kerberos(7) for a description of Kerberos environment
variables.
.SH SEE ALSO
.sp
kdb5_util(8), kdc.conf(5), krb5.conf(5),
kdb5_ldap_util(8), kerberos(7)
.SH AUTHOR
MIT
.SH COPYRIGHT
1985-2021, MIT
.\" Generated by docutils manpage writer.
.
