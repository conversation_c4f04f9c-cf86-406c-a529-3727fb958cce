.. hazmat::

Asymmetric Utilities
====================

.. currentmodule:: cryptography.hazmat.primitives.asymmetric.utils


.. function:: decode_dss_signature(signature)

    Takes in signatures generated by the DSA/ECDSA signers and returns a
    tuple ``(r, s)``. These signatures are ASN.1 encoded ``Dss-Sig-Value``
    sequences (as defined in :rfc:`3279`)

    :param bytes signature: The signature to decode.

    :returns: The decoded tuple ``(r, s)``.

    :raises ValueError: Raised if the signature is malformed.

.. function:: encode_dss_signature(r, s)

    Creates an ASN.1 encoded ``Dss-Sig-Value`` (as defined in :rfc:`3279`) from
    raw ``r`` and ``s`` values.

    :param int r: The raw signature value ``r``.

    :param int s: The raw signature value ``s``.

    :return bytes: The encoded signature.

.. class:: Prehashed(algorithm)

    .. versionadded:: 1.6

    ``Prehashed`` can be passed as the ``algorithm`` in the RSA
    :meth:`~cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.sign`
    and
    :meth:`~cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.verify`
    as well as DSA
    :meth:`~cryptography.hazmat.primitives.asymmetric.dsa.DSAPrivateKey.sign`
    and
    :meth:`~cryptography.hazmat.primitives.asymmetric.dsa.DSAPublicKey.verify`
    methods.

    For elliptic curves it can be passed as the ``algorithm`` in
    :class:`~cryptography.hazmat.primitives.asymmetric.ec.ECDSA` and then used
    with
    :meth:`~cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey.sign`
    and
    :meth:`~cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey.verify`
    .

    :param algorithm: An instance of
        :class:`~cryptography.hazmat.primitives.hashes.HashAlgorithm`.

    .. doctest::

        >>> import hashlib
        >>> from cryptography.hazmat.primitives import hashes
        >>> from cryptography.hazmat.primitives.asymmetric import (
        ...    padding, rsa, utils
        ... )
        >>> private_key = rsa.generate_private_key(
        ...     public_exponent=65537,
        ...     key_size=2048,
        ... )
        >>> prehashed_msg = hashlib.sha256(b"A message I want to sign").digest()
        >>> signature = private_key.sign(
        ...     prehashed_msg,
        ...     padding.PSS(
        ...         mgf=padding.MGF1(hashes.SHA256()),
        ...         salt_length=padding.PSS.MAX_LENGTH
        ...     ),
        ...     utils.Prehashed(hashes.SHA256())
        ... )
        >>> public_key = private_key.public_key()
        >>> public_key.verify(
        ...     signature,
        ...     prehashed_msg,
        ...     padding.PSS(
        ...         mgf=padding.MGF1(hashes.SHA256()),
        ...         salt_length=padding.PSS.MAX_LENGTH
        ...     ),
        ...     utils.Prehashed(hashes.SHA256())
        ... )
