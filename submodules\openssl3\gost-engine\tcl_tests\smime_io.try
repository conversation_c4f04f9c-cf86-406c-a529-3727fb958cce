#!/usr/bin/tclsh
lappend auto_path [file dirname [info script]]
package require ossltest
cd $::test::dir
set testname [file rootname [file tail $::argv0]]

start_tests "Тесты на совместимость smime и cms -sign" 

if {[info exists env(ALG_LIST)]} {
	set alg_list $env(ALG_LIST)
} else {
	switch -exact [engine_name] {
		"ccore" {set alg_list {gost2001:A gost2001:B gost2001:C gost2012_256:A gost2012_256:B gost2012_256:C gost2012_512:A gost2012_512:B}}
		"open" {set alg_list {gost2001:A gost2001:B gost2001:C gost2012_256:A gost2012_256:B gost2012_256:C gost2012_512:A gost2012_512:B}}
	}
}

foreach alg $alg_list {
	set alg_fn [string map {":" "_"} $alg]
	set username U_smime_$alg_fn
	switch -glob $alg {
		gost2012* {set ::test::ca smimeCA-2012
		           set ca_sign_alg hash_with_sign12_512
		          }
		* {set ::test::ca smimeCA
		   set ca_sign_alg hash_with_sign01_cp
		  }
	}

test -skip {![file exists sign_$alg_fn.msg]} "Verifying a message signed with $alg without ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_$alg_fn.msg -out verified.txt -noverify -certfile $username/cert.pem"]
} 0 "CMS Verification successful
"

test -skip {![file exists sign_$alg_fn.msg]}  "Verifying a message signed with $alg with ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_$alg_fn.msg -out verified.txt -certfile $username/cert.pem -CAfile $::test::ca/cacert.pem"]
} 0 "CMS Verification successful
"

test -skip {![file exists bad_$alg_fn.msg]} -createsfiles verified.txt "Verifying corrupted messages signed with $alg via smime" {
	grep Verification [openssl "cms -verify -text -in bad_$alg_fn.msg -out verified.txt -noverify -certfile $username/cert.pem"]
} 1 "Verification failure"

test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert inside without ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_c_$alg_fn.msg -out verified.txt -noverify"]
} 0 "CMS Verification successful
"

test -skip {![file exists sign_c_$alg_fn.msg]} "Verifying a message signed with $alg having cert with ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_c_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 0 "CMS Verification successful
"

test -createsfiles verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a message signed by $alg having cert inside without ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_op_$alg_fn.msg -out verified.txt -noverify"]
} 0 "CMS Verification successful
"

test -createsfiles verified.txt -skip {![file exists sign_op_$alg_fn.msg]} "Verifying a $alg opaque  message with ca via cms" {
	grep Veri [openssl "cms -verify -text -in sign_op_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 0 "CMS Verification successful
"

test -skip {![file exists broken_op_$alg_fn.msg]} "Verifying broken $alg opaque message" {
	grep Verification [openssl "cms -verify -text -in broken_op_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 1 "Verification failure"

test -skip {![file exists sign_det_$alg_fn.pem]} "Verifying detached $alg signature via cms" {
	grep Veri [openssl "cms -verify -binary -content sign.dat -inform PEM -in sign_det_$alg_fn.pem -out verified.txt -noverify"]
} 0 "CMS Verification successful
"

test -skip {![file exists sign_det_$alg_fn.msg]} -createsfiles {bad.dat} "Verifying corrupted $alg detached signature" {
	makeFile bad.dat [regsub Test [getFile sign.dat] Best]
	grep Verification [openssl "cms -verify -content bad.dat -in sign_det_$alg_fn.msg -out verified.txt -CAfile $::test::ca/cacert.pem"]
} 1 "Verification failure"

}
end_tests
