#
# Generated makefile dependencies follow.
#
$(OUTPRE)tcl_kadm5.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  $(BUILDTOP)/include/gssapi/gssapi.h $(BUILDTOP)/include/gssrpc/types.h \
  $(BUILDTOP)/include/kadm5/admin.h $(BUILDTOP)/include/kadm5/chpass_util_strings.h \
  $(BUILDTOP)/include/kadm5/kadm_err.h $(BUILDTOP)/include/krb5/krb5.h \
  $(BUILDTOP)/lib/kdb/adb_err.h $(COM_ERR_DEPS) $(top_srcdir)/include/gssrpc/auth.h \
  $(top_srcdir)/include/gssrpc/auth_gss.h $(top_srcdir)/include/gssrpc/auth_unix.h \
  $(top_srcdir)/include/gssrpc/clnt.h $(top_srcdir)/include/gssrpc/rename.h \
  $(top_srcdir)/include/gssrpc/rpc.h $(top_srcdir)/include/gssrpc/rpc_msg.h \
  $(top_srcdir)/include/gssrpc/svc.h $(top_srcdir)/include/gssrpc/svc_auth.h \
  $(top_srcdir)/include/gssrpc/xdr.h $(top_srcdir)/include/kdb.h \
  $(top_srcdir)/include/krb5.h tcl_kadm5.c tcl_kadm5.h
$(OUTPRE)test.$(OBJEXT): $(BUILDTOP)/include/autoconf.h \
  tcl_kadm5.h test.c
