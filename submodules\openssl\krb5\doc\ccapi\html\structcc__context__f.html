<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head><meta http-equiv="Content-Type" content="text/html;charset=iso-8859-1">
<title>Credentials Cache API   : cc_context_f Struct Reference</title>
<link href="doxygen.css" rel="stylesheet" type="text/css">
<link href="tabs.css" rel="stylesheet" type="text/css">
</head><body>
<!-- Generated by Doxygen 1.4.6 -->
<h1>cc_context_f Struct Reference</h1><!-- doxytag: class="cc_context_f" --><hr><a name="_details"></a><h2>Detailed Description</h2>
Function pointer table for cc_context_t. For more information see <a class="el" href="group__cc__context__reference.html">cc_context_t Overview</a>. 
<p>
<h2>Data Fields</h2>
<ul>
<li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#239ea938e3c076e6e245a9236bb05b24">release</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> io_context)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g8ff82ce108889d4ed29f46ffe6efc40e">cc_context_release()</a></b>: Release memory associated with a cc_context_t.  <a href="#239ea938e3c076e6e245a9236bb05b24"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#51bd5a48dcd263bfb3128cc5838b4cd7">get_change_time</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_time)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a></b>: Get the last time the cache collection changed.  <a href="#51bd5a48dcd263bfb3128cc5838b4cd7"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#4e9432f5a1a10319037b0f04e8219c1b">get_default_ccache_name</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_name)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name()</a></b>: Get the name of the default ccache.  <a href="#4e9432f5a1a10319037b0f04e8219c1b"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#bf8e7415758b890ca8f01ce5a00985ab">open_ccache</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, const char *in_name, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g256a5ba17fe0e4502e0722d9b081bbef">cc_context_open_ccache()</a></b>: Open a ccache.  <a href="#bf8e7415758b890ca8f01ce5a00985ab"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#7f101feaa1c88c8997b678507c029c39">open_default_ccache</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g45a7ce29eb409baabadcae1bc95d5c57">cc_context_open_default_ccache()</a></b>: Open the default ccache.  <a href="#7f101feaa1c88c8997b678507c029c39"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#82d3579723a0f909cb46c2016ed4ae22">create_ccache</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, const char *in_name, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a></b>: Create a new ccache.  <a href="#82d3579723a0f909cb46c2016ed4ae22"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#b52a31c1fb59ac752baa16503d8e0e3f">create_default_ccache</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a></b>: Create a new default ccache.  <a href="#b52a31c1fb59ac752baa16503d8e0e3f"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#939a8ed76079bf71000347c40aeb5b2f">create_new_ccache</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a></b>: Create a new uniquely named ccache.  <a href="#939a8ed76079bf71000347c40aeb5b2f"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#0f945985d42255226915403df147667f">new_ccache_iterator</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> *out_iterator)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g6957bc9570e4769a5b1213d2a1d90cd7">cc_context_new_ccache_iterator()</a></b>: Get an iterator for the cache collection.  <a href="#0f945985d42255226915403df147667f"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#1be78f795193b04c4f45cb7d3c46480c">lock</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_lock_type, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_block)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#gcf4595340ddc8dafa539a86ac317625d">cc_context_lock()</a></b>: Lock the cache collection.  <a href="#1be78f795193b04c4f45cb7d3c46480c"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#55e38bd72efee8445b3abbc3fa5e7e27">unlock</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g544261b88c9ac0f2379a35648cae3f27">cc_context_unlock()</a></b>: Unlock the cache collection.  <a href="#55e38bd72efee8445b3abbc3fa5e7e27"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#bb728ccd97eb387991feed0500475112">compare</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context, <a class="el" href="structcc__context__d.html">cc_context_t</a> in_compare_to_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g9258ef05d06f3d4dc798ec654f78b967">cc_context_compare()</a></b>: Compare two context objects.  <a href="#bb728ccd97eb387991feed0500475112"></a><br></dl><li><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#39a27ecd6d29fb7288f983c42d5686d0">wait_for_change</a> )(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context)
<dl class="el"><dd class="mdescRight"><b><a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a></b>: Wait for the next change in the cache collection.  <a href="#39a27ecd6d29fb7288f983c42d5686d0"></a><br></dl></ul>
<hr><h2>Field Documentation</h2>
<a class="anchor" name="239ea938e3c076e6e245a9236bb05b24"></a><!-- doxytag: member="cc_context_f::release" ref="239ea938e3c076e6e245a9236bb05b24" args=")(cc_context_t io_context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#239ea938e3c076e6e245a9236bb05b24">release</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> io_context)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g8ff82ce108889d4ed29f46ffe6efc40e">cc_context_release()</a></b>: Release memory associated with a cc_context_t. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>io_context</em>&nbsp;</td><td>the context object to free. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="51bd5a48dcd263bfb3128cc5838b4cd7"></a><!-- doxytag: member="cc_context_f::get_change_time" ref="51bd5a48dcd263bfb3128cc5838b4cd7" args=")(cc_context_t in_context, cc_time_t *out_time)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#51bd5a48dcd263bfb3128cc5838b4cd7">get_change_time</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ge7a754cfe5664beadddaa100646c9742">cc_time_t</a> *out_time)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a></b>: Get the last time the cache collection changed. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection to examine. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_time</em>&nbsp;</td><td>on exit, the time of the most recent change for the entire ccache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
This function returns the time of the most recent change for the entire ccache collection. By maintaining a local copy the caller can deduce whether or not the ccache collection has been modified since the previous call to <a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a>.<p>
The time returned by cc_context_get_changed_time() increases whenever:<p>
<ul>
<li>a ccache is created </li>
<li>a ccache is destroyed </li>
<li>a credential is stored </li>
<li>a credential is removed </li>
<li>a ccache principal is changed </li>
<li>the default ccache is changed</li>
</ul>
<dl compact><dt><b>Note:</b></dt><dd>In order to be able to compare two values returned by <a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a>, the caller must use the same context to acquire them. Callers should maintain a single context in memory for <a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a> calls rather than creating a new context for every call.</dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__context__f.html#39a27ecd6d29fb7288f983c42d5686d0">wait_for_change</a> </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="4e9432f5a1a10319037b0f04e8219c1b"></a><!-- doxytag: member="cc_context_f::get_default_ccache_name" ref="4e9432f5a1a10319037b0f04e8219c1b" args=")(cc_context_t in_context, cc_string_t *out_name)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#4e9432f5a1a10319037b0f04e8219c1b">get_default_ccache_name</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__string__d.html">cc_string_t</a> *out_name)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name()</a></b>: Get the name of the default ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_name</em>&nbsp;</td><td>on exit, the name of the default ccache. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
This function returns the name of the default ccache. When the default ccache exists, its name is returned. If there are no ccaches in the collection, and thus there is no default ccache, the name that the default ccache should have is returned. The ccache with that name will be used as the default ccache by all processes which initialized Kerberos libraries before the ccache was created.<p>
If there is no default ccache, and the client is creating a new ccache, it should be created with the default name. If there already is a default ccache, and the client wants to create a new ccache (as opposed to reusing an existing ccache), it should be created with any unique name; <a class="el" href="structcc__context__f.html#939a8ed76079bf71000347c40aeb5b2f">create_new_ccache()</a> can be used to accomplish that more easily.<p>
If the first ccache is created with a name other than the default name, then the processes already running will not notice the credentials stored in the new ccache, which is normally undesirable.     </td>
  </tr>
</table>
<a class="anchor" name="bf8e7415758b890ca8f01ce5a00985ab"></a><!-- doxytag: member="cc_context_f::open_ccache" ref="bf8e7415758b890ca8f01ce5a00985ab" args=")(cc_context_t in_context, const char *in_name, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#bf8e7415758b890ca8f01ce5a00985ab">open_ccache</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, const char *in_name, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g256a5ba17fe0e4502e0722d9b081bbef">cc_context_open_ccache()</a></b>: Open a ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_name</em>&nbsp;</td><td>the name of the ccache to open. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, a ccache object for the ccache </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. If no ccache named <em>in_name</em> exists, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b73098feac66058e6ebd02c5e44fa20a9c">ccErrCCacheNotFound</a>. On failure, an error code representing the failure.</dd></dl>
Opens an already existing ccache identified by its name. It returns a reference to the ccache in <em>out_ccache</em>.<p>
The list of all ccache names, principals, and credentials versions may be retrieved by calling cc_context_new_cache_iterator(), <a class="el" href="group__helper__macros.html#g042bea6044879ec03996b190792e3ae9">cc_ccache_get_name()</a>, <a class="el" href="group__helper__macros.html#g464aa49a2e8054c9c3c2a3410eaf5c54">cc_ccache_get_principal()</a>, and cc_ccache_get_cred_version().     </td>
  </tr>
</table>
<a class="anchor" name="7f101feaa1c88c8997b678507c029c39"></a><!-- doxytag: member="cc_context_f::open_default_ccache" ref="7f101feaa1c88c8997b678507c029c39" args=")(cc_context_t in_context, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#7f101feaa1c88c8997b678507c029c39">open_default_ccache</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g45a7ce29eb409baabadcae1bc95d5c57">cc_context_open_default_ccache()</a></b>: Open the default ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, a ccache object for the default ccache </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. If no default ccache exists, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b73098feac66058e6ebd02c5e44fa20a9c">ccErrCCacheNotFound</a>. On failure, an error code representing the failure.</dd></dl>
Opens the default ccache. It returns a reference to the ccache in *ccache.<p>
This function performs the same function as calling cc_context_get_default_ccache_name followed by cc_context_open_ccache, but it performs it atomically.     </td>
  </tr>
</table>
<a class="anchor" name="82d3579723a0f909cb46c2016ed4ae22"></a><!-- doxytag: member="cc_context_f::create_ccache" ref="82d3579723a0f909cb46c2016ed4ae22" args=")(cc_context_t in_context, const char *in_name, cc_uint32 in_cred_vers, const char *in_principal, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#82d3579723a0f909cb46c2016ed4ae22">create_ccache</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, const char *in_name, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a></b>: Create a new ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_name</em>&nbsp;</td><td>the name of the new ccache to create </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_cred_vers</em>&nbsp;</td><td>the version of the credentials the new ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_principal</em>&nbsp;</td><td>the client principal of the credentials the new ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, a ccache object for the newly created ccache </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Create a new credentials cache. The ccache is uniquely identified by its name. The principal given is also associated with the ccache and the credentials version specified. A NULL name is not allowed (and ccErrBadName is returned if one is passed in). Only cc_credentials_v4 and cc_credentials_v5 are valid input values for cred_vers. If you want to create a new ccache that will hold both versions of credentials, call <a class="el" href="group__helper__macros.html#g9fbcbd0f1b107cdaa2a0179e227f82cf">cc_context_create_ccache()</a> with one version, and then <a class="el" href="group__helper__macros.html#gfaa81492b5d7b3ba00208a9577ce0ba2">cc_ccache_set_principal()</a> with the other version.<p>
If you want to create a new ccache (with a unique name), you should use <a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a> instead. If you want to create or reinitialize the default cache, you should use <a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a>.<p>
If name is non-NULL and there is already a ccache named name:<p>
<ul>
<li>the credentials in the ccache whose version is cred_vers are removed </li>
<li>the principal (of the existing ccache) associated with cred_vers is set to principal </li>
<li>a handle for the existing ccache is returned and all existing handles for the ccache remain valid</li>
</ul>
If no ccache named name already exists:<p>
<ul>
<li>a new empty ccache is created </li>
<li>the principal of the new ccache associated with cred_vers is set to principal </li>
<li>a handle for the new ccache is returned</li>
</ul>
For a new ccache, the name should be any unique string. The name is not intended to be presented to users.<p>
If the created ccache is the first ccache in the collection, it is made the default ccache. Note that normally it is undesirable to create the first ccache with a name different from the default ccache name (as returned by <a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name()</a>); see the description of <a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name()</a> for details.<p>
The principal should be a C string containing an unparsed Kerberos principal in the format of the appropriate Kerberos version, i.e.<div class="fragment"><pre class="fragment">foo.bar/@BAZ 
      * </pre></div> for Kerberos v4 and<div class="fragment"><pre class="fragment">foo/bar/@BAZ </pre></div> for Kerberos v5.     </td>
  </tr>
</table>
<a class="anchor" name="b52a31c1fb59ac752baa16503d8e0e3f"></a><!-- doxytag: member="cc_context_f::create_default_ccache" ref="b52a31c1fb59ac752baa16503d8e0e3f" args=")(cc_context_t in_context, cc_uint32 in_cred_vers, const char *in_principal, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#b52a31c1fb59ac752baa16503d8e0e3f">create_default_ccache</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g10a184dd699cae4df6f3480290804a72">cc_context_create_default_ccache()</a></b>: Create a new default ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_cred_vers</em>&nbsp;</td><td>the version of the credentials the new default ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_principal</em>&nbsp;</td><td>the client principal of the credentials the new default ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, a ccache object for the newly created default ccache </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Create the default credentials cache. The behavior of this function is similar to that of cc_create_ccache(). If there is a default ccache (which is always the case except when there are no ccaches at all in the collection), it is initialized with the specified credentials version and principal, as per cc_create_ccache(); otherwise, a new ccache is created, and its name is the name returned by <a class="el" href="group__helper__macros.html#gcb4eb9f1db6f8ebf261339ad87cb6c51">cc_context_get_default_ccache_name()</a>.     </td>
  </tr>
</table>
<a class="anchor" name="939a8ed76079bf71000347c40aeb5b2f"></a><!-- doxytag: member="cc_context_f::create_new_ccache" ref="939a8ed76079bf71000347c40aeb5b2f" args=")(cc_context_t in_context, cc_uint32 in_cred_vers, const char *in_principal, cc_ccache_t *out_ccache)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#939a8ed76079bf71000347c40aeb5b2f">create_new_ccache</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_cred_vers, const char *in_principal, <a class="el" href="structcc__ccache__d.html">cc_ccache_t</a> *out_ccache)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g1a6dffb1db25590351646fdcf9824f09">cc_context_create_new_ccache()</a></b>: Create a new uniquely named ccache. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_cred_vers</em>&nbsp;</td><td>the version of the credentials the new ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_principal</em>&nbsp;</td><td>the client principal of the credentials the new ccache will hold </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_ccache</em>&nbsp;</td><td>on exit, a ccache object for the newly created ccache </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Create a new unique credentials cache. The behavior of this function is similar to that of cc_create_ccache(). If there are no ccaches, and therefore no default ccache, the new ccache is created with the default ccache name as would be returned by <a class="el" href="structcc__context__f.html#4e9432f5a1a10319037b0f04e8219c1b">get_default_ccache_name()</a>. If there are some ccaches, and therefore there is a default ccache, the new ccache is created with a new unique name. Clearly, this function never reinitializes a ccache, since it always uses a unique name.     </td>
  </tr>
</table>
<a class="anchor" name="0f945985d42255226915403df147667f"></a><!-- doxytag: member="cc_context_f::new_ccache_iterator" ref="0f945985d42255226915403df147667f" args=")(cc_context_t in_context, cc_ccache_iterator_t *out_iterator)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#0f945985d42255226915403df147667f">new_ccache_iterator</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="structcc__ccache__iterator__d.html">cc_ccache_iterator_t</a> *out_iterator)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g6957bc9570e4769a5b1213d2a1d90cd7">cc_context_new_ccache_iterator()</a></b>: Get an iterator for the cache collection. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_iterator</em>&nbsp;</td><td>on exit, a ccache iterator object for the ccache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Used to allocate memory and initialize iterator. Successive calls to iterator's next() function will return ccaches in the collection.<p>
If changes are made to the collection while an iterator is being used on it, the iterator must return at least the intersection, and at most the union, of the set of ccaches that were present when the iteration began and the set of ccaches that are present when it ends.     </td>
  </tr>
</table>
<a class="anchor" name="1be78f795193b04c4f45cb7d3c46480c"></a><!-- doxytag: member="cc_context_f::lock" ref="1be78f795193b04c4f45cb7d3c46480c" args=")(cc_context_t in_context, cc_uint32 in_lock_type, cc_uint32 in_block)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#1be78f795193b04c4f45cb7d3c46480c">lock</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_lock_type, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> in_block)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#gcf4595340ddc8dafa539a86ac317625d">cc_context_lock()</a></b>: Lock the cache collection. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_lock_type</em>&nbsp;</td><td>the type of lock to obtain. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_block</em>&nbsp;</td><td>whether or not the function should block if the lock cannot be obtained immediately. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
Attempts to acquire an advisory lock for the ccache collection. Allowed values for lock_type are:<p>
<ul>
<li>cc_lock_read: a read lock. </li>
<li>cc_lock_write: a write lock </li>
<li>cc_lock_upgrade: upgrade an already-obtained read lock to a write lock </li>
<li>cc_lock_downgrade: downgrade an already-obtained write lock to a read lock</li>
</ul>
If block is cc_lock_block, <a class="el" href="structcc__context__f.html#1be78f795193b04c4f45cb7d3c46480c">lock()</a> will not return until the lock is acquired. If block is cc_lock_noblock, <a class="el" href="structcc__context__f.html#1be78f795193b04c4f45cb7d3c46480c">lock()</a> will return immediately, either acquiring the lock and returning ccNoError, or failing to acquire the lock and returning an error explaining why.<p>
Locks apply only to the list of ccaches, not the contents of those ccaches. To prevent callers participating in the advisory locking from changing the credentials in a cache you must also lock that ccache with <a class="el" href="group__helper__macros.html#gb8c2624719ee1c4be5f1b1bc4844f0cc">cc_ccache_lock()</a>. This is so that you can get the list of ccaches without preventing applications from simultaneously obtaining service tickets.<p>
To avoid having to deal with differences between thread semantics on different platforms, locks are granted per context, rather than per thread or per process. That means that different threads of execution have to acquire separate contexts in order to be able to synchronize with each other.<p>
The lock should be unlocked by using <a class="el" href="group__helper__macros.html#g544261b88c9ac0f2379a35648cae3f27">cc_context_unlock()</a>.<p>
<dl compact><dt><b>Note:</b></dt><dd>All locks are advisory. For example, callers which do not call <a class="el" href="group__helper__macros.html#gcf4595340ddc8dafa539a86ac317625d">cc_context_lock()</a> and <a class="el" href="group__helper__macros.html#g544261b88c9ac0f2379a35648cae3f27">cc_context_unlock()</a> will not be prevented from writing to the cache collection when you have a read lock. This is because the CCAPI locking was added after the first release and thus adding mandatory locks would have changed the user experience and performance of existing applications. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="55e38bd72efee8445b3abbc3fa5e7e27"></a><!-- doxytag: member="cc_context_f::unlock" ref="55e38bd72efee8445b3abbc3fa5e7e27" args=")(cc_context_t in_cc_context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#55e38bd72efee8445b3abbc3fa5e7e27">unlock</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g544261b88c9ac0f2379a35648cae3f27">cc_context_unlock()</a></b>: Unlock the cache collection. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>the context object for the cache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="bb728ccd97eb387991feed0500475112"></a><!-- doxytag: member="cc_context_f::compare" ref="bb728ccd97eb387991feed0500475112" args=")(cc_context_t in_cc_context, cc_context_t in_compare_to_context, cc_uint32 *out_equal)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#bb728ccd97eb387991feed0500475112">compare</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context, <a class="el" href="structcc__context__d.html">cc_context_t</a> in_compare_to_context, <a class="el" href="group__ccapi__types__reference.html#ga00783c3f4aa70580d0900b1a79aab9d">cc_uint32</a> *out_equal)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g9258ef05d06f3d4dc798ec654f78b967">cc_context_compare()</a></b>: Compare two context objects. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>a context object. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>in_compare_to_context</em>&nbsp;</td><td>a context object to compare with <em>in_context</em>. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>out_equal</em>&nbsp;</td><td>on exit, whether or not the two contexts refer to the same cache collection. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure. </dd></dl>
    </td>
  </tr>
</table>
<a class="anchor" name="39a27ecd6d29fb7288f983c42d5686d0"></a><!-- doxytag: member="cc_context_f::wait_for_change" ref="39a27ecd6d29fb7288f983c42d5686d0" args=")(cc_context_t in_cc_context)" --><p>
<table class="mdTable" cellpadding="2" cellspacing="0">
  <tr>
    <td class="mdRow">
      <table cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td class="md" nowrap valign="top"><a class="el" href="group__ccapi__types__reference.html#g0ce639c8d65dc6367fb361d5bbcea874">cc_int32</a>(* <a class="el" href="structcc__context__f.html#39a27ecd6d29fb7288f983c42d5686d0">wait_for_change</a>)(<a class="el" href="structcc__context__d.html">cc_context_t</a> in_cc_context)          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
<table cellspacing="5" cellpadding="0" border="0">
  <tr>
    <td>
      &nbsp;
    </td>
    <td>

<p>
<b><a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a></b>: Wait for the next change in the cache collection. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>in_context</em>&nbsp;</td><td>a context object. </td></tr>
  </table>
</dl>
<dl compact><dt><b>Returns:</b></dt><dd>On success, <a class="el" href="group__ccapi__constants__reference.html#ggdf764cbdea00d65edcd07bb9953ad2b7386efd60970fd1740c97093a79558c26">ccNoError</a>. On failure, an error code representing the failure.</dd></dl>
This function blocks until the next change is made to the cache collection ccache collection. By repeatedly calling <a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a> from a worker thread the caller can effectively receive callbacks whenever the cache collection changes. This is considerably more efficient than polling with <a class="el" href="group__helper__macros.html#g82f551af17455b78fa3a2e3f83c96907">cc_context_get_change_time()</a>.<p>
<a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a> will return whenever:<p>
<ul>
<li>a ccache is created </li>
<li>a ccache is destroyed </li>
<li>a credential is stored </li>
<li>a credential is removed </li>
<li>a ccache principal is changed </li>
<li>the default ccache is changed</li>
</ul>
<dl compact><dt><b>Note:</b></dt><dd>In order to make sure that the caller doesn't miss any changes, <a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a> always returns immediately after the first time it is called on a new context object. Callers must use the same context object for successive calls to <a class="el" href="group__helper__macros.html#g9eb3508958528c00844a101275497e5a">cc_context_wait_for_change()</a> rather than creating a new context for every call.</dd></dl>
<dl compact><dt><b>See also:</b></dt><dd><a class="el" href="structcc__context__f.html#51bd5a48dcd263bfb3128cc5838b4cd7">get_change_time</a> </dd></dl>
    </td>
  </tr>
</table>
<hr size="1"><address style="align: right;"><small>Generated on Tue Oct 2 17:16:05 2007 for Credentials Cache API    by&nbsp;
<a href="http://www.doxygen.org/index.html">
<img src="doxygen.png" alt="doxygen" align="middle" border="0"></a> 1.4.6 </small></address>
</body>
</html>
