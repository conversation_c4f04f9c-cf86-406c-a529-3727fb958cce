krb5_encode_test runs through all the functions declared in
 src/include/krb5/asn.1/krb5_encode.h.  It passes various sample
 inputs to each function and prints the result to standard
 output.  This output should match the contents of the file
 "reference_encode.out".

 Each function is first run with a relatively simple, contrived
 sample structure.  Then if the structure has any optional parts,
 these parts are cleared and another run is made.

 Some structures (namely, those containing a krb5_kdc_req_body)
 have a third run, due to the fact that two of the kdc_req_body's
 optional fields have mutually exclusive conditions under which
 they may be omitted.


krb5_decode_test runs through all the functions declared in
 src/include/krb5/asn.1/krb5_decode.h.  It has the encodings in
 reference_encode.out hard-coded into itself.  It sets up the
 krb5 structures the same way krb5_encode_test does, then passes
 its hard-coded encoding strings through the krb5 decoders.
 
 The outputs of these functions are compared to the previously
 set-up structures in memory, and the results are reported to
 standard output.  If every line comes out prefixed by "OK: ",
 then the decoders are working properly.  If any decoder produces
 an anomalous output, then its output line will be prefixed by
 "ERROR: "
