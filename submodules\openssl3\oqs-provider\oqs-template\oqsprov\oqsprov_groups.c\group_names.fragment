{% set cnt = namespace(val=-1) %}
{% for kem in config['kems'] -%}
    {%- set cnt.val = cnt.val + 1 %}
    OQS_GROUP_ENTRY({{kem['name_group']}}, {{kem['name_group']}}, {{kem['name_group']}}, {{kem['bit_security']}}, {{ cnt.val }}),
    OQS_GROUP_ENTRY_ECP({{kem['name_group']}}, {{kem['name_group']}}, {{kem['name_group']}}, {{kem['bit_security']}}, {{ cnt.val }}),
{%- if 'nid_ecx_hybrid' in kem %}
    OQS_GROUP_ENTRY_ECX({{kem['name_group']}}, {{kem['name_group']}}, {{kem['name_group']}}, {{kem['bit_security']}}, {{ cnt.val }}),
{%- endif %}
{%- endfor %}

