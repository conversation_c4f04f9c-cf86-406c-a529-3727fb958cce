Development
===========

As an open source project, ``cryptography`` welcomes contributions of all
forms. The sections below will help you get started.

File bugs and feature requests on our issue tracker on `GitHub`_. If it is a
bug check out `what to put in your bug report`_.

.. toctree::
    :maxdepth: 2

    getting-started
    submitting-patches
    reviewing-patches
    test-vectors
    c-bindings

.. _`GitHub`: https://github.com/pyca/cryptography
.. _`what to put in your bug report`: https://www.contribution-guide.org/#what-to-put-in-your-bug-report
