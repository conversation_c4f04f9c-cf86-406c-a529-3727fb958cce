# Reformatted from https://www.schneier.com/wp-content/uploads/2015/12/vectors-2.txt
# to look like the NIST vectors

[ENCRYPT]

COUNT = 0
KEY = 0000000000000000
PLAINTEXT = 0000000000000000
CIPHERTEXT = 4EF997456198DD78

COUNT = 1
KEY = FFFFFFFFFFFFFFFF
PLAINTEXT = FFFFFFFFFFFFFFFF
CIPHERTEXT = 51866FD5B85ECB8A

COUNT = 2
KEY = 3000000000000000
PLAINTEXT = 1000000000000001
CIPHERTEXT = 7D856F9A613063F2

COUNT = 3
KEY = 1111111111111111
PLAINTEXT = 1111111111111111
CIPHERTEXT = 2466DD878B963C9D

COUNT = 4
KEY = 0123456789ABCDEF
PLAINTEXT = 1111111111111111
CIPHERTEXT = 61F9C3802281B096

COUNT = 5
KEY = 1111111111111111
PLAINTEXT = 0123456789ABCDEF
CIPHERTEXT = 7D0CC630AFDA1EC7

COUNT = 6
KEY = 0000000000000000
PLAINTEXT = 0000000000000000
CIPHERTEXT = 4EF997456198DD78

COUNT = 7
KEY = FEDCBA9876543210
PLAINTEXT = 0123456789ABCDEF
CIPHERTEXT = 0ACEAB0FC6A0A28D

COUNT = 8
KEY = 7CA110454A1A6E57
PLAINTEXT = 01A1D6D039776742
CIPHERTEXT = 59C68245EB05282B

COUNT = 9
KEY = 0131D9619DC1376E
PLAINTEXT = 5CD54CA83DEF57DA
CIPHERTEXT = B1B8CC0B250F09A0

COUNT = 10
KEY = 07A1133E4A0B2686
PLAINTEXT = 0248D43806F67172
CIPHERTEXT = 1730E5778BEA1DA4

COUNT = 11
KEY = 3849674C2602319E
PLAINTEXT = 51454B582DDF440A
CIPHERTEXT = A25E7856CF2651EB

COUNT = 12
KEY = 04B915BA43FEB5B6
PLAINTEXT = 42FD443059577FA2
CIPHERTEXT = 353882B109CE8F1A

COUNT = 13
KEY = 0113B970FD34F2CE
PLAINTEXT = 059B5E0851CF143A
CIPHERTEXT = 48F4D0884C379918

COUNT = 14
KEY = 0170F175468FB5E6
PLAINTEXT = 0756D8E0774761D2
CIPHERTEXT = 432193B78951FC98

COUNT = 15
KEY = 43297FAD38E373FE
PLAINTEXT = 762514B829BF486A
CIPHERTEXT = 13F04154D69D1AE5

COUNT = 16
KEY = 07A7137045DA2A16
PLAINTEXT = 3BDD119049372802
CIPHERTEXT = 2EEDDA93FFD39C79

COUNT = 17
KEY = 04689104C2FD3B2F
PLAINTEXT = 26955F6835AF609A
CIPHERTEXT = D887E0393C2DA6E3

COUNT = 18
KEY = 37D06BB516CB7546
PLAINTEXT = 164D5E404F275232
CIPHERTEXT = 5F99D04F5B163969

COUNT = 19
KEY = 1F08260D1AC2465E
PLAINTEXT = 6B056E18759F5CCA
CIPHERTEXT = 4A057A3B24D3977B

COUNT = 20
KEY = 584023641ABA6176
PLAINTEXT = 004BD6EF09176062
CIPHERTEXT = 452031C1E4FADA8E

COUNT = 21
KEY = 025816164629B007
PLAINTEXT = 480D39006EE762F2
CIPHERTEXT = 7555AE39F59B87BD

COUNT = 22
KEY = 49793EBC79B3258F
PLAINTEXT = 437540C8698F3CFA
CIPHERTEXT = 53C55F9CB49FC019

COUNT = 23
KEY = 4FB05E1515AB73A7
PLAINTEXT = 072D43A077075292
CIPHERTEXT = 7A8E7BFA937E89A3

COUNT = 24
KEY = 49E95D6D4CA229BF
PLAINTEXT = 02FE55778117F12A
CIPHERTEXT = CF9C5D7A4986ADB5

COUNT = 25
KEY = 018310DC409B26D6
PLAINTEXT = 1D9D5C5018F728C2
CIPHERTEXT = D1ABB290658BC778

COUNT = 26
KEY = 1C587F1C13924FEF
PLAINTEXT = 305532286D6F295A
CIPHERTEXT = 55CB3774D13EF201

COUNT = 27
KEY = 0101010101010101
PLAINTEXT = 0123456789ABCDEF
CIPHERTEXT = FA34EC4847B268B2

COUNT = 28
KEY = 1F1F1F1F0E0E0E0E
PLAINTEXT = 0123456789ABCDEF
CIPHERTEXT = A790795108EA3CAE

COUNT = 29
KEY = E0FEE0FEF1FEF1FE
PLAINTEXT = 0123456789ABCDEF
CIPHERTEXT = C39E072D9FAC631D

COUNT = 30
KEY = 0000000000000000
PLAINTEXT = FFFFFFFFFFFFFFFF
CIPHERTEXT = 014933E0CDAFF6E4

COUNT = 31
KEY = FFFFFFFFFFFFFFFF
PLAINTEXT = 0000000000000000
CIPHERTEXT = F21E9A77B71C49BC

COUNT = 32
KEY = 0123456789ABCDEF
PLAINTEXT = 0000000000000000
CIPHERTEXT = 245946885754369A

COUNT = 33
KEY = FEDCBA9876543210
PLAINTEXT = FFFFFFFFFFFFFFFF
CIPHERTEXT = 6B5C5A9C5D9E0A5A

# Blowfish is specified for key lengths 32-448 bit
# This an 8-bit key
# COUNT = 34
# KEY = F0
# PLAINTEXT = FEDCBA9876543210
# CIPHERTEXT = F9AD597C49DB005E

# This a 16-bit key
# COUNT = 35
# KEY = F0E1
# PLAINTEXT = FEDCBA9876543210
# CIPHERTEXT = E91D21C1D961A6D6

# This a 24-bit key
# COUNT = 36
# KEY = F0E1D2
# PLAINTEXT = FEDCBA9876543210
# CIPHERTEXT = E9C2B70A1BC65CF3

COUNT = 37
KEY = F0E1D2C3
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = BE1E639408640F05

COUNT = 38
KEY = F0E1D2C3B4
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = B39E44481BDB1E6E

COUNT = 39
KEY = F0E1D2C3B4A5
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 9457AA83B1928C0D

COUNT = 40
KEY = F0E1D2C3B4A596
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 8BB77032F960629D

COUNT = 41
KEY = F0E1D2C3B4A59687
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = E87A244E2CC85E82

COUNT = 42
KEY = F0E1D2C3B4A5968778
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 15750E7A4F4EC577

COUNT = 43
KEY = F0E1D2C3B4A596877869
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 122BA70B3AB64AE0

COUNT = 44
KEY = F0E1D2C3B4A5968778695A
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 3A833C9AFFC537F6

COUNT = 45
KEY = F0E1D2C3B4A5968778695A4B
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 9409DA87A90F6BF2

COUNT = 46
KEY = F0E1D2C3B4A5968778695A4B3C
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 884F80625060B8B4

COUNT = 47
KEY = F0E1D2C3B4A5968778695A4B3C2D
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 1F85031C19E11968

COUNT = 48
KEY = F0E1D2C3B4A5968778695A4B3C2D1E
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 79D9373A714CA34F

COUNT = 49
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 93142887EE3BE15C

COUNT = 50
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F00
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 03429E838CE2D14B

COUNT = 51
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F0011
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = A4299E27469FF67B

COUNT = 52
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F001122
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = AFD5AED1C1BC96A8

COUNT = 53
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F00112233
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 10851C0E3858DA9F

COUNT = 54
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F0011223344
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = E6F51ED79B9DB21F

COUNT = 55
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F001122334455
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 64A6E14AFD36B46F

COUNT = 56
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F00112233445566
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 80C7D7D45A5479AD

COUNT = 57
KEY = F0E1D2C3B4A5968778695A4B3C2D1E0F0011223344556677
PLAINTEXT = FEDCBA9876543210
CIPHERTEXT = 05044B62FA52D080
