// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#ifndef utilh
#define utilh
#include "common.h"
#ifdef _WIN32
#include <time.h>
#endif
#ifdef ANDROID
#include <android/log.h>
#include <unistd.h>
#endif
#include <stdint.h>

#include <thread>
#ifdef _WIN32
#define MAXBSIZE 131072  // 128K
#else
#define MAXBSIZE 4096
#endif

#ifdef _WIN32
#define poll(x, y, z) win32_poll(x, y, z)
static int inline win32_poll(struct pollfd* fds, unsigned int nfds, int timo) {
  struct timeval timeout, *toptr;
  fd_set ifds, ofds, efds, *ip, *op;
  unsigned int i;
  int rc;

  // Set up the file-descriptor sets in ifds, ofds and efds.
  FD_ZERO(&ifds);
  FD_ZERO(&ofds);
  FD_ZERO(&efds);
  for (i = 0, op = ip = 0; i < nfds; ++i) {
    fds[i].revents = 0;
    if (fds[i].events & (POLLIN | POLLPRI)) {
      ip = &ifds;
      FD_SET(fds[i].fd, ip);
    }
    if (fds[i].events & POLLOUT) {
      op = &ofds;
      FD_SET(fds[i].fd, op);
    }
    FD_SET(fds[i].fd, &efds);
  }

  // Set up the timeval structure for the timeout parameter
  if (timo < 0) {
    toptr = 0;
  } else {
    toptr = &timeout;
    timeout.tv_sec = timo / 1000;
    timeout.tv_usec = (timo - timeout.tv_sec * 1000) * 1000;
  }

#ifdef DEBUG_POLL
  printf("Entering select() sec=%ld usec=%ld ip=%lx op=%lx\n",
         (long)timeout.tv_sec, (long)timeout.tv_usec, (long)ip, (long)op);
#endif
  rc = select(0, ip, op, &efds, toptr);
#ifdef DEBUG_POLL
  printf("Exiting select rc=%d\n", rc);
#endif

  if (rc <= 0)
    return rc;

  if (rc > 0) {
    for (i = 0; i < nfds; ++i) {
      SOCKET fd = fds[i].fd;
      if (fds[i].events & (POLLIN | POLLPRI) && FD_ISSET(fd, &ifds))
        fds[i].revents |= POLLIN;
      if (fds[i].events & POLLOUT && FD_ISSET(fd, &ofds))
        fds[i].revents |= POLLOUT;
      if (FD_ISSET(fd, &efds))  // Some error was detected ... should be some
                                // way to know.
        fds[i].revents |= POLLERR;
#ifdef DEBUG_POLL
      printf("%d %d %d revent = %x\n", FD_ISSET(fd, &ifds), FD_ISSET(fd, &ofds),
             FD_ISSET(fd, &efds), fds[i].revents);
#endif
    }
  }
  return rc;
}
#endif

enum class SslErrcode {
  Success = 0,
  Unknow = 1,
  GeneratePkeyFailed = 2,
  GenerateX509Failed = 3,
};

enum log_level {
  LOG_NONE,
  LOG_ERROR,
  LOG_WARN,
  LOG_INFO,
  LOG_DEBUG,
  LOG_VERBOSE
};
static int gverbose = LOG_VERBOSE;  // LOG_VERBOSE;
static uint64_t inline mix(uint64_t seed, void* data, uint64_t size) {
  uint64_t acc = seed;
  for (uint64_t cursor = 0; cursor <= size;
       cursor += sizeof(char)) {  // XX we miss the tail for unaligned sizes
    acc += *(unsigned char*)data + cursor;
  }
  return (acc);
}

static int inline usleepFs(uint32_t time) {
#ifdef _WIN32
  std::this_thread::sleep_for(std::chrono::milliseconds(time / 1000));
  return 0;
#else
  usleep(time);
  return 0;
#endif
}
static int inline msleepFs(uint32_t time) {
#ifdef _WIN32
  std::this_thread::sleep_for(std::chrono::milliseconds(time));
  return 0;
#else
  usleep(time * 1000);
  return 0;
#endif
}
static uint64_t inline GetCurrentTimeFS() {
#ifdef _WIN32
  time_t clock;
  struct tm tm;
  SYSTEMTIME wtm;

  GetLocalTime(&wtm);
  tm.tm_year = wtm.wYear - 1900;
  tm.tm_mon = wtm.wMonth - 1;
  tm.tm_mday = wtm.wDay;
  tm.tm_hour = wtm.wHour;
  tm.tm_min = wtm.wMinute;
  tm.tm_sec = wtm.wSecond;
  tm.tm_isdst = -1;
  clock = mktime(&tm);

  return (uint64_t)((uint64_t)clock * 1000 + (uint64_t)wtm.wMilliseconds);
#else
  struct timeval tv;
  gettimeofday(&tv, NULL);
  return (uint64_t)((uint64_t)tv.tv_sec * 1000 + (uint64_t)tv.tv_usec / 1000);
#endif
}

#define whisper(level, ...)         \
  {                                 \
    if (level <= gverbose)          \
      fprintf(stderr, __VA_ARGS__); \
  }

#ifndef _WIN32
#define checkperror(...)                                              \
  do {                                                                \
    if (errno != 0) {                                                 \
      perror(__VA_ARGS__);                                            \
      whisper(LOG_ERROR, "checperror %s:%i", __FUNCTION__, __LINE__); \
    }                                                                 \
  } while (0);
#else
#define checkperror(...)
#endif
#endif
