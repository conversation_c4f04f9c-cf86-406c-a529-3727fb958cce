{"type": "object", "definitions": {"EcdhTestGroup": {"type": "object", "properties": {"type": {"enum": ["EcdhTest"]}, "tests": {"type": "array", "items": {"$ref": "#/definitions/EcdhTestVector"}}}}, "EcdhTestVector": {"type": "object", "properties": {"tcId": {"type": "integer", "description": "Identifier of the test case"}, "comment": {"type": "string", "description": "A brief description of the test case"}, "public": {"type": "string", "format": "<PERSON>n", "description": "Encoded public key"}, "private": {"type": "string", "format": "BigInt", "description": "the private key"}, "shared": {"type": "string", "format": "HexBytes", "description": "The shared secret key"}, "result": {"type": "string", "description": "Test result", "enum": ["valid", "invalid", "acceptable"]}, "flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags"}}}}, "properties": {"algorithm": {"type": "string", "description": "the primitive tested in the test file"}, "generatorVersion": {"type": "string", "description": "the version of the test vectors."}, "header": {"type": "array", "items": {"type": "string"}, "description": "additional documentation"}, "notes": {"type": "object", "description": "a description of the labels used in the test vectors"}, "numberOfTests": {"type": "integer", "description": "the number of test vectors in this test"}, "schema": {"enum": ["ecdh_test_schema.json"]}, "testGroups": {"type": "array", "items": {"$ref": "#/definitions/EcdhTestGroup"}}}}