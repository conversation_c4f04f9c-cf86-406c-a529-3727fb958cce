﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<TextAnalysisTool.NET version="2015-05-18" showOnlyFilteredLines="True">
  <filters>
    <filter enabled="y" excluding="n" foreColor="ff0000" type="matches_text" case_sensitive="n" regex="n" text="Assert" />
    <filter enabled="y" excluding="n" foreColor="ff0000" type="matches_text" case_sensitive="n" regex="n" text="ERROR, " />
    <filter enabled="y" excluding="n" foreColor="dc143c" type="matches_text" case_sensitive="n" regex="n" text="DROP packet" />
    <filter enabled="n" excluding="n" foreColor="ff1493" type="matches_text" case_sensitive="n" regex="n" text="[RX][" />
    <filter enabled="n" excluding="n" foreColor="006400" type="matches_text" case_sensitive="n" regex="n" text="[TX][" />
    <filter enabled="n" excluding="n" foreColor="6495ed" type="matches_text" case_sensitive="n" regex="n" text="[data" />
    <filter enabled="n" excluding="n" foreColor="ff0000" type="matches_text" case_sensitive="n" regex="n" text="[ api]" />
    <filter enabled="y" excluding="n" foreColor="ff00ff" type="matches_text" case_sensitive="n" regex="n" text="[conn]" />
    <filter enabled="n" excluding="n" foreColor="800080" type="matches_text" case_sensitive="n" regex="n" text="[strm]" />
    <filter enabled="y" excluding="n" foreColor="0000ff" type="matches_text" case_sensitive="n" regex="n" text="[list]" />
    <filter enabled="n" excluding="n" foreColor="b22222" type="matches_text" case_sensitive="n" regex="n" text="[cnfg]" />
    <filter enabled="n" excluding="n" foreColor="5f9ea0" type="matches_text" case_sensitive="n" regex="n" text="[ reg]" />
    <filter enabled="n" excluding="n" foreColor="6a5acd" type="matches_text" case_sensitive="n" regex="n" text="[bind]" />
    <filter enabled="y" excluding="n" type="matches_text" case_sensitive="n" regex="n" text="[test]" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Scheduling: " />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Execute: " />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="IN:" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="OUT:" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="CUBIC:" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Key Updated" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Updated Rtt=" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="loss detection " />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text=" UDP datagrams" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Send Blocked Flags:" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="QUIC_STREAM_EVENT_SEND_COMPLETE" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="QUIC_CONNECTION_EVENT_IDEAL_SEND_BUFFER" />
    <filter enabled="y" excluding="y" type="matches_text" case_sensitive="n" regex="n" text="Queueing send flush" />
  </filters>
</TextAnalysisTool.NET>