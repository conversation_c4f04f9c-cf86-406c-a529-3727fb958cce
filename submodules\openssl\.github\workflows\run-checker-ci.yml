---
name: Run-checker CI for 1.1.1
# Jobs run per pull request submission
on: [pull_request, push]
permissions:
  contents: read

jobs:
  run-checker:
    strategy:
      fail-fast: false
      matrix:
        opt: [
          no-cms,
          no-ct,
          no-dtls,
          no-ec,
          no-ec2m,
          no-sock,
          no-srp,
          no-srtp,
          enable-ssl-trace,
          no-tests,
          no-threads,
          no-tls,
          no-tls1_2,
          no-tls1_3,
          no-ts,
          no-ui,
        ]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: config
      run: CC=clang ./config --strict-warnings ${{ matrix.opt }}
    - name: config dump
      run: ./configdata.pm --dump
    - name: make
      run: make -s -j4
    - name: make test
      run: make test
