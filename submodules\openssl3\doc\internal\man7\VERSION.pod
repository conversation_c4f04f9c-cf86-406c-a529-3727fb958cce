=pod

=head1 NAME

VERSION - OpenSSL version information

=head1 SYNOPSIS

 MAJOR=3
 MINOR=0
 PATCH=0
 PRE_RELEASE_TAG=dev
 BUILD_METADATA=
 RELEASE_DATE=
 SHLIB_VERSION=3

=head1 DESCRIPTION

This file is a set of keyed information looking like simple variable
assignments.  When given an empty value, they are seen as unassigned.
The keys that are recognised are:

=over 4

=item B<MAJOR>, B<MINOR>, B<PATCH>

The three parts of OpenSSL's 3 numbered version number, MAJOR.MINOR.PATCH.
These are used to compose the values for the C macros B<OPENSSL_VERSION_MAJOR>,
B<OPENSSL_VERSION_MINOR>, B<OPENSSL_VERSION_PACTH>.

=item B<PRE_RELEASE_TAG>

This is the added pre-release tag, which is added to the version separated by
a dash.  For a value C<foo>, the C macro B<OPENSSL_VERSION_PRE_RELEASE> gets
the string C<-foo> (dash added).

=item B<BUILD_METADATA>

Extra metadata to be used by anyone for their own purposes.  This is added to
the version and possible pre-release tag, separated by a plus sign.  For a
value C<bar>, the C macro B<OPENSSL_VERSION_BUILD_METADATA> gets the string
C<+bar>.

=item B<RELEASE_DATE>

Defined in releases.  When not set, it gets the value C<xx XXX xxxx>.

=item B<SHLIB_VERSION>

The shared library version, which is something other than the project version.

=back

It is a configuration error if B<MAJOR>, B<MINOR>, B<PATCH> and B<SHLIB_VERSION>
don't have values.  Configuration will stop in that case.

=head2 Affected configuration data

The following items in %config from F<configdata.pm> are affected:

=over 4

=item $config{major}, $config{minor}, $config{patch}, $config{shlib_version}

These items get their values from B<MAJOR>, B<MINOR>, B<PATCH>, and
B<SHLIB_VERSION>, respectively.

=item $config{prerelease}

If B<PRERELEASE> is assigned a value, $config{prerelease} gets that same value,
prefixed by a dash, otherwise the empty string.

=item $config{build_metadata}

If B<BUILD_METADATA> is assigned a value, $config{build_metadata} gets that same
value, prefixed by a plus sign, otherwise the empty string.

=item $config{release_date}

If B<RELEASE_DATE> is assigned a value, $config{release_date} gets that same
value, otherwise the string C<xx XXX yyyy>.

=item $config{version}

The minimal version number, a string composed from B<MAJOR>, B<MINOR> and
B<PATCH>, separated by periods.  For C<MAJOR=3>, C<MINOR=0> and C<PATCH=0>,
the string will be C<3.0.0>.

=item $config{full_version}

The fully loaded version number, a string composed from $config{version},
$config{prerelease} and $config{build_metadata}.  See   See L</EXAMPLES> for
a few examples.

=back

=head1 EXAMPLES

=over 4

=item 1.

 MAJOR=3
 MINOR=0
 PATCH=0
 PRE_RELEASE_TAG=dev
 BUILD_METADATA=

The fully loaded version number ($config{full_version}) will be
C<3.0.0-dev>.

=item 2.

 MAJOR=3
 MINOR=0
 PATCH=0
 PRE_RELEASE_TAG=
 BUILD_METADATA=something

The fully loaded version number ($config{full_version}) will be
C<3.0.0+something>.

=item 3.

 MAJOR=3
 MINOR=0
 PATCH=0
 PRE_RELEASE_TAG=alpha3
 BUILD_METADATA=something

The fully loaded version number ($config{full_version}) will be
C<3.0.0-alpha3+something>.

=back

=head1 SEE ALSO

L<OpenSSL_version(3)>

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
