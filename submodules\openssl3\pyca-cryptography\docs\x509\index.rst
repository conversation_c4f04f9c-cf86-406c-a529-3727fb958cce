X.509
=====

X.509 is an ITU-T standard for a `public key infrastructure`_. X.509v3 is
defined in :rfc:`5280` (which obsoletes :rfc:`2459` and :rfc:`3280`). X.509
certificates are commonly used in protocols like `TLS`_.

.. toctree::
    :maxdepth: 2

    tutorial
    certificate-transparency
    ocsp
    reference

.. _`public key infrastructure`: https://en.wikipedia.org/wiki/Public_key_infrastructure
.. _`TLS`: https://en.wikipedia.org/wiki/Transport_Layer_Security
