# 
# Copyright 1993 by OpenVision Technologies, Inc.
# 
# Permission to use, copy, modify, distribute, and sell this software
# and its documentation for any purpose is hereby granted without fee,
# provided that the above copyright notice appears in all copies and
# that both that copyright notice and this permission notice appear in
# supporting documentation, and that the name of OpenVision not be used
# in advertising or publicity pertaining to distribution of the software
# without specific, written prior permission. OpenVision makes no
# representations about the suitability of this software for any
# purpose.  It is provided "as is" without express or implied warranty.
# 
# OPENVISION DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE,
# INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO
# EVENT SHALL OPENVISION BE LIABLE FOR ANY SPECIAL, INDIRECT OR
# CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF
# USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
# OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
# PERFORMANCE OF THIS SOFTWARE.
# 

error_table k5g

error_code KG_CCACHE_NOMATCH, "Principal in credential cache does not match desired name"
error_code KG_KEYTAB_NOMATCH, "No principal in keytab matches desired name"
error_code KG_TGT_MISSING, "Credential cache has no TGT"
error_code KG_NO_SUBKEY, "Authenticator has no subkey"
error_code KG_CONTEXT_ESTABLISHED, "Context is already fully established"
error_code KG_BAD_SIGN_TYPE, "Unknown signature type in token"
error_code KG_BAD_LENGTH, "Invalid field length in token"
error_code KG_CTX_INCOMPLETE, "Attempt to use incomplete security context"
error_code KG_CONTEXT, "Bad magic number for krb5_gss_ctx_id_t"
error_code KG_CRED, "Bad magic number for krb5_gss_cred_id_t"
error_code KG_ENC_DESC, "Bad magic number for krb5_gss_enc_desc"
error_code KG_BAD_SEQ, "Sequence number in token is corrupt"
error_code KG_EMPTY_CCACHE, "Credential cache is empty"
error_code KG_NO_CTYPES, "Acceptor and Initiator share no checksum types"
error_code KG_LUCID_VERSION, "Requested lucid context version not supported"
error_code KG_INPUT_TOO_LONG, "PRF input too long"
error_code KG_IAKERB_CONTEXT, "Bad magic number for iakerb_ctx_id_t"
end
