=pod

=head1 NAME

CONF_modules_free, CONF_modules_finish, CONF_modules_unload -
OpenSSL configuration cleanup functions

=head1 SYNOPSIS

 #include <openssl/conf.h>

 void CONF_modules_finish(void);
 void CONF_modules_unload(int all);

The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 void CONF_modules_free(void);

=head1 DESCRIPTION

CONF_modules_free() closes down and frees up all memory allocated by all
configuration modules.  Normally, in versions of OpenSSL prior to 1.1.0,
applications called
CONF_modules_free() at exit to tidy up any configuration performed.

CONF_modules_finish() calls each configuration modules B<finish> handler
to free up any configuration that module may have performed.

CONF_modules_unload() finishes and unloads configuration modules. If
B<all> is set to B<0> only modules loaded from DSOs will be unloads. If
B<all> is B<1> all modules, including built-in modules will be unloaded.

=head1 RETURN VALUES

None of the functions return a value.

=head1 SEE ALSO

L<config(5)>, L<OPENSSL_config(3)>,
L<CONF_modules_load_file_ex(3)>

=head1 HISTORY

CONF_modules_free() was deprecated in OpenSSL 1.1.0; do not use it.
For more information see L<OPENSSL_init_crypto(3)>.

=head1 COPYRIGHT

Copyright 2004-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
