/*++

    Copyright (c) Microsoft Corporation.
    Licensed under the MIT License.

 --*/

#ifndef _SAL_STUB_H
#define _SAL_STUB_H

#pragma once

//
// Necessary when SAL isn't supported to tell compiler it's not necessary.
//
#define INIT_NO_SAL(X) = X

#ifndef _Must_inspect_result_
#define _Must_inspect_result_
#endif

#ifndef _Pre_defensive_
#define _Pre_defensive_
#endif

#ifndef _Ret_notnull_
#define _Ret_notnull_
#endif

#ifndef _IRQL_requires_max_
#define _IRQL_requires_max_(...)
#endif

#ifndef _Function_class_
#define _Function_class_(...)
#endif

#ifndef _In_
#define _In_
#endif

#ifndef _In_opt_
#define _In_opt_
#endif

#ifndef _In_opt_z_
#define _In_opt_z_
#endif

#ifndef _Inout_
#define _Inout_
#endif

#ifndef _Inout_opt_
#define _Inout_opt_
#endif

#ifndef _In_z_
#define _In_z_
#endif

#ifndef _Out_
#define _Out_
#endif

#ifndef _Out_range_
#define _Out_range_(...)
#endif

#ifndef _Field_size_bytes_
#define _Field_size_bytes_(...)
#endif

#ifndef _Field_size_bytes_opt_
#define _Field_size_bytes_opt_(...)
#endif

#ifndef _In_reads_
#define _In_reads_(...)
#endif

#ifndef _In_reads_bytes_
#define _In_reads_bytes_(...)
#endif

#ifndef _In_reads_z_
#define _In_reads_z_(...)
#endif

#ifndef _In_reads_opt_z_
#define _In_reads_opt_z_(...)
#endif

#ifndef _In_reads_or_z_opt_
#define _In_reads_or_z_opt_(...)
#endif

#ifndef _Out_writes_bytes_opt_
#define _Out_writes_bytes_opt_(...)
#endif

#ifndef _Null_terminated_
#define _Null_terminated_
#endif

#ifndef _NullNull_terminated_
#define _NullNull_terminated_
#endif

#ifndef _Out_writes_bytes_
#define _Out_writes_bytes_(...)
#endif

#ifndef _Field_size_
#define _Field_size_(...)
#endif

#ifndef _Success_
#define _Success_(...)
#endif

#ifndef _Field_range_
#define _Field_range_(...)
#endif

#ifndef _In_reads_bytes_opt_
#define _In_reads_bytes_opt_(...)
#endif

#ifndef _Out_writes_bytes_to_opt_
#define _Out_writes_bytes_to_opt_(...)
#endif

#ifndef _Deref_pre_opt_count_
#define _Deref_pre_opt_count_(...)
#endif

#ifndef _Deref_post_opt_count_
#define _Deref_post_opt_count_(...)
#endif

#ifndef _Outptr_result_buffer_
#define _Outptr_result_buffer_(...)
#endif

#ifndef _Outptr_result_buffer_maybenull_
#define _Outptr_result_buffer_maybenull_(...)
#endif

#ifndef _Inout_updates_bytes_
#define _Inout_updates_bytes_(...)
#endif

#ifndef _Inout_updates_bytes_opt_
#define _Inout_updates_bytes_opt_(...)
#endif

#ifndef _Inout_updates_
#define _Inout_updates_(...)
#endif

#ifndef _Out_opt_
#define _Out_opt_
#endif

#ifndef _Outptr_
#define _Outptr_
#endif

#ifndef _Ret_maybenull_
#define _Ret_maybenull_
#endif

#ifndef _Must_inspect_result_
#define _Must_inspect_result_
#endif

#ifndef _Post_invalid_
#define _Post_invalid_
#endif

#ifndef _Post_writable_byte_size_
#define _Post_writable_byte_size_(...)
#endif

#ifndef __drv_allocatesMem
#define __drv_allocatesMem(...)
#endif

#ifndef __drv_freesMem
#define __drv_freesMem(...)
#endif

#ifndef __drv_aliasesMem
#define __drv_aliasesMem
#endif

#ifndef _Frees_ptr_
#define _Frees_ptr_
#endif

#ifndef _Frees_ptr_opt_
#define _Frees_ptr_opt_
#endif

#ifndef _In_range_
#define _In_range_(...)
#endif

#ifndef _When_
#define _When_(...)
#endif

#ifndef _Post_equal_to_
#define _Post_equal_to_(...)
#endif

#ifndef _Deref_in_range_
#define _Deref_in_range_(...)
#endif

#ifndef _Deref_out_range_
#define _Deref_out_range_(...)
#endif

#ifndef _Out_writes_all_
#define _Out_writes_all_(...)
#endif

#ifndef _Out_writes_to_
#define _Out_writes_to_(...)
#endif

#ifndef _Out_writes_
#define _Out_writes_(...)
#endif

#ifndef _Field_z_
#define _Field_z_
#endif

#ifndef __analysis_assume
#define __analysis_assume(expr)
#endif

#ifndef _Out_writes_bytes_all_
#define _Out_writes_bytes_all_(...)
#endif

#ifndef _Analysis_assume_
#define _Analysis_assume_(expr)
#endif

#ifndef _Ret_range_
#define _Ret_range_(...)
#endif

#ifndef _Ret_writes_bytes_
#define _Ret_writes_bytes_(...)
#endif

#ifndef _Printf_format_string_
#define _Printf_format_string_
#endif

#ifndef _Interlocked_operand_
#define _Interlocked_operand_
#endif

#ifndef _In_reads_opt_
#define _In_reads_opt_(...)
#endif

#ifndef _At_
#define _At_(...)
#endif

#ifndef _Check_return_
#define _Check_return_
#endif

#endif // _SAL_STUB_H
