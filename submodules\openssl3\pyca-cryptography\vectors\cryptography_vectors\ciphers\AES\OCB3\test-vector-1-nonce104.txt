# Vectors https://gitlab.com/dkg/ocb-test-vectors/-/blob/ec0131616679f588c3be0ac7c33b7a663e1a47d4/test-vector-1-nonce104.txt
# 104-bit nonce forms of RFC 7253
# Reformatted to work with our NIST loader

COUNT = 0
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221100
AAD =
Plaintext =
Ciphertext = 1AF957957B85C3D7F6CA08C7C5FC8F4A

COUNT = 1
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221101
AAD = 0001020304050607
Plaintext =  0001020304050607
Ciphertext = F4132F7B364D13A2303ACE52DDF90774E24E3E8895AC7F88

COUNT = 2
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221102
AAD = 0001020304050607
Plaintext =
Ciphertext = 20E9B13D02F7B19AFDC4659344960BED

COUNT = 3
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221103
AAD =
Plaintext =  0001020304050607
Ciphertext = 4A2A7E6D7A0A0EA4D652CC24F2208986E47B3251A66B5944

COUNT = 4
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221104
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext =  000102030405060708090A0B0C0D0E0F
Ciphertext = EEED2C9E7CFA9580551B03DCDB2E1DFA4A60E8225633281B98173DD6F1F1A57F

COUNT = 5
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221105
AAD = 000102030405060708090A0B0C0D0E0F
Plaintext =
Ciphertext = 19BAE49F721302071167C34E02A8BE9B

COUNT = 6
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221106
AAD =
Plaintext =  000102030405060708090A0B0C0D0E0F
Ciphertext = E254116668AEC1D2663E3E9B914AC47D0337401A0B16E4605B94A2C45F0F53CB

COUNT = 7
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221107
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext =  000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = 0C0299BEE2D5B65CBC82A6EE119543B7B89DE85561D149BFC1CBE6EC8749065C6068E046FB2BA7F7

COUNT = 8
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221108
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext =
Ciphertext = F4E603C017B49123CD3EEBF0F342DE31

COUNT = 9
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA99887766554433221109
AAD =
Plaintext =  000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = 9A2D75CA34639FF92CACDD3A881ED446B0E790D719A9DFD680C97FAE8ECE18A03A4C67DC1C0763B6

COUNT = 10
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110A
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext =  000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 0FEBCFA18BF3D2C4C155266755817F843DFA5A5CFD8987D87BE45F3669599D66B2D98602565E18AC31AD88C7C51A6988

COUNT = 11
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext =
Ciphertext = 6F48A1E1F0D43023CFA84F4143E286B8

COUNT = 12
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110C
AAD =
Plaintext =  000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 1DF09EC159EB74B3B4AC4B440D2D382ADE25D3A26D9A2A2EDCF23F41002FB7EB53417D8AFED547BFD54056BC9EA9C590

COUNT = 13
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110D
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext =  000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 3BCFFDEAC246CB305D367A25489ACE1F8FF0317260401B9E1A08DA6C11A0CC490871BD1A5E4FA29FF0E0A7326F0F871583AA3FAE224DD5EB

COUNT = 14
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110E
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Plaintext =
Ciphertext = FA7B7AC1ABD4097F4D547BE9FD5D0BB2

COUNT = 15
Key = 000102030405060708090A0B0C0D0E0F
Nonce = CCBBAA9988776655443322110F
AAD =
Plaintext =  000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = C8B16DA1C0AC72E4C993AC75EBD800D0ECDC45EE9DDC2D70C74BA3E4EFCC98A9DF77F8560D7EB2C33EDE4A46D9A2AD0CCAEB653BB6D38725
