=pod

=head1 NAME

EVP_desx_cbc
- EVP DES-X cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_desx_cbc(void);

=head1 DESCRIPTION

The DES-X encryption algorithm for EVP.

All modes below use a key length of 128 bits and acts on blocks of 128-bits.

=over 4

=item EVP_desx_cbc()

The DES-X algorithm in CBC mode.

This algorithm is not provided by the OpenSSL default provider.
To use it is necessary to load either the OpenSSL legacy provider or another
implementation.

=back

Developers should be aware of the negative performance implications of
calling this function multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-DES(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

