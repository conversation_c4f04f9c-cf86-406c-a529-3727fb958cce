/* Copyright (C) 1995-1998 <PERSON> (<EMAIL>)
 * All rights reserved.
 *
 * This package is an SSL implementation written
 * by <PERSON> (<EMAIL>).
 * The implementation was written so as to conform with Netscapes SSL.
 *
 * This library is free for commercial and non-commercial use as long as
 * the following conditions are aheared to.  The following conditions
 * apply to all code found in this distribution, be it the RC4, RSA,
 * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
 * included with this distribution is covered by the same copyright terms
 * except that the holder is <PERSON> (<EMAIL>).
 *
 * Copyright remains <PERSON>'s, and as such any Copyright notices in
 * the code are not to be removed.
 * If this package is used in a product, <PERSON> should be given attribution
 * as the author of the parts of the library used.
 * This can be in the form of a textual message at program startup or
 * in documentation (online or textual) provided with the package.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *    "This product includes cryptographic software written by
 *     <PERSON> (<EMAIL>)"
 *    The word 'cryptographic' can be left out if the rouines from the library
 *    being used are not cryptographic related :-).
 * 4. If you include any Windows specific code (or a derivative thereof) from
 *    the apps directory (application code) you must include an acknowledgement:
 *    "This product includes software written by Tim Hudson (<EMAIL>)"
 *
 * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 * The licence and distribution terms for any publically available version or
 * derivative of this code cannot be changed.  i.e. this code cannot simply be
 * copied and put under another distribution licence
 * [including the GNU Public Licence.] */

#if !defined(__STDC_FORMAT_MACROS)
#define __STDC_FORMAT_MACROS
#endif

#include <openssl/cpu.h>


#if !defined(OPENSSL_NO_ASM) && (defined(OPENSSL_X86) || defined(OPENSSL_X86_64))

#include <inttypes.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#if defined(OPENSSL_WINDOWS)
OPENSSL_MSVC_PRAGMA(warning(push, 3))
#include <immintrin.h>
#include <intrin.h>
OPENSSL_MSVC_PRAGMA(warning(pop))
#endif

#include "internal.h"


/* OPENSSL_cpuid runs the cpuid instruction. |leaf| is passed in as EAX and ECX
 * is set to zero. It writes EAX, EBX, ECX, and EDX to |*out_eax| through
 * |*out_edx|. */
static void OPENSSL_cpuid(uint32_t *out_eax, uint32_t *out_ebx,
                          uint32_t *out_ecx, uint32_t *out_edx, uint32_t leaf) {
#if defined(OPENSSL_WINDOWS)
  int tmp[4];
  __cpuid(tmp, (int)leaf);
  *out_eax = (uint32_t)tmp[0];
  *out_ebx = (uint32_t)tmp[1];
  *out_ecx = (uint32_t)tmp[2];
  *out_edx = (uint32_t)tmp[3];
#elif defined(__pic__) && defined(OPENSSL_32_BIT)
  /* Inline assembly may not clobber the PIC register. For 32-bit, this is EBX.
   * See https://gcc.gnu.org/bugzilla/show_bug.cgi?id=47602. */
  __asm__ volatile (
    "xor %%ecx, %%ecx\n"
    "mov %%ebx, %%edi\n"
    "cpuid\n"
    "xchg %%edi, %%ebx\n"
    : "=a"(*out_eax), "=D"(*out_ebx), "=c"(*out_ecx), "=d"(*out_edx)
    : "a"(leaf)
  );
#else
  __asm__ volatile (
    "xor %%ecx, %%ecx\n"
    "cpuid\n"
    : "=a"(*out_eax), "=b"(*out_ebx), "=c"(*out_ecx), "=d"(*out_edx)
    : "a"(leaf)
  );
#endif
}

/* OPENSSL_xgetbv returns the value of an Intel Extended Control Register (XCR).
 * Currently only XCR0 is defined by Intel so |xcr| should always be zero. */
static uint64_t OPENSSL_xgetbv(uint32_t xcr) {
#if defined(OPENSSL_WINDOWS)
  return (uint64_t)_xgetbv(xcr);
#else
  uint32_t eax, edx;
  __asm__ volatile ("xgetbv" : "=a"(eax), "=d"(edx) : "c"(xcr));
  return (((uint64_t)edx) << 32) | eax;
#endif
}

/* handle_cpu_env applies the value from |in| to the CPUID values in |out[0]|
 * and |out[1]|. See the comment in |OPENSSL_cpuid_setup| about this. */
static void handle_cpu_env(uint32_t *out, const char *in) {
  const int invert = in[0] == '~';
  uint64_t v;

  if (!sscanf(in + invert, "%" PRIu64, &v)) {
    return;
  }

  if (invert) {
    out[0] &= ~v;
    out[1] &= ~(v >> 32);
  } else {
    out[0] = v;
    out[1] = v >> 32;
  }
}

void OPENSSL_cpuid_setup(void) {
  /* Determine the vendor and maximum input value. */
  uint32_t eax, ebx, ecx, edx;
  OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 0);

  uint32_t num_ids = eax;

  int is_intel = ebx == 0x756e6547 /* Genu */ &&
                 edx == 0x49656e69 /* ineI */ &&
                 ecx == 0x6c65746e /* ntel */;
  int is_amd = ebx == 0x68747541 /* Auth */ &&
               edx == 0x69746e65 /* enti */ &&
               ecx == 0x444d4163 /* cAMD */;

  int has_amd_xop = 0;
  if (is_amd) {
    /* AMD-specific logic.
     * See http://developer.amd.com/wordpress/media/2012/10/254811.pdf */
    OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 0x80000000);
    uint32_t num_extended_ids = eax;
    if (num_extended_ids >= 0x80000001) {
      OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 0x80000001);
      if (ecx & (1 << 11)) {
        has_amd_xop = 1;
      }
    }
  }

  uint32_t extended_features = 0;
  if (num_ids >= 7) {
    OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 7);
    extended_features = ebx;
  }

  /* Determine the number of cores sharing an L1 data cache to adjust the
   * hyper-threading bit. */
  uint32_t cores_per_cache = 0;
  if (is_amd) {
    /* AMD CPUs never share an L1 data cache between threads but do set the HTT
     * bit on multi-core CPUs. */
    cores_per_cache = 1;
  } else if (num_ids >= 4) {
    /* TODO(davidben): The Intel manual says this CPUID leaf enumerates all
     * caches using ECX and doesn't say which is first. Does this matter? */
    OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 4);
    cores_per_cache = 1 + ((eax >> 14) & 0xfff);
  }

  OPENSSL_cpuid(&eax, &ebx, &ecx, &edx, 1);

  /* Adjust the hyper-threading bit. */
  if (edx & (1 << 28)) {
    uint32_t num_logical_cores = (ebx >> 16) & 0xff;
    if (cores_per_cache == 1 || num_logical_cores <= 1) {
      edx &= ~(1 << 28);
    }
  }

  /* Reserved bit #20 was historically repurposed to control the in-memory
   * representation of RC4 state. Always set it to zero. */
  edx &= ~(1 << 20);

  /* Reserved bit #30 is repurposed to signal an Intel CPU. */
  if (is_intel) {
    edx |= (1 << 30);
  } else {
    edx &= ~(1 << 30);
  }

  /* The SDBG bit is repurposed to denote AMD XOP support. */
  if (has_amd_xop) {
    ecx |= (1 << 11);
  } else {
    ecx &= ~(1 << 11);
  }

  uint64_t xcr0 = 0;
  if (ecx & (1 << 27)) {
    /* XCR0 may only be queried if the OSXSAVE bit is set. */
    xcr0 = OPENSSL_xgetbv(0);
  }
  /* See Intel manual, section 14.3. */
  if ((xcr0 & 6) != 6) {
    /* YMM registers cannot be used. */
    ecx &= ~(1 << 28); /* AVX */
    ecx &= ~(1 << 12); /* FMA */
    ecx &= ~(1 << 11); /* AMD XOP */
    extended_features &= ~(1 << 5); /* AVX2 */
  }

  OPENSSL_ia32cap_P[0] = edx;
  OPENSSL_ia32cap_P[1] = ecx;
  OPENSSL_ia32cap_P[2] = extended_features;
  OPENSSL_ia32cap_P[3] = 0;

  const char *env1, *env2;
  env1 = getenv("OPENSSL_ia32cap");
  if (env1 == NULL) {
    return;
  }

  /* OPENSSL_ia32cap can contain zero, one or two values, separated with a ':'.
   * Each value is a 64-bit, unsigned value which may start with "0x" to
   * indicate a hex value. Prior to the 64-bit value, a '~' may be given.
   *
   * If '~' isn't present, then the value is taken as the result of the CPUID.
   * Otherwise the value is inverted and ANDed with the probed CPUID result.
   *
   * The first value determines OPENSSL_ia32cap_P[0] and [1]. The second [2]
   * and [3]. */

  handle_cpu_env(&OPENSSL_ia32cap_P[0], env1);
  env2 = strchr(env1, ':');
  if (env2 != NULL) {
    handle_cpu_env(&OPENSSL_ia32cap_P[2], env2 + 1);
  }
}

#endif  /* !OPENSSL_NO_ASM && (OPENSSL_X86 || OPENSSL_X86_64) */
