KerberosV<PERSON><PERSON>MMAC DEFINITIONS EXPLICIT TAGS ::= BEGIN

IMPORTS
      AuthorizationData, PrincipalName, Checksum, UInt32, Int32
        FROM KerberosV5Spec2 { iso(1) identified-organization(3)
          dod(6) internet(1) security(5) kerberosV5(2)
          modules(4) krb5spec2(2) };
          -- as defined in RFC 4120.

AD-CAMMAC                   ::= SEQUENCE {
      elements              [0] AuthorizationData,
      kdc-verifier          [1] Verifier-MAC OPTIONAL,
      svc-verifier          [2] Verifier-MAC OPTIONAL,
      other-verifiers       [3] SEQUENCE (SIZE (1..MAX))
                                OF Verifier OPTIONAL
}

Verifier             ::= CHOICE {
      mac            Verifier-MAC,
      ...
}

Verifier-MAC         ::= SEQUENCE {
      identifier     [0] PrincipalName OPTIONAL,
      kvno           [1] UInt32 OPTIONAL,
      enctype        [2] Int32 OPTIONAL,
      mac            [3] Checksum
}

END
