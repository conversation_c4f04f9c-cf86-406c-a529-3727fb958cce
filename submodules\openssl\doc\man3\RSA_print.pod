=pod

=head1 NAME

RSA_print, RSA_print_fp,
DSAparams_print, DSAparams_print_fp, DSA_print, DSA_print_fp,
DHparams_print, DHparams_print_fp - print cryptographic parameters

=head1 SYNOPSIS

 #include <openssl/rsa.h>

 int RSA_print(BIO *bp, RSA *x, int offset);
 int RSA_print_fp(FILE *fp, RSA *x, int offset);

 #include <openssl/dsa.h>

 int DSAparams_print(BIO *bp, DSA *x);
 int DSAparams_print_fp(FILE *fp, DSA *x);
 int DSA_print(BIO *bp, DSA *x, int offset);
 int DSA_print_fp(FILE *fp, DSA *x, int offset);

 #include <openssl/dh.h>

 int DHparams_print(BIO *bp, DH *x);
 int DHparams_print_fp(FILE *fp, DH *x);

=head1 DESCRIPTION

A human-readable hexadecimal output of the components of the RSA
key, DSA parameters or key or DH parameters is printed to B<bp> or B<fp>.

The output lines are indented by B<offset> spaces.

=head1 RETURN VALUES

These functions return 1 on success, 0 on error.

=head1 SEE ALSO

L<BN_bn2bin(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
