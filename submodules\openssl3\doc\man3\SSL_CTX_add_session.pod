=pod

=head1 NAME

SSL_CTX_add_session, SSL_CTX_remove_session - manipulate session cache

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_CTX_add_session(SSL_CTX *ctx, SSL_SESSION *c);

 int SSL_CTX_remove_session(SSL_CTX *ctx, SSL_SESSION *c);

=head1 DESCRIPTION

SSL_CTX_add_session() adds the session B<c> to the context B<ctx>. The
reference count for session B<c> is incremented by 1. If a session with
the same session id already exists, the old session is removed by calling
L<SSL_SESSION_free(3)>.

SSL_CTX_remove_session() removes the session B<c> from the context B<ctx> and
marks it as non-resumable. L<SSL_SESSION_free(3)> is called once for B<c>.

=head1 NOTES

When adding a new session to the internal session cache, it is examined
whether a session with the same session id already exists. In this case
it is assumed that both sessions are identical. If the same session is
stored in a different SSL_SESSION object, The old session is
removed and replaced by the new session. If the session is actually
identical (the SSL_SESSION object is identical), SSL_CTX_add_session()
is a no-op, and the return value is 0.

If a server SSL_CTX is configured with the SSL_SESS_CACHE_NO_INTERNAL_STORE
flag then the internal cache will not be populated automatically by new
sessions negotiated by the SSL/TLS implementation, even though the internal
cache will be searched automatically for session-resume requests (the
latter can be suppressed by SSL_SESS_CACHE_NO_INTERNAL_LOOKUP). So the
application can use SSL_CTX_add_session() directly to have full control
over the sessions that can be resumed if desired.


=head1 RETURN VALUES

The following values are returned by all functions:

=over 4

=item Z<>0

The operation failed. In case of the add operation, it was tried to add
the same (identical) session twice. In case of the remove operation, the
session was not found in the cache.

=item Z<>1

The operation succeeded.

=back

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_session_cache_mode(3)>,
L<SSL_SESSION_free(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
