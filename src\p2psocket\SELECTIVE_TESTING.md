# P2P Socket 选择性测试指南

## 概述

现在您可以选择性地运行特定的测试，而不是一次运行所有测试。这样可以更容易地定位和调试问题。

## 使用方法

### 运行单个测试
```bash
# 只运行基础操作测试
testp2psocket.exe -test basic

# 只运行无效参数测试
testp2psocket.exe -test invalid

# 只运行绑定测试
testp2psocket.exe -test binding
```

### 可用的测试选项

| 测试名称 | 命令 | 描述 |
|---------|------|------|
| 基础操作 | `-test basic` | Socket 创建、关闭等基础操作 |
| 无效参数 | `-test invalid` | 测试 null 指针和无效参数处理 |
| Socket 绑定 | `-test binding` | 端口绑定、监听、获取本地端口 |
| Socket 设置 | `-test settings` | 超时设置、发送/读取模式设置 |
| 轮询操作 | `-test polling` | P2pPoll 功能测试 |
| 连接测试 | `-test connection` | 服务器-客户端连接和数据传输 |
| 向量化 I/O | `-test vectored` | P2pWritev 向量化写入测试 |
| 多客户端 | `-test multiclient` | 一个服务器处理多个客户端 |
| Socket 类型 | `-test types` | 测试不同的 socket 类型 |
| 边界条件 | `-test edge` | 边界条件和异常情况 |
| 所有测试 | `-test all` | 运行所有测试（默认行为） |

## 推荐的测试顺序

### 1. 从最简单的开始
```bash
# 第一步：测试基础操作
testp2psocket.exe -test basic
```
这个测试包括：
- 证书创建
- Socket 创建
- Socket 关闭

### 2. 测试参数验证
```bash
# 第二步：测试无效参数处理
testp2psocket.exe -test invalid
```
这个测试验证 API 对错误输入的处理。

### 3. 测试网络功能
```bash
# 第三步：测试绑定功能
testp2psocket.exe -test binding

# 第四步：测试设置功能
testp2psocket.exe -test settings

# 第五步：测试轮询功能
testp2psocket.exe -test polling
```

### 4. 测试连接功能
```bash
# 第六步：测试基本连接
testp2psocket.exe -test connection
```
这是第一个涉及真实网络连接的测试。

### 5. 测试高级功能
```bash
# 第七步：测试向量化 I/O
testp2psocket.exe -test vectored

# 第八步：测试多客户端
testp2psocket.exe -test multiclient
```

### 6. 测试兼容性
```bash
# 第九步：测试不同 socket 类型
testp2psocket.exe -test types

# 第十步：测试边界条件
testp2psocket.exe -test edge
```

## 调试策略

### 如果某个测试失败
1. **查看详细输出**: 测试会显示具体的失败信息
2. **检查前置条件**: 确保之前的测试都通过了
3. **隔离问题**: 只运行失败的测试来专注调试

### 示例调试流程
```bash
# 假设 connection 测试失败
testp2psocket.exe -test connection

# 如果失败，先确保基础功能正常
testp2psocket.exe -test basic
testp2psocket.exe -test binding
testp2psocket.exe -test settings

# 然后重新测试连接功能
testp2psocket.exe -test connection
```

## 组合使用

### 测试特定 socket 类型
```bash
# 测试 QUIC 的基础操作
testp2psocket.exe -test basic -type 3

# 测试 SSL 的连接功能
testp2psocket.exe -test connection -type 2

# 测试 MULTITCP 的多客户端
testp2psocket.exe -test multiclient -type 1
```

### 使用不同端口
```bash
# 避免端口冲突
testp2psocket.exe -test connection -port 5000
testp2psocket.exe -test multiclient -port 5001
```

## 输出解读

### 成功的测试输出
```
[TEST] === Testing Basic Socket Operations ===
[INFO] PASS: Certificate creation
[INFO] PASS: Socket creation
[INFO] PASS: Socket close
[TEST] === Test Summary ===
[TEST] Total tests: 3
[TEST] Passed: 3
[TEST] Failed: 0
[TEST] All tests passed!
```

### 失败的测试输出
```
[TEST] === Testing Basic Socket Operations ===
[INFO] PASS: Certificate creation
[ERROR] FAIL: Socket creation - socket != nullptr
[TEST] === Test Summary ===
[TEST] Total tests: 2
[TEST] Passed: 1
[TEST] Failed: 1
[TEST] Failures:
[TEST]   FAIL: Socket creation - socket != nullptr
[TEST] Some tests failed!
```

## 常见问题

### Q: 如何知道哪个测试失败了？
A: 使用选择性测试，一个一个地运行，直到找到失败的测试。

### Q: 测试之间有依赖关系吗？
A: 大部分测试是独立的，但建议按推荐顺序运行，因为后面的测试通常依赖前面测试验证的基础功能。

### Q: 可以同时运行多个测试吗？
A: 目前不支持，但您可以使用 `-test all` 运行所有测试。

### Q: 如何重复运行失败的测试？
A: 直接重新运行相同的命令即可，测试是可重复的。

## 总结

选择性测试让您能够：
1. **快速定位问题**: 只运行相关的测试
2. **节省时间**: 不需要等待所有测试完成
3. **专注调试**: 集中精力解决特定问题
4. **验证修复**: 快速验证问题是否已解决

建议从 `basic` 测试开始，逐步进行到更复杂的测试。
