=pod

=head1 NAME

SSL_clear - reset SSL object to allow another connection

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_clear(SSL *ssl);

=head1 DESCRIPTION

Reset B<ssl> to allow another connection. All settings (method, ciphers,
BIOs) are kept.

=head1 NOTES

SSL_clear is used to prepare an SSL object for a new connection. While all
settings are kept, a side effect is the handling of the current SSL session.
If a session is still B<open>, it is considered bad and will be removed
from the session cache, as required by RFC2246. A session is considered open,
if L<SSL_shutdown(3)> was not called for the connection
or at least L<SSL_set_shutdown(3)> was used to
set the SSL_SENT_SHUTDOWN state.

If a session was closed cleanly, the session object will be kept and all
settings corresponding. This explicitly means, that e.g. the special method
used during the session will be kept for the next handshake. So if the
session was a TLSv1 session, a SSL client object will use a TLSv1 client
method for the next handshake and a SSL server object will use a TLSv1
server method, even if TLS_*_methods were chosen on startup. This
will might lead to connection failures (see L<SSL_new(3)>)
for a description of the method's properties.

=head1 WARNINGS

SSL_clear() resets the SSL object to allow for another connection. The
reset operation however keeps several settings of the last sessions
(some of these settings were made automatically during the last
handshake). It only makes sense for a new connection with the exact
same peer that shares these settings, and may fail if that peer
changes its settings between connections. Use the sequence
L<SSL_get_session(3)>;
L<SSL_new(3)>;
L<SSL_set_session(3)>;
L<SSL_free(3)>
instead to avoid such failures
(or simply L<SSL_free(3)>; L<SSL_new(3)>
if session reuse is not desired).

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item Z<>0

The SSL_clear() operation could not be performed. Check the error stack to
find out the reason.

=item Z<>1

The SSL_clear() operation was successful.

=back

L<SSL_new(3)>, L<SSL_free(3)>,
L<SSL_shutdown(3)>, L<SSL_set_shutdown(3)>,
L<SSL_CTX_set_options(3)>, L<ssl(7)>,
L<SSL_CTX_set_client_cert_cb(3)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
