Fingerprints for Signing Releases

OpenSSL releases are signed with PGP/GnuPG keys.  This file contains
the fingerprints of team members who are "authorized" to sign the
next release.

The signature is a detached cleartxt signature, with the same name
as the release but with ".asc" appended.  For example, release
1.0.1h can be found in openssl-1.0.1h.tar.gz with the signature
in the file named openssl-1.0.1h.tar.gz.asc.

The following is the list of fingerprints for the keys that are
currently in use to sign OpenSSL distributions:

pub   rsa4096 2014-10-04
      Key fingerprint = EFC0 A467 D613 CB83 C7ED  6D30 D894 E2CE 8B3D 79F5
uid                  OpenSSL OMC <<EMAIL>>
uid                  OpenSSL Security <<EMAIL>>

pub   4096R/7DF9EE8C 2014-10-04
      Key fingerprint = 7953 AC1F BC3D C8B3 B292  393E D5E9 E43F 7DF9 EE8C
uid                  <PERSON> <<EMAIL>>
uid                  <PERSON> <<EMAIL>>
uid                  <PERSON> <<EMAIL>>

pub   2048R/0E604491 2013-04-30
      Key fingerprint = 8657 ABB2 60F0 56B1 E519 0839 D9C4 D26D 0E60 4491
uid                  Matt Caswell <<EMAIL>>
uid                  Matt Caswell <<EMAIL>>

pub   rsa4096 2021-02-14
      B7C1 C143 60F3 53A3 6862  E4D5 231C 84CD DCC6 9C45
uid                  <PERSON> <<EMAIL>>

pub   rsa4096 2021-07-16
      A21F AB74 B008 8AA3 6115  2586 B8EF 1A6B A9DA 2D5C
uid                  Tomáš Mráz <<EMAIL>>
uid                  Tomáš Mráz <<EMAIL>>
uid                  Tomáš Mráz <<EMAIL>>
