{"MacroConfigurations": {"lttng_plus": {"Modules": [{"ExportModule": "LTTNG", "CustomSettings": {}}]}, "empty": {"SkipProcessing": true, "Modules": []}, "stubs": {"Modules": []}, "STDOUT": {"Modules": [{"ExportModule": "STDOUT", "CustomSettings": {}}]}, "etw_only": {"Modules": [{"ExportModule": "MANIFESTED_ETW", "CustomSettings": {"ETWManifestFile": "MsQuicEtw.man", "ETW_Provider": "ff15e657-4f26-570e-88ab-0796b258d11c", "Level": "win:Informational", "Keywords": "ut:Connection ut:LowVolume"}}]}, "etw_plus": {"Modules": [{"ExportModule": "MANIFESTED_ETW", "CustomSettings": {"ETWManifestFile": "MsQuicEtw.man", "ETW_Provider": "ff15e657-4f26-570e-88ab-0796b258d11c", "Level": "win:Informational", "Keywords": "ut:Connection ut:LowVolume"}}]}}, "Version": 1, "CustomTypeClogCSharpFile": "msquic.clog.cs", "TypeEncoders": {"Version": 0, "TypeEncoder": [{"EncodingType": "ByteArray", "CType": "CLOG_PTR", "DefinationEncoding": "!ADDR!", "CustomDecoder": "msquic.clog_config.Types.ADDR"}, {"EncodingType": "ByteArray", "CType": "CLOG_PTR", "DefinationEncoding": "!CID!", "CustomDecoder": "msquic.clog_config.Types.CID"}, {"EncodingType": "ByteArray", "CType": "CLOG_PTR", "DefinationEncoding": "!VNL!", "CustomDecoder": "msquic.clog_config.Types.VNL"}, {"EncodingType": "ByteArray", "CType": "CLOG_PTR", "DefinationEncoding": "!ALPN!", "CustomDecoder": "msquic.clog_config.Types.ALPN"}, {"EncodingType": "UInt32", "CType": "CLOG_UINT32", "DefinationEncoding": "03u"}]}, "SourceCodeMacros": [{"MacroName": "QuicTraceLogWarning", "EncodedPrefix": null, "EncodedArgNumber": 1, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogStreamWarning", "EncodedPrefix": "[strm][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogStreamInfo", "EncodedPrefix": "[strm][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogStreamVerbose", "EncodedPrefix": "[strm][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogInfo", "EncodedPrefix": null, "EncodedArgNumber": 1, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogVerbose", "EncodedPrefix": null, "EncodedArgNumber": 1, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogError", "EncodedPrefix": null, "EncodedArgNumber": 1, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogConnError", "EncodedPrefix": "[conn][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogConnWarning", "EncodedPrefix": "[conn][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogConnInfo", "EncodedPrefix": "[conn][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceLogConnVerbose", "EncodedPrefix": "[conn][%p] ", "EncodedArgNumber": 2, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}, {"MacroName": "QuicTraceEvent", "EncodedPrefix": null, "EncodedArgNumber": 1, "MacroConfiguration": {"linux": "lttng_plus", "stubs": "stubs", "macos": "STDOUT", "windows_kernel": "empty", "windows": "empty"}}], "ChainedConfigFiles": ["DEFAULTS"]}