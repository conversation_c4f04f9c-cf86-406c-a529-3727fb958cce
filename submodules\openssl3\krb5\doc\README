BUILDING
========

See doc/build_this.rst for details about how to build the
documentation.


CONVENTIONS
===========

We use the following conventions:

* Use four-space indentation where indentation levels are arbitrary.
  Do not use tabs anywhere.  Avoid trailing whitespace at the end of
  lines or files.

* Fill lines to 70 columns (the emacs default) where lines can be
  wrapped.

* For section headers, use === underlines for page titles, --- for
  sections, ~~~ for subsections, and ### for sub-subsections.  Make
  underlines exactly as long as titles.  Do not include trailing
  punctuation in section headers.  Do not capitalize section headers
  (except for the first word) except in source files intended to
  generate man pages.

* For bullet lists, use * for top-level bullets and - for sub-bullets.
  Do not indent bullet or enumerated lists relative to the surrounding
  text.

* Use italics (*word*) for words representing variables or parameters.
  Use boldface (**word**) for command options, subcommands of programs
  like kadmin, and krb5.conf/kdc.conf parameter names.  Use literal
  text (``text``) for examples and multi-component pathnames.  For
  command names, single-component filenames, and krb5.conf/kdc.conf
  section names, use references (like :ref:`kadmin(1)`) if introducing
  them, or just use them unadorned otherwise.

* In man pages for commands with subcommands, make a subsection for
  each subcommand.  Start the subcommand with an indented synopsis,
  then follow with non-indented text describing the subcommand and its
  options.  See kadmin_local.rst for an example.

* In man page synopses, put a newline in the RST source before each
  option.  Put all parts of the synopsis at the same indentation
  level.  Ideally we would want a hanging indent to the width of the
  command or subcommand name, but RST doesn't support that.  Use
  boldface for literal text in the synopsis, italics for variable
  text, and unadorned text for syntax symbols (such as square brackets
  to indicate optional parameters).  If immediately following one kind
  of inline markup with another or putting inline markup next to
  punctuation, you may need to use "\ " as a dummy separator.

* For directives that take a content block (e.g., note, error, and
  warning), leave a blank line before the content block (after any
  arguments or options that may be present).
