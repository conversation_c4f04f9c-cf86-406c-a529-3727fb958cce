[libdefaults]
	default_realm = ATHENA.MIT.EDU 
	kdc_timesync = 1
	ccache_type = 4

[realms] 
	ATHENA.MIT.EDU = { 
#		kdc = kerberos-2000.mit.edu
		kdc = kerberos.mit.edu
		kdc = kerberos-1.mit.edu
		kdc = kerberos-2.mit.edu
		kdc = kerberos-3.mit.edu
		primary_kdc = kerberos.mit.edu
		admin_server = kerberos.mit.edu
	} 
	MEDIA-LAB.MIT.EDU = {
		kdc = kerberos.media.mit.edu
		admin_server = kerberos.media.mit.edu
	}
	ZONE.MIT.EDU = {
		kdc = casio.mit.edu
		kdc = seiko.mit.edu
		admin_server = casio.mit.edu
	}
	MOOF.MIT.EDU = {
		kdc = three-headed-dogcow.mit.edu
		kdc = three-headed-dogcow-1.mit.edu
		admin_server = three-headed-dogcow.mit.edu
	}
	CYGNUS.COM = {
		kdc = KERBEROS-1.CYGNUS.COM
		kdc = KERBEROS.CYGNUS.COM
		admin_server = KERBEROS.CYGNUS.COM
	}
	GREY17.ORG = {
		kdc = kerberos.grey17.org
		admin_server = kerberos.grey17.org
	}
	IHTFP.ORG = {
		kdc = kerberos.ihtfp.org
		admin_server = kerberos.ihtfp.org
	}

[domain_realm]
	mit.edu = ATHENA.MIT.EDU
	csail.mit.edu = CSAIL.MIT.EDU

[login]
	krb4_convert = true
	krb4_get_tickets = true
