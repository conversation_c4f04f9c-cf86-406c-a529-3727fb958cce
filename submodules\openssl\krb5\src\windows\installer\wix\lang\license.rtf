{\rtf1\ansi\ansicpg1252\deff0\deflang1033\deflangfe1033{\fonttbl{\f0\fmodern\fprq1\fcharset0 Courier New;}{\f1\froman\fprq2\fcharset0 Times New Roman;}}
{\*\generator Msftedit 5.41.15.1507;}\viewkind4\uc1\pard\nowidctlpar\tx916\tx1832\tx2748\tx3664\tx4580\tx5496\tx6412\tx7328\tx8244\tx9160\tx10076\tx10992\tx11908\tx12824\tx13740\tx14656\f0\fs20 Copyright Notice and Legal Administrivia\par
----------------------------------------\par
\par
Copyright (C) 1985-2012 by the Massachusetts Institute of Technology.\par
\par
All rights reserved.\par
\par
Export of this software from the United States of America may require a specific license from the United States Government.  It is the responsibility of any person or organization contemplating export to obtain such a license before exporting.\par
\par
WITHIN THAT CONSTRAINT, permission to use, copy, modify, and distribute this software and its documentation for any purpose and without fee is hereby granted, provided that the above copyright notice appear in all copies and that both that copyright notice and\par
this permission notice appear in supporting documentation, and that the name of M.I.T. not be used in advertising or publicity pertaining to distribution of the software without specific, written prior permission.  Furthermore if you modify this software you must label your software as modified software and not distribute it in such a fashion that it might be confused with the original MIT software. M.I.T. makes no representations about the suitability of this software for any purpose.  It is provided "as is" without express or implied warranty.\par
\par
THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF MERCHANTIBILITY AND FITNESS FOR A PARTICULAR PURPOSE.\par
\par
Individual source code files are copyright MIT, Cygnus Support, OpenVision, Oracle, Sun Soft, FundsXpress, and others.\par
\par
Project Athena, Athena, Athena MUSE, Discuss, Hesiod, Kerberos, Moira, and Zephyr are trademarks of the Massachusetts Institute of Technology (MIT).  No commercial use of these trademarks may be made without prior written permission of MIT.\par
\par
"Commercial use" means use of a name in a product or other for-profit manner.  It does NOT prevent a commercial firm from referring to the MIT trademarks in order to convey information (although in doing so, recognition of their trademark status should be given).\par
\par
----\par
\par
The following copyright and permission notice applies to the OpenVision Kerberos Administration system located in kadmin/create, kadmin/dbutil, kadmin/passwd, kadmin/server, lib/kadm5, and portions of lib/rpc:\par
\par
Copyright, OpenVision Technologies, Inc., 1996, All Rights Reserved\par
\par
WARNING: Retrieving the OpenVision Kerberos Administration system source code, as described below, indicates your acceptance of the following terms.  If you do not agree to the following terms, do not retrieve the OpenVision Kerberos administration system.\par
\par
You may freely use and distribute the Source Code and Object Code compiled from it, with or without modification, but this Source Code is provided to you "AS IS" EXCLUSIVE OF ANY WARRANTY, INCLUDING, WITHOUT LIMITATION, ANY WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE, OR ANY OTHER WARRANTY, WHETHER EXPRESS OR IMPLIED.  IN NO EVENT WILL OPENVISION HAVE ANY LIABILITY FOR ANY LOST PROFITS, LOSS OF DATA OR COSTS OF PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES, OR FOR ANY SPECIAL, INDIRECT, OR CONSEQUENTIAL DAMAGES ARISING OUT OF THIS AGREEMENT, INCLUDING, WITHOUT LIMITATION, THOSE RESULTING FROM THE USE OF THE SOURCE CODE, OR THE FAILURE OF THE SOURCE CODE TO PERFORM, OR FOR ANY OTHER REASON.\par
\par
OpenVision retains all copyrights in the donated Source Code. OpenVision also retains copyright to derivative works of the Source Code, whether created by OpenVision or by a third party. The OpenVision copyright notice must be preserved if derivative works are made based on the donated Source Code.\par
\par
OpenVision Technologies, Inc. has donated this Kerberos Administration system to MIT for inclusion in the standard Kerberos 5 distribution.  This donation underscores our commitment to continuing Kerberos technology development and our gratitude for the valuable work which has been performed by MIT and the Kerberos community.\par
\par
----\par
\par
Portions contributed by Matt Crawford <<EMAIL>> were work performed at Fermi National Accelerator Laboratory, which is operated by Universities Research Association, Inc., under contract DE-AC02-76CHO3000 with the U.S. Department of Energy.\par
\par
\pard ----\f1\fs24\par
\pard\nowidctlpar\tx916\tx1832\tx2748\tx3664\tx4580\tx5496\tx6412\tx7328\tx8244\tx9160\tx10076\tx10992\tx11908\tx12824\tx13740\tx14656\f0\fs20\par
The implementation of the Yarrow pseudo-random number generator in src/lib/crypto/yarrow has the following copyright:\par
\par
Copyright 2000 by Zero-Knowledge Systems, Inc.\par
\par
Permission to use, copy, modify, distribute, and sell this software and its documentation for any purpose is hereby granted without fee, provided that the above copyright notice appear in all copies and that both that copyright notice and this permission notice appear in supporting documentation, and that the name of Zero-Knowledge Systems, Inc. not be used in advertising or publicity pertaining to distribution of the software without specific, written prior permission.  Zero-Knowledge Systems, Inc. makes no representations about the suitability of this software for any purpose.  It is provided "as is" without express or implied warranty.\par
\par
ZERO-KNOWLEDGE SYSTEMS, INC. DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS, IN NO EVENT SHALL ZERO-KNOWLEDGE SYSTEMS, INC. BE LIABLE FOR ANY SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\par
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTUOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\par
\par
\pard ----\f1\fs24\par
\pard\nowidctlpar\tx916\tx1832\tx2748\tx3664\tx4580\tx5496\tx6412\tx7328\tx8244\tx9160\tx10076\tx10992\tx11908\tx12824\tx13740\tx14656\f0\fs20\par
The implementation of the AES encryption algorithm in src/lib/crypto/aes has the following copyright:\par
\par
Copyright (c) 2001, Dr Brian Gladman <<EMAIL>>, Worcester, UK.\par
All rights reserved.\par
\par
LICENSE TERMS\par
\par
The free distribution and use of this software in both source and binary form is allowed (with or without changes) provided that:\par
\par
1. distributions of this source code include the above copyright notice, this list of conditions and the following disclaimer;\par
\par
2. distributions in binary form include the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other associated materials;\par
\par
3. the copyright holder's name is not used to endorse products built using this software without specific written permission. \par
\par
DISCLAIMER\par
\par
This software is provided 'as is' with no explcit or implied warranties in respect of any properties, including, but not limited to, correctness and fitness for purpose.\par
\par
\par
\par
Acknowledgements\par
----------------\par
\par
Appreciation Time!!!!  There are far too many people to try to thank them all; many people have contributed to the development of Kerberos V5.  This is only a partial listing....\par
\par
Thanks to Paul Vixie and the Internet Software Consortium for funding the work of Barry Jaspan.  This funding was invaluable for the OV administration server integration, as well as the 1.0 release preparation process.\par
\par
Thanks to John Linn, Scott Foote, and all of the folks at OpenVision Technologies, Inc., who donated their administration server for use in the MIT release of Kerberos.\par
\par
Thanks to Jeff Bigler, Mark Eichin, Marc Horowitz, Nancy Gilman, Ken Raeburn, and all of the folks at Cygnus Support, who provided innumerable bug fixes and portability enhancements to the Kerberos V5 tree.  Thanks especially to Jeff Bigler, for the new user and system administrator's documentation.\par
\par
Thanks to Doug Engert from ANL for providing many bug fixes, as well as testing to ensure DCE interoperability.\par
\par
Thanks to Ken Hornstein at NRL for providing many bug fixes and suggestions, and for working on SAM preauthentication.\par
\par
Thanks to Matt Crawford at FNAL for bugfixes and enhancements.\par
\par
Thanks to Sean Mullan and Bill Sommerfeld from Hewlett Packard for their many suggestions and bug fixes.\par
\par
Thanks to Nalin Dahyabhai of RedHat and Chris Evans for locating and providing patches for numerous buffer overruns.\par
\par
Thanks to Christopher Thompson and Marcus Watts for discovering the ftpd security bug.\par
\par
Thanks to Paul Nelson of Thursby Software Systems for implementing the Microsoft set password protocol.\par
\par
Thanks to the members of the Kerberos V5 development team at MIT, both past and present: Danilo Almeida, Jeffrey Altman, Jay Berkenbilt, Richard Basch, Mitch Berger, John Carr, Don Davis, Alexandra Ellwood, Nancy Gilman, Matt Hancher, Sam Hartman, Paul Hill, Marc Horowitz, Eva Jacobus, Miroslav Jurisic, Barry Jaspan, Geoffrey King, John Kohl, Peter Litwack, Scott McGuire, Kevin Mitchell, Cliff Neuman, Paul Park, Ezra Peisach, Chris Provenzano, Ken Raeburn, Jon Rochlis, Jeff Schiller, Jen Selby, Brad Thompson, Harry Tsai, Ted Ts'o, Marshall Vale, Tom Yu.\par
\pard\nowidctlpar\f1\fs24\par
}
 