=pod

=head1 NAME

BIO_get_ex_new_index, BIO_set_ex_data, BIO_get_ex_data,
ENGINE_get_ex_new_index, ENGINE_set_ex_data, ENGINE_get_ex_data,
UI_get_ex_new_index, UI_set_ex_data, UI_get_ex_data,
X509_get_ex_new_index, X509_set_ex_data, X509_get_ex_data,
X509_STORE_get_ex_new_index, X509_STORE_set_ex_data, X509_STORE_get_ex_data,
X509_STORE_CTX_get_ex_new_index, X509_STORE_CTX_set_ex_data, X509_STORE_CTX_get_ex_data,
DH_get_ex_new_index, DH_set_ex_data, DH_get_ex_data,
DSA_get_ex_new_index, DSA_set_ex_data, DSA_get_ex_data,
ECDH_get_ex_new_index, ECDH_set_ex_data, ECDH_get_ex_data,
EC_KEY_get_ex_new_index, EC_KEY_set_ex_data, EC_KEY_get_ex_data,
RSA_get_ex_new_index, RSA_set_ex_data, RSA_get_ex_data
- application-specific data

=head1 SYNOPSIS

=for comment generic

 #include <openssl/x509.h>

 int TYPE_get_ex_new_index(long argl, void *argp,
                           CRYPTO_EX_new *new_func,
                           CRYPTO_EX_dup *dup_func,
                           CRYPTO_EX_free *free_func);

 int TYPE_set_ex_data(TYPE *d, int idx, void *arg);

 void *TYPE_get_ex_data(TYPE *d, int idx);

=head1 DESCRIPTION

In the description here, I<TYPE> is used a placeholder
for any of the OpenSSL datatypes listed in
L<CRYPTO_get_ex_new_index(3)>.

These functions handle application-specific data for OpenSSL data
structures.

TYPE_get_ex_new_index() is a macro that calls CRYPTO_get_ex_new_index()
with the correct B<index> value.

TYPE_set_ex_data() is a function that calls CRYPTO_set_ex_data() with
an offset into the opaque exdata part of the TYPE object.

TYPE_get_ex_data() is a function that calls CRYPTO_get_ex_data() with
an offset into the opaque exdata part of the TYPE object.

=head1 RETURN VALUES

TYPE_get_ex_new_index() returns a new index on success or -1 on error.

TYPE_set_ex_data() returns 1 on success or 0 on error.

TYPE_get_ex_data() returns the application data or NULL if an error occurred.

=head1 SEE ALSO

L<CRYPTO_get_ex_new_index(3)>.

=head1 COPYRIGHT

Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the OpenSSL license (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
