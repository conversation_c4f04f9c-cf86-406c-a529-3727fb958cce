version: "2"
checks:
  argument-count:
    enabled: false
  complex-logic:
    config:
      threshold: 4
  file-lines:
    config:
      threshold: 500
  method-complexity:
    config:
      threshold: 10
  method-count:
    config:
      threshold: 20
  method-lines:
    config:
      threshold: 25
  nested-control-flow:
    config:
      threshold: 4
  return-statements:
    config:
      threshold: 4

plugins:
  radon:
    enabled: true
    config:
       threshold: "C"
  pep8:
    enabled: true
  duplication:
    enabled: true
    config:
      languages:
          python:
             mass_threshold: 42
             python_version: 3
  fixme:
    enabled: true
    checks:
      bug:
        enabled: true
  markdownlint:
    enabled: true

exclude_patterns:
 - "tests/*"
 - "scripts/*"
 - "docs/source/hello-world.py"
