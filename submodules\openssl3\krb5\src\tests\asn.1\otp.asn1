   OTPKerberos
   DEFINITIONS IMPLICIT TAGS ::=
   BEGIN

   IMPORTS

       KerberosTime, KerberosFlags, <PERSON>cry<PERSON><PERSON>ey, Int32,
       <PERSON>cry<PERSON><PERSON><PERSON>, LastReq, KerberosString
       FROM KerberosV5Spec2 {iso(1) identified-organization(3)
                             dod(6) internet(1) security(5)
                             kerberosV5(2) modules(4) krb5spec2(2)}
                             -- as defined in RFC 4120.
       AlgorithmIdentifier
       FROM PKIX1Explicit88 { iso (1) identified-organization (3)
                              dod (6) internet (1)
                              security (5) mechanisms (5) pkix (7)
                              id-mod (0) id-pkix1-explicit (18) };
                              -- As defined in RFC 5280.

       PA-OTP-CHALLENGE ::= SEQUENCE {
         nonce            [0] OCTET STRING,
         otp-service      [1] UTF8String               OPTIONAL,
         otp-tokenInfo    [2] SEQUENCE (SIZE(1..MAX)) OF
                                                  OTP-TOKENINFO,
         salt             [3] KerberosString           OPTIONAL,
         s2kparams        [4] OCTET STRING             OPTIONAL,
         ...
       }

       OTP-TOKENINFO ::= SEQUENCE {
         flags            [0] OTPFlags,
         otp-vendor       [1] UTF8String               OPTIONAL,
         otp-challenge    [2] OCTET STRING (SIZE(1..MAX))
                                                       OPTIONAL,
         otp-length       [3] Int32                    OPTIONAL,
         otp-format       [4] OTPFormat                OPTIONAL,
         otp-tokenID      [5] OCTET STRING             OPTIONAL,
         otp-algID        [6] AnyURI                   OPTIONAL,
         supportedHashAlg [7] SEQUENCE OF AlgorithmIdentifier
                                                       OPTIONAL,
         iterationCount   [8] Int32                    OPTIONAL,
         ...
       }

       OTPFormat ::= INTEGER {
         decimal(0),
         hexadecimal(1),
         alphanumeric(2),
         binary(3),
         base64(4)
       }

       OTPFlags ::= KerberosFlags
       -- reserved(0),
       -- nextOTP(1),
       -- combine(2),
       -- collect-pin(3),
       -- do-not-collect-pin(4),
       -- must-encrypt-nonce (5),
       -- separate-pin-required (6),
       -- check-digit (7)

       PA-OTP-REQUEST ::= SEQUENCE {
         flags          [0]  OTPFlags,
         nonce          [1]  OCTET STRING                OPTIONAL,
         encData        [2]  EncryptedData,
                            -- PA-OTP-ENC-REQUEST or PA-ENC-TS-ENC
                            -- Key usage of KEY_USAGE_OTP_REQUEST
         hashAlg        [3]  AlgorithmIdentifier         OPTIONAL,
         iterationCount [4]  Int32                       OPTIONAL,
         otp-value      [5]  OCTET STRING                OPTIONAL,
         otp-pin        [6]  UTF8String                  OPTIONAL,
         otp-challenge  [7]  OCTET STRING (SIZE(1..MAX)) OPTIONAL,
         otp-time       [8]  KerberosTime                OPTIONAL,
         otp-counter    [9]  OCTET STRING                OPTIONAL,
         otp-format     [10] OTPFormat                   OPTIONAL,
         otp-tokenID    [11] OCTET STRING                OPTIONAL,
         otp-algID      [12] AnyURI                      OPTIONAL,
         otp-vendor     [13] UTF8String                  OPTIONAL,
         ...
       }

       PA-OTP-ENC-REQUEST ::= SEQUENCE {
         nonce     [0] OCTET STRING,
         ...
       }


       PA-OTP-PIN-CHANGE ::= SEQUENCE {
         flags     [0] PinFlags,
         pin       [1] UTF8String OPTIONAL,
         minLength [2] INTEGER    OPTIONAL,
         maxLength [3] INTEGER    OPTIONAL,
         last-req  [4] LastReq    OPTIONAL,
         format    [5] OTPFormat  OPTIONAL,
         ...
       }

       PinFlags ::= KerberosFlags
       -- reserved(0),
       -- systemSetPin(1),
       -- mandatory(2)

       AnyURI ::= UTF8String
          (CONSTRAINED BY {
          -- MUST be a valid URI in accordance with IETF RFC 2396
          })

   END
