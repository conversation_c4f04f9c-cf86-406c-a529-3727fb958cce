@echo off
REM Unit test runner for p2psocket interface
REM This script builds and runs the testp2psocket unit tests

echo ========================================
echo P2P Socket Interface Unit Test Runner
echo ========================================

REM Check if we're in the correct directory
if not exist "testp2psocket.cpp" (
    echo Error: testp2psocket.cpp not found in current directory
    echo Please run this script from the src/p2psocket directory
    pause
    exit /b 1
)

REM Build the project using the main build script
echo Building p2psocket library and tests...
cd ..\..
call build_p2psocket.bat
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

REM Go to the bin directory where executables are built
cd bin

REM Check if testp2psocket.exe exists
if not exist "testp2psocket.exe" (
    echo Error: testp2psocket.exe not found in bin directory
    echo Make sure the build completed successfully
    pause
    exit /b 1
)

echo.
echo ========================================
echo Running Unit Tests
echo ========================================

REM Run the unit tests with default settings (QUIC)
echo Running tests with QUIC socket type...
testp2psocket.exe -type 3
set QUIC_RESULT=%errorlevel%

echo.
echo ========================================
echo Running tests with SSL socket type...
testp2psocket.exe -type 2
set SSL_RESULT=%errorlevel%

echo.
echo ========================================
echo Running tests with MULTITCP socket type...
testp2psocket.exe -type 1
set TCP_RESULT=%errorlevel%

echo.
echo ========================================
echo Test Results Summary
echo ========================================
echo QUIC tests:     %QUIC_RESULT%
echo SSL tests:      %SSL_RESULT%
echo MULTITCP tests: %TCP_RESULT%

if %QUIC_RESULT% equ 0 (
    echo QUIC tests: PASSED
) else (
    echo QUIC tests: FAILED
)

if %SSL_RESULT% equ 0 (
    echo SSL tests: PASSED
) else (
    echo SSL tests: FAILED
)

if %TCP_RESULT% equ 0 (
    echo MULTITCP tests: PASSED
) else (
    echo MULTITCP tests: FAILED
)

REM Calculate overall result
set /a TOTAL_FAILURES=%QUIC_RESULT%+%SSL_RESULT%+%TCP_RESULT%

if %TOTAL_FAILURES% equ 0 (
    echo.
    echo *** ALL TESTS PASSED! ***
    echo Unit tests completed successfully.
) else (
    echo.
    echo *** SOME TESTS FAILED ***
    echo Please check the output above for details.
)

echo.
echo Press any key to exit...
pause >nul
exit /b %TOTAL_FAILURES%
